﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class MaterialsDict13
    Inherits HisControl.BaseChild

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(MaterialsDict13))
        Me.T_Line1 = New System.Windows.Forms.Label()
        Me.C1CommandHolder1 = New C1.Win.C1Command.C1CommandHolder()
        Me.Control1 = New C1.Win.C1Command.C1CommandControl()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.Move1 = New C1.Win.C1Command.C1Command()
        Me.Move2 = New C1.Win.C1Command.C1Command()
        Me.Control2 = New C1.Win.C1Command.C1CommandControl()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.Move3 = New C1.Win.C1Command.C1Command()
        Me.Move4 = New C1.Win.C1Command.C1Command()
        Me.Move5 = New C1.Win.C1Command.C1Command()
        Me.Comm1 = New C1.Win.C1Command.C1Command()
        Me.Comm3 = New C1.Win.C1Command.C1Command()
        Me.Comm2 = New C1.Win.C1Command.C1Command()
        Me.T_Line2 = New System.Windows.Forms.Label()
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel()
        Me.M_Code_Tb = New CustomControl.MyTextBox()
        Me.M_Name_Tb = New CustomControl.MyTextBox()
        Me.M_Py_Tb = New CustomControl.MyTextBox()
        Me.M_Memo_Tb = New CustomControl.MyTextBox()
        Me.M_Spec_Tb = New CustomControl.MyTextBox()
        Me.M_PackUnit_Tb = New CustomControl.MyTextBox()
        Me.M_ConvertRatio_Tb = New CustomControl.MyTextBox()
        Me.M_BulkUnit_Tb = New CustomControl.MyTextBox()
        Me.M_MateManu_Tb = New CustomControl.MyTextBox()
        Me.M_Class_Tb = New CustomControl.MyTextBox()
        Me.IsUse_Cb = New System.Windows.Forms.CheckBox()
        Me.Link5 = New C1.Win.C1Command.C1CommandLink()
        Me.Link7 = New C1.Win.C1Command.C1CommandLink()
        Me.Link4 = New C1.Win.C1Command.C1CommandLink()
        Me.Link3 = New C1.Win.C1Command.C1CommandLink()
        Me.Link2 = New C1.Win.C1Command.C1CommandLink()
        Me.Link1 = New C1.Win.C1Command.C1CommandLink()
        Me.Link6 = New C1.Win.C1Command.C1CommandLink()
        Me.Label3 = New C1.Win.C1Input.C1Label()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.Button2 = New CustomControl.MyButton()
        Me.Button1 = New CustomControl.MyButton()
        Me.ToolBar1 = New C1.Win.C1Command.C1ToolBar()
        Me.SerialNo_Tb = New CustomControl.MyTextBox()
        CType(Me.C1CommandHolder1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.TableLayoutPanel1.SuspendLayout()
        CType(Me.Label3, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Panel1.SuspendLayout()
        Me.ToolBar1.SuspendLayout()
        Me.SuspendLayout()
        '
        'T_Line1
        '
        Me.T_Line1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.T_Line1.BackColor = System.Drawing.SystemColors.Control
        Me.T_Line1.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.TableLayoutPanel1.SetColumnSpan(Me.T_Line1, 4)
        Me.T_Line1.Location = New System.Drawing.Point(3, 46)
        Me.T_Line1.Name = "T_Line1"
        Me.T_Line1.Size = New System.Drawing.Size(434, 2)
        Me.T_Line1.TabIndex = 133
        Me.T_Line1.Text = "Label1"
        '
        'C1CommandHolder1
        '
        Me.C1CommandHolder1.Commands.Add(Me.Control1)
        Me.C1CommandHolder1.Commands.Add(Me.Move1)
        Me.C1CommandHolder1.Commands.Add(Me.Move2)
        Me.C1CommandHolder1.Commands.Add(Me.Control2)
        Me.C1CommandHolder1.Commands.Add(Me.Move3)
        Me.C1CommandHolder1.Commands.Add(Me.Move4)
        Me.C1CommandHolder1.Commands.Add(Me.Move5)
        Me.C1CommandHolder1.Commands.Add(Me.Comm1)
        Me.C1CommandHolder1.Commands.Add(Me.Comm3)
        Me.C1CommandHolder1.Commands.Add(Me.Comm2)
        Me.C1CommandHolder1.Owner = Me
        '
        'Control1
        '
        Me.Control1.Control = Me.Label2
        Me.Control1.HotFrame = C1.Win.C1Command.HotFrameEnum.None
        Me.Control1.Name = "Control1"
        Me.Control1.ShortcutText = ""
        Me.Control1.ShowShortcut = False
        Me.Control1.ShowTextAsToolTip = False
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.BackColor = System.Drawing.Color.Transparent
        Me.Label2.Location = New System.Drawing.Point(1, 5)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(29, 12)
        Me.Label2.TabIndex = 165
        Me.Label2.Text = "记录"
        Me.Label2.TextAlign = System.Drawing.ContentAlignment.BottomCenter
        '
        'Move1
        '
        Me.Move1.Image = CType(resources.GetObject("Move1.Image"), System.Drawing.Image)
        Me.Move1.Name = "Move1"
        Me.Move1.ShortcutText = ""
        Me.Move1.Text = "最前"
        Me.Move1.ToolTipText = "移至最前记录"
        '
        'Move2
        '
        Me.Move2.Image = CType(resources.GetObject("Move2.Image"), System.Drawing.Image)
        Me.Move2.Name = "Move2"
        Me.Move2.ShortcutText = ""
        Me.Move2.Text = "上移"
        Me.Move2.ToolTipText = "上移记录"
        '
        'Control2
        '
        Me.Control2.Control = Me.Label1
        Me.Control2.HotFrame = C1.Win.C1Command.HotFrameEnum.None
        Me.Control2.Name = "Control2"
        Me.Control2.ShortcutText = ""
        Me.Control2.ShowShortcut = False
        Me.Control2.ShowTextAsToolTip = False
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.BackColor = System.Drawing.Color.Transparent
        Me.Label1.Location = New System.Drawing.Point(80, 5)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(23, 12)
        Me.Label1.TabIndex = 164
        Me.Label1.Text = "(1)"
        Me.Label1.TextAlign = System.Drawing.ContentAlignment.BottomCenter
        '
        'Move3
        '
        Me.Move3.Image = CType(resources.GetObject("Move3.Image"), System.Drawing.Image)
        Me.Move3.Name = "Move3"
        Me.Move3.ShortcutText = ""
        Me.Move3.Text = "下移"
        Me.Move3.ToolTipText = "下移记录"
        '
        'Move4
        '
        Me.Move4.Image = CType(resources.GetObject("Move4.Image"), System.Drawing.Image)
        Me.Move4.Name = "Move4"
        Me.Move4.ShortcutText = ""
        Me.Move4.Text = "最后"
        Me.Move4.ToolTipText = "移至最后记录"
        '
        'Move5
        '
        Me.Move5.Image = CType(resources.GetObject("Move5.Image"), System.Drawing.Image)
        Me.Move5.Name = "Move5"
        Me.Move5.ShortcutText = ""
        Me.Move5.Text = "新增"
        '
        'Comm1
        '
        Me.Comm1.Name = "Comm1"
        Me.Comm1.ShortcutText = ""
        Me.Comm1.Text = "增加"
        Me.Comm1.ToolTipText = "增加记录"
        '
        'Comm3
        '
        Me.Comm3.Name = "Comm3"
        Me.Comm3.ShortcutText = ""
        Me.Comm3.Text = "刷新"
        '
        'Comm2
        '
        Me.Comm2.Name = "Comm2"
        Me.Comm2.ShortcutText = ""
        Me.Comm2.Text = "删除"
        Me.Comm2.ToolTipText = "删除记录"
        '
        'T_Line2
        '
        Me.T_Line2.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.T_Line2.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.TableLayoutPanel1.SetColumnSpan(Me.T_Line2, 4)
        Me.T_Line2.Location = New System.Drawing.Point(3, 260)
        Me.T_Line2.Name = "T_Line2"
        Me.T_Line2.Size = New System.Drawing.Size(434, 2)
        Me.T_Line2.TabIndex = 158
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.ColumnCount = 5
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 20.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 200.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 200.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 20.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle())
        Me.TableLayoutPanel1.Controls.Add(Me.T_Line1, 0, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.T_Line2, 0, 8)
        Me.TableLayoutPanel1.Controls.Add(Me.M_Code_Tb, 1, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.M_Name_Tb, 1, 3)
        Me.TableLayoutPanel1.Controls.Add(Me.M_Py_Tb, 2, 3)
        Me.TableLayoutPanel1.Controls.Add(Me.M_Memo_Tb, 1, 7)
        Me.TableLayoutPanel1.Controls.Add(Me.M_Spec_Tb, 1, 4)
        Me.TableLayoutPanel1.Controls.Add(Me.M_PackUnit_Tb, 2, 4)
        Me.TableLayoutPanel1.Controls.Add(Me.M_ConvertRatio_Tb, 1, 5)
        Me.TableLayoutPanel1.Controls.Add(Me.M_BulkUnit_Tb, 2, 5)
        Me.TableLayoutPanel1.Controls.Add(Me.M_MateManu_Tb, 1, 6)
        Me.TableLayoutPanel1.Controls.Add(Me.M_Class_Tb, 2, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.IsUse_Cb, 2, 6)
        Me.TableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(0, 0)
        Me.TableLayoutPanel1.Margin = New System.Windows.Forms.Padding(0)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.RowCount = 10
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 18.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 2.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 100.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 2.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 20.0!))
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(441, 262)
        Me.TableLayoutPanel1.TabIndex = 163
        '
        'M_Code_Tb
        '
        Me.M_Code_Tb.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.M_Code_Tb.Captain = "编    码"
        Me.M_Code_Tb.CaptainBackColor = System.Drawing.Color.Transparent
        Me.M_Code_Tb.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.M_Code_Tb.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.M_Code_Tb.CaptainWidth = 60.0!
        Me.M_Code_Tb.ContentForeColor = System.Drawing.Color.Black
        Me.M_Code_Tb.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.M_Code_Tb.Location = New System.Drawing.Point(23, 22)
        Me.M_Code_Tb.Multiline = False
        Me.M_Code_Tb.Name = "M_Code_Tb"
        Me.M_Code_Tb.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.M_Code_Tb.ReadOnly = False
        Me.M_Code_Tb.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.M_Code_Tb.SelectionStart = 0
        Me.M_Code_Tb.SelectStart = 0
        Me.M_Code_Tb.Size = New System.Drawing.Size(194, 20)
        Me.M_Code_Tb.TabIndex = 159
        Me.M_Code_Tb.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.M_Code_Tb.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.M_Code_Tb.Watermark = Nothing
        '
        'M_Name_Tb
        '
        Me.M_Name_Tb.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.M_Name_Tb.Captain = "名    称"
        Me.M_Name_Tb.CaptainBackColor = System.Drawing.Color.Transparent
        Me.M_Name_Tb.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.M_Name_Tb.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.M_Name_Tb.CaptainWidth = 60.0!
        Me.M_Name_Tb.ContentForeColor = System.Drawing.Color.Black
        Me.M_Name_Tb.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.M_Name_Tb.Location = New System.Drawing.Point(23, 52)
        Me.M_Name_Tb.Multiline = False
        Me.M_Name_Tb.Name = "M_Name_Tb"
        Me.M_Name_Tb.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.M_Name_Tb.ReadOnly = False
        Me.M_Name_Tb.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.M_Name_Tb.SelectionStart = 0
        Me.M_Name_Tb.SelectStart = 0
        Me.M_Name_Tb.Size = New System.Drawing.Size(194, 20)
        Me.M_Name_Tb.TabIndex = 0
        Me.M_Name_Tb.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.M_Name_Tb.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.M_Name_Tb.Watermark = Nothing
        '
        'M_Py_Tb
        '
        Me.M_Py_Tb.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.M_Py_Tb.Captain = "拼音简称"
        Me.M_Py_Tb.CaptainBackColor = System.Drawing.Color.Transparent
        Me.M_Py_Tb.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.M_Py_Tb.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.M_Py_Tb.CaptainWidth = 60.0!
        Me.M_Py_Tb.ContentForeColor = System.Drawing.Color.Black
        Me.M_Py_Tb.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.M_Py_Tb.Location = New System.Drawing.Point(223, 52)
        Me.M_Py_Tb.Multiline = False
        Me.M_Py_Tb.Name = "M_Py_Tb"
        Me.M_Py_Tb.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.M_Py_Tb.ReadOnly = False
        Me.M_Py_Tb.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.M_Py_Tb.SelectionStart = 0
        Me.M_Py_Tb.SelectStart = 0
        Me.M_Py_Tb.Size = New System.Drawing.Size(194, 20)
        Me.M_Py_Tb.TabIndex = 161
        Me.M_Py_Tb.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.M_Py_Tb.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.M_Py_Tb.Watermark = Nothing
        '
        'M_Memo_Tb
        '
        Me.M_Memo_Tb.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.M_Memo_Tb.Captain = "备    注"
        Me.M_Memo_Tb.CaptainBackColor = System.Drawing.Color.Transparent
        Me.M_Memo_Tb.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.M_Memo_Tb.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.M_Memo_Tb.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.M_Memo_Tb, 2)
        Me.M_Memo_Tb.ContentForeColor = System.Drawing.Color.Black
        Me.M_Memo_Tb.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.M_Memo_Tb.Location = New System.Drawing.Point(23, 163)
        Me.M_Memo_Tb.Multiline = True
        Me.M_Memo_Tb.Name = "M_Memo_Tb"
        Me.M_Memo_Tb.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.M_Memo_Tb.ReadOnly = False
        Me.M_Memo_Tb.ScrollBars = System.Windows.Forms.ScrollBars.Vertical
        Me.M_Memo_Tb.SelectionStart = 0
        Me.M_Memo_Tb.SelectStart = 0
        Me.M_Memo_Tb.Size = New System.Drawing.Size(394, 94)
        Me.M_Memo_Tb.TabIndex = 7
        Me.M_Memo_Tb.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.M_Memo_Tb.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Top
        Me.M_Memo_Tb.Watermark = Nothing
        '
        'M_Spec_Tb
        '
        Me.M_Spec_Tb.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.M_Spec_Tb.Captain = "规    格"
        Me.M_Spec_Tb.CaptainBackColor = System.Drawing.Color.Transparent
        Me.M_Spec_Tb.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.M_Spec_Tb.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.M_Spec_Tb.CaptainWidth = 60.0!
        Me.M_Spec_Tb.ContentForeColor = System.Drawing.Color.Black
        Me.M_Spec_Tb.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.M_Spec_Tb.Location = New System.Drawing.Point(23, 80)
        Me.M_Spec_Tb.Multiline = False
        Me.M_Spec_Tb.Name = "M_Spec_Tb"
        Me.M_Spec_Tb.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.M_Spec_Tb.ReadOnly = False
        Me.M_Spec_Tb.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.M_Spec_Tb.SelectionStart = 0
        Me.M_Spec_Tb.SelectStart = 0
        Me.M_Spec_Tb.Size = New System.Drawing.Size(194, 20)
        Me.M_Spec_Tb.TabIndex = 1
        Me.M_Spec_Tb.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.M_Spec_Tb.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.M_Spec_Tb.Watermark = Nothing
        '
        'M_PackUnit_Tb
        '
        Me.M_PackUnit_Tb.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.M_PackUnit_Tb.Captain = "包装单位"
        Me.M_PackUnit_Tb.CaptainBackColor = System.Drawing.Color.Transparent
        Me.M_PackUnit_Tb.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.M_PackUnit_Tb.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.M_PackUnit_Tb.CaptainWidth = 60.0!
        Me.M_PackUnit_Tb.ContentForeColor = System.Drawing.Color.Black
        Me.M_PackUnit_Tb.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.M_PackUnit_Tb.Location = New System.Drawing.Point(223, 80)
        Me.M_PackUnit_Tb.Multiline = False
        Me.M_PackUnit_Tb.Name = "M_PackUnit_Tb"
        Me.M_PackUnit_Tb.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.M_PackUnit_Tb.ReadOnly = False
        Me.M_PackUnit_Tb.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.M_PackUnit_Tb.SelectionStart = 0
        Me.M_PackUnit_Tb.SelectStart = 0
        Me.M_PackUnit_Tb.Size = New System.Drawing.Size(194, 20)
        Me.M_PackUnit_Tb.TabIndex = 2
        Me.M_PackUnit_Tb.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.M_PackUnit_Tb.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.M_PackUnit_Tb.Watermark = Nothing
        '
        'M_ConvertRatio_Tb
        '
        Me.M_ConvertRatio_Tb.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.M_ConvertRatio_Tb.Captain = "拆分比例"
        Me.M_ConvertRatio_Tb.CaptainBackColor = System.Drawing.Color.Transparent
        Me.M_ConvertRatio_Tb.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.M_ConvertRatio_Tb.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.M_ConvertRatio_Tb.CaptainWidth = 60.0!
        Me.M_ConvertRatio_Tb.ContentForeColor = System.Drawing.Color.Black
        Me.M_ConvertRatio_Tb.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.M_ConvertRatio_Tb.Location = New System.Drawing.Point(23, 108)
        Me.M_ConvertRatio_Tb.Multiline = False
        Me.M_ConvertRatio_Tb.Name = "M_ConvertRatio_Tb"
        Me.M_ConvertRatio_Tb.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.M_ConvertRatio_Tb.ReadOnly = False
        Me.M_ConvertRatio_Tb.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.M_ConvertRatio_Tb.SelectionStart = 0
        Me.M_ConvertRatio_Tb.SelectStart = 0
        Me.M_ConvertRatio_Tb.Size = New System.Drawing.Size(194, 20)
        Me.M_ConvertRatio_Tb.TabIndex = 3
        Me.M_ConvertRatio_Tb.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.M_ConvertRatio_Tb.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.M_ConvertRatio_Tb.Watermark = Nothing
        '
        'M_BulkUnit_Tb
        '
        Me.M_BulkUnit_Tb.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.M_BulkUnit_Tb.Captain = "散装单位"
        Me.M_BulkUnit_Tb.CaptainBackColor = System.Drawing.Color.Transparent
        Me.M_BulkUnit_Tb.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.M_BulkUnit_Tb.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.M_BulkUnit_Tb.CaptainWidth = 60.0!
        Me.M_BulkUnit_Tb.ContentForeColor = System.Drawing.Color.Black
        Me.M_BulkUnit_Tb.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.M_BulkUnit_Tb.Location = New System.Drawing.Point(223, 108)
        Me.M_BulkUnit_Tb.Multiline = False
        Me.M_BulkUnit_Tb.Name = "M_BulkUnit_Tb"
        Me.M_BulkUnit_Tb.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.M_BulkUnit_Tb.ReadOnly = False
        Me.M_BulkUnit_Tb.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.M_BulkUnit_Tb.SelectionStart = 0
        Me.M_BulkUnit_Tb.SelectStart = 0
        Me.M_BulkUnit_Tb.Size = New System.Drawing.Size(194, 20)
        Me.M_BulkUnit_Tb.TabIndex = 4
        Me.M_BulkUnit_Tb.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.M_BulkUnit_Tb.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.M_BulkUnit_Tb.Watermark = Nothing
        '
        'M_MateManu_Tb
        '
        Me.M_MateManu_Tb.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.M_MateManu_Tb.Captain = "生产厂家"
        Me.M_MateManu_Tb.CaptainBackColor = System.Drawing.Color.Transparent
        Me.M_MateManu_Tb.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.M_MateManu_Tb.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.M_MateManu_Tb.CaptainWidth = 60.0!
        Me.M_MateManu_Tb.ContentForeColor = System.Drawing.Color.Black
        Me.M_MateManu_Tb.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.M_MateManu_Tb.Location = New System.Drawing.Point(23, 136)
        Me.M_MateManu_Tb.Multiline = False
        Me.M_MateManu_Tb.Name = "M_MateManu_Tb"
        Me.M_MateManu_Tb.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.M_MateManu_Tb.ReadOnly = False
        Me.M_MateManu_Tb.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.M_MateManu_Tb.SelectionStart = 0
        Me.M_MateManu_Tb.SelectStart = 0
        Me.M_MateManu_Tb.Size = New System.Drawing.Size(194, 20)
        Me.M_MateManu_Tb.TabIndex = 5
        Me.M_MateManu_Tb.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.M_MateManu_Tb.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.M_MateManu_Tb.Watermark = Nothing
        '
        'M_Class_Tb
        '
        Me.M_Class_Tb.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.M_Class_Tb.Captain = "分    类"
        Me.M_Class_Tb.CaptainBackColor = System.Drawing.Color.Transparent
        Me.M_Class_Tb.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.M_Class_Tb.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.M_Class_Tb.CaptainWidth = 60.0!
        Me.M_Class_Tb.ContentForeColor = System.Drawing.Color.Black
        Me.M_Class_Tb.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.M_Class_Tb.Location = New System.Drawing.Point(223, 22)
        Me.M_Class_Tb.Multiline = False
        Me.M_Class_Tb.Name = "M_Class_Tb"
        Me.M_Class_Tb.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.M_Class_Tb.ReadOnly = False
        Me.M_Class_Tb.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.M_Class_Tb.SelectionStart = 0
        Me.M_Class_Tb.SelectStart = 0
        Me.M_Class_Tb.Size = New System.Drawing.Size(194, 20)
        Me.M_Class_Tb.TabIndex = 167
        Me.M_Class_Tb.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.M_Class_Tb.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.M_Class_Tb.Watermark = Nothing
        '
        'IsUse_Cb
        '
        Me.IsUse_Cb.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.IsUse_Cb.AutoSize = True
        Me.IsUse_Cb.CheckAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.IsUse_Cb.Location = New System.Drawing.Point(223, 138)
        Me.IsUse_Cb.Name = "IsUse_Cb"
        Me.IsUse_Cb.Size = New System.Drawing.Size(72, 16)
        Me.IsUse_Cb.TabIndex = 6
        Me.IsUse_Cb.Text = "是否启用"
        Me.IsUse_Cb.UseVisualStyleBackColor = True
        '
        'Link5
        '
        Me.Link5.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image), C1.Win.C1Command.ButtonLookFlags)
        Me.Link5.Command = Me.Move5
        Me.Link5.Delimiter = True
        Me.Link5.SortOrder = 6
        Me.Link5.Text = "新增"
        Me.Link5.ToolTipText = "新增记录"
        '
        'Link7
        '
        Me.Link7.Command = Me.Control2
        Me.Link7.SortOrder = 3
        '
        'Link4
        '
        Me.Link4.Command = Me.Move4
        Me.Link4.SortOrder = 5
        '
        'Link3
        '
        Me.Link3.Command = Me.Move3
        Me.Link3.SortOrder = 4
        '
        'Link2
        '
        Me.Link2.Command = Me.Move2
        Me.Link2.SortOrder = 2
        Me.Link2.Text = "上移"
        Me.Link2.ToolTipText = "上移记录"
        '
        'Link1
        '
        Me.Link1.Command = Me.Move1
        Me.Link1.Delimiter = True
        Me.Link1.SortOrder = 1
        '
        'Link6
        '
        Me.Link6.Command = Me.Control1
        '
        'Label3
        '
        Me.Label3.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.Label3.Location = New System.Drawing.Point(223, 5)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(35, 15)
        Me.Label3.TabIndex = 4
        Me.Label3.Tag = Nothing
        Me.Label3.Text = "Σ=1"
        Me.Label3.TextAlign = System.Drawing.ContentAlignment.BottomCenter
        Me.Label3.TextDetached = True
        '
        'Panel1
        '
        Me.Panel1.Controls.Add(Me.Label3)
        Me.Panel1.Controls.Add(Me.Button2)
        Me.Panel1.Controls.Add(Me.Button1)
        Me.Panel1.Controls.Add(Me.ToolBar1)
        Me.Panel1.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.Panel1.Location = New System.Drawing.Point(0, 262)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(441, 29)
        Me.Panel1.TabIndex = 162
        '
        'Button2
        '
        Me.Button2.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.Button2.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.Button2.DialogResult = System.Windows.Forms.DialogResult.Cancel
        Me.Button2.Location = New System.Drawing.Point(347, 1)
        Me.Button2.Name = "Button2"
        Me.Button2.Size = New System.Drawing.Size(67, 27)
        Me.Button2.TabIndex = 1
        Me.Button2.Tag = "取消"
        Me.Button2.Text = "取消"
        '
        'Button1
        '
        Me.Button1.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.Button1.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.Button1.DialogResult = System.Windows.Forms.DialogResult.None
        Me.Button1.Location = New System.Drawing.Point(274, 1)
        Me.Button1.Name = "Button1"
        Me.Button1.Size = New System.Drawing.Size(67, 27)
        Me.Button1.TabIndex = 0
        Me.Button1.Tag = "保存"
        Me.Button1.Text = "保存"
        '
        'ToolBar1
        '
        Me.ToolBar1.AccessibleName = "Tool Bar"
        Me.ToolBar1.BackColor = System.Drawing.Color.Transparent
        Me.ToolBar1.CommandHolder = Nothing
        Me.ToolBar1.CommandLinks.AddRange(New C1.Win.C1Command.C1CommandLink() {Me.Link6, Me.Link1, Me.Link2, Me.Link7, Me.Link3, Me.Link4, Me.Link5})
        Me.ToolBar1.Controls.Add(Me.Label1)
        Me.ToolBar1.Controls.Add(Me.Label2)
        Me.ToolBar1.Location = New System.Drawing.Point(1, 3)
        Me.ToolBar1.MinButtonSize = 22
        Me.ToolBar1.Movable = False
        Me.ToolBar1.Name = "ToolBar1"
        Me.ToolBar1.Size = New System.Drawing.Size(207, 22)
        Me.ToolBar1.Text = "C1ToolBar2"
        Me.ToolBar1.VisualStyle = C1.Win.C1Command.VisualStyle.Custom
        Me.ToolBar1.VisualStyleBase = C1.Win.C1Command.VisualStyle.Classic
        '
        'SerialNo_Tb
        '
        Me.SerialNo_Tb.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SerialNo_Tb.Captain = "排列顺序"
        Me.SerialNo_Tb.CaptainBackColor = System.Drawing.Color.Transparent
        Me.SerialNo_Tb.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.SerialNo_Tb.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.SerialNo_Tb.CaptainWidth = 60.0!
        Me.SerialNo_Tb.ContentForeColor = System.Drawing.Color.Black
        Me.SerialNo_Tb.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.SerialNo_Tb.Location = New System.Drawing.Point(23, 164)
        Me.SerialNo_Tb.Multiline = False
        Me.SerialNo_Tb.Name = "SerialNo_Tb"
        Me.SerialNo_Tb.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.SerialNo_Tb.ReadOnly = False
        Me.SerialNo_Tb.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.SerialNo_Tb.SelectionStart = 0
        Me.SerialNo_Tb.SelectStart = 0
        Me.SerialNo_Tb.Size = New System.Drawing.Size(194, 20)
        Me.SerialNo_Tb.TabIndex = 167
        Me.SerialNo_Tb.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.SerialNo_Tb.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.SerialNo_Tb.Watermark = Nothing
        '
        'MaterialsDict13
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(441, 291)
        Me.Controls.Add(Me.TableLayoutPanel1)
        Me.Controls.Add(Me.Panel1)
        Me.Name = "MaterialsDict13"
        Me.Text = "物    资"
        CType(Me.C1CommandHolder1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.TableLayoutPanel1.ResumeLayout(False)
        Me.TableLayoutPanel1.PerformLayout()
        CType(Me.Label3, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Panel1.ResumeLayout(False)
        Me.ToolBar1.ResumeLayout(False)
        Me.ToolBar1.PerformLayout()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents T_Line1 As System.Windows.Forms.Label
    Friend WithEvents TableLayoutPanel1 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents T_Line2 As System.Windows.Forms.Label
    Friend WithEvents M_Code_Tb As CustomControl.MyTextBox
    Friend WithEvents M_Name_Tb As CustomControl.MyTextBox
    Friend WithEvents M_Py_Tb As CustomControl.MyTextBox
    Friend WithEvents M_Class_Tb As CustomControl.MyTextBox
    Friend WithEvents M_Memo_Tb As CustomControl.MyTextBox
    Friend WithEvents IsUse_Cb As System.Windows.Forms.CheckBox
    Friend WithEvents C1CommandHolder1 As C1.Win.C1Command.C1CommandHolder
    Private WithEvents Control1 As C1.Win.C1Command.C1CommandControl
    Friend WithEvents Label2 As System.Windows.Forms.Label
    Private WithEvents Move1 As C1.Win.C1Command.C1Command
    Private WithEvents Move2 As C1.Win.C1Command.C1Command
    Private WithEvents Control2 As C1.Win.C1Command.C1CommandControl
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Private WithEvents Move3 As C1.Win.C1Command.C1Command
    Private WithEvents Move4 As C1.Win.C1Command.C1Command
    Private WithEvents Move5 As C1.Win.C1Command.C1Command
    Friend WithEvents Comm1 As C1.Win.C1Command.C1Command
    Friend WithEvents Comm3 As C1.Win.C1Command.C1Command
    Friend WithEvents Comm2 As C1.Win.C1Command.C1Command
    Friend WithEvents Link5 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link7 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link4 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link3 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link2 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link1 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link6 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Label3 As C1.Win.C1Input.C1Label
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents Button2 As CustomControl.MyButton
    Friend WithEvents Button1 As CustomControl.MyButton
    Friend WithEvents ToolBar1 As C1.Win.C1Command.C1ToolBar
    Friend WithEvents M_Spec_Tb As CustomControl.MyTextBox
    Friend WithEvents M_PackUnit_Tb As CustomControl.MyTextBox
    Friend WithEvents M_ConvertRatio_Tb As CustomControl.MyTextBox
    Friend WithEvents M_BulkUnit_Tb As CustomControl.MyTextBox
    Friend WithEvents M_MateManu_Tb As CustomControl.MyTextBox
    Friend WithEvents SerialNo_Tb As CustomControl.MyTextBox
End Class
