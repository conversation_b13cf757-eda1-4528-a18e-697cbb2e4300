﻿/**  版本信息模板在安装目录下，可自行修改。
* M_DRYB_Jz.cs
*
* 功 能： N/A
* 类 名： M_DRYB_Jz
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/12/20 16:07:50   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace ModelOld
{
	/// <summary>
	/// M_DRYB_Jz:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class M_DRYB_Jz
	{
		public M_DRYB_Jz()
		{}
		#region Model
		private string _jz_code;
		private DateTime? _jz_time;
		private decimal? _bcgrzhzfje;
		private decimal? _bctczfje;
		private decimal? _bcxjzfje;
		private string _jsr_code;
        private DateTime? _lastjz_time;
		/// <summary>
		/// 
		/// </summary>
		public string Jz_Code
		{
			set{ _jz_code=value;}
			get{return _jz_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public DateTime? Jz_Time
		{
			set{ _jz_time=value;}
			get{return _jz_time;}
		}

        /// <summary>
        /// 
        /// </summary>
        public DateTime? LastJz_Time
        {
            set { _lastjz_time = value; }
            get { return _lastjz_time; }
        }

		/// <summary>
		/// 个人账户支付金额
		/// </summary>
		public decimal? BcGrzhZfJe
		{
			set{ _bcgrzhzfje=value;}
			get{return _bcgrzhzfje;}
		}
		/// <summary>
		/// 统筹支付
		/// </summary>
		public decimal? BcTcZfJe
		{
			set{ _bctczfje=value;}
			get{return _bctczfje;}
		}
		/// <summary>
		/// 个人现金支付
		/// </summary>
		public decimal? BcXjZfJe
		{
			set{ _bcxjzfje=value;}
			get{return _bcxjzfje;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Jsr_Code
		{
			set{ _jsr_code=value;}
			get{return _jsr_code;}
		}
		#endregion Model

	}
}

