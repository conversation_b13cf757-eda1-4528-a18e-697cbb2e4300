﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="0" />
    <DataSources isList="true" count="4">
      <处方明细1 Ref="2" type="DataTableSource" isKey="true">
        <Alias>处方明细1</Alias>
        <Columns isList="true" count="9">
          <value>Mx_Code,System.String</value>
          <value>Mz_Id,System.Int32</value>
          <value>Mx_Gg,System.String</value>
          <value>Mz_Sl,System.Decimal</value>
          <value>Mx_XsDw,System.String</value>
          <value>Yp_Yfyl,System.String</value>
          <value>Mz_Money,System.Decimal</value>
          <value>IsJb,System.String</value>
          <value>Yp_Name,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>处方明细1</Name>
        <NameInSource>处方明细1</NameInSource>
      </处方明细1>
      <中药处方 Ref="3" type="DataTableSource" isKey="true">
        <Alias>中药处方</Alias>
        <Columns isList="true" count="10">
          <value>Mx_Code,System.String</value>
          <value>Mz_Id,System.Int32</value>
          <value>Mx_Gg,System.String</value>
          <value>Mz_Sl,System.Decimal</value>
          <value>Mx_XsDw,System.String</value>
          <value>Yp_Yfyl,System.String</value>
          <value>Mz_Money,System.Decimal</value>
          <value>IsJb,System.String</value>
          <value>Yp_Name,System.String</value>
          <value>Mz_DfSl,System.Decimal</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>中药处方</Name>
        <NameInSource>中药处方</NameInSource>
      </中药处方>
      <卫材处方 Ref="4" type="DataTableSource" isKey="true">
        <Alias>卫材处方</Alias>
        <Columns isList="true" count="8">
          <value>Mx_Code,System.String</value>
          <value>Mz_Id,System.Int32</value>
          <value>Mx_Gg,System.String</value>
          <value>Mz_Sl,System.Decimal</value>
          <value>Mx_XsDw,System.String</value>
          <value>Yp_Yfyl,System.String</value>
          <value>Mz_Money,System.Decimal</value>
          <value>Yp_Name,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>卫材处方</Name>
        <NameInSource>卫材处方</NameInSource>
      </卫材处方>
      <诊疗处方 Ref="5" type="DataTableSource" isKey="true">
        <Alias>诊疗处方</Alias>
        <Columns isList="true" count="8">
          <value>Mx_Code,System.String</value>
          <value>Mz_Id,System.Int32</value>
          <value>Mx_Gg,System.String</value>
          <value>Mz_Sl,System.Decimal</value>
          <value>Mx_XsDw,System.String</value>
          <value>Yp_Yfyl,System.String</value>
          <value>Mz_Money,System.Decimal</value>
          <value>Yp_Name,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>诊疗处方</Name>
        <NameInSource>诊疗处方</NameInSource>
      </诊疗处方>
    </DataSources>
    <Relations isList="true" count="0" />
    <Report isRef="0" />
    <Variables isList="true" count="13">
      <value>,医生姓名,医生姓名,System.String,,False,False</value>
      <value>,经手人,经手人,System.String,,False,False</value>
      <value>,门诊编号,门诊编号,System.String,,False,False</value>
      <value>,科室,科室,System.String,,False,False</value>
      <value>,打印时间,打印时间,System.String,,False,False</value>
      <value>,患者姓名,患者姓名,System.String,,False,False</value>
      <value>,患者性别,患者性别,System.String,,False,False</value>
      <value>,年龄,年龄,System.String,,False,False</value>
      <value>,类别,类别,System.String,,False,False</value>
      <value>,疾病,疾病,System.String,,False,False</value>
      <value>,金额,金额,System.String,,False,False</value>
      <value>,标题,标题,System.String,,False,False</value>
      <value>,用法用量,用法用量,System.String,,False,False</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="4">
    <Page1 Ref="6" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="4">
        <ReportTitleBand1 Ref="7" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,17.2,0</ClientRectangle>
          <Components isList="true" count="0" />
          <Conditions isList="true" count="0" />
          <Guid>651739b2c3d840d4b6b05fcfedb47a12</Guid>
          <Name>ReportTitleBand1</Name>
          <Page isRef="6" />
          <Parent isRef="6" />
        </ReportTitleBand1>
        <GroupHeaderBand1 Ref="8" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,1.2,17.2,3</ClientRectangle>
          <Components isList="true" count="16">
            <Text1 Ref="9" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>13.1,0.8,4.1,0.65</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>ae1056b6222f44548451a630475d9696</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text1</Name>
              <Page isRef="6" />
              <Parent isRef="8" />
              <Text>NO.{门诊编号}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text1>
            <Text3 Ref="10" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,17.2,0.79</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,18,Bold,Point,False,0</Font>
              <Guid>373a84218f7f4d0e9cb98424a105bc39</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text3</Name>
              <Page isRef="6" />
              <Parent isRef="8" />
              <Text>{标题}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text3>
            <Text4 Ref="11" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.8,1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>843e853605874d01b372d01f815ddfcf</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text4</Name>
              <Page isRef="6" />
              <Parent isRef="8" />
              <Text>科别:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text4>
            <Text6 Ref="12" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.8,0.8,4.5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>2ce147c089e94901adfd9dcf1ea503c6</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text6</Name>
              <Page isRef="6" />
              <Parent isRef="8" />
              <Text>打印时间：{打印时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text6>
            <Text5 Ref="13" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1,0.8,3.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>bc4302cb138a4599a13394731c5c6158</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text5</Name>
              <Page isRef="6" />
              <Parent isRef="8" />
              <Text>{科室}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text5>
            <Text7 Ref="14" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>9.3,0.8,1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>3256b6ca0058477eb8d09bd7fdb7011d</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text7</Name>
              <Page isRef="6" />
              <Parent isRef="8" />
              <Text>费别:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text7>
            <Text8 Ref="15" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>10.3,0.8,2.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>49d0be77f0dc404487d2193b361ac6c8</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text8</Name>
              <Page isRef="6" />
              <Parent isRef="8" />
              <Text>{类别}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text8>
            <Text9 Ref="16" type="Text" isKey="true">
              <Border>Top, Right, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.4,1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>e239b6efbf3b4b90b93997ba2912f223</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text9</Name>
              <Page isRef="6" />
              <Parent isRef="8" />
              <Text>姓名</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text9>
            <Text12 Ref="17" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1,1.4,2.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>0223bc4051aa4238a08e0ab773236515</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text12</Name>
              <Page isRef="6" />
              <Parent isRef="8" />
              <Text>{患者姓名}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text12>
            <Text13 Ref="18" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.7,1.4,1.1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>70b8d262ead8450fa8b75b9d484c4ceb</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text13</Name>
              <Page isRef="6" />
              <Parent isRef="8" />
              <Text>年龄</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text13>
            <Text14 Ref="19" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.8,1.4,1.3,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>dd0fede85ae24ce8b91e32fd4daf3fc6</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text14</Name>
              <Page isRef="6" />
              <Parent isRef="8" />
              <Text>{年龄}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text14>
            <Text15 Ref="20" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6.1,1.4,1.1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>b40dfcaa438a450b97dca30c40a67694</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text15</Name>
              <Page isRef="6" />
              <Parent isRef="8" />
              <Text>性别</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text15>
            <Text16 Ref="21" type="Text" isKey="true">
              <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.2,1.4,1.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>0b9437b2cdf74e0b86028f322a4d1d9f</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text16</Name>
              <Page isRef="6" />
              <Parent isRef="8" />
              <Text>{患者性别}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text16>
            <Text11 Ref="22" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>8.9,1.4,1.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>c91a17fefb844d6bb87f13a7ec32aea1</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text11</Name>
              <Page isRef="6" />
              <Parent isRef="8" />
              <Text>临床诊断</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text11>
            <Text17 Ref="23" type="Text" isKey="true">
              <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>10.6,1.4,6.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>edb57b29db5b4cc885969bad9922abe5</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text17</Name>
              <Page isRef="6" />
              <Parent isRef="8" />
              <Text>{疾病}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text17>
            <Text18 Ref="24" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,2,17.2,0.9</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Times New Roman,21.75,Bold</Font>
              <Guid>4bd9191f990c4c088455976e81922396</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text18</Name>
              <Page isRef="6" />
              <Parent isRef="8" />
              <Text>RP:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text18>
          </Components>
          <Condition>{处方明细1.Mx_Code}</Condition>
          <Conditions isList="true" count="0" />
          <Guid>a749f17ac18945828b0409dc59e41855</Guid>
          <Name>GroupHeaderBand1</Name>
          <Page isRef="6" />
          <Parent isRef="6" />
        </GroupHeaderBand1>
        <DataBand1 Ref="25" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,5,17.2,1.8</ClientRectangle>
          <Columns>2</Columns>
          <Components isList="true" count="5">
            <Text21 Ref="26" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.01,4.7,1.2</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>335d237023c640f48874584ffcb3882b</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text21</Name>
              <Page isRef="6" />
              <Parent isRef="25" />
              <Text>{处方明细1.Yp_Name}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text21>
            <Text32 Ref="27" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.7,0.01,1.9,1.2</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>17baed80d9c440aab61542cd4ee9153f</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text32</Name>
              <Page isRef="6" />
              <Parent isRef="25" />
              <Text>{处方明细1.Mx_Gg}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text32>
            <Text34 Ref="28" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>6.6,0.01,1,1.2</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>67eae76af2ca4747948d105808e0a1f9</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text34</Name>
              <NullValue> </NullValue>
              <Page isRef="6" />
              <Parent isRef="25" />
              <Text>{处方明细1.Mz_Sl}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="29" type="CustomFormat" isKey="true">
                <StringFormat>0.####</StringFormat>
              </TextFormat>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text34>
            <Text10 Ref="30" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7.6,0.01,1,1.2</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>66bfc1af0a874e828c35b08e61114287</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text10</Name>
              <Page isRef="6" />
              <Parent isRef="25" />
              <Text>{处方明细1.Mx_XsDw}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text10>
            <Text33 Ref="31" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.2,8.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text33</Name>
              <Page isRef="6" />
              <Parent isRef="25" />
              <Text>{IIF(处方明细1.Yp_Yfyl=="用法： "," ",处方明细1.Yp_Yfyl)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text33>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>处方明细1</DataSourceName>
          <Filters isList="true" count="1">
            <value>,NotEqualTo,,,String</value>
          </Filters>
          <Guid>c6a9e6061c3e4378892221439d6e2dcf</Guid>
          <Name>DataBand1</Name>
          <Page isRef="6" />
          <Parent isRef="6" />
          <Sort isList="true" count="2">
            <value>ASC</value>
            <value>Mz_Id</value>
          </Sort>
        </DataBand1>
        <GroupFooterBand1 Ref="32" type="GroupFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,7.6,17.2,2.3</ClientRectangle>
          <Components isList="true" count="15">
            <Text20 Ref="33" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0.9,1,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>37f37e18fc2e4e248008d335c7ce7b47</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text20</Name>
              <Page isRef="6" />
              <Parent isRef="32" />
              <Text>{医生姓名}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text20>
            <Text23 Ref="34" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>5.7,1.6,1.5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>149ccb1fea6a4a15b67daf4eaaa7b47b</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text23</Name>
              <Page isRef="6" />
              <Parent isRef="32" />
              <Text>核对发药</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text23>
            <Text25 Ref="35" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.2,0.4,2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>e79139bf1f7b43b5b98231da53f67ae7</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text25</Name>
              <Page isRef="6" />
              <Parent isRef="32" />
              <Text>{经手人}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text25>
            <Text27 Ref="36" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>13.4,1,1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>808943933f7249519419e61a9e7eb63f</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text27</Name>
              <Page isRef="6" />
              <Parent isRef="32" />
              <Text>金额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text27>
            <Text30 Ref="37" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>14.4,1,2.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>d35c598bc8b040b3a899c23b2a10978d</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text30</Name>
              <Page isRef="6" />
              <Parent isRef="32" />
              <Text>{Sum(GroupHeaderBand1,处方明细1.Mz_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Totals</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text30>
            <Text31 Ref="38" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>10.2,0.4,3.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>0d3ab5c73b7543d18fcde5d9305994e7</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text31</Name>
              <Page isRef="6" />
              <Parent isRef="32" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text31>
            <Text22 Ref="39" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>5.7,0.4,1.5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>db469fbe118448789e08e5d3a82127fe</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text22</Name>
              <Page isRef="6" />
              <Parent isRef="32" />
              <Text>审核调配</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text22>
            <Text26 Ref="40" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>9.2,1.6,1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>f8b7d5f80de247d0b2ff2af240acd473</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text26</Name>
              <Page isRef="6" />
              <Parent isRef="32" />
              <Text>签字</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text26>
            <Text29 Ref="41" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>10.2,1.6,3.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>d5248e1de5f348889595e826aa476c39</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text29</Name>
              <Page isRef="6" />
              <Parent isRef="32" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text29>
            <Text24 Ref="42" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.2,1.6,2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>3ae4157d046e4e2f8cd32c03076c071f</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text24</Name>
              <Page isRef="6" />
              <Parent isRef="32" />
              <Text>{经手人}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text24>
            <Text28 Ref="43" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>9.2,0.4,1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>71d876062e6241c7aebf603ba01f6c05</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text28</Name>
              <Page isRef="6" />
              <Parent isRef="32" />
              <Text>签字</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text28>
            <Text19 Ref="44" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1,0.9,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>590cfd4cefa5495d9f52e53fb003c419</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text19</Name>
              <Page isRef="6" />
              <Parent isRef="32" />
              <Text>医师</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text19>
            <Text2 Ref="45" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.7,1,2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>a2748d4cd0b04947bcefc418220d3bec</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text2</Name>
              <Page isRef="6" />
              <Parent isRef="32" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text2>
            <Text35 Ref="46" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.7,1,1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>1cf3b0243b524ab8967ccc3fb71be8ef</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text35</Name>
              <Page isRef="6" />
              <Parent isRef="32" />
              <Text>签字</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text35>
            <HorizontalLinePrimitive1 Ref="47" type="HorizontalLinePrimitive" isKey="true">
              <ClientRectangle>0,0,17.2,0.0254</ClientRectangle>
              <Color>Black</Color>
              <EndCap Ref="48" type="Cap" isKey="true">
                <Color>Black</Color>
              </EndCap>
              <Name>HorizontalLinePrimitive1</Name>
              <Page isRef="6" />
              <Parent isRef="32" />
              <StartCap Ref="49" type="Cap" isKey="true">
                <Color>Black</Color>
              </StartCap>
            </HorizontalLinePrimitive1>
          </Components>
          <Conditions isList="true" count="0" />
          <Guid>fe7e19ad84744d48a19c766f037e0da7</Guid>
          <Name>GroupFooterBand1</Name>
          <Page isRef="6" />
          <Parent isRef="6" />
        </GroupFooterBand1>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>e4e1e4db58f545af984c08e14b4348db</Guid>
      <Margins>2,1.8,0.8,1</Margins>
      <Name>Page1</Name>
      <PageHeight>12.5</PageHeight>
      <PageWidth>21</PageWidth>
      <Report isRef="0" />
      <Watermark Ref="50" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page1>
    <Page2 Ref="51" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="6">
        <Text136 Ref="52" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,6.4,1.6,0.4</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,9</Font>
          <Guid>df4cc0db8c9047e395fcebd826524546</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text136</Name>
          <Page isRef="51" />
          <Parent isRef="51" />
          <Text>用法用量:</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
        </Text136>
        <Text137 Ref="53" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>1.6,6.4,5.2,0.4</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,9</Font>
          <Guid>90dd421f16c042f4852c162bbe5c8b15</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text137</Name>
          <Page isRef="51" />
          <Parent isRef="51" />
          <Text>{用法用量}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
        </Text137>
        <ReportTitleBand2 Ref="54" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,17.2,0</ClientRectangle>
          <Components isList="true" count="0" />
          <Conditions isList="true" count="0" />
          <Guid>651739b2c3d840d4b6b05fcfedb47a12</Guid>
          <Name>ReportTitleBand2</Name>
          <Page isRef="51" />
          <Parent isRef="51" />
        </ReportTitleBand2>
        <GroupHeaderBand2 Ref="55" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,1.2,17.2,2.8</ClientRectangle>
          <Components isList="true" count="16">
            <Text36 Ref="56" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>13.3,0.8,3.9,0.65</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>ae1056b6222f44548451a630475d9696</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text36</Name>
              <Page isRef="51" />
              <Parent isRef="55" />
              <Text>NO.{门诊编号}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text36>
            <Text37 Ref="57" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,17.2,0.79</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,18,Bold,Point,False,0</Font>
              <Guid>373a84218f7f4d0e9cb98424a105bc39</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text37</Name>
              <Page isRef="51" />
              <Parent isRef="55" />
              <Text>{标题}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text37>
            <Text38 Ref="58" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.8,1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>843e853605874d01b372d01f815ddfcf</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text38</Name>
              <Page isRef="51" />
              <Parent isRef="55" />
              <Text>科别:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text38>
            <Text39 Ref="59" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.8,0.8,4.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>2ce147c089e94901adfd9dcf1ea503c6</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text39</Name>
              <Page isRef="51" />
              <Parent isRef="55" />
              <Text>打印时间：{打印时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text39>
            <Text40 Ref="60" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1,0.8,3.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>bc4302cb138a4599a13394731c5c6158</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text40</Name>
              <Page isRef="51" />
              <Parent isRef="55" />
              <Text>{科室}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text40>
            <Text41 Ref="61" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>9.5,0.8,1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>3256b6ca0058477eb8d09bd7fdb7011d</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text41</Name>
              <Page isRef="51" />
              <Parent isRef="55" />
              <Text>费别:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text41>
            <Text42 Ref="62" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>10.5,0.8,2.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>49d0be77f0dc404487d2193b361ac6c8</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text42</Name>
              <Page isRef="51" />
              <Parent isRef="55" />
              <Text>{类别}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text42>
            <Text43 Ref="63" type="Text" isKey="true">
              <Border>Top, Right, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.4,1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>e239b6efbf3b4b90b93997ba2912f223</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text43</Name>
              <Page isRef="51" />
              <Parent isRef="55" />
              <Text>姓名</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text43>
            <Text44 Ref="64" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1,1.4,2.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>0223bc4051aa4238a08e0ab773236515</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text44</Name>
              <Page isRef="51" />
              <Parent isRef="55" />
              <Text>{患者姓名}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text44>
            <Text45 Ref="65" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.7,1.4,1.1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>70b8d262ead8450fa8b75b9d484c4ceb</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text45</Name>
              <Page isRef="51" />
              <Parent isRef="55" />
              <Text>年龄</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text45>
            <Text46 Ref="66" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.8,1.4,1.3,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>dd0fede85ae24ce8b91e32fd4daf3fc6</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text46</Name>
              <Page isRef="51" />
              <Parent isRef="55" />
              <Text>{年龄}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text46>
            <Text47 Ref="67" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6.1,1.4,1.1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>b40dfcaa438a450b97dca30c40a67694</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text47</Name>
              <Page isRef="51" />
              <Parent isRef="55" />
              <Text>性别</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text47>
            <Text48 Ref="68" type="Text" isKey="true">
              <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.2,1.4,1.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>0b9437b2cdf74e0b86028f322a4d1d9f</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text48</Name>
              <Page isRef="51" />
              <Parent isRef="55" />
              <Text>{患者性别}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text48>
            <Text49 Ref="69" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>8.9,1.4,1.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>c91a17fefb844d6bb87f13a7ec32aea1</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text49</Name>
              <Page isRef="51" />
              <Parent isRef="55" />
              <Text>临床诊断</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text49>
            <Text50 Ref="70" type="Text" isKey="true">
              <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>10.6,1.4,6.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>edb57b29db5b4cc885969bad9922abe5</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text50</Name>
              <Page isRef="51" />
              <Parent isRef="55" />
              <Text>{疾病}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text50>
            <Text51 Ref="71" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,2,17.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Times New Roman,21.75,Bold</Font>
              <Guid>4bd9191f990c4c088455976e81922396</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text51</Name>
              <Page isRef="51" />
              <Parent isRef="55" />
              <Text>RP:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text51>
          </Components>
          <Condition>{中药处方.Mx_Code}</Condition>
          <Conditions isList="true" count="0" />
          <Guid>a749f17ac18945828b0409dc59e41855</Guid>
          <Name>GroupHeaderBand2</Name>
          <Page isRef="51" />
          <Parent isRef="51" />
        </GroupHeaderBand2>
        <DataBand2 Ref="72" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,4.8,17.2,0.5</ClientRectangle>
          <Columns>4</Columns>
          <Components isList="true" count="3">
            <Text52 Ref="73" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.01,2.3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>335d237023c640f48874584ffcb3882b</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text52</Name>
              <Page isRef="51" />
              <Parent isRef="72" />
              <Text>{中药处方.Yp_Name}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text52>
            <Text54 Ref="74" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.3,0.01,1,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>67eae76af2ca4747948d105808e0a1f9</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text54</Name>
              <NullValue> </NullValue>
              <Page isRef="51" />
              <Parent isRef="72" />
              <Text>{中药处方.Mz_DfSl}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="75" type="CustomFormat" isKey="true">
                <StringFormat>0.####</StringFormat>
              </TextFormat>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text54>
            <Text55 Ref="76" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>3.3,0.01,1,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>66bfc1af0a874e828c35b08e61114287</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text55</Name>
              <Page isRef="51" />
              <Parent isRef="72" />
              <Text>{中药处方.Mx_XsDw}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text55>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>中药处方</DataSourceName>
          <Filters isList="true" count="1">
            <value>,NotEqualTo,,,String</value>
          </Filters>
          <Guid>c6a9e6061c3e4378892221439d6e2dcf</Guid>
          <Name>DataBand2</Name>
          <Page isRef="51" />
          <Parent isRef="51" />
          <Sort isList="true" count="2">
            <value>ASC</value>
            <value>Mz_Id</value>
          </Sort>
        </DataBand2>
        <GroupFooterBand2 Ref="77" type="GroupFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,6.1,17.2,2.3</ClientRectangle>
          <Components isList="true" count="15">
            <Text57 Ref="78" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0.9,1,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>37f37e18fc2e4e248008d335c7ce7b47</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text57</Name>
              <Page isRef="51" />
              <Parent isRef="77" />
              <Text>{医生姓名}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text57>
            <Text58 Ref="79" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>5.7,1.6,1.5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>149ccb1fea6a4a15b67daf4eaaa7b47b</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text58</Name>
              <Page isRef="51" />
              <Parent isRef="77" />
              <Text>核对发药</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text58>
            <Text59 Ref="80" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.2,0.4,2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>e79139bf1f7b43b5b98231da53f67ae7</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text59</Name>
              <Page isRef="51" />
              <Parent isRef="77" />
              <Text>{经手人}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text59>
            <Text60 Ref="81" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>13.4,1,1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>808943933f7249519419e61a9e7eb63f</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text60</Name>
              <Page isRef="51" />
              <Parent isRef="77" />
              <Text>金额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text60>
            <Text61 Ref="82" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>14.4,1,2.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>d35c598bc8b040b3a899c23b2a10978d</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text61</Name>
              <Page isRef="51" />
              <Parent isRef="77" />
              <Text>{Sum(GroupHeaderBand2,中药处方.Mz_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Totals</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text61>
            <Text62 Ref="83" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>10.2,0.4,3.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>0d3ab5c73b7543d18fcde5d9305994e7</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text62</Name>
              <Page isRef="51" />
              <Parent isRef="77" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text62>
            <Text63 Ref="84" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>5.7,0.4,1.5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>db469fbe118448789e08e5d3a82127fe</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text63</Name>
              <Page isRef="51" />
              <Parent isRef="77" />
              <Text>审核调配</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text63>
            <Text64 Ref="85" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>9.2,1.6,1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>f8b7d5f80de247d0b2ff2af240acd473</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text64</Name>
              <Page isRef="51" />
              <Parent isRef="77" />
              <Text>签字</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text64>
            <Text65 Ref="86" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>10.2,1.6,3.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>d5248e1de5f348889595e826aa476c39</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text65</Name>
              <Page isRef="51" />
              <Parent isRef="77" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text65>
            <Text66 Ref="87" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.2,1.6,2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>3ae4157d046e4e2f8cd32c03076c071f</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text66</Name>
              <Page isRef="51" />
              <Parent isRef="77" />
              <Text>{经手人}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text66>
            <Text67 Ref="88" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>9.2,0.4,1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>71d876062e6241c7aebf603ba01f6c05</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text67</Name>
              <Page isRef="51" />
              <Parent isRef="77" />
              <Text>签字</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text67>
            <Text68 Ref="89" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1,0.9,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>590cfd4cefa5495d9f52e53fb003c419</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text68</Name>
              <Page isRef="51" />
              <Parent isRef="77" />
              <Text>医师</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text68>
            <Text69 Ref="90" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.7,1,2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>a2748d4cd0b04947bcefc418220d3bec</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text69</Name>
              <Page isRef="51" />
              <Parent isRef="77" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text69>
            <Text70 Ref="91" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.7,1,1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>1cf3b0243b524ab8967ccc3fb71be8ef</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text70</Name>
              <Page isRef="51" />
              <Parent isRef="77" />
              <Text>签字</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text70>
            <HorizontalLinePrimitive2 Ref="92" type="HorizontalLinePrimitive" isKey="true">
              <ClientRectangle>0,0,17.2,0.0254</ClientRectangle>
              <Color>Black</Color>
              <EndCap Ref="93" type="Cap" isKey="true">
                <Color>Black</Color>
              </EndCap>
              <Name>HorizontalLinePrimitive2</Name>
              <Page isRef="51" />
              <Parent isRef="77" />
              <StartCap Ref="94" type="Cap" isKey="true">
                <Color>Black</Color>
              </StartCap>
            </HorizontalLinePrimitive2>
          </Components>
          <Conditions isList="true" count="0" />
          <Guid>fe7e19ad84744d48a19c766f037e0da7</Guid>
          <Name>GroupFooterBand2</Name>
          <Page isRef="51" />
          <Parent isRef="51" />
        </GroupFooterBand2>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>d2c75988f3994cd0b281677fa37b6f98</Guid>
      <Margins>2,1.8,0.8,1</Margins>
      <Name>Page2</Name>
      <PageHeight>12.5</PageHeight>
      <PageWidth>21</PageWidth>
      <Report isRef="0" />
      <Watermark Ref="95" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page2>
    <Page3 Ref="96" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="4">
        <ReportTitleBand3 Ref="97" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,17.2,0</ClientRectangle>
          <Components isList="true" count="0" />
          <Conditions isList="true" count="0" />
          <Guid>651739b2c3d840d4b6b05fcfedb47a12</Guid>
          <Name>ReportTitleBand3</Name>
          <Page isRef="96" />
          <Parent isRef="96" />
        </ReportTitleBand3>
        <GroupHeaderBand3 Ref="98" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,1.2,17.2,2.8</ClientRectangle>
          <Components isList="true" count="16">
            <Text53 Ref="99" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>13.3,0.8,3.9,0.65</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>ae1056b6222f44548451a630475d9696</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text53</Name>
              <Page isRef="96" />
              <Parent isRef="98" />
              <Text>NO.{门诊编号}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text53>
            <Text56 Ref="100" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,17.2,0.79</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,18,Bold,Point,False,0</Font>
              <Guid>373a84218f7f4d0e9cb98424a105bc39</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text56</Name>
              <Page isRef="96" />
              <Parent isRef="98" />
              <Text>{标题}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text56>
            <Text71 Ref="101" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.8,1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>843e853605874d01b372d01f815ddfcf</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text71</Name>
              <Page isRef="96" />
              <Parent isRef="98" />
              <Text>科别:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text71>
            <Text72 Ref="102" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.8,0.8,4.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>2ce147c089e94901adfd9dcf1ea503c6</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text72</Name>
              <Page isRef="96" />
              <Parent isRef="98" />
              <Text>打印时间：{打印时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text72>
            <Text73 Ref="103" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1,0.8,3.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>bc4302cb138a4599a13394731c5c6158</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text73</Name>
              <Page isRef="96" />
              <Parent isRef="98" />
              <Text>{科室}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text73>
            <Text74 Ref="104" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>9.5,0.8,1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>3256b6ca0058477eb8d09bd7fdb7011d</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text74</Name>
              <Page isRef="96" />
              <Parent isRef="98" />
              <Text>费别:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text74>
            <Text75 Ref="105" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>10.5,0.8,2.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>49d0be77f0dc404487d2193b361ac6c8</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text75</Name>
              <Page isRef="96" />
              <Parent isRef="98" />
              <Text>{类别}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text75>
            <Text76 Ref="106" type="Text" isKey="true">
              <Border>Top, Right, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.4,1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>e239b6efbf3b4b90b93997ba2912f223</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text76</Name>
              <Page isRef="96" />
              <Parent isRef="98" />
              <Text>姓名</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text76>
            <Text77 Ref="107" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1,1.4,2.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>0223bc4051aa4238a08e0ab773236515</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text77</Name>
              <Page isRef="96" />
              <Parent isRef="98" />
              <Text>{患者姓名}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text77>
            <Text78 Ref="108" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.7,1.4,1.1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>70b8d262ead8450fa8b75b9d484c4ceb</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text78</Name>
              <Page isRef="96" />
              <Parent isRef="98" />
              <Text>年龄</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text78>
            <Text79 Ref="109" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.8,1.4,1.3,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>dd0fede85ae24ce8b91e32fd4daf3fc6</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text79</Name>
              <Page isRef="96" />
              <Parent isRef="98" />
              <Text>{年龄}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text79>
            <Text80 Ref="110" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6.1,1.4,1.1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>b40dfcaa438a450b97dca30c40a67694</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text80</Name>
              <Page isRef="96" />
              <Parent isRef="98" />
              <Text>性别</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text80>
            <Text81 Ref="111" type="Text" isKey="true">
              <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.2,1.4,1.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>0b9437b2cdf74e0b86028f322a4d1d9f</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text81</Name>
              <Page isRef="96" />
              <Parent isRef="98" />
              <Text>{患者性别}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text81>
            <Text82 Ref="112" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>8.9,1.4,1.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>c91a17fefb844d6bb87f13a7ec32aea1</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text82</Name>
              <Page isRef="96" />
              <Parent isRef="98" />
              <Text>临床诊断</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text82>
            <Text83 Ref="113" type="Text" isKey="true">
              <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>10.6,1.4,6.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>edb57b29db5b4cc885969bad9922abe5</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text83</Name>
              <Page isRef="96" />
              <Parent isRef="98" />
              <Text>{疾病}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text83>
            <Text84 Ref="114" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,2,17.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Times New Roman,21.75,Bold</Font>
              <Guid>4bd9191f990c4c088455976e81922396</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text84</Name>
              <Page isRef="96" />
              <Parent isRef="98" />
              <Text>RP:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text84>
          </Components>
          <Condition>{卫材处方.Mx_Code}</Condition>
          <Conditions isList="true" count="0" />
          <Guid>a749f17ac18945828b0409dc59e41855</Guid>
          <Name>GroupHeaderBand3</Name>
          <Page isRef="96" />
          <Parent isRef="96" />
        </GroupHeaderBand3>
        <DataBand3 Ref="115" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,4.8,17.2,0.5</ClientRectangle>
          <Columns>2</Columns>
          <Components isList="true" count="4">
            <Text85 Ref="116" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.01,4.4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>335d237023c640f48874584ffcb3882b</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text85</Name>
              <Page isRef="96" />
              <Parent isRef="115" />
              <Text>{卫材处方.Yp_Name}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text85>
            <Text86 Ref="117" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>6.9,0.01,0.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>67eae76af2ca4747948d105808e0a1f9</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text86</Name>
              <NullValue> </NullValue>
              <Page isRef="96" />
              <Parent isRef="115" />
              <Text>{卫材处方.Mz_Sl}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="118" type="CustomFormat" isKey="true">
                <StringFormat>0.####</StringFormat>
              </TextFormat>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text86>
            <Text87 Ref="119" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7.6,0.01,1,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>66bfc1af0a874e828c35b08e61114287</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text87</Name>
              <Page isRef="96" />
              <Parent isRef="115" />
              <Text>{卫材处方.Mx_XsDw}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text87>
            <Text135 Ref="120" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.4,0,2.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>b26c9c0296cd47e797c22a8b5b910962</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text135</Name>
              <NullValue> </NullValue>
              <Page isRef="96" />
              <Parent isRef="115" />
              <Text>{卫材处方.Mx_Gg}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="121" type="CustomFormat" isKey="true">
                <StringFormat>0.####</StringFormat>
              </TextFormat>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text135>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>卫材处方</DataSourceName>
          <Filters isList="true" count="1">
            <value>,NotEqualTo,,,String</value>
          </Filters>
          <Guid>c6a9e6061c3e4378892221439d6e2dcf</Guid>
          <Name>DataBand3</Name>
          <Page isRef="96" />
          <Parent isRef="96" />
          <Sort isList="true" count="2">
            <value>ASC</value>
            <value>Mz_Id</value>
          </Sort>
        </DataBand3>
        <GroupFooterBand3 Ref="122" type="GroupFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,6.1,17.2,2.3</ClientRectangle>
          <Components isList="true" count="15">
            <Text88 Ref="123" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0.9,1,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>37f37e18fc2e4e248008d335c7ce7b47</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text88</Name>
              <Page isRef="96" />
              <Parent isRef="122" />
              <Text>{医生姓名}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text88>
            <Text89 Ref="124" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>5.7,1.6,1.5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>149ccb1fea6a4a15b67daf4eaaa7b47b</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text89</Name>
              <Page isRef="96" />
              <Parent isRef="122" />
              <Text>核对发药</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text89>
            <Text90 Ref="125" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.2,0.4,2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>e79139bf1f7b43b5b98231da53f67ae7</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text90</Name>
              <Page isRef="96" />
              <Parent isRef="122" />
              <Text>{经手人}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text90>
            <Text91 Ref="126" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>13.4,1,1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>808943933f7249519419e61a9e7eb63f</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text91</Name>
              <Page isRef="96" />
              <Parent isRef="122" />
              <Text>金额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text91>
            <Text92 Ref="127" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>14.4,1,2.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>d35c598bc8b040b3a899c23b2a10978d</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text92</Name>
              <Page isRef="96" />
              <Parent isRef="122" />
              <Text>{Sum(GroupHeaderBand3,卫材处方.Mz_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Totals</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text92>
            <Text93 Ref="128" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>10.2,0.4,3.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>0d3ab5c73b7543d18fcde5d9305994e7</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text93</Name>
              <Page isRef="96" />
              <Parent isRef="122" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text93>
            <Text94 Ref="129" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>5.7,0.4,1.5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>db469fbe118448789e08e5d3a82127fe</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text94</Name>
              <Page isRef="96" />
              <Parent isRef="122" />
              <Text>审核调配</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text94>
            <Text95 Ref="130" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>9.2,1.6,1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>f8b7d5f80de247d0b2ff2af240acd473</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text95</Name>
              <Page isRef="96" />
              <Parent isRef="122" />
              <Text>签字</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text95>
            <Text96 Ref="131" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>10.2,1.6,3.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>d5248e1de5f348889595e826aa476c39</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text96</Name>
              <Page isRef="96" />
              <Parent isRef="122" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text96>
            <Text97 Ref="132" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.2,1.6,2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>3ae4157d046e4e2f8cd32c03076c071f</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text97</Name>
              <Page isRef="96" />
              <Parent isRef="122" />
              <Text>{经手人}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text97>
            <Text98 Ref="133" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>9.2,0.4,1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>71d876062e6241c7aebf603ba01f6c05</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text98</Name>
              <Page isRef="96" />
              <Parent isRef="122" />
              <Text>签字</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text98>
            <Text99 Ref="134" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1,0.9,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>590cfd4cefa5495d9f52e53fb003c419</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text99</Name>
              <Page isRef="96" />
              <Parent isRef="122" />
              <Text>医师</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text99>
            <Text100 Ref="135" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.7,1,2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>a2748d4cd0b04947bcefc418220d3bec</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text100</Name>
              <Page isRef="96" />
              <Parent isRef="122" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text100>
            <Text101 Ref="136" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.7,1,1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>1cf3b0243b524ab8967ccc3fb71be8ef</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text101</Name>
              <Page isRef="96" />
              <Parent isRef="122" />
              <Text>签字</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text101>
            <HorizontalLinePrimitive3 Ref="137" type="HorizontalLinePrimitive" isKey="true">
              <ClientRectangle>0,0,17.2,0.0254</ClientRectangle>
              <Color>Black</Color>
              <EndCap Ref="138" type="Cap" isKey="true">
                <Color>Black</Color>
              </EndCap>
              <Name>HorizontalLinePrimitive3</Name>
              <Page isRef="96" />
              <Parent isRef="122" />
              <StartCap Ref="139" type="Cap" isKey="true">
                <Color>Black</Color>
              </StartCap>
            </HorizontalLinePrimitive3>
          </Components>
          <Conditions isList="true" count="0" />
          <Guid>fe7e19ad84744d48a19c766f037e0da7</Guid>
          <Name>GroupFooterBand3</Name>
          <Page isRef="96" />
          <Parent isRef="96" />
        </GroupFooterBand3>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>c8958f96882344c9bb48cab985a8c3c4</Guid>
      <Margins>2,1.8,0.8,1</Margins>
      <Name>Page3</Name>
      <PageHeight>12.5</PageHeight>
      <PageWidth>21</PageWidth>
      <Report isRef="0" />
      <Watermark Ref="140" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page3>
    <Page4 Ref="141" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="4">
        <ReportTitleBand4 Ref="142" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,17.2,0</ClientRectangle>
          <Components isList="true" count="0" />
          <Conditions isList="true" count="0" />
          <Guid>651739b2c3d840d4b6b05fcfedb47a12</Guid>
          <Name>ReportTitleBand4</Name>
          <Page isRef="141" />
          <Parent isRef="141" />
        </ReportTitleBand4>
        <GroupHeaderBand4 Ref="143" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,1.2,17.2,2.8</ClientRectangle>
          <Components isList="true" count="16">
            <Text102 Ref="144" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>13.3,0.8,3.9,0.65</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>ae1056b6222f44548451a630475d9696</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text102</Name>
              <Page isRef="141" />
              <Parent isRef="143" />
              <Text>NO.{门诊编号}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text102>
            <Text103 Ref="145" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,17.2,0.79</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,18,Bold,Point,False,0</Font>
              <Guid>373a84218f7f4d0e9cb98424a105bc39</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text103</Name>
              <Page isRef="141" />
              <Parent isRef="143" />
              <Text>{标题}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text103>
            <Text104 Ref="146" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.8,1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>843e853605874d01b372d01f815ddfcf</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text104</Name>
              <Page isRef="141" />
              <Parent isRef="143" />
              <Text>科别:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text104>
            <Text105 Ref="147" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.8,0.8,4.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>2ce147c089e94901adfd9dcf1ea503c6</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text105</Name>
              <Page isRef="141" />
              <Parent isRef="143" />
              <Text>打印时间：{打印时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text105>
            <Text106 Ref="148" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1,0.8,3.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>bc4302cb138a4599a13394731c5c6158</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text106</Name>
              <Page isRef="141" />
              <Parent isRef="143" />
              <Text>{科室}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text106>
            <Text107 Ref="149" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>9.5,0.8,1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>3256b6ca0058477eb8d09bd7fdb7011d</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text107</Name>
              <Page isRef="141" />
              <Parent isRef="143" />
              <Text>费别:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text107>
            <Text108 Ref="150" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>10.5,0.8,2.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>49d0be77f0dc404487d2193b361ac6c8</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text108</Name>
              <Page isRef="141" />
              <Parent isRef="143" />
              <Text>{类别}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text108>
            <Text109 Ref="151" type="Text" isKey="true">
              <Border>Top, Right, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.4,1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>e239b6efbf3b4b90b93997ba2912f223</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text109</Name>
              <Page isRef="141" />
              <Parent isRef="143" />
              <Text>姓名</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text109>
            <Text110 Ref="152" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1,1.4,2.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>0223bc4051aa4238a08e0ab773236515</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text110</Name>
              <Page isRef="141" />
              <Parent isRef="143" />
              <Text>{患者姓名}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text110>
            <Text111 Ref="153" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.7,1.4,1.1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>70b8d262ead8450fa8b75b9d484c4ceb</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text111</Name>
              <Page isRef="141" />
              <Parent isRef="143" />
              <Text>年龄</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text111>
            <Text112 Ref="154" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.8,1.4,1.3,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>dd0fede85ae24ce8b91e32fd4daf3fc6</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text112</Name>
              <Page isRef="141" />
              <Parent isRef="143" />
              <Text>{年龄}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text112>
            <Text113 Ref="155" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6.1,1.4,1.1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>b40dfcaa438a450b97dca30c40a67694</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text113</Name>
              <Page isRef="141" />
              <Parent isRef="143" />
              <Text>性别</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text113>
            <Text114 Ref="156" type="Text" isKey="true">
              <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.2,1.4,1.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>0b9437b2cdf74e0b86028f322a4d1d9f</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text114</Name>
              <Page isRef="141" />
              <Parent isRef="143" />
              <Text>{患者性别}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text114>
            <Text115 Ref="157" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>8.9,1.4,1.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>c91a17fefb844d6bb87f13a7ec32aea1</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text115</Name>
              <Page isRef="141" />
              <Parent isRef="143" />
              <Text>临床诊断</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text115>
            <Text116 Ref="158" type="Text" isKey="true">
              <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>10.6,1.4,6.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>edb57b29db5b4cc885969bad9922abe5</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text116</Name>
              <Page isRef="141" />
              <Parent isRef="143" />
              <Text>{疾病}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text116>
            <Text117 Ref="159" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,2,17.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Times New Roman,21.75,Bold</Font>
              <Guid>4bd9191f990c4c088455976e81922396</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text117</Name>
              <Page isRef="141" />
              <Parent isRef="143" />
              <Text>RP:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text117>
          </Components>
          <Condition>{诊疗处方.Mx_Code}</Condition>
          <Conditions isList="true" count="0" />
          <Guid>a749f17ac18945828b0409dc59e41855</Guid>
          <Name>GroupHeaderBand4</Name>
          <Page isRef="141" />
          <Parent isRef="141" />
        </GroupHeaderBand4>
        <DataBand4 Ref="160" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,4.8,17.2,0.5</ClientRectangle>
          <Columns>2</Columns>
          <Components isList="true" count="3">
            <Text118 Ref="161" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.01,6.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>335d237023c640f48874584ffcb3882b</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text118</Name>
              <Page isRef="141" />
              <Parent isRef="160" />
              <Text>{诊疗处方.Yp_Name}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text118>
            <Text119 Ref="162" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>6.9,0.01,0.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>67eae76af2ca4747948d105808e0a1f9</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text119</Name>
              <NullValue> </NullValue>
              <Page isRef="141" />
              <Parent isRef="160" />
              <Text>{诊疗处方.Mz_Sl}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="163" type="CustomFormat" isKey="true">
                <StringFormat>0.####</StringFormat>
              </TextFormat>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text119>
            <Text120 Ref="164" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7.6,0.01,1,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>66bfc1af0a874e828c35b08e61114287</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text120</Name>
              <Page isRef="141" />
              <Parent isRef="160" />
              <Text>{诊疗处方.Mx_XsDw}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text120>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>诊疗处方</DataSourceName>
          <Filters isList="true" count="1">
            <value>,NotEqualTo,,,String</value>
          </Filters>
          <Guid>c6a9e6061c3e4378892221439d6e2dcf</Guid>
          <Name>DataBand4</Name>
          <Page isRef="141" />
          <Parent isRef="141" />
          <Sort isList="true" count="2">
            <value>ASC</value>
            <value>Mz_Id</value>
          </Sort>
        </DataBand4>
        <GroupFooterBand4 Ref="165" type="GroupFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,6.1,17.2,2.3</ClientRectangle>
          <Components isList="true" count="15">
            <Text121 Ref="166" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0.9,1,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>37f37e18fc2e4e248008d335c7ce7b47</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text121</Name>
              <Page isRef="141" />
              <Parent isRef="165" />
              <Text>{医生姓名}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text121>
            <Text122 Ref="167" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>5.7,1.6,1.5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>149ccb1fea6a4a15b67daf4eaaa7b47b</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text122</Name>
              <Page isRef="141" />
              <Parent isRef="165" />
              <Text>核对发药</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text122>
            <Text123 Ref="168" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.2,0.4,2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>e79139bf1f7b43b5b98231da53f67ae7</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text123</Name>
              <Page isRef="141" />
              <Parent isRef="165" />
              <Text>{经手人}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text123>
            <Text124 Ref="169" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>13.4,1,1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>808943933f7249519419e61a9e7eb63f</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text124</Name>
              <Page isRef="141" />
              <Parent isRef="165" />
              <Text>金额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text124>
            <Text125 Ref="170" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>14.4,1,2.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>d35c598bc8b040b3a899c23b2a10978d</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text125</Name>
              <Page isRef="141" />
              <Parent isRef="165" />
              <Text>{Sum(GroupHeaderBand4,诊疗处方.Mz_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Totals</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text125>
            <Text126 Ref="171" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>10.2,0.4,3.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>0d3ab5c73b7543d18fcde5d9305994e7</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text126</Name>
              <Page isRef="141" />
              <Parent isRef="165" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text126>
            <Text127 Ref="172" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>5.7,0.4,1.5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>db469fbe118448789e08e5d3a82127fe</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text127</Name>
              <Page isRef="141" />
              <Parent isRef="165" />
              <Text>审核调配</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text127>
            <Text128 Ref="173" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>9.2,1.6,1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>f8b7d5f80de247d0b2ff2af240acd473</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text128</Name>
              <Page isRef="141" />
              <Parent isRef="165" />
              <Text>签字</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text128>
            <Text129 Ref="174" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>10.2,1.6,3.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>d5248e1de5f348889595e826aa476c39</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text129</Name>
              <Page isRef="141" />
              <Parent isRef="165" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text129>
            <Text130 Ref="175" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.2,1.6,2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>3ae4157d046e4e2f8cd32c03076c071f</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text130</Name>
              <Page isRef="141" />
              <Parent isRef="165" />
              <Text>{经手人}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text130>
            <Text131 Ref="176" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>9.2,0.4,1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>71d876062e6241c7aebf603ba01f6c05</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text131</Name>
              <Page isRef="141" />
              <Parent isRef="165" />
              <Text>签字</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text131>
            <Text132 Ref="177" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1,0.9,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>590cfd4cefa5495d9f52e53fb003c419</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text132</Name>
              <Page isRef="141" />
              <Parent isRef="165" />
              <Text>医师</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text132>
            <Text133 Ref="178" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.7,1,2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>a2748d4cd0b04947bcefc418220d3bec</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text133</Name>
              <Page isRef="141" />
              <Parent isRef="165" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text133>
            <Text134 Ref="179" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.7,1,1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>1cf3b0243b524ab8967ccc3fb71be8ef</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text134</Name>
              <Page isRef="141" />
              <Parent isRef="165" />
              <Text>签字</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text134>
            <HorizontalLinePrimitive4 Ref="180" type="HorizontalLinePrimitive" isKey="true">
              <ClientRectangle>0,0,17.2,0.0254</ClientRectangle>
              <Color>Black</Color>
              <EndCap Ref="181" type="Cap" isKey="true">
                <Color>Black</Color>
              </EndCap>
              <Name>HorizontalLinePrimitive4</Name>
              <Page isRef="141" />
              <Parent isRef="165" />
              <StartCap Ref="182" type="Cap" isKey="true">
                <Color>Black</Color>
              </StartCap>
            </HorizontalLinePrimitive4>
          </Components>
          <Conditions isList="true" count="0" />
          <Guid>fe7e19ad84744d48a19c766f037e0da7</Guid>
          <Name>GroupFooterBand4</Name>
          <Page isRef="141" />
          <Parent isRef="141" />
        </GroupFooterBand4>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>e65e92008fb04715ad4470c9d2d0e4c4</Guid>
      <Margins>2,1.8,0.8,1</Margins>
      <Name>Page4</Name>
      <PageHeight>12.5</PageHeight>
      <PageWidth>21</PageWidth>
      <Report isRef="0" />
      <Watermark Ref="183" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page4>
  </Pages>
  <PrinterSettings Ref="184" type="Stimulsoft.Report.Print.StiPrinterSettings" isKey="true" />
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>门诊处方表</ReportAlias>
  <ReportChanged>6/17/2020 3:55:01 PM</ReportChanged>
  <ReportCreated>12/7/2012 3:16:11 PM</ReportCreated>
  <ReportFile>D:\SVNNew\HIs通辽乡镇卫生院\his2010\Rpt\门诊处方表(二分之一A4).mrt</ReportFile>
  <ReportGuid>eda846e1b4c3428e86c9b19e485a90bf</ReportGuid>
  <ReportName>门诊处方表</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2011.2.1026</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class Report : Stimulsoft.Report.StiReport
    {
        public Report()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
        public string 医生姓名;
        public string 经手人;
        public string 门诊编号;
        public string 科室;
        public string 打印时间;
        public string 患者姓名;
        public string 患者性别;
        public string 年龄;
        public string 类别;
        public string 疾病;
        public string 金额;
        public string 标题;
        public Stimulsoft.Report.Components.StiPage Page1;
        public Stimulsoft.Report.Components.StiReportTitleBand ReportTitleBand1;
        public Stimulsoft.Report.Components.StiGroupHeaderBand GroupHeaderBand1;
        public Stimulsoft.Report.Components.StiText Text1;
        public Stimulsoft.Report.Components.StiText Text2;
        public Stimulsoft.Report.Components.StiText Text3;
        public Stimulsoft.Report.Components.StiText Text4;
        public Stimulsoft.Report.Components.StiText Text6;
        public Stimulsoft.Report.Components.StiText Text5;
        public Stimulsoft.Report.Components.StiText Text7;
        public Stimulsoft.Report.Components.StiText Text8;
        public Stimulsoft.Report.Components.StiText Text9;
        public Stimulsoft.Report.Components.StiText Text12;
        public Stimulsoft.Report.Components.StiText Text13;
        public Stimulsoft.Report.Components.StiText Text14;
        public Stimulsoft.Report.Components.StiText Text15;
        public Stimulsoft.Report.Components.StiText Text16;
        public Stimulsoft.Report.Components.StiText Text11;
        public Stimulsoft.Report.Components.StiText Text17;
        public Stimulsoft.Report.Components.StiText Text18;
        public Stimulsoft.Report.Components.StiDataBand DataBand1;
        public Stimulsoft.Report.Components.StiText Text21;
        public Stimulsoft.Report.Components.StiText Text32;
        public Stimulsoft.Report.Components.StiText Text34;
        public Stimulsoft.Report.Components.StiText Text10;
        public Stimulsoft.Report.Components.StiText Text33;
        public Stimulsoft.Report.Components.StiGroupFooterBand GroupFooterBand1;
        public Stimulsoft.Report.Components.StiText Text20;
        public Stimulsoft.Report.Components.StiText Text23;
        public Stimulsoft.Report.Components.StiText Text25;
        public Stimulsoft.Report.Components.StiText Text27;
        public Stimulsoft.Report.Components.StiText Text30;
        public Stimulsoft.Report.Dictionary.StiSumDecimalFunctionService Text30_Sum;
        public Stimulsoft.Report.Components.StiText Text31;
        public Stimulsoft.Report.Components.StiText Text22;
        public Stimulsoft.Report.Components.StiText Text26;
        public Stimulsoft.Report.Components.StiText Text29;
        public Stimulsoft.Report.Components.StiText Text24;
        public Stimulsoft.Report.Components.StiText Text28;
        public Stimulsoft.Report.Components.StiText Text19;
        public Stimulsoft.Report.Components.StiWatermark Page1_Watermark;
        public Stimulsoft.Report.Print.StiPrinterSettings 门诊处方表_PrinterSettings;
        public 处方明细1DataSource 处方明细1;
        
        public void GroupHeaderBand1__GetValue(object sender, Stimulsoft.Report.Events.StiValueEventArgs e)
        {
            e.Value = 处方明细1.Mx_Code;
        }
        
        public void Text1__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "NO." + ToString(sender, 门诊编号, true);
        }
        
        public void Text3__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 标题, true);
        }
        
        public void Text4__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "科别";
        }
        
        public void Text6__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 打印时间, true);
        }
        
        public void Text5__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 科室, true);
        }
        
        public void Text7__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "费别";
        }
        
        public void Text8__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 类别, true);
        }
        
        public void Text9__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "姓名";
        }
        
        public void Text12__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 患者姓名, true);
        }
        
        public void Text13__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "年龄";
        }
        
        public void Text14__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 年龄, true);
        }
        
        public void Text15__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "性别";
        }
        
        public void Text16__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 患者性别, true);
        }
        
        public void Text11__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "临床诊断";
        }
        
        public void Text17__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 疾病, true);
        }
        
        public void Text18__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "RP:";
        }
        
        public void Text21__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 处方明细1.Yp_Name, true);
        }
        
        public void Text32__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 处方明细1.Mx_Gg, true);
        }
        
        public void Text34__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            if (StiNullValuesHelper.IsNull(this, "处方明细1.Mz_Sl"))
            {
                e.Value = " ";
            }
            else
            {
                e.Value = System.String.Format("{0:0.####}", 处方明细1.Mz_Sl);
            }
        }
        
        public void Text10__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 处方明细1.Mx_XsDw, true);
        }
        

			public void Text20__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
			{
			e.Value = ToString(sender, 医生姓名, true);
			}
       
			public void Text23__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
			{
			e.Value = "核对发药";
			}
       
			public void Text25__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
			{
			e.Value = ToString(sender, 经手人, true);
			}
       
			public void Text27__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
			{
			e.Value = "金额";
			}
       
			public void Text30__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
			{
			e.Value = "#%#{Sum(GroupHeaderBand1,处方明细1.Mz_Money)}";
			e.StoreToPrinted = true;
			}
       
			public System.String Text30_GetValue_End(Stimulsoft.Report.Components.StiComponent sender)
			{
			return ToString(sender, ((decimal)(StiReport.ChangeType(this.Text30_Sum.GetValue(), typeof(decimal), true))), true);
			}
       
			public void Text22__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
			{
			e.Value = "审计调配";
			}
       
			public void Text26__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
			{
			e.Value = "签字";
			}
       
			public void Text24__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
			{
			e.Value = ToString(sender, 经手人, true);
			}
       
			public void Text28__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
			{
			e.Value = "签字";
			}
       
			public void Text19__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
			{
			e.Value = "医师";
			}
       
			public void GroupHeaderBand1__BeginRender(object sender, System.EventArgs e)
			{
			this.Text30_Sum.Init();
			this.Text30.TextValue = "";
			}
       
			public void GroupHeaderBand1__EndRender(object sender, System.EventArgs e)
			{
			this.Text30.SetText(new Stimulsoft.Report.Components.StiGetValue(this.Text30_GetValue_End));
			}
       
			public void GroupHeaderBand1__Rendering(object sender, System.EventArgs e)
			{
			this.Text30_Sum.CalcItem(处方明细1.Mz_Money);
			}
       
			private void InitializeComponent()
			{
			this.处方明细1 = new 处方明细1DataSource();
			this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "医生姓名", "医生姓名", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
			this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "经手人", "经手人", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
			this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "门诊编号", "门诊编号", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
			this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "科室", "科室", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
			this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "打印时间", "打印时间", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
			this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "患者姓名", "患者姓名", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
			this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "患者性别", "患者性别", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
			this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "年龄", "年龄", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
			this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "类别", "类别", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
			this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "疾病", "疾病", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
			this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "金额", "金额", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
			this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "标题", "标题", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
			this.NeedsCompiling = false;
			this.Text30_Sum = new Stimulsoft.Report.Dictionary.StiSumDecimalFunctionService();
			// 
			// Variables init
			// 
			this.医生姓名 = "";
			this.经手人 = "";
			this.门诊编号 = "";
			this.科室 = "";
			this.打印时间 = "";
			this.患者姓名 = "";
			this.患者性别 = "";
			this.年龄 = "";
			this.类别 = "";
			this.疾病 = "";
			this.金额 = "";
			this.标题 = "";
			this.EngineVersion = Stimulsoft.Report.Engine.StiEngineVersion.EngineV2;
			this.ReferencedAssemblies = new System.String[] {
				"System.Dll",
				"System.Drawing.Dll",
				"System.Windows.Forms.Dll",
				"System.Data.Dll",
				"System.Xml.Dll",
				"Stimulsoft.Controls.Dll",
				"Stimulsoft.Base.Dll",
				"Stimulsoft.Report.Dll"};
			this.ReportAlias = "门诊处方表";
			// 
			// ReportChanged
			// 
			this.ReportChanged = new DateTime(2012, 12, 18, 17, 31, 36, 921);
			// 
			// ReportCreated
			// 
			this.ReportCreated = new DateTime(2012, 12, 7, 15, 16, 11, 0);
			this.ReportFile = ".\\Rpt\\门诊处方表.mrt";
			this.ReportGuid = "b5cdf9b297c2492da62d2c20a761515d";
			this.ReportName = "门诊处方表";
			this.ReportUnit = Stimulsoft.Report.StiReportUnitType.Centimeters;
			this.ReportVersion = "2011.2.1026";
			this.ScriptLanguage = Stimulsoft.Report.StiReportLanguageType.CSharp;
			// 
			// Page1
			// 
			this.Page1 = new Stimulsoft.Report.Components.StiPage();
			this.Page1.Guid = "e4e1e4db58f545af984c08e14b4348db";
			this.Page1.Name = "Page1";
			this.Page1.PageHeight = 20;
			this.Page1.PageWidth = 10;
			this.Page1.PaperSize = System.Drawing.Printing.PaperKind.A4;
			this.Page1.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 2, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Page1.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			// 
			// ReportTitleBand1
			// 
			this.ReportTitleBand1 = new Stimulsoft.Report.Components.StiReportTitleBand();
			this.ReportTitleBand1.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 0.4, 9.6, 0);
			this.ReportTitleBand1.Guid = "651739b2c3d840d4b6b05fcfedb47a12";
			this.ReportTitleBand1.Name = "ReportTitleBand1";
			this.ReportTitleBand1.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.ReportTitleBand1.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.ReportTitleBand1.Interaction = null;
			// 
			// GroupHeaderBand1
			// 
			this.GroupHeaderBand1 = new Stimulsoft.Report.Components.StiGroupHeaderBand();
			this.GroupHeaderBand1.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 1.2, 9.6, 4.8);
			this.GroupHeaderBand1.GetValue += new Stimulsoft.Report.Events.StiValueEventHandler(this.GroupHeaderBand1__GetValue);
			this.GroupHeaderBand1.Guid = "a749f17ac18945828b0409dc59e41855";
			this.GroupHeaderBand1.Name = "GroupHeaderBand1";
			this.GroupHeaderBand1.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.GroupHeaderBand1.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			// 
			// Text1
			// 
			this.Text1 = new Stimulsoft.Report.Components.StiText();
			this.Text1.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 0, 9.6, 0.45);
			this.Text1.Guid = "ae1056b6222f44548451a630475d9696";
			this.Text1.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
			this.Text1.Name = "Text1";
			this.Text1.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text1__GetValue);
			this.Text1.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text1.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
			this.Text1.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text1.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text1.Font = new System.Drawing.Font("宋体", 10.5F);
			this.Text1.Indicator = null;
			this.Text1.Interaction = null;
			this.Text1.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text1.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text1.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text2
			// 
			this.Text2 = new Stimulsoft.Report.Components.StiText();
			this.Text2.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 0.45, 9.6, 0.55);
			this.Text2.Guid = "e8cbb9d1b5d04f889d1347409f8607ff";
			this.Text2.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
			this.Text2.Name = "Text2";
			this.Text2.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text2.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
			this.Text2.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text2.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text2.Font = new System.Drawing.Font("宋体", 14.25F);
			this.Text2.Indicator = null;
			this.Text2.Interaction = null;
			this.Text2.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text2.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text2.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text3
			// 
			this.Text3 = new Stimulsoft.Report.Components.StiText();
			this.Text3.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 1, 9.6, 0.79);
			this.Text3.Guid = "373a84218f7f4d0e9cb98424a105bc39";
			this.Text3.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
			this.Text3.Name = "Text3";
			this.Text3.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text3__GetValue);
			this.Text3.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text3.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
			this.Text3.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text3.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text3.Font = new System.Drawing.Font("宋体", 18F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
			this.Text3.Indicator = null;
			this.Text3.Interaction = null;
			this.Text3.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text3.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text3.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text4
			// 
			this.Text4 = new Stimulsoft.Report.Components.StiText();
			this.Text4.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 1.8, 1, 0.6);
			this.Text4.Guid = "843e853605874d01b372d01f815ddfcf";
			this.Text4.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
			this.Text4.Name = "Text4";
			this.Text4.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text4__GetValue);
			this.Text4.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text4.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
			this.Text4.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text4.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text4.Font = new System.Drawing.Font("宋体", 10.5F);
			this.Text4.Indicator = null;
			this.Text4.Interaction = null;
			this.Text4.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text4.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text4.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text6
			// 
			this.Text6 = new Stimulsoft.Report.Components.StiText();
			this.Text6.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(3.8, 1.8, 3, 0.6);
			this.Text6.Guid = "2ce147c089e94901adfd9dcf1ea503c6";
			this.Text6.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
			this.Text6.Name = "Text6";
			this.Text6.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text6__GetValue);
			this.Text6.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text6.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
			this.Text6.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text6.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text6.Font = new System.Drawing.Font("宋体", 10.5F);
			this.Text6.Indicator = null;
			this.Text6.Interaction = null;
			this.Text6.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text6.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text6.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text5
			// 
			this.Text5 = new Stimulsoft.Report.Components.StiText();
			this.Text5.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(1, 1.8, 2.8, 0.6);
			this.Text5.Guid = "bc4302cb138a4599a13394731c5c6158";
			this.Text5.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
			this.Text5.Name = "Text5";
			this.Text5.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text5__GetValue);
			this.Text5.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text5.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
			this.Text5.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text5.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text5.Font = new System.Drawing.Font("宋体", 10.5F);
			this.Text5.Indicator = null;
			this.Text5.Interaction = null;
			this.Text5.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text5.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text5.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text7
			// 
			this.Text7 = new Stimulsoft.Report.Components.StiText();
			this.Text7.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(6.8, 1.8, 1, 0.6);
			this.Text7.Guid = "3256b6ca0058477eb8d09bd7fdb7011d";
			this.Text7.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
			this.Text7.Name = "Text7";
			this.Text7.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text7__GetValue);
			this.Text7.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text7.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
			this.Text7.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text7.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text7.Font = new System.Drawing.Font("宋体", 10.5F);
			this.Text7.Indicator = null;
			this.Text7.Interaction = null;
			this.Text7.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text7.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text7.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text8
			// 
			this.Text8 = new Stimulsoft.Report.Components.StiText();
			this.Text8.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(7.8, 1.8, 1.8, 0.6);
			this.Text8.Guid = "49d0be77f0dc404487d2193b361ac6c8";
			this.Text8.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
			this.Text8.Name = "Text8";
			this.Text8.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text8__GetValue);
			this.Text8.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text8.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
			this.Text8.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text8.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text8.Font = new System.Drawing.Font("宋体", 10.5F);
			this.Text8.Indicator = null;
			this.Text8.Interaction = null;
			this.Text8.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text8.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text8.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text9
			// 
			this.Text9 = new Stimulsoft.Report.Components.StiText();
			this.Text9.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 2.4, 1, 0.6);
			this.Text9.Guid = "e239b6efbf3b4b90b93997ba2912f223";
			this.Text9.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
			this.Text9.Name = "Text9";
			this.Text9.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text9__GetValue);
			this.Text9.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text9.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
			this.Text9.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.All, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text9.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text9.Font = new System.Drawing.Font("宋体", 10.5F);
			this.Text9.Indicator = null;
			this.Text9.Interaction = null;
			this.Text9.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text9.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text9.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text12
			// 
			this.Text12 = new Stimulsoft.Report.Components.StiText();
			this.Text12.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(1, 2.4, 2.7, 0.6);
			this.Text12.Guid = "0223bc4051aa4238a08e0ab773236515";
			this.Text12.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
			this.Text12.Name = "Text12";
			this.Text12.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text12__GetValue);
			this.Text12.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text12.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
			this.Text12.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.All, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text12.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text12.Font = new System.Drawing.Font("宋体", 10.5F);
			this.Text12.Indicator = null;
			this.Text12.Interaction = null;
			this.Text12.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text12.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text12.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text13
			// 
			this.Text13 = new Stimulsoft.Report.Components.StiText();
			this.Text13.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(3.7, 2.4, 1.1, 0.6);
			this.Text13.Guid = "70b8d262ead8450fa8b75b9d484c4ceb";
			this.Text13.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
			this.Text13.Name = "Text13";
			this.Text13.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text13__GetValue);
			this.Text13.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text13.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
			this.Text13.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.All, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text13.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text13.Font = new System.Drawing.Font("宋体", 10.5F);
			this.Text13.Indicator = null;
			this.Text13.Interaction = null;
			this.Text13.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text13.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text13.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text14
			// 
			this.Text14 = new Stimulsoft.Report.Components.StiText();
			this.Text14.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(4.8, 2.4, 1.3, 0.6);
			this.Text14.Guid = "dd0fede85ae24ce8b91e32fd4daf3fc6";
			this.Text14.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
			this.Text14.Name = "Text14";
			this.Text14.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text14__GetValue);
			this.Text14.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text14.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
			this.Text14.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.All, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text14.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text14.Font = new System.Drawing.Font("宋体", 10.5F);
			this.Text14.Indicator = null;
			this.Text14.Interaction = null;
			this.Text14.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text14.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text14.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text15
			// 
			this.Text15 = new Stimulsoft.Report.Components.StiText();
			this.Text15.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(6.1, 2.4, 1.1, 0.6);
			this.Text15.Guid = "b40dfcaa438a450b97dca30c40a67694";
			this.Text15.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
			this.Text15.Name = "Text15";
			this.Text15.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text15__GetValue);
			this.Text15.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text15.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
			this.Text15.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.All, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text15.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text15.Font = new System.Drawing.Font("宋体", 10.5F);
			this.Text15.Indicator = null;
			this.Text15.Interaction = null;
			this.Text15.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text15.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text15.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text16
			// 
			this.Text16 = new Stimulsoft.Report.Components.StiText();
			this.Text16.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(7.2, 2.4, 2.4, 0.6);
			this.Text16.Guid = "0b9437b2cdf74e0b86028f322a4d1d9f";
			this.Text16.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
			this.Text16.Name = "Text16";
			this.Text16.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text16__GetValue);
			this.Text16.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text16.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
			this.Text16.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.All, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text16.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text16.Font = new System.Drawing.Font("宋体", 10.5F);
			this.Text16.Indicator = null;
			this.Text16.Interaction = null;
			this.Text16.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text16.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text16.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text11
			// 
			this.Text11 = new Stimulsoft.Report.Components.StiText();
			this.Text11.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 3, 1.9, 0.9);
			this.Text11.Guid = "c91a17fefb844d6bb87f13a7ec32aea1";
			this.Text11.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
			this.Text11.Name = "Text11";
			this.Text11.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text11__GetValue);
			this.Text11.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text11.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
			this.Text11.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.Bottom, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text11.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text11.Font = new System.Drawing.Font("宋体", 10.5F);
			this.Text11.Indicator = null;
			this.Text11.Interaction = null;
			this.Text11.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text11.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text11.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text17
			// 
			this.Text17 = new Stimulsoft.Report.Components.StiText();
			this.Text17.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(1.9, 3, 7.7, 0.9);
			this.Text17.Guid = "edb57b29db5b4cc885969bad9922abe5";
			this.Text17.Name = "Text17";
			this.Text17.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text17__GetValue);
			this.Text17.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text17.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
			this.Text17.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.Bottom, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text17.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text17.Font = new System.Drawing.Font("宋体", 10.5F);
			this.Text17.Indicator = null;
			this.Text17.Interaction = null;
			this.Text17.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text17.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text17.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text18
			// 
			this.Text18 = new Stimulsoft.Report.Components.StiText();
			this.Text18.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 3.9, 9.6, 0.9);
			this.Text18.Guid = "4bd9191f990c4c088455976e81922396";
			this.Text18.Name = "Text18";
			this.Text18.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text18__GetValue);
			this.Text18.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text18.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
			this.Text18.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text18.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text18.Font = new System.Drawing.Font("Times New Roman", 21.75F, System.Drawing.FontStyle.Bold);
			this.Text18.Indicator = null;
			this.Text18.Interaction = null;
			this.Text18.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text18.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text18.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			this.GroupHeaderBand1.Interaction = null;
			// 
			// DataBand1
			// 
			this.DataBand1 = new Stimulsoft.Report.Components.StiDataBand();
			this.DataBand1.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 6.8, 9.6, 1.8);
			this.DataBand1.DataSourceName = "处方明细1";
			this.DataBand1.Guid = "c6a9e6061c3e4378892221439d6e2dcf";
			this.DataBand1.Name = "DataBand1";
			this.DataBand1.Sort = new System.String[] {
				"DESC",
				"Mz_Id"};
			this.DataBand1.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.DataBand1.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.DataBand1.BusinessObjectGuid = null;
			// 
			// Text21
			// 
			this.Text21 = new Stimulsoft.Report.Components.StiText();
			this.Text21.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 0.01, 4.7, 1.2);
			this.Text21.Guid = "335d237023c640f48874584ffcb3882b";
			this.Text21.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
			this.Text21.Name = "Text21";
			this.Text21.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text21__GetValue);
			this.Text21.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text21.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
			this.Text21.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text21.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text21.Font = new System.Drawing.Font("宋体", 10.5F);
			this.Text21.Indicator = null;
			this.Text21.Interaction = null;
			this.Text21.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text21.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text21.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text32
			// 
			this.Text32 = new Stimulsoft.Report.Components.StiText();
			this.Text32.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(4.7, 0.01, 2.9, 1.2);
			this.Text32.Guid = "17baed80d9c440aab61542cd4ee9153f";
			this.Text32.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
			this.Text32.Name = "Text32";
			this.Text32.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text32__GetValue);
			this.Text32.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text32.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
			this.Text32.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text32.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text32.Font = new System.Drawing.Font("宋体", 10.5F);
			this.Text32.Indicator = null;
			this.Text32.Interaction = null;
			this.Text32.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text32.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text32.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text34
			// 
			this.Text34 = new Stimulsoft.Report.Components.StiText();
			this.Text34.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(7.6, 0.01, 1, 1.2);
			this.Text34.Guid = "67eae76af2ca4747948d105808e0a1f9";
			this.Text34.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
			this.Text34.Name = "Text34";
			this.Text34.NullValue = " ";
			this.Text34.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text34__GetValue);
			this.Text34.Type = Stimulsoft.Report.Components.StiSystemTextType.DataColumn;
			this.Text34.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
			this.Text34.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text34.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text34.Font = new System.Drawing.Font("宋体", 10.5F);
			this.Text34.Indicator = null;
			this.Text34.Interaction = null;
			this.Text34.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text34.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text34.TextFormat = new Stimulsoft.Report.Components.TextFormats.StiCustomFormatService("0.####");
			this.Text34.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text10
			// 
			this.Text10 = new Stimulsoft.Report.Components.StiText();
			this.Text10.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(8.6, 0.01, 1, 1.2);
			this.Text10.Guid = "66bfc1af0a874e828c35b08e61114287";
			this.Text10.Name = "Text10";
			this.Text10.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text10__GetValue);
			this.Text10.Type = Stimulsoft.Report.Components.StiSystemTextType.DataColumn;
			this.Text10.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
			this.Text10.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text10.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text10.Font = new System.Drawing.Font("宋体", 10.5F);
			this.Text10.Indicator = null;
			this.Text10.Interaction = null;
			this.Text10.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text10.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text10.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text33
			// 
			this.Text33 = new Stimulsoft.Report.Components.StiText();
			this.Text33.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 1.2, 9.6, 0.6);
			this.Text33.Name = "Text33";
			this.Text33.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text33__GetValue);
			this.Text33.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text33.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text33.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text33.Font = new System.Drawing.Font("宋体", 10.5F);
			this.Text33.Guid = null;
			this.Text33.Indicator = null;
			this.Text33.Interaction = null;
			this.Text33.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text33.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text33.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			this.DataBand1.DataRelationName = null;
			this.DataBand1.Interaction = null;
			this.DataBand1.MasterComponent = null;
			// 
			// GroupFooterBand1
			// 
			this.GroupFooterBand1 = new Stimulsoft.Report.Components.StiGroupFooterBand();
			this.GroupFooterBand1.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 9.4, 9.6, 3);
			this.GroupFooterBand1.Guid = "fe7e19ad84744d48a19c766f037e0da7";
			this.GroupFooterBand1.Name = "GroupFooterBand1";
			this.GroupFooterBand1.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.GroupFooterBand1.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			// 
			// Text20
			// 
			this.Text20 = new Stimulsoft.Report.Components.StiText();
			this.Text20.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(1.4, 1.11, 1.8, 0.6);
			this.Text20.Guid = "37f37e18fc2e4e248008d335c7ce7b47";
			this.Text20.Name = "Text20";
			this.Text20.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text20__GetValue);
			this.Text20.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text20.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
			this.Text20.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.Bottom, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text20.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text20.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 134);
			this.Text20.Indicator = null;
			this.Text20.Interaction = null;
			this.Text20.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text20.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text20.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text23
			// 
			this.Text23 = new Stimulsoft.Report.Components.StiText();
			this.Text23.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(3.2, 1.7, 1.5, 0.6);
			this.Text23.Guid = "149ccb1fea6a4a15b67daf4eaaa7b47b";
			this.Text23.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
			this.Text23.Name = "Text23";
			this.Text23.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text23__GetValue);
			this.Text23.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text23.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
			this.Text23.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text23.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text23.Font = new System.Drawing.Font("宋体", 10.5F);
			this.Text23.Indicator = null;
			this.Text23.Interaction = null;
			this.Text23.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text23.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text23.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text25
			// 
			this.Text25 = new Stimulsoft.Report.Components.StiText();
			this.Text25.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(4.7, 0.51, 1.5, 0.6);
			this.Text25.Guid = "e79139bf1f7b43b5b98231da53f67ae7";
			this.Text25.Name = "Text25";
			this.Text25.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text25__GetValue);
			this.Text25.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text25.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
			this.Text25.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.Bottom, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text25.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text25.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 134);
			this.Text25.Indicator = null;
			this.Text25.Interaction = null;
			this.Text25.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text25.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text25.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text27
			// 
			this.Text27 = new Stimulsoft.Report.Components.StiText();
			this.Text27.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(6.2, 1.11, 1, 0.6);
			this.Text27.Guid = "808943933f7249519419e61a9e7eb63f";
			this.Text27.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
			this.Text27.Name = "Text27";
			this.Text27.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text27__GetValue);
			this.Text27.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text27.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Bottom;
			this.Text27.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text27.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text27.Font = new System.Drawing.Font("宋体", 10.5F);
			this.Text27.Indicator = null;
			this.Text27.Interaction = null;
			this.Text27.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text27.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text27.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text30
			// 
			this.Text30 = new Stimulsoft.Report.Components.StiText();
			this.Text30.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(7.2, 1.11, 1.9, 0.6);
			this.Text30.Guid = "d35c598bc8b040b3a899c23b2a10978d";
			this.Text30.Name = "Text30";
			// 
			// Text30_Sum
			// 
			this.Text30.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text30__GetValue);
			this.Text30.Type = Stimulsoft.Report.Components.StiSystemTextType.Totals;
			this.Text30.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Bottom;
			this.Text30.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.Bottom, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text30.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text30.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 134);
			this.Text30.Indicator = null;
			this.Text30.Interaction = null;
			this.Text30.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text30.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text30.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text31
			// 
			this.Text31 = new Stimulsoft.Report.Components.StiText();
			this.Text31.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(7.2, 0.51, 1.9, 0.6);
			this.Text31.Guid = "0d3ab5c73b7543d18fcde5d9305994e7";
			this.Text31.Name = "Text31";
			this.Text31.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text31.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Bottom;
			this.Text31.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.Bottom, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text31.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text31.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 134);
			this.Text31.Indicator = null;
			this.Text31.Interaction = null;
			this.Text31.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text31.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text31.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text22
			// 
			this.Text22 = new Stimulsoft.Report.Components.StiText();
			this.Text22.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(3.2, 0.51, 1.5, 0.6);
			this.Text22.Guid = "db469fbe118448789e08e5d3a82127fe";
			this.Text22.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
			this.Text22.Name = "Text22";
			this.Text22.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text22__GetValue);
			this.Text22.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text22.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
			this.Text22.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text22.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text22.Font = new System.Drawing.Font("宋体", 10.5F);
			this.Text22.Indicator = null;
			this.Text22.Interaction = null;
			this.Text22.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text22.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text22.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text26
			// 
			this.Text26 = new Stimulsoft.Report.Components.StiText();
			this.Text26.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(6.2, 1.71, 1, 0.6);
			this.Text26.Guid = "f8b7d5f80de247d0b2ff2af240acd473";
			this.Text26.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
			this.Text26.Name = "Text26";
			this.Text26.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text26__GetValue);
			this.Text26.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text26.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Bottom;
			this.Text26.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text26.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text26.Font = new System.Drawing.Font("宋体", 10.5F);
			this.Text26.Indicator = null;
			this.Text26.Interaction = null;
			this.Text26.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text26.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text26.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text29
			// 
			this.Text29 = new Stimulsoft.Report.Components.StiText();
			this.Text29.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(7.2, 1.71, 1.9, 0.6);
			this.Text29.Guid = "d5248e1de5f348889595e826aa476c39";
			this.Text29.Name = "Text29";
			this.Text29.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text29.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Bottom;
			this.Text29.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.Bottom, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text29.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text29.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 134);
			this.Text29.Indicator = null;
			this.Text29.Interaction = null;
			this.Text29.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text29.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text29.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text24
			// 
			this.Text24 = new Stimulsoft.Report.Components.StiText();
			this.Text24.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(4.7, 1.7, 1.5, 0.6);
			this.Text24.Guid = "3ae4157d046e4e2f8cd32c03076c071f";
			this.Text24.Name = "Text24";
			this.Text24.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text24__GetValue);
			this.Text24.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text24.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
			this.Text24.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.Bottom, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text24.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text24.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 134);
			this.Text24.Indicator = null;
			this.Text24.Interaction = null;
			this.Text24.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text24.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text24.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text28
			// 
			this.Text28 = new Stimulsoft.Report.Components.StiText();
			this.Text28.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(6.2, 0.51, 1, 0.6);
			this.Text28.Guid = "71d876062e6241c7aebf603ba01f6c05";
			this.Text28.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
			this.Text28.Name = "Text28";
			this.Text28.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text28__GetValue);
			this.Text28.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text28.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Bottom;
			this.Text28.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text28.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text28.Font = new System.Drawing.Font("宋体", 10.5F);
			this.Text28.Indicator = null;
			this.Text28.Interaction = null;
			this.Text28.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text28.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text28.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text19
			// 
			this.Text19 = new Stimulsoft.Report.Components.StiText();
			this.Text19.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0.5, 1.11, 0.9, 0.6);
			this.Text19.Guid = "590cfd4cefa5495d9f52e53fb003c419";
			this.Text19.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
			this.Text19.Name = "Text19";
			this.Text19.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text19__GetValue);
			this.Text19.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text19.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
			this.Text19.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text19.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text19.Font = new System.Drawing.Font("宋体", 10.5F);
			this.Text19.Indicator = null;
			this.Text19.Interaction = null;
			this.Text19.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text19.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text19.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			this.GroupFooterBand1.Interaction = null;
			this.Page1.ExcelSheetValue = null;
			this.Page1.Interaction = null;
			this.Page1.Margins = new Stimulsoft.Report.Components.StiMargins(0.2, 0.2, 1, 1);
			this.Page1_Watermark = new Stimulsoft.Report.Components.StiWatermark();
			this.Page1_Watermark.Font = new System.Drawing.Font("Arial", 100F);
			this.Page1_Watermark.Image = null;
			this.Page1_Watermark.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.FromArgb(50, 0, 0, 0));
			this.门诊处方表_PrinterSettings = new Stimulsoft.Report.Print.StiPrinterSettings();
			this.PrinterSettings = this.门诊处方表_PrinterSettings;
			this.Page1.Report = this;
			this.Page1.Watermark = this.Page1_Watermark;
			this.ReportTitleBand1.Page = this.Page1;
			this.ReportTitleBand1.Parent = this.Page1;
			this.GroupHeaderBand1.Page = this.Page1;
			this.GroupHeaderBand1.Parent = this.Page1;
			this.Text1.Page = this.Page1;
			this.Text1.Parent = this.GroupHeaderBand1;
			this.Text2.Page = this.Page1;
			this.Text2.Parent = this.GroupHeaderBand1;
			this.Text3.Page = this.Page1;
			this.Text3.Parent = this.GroupHeaderBand1;
			this.Text4.Page = this.Page1;
			this.Text4.Parent = this.GroupHeaderBand1;
			this.Text6.Page = this.Page1;
			this.Text6.Parent = this.GroupHeaderBand1;
			this.Text5.Page = this.Page1;
			this.Text5.Parent = this.GroupHeaderBand1;
			this.Text7.Page = this.Page1;
			this.Text7.Parent = this.GroupHeaderBand1;
			this.Text8.Page = this.Page1;
			this.Text8.Parent = this.GroupHeaderBand1;
			this.Text9.Page = this.Page1;
			this.Text9.Parent = this.GroupHeaderBand1;
			this.Text12.Page = this.Page1;
			this.Text12.Parent = this.GroupHeaderBand1;
			this.Text13.Page = this.Page1;
			this.Text13.Parent = this.GroupHeaderBand1;
			this.Text14.Page = this.Page1;
			this.Text14.Parent = this.GroupHeaderBand1;
			this.Text15.Page = this.Page1;
			this.Text15.Parent = this.GroupHeaderBand1;
			this.Text16.Page = this.Page1;
			this.Text16.Parent = this.GroupHeaderBand1;
			this.Text11.Page = this.Page1;
			this.Text11.Parent = this.GroupHeaderBand1;
			this.Text17.Page = this.Page1;
			this.Text17.Parent = this.GroupHeaderBand1;
			this.Text18.Page = this.Page1;
			this.Text18.Parent = this.GroupHeaderBand1;
			this.DataBand1.Page = this.Page1;
			this.DataBand1.Parent = this.Page1;
			this.Text21.Page = this.Page1;
			this.Text21.Parent = this.DataBand1;
			this.Text32.Page = this.Page1;
			this.Text32.Parent = this.DataBand1;
			this.Text34.Page = this.Page1;
			this.Text34.Parent = this.DataBand1;
			this.Text10.Page = this.Page1;
			this.Text10.Parent = this.DataBand1;
			this.Text33.Page = this.Page1;
			this.Text33.Parent = this.DataBand1;
			this.GroupFooterBand1.Page = this.Page1;
			this.GroupFooterBand1.Parent = this.Page1;
			this.Text20.Page = this.Page1;
			this.Text20.Parent = this.GroupFooterBand1;
			this.Text23.Page = this.Page1;
			this.Text23.Parent = this.GroupFooterBand1;
			this.Text25.Page = this.Page1;
			this.Text25.Parent = this.GroupFooterBand1;
			this.Text27.Page = this.Page1;
			this.Text27.Parent = this.GroupFooterBand1;
			this.Text30.Page = this.Page1;
			this.Text30.Parent = this.GroupFooterBand1;
			this.Text31.Page = this.Page1;
			this.Text31.Parent = this.GroupFooterBand1;
			this.Text22.Page = this.Page1;
			this.Text22.Parent = this.GroupFooterBand1;
			this.Text26.Page = this.Page1;
			this.Text26.Parent = this.GroupFooterBand1;
			this.Text29.Page = this.Page1;
			this.Text29.Parent = this.GroupFooterBand1;
			this.Text24.Page = this.Page1;
			this.Text24.Parent = this.GroupFooterBand1;
			this.Text28.Page = this.Page1;
			this.Text28.Parent = this.GroupFooterBand1;
			this.Text19.Page = this.Page1;
			this.Text19.Parent = this.GroupFooterBand1;
			this.GroupHeaderBand1.BeginRender += new System.EventHandler(this.GroupHeaderBand1__BeginRender);
			this.GroupHeaderBand1.EndRender += new System.EventHandler(this.GroupHeaderBand1__EndRender);
			this.GroupHeaderBand1.Rendering += new System.EventHandler(this.GroupHeaderBand1__Rendering);
			this.AggregateFunctions = new object[] {
				this.Text30_Sum};
			// 
			// Add to GroupHeaderBand1.Components
			// 
			this.GroupHeaderBand1.Components.Clear();
			this.GroupHeaderBand1.Components.AddRange(new Stimulsoft.Report.Components.StiComponent[] {
				this.Text1,
				this.Text2,
				this.Text3,
				this.Text4,
				this.Text6,
				this.Text5,
				this.Text7,
				this.Text8,
				this.Text9,
				this.Text12,
				this.Text13,
				this.Text14,
				this.Text15,
				this.Text16,
				this.Text11,
				this.Text17,
				this.Text18});
			// 
			// Add to DataBand1.Components
			// 
			this.DataBand1.Components.Clear();
			this.DataBand1.Components.AddRange(new Stimulsoft.Report.Components.StiComponent[] {
				this.Text21,
				this.Text32,
				this.Text34,
				this.Text10,
				this.Text33});
			// 
			// Add to GroupFooterBand1.Components
			// 
			this.GroupFooterBand1.Components.Clear();
			this.GroupFooterBand1.Components.AddRange(new Stimulsoft.Report.Components.StiComponent[] {
				this.Text20,
				this.Text23,
				this.Text25,
				this.Text27,
				this.Text30,
				this.Text31,
				this.Text22,
				this.Text26,
				this.Text29,
				this.Text24,
				this.Text28,
				this.Text19});
			// 
			// Add to Page1.Components
			// 
			this.Page1.Components.Clear();
			this.Page1.Components.AddRange(new Stimulsoft.Report.Components.StiComponent[] {
				this.ReportTitleBand1,
				this.GroupHeaderBand1,
				this.DataBand1,
				this.GroupFooterBand1});
			// 
			// Add to Pages
			// 
			this.Pages.Clear();
			this.Pages.AddRange(new Stimulsoft.Report.Components.StiPage[] {
				this.Page1});
			this.处方明细1.Columns.AddRange(new Stimulsoft.Report.Dictionary.StiDataColumn[] {
				new Stimulsoft.Report.Dictionary.StiDataColumn("Mx_Code", "Mx_Code", "Mx_Code", typeof(string)),
				new Stimulsoft.Report.Dictionary.StiDataColumn("Mz_Id", "Mz_Id", "Mz_Id", typeof(int)),
				new Stimulsoft.Report.Dictionary.StiDataColumn("Mx_Gg", "Mx_Gg", "Mx_Gg", typeof(string)),
				new Stimulsoft.Report.Dictionary.StiDataColumn("Mz_Sl", "Mz_Sl", "Mz_Sl", typeof(decimal)),
				new Stimulsoft.Report.Dictionary.StiDataColumn("Mx_XsDw", "Mx_XsDw", "Mx_XsDw", typeof(string)),
				new Stimulsoft.Report.Dictionary.StiDataColumn("Yp_Yfyl", "Yp_Yfyl", "Yp_Yfyl", typeof(string)),
				new Stimulsoft.Report.Dictionary.StiDataColumn("Mz_Money", "Mz_Money", "Mz_Money", typeof(decimal)),
				new Stimulsoft.Report.Dictionary.StiDataColumn("IsJb", "IsJb", "IsJb", typeof(string)),
				new Stimulsoft.Report.Dictionary.StiDataColumn("Yp_Name", "Yp_Name", "Yp_Name", typeof(string))});
			this.DataSources.Add(this.处方明细1);
			}
       
			#region DataSource 处方明细1
			public class 处方明细1DataSource : Stimulsoft.Report.Dictionary.StiDataTableSource
			{
           
			public 处方明细1DataSource() : 
			base("处方明细1", "处方明细1")
			{
			}
           
			public virtual string Mx_Code
			{
			get
			{
			return ((string)(StiReport.ChangeType(this["Mx_Code"], typeof(string), true)));
			}
			}
           
			public virtual int Mz_Id
			{
			get
			{
			return ((int)(StiReport.ChangeType(this["Mz_Id"], typeof(int), true)));
			}
			}
           
			public virtual string Mx_Gg
			{
			get
			{
			return ((string)(StiReport.ChangeType(this["Mx_Gg"], typeof(string), true)));
			}
			}
           
			public virtual decimal Mz_Sl
			{
			get
			{
			return ((decimal)(StiReport.ChangeType(this["Mz_Sl"], typeof(decimal), true)));
			}
			}
           
			public virtual string Mx_XsDw
			{
			get
			{
			return ((string)(StiReport.ChangeType(this["Mx_XsDw"], typeof(string), true)));
			}
			}
           
			public virtual string Yp_Yfyl
			{
			get
			{
			return ((string)(StiReport.ChangeType(this["Yp_Yfyl"], typeof(string), true)));
			}
			}
           
			public virtual decimal Mz_Money
			{
			get
			{
			return ((decimal)(StiReport.ChangeType(this["Mz_Money"], typeof(decimal), true)));
			}
			}
           
			public virtual string IsJb
			{
			get
			{
			return ((string)(StiReport.ChangeType(this["IsJb"], typeof(string), true)));
			}
			}
           
			public virtual string Yp_Name
			{
			get
			{
			return ((string)(StiReport.ChangeType(this["Yp_Name"], typeof(string), true)));
			}
			}
			}
			#endregion DataSource 处方明细1
			#endregion StiReport Designer generated code - do not modify
			}
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>