﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class Cq_Cf31
    Inherits HisControl.BaseChild

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(Cq_Cf31))
        Me.Lr_Control = New C1.Win.C1Command.C1CommandControl()
        Me.Lr1 = New C1.Win.C1Command.C1Command()
        Me.Lr2 = New C1.Win.C1Command.C1Command()
        Me.Lr3 = New C1.Win.C1Command.C1Command()
        Me.ToolTip1 = New System.Windows.Forms.ToolTip(Me.components)
        Me.Comm2 = New System.Windows.Forms.Button()
        Me.T_Line4 = New System.Windows.Forms.Label()
        Me.Panel4 = New System.Windows.Forms.Panel()
        Me.C1ToolBar2 = New C1.Win.C1Command.C1ToolBar()
        Me.C1Holder1 = New C1.Win.C1Command.C1CommandHolder()
        Me.Move2 = New C1.Win.C1Command.C1Command()
        Me.Move3 = New C1.Win.C1Command.C1Command()
        Me.Move5 = New C1.Win.C1Command.C1Command()
        Me.Move1 = New C1.Win.C1Command.C1Command()
        Me.Control1 = New C1.Win.C1Command.C1CommandControl()
        Me.T_Label1 = New C1.Win.C1Input.C1Label()
        Me.Control2 = New C1.Win.C1Command.C1CommandControl()
        Me.T_Label2 = New C1.Win.C1Input.C1Label()
        Me.Move4 = New C1.Win.C1Command.C1Command()
        Me.Control1_Link = New C1.Win.C1Command.C1CommandLink()
        Me.Move_Link1 = New C1.Win.C1Command.C1CommandLink()
        Me.Move_Link2 = New C1.Win.C1Command.C1CommandLink()
        Me.Control2_Link = New C1.Win.C1Command.C1CommandLink()
        Me.Move_Link3 = New C1.Win.C1Command.C1CommandLink()
        Me.Move_Link4 = New C1.Win.C1Command.C1CommandLink()
        Me.Move5_Link = New C1.Win.C1Command.C1CommandLink()
        Me.T_Line6 = New System.Windows.Forms.Label()
        Me.Comm1 = New System.Windows.Forms.Button()
        Me.ErrorProvider1 = New System.Windows.Forms.ErrorProvider(Me.components)
        Me.Label2 = New System.Windows.Forms.Label()
        Me.Label04 = New System.Windows.Forms.Label()
        Me.Label03 = New System.Windows.Forms.Label()
        Me.C1Numeric4 = New C1.Win.C1Input.C1NumericEdit()
        Me.Label11 = New System.Windows.Forms.Label()
        Me.Label10 = New System.Windows.Forms.Label()
        Me.C1Numeric1 = New C1.Win.C1Input.C1NumericEdit()
        Me.C1Numeric2 = New C1.Win.C1Input.C1NumericEdit()
        Me.Label13 = New System.Windows.Forms.Label()
        Me.Label5 = New System.Windows.Forms.Label()
        Me.Label6 = New System.Windows.Forms.Label()
        Me.Label7 = New System.Windows.Forms.Label()
        Me.Label8 = New System.Windows.Forms.Label()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel()
        Me.ComboFrequency1 = New ZTHisControl.ComboFrequency()
        Me.ComboAdministration1 = New ZTHisControl.ComboAdministration()
        Me.NumDosage = New CustomControl.MyNumericEdit()
        Me.ComboYfyl1 = New ZTHisControl.ComboYfyl()
        Me.TxtYfyl = New CustomControl.MyTextBox()
        Me.ComboMzZyYp1 = New ZTHisControl.ComboMzZyYp()
        Me.Panel4.SuspendLayout
        Me.C1ToolBar2.SuspendLayout
        CType(Me.C1Holder1,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.T_Label1,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.T_Label2,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.ErrorProvider1,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.C1Numeric4,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.C1Numeric1,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.C1Numeric2,System.ComponentModel.ISupportInitialize).BeginInit
        Me.TableLayoutPanel1.SuspendLayout
        Me.SuspendLayout
        '
        'Lr_Control
        '
        Me.Lr_Control.HotFrame = C1.Win.C1Command.HotFrameEnum.None
        Me.Lr_Control.Name = "Lr_Control"
        Me.Lr_Control.ShortcutText = ""
        Me.Lr_Control.ShowShortcut = false
        Me.Lr_Control.ShowTextAsToolTip = false
        Me.Lr_Control.Text = "提示"
        '
        'Lr1
        '
        Me.Lr1.Name = "Lr1"
        Me.Lr1.Pressed = true
        Me.Lr1.Shortcut = System.Windows.Forms.Shortcut.F9
        Me.Lr1.ShortcutText = ""
        Me.Lr1.Text = "简称"
        '
        'Lr2
        '
        Me.Lr2.Name = "Lr2"
        Me.Lr2.Shortcut = System.Windows.Forms.Shortcut.F10
        Me.Lr2.ShortcutText = ""
        Me.Lr2.Text = "全称"
        '
        'Lr3
        '
        Me.Lr3.Name = "Lr3"
        Me.Lr3.Shortcut = System.Windows.Forms.Shortcut.F11
        Me.Lr3.ShortcutText = ""
        Me.Lr3.Text = "编码"
        '
        'Comm2
        '
        Me.Comm2.DialogResult = System.Windows.Forms.DialogResult.Cancel
        Me.Comm2.Location = New System.Drawing.Point(259, 4)
        Me.Comm2.Name = "Comm2"
        Me.Comm2.Size = New System.Drawing.Size(50, 24)
        Me.Comm2.TabIndex = 1
        Me.Comm2.Tag = "取消"
        Me.Comm2.Text = "取消"
        Me.ToolTip1.SetToolTip(Me.Comm2, "取消存盘并退出(&C)")
        Me.Comm2.UseVisualStyleBackColor = false
        '
        'T_Line4
        '
        Me.T_Line4.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.T_Line4.Dock = System.Windows.Forms.DockStyle.Top
        Me.T_Line4.Location = New System.Drawing.Point(0, 0)
        Me.T_Line4.Name = "T_Line4"
        Me.T_Line4.Size = New System.Drawing.Size(460, 2)
        Me.T_Line4.TabIndex = 134
        Me.T_Line4.Text = "Label1"
        '
        'Panel4
        '
        Me.Panel4.Controls.Add(Me.Comm2)
        Me.Panel4.Controls.Add(Me.C1ToolBar2)
        Me.Panel4.Controls.Add(Me.T_Line6)
        Me.Panel4.Controls.Add(Me.Comm1)
        Me.Panel4.Controls.Add(Me.T_Line4)
        Me.Panel4.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.Panel4.Location = New System.Drawing.Point(0, 288)
        Me.Panel4.Name = "Panel4"
        Me.Panel4.Size = New System.Drawing.Size(460, 30)
        Me.Panel4.TabIndex = 136
        '
        'C1ToolBar2
        '
        Me.C1ToolBar2.AccessibleName = "Tool Bar"
        Me.C1ToolBar2.CommandHolder = Me.C1Holder1
        Me.C1ToolBar2.CommandLinks.AddRange(New C1.Win.C1Command.C1CommandLink() {Me.Control1_Link, Me.Move_Link1, Me.Move_Link2, Me.Control2_Link, Me.Move_Link3, Me.Move_Link4, Me.Move5_Link})
        Me.C1ToolBar2.Controls.Add(Me.T_Label2)
        Me.C1ToolBar2.Controls.Add(Me.T_Label1)
        Me.C1ToolBar2.Location = New System.Drawing.Point(4, 6)
        Me.C1ToolBar2.MinButtonSize = 22
        Me.C1ToolBar2.Movable = false
        Me.C1ToolBar2.Name = "C1ToolBar2"
        Me.C1ToolBar2.Size = New System.Drawing.Size(174, 22)
        Me.C1ToolBar2.Text = "C1ToolBar2"
        Me.C1ToolBar2.VisualStyle = C1.Win.C1Command.VisualStyle.Classic
        Me.C1ToolBar2.VisualStyleBase = C1.Win.C1Command.VisualStyle.Classic
        '
        'C1Holder1
        '
        Me.C1Holder1.Commands.Add(Me.Move2)
        Me.C1Holder1.Commands.Add(Me.Move3)
        Me.C1Holder1.Commands.Add(Me.Move5)
        Me.C1Holder1.Commands.Add(Me.Move1)
        Me.C1Holder1.Commands.Add(Me.Control1)
        Me.C1Holder1.Commands.Add(Me.Control2)
        Me.C1Holder1.Commands.Add(Me.Lr_Control)
        Me.C1Holder1.Commands.Add(Me.Lr1)
        Me.C1Holder1.Commands.Add(Me.Lr2)
        Me.C1Holder1.Commands.Add(Me.Lr3)
        Me.C1Holder1.Commands.Add(Me.Move4)
        Me.C1Holder1.Owner = Me
        Me.C1Holder1.VisualStyle = C1.Win.C1Command.VisualStyle.Classic
        '
        'Move2
        '
        Me.Move2.Image = CType(resources.GetObject("Move2.Image"),System.Drawing.Image)
        Me.Move2.Name = "Move2"
        Me.Move2.Shortcut = System.Windows.Forms.Shortcut.F3
        Me.Move2.ShortcutText = ""
        Me.Move2.ShowShortcut = false
        Me.Move2.Text = "上移"
        Me.Move2.ToolTipText = "上移记录"
        '
        'Move3
        '
        Me.Move3.Image = CType(resources.GetObject("Move3.Image"),System.Drawing.Image)
        Me.Move3.Name = "Move3"
        Me.Move3.Shortcut = System.Windows.Forms.Shortcut.F4
        Me.Move3.ShortcutText = ""
        Me.Move3.Text = "下移"
        Me.Move3.ToolTipText = "下移记录"
        '
        'Move5
        '
        Me.Move5.Image = CType(resources.GetObject("Move5.Image"),System.Drawing.Image)
        Me.Move5.Name = "Move5"
        Me.Move5.Shortcut = System.Windows.Forms.Shortcut.F6
        Me.Move5.ShortcutText = ""
        Me.Move5.Text = "新增"
        '
        'Move1
        '
        Me.Move1.Image = CType(resources.GetObject("Move1.Image"),System.Drawing.Image)
        Me.Move1.Name = "Move1"
        Me.Move1.Shortcut = System.Windows.Forms.Shortcut.F2
        Me.Move1.ShortcutText = ""
        '
        'Control1
        '
        Me.Control1.Control = Me.T_Label1
        Me.Control1.HotFrame = C1.Win.C1Command.HotFrameEnum.None
        Me.Control1.Name = "Control1"
        Me.Control1.ShortcutText = ""
        Me.Control1.ShowShortcut = false
        Me.Control1.ShowTextAsToolTip = false
        Me.Control1.Text = "记录"
        '
        'T_Label1
        '
        Me.T_Label1.AutoSize = true
        Me.T_Label1.BackColor = System.Drawing.SystemColors.Control
        Me.T_Label1.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.T_Label1.ForeColor = System.Drawing.Color.Maroon
        Me.T_Label1.Location = New System.Drawing.Point(1, 5)
        Me.T_Label1.Name = "T_Label1"
        Me.T_Label1.Size = New System.Drawing.Size(29, 12)
        Me.T_Label1.TabIndex = 137
        Me.T_Label1.Tag = Nothing
        Me.T_Label1.Text = "记录"
        Me.T_Label1.TextAlign = System.Drawing.ContentAlignment.BottomLeft
        Me.T_Label1.TextDetached = true
        '
        'Control2
        '
        Me.Control2.Control = Me.T_Label2
        Me.Control2.HotFrame = C1.Win.C1Command.HotFrameEnum.None
        Me.Control2.Name = "Control2"
        Me.Control2.ShortcutText = ""
        Me.Control2.ShowShortcut = false
        Me.Control2.ShowTextAsToolTip = false
        '
        'T_Label2
        '
        Me.T_Label2.AutoSize = true
        Me.T_Label2.BackColor = System.Drawing.SystemColors.Control
        Me.T_Label2.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.T_Label2.ForeColor = System.Drawing.Color.Maroon
        Me.T_Label2.Location = New System.Drawing.Point(80, 5)
        Me.T_Label2.Name = "T_Label2"
        Me.T_Label2.Size = New System.Drawing.Size(23, 12)
        Me.T_Label2.TabIndex = 137
        Me.T_Label2.Tag = Nothing
        Me.T_Label2.Text = "(1)"
        Me.T_Label2.TextAlign = System.Drawing.ContentAlignment.BottomCenter
        Me.T_Label2.TextDetached = true
        Me.T_Label2.TrimStart = true
        '
        'Move4
        '
        Me.Move4.Image = CType(resources.GetObject("Move4.Image"),System.Drawing.Image)
        Me.Move4.Name = "Move4"
        Me.Move4.Shortcut = System.Windows.Forms.Shortcut.F6
        Me.Move4.ShortcutText = ""
        '
        'Control1_Link
        '
        Me.Control1_Link.Command = Me.Control1
        '
        'Move_Link1
        '
        Me.Move_Link1.Command = Me.Move1
        Me.Move_Link1.Delimiter = true
        Me.Move_Link1.SortOrder = 1
        Me.Move_Link1.Text = "New Command"
        '
        'Move_Link2
        '
        Me.Move_Link2.Command = Me.Move2
        Me.Move_Link2.SortOrder = 2
        '
        'Control2_Link
        '
        Me.Control2_Link.Command = Me.Control2
        Me.Control2_Link.SortOrder = 3
        '
        'Move_Link3
        '
        Me.Move_Link3.Command = Me.Move3
        Me.Move_Link3.SortOrder = 4
        '
        'Move_Link4
        '
        Me.Move_Link4.Command = Me.Move4
        Me.Move_Link4.SortOrder = 5
        Me.Move_Link4.Text = "New Command"
        '
        'Move5_Link
        '
        Me.Move5_Link.Command = Me.Move5
        Me.Move5_Link.Delimiter = true
        Me.Move5_Link.SortOrder = 6
        Me.Move5_Link.Text = "新增"
        Me.Move5_Link.ToolTipText = "新增记录"
        '
        'T_Line6
        '
        Me.T_Line6.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.T_Line6.Location = New System.Drawing.Point(190, 3)
        Me.T_Line6.Name = "T_Line6"
        Me.T_Line6.Size = New System.Drawing.Size(2, 30)
        Me.T_Line6.TabIndex = 0
        '
        'Comm1
        '
        Me.Comm1.Location = New System.Drawing.Point(207, 4)
        Me.Comm1.Name = "Comm1"
        Me.Comm1.Size = New System.Drawing.Size(50, 24)
        Me.Comm1.TabIndex = 0
        Me.Comm1.Tag = "保存"
        Me.Comm1.Text = "确认"
        Me.Comm1.UseVisualStyleBackColor = false
        '
        'ErrorProvider1
        '
        Me.ErrorProvider1.ContainerControl = Me
        '
        'Label2
        '
        Me.Label2.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.Label2.AutoSize = true
        Me.Label2.Location = New System.Drawing.Point(13, 99)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(54, 12)
        Me.Label2.TabIndex = 212
        Me.Label2.Text = "产品规格"
        '
        'Label04
        '
        Me.Label04.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.Label04.AutoSize = true
        Me.Label04.Location = New System.Drawing.Point(243, 73)
        Me.Label04.Name = "Label04"
        Me.Label04.Size = New System.Drawing.Size(54, 12)
        Me.Label04.TabIndex = 205
        Me.Label04.Text = "生产厂家"
        '
        'Label03
        '
        Me.Label03.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.Label03.AutoSize = true
        Me.Label03.Location = New System.Drawing.Point(13, 73)
        Me.Label03.Name = "Label03"
        Me.Label03.Size = New System.Drawing.Size(54, 12)
        Me.Label03.TabIndex = 204
        Me.Label03.Text = "批准文号"
        '
        'C1Numeric4
        '
        Me.C1Numeric4.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.C1Numeric4.AutoSize = false
        Me.C1Numeric4.BackColor = System.Drawing.SystemColors.Info
        Me.C1Numeric4.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1Numeric4.ImagePadding = New System.Windows.Forms.Padding(0)
        Me.C1Numeric4.Location = New System.Drawing.Point(303, 149)
        Me.C1Numeric4.Name = "C1Numeric4"
        Me.C1Numeric4.ReadOnly = true
        Me.C1Numeric4.Size = New System.Drawing.Size(144, 16)
        Me.C1Numeric4.TabIndex = 11
        Me.C1Numeric4.TabStop = false
        Me.C1Numeric4.Tag = Nothing
        Me.C1Numeric4.TextAlign = System.Windows.Forms.HorizontalAlignment.Right
        Me.C1Numeric4.Value = New Decimal(New Integer() {12, 0, 0, 0})
        Me.C1Numeric4.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.C1Numeric4.VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.None
        '
        'Label11
        '
        Me.Label11.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.Label11.AutoSize = true
        Me.Label11.Location = New System.Drawing.Point(13, 151)
        Me.Label11.Name = "Label11"
        Me.Label11.Size = New System.Drawing.Size(54, 12)
        Me.Label11.TabIndex = 196
        Me.Label11.Text = "药品单价"
        '
        'Label10
        '
        Me.Label10.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.Label10.AutoSize = true
        Me.Label10.Location = New System.Drawing.Point(13, 125)
        Me.Label10.Name = "Label10"
        Me.Label10.Size = New System.Drawing.Size(54, 12)
        Me.Label10.TabIndex = 195
        Me.Label10.Text = "使用数量"
        '
        'C1Numeric1
        '
        Me.C1Numeric1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.C1Numeric1.AutoSize = false
        Me.C1Numeric1.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1Numeric1.ImagePadding = New System.Windows.Forms.Padding(0)
        Me.C1Numeric1.Location = New System.Drawing.Point(73, 123)
        Me.C1Numeric1.Name = "C1Numeric1"
        Me.C1Numeric1.Size = New System.Drawing.Size(144, 16)
        Me.C1Numeric1.TabIndex = 2
        Me.C1Numeric1.Tag = "数量"
        Me.C1Numeric1.TextAlign = System.Windows.Forms.HorizontalAlignment.Right
        Me.C1Numeric1.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.C1Numeric1.VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.None
        '
        'C1Numeric2
        '
        Me.C1Numeric2.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.C1Numeric2.AutoSize = false
        Me.C1Numeric2.BackColor = System.Drawing.SystemColors.Info
        Me.C1Numeric2.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1Numeric2.ImagePadding = New System.Windows.Forms.Padding(0)
        Me.C1Numeric2.Location = New System.Drawing.Point(73, 149)
        Me.C1Numeric2.Name = "C1Numeric2"
        Me.C1Numeric2.ReadOnly = true
        Me.C1Numeric2.Size = New System.Drawing.Size(144, 16)
        Me.C1Numeric2.TabIndex = 9
        Me.C1Numeric2.TabStop = false
        Me.C1Numeric2.Tag = "单价"
        Me.C1Numeric2.TextAlign = System.Windows.Forms.HorizontalAlignment.Right
        Me.C1Numeric2.Value = New Decimal(New Integer() {2, 0, 0, 0})
        Me.C1Numeric2.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.C1Numeric2.VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.None
        '
        'Label13
        '
        Me.Label13.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.Label13.AutoSize = true
        Me.Label13.Location = New System.Drawing.Point(243, 151)
        Me.Label13.Name = "Label13"
        Me.Label13.Size = New System.Drawing.Size(54, 12)
        Me.Label13.TabIndex = 197
        Me.Label13.Text = "金    额"
        '
        'Label5
        '
        Me.Label5.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.Label5.BackColor = System.Drawing.SystemColors.Info
        Me.Label5.Location = New System.Drawing.Point(303, 97)
        Me.Label5.Name = "Label5"
        Me.Label5.Size = New System.Drawing.Size(144, 16)
        Me.Label5.TabIndex = 214
        Me.Label5.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'Label6
        '
        Me.Label6.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.Label6.AutoSize = true
        Me.Label6.Location = New System.Drawing.Point(243, 99)
        Me.Label6.Name = "Label6"
        Me.Label6.Size = New System.Drawing.Size(54, 12)
        Me.Label6.TabIndex = 210
        Me.Label6.Text = "剂型名称"
        '
        'Label7
        '
        Me.Label7.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.Label7.AutoSize = true
        Me.Label7.ForeColor = System.Drawing.Color.Maroon
        Me.Label7.Location = New System.Drawing.Point(243, 125)
        Me.Label7.Name = "Label7"
        Me.Label7.Size = New System.Drawing.Size(54, 12)
        Me.Label7.TabIndex = 195
        Me.Label7.Text = "当前库存"
        '
        'Label8
        '
        Me.Label8.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.Label8.BackColor = System.Drawing.SystemColors.Info
        Me.Label8.Location = New System.Drawing.Point(303, 123)
        Me.Label8.Name = "Label8"
        Me.Label8.Size = New System.Drawing.Size(144, 16)
        Me.Label8.TabIndex = 214
        Me.Label8.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'Label1
        '
        Me.Label1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.Label1.BackColor = System.Drawing.SystemColors.Info
        Me.Label1.Location = New System.Drawing.Point(73, 97)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(144, 16)
        Me.Label1.TabIndex = 214
        Me.Label1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'Label3
        '
        Me.Label3.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.Label3.BackColor = System.Drawing.SystemColors.Info
        Me.Label3.Location = New System.Drawing.Point(303, 71)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(144, 16)
        Me.Label3.TabIndex = 214
        Me.Label3.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'Label4
        '
        Me.Label4.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.Label4.BackColor = System.Drawing.SystemColors.Info
        Me.Label4.Location = New System.Drawing.Point(73, 71)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(144, 16)
        Me.Label4.TabIndex = 214
        Me.Label4.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.ColumnCount = 7
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 10!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 60!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 150!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 20!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 60!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 150!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 20!))
        Me.TableLayoutPanel1.Controls.Add(Me.ComboFrequency1, 4, 6)
        Me.TableLayoutPanel1.Controls.Add(Me.C1Numeric4, 5, 5)
        Me.TableLayoutPanel1.Controls.Add(Me.Label8, 5, 4)
        Me.TableLayoutPanel1.Controls.Add(Me.Label13, 4, 5)
        Me.TableLayoutPanel1.Controls.Add(Me.C1Numeric2, 2, 5)
        Me.TableLayoutPanel1.Controls.Add(Me.Label11, 1, 5)
        Me.TableLayoutPanel1.Controls.Add(Me.Label5, 5, 3)
        Me.TableLayoutPanel1.Controls.Add(Me.Label1, 2, 3)
        Me.TableLayoutPanel1.Controls.Add(Me.Label7, 4, 4)
        Me.TableLayoutPanel1.Controls.Add(Me.Label6, 4, 3)
        Me.TableLayoutPanel1.Controls.Add(Me.C1Numeric1, 2, 4)
        Me.TableLayoutPanel1.Controls.Add(Me.Label10, 1, 4)
        Me.TableLayoutPanel1.Controls.Add(Me.Label3, 5, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.Label4, 2, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.Label2, 1, 3)
        Me.TableLayoutPanel1.Controls.Add(Me.Label03, 1, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.Label04, 4, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.ComboAdministration1, 1, 6)
        Me.TableLayoutPanel1.Controls.Add(Me.NumDosage, 1, 7)
        Me.TableLayoutPanel1.Controls.Add(Me.ComboYfyl1, 4, 7)
        Me.TableLayoutPanel1.Controls.Add(Me.TxtYfyl, 1, 8)
        Me.TableLayoutPanel1.Controls.Add(Me.ComboMzZyYp1, 1, 1)
        Me.TableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Top
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(0, 0)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.RowCount = 9
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 40!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 26!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 26!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 26!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 26!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 26!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 26!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 26!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 20!))
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(460, 262)
        Me.TableLayoutPanel1.TabIndex = 215
        '
        'ComboFrequency1
        '
        Me.ComboFrequency1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.ComboFrequency1.Bookmark = -1
        Me.ComboFrequency1.Captain = "频    率"
        Me.ComboFrequency1.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.ComboFrequency1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.ComboFrequency1.CaptainWidth = 70!
        Me.ComboFrequency1.ColumnCaptionHeight = 18
        Me.TableLayoutPanel1.SetColumnSpan(Me.ComboFrequency1, 2)
        Me.ComboFrequency1.DataSource = Nothing
        Me.ComboFrequency1.DataView = Nothing
        Me.ComboFrequency1.ItemHeight = 16
        Me.ComboFrequency1.ItemTextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.ComboFrequency1.Location = New System.Drawing.Point(243, 173)
        Me.ComboFrequency1.MaximumSize = New System.Drawing.Size(10000, 23)
        Me.ComboFrequency1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.ComboFrequency1.Name = "ComboFrequency1"
        Me.ComboFrequency1.ReadOnly = false
        Me.ComboFrequency1.Row = 0
        Me.ComboFrequency1.Size = New System.Drawing.Size(204, 20)
        Me.ComboFrequency1.TabIndex = 216
        Me.ComboFrequency1.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        '
        'ComboAdministration1
        '
        Me.ComboAdministration1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.ComboAdministration1.Bookmark = -1
        Me.ComboAdministration1.Captain = "给药方式"
        Me.ComboAdministration1.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.ComboAdministration1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.ComboAdministration1.CaptainWidth = 70!
        Me.ComboAdministration1.ColumnCaptionHeight = 18
        Me.TableLayoutPanel1.SetColumnSpan(Me.ComboAdministration1, 2)
        Me.ComboAdministration1.DataSource = Nothing
        Me.ComboAdministration1.DataView = Nothing
        Me.ComboAdministration1.ItemHeight = 16
        Me.ComboAdministration1.ItemTextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.ComboAdministration1.Location = New System.Drawing.Point(13, 173)
        Me.ComboAdministration1.MaximumSize = New System.Drawing.Size(10000, 23)
        Me.ComboAdministration1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.ComboAdministration1.Name = "ComboAdministration1"
        Me.ComboAdministration1.ReadOnly = false
        Me.ComboAdministration1.Row = 0
        Me.ComboAdministration1.Size = New System.Drawing.Size(204, 20)
        Me.ComboAdministration1.TabIndex = 217
        Me.ComboAdministration1.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        '
        'NumDosage
        '
        Me.NumDosage.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.NumDosage.Captain = "单次用量"
        Me.NumDosage.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.NumDosage.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.NumDosage.CaptainWidth = 70!
        Me.TableLayoutPanel1.SetColumnSpan(Me.NumDosage, 2)
        Me.NumDosage.Location = New System.Drawing.Point(13, 199)
        Me.NumDosage.MaximumSize = New System.Drawing.Size(100000, 80)
        Me.NumDosage.MinimumSize = New System.Drawing.Size(0, 20)
        Me.NumDosage.Name = "NumDosage"
        Me.NumDosage.NumericInputKeys = CType(((((C1.Win.C1Input.NumericInputKeyFlags.F9 Or C1.Win.C1Input.NumericInputKeyFlags.Minus)  _
            Or C1.Win.C1Input.NumericInputKeyFlags.Plus)  _
            Or C1.Win.C1Input.NumericInputKeyFlags.[Decimal])  _
            Or C1.Win.C1Input.NumericInputKeyFlags.X),C1.Win.C1Input.NumericInputKeyFlags)
        Me.NumDosage.NumFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.NumDosage.ReadOnly = false
        Me.NumDosage.Size = New System.Drawing.Size(204, 20)
        Me.NumDosage.TabIndex = 218
        Me.NumDosage.ValueIsDbNull = false
        '
        'ComboYfyl1
        '
        Me.ComboYfyl1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.ComboYfyl1.Bookmark = -1
        Me.ComboYfyl1.Captain = "单次剂量单位"
        Me.ComboYfyl1.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.ComboYfyl1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.ComboYfyl1.CaptainWidth = 70!
        Me.ComboYfyl1.ColumnCaptionHeight = 18
        Me.TableLayoutPanel1.SetColumnSpan(Me.ComboYfyl1, 2)
        Me.ComboYfyl1.DataSource = Nothing
        Me.ComboYfyl1.DataView = Nothing
        Me.ComboYfyl1.ItemHeight = 16
        Me.ComboYfyl1.ItemTextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.ComboYfyl1.Location = New System.Drawing.Point(243, 199)
        Me.ComboYfyl1.MaximumSize = New System.Drawing.Size(10000, 23)
        Me.ComboYfyl1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.ComboYfyl1.Name = "ComboYfyl1"
        Me.ComboYfyl1.ReadOnly = false
        Me.ComboYfyl1.Row = 0
        Me.ComboYfyl1.Size = New System.Drawing.Size(204, 20)
        Me.ComboYfyl1.TabIndex = 219
        Me.ComboYfyl1.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        '
        'TxtYfyl
        '
        Me.TxtYfyl.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom)  _
            Or System.Windows.Forms.AnchorStyles.Left)  _
            Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.TxtYfyl.Captain = "用法用量"
        Me.TxtYfyl.CaptainBackColor = System.Drawing.Color.Transparent
        Me.TxtYfyl.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.TxtYfyl.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.TxtYfyl.CaptainWidth = 70!
        Me.TableLayoutPanel1.SetColumnSpan(Me.TxtYfyl, 5)
        Me.TxtYfyl.ContentForeColor = System.Drawing.Color.Black
        Me.TxtYfyl.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer))
        Me.TxtYfyl.EditMask = Nothing
        Me.TxtYfyl.Location = New System.Drawing.Point(13, 225)
        Me.TxtYfyl.Multiline = true
        Me.TxtYfyl.Name = "TxtYfyl"
        Me.TxtYfyl.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.TxtYfyl.ReadOnly = false
        Me.TxtYfyl.ScrollBars = System.Windows.Forms.ScrollBars.Vertical
        Me.TxtYfyl.SelectionStart = 0
        Me.TxtYfyl.SelectStart = 0
        Me.TxtYfyl.Size = New System.Drawing.Size(434, 34)
        Me.TxtYfyl.TabIndex = 220
        Me.TxtYfyl.TextAlign = System.Windows.Forms.HorizontalAlignment.Left
        Me.TxtYfyl.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.TxtYfyl.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Top
        Me.TxtYfyl.Watermark = Nothing
        '
        'ComboMzZyYp1
        '
        Me.ComboMzZyYp1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.ComboMzZyYp1.Bookmark = -1
        Me.ComboMzZyYp1.Captain = "药品卫材"
        Me.ComboMzZyYp1.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.ComboMzZyYp1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.ComboMzZyYp1.CaptainWidth = 70!
        Me.ComboMzZyYp1.ColumnCaptionHeight = 18
        Me.TableLayoutPanel1.SetColumnSpan(Me.ComboMzZyYp1, 5)
        Me.ComboMzZyYp1.DataSource = Nothing
        Me.ComboMzZyYp1.DataView = Nothing
        Me.ComboMzZyYp1.ItemHeight = 16
        Me.ComboMzZyYp1.ItemTextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.ComboMzZyYp1.Location = New System.Drawing.Point(13, 43)
        Me.ComboMzZyYp1.MaximumSize = New System.Drawing.Size(10000, 23)
        Me.ComboMzZyYp1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.ComboMzZyYp1.Name = "ComboMzZyYp1"
        Me.ComboMzZyYp1.ReadOnly = false
        Me.ComboMzZyYp1.Row = 0
        Me.ComboMzZyYp1.Size = New System.Drawing.Size(434, 20)
        Me.ComboMzZyYp1.TabIndex = 239
        Me.ComboMzZyYp1.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        '
        'Cq_Cf31
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6!, 12!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.CancelButton = Me.Comm2
        Me.ClientSize = New System.Drawing.Size(460, 318)
        Me.Controls.Add(Me.TableLayoutPanel1)
        Me.Controls.Add(Me.Panel4)
        Me.Icon = CType(resources.GetObject("$this.Icon"),System.Drawing.Icon)
        Me.Name = "Cq_Cf31"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.Manual
        Me.Text = "长期处方明细"
        Me.Panel4.ResumeLayout(false)
        Me.C1ToolBar2.ResumeLayout(false)
        Me.C1ToolBar2.PerformLayout
        CType(Me.C1Holder1,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.T_Label1,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.T_Label2,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.ErrorProvider1,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.C1Numeric4,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.C1Numeric1,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.C1Numeric2,System.ComponentModel.ISupportInitialize).EndInit
        Me.TableLayoutPanel1.ResumeLayout(false)
        Me.TableLayoutPanel1.PerformLayout
        Me.ResumeLayout(false)

End Sub
    Friend WithEvents T_Line4 As System.Windows.Forms.Label
    Friend WithEvents Panel4 As System.Windows.Forms.Panel
    Friend WithEvents T_Line6 As System.Windows.Forms.Label
    Friend WithEvents Comm1 As System.Windows.Forms.Button
    Friend WithEvents C1Holder1 As C1.Win.C1Command.C1CommandHolder
    Friend WithEvents Move2 As C1.Win.C1Command.C1Command
    Friend WithEvents Move3 As C1.Win.C1Command.C1Command
    Friend WithEvents Move5 As C1.Win.C1Command.C1Command
    Friend WithEvents Control2 As C1.Win.C1Command.C1CommandControl
    Friend WithEvents T_Label2 As C1.Win.C1Input.C1Label
    Friend WithEvents C1ToolBar2 As C1.Win.C1Command.C1ToolBar
    Friend WithEvents Move_Link2 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Control2_Link As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Move_Link3 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Move5_Link As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Control1 As C1.Win.C1Command.C1CommandControl
    Friend WithEvents Control1_Link As C1.Win.C1Command.C1CommandLink
    Friend WithEvents T_Label1 As C1.Win.C1Input.C1Label
    Friend WithEvents Comm2 As System.Windows.Forms.Button
    Friend WithEvents Move1 As C1.Win.C1Command.C1Command
    Friend WithEvents Move_Link1 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Move4 As C1.Win.C1Command.C1Command
    Friend WithEvents Move_Link4 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents ErrorProvider1 As System.Windows.Forms.ErrorProvider
    Friend WithEvents Label2 As System.Windows.Forms.Label
    Friend WithEvents Label04 As System.Windows.Forms.Label
    Friend WithEvents Label03 As System.Windows.Forms.Label
    Friend WithEvents C1Numeric4 As C1.Win.C1Input.C1NumericEdit
    Friend WithEvents Label11 As System.Windows.Forms.Label
    Friend WithEvents Label10 As System.Windows.Forms.Label
    Friend WithEvents C1Numeric1 As C1.Win.C1Input.C1NumericEdit
    Friend WithEvents C1Numeric2 As C1.Win.C1Input.C1NumericEdit
    Friend WithEvents Label13 As System.Windows.Forms.Label
    Friend WithEvents Label5 As System.Windows.Forms.Label
    Friend WithEvents Label6 As System.Windows.Forms.Label
    Friend WithEvents Label7 As System.Windows.Forms.Label
    Friend WithEvents Label8 As System.Windows.Forms.Label
    Friend WithEvents Label4 As System.Windows.Forms.Label
    Friend WithEvents Label3 As System.Windows.Forms.Label
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents TableLayoutPanel1 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents ComboAdministration1 As ZTHisControl.ComboAdministration
    Friend WithEvents ComboFrequency1 As ZTHisControl.ComboFrequency
    Private WithEvents ToolTip1 As ToolTip
    Private WithEvents Lr1 As C1.Win.C1Command.C1Command
    Private WithEvents Lr2 As C1.Win.C1Command.C1Command
    Private WithEvents Lr_Control As C1.Win.C1Command.C1CommandControl
    Private WithEvents Lr3 As C1.Win.C1Command.C1Command
    Friend WithEvents NumDosage As CustomControl.MyNumericEdit
    Friend WithEvents ComboYfyl1 As ZTHisControl.ComboYfyl
    Friend WithEvents TxtYfyl As CustomControl.MyTextBox
    Friend WithEvents ComboMzZyYp1 As ZTHisControl.ComboMzZyYp
End Class
