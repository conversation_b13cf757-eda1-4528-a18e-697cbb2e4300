﻿/**  版本信息模板在安装目录下，可自行修改。
* D_Zd_YyJsr.cs
*
* 功 能： N/A
* 类 名： D_Zd_YyJsr
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/7/1 14:19:52   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;

namespace DAL
{
	/// <summary>
	/// 数据访问类:D_Zd_YyJsr
	/// </summary>
	public partial class D_Zd_YyJsr
	{
		public D_Zd_YyJsr()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string Jsr_Code)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from Zd_YyJsr");
			strSql.Append(" where Jsr_Code=@Jsr_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Jsr_Code", SqlDbType.Char,7)			};
			parameters[0].Value = Jsr_Code;

			return HisVar.HisVar.Sqldal.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add( ModelOld.M_Zd_YyJsr model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into Zd_YyJsr(");
			strSql.Append("Yy_Code,Jsr_Code,Login_Code,Jsr_Name,Jsr_Jc,Jsr_Password,Jsr_Memo,Jsr_Py,Glz_Code,Yf_Code,Ys_Code,Xm_Ks)");
			strSql.Append(" values (");
			strSql.Append("@Yy_Code,@Jsr_Code,@Login_Code,@Jsr_Name,@Jsr_Jc,@Jsr_Password,@Jsr_Memo,@Jsr_Py,@Glz_Code,@Yf_Code,@Ys_Code,@Xm_Ks)");
			SqlParameter[] parameters = {
					new SqlParameter("@Yy_Code", SqlDbType.Char,4),
					new SqlParameter("@Jsr_Code", SqlDbType.Char,7),
					new SqlParameter("@Login_Code", SqlDbType.VarChar,50),
					new SqlParameter("@Jsr_Name", SqlDbType.VarChar,10),
					new SqlParameter("@Jsr_Jc", SqlDbType.VarChar,10),
					new SqlParameter("@Jsr_Password", SqlDbType.VarChar,100),
					new SqlParameter("@Jsr_Memo", SqlDbType.VarChar,200),
					new SqlParameter("@Jsr_Py", SqlDbType.VarChar,50),
					new SqlParameter("@Glz_Code", SqlDbType.Char,7),
					new SqlParameter("@Yf_Code", SqlDbType.Char,6),
					new SqlParameter("@Ys_Code", SqlDbType.Char,7),
					new SqlParameter("@Xm_Ks", SqlDbType.VarChar,20)};
			parameters[0].Value = model.Yy_Code;
			parameters[1].Value = model.Jsr_Code;
			parameters[2].Value = model.Login_Code;
			parameters[3].Value = model.Jsr_Name;
			parameters[4].Value = model.Jsr_Jc;
			parameters[5].Value = model.Jsr_Password;
			parameters[6].Value = model.Jsr_Memo;
			parameters[7].Value = model.Jsr_Py;
			parameters[8].Value = model.Glz_Code;
			parameters[9].Value = model.Yf_Code;
			parameters[10].Value = model.Ys_Code;
			parameters[11].Value = model.Xm_Ks;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

      
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update( ModelOld.M_Zd_YyJsr model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update Zd_YyJsr set ");
			strSql.Append("Yy_Code=@Yy_Code,");
			strSql.Append("Login_Code=@Login_Code,");
			strSql.Append("Jsr_Name=@Jsr_Name,");
			strSql.Append("Jsr_Jc=@Jsr_Jc,");
			strSql.Append("Jsr_Password=@Jsr_Password,");
			strSql.Append("Jsr_Memo=@Jsr_Memo,");
			strSql.Append("Jsr_Py=@Jsr_Py,");
			strSql.Append("Glz_Code=@Glz_Code,");
			strSql.Append("Yf_Code=@Yf_Code,");
			strSql.Append("Ys_Code=@Ys_Code,");
			strSql.Append("Xm_Ks=@Xm_Ks");
			strSql.Append(" where Jsr_Code=@Jsr_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Yy_Code", SqlDbType.Char,4),
					new SqlParameter("@Login_Code", SqlDbType.VarChar,50),
					new SqlParameter("@Jsr_Name", SqlDbType.VarChar,10),
					new SqlParameter("@Jsr_Jc", SqlDbType.VarChar,10),
					new SqlParameter("@Jsr_Password", SqlDbType.VarChar,100),
					new SqlParameter("@Jsr_Memo", SqlDbType.VarChar,200),
					new SqlParameter("@Jsr_Py", SqlDbType.VarChar,50),
					new SqlParameter("@Glz_Code", SqlDbType.Char,7),
					new SqlParameter("@Yf_Code", SqlDbType.Char,6),
					new SqlParameter("@Ys_Code", SqlDbType.Char,7),
					new SqlParameter("@Xm_Ks", SqlDbType.VarChar,20),
					new SqlParameter("@Jsr_Code", SqlDbType.Char,7)};
			parameters[0].Value = model.Yy_Code;
			parameters[1].Value = model.Login_Code;
			parameters[2].Value = model.Jsr_Name;
			parameters[3].Value = model.Jsr_Jc;
			parameters[4].Value = model.Jsr_Password;
			parameters[5].Value = model.Jsr_Memo;
			parameters[6].Value = model.Jsr_Py;
			parameters[7].Value = model.Glz_Code;
			parameters[8].Value = model.Yf_Code;
			parameters[9].Value = model.Ys_Code;
			parameters[10].Value = model.Xm_Ks;
			parameters[11].Value = model.Jsr_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string Jsr_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Zd_YyJsr ");
			strSql.Append(" where Jsr_Code=@Jsr_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Jsr_Code", SqlDbType.Char,7)			};
			parameters[0].Value = Jsr_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string Jsr_Codelist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Zd_YyJsr ");
			strSql.Append(" where Jsr_Code in ("+Jsr_Codelist + ")  ");
			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public  ModelOld.M_Zd_YyJsr GetModel(string Jsr_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 Yy_Code,Jsr_Code,Login_Code,Jsr_Name,Jsr_Jc,Jsr_Password,Jsr_Memo,Jsr_Py,Glz_Code,Yf_Code,Ys_Code,Xm_Ks from Zd_YyJsr ");
			strSql.Append(" where Jsr_Code=@Jsr_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Jsr_Code", SqlDbType.Char,7)			};
			parameters[0].Value = Jsr_Code;

			 ModelOld.M_Zd_YyJsr model=new  ModelOld.M_Zd_YyJsr();
			DataSet ds=HisVar.HisVar.Sqldal.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public  ModelOld.M_Zd_YyJsr DataRowToModel(DataRow row)
		{
			 ModelOld.M_Zd_YyJsr model=new  ModelOld.M_Zd_YyJsr();
			if (row != null)
			{
				if(row["Yy_Code"]!=null)
				{
					model.Yy_Code=row["Yy_Code"].ToString();
				}
				if(row["Jsr_Code"]!=null)
				{
					model.Jsr_Code=row["Jsr_Code"].ToString();
				}
				if(row["Login_Code"]!=null)
				{
					model.Login_Code=row["Login_Code"].ToString();
				}
				if(row["Jsr_Name"]!=null)
				{
					model.Jsr_Name=row["Jsr_Name"].ToString();
				}
				if(row["Jsr_Jc"]!=null)
				{
					model.Jsr_Jc=row["Jsr_Jc"].ToString();
				}
				if(row["Jsr_Password"]!=null)
				{
					model.Jsr_Password=row["Jsr_Password"].ToString();
				}
				if(row["Jsr_Memo"]!=null)
				{
					model.Jsr_Memo=row["Jsr_Memo"].ToString();
				}
				if(row["Jsr_Py"]!=null)
				{
					model.Jsr_Py=row["Jsr_Py"].ToString();
				}
				if(row["Glz_Code"]!=null)
				{
					model.Glz_Code=row["Glz_Code"].ToString();
				}
				if(row["Yf_Code"]!=null)
				{
					model.Yf_Code=row["Yf_Code"].ToString();
				}
				if(row["Ys_Code"]!=null)
				{
					model.Ys_Code=row["Ys_Code"].ToString();
				}
				if(row["Xm_Ks"]!=null)
				{
					model.Xm_Ks=row["Xm_Ks"].ToString();
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
            //"Select Zd_YyJsr.Yy_Code,Jsr_Code,Jsr_Name,Jsr_Jc,Login_Code,Jsr_Password,Zd_YyJsr.Glz_Code,Zd_YyJsr.Yf_Code,Zd_Qx1.Yy_Code,Zd_Qx1.Glz_Name,Ys_Code,Xm_Ks,Yf_Name,Ks_Name "
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT Jsr_Code,Jsr_Password,Jsr_Name,Jsr_Jc,Jsr_Py,Login_Code,Zd_YyJsr.Yy_Code,Yy_Name,Yy_Use,Fp_Print,Zd_YyJsr.Yf_Code, ");
            strSql.Append("Yf_Name,Zd_YyJsr.Ys_Code,Ys_Use,Ys_Name,Xm_Ks,Ks_Name,Zd_YyJsr.Glz_Code,Glz_Name,EmrColor  ");
            strSql.Append("FROM Zd_Yy,Zd_Qx1,Zd_YyJsr ");
            strSql.Append("LEFT JOIN Zd_YyYf ON Zd_YyJsr.Yf_Code = Zd_YyYf.Yf_Code ");
            strSql.Append("LEFT JOIN Zd_YyYs ON Zd_YyJsr.Ys_Code = Zd_YyYs.Ys_Code ");
            strSql.Append("LEFT JOIN Zd_YyKs ON Zd_YyJsr.Xm_Ks = Zd_YyKs.Ks_Code ");
            strSql.Append("WHERE  Zd_Qx1.Glz_Code=Zd_YyJsr.Glz_Code and Zd_Yy.Yy_Code = Zd_YyJsr.Yy_Code ");
            strSql.Append("AND Yy_Use = 1  ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" and "+strWhere);
			}
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

        

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" Yy_Code,Jsr_Code,Login_Code,Jsr_Name,Jsr_Jc,Jsr_Password,Jsr_Memo,Jsr_Py,Glz_Code,Yf_Code,Ys_Code,Xm_Ks ");
			strSql.Append(" FROM Zd_YyJsr ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM Zd_YyJsr ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.Jsr_Code desc");
			}
			strSql.Append(")AS Row, T.*  from Zd_YyJsr T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Zd_YyJsr";
			parameters[1].Value = "Jsr_Code";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return HisVar.HisVar.Sqldal.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

