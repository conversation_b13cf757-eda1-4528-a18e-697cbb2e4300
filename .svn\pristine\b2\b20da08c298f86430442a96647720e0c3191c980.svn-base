﻿Imports System.Data.SqlClient
Imports System.Windows.Forms
Imports System.Drawing
Public Class JYYQGLDic11
#Region "初始化"
    Dim My_Dataset As New DataSet
    Dim My_View As New DataView
    Public My_Table As New DataTable
    Public My_Cm As CurrencyManager
    Public My_Row As DataRow
    Public V_Insert As Boolean
    Public V_FirstLoad As Boolean

    Dim BllLisDict As New BLLOld.B_LIS_DictDev
    Dim ModelLisDict As New ModelOld.M_LIS_DictDev

    Dim V_FileName As String = ""
    Dim m_Rc As New BaseClass.C_RowChange


#End Region

    Private Sub JYYQGLDic11_load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Call Form_Init()
        Call Init_Data()
        AddHandler m_Rc.GridMoveEvent, AddressOf GridMove
    End Sub

#Region "窗口初始化"
    Private Sub Form_Init()
        Panel1.BorderStyle = BorderStyle.None
        Panel1.Height = 28
        ToolBar1.Location = New Point(1, 2)
        YqSxCb.Location = New Point(ToolBar1.Right + 2, ToolBar1.Top)
        T_Textbox.Location = New Point(YqSxCb.Right + 2, ToolBar1.Top + 1)
        T_Label.Location = New Point(T_Textbox.Right + 2, ToolBar1.Top + 4)
        T_Label.Width = HisVar.HisVar.DockTab.Width - T_Textbox.Right - 20

        With MyGrid1
            .Init_Column("仪器编号", "Dev_Code", 100, "中", "", False)
            .Init_Column("仪器名称", "Dev_Name", 100, "中", "", False)
            .Init_Column("仪器简称", "Dev_Jc", 90, "中", "", False)
            .Init_Column("仪器类型", "Dev_Lx", 100, "中", "", False)
            .Init_Column("仪器类别", "Dev_Lb", 100, "中", "", False)
            .Init_Column("仪器型号", "Dev_Model", 100, "中", "", False)
            .Init_Column("生产厂家", "Dev_Manufacturer", 120, "中", "", False)
            .Init_Column("使用科室", "Ks_Name", 120, "中", "", False)
            .Init_Column("接口类型", "InterfaceType", 100, "中", "", False)
            .Init_Column("通讯口", "ComPort", 100, "中", "", False)
            .Init_Column("波特率", "BaudRate", 100, "中", "", False)
            .Init_Column("数据位", "DataBits", 100, "中", "", False)
            .Init_Column("检验位", "CheckBits", 100, "中", "", False)
            .Init_Column("停止位", "StopBits", 100, "中", "", False)
            .Init_Column("通讯IP", "IP", 100, "左", "", False)
            .Init_Column("通讯端口", "Port", 80, "中", "", False)
            .Init_Column("文件提取路径", "FilePath", 280, "左", "", False)
            .Init_Column("接口程序名", "InterfacePrograme", 100, "中", "", False)
            .Init_Column("备注", "Memo", 200, "左", "", False)
        End With
        '初始化
        With YqSxCb
            .Additem = "仪器名称"
            .Additem = "仪器简称"
            .DisplayColumns(1).Visible = False
            .DroupDownWidth = .Width - .CaptainWidth
            .SelectedIndex = 0
        End With

    End Sub

    Private Sub Init_Data()
        My_Dataset = BllLisDict.GetAllList()

        My_Table = My_Dataset.Tables(0)

        My_Table.PrimaryKey = New DataColumn() {My_Table.Columns("Dev_Code")}

        With Me.MyGrid1
            My_Cm = CType(BindingContext(My_Table, ""), CurrencyManager)
            .DataTable = My_Table

            T_Label.Text = "∑=" + .Splits(0).Rows.Count.ToString
        End With

        My_View = My_Cm.List
        T_Textbox.Text = ""


    End Sub

    Private Sub JYYQGLDic11_FormClosing(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosingEventArgs) Handles Me.FormClosing
        My_Dataset.Dispose()
    End Sub

#End Region

#Region "控件__动作"

    Private Sub C1TrueDBGrid1_RowColChange_1(ByVal sender As System.Object, ByVal e As C1.Win.C1TrueDBGrid.RowColChangeEventArgs) Handles MyGrid1.RowColChange
        If (MyGrid1.Row + 1) > MyGrid1.RowCount Then
        Else
            My_Row = My_Cm.List(MyGrid1.Row).Row
            m_Rc.ChangeRow(My_Row)
        End If
    End Sub

    Private Sub GridMove(ByVal s As String)
        With MyGrid1
            If .RowCount = 0 Then Exit Sub
            Select Case s
                Case "最前"
                    .MoveFirst()
                Case "上移"
                    .MovePrevious()
                Case "下移"
                    .MoveNext()
                Case "最后"
                    .MoveLast()
                    T_Label.Text = "∑=" + .Splits(0).Rows.Count.ToString
            End Select
        End With
    End Sub

    Private Sub Comm_Click(ByVal sender As Object, ByVal e As C1.Win.C1Command.ClickEventArgs) Handles Comm1.Click, Comm2.Click, Comm3.Click
        Select Case sender.text
            Case "增加"
                Call P_ShowData("增加")

            Case "删除"
                Beep()
                If MyGrid1.Splits(0).Rows.Count = 0 Then Exit Sub
                Call P_Del_Data()

            Case "更新"
                Call Init_Data()
                MyGrid1.Select()
                MyGrid1.MoveFirst()
        End Select

    End Sub

    Private Sub T_Combo_Close(ByVal sender As Object, ByVal e As System.EventArgs)
        T_Textbox.Select()
    End Sub

    Private Sub C1TextBox1_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles T_Textbox.TextChanged
        Dim V_Str As String = ""
        If Trim(T_Textbox.Text & "") = "" Then
            V_Str = ""
        Else
            Select Case YqSxCb.Text

                Case "仪器名称"
                    V_Str = "Dev_Name Like '*" & Trim(T_Textbox.Text) & "*'"

                Case "仪器简称"
                    V_Str = "Dev_Jc Like '*" & Trim(T_Textbox.Text) & "*'"

            End Select
        End If

        With My_View
            .Sort = "Dev_Code Asc"
            .RowFilter = V_Str
        End With
        T_Label.Text = "∑=" + MyGrid1.Splits(0).Rows.Count.ToString
    End Sub

    Private Sub C1TrueDBGrid1_MouseUp(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles MyGrid1.MouseUp
        If e.Button = Windows.Forms.MouseButtons.Right Then Call P_ShowData("DBGrid")
    End Sub

    Private Sub C1TrueDBGrid1_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles MyGrid1.KeyDown
        Select Case e.KeyCode
            Case Keys.Return
                Call P_ShowData("DBGrid")
            Case Keys.Delete
                If Me.MyGrid1.RowCount > 0 Then Call P_Del_Data()
        End Select
    End Sub


#End Region

#Region "自定义函数"



    Private Sub P_ShowData(ByVal V_Lb As String)
        If V_Lb = "增加" Then
            V_Insert = True
        Else
            If Me.MyGrid1.RowCount > 0 Then V_Insert = False Else V_Insert = True
        End If

        If V_Insert = False Then
            My_Row = My_Cm.List(MyGrid1.Row).Row
        End If

        Dim vform As New JYYQGLDic12(V_Insert, My_Row, My_Table, m_Rc)
        If BaseFunc.BaseFunc.CheckOwnForm(Me, vform) = False Then
            vform.Owner = Me
            vform.Show()
        End If
        MyGrid1.Select()
    End Sub

    Private Sub P_Del_Data()
        Beep()
        If MsgBox("是否删除:仪器名称=" + Me.MyGrid1.Columns("Dev_Name").Value + " ", MsgBoxStyle.Information + MsgBoxStyle.OkCancel + MsgBoxStyle.DefaultButton1, "提示:") = MsgBoxResult.Cancel Then Exit Sub
        My_Row = My_Cm.List(MyGrid1.Row).Row
        Try
            BllLisDict.Delete(My_Row("Dev_Code"))
            MyGrid1.Delete()
            My_Row.AcceptChanges()
            T_Label.Text = "∑=" + MyGrid1.Splits(0).Rows.Count.ToString
        Catch ex As Exception
            Beep()
            MsgBox("错误:" + ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
        Finally
            MyGrid1.Select()
        End Try
    End Sub



#End Region

End Class