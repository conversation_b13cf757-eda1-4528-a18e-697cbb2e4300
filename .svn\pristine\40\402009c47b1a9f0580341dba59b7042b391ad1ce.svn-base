﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="0" />
    <DataSources isList="true" count="1">
      <医技科室收入统计 Ref="2" type="DataTableSource" isKey="true">
        <Alias>医技科室收入统计</Alias>
        <Columns isList="true" count="8">
          <value>Xm_Name,System.String</value>
          <value>Xm_Dw,System.String</value>
          <value>Xm_Dj,System.Decimal</value>
          <value>Xm_Sl,System.Decimal</value>
          <value>Xm_Money,System.Decimal</value>
          <value>Jc_KsName,System.String</value>
          <value>Cf_KsName,System.String</value>
          <value>Cf_YsName,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>医技科室收入统计</Name>
        <NameInSource>医技科室收入统计</NameInSource>
      </医技科室收入统计>
    </DataSources>
    <Relations isList="true" count="0" />
    <Report isRef="0" />
    <Variables isList="true" count="3">
      <value>,标题,标题,System.String,,False,False</value>
      <value>,查询时间,查询时间,System.String,,False,False</value>
      <value>,打印时间,打印时间,System.String,,False,False</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="2">
    <Page1 Ref="3" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="9">
        <PageFooterBand1 Ref="4" type="PageFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,26.9,19,0.8</ClientRectangle>
          <Components isList="true" count="0" />
          <Conditions isList="true" count="0" />
          <Name>PageFooterBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </PageFooterBand1>
        <ReportTitleBand1 Ref="5" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,19,1.3</ClientRectangle>
          <Components isList="true" count="3">
            <Text1 Ref="6" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.1,19,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,12,Bold,Point,False,134</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text1</Name>
              <Page isRef="3" />
              <Parent isRef="5" />
              <Text>{标题}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text1>
            <Text14 Ref="7" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.7,12.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>b82de35adabb43bda96fdad25cb98a82</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text14</Name>
              <Page isRef="3" />
              <Parent isRef="5" />
              <Text>结账时间:{查询时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text14>
            <Text15 Ref="8" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>12.2,0.7,6.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>9fc2f128844547c1acf2d71f6bfe6003</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text15</Name>
              <Page isRef="3" />
              <Parent isRef="5" />
              <Text>打印时间:{打印时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text15>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>ReportTitleBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </ReportTitleBand1>
        <HeaderBand1 Ref="9" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,2.5,19,0.6</ClientRectangle>
          <Components isList="true" count="5">
            <Text4 Ref="10" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,9.3,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11.25,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>9bc26ce6878940f2b2e5ae23949056c5</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text4</Name>
              <Page isRef="3" />
              <Parent isRef="9" />
              <Text>项目名称</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text4>
            <Text8 Ref="11" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>12.1,0,2.3,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11.25,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>bafc41814bd04253b4509a4751068b61</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text8</Name>
              <Page isRef="3" />
              <Parent isRef="9" />
              <Text>单价</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text8>
            <Text9 Ref="12" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>14.4,0,2.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11.25,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>5e4490a4b1954d46913915759e528209</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text9</Name>
              <Page isRef="3" />
              <Parent isRef="9" />
              <Text>数量</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text9>
            <Text10 Ref="13" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>16.6,0,2.4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11.25,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>381e492cb29047008b7a954f96fb2987</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text10</Name>
              <Page isRef="3" />
              <Parent isRef="9" />
              <Text>项目金额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text10>
            <Text11 Ref="14" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9.3,0,2.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11.25,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>60807a2c8a8b4bc8a257449b51554667</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text11</Name>
              <Page isRef="3" />
              <Parent isRef="9" />
              <Text>项目单位</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text11>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>HeaderBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </HeaderBand1>
        <GroupHeaderBand1 Ref="15" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,3.9,19,0.6</ClientRectangle>
          <Components isList="true" count="1">
            <Text2 Ref="16" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,19,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11.25,Bold,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text2</Name>
              <Page isRef="3" />
              <Parent isRef="15" />
              <Text>医技科室：{医技科室收入统计.Jc_KsName}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text2>
          </Components>
          <Condition>{医技科室收入统计.Jc_KsName}</Condition>
          <Conditions isList="true" count="0" />
          <Name>GroupHeaderBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </GroupHeaderBand1>
        <GroupHeaderBand2 Ref="17" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,5.3,19,0</ClientRectangle>
          <Components isList="true" count="0" />
          <Condition>{医技科室收入统计.Xm_Name}{医技科室收入统计.Cf_YsName}{医技科室收入统计.Xm_Dw}{医技科室收入统计.Xm_Dj}</Condition>
          <Conditions isList="true" count="0" />
          <Name>GroupHeaderBand2</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </GroupHeaderBand2>
        <DataBand1 Ref="18" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,6.1,19,0</ClientRectangle>
          <Columns>1</Columns>
          <Components isList="true" count="0" />
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>医技科室收入统计</DataSourceName>
          <Filters isList="true" count="0" />
          <Name>DataBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Sort isList="true" count="0" />
        </DataBand1>
        <GroupFooterBand2 Ref="19" type="GroupFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,6.9,19,0.6</ClientRectangle>
          <Components isList="true" count="5">
            <Text3 Ref="20" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,9.3,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11.25,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>ca3945fa0da543ed88eea309e5fba77a</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text3</Name>
              <Page isRef="3" />
              <Parent isRef="19" />
              <Text>{医技科室收入统计.Xm_Name}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text3>
            <Text5 Ref="21" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>12.1,0,2.3,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11.25,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>29c900d83ff44364a788e87ab0f63172</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text5</Name>
              <Page isRef="3" />
              <Parent isRef="19" />
              <Text>{医技科室收入统计.Xm_Dj}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="22" type="CustomFormat" isKey="true">
                <StringFormat>0.00##</StringFormat>
              </TextFormat>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text5>
            <Text6 Ref="23" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>14.4,0,2.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11.25,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>cde9a7448a7c451ea7361527fac9c549</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text6</Name>
              <Page isRef="3" />
              <Parent isRef="19" />
              <Text>{Sum(GroupHeaderBand2,医技科室收入统计.Xm_Sl)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="24" type="CustomFormat" isKey="true">
                <StringFormat>0.##</StringFormat>
              </TextFormat>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text6>
            <Text7 Ref="25" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>16.6,0,2.4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11.25,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>2d1fdb5e0aa9419bbd0ae3db291624cc</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text7</Name>
              <Page isRef="3" />
              <Parent isRef="19" />
              <Text>{Sum(GroupHeaderBand2,医技科室收入统计.Xm_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="26" type="CustomFormat" isKey="true">
                <StringFormat>0.00##</StringFormat>
              </TextFormat>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text7>
            <Text12 Ref="27" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9.3,0,2.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11.25,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>48f9431dead948a8b42190e4872ef963</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text12</Name>
              <Page isRef="3" />
              <Parent isRef="19" />
              <Text>{医技科室收入统计.Xm_Dw}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="28" type="CustomFormat" isKey="true">
                <StringFormat>0.00##</StringFormat>
              </TextFormat>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text12>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>GroupFooterBand2</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </GroupFooterBand2>
        <GroupFooterBand1 Ref="29" type="GroupFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,8.3,19,0.6</ClientRectangle>
          <Components isList="true" count="2">
            <Text13 Ref="30" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>16.6,0,2.4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11.25,Bold,Point,False,134</Font>
              <Guid>0465dabf4ab54c188f6f36d9e178d7f5</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text13</Name>
              <Page isRef="3" />
              <Parent isRef="29" />
              <Text>{Sum(GroupHeaderBand1,医技科室收入统计.Xm_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="31" type="CustomFormat" isKey="true">
                <StringFormat>0.00##</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text13>
            <Text16 Ref="32" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,16.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11.25,Bold,Point,False,134</Font>
              <Guid>6ac4ea184b7949e9b20caed8ad3cc93c</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text16</Name>
              <Page isRef="3" />
              <Parent isRef="29" />
              <Text>小计</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text16>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>GroupFooterBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </GroupFooterBand1>
        <ReportSummaryBand1 Ref="33" type="ReportSummaryBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,9.7,19,0.6</ClientRectangle>
          <Components isList="true" count="2">
            <Text17 Ref="34" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>16.6,0,2.4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11.25,Bold,Point,False,134</Font>
              <Guid>9b673f9538514ac28fdb68d3e78d6005</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text17</Name>
              <Page isRef="3" />
              <Parent isRef="33" />
              <Text>{Sum(DataBand1,医技科室收入统计.Xm_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="35" type="CustomFormat" isKey="true">
                <StringFormat>0.00##</StringFormat>
              </TextFormat>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text17>
            <Text18 Ref="36" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,16.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11.25,Bold,Point,False,134</Font>
              <Guid>6a6bca477fa24f0eb11b9a4c10a14dc4</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text18</Name>
              <Page isRef="3" />
              <Parent isRef="33" />
              <Text>合计</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text18>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>ReportSummaryBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </ReportSummaryBand1>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>0c0860d17d8b4f3ca60f5e98910f2468</Guid>
      <Margins>1,1,1,1</Margins>
      <Name>Page1</Name>
      <PageHeight>29.7</PageHeight>
      <PageWidth>21</PageWidth>
      <Report isRef="0" />
      <Watermark Ref="37" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page1>
    <Page2 Ref="38" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="9">
        <PageFooterBand2 Ref="39" type="PageFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,26.9,19,0.8</ClientRectangle>
          <Components isList="true" count="0" />
          <Conditions isList="true" count="0" />
          <Name>PageFooterBand2</Name>
          <Page isRef="38" />
          <Parent isRef="38" />
        </PageFooterBand2>
        <ReportTitleBand2 Ref="40" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,19,1.3</ClientRectangle>
          <Components isList="true" count="3">
            <Text19 Ref="41" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.1,19,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,12,Bold,Point,False,134</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text19</Name>
              <Page isRef="38" />
              <Parent isRef="40" />
              <Text>{标题}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text19>
            <Text20 Ref="42" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.7,12.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>b82de35adabb43bda96fdad25cb98a82</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text20</Name>
              <Page isRef="38" />
              <Parent isRef="40" />
              <Text>结账时间:{查询时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text20>
            <Text21 Ref="43" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>12.2,0.7,6.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>9fc2f128844547c1acf2d71f6bfe6003</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text21</Name>
              <Page isRef="38" />
              <Parent isRef="40" />
              <Text>打印时间:{打印时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text21>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>ReportTitleBand2</Name>
          <Page isRef="38" />
          <Parent isRef="38" />
        </ReportTitleBand2>
        <HeaderBand2 Ref="44" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,2.5,19,0.6</ClientRectangle>
          <Components isList="true" count="5">
            <Text22 Ref="45" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,9.3,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11.25,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>9bc26ce6878940f2b2e5ae23949056c5</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text22</Name>
              <Page isRef="38" />
              <Parent isRef="44" />
              <Text>项目名称</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text22>
            <Text23 Ref="46" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>12.1,0,2.3,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11.25,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>bafc41814bd04253b4509a4751068b61</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text23</Name>
              <Page isRef="38" />
              <Parent isRef="44" />
              <Text>单价</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text23>
            <Text24 Ref="47" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>14.4,0,2.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11.25,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>5e4490a4b1954d46913915759e528209</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text24</Name>
              <Page isRef="38" />
              <Parent isRef="44" />
              <Text>数量</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text24>
            <Text25 Ref="48" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>16.6,0,2.4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11.25,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>381e492cb29047008b7a954f96fb2987</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text25</Name>
              <Page isRef="38" />
              <Parent isRef="44" />
              <Text>项目金额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text25>
            <Text26 Ref="49" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9.3,0,2.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11.25,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>60807a2c8a8b4bc8a257449b51554667</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text26</Name>
              <Page isRef="38" />
              <Parent isRef="44" />
              <Text>项目单位</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text26>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>HeaderBand2</Name>
          <Page isRef="38" />
          <Parent isRef="38" />
        </HeaderBand2>
        <GroupHeaderBand3 Ref="50" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,3.9,19,0.6</ClientRectangle>
          <Components isList="true" count="1">
            <Text27 Ref="51" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,19,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11.25,Bold,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text27</Name>
              <Page isRef="38" />
              <Parent isRef="50" />
              <Text>处方医生：{医技科室收入统计.Cf_YsName}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text27>
          </Components>
          <Condition>{医技科室收入统计.Cf_YsName}</Condition>
          <Conditions isList="true" count="0" />
          <Name>GroupHeaderBand3</Name>
          <Page isRef="38" />
          <Parent isRef="38" />
        </GroupHeaderBand3>
        <GroupHeaderBand4 Ref="52" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,5.3,19,0</ClientRectangle>
          <Components isList="true" count="0" />
          <Condition>{医技科室收入统计.Xm_Name}{医技科室收入统计.Cf_YsName}{医技科室收入统计.Xm_Dw}{医技科室收入统计.Xm_Dj}</Condition>
          <Conditions isList="true" count="0" />
          <Name>GroupHeaderBand4</Name>
          <Page isRef="38" />
          <Parent isRef="38" />
        </GroupHeaderBand4>
        <DataBand2 Ref="53" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,6.1,19,0</ClientRectangle>
          <Columns>1</Columns>
          <Components isList="true" count="0" />
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>医技科室收入统计</DataSourceName>
          <Filters isList="true" count="0" />
          <Name>DataBand2</Name>
          <Page isRef="38" />
          <Parent isRef="38" />
          <Sort isList="true" count="0" />
        </DataBand2>
        <GroupFooterBand3 Ref="54" type="GroupFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,6.9,19,0.6</ClientRectangle>
          <Components isList="true" count="5">
            <Text28 Ref="55" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,9.3,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11.25,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>ca3945fa0da543ed88eea309e5fba77a</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text28</Name>
              <Page isRef="38" />
              <Parent isRef="54" />
              <Text>{医技科室收入统计.Xm_Name}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text28>
            <Text29 Ref="56" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>12.1,0,2.3,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11.25,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>29c900d83ff44364a788e87ab0f63172</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text29</Name>
              <Page isRef="38" />
              <Parent isRef="54" />
              <Text>{医技科室收入统计.Xm_Dj}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="57" type="CustomFormat" isKey="true">
                <StringFormat>0.00##</StringFormat>
              </TextFormat>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text29>
            <Text30 Ref="58" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>14.4,0,2.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11.25,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>cde9a7448a7c451ea7361527fac9c549</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text30</Name>
              <Page isRef="38" />
              <Parent isRef="54" />
              <Text>{Sum(GroupHeaderBand4,医技科室收入统计.Xm_Sl)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="59" type="CustomFormat" isKey="true">
                <StringFormat>0.##</StringFormat>
              </TextFormat>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text30>
            <Text31 Ref="60" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>16.6,0,2.4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11.25,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>2d1fdb5e0aa9419bbd0ae3db291624cc</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text31</Name>
              <Page isRef="38" />
              <Parent isRef="54" />
              <Text>{Sum(GroupHeaderBand4,医技科室收入统计.Xm_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="61" type="CustomFormat" isKey="true">
                <StringFormat>0.00##</StringFormat>
              </TextFormat>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text31>
            <Text32 Ref="62" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9.3,0,2.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11.25,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>48f9431dead948a8b42190e4872ef963</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text32</Name>
              <Page isRef="38" />
              <Parent isRef="54" />
              <Text>{医技科室收入统计.Xm_Dw}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="63" type="CustomFormat" isKey="true">
                <StringFormat>0.00##</StringFormat>
              </TextFormat>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text32>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>GroupFooterBand3</Name>
          <Page isRef="38" />
          <Parent isRef="38" />
        </GroupFooterBand3>
        <GroupFooterBand4 Ref="64" type="GroupFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,8.3,19,0.6</ClientRectangle>
          <Components isList="true" count="2">
            <Text33 Ref="65" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>16.6,0,2.4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11.25,Bold,Point,False,134</Font>
              <Guid>0465dabf4ab54c188f6f36d9e178d7f5</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text33</Name>
              <Page isRef="38" />
              <Parent isRef="64" />
              <Text>{Sum(GroupHeaderBand4,医技科室收入统计.Xm_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="66" type="CustomFormat" isKey="true">
                <StringFormat>0.00##</StringFormat>
              </TextFormat>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text33>
            <Text34 Ref="67" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,16.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11.25,Bold,Point,False,134</Font>
              <Guid>6ac4ea184b7949e9b20caed8ad3cc93c</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text34</Name>
              <Page isRef="38" />
              <Parent isRef="64" />
              <Text>小计</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text34>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>GroupFooterBand4</Name>
          <Page isRef="38" />
          <Parent isRef="38" />
        </GroupFooterBand4>
        <ReportSummaryBand2 Ref="68" type="ReportSummaryBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,9.7,19,0.6</ClientRectangle>
          <Components isList="true" count="2">
            <Text35 Ref="69" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>16.6,0,2.4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11.25,Bold,Point,False,134</Font>
              <Guid>9b673f9538514ac28fdb68d3e78d6005</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text35</Name>
              <Page isRef="38" />
              <Parent isRef="68" />
              <Text>{Sum(DataBand2,医技科室收入统计.Xm_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="70" type="CustomFormat" isKey="true">
                <StringFormat>0.00##</StringFormat>
              </TextFormat>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text35>
            <Text36 Ref="71" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,16.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11.25,Bold,Point,False,134</Font>
              <Guid>6a6bca477fa24f0eb11b9a4c10a14dc4</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text36</Name>
              <Page isRef="38" />
              <Parent isRef="68" />
              <Text>合计</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text36>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>ReportSummaryBand2</Name>
          <Page isRef="38" />
          <Parent isRef="38" />
        </ReportSummaryBand2>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>f3d92b04a6764a86bf644d79e81e782d</Guid>
      <Margins>1,1,1,1</Margins>
      <Name>Page2</Name>
      <PageHeight>29.7</PageHeight>
      <PageWidth>21</PageWidth>
      <Report isRef="0" />
      <Watermark Ref="72" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page2>
  </Pages>
  <PrinterSettings Ref="73" type="Stimulsoft.Report.Print.StiPrinterSettings" isKey="true" />
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>医技科室收入统计</ReportAlias>
  <ReportChanged>5/29/2014 9:30:00 AM</ReportChanged>
  <ReportCreated>4/30/2014 2:54:22 PM</ReportCreated>
  <ReportFile>E:\正在进行时\唐山\正在修改版\his2010\his2010\Rpt\医技科室收入统计.mrt</ReportFile>
  <ReportGuid>46b4c88b92b24ade86ca07e6a5bb3c30</ReportGuid>
  <ReportName>医技科室收入统计</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2011.2.1026</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class Report : Stimulsoft.Report.StiReport
    {
        public Report()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
        #endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>