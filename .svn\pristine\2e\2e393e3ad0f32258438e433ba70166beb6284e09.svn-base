﻿/**  版本信息模板在安装目录下，可自行修改。
* M_DRYB_MzSf_Ht.cs
*
* 功 能： N/A
* 类 名： M_DRYB_MzSf_Ht
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2017/3/8 11:36:04   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace ModelOld
{
	/// <summary>
	/// M_DRYB_MzSf_Ht:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class M_DRYB_MzSf_Ht
	{
		public M_DRYB_MzSf_Ht()
		{}
		#region Model
		private int _id;
		private int? _mzsf_id;
		private decimal? _bcfhjbylbxfyje;
		private decimal? _bcfhjbylbxwzfje;
		private decimal? _bcgrzhzfje;
		private decimal? _bcgwybzzfje;
		private decimal? _bcjrdbbf;
		private decimal? _bctczfje;
		private decimal? _bcxjzfje;
		private decimal? _jshicye;
		private decimal? _ylfyze;
		private decimal? _bnzycs;
		private decimal? _bcqfxbz;
		private string _mzzy_code;
		private string _ry_name;
		private string _lb;
		private string _ybjz_code;
		private string _jsr_code;
		private bool _js_zt;
        private bool _his_Rj_zt;
		/// <summary>
		/// 
		/// </summary>
		public int Id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 
		/// </summary>
		public int? MzSf_Id
		{
			set{ _mzsf_id=value;}
			get{return _mzsf_id;}
		}
		/// <summary>
		/// 本次符合基本医疗保险费用金额（门诊）
		/// </summary>
		public decimal? BcFhJbYlbxFyJe
		{
			set{ _bcfhjbylbxfyje=value;}
			get{return _bcfhjbylbxfyje;}
		}
		/// <summary>
		/// 本次符合基本医疗保险外自费金额（门诊）
		/// </summary>
		public decimal? BcFhJbYlbxWZfJe
		{
			set{ _bcfhjbylbxwzfje=value;}
			get{return _bcfhjbylbxwzfje;}
		}
		/// <summary>
		/// 本次个人帐户支付金额（门诊）
		/// </summary>
		public decimal? BcGrzhZfJe
		{
			set{ _bcgrzhzfje=value;}
			get{return _bcgrzhzfje;}
		}
		/// <summary>
		/// 本次公务员补助支出金额（门诊）
		/// </summary>
		public decimal? BcGwyBzZfJe
		{
			set{ _bcgwybzzfje=value;}
			get{return _bcgwybzzfje;}
		}
		/// <summary>
		/// 本次进入大病部分（门诊、住院）
		/// </summary>
		public decimal? BcJrDbBf
		{
			set{ _bcjrdbbf=value;}
			get{return _bcjrdbbf;}
		}
		/// <summary>
		/// 本次统筹支付金额（门诊、住院）
		/// </summary>
		public decimal? BcTcZfJe
		{
			set{ _bctczfje=value;}
			get{return _bctczfje;}
		}
		/// <summary>
		/// 本次现金支付金额（门诊、住院）
		/// </summary>
		public decimal? BcXjZfJe
		{
			set{ _bcxjzfje=value;}
			get{return _bcxjzfje;}
		}
		/// <summary>
		/// 结算后IC卡余额（门诊、住院）
		/// </summary>
		public decimal? JshICYe
		{
			set{ _jshicye=value;}
			get{return _jshicye;}
		}
		/// <summary>
		/// 医疗费用总额（门诊）
		/// </summary>
		public decimal? YlFyZe
		{
			set{ _ylfyze=value;}
			get{return _ylfyze;}
		}
		/// <summary>
		/// 本年住院次数（住院）
		/// </summary>
		public decimal? BnZyCs
		{
			set{ _bnzycs=value;}
			get{return _bnzycs;}
		}
		/// <summary>
		/// 本次起付线标准（住院）
		/// </summary>
		public decimal? BcQfxBz
		{
			set{ _bcqfxbz=value;}
			get{return _bcqfxbz;}
		}
		/// <summary>
		/// 门诊住院编码
		/// </summary>
		public string MzZy_Code
		{
			set{ _mzzy_code=value;}
			get{return _mzzy_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Ry_Name
		{
			set{ _ry_name=value;}
			get{return _ry_name;}
		}
		/// <summary>
		/// 类别：门诊、住院
		/// </summary>
		public string Lb
		{
			set{ _lb=value;}
			get{return _lb;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string YbJz_Code
		{
			set{ _ybjz_code=value;}
			get{return _ybjz_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Jsr_Code
		{
			set{ _jsr_code=value;}
			get{return _jsr_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public bool Js_Zt
		{
			set{ _js_zt=value;}
			get{return _js_zt;}
		}
        /// <summary>
        /// His 日结状态
        /// </summary>
        public bool His_Rj_Zt
        {
            set { _his_Rj_zt = value; }
            get { return _his_Rj_zt; }
        }
		#endregion Model

	}
}

