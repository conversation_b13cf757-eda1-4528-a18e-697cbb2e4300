﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Windows.Forms;
using Common;
using ERX.Model;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Org.BouncyCastle.Utilities.Encoders;
using ZTHisInsurance_Enum;
using ZTHisInsuranceAPI;

namespace ERX
{
    public class ERXApi
    {
        public ERXApi()
        {
            ERXConfig.Erx_Init();
        }
        /// <summary>
        /// 电子处方上传预核验
        /// </summary>
        /// <param name="mdlInput"></param>
        /// <param name="msg"></param>
        /// <param name="mdlOutput"></param>
        /// <returns></returns>
        public int uploadChk(MdluploadChkIn mdlInput, ref string msg, ref MdluploadChkOut mdlOutput)
        {
            int result;
            string action = "/fixmedins/uploadChk";
            result = SendMsg(action, "Ld7801", mdlInput, ref msg, ref mdlOutput);
            return result;
        }
        /// <summary>
        /// 电子处方医保电子签名
        /// </summary>
        /// <param name="mdlInput"></param>
        /// <param name="msg"></param>
        /// <param name="mdlOutput"></param>
        /// <returns></returns>
        public int rxFixmedinsSign(MdlrxFixmedinsSignIn mdlInput, ref string msg, ref MdlrxFixmedinsSignOut mdlOutput)
        {
            int result;
            string action = "/fixmedins/rxFixmedinsSign";
            result = SendMsg(action, "Ld7802", mdlInput, ref msg, ref mdlOutput);
            return result;
        }
        /// <summary>
        /// 电子处方上传
        /// </summary>
        /// <param name="mdlInput"></param>
        /// <param name="msg"></param>
        /// <param name="mdlOutput"></param>
        /// <returns></returns>
        public int rxFileUpld(MdlrxFileUpldIn mdlInput, ref string msg, ref MdlrxFileUpldOut mdlOutput)
        {
            int result;
            string action = "/fixmedins/rxFileUpld";
            result = SendMsg(action, "Ld7101", mdlInput, ref msg, ref mdlOutput);
            return result;
        }
        /// <summary>
        /// 电子处方撤销
        /// </summary>
        /// <param name="mdlInput"></param>
        /// <param name="msg"></param>
        /// <param name="mdlOutput"></param>
        /// <returns></returns>
        public int rxUndo(MdlrxUndoIn mdlInput, ref string msg, ref MdlrxUndoOut mdlOutput)
        {
            int result;
            string action = "/fixmedins/rxUndo";
            result = SendMsg(action, "Ld7104", mdlInput, ref msg, ref mdlOutput);
            return result;
        }
        /// <summary>
        /// 电子处方信息查询
        /// </summary>
        /// <param name="mdlInput"></param>
        /// <param name="msg"></param>
        /// <param name="mdlOutput"></param>
        /// <returns></returns>
        public int hospRxDetlQuery(MdlhospRxDetlQueryIn mdlInput, ref string msg, ref MdlhospRxDetlQueryOut mdlOutput)
        {
            int result;
            string action = "/fixmedins/hospRxDetlQuery";
            result = SendMsg(action, "Ld7202", mdlInput, ref msg, ref mdlOutput);
            return result;
        }
        /// <summary>
        /// 电子处方审核结果査询
        /// </summary>
        /// <param name="mdlInput"></param>
        /// <param name="msg"></param>
        /// <param name="mdlOutput"></param>
        /// <returns></returns>
        public int rxChkInfoQuery(MdlrxChkInfoQueryIn mdlInput, ref string msg, ref MdlrxChkInfoQueryOut mdlOutput)
        {
            int result;
            string action = "/fixmedins/rxChkInfoQuery";
            result = SendMsg(action, "Ld7805", mdlInput, ref msg, ref mdlOutput);
            return result;
        }
        /// <summary>
        /// 电子处方取药结果査询
        /// </summary>
        /// <param name="mdlInput"></param>
        /// <param name="msg"></param>
        /// <param name="mdlOutput"></param>
        /// <returns></returns>
        public int rxSetlInfoQuery(MdlrxSetlInfoQueryIn mdlInput, ref string msg, ref MdlrxSetlInfoQueryOut mdlOutput)
        {
            int result;
            string action = "/fixmedins/rxSetlInfoQuery";
            result = SendMsg(action, "Ld7804", mdlInput, ref msg, ref mdlOutput);
            return result;
        }
        /// <summary>
        /// 电子处方药品目录查询
        /// </summary>
        /// <param name="mdlInput"></param>
        /// <param name="msg"></param>
        /// <param name="mdlOutput"></param>
        /// <returns></returns>
        public int circDrugQuery(MdlcircDrugQueryIn mdlInput, ref string msg, ref MdlcircDrugQueryOut mdlOutput)
        {
            int result;
            string action = "/fixmedins/circDrugQuery";
            result = SendMsg(action, "Ld7806", mdlInput, ref msg, ref mdlOutput);
            return result;
        }
        #region 公共函数

        private int SendMsg<T, Out>(string action, string infno, T mdlInput, ref string msg, ref Out mdlOutput)
        {
            string signData, sm4Ciphertext, postData;
            string inputPlaintext = JsonConvert.SerializeObject(mdlInput);
            MdlERXInTemp mdlErxInTemp = new MdlERXInTemp();
            mdlErxInTemp.appId = ERXConfig.ERX_Id;
            mdlErxInTemp.timestamp = TimeHelp.GetTimeStamp(DateTime.Now);
            mdlErxInTemp.data = inputPlaintext;

            string signDataPlaintext = $"appId={mdlErxInTemp.appId}&data={SortJson(inputPlaintext)}&encType=SM4&signType=SM2&timestamp={mdlErxInTemp.timestamp}&version=1.0.0&key={ERXConfig.ERX_Key}";
            signData = Convert.ToBase64String(GmUtil.SignSm3WithSm2(signDataPlaintext, ERXConfig.ERX_Key, ERXConfig.ERX_PriKey));
            var sm4NewKeyByte = GmUtil.Sm4EncryptECB(Encoding.UTF8.GetBytes(ERXConfig.ERX_Id.Substring(0, 16)), Encoding.UTF8.GetBytes(ERXConfig.ERX_Key), GmUtil.SM4_ECB_PKCS7PADDING);
            string sm4NewKey = Hex.ToHexString(sm4NewKeyByte).ToUpper().Substring(0, 16);
            sm4Ciphertext = Hex.ToHexString(GmUtil.Sm4EncryptECB(Encoding.UTF8.GetBytes(sm4NewKey), Encoding.UTF8.GetBytes(inputPlaintext), GmUtil.SM4_ECB_PKCS7PADDING)).ToUpper();

            MdlERXIn mdlErxIn = new MdlERXIn();
            mdlErxIn.appId = mdlErxInTemp.appId;
            mdlErxIn.timestamp = mdlErxInTemp.timestamp;
            mdlErxIn.encData = sm4Ciphertext;
            mdlErxIn.signData = signData;
            switch (YB_Config.Province)
            {
                case YbProvince.内蒙古:
                    {
                        Common.Log.Log.WriteLog($"{YB_Config.TxtLogPath}\\" + DateTime.Now.ToString("yyyyMMdd") + ".log", $"*************************** 医保电子处方接口 【{action}】 -> 开始 ***************************" + System.Environment.NewLine);
                        Common.Log.Log.WriteLog($"{YB_Config.TxtLogPath}\\" + DateTime.Now.ToString("yyyyMMdd") + ".log", $"接口地址: {ERXConfig.ERX_Url + action}" + System.Environment.NewLine);
                        Common.Log.Log.WriteLog($"{YB_Config.TxtLogPath}\\" + DateTime.Now.ToString("yyyyMMdd") + ".log", "调用时间: " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + System.Environment.NewLine);
                        Common.Log.Log.WriteLog($"{YB_Config.TxtLogPath}\\" + DateTime.Now.ToString("yyyyMMdd") + ".log", "入参明文: " + JsonConvert.SerializeObject(mdlErxInTemp) + System.Environment.NewLine);
                        YB_API ybApi = new YB_API(ZTHisVar.Var.JsrCode, ZTHisVar.Var.JsrName);
                        string erxout = "";
                        int ret = ybApi.SendMsg<MdlERXIn, string>("", infno, action, mdlErxIn, ref msg, ref erxout);
                        MdlERXOut mdlErxOut = new MdlERXOut();
                        mdlErxOut = JsonConvert.DeserializeObject<MdlERXOut>(erxout);
                        if (ret == 0)
                        {
                            if (mdlErxOut.code == 0)
                            {
                                MdlERXOutPlaintext<Out> mdlErxOutPlaintext = new MdlERXOutPlaintext<Out>();
                                Common.ModelTools.ToNewModel(mdlErxOut, mdlErxOutPlaintext);
                                mdlErxOutPlaintext.data = JsonConvert.DeserializeObject<Out>(Encoding.UTF8.GetString(GmUtil.Sm4DecryptECB(Encoding.UTF8.GetBytes(sm4NewKey), Hex.Decode(mdlErxOut.encData), GmUtil.SM4_ECB_PKCS7PADDING)));
                                mdlOutput = mdlErxOutPlaintext.data;
                                Common.Log.Log.WriteLog($"{YB_Config.TxtLogPath}\\" + DateTime.Now.ToString("yyyyMMdd") + ".log", "出参明文: " + JsonConvert.SerializeObject(mdlErxOutPlaintext) + System.Environment.NewLine);
                            }
                            msg = mdlErxOut.message;
                            ret = mdlErxOut.code;
                        }
                        Common.Log.Log.WriteLog($"{YB_Config.TxtLogPath}\\" + DateTime.Now.ToString("yyyyMMdd") + ".log", $"*************************** 医保电子处方接口 【{action}】 -> 结束 ***************************" + System.Environment.NewLine + System.Environment.NewLine);
                        return ret;
                    }
                default:
                    {
                        RestClient restClient = new RestClient(ERXConfig.ERX_Url + action);
                        restClient.ContentType = "Application/json";
                        restClient.Method = HttpVerb.POST;
                        restClient.Timeout = 1000 * 60 * 5; //超时改成5分钟
                        postData = JsonConvert.SerializeObject(mdlErxIn);
                        restClient.PostData = postData;

                        Common.Log.Log.WriteLog($"{YB_Config.TxtLogPath}\\" + DateTime.Now.ToString("yyyyMMdd") + ".log", $"*************************** 医保电子处方接口 【{action}】 -> 开始 ***************************" + System.Environment.NewLine);
                        Common.Log.Log.WriteLog($"{YB_Config.TxtLogPath}\\" + DateTime.Now.ToString("yyyyMMdd") + ".log", $"接口地址: {ERXConfig.ERX_Url + action}" + System.Environment.NewLine);
                        Common.Log.Log.WriteLog($"{YB_Config.TxtLogPath}\\" + DateTime.Now.ToString("yyyyMMdd") + ".log", "调用时间: " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + System.Environment.NewLine);
                        Common.Log.Log.WriteLog($"{YB_Config.TxtLogPath}\\" + DateTime.Now.ToString("yyyyMMdd") + ".log", "入参明文: " + JsonConvert.SerializeObject(mdlErxInTemp) + System.Environment.NewLine);
                        Common.Log.Log.WriteLog($"{YB_Config.TxtLogPath}\\" + DateTime.Now.ToString("yyyyMMdd") + ".log", "入参密文: " + postData + System.Environment.NewLine);

                        string allOutPara;
                        allOutPara = restClient.MakeRequest();
                        Common.Log.Log.WriteLog($"{YB_Config.TxtLogPath}\\" + DateTime.Now.ToString("yyyyMMdd") + ".log", "回参时间: " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + System.Environment.NewLine);
                        Common.Log.Log.WriteLog($"{YB_Config.TxtLogPath}\\" + DateTime.Now.ToString("yyyyMMdd") + ".log", "出参密文: " + allOutPara + System.Environment.NewLine);
                        int ret;
                        if (restClient.StatusCode == HttpStatusCode.OK)
                        {
                            MdlERXOut mdlErxOut = new MdlERXOut();
                            mdlErxOut = JsonConvert.DeserializeObject<MdlERXOut>(allOutPara);
                            if (mdlErxOut.code == 0)
                            {
                                MdlERXOutPlaintext<Out> mdlErxOutPlaintext = new MdlERXOutPlaintext<Out>();
                                Common.ModelTools.ToNewModel(mdlErxOut, mdlErxOutPlaintext);
                                mdlErxOutPlaintext.data = JsonConvert.DeserializeObject<Out>(Encoding.UTF8.GetString(GmUtil.Sm4DecryptECB(Encoding.UTF8.GetBytes(sm4NewKey), Hex.Decode(mdlErxOut.encData), GmUtil.SM4_ECB_PKCS7PADDING)));
                                mdlOutput = mdlErxOutPlaintext.data;
                                Common.Log.Log.WriteLog($"{YB_Config.TxtLogPath}\\" + DateTime.Now.ToString("yyyyMMdd") + ".log", "出参明文: " + JsonConvert.SerializeObject(mdlErxOutPlaintext) + System.Environment.NewLine);


                            }
                            msg = mdlErxOut.message;
                            ret = mdlErxOut.code;
                        }
                        else
                        {
                            msg = allOutPara;
                            ret = -1;
                        }
                        Common.Log.Log.WriteLog($"{YB_Config.TxtLogPath}\\" + DateTime.Now.ToString("yyyyMMdd") + ".log", $"*************************** 医保电子处方接口 【{action}】 -> 结束 ***************************" + System.Environment.NewLine + System.Environment.NewLine);
                        return ret;
                    }
            }

        }

        //private string SortJson(string json)
        //{
        //    var dic = JsonConvert.DeserializeObject<SortedDictionary<string, object>>(json);
        //    SortedDictionary<string, object> keyValues = new SortedDictionary<string, object>(StringComparer.Ordinal);
        //    foreach (var item in dic)
        //    {
        //        if (item.Value != null)
        //        {
        //            keyValues.Add(item.Key, item.Value);
        //        }
        //    }
        //    keyValues.OrderBy(m => m.Key);//升序 把Key换成Value 就是对Value进行排序
        //    //keyValues.OrderByDescending(m => m.Key);//降序
        //    return JsonConvert.SerializeObject(keyValues);
        //}

        private string SortJson(string json)
        {
            var dic = JsonConvert.DeserializeObject<SortedDictionary<string, object>>(json);
            SortedDictionary<string, object> keyValues = new SortedDictionary<string, object>(StringComparer.Ordinal);
            foreach (var item in dic)
            {
                if (item.Value != null)
                {
                    Type t1 = item.Value.GetType();
                    if (t1 == typeof(JObject))
                    {
                        // value是JObject类型
                        string jsonItem = JsonConvert.SerializeObject(item.Value);
                        jsonItem = SortJson(jsonItem);
                        keyValues[item.Key] = JsonConvert.DeserializeObject<JObject>(jsonItem);
                    }
                    else if (t1 == typeof(JArray))
                    {
                        // value是JArray类型
                        JArray jsonArray = (JArray)item.Value;
                        JArray newjsonArray = new JArray();
                        foreach (JObject arrayItem in jsonArray)
                        {
                            string jsonItem = JsonConvert.SerializeObject(arrayItem);
                            jsonItem = SortJson(jsonItem);
                            newjsonArray.Add(JsonConvert.DeserializeObject<JObject>(jsonItem));
                        }
                        
                        keyValues[item.Key] = newjsonArray;
                    }
                    else if (t1 == typeof(JValue))
                    {
                        JValue jval = (JValue)item.Value;
                        if (jval != null && !String.IsNullOrEmpty(jval.ToString()))
                        {
                            if (jval.ToString().ToLower().Trim().Equals("true") || jval.ToString().ToLower().Trim().Equals("false"))
                                keyValues.Add(item.Key, jval.ToString().ToLower().Trim());
                            else
                                keyValues.Add(item.Key, jval.ToString());
                        }
                    }
                    else
                    {
                        if (!string.IsNullOrEmpty(item.Value.ToString()))
                        {
                            keyValues.Add(item.Key, item.Value);
                        }
                    }
                }
            }
            keyValues.OrderBy(m => m.Key);//升序 把Key换成Value 就是对Value进行排序
            //keyValues.OrderByDescending(m => m.Key);//降序
            return JsonConvert.SerializeObject(keyValues);
        }
        #endregion
    }
}
