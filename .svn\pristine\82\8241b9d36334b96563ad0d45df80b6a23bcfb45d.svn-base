﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class Xs_Mz1
    Inherits HisControl.BaseForm

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(Xs_Mz1))
        Me.Panel2 = New System.Windows.Forms.Panel()
        Me.T_Label3 = New System.Windows.Forms.Label()
        Me.T_Label4 = New System.Windows.Forms.Label()
        Me.C1Holder1 = New C1.Win.C1Command.C1CommandHolder()
        Me.Comm1 = New C1.Win.C1Command.C1Command()
        Me.Comm2 = New C1.Win.C1Command.C1Command()
        Me.Comm3 = New C1.Win.C1Command.C1Command()
        Me.Comm5 = New C1.Win.C1Command.C1Command()
        Me.CmdSm = New C1.Win.C1Command.C1Command()
        Me.CmdZh = New C1.Win.C1Command.C1Command()
        Me.CmdYbSq = New C1.Win.C1Command.C1Command()
        Me.CmdTxBl = New C1.Win.C1Command.C1Command()
        Me.T_Label2 = New C1.Win.C1Input.C1Label()
        Me.C1TrueDBGrid1 = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.QRTextBox1 = New CustomControl.MyTextBox()
        Me.C1DateEdit1 = New C1.Win.C1Input.C1DateEdit()
        Me.T_Line3 = New System.Windows.Forms.Label()
        Me.ToolBar1 = New C1.Win.C1Command.C1ToolBar()
        Me.Link1 = New C1.Win.C1Command.C1CommandLink()
        Me.Link2 = New C1.Win.C1Command.C1CommandLink()
        Me.Link3 = New C1.Win.C1Command.C1CommandLink()
        Me.C1CommandLink4 = New C1.Win.C1Command.C1CommandLink()
        Me.C1CommandLink2 = New C1.Win.C1Command.C1CommandLink()
        Me.C1CommandLink1 = New C1.Win.C1Command.C1CommandLink()
        Me.C1CommandLink3 = New C1.Win.C1Command.C1CommandLink()
        Me.T_Line1 = New System.Windows.Forms.Label()
        Me.T_Line2 = New System.Windows.Forms.Label()
        Me.T_Combo = New C1.Win.C1List.C1Combo()
        Me.T_Textbox = New C1.Win.C1Input.C1TextBox()
        Me.T_Label = New C1.Win.C1Input.C1Label()
        Me.C1CommandLink5 = New C1.Win.C1Command.C1CommandLink()
        Me.CmdAI = New C1.Win.C1Command.C1Command()
        Me.Panel2.SuspendLayout
        CType(Me.C1Holder1,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.T_Label2,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.C1TrueDBGrid1,System.ComponentModel.ISupportInitialize).BeginInit
        Me.Panel1.SuspendLayout
        CType(Me.C1DateEdit1,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.T_Combo,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.T_Textbox,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.T_Label,System.ComponentModel.ISupportInitialize).BeginInit
        Me.SuspendLayout
        '
        'Panel2
        '
        Me.Panel2.BackColor = System.Drawing.Color.Transparent
        Me.Panel2.Controls.Add(Me.T_Label3)
        Me.Panel2.Controls.Add(Me.T_Label4)
        Me.Panel2.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.Panel2.Location = New System.Drawing.Point(0, 521)
        Me.Panel2.Name = "Panel2"
        Me.Panel2.Size = New System.Drawing.Size(836, 21)
        Me.Panel2.TabIndex = 7
        '
        'T_Label3
        '
        Me.T_Label3.AutoSize = true
        Me.T_Label3.Location = New System.Drawing.Point(394, 4)
        Me.T_Label3.Name = "T_Label3"
        Me.T_Label3.Size = New System.Drawing.Size(47, 12)
        Me.T_Label3.TabIndex = 6
        Me.T_Label3.Text = "∑本日="
        Me.T_Label3.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'T_Label4
        '
        Me.T_Label4.AutoSize = true
        Me.T_Label4.BackColor = System.Drawing.SystemColors.ButtonFace
        Me.T_Label4.ForeColor = System.Drawing.Color.DarkRed
        Me.T_Label4.Location = New System.Drawing.Point(472, 4)
        Me.T_Label4.Name = "T_Label4"
        Me.T_Label4.Size = New System.Drawing.Size(53, 12)
        Me.T_Label4.TabIndex = 5
        Me.T_Label4.Text = "T_Label4"
        Me.T_Label4.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'C1Holder1
        '
        Me.C1Holder1.Commands.Add(Me.Comm1)
        Me.C1Holder1.Commands.Add(Me.Comm2)
        Me.C1Holder1.Commands.Add(Me.Comm3)
        Me.C1Holder1.Commands.Add(Me.Comm5)
        Me.C1Holder1.Commands.Add(Me.CmdSm)
        Me.C1Holder1.Commands.Add(Me.CmdZh)
        Me.C1Holder1.Commands.Add(Me.CmdYbSq)
        Me.C1Holder1.Commands.Add(Me.CmdTxBl)
        Me.C1Holder1.Commands.Add(Me.CmdAI)
        Me.C1Holder1.Owner = Me
        '
        'Comm1
        '
        Me.Comm1.Image = CType(resources.GetObject("Comm1.Image"),System.Drawing.Image)
        Me.Comm1.Name = "Comm1"
        Me.Comm1.ShortcutText = ""
        Me.Comm1.Text = "增加"
        Me.Comm1.ToolTipText = "增加记录"
        '
        'Comm2
        '
        Me.Comm2.Image = CType(resources.GetObject("Comm2.Image"),System.Drawing.Image)
        Me.Comm2.Name = "Comm2"
        Me.Comm2.ShortcutText = ""
        Me.Comm2.Text = "删除"
        Me.Comm2.ToolTipText = "删除记录"
        '
        'Comm3
        '
        Me.Comm3.Image = CType(resources.GetObject("Comm3.Image"),System.Drawing.Image)
        Me.Comm3.Name = "Comm3"
        Me.Comm3.ShortcutText = ""
        Me.Comm3.Text = "更新"
        Me.Comm3.ToolTipText = "更新记录"
        '
        'Comm5
        '
        Me.Comm5.Image = CType(resources.GetObject("Comm5.Image"),System.Drawing.Image)
        Me.Comm5.Name = "Comm5"
        Me.Comm5.ShortcutText = ""
        Me.Comm5.Text = "打印病历"
        '
        'CmdSm
        '
        Me.CmdSm.Image = CType(resources.GetObject("CmdSm.Image"),System.Drawing.Image)
        Me.CmdSm.Name = "CmdSm"
        Me.CmdSm.ShortcutText = ""
        '
        'CmdZh
        '
        Me.CmdZh.Image = CType(resources.GetObject("CmdZh.Image"),System.Drawing.Image)
        Me.CmdZh.Name = "CmdZh"
        Me.CmdZh.ShortcutText = ""
        Me.CmdZh.Text = "召回"
        Me.CmdZh.ToolTipText = "可以召回未收费的处方"
        '
        'CmdYbSq
        '
        Me.CmdYbSq.Name = "CmdYbSq"
        Me.CmdYbSq.ShortcutText = ""
        Me.CmdYbSq.Text = "医保事前分析"
        '
        'CmdTxBl
        '
        Me.CmdTxBl.Name = "CmdTxBl"
        Me.CmdTxBl.ShortcutText = ""
        Me.CmdTxBl.Text = "填写病历"
        '
        'T_Label2
        '
        Me.T_Label2.AutoSize = true
        Me.T_Label2.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.T_Label2.ForeColor = System.Drawing.Color.DarkRed
        Me.T_Label2.Location = New System.Drawing.Point(633, 6)
        Me.T_Label2.Name = "T_Label2"
        Me.T_Label2.Size = New System.Drawing.Size(53, 12)
        Me.T_Label2.TabIndex = 1
        Me.T_Label2.Tag = Nothing
        Me.T_Label2.Text = "门诊日期"
        Me.T_Label2.TextDetached = true
        '
        'C1TrueDBGrid1
        '
        Me.C1TrueDBGrid1.GroupByCaption = "Drag a column header here to group by that column"
        Me.C1TrueDBGrid1.Images.Add(CType(resources.GetObject("C1TrueDBGrid1.Images"),System.Drawing.Image))
        Me.C1TrueDBGrid1.Location = New System.Drawing.Point(104, 61)
        Me.C1TrueDBGrid1.Name = "C1TrueDBGrid1"
        Me.C1TrueDBGrid1.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.C1TrueDBGrid1.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.C1TrueDBGrid1.PreviewInfo.ZoomFactor = 75R
        Me.C1TrueDBGrid1.PrintInfo.MeasurementDevice = C1.Win.C1TrueDBGrid.PrintInfo.MeasurementDeviceEnum.Screen
        Me.C1TrueDBGrid1.PrintInfo.MeasurementPrinterName = Nothing
        Me.C1TrueDBGrid1.Size = New System.Drawing.Size(319, 182)
        Me.C1TrueDBGrid1.TabIndex = 10
        Me.C1TrueDBGrid1.Text = "C1TrueDBGrid1"
        Me.C1TrueDBGrid1.UseCompatibleTextRendering = false
        Me.C1TrueDBGrid1.PropBag = resources.GetString("C1TrueDBGrid1.PropBag")
        '
        'Panel1
        '
        Me.Panel1.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Panel1.Controls.Add(Me.QRTextBox1)
        Me.Panel1.Controls.Add(Me.C1DateEdit1)
        Me.Panel1.Controls.Add(Me.T_Line3)
        Me.Panel1.Controls.Add(Me.ToolBar1)
        Me.Panel1.Controls.Add(Me.T_Line1)
        Me.Panel1.Controls.Add(Me.T_Line2)
        Me.Panel1.Controls.Add(Me.T_Label2)
        Me.Panel1.Controls.Add(Me.T_Combo)
        Me.Panel1.Controls.Add(Me.T_Textbox)
        Me.Panel1.Controls.Add(Me.T_Label)
        Me.Panel1.Dock = System.Windows.Forms.DockStyle.Top
        Me.Panel1.Location = New System.Drawing.Point(0, 0)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(836, 24)
        Me.Panel1.TabIndex = 0
        '
        'QRTextBox1
        '
        Me.QRTextBox1.Captain = ""
        Me.QRTextBox1.CaptainBackColor = System.Drawing.Color.Transparent
        Me.QRTextBox1.CaptainFont = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.QRTextBox1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.QRTextBox1.CaptainWidth = 60!
        Me.QRTextBox1.ContentForeColor = System.Drawing.Color.Black
        Me.QRTextBox1.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer))
        Me.QRTextBox1.EditMask = Nothing
        Me.QRTextBox1.Location = New System.Drawing.Point(775, 1)
        Me.QRTextBox1.Multiline = false
        Me.QRTextBox1.Name = "QRTextBox1"
        Me.QRTextBox1.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.QRTextBox1.ReadOnly = false
        Me.QRTextBox1.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.QRTextBox1.SelectionStart = 0
        Me.QRTextBox1.SelectStart = 0
        Me.QRTextBox1.Size = New System.Drawing.Size(1, 1)
        Me.QRTextBox1.TabIndex = 9
        Me.QRTextBox1.Text = "MyTextBox1"
        Me.QRTextBox1.TextAlign = System.Windows.Forms.HorizontalAlignment.Left
        Me.QRTextBox1.TextFont = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.QRTextBox1.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.QRTextBox1.Watermark = Nothing
        '
        'C1DateEdit1
        '
        Me.C1DateEdit1.AcceptsTab = true
        Me.C1DateEdit1.AutoSize = false
        Me.C1DateEdit1.BorderStyle = System.Windows.Forms.BorderStyle.None
        '
        '
        '
        Me.C1DateEdit1.Calendar.ClearText = "&C清空"
        Me.C1DateEdit1.Calendar.ShowToday = true
        Me.C1DateEdit1.Calendar.ShowWeekNumbers = true
        Me.C1DateEdit1.Calendar.TodayText = "&今天"
        Me.C1DateEdit1.ImagePadding = New System.Windows.Forms.Padding(0)
        Me.C1DateEdit1.Location = New System.Drawing.Point(686, 3)
        Me.C1DateEdit1.Name = "C1DateEdit1"
        Me.C1DateEdit1.Size = New System.Drawing.Size(83, 16)
        Me.C1DateEdit1.TabIndex = 0
        Me.C1DateEdit1.Tag = Nothing
        Me.C1DateEdit1.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.C1DateEdit1.VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.UpDown
        '
        'T_Line3
        '
        Me.T_Line3.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.T_Line3.Location = New System.Drawing.Point(633, -1)
        Me.T_Line3.Name = "T_Line3"
        Me.T_Line3.Size = New System.Drawing.Size(2, 25)
        Me.T_Line3.TabIndex = 7
        Me.T_Line3.Text = "Label2"
        '
        'ToolBar1
        '
        Me.ToolBar1.AccessibleName = "Tool Bar"
        Me.ToolBar1.CommandHolder = Me.C1Holder1
        Me.ToolBar1.CommandLinks.AddRange(New C1.Win.C1Command.C1CommandLink() {Me.Link1, Me.Link2, Me.Link3, Me.C1CommandLink4, Me.C1CommandLink2, Me.C1CommandLink1, Me.C1CommandLink3, Me.C1CommandLink5})
        Me.ToolBar1.Location = New System.Drawing.Point(1, 0)
        Me.ToolBar1.MinButtonSize = 22
        Me.ToolBar1.Movable = false
        Me.ToolBar1.Name = "ToolBar1"
        Me.ToolBar1.Size = New System.Drawing.Size(527, 22)
        Me.ToolBar1.Text = "C1ToolBar1"
        Me.ToolBar1.VisualStyle = C1.Win.C1Command.VisualStyle.Classic
        Me.ToolBar1.VisualStyleBase = C1.Win.C1Command.VisualStyle.Classic
        '
        'Link1
        '
        Me.Link1.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image),C1.Win.C1Command.ButtonLookFlags)
        Me.Link1.Command = Me.Comm1
        '
        'Link2
        '
        Me.Link2.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image),C1.Win.C1Command.ButtonLookFlags)
        Me.Link2.Command = Me.Comm2
        Me.Link2.SortOrder = 1
        Me.Link2.Text = "删除"
        '
        'Link3
        '
        Me.Link3.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image),C1.Win.C1Command.ButtonLookFlags)
        Me.Link3.Command = Me.Comm3
        Me.Link3.SortOrder = 2
        '
        'C1CommandLink4
        '
        Me.C1CommandLink4.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image),C1.Win.C1Command.ButtonLookFlags)
        Me.C1CommandLink4.Command = Me.CmdTxBl
        Me.C1CommandLink4.SortOrder = 3
        '
        'C1CommandLink2
        '
        Me.C1CommandLink2.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image),C1.Win.C1Command.ButtonLookFlags)
        Me.C1CommandLink2.Command = Me.Comm5
        Me.C1CommandLink2.SortOrder = 4
        '
        'C1CommandLink1
        '
        Me.C1CommandLink1.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image),C1.Win.C1Command.ButtonLookFlags)
        Me.C1CommandLink1.Command = Me.CmdZh
        Me.C1CommandLink1.SortOrder = 5
        '
        'C1CommandLink3
        '
        Me.C1CommandLink3.ButtonLook = C1.Win.C1Command.ButtonLookFlags.Text
        Me.C1CommandLink3.Command = Me.CmdYbSq
        Me.C1CommandLink3.SortOrder = 6
        '
        'T_Line1
        '
        Me.T_Line1.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.T_Line1.Location = New System.Drawing.Point(405, 0)
        Me.T_Line1.Name = "T_Line1"
        Me.T_Line1.Size = New System.Drawing.Size(2, 25)
        Me.T_Line1.TabIndex = 4
        Me.T_Line1.Text = "Label1"
        '
        'T_Line2
        '
        Me.T_Line2.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.T_Line2.Location = New System.Drawing.Point(567, -1)
        Me.T_Line2.Name = "T_Line2"
        Me.T_Line2.Size = New System.Drawing.Size(2, 25)
        Me.T_Line2.TabIndex = 6
        Me.T_Line2.Text = "Label2"
        '
        'T_Combo
        '
        Me.T_Combo.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.T_Combo.Caption = ""
        Me.T_Combo.CaptionHeight = 17
        Me.T_Combo.CharacterCasing = System.Windows.Forms.CharacterCasing.Normal
        Me.T_Combo.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.T_Combo.Images.Add(CType(resources.GetObject("T_Combo.Images"),System.Drawing.Image))
        Me.T_Combo.ItemHeight = 15
        Me.T_Combo.Location = New System.Drawing.Point(413, 3)
        Me.T_Combo.MatchEntryTimeout = CType(2000,Long)
        Me.T_Combo.MaxDropDownItems = CType(5,Short)
        Me.T_Combo.MaxLength = 32767
        Me.T_Combo.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.T_Combo.Name = "T_Combo"
        Me.T_Combo.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.T_Combo.Size = New System.Drawing.Size(76, 16)
        Me.T_Combo.TabIndex = 2
        Me.T_Combo.TabStop = false
        Me.T_Combo.PropBag = resources.GetString("T_Combo.PropBag")
        '
        'T_Textbox
        '
        Me.T_Textbox.AutoSize = false
        Me.T_Textbox.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.T_Textbox.Location = New System.Drawing.Point(490, 4)
        Me.T_Textbox.Name = "T_Textbox"
        Me.T_Textbox.Size = New System.Drawing.Size(72, 14)
        Me.T_Textbox.TabIndex = 3
        Me.T_Textbox.TabStop = false
        Me.T_Textbox.Tag = Nothing
        Me.T_Textbox.TextDetached = true
        Me.T_Textbox.TrimStart = true
        Me.T_Textbox.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        '
        'T_Label
        '
        Me.T_Label.AutoSize = true
        Me.T_Label.BackColor = System.Drawing.SystemColors.Control
        Me.T_Label.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.T_Label.ForeColor = System.Drawing.Color.Maroon
        Me.T_Label.Location = New System.Drawing.Point(575, 5)
        Me.T_Label.Name = "T_Label"
        Me.T_Label.Size = New System.Drawing.Size(47, 12)
        Me.T_Label.TabIndex = 2
        Me.T_Label.Tag = Nothing
        Me.T_Label.Text = "T_Label"
        Me.T_Label.TextAlign = System.Drawing.ContentAlignment.BottomLeft
        Me.T_Label.TextDetached = true
        Me.T_Label.TrimStart = true
        '
        'C1CommandLink5
        '
        Me.C1CommandLink5.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image),C1.Win.C1Command.ButtonLookFlags)
        Me.C1CommandLink5.Command = Me.CmdAI
        Me.C1CommandLink5.SortOrder = 7
        '
        'CmdAI
        '
        Me.CmdAI.Image = CType(resources.GetObject("CmdAI.Image"),System.Drawing.Image)
        Me.CmdAI.Name = "CmdAI"
        Me.CmdAI.ShortcutText = ""
        Me.CmdAI.Text = "AI辅助"
        '
        'Xs_Mz1
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6!, 12!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(836, 542)
        Me.Controls.Add(Me.C1TrueDBGrid1)
        Me.Controls.Add(Me.Panel1)
        Me.Controls.Add(Me.Panel2)
        Me.Icon = CType(resources.GetObject("$this.Icon"),System.Drawing.Icon)
        Me.Name = "Xs_Mz1"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "门诊录入"
        Me.Panel2.ResumeLayout(false)
        Me.Panel2.PerformLayout
        CType(Me.C1Holder1,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.T_Label2,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.C1TrueDBGrid1,System.ComponentModel.ISupportInitialize).EndInit
        Me.Panel1.ResumeLayout(false)
        Me.Panel1.PerformLayout
        CType(Me.C1DateEdit1,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.T_Combo,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.T_Textbox,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.T_Label,System.ComponentModel.ISupportInitialize).EndInit
        Me.ResumeLayout(false)

End Sub
    Friend WithEvents Panel2 As System.Windows.Forms.Panel
    Friend WithEvents T_Label3 As System.Windows.Forms.Label
    Friend WithEvents T_Label4 As System.Windows.Forms.Label
    Friend WithEvents C1Holder1 As C1.Win.C1Command.C1CommandHolder
    Friend WithEvents Comm1 As C1.Win.C1Command.C1Command
    Friend WithEvents Comm2 As C1.Win.C1Command.C1Command
    Friend WithEvents Comm3 As C1.Win.C1Command.C1Command
    Friend WithEvents T_Label2 As C1.Win.C1Input.C1Label
    Friend WithEvents C1TrueDBGrid1 As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents ToolBar1 As C1.Win.C1Command.C1ToolBar
    Friend WithEvents Link1 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link2 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link3 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents T_Line1 As System.Windows.Forms.Label
    Friend WithEvents T_Line2 As System.Windows.Forms.Label
    Friend WithEvents T_Combo As C1.Win.C1List.C1Combo
    Friend WithEvents T_Textbox As C1.Win.C1Input.C1TextBox
    Friend WithEvents T_Label As C1.Win.C1Input.C1Label
    Friend WithEvents T_Line3 As System.Windows.Forms.Label
    Friend WithEvents C1DateEdit1 As C1.Win.C1Input.C1DateEdit
    Friend WithEvents Comm5 As C1.Win.C1Command.C1Command
    Friend WithEvents C1CommandLink2 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents CmdSm As C1.Win.C1Command.C1Command
    Friend WithEvents QRTextBox1 As CustomControl.MyTextBox
    Friend WithEvents CmdZh As C1.Win.C1Command.C1Command
    Friend WithEvents C1CommandLink1 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents CmdYbSq As C1.Win.C1Command.C1Command
    Friend WithEvents C1CommandLink3 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents CmdTxBl As C1.Win.C1Command.C1Command
    Friend WithEvents C1CommandLink4 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents CmdAI As C1.Win.C1Command.C1Command
    Friend WithEvents C1CommandLink5 As C1.Win.C1Command.C1CommandLink
End Class
