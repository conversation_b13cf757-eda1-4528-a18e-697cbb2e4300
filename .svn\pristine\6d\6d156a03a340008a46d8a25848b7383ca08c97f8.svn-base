﻿/**  版本信息模板在安装目录下，可自行修改。
* M_Materials_Warehouse_Dict.cs
*
* 功 能： N/A
* 类 名： M_Materials_Warehouse_Dict
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016-12-03 09:37:11   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace ModelOld
{
	/// <summary>
	/// 物资库房字典
	/// </summary>
	[Serializable]
	public partial class M_Materials_Warehouse_Dict
	{
		public M_Materials_Warehouse_Dict()
		{}
		#region Model
		private string _materialswh_code;
		private string _materialswh_name;
		private string _materialswh_py;
		private string _materialswh_wb;
		private string _materialswh_memo;
		private int? _serial_no;
		private bool _isuse;
		/// <summary>
		/// 库房编码
		/// </summary>
		public string MaterialsWh_Code
		{
			set{ _materialswh_code=value;}
			get{return _materialswh_code;}
		}
		/// <summary>
		/// 库房名称
		/// </summary>
		public string MaterialsWh_Name
		{
			set{ _materialswh_name=value;}
			get{return _materialswh_name;}
		}
		/// <summary>
		/// 库房拼音
		/// </summary>
		public string MaterialsWh_Py
		{
			set{ _materialswh_py=value;}
			get{return _materialswh_py;}
		}
		/// <summary>
		/// 库房五笔
		/// </summary>
		public string MaterialsWh_Wb
		{
			set{ _materialswh_wb=value;}
			get{return _materialswh_wb;}
		}
		/// <summary>
		/// 库房备注
		/// </summary>
		public string MaterialsWh_Memo
		{
			set{ _materialswh_memo=value;}
			get{return _materialswh_memo;}
		}
		/// <summary>
		/// 排列顺序
		/// </summary>
		public int? Serial_No
		{
			set{ _serial_no=value;}
			get{return _serial_no;}
		}
		/// <summary>
		/// 真为启用，假为停用，默认值为真
		/// </summary>
		public bool IsUse
		{
			set{ _isuse=value;}
			get{return _isuse;}
		}
		#endregion Model

	}
}

