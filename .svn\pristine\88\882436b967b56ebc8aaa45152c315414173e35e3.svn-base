﻿Imports System.Windows.Forms
Imports System.Text



Public Class ZkSc

#Region "变量"
    Private My_Table As New DataTable
    Private My_Cm As CurrencyManager
    Private My_View As DataView



    Private bllBl As New BLLOld.B_Bl
    Private bllKs As New BLLOld.B_Zd_YyKs
    Private bllYs As New BLLOld.B_Zd_YyYs
    Private bllBq1 As New BLLOld.B_Zd_YyBq1

#End Region

    Private Sub BlGuiDang_Load(sender As Object, e As System.EventArgs) Handles MyBase.Load
        Call Form_Init()
    End Sub
#Region "窗体初始化"
    Private Sub Form_Init()
        With MyGrid1
            .Clear()

            .Init_Column("病历号", "Bl_Code", "150", "中", "", False)
            .Init_Column("身份证", "Ry_Sfzh", "0 ", "左", "", False)
            .Init_Column("姓名", "Ry_Name", "120", "中", "", False)
            .Init_Column("性别", "Ry_Sex", "60", "中", "", False)
            .Init_Column("电话", "Ry_Tele", "100", "中", "", False)
            .Init_Column("疾病", "Jb_Name", "180", "中", "", False)
            .Init_Column("科室", "Ks_Name", "100 ", "中", "", False)
            .Init_Column("主治医师", "Ys_Name", "100 ", "中", "", False)
            .Init_Column("病区", "Bq_Name", "80 ", "中", "", False)
            .Init_Column("病床号", "Bc_Code", "80 ", "中", "", False)
            .Init_Column("病床", "Bc_Name", "80 ", "中", "", False)
            .Init_Column("入院时间", "Ry_RyDate", "100 ", "中", "yyyy-MM-dd", False)
            .Init_Column("出院时间", "Ry_CyDate", "100 ", "中", "yyyy-MM-dd", False)
            '.Init_Column("状态", "Emr_GuiDang", "60 ", "中", "Combobox", False)
            .Init_Column("总分数", "Zk_Pf", "100 ", "中", "", False)
            .Init_Column("等级", "ZkDj_name", "100 ", "中", "", False)

            '.Columns("Emr_GuiDang").ValueItems.Values.Add(New C1.Win.C1TrueDBGrid.ValueItem(True, "已归档"))
            '.Columns("Emr_GuiDang").ValueItems.Values.Add(New C1.Win.C1TrueDBGrid.ValueItem(False, "未归档"))

        End With

        With BqCombo
            .DataView = bllBq1.GetList("Yy_Code='" & HisVar.HisVar.WsyCode & "'").Tables(0).DefaultView
            .Init_Colum("bq_name", "病区名称", 120, "中")
            .Init_Colum("bq_code", "病区编码", 0, "中")
            .Init_Colum("bq_jc", "病区简称", 0, "中")
            .ValueMember = "bq_code"
            .DisplayMember = "bq_name"
            .RowFilterNotTextNull = "bq_jc"
            .DroupDownWidth = .Width
            .SelectedIndex = -1
        End With

        With ZtCombo
            .Additem = "在院"
            .Additem = "出院未归档"
            .Additem = "出院已归档"
            .DisplayColumns(1).Visible = False
            .DroupDownWidth = .Width
            .SelectedIndex = 0
        End With

        With KsCombo
            .DataView = bllKs.GetList("Yy_Code='" & HisVar.HisVar.WsyCode & "'").Tables(0).DefaultView
            .Init_Colum("ks_name", "科室名称", 120, "中")
            .Init_Colum("ks_code", "科室编码", 0, "中")
            .Init_Colum("ks_jc", "科室简称", 0, "中")
            .ValueMember = "ks_code"
            .DisplayMember = "ks_name"
            .RowFilterNotTextNull = "ks_jc"
            .DroupDownWidth = .Width
            .SelectedIndex = -1
        End With



        With YsCombo
            .DataView = bllYs.GetList("Yy_Code='" & HisVar.HisVar.WsyCode & "'").Tables(0).DefaultView
            .Init_Colum("Ys_name", "医生名称", 120, "中")
            .Init_Colum("Ys_code", "医生编码", 0, "中")
            .Init_Colum("Ys_jc", "医生简称", 0, "中")
            .ValueMember = "Ys_code"
            .DisplayMember = "Ys_name"
            .RowFilterNotTextNull = "Ys_jc"
            .DroupDownWidth = .Width
            .SelectedIndex = -1
        End With
    End Sub

    Private Sub Data_Init()
        Dim strConditon As New StringBuilder



        If ZtCombo.Text = "出院未归档" Then
            strConditon.Append(" CONVERT(VARCHAR(10),Ry_CyDate,126) BETWEEN '" &
CType(MyDateEdit1.Value, Date).ToString("yyyy-MM-dd") & "' AND '" &
CType(MyDateEdit2.Value, Date).ToString("yyyy-MM-dd") & "'")
            strConditon.Append(" AND (Emr_GuiDang=0 or Emr_GuiDang is null)")
        ElseIf ZtCombo.Text = "出院已归档" Then
            strConditon.Append(" CONVERT(VARCHAR(10),Ry_CyDate,126) BETWEEN '" &
CType(MyDateEdit1.Value, Date).ToString("yyyy-MM-dd") & "' AND '" &
CType(MyDateEdit2.Value, Date).ToString("yyyy-MM-dd") & "'")
            strConditon.Append(" AND Emr_GuiDang=1")
        Else
            strConditon.Append("  Ry_CyJsr IS NULL ")
        End If

        If Trim(NameText.Text) <> "" Then strConditon.Append("   AND (Ry_Name LIKE '%" &
            NameText.Text & "%' OR Ry_Jc LIKE '%" & NameText.Text & "%') ")

        If BqCombo.SelectedValue IsNot Nothing Then strConditon.Append(" AND Bq_Code='" & BqCombo.SelectedValue & "'")
        If Trim(BedText.Text) <> "" Then strConditon.Append(" AND Bl.Bc_Code LIKE '%" & BedText.Text & "%'")
        If KsCombo.SelectedValue IsNot Nothing Then strConditon.Append(" AND Bl.Ks_Code='" & KsCombo.SelectedValue & "'")
        If YsCombo.SelectedValue IsNot Nothing Then strConditon.Append(" AND Bl.Ys_Code='" & YsCombo.SelectedValue & "'")

        My_Table = bllBl.GetList(strConditon.ToString).Tables(0)
        ' My_Table.Columns("isSelected").ReadOnly = False
        With MyGrid1
            My_Cm = CType(BindingContext(My_Table, ""), CurrencyManager)
            My_View = My_Cm.List
            .DataTable = My_Table
        End With
    End Sub
#End Region

    Private Sub BtnCx_Click(sender As System.Object, e As System.EventArgs) Handles BtnCx.Click
        Call Data_Init()
    End Sub

  

    Private Sub KsCombo_RowChange(sender As Object, e As System.EventArgs) Handles KsCombo.SelectedValueChanged
        If KsCombo.SelectedValue IsNot Nothing Then
            YsCombo.DataView = bllYs.GetList("Yy_Code='" & HisVar.HisVar.WsyCode & "' and ks_code='" & KsCombo.SelectedValue & "'").Tables(0).DefaultView
        Else
            YsCombo.DataView = bllYs.GetList("Yy_Code='" & HisVar.HisVar.WsyCode & "'").Tables(0).DefaultView
        End If
        With YsCombo
            .Init_Colum("Ys_name", "医生名称", 120, "中")
            .Init_Colum("Ys_code", "医生编码", 0, "中")
            .Init_Colum("Ys_jc", "医生简称", 0, "中")
            .ValueMember = "Ys_code"
            .DisplayMember = "Ys_name"
            .RowFilterNotTextNull = "Ys_jc"
            .DroupDownWidth = .Width
            .SelectedIndex = -1
        End With

    End Sub


    Private Sub ZtCombo_RowChange(sender As Object, e As System.EventArgs) Handles ZtCombo.RowChange
        If ZtCombo.Text = "在院" Then
            MyDateEdit1.Enabled = False
            MyDateEdit2.Enabled = False
        Else
            MyDateEdit1.Enabled = True
            MyDateEdit2.Enabled = True
        End If
    End Sub

    Private Sub MyButton1_Click(sender As System.Object, e As System.EventArgs) Handles MyButton1.Click
        'Dim frm As New ZkSc2(My_Cm.List(MyGrid1.Row).Row("Bl_Code"))
        If My_Cm Is Nothing Then
            MsgBox("请选择病人")
            Exit Sub
        End If
        Dim frm As New ZkSc2(My_Cm.List(MyGrid1.Row).Row)
        frm.Owner = Me
        frm.ShowDialog()
    End Sub
End Class
