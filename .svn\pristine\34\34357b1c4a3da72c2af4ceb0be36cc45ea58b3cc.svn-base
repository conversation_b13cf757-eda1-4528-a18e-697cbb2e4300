ALTER TABLE Country_YB_SpCg ADD	jxc_code VARCHAR(100)
ALTER TABLE Country_YB_SpCgTh ADD	jxc_code VARCHAR(100)
GO
UPDATE Country_YB_SpCg SET 
jxc_code=CASE WHEN LEFT(Country_YB_SpCg.fixmedins_bchno,2)='Yk' THEN 'YKRK' ELSE 'YFRK' END
+SUBSTRING(Country_YB_SpCg.fixmedins_bchno,3,LEN(Country_YB_SpCg.fixmedins_bchno)-2)+'-'+Country_YB_SpCg.memo
WHERE Country_YB_SpCg.memo IS NOT NULL AND 
(LEFT(Country_YB_SpCg.fixmedins_bchno,2)='Yk' OR LEFT(Country_YB_SpCg.fixmedins_bchno,2)='Yf')