﻿Public Class Msg_YkYxq
    Dim My_Dataset As New DataSet
    Dim My_Cm As CurrencyManager             '同步指针
    Dim My_View As New DataView
    Private Sub Alar_Cx2_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Call Init_Form()
        Call Init_Data()
    End Sub

    Private Sub Init_Form()
        C1TextBox1.Text = ""
        Dim My_Grid As New BaseClass.C_Grid(Me.C1TrueDBGrid1)
        With My_Grid
            .Init_Grid()
            .Init_Column("药品编码", "Xx_Code", 0, "左", "")
            .Init_Column("药品名称", "Yp_Name", 150, "左", "")
            .Init_Column("规格", "Mx_Gg", 100, "左", "")
            .Init_Column("产地", "Mx_Cd", 150, "左", "")
            .Init_Column("有效期", "Yp_Yxq", 70, "中", "yyyy-MM-dd")
            .Init_Column("单位", "Mx_CgDw", 50, "中", "")
            .Init_Column("售价", "Yk_Xsj", 60, "右", "######0.00##")
            .Init_Column("库存", "Yk_Sl", 60, "右", "########0.####")
            .Init_Column("预警天数", "Xq_Ts", 60, "中", "")
            .Init_Column("排序", "Px", 0, "中", "")
        End With
        Me.C1TrueDBGrid1.Splits(0).DisplayColumns(8).OwnerDraw = True
        C1TrueDBGrid1.HScrollBar.Style = C1.Win.C1TrueDBGrid.ScrollBarStyleEnum.Automatic

        Dim My_Combo As New BaseClass.C_Combo1(Me.C1Combo1)
        With My_Combo
            .Init_TDBCombo()
            .AddItem("药品简称")
            .AddItem("药品名称")

        End With
        C1Combo1.Width = 124
        C1Combo1.DropDownWidth = 124

    End Sub
    Private Sub Init_Data()
        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "Select *,datediff(D,getdate(),Yp_Yxq) as Px,case  when CAST(datediff(D,getdate(),Yp_Yxq) AS varchar(50))<=0 then '已过期' else CAST(datediff(D,getdate(),Yp_Yxq) AS varchar(50)) end as Xq_Ts From V_Ypkc where Yk_Sl>0 And datediff(D,getdate(),Yp_Yxq)<=270 and Yy_Code='" & HisVar.HisVar.WsyCode & "' ", "警戒线", True)
        With C1TrueDBGrid1
            My_Cm = CType(BindingContext(My_Dataset, "警戒线"), CurrencyManager)
            .SetDataBinding(My_Dataset, "警戒线", True)

            My_View = My_Cm.List

            My_View.Sort = "Px Asc"
        End With

    End Sub

    Private Sub C1TextBox1_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1TextBox1.TextChanged
        My_View = My_Cm.List

        Dim V_Sort As String = ""
        Dim V_RowFilter As String = ""

        If Trim(Me.C1TextBox1.Text & "") = "" Then
            V_RowFilter = ""
        Else
            Select Case Me.C1Combo1.Text
                Case "药品简称"
                    V_RowFilter = "Yp_Jc like '*" & Trim(C1TextBox1.Text) & "*'"
                Case "药品名称"
                    V_RowFilter = "Yp_Name Like '*" & Trim(C1TextBox1.Text) & "*'"
                Case "药品编码"
                    V_RowFilter = "Xx_Code like '" & Trim(C1TextBox1.Text) & "*'"
            End Select
        End If
        My_View.RowFilter = V_RowFilter
    End Sub
    Private Sub C1Combo1_Close(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Combo1.Close
        C1TextBox1.Select()
        Select Case Mid(Me.C1Combo1.Text, 3)
            Case "名称"
                InputLanguage.CurrentInputLanguage = HisVar.HisVar.InputCode
            Case "编码"
                InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages(0)
            Case "简称"
                InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages(0)
        End Select
    End Sub


    Private Sub C1TrueDBGrid1_OwnerDrawCell(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.OwnerDrawCellEventArgs) Handles C1TrueDBGrid1.OwnerDrawCell
        If e.Col = 8 Then
            If e.Text = "已过期" Then
                e.Style.ForeColor = Color.Blue
            ElseIf e.Text > 0 And e.Text < 30 Then
                e.Style.ForeColor = Color.Red
            End If
        End If
    End Sub

End Class