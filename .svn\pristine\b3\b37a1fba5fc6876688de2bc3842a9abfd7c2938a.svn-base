﻿/**  版本信息模板在安装目录下，可自行修改。
* B_Materials_Check1.cs
*
* 功 能： N/A
* 类 名： B_Materials_Check1
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016-11-26 11:27:50   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Collections.Generic;
using ModelOld;
namespace BLLOld
{
	/// <summary>
	/// 物资盘点主表
	/// </summary>
	public partial class B_Materials_Check1
	{
		private readonly DAL.D_Materials_Check1 dal=new DAL.D_Materials_Check1();
		public B_Materials_Check1()
		{}
		#region  BasicMethod
        public string MaxCode(string date)
        {
            return dal.MaxCode(date);
        }
        public double GetSumMoeny(string strWhere)
        {
            return dal.GetSumMoney(strWhere );
        }
		/// <summary>
		/// 是否存在该记录
		/// </summary>
        /// 
		public bool Exists(string M_Check_Code)
		{
			return dal.Exists(M_Check_Code);
		}
        public DataSet GetHzList(string strWhere)
        {
            return dal.GetHzList(strWhere);
        }
		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ModelOld.M_Materials_Check1 model)
		{
			return dal.Add(model);
		}

		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ModelOld.M_Materials_Check1 model)
		{
			return dal.Update(model);
		}


        /// <summary>
        /// 单据完成,库存表插入新的记录
        /// </summary>
        public bool Complete(ModelOld.M_Materials_Check1 model)
        {
            return dal.Complete(model);
        }

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string M_Check_Code)
		{
			
			return dal.Delete(M_Check_Code);
		}
		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool DeleteList(string M_Check_Codelist )
		{
			return dal.DeleteList(M_Check_Codelist );
		}

		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Materials_Check1 GetModel(string M_Check_Code)
		{
			
			return dal.GetModel(M_Check_Code);
		}

        ///// <summary>
        ///// 得到一个对象实体，从缓存中
        ///// </summary>
        //public ModelOld.M_Materials_Check1 GetModelByCache(string M_Check_Code)
        //{
			
        //    string CacheKey = "M_Materials_Check1Model-" + M_Check_Code;
        //    object objModel = Common.DataCache.GetCache(CacheKey);
        //    if (objModel == null)
        //    {
        //        try
        //        {
        //            objModel = dal.GetModel(M_Check_Code);
        //            if (objModel != null)
        //            {
        //                int ModelCache = Common.ConfigHelper.GetConfigInt("ModelCache");
        //                Common.DataCache.SetCache(CacheKey, objModel, DateTime.Now.AddMinutes(ModelCache), TimeSpan.Zero);
        //            }
        //        }
        //        catch{}
        //    }
        //    return (ModelOld.M_Materials_Check1)objModel;
        //}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			return dal.GetList(strWhere);
		}
		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			return dal.GetList(Top,strWhere,filedOrder);
		}
		/// <summary>
		/// 获得数据列表
		/// </summary>
		public List<ModelOld.M_Materials_Check1> GetModelList(string strWhere)
		{
			DataSet ds = dal.GetList(strWhere);
			return DataTableToList(ds.Tables[0]);
		}
		/// <summary>
		/// 获得数据列表
		/// </summary>
		public List<ModelOld.M_Materials_Check1> DataTableToList(DataTable dt)
		{
			List<ModelOld.M_Materials_Check1> modelList = new List<ModelOld.M_Materials_Check1>();
			int rowsCount = dt.Rows.Count;
			if (rowsCount > 0)
			{
				ModelOld.M_Materials_Check1 model;
				for (int n = 0; n < rowsCount; n++)
				{
					model = dal.DataRowToModel(dt.Rows[n]);
					if (model != null)
					{
						modelList.Add(model);
					}
				}
			}
			return modelList;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetAllList()
		{
			return GetList("");
		}

		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			return dal.GetRecordCount(strWhere);
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			return dal.GetListByPage( strWhere,  orderby,  startIndex,  endIndex);
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		//public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		//{
			//return dal.GetList(PageSize,PageIndex,strWhere);
		//}

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

