﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class Xs_Zy_Yj11
    Inherits HisControl.BaseForm

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(Xs_Zy_Yj11))
        Me.T_Line1 = New System.Windows.Forms.Label()
        Me.T_Line2 = New System.Windows.Forms.Label()
        Me.C1CommandHolder1 = New C1.Win.C1Command.C1CommandHolder()
        Me.Comm1 = New C1.Win.C1Command.C1Command()
        Me.Comm3 = New C1.Win.C1Command.C1Command()
        Me.Comm4 = New C1.Win.C1Command.C1Command()
        Me.C1CommandControl1 = New C1.Win.C1Command.C1CommandControl()
        Me.RadioButton1 = New System.Windows.Forms.RadioButton()
        Me.C1CommandControl2 = New C1.Win.C1Command.C1CommandControl()
        Me.RadioButton2 = New System.Windows.Forms.RadioButton()
        Me.Link1 = New C1.Win.C1Command.C1CommandLink()
        Me.ToolBar1 = New C1.Win.C1Command.C1ToolBar()
        Me.Link3 = New C1.Win.C1Command.C1CommandLink()
        Me.C1CommandLink1 = New C1.Win.C1Command.C1CommandLink()
        Me.C1CommandLink3 = New C1.Win.C1Command.C1CommandLink()
        Me.C1CommandLink2 = New C1.Win.C1Command.C1CommandLink()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.C1DateEdit2 = New C1.Win.C1Input.C1DateEdit()
        Me.T_Label2 = New C1.Win.C1Input.C1Label()
        Me.T_Label = New C1.Win.C1Input.C1Label()
        Me.MyGrid1 = New CustomControl.MyGrid()
        CType(Me.C1CommandHolder1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.ToolBar1.SuspendLayout()
        Me.Panel1.SuspendLayout()
        CType(Me.C1DateEdit2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T_Label2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T_Label, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'T_Line1
        '
        Me.T_Line1.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.T_Line1.Location = New System.Drawing.Point(364, 1)
        Me.T_Line1.Name = "T_Line1"
        Me.T_Line1.Size = New System.Drawing.Size(2, 26)
        Me.T_Line1.TabIndex = 4
        Me.T_Line1.Text = "Label1"
        '
        'T_Line2
        '
        Me.T_Line2.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.T_Line2.Location = New System.Drawing.Point(552, 1)
        Me.T_Line2.Name = "T_Line2"
        Me.T_Line2.Size = New System.Drawing.Size(2, 26)
        Me.T_Line2.TabIndex = 6
        Me.T_Line2.Text = "Label2"
        '
        'C1CommandHolder1
        '
        Me.C1CommandHolder1.Commands.Add(Me.Comm1)
        Me.C1CommandHolder1.Commands.Add(Me.Comm3)
        Me.C1CommandHolder1.Commands.Add(Me.Comm4)
        Me.C1CommandHolder1.Commands.Add(Me.C1CommandControl1)
        Me.C1CommandHolder1.Commands.Add(Me.C1CommandControl2)
        Me.C1CommandHolder1.Owner = Me
        '
        'Comm1
        '
        Me.Comm1.Image = CType(resources.GetObject("Comm1.Image"), System.Drawing.Image)
        Me.Comm1.Name = "Comm1"
        Me.Comm1.Shortcut = System.Windows.Forms.Shortcut.F2
        Me.Comm1.ShortcutText = ""
        Me.Comm1.Text = "增加"
        Me.Comm1.ToolTipText = "增加记录"
        '
        'Comm3
        '
        Me.Comm3.Image = CType(resources.GetObject("Comm3.Image"), System.Drawing.Image)
        Me.Comm3.Name = "Comm3"
        Me.Comm3.Shortcut = System.Windows.Forms.Shortcut.F4
        Me.Comm3.ShortcutText = ""
        Me.Comm3.Text = "更新"
        Me.Comm3.ToolTipText = "更新记录"
        '
        'Comm4
        '
        Me.Comm4.Image = CType(resources.GetObject("Comm4.Image"), System.Drawing.Image)
        Me.Comm4.Name = "Comm4"
        Me.Comm4.ShortcutText = ""
        Me.Comm4.Text = "打印"
        '
        'C1CommandControl1
        '
        Me.C1CommandControl1.Control = Me.RadioButton1
        Me.C1CommandControl1.Name = "C1CommandControl1"
        Me.C1CommandControl1.ShortcutText = ""
        '
        'RadioButton1
        '
        Me.RadioButton1.AutoSize = True
        Me.RadioButton1.BackColor = System.Drawing.SystemColors.Control
        Me.RadioButton1.Location = New System.Drawing.Point(166, 3)
        Me.RadioButton1.Name = "RadioButton1"
        Me.RadioButton1.Size = New System.Drawing.Size(83, 16)
        Me.RadioButton1.TabIndex = 17
        Me.RadioButton1.Text = "套打押金单"
        Me.RadioButton1.UseVisualStyleBackColor = False
        '
        'C1CommandControl2
        '
        Me.C1CommandControl2.Control = Me.RadioButton2
        Me.C1CommandControl2.Name = "C1CommandControl2"
        Me.C1CommandControl2.ShortcutText = ""
        '
        'RadioButton2
        '
        Me.RadioButton2.AutoSize = True
        Me.RadioButton2.BackColor = System.Drawing.SystemColors.Control
        Me.RadioButton2.Checked = True
        Me.RadioButton2.Location = New System.Drawing.Point(251, 3)
        Me.RadioButton2.Name = "RadioButton2"
        Me.RadioButton2.Size = New System.Drawing.Size(83, 16)
        Me.RadioButton2.TabIndex = 18
        Me.RadioButton2.TabStop = True
        Me.RadioButton2.Text = "自带押金单"
        Me.RadioButton2.UseVisualStyleBackColor = False
        '
        'Link1
        '
        Me.Link1.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image), C1.Win.C1Command.ButtonLookFlags)
        Me.Link1.Command = Me.Comm1
        '
        'ToolBar1
        '
        Me.ToolBar1.AccessibleName = "Tool Bar"
        Me.ToolBar1.CommandHolder = Nothing
        Me.ToolBar1.CommandLinks.AddRange(New C1.Win.C1Command.C1CommandLink() {Me.Link1, Me.Link3, Me.C1CommandLink1, Me.C1CommandLink3, Me.C1CommandLink2})
        Me.ToolBar1.Controls.Add(Me.RadioButton2)
        Me.ToolBar1.Controls.Add(Me.RadioButton1)
        Me.ToolBar1.Location = New System.Drawing.Point(4, 0)
        Me.ToolBar1.MinButtonSize = 22
        Me.ToolBar1.Movable = False
        Me.ToolBar1.Name = "ToolBar1"
        Me.ToolBar1.Size = New System.Drawing.Size(335, 22)
        Me.ToolBar1.Text = "C1ToolBar1"
        Me.ToolBar1.VisualStyle = C1.Win.C1Command.VisualStyle.Classic
        Me.ToolBar1.VisualStyleBase = C1.Win.C1Command.VisualStyle.Classic
        '
        'Link3
        '
        Me.Link3.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image), C1.Win.C1Command.ButtonLookFlags)
        Me.Link3.Command = Me.Comm3
        Me.Link3.SortOrder = 1
        '
        'C1CommandLink1
        '
        Me.C1CommandLink1.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image), C1.Win.C1Command.ButtonLookFlags)
        Me.C1CommandLink1.Command = Me.Comm4
        Me.C1CommandLink1.SortOrder = 2
        '
        'C1CommandLink3
        '
        Me.C1CommandLink3.Command = Me.C1CommandControl1
        Me.C1CommandLink3.SortOrder = 3
        '
        'C1CommandLink2
        '
        Me.C1CommandLink2.Command = Me.C1CommandControl2
        Me.C1CommandLink2.SortOrder = 4
        '
        'Panel1
        '
        Me.Panel1.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Panel1.Controls.Add(Me.C1DateEdit2)
        Me.Panel1.Controls.Add(Me.T_Label2)
        Me.Panel1.Controls.Add(Me.ToolBar1)
        Me.Panel1.Controls.Add(Me.T_Line1)
        Me.Panel1.Controls.Add(Me.T_Line2)
        Me.Panel1.Controls.Add(Me.T_Label)
        Me.Panel1.Dock = System.Windows.Forms.DockStyle.Top
        Me.Panel1.Location = New System.Drawing.Point(0, 0)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(816, 26)
        Me.Panel1.TabIndex = 11
        '
        'C1DateEdit2
        '
        Me.C1DateEdit2.AcceptsTab = True
        Me.C1DateEdit2.AutoOpen = False
        Me.C1DateEdit2.AutoSize = False
        Me.C1DateEdit2.BorderStyle = System.Windows.Forms.BorderStyle.None
        '
        '
        '
        Me.C1DateEdit2.Calendar.ClearText = "&C清空"
        Me.C1DateEdit2.Calendar.DayNameLength = 1
        Me.C1DateEdit2.Calendar.ShowToday = True
        Me.C1DateEdit2.Calendar.ShowWeekNumbers = True
        Me.C1DateEdit2.Calendar.TodayText = "&今天"
        Me.C1DateEdit2.ImagePadding = New System.Windows.Forms.Padding(0)
        Me.C1DateEdit2.Location = New System.Drawing.Point(463, 3)
        Me.C1DateEdit2.Name = "C1DateEdit2"
        Me.C1DateEdit2.Size = New System.Drawing.Size(83, 16)
        Me.C1DateEdit2.TabIndex = 14
        Me.C1DateEdit2.Tag = Nothing
        Me.C1DateEdit2.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.C1DateEdit2.VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.UpDown
        '
        'T_Label2
        '
        Me.T_Label2.AutoSize = True
        Me.T_Label2.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.T_Label2.ForeColor = System.Drawing.Color.DarkRed
        Me.T_Label2.Location = New System.Drawing.Point(398, 5)
        Me.T_Label2.Name = "T_Label2"
        Me.T_Label2.Size = New System.Drawing.Size(53, 12)
        Me.T_Label2.TabIndex = 15
        Me.T_Label2.Tag = Nothing
        Me.T_Label2.Text = "缴费日期"
        Me.T_Label2.TextDetached = True
        '
        'T_Label
        '
        Me.T_Label.AutoSize = True
        Me.T_Label.BackColor = System.Drawing.SystemColors.Control
        Me.T_Label.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.T_Label.ForeColor = System.Drawing.Color.Maroon
        Me.T_Label.Location = New System.Drawing.Point(560, 3)
        Me.T_Label.Name = "T_Label"
        Me.T_Label.Size = New System.Drawing.Size(47, 12)
        Me.T_Label.TabIndex = 2
        Me.T_Label.Tag = Nothing
        Me.T_Label.Text = "T_Label"
        Me.T_Label.TextAlign = System.Drawing.ContentAlignment.BottomLeft
        Me.T_Label.TextDetached = True
        Me.T_Label.TrimStart = True
        '
        'MyGrid1
        '
        Me.MyGrid1.CanCustomCol = False
        Me.MyGrid1.Col = 0
        Me.MyGrid1.ColumnFooters = False
        Me.MyGrid1.DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveRight
        Me.MyGrid1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.MyGrid1.FetchRowStyles = False
        Me.MyGrid1.Location = New System.Drawing.Point(0, 26)
        Me.MyGrid1.Margin = New System.Windows.Forms.Padding(0)
        Me.MyGrid1.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.MyGrid1.Name = "MyGrid1"
        Me.MyGrid1.Size = New System.Drawing.Size(816, 462)
        Me.MyGrid1.TabAction = C1.Win.C1TrueDBGrid.TabActionEnum.ControlNavigation
        Me.MyGrid1.TabIndex = 12
        Me.MyGrid1.Xmlpath = Nothing
        '
        'Xs_Zy_Yj11
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(816, 488)
        Me.Controls.Add(Me.MyGrid1)
        Me.Controls.Add(Me.Panel1)
        Me.Icon = CType(resources.GetObject("$this.Icon"), System.Drawing.Icon)
        Me.MinimizeBox = False
        Me.Name = "Xs_Zy_Yj11"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "缴纳押金"
        CType(Me.C1CommandHolder1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ToolBar1.ResumeLayout(False)
        Me.ToolBar1.PerformLayout()
        Me.Panel1.ResumeLayout(False)
        Me.Panel1.PerformLayout()
        CType(Me.C1DateEdit2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T_Label2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T_Label, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents T_Line1 As System.Windows.Forms.Label
    Friend WithEvents T_Line2 As System.Windows.Forms.Label
    Friend WithEvents C1CommandHolder1 As C1.Win.C1Command.C1CommandHolder
    Friend WithEvents Comm1 As C1.Win.C1Command.C1Command
    Friend WithEvents Comm3 As C1.Win.C1Command.C1Command
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents ToolBar1 As C1.Win.C1Command.C1ToolBar
    Friend WithEvents Link1 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link3 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents T_Label As C1.Win.C1Input.C1Label
    Friend WithEvents C1DateEdit2 As C1.Win.C1Input.C1DateEdit
    Friend WithEvents T_Label2 As C1.Win.C1Input.C1Label
    Friend WithEvents Comm4 As C1.Win.C1Command.C1Command
    Friend WithEvents C1CommandLink1 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents C1CommandControl1 As C1.Win.C1Command.C1CommandControl
    Friend WithEvents RadioButton1 As System.Windows.Forms.RadioButton
    Friend WithEvents C1CommandLink3 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents C1CommandControl2 As C1.Win.C1Command.C1CommandControl
    Friend WithEvents RadioButton2 As System.Windows.Forms.RadioButton
    Friend WithEvents C1CommandLink2 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents MyGrid1 As CustomControl.MyGrid
End Class
