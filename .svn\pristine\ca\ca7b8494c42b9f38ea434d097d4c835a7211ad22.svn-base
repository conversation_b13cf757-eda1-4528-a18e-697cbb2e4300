﻿Imports BaseClass
Imports System.Windows.Forms

Public Class MaterialsOtherInMx

    Dim Rinsert As Boolean
    Dim Rrow As DataRow
    Dim Rtable As DataTable
    Dim Rrc As C_RowChange
    Dim B_Materials_Stock As New BLLOld.B_Materials_Stock

    Dim Materials_Code As String
    Dim MaterialsLot As String

    Public Sub New(ByVal tinsert As Boolean, ByVal trow As DataRow, ByVal ttable As DataTable, ByRef trc As C_RowChange)

        ' 此调用是设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Rinsert = tinsert
        Rrow = trow
        Rtable = ttable
        Rrc = trc

    End Sub
    Private Sub MaterialsOtherInMx_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Call Form_Init()
        If Rinsert Then Call Data_Clear() Else Data_Show(Rrow)
    End Sub

#Region "窗体_初始化"

    Private Sub Form_Init()

        '物资
        Dim MaterialsBll As New BLLOld.B_Materials_Dict
        With Materials_DtCom
            .DataView = MaterialsBll.GetList("IsUse='1'").Tables(0).DefaultView
            .Init_Colum("Materials_Py", "物资简称", 80, "左")
            .Init_Colum("Materials_Name", "物资名称", 150, "左")
            .Init_Colum("Materials_Code", "编码", 0, "左")
            .Init_Colum("Materials_Wb", "--", 0, "左")
            .Init_Colum("Materials_Spec", "规格", 80, "左")
            .Init_Colum("MateManu_Name", "生产厂家", 100, "左")
            .Init_Colum("Pack_Unit", "包装单位", 0, "左")
            .Init_Colum("Bulk_Unit", "散装单位", 0, "左")
            .Init_Colum("Convert_Ratio", "拆分比例", 0, "左")
            .Init_Colum("IsUse", "--", 0, "左")
            .DisplayMember = "Materials_Name"
            .ValueMember = "Materials_Code"
            .DroupDownWidth = 510
            .MaxDropDownItems = 15
            .SelectedIndex = -1
            .RowFilterTextNull = ""
            .RowFilterNotTextNull = "Materials_Py+Materials_Name+Materials_Spec"
        End With

        '有效期
        With Expiry_Date
            .CustomFormat = "yyyy-MM-dd HH:mm"
            .EditFormat = "yyyy-MM-dd"
            .DisplayFormat = "yyyy-MM-dd"
            .Value = Format(Now, "yyyy-MM-dd")
        End With
        PriceMoney_Num.CustomFormat = "##0.00##"
        PutInMoney_Num.CustomFormat = "##0.00##"
        PutInMoney_Num.Enabled = False

        GG_MyTextBox1.Enabled = False
        SCCJ_MyTextBox2.Enabled = False

        Expiry_Date.Enabled = False
        PriceMoney_Num.Enabled = False
    End Sub

    Private Sub Data_Clear()

        Rinsert = True

        Materials_DtCom.Text = ""
        GG_MyTextBox1.Text = ""
        SCCJ_MyTextBox2.Text = ""
        Lot_DtCom.SelectedIndex = -1
        Expiry_Date.Value = Format(Now, "yyyy-MM-dd")
        PutInNo_Num.Value = 0

        PriceMoney_Num.Value = 0
        PutInMoney_Num.Value = 0
        Memo_Text.Text = ""
        Materials_Code = ""
        MaterialsLot = ""
        Materials_DtCom.Select()
    End Sub


    Private Sub Data_Show(ByVal _row As DataRow)
        Rinsert = False
        With _row
            Materials_DtCom.SelectedValue = .Item("Materials_Code")
            Lot_DtCom.Text = .Item("MaterialsLot")
            Expiry_Date.Value = .Item("MaterialsExpiryDate")
            PutInNo_Num.Value = .Item("M_OtherIn_Num")
            PriceMoney_Num.Value = .Item("M_OtherIn_Price")
            PutInMoney_Num.Value = .Item("M_OtherIn_Money")
            Memo_Text.Text = .Item("M_OtherInDetail_Memo")
            Materials_Code = .Item("Materials_Code")
            MaterialsLot = .Item("MaterialsLot")
        End With
        Materials_DtCom.Select()
    End Sub

#End Region

    Private Sub Cancel_Btn_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Cancel_Btn.Click
        Me.Close()
    End Sub

    Private Sub Save_Btn_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Save_Btn.Click
        If Not Cb_Check() Then Exit Sub

        If Rinsert = True Then     '增加记录
            Call Data_Add()
        Else                       '编辑记录
            Call Data_Edit()
        End If
    End Sub
#Region "事件"

    Private Sub Materials_DtCom_RowChange(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Materials_DtCom.RowChange

        GG_MyTextBox1.Text = Materials_DtCom.Columns("Materials_Spec").Value
        SCCJ_MyTextBox2.Text = Materials_DtCom.Columns("MateManu_Name").Value

        '物资批号
        With Lot_DtCom
            .Text = ""
            .DataView = B_Materials_Stock.GetMaterialsLot(Materials_DtCom.SelectedValue).Tables(0).DefaultView
            .Init_Colum("MaterialsLot", "批号", 80, "左")
            .Init_Colum("MaterialsExpiryDate", "有效期", 80, "左")
            .Init_Colum("MaterialsStore_Price", "库存单价", 0, "左")
            .DisplayMember = "MaterialsLot"
            .ValueMember = "MaterialsLot"
            .RowFilterTextNull = ""
            .RowFilterNotTextNull = "MaterialsLot"
            .DroupDownWidth = .Width
            .MaxDropDownItems = 15
            .SelectedIndex = -1
            .AllowNew = True
        End With
    End Sub

    Private Sub Lot_DtCom_RowChange(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Lot_DtCom.RowChange
        If Lot_DtCom.WillChangeToValue = "" Then
            Expiry_Date.Value = ""
            PriceMoney_Num.Value = 0
        Else
            Expiry_Date.Value = Lot_DtCom.Columns("MaterialsExpiryDate").Value
            PriceMoney_Num.Value = Lot_DtCom.Columns("MaterialsStore_Price").Value
        End If

    End Sub

    Private Sub PutInNo_Num_ValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles PutInNo_Num.ValueChanged
        PutInMoney_Num.Value = PutInNo_Num.Value * PriceMoney_Num.Value
    End Sub

    Private Sub PriceMoney_Num_ValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles PriceMoney_Num.ValueChanged
        PutInMoney_Num.Value = PutInNo_Num.Value * PriceMoney_Num.Value
    End Sub


    'Private Sub Lot_DtCom_Validating(ByVal sender As System.Object, ByVal e As System.ComponentModel.CancelEventArgs) Handles Lot_DtCom.Validating
    '    Dim V_Code As String = Me.Lot_DtCom.WillChangeToValue
    '    If V_Code & "" = "" Then                  '编码不存在
    '        P_Scph_State(True)
    '    Else                                                            '编码存在
    '        P_Scph_State(False)
    '    End If
    'End Sub

#End Region

#Region "自定义函数"
    '从表数据验证
    Private Function Cb_Check() As Boolean
        If Materials_DtCom.SelectedValue Is Nothing Then
            Beep()
            MsgBox("请添加物资！", MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            Materials_DtCom.Select()
            Return False
        End If

        If Lot_DtCom.SelectedValue Is Nothing Then
            Beep()
            MsgBox("请添加批号！", MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            Lot_DtCom.Select()
            Return False
        End If

        'If Trim(Expiry_Date.Value & "") = "" Then
        '    Beep()
        '    MsgBox("有效期输入有误!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
        '    Expiry_Date.Select()
        '    Return False
        'End If

        If PutInNo_Num.Value <= 0 Then
            Beep()
            MsgBox("请添加入库数量！", MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            PutInNo_Num.Select()
            Return False
        End If

        If PriceMoney_Num.Value <= 0 Then
            Beep()
            MsgBox("请添加单价！", MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            PriceMoney_Num.Select()
            Return False
        End If

        If Materials_Code <> Materials_DtCom.SelectedValue And MaterialsLot <> Lot_DtCom.Text.Trim And Rtable.Select("Materials_Code = '" & Materials_DtCom.SelectedValue & "' and MaterialsLot = '" & Lot_DtCom.Text.Trim & "'").Length > 0 Then
            Beep()
            MsgBox("物资:" & Materials_DtCom.Text.Trim & " 批号:" & Lot_DtCom.Text.Trim & "不能重复录入！", MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            Materials_DtCom.Select()
            Return False
        End If

        Return True
    End Function

    Private Sub Data_Add()
        Dim My_NewRow As DataRow = Rtable.NewRow
        With My_NewRow
            .Item("Materials_Code") = Materials_DtCom.SelectedValue
            .Item("Materials_Name") = Materials_DtCom.Text
            .Item("MaterialsLot") = Lot_DtCom.Text
            .Item("MaterialsExpiryDate") = Format(Expiry_Date.Value, "yyyy-MM-dd 00:00:00")
            .Item("M_OtherIn_Num") = PutInNo_Num.Value
            .Item("M_OtherIn_Price") = PriceMoney_Num.Value
            .Item("M_OtherIn_Money") = PutInMoney_Num.Value
            .Item("M_OtherInDetail_Memo") = Memo_Text.Text
        End With

        '数据保存
        Try
            Rtable.Rows.Add(My_NewRow)
            Rrc.AddChange("")
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            Exit Sub
        Finally
            Materials_DtCom.Select()
        End Try

        '数据清空
        Call Data_Clear()

    End Sub

    Private Sub Data_Edit()
        Try
            With Rrow
                .BeginEdit()
                .Item("Materials_Code") = Materials_DtCom.SelectedValue
                .Item("Materials_Name") = Materials_DtCom.Text
                .Item("MaterialsLot") = Lot_DtCom.Text
                .Item("MaterialsExpiryDate") = Format(Expiry_Date.Value, "yyyy-MM-dd 00:00:00")
                .Item("M_OtherIn_Num") = PutInNo_Num.Value
                .Item("M_OtherIn_Price") = PriceMoney_Num.Value
                .Item("M_OtherIn_Money") = PutInMoney_Num.Value
                .Item("M_OtherInDetail_Memo") = Memo_Text.Text
                .EndEdit()
            End With
            Rrc.AddChange("")
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Critical, "提示:")
            Rrow.CancelEdit()
            Exit Sub
        Finally
            Materials_DtCom.Select()
        End Try

    End Sub

    'Private Sub P_Scph_State(ByVal V_NewPh As Boolean)
    '    If V_NewPh = False Then
    '        Expiry_Date.Enabled = False
    '        PriceMoney_Num.Enabled = False
    '    Else
    '        Expiry_Date.Enabled = True
    '        PriceMoney_Num.Enabled = True
    '    End If
    'End Sub
#End Region



End Class
