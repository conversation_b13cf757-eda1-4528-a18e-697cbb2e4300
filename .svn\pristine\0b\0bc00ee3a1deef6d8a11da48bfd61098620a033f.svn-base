﻿Imports System.Windows.Forms
Imports System.Text
Imports System.IO
Imports System.Data.OleDb

Public Class MbExport

#Region "变量初始化"

    Dim V_Finish As Boolean = False             '初始化完成
    Dim My_Table As New DataTable            '药品字典
    Dim My_Cm As CurrencyManager             '同步指针
    Dim m_Rc As New BaseClass.C_RowChange
    Dim My_Row As DataRow                    '当 前 行
    Dim My_View As New DataView                 '视图
    Dim completeFlag As Boolean
    Dim nodeFind As TreeNode
    Public Shared vMblbCode As String
    Dim vSelectedNodeTag As String
    Dim vSelectedNodeText As String

    Dim BllEmr_Mblb As New BLLOld.B_Emr_Mblb
    Dim BllEmr_Mb As New BLLOld.B_Emr_Mb
    Dim ModelEmr_Mblb As New ModelOld.M_Emr_Mblb
    Dim ModelEmr_Mb As New ModelOld.M_Emr_Mb




    Private oleDal As New oledbDalHelper.dzhOleDbDal

#End Region

#Region "传入参数"
    Private rState As String
#End Region

    Public Sub New(ByVal rRc As BaseClass.C_RowChange, ByVal tState As String)

        ' 此调用是设计器所必需的。
        InitializeComponent()
        rState = tState
        m_Rc = rRc
        ' 在 InitializeComponent() 调用之后添加任何初始化。

    End Sub

    Private Sub Zd_Cl21_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Me.Visible = False
        Call Form_Init()
        Me.Visible = True
        AddHandler m_Rc.DrDcEvent, AddressOf Tree_Edit
    End Sub

    Private Sub MblbTree_FormClosing(sender As Object, e As System.Windows.Forms.FormClosingEventArgs) Handles Me.FormClosing
        RemoveHandler m_Rc.DrDcEvent, AddressOf Tree_Edit
    End Sub

#Region "窗体初始化"

    Private Sub Form_Init()
        Me.Text = rState
        'Treeview初始化
        With Me.TreeView1
            .Nodes.Clear()
            .FullRowSelect = True
            .HideSelection = False
            .HotTracking = True
            .Scrollable = True
            .ShowRootLines = False
            .Sorted = False
        End With

        C1CommandLink1.Text = rState
        Comm4.Text = rState

        If rState = "导入" Then
            Comm4.Image = My.Resources.导入
            Dim fileOpen As New OpenFileDialog
            With fileOpen
                .InitialDirectory = Application.StartupPath
                .Filter = "ztemr文件|*.ztemr|所有文件|*.*"
                .Title = "打开文件"
                If .ShowDialog = Windows.Forms.DialogResult.OK Then
                    oleDal.ConnectionString = "Provider=Microsoft.Jet.OLEDB.4.0;Data Source=" & fileOpen.FileName & ";Persist Security Info=True"
                Else
                    Me.Close()
                End If
            End With
            TableLayoutPanel1.ColumnStyles(0).Width = 0
            Call Init_TDBGrid1()
            Call P_Init_Data1()
            V_Finish = True
        Else
            Comm4.Image = My.Resources.导出
            Call Init_TDBGrid1()
            Init_Tree()
        End If





    End Sub

    Private Sub Init_TDBGrid1()
        '初始化TDBGrid
        With MyGrid1
            .Clear()
            .Init_Column("模板类别编码", "Mblb_Code", "0", "中", "", False)
            .Init_Column("所属类别", "Mblb_Name", "200 ", "左", "", False)
            .Init_Column("模板编码", "Mb_Code", "0", "中", "", False)
            .Init_Column("模板名称", "Mb_Name", MyGrid1.Width - 285, "左", "", False)
            .Init_Column("选择", "isSelected", 45, "中", "check", True)
        End With
    End Sub

    Private Sub P_Init_Data1(Optional ByVal V_Mblb_Code As String = "")
        If rState = "导入" Then
            Dim strAccess As New StringBuilder
            strAccess.Append("SELECT  Emr_Mblb.Mblb_Name, Emr_Mblb.Mblb_Jc, Father_Code,Mb_Code,Emr_Mb.Mblb_Code,")
            strAccess.Append("Mb_Name,  Mb_Jc, Mb_Nr, Mb_Sex, isMust, isMulti, AgeLimit, isStandard, Ks_Code, Ys_Code, Mb_Memo")
            strAccess.Append(" FROM Emr_Mb left JOIN Emr_Mblb ON Emr_Mblb.Mblb_Code = Emr_Mb.Mblb_Code;")
            'If V_Mblb_Code <> "00000" Then
            '    strAccess.Append(" where  Emr_Mb.Mblb_Code='" & V_Mblb_Code & "'")
            'End If
            My_Table = oleDal.Query(strAccess.ToString()).Tables(0)
        Else
            If V_Mblb_Code = "00000" Then
                My_Table = BllEmr_Mb.GetListExport("").Tables(0)
            Else
                My_Table = BllEmr_Mb.GetListExport("Emr_Mb.Mblb_Code='" & V_Mblb_Code & "'").Tables(0)
            End If
        End If

        Dim dtCol As New DataColumn()
        dtCol.Caption = "选择"
        dtCol.ColumnName = "isSelected"
        dtCol.DataType = GetType(Boolean)
        dtCol.DefaultValue = False
        My_Table.Columns.Add(dtCol)
        If rState <> "导入" Then
            My_Table.PrimaryKey = New DataColumn() {My_Table.Columns("Mb_Code")}
        End If

        With MyGrid1
            My_Cm = CType(BindingContext(My_Table, ""), CurrencyManager)
            My_View = My_Cm.List
            .DataTable = My_Table
            T_Label.Text = "模板数量：" & MyGrid1.Splits(0).Rows.Count.ToString & ",选择数量：" & My_Table.Select("isSelected=true").Length.ToString()
        End With
        T_Textbox.Text = ""
    End Sub

#End Region

#Region "Tree"
    Private Sub Init_Tree()
        V_Finish = False
        '根节点

        TreeView1.Nodes.Clear()
        Dim My_Node As New TreeNode
        With My_Node
            .Tag = "00000"
            If Comm4.Text <> "导入" Then
                .Text = "模板类别" & "(" & BllEmr_Mb.GetRecordCount("") & ")"
                'Else
                '    .Text = "模板类别" & "(" & oleDal.GetSingle("SELECT count(1) FROM Emr_Mblb ") & ")"
            End If
            .ImageIndex = 0
            .SelectedImageIndex = 0
        End With
        TreeView1.Nodes.Add(My_Node)
        Product_Node(My_Node)
        '一级数据
        V_Finish = True
        With Me.TreeView1
            .SelectedNode = TreeView1.TopNode
            .SelectedNode.Expand()
            .Select()
        End With
    End Sub

    'Mblb_Code, Mblb_Name, Father_Code
    'Mblb_Code, Mb_Code, Mb_Name

    Private Sub Product_Node(ByVal FatherNode As TreeNode)
        Dim dt As DataTable
        If rState = "导入" Then
            'dt = oleDal.Query("SELECT Mblb_Code, Mblb_Name, Mblb_Jc, Father_Code FROM Emr_Mblb where Emr_Mblb.Father_Code='" & FatherNode.Tag & "'").Tables(0)
            'If dt.Rows.Count > 0 Then
            '    For Each row As DataRow In dt.Rows
            '        Dim My_Node As New TreeNode
            '        With My_Node
            '            .Tag = row("Mblb_Code").ToString
            '            .Text = row("Mblb_Name").ToString & "(" & oleDal.GetSingle("SELECT COUNT(1) FROM Emr_Mb  Where Mblb_Code='" & row("Mblb_Code").ToString.Trim & "'") & ")"
            '            .ImageIndex = 1
            '            .SelectedImageIndex = 2
            '        End With
            '        FatherNode.Nodes.Add(My_Node)
            '        Product_Node(My_Node)
            '    Next
            'End If
        Else
            dt = BllEmr_Mblb.GetList("Emr_Mblb.Father_Code='" & FatherNode.Tag & "'").Tables(0)
            If dt.Rows.Count > 0 Then
                For Each row As DataRow In dt.Rows
                    Dim My_Node As New TreeNode
                    With My_Node
                        .Tag = row("Mblb_Code").ToString
                        .Text = row("Mblb_Name").ToString & "(" & BllEmr_Mb.GetRecordCount("Mblb_Code='" & row("Mblb_Code").ToString.Trim & "'") & ")"
                        .ImageIndex = 1
                        .SelectedImageIndex = 2
                    End With
                    FatherNode.Nodes.Add(My_Node)
                    Product_Node(My_Node)
                Next
            End If
        End If


    End Sub


    Private Sub Tree_Edit(ByVal V_Key As String, ByVal V_Text As String, Optional ByVal InsertFlag As String = "")
        If rState = "导入" Then Exit Sub
        If InsertFlag = "dr" Then
            completeFlag = False
            Find_Node(V_Key, TreeView1.TopNode)
            P_Count()
        ElseIf InsertFlag = "update" Then
            completeFlag = False
            Find_Node(V_Key, TreeView1.TopNode)
            nodeFind.Text = V_Text
            If My_Table.Rows.Count > 0 Then
                For Each row In My_Table.Rows
                    row("Mblb_name") = Mid(V_Text, 1, InStr(V_Text, "(") - 1)
                Next
            End If

        Else
            completeFlag = False
            Find_Node(InsertFlag, TreeView1.TopNode)
            Dim My_Node As New TreeNode
            My_Node.Tag = V_Key
            My_Node.Text = V_Text
            My_Node.ImageIndex = 1
            My_Node.SelectedImageIndex = 2
            nodeFind.Nodes.Add(My_Node)
            vSelectedNodeTag = V_Key
            P_Count()
        End If

    End Sub


    Private Sub Find_Node(ByVal V_Key As String, ByVal node As TreeNode)

        If node.Tag = V_Key Then
            nodeFind = node
            vSelectedNodeTag = node.Tag
            completeFlag = True
            Exit Sub
        End If

        If node.Nodes.Count > 0 Then
            For Each My_Node As TreeNode In node.Nodes
                If completeFlag = True Then
                    Exit Sub
                End If

                If My_Node.Tag = V_Key Then
                    nodeFind = My_Node
                    vSelectedNodeTag = My_Node.Tag
                    completeFlag = True
                    Exit Sub
                End If

                Find_Node(V_Key, My_Node)
            Next
        End If
    End Sub


#End Region

#Region "控件__动作"

    Private Sub Comm_Click(ByVal sender As Object, ByVal e As C1.Win.C1Command.ClickEventArgs) Handles Comm4.Click, comm5.Click, comm全选.Click, comm反选.Click
        If sender.Text <> "全选" And sender.Text <> "反选" Then
            My_Table.AcceptChanges()
            If My_Table.Select("isSelected=true").Length = 0 Then
                MsgBox("未选择模板！", MsgBoxStyle.Critical, "提示")
                Exit Sub
            End If
        End If

        Select Case sender.text
            Case "导出"
                Dim fileOpen As New SaveFileDialog
                With fileOpen
                    .InitialDirectory = Environment.SpecialFolder.Desktop
                    .Filter = "ztemr文件|*.ztemr"
                    .Title = "打开文件"
                    .FileName = "Emrbak" & Date.Now.ToString("yyyyMMdd")
                    If .ShowDialog = Windows.Forms.DialogResult.OK Then
                        FileSystem.FileCopy(".\EmrMb.ztemr", fileOpen.FileName)
                        oleDal.ConnectionString = "Provider=Microsoft.Jet.OLEDB.4.0;Data Source=" & fileOpen.FileName & ";Persist Security Info=True"
                        Call DataDc()
                    End If
                End With

            Case "导入"
                vMblbCode = ""
                Dim frm As New MblbTree(m_Rc, "导入")
                If frm.ShowDialog() = Windows.Forms.DialogResult.OK Then
                    If Trim(vMblbCode & "") <> "" Then
                        Call DataDr()
                    End If
                End If
            Case "批量移动"
                My_Table.AcceptChanges()
                If My_Table.Select("isSelected=true").Length = 0 Then
                    MsgBox("未选择模板！", MsgBoxStyle.Critical, "提示")
                    Exit Sub
                End If
                Call DataMove()
            Case "全选", "反选"
                P_Select(sender.Text)
        End Select

    End Sub

    Private Sub MyGrid1_MouseClick(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles MyGrid1.MouseUp
        If e.Button = Windows.Forms.MouseButtons.Left Then
            My_Table.AcceptChanges()
            T_Label.Text = "模板数量：" & MyGrid1.Splits(0).Rows.Count.ToString & ",选择数量：" & My_Table.Select("isSelected=true").Length.ToString()
        End If
    End Sub

    Private Sub T_Combo_Close(ByVal sender As Object, ByVal e As System.EventArgs)
        T_Textbox.Select()
    End Sub

    Private Sub C1TextBox1_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles T_Textbox.TextChanged
        If V_Finish = True Then Call P_Filter()
    End Sub


    Private Sub TreeView1_AfterSelect(ByVal sender As System.Object, ByVal e As System.Windows.Forms.TreeViewEventArgs) Handles TreeView1.AfterSelect
        If V_Finish = False Then Exit Sub '     '初始化完成
        comm全选.Enabled = True
        comm反选.Enabled = True
        Call P_Init_Data1(Me.TreeView1.SelectedNode.Tag)

        vSelectedNodeTag = TreeView1.SelectedNode.Tag
        vSelectedNodeText = TreeView1.SelectedNode.Text
    End Sub


    Private Sub TreeView1_BeforeCollapse(ByVal sender As Object, ByVal e As System.Windows.Forms.TreeViewCancelEventArgs) Handles TreeView1.BeforeCollapse
        '如果为TopNode不进行折叠
        If e.Node.Tag = TreeView1.TopNode.Tag Then e.Cancel = True
    End Sub
#End Region

#Region "数据编辑"
    Private Sub DataDc()
        Try
            'Mb_Code, Mblb_Code, Mb_Name, Mb_Jc, Mb_Nr, Mb_Sex, isMust, isMulti,
            'AgeLimit, isStandard, Ks_Code, Ys_Code, Mb_Memo
            Dim arr As New ArrayList
            arr.Add("delete from  Emr_Mb;")
            arr.Add("delete from  Emr_Mblb;")

            Dim dt As DataTable = My_Table.Clone
            For Each row In My_Table.Select("isSelected=true")
                dt.Rows.Add(row.ItemArray)
            Next


            Dim view As DataView = dt.DefaultView
            Dim MblbTb As DataTable = view.ToTable(True, "Mblb_Code", "Mblb_Name", "Mblb_Jc", "Father_Code")

            For Each row In MblbTb.Rows
                arr.Add("insert into Emr_Mblb values('" & row("Mblb_Code") & "','" & row("Mblb_Name") & "','" & row("Mblb_Jc") &
                        "','" & row("Father_Code") & "');")
            Next
            oleDal.ExecuteOleDbTran(arr)

            For Each row In dt.Rows
                Dim accStr = "insert into Emr_Mb (Mb_Code, Mblb_Code, Mb_Name, Mb_Jc,Mb_Nr, Mb_Sex, isMust, isMulti, AgeLimit, " &
    "isStandard, Ks_Code, Ys_Code, Mb_Memo)values(@Mb_Code, @Mblb_Code, @Mb_Name, @Mb_Jc,@Mb_Nr, @Mb_Sex, @isMust, @isMulti, @AgeLimit, " &
    "@isStandard, @Ks_Code, @Ys_Code, @Mb_Memo);"

                Dim accPara(12) As OleDbParameter
                accPara(0) = New OleDbParameter("@Mb_Code", OleDbType.VarChar, 10)
                accPara(1) = New OleDbParameter("@Mblb_Code", OleDbType.VarChar, 5)
                accPara(2) = New OleDbParameter("@Mb_Name", OleDbType.VarChar, 50)
                accPara(3) = New OleDbParameter("@Mb_Jc", OleDbType.VarChar, 50)
                accPara(4) = New OleDbParameter("@Mb_Nr", OleDbType.LongVarBinary)
                accPara(5) = New OleDbParameter("@Mb_Sex", OleDbType.Char, 1)
                accPara(6) = New OleDbParameter("@isMust", OleDbType.Boolean, 1)
                accPara(7) = New OleDbParameter("@isMulti", OleDbType.Boolean, 1)
                accPara(8) = New OleDbParameter("@AgeLimit", OleDbType.Integer)
                accPara(9) = New OleDbParameter("@isStandard", OleDbType.Boolean, 1)
                accPara(10) = New OleDbParameter("@Ks_Code", OleDbType.Char, 6)
                accPara(11) = New OleDbParameter("@Ys_Code", OleDbType.Char, 7)
                accPara(12) = New OleDbParameter("@Mb_Memo", OleDbType.VarChar, 50)
                accPara(0).Value = row("Mb_Code")
                accPara(1).Value = row("Mblb_Code")
                accPara(2).Value = row("Mb_Name")
                accPara(3).Value = row("Mb_Jc")
                accPara(4).Value = row("Mb_Nr")
                accPara(5).Value = row("Mb_Sex")
                accPara(6).Value = row("isMust")
                accPara(7).Value = row("isMulti")
                accPara(8).Value = row("AgeLimit")
                accPara(9).Value = row("isStandard")
                accPara(10).Value = row("Ks_Code")
                accPara(11).Value = row("Ys_Code")
                accPara(12).Value = row("Mb_Memo")
                Dim s As Integer = oleDal.ExecuteOleDb(accStr, accPara)
            Next
            MsgBox("导出成功！", MsgBoxStyle.Information, "提示")
        Catch ex As Exception
            MsgBox(ex.ToString, MsgBoxStyle.Critical, "提示")
        End Try

    End Sub

    Private Sub DataDr()
        'Mb_Code, Mblb_Code, Mb_Name, Mb_Jc, Mb_Nr, Mb_Sex, isMust, isMulti,
        'AgeLimit, isStandard, Ks_Code, Ys_Code, Mb_Memo
        Try
            Dim dt As DataTable = My_Table.Clone
            For Each row In My_Table.Select("isSelected=true")
                dt.Rows.Add(row.ItemArray)
            Next

            Dim intExist As Integer = 0
            For Each My_NewRow In dt.Rows
                With ModelEmr_Mb
                    .Mblb_Code = vMblbCode
                    intExist = BllEmr_Mb.GetList("emr_mb.mblb_code='" & vMblbCode & "' and mb_name='" & My_NewRow.Item("Mb_Name") &
                                         "'").Tables(0).Rows.Count
                    If intExist > 0 Then
                        .Mb_Code = BllEmr_Mb.GetList("emr_mb.mblb_code='" & vMblbCode & "' and mb_name='" & My_NewRow.Item("Mb_Name") &
                                                   "'").Tables(0).Rows(0).Item("Mb_Code")
                    Else
                        .Mb_Code = BllEmr_Mb.MaxCode()
                    End If
                    .Mb_Name = My_NewRow.Item("Mb_Name")
                    .Mb_Jc = My_NewRow.Item("Mb_Jc")
                    If IsDBNull(My_NewRow.Item("Mb_Nr")) = True Then
                    Else
                        .Mb_Nr = My_NewRow.Item("Mb_Nr")
                    End If

                    .Mb_Sex = My_NewRow.Item("Mb_sex")
                    .isMust = My_NewRow.Item("isMust")
                    .isMulti = My_NewRow.Item("isMulti")
                    .AgeLimit = My_NewRow.Item("AgeLimit")
                    .isStandard = My_NewRow.Item("isStandard")
                    If IsDBNull(My_NewRow.Item("Ks_Code")) = True Then
                    Else
                        .Ks_Code = My_NewRow.Item("Ks_Code")
                    End If
                    If IsDBNull(My_NewRow.Item("Ys_Code")) = False Then
                        .Ys_Code = My_NewRow.Item("Ys_Code")
                    End If
                    If IsDBNull(My_NewRow.Item("Mb_Memo")) = False Then
                        .Mb_Memo = My_NewRow.Item("Mb_Memo")
                    End If

                End With
                If intExist = 0 Then
                    BllEmr_Mb.Add(ModelEmr_Mb)
                Else
                    BllEmr_Mb.Update(ModelEmr_Mb)
                End If
            Next
            Call m_Rc.DrDc(vMblbCode, "", "dr")
        Catch ex As Exception
            MsgBox(ex.ToString, MsgBoxStyle.Critical, "提示")
        End Try
    End Sub

    Private Sub DataMove()
        Dim frm As New MblbTree(m_Rc, "模板批量移动")

        If frm.ShowDialog = Windows.Forms.DialogResult.OK Then
            Try
                'Mb_Code, Mblb_Code, Mb_Name, Mb_Jc, Mb_Nr, Mb_Sex, isMust, isMulti,
                'AgeLimit, isStandard, Ks_Code, Ys_Code, Mb_Memo

                Dim vMblb_Code As String = EmrMb1.vSelectedNodeTag
                Dim MbList As New List(Of String)
                Dim dt As DataTable = My_Table.Clone
                My_Table.AcceptChanges()
                For Each row In My_Table.Select("isSelected=true")
                    If BllEmr_Mb.GetList("emr_mb.mblb_code='" & vMblb_Code & "' and mb_name='" & row("mb_name") & "'").Tables(0).Rows.Count = 0 Then
                        dt.Rows.Add(row.ItemArray)
                    End If
                Next

                For Each row In dt.Rows
                    MbList.Add(row("Mb_Code"))
                Next
                BllEmr_Mb.Update(vMblb_Code, MbList)

                Dim view As DataView = dt.DefaultView
                Dim MblbTb As DataTable = view.ToTable(True, "Mblb_Code", "Mblb_Name", "Mblb_Jc", "Father_Code")

                For Each row In MblbTb.Rows
                    'If row("mblb_code") <> vMblb_Code Then
                    EmrMb1.vSelectedNodeTag = row("Mblb_Code")
                    m_Rc.NodeAdd()
                    'End If
                Next
                EmrMb1.vSelectedNodeTag = vMblb_Code
                m_Rc.NodeAdd()
            Catch ex As Exception
                MsgBox(ex.ToString, MsgBoxStyle.Critical, "提示")
            End Try

            Me.Close()
        End If
    End Sub
#End Region

#Region "自定义函数"

    Private Sub P_Filter()
        Dim V_Str As String = ""
        If rState = "导入" Then
            V_Str = " Mb_Name Like '*" & Trim(T_Textbox.Text) & "*'"
        Else
            If TreeView1.SelectedNode.Tag <> "00000" Then
                V_Str = "Mblb_Code='" & TreeView1.SelectedNode.Tag & "' And Mb_Name Like '*" & Trim(T_Textbox.Text) & "*'"
            Else
                V_Str = " Mb_Name Like '*" & Trim(T_Textbox.Text) & "*'"
            End If
        End If
        My_View.Sort = "Mb_Name"
        My_View.RowFilter = V_Str
        MyGrid1.MoveFirst()
        ' T_Label.Text = "∑=" + MyGrid1.Splits(0).Rows.Count.ToString
        My_Table.AcceptChanges()
        T_Label.Text = "模板数量：" & MyGrid1.Splits(0).Rows.Count.ToString & ",选择数量：" & My_Table.Select("isSelected=true").Length.ToString()

    End Sub

    Private Sub P_Select(ByVal sel As String)
        If sel = "全选" Then
            For Each RowAllSelected In My_Table.Rows
                If RowAllSelected("isSelected") = False Then RowAllSelected("isSelected") = True
            Next
        Else
            For Each RowNotSelected In My_Table.Rows
                If RowNotSelected("isSelected") = True Then RowNotSelected("isSelected") = False
            Next
        End If
        My_Table.AcceptChanges()
        T_Label.Text = "模板数量：" & MyGrid1.Splits(0).Rows.Count.ToString & ",选择数量：" & My_Table.Select("isSelected=true").Length.ToString()
    End Sub


    Private Sub P_Count()
        Try
            TreeView1.TopNode.Text = "模板类别" & "(" & BllEmr_Mb.GetRecordCount("") & ")" '修改Treeview节点
            completeFlag = False
            Find_Node(vSelectedNodeTag, TreeView1.TopNode)
            If nodeFind.Tag <> "00000" Then
                '  TreeView1.SelectedNode.Text = Mid(TreeView1.SelectedNode.Text, 1, InStr(TreeView1.SelectedNode.Text, "(") - 1) & "(" & MyGrid1.Splits(0).Rows.Count & ")"
                nodeFind.Text = Mid(nodeFind.Text, 1, InStr(nodeFind.Text, "(") - 1) & "(" &
                    BllEmr_Mb.GetRecordCount("mblb_code='" & nodeFind.Tag & "'") & ")"
            End If
        Catch ex As Exception

        End Try

    End Sub

#End Region




End Class