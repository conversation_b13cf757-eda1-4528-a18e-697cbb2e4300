﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ERX.Model
{
    /// <summary>
    /// 电子处方药品目录查询
    /// </summary>
    public class MdlcircDrugQueryIn
    {
        public string fixmedinsCode { get; set; }
        public string medListCodg { get; set; }
        public string medListCodgs { get; set; }
        public string begntime { get; set; }
        public string endtime { get; set; }
        public string pageNum { get; set; }
        public string pageSize { get; set; }
       
    }
    public class MdlcircDrugQueryOut
    {
        public string total { get; set; }
        public string size { get; set; }
        public List<MdlcircDrugQueryOutList> list{ get; set; }
    }

    public class MdlcircDrugQueryOutList
    {
        public string medListCodg { get; set; }
        public string natDrugNo { get; set; }
        public string genname { get; set; }
        public string prodname { get; set; }
        public string regName { get; set; }
        public string listType { get; set; }
        public string listTypeName { get; set; }
        public string specName { get; set; }
        public string prdrName { get; set; }
        public string aprvno { get; set; }
        public string dosformName { get; set; }
        public string minPacunt { get; set; }
        public string minPacCnt { get; set; }
        public string minPrepunt { get; set; }
        public string poolareaNo { get; set; }
        public string poolareaName { get; set; }
        public string dualchnlFlag { get; set; }
        public string begntime { get; set; }
        public string endtime { get; set; }
      
    }
}
