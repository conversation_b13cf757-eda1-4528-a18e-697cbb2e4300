﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class MbExport
    Inherits HisControl.BaseChild

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(MbExport))
        Me.MyGrid1 = New CustomControl.MyGrid()
        Me.comm6 = New C1.Win.C1Command.C1Command()
        Me.C1CommandHolder1 = New C1.Win.C1Command.C1CommandHolder()
        Me.comm5 = New C1.Win.C1Command.C1Command()
        Me.Comm4 = New C1.Win.C1Command.C1Command()
        Me.C1Command1 = New C1.Win.C1Command.C1Command()
        Me.T_Line1 = New System.Windows.Forms.Label()
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel()
        Me.TreeView1 = New System.Windows.Forms.TreeView()
        Me.Image1 = New System.Windows.Forms.ImageList(Me.components)
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.T_Textbox = New CustomControl.MyTextBox()
        Me.T_Label = New C1.Win.C1Input.C1Label()
        Me.ToolBar1 = New C1.Win.C1Command.C1ToolBar()
        Me.C1CommandLink1 = New C1.Win.C1Command.C1CommandLink()
        Me.C1CommandLink3 = New C1.Win.C1Command.C1CommandLink()
        Me.C1CommandLink4 = New C1.Win.C1Command.C1CommandLink()
        Me.ContextMenuStrip1 = New System.Windows.Forms.ContextMenuStrip(Me.components)
        Me.comm全选 = New C1.Win.C1Command.C1Command()
        Me.comm反选 = New C1.Win.C1Command.C1Command()
        Me.Comm1 = New C1.Win.C1Command.C1Command()
        Me.Comm2 = New C1.Win.C1Command.C1Command()
        Me.Comm3 = New C1.Win.C1Command.C1Command()
        Me.AddNode = New System.Windows.Forms.ToolStripMenuItem()
        Me.DeleNode = New System.Windows.Forms.ToolStripMenuItem()
        Me.UpdateNode = New System.Windows.Forms.ToolStripMenuItem()
        CType(Me.C1CommandHolder1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.TableLayoutPanel1.SuspendLayout()
        Me.Panel1.SuspendLayout()
        CType(Me.T_Label, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.ContextMenuStrip1.SuspendLayout()
        Me.SuspendLayout()
        '
        'MyGrid1
        '
        Me.MyGrid1.CanCustomCol = False
        Me.MyGrid1.Col = 0
        Me.MyGrid1.ColumnFooters = False
        Me.MyGrid1.DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveRight
        Me.MyGrid1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.MyGrid1.FetchRowStyles = False
        Me.MyGrid1.Location = New System.Drawing.Point(185, 0)
        Me.MyGrid1.Margin = New System.Windows.Forms.Padding(0)
        Me.MyGrid1.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.MyGrid1.Name = "MyGrid1"
        Me.MyGrid1.Size = New System.Drawing.Size(702, 573)
        Me.MyGrid1.TabAction = C1.Win.C1TrueDBGrid.TabActionEnum.ControlNavigation
        Me.MyGrid1.TabIndex = 6
        Me.MyGrid1.Xmlpath = Nothing
        '
        'comm6
        '
        Me.comm6.Name = "comm6"
        Me.comm6.ShortcutText = ""
        Me.comm6.Text = "编辑"
        '
        'C1CommandHolder1
        '
        Me.C1CommandHolder1.Commands.Add(Me.comm6)
        Me.C1CommandHolder1.Commands.Add(Me.comm5)
        Me.C1CommandHolder1.Commands.Add(Me.Comm1)
        Me.C1CommandHolder1.Commands.Add(Me.Comm2)
        Me.C1CommandHolder1.Commands.Add(Me.Comm3)
        Me.C1CommandHolder1.Commands.Add(Me.Comm4)
        Me.C1CommandHolder1.Commands.Add(Me.C1Command1)
        Me.C1CommandHolder1.Commands.Add(Me.comm全选)
        Me.C1CommandHolder1.Commands.Add(Me.comm反选)
        Me.C1CommandHolder1.Owner = Me
        '
        'comm5
        '
        Me.comm5.Name = "comm5"
        Me.comm5.ShortcutText = ""
        Me.comm5.Text = "导入"
        '
        'Comm4
        '
        Me.Comm4.Name = "Comm4"
        Me.Comm4.ShortcutText = ""
        Me.Comm4.Text = "导出"
        Me.Comm4.ToolTipText = "导出记录"
        '
        'C1Command1
        '
        Me.C1Command1.Name = "C1Command1"
        Me.C1Command1.ShortcutText = ""
        Me.C1Command1.Text = "修改"
        '
        'T_Line1
        '
        Me.T_Line1.BackColor = System.Drawing.SystemColors.Control
        Me.T_Line1.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.T_Line1.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.T_Line1.Location = New System.Drawing.Point(0, 24)
        Me.T_Line1.Name = "T_Line1"
        Me.T_Line1.Size = New System.Drawing.Size(885, 2)
        Me.T_Line1.TabIndex = 120
        Me.T_Line1.Text = "Label1"
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.ColumnCount = 2
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 185.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanel1.Controls.Add(Me.TreeView1, 0, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.MyGrid1, 1, 0)
        Me.TableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(0, 28)
        Me.TableLayoutPanel1.Margin = New System.Windows.Forms.Padding(0)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.RowCount = 1
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(887, 573)
        Me.TableLayoutPanel1.TabIndex = 6
        '
        'TreeView1
        '
        Me.TreeView1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TreeView1.FullRowSelect = True
        Me.TreeView1.HotTracking = True
        Me.TreeView1.ImageIndex = 0
        Me.TreeView1.ImageList = Me.Image1
        Me.TreeView1.Location = New System.Drawing.Point(0, 0)
        Me.TreeView1.Margin = New System.Windows.Forms.Padding(0)
        Me.TreeView1.Name = "TreeView1"
        Me.TreeView1.SelectedImageIndex = 0
        Me.TreeView1.Size = New System.Drawing.Size(185, 573)
        Me.TreeView1.TabIndex = 5
        '
        'Image1
        '
        Me.Image1.ImageStream = CType(resources.GetObject("Image1.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.Image1.TransparentColor = System.Drawing.Color.White
        Me.Image1.Images.SetKeyName(0, "")
        Me.Image1.Images.SetKeyName(1, "")
        Me.Image1.Images.SetKeyName(2, "")
        '
        'Panel1
        '
        Me.Panel1.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Panel1.Controls.Add(Me.T_Textbox)
        Me.Panel1.Controls.Add(Me.T_Label)
        Me.Panel1.Controls.Add(Me.T_Line1)
        Me.Panel1.Controls.Add(Me.ToolBar1)
        Me.Panel1.Dock = System.Windows.Forms.DockStyle.Top
        Me.Panel1.Location = New System.Drawing.Point(0, 0)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(887, 28)
        Me.Panel1.TabIndex = 5
        '
        'T_Textbox
        '
        Me.T_Textbox.Captain = "名    称"
        Me.T_Textbox.CaptainBackColor = System.Drawing.Color.Transparent
        Me.T_Textbox.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.T_Textbox.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.T_Textbox.CaptainWidth = 60.0!
        Me.T_Textbox.ContentForeColor = System.Drawing.Color.Black
        Me.T_Textbox.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.T_Textbox.Location = New System.Drawing.Point(211, 3)
        Me.T_Textbox.Multiline = False
        Me.T_Textbox.Name = "T_Textbox"
        Me.T_Textbox.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.T_Textbox.ReadOnly = False
        Me.T_Textbox.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.T_Textbox.SelectionStart = 0
        Me.T_Textbox.SelectStart = 0
        Me.T_Textbox.Size = New System.Drawing.Size(185, 20)
        Me.T_Textbox.TabIndex = 127
        Me.T_Textbox.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.T_Textbox.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        '
        'T_Label
        '
        Me.T_Label.AutoSize = True
        Me.T_Label.BackColor = System.Drawing.Color.Transparent
        Me.T_Label.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.T_Label.ForeColor = System.Drawing.Color.Black
        Me.T_Label.Location = New System.Drawing.Point(413, 7)
        Me.T_Label.Name = "T_Label"
        Me.T_Label.Size = New System.Drawing.Size(47, 12)
        Me.T_Label.TabIndex = 2
        Me.T_Label.Tag = Nothing
        Me.T_Label.Text = "T_Label"
        Me.T_Label.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.T_Label.TextDetached = True
        Me.T_Label.TrimStart = True
        Me.T_Label.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'ToolBar1
        '
        Me.ToolBar1.AccessibleName = "Tool Bar"
        Me.ToolBar1.BackColor = System.Drawing.Color.Transparent
        Me.ToolBar1.CommandHolder = Nothing
        Me.ToolBar1.CommandLinks.AddRange(New C1.Win.C1Command.C1CommandLink() {Me.C1CommandLink1, Me.C1CommandLink3, Me.C1CommandLink4})
        Me.ToolBar1.Location = New System.Drawing.Point(0, 2)
        Me.ToolBar1.MinButtonSize = 22
        Me.ToolBar1.Movable = False
        Me.ToolBar1.Name = "ToolBar1"
        Me.ToolBar1.Size = New System.Drawing.Size(165, 22)
        Me.ToolBar1.Text = "C1ToolBar1"
        Me.ToolBar1.VisualStyle = C1.Win.C1Command.VisualStyle.Custom
        Me.ToolBar1.VisualStyleBase = C1.Win.C1Command.VisualStyle.Classic
        '
        'C1CommandLink1
        '
        Me.C1CommandLink1.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image), C1.Win.C1Command.ButtonLookFlags)
        Me.C1CommandLink1.Command = Me.Comm4
        Me.C1CommandLink1.Text = "导出"
        '
        'C1CommandLink3
        '
        Me.C1CommandLink3.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image), C1.Win.C1Command.ButtonLookFlags)
        Me.C1CommandLink3.Command = Me.comm全选
        Me.C1CommandLink3.SortOrder = 1
        '
        'C1CommandLink4
        '
        Me.C1CommandLink4.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image), C1.Win.C1Command.ButtonLookFlags)
        Me.C1CommandLink4.Command = Me.comm反选
        Me.C1CommandLink4.SortOrder = 2
        '
        'ContextMenuStrip1
        '
        Me.ContextMenuStrip1.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.AddNode, Me.DeleNode, Me.UpdateNode})
        Me.ContextMenuStrip1.Name = "ContextMenuStrip1"
        Me.ContextMenuStrip1.Size = New System.Drawing.Size(101, 70)
        '
        'comm全选
        '
        Me.comm全选.Image = Global.ZtHis.Emr.My.Resources.Resources.全选
        Me.comm全选.Name = "comm全选"
        Me.comm全选.ShortcutText = ""
        Me.comm全选.Text = "全选"
        '
        'comm反选
        '
        Me.comm反选.Image = Global.ZtHis.Emr.My.Resources.Resources.反选
        Me.comm反选.Name = "comm反选"
        Me.comm反选.ShortcutText = ""
        Me.comm反选.Text = "反选"
        '
        'Comm1
        '
        Me.Comm1.Image = Global.ZtHis.Emr.My.Resources.Resources.增加
        Me.Comm1.Name = "Comm1"
        Me.Comm1.Shortcut = System.Windows.Forms.Shortcut.F2
        Me.Comm1.ShortcutText = ""
        Me.Comm1.Text = "增加"
        Me.Comm1.ToolTipText = "增加记录"
        '
        'Comm2
        '
        Me.Comm2.Image = Global.ZtHis.Emr.My.Resources.Resources.删除
        Me.Comm2.Name = "Comm2"
        Me.Comm2.Shortcut = System.Windows.Forms.Shortcut.F3
        Me.Comm2.ShortcutText = ""
        Me.Comm2.Text = "删除"
        Me.Comm2.ToolTipText = "删除记录"
        '
        'Comm3
        '
        Me.Comm3.Image = Global.ZtHis.Emr.My.Resources.Resources.刷新
        Me.Comm3.Name = "Comm3"
        Me.Comm3.Shortcut = System.Windows.Forms.Shortcut.F4
        Me.Comm3.ShortcutText = ""
        Me.Comm3.Text = "刷新"
        Me.Comm3.ToolTipText = "刷新记录"
        '
        'AddNode
        '
        Me.AddNode.Image = Global.ZtHis.Emr.My.Resources.Resources.增加
        Me.AddNode.Name = "AddNode"
        Me.AddNode.Size = New System.Drawing.Size(100, 22)
        Me.AddNode.Text = "增加"
        '
        'DeleNode
        '
        Me.DeleNode.Image = Global.ZtHis.Emr.My.Resources.Resources.删除
        Me.DeleNode.Name = "DeleNode"
        Me.DeleNode.Size = New System.Drawing.Size(100, 22)
        Me.DeleNode.Text = "删除"
        '
        'UpdateNode
        '
        Me.UpdateNode.Image = Global.ZtHis.Emr.My.Resources.Resources.刷新
        Me.UpdateNode.Name = "UpdateNode"
        Me.UpdateNode.Size = New System.Drawing.Size(100, 22)
        Me.UpdateNode.Text = "修改"
        '
        'MbExport
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(887, 601)
        Me.Controls.Add(Me.TableLayoutPanel1)
        Me.Controls.Add(Me.Panel1)
        Me.Name = "MbExport"
        Me.Text = "模板导出"
        CType(Me.C1CommandHolder1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.TableLayoutPanel1.ResumeLayout(False)
        Me.Panel1.ResumeLayout(False)
        Me.Panel1.PerformLayout()
        CType(Me.T_Label, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ContextMenuStrip1.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents MyGrid1 As CustomControl.MyGrid
    Friend WithEvents C1CommandHolder1 As C1.Win.C1Command.C1CommandHolder
    Friend WithEvents comm5 As C1.Win.C1Command.C1Command
    Friend WithEvents Comm1 As C1.Win.C1Command.C1Command
    Friend WithEvents Comm2 As C1.Win.C1Command.C1Command
    Friend WithEvents Comm3 As C1.Win.C1Command.C1Command
    Friend WithEvents Comm4 As C1.Win.C1Command.C1Command
    Friend WithEvents C1Command1 As C1.Win.C1Command.C1Command
    Friend WithEvents TableLayoutPanel1 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents TreeView1 As System.Windows.Forms.TreeView
    Friend WithEvents Image1 As System.Windows.Forms.ImageList
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents T_Label As C1.Win.C1Input.C1Label
    Friend WithEvents T_Line1 As System.Windows.Forms.Label
    Friend WithEvents ToolBar1 As C1.Win.C1Command.C1ToolBar
    Friend WithEvents C1CommandLink1 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents UpdateNode As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents DeleNode As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents AddNode As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents ContextMenuStrip1 As System.Windows.Forms.ContextMenuStrip
    Friend WithEvents comm全选 As C1.Win.C1Command.C1Command
    Friend WithEvents comm反选 As C1.Win.C1Command.C1Command
    Friend WithEvents C1CommandLink3 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents C1CommandLink4 As C1.Win.C1Command.C1CommandLink
    Private WithEvents comm6 As C1.Win.C1Command.C1Command
    Friend WithEvents T_Textbox As CustomControl.MyTextBox
End Class
