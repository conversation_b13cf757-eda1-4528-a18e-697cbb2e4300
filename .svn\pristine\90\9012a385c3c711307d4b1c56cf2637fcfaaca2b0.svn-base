﻿Imports System.Data.SqlClient
Imports Stimulsoft.Report
Imports HisControl

Public Class Zy_Fy3

#Region "定义__变量"

    Dim My_Table As New DataTable                       '从表
    Dim V_Sum As Double                                 '金额小计
    Dim Zb_Sum As Double

    Dim Cb_Cm As CurrencyManager                     '同步指针
    Dim Cb_Row As DataRow                            '当前选择行

    Dim Str_Select As String
    Dim My_Dataset As New DataSet
    Dim V_Yf_Sl As String           '发药时显示对应药房库存

    Public Cf_Code As String
#End Region

#Region "传参"
    Dim Rform As BaseForm
    Dim Rrow As DataRow
    Dim RFylb As String
#End Region

    Public Sub New(ByVal tform As BaseForm, ByVal trow As DataRow, ByVal tFylb As String)
        '此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        Rform = tform
        Rrow = trow
        RFylb = tFylb
        '在 InitializeComponent() 调用之后添加任何初始化。
    End Sub

    Private Sub Zy_Fy3_FormClosed(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosedEventArgs) Handles Me.FormClosed
        Rform.F_Sum()
    End Sub

    Private Sub Zy_Fy3_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        V_Yf_Sl = "Yf_Sl" & Mid(HisVar.HisVar.YfCode, 6)         'Yf_Sl+药房编号的第6位
        Call Form_Init()                '窗体初始化
        Call Zb_Show()                  '显示数据

    End Sub

#Region "窗体__事件"

    Private Sub Form_Init()
        Panel2.Height = 30


        '初始化TDBGrid
        Dim My_Grid As New BaseClass.C_Grid(Me.C1TrueDBGrid1)
        With My_Grid
            .Init_Grid()
            .AllSort(False)
            .P_Dock(BaseClass.C_Grid.Dock.Fill)
            .Init_Column("药品名称", "Yp_Name", 160, "左", "")
            .Init_Column("规格", "Mx_Gg", 100, "左", "")
            .Init_Column("产地", "Mx_Cd", 200, "左", "")
            .Init_Column("生产批号", "Yp_Ph", 70, "左", "")
            .Init_Column("有效期", "Yp_Yxq", 80, "中", "yyyy-MM-dd")
            .Init_Column("数量", "Cf_Sl", 60, "右", "0.####")
            If ZTHisPara.PublicConfig.PharmacyShowKc = True Then
                .Init_Column("库存数量", "Yf_Sl", 70, "右", "###,###,##0.####")
            End If
            .Init_Column("单位", "Mx_XsDw", 32, "中", "")
            .Init_Column("单价", "Cf_Dj", 45, "右", "###,###,##0.00####")
            .Init_Column("金额", "Cf_Money", 55, "右", "###,###,##0.00##")
        End With
    End Sub

#End Region

#Region "清空__显示"

    Private Sub Zb_Show()   '显示记录

        Select Case RFylb
            Case "按经手人汇总发药"
                V_Sum = 0
                With Rrow
                    Label12.Text = .Item("Jsr_Name") & ""
                    Label2.Text = .Item("Cf_Money") & ""
                End With
            Case "按医生汇总发药"
                V_Sum = 0
                With Rrow
                    Label11.Text = "医  生"
                    Label12.Text = .Item("Ys_Name") & ""
                    Label2.Text = .Item("Cf_Money") & ""
                End With
            Case "按科室汇总发药"
                V_Sum = 0
                With Rrow
                    Label11.Text = "科  室"
                    Label12.Text = .Item("Ks_Name") & ""
                    Label2.Text = .Item("Cf_Money") & ""
                End With
            Case "按长期/临时医嘱汇总发药"
                V_Sum = 0
                With Rrow
                    Label11.Text = "处方类型"
                    Label12.Text = .Item("Cf_Lx") & ""
                    Label2.Text = .Item("Cf_Money") & ""
                End With
            Case "全部待发药汇总"
                V_Sum = 0
                With Rrow
                    Label11.Text = "汇总方式"
                    Label12.Text = .Item("cf") & ""
                    Label2.Text = .Item("Cf_Money") & ""
                End With
        End Select

        Call P_Data_Show()

    End Sub

    Private Sub P_Data_Show()   '从表数据
        Dim str As String = "SELECT Bl_Cfyp.Xx_Code,Yp_Name,Mx_Gg,Mx_Cd,Mx_Gyzz,Mx_XsDw,sum(Bl_Cfyp.Cf_Sl) as Cf_Sl,Bl_Cfyp.Cf_Dj,sum(Bl_Cfyp.Cf_Money) as Cf_Money,Bl_Cfyp.Cf_Lb,V_YpKc.Yp_Ph,V_YpKc.Yp_Yxq," & V_Yf_Sl & " as Yf_Sl FROM Bl_Cfyp,V_YpKc where Cf_Code IN " & Cf_Code & " and Bl_Cfyp.Xx_Code = dbo.V_YpKc.Xx_Code group by Bl_Cfyp.Xx_Code,Yp_Name,Mx_Gg,Mx_Cd,Mx_Gyzz,Mx_XsDw,Bl_Cfyp.Cf_Dj,Bl_Cfyp.Cf_Lb,V_YpKc.Yp_Ph,V_YpKc.Yp_Yxq," & V_Yf_Sl & " Order By Xx_Code "
        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, str, "从表", True)

        My_Table = My_Dataset.Tables("从表")

        '主表记录
        Cb_Cm = CType(BindingContext(My_Dataset, "从表"), CurrencyManager)
        C1TrueDBGrid1.SetDataBinding(My_Dataset, "从表", True)

        T_Label5.Text = My_Dataset.Tables("从表").Compute("Sum(Cf_Money)", "")

    End Sub

#End Region

#Region "控件__动作"

#Region "其它__控件"

    Private Sub Comm_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.Click, Comm2.Click
        Select Case sender.Text
            Case "发药"
                Dim Pd As Object = HisVar.HisVar.Sqldal.GetSingle("select distinct Pd_Wc from Zd_YfPd where Pd_Wc='否' and Yf_Code='" & HisVar.HisVar.YfCode & "'")
                If Pd Is Nothing Then
                Else
                    MsgBox("正在进行盘点，请等待盘点完成后在进行发药！", MsgBoxStyle.Information, "提示：")
                    Exit Sub
                End If
                If HisVar.HisVar.Sqldal.GetSingle("Select Count(*) from Bl_Cf where Cf_Qr_Date is not null and Cf_Code in  " & Cf_Code & "") > 0 Then
                    MsgBox("该处方已经发药,请更新状态！", MsgBoxStyle.Information, "提示")
                    Exit Sub
                End If
                If HisVar.HisVar.Sqldal.GetSingle("Select Count(*) from Bl_Cf where Cf_Print = '否' and Cf_Qr = '否' And Cf_Code in  " & Cf_Code & "") > 0 Then
                    MsgBox("该处方已经退回,请更新状态！", MsgBoxStyle.Information, "提示")
                    Exit Sub
                End If
                Me.Cursor = Cursors.WaitCursor
                Comm1.Enabled = False
                Call Data_Update()                        '更新从表
                Me.Cursor = Cursors.Default
                Comm1.Enabled = True
            Case "打印"
                Dim StiRpt As New StiReport
                StiRpt.Load(".\Rpt\药房住院发药汇总.mrt")
                StiRpt.ReportName = "住院发药汇总表"
                StiRpt.RegData(My_Dataset.Tables("从表"))
                StiRpt.Compile()
                StiRpt("汇总发药方式") = RFylb
                StiRpt("分类") = Label11.Text
                StiRpt("属性") = Label12.Text
                StiRpt("打印单位") = HisVar.HisVar.WsyName
                StiRpt("经手人") = HisVar.HisVar.JsrName
                StiRpt.Show()
        End Select

    End Sub

#End Region

#End Region

#Region "从表__编辑"

    Private Sub Data_Update()   '从表更新
        If My_Table Is Nothing Then Exit Sub
        Dim Yf_Sl As String

        Yf_Sl = "Yf_Sl" & CDbl(Mid(HisVar.HisVar.YfCode, 5, 2))
        Dim arr As New ArrayList
        Dim Str As String = ""

        For Each Me.Cb_Row In My_Table.Rows
            Dim V_YpName As String = HisVar.HisVar.Sqldal.GetSingle("Select Yp_Name from V_Ypkc where Xx_Code='" & Cb_Row.Item("Xx_Code") & "' and isnull(" & Yf_Sl & ",0)-(" & Cb_Row.Item("Cf_Sl") & ")<0")
            If V_YpName & "" <> "" Then
                Str = Str & V_YpName & vbCr
            End If
            arr.Add("Update Zd_Ml_Yp4 Set " & Yf_Sl & "=isnull(" & Yf_Sl & ",0)-(" & Cb_Row.Item("Cf_Sl") & ") Where Xx_Code='" & Cb_Row.Item("Xx_Code") & "' ")
        Next

        arr.Add("UpDate Bl_Cf Set Cf_Qr='是',Cf_Qr_Date='" & Now & "',Jsr_Code_Qr='" & HisVar.HisVar.JsrCode & "', Ly_Wc='是' where Bl_Cf.Cf_Code IN " & Cf_Code & "    and Yf_Code='" & HisVar.HisVar.YfCode & "'")



        Try
            If Str & "" <> "" Then
                MsgBox(Str & "大于库存数量，请点击【退回】进行修改处方！", MsgBoxStyle.Information, "提示：")
                Exit Sub
            End If
            HisVar.HisVar.Sqldal.ExecuteSqlTran(arr)
            Rrow.Delete()
            Rrow.AcceptChanges()
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
        End Try
        Me.Close()
    End Sub

#End Region



End Class