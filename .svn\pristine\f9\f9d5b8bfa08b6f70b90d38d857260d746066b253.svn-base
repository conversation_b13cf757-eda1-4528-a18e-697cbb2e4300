﻿/**  版本信息模板在安装目录下，可自行修改。
* B_Materials_InOut_Class_Dict.cs
*
* 功 能： N/A
* 类 名： B_Materials_InOut_Class_Dict
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016-12-03 09:37:10   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Collections.Generic;
using Common;
using ModelOld;
namespace BLLOld
{
	/// <summary>
	/// 物资出入类别
	/// </summary>
	public partial class B_Materials_InOut_Class_Dict
	{
		private readonly DAL.D_Materials_InOut_Class_Dict dal=new DAL.D_Materials_InOut_Class_Dict();
		public B_Materials_InOut_Class_Dict()
		{}
		#region  BasicMethod


        public string MaxCode()
        {
            string max = dal.MaxCode();
            return max;
        }


		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string MaterialsInOut_Code)
		{
			return dal.Exists(MaterialsInOut_Code);
		}

		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ModelOld.M_Materials_InOut_Class_Dict model)
		{
			return dal.Add(model);
		}

		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ModelOld.M_Materials_InOut_Class_Dict model)
		{
			return dal.Update(model);
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string MaterialsInOut_Code)
		{
			
			return dal.Delete(MaterialsInOut_Code);
		}
		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool DeleteList(string MaterialsInOut_Codelist )
		{
			return dal.DeleteList(MaterialsInOut_Codelist );
		}

		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Materials_InOut_Class_Dict GetModel(string MaterialsInOut_Code)
		{
			
			return dal.GetModel(MaterialsInOut_Code);
		}

		/// <summary>
		/// 得到一个对象实体，从缓存中
		/// </summary>
		public ModelOld.M_Materials_InOut_Class_Dict GetModelByCache(string MaterialsInOut_Code)
		{
			
			string CacheKey = "M_Materials_InOut_Class_DictModel-" + MaterialsInOut_Code;
			object objModel = Common.DataCache.GetCache(CacheKey);
			if (objModel == null)
			{
				try
				{
					objModel = dal.GetModel(MaterialsInOut_Code);
					if (objModel != null)
					{
						int ModelCache = Common.ConfigHelper.GetConfigInt("ModelCache");
						Common.DataCache.SetCache(CacheKey, objModel, DateTime.Now.AddMinutes(ModelCache), TimeSpan.Zero);
					}
				}
				catch{}
			}
			return (ModelOld.M_Materials_InOut_Class_Dict)objModel;
		}

        public DataSet GetListForDr()
        {
            return dal.GetListForDr();
        }


		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			return dal.GetList(strWhere);
		}
		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			return dal.GetList(Top,strWhere,filedOrder);
		}
		/// <summary>
		/// 获得数据列表
		/// </summary>
		public List<ModelOld.M_Materials_InOut_Class_Dict> GetModelList(string strWhere)
		{
			DataSet ds = dal.GetList(strWhere);
			return DataTableToList(ds.Tables[0]);
		}
		/// <summary>
		/// 获得数据列表
		/// </summary>
		public List<ModelOld.M_Materials_InOut_Class_Dict> DataTableToList(DataTable dt)
		{
			List<ModelOld.M_Materials_InOut_Class_Dict> modelList = new List<ModelOld.M_Materials_InOut_Class_Dict>();
			int rowsCount = dt.Rows.Count;
			if (rowsCount > 0)
			{
				ModelOld.M_Materials_InOut_Class_Dict model;
				for (int n = 0; n < rowsCount; n++)
				{
					model = dal.DataRowToModel(dt.Rows[n]);
					if (model != null)
					{
						modelList.Add(model);
					}
				}
			}
			return modelList;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetAllList()
		{
			return GetList("");
		}

		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			return dal.GetRecordCount(strWhere);
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			return dal.GetListByPage( strWhere,  orderby,  startIndex,  endIndex);
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		//public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		//{
			//return dal.GetList(PageSize,PageIndex,strWhere);
		//}

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

