﻿Imports System.Data.SqlClient
Imports System.Text.RegularExpressions
Imports BLL
Imports Common.Delegate
Imports DTO
Imports HisControl
Imports Model
Imports ZTHisOutpatient

Public Class Xs_Mz2

#Region "定义__变量"
    Dim My_Adapter As New SqlDataAdapter                '从表适配器
    Dim My_Table As New DataTable                       '从表
    Dim Cb_Cm As CurrencyManager                     '同步指针
    Dim Cb_Row As DataRow                            '当前选择行
    Dim V_Insert As Boolean                          '增加记录
    Dim Yp_Lb As String
    Dim My_Dataset As New DataSet
    Dim Tb As DataTable
    Private _patientInfo As New DtoPatientInfo()
    Private _transmitTxt As Common.Delegate.TransmitTxt = New TransmitTxt()
    Private _mayReceive As Decimal '应收  折扣后
    Dim _mdlmz As New Model.MdlMz
    Dim _bllMz As New BllMz
    Dim _bllMzYp As New BllMz_Yp
    Dim _bllMzXm As New BllMz_Xm
    Dim _payList As New List(Of MdlMz_PayMents_Money)()
#End Region

#Region "传参"
    Dim Rform As BaseForm
    Dim Rinsert As Boolean
    Dim Rdate As Date
    Dim Rrow As DataRow
    Dim RZbtb As DataTable
    Dim Rtdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Dim Rlb As C1.Win.C1Input.C1Label
#End Region

    Public Sub New(ByVal tform As BaseForm, ByVal tinsert As Boolean, ByVal tdate As Date, ByVal trow As DataRow, ByVal tZbtb As DataTable, ByVal ttdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid, ByVal tlb As C1.Win.C1Input.C1Label)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        Rform = tform
        Rinsert = tinsert
        Rdate = tdate
        Rrow = trow
        RZbtb = tZbtb
        Rtdbgrid = ttdbgrid
        Rlb = tlb
        ' 在 InitializeComponent() 调用之后添加任何初始化。

    End Sub

    Protected Overrides Sub OnKeyDown(ByVal e As System.Windows.Forms.KeyEventArgs)
        MyBase.OnKeyDown(e)
        If e.KeyCode = Keys.F1 Then
            RadioButton4.Checked = True
        ElseIf e.KeyCode = Keys.F2 Then
            RadioButton1.Checked = True
        ElseIf e.KeyCode = Keys.F3 Then
            Comm3.Select()
            Call Comm_Click(Comm3, Nothing)
        ElseIf e.Control = True And e.KeyCode = Keys.S Then
            Comm1.Select()
            Call Comm_Click(Comm1, Nothing)
        End If


    End Sub



    Private Sub Xs_Mz2_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load

        If HisPara.PublicConfig.MzGh = "是" Then
            TableLayoutPanel1.Controls.Add(C1Combo8, 3, 0)
            Me.TableLayoutPanel1.SetColumnSpan(Me.C1Combo8, 3)
            C1Combo8.Visible = True
            XmTextBox.Visible = False
            'C1TextBox4.Enabled = False
            SingleSex1.Enabled = False
            SfzhTextBox.Enabled = False
            AddressTextBox.Enabled = False
            ComboBxlb1.Enabled = False
            YlCodeTextBox.Enabled = False
            NlC1NumericEdit.Enabled = False

            SfzhTextBox.BackColor = SystemColors.Info
            AddressTextBox.BackColor = SystemColors.Info
            NlC1NumericEdit.BackColor = SystemColors.Info

            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "Select Mz_Code,Ry_Jc,Ry_Name,Ry_Sex,Ry_Sfzh,Ry_Age,Ry_Address,Mz_Gh.Bxlb_Code,Bxlb_Name,Ry_YlCode,Ks_Code from Mz_Gh,Zd_Bxlb where Mz_Gh.Bxlb_Code=Zd_Bxlb.Bxlb_Code and Gh_Print='是' and Gh_Zf='否' and Convert(Varchar(10),Gh_Date,126)='" & Format(Rdate, "yyyy-MM-dd") & "'  Union all Select Mz_Code,Ry_Jc,Ry_Name,Ry_Sex,Ry_Sfzh,Ry_Age,Ry_Address,Mz_Gh_Sum.Bxlb_Code,Bxlb_Name,Ry_YlCode,Ks_Code from Mz_Gh_Sum,Zd_Bxlb where Mz_Gh_Sum.Bxlb_Code=Zd_Bxlb.Bxlb_Code and Gh_Print='是' and Gh_Zf='否' and Convert(Varchar(10),Gh_Date,126)='" & Format(Rdate, "yyyy-MM-dd") & "' order by Mz_Code desc", "挂号人员", True)

            Dim My_Combo8 As New BaseClass.C_Combo2(Me.C1Combo8, My_Dataset.Tables("挂号人员").DefaultView, "Ry_Name", "Mz_Code", 560)
            With My_Combo8
                .Init_TDBCombo()
                .Init_Colum("Mz_Code", "挂号编码", 0, "左")
                .Init_Colum("Ry_Jc", "姓名简称", 65, "左")
                .Init_Colum("Ry_Name", "姓名", 90, "左")
                .Init_Colum("Ry_Sex", "性别", 35, "中")
                .Init_Colum("Ry_Sfzh", "身份证号", 120, "左")
                .Init_Colum("Ry_Age", "年龄", 40, "右")
                .Init_Colum("Ry_Address", "家庭住址", 0, "左")
                .Init_Colum("Bxlb_Code", "", 0, "左")
                .Init_Colum("Bxlb_Name", "患者类别", 65, "中")
                .Init_Colum("Ry_YlCode", "医疗证号", 120, "左")
                .Init_Colum("Ks_Code", "科室", 0, "左")
                .MaxDropDownItems(17)
                .SelectedIndex(-1)
            End With
            With C1Combo8
                .AutoCompletion = False
                .AutoSelect = False
            End With
        Else
            TableLayoutPanel1.Controls.Add(XmTextBox, 2, 0)
            C1Combo8.Visible = False
            XmTextBox.Visible = True
        End If


        RadioButton1.Checked = True

        Call Form_Init()                '窗体初始化
        ComboYf1.SelectedValue = iniOperate.iniopreate.GetINI("Personal", "默认药房", "", HisVar.HisVar.Parapath & "\Config.Ini") & ""
        AddHandler Me._transmitTxt.SetText, AddressOf GridMove
        If Rinsert = True Then           '主表
            Call Zb_Clear()                 '清空数据
        Else
            Call Zb_Show()                  '显示数据
        End If


    End Sub

#Region "窗体__事件"


    Private Sub Form_Init()
        Panel2.Height = 30

        C1NumericEdit1.Enabled = False
        C1Command1.Enabled = False
        C1Command2.Enabled = False

        SingleSex1.Init()
        ComboBxlb1.Init()
        ComboKs1.Init()
        ComboYs1.Init()
        ComboJb1.Init()
        ComboYf1.Init("Yf_Use=1")
        Dim autohide As String = Common.WinFormVar.Var.IniFileHelper.IniReadValue("参数", "门诊录入辅助框") + ""
        C1DockingTab1.AutoHiding = Not String.IsNullOrEmpty(autohide) AndAlso Boolean.Parse(autohide)
        MzData1.Init(XmTextBox, TelText, SfzhTextBox)
        MzData1.GenCzdAction = AddressOf GenCzd
        'YfComobo1.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
    End Sub

    Private Sub Xs_Mz_Yp_FormClosing(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosingEventArgs) Handles Me.FormClosing
        If e.CloseReason = CloseReason.UserClosing Then
            If Rinsert = False Then
                If HisVar.HisVar.Sqldal.GetSingle("Select Isnull(Mz_Print,0)|Isnull(MzCf_Ok,0)|Isnull(Mz_FyQr,0) from Mz where  Mz_Code='" & Rrow.Item("Mz_Code") & "'") = False Then
                    Call Zb_Save() '更新主表(主表已经保存过,取消操作)
                Else
                    Call Rform.F_Sum()
                End If
            End If
        End If

    End Sub

#End Region

#Region "清空__显示"

    Private Sub Zb_Clear()
        _mdlmz = New MdlMz()
        XmTextBox.Text = ""

        AddressTextBox.Text = ""
        YlCodeTextBox.Text = ""
        SfzhTextBox.Text = ""

        C1NumericEdit1.Value = 1

        NlC1NumericEdit.Value = DBNull.Value
        TelText.Text = ""
        DateRyBirthday.Value = Nothing
        '备注
        lblMz_Code.Text = F_MaxCode(Format(Rdate, "yyMMdd"))                                                  '客户编码

        SingleSex1.SelectedIndex = 0
        ComboBxlb1.SelectedIndex = 0
        ComboJb1.SelectedIndex = -1

        C1Combo8.SelectedIndex = -1

        Dim ysCode As String = Common.WinFormVar.Var.IniFileHelper.IniReadValue("参数", "门诊录入医生") + ""
        Dim ksCode As String = Common.WinFormVar.Var.IniFileHelper.IniReadValue("参数", "门诊录入科室") + ""
        ComboYs1.SelectedValue = ysCode
        ComboKs1.SelectedValue = ksCode

        If RadioButton1.Checked = True Then
            Yp_Lb = Mid(RadioButton1.Text, 4)
            C1TrueDBGrid_Init_Zxy()
            C1Command1.Enabled = True
            C1Command2.Enabled = True
            C1NumericEdit1.Enabled = True
        End If
        If RadioButton4.Checked = True Then
            Yp_Lb = Mid(RadioButton1.Text, 4)
            C1TrueDBGrid_Init_Xm()
            C1Command1.Enabled = False
            C1Command2.Enabled = False
            C1NumericEdit1.Enabled = False
        End If

        Call P_Data_Show()
    End Sub

    Private Sub Zb_Show()   '显示记录

        With Rrow
            lblMz_Code.Text = .Item("Mz_Code") & ""
            _mdlmz = _bllmz.GetModel(.Item("Mz_Code") & "")
            XmTextBox.Text = .Item("Ry_Name") & ""
            SingleSex1.Text = .Item("Ry_Sex") & ""
            AddressTextBox.Text = .Item("Ry_Address") & ""
            NlC1NumericEdit.Text = .Item("Ry_Age") & ""
            DateRyBirthday.Value = .Item("Ry_Birthday")
            TelText.Text = .Item("Ry_Tell") & ""

            '关于身份证号的验证
            SfzhTextBox.Text = .Item("Ry_Sfzh") & ""

            If HisPara.PublicConfig.MzGh = "是" Then
                C1Combo8.SelectedValue = .Item("Ry_Memo") & ""
            End If
            ComboBxlb1.SelectedValue = .Item("Bxlb_Code") & ""
            YlCodeTextBox.Text = .Item("Ry_YlCode") & ""

            ComboJb1.SelectedValue = .Item("Jb_Code") & ""
            ComboYs1.SelectedValue = .Item("Ys_Code") & ""
            ComboKs1.SelectedValue = .Item("Ks_Code") & ""
            ComboYf1.SelectedValue = .Item("Yf_Code") & ""

        End With

        C1NumericEdit1.Value = 1

        If RadioButton1.Checked = True Then
            Yp_Lb = Mid(RadioButton1.Text, 4)
            C1TrueDBGrid_Init_Zxy()
            C1Command1.Enabled = True
            C1Command2.Enabled = True
            C1NumericEdit1.Enabled = True
        End If

        If RadioButton4.Checked = True Then
            Yp_Lb = Mid(RadioButton4.Text, 4)
            C1TrueDBGrid_Init_Xm()
            C1Command1.Enabled = False
            C1Command2.Enabled = False
            C1NumericEdit1.Enabled = False
        End If

        '出库编码

        Call P_Data_Show()
        C1TrueDBGrid1.Select()
        C1TrueDBGrid1.MoveLast()

    End Sub

    Private Sub P_Data_Show()   '从表数据

        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "Select Mz_Xm.*,Xm_Dw,Xm_Name,医疗目录编码 From Mz_Xm,Zd_Ml_Xm3 Left Join Country_YBMLDZ_XM on Xm_Code=Mx_Code Where  Mz_Xm.Xm_Code=Zd_Ml_Xm3.Xm_Code And Mz_Code='" & lblMz_Code.Text & "' Order By Mz_Id", "诊疗项目", True)
        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "Select Mz_Yp.*,Mx_Gg,Mx_Cd,Yp_Name,Mx_XsDw,V_YpKc.Yp_Ph,V_YpKc.Yp_Yxq,医疗目录编码,Dl_Code,Mx_Gyzz From Mz_Yp,V_YpKc Left Join (SELECT Mx_Code,医疗目录编码 FROM Country_YBMLDZ_WC UNION ALL SELECT Mx_Code,医疗目录编码 FROM Country_YBMLDZ_XY UNION ALL SELECT Mx_Code,医疗目录编码 FROM Country_YBMLDZ_ZY)YBDZ On YBDZ.Mx_Code=V_YpKc.Mx_Code Where Mz_Yp.Xx_Code=V_YpKc.Xx_Code And Mz_Code='" & lblMz_Code.Text & "' Order By Mz_Id", "药品卫材", True)

        If Yp_Lb = "诊疗项目" Then
            My_Table = My_Dataset.Tables("诊疗项目")
        ElseIf Yp_Lb = "药品卫材" Then
            My_Table = My_Dataset.Tables("药品卫材")
        End If


        My_Table.PrimaryKey = New DataColumn() {My_Table.Columns("Mz_Id")}
        My_Dataset.Tables("药品卫材").Columns("医疗目录编码").ReadOnly = False

        '列的唯一性
        Dim My_Column As DataColumn = My_Table.Columns("Mz_Id")
        With My_Column
            .AutoIncrement = True
            .AutoIncrementSeed = .AutoIncrementSeed
            .AutoIncrementStep = 1
        End With

        '主表记录
        Cb_Cm = CType(BindingContext(My_Dataset, My_Table.TableName), CurrencyManager)
        C1TrueDBGrid1.SetDataBinding(My_Dataset, My_Table.TableName, True)


        Call F_Sum()

        If HisPara.PublicConfig.MzGh = "是" Then
            C1Combo8.Select()
        Else
            XmTextBox.Select()
        End If
        MzData1.YpTable = My_Dataset.Tables("药品卫材")
        MzData1.XmTable = My_Dataset.Tables("诊疗项目")
    End Sub

#End Region

#Region "主表__编辑"

    Private Function Zb_Save()                       '主表保存
        If CustomControl.Func.NotAllowEmpty(ComboBxlb1) Then Return False
        Call Zb_Add()
        Rform.F_Sum()
        Return True
    End Function

    Private Sub Zb_Add()    '增加记录
        If RadioButton1.Checked = True Then
            Yp_Lb = Mid(RadioButton1.Text, 4)
        End If

        Dim My_NewRow As DataRow
        If Rinsert = True Then
            My_NewRow = RZbtb.NewRow
        Else
            My_NewRow = Rrow
        End If

        With My_NewRow

            .Item("Yy_Code") = HisVar.HisVar.WsyCode
            If Rinsert = True Then
                .Item("Mz_Code") = F_MaxCode(Format(Rdate, "yyMMdd"))        '出库编码
            Else
                .Item("Mz_Code") = .Item("Mz_Code")
            End If
            lblMz_Code.Text = .Item("Mz_Code") & ""
            .Item("Yf_Code") = ComboYf1.SelectedValue
            .Item("Ks_Code") = ComboKs1.SelectedValue
            .Item("Ys_Code") = ComboYs1.SelectedValue
            .Item("Mz_Date") = Format(Rdate, "yyyy-MM-dd")               '入库日期
            .Item("Mz_Time") = Format(Now, "HH:mm:ss")
            .Item("Jsr_Code") = HisVar.HisVar.JsrCode
            .Item("Lr_JsrCode") = HisVar.HisVar.JsrCode
            If ComboJb1.SelectedValue = "" Then
                .Item("Jb_Code") = DBNull.Value
                .Item("Jb_Name") = ""
            Else
                .Item("Jb_Code") = ComboJb1.SelectedValue
                .Item("Jb_Name") = ComboJb1.Text
            End If
            .Item("Bxlb_Code") = ComboBxlb1.SelectedValue
            .Item("Bxlb_Name") = ComboBxlb1.Text
            .Item("Ry_YlCode") = YlCodeTextBox.Text
            .Item("Ry_Name") = XmTextBox.Text
            .Item("Ry_Sex") = SingleSex1.Text
            .Item("Ry_Age") = NlC1NumericEdit.Value
            .Item("Ry_Address") = AddressTextBox.Text
            .Item("Ry_Tell") = TelText.Text
            .Item("Ry_Birthday") = DateRyBirthday.Value

            .Item("Ry_Sfzh") = SfzhTextBox.Text
            If HisPara.PublicConfig.MzGh = "是" Then
                .Item("Ry_Memo") = C1Combo8.Columns("Mz_Code").Value
            Else
                .Item("Ry_Memo") = ""
            End If

            Dim V_YpMoney As Decimal = HisVar.HisVar.Sqldal.GetSingle("select Isnull(Sum(Mz_Money),0) from Mz_Yp where Mz_Code='" & lblMz_Code.Text & "'")
            Dim V_XmMoney As Decimal = HisVar.HisVar.Sqldal.GetSingle("select Isnull(Sum(Mz_Money),0) from Mz_Xm where Mz_Code='" & lblMz_Code.Text & "'")
            .Item("Mz_YpMoney") = Format(V_YpMoney, "0.00##")
            .Item("Mz_XmMoney") = Format(V_XmMoney, "0.00##")
            .Item("Mz_Money") = Format(V_YpMoney + V_XmMoney, "0.00##")
            _mayReceive = .Item("Mz_Money")
            .Item("Mz_Original_Money") = Format(V_YpMoney + V_XmMoney, "0.00##")
            '.Item("Mz_YpMoney") = Format(IIf(My_Dataset.Tables("药品卫材").Compute("Sum(Mz_Money)", "") Is DBNull.Value, 0, My_Dataset.Tables("药品卫材").Compute("Sum(Mz_Money)", "")), "###,###,##0.00##")
            '.Item("Mz_XmMoney") = Format(IIf(My_Dataset.Tables("诊疗项目").Compute("Sum(Mz_Money)", "") Is DBNull.Value, 0, My_Dataset.Tables("诊疗项目").Compute("Sum(Mz_Money)", "")), "###,###,##0.00##")
            '.Item("Mz_Money") = Format(IIf(My_Dataset.Tables("药品卫材").Compute("Sum(Mz_Money)", "") Is DBNull.Value, 0, My_Dataset.Tables("药品卫材").Compute("Sum(Mz_Money)", "")) + IIf(My_Dataset.Tables("诊疗项目").Compute("Sum(Mz_Money)", "") Is DBNull.Value, 0, My_Dataset.Tables("诊疗项目").Compute("Sum(Mz_Money)", "")), "###,###,##0.00##")

            .Item("Ks_Name") = ComboKs1.Text
            .Item("Ys_Name") = ComboYs1.Text

            If Rinsert = True Then
                .Item("Mz_Print") = False
                .Item("Mz_FyQr") = False
                .Item("Mz_Print1") = "否"
                .Item("Mz_FyQr1") = "否"
            End If
            .Item("MzCfZxType") = "正常"
            .Item("IdentityType") = "0001"
        End With
        Common.WinFormVar.Var.IniFileHelper.IniWriteValue("参数", "门诊录入医生", ComboYs1.SelectedValue)
        Common.WinFormVar.Var.IniFileHelper.IniWriteValue("参数", "门诊录入科室", ComboKs1.SelectedValue)
        Zb_Update(My_NewRow)
        Rlb.Text = "∑=" + (Rtdbgrid.Splits(0).Rows.Count - 1).ToString
    End Sub

    Private Sub Zb_Update(ByVal V_Row As DataRow)      '更新主表
        '        Dim Insert_String As String = ""
        '        Dim Update_String As String = ""
        '        Dim para() As SqlParameter = Nothing
        '        Dim ilist As List(Of SqlParameter) = New List(Of SqlParameter)()
        '
        '        ilist.Add(New SqlParameter("@Yy_Code", SqlDbType.Char))
        '        ilist.Add(New SqlParameter("@Yf_Code", SqlDbType.VarChar))
        '        ilist.Add(New SqlParameter("@Ks_Code", SqlDbType.VarChar))
        '        ilist.Add(New SqlParameter("@Ys_Code", SqlDbType.VarChar))
        '        ilist.Add(New SqlParameter("@Mz_Date", SqlDbType.VarChar))
        '        ilist.Add(New SqlParameter("@Mz_Time", SqlDbType.VarChar))
        '        ilist.Add(New SqlParameter("@Jsr_Code", SqlDbType.VarChar))
        '        ilist.Add(New SqlParameter("@Jb_Code", SqlDbType.VarChar))
        '        ilist.Add(New SqlParameter("@Jb_Name", SqlDbType.VarChar))
        '        ilist.Add(New SqlParameter("@Bxlb_Code", SqlDbType.VarChar))
        '        ilist.Add(New SqlParameter("@Ry_YlCode", SqlDbType.VarChar))
        '        ilist.Add(New SqlParameter("@Ry_Name", SqlDbType.VarChar))
        '        ilist.Add(New SqlParameter("@Ry_Sex", SqlDbType.VarChar))
        '        ilist.Add(New SqlParameter("@Ry_Age", SqlDbType.Decimal))
        '        ilist.Add(New SqlParameter("@Ry_Address", SqlDbType.VarChar))
        '        ilist.Add(New SqlParameter("@Ry_Sfzh", SqlDbType.VarChar))
        '        ilist.Add(New SqlParameter("@Ry_Memo", SqlDbType.VarChar))
        '        ilist.Add(New SqlParameter("@Mz_Jffs", SqlDbType.VarChar))
        '        ilist.Add(New SqlParameter("@Mz_YpMoney", SqlDbType.Decimal))
        '        ilist.Add(New SqlParameter("@Mz_XmMoney", SqlDbType.Decimal))
        '        ilist.Add(New SqlParameter("@Mz_Money", SqlDbType.Decimal))
        '        ilist.Add(New SqlParameter("@Mz_Original_Money", SqlDbType.Decimal))
        '        ilist.Add(New SqlParameter("@Mz_Code", SqlDbType.VarChar))
        '
        '        para = ilist.ToArray()
        '
        '        Insert_String = "Insert Into Mz(Yy_Code,Yf_Code,Ks_Code,Ys_Code,Mz_Date,Mz_Time,Jsr_Code,Jb_Code,Jb_Name,Bxlb_Code,Ry_YlCode,Ry_Name,Ry_Sex,Ry_Age,Ry_Address,Ry_Sfzh,Ry_Memo,Mz_Jffs,Mz_YpMoney,Mz_XmMoney,Mz_Money,Mz_Original_Money,Mz_Code)Values(@Yy_Code,@Yf_Code,@Ks_Code,@Ys_Code,@Mz_Date,@Mz_Time,@Jsr_Code,@Jb_Code,@Jb_Name,@Bxlb_Code,@Ry_YlCode,@Ry_Name,@Ry_Sex,@Ry_Age,@Ry_Address,@Ry_Sfzh,@Ry_Memo,@Mz_Jffs,@Mz_YpMoney,@Mz_XmMoney,@Mz_Money,@Mz_Original_Money,@Mz_Code)"
        '        Update_String = "Update Mz Set Yy_Code=@Yy_Code,Yf_Code=@Yf_Code,Ks_Code=@Ks_Code,Ys_Code=@Ys_Code,Mz_Date=@Mz_Date,Mz_Time=@Mz_Time,Jsr_Code=@Jsr_Code,Jb_Code=@Jb_Code,Jb_Name=@Jb_Name,Bxlb_Code=@Bxlb_Code,Ry_YlCode=@Ry_YlCode,Ry_Name=@Ry_Name,Ry_Sex=@Ry_Sex,Ry_Age=@Ry_Age,Ry_Address=@Ry_Address,Ry_Sfzh=@Ry_Sfzh,Ry_Memo=@Ry_Memo,Mz_Jffs=@Mz_Jffs,Mz_YpMoney=@Mz_YpMoney,Mz_XmMoney=@Mz_XmMoney,Mz_Money=@Mz_Money,Mz_Original_Money=@Mz_Original_Money Where Mz_Code=@Mz_Code "
        '
        '        For I = 0 To para.Length - 1
        '            para(I).Value = V_Row.Item(Mid(para(I).ParameterName, 2))
        '        Next


        _mdlmz = Common.DataTableToList.ToModel(Of Model.MdlMz)(V_Row)
        Try
            If Rinsert = True Then
                '                HisVar.HisVar.Sqldal.ExecuteSql(Insert_String, para)
                _bllmz.Add(_mdlmz, Nothing)
                RZbtb.Rows.Add(V_Row)
                Rrow = V_Row
                Rrow.AcceptChanges()
                Rinsert = False
                Rtdbgrid.MoveLast()
            Else
                '                HisVar.HisVar.Sqldal.ExecuteSql(Update_String, para)
                _bllmz.Update(_mdlmz, Nothing)
                Rrow.AcceptChanges()
            End If

        Catch ex As Exception
            MsgBox(ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
        End Try
        C1TrueDBGrid1.Select()

    End Sub




#End Region
#Region "控件__动作"
#Region "其它__控件"

    Private Sub Comm_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.Click, Comm2.Click, Comm3.Click
        If sender.tag = "保存" Or sender.tag = "数据结算" Or sender.tag = "办卡充值" Then
            If XmTextBox.Text.Trim = "" Then
                Beep()
                MsgBox("门诊患者姓名不能为空!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                XmTextBox.Select()
                Exit Sub
            End If

            If SfzhTextBox.Text <> "" Then
                If Len(SfzhTextBox.Text) <> 15 And Len(SfzhTextBox.Text) <> 18 Then
                    MsgBox("请输入有效身份证号！", MsgBoxStyle.Critical, "提示")
                    SfzhTextBox.Select()
                    Exit Sub
                End If
                Dim R As Regex
                R = New Regex("^\d{18}$|^\d{17}[a-zA-Z]{1}$|^\d{15}$")
                If R.IsMatch(SfzhTextBox.Text) = False Then
                    MsgBox("请输入有效身份证号！", MsgBoxStyle.Critical, "提示")
                    SfzhTextBox.Select()
                    Exit Sub
                End If
            End If

            If ComboYs1.SelectedValue = "" Then
                Beep()
                MsgBox("医生编码不能为空!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                ComboYs1.Select()
                Exit Sub
            End If
            If ComboKs1.SelectedValue = "" Then
                Beep()
                MsgBox("科室编码不能为空!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                ComboKs1.Select()
                Exit Sub
            End If

            If ComboYf1.SelectedValue = "" Then
                Beep()
                MsgBox("药房编码不能为空!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                ComboYf1.Select()
                Exit Sub
            End If

            If NlC1NumericEdit.Value IsNot DBNull.Value AndAlso NlC1NumericEdit.Value < 0 Then
                Beep()
                MsgBox("年龄不能小于0!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                NlC1NumericEdit.Select()
                Exit Sub
            End If

        End If


        Select Case sender.tag

            Case "保存"
                If Zb_Save() = False Then                        '主表存盘
                    Exit Sub
                End If              
                C1TrueDBGrid1.Select()
            Case "取消"
                Me.Close()
            Case "数据结算"
                If Zb_Save() = False Then                        '主表存盘
                    Exit Sub
                End If

                If My_Dataset.Tables("药品卫材").Rows.Count = 0 And My_Dataset.Tables("诊疗项目").Rows.Count = 0 Then
                    MsgBox("没有要结算的数据！", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    Exit Sub
                End If

                Dim V_YpMoney As Decimal = HisVar.HisVar.Sqldal.GetSingle("select Isnull(Sum(Mz_Money),0) from Mz_Yp where Mz_Code='" & lblMz_Code.Text & "'")
                Dim V_XmMoney As Decimal = HisVar.HisVar.Sqldal.GetSingle("select Isnull(Sum(Mz_Money),0) from Mz_Xm where Mz_Code='" & lblMz_Code.Text & "'")
                Dim V_Money As Decimal = HisVar.HisVar.Sqldal.GetSingle("select Isnull(Sum(Mz_Money),0) from Mz where Mz_Code='" & lblMz_Code.Text & "'")
                If V_YpMoney + V_XmMoney <> V_Money Then
                    MsgBox("金额计算有误，请重新点击按钮！", MsgBoxStyle.Information, "提示")
                    Exit Sub
                End If


                If sender.tag = "数据结算" Then
                    '                    Dim vform As New Xs_Mz_Js(Rrow, My_Dataset)
                    '                    If BaseFunc.BaseFunc.CheckOwnForm(Me, vform) = False Then
                    '                        If vform.ShowDialog = DialogResult.Cancel Then
                    '
                    '                        Else
                    '                            Me.Close()
                    '                        End If
                    '                    End If

                    Dim frm As New PayMethod(lblMz_Code.Text.Trim(), Math.Round(_mayReceive, 2, MidpointRounding.AwayFromZero))
                    frm.Owner = Me
                    If frm.ShowDialog() = DialogResult.OK Then
                        _payList = frm._mdlMzPayList
                        _mdlmz.Mz_Jffs = frm.Mz_ffss
                        _mdlmz.Mz_SsMoney = frm.received
                        _mdlmz.Mz_ThMoney = frm.received - _mayReceive


                        If _bllmz.Complete(_payList, _mdlmz, True) Then
                            If (ZTHisPara.PublicConfig.MzAutoDispensing) Then
                                If (_bllMzYp.GetRecordCount($"Mz_Code='{lblMz_Code.Text}'") > 0) Then
                                    _bllmz.Dispen("'" + lblMz_Code.Text + "'", _mdlmz.Yf_Code)
                                End If
                            End If
                            MessageBox.Show("收费成功", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
                            Rrow("Mz_Print") = True
                            Rrow("Mz_Print1") = "是"
                            Rrow("MzCf_Ok") = True
                            Rrow("MzCf_Ok1") = "是"
                            Rrow("Mz_Jffs") = frm.Mz_ffss
                            Rrow("Mz_SsMoney") = _mdlmz.Mz_SsMoney
                            Rrow("Mz_ThMoney") = _mdlmz.Mz_ThMoney
                            PublicFunction.MzFpPrint(_mdlmz, False)
                            Me.Close()
                        End If
                    End If
                End If
        End Select
    End Sub
    Private Sub C1Command1_Click(ByVal sender As Object, ByVal e As C1.Win.C1Command.ClickEventArgs) Handles C1Command1.Click, C1Command2.Click
        C1TrueDBGrid1.Select()
        If sender.text = "乘" Then
            If C1TrueDBGrid1.RowCount = 0 Then Exit Sub
            If C1TrueDBGrid1.Columns("数量").Value = 0 Then Exit Sub
            Dim m_Select As String
            Dim m_Str As String
            m_Str = ""
            Dim V_Yf_Sl As String = "Yf_Sl" & Mid(ComboYf1.SelectedValue, 6)
            m_Select = "select " & V_Yf_Sl & "-mz_yp.Mz_Sl*" & C1NumericEdit1.Value & ",Yp_Name from mz_yp,(SELECT  (" & V_Yf_Sl & "-isnull(Mz_Sl,0)-Isnull(Cf_Sl,0)) As " & V_Yf_Sl & ",V_Ypkc.Xx_Code,Yp_Name From  V_Ypkc left join (select Xx_Code,Sum(Mz_Sl) AS Mz_Sl from mz_yp,Mz where Mz.Mz_Code=mz_yp.Mz_Code and Mz_FyQr=0 And Mz.Mz_code<>'" & lblMz_Code.Text & "' and  Mz.Yy_Code='" & HisVar.HisVar.WsyCode & "' group By Xx_Code ) a on V_Ypkc.Xx_Code=a.Xx_Code Left Join (select Xx_Code,Sum(Cf_Sl) AS Cf_Sl from Bl_Cfyp,Bl_Cf where Bl_Cfyp.Cf_Code=Bl_Cf.Cf_Code and Cf_Qr='否' And Bl_Cf.Yy_Code='" & HisVar.HisVar.WsyCode & "' group By Xx_Code ) b on V_Ypkc.Xx_Code=b.Xx_Code where  Yy_Code='" & HisVar.HisVar.WsyCode & "')b where mz_yp.xx_code=b.xx_code and  mz_code='" & lblMz_Code.Text & "' And  (" & V_Yf_Sl & "-mz_yp.Mz_Sl*" & C1NumericEdit1.Value & ")<0 And Mz_Lb='中草药'"
            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, m_Select, "草药副数", True)

            For Each m_Row In My_Dataset.Tables("草药副数").Rows
                m_Str = m_Str & vbCrLf & m_Row("Yp_Name")
            Next

            If m_Str <> "" Then
                m_Str = m_Str & vbCrLf
                MsgBox("中草药:" & m_Str & "库存不足!", vbCritical, "提示")
                Exit Sub
            End If

            HisVar.HisVar.Sqldal.ExecuteSql("Update Mz_Yp Set Mz_Sl=Mz_Sl*" & C1NumericEdit1.Value & ",Mz_Money=Mz_Sl*" & C1NumericEdit1.Value & "*Mz_Dj Where Mz_Code='" & lblMz_Code.Text & "' And Mz_Lb like '%草药%'")
            Call P_Data_Show()
            Call UpdateCfMoney()
            C1NumericEdit1.Value = 1
        End If

        If sender.text = "除" Then
            If C1TrueDBGrid1.RowCount = 0 Then Exit Sub
            If C1TrueDBGrid1.Columns("数量").Value = 0 Then Exit Sub
            HisVar.HisVar.Sqldal.ExecuteSql("Update Mz_Yp Set Mz_Sl=Mz_Sl/" & C1NumericEdit1.Value & ",Mz_Money=Mz_Sl/" & C1NumericEdit1.Value & "*Mz_Dj Where Mz_Code='" & lblMz_Code.Text & "' And Mz_Lb like '%草药%'")
            Call P_Data_Show()
            Call UpdateCfMoney()
            C1NumericEdit1.Value = 1
        End If
    End Sub

    Private Sub C1Combo8_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles C1Combo8.KeyPress
        If e.KeyChar <> Chr(Keys.Return) Then Exit Sub
        e.Handled = True
        YlCodeTextBox.Select()
    End Sub


#End Region

    Private Sub DoctorCombo_RowChange(ByVal sender As Object, ByVal e As System.EventArgs) Handles ComboYs1.RowChange
        If ComboYs1.WillChangeToValue = "" Then
        Else
            ComboKs1.SelectedValue = ComboYs1.Columns("Ks_Code").Value
        End If
    End Sub

    Private Sub C1Combo6_RowChange(ByVal sender As Object, ByVal e As System.EventArgs) Handles ComboYf1.RowChange
        If ComboYf1.WillChangeToValue = "" Then

        ElseIf ComboYf1.WillChangeToValue = iniOperate.iniopreate.GetINI("Personal", "默认药房", "", HisVar.HisVar.Parapath & "\Config.Ini") Then
            iniOperate.iniopreate.WriteINI("Personal", "默认药房", ComboYf1.SelectedValue, HisVar.HisVar.Parapath & "\Config.Ini")
        Else
            If My_Dataset.Tables("药品卫材") IsNot Nothing Then

                If My_Dataset.Tables("药品卫材").Rows.Count = 0 Then
                Else
                    If My_Dataset.HasChanges(DataRowState.Deleted) = True Then
                        If My_Dataset.Tables("药品卫材").Rows.Count = My_Dataset.Tables("药品卫材").GetChanges(DataRowState.Deleted).Rows.Count Then
                        Else
                            ComboYf1.SelectedValue = iniOperate.iniopreate.GetINI("Personal", "默认药房", "", HisVar.HisVar.Parapath & "\Config.Ini")
                            MsgBox("请先删除药品再更换药房！", MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
                            Exit Sub
                        End If
                    Else
                        ComboYf1.SelectedValue = iniOperate.iniopreate.GetINI("Personal", "默认药房", "", HisVar.HisVar.Parapath & "\Config.Ini")
                        MsgBox("请先删除药品再更换药房！", MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
                        Exit Sub
                    End If
                End If

            End If

            iniOperate.iniopreate.WriteINI("Personal", "默认药房", ComboYf1.SelectedValue, HisVar.HisVar.Parapath & "\Config.Ini")
        End If
    End Sub


#Region "Combo8动作"

    Private Sub C1Combo8_RowChange(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1Combo8.RowChange
        If C1Combo8.WillChangeToValue = "" Then
            XmTextBox.Text = ""
            SfzhTextBox.Text = ""
            SingleSex1.SelectedValue = 0
            AddressTextBox.Text = ""
            ComboBxlb1.SelectedValue = 0
            YlCodeTextBox.Text = ""
            ComboKs1.SelectedValue = -1
            ComboYs1.SelectedValue = -1
            NlC1NumericEdit.Value = DBNull.Value
        Else
            XmTextBox.Text = C1Combo8.Columns("Ry_Name").Value & ""
            SfzhTextBox.Text = C1Combo8.Columns("Ry_Sfzh").Value & ""
            SingleSex1.Text = C1Combo8.Columns("Ry_Sex").Value & ""
            AddressTextBox.Text = C1Combo8.Columns("Ry_Address").Value & ""
            ComboBxlb1.SelectedValue = C1Combo8.Columns("Bxlb_Code").Value & ""
            YlCodeTextBox.Text = C1Combo8.Columns("Ry_YlCode").Value & ""
            ComboKs1.SelectedValue = C1Combo8.Columns("Ks_Code").Value & ""
            NlC1NumericEdit.Value = C1Combo8.Columns("Ry_Age").Value
        End If
    End Sub

    Private Sub C1Combo8_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles C1Combo8.KeyUp

        If (e.KeyValue > 47 And e.KeyValue < 58) Or (e.KeyValue > 96 And e.KeyValue < 123) Or (e.KeyValue > 64 And e.KeyValue < 91) Or (e.KeyValue = 8) Or (e.KeyValue = 189) Or (e.KeyValue = 190) Then
            Dim s As String = C1Combo8.Text
            If C1Combo8.Text = "" Then
                C1Combo8.DataSource.RowFilter = ""
            Else
                C1Combo8.DataSource.RowFilter = "Ry_Jc like '*" & C1Combo8.Text.Replace("*", "[*]") & "*'"
            End If
            If (e.KeyValue = 8) Then
                If C1Combo8.Text <> "" Then
                    C1Combo8.DroppedDown = False
                    C1Combo8.DroppedDown = True
                Else
                    C1Combo8.DroppedDown = False
                End If
            End If

            C1Combo8.Text = s
            C1Combo8.SelectionStart = C1Combo8.Text.Length
            C1Combo8.SelectionLength = 0
        End If

    End Sub

    Private Sub C1Combo8_KeyDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles C1Combo8.KeyDown
        If e.KeyValue = 13 Then
            If C1Combo8.WillChangeToIndex < 1 Then
                If (CType(C1Combo8.DataSource, DataView).Count) = 0 Then
                    MsgBox("患者: '" + Me.C1Combo8.Text + "' 不存在！", MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
                    System.Windows.Forms.SendKeys.Send("^{A}")
                    Exit Sub
                End If
                C1Combo8.SelectedIndex = -1
                C1Combo8.SelectedIndex = 0
            Else
                Dim index As Integer
                index = C1Combo8.WillChangeToIndex
                C1Combo8.SelectedIndex = -1
                C1Combo8.SelectedIndex = index
            End If
            e.Handled = True
            System.Windows.Forms.SendKeys.Send("{Tab}")
        End If
    End Sub

#End Region


#Region "DBGrid动作"

    Private Sub C1TrueDBGrid1_MouseDown(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles C1TrueDBGrid1.MouseDown
        If e.Button = MouseButtons.Right Then
            If Trim(XmTextBox.Text) = "" Then
                Beep()
                MsgBox("病人姓名不能为空，按任意键返回!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                XmTextBox.Select()
            ElseIf ComboYs1.SelectedValue = "" Then
                Beep()
                MsgBox("医生名称不能为空,按任意键返回!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                ComboYs1.Select()

            ElseIf ComboKs1.SelectedValue = "" Then
                Beep()
                MsgBox("科室名称不能为空,按任意键返回!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                ComboKs1.Select()
            ElseIf ComboYf1.SelectedValue = "" Then
                Beep()
                MsgBox("药房名称不能为空,按任意键返回!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                ComboYf1.Select()
            Else
                Call Cb_Edit()
            End If
        End If
    End Sub


    Private Sub C1TrueDBGrid1_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles C1TrueDBGrid1.KeyDown
        Select Case e.KeyCode

            Case Keys.Return
                If Trim(XmTextBox.Text) = "" Then
                    Beep()
                    MsgBox("病人姓名不能为空，按任意键返回!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    XmTextBox.Select()
                ElseIf ComboYs1.SelectedValue = "" Then
                    Beep()
                    MsgBox("医生名称不能为空,按任意键返回!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    ComboYs1.Select()

                ElseIf ComboKs1.SelectedValue = "" Then
                    Beep()
                    MsgBox("科室名称不能为空,按任意键返回!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    ComboKs1.Select()
                ElseIf ComboYf1.SelectedValue = "" Then
                    Beep()
                    MsgBox("药房名称不能为空,按任意键返回!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    ComboYf1.Select()
                Else
                    Call Cb_Edit()
                End If

            Case Keys.Delete


                If (C1TrueDBGrid1.Row + 1) > C1TrueDBGrid1.RowCount Then
                Else
                    Cb_Row = Cb_Cm.List(C1TrueDBGrid1.Row).Row
                    If RadioButton1.Checked = True Then
                        If MsgBox("是否删除:" + Me.C1TrueDBGrid1.Columns("Yp_Name").Value + " ", MsgBoxStyle.Information + MsgBoxStyle.OkCancel, "提示:") = MsgBoxResult.Cancel Then Exit Sub
                        HisVar.HisVar.Sqldal.ExecuteSql("Delete from Mz_Yp where Mz_Id='" & Cb_Row.Item("Mz_Id") & "'")
                        C1TrueDBGrid1.Delete()
                        Cb_Row.AcceptChanges()
                    Else
                        If (Cb_Row("Templet_Code") & "").ToString().Trim() = "" Then
                            If MsgBox("是否删除:" + Cb_Row.Item("Xm_Name") + " ", MsgBoxStyle.Information + MsgBoxStyle.OkCancel, "提示:") = MsgBoxResult.Cancel Then Exit Sub
                            HisVar.HisVar.Sqldal.ExecuteSql("Delete from Mz_Xm where Mz_Id='" & Cb_Row.Item("Mz_Id") & "'")
                            C1TrueDBGrid1.Delete()
                            Cb_Row.AcceptChanges()
                        Else
                            If MsgBox(" " + Cb_Row.Item("Xm_Name") + " 由项目模板生成，是否删除该模板下所有项目？", MsgBoxStyle.Information + MsgBoxStyle.OkCancel, "提示:") = MsgBoxResult.Cancel Then Exit Sub
                            HisVar.HisVar.Sqldal.ExecuteSql("Delete from Mz_Xm where Mz_Code='" & Cb_Row.Item("Mz_Code") & "' and Templet_Code='" & Cb_Row.Item("Templet_Code") & "'")
                            P_Data_Show()
                        End If
                    End If
                    Call F_Sum()
                    Call UpdateCfMoney()
                End If
        End Select

    End Sub


#End Region

#End Region

#Region "从表__编辑"


    Private Sub Init_Data1()     '从表数据
        Dim Str_Insert As String = "Insert Into Mz_Xm(Yy_Code,Mz_Code,Xm_Code,Mz_Sl,Mz_Dj,Mz_Money,Xm_Discount,Xm_Original_Money,Mz_Lb,Templet_Code)Values(@Yy_Code,@Mz_Code,@Xm_Code,@Mz_Sl,@mz_Dj,@Mz_Money,@Xm_Discount,@Xm_Original_Money,@Mz_Lb,@Templet_Code)"
        Dim Str_Update As String = "Update Mz_Xm Set Xm_Code=@Xm_Code,Mz_Sl=@Mz_Sl,Mz_Dj=@Mz_Dj,Mz_Money=@Mz_Money,Xm_Discount=@Xm_Discount,Xm_Original_Money=@Xm_Original_Money,Mz_Lb=@Mz_Lb Where Mz_Id=@Old_Mz_Id "
        With My_Adapter

            .InsertCommand = New SqlCommand(Str_Insert, My_Cn)
            With .InsertCommand.Parameters
                .Add("@Yy_Code", SqlDbType.VarChar, 4)
                .Add("@Mz_Code", SqlDbType.VarChar, 14)                 '出库编码
                .Add("@Xm_Code", SqlDbType.VarChar, 12)                 '类别编码
                .Add("@Mz_Sl", SqlDbType.Decimal)                   '入库数量
                .Add("@Mz_Dj", SqlDbType.Decimal)                   '入库单价
                .Add("@Mz_Money", SqlDbType.Decimal, 10, 2)
                .Add("@Xm_Discount", SqlDbType.Decimal, 10, 2)
                .Add("@Xm_Original_Money", SqlDbType.Decimal, 10, 2)
                .Add("@Mz_Lb", SqlDbType.VarChar, 50)
                .Add("@Templet_Code", SqlDbType.VarChar, 8)
            End With

            .UpdateCommand = New SqlCommand(Str_Update, My_Cn)
            With .UpdateCommand.Parameters
                .Add("@Xm_Code", SqlDbType.VarChar, 12)                 '类别编码
                .Add("@Mz_Sl", SqlDbType.Decimal)                   '入库数量
                .Add("@Mz_Dj", SqlDbType.Decimal)                   '入库单价
                .Add("@Mz_Money", SqlDbType.Decimal, 10, 2)
                .Add("@Xm_Discount", SqlDbType.Decimal, 10, 2)
                .Add("@Xm_Original_Money", SqlDbType.Decimal, 10, 2)
                .Add("@Mz_Lb", SqlDbType.VarChar, 50)
                .Add("@Old_Mz_Id", SqlDbType.Int, 4, "Mz_Id")
            End With

        End With
    End Sub


#End Region

#Region "自定义函数"

    Private Sub Cb_Edit()

        '判断主表是否存在

        If (C1TrueDBGrid1.Row + 1) > C1TrueDBGrid1.RowCount Then
            V_Insert = True
        Else
            V_Insert = False
            Cb_Row = Cb_Cm.List(C1TrueDBGrid1.Row).Row
        End If

        If Zb_Save() = False Then                        '主表存盘
            Exit Sub
        End If

        If HisVar.HisVar.Sqldal.GetSingle("Select Isnull(Mz_Print,0)|Isnull(MzCf_Ok,0)|Isnull(Mz_FyQr,0) from Mz where  Mz_Code='" & Rrow.Item("Mz_Code") & "'") = True Then
            MsgBox("处方状态有误，请先关闭窗口，刷新后从新打开进行明细录入！", MsgBoxStyle.Information, "提示")
            Exit Sub
        End If


        Select Case Yp_Lb
            Case "药品卫材"
                'Dim vform As New Xs_Mz31(Me, V_Insert, Cb_Row, My_Dataset.Tables(Yp_Lb), C1TrueDBGrid1, Rrow.Item("Mz_Code"), ComboYf1.SelectedValue)
                'If BaseFunc.BaseFunc.CheckOwnForm(Me, vform) = False Then
                '    vform.ShowDialog()
                'End If
                Dim vform As New ZTHisOutpatient.MzCfYp(lblMz_Code.Text, ComboYf1.SelectedValue+"", V_Insert, Cb_Row, My_Dataset.Tables("药品卫材"))
                vform.MyTransmitTxt = _transmitTxt
                vform.ShowDialog()
            Case "诊疗项目"
                If V_Insert = False Then
                    If Cb_Row("Templet_Code").Trim() <> "" Then
                        MsgBox("模板里的项目不允许修改", MsgBoxStyle.Information, "提示")
                        Exit Sub
                    End If
                End If
'                Dim vform As New Xs_Mz34(Me, V_Insert, Rrow, Cb_Row, My_Dataset.Tables(Yp_Lb), C1TrueDBGrid1, My_Adapter)
'                If BaseFunc.BaseFunc.CheckOwnForm(Me, vform) = False Then
'                    vform.ShowDialog()
'                End If
                Dim vform As New MzCfXm(lblMz_Code.Text, ComboYf1.SelectedValue + "", V_Insert, Cb_Row, My_Dataset.Tables("诊疗项目"))
                vform.MyTransmitTxt = _transmitTxt
                vform.ShowDialog()
        End Select
        ''重新计算
        Call P_Data_Show()
        C1TrueDBGrid1.Select()
        C1TrueDBGrid1.MoveLast()
        Call UpdateCfMoney()
    End Sub

    Public Overrides Sub F_Sum()
        Dim V_YpWc As Double = IIf(My_Dataset.Tables("药品卫材").Compute("Sum(Mz_Money)", "") Is DBNull.Value, 0, My_Dataset.Tables("药品卫材").Compute("Sum(Mz_Money)", ""))
        Dim V_Xm As Double = IIf(My_Dataset.Tables("诊疗项目").Compute("Sum(Mz_Money)", "") Is DBNull.Value, 0, My_Dataset.Tables("诊疗项目").Compute("Sum(Mz_Money)", ""))
        T_Label5.Text = "费用总额：" & Trim(Format(V_YpWc + V_Xm, "########0.00") + "元") & "   其中  药品卫材：" & Trim(Format(V_YpWc, "########0.00") + "元") & "  诊疗项目：" & Trim(Format(V_Xm, "#######0.00") + "元")
    End Sub
    Private Sub UpdateCfMoney()
        Dim mdlMz As New MdlMz
        mdlMz = _bllMz.GetModel(Rrow("Mz_Code"))
        mdlMz.Mz_YpMoney = _bllMzYp.GetCfYpMoney(Rrow("Mz_Code"))
        mdlMz.Mz_XmMoney = _bllMzXm.GetCfXmMoney(Rrow("Mz_Code"))
        mdlMz.Mz_Money = _bllMzYp.GetCfYpMoney(Rrow("Mz_Code")) + _bllMzXm.GetCfXmMoney(Rrow("Mz_Code"))
        _bllMz.UpdateCfMoney(mdlMz)
    End Sub
    Private Sub GridMove(moveType As String)
        If C1TrueDBGrid1.RowCount = 0 Then
            Return
        End If
        Select Case moveType
            Case "最前"
                C1TrueDBGrid1.MoveFirst()
                Exit Select
            Case "上移"
                C1TrueDBGrid1.MovePrevious()
                Exit Select
            Case "下移"
                C1TrueDBGrid1.MoveNext()
                Exit Select
            Case "最后"
                C1TrueDBGrid1.MoveLast()
                Exit Select
            Case Else
                Dim index As Integer
                If Integer.TryParse(moveType, index) Then
                    C1TrueDBGrid1.Row = index
                End If
                Exit Select
        End Select
        Call F_Sum()
        Call UpdateCfMoney()
    End Sub
    Private Function F_MaxCode(ByVal V_MaxCode As String)   '出库编码
        Dim My_Cc As New BaseClass.C_Cc()
        Dim My_Cd As New BaseClass.C_Cc()
        Dim V_Code As String = V_MaxCode

        My_Cc.Get_MaxCode("Mz", "Mz_Code", 14, "Left(Mz_Code,10)", HisVar.HisVar.WsyCode & V_MaxCode)
        My_Cd.Get_MaxCode("Mz_Sum", "Mz_Code", 14, "Left(Mz_Code,10)", HisVar.HisVar.WsyCode & V_MaxCode)
        If My_Cc.编码 <= My_Cd.编码 Then
            F_MaxCode = My_Cd.编码
        Else
            F_MaxCode = My_Cc.编码
        End If

        If Mid(F_MaxCode, 1, 6) = "000000" Then F_MaxCode = V_MaxCode + Mid(My_Cc.编码, 7)
        Return F_MaxCode
    End Function

    Private Sub GenCzd(ByRef mdlMzInfusion As MdlMz_Infusion, ByRef listGroup1 As List(Of MdlMz_InfusionGroup1), ByRef listGroup2 As List(Of MdlMz_InfusionGroup2), ByRef listMzCzd As List(Of MdlMz_Czd))
        mdlMzInfusion.Mz_Code = lblMz_Code.Text
        For Each mdl As MdlMz_Czd In listMzCzd
            mdl.Mz_Code = lblMz_Code.Text
        Next
        For Each mdl As MdlMz_InfusionGroup1 In listGroup1
            mdl.Mz_Code = lblMz_Code.Text
        Next
        For Each mdl As MdlMz_InfusionGroup2 In listGroup2
            mdl.Mz_Code = lblMz_Code.Text
        Next
    End Sub
#End Region

#Region "中西药、草药、卫材切换"

    Private Sub RadioButton1_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles RadioButton1.CheckedChanged, RadioButton4.CheckedChanged

        If Me.Visible = False Then Exit Sub

        If Rinsert = True Then           '主表
            lblMz_Code.Text = F_MaxCode(Format(Rdate, "yyMMdd"))
        End If


        If RadioButton1.Checked = True Then
            C1TrueDBGrid_Init_Zxy()
            Yp_Lb = Mid(RadioButton1.Text, 4)
            C1Command1.Enabled = True
            C1Command2.Enabled = True
            C1NumericEdit1.Enabled = True
        End If
        If RadioButton4.Checked = True Then
            Call Init_Data1()
            C1TrueDBGrid_Init_Xm()
            Yp_Lb = Mid(RadioButton4.Text, 4)
            C1Command1.Enabled = False
            C1Command2.Enabled = False
            C1NumericEdit1.Enabled = False
        End If
        Call P_Data_Show()
        C1TrueDBGrid1.Select()
    End Sub


#End Region

#Region "中西药、草药、卫材C1Truedbgrid初始化"

    Private Sub C1TrueDBGrid_Init_Zxy()
        '初始化TDBGrid
        Dim My_Grid As New BaseClass.C_Grid(Me.C1TrueDBGrid1)
        With My_Grid
            .Init_Grid()
            .AllAddNew(True)
            .AllDelete(True)
            .AllUpdate(False)
            .AllSort(False)

            .P_Dock(BaseClass.C_Grid.Dock.Fill)
            .Init_Column("药品Id", "Mz_Id", 0, "左", "")
            .Init_Column("药品名称", "Yp_Name", 200, "左", "")
            .Init_Column("规格", "Mx_Gg", 125, "左", "")
            .Init_Column("产地", "Mx_Cd", 140, "左", "")
            .Init_Column("有效期", "Yp_Yxq", 75, "中", "yyyy-MM-dd")
            .Init_Column("数量", "Mz_Sl", 60, "右", "###,###,##0.00##")
            .Init_Column("单价", "Mz_Dj", 60, "右", "###,###,##0.00####")
            .Init_Column("金额", "Mz_Money", 70, "右", "###,###,##0.00##")
            .Init_Column("类别", "Mz_Lb", 120, "中", "")
            .Init_Column("国家医保编码", "医疗目录编码", 100, "左", "")
        End With

    End Sub

    Private Sub C1TrueDBGrid_Init_Xm()
        '初始化TDBGrid
        Dim My_Grid As New BaseClass.C_Grid(Me.C1TrueDBGrid1)
        With My_Grid
            .Init_Grid()
            .AllAddNew(True)
            .AllDelete(True)
            .AllUpdate(False)
            .AllSort(False)

            .P_Dock(BaseClass.C_Grid.Dock.Fill)
            .Init_Column("项目名称", "Xm_Name", 430, "左", "")
            .Init_Column("项目单位", "Xm_Dw", 90, "左", "")
            .Init_Column("数量", "Mz_Sl", 70, "右", "###,###,##0")
            .Init_Column("单价", "Mz_Dj", 70, "右", "###,###,##0.00")
            .Init_Column("金额", "Mz_Money", 70, "右", "###,###,##0.00")
            .Init_Column("类别", "Mz_Lb", 120, "中", "###,###,##0.00")
            .Init_Column("国家医保编码", "医疗目录编码", 70, "左", "")
        End With
    End Sub
#End Region

#Region "输入法设置"
    '中文
    Private Sub C1TextBox7_GotFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles XmTextBox.GotFocus, AddressTextBox.GotFocus
        InputLanguage.CurrentInputLanguage = HisVar.HisVar.InputCode
    End Sub

    Private Sub C1Numeric1_KeyDown(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Combo8.GotFocus, NlC1NumericEdit.GotFocus, ComboYs1.GotFocus
        InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages(0)
    End Sub
    Private Sub BtnSaveTemplate_Click(sender As Object, e As EventArgs) Handles BtnSaveTemplate.Click
        Dim mdlmztemplate As New MdlMz_Template
        mdlmztemplate = Common.DataTableToList.ToModel(Of MdlMz_Template)(Rrow)
        Dim list As New List(Of MdlMz_TemplateMx)
        list = Common.DataTableToList.ToList(Of MdlMz_TemplateMx)(My_Dataset.Tables("药品卫材"))
        For Each yp As MdlMz_TemplateMx In list
            yp.Type = "Yp"
            yp.Mx_Code = yp.Xx_Code.Substring(0, 11)
            yp.Yf_Code = ComboYf1.SelectedValue + ""
        Next
        For Each xmrow As DataRow In My_Dataset.Tables("诊疗项目").Rows
            Dim xm As New MdlMz_TemplateMx
            xm.Type = "Xm"
            xm.Yp_Name = xmrow("Xm_Name")
            xm.Mx_Code = xmrow("Xm_Code")
            xm.Mz_Sl = xmrow("Mz_Sl")
            xm.Mx_Gg = xmrow("Xm_Dw")
            list.Add(xm)
        Next
        Dim frm As New ZTHisOutpatient.MzTemplate2(mdlmztemplate, Nothing, MzData1.CfTemplatedt, list)
        frm.ShowDialog()
    End Sub
    Private Sub ToolBarReadCard1_CmdReadCardClick(patientInfo As DTO.DtoPatientInfo) Handles ToolBarReadCard1.CmdReadCardClick
        Dim chs2Spell As New Common.Chs2Spell()
        'JcLabel1.Text = chs2Spell.GetPy(XmTextBox.Text)
        XmTextBox.Text = patientInfo.Pat_Name
        SfzhTextBox.Text = patientInfo.ID_No
        SingleSex1.Text = If(patientInfo.Pat_Sex = "1", "男", "女")
        AddressTextBox.Text = patientInfo.Pat_Address
        'DtcboNation.Text = patientInfo.Pat_Nation
        Common.ModelTools.ToNewModel(patientInfo, _patientInfo)
        Dim dt As DateTime
        If DateTime.TryParse(SfzhTextBox.Text.Substring(6, 4) + "-" + SfzhTextBox.Text.Substring(10, 2) + "-" + SfzhTextBox.Text.Substring(12, 2), dt) Then
            DateRyBirthday.Value = dt
            NlC1NumericEdit.Text = DateTime.Now.Year - dt.Year
        End If
        If patientInfo.mdtrt_cert_type = "03" Then
            YlCodeTextBox.Text = patientInfo.mdtrt_cert_no
        End If
        Dim bxlbdt As DataTable
        Dim bllbxlb As New BLL.BllZd_Bxlb
        If patientInfo.insutype = "310" Then
            bxlbdt = bllbxlb.GetList("BxLb_Name='城镇职工'").Tables(0)
            If bxlbdt.Rows.Count > 0 Then
                ComboBxlb1.SelectedValue = bxlbdt.Rows(0).Item("Bxlb_Code")
            End If
        Else
            bxlbdt = bllbxlb.GetList("BxLb_Name in ('城镇居民','城乡居民')").Tables(0)
            If bxlbdt.Rows.Count > 0 Then
                ComboBxlb1.SelectedValue = bxlbdt.Rows(0).Item("Bxlb_Code")
            End If
        End If
    End Sub

    Private Sub DateRyBirthday_Validated(sender As Object, e As EventArgs) Handles DateRyBirthday.Validated
        If DateRyBirthday.Value IsNot DBNull.Value Then
            NlC1NumericEdit.Value = DateTime.Now.Year - Convert.ToInt32((Convert.ToDateTime(DateRyBirthday.Value)).ToString("yyyy"))
        End If
    End Sub

    Private Sub SfzhTextBox_Validated(sender As Object, e As EventArgs) Handles SfzhTextBox.Validated
        If SfzhTextBox.Text.Trim() <> "" Then
            If Common.RegexHelper.IsIDCard(SfzhTextBox.Text) Then
                Dim dt As DateTime
                If DateTime.TryParse(SfzhTextBox.Text.Substring(6, 4) + "-" + SfzhTextBox.Text.Substring(10, 2) + "-" + SfzhTextBox.Text.Substring(12, 2), dt) Then
                    DateRyBirthday.Value = dt
                    NlC1NumericEdit.Value = DateTime.Now.Year - dt.Year
                End If
            Else
                NlC1NumericEdit.[Select]()
                MessageBox.Show("请填写正确的身份证号码!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
            End If
        End If
    End Sub

    Private Sub MzData1_LsCfMouseDoubleClick(mdlMzYp As MdlMz_Yp, mdlMzXm As MdlMz_Xm) Handles MzData1.LsCfMouseDoubleClick
        If Trim(XmTextBox.Text) = "" Then
            Beep()
            MsgBox("病人姓名不能为空，按任意键返回!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
            XmTextBox.Select()
        ElseIf ComboYs1.SelectedValue = "" Then
            Beep()
            MsgBox("医生名称不能为空,按任意键返回!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
            ComboYs1.Select()

        ElseIf ComboKs1.SelectedValue = "" Then
            Beep()
            MsgBox("科室名称不能为空,按任意键返回!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
            ComboKs1.Select()
        ElseIf ComboYf1.SelectedValue = "" Then
            Beep()
            MsgBox("药房名称不能为空,按任意键返回!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
            ComboYf1.Select()
        Else
            If Zb_Save() = False Then                        '主表存盘
                Exit Sub
            End If
            Dim bllMz_Yp = New BllMz_Yp
            Dim bllMz_Xm = New BllMz_Xm
            If mdlMzYp IsNot Nothing Then
                mdlMzYp.Mz_Code = lblMz_Code.Text
                mdlMzYp.Group_Id = bllMz_Yp.GetGroupId(mdlMzYp.Mz_Code)
                Dim id As Integer = bllMz_Yp.Add(mdlMzYp)
                MzData1.DicMz_Id(mdlMzYp.Mz_Id) = id
            End If
            If mdlMzXm IsNot Nothing Then
                mdlMzXm.Mz_Code = lblMz_Code.Text
                bllMz_Xm.Add(mdlMzXm)
            End If
            P_Data_Show()
            F_Sum()
            UpdateCfMoney()
        End If
    End Sub

    Private Sub MzData1_PatientInfoClick(mdlMz As MdlMz) Handles MzData1.PatientInfoClick
        XmTextBox.Text = mdlMz.Ry_Name
        SfzhTextBox.Text = mdlMz.Ry_Sfzh
        AddressTextBox.Text = mdlMz.Ry_Address
        TelText.Text = mdlMz.Ry_Tell
    End Sub
    Private Sub MzData1_CfTemplateMouseDoubleClick(Yf_Code As String, listYp As List(Of MdlMz_Yp), listXm As List(Of MdlMz_Xm)) Handles MzData1.CfTemplateMouseDoubleClick
        If String.IsNullOrEmpty(Yf_Code) = False Then
            ComboYf1.SelectedValue = Yf_Code
        End If
        If Trim(XmTextBox.Text) = "" Then
            Beep()
            MsgBox("病人姓名不能为空，按任意键返回!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
            XmTextBox.Select()
        ElseIf ComboKs1.SelectedValue = "" Then
            Beep()
            MsgBox("科室名称不能为空,按任意键返回!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
            ComboKs1.Select()
        ElseIf ComboYf1.SelectedValue = "" Then
            Beep()
            MsgBox("药房名称不能为空,按任意键返回!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
            ComboYf1.Select()
        Else
            If Zb_Save() = False Then                        '主表存盘
                Exit Sub
            End If
            Dim bllMz_Yp = New BllMz_Yp
            Dim bllMz_Xm = New BllMz_Xm
            For Each mdlMzYp As MdlMz_Yp In listYp
                mdlMzYp.Mz_Code = lblMz_Code.Text
                mdlMzYp.Group_Id = bllMz_Yp.GetGroupId(mdlMzYp.Mz_Code)
                bllMz_Yp.Add(mdlMzYp)
            Next
            For Each mdlMzXm As MdlMz_Xm In listXm
                mdlMzXm.Mz_Code = lblMz_Code.Text
                bllMz_Xm.Add(mdlMzXm)
            Next
            P_Data_Show()
            F_Sum()
            UpdateCfMoney()
        End If
    End Sub
    Private Sub C1DockingTab1_AutoHidingChanged(sender As Object, e As EventArgs) Handles C1DockingTab1.AutoHidingChanged
        Dim autohide As Boolean = C1DockingTab1.AutoHiding
        Common.WinFormVar.Var.IniFileHelper.IniWriteValue("参数", "门诊录入辅助框", autohide)
    End Sub

#End Region

End Class