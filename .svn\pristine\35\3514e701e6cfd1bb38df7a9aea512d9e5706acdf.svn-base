﻿/**  版本信息模板在安装目录下，可自行修改。
* M_Jkk_Consume.cs
*
* 功 能： N/A
* 类 名： M_Jkk_Consume
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/7/2 10:06:58   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace ModelOld
{
	/// <summary>
	/// M_Jkk_Consume:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class M_Jkk_Consume
	{
		public M_Jkk_Consume()
		{}
		#region Model
		private int _id;
		private string _consume_lb;
		private string _consume_code;
		private string _ry_name;
		private string _ry_sfzh;
		private decimal? _consume_money;
		private string _jsr_code;
		private string _jkk_code;
		private DateTime? _consume_date;
		private int? _jkkid;
		private string _consume_fs;
		private string _jkklb;
		private string _consume_zt;
		private string _jz_code;
		private string _jz_cwcode;
		/// <summary>
		/// 
		/// </summary>
		public int ID
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Consume_Lb
		{
			set{ _consume_lb=value;}
			get{return _consume_lb;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Consume_Code
		{
			set{ _consume_code=value;}
			get{return _consume_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Ry_Name
		{
			set{ _ry_name=value;}
			get{return _ry_name;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Ry_Sfzh
		{
			set{ _ry_sfzh=value;}
			get{return _ry_sfzh;}
		}
		/// <summary>
		/// 
		/// </summary>
		public decimal? Consume_Money
		{
			set{ _consume_money=value;}
			get{return _consume_money;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Jsr_Code
		{
			set{ _jsr_code=value;}
			get{return _jsr_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Jkk_Code
		{
			set{ _jkk_code=value;}
			get{return _jkk_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public DateTime? Consume_Date
		{
			set{ _consume_date=value;}
			get{return _consume_date;}
		}
		/// <summary>
		/// 
		/// </summary>
		public int? JkkId
		{
			set{ _jkkid=value;}
			get{return _jkkid;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Consume_Fs
		{
			set{ _consume_fs=value;}
			get{return _consume_fs;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string JkkLb
		{
			set{ _jkklb=value;}
			get{return _jkklb;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Consume_Zt
		{
			set{ _consume_zt=value;}
			get{return _consume_zt;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Jz_Code
		{
			set{ _jz_code=value;}
			get{return _jz_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Jz_CwCode
		{
			set{ _jz_cwcode=value;}
			get{return _jz_cwcode;}
		}
		#endregion Model

	}
}

