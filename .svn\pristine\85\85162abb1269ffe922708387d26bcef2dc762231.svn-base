﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="0" />
    <DataSources isList="true" count="0" />
    <Relations isList="true" count="0" />
    <Report isRef="0" />
    <Variables isList="true" count="0" />
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="2" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="2">
        <HeaderBand1 Ref="3" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,20,3.3</ClientRectangle>
          <Components isList="true" count="22">
            <Text5 Ref="4" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,2.2,3.5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text5</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>起始</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text5>
            <Text8 Ref="5" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.5,2.2,6,1.1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10,Bold</Font>
              <Guid>6d90f01f16e34d2abacb6de0654dafa3</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text8</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>长期医嘱</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text8>
            <Text9 Ref="6" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>16.3,2.8,1.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10,Bold</Font>
              <Guid>a4ad7483276f4f9abe47e9669e9f8b5f</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text9</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>医生签字</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text9>
            <Text20 Ref="7" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,2.8,3.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10,Bold</Font>
              <Guid>4158fd95c0124e7c800b8302f7d546ef</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text20</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>日期及时间</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text20>
            <Text17 Ref="8" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>12.8,2.2,7.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10,Bold</Font>
              <Guid>88a2d4e2412b4c4689de2e3ee52ab74a</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text17</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>停止</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text17>
            <Text1 Ref="9" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,20,1.2</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>黑体,18,Regular,Point,False,134</Font>
              <Guid>cbc5f508888542128997844fb5e7e489</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text1</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>临时医嘱单</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text1>
            <Text7 Ref="10" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.5,1.7,2.2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9.75,Regular,Point,False,0</Font>
              <Guid>d79f96271596440c83d92b0b4ea89569</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text7</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{姓名}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text7>
            <Text18 Ref="11" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>10.5,1.7,1.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9.75,Regular,Point,False,0</Font>
              <Guid>99f4e3aec7cd4e6abd1001fc138aa7f6</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text18</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>病历号：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text18>
            <Text12 Ref="12" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>12.1,1.7,2.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9.75,Regular,Point,False,0</Font>
              <Guid>cffbd19fc53d4ae491a473dae68cf725</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text12</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{病历号}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text12>
            <Text11 Ref="13" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.7,1.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9.75,Regular,Point,False,0</Font>
              <Guid>7793ae26ad9044e4b226865f43fb2850</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text11</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>姓名：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text11>
            <Text6 Ref="14" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>3.7,1.7,1.2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9.75,Regular,Point,False,0</Font>
              <Guid>aa4b73ee8cbc49ca934db9659306a175</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text6</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>性别：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text6>
            <Text2 Ref="15" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.9,1.7,1.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9.75,Regular,Point,False,0</Font>
              <Guid>646e0b1bf2aa4ca782b93da2b4f39a24</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text2</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{性别}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text2>
            <Text4 Ref="16" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>6.4,1.7,1,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9.75,Regular,Point,False,0</Font>
              <Guid>cba3467daedd4773b45963456dde59cb</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text4</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>床号：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text4>
            <Text3 Ref="17" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7.4,1.7,3.1,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9.75,Regular,Point,False,0</Font>
              <Guid>04958e3d40bf4bd5bb644c2291f23fb4</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text3</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{床号}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text3>
            <Text2 Ref="18" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>14.8,1.7,1.3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9.75,Regular,Point,False,0</Font>
              <Guid>3d9e9659fd224bd09fba935a21a8f3e4</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text2</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>科室：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text2>
            <Text19 Ref="19" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>16.1,1.7,3.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9.75,Regular,Point,False,0</Font>
              <Guid>366444f8671e47e6984cb7da210cb2f6</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text19</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{科室}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text19>
            <Text31 Ref="20" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>6.4,1.2,1.8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9.75,Regular,Point,False,0</Font>
              <Guid>fe83e22ffdb14673917a4546117ff19e</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text31</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>入院编码：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text31>
            <Text19 Ref="21" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>8.2,1.2,4.8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9.75,Regular,Point,False,0</Font>
              <Guid>5c89cb57987842eda34c9322f40f3d8f</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text19</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{入院编码}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text19>
            <Text16 Ref="22" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9.5,2.2,1.6,1.1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10,Bold</Font>
              <Guid>e3c05430add4449f849c83f5ad92b0fd</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text16</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>医 生
签 字</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text16>
            <Text22 Ref="23" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>17.8,2.8,2.2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10,Bold</Font>
              <Guid>3cc694fd54514413908ffe8ec6814f28</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text22</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>执行护士签字</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text22>
            <Text23 Ref="24" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>12.8,2.8,3.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10,Bold</Font>
              <Guid>edc55d7232ad437c91eb5a3283fcbbd3</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text23</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>日期及时间</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text23>
            <Text13 Ref="25" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>11.1,2.2,1.7,1.1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10,Bold</Font>
              <Guid>a995abf4fb95400a8c301e948b479221</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text13</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>执行护
士签字</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text13>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>HeaderBand1</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
        </HeaderBand1>
        <DataBand1 Ref="26" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,4.5,20,0.6</ClientRectangle>
          <Components isList="true" count="7">
            <Text21 Ref="27" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.5,0,6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11</Font>
              <Guid>6352f8ef5c054bbfa40d0da7a813401b</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text21</Name>
              <Page isRef="2" />
              <Parent isRef="26" />
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="28" type="CustomFormat" isKey="true">
                <StringFormat>0.####</StringFormat>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text21>
            <Text25 Ref="29" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>12.8,0,3.5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11</Font>
              <Guid>c672c0fdc6264619a6108170ff0bbdff</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text25</Name>
              <NullValue> </NullValue>
              <Page isRef="2" />
              <Parent isRef="26" />
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text25>
            <Text14 Ref="30" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,3.5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text14</Name>
              <Page isRef="2" />
              <Parent isRef="26" />
              <Text>2012-12-12 12:12:12</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text14>
            <Text15 Ref="31" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>16.3,0,1.5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11</Font>
              <Guid>e61ea79b15bf4a8e9d9e238404cddadf</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text15</Name>
              <NullValue> </NullValue>
              <Page isRef="2" />
              <Parent isRef="26" />
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text15>
            <Text27 Ref="32" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>17.8,0,2.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11</Font>
              <Guid>77095d2480bf4c9b86f1843f5a138276</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text27</Name>
              <NullValue> </NullValue>
              <Page isRef="2" />
              <Parent isRef="26" />
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text27>
            <Text29 Ref="33" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9.5,0,1.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11</Font>
              <Guid>5cea31de09364308af745ed36d6ff576</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text29</Name>
              <Page isRef="2" />
              <Parent isRef="26" />
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="34" type="CustomFormat" isKey="true">
                <StringFormat>0.####</StringFormat>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text29>
            <Text30 Ref="35" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>11.1,0,1.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11</Font>
              <Guid>d044feeafd2b490c82cd18587c74b5ed</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text30</Name>
              <NullValue> </NullValue>
              <Page isRef="2" />
              <Parent isRef="26" />
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text30>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>报表</DataSourceName>
          <Filters isList="true" count="0" />
          <Name>DataBand1</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Sort isList="true" count="2">
            <value>ASC</value>
            <value>XsMx_Code</value>
          </Sort>
        </DataBand1>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>f9c8eab4601d4d9f89df0e7a12c5106a</Guid>
      <Margins>0.5,0.5,1,1</Margins>
      <Name>Page1</Name>
      <PageHeight>29.7</PageHeight>
      <PageWidth>21</PageWidth>
      <PaperSize>A4</PaperSize>
      <Report isRef="0" />
      <Watermark Ref="36" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page1>
  </Pages>
  <PrinterSettings Ref="37" type="Stimulsoft.Report.Print.StiPrinterSettings" isKey="true" />
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>销售提货单</ReportAlias>
  <ReportChanged>7/26/2013 3:07:06 PM</ReportChanged>
  <ReportCreated>3/22/2012 11:59:29 AM</ReportCreated>
  <ReportFile>C:\Documents and Settings\Administrator\桌面\长期医嘱单.mrt</ReportFile>
  <ReportGuid>d6279d4c2bd744e19376f686deef955f</ReportGuid>
  <ReportName>销售提货单</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2011.2.1026</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class Report : Stimulsoft.Report.StiReport
    {
        public Report()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify#endregion StiReport Designer generated code - do not modify
		
		public	string NumberCn(decimal ANumber) 

		{

			const string cPointCn = "点十百千万十百千亿十百千";

			const string cNumberCn = "零一二三四五六七八九";

			string S = ANumber.ToString();

			if (S == "0") return "" + cPointCn[0];

			if (!S.Contains(".")) S += ".";

			int P = S.IndexOf(".");

			string Result = "";

    

			for (int i = 0; i &lt; S.Length; i++)

			{

				if (P == i)

				{

					Result = Result.Replace("零十零", "零");

					Result = Result.Replace("零百零", "零");

					Result = Result.Replace("零千零", "零");

					Result = Result.Replace("零十", "零");

					Result = Result.Replace("零百", "零");

					Result = Result.Replace("零千", "零");

					Result = Result.Replace("零万", "万");

					Result = Result.Replace("零亿", "亿");

					Result = Result.Replace("亿万", "亿");

					Result = Result.Replace("零点", "点");

				}

				else

				{

					if (P &gt; i)

						Result += "" + cNumberCn[S[i] - '0'] + cPointCn[P - i - 1];

					else Result += "" + cNumberCn[S[i] - '0'];

				}

			}

			if (Result.Substring(Result.Length - 1, 1) == "" + cPointCn[0])

				Result = Result.Remove(Result.Length - 1); // 一点-&gt; 一

    

			if (Result[0] == cPointCn[0])

				Result = cNumberCn[0] + Result; // 点三-&gt; 零点三

 

			if ((Result.Length &gt; 1) &amp;&amp; (Result[1] == cPointCn[1]) &amp;&amp; 

				(Result[0] == cNumberCn[1]))

				Result = Result.Remove(0, 1); // 一十三-&gt; 十三

			return Result;

		}

 

		public	string MoneyCn(decimal ANumber)

		{

			string V_Fs="";
			if (ANumber &lt; 0)
			{
				ANumber = -ANumber;
				V_Fs="负";
			}
			else
			{
				V_Fs = "";
			}

			
			if (ANumber == 0) return "零";

			string Result = NumberCn(Math.Truncate(ANumber * 100) / 100);

			Result = Result.Replace("一", "壹");

			Result = Result.Replace("二", "贰");

			Result = Result.Replace("三", "叁");

			Result = Result.Replace("四", "肆");

			Result = Result.Replace("五", "伍");

			Result = Result.Replace("六", "陆");

			Result = Result.Replace("七", "柒");

			Result = Result.Replace("八", "捌");

			Result = Result.Replace("九", "玖");

			Result = Result.Replace("九", "玖");

			Result = Result.Replace("十", "拾");

			Result = Result.Replace("百", "佰");

			Result = Result.Replace("千", "仟");

			if (Result.Contains("点"))

			{

				int P = Result.IndexOf("点");
				if (P + 3 &gt; Result.Length)
				{
					Result = Result+ "分";
				}
				else

				{
					Result = Result.Insert(P + 3, "分");
				}
				//	Result = Result.Insert(P + 3, "分");

				Result = Result.Insert(P + 2, "角");

				Result = Result.Replace("点", "圆");

				Result = Result.Replace("角分", "角");

				Result = Result.Replace("零分", "");

				Result = Result.Replace("零角", "");

				Result = Result.Replace("分角", "");

				if (Result.Substring(0, 2) == "零圆")

					Result = Result.Replace("零圆", "");

			} else Result += "圆整";

			Result =  V_Fs+Result;

			return Result;

		}

    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>