﻿/**  版本信息模板在安装目录下，可自行修改。
* D_Materials_Check1.cs
*
* 功 能： N/A
* 类 名： D_Materials_Check1
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016-11-26 11:27:50   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using System.Collections.Generic;
namespace DAL
{
    /// <summary>
    /// 数据访问类:D_Materials_Check1
    /// </summary>
    public partial class D_Materials_Check1
    {
        public D_Materials_Check1()
        { }
        #region  BasicMethod

        public string MaxCode(string date)
        {
            string max = date + (string)(HisVar.HisVar.Sqldal.F_MaxCode("select right(max(M_Check_Code),5) from Materials_Check1 where left(M_Check_Code,6)='" + date + "'", 5));
            return max;
        }

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        /// 
        public bool Exists(string M_Check_Code)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from Materials_Check1");
            strSql.Append(" where M_Check_Code=@M_Check_Code ");
            SqlParameter[] parameters = {
                    new SqlParameter("@M_Check_Code", SqlDbType.Char,11)            };
            parameters[0].Value = M_Check_Code;

            return HisVar.HisVar.Sqldal.Exists(strSql.ToString(), parameters);
        }
        public DataSet GetHzList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(" SELECT * FROM Materials_Check1,Materials_Check2,dbo.Materials_Warehouse_Dict ,dbo.Zd_YyJsr,dbo.Materials_Dict,dbo.Materials_Stock ");
            strSql.Append(" WHERE Materials_Check1.M_Check_Code=Materials_Check2.M_Check_Code AND Materials_Check1.MaterialsWh_Code=Materials_Warehouse_Dict.MaterialsWh_Code ");
            strSql.Append(" AND Materials_Check1.Jsr_Code=Zd_YyJsr.Jsr_Code AND Materials_Check2.Materials_Code=Materials_Dict.Materials_Code AND Materials_Check2.MaterialsStock_Code=Materials_Stock.MaterialsStock_Code");
            if (strWhere.Trim() != "")
            {
                strSql.Append(strWhere);
            }
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ModelOld.M_Materials_Check1 model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into Materials_Check1(");
            strSql.Append("M_Check_Code,MaterialsWh_Code,Check_Date,Input_Date,Finish_Date,Jsr_Code,M_Check_Memo,OrdersStatus,TotalMoney)");
            strSql.Append(" values (");
            strSql.Append("@M_Check_Code,@MaterialsWh_Code,@Check_Date,@Input_Date,@Finish_Date,@Jsr_Code,@M_Check_Memo,@OrdersStatus,@TotalMoney)");
            SqlParameter[] parameters = {
                    new SqlParameter("@M_Check_Code", SqlDbType.Char,11),
                    new SqlParameter("@MaterialsWh_Code", SqlDbType.Char,2),
                                        new SqlParameter("@Check_Date", SqlDbType.SmallDateTime),
                    new SqlParameter("@Input_Date", SqlDbType.SmallDateTime),
                    new SqlParameter("@Finish_Date", SqlDbType.SmallDateTime),
                    new SqlParameter("@Jsr_Code", SqlDbType.Char,8),
                    new SqlParameter("@M_Check_Memo", SqlDbType.VarChar,50),
                    new SqlParameter("@OrdersStatus", SqlDbType.VarChar,10),
                                                    new SqlParameter("@TotalMoney", SqlDbType.Decimal, 5)};
            parameters[0].Value = model.M_Check_Code;
            parameters[1].Value = model.MaterialsWh_Code;
            parameters[2].Value = model.Check_Date;
            parameters[3].Value = model.Input_Date;
            parameters[4].Value = Common.Tools.IsValueNull(model.Finish_Date);
            parameters[5].Value = model.Jsr_Code;
            parameters[6].Value = model.M_Check_Memo;
            parameters[7].Value = model.OrdersStatus;
            parameters[8].Value = model.TotalMoney;
            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ModelOld.M_Materials_Check1 model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update Materials_Check1 set ");
            strSql.Append("MaterialsWh_Code=@MaterialsWh_Code,");
            strSql.Append("Check_Date=@Check_Date,");
            strSql.Append("Input_Date=@Input_Date,");
            strSql.Append("Finish_Date=@Finish_Date,");
            strSql.Append("Jsr_Code=@Jsr_Code,");
            strSql.Append("M_Check_Memo=@M_Check_Memo,");
            strSql.Append("OrdersStatus=@OrdersStatus,");
            strSql.Append("TotalMoney=@TotalMoney");
            strSql.Append(" where M_Check_Code=@M_Check_Code ");
            SqlParameter[] parameters = {
                    new SqlParameter("@MaterialsWh_Code", SqlDbType.Char,2),
                                                            new SqlParameter("@Check_Date", SqlDbType.SmallDateTime),
                    new SqlParameter("@Input_Date", SqlDbType.SmallDateTime),
                    new SqlParameter("@Finish_Date", SqlDbType.SmallDateTime),
                    new SqlParameter("@Jsr_Code", SqlDbType.Char,8),
                    new SqlParameter("@M_Check_Memo", SqlDbType.VarChar,50),
                    new SqlParameter("@OrdersStatus", SqlDbType.VarChar,10),
                            new SqlParameter("@TotalMoney", SqlDbType.Decimal, 5),
                    new SqlParameter("@M_Check_Code", SqlDbType.Char,11)};
            parameters[0].Value = model.MaterialsWh_Code;
            parameters[1].Value = model.Check_Date;
            parameters[2].Value = model.Input_Date;
            parameters[3].Value = Common.Tools.IsValueNull(model.Finish_Date);
            parameters[4].Value = model.Jsr_Code;
            parameters[5].Value = model.M_Check_Memo;
            parameters[6].Value = model.OrdersStatus;
            parameters[7].Value = model.TotalMoney;
            parameters[8].Value = model.M_Check_Code;

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 单据完成,库存表插入新的记录
        /// </summary>
        public bool Complete(ModelOld.M_Materials_Check1 model)
        {
            List<string> sqlList = new List<string>();
            List<SqlParameter[]> parametersList = new List<SqlParameter[]>();

            //单据状态改成完成,填写完成时间
            StringBuilder strSql1 = new StringBuilder();
            strSql1.Append("update Materials_Check1 set ");
            strSql1.Append("OrdersStatus=@OrdersStatus,");
            strSql1.Append("Finish_Date=@Finish_Date");
            strSql1.Append(" where M_Check_Code=@M_Check_Code ");
            SqlParameter[] parameters1 =
            {
                new SqlParameter("@OrdersStatus", SqlDbType.VarChar, 10),
                new SqlParameter("@Finish_Date", SqlDbType.SmallDateTime),
                new SqlParameter("@M_Check_Code", SqlDbType.Char, 11)
            };
            parameters1[0].Value = model.OrdersStatus;
            parameters1[1].Value = model.Finish_Date;
            parameters1[2].Value = model.M_Check_Code;

            sqlList.Add(strSql1.ToString());
            parametersList.Add(parameters1);


            //库存加上盘点盈亏数据
            StringBuilder strSqlend = new StringBuilder();
            strSqlend.Append("UPDATE Materials_Stock SET MaterialsStore_Num=MaterialsStore_Num+M_Check_Num   ");
            strSqlend.Append("FROM Materials_Check2 ");
            strSqlend.Append("WHERE Materials_Stock.MaterialsStock_Code=Materials_Check2.MaterialsStock_Code ");
            strSqlend.Append("AND M_Check_Code=@M_Check_Code ");
            SqlParameter[] parametersend = {
                    new SqlParameter("@M_Check_Code", SqlDbType.Char,11)};
            parametersend[0].Value = model.M_Check_Code;

            sqlList.Add(strSqlend.ToString());
            parametersList.Add(parametersend);

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(sqlList, parametersList);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(string M_Check_Code)
        {
            List<string> sqlList = new List<string>();
            List<SqlParameter[]> parametersList = new List<SqlParameter[]>();

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from Materials_Check2 ");
            strSql.Append(" where M_Check_Code=@M_Check_Code ");
            SqlParameter[] parameters = {
                    new SqlParameter("@M_Check_Code", SqlDbType.Char,15)            };
            parameters[0].Value = M_Check_Code;

            sqlList.Add(strSql.ToString());
            parametersList.Add(parameters);

            strSql = new StringBuilder();
            strSql.Append("delete from Materials_Check1 ");
            strSql.Append(" where M_Check_Code=@M_Check_Code ");
            parameters = new SqlParameter[] {
                    new SqlParameter("@M_Check_Code", SqlDbType.Char,11)            };
            parameters[0].Value = M_Check_Code;


            sqlList.Add(strSql.ToString());
            parametersList.Add(parameters);

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(sqlList, parametersList);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string M_Check_Codelist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from Materials_Check1 ");
            strSql.Append(" where M_Check_Code in (" + M_Check_Codelist + ")  ");
            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ModelOld.M_Materials_Check1 GetModel(string M_Check_Code)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 M_Check_Code,MaterialsWh_Code,Check_Date,Input_Date,Finish_Date,Materials_Check1.Jsr_Code,Jsr_Name,M_Check_Memo,OrdersStatus,TotalMoney from Materials_Check1  JOIN dbo.Zd_YyJsr ON dbo.Materials_Check1.Jsr_Code = dbo.Zd_YyJsr.Jsr_Code");
            strSql.Append(" where M_Check_Code=@M_Check_Code ");
            SqlParameter[] parameters = {
                    new SqlParameter("@M_Check_Code", SqlDbType.Char,11)            };
            parameters[0].Value = M_Check_Code;

            ModelOld.M_Materials_Check1 model = new ModelOld.M_Materials_Check1();
            DataSet ds = HisVar.HisVar.Sqldal.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ModelOld.M_Materials_Check1 DataRowToModel(DataRow row)
        {
            ModelOld.M_Materials_Check1 model = new ModelOld.M_Materials_Check1();
            if (row != null)
            {
                if (row["M_Check_Code"] != null)
                {
                    model.M_Check_Code = row["M_Check_Code"].ToString();
                }
                if (row["MaterialsWh_Code"] != null)
                {
                    model.MaterialsWh_Code = row["MaterialsWh_Code"].ToString();
                }
                if (row["Check_Date"] != null && row["Check_Date"].ToString() != "")
                {
                    model.Check_Date = DateTime.Parse(row["Check_Date"].ToString());
                }
                if (row["Input_Date"] != null && row["Input_Date"].ToString() != "")
                {
                    model.Input_Date = DateTime.Parse(row["Input_Date"].ToString());
                }
                if (row["Finish_Date"] != null && row["Finish_Date"].ToString() != "")
                {
                    model.Finish_Date = DateTime.Parse(row["Finish_Date"].ToString());
                }
                if (row["Jsr_Code"] != null)
                {
                    model.Jsr_Code = row["Jsr_Code"].ToString();
                }
                if (row["Jsr_Name"] != null)
                {
                    model.Jsr_Name = row["Jsr_Name"].ToString();
                }
                if (row["M_Check_Memo"] != null)
                {
                    model.M_Check_Memo = row["M_Check_Memo"].ToString();
                }
                if (row["OrdersStatus"] != null)
                {
                    model.OrdersStatus = row["OrdersStatus"].ToString();
                }

                if (row["TotalMoney"] != null && row["TotalMoney"].ToString() != "")
                {
                    model.TotalMoney = decimal.Parse(row["TotalMoney"].ToString());
                }
            }
            return model;
        }
        /// <summary>
        ///总金额
        /// </summary>
        public double GetSumMoney(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT SUM(TotalMoney) FROM dbo.Materials_Check1   where OrdersStatus='完成'");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" and " + strWhere);
            }
            try
            {
                double douSum = Double.Parse(HisVar.HisVar.Sqldal.GetSingle(strSql.ToString()).ToString());
                return douSum;
            }
            catch (Exception)
            {
                return 0;
            }

        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT M_Check_Code,Materials_Check1. MaterialsWh_Code,MaterialsWh_Name, Check_Date, Input_Date, Finish_Date,Materials_Check1. Jsr_Code,Jsr_Name, M_Check_Memo, OrdersStatus,TotalMoney ");
            strSql.Append(" FROM dbo.Zd_YyJsr JOIN dbo.Materials_Check1 ON Zd_YyJsr.Jsr_Code=dbo.Materials_Check1.Jsr_Code JOIN dbo.Materials_Warehouse_Dict ON dbo.Materials_Check1.MaterialsWh_Code = dbo.Materials_Warehouse_Dict.MaterialsWh_Code");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" M_Check_Code,MaterialsWh_Code,Check_Date,Input_Date,Finish_Date,Jsr_Code,M_Check_Memo,OrdersStatus,TotalMoney ");
            strSql.Append(" FROM Materials_Check1 ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM Materials_Check1 ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.M_Check_Code desc");
            }
            strSql.Append(")AS Row, T.*  from Materials_Check1 T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Materials_Check1";
			parameters[1].Value = "M_Check_Code";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return HisVar.HisVar.Sqldal.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod
        #region  ExtensionMethod

        #endregion  ExtensionMethod
    }
}

