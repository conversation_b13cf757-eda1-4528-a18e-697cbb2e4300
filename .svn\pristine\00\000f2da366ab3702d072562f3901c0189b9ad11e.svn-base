﻿/**  版本信息模板在安装目录下，可自行修改。
* D_Emr_BlZk2.cs
*
* 功 能： N/A
* 类 名： D_Emr_BlZk2
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/9/19 17:08:27   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;

namespace DAL
{
    /// <summary>
    /// 数据访问类:D_Emr_BlZk2
    /// </summary>
    public partial class D_Emr_BlZk2
    {
        public D_Emr_BlZk2()
        { }
        #region  BasicMethod

        /// <summary>
        /// 得到最大ID
        /// </summary>
        public int GetMaxId()
        {
            return HisVar.HisVar.Sqldal.GetMaxID("id", "Emr_BlZk2");
        }

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(string Bl_Code, int id, string Mb_Code)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from Emr_BlZk2");
            strSql.Append(" where Bl_Code=@Bl_Code and id=@id and Mb_Code=@Mb_Code ");
            SqlParameter[] parameters = {
					new SqlParameter("@Bl_Code", SqlDbType.Char,14),
					new SqlParameter("@id", SqlDbType.Int,4),
                                        new SqlParameter("@Mb_Code", SqlDbType.Char,10)				};
            parameters[0].Value = Bl_Code;
            parameters[1].Value = id;
            parameters[2].Value = Mb_Code;

            return HisVar.HisVar.Sqldal.Exists(strSql.ToString(), parameters);
        }

       
        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ModelOld.M_Emr_BlZk2 model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into Emr_BlZk2(");
            strSql.Append("Bl_Code,id,Kf_Count,Kf,Mb_Code)");
            strSql.Append(" values (");
            strSql.Append("@Bl_Code,@id,@Kf_Count,@Kf,@Mb_Code)");
            SqlParameter[] parameters = {
					new SqlParameter("@Bl_Code", SqlDbType.Char,14),
					new SqlParameter("@id", SqlDbType.Int,4),
					new SqlParameter("@Kf_Count", SqlDbType.Int,4),
					new SqlParameter("@Kf", SqlDbType.Decimal,9),
                    new SqlParameter("@Mb_Code", SqlDbType.Char,10)};
            parameters[0].Value = model.Bl_Code;
            parameters[1].Value = model.id;
            parameters[2].Value = model.Kf_Count;
            parameters[3].Value = model.Kf;
            parameters[4].Value = model.Mb_Code;

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ModelOld.M_Emr_BlZk2 model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update Emr_BlZk2 set ");
            strSql.Append("Kf_Count=@Kf_Count,");
            strSql.Append("Kf=@Kf");
            strSql.Append(" where Bl_Code=@Bl_Code and id=@id and Mb_Code=@Mb_Code");
            SqlParameter[] parameters = {
					new SqlParameter("@Kf_Count", SqlDbType.Int,4),
					new SqlParameter("@Kf", SqlDbType.Decimal,9),
					new SqlParameter("@Bl_Code", SqlDbType.Char,14),
					new SqlParameter("@id", SqlDbType.Int,4),
                                        new SqlParameter("@Mb_Code", SqlDbType.Char,10)};
            parameters[0].Value = model.Kf_Count;
            parameters[1].Value = model.Kf;
            parameters[2].Value = model.Bl_Code;
            parameters[3].Value = model.id;
            parameters[4].Value = model.Mb_Code;

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(string Bl_Code, int id, string Mb_Code)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from Emr_BlZk2 ");
            strSql.Append(" where Bl_Code=@Bl_Code and id=@id and Mb_Code=@Mb_Code ");
            SqlParameter[] parameters = {
					new SqlParameter("@Bl_Code", SqlDbType.Char,14),
					new SqlParameter("@id", SqlDbType.Int,4),
                                        new SqlParameter("@Mb_Code", SqlDbType.Char,10)			};
            parameters[0].Value = Bl_Code;
            parameters[1].Value = id;
            parameters[2].Value = Mb_Code;

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除个人全部数据
        /// </summary>
        public bool DeleteAllbybl(string Bl_Code)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from Emr_BlZk2 ");
            strSql.Append(" where Bl_Code=@Bl_Code");
            SqlParameter[] parameters = {
					new SqlParameter("@Bl_Code", SqlDbType.Char,14)
						};
            parameters[0].Value = Bl_Code;
            
            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ModelOld.M_Emr_BlZk2 GetModel(string Bl_Code, int id, string Mb_Code)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Bl_Code,id,Kf_Count,Kf from Emr_BlZk2 ");
            strSql.Append(" where Bl_Code=@Bl_Code and id=@id and Mb_Code=@Mb_Code  ");
            SqlParameter[] parameters = {
					new SqlParameter("@Bl_Code", SqlDbType.Char,14),
					new SqlParameter("@id", SqlDbType.Int,4),
                                        new SqlParameter("@Mb_Code", SqlDbType.Char,10)				};
            parameters[0].Value = Bl_Code;
            parameters[1].Value = id;
            parameters[2].Value = Mb_Code;

            ModelOld.M_Emr_BlZk2 model = new ModelOld.M_Emr_BlZk2();
            DataSet ds = HisVar.HisVar.Sqldal.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ModelOld.M_Emr_BlZk2 DataRowToModel(DataRow row)
        {
            ModelOld.M_Emr_BlZk2 model = new ModelOld.M_Emr_BlZk2();
            if (row != null)
            {
                if (row["Bl_Code"] != null)
                {
                    model.Bl_Code = row["Bl_Code"].ToString();
                }
                if (row["id"] != null && row["id"].ToString() != "")
                {
                    model.id = int.Parse(row["id"].ToString());
                }
                if (row["Kf_Count"] != null && row["Kf_Count"].ToString() != "")
                {
                    model.Kf_Count = int.Parse(row["Kf_Count"].ToString());
                }
                if (row["Kf"] != null && row["Kf"].ToString() != "")
                {
                    model.Kf = decimal.Parse(row["Kf"].ToString());
                }
                if (row["Mb_Code"] != null)
                {
                    model.Mb_Code = row["Mb_Code"].ToString();
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Bl_Code,id,Kf_Count,Kf ");
            strSql.Append(" FROM Emr_BlZk2 ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

        public DataSet GetZkPfList(string Bl_Code)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(" SELECT distinct a.Bl_Code,a.id,a.Mb_Name,a.Mx_Name,a.Kf_Pz,a.Kf_PzNr,a.Zk_Kf,Kf_Count,Kf,a.Mb_Code");
            strSql.Append(" FROM( SELECT distinct Bl_Code ,Emr_ZhiKong2.id ,Emr_Bl.Mb_Name ,Mx_Name ,Kf_Pz ,Kf_PzNr ,Zk_Kf,Emr_Bl.Mb_Code");
            strSql.Append(" FROM  Emr_Mb,Emr_Mblb,Emr_Bl,Emr_ZhiKong1,Emr_ZhiKong2");
            strSql.Append(" WHERE Emr_Mb.Mblb_Code = Emr_Mblb.Mblb_Code");
            strSql.Append(" AND Emr_Bl.Mb_Code = Emr_Mb.Mb_Code");
            strSql.Append(" AND Emr_ZhiKong1.Zk_Code = Emr_ZhiKong2.Zk_Code");
            strSql.Append(" AND Emr_Mb.Mblb_Code = Emr_ZhiKong1.Mblb_Code");
            strSql.Append(" AND ISNULL(Emr_ZhiKong1.Mb_Code, Emr_Bl.Mb_Code) = Emr_Bl.Mb_Code");
            strSql.Append(" AND Emr_Bl.Bl_Code = '" + Bl_Code + "'");
            strSql.Append(" ) a LEFT JOIN dbo.Emr_BlZk2 ON a.id = Emr_BlZk2.id  AND a.Bl_Code = Emr_BlZk2.Bl_Code and a.Mb_code=Emr_BlZk2.Mb_code order by a.Mb_Code");

            return HisVar.HisVar.Sqldal.Query(strSql.ToString(),false);
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" Bl_Code,id,Kf_Count,Kf ");
            strSql.Append(" FROM Emr_BlZk2 ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM Emr_BlZk2 ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.id desc");
            }
            strSql.Append(")AS Row, T.*  from Emr_BlZk2 T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

        /*
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetList(int PageSize,int PageIndex,string strWhere)
        {
            SqlParameter[] parameters = {
                    new SqlParameter("@tblName", SqlDbType.VarChar, 255),
                    new SqlParameter("@fldName", SqlDbType.VarChar, 255),
                    new SqlParameter("@PageSize", SqlDbType.Int),
                    new SqlParameter("@PageIndex", SqlDbType.Int),
                    new SqlParameter("@IsReCount", SqlDbType.Bit),
                    new SqlParameter("@OrderType", SqlDbType.Bit),
                    new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
                    };
            parameters[0].Value = "Emr_BlZk2";
            parameters[1].Value = "id";
            parameters[2].Value = PageSize;
            parameters[3].Value = PageIndex;
            parameters[4].Value = 0;
            parameters[5].Value = 0;
            parameters[6].Value = strWhere;	
            return HisVar.HisVar.Sqldal.RunProcedure("UP_GetRecordByPage",parameters,"ds");
        }*/

        #endregion  BasicMethod
        #region  ExtensionMethod

        #endregion  ExtensionMethod
    }
}

