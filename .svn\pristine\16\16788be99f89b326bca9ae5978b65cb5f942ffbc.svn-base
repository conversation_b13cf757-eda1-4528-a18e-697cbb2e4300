﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class LISMetaDic12
    Inherits HisControl.BaseChild

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(LISMetaDic12))
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.Label3 = New C1.Win.C1Input.C1Label()
        Me.Button2 = New CustomControl.MyButton()
        Me.Button1 = New CustomControl.MyButton()
        Me.ToolBar1 = New C1.Win.C1Command.C1ToolBar()
        Me.Link6 = New C1.Win.C1Command.C1CommandLink()
        Me.Control1 = New C1.Win.C1Command.C1CommandControl()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.Link1 = New C1.Win.C1Command.C1CommandLink()
        Me.Move1 = New C1.Win.C1Command.C1Command()
        Me.Link2 = New C1.Win.C1Command.C1CommandLink()
        Me.Move2 = New C1.Win.C1Command.C1Command()
        Me.Link7 = New C1.Win.C1Command.C1CommandLink()
        Me.Control2 = New C1.Win.C1Command.C1CommandControl()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.Link3 = New C1.Win.C1Command.C1CommandLink()
        Me.Move3 = New C1.Win.C1Command.C1Command()
        Me.Link4 = New C1.Win.C1Command.C1CommandLink()
        Me.Move4 = New C1.Win.C1Command.C1Command()
        Me.Link5 = New C1.Win.C1Command.C1CommandLink()
        Me.Move5 = New C1.Win.C1Command.C1Command()
        Me.C1CommandHolder1 = New C1.Win.C1Command.C1CommandHolder()
        Me.Comm1 = New C1.Win.C1Command.C1Command()
        Me.Comm3 = New C1.Win.C1Command.C1Command()
        Me.Comm2 = New C1.Win.C1Command.C1Command()
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel()
        Me.T_Line1 = New System.Windows.Forms.Label()
        Me.EleCodeTb = New CustomControl.MyTextBox()
        Me.EleMemoTb = New CustomControl.MyTextBox()
        Me.EleJcTb = New CustomControl.MyTextBox()
        Me.EleNameTb = New CustomControl.MyTextBox()
        Me.T_Line2 = New System.Windows.Forms.Label()
        Me.Panel1.SuspendLayout()
        CType(Me.Label3, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.ToolBar1.SuspendLayout()
        CType(Me.C1CommandHolder1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.TableLayoutPanel1.SuspendLayout()
        Me.SuspendLayout()
        '
        'Panel1
        '
        Me.Panel1.Controls.Add(Me.Label3)
        Me.Panel1.Controls.Add(Me.Button2)
        Me.Panel1.Controls.Add(Me.Button1)
        Me.Panel1.Controls.Add(Me.ToolBar1)
        Me.Panel1.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.Panel1.Location = New System.Drawing.Point(0, 162)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(442, 29)
        Me.Panel1.TabIndex = 103
        '
        'Label3
        '
        Me.Label3.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.Label3.Location = New System.Drawing.Point(223, 5)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(35, 15)
        Me.Label3.TabIndex = 4
        Me.Label3.Tag = Nothing
        Me.Label3.Text = "Σ=1"
        Me.Label3.TextAlign = System.Drawing.ContentAlignment.BottomCenter
        Me.Label3.TextDetached = True
        '
        'Button2
        '
        Me.Button2.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.Button2.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.Button2.DialogResult = System.Windows.Forms.DialogResult.Cancel
        Me.Button2.Location = New System.Drawing.Point(347, 1)
        Me.Button2.Name = "Button2"
        Me.Button2.Size = New System.Drawing.Size(67, 27)
        Me.Button2.TabIndex = 1
        Me.Button2.Tag = "取消"
        Me.Button2.Text = "取消"
        '
        'Button1
        '
        Me.Button1.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.Button1.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.Button1.DialogResult = System.Windows.Forms.DialogResult.None
        Me.Button1.Location = New System.Drawing.Point(274, 1)
        Me.Button1.Name = "Button1"
        Me.Button1.Size = New System.Drawing.Size(67, 27)
        Me.Button1.TabIndex = 0
        Me.Button1.Tag = "保存"
        Me.Button1.Text = "保存"
        '
        'ToolBar1
        '
        Me.ToolBar1.AccessibleName = "Tool Bar"
        Me.ToolBar1.BackColor = System.Drawing.Color.Transparent
        Me.ToolBar1.CommandHolder = Nothing
        Me.ToolBar1.CommandLinks.AddRange(New C1.Win.C1Command.C1CommandLink() {Me.Link6, Me.Link1, Me.Link2, Me.Link7, Me.Link3, Me.Link4, Me.Link5})
        Me.ToolBar1.Controls.Add(Me.Label1)
        Me.ToolBar1.Controls.Add(Me.Label2)
        Me.ToolBar1.Location = New System.Drawing.Point(1, 3)
        Me.ToolBar1.MinButtonSize = 22
        Me.ToolBar1.Movable = False
        Me.ToolBar1.Name = "ToolBar1"
        Me.ToolBar1.Size = New System.Drawing.Size(207, 22)
        Me.ToolBar1.Text = "C1ToolBar2"
        Me.ToolBar1.VisualStyle = C1.Win.C1Command.VisualStyle.Custom
        Me.ToolBar1.VisualStyleBase = C1.Win.C1Command.VisualStyle.Classic
        '
        'Link6
        '
        Me.Link6.Command = Me.Control1
        '
        'Control1
        '
        Me.Control1.Control = Me.Label2
        Me.Control1.HotFrame = C1.Win.C1Command.HotFrameEnum.None
        Me.Control1.Name = "Control1"
        Me.Control1.ShortcutText = ""
        Me.Control1.ShowShortcut = False
        Me.Control1.ShowTextAsToolTip = False
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.BackColor = System.Drawing.Color.Transparent
        Me.Label2.Location = New System.Drawing.Point(1, 5)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(29, 12)
        Me.Label2.TabIndex = 159
        Me.Label2.Text = "记录"
        Me.Label2.TextAlign = System.Drawing.ContentAlignment.BottomCenter
        '
        'Link1
        '
        Me.Link1.Command = Me.Move1
        Me.Link1.Delimiter = True
        Me.Link1.SortOrder = 1
        '
        'Move1
        '
        Me.Move1.Image = CType(resources.GetObject("Move1.Image"), System.Drawing.Image)
        Me.Move1.Name = "Move1"
        Me.Move1.ShortcutText = ""
        Me.Move1.Text = "最前"
        Me.Move1.ToolTipText = "移至最前记录"
        '
        'Link2
        '
        Me.Link2.Command = Me.Move2
        Me.Link2.SortOrder = 2
        Me.Link2.Text = "上移"
        Me.Link2.ToolTipText = "上移记录"
        '
        'Move2
        '
        Me.Move2.Image = CType(resources.GetObject("Move2.Image"), System.Drawing.Image)
        Me.Move2.Name = "Move2"
        Me.Move2.ShortcutText = ""
        Me.Move2.Text = "上移"
        Me.Move2.ToolTipText = "上移记录"
        '
        'Link7
        '
        Me.Link7.Command = Me.Control2
        Me.Link7.SortOrder = 3
        '
        'Control2
        '
        Me.Control2.Control = Me.Label1
        Me.Control2.HotFrame = C1.Win.C1Command.HotFrameEnum.None
        Me.Control2.Name = "Control2"
        Me.Control2.ShortcutText = ""
        Me.Control2.ShowShortcut = False
        Me.Control2.ShowTextAsToolTip = False
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.BackColor = System.Drawing.Color.Transparent
        Me.Label1.Location = New System.Drawing.Point(80, 5)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(23, 12)
        Me.Label1.TabIndex = 159
        Me.Label1.Text = "(1)"
        Me.Label1.TextAlign = System.Drawing.ContentAlignment.BottomCenter
        '
        'Link3
        '
        Me.Link3.Command = Me.Move3
        Me.Link3.SortOrder = 4
        '
        'Move3
        '
        Me.Move3.Image = CType(resources.GetObject("Move3.Image"), System.Drawing.Image)
        Me.Move3.Name = "Move3"
        Me.Move3.ShortcutText = ""
        Me.Move3.Text = "下移"
        Me.Move3.ToolTipText = "下移记录"
        '
        'Link4
        '
        Me.Link4.Command = Me.Move4
        Me.Link4.SortOrder = 5
        '
        'Move4
        '
        Me.Move4.Image = CType(resources.GetObject("Move4.Image"), System.Drawing.Image)
        Me.Move4.Name = "Move4"
        Me.Move4.ShortcutText = ""
        Me.Move4.Text = "最后"
        Me.Move4.ToolTipText = "移至最后记录"
        '
        'Link5
        '
        Me.Link5.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image), C1.Win.C1Command.ButtonLookFlags)
        Me.Link5.Command = Me.Move5
        Me.Link5.Delimiter = True
        Me.Link5.SortOrder = 6
        Me.Link5.Text = "新增"
        Me.Link5.ToolTipText = "新增记录"
        '
        'Move5
        '
        Me.Move5.Image = CType(resources.GetObject("Move5.Image"), System.Drawing.Image)
        Me.Move5.Name = "Move5"
        Me.Move5.ShortcutText = ""
        Me.Move5.Text = "新增"
        '
        'C1CommandHolder1
        '
        Me.C1CommandHolder1.Commands.Add(Me.Control1)
        Me.C1CommandHolder1.Commands.Add(Me.Move1)
        Me.C1CommandHolder1.Commands.Add(Me.Move2)
        Me.C1CommandHolder1.Commands.Add(Me.Control2)
        Me.C1CommandHolder1.Commands.Add(Me.Move3)
        Me.C1CommandHolder1.Commands.Add(Me.Move4)
        Me.C1CommandHolder1.Commands.Add(Me.Move5)
        Me.C1CommandHolder1.Commands.Add(Me.Comm1)
        Me.C1CommandHolder1.Commands.Add(Me.Comm3)
        Me.C1CommandHolder1.Commands.Add(Me.Comm2)
        Me.C1CommandHolder1.Owner = Me
        '
        'Comm1
        '
        Me.Comm1.Name = "Comm1"
        Me.Comm1.ShortcutText = ""
        Me.Comm1.Text = "增加"
        Me.Comm1.ToolTipText = "增加记录"
        '
        'Comm3
        '
        Me.Comm3.Name = "Comm3"
        Me.Comm3.ShortcutText = ""
        Me.Comm3.Text = "刷新"
        '
        'Comm2
        '
        Me.Comm2.Name = "Comm2"
        Me.Comm2.ShortcutText = ""
        Me.Comm2.Text = "删除"
        Me.Comm2.ToolTipText = "删除记录"
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.ColumnCount = 5
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 20.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 200.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 200.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 20.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle())
        Me.TableLayoutPanel1.Controls.Add(Me.T_Line1, 0, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.EleCodeTb, 1, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.EleMemoTb, 1, 4)
        Me.TableLayoutPanel1.Controls.Add(Me.EleJcTb, 2, 3)
        Me.TableLayoutPanel1.Controls.Add(Me.EleNameTb, 1, 3)
        Me.TableLayoutPanel1.Controls.Add(Me.T_Line2, 0, 5)
        Me.TableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(0, 0)
        Me.TableLayoutPanel1.Margin = New System.Windows.Forms.Padding(0)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.RowCount = 6
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 10.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 2.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 2.0!))
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(442, 162)
        Me.TableLayoutPanel1.TabIndex = 104
        '
        'T_Line1
        '
        Me.T_Line1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.T_Line1.BackColor = System.Drawing.SystemColors.Control
        Me.T_Line1.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.TableLayoutPanel1.SetColumnSpan(Me.T_Line1, 4)
        Me.T_Line1.Location = New System.Drawing.Point(3, 38)
        Me.T_Line1.Name = "T_Line1"
        Me.T_Line1.Size = New System.Drawing.Size(434, 2)
        Me.T_Line1.TabIndex = 133
        Me.T_Line1.Text = "Label1"
        '
        'EleCodeTb
        '
        Me.EleCodeTb.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.EleCodeTb.Captain = "编    号"
        Me.EleCodeTb.CaptainBackColor = System.Drawing.Color.Transparent
        Me.EleCodeTb.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.EleCodeTb.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.EleCodeTb.CaptainWidth = 60.0!
        Me.EleCodeTb.ContentForeColor = System.Drawing.Color.Black
        Me.EleCodeTb.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.EleCodeTb.Enabled = False
        Me.EleCodeTb.Location = New System.Drawing.Point(23, 14)
        Me.EleCodeTb.Multiline = False
        Me.EleCodeTb.Name = "EleCodeTb"
        Me.EleCodeTb.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.EleCodeTb.ReadOnly = False
        Me.EleCodeTb.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.EleCodeTb.SelectionStart = 0
        Me.EleCodeTb.SelectStart = 0
        Me.EleCodeTb.Size = New System.Drawing.Size(194, 20)
        Me.EleCodeTb.TabIndex = 147
        Me.EleCodeTb.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.EleCodeTb.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        '
        'EleMemoTb
        '
        Me.EleMemoTb.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.EleMemoTb.Captain = "备    注"
        Me.EleMemoTb.CaptainBackColor = System.Drawing.Color.Transparent
        Me.EleMemoTb.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.EleMemoTb.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.EleMemoTb.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.EleMemoTb, 2)
        Me.EleMemoTb.ContentForeColor = System.Drawing.Color.Black
        Me.EleMemoTb.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.EleMemoTb.Location = New System.Drawing.Point(23, 71)
        Me.EleMemoTb.Multiline = True
        Me.EleMemoTb.Name = "EleMemoTb"
        Me.EleMemoTb.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.EleMemoTb.ReadOnly = False
        Me.EleMemoTb.ScrollBars = System.Windows.Forms.ScrollBars.Vertical
        Me.EleMemoTb.SelectionStart = 0
        Me.EleMemoTb.SelectStart = 0
        Me.EleMemoTb.Size = New System.Drawing.Size(394, 86)
        Me.EleMemoTb.TabIndex = 5
        Me.EleMemoTb.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.EleMemoTb.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Top
        '
        'EleJcTb
        '
        Me.EleJcTb.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.EleJcTb.Captain = "简    称"
        Me.EleJcTb.CaptainBackColor = System.Drawing.Color.Transparent
        Me.EleJcTb.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.EleJcTb.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.EleJcTb.CaptainWidth = 60.0!
        Me.EleJcTb.ContentForeColor = System.Drawing.Color.Black
        Me.EleJcTb.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.EleJcTb.Enabled = False
        Me.EleJcTb.Location = New System.Drawing.Point(223, 44)
        Me.EleJcTb.Multiline = False
        Me.EleJcTb.Name = "EleJcTb"
        Me.EleJcTb.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.EleJcTb.ReadOnly = False
        Me.EleJcTb.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.EleJcTb.SelectionStart = 0
        Me.EleJcTb.SelectStart = 0
        Me.EleJcTb.Size = New System.Drawing.Size(194, 20)
        Me.EleJcTb.TabIndex = 155
        Me.EleJcTb.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.EleJcTb.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        '
        'EleNameTb
        '
        Me.EleNameTb.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.EleNameTb.Captain = "名    称"
        Me.EleNameTb.CaptainBackColor = System.Drawing.Color.Transparent
        Me.EleNameTb.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.EleNameTb.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.EleNameTb.CaptainWidth = 60.0!
        Me.EleNameTb.ContentForeColor = System.Drawing.Color.Black
        Me.EleNameTb.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.EleNameTb.Location = New System.Drawing.Point(23, 44)
        Me.EleNameTb.Multiline = False
        Me.EleNameTb.Name = "EleNameTb"
        Me.EleNameTb.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.EleNameTb.ReadOnly = False
        Me.EleNameTb.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.EleNameTb.SelectionStart = 0
        Me.EleNameTb.SelectStart = 0
        Me.EleNameTb.Size = New System.Drawing.Size(194, 20)
        Me.EleNameTb.TabIndex = 0
        Me.EleNameTb.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.EleNameTb.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        '
        'T_Line2
        '
        Me.T_Line2.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.T_Line2.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.TableLayoutPanel1.SetColumnSpan(Me.T_Line2, 4)
        Me.T_Line2.Location = New System.Drawing.Point(3, 160)
        Me.T_Line2.Name = "T_Line2"
        Me.T_Line2.Size = New System.Drawing.Size(434, 2)
        Me.T_Line2.TabIndex = 158
        '
        'LISMetaDic12
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(442, 191)
        Me.Controls.Add(Me.TableLayoutPanel1)
        Me.Controls.Add(Me.Panel1)
        Me.Name = "LISMetaDic12"
        Me.Text = "LIS元字典明细"
        Me.Panel1.ResumeLayout(False)
        CType(Me.Label3, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ToolBar1.ResumeLayout(False)
        Me.ToolBar1.PerformLayout()
        CType(Me.C1CommandHolder1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.TableLayoutPanel1.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents Label3 As C1.Win.C1Input.C1Label
    Friend WithEvents Button2 As CustomControl.MyButton
    Friend WithEvents Button1 As CustomControl.MyButton
    Friend WithEvents ToolBar1 As C1.Win.C1Command.C1ToolBar
    Friend WithEvents Link6 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link1 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link2 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link7 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link3 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link4 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link5 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents C1CommandHolder1 As C1.Win.C1Command.C1CommandHolder
    Friend WithEvents Comm1 As C1.Win.C1Command.C1Command
    Friend WithEvents Comm3 As C1.Win.C1Command.C1Command
    Friend WithEvents Comm2 As C1.Win.C1Command.C1Command
    Friend WithEvents TableLayoutPanel1 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents T_Line1 As System.Windows.Forms.Label
    Friend WithEvents EleCodeTb As CustomControl.MyTextBox
    Friend WithEvents EleMemoTb As CustomControl.MyTextBox
    Friend WithEvents EleJcTb As CustomControl.MyTextBox
    Friend WithEvents EleNameTb As CustomControl.MyTextBox
    Friend WithEvents T_Line2 As System.Windows.Forms.Label
    Friend WithEvents Label2 As System.Windows.Forms.Label
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Private WithEvents Control1 As C1.Win.C1Command.C1CommandControl
    Private WithEvents Move1 As C1.Win.C1Command.C1Command
    Private WithEvents Move2 As C1.Win.C1Command.C1Command
    Private WithEvents Control2 As C1.Win.C1Command.C1CommandControl
    Private WithEvents Move3 As C1.Win.C1Command.C1Command
    Private WithEvents Move4 As C1.Win.C1Command.C1Command
    Private WithEvents Move5 As C1.Win.C1Command.C1Command
End Class
