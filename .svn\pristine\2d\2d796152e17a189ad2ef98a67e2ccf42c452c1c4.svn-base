﻿/**  版本信息模板在安装目录下，可自行修改。
* M_LIS_TestItem.cs
*
* 功 能： N/A
* 类 名： M_LIS_TestItem
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/10/17 星期一 下午 1:38:53   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace ModelOld
{
	/// <summary>
	/// M_LIS_TestItem:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class M_LIS_TestItem
	{
		public M_LIS_TestItem()
		{}
		#region Model
		private string _testxm_code;
		private string _testitem_code;
		private string _item_name;
		private string _item_jc;
		private string _item_dw;
		private decimal? _malemin;
		private decimal? _malemax;
		private decimal? _femalemin;
		private decimal? _femalemax;
		private bool _isdefault;
		private string _itemgroup;
		private int? _itemorder;
		private string _memo;
		/// <summary>
		/// 
		/// </summary>
		public string TestXm_Code
		{
			set{ _testxm_code=value;}
			get{return _testxm_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string TestItem_Code
		{
			set{ _testitem_code=value;}
			get{return _testitem_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Item_Name
		{
			set{ _item_name=value;}
			get{return _item_name;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Item_Jc
		{
			set{ _item_jc=value;}
			get{return _item_jc;}
		}
		/// <summary>
		/// 小项单位
		/// </summary>
		public string Item_Dw
		{
			set{ _item_dw=value;}
			get{return _item_dw;}
		}
		/// <summary>
		/// 男性下限
		/// </summary>
		public decimal? MaleMin
		{
			set{ _malemin=value;}
			get{return _malemin;}
		}
		/// <summary>
		/// 男性上限
		/// </summary>
		public decimal? MaleMax
		{
			set{ _malemax=value;}
			get{return _malemax;}
		}
		/// <summary>
		/// 女性下限
		/// </summary>
		public decimal? FemaleMin
		{
			set{ _femalemin=value;}
			get{return _femalemin;}
		}
		/// <summary>
		/// 女性上限
		/// </summary>
		public decimal? FemaleMax
		{
			set{ _femalemax=value;}
			get{return _femalemax;}
		}
		/// <summary>
		/// 是否系统默认
		/// </summary>
		public bool isDefault
		{
			set{ _isdefault=value;}
			get{return _isdefault;}
		}
		/// <summary>
		/// 项目组合名
		/// </summary>
		public string ItemGroup
		{
			set{ _itemgroup=value;}
			get{return _itemgroup;}
		}
		/// <summary>
		/// 
		/// </summary>
		public int? ItemOrder
		{
			set{ _itemorder=value;}
			get{return _itemorder;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Memo
		{
			set{ _memo=value;}
			get{return _memo;}
		}
		#endregion Model

	}
}

