﻿Imports System
Imports System.Collections.Generic
Imports System.ComponentModel
Imports System.Drawing
'Imports System.Data
Imports System.Text
Imports System.Windows.Forms
Public Class MsgNotify
    'Inherits HisControl.BaseForm
 
    Dim tpaly As Threading.Thread = Nothing

    Public Sub New()
        InitializeComponent()
        '用线程播放声音
        tpaly = New Threading.Thread(AddressOf ToPaly)
        tpaly.Start()
    End Sub

    Private Sub ToPaly()
        Dim Sound As String = Application.StartupPath + "\sound\msg.wav"
        clsPalyWave.Play(Sound)
        tpaly.Abort()
    End Sub
        
    ''' <summary>
    ''' 提示框高度
    ''' </summary>
    ''' <remarks></remarks>
    Private _HeightMax As Integer
    ' 提示框高度
    Public WriteOnly Property HeightMax() As Integer
        Set(ByVal Value As Integer)
            _HeightMax = Value
        End Set
    End Property

    ' 
    ''' <summary>
    ''' 提示框停留时间（单位：秒）
    ''' </summary>
    ''' <value></value>
    ''' <remarks></remarks>
    Public WriteOnly Property StayTime() As Integer
        Set(ByVal Value As Integer)
            Me.TimerStay.Interval = Value
        End Set
    End Property

    ''' <summary>
    ''' 显示窗体
    ''' </summary>
    ''' <param name="text"></param>
    ''' <remarks></remarks>
    Public Sub ShowMessage(ByVal text As String)
        Me.Content.Text = text
        Me.TimerUp.Enabled = True
        Me.Height = 0
        Me.Show()
    End Sub
    ' 
    ''' <summary>
    ''' 显示窗体
    ''' </summary>
    ''' <param name="text"></param>
    ''' <param name="caption"></param>
    ''' <remarks></remarks>
    Public Sub ShowMessage(ByVal text As String, ByVal caption As String)
        Me.Text = caption
        Me.Content.Text = text
        Me.TimerUp.Enabled = True
        Me.Height = 0
        Me.Show()
    End Sub

    ' 
    ''' <summary>
    ''' 向上移动窗体
    ''' </summary>
    ''' <remarks></remarks>
    Private Sub MoveUp()
        If Me.Height < _HeightMax Then
            Me.Height += 3
            Me.Location = New Point(Location.X, Location.Y - 3)
        Else
            Me.TimerUp.Enabled = False
            Me.TimerStay.Enabled = True
        End If
    End Sub

    ''' <summary>
    ''' 向下移动窗体
    ''' </summary>
    ''' <remarks></remarks>
    Private Sub MoveDown()
        'Me.Dispose()
        If Me.Height > 0 Then
            Me.Height -= 10
            Me.Location = New Point(Location.X, Location.Y + 10)
        Else
            Me.Dispose()
        End If
    End Sub

    Private Sub MessageNotify_Load(ByVal sender As Object, ByVal e As EventArgs) Handles Me.Load
        '根据不同的屏幕分辨率来确定窗体的初始位置
        Dim screens() As Screen = Screen.AllScreens
        Dim s As Screen = screens(0)
        Me.Location = New Point(s.WorkingArea.Width - Me.Width - 2, s.WorkingArea.Height - 30)
    End Sub

    Private Sub TimerUp_Tick(ByVal sender As Object, ByVal e As EventArgs) Handles TimerUp.Tick
        Me.MoveUp()
    End Sub

    Private Sub TimerStay_Tick(ByVal sender As Object, ByVal e As EventArgs) Handles TimerStay.Tick
        Me.TimerStay.Enabled = False
        Me.TimerDown.Enabled = True
    End Sub

    Private Sub TimerDown_Tick(ByVal sender As Object, ByVal e As EventArgs) Handles TimerDown.Tick
        Me.MoveDown()
    End Sub

End Class


Public Class clsPalyWave

    Private Shared player As Media.SoundPlayer = New Media.SoundPlayer()

    Public Shared Sub Play(ByVal wfname As String)
        player.SoundLocation = wfname
        player.Load()
        player.Play()
    End Sub

    Public Shared Sub RedoPlay(ByVal wfname As String)
        player.SoundLocation = wfname
        player.Load()
        player.PlayLooping()
    End Sub

    Public Sub StopPlay()
        player.Stop()
    End Sub

End Class