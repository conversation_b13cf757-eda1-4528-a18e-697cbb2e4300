﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class JYYQGLDic11
    Inherits HisControl.BaseForm

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.T_Line1 = New System.Windows.Forms.Label()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.YqSxCb = New CustomControl.MySingleComobo()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.T_Textbox = New C1.Win.C1Input.C1TextBox()
        Me.ToolBar1 = New C1.Win.C1Command.C1ToolBar()
        Me.C1Holder1 = New C1.Win.C1Command.C1CommandHolder()
        Me.Comm1 = New C1.Win.C1Command.C1Command()
        Me.Comm2 = New C1.Win.C1Command.C1Command()
        Me.Comm3 = New C1.Win.C1Command.C1Command()
        Me.Link1 = New C1.Win.C1Command.C1CommandLink()
        Me.Link2 = New C1.Win.C1Command.C1CommandLink()
        Me.Link3 = New C1.Win.C1Command.C1CommandLink()
        Me.T_Label = New C1.Win.C1Input.C1Label()
        Me.MyGrid1 = New CustomControl.MyGrid()
        Me.Panel1.SuspendLayout()
        CType(Me.T_Textbox, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1Holder1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T_Label, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'T_Line1
        '
        Me.T_Line1.BackColor = System.Drawing.SystemColors.Control
        Me.T_Line1.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.T_Line1.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.T_Line1.Location = New System.Drawing.Point(0, 420)
        Me.T_Line1.Name = "T_Line1"
        Me.T_Line1.Size = New System.Drawing.Size(752, 2)
        Me.T_Line1.TabIndex = 131
        Me.T_Line1.Text = "Label1"
        '
        'Panel1
        '
        Me.Panel1.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Panel1.Controls.Add(Me.YqSxCb)
        Me.Panel1.Controls.Add(Me.Label1)
        Me.Panel1.Controls.Add(Me.T_Textbox)
        Me.Panel1.Controls.Add(Me.ToolBar1)
        Me.Panel1.Controls.Add(Me.T_Label)
        Me.Panel1.Dock = System.Windows.Forms.DockStyle.Top
        Me.Panel1.Location = New System.Drawing.Point(0, 0)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(752, 28)
        Me.Panel1.TabIndex = 132
        '
        'YqSxCb
        '
        Me.YqSxCb.Captain = "筛 选"
        Me.YqSxCb.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.YqSxCb.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.YqSxCb.CaptainWidth = 60.0!
        Me.YqSxCb.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        Me.YqSxCb.ItemHeight = 16
        Me.YqSxCb.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.YqSxCb.Location = New System.Drawing.Point(252, 2)
        Me.YqSxCb.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.YqSxCb.MinimumSize = New System.Drawing.Size(0, 20)
        Me.YqSxCb.Name = "YqSxCb"
        Me.YqSxCb.ReadOnly = False
        Me.YqSxCb.Size = New System.Drawing.Size(150, 20)
        Me.YqSxCb.TabIndex = 126
        Me.YqSxCb.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'Label1
        '
        Me.Label1.BackColor = System.Drawing.SystemColors.Control
        Me.Label1.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.Label1.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.Label1.Location = New System.Drawing.Point(0, 24)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(750, 2)
        Me.Label1.TabIndex = 120
        Me.Label1.Text = "Label1"
        '
        'T_Textbox
        '
        Me.T_Textbox.AutoSize = False
        Me.T_Textbox.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.T_Textbox.Location = New System.Drawing.Point(419, 2)
        Me.T_Textbox.Name = "T_Textbox"
        Me.T_Textbox.Size = New System.Drawing.Size(70, 20)
        Me.T_Textbox.TabIndex = 3
        Me.T_Textbox.TabStop = False
        Me.T_Textbox.Tag = Nothing
        Me.T_Textbox.TextDetached = True
        Me.T_Textbox.TrimStart = True
        Me.T_Textbox.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.T_Textbox.VisualStyle = C1.Win.C1Input.VisualStyle.Office2010Blue
        Me.T_Textbox.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2010Blue
        '
        'ToolBar1
        '
        Me.ToolBar1.AccessibleName = "Tool Bar"
        Me.ToolBar1.BackColor = System.Drawing.Color.Transparent
        Me.ToolBar1.CommandHolder = Me.C1Holder1
        Me.ToolBar1.CommandLinks.AddRange(New C1.Win.C1Command.C1CommandLink() {Me.Link1, Me.Link2, Me.Link3})
        Me.ToolBar1.Location = New System.Drawing.Point(3, 3)
        Me.ToolBar1.MinButtonSize = 22
        Me.ToolBar1.Movable = False
        Me.ToolBar1.Name = "ToolBar1"
        Me.ToolBar1.Size = New System.Drawing.Size(165, 22)
        Me.ToolBar1.Text = "C1ToolBar1"
        Me.ToolBar1.VisualStyle = C1.Win.C1Command.VisualStyle.Custom
        Me.ToolBar1.VisualStyleBase = C1.Win.C1Command.VisualStyle.Classic
        '
        'C1Holder1
        '
        Me.C1Holder1.Commands.Add(Me.Comm1)
        Me.C1Holder1.Commands.Add(Me.Comm2)
        Me.C1Holder1.Commands.Add(Me.Comm3)
        Me.C1Holder1.Owner = Me
        '
        'Comm1
        '
        Me.Comm1.Image = Global.ZtHis.Lis.My.Resources.Resources.增加
        Me.Comm1.Name = "Comm1"
        Me.Comm1.Shortcut = System.Windows.Forms.Shortcut.F2
        Me.Comm1.ShortcutText = ""
        Me.Comm1.Text = "增加"
        Me.Comm1.ToolTipText = "增加记录"
        '
        'Comm2
        '
        Me.Comm2.Image = Global.ZtHis.Lis.My.Resources.Resources.删除
        Me.Comm2.Name = "Comm2"
        Me.Comm2.Shortcut = System.Windows.Forms.Shortcut.F3
        Me.Comm2.ShortcutText = ""
        Me.Comm2.Text = "删除"
        Me.Comm2.ToolTipText = "删除记录"
        '
        'Comm3
        '
        Me.Comm3.Image = Global.ZtHis.Lis.My.Resources.Resources.刷新
        Me.Comm3.Name = "Comm3"
        Me.Comm3.Shortcut = System.Windows.Forms.Shortcut.F4
        Me.Comm3.ShortcutText = ""
        Me.Comm3.Text = "更新"
        Me.Comm3.ToolTipText = "更新记录"
        '
        'Link1
        '
        Me.Link1.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image), C1.Win.C1Command.ButtonLookFlags)
        Me.Link1.Command = Me.Comm1
        '
        'Link2
        '
        Me.Link2.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image), C1.Win.C1Command.ButtonLookFlags)
        Me.Link2.Command = Me.Comm2
        Me.Link2.SortOrder = 1
        Me.Link2.Text = "删除"
        '
        'Link3
        '
        Me.Link3.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image), C1.Win.C1Command.ButtonLookFlags)
        Me.Link3.Command = Me.Comm3
        Me.Link3.SortOrder = 2
        '
        'T_Label
        '
        Me.T_Label.BackColor = System.Drawing.Color.Transparent
        Me.T_Label.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.T_Label.ForeColor = System.Drawing.Color.Black
        Me.T_Label.Location = New System.Drawing.Point(519, 3)
        Me.T_Label.Name = "T_Label"
        Me.T_Label.Size = New System.Drawing.Size(237, 16)
        Me.T_Label.TabIndex = 2
        Me.T_Label.Tag = Nothing
        Me.T_Label.Text = "T_Label"
        Me.T_Label.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.T_Label.TextDetached = True
        Me.T_Label.TrimStart = True
        Me.T_Label.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'MyGrid1
        '
        Me.MyGrid1.CanCustomCol = False
        Me.MyGrid1.Col = 0
        Me.MyGrid1.ColumnFooters = False
        Me.MyGrid1.DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveRight
        Me.MyGrid1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.MyGrid1.FetchRowStyles = False
        Me.MyGrid1.Location = New System.Drawing.Point(0, 28)
        Me.MyGrid1.Margin = New System.Windows.Forms.Padding(0)
        Me.MyGrid1.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.MyGrid1.Name = "MyGrid1"
        Me.MyGrid1.Size = New System.Drawing.Size(752, 392)
        Me.MyGrid1.TabAction = C1.Win.C1TrueDBGrid.TabActionEnum.ControlNavigation
        Me.MyGrid1.TabIndex = 133
        Me.MyGrid1.Xmlpath = Nothing
        '
        'JYYQGLDic11
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(752, 422)
        Me.Controls.Add(Me.MyGrid1)
        Me.Controls.Add(Me.Panel1)
        Me.Controls.Add(Me.T_Line1)
        Me.Name = "JYYQGLDic11"
        Me.Text = "检验仪器管理"
        Me.Panel1.ResumeLayout(False)
        CType(Me.T_Textbox, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1Holder1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T_Label, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents T_Line1 As System.Windows.Forms.Label
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents YqSxCb As CustomControl.MySingleComobo
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents T_Textbox As C1.Win.C1Input.C1TextBox
    Friend WithEvents ToolBar1 As C1.Win.C1Command.C1ToolBar
    Friend WithEvents C1Holder1 As C1.Win.C1Command.C1CommandHolder
    Friend WithEvents Comm1 As C1.Win.C1Command.C1Command
    Friend WithEvents Comm2 As C1.Win.C1Command.C1Command
    Friend WithEvents Comm3 As C1.Win.C1Command.C1Command
    Friend WithEvents Link1 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link2 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link3 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents T_Label As C1.Win.C1Input.C1Label
    Friend WithEvents MyGrid1 As CustomControl.MyGrid
End Class
