﻿Imports System.Data.SqlClient
Public Class YkYf_Pd1
    Public Adapter As New SqlDataAdapter
    Public Ds As New DataSet
    Public My_Table As DataTable
    Dim My_Date As String
    Public Cmd As New SqlCommand

    Dim YfCode As String
    Dim YfName As String
    Dim FormLb As String

    Public Sub New(ByVal m_YfCode As String, ByVal m_YfName As String, ByVal m_formlb As String)

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        YfCode = m_YfCode
        YfName = m_YfName
        FormLb = m_formlb
        ' 在 InitializeComponent() 调用之后添加任何初始化。

    End Sub

    Private Sub YkYf_Pd1_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Init_Data()
        C1TextBox2.Select()
    End Sub
    Private Sub Init_Data()
        With C1DateEdit1
            .Value = Date.Today
            .DateTimeInput = True
            .AutoChangePosition = False
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .InitialSelection = C1.Win.C1Input.InitialSelectionEnum.CaretAtEnd
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .EditFormat.CustomFormat = "yyyy-MM"
            .DisplayFormat.CustomFormat = "yyyy-MM"
            .MaskInfo.AutoTabWhenFilled = True
            .AcceptsTab = True
            .EmptyAsNull = True
        End With
    End Sub

#Region "向数据盘点表中插入或更新数据"

    Private Sub Insert_Yk_Pd()
        If Ds.Tables("库存盘点") IsNot Nothing Then Ds.Tables("库存盘点").Clear()
        If Ds.Tables("盘点完成") IsNot Nothing Then Ds.Tables("盘点完成").Clear()
        Adapter = New SqlDataAdapter("Select Top 1 * From Zd_YkPd Where Yy_code='" & HisVar.HisVar.WsyCode & "' and Pd_Month='" & Me.C1DateEdit1.Text & "'", My_Cn)
        Adapter.Fill(Ds, "库存盘点")

        Adapter = New SqlDataAdapter("Select Top 1 * From Zd_YkPd Where Yy_code='" & HisVar.HisVar.WsyCode & "' and Isnull(Pd_Wc,'')='否'", My_Cn)
        Adapter.Fill(Ds, "盘点完成")

        If Ds.Tables("盘点完成").Rows.Count > 0 Then
            If Me.C1DateEdit1.Text <> Ds.Tables("盘点完成").Rows(0).Item("Pd_Month") Then
                MsgBox("上次未执行盘点完成，数据作废，本月将进行新一次盘点！", vbExclamation + vbOKOnly + vbDefaultButton1, "提示:")
                HisVar.HisVar.Sqldal.ExecuteSql("Delete from Zd_YKPd where Pd_Month='" & Ds.Tables("盘点完成").Rows(0).Item("Pd_Month") & "' and Yy_Code='" & HisVar.HisVar.WsyCode & "' ")

                If MsgBox("是否确认在" + Format(Now, "yyyy年MM月dd日") + " " + Format(Now, "HH:mm:ss") + "做库存盘点操作？" + Chr(10) + Chr(13) + Chr(10) + Chr(13) + "(系统将此时刻的库存记录在案，在此基础上进行数据盘点工作。)", vbQuestion + vbYesNo + vbDefaultButton1, "提示:") = vbYes Then
                    HisVar.HisVar.Sqldal.ExecuteSql("Insert Into Zd_YkPd(Yy_Code,Jsr_Code,Xx_Code,Pd_Month,Pd_Time,Pd_Date,Mx_Sl,Pd_Sl,Yk_CgDj,Yk_XsDj) Select '" & HisVar.HisVar.WsyCode & "','" & HisVar.HisVar.JsrCode & "',Xx_Code,'" & Me.C1DateEdit1.Text & "','" & Format(Now, "HH:mm:ss") & "','" & Now & "',Yk_Sl,Yk_Sl,Yk_Cgj,Yk_Xsj From Zd_Ml_Yp4 where Zd_Ml_Yp4.Yy_code='" & HisVar.HisVar.WsyCode & "' ")
                    '本月入库
                    HisVar.HisVar.Sqldal.ExecuteSql("Update Zd_YkPd Set Mx_RkSl=Isnull(SySl,0) From (select Sum(Rk_Sl) AS SySl,Yk_Rk2.Yy_Code,Xx_Code From Yk_Rk2,Yk_Rk1 Where Yk_Rk1.Rk_Code=Yk_Rk2.Rk_Code And Convert(varchar(10),Rk_Date,126)>Convert(varchar(7),'" & Me.C1DateEdit1.Text & "',126) Group By Yk_Rk2.Yy_Code,Xx_Code) a where  Zd_YkPd.Yy_code=a.Yy_code and Zd_YkPd.Yy_code='" & HisVar.HisVar.WsyCode & "' and Zd_YkPd.Xx_Code=a.Xx_Code And Jsr_Code='" & HisVar.HisVar.JsrCode & "' And Pd_Month='" & Me.C1DateEdit1.Text & "'")
                    '药房退库
                    HisVar.HisVar.Sqldal.ExecuteSql("Update Zd_YkPd Set Mx_YfThSl=Isnull(SySl,0) From (select Sum(Tk_Sl) AS SySl,Yf_Tk2.Yy_Code,Xx_Code From Yf_Tk2,Yf_Tk1 Where Yf_Tk1.Tk_Code=Yf_Tk2.Tk_Code And Convert(varchar(10),Tk_Date,126)>Convert(varchar(7),'" & Me.C1DateEdit1.Text & "',126) Group By Yf_Tk2.Yy_Code,Xx_Code) a where  Zd_YkPd.Yy_code=a.Yy_code and Zd_YkPd.Yy_code='" & HisVar.HisVar.WsyCode & "' and Zd_YkPd.Xx_Code=a.Xx_Code And Jsr_Code='" & HisVar.HisVar.JsrCode & "'And Pd_Month='" & Me.C1DateEdit1.Text & "'")
                    '本月调拨
                    HisVar.HisVar.Sqldal.ExecuteSql("Update Zd_YkPd Set Mx_DbSl=-Isnull(SySl,0) From (select Sum(Ck_Sl) AS SySl,Yk_Yf2.Yy_Code,Xx_Code From Yk_Yf2,Yk_Yf1 Where Yk_Yf1.Ck_Code=Yk_Yf2.Ck_Code And Convert(varchar(10),Ck_Date,126)>Convert(varchar(7),'" & Me.C1DateEdit1.Text & "',126) Group By Yk_Yf2.Yy_Code,Xx_Code) a where Zd_YkPd.Yy_code=a.Yy_code and Zd_YkPd.Yy_code='" & HisVar.HisVar.WsyCode & "' and Zd_YkPd.Xx_Code=a.Xx_Code And Jsr_Code='" & HisVar.HisVar.JsrCode & "'And Pd_Month='" & Me.C1DateEdit1.Text & "'")
                    '普通销售
                    HisVar.HisVar.Sqldal.ExecuteSql("Update Zd_YkPd Set Mx_XsSl=-Isnull(SySl,0) From (select Sum(Ck_Sl) AS SySl,Yk_Pf2.Yy_Code,Xx_Code From Yk_Pf2,Yk_Pf1 Where Yk_Pf1.Ck_Code=Yk_Pf2.Ck_Code And Convert(varchar(10),Ck_Date,126)>Convert(varchar(7),'" & Me.C1DateEdit1.Text & "',126) Group By Yk_Pf2.Yy_Code,Xx_Code) a where  Zd_YkPd.Yy_code=a.Yy_code and Zd_YkPd.Yy_code='" & HisVar.HisVar.WsyCode & "' and Zd_YkPd.Xx_Code=a.Xx_Code And Jsr_Code='" & HisVar.HisVar.JsrCode & "'And Pd_Month='" & Me.C1DateEdit1.Text & "'")
                    '科室支领
                    HisVar.HisVar.Sqldal.ExecuteSql("Update Zd_YkPd Set Mx_KsSl=-Isnull(SySl,0) From (select Sum(Ck_Sl) AS SySl,Yk_Ks2.Yy_Code,Xx_Code From Yk_Ks2,Yk_Ks1 Where Yk_Ks1.Ck_Code=Yk_Ks2.Ck_Code And Convert(varchar(10),Ck_Date,126)>Convert(varchar(7),'" & Me.C1DateEdit1.Text & "',126) Group By Yk_Ks2.Yy_Code,Xx_Code) a where Zd_YkPd.Yy_code=a.Yy_code and Zd_YkPd.Yy_code='" & HisVar.HisVar.WsyCode & "' and Zd_YkPd.Xx_Code=a.Xx_Code And Jsr_Code='" & HisVar.HisVar.JsrCode & "'And Pd_Month='" & Me.C1DateEdit1.Text & "'")
                    '退供应商
                    HisVar.HisVar.Sqldal.ExecuteSql("Update Zd_YkPd Set Mx_ThSl=-Isnull(SySl,0) From (select Sum(Tk_Sl) AS SySl,Yk_TkPf2.Yy_Code,Xx_Code From Yk_TkPf2,Yk_TkPf1 Where Yk_TkPf1.Tk_Code=Yk_TkPf2.Tk_Code And Convert(varchar(10),Tk_Date,126)>Convert(varchar(7),'" & Me.C1DateEdit1.Text & "',126) Group By Yk_TkPf2.Yy_Code,Xx_Code) a where  Zd_YkPd.Yy_code=a.Yy_code and Zd_YkPd.Yy_code='" & HisVar.HisVar.WsyCode & "' and Zd_YkPd.Xx_Code=a.Xx_Code And Jsr_Code='" & HisVar.HisVar.JsrCode & "'And Pd_Month='" & Me.C1DateEdit1.Text & "'")
                    '上月结余

                    HisVar.HisVar.Sqldal.ExecuteSql("Update Zd_YkPd Set Mx_SySl=Mx_Sl-Mx_RkSl-Mx_YfThSl-Mx_DbSl-Mx_XsSl-Mx_KsSl-Mx_ThSl Where Yy_code='" & HisVar.HisVar.WsyCode & "' and Jsr_Code='" & HisVar.HisVar.JsrCode & "' And Pd_Month='" & Me.C1DateEdit1.Text & "'")
                Else

                    Exit Sub
                End If
            End If
        End If
        If Ds.Tables("盘点完成").Rows.Count = 0 Then
            If Ds.Tables("库存盘点").Rows.Count = 0 Then
                If MsgBox("是否确认在" + Format(Now, "yyyy年MM月dd日") + " " + Format(Now, "HH:mm:ss") + "做库存盘点操作？" + Chr(10) + Chr(13) + Chr(10) + Chr(13) + "(系统将此时刻的库存记录在案，在此基础上进行数据盘点工作。)", vbQuestion + vbYesNo + vbDefaultButton1, "提示:") = vbYes Then
                    HisVar.HisVar.Sqldal.ExecuteSql("Insert Into Zd_YkPd(Yy_Code,Jsr_Code,Xx_Code,Pd_Month,Pd_Time,Pd_Date,Mx_Sl,Pd_Sl,Yk_CgDj,Yk_XsDj) Select '" & HisVar.HisVar.WsyCode & "','" & HisVar.HisVar.JsrCode & "',Xx_Code,'" & Me.C1DateEdit1.Text & "','" & Format(Now, "HH:mm:ss") & "','" & Now & "',Yk_Sl,Yk_Sl,Yk_Cgj,Yk_Xsj From Zd_Ml_Yp4 where Zd_Ml_Yp4.Yy_code='" & HisVar.HisVar.WsyCode & "' ")


                    '本月入库
                    HisVar.HisVar.Sqldal.ExecuteSql("Update Zd_YkPd Set Mx_RkSl=Isnull(SySl,0) From (select Sum(Rk_Sl) AS SySl,Yk_Rk2.Yy_Code,Xx_Code From Yk_Rk2,Yk_Rk1 Where Yk_Rk1.Rk_Code=Yk_Rk2.Rk_Code And Convert(varchar(10),Rk_Date,126)>Convert(varchar(7),'" & Me.C1DateEdit1.Text & "',126) Group By Yk_Rk2.Yy_Code,Xx_Code) a where  Zd_YkPd.Yy_code=a.Yy_code and Zd_YkPd.Yy_code='" & HisVar.HisVar.WsyCode & "' and Zd_YkPd.Xx_Code=a.Xx_Code And Jsr_Code='" & HisVar.HisVar.JsrCode & "' And Pd_Month='" & Me.C1DateEdit1.Text & "'")
                    '药房退库
                    HisVar.HisVar.Sqldal.ExecuteSql("Update Zd_YkPd Set Mx_YfThSl=Isnull(SySl,0) From (select Sum(Tk_Sl) AS SySl,Yf_Tk2.Yy_Code,Xx_Code From Yf_Tk2,Yf_Tk1 Where Yf_Tk1.Tk_Code=Yf_Tk2.Tk_Code And Convert(varchar(10),Tk_Date,126)>Convert(varchar(7),'" & Me.C1DateEdit1.Text & "',126) Group By Yf_Tk2.Yy_Code,Xx_Code) a where  Zd_YkPd.Yy_code=a.Yy_code and Zd_YkPd.Yy_code='" & HisVar.HisVar.WsyCode & "' and Zd_YkPd.Xx_Code=a.Xx_Code And Jsr_Code='" & HisVar.HisVar.JsrCode & "'And Pd_Month='" & Me.C1DateEdit1.Text & "'")
                    '本月调拨
                    HisVar.HisVar.Sqldal.ExecuteSql("Update Zd_YkPd Set Mx_DbSl=-Isnull(SySl,0) From (select Sum(Ck_Sl) AS SySl,Yk_Yf2.Yy_Code,Xx_Code From Yk_Yf2,Yk_Yf1 Where Yk_Yf1.Ck_Code=Yk_Yf2.Ck_Code And Convert(varchar(10),Ck_Date,126)>Convert(varchar(7),'" & Me.C1DateEdit1.Text & "',126) Group By Yk_Yf2.Yy_Code,Xx_Code) a where Zd_YkPd.Yy_code=a.Yy_code and Zd_YkPd.Yy_code='" & HisVar.HisVar.WsyCode & "' and Zd_YkPd.Xx_Code=a.Xx_Code And Jsr_Code='" & HisVar.HisVar.JsrCode & "'And Pd_Month='" & Me.C1DateEdit1.Text & "'")
                    '普通销售
                    HisVar.HisVar.Sqldal.ExecuteSql("Update Zd_YkPd Set Mx_XsSl=-Isnull(SySl,0) From (select Sum(Ck_Sl) AS SySl,Yk_Pf2.Yy_Code,Xx_Code From Yk_Pf2,Yk_Pf1 Where Yk_Pf1.Ck_Code=Yk_Pf2.Ck_Code And Convert(varchar(10),Ck_Date,126)>Convert(varchar(7),'" & Me.C1DateEdit1.Text & "',126) Group By Yk_Pf2.Yy_Code,Xx_Code) a where  Zd_YkPd.Yy_code=a.Yy_code and Zd_YkPd.Yy_code='" & HisVar.HisVar.WsyCode & "' and Zd_YkPd.Xx_Code=a.Xx_Code And Jsr_Code='" & HisVar.HisVar.JsrCode & "'And Pd_Month='" & Me.C1DateEdit1.Text & "'")
                    '科室支领
                    HisVar.HisVar.Sqldal.ExecuteSql("Update Zd_YkPd Set Mx_KsSl=-Isnull(SySl,0) From (select Sum(Ck_Sl) AS SySl,Yk_Ks2.Yy_Code,Xx_Code From Yk_Ks2,Yk_Ks1 Where Yk_Ks1.Ck_Code=Yk_Ks2.Ck_Code And Convert(varchar(10),Ck_Date,126)>Convert(varchar(7),'" & Me.C1DateEdit1.Text & "',126) Group By Yk_Ks2.Yy_Code,Xx_Code) a where Zd_YkPd.Yy_code=a.Yy_code and Zd_YkPd.Yy_code='" & HisVar.HisVar.WsyCode & "' and Zd_YkPd.Xx_Code=a.Xx_Code And Jsr_Code='" & HisVar.HisVar.JsrCode & "'And Pd_Month='" & Me.C1DateEdit1.Text & "'")
                    '退供应商
                    HisVar.HisVar.Sqldal.ExecuteSql("Update Zd_YkPd Set Mx_ThSl=-Isnull(SySl,0) From (select Sum(Tk_Sl) AS SySl,Yk_TkPf2.Yy_Code,Xx_Code From Yk_TkPf2,Yk_TkPf1 Where Yk_TkPf1.Tk_Code=Yk_TkPf2.Tk_Code And Convert(varchar(10),Tk_Date,126)>Convert(varchar(7),'" & Me.C1DateEdit1.Text & "',126) Group By Yk_TkPf2.Yy_Code,Xx_Code) a where  Zd_YkPd.Yy_code=a.Yy_code and Zd_YkPd.Yy_code='" & HisVar.HisVar.WsyCode & "' and Zd_YkPd.Xx_Code=a.Xx_Code And Jsr_Code='" & HisVar.HisVar.JsrCode & "'And Pd_Month='" & Me.C1DateEdit1.Text & "'")
                    '上月结余
                    HisVar.HisVar.Sqldal.ExecuteSql("Update Zd_YkPd Set Mx_SySl=Mx_Sl-Mx_RkSl-Mx_YfThSl-Mx_DbSl-Mx_XsSl-Mx_KsSl-Mx_ThSl Where Yy_code='" & HisVar.HisVar.WsyCode & "' and Jsr_Code='" & HisVar.HisVar.JsrCode & "' And Pd_Month='" & Me.C1DateEdit1.Text & "'")
                Else

                    Exit Sub

                End If
            Else
                MessageBox.Show("本月已进行过数据盘点！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
                Exit Sub
            End If
        Else
        End If
        Dim vform As New YkYf_Pd2(Me, C1DateEdit1.Value, YfCode, YfName, FormLb)
        vform.Tag = Me.Name
        vform.Text = Me.Text
        BaseFunc.BaseFunc.addTabControl(vform, vform.Text)
    End Sub

    Private Sub Insert_Yf_Pd()

        Dim Yf_Sl As String

        Yf_Sl = "Yf_Sl" & CDbl(Mid(HisVar.HisVar.YfCode, 5, 2))

        Dim Yf_lsj As String

        Yf_lsj = "Yf_Lsj" & CDbl(Mid(HisVar.HisVar.YfCode, 5, 2))

        If Ds.Tables("库存盘点") IsNot Nothing Then Ds.Tables("库存盘点").Clear()
        If Ds.Tables("盘点完成") IsNot Nothing Then Ds.Tables("盘点完成").Clear()

        Adapter = New SqlDataAdapter("Select Top 1 * From Zd_YfPd Where  Yf_Code='" & HisVar.HisVar.YfCode & "' and Isnull(Pd_Wc,'')='否'", My_Cn)
        Adapter.Fill(Ds, "盘点完成")

        Adapter = New SqlDataAdapter("Select Top 1 * From Zd_YfPd Where  Yf_Code='" & HisVar.HisVar.YfCode & "' and Pd_Month='" & Me.C1DateEdit1.Text & "'", My_Cn)
        Adapter.Fill(Ds, "库存盘点")

        Dim arr As New ArrayList
        If Ds.Tables("盘点完成").Rows.Count > 0 Then
            If Me.C1DateEdit1.Text <> Ds.Tables("盘点完成").Rows(0).Item("Pd_Month") Then
                MsgBox("上次未执行盘点完成，数据作废，本月将进行新一次盘点！", vbExclamation + vbOKOnly + vbDefaultButton1, "提示:")
                HisVar.HisVar.Sqldal.ExecuteSql("Delete from Zd_YfPd where Pd_Month='" & Ds.Tables("盘点完成").Rows(0).Item("Pd_Month") & "' and Yy_Code='" & HisVar.HisVar.WsyCode & "' and Yf_Code='" & HisVar.HisVar.YfCode & "'")
                If MsgBox("是否确认在" + Format(Now, "yyyy年MM月dd日 HH:mm:ss") + "做库存盘点操作？" + Chr(10) + Chr(13) + Chr(10) + Chr(13) + "(系统将此时刻的库存记录在案，在此基础上进行数据盘点工作。)", vbQuestion + vbYesNo + vbDefaultButton1, "提示:") = vbYes Then

                    Dim V_Table As String = "#" & Format(Now, "yyyyMMddHHmmss")
                    arr.Add(" Select '" & HisVar.HisVar.WsyCode & "' as Yy_code,'" & HisVar.HisVar.YfCode & "' as Yf_Code,'" & HisVar.HisVar.JsrCode & "' as Jsr_Code,Xx_Code,'" & Me.C1DateEdit1.Text & "' as Pd_Month,'" & Format(Now, "HH:mm:ss") & "' as Pd_Time,'" & Now & "' as Pd_Date,isnull(" & Yf_Sl & ",0) as Mx_Sl,isnull(" & Yf_Sl & ",0) as Pd_Sl,Yk_Cgj/Mx_Cfbl as Yk_CgDj," & Yf_lsj & " as Yk_XsDj,0 as Mx_RkSl,0 as Mx_MzThSl,0 as Mx_MzSl ,0 as Mx_ZySl,0 as Mx_TkSl,0 as Mx_KsSl,0 as Mx_SySl into " & V_Table & " From V_YpKc ")
                    arr.Add("Update Zd_YfPd Set Mx_RkSl=SySl From (select Sum(CK_SL*Yk_Yf2.Mx_Cfbl) AS SySl,Yk_Yf2.Yy_Code,Yf_code,Yk_Yf2.Xx_Code From V_YpKc,Yk_Yf1,Yk_Yf2 Where Yk_Yf2.Xx_Code=V_YPKc.Xx_Code and Yk_Yf1.Ck_Code=Yk_Yf2.Ck_Code And Convert(varchar(10),Ck_Date,126)>Convert(varchar(7),'" & Me.C1DateEdit1.Text & "',126) Group By Yk_Yf2.Yy_code,Yf_code,Yk_Yf2.Xx_Code) a where  a.Yf_code=Zd_YfPd.Yf_code and a.Yy_code=Zd_YfPd.Yy_code  and Zd_YfPd.Yf_Code='" & HisVar.HisVar.YfCode & "' and Zd_YfPd.Xx_Code=a.Xx_Code And Jsr_Code='" & HisVar.HisVar.JsrCode & "'And Pd_Month='" & C1DateEdit1.Text & "'")
                    '门诊退回
                    arr.Add("Update " & V_Table & " Set Mx_MzThSl=SySl From (select Sum(Mz_Sl) AS SySl,mz.Yy_code,Yf_code,Xx_Code From mz_yp,mz Where mz.Mz_Code=mz_yp.Mz_Code And Convert(varchar(10),Mz_Date,126)>Convert(varchar(7),'" & Me.C1DateEdit1.Text & "',126)  And mz_yp.Mz_Code in (Select Mz_Code From Mz_Ty Where Yf_Code='" & HisVar.HisVar.YfCode & "' and Yy_code='" & HisVar.HisVar.WsyCode & "' and Ty_Qr='1') Group By mz.Yy_code,Yf_code,Xx_Code) a where  a.Yf_code=" & V_Table & ".Yf_code and a.Yy_code=" & V_Table & ".Yy_code and " & V_Table & ".Yy_code='" & HisVar.HisVar.WsyCode & "' and " & V_Table & ".Yf_Code='" & HisVar.HisVar.YfCode & "' and " & V_Table & ".Xx_Code=a.Xx_Code And Jsr_Code='" & HisVar.HisVar.JsrCode & "'And Pd_Month='" & C1DateEdit1.Text & "'")
                    arr.Add("Update " & V_Table & " Set Mx_MzThSl=isnull(Mx_MzThSl,0)+SySl From (select Sum(Mz_Sl) AS SySl,mz_sum.Yy_code,Yf_code,Xx_Code From mz_yp_Sum,mz_Sum Where mz_Sum.Mz_Code=mz_yp_Sum.Mz_Code And Convert(varchar(10),Mz_Date,126)>Convert(varchar(7),'" & Me.C1DateEdit1.Text & "',126)  And mz_yp_Sum.Mz_Code in (Select Mz_Code From Mz_Ty_Sum Where Yf_Code='" & HisVar.HisVar.YfCode & "' and Yy_code='" & HisVar.HisVar.WsyCode & "' and Ty_Qr='1') Group By mz_sum.Yy_code,Yf_code,Xx_Code) a where  a.Yf_code=" & V_Table & ".Yf_code and a.Yy_code=" & V_Table & ".Yy_code and " & V_Table & ".Yy_code='" & HisVar.HisVar.WsyCode & "' and " & V_Table & ".Yf_Code='" & HisVar.HisVar.YfCode & "' and " & V_Table & ".Xx_Code=a.Xx_Code And Jsr_Code='" & HisVar.HisVar.JsrCode & "'And Pd_Month='" & C1DateEdit1.Text & "'")
                    '门诊销售
                    arr.Add("Update " & V_Table & " Set Mx_MzSl=-SySl From (select Sum(Mz_Sl) AS SySl,mz.Yy_code,Yf_code,Xx_Code From mz_yp,mz Where mz.Mz_Code=mz_yp.Mz_Code And Convert(varchar(10),Mz_Date,126)>Convert(varchar(7),'" & Me.C1DateEdit1.Text & "',126)  Group By mz.Yy_code,Yf_code,Xx_Code) a where  a.Yf_code=" & V_Table & ".Yf_code and a.Yy_code=" & V_Table & ".Yy_code and " & V_Table & ".Yy_code='" & HisVar.HisVar.WsyCode & "' and " & V_Table & ".Yf_Code='" & HisVar.HisVar.YfCode & "' and " & V_Table & ".Xx_Code=a.Xx_Code And Jsr_Code='" & HisVar.HisVar.JsrCode & "'And Pd_Month='" & C1DateEdit1.Text & "'")
                    arr.Add("Update " & V_Table & " Set Mx_MzSl=Isnull(Mx_MzSl,0)-SySl From (select Sum(Mz_Sl) AS SySl,mz_Sum.Yy_code,Yf_code,Xx_Code From mz_yp_Sum,mz_Sum Where mz_Sum.Mz_Code=mz_yp_Sum.Mz_Code And Convert(varchar(10),Mz_Date,126)>Convert(varchar(7),'" & Me.C1DateEdit1.Text & "',126)  Group By mz_Sum.Yy_code,Yf_code,Xx_Code) a where  a.Yf_code=" & V_Table & ".Yf_code and a.Yy_code=" & V_Table & ".Yy_code and " & V_Table & ".Yy_code='" & HisVar.HisVar.WsyCode & "' and " & V_Table & ".Yf_Code='" & HisVar.HisVar.YfCode & "' and " & V_Table & ".Xx_Code=a.Xx_Code And Jsr_Code='" & HisVar.HisVar.JsrCode & "'And Pd_Month='" & C1DateEdit1.Text & "'")
                    '住院销售
                    arr.Add("Update " & V_Table & " Set Mx_ZySl=-SySl From (select Sum(Cf_Sl) AS SySl,Bl_Cf.Yy_Code,Yf_code,Xx_Code From Bl_Cfyp,Bl_Cf Where Bl_Cf.Cf_Code=Bl_Cfyp.Cf_Code And Convert(varchar(10),Cf_Date,126)>Convert(varchar(7),'" & Me.C1DateEdit1.Text & "',126) Group By Bl_Cf.Yy_Code,Yf_code,Xx_Code) a where  a.Yf_code=" & V_Table & ".Yf_code and a.Yy_code=" & V_Table & ".Yy_code and " & V_Table & ".Yy_code='" & HisVar.HisVar.WsyCode & "' and " & V_Table & ".Yf_Code='" & HisVar.HisVar.YfCode & "' and " & V_Table & ".Xx_Code=a.Xx_Code And Jsr_Code='" & HisVar.HisVar.JsrCode & "'And Pd_Month='" & C1DateEdit1.Text & "'")
                    '退回药库
                    arr.Add("Update " & V_Table & " Set Mx_TkSl=-SySl From (select Sum(Tk_Sl) AS SySl,Yf_Tk2.Yy_Code,Yf_code,Xx_Code From Yf_Tk2,Yf_Tk1 Where Yf_Tk1.Tk_Code=Yf_Tk2.Tk_Code And Convert(varchar(10),Tk_Date,126)>Convert(varchar(7),'" & Me.C1DateEdit1.Text & "',126) Group By Yf_Tk2.Yy_Code,Yf_code,Xx_Code) a where  a.Yf_code=" & V_Table & ".Yf_code and a.Yy_code=" & V_Table & ".Yy_code and " & V_Table & ".Yy_code='" & HisVar.HisVar.WsyCode & "' and " & V_Table & ".Yf_Code='" & HisVar.HisVar.YfCode & "' and " & V_Table & ".Xx_Code=a.Xx_Code And Jsr_Code='" & HisVar.HisVar.JsrCode & "'And Pd_Month='" & C1DateEdit1.Text & "'")
                    '科室支领
                    arr.Add("Update " & V_Table & " Set Mx_KsSl=-SySl From (select Sum(Ck_Sl) AS SySl,Yf_Ks2.Yy_Code,Yf_code,Xx_Code From Yf_Ks2,Yf_Ks1 Where Yf_Ks1.Ck_Code=Yf_Ks2.Ck_Code And Convert(varchar(10),Ck_Date,126)>Convert(varchar(7),'" & Me.C1DateEdit1.Text & "',126) Group By Yf_Ks2.Yy_Code,Yf_code,Xx_Code) a where  a.Yf_code=" & V_Table & ".Yf_code and a.Yy_code=" & V_Table & ".Yy_code and " & V_Table & ".Yy_code='" & HisVar.HisVar.WsyCode & "' and " & V_Table & ".Yf_Code='" & HisVar.HisVar.YfCode & "' and " & V_Table & ".Xx_Code=a.Xx_Code And Jsr_Code='" & HisVar.HisVar.JsrCode & "'And Pd_Month='" & C1DateEdit1.Text & "'")
                    '上月结余
                    arr.Add("Update " & V_Table & " Set Mx_SySl=Mx_Sl-Mx_RkSl-Mx_MzThSl-Mx_TkSl-Mx_KsSl-Mx_MzSl-Mx_ZySl Where Yy_Code='" & HisVar.HisVar.WsyCode & "' and Yf_Code= '" & HisVar.HisVar.YfCode & "' and  Jsr_Code='" & HisVar.HisVar.JsrCode & "'And Pd_Month='" & C1DateEdit1.Text & "'")
                    '临时表插入数据库
                    arr.Add("Insert into Zd_Yfpd(Yy_code, Yf_Code, Jsr_Code,Xx_Code, Pd_Month, Pd_Time, Pd_Date, Mx_Sl,Pd_Sl, Yk_CgDj, Yk_XsDj, Mx_RkSl, Mx_MzThSl, Mx_MzSl ,Mx_ZySl, Mx_TkSl, Mx_KsSl, Mx_SySl) select  Yy_code, Yf_Code, Jsr_Code,Xx_Code, Pd_Month, Pd_Time, Pd_Date, Mx_Sl,Pd_Sl, Yk_CgDj, Yk_XsDj, Mx_RkSl, Mx_MzThSl, Mx_MzSl ,Mx_ZySl, Mx_TkSl, Mx_KsSl, Mx_SySl from " & V_Table & "")
                    '删除临时表
                    arr.Add("Truncate Table " & V_Table & "")
                    HisVar.HisVar.Sqldal.ExecuteSqlTran(arr)
                Else
                    Exit Sub
                End If
            End If
        End If

        If Ds.Tables("盘点完成").Rows.Count = 0 Then
            If Ds.Tables("库存盘点").Rows.Count = 0 Then
                If MsgBox("是否确认在" + Format(Now, "yyyy年MM月dd日 HH:mm:ss") + "做库存盘点操作？" + Chr(10) + Chr(13) + Chr(10) + Chr(13) + "(系统将此时刻的库存记录在案，在此基础上进行数据盘点工作。)", vbQuestion + vbYesNo + vbDefaultButton1, "提示:") = vbYes Then

                    Dim V_Table As String = "#" & Format(Now, "yyyyMMddHHmmss")
                    '向临时表中插入数据
                    arr.Add(" Select '" & HisVar.HisVar.WsyCode & "' as Yy_code,'" & HisVar.HisVar.YfCode & "' as Yf_Code,'" & HisVar.HisVar.JsrCode & "' as Jsr_Code,Xx_Code,'" & Me.C1DateEdit1.Text & "' as Pd_Month,'" & Format(Now, "HH:mm:ss") & "' as Pd_Time,'" & Now & "' as Pd_Date,isnull(" & Yf_Sl & ",0) as Mx_Sl,isnull(" & Yf_Sl & ",0) as Pd_Sl,Yk_Cgj/Mx_Cfbl as Yk_CgDj," & Yf_lsj & " as Yk_XsDj,0 as Mx_RkSl,0 as Mx_MzThSl,0 as Mx_MzSl ,0 as Mx_ZySl,0 as Mx_TkSl,0 as Mx_KsSl,0 as Mx_SySl into " & V_Table & " From V_YpKc ")
                    '药房接受调拨
                    arr.Add("Update Zd_YfPd Set Mx_RkSl=SySl From (select Sum(CK_SL*Yk_Yf2.Mx_Cfbl) AS SySl,Yk_Yf2.Yy_Code,Yf_code,Yk_Yf2.Xx_Code From V_YpKc,Yk_Yf1,Yk_Yf2 Where Yk_Yf2.Xx_Code=V_YPKc.Xx_Code and Yk_Yf1.Ck_Code=Yk_Yf2.Ck_Code And Convert(varchar(10),Ck_Date,126)>Convert(varchar(7),'" & Me.C1DateEdit1.Text & "',126) Group By Yk_Yf2.Yy_code,Yf_code,Yk_Yf2.Xx_Code) a where  a.Yf_code=Zd_YfPd.Yf_code and a.Yy_code=Zd_YfPd.Yy_code  and Zd_YfPd.Yf_Code='" & HisVar.HisVar.YfCode & "' and Zd_YfPd.Xx_Code=a.Xx_Code And Jsr_Code='" & HisVar.HisVar.JsrCode & "'And Pd_Month='" & C1DateEdit1.Text & "'")
                    '门诊退回
                    arr.Add("Update " & V_Table & " Set Mx_MzThSl=SySl From (select Sum(Mz_Sl) AS SySl,mz.Yy_code,Yf_code,Xx_Code From mz_yp,mz Where mz.Mz_Code=mz_yp.Mz_Code And Convert(varchar(10),Mz_Date,126)>Convert(varchar(7),'" & Me.C1DateEdit1.Text & "',126)  And mz_yp.Mz_Code in (Select Mz_Code From Mz_Ty Where Yf_Code='" & HisVar.HisVar.YfCode & "'  and Ty_Qr='1') Group By mz.Yy_code,Yf_code,Xx_Code) a where  a.Yf_code=" & V_Table & ".Yf_code  and " & V_Table & ".Yf_Code='" & HisVar.HisVar.YfCode & "' and " & V_Table & ".Xx_Code=a.Xx_Code And Jsr_Code='" & HisVar.HisVar.JsrCode & "'And Pd_Month='" & C1DateEdit1.Text & "'")
                    arr.Add("Update " & V_Table & " Set Mx_MzThSl=isnull(Mx_MzThSl,0)+SySl From (select Sum(Mz_Sl) AS SySl,mz_sum.Yy_code,Yf_code,Xx_Code From mz_yp_Sum,mz_Sum Where mz_Sum.Mz_Code=mz_yp_Sum.Mz_Code And Convert(varchar(10),Mz_Date,126)>Convert(varchar(7),'" & Me.C1DateEdit1.Text & "',126)  And mz_yp_Sum.Mz_Code in (Select Mz_Code From Mz_Ty_Sum Where Yf_Code='" & HisVar.HisVar.YfCode & "'  and Ty_Qr='1') Group By mz_sum.Yy_code,Yf_code,Xx_Code) a where  a.Yf_code=" & V_Table & ".Yf_code  and " & V_Table & ".Yf_Code='" & HisVar.HisVar.YfCode & "' and " & V_Table & ".Xx_Code=a.Xx_Code And Jsr_Code='" & HisVar.HisVar.JsrCode & "'And Pd_Month='" & C1DateEdit1.Text & "'")
                    '门诊销售
                    arr.Add("Update " & V_Table & " Set Mx_MzSl=-SySl From (select Sum(Mz_Sl) AS SySl,mz.Yy_code,Yf_code,Xx_Code From mz_yp,mz Where mz.Mz_Code=mz_yp.Mz_Code And Convert(varchar(10),Mz_Date,126)>Convert(varchar(7),'" & Me.C1DateEdit1.Text & "',126)  Group By mz.Yy_code,Yf_code,Xx_Code) a where  a.Yf_code=" & V_Table & ".Yf_code  and " & V_Table & ".Yf_Code='" & HisVar.HisVar.YfCode & "' and " & V_Table & ".Xx_Code=a.Xx_Code And Jsr_Code='" & HisVar.HisVar.JsrCode & "'And Pd_Month='" & C1DateEdit1.Text & "'")
                    arr.Add("Update " & V_Table & " Set Mx_MzSl=Isnull(Mx_MzSl,0)-SySl From (select Sum(Mz_Sl) AS SySl,mz_Sum.Yy_code,Yf_code,Xx_Code From mz_yp_Sum,mz_Sum Where mz_Sum.Mz_Code=mz_yp_Sum.Mz_Code And Convert(varchar(10),Mz_Date,126)>Convert(varchar(7),'" & Me.C1DateEdit1.Text & "',126)  Group By mz_Sum.Yy_code,Yf_code,Xx_Code) a where  a.Yf_code=" & V_Table & ".Yf_code  and " & V_Table & ".Yf_Code='" & HisVar.HisVar.YfCode & "' and " & V_Table & ".Xx_Code=a.Xx_Code And Jsr_Code='" & HisVar.HisVar.JsrCode & "'And Pd_Month='" & C1DateEdit1.Text & "'")
                    '住院销售
                    arr.Add("Update " & V_Table & " Set Mx_ZySl=-SySl From (select Sum(Cf_Sl) AS SySl,Bl_Cf.Yy_Code,Yf_code,Xx_Code From Bl_Cfyp,Bl_Cf Where Bl_Cf.Cf_Code=Bl_Cfyp.Cf_Code And Convert(varchar(10),Cf_Date,126)>Convert(varchar(7),'" & Me.C1DateEdit1.Text & "',126) Group By Bl_Cf.Yy_Code,Yf_code,Xx_Code) a where  a.Yf_code=" & V_Table & ".Yf_code  and " & V_Table & ".Yf_Code='" & HisVar.HisVar.YfCode & "' and " & V_Table & ".Xx_Code=a.Xx_Code And Jsr_Code='" & HisVar.HisVar.JsrCode & "'And Pd_Month='" & C1DateEdit1.Text & "'")
                    '退回药库
                    arr.Add("Update " & V_Table & " Set Mx_TkSl=-SySl From (select Sum(Tk_Sl) AS SySl,Yf_Tk2.Yy_Code,Yf_code,Xx_Code From Yf_Tk2,Yf_Tk1 Where Yf_Tk1.Tk_Code=Yf_Tk2.Tk_Code And Convert(varchar(10),Tk_Date,126)>Convert(varchar(7),'" & Me.C1DateEdit1.Text & "',126) Group By Yf_Tk2.Yy_Code,Yf_code,Xx_Code) a where  a.Yf_code=" & V_Table & ".Yf_code  and " & V_Table & ".Yf_Code='" & HisVar.HisVar.YfCode & "' and " & V_Table & ".Xx_Code=a.Xx_Code And Jsr_Code='" & HisVar.HisVar.JsrCode & "'And Pd_Month='" & C1DateEdit1.Text & "'")
                    '科室支领
                    arr.Add("Update " & V_Table & " Set Mx_KsSl=-SySl From (select Sum(Ck_Sl) AS SySl,Yf_Ks2.Yy_Code,Yf_code,Xx_Code From Yf_Ks2,Yf_Ks1 Where Yf_Ks1.Ck_Code=Yf_Ks2.Ck_Code And Convert(varchar(10),Ck_Date,126)>Convert(varchar(7),'" & Me.C1DateEdit1.Text & "',126) Group By Yf_Ks2.Yy_Code,Yf_code,Xx_Code) a where  a.Yf_code=" & V_Table & ".Yf_code  and " & V_Table & ".Yf_Code='" & HisVar.HisVar.YfCode & "' and " & V_Table & ".Xx_Code=a.Xx_Code And Jsr_Code='" & HisVar.HisVar.JsrCode & "'And Pd_Month='" & C1DateEdit1.Text & "'")
                    '上月结余
                    arr.Add("Update " & V_Table & " Set Mx_SySl=Mx_Sl-Mx_RkSl-Mx_MzThSl-Mx_TkSl-Mx_KsSl-Mx_MzSl-Mx_ZySl Where Yy_Code='" & HisVar.HisVar.WsyCode & "' and Yf_Code= '" & HisVar.HisVar.YfCode & "' and  Jsr_Code='" & HisVar.HisVar.JsrCode & "'And Pd_Month='" & C1DateEdit1.Text & "'")
                    '临时表插入数据库
                    arr.Add("Insert into Zd_Yfpd(Yy_code, Yf_Code, Jsr_Code,Xx_Code, Pd_Month, Pd_Time, Pd_Date, Mx_Sl,Pd_Sl, Yk_CgDj, Yk_XsDj, Mx_RkSl, Mx_MzThSl, Mx_MzSl ,Mx_ZySl, Mx_TkSl, Mx_KsSl, Mx_SySl) select  Yy_code, Yf_Code, Jsr_Code,Xx_Code, Pd_Month, Pd_Time, Pd_Date, Mx_Sl,Pd_Sl, Yk_CgDj, Yk_XsDj, Mx_RkSl, Mx_MzThSl, Mx_MzSl ,Mx_ZySl, Mx_TkSl, Mx_KsSl, Mx_SySl from " & V_Table & "")
                    '删除临时表
                    arr.Add("Truncate Table " & V_Table & "")
                    HisVar.HisVar.Sqldal.ExecuteSqlTran(arr)
                Else
                    Exit Sub
                End If
            Else
                MsgBox("本月已进行过盘点，不能再进行盘点！", vbExclamation + vbOKOnly + vbDefaultButton1, "提示:")
                Exit Sub
            End If
        End If

        Dim vform As New YkYf_Pd2(Me, C1DateEdit1.Value, YfCode, YfName, FormLb)
        vform.Tag = Me.Name
        vform.Text = Me.Text
        BaseFunc.BaseFunc.addTabControl(vform, vform.Text)
    End Sub

#End Region

    Private Sub C1TextBox2_KeyPress(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles C1TextBox2.KeyPress
        Select Case e.KeyChar
            Case Chr(Keys.Enter)
                e.Handled = True
                Call C1Button1_Click(Nothing, Nothing)
        End Select
    End Sub

    Private Sub C1Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Button1.Click
        C1Button1.Enabled = False

        If encode.F_Encode(C1TextBox2.Text) <> V_PassWord Then

            MsgBox("盘点密码不正确，请重新输入！", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示！")
            C1Button1.Enabled = True
            C1TextBox2.Text = ""
            C1TextBox2.Select()
            Exit Sub
        Else
            If FormLb = "药库数据盘点" Then
                Call Insert_Yk_Pd()
            ElseIf Formlb = "药房数据盘点" Then
                Call Insert_Yf_Pd()
            End If

            C1TextBox2.Clear()
        End If
        C1Button1.Enabled = True
    End Sub

    Private Sub C1Button2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Button2.Click
        If FormLb = "药库数据盘点" Then
            If Ds.Tables("盘点生效") IsNot Nothing Then Ds.Tables("盘点生效").Clear()
            Adapter = New SqlDataAdapter("Select Top 1 * From Zd_YkPd Where Yy_code='" & HisVar.HisVar.WsyCode & "' and Pd_Wc='否'", My_Cn)
            Adapter.Fill(Ds, "盘点生效")

            If Ds.Tables("盘点生效").Rows.Count > 0 Then
                If MsgBox("是否确认在" + Format(Ds.Tables("盘点生效").Rows(0).Item("Pd_Date"), "yyyy年MM月dd日") + " " + Format(Ds.Tables("盘点生效").Rows(0).Item("Pd_Date"), "HH:mm:ss") + "的盘点数据已全部录入完成？" + Chr(10) + Chr(13) + Chr(10) + Chr(13) + "(系统将本次操作的全部数据做为下次盘点的期初数据。)", vbQuestion + vbYesNo + vbDefaultButton1, "提示:") = vbYes Then
                    Dim str As New ArrayList
                    str.Add("Update Zd_Ml_Yp4 Set Zd_Ml_Yp4.Yk_Sl=Pd_Sl From Zd_Ml_Yp4,Zd_YkPd Where Zd_Ml_Yp4.Xx_Code=Zd_YkPd.Xx_Code and Zd_Ml_Yp4.Yy_Code=Zd_YkPd.Yy_Code and Zd_Ml_Yp4.Yy_Code='" & HisVar.HisVar.WsyCode & "' And Pd_Wc='否'")
                    str.Add("Update Zd_YkPd Set Pd_Wc='是',Jsr_Code='" & HisVar.HisVar.JsrCode & "',Pd_Finish_Date='" & Format(Now, "yyyy-MM-dd HH:mm:ss") & "' Where Yy_code='" & HisVar.HisVar.WsyCode & "' and  Pd_Wc='否'")
                    HisVar.HisVar.Sqldal.ExecuteSqlTran(str)
                    MsgBox("盘点数据结转完成。", vbExclamation + vbOKOnly + vbDefaultButton1, "提示:")
                End If
            Else
                MsgBox("没有盘点数据要结转，操作被取消。", vbExclamation + vbOKOnly + vbDefaultButton1, "提示:")
            End If
        ElseIf FormLb = "药房数据盘点" Then
            If Ds.Tables("盘点生效") IsNot Nothing Then Ds.Tables("盘点生效").Clear()
            Adapter = New SqlDataAdapter("Select Top 1 * From Zd_YfPd Where Yy_Code='" & HisVar.HisVar.WsyCode & "' and Yf_Code= '" & HisVar.HisVar.YfCode & "' and Pd_Wc='否'", My_Cn)
            Adapter.Fill(Ds, "盘点生效")

            If Ds.Tables("盘点生效").Rows.Count > 0 Then

                Dim Yf_Sl As String

                Yf_Sl = "Yf_Sl" & CDbl(Mid(HisVar.HisVar.YfCode, 5, 2))

                If MsgBox("是否确认在" + Format(Ds.Tables("盘点生效").Rows(0).Item("Pd_Date"), "yyyy年MM月dd日") + " " + Ds.Tables("盘点生效").Rows(0).Item("Pd_Time") + "的盘点数据已全部录入完成？" + Chr(10) + Chr(13) + Chr(10) + Chr(13) + "(系统将本次操作的全部数据做为下次盘点的期初数据。)", vbQuestion + vbYesNo + vbDefaultButton1, "提示:") = vbYes Then
                    Dim str As New ArrayList
                    str.Add("Update Zd_Ml_Yp4 Set Zd_Ml_Yp4." & Yf_Sl & " =Pd_Sl From Zd_Ml_Yp4,Zd_YfPd Where Zd_Ml_Yp4.Yy_Code='" & HisVar.HisVar.WsyCode & "'  and Zd_Ml_Yp4.Yy_Code=Zd_YfPd.Yy_Code and Yf_Code= '" & HisVar.HisVar.YfCode & "'and Zd_Ml_Yp4.Xx_Code=Zd_YfPd.Xx_Code And Pd_Wc='否'")
                    str.Add("Update Zd_YfPd Set Pd_Wc='是',Jsr_Code='" & HisVar.HisVar.JsrCode & "',Pd_Finish_Date='" & Format(Now, "yyyy-MM-dd HH:mm:ss") & "' Where Yy_Code='" & HisVar.HisVar.WsyCode & "' and Yf_Code='" & HisVar.HisVar.YfCode & "'and Pd_Wc='否'")
                    MsgBox("盘点数据结转完成。", vbExclamation + vbOKOnly + vbDefaultButton1, "提示:")
                    HisVar.HisVar.Sqldal.ExecuteSqlTran(str)
                End If
            Else
                MsgBox("没有盘点数据要结转，操作被取消。", vbExclamation + vbOKOnly + vbDefaultButton1, "提示:")
            End If
        End If
     
    End Sub
End Class