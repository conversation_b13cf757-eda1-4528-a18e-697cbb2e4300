﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="0" />
    <DataSources isList="true" count="1">
      <收费明细 Ref="2" type="DataTableSource" isKey="true">
        <Alias>收费明细</Alias>
        <Columns isList="true" count="4">
          <value>V_Group,System.Int64</value>
          <value>Mz_Lb,System.String</value>
          <value>Mz_Sl,System.Int32</value>
          <value>Mz_Money,System.Decimal</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>收费明细</Name>
        <NameInSource>收费明细</NameInSource>
      </收费明细>
    </DataSources>
    <Relations isList="true" count="0" />
    <Report isRef="0" />
    <Variables isList="true" count="16">
      <value>,姓名,姓名,System.String,,False,False</value>
      <value>,性别,性别,System.String,,False,False</value>
      <value>,社保号码,社保号码,System.String,,False,False</value>
      <value>,业务流水号,业务流水号,System.String,,False,False</value>
      <value>,缴费日期,缴费日期,System.String,,False,False</value>
      <value>,医保统筹支付,医保统筹支付,System.String,,False,False</value>
      <value>,个人账户支付,个人账户支付,System.String,,False,False</value>
      <value>,其他医保支付,其他医保支付,System.String,,False,False</value>
      <value>,个人支付金额,个人支付金额,System.String,,False,False</value>
      <value>,报销类别,报销类别,System.String,,False,False</value>
      <value>,科室,科室,System.String,,False,False</value>
      <value>,类型,类型,System.String,,False,False</value>
      <value>,医疗机构,医疗机构,System.String,,False,False</value>
      <value>,医保类型,医保类型,System.String,,False,False</value>
      <value>,收款人,收款人,System.String,,False,False</value>
      <value>,打印时间,打印时间,System.String,,False,False</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="3" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="5">
        <Text27 Ref="4" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>6.8,24.7,1.3,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Microsoft Sans Serif,8.25,Regular,Point,False,134</Font>
          <Guid>f6b2f24201c9450bb6e8dd54b4181fda</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text27</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Text>金额</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Bottom</VertAlignment>
        </Text27>
        <Text120 Ref="5" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>3.2,-0.6,19.4,1</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text120</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <TextBrush>Black</TextBrush>
        </Text120>
        <GroupHeaderBand1 Ref="6" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,16.2,2.2</ClientRectangle>
          <Components isList="true" count="9">
            <Text37 Ref="7" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.1,6.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>d1f710b3f6cd4e4e94d0a8d44c1d404a</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text37</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Text>{医疗机构}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text37>
            <Text48 Ref="8" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.3,0.6,2.8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>f1033e5cb14e40f7b2d7c02e19f023fc</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text48</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Text>{业务流水号}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text48>
            <Text60 Ref="9" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,16.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>de75c2bf151d45cca4e53d05fcbacf73</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text60</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <TextBrush>Black</TextBrush>
            </Text60>
            <Text8 Ref="10" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.6,1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>ba9cafd58af948f0b4b8976a0a0b9fa7</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text8</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Text>姓名：
</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text8>
            <Text9 Ref="11" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1,1.6,2.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>8888ebcc1ee44c6b9e371d23696042e1</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text9</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Text>{姓名}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text9>
            <Text13 Ref="12" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>3.5,1.6,1.3,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>d71ed76c76474c5da6f65fd6ffc990a3</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text13</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Text>{性别}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text13>
            <Text11 Ref="13" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.8,1.6,2.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>445bbfb496d84a70b82671250403263a</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text11</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Text>{医保类型}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text11>
            <Text17 Ref="14" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7,1.6,3.1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>e435ecb6995b4735a527ac824d702f47</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text17</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Text>{社保号码}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text17>
            <Text1 Ref="15" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.6,1.3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>a4498b7295584171884bd4a4fd43f863</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text1</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Text>流水号：
</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text1>
          </Components>
          <Condition>{收费明细.V_Group}</Condition>
          <Conditions isList="true" count="0" />
          <Name>GroupHeaderBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </GroupHeaderBand1>
        <DataBand1 Ref="16" type="DataBand" isKey="true">
          <Border>None;Black;2;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,3.4,16.2,0.5</ClientRectangle>
          <ColumnDirection>DownThenAcross</ColumnDirection>
          <Columns>2</Columns>
          <Components isList="true" count="4">
            <Text26 Ref="17" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>3.1,0,0.8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>cf8245b781b241699d2390cd188a5f47</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text26</Name>
              <NullValue>                               </NullValue>
              <Page isRef="3" />
              <Parent isRef="16" />
              <Text>{收费明细.Mz_Sl}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="18" type="CustomFormat" isKey="true">
                <StringFormat>0.####</StringFormat>
              </TextFormat>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text26>
            <Text29 Ref="19" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>3.9,0,1.85,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>3b3a939e7b59422592930d597aecdf53</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text29</Name>
              <NullValue>  </NullValue>
              <Page isRef="3" />
              <Parent isRef="16" />
              <Text>{收费明细.Mz_Money}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text29>
            <Text30 Ref="20" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>5.75,0,2.35,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>47a3e0c9e6ff4fdf84a2b5a4a22b4792</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text30</Name>
              <Page isRef="3" />
              <Parent isRef="16" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text30>
            <Text5 Ref="21" type="Text" isKey="true">
              <Border>None;Black;1;Solid;False;4;Transparent</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,3.1,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>073ce9d0318c4441a9e00a1be3eee141</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text5</Name>
              <Page isRef="3" />
              <Parent isRef="16" />
              <Text>{收费明细.Mz_Lb}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text5>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>收费明细</DataSourceName>
          <Filters isList="true" count="0" />
          <Name>DataBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Sort isList="true" count="2">
            <value>DESC</value>
            <value>Mz_Lb</value>
          </Sort>
        </DataBand1>
        <GroupFooterBand1 Ref="22" type="GroupFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,4.7,16.2,0.9</ClientRectangle>
          <Components isList="true" count="7">
            <Text43 Ref="23" type="Text" isKey="true">
              <Border>None;Black;1;Solid;False;4;Transparent</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.2,0,6.9,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>f05a21251ac84c2da3b1e837f5d9b959</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text43</Name>
              <Page isRef="3" />
              <Parent isRef="22" />
              <Text>{MoneyCn(Sum(GroupHeaderBand1,收费明细.Mz_Money))}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text43>
            <Text44 Ref="24" type="Text" isKey="true">
              <Border>None;Black;1;Solid;False;4;Transparent</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9.1,0,0.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>8476af18b9274878b986fe14d535accc</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text44</Name>
              <Page isRef="3" />
              <Parent isRef="22" />
              <Text>￥</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text44>
            <Text49 Ref="25" type="Text" isKey="true">
              <Border>None;Black;1;Solid;False;4;Transparent</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9.7,0,6.5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>91b8bd3567144a42809a29ecb3d89ca3</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text49</Name>
              <Page isRef="3" />
              <Parent isRef="22" />
              <Text>{Sum(GroupHeaderBand1,收费明细.Mz_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text49>
            <Text41 Ref="26" type="Text" isKey="true">
              <Border>None;Black;1;Solid;False;4;Transparent</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>90398efe882947c9a6b62607918a7968</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text41</Name>
              <Page isRef="3" />
              <Parent isRef="22" />
              <Text>合计（大写）：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text41>
            <Text50 Ref="27" type="Text" isKey="true">
              <Border>None;Black;1;Solid;False;4;Transparent</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.2,0.5,3,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,7.5,Regular,Point,False,134</Font>
              <Guid>1c38663c3a144720b965eeb6b383650b</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text50</Name>
              <Page isRef="3" />
              <Parent isRef="22" />
              <Text>{收款人}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text50>
            <Text52 Ref="28" type="Text" isKey="true">
              <Border>None;Black;1;Solid;False;4;Transparent</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.2,0.5,4.8,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,7.5,Regular,Point,False,134</Font>
              <Guid>0c729726e2fa44cc85c3f9d91abc03af</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text52</Name>
              <Page isRef="3" />
              <Parent isRef="22" />
              <Text>{打印时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text52>
            <Text2 Ref="29" type="Text" isKey="true">
              <Border>None;Black;1;Solid;False;4;Transparent</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.5,1.2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,7.5,Regular,Point,False,134</Font>
              <Guid>4379e53e0d6a4ba8b608a3848bbfa39b</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text2</Name>
              <Page isRef="3" />
              <Parent isRef="22" />
              <Text>收款人：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text2>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>GroupFooterBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </GroupFooterBand1>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>20c1288e9da6491e858185f416cfb06d</Guid>
      <Margins>2,0.8,0.78740157480315,0</Margins>
      <Name>Page1</Name>
      <PageHeight>10.13</PageHeight>
      <PageWidth>19</PageWidth>
      <Report isRef="0" />
      <Watermark Ref="30" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page1>
  </Pages>
  <PrinterSettings Ref="31" type="Stimulsoft.Report.Print.StiPrinterSettings" isKey="true" />
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>辽宁省门诊收费票据</ReportAlias>
  <ReportChanged>1/19/2015 7:08:33 PM</ReportChanged>
  <ReportCreated>12/21/2012 9:16:47 AM</ReportCreated>
  <ReportFile>.\Rpt\辽宁省门诊收费票据老版.mrt</ReportFile>
  <ReportGuid>7d3375d1d5d6499995337bf653149396</ReportGuid>
  <ReportName>辽宁省门诊收费票据</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2011.2.1026</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class Report : Stimulsoft.Report.StiReport
    {
        public Report()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
        #endregion StiReport Designer generated code - do not modify
		
		public	string NumberCn(decimal ANumber) 

		{

			const string cPointCn = "点十百千万十百千亿十百千";

			const string cNumberCn = "零一二三四五六七八九";

			string S = ANumber.ToString();

			if (S == "0") return "" + cPointCn[0];

			if (!S.Contains(".")) S += ".";

			int P = S.IndexOf(".");

			string Result = "";

    

			for (int i = 0; i &lt; S.Length; i++)

			{

				if (P == i)

				{

					Result = Result.Replace("零十零", "零");

					Result = Result.Replace("零百零", "零");

					Result = Result.Replace("零千零", "零");

					Result = Result.Replace("零十", "零");

					Result = Result.Replace("零百", "零");

					Result = Result.Replace("零千", "零");

					Result = Result.Replace("零万", "万");

					Result = Result.Replace("零亿", "亿");

					Result = Result.Replace("亿万", "亿");

					Result = Result.Replace("零点", "点");

				}

				else

				{

					if (P &gt; i)

						Result += "" + cNumberCn[S[i] - '0'] + cPointCn[P - i - 1];

					else Result += "" + cNumberCn[S[i] - '0'];

				}

			}

			if (Result.Substring(Result.Length - 1, 1) == "" + cPointCn[0])

				Result = Result.Remove(Result.Length - 1); // 一点-&gt; 一

    

			if (Result[0] == cPointCn[0])

				Result = cNumberCn[0] + Result; // 点三-&gt; 零点三

 

			if ((Result.Length &gt; 1) &amp;&amp; (Result[1] == cPointCn[1]) &amp;&amp; 

				(Result[0] == cNumberCn[1]))

				Result = Result.Remove(0, 1); // 一十三-&gt; 十三

			return Result;

		}

 

		public	string MoneyCn(decimal ANumber)

		{

			string V_Fs="";
			if (ANumber &lt; 0)
			{
				ANumber = -ANumber;
				V_Fs="负";
			}
			else
			{
				V_Fs = "";
			}

			
			if (ANumber == 0) return "零";

			string Result = NumberCn(Math.Truncate(ANumber * 100) / 100);

			Result = Result.Replace("一", "壹");

			Result = Result.Replace("二", "贰");

			Result = Result.Replace("三", "叁");

			Result = Result.Replace("四", "肆");

			Result = Result.Replace("五", "伍");

			Result = Result.Replace("六", "陆");

			Result = Result.Replace("七", "柒");

			Result = Result.Replace("八", "捌");

			Result = Result.Replace("九", "玖");

			Result = Result.Replace("九", "玖");

			Result = Result.Replace("十", "拾");

			Result = Result.Replace("百", "佰");

			Result = Result.Replace("千", "仟");

			if (Result.Contains("点"))

			{

				int P = Result.IndexOf("点");
				if (P + 3 &gt; Result.Length)
				{
					Result = Result+ "分";
				}
				else

				{
					Result = Result.Insert(P + 3, "分");
				}
				//	Result = Result.Insert(P + 3, "分");

				Result = Result.Insert(P + 2, "角");

				Result = Result.Replace("点", "圆");

				Result = Result.Replace("角分", "角");

				Result = Result.Replace("零分", "");

				Result = Result.Replace("零角", "");

				Result = Result.Replace("分角", "");

				if (Result.Substring(0, 2) == "零圆")

					Result = Result.Replace("零圆", "");

			} else Result += "圆整";

			Result =  V_Fs+Result;

			return Result;

		}
		
		
		
		
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>