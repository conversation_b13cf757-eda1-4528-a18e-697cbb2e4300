﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="0" />
    <DataSources isList="true" count="2">
      <检验主记录 Ref="2" type="DataTableSource" isKey="true">
        <Alias>检验主记录</Alias>
        <Columns isList="true" count="45">
          <value>IsBold,System.Boolean</value>
          <value>Item_No,System.Int32</value>
          <value>Ys_Name,System.String</value>
          <value>Ks_Name,System.String</value>
          <value>Xm_Name,System.String</value>
          <value>Ry_Name,System.String</value>
          <value>Ry_Sex,System.String</value>
          <value>Ry_Age,System.Decimal</value>
          <value>Apply_Ks_Code,System.String</value>
          <value>Specimen,System.String</value>
          <value>Apply_Ys_Code,System.String</value>
          <value>Relevant_Clinic_Diag,System.String</value>
          <value>BarCode,System.String</value>
          <value>Requested_Date_Time,System.DateTime</value>
          <value>Spcm_Sample_Date_Time,System.DateTime</value>
          <value>Results_Rpt_Date_Time,System.DateTime</value>
          <value>Transcriptionist,System.String</value>
          <value>Verified_By,System.String</value>
          <value>Xm_Code,System.String</value>
          <value>Test_Code,System.String</value>
          <value>Item_Loinc,System.String</value>
          <value>Item_Name,System.String</value>
          <value>Result,System.String</value>
          <value>Units,System.String</value>
          <value>Result_Interpre1,System.String</value>
          <value>Norm_UpLow_Limit,System.String</value>
          <value>Lb,System.String</value>
          <value>Name_Phonetic,System.String</value>
          <value>Test_Cause,System.String</value>
          <value>Notes_For_Spcm,System.String</value>
          <value>Spcm_Received_Date_Time,System.DateTime</value>
          <value>Excute_Ks_Code,System.String</value>
          <value>Result_Status,System.String</value>
          <value>Costs,System.Decimal</value>
          <value>Charges,System.Decimal</value>
          <value>Print_Indicator,System.Boolean</value>
          <value>Test_Code1,System.String</value>
          <value>Xm_Code1,System.String</value>
          <value>Item_Code,System.String</value>
          <value>Norm_Lower_Limit,System.String</value>
          <value>Norm_Upper_Limit,System.String</value>
          <value>Result_Interpre,System.String</value>
          <value>Create_Time,System.DateTime</value>
          <value>Jsr_Code,System.String</value>
          <value>Norm_Limit,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>检验主记录</Name>
        <NameInSource>检验主记录</NameInSource>
      </检验主记录>
      <检验项目结果 Ref="3" type="DataTableSource" isKey="true">
        <Alias>检验项目结果</Alias>
        <Columns isList="true" count="16">
          <value>Test_Code,System.String</value>
          <value>Xm_Code,System.String</value>
          <value>Item_No,System.Int32</value>
          <value>Item_Code,System.String</value>
          <value>Item_Loinc,System.String</value>
          <value>Item_Name,System.String</value>
          <value>Result,System.String</value>
          <value>Units,System.String</value>
          <value>Norm_Lower_Limit,System.String</value>
          <value>Norm_Upper_Limit,System.String</value>
          <value>Result_Interpre,System.String</value>
          <value>Create_Time,System.DateTime</value>
          <value>Jsr_Code,System.String</value>
          <value>Result_Interpre1,System.String</value>
          <value>Norm_Limit,System.String</value>
          <value>Norm_UpLow_Limit,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>检验项目结果</Name>
        <NameInSource>检验项目结果</NameInSource>
      </检验项目结果>
    </DataSources>
    <Relations isList="true" count="0" />
    <Report isRef="0" />
    <Variables isList="true" count="9">
      <value>,标题,标题,System.String,,False,False</value>
      <value>,检验日期,检验日期,System.String,,False,False</value>
      <value>,报告日期,报告日期,System.String,,False,False</value>
      <value>,检验者,检验者,System.String,,False,False</value>
      <value>,审核者,审核者,System.String,,False,False</value>
      <value>,检验者签名,检验者签名,System.Drawing.Image,,False,False</value>
      <value>,审核者签名,审核者签名,System.Drawing.Image,,False,False</value>
      <value>,检验者签名字,检验者签名字,System.String,,False,False</value>
      <value>,审核者签名字,审核者签名字,System.String,,False,False</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="4" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="4">
        <ReportTitleBand1 Ref="5" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,19,0</ClientRectangle>
          <Components isList="true" count="0" />
          <Conditions isList="true" count="0" />
          <Name>ReportTitleBand1</Name>
          <Page isRef="4" />
          <Parent isRef="4" />
        </ReportTitleBand1>
        <GroupHeaderBand1 Ref="6" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,1.2,19,4</ClientRectangle>
          <Components isList="true" count="25">
            <Text7 Ref="7" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,2.8,1.4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Bold,Point,False,0</Font>
              <Guid>e6bd6d88b3e5466fa2e3e86bedb4b725</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text7</Name>
              <Page isRef="4" />
              <Parent isRef="6" />
              <Text>姓  名：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text7>
            <Text8 Ref="8" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.4,2.8,2.4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,0</Font>
              <Guid>f6a36ef75bad4a7392978f9fcda6af4b</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text8</Name>
              <Page isRef="4" />
              <Parent isRef="6" />
              <Text>{检验主记录.Ry_Name}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text8>
            <Text9 Ref="9" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.2,1.8,1.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Bold,Point,False,0</Font>
              <Guid>4266bb9746964037b7ad361566fbc2d1</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text9</Name>
              <Page isRef="4" />
              <Parent isRef="6" />
              <Text>病 员 号：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text9>
            <Text10 Ref="10" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>5.8,1.8,3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,0</Font>
              <Guid>eeda7738559c4487beb2e7b44772d943</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text10</Name>
              <Page isRef="4" />
              <Parent isRef="6" />
              <Text>{检验主记录.Test_Code}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text10>
            <Text11 Ref="11" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>9.2,1.8,1.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Bold,Point,False,0</Font>
              <Guid>168b2a9bafc649c0881f53bd462cefd9</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text11</Name>
              <Page isRef="4" />
              <Parent isRef="6" />
              <Text>送检样本：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text11>
            <Text12 Ref="12" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>10.8,1.8,3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,0</Font>
              <Guid>27ca17e02b044ca9ab10a2e39b6fb3b8</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text12</Name>
              <Page isRef="4" />
              <Parent isRef="6" />
              <Text>{检验主记录.Specimen}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text12>
            <Text13 Ref="13" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>14.2,1.8,1.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Bold,Point,False,0</Font>
              <Guid>d90d7e01550d48aa95d98a7f9cfb3f43</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text13</Name>
              <Page isRef="4" />
              <Parent isRef="6" />
              <Text>样本编号：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text13>
            <Text14 Ref="14" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>15.8,1.8,3,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,0</Font>
              <Guid>1ac2c37ea14f4a77b926ac82d3ea212e</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text14</Name>
              <Page isRef="4" />
              <Parent isRef="6" />
              <Text>{检验主记录.BarCode}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text14>
            <Text15 Ref="15" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.8,1.4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Bold,Point,False,0</Font>
              <Guid>c2588869bf914f79a34e4382580846a6</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text15</Name>
              <Page isRef="4" />
              <Parent isRef="6" />
              <Text>性  别：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text15>
            <Text16 Ref="16" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.4,1.8,2.4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,0</Font>
              <Guid>df7446b7be0149ba871c7f6dda77d855</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text16</Name>
              <Page isRef="4" />
              <Parent isRef="6" />
              <Text>{检验主记录.Ry_Sex}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text16>
            <Text17 Ref="17" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.2,2.3,1.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Bold,Point,False,0</Font>
              <Guid>6065d6d903924b829d8a7e32592343ff</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text17</Name>
              <Page isRef="4" />
              <Parent isRef="6" />
              <Text>科    别：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text17>
            <Text18 Ref="18" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>5.8,2.3,3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,0</Font>
              <Guid>6e0394d1f8f146ef9e2e82b252f41bc0</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text18</Name>
              <Page isRef="4" />
              <Parent isRef="6" />
              <Text>{检验主记录.Ks_Name}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text18>
            <Text19 Ref="19" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>9.2,2.3,1.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Bold,Point,False,0</Font>
              <Guid>1e08ae0934204c25a1e1e7dd469a7404</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text19</Name>
              <Page isRef="4" />
              <Parent isRef="6" />
              <Text>送检医师：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text19>
            <Text20 Ref="20" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>10.8,2.3,3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,0</Font>
              <Guid>cc9f0e7768824641b09c06a3792b48e8</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text20</Name>
              <Page isRef="4" />
              <Parent isRef="6" />
              <Text>{检验主记录.Ys_Name}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text20>
            <Text21 Ref="21" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>14.2,2.3,1.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Bold,Point,False,0</Font>
              <Guid>2b51083d0d764cedaf75357d33f5b689</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text21</Name>
              <Page isRef="4" />
              <Parent isRef="6" />
              <Text>送检时间：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text21>
            <Text22 Ref="22" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>15.8,2.4,3,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,0</Font>
              <Guid>24b6ec130c514158a4b80245906a6021</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text22</Name>
              <Page isRef="4" />
              <Parent isRef="6" />
              <Text>{检验主记录.Requested_Date_Time}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text22>
            <Text23 Ref="23" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,2.3,1.4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Bold,Point,False,0</Font>
              <Guid>90b6daa25dab4737aa5581f9db4c5c78</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text23</Name>
              <Page isRef="4" />
              <Parent isRef="6" />
              <Text>年  龄：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text23>
            <Text61 Ref="24" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.4,2.3,2.4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,0</Font>
              <Guid>a6451f5434d646bea709aa922a10d534</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text61</Name>
              <Page isRef="4" />
              <Parent isRef="6" />
              <Text>{检验主记录.Ry_Age}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text61>
            <Text62 Ref="25" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.2,2.8,1.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Bold,Point,False,0</Font>
              <Guid>be8259332d1e434f94cc69cda0e7e73f</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text62</Name>
              <Page isRef="4" />
              <Parent isRef="6" />
              <Text>病 床 号：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text62>
            <Text63 Ref="26" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>5.8,2.8,3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,0</Font>
              <Guid>81128fc51abd4e2db8d831d2b9b063da</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text63</Name>
              <Page isRef="4" />
              <Parent isRef="6" />
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text63>
            <Text64 Ref="27" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>9.2,2.8,1.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Bold,Point,False,0</Font>
              <Guid>882cb238a80148cabbfa1270a2d36766</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text64</Name>
              <Page isRef="4" />
              <Parent isRef="6" />
              <Text>临床诊断：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text64>
            <Text65 Ref="28" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>10.8,2.8,8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,0</Font>
              <Guid>75240d391a6a45068da06f7e275cb232</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text65</Name>
              <Page isRef="4" />
              <Parent isRef="6" />
              <Text>{检验主记录.Relevant_Clinic_Diag}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text65>
            <Panel2 Ref="29" type="Stimulsoft.Report.Components.StiPanel" isKey="true">
              <Border>Top, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,3.3,19,0.7</ClientRectangle>
              <Components isList="true" count="5">
                <Text2 Ref="30" type="Text" isKey="true">
                  <Brush>Transparent</Brush>
                  <ClientRectangle>0.8,0.1,0.8,0.5</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>宋体,9,Bold,Point,False,0</Font>
                  <Guid>b6c8cdea927d4937a593b5bd19c8b36e</Guid>
                  <Margins>0,0,0,0</Margins>
                  <Name>Text2</Name>
                  <Page isRef="4" />
                  <Parent isRef="29" />
                  <Text>项目</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Text2>
                <Text3 Ref="31" type="Text" isKey="true">
                  <Brush>Transparent</Brush>
                  <ClientRectangle>5,0.1,1.6,0.5</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>宋体,9,Bold,Point,False,0</Font>
                  <Guid>04eb62b3a2b34067a8367872e5f645a9</Guid>
                  <Margins>0,0,0,0</Margins>
                  <Name>Text3</Name>
                  <Page isRef="4" />
                  <Parent isRef="29" />
                  <Text>中文名称</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Text3>
                <Text4 Ref="32" type="Text" isKey="true">
                  <Brush>Transparent</Brush>
                  <ClientRectangle>9.1,0.1,1.4,0.5</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>宋体,9,Bold,Point,False,0</Font>
                  <Guid>f8666fcee08d44caaea9a093995d9cdd</Guid>
                  <Margins>0,0,0,0</Margins>
                  <Name>Text4</Name>
                  <Page isRef="4" />
                  <Parent isRef="29" />
                  <Text>检验结果</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Text4>
                <Text5 Ref="33" type="Text" isKey="true">
                  <Brush>Transparent</Brush>
                  <ClientRectangle>12.8,0.1,0.8,0.5</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>宋体,9,Bold,Point,False,0</Font>
                  <Guid>a04fa201b2804b8195f0a955a08cb2da</Guid>
                  <Margins>0,0,0,0</Margins>
                  <Name>Text5</Name>
                  <Page isRef="4" />
                  <Parent isRef="29" />
                  <Text>单位</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Text5>
                <Text30 Ref="34" type="Text" isKey="true">
                  <Brush>Transparent</Brush>
                  <ClientRectangle>15.4,0.1,1.2,0.5</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>宋体,9,Bold,Point,False,0</Font>
                  <Guid>a3d0998569664de0a4c92c5e06181616</Guid>
                  <Margins>0,0,0,0</Margins>
                  <Name>Text30</Name>
                  <Page isRef="4" />
                  <Parent isRef="29" />
                  <Text>参考值</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                  <VertAlignment>Center</VertAlignment>
                </Text30>
              </Components>
              <Conditions isList="true" count="0" />
              <Guid>28467ead0c16405a95ee1484799a2279</Guid>
              <Name>Panel2</Name>
              <Page isRef="4" />
              <Parent isRef="6" />
            </Panel2>
            <Text24 Ref="35" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>3.8,0,11.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,15.75,Bold,Point,False,0</Font>
              <Guid>2dc6e0a466f544ba9b4cd3662d6df343</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text24</Name>
              <Page isRef="4" />
              <Parent isRef="6" />
              <Text>{标题}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text24>
            <Text25 Ref="36" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.2,5.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text25</Name>
              <Page isRef="4" />
              <Parent isRef="6" />
              <Text>【{检验主记录.Xm_Name}】</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text25>
          </Components>
          <Condition>{检验主记录.Xm_Code}</Condition>
          <Conditions isList="true" count="0" />
          <Name>GroupHeaderBand1</Name>
          <Page isRef="4" />
          <Parent isRef="4" />
        </GroupHeaderBand1>
        <DataBand1 Ref="37" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,6,19,0.7</ClientRectangle>
          <Components isList="true" count="6">
            <Text29 Ref="38" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4,0.1,4.6,0.5</ClientRectangle>
              <Conditions isList="true" count="1">
                <value>检验主记录.IsBold,EqualTo,true,,Boolean,Black,Transparent,Arial_x002C_10.5_x002C_Bold_x002C_Point_x002C_False_x002C_0,True,False,,,None</value>
              </Conditions>
              <Font>宋体,9,Regular,Point,False,0</Font>
              <Guid>8a6eeef213754ca38ab0b463ce3806e5</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text29</Name>
              <Page isRef="4" />
              <Parent isRef="37" />
              <Text>{检验主记录.Item_Name}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text29>
            <Text40 Ref="39" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>9.2,0.1,2.3,0.5</ClientRectangle>
              <Conditions isList="true" count="1">
                <value>检验主记录.IsBold,EqualTo,true,,Boolean,Black,Transparent,Arial_x002C_10.5_x002C_Bold_x002C_Point_x002C_False_x002C_0,True,False,,,None</value>
              </Conditions>
              <Font>宋体,9,Regular,Point,False,0</Font>
              <Guid>01f76d43b5504f04a306ba6d41ee73bb</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text40</Name>
              <Page isRef="4" />
              <Parent isRef="37" />
              <Text>{检验主记录.Result}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text40>
            <Text41 Ref="40" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>12.8,0.1,1.4,0.5</ClientRectangle>
              <Conditions isList="true" count="1">
                <value>检验主记录.IsBold,EqualTo,true,,Boolean,Black,Transparent,Arial_x002C_10.5_x002C_Bold_x002C_Point_x002C_False_x002C_0,True,False,,,None</value>
              </Conditions>
              <Font>宋体,9,Regular,Point,False,0</Font>
              <Guid>66cea0d8e8d542159769a709a7c0d970</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text41</Name>
              <Page isRef="4" />
              <Parent isRef="37" />
              <Text>{检验主记录.Units}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text41>
            <Text42 Ref="41" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>15.4,0.1,2.8,0.5</ClientRectangle>
              <Conditions isList="true" count="1">
                <value>检验主记录.IsBold,EqualTo,true,,Boolean,Black,Transparent,Arial_x002C_10.5_x002C_Bold_x002C_Point_x002C_False_x002C_0,True,False,,,None</value>
              </Conditions>
              <Font>宋体,9,Regular,Point,False,0</Font>
              <Guid>7d8cf2711fd1437e954880d4dde591b7</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text42</Name>
              <Page isRef="4" />
              <Parent isRef="37" />
              <Text>{检验主记录.Norm_UpLow_Limit}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="42" type="CustomFormat" isKey="true">
                <StringFormat>###0.####</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text42>
            <Text43 Ref="43" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0.8,0.1,2.6,0.5</ClientRectangle>
              <Conditions isList="true" count="1">
                <value>检验主记录.IsBold,EqualTo,true,,Boolean,Black,Transparent,Arial_x002C_10.5_x002C_Bold_x002C_Point_x002C_False_x002C_0,True,False,,,None</value>
              </Conditions>
              <Font>宋体,9,Regular,Point,False,0</Font>
              <Guid>2d5a9d85da4d438e83a4609ade9ae74c</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text43</Name>
              <Page isRef="4" />
              <Parent isRef="37" />
              <Text>{检验主记录.Item_Loinc}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text43>
            <Text6 Ref="44" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>11.6,0.1,0.7,0.5</ClientRectangle>
              <Conditions isList="true" count="1">
                <value>检验主记录.IsBold,EqualTo,true,,Boolean,Black,Transparent,Arial_x002C_10.5_x002C_Bold_x002C_Point_x002C_False_x002C_0,True,False,,,None</value>
              </Conditions>
              <Font>Arial,8</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text6</Name>
              <Page isRef="4" />
              <Parent isRef="37" />
              <Text>{检验主记录.Result_Interpre1}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text6>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>检验主记录</DataSourceName>
          <Filters isList="true" count="0" />
          <Guid>a297b2c1fa8740d7b23aa24a17f42459</Guid>
          <Name>DataBand1</Name>
          <Page isRef="4" />
          <Parent isRef="4" />
          <Sort isList="true" count="2">
            <value>ASC</value>
            <value>Item_No</value>
          </Sort>
        </DataBand1>
        <GroupFooterBand1 Ref="45" type="GroupFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,7.5,19,1.2</ClientRectangle>
          <Components isList="true" count="11">
            <Text35 Ref="46" type="Text" isKey="true">
              <Border>Top, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.6,0,3.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,0</Font>
              <Guid>8fda46502eaa48c38b141bd5d8eb98be</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text35</Name>
              <Page isRef="4" />
              <Parent isRef="45" />
              <Text>{检验主记录.Spcm_Sample_Date_Time}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text35>
            <Text36 Ref="47" type="Text" isKey="true">
              <Border>Top, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7,0,3.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,0</Font>
              <Guid>5798aafc293147dd968aa71debcff247</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text36</Name>
              <Page isRef="4" />
              <Parent isRef="45" />
              <Text>{检验主记录.Results_Rpt_Date_Time}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text36>
            <Text44 Ref="48" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.8,9.2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,0</Font>
              <Guid>82056b47cb32423f8c41db2638522abc</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text44</Name>
              <Page isRef="4" />
              <Parent isRef="45" />
              <Text>注：本结果仅对所测标本负责，如有疑问请在24小时内复查！</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text44>
            <Text45 Ref="49" type="Text" isKey="true">
              <Border>Top, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,1.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Bold,Point,False,0</Font>
              <Guid>e059c794392644bf989cb779016bd905</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text45</Name>
              <Page isRef="4" />
              <Parent isRef="45" />
              <Text>检验日期：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text45>
            <Text46 Ref="50" type="Text" isKey="true">
              <Border>Top, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>5.4,0,1.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Bold,Point,False,0</Font>
              <Guid>06221966c6ff4b63ad76efd1c2b67dd4</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text46</Name>
              <Page isRef="4" />
              <Parent isRef="45" />
              <Text>报告日期：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text46>
            <Text47 Ref="51" type="Text" isKey="true">
              <Border>Top, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>10.8,0,1.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Bold,Point,False,0</Font>
              <Guid>386ddafea8e14bfca986fb4fe993d160</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text47</Name>
              <Page isRef="4" />
              <Parent isRef="45" />
              <Text>检验者：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text47>
            <Text48 Ref="52" type="Text" isKey="true">
              <Border>Top, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>14.9,0,1.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Bold,Point,False,0</Font>
              <Guid>86a34d1771f443309d53d9c73d34f826</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text48</Name>
              <Page isRef="4" />
              <Parent isRef="45" />
              <Text>审核者：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text48>
            <Image1 Ref="53" type="Image" isKey="true">
              <AspectRatio>True</AspectRatio>
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>12,0,2.9,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <ImageData>{检验者签名}</ImageData>
              <Name>Image1</Name>
              <Page isRef="4" />
              <Parent isRef="45" />
              <Stretch>True</Stretch>
            </Image1>
            <Image2 Ref="54" type="Image" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>16.1,0,2.9,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <ImageData>{审核者签名}</ImageData>
              <Name>Image2</Name>
              <Page isRef="4" />
              <Parent isRef="45" />
              <Stretch>True</Stretch>
            </Image2>
            <Text1 Ref="55" type="Text" isKey="true">
              <Border>Top, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>12,0,2.9,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text1</Name>
              <Page isRef="4" />
              <Parent isRef="45" />
              <Text>{检验者签名字}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text1>
            <Text26 Ref="56" type="Text" isKey="true">
              <Border>Top, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>16.1,0,2.9,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>cf756e49b4fd4dd283d89a3ec225b000</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text26</Name>
              <Page isRef="4" />
              <Parent isRef="45" />
              <Text>{审核者签名字}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text26>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>GroupFooterBand1</Name>
          <Page isRef="4" />
          <Parent isRef="4" />
        </GroupFooterBand1>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>4088db33b5f1475381489e920d812f97</Guid>
      <Margins>1,1,1,1</Margins>
      <Name>Page1</Name>
      <PageHeight>29.7</PageHeight>
      <PageWidth>21</PageWidth>
      <Report isRef="0" />
      <Watermark Ref="57" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page1>
  </Pages>
  <PrinterSettings Ref="58" type="Stimulsoft.Report.Print.StiPrinterSettings" isKey="true" />
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>检验报告单（190103)</ReportAlias>
  <ReportChanged>1/8/2019 10:51:43 AM</ReportChanged>
  <ReportCreated>11/21/2018 4:33:42 PM</ReportCreated>
  <ReportFile>E:\ZTHis5\ZTHisLis\Rpt\检验报告单.mrt</ReportFile>
  <ReportGuid>4e6e4bc662804ec58fdc47d7f61cae11</ReportGuid>
  <ReportName>检验报告单（190103)</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2011.2.1026</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class Report : Stimulsoft.Report.StiReport
    {
        public Report()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
        #endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>