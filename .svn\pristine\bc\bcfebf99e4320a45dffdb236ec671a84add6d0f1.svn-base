﻿Imports System.Windows.Forms

Public Class Pr_Cfj
    Dim V_Lb As String
    Dim FormName As String
    Dim V_Invoke As BaseClass.C_Delegate.MyInvoke
    Dim Rrow As DataRow

    Public Sub New(ByVal m_FormName As String, ByVal m_Lb As String, ByVal m_Invoke As BaseClass.C_Delegate.MyInvoke, ByVal trow As DataRow)

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        FormName = m_FormName
        V_Lb = m_Lb
        V_Invoke = m_Invoke
        Rrow = trow
        ' 在 InitializeComponent() 调用之后添加任何初始化。

    End Sub

    Private Sub Form_Init()
        Me.WindowState = FormWindowState.Maximized
        With Me.Viewer1.Toolbar
            .Tools(2).Visible = False
            .Tools(4).Visible = False
            .Tools(5).Visible = False
            .Tools(6).Visible = False
            .Tools(7).Visible = False
            .Tools(22).Visible = False
            .Tools(23).Visible = False
        End With
        With Viewer1
            .Toolbar.Tools.Item(2).Caption = "打印"
            .Toolbar.Tools.Item(2).ToolTip = "打印报表"
            .Toolbar.Tools.Item(8).Caption = "单页"
            .Toolbar.Tools.Item(8).ToolTip = "单页显示"
            .Toolbar.Tools.Item(9).Caption = "多页"
            .Toolbar.Tools.Item(9).ToolTip = "多页显示"
            .Toolbar.Tools.Item(10).Caption = "连页"
            .Toolbar.Tools.Item(10).ToolTip = "连页显示"

            .Toolbar.Tools.Item(12).ToolTip = "缩小"
            .Toolbar.Tools.Item(13).ToolTip = "放大"

            .Toolbar.Tools.Item(16).ToolTip = "上一页"
            .Toolbar.Tools.Item(17).ToolTip = "下一页"

            .Toolbar.Tools.Item(20).ToolTip = "前进"
            .Toolbar.Tools.Item(20).Caption = "前进"
            .Toolbar.Tools.Item(21).ToolTip = "后退"
            .Toolbar.Tools.Item(21).Caption = "后退"

            Dim Btn As New DataDynamics.ActiveReports.Toolbar.Button
            Btn.Caption = "打印"
            Btn.ToolTip = "打印报表"
            'btn.ImageIndex = 1
            Btn.ButtonStyle = DataDynamics.ActiveReports.Toolbar.ButtonStyle.TextAndIcon
            Btn.Id = 5000
            .Toolbar.Tools.RemoveAt(2)
            .Toolbar.Tools.Insert(2, Btn)
            .Toolbar.Tools.Item(2).ImageIndex = 1
        End With
    End Sub

    Private Sub Pr_Cfj_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Call Form_Init()
    End Sub

    Private Sub Viewer1_ToolClick(ByVal sender As Object, ByVal e As DataDynamics.ActiveReports.Toolbar.ToolClickEventArgs) Handles Viewer1.ToolClick
        If e.Tool.Caption = "打印" Then
            Select Case V_Lb
                Case "门诊录入打印", "门诊收费打印"
                    HisVar.HisVar.Sqldal.ExecuteSql("Update Mz Set Mz_Print='1' Where Mz_Code='" & Rrow("Mz_Code") & "'")

                    If HisVar.HisVar.Sqldal.ExecuteSql("Update Mz Set Mz_FyQr='1',Mz_FyQr_Date=getdate(),Fy_JsrCode='" & HisVar.HisVar.JsrCode & "'  where Mz_Code In (Select Mz_Code From Mz_Xm) And Mz_Code not In (Select Mz_Code From Mz_Yp) And Mz_Code='" & Rrow("Mz_Code") & "'") = 0 Then
                     
                    Else
                        Rrow("Mz_FyQr") = True
                        Rrow("Mz_FyQr1") = "是"
                    End If

                    Rrow("Mz_Print") = True
                    Rrow("Mz_Print1") = "是"

                Case "出院打印"
                    If HisPara.PublicConfig.ZyHsz = "否" Then
                        HisVar.HisVar.Sqldal.ExecuteSql("Update Bl Set Ry_CyDate='" & Format(Rrow("Ry_CyDate"), "yyyy-MM-dd HH:mm:ss") & "',Ry_CyJsr='" & HisVar.HisVar.JsrCode & "',Ry_ZyTs='" & Rrow("Ry_ZyTs") & "' Where Yy_Code='" & HisVar.HisVar.WsyCode & "' and Bl_Code='" & Rrow("Bl_Code") & "'")

                    Else
                        HisVar.HisVar.Sqldal.ExecuteSql("Update Bl Set Ry_CyJsr='" & HisVar.HisVar.JsrCode & "' Where Yy_Code='" & HisVar.HisVar.WsyCode & "' and Bl_Code='" & Rrow("Bl_Code") & "'")

                    End If
                    '当是被召回的病人出院时，需要再插入一条出院信息
                    Dim My_Reader As SqlClient.SqlDataReader
                    My_Reader = HisVar.HisVar.Sqldal.ExecuteReader("Select  * From Bl_Zh Where Yy_Code='" & HisVar.HisVar.WsyCode & "' and Bl_Code='" & Rrow("Bl_Code") & "' And Cy_Lb='召回' order by Cy_Id desc")
                    My_Reader.Read()  '取最近一次召回
                    If My_Reader.HasRows = True Then
                        Dim Th As String

                        Th = My_Reader.Item("Bl_M_Th") + Rrow("Bl_M_Th")
                        My_Reader.Close()
                        If HisPara.PublicConfig.ZyHsz = "否" Then
                            HisVar.HisVar.Sqldal.ExecuteSql("Insert Into Bl_Zh(Yy_Code,Bl_Code,Cy_Date,Cy_Time,Jsr_Code,Cy_Lb,Bl_M_Th) Values('" & HisVar.HisVar.WsyCode & "','" & Rrow("Bl_Code") & "','" & Format(Rrow("Ry_CyDate"), "yyyy-MM-dd") & "','" & Format(TimeOfDay, "HH:mm:ss") & "','" & HisVar.HisVar.JsrCode & "','出院'," & Th * -1 & ")")

                        Else
                            HisVar.HisVar.Sqldal.ExecuteSql("Insert Into Bl_Zh(Yy_Code,Bl_Code,Cy_Date,Cy_Time,Jsr_Code,Cy_Lb,Bl_M_Th) Values('" & HisVar.HisVar.WsyCode & "','" & Rrow("Bl_Code") & "','" & Format(Rrow("Ry_CyDate"), "yyyy-MM-dd") & "','" & Format(Rrow("Ry_CyDate"), "HH:mm:ss") & "','" & HisVar.HisVar.JsrCode & "','出院'," & Th * -1 & ")")

                        End If
                    End If
                    HisVar.HisVar.Sqldal.ExecuteSql("Update Auto_Cf set Auto_Zt='停止',End_Date='" & Format(Now, "yyyy-MM-dd HH:mm") & "' where Bl_Code='" & Rrow("Bl_Code") & "' and Auto_Zt<>'停止'")
                Case "出院重打"
            End Select
            Viewer1.Document.Print(True, True)
            Me.Close()
        End If
    End Sub


End Class