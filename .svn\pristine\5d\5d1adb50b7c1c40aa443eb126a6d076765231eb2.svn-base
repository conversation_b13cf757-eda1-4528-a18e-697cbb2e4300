﻿Imports System.Data.SqlClient
Imports Common.Delegate
Imports Stimulsoft.Report
Imports HisControl
Imports ZTHisDrugStore

Public Class YkYf_Crk2

#Region "定义__变量"
    Public Cb_Cm As CurrencyManager                     '同步指针
    Public Cb_Row As DataRow                            '当前选择行
    Public V_Insert As Boolean                          '增加记录
    Dim My_DataSet As New DataSet
    Dim V_CrkOk As String
    Dim _bllYkRk2 As New BLL.BllYk_Rk2
    Dim _bllYfRk2 As New BLL.BllYf_Rk2
    Dim _bllYkKs2 As New BLL.BllYk_Ks2
    Private _transmitTxt As Common.Delegate.TransmitTxt = New TransmitTxt()
#End Region

#Region "传参"
    Dim Rform As BaseForm
    Dim Rinsert As Boolean
    Dim Rdate As Date
    Dim Rrow As DataRow
    Dim RZbtb As DataTable
    Dim Rtdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Dim Rlb As C1.Win.C1Input.C1Label
    Dim Form_Lb As String
#End Region

    Public Sub New(ByVal tform As BaseForm, ByVal tinsert As Boolean, ByVal tdate As Date, ByVal trow As DataRow, ByVal tZbtb As DataTable, ByVal ttdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid, ByVal tlb As C1.Win.C1Input.C1Label, ByVal tform_Lb As String)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        Rform = tform
        Rinsert = tinsert
        Rdate = tdate
        Rrow = trow
        RZbtb = tZbtb
        Rtdbgrid = ttdbgrid
        Rlb = tlb
        Form_Lb = tform_Lb
        ' 在 InitializeComponent() 调用之后添加任何初始化。

    End Sub

    Protected Overrides Sub OnKeyDown(ByVal e As System.Windows.Forms.KeyEventArgs)
        MyBase.OnKeyDown(e)
        If e.Control = True And e.KeyCode = Keys.S Then
            Call C1Command1_Click(C1Command2, Nothing)
        ElseIf e.KeyCode = Keys.F3 Then
            Call C1Command1_Click(C1Command4, Nothing)
        ElseIf e.Control = True And e.KeyCode = Keys.P Then
            Call C1Command1_Click(C1Command1, Nothing)
        End If
    End Sub

    Private Sub Yk_Rk2_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Call Form_Init()                '窗体初始化
        If Rinsert = True Then           '主表
            Call Zb_Clear()                 '清空数据
        Else
            Call Zb_Show()                  '显示数据
        End If
    End Sub

#Region "窗体__事件"

    Private Sub Form_Init()
        '按扭初始化
        Dim My_Combo2 As New BaseClass.C_Combo1(Me.C1Combo2)
        With My_Combo2
            .Init_TDBCombo()
            .AddItem("已付款")
            .AddItem("未付款")
            .SelectedIndex(0)
        End With
        C1Combo2.DropDownWidth = C1Combo2.Width


        C1Combo2.Visible = IIf(Form_Lb = "药库采购入库" Or Form_Lb = "药房采购入库", True, False)
        Label1.Visible = IIf(Form_Lb = "药库采购入库" Or Form_Lb = "药房采购入库", True, False)
        C1Command5.Visible = IIf(Form_Lb = "药库调拨药房", True, False)
        If Form_Lb = "药库采购入库" Or Form_Lb = "药房采购入库" Then
            If Rinsert = True Then
                V_CrkOk = "未完成"
            Else
                V_CrkOk = Rrow.Item("Rk_Ok")
            End If
            CgkhCombo.Captain = "供 应 商"
        ElseIf Form_Lb = "药库科室支领" Or Form_Lb = "药房科室支领" Then
            If Rinsert = True Then
                V_CrkOk = "未完成"
            Else
                V_CrkOk = Rrow.Item("Ck_Ok")
            End If
            CgkhCombo.Captain = "科室名称"
        ElseIf Form_Lb = "药库退供应商" Or Form_Lb = "药房退供应商" Then
            If Rinsert = True Then
                V_CrkOk = "未完成"
            Else
                V_CrkOk = Rrow.Item("Ck_Ok")
            End If
            CgkhCombo.Captain = "供 应 商"
        ElseIf Form_Lb = "药库调拨药房" Then
            If Rinsert = True Then
                V_CrkOk = "未完成"
            Else
                V_CrkOk = Rrow.Item("Ck_Ok1")
            End If
            CgkhCombo.Captain = "药房名称"
        ElseIf Form_Lb = "药库药品批发" Or Form_Lb = "药房药品批发" Then
            If Rinsert = True Then
                V_CrkOk = "未完成"
            Else
                V_CrkOk = Rrow.Item("Ck_Ok")
            End If
            CgkhCombo.Captain = "批发客户"
        ElseIf Form_Lb = "药房退回药库" Then
            If Rinsert = True Then
                V_CrkOk = "未完成"
            Else
                V_CrkOk = Rrow.Item("Tk_Ok1")
            End If
            CgkhCombo.Captain = "药房名称"
        End If
        With CgkhCombo
            If Form_Lb = "药库采购入库" Or Form_Lb = "药库退供应商" Or Form_Lb = "药房采购入库" Or Form_Lb = "药房退供应商" Then
                .DataView = DAL.DAL_Dict.GetCgkhDict.DefaultView
                .Init_Colum("Kh_Jc", "简称", 84, "左")
                .Init_Colum("Kh_Name", "名称", 290, "左")
                .Init_Colum("Kh_Code", "编码", 0, "左")
                .Init_Colum("Kh_Fzr", "", 0, "左")
                .Init_Colum("Kh_Tel", "", 0, "左")
                .Init_Colum("Kh_Address", "", 0, "左")
                .Init_Colum("Kh_Fax", "", 0, "左")
                .DisplayMember = "Kh_Name"
                .ValueMember = "Kh_Code"
                .RowFilterNotTextNull = "Kh_Jc"
            ElseIf Form_Lb = "药库科室支领" Or Form_Lb = "药房科室支领" Then
                .DataView = DAL.DAL_Dict.GetDepartmentDict.DefaultView
                .Init_Colum("Ks_Jc", "简称", 84, "左")
                .Init_Colum("Ks_Name", "名称", 290, "左")
                .Init_Colum("Ks_Code", "编码", 0, "左")
                .DisplayMember = "Ks_Name"
                .ValueMember = "Ks_Code"
                .RowFilterNotTextNull = "ks_Jc"
            ElseIf Form_Lb = "药库调拨药房" Or Form_Lb = "药房退回药库" Then
                .DataView = DAL.DAL_Dict.GetYfDict.DefaultView
                .Init_Colum("Yf_Jc", "简称", 84, "左")
                .Init_Colum("Yf_Name", "名称", 290, "左")
                .Init_Colum("Yf_Code", "编码", 0, "左")
                .DisplayMember = "Yf_Name"
                .ValueMember = "Yf_Code"
                .RowFilterNotTextNull = "Yf_Jc"
            ElseIf Form_Lb = "药库药品批发" Or Form_Lb = "药房药品批发" Then
                .DataView = DAL.DAL_Dict.GetPfsDict.DefaultView
                .Init_Colum("Kh_Jc", "简称", 84, "左")
                .Init_Colum("Kh_Name", "名称", 290, "左")
                .Init_Colum("Kh_Code", "编码", 0, "左")
                .Init_Colum("Kh_Fzr", "", 0, "左")
                .Init_Colum("Kh_Tel", "", 0, "左")
                .Init_Colum("Hzyl_Zs_Code", "", 0, "左")
                .DisplayMember = "Kh_Name"
                .ValueMember = "Kh_Code"
                .RowFilterNotTextNull = "Kh_Jc"
            End If
            .DroupDownWidth = 400
            .MaxDropDownItems = 15
            .SelectedIndex = -1
            .RowFilterTextNull = ""
        End With
        '初始化TDBGrid
        Dim My_Grid As New BaseClass.C_Grid(Me.C1TrueDBGrid1)
        With My_Grid
            .Init_Grid()
            .AllAddNew(True)
            .AllDelete(False)
            .AllUpdate(False)
            .AllSort(False)
            .P_Dock(BaseClass.C_Grid.Dock.Fill)
            .Init_Column("名称", "Yp_Name", 130, "左", "")
            .Init_Column("批准文号", "Mx_Gyzz", 70, "左", "")
            .Init_Column("规格", "Mx_Gg", 90, "左", "")
            .Init_Column("产地", "Mx_Cd", 150, "左", "")
            .Init_Column("产品批号", "Yp_Ph", 70, "左", "")
            .Init_Column("有效期", "Yp_Yxq", 100, "中", "yyyy-MM-dd")
            If Form_Lb = "药库采购入库" Or Form_Lb = "药房采购入库" Then
                .Init_Column("数量", "Rk_Sl", 60, "右", "0.####")
                .Init_Column("采购单价", "Rk_Dj", 60, "右", "0.00####")
                .Init_Column("采购金额", "Rk_Money", 80, "右", "0.00##")
                .Init_Column("销售单价", "Rk_Xsj", 60, "右", "0.00####")
                .Init_Column("销售金额", "Rk_XsMoney", 150, "右", "0.00##")
                .Init_Column("国家医保编码", "医疗目录编码", 100, "左", "")
            ElseIf Form_Lb = "药库科室支领" Then
                .Init_Column("数量", "Ck_Sl", 60, "右", "0.####")
                .Init_Column("采购单价", "Ck_Cgj", 60, "右", "0.00####")
                .Init_Column("金额", "Ck_Money", 80, "右", "0.00##")
                .Init_Column("国家医保编码", "医疗目录编码", 100, "左", "")
            ElseIf Form_Lb = "药库退供应商" Or Form_Lb = "药房退回药库" Or Form_Lb = "药房退供应商" Then
                .Init_Column("数量", "Tk_Sl", 60, "右", "0.####")
                .Init_Column("退库单价", "Tk_Dj", 60, "右", "0.00####")
                .Init_Column("金额", "Tk_Money", 10, "右", "0.00##")
            ElseIf Form_Lb = "药库调拨药房" Or Form_Lb = "药房科室支领" Then
                .Init_Column("数量", "Ck_Sl", 60, "右", "0.####")
                .Init_Column("销售单价", "Ck_Xsj", 60, "右", "0.00####")
                .Init_Column("金额", "Ck_Money", 10, "右", "0.00##")
            ElseIf Form_Lb = "药库药品批发" Or Form_Lb = "药房药品批发" Then
                .Init_Column("数量", "Ck_Sl", 60, "右", "0.####")
                .Init_Column("批发单价", "Ck_Dj", 60, "右", "0.00####")
                .Init_Column("金额", "Ck_Money", 10, "右", "0.00##")
            End If
        End With
        AddHandler Me._transmitTxt.SetText, AddressOf GridMove
    End Sub

    Private Sub Yk_Rk2_FormClosing(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosingEventArgs) Handles Me.FormClosing
        If e.CloseReason = CloseReason.UserClosing Then
            If Rinsert = False Then
                If V_CrkOk <> "已完成" Then
                    Call Zb_Save() '更新主表(主表已经保存过,取消操作)
                Else
                    If Form_Lb = "药库采购入库" Then
                        HisVar.HisVar.Sqldal.ExecuteSql("Update Yk_Rk1 Set Fk_Zt='" & C1Combo2.Text & "' where Rk_Code='" & Rrow.Item("Rk_Code") & "'")
                        Rrow.Item("Fk_Zt") = C1Combo2.Text
                    ElseIf Form_Lb = "药房采购入库" Then
                        HisVar.HisVar.Sqldal.ExecuteSql("Update Yf_Rk1 Set Fk_Zt='" & C1Combo2.Text & "' where Rk_Code='" & Rrow.Item("Rk_Code") & "'")
                        Rrow.Item("Fk_Zt") = C1Combo2.Text
                    End If
                End If

            End If
        End If
    End Sub

#End Region

#Region "清空__显示"

    Private Sub Zb_Clear()
        Label12.Text = F_MaxCode(HisVar.HisVar.WsyCode & Format(Rdate, "yyMMdd"))       '入库编码
        C1TextBox1.Text = ""
        C1Combo2.SelectedIndex = 0
        C1TextBox1.Enabled = True
        C1Command2.Enabled = True
        C1Command3.Enabled = True
        C1Command4.Enabled = True
        C1Command5.Enabled = True
        Call P_Data_Show()
        If Form_Lb = "药房退回药库" Then
            CgkhCombo.SelectedValue = HisVar.HisVar.YfCode
            CgkhCombo.Enabled = False
            C1TrueDBGrid1.Select()
        Else
            CgkhCombo.SelectedIndex = -1
            CgkhCombo.Enabled = True
            CgkhCombo.Focus()
            CgkhCombo.Select()
        End If
    End Sub

    Private Sub Zb_Show()   '显示记录
        If V_CrkOk = "已完成" Then
            CgkhCombo.Enabled = False
            C1TextBox1.ReadOnly = True
            C1TextBox1.BackColor = SystemColors.Info
            C1Command2.Enabled = False
            C1Command3.Enabled = False
            C1Command4.Enabled = False
            C1Command5.Enabled = False
        Else
            CgkhCombo.Enabled = True
            C1TextBox1.ReadOnly = False
            C1TextBox1.BackColor = Color.White
            C1Command2.Enabled = True
            C1Command3.Enabled = True
            C1Command4.Enabled = True
            C1Command5.Enabled = True
        End If

        With Rrow
            If Form_Lb = "药库采购入库" Or Form_Lb = "药房采购入库" Then
                Label12.Text = .Item("Rk_Code") & ""                                   '入库编码
                CgkhCombo.SelectedValue = .Item("Kh_Code") & ""                                   '客户编码
                C1TextBox1.Text = .Item("Rk_Memo")                                  '入库说明
                C1Combo2.Text = .Item("Fk_Zt") & ""
            ElseIf Form_Lb = "药库科室支领" Or Form_Lb = "药房科室支领" Then
                Label12.Text = .Item("Ck_Code") & ""                                   '批发编码
                CgkhCombo.SelectedValue = .Item("Ks_Code") & ""                                   '客户编码
                C1TextBox1.Text = .Item("Ck_Memo")                                  '入库说明
            ElseIf Form_Lb = "药库退供应商" Or Form_Lb = "药房退供应商" Then
                Label12.Text = .Item("Tk_Code") & ""                                   '供应编码
                CgkhCombo.SelectedValue = .Item("Kh_Code") & ""                                   '客户编码
                C1TextBox1.Text = .Item("Tk_Memo")
            ElseIf Form_Lb = "药库调拨药房" Then
                Label12.Text = .Item("Ck_Code") & ""                                   '批发编码
                CgkhCombo.SelectedValue = .Item("Yf_Code") & ""                                   '客户编码
                C1TextBox1.Text = .Item("Ck_Memo")
            ElseIf Form_Lb = "药库药品批发" Or Form_Lb = "药房药品批发" Then
                Label12.Text = .Item("Ck_Code") & ""                                   '批发编码
                CgkhCombo.SelectedValue = .Item("Kh_Code") & ""                                   '客户编码
                C1TextBox1.Text = .Item("Ck_Memo")
            ElseIf Form_Lb = "药房退回药库" Then
                CgkhCombo.SelectedValue = HisVar.HisVar.YfCode
                CgkhCombo.Enabled = False
                C1TrueDBGrid1.Select()

                Label12.Text = .Item("Tk_Code") & ""                                   '批发编码
                CgkhCombo.SelectedValue = .Item("Yf_Code") & ""                                   '客户编码
                C1TextBox1.Text = .Item("Tk_Memo")                                  '入库说明
            End If
        End With
        Call P_Data_Show()
        C1TrueDBGrid1.Select()
    End Sub

    Private Sub P_Data_Show()   '从表数据
        If Form_Lb = "药库采购入库" Then
            Dim dt As DataTable = _bllYkRk2.GetListByRkCode(Label12.Text).Tables(0)
            dt.TableName = "明细"
            My_DataSet.Tables.Add(dt.Copy())
            My_DataSet.Tables("明细").Columns("Rk_XsMoney").ReadOnly = False
            My_DataSet.Tables("明细").Columns("医疗目录编码").ReadOnly = False
        ElseIf Form_Lb = "药库科室支领" Then
            Dim dt As DataTable = _bllYkKs2.GetListByCode(Label12.Text).Tables(0)
            dt.TableName = "明细"
            My_DataSet.Tables.Add(dt.Copy())
        ElseIf Form_Lb = "药库退供应商" Then
            HisVar.HisVar.Sqldal.QueryDt(My_DataSet, "Select Tk_Code,Tk_Sl,Tk_Dj,Tk_Money,Tk_Memo,V_YpKc.Yy_Code,V_YpKc.Xx_Code,Yp_Name,Mx_Gg,Mx_CgDw,Mx_Cd,Mx_XsDw,Mx_Gyzz,Jx_Name,Yp_Ph,Yp_Yxq,Dl_Code,IsJb From Yk_TkPf2,V_YpKc Where Yk_TkPf2.Xx_Code=V_YpKc.Xx_Code and Tk_Code='" & Label12.Text & "'   Order By Tk_Id", "明细", True)
        ElseIf Form_Lb = "药库调拨药房" Then
            HisVar.HisVar.Sqldal.QueryDt(My_DataSet, "Select Ck_Code,Ck_Sl,Ck_Cgj,Ck_Pfj,Ck_Xsj,Ck_Money,Ck_Memo,V_YpKc.Yy_Code,V_YpKc.Xx_Code,Yp_Name,Mx_Gg,Yk_Yf2.Mx_CgDw,Mx_Cd,Yk_Yf2.Mx_XsDw,Mx_Gyzz,Jx_Name,Yp_Ph,Yp_Yxq,Dl_Code,IsJb,Yk_Yf2.Mx_Cfbl,Yk_Yf2.Ck_YfSl  From Yk_Yf2,V_YpKc Where YK_Yf2.Xx_Code=V_YpKc.Xx_Code and Ck_Code='" & Label12.Text & "'   Order By Ck_Id", "明细", True)
        ElseIf Form_Lb = "药库药品批发" Then
            HisVar.HisVar.Sqldal.QueryDt(My_DataSet, "Select Ck_Code,Ck_Sl,Ck_Dj,Ck_Money,Ck_Memo,V_Ypkc.Yy_Code,V_Ypkc.Xx_Code,Yp_Name,Mx_Gg,Mx_CgDw,Mx_Cd,Mx_XsDw,Mx_Gyzz,Jx_Name,Yp_Ph,Yp_Yxq,Dl_Code,IsJb  From Yk_Pf2,V_YpKc Where Yk_Pf2.Xx_Code=V_Ypkc.Xx_Code and Ck_Code='" & Label12.Text & "'   Order By Ck_Id", "明细", True)
        ElseIf Form_Lb = "药房退回药库" Then
            HisVar.HisVar.Sqldal.QueryDt(My_DataSet, "Select Tk_Code,Tk_Sl,Tk_Dj,Tk_Money,Tk_Memo,V_YpKc.Yy_Code,V_YpKc.Xx_Code,Yp_Name,Mx_Gg,Mx_CgDw,Mx_Cd,Mx_XsDw,Mx_Gyzz,Jx_Name,Yp_Ph,Yp_Yxq,Dl_Code,IsJb  From Yf_Tk2,V_YpKc Where Yf_Tk2.Xx_Code=V_YpKc.Xx_Code and Tk_Code='" & Label12.Text & "'   Order By Tk_Id", "明细", True)
        ElseIf Form_Lb = "药房科室支领" Then
            HisVar.HisVar.Sqldal.QueryDt(My_DataSet, "Select Ck_Code,Ck_Sl,Ck_Cgj,Ck_Pfj,Ck_Xsj,Ck_Money,Ck_Memo,V_YpKc.Yy_Code,V_YpKc.Xx_Code,Yp_Name,Mx_Gg,Mx_CgDw,Mx_Cd,Mx_XsDw,Mx_Gyzz,Jx_Name,Yp_Ph,Yp_Yxq,Dl_Code,IsJb  From Yf_Ks2,V_Ypkc where Yf_Ks2.Xx_Code=V_YpKc.Xx_Code and Ck_Code='" & Label12.Text & "'  Order By Ck_Id", "明细", True)
        ElseIf Form_Lb = "药房药品批发" Then
            HisVar.HisVar.Sqldal.QueryDt(My_DataSet, "Select Ck_Code,Ck_Sl,Ck_Dj,Ck_Money,Ck_Memo,V_YpKc.Yy_Code,V_YpKc.Xx_Code,Yp_Name,Mx_Gg,Mx_CgDw,Mx_Cd,Mx_XsDw,Mx_Gyzz,Jx_Name,Yp_Ph,Yp_Yxq,Dl_Code,IsJb  From Yf_Pf2,V_YpKc Where Yf_Pf2.Xx_Code=V_YpKc.Xx_Code and Ck_Code='" & Label12.Text & "'   Order By Ck_Id", "明细", True)
        ElseIf Form_Lb = "药房采购入库" Then
            Dim dt As DataTable = _bllYfRk2.GetListByRkCode(Label12.Text).Tables(0)
            dt.TableName = "明细"
            My_DataSet.Tables.Add(dt.Copy())
            My_DataSet.Tables("明细").Columns("Rk_XsMoney").ReadOnly = False
            My_DataSet.Tables("明细").Columns("医疗目录编码").ReadOnly = False
        ElseIf Form_Lb = "药房退供应商" Then
            HisVar.HisVar.Sqldal.QueryDt(My_DataSet, "Select Tk_Code,Tk_Sl,Tk_Dj,Tk_Money,Tk_Memo,V_YpKc.Yy_Code,Yf_TkPf2.Xx_Code,Yp_Name,Mx_Gg,Mx_CgDw,Mx_Cd,Mx_XsDw,Mx_Gyzz,Jx_Name,Yp_Ph,Yp_Yxq,Dl_Code,IsJb From Yf_TkPf2,V_YpKc Where Yf_TkPf2.Xx_Code=V_YpKc.Xx_Code and Tk_Code='" & Label12.Text & "'   Order By Tk_Id", "明细", True)
        End If
        Call F_Sum()
        '主表记录
        Cb_Cm = CType(BindingContext(My_DataSet, "明细"), CurrencyManager)
        C1TrueDBGrid1.SetDataBinding(My_DataSet, "明细", True)
    End Sub

    Public Overrides Sub F_Sum()
        If Form_Lb = "药库采购入库" Or Form_Lb = "药房采购入库" Then
            T_Label5.Text = Format(IIf(My_DataSet.Tables("明细").Compute("Sum(Rk_Money)", "") Is DBNull.Value, 0, My_DataSet.Tables("明细").Compute("Sum(Rk_Money)", "")), "###,###,###0.00") + "元"
        ElseIf Form_Lb = "药库科室支领" Or Form_Lb = "药库调拨药房" Or Form_Lb = "药库药品批发" Or Form_Lb = "药房科室支领" Or Form_Lb = "药房药品批发" Then
            T_Label5.Text = Format(IIf(My_DataSet.Tables("明细").Compute("Sum(Ck_Money)", "") Is DBNull.Value, 0, My_DataSet.Tables("明细").Compute("Sum(Ck_Money)", "")), "###,###,###0.00") + "元"
        ElseIf Form_Lb = "药库退供应商" Or Form_Lb = "药房退回药库" Or Form_Lb = "药房退供应商" Then
            T_Label5.Text = Format(IIf(My_DataSet.Tables("明细").Compute("Sum(Tk_Money)", "") Is DBNull.Value, 0, My_DataSet.Tables("明细").Compute("Sum(Tk_Money)", "")), "###,###,###0.00") + "元"
        End If
    End Sub

#End Region

#Region "主表__编辑"

    Private Sub Zb_Save()    '增加记录
        If CgkhCombo.SelectedValue = "" Then
            Beep()
            MsgBox("名称不能为空！", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
            CgkhCombo.Select()
            Exit Sub
        End If

        Dim My_NewRow As DataRow
        If Rinsert = True Then
            My_NewRow = RZbtb.NewRow
        Else
            My_NewRow = Rrow
        End If

        If Form_Lb = "药库采购入库" Or Form_Lb = "药房采购入库" Then
            With My_NewRow
                .Item("Yy_Code") = HisVar.HisVar.WsyCode
                .Item("Rk_Date") = Format(Rdate, "yyyy-MM-dd") & " " & Format(Now, "HH:mm:ss")    '入库日期
                If Rinsert = True Then
                    .Item("Rk_Code") = F_MaxCode(HisVar.HisVar.WsyCode & Format(Rdate, "yyMMdd"))        '入库编码
                Else
                    .Item("Rk_Code") = My_NewRow.Item("Rk_Code")
                End If
                Label12.Text = My_NewRow.Item("Rk_Code")
                .Item("Kh_Code") = CgkhCombo.SelectedValue                                       '客户编码
                .Item("Kh_Name") = Trim(CgkhCombo.Text & "")                                      '客户名称
                .Item("Rk_Money") = Format(IIf(My_DataSet.Tables("明细").Compute("Sum(Rk_Money)", "") Is DBNull.Value, 0, My_DataSet.Tables("明细").Compute("Sum(Rk_Money)", "")), "###,###,##0.00")
                .Item("Rk_Memo") = Trim(C1TextBox1.Text & "")
                .Item("Jsr_Code") = HisVar.HisVar.JsrCode
                .Item("Jsr_Name") = HisVar.HisVar.JsrName
                .Item("Rk_Ok") = "未完成"
                .Item("Fk_Zt") = C1Combo2.Text & ""
                If Form_Lb = "药房采购入库" Then
                    .Item("Yf_Code") = HisVar.HisVar.YfCode
                End If
            End With
        ElseIf Form_Lb = "药库科室支领" Or Form_Lb = "药房科室支领" Then
            With My_NewRow
                .Item("Yy_Code") = HisVar.HisVar.WsyCode
                .Item("Ck_Date") = Format(Rdate, "yyyy-MM-dd") & " " & Format(Now, "HH:mm:ss")    '入库日期
                If Rinsert = True Then
                    .Item("Ck_Code") = F_MaxCode(HisVar.HisVar.WsyCode & Format(Rdate, "yyMMdd"))        '批发编码
                Else
                    .Item("Ck_Code") = My_NewRow.Item("Ck_Code")
                End If
                Label12.Text = My_NewRow.Item("Ck_Code")
                .Item("Ks_Code") = CgkhCombo.SelectedValue                                       '客户编码
                .Item("Ks_Name") = Trim(CgkhCombo.Text & "")                                      '客户名称
                .Item("Ck_Money") = Format(IIf(My_DataSet.Tables("明细").Compute("Sum(Ck_Money)", "") Is DBNull.Value, 0, My_DataSet.Tables("明细").Compute("Sum(Ck_Money)", "")), "###,###,##0.00")
                .Item("Ck_Memo") = Trim(C1TextBox1.Text & "")
                .Item("Jsr_Code") = HisVar.HisVar.JsrCode
                .Item("Jsr_Name") = HisVar.HisVar.JsrName
                .Item("Ck_Ok") = "未完成"
                If Form_Lb = "药房科室支领" Then
                    .Item("Yf_Code") = HisVar.HisVar.YfCode
                End If
            End With

        ElseIf Form_Lb = "药库退供应商" Or Form_Lb = "药房退供应商" Then
            With My_NewRow
                .Item("Yy_Code") = HisVar.HisVar.WsyCode
                .Item("Tk_Date") = Format(Rdate, "yyyy-MM-dd") & " " & Format(Now, "HH:mm:ss")  '入库日期
                If Rinsert = True Then
                    .Item("Tk_Code") = F_MaxCode(HisVar.HisVar.WsyCode & Format(Rdate, "yyMMdd"))        '供应编码
                Else
                    .Item("Tk_Code") = My_NewRow.Item("Tk_Code")
                End If
                Label12.Text = My_NewRow.Item("Tk_Code")
                .Item("Kh_Code") = CgkhCombo.SelectedValue                                       '客户编码
                .Item("Kh_Name") = Trim(CgkhCombo.Text & "")                                      '客户名称
                .Item("Tk_Money") = Format(IIf(My_DataSet.Tables("明细").Compute("Sum(Tk_Money)", "") Is DBNull.Value, 0, My_DataSet.Tables("明细").Compute("Sum(Tk_Money)", "")), "###,###,##0.00")
                .Item("Tk_Memo") = Trim(C1TextBox1.Text & "")
                .Item("Jsr_Code") = HisVar.HisVar.JsrCode
                .Item("Jsr_Name") = HisVar.HisVar.JsrName
                .Item("Ck_Ok") = "未完成"
                If Form_Lb = "药房退供应商" Then
                    .Item("Yf_Code") = HisVar.HisVar.YfCode
                End If
            End With
        ElseIf Form_Lb = "药库调拨药房" Then
            With My_NewRow
                .Item("Yy_Code") = HisVar.HisVar.WsyCode
                .Item("Ck_Date") = Format(Rdate, "yyyy-MM-dd") & " " & Format(Now, "HH:mm:ss")  '入库日期
                If Rinsert = True Then
                    .Item("Ck_Code") = F_MaxCode(HisVar.HisVar.WsyCode & Format(Rdate, "yyMMdd"))        '批发编码
                Else
                    .Item("Ck_Code") = My_NewRow.Item("Ck_Code")
                End If
                Label12.Text = My_NewRow.Item("Ck_Code")
                .Item("Yf_Code") = CgkhCombo.SelectedValue                                       '客户编码
                .Item("Yf_Name") = Trim(CgkhCombo.Text & "")                                      '客户名称
                .Item("Ck_Money") = Format(IIf(My_DataSet.Tables("明细").Compute("Sum(Ck_Money)", "") Is DBNull.Value, 0, My_DataSet.Tables("明细").Compute("Sum(Ck_Money)", "")), "###,###,##0.00")
                .Item("Ck_Memo") = Trim(C1TextBox1.Text & "")
                .Item("Jsr_Code") = HisVar.HisVar.JsrCode
                .Item("Jsr_Name") = HisVar.HisVar.JsrName
                .Item("Ck_Ok") = 0
                .Item("Ck_Ok1") = "调拨进行"
                .Item("Ck_Qr") = 0
                .Item("Ck_Qr1") = "未接收"
            End With
        ElseIf Form_Lb = "药库药品批发" Or Form_Lb = "药房药品批发" Then
            With My_NewRow
                .Item("Yy_Code") = HisVar.HisVar.WsyCode
                .Item("Ck_Date") = Format(Rdate, "yyyy-MM-dd") & " " & Format(Now, "HH:mm:ss")  '入库日期
                If Rinsert = True Then
                    .Item("Ck_Code") = F_MaxCode(HisVar.HisVar.WsyCode & Format(Rdate, "yyMMdd"))        '批发编码
                Else
                    .Item("Ck_Code") = My_NewRow.Item("Ck_Code")
                End If
                Label12.Text = My_NewRow.Item("Ck_Code")
                .Item("Kh_Code") = CgkhCombo.SelectedValue                                       '客户编码
                .Item("Kh_Name") = Trim(CgkhCombo.Text & "")                                      '客户名称
                .Item("Ck_Money") = Format(IIf(My_DataSet.Tables("明细").Compute("Sum(Ck_Money)", "") Is DBNull.Value, 0, My_DataSet.Tables("明细").Compute("Sum(Ck_Money)", "")), "###,###,##0.00")
                .Item("Ck_Memo") = Trim(C1TextBox1.Text & "")
                .Item("Jsr_Code") = HisVar.HisVar.JsrCode
                .Item("Jsr_Name") = HisVar.HisVar.JsrName
                .Item("Ck_Ok") = "未完成"
                .Item("Hzyl_Zs_Code") = CgkhCombo.Columns("Hzyl_Zs_Code").Value
                .Item("IsDb") = "否"
                If Form_Lb = "药房药品批发" Then
                    .Item("Yf_Code") = HisVar.HisVar.YfCode
                End If
            End With
        ElseIf Form_Lb = "药房退回药库" Then
            With My_NewRow
                .Item("Yy_Code") = HisVar.HisVar.WsyCode
                .Item("Tk_Date") = Format(Rdate, "yyyy-MM-dd") & " " & Format(Now, "HH:mm:ss")     '入库日期
                If Rinsert = True Then
                    .Item("Tk_Code") = F_MaxCode(HisVar.HisVar.WsyCode & Format(Rdate, "yyMMdd"))        '批发编码
                Else
                    .Item("Tk_Code") = My_NewRow.Item("Tk_Code")
                End If
                Label12.Text = My_NewRow.Item("Tk_Code")
                .Item("Yf_Code") = HisVar.HisVar.YfCode                                      '客户编码
                .Item("Yf_Name") = Trim(CgkhCombo.Text & "")                                      '客户名称
                .Item("Tk_Money") = Format(IIf(My_DataSet.Tables("明细").Compute("Sum(Tk_Money)", "") Is DBNull.Value, 0, My_DataSet.Tables("明细").Compute("Sum(Tk_Money)", "")), "###,###,##0.00")
                .Item("Tk_Memo") = Trim(C1TextBox1.Text & "")
                .Item("Jsr_Code") = HisVar.HisVar.JsrCode
                .Item("Jsr_Name") = HisVar.HisVar.JsrName
                .Item("Tk_Ok") = 0
                .Item("Tk_Ok1") = "退库进行"
                .Item("Tk_Qr") = 0
                .Item("Tk_Qr1") = "未接收"
            End With

        End If

        '更新主表
        Call Zb_Para_Int(My_NewRow)
        Call Rform.F_Sum()
        '显示增加后的状态
        Rtdbgrid.MoveLast()
        Rlb.Text = "∑=" + Rtdbgrid.RowCount.ToString
    End Sub

    Private Sub Zb_Para_Int(ByVal Row As DataRow)
        Dim Insert_String As String = ""
        Dim Update_String As String = ""
        Dim para() As SqlParameter = Nothing
        Dim ilist As List(Of SqlParameter) = New List(Of SqlParameter)()

        If Form_Lb = "药库采购入库" Then
            ilist.Add(New SqlParameter("@Yy_Code", SqlDbType.Char))
            ilist.Add(New SqlParameter("@Rk_Date", SqlDbType.SmallDateTime))
            ilist.Add(New SqlParameter("@Kh_Code", SqlDbType.Char))
            ilist.Add(New SqlParameter("@Kh_Name", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Rk_Money", SqlDbType.Decimal))
            ilist.Add(New SqlParameter("@Rk_Memo", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Jsr_Code", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Fk_Zt", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Rk_Code", SqlDbType.Char))
            Insert_String = "Insert Into Yk_Rk1(Yy_Code,Rk_Date,Kh_Code,Kh_Name,Rk_Money,Rk_Memo,Jsr_Code,Fk_Zt,Rk_Code)Values(@Yy_Code,@Rk_Date,@Kh_Code,@Kh_Name,@Rk_Money,@Rk_Memo,@Jsr_Code,@Fk_Zt,@Rk_Code)"
            Update_String = "Update Yk_Rk1 Set Yy_Code=@Yy_Code,Rk_Date=@Rk_Date,Kh_Code=@Kh_Code,Kh_Name=@Kh_Name,Rk_Money=@Rk_Money,Rk_Memo=@Rk_Memo,Jsr_Code=@Jsr_Code,Fk_Zt=@Fk_Zt Where Rk_Code=@Rk_Code"
        ElseIf Form_Lb = "药库科室支领" Then
            ilist.Add(New SqlParameter("@Yy_Code", SqlDbType.Char))
            ilist.Add(New SqlParameter("@Ck_Date", SqlDbType.SmallDateTime))
            ilist.Add(New SqlParameter("@Ks_Code", SqlDbType.Char))
            ilist.Add(New SqlParameter("@Ck_Money", SqlDbType.Decimal))
            ilist.Add(New SqlParameter("@Ck_Memo", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Jsr_Code", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Ck_Code", SqlDbType.Char))
            Insert_String = "Insert Into Yk_Ks1(Yy_Code,Ck_Date,Ks_Code,Ck_Money,Ck_Memo,Jsr_Code,Ck_Code)Values(@Yy_Code,@Ck_Date,@Ks_Code,@Ck_Money,@Ck_Memo,@Jsr_Code,@Ck_Code)"
            Update_String = "Update Yk_Ks1 Set Yy_Code=@Yy_Code,Ck_Date=@Ck_Date,Ks_Code=@Ks_Code,Ck_Money=@Ck_Money,Ck_Memo=@Ck_Memo,Jsr_Code=@Jsr_Code Where Ck_Code=@Ck_Code"
        ElseIf Form_Lb = "药库退供应商" Then
            ilist.Add(New SqlParameter("@Yy_Code", SqlDbType.Char))
            ilist.Add(New SqlParameter("@Tk_Date", SqlDbType.SmallDateTime))
            ilist.Add(New SqlParameter("@Kh_Code", SqlDbType.Char))
            ilist.Add(New SqlParameter("@Kh_Name", SqlDbType.Char))
            ilist.Add(New SqlParameter("@Tk_Money", SqlDbType.Decimal))
            ilist.Add(New SqlParameter("@Tk_Memo", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Jsr_Code", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Tk_Code", SqlDbType.Char))
            Insert_String = "Insert Into Yk_TkPf1(Yy_Code,Tk_Date,Kh_Code,Kh_Name,Tk_Money,Tk_Memo,Jsr_Code,Tk_Code)Values(@Yy_Code,@Tk_Date,@Kh_Code,@Kh_Name,@Tk_Money,@Tk_Memo,@Jsr_Code,@Tk_Code)"
            Update_String = "Update Yk_TkPf1 Set Yy_Code=@Yy_Code,Tk_Date=@Tk_Date,Kh_Code=@Kh_Code,Kh_Name=@Kh_Name,Tk_Money=@Tk_Money,Tk_Memo=@Tk_Memo,Jsr_Code=@Jsr_Code Where Tk_Code=@Tk_Code"
        ElseIf Form_Lb = "药库调拨药房" Then
            ilist.Add(New SqlParameter("@Yy_Code", SqlDbType.Char))
            ilist.Add(New SqlParameter("@Ck_Date", SqlDbType.SmallDateTime))
            ilist.Add(New SqlParameter("@Yf_Code", SqlDbType.Char))
            ilist.Add(New SqlParameter("@Ck_Money", SqlDbType.Decimal))
            ilist.Add(New SqlParameter("@Ck_Memo", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Jsr_Code", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Ck_Code", SqlDbType.Char))
            Insert_String = "Insert Into Yk_Yf1(Yy_Code,Ck_Date,Yf_Code,Ck_Money,Ck_Memo,Jsr_Code,Ck_Code)Values(@Yy_Code,@Ck_Date,@Yf_Code,@Ck_Money,@Ck_Memo,@Jsr_Code,@Ck_Code)"
            Update_String = "Update Yk_Yf1 Set Yy_Code=@Yy_Code,Ck_Date=@Ck_Date,Yf_Code=@Yf_Code,Ck_Money=@Ck_Money,Ck_Memo=@Ck_Memo,Jsr_Code=@Jsr_Code Where Ck_Code=@Ck_Code"
        ElseIf Form_Lb = "药库药品批发" Then
            ilist.Add(New SqlParameter("@Yy_Code", SqlDbType.Char))
            ilist.Add(New SqlParameter("@Ck_Date", SqlDbType.SmallDateTime))
            ilist.Add(New SqlParameter("@Kh_Code", SqlDbType.Char))
            ilist.Add(New SqlParameter("@Kh_Name", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Ck_Money", SqlDbType.Decimal))
            ilist.Add(New SqlParameter("@Ck_Memo", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Jsr_Code", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Ck_Code", SqlDbType.Char))
            Insert_String = "Insert Into Yk_Pf1(Yy_Code,Ck_Date,Kh_Code,Kh_Name,Ck_Money,Ck_Memo,Jsr_Code,Ck_Code)Values(@Yy_Code,@Ck_Date,@Kh_Code,@Kh_Name,@Ck_Money,@Ck_Memo,@Jsr_Code,@Ck_Code)"
            Update_String = "Update Yk_Pf1 Set Yy_Code=@Yy_Code,Ck_Date=@Ck_Date,Kh_Code=@Kh_Code,Kh_Name=@Kh_Name,Ck_Money=@Ck_Money,Ck_Memo=@Ck_Memo,Jsr_Code=@Jsr_Code Where Ck_Code=@Ck_Code"
        ElseIf Form_Lb = "药房退回药库" Then
            ilist.Add(New SqlParameter("@Yy_Code", SqlDbType.Char))
            ilist.Add(New SqlParameter("@Tk_Date", SqlDbType.SmallDateTime))
            ilist.Add(New SqlParameter("@Yf_Code", SqlDbType.Char))
            ilist.Add(New SqlParameter("@Tk_Money", SqlDbType.Decimal))
            ilist.Add(New SqlParameter("@Tk_Memo", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Jsr_Code", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Tk_Code", SqlDbType.Char))
            Insert_String = "Insert Into Yf_Tk1(Yy_Code,Tk_Date,Yf_Code,Tk_Money,Tk_Memo,Jsr_Code,Tk_Code)Values(@Yy_Code,@Tk_Date,@Yf_Code,@Tk_Money,@Tk_Memo,@Jsr_Code,@Tk_Code)"
            Update_String = "Update Yf_Tk1 Set Yy_Code=@Yy_Code,Tk_Date=@Tk_Date, Yf_Code=@Yf_Code,Tk_Money=@Tk_Money,Tk_Memo=@Tk_Memo,Jsr_Code=@Jsr_Code Where Tk_Code=@Tk_Code"
        ElseIf Form_Lb = "药房科室支领" Then
            ilist.Add(New SqlParameter("@Yy_Code", SqlDbType.Char))
            ilist.Add(New SqlParameter("@Ck_Date", SqlDbType.SmallDateTime))
            ilist.Add(New SqlParameter("@Ks_Code", SqlDbType.Char))
            ilist.Add(New SqlParameter("@Ck_Money", SqlDbType.Decimal))
            ilist.Add(New SqlParameter("@Ck_Memo", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Jsr_Code", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Yf_Code", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Ck_Code", SqlDbType.Char))
            Insert_String = "Insert Into Yf_Ks1(Yy_Code,Ck_Date,Ks_Code,Ck_Money,Ck_Memo,Jsr_Code,Yf_Code,Ck_Code)Values(@Yy_Code,@Ck_Date,@Ks_Code,@Ck_Money,@Ck_Memo,@Jsr_Code,@Yf_Code,@Ck_Code)"
            Update_String = "Update Yf_Ks1 Set Yy_Code=@Yy_Code,Ck_Date=@Ck_Date,Ks_Code=@Ks_Code,Ck_Money=@Ck_Money,Ck_Memo=@Ck_Memo,Jsr_Code=@Jsr_Code,Yf_Code=@Yf_Code Where Ck_Code=@Ck_Code"
        ElseIf Form_Lb = "药房药品批发" Then
            ilist.Add(New SqlParameter("@Yy_Code", SqlDbType.Char))
            ilist.Add(New SqlParameter("@Ck_Date", SqlDbType.SmallDateTime))
            ilist.Add(New SqlParameter("@Kh_Code", SqlDbType.Char))
            ilist.Add(New SqlParameter("@Kh_Name", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Ck_Money", SqlDbType.Decimal))
            ilist.Add(New SqlParameter("@Ck_Memo", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Jsr_Code", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Yf_Code", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Ck_Code", SqlDbType.Char))
            Insert_String = "  Insert Into Yf_Pf1(Yy_Code,Ck_Date,Kh_Code,Kh_Name,Ck_Money,Ck_Memo,Jsr_Code,Yf_Code,Ck_Code)Values(@Yy_Code,@Ck_Date,@Kh_Code,@Kh_Name,@Ck_Money,@Ck_Memo,@Jsr_Code,@Yf_Code,@Ck_Code)"
            Update_String = "Update Yf_Pf1 Set Yy_Code=@Yy_Code,Ck_Date=@Ck_Date,Kh_Code=@Kh_Code,Kh_Name=@Kh_Name,Ck_Money=@Ck_Money,Ck_Memo=@Ck_Memo,Jsr_Code=@Jsr_Code Where Ck_Code=@Ck_Code"
        ElseIf Form_Lb = "药房采购入库" Then
            ilist.Add(New SqlParameter("@Yy_Code", SqlDbType.Char))
            ilist.Add(New SqlParameter("@Rk_Date", SqlDbType.SmallDateTime))
            ilist.Add(New SqlParameter("@Kh_Code", SqlDbType.Char))
            ilist.Add(New SqlParameter("@Kh_Name", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Rk_Money", SqlDbType.Decimal))
            ilist.Add(New SqlParameter("@Rk_Memo", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Jsr_Code", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Yf_Code", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Fk_Zt", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Rk_Code", SqlDbType.Char))
            Insert_String = "Insert Into Yf_Rk1(Yy_Code,Rk_Date,Kh_Code,Kh_Name,Rk_Money,Rk_Memo,Jsr_Code,Yf_Code,Fk_Zt,Rk_Code)Values(@Yy_Code,@Rk_Date,@Kh_Code,@Kh_Name,@Rk_Money,@Rk_Memo,@Jsr_Code,@Yf_Code,@Fk_Zt,@Rk_Code)"
            Update_String = "Update Yf_Rk1 Set Yy_Code=@Yy_Code,Rk_Date=@Rk_Date,Kh_Code=@Kh_Code,Kh_Name=@Kh_Name,Rk_Money=@Rk_Money,Rk_Memo=@Rk_Memo,Jsr_Code=@Jsr_Code,Yf_Code=@Yf_Code,Fk_Zt=@Fk_Zt Where Rk_Code=@Rk_Code"
        ElseIf Form_Lb = "药房退供应商" Then
            ilist.Add(New SqlParameter("@Yy_Code", SqlDbType.Char))
            ilist.Add(New SqlParameter("@Tk_Date", SqlDbType.SmallDateTime))
            ilist.Add(New SqlParameter("@Kh_Code", SqlDbType.Char))
            ilist.Add(New SqlParameter("@Kh_Name", SqlDbType.Char))
            ilist.Add(New SqlParameter("@Tk_Money", SqlDbType.Decimal))
            ilist.Add(New SqlParameter("@Tk_Memo", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Jsr_Code", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Yf_Code", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Tk_Code", SqlDbType.Char))
            Insert_String = "Insert Into Yf_TkPf1(Yy_Code,Tk_Date,Kh_Code,Kh_Name,Tk_Money,Tk_Memo,Jsr_Code,Yf_Code,Tk_Code)Values(@Yy_Code,@Tk_Date,@Kh_Code,@Kh_Name,@Tk_Money,@Tk_Memo,@Jsr_Code,@Yf_Code,@Tk_Code)"
            Update_String = "Update Yf_TkPf1 Set Yy_Code=@Yy_Code,Tk_Date=@Tk_Date,Kh_Code=@Kh_Code,Kh_Name=@Kh_Name,Tk_Money=@Tk_Money,Tk_Memo=@Tk_Memo,Jsr_Code=@Jsr_Code,Yf_Code=@Yf_Code Where Tk_Code=@Tk_Code"
        End If
        para = ilist.ToArray()
        If Rinsert = True Then
            Zb_Para_Excute(para, Row, Insert_String)
            RZbtb.Rows.Add(Row)
            Rrow = Row
            Rinsert = False
        Else
            Zb_Para_Excute(para, Row, Update_String)
        End If
        If Form_Lb = "药库采购入库" Or Form_Lb = "药房采购入库" Then
            Me.Name = "YkYf_Crk2" & Row("Rk_Code")
            Me.Text = Form_Lb & "明细" & "-" & Row("Kh_Name")
        ElseIf Form_Lb = "药库科室支领" Or Form_Lb = "药房科室支领" Then
            Me.Name = "YkYf_Crk2" & Row("Ck_Code")
            Me.Text = Form_Lb & "明细" & "-" & Row("Ks_Name")
        ElseIf Form_Lb = "药库退供应商" Or Form_Lb = "药房退供应商" Then
            Me.Name = "YkYf_Crk2" & Row("Tk_Code")
            Me.Text = Form_Lb & "明细" & "-" & Row("Kh_Name")
        ElseIf Form_Lb = "药库调拨药房" Then
            Me.Name = "YkYf_Crk2" & Row("Ck_Code")
            Me.Text = Form_Lb & "明细" & "-" & Row("Yf_Name")
        ElseIf Form_Lb = "药库药品批发" Or Form_Lb = "药房药品批发" Then
            Me.Name = "YkYf_Crk2" & Row("Ck_Code")
            Me.Text = Form_Lb & "明细" & "-" & Row("Kh_Name")
        ElseIf Form_Lb = "药房退回药库" Then
            Me.Name = "YkYf_Crk2" & Row("Tk_Code")
            Me.Text = Form_Lb & "明细" & "-" & Row("Yf_Name")
        End If
    End Sub

    Private Sub Zb_Para_Excute(ByVal V_Paras() As SqlClient.SqlParameter, ByVal V_Row As DataRow, ByVal V_Command_Str As String)
        For I = 0 To V_Paras.Length - 1
            V_Paras(I).Value = V_Row.Item(Mid(V_Paras(I).ParameterName, 2))
        Next
        Try
            HisVar.HisVar.Sqldal.ExecuteSql(V_Command_Str, V_Paras)
        Catch ex As Exception
            MsgBox(ex.Message.ToString, MsgBoxStyle.Information, "提示")
        End Try
        C1TrueDBGrid1.Select()
    End Sub

#End Region

#Region "控件__动作"

#Region "其它__控件"

    Private Sub C1Command1_Click(ByVal sender As System.Object, ByVal e As C1.Win.C1Command.ClickEventArgs) Handles C1Command1.Click, C1Command2.Click, C1Command3.Click, C1Command4.Click, C1Command5.Click, C1Command6.Click
        Select Case sender.text
            Case "单据打印"
                If C1TrueDBGrid1.RowCount = 0 Then Exit Sub
                If V_CrkOk <> "已完成" Then
                    MsgBox("完成之后才能进行单据打印！", MsgBoxStyle.Information, "提示")
                    Exit Sub
                End If
                Call C1Command1_Print()
            Case "保存"
                Call Zb_Save()                          '主表存盘
            Case "取消"
                Me.Close()
            Case "单据完成"
                If C1TrueDBGrid1.RowCount = 0 Then Exit Sub

                Dim tmp_Ds As New DataSet
                Dim tmp_str As String = ""

                If HisVar.HisVar.Sqldal.GetSingle("select Count(*) from Zd_YkPd where Pd_Wc='否'") > 0 And (Form_Lb = "药库采购入库" Or Form_Lb = "药库科室支领" Or Form_Lb = "药库退供应商" Or Form_Lb = "药库药品批发") Then
                    MsgBox("正在进行盘点，请等待盘点完成后在进行完成操作！", MsgBoxStyle.Information, "提示：")
                    Exit Sub
                End If

                If HisVar.HisVar.Sqldal.GetSingle("select Count(*) from Zd_YfPd where Pd_Wc='否'") > 0 And (Form_Lb = "药房采购入库" Or Form_Lb = "药房科室支领" Or Form_Lb = "药房退供应商" Or Form_Lb = "药房药品批发") Then
                    MsgBox("正在进行盘点，请等待盘点完成后在进行完成操作！", MsgBoxStyle.Information, "提示：")
                    Exit Sub
                End If

                If Form_Lb = "药库采购入库" Then
                    If HisVar.HisVar.Sqldal.GetSingle("select Rk_Ok from Yk_Rk1 where Rk_Code='" & Label12.Text & "'") = "已完成" Then
                        MsgBox("已经入库完成！请退出后更新查看！", MsgBoxStyle.Information, "提示：")
                        Exit Sub
                    End If
                ElseIf Form_Lb = "药库科室支领" Then
                    If HisVar.HisVar.Sqldal.GetSingle("select Ck_Ok from Yk_Ks1 where Ck_Code='" & Label12.Text & "'") = "已完成" Then
                        MsgBox("已经完成！请退出后更新查看！", MsgBoxStyle.Information, "提示：")
                        Exit Sub
                    End If
                    tmp_Ds = HisVar.HisVar.Sqldal.Query("select Yp_Name,Mx_Gg,Yp_Ph from V_YpKc,Yk_Ks2  where  Yk_Ks2.Xx_Code = V_YpKc.Xx_Code and yk_sl<ck_sl and Ck_Code='" & Label12.Text & "'")
                ElseIf Form_Lb = "药库退供应商" Then
                    If HisVar.HisVar.Sqldal.GetSingle("select Ck_Ok from Yk_TkPf1 where Tk_Code='" & Label12.Text & "'") = "已完成" Then
                        MsgBox("已经完成！请退出后更新查看！", MsgBoxStyle.Information, "提示：")
                        Exit Sub
                    End If
                    tmp_Ds = HisVar.HisVar.Sqldal.Query("select Yp_Name,Mx_Gg,Yp_Ph from V_YpKc,Yk_TkPf2  where Yk_TkPf2.Xx_Code = V_YpKc.Xx_Code and yk_sl<Tk_sl and Tk_Code='" & Label12.Text & "'")
                ElseIf Form_Lb = "药库药品批发" Then
                    If HisVar.HisVar.Sqldal.GetSingle("select Ck_Ok from Yk_Pf1 where Ck_Code='" & Label12.Text & "'") = "已完成" Then
                        MsgBox("已经完成！请退出后更新查看！", MsgBoxStyle.Information, "提示：")
                        Exit Sub
                    End If

                    '针对丢数验证数据库金额与缓存金额

                    If Format(HisVar.HisVar.Sqldal.GetSingle("select Sum(Ck_Money) from Yk_Pf2 where Ck_Code='" & Label12.Text & "'"), "########0.00") <> Format(IIf(My_DataSet.Tables("明细").Compute("Sum(Ck_Money)", "") Is DBNull.Value, 0, My_DataSet.Tables("明细").Compute("Sum(Ck_Money)", "")), "########0.00") Then
                        MsgBox("出库金额计算有误！前关闭当前窗口从新打开后检查明细！", MsgBoxStyle.Information, "提示：")
                        Exit Sub
                    End If



                    tmp_Ds = HisVar.HisVar.Sqldal.Query("select Yp_Name,Mx_Gg,Yp_Ph from V_YpKc,Yk_Pf2 where  Yk_Pf2.Xx_Code=V_YpKc.Xx_Code  and yk_sl<ck_sl and Ck_Code='" & Label12.Text & "'")
                ElseIf Form_Lb = "药房退回药库" Then
                    If HisVar.HisVar.Sqldal.GetSingle("select Tk_Ok from Yf_Tk1 where Tk_Code='" & Label12.Text & "'") = 1 Then
                        MsgBox("已经完成！请退出后更新查看！", MsgBoxStyle.Information, "提示：")
                        Exit Sub
                    End If
                    tmp_Ds = HisVar.HisVar.Sqldal.Query("SELECT Yp_Name,Mx_Gg,Yp_Ph  FROM Yf_Tk2, V_YpKc where Yf_Tk2.Xx_Code = V_YpKc.Xx_Code  and Tk_sl>yf_sl" & Mid(HisVar.HisVar.YfCode, 6) & "   and Tk_Code='" & Label12.Text & "'")
                ElseIf Form_Lb = "药房科室支领" Then
                    If HisVar.HisVar.Sqldal.GetSingle("select Ck_Ok from Yf_Ks1 where Ck_Code='" & Label12.Text & "'") = "已完成" Then
                        MsgBox("已经完成！请退出后更新查看！", MsgBoxStyle.Information, "提示：")
                        Exit Sub
                    End If
                    tmp_Ds = HisVar.HisVar.Sqldal.Query("SELECT Yp_Name,Mx_Gg,Yp_Ph  FROM Yf_Ks2, V_YpKc where Yf_Ks2.Xx_Code = V_YpKc.Xx_Code  and ck_sl>yf_sl" & Mid(HisVar.HisVar.YfCode, 6) & "  and Ck_Code='" & Label12.Text & "'")
                ElseIf Form_Lb = "药房药品批发" Then
                    If HisVar.HisVar.Sqldal.GetSingle("select Ck_Ok from Yf_Pf1 where Ck_Code='" & Label12.Text & "'") = "已完成" Then
                        MsgBox("已经完成！请退出后更新查看！", MsgBoxStyle.Information, "提示：")
                        Exit Sub
                    End If

                    tmp_Ds = HisVar.HisVar.Sqldal.Query("SELECT Yp_Name,Mx_Gg,Yp_Ph FROM Yf_Pf2, V_YpKc  where Yf_Pf2.Xx_Code = V_YpKc.Xx_Code and ck_sl>yf_sl" & Mid(HisVar.HisVar.YfCode, 6) & "  and Ck_Code='" & Label12.Text & "'")
                ElseIf Form_Lb = "药房采购入库" Then
                    If HisVar.HisVar.Sqldal.GetSingle("select Rk_Ok from Yf_Rk1 where Rk_Code='" & Label12.Text & "'") = "已完成" Then
                        MsgBox("已经完成！请退出后更新查看！", MsgBoxStyle.Information, "提示：")
                        Exit Sub
                    End If
                ElseIf Form_Lb = "药房退供应商" Then
                    If HisVar.HisVar.Sqldal.GetSingle("select Ck_Ok from Yf_TkPf1 where Tk_Code='" & Label12.Text & "'") = "已完成" Then
                        MsgBox("已经完成！请退出后更新查看！", MsgBoxStyle.Information, "提示：")
                        Exit Sub
                    End If
                    tmp_Ds = HisVar.HisVar.Sqldal.Query("SELECT Yp_Name,Mx_Gg,Yp_Ph FROM Yf_TkPf2, V_YpKc  where Yf_TkPf2.Xx_Code = V_YpKc.Xx_Code and tk_sl>yf_sl" & Mid(HisVar.HisVar.YfCode, 6) & "  and Tk_Code='" & Label12.Text & "'")
                End If

                If tmp_Ds.Tables.Count <> 0 Then
                    For Each tmprow In tmp_Ds.Tables(0).Rows
                        tmp_str = tmprow("Yp_Name") & tmprow("Mx_Gg") & ":" & tmprow("Yp_Ph") & vbCrLf
                    Next
                    If tmp_str.Length > 0 Then
                        MsgBox("下列药品实际库存不足" & vbCrLf & tmp_str, MsgBoxStyle.Information, "提示")
                        Exit Sub
                    End If
                End If

                Call Zb_Save()

                If MsgBox("是否确认完成！", MsgBoxStyle.Information + MsgBoxStyle.OkCancel, "提示:") = MsgBoxResult.Cancel Then Exit Sub
                Dim Kc_Row As DataRow
                Dim Kc_Arry As New ArrayList
                If Form_Lb = "药库采购入库" Then
                    For Each Kc_Row In My_DataSet.Tables("明细").Rows
                        Kc_Arry.Add("Update Zd_Ml_Yp4 Set Yp_Yxq='" & Kc_Row.Item("Yp_Yxq") & "',Yk_Sl=Yk_Sl+(" & Kc_Row.Item("Rk_Sl") & "),Yk_Xsj=" & Kc_Row.Item("Rk_Xsj") & ",Yk_Pfj=" & Kc_Row.Item("Rk_Pfj") & ",Yf_Lsj1=" & Kc_Row.Item("Rk_Xsj") / Kc_Row.Item("Mx_Cfbl") & ",Yk_Cgj=" & Kc_Row.Item("Rk_Dj") & " Where Xx_Code='" & Kc_Row.Item("Xx_Code") & "' ")
                        Kc_Arry.Add("Update Zd_Ml_Yp4 Set Yf_Lsj2=Yf_Lsj1,Yf_Lsj3=Yf_Lsj1,Yf_Lsj4=Yf_Lsj1,Yf_Lsj5=Yf_Lsj1,Yf_Lsj6=Yf_Lsj1,Yf_Lsj7=Yf_Lsj1,Yf_Lsj8=Yf_Lsj1,Yf_Lsj9=Yf_Lsj1 Where Xx_Code='" & Kc_Row.Item("Xx_Code") & "' ")
                    Next
                    Kc_Arry.Add("Update Yk_Rk1 set Rk_Ok='已完成' where Rk_Code='" & Label12.Text & "'")
                ElseIf Form_Lb = "药库科室支领" Then
                    For Each Kc_Row In My_DataSet.Tables("明细").Rows
                        Kc_Arry.Add("Update Zd_Ml_Yp4 Set Yk_Sl=Yk_Sl-(" & Kc_Row.Item("Ck_Sl") & ") Where Xx_Code='" & Kc_Row.Item("Xx_Code") & "' ")
                    Next
                    Kc_Arry.Add("Update Yk_Ks1 set Ck_Ok='已完成' where Ck_Code='" & Label12.Text & "'")
                ElseIf Form_Lb = "药库退供应商" Then
                    For Each Kc_Row In My_DataSet.Tables("明细").Rows
                        Kc_Arry.Add("Update Zd_Ml_Yp4 Set Yk_Sl=Yk_Sl-(" & Kc_Row.Item("Tk_Sl") & ") Where Xx_Code='" & Kc_Row.Item("Xx_Code") & "' ")
                    Next
                    Kc_Arry.Add("Update Yk_TkPf1 set Ck_Ok='已完成' where Tk_Code='" & Label12.Text & "'")
                ElseIf Form_Lb = "药库调拨药房" Then
                    Kc_Arry.Add("Update Yk_Yf1 Set Ck_Ok=1 Where Ck_Code='" & Label12.Text & "'")
                ElseIf Form_Lb = "药库药品批发" Then
                    For Each Kc_Row In My_DataSet.Tables("明细").Rows
                        Kc_Arry.Add("Update Zd_Ml_Yp4 Set Yk_Sl=Yk_Sl-(" & Kc_Row.Item("Ck_Sl") & ") Where Xx_Code='" & Kc_Row.Item("Xx_Code") & "' ")
                    Next
                    Kc_Arry.Add("Update Yk_Pf1 set Ck_Ok='已完成' where Ck_Code='" & Label12.Text & "'")
                ElseIf Form_Lb = "药房退回药库" Then
                    HisVar.HisVar.Sqldal.ExecuteSql("Update Yf_Tk1 Set Tk_Ok=1 Where Tk_Code='" & Label12.Text & "'")
                ElseIf Form_Lb = "药房科室支领" Then
                    For Each Kc_Row In My_DataSet.Tables("明细").Rows
                        Kc_Arry.Add("Update Zd_Ml_Yp4 Set Yf_Sl" & Mid(HisVar.HisVar.YfCode, 6) & "=Yf_Sl" & Mid(HisVar.HisVar.YfCode, 6) & "-(" & Kc_Row.Item("Ck_Sl") & ") Where Xx_Code='" & Kc_Row.Item("Xx_Code") & "' ")
                    Next
                    Kc_Arry.Add("Update Yf_Ks1 set Ck_Ok='已完成' where Ck_Code='" & Label12.Text & "'")
                ElseIf Form_Lb = "药房药品批发" Then
                    For Each Kc_Row In My_DataSet.Tables("明细").Rows
                        Kc_Arry.Add("Update Zd_Ml_Yp4 Set Yf_Sl" & Mid(HisVar.HisVar.YfCode, 6) & "=Yf_Sl" & Mid(HisVar.HisVar.YfCode, 6) & "-(" & Kc_Row.Item("Ck_Sl") & ") Where Xx_Code='" & Kc_Row.Item("Xx_Code") & "' ")
                    Next
                    Kc_Arry.Add("Update Yf_Pf1 set Ck_Ok='已完成' where Ck_Code='" & Label12.Text & "'")
                ElseIf Form_Lb = "药房采购入库" Then
                    For Each Kc_Row In My_DataSet.Tables("明细").Rows
                        Kc_Arry.Add("Update Zd_Ml_Yp4 Set Yp_Yxq='" & Kc_Row.Item("Yp_Yxq") & "',Yf_Sl" & Mid(HisVar.HisVar.YfCode, 6) & "=Yf_Sl" & Mid(HisVar.HisVar.YfCode, 6) & "+(" & Kc_Row.Item("Rk_Sl") & "),Yk_Xsj=" & Kc_Row.Item("Rk_Xsj") & " * " & Kc_Row.Item("Mx_Cfbl") & ",Yk_Pfj=" & Kc_Row.Item("Rk_Pfj") & " * " & Kc_Row.Item("Mx_Cfbl") & " ,Yf_Lsj1=" & Kc_Row.Item("Rk_Xsj") & " ,Yk_Cgj=" & Kc_Row.Item("Rk_Dj") & " * " & Kc_Row.Item("Mx_Cfbl") & "  Where Xx_Code='" & Kc_Row.Item("Xx_Code") & "' And Yy_Code='" & HisVar.HisVar.WsyCode & "'")
                        Kc_Arry.Add("Update Zd_Ml_Yp4 Set Yf_Lsj2=Yf_Lsj1,Yf_Lsj3=Yf_Lsj1,Yf_Lsj4=Yf_Lsj1,Yf_Lsj5=Yf_Lsj1,Yf_Lsj6=Yf_Lsj1,Yf_Lsj7=Yf_Lsj1,Yf_Lsj8=Yf_Lsj1,Yf_Lsj9=Yf_Lsj1 Where Xx_Code='" & Kc_Row.Item("Xx_Code") & "' ")
                    Next
                    Kc_Arry.Add("Update Yf_Rk1 set Rk_Ok='已完成' where Rk_Code='" & Label12.Text & "'")
                ElseIf Form_Lb = "药房退供应商" Then
                    For Each Kc_Row In My_DataSet.Tables("明细").Rows
                        Kc_Arry.Add("Update Zd_Ml_Yp4 Set Yf_Sl" & Mid(HisVar.HisVar.YfCode, 6) & "=Yf_Sl" & Mid(HisVar.HisVar.YfCode, 6) & "-(" & Kc_Row.Item("Tk_Sl") & ") Where Xx_Code='" & Kc_Row.Item("Xx_Code") & "' ")
                    Next

                    Kc_Arry.Add("Update Yf_TkPf1 set Ck_Ok='已完成' where Tk_Code='" & Label12.Text & "'")

                End If
                Try
                    HisVar.HisVar.Sqldal.ExecuteSqlTran(Kc_Arry)
                    If Form_Lb = "药库采购入库" Or Form_Lb = "药房采购入库" Then
                        Rrow.Item("Rk_Ok") = "已完成"
                        V_CrkOk = "已完成"
                    ElseIf Form_Lb = "药库科室支领" Or Form_Lb = "药库退供应商" Or Form_Lb = "药库药品批发" Or Form_Lb = "药房科室支领" Or Form_Lb = "药房药品批发" Or Form_Lb = "药房退供应商" Then
                        Rrow.Item("Ck_Ok") = "已完成"
                        V_CrkOk = "已完成"
                    ElseIf Form_Lb = "药库调拨药房" Then
                        Rrow.Item("Ck_Ok") = 1
                        Rrow.Item("Ck_Ok1") = "已完成"
                        V_CrkOk = "已完成"
                    ElseIf Form_Lb = "药房退回药库" Then
                        Rrow.Item("Tk_Ok") = 1
                        Rrow.Item("Tk_Ok1") = "已完成"
                        V_CrkOk = "已完成"
                    End If
                    CgkhCombo.Enabled = False
                    C1TextBox1.ReadOnly = True
                    C1TextBox1.BackColor = SystemColors.Info
                    C1Command2.Enabled = False
                    C1Command3.Enabled = False
                    C1Command4.Enabled = False
                    C1Command5.Enabled = False
                Catch ex As Exception
                    MsgBox(ex.Message.ToString, MsgBoxStyle.Information, "提示")
                    Exit Sub
                End Try
                If MsgBox("操作已完成，是否进行打印？", MsgBoxStyle.Information + MsgBoxStyle.OkCancel, "提示：") = MsgBoxResult.Cancel Then Exit Select
                Call C1Command1_Print()

            Case "全部调拨"
                If CgkhCombo.SelectedValue = "" Then
                    Beep()
                    MsgBox("名称不能为空！", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    CgkhCombo.Select()
                    Exit Sub
                End If

                If MsgBox("是否要全部调拨？", vbQuestion + vbYesNo + vbDefaultButton1, "提示:") = vbNo Then Exit Sub
                Call Zb_Save()
                Dim Frm As New Yk_Yf4
                Frm.ShowDialog()
                If Frm.V_Confirm = False Then
                    Exit Sub
                End If
                Dim V_Str As String = ""
                Select Case Frm.V_YpLb
                    Case "全部药品"
                        V_Str = "Insert into Yk_Yf2 (Yy_Code,Ck_Code,Xx_Code,Ck_Sl,Ck_Xsj,Ck_Cgj,Ck_Pfj,Ck_Money,Ck_Memo,Mx_Cfbl,Ck_YfSl,Mx_CgDw,Mx_XsDw) select Yy_Code,'" & Label12.Text & "' as Ck_Code,Xx_Code,Yk_Sl,Yk_Xsj,Yk_Cgj,Yk_Pfj,(Yk_Sl*Yk_Xsj) as Ck_Money,'" & C1TextBox1.Text & "' as Ck_Memo,Mx_Cfbl,Yk_Sl*Mx_Cfbl,Mx_CgDw,Mx_XsDw from V_YpKc  Where Yy_Code='" & HisVar.HisVar.WsyCode & "' And Yk_Sl>0 and Xx_Code not in(select Xx_Code from Yk_Yf2 where Ck_Code='" & Label12.Text & "')"
                    Case Else
                        V_Str = "Insert into Yk_Yf2 (Yy_Code,Ck_Code,Xx_Code,Ck_Sl,Ck_Xsj,Ck_Cgj,Ck_Pfj,Ck_Money,Ck_Memo,Mx_Cfbl,Ck_YfSl,Mx_CgDw,Mx_XsDw) select Yy_Code,'" & Label12.Text & "' as Ck_Code,Xx_Code,Yk_Sl,Yk_Xsj,Yk_Cgj,Yk_Pfj,(Yk_Sl*Yk_Xsj) as Ck_Money,'" & C1TextBox1.Text & "' as Ck_Memo,Mx_Cfbl,Yk_Sl*Mx_Cfbl,Mx_CgDw,Mx_XsDw from V_YpKc  Where Dl_Name='" & Frm.V_YpLb & "' And Yy_Code='" & HisVar.HisVar.WsyCode & "' And Yk_Sl>0 and Xx_Code not in(select Xx_Code from Yk_Yf2 where Ck_Code='" & Label12.Text & "')"
                End Select
                HisVar.HisVar.Sqldal.ExecuteSql(V_Str)
                Call P_Data_Show()
            Case "查看统采药品"
                If CgkhCombo.SelectedValue = "" Then
                    Beep()
                    MsgBox("名称不能为空！", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    CgkhCombo.Select()
                    Exit Sub
                End If

                Call Zb_Save()
                Dim V_Form As New YpNetCg_Dr(Rrow, My_DataSet.Tables("明细"))
                V_Form.Name = "YpNetCg_Dr"
                V_Form.Text = "统采药品导入"
                V_Form.ShowDialog()
                Call P_Data_Show()
        End Select
        C1TrueDBGrid1.Select()
    End Sub

    Private Sub C1Command1_Print()
        Dim My_Dst As New DataSet
        Dim Stirpt As New StiReport
        If Form_Lb = "药库采购入库" Then
            Stirpt.Load(".\Rpt\药库入库单.mrt")
        ElseIf Form_Lb = "药库科室支领" Then
            Stirpt.Load(".\Rpt\药品科室支领表.mrt")
        ElseIf Form_Lb = "药库退供应商" Then
            Stirpt.Load(".\Rpt\药库退供应商表.mrt")
        ElseIf Form_Lb = "药库调拨药房" Then
            Stirpt.Load(".\Rpt\药库调拨药房表.mrt")
        ElseIf Form_Lb = "药库药品批发" Then
            Stirpt.Load(".\Rpt\药库批发表.mrt")
        ElseIf Form_Lb = "药房退回药库" Then
            Stirpt.Load(".\Rpt\药房退药库表.mrt")
        ElseIf Form_Lb = "药房科室支领" Then
            Dim assembly As System.Reflection.Assembly = Assembly.Load("ZTHisPharmacy")
            Dim rpt As Object =ZTHisPublicFunction.RptFunc.GetCustomRpt("药房科室支领表", assembly.GetManifestResourceStream("ZTHisPharmacy.Rpt.药房科室支领表.mrt"))
            StiRpt.Load(rpt)
'            Stirpt.Load(".\Rpt\药房科室支领表.mrt")
        ElseIf Form_Lb = "药房药品批发" Then
            Stirpt.Load(".\Rpt\药房批发表.mrt")
        ElseIf Form_Lb = "药房采购入库" Then
            Stirpt.Load(".\Rpt\药房入库单.mrt")
        ElseIf Form_Lb = "药房退供应商" Then
            Stirpt.Load(".\Rpt\药库退供应商表.mrt")
        End If
        Stirpt.ReportName = Form_Lb
        Stirpt.RegData(My_DataSet.Tables("明细"))
        Dim V_PageH As Double = 0
        Dim V_PName As String = ""
        If Form_Lb = "药库采购入库" Or Form_Lb = "药库科室支领" Or Form_Lb = "药库退供应商" Or Form_Lb = "药库调拨药房" Or Form_Lb = "药库药品批发" Then
            V_PName = IIf(iniOperate.iniopreate.GetINI("打印机设置", "药库管理纸型", "", HisVar.HisVar.Parapath & "\Config.ini") & "" = "", "80列(241mm*279.4mm)三分割(9.3)", iniOperate.iniopreate.GetINI("打印机设置", "药库管理纸型", "", HisVar.HisVar.Parapath & "\Config.ini"))
        ElseIf Form_Lb = "药房退回药库" Or Form_Lb = "药房科室支领" Or Form_Lb = "药房药品批发" Or Form_Lb = "药房采购入库" Or Form_Lb = "药房退供应商" Then
            V_PName = IIf(iniOperate.iniopreate.GetINI("打印机设置", "药房管理纸型", "", HisVar.HisVar.Parapath & "\Config.ini") & "" = "", "80列(241mm*279.4mm)三分割(9.3)", iniOperate.iniopreate.GetINI("打印机设置", "药房管理纸型", "", HisVar.HisVar.Parapath & "\Config.ini"))
        End If

        If V_PName = "80列(241mm*279.4mm)三分割(9.3)" Then
            V_PageH = 9.3
        ElseIf V_PName = "80列(241mm*279.4mm)二分割(13.95)" Then
            V_PageH = 13.95
        ElseIf V_PName = "80列(241mm*279.4mm)未分割(27.9)" Then
            V_PageH = 27.9
        End If
        If V_PageH <> 0 Then
            Stirpt.Pages(0).PaperSize = Printing.PaperKind.Custom
            Stirpt.Pages(0).PageHeight = V_PageH
            Stirpt.Compile()
            Stirpt("经手人") = "经手人:" & HisVar.HisVar.JsrName
            Stirpt("打印时间") = Format(Now, "yyyy-MM-dd HH:mm")
            Stirpt("客户") = CgkhCombo.Text
            Stirpt("医院名称") = ZTHisVar.Var.HosName
            Stirpt("药房") = ZTHisVar.Var.YfName
            'Stirpt.Design()
            Stirpt.Show()
        End If
    End Sub

    Private Sub C1TextBox1_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles C1TextBox1.KeyPress
        If e.KeyChar = Chr(Keys.Enter) Then
            e.Handled = True
            C1TrueDBGrid1.Focus()          '保存按扭
        End If
    End Sub

    Private Sub CgkhCombo_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles CgkhCombo.KeyPress
        If e.KeyChar = Chr(Keys.Enter) Then
            e.Handled = True
            C1TextBox1.Select()
        End If
    End Sub

#End Region

#Region "DBGrid动作"

    Dim m_Rc As New BaseClass.C_RowChange
    Private Sub C1TrueDBGrid1_RowColChange(ByVal sender As System.Object, ByVal e As C1.Win.C1TrueDBGrid.RowColChangeEventArgs) Handles C1TrueDBGrid1.RowColChange
        If (C1TrueDBGrid1.Row + 1) > C1TrueDBGrid1.RowCount Then
        Else
            Cb_Row = Cb_Cm.List(C1TrueDBGrid1.Row).Row
            m_Rc.ChangeRow(Cb_Row)
        End If
    End Sub

    Private Sub C1TrueDBGrid1_MouseDown(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles C1TrueDBGrid1.MouseDown
        If e.Button = Windows.Forms.MouseButtons.Right Then
            Call Cb_Edit()
        End If
    End Sub

    Private Sub C1TrueDBGrid1_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles C1TrueDBGrid1.KeyDown
        Select Case e.KeyCode
            Case Keys.Return
                Call Cb_Edit()
            Case Keys.Delete
                If V_CrkOk = "已完成" Then
                    MsgBox("已经完成不能删除！", MsgBoxStyle.Information, "提示：")
                    Exit Sub
                End If
                If (C1TrueDBGrid1.Row + 1) > C1TrueDBGrid1.RowCount Then
                Else
                    If MsgBox("是否删除:药品名称=" + Me.C1TrueDBGrid1.Columns("Yp_Name").Value + " ", MsgBoxStyle.Information + MsgBoxStyle.OkCancel, "提示:") = MsgBoxResult.Cancel Then Exit Sub
                    Dim My_Row As DataRow = Cb_Cm.List(C1TrueDBGrid1.Row).Row
                    Try
                        If Form_Lb = "药库采购入库" Then
                            ' HisVar.HisVar.Sqldal.ExecuteSql("Delete From Yk_Rk2 Where Rk_Code='" & My_Row.Item("Rk_Code") & "' And Xx_Code='" & My_Row.Item("Xx_Code") & "' And Yy_Code='" & HisVar.HisVar.WsyCode & "'")
                            _bllYkRk2.Delete(ZTHisVar.Var.HosCode, My_Row.Item("Xx_Code"), My_Row.Item("Rk_Code"))
                        ElseIf Form_Lb = "药库科室支领" Then
                            HisVar.HisVar.Sqldal.ExecuteSql("Delete From Yk_Ks2 Where Ck_Code='" & My_Row.Item("Ck_Code") & "' And Xx_Code='" & My_Row.Item("Xx_Code") & "' And Yy_Code='" & HisVar.HisVar.WsyCode & "'")
                        ElseIf Form_Lb = "药库退供应商" Then
                            HisVar.HisVar.Sqldal.ExecuteSql("Delete From Yk_TkPf2 Where Tk_Code='" & My_Row.Item("Tk_Code") & "' And Xx_Code='" & My_Row.Item("Xx_Code") & "' And Yy_Code='" & HisVar.HisVar.WsyCode & "'")
                        ElseIf Form_Lb = "药库调拨药房" Then
                            HisVar.HisVar.Sqldal.ExecuteSql("Delete From Yk_Yf2 Where Ck_Code='" & My_Row.Item("Ck_Code") & "' And Xx_Code='" & My_Row.Item("Xx_Code") & "' And Yy_Code='" & HisVar.HisVar.WsyCode & "'")
                        ElseIf Form_Lb = "药库药品批发" Then
                            HisVar.HisVar.Sqldal.ExecuteSql("Delete From Yk_Pf2 Where Ck_Code='" & My_Row.Item("Ck_Code") & "' And Xx_Code='" & My_Row.Item("Xx_Code") & "' And Yy_Code='" & HisVar.HisVar.WsyCode & "'")
                        ElseIf Form_Lb = "药房退回药库" Then
                            HisVar.HisVar.Sqldal.ExecuteSql("Delete From Yf_Tk2 Where Tk_Code='" & My_Row.Item("Tk_Code") & "' And Xx_Code='" & My_Row.Item("Xx_Code") & "' And Yy_Code='" & HisVar.HisVar.WsyCode & "'")
                        ElseIf Form_Lb = "药房科室支领" Then
                            HisVar.HisVar.Sqldal.ExecuteSql("Delete From Yf_Ks2 Where Ck_Code='" & My_Row.Item("Ck_Code") & "' And Xx_Code='" & My_Row.Item("Xx_Code") & "' And Yy_Code='" & HisVar.HisVar.WsyCode & "'")
                        ElseIf Form_Lb = "药房药品批发" Then
                            HisVar.HisVar.Sqldal.ExecuteSql("Delete From Yf_Pf2 Where Ck_Code='" & My_Row.Item("Ck_Code") & "' And Xx_Code='" & My_Row.Item("Xx_Code") & "' And Yy_Code='" & HisVar.HisVar.WsyCode & "'")
                        ElseIf Form_Lb = "药房采购入库" Then
                            'HisVar.HisVar.Sqldal.ExecuteSql("Delete From Yf_Rk2 Where Rk_Code='" & My_Row.Item("Rk_Code") & "' And Xx_Code='" & My_Row.Item("Xx_Code") & "' And Yy_Code='" & HisVar.HisVar.WsyCode & "'")
                            _bllYfRk2.Delete(ZTHisVar.Var.HosCode, My_Row.Item("Xx_Code"), My_Row.Item("Rk_Code"))
                        ElseIf Form_Lb = "药房退供应商" Then
                            HisVar.HisVar.Sqldal.ExecuteSql("Delete From Yf_TkPf2 Where Tk_Code='" & My_Row.Item("Tk_Code") & "' And Xx_Code='" & My_Row.Item("Xx_Code") & "' And Yy_Code='" & HisVar.HisVar.WsyCode & "'")
                        End If
                        C1TrueDBGrid1.Delete()
                        My_Row.AcceptChanges()
                        Call F_Sum()
                    Catch ex As Exception

                    End Try
                End If
        End Select

    End Sub

#End Region

#End Region

#Region "自定义函数"
    Private Sub GridMove(moveType As String)
        If C1TrueDBGrid1.RowCount = 0 Then
            Return
        End If
        Select Case moveType
            Case "最前"
                C1TrueDBGrid1.MoveFirst()
                Exit Select
            Case "上移"
                C1TrueDBGrid1.MovePrevious()
                Exit Select
            Case "下移"
                C1TrueDBGrid1.MoveNext()
                Exit Select
            Case "最后"
                C1TrueDBGrid1.MoveLast()
                Exit Select
            Case Else
                Dim index As Integer
                If Integer.TryParse(moveType, index) Then
                    C1TrueDBGrid1.Row = index
                End If
                Exit Select
        End Select
        F_Sum()
    End Sub
    Private Sub Cb_Edit()
        If CgkhCombo.SelectedValue = "" Then
            Beep()
            MsgBox("名称不能为空!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
            CgkhCombo.Select()
            Exit Sub
        End If
        '判断主表是否存在
        If (C1TrueDBGrid1.Row + 1) > C1TrueDBGrid1.RowCount Then
            V_Insert = True
        Else
            V_Insert = False
            Cb_Row = Cb_Cm.List(C1TrueDBGrid1.Row).Row
        End If

        If V_CrkOk = "已完成" Then
            MsgBox("已经完成不能修改！", MsgBoxStyle.Information, "提示：")
            Exit Sub
        Else
            Call Zb_Save()
            If Form_Lb = "药房采购入库" Then
                Dim vform As New ZTHisPharmacy.Yf_Rk3(Label12.Text, V_Insert, Cb_Row, My_DataSet.Tables("明细"))
                vform.MyTransmitTxt = _transmitTxt
                vform.ShowDialog()
            ElseIf Form_Lb = "药库采购入库" Then
                Dim vform As New ZTHisDrugStore.Yk_Rk3(Label12.Text, V_Insert, Cb_Row, My_DataSet.Tables("明细"))
                vform.MyTransmitTxt = _transmitTxt
                vform.ShowDialog()
            ElseIf Form_Lb = "药库调拨药房" Then
                Dim frm As New Yk_Yf3(Label12.Text, V_Insert, Cb_Row, My_DataSet.Tables("明细"))
                frm.MyTransmitTxt = _transmitTxt
                frm.ShowDialog()
            Else
                Dim vform As New BaseChild
                'If Form_Lb = "药库采购入库" Or Form_Lb = "药房采购入库" Then
                '    vform = New YkYf_Rk3(Me, V_Insert, Cb_Row, My_DataSet.Tables("明细"), C1TrueDBGrid1, Label12.Text, m_Rc, Form_Lb)
                If Form_Lb = "药库科室支领" Or Form_Lb = "药房科室支领" Or Form_Lb = "药库药品批发" Or Form_Lb = "药房药品批发" Or Form_Lb = "药房退回药库" Or Form_Lb = "药库调拨药房" Then
                    vform = New YkYf_Ck3(Me, V_Insert, Cb_Row, My_DataSet.Tables("明细"), C1TrueDBGrid1, Label12.Text, m_Rc, Form_Lb)
                ElseIf Form_Lb = "药库退供应商" Or Form_Lb = "药房退供应商" Then
                    vform = New YkYf_TkPf3(Me, V_Insert, Cb_Row, My_DataSet.Tables("明细"), C1TrueDBGrid1, Label12.Text, m_Rc, Form_Lb)
                End If
                If BaseFunc.BaseFunc.CheckOwnForm(Me, vform) = False Then
                    vform.Owner = Me
                    vform.Show()
                End If
            End If

        End If
    End Sub

    Private Function F_MaxCode(ByVal V_MaxCode As String)   '入库编码
        Dim My_Cc As New BaseClass.C_Cc()
        If Form_Lb = "药库采购入库" Then
            My_Cc.Get_MaxCode("Yk_Rk1", "Rk_Code", 14, "Left(Rk_Code,10)", V_MaxCode)
        ElseIf Form_Lb = "药库科室支领" Then
            My_Cc.Get_MaxCode("Yk_Ks1", "Ck_Code", 14, "Left(Ck_Code,10)", V_MaxCode)
        ElseIf Form_Lb = "药库退供应商" Then
            My_Cc.Get_MaxCode("Yk_TkPf1", "Tk_Code", 14, "Left(Tk_Code,10)", V_MaxCode)
        ElseIf Form_Lb = "药库调拨药房" Then
            My_Cc.Get_MaxCode("Yk_Yf1", "Ck_Code", 14, "Left(Ck_Code,10)", V_MaxCode)
        ElseIf Form_Lb = "药库药品批发" Then
            My_Cc.Get_MaxCode("Yk_Pf1", "Ck_Code", 14, "Left(Ck_Code,10)", V_MaxCode)
        ElseIf Form_Lb = "药房退回药库" Then
            My_Cc.Get_MaxCode("Yf_Tk1", "Tk_Code", 14, "Left(Tk_Code,10)", V_MaxCode)
        ElseIf Form_Lb = "药房科室支领" Then
            My_Cc.Get_MaxCode("Yf_Ks1", "Ck_Code", 14, "Left(Ck_Code,10)", V_MaxCode)
        ElseIf Form_Lb = "药房药品批发" Then
            My_Cc.Get_MaxCode("Yf_Pf1", "Ck_Code", 14, "Left(Ck_Code,10)", V_MaxCode)
        ElseIf Form_Lb = "药房采购入库" Then
            My_Cc.Get_MaxCode("Yf_Rk1", "Rk_Code", 14, "Left(Rk_Code,10)", V_MaxCode)
        ElseIf Form_Lb = "药房退供应商" Then
            My_Cc.Get_MaxCode("Yf_TkPf1", "Tk_Code", 14, "Left(Tk_Code,10)", V_MaxCode)
        End If
        F_MaxCode = My_Cc.编码
        If Mid(F_MaxCode, 1, 6) = "000000" Then F_MaxCode = V_MaxCode + Mid(My_Cc.编码, 7)
        Return F_MaxCode
    End Function

#End Region

#Region "输入法设置"
    '中文
    Private Sub C1TextBox1_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1TextBox1.GotFocus
        InputLanguage.CurrentInputLanguage = HisVar.HisVar.InputCode
    End Sub
    '英文
    Private Sub C1Numeric1_KeyDown(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CgkhCombo.GotFocus
        InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages(0)
    End Sub
#End Region

End Class
