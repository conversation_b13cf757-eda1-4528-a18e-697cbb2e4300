﻿/**  版本信息模板在安装目录下，可自行修改。
* D_Emr_ZhiKong1.cs
*
* 功 能： N/A
* 类 名： D_Emr_ZhiKong1
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/8/20 12:18:10   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
namespace DAL
{
	/// <summary>
	/// 数据访问类:D_Emr_ZhiKong1
	/// </summary>
	public partial class D_Emr_ZhiKong1
	{
		public D_Emr_ZhiKong1()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string Zk_Code)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from Emr_ZhiKong1");
			strSql.Append(" where Zk_Code=@Zk_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Zk_Code", SqlDbType.Char,10)			};
			parameters[0].Value = Zk_Code;

			return HisVar.HisVar.Sqldal.Exists(strSql.ToString(),parameters);
		}
        public string MaxCode()
        {
            string max = (string)(HisVar.HisVar.Sqldal.F_MaxCode("select max(Zk_Code) from Emr_ZhiKong1", 10));
            return max;
        }

		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ModelOld.M_Emr_ZhiKong1 model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into Emr_ZhiKong1(");
			strSql.Append("Zk_Code,Mblb_Code,Mb_Code)");
			strSql.Append(" values (");
			strSql.Append("@Zk_Code,@Mblb_Code,@Mb_Code)");
			SqlParameter[] parameters = {
					new SqlParameter("@Zk_Code", SqlDbType.Char,10),
					new SqlParameter("@Mblb_Code", SqlDbType.Char,5),
					new SqlParameter("@Mb_Code", SqlDbType.Char,10)};
			parameters[0].Value = model.Zk_Code;
			parameters[1].Value =model.Mblb_Code;
            parameters[2].Value = Common.Tools.IsValueNull(model.Mb_Code);

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ModelOld.M_Emr_ZhiKong1 model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update Emr_ZhiKong1 set ");
			strSql.Append("Mblb_Code=@Mblb_Code,");
			strSql.Append("Mb_Code=@Mb_Code");
			strSql.Append(" where Zk_Code=@Zk_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Mblb_Code", SqlDbType.Char,5),
					new SqlParameter("@Mb_Code", SqlDbType.Char,10),
					new SqlParameter("@Zk_Code", SqlDbType.Char,10)};
			parameters[0].Value = model.Mblb_Code;
            parameters[1].Value = Common.Tools.IsValueNull(model.Mb_Code);
			parameters[2].Value = model.Zk_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string Zk_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Emr_ZhiKong1 ");
			strSql.Append(" where Zk_Code=@Zk_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Zk_Code", SqlDbType.Char,10)			};
			parameters[0].Value = Zk_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string Zk_Codelist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Emr_ZhiKong1 ");
			strSql.Append(" where Zk_Code in ("+Zk_Codelist + ")  ");
			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Emr_ZhiKong1 GetModel(string Zk_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 Zk_Code,Mblb_Code,Mb_Code from Emr_ZhiKong1 ");
			strSql.Append(" where Zk_Code=@Zk_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Zk_Code", SqlDbType.Char,10)			};
			parameters[0].Value = Zk_Code;

			ModelOld.M_Emr_ZhiKong1 model=new ModelOld.M_Emr_ZhiKong1();
			DataSet ds=HisVar.HisVar.Sqldal.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Emr_ZhiKong1 DataRowToModel(DataRow row)
		{
			ModelOld.M_Emr_ZhiKong1 model=new ModelOld.M_Emr_ZhiKong1();
			if (row != null)
			{
				if(row["Zk_Code"]!=null)
				{
					model.Zk_Code=row["Zk_Code"].ToString();
				}
				if(row["Mblb_Code"]!=null)
				{
					model.Mblb_Code=row["Mblb_Code"].ToString();
				}
				if(row["Mb_Code"]!=null)
				{
					model.Mb_Code=row["Mb_Code"].ToString();
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
            strSql.Append("select Zk_Code,dbo.Emr_Mblb.Mblb_Code,Mblb_Name,dbo.Emr_ZhiKong1.Mb_Code,Mb_Name");
            strSql.Append(" FROM Emr_ZhiKong1 JOIN dbo.Emr_Mblb ON Emr_Mblb.Mblb_Code = Emr_ZhiKong1.Mblb_Code ");
            strSql.Append(" LEFT OUTER JOIN dbo.Emr_Mb ON Emr_Mb.Mb_Code = Emr_ZhiKong1.Mb_Code  ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" Zk_Code,Mblb_Code,Mb_Code ");
			strSql.Append(" FROM Emr_ZhiKong1 ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM Emr_ZhiKong1 ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
            object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.Zk_Code desc");
			}
			strSql.Append(")AS Row, T.*  from Emr_ZhiKong1 T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Emr_ZhiKong1";
			parameters[1].Value = "Zk_Code";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return HisVar.HisVar.Sqldal.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

