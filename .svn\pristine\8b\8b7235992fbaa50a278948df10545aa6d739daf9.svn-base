﻿Imports Stimulsoft.Report
Imports Stimulsoft.Report.Components
Public Class Yp_Cr_Tz
    Dim My_Date As String
    Dim My_Dataset1 As New DataSet
    Dim My_Dataset2 As New DataSet
    Public Zb_Cm As CurrencyManager
    Dim V_Fiter As String

    Private Sub Yp_Cr_Tz_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        V_Fiter = " and Yk_Sl<>0"
        Call Init_Form()
        Call Init_Grid()
        Call Init_Data()
        Call F_Sum()
    End Sub

    Private Sub Init_Form()
        Dim My_Combo As New BaseClass.C_Combo1(Me.C1Combo1)
        With My_Combo
            .Init_TDBCombo()
            .AddItem("药品简称")
            .AddItem("药品名称")
            .SelectedIndex(0)
        End With
        C1Combo1.Width = 100
        Me.Panel2.Location = New Point(0, 0)
        My_Date = Date.Today
        With C1DateEdit1
            .Value = My_Date
            .DateTimeInput = True
            .AutoChangePosition = False
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .InitialSelection = C1.Win.C1Input.InitialSelectionEnum.CaretAtEnd
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .EditFormat.CustomFormat = "yyyy-MM-dd"
            .DisplayFormat.CustomFormat = "yyyy-MM-dd"
            .MaskInfo.AutoTabWhenFilled = True
            .AcceptsTab = True
            .EmptyAsNull = True
        End With
        With C1DateEdit2
            .Value = My_Date
            .DateTimeInput = True
            .AutoChangePosition = False
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .InitialSelection = C1.Win.C1Input.InitialSelectionEnum.CaretAtEnd
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .EditFormat.CustomFormat = "yyyy-MM-dd"
            .DisplayFormat.CustomFormat = "yyyy-MM-dd"
            .MaskInfo.AutoTabWhenFilled = True
            .AcceptsTab = True
            .EmptyAsNull = True
        End With

        Dim My_Combo2 As New BaseClass.C_Combo1(Me.C1Combo2)
        With My_Combo2
            .Init_TDBCombo()
            .AddItem("合计金额")
            .AddItem("药品级别")
            .AddItem("厂家级别")
            .SelectedIndex(0)
        End With
        C1Combo2.Width = 90

    End Sub

    Private Sub Init_Grid()
        Dim My_Grid1 As New BaseClass.C_Grid(Me.C1TrueDBGrid1)
        Dim My_Grid2 As New BaseClass.C_Grid(Me.C1TrueDBGrid2)
        With My_Grid1
            .Init_Grid()
            .Init_Column("", "Xx_Code", 0, "", "")
            .Init_Column("药品名称", "Yp_Name", 200, "左", "")
            .Init_Column("药品规格", "Mx_Gg", 100, "左", "")
            .Init_Column("单位", "Mx_CgDw", 45, "中", "")
            .Init_Column("产地", "Mx_Cd", 100, "左", "")
            .Init_Column("有效期", "Yp_Yxq", 75, "中", "yyyy-MM-dd")
        End With

        With My_Grid2
            .Init_Grid()
            .Init_Column("类别", "Lb", 70, "中", "")
            .Init_Column("日期", "Mx_Date", 75, "中", "yyyy-MM-dd")
            '.Init_Column("票号", "Mx_Ph", 80, "中", "")
            .Init_Column("数量", "Mx_Sl", 47, "右", "##0.####")
            .Init_Column("单价", "Mx_Cgj", 45, "右", "###0.00")
            .Init_Column("金额", "Mx_CgMoney", 60, "右", "###0.00")
            .Init_Column("经手人", "Jsr_Name", 55, "左", "")
        End With
        C1TrueDBGrid1.Splits(0).DisplayColumns(0).Visible = False

        C1TrueDBGrid1.Splits(0).HScrollBar.Style = C1.Win.C1TrueDBGrid.ScrollBarStyleEnum.Automatic
        C1TrueDBGrid2.Splits(0).HScrollBar.Style = C1.Win.C1TrueDBGrid.ScrollBarStyleEnum.Automatic
        C1TrueDBGrid2.ColumnFooters = True
        C1TrueDBGrid1.RecordSelectors = False
        C1TrueDBGrid2.RecordSelectors = False
    End Sub
    Public Overrides Sub F_Sum()
        If C1TrueDBGrid2.RowCount = 0 Then
            C1TrueDBGrid2.Columns(2).FooterText = 0

        Else
            C1TrueDBGrid2.Columns(2).FooterText = Format(My_Dataset2.Tables("药品详情").Compute("Sum(Mx_Sl)", ""), "###0.####")

        End If
        C1TrueDBGrid1.Select()
    End Sub

    Private Sub Init_Data()
        My_Dataset1.Clear()
        Dim My_Adapter As New SqlClient.SqlDataAdapter
        My_Adapter.SelectCommand = New SqlClient.SqlCommand("select * From V_YpKc Where  Yy_Code='" & HisVar.HisVar.WsyCode & "'" & V_Fiter & "", My_Cn)
        My_Adapter.Fill(My_Dataset1, "药品信息")
        Me.C1TrueDBGrid1.SetDataBinding(My_Dataset1, "药品信息", True)
        Zb_Cm = CType(BindingContext(C1TrueDBGrid1.DataSource, C1TrueDBGrid1.DataMember), CurrencyManager)
    End Sub

    Private Sub C1TextBox1_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1TextBox1.TextChanged
        Dim My_View As New DataView
        My_View = Zb_Cm.List
        Dim V_Sort As String = ""
        Dim V_RowFilter As String = ""

        If Trim(Me.C1TextBox1.Text & "") = "" Then
            V_Sort = "Yp_Jc Asc"
            V_RowFilter = ""
        Else
            Select Case Me.C1Combo1.Text
                Case "药品简称"
                    V_Sort = "Yp_Jc Asc"
                    V_RowFilter = "Yp_Jc Like '*" & Trim(C1TextBox1.Text) & "*'"
                Case "药品名称"
                    V_Sort = "Yp_Name Asc"
                    V_RowFilter = "Yp_Name Like '*" & Trim(C1TextBox1.Text) & "*'"
            End Select
        End If

        My_View.Sort = V_Sort
        My_View.RowFilter = V_RowFilter

    End Sub

    Private Sub C1Combo1_Close(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Combo1.Close
        C1TextBox1.Select()
        Select Case Mid(Me.C1Combo1.Text, 3)
            Case "名称"
                InputLanguage.CurrentInputLanguage = HisVar.HisVar.InputCode
            Case "简称"
                InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages(0)
        End Select
    End Sub

    Private Sub CheckBox1_CheckedChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles CheckBox1.CheckedChanged
        If Me.CheckBox1.Checked = True Then
            V_Fiter = ""
            Call Init_Data()
        Else
            V_Fiter = " and Yk_Sl<>0"
            Call Init_Data()
        End If

    End Sub

    Private Sub C1Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Button1.Click
        My_Dataset2.Clear()

        Dim Cx_Date1 As String
        Dim Cx_Date2 As String

        Cx_Date1 = Format(C1DateEdit1.Value, "yyyy-MM-dd 00:00:00")
        Cx_Date2 = Format(C1DateEdit2.Value, "yyyy-MM-dd 23:59:59")
        Dim V_Str1 As String
        V_Str1 = "select '1' as V_Order,'采购入库'as Lb,Rk_Date Mx_Date,Yk_Rk1.Rk_Code Mx_Ph,Rk_Sl Mx_Sl,Rk_Dj Mx_Cgj,Rk_Dj*Rk_Sl Mx_CgMoney,Jsr_Name from Yk_Rk1,Yk_Rk2,Zd_YyJsr where Yk_Rk1.Rk_Code=Yk_Rk2.Rk_Code and Xx_Code='" & C1TrueDBGrid1.Columns("Xx_Code").Value & "' and Rk_Date between '" & Cx_Date1 & "' and '" & Cx_Date2 & "' and Yk_Rk1.Jsr_Code=Zd_YyJsr.Jsr_Code  and Rk_Ok='已完成'" & _
       " union all" & _
       " select '2' as V_Order,'药品批发'   ,Ck_Date,Yk_Pf1.Ck_Code,-Ck_Sl,Ck_Dj,Ck_Dj*Ck_Sl,Jsr_Name from Yk_Pf1,Yk_Pf2,Zd_YyJsr where Yk_Pf1.Ck_Code=Yk_Pf2.Ck_Code and Xx_Code='" & C1TrueDBGrid1.Columns("Xx_Code").Value & "' and Ck_date between'" & Cx_Date1 & "'and'" & Cx_Date2 & "' and Yk_Pf1.Jsr_Code=Zd_YyJsr.Jsr_Code  and Ck_Ok='已完成'" & _
       " union all" & _
       " select '3' as V_Order,'调拨药房'   ,Ck_Date,Yk_Yf1.Ck_Code,-Ck_Sl,Ck_Xsj,Ck_Xsj*Ck_Sl,Jsr_Name from Yk_Yf1,Yk_Yf2,Zd_YyJsr where Yk_Yf1.Ck_Code=Yk_Yf2.Ck_Code and Xx_Code='" & C1TrueDBGrid1.Columns("Xx_Code").Value & "' and Ck_Date between'" & Cx_Date1 & "'and'" & Cx_Date2 & "' and Yk_Yf1.Jsr_Code=Zd_YyJsr.Jsr_Code  And Ck_Qr=1" & _
       " union all" & _
       " select '4' as V_Order,'科室支领'  ,Ck_Date,Yk_Ks1.Ck_Code,-Ck_Sl,Ck_Cgj,Ck_Cgj*Ck_Sl,Jsr_Name from Yk_Ks1,Yk_Ks2,Zd_YyJsr where Yk_Ks1.Ck_Code=Yk_Ks2.Ck_Code and Xx_Code='" & C1TrueDBGrid1.Columns("Xx_Code").Value & "' and Ck_Date between'" & Cx_Date1 & "'and'" & Cx_Date2 & "' and Yk_Ks1.Jsr_Code=Zd_YyJsr.Jsr_Code  and Ck_Ok='已完成'" & _
       " union all" & _
       " Select '5' As V_Order, '药库盘点',Pd_Date,Pd_Month,Pd_Sl-Mx_Sl,Yk_CgDj,(Pd_Sl-Mx_Sl)*Yk_CgDj,Jsr_Name From Zd_YkPd ,Zd_YyJsr Where Zd_YkPd.Yy_Code=Zd_YyJsr.Yy_Code And Pd_Date Between '" & Cx_Date1 & "' And '" & Cx_Date2 & "' And Pd_Wc='是' And Xx_Code='" & C1TrueDBGrid1.Columns("Xx_Code").Value & "' And Pd_Sl-Mx_Sl<>0  and Zd_YkPd.Jsr_Code=Zd_YyJsr.Jsr_Code  and Pd_Wc='是'" & _
       " union all" & _
       " select '6' as V_Order,'药库退库',  tk_Date,Yk_TkPf1.tk_Code,-tk_Sl,tk_Dj,tk_Dj*tk_Sl,Jsr_Name from Yk_TkPf1,Yk_TkPf2,Zd_YyJsr where Yk_TkPf1.tk_Code=Yk_TkPf2.tk_Code and tk_Date between'" & Cx_Date1 & "'and '" & Cx_Date2 & "'and Xx_Code='" & C1TrueDBGrid1.Columns("Xx_Code").Value & "' and Yk_TkPf1.Jsr_Code=Zd_YyJsr.Jsr_Code   and Ck_Ok='已完成'" & _
       " union all" & _
       " select '7' as V_Order,'药房退库',  tk_Date,Yf_tk1.tk_Code,TK_SL/Mx_Cfbl as Yk_JsSl,TK_DJ * Mx_Cfbl as  Yk_JsXsj,Tk_Dj*Tk_Sl,Jsr_Name from V_YpKc,Yf_tk1,Yf_Tk2,Zd_YyJsr where V_YpKc.Xx_Code=Yf_Tk2.Xx_Code and Yf_tk1.tk_Code=Yf_Tk2.tk_Code and tk_Date between'" & Cx_Date1 & "'and '" & Cx_Date2 & "'and Yf_Tk2.Xx_Code='" & C1TrueDBGrid1.Columns("Xx_Code").Value & "' and Yf_tk1.Jsr_Code=Zd_YyJsr.Jsr_Code  And Tk_Qr=1" & _
        " union all" & _
       " select '8' as V_Order,'报损报益'  ,BsBy_Date,BsBy_Code,BsBy_Sl,Yk_Cgj,Yk_Cgj*BsBy_Sl,Jsr_Name from Yk_Bsby,Zd_Yyjsr,V_Ypkc  where Yk_Bsby.Xx_Code=V_Ypkc.Xx_Code and Yk_Bsby.Xx_code='" & C1TrueDBGrid1.Columns("Xx_Code").Value & "' and BsBy_Date between'" & Cx_Date1 & "' And '" & Cx_Date2 & "'  and  Yk_Bsby.Jsr_Code=Zd_YyJsr.Jsr_Code   Order By V_Order"


        Dim My_Adapter2 As New SqlClient.SqlDataAdapter
        My_Adapter2.SelectCommand = New SqlClient.SqlCommand(V_Str1, My_Cn)
        My_Adapter2.Fill(My_Dataset2, "药品详情")
        Me.C1TrueDBGrid2.SetDataBinding(My_Dataset2, "药品详情", True)
        Call F_Sum()
    End Sub
    
    Private Sub Button2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button2.Click
        Dim My_Dataset As New DataSet
        Dim Cx_Date1 As String = Format(C1DateEdit1.Value, "yyyy-MM-dd 00:00:00")
        Dim Cx_Date2 As String = Format(C1DateEdit2.Value, "yyyy-MM-dd 23:59:59")
        Dim Str As String = ""
        Dim Str0 As String = ""
        Dim Str1 As String = ""
        Dim Str2 As String = ""
        Dim GgCd As String = ""
        If C1Combo2.Text = "合计金额" Then
            Str = "Yp_Code"
            Str0 = "Dl_Name,Dl_Code,"
            Str1 = "Dl_Name,Dl_Code,"
            Str2 = " group by Dl_Name,Dl_Code"
            GgCd = ",Dl_Name,Dl_Code"
        Else
            Str = "Mx_Code"
            Str0 = " " & Str & ",yp_name,Mx_Gg, Mx_Cd,Mx_CgDw,Dl_Name,Dl_Code,"
            Str1 = "Kc." & Str & ",yp_name,Mx_Gg, Mx_Cd,Mx_CgDw,Dl_Name,Dl_Code,"
            Str2 = " group by Kc." & Str & ",yp_name,Mx_Gg, Mx_Cd,Mx_CgDw,Dl_Name,Dl_Code"
            GgCd = ",Mx_Gg, Mx_Cd,Mx_CgDw,Dl_Name,Dl_Code"
        End If
        Dim V_Str As String = " select " & Str0 & "Mx_Money,Rk_Money,Pf_Money,Yf_Money,Ks_Money,Pd_Money,Tk_Money,YfTk_Money,BsBy_Money,Crk_Money,Yk_Sl,Rk_Sl,Pf_Sl,Yf_Sl,Ks_Sl,Pd_Sl,Tk_Sl,YfTk_Sl,BsBy_Sl,Crk_Sl  from (" & _
            " select " & Str1 & "sum(Mx_Money)Mx_Money,isnull(sum(Rk_Money),0)Rk_Money,isnull(sum(Pf_Money),0)Pf_Money,isnull(sum(Yf_Money),0)Yf_Money,isnull(sum(Ks_Money),0)Ks_Money,isnull(sum(Pd_Money),0)Pd_Money,isnull(sum(Tk_Money),0)Tk_Money,isnull(sum(YfTk_Money),0)YfTk_Money,isnull(sum(BsBy_Money),0)BsBy_Money,(isnull(sum(Rk_Money),0)-isnull(sum(Pf_Money),0)-isnull(sum(Yf_Money),0)-isnull(sum(Ks_Money),0)+isnull(sum(Pd_Money),0)-isnull(sum(Tk_Money),0)+isnull(sum(YfTk_Money),0)+isnull(sum(BsBy_Money),0))Crk_Money," & _
            " sum(Yk_Sl)Yk_Sl,isnull(sum(Rk_Sl),0)Rk_Sl,isnull(sum(Pf_Sl),0)Pf_Sl,isnull(sum(Yf_Sl),0)Yf_Sl,isnull(sum(Ks_Sl),0)Ks_Sl,isnull(sum(Pd_Sl),0)Pd_Sl,isnull(sum(Tk_Sl),0)Tk_Sl,isnull(sum(YfTk_Sl),0)YfTk_Sl,isnull(sum(BsBy_Sl),0)BsBy_Sl,(isnull(sum(Rk_Sl),0)-isnull(sum(Pf_Sl),0)-isnull(sum(Yf_Sl),0)-isnull(sum(Ks_Sl),0)+isnull(sum(Pd_Sl),0)-isnull(sum(Tk_Sl),0)+isnull(sum(YfTk_Sl),0)+isnull(sum(BsBy_Sl),0))Crk_Sl from" & _
        "(select sum(Yk_Sl)Yk_Sl,sum(Yk_Cgj*Yk_Sl) Mx_Money," & Str & ",Yp_Name" & GgCd & "  from v_ypKc   group by " & Str & ",Yp_Name" & GgCd & ") Kc " & _
        "Left Join (select sum(Rk_Sl)Rk_Sl,sum(Yk_Cgj*Rk_Sl) Rk_Money,v_ypKc." & Str & " from Yk_Rk1,Yk_Rk2,v_ypKc where Yk_Rk2.xx_code=v_ypkc.xx_code and Yk_Rk1.Rk_Code=Yk_Rk2.Rk_Code and  Rk_Date between '" & Cx_Date1 & "' and '" & Cx_Date2 & "'   and Rk_Ok='已完成'  group by v_ypKc." & Str & ")Rk on Rk." & Str & "=Kc." & Str & "   " & _
      "  Left Join (select sum(Ck_Sl)Pf_Sl,sum(Yk_Cgj*Ck_Sl) Pf_Money,v_ypKc." & Str & " from Yk_Pf1,Yk_Pf2 ,v_ypKc where Yk_Pf2.xx_code=v_ypkc.xx_code and  Yk_Pf1.Ck_Code=Yk_Pf2.Ck_Code  and Ck_date between'" & Cx_Date1 & "'and'" & Cx_Date2 & "'   and Ck_Ok='已完成'  group by v_ypKc." & Str & ")Pf on  Pf." & Str & "=Kc." & Str & " " & _
      "  Left Join (select sum(Ck_Sl)Yf_Sl,sum(Yk_Cgj*Ck_Sl) Yf_Money,v_ypKc." & Str & " from Yk_Yf1,Yk_Yf2 ,v_ypKc where Yk_Yf2.xx_code=v_ypkc.xx_code and  Yk_Yf1.Ck_Code=Yk_Yf2.Ck_Code  and Ck_Date between'" & Cx_Date1 & "'and'" & Cx_Date2 & "'   And Ck_Qr=1   group by v_ypKc." & Str & ")Yf on Yf." & Str & "=Kc." & Str & "   " & _
     "   Left Join (select sum(Ck_Sl)Ks_Sl,sum(Yk_Cgj*Ck_Sl) Ks_Money,v_ypKc." & Str & " from Yk_Ks1,Yk_Ks2 ,v_ypKc where Yk_Ks2.xx_code=v_ypkc.xx_code and  Yk_Ks1.Ck_Code=Yk_Ks2.Ck_Code  and Ck_Date between'" & Cx_Date1 & "'and'" & Cx_Date2 & "'   and Ck_Ok='已完成' group by v_ypKc." & Str & ")Ks on  Ks." & Str & "=Kc." & Str & "  " & _
      "  Left Join (Select sum(Pd_Sl-Mx_Sl)Pd_Sl,sum((Pd_Sl-Mx_Sl)*Yk_Cgj)Pd_Money,v_ypKc." & Str & " From Zd_YkPd  ,v_ypKc where Zd_YkPd.xx_code=v_ypkc.xx_code and    Pd_Date Between '" & Cx_Date1 & "' And '" & Cx_Date2 & "' And Pd_Wc='是'   And Pd_Sl-Mx_Sl<>0    and Pd_Wc='是' group by v_ypKc." & Str & ")Pd on Pd." & Str & "=Kc." & Str & "    " & _
       " Left Join(select sum(Tk_Sl)Tk_Sl,sum(Yk_Cgj*tk_Sl)Tk_Money,v_ypKc." & Str & " from Yk_TkPf1,Yk_TkPf2 ,v_ypKc where Yk_TkPf2.xx_code=v_ypkc.xx_code and  Yk_TkPf1.tk_Code=Yk_TkPf2.tk_Code and tk_Date between'" & Cx_Date1 & "'and '" & Cx_Date2 & "'   And Yk_TkPf1.Yy_Code='" & HisVar.HisVar.WsyCode & "' and Ck_Ok='已完成'  group by v_ypKc." & Str & ")Tk on Tk." & Str & "=Kc." & Str & "   " & _
      "  Left Join (select  sum(TK_SL/Mx_Cfbl)YfTk_Sl,sum(Yk_Cgj*TK_SL/Mx_Cfbl) YfTk_Money,v_ypKc." & Str & " from Yf_tk1,Yf_Tk2 ,V_YpKc where Yf_Tk2.xx_code=v_ypkc.xx_code and  Yf_tk1.tk_Code=Yf_Tk2.tk_Code and tk_Date between'" & Cx_Date1 & "'and '" & Cx_Date2 & "'  And Tk_Qr=1  group by V_YpKc." & Str & ")YfTk on YfTk." & Str & "=Kc." & Str & "  " & _
       "Left Join (select sum(BsBy_Sl)BsBy_Sl,sum(Yk_Cgj*BsBy_Sl)BsBy_Money,v_ypKc." & Str & " from Yk_Bsby,V_YpKc  where Yk_Bsby.Xx_Code=V_YpKc.Xx_Code  and BsBy_Date between'" & Cx_Date1 & "' And '" & Cx_Date2 & "'    group by v_ypKc." & Str & ")BsBy on Bsby." & Str & "=Kc." & Str & " " & _
 " " & Str2 & ")A  where  Rk_Money<>0 or Pf_Money<>0 or Yf_Money<>0 or Ks_Money<>0 or Pd_Money<>0 or Tk_Money<>0 or YfTk_Money<>0 or BsBy_Money<>0  order by Dl_Code"

        My_Dataset.EnforceConstraints = False
        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, V_Str, "台账统计", True)

        Dim Stirpt As New StiReport
        Stirpt.RegData(My_Dataset.Tables("台账统计"))
        Stirpt.Pages(0).PaperSize = Printing.PaperKind.Custom
        If C1Combo2.Text = "合计金额" Then
            Stirpt.Load(".\Rpt\药库台账汇总.mrt")
        Else
            Stirpt.Load(".\Rpt\药库台账统计.mrt")
        End If

        Stirpt.ReportName = "药库台账统计"

        If C1Combo2.Text = "药品级别" Then
            Dim groupBand As New StiGroupHeaderBand
            groupBand = Stirpt.GetComponents("GroupHeaderBand1")
            groupBand.Condition.Value = "{台账统计.yp_name}"
            groupBand.Height = 0
            TryCast(Stirpt.Pages(0).GetComponents.Item("Text35"), StiText).Text = ""
            TryCast(Stirpt.Pages(0).GetComponents.Item("Text36"), StiText).Text = ""

            Dim groupBand2 As New StiGroupHeaderBand
            groupBand2 = Stirpt.GetComponents("GroupHeaderBand2")
            groupBand2.Condition.Value = "{台账统计.yp_name}{台账统计.Mx_CgDw}"
            groupBand2.Height = 0
            TryCast(Stirpt.Pages(1).GetComponents.Item("Text64"), StiText).Text = ""
            TryCast(Stirpt.Pages(1).GetComponents.Item("Text65"), StiText).Text = ""
        End If
  
        Stirpt.Compile()
        Stirpt("打印时间") = Format(Now, "yyyy-MM-dd HH:mm:ss")

        Stirpt("查询时间") = Cx_Date1 + "至" + Cx_Date2

        'Stirpt.Design()
        Stirpt.Show()
    End Sub
End Class