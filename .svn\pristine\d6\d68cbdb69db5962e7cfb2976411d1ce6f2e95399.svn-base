﻿/**  版本信息模板在安装目录下，可自行修改。
* M_Emr_Mb.cs
*
* 功 能： N/A
* 类 名： M_Emr_Mb
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/8/10 11:31:32   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace ModelOld
{
	/// <summary>
	/// M_Emr_Mb:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class M_Emr_Mb
	{
		public M_Emr_Mb()
		{}
		#region Model
		private string _mb_code;
		private string _mblb_code;
		private string _mb_name;
		private string _mb_jc;
		private byte[] _mb_nr;
		private string _mb_sex;
		private bool _ismust;
		private bool _ismulti;
		private int? _agelimit;
		private bool _isstandard;
		private string _ks_code;
		private string _ys_code;
		private string _mb_memo;
		/// <summary>
		/// 
		/// </summary>
		public string Mb_Code
		{
			set{ _mb_code=value;}
			get{return _mb_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Mblb_Code
		{
			set{ _mblb_code=value;}
			get{return _mblb_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Mb_Name
		{
			set{ _mb_name=value;}
			get{return _mb_name;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Mb_Jc
		{
			set{ _mb_jc=value;}
			get{return _mb_jc;}
		}
		/// <summary>
		/// 
		/// </summary>
		public byte[] Mb_Nr
		{
			set{ _mb_nr=value;}
			get{return _mb_nr;}
		}
		/// <summary>
		/// 0，通用；1，男；2，女
		/// </summary>
		public string Mb_Sex
		{
			set{ _mb_sex=value;}
			get{return _mb_sex;}
		}
		/// <summary>
		/// 是否必需
		/// </summary>
		public bool isMust
		{
			set{ _ismust=value;}
			get{return _ismust;}
		}
		/// <summary>
		/// 是否多份
		/// </summary>
		public bool isMulti
		{
			set{ _ismulti=value;}
			get{return _ismulti;}
		}
		/// <summary>
		/// 年龄限制
		/// </summary>
		public int? AgeLimit
		{
			set{ _agelimit=value;}
			get{return _agelimit;}
		}
		/// <summary>
		/// 
		/// </summary>
		public bool isStandard
		{
			set{ _isstandard=value;}
			get{return _isstandard;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Ks_Code
		{
			set{ _ks_code=value;}
			get{return _ks_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Ys_Code
		{
			set{ _ys_code=value;}
			get{return _ys_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Mb_Memo
		{
			set{ _mb_memo=value;}
			get{return _mb_memo;}
		}
		#endregion Model

	}
}

