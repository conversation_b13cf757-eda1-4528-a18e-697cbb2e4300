﻿/**  版本信息模板在安装目录下，可自行修改。
* D_Zd_YyKs.cs
*
* 功 能： N/A
* 类 名： D_Zd_YyKs
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/8/11 9:17:02   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
namespace DAL
{
	/// <summary>
	/// 数据访问类:D_Zd_YyKs
	/// </summary>
	public partial class D_Zd_YyKs
	{
		public D_Zd_YyKs()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string Ks_Code)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from Zd_YyKs");
			strSql.Append(" where Ks_Code=@Ks_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Ks_Code", SqlDbType.Char,6)			};
			parameters[0].Value = Ks_Code;

			return HisVar.HisVar.Sqldal.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ModelOld.M_Zd_YyKs model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into Zd_YyKs(");
			strSql.Append("Yy_Code,Ks_Code,Ks_Name,Ks_Jc,Ks_Fzr,Ks_Tel,Ks_Memo)");
			strSql.Append(" values (");
			strSql.Append("@Yy_Code,@Ks_Code,@Ks_Name,@Ks_Jc,@Ks_Fzr,@Ks_Tel,@Ks_Memo)");
			SqlParameter[] parameters = {
					new SqlParameter("@Yy_Code", SqlDbType.Char,4),
					new SqlParameter("@Ks_Code", SqlDbType.Char,6),
					new SqlParameter("@Ks_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Ks_Jc", SqlDbType.VarChar,30),
					new SqlParameter("@Ks_Fzr", SqlDbType.VarChar,50),
					new SqlParameter("@Ks_Tel", SqlDbType.VarChar,50),
					new SqlParameter("@Ks_Memo", SqlDbType.VarChar,200)};
			parameters[0].Value = model.Yy_Code;
			parameters[1].Value = model.Ks_Code;
			parameters[2].Value = model.Ks_Name;
			parameters[3].Value = model.Ks_Jc;
			parameters[4].Value = model.Ks_Fzr;
			parameters[5].Value = model.Ks_Tel;
			parameters[6].Value = model.Ks_Memo;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ModelOld.M_Zd_YyKs model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update Zd_YyKs set ");
			strSql.Append("Yy_Code=@Yy_Code,");
			strSql.Append("Ks_Name=@Ks_Name,");
			strSql.Append("Ks_Jc=@Ks_Jc,");
			strSql.Append("Ks_Fzr=@Ks_Fzr,");
			strSql.Append("Ks_Tel=@Ks_Tel,");
			strSql.Append("Ks_Memo=@Ks_Memo");
			strSql.Append(" where Ks_Code=@Ks_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Yy_Code", SqlDbType.Char,4),
					new SqlParameter("@Ks_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Ks_Jc", SqlDbType.VarChar,30),
					new SqlParameter("@Ks_Fzr", SqlDbType.VarChar,50),
					new SqlParameter("@Ks_Tel", SqlDbType.VarChar,50),
					new SqlParameter("@Ks_Memo", SqlDbType.VarChar,200),
					new SqlParameter("@Ks_Code", SqlDbType.Char,6)};
			parameters[0].Value = model.Yy_Code;
			parameters[1].Value = model.Ks_Name;
			parameters[2].Value = model.Ks_Jc;
			parameters[3].Value = model.Ks_Fzr;
			parameters[4].Value = model.Ks_Tel;
			parameters[5].Value = model.Ks_Memo;
			parameters[6].Value = model.Ks_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string Ks_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Zd_YyKs ");
			strSql.Append(" where Ks_Code=@Ks_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Ks_Code", SqlDbType.Char,6)			};
			parameters[0].Value = Ks_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string Ks_Codelist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Zd_YyKs ");
			strSql.Append(" where Ks_Code in ("+Ks_Codelist + ")  ");
			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Zd_YyKs GetModel(string Ks_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 Yy_Code,Ks_Code,Ks_Name,Ks_Jc,Ks_Fzr,Ks_Tel,Ks_Memo from Zd_YyKs ");
			strSql.Append(" where Ks_Code=@Ks_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Ks_Code", SqlDbType.Char,6)			};
			parameters[0].Value = Ks_Code;

			ModelOld.M_Zd_YyKs model=new ModelOld.M_Zd_YyKs();
			DataSet ds=HisVar.HisVar.Sqldal.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Zd_YyKs DataRowToModel(DataRow row)
		{
			ModelOld.M_Zd_YyKs model=new ModelOld.M_Zd_YyKs();
			if (row != null)
			{
				if(row["Yy_Code"]!=null)
				{
					model.Yy_Code=row["Yy_Code"].ToString();
				}
				if(row["Ks_Code"]!=null)
				{
					model.Ks_Code=row["Ks_Code"].ToString();
				}
				if(row["Ks_Name"]!=null)
				{
					model.Ks_Name=row["Ks_Name"].ToString();
				}
				if(row["Ks_Jc"]!=null)
				{
					model.Ks_Jc=row["Ks_Jc"].ToString();
				}
				if(row["Ks_Fzr"]!=null)
				{
					model.Ks_Fzr=row["Ks_Fzr"].ToString();
				}
				if(row["Ks_Tel"]!=null)
				{
					model.Ks_Tel=row["Ks_Tel"].ToString();
				}
				if(row["Ks_Memo"]!=null)
				{
					model.Ks_Memo=row["Ks_Memo"].ToString();
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select Yy_Code,Ks_Code,Ks_Name,Ks_Jc,Ks_Fzr,Ks_Tel,Ks_Memo ");
			strSql.Append(" FROM Zd_YyKs ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}
        public DataSet Getksname(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select DISTINCT  Ks_Name,Ks_Jc,Ks_Code ");
            strSql.Append(" FROM Zd_YyKs ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by Ks_Name ");
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }
		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" Yy_Code,Ks_Code,Ks_Name,Ks_Jc,Ks_Fzr,Ks_Tel,Ks_Memo ");
			strSql.Append(" FROM Zd_YyKs ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM Zd_YyKs ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = HisVar.HisVar.Sqldal .GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.Ks_Code desc");
			}
			strSql.Append(")AS Row, T.*  from Zd_YyKs T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Zd_YyKs";
			parameters[1].Value = "Ks_Code";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return HisVar.HisVar.Sqldal.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

