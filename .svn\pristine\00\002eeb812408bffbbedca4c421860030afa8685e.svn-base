﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="0" />
    <DataSources isList="true" count="1">
      <收入统计 Ref="2" type="DataTableSource" isKey="true">
        <Alias>收入统计</Alias>
        <Columns isList="true" count="16">
          <value>V_Lb,System.String</value>
          <value>Ks_Code,System.String</value>
          <value>Ys_Code,System.String</value>
          <value>Ks_Name,System.String</value>
          <value>Ys_Name,System.String</value>
          <value>Dl_Code,System.String</value>
          <value>Dl_Name,System.String</value>
          <value>Ry_Name,System.String</value>
          <value>Mz_Date,System.DateTime</value>
          <value>Yp_Name,System.String</value>
          <value>Mx_Gg,System.String</value>
          <value>Mx_XsDw,System.String</value>
          <value>Mz_Dj,System.Decimal</value>
          <value>Mz_Sl,System.Decimal</value>
          <value>Mz_Money,System.Decimal</value>
          <value>Bxlb_Name,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>收入统计</Name>
        <NameInSource>收入统计</NameInSource>
      </收入统计>
    </DataSources>
    <Relations isList="true" count="0" />
    <Report isRef="0" />
    <Variables isList="true" count="1">
      <value>,标题,标题,System.String,,False,False</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="3" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="2">
        <PageHeaderBand1 Ref="4" type="PageHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,38,0.6</ClientRectangle>
          <Components isList="true" count="1">
            <Text1 Ref="5" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,38,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,14.25,Bold,Point,False,0</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text1</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>{标题}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text1>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>PageHeaderBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </PageHeaderBand1>
        <CrossTab1 Ref="6" type="Stimulsoft.Report.CrossTab.StiCrossTab" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,1,38,18</ClientRectangle>
          <Components isList="true" count="11">
            <CrossTab1_RowTotal1 Ref="7" type="CrossRowTotal" isKey="true">
              <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
              <Brush>[255:255:255]</Brush>
              <ClientRectangle>0,2.5,3.4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>c24a24ec945a471f96ced1e91524333b</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>CrossTab1_RowTotal1</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
              <Text>总计</Text>
              <TextBrush>Black</TextBrush>
            </CrossTab1_RowTotal1>
            <CrossTab1_Row1_Title Ref="8" type="CrossTitle" isKey="true">
              <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
              <Brush>White</Brush>
              <ClientRectangle>0,0.65,1.6,0.6</ClientRectangle>
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>ed7d1dce02d84ea0af34c787638c5857</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>CrossTab1_Row1_Title</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
              <Text>查询类别</Text>
              <TextBrush>[105:105:105]</TextBrush>
              <TypeOfComponent>Row:CrossTab1_Row1</TypeOfComponent>
            </CrossTab1_Row1_Title>
            <CrossTab1_RowTotal2 Ref="9" type="CrossRowTotal" isKey="true">
              <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
              <Brush>[255:255:255]</Brush>
              <ClientRectangle>1.6,1.9,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>1c56f3dfefd848a8bce4dfadcff234b6</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>CrossTab1_RowTotal2</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
              <Text>合计</Text>
              <TextBrush>Black</TextBrush>
            </CrossTab1_RowTotal2>
            <CrossTab1_Row2_Title Ref="10" type="CrossTitle" isKey="true">
              <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
              <Brush>White</Brush>
              <ClientRectangle>1.6,0.65,1.8,0.6</ClientRectangle>
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>7ad2f17d99a645ed97b1942b2b84d8bb</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>CrossTab1_Row2_Title</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
              <Text>患者类别</Text>
              <TextBrush>[105:105:105]</TextBrush>
              <TypeOfComponent>Row:CrossTab1_Row2</TypeOfComponent>
            </CrossTab1_Row2_Title>
            <CrossTab1_ColTotal1 Ref="11" type="CrossColumnTotal" isKey="true">
              <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
              <Brush>[255:255:255]</Brush>
              <ClientRectangle>5.05,0.65,1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>94d68f636c6c4f4cb8db40e612a54e91</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>CrossTab1_ColTotal1</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
              <Text>总计</Text>
              <TextBrush>Black</TextBrush>
            </CrossTab1_ColTotal1>
            <CrossTab1_LeftTitle Ref="12" type="CrossTitle" isKey="true">
              <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
              <Brush>White</Brush>
              <ClientRectangle>0,0,3.4,0.6</ClientRectangle>
              <Font>宋体,10.5,Bold</Font>
              <Guid>a187f108784e4ece92c2183ecb70905c</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>CrossTab1_LeftTitle</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
              <Text>患者类别统计</Text>
              <TextBrush>[105:105:105]</TextBrush>
              <TypeOfComponent>LeftTitle</TypeOfComponent>
            </CrossTab1_LeftTitle>
            <CrossTab1_Row1 Ref="13" type="CrossRow" isKey="true">
              <Alias>V_Lb</Alias>
              <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
              <Brush>White</Brush>
              <ClientRectangle>0,1.3,1.6,1.2</ClientRectangle>
              <Conditions isList="true" count="0" />
              <DisplayValue>{收入统计.V_Lb}</DisplayValue>
              <Font>宋体,10.5</Font>
              <Guid>f4cfcb4080834e12a07a96be7fc8ef17</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>CrossTab1_Row1</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
              <Text>V_Lb</Text>
              <TextBrush>[105:105:105]</TextBrush>
              <TotalGuid>c24a24ec945a471f96ced1e91524333b</TotalGuid>
              <Value>{收入统计.V_Lb}</Value>
            </CrossTab1_Row1>
            <CrossTab1_Row2 Ref="14" type="CrossRow" isKey="true">
              <Alias>Bxlb_Name</Alias>
              <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
              <Brush>White</Brush>
              <ClientRectangle>1.6,1.3,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <DisplayValue>{收入统计.Bxlb_Name}</DisplayValue>
              <Font>宋体,10.5</Font>
              <Guid>68fe9208279d4741a09cdad739dff623</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>CrossTab1_Row2</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
              <Text>Bxlb_Name</Text>
              <TextBrush>[105:105:105]</TextBrush>
              <TotalGuid>1c56f3dfefd848a8bce4dfadcff234b6</TotalGuid>
              <Value>{收入统计.Bxlb_Name}</Value>
            </CrossTab1_Row2>
            <CrossTab1_Column1 Ref="15" type="CrossColumn" isKey="true">
              <Alias>Dl_Name</Alias>
              <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
              <Brush>White</Brush>
              <ClientRectangle>3.45,0.65,1.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <DisplayValue>{收入统计.Dl_Name}</DisplayValue>
              <Font>宋体,10.5</Font>
              <Guid>e73f793ed3bb4c1aa04074957c98aef7</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>CrossTab1_Column1</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
              <Text>Dl_Name</Text>
              <TextBrush>[105:105:105]</TextBrush>
              <TotalGuid>94d68f636c6c4f4cb8db40e612a54e91</TotalGuid>
              <Value>{收入统计.Dl_Name}</Value>
            </CrossTab1_Column1>
            <CrossTab1_Sum1 Ref="16" type="CrossSummary" isKey="true">
              <Alias>Mz_Money</Alias>
              <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
              <Brush>[255:255:255]</Brush>
              <ClientRectangle>3.45,1.3,1.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>a15662bdc71d400aa1da72e9d1398324</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>CrossTab1_Sum1</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
              <Text>0</Text>
              <TextBrush>Black</TextBrush>
              <Value>{收入统计.Mz_Money}</Value>
            </CrossTab1_Sum1>
            <CrossTab1_RightTitle Ref="17" type="CrossTitle" isKey="true">
              <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
              <Brush>White</Brush>
              <ClientRectangle>3.45,0,2.6,0.6</ClientRectangle>
              <Font>宋体,10.5,Bold</Font>
              <Guid>32735df05e37456090c3e20974fc8932</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>CrossTab1_RightTitle</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
              <Text>收费类别</Text>
              <TextBrush>[105:105:105]</TextBrush>
              <TypeOfComponent>RightTitle</TypeOfComponent>
            </CrossTab1_RightTitle>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName />
          <DataSourceName>收入统计</DataSourceName>
          <DockStyle>Bottom</DockStyle>
          <EmptyValue />
          <Filters isList="true" count="0" />
          <GrowToHeight>True</GrowToHeight>
          <HorAlignment>Width</HorAlignment>
          <Name>CrossTab1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Sort isList="true" count="0" />
        </CrossTab1>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>ab4a11e050e84831915bf6663408f906</Guid>
      <Margins>1,1,1,1</Margins>
      <Name>Page1</Name>
      <Orientation>Landscape</Orientation>
      <PageHeight>21</PageHeight>
      <PageWidth>40</PageWidth>
      <Report isRef="0" />
      <Watermark Ref="18" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page1>
  </Pages>
  <PrinterSettings Ref="19" type="Stimulsoft.Report.Print.StiPrinterSettings" isKey="true" />
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>患者类别统计</ReportAlias>
  <ReportChanged>3/27/2012 2:12:54 PM</ReportChanged>
  <ReportCreated>12/6/2011 4:42:32 PM</ReportCreated>
  <ReportFile>D:\正在进行时\唐山\his2010\His2010\Rpt\患者类别统计表.mrt</ReportFile>
  <ReportGuid>096942b1ca144d5a9bf7f464baacf51b</ReportGuid>
  <ReportName>患者类别统计</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2011.2.1026</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class Report : Stimulsoft.Report.StiReport
    {
        public Report()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
        #endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>