﻿Imports System.Data.SqlClient
Imports System.Linq
Imports System.Security.Cryptography
Imports BLL
Imports Stimulsoft.Report
Imports HisControl
Imports Model
Imports ZTHisEnum

Public Class Hsz_Syk

#Region "定义__变量"
    Dim My_Dataset As New DataSet
    Dim My_Table As New DataTable
    Public My_Cm As CurrencyManager

#End Region

#Region "传参"
    Dim Rfl As String
    Dim Rrow As DataRow
#End Region

    Public Sub New(ByVal trow As DataRow, ByVal tfl As String)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        Rrow = trow
        Rfl = tfl
        ' 在 InitializeComponent() 调用之后添加任何初始化。
    End Sub
    Private Sub Mz_Czd_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Call Form_Init()
        Call Init_Data()
    End Sub
#Region "窗体__事件"

    Private Sub Form_Init()
        If Rfl = "门诊" Then
            DyButton.Visible = True
        ElseIf Rfl = "住院" Or Rfl = "长期医嘱" Then
            DyButton.Visible = False
            DyButton.Enabled = False
        End If
        With PbSlNumericEdit
            .Value = 1
            .CustomFormat = "###0.#####"
        End With
        KySlNumericEdit.Enabled = False
        With FzComobo
            .Additem = "第01组"
            .Additem = "第02组"
            .Additem = "第03组"
            .Additem = "第04组"
            .Additem = "第05组"
            .Additem = "第06组"
            .Additem = "第07组"
            .Additem = "第08组"
            .Additem = "第09组"
            .Additem = "第10组"
            '.Width = 50
            .DroupDownWidth = 90
            .SelectedIndex = 0
            .DisplayColumns(1).Visible = False
        End With
        Combol_data()
        With YPComobo
            .DataView = My_Dataset.Tables("用药明细").DefaultView
            .Init_Colum("Xx_Code", "简称", 0, "左")
            .Init_Colum("Yp_Jc", "简称", 0, "左")
            .Init_Colum("Yp_Name", "名称", 150, "左")
            .Init_Colum("Mx_Gyzz", "批准文号", 80, "左")
            .Init_Colum("Mx_Gg", "规格", 60, "左")
            .Init_Colum("Mx_Cd", "产地", 90, "左")
            .Init_Colum("Yp_Yxq", "有效期", 60, "中")
            .Init_Colum("Sl", "数量", 40, "右")
            .Init_Colum("Mx_XsDw", "单位", 40, "中")
            .DisplayMember = "Yp_Name"
            .ValueMember = "Xx_Code"
            .RowFilterNotTextNull = "Yp_Jc"
            .DroupDownWidth = 550
            .MaxDropDownItems = 15
            .SelectedIndex = -1
            .RowFilterTextNull = ""
            .Columns("Sl").NumberFormat = "0.######"
        End With

        With MyGrid1
            .Init_Grid()
            .Init_Column("分组名称", "Z_Name", 55, "左", "", False)
            .Init_Column("药品名称", "Yp_Name", 200, "左", "", False)
            .Init_Column("规格", "Mx_Gg", 120, "左", "", False)
            .Init_Column("产地", "Mx_Cd", 120, "左", "", False)
            .Init_Column("有效期", "Yp_Yxq", 70, "中", "yyyy-MM-dd", False)
            .Init_Column("使用数量", "Use_Num", 80, "右", "###0.#####", False)
            .Init_Column("单位", "Mx_Dw", 80, "中", "", False)
            .Init_Column("备注", "Czd_Memo", 80, "左", "", False)
            .Init_Column("标志", "Czd_Id", 0, "左", "", False)
        End With

        MyGrid1.Splits(0).DisplayColumns("Czd_Id").Visible = False
        MyGrid1.Splits(0).DisplayColumns("Z_Name").Merge = C1.Win.C1TrueDBGrid.ColumnMergeEnum.Restricted
        MyGrid1.Splits(0).DisplayColumns("Z_Name").Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
        MyGrid1.Splits(0).DisplayColumns("Z_Name").Style.VerticalAlignment = C1.Win.C1TrueDBGrid.AlignVertEnum.Center
        MyGrid1.Splits(0).MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightCell
    End Sub
    Private Sub Combol_data()
        Dim Str As String = ""
        If Rfl = "门诊" Then
            Str = "Select V_Ypkc.Xx_Code,Yp_Jc,Yp_Name,Mx_Gyzz,Mx_Gg,Mx_Cd,Yp_Yxq,Mz_Sl-isnull(Use_Sl,0) as Sl,Mx_XsDw From V_Ypkc inner join (select Mz_Code,Xx_Code,isnull(Sum(Mz_Sl),0)Mz_sl from Mz_Yp where mz_Code='" & Rrow.Item("Mz_Code") & "' group by Mz_Code,Xx_Code) b on B.Xx_Code=V_YPKC.Xx_Code left join (select Mz_Code,Xx_Code,sum(use_num) as use_sl from  Mz_Czd where  Mz_Czd.Mz_Code='" & Rrow.Item("Mz_Code") & "' group by Mz_Code,Xx_Code)a on a.Mz_Code=B.Mz_CODE AND a.Xx_Code=B.Xx_Code where  ((isnull(Mz_Sl,0)-isnull(Use_Sl,0)>0))   order by Yp_Jc,Yp_Name,Mx_Gyzz,Mx_Gg,Mx_Cd,Yp_Yxq,Use_Sl,Mx_XsDw"
        ElseIf Rfl = "住院" Then
            Str = "Select V_Ypkc.Xx_Code,Yp_Jc,Yp_Name,Mx_Gyzz,Mx_Gg,Mx_Cd,Yp_Yxq,Cf_Sl-isnull(Use_Sl,0) as Sl,Mx_XsDw From V_Ypkc inner join (select Cf_Code,Xx_Code,isnull(Sum(Cf_Sl),0)Cf_sl from Bl_Cfyp where Cf_Code='" & Rrow.Item("Cf_Code") & "' group by Cf_Code,Xx_Code) b on B.Xx_Code=V_YPKC.Xx_Code left join (select Cf_Code,Xx_Code,sum(use_num) as use_sl from  Bl_Czd where  Bl_Czd.Cf_Code='" & Rrow.Item("Cf_Code") & "' group by Cf_Code,Xx_Code)a on a.Cf_Code=B.Cf_CODE AND a.Xx_Code=B.Xx_Code where  ((isnull(Cf_Sl,0)-isnull(Use_Sl,0)>0))   order by Yp_Jc,Yp_Name,Mx_Gyzz,Mx_Gg,Mx_Cd,Yp_Yxq,Use_Sl,Mx_XsDw"
        ElseIf Rfl = "长期医嘱" Then
            Str = "Select V_Ypkc.Xx_Code,Yp_Jc,Yp_Name,Mx_Gyzz,Mx_Gg,Mx_Cd,Yp_Yxq,Cf_Sl-isnull(Use_Sl,0) as Sl,Mx_XsDw From V_Ypkc inner join (select AutoCf_Code,Xx_Code,isnull(Sum(Cf_Sl),0)Cf_sl from Auto_Cfyp where AutoCf_Code='" & Rrow.Item("AutoCf_Code") & "' group by AutoCf_Code,Xx_Code) b on B.Xx_Code=V_YPKC.Xx_Code left join (select AutoCf_Code,Xx_Code,sum(use_num) as use_sl from  Auto_Czd where  Auto_Czd.AutoCf_Code='" & Rrow.Item("AutoCf_Code") & "' group by AutoCf_Code,Xx_Code)a on a.AutoCf_Code=B.AutoCf_CODE AND a.Xx_Code=B.Xx_Code where  ((isnull(Cf_Sl,0)-isnull(Use_Sl,0)>0))   order by Yp_Jc,Yp_Name,Mx_Gyzz,Mx_Gg,Mx_Cd,Yp_Yxq,Use_Sl,Mx_XsDw"
        End If
        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, Str, "用药明细", True)
    End Sub
    Private Sub Init_Data()
        Dim Str As String = ""
        If Rfl = "门诊" Then
            Str = "SELECT Czd_Id,Mz_Code as Cf_Code,Mz_Code as Bl_Code,Z_Name,Xx_Code,Use_Num,Czd_Memo,Yp_Name,Mx_Gg,Mx_Cd,Yp_Yxq,Ys_Name,Ry_Name,Mx_Dw  FROM Mz_Czd where Mz_Code='" & Rrow.Item("Mz_Code") & "' order by z_name"
        ElseIf Rfl = "住院" Then
            Str = "SELECT Czd_Id,Cf_Code,Z_Name,Xx_Code,Use_Num,Czd_Memo,Yp_Name,Mx_Gg,Mx_Cd,Yp_Yxq,Ys_Name,Ry_Name,Bc_Name,Bl_Code,Ry_Blcode,Mx_Dw  FROM Bl_Czd where Cf_Code='" & Rrow.Item("Cf_Code") & "' order by z_name"
        ElseIf Rfl = "长期医嘱" Then
            Str = "SELECT Czd_Id,AutoCf_Code,Z_Name,Xx_Code,Use_Num,Czd_Memo,Yp_Name,Mx_Gg,Mx_Cd,Yp_Yxq,Ys_Name,Ry_Name,Bc_Name,Bl_Code,Ry_Blcode,Mx_Dw  FROM Auto_Czd where AutoCf_Code='" & Rrow.Item("AutoCf_Code") & "' order by z_name"
        End If
        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, Str, "分组", True)
        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, Str, "分组1", True)

        Dim My_Column As DataColumn = My_Dataset.Tables("分组").Columns("Czd_Id")
        With My_Column
            .AutoIncrement = True
            .AutoIncrementSeed = .AutoIncrementSeed
            .AutoIncrementStep = 1
        End With

        With MyGrid1
            My_Cm = CType(BindingContext(My_Dataset, "分组"), CurrencyManager)
            .DataTable = My_Dataset.Tables("分组")

        End With
        My_Table = My_Dataset.Tables("分组")

    End Sub
#End Region

#Region "控件__动作"
    Private Sub OKButton_Click() Handles OKButton.Click
        If YPComobo.Text = "" Then
            MsgBox("请选择要加到处置单上的药品！", MsgBoxStyle.Information, "提示:")
            YPComobo.Select()
            Exit Sub
        End If
        If PbSlNumericEdit.Value <= 0 Or KySlNumericEdit.Text < PbSlNumericEdit.Value Then
            MsgBox("配比数有误！", MsgBoxStyle.Information, "提示:")
            PbSlNumericEdit.Select()
            Exit Sub
        End If
        Dim cou As Integer
        If Rfl = "门诊" Then
            cou = HisVar.HisVar.Sqldal.GetSingle("select count(*) from mz_czd where mz_code='" & Rrow.Item("Mz_Code") & "' and z_name='" & FzComobo.Text & "' and xx_code='" & YPComobo.SelectedValue & "'")
        ElseIf Rfl = "住院" Then
            cou = HisVar.HisVar.Sqldal.GetSingle("select count(*) from bl_czd where cf_code='" & Rrow.Item("Cf_Code") & "' and z_name='" & FzComobo.Text & "' and xx_code='" & YPComobo.SelectedValue & "'")
        ElseIf Rfl = "长期医嘱" Then
            cou = HisVar.HisVar.Sqldal.GetSingle("select count(*) from Auto_Czd where AutoCf_code='" & Rrow.Item("AutoCf_code") & "' and z_name='" & FzComobo.Text & "' and xx_code='" & YPComobo.SelectedValue & "'")
        End If
        If cou > 0 Then
            MsgBox("该组已经分配了该药，不能添加相同药品，可删除之前数据重新添加！", MsgBoxStyle.Information, "提示:")
            YPComobo.Select()
            Exit Sub
        End If
        Call Save_Add()
    End Sub


    Private Sub DyButton_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles DyButton.Click
        Dim Mytable As DataTable = My_Dataset.Tables("分组").DefaultView.ToTable("分组1")

        Dim StiRpt As New StiReport
        'StiRpt.Load("Rpt\输液卡（连打）.mrt")
        'StiRpt.ReportName = "输液卡"
        If HisPara.PublicConfig.XqName.Contains("丰南") Then
            StiRpt.Load("Rpt\输液卡（丰南连打）.mrt")  '丰南要求每个患者的每个组单独打印
        Else
            StiRpt.Load("Rpt\输液卡（连打）.mrt")
        End If
        StiRpt.ReportName = "输液卡"
        'Dim I As Integer
        'Dim M As Integer = Mytable.Rows.Count - 1
        'Dim K As Integer = 0
        'Dim Str1 As String
        'Dim Row1 As DataRow
        'Dim Row2 As DataRow
        'Dim V_NewRow As DataRow
        'For I = 0 To M
        '    Row1 = Mytable.Rows.Item(I)
        '    If I = M Then
        '        Str1 = True
        '    Else
        '        Row2 = Mytable.Rows.Item(I + 1)
        '        Str1 = Row1.Item("z_name") <> Row2.Item("z_name")
        '    End If

        '    If Str1 Then
        '        If Mytable.Select("z_name='" & Row1.Item("z_name") & "'").Length Mod 10 <> 0 Then

        '            For V_TbRowCount = 1 To 10 - (Mytable.Select("z_name='" & Row1.Item("z_name") & "'").Length Mod 10)
        '                V_NewRow = Mytable.NewRow
        '                With V_NewRow

        '                    If Rfl = "门诊" Then
        '                        .Item("Bl_Code") = Rrow.Item("Mz_Code")
        '                        .Item("Cf_Code") = Rrow.Item("Mz_Code")
        '                    ElseIf Rfl = "住院" Then
        '                        .Item("Cf_Code") = Rrow.Item("Cf_Code")
        '                        .Item("Bc_Name") = Rrow.Item("Bc_Name")
        '                        .Item("Bl_Code") = Rrow.Item("Bl_Code")
        '                        .Item("Ry_Blcode") = Rrow.Item("Ry_BlCode")
        '                    End If
        '                    .Item("Z_Name") = Row1.Item("Z_Name")
        '                    .Item("Xx_Code") = DBNull.Value
        '                    .Item("Use_Num") = DBNull.Value
        '                    .Item("Czd_Memo") = DBNull.Value
        '                    .Item("Yp_Name") = DBNull.Value
        '                    .Item("Mx_Gg") = DBNull.Value
        '                    .Item("Yp_Yxq") = DBNull.Value
        '                    .Item("Mx_Dw") = DBNull.Value
        '                End With
        '                Mytable.Rows.Add(V_NewRow)
        '                V_NewRow.AcceptChanges()
        '            Next
        '        End If
        '        'Q = Q + 1
        '    End If

        'Next
        'Mytable.DefaultView.Sort = "z_name"

        StiRpt.RegData(Mytable)
        StiRpt.Pages(0).PaperSize = Printing.PaperKind.Custom
        StiRpt.Pages(0).PageHeight = 27.9
        StiRpt.Compile()
        StiRpt("医嘱医生") = Rrow.Item("Ys_Name")
        StiRpt("患者姓名") = Rrow.Item("Ry_Name")
        If Rfl = "门诊" Then
            StiRpt("打印时间") = Format(Now, "yyyy年MM月dd日")
        End If

        'StiRpt.Design()
        StiRpt.Show()


    End Sub

    Private Sub DelButton_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles DelButton.Click
        Dim My_Row As DataRow = My_Cm.List(MyGrid1.Row).Row
        If MsgBox("确认删除" + MyGrid1.Columns("Z_Name").Value + " 的" + MyGrid1.Columns("Yp_Name").Value + "吗？", MsgBoxStyle.Information + MsgBoxStyle.OkCancel, "提示:") = MsgBoxResult.Cancel Then Exit Sub

        If Rfl = "门诊" Then
            DeleteInfusion(My_Row.Item("Xx_Code"), My_Row.Item("Z_Name"))
            HisVar.HisVar.Sqldal.ExecuteSql("Delete From Mz_Czd Where Mz_Code='" & Rrow.Item("Mz_Code") & "' And Czd_Id='" & My_Row.Item("Czd_Id") & "'")
        ElseIf Rfl = "住院" Then
            HisVar.HisVar.Sqldal.ExecuteSql("Delete From Bl_Czd Where Cf_Code='" & Rrow.Item("Cf_Code") & "' And Czd_Id='" & My_Row.Item("Czd_Id") & "'")
        ElseIf Rfl = "长期医嘱" Then
            HisVar.HisVar.Sqldal.ExecuteSql("Delete From Auto_Czd Where AutoCf_Code='" & Rrow.Item("AutoCf_Code") & "' And Czd_Id='" & My_Row.Item("Czd_Id") & "'")
        End If
        MyGrid1.Delete()
        My_Row.AcceptChanges()
        Call Combol_data()
        Call Init_Data()

    End Sub

    Private Sub C1TrueDBGrid1_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles MyGrid1.KeyDown
        Dim My_Row As DataRow = My_Cm.List(MyGrid1.Row).Row
        Select Case e.KeyCode
            Case Keys.Delete
                If MsgBox("确认删除" + MyGrid1.Columns("Z_Name").Value + " 的" + MyGrid1.Columns("Yp_Name").Value + "吗？", MsgBoxStyle.Information + MsgBoxStyle.OkCancel, "提示:") = MsgBoxResult.Cancel Then Exit Sub
                If Rfl = "门诊" Then
                    DeleteInfusion(My_Row.Item("Xx_Code"), My_Row.Item("Z_Name"))
                    HisVar.HisVar.Sqldal.ExecuteSql("Delete From Mz_Czd Where Mz_Code='" & Rrow.Item("Mz_Code") & "' And Czd_Id='" & My_Row.Item("Czd_Id") & "'")
                ElseIf Rfl = "住院" Then
                    HisVar.HisVar.Sqldal.ExecuteSql("Delete From Bl_Czd Where Cf_Code='" & Rrow.Item("Cf_Code") & "' And Czd_Id='" & My_Row.Item("Czd_Id") & "'")
                ElseIf Rfl = "长期医嘱" Then
                    HisVar.HisVar.Sqldal.ExecuteSql("Delete From Auto_Czd Where AutoCf_Code='" & Rrow.Item("AutoCf_Code") & "' And Czd_Id='" & My_Row.Item("Czd_Id") & "'")
                End If
                MyGrid1.Delete()
                My_Row.AcceptChanges()
        End Select

        Call Combol_data()
    End Sub

    Private Sub YpJcComobo1_RowChange(ByVal sender As Object, ByVal e As System.EventArgs) Handles YPComobo.RowChange
        If YPComobo.WillChangeToValue = "" Then
            KySlNumericEdit.Value = ""
        Else
            KySlNumericEdit.Value = Format(YPComobo.Columns("Sl").Value, "0.0")
        End If
    End Sub

#End Region

#Region "数据__编辑"

    Private Sub Data_Clear()
        YPComobo.SelectedValue = -1
        PbSlNumericEdit.Value = 1
        MemoTextBox.Text = ""
        YPComobo.Select()
    End Sub

    Private Sub Save_Add()
        Dim My_NewRow As DataRow = My_Table.NewRow
        Try
            With My_NewRow
                If Rfl = "门诊" Then
                    .Item("Bl_Code") = Rrow.Item("Mz_Code")
                    .Item("Cf_Code") = Rrow.Item("Mz_Code")
                ElseIf Rfl = "住院" Then
                    .Item("Cf_Code") = Rrow.Item("Cf_Code")
                    .Item("Bc_Name") = HisVar.HisVar.Sqldal.GetSingle("select Bc_Name from bl,zd_yybc where bl.bc_code=zd_yybc.bc_code and bl_code='" & Rrow.Item("Bl_Code") & "'")
                    .Item("Bl_Code") = Rrow.Item("Bl_Code")
                    .Item("Ry_Blcode") = HisVar.HisVar.Sqldal.GetSingle("select Ry_Blcode from bl where bl_code='" & Rrow.Item("Bl_Code") & "'")
                ElseIf Rfl = "长期医嘱" Then
                    .Item("AutoCf_Code") = Rrow.Item("AutoCf_Code")
                    .Item("Bc_Name") = HisVar.HisVar.Sqldal.GetSingle("select Bc_Name from bl,zd_yybc where bl.bc_code=zd_yybc.bc_code and bl_code='" & Rrow.Item("Bl_Code") & "'")
                    .Item("Bl_Code") = Rrow.Item("Bl_Code")
                    .Item("Ry_Blcode") = HisVar.HisVar.Sqldal.GetSingle("select Ry_Blcode from bl where bl_code='" & Rrow.Item("Bl_Code") & "'")
                End If
                .Item("Ys_Name") = Rrow.Item("Ys_Name")
                .Item("Ry_Name") = Rrow.Item("Ry_Name")
                .Item("Z_Name") = FzComobo.Text
                .Item("Xx_Code") = YPComobo.SelectedValue
                .Item("Use_Num") = PbSlNumericEdit.Value
                .Item("Czd_Memo") = MemoTextBox.Text
                .Item("Yp_Name") = YPComobo.Columns("Yp_Name").Value
                .Item("Mx_Gg") = YPComobo.Columns("Mx_Gg").Value
                .Item("Mx_Cd") = YPComobo.Columns("Mx_Cd").Value
                .Item("Yp_Yxq") = YPComobo.Columns("Yp_Yxq").Value
                .Item("Mx_Dw") = YPComobo.Columns("Mx_Xsdw").Value
            End With

            If Rfl = "门诊" Then
                HisVar.HisVar.Sqldal.ExecuteSql("Insert into Mz_Czd (Mz_Code,Z_Name,Xx_Code,Use_Num,Czd_Memo,Yp_Name,Mx_Gg,Mx_Cd,Yp_Yxq,Ys_Name,Ry_Name,Mx_Dw) Values('" & My_NewRow.Item("Bl_Code") & "','" & My_NewRow.Item("Z_Name") & "','" & My_NewRow.Item("Xx_Code") & "','" & My_NewRow.Item("Use_Num") & "','" & My_NewRow.Item("Czd_Memo") & "','" & My_NewRow.Item("Yp_Name") & "','" & My_NewRow.Item("Mx_Gg") & "','" & My_NewRow.Item("Mx_Cd") & "','" & My_NewRow.Item("Yp_Yxq") & "','" & My_NewRow.Item("Ys_Name") & "','" & My_NewRow.Item("Ry_Name") & "','" & My_NewRow.Item("Mx_Dw") & "')")
                AddInfusion(FzComobo.Text)
            ElseIf Rfl = "住院" Then
                HisVar.HisVar.Sqldal.ExecuteSql("Insert into Bl_Czd (Cf_Code,Z_Name,Xx_Code,Use_Num,Czd_Memo,Yp_Name,Mx_Gg,Mx_Cd,Yp_Yxq,Ys_Name,Ry_Name,Bc_Name,Bl_Code,Ry_Blcode,Mx_Dw) Values('" & My_NewRow.Item("Cf_Code") & "','" & My_NewRow.Item("Z_Name") & "','" & My_NewRow.Item("Xx_Code") & "','" & My_NewRow.Item("Use_Num") & "','" & My_NewRow.Item("Czd_Memo") & "','" & My_NewRow.Item("Yp_Name") & "','" & My_NewRow.Item("Mx_Gg") & "','" & My_NewRow.Item("Mx_Cd") & "','" & My_NewRow.Item("Yp_Yxq") & "','" & My_NewRow.Item("Ys_Name") & "','" & My_NewRow.Item("Ry_Name") & "','" & My_NewRow.Item("Bc_Name") & "','" & My_NewRow.Item("Bl_Code") & "','" & My_NewRow.Item("Ry_BlCode") & "','" & My_NewRow.Item("Mx_Dw") & "')")
            ElseIf Rfl = "长期医嘱" Then
                Dim a As String = "Insert into Auto_Czd (AutoCf_Code,Z_Name,Xx_Code,Use_Num,Czd_Memo,Yp_Name,Mx_Gg,Mx_Cd,Yp_Yxq,Ys_Name,Ry_Name,Bc_Name,Bl_Code,Ry_Blcode,Mx_Dw) Values('" & My_NewRow.Item("AutoCf_Code") & "','" & My_NewRow.Item("Z_Name") & "','" & My_NewRow.Item("Xx_Code") & "','" & My_NewRow.Item("Use_Num") & "','" & My_NewRow.Item("Czd_Memo") & "','" & My_NewRow.Item("Yp_Name") & "','" & My_NewRow.Item("Mx_Gg") & "','" & My_NewRow.Item("Mx_Cd") & "','" & My_NewRow.Item("Yp_Yxq") & "','" & My_NewRow.Item("Ys_Name") & "','" & My_NewRow.Item("Ry_Name") & "','" & My_NewRow.Item("Bc_Name") & "','" & My_NewRow.Item("Bl_Code") & "','" & My_NewRow.Item("Ry_BlCode") & "','" & My_NewRow.Item("Mx_Dw") & "')"
                HisVar.HisVar.Sqldal.ExecuteSql("Insert into Auto_Czd (AutoCf_Code,Z_Name,Xx_Code,Use_Num,Czd_Memo,Yp_Name,Mx_Gg,Mx_Cd,Yp_Yxq,Ys_Name,Ry_Name,Bc_Name,Bl_Code,Ry_Blcode,Mx_Dw) Values('" & My_NewRow.Item("AutoCf_Code") & "','" & My_NewRow.Item("Z_Name") & "','" & My_NewRow.Item("Xx_Code") & "','" & My_NewRow.Item("Use_Num") & "','" & My_NewRow.Item("Czd_Memo") & "','" & My_NewRow.Item("Yp_Name") & "','" & My_NewRow.Item("Mx_Gg") & "','" & My_NewRow.Item("Mx_Cd") & "','" & My_NewRow.Item("Yp_Yxq") & "','" & My_NewRow.Item("Ys_Name") & "','" & My_NewRow.Item("Ry_Name") & "','" & My_NewRow.Item("Bc_Name") & "','" & My_NewRow.Item("Bl_Code") & "','" & My_NewRow.Item("Ry_BlCode") & "','" & My_NewRow.Item("Mx_Dw") & "')")
            End If
            My_Table.Rows.Add(My_NewRow)
            My_NewRow.AcceptChanges()
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            Exit Sub
        End Try
        Call Combol_data()
        Call Data_Clear()
        Call Init_Data()
    End Sub

    Private Sub AddInfusion(ByVal Group_No As String)

        Dim _bllMzInfusion = New BllMz_Infusion()
        Dim _mdlMzInfusion = New MdlMz_Infusion()
        If Not _bllMzInfusion.Exists(Rrow.Item("Mz_Code")) Then
            _mdlMzInfusion.Mz_Code = Rrow.Item("Mz_Code")
            _mdlMzInfusion.Status = "待接诊"
            _bllMzInfusion.Add(_mdlMzInfusion)
        End If
        Dim _bllMzInfusionGroup1 As New BLL.BllMz_InfusionGroup1
        Dim _mdlMzInfusionGroup1 As New MdlMz_InfusionGroup1
        Group_No = Group_No.Replace("第", "").Replace("组", "")
        If _bllMzInfusionGroup1.GetRecordCount($"Mz_Code='{Rrow.Item("Mz_Code")}' And Group_No={Group_No}") = 0 Then
            _mdlMzInfusionGroup1.Infusion_Code = _bllMzInfusionGroup1.MaxCode()
            _mdlMzInfusionGroup1.Group_No = Group_No
            _mdlMzInfusionGroup1.Mz_Code = Rrow.Item("Mz_Code")
            _mdlMzInfusionGroup1.SkinTest = ""
            _mdlMzInfusionGroup1.Begin_Time = Nothing
            _mdlMzInfusionGroup1.End_Time = Nothing
            _mdlMzInfusionGroup1.DropCoefficient = 0
            _mdlMzInfusionGroup1.TotalLiquid = 0
            _mdlMzInfusionGroup1.DropSpeed = 0
            _mdlMzInfusionGroup1.Duration = 0
            _mdlMzInfusionGroup1.Infusion_Memo = ""
            _mdlMzInfusionGroup1.Status = InfusionStatus.正在配药.ToString()
            _mdlMzInfusionGroup1.Py_Jsr_Code = ZTHisVar.Var.JsrCode
            _bllMzInfusionGroup1.Add(_mdlMzInfusionGroup1)
        Else
            _mdlMzInfusionGroup1 = _bllMzInfusionGroup1.GetModelList($"Mz_Code='{Rrow.Item("Mz_Code")}' And Group_No={Group_No}").First()
        End If
        Dim bllMz_Yp = New BllMz_Yp
        Dim ypdt = New DataTable
        ypdt = bllMz_Yp.GetList($"Mz_Yp.Mz_Code='{Rrow.Item("Mz_Code")}' And Mz_Yp.Xx_Code='{YPComobo.SelectedValue}'").Tables(0)
        Dim UseSl As Decimal = PbSlNumericEdit.Value
        Dim _bllMzInfusionGroup2 As New BLL.BllMz_InfusionGroup2
        For Each row As DataRow In ypdt.Rows
            Dim _mdlMzInfusionGroup2 As New MdlMz_InfusionGroup2
            If UseSl <= row("Mz_Sl") Then
                _mdlMzInfusionGroup2.Infusion_Code = _mdlMzInfusionGroup1.Infusion_Code
                _mdlMzInfusionGroup2.Mz_Id = row("Mz_Id")
                _mdlMzInfusionGroup2.Xx_Code = YPComobo.SelectedValue
                _mdlMzInfusionGroup2.Mz_Code = Rrow.Item("Mz_Code")
                _mdlMzInfusionGroup2.Use_Num = UseSl
                _mdlMzInfusionGroup2.Yp_Yfyl = row("Yp_Yfyl") + ""
                _bllMzInfusionGroup2.Add(_mdlMzInfusionGroup2)
                Exit For
            Else
                _mdlMzInfusionGroup2.Infusion_Code = _mdlMzInfusionGroup1.Infusion_Code
                _mdlMzInfusionGroup2.Mz_Id = row("Mz_Id")
                _mdlMzInfusionGroup2.Xx_Code = YPComobo.SelectedValue
                _mdlMzInfusionGroup2.Mz_Code = Rrow.Item("Mz_Code")
                _mdlMzInfusionGroup2.Use_Num = row("Mz_Sl")
                _mdlMzInfusionGroup2.Yp_Yfyl = row("Yp_Yfyl") + ""
                _bllMzInfusionGroup2.Add(_mdlMzInfusionGroup2)
                UseSl = UseSl - row("Mz_Sl")
            End If
        Next
    End Sub

    Private Sub DeleteInfusion(ByVal Xx_Code As String, ByVal Group_No As String)
        Group_No = Group_No.Replace("第", "").Replace("组", "")
        Dim _bllMzInfusionGroup2 As New BLL.BllMz_InfusionGroup2
        _bllMzInfusionGroup2.Delete(Rrow.Item("Mz_Code"), Xx_Code, Group_No)
    End Sub
#End Region


End Class