﻿Imports System.Data.SqlClient


Public Class Zd_Dict1

#Region "变量初始化"
    Dim My_Dataset As New DataSet
    Public My_Cm As CurrencyManager             '同步指针
    Public My_Row As DataRow                    '当 前 行
    Public V_Insert As Boolean                  '增加记录
    Dim Form_Lb As String
#End Region

    Public Sub New(ByVal tFormLb As String)
        ' 此调用是设计器所必需的。
        InitializeComponent()
        Form_Lb = tFormLb
        ' 在 InitializeComponent() 调用之后添加任何初始化。
    End Sub

    Private Sub Zd_BlLb1_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Call Form_Init()
        Call Init_Data()
    End Sub

#Region "窗体初始化"

    Private Sub Form_Init()
        '初始化TDBGrid
        Me.Text = Form_Lb

        Comm1.Visible = IIf(Form_Lb <> "药房字典", True, False)
        Comm2.Visible = IIf(Form_Lb <> "药房字典", True, False)
        Comm4.Visible = IIf(Form_Lb = "药房字典", True, False)
        C1Command1.Visible = IIf(Form_Lb = "销售客户", True, False)

        Panel1.BorderStyle = BorderStyle.None
        Panel1.Height = 25
        ToolBar1.Location = New Point(1, 1)
        T_Line2.Location = New Point(ToolBar1.Width + 2, 0)
        T_Label.Location = New Point(T_Line2.Left + 2, 2)

        Dim My_Grid As New BaseClass.C_Grid(Me.C1TrueDBGrid1)
        With My_Grid
            .Init_Grid()
            .P_Dock(BaseClass.C_Grid.Dock.Fill)
            If Form_Lb = "病例类别字典" Then
                .Init_Column("编码", "Bxlb_Code", 57, "中", "")
                .Init_Column("名称", "Bxlb_Name", 90, "左", "")
                .Init_Column("简称", "Bxlb_Jc", 90, "左", "")
                .Init_Column("备注", "Bxlb_Memo", 150, "左", "")
            ElseIf Form_Lb = "病区字典" Then
                .Init_Column("编码", "Bq_Code", 65, "中", "")
                .Init_Column("名称", "Bq_Name", 110, "左", "")
                .Init_Column("简称", "Bq_Jc", 90, "左", "")
                .Init_Column("备注", "Bq_Memo", 220, "左", "")
                .Init_Column("病床分配", "", 1, "中", "")
                With C1TrueDBGrid1.Splits(0).DisplayColumns(4)
                    .ButtonAlways = True
                    .Button = True
                    .ButtonText = True
                End With
            ElseIf Form_Lb = "床位字典" Then
                .Init_Column("编码", "Bc_Code", 57, "中", "")
                .Init_Column("名称", "Bc_Name", 120, "左", "")
                .Init_Column("简称", "Bc_Jc", 120, "左", "")
                .Init_Column("备注", "Bc_Memo", 100, "左", "")
            ElseIf Form_Lb = "科室字典" Then
                .Init_Column("编码", "Ks_Code", 57, "中", "")
                .Init_Column("科室名称", "Ks_Name", 130, "左", "")
                .Init_Column("科室简称", "Ks_Jc", 80, "左", "")
                .Init_Column("负责人", "Ks_Fzr", 90, "左", "")
                .Init_Column("电话", "Ks_Tel", 150, "左", "")
                .Init_Column("关联诊疗项目", "", 10, "中", "")
                With C1TrueDBGrid1.Splits(0).DisplayColumns(5)
                    .ButtonAlways = True
                    .Button = True
                    .ButtonText = True
                End With
            ElseIf Form_Lb = "领导字典" Then
                .Init_Column("编码", "Ld_Code", 57, "中", "")
                .Init_Column("领导姓名", "Ld_Name", 100, "左", "")
                .Init_Column("简称", "Ld_Jc", 80, "左", "")
                .Init_Column("职位", "Ld_Zw", 150, "左", "")
                .Init_Column("电话", "Ld_Tel", 150, "左", "")
            ElseIf Form_Lb = "医生字典" Then
                .Init_Column("编码", "Ys_Code", 60, "中", "")
                .Init_Column("医生姓名", "Ys_Name", 80, "左", "")
                .Init_Column("医生简称", "Ys_Jc", 65, "左", "")
                .Init_Column("性别", "Ys_Sex", 45, "中", "")
                .Init_Column("科室名称", "Ks_Name", 90, "左", "")
                .Init_Column("是否使用", "Ys_Use", 60, "中", "")
                .Init_Column("备注", "Ys_Memo", 10, "左", "")
            ElseIf Form_Lb = "药品供应商" Then
                .Init_Column("编码", "Kh_Code", 57, "中", "")
                .Init_Column("客户姓名", "Kh_Name", 250, "左", "")
                .Init_Column("客户简称", "Kh_Jc", 100, "左", "")
                .Init_Column("负责人", "Kh_Fzr", 100, "左", "")
                .Init_Column("是否使用", "Kh_Use", 20, "中", "")
            ElseIf Form_Lb = "销售客户" Then
                .Init_Column("编码", "Kh_Code", 57, "中", "")
                .Init_Column("客户名称", "Kh_Name", 150, "左", "")
                .Init_Column("客户简称", "Kh_Jc", 60, "左", "")
                .Init_Column("负责人", "Kh_Fzr", 70, "左", "")
                .Init_Column("电话", "Kh_Tel", 100, "左", "")
                .Init_Column("是否使用", "Kh_Use", 60, "中", "")
                .Init_Column("农合编码", "Hzyl_Zs_Code", 20, "中", "")
            ElseIf Form_Lb = "剂型字典" Then
                .Init_Column("编码", "Jx_Code", 57, "中", "")
                .Init_Column("剂型名称", "Jx_Name", 120, "左", "")
                .Init_Column("剂型简称", "Jx_Jc", 120, "左", "")
                .Init_Column("说明", "Jx_Memo", 90, "左", "")
            ElseIf Form_Lb = "功效字典" Then
                .Init_Column("编码", "Gx_Code", 57, "中", "")
                .Init_Column("功效名称", "Gx_Name", 150, "左", "")
                .Init_Column("功效简称", "Gx_Jc", 150, "左", "")
                .Init_Column("说明", "Gx_Memo", 90, "左", "")
            ElseIf Form_Lb = "药房字典" Then
                .Init_Column("编码", "Yf_Code", 57, "中", "")
                .Init_Column("药房名称", "Yf_Name", 120, "左", "")
                .Init_Column("药房简称", "Yf_Jc", 120, "左", "")
                .Init_Column("使用", "Yf_Use", 50, "中", "")
                .Init_Column("说明", "Yf_Memo", 90, "左", "")
                With C1TrueDBGrid1
                    .AllowUpdate = True
                    .Splits(0).DisplayColumns(0).Locked = True
                    .Splits(0).DisplayColumns(1).Locked = True
                    .Splits(0).DisplayColumns(2).Locked = True
                    .Splits(0).DisplayColumns(4).Locked = True
                End With

                With C1TrueDBGrid1.Columns(3).ValueItems
                    .Presentation = C1.Win.C1TrueDBGrid.PresentationEnum.CheckBox
                End With
            ElseIf Form_Lb = "诊疗模板" Then
                .Init_Column("编码", "Templet_Code", 90, "中", "")
                .Init_Column("模板名称", "Templet_Name", 210, "左", "")
                .Init_Column("模板简称", "Templet_Jc", 100, "左", "")
                .Init_Column("模板单价", "Templet_Dj", 80, "右", "")
                .Init_Column("诊疗模板明细", "", 1, "中", "")
                With C1TrueDBGrid1.Splits(0).DisplayColumns(4)
                    .ButtonAlways = True
                    .Button = True
                    .ButtonText = True
                End With
            ElseIf Form_Lb = "住院票据分类" Then
                .Init_Column("编码", "Lb_Code", 70, "中", "")
                .Init_Column("票据类别名称", "Lb_Name", 100, "中", "")
                .Init_Column("票据类别简称", "Lb_Jc", 100, "中", "")
                .Init_Column("备注", "Lb_Memo", 210, "左", "")
                .Init_Column("诊疗项目归类", "", 1, "中", "")
                With C1TrueDBGrid1.Splits(0).DisplayColumns(4)
                    .ButtonAlways = True
                    .Button = True
                    .ButtonText = True
                End With
            ElseIf Form_Lb = "门诊票据分类" Then
                .Init_Column("编码", "Lb_Code", 70, "中", "")
                .Init_Column("票据类别名称", "Lb_Name", 100, "中", "")
                .Init_Column("票据类别简称", "Lb_Jc", 100, "中", "")
                .Init_Column("备注", "Lb_Memo", 210, "左", "")
                .Init_Column("诊疗项目归类", "", 1, "中", "")
                With C1TrueDBGrid1.Splits(0).DisplayColumns(4)
                    .ButtonAlways = True
                    .Button = True
                    .ButtonText = True
                End With
            ElseIf Form_Lb = "就诊磁卡" Then
                .Init_Column("姓名", "Ry_Name", 60, "左", "")
                .Init_Column("简称", "Ry_Jc", 0, "左", "")
                .Init_Column("性别", "Ry_Sex", 40, "中", "")
                .Init_Column("出生日期", "Ry_CsDate", 80, "中", "yyyy-MM-dd")
                .Init_Column("身份证号", "Ry_Sfzh", 140, "中", "")
                .Init_Column("卡号", "Kh", 150, "左", "")
                .Init_Column("联系电话", "Ry_Tele", 110, "左", "")
                .Init_Column("地址", "Ry_Address", 100, "左", "")
                .Init_Column("备注", "Ry_Memo", 0, "左", "")
                C1TrueDBGrid1.Splits(0).DisplayColumns(1).Visible = False
                C1TrueDBGrid1.Splits(0).DisplayColumns(8).Visible = False
            ElseIf Form_Lb = "挂号媒体字典" Then
                .Init_Column("编码", "Ghmt_Code", 57, "中", "")
                .Init_Column("媒体名称", "Ghmt_Name", 150, "左", "")
                .Init_Column("媒体简称", "Ghmt_Jc", 150, "左", "")
                .Init_Column("说明", "Ghmt_Memo", 90, "左", "")
            End If
        End With
    End Sub

    Private Sub Init_Data()
        If Form_Lb = "病例类别字典" Then
            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "Select Bxlb_Code,Bxlb_Name,Bxlb_Jc,Bxlb_Memo,Yy_Code From Zd_Bxlb where Yy_Code = '" & HisVar.HisVar.WsyCode & "' Order By Bxlb_Code", "主表", True)
        ElseIf Form_Lb = "病区字典" Then
            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "select Bq_Code,Bq_Name,Bq_Jc,Bq_Memo,Yy_Code from Zd_YyBq1 where Yy_Code = '" & HisVar.HisVar.WsyCode & "' Order By Bq_Code", "主表", True)
        ElseIf Form_Lb = "床位字典" Then
            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "select Bc_Code,Bc_Name,Bc_Jc,Bc_Memo,Yy_Code from Zd_YyBc where Yy_Code = '" & HisVar.HisVar.WsyCode & "' Order By Bc_Code", "主表", True)
        ElseIf Form_Lb = "科室字典" Then
            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "Select Ks_Code,Ks_Name,Ks_Jc,Ks_Fzr,Ks_Tel,Ks_Memo,Yy_Code from Zd_YyKs where Yy_Code = '" & HisVar.HisVar.WsyCode & "' order by Ks_Code", "主表", True)
        ElseIf Form_Lb = "领导字典" Then
            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "Select Ld_Code,Ld_Name,Ld_Jc,Ld_Zw,Ld_Tel,Ld_Memo,Yy_Code from Zd_YyLd where Yy_Code = '" & HisVar.HisVar.WsyCode & "' order by Ld_Code", "主表", True)
        ElseIf Form_Lb = "医生字典" Then
            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "Select Ys_Code,Ys_Name,Ys_Jc,Ys_Sex,Ys_Memo,Ys_Use,Zd_YyYs.Ks_Code,Zd_YyYs.Yy_Code,Zd_YyKs.Ks_Name From Zd_YyYs,Zd_YyKs where Zd_YyYs.Ks_Code=Zd_YyKs.Ks_Code and Zd_YyYs.Yy_Code = '" & HisVar.HisVar.WsyCode & "' Order By Ys_Code", "主表", True)
        ElseIf Form_Lb = "药品供应商" Then
            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "select Kh_Code,Kh_Name,Kh_Jc,Kh_Fzr,Kh_Address,Kh_Tel,Kh_Fax,Kh_FrDb,Kh_Bank,Kh_BankCode,Kh_Tax,Kh_Memo,Kh_Use,Kh_PostCode,Yy_Code from Zd_Kh_Rk where Yy_Code='" & HisVar.HisVar.WsyCode & "' order by Kh_Code", "主表", True)
        ElseIf Form_Lb = "销售客户" Then
            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "Select Kh_Code,Kh_Name,Kh_Jc,Kh_Fzr,Kh_Tel,Kh_Use,Kh_Memo,Yy_Code,Hzyl_Zs_Code from Zd_Kh_Xs  order by Kh_Code", "主表", True)
        ElseIf Form_Lb = "剂型字典" Then
            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "select Jx_Code,Jx_Name,Jx_Jc,Jx_Memo from Zd_Ml_Ypjx order by Jx_Code", "主表", True)
        ElseIf Form_Lb = "功效字典" Then
            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "select Gx_Code,Gx_Name,Gx_Jc,Gx_Memo from Zd_Ml_YpGx order by Gx_Code", "主表", True)
        ElseIf Form_Lb = "药房字典" Then
            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "select Yy_Code,Yf_Code,Yf_Name,Yf_Jc,Yf_Memo,Yf_Use from Zd_YyYf Where Yy_Code='" & HisVar.HisVar.WsyCode & "' and (Yf_code='" & HisVar.HisVar.YfCode & "' or exists (select * from zd_qx2 where Module_code in ('9901','9902','9905','9907') and Glz_Code='" & HisVar.HisVar.GlzCode & "') ) order by Yf_Code", "主表", True)
        ElseIf Form_Lb = "诊疗模板" Then
            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "Select Zd_Templet1.Templet_Code,Templet_Name,Templet_Jc,Templet_Memo,Zd_Templet1.Yy_Code,sum(Xm_dj*Mx_sl) as Templet_Dj From Zd_Templet1 left join Zd_Templet2 On  Zd_Templet1.Templet_code=Zd_Templet2.Templet_code left join Zd_Ml_Xm3 On Zd_Templet2.Mx_Code=Zd_Ml_Xm3.Xm_Code    group by Zd_Templet1.Templet_Code,Templet_Name,Templet_Jc,Templet_Memo,Zd_Templet1.Yy_Code Order By Zd_Templet1.Templet_Code", "主表", True)
            My_Dataset.Tables("主表").Columns("Templet_Dj").ReadOnly = False
        ElseIf Form_Lb = "住院票据分类" Then
            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "select Lb_Code,Lb_Name,Lb_Jc,Lb_Memo,Yy_Code from Zd_JkFl1 where Yy_Code = '" & HisVar.HisVar.WsyCode & "' Order By Lb_Code", "主表", True)
        ElseIf Form_Lb = "门诊票据分类" Then
            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "select Lb_Code,Lb_Name,Lb_Jc,Lb_Memo from Zd_MzFp1 Order By Lb_Code", "主表", True)

        ElseIf Form_Lb = "就诊磁卡" Then
            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "SELECT Ry_Name,Ry_Jc,Ry_Sex,Ry_Sfzh,Ry_CsDate,Ry_Address,Ry_Tele,Kh,Ry_Memo FROM Zd_Ry", "主表", True)
        ElseIf Form_Lb = "挂号媒体字典" Then
            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "Select Ghmt_Code,Ghmt_Name,Ghmt_Jc,Ghmt_Memo,Yy_Code From Zd_GhMt where Yy_Code = '" & HisVar.HisVar.WsyCode & "' Order By Ghmt_Code", "主表", True)
        End If

        With Me.C1TrueDBGrid1
            My_Cm = CType(BindingContext(My_Dataset, "主表"), CurrencyManager)
            .SetDataBinding(My_Dataset, "主表", True)
            T_Label.Text = "∑=" + .Splits(0).Rows.Count.ToString
        End With
        C1TrueDBGrid1.Select()
    End Sub


#End Region

#Region "控件__动作"

    Private Sub Comm_Click(ByVal sender As Object, ByVal e As C1.Win.C1Command.ClickEventArgs) Handles Comm1.Click, Comm2.Click, Comm3.Click, Comm4.Click, C1Command1.Click
        Select Case sender.text
            Case "增加"
                Call P_ShowData("增加")
            Case "删除"
                Beep()
                If C1TrueDBGrid1.Splits(0).Rows.Count = 0 Then Exit Sub
                Call P_Del_Data()
            Case "更新"
                Call Init_Data()
                C1TrueDBGrid1.Select()
                C1TrueDBGrid1.MoveFirst()
            Case "药房初始化"
                Dim T_i As Integer
                Dim T_Row As DataRow
                For T_i = 1 To 5
                    If HisVar.HisVar.Sqldal.GetSingle("Select Count(*) from Zd_YyYf where Yf_Code='" & HisVar.HisVar.WsyCode & "0" & T_i & "'") > 0 Then Continue For
                    T_Row = My_Dataset.Tables("主表").Rows.Find(HisVar.HisVar.WsyCode & "0" & T_i)
                    If T_Row Is Nothing Then
                        T_Row = My_Dataset.Tables("主表").NewRow
                        With T_Row
                            .Item("Yy_Code") = HisVar.HisVar.WsyCode
                            .Item("Yf_Code") = HisVar.HisVar.WsyCode & "0" & T_i
                            .Item("Yf_Name") = "药房" & T_i
                            .Item("Yf_Jc") = "YF" & T_i
                            .Item("Yf_Memo") = ""
                            .Item("Yf_Use") = 0
                        End With
                        My_Dataset.Tables("主表").Rows.Add(T_Row)
                        C1TrueDBGrid1.MoveLast()
                        Try
                            HisVar.HisVar.Sqldal.ExecuteSql("Insert into Zd_YyYf(Yy_Code,Yf_Code,Yf_Name,Yf_Jc,Yf_Memo,Yf_Use)Values('" & T_Row.Item("Yy_Code") & "','" & T_Row.Item("Yf_Code") & "','" & T_Row.Item("Yf_Name") & "','" & T_Row.Item("Yf_Jc") & "','" & T_Row.Item("Yf_Memo") & "','" & T_Row.Item("Yf_Use") & "')")
                            T_Row.AcceptChanges()
                        Catch ex As Exception
                            Beep()
                            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
                            Exit Sub
                        Finally
                            Call P_Conn(False)
                        End Try
                    End If
                Next
            Case "合并客户"
'                If C1TrueDBGrid1.Columns("Hzyl_Zs_Code").Value & "" = "" Then
'                    MsgBox("该卫生室没有农合编码，不允许其他卫生室并入！", MsgBoxStyle.Information, "提示:")
'                    Exit Sub
'                End If
'                Dim vform As New HisControl.BaseChild
'                vform = New Zd_Xs_Kh3(My_Row, C1TrueDBGrid1, T_Label, My_Dataset.Tables("主表"))
'                If BaseFunc.BaseFunc.CheckOwnForm(Me, vform) = False Then
'                    vform.ShowDialog()
'                End If
        End Select
    End Sub

    Private Sub C1TrueDBGrid1_MouseUp(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles C1TrueDBGrid1.MouseUp
        If e.Button = MouseButtons.Right Then
            If (C1TrueDBGrid1.Row + 1) > C1TrueDBGrid1.RowCount Then
                Call P_ShowData("增加")
            Else
                Call P_ShowData("DBGrid")
            End If
        End If

    End Sub

    Private Sub C1TrueDBGrid1_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles C1TrueDBGrid1.KeyDown
        Select Case e.KeyCode
            Case Keys.Return
                If (C1TrueDBGrid1.Row + 1) > C1TrueDBGrid1.RowCount Then
                    Call P_ShowData("增加")
                Else
                    Call P_ShowData("DBGrid")
                End If
            Case Keys.Delete
                If Me.C1TrueDBGrid1.RowCount > 0 Then Call P_Del_Data()
            Case Keys.Insert
                Call P_ShowData("增加")
        End Select
    End Sub

    Private Sub C1TrueDBGrid1_ButtonClick(ByVal sender As System.Object, ByVal e As C1.Win.C1TrueDBGrid.ColEventArgs) Handles C1TrueDBGrid1.ButtonClick
        Dim VForm As New HisControl.BaseChild()
        My_Row = My_Cm.List(C1TrueDBGrid1.Row).Row
        If Form_Lb = "住院票据分类" Or Form_Lb = "病区字典" Or Form_Lb = "科室字典" Or Form_Lb = "门诊票据分类" Then
            VForm = New Zd_JjSave(My_Row, Form_Lb)
'        ElseIf Form_Lb = "诊疗模板" Then
'            VForm = New Zd_Zlmb3(My_Row)
        End If
        If VForm Is Nothing Then                      '窗体没有调入
            VForm.Owner = Me
        End If
        VForm.ShowDialog()
    End Sub

    Private Sub C1TrueDBGrid1_AfterColEdit(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.ColEventArgs) Handles C1TrueDBGrid1.AfterColEdit
        If Form_Lb = "药房字典" Then
            HisVar.HisVar.Sqldal.ExecuteSql("Update Zd_Yyyf Set Yf_Use='" & C1TrueDBGrid1.Columns("Yf_Use").Value & "' where Yf_Code='" & C1TrueDBGrid1.Columns("Yf_Code").Value & "'")
        End If
    End Sub

    Dim m_Rc As New BaseClass.C_RowChange
    Private Sub C1TrueDBGrid1_RowColChange(ByVal sender As System.Object, ByVal e As C1.Win.C1TrueDBGrid.RowColChangeEventArgs) Handles C1TrueDBGrid1.RowColChange
        If (C1TrueDBGrid1.Row + 1) > C1TrueDBGrid1.RowCount Then
        Else
            My_Row = My_Cm.List(C1TrueDBGrid1.Row).Row
            m_Rc.ChangeRow(My_Row)
        End If
    End Sub
#End Region

#Region "自定义函数"

    Private Sub P_ShowData(ByVal V_Lb As String)
        If V_Lb = "增加" Then
            V_Insert = True
        Else
            If Me.C1TrueDBGrid1.RowCount > 0 Then V_Insert = False Else V_Insert = True
        End If

        If V_Insert = False Then
            My_Row = My_Cm.List(C1TrueDBGrid1.Row).Row
        End If

        Dim vform As New HisControl.BaseChild
        If Form_Lb = "病例类别字典" Or Form_Lb = "病区字典" Or Form_Lb = "床位字典" Or Form_Lb = "药房字典" Or Form_Lb = "诊疗模板" Or Form_Lb = "住院票据分类" Or Form_Lb = "门诊票据分类" Or Form_Lb = "挂号媒体字典" Then
            vform = New Zd_Dict2(V_Insert, My_Row, My_Dataset.Tables("主表"), C1TrueDBGrid1, T_Label, m_Rc, Form_Lb)
'        ElseIf Form_Lb = "科室字典" Then
'            vform = New Zd_Ks2(V_Insert, My_Row, My_Dataset.Tables("主表"), C1TrueDBGrid1, T_Label, m_Rc)
'        ElseIf Form_Lb = "领导字典" Then
'            vform = New Zd_Ld2(V_Insert, My_Row, My_Dataset.Tables("主表"), C1TrueDBGrid1, T_Label, m_Rc)
'        ElseIf Form_Lb = "医生字典" Then
'            vform = New Zd_Ys2(V_Insert, My_Row, My_Dataset.Tables("主表"), C1TrueDBGrid1, T_Label, m_Rc)
'        ElseIf Form_Lb = "药品供应商" Then
'            vform = New Zd_Rk_Kh2(V_Insert, My_Row, My_Dataset.Tables("主表"), C1TrueDBGrid1, T_Label, m_Rc)
'        ElseIf Form_Lb = "销售客户" Then
'            vform = New Zd_Xs_Kh2(V_Insert, My_Row, My_Dataset.Tables("主表"), C1TrueDBGrid1, T_Label, m_Rc)
'        ElseIf Form_Lb = "剂型字典" Then
'            If HisPara.PublicConfig.IsCenterDb = "是" And V_Insert = True Then
'                vform = New Zd_Jx2_Database(V_Insert, My_Row, My_Dataset.Tables("主表"), C1TrueDBGrid1, T_Label, m_Rc)
'            Else
'                vform = New Zd_Dict2(V_Insert, My_Row, My_Dataset.Tables("主表"), C1TrueDBGrid1, T_Label, m_Rc, Form_Lb)
'            End If
'
'        ElseIf Form_Lb = "功效字典" Then
'            If HisPara.PublicConfig.IsCenterDb = "是" And V_Insert = True Then
'                vform = New Zd_Gx2_Database(V_Insert, My_Row, My_Dataset.Tables("主表"), C1TrueDBGrid1, T_Label, m_Rc)
'            Else
'                vform = New Zd_Dict2(V_Insert, My_Row, My_Dataset.Tables("主表"), C1TrueDBGrid1, T_Label, m_Rc, Form_Lb)
'            End If
'        ElseIf Form_Lb = "就诊磁卡" Then
'            vform = New Tl_JzkRy12(V_Insert, My_Row, My_Dataset.Tables("主表"), C1TrueDBGrid1, T_Label, m_Rc)
        End If

        If BaseFunc.BaseFunc.CheckOwnForm(Me, vform) = False Then
            vform.Owner = Me
            vform.Show()
        End If
    End Sub

    Private Sub P_Del_Data()
        If MsgBox("是否删除:" + C1TrueDBGrid1.Columns(1).Value + " ", MsgBoxStyle.Information + MsgBoxStyle.OkCancel + MsgBoxStyle.DefaultButton1, "提示:") = MsgBoxResult.Cancel Then Exit Sub
        My_Row = My_Cm.List(C1TrueDBGrid1.Row).Row
        Try
            If Form_Lb = "病例类别字典" Then
                If C1TrueDBGrid1.Columns("Bxlb_Name").Value = "普通患者" Or C1TrueDBGrid1.Columns("Bxlb_Name").Value = "合作医疗" Then
                    MsgBox("基本数据不能删除", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
                    Exit Sub
                End If
                HisVar.HisVar.Sqldal.ExecuteSql("Delete From Zd_Bxlb WHERE Bxlb_Code='" & My_Row.Item("Bxlb_Code") & "'")
            ElseIf Form_Lb = "病区字典" Then
                HisVar.HisVar.Sqldal.ExecuteSql("Delete From Zd_YyBq2 WHERE Bq_Code='" & My_Row.Item("Bq_Code") & "'")
                HisVar.HisVar.Sqldal.ExecuteSql("Delete From Zd_YyBq1 WHERE Bq_Code='" & My_Row.Item("Bq_Code") & "'")
            ElseIf Form_Lb = "床位字典" Then
                HisVar.HisVar.Sqldal.ExecuteSql("Delete From Zd_YyBc WHERE Bc_Code='" & My_Row.Item("Bc_Code") & "'")
                HisVar.HisVar.Sqldal.ExecuteSql("Delete from Zd_YyBq2 Where Bc_Code = '" & My_Row.Item("Bc_Code") & "'")
            ElseIf Form_Lb = "科室字典" Then
                If HisVar.HisVar.Sqldal.GetSingle("Select Count(Ks_Code) From Zd_YyYs where Ks_Code='" & My_Row.Item("Ks_Code") & "'") > 0 Then
                    MsgBox("科室内包含医生,不能删除!", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
                    Exit Sub
                End If

                If HisVar.HisVar.Sqldal.GetSingle("Select Count(Ks_Code) From Zd_YyKs_Xm where Ks_Code='" & My_Row.Item("Ks_Code") & "'") > 0 Then
                    MsgBox("科室已关联诊疗项目,不能删除!", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
                    Exit Sub
                End If

                If HisVar.HisVar.Sqldal.GetSingle("Select Count(Ks_Code) From Mz where Ks_Code='" & My_Row.Item("Ks_Code") & "'") > 0 Or HisVar.HisVar.Sqldal.GetSingle("Select Count(Ks_Code) From Mz_Sum where Ks_Code='" & My_Row.Item("Ks_Code") & "'") Or HisVar.HisVar.Sqldal.GetSingle("Select Count(Ks_Code) From Bl where Ks_Code='" & My_Row.Item("Ks_Code") & "'") Or HisVar.HisVar.Sqldal.GetSingle("Select Count(Ks_Code) From Bl_Cf where Ks_Code='" & My_Row.Item("Ks_Code") & "'") Then
                    MsgBox("该科室已被使用,不能删除!", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
                    Exit Sub
                End If

                HisVar.HisVar.Sqldal.ExecuteSql("Delete From Zd_YyKs WHERE Ks_Code='" & My_Row.Item("Ks_Code") & "'")
            ElseIf Form_Lb = "领导字典" Then
                HisVar.HisVar.Sqldal.ExecuteSql("Delete From Zd_YyLd WHERE Ld_Code='" & My_Row.Item("Ld_Code") & "'")
            ElseIf Form_Lb = "医生字典" Then

                If HisVar.HisVar.Sqldal.GetSingle("Select Count(Ys_Code) From Mz where Ys_Code='" & My_Row.Item("Ys_Code") & "'") > 0 Or HisVar.HisVar.Sqldal.GetSingle("Select Count(Ys_Code) From Mz_Sum where Ys_Code='" & My_Row.Item("Ys_Code") & "'") Or HisVar.HisVar.Sqldal.GetSingle("Select Count(Ys_Code) From Bl where Ys_Code='" & My_Row.Item("Ys_Code") & "'") Or HisVar.HisVar.Sqldal.GetSingle("Select Count(Ys_Code) From Bl_Cf where Ys_Code='" & My_Row.Item("Ys_Code") & "'") Then
                    MsgBox("该医生已被使用,不能删除!", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
                    Exit Sub
                End If

                HisVar.HisVar.Sqldal.ExecuteSql("Delete From Zd_YyYs WHERE Ys_Code='" & My_Row.Item("Ys_Code") & "'")
            ElseIf Form_Lb = "药品供应商" Then
                HisVar.HisVar.Sqldal.ExecuteSql("Delete From Zd_Kh_Rk WHERE Kh_Code='" & My_Row.Item("Kh_Code") & "'")
            ElseIf Form_Lb = "销售客户" Then
                HisVar.HisVar.Sqldal.ExecuteSql("Delete From Zd_Kh_Xs WHERE Kh_Code='" & My_Row.Item("Kh_Code") & "'")
            ElseIf Form_Lb = "剂型字典" Then
                HisVar.HisVar.Sqldal.ExecuteSql("Delete From Zd_Ml_Ypjx WHERE Jx_Code='" & My_Row.Item("Jx_Code") & "'")
            ElseIf Form_Lb = "功效字典" Then
                HisVar.HisVar.Sqldal.ExecuteSql("Delete From Zd_Ml_YpGx WHERE Gx_Code='" & My_Row.Item("Gx_Code") & "'")
            ElseIf Form_Lb = "药房字典" Then

                Exit Sub
            ElseIf Form_Lb = "诊疗模板" Then
                HisVar.HisVar.Sqldal.ExecuteSql("Delete From Zd_Templet2 WHERE Templet_Code='" & My_Row.Item("Templet_Code") & "'")
                HisVar.HisVar.Sqldal.ExecuteSql("Delete From Zd_Templet1 WHERE Templet_Code='" & My_Row.Item("Templet_Code") & "'")
            ElseIf Form_Lb = "住院票据分类" Then
                HisVar.HisVar.Sqldal.ExecuteSql("Delete From Zd_JkFl2 WHERE Lb_Code='" & My_Row.Item("Lb_Code") & "'")
                HisVar.HisVar.Sqldal.ExecuteSql("Delete From Zd_JkFl1 WHERE Lb_Code='" & My_Row.Item("Lb_Code") & "'")
                '------------------------
            ElseIf Form_Lb = "门诊票据分类" Then
                HisVar.HisVar.Sqldal.ExecuteSql("Delete From Zd_MzFp2 WHERE Lb_Code='" & My_Row.Item("Lb_Code") & "'")
                HisVar.HisVar.Sqldal.ExecuteSql("Delete From Zd_MzFp1 WHERE Lb_Code='" & My_Row.Item("Lb_Code") & "'")

            ElseIf Form_Lb = "就诊磁卡" Then
                HisVar.HisVar.Sqldal.ExecuteSql("Delete From Zd_Ry WHERE Kh='" & My_Row.Item("Kh") & "'")
            ElseIf Form_Lb = "挂号媒体字典" Then
                HisVar.HisVar.Sqldal.ExecuteSql("Delete From Zd_GhMt WHERE Ghmt_Code='" & My_Row.Item("Ghmt_Code") & "'")
            End If
            C1TrueDBGrid1.Delete()
            My_Row.AcceptChanges()

        Catch ex As Exception
            Beep()
            MsgBox("错误:" + ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
        Finally
            C1TrueDBGrid1.Select()
        End Try
    End Sub

#End Region


  
End Class