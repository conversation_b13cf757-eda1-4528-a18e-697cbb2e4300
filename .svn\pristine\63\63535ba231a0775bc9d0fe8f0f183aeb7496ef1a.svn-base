﻿Imports System.Data.SqlClient
Imports HisControl



Public Class Yk_Js2

#Region "定义__变量"
    Dim My_Adapter As New SqlDataAdapter                '从表适配器
    Dim My_Table As New DataTable                       '从表
    Dim V_GridCol As Integer                            '当前TDBGrid所在的列
    Public Cb_Cm As CurrencyManager                     '同步指针
    Public V_Tk_Code As String                          '出库编码
    Public V_Insert As Boolean                          '增加记录
    Public Ck3_FirstLoad As Boolean                     '第一次调入明细表
    Dim Str_Select As String
    Public Yp_Lb As String
    Dim My_Dataset As New DataSet
#End Region

#Region "传参"
    Dim Rrow As DataRow
    Dim Rtdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid
#End Region
    Public Sub New(ByVal tform As BaseForm, ByVal trow As DataRow, ByVal ttdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid)

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        Rrow = trow
        Rtdbgrid = ttdbgrid

        ' 在 InitializeComponent() 调用之后添加任何初始化。

    End Sub

    Private Sub Yk_Js2_FormClosing(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosingEventArgs) Handles Me.FormClosing
    End Sub

    Private Sub Yk_Js2_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Call Form_Init()                '窗体初始化
        Call Zb_Show()                  '显示数据

    End Sub

#Region "窗体__事件"

    Private Sub Form_Init()
        Panel2.Height = 30
        '按扭初始化
        Call P_Comm(Me.Comm1)
        Call P_Comm(Me.Comm2)
        Call P_Comm(Me.Comm3)

        Dim My_Grid As New BaseClass.C_Grid(Me.C1TrueDBGrid1)
        With My_Grid
            .Init_Grid()
            .AllSort(False)
            .P_MultiSelect(BaseClass.C_Grid.MultiSelect.Extended)
            .P_MarqueeEnum(BaseClass.C_Grid.MarqueeEnum.FloatingEditor)
            .P_Dock(BaseClass.C_Grid.Dock.Fill)
            .Init_Column("名称", "Yp_Name", 300, "左", "")
            .Init_Column("批准文号", "Mx_Gyzz", 120, "左", "")
            .Init_Column("规格", "Mx_Gg", 110, "左", "")
            .Init_Column("产地", "Mx_Cd", 110, "左", "")
            .Init_Column("产品批号", "Yp_Ph", 70, "左", "")
            .Init_Column("有效期", "Yp_Yxq", 70, "中", "yyyy-MM-dd")
            .Init_Column("单位", "Mx_CgDw", 50, "中", "")
            .Init_Column("数量", "Yk_JsSl", 60, "右", "##0.####")



        End With
    End Sub
    Private Sub P_Data_Show()   '从表数据

        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "Select Yf_Tk2.Xx_Code,Yp_Name,Mx_Gyzz,Mx_Gg,Mx_Cd,Yp_Ph,Yp_Yxq,Mx_CgDw,Tk_Sl/Mx_Cfbl as Yk_JsSl,Tk_Sl From Yf_Tk2,V_Ypkc Where Yf_Tk2.Xx_Code=V_Ypkc.Xx_Code and Tk_Code='" & Rtdbgrid.Columns("Tk_Code").Value & "' Order By Tk_Id", "从表", True)

        '主表记录
        Cb_Cm = CType(BindingContext(My_Dataset, "从表"), CurrencyManager)
        C1TrueDBGrid1.SetDataBinding(My_Dataset, "从表", True)
     
        C1TextBox1.Select()

    End Sub
#End Region

#Region "清空__显示"

    Private Sub Zb_Show()   '显示记录
        If Rtdbgrid.RowCount = 0 Then Exit Sub

        With Rrow
            V_Tk_Code = .Item("Tk_Code") & ""
            Me.C1TextBox1.Text = .Item("Tk_Memo") & ""
            Me.Label1.Text = .Item("Jsr_Name") & ""
        End With
        Label12.Text = V_Tk_Code                                                '出库编码
        Call P_Data_Show()
    End Sub



#End Region

#Region "控件__动作"

#Region "其它__控件"


    Private Sub Comm_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.Click
        Dim Pd As Object = HisVar.HisVar.Sqldal.GetSingle("select distinct Pd_Wc from Zd_YkPd where Pd_Wc='否'")
        If Pd Is Nothing Then
        Else
            MsgBox("药库正在进行盘点，请等待盘点完成后在进行接收！", MsgBoxStyle.Information, "提示：")
            Exit Sub
        End If

        Pd = HisVar.HisVar.Sqldal.GetSingle("select distinct Pd_Wc from Zd_YfPd where Pd_Wc='否'")
        If Pd Is Nothing Then
        Else
            MsgBox("药房正在进行盘点，请等待盘点完成后在进行接收！", MsgBoxStyle.Information, "提示：")
            Exit Sub
        End If


        Dim tmp_Ds As New DataSet
        Dim tmp_str As String = ""
        tmp_Ds = HisVar.HisVar.Sqldal.Query("select Yp_Name,Mx_Gg,Yp_Ph from Yf_Tk2,Yf_Tk1,V_Ypkc where yf_Tk2.Tk_Code=Yf_Tk1.Tk_Code and Yf_Tk2.Xx_Code=V_Ypkc.Xx_Code and Tk_Sl>Yf_sl" & Microsoft.VisualBasic.Right(Rtdbgrid.Columns("Yf_Code").Value, 1) & "   and Yf_Tk1.Tk_Code='" & Label12.Text & "'")
        For Each tmprow In tmp_Ds.Tables(0).Rows
            tmp_str = tmprow("Yp_Name") & tmprow("Mx_Gg") & ":" & tmprow("Yp_Ph") & vbCrLf
        Next
        If tmp_str.Length > 0 Then
            MsgBox("下列药品实际库存不足" & vbCrLf & tmp_str, MsgBoxStyle.Information, "提示")
            Exit Sub
        End If

        If MsgBox("是否接收:退库编码=" + Rtdbgrid.Columns("Tk_Code").Value + " ", MsgBoxStyle.Information + MsgBoxStyle.OkCancel, "提示:") = MsgBoxResult.Cancel Then Exit Sub

        Dim Cb_Row As DataRow

        Dim Yf_Sl As String = "Yf_Sl" & Microsoft.VisualBasic.Right(Rtdbgrid.Columns("Yf_Code").Value, 1)
        Dim arr As New ArrayList
        For Each Cb_Row In My_Dataset.Tables("从表").Rows

            arr.Add("Update Zd_Ml_Yp4 Set " & Yf_Sl & "=Isnull(" & Yf_Sl & ",0)-(" & Cb_Row.Item("Tk_Sl") & "),Yk_Sl=Yk_Sl+(" & Cb_Row.Item("Yk_JsSl") & ") Where Xx_code='" & Cb_Row.Item("Xx_Code") & "' And Yy_Code='" & HisVar.HisVar.WsyCode & "'")
        Next
        arr.Add("Update Yf_Tk1 Set Tk_Qr=1 Where Yy_Code='" & HisVar.HisVar.WsyCode & "' and Tk_Code='" & Rrow.Item("Tk_Code") & "'")
        HisVar.HisVar.Sqldal.ExecuteSqlTran(arr)
        Rrow.Delete()
        Rrow.AcceptChanges()
        Me.Close()
    End Sub
#End Region


    Private Sub Comm2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm2.Click
        Me.Close()
    End Sub
#End Region


#Region "鼠标__外观"
    Private Sub P_Comm(ByVal Bt As Button)
        With Bt
            Select Case .Tag
                Case "接收"
                    .Location = New Point(T_Line1.Left + 10, 4)
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_接收1")
                    .Text = "            &B"

                Case "取消"
                    .Location = New Point(Comm1.Left + Comm1.Width + 4, 4)
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
                    .Width = Me.Comm1.Width
                    .Text = "            &C"
                    'T_Line2.Location = New Point(Me.Comm2.Left + Me.Comm2.Width + 8, 0)
                Case "拒收"
                    .Location = New Point(Comm2.Left + Comm2.Width + 4, 4)
                    .BackgroundImage = MyResources.C_Resources.getimage("拒收1")
                    .Width = Me.Comm1.Width
                    .Text = "            &T"
            End Select
            .FlatStyle = FlatStyle.Flat
            .FlatAppearance.BorderSize = 0
            .BackgroundImageLayout = ImageLayout.Center
        End With
    End Sub

    Private Sub Comm_MouseEnter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm2.MouseEnter, Comm1.MouseEnter, Comm3.MouseEnter
        Select Case sender.tag
            Case "接收"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_接收2")
                Comm1.Cursor = Cursors.Hand
            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消2")
                Comm2.Cursor = Cursors.Hand
            Case "拒收"
                Comm3.BackgroundImage = MyResources.C_Resources.getimage("拒收2")
                Comm3.Cursor = Cursors.Hand
        End Select

    End Sub

    Private Sub Comm_MouseLeave(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm2.MouseLeave, Comm1.MouseLeave, Comm3.MouseLeave
        Select Case sender.tag
            Case "接收"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_接收1")
                Comm1.Cursor = Cursors.Default
            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
                Comm2.Cursor = Cursors.Default
            Case "拒收"
                Comm3.BackgroundImage = MyResources.C_Resources.getimage("拒收1")
                Comm3.Cursor = Cursors.Default
        End Select
    End Sub

    Private Sub Comm_MouseDown(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm2.MouseDown, Comm1.MouseDown, Comm3.MouseDown
        Select Case sender.tag
            Case "接收"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_接收3")
                Comm1.Cursor = Cursors.Default

            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消3")
                Comm2.Cursor = Cursors.Default
            Case "拒收"
                Comm3.BackgroundImage = MyResources.C_Resources.getimage("拒收3")
                Comm3.Cursor = Cursors.Default
        End Select
    End Sub

    Private Sub Comm_MouseUp(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm2.MouseUp, Comm1.MouseUp, Comm3.MouseUp
        Select Case sender.tag
            Case "接收"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_接收1")
                Comm1.Cursor = Cursors.Hand
            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
                Comm2.Cursor = Cursors.Hand
            Case "拒收"
                Comm3.BackgroundImage = MyResources.C_Resources.getimage("拒收1")
                Comm3.Cursor = Cursors.Hand
        End Select
    End Sub
#End Region


#Region "输入法设置"
    '中文
    Private Sub C1TextBox1_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1TextBox1.GotFocus
        InputLanguage.CurrentInputLanguage = HisVar.HisVar.InputCode
    End Sub

#End Region


    Private Sub Comm3_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm3.Click
        If MsgBox("是否拒收收该单据？ ", MsgBoxStyle.Information + MsgBoxStyle.OkCancel, "提示:") = MsgBoxResult.Cancel Then Exit Sub
        Try

            HisVar.HisVar.Sqldal.ExecuteSql("Update Yf_Tk1 Set Tk_Ok='0' where   Tk_Code='" & Label12.Text & "' ")

            Rrow.Delete()
            Rrow.AcceptChanges()
        Catch ex As Exception
            Beep()
            Rrow.CancelEdit()
            MsgBox(ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
            Exit Sub
        End Try


        Me.Close()
    End Sub
End Class