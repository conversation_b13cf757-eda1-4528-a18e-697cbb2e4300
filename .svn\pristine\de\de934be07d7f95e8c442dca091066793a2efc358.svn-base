﻿/**  版本信息模板在安装目录下，可自行修改。
* D_Yb_InfoDy.cs
*
* 功 能： N/A
* 类 名： D_Yb_InfoDy
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/9/23 09:50:31   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using System.Collections;
namespace DAL
{
	/// <summary>
	/// 数据访问类:D_Yb_InfoDy
	/// </summary>
	public partial class D_Yb_InfoDy
	{
		public D_Yb_InfoDy()
		{}
		#region  BasicMethod

        /// <summary>
        /// 导入His药品，诊疗项目 数据
        /// </summary>
        public bool DrHisInfor()
        {
            ArrayList arry = new ArrayList();
            StringBuilder strSql = new StringBuilder();
            strSql.Append("INSERT  INTO dbo.Yb_InfoDy( His_Code ,Lb ,His_Name ,His_Gg ,His_Cd ,His_Jx )SELECT V_Yp.Mx_Code AS His_Code ,CASE Dl_Name WHEN '卫生材料' THEN '2' ELSE '1' END AS Lb ,Yp_Name AS His_Name ,Mx_Gg AS His_Gg ,Mx_Cd AS His_Cd ,Jx_Name AS His_Jx FROM dbo.V_Yp WHERE  NOT EXISTS(SELECT 1 FROM dbo.Yb_InfoDy WHERE V_Yp.Mx_Code = dbo.Yb_InfoDy.His_Code)");
            arry.Add(strSql.ToString());
            strSql = new StringBuilder();
            strSql.Append("INSERT  INTO dbo.Yb_InfoDy( His_Code ,Lb ,His_Name ,His_Gg ,His_Cd ,His_Jx )SELECT Xm_Code AS His_Code,'2' AS Lb,Xm_Name AS His_Name,NULL AS His_Gg,NULL AS His_Cd,NULL AS His_Jx FROM dbo.Zd_Ml_Xm3 WHERE  NOT EXISTS(SELECT 1 FROM dbo.Yb_InfoDy WHERE Zd_Ml_Xm3.Xm_Code = dbo.Yb_InfoDy.His_Code)");
            arry.Add(strSql.ToString());
            try
            {
                HisVar.HisVar.Sqldal.ExecuteSqlTran(arry);
                return true;
            }
            catch (Exception e)
            {
                return false;
            }
        }


		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string His_Code)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from Yb_InfoDy");
			strSql.Append(" where His_Code=@His_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@His_Code", SqlDbType.VarChar,20)			};
			parameters[0].Value = His_Code;

			return HisVar.HisVar.Sqldal.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ModelOld.M_Yb_InfoDy model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into Yb_InfoDy(");
			strSql.Append("His_Code,Lb,His_Name,His_Gg,His_Cd,His_Jx,Zg_FyDj,Zg_MzBl,Zg_ZyBl,Zg_Dj,Jm_FyDj,Jm_MzBl,Jm_ZyBl,Jm_Dj,Gs_FyDj,Gs_MzBl,Gs_ZyBl,Gs_Dj)");
			strSql.Append(" values (");
			strSql.Append("@His_Code,@Lb,@His_Name,@His_Gg,@His_Cd,@His_Jx,@Zg_FyDj,@Zg_MzBl,@Zg_ZyBl,@Zg_Dj,@Jm_FyDj,@Jm_MzBl,@Jm_ZyBl,@Jm_Dj,@Gs_FyDj,@Gs_MzBl,@Gs_ZyBl,@Gs_Dj)");
			SqlParameter[] parameters = {
					new SqlParameter("@His_Code", SqlDbType.VarChar,20),
					new SqlParameter("@Lb", SqlDbType.Char,1),
					new SqlParameter("@His_Name", SqlDbType.VarChar,50),
					new SqlParameter("@His_Gg", SqlDbType.VarChar,50),
					new SqlParameter("@His_Cd", SqlDbType.VarChar,50),
					new SqlParameter("@His_Jx", SqlDbType.VarChar,50),
					new SqlParameter("@Zg_FyDj", SqlDbType.VarChar,10),
					new SqlParameter("@Zg_MzBl", SqlDbType.VarChar,10),
					new SqlParameter("@Zg_ZyBl", SqlDbType.VarChar,10),
					new SqlParameter("@Zg_Dj", SqlDbType.VarChar,10),
					new SqlParameter("@Jm_FyDj", SqlDbType.VarChar,10),
					new SqlParameter("@Jm_MzBl", SqlDbType.VarChar,10),
					new SqlParameter("@Jm_ZyBl", SqlDbType.VarChar,10),
					new SqlParameter("@Jm_Dj", SqlDbType.VarChar,10),
					new SqlParameter("@Gs_FyDj", SqlDbType.VarChar,10),
					new SqlParameter("@Gs_MzBl", SqlDbType.VarChar,10),
					new SqlParameter("@Gs_ZyBl", SqlDbType.VarChar,10),
					new SqlParameter("@Gs_Dj", SqlDbType.VarChar,10)};
			parameters[0].Value = model.His_Code;
			parameters[1].Value = model.Lb;
			parameters[2].Value = model.His_Name;
			parameters[3].Value = model.His_Gg;
			parameters[4].Value = model.His_Cd;
			parameters[5].Value = model.His_Jx;
			parameters[6].Value = model.Zg_FyDj;
			parameters[7].Value = model.Zg_MzBl;
			parameters[8].Value = model.Zg_ZyBl;
			parameters[9].Value = model.Zg_Dj;
			parameters[10].Value = model.Jm_FyDj;
			parameters[11].Value = model.Jm_MzBl;
			parameters[12].Value = model.Jm_ZyBl;
			parameters[13].Value = model.Jm_Dj;
			parameters[14].Value = model.Gs_FyDj;
			parameters[15].Value = model.Gs_MzBl;
			parameters[16].Value = model.Gs_ZyBl;
			parameters[17].Value = model.Gs_Dj;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


        /// <summary>
        /// 更新对应数据
        /// </summary>
       
        public bool Update(string FyDj, string MzBl, string ZyBl, string Dj,string His_Code,string Lb)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update Yb_InfoDy set ");
            //职工
            if (Lb == "ZG")
            {
                strSql.Append("Zg_FyDj=@FyDj,");
                strSql.Append("Zg_MzBl=@MzBl,");
                strSql.Append("Zg_ZyBl=@ZyBl,");
                strSql.Append("Zg_Dj=@Dj");
            }else if (Lb == "JM") //居民
            {
                strSql.Append("Jm_FyDj=@FyDj,");
                strSql.Append("Jm_MzBl=@MzBl,");
                strSql.Append("Jm_ZyBl=@ZyBl,");
                strSql.Append("Jm_Dj=@Dj");
            }
            else if (Lb == "GS")   //工伤
            {
                strSql.Append("Gs_FyDj=@FyDj,");
                strSql.Append("Gs_MzBl=@MzBl,");
                strSql.Append("Gs_ZyBl=@ZyBl,");
                strSql.Append("Gs_Dj=@Dj");
            }
            else
            {
                return false;
            }

            strSql.Append(" where His_Code=@His_Code ");

            SqlParameter[] parameters = {
					new SqlParameter("@FyDj", SqlDbType.VarChar,10),
					new SqlParameter("@MzBl", SqlDbType.VarChar,10),
					new SqlParameter("@ZyBl", SqlDbType.VarChar,10),
					new SqlParameter("@Dj", SqlDbType.VarChar,10),
					new SqlParameter("@His_Code", SqlDbType.VarChar,20)};

            parameters[0].Value = FyDj;
            parameters[1].Value = MzBl;
            parameters[2].Value = ZyBl;
            parameters[3].Value = Dj;
            parameters[4].Value = His_Code;

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ModelOld.M_Yb_InfoDy model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update Yb_InfoDy set ");
			strSql.Append("Lb=@Lb,");
			strSql.Append("His_Name=@His_Name,");
			strSql.Append("His_Gg=@His_Gg,");
			strSql.Append("His_Cd=@His_Cd,");
			strSql.Append("His_Jx=@His_Jx,");
			strSql.Append("Zg_FyDj=@Zg_FyDj,");
			strSql.Append("Zg_MzBl=@Zg_MzBl,");
			strSql.Append("Zg_ZyBl=@Zg_ZyBl,");
			strSql.Append("Zg_Dj=@Zg_Dj,");
			strSql.Append("Jm_FyDj=@Jm_FyDj,");
			strSql.Append("Jm_MzBl=@Jm_MzBl,");
			strSql.Append("Jm_ZyBl=@Jm_ZyBl,");
			strSql.Append("Jm_Dj=@Jm_Dj,");
			strSql.Append("Gs_FyDj=@Gs_FyDj,");
			strSql.Append("Gs_MzBl=@Gs_MzBl,");
			strSql.Append("Gs_ZyBl=@Gs_ZyBl,");
			strSql.Append("Gs_Dj=@Gs_Dj");
			strSql.Append(" where His_Code=@His_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Lb", SqlDbType.Char,1),
					new SqlParameter("@His_Name", SqlDbType.VarChar,50),
					new SqlParameter("@His_Gg", SqlDbType.VarChar,50),
					new SqlParameter("@His_Cd", SqlDbType.VarChar,50),
					new SqlParameter("@His_Jx", SqlDbType.VarChar,50),
					new SqlParameter("@Zg_FyDj", SqlDbType.VarChar,10),
					new SqlParameter("@Zg_MzBl", SqlDbType.VarChar,10),
					new SqlParameter("@Zg_ZyBl", SqlDbType.VarChar,10),
					new SqlParameter("@Zg_Dj", SqlDbType.VarChar,10),
					new SqlParameter("@Jm_FyDj", SqlDbType.VarChar,10),
					new SqlParameter("@Jm_MzBl", SqlDbType.VarChar,10),
					new SqlParameter("@Jm_ZyBl", SqlDbType.VarChar,10),
					new SqlParameter("@Jm_Dj", SqlDbType.VarChar,10),
					new SqlParameter("@Gs_FyDj", SqlDbType.VarChar,10),
					new SqlParameter("@Gs_MzBl", SqlDbType.VarChar,10),
					new SqlParameter("@Gs_ZyBl", SqlDbType.VarChar,10),
					new SqlParameter("@Gs_Dj", SqlDbType.VarChar,10),
					new SqlParameter("@His_Code", SqlDbType.VarChar,20)};
			parameters[0].Value = model.Lb;
			parameters[1].Value = model.His_Name;
			parameters[2].Value = model.His_Gg;
			parameters[3].Value = model.His_Cd;
			parameters[4].Value = model.His_Jx;
			parameters[5].Value = model.Zg_FyDj;
			parameters[6].Value = model.Zg_MzBl;
			parameters[7].Value = model.Zg_ZyBl;
			parameters[8].Value = model.Zg_Dj;
			parameters[9].Value = model.Jm_FyDj;
			parameters[10].Value = model.Jm_MzBl;
			parameters[11].Value = model.Jm_ZyBl;
			parameters[12].Value = model.Jm_Dj;
			parameters[13].Value = model.Gs_FyDj;
			parameters[14].Value = model.Gs_MzBl;
			parameters[15].Value = model.Gs_ZyBl;
			parameters[16].Value = model.Gs_Dj;
			parameters[17].Value = model.His_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string His_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Yb_InfoDy ");
			strSql.Append(" where His_Code=@His_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@His_Code", SqlDbType.VarChar,20)			};
			parameters[0].Value = His_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string His_Codelist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Yb_InfoDy ");
			strSql.Append(" where His_Code in ("+His_Codelist + ")  ");
			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Yb_InfoDy GetModel(string His_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 His_Code,Lb,His_Name,His_Gg,His_Cd,His_Jx,Zg_FyDj,Zg_MzBl,Zg_ZyBl,Zg_Dj,Jm_FyDj,Jm_MzBl,Jm_ZyBl,Jm_Dj,Gs_FyDj,Gs_MzBl,Gs_ZyBl,Gs_Dj from Yb_InfoDy ");
			strSql.Append(" where His_Code=@His_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@His_Code", SqlDbType.VarChar,20)			};
			parameters[0].Value = His_Code;

			ModelOld.M_Yb_InfoDy model=new ModelOld.M_Yb_InfoDy();
			DataSet ds=HisVar.HisVar.Sqldal.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Yb_InfoDy DataRowToModel(DataRow row)
		{
			ModelOld.M_Yb_InfoDy model=new ModelOld.M_Yb_InfoDy();
			if (row != null)
			{
				if(row["His_Code"]!=null)
				{
					model.His_Code=row["His_Code"].ToString();
				}
				if(row["Lb"]!=null)
				{
					model.Lb=row["Lb"].ToString();
				}
				if(row["His_Name"]!=null)
				{
					model.His_Name=row["His_Name"].ToString();
				}
				if(row["His_Gg"]!=null)
				{
					model.His_Gg=row["His_Gg"].ToString();
				}
				if(row["His_Cd"]!=null)
				{
					model.His_Cd=row["His_Cd"].ToString();
				}
				if(row["His_Jx"]!=null)
				{
					model.His_Jx=row["His_Jx"].ToString();
				}
				if(row["Zg_FyDj"]!=null)
				{
					model.Zg_FyDj=row["Zg_FyDj"].ToString();
				}
				if(row["Zg_MzBl"]!=null)
				{
					model.Zg_MzBl=row["Zg_MzBl"].ToString();
				}
				if(row["Zg_ZyBl"]!=null)
				{
					model.Zg_ZyBl=row["Zg_ZyBl"].ToString();
				}
				if(row["Zg_Dj"]!=null)
				{
					model.Zg_Dj=row["Zg_Dj"].ToString();
				}
				if(row["Jm_FyDj"]!=null)
				{
					model.Jm_FyDj=row["Jm_FyDj"].ToString();
				}
				if(row["Jm_MzBl"]!=null)
				{
					model.Jm_MzBl=row["Jm_MzBl"].ToString();
				}
				if(row["Jm_ZyBl"]!=null)
				{
					model.Jm_ZyBl=row["Jm_ZyBl"].ToString();
				}
				if(row["Jm_Dj"]!=null)
				{
					model.Jm_Dj=row["Jm_Dj"].ToString();
				}
				if(row["Gs_FyDj"]!=null)
				{
					model.Gs_FyDj=row["Gs_FyDj"].ToString();
				}
				if(row["Gs_MzBl"]!=null)
				{
					model.Gs_MzBl=row["Gs_MzBl"].ToString();
				}
				if(row["Gs_ZyBl"]!=null)
				{
					model.Gs_ZyBl=row["Gs_ZyBl"].ToString();
				}
				if(row["Gs_Dj"]!=null)
				{
					model.Gs_Dj=row["Gs_Dj"].ToString();
				}
			}
			return model;
		}


		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select His_Code,Lb,His_Name,His_Gg,His_Cd,His_Jx,Zg_FyDj,Zg_MzBl,Zg_ZyBl,Zg_Dj,Jm_FyDj,Jm_MzBl,Jm_ZyBl,Jm_Dj,Gs_FyDj,Gs_MzBl,Gs_ZyBl,Gs_Dj ");
			strSql.Append(" FROM Yb_InfoDy ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" His_Code,Lb,His_Name,His_Gg,His_Cd,His_Jx,Zg_FyDj,Zg_MzBl,Zg_ZyBl,Zg_Dj,Jm_FyDj,Jm_MzBl,Jm_ZyBl,Jm_Dj,Gs_FyDj,Gs_MzBl,Gs_ZyBl,Gs_Dj ");
			strSql.Append(" FROM Yb_InfoDy ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM Yb_InfoDy ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.His_Code desc");
			}
			strSql.Append(")AS Row, T.*  from Yb_InfoDy T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Yb_InfoDy";
			parameters[1].Value = "His_Code";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return HisVar.HisVar.Sqldal.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

