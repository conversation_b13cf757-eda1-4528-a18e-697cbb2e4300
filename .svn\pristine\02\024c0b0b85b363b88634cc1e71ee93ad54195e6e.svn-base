﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="0" />
    <DataSources isList="true" count="0" />
    <Relations isList="true" count="0" />
    <Report isRef="0" />
    <Variables isList="true" count="89">
      <value>,名称,名称,名称,System.String,,False,False,False</value>
      <value>,日期,日期,System.String,,False,False</value>
      <value>,个人编号,个人编号,System.String,,False,False</value>
      <value>,姓名,姓名,System.String,,False,False</value>
      <value>,性别,性别,System.String,,False,False</value>
      <value>,年龄,年龄,System.String,,False,False</value>
      <value>,人员类别,人员类别,System.String,,False,False</value>
      <value>,工作单位,工作单位,System.String,,False,False</value>
      <value>,账户余额,账户余额,System.String,,False,False</value>
      <value>,待遇类别,待遇类别,System.String,,False,False</value>
      <value>,公务员类别,公务员类别,System.String,,False,False</value>
      <value>,医疗类别,医疗类别,System.String,,False,False</value>
      <value>,就诊医院,就诊医院,System.String,,False,False</value>
      <value>,疾病诊断,疾病诊断,System.String,,False,False</value>
      <value>,入院日期,入院日期,System.String,,False,False</value>
      <value>,出院日期,出院日期,System.String,,False,False</value>
      <value>,本年住院次数,本年住院次数,System.String,,False,False</value>
      <value>,押金金额,押金金额,System.String,,False,False</value>
      <value>,截止上次本年医疗费总额,截止上次本年医疗费总额,System.String,,False,False</value>
      <value>,截止上次本年住院符合基本医疗费用累计,截止上次本年住院符合基本医疗费用累计,System.String,,False,False</value>
      <value>,截止上次本年统筹基金支付累计,截止上次本年统筹基金支付累计,System.String,,False,False</value>
      <value>,本次医疗费用总额,本次医疗费用总额,System.String,,False,False</value>
      <value>,本次符合基本医疗费用合计,本次符合基本医疗费用合计,System.String,,False,False</value>
      <value>,起付标准,起付标准,System.String,,False,False</value>
      <value>,转诊先自付,转诊先自付,System.String,,False,False</value>
      <value>,本次不符合基本医疗费用合计,本次不符合基本医疗费用合计,System.String,,False,False</value>
      <value>,乙类自理,乙类自理,System.String,,False,False</value>
      <value>,丙类自费,丙类自费,System.String,,False,False</value>
      <value>,超限价自费,超限价自费,System.String,,False,False</value>
      <value>,进入分段费用1,进入分段费用1,System.String,,False,False</value>
      <value>,进入分段费用2,进入分段费用2,System.String,,False,False</value>
      <value>,进入分段费用3,进入分段费用3,System.String,,False,False</value>
      <value>,蒙中药报销比例1,蒙中药报销比例1,System.String,,False,False</value>
      <value>,蒙中药报销比例2,蒙中药报销比例2,System.String,,False,False</value>
      <value>,蒙中药报销比例3,蒙中药报销比例3,System.String,,False,False</value>
      <value>,分段基金支付1,分段基金支付1,System.String,,False,False</value>
      <value>,分段基金支付2,分段基金支付2,System.String,,False,False</value>
      <value>,分段基金支付3,分段基金支付3,System.String,,False,False</value>
      <value>,分段公务员补助1,分段公务员补助1,System.String,,False,False</value>
      <value>,分段公务员补助2,分段公务员补助2,System.String,,False,False</value>
      <value>,分段公务员补助3,分段公务员补助3,System.String,,False,False</value>
      <value>,蒙中药分段个人自付1,蒙中药分段个人自付1,System.String,,False,False</value>
      <value>,蒙中药分段个人自付2,蒙中药分段个人自付2,System.String,,False,False</value>
      <value>,蒙中药分段个人自付3,蒙中药分段个人自付3,System.String,,False,False</value>
      <value>,超大额封顶线部分,超大额封顶线部分,System.String,,False,False</value>
      <value>,超大额封顶线公务员补助,超大额封顶线公务员补助,System.String,,False,False</value>
      <value>,超大额封顶线个人自付,超大额封顶线个人自付,System.String,,False,False</value>
      <value>,统筹基金支付,统筹基金支付,System.String,,False,False</value>
      <value>,大额基金支付,大额基金支付,System.String,,False,False</value>
      <value>,公务员补助支付,公务员补助支付,System.String,,False,False</value>
      <value>,个人账户支付,个人账户支付,System.String,,False,False</value>
      <value>,自费费用,自费费用,System.String,,False,False</value>
      <value>,个人自付,个人自付,System.String,,False,False</value>
      <value>,本次报销总额,本次报销总额,System.String,,False,False</value>
      <value>,现金支付,现金支付,System.String,,False,False</value>
      <value>,本次报销总额大写,本次报销总额大写,System.String,,False,False</value>
      <value>,合计,合计,System.String,,False,False</value>
      <value>,年度报销累计,年度报销累计,System.String,,False,False</value>
      <value>,统筹基金支付累计,统筹基金支付累计,System.String,,False,False</value>
      <value>,大额基金支付累计,大额基金支付累计,System.String,,False,False</value>
      <value>,超大额封顶线公务员补助累计,超大额封顶线公务员补助累计,System.String,,False,False</value>
      <value>,住院单号,住院单号,System.String,,False,False</value>
      <value>,编号,编号,System.String,,False,False</value>
      <value>,扶贫商业补充保险,扶贫商业补充保险,System.String,,False,False</value>
      <value>,民政商业保险,民政商业保险,System.String,,False,False</value>
      <value>,民政医疗救助,民政医疗救助,System.String,,False,False</value>
      <value>,政府大病兜底保证,政府大病兜底保证,System.String,,False,False</value>
      <value>,本次医疗蒙中医药及蒙中医诊疗项目费用总额,本次医疗蒙中医药及蒙中医诊疗项目费用总额,System.String,,False,False</value>
      <value>,本次医疗蒙中医药及蒙中医诊疗项目费用占比,本次医疗蒙中医药及蒙中医诊疗项目费用占比,System.String,,False,False</value>
      <value>,蒙中医药进入分段费用总额1,蒙中医药进入分段费用总额1,System.String,,False,False</value>
      <value>,蒙中医药进入分段费用总额2,蒙中医药进入分段费用总额2,System.String,,False,False</value>
      <value>,蒙中医药进入分段费用总额3,蒙中医药进入分段费用总额3,System.String,,False,False</value>
      <value>,蒙中医药分段基金支付1,蒙中医药分段基金支付1,System.String,,False,False</value>
      <value>,蒙中医药分段基金支付2,蒙中医药分段基金支付2,System.String,,False,False</value>
      <value>,蒙中医药分段基金支付3,蒙中医药分段基金支付3,System.String,,False,False</value>
      <value>,西医药进入分段费用总额1,西医药进入分段费用总额1,System.String,,False,False</value>
      <value>,西医药进入分段费用总额2,西医药进入分段费用总额2,System.String,,False,False</value>
      <value>,西医药进入分段费用总额3,西医药进入分段费用总额3,System.String,,False,False</value>
      <value>,西医药报销比例1,西医药报销比例1,System.String,,False,False</value>
      <value>,西医药报销比例2,西医药报销比例2,System.String,,False,False</value>
      <value>,西医药报销比例3,西医药报销比例3,System.String,,False,False</value>
      <value>,西医药分段基金支付1,西医药分段基金支付1,System.String,,False,False</value>
      <value>,西医药分段基金支付2,西医药分段基金支付2,System.String,,False,False</value>
      <value>,西医药分段基金支付3,西医药分段基金支付3,System.String,,False,False</value>
      <value>,西医药分段个人自付1,西医药分段个人自付1,System.String,,False,False</value>
      <value>,西医药分段个人自付2,西医药分段个人自付2,System.String,,False,False</value>
      <value>,西医药分段个人自付3,西医药分段个人自付3,System.String,,False,False</value>
      <value>,其中蒙中医药符合基本医疗费用,其中蒙中医药符合基本医疗费用,System.String,,False,False</value>
      <value>,其中西医药符合基本医疗费用,其中西医药符合基本医疗费用,System.String,,False,False</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="2" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="345">
        <Text2 Ref="3" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0.05,2.39,1.87,0.76</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text2</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>个人编号</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text2>
        <Text3 Ref="4" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>5.93,2.59,0.76,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text3</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>姓名</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text3>
        <Text4 Ref="5" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>9.7,2.53,1.1,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text4</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>性别</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text4>
        <Text5 Ref="6" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>12.16,2.5,0.65,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text5</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>年龄</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text5>
        <Text6 Ref="7" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>14.42,2.5,1.52,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text6</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>人员类别</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text6>
        <Text7 Ref="8" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0.06,3.34,1.84,0.7</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text7</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>工作单位</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text7>
        <Text8 Ref="9" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>10.09,3.46,1.52,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text8</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>待遇类别</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text8>
        <Text9 Ref="10" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>14.37,3.2,1.52,0.9</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text9</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>公务员类别</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text9>
        <Text10 Ref="11" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0.04,4.28,1.94,0.62</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text10</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>医疗类别</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text10>
        <Text11 Ref="12" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>5.84,4.3,1.52,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text11</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>就诊医院</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text11>
        <Text12 Ref="13" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>11.33,4.34,1.52,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text12</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>疾病诊断</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text12>
        <Text13 Ref="14" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0.06,5.12,1.96,0.7</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text13</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>入院日期</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text13>
        <Text14 Ref="15" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>5.83,5.25,1.52,0.46</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text14</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>出院日期</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text14>
        <Text15 Ref="16" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>11.01,5.21,2,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text15</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>本年住院次数</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text15>
        <Text16 Ref="17" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>15.24,5.26,1.52,0.43</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text16</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>押金金额</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text16>
        <Text17 Ref="18" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0.08,5.91,2.33,0.86</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text17</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>截止上次本年
医疗费总额</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text17>
        <Text18 Ref="19" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>5.96,6.06,2.99,0.73</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Width</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text18</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>截止上次本年住院符
合基本医疗费用累计				
</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
        </Text18>
        <Text19 Ref="20" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>13.75,6.03,2.29,0.72</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Width</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text19</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>截止上次本年统
筹基金支付累计		
</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
        </Text19>
        <Text20 Ref="21" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0.12,6.86,2.33,0.74</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text20</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>本次医疗费用
总额	
</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
        </Text20>
        <Text21 Ref="22" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>5.98,6.91,2.88,0.71</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text21</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>本次符合基本医疗费
用合计				
</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
        </Text21>
        <Text22 Ref="23" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>11.77,7.02,1.52,1.41</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text22</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>起付标准</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text22>
        <Text24 Ref="24" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0.02,8.93,2.43,0.66</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text24</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>本次不符合基本
医疗费用合计
</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
        </Text24>
        <Text25 Ref="25" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>5.77,9.09,1.52,0.4</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text25</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>其中:</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
        </Text25>
        <Text26 Ref="26" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>7.6,9.02,1.52,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text26</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>乙类自理:</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text26>
        <Text27 Ref="27" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>11.11,9.02,1.52,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text27</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>丙类自费:</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text27>
        <Text28 Ref="28" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>14.67,9.03,1.52,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text28</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>超限价自费</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text28>
        <Text29 Ref="29" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0.7,9.85,2.1,0.71</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text29</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>符合基本医疗
费用</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text29>
        <Text30 Ref="30" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>2.9,9.86,2.1,0.71</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text30</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>进入分段费用
总额</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text30>
        <Text32 Ref="31" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>17,9.76,1.7,0.81</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text32</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>分段基金
支付合计</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text32>
        <Text33 Ref="32" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>15.6,9.77,1.3,0.81</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text33</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>分段公务
员补助</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text33>
        <Text35 Ref="33" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0.17,9.83,0.45,3.32</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text35</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>分
段
计
算
明
细
</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text35>
        <Text36 Ref="34" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0.13,13.5,2.88,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text36</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>超大额封顶线部分</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text36>
        <Text37 Ref="35" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>6.49,13.48,3.64,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text37</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>超大额封顶线公务员补助</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text37>
        <Text38 Ref="36" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>13.9,13.46,2.27,0.7</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text38</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>超大额封顶线个
人自付</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text38>
        <Text39 Ref="37" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0.15,14.47,2.65,1.49</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text39</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>本次基金支付</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text39>
        <Text40 Ref="38" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>3.41,14.36,2.37,0.63</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text40</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>统筹基金支付</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text40>
        <Text41 Ref="39" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>3.65,15.23,1.98,0.65</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text41</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>大额基金支付</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text41>
        <Text42 Ref="40" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>8.71,14.41,2.15,0.54</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text42</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>公务员补助支付</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text42>
        <Text43 Ref="41" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>8.73,15.31,2.13,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text43</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>个人账户支付</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text43>
        <Text44 Ref="42" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0.31,16.25,2.25,0.53</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text44</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>本次报销总额</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text44>
        <Text45 Ref="43" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>9.08,16.28,1.49,0.41</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text45</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>现金支付</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text45>
        <Text46 Ref="44" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0.14,17.15,2.88,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text46</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>本次报销总额(大写)</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text46>
        <Text47 Ref="45" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>13.83,14.34,0.52,3.19</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text47</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>个
人
负
担
部
分</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text47>
        <Text48 Ref="46" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>14.74,14.41,1.52,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text48</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>乙类自理</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text48>
        <Text49 Ref="47" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>14.74,15.33,1.52,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text49</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>自费费用</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text49>
        <Text50 Ref="48" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>14.72,16.19,1.52,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text50</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>个人自付</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text50>
        <Text51 Ref="49" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>14.73,17.12,1.52,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text51</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>合计:</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text51>
        <Text52 Ref="50" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0.16,17.99,2.13,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text52</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>年度报销累计</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text52>
        <Text53 Ref="51" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>4.88,18.04,2.73,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text53</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>统筹基金支付累计</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text53>
        <Text54 Ref="52" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>9.8,17.82,1.76,0.9</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text54</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>大额基金支
付累计</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text54>
        <Text55 Ref="53" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>13.77,17.93,2.58,0.72</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text55</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>超大额封顶线公务
员补助累计</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text55>
        <Text56 Ref="54" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>6.51,3.32,1.5,0.64</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text56</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>账户余额</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text56>
        <Text57 Ref="55" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0.05,1.72,0.83,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text57</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>编号:</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
        </Text57>
        <Text58 Ref="56" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>6.34,1.75,1.52,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text58</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>住院单号:</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
        </Text58>
        <Text59 Ref="57" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>17.24,1.72,1.52,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text59</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>单位:元</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
        </Text59>
        <Text60 Ref="58" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0.42,19.19,1.91,0.57</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text60</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>初审人签字:</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text60>
        <Text61 Ref="59" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>6.21,19.11,1.78,0.59</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text61</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>复审人签字:</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text61>
        <Text62 Ref="60" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>12.24,19.25,1.94,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text62</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>领款人签字:</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text62>
        <Text63 Ref="61" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>2.11,19.81,2.11,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text63</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>    年    月    日	
</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text63>
        <Text64 Ref="62" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>8.2,19.8,2.11,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>93652a6ea611421188a6ec41248df72d</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text64</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>    年    月    日	
</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text64>
        <Text65 Ref="63" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>13.85,19.87,2.11,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>6cf962a16e7a46c89f1cceba59eb7665</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text65</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>    年    月    日	
</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text65>
        <Text66 Ref="64" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>12.67,1.62,1.52,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text66</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{日期}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text66>
        <Text67 Ref="65" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>2.14,2.55,3.48,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text67</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{个人编号}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text67>
        <Text68 Ref="66" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>7.03,2.5,2.59,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text68</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{姓名}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text68>
        <Text69 Ref="67" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>10.96,2.55,0.88,0.44</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text69</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{性别}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text69>
        <Text70 Ref="68" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>13.06,2.51,0.91,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text70</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{年龄}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text70>
        <Text71 Ref="69" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>16.27,2.5,2.51,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text71</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{人员类别}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text71>
        <Text72 Ref="70" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>2.1,3.36,4.15,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text72</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{工作单位}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text72>
        <Text73 Ref="71" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>8.12,3.44,1.69,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text73</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{账户余额}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text73>
        <Text74 Ref="72" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>11.88,3.45,2.2,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text74</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{待遇类别}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text74>
        <Text75 Ref="73" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>16.29,3.38,2.46,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text75</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{公务员类别}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text75>
        <Text76 Ref="74" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>2.17,4.32,3.4,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text76</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{医疗类别}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text76>
        <Text77 Ref="75" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>7.63,4.32,3.17,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text77</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{就诊医院}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text77>
        <Text78 Ref="76" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>13.45,4.33,5.28,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text78</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{疾病诊断}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text78>
        <Text79 Ref="77" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>2.11,5.19,3.45,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text79</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{入院日期}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text79>
        <Text80 Ref="78" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>7.7,5.17,3.08,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text80</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{出院日期}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text80>
        <Text81 Ref="79" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>13.37,5.21,1.7,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text81</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{本年住院次数}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text81>
        <Text82 Ref="80" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>16.98,5.23,1.75,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text82</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{押金金额}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text82>
        <Text83 Ref="81" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>2.82,6.16,2.73,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text83</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{截止上次本年医疗费总额}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text83>
        <Text84 Ref="82" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>9.26,6.19,3.99,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text84</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{截止上次本年住院符合基本医疗费用累计}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text84>
        <Text85 Ref="83" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>16.49,6.19,2.28,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text85</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{截止上次本年统筹基金支付累计}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text85>
        <Text86 Ref="84" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>2.74,6.95,2.85,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text86</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{本次医疗费用总额}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text86>
        <Text87 Ref="85" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>9.18,7,2.44,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text87</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{本次符合基本医疗费用合计}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text87>
        <Text88 Ref="86" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>13.41,7.05,1.64,1.41</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text88</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{起付标准}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text88>
        <Text89 Ref="87" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>17.2,7.05,1.52,1.41</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text89</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{转诊先自付}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text89>
        <Text90 Ref="88" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>2.84,9.02,2.74,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text90</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{本次不符合基本医疗费用合计}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text90>
        <Text91 Ref="89" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>9.26,9,1.73,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text91</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{乙类自理}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text91>
        <Text92 Ref="90" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>12.66,9.01,1.86,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text92</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{丙类自费}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text92>
        <Text93 Ref="91" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>16.17,9.03,2.54,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text93</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{超限价自费}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text93>
        <Text94 Ref="92" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0.9,10.61,1.7,0.71</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text94</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>起付线--
30000</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text94>
        <Text95 Ref="93" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>1,11.65,1.6,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text95</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>30000以上</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text95>
        <Text96 Ref="94" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>1,12.59,1.6,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text96</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>大额基金段</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text96>
        <Text97 Ref="95" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>2.9,10.76,2.1,0.61</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text97</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{进入分段费用1}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text97>
        <Text98 Ref="96" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>2.9,11.74,2.1,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text98</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{进入分段费用2}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text98>
        <Text99 Ref="97" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>2.9,12.56,2.1,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text99</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{进入分段费用3}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text99>
        <Text100 Ref="98" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>6.85,10.81,1,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text100</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{蒙中药报销比例1}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text100>
        <Text101 Ref="99" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>6.85,11.66,1,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text101</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{蒙中药报销比例2}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text101>
        <Text102 Ref="100" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>6.85,12.63,1,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text102</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{蒙中药报销比例3}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text102>
        <Text103 Ref="101" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>17.05,10.82,1.7,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text103</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{分段基金支付1}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text103>
        <Text104 Ref="102" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>17.05,11.7,1.7,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text104</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{分段基金支付2}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text104>
        <Text106 Ref="103" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>15.6,10.8,1.4,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text106</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{分段公务员补助1}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text106>
        <Text107 Ref="104" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>15.6,11.71,1.4,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text107</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{分段公务员补助2}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text107>
        <Text108 Ref="105" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>15.6,12.62,1.4,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text108</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{分段公务员补助3}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text108>
        <Text109 Ref="106" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>9.15,10.74,1.1,0.61</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text109</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{蒙中药分段个人自付1}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text109>
        <Text110 Ref="107" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>9.15,11.66,1.1,0.61</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text110</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{蒙中药分段个人自付2}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text110>
        <Text111 Ref="108" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>9.15,12.59,1.1,0.61</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text111</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{蒙中药分段个人自付3}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text111>
        <Text112 Ref="109" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>3.24,13.5,2.6,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text112</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{超大额封顶线部分}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text112>
        <Text113 Ref="110" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>10.64,13.53,2.91,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text113</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{超大额封顶线公务员补助}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text113>
        <Text114 Ref="111" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>16.56,13.54,2.17,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text114</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{超大额封顶线个人自付}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text114>
        <Text115 Ref="112" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>6.14,14.46,2.08,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text115</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{统筹基金支付}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text115>
        <Text116 Ref="113" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>6.16,15.35,2.11,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text116</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{大额基金支付}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text116>
        <Text117 Ref="114" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>11.26,14.44,2.26,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text117</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{公务员补助支付}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text117>
        <Text118 Ref="115" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>11.3,15.34,2.26,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text118</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{个人账户支付}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text118>
        <Text119 Ref="116" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>16.66,14.38,1.98,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text119</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{乙类自理}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text119>
        <Text120 Ref="117" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>16.63,15.33,2.11,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text120</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{自费费用}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text120>
        <Text121 Ref="118" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>16.59,16.19,2.11,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text121</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{个人自付}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text121>
        <Text122 Ref="119" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>3.25,16.2,4.93,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text122</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{本次报销总额}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text122>
        <Text123 Ref="120" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>11.33,16.18,2.25,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text123</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{现金支付}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text123>
        <Text124 Ref="121" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>3.29,17.12,10.18,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text124</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{本次报销总额大写}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text124>
        <Text125 Ref="122" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>16.58,17.12,2.01,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text125</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{合计}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text125>
        <Text126 Ref="123" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>2.56,18.04,2.17,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text126</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{年度报销累计}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text126>
        <Text127 Ref="124" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>7.83,18.06,1.78,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text127</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{统筹基金支付累计}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text127>
        <Text128 Ref="125" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>11.79,18.02,1.85,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text128</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{大额基金支付累计}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text128>
        <Text129 Ref="126" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>16.54,18,2.2,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text129</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{超大额封顶线公务员补助累计}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text129>
        <Text130 Ref="127" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>7.86,1.69,3.49,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text130</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{住院单号}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text130>
        <Text131 Ref="128" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0.92,1.67,4.49,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text131</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{编号}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text131>
        <Text23 Ref="129" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>15.18,7.09,1.74,1.4</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text23</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>转诊先自付</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text23>
        <Text141 Ref="130" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0.1,7.8,2.53,0.94</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>570ff397bdb54981b96185a981d86efd</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text141</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>本次医疗蒙中医药
及蒙中医诊疗项目
费用总额</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
        </Text141>
        <Text142 Ref="131" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>2.8,7.9,2.73,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>82d7c948c0c441de884264048cf7a4e4</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text142</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{本次医疗蒙中医药及蒙中医诊疗项目费用总额}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text142>
        <Text140 Ref="132" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>6,7.8,2.83,0.94</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>71065d02996b4351ad8f7b825b9b7a1b</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text140</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>本次医疗蒙中医药及
蒙中医诊疗项目费用
占比</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
        </Text140>
        <Text144 Ref="133" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>9.2,8,2.44,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>bb82cbd8e24b42928a3d0126fb6297c0</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text144</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{本次医疗蒙中医药及蒙中医诊疗项目费用占比}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text144>
        <Text143 Ref="134" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>5.1,9.7,4,0.41</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>ce4a842b8ef84a7b88db5efccda4f02c</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text143</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>其中蒙中医药符合基本医疗费用</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text143>
        <Text145 Ref="135" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>10.3,9.7,4,0.41</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>4bb22b04663b402a89a222ff2172ca3a</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text145</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>其中西医药符合基本医疗费用</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text145>
        <Text146 Ref="136" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>5.05,10,1.7,0.61</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,6</Font>
          <Guid>e80fd02e1eec48319b1b104c574d051c</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text146</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>蒙中医药进入
分段费用总额</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text146>
        <Text147 Ref="137" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>6.85,10,1,0.61</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,6</Font>
          <Guid>3c93b92081d34ff29afebd64ef3c88bc</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text147</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>报销比例</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text147>
        <Text148 Ref="138" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>7.95,10,1.1,0.61</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,6</Font>
          <Guid>5e65bc2084264f09b7019450ce8a1fe1</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text148</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>分段基金
支付</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text148>
        <Text149 Ref="139" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>9.15,10,1.1,0.61</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,6</Font>
          <Guid>b4aa3119a60a41f58acbe4ffa5ba8994</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text149</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>分段个人
自付</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text149>
        <Text150 Ref="140" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>10.3,10,1.7,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,6</Font>
          <Guid>32a84f23866b4373a41060e5ade110c2</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text150</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>西医药进入分
段费用总额</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text150>
        <Text151 Ref="141" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>12.1,10,1,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,6</Font>
          <Guid>f38872112f0f4dfbbc5fdc8c6b498c89</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text151</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>报销比例</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text151>
        <Text152 Ref="142" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>13.2,10,1.1,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,6</Font>
          <Guid>a6252e20e90e40a5ad41e72e87da6052</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text152</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>分段基金
支付</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text152>
        <Text153 Ref="143" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>14.4,10,1.1,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,6</Font>
          <Guid>a8b85d8cf25c484f89b7ff2ef047bd61</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text153</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>分段个人
自付</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text153>
        <Text31 Ref="144" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>14.4,10.8,1.1,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>3432bd187d5046a2b551c157d17043a8</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text31</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{西医药分段个人自付1}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text31>
        <Text34 Ref="145" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>14.4,11.7,1.1,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>b21eaf6026b64380a9c2813c1b49c4d4</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text34</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{西医药分段个人自付2}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text34>
        <Text154 Ref="146" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>14.4,12.6,1.1,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>b78b030102014a168708c1d5cf58267e</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text154</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{西医药分段个人自付3}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text154>
        <Text155 Ref="147" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>12.1,10.8,1,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>c5f019b354274daaae5a5e8cb8edce1a</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text155</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{西医药报销比例1}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text155>
        <Text156 Ref="148" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>12.1,11.7,1,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>b96f7b3888d544e9adb3271c3c3505f7</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text156</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{西医药报销比例2}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text156>
        <Text157 Ref="149" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>12.1,12.6,1,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>afe37509e8ba4bdea98cfc73826fefa8</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text157</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{西医药报销比例3}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text157>
        <Text159 Ref="150" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>5.05,10.8,1.7,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>03cea89820834d89ac7157a0648535e4</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text159</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{蒙中医药进入分段费用总额1}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text159>
        <Text158 Ref="151" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>5.1,11.7,1.7,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>d97efb051a1d4b809d3fd2f36c8994cb</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text158</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{蒙中医药进入分段费用总额2}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text158>
        <Text160 Ref="152" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>5.1,12.6,1.7,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>ed02b073cc3c4f5e94265218623c4bda</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text160</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{蒙中医药进入分段费用总额3}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text160>
        <Text161 Ref="153" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>10.3,10.8,1.7,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>067f49296c404aa08fceb7905742abe1</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text161</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{西医药进入分段费用总额1}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text161>
        <Text162 Ref="154" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>10.3,11.7,1.7,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>3c814b3b52d7418a8387d140fd3dd4ce</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text162</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{西医药进入分段费用总额2}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text162>
        <Text163 Ref="155" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>10.3,12.6,1.7,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>ca75e101a9344911be8870c4f417e26a</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text163</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{西医药进入分段费用总额3}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text163>
        <Text164 Ref="156" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>8,10.8,1,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>02d08e5790334db09c6e3fa4be70b1a3</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text164</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{蒙中医药分段基金支付1}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text164>
        <Text165 Ref="157" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>8,11.6,1,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>2eb58735f3444e3d8a146f35fd9d4426</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text165</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{蒙中医药分段基金支付2}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text165>
        <Text166 Ref="158" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>8,12.6,1,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>a9a2c51c0a9e4d339b51803c87f80365</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text166</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{蒙中医药分段基金支付3}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text166>
        <Text167 Ref="159" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>13.2,10.8,1.1,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>5820daedf559401eb94927444f550cf2</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text167</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{西医药分段基金支付1}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text167>
        <Text168 Ref="160" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>13.2,11.7,1.1,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>2ddef9ad68074bafb24d62ec664c3369</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text168</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{西医药分段基金支付2}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text168>
        <Text169 Ref="161" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>13.2,12.6,1.1,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>614d7bc1fd624f21860de9f5f9570460</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text169</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{西医药分段基金支付3}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text169>
        <Text105 Ref="162" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>17.05,12.6,1.7,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>f8e07590811c4caba9fa6e9771d8c3ad</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text105</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{分段基金支付3}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text105>
        <Text170 Ref="163" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>9.1,9.7,1.2,0.31</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>ff1be11140ce411bb028300d3d79b196</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text170</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{其中蒙中医药符合基本医疗费用}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text170>
        <Text171 Ref="164" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>14.3,9.7,1.2,0.31</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>97bb688cc0b4442491225f93c3c1e179</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text171</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{其中西医药符合基本医疗费用}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text171>
        <ReportTitleBand1 Ref="165" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,18.8,0.8</ClientRectangle>
          <Components isList="true" count="1">
            <Text1 Ref="166" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>6.82,0,5.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text1</Name>
              <Page isRef="2" />
              <Parent isRef="165" />
              <Text>{名称}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text1>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>ReportTitleBand1</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
        </ReportTitleBand1>
        <HorizontalLinePrimitive1 Ref="167" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>0,2.3,18.8,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="168" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Name>HorizontalLinePrimitive1</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="169" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive1>
        <VerticalLinePrimitive1 Ref="170" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>0,2.3,0.0254,16.4</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="171" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>168bd8311aae458bbb9560ebf77ba1ab</Guid>
          <Name>VerticalLinePrimitive1</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="172" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive1>
        <StartPointPrimitive1 Ref="173" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>0,2.3,0,0</ClientRectangle>
          <Name>StartPointPrimitive1</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>168bd8311aae458bbb9560ebf77ba1ab</ReferenceToGuid>
        </StartPointPrimitive1>
        <EndPointPrimitive1 Ref="174" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>0,18.7,0,0</ClientRectangle>
          <Name>EndPointPrimitive1</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>168bd8311aae458bbb9560ebf77ba1ab</ReferenceToGuid>
        </EndPointPrimitive1>
        <HorizontalLinePrimitive2 Ref="175" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>0,3.2,18.8,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="176" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Name>HorizontalLinePrimitive2</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="177" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive2>
        <VerticalLinePrimitive2 Ref="178" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>18.8,2.3,0.0254,16.4</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="179" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>7015fe4a909241b9b0a758edad0cdb8a</Guid>
          <Name>VerticalLinePrimitive2</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="180" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive2>
        <StartPointPrimitive2 Ref="181" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>18.8,2.3,0,0</ClientRectangle>
          <Name>StartPointPrimitive2</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>7015fe4a909241b9b0a758edad0cdb8a</ReferenceToGuid>
        </StartPointPrimitive2>
        <EndPointPrimitive2 Ref="182" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>18.8,18.7,0,0</ClientRectangle>
          <Name>EndPointPrimitive2</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>7015fe4a909241b9b0a758edad0cdb8a</ReferenceToGuid>
        </EndPointPrimitive2>
        <VerticalLinePrimitive3 Ref="183" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>2.05,2.3,0.0254,3.6</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="184" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>b19207f24b2f401bae839152c398722d</Guid>
          <Name>VerticalLinePrimitive3</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="185" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive3>
        <StartPointPrimitive3 Ref="186" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>2.05,2.3,0,0</ClientRectangle>
          <Name>StartPointPrimitive3</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>b19207f24b2f401bae839152c398722d</ReferenceToGuid>
        </StartPointPrimitive3>
        <EndPointPrimitive3 Ref="187" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>2.05,5.9,0,0</ClientRectangle>
          <Name>EndPointPrimitive3</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>b19207f24b2f401bae839152c398722d</ReferenceToGuid>
        </EndPointPrimitive3>
        <VerticalLinePrimitive4 Ref="188" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>5.7,2.3,0.0254,0.9</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="189" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>449ffca869bf4b919207a70849c66624</Guid>
          <Name>VerticalLinePrimitive4</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="190" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive4>
        <StartPointPrimitive4 Ref="191" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>5.7,2.3,0,0</ClientRectangle>
          <Name>StartPointPrimitive4</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>449ffca869bf4b919207a70849c66624</ReferenceToGuid>
        </StartPointPrimitive4>
        <EndPointPrimitive4 Ref="192" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>5.7,3.2,0,0</ClientRectangle>
          <Name>EndPointPrimitive4</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>449ffca869bf4b919207a70849c66624</ReferenceToGuid>
        </EndPointPrimitive4>
        <VerticalLinePrimitive5 Ref="193" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>6.9,2.3,0.0254,0.9</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="194" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>ed90a0714dd94688947e0918f876ecd9</Guid>
          <Name>VerticalLinePrimitive5</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="195" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive5>
        <StartPointPrimitive5 Ref="196" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>6.9,2.3,0,0</ClientRectangle>
          <Name>StartPointPrimitive5</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>ed90a0714dd94688947e0918f876ecd9</ReferenceToGuid>
        </StartPointPrimitive5>
        <EndPointPrimitive5 Ref="197" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>6.9,3.2,0,0</ClientRectangle>
          <Name>EndPointPrimitive5</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>ed90a0714dd94688947e0918f876ecd9</ReferenceToGuid>
        </EndPointPrimitive5>
        <VerticalLinePrimitive6 Ref="198" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>9.65,2.3,0.0254,0.9</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="199" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>ae3d668dd285457faf191066f6ad61d6</Guid>
          <Name>VerticalLinePrimitive6</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="200" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive6>
        <StartPointPrimitive6 Ref="201" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>9.65,2.3,0,0</ClientRectangle>
          <Name>StartPointPrimitive6</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>ae3d668dd285457faf191066f6ad61d6</ReferenceToGuid>
        </StartPointPrimitive6>
        <EndPointPrimitive6 Ref="202" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>9.7,3.2,0,0</ClientRectangle>
          <Name>EndPointPrimitive6</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>ae3d668dd285457faf191066f6ad61d6</ReferenceToGuid>
        </EndPointPrimitive6>
        <VerticalLinePrimitive7 Ref="203" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>10.85,2.3,0.0254,0.9</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="204" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>1505fe22d3004949a335cd33818201d1</Guid>
          <Name>VerticalLinePrimitive7</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="205" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive7>
        <StartPointPrimitive7 Ref="206" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>10.85,2.3,0,0</ClientRectangle>
          <Name>StartPointPrimitive7</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>1505fe22d3004949a335cd33818201d1</ReferenceToGuid>
        </StartPointPrimitive7>
        <EndPointPrimitive7 Ref="207" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>10.85,3.2,0,0</ClientRectangle>
          <Name>EndPointPrimitive7</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>1505fe22d3004949a335cd33818201d1</ReferenceToGuid>
        </EndPointPrimitive7>
        <VerticalLinePrimitive8 Ref="208" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>12,2.3,0.0254,0.9</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="209" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>0fb939c850154397ad17f6ae4ca7a17a</Guid>
          <Name>VerticalLinePrimitive8</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="210" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive8>
        <StartPointPrimitive8 Ref="211" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>12,2.3,0,0</ClientRectangle>
          <Name>StartPointPrimitive8</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>0fb939c850154397ad17f6ae4ca7a17a</ReferenceToGuid>
        </StartPointPrimitive8>
        <EndPointPrimitive8 Ref="212" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>13.1,3.2,0,0</ClientRectangle>
          <Name>EndPointPrimitive8</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>0fb939c850154397ad17f6ae4ca7a17a</ReferenceToGuid>
        </EndPointPrimitive8>
        <VerticalLinePrimitive9 Ref="213" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>13,2.3,0.0254,0.9</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="214" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>b07a7db015d84f8296ff70d4029279e3</Guid>
          <Name>VerticalLinePrimitive9</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="215" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive9>
        <StartPointPrimitive9 Ref="216" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>13,2.3,0,0</ClientRectangle>
          <Name>StartPointPrimitive9</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>b07a7db015d84f8296ff70d4029279e3</ReferenceToGuid>
        </StartPointPrimitive9>
        <EndPointPrimitive9 Ref="217" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>14.2,3.2,0,0</ClientRectangle>
          <Name>EndPointPrimitive9</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>b07a7db015d84f8296ff70d4029279e3</ReferenceToGuid>
        </EndPointPrimitive9>
        <VerticalLinePrimitive10 Ref="218" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>14.15,2.3,0.0254,1.8</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="219" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>e2cd15a85ff44e9f967f6a8c67967d2a</Guid>
          <Name>VerticalLinePrimitive10</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="220" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive10>
        <StartPointPrimitive10 Ref="221" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>14.15,2.3,0,0</ClientRectangle>
          <Name>StartPointPrimitive10</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>e2cd15a85ff44e9f967f6a8c67967d2a</ReferenceToGuid>
        </StartPointPrimitive10>
        <EndPointPrimitive10 Ref="222" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>14.15,4.1,0,0</ClientRectangle>
          <Name>EndPointPrimitive10</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>e2cd15a85ff44e9f967f6a8c67967d2a</ReferenceToGuid>
        </EndPointPrimitive10>
        <VerticalLinePrimitive11 Ref="223" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>16.2,2.3,0.0254,1.8</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="224" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>0e73fea7a5b74f6baf66bfa65241b28b</Guid>
          <Name>VerticalLinePrimitive11</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="225" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive11>
        <StartPointPrimitive11 Ref="226" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>16.2,2.3,0,0</ClientRectangle>
          <Name>StartPointPrimitive11</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>0e73fea7a5b74f6baf66bfa65241b28b</ReferenceToGuid>
        </StartPointPrimitive11>
        <EndPointPrimitive11 Ref="227" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>16.2,4.1,0,0</ClientRectangle>
          <Name>EndPointPrimitive11</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>0e73fea7a5b74f6baf66bfa65241b28b</ReferenceToGuid>
        </EndPointPrimitive11>
        <HorizontalLinePrimitive3 Ref="228" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>0,4.1,18.8,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="229" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Name>HorizontalLinePrimitive3</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="230" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive3>
        <HorizontalLinePrimitive4 Ref="231" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>0,5,18.8,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="232" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Name>HorizontalLinePrimitive4</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="233" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive4>
        <HorizontalLinePrimitive5 Ref="234" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>0,5.9,18.8,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="235" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Name>HorizontalLinePrimitive5</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="236" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive5>
        <VerticalLinePrimitive12 Ref="237" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>9.86,3.2,0.0254,0.91</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="238" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>48419022ec16489ebb5cf0d491155d8f</Guid>
          <Name>VerticalLinePrimitive12</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="239" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive12>
        <StartPointPrimitive12 Ref="240" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>9.86,3.2,0,0</ClientRectangle>
          <Name>StartPointPrimitive12</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>48419022ec16489ebb5cf0d491155d8f</ReferenceToGuid>
        </StartPointPrimitive12>
        <EndPointPrimitive12 Ref="241" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>9.66,4.11,0,0</ClientRectangle>
          <Name>EndPointPrimitive12</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>48419022ec16489ebb5cf0d491155d8f</ReferenceToGuid>
        </EndPointPrimitive12>
        <VerticalLinePrimitive13 Ref="242" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>5.65,4.1,0.0254,4.7</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="243" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>c4407cb76e0640e4b3d9431d9b63a75f</Guid>
          <Name>VerticalLinePrimitive13</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="244" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive13>
        <StartPointPrimitive13 Ref="245" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>5.65,4.1,0,0</ClientRectangle>
          <Name>StartPointPrimitive13</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>c4407cb76e0640e4b3d9431d9b63a75f</ReferenceToGuid>
        </StartPointPrimitive13>
        <EndPointPrimitive13 Ref="246" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>5.65,8.8,0,0</ClientRectangle>
          <Name>EndPointPrimitive13</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>c4407cb76e0640e4b3d9431d9b63a75f</ReferenceToGuid>
        </EndPointPrimitive13>
        <VerticalLinePrimitive14 Ref="247" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>7.55,4.09,0.0254,1.8</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="248" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>9ea75a36916248f99eb127f200b9d8ca</Guid>
          <Name>VerticalLinePrimitive14</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="249" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive14>
        <StartPointPrimitive14 Ref="250" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>7.55,4.09,0,0</ClientRectangle>
          <Name>StartPointPrimitive14</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>9ea75a36916248f99eb127f200b9d8ca</ReferenceToGuid>
        </StartPointPrimitive14>
        <EndPointPrimitive14 Ref="251" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>7.55,5.89,0,0</ClientRectangle>
          <Name>EndPointPrimitive14</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>9ea75a36916248f99eb127f200b9d8ca</ReferenceToGuid>
        </EndPointPrimitive14>
        <VerticalLinePrimitive15 Ref="252" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>13.35,4.1,0.0254,4.7</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="253" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>d3b7e8751da647e8996b87e69c6984bc</Guid>
          <Name>VerticalLinePrimitive15</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="254" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive15>
        <StartPointPrimitive15 Ref="255" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>13.35,4.1,0,0</ClientRectangle>
          <Name>StartPointPrimitive15</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>d3b7e8751da647e8996b87e69c6984bc</ReferenceToGuid>
        </StartPointPrimitive15>
        <EndPointPrimitive15 Ref="256" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>13.35,8.8,0,0</ClientRectangle>
          <Name>EndPointPrimitive15</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>d3b7e8751da647e8996b87e69c6984bc</ReferenceToGuid>
        </EndPointPrimitive15>
        <VerticalLinePrimitive16 Ref="257" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>15.1,5,0.0254,0.9</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="258" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>e5b65bb9bb5c437b945c940daf8b91e9</Guid>
          <Name>VerticalLinePrimitive16</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="259" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive16>
        <StartPointPrimitive16 Ref="260" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>15.1,5,0,0</ClientRectangle>
          <Name>StartPointPrimitive16</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>e5b65bb9bb5c437b945c940daf8b91e9</ReferenceToGuid>
        </StartPointPrimitive16>
        <EndPointPrimitive16 Ref="261" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>15.1,5.9,0,0</ClientRectangle>
          <Name>EndPointPrimitive16</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>e5b65bb9bb5c437b945c940daf8b91e9</ReferenceToGuid>
        </EndPointPrimitive16>
        <VerticalLinePrimitive17 Ref="262" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>16.9,5,0.0254,0.9</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="263" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>09048c5300fe4d82bae2d5ee0b237cd1</Guid>
          <Name>VerticalLinePrimitive17</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="264" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive17>
        <StartPointPrimitive17 Ref="265" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>16.9,5,0,0</ClientRectangle>
          <Name>StartPointPrimitive17</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>09048c5300fe4d82bae2d5ee0b237cd1</ReferenceToGuid>
        </StartPointPrimitive17>
        <EndPointPrimitive17 Ref="266" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>16.9,5.9,0,0</ClientRectangle>
          <Name>EndPointPrimitive17</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>09048c5300fe4d82bae2d5ee0b237cd1</ReferenceToGuid>
        </EndPointPrimitive17>
        <VerticalLinePrimitive18 Ref="267" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>2.75,5.9,0.0254,2.9</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="268" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>9cf8dd166a254d97882a3d33b415aa79</Guid>
          <Name>VerticalLinePrimitive18</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="269" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive18>
        <StartPointPrimitive18 Ref="270" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>2.75,5.9,0,0</ClientRectangle>
          <Name>StartPointPrimitive18</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>9cf8dd166a254d97882a3d33b415aa79</ReferenceToGuid>
        </StartPointPrimitive18>
        <EndPointPrimitive18 Ref="271" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>2.75,8.8,0,0</ClientRectangle>
          <Name>EndPointPrimitive18</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>9cf8dd166a254d97882a3d33b415aa79</ReferenceToGuid>
        </EndPointPrimitive18>
        <HorizontalLinePrimitive6 Ref="272" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>0,6.8,18.8,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="273" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Name>HorizontalLinePrimitive6</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="274" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive6>
        <HorizontalLinePrimitive7 Ref="275" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>0,8.8,18.8,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="276" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Name>HorizontalLinePrimitive7</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="277" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive7>
        <HorizontalLinePrimitive8 Ref="278" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>0,9.7,18.8,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="279" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Name>HorizontalLinePrimitive8</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="280" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive8>
        <VerticalLinePrimitive19 Ref="281" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>9.15,5.9,0.0254,2.9</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="282" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>f13723d5c75f4377a57d4702c1911f6c</Guid>
          <Name>VerticalLinePrimitive19</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="283" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive19>
        <StartPointPrimitive19 Ref="284" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>9.15,5.9,0,0</ClientRectangle>
          <Name>StartPointPrimitive19</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>f13723d5c75f4377a57d4702c1911f6c</ReferenceToGuid>
        </StartPointPrimitive19>
        <EndPointPrimitive19 Ref="285" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>9.15,8.8,0,0</ClientRectangle>
          <Name>EndPointPrimitive19</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>f13723d5c75f4377a57d4702c1911f6c</ReferenceToGuid>
        </EndPointPrimitive19>
        <VerticalLinePrimitive20 Ref="286" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>16.45,5.9,0.0254,0.9</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="287" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>e7865635d5c54bd0a74af05d21f963dc</Guid>
          <Name>VerticalLinePrimitive20</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="288" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive20>
        <StartPointPrimitive20 Ref="289" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>16.45,5.9,0,0</ClientRectangle>
          <Name>StartPointPrimitive20</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>e7865635d5c54bd0a74af05d21f963dc</ReferenceToGuid>
        </StartPointPrimitive20>
        <EndPointPrimitive20 Ref="290" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>16.45,6.8,0,0</ClientRectangle>
          <Name>EndPointPrimitive20</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>e7865635d5c54bd0a74af05d21f963dc</ReferenceToGuid>
        </EndPointPrimitive20>
        <VerticalLinePrimitive21 Ref="291" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>11.7,6.8,0.0254,2</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="292" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>5372ffe9613241ba964626108ae50589</Guid>
          <Name>VerticalLinePrimitive21</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="293" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive21>
        <StartPointPrimitive21 Ref="294" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>11.7,6.8,0,0</ClientRectangle>
          <Name>StartPointPrimitive21</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>5372ffe9613241ba964626108ae50589</ReferenceToGuid>
        </StartPointPrimitive21>
        <EndPointPrimitive21 Ref="295" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>11.7,8.8,0,0</ClientRectangle>
          <Name>EndPointPrimitive21</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>5372ffe9613241ba964626108ae50589</ReferenceToGuid>
        </EndPointPrimitive21>
        <VerticalLinePrimitive22 Ref="296" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>15.1,6.8,0.0254,2</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="297" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>3299b54df8bd4ba7b1e8f5c74f58f1e3</Guid>
          <Name>VerticalLinePrimitive22</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="298" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive22>
        <StartPointPrimitive22 Ref="299" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>15.1,6.8,0,0</ClientRectangle>
          <Name>StartPointPrimitive22</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>3299b54df8bd4ba7b1e8f5c74f58f1e3</ReferenceToGuid>
        </StartPointPrimitive22>
        <EndPointPrimitive22 Ref="300" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>15.1,8.8,0,0</ClientRectangle>
          <Name>EndPointPrimitive22</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>3299b54df8bd4ba7b1e8f5c74f58f1e3</ReferenceToGuid>
        </EndPointPrimitive22>
        <VerticalLinePrimitive23 Ref="301" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>17.15,6.8,0.0254,2</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="302" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>134d1c2231354998a310f25c163b63c1</Guid>
          <Name>VerticalLinePrimitive23</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="303" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive23>
        <StartPointPrimitive23 Ref="304" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>17.15,6.8,0,0</ClientRectangle>
          <Name>StartPointPrimitive23</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>134d1c2231354998a310f25c163b63c1</ReferenceToGuid>
        </StartPointPrimitive23>
        <EndPointPrimitive23 Ref="305" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>17.15,8.8,0,0</ClientRectangle>
          <Name>EndPointPrimitive23</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>134d1c2231354998a310f25c163b63c1</ReferenceToGuid>
        </EndPointPrimitive23>
        <VerticalLinePrimitive24 Ref="306" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>0.75,9.7,0.0254,3.6</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="307" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>9bc4eb0b80044a77b0188ea237c41c5c</Guid>
          <Name>VerticalLinePrimitive24</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="308" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive24>
        <StartPointPrimitive24 Ref="309" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>0.75,9.7,0,0</ClientRectangle>
          <Name>StartPointPrimitive24</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>9bc4eb0b80044a77b0188ea237c41c5c</ReferenceToGuid>
        </StartPointPrimitive24>
        <EndPointPrimitive24 Ref="310" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>0.75,13.3,0,0</ClientRectangle>
          <Name>EndPointPrimitive24</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>9bc4eb0b80044a77b0188ea237c41c5c</ReferenceToGuid>
        </EndPointPrimitive24>
        <HorizontalLinePrimitive9 Ref="311" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>0,13.3,18.8,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="312" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Name>HorizontalLinePrimitive9</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="313" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive9>
        <HorizontalLinePrimitive10 Ref="314" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>0.75,10.6,18.05,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="315" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Name>HorizontalLinePrimitive10</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="316" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive10>
        <HorizontalLinePrimitive11 Ref="317" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>0.75,11.5,18.05,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="318" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>d8230717a9844d69a8657cf8c81d040d</Guid>
          <Name>HorizontalLinePrimitive11</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="319" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive11>
        <HorizontalLinePrimitive12 Ref="320" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>0.75,12.4,18.05,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="321" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>8e079da3d2ac46129f8c5c63689f2d3b</Guid>
          <Name>HorizontalLinePrimitive12</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="322" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive12>
        <VerticalLinePrimitive25 Ref="323" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>2.85,9.7,0.0254,3.6</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="324" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>c1c9bee6366f484a8e52cdb06bb7fab8</Guid>
          <Name>VerticalLinePrimitive25</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="325" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive25>
        <StartPointPrimitive25 Ref="326" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>2.85,9.7,0,0</ClientRectangle>
          <Name>StartPointPrimitive25</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>c1c9bee6366f484a8e52cdb06bb7fab8</ReferenceToGuid>
        </StartPointPrimitive25>
        <EndPointPrimitive25 Ref="327" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>2.85,13.3,0,0</ClientRectangle>
          <Name>EndPointPrimitive25</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>c1c9bee6366f484a8e52cdb06bb7fab8</ReferenceToGuid>
        </EndPointPrimitive25>
        <VerticalLinePrimitive26 Ref="328" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>5.05,9.7,0.0254,3.6</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="329" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>f359b80936f242c8b99db049ce74e6b6</Guid>
          <Name>VerticalLinePrimitive26</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="330" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive26>
        <StartPointPrimitive26 Ref="331" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>5.05,9.7,0,0</ClientRectangle>
          <Name>StartPointPrimitive26</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>f359b80936f242c8b99db049ce74e6b6</ReferenceToGuid>
        </StartPointPrimitive26>
        <EndPointPrimitive26 Ref="332" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>5.05,13.3,0,0</ClientRectangle>
          <Name>EndPointPrimitive26</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>f359b80936f242c8b99db049ce74e6b6</ReferenceToGuid>
        </EndPointPrimitive26>
        <VerticalLinePrimitive27 Ref="333" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>10.3,9.7,0.0254,3.6</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="334" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>aaccc76961e74a2b94a5f162b00c5a8b</Guid>
          <Name>VerticalLinePrimitive27</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="335" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive27>
        <StartPointPrimitive27 Ref="336" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>10.3,9.7,0,0</ClientRectangle>
          <Name>StartPointPrimitive27</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>aaccc76961e74a2b94a5f162b00c5a8b</ReferenceToGuid>
        </StartPointPrimitive27>
        <EndPointPrimitive27 Ref="337" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>10.3,13.3,0,0</ClientRectangle>
          <Name>EndPointPrimitive27</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>aaccc76961e74a2b94a5f162b00c5a8b</ReferenceToGuid>
        </EndPointPrimitive27>
        <VerticalLinePrimitive28 Ref="338" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>13.65,13.3,0.0254,5.4</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="339" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>3e3702fbcd254abf88d0b96f69f919a7</Guid>
          <Name>VerticalLinePrimitive28</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="340" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive28>
        <StartPointPrimitive28 Ref="341" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>13.65,13.3,0,0</ClientRectangle>
          <Name>StartPointPrimitive28</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>3e3702fbcd254abf88d0b96f69f919a7</ReferenceToGuid>
        </StartPointPrimitive28>
        <EndPointPrimitive28 Ref="342" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>13.65,18.7,0,0</ClientRectangle>
          <Name>EndPointPrimitive28</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>3e3702fbcd254abf88d0b96f69f919a7</ReferenceToGuid>
        </EndPointPrimitive28>
        <VerticalLinePrimitive29 Ref="343" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>16.5,13.3,0.0254,5.4</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="344" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>381a8e32752d4768a5c118b8977b50b0</Guid>
          <Name>VerticalLinePrimitive29</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="345" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive29>
        <StartPointPrimitive29 Ref="346" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>16.5,13.3,0,0</ClientRectangle>
          <Name>StartPointPrimitive29</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>381a8e32752d4768a5c118b8977b50b0</ReferenceToGuid>
        </StartPointPrimitive29>
        <EndPointPrimitive29 Ref="347" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>16.5,18.7,0,0</ClientRectangle>
          <Name>EndPointPrimitive29</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>381a8e32752d4768a5c118b8977b50b0</ReferenceToGuid>
        </EndPointPrimitive29>
        <HorizontalLinePrimitive13 Ref="348" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>0,14.2,18.8,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="349" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Name>HorizontalLinePrimitive13</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="350" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive13>
        <VerticalLinePrimitive30 Ref="351" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>3.15,13.3,0.0254,4.5</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="352" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>b32f914d85b14e6c85c4a840d3ea7181</Guid>
          <Name>VerticalLinePrimitive30</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="353" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive30>
        <StartPointPrimitive30 Ref="354" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>3.15,13.3,0,0</ClientRectangle>
          <Name>StartPointPrimitive30</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>b32f914d85b14e6c85c4a840d3ea7181</ReferenceToGuid>
        </StartPointPrimitive30>
        <EndPointPrimitive30 Ref="355" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>3.15,17.8,0,0</ClientRectangle>
          <Name>EndPointPrimitive30</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>b32f914d85b14e6c85c4a840d3ea7181</ReferenceToGuid>
        </EndPointPrimitive30>
        <VerticalLinePrimitive31 Ref="356" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>6,13.3,0.0254,2.7</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="357" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>0d32260479254158972e7997b567dcf1</Guid>
          <Name>VerticalLinePrimitive31</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="358" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive31>
        <StartPointPrimitive31 Ref="359" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>6,13.3,0,0</ClientRectangle>
          <Name>StartPointPrimitive31</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>0d32260479254158972e7997b567dcf1</ReferenceToGuid>
        </StartPointPrimitive31>
        <EndPointPrimitive31 Ref="360" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>6,16,0,0</ClientRectangle>
          <Name>EndPointPrimitive31</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>0d32260479254158972e7997b567dcf1</ReferenceToGuid>
        </EndPointPrimitive31>
        <HorizontalLinePrimitive14 Ref="361" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>0,16,13.65,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="362" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Name>HorizontalLinePrimitive14</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="363" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive14>
        <HorizontalLinePrimitive15 Ref="364" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>3.15,15.1,10.5,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="365" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Name>HorizontalLinePrimitive15</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="366" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive15>
        <VerticalLinePrimitive32 Ref="367" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>8.4,14.2,0.0254,2.7</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="368" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>27435a4824ac42a3b817c15a7e767974</Guid>
          <Name>VerticalLinePrimitive32</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="369" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive32>
        <StartPointPrimitive32 Ref="370" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>8.4,14.2,0,0</ClientRectangle>
          <Name>StartPointPrimitive32</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>27435a4824ac42a3b817c15a7e767974</ReferenceToGuid>
        </StartPointPrimitive32>
        <EndPointPrimitive32 Ref="371" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>8.4,16.9,0,0</ClientRectangle>
          <Name>EndPointPrimitive32</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>27435a4824ac42a3b817c15a7e767974</ReferenceToGuid>
        </EndPointPrimitive32>
        <VerticalLinePrimitive33 Ref="372" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>11.25,14.2,0.0254,2.7</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="373" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>87c839aa4fa545548f09aa6aa912d5b4</Guid>
          <Name>VerticalLinePrimitive33</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="374" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive33>
        <StartPointPrimitive33 Ref="375" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>11.25,14.2,0,0</ClientRectangle>
          <Name>StartPointPrimitive33</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>87c839aa4fa545548f09aa6aa912d5b4</ReferenceToGuid>
        </StartPointPrimitive33>
        <EndPointPrimitive33 Ref="376" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>11.25,16.9,0,0</ClientRectangle>
          <Name>EndPointPrimitive33</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>87c839aa4fa545548f09aa6aa912d5b4</ReferenceToGuid>
        </EndPointPrimitive33>
        <HorizontalLinePrimitive16 Ref="377" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>0,16.9,13.65,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="378" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Name>HorizontalLinePrimitive16</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="379" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive16>
        <HorizontalLinePrimitive17 Ref="380" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>0,17.8,18.8,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="381" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Name>HorizontalLinePrimitive17</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="382" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive17>
        <VerticalLinePrimitive34 Ref="383" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>14.55,14.2,0.0254,3.6</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="384" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>218086df90ba4af98cd0269004dded90</Guid>
          <Name>VerticalLinePrimitive34</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="385" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive34>
        <StartPointPrimitive34 Ref="386" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>14.55,14.2,0,0</ClientRectangle>
          <Name>StartPointPrimitive34</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>218086df90ba4af98cd0269004dded90</ReferenceToGuid>
        </StartPointPrimitive34>
        <EndPointPrimitive34 Ref="387" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>14.55,17.8,0,0</ClientRectangle>
          <Name>EndPointPrimitive34</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>218086df90ba4af98cd0269004dded90</ReferenceToGuid>
        </EndPointPrimitive34>
        <HorizontalLinePrimitive18 Ref="388" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>14.55,15.1,4.25,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="389" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Name>HorizontalLinePrimitive18</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="390" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive18>
        <HorizontalLinePrimitive19 Ref="391" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>14.55,16,4.25,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="392" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>406b448fd36e4cf88321b700ccb79ae8</Guid>
          <Name>HorizontalLinePrimitive19</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="393" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive19>
        <HorizontalLinePrimitive20 Ref="394" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>14.55,16.9,4.25,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="395" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>7d76bfc7faa5495d8f809173b39d7e49</Guid>
          <Name>HorizontalLinePrimitive20</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="396" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive20>
        <HorizontalLinePrimitive21 Ref="397" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>0,18.7,18.8,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="398" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Name>HorizontalLinePrimitive21</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="399" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive21>
        <VerticalLinePrimitive35 Ref="400" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>2.5,17.8,0.0254,0.9</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="401" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>4c653c5dd66d4aa19afafdce7f84c4b9</Guid>
          <Name>VerticalLinePrimitive35</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="402" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive35>
        <StartPointPrimitive35 Ref="403" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>2.5,17.8,0,0</ClientRectangle>
          <Name>StartPointPrimitive35</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>4c653c5dd66d4aa19afafdce7f84c4b9</ReferenceToGuid>
        </StartPointPrimitive35>
        <EndPointPrimitive35 Ref="404" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>2.5,18.7,0,0</ClientRectangle>
          <Name>EndPointPrimitive35</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>4c653c5dd66d4aa19afafdce7f84c4b9</ReferenceToGuid>
        </EndPointPrimitive35>
        <VerticalLinePrimitive36 Ref="405" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>4.8,17.8,0.0254,0.9</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="406" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>7e2e45b4062d4ed78a812db150a75014</Guid>
          <Name>VerticalLinePrimitive36</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="407" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive36>
        <StartPointPrimitive36 Ref="408" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>4.8,17.8,0,0</ClientRectangle>
          <Name>StartPointPrimitive36</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>7e2e45b4062d4ed78a812db150a75014</ReferenceToGuid>
        </StartPointPrimitive36>
        <EndPointPrimitive36 Ref="409" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>4.8,18.7,0,0</ClientRectangle>
          <Name>EndPointPrimitive36</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>7e2e45b4062d4ed78a812db150a75014</ReferenceToGuid>
        </EndPointPrimitive36>
        <VerticalLinePrimitive37 Ref="410" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>7.7,17.8,0.0254,0.9</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="411" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>a61bcef5d216463aab3237e85920e9be</Guid>
          <Name>VerticalLinePrimitive37</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="412" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive37>
        <StartPointPrimitive37 Ref="413" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>7.7,17.8,0,0</ClientRectangle>
          <Name>StartPointPrimitive37</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>a61bcef5d216463aab3237e85920e9be</ReferenceToGuid>
        </StartPointPrimitive37>
        <EndPointPrimitive37 Ref="414" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>7.7,18.7,0,0</ClientRectangle>
          <Name>EndPointPrimitive37</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>a61bcef5d216463aab3237e85920e9be</ReferenceToGuid>
        </EndPointPrimitive37>
        <VerticalLinePrimitive38 Ref="415" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>9.7,17.8,0.0254,0.9</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="416" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>baef33ad8bea452e81336926b0ef14b6</Guid>
          <Name>VerticalLinePrimitive38</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="417" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive38>
        <StartPointPrimitive38 Ref="418" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>9.7,17.8,0,0</ClientRectangle>
          <Name>StartPointPrimitive38</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>baef33ad8bea452e81336926b0ef14b6</ReferenceToGuid>
        </StartPointPrimitive38>
        <EndPointPrimitive38 Ref="419" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>9.7,18.7,0,0</ClientRectangle>
          <Name>EndPointPrimitive38</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>baef33ad8bea452e81336926b0ef14b6</ReferenceToGuid>
        </EndPointPrimitive38>
        <VerticalLinePrimitive39 Ref="420" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>11.7,17.8,0.0254,0.91</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="421" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>989a24d07e3a44858c0f86a173df90a7</Guid>
          <Name>VerticalLinePrimitive39</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="422" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive39>
        <StartPointPrimitive39 Ref="423" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>11.7,17.8,0,0</ClientRectangle>
          <Name>StartPointPrimitive39</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>989a24d07e3a44858c0f86a173df90a7</ReferenceToGuid>
        </StartPointPrimitive39>
        <EndPointPrimitive39 Ref="424" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>11.7,18.71,0,0</ClientRectangle>
          <Name>EndPointPrimitive39</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>989a24d07e3a44858c0f86a173df90a7</ReferenceToGuid>
        </EndPointPrimitive39>
        <VerticalLinePrimitive40 Ref="425" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>6.31,3.2,0.0254,0.9</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="426" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>2ba56b89eb934b248afb5f530cd96682</Guid>
          <Name>VerticalLinePrimitive40</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="427" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive40>
        <StartPointPrimitive40 Ref="428" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>6.31,3.2,0,0</ClientRectangle>
          <Name>StartPointPrimitive40</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>2ba56b89eb934b248afb5f530cd96682</ReferenceToGuid>
        </StartPointPrimitive40>
        <EndPointPrimitive40 Ref="429" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>6.11,4.1,0,0</ClientRectangle>
          <Name>EndPointPrimitive40</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>2ba56b89eb934b248afb5f530cd96682</ReferenceToGuid>
        </EndPointPrimitive40>
        <VerticalLinePrimitive41 Ref="430" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>8.1,3.2,0.0254,0.9</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="431" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>1f24d9feb9de4dd38d78b138baedf21f</Guid>
          <Name>VerticalLinePrimitive41</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="432" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive41>
        <StartPointPrimitive41 Ref="433" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>8.1,3.2,0,0</ClientRectangle>
          <Name>StartPointPrimitive41</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>1f24d9feb9de4dd38d78b138baedf21f</ReferenceToGuid>
        </StartPointPrimitive41>
        <EndPointPrimitive41 Ref="434" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>7.9,4.1,0,0</ClientRectangle>
          <Name>EndPointPrimitive41</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>1f24d9feb9de4dd38d78b138baedf21f</ReferenceToGuid>
        </EndPointPrimitive41>
        <VerticalLinePrimitive42 Ref="435" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>11.79,3.2,0.0254,0.9</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="436" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>7ff800edf3d94b58ab1cdbf26018b8cc</Guid>
          <Name>VerticalLinePrimitive42</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="437" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive42>
        <StartPointPrimitive42 Ref="438" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>11.79,3.2,0,0</ClientRectangle>
          <Name>StartPointPrimitive42</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>7ff800edf3d94b58ab1cdbf26018b8cc</ReferenceToGuid>
        </StartPointPrimitive42>
        <EndPointPrimitive42 Ref="439" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>11.79,4.1,0,0</ClientRectangle>
          <Name>EndPointPrimitive42</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>7ff800edf3d94b58ab1cdbf26018b8cc</ReferenceToGuid>
        </EndPointPrimitive42>
        <VerticalLinePrimitive43 Ref="440" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>10.85,4.1,0.0254,1.8</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="441" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>c54156cf2da940059273501362ac78d6</Guid>
          <Name>VerticalLinePrimitive43</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="442" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive43>
        <StartPointPrimitive43 Ref="443" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>10.85,4.1,0,0</ClientRectangle>
          <Name>StartPointPrimitive43</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>c54156cf2da940059273501362ac78d6</ReferenceToGuid>
        </StartPointPrimitive43>
        <EndPointPrimitive43 Ref="444" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>10.85,5.9,0,0</ClientRectangle>
          <Name>EndPointPrimitive43</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>c54156cf2da940059273501362ac78d6</ReferenceToGuid>
        </EndPointPrimitive43>
        <HorizontalLinePrimitive23 Ref="445" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>0,7.7,11.7,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="446" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>1382e4b9c4dd4784aecc648f1b338065</Guid>
          <Name>HorizontalLinePrimitive23</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="447" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive23>
        <VerticalLinePrimitive48 Ref="448" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>17,9.7,0.0254,3.6</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="449" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>04833239f8a94b8d815b1e7e61778401</Guid>
          <Name>VerticalLinePrimitive48</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="450" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive48>
        <StartPointPrimitive48 Ref="451" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>17,9.7,0,0</ClientRectangle>
          <Name>StartPointPrimitive48</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>04833239f8a94b8d815b1e7e61778401</ReferenceToGuid>
        </StartPointPrimitive48>
        <EndPointPrimitive48 Ref="452" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>17,13.3,0,0</ClientRectangle>
          <Name>EndPointPrimitive48</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>04833239f8a94b8d815b1e7e61778401</ReferenceToGuid>
        </EndPointPrimitive48>
        <VerticalLinePrimitive49 Ref="453" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>15.5,9.7,0.0254,3.6</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="454" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>40793479c9c34d8b850923e7f27bb962</Guid>
          <Name>VerticalLinePrimitive49</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="455" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive49>
        <StartPointPrimitive49 Ref="456" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>15.5,9.7,0,0</ClientRectangle>
          <Name>StartPointPrimitive49</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>40793479c9c34d8b850923e7f27bb962</ReferenceToGuid>
        </StartPointPrimitive49>
        <EndPointPrimitive49 Ref="457" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>15.5,13.3,0,0</ClientRectangle>
          <Name>EndPointPrimitive49</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>40793479c9c34d8b850923e7f27bb962</ReferenceToGuid>
        </EndPointPrimitive49>
        <HorizontalLinePrimitive24 Ref="458" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>5.05,10,10.45,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="459" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>ce935ec58c9441bb833f9d033e045514</Guid>
          <Name>HorizontalLinePrimitive24</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="460" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive24>
        <VerticalLinePrimitive50 Ref="461" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>6.8,10,0.0254,3.3</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="462" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>72c746a811c7460d8097afdba67649a3</Guid>
          <Name>VerticalLinePrimitive50</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="463" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive50>
        <StartPointPrimitive50 Ref="464" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>6.8,10,0,0</ClientRectangle>
          <Name>StartPointPrimitive50</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>72c746a811c7460d8097afdba67649a3</ReferenceToGuid>
        </StartPointPrimitive50>
        <EndPointPrimitive50 Ref="465" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>6.8,13.3,0,0</ClientRectangle>
          <Name>EndPointPrimitive50</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>72c746a811c7460d8097afdba67649a3</ReferenceToGuid>
        </EndPointPrimitive50>
        <VerticalLinePrimitive51 Ref="466" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>7.9,10,0.0254,3.3</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="467" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>7043ee00d25c499e8e5cf915f2dc8af4</Guid>
          <Name>VerticalLinePrimitive51</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="468" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive51>
        <StartPointPrimitive51 Ref="469" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>7.9,10,0,0</ClientRectangle>
          <Name>StartPointPrimitive51</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>7043ee00d25c499e8e5cf915f2dc8af4</ReferenceToGuid>
        </StartPointPrimitive51>
        <EndPointPrimitive51 Ref="470" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>7.9,13.3,0,0</ClientRectangle>
          <Name>EndPointPrimitive51</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>7043ee00d25c499e8e5cf915f2dc8af4</ReferenceToGuid>
        </EndPointPrimitive51>
        <VerticalLinePrimitive52 Ref="471" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>9.1,10,0.0254,3.3</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="472" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>808980c8167b4c0aaef8a0f8dae3a182</Guid>
          <Name>VerticalLinePrimitive52</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="473" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive52>
        <StartPointPrimitive52 Ref="474" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>9.1,10,0,0</ClientRectangle>
          <Name>StartPointPrimitive52</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>808980c8167b4c0aaef8a0f8dae3a182</ReferenceToGuid>
        </StartPointPrimitive52>
        <EndPointPrimitive52 Ref="475" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>9.1,13.3,0,0</ClientRectangle>
          <Name>EndPointPrimitive52</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>808980c8167b4c0aaef8a0f8dae3a182</ReferenceToGuid>
        </EndPointPrimitive52>
        <VerticalLinePrimitive53 Ref="476" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>10.4,13.3,0.0254,0.9</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="477" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>be03a4b4c98c4549b8e36cdf99705812</Guid>
          <Name>VerticalLinePrimitive53</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="478" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive53>
        <StartPointPrimitive53 Ref="479" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>10.4,13.3,0,0</ClientRectangle>
          <Name>StartPointPrimitive53</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>be03a4b4c98c4549b8e36cdf99705812</ReferenceToGuid>
        </StartPointPrimitive53>
        <EndPointPrimitive53 Ref="480" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>10.4,14.2,0,0</ClientRectangle>
          <Name>EndPointPrimitive53</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>be03a4b4c98c4549b8e36cdf99705812</ReferenceToGuid>
        </EndPointPrimitive53>
        <VerticalLinePrimitive54 Ref="481" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>12.05,10,0.0254,3.3</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="482" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>17902987c04549a5bd942796c43ce60d</Guid>
          <Name>VerticalLinePrimitive54</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="483" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive54>
        <VerticalLinePrimitive55 Ref="484" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>13.15,10,0.0254,3.3</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="485" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>e6b2b3b3a3944a62b61612a5a29ccfe2</Guid>
          <Name>VerticalLinePrimitive55</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="486" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive55>
        <VerticalLinePrimitive56 Ref="487" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>14.35,10,0.0254,3.3</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="488" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>c0b6e59b77f545bea97dbdc879d6447a</Guid>
          <Name>VerticalLinePrimitive56</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="489" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive56>
        <StartPointPrimitive54 Ref="490" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>12.05,10,0,0</ClientRectangle>
          <Name>StartPointPrimitive54</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>17902987c04549a5bd942796c43ce60d</ReferenceToGuid>
        </StartPointPrimitive54>
        <EndPointPrimitive54 Ref="491" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>12,13.3,0,0</ClientRectangle>
          <Name>EndPointPrimitive54</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>17902987c04549a5bd942796c43ce60d</ReferenceToGuid>
        </EndPointPrimitive54>
        <StartPointPrimitive55 Ref="492" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>13.15,10,0,0</ClientRectangle>
          <Name>StartPointPrimitive55</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>e6b2b3b3a3944a62b61612a5a29ccfe2</ReferenceToGuid>
        </StartPointPrimitive55>
        <EndPointPrimitive55 Ref="493" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>13.1,13.3,0,0</ClientRectangle>
          <Name>EndPointPrimitive55</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>e6b2b3b3a3944a62b61612a5a29ccfe2</ReferenceToGuid>
        </EndPointPrimitive55>
        <StartPointPrimitive56 Ref="494" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>14.35,10,0,0</ClientRectangle>
          <Name>StartPointPrimitive56</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>c0b6e59b77f545bea97dbdc879d6447a</ReferenceToGuid>
        </StartPointPrimitive56>
        <EndPointPrimitive56 Ref="495" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>14.35,13.3,0,0</ClientRectangle>
          <Name>EndPointPrimitive56</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>c0b6e59b77f545bea97dbdc879d6447a</ReferenceToGuid>
        </EndPointPrimitive56>
        <VerticalLinePrimitive57 Ref="496" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>2.7,8.8,0.0254,0.9</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="497" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>cdf77592c0324a8e96efdcc417c68001</Guid>
          <Name>VerticalLinePrimitive57</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="498" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive57>
        <StartPointPrimitive57 Ref="499" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>2.7,8.8,0,0</ClientRectangle>
          <Name>StartPointPrimitive57</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>cdf77592c0324a8e96efdcc417c68001</ReferenceToGuid>
        </StartPointPrimitive57>
        <EndPointPrimitive57 Ref="500" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>2.7,9.7,0,0</ClientRectangle>
          <Name>EndPointPrimitive57</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>cdf77592c0324a8e96efdcc417c68001</ReferenceToGuid>
        </EndPointPrimitive57>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>a602b0ce0a8b4f73b56e7924e9f50042</Guid>
      <Margins>1.1,1.1,1,1</Margins>
      <Name>Page1</Name>
      <PageHeight>29.7</PageHeight>
      <PageWidth>21</PageWidth>
      <Report isRef="0" />
      <Watermark Ref="501" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page1>
  </Pages>
  <PrinterSettings Ref="502" type="Stimulsoft.Report.Print.StiPrinterSettings" isKey="true" />
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>Report</ReportAlias>
  <ReportChanged>7/25/2019 5:12:10 PM</ReportChanged>
  <ReportCreated>5/22/2019 2:37:22 PM</ReportCreated>
  <ReportFile>D:\SVN\his2010(通辽)\his2010\Rpt\通辽市医疗保险住院费用结算单(职工).mrt</ReportFile>
  <ReportGuid>e3e3f43722f8422d932c116b2c3d6a17</ReportGuid>
  <ReportName>Report</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2011.2.1026</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class Report : Stimulsoft.Report.StiReport
    {
        public Report()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
        #endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>