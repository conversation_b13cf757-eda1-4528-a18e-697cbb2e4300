﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="0" />
    <DataSources isList="true" count="1">
      <ds Ref="2" type="DataTableSource" isKey="true">
        <Alias>ds</Alias>
        <Columns isList="true" count="8">
          <value>Yp_name,System.String</value>
          <value>Mz_sl,System.Decimal</value>
          <value>Mz_Dj,System.Decimal</value>
          <value>Mz_Money,System.Decimal</value>
          <value>Xx_Code,System.String</value>
          <value>Yp_Discount,System.Decimal</value>
          <value>Yp_Original_Money,System.Decimal</value>
          <value>Xm_Name,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>ds</Name>
        <NameInSource>ds</NameInSource>
      </ds>
    </DataSources>
    <Relations isList="true" count="0" />
    <Report isRef="0" />
    <Variables isList="true" count="6">
      <value>,医院名称,医院名称,System.String,,False,False</value>
      <value>,姓名,姓名,System.String,,False,False</value>
      <value>,收费日期,收费日期,System.String,,False,False</value>
      <value>,收款人,收款人,System.String,,False,False</value>
      <value>,合计大写,合计大写,System.String,,False,False</value>
      <value>,打印编号,打印编号,System.String,,False,False</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="3" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="3">
        <ReportTitleBand1 Ref="4" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,5.5,1.89</ClientRectangle>
          <Components isList="true" count="11">
            <Text1 Ref="5" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.2,5.4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text1</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>{医院名称}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text1>
            <Text2 Ref="6" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.2,5.4,0.1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text2</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>------------------------------------------------------------</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text2>
            <Text23 Ref="7" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.3,1.8,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,7,Regular,Point,False,134</Font>
              <Guid>811a7f58233d4216a4a137042ef0a058</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text23</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>收费名称</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text23>
            <Text25 Ref="8" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.8,1.3,0.6,0.41</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,7,Regular,Point,False,134</Font>
              <Guid>a9b8a5c4b90f4432a84ec5dcbfe43189</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text25</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>数量</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text25>
            <Text24 Ref="9" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.4,1.3,0.8,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,7,Regular,Point,False,134</Font>
              <Guid>09d673f66bee4c018b1c404738f7b396</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text24</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>单价</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text24>
            <Text37 Ref="10" type="Text" isKey="true">
              <Border>AdvBlack;2;None;Black;1;None;Black;2;None;Black;1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0.9,0.8,1.9,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text37</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>{姓名}</Text>
              <TextBrush>Black</TextBrush>
            </Text37>
            <Text39 Ref="11" type="Text" isKey="true">
              <Border>AdvBlack;2;None;Black;1;None;Black;2;None;Black;1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0.1,0.8,0.8,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text39</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>姓名：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text39>
            <Text40 Ref="12" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.5,1.3,0.9,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,7,Regular,Point,False,134</Font>
              <Guid>ada49be2a4594a54b533bc9c6bd8b2ce</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text40</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>现价</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text40>
            <Text3 Ref="13" type="Text" isKey="true">
              <Border>AdvBlack;2;None;Black;1;None;Black;2;None;Black;1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.8,0.8,2.6,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>4bff8d67e696420e83b1bdf7ca256172</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text3</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>{打印编号}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text3>
            <Text9 Ref="14" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>3.9,1.3,0.6,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,7,Regular,Point,False,134</Font>
              <Guid>54110e3fc37b4835a4a7a96e11024a5f</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text9</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>折扣</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text9>
            <Text13 Ref="15" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>3.2,1.3,0.7,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,7,Regular,Point,False,134</Font>
              <Guid>0230e4557c0945f2954e7da6091524af</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text13</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>原价</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text13>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>ReportTitleBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </ReportTitleBand1>
        <DataBand1 Ref="16" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,3.09,5.5,0.43</ClientRectangle>
          <Components isList="true" count="7">
            <Text27 Ref="17" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,1.8,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,7,Regular,Point,False,134</Font>
              <Guid>f90191201a3f4c4c81ca47d1105ebf7c</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text27</Name>
              <Page isRef="3" />
              <Parent isRef="16" />
              <Text>{ds.Yp_name}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text27>
            <Text28 Ref="18" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.4,0,0.8,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,7,Regular,Point,False,134</Font>
              <Guid>181806d61e74403997e494da47be1af7</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text28</Name>
              <Page isRef="3" />
              <Parent isRef="16" />
              <Text>{ds.Mz_Dj}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="19" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text28>
            <Text29 Ref="20" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.8,0,0.6,0.41</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,7,Regular,Point,False,134</Font>
              <Guid>94668695ad84466b8f9a521ed7335c12</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text29</Name>
              <Page isRef="3" />
              <Parent isRef="16" />
              <Text>{ds.Mz_sl}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="21" type="CustomFormat" isKey="true">
                <StringFormat>#.00</StringFormat>
              </TextFormat>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text29>
            <Text41 Ref="22" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.5,0,0.9,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,7,Regular,Point,False,134</Font>
              <Guid>38106338a0b9413295204d2187202f36</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text41</Name>
              <Page isRef="3" />
              <Parent isRef="16" />
              <Text>{ds.Mz_Money}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="23" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text41>
            <Text10 Ref="24" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>3.9,0,0.6,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,7,Regular,Point,False,134</Font>
              <Guid>ef407b7139ee47979614cff9f4c0fde9</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text10</Name>
              <Page isRef="3" />
              <Parent isRef="16" />
              <Text>{ds.Yp_Discount}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="25" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text10>
            <Text16 Ref="26" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>3.2,0,0.7,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,7,Regular,Point,False,134</Font>
              <Guid>3a4494b1d62b4405839e02cf102afe58</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text16</Name>
              <Page isRef="3" />
              <Parent isRef="16" />
              <Text>{ds.Yp_Original_Money}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="27" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text16>
            <Text17 Ref="28" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,1.8,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,7,Regular,Point,False,134</Font>
              <Guid>06141afca31a4e188f2fae7934ca6908</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text17</Name>
              <Page isRef="3" />
              <Parent isRef="16" />
              <Text>{ds.Xm_Name}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text17>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>ds</DataSourceName>
          <Filters isList="true" count="0" />
          <Name>DataBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Sort isList="true" count="0" />
        </DataBand1>
        <ReportSummaryBand1 Ref="29" type="ReportSummaryBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,4.32,5.5,2.5</ClientRectangle>
          <Components isList="true" count="12">
            <Text14 Ref="30" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0.1,0,1.2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,7,Regular,Point,False,134</Font>
              <Guid>9f1cba0c85d14081a9e349712c5a0cba</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text14</Name>
              <Page isRef="3" />
              <Parent isRef="29" />
              <Text>合计金额:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text14>
            <Text15 Ref="31" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.3,0,0.9,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,7,Regular,Point,False,0</Font>
              <Guid>40672e64d31c4bf3a0bc3c486c3e794b</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text15</Name>
              <Page isRef="3" />
              <Parent isRef="29" />
              <Text>{Sum(DataBand1,ds.Mz_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="32" type="CustomFormat" isKey="true">
                <StringFormat>#.00</StringFormat>
              </TextFormat>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text15>
            <Text18 Ref="33" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.4,5.4,0.2</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>f3732a81a32b46bbb500c943365314fa</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text18</Name>
              <Page isRef="3" />
              <Parent isRef="29" />
              <Text>------------------------------------------------------------</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text18>
            <Text11 Ref="34" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0.1,1.1,1.2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,7,Regular,Point,False,134</Font>
              <Guid>d478abcdc95a41cfa241ee4df825c8a6</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text11</Name>
              <Page isRef="3" />
              <Parent isRef="29" />
              <Text>收 款 人:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text11>
            <Text7 Ref="35" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0.1,0.68,1.2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,7,Regular,Point,False,134</Font>
              <Guid>01f38197351e47d1b256190ff3c87870</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text7</Name>
              <Page isRef="3" />
              <Parent isRef="29" />
              <Text>合计大写:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text7>
            <Text8 Ref="36" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.3,0.68,4.1,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,7,Regular,Point,False,0</Font>
              <Guid>695ff257e0084aa6b6497afc42df7b43</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text8</Name>
              <Page isRef="3" />
              <Parent isRef="29" />
              <Text>{合计大写}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text8>
            <Text12 Ref="37" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.3,1.1,4.1,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,7,Regular,Point,False,0</Font>
              <Guid>fd5840e0303142ca871ec41e54bc225a</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text12</Name>
              <Page isRef="3" />
              <Parent isRef="29" />
              <Text>{收款人}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text12>
            <Text6 Ref="38" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.3,1.5,4.1,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,7,Regular,Point,False,0</Font>
              <Guid>024e9d11c65540999e46fcd224413a28</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text6</Name>
              <Page isRef="3" />
              <Parent isRef="29" />
              <Text>{收费日期}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text6>
            <Text5 Ref="39" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0.1,1.5,1.2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,7,Regular,Point,False,134</Font>
              <Guid>57b491d1586d438698c01a9ebde9bf37</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text5</Name>
              <Page isRef="3" />
              <Parent isRef="29" />
              <Text>收费时间:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text5>
            <Text4 Ref="40" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0.1,2,5.3,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,7,Regular,Point,False,134</Font>
              <Guid>a33e4b0a8b264d53ab48d08b6208411d</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text4</Name>
              <Page isRef="3" />
              <Parent isRef="29" />
              <Text>合利康门诊为健康保驾护航 电话:0431-80806261</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text4>
            <Text19 Ref="41" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.2,0,1.2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,7,Regular,Point,False,134</Font>
              <Guid>07edd4be501d4f94ae71160a2073ef1c</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text19</Name>
              <Page isRef="3" />
              <Parent isRef="29" />
              <Text>折扣金额：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text19>
            <Text20 Ref="42" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>3.4,0,1.1,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,7,Regular,Point,False,0</Font>
              <Guid>42ec7ba7fa7f4356a43148d2979be0ec</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text20</Name>
              <Page isRef="3" />
              <Parent isRef="29" />
              <Text>{Format("{0:N2}", (Sum(DataBand1,ds.Yp_Original_Money))-(Sum(DataBand1,ds.Mz_Money)))}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="43" type="CustomFormat" isKey="true">
                <StringFormat>#.00</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text20>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>ReportSummaryBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </ReportSummaryBand1>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>02afbf12edcb4530b97328e145b4fe1d</Guid>
      <Margins>0,0,0.1,0.2</Margins>
      <Name>Page1</Name>
      <PageHeight>7</PageHeight>
      <PageWidth>5.5</PageWidth>
      <Report isRef="0" />
      <Watermark Ref="44" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page1>
  </Pages>
  <PrinterSettings Ref="45" type="Stimulsoft.Report.Print.StiPrinterSettings" isKey="true" />
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>门诊收据</ReportAlias>
  <ReportChanged>6/8/2020 3:03:22 PM</ReportChanged>
  <ReportCreated>4/12/2019 9:27:21 AM</ReportCreated>
  <ReportFile>D:\SVNNew\HIs通辽乡镇卫生院\his2010\Rpt\合力康医院发票.mrt</ReportFile>
  <ReportGuid>eb5ed0698d1642f88ed4aaf0c6082fe6</ReportGuid>
  <ReportName>门诊收据</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2011.2.1026</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class Report : Stimulsoft.Report.StiReport
    {
        public Report()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
        #endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>