﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ERX.Model
{
    /// <summary>
    /// 电子处方取药结果查询
    /// </summary>
    public class MdlrxSetlInfoQueryIn
    {
        public string hiRxno { get; set; }   //医保处方编号
        public string fixmedinsCode { get; set; }   //定点医疗机构编号
        public string mdtrtId { get; set; }   //医保就诊ID 
        public string psnName { get; set; }   //人员名称 
        public string psnCertType { get; set; }   //人员证件类型 
        public string certno { get; set; }   //证件号码 

    }
    public class MdlrxSetlInfoQueryOut
    {
        public string hiRxno { get; set; }   //医保处方编号 
        public DateTime  setlTime { get; set; }   //医保结算时间
        public List<MdlrxSetlInfoQueryseltdeltsOut> seltdelts { get; set; }

    }
    public class MdlrxSetlInfoQueryseltdeltsOut
    {
        public string medinsListCodg { get; set; }   //医药机构药品编号
        public string drugGenname { get; set; }   //通用名  
        public string drugProdname { get; set; }   //药品商品名  
        public string drugDosform { get; set; }   //药品剂型
        public decimal drugSpec { get; set; }   //药品规格
        public decimal ent { get; set; }   //数量 
        public string aprvno { get; set; }   //批准文号
        public string bchno { get; set; }   //批次号 
        public string manuLotnum { get; set; }   //生产批号 
        public string prdrName { get; set; }   //生厂厂家

    }
}
