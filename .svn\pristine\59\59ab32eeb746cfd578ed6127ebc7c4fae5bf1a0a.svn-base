﻿/**  版本信息模板在安装目录下，可自行修改。
* D_Zd_QxMenu2.cs
*
* 功 能： N/A
* 类 名： D_Zd_QxMenu2
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016-08-15 09:56:33   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
namespace DAL
{
	/// <summary>
	/// 数据访问类:D_Zd_QxMenu2
	/// </summary>
	public partial class D_Zd_QxMenu2
	{
		public D_Zd_QxMenu2()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string SecondMenu_Code)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from Zd_QxMenu2");
			strSql.Append(" where SecondMenu_Code=@SecondMenu_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@SecondMenu_Code", SqlDbType.Char,5)			};
			parameters[0].Value = SecondMenu_Code;

			return HisVar.HisVar.Sqldal.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ModelOld.M_Zd_QxMenu2 model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into Zd_QxMenu2(");
			strSql.Append("Menu_Code,SecondMenu_Code,SecondName_Name,SecondMenu_Order)");
			strSql.Append(" values (");
			strSql.Append("@Menu_Code,@SecondMenu_Code,@SecondName_Name,@SecondMenu_Order)");
			SqlParameter[] parameters = {
					new SqlParameter("@Menu_Code", SqlDbType.Char,2),
					new SqlParameter("@SecondMenu_Code", SqlDbType.Char,5),
					new SqlParameter("@SecondName_Name", SqlDbType.VarChar,50),
					new SqlParameter("@SecondMenu_Order", SqlDbType.Int,4)};
			parameters[0].Value = model.Menu_Code;
			parameters[1].Value = model.SecondMenu_Code;
			parameters[2].Value = model.SecondName_Name;
			parameters[3].Value = model.SecondMenu_Order;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ModelOld.M_Zd_QxMenu2 model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update Zd_QxMenu2 set ");
			strSql.Append("Menu_Code=@Menu_Code,");
			strSql.Append("SecondName_Name=@SecondName_Name,");
			strSql.Append("SecondMenu_Order=@SecondMenu_Order");
			strSql.Append(" where SecondMenu_Code=@SecondMenu_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Menu_Code", SqlDbType.Char,2),
					new SqlParameter("@SecondName_Name", SqlDbType.VarChar,50),
					new SqlParameter("@SecondMenu_Order", SqlDbType.Int,4),
					new SqlParameter("@SecondMenu_Code", SqlDbType.Char,5)};
			parameters[0].Value = model.Menu_Code;
			parameters[1].Value = model.SecondName_Name;
			parameters[2].Value = model.SecondMenu_Order;
			parameters[3].Value = model.SecondMenu_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string SecondMenu_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Zd_QxMenu2 ");
			strSql.Append(" where SecondMenu_Code=@SecondMenu_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@SecondMenu_Code", SqlDbType.Char,5)			};
			parameters[0].Value = SecondMenu_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string SecondMenu_Codelist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Zd_QxMenu2 ");
			strSql.Append(" where SecondMenu_Code in ("+SecondMenu_Codelist + ")  ");
			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Zd_QxMenu2 GetModel(string SecondMenu_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 Menu_Code,SecondMenu_Code,SecondName_Name,SecondMenu_Order from Zd_QxMenu2 ");
			strSql.Append(" where SecondMenu_Code=@SecondMenu_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@SecondMenu_Code", SqlDbType.Char,5)			};
			parameters[0].Value = SecondMenu_Code;

			ModelOld.M_Zd_QxMenu2 model=new ModelOld.M_Zd_QxMenu2();
			DataSet ds=HisVar.HisVar.Sqldal.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Zd_QxMenu2 DataRowToModel(DataRow row)
		{
			ModelOld.M_Zd_QxMenu2 model=new ModelOld.M_Zd_QxMenu2();
			if (row != null)
			{
				if(row["Menu_Code"]!=null)
				{
					model.Menu_Code=row["Menu_Code"].ToString();
				}
				if(row["SecondMenu_Code"]!=null)
				{
					model.SecondMenu_Code=row["SecondMenu_Code"].ToString();
				}
				if(row["SecondName_Name"]!=null)
				{
					model.SecondName_Name=row["SecondName_Name"].ToString();
				}
				if(row["SecondMenu_Order"]!=null && row["SecondMenu_Order"].ToString()!="")
				{
					model.SecondMenu_Order=int.Parse(row["SecondMenu_Order"].ToString());
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select Menu_Code,SecondMenu_Code,SecondName_Name,SecondMenu_Order ");
			strSql.Append(" FROM Zd_QxMenu2 ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" Menu_Code,SecondMenu_Code,SecondName_Name,SecondMenu_Order ");
			strSql.Append(" FROM Zd_QxMenu2 ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM Zd_QxMenu2 ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.SecondMenu_Code desc");
			}
			strSql.Append(")AS Row, T.*  from Zd_QxMenu2 T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Zd_QxMenu2";
			parameters[1].Value = "SecondMenu_Code";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return HisVar.HisVar.Sqldal.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

