﻿/**  版本信息模板在安装目录下，可自行修改。
* D_Zd_YyYs.cs
*
* 功 能： N/A
* 类 名： D_Zd_YyYs
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/8/11 9:17:05   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
namespace DAL
{
	/// <summary>
	/// 数据访问类:D_Zd_YyYs
	/// </summary>
	public partial class D_Zd_YyYs
	{
		public D_Zd_YyYs()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string Ys_Code)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from Zd_YyYs");
			strSql.Append(" where Ys_Code=@Ys_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Ys_Code", SqlDbType.Char,7)			};
			parameters[0].Value = Ys_Code;

			return HisVar.HisVar.Sqldal.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ModelOld.M_Zd_YyYs model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into Zd_YyYs(");
			strSql.Append("Yy_Code,Ys_Code,Ks_Code,Ys_Name,Ys_Jc,Ys_Sex,Ys_Memo,Ys_Use)");
			strSql.Append(" values (");
			strSql.Append("@Yy_Code,@Ys_Code,@Ks_Code,@Ys_Name,@Ys_Jc,@Ys_Sex,@Ys_Memo,@Ys_Use)");
			SqlParameter[] parameters = {
					new SqlParameter("@Yy_Code", SqlDbType.Char,4),
					new SqlParameter("@Ys_Code", SqlDbType.Char,7),
					new SqlParameter("@Ks_Code", SqlDbType.Char,6),
					new SqlParameter("@Ys_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Ys_Jc", SqlDbType.VarChar,50),
					new SqlParameter("@Ys_Sex", SqlDbType.VarChar,2),
					new SqlParameter("@Ys_Memo", SqlDbType.VarChar,200),
					new SqlParameter("@Ys_Use", SqlDbType.Char,2)};
			parameters[0].Value = model.Yy_Code;
			parameters[1].Value = model.Ys_Code;
			parameters[2].Value = model.Ks_Code;
			parameters[3].Value = model.Ys_Name;
			parameters[4].Value = model.Ys_Jc;
			parameters[5].Value = model.Ys_Sex;
			parameters[6].Value = model.Ys_Memo;
			parameters[7].Value = model.Ys_Use;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ModelOld.M_Zd_YyYs model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update Zd_YyYs set ");
			strSql.Append("Yy_Code=@Yy_Code,");
			strSql.Append("Ks_Code=@Ks_Code,");
			strSql.Append("Ys_Name=@Ys_Name,");
			strSql.Append("Ys_Jc=@Ys_Jc,");
			strSql.Append("Ys_Sex=@Ys_Sex,");
			strSql.Append("Ys_Memo=@Ys_Memo,");
			strSql.Append("Ys_Use=@Ys_Use");
			strSql.Append(" where Ys_Code=@Ys_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Yy_Code", SqlDbType.Char,4),
					new SqlParameter("@Ks_Code", SqlDbType.Char,6),
					new SqlParameter("@Ys_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Ys_Jc", SqlDbType.VarChar,50),
					new SqlParameter("@Ys_Sex", SqlDbType.VarChar,2),
					new SqlParameter("@Ys_Memo", SqlDbType.VarChar,200),
					new SqlParameter("@Ys_Use", SqlDbType.Char,2),
					new SqlParameter("@Ys_Code", SqlDbType.Char,7)};
			parameters[0].Value = model.Yy_Code;
			parameters[1].Value = model.Ks_Code;
			parameters[2].Value = model.Ys_Name;
			parameters[3].Value = model.Ys_Jc;
			parameters[4].Value = model.Ys_Sex;
			parameters[5].Value = model.Ys_Memo;
			parameters[6].Value = model.Ys_Use;
			parameters[7].Value = model.Ys_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string Ys_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Zd_YyYs ");
			strSql.Append(" where Ys_Code=@Ys_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Ys_Code", SqlDbType.Char,7)			};
			parameters[0].Value = Ys_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string Ys_Codelist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Zd_YyYs ");
			strSql.Append(" where Ys_Code in ("+Ys_Codelist + ")  ");
			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Zd_YyYs GetModel(string Ys_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 Yy_Code,Ys_Code,Ks_Code,Ys_Name,Ys_Jc,Ys_Sex,Ys_Memo,Ys_Use from Zd_YyYs ");
			strSql.Append(" where Ys_Code=@Ys_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Ys_Code", SqlDbType.Char,7)			};
			parameters[0].Value = Ys_Code;

			ModelOld.M_Zd_YyYs model=new ModelOld.M_Zd_YyYs();
			DataSet ds=HisVar.HisVar.Sqldal.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Zd_YyYs DataRowToModel(DataRow row)
		{
			ModelOld.M_Zd_YyYs model=new ModelOld.M_Zd_YyYs();
			if (row != null)
			{
				if(row["Yy_Code"]!=null)
				{
					model.Yy_Code=row["Yy_Code"].ToString();
				}
				if(row["Ys_Code"]!=null)
				{
					model.Ys_Code=row["Ys_Code"].ToString();
				}
				if(row["Ks_Code"]!=null)
				{
					model.Ks_Code=row["Ks_Code"].ToString();
				}
				if(row["Ys_Name"]!=null)
				{
					model.Ys_Name=row["Ys_Name"].ToString();
				}
				if(row["Ys_Jc"]!=null)
				{
					model.Ys_Jc=row["Ys_Jc"].ToString();
				}
				if(row["Ys_Sex"]!=null)
				{
					model.Ys_Sex=row["Ys_Sex"].ToString();
				}
				if(row["Ys_Memo"]!=null)
				{
					model.Ys_Memo=row["Ys_Memo"].ToString();
				}
				if(row["Ys_Use"]!=null)
				{
					model.Ys_Use=row["Ys_Use"].ToString();
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select Yy_Code,Ys_Code,Ks_Code,Ys_Name,Ys_Jc,Ys_Sex,Ys_Memo,Ys_Use ");
			strSql.Append(" FROM Zd_YyYs ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" Yy_Code,Ys_Code,Ks_Code,Ys_Name,Ys_Jc,Ys_Sex,Ys_Memo,Ys_Use ");
			strSql.Append(" FROM Zd_YyYs ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM Zd_YyYs ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
            object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.Ys_Code desc");
			}
			strSql.Append(")AS Row, T.*  from Zd_YyYs T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Zd_YyYs";
			parameters[1].Value = "Ys_Code";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return HisVar.HisVar.Sqldal.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

