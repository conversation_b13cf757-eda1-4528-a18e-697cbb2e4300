<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:tns="http://tempuri.org/" xmlns:s="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tm="http://microsoft.com/wsdl/mime/textMatching/" xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <s:schema elementFormDefault="qualified" targetNamespace="http://tempuri.org/">
      <s:element name="To_Cgd">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Cg_Name" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Lb" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="To_CgdResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="To_CgdResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="To_Psd">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Cg_Name" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Lsh" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Cg_Ph" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="To_PsdResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="To_PsdResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="To_Rkd">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Cg_Name" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Lsh" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Cg_Ph" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Cg_Dhsl" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="To_RkdResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="To_RkdResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetCg_ZCgj">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Cg_Name" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Lb" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetCg_ZCgjResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetCg_ZCgjResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DB_Cg_Name">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Sw_Cg_Name" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Ts_Cg_Name" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Lb" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DB_Cg_NameResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="DB_Cg_NameResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetCgdName">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Cg_Name" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="lb" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetCgdNameResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetCgdNameResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getPs_Date">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Qy_Name" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="date1" type="s:dateTime" />
            <s:element minOccurs="1" maxOccurs="1" name="date2" type="s:dateTime" />
            <s:element minOccurs="0" maxOccurs="1" name="Lb" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getPs_DateResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="getPs_DateResult">
              <s:complexType>
                <s:sequence>
                  <s:any minOccurs="0" maxOccurs="unbounded" namespace="http://www.w3.org/2001/XMLSchema" processContents="lax" />
                  <s:any minOccurs="1" namespace="urn:schemas-microsoft-com:xml-diffgram-v1" processContents="lax" />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getCg_Date">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Wsj_Name" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="date1" type="s:dateTime" />
            <s:element minOccurs="1" maxOccurs="1" name="date2" type="s:dateTime" />
            <s:element minOccurs="0" maxOccurs="1" name="Lb" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getCg_DateResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="getCg_DateResult">
              <s:complexType>
                <s:sequence>
                  <s:any minOccurs="0" maxOccurs="unbounded" namespace="http://www.w3.org/2001/XMLSchema" processContents="lax" />
                  <s:any minOccurs="1" namespace="urn:schemas-microsoft-com:xml-diffgram-v1" processContents="lax" />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getRk_Date">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Wsj_Name" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="date1" type="s:dateTime" />
            <s:element minOccurs="1" maxOccurs="1" name="date2" type="s:dateTime" />
            <s:element minOccurs="0" maxOccurs="1" name="Lb" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getRk_DateResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="getRk_DateResult">
              <s:complexType>
                <s:sequence>
                  <s:any minOccurs="0" maxOccurs="unbounded" namespace="http://www.w3.org/2001/XMLSchema" processContents="lax" />
                  <s:any minOccurs="1" namespace="urn:schemas-microsoft-com:xml-diffgram-v1" processContents="lax" />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetHISRk_Date">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Nh_Bm" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetHISRk_DateResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetHISRk_DateResult">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="HIs_Back">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Cg_MxCode" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="HIs_BackResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="HIs_BackResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
    </s:schema>
  </wsdl:types>
  <wsdl:message name="To_CgdSoapIn">
    <wsdl:part name="parameters" element="tns:To_Cgd" />
  </wsdl:message>
  <wsdl:message name="To_CgdSoapOut">
    <wsdl:part name="parameters" element="tns:To_CgdResponse" />
  </wsdl:message>
  <wsdl:message name="To_PsdSoapIn">
    <wsdl:part name="parameters" element="tns:To_Psd" />
  </wsdl:message>
  <wsdl:message name="To_PsdSoapOut">
    <wsdl:part name="parameters" element="tns:To_PsdResponse" />
  </wsdl:message>
  <wsdl:message name="To_RkdSoapIn">
    <wsdl:part name="parameters" element="tns:To_Rkd" />
  </wsdl:message>
  <wsdl:message name="To_RkdSoapOut">
    <wsdl:part name="parameters" element="tns:To_RkdResponse" />
  </wsdl:message>
  <wsdl:message name="GetCg_ZCgjSoapIn">
    <wsdl:part name="parameters" element="tns:GetCg_ZCgj" />
  </wsdl:message>
  <wsdl:message name="GetCg_ZCgjSoapOut">
    <wsdl:part name="parameters" element="tns:GetCg_ZCgjResponse" />
  </wsdl:message>
  <wsdl:message name="DB_Cg_NameSoapIn">
    <wsdl:part name="parameters" element="tns:DB_Cg_Name" />
  </wsdl:message>
  <wsdl:message name="DB_Cg_NameSoapOut">
    <wsdl:part name="parameters" element="tns:DB_Cg_NameResponse" />
  </wsdl:message>
  <wsdl:message name="GetCgdNameSoapIn">
    <wsdl:part name="parameters" element="tns:GetCgdName" />
  </wsdl:message>
  <wsdl:message name="GetCgdNameSoapOut">
    <wsdl:part name="parameters" element="tns:GetCgdNameResponse" />
  </wsdl:message>
  <wsdl:message name="getPs_DateSoapIn">
    <wsdl:part name="parameters" element="tns:getPs_Date" />
  </wsdl:message>
  <wsdl:message name="getPs_DateSoapOut">
    <wsdl:part name="parameters" element="tns:getPs_DateResponse" />
  </wsdl:message>
  <wsdl:message name="getCg_DateSoapIn">
    <wsdl:part name="parameters" element="tns:getCg_Date" />
  </wsdl:message>
  <wsdl:message name="getCg_DateSoapOut">
    <wsdl:part name="parameters" element="tns:getCg_DateResponse" />
  </wsdl:message>
  <wsdl:message name="getRk_DateSoapIn">
    <wsdl:part name="parameters" element="tns:getRk_Date" />
  </wsdl:message>
  <wsdl:message name="getRk_DateSoapOut">
    <wsdl:part name="parameters" element="tns:getRk_DateResponse" />
  </wsdl:message>
  <wsdl:message name="GetHISRk_DateSoapIn">
    <wsdl:part name="parameters" element="tns:GetHISRk_Date" />
  </wsdl:message>
  <wsdl:message name="GetHISRk_DateSoapOut">
    <wsdl:part name="parameters" element="tns:GetHISRk_DateResponse" />
  </wsdl:message>
  <wsdl:message name="HIs_BackSoapIn">
    <wsdl:part name="parameters" element="tns:HIs_Back" />
  </wsdl:message>
  <wsdl:message name="HIs_BackSoapOut">
    <wsdl:part name="parameters" element="tns:HIs_BackResponse" />
  </wsdl:message>
  <wsdl:portType name="WebServiceSoap">
    <wsdl:operation name="To_Cgd">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">导入采购单</wsdl:documentation>
      <wsdl:input message="tns:To_CgdSoapIn" />
      <wsdl:output message="tns:To_CgdSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="To_Psd">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">导入配送单</wsdl:documentation>
      <wsdl:input message="tns:To_PsdSoapIn" />
      <wsdl:output message="tns:To_PsdSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="To_Rkd">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">导入入库单</wsdl:documentation>
      <wsdl:input message="tns:To_RkdSoapIn" />
      <wsdl:output message="tns:To_RkdSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetCg_ZCgj">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">采购单总金额</wsdl:documentation>
      <wsdl:input message="tns:GetCg_ZCgjSoapIn" />
      <wsdl:output message="tns:GetCg_ZCgjSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="DB_Cg_Name">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">保存采购单名称</wsdl:documentation>
      <wsdl:input message="tns:DB_Cg_NameSoapIn" />
      <wsdl:output message="tns:DB_Cg_NameSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetCgdName">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">获取省网采购单名</wsdl:documentation>
      <wsdl:input message="tns:GetCgdNameSoapIn" />
      <wsdl:output message="tns:GetCgdNameSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="getPs_Date">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">配送企业数据</wsdl:documentation>
      <wsdl:input message="tns:getPs_DateSoapIn" />
      <wsdl:output message="tns:getPs_DateSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="getCg_Date">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">采购录入数据</wsdl:documentation>
      <wsdl:input message="tns:getCg_DateSoapIn" />
      <wsdl:output message="tns:getCg_DateSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="getRk_Date">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">入库数据</wsdl:documentation>
      <wsdl:input message="tns:getRk_DateSoapIn" />
      <wsdl:output message="tns:getRk_DateSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetHISRk_Date">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">HIS入库获取数据</wsdl:documentation>
      <wsdl:input message="tns:GetHISRk_DateSoapIn" />
      <wsdl:output message="tns:GetHISRk_DateSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="HIs_Back">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">HIS入库成功后返回采购编码</wsdl:documentation>
      <wsdl:input message="tns:HIs_BackSoapIn" />
      <wsdl:output message="tns:HIs_BackSoapOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="WebServiceSoap" type="tns:WebServiceSoap">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="To_Cgd">
      <soap:operation soapAction="http://tempuri.org/To_Cgd" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="To_Psd">
      <soap:operation soapAction="http://tempuri.org/To_Psd" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="To_Rkd">
      <soap:operation soapAction="http://tempuri.org/To_Rkd" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetCg_ZCgj">
      <soap:operation soapAction="http://tempuri.org/GetCg_ZCgj" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DB_Cg_Name">
      <soap:operation soapAction="http://tempuri.org/DB_Cg_Name" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetCgdName">
      <soap:operation soapAction="http://tempuri.org/GetCgdName" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getPs_Date">
      <soap:operation soapAction="http://tempuri.org/getPs_Date" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getCg_Date">
      <soap:operation soapAction="http://tempuri.org/getCg_Date" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getRk_Date">
      <soap:operation soapAction="http://tempuri.org/getRk_Date" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetHISRk_Date">
      <soap:operation soapAction="http://tempuri.org/GetHISRk_Date" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="HIs_Back">
      <soap:operation soapAction="http://tempuri.org/HIs_Back" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="WebServiceSoap12" type="tns:WebServiceSoap">
    <soap12:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="To_Cgd">
      <soap12:operation soapAction="http://tempuri.org/To_Cgd" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="To_Psd">
      <soap12:operation soapAction="http://tempuri.org/To_Psd" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="To_Rkd">
      <soap12:operation soapAction="http://tempuri.org/To_Rkd" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetCg_ZCgj">
      <soap12:operation soapAction="http://tempuri.org/GetCg_ZCgj" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DB_Cg_Name">
      <soap12:operation soapAction="http://tempuri.org/DB_Cg_Name" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetCgdName">
      <soap12:operation soapAction="http://tempuri.org/GetCgdName" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getPs_Date">
      <soap12:operation soapAction="http://tempuri.org/getPs_Date" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getCg_Date">
      <soap12:operation soapAction="http://tempuri.org/getCg_Date" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getRk_Date">
      <soap12:operation soapAction="http://tempuri.org/getRk_Date" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetHISRk_Date">
      <soap12:operation soapAction="http://tempuri.org/GetHISRk_Date" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="HIs_Back">
      <soap12:operation soapAction="http://tempuri.org/HIs_Back" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="WebService">
    <wsdl:port name="WebServiceSoap" binding="tns:WebServiceSoap">
      <soap:address location="http://************/Ds_Service_temp/WebService.asmx" />
    </wsdl:port>
    <wsdl:port name="WebServiceSoap12" binding="tns:WebServiceSoap12">
      <soap12:address location="http://************/Ds_Service_temp/WebService.asmx" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>