﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class Ys_Cf4
    Inherits HisControl.BaseChild

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(Ys_Cf4))
        Me.Control3 = New C1.Win.C1Command.C1CommandControl()
        Me.C1CommandHolder1 = New C1.Win.C1Command.C1CommandHolder()
        Me.Move2 = New C1.Win.C1Command.C1Command()
        Me.Move4 = New C1.Win.C1Command.C1Command()
        Me.Move3 = New C1.Win.C1Command.C1Command()
        Me.Move1 = New C1.Win.C1Command.C1Command()
        Me.Control2 = New C1.Win.C1Command.C1CommandControl()
        Me.T_Label2 = New C1.Win.C1Input.C1Label()
        Me.Label5 = New System.Windows.Forms.Label()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.ToolTip1 = New System.Windows.Forms.ToolTip(Me.components)
        Me.Comm1 = New System.Windows.Forms.Button()
        Me.Comm2 = New System.Windows.Forms.Button()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.C1TextBox2 = New C1.Win.C1Input.C1TextBox()
        Me.T_Line1 = New System.Windows.Forms.Label()
        Me.C1TextBox1 = New C1.Win.C1Input.C1TextBox()
        Me.C1TextBox4 = New C1.Win.C1Input.C1TextBox()
        Me.C1TextBox5 = New C1.Win.C1Input.C1TextBox()
        Me.Label6 = New System.Windows.Forms.Label()
        Me.Label7 = New System.Windows.Forms.Label()
        Me.C1TextBox6 = New C1.Win.C1Input.C1TextBox()
        Me.ToolBar1 = New C1.Win.C1Command.C1ToolBar()
        Me.Link7 = New C1.Win.C1Command.C1CommandLink()
        Me.T_Line3 = New System.Windows.Forms.Label()
        Me.T_Line2 = New System.Windows.Forms.Label()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.C1TextBox3 = New C1.Win.C1Input.C1TextBox()
        Me.C1TextBox7 = New C1.Win.C1Input.C1TextBox()
        Me.C1TextBox8 = New C1.Win.C1Input.C1TextBox()
        Me.C1TextBox9 = New C1.Win.C1Input.C1TextBox()
        Me.C1TextBox10 = New C1.Win.C1Input.C1TextBox()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.Label8 = New System.Windows.Forms.Label()
        Me.Label9 = New System.Windows.Forms.Label()
        Me.Label10 = New System.Windows.Forms.Label()
        Me.Label11 = New System.Windows.Forms.Label()
        Me.C1TextBox11 = New C1.Win.C1Input.C1TextBox()
        Me.Label12 = New System.Windows.Forms.Label()
        Me.C1TextBox12 = New C1.Win.C1Input.C1TextBox()
        CType(Me.C1CommandHolder1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T_Label2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1TextBox2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1TextBox1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1TextBox4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1TextBox5, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1TextBox6, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.ToolBar1.SuspendLayout()
        Me.Panel1.SuspendLayout()
        CType(Me.C1TextBox3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1TextBox7, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1TextBox8, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1TextBox9, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1TextBox10, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1TextBox11, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1TextBox12, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'Control3
        '
        Me.Control3.HotFrame = C1.Win.C1Command.HotFrameEnum.None
        Me.Control3.Name = "Control3"
        Me.Control3.ShortcutText = ""
        Me.Control3.ShowShortcut = False
        Me.Control3.ShowTextAsToolTip = False
        '
        'C1CommandHolder1
        '
        Me.C1CommandHolder1.Commands.Add(Me.Control3)
        Me.C1CommandHolder1.Commands.Add(Me.Move2)
        Me.C1CommandHolder1.Commands.Add(Me.Move4)
        Me.C1CommandHolder1.Commands.Add(Me.Move3)
        Me.C1CommandHolder1.Commands.Add(Me.Move1)
        Me.C1CommandHolder1.Commands.Add(Me.Control2)
        Me.C1CommandHolder1.Owner = Me
        '
        'Move2
        '
        Me.Move2.Image = CType(resources.GetObject("Move2.Image"), System.Drawing.Image)
        Me.Move2.Name = "Move2"
        Me.Move2.Shortcut = System.Windows.Forms.Shortcut.F3
        Me.Move2.ShortcutText = ""
        Me.Move2.Text = "上移"
        Me.Move2.ToolTipText = "上移记录"
        '
        'Move4
        '
        Me.Move4.Image = CType(resources.GetObject("Move4.Image"), System.Drawing.Image)
        Me.Move4.Name = "Move4"
        Me.Move4.Shortcut = System.Windows.Forms.Shortcut.F5
        Me.Move4.ShortcutText = ""
        Me.Move4.Text = "最后"
        Me.Move4.ToolTipText = "移至最后记录"
        '
        'Move3
        '
        Me.Move3.Image = CType(resources.GetObject("Move3.Image"), System.Drawing.Image)
        Me.Move3.Name = "Move3"
        Me.Move3.Shortcut = System.Windows.Forms.Shortcut.F4
        Me.Move3.ShortcutText = ""
        Me.Move3.Text = "下移"
        Me.Move3.ToolTipText = "下移记录"
        '
        'Move1
        '
        Me.Move1.Image = CType(resources.GetObject("Move1.Image"), System.Drawing.Image)
        Me.Move1.Name = "Move1"
        Me.Move1.Shortcut = System.Windows.Forms.Shortcut.F2
        Me.Move1.ShortcutText = ""
        Me.Move1.Text = "最前"
        Me.Move1.ToolTipText = "移到最前记录"
        '
        'Control2
        '
        Me.Control2.Control = Me.T_Label2
        Me.Control2.HotFrame = C1.Win.C1Command.HotFrameEnum.None
        Me.Control2.Name = "Control2"
        Me.Control2.ShortcutText = ""
        Me.Control2.ShowShortcut = False
        Me.Control2.ShowTextAsToolTip = False
        '
        'T_Label2
        '
        Me.T_Label2.AutoSize = True
        Me.T_Label2.BackColor = System.Drawing.SystemColors.Control
        Me.T_Label2.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.T_Label2.ForeColor = System.Drawing.Color.Maroon
        Me.T_Label2.Location = New System.Drawing.Point(11, 11)
        Me.T_Label2.Name = "T_Label2"
        Me.T_Label2.Size = New System.Drawing.Size(0, 0)
        Me.T_Label2.TabIndex = 49
        Me.T_Label2.Tag = Nothing
        Me.T_Label2.TextAlign = System.Drawing.ContentAlignment.BottomCenter
        Me.T_Label2.TextDetached = True
        Me.T_Label2.TrimStart = True
        '
        'Label5
        '
        Me.Label5.AutoSize = True
        Me.Label5.Location = New System.Drawing.Point(357, 178)
        Me.Label5.Name = "Label5"
        Me.Label5.Size = New System.Drawing.Size(53, 12)
        Me.Label5.TabIndex = 175
        Me.Label5.Text = "现 病 史"
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.Location = New System.Drawing.Point(28, 178)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(53, 12)
        Me.Label2.TabIndex = 171
        Me.Label2.Text = "主    诉"
        '
        'Comm1
        '
        Me.Comm1.Location = New System.Drawing.Point(275, 2)
        Me.Comm1.Name = "Comm1"
        Me.Comm1.Size = New System.Drawing.Size(50, 24)
        Me.Comm1.TabIndex = 0
        Me.Comm1.Tag = "保存"
        Me.Comm1.Text = "保存           &S"
        Me.ToolTip1.SetToolTip(Me.Comm1, "数据存盘(&S)")
        Me.Comm1.UseVisualStyleBackColor = False
        '
        'Comm2
        '
        Me.Comm2.DialogResult = DialogResult.Cancel
        Me.Comm2.Location = New System.Drawing.Point(331, 3)
        Me.Comm2.Name = "Comm2"
        Me.Comm2.Size = New System.Drawing.Size(50, 24)
        Me.Comm2.TabIndex = 1
        Me.Comm2.Tag = "取消"
        Me.Comm2.Text = "取消             &C"
        Me.ToolTip1.SetToolTip(Me.Comm2, "取消存盘并退出(&C)")
        Me.Comm2.UseVisualStyleBackColor = False
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.ForeColor = System.Drawing.Color.DarkRed
        Me.Label1.Location = New System.Drawing.Point(28, 14)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(53, 12)
        Me.Label1.TabIndex = 170
        Me.Label1.Text = "编    码"
        '
        'C1TextBox2
        '
        Me.C1TextBox2.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.C1TextBox2.Location = New System.Drawing.Point(87, 125)
        Me.C1TextBox2.Multiline = True
        Me.C1TextBox2.Name = "C1TextBox2"
        Me.C1TextBox2.ScrollBars = System.Windows.Forms.ScrollBars.Vertical
        Me.C1TextBox2.Size = New System.Drawing.Size(261, 122)
        Me.C1TextBox2.TabIndex = 1
        Me.C1TextBox2.Tag = Nothing
        Me.C1TextBox2.TextDetached = True
        '
        'T_Line1
        '
        Me.T_Line1.BackColor = System.Drawing.SystemColors.Control
        Me.T_Line1.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.T_Line1.Location = New System.Drawing.Point(-6, 39)
        Me.T_Line1.Name = "T_Line1"
        Me.T_Line1.Size = New System.Drawing.Size(730, 2)
        Me.T_Line1.TabIndex = 168
        Me.T_Line1.Text = "Label1"
        '
        'C1TextBox1
        '
        Me.C1TextBox1.BackColor = System.Drawing.SystemColors.Info
        Me.C1TextBox1.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.C1TextBox1.Location = New System.Drawing.Point(87, 12)
        Me.C1TextBox1.Name = "C1TextBox1"
        Me.C1TextBox1.ReadOnly = True
        Me.C1TextBox1.Size = New System.Drawing.Size(121, 19)
        Me.C1TextBox1.TabIndex = 0
        Me.C1TextBox1.TabStop = False
        Me.C1TextBox1.Tag = Nothing
        Me.C1TextBox1.TextDetached = True
        Me.C1TextBox1.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        '
        'C1TextBox4
        '
        Me.C1TextBox4.AutoSize = False
        Me.C1TextBox4.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.C1TextBox4.Location = New System.Drawing.Point(416, 125)
        Me.C1TextBox4.Multiline = True
        Me.C1TextBox4.Name = "C1TextBox4"
        Me.C1TextBox4.ScrollBars = System.Windows.Forms.ScrollBars.Vertical
        Me.C1TextBox4.Size = New System.Drawing.Size(261, 122)
        Me.C1TextBox4.TabIndex = 3
        Me.C1TextBox4.Tag = Nothing
        Me.C1TextBox4.TextDetached = True
        '
        'C1TextBox5
        '
        Me.C1TextBox5.AutoSize = False
        Me.C1TextBox5.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.C1TextBox5.Location = New System.Drawing.Point(87, 270)
        Me.C1TextBox5.Multiline = True
        Me.C1TextBox5.Name = "C1TextBox5"
        Me.C1TextBox5.ScrollBars = System.Windows.Forms.ScrollBars.Vertical
        Me.C1TextBox5.Size = New System.Drawing.Size(261, 122)
        Me.C1TextBox5.TabIndex = 5
        Me.C1TextBox5.Tag = Nothing
        Me.C1TextBox5.TextDetached = True
        '
        'Label6
        '
        Me.Label6.AutoSize = True
        Me.Label6.Location = New System.Drawing.Point(28, 320)
        Me.Label6.Name = "Label6"
        Me.Label6.Size = New System.Drawing.Size(53, 12)
        Me.Label6.TabIndex = 186
        Me.Label6.Text = "查    体"
        '
        'Label7
        '
        Me.Label7.AutoSize = True
        Me.Label7.Location = New System.Drawing.Point(357, 318)
        Me.Label7.Name = "Label7"
        Me.Label7.Size = New System.Drawing.Size(53, 12)
        Me.Label7.TabIndex = 187
        Me.Label7.Text = "症    状"
        '
        'C1TextBox6
        '
        Me.C1TextBox6.AutoSize = False
        Me.C1TextBox6.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.C1TextBox6.Location = New System.Drawing.Point(416, 270)
        Me.C1TextBox6.Multiline = True
        Me.C1TextBox6.Name = "C1TextBox6"
        Me.C1TextBox6.ScrollBars = System.Windows.Forms.ScrollBars.Vertical
        Me.C1TextBox6.Size = New System.Drawing.Size(261, 122)
        Me.C1TextBox6.TabIndex = 7
        Me.C1TextBox6.Tag = Nothing
        Me.C1TextBox6.TextDetached = True
        '
        'ToolBar1
        '
        Me.ToolBar1.AccessibleName = "Tool Bar"
        Me.ToolBar1.CommandHolder = Nothing
        Me.ToolBar1.CommandLinks.AddRange(New C1.Win.C1Command.C1CommandLink() {Me.Link7})
        Me.ToolBar1.Controls.Add(Me.T_Label2)
        Me.ToolBar1.Location = New System.Drawing.Point(12, 3)
        Me.ToolBar1.MinButtonSize = 22
        Me.ToolBar1.Movable = False
        Me.ToolBar1.Name = "ToolBar1"
        Me.ToolBar1.Size = New System.Drawing.Size(22, 22)
        Me.ToolBar1.Text = "C1ToolBar2"
        Me.ToolBar1.VisualStyle = C1.Win.C1Command.VisualStyle.Classic
        Me.ToolBar1.VisualStyleBase = C1.Win.C1Command.VisualStyle.Classic
        '
        'Link7
        '
        Me.Link7.Command = Me.Control2
        '
        'T_Line3
        '
        Me.T_Line3.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.T_Line3.Dock = System.Windows.Forms.DockStyle.Top
        Me.T_Line3.Location = New System.Drawing.Point(0, 0)
        Me.T_Line3.Name = "T_Line3"
        Me.T_Line3.Size = New System.Drawing.Size(705, 2)
        Me.T_Line3.TabIndex = 101
        Me.T_Line3.Text = "Label2"
        '
        'T_Line2
        '
        Me.T_Line2.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.T_Line2.Location = New System.Drawing.Point(259, 3)
        Me.T_Line2.Name = "T_Line2"
        Me.T_Line2.Size = New System.Drawing.Size(2, 28)
        Me.T_Line2.TabIndex = 3
        Me.T_Line2.Text = "Label1"
        '
        'Panel1
        '
        Me.Panel1.Controls.Add(Me.T_Line2)
        Me.Panel1.Controls.Add(Me.T_Line3)
        Me.Panel1.Controls.Add(Me.ToolBar1)
        Me.Panel1.Controls.Add(Me.Comm2)
        Me.Panel1.Controls.Add(Me.Comm1)
        Me.Panel1.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.Panel1.Location = New System.Drawing.Point(0, 423)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(705, 27)
        Me.Panel1.TabIndex = 173
        '
        'C1TextBox3
        '
        Me.C1TextBox3.BackColor = System.Drawing.SystemColors.Info
        Me.C1TextBox3.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.C1TextBox3.Location = New System.Drawing.Point(87, 50)
        Me.C1TextBox3.Name = "C1TextBox3"
        Me.C1TextBox3.ReadOnly = True
        Me.C1TextBox3.Size = New System.Drawing.Size(121, 19)
        Me.C1TextBox3.TabIndex = 188
        Me.C1TextBox3.TabStop = False
        Me.C1TextBox3.Tag = Nothing
        Me.C1TextBox3.TextDetached = True
        Me.C1TextBox3.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        '
        'C1TextBox7
        '
        Me.C1TextBox7.BackColor = System.Drawing.SystemColors.Info
        Me.C1TextBox7.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.C1TextBox7.Location = New System.Drawing.Point(87, 84)
        Me.C1TextBox7.Name = "C1TextBox7"
        Me.C1TextBox7.ReadOnly = True
        Me.C1TextBox7.Size = New System.Drawing.Size(121, 19)
        Me.C1TextBox7.TabIndex = 189
        Me.C1TextBox7.TabStop = False
        Me.C1TextBox7.Tag = Nothing
        Me.C1TextBox7.TextDetached = True
        Me.C1TextBox7.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        '
        'C1TextBox8
        '
        Me.C1TextBox8.BackColor = System.Drawing.SystemColors.Info
        Me.C1TextBox8.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.C1TextBox8.Location = New System.Drawing.Point(285, 50)
        Me.C1TextBox8.Name = "C1TextBox8"
        Me.C1TextBox8.ReadOnly = True
        Me.C1TextBox8.Size = New System.Drawing.Size(73, 19)
        Me.C1TextBox8.TabIndex = 190
        Me.C1TextBox8.TabStop = False
        Me.C1TextBox8.Tag = Nothing
        Me.C1TextBox8.TextDetached = True
        Me.C1TextBox8.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        '
        'C1TextBox9
        '
        Me.C1TextBox9.BackColor = System.Drawing.SystemColors.Info
        Me.C1TextBox9.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.C1TextBox9.Location = New System.Drawing.Point(424, 50)
        Me.C1TextBox9.Name = "C1TextBox9"
        Me.C1TextBox9.ReadOnly = True
        Me.C1TextBox9.Size = New System.Drawing.Size(77, 19)
        Me.C1TextBox9.TabIndex = 191
        Me.C1TextBox9.TabStop = False
        Me.C1TextBox9.Tag = Nothing
        Me.C1TextBox9.TextDetached = True
        Me.C1TextBox9.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        '
        'C1TextBox10
        '
        Me.C1TextBox10.BackColor = System.Drawing.SystemColors.Info
        Me.C1TextBox10.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.C1TextBox10.Location = New System.Drawing.Point(285, 84)
        Me.C1TextBox10.Name = "C1TextBox10"
        Me.C1TextBox10.ReadOnly = True
        Me.C1TextBox10.Size = New System.Drawing.Size(216, 19)
        Me.C1TextBox10.TabIndex = 192
        Me.C1TextBox10.TabStop = False
        Me.C1TextBox10.Tag = Nothing
        Me.C1TextBox10.TextDetached = True
        Me.C1TextBox10.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        '
        'Label3
        '
        Me.Label3.AutoSize = True
        Me.Label3.ForeColor = System.Drawing.Color.DarkRed
        Me.Label3.Location = New System.Drawing.Point(28, 52)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(53, 12)
        Me.Label3.TabIndex = 193
        Me.Label3.Text = "姓    名"
        '
        'Label4
        '
        Me.Label4.AutoSize = True
        Me.Label4.ForeColor = System.Drawing.Color.DarkRed
        Me.Label4.Location = New System.Drawing.Point(226, 52)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(53, 12)
        Me.Label4.TabIndex = 194
        Me.Label4.Text = "性    别"
        '
        'Label8
        '
        Me.Label8.AutoSize = True
        Me.Label8.ForeColor = System.Drawing.Color.DarkRed
        Me.Label8.Location = New System.Drawing.Point(365, 52)
        Me.Label8.Name = "Label8"
        Me.Label8.Size = New System.Drawing.Size(53, 12)
        Me.Label8.TabIndex = 195
        Me.Label8.Text = "年    龄"
        '
        'Label9
        '
        Me.Label9.AutoSize = True
        Me.Label9.ForeColor = System.Drawing.Color.DarkRed
        Me.Label9.Location = New System.Drawing.Point(28, 86)
        Me.Label9.Name = "Label9"
        Me.Label9.Size = New System.Drawing.Size(53, 12)
        Me.Label9.TabIndex = 196
        Me.Label9.Text = "身份证号"
        '
        'Label10
        '
        Me.Label10.AutoSize = True
        Me.Label10.ForeColor = System.Drawing.Color.DarkRed
        Me.Label10.Location = New System.Drawing.Point(226, 86)
        Me.Label10.Name = "Label10"
        Me.Label10.Size = New System.Drawing.Size(53, 12)
        Me.Label10.TabIndex = 197
        Me.Label10.Text = "家庭住址"
        '
        'Label11
        '
        Me.Label11.AutoSize = True
        Me.Label11.ForeColor = System.Drawing.Color.DarkRed
        Me.Label11.Location = New System.Drawing.Point(518, 52)
        Me.Label11.Name = "Label11"
        Me.Label11.Size = New System.Drawing.Size(53, 12)
        Me.Label11.TabIndex = 199
        Me.Label11.Text = "患者类别"
        '
        'C1TextBox11
        '
        Me.C1TextBox11.BackColor = System.Drawing.SystemColors.Info
        Me.C1TextBox11.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.C1TextBox11.Location = New System.Drawing.Point(577, 50)
        Me.C1TextBox11.Name = "C1TextBox11"
        Me.C1TextBox11.ReadOnly = True
        Me.C1TextBox11.Size = New System.Drawing.Size(100, 19)
        Me.C1TextBox11.TabIndex = 198
        Me.C1TextBox11.TabStop = False
        Me.C1TextBox11.Tag = Nothing
        Me.C1TextBox11.TextDetached = True
        Me.C1TextBox11.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        '
        'Label12
        '
        Me.Label12.AutoSize = True
        Me.Label12.ForeColor = System.Drawing.Color.DarkRed
        Me.Label12.Location = New System.Drawing.Point(518, 90)
        Me.Label12.Name = "Label12"
        Me.Label12.Size = New System.Drawing.Size(53, 12)
        Me.Label12.TabIndex = 201
        Me.Label12.Text = "诊    断"
        '
        'C1TextBox12
        '
        Me.C1TextBox12.BackColor = System.Drawing.SystemColors.Info
        Me.C1TextBox12.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.C1TextBox12.Location = New System.Drawing.Point(577, 86)
        Me.C1TextBox12.Name = "C1TextBox12"
        Me.C1TextBox12.ReadOnly = True
        Me.C1TextBox12.Size = New System.Drawing.Size(100, 19)
        Me.C1TextBox12.TabIndex = 200
        Me.C1TextBox12.TabStop = False
        Me.C1TextBox12.Tag = Nothing
        Me.C1TextBox12.TextDetached = True
        Me.C1TextBox12.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        '
        'Ys_Cf4
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.CancelButton = Me.Comm2
        Me.ClientSize = New System.Drawing.Size(705, 450)
        Me.Controls.Add(Me.Label12)
        Me.Controls.Add(Me.C1TextBox12)
        Me.Controls.Add(Me.Label11)
        Me.Controls.Add(Me.C1TextBox11)
        Me.Controls.Add(Me.Label10)
        Me.Controls.Add(Me.Label9)
        Me.Controls.Add(Me.Label8)
        Me.Controls.Add(Me.Label4)
        Me.Controls.Add(Me.Label3)
        Me.Controls.Add(Me.C1TextBox10)
        Me.Controls.Add(Me.C1TextBox9)
        Me.Controls.Add(Me.C1TextBox8)
        Me.Controls.Add(Me.C1TextBox7)
        Me.Controls.Add(Me.C1TextBox3)
        Me.Controls.Add(Me.C1TextBox6)
        Me.Controls.Add(Me.Label7)
        Me.Controls.Add(Me.Label2)
        Me.Controls.Add(Me.Label1)
        Me.Controls.Add(Me.C1TextBox5)
        Me.Controls.Add(Me.Label6)
        Me.Controls.Add(Me.C1TextBox4)
        Me.Controls.Add(Me.T_Line1)
        Me.Controls.Add(Me.Label5)
        Me.Controls.Add(Me.C1TextBox1)
        Me.Controls.Add(Me.Panel1)
        Me.Controls.Add(Me.C1TextBox2)
        Me.Icon = CType(resources.GetObject("$this.Icon"), System.Drawing.Icon)
        Me.MinimizeBox = False
        Me.Name = "Ys_Cf4"
        Me.Text = "电子病历信息填写"
        CType(Me.C1CommandHolder1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T_Label2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1TextBox2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1TextBox1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1TextBox4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1TextBox5, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1TextBox6, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ToolBar1.ResumeLayout(False)
        Me.ToolBar1.PerformLayout()
        Me.Panel1.ResumeLayout(False)
        CType(Me.C1TextBox3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1TextBox7, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1TextBox8, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1TextBox9, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1TextBox10, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1TextBox11, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1TextBox12, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents C1CommandHolder1 As C1.Win.C1Command.C1CommandHolder
    Friend WithEvents Move2 As C1.Win.C1Command.C1Command
    Friend WithEvents Move4 As C1.Win.C1Command.C1Command
    Friend WithEvents Move3 As C1.Win.C1Command.C1Command
    Friend WithEvents Move1 As C1.Win.C1Command.C1Command
    Friend WithEvents Control2 As C1.Win.C1Command.C1CommandControl
    Friend WithEvents Label5 As System.Windows.Forms.Label
    Friend WithEvents Label2 As System.Windows.Forms.Label
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents C1TextBox2 As C1.Win.C1Input.C1TextBox
    Friend WithEvents T_Line1 As System.Windows.Forms.Label
    Friend WithEvents C1TextBox1 As C1.Win.C1Input.C1TextBox
    Friend WithEvents ToolTip1 As System.Windows.Forms.ToolTip
    Friend WithEvents C1TextBox5 As C1.Win.C1Input.C1TextBox
    Friend WithEvents C1TextBox4 As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label6 As System.Windows.Forms.Label
    Friend WithEvents T_Label2 As C1.Win.C1Input.C1Label
    Friend WithEvents Comm2 As System.Windows.Forms.Button
    Friend WithEvents C1TextBox6 As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label7 As System.Windows.Forms.Label
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents T_Line2 As System.Windows.Forms.Label
    Friend WithEvents T_Line3 As System.Windows.Forms.Label
    Friend WithEvents ToolBar1 As C1.Win.C1Command.C1ToolBar
    Friend WithEvents Link7 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Comm1 As System.Windows.Forms.Button
    Friend WithEvents C1TextBox7 As C1.Win.C1Input.C1TextBox
    Friend WithEvents C1TextBox3 As C1.Win.C1Input.C1TextBox
    Friend WithEvents C1TextBox8 As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label4 As System.Windows.Forms.Label
    Friend WithEvents Label3 As System.Windows.Forms.Label
    Friend WithEvents C1TextBox10 As C1.Win.C1Input.C1TextBox
    Friend WithEvents C1TextBox9 As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label10 As System.Windows.Forms.Label
    Friend WithEvents Label9 As System.Windows.Forms.Label
    Friend WithEvents Label8 As System.Windows.Forms.Label
    Friend WithEvents Label11 As System.Windows.Forms.Label
    Friend WithEvents C1TextBox11 As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label12 As System.Windows.Forms.Label
    Friend WithEvents C1TextBox12 As C1.Win.C1Input.C1TextBox
    Private WithEvents Control3 As C1.Win.C1Command.C1CommandControl
End Class
