﻿Public Class BedAllocation


#Region "变量初始化"
    Private My_Table As New DataTable            '药品字典
    Private My_Cm As CurrencyManager             '同步指针
    'Private My_Row As DataRow                    '当 前 行
    'Private V_Insert As Boolean                  '增加记录

    Dim modelPatientInfo As ModelOld.M_PatientInfo
#End Region

    Public Sub New(ByVal patient As ModelOld.M_PatientInfo)

        ' 此调用是设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        modelPatientInfo = patient
    End Sub
    Private Sub BedAllocation_Load(sender As System.Object, e As System.EventArgs) Handles MyBase.Load
        Call Form_Init()
        Call Init_Data()
    End Sub


#Region "窗体初始化"

    Private Sub Form_Init()

        '初始化MyGrid1

        With MyGrid1
            .Init_Column("病例号", "Bl_Code", "0", "中", "", False)
            .Init_Column("姓名", "Ry_Name", "60", "中", "", False)
            .Init_Column("简称", "Ry_Jc", "0", "左", "", False)
            .Init_Column("性别", "Ry_Sex", "35", "中", "yyyy-MM-dd", False)
            .Init_Column("入院日期", "Ry_RyDate", "150", "中", "", False)
            .Init_Column("病人类别", "BxLb_Name", "90", "左", "", False)
            .Init_Column("入院科室", "Ks_Name", "80", "左", "", False)
            .Init_Column("主治医师", "Ys_Name", "60", "左", "", False)
        End With
        NameMyTextBox1.Focus()
    End Sub

    Private Sub Init_Data()
        Dim strSql As String = "SELECT Bl_Code,Ry_YlCode,BxLb_Name,Ry_Name,Ry_Jc,Ry_Sex,Ry_Sfzh,Ry_Csdate,Ry_Address,Ry_RyDate" &
            ",Ry_BlCode,Ry_Tele,Jb_Name,Ys_Name,Ks_Name,Bq_Name,Bc_Name FROM Zd_BxLb,Zd_YyYs,Zd_Yyks,Bl " &
            "  left join V_YyBc on  Bl.Bc_Code=V_YyBc.Bc_Code Where Bl.Ys_code=Zd_YyYs.Ys_code and Bl.Ks_Code=Zd_YyKs.Ks_Code" &
            "  and Zd_BxLB.BxLb_Code=BL.BxLb_Code And Ry_Cyjsr is null and bl.bc_code is null and cy_qr='否'"
        If HisPara.PublicConfig.ZyCfKsXz = "是" Then
            strSql += " and Bl.Ks_Code = '" & HisVar.HisVar.XmKs & "' "
        End If
     
        My_Table = HisVar.HisVar.Sqldal.Query(strSql).Tables(0)
        With My_Table
            .PrimaryKey = New DataColumn() {.Columns("Bl_Code")}
        End With
        With Me.MyGrid1
            My_Cm = CType(BindingContext(My_Table, ""), CurrencyManager)
            .DataTable = My_Table
        End With

    End Sub


#End Region


#Region "控件动作"




    Private Sub MyGrid1_DoubleClick(sender As Object, e As System.EventArgs) Handles MyGrid1.DoubleClick
        If MyGrid1.RowCount > 0 Then
            If MsgBox("您确定要给" & MyGrid1.Columns("Ry_Name").Value & "分配" & modelPatientInfo.BedName & "号病床吗？", MsgBoxStyle.OkCancel + MsgBoxStyle.DefaultButton1, "提示：") = MsgBoxResult.Ok Then
                HisVar.HisVar.Sqldal.ExecuteSql("update bl set bc_code='" &
             modelPatientInfo.BedID & "' where bl_code='" & MyGrid1.Columns("Bl_Code").Value & "'")
                MyGrid1.Delete()
                Me.DialogResult = Windows.Forms.DialogResult.OK
            End If
            
            Me.Close()
        End If
    End Sub
#End Region



    Private Sub NameMyTextBox1_TextChanged(sender As Object, e As System.EventArgs) Handles NameMyTextBox1.TextChanged
        Dim view As DataView = My_Cm.List
        view.RowFilter = "Ry_Jc like '%" & NameMyTextBox1.Text & "%' or Ry_Name like '%" & NameMyTextBox1.Text & "%'"
    End Sub
End Class