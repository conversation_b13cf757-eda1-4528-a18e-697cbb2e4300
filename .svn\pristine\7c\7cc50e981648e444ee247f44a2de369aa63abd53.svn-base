﻿Imports System.Windows.Forms

Public Class MaterialsStockSelect
    Dim m_Wh As String
    Private strWhere As String
    Dim My_Cm As CurrencyManager             '同步指针
    Dim SelectRow As DataRow
    Private BllMaterials_Stock As New BLLOld.B_Materials_Stock
    Public ModelMaterials_Stock As New ModelOld.M_Materials_Stock

    Public Sub New(ByVal WareHouse As String, ByVal _strWhere As String)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        m_Wh = WareHouse
        strWhere = _strWhere
        ' 在 InitializeComponent() 调用之后添加任何初始化。
    End Sub

    Private Sub F_Select_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        FormInit()
        DataInit()
    End Sub

#Region "窗体初始化"
    Private Sub FormInit()
        With MyGrid1
            .Init_Grid()
            .Init_Column("物资编码", "Materials_Code", "0", "左", "", False) '0
            .Init_Column("物资名称", "Materials_Name", "120", "左", "", False) '4-
            .Init_Column("物资简称", "Materials_Py", "100", "左", "", False) '3-
            .Init_Column("物资有效期", "MaterialsExpiryDate", "120", "中", "yyyy-MM-dd", False) '1-
            .Init_Column("物资批号", "MaterialsLot", "100", "左", "", False) '3-
            .Init_Column("编码", "MaterialsStock_Code", "0", "左", "", False) '0
            .Init_Column("数量", "MaterialsStore_Num", "110", "右", "0.###", False) '1-
            .Init_Column("单价", "MaterialsStore_Price", "100", "右", "0.00##", False) '3-
            .Init_Column("金额", "MaterialsStore_Money", "120", "右", "0.00##", False) '4-
        End With
        MaterialTextBox1.Select()
    End Sub

    Private Sub DataInit()
        Dim str As String = "MaterialsStore_Num>0 "
        If m_Wh <> "" Then
            str += " and Materials_Stock.MaterialsWh_Code='" & m_Wh & "' "
        End If
        If strWhere <> "" Then
            str += " and MaterialsStock_Code not in (" & strWhere.Substring(0, strWhere.Length - 1) & ")"
        End If
        MyGrid1.DataTable = BllMaterials_Stock.GetHzList(str).Tables(0)
        My_Cm = CType(BindingContext(MyGrid1.DataSource, MyGrid1.DataMember), CurrencyManager)
    End Sub
#End Region

    Private Sub MaterialTextBox1_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MaterialTextBox1.TextChanged
        Dim My_View As DataView = My_Cm.List
        My_View.RowFilter = "Materials_Py like '*" & MaterialTextBox1.Text & "*' or Materials_Name like '*" & MaterialTextBox1.Text & "*' or Materials_Spec like '*" & MaterialTextBox1.Text & "*' or MaterialsLot like '*" & MaterialTextBox1.Text & "*'"
    End Sub

    Private Sub MaterialTextBox1_KeyDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles MaterialTextBox1.KeyDown
        If e.KeyValue = 40 Then
            MyGrid1.Focus()
            MyGrid1.MoveNext()
        End If
        If e.KeyValue = 13 Then
            ThisRow()
        End If
    End Sub

    Private Sub MyGrid1_KeyDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles MyGrid1.KeyDown
        If e.KeyValue = 13 Then
            ThisRow()
        End If

        If e.KeyValue = 38 Then
            If MyGrid1.Row = 1 Then
                MyGrid1.Focus()
                MaterialTextBox1.Select()
            End If
        End If
    End Sub

    Private Sub MyGrid1_MouseDoubleClick(ByVal sender As System.Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles MyGrid1.MouseDoubleClick
        ThisRow()
    End Sub

    Private Sub ThisRow()
        If MyGrid1.RowCount <= 0 Then Exit Sub
        If MyGrid1.Row < 0 Then Exit Sub
        SelectRow = My_Cm.List(MyGrid1.Row).Row
        With ModelMaterials_Stock
            .Materials_Code = SelectRow.Item("Materials_Code")
            .MaterialsWh_Code = SelectRow.Item("MaterialsWh_Code")
            .MaterialsStock_Code = SelectRow.Item("MaterialsStock_Code")
            .MaterialsLot = SelectRow.Item("MaterialsLot")
            .Materials_Spec = SelectRow.Item("Materials_Spec")
            .MaterialsExpiryDate = SelectRow.Item("MaterialsExpiryDate")
            .MaterialsStore_Num = SelectRow.Item("MaterialsStore_Num")
            .MaterialsStore_Price = SelectRow.Item("MaterialsStore_Price")
            .MaterialsStore_Money = SelectRow.Item("MaterialsStore_Money")
            .Materials_Name = SelectRow.Item("Materials_Name")
            .Materials_Py = SelectRow.Item("Materials_Py")
        End With

        Me.DialogResult = Windows.Forms.DialogResult.OK
    End Sub

    '英文
    Private Sub yw_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MaterialTextBox1.GotFocus
        InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages(0)
    End Sub


End Class
