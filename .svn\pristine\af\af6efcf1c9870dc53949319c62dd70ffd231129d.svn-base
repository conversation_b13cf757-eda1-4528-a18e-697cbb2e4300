﻿Imports System.Data.SqlClient
Imports System.Text.RegularExpressions
Public Class Cy_Zh21
    Public My_Dataset As New DataSet
    Dim My_Cc As New BaseClass.C_Cc()                         '取最大编码及简称的类

#Region "传参"
    'Di<PERSON> As Boolean
    Dim <PERSON>row As DataRow
    'Dim RZbtb As DataTable
    'Dim Rtdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    'Dim Rlb As C1.Win.C1Input.C1Label
    Dim Rzbadt As SqlDataAdapter

#End Region

    Public Sub New(ByVal trow As DataRow, ByVal tzbadt As SqlDataAdapter)

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        'Rinsert = tinsert
        'Rdate = tdate
        Rrow = trow
        'RZbtb = tZbtb
        'Rtdbgrid = ttdbgrid
        'Rlb = tlb
        Rzbadt = tzbadt
        ' 在 InitializeComponent() 调用之后添加任何初始化。
    End Sub
    Protected Overrides Sub OnKeyDown(ByVal e As System.Windows.Forms.KeyEventArgs)
        MyBase.OnKeyDown(e)
        If e.Control = True And e.KeyCode = Keys.S Then
            Call Comm_Click(Comm1, Nothing)
        End If
    End Sub

    Private Sub Cy_Zh21_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load

        Call Form_Init()

        Call Data_Show()
        C1TextBox1.Select()

    End Sub

#Region "窗体__事件"

    Private Sub Form_Init()
        Panel1.Height = 28

        With Me.C1DateEdit2
            '.Value = DBNull.Value
            .DateTimeInput = False     '显示日期的开关
            .AutoChangePosition = False
            .CaseSensitive = True
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .InitialSelection = C1.Win.C1Input.InitialSelectionEnum.CaretAtStart

            .VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.DropDown

            .FormatType = C1.Win.C1Input.FormatTypeEnum.ShortDate


            .EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .EditFormat.CustomFormat = "yyyy-MM-dd"

            .DisplayFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .DisplayFormat.CustomFormat = "yyyy-MM-dd"

            .MaskInfo.EditMask = "####-##-##"
            .MaskInfo.AutoTabWhenFilled = True
            .AcceptsTab = True
            .EmptyAsNull = True
            .ValueIsDbNull = True
            With .ErrorInfo
                .BeepOnError = True
                .CanLoseFocus = False
                .ErrorAction = C1.Win.C1Input.ErrorActionEnum.None
            End With
            .Value = Now
        End With


        Dim My_Combo As New BaseClass.C_Combo1(Me.C1Combo2)
        My_Combo.Init_TDBCombo()
        With C1Combo2
            .AddItem("男")
            .AddItem("女")
            .SelectedIndex = 0
            .DropDownWidth = 121
            .Width = 121
        End With

        '按扭初始化
        Call P_Comm(Me.Comm1)
        Call P_Comm(Me.Comm2)
        Call Form_Data()

        T_Combo.ReadOnly = True
        T_Combo.EditorBackColor = SystemColors.Info
        If HisPara.PublicConfig.ZyHzxg = "是" Then

            C1TextBox1.ReadOnly = True
            C1TextBox1.BackColor = SystemColors.Info
            C1Combo2.ReadOnly = True
            C1Combo2.EditorBackColor = SystemColors.Info
            C1DateEdit2.ReadOnly = True
            C1DateEdit2.BackColor = SystemColors.Info
            C1TextBox8.ReadOnly = True
            C1TextBox8.BackColor = SystemColors.Info
            C1TextBox3.ReadOnly = True
            C1TextBox3.BackColor = SystemColors.Info
            C1TextBox6.ReadOnly = True
            C1TextBox6.BackColor = SystemColors.Info
            C1TextBox4.ReadOnly = True
            C1TextBox4.BackColor = SystemColors.Info

        Else

            C1TextBox1.ReadOnly = False
            C1Combo2.ReadOnly = False
            C1DateEdit2.ReadOnly = False
            C1TextBox8.ReadOnly = False
            C1TextBox3.ReadOnly = False
            C1TextBox6.ReadOnly = False

        End If
    End Sub

    Private Sub Form_Data()     '病人字典
        If My_Dataset.Tables("病人类别") IsNot Nothing Then My_Dataset.Tables("病人类别").Clear()
        Dim My_Adapter As SqlDataAdapter = New SqlDataAdapter("Select Bxlb_Name,Bxlb_Code From Zd_Bxlb where Yy_Code='" & HisVar.HisVar.WsyCode & "'", My_Cn)
        My_Adapter.Fill(My_Dataset, "病人类别")
        Dim My_Combo As BaseClass.C_Combo2 = New BaseClass.C_Combo2(T_Combo, My_Dataset.Tables("病人类别").DefaultView, "Bxlb_Name", "Bxlb_Code", 100)
        With My_Combo
            .Init_TDBCombo()
            .Init_Colum("Bxlb_Name", "类别", 95, "中")
            .Init_Colum("Bxlb_Code", "编码", 0, "左")
            .MaxDropDownItems(15)
            .SelectedIndex(-1)
        End With
    End Sub

#End Region

#Region "数据__操作"

    Private Sub Data_Show()
        With Rrow
            C1TextBox1.Text = .Item("Ry_Name") & ""
            L_Dl_Jc.Text = .Item("Ry_Jc") & ""
            C1Combo2.Text = .Item("Ry_Sex") & ""
            C1DateEdit2.Value = .Item("Ry_Csdate")
            C1TextBox8.Text = .Item("Ry_Tele") & ""
            C1TextBox3.Text = .Item("Ry_Sfzh") & ""
            C1TextBox6.Text = .Item("Ry_Address") & ""
            T_Combo.SelectedValue = .Item("Bxlb_Code") & ""
            C1TextBox4.Text = .Item("Ry_YlCode") & ""
        End With


        If T_Combo.Text = "合作医疗" Or T_Combo.Text = "城乡居民" Then
            If HisPara.PublicConfig.ZyHzxg = "否" Then
                C1TextBox4.ReadOnly = False
                C1TextBox4.BackColor = Color.White
            End If

        Else
            C1TextBox4.ReadOnly = True
            C1TextBox4.BackColor = SystemColors.Info
            Comm1.Select()
        End If

    End Sub

#End Region

#Region "控件__动作"

    Private Sub Comm_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.Click, Comm2.Click
        Select Case sender.tag
            Case "保存"
                If T_Combo.Text = "" Then
                    MsgBox("请选择病人类别！", MsgBoxStyle.Critical, "提示")
                    T_Combo.Select()
                    Exit Sub
                End If

                If C1TextBox3.Text <> "" Then
                    If Len(C1TextBox3.Text) <> 15 And Len(C1TextBox3.Text) <> 18 Then
                        MsgBox("请输入有效身份证号！", MsgBoxStyle.Critical, "提示")

                        C1TextBox3.Select()
                        Exit Sub
                    End If
                    Dim R As Regex
                    R = New Regex("\d{18}|\d{17}[a-zA-Z]{1}|\d{15}")
                    If R.IsMatch(C1TextBox3.Text) = False Then
                        MsgBox("请输入有效身份证号！", MsgBoxStyle.Critical, "提示")
                        C1TextBox3.Select()
                        Exit Sub
                    End If
                End If

                If LTrim(RTrim(C1TextBox1.Text)) = "" Then
                    Beep()
                    MsgBox("病人姓名不能为空,按任意键返回!", vbOKOnly + vbExclamation, "提示:")
                    C1TextBox1.Select()

                    Exit Sub

                End If

                If T_Combo.Text = "合作医疗" Or T_Combo.Text = "城乡居民" Then
                    If LTrim(RTrim(C1TextBox4.Text)) = "" Then
                        MsgBox("请正确输入合作医疗编码。", vbExclamation + vbOKOnly + vbDefaultButton1, "提示:")
                        T_Combo.Select()
                        Exit Sub
                    End If
                End If

                If T_Combo.Text <> "合作医疗" And T_Combo.Text <> "城乡居民" Then
                    C1TextBox4.Text = ""
                End If

                If C1DateEdit2.ValueIsDbNull = True Then
                    MsgBox("请输入病人出生日期。", vbExclamation + vbOKOnly + vbDefaultButton1, "提示:")
                    C1DateEdit2.Select()

                    Exit Sub

                End If

                Call Data_Edit()
                MsgBox("保存成功！", MsgBoxStyle.Information + MsgBoxStyle.OkOnly, "提示")
                Me.Close()
            Case "取消"
                Me.Close()
        End Select
    End Sub


    Private Sub TextBox_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles C1TextBox1.KeyPress, C1DateEdit2.KeyPress, C1TextBox3.KeyPress, C1TextBox4.KeyPress, C1Combo2.KeyPress, T_Combo.KeyPress, C1TextBox6.KeyPress, C1TextBox8.KeyPress
        If e.KeyChar <> Chr(Keys.Return) Then Exit Sub
        e.Handled = True
        System.Windows.Forms.SendKeys.Send("{Tab}")
    End Sub

    Private Sub T_Combo_Validated(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles T_Combo.Validating
        If T_Combo.Text = "合作医疗" Or T_Combo.Text = "城乡居民" Then
            C1TextBox4.Enabled = True
            C1TextBox4.BackColor = Color.White
        Else
            C1TextBox4.Enabled = False
            C1TextBox4.BackColor = SystemColors.Info
            Comm1.Select()
        End If
    End Sub

    Private Sub C1TextBox2_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1TextBox1.Validated
        My_Cc.Get_Py(Me.C1TextBox1.Text & "")
        L_Dl_Jc.Text = My_Cc.简拚.ToString
    End Sub

#End Region

#Region "数据__编辑"

   

    Private Sub Data_Edit()

        Dim My_Row As DataRow = Rrow
        Try
            With My_Row
                .BeginEdit()
                .Item("Ry_Name") = C1TextBox1.Text
                .Item("Ry_Jc") = L_Dl_Jc.Text
                .Item("Ry_Sex") = C1Combo2.Text
                .Item("Ry_Csdate") = C1DateEdit2.Value
                .Item("Ry_Tele") = C1TextBox8.Text
                .Item("Ry_Sfzh") = C1TextBox3.Text
                .Item("Ry_Address") = C1TextBox6.Text
                .Item("Bxlb_Code") = T_Combo.SelectedValue
                .Item("Ry_YlCode") = C1TextBox4.Text
                .EndEdit()
            End With
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Critical, "提示:")
            My_Row.CancelEdit()
            Exit Sub
        Finally

        End Try

        '数据更新
        Call P_Conn(True)
        With Rzbadt.UpdateCommand()

            .Parameters(0).Value = My_Row.Item("Ry_YlCode")
            .Parameters(1).Value = My_Row.Item("Bxlb_Code")
            .Parameters(2).Value = My_Row.Item("Ry_Name")
            .Parameters(3).Value = My_Row.Item("Ry_Jc")
            .Parameters(4).Value = My_Row.Item("Ry_Sex")
            .Parameters(5).Value = My_Row.Item("Ry_Sfzh")
            .Parameters(6).Value = My_Row.Item("Ry_Csdate")
            .Parameters(7).Value = My_Row.Item("Ry_Address")
            .Parameters(8).Value = My_Row.Item("Bl_Code")
            Try
                .ExecuteNonQuery()
                My_Row.AcceptChanges()
            Catch ex As Exception
                Beep()
                MsgBox(ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
            Finally
                Call P_Conn(False)

            End Try
        End With

    End Sub

#End Region

#Region "自定义按扭"

    Private Sub P_Comm(ByVal Bt As Button)
        With Bt
            Select Case .Tag
                Case "保存"
                    .Top = 3
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")
                    .Size = New Size(52, 24)
                    .Text = "            &B"
                Case "取消"
                    .Location = New Point(Comm1.Left + Comm1.Width, Comm1.Top)
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
                    .Size = New Size(52, 24)
                    .Text = "            &C"
            End Select
            .FlatStyle = FlatStyle.Flat
            .FlatAppearance.BorderSize = 0
            .BackgroundImageLayout = ImageLayout.Center
        End With

    End Sub

    Private Sub Comm_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles Comm1.KeyDown, Comm2.KeyDown
        If e.KeyCode = Keys.Space Then
            Select Case sender.tag
                Case "保存"
                    Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存3")

                Case "取消"
                    Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消3")
            End Select
        End If
    End Sub

    Private Sub Comm_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles Comm1.KeyUp, Comm2.KeyUp
        If e.KeyCode = Keys.Enter Or e.KeyCode = Keys.Space Then
            Select Case sender.tag
                Case "保存"
                    Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")
                Case "取消"
                    Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
            End Select
        End If

    End Sub

    Private Sub Comm_MouseEnter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.MouseEnter, Comm2.MouseEnter
        Select Case sender.tag
            Case "保存"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存2")
            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消2")
        End Select
    End Sub

    Private Sub Comm_MouseLeave(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.MouseLeave, Comm2.MouseLeave
        Select Case sender.tag
            Case "保存"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")
            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
        End Select
    End Sub

    Private Sub Comm_MouseDown(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm1.MouseDown, Comm2.MouseDown
        Select Case sender.tag
            Case "保存"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存3")

            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消3")
        End Select
    End Sub

    Private Sub Comm_MouseUp(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm1.MouseUp, Comm2.MouseUp
        Select Case sender.tag
            Case "保存"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")
            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
        End Select
    End Sub

#End Region

#Region "输入法设置"
    '中文
    Private Sub C1TextBox1_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1TextBox1.GotFocus, C1TextBox6.GotFocus
        InputLanguage.CurrentInputLanguage = HisVar.HisVar.InputCode
    End Sub
    '英文
    Private Sub C1DateEdit1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1TextBox8.GotFocus, C1TextBox3.GotFocus, C1TextBox4.GotFocus
        InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages(0)
    End Sub
#End Region
    
End Class
