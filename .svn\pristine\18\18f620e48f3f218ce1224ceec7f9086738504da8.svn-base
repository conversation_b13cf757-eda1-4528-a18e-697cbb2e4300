﻿/**  版本信息模板在安装目录下，可自行修改。
* M_Yb_ConfigFile.cs
*
* 功 能： N/A
* 类 名： M_Yb_ConfigFile
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/11/19 9:11:03   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace ModelOld
{
	/// <summary>
	/// M_Yb_ConfigFile:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class M_Yb_ConfigFile
	{
		public M_Yb_ConfigFile()
		{}
		#region Model
		private string _yy_code;
		private string _zg_port;
		private string _zg_baud;
		private string _zg_dkqlx;
		private string _zg_passport;
		private string _zg_passbaud;
		private string _zg_kwdmod;
		private string _zg_softwaremode;
		private string _zg_aab034;
		private string _czjm_port;
		private string _czjm_baud;
		private string _czjm_dkqlx;
		private string _czjm_xjplx;
		private string _czjm_passport;
		private string _czjm_kwdmod;
		private string _czjm_passbaud;
		private string _czjm_softwaremode;
		private string _czjm_aab034;
		private string _gs_port;
		private string _gs_baud;
		private string _gs_dkqlx;
		private string _gs_passport;
		private string _gs_kwdmod;
		private string _gs_softwaremode;
		private string _gs_aab034;
		/// <summary>
		/// 
		/// </summary>
		public string Yy_Code
		{
			set{ _yy_code=value;}
			get{return _yy_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string ZG_Port
		{
			set{ _zg_port=value;}
			get{return _zg_port;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string ZG_Baud
		{
			set{ _zg_baud=value;}
			get{return _zg_baud;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string ZG_dkqlx
		{
			set{ _zg_dkqlx=value;}
			get{return _zg_dkqlx;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string ZG_PassPort
		{
			set{ _zg_passport=value;}
			get{return _zg_passport;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string ZG_passBaud
		{
			set{ _zg_passbaud=value;}
			get{return _zg_passbaud;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string ZG_KWDMOD
		{
			set{ _zg_kwdmod=value;}
			get{return _zg_kwdmod;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string ZG_SoftwareMode
		{
			set{ _zg_softwaremode=value;}
			get{return _zg_softwaremode;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string ZG_AAB034
		{
			set{ _zg_aab034=value;}
			get{return _zg_aab034;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string CZJM_Port
		{
			set{ _czjm_port=value;}
			get{return _czjm_port;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string CZJM_Baud
		{
			set{ _czjm_baud=value;}
			get{return _czjm_baud;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string CZJM_dkqlx
		{
			set{ _czjm_dkqlx=value;}
			get{return _czjm_dkqlx;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string CZJM_XJPLX
		{
			set{ _czjm_xjplx=value;}
			get{return _czjm_xjplx;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string CZJM_PassPort
		{
			set{ _czjm_passport=value;}
			get{return _czjm_passport;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string CZJM_KWDMOD
		{
			set{ _czjm_kwdmod=value;}
			get{return _czjm_kwdmod;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string CZJM_passBaud
		{
			set{ _czjm_passbaud=value;}
			get{return _czjm_passbaud;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string CZJM_SoftwareMode
		{
			set{ _czjm_softwaremode=value;}
			get{return _czjm_softwaremode;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string CZJM_AAB034
		{
			set{ _czjm_aab034=value;}
			get{return _czjm_aab034;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string GS_Port
		{
			set{ _gs_port=value;}
			get{return _gs_port;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string GS_Baud
		{
			set{ _gs_baud=value;}
			get{return _gs_baud;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string GS_dkqlx
		{
			set{ _gs_dkqlx=value;}
			get{return _gs_dkqlx;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string GS_PassPort
		{
			set{ _gs_passport=value;}
			get{return _gs_passport;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string GS_KWDMOD
		{
			set{ _gs_kwdmod=value;}
			get{return _gs_kwdmod;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string GS_SoftwareMode
		{
			set{ _gs_softwaremode=value;}
			get{return _gs_softwaremode;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string GS_AAB034
		{
			set{ _gs_aab034=value;}
			get{return _gs_aab034;}
		}
		#endregion Model

	}
}

