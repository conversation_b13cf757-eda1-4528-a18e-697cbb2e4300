﻿/**  版本信息模板在安装目录下，可自行修改。
* D_Bl_TWD2.cs
*
* 功 能： N/A
* 类 名： D_Bl_TWD2
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/6/23 15:47:22   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using System.Collections.Generic;
using Common;

namespace DAL
{
    /// <summary>
    /// 数据访问类:D_Bl_TWD2
    /// </summary>
    public partial class D_Bl_TWD2
    {
        public D_Bl_TWD2()
        { }
        #region  BasicMethod

        /// <summary>
        /// 得到最大ID
        /// </summary>
        public int GetMaxId()
        {
            return HisVar.HisVar.Sqldal.GetMaxID("CL_Time", "Bl_TWD2");
        }

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(string CL_Code, int CL_Time)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from Bl_TWD2");
            strSql.Append(" where CL_Code=@CL_Code and CL_Time=@CL_Time ");
            SqlParameter[] parameters = {
                    new SqlParameter("@CL_Code", SqlDbType.Char,10),
                    new SqlParameter("@CL_Time", SqlDbType.VarChar ,5)          };
            parameters[0].Value = CL_Code;
            parameters[1].Value = CL_Time;

            return HisVar.HisVar.Sqldal.Exists(strSql.ToString(), parameters);
        }

        /// <summary>
        /// 增加若干条数据
        /// </summary>
        public bool Add(ModelOld.M_Bl_TWD1 Model1, List<ModelOld.M_Bl_TWD2> list)
        {
            List<string> li = new List<string>();
            List<SqlParameter[]> liPara = new List<SqlParameter[]>();
            StringBuilder strSql;
            SqlParameter[] parameters;


            strSql = new StringBuilder();
            strSql.Append("insert into Bl_TWD1(");
            strSql.Append("Bl_Code,CL_Code,CL_Date,Height,Weight,ZRL,ZCL,YLL,DBCS,XBCS,XBL,GMYW1,PSJG,XY1,XY2,IsPregnant,Jsr_Code,Lr_Date)");
            strSql.Append(" values (");
            strSql.Append("@Bl_Code,@CL_Code,@CL_Date,@Height,@Weight,@ZRL,@ZCL,@YLL,@DBCS,@XBCS,@XBL,@GMYW1,@PSJG,@XY1,@XY2,@IsPregnant,@Jsr_Code,@Lr_Date)");

            li.Add(strSql.ToString());

            parameters = new SqlParameter[] {
                    new SqlParameter("@Bl_Code", SqlDbType.Char,14),
                    new SqlParameter("@CL_Code", SqlDbType.Char,10),
                    new SqlParameter("@CL_Date", SqlDbType.SmallDateTime),
                    new SqlParameter("@Height", SqlDbType.Int,4),
                    new SqlParameter("@Weight", SqlDbType.Decimal,9),
                    new SqlParameter("@ZRL", SqlDbType.Decimal,9),
                    new SqlParameter("@ZCL", SqlDbType.Decimal,9),
                    new SqlParameter("@YLL", SqlDbType.Decimal,9),
                    new SqlParameter("@DBCS", SqlDbType.VarChar,50),
                    new SqlParameter("@XBCS", SqlDbType.Int,4),
                    new SqlParameter("@XBL", SqlDbType.Decimal,9),
                    new SqlParameter("@GMYW1", SqlDbType.VarChar,50),
                    new SqlParameter("@PSJG", SqlDbType.VarChar,50),
                    new SqlParameter("@XY1", SqlDbType.VarChar,50),
                    new SqlParameter("@XY2", SqlDbType.VarChar,50),
                    new SqlParameter("@IsPregnant", SqlDbType.Bit,1),
                    new SqlParameter("@Jsr_Code", SqlDbType.Char,7),
                    new SqlParameter("@Lr_Date", SqlDbType.SmallDateTime)};
            parameters[0].Value = Model1.Bl_Code;
            parameters[1].Value = Model1.CL_Code;
            parameters[2].Value = Model1.CL_Date;
            parameters[3].Value = Common.Tools.IsValueNull(Model1.Height);
            parameters[4].Value = Common.Tools.IsValueNull(Model1.Weight);
            parameters[5].Value = Common.Tools.IsValueNull(Model1.ZRL);
            parameters[6].Value = Common.Tools.IsValueNull(Model1.ZCL);
            if (Model1.YLL == null)
            {
                parameters[7].Value = DBNull.Value;
            }
            else
            {
                parameters[7].Value = Model1.YLL;
            }

            parameters[8].Value = Model1.DBCS;
            if (Model1.XBCS == null)
            {
                parameters[9].Value = DBNull.Value;
            }
            else
            {
                parameters[9].Value = Model1.XBCS;
            }
            parameters[10].Value = Common.Tools.IsValueNull(Model1.XBL);
            parameters[11].Value = Model1.GMYW1;
            parameters[12].Value = Model1.PSJG;
            parameters[13].Value = Model1.XY1;

            if (Model1.XY2 == null)
            {
                parameters[14].Value = DBNull.Value;
            }
            else
            {
                parameters[14].Value = Model1.XY2;
            }

            parameters[15].Value = Model1.IsPregnant;


            parameters[16].Value = Model1.Jsr_Code;
            parameters[17].Value = Model1.Lr_Date;

            liPara.Add(parameters);

            foreach (ModelOld.M_Bl_TWD2 Model in list)
            {
                strSql = new StringBuilder();
                strSql.Append("insert into Bl_TWD2(");
                strSql.Append("CL_Code,CL_Time,TWBW,Temperature,Pulse,HeartRate,Breath,WLJW,Event,EventTime,SpecialEvent,isFH,isHXJ,isXZQBQ,Jsr_Code,Lr_Date)");
                strSql.Append(" values (");
                strSql.Append("@CL_Code,@CL_Time,@TWBW,@Temperature,@Pulse,@HeartRate,@Breath,@WLJW,@Event,@EventTime,@SpecialEvent,@isFH,@isHXJ,@isXZQBQ,@Jsr_Code,@Lr_Date)");

                parameters = new SqlParameter[] {
                    new SqlParameter("@CL_Code", SqlDbType.Char,10),
                    new SqlParameter("@CL_Time", SqlDbType.VarChar ,5),
                    new SqlParameter("@TWBW", SqlDbType.Char,4),
                    new SqlParameter("@Temperature", SqlDbType.Decimal,9),
                    new SqlParameter("@Pulse", SqlDbType.Int,4),
                    new SqlParameter("@HeartRate",SqlDbType.Int,4),
                    new SqlParameter("@Breath", SqlDbType.Int,4),
                    new SqlParameter("@WLJW", SqlDbType.Decimal,9),
                    new SqlParameter("@Event", SqlDbType.VarChar,50),
                    new SqlParameter("@EventTime", SqlDbType.VarChar,50),
                    new SqlParameter("@SpecialEvent", SqlDbType.VarChar,50),
                    new SqlParameter("@isFH", SqlDbType.Bit,1),
                    new SqlParameter("@isHXJ", SqlDbType.Bit,1),
                    new SqlParameter("@isXZQBQ", SqlDbType.Bit,1),
                    new SqlParameter("@Jsr_Code", SqlDbType.Char,7),
                    new SqlParameter("@Lr_Date", SqlDbType.SmallDateTime)};
                parameters[0].Value = Model.CL_Code;
                parameters[1].Value = Model.CL_Time;
                parameters[2].Value = Common.Tools.IsValueNull(Model.TWBW);
                parameters[3].Value = Common.Tools.IsValueNull(Model.Temperature);
                parameters[4].Value = Common.Tools.IsValueNull(Model.Pulse);
                parameters[5].Value = Common.Tools.IsValueNull(Model.HeartRate);
                parameters[6].Value = Common.Tools.IsValueNull(Model.Breath);
                parameters[7].Value = Common.Tools.IsValueNull(Model.WLJW);
                parameters[8].Value = Common.Tools.IsValueNull(Model.Event);
                parameters[9].Value = Common.Tools.IsValueNull(Model.EventTime);
                parameters[10].Value = Common.Tools.IsValueNull(Model.SpecialEvent);
                parameters[11].Value = Common.Tools.IsValueNull(Model.isFH);
                parameters[12].Value = Common.Tools.IsValueNull(Model.isHXJ);
                parameters[13].Value = Common.Tools.IsValueNull(Model.isXZQBQ);
                parameters[14].Value = Model.Jsr_Code;
                parameters[15].Value = Model.Lr_Date;

                li.Add(strSql.ToString());
                liPara.Add(parameters);
            }

            try
            {
                int rows = HisVar.HisVar.Sqldal.ExecuteSql(li, liPara);
                if (rows > 0)
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
            catch (Exception ex)
            {
                return false;
            }

        }
        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ModelOld.M_Bl_TWD2 Model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into Bl_TWD2(");
            strSql.Append("CL_Code,CL_Time,TWBW,Temperature,Pulse,HeartRate,Breath,WLJW,Event,EventTime,SpecialEvent,isFH,isHXJ,isXZQBQ,Jsr_Code,Lr_Date)");
            strSql.Append(" values (");
            strSql.Append("@CL_Code,@CL_Time,@TWBW,@Temperature,@Pulse,@HeartRate,@Breath,@WLJW,@Event,@EventTime,@SpecialEvent,@isFH,@isHXJ,@isXZQBQ,@Jsr_Code,@Lr_Date)");
            SqlParameter[] parameters = {
                    new SqlParameter("@CL_Code", SqlDbType.Char,10),
                    new SqlParameter("@CL_Time", SqlDbType.VarChar ,5),
                    new SqlParameter("@TWBW", SqlDbType.Char,4),
                    new SqlParameter("@Temperature", SqlDbType.Decimal,9),
                    new SqlParameter("@Pulse", SqlDbType.Int,4),
                    new SqlParameter("@HeartRate",SqlDbType.Int,4),
                    new SqlParameter("@Breath", SqlDbType.Int,4),
                    new SqlParameter("@WLJW", SqlDbType.Decimal,9),
                    new SqlParameter("@Event", SqlDbType.VarChar,50),
                    new SqlParameter("@EventTime", SqlDbType.VarChar,50),
                    new SqlParameter("@SpecialEvent", SqlDbType.VarChar,50),
                    new SqlParameter("@isFH", SqlDbType.Bit,1),
                    new SqlParameter("@isHXJ", SqlDbType.Bit,1),
                    new SqlParameter("@isXZQBQ", SqlDbType.Bit,1),
                    new SqlParameter("@Jsr_Code", SqlDbType.Char,7),
                    new SqlParameter("@Lr_Date", SqlDbType.SmallDateTime)};
            parameters[0].Value = Model.CL_Code;
            parameters[1].Value = Model.CL_Time;
            parameters[2].Value = Common.Tools.IsValueNull(Model.TWBW);
            parameters[3].Value = Common.Tools.IsValueNull(Model.Temperature);
            parameters[4].Value = Common.Tools.IsValueNull(Model.Pulse);
            parameters[5].Value = Common.Tools.IsValueNull(Model.HeartRate);
            parameters[6].Value = Common.Tools.IsValueNull(Model.Breath);
            parameters[7].Value = Common.Tools.IsValueNull(Model.WLJW);
            parameters[8].Value = Common.Tools.IsValueNull(Model.Event);
            parameters[9].Value = Common.Tools.IsValueNull(Model.EventTime);
            parameters[10].Value = Common.Tools.IsValueNull(Model.SpecialEvent);
            parameters[11].Value = Common.Tools.IsValueNull(Model.isFH);
            parameters[12].Value = Common.Tools.IsValueNull(Model.isHXJ);
            parameters[13].Value = Common.Tools.IsValueNull(Model.isXZQBQ);
            parameters[14].Value = Model.Jsr_Code;
            parameters[15].Value = Model.Lr_Date;

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 更新若干条数据
        /// </summary>
        public bool Update(ModelOld.M_Bl_TWD1 Model1, List<ModelOld.M_Bl_TWD2> list)
        {
            List<string> li = new List<string>();
            List<SqlParameter[]> liPara = new List<SqlParameter[]>();
            StringBuilder strSql;
            SqlParameter[] parameters;

            strSql = new StringBuilder();
            strSql.Append("update Bl_TWD1 set ");
            strSql.Append("Bl_Code=@Bl_Code,");
            strSql.Append("CL_Date=@CL_Date,");
            strSql.Append("Height=@Height,");
            strSql.Append("Weight=@Weight,");
            strSql.Append("ZRL=@ZRL,");
            strSql.Append("ZCL=@ZCL,");
            strSql.Append("YLL=@YLL,");
            strSql.Append("DBCS=@DBCS,");
            strSql.Append("XBCS=@XBCS,");
            strSql.Append("XBL=@XBL,");
            strSql.Append("GMYW1=@GMYW1,");
            strSql.Append("PSJG=@PSJG,");
            strSql.Append("XY1=@XY1,");
            strSql.Append("XY2=@XY2,");
            strSql.Append("IsPregnant=@IsPregnant,");
            strSql.Append("Jsr_Code=@Jsr_Code,");
            strSql.Append("Lr_Date=@Lr_Date");
            strSql.Append(" where CL_Code=@CL_Code ");
            li.Add(strSql.ToString());
            parameters = new SqlParameter[]{
                    new SqlParameter("@Bl_Code", SqlDbType.Char,14),
                    new SqlParameter("@CL_Date", SqlDbType.SmallDateTime),
                    new SqlParameter("@Height", SqlDbType.Int,4),
                    new SqlParameter("@Weight", SqlDbType.Decimal,9),
                    new SqlParameter("@ZRL", SqlDbType.Decimal,9),
                    new SqlParameter("@ZCL", SqlDbType.Decimal,9),
                    new SqlParameter("@YLL", SqlDbType.Decimal,9),
                    new SqlParameter("@DBCS", SqlDbType.VarChar,50),
                    new SqlParameter("@XBCS", SqlDbType.Int,4),
                    new SqlParameter("@XBL", SqlDbType.Decimal,9),
                    new SqlParameter("@GMYW1", SqlDbType.VarChar,50),
                    new SqlParameter("@PSJG", SqlDbType.VarChar,50),
                    new SqlParameter("@XY1", SqlDbType.VarChar,50),
                    new SqlParameter("@XY2", SqlDbType.VarChar,50),
                    new SqlParameter("@IsPregnant", SqlDbType.Bit,1),
                    new SqlParameter("@Jsr_Code", SqlDbType.Char,7),
                    new SqlParameter("@Lr_Date", SqlDbType.SmallDateTime),
                    new SqlParameter("@CL_Code", SqlDbType.Char,10)};
            parameters[0].Value = Model1.Bl_Code;
            parameters[1].Value = Model1.CL_Date;
            parameters[2].Value = Common.Tools.IsValueNull(Model1.Height);
            parameters[3].Value = Common.Tools.IsValueNull(Model1.Weight);
            parameters[4].Value = Common.Tools.IsValueNull(Model1.ZRL);
            parameters[5].Value = Common.Tools.IsValueNull(Model1.ZCL);
            if (Model1.YLL == null)
            {
                parameters[6].Value = DBNull.Value;
            }
            else
            {
                parameters[6].Value = Model1.YLL;
            }

            parameters[7].Value = Model1.DBCS;
            if (Model1.XBCS == null)
            {
                parameters[8].Value = DBNull.Value;
            }
            else
            {
                parameters[8].Value = Model1.XBCS;
            }
            parameters[9].Value = Common.Tools.IsValueNull(Model1.XBL);
            parameters[10].Value = Model1.GMYW1;
            parameters[11].Value = Model1.PSJG;
            parameters[12].Value = Model1.XY1;

            if (Model1.XY2 == null)
            {
                parameters[13].Value = DBNull.Value;
            }
            else
            {
                parameters[13].Value = Model1.XY2;
            }

            parameters[14].Value = Model1.IsPregnant;
            parameters[15].Value = Model1.Jsr_Code;
            parameters[16].Value = Model1.Lr_Date;
            parameters[17].Value = Model1.CL_Code;
            liPara.Add(parameters);

            foreach (ModelOld.M_Bl_TWD2 Model in list)
            {
                strSql = new StringBuilder();
                strSql.Append("update Bl_TWD2 set ");
                strSql.Append("TWBW=@TWBW,");
                strSql.Append("Temperature=@Temperature,");
                strSql.Append("Pulse=@Pulse,");
                strSql.Append("HeartRate=@HeartRate,");
                strSql.Append("Breath=@Breath,");
                strSql.Append("WLJW=@WLJW,");
                strSql.Append("Event=@Event,");
                strSql.Append("EventTime=@EventTime,");
                strSql.Append("SpecialEvent=@SpecialEvent,");
                strSql.Append("isFH=@isFH,");
                strSql.Append("isHXJ=@isHXJ,");
                strSql.Append("isXZQBQ=@isXZQBQ,");
                strSql.Append("Jsr_Code=@Jsr_Code,");
                strSql.Append("Lr_Date=@Lr_Date");
                strSql.Append(" where CL_Code=@CL_Code and CL_Time=@CL_Time ");
                parameters = new SqlParameter[]{
                    new SqlParameter("@TWBW", SqlDbType.Char,4),
                    new SqlParameter("@Temperature", SqlDbType.Decimal,9),
                    new SqlParameter("@Pulse", SqlDbType.Int,4),
                    new SqlParameter("@HeartRate",SqlDbType.Int,4),
                    new SqlParameter("@Breath", SqlDbType.Int,4),
                    new SqlParameter("@WLJW", SqlDbType.Decimal,9),
                    new SqlParameter("@Event", SqlDbType.VarChar,50),
                    new SqlParameter("@EventTime", SqlDbType.VarChar,50),
                    new SqlParameter("@SpecialEvent", SqlDbType.VarChar,50),
                    new SqlParameter("@isFH", SqlDbType.Bit,1),
                    new SqlParameter("@isHXJ", SqlDbType.Bit,1),
                    new SqlParameter("@isXZQBQ", SqlDbType.Bit,1),
                    new SqlParameter("@Jsr_Code", SqlDbType.Char,7),
                    new SqlParameter("@Lr_Date", SqlDbType.SmallDateTime),
                    new SqlParameter("@CL_Code", SqlDbType.Char,10),
                    new SqlParameter("@CL_Time", SqlDbType.VarChar ,5)};
                parameters[0].Value = Common.Tools.IsValueNull(Model.TWBW);
                parameters[1].Value = Common.Tools.IsValueNull(Model.Temperature);
                parameters[2].Value = Common.Tools.IsValueNull(Model.Pulse);
                parameters[3].Value = Common.Tools.IsValueNull(Model.HeartRate);
                parameters[4].Value = Common.Tools.IsValueNull(Model.Breath);
                parameters[5].Value = Common.Tools.IsValueNull(Model.WLJW);
                parameters[6].Value = Common.Tools.IsValueNull(Model.Event);
                parameters[7].Value = Common.Tools.IsValueNull(Model.EventTime);
                parameters[8].Value = Common.Tools.IsValueNull(Model.SpecialEvent);
                parameters[9].Value = Common.Tools.IsValueNull(Model.isFH);
                parameters[10].Value = Common.Tools.IsValueNull(Model.isHXJ);
                parameters[11].Value = Common.Tools.IsValueNull(Model.isXZQBQ);
                parameters[12].Value = Model.Jsr_Code;
                parameters[13].Value = Model.Lr_Date;
                parameters[14].Value = Model.CL_Code;
                parameters[15].Value = Model.CL_Time;
                li.Add(strSql.ToString());
                liPara.Add(parameters);
            }

            try
            {
                int rows = HisVar.HisVar.Sqldal.ExecuteSql(li, liPara);
                if (rows > 0)
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
            catch (Exception ex)
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ModelOld.M_Bl_TWD2 Model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update Bl_TWD2 set ");
            strSql.Append("TWBW=@TWBW,");
            strSql.Append("Temperature=@Temperature,");
            strSql.Append("Pulse=@Pulse,");
            strSql.Append("HeartRate=@HeartRate");
            strSql.Append("Breath=@Breath,");
            strSql.Append("WLJW=@WLJW,");
            strSql.Append("Event=@Event,");
            strSql.Append("EventTime=@EventTime,");
            strSql.Append("SpecialEvent=@SpecialEvent,");
            strSql.Append("isFH=@isFH,");
            strSql.Append("isHXJ=@isHXJ,");
            strSql.Append("isXZQBQ=@isXZQBQ,");
            strSql.Append("Jsr_Code=@Jsr_Code,");
            strSql.Append("Lr_Date=@Lr_Date");
            strSql.Append(" where CL_Code=@CL_Code and CL_Time=@CL_Time ");
            SqlParameter[] parameters = {
                    new SqlParameter("@TWBW", SqlDbType.Char,4),
                    new SqlParameter("@Temperature", SqlDbType.Decimal,9),
                    new SqlParameter("@Pulse", SqlDbType.Int,4),
                    new SqlParameter("@HeartRate",SqlDbType.Int,4),
                    new SqlParameter("@Breath", SqlDbType.Int,4),
                    new SqlParameter("@WLJW", SqlDbType.Decimal,9),
                    new SqlParameter("@Event", SqlDbType.VarChar,50),
                    new SqlParameter("@EventTime", SqlDbType.VarChar,50),
                    new SqlParameter("@SpecialEvent", SqlDbType.VarChar,50),
                    new SqlParameter("@isFH", SqlDbType.Bit,1),
                    new SqlParameter("@isHXJ", SqlDbType.Bit,1),
                    new SqlParameter("@isXZQBQ", SqlDbType.Bit,1),
                    new SqlParameter("@Jsr_Code", SqlDbType.Char,7),
                    new SqlParameter("@Lr_Date", SqlDbType.SmallDateTime),
                    new SqlParameter("@CL_Code", SqlDbType.Char,10),
                    new SqlParameter("@CL_Time", SqlDbType.VarChar ,5)};
            parameters[0].Value = Common.Tools.IsValueNull(Model.TWBW);
            parameters[1].Value = Common.Tools.IsValueNull(Model.Temperature);
            parameters[2].Value = Common.Tools.IsValueNull(Model.Pulse);
            parameters[3].Value = Common.Tools.IsValueNull(Model.HeartRate);
            parameters[4].Value = Common.Tools.IsValueNull(Model.Breath);
            parameters[5].Value = Common.Tools.IsValueNull(Model.WLJW);
            parameters[6].Value = Common.Tools.IsValueNull(Model.Event);
            parameters[7].Value = Common.Tools.IsValueNull(Model.EventTime);
            parameters[8].Value = Common.Tools.IsValueNull(Model.SpecialEvent);
            parameters[9].Value = Common.Tools.IsValueNull(Model.isFH);
            parameters[10].Value = Common.Tools.IsValueNull(Model.isHXJ);
            parameters[11].Value = Common.Tools.IsValueNull(Model.isXZQBQ);
            parameters[12].Value = Model.Jsr_Code;
            parameters[13].Value = Model.Lr_Date;
            parameters[14].Value = Model.CL_Code;
            parameters[15].Value = Model.CL_Time;

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(string CL_Code, int CL_Time)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from Bl_TWD2 ");
            strSql.Append(" where CL_Code=@CL_Code and CL_Time=@CL_Time ");
            SqlParameter[] parameters = {
                    new SqlParameter("@CL_Code", SqlDbType.Char,10),
                    new SqlParameter("@CL_Time", SqlDbType.VarChar ,5)          };
            parameters[0].Value = CL_Code;
            parameters[1].Value = CL_Time;

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(string CL_Code)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from Bl_TWD2 ");
            strSql.Append(" where CL_Code=@CL_Code ");
            SqlParameter[] parameters = {
                    new SqlParameter("@CL_Code", SqlDbType.Char,10)     };
            parameters[0].Value = CL_Code;


            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ModelOld.M_Bl_TWD2 GetModel(string CL_Code, int CL_Time)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 CL_Code,CL_Time,TWBW,Temperature,Pulse,HeartRate,Breath,WLJW,Event,EventTime,SpecialEvent,isFH,isHXJ,isXZQBQ,Jsr_Code,Lr_Date from Bl_TWD2 ");
            strSql.Append(" where CL_Code=@CL_Code and CL_Time=@CL_Time ");
            SqlParameter[] parameters = {
                    new SqlParameter("@CL_Code", SqlDbType.Char,10),
                    new SqlParameter("@CL_Time", SqlDbType.VarChar ,5)          };
            parameters[0].Value = CL_Code;
            parameters[1].Value = CL_Time;

            ModelOld.M_Bl_TWD2 Model = new ModelOld.M_Bl_TWD2();
            DataSet ds = HisVar.HisVar.Sqldal.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ModelOld.M_Bl_TWD2 DataRowToModel(DataRow row)
        {
            ModelOld.M_Bl_TWD2 Model = new ModelOld.M_Bl_TWD2();
            if (row != null)
            {
                if (row["CL_Code"] != null)
                {
                    Model.CL_Code = row["CL_Code"].ToString();
                }
                if (row["CL_Time"] != null && row["CL_Time"].ToString() != "")
                {
                    Model.CL_Time = row["CL_Time"].ToString();
                }
                if (row["TWBW"] != null)
                {
                    Model.TWBW = row["TWBW"].ToString();
                }
                if (row["Temperature"] != null && row["Temperature"].ToString() != "")
                {
                    Model.Temperature = decimal.Parse(row["Temperature"].ToString());
                }
                if (row["Pulse"] != null && row["Pulse"].ToString() != "")
                {
                    Model.Pulse = int.Parse(row["Pulse"].ToString());
                }
                if (row["HeartRate"] != null && row["HeartRate"].ToString() != "")
                {
                    Model.Pulse = int.Parse(row["HeartRate"].ToString());
                }
                if (row["Breath"] != null && row["Breath"].ToString() != "")
                {
                    Model.Breath = int.Parse(row["Breath"].ToString());
                }
                if (row["WLJW"] != null && row["WLJW"].ToString() != "")
                {
                    Model.WLJW = decimal.Parse(row["WLJW"].ToString());
                }
                if (row["Event"] != null)
                {
                    Model.Event = row["Event"].ToString();
                }
                if (row["EventTime"] != null)
                {
                    Model.EventTime = row["EventTime"].ToString();
                }
                if (row["SpecialEvent"] != null)
                {
                    Model.SpecialEvent = row["SpecialEvent"].ToString();
                }
                if (row["isFH"] != null && row["isFH"].ToString() != "")
                {
                    if ((row["isFH"].ToString() == "1") || (row["isFH"].ToString().ToLower() == "true"))
                    {
                        Model.isFH = true;
                    }
                    else
                    {
                        Model.isFH = false;
                    }
                }
                if (row["isHXJ"] != null && row["isHXJ"].ToString() != "")
                {
                    if ((row["isHXJ"].ToString() == "1") || (row["isHXJ"].ToString().ToLower() == "true"))
                    {
                        Model.isHXJ = true;
                    }
                    else
                    {
                        Model.isHXJ = false;
                    }
                }
                if (row["isXZQBQ"] != null && row["isXZQBQ"].ToString() != "")
                {
                    if ((row["isXZQBQ"].ToString() == "1") || (row["isXZQBQ"].ToString().ToLower() == "true"))
                    {
                        Model.isXZQBQ = true;
                    }
                    else
                    {
                        Model.isXZQBQ = false;
                    }
                }
                if (row["Jsr_Code"] != null)
                {
                    Model.Jsr_Code = row["Jsr_Code"].ToString();
                }
                if (row["Lr_Date"] != null && row["Lr_Date"].ToString() != "")
                {
                    Model.Lr_Date = DateTime.Parse(row["Lr_Date"].ToString());
                }
            }
            return Model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("	SELECT CL_Code, CL_Time, TWBW, Temperature, Pulse,HeartRate, Breath, WLJW, Event,");
            strSql.Append(" EventTime, SpecialEvent, isFH, isHXJ, isXZQBQ, dbo.Bl_TWD2.Jsr_Code, Lr_Date,Jsr_Name");
            strSql.Append("	FROM dbo.Bl_TWD2 JOIN dbo.Zd_YyJsr ON Zd_YyJsr.Jsr_Code = Bl_TWD2.Jsr_Code ");

            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }


        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetListForChart(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("   SELECT dbo.Bl_TWD2.CL_Code,");
            strSql.Append(" Cl_Time, ");
            strSql.Append("	 TWBW, Temperature , Pulse, HeartRate,Breath , ");
            strSql.Append("   WLJW, Event, EventTime, SpecialEvent, isFH, isHXJ, ");
            strSql.Append("   isXZQBQ ,dbo.Bl_TWD2. Jsr_Code,jsr_name,dbo.Bl_TWD2.Lr_Date,CL_Date");
            strSql.Append("    FROM dbo.Bl_TWD2 JOIN dbo.Bl_TWD1 ON Bl_TWD1.CL_Code = Bl_TWD2.CL_Code");
            strSql.Append("    JOIN dbo.Zd_YyJsr ON Bl_TWD2.Jsr_Code=Zd_YyJsr.Jsr_Code");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" CL_Code,CL_Time,TWBW,Temperature,Pulse,HeartRate,Breath,WLJW,Event,EventTime,SpecialEvent,isFH,isHXJ,isXZQBQ,Jsr_Code,Lr_Date ");
            strSql.Append(" FROM Bl_TWD2 ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM Bl_TWD2 ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.CL_Time desc");
            }
            strSql.Append(")AS Row, T.*  from Bl_TWD2 T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Bl_TWD2";
			parameters[1].Value = "CL_Time";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return HisVar.HisVar.Sqldal.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod
        #region  ExtensionMethod

        #endregion  ExtensionMethod
    }
}

