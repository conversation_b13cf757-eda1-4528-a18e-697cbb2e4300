﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="0" />
    <DataSources isList="true" count="0" />
    <Relations isList="true" count="0" />
    <Report isRef="0" />
    <Variables isList="true" count="0" />
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="3">
    <Page1 Ref="2" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="2">
        <SubReport1 Ref="3" type="SubReport" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>1.9,0.8,6.2,6.6</ClientRectangle>
          <Components isList="true" count="0" />
          <Name>SubReport1</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <SubReportPageGuid>7de8f77438c943b59f634a93c47bfefe</SubReportPageGuid>
        </SubReport1>
        <SubReport2 Ref="4" type="SubReport" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>1.8,7.8,6.3,5.7</ClientRectangle>
          <Components isList="true" count="0" />
          <Name>SubReport2</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <SubReportPageGuid>c8b7073b6b704f5289d3ffd453c1e89c</SubReportPageGuid>
        </SubReport2>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>64c3bd76ea874403920186d8fc9a62c6</Guid>
      <Margins>1,1,1,1</Margins>
      <Name>Page1</Name>
      <PageHeight>29.7</PageHeight>
      <PageWidth>21</PageWidth>
      <Report isRef="0" />
      <Watermark Ref="5" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page1>
    <Page2 Ref="6" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="0" />
      <Conditions isList="true" count="0" />
      <Guid>7de8f77438c943b59f634a93c47bfefe</Guid>
      <Margins>1,1,1,1</Margins>
      <Name>Page2</Name>
      <PageHeight>29.7</PageHeight>
      <PageWidth>8.2</PageWidth>
      <Report isRef="0" />
      <Watermark Ref="7" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page2>
    <Page3 Ref="8" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="0" />
      <Conditions isList="true" count="0" />
      <Guid>c8b7073b6b704f5289d3ffd453c1e89c</Guid>
      <Margins>1,1,1,1</Margins>
      <Name>Page3</Name>
      <PageHeight>29.7</PageHeight>
      <PageWidth>8.3</PageWidth>
      <Report isRef="0" />
      <Watermark Ref="9" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page3>
  </Pages>
  <PrinterSettings Ref="10" type="Stimulsoft.Report.Print.StiPrinterSettings" isKey="true" />
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>Report</ReportAlias>
  <ReportChanged>2/2/2012 3:06:29 PM</ReportChanged>
  <ReportCreated>2/2/2012 3:04:27 PM</ReportCreated>
  <ReportFile />
  <ReportGuid>dd846c0733eb42118dc709c2a7fd1b7e</ReportGuid>
  <ReportName>Report</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2011.2.1026</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class Report : Stimulsoft.Report.StiReport
    {
        public Report()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
        #endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>