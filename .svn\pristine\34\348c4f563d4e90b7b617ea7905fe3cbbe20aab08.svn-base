/****** Object:  Table [dbo].[ApiConfig]    Script Date: 2025/3/19 9:05:12 ******/
SET ANSI_NULLS ON;
GO
SET QUOTED_IDENTIFIER ON;
GO
CREATE TABLE [dbo].[ApiConfig] ([ID] [INT] NOT NULL,
[ConfigType] [NVARCHAR](50) NOT NULL,
[ConfigKey] [NVARCHAR](100) NOT NULL,
[ConfigValue] [NVARCHAR](500) NOT NULL,
[Description] [NVARCHAR](255) NULL,
[CreatedAt] [DATETIME] NULL,
[UpdatedAt] [DATETIME] NULL,
CONSTRAINT [PK__ApiConfi] PRIMARY KEY CLUSTERED([ID] ASC)WITH(PAD_INDEX=OFF, STATISTICS_NORECOMPUTE=OFF, IGNORE_DUP_KEY=OFF, ALLOW_ROW_LOCKS= ON, ALLOW_PAGE_LOCKS=ON)ON [PRIMARY],
CONSTRAINT [UQ__ApiConfi] UNIQUE NONCLUSTERED([ConfigType] ASC, [ConfigKey] ASC)WITH(PAD_INDEX=OFF, STATISTICS_NORECOMPUTE=OFF, IGNORE_DUP_KEY=OFF, ALLOW_ROW_LOCKS=ON, ALLOW_PAGE_LOCKS=ON)ON [PRIMARY])ON [PRIMARY];
GO
ALTER TABLE [dbo].[ApiConfig] ADD DEFAULT(GETDATE())FOR [CreatedAt];
GO
ALTER TABLE [dbo].[ApiConfig] ADD DEFAULT(GETDATE())FOR [UpdatedAt];
GO

INSERT INTO dbo.ApiConfig(ID, ConfigType, ConfigKey, ConfigValue, Description, UpdatedAt)
VALUES(1, N'LLM', N'ApiUrl', N'', NULL, NULL)
INSERT INTO dbo.ApiConfig(ID, ConfigType, ConfigKey, ConfigValue, Description, UpdatedAt)
VALUES(2, N'LLM', N'ApiKey', N'', NULL, NULL)
INSERT INTO dbo.ApiConfig(ID, ConfigType, ConfigKey, ConfigValue, Description, UpdatedAt)
VALUES(3, N'LLM', N'ModelName', N'', NULL, NULL)
