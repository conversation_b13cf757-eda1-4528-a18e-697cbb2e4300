﻿/**  版本信息模板在安装目录下，可自行修改。
* M_Materials_Sup_Dict.cs
*
* 功 能： N/A
* 类 名： M_Materials_Sup_Dict
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016-12-03 09:37:11   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace ModelOld
{
	/// <summary>
	/// 物资供应商字典
	/// </summary>
	[Serializable]
	public partial class M_Materials_Sup_Dict
	{
		public M_Materials_Sup_Dict()
		{}
		#region Model
		private string _materialssup_code;
		private string _materialssup_name;
		private string _materialssup_py;
		private string _materialssup_wb;
		private string _contact_person;
		private string _contact_phone;
		private string _bank;
		private string _bankno;
		private string _materialssup_add;
		private string _materialssup_memo;
		private int? _serial_no;
		private bool _isuse;
		/// <summary>
		/// 编码
		/// </summary>
		public string MaterialsSup_Code
		{
			set{ _materialssup_code=value;}
			get{return _materialssup_code;}
		}
		/// <summary>
		/// 名称
		/// </summary>
		public string MaterialsSup_Name
		{
			set{ _materialssup_name=value;}
			get{return _materialssup_name;}
		}
		/// <summary>
		/// 拼音
		/// </summary>
		public string MaterialsSup_Py
		{
			set{ _materialssup_py=value;}
			get{return _materialssup_py;}
		}
		/// <summary>
		/// 五笔
		/// </summary>
		public string MaterialsSup_Wb
		{
			set{ _materialssup_wb=value;}
			get{return _materialssup_wb;}
		}
		/// <summary>
		/// 联系人
		/// </summary>
		public string Contact_Person
		{
			set{ _contact_person=value;}
			get{return _contact_person;}
		}
		/// <summary>
		/// 联系电话
		/// </summary>
		public string Contact_Phone
		{
			set{ _contact_phone=value;}
			get{return _contact_phone;}
		}
		/// <summary>
		/// 开户银行
		/// </summary>
		public string Bank
		{
			set{ _bank=value;}
			get{return _bank;}
		}
		/// <summary>
		/// 开户账号
		/// </summary>
		public string BankNo
		{
			set{ _bankno=value;}
			get{return _bankno;}
		}
		/// <summary>
		/// 地址
		/// </summary>
		public string MaterialsSup_Add
		{
			set{ _materialssup_add=value;}
			get{return _materialssup_add;}
		}
		/// <summary>
		/// 备注
		/// </summary>
		public string MaterialsSup_Memo
		{
			set{ _materialssup_memo=value;}
			get{return _materialssup_memo;}
		}
		/// <summary>
		/// 排列顺序
		/// </summary>
		public int? Serial_No
		{
			set{ _serial_no=value;}
			get{return _serial_no;}
		}
		/// <summary>
		/// 真为启用，假为停用，默认值为真
		/// </summary>
		public bool IsUse
		{
			set{ _isuse=value;}
			get{return _isuse;}
		}
		#endregion Model

	}
}

