<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="Button6.BackgroundImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        Qk1GDQAAAAAAADYAAAAoAAAAMgAAABYAAAABABgAAAAAAAAAAADEDgAAxA4AAAAAAAAAAAAAyNDUyNDU
        yNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDU
        yNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDU
        yNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUAADI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI
        0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI
        0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NQAAMjQ
        1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ
        1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ
        1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1AAAyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDU
        yNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDU
        yNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDU
        AADI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI
        0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI
        0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NQAAMjQ1MjQ1MjQ1MjQ1MjQ1AAAAAAAAAAAAAAA
        AAAAAMjQ1MjQ1MjQ1MjQ1MjQ1AAAAAAAAAAAAAAAAAAAAMjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ
        1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ
        1MjQ1AAAyNDUyNDUyNDUyNDUyNDUAAAA////AAAAAAAAAAAAyNDUyNDUyNDUyNDUyNDUAAAA////AAAA
        AAAAAAAAyNDUyNDUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAyNDUAAAAAAAAAAAAyNDU
        AAAAyNDUyNDUyNDUyNDUAAAAAAAAyNDUyNDUyNDUyNDUyNDUAADI0NTI0NTI0NTI0NTI0NQAAAD///8A
        AAAAAAAAAADI0NTI0NTI0NTI0NTI0NQAAAD///8AAAAAAAAAAADI0NTI0NTI0NTI0NTI0NTI0NTI0NTI
        0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NQAAADI0NTI0NQAAADI0NTI0NQAAADI0NQAAADI0NTI0NTI
        0NTI0NTI0NQAAMjQ1MjQ1MjQ1MjQ1MjQ1AAAAAAAAAAAAAAAAAAAAAAAAAAAAMjQ1AAAAAAAAAAAAAAA
        AAAAAAAAAAAAAMjQ1MjQ1MjQ1MjQ1AAAAAAAAAAAAAAAAAAAAAAAAAAAAMjQ1MjQ1MjQ1MjQ1MjQ1AAA
        AMjQ1MjQ1MjQ1AAAAAAAAMjQ1MjQ1AAAAMjQ1MjQ1MjQ1MjQ1MjQ1AAAyNDUyNDUyNDUyNDUyNDUAAAA
        AAAA////AAAAAAAAAAAAAAAAAAAAAAAA////AAAAAAAAAAAAAAAAAAAAyNDUyNDUyNDUyNDUAAAAyNDU
        yNDUyNDUyNDUyNDUAAAAyNDUyNDUyNDUyNDUyNDUAAAAyNDUyNDUyNDUyNDUAAAAyNDUyNDUyNDUyNDU
        yNDUyNDUyNDUyNDUAADI0NTI0NTI0NTI0NTI0NQAAAAAAAD///8AAAAAAAAAAADAwMAAAAAAAAD///8A
        AAAAAAAAAAAAAAAAAADI0NTI0NTI0NTI0NQAAAAAAAAAAAAAAAAAAAAAAAAAAADI0NTI0NTI0NQAAAAA
        AAAAAADI0NTI0NTI0NQAAADI0NQAAADI0NTI0NTI0NTI0NTI0NTI0NTI0NQAAMjQ1MjQ1MjQ1MjQ1MjQ
        1AAAAAAAAP///wAAAAAAAAAAAMDAwAAAAAAAAP///wAAAAAAAAAAAAAAAAAAAMjQ1MjQ1AAAAAAAAAAA
        AMjQ1MjQ1MjQ1MjQ1MjQ1AAAAMjQ1AAAAMjQ1MjQ1MjQ1AAAAAAAAAAAAMjQ1AAAAMjQ1MjQ1AAAAMjQ
        1MjQ1MjQ1MjQ1MjQ1MjQ1AAAyNDUyNDUyNDUyNDUyNDUyNDUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAyNDUyNDUyNDUyNDUyNDUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAyNDUyNDU
        yNDUyNDUAAAAyNDUyNDUyNDUAAAAyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUAADI0NTI0NTI0NTI
        0NTI0NTI0NTI0NQAAAD///8AAAAAAAAAAADI0NQAAAD///8AAAAAAAAAAADI0NTI0NTI0NTI0NTI0NTI
        0NTI0NQAAADI0NQAAADI0NTI0NQAAADI0NTI0NTI0NTI0NTI0NQAAADI0NTI0NQAAAAAAAAAAAAAAAAA
        AAAAAADI0NTI0NTI0NTI0NTI0NQAAMjQ1MjQ1MjQ1MjQ1MjQ1MjQ1AAAAAAAAAAAAAAAAAAAAAAAAMjQ
        1AAAAAAAAAAAAAAAAAAAAMjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1AAAAAAAAMjQ1AAAAMjQ1MjQ1MjQ
        1MjQ1AAAAAAAAAAAAAAAAAAAAMjQ1AAAAMjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1AAAyNDUyNDU
        yNDUyNDUyNDUyNDUyNDUyNDUAAAAAAAAAAAAyNDUyNDUyNDUAAAAAAAAAAAAyNDUyNDUyNDUyNDUyNDU
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAyNDUyNDUyNDUAAAAyNDUyNDUyNDUAAAAyNDU
        yNDUAAAAyNDUyNDUyNDUyNDUyNDUyNDUAADI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NQAAAD///8AAADI
        0NTI0NTI0NQAAAD///8AAADI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NQAAADI0NTI0NTI0NTI
        0NTI0NTI0NTI0NTI0NQAAADI0NTI0NTI0NQAAADI0NQAAADI0NTI0NTI0NTI0NTI0NTI0NTI0NQAAMjQ
        1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1AAAAAAAAAAAAMjQ1MjQ1MjQ1AAAAAAAAAAAAMjQ1MjQ1MjQ1MjQ
        1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ
        1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1AAAyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDU
        yNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDU
        yNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDU
        AADI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI
        0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI
        0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NQAAMjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ
        1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ
        1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ
        1MjQ1AAAyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDU
        yNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDU
        yNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUAAA=
</value>
  </data>
  <data name="Button5.BackgroundImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        Qk1GDQAAAAAAADYAAAAoAAAAMgAAABYAAAABABgAAAAAAAAAAADEDgAAxA4AAAAAAAAAAAAAyNDUyNDU
        yNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDU
        yNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDU
        yNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUAADI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI
        0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI
        0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NQAAMjQ
        1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ
        1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ
        1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1AAAyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDU
        yNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDU
        yNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDU
        AADI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI
        0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI
        0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NQAAMjQ1MjQ1MjQ1MjQ1MjQ1AAAAAAAAAAAAAAA
        AAAAAMjQ1MjQ1MjQ1MjQ1MjQ1AAAAAAAAAAAAAAAAAAAAMjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ
        1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ
        1MjQ1AAAyNDUyNDUyNDUyNDUyNDUAAAA////AAAAAAAAAAAAyNDUyNDUyNDUyNDUyNDUAAAA////AAAA
        AAAAAAAAyNDUyNDUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAyNDUAAAAAAAAAAAAyNDU
        AAAAyNDUyNDUyNDUyNDUAAAAAAAAyNDUyNDUyNDUyNDUyNDUAADI0NTI0NTI0NTI0NTI0NQAAAD///8A
        AAAAAAAAAADI0NTI0NTI0NTI0NTI0NQAAAD///8AAAAAAAAAAADI0NTI0NTI0NTI0NTI0NTI0NTI0NTI
        0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NQAAADI0NTI0NQAAADI0NTI0NQAAADI0NQAAADI0NTI0NTI
        0NTI0NTI0NQAAMjQ1MjQ1MjQ1MjQ1MjQ1AAAAAAAAAAAAAAAAAAAAAAAAAAAAMjQ1AAAAAAAAAAAAAAA
        AAAAAAAAAAAAAMjQ1MjQ1MjQ1MjQ1AAAAAAAAAAAAAAAAAAAAAAAAAAAAMjQ1MjQ1MjQ1MjQ1MjQ1AAA
        AMjQ1MjQ1MjQ1AAAAAAAAMjQ1MjQ1AAAAMjQ1MjQ1MjQ1MjQ1MjQ1AAAyNDUyNDUyNDUyNDUyNDUAAAA
        AAAA////AAAAAAAAAAAAAAAAAAAAAAAA////AAAAAAAAAAAAAAAAAAAAyNDUyNDUyNDUyNDUAAAAyNDU
        yNDUyNDUyNDUyNDUAAAAyNDUyNDUyNDUyNDUyNDUAAAAyNDUyNDUyNDUyNDUAAAAyNDUyNDUyNDUyNDU
        yNDUyNDUyNDUyNDUAADI0NTI0NTI0NTI0NTI0NQAAAAAAAD///8AAAAAAAAAAADAwMAAAAAAAAD///8A
        AAAAAAAAAAAAAAAAAADI0NTI0NTI0NTI0NQAAAAAAAAAAAAAAAAAAAAAAAAAAADI0NTI0NTI0NQAAAAA
        AAAAAADI0NTI0NTI0NQAAADI0NQAAADI0NTI0NTI0NTI0NTI0NTI0NTI0NQAAMjQ1MjQ1MjQ1MjQ1MjQ
        1AAAAAAAAP///wAAAAAAAAAAAMDAwAAAAAAAAP///wAAAAAAAAAAAAAAAAAAAMjQ1MjQ1AAAAAAAAAAA
        AMjQ1MjQ1MjQ1MjQ1MjQ1AAAAMjQ1AAAAMjQ1MjQ1MjQ1AAAAAAAAAAAAMjQ1AAAAMjQ1MjQ1AAAAMjQ
        1MjQ1MjQ1MjQ1MjQ1MjQ1AAAyNDUyNDUyNDUyNDUyNDUyNDUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAyNDUyNDUyNDUyNDUyNDUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAyNDUyNDU
        yNDUyNDUAAAAyNDUyNDUyNDUAAAAyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUAADI0NTI0NTI0NTI
        0NTI0NTI0NTI0NQAAAD///8AAAAAAAAAAADI0NQAAAD///8AAAAAAAAAAADI0NTI0NTI0NTI0NTI0NTI
        0NTI0NQAAADI0NQAAADI0NTI0NQAAADI0NTI0NTI0NTI0NTI0NQAAADI0NTI0NQAAAAAAAAAAAAAAAAA
        AAAAAADI0NTI0NTI0NTI0NTI0NQAAMjQ1MjQ1MjQ1MjQ1MjQ1MjQ1AAAAAAAAAAAAAAAAAAAAAAAAMjQ
        1AAAAAAAAAAAAAAAAAAAAMjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1AAAAAAAAMjQ1AAAAMjQ1MjQ1MjQ
        1MjQ1AAAAAAAAAAAAAAAAAAAAMjQ1AAAAMjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1AAAyNDUyNDU
        yNDUyNDUyNDUyNDUyNDUyNDUAAAAAAAAAAAAyNDUyNDUyNDUAAAAAAAAAAAAyNDUyNDUyNDUyNDUyNDU
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAyNDUyNDUyNDUAAAAyNDUyNDUyNDUAAAAyNDU
        yNDUAAAAyNDUyNDUyNDUyNDUyNDUyNDUAADI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NQAAAD///8AAADI
        0NTI0NTI0NQAAAD///8AAADI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NQAAADI0NTI0NTI0NTI
        0NTI0NTI0NTI0NTI0NQAAADI0NTI0NTI0NQAAADI0NQAAADI0NTI0NTI0NTI0NTI0NTI0NTI0NQAAMjQ
        1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1AAAAAAAAAAAAMjQ1MjQ1MjQ1AAAAAAAAAAAAMjQ1MjQ1MjQ1MjQ
        1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ
        1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1AAAyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDU
        yNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDU
        yNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDU
        AADI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI
        0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI
        0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NTI0NQAAMjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ
        1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ
        1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ1MjQ
        1MjQ1AAAyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDU
        yNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDU
        yNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUyNDUAAA=
</value>
  </data>
  <data name="Combo2.Images" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAAkAAAAJCAYAAADgkQYQAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAgY0hSTQAAeiYAAICEAAD6AAAAgOgAAHUwAADqYAAAOpgAABdwnLpRPAAAADdJREFUKFNjYMAE
        /7GIgYWQJXCxURTCFOE1ESSJoQAmiI9GcSayQqzuR3cLVithOvH6DpsisBgAoKoc5C8W7oEAAAAASUVO
        RK5CYII=
</value>
  </data>
  <data name="Combo2.PropBag" xml:space="preserve">
    <value>&lt;?xml version="1.0"?&gt;&lt;Blob&gt;&lt;Styles type="C1.Win.C1List.Design.ContextWrapper"&gt;&lt;Data&gt;Inactive{ForeColor:InactiveCaptionText;BackColor:InactiveCaption;}OddRow{}Style5{}Style1{}RecordSelector{AlignImage:Center;}Style6{}Style8{}Footer{}Style3{}Style4{}HighlightRow{ForeColor:HighlightText;BackColor:Highlight;}Style10{}EvenRow{BackColor:Aqua;}Style11{}Group{AlignVert:Center;Border:None,,0, 0, 0, 0;BackColor:ControlDark;}Caption{AlignHorz:Center;}Selected{ForeColor:HighlightText;BackColor:Highlight;}Style9{AlignHorz:Near;}Style2{}Normal{}Style7{}Heading{Wrap:True;BackColor:Control;Border:Raised,,1, 1, 1, 1;ForeColor:ControlText;AlignVert:Center;}&lt;/Data&gt;&lt;/Styles&gt;&lt;Splits&gt;&lt;C1.Win.C1List.ListBoxView AllowColSelect="False" Name="Split[0,0]" CaptionHeight="18" ColumnCaptionHeight="18" ColumnFooterHeight="18" DefRecSelWidth="17" VerticalScrollGroup="1" HorizontalScrollGroup="1"&gt;&lt;ClientRect&gt;0, 0, 116, 156&lt;/ClientRect&gt;&lt;Height&gt;156&lt;/Height&gt;&lt;BorderSide&gt;Right&lt;/BorderSide&gt;&lt;VScrollBar&gt;&lt;Width&gt;16&lt;/Width&gt;&lt;/VScrollBar&gt;&lt;HScrollBar&gt;&lt;Height&gt;16&lt;/Height&gt;&lt;/HScrollBar&gt;&lt;CaptionStyle parent="Style2" me="Style9" /&gt;&lt;EvenRowStyle parent="EvenRow" me="Style7" /&gt;&lt;FooterStyle parent="Footer" me="Style3" /&gt;&lt;GroupStyle parent="Group" me="Style11" /&gt;&lt;HeadingStyle parent="Heading" me="Style2" /&gt;&lt;HighLightRowStyle parent="HighlightRow" me="Style6" /&gt;&lt;InactiveStyle parent="Inactive" me="Style4" /&gt;&lt;OddRowStyle parent="OddRow" me="Style8" /&gt;&lt;RecordSelectorStyle parent="RecordSelector" me="Style10" /&gt;&lt;SelectedStyle parent="Selected" me="Style5" /&gt;&lt;Style parent="Normal" me="Style1" /&gt;&lt;/C1.Win.C1List.ListBoxView&gt;&lt;/Splits&gt;&lt;NamedStyles&gt;&lt;Style parent="" me="Normal" /&gt;&lt;Style parent="Normal" me="Heading" /&gt;&lt;Style parent="Heading" me="Footer" /&gt;&lt;Style parent="Heading" me="Caption" /&gt;&lt;Style parent="Heading" me="Inactive" /&gt;&lt;Style parent="Normal" me="Selected" /&gt;&lt;Style parent="Normal" me="HighlightRow" /&gt;&lt;Style parent="Normal" me="EvenRow" /&gt;&lt;Style parent="Normal" me="OddRow" /&gt;&lt;Style parent="Heading" me="RecordSelector" /&gt;&lt;Style parent="Caption" me="Group" /&gt;&lt;/NamedStyles&gt;&lt;vertSplits&gt;1&lt;/vertSplits&gt;&lt;horzSplits&gt;1&lt;/horzSplits&gt;&lt;Layout&gt;Modified&lt;/Layout&gt;&lt;DefaultRecSelWidth&gt;17&lt;/DefaultRecSelWidth&gt;&lt;/Blob&gt;</value>
  </data>
  <data name="Combo1.Images" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAAkAAAAJCAYAAADgkQYQAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAgY0hSTQAAeiYAAICEAAD6AAAAgOgAAHUwAADqYAAAOpgAABdwnLpRPAAAADdJREFUKFNjYMAE
        /7GIgYWQJXCxURTCFOE1ESSJoQAmiI9GcSayQqzuR3cLVithOvH6DpsisBgAoKoc5C8W7oEAAAAASUVO
        RK5CYII=
</value>
  </data>
  <data name="Combo1.PropBag" xml:space="preserve">
    <value>&lt;?xml version="1.0"?&gt;&lt;Blob&gt;&lt;Styles type="C1.Win.C1List.Design.ContextWrapper"&gt;&lt;Data&gt;Inactive{ForeColor:InactiveCaptionText;BackColor:InactiveCaption;}OddRow{}Style5{}Style1{}RecordSelector{AlignImage:Center;}Style6{}Style8{}Footer{}Style3{}Style4{}HighlightRow{ForeColor:HighlightText;BackColor:Highlight;}Style10{}EvenRow{BackColor:Aqua;}Style11{}Group{AlignVert:Center;Border:None,,0, 0, 0, 0;BackColor:ControlDark;}Caption{AlignHorz:Center;}Selected{ForeColor:HighlightText;BackColor:Highlight;}Style9{AlignHorz:Near;}Style2{}Normal{}Style7{}Heading{Wrap:True;BackColor:Control;Border:Raised,,1, 1, 1, 1;ForeColor:ControlText;AlignVert:Center;}&lt;/Data&gt;&lt;/Styles&gt;&lt;Splits&gt;&lt;C1.Win.C1List.ListBoxView AllowColSelect="False" Name="Split[0,0]" CaptionHeight="18" ColumnCaptionHeight="18" ColumnFooterHeight="18" DefRecSelWidth="17" VerticalScrollGroup="1" HorizontalScrollGroup="1"&gt;&lt;ClientRect&gt;0, 0, 116, 156&lt;/ClientRect&gt;&lt;Height&gt;156&lt;/Height&gt;&lt;BorderSide&gt;Right&lt;/BorderSide&gt;&lt;VScrollBar&gt;&lt;Width&gt;16&lt;/Width&gt;&lt;/VScrollBar&gt;&lt;HScrollBar&gt;&lt;Height&gt;16&lt;/Height&gt;&lt;/HScrollBar&gt;&lt;CaptionStyle parent="Style2" me="Style9" /&gt;&lt;EvenRowStyle parent="EvenRow" me="Style7" /&gt;&lt;FooterStyle parent="Footer" me="Style3" /&gt;&lt;GroupStyle parent="Group" me="Style11" /&gt;&lt;HeadingStyle parent="Heading" me="Style2" /&gt;&lt;HighLightRowStyle parent="HighlightRow" me="Style6" /&gt;&lt;InactiveStyle parent="Inactive" me="Style4" /&gt;&lt;OddRowStyle parent="OddRow" me="Style8" /&gt;&lt;RecordSelectorStyle parent="RecordSelector" me="Style10" /&gt;&lt;SelectedStyle parent="Selected" me="Style5" /&gt;&lt;Style parent="Normal" me="Style1" /&gt;&lt;/C1.Win.C1List.ListBoxView&gt;&lt;/Splits&gt;&lt;NamedStyles&gt;&lt;Style parent="" me="Normal" /&gt;&lt;Style parent="Normal" me="Heading" /&gt;&lt;Style parent="Heading" me="Footer" /&gt;&lt;Style parent="Heading" me="Caption" /&gt;&lt;Style parent="Heading" me="Inactive" /&gt;&lt;Style parent="Normal" me="Selected" /&gt;&lt;Style parent="Normal" me="HighlightRow" /&gt;&lt;Style parent="Normal" me="EvenRow" /&gt;&lt;Style parent="Normal" me="OddRow" /&gt;&lt;Style parent="Heading" me="RecordSelector" /&gt;&lt;Style parent="Caption" me="Group" /&gt;&lt;/NamedStyles&gt;&lt;vertSplits&gt;1&lt;/vertSplits&gt;&lt;horzSplits&gt;1&lt;/horzSplits&gt;&lt;Layout&gt;Modified&lt;/Layout&gt;&lt;DefaultRecSelWidth&gt;17&lt;/DefaultRecSelWidth&gt;&lt;/Blob&gt;</value>
  </data>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAOTkAAAEAIAC0NAAAFgAAACgAAAA5AAAAcgAAAAEAIAAAAAAAjDQAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAaFC6tFZUwv9UYrr/0/P///b////6/f3/+/70//f9
        7v/2//r/9f3//7u74f+QfLb/mZDBXwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAABYS4q8AAAAAP/////JzvD/RDOV/+zY9v///P////36//n/+f/s/f//Umae/296
        wf/o4/P/q6vLPlpZnaAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD49
        ks7Y1PsN7/b3/+Pl+f/9//////H6/1NNmP+ouNX/9////8K85/9NN57/ysTr///////k7/n/9P///62s
        4T5iVqqiAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUUuRvM3D9CH8////iozW/x8Y
        r/+IkNP//////+v///9ic7D/UEiu/2xorv/3/v//+vj//2xewP8mMJ//z+H9//v///+zqdpAbGCbmQAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABOUYm3wsPgJP////+SktH/GB+u/xQn1P8QFdb/SE/B//Tz
        /////P//6+f9//z///+01+H/Izil/xUZ1f8YJMr/LCPE/9fa+v//////uKvUPmNgq5kAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAENHpMa1s9w2/////36K0v8XFL//HCrY/xIszf8VIeD/Fxnb/ywrtv+ywvL/5////4KI
        1P8dErz/IRze/xcpzf8PMMn/FB7n/yoYx//r2/j//////5Oc11tWWa2iAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAW12tp5Kb
        0WL/////horW/woYv/8cHev/GyDi/xsj3P8fHeb/IyPa/xYe1/8TIsT/GSqx/xkjwf8fIuj/EyDs/xId
        6/8jGO7/HijP/xoe2v8kLbX/y+bu//7///+soM9Mc2askQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABhYLeigoHHdv////+Nj9H/ABbD/xIk
        4f8XI9r/HyfU/yEi3P8gIOD/GivO/xor0f8aItX/IiXa/yIl0P8gKMv/HCnN/x4o3P8iGuH/JirJ/xgr
        0P8DHd//Fii+/9/Z9v//////rJ7MT3Ndt5sAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAGFvvqBOZb63/////6SR0f8TEsb/DCXX/x4l2P8gK8n/JSjS/yIi
        4/8ZIeP/DiTa/xcuzv8aJ9r/HR3m/yEi3v8iKdH/IibW/xki2/8WK9X/LifW/ywqz/8WI+T/Exjv/ykf
        u//b5vn//////6KSzV13aruNAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAfnLIi0xXm7n/////i4Pc/wUWwf8RJtT/Lx7h/yUg3v8lI9j/JB3m/xgY1f8QGbn/FRrG/wgd
        t/8HH7r/ChvD/wscxf8MHcL/CxjG/wkWyf8PIM3/JiLe/yod4v8nJN7/HCbR/xgj2f8eJLr/ydf4////
        //+gmsNTamuwjQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACIh9JvMDKK3v//
        //+Wlcz/DRjG/w4d6v8fJdn/IiLT/x4k2P8cI9v/FRvZ/1tt3v+ov+T/sq/p/66j8f+trO7/rLPu/6qu
        7/+mpvL/pqXy/6ir8f9nbtf/FB/W/w8i3v8gKNP/Jx/a/xsm1v8OJtf/KCu1/+ze+P//////iZK+Zpaf
        xFgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAiPKPw2Oz5/6yk7P8XFK3/GCXj/xQd
        5f8XJtr/FCzS/xol2P8WJ9b/FB3J/4+e3//+//3////////////////////6/////P//////////////
        //+Wnd3/Dhfa/xQl2f8iJ9T/HyHn/xsf3f8YKdb/ICDU/zMjrP/X5vT/3/n7/2Frn5YAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAA1trsCri27TRKSrG86vr//2hxtv8mHtL/Gh3h/xYo1v8XJNz/HCTZ/xwj
        3v8XJ9j/GRrL/5KV3v///////Pr8//////////////////////////////////////+SmOD/DxXf/x8i
        2f8mJtP/ECnV/xoj3v8eKtn/GR/c/x4Uwv+cpNj/29z4/zMUofFcT7SsjJXbZAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AABla6ibYWeyoJONyv+Na8j/6u35/97n//8oJ7T/HibN/x8p0f8kHeT/JiDd/xsj3f8XJ9j/GRrL/5KV
        3v///////Pr8//////////////////////////////////////+UleP/Dhfb/xwn0f8hKdD/DynW/xgl
        2/8TJdT/Fxvi/1tUzf/0+Pv/4drp//Hc//+tpeH/BiWU/29+0IUAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFJemLKruthA//////r7
        ///1///////6///7///Q4/f/Hyim/x8g2v8fJdb/IiPf/xsj3f8XJ9j/GRrL/5GV3v///////Pr8////
        //////////////////////////////////+Vl9//DxnV/xsp0v8dIdb/ECPj/xof5P8cG8X/Ul29//3/
        ///7/+7////5/7Cq6P/f+fr/5+///zM0p813fcR8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUFSZtMex5jn/////q67e/yAZrv8wQaj/18fs////
        +P/2/+7/0dv//yEhtv8aKND/FivI/xwi3/8XJ9j/GRrL/5CU3v///////fv8////////////////////
        //////////////////+Wmdz/DRrS/xom1v8oJOv/ERnf/xgZyv9uZML///////D+6v/+/v//dGfc/xkF
        1P8aKqf/6OH4//////9WXKOiXoPBmQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAABHWY/BuMLnMf////+PisT/CB+t/w8m3P8iFuH/JR2x/8bQ7f/9/////////9LW
        +v8iKrH/GSy+/xsi3/8XJ9j/GRrL/5CU3v///////fv9////////////////////////////////////
        //+UmN7/DBrT/xwl2v8mHuD/FR/A/1Ziv///////+fn9//P//v9td83/FxPL/x4k1/8WIdX/Jiis/+nc
        9f//////Ymu8lnJ/tIMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEZL
        kMHPx+wc/////4aSzP8PGsT/DCLd/xEvy/8bJNv/DCbY/xccuP/Hwen////2////+v/Ozvz/KSK//xkd
        3/8VJdr/GBzK/5GV3f///////fz6/////v////////////////////////////////+SlOD/DBXc/xok
        2/8dGMj/UWHJ/+P7+//9//r//////2Vk0/8YG8b/Fiy7/x4e4/8iItz/EB7o/wYetP/f3vn//////5aG
        xmtueLuIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAO0Kb0dTM9hf/////dYfL/xIf
        t/8XIOL/GibY/xso0/8VIt//FCrf/xYZ4P8dGLT/x8/r//3/+v///f7/4tH9/ysnu/8cJ9n/EB++/5CS
        2v//+/////z0////+v///////////////////////////////f+cn9H/EAjx/wMgwf9fY8v///f/////
        8f/y////bHnE/xwav/8hId//HSbW/yIl1/8jId3/FSvY/xQk4P8kHLr/4tb4//////97is53YG3CmQAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABCPZTO2c35Ff////+Dj9P/FhbG/xof5f8ZHO3/IyDg/ykk
        1P8nJ9D/EyDl/xMj3f8ZG93/Hhu3/7nF7v/7////+v///8/i9f8cJaT/IRrN/5OO3///////+/P8//77
        //////////////////////////////////+PlNz/GQna/1Nlvv/7//////71///4//9pctX/Cx65/xci
        6f8MIOr/DCbc/w8n2P8WIeT/HRXr/xwk2P8ZH+D/FySs/9P37///////j4u+ZH97wXcAAAAAAAAAAAAA
        AAAAAAAA6dP/D0Q3mdYAAAAA/////4OC2v8JHbP/HCbR/xMwzf8TJ9P/IB/i/yUe4/8hI9n/JiPY/yUi
        4v8gH+X/JB/a/x8Vp//LyOz////6//f//f/U2P//LBev/5aQ2//9/+3/8/zs//78////////////////
        //////////////////+Jjs3/YFDJ//////////H//////2xm0/8UE8r/HyPh/xUa4v8WIt3/GCrQ/x4o
        0P8hI9r/HiHd/yIl0v8XJtv/CCbh/wwoqP/Y2PT//////4iDz25pecCNAAAAAAAAAAAAAAAAOTKm29vk
        /v////n/iIrR/xERzf8gGe//HiLe/xEszP8PLc3/FSXb/xkn4f8OK9T/IyfV/yAl2f8WLNH/FirT/yQh
        3f8oJan/xcfr/////v/4/+3/08z1/52Y0v/3//f/9vX6///9////////////////////////////////
        ///P19r//fz/////8P//////cHTP/wYgwP8VJdr/HibY/x8q2f8fLdH/IC7N/yUq1P8kJtv/DCjW/yUo
        0/8oKsv/FCHk/w8T8/8oIrP/4+D4//////9+h8d1fHm7egAAAABgV7OtzOnzF/////+Le9X/BxfI/xUi
        4v8qHtz/ICHf/xYl2/8XJtP/Fh7K/xIbyf8GF87/ChLa/wgivv8CI7//ABPX/wAU0v8KGs7/DAqw/76y
        4P///+///f30//j6///z/Pr///3y/////f////////////////////////////78////+v///vr6//fw
        //9nWcf/Dga6/wsZy/8KHs7/CBnJ/wgcxf8JG8b/CRjN/wsV0/8KE9b/Chva/x8k0v8oItX/JyDk/xoe
        3v8VItH/HSSo/9Ld7///////kH2+dZmX01Y9Rr///////7Kd1/8LD8X/By7c/xwm0/8oIdP/HCXZ/xsh
        3f8tMMX/iIrd/5CP1v+OjN//i4vn/4+M4P+QieP/korr/4uK5v+Eiub/gIfk/5KTwf/99P///vj4//X7
        /v/y9/f///zy///9+f////////////////////////////79///48/H/+/r5/8rQ7v99iMv/i43f/5aM
        3f+PieD/i4nl/4yL4v+MieX/iobo/4iH6P+Kj+f/aWrY/xcc1/8NH93/HyzO/yQl2f8VHuH/CyTU/yco
        o//75vv//////zterf9kg8f/z7Hu/8/b4/8LH7r/Fx7d/x4ox/8cKOD/HCHc/xcg3v8xN8D/////////
        ///////////5///////////////8//////////3////+//b////9+/////z+//r7/v/8/P////f/////
        //////////////////////////////7+/P/9/vP/+Pzw//n//v/5////////////////////////////
        ////////////////////////vsDq/xAT0P8SJ+T/ESLK/xgj4f8ZIeX/DB7P/1FMyv//9v3/1uL3/1Jt
        s//b8P//TUKd//f///+Jkd7/GxTQ/x0o0/8XKdX/HyPX/xMk2v8oMsT///3///z//v/6/vn////+////
        ////////////////////////////////////////////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        ////////urvr/xAVzv8ZJ9n/ISXc/w8j2f8cI+H/HCK3/9zm////////QkqZ/87b9P////7/pK/h/4Bq
        xv//////Q1i8/xsiyv8kItr/HiPX/xQj2v8oMsT///z///z//v/6/vr////+////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////uLXv/w4V
        0P8ZLNH/IyfN/xopz/8UD77/pa/x//v///9+Y7j/sbDs//P/9//8+/T//P///3Jgtv/Ozeb/3/X8/y0x
        t/8bHtb/HiPY/xQj2v8oMsT///z///z//v/6/vr////+////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        ////////////////////////////////////////////////////////ubnu/w0Tz/8YJ9n/KCXX/xko
        tv9vd8z/9////5+pwP+cic///P////b96P/5+/z////y/9ff9v9uc6r//////4yN6v8KH9T/HyLY/xQj
        2v8oMsT///z///z//v/6/vr////+////////////////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        ////////////////////////////////////////vL/r/wsTyv8XIuL/IRjd/yEvs//R6v3//////2BS
        sP/l8f//9f3u///9/f/69vr//f///5Wix/+RksT//////0xPzv8MIdr/HyPX/xQj2v8oMsP///z///z/
        /v/6/vr////+////////////////////////////////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        ////////////////////////urjr/wsXx/8UJtj/JB/k/xghvP+msuj//////5GM0/+KpMf////////0
        /////Pv/tsry/1BQpf//////i4/S/xUbw/8bIeX/HyTV/xQj2v8pMsT///z///79/v/7/Pz//v/8//7/
        /f/+/////v////7//v/+/////v///////////f///v3///7///////7////+////////////////////
        //////////////////////3////+///+/////f////3//////v/+//3//f////3////9/////f////7/
        /v//////urDu/w4ZxP8SL8j/HSPY/yIk2P8pIrH/7d3///X///87UpH/8OD7///6///z5v//PT+R//H/
        //+/yO//Eh+x/xIk2f8kH+j/HCnL/xAj2v8yMMP////////x/f/69/f/9f/u//f89//5+f7/+vv9//v9
        +//6/P7/+fv3//7+9f/28/z/8fP7//n/+//+//v///38/////v////////////////////////////79
        ///9+/L//f3r///y////+vv///b+///89f/7//L/9/3+//j9/f/4/f3/+P3+//j9/v//////urnn/xwX
        yf8RI9z/HCXe/yYl0/8cH9P/Pz7J//f////h7fT/XEWh//z3//9ye7n/z7zs/+b8+f8lN67/IxP5/x8u
        0v8ZKdP/GyPX/w4j2/8sN7///////////f/0//X///////////////////////////////////////n/
        +P/5/+//9/3o////+//++////vv////+//////////////////////////////79///y9vX/9P/0//b+
        /f/7/+7////9///6//////7////8/////P////z////8////+///////ssLk/xsav/8dJeP/HR7i/yMi
        3f8aJdj/DhfP/3eB2f//////lY/I/4ya3v9MYbX//////4+Hwf8OILj/GiPl/xke2f8fJ9z/GSXV/xAi
        4v8eJtD/gY3V/4SG2f95itv/hofi/4iJ4P+FjNz/hY/d/4WO3/+Hj97/hYnf/4WDxP/Q2Nv//fr1///4
        ////+v//+//7//3//f/////////////////////////////+///8/fz/7fX//5+n1v+Dj87/io7a/5CO
        1P+Mjtv/hovi/4eL4v+Hi+L/hYnh/4WK4f+HjeL/XGfY/x8Y2/8iIOP/JSTZ/yAl2P8UKtD/DyzU/w0O
        qf/s3fv//////zFRpv9zdMKqusHgPOfn//88PrP/Ah3E/yEr4/8nHt7/GirP/xUi4v8aHOT/GR3E/xoT
        1f8VEdz/DRjS/wsex/8GIMT/BRjN/wsa1v8MGMz/DBC1/2Zevv/z/P//+fX+///z///9/fn/9//u//z/
        +//////////////////////////////9/v//+/z//v7+/8nQ7/8gG7D/Eg3K/xcexv8NG8n/CBfS/wgX
        0f8JF9H/ChnS/woZ0v8KGNL/FhnZ/yYd7P8nItX/IyzF/x8k2/8bIOH/Bha+/36K1f////3///n//z5L
        tP8AAAAAP02P/MbH8in3/f//MEyp/xIYzv8lI9T/HSfV/x4j2v8cJNr/GynS/xkn1f8aJdr/ISLd/x8m
        1f8eJtf/ICDk/yEZ3P8oIcH/b2/J//v////u/+b/8fb//87K6P/8//f/9v3y///+////////////////
        ///////////////////78P7////y//f/7f/X3f//LTS+/xMf1P8bJN3/HyjR/yAo0/8fJ9P/HyjS/x8o
        0v8fKNT/FybY/yEn0v8jKc3/ITTG/xoY3/8iDdv/h4Dg//3//v////r/ZEmeyMG/6CkAAAAAAAAAADpC
        of/Gyewp9/b//zI6tP8NJMn/ISPd/yQi2f8hJdX/FybY/xYp1P8eJNn/ICHe/x4m1f8cJ9X/ISHc/xsa
        vP9pcLv/9P7/////+//6////aGOz/4yKzv/2//r/+ff2//77////////////////////////////////
        //+Jl8D/vrTf////////////1+L2/y4xsf8bG9n/HibX/x8l1/8fJtb/HibV/x4m1f8eJtb/FifW/yEl
        1/8fIOP/ECbK/xAaw/+Ff9f///////v0//9LU5fIuLvnNAAAAAAAAAAA+f/iBgAAAABRQp3s2b7xLObx
        //8kS6r/FCPF/y8l2f8iIeH/HSLf/x0f4v8NJdz/HCTb/xMm2P8QKNP/FBy//2dsw//7////6v35//L8
        //9eaL3/GBOw/46Q4f/4//7//Pb8//76//////////////////////////////////+Lm93/FAu1/7qy
        8P////r////8/+zk//8wK7//DyHH/xEr1v8TIOH/HiLe/x0k2f8VIuD/GybS/yMj1f8SHer/ABy7/4WN
        zv////z//Pn3/1ZKl8iywOs8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAV0yU3MjI6Cbr+v//MD2u/xcY
        zf8mINr/HyHe/xMp0/8ZJ9X/Hybe/w8d3/8JE8//dHvI///////9+P7/9////1hsu/8TI7X/FRfP/46W
        3//9//z//Pb8///8//////////////////////////////////+SkuD/DhXc/xcgrf+3t+H/+v////79
        /v/h4P3/MTu0/xAf0/8YJ9r/IyTZ/yAi2/8WKdH/KC3T/xAf4P8EGLz/ho3Q///////08///UlGSvcHE
        5CYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFlUjNPQvOsv8e7//zdIt/8ZGdL/FiLg/xMm
        2f8wItX/FyfZ/w0R2f9mZdj/////////9P/5+f//Zmm//xwgxv8dJeH/ExTW/42V3/////r//ff7///9
        //////////////////////////////////+fl+L/Chba/xcg2v8pHrX/qrnj//n///////X/6Of//yor
        wf8WGdf/GinS/xcszf8SJN3/Ex7d/wITyv+Wj9L//////+j3//85QpnhzMX0IQAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABVR5nmzcnqHPT///8jPbn/Fhrd/ysc4v8dKNn/EBu7/3F2
        y//9+/////z+//////9aaL3/ExbJ/xkd4v8SH+L/FRzU/4yX3f////z//fX9///8////////////////
        //////////////////+flOD/CBTl/xAr2/8fH9D/HRy9/6mv5f////X////5/9rj//8xPLP/FyjG/xcm
        1v8THeb/AxvJ/5GJz///////6vP//y1Mk/HJzfIbAAAAAAAAAAAAAAAAAAAAAAAAAAD//+MDAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAQU6P+b3G7Tbs+f//Pzm1/yAizv8BG77/eHXP/////////+///////11g
        zP8KFdj/DiTa/xkk2/8UKdX/FB/D/5Ca3P///////PT+///8////////////////////////////////
        //+ekuH/Bx/Q/wwuy/8jJ+D/HSDd/xoWuP+9sef///z///b/8P/c9Pn/JjCu/x8b1f8WGrf/gojR////
        ///j8P7/K0aS9M/S9hMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAEdNm+/NwOkp7/n//0RXsf91fMz////////z/v//////WGLC/wgM2f8VIuX/HCjT/x0k
        2P8WLM3/GCDC/5KV3P//////+vn7///+/v////////////////////////////////+Xkd//CSPG/xYs
        1P8jIN7/FyTW/xwd5P8ZFcn/pa/q////+P/2+/v/2eD//0tTo/+RkND//////+ju//8pQ4z3ztv3FgAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AABCTZH2ucHrPP///////////Pz///////9mZMT/DRbL/xMn5f8fJs3/LSnL/x8f4/8dJtX/HBvL/5GR
        3v//////+f70//3//f/+/////v////7//v/+//7////+//////+Wmd3/DBPj/xsk1/8qJdT/FiHc/xUk
        4f8MJtz/EBy5/7635P//+/n/8/b7//7/////////7+76/zg5lOHc1P8LAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAc2ess1xZ
        s81BS6f/++z//2dowv8CIb7/ECHj/y8a5P8mItr/FCfY/yEi3P8YKdL/FRfV/4mP4v/z/+v/8fvv//j7
        /P/7//v/+//7//v/+//8//z//P/7//////+PlOL/GRXa/xcty/8kItr/JyrL/xsl1/8PJeD/GBvl/xgV
        vP+0xer//////3BIzP9CY7DTTliaw9fd+ggAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAN3j/wNVW6zZ/////0JX
        ov8WIMn/KCDd/xYn1f8TKNX/IiPZ/xkrzf8WMcL/GRzJ/5iS4f////j///r3///6/////v7///3+///+
        /v////////////////+Mm9n/GhjU/xYl0v8eHeb/IybT/xgszv8XI9n/Gynd/xcMvP+3q9f//f/y/2dc
        06/U5v8OAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABeW6PJ6Pr//+/0//9FP7f/FiDQ/xIx
        w/8YK8//Hhjx/xki4P8VKdT/GRzN/42I5P/8////9fD+/+7x///s8/3/7PP9/+zz/f/t9P3/7fT9/+/6
        //+DkeD/GhnU/xkn2P8ZHen/GSHf/xYp0v8dGu3/DBO3/5md0v//////2O37/zhDnuEAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAA/P/iBgAAAACzqtxMV1ex1v/+///v+///J0Ky/xYY2/8kH+T/GiTV/yAp
        z/8XK87/GSLb/ygnzv8zOLz/LjS//ysxw/8sMcH/LDHB/yswwP8qL8D/Ki/A/ysyvP8mKcP/JB7i/xsq
        z/8UJNr/GSLn/xwg3P8KEMf/hIjW///////p8v//Oj+O3tra9QUAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAvLPdPEpOmOnq9f//7vf//zc9uf8ZG93/KybV/yQj1/8YJdr/FSHi/xge
        5/8UH+H/ByLh/w0j3v8QIt3/ECLd/xAj3f8QI93/ECPd/xIi3f8fH+X/Jh7o/yEq0f8ZLNT/FR7W/xka
        uP+Gl9r///////Pd//9BQJvW2Nf4CAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAALTD7EU+SI78/fb///r3//81QLH/FBzP/x8f4v8UJdz/FifW/yAi3P8fIOD/EynU/xgn
        2P8bJNr/GyTb/xkl2/8ZJtr/GSXa/xkl2/8WJdr/JR7j/yUh3v8XJNn/Cxe//3+O1v///////Pr//0A9
        n9nQ1usTAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AADQ2vIZUkCf7/Da///o9P//KVCj/wsky/8gJNj/JSDf/yAl2/8bKsz/IyjW/xok1/8OHd7/CBrK/x8h
        1v8rGuH/KyTa/xoj1P8bKNj/GSPZ/xsf6P8XHMz/f4fa//3/+P/77v//Rz+T1sbW9yEAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA3tn7A0k9
        lPbW2PkQ7////zdBrP8ZHs//FyLa/x0g4/8hJtf/HR7f/xMN0/9fZcn/rLTw/zE3tf8UJNT/DCHU/x4x
        zP8eJtH/DyTj/woXvf+SjNP///////Tz//9HSpXOv8ntKQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABKQZPv1MXyH///
        //8xSqT/ByHO/xgf4/8oDvX/IRyz/7Os2P//////2Nvv/+n4//9YZcf/BxHO/wsr1P8VJ9z/GhDG/5mO
        1v////v//fn//0NFp9O3wuw0AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAWkmm48DE6DL4/v//Rj3C/xAU
        w/8xOrj/5un4//////9ygrr/WmCl/1dJpf/n1P//u7Pv/xsZvf8AD7z/rY/e///////v/f//PV2H28LI
        5CYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAENPovatsuRP9f///56Xzv/1/P//9/n//0lS
        tP+OlNX//////+bp//9ITJz/qKvc/+zv//+kreD//P/7//////9LTqDItMHrOgAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABWY6DWlq7CdP/////h1fT/RkCh/8/A6//+/+3/7v/n//n/
        8f//////eXq//2d6tv/9/P//2v3//z9elNnAteY3AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAe3XRnTdMvv9SaK7/5ef6//v/8P/88/z//O79///3/f/+//z//v///8W3
        6P9VULf/U1G8/8HJ6SYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///wAH///gP//+gAP//+A///wAAf/
        /4D//+AAA///gP//wAAB//+A//+AAAD//4D//wAAAH//gP/+AAAAP/+A//wAAAAf/4D/+AAAAA//gP/w
        AAAAB/+A//AAAAAH/4D/wAAAAAH/gP+AAAAAAP+A/wAAAAAAf4D+AAAAAAA/gPwAAAAAAB+A+AAAAAAA
        D4DwAAAAAAAHgOAAAAAAAAOAkAAAAAAAAYCAAAAAAAAAgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAgAAAAAAAAADAAAAAAAAAgKAAAAAAAAGA8AAAAAAAA4D4AAAAAAAHgPwA
        AAAAAA+AfgAAAAAAH4D/AAAAAAA/gP+AAAAAAH+A/8AAAAAA/4D/4AAAAAP/gP/wAAAAB/+A/9AAAAAH
        /4D/+AAAAA//gP/8AAAAH/+A//4AAAA//4D//wAAAH//gP//wAAA//+A///gAAH//4D///AAA///gP//
        +AAH//+A///8AA///4A=
</value>
  </data>
</root>