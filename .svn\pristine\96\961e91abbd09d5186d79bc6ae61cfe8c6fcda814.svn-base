﻿/**  版本信息模板在安装目录下，可自行修改。
* D_Jkk_Consume.cs
*
* 功 能： N/A
* 类 名： D_Jkk_Consume
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/7/2 10:06:58   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;

namespace DAL
{
	/// <summary>
	/// 数据访问类:D_Jkk_Consume
	/// </summary>
	public partial class D_Jkk_Consume
	{
		public D_Jkk_Consume()
		{}
		#region  BasicMethod

		/// <summary>
		/// 得到最大ID
		/// </summary>
		public int GetMaxId()
		{
		return HisVar.HisVar.Sqldal.GetMaxID("ID", "Jkk_Consume"); 
		}

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(int ID)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from Jkk_Consume");
			strSql.Append(" where ID=@ID");
			SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.Int,4)
			};
			parameters[0].Value = ID;

			return HisVar.HisVar.Sqldal.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public int Add( ModelOld.M_Jkk_Consume model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into Jkk_Consume(");
			strSql.Append("Consume_Lb,Consume_Code,Ry_Name,Ry_Sfzh,Consume_Money,Jsr_Code,Jkk_Code,Consume_Date,JkkId,Consume_Fs,JkkLb,Consume_Zt,Jz_Code,Jz_CwCode)");
			strSql.Append(" values (");
			strSql.Append("@Consume_Lb,@Consume_Code,@Ry_Name,@Ry_Sfzh,@Consume_Money,@Jsr_Code,@Jkk_Code,@Consume_Date,@JkkId,@Consume_Fs,@JkkLb,@Consume_Zt,@Jz_Code,@Jz_CwCode)");
			strSql.Append(";select @@IDENTITY");
			SqlParameter[] parameters = {
					new SqlParameter("@Consume_Lb", SqlDbType.VarChar,50),
					new SqlParameter("@Consume_Code", SqlDbType.VarChar,50),
					new SqlParameter("@Ry_Name", SqlDbType.VarChar,200),
					new SqlParameter("@Ry_Sfzh", SqlDbType.VarChar,50),
					new SqlParameter("@Consume_Money", SqlDbType.Decimal,9),
					new SqlParameter("@Jsr_Code", SqlDbType.VarChar,20),
					new SqlParameter("@Jkk_Code", SqlDbType.VarChar,50),
					new SqlParameter("@Consume_Date", SqlDbType.DateTime),
					new SqlParameter("@JkkId", SqlDbType.Int,4),
					new SqlParameter("@Consume_Fs", SqlDbType.VarChar,50),
					new SqlParameter("@JkkLb", SqlDbType.VarChar,50),
					new SqlParameter("@Consume_Zt", SqlDbType.VarChar,50),
					new SqlParameter("@Jz_Code", SqlDbType.VarChar,50),
					new SqlParameter("@Jz_CwCode", SqlDbType.VarChar,50)};
			parameters[0].Value = model.Consume_Lb;
			parameters[1].Value = model.Consume_Code;
			parameters[2].Value = model.Ry_Name;
			parameters[3].Value = model.Ry_Sfzh;
			parameters[4].Value = model.Consume_Money;
			parameters[5].Value = model.Jsr_Code;
			parameters[6].Value = model.Jkk_Code;
			parameters[7].Value = model.Consume_Date;
			parameters[8].Value = model.JkkId;
			parameters[9].Value = model.Consume_Fs;
			parameters[10].Value = model.JkkLb;
			parameters[11].Value = model.Consume_Zt;
			parameters[12].Value = model.Jz_Code;
			parameters[13].Value = model.Jz_CwCode;

			object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString(),parameters);
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}

        //增加缴费记录
        public int AddJfjl(ModelOld.M_Jkk_Consume model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into Jkk_Consume(");
            strSql.Append("Consume_Lb,Consume_Code,Ry_Name,Ry_Sfzh,Consume_Money,Jsr_Code,Jkk_Code,Consume_Date,JkkId,Consume_Fs,JkkLb,Consume_Zt)");
            strSql.Append(" values (");
            strSql.Append("@Consume_Lb,@Consume_Code,@Ry_Name,@Ry_Sfzh,@Consume_Money,@Jsr_Code,@Jkk_Code,@Consume_Date,@JkkId,@Consume_Fs,@JkkLb,@Consume_Zt)");
            strSql.Append(";select @@IDENTITY");
            SqlParameter[] parameters = {
					new SqlParameter("@Consume_Lb", SqlDbType.VarChar,50),
					new SqlParameter("@Consume_Code", SqlDbType.VarChar,50),
					new SqlParameter("@Ry_Name", SqlDbType.VarChar,200),
					new SqlParameter("@Ry_Sfzh", SqlDbType.VarChar,50),
					new SqlParameter("@Consume_Money", SqlDbType.Decimal,9),
					new SqlParameter("@Jsr_Code", SqlDbType.VarChar,20),
					new SqlParameter("@Jkk_Code", SqlDbType.VarChar,50),
					new SqlParameter("@Consume_Date", SqlDbType.DateTime),
					new SqlParameter("@JkkId", SqlDbType.Int,4),
					new SqlParameter("@Consume_Fs", SqlDbType.VarChar,50),
					new SqlParameter("@JkkLb", SqlDbType.VarChar,50),
					new SqlParameter("@Consume_Zt", SqlDbType.VarChar,50)};
            parameters[0].Value = model.Consume_Lb;
            parameters[1].Value = model.Consume_Code;
            parameters[2].Value = model.Ry_Name;
            parameters[3].Value = model.Ry_Sfzh;
            parameters[4].Value = model.Consume_Money;
            parameters[5].Value = model.Jsr_Code;
            parameters[6].Value = model.Jkk_Code;
            parameters[7].Value = model.Consume_Date;
            parameters[8].Value = model.JkkId;
            parameters[9].Value = model.Consume_Fs;
            parameters[10].Value = model.JkkLb;
            parameters[11].Value = model.Consume_Zt;
            object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString(), parameters);
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update( ModelOld.M_Jkk_Consume model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update Jkk_Consume set ");
			strSql.Append("Consume_Lb=@Consume_Lb,");
			strSql.Append("Consume_Code=@Consume_Code,");
			strSql.Append("Ry_Name=@Ry_Name,");
			strSql.Append("Ry_Sfzh=@Ry_Sfzh,");
			strSql.Append("Consume_Money=@Consume_Money,");
			strSql.Append("Jsr_Code=@Jsr_Code,");
			strSql.Append("Jkk_Code=@Jkk_Code,");
			strSql.Append("Consume_Date=@Consume_Date,");
			strSql.Append("JkkId=@JkkId,");
			strSql.Append("Consume_Fs=@Consume_Fs,");
			strSql.Append("JkkLb=@JkkLb,");
			strSql.Append("Consume_Zt=@Consume_Zt,");
			strSql.Append("Jz_Code=@Jz_Code,");
			strSql.Append("Jz_CwCode=@Jz_CwCode");
			strSql.Append(" where ID=@ID");
			SqlParameter[] parameters = {
					new SqlParameter("@Consume_Lb", SqlDbType.VarChar,50),
					new SqlParameter("@Consume_Code", SqlDbType.VarChar,50),
					new SqlParameter("@Ry_Name", SqlDbType.VarChar,200),
					new SqlParameter("@Ry_Sfzh", SqlDbType.VarChar,50),
					new SqlParameter("@Consume_Money", SqlDbType.Decimal,9),
					new SqlParameter("@Jsr_Code", SqlDbType.VarChar,20),
					new SqlParameter("@Jkk_Code", SqlDbType.VarChar,50),
					new SqlParameter("@Consume_Date", SqlDbType.DateTime),
					new SqlParameter("@JkkId", SqlDbType.Int,4),
					new SqlParameter("@Consume_Fs", SqlDbType.VarChar,50),
					new SqlParameter("@JkkLb", SqlDbType.VarChar,50),
					new SqlParameter("@Consume_Zt", SqlDbType.VarChar,50),
					new SqlParameter("@Jz_Code", SqlDbType.VarChar,50),
					new SqlParameter("@Jz_CwCode", SqlDbType.VarChar,50),
					new SqlParameter("@ID", SqlDbType.Int,4)};
			parameters[0].Value = model.Consume_Lb;
			parameters[1].Value = model.Consume_Code;
			parameters[2].Value = model.Ry_Name;
			parameters[3].Value = model.Ry_Sfzh;
			parameters[4].Value = model.Consume_Money;
			parameters[5].Value = model.Jsr_Code;
			parameters[6].Value = model.Jkk_Code;
			parameters[7].Value = model.Consume_Date;
			parameters[8].Value = model.JkkId;
			parameters[9].Value = model.Consume_Fs;
			parameters[10].Value = model.JkkLb;
			parameters[11].Value = model.Consume_Zt;
			parameters[12].Value = model.Jz_Code;
			parameters[13].Value = model.Jz_CwCode;
			parameters[14].Value = model.ID;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(int ID)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Jkk_Consume ");
			strSql.Append(" where ID=@ID");
			SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.Int,4)
			};
			parameters[0].Value = ID;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string IDlist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Jkk_Consume ");
			strSql.Append(" where ID in ("+IDlist + ")  ");
			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Jkk_Consume GetModel(int ID)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 ID,Consume_Lb,Consume_Code,Ry_Name,Ry_Sfzh,Consume_Money,Jsr_Code,Jkk_Code,Consume_Date,JkkId,Consume_Fs,JkkLb,Consume_Zt,Jz_Code,Jz_CwCode from Jkk_Consume ");
			strSql.Append(" where ID=@ID");
			SqlParameter[] parameters = {
					new SqlParameter("@ID", SqlDbType.Int,4)
			};
			parameters[0].Value = ID;

			 ModelOld.M_Jkk_Consume model=new ModelOld.M_Jkk_Consume();
			DataSet ds=HisVar.HisVar.Sqldal.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Jkk_Consume DataRowToModel(DataRow row)
		{
			 ModelOld.M_Jkk_Consume model=new ModelOld.M_Jkk_Consume();
			if (row != null)
			{
				if(row["ID"]!=null && row["ID"].ToString()!="")
				{
					model.ID=int.Parse(row["ID"].ToString());
				}
				if(row["Consume_Lb"]!=null)
				{
					model.Consume_Lb=row["Consume_Lb"].ToString();
				}
				if(row["Consume_Code"]!=null)
				{
					model.Consume_Code=row["Consume_Code"].ToString();
				}
				if(row["Ry_Name"]!=null)
				{
					model.Ry_Name=row["Ry_Name"].ToString();
				}
				if(row["Ry_Sfzh"]!=null)
				{
					model.Ry_Sfzh=row["Ry_Sfzh"].ToString();
				}
				if(row["Consume_Money"]!=null && row["Consume_Money"].ToString()!="")
				{
					model.Consume_Money=decimal.Parse(row["Consume_Money"].ToString());
				}
				if(row["Jsr_Code"]!=null)
				{
					model.Jsr_Code=row["Jsr_Code"].ToString();
				}
				if(row["Jkk_Code"]!=null)
				{
					model.Jkk_Code=row["Jkk_Code"].ToString();
				}
				if(row["Consume_Date"]!=null && row["Consume_Date"].ToString()!="")
				{
					model.Consume_Date=DateTime.Parse(row["Consume_Date"].ToString());
				}
				if(row["JkkId"]!=null && row["JkkId"].ToString()!="")
				{
					model.JkkId=int.Parse(row["JkkId"].ToString());
				}
				if(row["Consume_Fs"]!=null)
				{
					model.Consume_Fs=row["Consume_Fs"].ToString();
				}
				if(row["JkkLb"]!=null)
				{
					model.JkkLb=row["JkkLb"].ToString();
				}
				if(row["Consume_Zt"]!=null)
				{
					model.Consume_Zt=row["Consume_Zt"].ToString();
				}
				if(row["Jz_Code"]!=null)
				{
					model.Jz_Code=row["Jz_Code"].ToString();
				}
				if(row["Jz_CwCode"]!=null)
				{
					model.Jz_CwCode=row["Jz_CwCode"].ToString();
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ID,Consume_Lb,Consume_Code,Ry_Name,Ry_Sfzh,Consume_Money,Jsr_Code,Jkk_Code,Consume_Date,JkkId,Consume_Fs,JkkLb,Consume_Zt,Jz_Code,Jz_CwCode ");
			strSql.Append(" FROM Jkk_Consume ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" ID,Consume_Lb,Consume_Code,Ry_Name,Ry_Sfzh,Consume_Money,Jsr_Code,Jkk_Code,Consume_Date,JkkId,Consume_Fs,JkkLb,Consume_Zt,Jz_Code,Jz_CwCode ");
			strSql.Append(" FROM Jkk_Consume ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM Jkk_Consume ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.ID desc");
			}
			strSql.Append(")AS Row, T.*  from Jkk_Consume T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Jkk_Consume";
			parameters[1].Value = "ID";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return HisVar.HisVar.Sqldal.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

