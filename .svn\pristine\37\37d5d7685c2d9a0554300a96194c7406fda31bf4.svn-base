﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="0" />
    <DataSources isList="true" count="1">
      <收费明细 Ref="2" type="DataTableSource" isKey="true">
        <Alias>收费明细</Alias>
        <Columns isList="true" count="4">
          <value>V_Group,System.Int64</value>
          <value>Mz_Lb,System.String</value>
          <value>Mz_Sl,System.Int32</value>
          <value>Mz_Money,System.Decimal</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>收费明细</Name>
        <NameInSource>收费明细</NameInSource>
      </收费明细>
    </DataSources>
    <Relations isList="true" count="0" />
    <Report isRef="0" />
    <Variables isList="true" count="22">
      <value>,姓名,姓名,System.String,,False,False</value>
      <value>,性别,性别,System.String,,False,False</value>
      <value>,社保号码,社保号码,System.String,,False,False</value>
      <value>,业务流水号,业务流水号,System.String,,False,False</value>
      <value>,缴费日期,缴费日期,System.String,,False,False</value>
      <value>,医保统筹支付,医保统筹支付,System.String,,False,False</value>
      <value>,个人账户支付,个人账户支付,System.String,,False,False</value>
      <value>,其他医保支付,其他医保支付,System.String,,False,False</value>
      <value>,自费,自费,System.String,,False,False</value>
      <value>,医保外自费,医保外自费,System.String,,False,False</value>
      <value>,报销类别,报销类别,System.String,,False,False</value>
      <value>,个人自付,个人自付,System.String,,False,False</value>
      <value>,个人账户余额,个人账户余额,System.String,,False,False</value>
      <value>,起付标准累计,起付标准累计,System.String,,False,False</value>
      <value>,统筹累计支付,统筹累计支付,System.String,,False,False</value>
      <value>,科室,科室,System.String,,False,False</value>
      <value>,类型,类型,System.String,,False,False</value>
      <value>,医疗机构,医疗机构,System.String,,False,False</value>
      <value>,医保类型,医保类型,System.String,,False,False</value>
      <value>,收款人,收款人,System.String,,False,False</value>
      <value>,打印时间,打印时间,System.String,,False,False</value>
      <value>,健康卡余额,健康卡余额,System.String,,False,False</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="3" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="5">
        <Text27 Ref="4" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>6.8,24.7,1.3,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Microsoft Sans Serif,8.25,Regular,Point,False,134</Font>
          <Guid>f6b2f24201c9450bb6e8dd54b4181fda</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text27</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Text>金额</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Bottom</VertAlignment>
        </Text27>
        <Text120 Ref="5" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>3.2,-0.6,19.4,1</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text120</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <TextBrush>Black</TextBrush>
        </Text120>
        <GroupHeaderBand1 Ref="6" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,16.2,2.6</ClientRectangle>
          <Components isList="true" count="24">
            <Text37 Ref="7" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.6,5,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>d1f710b3f6cd4e4e94d0a8d44c1d404a</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text37</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Text>{医疗机构}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text37>
            <Text57 Ref="8" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>5,1.1,0.8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>4912ad3edb9940989fa996670d085f5a</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text57</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Text>类型：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text57>
            <Text28 Ref="9" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>5.8,1.1,1,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>66311eaa294943c79a30f5d0fa36c5ee</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text28</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Text>{类型}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text28>
            <Text42 Ref="10" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>6.8,1.1,1.2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>f6cfc4c018e843bebf7553d4d7536809</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text42</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Text>科室：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text42>
            <Text45 Ref="11" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>8,1.1,2.3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>980d09dceb0b4f2b93904db8e08e4837</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text45</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Text>{科室}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text45>
            <Text47 Ref="12" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>10.3,1.1,1,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>4797a4b414044ccb99d5de77f0b83a52</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text47</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Text>流水号：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text47>
            <Text48 Ref="13" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>11.3,1.1,4.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>f1033e5cb14e40f7b2d7c02e19f023fc</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text48</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Text>{业务流水号}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text48>
            <Text60 Ref="14" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,16.2,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>de75c2bf151d45cca4e53d05fcbacf73</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text60</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <TextBrush>Black</TextBrush>
            </Text60>
            <Text8 Ref="15" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.6,0.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>ba9cafd58af948f0b4b8976a0a0b9fa7</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text8</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Text>姓名：
</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text8>
            <Text9 Ref="16" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0.8,1.4,3.6,1.1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>8888ebcc1ee44c6b9e371d23696042e1</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text9</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Text>{姓名}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text9>
            <Text10 Ref="17" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.4,1.6,0.6,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>06f480ea1870439882ec67f7007b75bf</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text10</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Text>性别：
</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text10>
            <Text13 Ref="18" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>5,1.6,1.5,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>d71ed76c76474c5da6f65fd6ffc990a3</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text13</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Text>{性别}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text13>
            <Text12 Ref="19" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>6.5,1.6,1.5,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>b9dbbd5c7c334c518c84afc5eee5d3c7</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text12</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Text>医保类型：
</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text12>
            <Text11 Ref="20" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>8,1.6,2.3,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>445bbfb496d84a70b82671250403263a</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text11</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Text>{医保类型}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text11>
            <Text16 Ref="21" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>10.3,1.6,2.1,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>aab06a62903446a8acf955a61f1772a2</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text16</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Text>社会保障号码</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text16>
            <Text17 Ref="22" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>12.4,1.6,3.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>e435ecb6995b4735a527ac824d702f47</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text17</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Text>{社保号码}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text17>
            <Text6 Ref="23" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,2.3,4.4,0.3</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>d8691a73cf6547c9b379f4e4053eb15c</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text6</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Text>       项目/规格</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text6>
            <Text14 Ref="24" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.4,2.3,1,0.3</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>a7e215fe0c8441aca623a3eb50081ddc</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text14</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Text>数量</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text14>
            <Text15 Ref="25" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>5.4,2.3,1.45,0.3</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>950863d87996460ea129b5d91367ef3d</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text15</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Text>金额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text15>
            <Text18 Ref="26" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>6.85,2.3,1.15,0.3</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>cafa629d03e441fea27509c129975425</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text18</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Text>支付类型</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text18>
            <Text19 Ref="27" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>8,2.3,4.4,0.3</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>983f7cb7a17549d89d4087532a93e77a</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text19</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Text>       项目/规格</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text19>
            <Text20 Ref="28" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>12.4,2.3,1,0.3</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>0c9d5bf8fe9a4e0597125c91d98494dd</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text20</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Text>数量</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text20>
            <Text21 Ref="29" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>13.4,2.3,1.2,0.3</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>9a3e0b1dcae2486bbb691337e659c9f0</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text21</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Text>金额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text21>
            <Text22 Ref="30" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>14.6,2.3,1.6,0.3</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>64df6859defb499e8a2938c3541ce13a</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text22</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Text>支付类型</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text22>
          </Components>
          <Condition>{收费明细.V_Group}</Condition>
          <Conditions isList="true" count="0" />
          <Name>GroupHeaderBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </GroupHeaderBand1>
        <DataBand1 Ref="31" type="DataBand" isKey="true">
          <Border>None;Black;2;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,3.8,16.2,0.5</ClientRectangle>
          <ColumnDirection>DownThenAcross</ColumnDirection>
          <Columns>2</Columns>
          <Components isList="true" count="4">
            <Text26 Ref="32" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.4,0,1,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>cf8245b781b241699d2390cd188a5f47</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text26</Name>
              <NullValue>                               </NullValue>
              <Page isRef="3" />
              <Parent isRef="31" />
              <Text>{收费明细.Mz_Sl}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="33" type="CustomFormat" isKey="true">
                <StringFormat>0.####</StringFormat>
              </TextFormat>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text26>
            <Text29 Ref="34" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>5.4,0,1.45,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>3b3a939e7b59422592930d597aecdf53</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text29</Name>
              <NullValue>  </NullValue>
              <Page isRef="3" />
              <Parent isRef="31" />
              <Text>{收费明细.Mz_Money}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text29>
            <Text30 Ref="35" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>6.85,0,1.25,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>47a3e0c9e6ff4fdf84a2b5a4a22b4792</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text30</Name>
              <Page isRef="3" />
              <Parent isRef="31" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text30>
            <Text5 Ref="36" type="Text" isKey="true">
              <Border>None;Black;1;Solid;False;4;Transparent</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,4.4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>073ce9d0318c4441a9e00a1be3eee141</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text5</Name>
              <Page isRef="3" />
              <Parent isRef="31" />
              <Text>{收费明细.Mz_Lb}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text5>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>收费明细</DataSourceName>
          <Filters isList="true" count="0" />
          <Name>DataBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Sort isList="true" count="2">
            <value>DESC</value>
            <value>Mz_Lb</value>
          </Sort>
        </DataBand1>
        <GroupFooterBand1 Ref="37" type="GroupFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,5.1,16.2,3.2</ClientRectangle>
          <Components isList="true" count="25">
            <Text24 Ref="38" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.6,2.2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>ecda88153b7a45929cfefe6856a4bd4d</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text24</Name>
              <Page isRef="3" />
              <Parent isRef="37" />
              <Text>医保统筹支付:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text24>
            <Text25 Ref="39" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.2,0.6,2.2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>eefce81440e443cb8cb8ff376bc6a23d</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text25</Name>
              <Page isRef="3" />
              <Parent isRef="37" />
              <Text>{医保统筹支付}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text25>
            <Text46 Ref="40" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.4,0.6,2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>5ab927c113cc49dea10ee0f4602a0b46</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text46</Name>
              <Page isRef="3" />
              <Parent isRef="37" />
              <Text>个人账户支付:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text46>
            <Text59 Ref="41" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>6.4,0.6,2.2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>13fbe2e18e3145e4956a151e84ba6ad0</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text59</Name>
              <Page isRef="3" />
              <Parent isRef="37" />
              <Text>{个人账户支付}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text59>
            <Text64 Ref="42" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1,2.2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>f8e5fbd9afc74dabb8928fc4a6009dea</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text64</Name>
              <Page isRef="3" />
              <Parent isRef="37" />
              <Text>个人账户余额:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text64>
            <Text65 Ref="43" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.2,1,2.2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>7fa11cf46c284f25a1fe96663e0d4d49</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text65</Name>
              <Page isRef="3" />
              <Parent isRef="37" />
              <Text>{个人账户余额}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text65>
            <Text66 Ref="44" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.4,1,2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>7b6141328c024fa4aef31bddfbd78018</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text66</Name>
              <Page isRef="3" />
              <Parent isRef="37" />
              <Text>起付标准累计:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text66>
            <Text67 Ref="45" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>6.4,1,2.2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>bc0e2e8490e54a2e94394b32ed16711b</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text67</Name>
              <Page isRef="3" />
              <Parent isRef="37" />
              <Text>{起付标准累计}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text67>
            <Text68 Ref="46" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>8.6,1,2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>ad1820c27ddb422f8aeef66f68ad8cde</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text68</Name>
              <Page isRef="3" />
              <Parent isRef="37" />
              <Text>统筹累计支付:
</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text68>
            <Text69 Ref="47" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>10.6,1,5.6,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>21c71b6df7164501b64c5ab3a090bfab</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text69</Name>
              <Page isRef="3" />
              <Parent isRef="37" />
              <Text>{统筹累计支付}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text69>
            <Text70 Ref="48" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>8.6,0.6,1.6,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>08f4c01d9eaa468abb59be1dd315e09d</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text70</Name>
              <Page isRef="3" />
              <Parent isRef="37" />
              <Text>个人自付:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text70>
            <Text71 Ref="49" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>10.2,0.6,1.6,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>4ab29741037e46f599a7d9d8a808dae2</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text71</Name>
              <Page isRef="3" />
              <Parent isRef="37" />
              <Text>{个人自付}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text71>
            <Text72 Ref="50" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>11.8,0.6,1.6,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>3fdc13915f8840b5962d4680cdd5a93f</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text72</Name>
              <Page isRef="3" />
              <Parent isRef="37" />
              <Text>个人自费:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text72>
            <Text73 Ref="51" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>13.4,0.6,2.8,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>74ecc623345a417cabeb13be53cb58e0</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text73</Name>
              <Page isRef="3" />
              <Parent isRef="37" />
              <Text>{自费}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text73>
            <Text43 Ref="52" type="Text" isKey="true">
              <Border>None;Black;1;Solid;False;4;Transparent</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.2,0,6.9,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>f05a21251ac84c2da3b1e837f5d9b959</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text43</Name>
              <Page isRef="3" />
              <Parent isRef="37" />
              <Text>{MoneyCn(Sum(GroupHeaderBand1,收费明细.Mz_Money))}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text43>
            <Text44 Ref="53" type="Text" isKey="true">
              <Border>None;Black;1;Solid;False;4;Transparent</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9.1,0,0.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>8476af18b9274878b986fe14d535accc</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text44</Name>
              <Page isRef="3" />
              <Parent isRef="37" />
              <Text>￥</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text44>
            <Text49 Ref="54" type="Text" isKey="true">
              <Border>None;Black;1;Solid;False;4;Transparent</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9.7,0,6.5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>91b8bd3567144a42809a29ecb3d89ca3</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text49</Name>
              <Page isRef="3" />
              <Parent isRef="37" />
              <Text>{Sum(GroupHeaderBand1,收费明细.Mz_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text49>
            <Text41 Ref="55" type="Text" isKey="true">
              <Border>None;Black;1;Solid;False;4;Transparent</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>90398efe882947c9a6b62607918a7968</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text41</Name>
              <Page isRef="3" />
              <Parent isRef="37" />
              <Text>合计（大写）：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text41>
            <Text1 Ref="56" type="Text" isKey="true">
              <Border>None;Black;1;Solid;False;4;Transparent</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.4,16.2,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>794b2f6b3f284cba9dcae2e0d2c5cf72</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text1</Name>
              <Page isRef="3" />
              <Parent isRef="37" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text1>
            <Text38 Ref="57" type="Text" isKey="true">
              <Border>None;Black;1;Solid;False;4;Transparent</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,2.7,2.4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>db788f7e516f4c14a4247ca6a71b9b6f</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text38</Name>
              <Page isRef="3" />
              <Parent isRef="37" />
              <Text>收款单位（章）：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text38>
            <Text39 Ref="58" type="Text" isKey="true">
              <Border>None;Black;1;Solid;False;4;Transparent</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.4,2.7,4.8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>fd9147d0a886491989f6350600236354</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text39</Name>
              <Page isRef="3" />
              <Parent isRef="37" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text39>
            <Text40 Ref="59" type="Text" isKey="true">
              <Border>None;Black;1;Solid;False;4;Transparent</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.2,2.7,1.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>8a92e670768940628290dae793f0cdf2</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text40</Name>
              <Page isRef="3" />
              <Parent isRef="37" />
              <Text>收款人:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text40>
            <Text50 Ref="60" type="Text" isKey="true">
              <Border>None;Black;1;Solid;False;4;Transparent</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>8.8,2.7,3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>1c38663c3a144720b965eeb6b383650b</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text50</Name>
              <Page isRef="3" />
              <Parent isRef="37" />
              <Text>{收款人}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text50>
            <Text52 Ref="61" type="Text" isKey="true">
              <Border>None;Black;1;Solid;False;4;Transparent</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>11.8,2.7,4.4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>0c729726e2fa44cc85c3f9d91abc03af</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text52</Name>
              <Page isRef="3" />
              <Parent isRef="37" />
              <Text>{打印时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text52>
            <Text2 Ref="62" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,2.1,16.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>a5ca79a30d5840c8997bca931e57e95d</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text2</Name>
              <Page isRef="3" />
              <Parent isRef="37" />
              <Text>{健康卡余额}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text2>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>GroupFooterBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </GroupFooterBand1>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>20c1288e9da6491e858185f416cfb06d</Guid>
      <Margins>2,0.8,0,0</Margins>
      <Name>Page1</Name>
      <PageHeight>12.7</PageHeight>
      <PageWidth>19</PageWidth>
      <Report isRef="0" />
      <Watermark Ref="63" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page1>
  </Pages>
  <PrinterSettings Ref="64" type="Stimulsoft.Report.Print.StiPrinterSettings" isKey="true" />
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>2013年河北省门诊收费票据</ReportAlias>
  <ReportChanged>1/21/2015 6:04:57 PM</ReportChanged>
  <ReportCreated>12/21/2012 9:16:47 AM</ReportCreated>
  <ReportFile>D:\his医院系统\新医院管理系统V3\his2010\Rpt\2013年河北省门诊收费票据.mrt</ReportFile>
  <ReportGuid>1949ad6a36a440d1b6c424a7f486b70b</ReportGuid>
  <ReportName>2013年河北省门诊收费票据</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2011.2.1026</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class Report : Stimulsoft.Report.StiReport
    {
        public Report()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
        #endregion StiReport Designer generated code - do not modify
		
		public	string NumberCn(decimal ANumber) 

		{

			const string cPointCn = "点十百千万十百千亿十百千";

			const string cNumberCn = "零一二三四五六七八九";

			string S = ANumber.ToString();

			if (S == "0") return "" + cPointCn[0];

			if (!S.Contains(".")) S += ".";

			int P = S.IndexOf(".");

			string Result = "";

    

			for (int i = 0; i &lt; S.Length; i++)

			{

				if (P == i)

				{

					Result = Result.Replace("零十零", "零");

					Result = Result.Replace("零百零", "零");

					Result = Result.Replace("零千零", "零");

					Result = Result.Replace("零十", "零");

					Result = Result.Replace("零百", "零");

					Result = Result.Replace("零千", "零");

					Result = Result.Replace("零万", "万");

					Result = Result.Replace("零亿", "亿");

					Result = Result.Replace("亿万", "亿");

					Result = Result.Replace("零点", "点");

				}

				else

				{

					if (P &gt; i)

						Result += "" + cNumberCn[S[i] - '0'] + cPointCn[P - i - 1];

					else Result += "" + cNumberCn[S[i] - '0'];

				}

			}

			if (Result.Substring(Result.Length - 1, 1) == "" + cPointCn[0])

				Result = Result.Remove(Result.Length - 1); // 一点-&gt; 一

    

			if (Result[0] == cPointCn[0])

				Result = cNumberCn[0] + Result; // 点三-&gt; 零点三

 

			if ((Result.Length &gt; 1) &amp;&amp; (Result[1] == cPointCn[1]) &amp;&amp; 

				(Result[0] == cNumberCn[1]))

				Result = Result.Remove(0, 1); // 一十三-&gt; 十三

			return Result;

		}

 

		public	string MoneyCn(decimal ANumber)

		{

			string V_Fs="";
			if (ANumber &lt; 0)
			{
				ANumber = -ANumber;
				V_Fs="负";
			}
			else
			{
				V_Fs = "";
			}

			
			if (ANumber == 0) return "零";

			string Result = NumberCn(Math.Truncate(ANumber * 100) / 100);

			Result = Result.Replace("一", "壹");

			Result = Result.Replace("二", "贰");

			Result = Result.Replace("三", "叁");

			Result = Result.Replace("四", "肆");

			Result = Result.Replace("五", "伍");

			Result = Result.Replace("六", "陆");

			Result = Result.Replace("七", "柒");

			Result = Result.Replace("八", "捌");

			Result = Result.Replace("九", "玖");

			Result = Result.Replace("九", "玖");

			Result = Result.Replace("十", "拾");

			Result = Result.Replace("百", "佰");

			Result = Result.Replace("千", "仟");

			if (Result.Contains("点"))

			{

				int P = Result.IndexOf("点");
				if (P + 3 &gt; Result.Length)
				{
					Result = Result+ "分";
				}
				else

				{
					Result = Result.Insert(P + 3, "分");
				}
				//	Result = Result.Insert(P + 3, "分");

				Result = Result.Insert(P + 2, "角");

				Result = Result.Replace("点", "圆");

				Result = Result.Replace("角分", "角");

				Result = Result.Replace("零分", "");

				Result = Result.Replace("零角", "");

				Result = Result.Replace("分角", "");

				if (Result.Substring(0, 2) == "零圆")

					Result = Result.Replace("零圆", "");

			} else Result += "圆整";

			Result =  V_Fs+Result;

			return Result;

		}
		
		
		
		
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>