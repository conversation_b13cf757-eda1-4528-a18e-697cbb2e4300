﻿Imports System.Data.SqlClient
Imports HisControl

Public Class Cq_Cf3

#Region "定义__变量"
    Public Cb_Cm As CurrencyManager                     '同步指针

    Dim My_Dataset As New DataSet

#End Region

#Region "传参"
    'Dim Rform As BaseForm
    Dim Rinsert As Boolean
    Dim Rrow As DataRow
    Dim RZbtb As DataTable
    Dim Rtdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Dim Rlb As C1.Win.C1Input.C1Label
    Dim Rzbadt As SqlDataAdapter
    Dim Rlx As String
#End Region

    Public Sub New(ByVal tform As BaseForm, ByVal tinsert As Boolean, ByVal trow As DataRow, ByVal tZbtb As DataTable, ByVal ttdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid, ByVal tlb As C1.Win.C1Input.C1Label, ByVal tzbadt As SqlDataAdapter, ByVal tdataset As DataSet, ByVal tlx As String)

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        'Rform = tform
        Rinsert = tinsert
        Rrow = trow
        RZbtb = tZbtb
        Rtdbgrid = ttdbgrid
        Rlb = tlb
        Rzbadt = tzbadt
        My_Dataset = tdataset
        Rlx = tlx
        ' 在 InitializeComponent() 调用之后添加任何初始化。

    End Sub

    Private Sub Cq_Cf3_FormClosed(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosedEventArgs) Handles Me.FormClosed
        'Rform.Visible = True
    End Sub

    Private Sub Cq_Cf3_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        'Rform.Visible = False
        Call Form_Init()
        Call Zb_Show()
    End Sub

#Region "窗体__事件"

    Private Sub Form_Init()
        Dim My_Grid As New BaseClass.C_Grid(Me.C1TrueDBGrid1)
        With My_Grid
            .Init_Grid()
            .P_Dock(BaseClass.C_Grid.Dock.Fill)
            .Init_Column("名称", "Xm_Name", 160, "左", "")
            .Init_Column("规格", "Mx_Gg", 100, "左", "")
            .Init_Column("产地", "Mx_Cd", 200, "左", "")
            .Init_Column("有效期", "Yp_Yxq", 80, "中", "yyyy-MM-dd")
            .Init_Column("数量", "Cf_Sl", 60, "右", "###,###,##0.00##")
            .Init_Column("单位", "Xm_Dw", 45, "左", "")
            .Init_Column("单价", "Cf_Dj", 70, "右", "###,###,##0.00####")
            .Init_Column("金额", "Cf_Money", 80, "右", "###,###,##0.00##")
            .Init_Column("用法用量", "Yp_Yfyl", 200, "左", "")
            .Init_Column("类别", "Cf_Lb", 70, "中", "")
        End With
    End Sub
#End Region

#Region "清空__显示"

    Private Sub Zb_Show()   '显示记录
        With Rrow
            C1TextBox1.Text = .Item("AutoCf_Code") & ""                                   '出库编码
            C1TextBox2.Text = .Item("Ry_Name") & ""                                   '客户编码
            C1TextBox3.Text = .Item("Ks_Name") & ""
            C1TextBox4.Text = .Item("Ry_Sex") & ""
            C1TextBox5.Text = .Item("Ys_Name") & ""
            C1TextBox6.Text = .Item("Bc_Name") & ""

            C1TextBox1.ReadOnly = True
            C1TextBox1.BackColor = SystemColors.Info
            C1TextBox2.ReadOnly = True
            C1TextBox2.BackColor = SystemColors.Info
            C1TextBox3.ReadOnly = True
            C1TextBox3.BackColor = SystemColors.Info
            C1TextBox4.ReadOnly = True
            C1TextBox4.BackColor = SystemColors.Info
            C1TextBox5.ReadOnly = True
            C1TextBox5.BackColor = SystemColors.Info
            C1TextBox6.ReadOnly = True
            C1TextBox6.BackColor = SystemColors.Info


        End With
        Call P_Data_Show()

    End Sub


    Private Sub P_Data_Show()   '从表数据
   
        Dim Str_Select As String = "Select '诊疗' as Lb, Cf_Id,Cf_Dj,Cf_Sl,Cf_Money,''Mx_Gyzz,'' As Mx_Gg,''Mx_Cd,Xm_Dw,Xm_Name,Cf_Lb,''Yp_Ph,Null Yp_Yxq,''Yp_Yfyl From Auto_Cfxm,Zd_Ml_Xm3 Where  Auto_Cfxm.Xm_Code=Zd_Ml_Xm3.Xm_Code And AutoCf_Code='" & Rrow.Item("AutoCf_Code") & "' Union All  Select '药品' as Lb,Cf_Id,Cf_Dj,Cf_Sl,Cf_Money,Mx_Gyzz,Mx_Gg,Mx_Cd ,Mx_XsDw,Yp_Name,Cf_Lb,V_YpKc.Yp_Ph,V_YpKc.Yp_Yxq,Yp_Yfyl From Auto_Cfyp,V_YpKc Where Auto_Cfyp.Xx_Code=V_YpKc.Xx_Code And AutoCf_Code='" & Rrow.Item("AutoCf_Code") & "' Order By Cf_Id"
        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, Str_Select, "医嘱明细", True)

        '主表记录
        Cb_Cm = CType(BindingContext(My_Dataset, "医嘱明细"), CurrencyManager)
        C1TrueDBGrid1.SetDataBinding(My_Dataset, "医嘱明细", True)
        Dim Sum1 As Double = 0
        Dim Sum2 As Double = 0
     

        Sum1 = IIf(My_Dataset.Tables("医嘱明细").Compute("Sum(Cf_Money)", "Lb='诊疗'") Is DBNull.Value, 0, My_Dataset.Tables("医嘱明细").Compute("Sum(Cf_Money)", "Lb='诊疗'"))
        Sum2 = IIf(My_Dataset.Tables("医嘱明细").Compute("Sum(Cf_Money)", "Lb='药品'") Is DBNull.Value, 0, My_Dataset.Tables("医嘱明细").Compute("Sum(Cf_Money)", "Lb='药品'"))

        Label2.Text = "诊疗费用：" & Format(Sum1, "###0.00") & "  药品费用:" & Format(Sum2, "###0.00") & "  合计：" & Format(Sum1 + Sum2, "###0.00")
    
    End Sub



#End Region



End Class