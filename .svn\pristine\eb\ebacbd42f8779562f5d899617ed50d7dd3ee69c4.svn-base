﻿/**  版本信息模板在安装目录下，可自行修改。
* Jkda_User.cs
*
* 功 能： N/A
* 类 名： Jkda_User
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2018/10/22 9:07:57   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using ModelOld;

namespace DAL
{
	/// <summary>
	/// 数据访问类:Jkda_User
	/// </summary>
	public partial class D_Jkda_User
	{
		public D_Jkda_User()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string Jsr_Code)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from Jkda_User");
			strSql.Append(" where Jsr_Code=@Jsr_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Jsr_Code", SqlDbType.Char,7)			};
			parameters[0].Value = Jsr_Code;

			return HisVar.HisVar.Sqldal.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(M_Jkda_User model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into Jkda_User(");
			strSql.Append("Jsr_Code,Jkda_Username,Jkda_Password,Jkda_Token)");
			strSql.Append(" values (");
			strSql.Append("@Jsr_Code,@Jkda_Username,@Jkda_Password,@Jkda_Token)");
			SqlParameter[] parameters = {
					new SqlParameter("@Jsr_Code", SqlDbType.Char,7),
					new SqlParameter("@Jkda_Username", SqlDbType.VarChar,50),
					new SqlParameter("@Jkda_Password", SqlDbType.VarChar,50),
					new SqlParameter("@Jkda_Token", SqlDbType.VarChar,50)};
			parameters[0].Value = model.Jsr_Code;
			parameters[1].Value = model.Jkda_Username;
			parameters[2].Value = model.Jkda_Password;
			parameters[3].Value = Common.Tools.IsValueNull(model.Jkda_Token);

			int rows= HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(M_Jkda_User model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update Jkda_User set ");
			strSql.Append("Jkda_Username=@Jkda_Username,");
			strSql.Append("Jkda_Password=@Jkda_Password,");
			strSql.Append("Jkda_Token=@Jkda_Token");
			strSql.Append(" where Jsr_Code=@Jsr_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Jkda_Username", SqlDbType.VarChar,50),
					new SqlParameter("@Jkda_Password", SqlDbType.VarChar,50),
					new SqlParameter("@Jkda_Token", SqlDbType.VarChar,50),
					new SqlParameter("@Jsr_Code", SqlDbType.Char,7)};
			parameters[0].Value = model.Jkda_Username;
			parameters[1].Value = model.Jkda_Password;
			parameters[2].Value = Common.Tools.IsValueNull(model.Jkda_Token);
			parameters[3].Value = model.Jsr_Code;

			int rows= HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string Jsr_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Jkda_User ");
			strSql.Append(" where Jsr_Code=@Jsr_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Jsr_Code", SqlDbType.Char,7)			};
			parameters[0].Value = Jsr_Code;

			int rows= HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string Jsr_Codelist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Jkda_User ");
			strSql.Append(" where Jsr_Code in ("+Jsr_Codelist + ")  ");
			int rows= HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public M_Jkda_User GetModel(string Jsr_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 Jsr_Code,Jkda_Username,Jkda_Password,Jkda_Token from Jkda_User ");
			strSql.Append(" where Jsr_Code=@Jsr_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Jsr_Code", SqlDbType.Char,7)			};
			parameters[0].Value = Jsr_Code;

			M_Jkda_User model=new M_Jkda_User();
			DataSet ds= HisVar.HisVar.Sqldal.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public M_Jkda_User DataRowToModel(DataRow row)
		{
			M_Jkda_User model=new M_Jkda_User();
			if (row != null)
			{
				if(row["Jsr_Code"]!=null)
				{
					model.Jsr_Code=row["Jsr_Code"].ToString();
				}
				if(row["Jkda_Username"]!=null)
				{
					model.Jkda_Username=row["Jkda_Username"].ToString();
				}
				if(row["Jkda_Password"]!=null)
				{
					model.Jkda_Password=row["Jkda_Password"].ToString();
				}
				if(row["Jkda_Token"]!=null)
				{
					model.Jkda_Token=row["Jkda_Token"].ToString();
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select Jsr_Code,Jkda_Username,Jkda_Password,Jkda_Token ");
			strSql.Append(" FROM Jkda_User ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" Jsr_Code,Jkda_Username,Jkda_Password,Jkda_Token ");
			strSql.Append(" FROM Jkda_User ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM Jkda_User ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.Jsr_Code desc");
			}
			strSql.Append(")AS Row, T.*  from Jkda_User T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Jkda_User";
			parameters[1].Value = "Jsr_Code";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return DbHelperSQL.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

