﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class Hsz_Cqcf_Dr
    Inherits HisControl.Base

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel()
        Me.C1FlexGrid2 = New C1.Win.C1FlexGrid.C1FlexGrid()
        Me.C1FlexGrid1 = New C1.Win.C1FlexGrid.C1FlexGrid()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.DateComobo = New CustomControl.MySingleComobo()
        Me.CfTimeDateEdit = New CustomControl.MyDateEdit()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.ChkKs = New System.Windows.Forms.CheckBox()
        Me.Button1 = New System.Windows.Forms.Button()
        Me.RadioButton2 = New System.Windows.Forms.RadioButton()
        Me.RadioButton1 = New System.Windows.Forms.RadioButton()
        Me.ChkAllCheck = New System.Windows.Forms.CheckBox()
        Me.TableLayoutPanel1.SuspendLayout
        CType(Me.C1FlexGrid2,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.C1FlexGrid1,System.ComponentModel.ISupportInitialize).BeginInit
        Me.Panel1.SuspendLayout
        Me.SuspendLayout
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.ColumnCount = 1
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100!))
        Me.TableLayoutPanel1.Controls.Add(Me.C1FlexGrid2, 0, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.C1FlexGrid1, 0, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.Panel1, 0, 0)
        Me.TableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(0, 0)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.RowCount = 3
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 66!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 280!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100!))
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(1014, 701)
        Me.TableLayoutPanel1.TabIndex = 0
        '
        'C1FlexGrid2
        '
        Me.C1FlexGrid2.ColumnInfo = "10,1,0,0,0,0,Columns:"
        Me.C1FlexGrid2.Dock = System.Windows.Forms.DockStyle.Fill
        Me.C1FlexGrid2.DrawMode = C1.Win.C1FlexGrid.DrawModeEnum.OwnerDraw
        Me.C1FlexGrid2.Location = New System.Drawing.Point(3, 349)
        Me.C1FlexGrid2.Name = "C1FlexGrid2"
        Me.C1FlexGrid2.Rows.DefaultSize = 18
        Me.C1FlexGrid2.Size = New System.Drawing.Size(1008, 349)
        Me.C1FlexGrid2.TabIndex = 4
        '
        'C1FlexGrid1
        '
        Me.C1FlexGrid1.ColumnInfo = "10,1,0,0,0,0,Columns:"
        Me.C1FlexGrid1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.C1FlexGrid1.DrawMode = C1.Win.C1FlexGrid.DrawModeEnum.OwnerDraw
        Me.C1FlexGrid1.Location = New System.Drawing.Point(3, 69)
        Me.C1FlexGrid1.Name = "C1FlexGrid1"
        Me.C1FlexGrid1.Rows.DefaultSize = 18
        Me.C1FlexGrid1.Size = New System.Drawing.Size(1008, 274)
        Me.C1FlexGrid1.TabIndex = 3
        '
        'Panel1
        '
        Me.Panel1.Controls.Add(Me.ChkAllCheck)
        Me.Panel1.Controls.Add(Me.DateComobo)
        Me.Panel1.Controls.Add(Me.CfTimeDateEdit)
        Me.Panel1.Controls.Add(Me.Label1)
        Me.Panel1.Controls.Add(Me.ChkKs)
        Me.Panel1.Controls.Add(Me.Button1)
        Me.Panel1.Controls.Add(Me.RadioButton2)
        Me.Panel1.Controls.Add(Me.RadioButton1)
        Me.Panel1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel1.Location = New System.Drawing.Point(3, 3)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(1008, 60)
        Me.Panel1.TabIndex = 0
        '
        'DateComobo
        '
        Me.DateComobo.Captain = "生成日期"
        Me.DateComobo.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.DateComobo.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.DateComobo.CaptainWidth = 59!
        Me.DateComobo.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        Me.DateComobo.ItemHeight = 16
        Me.DateComobo.ItemTextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.DateComobo.Location = New System.Drawing.Point(204, 9)
        Me.DateComobo.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.DateComobo.MinimumSize = New System.Drawing.Size(0, 20)
        Me.DateComobo.Name = "DateComobo"
        Me.DateComobo.ReadOnly = false
        Me.DateComobo.Size = New System.Drawing.Size(150, 20)
        Me.DateComobo.TabIndex = 7
        Me.DateComobo.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        '
        'CfTimeDateEdit
        '
        Me.CfTimeDateEdit.Captain = "处方时间"
        Me.CfTimeDateEdit.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.CfTimeDateEdit.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.CfTimeDateEdit.CaptainWidth = 60!
        Me.CfTimeDateEdit.InitialSelection = C1.Win.C1Input.InitialSelectionEnum.CaretAtEnd
        Me.CfTimeDateEdit.Location = New System.Drawing.Point(204, 35)
        Me.CfTimeDateEdit.MaximumSize = New System.Drawing.Size(100000000, 20)
        Me.CfTimeDateEdit.MinimumSize = New System.Drawing.Size(0, 20)
        Me.CfTimeDateEdit.Name = "CfTimeDateEdit"
        Me.CfTimeDateEdit.ReadOnly = false
        Me.CfTimeDateEdit.Size = New System.Drawing.Size(150, 20)
        Me.CfTimeDateEdit.TabIndex = 6
        Me.CfTimeDateEdit.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.CfTimeDateEdit.ValueIsDbNull = false
        Me.CfTimeDateEdit.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.CfTimeDateEdit.VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.DropDown
        '
        'Label1
        '
        Me.Label1.AutoSize = true
        Me.Label1.ForeColor = System.Drawing.Color.FromArgb(CType(CType(128,Byte),Integer), CType(CType(64,Byte),Integer), CType(CType(0,Byte),Integer))
        Me.Label1.Location = New System.Drawing.Point(453, 25)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(425, 12)
        Me.Label1.TabIndex = 5
        Me.Label1.Text = "注：导入后，如发现有问题，请使用拒领功能后根据日期在医嘱录入处进行更改"
        '
        'ChkKs
        '
        Me.ChkKs.AutoSize = true
        Me.ChkKs.Location = New System.Drawing.Point(11, 36)
        Me.ChkKs.Name = "ChkKs"
        Me.ChkKs.Size = New System.Drawing.Size(84, 16)
        Me.ChkKs.TabIndex = 4
        Me.ChkKs.Text = "选择本科室"
        Me.ChkKs.UseVisualStyleBackColor = true
        '
        'Button1
        '
        Me.Button1.Location = New System.Drawing.Point(370, 16)
        Me.Button1.Name = "Button1"
        Me.Button1.Size = New System.Drawing.Size(68, 31)
        Me.Button1.TabIndex = 2
        Me.Button1.Text = "导入"
        Me.Button1.UseVisualStyleBackColor = true
        '
        'RadioButton2
        '
        Me.RadioButton2.AutoSize = true
        Me.RadioButton2.Location = New System.Drawing.Point(101, 35)
        Me.RadioButton2.Name = "RadioButton2"
        Me.RadioButton2.Size = New System.Drawing.Size(95, 16)
        Me.RadioButton2.TabIndex = 1
        Me.RadioButton2.Text = "明天可领医嘱"
        Me.RadioButton2.UseVisualStyleBackColor = true
        '
        'RadioButton1
        '
        Me.RadioButton1.AutoSize = true
        Me.RadioButton1.Checked = true
        Me.RadioButton1.Location = New System.Drawing.Point(101, 9)
        Me.RadioButton1.Name = "RadioButton1"
        Me.RadioButton1.Size = New System.Drawing.Size(95, 16)
        Me.RadioButton1.TabIndex = 0
        Me.RadioButton1.TabStop = true
        Me.RadioButton1.Text = "今天可领医嘱"
        Me.RadioButton1.UseVisualStyleBackColor = true
        '
        'ChkAllCheck
        '
        Me.ChkAllCheck.AutoSize = true
        Me.ChkAllCheck.Location = New System.Drawing.Point(11, 9)
        Me.ChkAllCheck.Name = "ChkAllCheck"
        Me.ChkAllCheck.Size = New System.Drawing.Size(48, 16)
        Me.ChkAllCheck.TabIndex = 10
        Me.ChkAllCheck.Text = "全选"
        Me.ChkAllCheck.UseVisualStyleBackColor = true
        '
        'Hsz_Cqcf_Dr
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6!, 12!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1014, 701)
        Me.Controls.Add(Me.TableLayoutPanel1)
        Me.MaximizeBox = false
        Me.MinimizeBox = false
        Me.Name = "Hsz_Cqcf_Dr"
        Me.ShowInTaskbar = false
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "长期医嘱导入"
        Me.TableLayoutPanel1.ResumeLayout(false)
        CType(Me.C1FlexGrid2,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.C1FlexGrid1,System.ComponentModel.ISupportInitialize).EndInit
        Me.Panel1.ResumeLayout(false)
        Me.Panel1.PerformLayout
        Me.ResumeLayout(false)

End Sub
    Friend WithEvents TableLayoutPanel1 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents RadioButton2 As System.Windows.Forms.RadioButton
    Friend WithEvents RadioButton1 As System.Windows.Forms.RadioButton
    Friend WithEvents Button1 As System.Windows.Forms.Button
    Friend WithEvents ChkKs As System.Windows.Forms.CheckBox
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents CfTimeDateEdit As CustomControl.MyDateEdit
    Friend WithEvents DateComobo As CustomControl.MySingleComobo
    Friend WithEvents C1FlexGrid1 As C1.Win.C1FlexGrid.C1FlexGrid
    Friend WithEvents C1FlexGrid2 As C1.Win.C1FlexGrid.C1FlexGrid
    Friend WithEvents ChkAllCheck As CheckBox
End Class
