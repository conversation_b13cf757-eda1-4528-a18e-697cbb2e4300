﻿/**  版本信息模板在安装目录下，可自行修改。
* M_Materials_Return2.cs
*
* 功 能： N/A
* 类 名： M_Materials_Return2
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016-11-30 17:16:48   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace ModelOld
{
	/// <summary>
	/// 物资退库从表
	/// </summary>
	[Serializable]
	public partial class M_Materials_Return2
	{
		public M_Materials_Return2()
		{}
		#region Model
		private string _m_return_code;
		private string _materials_code;
        private string _materials_name;
		private string _materialsstock_code;
		private string _m_return_detail_code;
		private string _materialslot;
		private DateTime? _materialsexpirydate;
		private decimal? _m_return_num;
		private decimal? _m_return_writeoffno;
		private decimal? _m_return_realno;
		private decimal? _m_return_price;
		private decimal? _m_return_money;
		private decimal? _m_return_realmoney;
		private string _m_returndetail_memo;
		/// <summary>
		/// yyMMdd+5位流水号
		/// </summary>
		public string M_Return_Code
		{
			set{ _m_return_code=value;}
			get{return _m_return_code;}
		}
		/// <summary>
		/// 物资编码
		/// </summary>
		public string Materials_Code
		{
			set{ _materials_code=value;}
			get{return _materials_code;}
		}
        /// <summary>
        /// 物资名称
        /// </summary>
        public string Materials_Name
        {
            set { _materials_name = value; }
            get { return _materials_name; }
        }
		/// <summary>
		/// 物资编码+6位流水号
		/// </summary>
		public string MaterialsStock_Code
		{
			set{ _materialsstock_code=value;}
			get{return _materialsstock_code;}
		}
		/// <summary>
		/// 退库明细编码
		/// </summary>
		public string M_Return_Detail_Code
		{
			set{ _m_return_detail_code=value;}
			get{return _m_return_detail_code;}
		}
		/// <summary>
		/// 物资批号
		/// </summary>
		public string MaterialsLot
		{
			set{ _materialslot=value;}
			get{return _materialslot;}
		}
		/// <summary>
		/// 物资有效期
		/// </summary>
		public DateTime? MaterialsExpiryDate
		{
			set{ _materialsexpirydate=value;}
			get{return _materialsexpirydate;}
		}
		/// <summary>
		/// 退库数量
		/// </summary>
		public decimal? M_Return_Num
		{
			set{ _m_return_num=value;}
			get{return _m_return_num;}
		}
		/// <summary>
		/// 
		/// </summary>
		public decimal? M_Return_WriteoffNo
		{
			set{ _m_return_writeoffno=value;}
			get{return _m_return_writeoffno;}
		}
		/// <summary>
		/// 
		/// </summary>
		public decimal? M_Return_RealNo
		{
			set{ _m_return_realno=value;}
			get{return _m_return_realno;}
		}
		/// <summary>
		/// 退库单价
		/// </summary>
		public decimal? M_Return_Price
		{
			set{ _m_return_price=value;}
			get{return _m_return_price;}
		}
		/// <summary>
		/// 退库金额
		/// </summary>
		public decimal? M_Return_Money
		{
			set{ _m_return_money=value;}
			get{return _m_return_money;}
		}
		/// <summary>
		/// 采购金额
		/// </summary>
		public decimal? M_Return_RealMoney
		{
			set{ _m_return_realmoney=value;}
			get{return _m_return_realmoney;}
		}
		/// <summary>
		/// 退库备注
		/// </summary>
		public string M_ReturnDetail_Memo
		{
			set{ _m_returndetail_memo=value;}
			get{return _m_returndetail_memo;}
		}
		#endregion Model

	}
}

