﻿Imports System.Data.SqlClient
Imports HisControl
Imports Stimulsoft.Report

Public Class Cw_Cx_MzZy_Rj3

    Dim My_Cm As CurrencyManager             '同步指针
    Dim My_View As New DataView
    Public V_Str1 As String

#Region "传参"

    'Dim Rform As Base
    Dim Rrb1 As Boolean
    Dim Rrb2 As RadioButton
    Dim Rcombo As C1.Win.C1List.C1Combo
    Dim Rtdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Dim Rrow As DataRow
    Dim Rds As DataSet
    '----------------------------------------------------

#End Region

    Public Sub New(ByVal tform As Base, ByVal trb1 As Boolean, ByVal ttdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid, ByVal tDs As DataSet)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        'Rform = tform
        Rtdbgrid = ttdbgrid
        Rrb1 = trb1
        Rds = tDs
    End Sub

    '窗体加载事件
    Private Sub Cw_Cx_MzZy_Rj3_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

        'Rform.Visible = False

        Call C1TrueDBGrid1_OnInit()
        Call Init_Data()
        Call F_Sum()
        If Rrb1 = True Then
            Me.Text = Rtdbgrid.Columns("Jsr_Name").Value & "日结单明细"
        Else
            Me.Text = "日结单明细"
        End If


    End Sub


#Region "控件__动作"

    '查询文本框内容发生改变的事件
    Private Sub C1TextBox1_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1TextBox1.TextChanged
        My_View = My_Cm.List

        Dim V_RowFilter As String = ""

        If Trim(Me.C1TextBox1.Text & "") = "" Then
            V_RowFilter = ""
        Else
            Select Case Me.C1Combo1.Text
                Case "医生姓名"
                    V_RowFilter = "Ys_Name Like '%" + C1TextBox1.Text + "%'"
                Case "患者类别"
                    V_RowFilter = "Bxlb_Name Like '%" + C1TextBox1.Text + "%'"
                Case "药品名称"
                    V_RowFilter = "Yp_Name Like '%" + C1TextBox1.Text + "%'"
            End Select
        End If

        My_View.RowFilter = V_RowFilter

        Call F_Sum()
    End Sub

    '关闭窗体事件
    Private Sub Cw_Cx_Mz_Rj3_FormClosed(ByVal sender As System.Object, ByVal e As System.Windows.Forms.FormClosedEventArgs) Handles MyBase.FormClosed
        'Rform.Visible = True
        C1TextBox1.Text = ""
    End Sub

#End Region

#Region "自定义方法"

    '数据
    Private Sub Init_Data()

        'C1TrueDBGrid1的初始化
        If Rrb1 = True Then
            If Rtdbgrid.Columns("V_Lb").Value = "门诊" Then
                V_Str1 = "select Ks_Name,Ys_Name,Ry_Name,Bxlb_Name,Yp_Name,Mz_Lb,Mz_Date,Mz_Time,Mz_Sl,Mz_Yp_Sum.Mz_Money as M from Mz_Sum,Mz_Yp_Sum,V_YpKc,Zd_Bxlb,Zd_Yyys,Zd_YyKs  where Mz_Sum.Ks_Code=Zd_YyKs.Ks_Code and Mz_Sum.Ys_Code=Zd_Yyys.Ys_Code and Mz_Sum.Bxlb_Code=Zd_Bxlb.Bxlb_Code and Mz_Print=1 and Mz_Sum.Yy_Code='" & HisVar.HisVar.WsyCode & "' and Mz_Sum.Jz_Code='" & Rtdbgrid.Columns("Jz_Code").Value & "' and Mz_Sum.Mz_Code=Mz_Yp_Sum.Mz_Code and Mz_Yp_Sum.Xx_code=V_YpKc.Xx_code And Mz_Ph Not In (Select Mz_Ph From Mz_Ty_Sum Where Jz_Code='" & Rtdbgrid.Columns("Jz_Code").Value & "') " & _
              " union all select Ks_Name,Ys_Name,Ry_Name,Bxlb_Name,Xm_Name,Mz_Lb,Mz_Date,Mz_Time,Mz_Sl,Mz_Xm_Sum.Mz_Money as M  from Mz_Sum,Mz_Xm_Sum,Zd_Ml_Xm3,Zd_Bxlb,Zd_Yyys,Zd_YyKs  where Mz_Sum.Ks_Code=Zd_YyKs.Ks_Code and Mz_Sum.Ys_Code=Zd_Yyys.Ys_Code and Mz_Sum.Bxlb_Code=Zd_Bxlb.Bxlb_Code and Mz_Print=1  and Mz_Sum.Yy_Code='" & HisVar.HisVar.WsyCode & "' and Mz_Sum.Jz_Code='" & Rtdbgrid.Columns("Jz_Code").Value & "'and Mz_Sum.Mz_Code=Mz_Xm_Sum.Mz_Code and Mz_Xm_Sum.Xm_Code=Zd_Ml_Xm3.Xm_Code And Mz_Ph Not In (Select Mz_Ph From Mz_Ty_Sum Where Jz_Code='" & Rtdbgrid.Columns("Jz_Code").Value & "') order by Ys_Name,Bxlb_Name,Ry_Name"
            ElseIf Rtdbgrid.Columns("V_Lb").Value = "住院" Then
                V_Str1 = "select Ks_Name,Ys_Name,Ry_Name,Bxlb_Name,Yp_Name,Cf_Lb as Mz_Lb,Cf_Date as Mz_Date,Cf_Time As Mz_Time,Cf_Sl as Mz_Sl,Bl_CfYp.Cf_Money  as M from Bl,Bl_Cf,Bl_CfYp,V_YpKc,Zd_Bxlb,Zd_Yyys,Zd_YyKs where BL_Cf.Ks_Code=Zd_YyKs.Ks_Code and BL_Cf.Ys_Code=Zd_Yyys.Ys_Code and  Bl.Bl_Code=Bl_Cf.Bl_Code and Bl.Bxlb_Code=Zd_Bxlb.Bxlb_Code and Bl_Cf.Yy_Code='" & HisVar.HisVar.WsyCode & "' and Bl_Cf.Jz_Code='" & Rtdbgrid.Columns("Jz_Code").Value & "' and Bl_Cf.Cf_Code=Bl_CfYP.Cf_Code and Bl_CfYP.Xx_code=V_YpKc.Xx_code " & _
              " union all select Ks_Name,Ys_Name,Ry_Name,Bxlb_Name,Xm_Name,Cf_Lb as Mz_Lb,Cf_Date as Mz_Date,Cf_Time as Mz_Time,Cf_Sl as Mz_Sl,Bl_CfXm.Cf_Money  as M from Bl,Bl_Cf,Bl_CfXm,Zd_Ml_Xm3,Zd_Bxlb,Zd_Yyys,Zd_YyKs where BL_Cf.Ks_Code=Zd_YyKs.Ks_Code and BL_Cf.Ys_Code=Zd_Yyys.Ys_Code and Bl.Bl_Code=Bl_Cf.Bl_Code and Bl.Bxlb_Code=Zd_Bxlb.Bxlb_Code  and Bl_Cf.Yy_Code='" & HisVar.HisVar.WsyCode & "' and Bl_Cf.Jz_Code='" & Rtdbgrid.Columns("Jz_Code").Value & "' and Bl_Cf.Cf_Code=Bl_CfXm.Cf_Code and Bl_CfXm.Xm_Code=Zd_Ml_Xm3.Xm_Code order by Ys_Name,Bxlb_Name,Ry_Name"

            End If
            HisVar.HisVar.Sqldal.QueryDt(Rds, V_Str1, "日结明细", True)
        Else

        End If

        With C1TrueDBGrid1
            My_Cm = CType(BindingContext(Rds, "日结明细"), CurrencyManager)
            .SetDataBinding(Rds, "日结明细", True)



            My_View = My_Cm.List
        End With

        'C1Combo1的初始化
        Dim My_Combo1 As New BaseClass.C_Combo1(Me.C1Combo1)
        My_Combo1.Init_TDBCombo()
        With C1Combo1

            .AddItem("医生姓名")
            .AddItem("患者类别")
            .AddItem("药品名称")
            .SelectedIndex = 0
            .Width = 124
            .DropDownWidth = 124
        End With
    End Sub

    '根据条件添加C1TrueDBGrid1
    Private Sub C1TrueDBGrid1_OnInit()

        Dim My_Grid As New BaseClass.C_Grid(Me.C1TrueDBGrid1)
        With My_Grid
            .Init_Grid()
            .Init_Column("医生姓名", "Ys_Name", 70, "中", "")
            .Init_Column("患者类别", "Bxlb_Name", 70, "中", "")
            .Init_Column("患者姓名", "Ry_Name", 70, "中", "")
            .Init_Column("药品名称", "Yp_Name", 200, "左", "")
            .Init_Column("处方日期", "Mz_Date", 80, "中", "yyyy-MM-dd")
            .Init_Column("处方时间", "Mz_Time", 80, "中", "HH:mm:ss")
            .Init_Column("数量", "Mz_Sl", 70, "右", "###0.00")
            .Init_Column("金额", "M", 50, "右", "###0.00")
        End With
        C1TrueDBGrid1.HScrollBar.Style = C1.Win.C1TrueDBGrid.ScrollBarStyleEnum.Automatic
        C1TrueDBGrid1.Splits(0).DisplayColumns("Ys_Name").Merge = C1.Win.C1TrueDBGrid.ColumnMergeEnum.Free
        C1TrueDBGrid1.Splits(0).DisplayColumns("Bxlb_Name").Merge = C1.Win.C1TrueDBGrid.ColumnMergeEnum.Free
        C1TrueDBGrid1.Splits(0).DisplayColumns("Ry_Name").Merge = C1.Win.C1TrueDBGrid.ColumnMergeEnum.Free
        C1TrueDBGrid1.Splits(0).DisplayColumns("Yp_Name").Merge = C1.Win.C1TrueDBGrid.ColumnMergeEnum.Free
        C1TrueDBGrid1.Splits(0).DisplayColumns("Ys_Name").Style.VerticalAlignment = C1.Win.C1TrueDBGrid.AlignVertEnum.Center
        C1TrueDBGrid1.Splits(0).DisplayColumns("Bxlb_Name").Style.VerticalAlignment = C1.Win.C1TrueDBGrid.AlignVertEnum.Center
        C1TrueDBGrid1.Splits(0).DisplayColumns("Ry_Name").Style.VerticalAlignment = C1.Win.C1TrueDBGrid.AlignVertEnum.Center
        C1TrueDBGrid1.Splits(0).DisplayColumns("Yp_Name").Style.VerticalAlignment = C1.Win.C1TrueDBGrid.AlignVertEnum.Center
        C1TrueDBGrid1.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightCell

    End Sub

#End Region

    Public Overrides Sub F_Sum()
        Dim Sum1 As Double = 0
        Sum1 = IIf(My_View.Table.Compute("Sum(M)", My_View.RowFilter) Is DBNull.Value, 0, My_View.Table.Compute("Sum(M)", My_View.RowFilter))

        With C1TrueDBGrid1
            .ColumnFooters = True
            .Columns(7).FooterText = Format(Sum1, "###0.00")
        End With

    End Sub

    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click
        Dim StiRpt As New StiReport
        StiRpt.Load("Rpt\科室日报表.mrt")
        StiRpt.ReportName = "科室日报表"
        StiRpt.RegData(Rds.Tables("日结明细"))
        StiRpt.Compile()
        StiRpt("统计人") = HisVar.HisVar.JsrName
        StiRpt("打印时间") = "打印时间:" & Format(Now, "yyyy-MM-dd HH:mm")
        '     StiRpt.Design()
        StiRpt.Show()
    End Sub
End Class