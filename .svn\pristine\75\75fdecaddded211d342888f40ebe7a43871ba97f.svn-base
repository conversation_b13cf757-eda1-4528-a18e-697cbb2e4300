﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="0" />
    <DataSources isList="true" count="1">
      <明细表 Ref="2" type="DataTableSource" isKey="true">
        <Alias>明细表</Alias>
        <Columns isList="true" count="4">
          <value>Cf_Sl,System.Decimal</value>
          <value>Cf_Money,System.Decimal</value>
          <value>Cf_Lb,System.String</value>
          <value>Bl_Code,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>明细表</Name>
        <NameInSource>明细表</NameInSource>
      </明细表>
    </DataSources>
    <Relations isList="true" count="0" />
    <Report isRef="0" />
    <Variables isList="true" count="26">
      <value>,姓名,姓名,System.String,,False,False</value>
      <value>,性别,性别,System.String,,False,False</value>
      <value>,付款方式,付款方式,System.String,,False,False</value>
      <value>,社保号码,社保号码,System.String,,False,False</value>
      <value>,业务流水号,业务流水号,System.String,,False,False</value>
      <value>,缴费日期,缴费日期,System.String,,False,False</value>
      <value>,合计大写,合计大写,System.String,,False,False</value>
      <value>,合计,合计,System.String,,False,False</value>
      <value>,医保统筹支付,医保统筹支付,System.String,,False,False</value>
      <value>,个人账户支付,个人账户支付,System.String,,False,False</value>
      <value>,其他医保支付,其他医保支付,System.String,,False,False</value>
      <value>,个人支付金额,个人支付金额,System.String,,False,False</value>
      <value>,报销类别,报销类别,System.String,,False,False</value>
      <value>,医疗机构,医疗机构,System.String,,False,False</value>
      <value>,类别,类别,System.String,,False,False</value>
      <value>,流水号,流水号,System.String,,False,False</value>
      <value>,住院号,住院号,System.String,,False,False</value>
      <value>,医保类型,医保类型,System.String,,False,False</value>
      <value>,预缴金额,预缴金额,System.String,,False,False</value>
      <value>,补缴金额,补缴金额,System.String,,False,False</value>
      <value>,退费金额,退费金额,System.String,,False,False</value>
      <value>,医院垫支,医院垫支,System.String,,False,False</value>
      <value>,住院时间,住院时间,System.String,,False,False</value>
      <value>,住院天数,住院天数,System.String,,False,False</value>
      <value>,收款人,收款人,System.String,,False,False</value>
      <value>,打印时间,打印时间,System.String,,False,False</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="3" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="4">
        <Text27 Ref="4" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>6.8,24.7,1.3,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Microsoft Sans Serif,8.25,Regular,Point,False,134</Font>
          <Guid>f6b2f24201c9450bb6e8dd54b4181fda</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text27</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Text>金额</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Bottom</VertAlignment>
        </Text27>
        <ReportTitleBand1 Ref="5" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,16.5,2.1</ClientRectangle>
          <Components isList="true" count="13">
            <Text120 Ref="6" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,16.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text120</Name>
              <Page isRef="3" />
              <Parent isRef="5" />
              <TextBrush>Black</TextBrush>
            </Text120>
            <Text48 Ref="7" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.5,0.5,3.2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>c4df1a87ff2a4431849721a320e0d39f</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text48</Name>
              <Page isRef="3" />
              <Parent isRef="5" />
              <Text>{流水号}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text48>
            <Text91 Ref="8" type="Text" isKey="true">
              <Border>None;Black;1;Solid;False;4;Transparent</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>12.6,1,1.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>81085eea8f3142acb9aeae50f7951d36</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text91</Name>
              <Page isRef="3" />
              <Parent isRef="5" />
              <Text>{住院天数}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text91>
            <Text8 Ref="9" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.5,0.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>829031bdf8e14ea1bae2cbc7f5fe7db6</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text8</Name>
              <Page isRef="3" />
              <Parent isRef="5" />
              <Text>姓名：
</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text8>
            <Text9 Ref="10" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0.8,1.5,1.9,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>75cc88a2e1a3443fa51013c4c49727c6</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text9</Name>
              <Page isRef="3" />
              <Parent isRef="5" />
              <Text>{姓名}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text9>
            <Text13 Ref="11" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.7,1.5,1.1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>4a15957ed78c4ca2b01dbd0d381f8396</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text13</Name>
              <Page isRef="3" />
              <Parent isRef="5" />
              <Text>{性别}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text13>
            <Text17 Ref="12" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>6.1,1.5,3.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>a5304f06bfa64e7791b12d1843e1feea</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text17</Name>
              <Page isRef="3" />
              <Parent isRef="5" />
              <Text>{社保号码}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text17>
            <Text11 Ref="13" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.4,1.5,1.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>1ffb683024fb459f99d35cb67832aa97</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text11</Name>
              <Page isRef="3" />
              <Parent isRef="5" />
              <Text>{医保类型}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text11>
            <Text70 Ref="14" type="Text" isKey="true">
              <Border>None;Black;1;Solid;False;4;Transparent</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6.9,1,3.8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>08db345d1df64cb6a90012292adc6bd5</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text70</Name>
              <Page isRef="3" />
              <Parent isRef="5" />
              <Text>{住院时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text70>
            <Text37 Ref="15" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1,5.4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>3f6776d1eeae42ad8f7c82500526e3c0</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text37</Name>
              <Page isRef="3" />
              <Parent isRef="5" />
              <Text>{医疗机构}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text37>
            <Text1 Ref="16" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.5,1.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>88e3930ff4e9421ba97732a48c4b7416</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text1</Name>
              <Page isRef="3" />
              <Parent isRef="5" />
              <Text>流水号：
</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text1>
            <Text2 Ref="17" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>5.4,1,1.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>7dab647c38b74a5ea4fe0d4e0dc913d2</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text2</Name>
              <Page isRef="3" />
              <Parent isRef="5" />
              <Text>住院时间：
</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text2>
            <Text7 Ref="18" type="Text" isKey="true">
              <Border>None;Black;1;Solid;False;4;Transparent</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>10.9,1,1.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>64c8f2a81811483da45d601879dfc839</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text7</Name>
              <Page isRef="3" />
              <Parent isRef="5" />
              <Text>住院天数：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text7>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>ReportTitleBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </ReportTitleBand1>
        <DataBand1 Ref="19" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,3.3,16.5,0.5</ClientRectangle>
          <ColumnDirection>DownThenAcross</ColumnDirection>
          <Columns>3</Columns>
          <Components isList="true" count="3">
            <Text29 Ref="20" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.6,0,1.85,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>3b3a939e7b59422592930d597aecdf53</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text29</Name>
              <NullValue>   </NullValue>
              <Page isRef="3" />
              <Parent isRef="19" />
              <Text>{明细表.Cf_Money}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="21" type="CustomFormat" isKey="true">
                <StringFormat>0.00##</StringFormat>
              </TextFormat>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text29>
            <Text30 Ref="22" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.45,0,1.05,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>47a3e0c9e6ff4fdf84a2b5a4a22b4792</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text30</Name>
              <Page isRef="3" />
              <Parent isRef="19" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text30>
            <Text5 Ref="23" type="Text" isKey="true">
              <Border>None;Black;1;Solid;False;4;Transparent</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>073ce9d0318c4441a9e00a1be3eee141</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text5</Name>
              <Page isRef="3" />
              <Parent isRef="19" />
              <Text>{明细表.Cf_Lb}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text5>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>明细表</DataSourceName>
          <Filters isList="true" count="0" />
          <Name>DataBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Sort isList="true" count="0" />
        </DataBand1>
        <ReportSummaryBand1 Ref="24" type="ReportSummaryBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,4.6,16.5,2.5</ClientRectangle>
          <Components isList="true" count="13">
            <Text41 Ref="25" type="Text" isKey="true">
              <Border>None;Black;1;Solid;False;4;Transparent</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.4,2.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>ca9ff5c8e609486d8c65e1bb9c0bdc65</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text41</Name>
              <Page isRef="3" />
              <Parent isRef="24" />
              <Text>合计（大写）：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text41>
            <Text43 Ref="26" type="Text" isKey="true">
              <Border>None;Black;1;Solid;False;4;Transparent</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.7,0.4,5.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>df7e1c21ecee46189f8b831a19e94f1a</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text43</Name>
              <Page isRef="3" />
              <Parent isRef="24" />
              <Text>{合计大写}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text43>
            <Text44 Ref="27" type="Text" isKey="true">
              <Border>None;Black;1;Solid;False;4;Transparent</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>8.4,0.4,0.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>ed8958f943cb4257811635fcd566c997</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text44</Name>
              <Page isRef="3" />
              <Parent isRef="24" />
              <Text>￥</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text44>
            <Text49 Ref="28" type="Text" isKey="true">
              <Border>None;Black;1;Solid;False;4;Transparent</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9,0.4,7.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>70b2dfa01af44df192242a5e6e3e6542</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text49</Name>
              <Page isRef="3" />
              <Parent isRef="24" />
              <Text>{合计}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text49>
            <Text4 Ref="29" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.3,0.9,1.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>4f3a2f20e6c14ffb8df4a9ac7c3ec167</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text4</Name>
              <Page isRef="3" />
              <Parent isRef="24" />
              <Text>补缴金额：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text4>
            <Text23 Ref="30" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.6,0.9,2.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text23</Name>
              <Page isRef="3" />
              <Parent isRef="24" />
              <Text>{预缴金额}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text23>
            <Text31 Ref="31" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>9.3,0.9,1.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>add52a44b2d54ba49aa1101a92cf934c</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text31</Name>
              <Page isRef="3" />
              <Parent isRef="24" />
              <Text>退费金额：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text31>
            <Text32 Ref="32" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>5.9,0.9,2.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>9c8c6a1cb0b2441b9738e01770b97352</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text32</Name>
              <Page isRef="3" />
              <Parent isRef="24" />
              <Text>{补缴金额}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text32>
            <Text34 Ref="33" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>10.9,0.9,1.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>2b4fdcdffaf24e4d81a720d1bc0f8d0e</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text34</Name>
              <Page isRef="3" />
              <Parent isRef="24" />
              <Text>{退费金额}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text34>
            <Text50 Ref="34" type="Text" isKey="true">
              <Border>None;Black;1;Solid;False;4;Transparent</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.6,1.8,3,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,7.5,Regular,Point,False,134</Font>
              <Guid>f642494222454374a841fff12eb01fe3</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text50</Name>
              <Page isRef="3" />
              <Parent isRef="24" />
              <Text>{收款人}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text50>
            <Text52 Ref="35" type="Text" isKey="true">
              <Border>None;Black;1;Solid;False;4;Transparent</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>10.6,1.8,4.7,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,7.5,Regular,Point,False,134</Font>
              <Guid>cf785cd2ef2c4b9d9a39437a1248dce7</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text52</Name>
              <Page isRef="3" />
              <Parent isRef="24" />
              <Text>{打印时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text52>
            <Text3 Ref="36" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.9,1.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text3</Name>
              <Page isRef="3" />
              <Parent isRef="24" />
              <Text>  预缴金额：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text3>
            <Text10 Ref="37" type="Text" isKey="true">
              <Border>None;Black;1;Solid;False;4;Transparent</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6.1,1.8,1.5,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,7.5,Regular,Point,False,134</Font>
              <Guid>c840f0efd4684a129e85f1f74ee788aa</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text10</Name>
              <Page isRef="3" />
              <Parent isRef="24" />
              <Text>收款人</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text10>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>ReportSummaryBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </ReportSummaryBand1>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>20c1288e9da6491e858185f416cfb06d</Guid>
      <Margins>1.3,1.2,0.78740157480315,0</Margins>
      <Name>Page1</Name>
      <PageHeight>10.13</PageHeight>
      <PageWidth>19</PageWidth>
      <Report isRef="0" />
      <Watermark Ref="38" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page1>
  </Pages>
  <PrinterSettings Ref="39" type="Stimulsoft.Report.Print.StiPrinterSettings" isKey="true" />
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>辽宁省住院收费票据</ReportAlias>
  <ReportChanged>1/13/2015 5:15:23 PM</ReportChanged>
  <ReportCreated>12/21/2012 9:16:47 AM</ReportCreated>
  <ReportFile>.\Rpt\辽宁省住院收费票据老版.mrt</ReportFile>
  <ReportGuid>4d2fc4de0b8d4b6ba945e5693554aa7e</ReportGuid>
  <ReportName>辽宁省住院收费票据</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2011.2.1026</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class Report : Stimulsoft.Report.StiReport
    {
        public Report()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
        #endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>