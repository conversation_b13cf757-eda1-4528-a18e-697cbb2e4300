﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="0" />
    <DataSources isList="true" count="3">
      <出院诊断信息右 Ref="2" type="DataTableSource" isKey="true">
        <Alias>出院诊断信息右</Alias>
        <Columns isList="true" count="3">
          <value>DIAGNOSIS_NAME,System.String</value>
          <value>DIAGNOSIS_CD,System.String</value>
          <value>DIAG_RESULT_CODE,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>出院诊断信息右</Name>
        <NameInSource>出院诊断信息右</NameInSource>
      </出院诊断信息右>
      <出院诊断信息左 Ref="3" type="DataTableSource" isKey="true">
        <Alias>出院诊断信息左</Alias>
        <Columns isList="true" count="3">
          <value>DIAGNOSIS_NAME,System.String</value>
          <value>DIAGNOSIS_CD,System.String</value>
          <value>DIAG_RESULT_CODE,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>出院诊断信息左</Name>
        <NameInSource>出院诊断信息左</NameInSource>
      </出院诊断信息左>
      <手术信息 Ref="4" type="DataTableSource" isKey="true">
        <Alias>手术信息</Alias>
        <Columns isList="true" count="10">
          <value>OPERATION_CODE,System.String</value>
          <value>OPERATION_DTIME,System.DateTime</value>
          <value>Operation_Scale_Name,System.String</value>
          <value>OPERATION_NAME,System.String</value>
          <value>SURGEON_NAME,System.String</value>
          <value>ASSISTANT1_NAME,System.String</value>
          <value>ASSISTANT2_NAME,System.String</value>
          <value>INCISION_HEALING_Name,System.String</value>
          <value>ANESTHESIA_Name,System.String</value>
          <value>ANESTHESIOLOGIST_NAME,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>手术信息</Name>
        <NameInSource>手术信息</NameInSource>
      </手术信息>
    </DataSources>
    <Relations isList="true" count="0" />
    <Report isRef="0" />
    <Variables isList="true" count="109">
      <value>,医疗机构,医疗机构,System.String,,False,False</value>
      <value>,组织机构代码,组织机构代码,System.String,,False,False</value>
      <value>,医疗付费方式,医疗付费方式,System.String,,False,False</value>
      <value>,病历号,病历号,System.String,,False,False</value>
      <value>,姓名,姓名,System.String,,False,False</value>
      <value>,性别,性别,System.String,,False,False</value>
      <value>,出生日期,出生日期,System.String,,False,False</value>
      <value>,年龄,年龄,System.String,,False,False</value>
      <value>,国籍,国籍,System.String,,False,False</value>
      <value>,年龄不足一周岁的年龄,年龄不足一周岁的年龄,System.String,,False,False</value>
      <value>,新生儿出生体重,新生儿出生体重,System.String,,False,False</value>
      <value>,新生儿入院体重,新生儿入院体重,System.String,,False,False</value>
      <value>,出生地,出生地,System.String,,False,False</value>
      <value>,籍贯,籍贯,System.String,,False,False</value>
      <value>,民族,民族,System.String,,False,False</value>
      <value>,身份证号,身份证号,System.String,,False,False</value>
      <value>,职业,职业,System.String,,False,False</value>
      <value>,婚姻,婚姻,System.String,,False,False</value>
      <value>,现住址,现住址,System.String,,False,False</value>
      <value>,电话,电话,System.String,,False,False</value>
      <value>,邮编,邮编,System.String,,False,False</value>
      <value>,户口地址,户口地址,System.String,,False,False</value>
      <value>,户口邮编,户口邮编,System.String,,False,False</value>
      <value>,工作单位及地址,工作单位及地址,System.String,,False,False</value>
      <value>,单位电话,单位电话,System.String,,False,False</value>
      <value>,单位邮编,单位邮编,System.String,,False,False</value>
      <value>,联系人姓名,联系人姓名,System.String,,False,False</value>
      <value>,关系,关系,System.String,,False,False</value>
      <value>,地址,地址,System.String,,False,False</value>
      <value>,联系电话,联系电话,System.String,,False,False</value>
      <value>,入院途径,入院途径,System.String,,False,False</value>
      <value>,入院时间,入院时间,System.String,,False,False</value>
      <value>,入院科别,入院科别,System.String,,False,False</value>
      <value>,入院病房,入院病房,System.String,,False,False</value>
      <value>,转科科别,转科科别,System.String,,False,False</value>
      <value>,出院时间,出院时间,System.String,,False,False</value>
      <value>,出院科别,出院科别,System.String,,False,False</value>
      <value>,出院病房,出院病房,System.String,,False,False</value>
      <value>,实际住院天数,实际住院天数,System.String,,False,False</value>
      <value>,门诊或急诊诊断,门诊或急诊诊断,System.String,,False,False</value>
      <value>,疾病编码,疾病编码,System.String,,False,False</value>
      <value>,损伤或中毒的外部原因,损伤或中毒的外部原因,System.String,,False,False</value>
      <value>,其疾病编码,其疾病编码,System.String,,False,False</value>
      <value>,病理诊断,病理诊断,System.String,,False,False</value>
      <value>,病理疾病编码,病理疾病编码,System.String,,False,False</value>
      <value>,病理号,病理号,System.String,,False,False</value>
      <value>,药物过敏,药物过敏,System.String,,False,False</value>
      <value>,死亡患者尸检,死亡患者尸检,System.String,,False,False</value>
      <value>,血型,血型,System.String,,False,False</value>
      <value>,RH,RH,System.String,,False,False</value>
      <value>,科主任,科主任,System.String,,False,False</value>
      <value>,主任或副主任医师,主任或副主任医师,System.String,,False,False</value>
      <value>,主治医师,主治医师,System.String,,False,False</value>
      <value>,住院医师,住院医师,System.String,,False,False</value>
      <value>,责任护士,责任护士,System.String,,False,False</value>
      <value>,进修医师,进修医师,System.String,,False,False</value>
      <value>,实习医师,实习医师,System.String,,False,False</value>
      <value>,编码员,编码员,System.String,,False,False</value>
      <value>,病案质量,病案质量,System.String,,False,False</value>
      <value>,质控医师,质控医师,System.String,,False,False</value>
      <value>,质控护士,质控护士,System.String,,False,False</value>
      <value>,质控日期,质控日期,System.String,,False,False</value>
      <value>,离院方式,离院方式,System.String,,False,False</value>
      <value>,拟接收医疗机构名称,拟接收医疗机构名称,System.String,,False,False</value>
      <value>,是否有出院31天内再住院计划,是否有出院31天内再住院计划,System.String,,False,False</value>
      <value>,入院前,入院前,System.String,,False,False</value>
      <value>,入院后,入院后,System.String,,False,False</value>
      <value>,总费用,总费用,System.String,,False,False</value>
      <value>,自付金额,自付金额,System.String,,False,False</value>
      <value>,一般医疗服务费,一般医疗服务费,System.String,,False,False</value>
      <value>,一般治疗操作费,一般治疗操作费,System.String,,False,False</value>
      <value>,护理费,护理费,System.String,,False,False</value>
      <value>,其他费用,其他费用,System.String,,False,False</value>
      <value>,病理诊断费,病理诊断费,System.String,,False,False</value>
      <value>,实验室诊断费,实验室诊断费,System.String,,False,False</value>
      <value>,影像学诊断费,影像学诊断费,System.String,,False,False</value>
      <value>,临床诊断项目费,临床诊断项目费,System.String,,False,False</value>
      <value>,非手术治疗项目费,非手术治疗项目费,System.String,,False,False</value>
      <value>,临床物理治疗费,临床物理治疗费,System.String,,False,False</value>
      <value>,手术治疗费,手术治疗费,System.String,,False,False</value>
      <value>,麻醉费,麻醉费,System.String,,False,False</value>
      <value>,手术费,手术费,System.String,,False,False</value>
      <value>,康复费,康复费,System.String,,False,False</value>
      <value>,中医治疗费,中医治疗费,System.String,,False,False</value>
      <value>,西药费,西药费,System.String,,False,False</value>
      <value>,抗菌药物费用,抗菌药物费用,System.String,,False,False</value>
      <value>,中成药费,中成药费,System.String,,False,False</value>
      <value>,中草药费,中草药费,System.String,,False,False</value>
      <value>,血费,血费,System.String,,False,False</value>
      <value>,白蛋白类制品费,白蛋白类制品费,System.String,,False,False</value>
      <value>,球蛋白类制品费,球蛋白类制品费,System.String,,False,False</value>
      <value>,凝血因子类制品费,凝血因子类制品费,System.String,,False,False</value>
      <value>,细胞因子类制品费,细胞因子类制品费,System.String,,False,False</value>
      <value>,检查用一次性医用材料费,检查用一次性医用材料费,System.String,,False,False</value>
      <value>,治疗用一次性医用材料费,治疗用一次性医用材料费,System.String,,False,False</value>
      <value>,手术用一次性医用材料费,手术用一次性医用材料费,System.String,,False,False</value>
      <value>,其他费,其他费,System.String,,False,False</value>
      <value>,目的,目的,System.String,,False,False</value>
      <value>,抗菌药物使用情况,抗菌药物使用情况,System.String,,False,False</value>
      <value>,输液反应,输液反应,System.String,,False,False</value>
      <value>,引发反应药物,引发反应药物,System.String,,False,False</value>
      <value>,临床表现,临床表现,System.String,,False,False</value>
      <value>,住院有无坠床或跌倒及伤害程度,住院有无坠床或跌倒及伤害程度,System.String,,False,False</value>
      <value>,跌倒或坠床原因,跌倒或坠床原因,System.String,,False,False</value>
      <value>,医院感染情况,医院感染情况,System.String,,False,False</value>
      <value>,感染部位,感染部位,System.String,,False,False</value>
      <value>,医院感染名称,医院感染名称,System.String,,False,False</value>
      <value>,住院次数,住院次数,System.String,,False,False</value>
      <value>,健康卡号,健康卡号,System.String,,False,False</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="4">
    <Page1 Ref="5" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="3">
        <ReportTitleBand1 Ref="6" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,19,10.8</ClientRectangle>
          <Components isList="true" count="81">
            <Text3 Ref="7" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,11,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,12,Bold</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text3</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>医疗机构：{医疗机构}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text3>
            <Text2 Ref="8" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>11,0,8,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,12,Bold</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text2</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>组织机构代码：{组织机构代码}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text2>
            <Text42 Ref="9" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1,15.2,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text42</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>医疗付费方式：{医疗付费方式}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text42>
            <Text43 Ref="10" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>15.2,2.2,3.8,0.9</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text43</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>病历号：{病历号}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text43>
            <Text44 Ref="11" type="Text" isKey="true">
              <Border>Top, Left;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,3.1,1,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text44</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>姓名：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text44>
            <Text46 Ref="12" type="Text" isKey="true">
              <Border>Top;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.6,3.1,1,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text46</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>性别：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text46>
            <Text45 Ref="13" type="Text" isKey="true">
              <Border>Top;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.4,3.1,1.6,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text45</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>出生日期：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text45>
            <Text47 Ref="14" type="Text" isKey="true">
              <Border>Top;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>11.6,3.1,1,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text47</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>年龄：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text47>
            <Text49 Ref="15" type="Text" isKey="true">
              <Border>Top;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15.2,3.1,1,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text49</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>国籍：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text49>
            <Text54 Ref="16" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7.4,3.8,2.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text54</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>新生儿出生体重：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text54>
            <Text56 Ref="17" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>11.6,3.8,3,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text56</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>新生儿入院体重：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text56>
            <Text58 Ref="18" type="Text" isKey="true">
              <Border>Left;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,4.5,1.4,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text58</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>出生地：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text58>
            <Text60 Ref="19" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7.4,4.5,0.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text60</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>籍贯：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text60>
            <Text62 Ref="20" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>15.2,4.5,1,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text62</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>民族：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text62>
            <Text64 Ref="21" type="Text" isKey="true">
              <Border>Left;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,5.2,1.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text64</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>身份证号：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text64>
            <Text66 Ref="22" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7.4,5.2,0.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text66</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>职业：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text66>
            <Text67 Ref="23" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>15.2,5.2,1,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text67</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>婚姻：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text67>
            <Text69 Ref="24" type="Text" isKey="true">
              <Border>Left;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,5.9,1.4,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text69</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>现住址：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text69>
            <Text5 Ref="25" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>11.6,5.9,1,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text5</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>电话：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text5>
            <Text71 Ref="26" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>15.2,5.9,1,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text71</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>邮编：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text71>
            <Text73 Ref="27" type="Text" isKey="true">
              <Border>Left;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,6.6,1.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text73</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>户口地址：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text73>
            <Text75 Ref="28" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>15.2,6.6,1.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text75</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>户口邮编：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text75>
            <Text77 Ref="29" type="Text" isKey="true">
              <Border>Left;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,7.3,3,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text77</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>工作单位及地址：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text77>
            <Text79 Ref="30" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>11.6,7.3,1.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text79</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>单位电话：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text79>
            <Text81 Ref="31" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>15.2,7.3,1.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text81</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>单位邮编：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text81>
            <Text83 Ref="32" type="Text" isKey="true">
              <Border>Left;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,8,2.2,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text83</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>联系人姓名：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text83>
            <Text85 Ref="33" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>3.6,8,1,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text85</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>关系：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text85>
            <Text87 Ref="34" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7.4,8,0.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text87</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>地址：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text87>
            <Text89 Ref="35" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>15.2,8,1.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text89</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>联系电话：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text89>
            <Text48 Ref="36" type="Text" isKey="true">
              <Border>Left;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,8.7,1.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text48</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>入院途径:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text48>
            <Text50 Ref="37" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7.4,8.7,1.6,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text50</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>入院时间:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text50>
            <Text55 Ref="38" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>11.6,8.7,1.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text55</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>入院科别:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text55>
            <Text59 Ref="39" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>15.2,8.7,1.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text59</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>入院病房:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text59>
            <Text63 Ref="40" type="Text" isKey="true">
              <Border>Left;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,9.4,1.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text63</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>转科科别:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text63>
            <Text68 Ref="41" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>3.6,9.4,1.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text68</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>出院时间:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text68>
            <Text72 Ref="42" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7.4,9.4,1.6,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text72</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>出院科别:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text72>
            <Text76 Ref="43" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>11.6,9.4,1.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text76</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>出院病房:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text76>
            <Text80 Ref="44" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>15.2,9.4,2.6,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text80</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>实际住院天数:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text80>
            <Text88 Ref="45" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>11.6,10.1,1.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text88</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>疾病编码：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text88>
            <Text52 Ref="46" type="Text" isKey="true">
              <Border>Left;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,3.8,4,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text52</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>年龄不足一周岁的年龄:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text52>
            <Text65 Ref="47" type="Text" isKey="true">
              <Border>Left;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,10.1,3,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text65</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>门诊或急诊诊断:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text65>
            <Text236 Ref="48" type="Text" isKey="true">
              <Border>Top, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1,3.1,2.6,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text236</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>{姓名}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text236>
            <Text237 Ref="49" type="Text" isKey="true">
              <Border>Top, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.6,3.1,2.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text237</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>{性别}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text237>
            <Text238 Ref="50" type="Text" isKey="true">
              <Border>Top, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9,3.1,2.6,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text238</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>{出生日期}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text238>
            <Text239 Ref="51" type="Text" isKey="true">
              <Border>Top, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>12.6,3.1,2.6,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text239</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>{年龄}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text239>
            <Text240 Ref="52" type="Text" isKey="true">
              <Border>Top, Right, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>16.2,3.1,2.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text240</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>{国籍}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text240>
            <Text241 Ref="53" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4,3.8,3.4,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text241</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>{年龄不足一周岁的年龄}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text241>
            <Text242 Ref="54" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>10.2,3.8,1.4,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text242</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>{新生儿出生体重}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text242>
            <Text243 Ref="55" type="Text" isKey="true">
              <Border>Right, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>14.6,3.8,4.4,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text243</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>{新生儿入院体重}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text243>
            <Text244 Ref="56" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.4,4.5,6,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text244</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>{出生地}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text244>
            <Text245 Ref="57" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>8.2,4.5,7,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text245</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>{籍贯}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text245>
            <Text246 Ref="58" type="Text" isKey="true">
              <Border>Right, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>16.2,4.5,2.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text246</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>{民族}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text246>
            <Text247 Ref="59" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.8,5.2,5.6,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text247</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>{身份证号}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text247>
            <Text248 Ref="60" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>8.2,5.2,7,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text248</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>{职业}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text248>
            <Text249 Ref="61" type="Text" isKey="true">
              <Border>Right, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>16.2,5.2,2.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text249</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>{婚姻}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text249>
            <Text250 Ref="62" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.4,5.9,10.2,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text250</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>{现住址}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text250>
            <Text251 Ref="63" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>12.6,5.9,2.6,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text251</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>{电话}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text251>
            <Text252 Ref="64" type="Text" isKey="true">
              <Border>Right, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>16.2,5.9,2.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text252</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>{邮编}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text252>
            <Text253 Ref="65" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.8,6.6,13.4,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text253</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>{户口地址}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text253>
            <Text254 Ref="66" type="Text" isKey="true">
              <Border>Right, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>17,6.6,2,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text254</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>{户口邮编}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text254>
            <Text255 Ref="67" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3,7.3,8.6,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text255</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>{工作单位及地址}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text255>
            <Text256 Ref="68" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13.4,7.3,1.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text256</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>{单位电话}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text256>
            <Text257 Ref="69" type="Text" isKey="true">
              <Border>Right, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>17,7.3,2,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text257</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>{单位邮编}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text257>
            <Text258 Ref="70" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.2,8,1.4,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text258</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>{联系人姓名}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text258>
            <Text259 Ref="71" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.6,8,2.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text259</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>{关系}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text259>
            <Text260 Ref="72" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>8.2,8,7,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text260</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>{地址}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text260>
            <Text261 Ref="73" type="Text" isKey="true">
              <Border>Right, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>17,8,2,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text261</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>{联系电话}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text261>
            <Text262 Ref="74" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.8,8.7,5.6,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text262</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>{入院途径}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text262>
            <Text264 Ref="75" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9,8.7,2.6,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text264</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>{入院时间}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text264>
            <Text265 Ref="76" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13.4,8.7,1.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text265</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>{入院科别}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text265>
            <Text266 Ref="77" type="Text" isKey="true">
              <Border>Right, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>17,8.7,2,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text266</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>{入院病房}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text266>
            <Text267 Ref="78" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.8,9.4,1.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text267</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>{转科科别}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text267>
            <Text268 Ref="79" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>5.4,9.4,2,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text268</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>{出院时间}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text268>
            <Text269 Ref="80" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9,9.4,2.6,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text269</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>{出院科别}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text269>
            <Text270 Ref="81" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13.4,9.4,1.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text270</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>{出院病房}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text270>
            <Text271 Ref="82" type="Text" isKey="true">
              <Border>Right, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>17.8,9.4,1.2,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text271</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>{实际住院天数}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text271>
            <Text1 Ref="83" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.5,19,1.3</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>黑体,16,Bold</Font>
              <Guid>ac000e001dd94b16a743b2ea6bf49e88</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text1</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>住 院 病 案 首 页</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text1>
            <Text232 Ref="84" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,2.2,7.8,0.9</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>d246d4821f364f94b165ed78ecf948d7</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text232</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>健康卡号：{健康卡号}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text232>
            <Text233 Ref="85" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7.8,2.2,6.4,0.9</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>8a9c90e8f5584d78914b1255192dca0b</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text233</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>第 {住院次数} 次住院</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text233>
            <Text51 Ref="86" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3,10.1,8.6,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text51</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>{门诊或急诊诊断}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text51>
            <Text14 Ref="87" type="Text" isKey="true">
              <Border>Right, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13.4,10.1,5.6,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text14</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>{疾病编码}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text14>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>ReportTitleBand1</Name>
          <Page isRef="5" />
          <Parent isRef="5" />
        </ReportTitleBand1>
        <DataBand8 Ref="88" type="DataBand" isKey="true">
          <Border>Left, Right, Bottom;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,12,19,6</ClientRectangle>
          <Components isList="true" count="2">
            <SubReport1 Ref="89" type="SubReport" isKey="true">
              <Border>Left;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.2,9.5,5.8</ClientRectangle>
              <Components isList="true" count="0" />
              <Guid>f62256d96d1b418c8d6d44dec0146de3</Guid>
              <Name>SubReport1</Name>
              <Page isRef="5" />
              <Parent isRef="88" />
              <SubReportPageGuid>f025fdeb64774211b0e0a5eac18353e3</SubReportPageGuid>
            </SubReport1>
            <SubReport2 Ref="90" type="SubReport" isKey="true">
              <Border>Left;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9.5,0.2,9.5,5.8</ClientRectangle>
              <Components isList="true" count="0" />
              <Guid>f15295e87af1497fa147333cca681f1d</Guid>
              <Name>SubReport2</Name>
              <Page isRef="5" />
              <Parent isRef="88" />
              <SubReportPageGuid>42d6be8c1e03443bba075f1b6ed8863f</SubReportPageGuid>
            </SubReport2>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>view明细</DataSourceName>
          <Filters isList="true" count="0" />
          <Guid>acb3ec7aca044e26903d44781b9d3392</Guid>
          <Name>DataBand8</Name>
          <Page isRef="5" />
          <Parent isRef="5" />
          <Sort isList="true" count="2">
            <value>ASC</value>
            <value>Yp_Code</value>
          </Sort>
        </DataBand8>
        <DataBand2 Ref="91" type="DataBand" isKey="true">
          <Border>Left, Right, Bottom;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,18.8,19,4.4</ClientRectangle>
          <Components isList="true" count="42">
            <Text74 Ref="92" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.2,4,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>77ee930409914c63a8b22fbe46fe080d</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text74</Name>
              <Page isRef="5" />
              <Parent isRef="91" />
              <Text>损伤或中毒的外部原因：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text74>
            <Text90 Ref="93" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>14.4,0.2,2.2,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>358db18ec83f4f53979fb88dd67576c7</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text90</Name>
              <Page isRef="5" />
              <Parent isRef="91" />
              <Text>其疾病编码：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text90>
            <Text96 Ref="94" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>10.8,0.9,2.4,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>7490e9f6585c4eceaf283ea18a6aadab</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text96</Name>
              <Page isRef="5" />
              <Parent isRef="91" />
              <Text>病理疾病编码：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text96>
            <Text98 Ref="95" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>14.4,0.9,1.4,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>c9f4d051ba164be0837b2627ad0cbef4</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text98</Name>
              <Page isRef="5" />
              <Parent isRef="91" />
              <Text>病理号：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text98>
            <Text100 Ref="96" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.6,1.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>ac3b894b082f412aa9e2e9a36cc37da1</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text100</Name>
              <Page isRef="5" />
              <Parent isRef="91" />
              <Text>药物过敏：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text100>
            <Text102 Ref="97" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>10.8,1.6,2.4,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>898b0ab733af41038468ed050b621d99</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text102</Name>
              <Page isRef="5" />
              <Parent isRef="91" />
              <Text>死亡患者尸检：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text102>
            <Text104 Ref="98" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>14.4,1.6,1,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>21f300174b8b4739898d2bf679e1a897</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text104</Name>
              <Page isRef="5" />
              <Parent isRef="91" />
              <Text>血型：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text104>
            <Text106 Ref="99" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>16.8,1.6,0.6,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>39917f0db3694c83b4950737ba7dd1cd</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text106</Name>
              <Page isRef="5" />
              <Parent isRef="91" />
              <Text>RH：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text106>
            <Text108 Ref="100" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,2.3,1.4,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>72192f32b8354cfcbec5c16ac8246baa</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text108</Name>
              <Page isRef="5" />
              <Parent isRef="91" />
              <Text>科主任：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text108>
            <Text112 Ref="101" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9.6,2.3,1.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>7acb89d54a2c4806837792107edf0f49</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text112</Name>
              <Page isRef="5" />
              <Parent isRef="91" />
              <Text>主治医师：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text112>
            <Text114 Ref="102" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>14.4,2.3,1.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>e0115e4bcf394573897da3ef1f46cc47</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text114</Name>
              <Page isRef="5" />
              <Parent isRef="91" />
              <Text>住院医师：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text114>
            <Text116 Ref="103" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,3,1.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>ff92ea4b92ef40d38516d02cd2a0df15</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text116</Name>
              <Page isRef="5" />
              <Parent isRef="91" />
              <Text>责任护士：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text116>
            <Text118 Ref="104" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.8,3,1.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>c1f8f2884de14b7eb12ac1396624b665</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text118</Name>
              <Page isRef="5" />
              <Parent isRef="91" />
              <Text>进修医师：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text118>
            <Text120 Ref="105" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9.6,3,1.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>81641fafcfba4f0cbbad8a838fe408d9</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text120</Name>
              <Page isRef="5" />
              <Parent isRef="91" />
              <Text>实习医师：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text120>
            <Text122 Ref="106" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>14.4,3,1.4,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>71cfc149c7764990b565be76f2179084</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text122</Name>
              <Page isRef="5" />
              <Parent isRef="91" />
              <Text>编码员：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text122>
            <Text126 Ref="107" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.8,3.7,1.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>32a920bfe547461088bcb42846e060f8</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text126</Name>
              <Page isRef="5" />
              <Parent isRef="91" />
              <Text>质控医师：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text126>
            <Text128 Ref="108" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>9.6,3.7,1.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>9f289ed89a42407cbc61b4e9dc7cb8b5</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text128</Name>
              <Page isRef="5" />
              <Parent isRef="91" />
              <Text>质控护士：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text128>
            <Text130 Ref="109" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>14.4,3.7,1.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>3a04b131b2c24fdeac641236f619e007</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text130</Name>
              <Page isRef="5" />
              <Parent isRef="91" />
              <Text>质控日期：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text130>
            <Text124 Ref="110" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,3.7,1.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>3b6db6415653495eba3a207c5d2a8eef</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text124</Name>
              <Page isRef="5" />
              <Parent isRef="91" />
              <Text>病案质量：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text124>
            <Text84 Ref="111" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.8,2.3,3.2,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>674d0e6209c1411ca012166a95baf249</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text84</Name>
              <Page isRef="5" />
              <Parent isRef="91" />
              <Text>主任或副主任医师:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text84>
            <Text94 Ref="112" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.9,1.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>928129e944454f959e69f93a2489937f</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text94</Name>
              <Page isRef="5" />
              <Parent isRef="91" />
              <Text>病理诊断：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text94>
            <Text283 Ref="113" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4,0.2,10.4,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text283</Name>
              <Page isRef="5" />
              <Parent isRef="91" />
              <Text>{损伤或中毒的外部原因}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text283>
            <Text284 Ref="114" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>16.6,0.2,2.4,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text284</Name>
              <Page isRef="5" />
              <Parent isRef="91" />
              <Text>{其疾病编码}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text284>
            <Text285 Ref="115" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.8,0.9,9,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text285</Name>
              <Page isRef="5" />
              <Parent isRef="91" />
              <Text>{病理诊断}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text285>
            <Text286 Ref="116" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13.2,0.9,1.2,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text286</Name>
              <Page isRef="5" />
              <Parent isRef="91" />
              <Text>{病理疾病编码}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text286>
            <Text287 Ref="117" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15.8,0.9,3.2,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text287</Name>
              <Page isRef="5" />
              <Parent isRef="91" />
              <Text>{病理号}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text287>
            <Text288 Ref="118" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.8,1.6,9,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text288</Name>
              <Page isRef="5" />
              <Parent isRef="91" />
              <Text>{药物过敏}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text288>
            <Text289 Ref="119" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13.2,1.6,1.2,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text289</Name>
              <Page isRef="5" />
              <Parent isRef="91" />
              <Text>{死亡患者尸检}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text289>
            <Text290 Ref="120" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15.4,1.6,1.4,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text290</Name>
              <Page isRef="5" />
              <Parent isRef="91" />
              <Text>{血型}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text290>
            <Text291 Ref="121" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>17.4,1.6,1.6,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text291</Name>
              <Page isRef="5" />
              <Parent isRef="91" />
              <Text>{RH}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text291>
            <Text292 Ref="122" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.4,2.3,3.4,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text292</Name>
              <Page isRef="5" />
              <Parent isRef="91" />
              <Text>{科主任}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text292>
            <Text293 Ref="123" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>8,2.3,1.6,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text293</Name>
              <Page isRef="5" />
              <Parent isRef="91" />
              <Text>{主任或副主任医师}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text293>
            <Text294 Ref="124" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>11.4,2.3,3,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text294</Name>
              <Page isRef="5" />
              <Parent isRef="91" />
              <Text>{主治医师}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text294>
            <Text295 Ref="125" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>16.2,2.3,2.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text295</Name>
              <Page isRef="5" />
              <Parent isRef="91" />
              <Text>{住院医师}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text295>
            <Text296 Ref="126" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.8,3,3,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text296</Name>
              <Page isRef="5" />
              <Parent isRef="91" />
              <Text>{责任护士}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text296>
            <Text297 Ref="127" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6.6,3,3,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text297</Name>
              <Page isRef="5" />
              <Parent isRef="91" />
              <Text>{进修医师}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text297>
            <Text298 Ref="128" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>11.4,3,3,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text298</Name>
              <Page isRef="5" />
              <Parent isRef="91" />
              <Text>{实习医师}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text298>
            <Text299 Ref="129" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15.8,3,3.2,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text299</Name>
              <Page isRef="5" />
              <Parent isRef="91" />
              <Text>{编码员}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text299>
            <Text300 Ref="130" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.8,3.7,3,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text300</Name>
              <Page isRef="5" />
              <Parent isRef="91" />
              <Text>{病案质量}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text300>
            <Text301 Ref="131" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>11.4,3.7,3,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text301</Name>
              <Page isRef="5" />
              <Parent isRef="91" />
              <Text>{质控护士}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text301>
            <Text302 Ref="132" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>6.6,3.7,3,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text302</Name>
              <Page isRef="5" />
              <Parent isRef="91" />
              <Text>{质控医师}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text302>
            <Text303 Ref="133" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>16.2,3.7,2.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text303</Name>
              <Page isRef="5" />
              <Parent isRef="91" />
              <Text>{质控日期}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text303>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>view明细</DataSourceName>
          <Filters isList="true" count="0" />
          <Guid>380d0ec60a8d4f20aa8342289c777bd5</Guid>
          <Name>DataBand2</Name>
          <Page isRef="5" />
          <Parent isRef="5" />
          <Sort isList="true" count="2">
            <value>ASC</value>
            <value>Yp_Code</value>
          </Sort>
        </DataBand2>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>b34eae1e5edd44cdb0bd8cc5d316eea3</Guid>
      <Margins>1,1,1,1</Margins>
      <Name>Page1</Name>
      <PageHeight>29.7</PageHeight>
      <PageWidth>21</PageWidth>
      <PaperSize>A4</PaperSize>
      <Report isRef="0" />
      <StretchToPrintArea>True</StretchToPrintArea>
      <Watermark Ref="134" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page1>
    <Page2 Ref="135" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="5">
        <HeaderBand2 Ref="136" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,19,1</ClientRectangle>
          <Components isList="true" count="11">
            <Text4 Ref="137" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>502742fc8de540a49cd715393ac4fd85</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text4</Name>
              <Page isRef="135" />
              <Parent isRef="136" />
              <Text>手术及操作编码</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text4>
            <Text8 Ref="138" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2,0,2,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>a7f55b949e1148b2b5b6b325d53ffd87</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text8</Name>
              <Page isRef="135" />
              <Parent isRef="136" />
              <Text>手术及操作日期</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text8>
            <Text10 Ref="139" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4,0,1,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>0f4daaaebe3a4a95946aea080b4633a1</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text10</Name>
              <Page isRef="135" />
              <Parent isRef="136" />
              <Text>手术级别</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text10>
            <Text12 Ref="140" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>16.9,0,2.1,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>012726c8141b40e69b7e99c18059fda1</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text12</Name>
              <Page isRef="135" />
              <Parent isRef="136" />
              <Text>麻醉医师</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text12>
            <Text15 Ref="141" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.8,0.5,1.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>59371450d54c4c188933881622919b7b</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text15</Name>
              <Page isRef="135" />
              <Parent isRef="136" />
              <Text>术者</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text15>
            <Text17 Ref="142" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9.4,0.5,1.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>071f9ae1c67a4b54be10998f4b1af880</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text17</Name>
              <Page isRef="135" />
              <Parent isRef="136" />
              <Text>Ⅰ助</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text17>
            <Text23 Ref="143" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.8,0,4.8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>e2072728c1424a79943cd4d4988d35b4</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text23</Name>
              <Page isRef="135" />
              <Parent isRef="136" />
              <Text>手术及操作医师</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text23>
            <Text25 Ref="144" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>12.6,0,1.8,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>22688417867b471aa6341475f3f3bef4</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text25</Name>
              <Page isRef="135" />
              <Parent isRef="136" />
              <Text>切口愈合等级</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text25>
            <Text20 Ref="145" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>5,0,2.8,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>74268f0bbea344abbb2f70e7c2afd14c</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text20</Name>
              <Page isRef="135" />
              <Parent isRef="136" />
              <Text>手术及操作名称</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text20>
            <Text26 Ref="146" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>14.4,0,2.5,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>a983f534586741198eceb809387faa65</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text26</Name>
              <Page isRef="135" />
              <Parent isRef="136" />
              <Text>麻醉方式</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text26>
            <Text27 Ref="147" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>11,0.5,1.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>887b08bd0c914b66ad0645f885f3d5d7</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text27</Name>
              <Page isRef="135" />
              <Parent isRef="136" />
              <Text>Ⅱ助</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text27>
          </Components>
          <Conditions isList="true" count="0" />
          <Guid>e98c95d8d4bd4643b4661928ee5599b2</Guid>
          <Name>HeaderBand2</Name>
          <Page isRef="135" />
          <Parent isRef="135" />
          <PrintIfEmpty>True</PrintIfEmpty>
        </HeaderBand2>
        <DataBand3 Ref="148" type="DataBand" isKey="true">
          <Border>Bottom;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,2.2,19,0.5</ClientRectangle>
          <Components isList="true" count="10">
            <Text31 Ref="149" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>53fa33eae46a4b03a90e4c83a70081ec</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text31</Name>
              <Page isRef="135" />
              <Parent isRef="148" />
              <Text>{手术信息.OPERATION_CODE}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text31>
            <Text32 Ref="150" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>2,0,2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>5098426210874901ab4720868e4234a2</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text32</Name>
              <Page isRef="135" />
              <Parent isRef="148" />
              <Text>{手术信息.OPERATION_DTIME}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text32>
            <Text33 Ref="151" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>4,0,1,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>92d9e08bed3f4916ab28f75bcbcf256d</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text33</Name>
              <Page isRef="135" />
              <Parent isRef="148" />
              <Text>{手术信息.Operation_Scale_Name}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text33>
            <Text34 Ref="152" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>5,0,2.8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>ea1f88f3a4214e52abb553f39fbe05d9</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text34</Name>
              <Page isRef="135" />
              <Parent isRef="148" />
              <Text>{手术信息.OPERATION_NAME}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text34>
            <Text35 Ref="153" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>7.8,0,1.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>75c3e4693c6f4937ad22f760f05d1b80</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text35</Name>
              <Page isRef="135" />
              <Parent isRef="148" />
              <Text>{手术信息.SURGEON_NAME}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text35>
            <Text36 Ref="154" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>9.4,0,1.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>f03463bbdc59487baf4b94b782a7f60d</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text36</Name>
              <Page isRef="135" />
              <Parent isRef="148" />
              <Text>{手术信息.ASSISTANT1_NAME}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text36>
            <Text37 Ref="155" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>11,0,1.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>9670d9b60f1c4f2a8023967130639fe6</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text37</Name>
              <Page isRef="135" />
              <Parent isRef="148" />
              <Text>{手术信息.ASSISTANT2_NAME}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text37>
            <Text91 Ref="156" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>12.6,0,1.8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>79156209c62b4a498ceb08aecd325d57</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text91</Name>
              <Page isRef="135" />
              <Parent isRef="148" />
              <Text>{手术信息.INCISION_HEALING_Name}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text91>
            <Text92 Ref="157" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>14.4,0,2.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>d70fbd288ae940269ad4cdee67a70af7</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text92</Name>
              <Page isRef="135" />
              <Parent isRef="148" />
              <Text>{手术信息.ANESTHESIA_Name}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text92>
            <Text95 Ref="158" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>16.9,0,2.1,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>667dd796cc564a86ac09584f25160887</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text95</Name>
              <Page isRef="135" />
              <Parent isRef="148" />
              <Text>{手术信息.ANESTHESIOLOGIST_NAME}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text95>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>手术信息</DataSourceName>
          <Filters isList="true" count="0" />
          <Guid>0fc71aba1d754c33ad03cfeff8126f48</Guid>
          <Name>DataBand3</Name>
          <Page isRef="135" />
          <Parent isRef="135" />
          <Sort isList="true" count="0" />
        </DataBand3>
        <DataBand4 Ref="159" type="DataBand" isKey="true">
          <Border>Left, Right, Bottom;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,3.5,19,3.5</ClientRectangle>
          <Components isList="true" count="9">
            <Text99 Ref="160" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.2,1.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>d8b0d219af7f4cd6ab98f4ad6351d9f1</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text99</Name>
              <Page isRef="135" />
              <Parent isRef="159" />
              <Text>离院方式:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text99>
            <Text101 Ref="161" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9.2,0.2,9.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>27c81dd576e84f78bbfc43a7da57f61c</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text101</Name>
              <Page isRef="135" />
              <Parent isRef="159" />
              <Text>拟接收医疗机构名称:{拟接收医疗机构名称}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text101>
            <Text105 Ref="162" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.9,6.2,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>46e3f3af8bbd4736af5e8e5040e40c9c</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text105</Name>
              <Page isRef="135" />
              <Parent isRef="159" />
              <Text>是否有出院31天内再住院计划:{是否有出院31天内再住院计划}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text105>
            <Text107 Ref="163" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4,1.6,5.2,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>6eb1116a4a9843099f1ee302fd83ca98</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text107</Name>
              <Page isRef="135" />
              <Parent isRef="159" />
              <Text>入院前:{入院前}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text107>
            <Text111 Ref="164" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9.2,1.6,9.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>f0303de5c01e4605a5df6479ee0a9ba6</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text111</Name>
              <Page isRef="135" />
              <Parent isRef="159" />
              <Text>入院后:{入院后}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text111>
            <Text135 Ref="165" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.6,4,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>94ce5d0e10ce43a695e2592d10cfcc15</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text135</Name>
              <Page isRef="135" />
              <Parent isRef="159" />
              <Text>颅脑损伤患者昏迷时间:</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text135>
            <Text281 Ref="166" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.8,0.2,7.4,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text281</Name>
              <Page isRef="135" />
              <Parent isRef="159" />
              <Text>{离院方式}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text281>
            <Text11 Ref="167" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.2,0.9,11.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text11</Name>
              <Page isRef="135" />
              <Parent isRef="159" />
              <Text>{目的}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text11>
            <Text13 Ref="168" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6.2,0.9,1,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text13</Name>
              <Page isRef="135" />
              <Parent isRef="159" />
              <Text>目的:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text13>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>view明细</DataSourceName>
          <Filters isList="true" count="0" />
          <Guid>7b8fceac0a5146ad9b190bbc892b15dc</Guid>
          <Name>DataBand4</Name>
          <Page isRef="135" />
          <Parent isRef="135" />
          <Sort isList="true" count="2">
            <value>ASC</value>
            <value>Yp_Code</value>
          </Sort>
        </DataBand4>
        <DataBand5 Ref="169" type="DataBand" isKey="true">
          <Border>Left, Right, Bottom;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,7.8,19,12.9</ClientRectangle>
          <Components isList="true" count="89">
            <Text103 Ref="170" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1,3.2,1.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>f7dcb9f38c74478d914b1002411b1037</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text103</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>综合医疗服务类:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text103>
            <Text110 Ref="171" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,4.5,3.2,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>557e4603cd9e45cfb27ab52ca403ab8f</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text110</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>康复类：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text110>
            <Text113 Ref="172" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,5.2,3.2,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>33e6c699fa2a4d2283a3e18cff3ba2be</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text113</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>中医类：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text113>
            <Text38 Ref="173" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,5.9,3.2,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>c35ad5cd05ca44e4b86a877a18bf9a2b</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text38</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>西药类:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text38>
            <Text39 Ref="174" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,6.6,3.2,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>47c4e2ab4e934e168b3b3f9e556a7fc6</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text39</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>中药类:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text39>
            <Text41 Ref="175" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,7.3,3.2,1.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>29559b19248f48e1a7789873bbbe163f</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text41</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>血液和血液制品:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text41>
            <Text117 Ref="176" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,8.7,3.2,1.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>1d976ad1cfa74634b43ee5bcd508aad2</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text117</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>耗材类:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text117>
            <Text119 Ref="177" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,10.1,3.2,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>e1653f88bd3649caa60913408d801548</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text119</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>其他类:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text119>
            <Text115 Ref="178" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,3.1,3.2,1.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>425965df569f48deb6298104d3ae779a</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text115</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>治疗类:</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text115>
            <Text109 Ref="179" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,2.4,3.2,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>cc43886568da40ef849c66a04cf43058</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text109</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>诊断类:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text109>
            <Text97 Ref="180" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.2,3.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>9a42d5b9c5a04dada4b709a22ec46fbc</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text97</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>住院费用(元):</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text97>
            <Text123 Ref="181" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>3.2,0.2,1.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>4e0006b7f504421790a9617c32d52751</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text123</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>总费用:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text123>
            <Text127 Ref="182" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>8.4,0.2,1.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>dedb544bedb341b3a2f9c769998e7010</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text127</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>自付金额：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text127>
            <Text40 Ref="183" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.6,0.2,3.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>dc7cf5300c2a440982412eae12e10b6b</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text40</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>{总费用}</Text>
              <TextBrush>Black</TextBrush>
              <TextQuality>Wysiwyg</TextQuality>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text40>
            <Text121 Ref="184" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6.4,1,2,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>4086f148ad4749c8b7e0ce90ba064f9f</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text121</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>{一般医疗服务费}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text121>
            <Text125 Ref="185" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>3.2,1,3.2,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>f7c91efd470d4c769c9931532b2c7cb5</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text125</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>1.一般医疗服务费:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text125>
            <Text134 Ref="186" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>11.6,1,2,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>7d6f0a3b9a8f415aa474dccba036b6d7</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text134</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>{一般治疗操作费}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text134>
            <Text136 Ref="187" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>8.4,1,3.2,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>0f83f6496d174228a37cb29f5327c58b</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text136</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>2.一般治疗操作费:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text136>
            <Text137 Ref="188" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>5,1.7,3.4,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>a9c247ef436d43279781360cd784e2b2</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text137</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>{护理费}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text137>
            <Text138 Ref="189" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>3.2,1.7,1.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>daffb94af32743c995dbda90dec87290</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text138</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>3.护理费:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text138>
            <Text139 Ref="190" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>10.6,1.7,3,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>918279ba360d4018ba08b00daf0276f1</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text139</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>{其他费用}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text139>
            <Text140 Ref="191" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>8.4,1.7,2.2,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>9fef4fd32e4b4de49d2ac519fea94013</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text140</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>4.其他费用:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text140>
            <Text141 Ref="192" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>5.6,2.4,1,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>77351a3f32bd45eb848e01e291b73b11</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text141</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>{病理诊断费}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text141>
            <Text142 Ref="193" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>3.2,2.4,2.4,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>9111a59514394544be0e9d25fbb2de9e</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text142</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>5.病理诊断费:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text142>
            <Text143 Ref="194" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9.4,2.4,1.2,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>c783bdf8397b4527a22c05ce0597109f</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text143</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>{实验室诊断费}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text143>
            <Text144 Ref="195" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>6.6,2.4,2.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>63a6524da3b148cbbf513e4a5ef06503</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text144</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>6.实验室诊断费:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text144>
            <Text145 Ref="196" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13.4,2.4,1.2,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>7fdaee860b114af29edb7744bcee5cc8</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text145</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>{影像学诊断费}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text145>
            <Text146 Ref="197" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>10.6,2.4,2.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>3560c5a4070943c89a71ea9f99c3a9ea</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text146</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>7.影像学诊断费:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text146>
            <Text147 Ref="198" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>17.8,2.4,1.2,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>a99e1727952a4b93b216c310d64f9dbd</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text147</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>{临床诊断项目费}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text147>
            <Text148 Ref="199" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>14.6,2.4,3.2,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>49c627a56dd9441284a4c1ae5915fc3f</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text148</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>8.临床诊断项目费:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text148>
            <Text149 Ref="200" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6.8,3.1,1.6,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>eeb41eed8a2c4ceea82857348fd4dc18</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text149</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>{非手术治疗项目费}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text149>
            <Text150 Ref="201" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>3.2,3.1,3.6,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>dbbb5b0df12c4f80a26461f86d3f966d</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text150</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>9.非手术治疗项目费:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text150>
            <Text151 Ref="202" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>11.2,3.1,2,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>8e3e1a3f8bfb42768a343fbc75553d54</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text151</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>{临床物理治疗费}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text151>
            <Text152 Ref="203" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>8.4,3.1,2.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>f1e6f55a761a492caf8a8a1cd3d1087e</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text152</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>临床物理治疗费:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text152>
            <Text153 Ref="204" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>5.8,3.8,2.6,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>687de9be6ea14abb9a4704e621956902</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text153</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>{手术治疗费}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text153>
            <Text154 Ref="205" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>3.2,3.8,2.6,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>a44903210388476a86a651cd94b6a228</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text154</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>10.手术治疗费：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text154>
            <Text159 Ref="206" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>5.2,4.5,3.2,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>66f621dc39a94fe99a7670c56d65dc6d</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text159</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>{康复费}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text159>
            <Text160 Ref="207" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>3.2,4.5,2,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>62267ff43e2248a7b31a1fc1f30ad2cc</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text160</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>11.康复费：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text160>
            <Text207 Ref="208" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9.8,3.8,3.4,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>637caa6c0b50493abcb35a6c4462851d</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text207</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>{麻醉费}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text207>
            <Text208 Ref="209" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>8.4,3.8,1.4,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>c08fdff46bbb4f909c8dd503430523f6</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text208</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>麻醉费：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text208>
            <Text155 Ref="210" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>14.6,3.8,3.4,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>aac562ce87c6431998f863ac73862b43</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text155</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>{手术费}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text155>
            <Text156 Ref="211" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>13.2,3.8,1.4,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>f82624692b1d4967b0a2d8f3edf4deff</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text156</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>手术费：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text156>
            <Text162 Ref="212" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>3.2,5.2,2.6,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>94ee11bfec7f4434a61a14c49ffcbad5</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text162</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>12.中医治疗费：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text162>
            <Text181 Ref="213" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>5.2,5.9,3.2,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>136665414e714392a49f6b8f1629929e</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text181</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>{西药费}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text181>
            <Text182 Ref="214" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>3.2,5.9,2,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>130a8d5e0a9c451f98d916108b470d03</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text182</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>13.西药费:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text182>
            <Text183 Ref="215" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>10.8,5.9,3.2,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>1c06aae38aad4d4f90d4bebf00633d60</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text183</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>{抗菌药物费用}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text183>
            <Text184 Ref="216" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>8.4,5.9,2.4,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>08192c7adb8548c1bea2c0eff4410664</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text184</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>抗菌药物费用:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text184>
            <Text185 Ref="217" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>5.4,6.6,3,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>430bf8d8301644d29f84e707bf22ba14</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text185</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>{中成药费}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text185>
            <Text186 Ref="218" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>3.2,6.6,2.2,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>f5bf88c6c8c341459acfeb1f63e3339b</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text186</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>14.中成药费:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text186>
            <Text189 Ref="219" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>10.6,6.6,3,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>8e07c92825294dfdacacd7c5e0d8d160</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text189</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>{中草药费}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text189>
            <Text190 Ref="220" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>8.4,6.6,2.2,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>665c4900239d4764958d859903859f32</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text190</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>15.中草药费：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text190>
            <Text191 Ref="221" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.8,7.3,3.6,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>946e795590eb403f8e0f0b201133d9bf</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text191</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>{血费}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text191>
            <Text192 Ref="222" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>3.2,7.3,1.6,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>f9bd13413bb24873bfad6f4459460d90</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text192</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>16.血费：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text192>
            <Text193 Ref="223" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>11.8,7.3,1.4,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>e783b5780c884e44a0b0a91182af1369</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text193</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>{白蛋白类制品费}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text193>
            <Text194 Ref="224" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>8.4,7.3,3.4,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>bd61ecdbb83942b68f8dc105fcc34ce9</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text194</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>17.白蛋白类制品费：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text194>
            <Text195 Ref="225" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>16.6,7.3,2.4,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>8f394071c2094c6ab1239c841c4ef6dc</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text195</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>{球蛋白类制品费}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text195>
            <Text196 Ref="226" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>13.2,7.3,3.4,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>5071df3640644ab6994c84df4e3fd725</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text196</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>28.球蛋白类制品费：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text196>
            <Text197 Ref="227" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7,8,1.4,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>f0631efe656c49b693a2ad2d03131e74</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text197</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>{凝血因子类制品费}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text197>
            <Text198 Ref="228" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>3.2,8,3.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>38884c4497b5407ea36dd392d390fc58</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text198</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>19.凝血因子类制品费：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text198>
            <Text199 Ref="229" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>12.2,8,1.4,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>fd13511b8153467f8c44411c0886cc17</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text199</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>{细胞因子类制品费}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text199>
            <Text200 Ref="230" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>8.4,8,3.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>a35577558aeb46baa5595ac1b5057ee3</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text200</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>20.细胞因子类制品费:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text200>
            <Text201 Ref="231" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>8,8.7,2.6,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>9578a0f1d8d74670b01f22799cacadea</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text201</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>{检查用一次性医用材料费}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text201>
            <Text202 Ref="232" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>3.2,8.7,4.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>04fcdc2709344777940e3d26976d8f3e</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text202</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>21.检查用一次性医用材料费:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text202>
            <Text203 Ref="233" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15.4,8.7,2.6,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>f8afa6fb5fa641adb87c4fd4bd4cc546</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text203</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>{治疗用一次性医用材料费}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text203>
            <Text204 Ref="234" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>10.6,8.7,4.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>7d1079f947844884b0408bc2d1f96dc0</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text204</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>22.治疗用一次性医用材料费:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text204>
            <Text205 Ref="235" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>8,9.4,2.6,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>2f0da8efc50f4db6ac656df39dfd8a30</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text205</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>{手术用一次性医用材料费}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text205>
            <Text206 Ref="236" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>3.2,9.4,4.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>98cb5cd3d41a4ba19e0e383b569328ff</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text206</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>23.手术用一次性医用材料费:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text206>
            <Text209 Ref="237" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>5.2,10.1,13.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>1448ac113c7a4786abc5cc54298538ab</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text209</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>{其他费}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text209>
            <Text210 Ref="238" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.2,10.1,2,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>d4f69150c67d491c8e96ae4b5939f98f</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text210</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>24.其他费:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text210>
            <Text235 Ref="239" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>10,0.2,3.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text235</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>{自付金额}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text235>
            <Text28 Ref="240" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.6,11,1.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text28</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>{抗菌药物使用情况}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text28>
            <Text29 Ref="241" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0.2,11,3.4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text29</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>抗菌药物使用情况：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text29>
            <Text30 Ref="242" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>10,11,2.4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text30</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>{住院有无坠床或跌倒及伤害程度}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text30>
            <Text211 Ref="243" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.8,11,5.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text211</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>住院有无坠床或跌倒及伤害程度：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text211>
            <Text212 Ref="244" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15.2,11,3.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text212</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>{跌倒或坠床原因}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text212>
            <Text216 Ref="245" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>12.4,11,2.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text216</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>跌倒或坠床原因：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text216>
            <Text217 Ref="246" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2,11.6,2.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text217</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>{输液反应}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text217>
            <Text221 Ref="247" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0.2,11.6,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text221</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>输液反应：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text221>
            <Text222 Ref="248" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.2,11.6,5.4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text222</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>{引发反应药物}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text222>
            <Text223 Ref="249" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.8,11.6,2.4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text223</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>引发反应药物：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text223>
            <Text224 Ref="250" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>14.4,11.6,4.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text224</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>{临床表现}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text224>
            <Text225 Ref="251" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>12.6,11.6,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text225</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>临床表现：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text225>
            <Text226 Ref="252" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.6,12.2,2.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text226</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>{医院感染情况}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text226>
            <Text227 Ref="253" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0.2,12.2,2.4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text227</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>医院感染情况：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text227>
            <Text228 Ref="254" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.2,12.2,5.4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text228</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>{感染部位}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text228>
            <Text229 Ref="255" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.8,12.2,2.4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text229</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>感染部位：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text229>
            <Text230 Ref="256" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15,12.2,4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text230</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>{医院感染名称}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text230>
            <Text231 Ref="257" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>12.6,12.2,2.4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text231</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>医院感染名称：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text231>
            <Text53 Ref="258" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>5.8,5.2,2.6,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text53</Name>
              <Page isRef="135" />
              <Parent isRef="169" />
              <Text>{中医治疗费}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text53>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>view明细</DataSourceName>
          <Filters isList="true" count="0" />
          <Guid>c45dcb52ff9d4a3fa5d9e6d8269f3af1</Guid>
          <Name>DataBand5</Name>
          <Page isRef="135" />
          <Parent isRef="135" />
          <Sort isList="true" count="2">
            <value>ASC</value>
            <value>Yp_Code</value>
          </Sort>
        </DataBand5>
        <DataBand7 Ref="259" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,21.5,19,2.2</ClientRectangle>
          <Components isList="true" count="3">
            <Text21 Ref="260" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.2,1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>d48bdb9c18b7484abfc49f89c22ec42c</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text21</Name>
              <Page isRef="135" />
              <Parent isRef="259" />
              <Text>说明:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text21>
            <Text22 Ref="261" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1,0.2,18,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>9b136a0bd4a5454ab9775f34b20f951d</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text22</Name>
              <Page isRef="135" />
              <Parent isRef="259" />
              <Text>（一）医疗付费方式 1.城镇职工基本医疗保险 2.城镇居民基本医疗保险 3.新型农村合作医疗 4.贫困救助 5.商业医疗保险 6.全公费 7.全自费 8.其他社会保险 9.其他</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text22>
            <Text24 Ref="262" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1,1.2,18,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>fb16f01771324e81b8262055cf40cde4</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text24</Name>
              <Page isRef="135" />
              <Parent isRef="259" />
              <Text>（二）凡可由医院信息系统提供住院费用清单的，住院病案首页中可不填写“住院费用”。</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text24>
          </Components>
          <Conditions isList="true" count="0" />
          <Filters isList="true" count="0" />
          <Name>DataBand7</Name>
          <Page isRef="135" />
          <Parent isRef="135" />
          <Sort isList="true" count="0" />
        </DataBand7>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>16a10d8d85d74a0eb78fe7618b043b56</Guid>
      <Margins>1,1,1,1</Margins>
      <Name>Page2</Name>
      <PageHeight>29.7</PageHeight>
      <PageWidth>21</PageWidth>
      <PaperSize>A4</PaperSize>
      <Report isRef="0" />
      <Watermark Ref="263" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page2>
    <subReport_1 Ref="264" type="Page" isKey="true">
      <Border>Left, Right;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="2">
        <HeaderBand1 Ref="265" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,9.5,0.7</ClientRectangle>
          <Components isList="true" count="3">
            <Text6 Ref="266" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,5,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>a832ab84203844eb8bd869bf404ad900</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text6</Name>
              <Page isRef="264" />
              <Parent isRef="265" />
              <Text>出院诊断</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text6>
            <Text7 Ref="267" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>5,0,2,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>1274b02299894b8dbdc7fd6039aa7e05</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text7</Name>
              <Page isRef="264" />
              <Parent isRef="265" />
              <Text>疾病编码</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text7>
            <Text9 Ref="268" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7,0,2.5,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>f97efbe2e5a241dbb18751dc4878f893</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text9</Name>
              <Page isRef="264" />
              <Parent isRef="265" />
              <Text>入院病情</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text9>
          </Components>
          <Conditions isList="true" count="0" />
          <Guid>f3fbda5e1f6749f492b9782e8ce12961</Guid>
          <Name>HeaderBand1</Name>
          <Page isRef="264" />
          <Parent isRef="264" />
          <PrintIfEmpty>True</PrintIfEmpty>
        </HeaderBand1>
        <DataBand1 Ref="269" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,1.9,9.5,0.7</ClientRectangle>
          <Components isList="true" count="3">
            <Text16 Ref="270" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,5,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>ce1651d70e4348b280bd332281216615</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text16</Name>
              <Page isRef="264" />
              <Parent isRef="269" />
              <Text>{出院诊断信息左.DIAGNOSIS_NAME}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text16>
            <Text18 Ref="271" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>5,0,2,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>3059dcec060e462b9667f55f9eeb342a</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text18</Name>
              <Page isRef="264" />
              <Parent isRef="269" />
              <Text>{出院诊断信息左.DIAGNOSIS_CD}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="272" type="CustomFormat" isKey="true">
                <StringFormat>yyyy-MM-dd</StringFormat>
              </TextFormat>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text18>
            <Text19 Ref="273" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7,0,2.5,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>d0d3711d91704631aae345818afd7ee3</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text19</Name>
              <Page isRef="264" />
              <Parent isRef="269" />
              <Text>{出院诊断信息左.DIAG_RESULT_CODE}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="274" type="CustomFormat" isKey="true">
                <StringFormat>0.####</StringFormat>
              </TextFormat>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text19>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>出院诊断信息左</DataSourceName>
          <Filters isList="true" count="0" />
          <Guid>e53dcf0b4e5b4fa89d88ee951e285f01</Guid>
          <Name>DataBand1</Name>
          <Page isRef="264" />
          <Parent isRef="264" />
          <Sort isList="true" count="0" />
        </DataBand1>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>f025fdeb64774211b0e0a5eac18353e3</Guid>
      <Margins>0,0,0,0</Margins>
      <Name>subReport_1</Name>
      <PageHeight>8.4</PageHeight>
      <PageWidth>9.5</PageWidth>
      <Report isRef="0" />
      <Watermark Ref="275" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </subReport_1>
    <subReport_2 Ref="276" type="Page" isKey="true">
      <Border>Left, Right;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="2">
        <HeaderBand3 Ref="277" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,9.5,0.7</ClientRectangle>
          <Components isList="true" count="3">
            <Text213 Ref="278" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,5,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>098998bfc16e4a18a29350b5d6988ac0</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text213</Name>
              <Page isRef="276" />
              <Parent isRef="277" />
              <Text>出院诊断</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text213>
            <Text214 Ref="279" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>5,0,2,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>0b13a24963524134944261be393a43ed</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text214</Name>
              <Page isRef="276" />
              <Parent isRef="277" />
              <Text>疾病编码</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text214>
            <Text215 Ref="280" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7,0,2.5,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>daa593b1d53c44ebb9b50e9ba6aa53d1</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text215</Name>
              <Page isRef="276" />
              <Parent isRef="277" />
              <Text>入院病情</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text215>
          </Components>
          <Conditions isList="true" count="0" />
          <Guid>47500a0a7bc5464ca1b5b431237ae397</Guid>
          <Name>HeaderBand3</Name>
          <Page isRef="276" />
          <Parent isRef="276" />
          <PrintIfEmpty>True</PrintIfEmpty>
        </HeaderBand3>
        <DataBand6 Ref="281" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,1.9,9.5,0.7</ClientRectangle>
          <Components isList="true" count="3">
            <Text218 Ref="282" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,5,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>a967ab2fb2e34983b9ef4b6657dd6e47</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text218</Name>
              <Page isRef="276" />
              <Parent isRef="281" />
              <Text>{出院诊断信息右.DIAGNOSIS_NAME}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="283" type="CustomFormat" isKey="true">
                <StringFormat>0.00##</StringFormat>
              </TextFormat>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text218>
            <Text219 Ref="284" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>5,0,2,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>f3426c544cad4f48990eba26c88730f5</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text219</Name>
              <Page isRef="276" />
              <Parent isRef="281" />
              <Text>{出院诊断信息右.DIAGNOSIS_CD}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="285" type="CustomFormat" isKey="true">
                <StringFormat>0.00##</StringFormat>
              </TextFormat>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text219>
            <Text220 Ref="286" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7,0,2.5,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>33598f8d6e034f49bfd0ae8b632ba6e5</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text220</Name>
              <Page isRef="276" />
              <Parent isRef="281" />
              <Text>{出院诊断信息右.DIAG_RESULT_CODE}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="287" type="CustomFormat" isKey="true">
                <StringFormat>0.00##</StringFormat>
              </TextFormat>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text220>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>出院诊断信息右</DataSourceName>
          <Filters isList="true" count="0" />
          <Guid>8179b730d985431da50590494e74ca7f</Guid>
          <Name>DataBand6</Name>
          <Page isRef="276" />
          <Parent isRef="276" />
          <Sort isList="true" count="0" />
        </DataBand6>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>42d6be8c1e03443bba075f1b6ed8863f</Guid>
      <Margins>0,0,0,0</Margins>
      <Name>subReport_2</Name>
      <PageHeight>8.4</PageHeight>
      <PageWidth>9.5</PageWidth>
      <Report isRef="0" />
      <Watermark Ref="288" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </subReport_2>
  </Pages>
  <PrinterSettings Ref="289" type="Stimulsoft.Report.Print.StiPrinterSettings" isKey="true" />
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>住院病案首页</ReportAlias>
  <ReportChanged>11/20/2020 11:28:22 AM</ReportChanged>
  <ReportCreated>12/8/2011 4:39:33 PM</ReportCreated>
  <ReportFile>C:\Users\<USER>\Desktop\西医病案首页.mrt</ReportFile>
  <ReportGuid>bbe31f4b83ad472d9b347ef3a8a7a15b</ReportGuid>
  <ReportName>住院病案首页</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2011.2.1026</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class Report : Stimulsoft.Report.StiReport
    {
        public Report()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
        public string 医疗机构;
        public string 组织机构代码;
        public string 医疗付费方式;
        public string 病历号;
        public string 姓名;
        public string 性别;
        public DateTime 出生日期;
        public string 年龄;
        public string 国籍;
        public string _年龄不足一周岁的_年龄;
        public string 新生儿出生体重;
        public string 新生儿入院体重;
        public string 出生地;
        public string 籍贯;
        public string 民族;
        public string 身份证号;
        public string 职业;
        public string 婚姻;
        public string 现住址;
        public string 电话;
        public string 邮编;
        public string 户口地址;
        public string 户口邮编;
        public string 工作单位及地址;
        public string 单位电话;
        public string 单位邮编;
        public string 联系人姓名;
        public string 关系;
        public string 地址;
        public string 联系电话;
        public string 入院途径;
        public string 治疗类别;
        public DateTime 入院时间;
        public string 入院科别;
        public string 入院病房;
        public string 转科科别;
        public string 出院时间;
        public string 出院科别;
        public string 出院病房;
        public string 实际住院天数;
        public string 门_急_诊诊断_中医_;
        public string 中医疾病编码;
        public string 门_急_诊诊断_西医_;
        public string 西医疾病编码;
        public string 实施临床路径;
        public string 使用医疗机构中药制剂;
        public string 使用中医诊疗设备;
        public string 使用中医诊疗技术;
        public string 辩证施护;
        public string 入院病情;
        public string 损伤或中毒的外部原因;
        public string 其疾病编码;
        public string 病理诊断;
        public string 病理疾病编码;
        public string 病理号;
        public string 药物过敏;
        public string 死亡患者尸检;
        public string 血型;
        public string RH;
        public string 科主任;
        public string 主任_副主任_医师;
        public string 主治医师;
        public string 住院医师;
        public string 责任护士;
        public string 进修医师;
        public string 实习医师;
        public string 编码员;
        public string 病案质量;
        public string 质控医师;
        public string 质控护士;
        public DateTime 质控日期;
        public Stimulsoft.Report.Components.StiPage Page1;
        public Stimulsoft.Report.Components.StiPageFooterBand PageFooterBand1;
        public Stimulsoft.Report.Components.StiText Text38;
        public Stimulsoft.Report.Components.StiText Text39;
        public Stimulsoft.Report.Components.StiText Text40;
        public Stimulsoft.Report.Dictionary.StiSumDecimalFunctionService Text40_Sum;
        public Stimulsoft.Report.Components.StiText Text41;
        public Stimulsoft.Report.Dictionary.StiSumDecimalFunctionService Text41_Sum;
        public Stimulsoft.Report.Components.StiReportTitleBand ReportTitleBand1;
        public Stimulsoft.Report.Components.StiText Text3;
        public Stimulsoft.Report.Components.StiText Text1;
        public Stimulsoft.Report.Components.StiText Text2;
        public Stimulsoft.Report.Components.StiText Text42;
        public Stimulsoft.Report.Components.StiText Text43;
        public Stimulsoft.Report.Components.StiText Text44;
        public Stimulsoft.Report.Components.StiText Text46;
        public Stimulsoft.Report.Components.StiText Text45;
        public Stimulsoft.Report.Components.StiText Text47;
        public Stimulsoft.Report.Components.StiText Text49;
        public Stimulsoft.Report.Components.StiText Text52;
        public Stimulsoft.Report.Components.StiText Text54;
        public Stimulsoft.Report.Components.StiText Text56;
        public Stimulsoft.Report.Components.StiText Text58;
        public Stimulsoft.Report.Components.StiText Text60;
        public Stimulsoft.Report.Components.StiText Text62;
        public Stimulsoft.Report.Components.StiText Text64;
        public Stimulsoft.Report.Components.StiText Text66;
        public Stimulsoft.Report.Components.StiText Text67;
        public Stimulsoft.Report.Components.StiText Text69;
        public Stimulsoft.Report.Components.StiText Text5;
        public Stimulsoft.Report.Components.StiText Text71;
        public Stimulsoft.Report.Components.StiText Text73;
        public Stimulsoft.Report.Components.StiText Text75;
        public Stimulsoft.Report.Components.StiText Text77;
        public Stimulsoft.Report.Components.StiText Text79;
        public Stimulsoft.Report.Components.StiText Text81;
        public Stimulsoft.Report.Components.StiText Text83;
        public Stimulsoft.Report.Components.StiText Text85;
        public Stimulsoft.Report.Components.StiText Text87;
        public Stimulsoft.Report.Components.StiText Text89;
        public Stimulsoft.Report.Components.StiText Text48;
        public Stimulsoft.Report.Components.StiText Text51;
        public Stimulsoft.Report.Components.StiText Text50;
        public Stimulsoft.Report.Components.StiText Text55;
        public Stimulsoft.Report.Components.StiText Text59;
        public Stimulsoft.Report.Components.StiText Text63;
        public Stimulsoft.Report.Components.StiText Text68;
        public Stimulsoft.Report.Components.StiText Text72;
        public Stimulsoft.Report.Components.StiText Text76;
        public Stimulsoft.Report.Components.StiText Text80;
        public Stimulsoft.Report.Components.StiText Text88;
        public Stimulsoft.Report.Components.StiText Text91;
        public Stimulsoft.Report.Components.StiText Text93;
        public Stimulsoft.Report.Components.StiText Text84;
        public Stimulsoft.Report.Components.StiText Text53;
        public Stimulsoft.Report.Components.StiText Text61;
        public Stimulsoft.Report.Components.StiText Text70;
        public Stimulsoft.Report.Components.StiText Text78;
        public Stimulsoft.Report.Components.StiText Text86;
        public Stimulsoft.Report.Components.StiText Text57;
        public Stimulsoft.Report.Components.StiText Text74;
        public Stimulsoft.Report.Components.StiText Text90;
        public Stimulsoft.Report.Components.StiText Text94;
        public Stimulsoft.Report.Components.StiText Text96;
        public Stimulsoft.Report.Components.StiText Text98;
        public Stimulsoft.Report.Components.StiText Text100;
        public Stimulsoft.Report.Components.StiText Text102;
        public Stimulsoft.Report.Components.StiText Text104;
        public Stimulsoft.Report.Components.StiText Text106;
        public Stimulsoft.Report.Components.StiText Text108;
        public Stimulsoft.Report.Components.StiText Text110;
        public Stimulsoft.Report.Components.StiText Text112;
        public Stimulsoft.Report.Components.StiText Text114;
        public Stimulsoft.Report.Components.StiText Text116;
        public Stimulsoft.Report.Components.StiText Text118;
        public Stimulsoft.Report.Components.StiText Text120;
        public Stimulsoft.Report.Components.StiText Text122;
        public Stimulsoft.Report.Components.StiText Text126;
        public Stimulsoft.Report.Components.StiText Text128;
        public Stimulsoft.Report.Components.StiText Text130;
        public Stimulsoft.Report.Components.StiText Text124;
        public Stimulsoft.Report.Components.StiHeaderBand HeaderBand1;
        public Stimulsoft.Report.Components.StiText Text7;
        public Stimulsoft.Report.Components.StiText Text9;
        public Stimulsoft.Report.Components.StiText Text11;
        public Stimulsoft.Report.Components.StiText Text13;
        public Stimulsoft.Report.Components.StiText Text15;
        public Stimulsoft.Report.Components.StiText Text17;
        public Stimulsoft.Report.Components.StiText Text19;
        public Stimulsoft.Report.Components.StiText Text21;
        public Stimulsoft.Report.Components.StiText Text23;
        public Stimulsoft.Report.Components.StiText Text25;
        public Stimulsoft.Report.Components.StiGroupHeaderBand GroupHeaderBand1;
        public Stimulsoft.Report.Components.StiText Text26;
        public Stimulsoft.Report.Components.StiGroupHeaderBand GroupHeaderBand2;
        public Stimulsoft.Report.Components.StiText Text27;
        public Stimulsoft.Report.Components.StiDataBand DataBand1;
        public Stimulsoft.Report.Components.StiText Text6;
        public Stimulsoft.Report.Components.StiText Text8;
        public Stimulsoft.Report.Components.StiText Text10;
        public Stimulsoft.Report.Components.StiText Text12;
        public Stimulsoft.Report.Components.StiText Text14;
        public Stimulsoft.Report.Components.StiText Text16;
        public Stimulsoft.Report.Components.StiText Text18;
        public Stimulsoft.Report.Components.StiText Text20;
        public Stimulsoft.Report.Components.StiText Text22;
        public Stimulsoft.Report.Components.StiText Text24;
        public Stimulsoft.Report.Components.StiGroupFooterBand GroupFooterBand2;
        public Stimulsoft.Report.Components.StiText Text28;
        public Stimulsoft.Report.Components.StiText Text29;
        public Stimulsoft.Report.Components.StiText Text30;
        public Stimulsoft.Report.Components.StiText Text31;
        public Stimulsoft.Report.Dictionary.StiSumDecimalFunctionService Text31_Sum;
        public Stimulsoft.Report.Components.StiText Text32;
        public Stimulsoft.Report.Dictionary.StiSumDecimalFunctionService Text32_Sum;
        public Stimulsoft.Report.Components.StiGroupFooterBand GroupFooterBand1;
        public Stimulsoft.Report.Components.StiText Text33;
        public Stimulsoft.Report.Components.StiText Text34;
        public Stimulsoft.Report.Components.StiText Text35;
        public Stimulsoft.Report.Components.StiText Text36;
        public Stimulsoft.Report.Dictionary.StiSumDecimalFunctionService Text36_Sum;
        public Stimulsoft.Report.Components.StiText Text37;
        public Stimulsoft.Report.Dictionary.StiSumDecimalFunctionService Text37_Sum;
        public Stimulsoft.Report.Components.StiReportSummaryBand ReportSummaryBand1;
        public Stimulsoft.Report.Components.StiWatermark Page1_Watermark;
        public Stimulsoft.Report.Print.StiPrinterSettings 药库盘点表_PrinterSettings;
        public view明细DataSource view明细;
        
        public void Text38__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "#%#第{PageNumber}页";
            e.StoreToPrinted = true;
        }
        
        public System.String Text38_GetValue_End(Stimulsoft.Report.Components.StiComponent sender)
        {
            return "第" + ToString(sender, PageNumber, true) + "页";
        }
        
        public void Text39__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "合      计:";
        }
        
        public void Text40__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "#%#{Sum(DataBand1,view明细.Pd_CgMoney)}";
            e.StoreToPrinted = true;
        }
        
        public System.String Text40_GetValue_End(Stimulsoft.Report.Components.StiComponent sender)
        {
            return this.Text40.TextFormat.Format(CheckExcelValue(sender, ((decimal)(StiReport.ChangeType(this.Text40_Sum.GetValue(), typeof(decimal), true)))));
        }
        
        public void Text41__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "#%#{Sum(DataBand1,view明细.Pd_XsMoney)}";
            e.StoreToPrinted = true;
        }
        
        public System.String Text41_GetValue_End(Stimulsoft.Report.Components.StiComponent sender)
        {
            return this.Text41.TextFormat.Format(CheckExcelValue(sender, ((decimal)(StiReport.ChangeType(this.Text41_Sum.GetValue(), typeof(decimal), true)))));
        }
        
        public void Text3__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "医疗机构：" + ToString(sender, 医疗机构, true);
        }
        
        public void Text1__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "中 医 住 院 病 案 首 页";
        }
        
        public void Text2__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "组织机构代码：" + ToString(sender, 组织机构代码, true);
        }
        
        public void Text42__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "医疗付费方式：" + ToString(sender, 医疗付费方式, true);
        }
        
        public void Text43__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "病历号：" + ToString(sender, 病历号, true);
        }
        
        public void Text44__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "姓名：" + ToString(sender, 姓名, true);
        }
        
        public void Text46__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "性别：" + ToString(sender, 性别, true);
        }
        
        public void Text45__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "出生日期：" + ToString(sender, 出生日期, true);
        }
        
        public void Text47__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "年龄：" + ToString(sender, 年龄, true);
        }
        
        public void Text49__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "国籍：" + ToString(sender, 国籍, true);
        }
        
        public void Text52__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "(年龄不足一周岁的)年龄：" + ToString(sender, (年龄不足一周岁的)年龄, true);
        }
        
        public void Text54__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "新生儿出生体重：" + ToString(sender, 新生儿出生体重, true);
        }
        
        public void Text56__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "新生儿入院体重：" + ToString(sender, 新生儿入院体重, true);
        }
        
        public void Text58__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "出生地：" + ToString(sender, 出生地, true);
        }
        
        public void Text60__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "籍贯：" + ToString(sender, 籍贯, true);
        }
        
        public void Text62__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "民族：" + ToString(sender, 民族, true);
        }
        
        public void Text64__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "身份证号：" + ToString(sender, 身份证号, true);
        }
        
        public void Text66__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "职业：" + ToString(sender, 职业, true);
        }
        
        public void Text67__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "婚姻：" + ToString(sender, 婚姻, true);
        }
        
        public void Text69__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "现住址：" + ToString(sender, 现住址, true);
        }
        
        public void Text5__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "电话：" + ToString(sender, 电话, true);
        }
        
        public void Text71__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "邮编：" + ToString(sender, 邮编, true);
        }
        
        public void Text73__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "户口地址：" + ToString(sender, 户口地址, true);
        }
        
        public void Text75__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "户口邮编：" + ToString(sender, 户口邮编, true);
        }
        
        public void Text77__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "工作单位及地址：" + ToString(sender, 工作单位及地址, true);
        }
        
        public void Text79__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "单位电话：" + ToString(sender, 单位电话, true);
        }
        
        public void Text81__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "单位邮编：" + ToString(sender, 单位邮编, true);
        }
        
        public void Text83__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "联系人姓名：" + ToString(sender, 联系人姓名, true);
        }
        
        public void Text85__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "关系：" + ToString(sender, 关系, true);
        }
        
        public void Text87__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "地址：" + ToString(sender, 地址, true);
        }
        
        public void Text89__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "联系电话：" + ToString(sender, 联系电话, true);
        }
        
        public void Text48__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "入院途径:" + ToString(sender, 入院途径, true);
        }
        
        public void Text51__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "治疗类别:" + ToString(sender, 治疗类别, true);
        }
        
        public void Text50__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "入院时间:" + ToString(sender, 入院时间, true);
        }
        
        public void Text55__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "入院科别:" + ToString(sender, 入院科别, true);
        }
        
        public void Text59__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "入院病房:" + ToString(sender, 入院病房, true);
        }
        
        public void Text63__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "转科科别:" + ToString(sender, 转科科别, true);
        }
        
        public void Text68__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "出院时间:" + ToString(sender, 出院时间, true);
        }
        
        public void Text72__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "出院科别:" + ToString(sender, 出院科别, true);
        }
        
        public void Text76__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "出院病房:" + ToString(sender, 出院病房, true);
        }
        
        public void Text80__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "实际住院天数:" + ToString(sender, 实际住院天数, true);
        }
        
        public void Text88__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "中医疾病编码";
        }
        
        public void Text91__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "门(急)诊诊断(西医):" + ToString(sender, 门(急)诊诊断(西医), true);
        }
        
        public void Text93__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "西医疾病编码";
        }
        
        public void Text84__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "门(急)诊诊断(中医):" + ToString(sender, 门(急)诊诊断(中医), true);
        }
        
        public void Text53__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "实施临床路径:" + ToString(sender, 实施临床路径, true);
        }
        
        public void Text61__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "使用医疗机构中药制剂:" + ToString(sender, 使用医疗机构中药制剂, true);
        }
        
        public void Text70__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "使用中医诊疗设备:" + ToString(sender, 使用中医诊疗设备, true);
        }
        
        public void Text78__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "使用中医诊疗技术:" + ToString(sender, 使用中医诊疗技术, true);
        }
        
        public void Text86__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "辩证施护:" + ToString(sender, 辩证施护, true);
        }
        
        public void Text57__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "入院病情：" + ToString(sender, 入院病情, true);
        }
        
        public void Text74__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "损伤或中毒的外部原因：" + ToString(sender, 损伤或中毒的外部原因, true);
        }
        
        public void Text90__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "其疾病编码：" + ToString(sender, 其疾病编码, true);
        }
        
        public void Text94__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "病理诊断：" + ToString(sender, 病理诊断, true);
        }
        
        public void Text96__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "病理疾病编码：" + ToString(sender, 病理疾病编码, true);
        }
        
        public void Text98__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "病理号：" + ToString(sender, 病理号, true);
        }
        
        public void Text100__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "药物过敏：" + ToString(sender, 药物过敏, true);
        }
        
        public void Text102__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "死亡患者尸检：" + ToString(sender, 死亡患者尸检, true);
        }
        
        public void Text104__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "血型：" + ToString(sender, 血型, true);
        }
        
        public void Text106__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "RH：" + ToString(sender, RH, true);
        }
        
        public void Text108__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "科主任：" + ToString(sender, 科主任, true);
        }
        
        public void Text110__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "主任(副主任)医师：" 
        }
        
        public void Text112__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "主治医师：" + ToString(sender, 主治医师, true);
        }
        
        public void Text114__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "住院医师：" + ToString(sender, 住院医师, true);
        }
        
        public void Text116__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "责任护士：" + ToString(sender, 责任护士, true);
        }
        
        public void Text118__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "进修医师：" + ToString(sender, 进修医师, true);
        }
        
        public void Text120__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "实习医师：" + ToString(sender, 实习医师, true);
        }
        
        public void Text122__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "编码员：" + ToString(sender, 编码员, true);
        }
        
        public void Text126__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "质控医师：" + ToString(sender, 质控医师, true);
        }
        
        public void Text128__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "质控护士：" + ToString(sender, 质控护士, true);
        }
        
        public void Text130__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "质控日期：" + ToString(sender, 质控日期, true);
        }
        
        public void Text124__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "病案质量：" + ToString(sender, 病案质量, true);
        }
        
        public void Text7__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "药品名称";
        }
        
        public void Text9__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "规格";
        }
        
        public void Text11__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "产地";
        }
        
        public void Text13__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "单位";
        }
        
        public void Text15__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "有效期";
        }
        
        public void Text17__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "盘点数量";
        }
        
        public void Text19__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "采购价";
        }
        
        public void Text21__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "采购金额";
        }
        
        public void Text23__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "销售价";
        }
        
        public void Text25__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "销售金额";
        }
        
        public void GroupHeaderBand1__GetValue(object sender, Stimulsoft.Report.Events.StiValueEventArgs e)
        {
            e.Value = view明细.IsJb;
        }
        
        public void Text26__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, view明细.IsJb, true);
        }
        
        public void GroupHeaderBand2__GetValue(object sender, Stimulsoft.Report.Events.StiValueEventArgs e)
        {
            e.Value = view明细.Dl_Code;
        }
        
        public void Text27__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, view明细.Dl_Name, true);
        }
        
        public void Text6__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, view明细.Yp_Name, true);
        }
        
        public void Text8__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, view明细.Mx_Gg, true);
        }
        
        public void Text10__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, view明细.Mx_Cd, true);
        }
        
        public void Text12__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, view明细.Yp_Dw, true);
        }
        
        public void Text14__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = System.String.Format("{0:yyyy-MM-dd}", view明细.Yp_Yxq);
        }
        
        public void Text16__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = System.String.Format("{0:0.####}", view明细.Pd_Sl);
        }
        
        public void Text18__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = System.String.Format("{0:0.00##}", view明细.Yp_Cgj);
        }
        
        public void Text20__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = System.String.Format("{0:0.00##}", view明细.Pd_CgMoney);
        }
        
        public void Text22__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = System.String.Format("{0:0.00##}", view明细.Yp_Xsj);
        }
        
        public void Text24__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = System.String.Format("{0:0.00##}", view明细.Pd_XsMoney);
        }
        
        public void Text28__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, view明细.Dl_Name, true);
        }
        
        public void Text29__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "小计:";
        }
        
        public void Text31__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "#%#{Sum(GroupHeaderBand2,view明细.Pd_CgMoney)}";
            e.StoreToPrinted = true;
        }
        
        public System.String Text31_GetValue_End(Stimulsoft.Report.Components.StiComponent sender)
        {
            return this.Text31.TextFormat.Format(CheckExcelValue(sender, ((decimal)(StiReport.ChangeType(this.Text31_Sum.GetValue(), typeof(decimal), true)))));
        }
        
        public void Text32__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "#%#{Sum(GroupHeaderBand2,view明细.Pd_XsMoney)}";
            e.StoreToPrinted = true;
        }
        
        public System.String Text32_GetValue_End(Stimulsoft.Report.Components.StiComponent sender)
        {
            return this.Text32.TextFormat.Format(CheckExcelValue(sender, ((decimal)(StiReport.ChangeType(this.Text32_Sum.GetValue(), typeof(decimal), true)))));
        }
        
        public void Text33__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, view明细.IsJb, true);
        }
        
        public void Text34__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "小计:";
        }
        
        public void Text36__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "#%#{Sum(GroupHeaderBand1,view明细.Pd_CgMoney)}";
            e.StoreToPrinted = true;
        }
        
        public System.String Text36_GetValue_End(Stimulsoft.Report.Components.StiComponent sender)
        {
            return this.Text36.TextFormat.Format(CheckExcelValue(sender, ((decimal)(StiReport.ChangeType(this.Text36_Sum.GetValue(), typeof(decimal), true)))));
        }
        
        public void Text37__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "#%#{Sum(GroupHeaderBand1,view明细.Pd_XsMoney)}";
            e.StoreToPrinted = true;
        }
        
        public System.String Text37_GetValue_End(Stimulsoft.Report.Components.StiComponent sender)
        {
            return this.Text37.TextFormat.Format(CheckExcelValue(sender, ((decimal)(StiReport.ChangeType(this.Text37_Sum.GetValue(), typeof(decimal), true)))));
        }
        
        public void DataBand1__BeginRender(object sender, System.EventArgs e)
        {
            this.Text40_Sum.Init();
            this.Text40.TextValue = "";
            this.Text41_Sum.Init();
            this.Text41.TextValue = "";
        }
        
        public void DataBand1__EndRender(object sender, System.EventArgs e)
        {
            this.Text40.SetText(new Stimulsoft.Report.Components.StiGetValue(this.Text40_GetValue_End));
            this.Text41.SetText(new Stimulsoft.Report.Components.StiGetValue(this.Text41_GetValue_End));
        }
        
        public void GroupHeaderBand2__BeginRender(object sender, System.EventArgs e)
        {
            this.Text31_Sum.Init();
            this.Text31.TextValue = "";
            this.Text32_Sum.Init();
            this.Text32.TextValue = "";
        }
        
        public void GroupHeaderBand2__EndRender(object sender, System.EventArgs e)
        {
            this.Text31.SetText(new Stimulsoft.Report.Components.StiGetValue(this.Text31_GetValue_End));
            this.Text32.SetText(new Stimulsoft.Report.Components.StiGetValue(this.Text32_GetValue_End));
        }
        
        public void GroupHeaderBand1__BeginRender(object sender, System.EventArgs e)
        {
            this.Text36_Sum.Init();
            this.Text36.TextValue = "";
            this.Text37_Sum.Init();
            this.Text37.TextValue = "";
        }
        
        public void GroupHeaderBand1__EndRender(object sender, System.EventArgs e)
        {
            this.Text36.SetText(new Stimulsoft.Report.Components.StiGetValue(this.Text36_GetValue_End));
            this.Text37.SetText(new Stimulsoft.Report.Components.StiGetValue(this.Text37_GetValue_End));
        }
        
        public void DataBand1__Rendering(object sender, System.EventArgs e)
        {
            this.Text40_Sum.CalcItem(view明细.Pd_CgMoney);
            this.Text41_Sum.CalcItem(view明细.Pd_XsMoney);
        }
        
        public void GroupHeaderBand2__Rendering(object sender, System.EventArgs e)
        {
            this.Text31_Sum.CalcItem(view明细.Pd_CgMoney);
            this.Text32_Sum.CalcItem(view明细.Pd_XsMoney);
        }
        
        public void GroupHeaderBand1__Rendering(object sender, System.EventArgs e)
        {
            this.Text36_Sum.CalcItem(view明细.Pd_CgMoney);
            this.Text37_Sum.CalcItem(view明细.Pd_XsMoney);
        }
        
        public void 药库盘点表WordsToEnd__EndRender(object sender, System.EventArgs e)
        {
            this.Text38.SetText(new Stimulsoft.Report.Components.StiGetValue(this.Text38_GetValue_End));
        }
        
        private void InitializeComponent()
        {
            this.view明细 = new view明细DataSource();
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "医疗机构", "医疗机构", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "组织机构代码", "组织机构代码", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "医疗付费方式", "医疗付费方式", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "病历号", "病历号", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "姓名", "姓名", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "性别", "性别", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "出生日期", "出生日期", "", typeof(DateTime), "10/25/2020 2:36:27 PM", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "年龄", "年龄", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "国籍", "国籍", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "(年龄不足一周岁的)年龄", "(年龄不足一周岁的)年龄", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "新生儿出生体重", "新生儿出生体重", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "新生儿入院体重", "新生儿入院体重", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "出生地", "出生地", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "籍贯", "籍贯", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "民族", "民族", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "身份证号", "身份证号", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "职业", "职业", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "婚姻", "婚姻", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "现住址", "现住址", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "电话", "电话", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "邮编", "邮编", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "户口地址", "户口地址", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "户口邮编", "户口邮编", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "工作单位及地址", "工作单位及地址", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "单位电话", "单位电话", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "单位邮编", "单位邮编", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "联系人姓名", "联系人姓名", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "关系", "关系", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "地址", "地址", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "联系电话", "联系电话", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "入院途径", "入院途径", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "治疗类别", "治疗类别", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "入院时间", "入院时间", "", typeof(DateTime), "10/25/2020 3:26:39 PM", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "入院科别", "入院科别", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "入院病房", "入院病房", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "转科科别", "转科科别", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "出院时间", "出院时间", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "出院科别", "出院科别", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "出院病房", "出院病房", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "实际住院天数", "实际住院天数", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "门(急)诊诊断(中医)", "门(急)诊诊断(中医)", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "中医疾病编码", "中医疾病编码", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "门(急)诊诊断(西医)", "门(急)诊诊断(西医)", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "西医疾病编码", "西医疾病编码", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "实施临床路径", "实施临床路径", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "使用医疗机构中药制剂", "使用医疗机构中药制剂", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "使用中医诊疗设备", "使用中医诊疗设备", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "使用中医诊疗技术", "使用中医诊疗技术", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "辩证施护", "辩证施护", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "入院病情", "入院病情", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "损伤或中毒的外部原因", "损伤或中毒的外部原因", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "其疾病编码", "其疾病编码", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "病理诊断", "病理诊断", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "病理疾病编码", "病理疾病编码", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "病理号", "病理号", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "药物过敏", "药物过敏", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "死亡患者尸检", "死亡患者尸检", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "血型", "血型", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "RH", "RH", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "科主任", "科主任", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "主任(副主任)医师", "主任(副主任)医师", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "主治医师", "主治医师", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "住院医师", "住院医师", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "责任护士", "责任护士", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "进修医师", "进修医师", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "实习医师", "实习医师", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "编码员", "编码员", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "病案质量", "病案质量", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "质控医师", "质控医师", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "质控护士", "质控护士", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "质控日期", "质控日期", "", typeof(DateTime), "10/25/2020 4:04:01 PM", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.NeedsCompiling = false;
            this.Text37_Sum = new Stimulsoft.Report.Dictionary.StiSumDecimalFunctionService();
            this.Text36_Sum = new Stimulsoft.Report.Dictionary.StiSumDecimalFunctionService();
            this.Text32_Sum = new Stimulsoft.Report.Dictionary.StiSumDecimalFunctionService();
            this.Text31_Sum = new Stimulsoft.Report.Dictionary.StiSumDecimalFunctionService();
            this.Text41_Sum = new Stimulsoft.Report.Dictionary.StiSumDecimalFunctionService();
            this.Text40_Sum = new Stimulsoft.Report.Dictionary.StiSumDecimalFunctionService();
            // 
            // Variables init
            // 
            this.医疗机构 = "";
            this.组织机构代码 = "";
            this.医疗付费方式 = "";
            this.病历号 = "";
            this.姓名 = "";
            this.性别 = "";
            this.出生日期 = ParseDateTime("10/25/2020 2:36:27 PM");
            this.年龄 = "";
            this.国籍 = "";
            this.(年龄不足一周岁的)年龄 = "";
            this.新生儿出生体重 = "";
            this.新生儿入院体重 = "";
            this.出生地 = "";
            this.籍贯 = "";
            this.民族 = "";
            this.身份证号 = "";
            this.职业 = "";
            this.婚姻 = "";
            this.现住址 = "";
            this.电话 = "";
            this.邮编 = "";
            this.户口地址 = "";
            this.户口邮编 = "";
            this.工作单位及地址 = "";
            this.单位电话 = "";
            this.单位邮编 = "";
            this.联系人姓名 = "";
            this.关系 = "";
            this.地址 = "";
            this.联系电话 = "";
            this.入院途径 = "";
            this.治疗类别 = "";
            this.入院时间 = ParseDateTime("10/25/2020 3:26:39 PM");
            this.入院科别 = "";
            this.入院病房 = "";
            this.转科科别 = "";
            this.出院时间 = "";
            this.出院科别 = "";
            this.出院病房 = "";
            this.实际住院天数 = "";
            this.门(急)诊诊断(中医) = "";
            this.中医疾病编码 = "";
            this.门(急)诊诊断(西医) = "";
            this.西医疾病编码 = "";
            this.实施临床路径 = "";
            this.使用医疗机构中药制剂 = "";
            this.使用中医诊疗设备 = "";
            this.使用中医诊疗技术 = "";
            this.辩证施护 = "";
            this.入院病情 = "";
            this.损伤或中毒的外部原因 = "";
            this.其疾病编码 = "";
            this.病理诊断 = "";
            this.病理疾病编码 = "";
            this.病理号 = "";
            this.药物过敏 = "";
            this.死亡患者尸检 = "";
            this.血型 = "";
            this.RH = "";
            this.科主任 = "";
            this.主任(副主任)医师 = "";
            this.主治医师 = "";
            this.住院医师 = "";
            this.责任护士 = "";
            this.进修医师 = "";
            this.实习医师 = "";
            this.编码员 = "";
            this.病案质量 = "";
            this.质控医师 = "";
            this.质控护士 = "";
            this.质控日期 = ParseDateTime("10/25/2020 4:04:01 PM");
            this.EngineVersion = Stimulsoft.Report.Engine.StiEngineVersion.EngineV2;
            this.ReferencedAssemblies = new System.String[] {
                    "System.Dll",
                    "System.Drawing.Dll",
                    "System.Windows.Forms.Dll",
                    "System.Data.Dll",
                    "System.Xml.Dll",
                    "Stimulsoft.Controls.Dll",
                    "Stimulsoft.Base.Dll",
                    "Stimulsoft.Report.Dll"};
            this.ReportAlias = "药库盘点表";
            // 
            // ReportChanged
            // 
            this.ReportChanged = new DateTime(2020, 10, 25, 16, 21, 56, 643);
            // 
            // ReportCreated
            // 
            this.ReportCreated = new DateTime(2011, 12, 8, 16, 39, 33, 0);
            this.ReportFile = "C:\\Users\\<USER>\\Desktop\\药库药房盘点表.mrt";
            this.ReportGuid = "e58ad29c769144289ffbad6b82af825b";
            this.ReportName = "药库盘点表";
            this.ReportUnit = Stimulsoft.Report.StiReportUnitType.Centimeters;
            this.ReportVersion = "2011.2.1026";
            this.ScriptLanguage = Stimulsoft.Report.StiReportLanguageType.CSharp;
            // 
            // Page1
            // 
            this.Page1 = new Stimulsoft.Report.Components.StiPage();
            this.Page1.Guid = "b34eae1e5edd44cdb0bd8cc5d316eea3";
            this.Page1.Name = "Page1";
            this.Page1.PageHeight = 29.7;
            this.Page1.PageWidth = 21;
            this.Page1.PaperSize = System.Drawing.Printing.PaperKind.A4;
            this.Page1.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 2, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Page1.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            // 
            // PageFooterBand1
            // 
            this.PageFooterBand1 = new Stimulsoft.Report.Components.StiPageFooterBand();
            this.PageFooterBand1.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 27.2, 19, 0.5);
            this.PageFooterBand1.Name = "PageFooterBand1";
            this.PageFooterBand1.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.PageFooterBand1.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            // 
            // Text38
            // 
            this.Text38 = new Stimulsoft.Report.Components.StiText();
            this.Text38.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 0, 19, 0.5);
            this.Text38.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text38.Name = "Text38";
            this.Text38.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text38__GetValue);
            this.Text38.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text38.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text38.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text38.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text38.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text38.Guid = null;
            this.Text38.Indicator = null;
            this.Text38.Interaction = null;
            this.Text38.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text38.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text38.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text39
            // 
            this.Text39 = new Stimulsoft.Report.Components.StiText();
            this.Text39.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 0, 3.2, 0.5);
            this.Text39.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text39.Name = "Text39";
            this.Text39.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text39__GetValue);
            this.Text39.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text39.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text39.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.All, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text39.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text39.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 134);
            this.Text39.Guid = null;
            this.Text39.Indicator = null;
            this.Text39.Interaction = null;
            this.Text39.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text39.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text39.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text40
            // 
            this.Text40 = new Stimulsoft.Report.Components.StiText();
            this.Text40.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(3.2, 0, 12.4, 0.5);
            this.Text40.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text40.Name = "Text40";
            // 
            // Text40_Sum
            // 
            this.Text40.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text40__GetValue);
            this.Text40.Type = Stimulsoft.Report.Components.StiSystemTextType.Totals;
            this.Text40.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text40.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.All, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text40.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text40.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 134);
            this.Text40.Guid = null;
            this.Text40.Indicator = null;
            this.Text40.Interaction = null;
            this.Text40.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text40.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text40.TextFormat = new Stimulsoft.Report.Components.TextFormats.StiNumberFormatService(1, ".", 2, ",", 3, false, true, " ");
            this.Text40.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text41
            // 
            this.Text41 = new Stimulsoft.Report.Components.StiText();
            this.Text41.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(15.6, 0, 3.4, 0.5);
            this.Text41.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text41.Name = "Text41";
            // 
            // Text41_Sum
            // 
            this.Text41.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text41__GetValue);
            this.Text41.Type = Stimulsoft.Report.Components.StiSystemTextType.Totals;
            this.Text41.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text41.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.All, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text41.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text41.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 134);
            this.Text41.Guid = null;
            this.Text41.Indicator = null;
            this.Text41.Interaction = null;
            this.Text41.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text41.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text41.TextFormat = new Stimulsoft.Report.Components.TextFormats.StiNumberFormatService(1, ".", 2, ",", 3, false, true, " ");
            this.Text41.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            this.PageFooterBand1.Guid = null;
            this.PageFooterBand1.Interaction = null;
            // 
            // ReportTitleBand1
            // 
            this.ReportTitleBand1 = new Stimulsoft.Report.Components.StiReportTitleBand();
            this.ReportTitleBand1.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 0.4, 19, 18.2);
            this.ReportTitleBand1.Name = "ReportTitleBand1";
            this.ReportTitleBand1.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.ReportTitleBand1.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            // 
            // Text3
            // 
            this.Text3 = new Stimulsoft.Report.Components.StiText();
            this.Text3.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 0.1, 3.4, 0.5);
            this.Text3.Name = "Text3";
            this.Text3.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text3__GetValue);
            this.Text3.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text3.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text3.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text3.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text3.Font = new System.Drawing.Font("宋体", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 134);
            this.Text3.Guid = null;
            this.Text3.Indicator = null;
            this.Text3.Interaction = null;
            this.Text3.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text3.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text3.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text1
            // 
            this.Text1 = new Stimulsoft.Report.Components.StiText();
            this.Text1.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 1.2, 19, 0.8);
            this.Text1.Guid = "ac000e001dd94b16a743b2ea6bf49e88";
            this.Text1.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text1.Name = "Text1";
            this.Text1.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text1__GetValue);
            this.Text1.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text1.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text1.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text1.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text1.Font = new System.Drawing.Font("黑体", 15F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 134);
            this.Text1.Indicator = null;
            this.Text1.Interaction = null;
            this.Text1.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text1.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text1.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text2
            // 
            this.Text2 = new Stimulsoft.Report.Components.StiText();
            this.Text2.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(3.4, 0.1, 4.6, 0.5);
            this.Text2.Name = "Text2";
            this.Text2.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text2__GetValue);
            this.Text2.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text2.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text2.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text2.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text2.Font = new System.Drawing.Font("宋体", 9.75F);
            this.Text2.Guid = null;
            this.Text2.Indicator = null;
            this.Text2.Interaction = null;
            this.Text2.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text2.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text2.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text42
            // 
            this.Text42 = new Stimulsoft.Report.Components.StiText();
            this.Text42.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 0.7, 4.4, 0.5);
            this.Text42.Name = "Text42";
            this.Text42.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text42__GetValue);
            this.Text42.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text42.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text42.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text42.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text42.Font = new System.Drawing.Font("Arial", 8F);
            this.Text42.Guid = null;
            this.Text42.Indicator = null;
            this.Text42.Interaction = null;
            this.Text42.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text42.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text42.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text43
            // 
            this.Text43 = new Stimulsoft.Report.Components.StiText();
            this.Text43.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(15.8, 0.7, 3.2, 0.5);
            this.Text43.Name = "Text43";
            this.Text43.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text43__GetValue);
            this.Text43.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text43.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text43.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text43.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text43.Font = new System.Drawing.Font("Arial", 8F);
            this.Text43.Guid = null;
            this.Text43.Indicator = null;
            this.Text43.Interaction = null;
            this.Text43.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text43.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text43.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text44
            // 
            this.Text44 = new Stimulsoft.Report.Components.StiText();
            this.Text44.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 2, 3.8, 0.5);
            this.Text44.Name = "Text44";
            this.Text44.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text44__GetValue);
            this.Text44.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text44.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text44.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text44.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text44.Font = new System.Drawing.Font("Arial", 8F);
            this.Text44.Guid = null;
            this.Text44.Indicator = null;
            this.Text44.Interaction = null;
            this.Text44.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text44.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text44.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text46
            // 
            this.Text46 = new Stimulsoft.Report.Components.StiText();
            this.Text46.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(3.8, 2, 3.4, 0.5);
            this.Text46.Name = "Text46";
            this.Text46.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text46__GetValue);
            this.Text46.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text46.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text46.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text46.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text46.Font = new System.Drawing.Font("Arial", 8F);
            this.Text46.Guid = null;
            this.Text46.Indicator = null;
            this.Text46.Interaction = null;
            this.Text46.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text46.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text46.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text45
            // 
            this.Text45 = new Stimulsoft.Report.Components.StiText();
            this.Text45.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(7.2, 2, 4.4, 0.5);
            this.Text45.Name = "Text45";
            this.Text45.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text45__GetValue);
            this.Text45.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text45.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text45.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text45.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text45.Font = new System.Drawing.Font("Arial", 8F);
            this.Text45.Guid = null;
            this.Text45.Indicator = null;
            this.Text45.Interaction = null;
            this.Text45.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text45.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text45.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text47
            // 
            this.Text47 = new Stimulsoft.Report.Components.StiText();
            this.Text47.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(11.6, 2, 3.6, 0.5);
            this.Text47.Name = "Text47";
            this.Text47.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text47__GetValue);
            this.Text47.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text47.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text47.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text47.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text47.Font = new System.Drawing.Font("Arial", 8F);
            this.Text47.Guid = null;
            this.Text47.Indicator = null;
            this.Text47.Interaction = null;
            this.Text47.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text47.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text47.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text49
            // 
            this.Text49 = new Stimulsoft.Report.Components.StiText();
            this.Text49.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(15.2, 2, 3.8, 0.5);
            this.Text49.Name = "Text49";
            this.Text49.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text49__GetValue);
            this.Text49.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text49.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text49.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text49.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text49.Font = new System.Drawing.Font("Arial", 8F);
            this.Text49.Guid = null;
            this.Text49.Indicator = null;
            this.Text49.Interaction = null;
            this.Text49.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text49.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text49.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text52
            // 
            this.Text52 = new Stimulsoft.Report.Components.StiText();
            this.Text52.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 2.4, 7.2, 0.5);
            this.Text52.Name = "Text52";
            this.Text52.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text52__GetValue);
            this.Text52.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text52.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text52.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text52.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text52.Font = new System.Drawing.Font("Arial", 8F);
            this.Text52.Guid = null;
            this.Text52.Indicator = null;
            this.Text52.Interaction = null;
            this.Text52.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text52.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text52.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text54
            // 
            this.Text54 = new Stimulsoft.Report.Components.StiText();
            this.Text54.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(7.2, 2.4, 5.6, 0.5);
            this.Text54.Name = "Text54";
            this.Text54.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text54__GetValue);
            this.Text54.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text54.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text54.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text54.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text54.Font = new System.Drawing.Font("Arial", 8F);
            this.Text54.Guid = null;
            this.Text54.Indicator = null;
            this.Text54.Interaction = null;
            this.Text54.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text54.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text54.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text56
            // 
            this.Text56 = new Stimulsoft.Report.Components.StiText();
            this.Text56.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(12.8, 2.4, 6.2, 0.5);
            this.Text56.Name = "Text56";
            this.Text56.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text56__GetValue);
            this.Text56.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text56.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text56.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text56.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text56.Font = new System.Drawing.Font("Arial", 8F);
            this.Text56.Guid = null;
            this.Text56.Indicator = null;
            this.Text56.Interaction = null;
            this.Text56.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text56.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text56.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text58
            // 
            this.Text58 = new Stimulsoft.Report.Components.StiText();
            this.Text58.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 2.9, 8.8, 0.5);
            this.Text58.Name = "Text58";
            this.Text58.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text58__GetValue);
            this.Text58.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text58.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text58.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text58.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text58.Font = new System.Drawing.Font("Arial", 8F);
            this.Text58.Guid = null;
            this.Text58.Indicator = null;
            this.Text58.Interaction = null;
            this.Text58.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text58.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text58.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text60
            // 
            this.Text60 = new Stimulsoft.Report.Components.StiText();
            this.Text60.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(8.8, 2.8, 6, 0.5);
            this.Text60.Name = "Text60";
            this.Text60.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text60__GetValue);
            this.Text60.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text60.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text60.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text60.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text60.Font = new System.Drawing.Font("Arial", 8F);
            this.Text60.Guid = null;
            this.Text60.Indicator = null;
            this.Text60.Interaction = null;
            this.Text60.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text60.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text60.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text62
            // 
            this.Text62 = new Stimulsoft.Report.Components.StiText();
            this.Text62.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(14.8, 2.8, 4.2, 0.5);
            this.Text62.Name = "Text62";
            this.Text62.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text62__GetValue);
            this.Text62.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text62.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text62.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text62.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text62.Font = new System.Drawing.Font("Arial", 8F);
            this.Text62.Guid = null;
            this.Text62.Indicator = null;
            this.Text62.Interaction = null;
            this.Text62.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text62.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text62.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text64
            // 
            this.Text64 = new Stimulsoft.Report.Components.StiText();
            this.Text64.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 3.2, 7.4, 0.5);
            this.Text64.Name = "Text64";
            this.Text64.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text64__GetValue);
            this.Text64.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text64.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text64.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text64.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text64.Font = new System.Drawing.Font("Arial", 8F);
            this.Text64.Guid = null;
            this.Text64.Indicator = null;
            this.Text64.Interaction = null;
            this.Text64.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text64.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text64.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text66
            // 
            this.Text66 = new Stimulsoft.Report.Components.StiText();
            this.Text66.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(7.4, 3.2, 7.4, 0.5);
            this.Text66.Name = "Text66";
            this.Text66.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text66__GetValue);
            this.Text66.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text66.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text66.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text66.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text66.Font = new System.Drawing.Font("Arial", 8F);
            this.Text66.Guid = null;
            this.Text66.Indicator = null;
            this.Text66.Interaction = null;
            this.Text66.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text66.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text66.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text67
            // 
            this.Text67 = new Stimulsoft.Report.Components.StiText();
            this.Text67.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(14.8, 3.2, 4.2, 0.5);
            this.Text67.Name = "Text67";
            this.Text67.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text67__GetValue);
            this.Text67.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text67.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text67.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text67.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text67.Font = new System.Drawing.Font("Arial", 8F);
            this.Text67.Guid = null;
            this.Text67.Indicator = null;
            this.Text67.Interaction = null;
            this.Text67.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text67.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text67.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text69
            // 
            this.Text69 = new Stimulsoft.Report.Components.StiText();
            this.Text69.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 3.6, 8.8, 0.5);
            this.Text69.Name = "Text69";
            this.Text69.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text69__GetValue);
            this.Text69.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text69.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text69.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text69.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text69.Font = new System.Drawing.Font("Arial", 8F);
            this.Text69.Guid = null;
            this.Text69.Indicator = null;
            this.Text69.Interaction = null;
            this.Text69.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text69.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text69.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text5
            // 
            this.Text5 = new Stimulsoft.Report.Components.StiText();
            this.Text5.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(8.8, 3.6, 5.4, 0.5);
            this.Text5.Name = "Text5";
            this.Text5.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text5__GetValue);
            this.Text5.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text5.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text5.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text5.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text5.Font = new System.Drawing.Font("Arial", 8F);
            this.Text5.Guid = null;
            this.Text5.Indicator = null;
            this.Text5.Interaction = null;
            this.Text5.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text5.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text5.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text71
            // 
            this.Text71 = new Stimulsoft.Report.Components.StiText();
            this.Text71.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(14.2, 3.6, 4.8, 0.5);
            this.Text71.Name = "Text71";
            this.Text71.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text71__GetValue);
            this.Text71.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text71.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text71.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text71.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text71.Font = new System.Drawing.Font("Arial", 8F);
            this.Text71.Guid = null;
            this.Text71.Indicator = null;
            this.Text71.Interaction = null;
            this.Text71.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text71.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text71.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text73
            // 
            this.Text73 = new Stimulsoft.Report.Components.StiText();
            this.Text73.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 4, 14.2, 0.5);
            this.Text73.Name = "Text73";
            this.Text73.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text73__GetValue);
            this.Text73.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text73.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text73.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text73.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text73.Font = new System.Drawing.Font("Arial", 8F);
            this.Text73.Guid = null;
            this.Text73.Indicator = null;
            this.Text73.Interaction = null;
            this.Text73.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text73.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text73.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text75
            // 
            this.Text75 = new Stimulsoft.Report.Components.StiText();
            this.Text75.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(14.2, 4, 4.8, 0.5);
            this.Text75.Name = "Text75";
            this.Text75.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text75__GetValue);
            this.Text75.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text75.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text75.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text75.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text75.Font = new System.Drawing.Font("Arial", 8F);
            this.Text75.Guid = null;
            this.Text75.Indicator = null;
            this.Text75.Interaction = null;
            this.Text75.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text75.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text75.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text77
            // 
            this.Text77 = new Stimulsoft.Report.Components.StiText();
            this.Text77.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 4.6, 8.8, 0.5);
            this.Text77.Name = "Text77";
            this.Text77.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text77__GetValue);
            this.Text77.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text77.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text77.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text77.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text77.Font = new System.Drawing.Font("Arial", 8F);
            this.Text77.Guid = null;
            this.Text77.Indicator = null;
            this.Text77.Interaction = null;
            this.Text77.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text77.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text77.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text79
            // 
            this.Text79 = new Stimulsoft.Report.Components.StiText();
            this.Text79.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(8.8, 4.6, 5.4, 0.5);
            this.Text79.Name = "Text79";
            this.Text79.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text79__GetValue);
            this.Text79.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text79.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text79.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text79.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text79.Font = new System.Drawing.Font("Arial", 8F);
            this.Text79.Guid = null;
            this.Text79.Indicator = null;
            this.Text79.Interaction = null;
            this.Text79.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text79.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text79.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text81
            // 
            this.Text81 = new Stimulsoft.Report.Components.StiText();
            this.Text81.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(14.2, 4.6, 4.8, 0.5);
            this.Text81.Name = "Text81";
            this.Text81.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text81__GetValue);
            this.Text81.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text81.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text81.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text81.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text81.Font = new System.Drawing.Font("Arial", 8F);
            this.Text81.Guid = null;
            this.Text81.Indicator = null;
            this.Text81.Interaction = null;
            this.Text81.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text81.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text81.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text83
            // 
            this.Text83 = new Stimulsoft.Report.Components.StiText();
            this.Text83.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 5.2, 5, 0.5);
            this.Text83.Name = "Text83";
            this.Text83.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text83__GetValue);
            this.Text83.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text83.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text83.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text83.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text83.Font = new System.Drawing.Font("Arial", 8F);
            this.Text83.Guid = null;
            this.Text83.Indicator = null;
            this.Text83.Interaction = null;
            this.Text83.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text83.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text83.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text85
            // 
            this.Text85 = new Stimulsoft.Report.Components.StiText();
            this.Text85.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(5, 5.2, 2.6, 0.5);
            this.Text85.Name = "Text85";
            this.Text85.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text85__GetValue);
            this.Text85.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text85.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text85.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text85.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text85.Font = new System.Drawing.Font("Arial", 8F);
            this.Text85.Guid = null;
            this.Text85.Indicator = null;
            this.Text85.Interaction = null;
            this.Text85.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text85.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text85.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text87
            // 
            this.Text87 = new Stimulsoft.Report.Components.StiText();
            this.Text87.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(7.6, 5.2, 6.6, 0.5);
            this.Text87.Name = "Text87";
            this.Text87.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text87__GetValue);
            this.Text87.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text87.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text87.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text87.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text87.Font = new System.Drawing.Font("Arial", 8F);
            this.Text87.Guid = null;
            this.Text87.Indicator = null;
            this.Text87.Interaction = null;
            this.Text87.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text87.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text87.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text89
            // 
            this.Text89 = new Stimulsoft.Report.Components.StiText();
            this.Text89.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(14.2, 5.2, 4.8, 0.5);
            this.Text89.Name = "Text89";
            this.Text89.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text89__GetValue);
            this.Text89.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text89.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text89.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text89.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text89.Font = new System.Drawing.Font("Arial", 8F);
            this.Text89.Guid = null;
            this.Text89.Indicator = null;
            this.Text89.Interaction = null;
            this.Text89.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text89.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text89.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text48
            // 
            this.Text48 = new Stimulsoft.Report.Components.StiText();
            this.Text48.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 5.8, 4.4, 0.5);
            this.Text48.Name = "Text48";
            this.Text48.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text48__GetValue);
            this.Text48.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text48.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text48.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text48.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text48.Font = new System.Drawing.Font("Arial", 8F);
            this.Text48.Guid = null;
            this.Text48.Indicator = null;
            this.Text48.Interaction = null;
            this.Text48.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text48.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text48.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text51
            // 
            this.Text51 = new Stimulsoft.Report.Components.StiText();
            this.Text51.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(4.4, 5.8, 4, 0.5);
            this.Text51.Name = "Text51";
            this.Text51.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text51__GetValue);
            this.Text51.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text51.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text51.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text51.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text51.Font = new System.Drawing.Font("Arial", 8F);
            this.Text51.Guid = null;
            this.Text51.Indicator = null;
            this.Text51.Interaction = null;
            this.Text51.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text51.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text51.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text50
            // 
            this.Text50 = new Stimulsoft.Report.Components.StiText();
            this.Text50.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(8.4, 5.8, 3.6, 0.5);
            this.Text50.Name = "Text50";
            this.Text50.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text50__GetValue);
            this.Text50.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text50.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text50.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text50.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text50.Font = new System.Drawing.Font("Arial", 8F);
            this.Text50.Guid = null;
            this.Text50.Indicator = null;
            this.Text50.Interaction = null;
            this.Text50.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text50.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text50.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text55
            // 
            this.Text55 = new Stimulsoft.Report.Components.StiText();
            this.Text55.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(12, 5.8, 3.4, 0.5);
            this.Text55.Name = "Text55";
            this.Text55.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text55__GetValue);
            this.Text55.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text55.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text55.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text55.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text55.Font = new System.Drawing.Font("Arial", 8F);
            this.Text55.Guid = null;
            this.Text55.Indicator = null;
            this.Text55.Interaction = null;
            this.Text55.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text55.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text55.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text59
            // 
            this.Text59 = new Stimulsoft.Report.Components.StiText();
            this.Text59.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(15.4, 5.8, 3.6, 0.5);
            this.Text59.Name = "Text59";
            this.Text59.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text59__GetValue);
            this.Text59.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text59.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text59.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text59.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text59.Font = new System.Drawing.Font("Arial", 8F);
            this.Text59.Guid = null;
            this.Text59.Indicator = null;
            this.Text59.Interaction = null;
            this.Text59.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text59.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text59.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text63
            // 
            this.Text63 = new Stimulsoft.Report.Components.StiText();
            this.Text63.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 6.4, 4, 0.5);
            this.Text63.Name = "Text63";
            this.Text63.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text63__GetValue);
            this.Text63.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text63.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text63.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text63.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text63.Font = new System.Drawing.Font("Arial", 8F);
            this.Text63.Guid = null;
            this.Text63.Indicator = null;
            this.Text63.Interaction = null;
            this.Text63.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text63.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text63.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text68
            // 
            this.Text68 = new Stimulsoft.Report.Components.StiText();
            this.Text68.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(4, 6.4, 3.4, 0.5);
            this.Text68.Name = "Text68";
            this.Text68.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text68__GetValue);
            this.Text68.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text68.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text68.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text68.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text68.Font = new System.Drawing.Font("Arial", 8F);
            this.Text68.Guid = null;
            this.Text68.Indicator = null;
            this.Text68.Interaction = null;
            this.Text68.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text68.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text68.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text72
            // 
            this.Text72 = new Stimulsoft.Report.Components.StiText();
            this.Text72.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(7.4, 6.4, 3.6, 0.5);
            this.Text72.Name = "Text72";
            this.Text72.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text72__GetValue);
            this.Text72.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text72.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text72.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text72.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text72.Font = new System.Drawing.Font("Arial", 8F);
            this.Text72.Guid = null;
            this.Text72.Indicator = null;
            this.Text72.Interaction = null;
            this.Text72.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text72.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text72.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text76
            // 
            this.Text76 = new Stimulsoft.Report.Components.StiText();
            this.Text76.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(11, 6.4, 4, 0.5);
            this.Text76.Name = "Text76";
            this.Text76.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text76__GetValue);
            this.Text76.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text76.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text76.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text76.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text76.Font = new System.Drawing.Font("Arial", 8F);
            this.Text76.Guid = null;
            this.Text76.Indicator = null;
            this.InitializeComponent2();
        }
        
        public void InitializeComponent2()
        {
            this.Text76.Interaction = null;
            this.Text76.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text76.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text76.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text80
            // 
            this.Text80 = new Stimulsoft.Report.Components.StiText();
            this.Text80.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(15, 6.4, 4, 0.5);
            this.Text80.Name = "Text80";
            this.Text80.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text80__GetValue);
            this.Text80.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text80.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text80.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text80.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text80.Font = new System.Drawing.Font("Arial", 8F);
            this.Text80.Guid = null;
            this.Text80.Indicator = null;
            this.Text80.Interaction = null;
            this.Text80.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text80.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text80.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text88
            // 
            this.Text88 = new Stimulsoft.Report.Components.StiText();
            this.Text88.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(11, 7, 8, 0.5);
            this.Text88.Name = "Text88";
            this.Text88.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text88__GetValue);
            this.Text88.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text88.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text88.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text88.Font = new System.Drawing.Font("Arial", 8F);
            this.Text88.Guid = null;
            this.Text88.Indicator = null;
            this.Text88.Interaction = null;
            this.Text88.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text88.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text88.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text91
            // 
            this.Text91 = new Stimulsoft.Report.Components.StiText();
            this.Text91.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 7.6, 11, 0.5);
            this.Text91.Name = "Text91";
            this.Text91.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text91__GetValue);
            this.Text91.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text91.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text91.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text91.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text91.Font = new System.Drawing.Font("Arial", 8F);
            this.Text91.Guid = null;
            this.Text91.Indicator = null;
            this.Text91.Interaction = null;
            this.Text91.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text91.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text91.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text93
            // 
            this.Text93 = new Stimulsoft.Report.Components.StiText();
            this.Text93.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(10.8, 7.6, 8, 0.5);
            this.Text93.Name = "Text93";
            this.Text93.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text93__GetValue);
            this.Text93.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text93.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text93.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text93.Font = new System.Drawing.Font("Arial", 8F);
            this.Text93.Guid = null;
            this.Text93.Indicator = null;
            this.Text93.Interaction = null;
            this.Text93.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text93.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text93.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text84
            // 
            this.Text84 = new Stimulsoft.Report.Components.StiText();
            this.Text84.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 7, 11, 0.5);
            this.Text84.Name = "Text84";
            this.Text84.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text84__GetValue);
            this.Text84.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text84.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text84.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text84.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text84.Font = new System.Drawing.Font("Arial", 8F);
            this.Text84.Guid = null;
            this.Text84.Indicator = null;
            this.Text84.Interaction = null;
            this.Text84.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text84.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text84.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text53
            // 
            this.Text53 = new Stimulsoft.Report.Components.StiText();
            this.Text53.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 8.2, 7.2, 0.5);
            this.Text53.Name = "Text53";
            this.Text53.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text53__GetValue);
            this.Text53.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text53.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text53.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text53.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text53.Font = new System.Drawing.Font("Arial", 8F);
            this.Text53.Guid = null;
            this.Text53.Indicator = null;
            this.Text53.Interaction = null;
            this.Text53.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text53.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text53.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text61
            // 
            this.Text61 = new Stimulsoft.Report.Components.StiText();
            this.Text61.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(7.2, 8.2, 11.8, 0.5);
            this.Text61.Name = "Text61";
            this.Text61.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text61__GetValue);
            this.Text61.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text61.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text61.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text61.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text61.Font = new System.Drawing.Font("Arial", 8F);
            this.Text61.Guid = null;
            this.Text61.Indicator = null;
            this.Text61.Interaction = null;
            this.Text61.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text61.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text61.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text70
            // 
            this.Text70 = new Stimulsoft.Report.Components.StiText();
            this.Text70.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(7, 8.8, 7, 0.5);
            this.Text70.Name = "Text70";
            this.Text70.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text70__GetValue);
            this.Text70.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text70.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text70.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text70.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text70.Font = new System.Drawing.Font("Arial", 8F);
            this.Text70.Guid = null;
            this.Text70.Indicator = null;
            this.Text70.Interaction = null;
            this.Text70.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text70.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text70.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text78
            // 
            this.Text78 = new Stimulsoft.Report.Components.StiText();
            this.Text78.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 8.8, 7, 0.5);
            this.Text78.Name = "Text78";
            this.Text78.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text78__GetValue);
            this.Text78.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text78.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text78.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text78.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text78.Font = new System.Drawing.Font("Arial", 8F);
            this.Text78.Guid = null;
            this.Text78.Indicator = null;
            this.Text78.Interaction = null;
            this.Text78.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text78.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text78.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text86
            // 
            this.Text86 = new Stimulsoft.Report.Components.StiText();
            this.Text86.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(14, 8.8, 5, 0.5);
            this.Text86.Name = "Text86";
            this.Text86.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text86__GetValue);
            this.Text86.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text86.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text86.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text86.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text86.Font = new System.Drawing.Font("Arial", 8F);
            this.Text86.Guid = null;
            this.Text86.Indicator = null;
            this.Text86.Interaction = null;
            this.Text86.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text86.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text86.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text57
            // 
            this.Text57 = new Stimulsoft.Report.Components.StiText();
            this.Text57.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 9.4, 7, 0.5);
            this.Text57.Name = "Text57";
            this.Text57.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text57__GetValue);
            this.Text57.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text57.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text57.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text57.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text57.Font = new System.Drawing.Font("Arial", 8F);
            this.Text57.Guid = null;
            this.Text57.Indicator = null;
            this.Text57.Interaction = null;
            this.Text57.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text57.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text57.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text74
            // 
            this.Text74 = new Stimulsoft.Report.Components.StiText();
            this.Text74.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(7, 9.4, 7, 0.5);
            this.Text74.Name = "Text74";
            this.Text74.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text74__GetValue);
            this.Text74.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text74.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text74.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text74.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text74.Font = new System.Drawing.Font("Arial", 8F);
            this.Text74.Guid = null;
            this.Text74.Indicator = null;
            this.Text74.Interaction = null;
            this.Text74.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text74.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text74.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text90
            // 
            this.Text90 = new Stimulsoft.Report.Components.StiText();
            this.Text90.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(14, 9.4, 5, 0.5);
            this.Text90.Name = "Text90";
            this.Text90.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text90__GetValue);
            this.Text90.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text90.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text90.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text90.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text90.Font = new System.Drawing.Font("Arial", 8F);
            this.Text90.Guid = null;
            this.Text90.Indicator = null;
            this.Text90.Interaction = null;
            this.Text90.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text90.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text90.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text94
            // 
            this.Text94 = new Stimulsoft.Report.Components.StiText();
            this.Text94.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 10, 7.2, 0.5);
            this.Text94.Name = "Text94";
            this.Text94.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text94__GetValue);
            this.Text94.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text94.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text94.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text94.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text94.Font = new System.Drawing.Font("Arial", 8F);
            this.Text94.Guid = null;
            this.Text94.Indicator = null;
            this.Text94.Interaction = null;
            this.Text94.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text94.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text94.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text96
            // 
            this.Text96 = new Stimulsoft.Report.Components.StiText();
            this.Text96.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(7.2, 10, 6.8, 0.5);
            this.Text96.Name = "Text96";
            this.Text96.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text96__GetValue);
            this.Text96.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text96.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text96.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text96.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text96.Font = new System.Drawing.Font("Arial", 8F);
            this.Text96.Guid = null;
            this.Text96.Indicator = null;
            this.Text96.Interaction = null;
            this.Text96.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text96.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text96.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text98
            // 
            this.Text98 = new Stimulsoft.Report.Components.StiText();
            this.Text98.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(14, 10, 5, 0.5);
            this.Text98.Name = "Text98";
            this.Text98.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text98__GetValue);
            this.Text98.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text98.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text98.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text98.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text98.Font = new System.Drawing.Font("Arial", 8F);
            this.Text98.Guid = null;
            this.Text98.Indicator = null;
            this.Text98.Interaction = null;
            this.Text98.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text98.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text98.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text100
            // 
            this.Text100 = new Stimulsoft.Report.Components.StiText();
            this.Text100.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 10.6, 6, 0.5);
            this.Text100.Name = "Text100";
            this.Text100.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text100__GetValue);
            this.Text100.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text100.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text100.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text100.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text100.Font = new System.Drawing.Font("Arial", 8F);
            this.Text100.Guid = null;
            this.Text100.Indicator = null;
            this.Text100.Interaction = null;
            this.Text100.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text100.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text100.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text102
            // 
            this.Text102 = new Stimulsoft.Report.Components.StiText();
            this.Text102.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(6, 10.6, 6, 0.5);
            this.Text102.Name = "Text102";
            this.Text102.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text102__GetValue);
            this.Text102.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text102.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text102.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text102.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text102.Font = new System.Drawing.Font("Arial", 8F);
            this.Text102.Guid = null;
            this.Text102.Indicator = null;
            this.Text102.Interaction = null;
            this.Text102.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text102.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text102.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text104
            // 
            this.Text104 = new Stimulsoft.Report.Components.StiText();
            this.Text104.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(12, 10.6, 3, 0.5);
            this.Text104.Name = "Text104";
            this.Text104.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text104__GetValue);
            this.Text104.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text104.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text104.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text104.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text104.Font = new System.Drawing.Font("Arial", 8F);
            this.Text104.Guid = null;
            this.Text104.Indicator = null;
            this.Text104.Interaction = null;
            this.Text104.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text104.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text104.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text106
            // 
            this.Text106 = new Stimulsoft.Report.Components.StiText();
            this.Text106.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(15, 10.6, 4, 0.5);
            this.Text106.Name = "Text106";
            this.Text106.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text106__GetValue);
            this.Text106.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text106.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text106.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text106.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text106.Font = new System.Drawing.Font("Arial", 8F);
            this.Text106.Guid = null;
            this.Text106.Indicator = null;
            this.Text106.Interaction = null;
            this.Text106.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text106.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text106.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text108
            // 
            this.Text108 = new Stimulsoft.Report.Components.StiText();
            this.Text108.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 11.2, 4.2, 0.5);
            this.Text108.Name = "Text108";
            this.Text108.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text108__GetValue);
            this.Text108.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text108.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text108.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text108.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text108.Font = new System.Drawing.Font("Arial", 8F);
            this.Text108.Guid = null;
            this.Text108.Indicator = null;
            this.Text108.Interaction = null;
            this.Text108.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text108.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text108.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text110
            // 
            this.Text110 = new Stimulsoft.Report.Components.StiText();
            this.Text110.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(4.2, 11.2, 5.4, 0.5);
            this.Text110.Name = "Text110";
            this.Text110.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text110__GetValue);
            this.Text110.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text110.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text110.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text110.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text110.Font = new System.Drawing.Font("Arial", 8F);
            this.Text110.Guid = null;
            this.Text110.Indicator = null;
            this.Text110.Interaction = null;
            this.Text110.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text110.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text110.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text112
            // 
            this.Text112 = new Stimulsoft.Report.Components.StiText();
            this.Text112.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(9.6, 11.2, 4.2, 0.5);
            this.Text112.Name = "Text112";
            this.Text112.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text112__GetValue);
            this.Text112.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text112.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text112.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text112.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text112.Font = new System.Drawing.Font("Arial", 8F);
            this.Text112.Guid = null;
            this.Text112.Indicator = null;
            this.Text112.Interaction = null;
            this.Text112.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text112.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text112.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text114
            // 
            this.Text114 = new Stimulsoft.Report.Components.StiText();
            this.Text114.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(13.8, 11.2, 5.2, 0.5);
            this.Text114.Name = "Text114";
            this.Text114.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text114__GetValue);
            this.Text114.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text114.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text114.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text114.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text114.Font = new System.Drawing.Font("Arial", 8F);
            this.Text114.Guid = null;
            this.Text114.Indicator = null;
            this.Text114.Interaction = null;
            this.Text114.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text114.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text114.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text116
            // 
            this.Text116 = new Stimulsoft.Report.Components.StiText();
            this.Text116.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 11.8, 4.2, 0.5);
            this.Text116.Name = "Text116";
            this.Text116.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text116__GetValue);
            this.Text116.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text116.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text116.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text116.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text116.Font = new System.Drawing.Font("Arial", 8F);
            this.Text116.Guid = null;
            this.Text116.Indicator = null;
            this.Text116.Interaction = null;
            this.Text116.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text116.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text116.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text118
            // 
            this.Text118 = new Stimulsoft.Report.Components.StiText();
            this.Text118.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(4.2, 11.8, 5.4, 0.5);
            this.Text118.Name = "Text118";
            this.Text118.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text118__GetValue);
            this.Text118.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text118.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text118.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text118.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text118.Font = new System.Drawing.Font("Arial", 8F);
            this.Text118.Guid = null;
            this.Text118.Indicator = null;
            this.Text118.Interaction = null;
            this.Text118.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text118.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text118.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text120
            // 
            this.Text120 = new Stimulsoft.Report.Components.StiText();
            this.Text120.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(9.6, 11.8, 4.2, 0.5);
            this.Text120.Name = "Text120";
            this.Text120.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text120__GetValue);
            this.Text120.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text120.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text120.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text120.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text120.Font = new System.Drawing.Font("Arial", 8F);
            this.Text120.Guid = null;
            this.Text120.Indicator = null;
            this.Text120.Interaction = null;
            this.Text120.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text120.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text120.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text122
            // 
            this.Text122 = new Stimulsoft.Report.Components.StiText();
            this.Text122.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(13.8, 11.8, 5.2, 0.5);
            this.Text122.Name = "Text122";
            this.Text122.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text122__GetValue);
            this.Text122.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text122.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text122.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text122.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text122.Font = new System.Drawing.Font("Arial", 8F);
            this.Text122.Guid = null;
            this.Text122.Indicator = null;
            this.Text122.Interaction = null;
            this.Text122.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text122.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text122.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text126
            // 
            this.Text126 = new Stimulsoft.Report.Components.StiText();
            this.Text126.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(4.2, 12.4, 5.4, 0.5);
            this.Text126.Name = "Text126";
            this.Text126.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text126__GetValue);
            this.Text126.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text126.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text126.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text126.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text126.Font = new System.Drawing.Font("Arial", 8F);
            this.Text126.Guid = null;
            this.Text126.Indicator = null;
            this.Text126.Interaction = null;
            this.Text126.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text126.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text126.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text128
            // 
            this.Text128 = new Stimulsoft.Report.Components.StiText();
            this.Text128.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(9.6, 12.4, 4.2, 0.5);
            this.Text128.Name = "Text128";
            this.Text128.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text128__GetValue);
            this.Text128.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text128.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text128.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text128.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text128.Font = new System.Drawing.Font("Arial", 8F);
            this.Text128.Guid = null;
            this.Text128.Indicator = null;
            this.Text128.Interaction = null;
            this.Text128.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text128.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text128.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text130
            // 
            this.Text130 = new Stimulsoft.Report.Components.StiText();
            this.Text130.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(13.8, 12.4, 5.2, 0.5);
            this.Text130.Name = "Text130";
            this.Text130.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text130__GetValue);
            this.Text130.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text130.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text130.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text130.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text130.Font = new System.Drawing.Font("Arial", 8F);
            this.Text130.Guid = null;
            this.Text130.Indicator = null;
            this.Text130.Interaction = null;
            this.Text130.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text130.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text130.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text124
            // 
            this.Text124 = new Stimulsoft.Report.Components.StiText();
            this.Text124.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 12.4, 4.2, 0.5);
            this.Text124.Name = "Text124";
            this.Text124.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text124__GetValue);
            this.Text124.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text124.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text124.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text124.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text124.Font = new System.Drawing.Font("Arial", 8F);
            this.Text124.Guid = null;
            this.Text124.Indicator = null;
            this.Text124.Interaction = null;
            this.Text124.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text124.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text124.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            this.ReportTitleBand1.Guid = null;
            this.ReportTitleBand1.Interaction = null;
            // 
            // HeaderBand1
            // 
            this.HeaderBand1 = new Stimulsoft.Report.Components.StiHeaderBand();
            this.HeaderBand1.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 19.4, 19, 0.5);
            this.HeaderBand1.Name = "HeaderBand1";
            this.HeaderBand1.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.HeaderBand1.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            // 
            // Text7
            // 
            this.Text7 = new Stimulsoft.Report.Components.StiText();
            this.Text7.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 0, 3.2, 0.5);
            this.Text7.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text7.Name = "Text7";
            this.Text7.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text7__GetValue);
            this.Text7.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text7.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.All, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text7.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text7.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 134);
            this.Text7.Guid = null;
            this.Text7.Indicator = null;
            this.Text7.Interaction = null;
            this.Text7.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text7.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text7.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text9
            // 
            this.Text9 = new Stimulsoft.Report.Components.StiText();
            this.Text9.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(3.2, 0, 2.2, 0.5);
            this.Text9.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text9.Name = "Text9";
            this.Text9.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text9__GetValue);
            this.Text9.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text9.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.All, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text9.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text9.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 134);
            this.Text9.Guid = null;
            this.Text9.Indicator = null;
            this.Text9.Interaction = null;
            this.Text9.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text9.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text9.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text11
            // 
            this.Text11 = new Stimulsoft.Report.Components.StiText();
            this.Text11.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(5.4, 0, 2.1, 0.5);
            this.Text11.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text11.Name = "Text11";
            this.Text11.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text11__GetValue);
            this.Text11.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text11.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.All, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text11.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text11.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 134);
            this.Text11.Guid = null;
            this.Text11.Indicator = null;
            this.Text11.Interaction = null;
            this.Text11.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text11.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text11.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text13
            // 
            this.Text13 = new Stimulsoft.Report.Components.StiText();
            this.Text13.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(7.5, 0, 0.9, 0.5);
            this.Text13.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text13.Name = "Text13";
            this.Text13.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text13__GetValue);
            this.Text13.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text13.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text13.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.All, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text13.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text13.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 134);
            this.Text13.Guid = null;
            this.Text13.Indicator = null;
            this.Text13.Interaction = null;
            this.Text13.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text13.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text13.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text15
            // 
            this.Text15 = new Stimulsoft.Report.Components.StiText();
            this.Text15.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(8.4, 0, 2.2, 0.5);
            this.Text15.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text15.Name = "Text15";
            this.Text15.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text15__GetValue);
            this.Text15.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text15.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.All, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text15.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text15.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 134);
            this.Text15.Guid = null;
            this.Text15.Indicator = null;
            this.Text15.Interaction = null;
            this.Text15.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text15.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text15.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text17
            // 
            this.Text17 = new Stimulsoft.Report.Components.StiText();
            this.Text17.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(10.6, 0, 1.6, 0.5);
            this.Text17.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text17.Name = "Text17";
            this.Text17.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text17__GetValue);
            this.Text17.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text17.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.All, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text17.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text17.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 134);
            this.Text17.Guid = null;
            this.Text17.Indicator = null;
            this.Text17.Interaction = null;
            this.Text17.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text17.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text17.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text19
            // 
            this.Text19 = new Stimulsoft.Report.Components.StiText();
            this.Text19.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(12.2, 0, 1.7, 0.5);
            this.Text19.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text19.Name = "Text19";
            this.Text19.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text19__GetValue);
            this.Text19.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text19.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.All, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text19.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text19.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 134);
            this.Text19.Guid = null;
            this.Text19.Indicator = null;
            this.Text19.Interaction = null;
            this.Text19.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text19.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text19.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text21
            // 
            this.Text21 = new Stimulsoft.Report.Components.StiText();
            this.Text21.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(13.9, 0, 1.7, 0.5);
            this.Text21.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text21.Name = "Text21";
            this.Text21.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text21__GetValue);
            this.Text21.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text21.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.All, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text21.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text21.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 134);
            this.Text21.Guid = null;
            this.Text21.Indicator = null;
            this.Text21.Interaction = null;
            this.Text21.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text21.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text21.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text23
            // 
            this.Text23 = new Stimulsoft.Report.Components.StiText();
            this.Text23.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(15.6, 0, 1.7, 0.5);
            this.Text23.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text23.Name = "Text23";
            this.Text23.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text23__GetValue);
            this.Text23.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text23.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.All, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text23.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text23.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 134);
            this.Text23.Guid = null;
            this.Text23.Indicator = null;
            this.Text23.Interaction = null;
            this.Text23.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text23.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text23.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text25
            // 
            this.Text25 = new Stimulsoft.Report.Components.StiText();
            this.Text25.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(17.3, 0, 1.7, 0.5);
            this.Text25.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text25.Name = "Text25";
            this.Text25.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text25__GetValue);
            this.Text25.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text25.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.All, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text25.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text25.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 134);
            this.Text25.Guid = null;
            this.Text25.Indicator = null;
            this.Text25.Interaction = null;
            this.Text25.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text25.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text25.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            this.HeaderBand1.Guid = null;
            this.HeaderBand1.Interaction = null;
            // 
            // GroupHeaderBand1
            // 
            this.GroupHeaderBand1 = new Stimulsoft.Report.Components.StiGroupHeaderBand();
            this.GroupHeaderBand1.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 20.7, 19, 0.5);
            this.GroupHeaderBand1.GetValue += new Stimulsoft.Report.Events.StiValueEventHandler(this.GroupHeaderBand1__GetValue);
            this.GroupHeaderBand1.Name = "GroupHeaderBand1";
            this.GroupHeaderBand1.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.GroupHeaderBand1.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            // 
            // Text26
            // 
            this.Text26 = new Stimulsoft.Report.Components.StiText();
            this.Text26.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 0, 19, 0.5);
            this.Text26.Name = "Text26";
            this.Text26.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text26__GetValue);
            this.Text26.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text26.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text26.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.All, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text26.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text26.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 134);
            this.Text26.Guid = null;
            this.Text26.Indicator = null;
            this.Text26.Interaction = null;
            this.Text26.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text26.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text26.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            this.GroupHeaderBand1.Guid = null;
            this.GroupHeaderBand1.Interaction = null;
            // 
            // GroupHeaderBand2
            // 
            this.GroupHeaderBand2 = new Stimulsoft.Report.Components.StiGroupHeaderBand();
            this.GroupHeaderBand2.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 22, 19, 0.5);
            this.GroupHeaderBand2.GetValue += new Stimulsoft.Report.Events.StiValueEventHandler(this.GroupHeaderBand2__GetValue);
            this.GroupHeaderBand2.Name = "GroupHeaderBand2";
            this.GroupHeaderBand2.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.GroupHeaderBand2.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            // 
            // Text27
            // 
            this.Text27 = new Stimulsoft.Report.Components.StiText();
            this.Text27.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 0, 19, 0.5);
            this.Text27.Name = "Text27";
            this.Text27.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text27__GetValue);
            this.Text27.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text27.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text27.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.All, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text27.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text27.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 134);
            this.Text27.Guid = null;
            this.Text27.Indicator = null;
            this.Text27.Interaction = null;
            this.Text27.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text27.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text27.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            this.GroupHeaderBand2.Guid = null;
            this.GroupHeaderBand2.Interaction = null;
            // 
            // DataBand1
            // 
            this.DataBand1 = new Stimulsoft.Report.Components.StiDataBand();
            this.DataBand1.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 23.3, 19, 0.5);
            this.DataBand1.DataSourceName = "view明细";
            this.DataBand1.Name = "DataBand1";
            this.DataBand1.Sort = new System.String[] {
                    "ASC",
                    "Yp_Code"};
            this.DataBand1.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.DataBand1.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.DataBand1.BusinessObjectGuid = null;
            // 
            // Text6
            // 
            this.Text6 = new Stimulsoft.Report.Components.StiText();
            this.Text6.CanGrow = true;
            this.Text6.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 0, 3.2, 0.5);
            this.Text6.GrowToHeight = true;
            this.Text6.Name = "Text6";
            this.Text6.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text6__GetValue);
            this.Text6.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text6.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text6.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.All, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text6.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text6.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 134);
            this.Text6.Guid = null;
            this.Text6.Indicator = null;
            this.Text6.Interaction = null;
            this.Text6.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text6.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text6.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, true, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text8
            // 
            this.Text8 = new Stimulsoft.Report.Components.StiText();
            this.Text8.CanGrow = true;
            this.Text8.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(3.2, 0, 2.2, 0.5);
            this.Text8.GrowToHeight = true;
            this.Text8.Name = "Text8";
            this.Text8.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text8__GetValue);
            this.Text8.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text8.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text8.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.All, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text8.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text8.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 134);
            this.Text8.Guid = null;
            this.Text8.Indicator = null;
            this.Text8.Interaction = null;
            this.Text8.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text8.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text8.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, true, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text10
            // 
            this.Text10 = new Stimulsoft.Report.Components.StiText();
            this.Text10.CanGrow = true;
            this.Text10.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(5.4, 0, 2.1, 0.5);
            this.Text10.GrowToHeight = true;
            this.Text10.Name = "Text10";
            this.Text10.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text10__GetValue);
            this.Text10.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text10.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text10.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.All, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text10.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text10.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 134);
            this.Text10.Guid = null;
            this.Text10.Indicator = null;
            this.Text10.Interaction = null;
            this.Text10.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text10.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text10.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, true, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text12
            // 
            this.Text12 = new Stimulsoft.Report.Components.StiText();
            this.Text12.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(7.5, 0, 0.9, 0.5);
            this.Text12.GrowToHeight = true;
            this.Text12.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text12.Name = "Text12";
            this.Text12.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text12__GetValue);
            this.Text12.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text12.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text12.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.All, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text12.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text12.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 134);
            this.Text12.Guid = null;
            this.Text12.Indicator = null;
            this.Text12.Interaction = null;
            this.Text12.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text12.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text12.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text14
            // 
            this.Text14 = new Stimulsoft.Report.Components.StiText();
            this.Text14.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(8.4, 0, 2.2, 0.5);
            this.Text14.GrowToHeight = true;
            this.Text14.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text14.Name = "Text14";
            this.Text14.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text14__GetValue);
            this.Text14.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text14.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text14.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.All, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text14.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text14.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 134);
            this.Text14.Guid = null;
            this.Text14.Indicator = null;
            this.Text14.Interaction = null;
            this.Text14.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text14.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text14.TextFormat = new Stimulsoft.Report.Components.TextFormats.StiCustomFormatService("yyyy-MM-dd");
            this.Text14.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text16
            // 
            this.Text16 = new Stimulsoft.Report.Components.StiText();
            this.Text16.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(10.6, 0, 1.6, 0.5);
            this.Text16.GrowToHeight = true;
            this.Text16.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text16.Name = "Text16";
            this.Text16.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text16__GetValue);
            this.Text16.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text16.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text16.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.All, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text16.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text16.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 134);
            this.Text16.Guid = null;
            this.Text16.Indicator = null;
            this.Text16.Interaction = null;
            this.Text16.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text16.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text16.TextFormat = new Stimulsoft.Report.Components.TextFormats.StiCustomFormatService("0.####");
            this.Text16.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text18
            // 
            this.Text18 = new Stimulsoft.Report.Components.StiText();
            this.Text18.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(12.2, 0, 1.7, 0.5);
            this.Text18.GrowToHeight = true;
            this.Text18.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text18.Name = "Text18";
            this.Text18.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text18__GetValue);
            this.Text18.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text18.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text18.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.All, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text18.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text18.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 134);
            this.Text18.Guid = null;
            this.Text18.Indicator = null;
            this.Text18.Interaction = null;
            this.Text18.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text18.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text18.TextFormat = new Stimulsoft.Report.Components.TextFormats.StiCustomFormatService("0.00##");
            this.Text18.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text20
            // 
            this.Text20 = new Stimulsoft.Report.Components.StiText();
            this.Text20.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(13.9, 0, 1.7, 0.5);
            this.Text20.GrowToHeight = true;
            this.Text20.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text20.Name = "Text20";
            this.Text20.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text20__GetValue);
            this.Text20.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text20.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text20.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.All, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text20.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text20.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 134);
            this.Text20.Guid = null;
            this.Text20.Indicator = null;
            this.Text20.Interaction = null;
            this.InitializeComponent3();
        }
        
        public void InitializeComponent3()
        {
            this.Text20.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text20.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text20.TextFormat = new Stimulsoft.Report.Components.TextFormats.StiCustomFormatService("0.00##");
            this.Text20.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text22
            // 
            this.Text22 = new Stimulsoft.Report.Components.StiText();
            this.Text22.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(15.6, 0, 1.7, 0.5);
            this.Text22.GrowToHeight = true;
            this.Text22.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text22.Name = "Text22";
            this.Text22.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text22__GetValue);
            this.Text22.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text22.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text22.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.All, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text22.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text22.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 134);
            this.Text22.Guid = null;
            this.Text22.Indicator = null;
            this.Text22.Interaction = null;
            this.Text22.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text22.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text22.TextFormat = new Stimulsoft.Report.Components.TextFormats.StiCustomFormatService("0.00##");
            this.Text22.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text24
            // 
            this.Text24 = new Stimulsoft.Report.Components.StiText();
            this.Text24.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(17.3, 0, 1.7, 0.5);
            this.Text24.GrowToHeight = true;
            this.Text24.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text24.Name = "Text24";
            this.Text24.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text24__GetValue);
            this.Text24.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text24.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text24.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.All, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text24.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text24.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 134);
            this.Text24.Guid = null;
            this.Text24.Indicator = null;
            this.Text24.Interaction = null;
            this.Text24.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text24.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text24.TextFormat = new Stimulsoft.Report.Components.TextFormats.StiCustomFormatService("0.00##");
            this.Text24.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            this.DataBand1.DataRelationName = null;
            this.DataBand1.Guid = null;
            this.DataBand1.Interaction = null;
            this.DataBand1.MasterComponent = null;
            // 
            // GroupFooterBand2
            // 
            this.GroupFooterBand2 = new Stimulsoft.Report.Components.StiGroupFooterBand();
            this.GroupFooterBand2.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 24.6, 19, 0.5);
            this.GroupFooterBand2.Name = "GroupFooterBand2";
            this.GroupFooterBand2.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.GroupFooterBand2.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            // 
            // Text28
            // 
            this.Text28 = new Stimulsoft.Report.Components.StiText();
            this.Text28.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 0, 2.2, 0.5);
            this.Text28.Name = "Text28";
            this.Text28.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text28__GetValue);
            this.Text28.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text28.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text28.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.All, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text28.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text28.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 134);
            this.Text28.Guid = null;
            this.Text28.Indicator = null;
            this.Text28.Interaction = null;
            this.Text28.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text28.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text28.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text29
            // 
            this.Text29 = new Stimulsoft.Report.Components.StiText();
            this.Text29.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(2.2, 0, 1, 0.5);
            this.Text29.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text29.Name = "Text29";
            this.Text29.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text29__GetValue);
            this.Text29.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text29.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text29.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.All, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text29.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text29.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 134);
            this.Text29.Guid = null;
            this.Text29.Indicator = null;
            this.Text29.Interaction = null;
            this.Text29.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text29.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text29.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text30
            // 
            this.Text30 = new Stimulsoft.Report.Components.StiText();
            this.Text30.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(3.2, 0, 9, 0.5);
            this.Text30.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text30.Name = "Text30";
            this.Text30.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text30.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text30.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.All, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text30.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text30.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 134);
            this.Text30.Guid = null;
            this.Text30.Indicator = null;
            this.Text30.Interaction = null;
            this.Text30.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text30.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text30.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text31
            // 
            this.Text31 = new Stimulsoft.Report.Components.StiText();
            this.Text31.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(12.2, 0, 3.4, 0.5);
            this.Text31.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text31.Name = "Text31";
            // 
            // Text31_Sum
            // 
            this.Text31.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text31__GetValue);
            this.Text31.Type = Stimulsoft.Report.Components.StiSystemTextType.Totals;
            this.Text31.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text31.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.All, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text31.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text31.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 134);
            this.Text31.Guid = null;
            this.Text31.Indicator = null;
            this.Text31.Interaction = null;
            this.Text31.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text31.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text31.TextFormat = new Stimulsoft.Report.Components.TextFormats.StiNumberFormatService(1, ".", 2, ",", 3, false, true, " ");
            this.Text31.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text32
            // 
            this.Text32 = new Stimulsoft.Report.Components.StiText();
            this.Text32.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(15.6, 0, 3.4, 0.5);
            this.Text32.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text32.Name = "Text32";
            // 
            // Text32_Sum
            // 
            this.Text32.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text32__GetValue);
            this.Text32.Type = Stimulsoft.Report.Components.StiSystemTextType.Totals;
            this.Text32.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text32.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.All, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text32.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text32.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 134);
            this.Text32.Guid = null;
            this.Text32.Indicator = null;
            this.Text32.Interaction = null;
            this.Text32.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text32.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text32.TextFormat = new Stimulsoft.Report.Components.TextFormats.StiNumberFormatService(1, ".", 2, ",", 3, false, true, " ");
            this.Text32.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            this.GroupFooterBand2.Guid = null;
            this.GroupFooterBand2.Interaction = null;
            // 
            // GroupFooterBand1
            // 
            this.GroupFooterBand1 = new Stimulsoft.Report.Components.StiGroupFooterBand();
            this.GroupFooterBand1.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 25.9, 19, 0.5);
            this.GroupFooterBand1.Name = "GroupFooterBand1";
            this.GroupFooterBand1.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.GroupFooterBand1.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            // 
            // Text33
            // 
            this.Text33 = new Stimulsoft.Report.Components.StiText();
            this.Text33.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 0, 2.2, 0.5);
            this.Text33.Name = "Text33";
            this.Text33.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text33__GetValue);
            this.Text33.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text33.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text33.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.All, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text33.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text33.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 134);
            this.Text33.Guid = null;
            this.Text33.Indicator = null;
            this.Text33.Interaction = null;
            this.Text33.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text33.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text33.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text34
            // 
            this.Text34 = new Stimulsoft.Report.Components.StiText();
            this.Text34.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(2.2, 0, 1, 0.5);
            this.Text34.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text34.Name = "Text34";
            this.Text34.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text34__GetValue);
            this.Text34.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text34.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text34.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.All, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text34.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text34.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 134);
            this.Text34.Guid = null;
            this.Text34.Indicator = null;
            this.Text34.Interaction = null;
            this.Text34.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text34.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text34.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text35
            // 
            this.Text35 = new Stimulsoft.Report.Components.StiText();
            this.Text35.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(3.2, 0, 9, 0.5);
            this.Text35.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text35.Name = "Text35";
            this.Text35.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text35.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text35.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.All, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text35.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text35.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 134);
            this.Text35.Guid = null;
            this.Text35.Indicator = null;
            this.Text35.Interaction = null;
            this.Text35.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text35.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text35.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text36
            // 
            this.Text36 = new Stimulsoft.Report.Components.StiText();
            this.Text36.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(12.2, 0, 3.4, 0.5);
            this.Text36.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text36.Name = "Text36";
            // 
            // Text36_Sum
            // 
            this.Text36.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text36__GetValue);
            this.Text36.Type = Stimulsoft.Report.Components.StiSystemTextType.Totals;
            this.Text36.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text36.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.All, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text36.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text36.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 134);
            this.Text36.Guid = null;
            this.Text36.Indicator = null;
            this.Text36.Interaction = null;
            this.Text36.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text36.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text36.TextFormat = new Stimulsoft.Report.Components.TextFormats.StiNumberFormatService(1, ".", 2, ",", 3, false, true, " ");
            this.Text36.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text37
            // 
            this.Text37 = new Stimulsoft.Report.Components.StiText();
            this.Text37.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(15.6, 0, 3.4, 0.5);
            this.Text37.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
            this.Text37.Name = "Text37";
            // 
            // Text37_Sum
            // 
            this.Text37.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text37__GetValue);
            this.Text37.Type = Stimulsoft.Report.Components.StiSystemTextType.Totals;
            this.Text37.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text37.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.All, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text37.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text37.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 134);
            this.Text37.Guid = null;
            this.Text37.Indicator = null;
            this.Text37.Interaction = null;
            this.Text37.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text37.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text37.TextFormat = new Stimulsoft.Report.Components.TextFormats.StiNumberFormatService(1, ".", 2, ",", 3, false, true, " ");
            this.Text37.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            this.GroupFooterBand1.Guid = null;
            this.GroupFooterBand1.Interaction = null;
            // 
            // ReportSummaryBand1
            // 
            this.ReportSummaryBand1 = new Stimulsoft.Report.Components.StiReportSummaryBand();
            this.ReportSummaryBand1.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 27.2, 19, 0.5);
            this.ReportSummaryBand1.Name = "ReportSummaryBand1";
            this.ReportSummaryBand1.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.ReportSummaryBand1.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.ReportSummaryBand1.Guid = null;
            this.ReportSummaryBand1.Interaction = null;
            this.Page1.ExcelSheetValue = null;
            this.Page1.Interaction = null;
            this.Page1.Margins = new Stimulsoft.Report.Components.StiMargins(1, 1, 1, 1);
            this.Page1_Watermark = new Stimulsoft.Report.Components.StiWatermark();
            this.Page1_Watermark.Font = new System.Drawing.Font("Arial", 100F);
            this.Page1_Watermark.Image = null;
            this.Page1_Watermark.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.FromArgb(50, 0, 0, 0));
            this.药库盘点表_PrinterSettings = new Stimulsoft.Report.Print.StiPrinterSettings();
            this.PrinterSettings = this.药库盘点表_PrinterSettings;
            this.Page1.Report = this;
            this.Page1.Watermark = this.Page1_Watermark;
            this.PageFooterBand1.Page = this.Page1;
            this.PageFooterBand1.Parent = this.Page1;
            this.Text38.Page = this.Page1;
            this.Text38.Parent = this.PageFooterBand1;
            this.Text39.Page = this.Page1;
            this.Text39.Parent = this.PageFooterBand1;
            this.Text40.Page = this.Page1;
            this.Text40.Parent = this.PageFooterBand1;
            this.Text41.Page = this.Page1;
            this.Text41.Parent = this.PageFooterBand1;
            this.ReportTitleBand1.Page = this.Page1;
            this.ReportTitleBand1.Parent = this.Page1;
            this.Text3.Page = this.Page1;
            this.Text3.Parent = this.ReportTitleBand1;
            this.Text1.Page = this.Page1;
            this.Text1.Parent = this.ReportTitleBand1;
            this.Text2.Page = this.Page1;
            this.Text2.Parent = this.ReportTitleBand1;
            this.Text42.Page = this.Page1;
            this.Text42.Parent = this.ReportTitleBand1;
            this.Text43.Page = this.Page1;
            this.Text43.Parent = this.ReportTitleBand1;
            this.Text44.Page = this.Page1;
            this.Text44.Parent = this.ReportTitleBand1;
            this.Text46.Page = this.Page1;
            this.Text46.Parent = this.ReportTitleBand1;
            this.Text45.Page = this.Page1;
            this.Text45.Parent = this.ReportTitleBand1;
            this.Text47.Page = this.Page1;
            this.Text47.Parent = this.ReportTitleBand1;
            this.Text49.Page = this.Page1;
            this.Text49.Parent = this.ReportTitleBand1;
            this.Text52.Page = this.Page1;
            this.Text52.Parent = this.ReportTitleBand1;
            this.Text54.Page = this.Page1;
            this.Text54.Parent = this.ReportTitleBand1;
            this.Text56.Page = this.Page1;
            this.Text56.Parent = this.ReportTitleBand1;
            this.Text58.Page = this.Page1;
            this.Text58.Parent = this.ReportTitleBand1;
            this.Text60.Page = this.Page1;
            this.Text60.Parent = this.ReportTitleBand1;
            this.Text62.Page = this.Page1;
            this.Text62.Parent = this.ReportTitleBand1;
            this.Text64.Page = this.Page1;
            this.Text64.Parent = this.ReportTitleBand1;
            this.Text66.Page = this.Page1;
            this.Text66.Parent = this.ReportTitleBand1;
            this.Text67.Page = this.Page1;
            this.Text67.Parent = this.ReportTitleBand1;
            this.Text69.Page = this.Page1;
            this.Text69.Parent = this.ReportTitleBand1;
            this.Text5.Page = this.Page1;
            this.Text5.Parent = this.ReportTitleBand1;
            this.Text71.Page = this.Page1;
            this.Text71.Parent = this.ReportTitleBand1;
            this.Text73.Page = this.Page1;
            this.Text73.Parent = this.ReportTitleBand1;
            this.Text75.Page = this.Page1;
            this.Text75.Parent = this.ReportTitleBand1;
            this.Text77.Page = this.Page1;
            this.Text77.Parent = this.ReportTitleBand1;
            this.Text79.Page = this.Page1;
            this.Text79.Parent = this.ReportTitleBand1;
            this.Text81.Page = this.Page1;
            this.Text81.Parent = this.ReportTitleBand1;
            this.Text83.Page = this.Page1;
            this.Text83.Parent = this.ReportTitleBand1;
            this.Text85.Page = this.Page1;
            this.Text85.Parent = this.ReportTitleBand1;
            this.Text87.Page = this.Page1;
            this.Text87.Parent = this.ReportTitleBand1;
            this.Text89.Page = this.Page1;
            this.Text89.Parent = this.ReportTitleBand1;
            this.Text48.Page = this.Page1;
            this.Text48.Parent = this.ReportTitleBand1;
            this.Text51.Page = this.Page1;
            this.Text51.Parent = this.ReportTitleBand1;
            this.Text50.Page = this.Page1;
            this.Text50.Parent = this.ReportTitleBand1;
            this.Text55.Page = this.Page1;
            this.Text55.Parent = this.ReportTitleBand1;
            this.Text59.Page = this.Page1;
            this.Text59.Parent = this.ReportTitleBand1;
            this.Text63.Page = this.Page1;
            this.Text63.Parent = this.ReportTitleBand1;
            this.Text68.Page = this.Page1;
            this.Text68.Parent = this.ReportTitleBand1;
            this.Text72.Page = this.Page1;
            this.Text72.Parent = this.ReportTitleBand1;
            this.Text76.Page = this.Page1;
            this.Text76.Parent = this.ReportTitleBand1;
            this.Text80.Page = this.Page1;
            this.Text80.Parent = this.ReportTitleBand1;
            this.Text88.Page = this.Page1;
            this.Text88.Parent = this.ReportTitleBand1;
            this.Text91.Page = this.Page1;
            this.Text91.Parent = this.ReportTitleBand1;
            this.Text93.Page = this.Page1;
            this.Text93.Parent = this.ReportTitleBand1;
            this.Text84.Page = this.Page1;
            this.Text84.Parent = this.ReportTitleBand1;
            this.Text53.Page = this.Page1;
            this.Text53.Parent = this.ReportTitleBand1;
            this.Text61.Page = this.Page1;
            this.Text61.Parent = this.ReportTitleBand1;
            this.Text70.Page = this.Page1;
            this.Text70.Parent = this.ReportTitleBand1;
            this.Text78.Page = this.Page1;
            this.Text78.Parent = this.ReportTitleBand1;
            this.Text86.Page = this.Page1;
            this.Text86.Parent = this.ReportTitleBand1;
            this.Text57.Page = this.Page1;
            this.Text57.Parent = this.ReportTitleBand1;
            this.Text74.Page = this.Page1;
            this.Text74.Parent = this.ReportTitleBand1;
            this.Text90.Page = this.Page1;
            this.Text90.Parent = this.ReportTitleBand1;
            this.Text94.Page = this.Page1;
            this.Text94.Parent = this.ReportTitleBand1;
            this.Text96.Page = this.Page1;
            this.Text96.Parent = this.ReportTitleBand1;
            this.Text98.Page = this.Page1;
            this.Text98.Parent = this.ReportTitleBand1;
            this.Text100.Page = this.Page1;
            this.Text100.Parent = this.ReportTitleBand1;
            this.Text102.Page = this.Page1;
            this.Text102.Parent = this.ReportTitleBand1;
            this.Text104.Page = this.Page1;
            this.Text104.Parent = this.ReportTitleBand1;
            this.Text106.Page = this.Page1;
            this.Text106.Parent = this.ReportTitleBand1;
            this.Text108.Page = this.Page1;
            this.Text108.Parent = this.ReportTitleBand1;
            this.Text110.Page = this.Page1;
            this.Text110.Parent = this.ReportTitleBand1;
            this.Text112.Page = this.Page1;
            this.Text112.Parent = this.ReportTitleBand1;
            this.Text114.Page = this.Page1;
            this.Text114.Parent = this.ReportTitleBand1;
            this.Text116.Page = this.Page1;
            this.Text116.Parent = this.ReportTitleBand1;
            this.Text118.Page = this.Page1;
            this.Text118.Parent = this.ReportTitleBand1;
            this.Text120.Page = this.Page1;
            this.Text120.Parent = this.ReportTitleBand1;
            this.Text122.Page = this.Page1;
            this.Text122.Parent = this.ReportTitleBand1;
            this.Text126.Page = this.Page1;
            this.Text126.Parent = this.ReportTitleBand1;
            this.Text128.Page = this.Page1;
            this.Text128.Parent = this.ReportTitleBand1;
            this.Text130.Page = this.Page1;
            this.Text130.Parent = this.ReportTitleBand1;
            this.Text124.Page = this.Page1;
            this.Text124.Parent = this.ReportTitleBand1;
            this.HeaderBand1.Page = this.Page1;
            this.HeaderBand1.Parent = this.Page1;
            this.Text7.Page = this.Page1;
            this.Text7.Parent = this.HeaderBand1;
            this.Text9.Page = this.Page1;
            this.Text9.Parent = this.HeaderBand1;
            this.Text11.Page = this.Page1;
            this.Text11.Parent = this.HeaderBand1;
            this.Text13.Page = this.Page1;
            this.Text13.Parent = this.HeaderBand1;
            this.Text15.Page = this.Page1;
            this.Text15.Parent = this.HeaderBand1;
            this.Text17.Page = this.Page1;
            this.Text17.Parent = this.HeaderBand1;
            this.Text19.Page = this.Page1;
            this.Text19.Parent = this.HeaderBand1;
            this.Text21.Page = this.Page1;
            this.Text21.Parent = this.HeaderBand1;
            this.Text23.Page = this.Page1;
            this.Text23.Parent = this.HeaderBand1;
            this.Text25.Page = this.Page1;
            this.Text25.Parent = this.HeaderBand1;
            this.GroupHeaderBand1.Page = this.Page1;
            this.GroupHeaderBand1.Parent = this.Page1;
            this.Text26.Page = this.Page1;
            this.Text26.Parent = this.GroupHeaderBand1;
            this.GroupHeaderBand2.Page = this.Page1;
            this.GroupHeaderBand2.Parent = this.Page1;
            this.Text27.Page = this.Page1;
            this.Text27.Parent = this.GroupHeaderBand2;
            this.DataBand1.Page = this.Page1;
            this.DataBand1.Parent = this.Page1;
            this.Text6.Page = this.Page1;
            this.Text6.Parent = this.DataBand1;
            this.Text8.Page = this.Page1;
            this.Text8.Parent = this.DataBand1;
            this.Text10.Page = this.Page1;
            this.Text10.Parent = this.DataBand1;
            this.Text12.Page = this.Page1;
            this.Text12.Parent = this.DataBand1;
            this.Text14.Page = this.Page1;
            this.Text14.Parent = this.DataBand1;
            this.Text16.Page = this.Page1;
            this.Text16.Parent = this.DataBand1;
            this.Text18.Page = this.Page1;
            this.Text18.Parent = this.DataBand1;
            this.Text20.Page = this.Page1;
            this.Text20.Parent = this.DataBand1;
            this.Text22.Page = this.Page1;
            this.Text22.Parent = this.DataBand1;
            this.Text24.Page = this.Page1;
            this.Text24.Parent = this.DataBand1;
            this.GroupFooterBand2.Page = this.Page1;
            this.GroupFooterBand2.Parent = this.Page1;
            this.Text28.Page = this.Page1;
            this.Text28.Parent = this.GroupFooterBand2;
            this.Text29.Page = this.Page1;
            this.Text29.Parent = this.GroupFooterBand2;
            this.Text30.Page = this.Page1;
            this.Text30.Parent = this.GroupFooterBand2;
            this.Text31.Page = this.Page1;
            this.Text31.Parent = this.GroupFooterBand2;
            this.Text32.Page = this.Page1;
            this.Text32.Parent = this.GroupFooterBand2;
            this.GroupFooterBand1.Page = this.Page1;
            this.GroupFooterBand1.Parent = this.Page1;
            this.Text33.Page = this.Page1;
            this.Text33.Parent = this.GroupFooterBand1;
            this.Text34.Page = this.Page1;
            this.Text34.Parent = this.GroupFooterBand1;
            this.Text35.Page = this.Page1;
            this.Text35.Parent = this.GroupFooterBand1;
            this.Text36.Page = this.Page1;
            this.Text36.Parent = this.GroupFooterBand1;
            this.Text37.Page = this.Page1;
            this.Text37.Parent = this.GroupFooterBand1;
            this.ReportSummaryBand1.Page = this.Page1;
            this.ReportSummaryBand1.Parent = this.Page1;
            this.DataBand1.BeginRender += new System.EventHandler(this.DataBand1__BeginRender);
            this.DataBand1.EndRender += new System.EventHandler(this.DataBand1__EndRender);
            this.GroupHeaderBand2.BeginRender += new System.EventHandler(this.GroupHeaderBand2__BeginRender);
            this.GroupHeaderBand2.EndRender += new System.EventHandler(this.GroupHeaderBand2__EndRender);
            this.GroupHeaderBand1.BeginRender += new System.EventHandler(this.GroupHeaderBand1__BeginRender);
            this.GroupHeaderBand1.EndRender += new System.EventHandler(this.GroupHeaderBand1__EndRender);
            this.DataBand1.Rendering += new System.EventHandler(this.DataBand1__Rendering);
            this.GroupHeaderBand2.Rendering += new System.EventHandler(this.GroupHeaderBand2__Rendering);
            this.GroupHeaderBand1.Rendering += new System.EventHandler(this.GroupHeaderBand1__Rendering);
            this.EndRender += new System.EventHandler(this.药库盘点表WordsToEnd__EndRender);
            this.AggregateFunctions = new object[] {
                    this.Text40_Sum,
                    this.Text41_Sum,
                    this.Text31_Sum,
                    this.Text32_Sum,
                    this.Text36_Sum,
                    this.Text37_Sum};
            // 
            // Add to PageFooterBand1.Components
            // 
            this.PageFooterBand1.Components.Clear();
            this.PageFooterBand1.Components.AddRange(new Stimulsoft.Report.Components.StiComponent[] {
                        this.Text38,
                        this.Text39,
                        this.Text40,
                        this.Text41});
            // 
            // Add to ReportTitleBand1.Components
            // 
            this.ReportTitleBand1.Components.Clear();
            this.ReportTitleBand1.Components.AddRange(new Stimulsoft.Report.Components.StiComponent[] {
                        this.Text3,
                        this.Text1,
                        this.Text2,
                        this.Text42,
                        this.Text43,
                        this.Text44,
                        this.Text46,
                        this.Text45,
                        this.Text47,
                        this.Text49,
                        this.Text52,
                        this.Text54,
                        this.Text56,
                        this.Text58,
                        this.Text60,
                        this.Text62,
                        this.Text64,
                        this.Text66,
                        this.Text67,
                        this.Text69,
                        this.Text5,
                        this.Text71,
                        this.Text73,
                        this.Text75,
                        this.Text77,
                        this.Text79,
                        this.Text81,
                        this.Text83,
                        this.Text85,
                        this.Text87,
                        this.Text89,
                        this.Text48,
                        this.Text51,
                        this.Text50,
                        this.Text55,
                        this.Text59,
                        this.Text63,
                        this.Text68,
                        this.Text72,
                        this.Text76,
                        this.Text80,
                        this.Text88,
                        this.Text91,
                        this.Text93,
                        this.Text84,
                        this.Text53,
                        this.Text61,
                        this.Text70,
                        this.Text78,
                        this.Text86,
                        this.Text57,
                        this.Text74,
                        this.Text90,
                        this.Text94,
                        this.Text96,
                        this.Text98,
                        this.Text100,
                        this.Text102,
                        this.Text104,
                        this.Text106,
                        this.Text108,
                        this.Text110,
                        this.Text112,
                        this.Text114,
                        this.Text116,
                        this.Text118,
                        this.Text120,
                        this.Text122,
                        this.Text126,
                        this.Text128,
                        this.Text130,
                        this.Text124});
            // 
            // Add to HeaderBand1.Components
            // 
            this.HeaderBand1.Components.Clear();
            this.HeaderBand1.Components.AddRange(new Stimulsoft.Report.Components.StiComponent[] {
                        this.Text7,
                        this.Text9,
                        this.Text11,
                        this.Text13,
                        this.Text15,
                        this.Text17,
                        this.Text19,
                        this.Text21,
                        this.Text23,
                        this.Text25});
            // 
            // Add to GroupHeaderBand1.Components
            // 
            this.GroupHeaderBand1.Components.Clear();
            this.GroupHeaderBand1.Components.AddRange(new Stimulsoft.Report.Components.StiComponent[] {
                        this.Text26});
            // 
            // Add to GroupHeaderBand2.Components
            // 
            this.GroupHeaderBand2.Components.Clear();
            this.GroupHeaderBand2.Components.AddRange(new Stimulsoft.Report.Components.StiComponent[] {
                        this.Text27});
            // 
            // Add to DataBand1.Components
            // 
            this.DataBand1.Components.Clear();
            this.DataBand1.Components.AddRange(new Stimulsoft.Report.Components.StiComponent[] {
                        this.Text6,
                        this.Text8,
                        this.Text10,
                        this.Text12,
                        this.Text14,
                        this.Text16,
                        this.Text18,
                        this.Text20,
                        this.Text22,
                        this.Text24});
            // 
            // Add to GroupFooterBand2.Components
            // 
            this.GroupFooterBand2.Components.Clear();
            this.GroupFooterBand2.Components.AddRange(new Stimulsoft.Report.Components.StiComponent[] {
                        this.Text28,
                        this.Text29,
                        this.Text30,
                        this.Text31,
                        this.Text32});
            // 
            // Add to GroupFooterBand1.Components
            // 
            this.GroupFooterBand1.Components.Clear();
            this.GroupFooterBand1.Components.AddRange(new Stimulsoft.Report.Components.StiComponent[] {
                        this.Text33,
                        this.Text34,
                        this.Text35,
                        this.Text36,
                        this.Text37});
            // 
            // Add to Page1.Components
            // 
            this.Page1.Components.Clear();
            this.Page1.Components.AddRange(new Stimulsoft.Report.Components.StiComponent[] {
                        this.PageFooterBand1,
                        this.ReportTitleBand1,
                        this.HeaderBand1,
                        this.GroupHeaderBand1,
                        this.GroupHeaderBand2,
                        this.DataBand1,
                        this.GroupFooterBand2,
                        this.GroupFooterBand1,
                        this.ReportSummaryBand1});
            // 
            // Add to Pages
            // 
            this.Pages.Clear();
            this.Pages.AddRange(new Stimulsoft.Report.Components.StiPage[] {
                        this.Page1});
            this.view明细.Columns.AddRange(new Stimulsoft.Report.Dictionary.StiDataColumn[] {
                        new Stimulsoft.Report.Dictionary.StiDataColumn("Dl_Code", "Dl_Code", "Dl_Code", typeof(string)),
                        new Stimulsoft.Report.Dictionary.StiDataColumn("Dl_Name", "Dl_Name", "Dl_Name", typeof(string)),
                        new Stimulsoft.Report.Dictionary.StiDataColumn("Yp_Code", "Yp_Code", "Yp_Code", typeof(string)),
                        new Stimulsoft.Report.Dictionary.StiDataColumn("IsJb", "IsJb", "IsJb", typeof(string)),
                        new Stimulsoft.Report.Dictionary.StiDataColumn("Yp_Name", "Yp_Name", "Yp_Name", typeof(string)),
                        new Stimulsoft.Report.Dictionary.StiDataColumn("Yp_Jc", "Yp_Jc", "Yp_Jc", typeof(string)),
                        new Stimulsoft.Report.Dictionary.StiDataColumn("Mx_Code", "Mx_Code", "Mx_Code", typeof(string)),
                        new Stimulsoft.Report.Dictionary.StiDataColumn("Mx_Gg", "Mx_Gg", "Mx_Gg", typeof(string)),
                        new Stimulsoft.Report.Dictionary.StiDataColumn("Mx_Cd", "Mx_Cd", "Mx_Cd", typeof(string)),
                        new Stimulsoft.Report.Dictionary.StiDataColumn("Mx_Gyzz", "Mx_Gyzz", "Mx_Gyzz", typeof(string)),
                        new Stimulsoft.Report.Dictionary.StiDataColumn("Yp_Dw", "Yp_Dw", "Yp_Dw", typeof(string)),
                        new Stimulsoft.Report.Dictionary.StiDataColumn("Xx_Code", "Xx_Code", "Xx_Code", typeof(string)),
                        new Stimulsoft.Report.Dictionary.StiDataColumn("Yp_Ph", "Yp_Ph", "Yp_Ph", typeof(string)),
                        new Stimulsoft.Report.Dictionary.StiDataColumn("Yp_Yxq", "Yp_Yxq", "Yp_Yxq", typeof(DateTime)),
                        new Stimulsoft.Report.Dictionary.StiDataColumn("Yp_Cgj", "Yp_Cgj", "Yp_Cgj", typeof(decimal)),
                        new Stimulsoft.Report.Dictionary.StiDataColumn("Yp_Xsj", "Yp_Xsj", "Yp_Xsj", typeof(decimal)),
                        new Stimulsoft.Report.Dictionary.StiDataColumn("Mx_Sl", "Mx_Sl", "Mx_Sl", typeof(decimal)),
                        new Stimulsoft.Report.Dictionary.StiDataColumn("Pd_Sl", "Pd_Sl", "Pd_Sl", typeof(decimal)),
                        new Stimulsoft.Report.Dictionary.StiDataColumn("Pd_Month", "Pd_Month", "Pd_Month", typeof(string)),
                        new Stimulsoft.Report.Dictionary.StiDataColumn("Pd_CgMoney", "Pd_CgMoney", "Pd_CgMoney", typeof(decimal)),
                        new Stimulsoft.Report.Dictionary.StiDataColumn("Pd_XsMoney", "Pd_XsMoney", "Pd_XsMoney", typeof(decimal)),
                        new Stimulsoft.Report.Dictionary.StiDataColumn("PdC_Sl", "PdC_Sl", "PdC_Sl", typeof(decimal)),
                        new Stimulsoft.Report.Dictionary.StiDataColumn("PdC_CgMoney", "PdC_CgMoney", "PdC_CgMoney", typeof(decimal)),
                        new Stimulsoft.Report.Dictionary.StiDataColumn("PdC_XsMoney", "PdC_XsMoney", "PdC_XsMoney", typeof(decimal))});
            this.DataSources.Add(this.view明细);
        }
        
        #region DataSource view明细
        public class view明细DataSource : Stimulsoft.Report.Dictionary.StiDataViewSource
        {
            
            public view明细DataSource() : 
                    base("view明细", "view明细")
            {
            }
            
            public virtual string Dl_Code
            {
                get
                {
                    return ((string)(StiReport.ChangeType(this["Dl_Code"], typeof(string), true)));
                }
            }
            
            public virtual string Dl_Name
            {
                get
                {
                    return ((string)(StiReport.ChangeType(this["Dl_Name"], typeof(string), true)));
                }
            }
            
            public virtual string Yp_Code
            {
                get
                {
                    return ((string)(StiReport.ChangeType(this["Yp_Code"], typeof(string), true)));
                }
            }
            
            public virtual string IsJb
            {
                get
                {
                    return ((string)(StiReport.ChangeType(this["IsJb"], typeof(string), true)));
                }
            }
            
            public virtual string Yp_Name
            {
                get
                {
                    return ((string)(StiReport.ChangeType(this["Yp_Name"], typeof(string), true)));
                }
            }
            
            public virtual string Yp_Jc
            {
                get
                {
                    return ((string)(StiReport.ChangeType(this["Yp_Jc"], typeof(string), true)));
                }
            }
            
            public virtual string Mx_Code
            {
                get
                {
                    return ((string)(StiReport.ChangeType(this["Mx_Code"], typeof(string), true)));
                }
            }
            
            public virtual string Mx_Gg
            {
                get
                {
                    return ((string)(StiReport.ChangeType(this["Mx_Gg"], typeof(string), true)));
                }
            }
            
            public virtual string Mx_Cd
            {
                get
                {
                    return ((string)(StiReport.ChangeType(this["Mx_Cd"], typeof(string), true)));
                }
            }
            
            public virtual string Mx_Gyzz
            {
                get
                {
                    return ((string)(StiReport.ChangeType(this["Mx_Gyzz"], typeof(string), true)));
                }
            }
            
            public virtual string Yp_Dw
            {
                get
                {
                    return ((string)(StiReport.ChangeType(this["Yp_Dw"], typeof(string), true)));
                }
            }
            
            public virtual string Xx_Code
            {
                get
                {
                    return ((string)(StiReport.ChangeType(this["Xx_Code"], typeof(string), true)));
                }
            }
            
            public virtual string Yp_Ph
            {
                get
                {
                    return ((string)(StiReport.ChangeType(this["Yp_Ph"], typeof(string), true)));
                }
            }
            
            public virtual DateTime Yp_Yxq
            {
                get
                {
                    return ((DateTime)(StiReport.ChangeType(this["Yp_Yxq"], typeof(DateTime), true)));
                }
            }
            
            public virtual decimal Yp_Cgj
            {
                get
                {
                    return ((decimal)(StiReport.ChangeType(this["Yp_Cgj"], typeof(decimal), true)));
                }
            }
            
            public virtual decimal Yp_Xsj
            {
                get
                {
                    return ((decimal)(StiReport.ChangeType(this["Yp_Xsj"], typeof(decimal), true)));
                }
            }
            
            public virtual decimal Mx_Sl
            {
                get
                {
                    return ((decimal)(StiReport.ChangeType(this["Mx_Sl"], typeof(decimal), true)));
                }
            }
            
            public virtual decimal Pd_Sl
            {
                get
                {
                    return ((decimal)(StiReport.ChangeType(this["Pd_Sl"], typeof(decimal), true)));
                }
            }
            
            public virtual string Pd_Month
            {
                get
                {
                    return ((string)(StiReport.ChangeType(this["Pd_Month"], typeof(string), true)));
                }
            }
            
            public virtual decimal Pd_CgMoney
            {
                get
                {
                    return ((decimal)(StiReport.ChangeType(this["Pd_CgMoney"], typeof(decimal), true)));
                }
            }
            
            public virtual decimal Pd_XsMoney
            {
                get
                {
                    return ((decimal)(StiReport.ChangeType(this["Pd_XsMoney"], typeof(decimal), true)));
                }
            }
            
            public virtual decimal PdC_Sl
            {
                get
                {
                    return ((decimal)(StiReport.ChangeType(this["PdC_Sl"], typeof(decimal), true)));
                }
            }
            
            public virtual decimal PdC_CgMoney
            {
                get
                {
                    return ((decimal)(StiReport.ChangeType(this["PdC_CgMoney"], typeof(decimal), true)));
                }
            }
            
            public virtual decimal PdC_XsMoney
            {
                get
                {
                    return ((decimal)(StiReport.ChangeType(this["PdC_XsMoney"], typeof(decimal), true)));
                }
            }
        }
        #endregion DataSource view明细
        #endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>