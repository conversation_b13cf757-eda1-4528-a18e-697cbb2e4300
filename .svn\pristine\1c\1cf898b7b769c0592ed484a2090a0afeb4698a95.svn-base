﻿/**  版本信息模板在安装目录下，可自行修改。
* D_DRYB_ZYJS.cs
*
* 功 能： N/A
* 类 名： D_DRYB_ZYJS
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2017/12/15 10:05:04   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;

namespace DAL
{
	/// <summary>
	/// 数据访问类:D_DRYB_ZYJS
	/// </summary>
	public partial class D_DRYB_ZYJS
	{
		public D_DRYB_ZYJS()
		{}
		#region  BasicMethod

		/// <summary>
		/// 得到最大ID
		/// </summary>
		public int GetMaxId()
		{
		return HisVar.HisVar.Sqldal.GetMaxID("Id", "DRYB_ZYJS"); 
		}

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(int Id)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from DRYB_ZYJS");
			strSql.Append(" where Id=@Id");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.Int,4)
			};
			parameters[0].Value = Id;

			return HisVar.HisVar.Sqldal.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public int Add(Model.M_DRYB_ZYJS model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into DRYB_ZYJS(");
			strSql.Append("AKC190,AKA130,Dj_Code,ZY_JSFS,JF_MONEY,AccountMoney,LrDate)");
			strSql.Append(" values (");
			strSql.Append("@AKC190,@AKA130,@Dj_Code,@ZY_JSFS,@JF_MONEY,@AccountMoney,@LrDate)");
			strSql.Append(";select @@IDENTITY");
			SqlParameter[] parameters = {
					new SqlParameter("@AKC190", SqlDbType.VarChar,18),
					new SqlParameter("@AKA130", SqlDbType.VarChar,3),
					new SqlParameter("@Dj_Code", SqlDbType.Char,18),
					new SqlParameter("@ZY_JSFS", SqlDbType.Char,1),
					new SqlParameter("@JF_MONEY", SqlDbType.Decimal,5),
					new SqlParameter("@AccountMoney", SqlDbType.Decimal,5),
					new SqlParameter("@LrDate", SqlDbType.SmallDateTime)};
			parameters[0].Value = model.AKC190;
			parameters[1].Value = model.AKA130;
			parameters[2].Value = model.Dj_Code;
			parameters[3].Value = model.ZY_JSFS;
			parameters[4].Value = model.JF_MONEY;
			parameters[5].Value = model.AccountMoney;
			parameters[6].Value = model.LrDate;

			object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString(),parameters);
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.M_DRYB_ZYJS model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update DRYB_ZYJS set ");
			strSql.Append("AKC190=@AKC190,");
			strSql.Append("AKA130=@AKA130,");
			strSql.Append("Dj_Code=@Dj_Code,");
			strSql.Append("ZY_JSFS=@ZY_JSFS,");
			strSql.Append("JF_MONEY=@JF_MONEY,");
			strSql.Append("AccountMoney=@AccountMoney,");
			strSql.Append("LrDate=@LrDate");
			strSql.Append(" where Id=@Id");
			SqlParameter[] parameters = {
					new SqlParameter("@AKC190", SqlDbType.VarChar,18),
					new SqlParameter("@AKA130", SqlDbType.VarChar,3),
					new SqlParameter("@Dj_Code", SqlDbType.Char,18),
					new SqlParameter("@ZY_JSFS", SqlDbType.Char,1),
					new SqlParameter("@JF_MONEY", SqlDbType.Decimal,5),
					new SqlParameter("@AccountMoney", SqlDbType.Decimal,5),
					new SqlParameter("@LrDate", SqlDbType.SmallDateTime),
					new SqlParameter("@Id", SqlDbType.Int,4)};
			parameters[0].Value = model.AKC190;
			parameters[1].Value = model.AKA130;
			parameters[2].Value = model.Dj_Code;
			parameters[3].Value = model.ZY_JSFS;
			parameters[4].Value = model.JF_MONEY;
			parameters[5].Value = model.AccountMoney;
			parameters[6].Value = model.LrDate;
			parameters[7].Value = model.Id;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(int Id)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from DRYB_ZYJS ");
			strSql.Append(" where Id=@Id");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.Int,4)
			};
			parameters[0].Value = Id;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string Idlist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from DRYB_ZYJS ");
			strSql.Append(" where Id in ("+Idlist + ")  ");
			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.M_DRYB_ZYJS GetModel(int Id)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 Id,AKC190,AKA130,Dj_Code,ZY_JSFS,JF_MONEY,AccountMoney,LrDate from DRYB_ZYJS ");
			strSql.Append(" where Id=@Id");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.Int,4)
			};
			parameters[0].Value = Id;

			Model.M_DRYB_ZYJS model=new Model.M_DRYB_ZYJS();
			DataSet ds=HisVar.HisVar.Sqldal.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.M_DRYB_ZYJS DataRowToModel(DataRow row)
		{
			Model.M_DRYB_ZYJS model=new Model.M_DRYB_ZYJS();
			if (row != null)
			{
				if(row["Id"]!=null && row["Id"].ToString()!="")
				{
					model.Id=int.Parse(row["Id"].ToString());
				}
				if(row["AKC190"]!=null)
				{
					model.AKC190=row["AKC190"].ToString();
				}
				if(row["AKA130"]!=null)
				{
					model.AKA130=row["AKA130"].ToString();
				}
				if(row["Dj_Code"]!=null)
				{
					model.Dj_Code=row["Dj_Code"].ToString();
				}
				if(row["ZY_JSFS"]!=null)
				{
					model.ZY_JSFS=row["ZY_JSFS"].ToString();
				}
				if(row["JF_MONEY"]!=null && row["JF_MONEY"].ToString()!="")
				{
					model.JF_MONEY=decimal.Parse(row["JF_MONEY"].ToString());
				}
				if(row["AccountMoney"]!=null && row["AccountMoney"].ToString()!="")
				{
					model.AccountMoney=decimal.Parse(row["AccountMoney"].ToString());
				}
				if(row["LrDate"]!=null && row["LrDate"].ToString()!="")
				{
					model.LrDate=DateTime.Parse(row["LrDate"].ToString());
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select Id,AKC190,AKA130,CASE AKA130 WHEN '21' THEN '普通住院' WHEN '51' THEN '生育住院' END AKA130_Name ,Dj_Code,ZY_JSFS,CASE ZY_JSFS WHEN '1' THEN '普通住院结算' WHEN '3' THEN '年终结算' END	Zy_JsFs_Name,JF_MONEY,AccountMoney,LrDate ");
			strSql.Append(" FROM DRYB_ZYJS ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
            strSql.Append(" ORDER BY LrDate asc ");
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" Id,AKC190,AKA130,Dj_Code,ZY_JSFS,JF_MONEY,AccountMoney,LrDate ");
			strSql.Append(" FROM DRYB_ZYJS ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM DRYB_ZYJS ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.Id desc");
			}
			strSql.Append(")AS Row, T.*  from DRYB_ZYJS T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "DRYB_ZYJS";
			parameters[1].Value = "Id";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return HisVar.HisVar.Sqldal.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

