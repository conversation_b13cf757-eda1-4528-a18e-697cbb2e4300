<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:tm="http://microsoft.com/wsdl/mime/textMatching/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:tns="http://tempuri.org/" xmlns:s="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:s1="http://tempuri.org/AbstractTypes" xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <s:schema elementFormDefault="qualified" targetNamespace="http://tempuri.org/">
      <s:element name="HelloWorld">
        <s:complexType />
      </s:element>
      <s:element name="HelloWorldResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="HelloWorldResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UploadFile">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="upData" type="s:base64Binary" />
            <s:element minOccurs="0" maxOccurs="1" name="foldername" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="filename" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UploadFileResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="UploadFileResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DownLoadPdf">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="foldername" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="filename" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DownLoadPdfResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="DownLoadPdfResult" type="s:base64Binary" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="pdfDel">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="foldername" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="filename" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="pdfDelResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="pdfDelResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="Test">
        <s:complexType />
      </s:element>
      <s:element name="TestResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="TestResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="string" nillable="true" type="s:string" />
      <s:element name="base64Binary" nillable="true" type="s:base64Binary" />
    </s:schema>
    <s:schema targetNamespace="http://tempuri.org/AbstractTypes">
      <s:import namespace="http://schemas.xmlsoap.org/soap/encoding/" />
      <s:complexType name="StringArray">
        <s:complexContent mixed="false">
          <s:restriction base="soapenc:Array">
            <s:sequence>
              <s:element minOccurs="0" maxOccurs="unbounded" name="String" type="s:string" />
            </s:sequence>
          </s:restriction>
        </s:complexContent>
      </s:complexType>
    </s:schema>
  </wsdl:types>
  <wsdl:message name="HelloWorldSoapIn">
    <wsdl:part name="parameters" element="tns:HelloWorld" />
  </wsdl:message>
  <wsdl:message name="HelloWorldSoapOut">
    <wsdl:part name="parameters" element="tns:HelloWorldResponse" />
  </wsdl:message>
  <wsdl:message name="UploadFileSoapIn">
    <wsdl:part name="parameters" element="tns:UploadFile" />
  </wsdl:message>
  <wsdl:message name="UploadFileSoapOut">
    <wsdl:part name="parameters" element="tns:UploadFileResponse" />
  </wsdl:message>
  <wsdl:message name="DownLoadPdfSoapIn">
    <wsdl:part name="parameters" element="tns:DownLoadPdf" />
  </wsdl:message>
  <wsdl:message name="DownLoadPdfSoapOut">
    <wsdl:part name="parameters" element="tns:DownLoadPdfResponse" />
  </wsdl:message>
  <wsdl:message name="pdfDelSoapIn">
    <wsdl:part name="parameters" element="tns:pdfDel" />
  </wsdl:message>
  <wsdl:message name="pdfDelSoapOut">
    <wsdl:part name="parameters" element="tns:pdfDelResponse" />
  </wsdl:message>
  <wsdl:message name="TestSoapIn">
    <wsdl:part name="parameters" element="tns:Test" />
  </wsdl:message>
  <wsdl:message name="TestSoapOut">
    <wsdl:part name="parameters" element="tns:TestResponse" />
  </wsdl:message>
  <wsdl:message name="HelloWorldHttpGetIn" />
  <wsdl:message name="HelloWorldHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="UploadFileHttpGetIn">
    <wsdl:part name="upData" type="s1:StringArray" />
    <wsdl:part name="foldername" type="s:string" />
    <wsdl:part name="filename" type="s:string" />
  </wsdl:message>
  <wsdl:message name="UploadFileHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="DownLoadPdfHttpGetIn">
    <wsdl:part name="foldername" type="s:string" />
    <wsdl:part name="filename" type="s:string" />
  </wsdl:message>
  <wsdl:message name="DownLoadPdfHttpGetOut">
    <wsdl:part name="Body" element="tns:base64Binary" />
  </wsdl:message>
  <wsdl:message name="pdfDelHttpGetIn">
    <wsdl:part name="foldername" type="s:string" />
    <wsdl:part name="filename" type="s:string" />
  </wsdl:message>
  <wsdl:message name="pdfDelHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="TestHttpGetIn" />
  <wsdl:message name="TestHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="HelloWorldHttpPostIn" />
  <wsdl:message name="HelloWorldHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="UploadFileHttpPostIn">
    <wsdl:part name="upData" type="s1:StringArray" />
    <wsdl:part name="foldername" type="s:string" />
    <wsdl:part name="filename" type="s:string" />
  </wsdl:message>
  <wsdl:message name="UploadFileHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="DownLoadPdfHttpPostIn">
    <wsdl:part name="foldername" type="s:string" />
    <wsdl:part name="filename" type="s:string" />
  </wsdl:message>
  <wsdl:message name="DownLoadPdfHttpPostOut">
    <wsdl:part name="Body" element="tns:base64Binary" />
  </wsdl:message>
  <wsdl:message name="pdfDelHttpPostIn">
    <wsdl:part name="foldername" type="s:string" />
    <wsdl:part name="filename" type="s:string" />
  </wsdl:message>
  <wsdl:message name="pdfDelHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="TestHttpPostIn" />
  <wsdl:message name="TestHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:portType name="DzblFileSendSoap">
    <wsdl:operation name="HelloWorld">
      <wsdl:input message="tns:HelloWorldSoapIn" />
      <wsdl:output message="tns:HelloWorldSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="UploadFile">
      <wsdl:input message="tns:UploadFileSoapIn" />
      <wsdl:output message="tns:UploadFileSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="DownLoadPdf">
      <wsdl:input message="tns:DownLoadPdfSoapIn" />
      <wsdl:output message="tns:DownLoadPdfSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="pdfDel">
      <wsdl:input message="tns:pdfDelSoapIn" />
      <wsdl:output message="tns:pdfDelSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="Test">
      <wsdl:input message="tns:TestSoapIn" />
      <wsdl:output message="tns:TestSoapOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:portType name="DzblFileSendHttpGet">
    <wsdl:operation name="HelloWorld">
      <wsdl:input message="tns:HelloWorldHttpGetIn" />
      <wsdl:output message="tns:HelloWorldHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="UploadFile">
      <wsdl:input message="tns:UploadFileHttpGetIn" />
      <wsdl:output message="tns:UploadFileHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="DownLoadPdf">
      <wsdl:input message="tns:DownLoadPdfHttpGetIn" />
      <wsdl:output message="tns:DownLoadPdfHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="pdfDel">
      <wsdl:input message="tns:pdfDelHttpGetIn" />
      <wsdl:output message="tns:pdfDelHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="Test">
      <wsdl:input message="tns:TestHttpGetIn" />
      <wsdl:output message="tns:TestHttpGetOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:portType name="DzblFileSendHttpPost">
    <wsdl:operation name="HelloWorld">
      <wsdl:input message="tns:HelloWorldHttpPostIn" />
      <wsdl:output message="tns:HelloWorldHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="UploadFile">
      <wsdl:input message="tns:UploadFileHttpPostIn" />
      <wsdl:output message="tns:UploadFileHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="DownLoadPdf">
      <wsdl:input message="tns:DownLoadPdfHttpPostIn" />
      <wsdl:output message="tns:DownLoadPdfHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="pdfDel">
      <wsdl:input message="tns:pdfDelHttpPostIn" />
      <wsdl:output message="tns:pdfDelHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="Test">
      <wsdl:input message="tns:TestHttpPostIn" />
      <wsdl:output message="tns:TestHttpPostOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="DzblFileSendSoap" type="tns:DzblFileSendSoap">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="HelloWorld">
      <soap:operation soapAction="http://tempuri.org/HelloWorld" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UploadFile">
      <soap:operation soapAction="http://tempuri.org/UploadFile" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DownLoadPdf">
      <soap:operation soapAction="http://tempuri.org/DownLoadPdf" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="pdfDel">
      <soap:operation soapAction="http://tempuri.org/pdfDel" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Test">
      <soap:operation soapAction="http://tempuri.org/Test" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="DzblFileSendSoap12" type="tns:DzblFileSendSoap">
    <soap12:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="HelloWorld">
      <soap12:operation soapAction="http://tempuri.org/HelloWorld" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UploadFile">
      <soap12:operation soapAction="http://tempuri.org/UploadFile" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DownLoadPdf">
      <soap12:operation soapAction="http://tempuri.org/DownLoadPdf" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="pdfDel">
      <soap12:operation soapAction="http://tempuri.org/pdfDel" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Test">
      <soap12:operation soapAction="http://tempuri.org/Test" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="DzblFileSendHttpGet" type="tns:DzblFileSendHttpGet">
    <http:binding verb="GET" />
    <wsdl:operation name="HelloWorld">
      <http:operation location="/HelloWorld" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UploadFile">
      <http:operation location="/UploadFile" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DownLoadPdf">
      <http:operation location="/DownLoadPdf" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="pdfDel">
      <http:operation location="/pdfDel" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Test">
      <http:operation location="/Test" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="DzblFileSendHttpPost" type="tns:DzblFileSendHttpPost">
    <http:binding verb="POST" />
    <wsdl:operation name="HelloWorld">
      <http:operation location="/HelloWorld" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UploadFile">
      <http:operation location="/UploadFile" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DownLoadPdf">
      <http:operation location="/DownLoadPdf" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="pdfDel">
      <http:operation location="/pdfDel" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Test">
      <http:operation location="/Test" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="DzblFileSend">
    <wsdl:port name="DzblFileSendSoap" binding="tns:DzblFileSendSoap">
      <soap:address location="http://localhost/His_Database/DzblFileSend.asmx" />
    </wsdl:port>
    <wsdl:port name="DzblFileSendSoap12" binding="tns:DzblFileSendSoap12">
      <soap12:address location="http://localhost/His_Database/DzblFileSend.asmx" />
    </wsdl:port>
    <wsdl:port name="DzblFileSendHttpGet" binding="tns:DzblFileSendHttpGet">
      <http:address location="http://localhost/His_Database/DzblFileSend.asmx" />
    </wsdl:port>
    <wsdl:port name="DzblFileSendHttpPost" binding="tns:DzblFileSendHttpPost">
      <http:address location="http://localhost/His_Database/DzblFileSend.asmx" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>