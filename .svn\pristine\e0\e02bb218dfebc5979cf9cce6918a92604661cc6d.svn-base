﻿/**  版本信息模板在安装目录下，可自行修改。
* D_LIS_TestXmDy.cs
*
* 功 能： N/A
* 类 名： D_LIS_TestXmDy
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/10/17 星期一 下午 1:38:54   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;

namespace DAL
{
	/// <summary>
	/// 数据访问类:D_LIS_TestXmDy
	/// </summary>
	public partial class D_LIS_TestXmDy
	{
		public D_LIS_TestXmDy()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string Xm_Code,string TestXm_Code)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from LIS_TestXmDy");
			strSql.Append(" where Xm_Code=@Xm_Code and TestXm_Code=@TestXm_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Xm_Code", SqlDbType.Char,12),
					new SqlParameter("@TestXm_Code", SqlDbType.Char,5)			};
			parameters[0].Value = Xm_Code;
			parameters[1].Value = TestXm_Code;

			return HisVar.HisVar.Sqldal.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ModelOld.M_LIS_TestXmDy model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into LIS_TestXmDy(");
			strSql.Append("Xm_Code,TestXm_Code)");
			strSql.Append(" values (");
			strSql.Append("@Xm_Code,@TestXm_Code)");
			SqlParameter[] parameters = {
					new SqlParameter("@Xm_Code", SqlDbType.Char,12),
					new SqlParameter("@TestXm_Code", SqlDbType.Char,5)};
			parameters[0].Value = model.Xm_Code;
			parameters[1].Value = model.TestXm_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ModelOld.M_LIS_TestXmDy model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update LIS_TestXmDy set ");
			strSql.Append("Xm_Code=@Xm_Code,");
			strSql.Append("TestXm_Code=@TestXm_Code");
			strSql.Append(" where Xm_Code=@Xm_Code and TestXm_Code=@TestXm_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Xm_Code", SqlDbType.Char,12),
					new SqlParameter("@TestXm_Code", SqlDbType.Char,5)};
			parameters[0].Value = model.Xm_Code;
			parameters[1].Value = model.TestXm_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string Xm_Code,string TestXm_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from LIS_TestXmDy ");
			strSql.Append(" where Xm_Code=@Xm_Code and TestXm_Code=@TestXm_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Xm_Code", SqlDbType.Char,12),
					new SqlParameter("@TestXm_Code", SqlDbType.Char,5)			};
			parameters[0].Value = Xm_Code;
			parameters[1].Value = TestXm_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_LIS_TestXmDy GetModel(string Xm_Code,string TestXm_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 Xm_Code,TestXm_Code from LIS_TestXmDy ");
			strSql.Append(" where Xm_Code=@Xm_Code and TestXm_Code=@TestXm_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Xm_Code", SqlDbType.Char,12),
					new SqlParameter("@TestXm_Code", SqlDbType.Char,5)			};
			parameters[0].Value = Xm_Code;
			parameters[1].Value = TestXm_Code;

			ModelOld.M_LIS_TestXmDy model=new ModelOld.M_LIS_TestXmDy();
			DataSet ds=HisVar.HisVar.Sqldal.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_LIS_TestXmDy DataRowToModel(DataRow row)
		{
			ModelOld.M_LIS_TestXmDy model=new ModelOld.M_LIS_TestXmDy();
			if (row != null)
			{
				if(row["Xm_Code"]!=null)
				{
					model.Xm_Code=row["Xm_Code"].ToString();
				}
				if(row["TestXm_Code"]!=null)
				{
					model.TestXm_Code=row["TestXm_Code"].ToString();
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
            strSql.Append("SELECT  LIS_TestXmDy.Xm_Code ,Xm_Name ,LIS_TestXmDy.TestXm_Code ,TestXm_Name ");
			strSql.Append(" FROM  LIS_TestXm ,LIS_TestXmDy ,Zd_Ml_Xm3 ");
            strSql.Append(" WHERE  LIS_TestXmDy.Xm_Code = Zd_Ml_Xm3.Xm_Code AND LIS_TestXmDy.TestXm_Code = LIS_TestXm.TestXm_Code ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" AND "+strWhere);
			}
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" Xm_Code,TestXm_Code ");
			strSql.Append(" FROM LIS_TestXmDy ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM LIS_TestXmDy ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.TestXm_Code desc");
			}
			strSql.Append(")AS Row, T.*  from LIS_TestXmDy T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "LIS_TestXmDy";
			parameters[1].Value = "TestXm_Code";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return HisVar.HisVar.Sqldal.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

