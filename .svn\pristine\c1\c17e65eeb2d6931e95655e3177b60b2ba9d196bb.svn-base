﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class Materials_Buy_Search
    Inherits HisControl.BaseForm

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel()
        Me.M_Buy_Code = New CustomControl.MyTextBox()
        Me.MaterialsSup = New CustomControl.MyDtComobo()
        Me.MaterialsWh = New CustomControl.MyDtComobo()
        Me.Search_Date = New CustomControl.MyDateEdit()
        Me.Jsr_MyDtComobo1 = New CustomControl.MyDtComobo()
        Me.WCZT_MySingleComobo1 = New CustomControl.MySingleComobo()
        Me.Date_MySingleComobo1 = New CustomControl.MySingleComobo()
        Me.CxZt_MySingleComobo1 = New CustomControl.MySingleComobo()
        Me.Search_MyButton1 = New CustomControl.MyButton()
        Me.MyGrid1 = New CustomControl.MyGrid()
        Me.TableLayoutPanel1.SuspendLayout()
        Me.SuspendLayout()
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.ColumnCount = 6
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 198.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 187.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 82.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 125.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 155.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 8.0!))
        Me.TableLayoutPanel1.Controls.Add(Me.M_Buy_Code, 0, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.MaterialsSup, 0, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.MaterialsWh, 1, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.Search_Date, 3, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.Jsr_MyDtComobo1, 1, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.WCZT_MySingleComobo1, 2, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.Date_MySingleComobo1, 2, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.CxZt_MySingleComobo1, 4, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.Search_MyButton1, 4, 1)
        Me.TableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Top
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(0, 0)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.RowCount = 3
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 26.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 26.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 26.0!))
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(902, 58)
        Me.TableLayoutPanel1.TabIndex = 0
        '
        'M_Buy_Code
        '
        Me.M_Buy_Code.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.M_Buy_Code.Captain = "入库编码"
        Me.M_Buy_Code.CaptainBackColor = System.Drawing.Color.Transparent
        Me.M_Buy_Code.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.M_Buy_Code.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.M_Buy_Code.CaptainWidth = 60.0!
        Me.M_Buy_Code.ContentForeColor = System.Drawing.Color.Black
        Me.M_Buy_Code.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.M_Buy_Code.Location = New System.Drawing.Point(3, 3)
        Me.M_Buy_Code.Multiline = False
        Me.M_Buy_Code.Name = "M_Buy_Code"
        Me.M_Buy_Code.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.M_Buy_Code.ReadOnly = False
        Me.M_Buy_Code.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.M_Buy_Code.SelectionStart = 0
        Me.M_Buy_Code.SelectStart = 0
        Me.M_Buy_Code.Size = New System.Drawing.Size(192, 20)
        Me.M_Buy_Code.TabIndex = 1
        Me.M_Buy_Code.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.M_Buy_Code.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.M_Buy_Code.Watermark = Nothing
        '
        'MaterialsSup
        '
        Me.MaterialsSup.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.MaterialsSup.Captain = "供 应 商"
        Me.MaterialsSup.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MaterialsSup.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.MaterialsSup.CaptainWidth = 60.0!
        Me.MaterialsSup.DataSource = Nothing
        Me.MaterialsSup.ItemHeight = 18
        Me.MaterialsSup.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MaterialsSup.Location = New System.Drawing.Point(3, 29)
        Me.MaterialsSup.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.MaterialsSup.MinimumSize = New System.Drawing.Size(0, 20)
        Me.MaterialsSup.Name = "MaterialsSup"
        Me.MaterialsSup.ReadOnly = False
        Me.MaterialsSup.Size = New System.Drawing.Size(192, 20)
        Me.MaterialsSup.TabIndex = 124
        Me.MaterialsSup.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'MaterialsWh
        '
        Me.MaterialsWh.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.MaterialsWh.Captain = "库    房"
        Me.MaterialsWh.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MaterialsWh.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.MaterialsWh.CaptainWidth = 60.0!
        Me.MaterialsWh.DataSource = Nothing
        Me.MaterialsWh.ItemHeight = 18
        Me.MaterialsWh.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MaterialsWh.Location = New System.Drawing.Point(201, 29)
        Me.MaterialsWh.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.MaterialsWh.MinimumSize = New System.Drawing.Size(0, 20)
        Me.MaterialsWh.Name = "MaterialsWh"
        Me.MaterialsWh.ReadOnly = False
        Me.MaterialsWh.Size = New System.Drawing.Size(181, 20)
        Me.MaterialsWh.TabIndex = 125
        Me.MaterialsWh.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'Search_Date
        '
        Me.Search_Date.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Search_Date.Captain = ""
        Me.Search_Date.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Search_Date.CaptainWidth = 0.0!
        Me.Search_Date.InitialSelection = C1.Win.C1Input.InitialSelectionEnum.CaretAtEnd
        Me.Search_Date.Location = New System.Drawing.Point(470, 29)
        Me.Search_Date.MaximumSize = New System.Drawing.Size(100000000, 20)
        Me.Search_Date.MinimumSize = New System.Drawing.Size(0, 20)
        Me.Search_Date.Name = "Search_Date"
        Me.Search_Date.Size = New System.Drawing.Size(119, 20)
        Me.Search_Date.TabIndex = 126
        Me.Search_Date.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Search_Date.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.Search_Date.VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.DropDown
        '
        'Jsr_MyDtComobo1
        '
        Me.Jsr_MyDtComobo1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Jsr_MyDtComobo1.Captain = "经手人"
        Me.Jsr_MyDtComobo1.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Jsr_MyDtComobo1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.Jsr_MyDtComobo1.CaptainWidth = 60.0!
        Me.Jsr_MyDtComobo1.DataSource = Nothing
        Me.Jsr_MyDtComobo1.ItemHeight = 18
        Me.Jsr_MyDtComobo1.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Jsr_MyDtComobo1.Location = New System.Drawing.Point(201, 3)
        Me.Jsr_MyDtComobo1.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.Jsr_MyDtComobo1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.Jsr_MyDtComobo1.Name = "Jsr_MyDtComobo1"
        Me.Jsr_MyDtComobo1.ReadOnly = False
        Me.Jsr_MyDtComobo1.Size = New System.Drawing.Size(181, 20)
        Me.Jsr_MyDtComobo1.TabIndex = 127
        Me.Jsr_MyDtComobo1.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'WCZT_MySingleComobo1
        '
        Me.WCZT_MySingleComobo1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.WCZT_MySingleComobo1.Captain = "完成状态"
        Me.WCZT_MySingleComobo1.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.WCZT_MySingleComobo1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.WCZT_MySingleComobo1.CaptainWidth = 80.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.WCZT_MySingleComobo1, 2)
        Me.WCZT_MySingleComobo1.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        Me.WCZT_MySingleComobo1.ItemHeight = 16
        Me.WCZT_MySingleComobo1.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.WCZT_MySingleComobo1.Location = New System.Drawing.Point(388, 3)
        Me.WCZT_MySingleComobo1.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.WCZT_MySingleComobo1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.WCZT_MySingleComobo1.Name = "WCZT_MySingleComobo1"
        Me.WCZT_MySingleComobo1.ReadOnly = False
        Me.WCZT_MySingleComobo1.Size = New System.Drawing.Size(201, 20)
        Me.WCZT_MySingleComobo1.TabIndex = 128
        Me.WCZT_MySingleComobo1.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'Date_MySingleComobo1
        '
        Me.Date_MySingleComobo1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Date_MySingleComobo1.Captain = ""
        Me.Date_MySingleComobo1.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Date_MySingleComobo1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.Date_MySingleComobo1.CaptainWidth = 0.0!
        Me.Date_MySingleComobo1.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        Me.Date_MySingleComobo1.ItemHeight = 16
        Me.Date_MySingleComobo1.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Date_MySingleComobo1.Location = New System.Drawing.Point(388, 29)
        Me.Date_MySingleComobo1.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.Date_MySingleComobo1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.Date_MySingleComobo1.Name = "Date_MySingleComobo1"
        Me.Date_MySingleComobo1.ReadOnly = False
        Me.Date_MySingleComobo1.Size = New System.Drawing.Size(76, 20)
        Me.Date_MySingleComobo1.TabIndex = 130
        Me.Date_MySingleComobo1.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'CxZt_MySingleComobo1
        '
        Me.CxZt_MySingleComobo1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.CxZt_MySingleComobo1.Captain = "冲销状态"
        Me.CxZt_MySingleComobo1.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.CxZt_MySingleComobo1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.CxZt_MySingleComobo1.CaptainWidth = 60.0!
        Me.CxZt_MySingleComobo1.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        Me.CxZt_MySingleComobo1.ItemHeight = 16
        Me.CxZt_MySingleComobo1.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.CxZt_MySingleComobo1.Location = New System.Drawing.Point(595, 3)
        Me.CxZt_MySingleComobo1.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.CxZt_MySingleComobo1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.CxZt_MySingleComobo1.Name = "CxZt_MySingleComobo1"
        Me.CxZt_MySingleComobo1.ReadOnly = False
        Me.CxZt_MySingleComobo1.Size = New System.Drawing.Size(149, 20)
        Me.CxZt_MySingleComobo1.TabIndex = 129
        Me.CxZt_MySingleComobo1.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'Search_MyButton1
        '
        Me.Search_MyButton1.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom), System.Windows.Forms.AnchorStyles)
        Me.Search_MyButton1.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.Search_MyButton1.DialogResult = System.Windows.Forms.DialogResult.None
        Me.Search_MyButton1.Location = New System.Drawing.Point(635, 29)
        Me.Search_MyButton1.Name = "Search_MyButton1"
        Me.Search_MyButton1.Size = New System.Drawing.Size(69, 20)
        Me.Search_MyButton1.TabIndex = 131
        Me.Search_MyButton1.Text = "查询"
        '
        'MyGrid1
        '
        Me.MyGrid1.CanCustomCol = False
        Me.MyGrid1.Col = 0
        Me.MyGrid1.ColumnFooters = False
        Me.MyGrid1.DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveRight
        Me.MyGrid1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.MyGrid1.FetchRowStyles = False
        Me.MyGrid1.Location = New System.Drawing.Point(0, 58)
        Me.MyGrid1.Margin = New System.Windows.Forms.Padding(0)
        Me.MyGrid1.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.MyGrid1.Name = "MyGrid1"
        Me.MyGrid1.Size = New System.Drawing.Size(902, 385)
        Me.MyGrid1.TabAction = C1.Win.C1TrueDBGrid.TabActionEnum.ControlNavigation
        Me.MyGrid1.TabIndex = 1
        Me.MyGrid1.Xmlpath = Nothing
        '
        'Materials_Buy_Search
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(902, 443)
        Me.Controls.Add(Me.MyGrid1)
        Me.Controls.Add(Me.TableLayoutPanel1)
        Me.Name = "Materials_Buy_Search"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "速查"
        Me.TableLayoutPanel1.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents TableLayoutPanel1 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents M_Buy_Code As CustomControl.MyTextBox
    Friend WithEvents MaterialsSup As CustomControl.MyDtComobo
    Friend WithEvents MaterialsWh As CustomControl.MyDtComobo
    Friend WithEvents Search_Date As CustomControl.MyDateEdit
    Friend WithEvents Jsr_MyDtComobo1 As CustomControl.MyDtComobo
    Friend WithEvents WCZT_MySingleComobo1 As CustomControl.MySingleComobo
    Friend WithEvents Date_MySingleComobo1 As CustomControl.MySingleComobo
    Friend WithEvents CxZt_MySingleComobo1 As CustomControl.MySingleComobo
    Friend WithEvents Search_MyButton1 As CustomControl.MyButton
    Friend WithEvents MyGrid1 As CustomControl.MyGrid
End Class
