﻿Imports System.Data.SqlClient
Imports HisControl
Imports BaseClass

Public Class Yk_Tj31

#Region "定义变量"

    Dim V_Mx_Code As String = ""                '药品__明细编码
    Dim V_Lr As String = "简称"                 '药品__辅助录入
    Dim My_FindView As New DataView             '药品__字典视图


    Dim V_Yp_Code As String                     '药品编码

    Dim My_DataSet As New DataSet

#End Region

#Region "传参"
    Dim Rform As BaseForm
    Dim Rinsert As Boolean
    'Dim Rdate As Date
    Dim Rrow As DataRow
    Dim RZbtb As DataTable
    Dim Rtdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    'Dim Rlb As C1.Win.C1Input.C1Label
    Dim Rzbadt As SqlDataAdapter
    Dim Rds As DataSet
    Dim Rcode As String
    Dim Rrc As C_RowChange
#End Region

    Public Sub New(ByVal tform As BaseForm, ByVal tinsert As Boolean, ByVal trow As DataRow, ByVal tZbtb As DataTable, ByVal ttdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid, ByVal tzbadt As SqlDataAdapter, ByVal tds As DataSet, ByVal tcode As String, ByRef trc As C_RowChange)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        Rform = tform
        Rinsert = tinsert
        'Rdate = tdate
        Rrow = trow
        RZbtb = tZbtb
        Rtdbgrid = ttdbgrid
        'Rlb = tlb
        Rzbadt = tzbadt
        Rds = tds
        Rcode = tcode
        Rrc = trc
        ' 在 InitializeComponent() 调用之后添加任何初始化。

    End Sub

    Protected Overrides Sub OnKeyDown(ByVal e As System.Windows.Forms.KeyEventArgs)
        MyBase.OnKeyDown(e)
        If e.Control = True And e.KeyCode = Keys.S Then
            Comm1.Select()
            Call Comm_Click(Comm1, Nothing)
        End If
    End Sub

    Private Sub Yk_Tj31_FormClosed(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosedEventArgs) Handles Me.FormClosed
        RemoveHandler Rrc.RowChanged, New BaseClass.RowChangedHandler(AddressOf Data_Show)
        Me.My_DataSet.Dispose()
    End Sub

    Private Sub Yf_Ck31_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        AddHandler Rrc.RowChanged, New BaseClass.RowChangedHandler(AddressOf Data_Show)
        Call Form_Init()

        If Rinsert = True Then Call Data_Clear() Else Call Data_Show(Rrow)

    End Sub

#Region "窗体__事件"

    Private Sub Form_Init()
        Panel1.Height = 28


        '按扭初始化
        Call P_Comm(Me.Comm1)
        Call P_Comm(Me.Comm2)

        '药品字典

        HisVar.HisVar.Sqldal.QueryDt(My_DataSet, "SELECT  Yp_Jc,Yp_Name,Jx_Name,Mx_Gyzz,Mx_Gg,Mx_Cd,Yk_Cgj,Yk_Xsj,Yp_Yxq,Yf_Lsj1,Mx_CfBl,Yk_Pfj,Xx_Code,Mx_CgDw,Mx_XsDw,Yk_Sl,Yf_Sl1,Yf_Sl2,Yf_Sl3,Yf_Sl4,Yf_Sl5,Yf_Sl6,Yf_Sl7,Yf_Sl8,Yf_Sl9 FROM  V_Ypkc   Order By Yp_Jc", "药品字典", True)
        My_DataSet.Tables("药品字典").PrimaryKey = New DataColumn() {My_DataSet.Tables("药品字典").Columns("Xx_Code")}

        '药品__字典C1Combo实例化
        Dim My_Tb As DataTable = My_DataSet.Tables("药品字典")
        Dim My_Combo As BaseClass.C_Combo2 = New BaseClass.C_Combo2(C1Combo1, My_Tb.DefaultView, "Yp_Name", "Xx_Code", 740)
        With My_Combo
            .Init_TDBCombo()
            .Init_Colum("Yp_Jc", "简称", 0, "左")
            .Init_Colum("Yp_Name", "药品名称", 150, "左")
            .Init_Colum("Jx_Name", "剂型", 60, "左")
            .Init_Colum("Mx_Gyzz", "国药准字", 80, "左")
            .Init_Colum("Mx_Gg", "规格", 80, "左")
            .Init_Colum("Mx_Cd", "生产厂家", 150, "左")
            .Init_Colum("Yp_Yxq", "有效期", 70, "左")
            .Init_Colum("Yk_Sl", "", 0, "中")
            .Init_Colum("Yk_Cgj", "", 0, "中")
            .Init_Colum("Yk_Xsj", "", 0, "中")
            .Init_Colum("Yf_lsj1", "", 0, "中")
            .Init_Colum("Yk_Pfj", "", 0, "中")
            .Init_Colum("Xx_Code", "库存编码", 120, "中")
            .Init_Colum("Mx_Xsdw", "", 0, "中")
            .Init_Colum("Mx_Cfbl", "", 0, "中")
            .Init_Colum("Mx_Cgdw", "", 0, "中")


            .Init_Colum("Yf_Sl1", "", 0, "中")
            .Init_Colum("Yf_Sl2", "", 0, "中")
            .Init_Colum("Yf_Sl3", "", 0, "中")
            .Init_Colum("Yf_Sl4", "", 0, "中")
            .Init_Colum("Yf_Sl5", "", 0, "中")
            .Init_Colum("Yf_Sl6", "", 0, "中")
            .Init_Colum("Yf_Sl7", "", 0, "中")
            .Init_Colum("Yf_Sl8", "", 0, "中")
            .Init_Colum("Yf_Sl9", "", 0, "中")
            .MaxDropDownItems(15)
            .SelectedIndex(-1)
        End With

        '药品字典视图
        My_FindView = My_Tb.DefaultView
        My_FindView.Sort = "Yp_Jc Asc "
        C1Combo1.AutoSelect = False
        C1Combo1.AutoCompletion = False
        C1Combo1.Columns("Yp_Yxq").NumberFormat = "yyyy-MM-dd"
        With Me.C1Numeric1
            .Value = 0
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .CustomFormat = "###,###,##0.0#####"
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .ShowContextMenu = False
            .EmptyAsNull = True
        End With

        With Me.C1Numeric2
            .Value = 0
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .CustomFormat = "###,###,##0.0#####"
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .ShowContextMenu = False
            .EmptyAsNull = True
        End With

        With Me.C1NumericEdit1
            .Value = 0
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .CustomFormat = "###,###,##0.0#####"
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .ShowContextMenu = False
            .EmptyAsNull = True
        End With

        With Me.C1NumericEdit2
            .Value = 0
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .CustomFormat = "###,###,##0.0#####"
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .ShowContextMenu = False
            .EmptyAsNull = True
        End With
        With Me.C1NumericEdit3
            .Value = 0
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .CustomFormat = "###,###,##0.0#####"
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .ShowContextMenu = False
            .EmptyAsNull = True
        End With

        With Me.C1NumericEdit4
            .Value = 0
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .CustomFormat = "###,###,##0.0#####"
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .ShowContextMenu = False
            .EmptyAsNull = True
        End With

        With Me.C1NumericEdit5
            .Value = 0
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .CustomFormat = "###,###,##0.0#####"
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .ShowContextMenu = False
            .EmptyAsNull = True
        End With

        With Me.C1NumericEdit6
            .Value = 0
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .CustomFormat = "###,###,##0.0#####"
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .ShowContextMenu = False
            .EmptyAsNull = True
        End With


        If iniOperate.iniopreate.GetINI("显示零库存", "调价录入", "", HisVar.HisVar.Parapath & "\Config.Ini") & "" = "" Then
            CheckBox1.CheckState = CheckState.Checked
        Else
            CheckBox1.CheckState = iniOperate.iniopreate.GetINI("显示零库存", "调价录入", "", HisVar.HisVar.Parapath & "\Config.Ini")
        End If

        If CheckBox1.Checked = True Then
            My_DataSet.Tables("药品字典").DefaultView.RowFilter = ""
        Else
            My_DataSet.Tables("药品字典").DefaultView.RowFilter = "Yk_Sl<>0"
        End If

    End Sub

#End Region

#Region "其它项目"


    Private Sub C1Numeric_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles C1Numeric1.KeyPress, C1NumericEdit3.KeyPress, C1Numeric2.KeyPress
        If e.KeyChar <> Chr(Keys.Return) Then Exit Sub
        e.Handled = True
        System.Windows.Forms.SendKeys.Send("{Tab}")
    End Sub
#End Region

#Region "控件__动作"

    Private Sub Comm_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.Click, Comm2.Click
        Select Case sender.tag
            Case "保存"

                If C1Combo1.SelectedValue = "" Then
                    Beep()
                    MsgBox("药品编码不能为空,按任意键返回!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    C1Combo1.Select()
                    Exit Sub
                End If

                If Rinsert = True Then      '增加记录
                    Call Save_Add()
                Else                                '编辑记录
                    Call Save_Edit()
                    MsgBox("修改成功！", MsgBoxStyle.Information, "提示")
                    Me.Close()
                End If

            Case "取消"
                C1Combo1.SelectedValue = -1
                C1Combo1.Text = ""
                Rtdbgrid.Focus()
                Me.Close()
        End Select
    End Sub

    Private Sub C1Move_Click(ByVal sender As Object, ByVal e As C1.Win.C1Command.ClickEventArgs) Handles Move2.Click, Move3.Click, Move5.Click
        If sender.text = "新增" Then
            Rinsert = True
            Call Data_Clear()
        Else
            Rinsert = False
            Select Case sender.text
                Case "上移"
                    Rtdbgrid.MovePrevious()
                Case "下移"
                    Rtdbgrid.MoveNext()
            End Select

        End If

    End Sub

#Region "C1Combo1"



    Private Sub C1Combo1_RowChange(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1Combo1.RowChange
        If C1Combo1.Text = "" Then
            Label4.Text = ""
            Label3.Text = ""
            Label1.Text = ""
            Label5.Text = ""
            C1NumericEdit1.Value = 0
            C1NumericEdit2.Value = 0
            C1NumericEdit3.Value = 0
            C1NumericEdit4.Value = 0
            C1NumericEdit5.Value = 0
            C1NumericEdit6.Value = 0
            C1Numeric1.Value = 0
            C1Numeric2.Value = 0
            V_Mx_Code = ""
        End If

        If C1Combo1.WillChangeToValue = "" Then

            Label4.Text = ""
            Label3.Text = ""
            Label1.Text = ""
            Label5.Text = ""
            C1NumericEdit1.Value = 0
            C1NumericEdit2.Value = 0
            C1NumericEdit3.Value = 0
            C1NumericEdit4.Value = 0
            C1NumericEdit5.Value = 0
            C1NumericEdit6.Value = 0
            C1Numeric1.Value = 0
            C1Numeric2.Value = 0
            V_Mx_Code = ""
        Else
            V_Mx_Code = C1Combo1.Columns("Xx_Code").Value & ""
            Label4.Text = C1Combo1.Columns("Mx_Gyzz").Value & ""
            Label3.Text = C1Combo1.Columns("Mx_Cd").Value & ""
            Label1.Text = C1Combo1.Columns("Mx_Gg").Value & ""
            Label5.Text = C1Combo1.Columns("Jx_Name").Value & ""
            C1NumericEdit1.Value = C1Combo1.Columns("Yf_lsj1").Value & ""
            C1NumericEdit2.Value = C1Combo1.Columns("Yk_Pfj").Value & ""
            C1NumericEdit4.Value = C1Combo1.Columns("Yk_Cgj").Value & ""
        End If
    End Sub

    Private Sub C1Combo1_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles C1Combo1.KeyUp

        If (e.KeyValue > 47 And e.KeyValue < 58) Or (e.KeyValue > 96 And e.KeyValue < 123) Or (e.KeyValue > 64 And e.KeyValue < 91) Or (e.KeyValue = 8) Or (e.KeyValue = 45) Or (e.KeyValue = 46) Then
            Dim s As String = C1Combo1.Text
            If CheckBox1.Checked = True Then
                If C1Combo1.Text = "" Then
                    C1Combo1.DataSource.RowFilter = ""
                Else
                    C1Combo1.DataSource.RowFilter = "Yp_Jc like '*" & C1Combo1.Text & "*'"
                End If
            Else
                If C1Combo1.Text = "" Then
                    C1Combo1.DataSource.RowFilter = "Yk_Sl>0"
                Else
                    C1Combo1.DataSource.RowFilter = "Yp_Jc like '*" & C1Combo1.Text & "*' and Yk_Sl>0"
                End If
            End If

            If (e.KeyValue = 8) Then

                If C1Combo1.Text <> "" Then
                    C1Combo1.DroppedDown = False
                    C1Combo1.DroppedDown = True

                Else
                    C1Combo1.DroppedDown = False
                End If
            End If
            C1Combo1.Text = s
            C1Combo1.SelectionStart = C1Combo1.Text.Length
            C1Combo1.SelectionLength = 0
        End If

    End Sub

    Private Sub C1Combo1_KeyDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles C1Combo1.KeyDown
        If e.KeyValue = 13 Then
            If C1Combo1.WillChangeToIndex < 1 Then
                If (CType(C1Combo1.DataSource, DataView).Count) = 0 Then
                    MsgBox("药品: '" + Me.C1Combo1.Text + "' 不存在！", MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
                    System.Windows.Forms.SendKeys.Send("^{A}")
                    Exit Sub
                End If
                C1Combo1.SelectedIndex = -1
                C1Combo1.SelectedIndex = 0
            Else
                Dim index As Integer
                index = C1Combo1.WillChangeToIndex
                C1Combo1.SelectedIndex = -1
                C1Combo1.SelectedIndex = index
            End If
            e.Handled = True
            System.Windows.Forms.SendKeys.Send("{Tab}")

        End If
    End Sub

#End Region

#End Region

#Region "数据__编辑"

    Private Sub Data_Clear()
        Rinsert = True
        If Move5.Enabled = True Then Move5.Enabled = False
        V_Mx_Code = ""                                          '药品编码

        C1Combo1.Enabled = True
        C1Combo1.SelectedValue = -1
        C1Combo1.Text = ""                                      '药品明细编码
        C1NumericEdit3.Value = 0
        C1Numeric1.Value = 0                                    '采购数量
        C1Numeric2.Value = 0                                    '采购单价
        C1Combo1.Select()

    End Sub

    Private Sub Data_Show(ByVal tmp_Row As DataRow)
        C1Combo1.DataSource.RowFilter = ""
        Rinsert = False
        If Move5.Enabled = False Then Move5.Enabled = True
        T_Label2.Text = CStr((Rtdbgrid.Row) + 1)
        Rrow = tmp_Row

        With Rrow
            V_Mx_Code = .Item("Xx_Code") & ""
            '药品名称
            C1Combo1.SelectedValue = V_Mx_Code
            C1Numeric1.Value = .Item("Tj_Lsj_New")                   '采购数量
            C1Numeric2.Value = .Item("Tj_Pfj_New") & ""


        End With



    End Sub

    Private Sub Save_Add()
        Dim My_Tb As DataTable = RZbtb
        Dim My_NewRow As DataRow = My_Tb.NewRow
        With My_NewRow
            .BeginEdit()
            .Item("Tj_Code") = Rcode
            .Item("Xx_Code") = V_Mx_Code
            .Item("Tj_Lsj_New") = C1Numeric1.Value
            .Item("Tj_Pfj_New") = C1Numeric2.Value
            .Item("Tj_Lsj_Old") = C1NumericEdit1.Value
            .Item("Tj_Pfj_Old") = C1NumericEdit2.Value
            .Item("Yp_Yxq") = C1Combo1.Columns("Yp_Yxq").Value & ""
            .Item("Mx_Gg") = Trim(Label1.Text & "")
            .Item("Mx_Cd") = Trim(Label3.Text & "")
            .Item("Yp_Name") = Trim(C1Combo1.Text & "")
            .Item("Mx_Cfbl") = C1Combo1.Columns("Mx_Cfbl").Value
            .Item("Tj_Yk_Sl") = C1Combo1.Columns("Yk_Sl").Value
            .Item("Tj_Yf_Sl1") = C1Combo1.Columns("Yf_Sl1").Value
            .Item("Tj_Yf_Sl2") = C1Combo1.Columns("Yf_Sl2").Value
            .Item("Tj_Yf_Sl3") = C1Combo1.Columns("Yf_Sl3").Value
            .Item("Tj_Yf_Sl4") = C1Combo1.Columns("Yf_Sl4").Value
            .Item("Tj_Yf_Sl5") = C1Combo1.Columns("Yf_Sl5").Value
            .Item("Tj_Yf_Sl6") = C1Combo1.Columns("Yf_Sl6").Value
            .Item("Tj_Yf_Sl7") = C1Combo1.Columns("Yf_Sl7").Value
            .Item("Tj_Yf_Sl8") = C1Combo1.Columns("Yf_Sl8").Value
            .Item("Tj_Yf_Sl9") = C1Combo1.Columns("Yf_Sl9").Value
            .EndEdit()
        End With

        '数据保存

        My_Tb.Rows.Add(My_NewRow)

        Rtdbgrid.MoveLast()
        With Rzbadt.InsertCommand
            Try
                .Parameters(0).Value = HisVar.HisVar.WsyCode
                .Parameters(1).Value = Rcode
                .Parameters(2).Value = My_NewRow.Item("Xx_Code")
                .Parameters(3).Value = My_NewRow.Item("Tj_Lsj_Old")
                .Parameters(4).Value = My_NewRow.Item("Tj_Pfj_Old")
                .Parameters(5).Value = My_NewRow.Item("Tj_Lsj_New")
                .Parameters(6).Value = My_NewRow.Item("Tj_Pfj_New")
                .Parameters(7).Value = My_NewRow.Item("Tj_Yk_Sl")
                .Parameters(8).Value = My_NewRow.Item("Tj_Yf_Sl1")
                .Parameters(9).Value = My_NewRow.Item("Tj_Yf_Sl2")
                .Parameters(10).Value = My_NewRow.Item("Tj_Yf_Sl3")
                .Parameters(11).Value = My_NewRow.Item("Tj_Yf_Sl4")
                .Parameters(12).Value = My_NewRow.Item("Tj_Yf_Sl5")
                .Parameters(13).Value = My_NewRow.Item("Tj_Yf_Sl6")
                .Parameters(14).Value = My_NewRow.Item("Tj_Yf_Sl7")
                .Parameters(15).Value = My_NewRow.Item("Tj_Yf_Sl8")
                .Parameters(16).Value = My_NewRow.Item("Tj_Yf_Sl9")
                Call P_Conn(True)
                .ExecuteNonQuery()
                Call P_Conn(False)
                My_NewRow.AcceptChanges()
            Catch ex As Exception
                Beep()
                MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            End Try
        End With

        Call Data_Clear()                                   '清空记录

    End Sub

    Private Sub Save_Edit()

        With Rrow
            Try
                .BeginEdit()
                .Item("Tj_Code") = Rcode
                .Item("Xx_Code") = V_Mx_Code
                .Item("Tj_Lsj_New") = C1Numeric1.Value
                .Item("Tj_Pfj_New") = C1Numeric2.Value
                .Item("Tj_Lsj_Old") = C1NumericEdit1.Value
                .Item("Tj_Pfj_Old") = C1NumericEdit2.Value
                .Item("Yp_Yxq") = C1Combo1.Columns("Yp_Yxq").Value & ""
                .Item("Mx_Gg") = Trim(Label1.Text & "")
                .Item("Mx_Cd") = Trim(Label3.Text & "")
                .Item("Yp_Name") = Trim(C1Combo1.Text & "")
                .Item("Mx_Cfbl") = C1Combo1.Columns("Mx_Cfbl").Value
                .Item("Tj_Yk_Sl") = C1Combo1.Columns("Yk_Sl").Value
                .Item("Tj_Yf_Sl1") = C1Combo1.Columns("Yf_Sl1").Value
                .Item("Tj_Yf_Sl2") = C1Combo1.Columns("Yf_Sl2").Value
                .Item("Tj_Yf_Sl3") = C1Combo1.Columns("Yf_Sl3").Value
                .Item("Tj_Yf_Sl4") = C1Combo1.Columns("Yf_Sl4").Value
                .Item("Tj_Yf_Sl5") = C1Combo1.Columns("Yf_Sl5").Value
                .Item("Tj_Yf_Sl6") = C1Combo1.Columns("Yf_Sl6").Value
                .Item("Tj_Yf_Sl7") = C1Combo1.Columns("Yf_Sl7").Value
                .Item("Tj_Yf_Sl8") = C1Combo1.Columns("Yf_Sl8").Value
                .Item("Tj_Yf_Sl9") = C1Combo1.Columns("Yf_Sl9").Value
                .EndEdit()
            Catch ex As Exception
                Beep()
                MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            End Try
        End With

        With Rzbadt.UpdateCommand
            .Parameters(0).Value = Rrow.Item("Tj_Code")
            .Parameters(1).Value = Rrow.Item("Xx_Code")
            .Parameters(2).Value = Rrow.Item("Tj_Lsj_Old")
            .Parameters(3).Value = Rrow.Item("Tj_Pfj_Old")
            .Parameters(4).Value = Rrow.Item("Tj_Lsj_New")
            .Parameters(5).Value = Rrow.Item("Tj_Pfj_New")
            .Parameters(6).Value = Rrow.Item("Tj_Yk_Sl")
            .Parameters(7).Value = Rrow.Item("Tj_Yf_Sl1")
            .Parameters(8).Value = Rrow.Item("Tj_Yf_Sl2")
            .Parameters(9).Value = Rrow.Item("Tj_Yf_Sl3")
            .Parameters(10).Value = Rrow.Item("Tj_Yf_Sl4")
            .Parameters(11).Value = Rrow.Item("Tj_Yf_Sl5")
            .Parameters(12).Value = Rrow.Item("Tj_Yf_Sl6")
            .Parameters(13).Value = Rrow.Item("Tj_Yf_Sl7")
            .Parameters(14).Value = Rrow.Item("Tj_Yf_Sl8")
            .Parameters(15).Value = Rrow.Item("Tj_Yf_Sl9")
            .Parameters(16).Value = Rrow.Item("Tj_Id", DataRowVersion.Original)
            Try
                Call P_Conn(True)
                .ExecuteNonQuery()
                Call P_Conn(False)
                Rrow.AcceptChanges()
            Catch ex As Exception
                Beep()
                MsgBox(ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
            End Try
        End With

    End Sub

#End Region

#Region "鼠标__外观"

    Private Sub P_Comm(ByVal Bt As Button)

        With Bt
            Select Case .Tag
                Case "保存"
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")

                Case "取消"
                    .Left = Me.Comm1.Left + Me.Comm1.Width + 1
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
                    .Width = Me.Comm1.Width
            End Select

            .Text = ""
            .FlatStyle = FlatStyle.Flat
            .FlatAppearance.BorderSize = 0
            .BackgroundImageLayout = ImageLayout.Center
        End With

    End Sub

    Private Sub Comm_MouseEnter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.MouseEnter, Comm2.MouseEnter
        Select Case sender.tag
            Case "保存"
                Me.Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存2")
                Me.Comm1.Cursor = Cursors.Hand

            Case "取消"
                Me.Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消2")
                Me.Comm2.Cursor = Cursors.Hand

        End Select

    End Sub

    Private Sub Comm_MouseLeave(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.MouseLeave, Comm2.MouseLeave
        Select Case sender.tag

            Case "保存"
                Me.Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")
                Me.Comm1.Cursor = Cursors.Default

            Case "取消"
                Me.Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
                Me.Comm2.Cursor = Cursors.Default

        End Select

    End Sub

    Private Sub Comm_MouseDown(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm1.MouseDown, Comm2.MouseDown
        Select Case sender.tag
            Case "保存"
                Me.Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存3")

            Case "取消"
                Me.Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消3")
        End Select
    End Sub

    Private Sub Comm_MouseUp(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm1.MouseUp, Comm2.MouseUp
        Select Case sender.tag
            Case "保存"
                Me.Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存2")

            Case "取消"
                Me.Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消2")
        End Select
    End Sub


#End Region


    Private Sub C1Numeric1_Validated(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Numeric1.Validated
        If C1Numeric1.Text = "" Then Exit Sub
        C1NumericEdit3.Value = C1Numeric1.Value * C1Combo1.Columns("Mx_Cfbl").Value
    End Sub

#Region "输入法设置"
    '中文
    'Private Sub C1TextBox1_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1TextBox7.GotFocus
    '    InputLanguage.CurrentInputLanguage = HisVar.HisVar.InputCode
    'End Sub
    '英文
    Private Sub C1DateEdit1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Numeric1.GotFocus, C1Numeric2.GotFocus, C1NumericEdit3.GotFocus, C1Combo1.GotFocus
        InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages(0)
    End Sub
#End Region

    Private Sub C1Numeric6_ValueChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1NumericEdit6.ValueChanged, C1NumericEdit5.ValueChanged

        If C1NumericEdit4.Text = "" Then Exit Sub
        Select Case sender.tag

            Case "售价比例"
                If C1NumericEdit6.Text <> "" And C1NumericEdit6.Value <> 0 Then

                    C1Numeric1.Value = C1NumericEdit4.Value * C1NumericEdit6.Value / C1Combo1.Columns("Mx_Cfbl").Value
                    C1NumericEdit3.Value = C1Numeric1.Value * C1Combo1.Columns("Mx_Cfbl").Value
                Else
                End If
            Case "批发价比例"
                If C1NumericEdit5.Text <> "" Then
                    C1Numeric2.Value = C1NumericEdit4.Value * C1NumericEdit5.Value
                Else
                End If
        End Select

    End Sub

    Private Sub CheckBox1_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox1.CheckedChanged
        Dim V_Str As String = ""

        If Trim(C1Combo1.Text & "") = "" Then
            V_Str = ""
        Else
            V_Str = " And Yp_Jc Like '*" & C1Combo1.Text.Replace("*", "[*]").Replace("%", "[%]") & "*'"
        End If

        If CheckBox1.Checked = True Then
            V_Str = "1=1" & V_Str
        Else
            V_Str = "Yk_Sl>0" & V_Str
        End If

        My_DataSet.Tables("药品字典").DefaultView.RowFilter = V_Str

        iniOperate.iniopreate.WriteINI("显示零库存", "调价录入", CheckBox1.CheckState, HisVar.HisVar.Parapath & "\Config.Ini")
    End Sub
End Class