﻿Imports BaseClass
Imports System.Drawing
Imports System.Windows.Forms
Public Class MaterialsDict12

#Region "变量定义"
    Dim JCSpell As New BaseClass.Chs2Spell
    Dim BllMtClassDict2 As New BLLOld.B_Materials_Class_Dict
    Dim BllMtDict12 As New BLLOld.B_Materials_Dict
    Dim ModelMtClassDict2 As New ModelOld.M_Materials_Class_Dict

#End Region

#Region "传参"

    Dim Rinsert As Boolean
    Dim RtreeView As TreeView
#End Region

    Public Sub New(ByVal tinsert As Boolean, ByRef ttreeview As TreeView)
        InitializeComponent()
        Rinsert = tinsert
        RtreeView = ttreeview
    End Sub

    Private Sub MaterialsInOutClassDict12_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Call Form_Init()
        If Rinsert = True Then Call Data_Clear() Else Call Data_Show(RtreeView.SelectedNode.Tag)
    End Sub


#Region "窗体__事件"

    Private Sub Form_Init()
        Panel1.Height = 29
        MC_Code_Tb.Enabled = False
        MC_Py_Tb.Enabled = False
        MC_Father_Tb.Enabled = False
        Button1.Top = 1
        Button2.Location = New Point(Button1.Left + Button1.Width + 2, Button1.Top)
    End Sub

#End Region

#Region "数据__操作"

    Private Sub Data_Clear()
        Rinsert = True
        MC_Code_Tb.Text = BllMtClassDict2.MaxCode
        MC_Py_Tb.Text = ""
        MC_Name_Tb.Text = ""
        MC_HaveChild_Cb.Checked = False
        MC_Father_Tb.Text = Trim(RtreeView.SelectedNode.Tag)
        Me.MC_Name_Tb.Select()
    End Sub

    Private Sub Data_Show(ByVal ModelCode As String)
        ModelMtClassDict2 = BllMtClassDict2.GetModel(ModelCode)
        If ModelMtClassDict2.HaveChild = True Then
            Dim CanKilChild As Integer = BllMtClassDict2.GetRecordCount("Class_Father = '" & ModelCode & "'")
            If CanKilChild <> 0 Then
                MC_HaveChild_Cb.Enabled = False
            End If
        ElseIf ModelMtClassDict2.HaveChild = False Then
            Dim CanKilChild As Integer = BllMtDict12.GetRecordCount("Class_Code = '" & ModelCode & "'")
            If CanKilChild <> 0 Then
                MC_HaveChild_Cb.Enabled = False
            End If
        End If
        With ModelMtClassDict2
            MC_Code_Tb.Text = .Class_Code & ""
            MC_Py_Tb.Text = .Class_Py & ""
            MC_Name_Tb.Text = .Class_Name & ""
            MC_Father_Tb.Text = .Class_Father & ""
            If .HaveChild = True Then
                MC_HaveChild_Cb.Checked = True
            Else
                MC_HaveChild_Cb.Checked = False
            End If
        End With
        Me.MC_Name_Tb.Select()
    End Sub

#End Region

#Region "控件__动作"

    Private Sub Comm_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click, Button2.Click
        Select Case sender.tag
            Case "保存"
                If Rinsert = True Then     '增加记录
                    Call Data_Add()
                Else                       '编辑记录
                    Call Data_Edit()
                End If
            Case "取消"
                Me.Close()
        End Select
    End Sub

    Private Sub TextBox_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs)
        If e.KeyChar <> Chr(Keys.Return) Then Exit Sub
        e.Handled = True
        System.Windows.Forms.SendKeys.Send("{Tab}")
    End Sub

    Private Sub DevNameTb_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles MC_Name_Tb.Validated
        MC_Py_Tb.Text = JCSpell.GetPy(Me.MC_Name_Tb.Text & "")
    End Sub

#End Region

#Region "数据__编辑"

    Private Sub Data_Add()
        Try
            With ModelMtClassDict2
                .Class_Code = BllMtClassDict2.MaxCode
                .Class_Name = Trim(MC_Name_Tb.Text & "")
                .Class_Py = Trim(MC_Py_Tb.Text & "")
                .HaveChild = MC_HaveChild_Cb.Checked
                .Class_Father = Trim(RtreeView.SelectedNode.Tag)
            End With
            BllMtClassDict2.Add(ModelMtClassDict2)
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            Exit Sub
        Finally
            MC_Name_Tb.Select()
        End Try

        Dim imageFlag As Boolean
        If MC_HaveChild_Cb.Checked = True Then
            imageFlag = True
        Else
            imageFlag = False
        End If
        RtreeView.SelectedNode.Text = Mid(RtreeView.SelectedNode.Text, 1, InStrRev(RtreeView.SelectedNode.Text, "(")) & BllMtClassDict2.GetRecordCount("Class_Father='" & Trim(MC_Father_Tb.Text) & "'") & ")"
        Call Tree_Edit(MC_Code_Tb.Text, MC_Name_Tb.Text & "(0)", imageFlag)
        Call Data_Clear()
    End Sub

    Private Sub Data_Edit()
        Try
            With ModelMtClassDict2
                .Class_Code = Trim(MC_Code_Tb.Text & "")
                .Class_Name = Trim(MC_Name_Tb.Text & "")
                .Class_Py = Trim(MC_Py_Tb.Text & "")
                .HaveChild = MC_HaveChild_Cb.Checked
                .Class_Father = Trim(MC_Father_Tb.Text & "")
            End With
            BllMtClassDict2.Update(ModelMtClassDict2)
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
        Finally
            MC_Name_Tb.Select()
        End Try

        Dim imageFlag As Boolean
        Dim tmpText As Integer
        If MC_HaveChild_Cb.Checked = True Then
            imageFlag = True
            tmpText = BllMtClassDict2.GetRecordCount("Class_Father='" & Trim(MC_Code_Tb.Text) & "'")
        Else
            imageFlag = False
            tmpText = BllMtDict12.GetRecordCount("Class_Code='" & Trim(MC_Code_Tb.Text) & "'")
        End If

        Call Tree_Edit(MC_Code_Tb.Text, MC_Name_Tb.Text & "(" & tmpText & ")", imageFlag)
    End Sub

#End Region

#Region "自定义函数"

    Private Sub Tree_Edit(ByVal V_Key As String, ByVal V_Text As String, ByVal V_ImageFlag As Boolean)
        Dim My_Node As New TreeNode
        If Rinsert = True Then
            My_Node.Tag = MC_Code_Tb.Text & ""
            My_Node.Text = V_Text
            If V_ImageFlag = True Then
                My_Node.ImageIndex = 1
                My_Node.SelectedImageIndex = 2
            Else
                My_Node.ImageIndex = 3
                My_Node.SelectedImageIndex = 4
            End If
            RtreeView.SelectedNode.Nodes.Add(My_Node)
            If RtreeView.SelectedNode.IsExpanded = False Then
                RtreeView.SelectedNode.Expand()
            End If
        Else
            RtreeView.SelectedNode.Tag = MC_Code_Tb.Text & ""
            RtreeView.SelectedNode.Text = V_Text
            If V_ImageFlag = True Then
                RtreeView.SelectedNode.ImageIndex = 1
                RtreeView.SelectedNode.SelectedImageIndex = 2
            Else
                RtreeView.SelectedNode.ImageIndex = 3
                RtreeView.SelectedNode.SelectedImageIndex = 4
            End If
        End If
    End Sub

#End Region

#Region "输入法设置"
    '英文
    Private Sub C1TextBox1_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs)
        InputLanguage.CurrentInputLanguage = HisVar.HisVar.InputCode
    End Sub

    '中文
    Private Sub C1TextBox2_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MC_Name_Tb.GotFocus
        InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages(0)
    End Sub
#End Region

End Class