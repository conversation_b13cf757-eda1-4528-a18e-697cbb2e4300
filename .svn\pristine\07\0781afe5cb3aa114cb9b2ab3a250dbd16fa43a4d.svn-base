﻿/**  版本信息模板在安装目录下，可自行修改。
* M_Emr_BasicElementTree.cs
*
* 功 能： N/A
* 类 名： M_Emr_BasicElementTree
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/8/16 星期二 下午 1:38:15   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace ModelOld
{
	/// <summary>
	/// M_Emr_BasicElementTree:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class M_Emr_BasicElementTree
	{
		public M_Emr_BasicElementTree()
		{}
		#region Model
		private string _ele_code;
		private string _ele_name;
		private bool _isdir;
		private string _eletype;
		private bool _ismultiselect;
		private string _datafield;
		private string _father_code;
		/// <summary>
		/// 
		/// </summary>
		public string Ele_Code
		{
			set{ _ele_code=value;}
			get{return _ele_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Ele_Name
		{
			set{ _ele_name=value;}
			get{return _ele_name;}
		}
		/// <summary>
		/// 是否是目录
		/// </summary>
		public bool isDir
		{
			set{ _isdir=value;}
			get{return _isdir;}
		}
		/// <summary>
		/// -1：目录；1：选择项；2：输入框；3：数据字段
		/// </summary>
		public string EleType
		{
			set{ _eletype=value;}
			get{return _eletype;}
		}
		/// <summary>
		/// 选择框是否多选
		/// </summary>
		public bool isMultiSelect
		{
			set{ _ismultiselect=value;}
			get{return _ismultiselect;}
		}
		/// <summary>
		/// 数据字段
		/// </summary>
		public string DataField
		{
			set{ _datafield=value;}
			get{return _datafield;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Father_Code
		{
			set{ _father_code=value;}
			get{return _father_code;}
		}
		#endregion Model

	}
}

