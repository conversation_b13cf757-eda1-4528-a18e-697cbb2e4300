﻿Imports System.Text
Imports System.Reflection
Public Class TemparatureChart
#Region "参数"
    Private bllTw1 As New BLLOld.B_Bl_TWD1
    Private bllTw2 As New BLLOld.B_Bl_TWD2

    Private vBl, vStartTime, vStopTime As String
#End Region

    Public Sub New(ByVal bl As String, ByVal startTime As String, ByVal stopTime As String)

        ' 此调用是设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        vBl = bl
        vStartTime = startTime
        vStopTime = stopTime
        CreateChart()
    End Sub
    Private Sub TemparatureChart_Load(sender As Object, e As System.EventArgs) Handles Me.Load
        Me.StartPosition = FormStartPosition.CenterScreen

    End Sub


    Private Sub CreateChart()

        Dim dtBrxx As DataTable = bllTw1.GetListOfPersonalInformation(vBl, vStopTime).Tables(0)
        dtBrxx.TableName = "BRXX"

        Dim sb As New StringBuilder()
        sb.Append("<ROOT>")
        sb.Append("	<BRXX> <!--病人信息 --> ")
        sb.Append("		<NAME>" & dtBrxx.Rows(0).Item("Ry_Name") & "</NAME> 	 <!--病人姓名 --> ")
        sb.Append("		<AGE>" & dtBrxx.Rows(0).Item("AGE") & "岁</AGE>          	 <!--病人年龄 --> ")
        sb.Append("		<SEX>" & dtBrxx.Rows(0).Item("Ry_Sex") & "</SEX>			<!--病人性别--> ")
        sb.Append("		<DEPARTMENT>" & dtBrxx.Rows(0).Item("Ks_Name") & "</DEPARTMENT>		<!--病人科别 --> ")
        sb.Append("		<INPATIENTAREA>" & dtBrxx.Rows(0).Item("Bq_Name") & "</INPATIENTAREA>		<!--病人病区 --> ")
        sb.Append("		<BEDNO>" & dtBrxx.Rows(0).Item("Bc_Name") & "</BEDNO>		<!--病人床号 --> ")
        sb.Append("		<HOSPITALIZATIONNUMBER>" & dtBrxx.Rows(0).Item("Bl_Code").ToString().Substring(4) & "</HOSPITALIZATIONNUMBER>		<!--病人住院号(病案号) --> ")
        sb.Append("		<RYRQ>" & CDate(dtBrxx.Rows(0).Item("Ry_RyDate")).ToString("yyyy-MM-dd") & "</RYRQ>		<!--病人入院日期 --> ")
        If dtBrxx.Rows(0).Item("operationTime") & "" = "" Then
            sb.Append("		<SSRQ>" & Now.ToString("yyyy-MM-dd") & "</SSRQ>		<!--病人手术日期--> ")
        Else
            sb.Append("		<SSRQ>" & CDate(dtBrxx.Rows(0).Item("operationTime")).ToString("yyyy-MM-dd") & "</SSRQ>		<!--病人手术日期--> ")
        End If
        sb.Append("	</BRXX> ")




        Dim dtHljl As DataTable = bllTw1.GetList("Bl_Code='" & vBl & "' AND CL_Date  BETWEEN '" & vStartTime & "' and '" & vStopTime & "'").Tables(0)
        dtHljl.TableName = "HLJL"
        Dim strCl_Code As String = ""
        sb.Append("	<HLJLDATA> <!--病人护理相关数据--> ")
        For Each tRow In dtHljl.Rows
            strCl_Code += "'" & tRow("Cl_Code") & "',"
            sb.Append("		<HLJL> ")
            sb.Append("			<RQ>" & CDate(tRow("CL_Date")).ToString("yyyy-MM-dd") & "</RQ> 		<!--护理日期 --> ")
            sb.Append("			<XY>" & tRow("XY1") & "</XY>		<!--床血压--> ")
            sb.Append("			<RL>" & tRow("ZRL") & "</RL>		<!--入量--> ")
            sb.Append("			<CL>" & tRow("ZCL") & "</CL>		<!--出量 --> ")
            sb.Append("			<NL>" & tRow("XBL") & "</NL>		<!--尿量 --> ")
            sb.Append("			<DBCS>" & tRow("DBCS") & "</DBCS>	<!--大便次数 --> ")
            sb.Append("			<TZ>" & tRow("Height") & "</TZ>		<!--体重--> ")
            sb.Append("			<SG>" & tRow("weight") & "</SG>		<!--身高 --> ")
            If (tRow("PSJG") & "").Trim() = "" Then
                sb.Append("			<PSXX>" & tRow("GMYW1") & "</PSXX>	<!--皮试信息 --> ")
            Else
                sb.Append("			<PSXX>" & tRow("GMYW1") & "(" & tRow("PSJG") & ")" & "</PSXX>	<!--皮试信息 --> ")
            End If
            sb.Append("			<PSXX>" & tRow("GMYW1") & "(" & tRow("PSJG") & ")" & "</PSXX>	<!--皮试信息 --> ")
            sb.Append("			<PSJG>" & tRow("PSJG") & "</PSJG>		<!--皮试结果(阴性/阳性)--> ")
            sb.Append("		</HLJL> ")
        Next
        sb.Append("	</HLJLDATA> ")


        Dim dtTwjl As DataTable = bllTw2.GetListForChart("Bl_TWD2.Cl_Code in (" & Strings.Left(strCl_Code, Len(strCl_Code) - 1) & ")").Tables(0)
        dtTwjl.TableName = "TWJL"
        sb.Append("	<TWJLDATA> <!--病人体温相关数据 --> ")
        For Each dtRow In dtTwjl.Rows
            sb.Append("		<TWJL> ")
            sb.Append("			<RQ>" & CDate(dtRow("CL_Date")).ToString("yyyy-MM-dd") & "</RQ>		<!--体温记录日期 --> ")
            sb.Append("			<SJ>" & dtRow("Cl_Time").ToString & "</SJ>		<!--体温记录日期 --> ")
            sb.Append("			<TW>" & dtRow("Temperature").ToString & "</TW>		<!--体温 --> ")
            sb.Append("			<TWBW>" & dtRow("TWBW").ToString & "</TWBW>		<!--体温部分(取值范围：口表、耳表、腋表、肛表) --> ")
            sb.Append("			<MB>" & dtRow("Pulse") & "" & "</MB>		<!--脉搏--> ")
            sb.Append("			<HX>" & dtRow("Breath") & "" & "</HX>		<!--呼吸 --> ")
            sb.Append("			<HXJ>" & IIf(dtRow("isHXJ") = True, 1, 0) & "</HXJ>		<!--是否使用呼吸机 (0：否 1：是) --> ")
            sb.Append("			<XL>" & dtRow("HeartRate") & "" & "</XL>		<!--心率--> ")
            sb.Append("			<XZQBQ>" & IIf(dtRow("isXZQBQ") = True, 1, 0) & "</XZQBQ>		<!--是否启用心脏起搏器(0：否 1：是) --> ")
            sb.Append("			<WLJW>" & dtRow("WLJW").ToString & "</WLJW>		<!--物理降温--> ")
            sb.Append("			<JCWC>" & dtRow("SpecialEvent").ToString & "</JCWC>		<!--拒测外出(取值范围：拒测、外出) --> ")
            sb.Append("			<EVENT>" & dtRow("EVENT").ToString & "</EVENT>	<!--事件--> ")
            If dtRow("EVENTTIME") & "" = "" Then
                sb.Append(" 	<EVENTTIME></EVENTTIME>		<!--事件时间(时间格式(时：分) --> ")
            Else
                sb.Append(" 	<EVENTTIME>" & CDate(dtRow("EVENTTIME")).ToString("HH:mm") & "</EVENTTIME>		<!--事件时间(时间格式(时：分) --> ")
            End If


            sb.Append("			<SFFH>" & IIf(dtRow("isFH") = True, 1, 0) & "</SFFH>		<!--是否审核(0：否 1：是) --> ")

            sb.Append("		</TWJL>		 ")
        Next
        sb.Append("	</TWJLDATA> ")
        sb.Append("</ROOT>")

        Dim xmlData As String = sb.ToString()

        Dim ass As Assembly = Assembly.LoadFrom(Application.StartupPath & "\TemperatureChart.dll")
        For Each t As Type In ass.GetTypes
            If t.FullName = "TemperatureChart.TemperatureUC" Then
                Dim ob As Object = Activator.CreateInstance(t)

                Dim MainTitle As PropertyInfo = t.GetProperty("MainTitle")
                MainTitle.SetValue(ob, "", Nothing)
                Dim SecondTitle As PropertyInfo = t.GetProperty("SecondTitle")
                SecondTitle.SetValue(ob, ZTHisVar.Var.HosName + "体温单", Nothing)
                Dim TimeStart As PropertyInfo = t.GetProperty("TimeStart")
                TimeStart.SetValue(ob, 2, Nothing)
                Dim XData As PropertyInfo = t.GetProperty("XmlData")
                XData.SetValue(ob, xmlData, Nothing)
                ' Dim s As String = "<ROOT> 	<BRXX> <!--病人信息 --> 		<NAME>李四</NAME> 	 <!--病人姓名 --> 		<AGE>29</AGE>          	 <!--病人年龄 --> 		<SEX>男</SEX>			<!--病人性别--> 		<DEPARTMENT>外科</DEPARTMENT>		<!--病人科别 --> 		<INPATIENTAREA>外科一区</INPATIENTAREA>		<!--病人病区 --> 		<BEDNO>22</BEDNO>		<!--病人床号 --> 		<HOSPITALIZATIONNUMBER>20150909</HOSPITALIZATIONNUMBER>		<!--病人住院号(病案号) --> 		<RYRQ>2015-07-01</RYRQ>		<!--病人入院日期 --> 		<SSRQ>2015-07-07</SSRQ>		<!--病人手术日期--> 	</BRXX> 	<HLJLDATA> <!--病人护理相关数据--> 		<HLJL> 			<RQ>2015-07-08</RQ> 		<!--护理日期 --> 			<XY>66/99</XY>		<!--床血压--> 			<RL>66</RL>		<!--入量--> 			<CL>98</CL>		<!--出量 --> 			<NL>67</NL>		<!--尿量 --> 			<DBCS>☆</DBCS>	<!--大便次数 --> 			<TZ>66</TZ>		<!--体重--> 			<SG>178</SG>		<!--身高 --> 			<PSXX>青霉素</PSXX>	<!--皮试信息 --> 			<PSJG>阳性</PSJG>		<!--皮试结果(阴性/阳性)--> 		</HLJL> 	</HLJLDATA> 	<TWJLDATA> <!--病人体温相关数据 --> 		<TWJL> 			<RQ>2015-07-08</RQ>		<!--体温记录日期 --> 			<SJ>10:00</SJ>		<!--体温记录日期 --> 			<TW>36.8</TW>		<!--体温 --> 			<TWBW>肛表</TWBW>		<!--体温部分(取值范围：口表、耳表、腋表、肛表) --> 			<MB>68</MB>		<!--脉搏--> 			<HX>40</HX>		<!--呼吸 --> 			<HXJ>0</HXJ>		<!--是否使用呼吸机 (0：否 1：是) --> 			<XL>100</XL>		<!--心率--> 			<XZQBQ>1</XZQBQ>		<!--是否启用心脏起搏器(0：否 1：是) --> 			<WLJW>36.8</WLJW>		<!--物理降温--> 			<JCWC></JCWC>		<!--拒测外出(取值范围：拒测、外出) --> 			<EVENT>转入</EVENT>	<!--事件--> 			<EVENTTIME>23:19</EVENTTIME>		<!--事件时间(时间格式(时：分) --> 			<SFFH>1</SFFH>		<!--是否审核(0：否 1：是) --> 		</TWJL>		 	</TWJLDATA> </ROOT> "
                Dim dock As PropertyInfo = t.GetProperty("Dock")
                dock.SetValue(ob, DockStyle.Fill, Nothing)
                Dim cont As Control = CType(ob, Control)
                Me.Controls.Add(cont)
            End If
        Next



    End Sub

End Class