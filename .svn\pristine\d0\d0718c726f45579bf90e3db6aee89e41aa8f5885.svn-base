﻿Imports C1.Win.C1Input
Imports C1.Win.C1TrueDBGrid
Imports System.Data.SqlClient
Imports BaseClass

Public Class Zd_Yp13_Database

#Region "变量定义"
    Dim My_Button As C_Button                                       '按扭初始化
    Dim V_Name As String                                            '简称是否发生变化
    Dim V_Gx_Code As String                             '客户编码
    Dim V_Yp_Code As String
    Dim My_Cc As New BaseClass.C_Cc()
    Dim My_Tb As DataTable
#End Region

#Region "传参"
    Dim Rinsert As Boolean
    Dim Rrow As DataRow
    Dim RZbtb As DataTable
    Dim Rtdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Dim Rlb As C1.Win.C1Input.C1Label
    Dim Rrc As C_RowChange
    Dim Rtree As TreeView
    Dim R_Count As Integer

    Dim Rds As DataSet
    Dim Rdlcode As String
#End Region

    Public Sub New(ByVal tinsert As Boolean, ByVal trow As DataRow, ByVal tZbtb As DataTable, ByVal ttdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid, ByVal tlb As C1.Win.C1Input.C1Label, ByRef trc As C_RowChange, ByRef ttree As TreeView, ByRef t_Count As Integer, ByVal tds As DataSet, ByVal tdlcode As String)

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        Rinsert = tinsert
        Rrow = trow
        RZbtb = tZbtb
        Rtdbgrid = ttdbgrid
        Rlb = tlb
        Rrc = trc
        Rtree = ttree
        R_Count = t_Count

        Rds = tds
        Rdlcode = tdlcode

        ' 在 InitializeComponent() 调用之后添加任何初始化。
    End Sub


    Private Sub Zd_Yp13_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Call Form_Init()

        Call Combo_Init()
        If Rinsert = True Then
            Call Data_Clear()

        Else
            Call Data_Show(Rrow)
        End If
    End Sub

    Private Sub Form_Init()
        Panel1.Height = 29
       
        'Escape 不接收修改
        C1TextBox2.AcceptsEscape = False
        C1TextBox3.AcceptsEscape = False

        '客户字典
        Dim My_Combo As New BaseClass.C_Combo2(C1Combo1, Rds.Tables("功效").DefaultView, "Gx_Name", "Gx_Code", 386)
        With My_Combo
            .Init_TDBCombo()
            .Init_Colum("Gx_Jc", "简称", 92, "左")
            .Init_Colum("Gx_Name", "功效名称", 200, "左")
            .Init_Colum("Gx_Code", "编码", 0, "左")
            .MaxDropDownItems(12)
            .SelectedIndex(-1)
        End With
        With C1Combo1
            .AutoCompletion = False
            .AutoSelect = False
        End With


        Dim My_Combo1 As New BaseClass.C_Combo1(Me.C1Combo2)
        My_Combo1.Init_TDBCombo()
        With C1Combo2
            .AddItem("国家基本药物")
            .AddItem("省增补基本药物")
            .AddItem("非基本药物")
            .AddItem("其他")
            .SelectedIndex = 0
            .Width = 121
            .DropDownWidth = 121
        End With

        Dim My_Combo4 As New BaseClass.C_Combo1(Me.C1Combo4)
        My_Combo4.Init_TDBCombo()
        With C1Combo4
            .AddItem("非特殊药品")
            .AddItem("麻醉药品")
            .AddItem("一类精神药品")
            .AddItem("二类精神药品")
            .AddItem("医疗用毒性药品")
            .AddItem("疫苗")

            .SelectedIndex = 0
            .Width = 169
            .DropDownWidth = 169
        End With

        '按扭初始化
        My_Button = New C_Button(Comm1, Comm2)
        My_Button.Init_Button(Panel1.Left + Panel1.Width / 2, 4)


    End Sub

    Private Sub Combo_Init()

        My_Tb = HisVar.HisVar.HisDBservice.GetMlYp2(Rdlcode).Tables("药品")
        Dim My_Combo As New BaseClass.C_Combo2(C1Combo3, My_Tb.DefaultView, "Yp_Name", "Yp_Code", 520)

        With My_Combo
            .Init_TDBCombo()
            .Init_Colum("Yp_Jc", "简称", 92, "左")
            .Init_Colum("Yp_Name", "名称", 300, "左")
            .Init_Colum("IsJb", "基本药物", 100, "左")
            .Init_Colum("Yp_Code", "编码", 0, "左")
            .Init_Colum("Yp_Memo", "备注", 0, "左")
            .Init_Colum("Gx_Code", "功效编码", 0, "左")
            .Init_Colum("Dl_Code", "大类编码", 0, "左")
            .Init_Colum("Yy_Yp_Code", "调整编码", 0, "左")
            .Init_Colum("IsUse", "调整编码", 0, "左")
            .Init_Colum("JbId", "调整编码", 0, "左")
            .MaxDropDownItems(12)
            .SelectedIndex(-1)
        End With


        C1Combo3.Splits(0).DisplayColumns(6).OwnerDraw = True

        My_Tb.DefaultView.Sort = "Yp_Name Asc"
        C1Combo3.DropdownPosition = C1.Win.C1List.DropdownPositionEnum.LeftUp
        C1Combo3.AutoCompletion = False


    End Sub


#Region "数据__操作"

    Private Sub Data_Clear()

        C1TextBox1.Enabled = True
        C1Combo3.Enabled = True
        C1TextBox2.Enabled = True
        C1Combo1.Enabled = True
        C1Combo2.Enabled = True

        L_Dl_Code.Text = ""


        C1Combo3.Text = ""
        C1TextBox1.Text = ""
        C1TextBox2.Text = ""
        C1TextBox3.Text = ""

        C1Combo1.SelectedIndex = -1
        C1Combo2.SelectedIndex = -1
        C1Combo3.SelectedIndex = -1

        C1Combo4.Text = "非特殊药品"

        V_Gx_Code = ""
    End Sub

    Private Sub Data_Show(ByVal tmp_Row As DataRow)
        Rrow = tmp_Row
        C1TextBox1.Enabled = False
        C1Combo3.Enabled = False
        C1TextBox2.Enabled = False
        C1Combo1.Enabled = False
        C1Combo2.Enabled = False
        With Rrow
            V_Name = .Item("Yp_Name") & ""
            L_Dl_Code.Text = .Item("Yp_Code") & ""
            V_Gx_Code = .Item("Gx_Code") & ""
            V_Yp_Code = .Item("Yp_Code") & ""
            C1Combo3.Text = .Item("Yp_Name") & ""
            C1TextBox2.Text = .Item("Yp_Jc") & ""
            C1TextBox3.Text = .Item("Yp_Memo") & ""
            C1Combo1.SelectedValue = .Item("Gx_Code")
            C1Combo2.Text = .Item("IsJb") & ""
            C1Combo4.Text = .Item("Special") & ""
        End With
    End Sub


#End Region

#Region "控件__动作"

    Private Sub Comm_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.Click, Comm2.Click
        Select Case sender.tag

            Case "保存"
                If Trim(C1Combo3.Text & "") = "" Then
                    MsgBox("药品名称不能为空,按任意键返回！", MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
                    C1Combo3.Focus()
                    Exit Sub
                End If

                If Rinsert = True Then Call Data_Add() Else Call Data_Edit()

            Case "取消"
                Me.Close()

        End Select
    End Sub

    Private Sub TextBox_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles C1TextBox2.KeyPress, C1TextBox3.KeyPress, C1Combo2.KeyPress
        If e.KeyChar <> Chr(Keys.Return) Then Exit Sub
        e.Handled = True
        System.Windows.Forms.SendKeys.Send("{Tab}")
    End Sub

#End Region

#Region "数据__编辑"

    Private Sub Data_Add()

        Dim My_NewRow As DataRow = RZbtb.NewRow

        Dim V_CenerCode As String = HisVar.HisVar.HisDBservice.UpDate_Yp2(Rdlcode, L_Dl_Code.Text, Trim(C1Combo3.Text) & "", Trim(C1TextBox2.Text) & "", Trim(C1TextBox3.Text) & "", Trim(C1Combo1.SelectedValue & ""), Trim(C1Combo2.Text & ""))

        With My_NewRow
            .Item("Dl_Code") = Rdlcode
            .Item("Yp_Code") = Microsoft.VisualBasic.Right(V_CenerCode, Len(V_CenerCode) - InStr(V_CenerCode, "New") - 2)
            .Item("Yp_Name") = Trim(C1Combo3.Text & "")
            .Item("Yp_Jc") = Trim(C1TextBox2.Text & "")
            If C1Combo1.SelectedValue <> "" Then
                .Item("Gx_Code") = Trim(C1Combo1.SelectedValue & "")
                .Item("Gx_Name") = Trim(C1Combo1.Text & "")
            Else
                .Item("Gx_Code") = DBNull.Value
                .Item("Gx_Name") = ""
            End If
            .Item("Yp_Memo") = Trim(C1TextBox3.Text & "")
            .Item("IsJb") = Trim(C1Combo2.Text & "")
            .Item("Special") = Trim(C1Combo4.Text & "")
        End With
        Call Data_SaveAdd(My_NewRow)
        Call Data_Clear()
        Call Tree_Add()
    End Sub

    Private Sub Tree_Add()
        R_Count = R_Count + 1
        Rtree.TopNode.Text = "药品类别(" + R_Count.ToString + ")"

        Dim V_NodeText As String = Rtree.SelectedNode.Text         '当前选中的节点
        V_NodeText = Mid(V_NodeText, 1, InStr(V_NodeText, "(") - 1)
        Rtree.SelectedNode.Text = V_NodeText + "(" + Rtdbgrid.Splits(0).Rows.Count.ToString + ")"
    End Sub

    Private Sub Data_Edit()
        Dim My_Row As DataRow = Rrow
        Dim V_CenerCode As String = HisVar.HisVar.HisDBservice.UpDate_Yp2(Rdlcode, L_Dl_Code.Text, Trim(C1Combo3.Text) & "", Trim(C1TextBox2.Text) & "", Trim(C1TextBox3.Text) & "", Trim(C1Combo1.SelectedValue & ""), Trim(C1Combo2.Text & ""))
        With My_Row
            .BeginEdit()
            .Item("Yp_Name") = Trim(C1Combo3.Text & "")
            .Item("Yp_Jc") = Trim(C1TextBox2.Text & "")
            .Item("Yp_Memo") = Trim(C1TextBox3.Text & "")
            If C1Combo1.SelectedValue <> "" Then
                .Item("Gx_Code") = Trim(C1Combo1.SelectedValue & "")
                .Item("Gx_Name") = Trim(C1Combo1.Text & "")
            Else
                .Item("Gx_Code") = DBNull.Value
                .Item("Gx_Name") = ""
            End If
            .Item("IsJb") = Trim(C1Combo2.Text & "")
            .Item("Special") = Trim(C1Combo4.Text & "")
            .EndEdit()
        End With
        Call Data_SaveEdit(My_Row)
    End Sub

    Private Sub Data_SaveAdd(ByVal My_Row As DataRow)        '数据保存
        Try
            RZbtb.Rows.Add(My_Row)
            Rtdbgrid.MoveLast()
            Rlb.Text = "∑=" + Rtdbgrid.Splits(0).Rows.Count.ToString
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            C1Combo3.Select()
            Exit Sub
        End Try


        '数据更新
        Try
            Dim Para(7) As SqlClient.SqlParameter
            Para(0) = New SqlParameter("@Dl_Code", SqlDbType.Char)
            Para(1) = New SqlParameter("@Yp_Code", SqlDbType.VarChar)
            Para(2) = New SqlParameter("@Yp_Name", SqlDbType.VarChar)
            Para(3) = New SqlParameter("@Yp_Jc", SqlDbType.VarChar)
            Para(4) = New SqlParameter("@Gx_Code", SqlDbType.Char)
            Para(5) = New SqlParameter("@Yp_Memo", SqlDbType.VarChar)
            Para(6) = New SqlParameter("@IsJb", SqlDbType.VarChar)
            Para(7) = New SqlParameter("@Special", SqlDbType.VarChar)


            Para(0).Value = My_Row.Item("Dl_Code")
            Para(1).Value = My_Row.Item("Yp_Code")
            Para(2).Value = My_Row.Item("Yp_Name")
            Para(3).Value = My_Row.Item("Yp_Jc")
            Para(4).Value = My_Row.Item("Gx_Code")
            Para(5).Value = My_Row.Item("Yp_Memo")
            Para(6).Value = My_Row.Item("IsJb")
            Para(7).Value = My_Row.Item("Special")

            HisVar.HisVar.Sqldal.ExecuteSql("Insert Into Zd_Ml_Yp2(Dl_Code,Yp_Code,Yp_Name,Yp_Jc,Gx_Code,Yp_Memo,IsJb,Special)Values(@Dl_Code,@Yp_Code,@Yp_Name,@Yp_Jc,@Gx_Code,@Yp_Memo,@IsJb,@Special)", Para)

            My_Row.AcceptChanges()
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            C1Combo3.Select()
        Finally

        End Try
    End Sub

    Private Sub Data_SaveEdit(ByVal My_Row As DataRow)        '数据更新


        Try
            Dim Para(7) As SqlClient.SqlParameter
            Para(0) = New SqlParameter("@Dl_Code", SqlDbType.Char)
            Para(1) = New SqlParameter("@Yp_Name", SqlDbType.VarChar)
            Para(2) = New SqlParameter("@Yp_Jc", SqlDbType.VarChar)
            Para(3) = New SqlParameter("@Gx_Code", SqlDbType.Char)
            Para(4) = New SqlParameter("@Yp_Memo", SqlDbType.VarChar)
            Para(5) = New SqlParameter("@IsJb", SqlDbType.VarChar)
            Para(6) = New SqlParameter("@Special", SqlDbType.VarChar)
            Para(7) = New SqlParameter("@Old_Yp_Code", SqlDbType.VarChar)

            Para(0).Value = My_Row.Item("Dl_Code")
            Para(1).Value = My_Row.Item("Yp_Name")
            Para(2).Value = My_Row.Item("Yp_Jc")
            Para(3).Value = My_Row.Item("Gx_Code")
            Para(4).Value = My_Row.Item("Yp_Memo")
            Para(5).Value = My_Row.Item("IsJb")
            Para(6).Value = My_Row.Item("Special")
            Para(7).Value = My_Row.Item("Yp_Code", DataRowVersion.Original)

            HisVar.HisVar.Sqldal.ExecuteSql("Update  Zd_Ml_Yp2 Set Yp_Name=@Yp_Name,Yp_Jc=@Yp_Jc,Gx_Code=@Gx_Code,Yp_Memo=@Yp_Memo,IsJb=@IsJb,Special=@Special Where Yp_Code=@Old_Yp_Code", Para)

            My_Row.AcceptChanges()
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
        Finally
            C1Combo3.Select()
        End Try

    End Sub

#End Region

#Region "自定义按扭"

    Private Sub Comm_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles Comm1.KeyDown, Comm2.KeyDown
        If e.KeyCode = Keys.Enter Or e.KeyCode = Keys.Space Then My_Button.KeyDown(sender.tag)
    End Sub

    Private Sub Comm_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles Comm1.KeyUp, Comm2.KeyUp
        If e.KeyCode = Keys.Enter Or e.KeyCode = Keys.Space Then My_Button.KeyUp(sender.tag)
    End Sub

    Private Sub Comm_MouseEnter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.MouseEnter, Comm2.MouseEnter
        My_Button.MouseEnter(sender.tag)
    End Sub

    Private Sub Comm_MouseLeave(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.MouseLeave, Comm2.MouseLeave
        My_Button.MouseLeave(sender.tag)
    End Sub

    Private Sub Comm_MouseDown(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm1.MouseDown, Comm2.MouseDown
        My_Button.MouseDown(sender.tag)
    End Sub

    Private Sub Comm_MouseUp(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm1.MouseUp, Comm2.MouseUp
        My_Button.MouseUp(sender.tag)
    End Sub

#End Region

#Region "Combo1动作"

    Private Sub C1Combo1_GotFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1Combo1.GotFocus
        Me.CancelButton = Nothing
    End Sub

    Private Sub C1Combo1_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1Combo1.Validated
        Me.CancelButton = Comm2
    End Sub

    Private Sub C1Combo1_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles C1Combo1.KeyUp

        If (e.KeyValue > 47 And e.KeyValue < 58) Or (e.KeyValue > 96 And e.KeyValue < 123) Or (e.KeyValue > 64 And e.KeyValue < 91) Or (e.KeyValue = 8) Or (e.KeyValue = 189) Or (e.KeyValue = 190) Then
            Dim s As String = C1Combo1.Text
            If C1Combo1.Text = "" Then
                C1Combo1.DataSource.RowFilter = ""
            Else
                C1Combo1.DataSource.RowFilter = "Gx_Jc like '*" & C1Combo1.Text.Replace("*", "[*]") & "*'"
            End If
            If (e.KeyValue = 8) Then
                C1Combo1.DroppedDown = False
                C1Combo1.DroppedDown = True
            End If

            C1Combo1.Text = s
            C1Combo1.SelectionStart = C1Combo1.Text.Length
            C1Combo1.SelectionLength = 0
        End If

    End Sub

    Private Sub C1Combo1_KeyDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles C1Combo1.KeyDown
        If e.KeyValue = 13 Then
            If C1Combo1.WillChangeToIndex < 1 Then
                If (CType(C1Combo1.DataSource, DataView).Count) = 0 Then
                    MsgBox("功效: '" + Me.C1Combo1.Text + "' 不存在！", MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
                    System.Windows.Forms.SendKeys.Send("^{A}")
                    Exit Sub
                End If
                C1Combo1.SelectedIndex = -1
                C1Combo1.SelectedIndex = 0
            Else
                Dim index As Integer
                index = C1Combo1.WillChangeToIndex
                C1Combo1.SelectedIndex = -1
                C1Combo1.SelectedIndex = index
            End If
            e.Handled = True
            System.Windows.Forms.SendKeys.Send("{Tab}")
        End If
    End Sub
#End Region

#Region "Combo3动作"

    Private Sub C1Combo3_GotFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1Combo3.GotFocus
        Me.CancelButton = Nothing
        InputLanguage.CurrentInputLanguage = HisVar.HisVar.InputCode
    End Sub

    Private Sub C1Combo3_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1Combo3.Validated
        Me.CancelButton = Comm2
    End Sub

    Private Sub C1Combo3_RowChange(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1Combo3.RowChange
        If C1Combo3.WillChangeToValue = "" Then
            C1TextBox2.Text = ""
            L_Dl_Code.Text = ""
        Else
            C1TextBox2.Text = Trim(C1Combo3.Columns("Yp_Jc").Text)
            L_Dl_Code.Text = Trim(C1Combo3.Columns("Yp_Code").Text)
            C1Combo1.SelectedValue = Trim(C1Combo3.Columns("Gx_Code").Text)
            C1Combo2.Text = Trim(C1Combo3.Columns("IsJb").Text)
        End If
    End Sub

    Private Sub C1Combo3_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1Combo3.TextChanged
        If C1Combo3.Text = "" Then
            C1TextBox2.Text = ""
        End If
    End Sub

    Private Sub C1Combo3_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles C1Combo3.KeyPress
        Select Case e.KeyChar
            Case Chr(Keys.Enter)
                e.Handled = True
                C1TextBox2.Select()

            Case Chr(Keys.Escape)
                If C1Combo3.WillChangeToValue = "" Then                                 '客户简称不存在
                    If V_Gx_Code = "" Then
                        If C1Combo3.Text <> "" Then
                            C1Combo3.Text = ""
                            C1TextBox2.Text = ""
                        Else
                            Call Comm_Click(Comm2, Nothing)         '调用取消按键
                        End If
                    Else
                        C1Combo3.SelectedValue = V_Gx_Code          '恢复到原来的状态
                    End If
                Else                                                '客户简称存在
                    If C1Combo3.WillChangeToValue = V_Gx_Code Then                      '客户简称没有发生变化    
                        Call Comm_Click(Comm2, Nothing)             '调用取消按键
                    Else
                        C1Combo3.SelectedValue = V_Gx_Code          '恢复到原来的状态
                    End If
                End If
        End Select
    End Sub

    Private Sub C1Combo3_Validating(ByVal sender As System.Object, ByVal e As System.ComponentModel.CancelEventArgs) Handles C1Combo3.Validating
        My_Cc.Get_Py(Me.C1Combo3.Text & "")
        C1TextBox2.Text = My_Cc.简拚.ToString

        If C1Combo3.WillChangeToValue = "" Then
            If Rinsert = True Then
                L_Dl_Code.Text = ""
            End If
        End If
    End Sub


#End Region

#Region "输入法设置"
    '中文
    Private Sub C1TextBox1_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Combo3.GotFocus, C1TextBox3.GotFocus
        InputLanguage.CurrentInputLanguage = HisVar.HisVar.InputCode
    End Sub
    '英文
    Private Sub C1TextBox2_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1TextBox2.GotFocus, C1Combo1.GotFocus, C1TextBox1.GotFocus
        InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages(0)
    End Sub

#End Region

    Private Sub C1Combo3_DrawItem(ByVal sender As System.Object, ByVal e As C1.Win.C1List.OwnerDrawCellEventArgs) Handles C1Combo3.DrawItem
        'Dim bh1, bh2 As Brush
        'Dim ft As Font
        'If e.Col = 6 Then
        '    If e.Text = "国家基本药物" Then
        '        e.Style.Font.Style
        '        'bh1 = New SolidBrush(Color.Pink)
        '        bh2 = New SolidBrush(Color.Plum)
        '        'Dim ff As FontFamily
        '        'ff = New FontFamily("宋体")
        '        'ft = New Font(ff, 9, FontStyle.Regular, GraphicsUnit.Point)
        '        e.Graphics.FillRectangle(bh2, e.CellRect)
        '        ' e.Graphics.DrawString(e.Text, ft, New SolidBrush(Color.Black), e.CellRect.Left, e.CellRect.Top - 2)
        '    End If
        'Else
        'End If

        'e.Handled = True
    End Sub

    Private Sub C1TextBox1_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1TextBox1.TextChanged
        If C1TextBox1.Text = "" Then
            My_Tb.DefaultView.RowFilter = ""
        Else
            My_Tb.DefaultView.RowFilter = "Yp_Jc Like '*" & Trim(C1TextBox1.Text) & "*' or Yp_Name Like '*" & Trim(C1TextBox1.Text) & "*'"
        End If
    End Sub
    Private Sub C1TextBox1_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles C1TextBox1.KeyPress
        If e.KeyChar <> Chr(Keys.Return) Then Exit Sub
        C1Combo3.OpenCombo()
    End Sub
End Class