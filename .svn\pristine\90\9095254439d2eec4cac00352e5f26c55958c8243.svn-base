﻿Public Class C_Button
    Private My_Button1 As Button
    Private My_Button2 As Button

    Public Sub New(ByVal V_Button1 As Button, ByVal V_Button2 As Button) ' 定义一个公用构造函数来初始化Button
        My_Button1 = V_Button1
        My_Button2 = V_Button2
    End Sub

    Public Sub Init_Button(ByVal V_Left As Integer, ByVal V_Top As Integer)
        With My_Button1
            .Top = V_Top
            .Left = V_Left
            .BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")
            .Size = New Size(52, 24)
            .Text = "            &B"
            .FlatStyle = FlatStyle.Flat
            .FlatAppearance.BorderSize = 0
            .BackgroundImageLayout = ImageLayout.Center
        End With

        With My_Button2
            .BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
            .Size = New Size(52, 24)
            .Text = "            &C"
            .FlatStyle = FlatStyle.Flat
            .FlatAppearance.BorderSize = 0
            .BackgroundImageLayout = ImageLayout.Center
            .Width = My_Button1.Width
            .Height = My_Button1.Height
            .Location = New Point(My_Button1.Left + My_Button1.Width, My_Button1.Top)
        End With
    End Sub


    Public Sub MouseEnter(ByVal V_Tag As String)
        Select Case V_Tag
            Case "保存"
                My_Button1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存2")

            Case "取消"
                My_Button2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消2")
        End Select
    End Sub

    Public Sub MouseLeave(ByVal V_Tag As String)
        Select Case V_Tag
            Case "保存"
                My_Button1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")
            Case "取消"
                My_Button2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
        End Select
    End Sub

    Public Sub MouseDown(ByVal V_Tag As String)
        Select Case V_Tag
            Case "保存"
                My_Button1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存3")
            Case "取消"
                My_Button2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消3")
        End Select
    End Sub

    Public Sub MouseUp(ByVal V_Tag As String)
        Select Case V_Tag
            Case "保存"
                My_Button1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")
            Case "取消"
                My_Button2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
        End Select
    End Sub

    Public Sub KeyDown(ByVal V_Tag As String)
        Select Case V_Tag
            Case "保存"
                My_Button1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存3")
            Case "取消"
                My_Button2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消3")
        End Select
    End Sub

    Public Sub KeyUp(ByVal V_Tag As String)
        Select Case V_Tag
            Case "保存"
                My_Button1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")
            Case "取消"
                My_Button2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
        End Select
    End Sub

End Class
