﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using YBBLL;
using YBModel;

namespace ERX
{
    public class ERXConfig
    {
        public static string ERX_Id { get; set; } = "";
        public static string ERX_Key { get; set; } = "";
        public static string ERX_PriKey { get; set; } = "";
        public static string ERX_PbcKey { get; set; } = "";
        public static string ERX_Url { get; set; } = "";

        public static bool Erx_Init()
        {
            #region 读取电子处方配置信息
            BllERx_Config _bllERxConfig = new BllERx_Config();
            MdlERx_Config _mdlERxConfig = new MdlERx_Config();

            _mdlERxConfig = _bllERxConfig.GetModel(1);
            if (_mdlERxConfig == null)
            {
                return false;
            }

            ERX_Id = _mdlERxConfig.ERX_Id;
            ERX_Key = _mdlERxConfig.ERX_Key;
            ERX_PriKey = _mdlERxConfig.ERX_PriKey;
            ERX_Url = _mdlERxConfig.ERX_Url;

            #endregion

            return true;
        }
    }
}
