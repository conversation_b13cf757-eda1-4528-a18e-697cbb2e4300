﻿Imports System.Data.SqlClient
Imports System.Linq
Imports System.Text
Imports System.Text.RegularExpressions
Imports Common
Imports DTO
Imports BLL
Imports Common.Delegate
Imports HisControl
Imports Model
Imports ZTHisOutpatient
Imports ZTHisPara

Public Class Ys_Cf2

#Region "定义__变量"

    Dim My_Adapter As New SqlDataAdapter                '从表适配器
    Dim Cb_Cm_Yp As CurrencyManager                     '同步指针
    Dim Cb_Cm_Xm As CurrencyManager
    Dim Cb_Row_Yp As DataRow                            '当前选择行
    Dim Cb_Row_Xm As DataRow                            '当前选择行
    Dim V_Insert As Boolean                          '增加记录
    Dim My_Dataset As New DataSet
    Public F_CfOK As Boolean
    Public F_CfOkstr As String
    Public Mz_Code As String = ""
    Dim Tb As DataTable
    Private _patientInfo As New DtoPatientInfo()
    Private _transmitTxt As Common.Delegate.TransmitTxt = New TransmitTxt()
    Private _transmitTxt2 As Common.Delegate.TransmitTxt = New TransmitTxt()
    Dim listDiag As New List(Of MdlMz_Diag)
    Dim _bllMzDiag As New BllMz_Diag
    Dim _bllMz As New BllMz
    Dim _bllMzYp As New BllMz_Yp
    Dim _bllMzXm As New BllMz_Xm
    Dim _bllZdMlYp4 As New BllZd_Ml_Yp4
#End Region

#Region "传参"
    Dim Rform As BaseForm
    Dim Rinsert As Boolean
    Dim Rdate As Date
    Dim Rrow As DataRow
    Dim RZbtb As DataTable
    Dim Rtdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Dim Rlb As C1.Win.C1Input.C1Label



#End Region

    Public Sub New(ByVal tform As BaseForm, ByVal tinsert As Boolean, ByVal tdate As Date, ByVal trow As DataRow, ByVal tZbtb As DataTable, ByVal ttdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid, ByVal tlb As C1.Win.C1Input.C1Label)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        Rform = tform
        Rinsert = tinsert
        Rdate = tdate
        Rrow = trow
        RZbtb = tZbtb
        Rtdbgrid = ttdbgrid
        Rlb = tlb
        ' 在 InitializeComponent() 调用之后添加任何初始化。
    End Sub

    Protected Overrides Sub OnKeyDown(ByVal e As System.Windows.Forms.KeyEventArgs)
        MyBase.OnKeyDown(e)
        If e.KeyCode = Keys.F1 Then
            RadioButton4.Checked = True
        ElseIf e.KeyCode = Keys.F2 Then
            RadioButton1.Checked = True
        ElseIf e.KeyCode = Keys.F3 Then
            Comm3.Select()
            Call Comm_Click(Comm3, Nothing)
        ElseIf e.KeyCode = Keys.F4 Then
            comm4.Select()
            Call Comm_Click(comm4, Nothing)
        ElseIf e.Control = True And e.KeyCode = Keys.S Then
            Comm1.Select()
            Call Comm_Click(Comm1, Nothing)
        End If
    End Sub

    Private Sub Ys_Cf_Yp_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        F_CfOK = False
        F_CfOkstr = "否"
        '1：ZjJsStyle=2是什么意思:2：能打开这个窗体不就代表启动门诊医生站了么
        If HisPara.PublicConfig.ZjJsStyle = 2 And HisPara.PublicConfig.MzYsz = "是" Then
            comm5.Visible = True
        Else
            comm5.Visible = False
        End If
        '后改
        Call Form_Init()                '窗体初始化
        If HisPara.PublicConfig.MzGh = "是" Then
            TableLayoutPanel1.Controls.Add(lblHzName, 2, 0)
            TableLayoutPanel1.Controls.Add(C1Combo2, 3, 0)
            Me.TableLayoutPanel1.SetColumnSpan(Me.C1Combo2, 3)
            C1Combo2.Visible = True
            lblHzName.Visible = True
            XmTextBox.Visible = False
            SfzhTextBox.ReadOnly = True
            SingleSex1.ReadOnly = True
            NlC1NumericEdit.ReadOnly = True
            NumAgeDay.ReadOnly = True
            'C1TextBox1.ReadOnly = True
            YlCodeTextBox.ReadOnly = True
            ComboKs1.Enabled = False
            ComboYs1.Enabled = False

            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "Select Mz_Code,Ry_Jc,Ry_Name,Ry_Sex,Ry_Sfzh,Ry_Age,Convert(Varchar(10),Gh_Date,126)Gh_Date,Ry_Address,Ry_Tel,Mz_Gh.Bxlb_Code,Bxlb_Name,Ry_YlCode,Ks_Code,Identity_Code,Ry_Birthday,IdentityType from Mz_Gh,Zd_Bxlb 
where Mz_Gh.Bxlb_Code=Zd_Bxlb.Bxlb_Code and Gh_Print='是' and Gh_Zf='否' and Ks_Code='" & HisVar.HisVar.XmKs & "'and Convert(Varchar(10),Gh_Date,126) BETWEEN '" & Format(Rdate.AddDays(-ZTHisPara.PublicConfig.RegValidDays), "yyyy-MM-dd") & "' And '" & Format(Rdate, "yyyy-MM-dd") & "' 
Union all Select Mz_Code,Ry_Jc,Ry_Name,Ry_Sex,Ry_Sfzh,Ry_Age,Convert(Varchar(10),Gh_Date,126)Gh_Date,Ry_Address,Ry_Tel,Mz_Gh_Sum.Bxlb_Code,Bxlb_Name,Ry_YlCode,Ks_Code,Identity_Code,Ry_Birthday,IdentityType from Mz_Gh_Sum,Zd_Bxlb 
where Mz_Gh_Sum.Bxlb_Code=Zd_Bxlb.Bxlb_Code and Gh_Print='是' and Gh_Zf='否' and Ks_Code='" & HisVar.HisVar.XmKs & "' and Convert(Varchar(10),Gh_Date,126) BETWEEN '" & Format(Rdate.AddDays(-ZTHisPara.PublicConfig.RegValidDays), "yyyy-MM-dd") & "' And '" & Format(Rdate, "yyyy-MM-dd") & "' order by Mz_Code desc", "挂号人员", True)

            Dim My_Combo2 As New BaseClass.C_Combo2(Me.C1Combo2, My_Dataset.Tables("挂号人员").DefaultView, "Ry_Name", "Mz_Code", 560)
            With My_Combo2
                .Init_TDBCombo()
                .Init_Colum("Mz_Code", "挂号编码", 0, "左")
                .Init_Colum("Ry_Jc", "姓名简称", 65, "左")
                .Init_Colum("Ry_Name", "姓名", 90, "左")
                .Init_Colum("Ry_Sex", "性别", 35, "中")
                .Init_Colum("Ry_Sfzh", "身份证号", 120, "左")
                .Init_Colum("Ry_Age", "年龄", 40, "右")
                .Init_Colum("Gh_Date", "挂号日期", 80, "中")
                .Init_Colum("Ry_Address", "家庭住址", 0, "左")
                .Init_Colum("Ry_Tel", "电话", 0, "左")
                .Init_Colum("Bxlb_Code", "", 0, "左")
                .Init_Colum("Bxlb_Name", "患者类别", 65, "中")
                .Init_Colum("Ry_YlCode", "医疗证号", 120, "左")
                .Init_Colum("Ks_Code", "科室", 0, "左")
                .Init_Colum("Identity_Code", "Identity_Code", 0, "左")
                .Init_Colum("Ry_Birthday", "出生日期", 0, "左")
                .MaxDropDownItems(17)
                .SelectedIndex(-1)
            End With
            With C1Combo2
                .AutoCompletion = False
                .AutoSelect = False
            End With
        Else
            TableLayoutPanel1.Controls.Add(XmTextBox, 2, 0)
            C1Combo2.Visible = False
            lblHzName.Visible = False
            XmTextBox.Visible = True
        End If


        ComboYf1.SelectedValue = iniOperate.iniopreate.GetINI("Personal", "默认药房", "", HisVar.HisVar.Parapath & "\Config.Ini") & ""
        AddHandler Me._transmitTxt.SetText, AddressOf GridMove
        AddHandler Me._transmitTxt2.SetText, AddressOf GridMove2
        If Rinsert = True Then           '主表
            Call Zb_Clear()                 '清空数据
        Else
            Call Zb_Show()                  '显示数据
        End If

        RadioButton1.Checked = True

        Call P_Data_Show()



    End Sub

#Region "窗体__事件"

    Private Sub Form_Init()
        Panel2.Height = 30

        '按扭初始化
        'Call P_Comm(Me.Comm1)
        'Call P_Comm(Me.Comm2)
        'Call P_Comm(Me.Comm3)
        'Call P_Comm(Me.comm4)
        'Call P_Comm(Me.comm5)

        C1NumericEdit1.Enabled = False
        C1Command1.Enabled = False

        Dim My_Grid1 As New BaseClass.C_Grid(Me.C1TrueDBGrid1)
        With My_Grid1
            .Init_Grid()
            .AllAddNew(True)
            .AllUpdate(False)
            .AllSort(False)

            .P_Dock(BaseClass.C_Grid.Dock.Fill)
            .Init_Column("药品名称", "Yp_Name", 260, "左", "")
            .Init_Column("规格", "Mx_Gg", 100, "左", "")
            .Init_Column("产地", "Mx_Cd", 120, "左", "")
            .Init_Column("有效期", "Yp_Yxq", 80, "中", "yyyy-MM-dd")
            .Init_Column("数量", "Mz_Sl", 70, "右", "###,###,##0.00##")
            .Init_Column("单价", "Mz_Dj", 70, "右", "###,###,##0.00####")
            .Init_Column("金额", "Mz_Money", 70, "右", "###,###,##0.00##")
            .Init_Column("用法用量", "Yp_Yfyl", 200, "左", "")
            .Init_Column("国家医保编码", "医疗目录编码", 100, "左", "")

        End With

        Dim My_Grid2 As New BaseClass.C_Grid(Me.C1TrueDBGrid2)
        With My_Grid2
            .Init_Grid()
            .AllAddNew(True)
            .AllUpdate(False)
            .AllSort(False)

            .P_Dock(BaseClass.C_Grid.Dock.Fill)
            .Init_Column("项目名称", "Xm_Name", 380, "左", "")
            .Init_Column("项目单位", "Xm_Dw", 100, "左", "")
            .Init_Column("数量", "Mz_Sl", 70, "右", "###,###,##0")
            .Init_Column("单价", "Mz_Dj", 70, "右", "###,###,##0.00")
            .Init_Column("金额", "Mz_Money", 70, "右", "###,###,##0.00")
            .Init_Column("国家医保编码", "医疗目录编码", 70, "左", "")

        End With
        SingleSex1.Init()
        ComboBxlb1.Init()
        ComboKs1.Init("Zd_YyKs.OI_Code IN('02','04') ")
        ComboYs1.Init()
        ComboJb1.Init()
        ComboYf1.Init("Yf_Use=1")
        SingleMzCfZxType1.Init()
        If Not PublicConfig.MzYszChangeYs Then
            ComboYs1.Enabled = False
        End If
        ComDiagType.Init()
        comboIdentityType.Init("SubCategoryValue='IdentityType'")
        Dim autohide As String = Common.WinFormVar.Var.IniFileHelper.IniReadValue("参数", "门诊医生站辅助框") + ""
        C1DockingTab1.AutoHiding = Not String.IsNullOrEmpty(autohide) AndAlso Boolean.Parse(autohide)
        MzData1.Init(XmTextBox, TxtTel, SfzhTextBox)
        MzData1.GenCzdAction = AddressOf GenCzd
    End Sub

    Private Sub Ys_Cf_Yp_FormClosing(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosingEventArgs) Handles Me.FormClosing
        If e.CloseReason = CloseReason.UserClosing Then
            If Rinsert = False Then
                If HisVar.HisVar.Sqldal.GetSingle("Select Isnull(Mz_Print,0)|Isnull(MzCf_Ok,0)|Isnull(Mz_FyQr,0) from Mz where  Mz_Code='" & Rrow.Item("Mz_Code") & "'") = False Then
                    Call Zb_Save() '更新主表(主表已经保存过,取消操作)
                Else
                    Call Rform.F_Sum()
                End If
            End If
        End If
    End Sub

#End Region

#Region "清空__显示"

    Private Sub Zb_Clear()

        lblMz_Code.Text = F_MaxCode(Format(Rdate, "yyMMdd"))       '出库编码
        XmTextBox.Text = ""
        AddressTextBox.Text = ""
        YlCodeTextBox.Text = ""
        NlC1NumericEdit.Value = Nothing
        NumAgeDay.Value = Nothing
        comboIdentityType.SelectedValue = "0001"
        SfzhTextBox.Text = ""
        TxtTel.Text = ""
        Label20.Text = ""
        C1NumericEdit1.Value = 1
        DateRyBirthday.Value = Nothing
        ComboYs1.SelectedValue = ZTHisVar.Var.YsCode
        ComboKs1.SelectedValue = ZTHisVar.Var.KsCode

        C1Combo2.SelectedIndex = -1
        SingleSex1.SelectedIndex = 0
        ComboBxlb1.SelectedIndex = 0
        ComboJb1.SelectedIndex = -1
        Dim diagType As Object = Common.WinFormVar.Var.IniFileHelper.IniReadValue("参数", "门诊医生站诊断类型")
        If (String.IsNullOrEmpty(diagType)) Then diagType = 0
        ComDiagType.SelectedIndex = diagType
        Dim mzCfZxType As Object = Common.WinFormVar.Var.IniFileHelper.IniReadValue("参数", "门诊医生站执行性质")
        If (String.IsNullOrEmpty(mzCfZxType)) Then mzCfZxType = 0
        SingleMzCfZxType1.SelectedIndex = mzCfZxType
        lblBalc.Text = ""
        listDiag = New List(Of MdlMz_Diag)
        FbDate.Value = Format(Now, "yyyy-MM-dd")
        If Mz_Code <> "" Then
            C1Combo2.SelectedValue = Mz_Code
            Mz_Code = ""
        End If
    End Sub

    Private Sub Zb_Show()   '显示记录
        With Rrow
            lblMz_Code.Text = .Item("Mz_Code") & ""
            comboIdentityType.SelectedValue = .Item("IdentityType") & ""
            SfzhTextBox.Text = .Item("Ry_Sfzh") & "" '
            If HisPara.PublicConfig.MzGh = "是" Then
                C1Combo2.SelectedValue = .Item("MzGh_Code") & ""
            Else
                If SfzhTextBox.Text <> "" Then

                    Dim R As Regex
                    R = New Regex("^\d{18}$|^\d{17}[a-zA-Z]{1}$|^\d{15}$")
                    If R.IsMatch(SfzhTextBox.Text) = True Then
                        If ComboBxlb1.Columns("Bxlb_Name").Value = "合作医疗" Or ComboBxlb1.Columns("Bxlb_Name").Value = "城乡居民" Then
                            'Call P_JtData()
                        End If
                    End If
                Else
                End If
            End If
            XmTextBox.Text = .Item("Ry_Name") & ""
            SingleSex1.Text = .Item("Ry_Sex") & ""
            NlC1NumericEdit.Value = .Item("Ry_Age") & ""
            NumAgeDay.Value = .Item("Ry_Age_Day") & ""
            AddressTextBox.Text = .Item("Ry_Address") & ""
            TxtTel.Text = .Item("Ry_Tell") & ""
            ComboBxlb1.SelectedValue = .Item("Bxlb_Code") & ""
            FbDate.Value = .Item("Mz_FbDate") & ""
            YlCodeTextBox.Text = .Item("Ry_YlCode") & ""
            ComboJb1.SelectedValue = .Item("Jb_Code") & ""
            Label20.Text = .Item("Jb_Name") & ""
            ComboYs1.SelectedValue = .Item("Ys_Code") & ""
            ComboKs1.SelectedValue = .Item("Ks_Code") & ""
            If .Item("Yf_Code") & "" <> "" Then
                ComboYf1.SelectedValue = .Item("Yf_Code") & ""
            End If
            SingleMzCfZxType1.SelectedValue = EnumConvert.ConvertEnumToInt(Of ZTHisEnum.MzCfZxType)(.Item("MzCfZxType"))
            MzData1.MzCfZxType = System.[Enum].Parse(GetType(ZTHisEnum.MzCfZxType), SingleMzCfZxType1.Text)
        End With
        listDiag = _bllMzDiag.GetModelList($"Mz_Code='{lblMz_Code.Text}'")
        C1NumericEdit1.Value = 1


    End Sub

    Private Sub P_Data_Show()   '从表数据
        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "Select Mz_Xm.*,Xm_Dw,Xm_Name,医疗目录编码 From Mz_Xm,Zd_Ml_Xm3 Left Join Country_YBMLDZ_XM on Xm_Code=Mx_Code Where  Mz_Xm.Xm_Code=Zd_Ml_Xm3.Xm_Code And Mz_Code='" & lblMz_Code.Text & "' Order By Mz_Id", "诊疗项目", True)
        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "Select Mz_Yp.*,Mx_Gg,Mx_Cd,Yp_Name,Mx_XsDw,V_YpKc.Yp_Ph,V_YpKc.Yp_Yxq,医疗目录编码,Dl_Code,Mx_Gyzz From Mz_Yp,V_YpKc Left Join (SELECT Mx_Code,医疗目录编码 FROM Country_YBMLDZ_WC UNION ALL SELECT Mx_Code,医疗目录编码 FROM Country_YBMLDZ_XY UNION ALL SELECT Mx_Code,医疗目录编码 FROM Country_YBMLDZ_ZY)YBDZ On YBDZ.Mx_Code=V_YpKc.Mx_Code Where Mz_Yp.Xx_Code=V_YpKc.Xx_Code And Mz_Code='" & lblMz_Code.Text & "' Order By Mz_Id", "药品卫材", True)


        My_Dataset.Tables("药品卫材").PrimaryKey = New DataColumn() {My_Dataset.Tables("药品卫材").Columns("Mz_Id")}
        My_Dataset.Tables("诊疗项目").PrimaryKey = New DataColumn() {My_Dataset.Tables("诊疗项目").Columns("Mz_Id")}


        '列的唯一性
        Dim My_Column_Yp As DataColumn = My_Dataset.Tables("药品卫材").Columns("Mz_Id")
        With My_Column_Yp
            .AutoIncrement = True
            .AutoIncrementSeed = .AutoIncrementSeed
            .AutoIncrementStep = 1
        End With
        My_Dataset.Tables("药品卫材").Columns("医疗目录编码").ReadOnly = False

        Dim My_Column_Xm As DataColumn = My_Dataset.Tables("诊疗项目").Columns("Mz_Id")
        With My_Column_Xm
            .AutoIncrement = True
            .AutoIncrementSeed = .AutoIncrementSeed
            .AutoIncrementStep = 1
        End With

        '主表记录
        Cb_Cm_Yp = CType(BindingContext(My_Dataset, "药品卫材"), CurrencyManager)
        Cb_Cm_Xm = CType(BindingContext(My_Dataset, "诊疗项目"), CurrencyManager)

        C1TrueDBGrid1.SetDataBinding(My_Dataset, "药品卫材", True)
        C1TrueDBGrid2.SetDataBinding(My_Dataset, "诊疗项目", True)

        Call F_Sum()

        If HisPara.PublicConfig.MzGh = "是" Then
            C1Combo2.Select()
        Else
            XmTextBox.Select()
        End If
        MzData1.YpTable = My_Dataset.Tables("药品卫材")
        MzData1.XmTable = My_Dataset.Tables("诊疗项目")
    End Sub

#End Region

#Region "主表__编辑"

    Private Function Zb_Save() As Boolean                      '主表保存
        If XmTextBox.Text.Trim = "" Then
            Beep()
            MsgBox("门诊患者姓名不能为空!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
            XmTextBox.Select()
            Return False
        End If
        If CustomControl.Func.NotAllowEmpty(comboIdentityType) Then Return False
        If SfzhTextBox.Text.Trim() <> "" AndAlso comboIdentityType.SelectedValue.ToString() = "0001" Then
            If Not Common.RegexHelper.IsIDCard(SfzhTextBox.Text) Then
                SfzhTextBox.Select()
                MessageBox.Show("请填写正确的身份证号码!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
                SfzhTextBox.Select()
                Return False
            End If
        End If

        If ComboYs1.SelectedValue = "" Then
            Beep()
            MsgBox("医生不能为空!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
            ComboYs1.Select()
            Return False
        End If

        If ComboKs1.SelectedValue = "" Then
            Beep()
            MsgBox("科室不能为空!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
            ComboKs1.Select()
            Return False
        End If

        If ComboYf1.SelectedValue = "" Then
            Beep()
            MsgBox("药房编码不能为空!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
            ComboYf1.Select()
            Return False
        End If

        If CustomControl.Func.NotAllowEmpty(ComboBxlb1) Then Return False

        If ComboJb1.SelectedValue + "" <> "" And Label20.Text.Trim() = "" Then
            MsgBox("请点击【增加疾病】按钮保存诊断数据!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
            Return False
        End If

        If _bllMz.GetRecordCount($"  mzcf_ok=1 and mz_code='{lblMz_Code.Text }'") > 0 Then
            MsgBox("当前处方已完成，请关闭当前窗体", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
            Return False
        End If
        Call Zb_Add()
        Rform.F_Sum()
        Common.WinFormVar.Var.IniFileHelper.IniWriteValue("参数", "门诊医生站执行性质", SingleMzCfZxType1.SelectedIndex)
        Common.WinFormVar.Var.IniFileHelper.IniWriteValue("参数", "门诊医生站诊断类型", ComDiagType.SelectedIndex)
        Return True
    End Function

    Private Sub Zb_Add()    '增加记录

        Dim My_NewRow As DataRow
        If Rinsert = True Then
            My_NewRow = RZbtb.NewRow
        Else
            My_NewRow = Rrow
        End If

        With My_NewRow
            .Item("Yy_Code") = HisVar.HisVar.WsyCode
            If Rinsert = True Then
                .Item("Mz_Code") = F_MaxCode(Format(Rdate, "yyMMdd"))
            Else
                .Item("Mz_Code") = .Item("Mz_Code")
            End If
            lblMz_Code.Text = .Item("Mz_Code")
            .Item("Yf_Code") = ComboYf1.SelectedValue
            .Item("Ks_Code") = ComboKs1.SelectedValue
            .Item("Ys_Code") = ComboYs1.SelectedValue
            .Item("Mz_Date") = Format(Rdate, "yyyy-MM-dd")               '入库日期
            .Item("Mz_Time") = Format(Now, "HH:mm:ss")
            .Item("Lr_JsrCode") = HisVar.HisVar.JsrCode
            .Item("Jb_Code") = listDiag.FirstOrDefault()?.Jb_Code
            .Item("Jb_Name") = Label20.Text
            .Item("Bxlb_Code") = ComboBxlb1.SelectedValue
            .Item("Bxlb_Name") = ComboBxlb1.Text
            .Item("Ry_YlCode") = YlCodeTextBox.Text
            .Item("Ry_Name") = XmTextBox.Text
            .Item("Ry_Sex") = SingleSex1.Text
            If Trim(NlC1NumericEdit.Text) = "" Then
                .Item("Ry_Age") = DBNull.Value
            Else
                .Item("Ry_Age") = NlC1NumericEdit.Text
            End If
            .Item("Ry_Age_Day") = NumAgeDay.Value
            .Item("Ry_Address") = AddressTextBox.Text
            .Item("Ry_Tell") = TxtTel.Text
            .Item("IdentityType") = comboIdentityType.SelectedValue
            .Item("IdentityType_Name") = comboIdentityType.Text
            .Item("Ry_Sfzh") = SfzhTextBox.Text
            .Item("Ry_Birthday") = DateRyBirthday.Value

            If HisPara.PublicConfig.MzGh = "是" Then
                .Item("Ry_Memo") = C1Combo2.Columns("Mz_Code").Value
                .Item("MzGh_Code") = C1Combo2.Columns("Mz_Code").Value
                .Item("Identity_Code") = C1Combo2.Columns("Identity_Code").Value
            Else
                .Item("Ry_Memo") = ""
                .Item("Identity_Code") = ""
            End If
            Dim V_YpMoney As Decimal = HisVar.HisVar.Sqldal.GetSingle("select Isnull(Sum(Mz_Money),0) from Mz_Yp where Mz_Code='" & lblMz_Code.Text & "'")
            Dim V_XmMoney As Decimal = HisVar.HisVar.Sqldal.GetSingle("select Isnull(Sum(Mz_Money),0) from Mz_Xm where Mz_Code='" & lblMz_Code.Text & "'")
            .Item("Mz_YpMoney") = Format(V_YpMoney, "0.00##")
            .Item("Mz_XmMoney") = Format(V_XmMoney, "0.00##")
            .Item("Mz_Money") = Format(V_YpMoney + V_XmMoney, "0.00##")
            .Item("Mz_Original_Money") = Format(V_YpMoney + V_XmMoney, "0.00##")
            .Item("Ks_Name") = ComboKs1.Text
            .Item("Ys_Name") = ComboYs1.Text
            .Item("Mz_FbDate") = Format(FbDate.Value, "yyyy-MM-dd")
            If Rinsert = True Then
                .Item("MzCf_Ok") = False
                .Item("MzCf_Ok1") = "否"
            End If
            .Item("MzCfZxType") = SingleMzCfZxType1.Text
        End With

        '显示增加后的状态
        Call Zb_Update(My_NewRow)

        Rlb.Text = "∑=" + Rtdbgrid.Splits(0).Rows.Count.ToString

    End Sub

    Private Sub Zb_Update(ByVal V_Row As DataRow)     '更新主表
        Dim Insert_String As String = ""
        Dim Update_String As String = ""

        Dim DeleteDiag As String = ""
        Dim para() As SqlParameter = Nothing
        Dim ilist As List(Of SqlParameter) = New List(Of SqlParameter)()

        ilist.Add(New SqlParameter("@Yy_Code", SqlDbType.Char))
        ilist.Add(New SqlParameter("@Yf_Code", SqlDbType.VarChar))
        ilist.Add(New SqlParameter("@Ks_Code", SqlDbType.VarChar))
        ilist.Add(New SqlParameter("@Ys_Code", SqlDbType.VarChar))
        ilist.Add(New SqlParameter("@Mz_Date", SqlDbType.VarChar))
        ilist.Add(New SqlParameter("@Mz_Time", SqlDbType.VarChar))
        ilist.Add(New SqlParameter("@Jsr_Code", SqlDbType.VarChar))
        ilist.Add(New SqlParameter("@Jb_Code", SqlDbType.VarChar))
        ilist.Add(New SqlParameter("@Jb_Name", SqlDbType.VarChar))
        ilist.Add(New SqlParameter("@Bxlb_Code", SqlDbType.VarChar))
        ilist.Add(New SqlParameter("@Ry_YlCode", SqlDbType.VarChar))
        ilist.Add(New SqlParameter("@Ry_Name", SqlDbType.VarChar))
        ilist.Add(New SqlParameter("@Ry_Sex", SqlDbType.VarChar))
        ilist.Add(New SqlParameter("@Ry_Age", SqlDbType.Decimal))
        ilist.Add(New SqlParameter("@Ry_Address", SqlDbType.VarChar))
        ilist.Add(New SqlParameter("@Ry_Sfzh", SqlDbType.VarChar))
        ilist.Add(New SqlParameter("@Ry_Memo", SqlDbType.VarChar))
        ilist.Add(New SqlParameter("@RY_Tell", SqlDbType.VarChar))
        ilist.Add(New SqlParameter("@Mz_YpMoney", SqlDbType.Decimal))
        ilist.Add(New SqlParameter("@Mz_XmMoney", SqlDbType.Decimal))
        ilist.Add(New SqlParameter("@Mz_Money", SqlDbType.Decimal))
        ilist.Add(New SqlParameter("@Mz_Original_Money", SqlDbType.Decimal))
        ilist.Add(New SqlParameter("@MzGh_Code", SqlDbType.VarChar))
        ilist.Add(New SqlParameter("@Identity_Code", SqlDbType.Char))
        ilist.Add(New SqlParameter("@Mz_FbDate", SqlDbType.VarChar))
        ilist.Add(New SqlParameter("@Ry_Birthday", SqlDbType.DateTime))
        ilist.Add(New SqlParameter("@Lr_JsrCode", SqlDbType.VarChar))
        ilist.Add(New SqlParameter("@MzCfZxType", SqlDbType.VarChar))
        ilist.Add(New SqlParameter("@Mz_Code", SqlDbType.VarChar))
        ilist.Add(New SqlParameter("@MzCf_Ok", SqlDbType.Bit))
        ilist.Add(New SqlParameter("@Ry_Age_Day", SqlDbType.Decimal))
        ilist.Add(New SqlParameter("@IdentityType", SqlDbType.Char))


        para = ilist.ToArray()

        Insert_String = "Insert Into Mz(Yy_Code,Yf_Code,Ks_Code,Ys_Code,Mz_Date,Mz_Time,Jb_Code,Jb_Name,Bxlb_Code,Ry_YlCode,Ry_Name,Ry_Sex,Ry_Age,Ry_Address,Ry_Sfzh,Ry_Memo,RY_Tell,Mz_YpMoney,Mz_XmMoney,Mz_Money,Mz_Original_Money,MzGh_Code,Identity_Code,Mz_FbDate,Ry_Birthday,Lr_JsrCode,MzCfZxType,Mz_Code,MzCf_Ok,Ry_Age_Day,IdentityType)Values(@Yy_Code,@Yf_Code,@Ks_Code,@Ys_Code,@Mz_Date,@Mz_Time,@Jb_Code,@Jb_Name,@Bxlb_Code,@Ry_YlCode,@Ry_Name,@Ry_Sex,@Ry_Age,@Ry_Address,@Ry_Sfzh,@Ry_Memo,@RY_Tell,@Mz_YpMoney,@Mz_XmMoney,@Mz_Money,@Mz_Original_Money,@MzGh_Code,@Identity_Code,@Mz_FbDate,@Ry_Birthday,@Lr_JsrCode,@MzCfZxType,@Mz_Code,@MzCf_Ok,@Ry_Age_Day,@IdentityType)"
        Update_String = "Update Mz Set Yy_Code=@Yy_Code,Yf_Code=@Yf_Code,Ks_Code=@Ks_Code,Ys_Code=@Ys_Code,Mz_Date=@Mz_Date,Mz_Time=@Mz_Time,Jb_Code=@Jb_Code,Jb_Name=@Jb_Name,Bxlb_Code=@Bxlb_Code,Ry_YlCode=@Ry_YlCode,Ry_Name=@Ry_Name,Ry_Sex=@Ry_Sex,Ry_Age=@Ry_Age,Ry_Address=@Ry_Address,Ry_Sfzh=@Ry_Sfzh,Ry_Memo=@Ry_Memo,RY_Tell=@RY_Tell,Mz_YpMoney=@Mz_YpMoney,Mz_XmMoney=@Mz_XmMoney,Mz_Money=@Mz_Money,Mz_Original_Money=@Mz_Original_Money,MzGh_Code=@MzGh_Code,Identity_Code=@Identity_Code,Mz_FbDate=@Mz_FbDate,Ry_Birthday=@Ry_Birthday,Lr_JsrCode=@Lr_JsrCode,MzCfZxType=@MzCfZxType,MzCf_Ok=@MzCf_Ok,Ry_Age_Day=@Ry_Age_Day,IdentityType=@IdentityType Where Mz_Code=@Mz_Code"
        DeleteDiag = "Delete Mz_Diag where Mz_Code=@Mz_Code"
        Dim InsertDiag As String = "Insert Into Mz_Diag (Mz_Code,Diag_Type ,Diag_Type_Name ,Diag_Srt_No ,Jb_Code ,Jb_Name ,Ks_Code,Ys_Code ,Diag_Time ,Created_Jsr ,Created_Time ,Update_Jsr ,Update_Time ) values(@Mz_Code,@Diag_Type,@Diag_Type_Name,@Diag_Srt_No,@Jb_Code,@Jb_Name,@Ks_Code,@Ys_Code,@Diag_Time,@Created_Jsr,@Created_Time,@Update_Jsr,@Update_Time)"

        For I = 0 To para.Length - 1
            para(I).Value = V_Row.Item(Mid(para(I).ParameterName, 2))
        Next

        Dim strList As List(Of String) = New List(Of String)()
        Dim paraList As List(Of SqlParameter()) = New List(Of SqlParameter())
        Try
            If Rinsert = True Then
                strList.Add(Insert_String)
                paraList.Add(para)
                For Each mdlDiag In listDiag
                    Dim ParaMeter() As SqlParameter = {New SqlParameter("@Mz_Code", SqlDbType.Char, 14),
                                                       New SqlParameter("@Diag_Type", SqlDbType.VarChar, 3),
                                                       New SqlParameter("@Diag_Type_Name", SqlDbType.VarChar, 50),
                                                       New SqlParameter("@Diag_Srt_No", SqlDbType.Int),
                                                       New SqlParameter("@Jb_Code", SqlDbType.VarChar, 20),
                                                       New SqlParameter("@Jb_Name", SqlDbType.VarChar, 200),
                                                       New SqlParameter("@Ks_Code", SqlDbType.Char, 6),
                                                       New SqlParameter("@Ys_Code", SqlDbType.Char, 7),
                                                       New SqlParameter("@Diag_Time", SqlDbType.SmallDateTime),
                                                       New SqlParameter("@Created_Jsr", SqlDbType.VarChar, 50),
                                                       New SqlParameter("@Created_Time", SqlDbType.DateTime),
                                                       New SqlParameter("@Update_Jsr", SqlDbType.VarChar, 50),
                                                       New SqlParameter("@Update_Time", SqlDbType.DateTime)}
                    ParaMeter(0).Value = V_Row("Mz_Code")
                    ParaMeter(1).Value = mdlDiag.Diag_Type
                    ParaMeter(2).Value = mdlDiag.Diag_Type_Name
                    ParaMeter(3).Value = mdlDiag.Diag_Srt_No
                    ParaMeter(4).Value = mdlDiag.Jb_Code
                    ParaMeter(5).Value = mdlDiag.Jb_Name
                    ParaMeter(6).Value = mdlDiag.Ks_Code
                    ParaMeter(7).Value = mdlDiag.Ys_Code
                    ParaMeter(8).Value = mdlDiag.Diag_Time
                    ParaMeter(9).Value = mdlDiag.Created_Jsr
                    ParaMeter(10).Value = mdlDiag.Created_Time
                    ParaMeter(11).Value = Common.Tools.IsValueNull(mdlDiag.Update_Jsr)
                    ParaMeter(12).Value = Common.Tools.IsValueNull(mdlDiag.Update_Time)

                    strList.Add(InsertDiag)
                    paraList.Add(ParaMeter)
                Next
                Dim strSql1 As New StringBuilder()
                strSql1.Append("UPDATE Mz_Gh SET OutPatient_State='已就诊' ")
                strSql1.Append("WHERE Mz_Code = @MzGh_Code ")
                Dim parameters1 As SqlParameter() = {New SqlParameter("@MzGh_Code", SqlDbType.Char, 14)}
                parameters1(0).Value = V_Row("MzGh_Code")
                strList.Add(strSql1.ToString())
                paraList.Add(parameters1)

                Dim strSql2 As New StringBuilder()
                strSql2.Append("UPDATE Mz_Gh_Sum SET OutPatient_State='已就诊' ")
                strSql2.Append("WHERE Mz_Code = @MzGh_Code ")
                Dim parameters2 As SqlParameter() = {New SqlParameter("@MzGh_Code", SqlDbType.Char, 14)}
                parameters2(0).Value = V_Row("MzGh_Code")
                strList.Add(strSql2.ToString())
                paraList.Add(parameters2)
                HisVar.HisVar.Sqldal.ExecuteSql(strList, paraList)
                RZbtb.Rows.Add(V_Row)
                V_Row.AcceptChanges()
                Rrow = V_Row
                Rinsert = False
                Rtdbgrid.MoveLast()
            Else
                strList.Add(Update_String)
                paraList.Add(para)
                Dim paraMeter1() As SqlParameter = {New SqlParameter("@Mz_Code", SqlDbType.Char, 14)}
                paraMeter1(0).Value = V_Row("Mz_Code")
                strList.Add(DeleteDiag)
                paraList.Add(paraMeter1)
                For Each mdlDiag In listDiag
                    Dim paraMeter2() As SqlParameter = {New SqlParameter("@Mz_Code", SqlDbType.Char, 14),
                                                      New SqlParameter("@Diag_Type", SqlDbType.VarChar, 3),
                                                      New SqlParameter("@Diag_Type_Name", SqlDbType.VarChar, 50),
                                                      New SqlParameter("@Diag_Srt_No", SqlDbType.Int),
                                                      New SqlParameter("@Jb_Code", SqlDbType.VarChar, 20),
                                                      New SqlParameter("@Jb_Name", SqlDbType.VarChar, 200),
                                                      New SqlParameter("@Ks_Code", SqlDbType.Char, 6),
                                                      New SqlParameter("@Ys_Code", SqlDbType.Char, 7),
                                                      New SqlParameter("@Diag_Time", SqlDbType.SmallDateTime),
                                                      New SqlParameter("@Created_Jsr", SqlDbType.VarChar, 50),
                                                      New SqlParameter("@Created_Time", SqlDbType.DateTime),
                                                      New SqlParameter("@Update_Jsr", SqlDbType.VarChar, 50),
                                                      New SqlParameter("@Update_Time", SqlDbType.DateTime)}
                    paraMeter2(0).Value = V_Row("Mz_Code")
                    paraMeter2(1).Value = mdlDiag.Diag_Type
                    paraMeter2(2).Value = mdlDiag.Diag_Type_Name
                    paraMeter2(3).Value = mdlDiag.Diag_Srt_No
                    paraMeter2(4).Value = mdlDiag.Jb_Code
                    paraMeter2(5).Value = mdlDiag.Jb_Name
                    paraMeter2(6).Value = mdlDiag.Ks_Code
                    paraMeter2(7).Value = mdlDiag.Ys_Code
                    paraMeter2(8).Value = mdlDiag.Diag_Time
                    paraMeter2(9).Value = mdlDiag.Created_Jsr
                    paraMeter2(10).Value = mdlDiag.Created_Time
                    paraMeter2(11).Value = Common.Tools.IsValueNull(mdlDiag.Update_Jsr)
                    paraMeter2(12).Value = Common.Tools.IsValueNull(mdlDiag.Update_Time)

                    strList.Add(InsertDiag)
                    paraList.Add(paraMeter2)
                Next

                HisVar.HisVar.Sqldal.ExecuteSql(strList, paraList)
                V_Row.AcceptChanges()
            End If
        Catch ex As Exception
            MsgBox(ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
        End Try
        C1TrueDBGrid1.Select()

    End Sub

#End Region

#Region "控件__动作"

#Region "其它__控件"

    Private Sub Comm_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.Click, Comm2.Click, Comm3.Click, comm4.Click, comm5.Click
        Try
            Select Case sender.tag

                Case "保存", "处置单", "完成处方", "数据结算"

                    If Zb_Save() = False Then                        '主表存盘
                        Exit Sub
                    End If

                    If sender.tag = "处置单" Then
                        Dim vform As New 护士站.Hsz_Syk(Rrow, "门诊") '
                        If BaseFunc.BaseFunc.CheckOwnForm(Me, vform) = False Then
                            vform.ShowDialog()
                        End If
                    ElseIf sender.tag = "完成处方" Or sender.tag = "数据结算" Then

                        If My_Dataset.Tables("诊疗项目").Rows.Count = 0 And My_Dataset.Tables("药品卫材").Rows.Count = 0 Then
                            MsgBox("没有药品及诊疗数据不能完成处方或结算！", MsgBoxStyle.OkOnly, "提示")
                            Exit Sub
                        End If
                        Dim V_YpMoney As Decimal = HisVar.HisVar.Sqldal.GetSingle("select Isnull(Sum(Mz_Money),0) from Mz_Yp where Mz_Code='" & lblMz_Code.Text & "'")
                        Dim V_XmMoney As Decimal = HisVar.HisVar.Sqldal.GetSingle("select Isnull(Sum(Mz_Money),0) from Mz_Xm where Mz_Code='" & lblMz_Code.Text & "'")
                        Dim V_Money As Decimal = HisVar.HisVar.Sqldal.GetSingle("select Isnull(Sum(Mz_Money),0) from Mz where Mz_Code='" & lblMz_Code.Text & "'")
                        If V_YpMoney + V_XmMoney <> V_Money Then
                            MsgBox("金额计算有误，请重新点击完成！", MsgBoxStyle.Information, "提示")
                            Exit Sub
                        End If
                        If SingleMzCfZxType1.Text = "正常" Then
                            Dim tips As String = _bllZdMlYp4.GetDynamicKcNotEnoughList(ComboYf1.SelectedValue, lblMz_Code.Text)
                            If Not tips.IsNullOrEmpty() Then
                                MsgBox(tips, MsgBoxStyle.Information, "提示")
                                Exit Sub
                            End If
                        End If
                        HisVar.HisVar.Sqldal.ExecuteSql("Update Mz Set MzCf_Ok='1' Where Mz_Code='" & lblMz_Code.Text & "'")
                        Rrow.Item("MzCf_Ok") = True
                        Rrow.Item("MzCf_Ok1") = "是"

                        If ZTHisPara.PublicConfig.EnablePharmacyAudit = True Then
                            HisVar.HisVar.Sqldal.ExecuteSql("Update Mz Set Sh_Zt='待审' Where Mz_Code='" & lblMz_Code.Text & "'")
                            MsgBox("已经提交到" & Trim(ComboYf1.Text), MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                        End If
                        Me.Close()
                    End If
                Case "取消"
                    Me.Close()

            End Select
        Catch ex As Exception
            MsgBox(ex.Message.ToString, MsgBoxStyle.Information, "提示")
        End Try
    End Sub

    Private Sub C1Command1_Click(ByVal sender As Object, ByVal e As C1.Win.C1Command.ClickEventArgs) Handles C1Command1.Click
        C1ToolBar1.Select()
        If sender.text = "乘" Then
            If C1TrueDBGrid1.RowCount = 0 Then Exit Sub
            If C1TrueDBGrid1.Columns("数量").Value = 0 Then Exit Sub
            Dim m_Select As String
            Dim m_Str As String

            m_Str = ""
            Dim V_Yf_Sl As String = "Yf_Sl" & Mid(ComboYf1.SelectedValue, 6)
            m_Select = "select " & V_Yf_Sl & "-mz_yp.Mz_DfSl*" & C1NumericEdit1.Value & ",Yp_Name from mz_yp,(SELECT  (" & V_Yf_Sl & "-isnull(Mz_Sl,0)-Isnull(Cf_Sl,0)) As " & V_Yf_Sl & ",V_Ypkc.Xx_Code,Yp_Name From  V_Ypkc left join (select Xx_Code,Sum(Mz_Sl) AS Mz_Sl from mz_yp,Mz where Mz.Mz_Code=mz_yp.Mz_Code and Mz_FyQr=0 And  Mz.mz_code<>'" & lblMz_Code.Text & "' and  Mz.Yy_Code='" & HisVar.HisVar.WsyCode & "' group By Xx_Code ) a on V_Ypkc.Xx_Code=a.Xx_Code Left Join (select Xx_Code,Sum(Cf_Sl) AS Cf_Sl from Bl_Cfyp,Bl_Cf where Bl_Cfyp.Cf_Code=Bl_Cf.Cf_Code and Cf_Qr='否' And Bl_Cf.Yy_Code='" & HisVar.HisVar.WsyCode & "' group By Xx_Code ) b on V_Ypkc.Xx_Code=b.Xx_Code where  Yy_Code='" & HisVar.HisVar.WsyCode & "')b where mz_yp.xx_code=b.xx_code and  mz_code='" & lblMz_Code.Text & "' And  (" & V_Yf_Sl & "-mz_yp.Mz_DfSl*" & C1NumericEdit1.Value & ")<0 And Mz_Lb='中草药'"
            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, m_Select, "草药副数", True)

            For Each m_Row In My_Dataset.Tables("草药副数").Rows
                m_Str = m_Str & vbCrLf & m_Row("Yp_Name")
            Next

            If m_Str <> "" And SingleMzCfZxType1.Text = "正常" Then
                m_Str = m_Str & vbCrLf
                MsgBox("中草药:" & m_Str & "库存不足!", vbCritical, "提示")
                Exit Sub
            End If

            If C1TrueDBGrid1.RowCount = 0 Then Exit Sub
            If C1TrueDBGrid1.Columns("数量").Value = 0 Then Exit Sub
            HisVar.HisVar.Sqldal.ExecuteSql("Update Mz_Yp Set Mz_Sl=Mz_DfSl*" & C1NumericEdit1.Value & ",Mz_Money=Mz_DfSl*" & C1NumericEdit1.Value & "*Mz_Dj,Yp_Original_Money=Mz_DfSl*" & C1NumericEdit1.Value & "*Mz_Dj,Mz_Fs=" & C1NumericEdit1.Value & " Where Mz_Code='" & lblMz_Code.Text & "' And Mz_Lb like '%草药%'")
            Call P_Data_Show()
            Call UpdateCfMoney()
        End If
        If sender.text = "除" Then
            If C1TrueDBGrid1.RowCount = 0 Then Exit Sub
            If C1TrueDBGrid1.Columns("数量").Value = 0 Then Exit Sub
            HisVar.HisVar.Sqldal.ExecuteSql("Update Mz_Yp Set Mz_Sl=Mz_Sl/" & C1NumericEdit1.Value & ",Mz_Money=Mz_Sl/" & C1NumericEdit1.Value & "*Mz_Dj,Yp_Original_Money=Mz_Sl/" & C1NumericEdit1.Value & "*Mz_Dj,Mz_Fs=Mz_Fs/ " & C1NumericEdit1.Value & " Where Mz_Code='" & lblMz_Code.Text & "' And Mz_Lb like '%草药%'")
            Call P_Data_Show()
            Call UpdateCfMoney()
        End If
    End Sub

    Private Sub C1Command4_Click(ByVal sender As System.Object, ByVal e As C1.Win.C1Command.ClickEventArgs) Handles C1Command4.Click, C1Command5.Click, C1Command9.Click

        Select Case sender.text
            Case "增加疾病"
                'Dim list =listDiag.Where(Function (p) p.Jb_Code=ComboJb1.SelectedValue)
                If listDiag.Where(Function(p) p.Jb_Code = ComboJb1.SelectedValue).Count() > 0 Then
                    MsgBox("请勿选择重复疾病！", MsgBoxStyle.Information, "提示：")
                    ComboJb1.Select()
                    Exit Sub
                End If

                If ComboJb1.SelectedValue = "" Then
                    MsgBox("请选择要增加的疾病！", MsgBoxStyle.Information, "提示：")
                    ComboJb1.Select()
                    Exit Sub
                End If
                If ComDiagType.Text.Trim = "" Then
                    MsgBox("请选择要增加的诊断类型！", MsgBoxStyle.Information, "提示：")
                    ComDiagType.Select()
                    Exit Sub
                End If
                If (CustomControl.Func.NotAllowEmpty(ComboYs1)) Then
                    Exit Sub
                End If
                If (CustomControl.Func.NotAllowEmpty(ComboKs1)) Then
                    Exit Sub
                End If
                If Label20.Text = "" Then
                    Label20.Text = ComboJb1.Columns("Jb_Name").Value & ""
                Else
                    Label20.Text = Label20.Text & "," & ComboJb1.Columns("Jb_Name").Value & ""
                End If

                Dim mdlDiag As MdlMz_Diag = New MdlMz_Diag
                mdlDiag.Mz_Code = lblMz_Code.Text.Trim()
                mdlDiag.Diag_Type = ComDiagType.SelectedValue + ""
                mdlDiag.Diag_Type_Name = ComDiagType.Text + ""
                mdlDiag.Diag_Srt_No = listDiag.Count + 1
                mdlDiag.Jb_Code = ComboJb1.SelectedValue + ""
                mdlDiag.Jb_Name = ComboJb1.Text.Trim()
                mdlDiag.Ks_Code = ComboKs1.SelectedValue + ""
                mdlDiag.Ys_Code = ComboYs1.SelectedValue + ""
                mdlDiag.Diag_Time = Now()
                mdlDiag.Created_Jsr = ZTHisVar.Var.JsrName
                mdlDiag.Created_Time = Now()
                mdlDiag.Update_Jsr = ""
                mdlDiag.Update_Time = Nothing
                listDiag.Add(mdlDiag)

            Case "撤销"
                Dim I As Integer = Label20.Text.LastIndexOf(",")
                listDiag.RemoveAt(listDiag.Count - 1)
                If I = -1 Then
                    Label20.Text = ""
                Else
                    Label20.Text = Mid(Label20.Text, 1, I)
                End If
            Case "清除所有疾病"
                listDiag.Clear()
                Label20.Text = ""
        End Select
        ComboJb1.SelectedIndex = -1
        ComboJb1.Select()
    End Sub

    Private Sub C1Command7_Click(ByVal sender As System.Object, ByVal e As C1.Win.C1Command.ClickEventArgs) Handles C1Command7.Click
        If XmTextBox.Text.Trim = "" Then
            Beep()
            MsgBox("门诊患者姓名不能为空!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
            XmTextBox.Select()
            Exit Sub
        End If

        If Zb_Save() = False Then                        '主表存盘
            Exit Sub
        End If

        'Dim vform As New Ys_Cf4(Me, Rrow)
        'If BaseFunc.BaseFunc.CheckOwnForm(Me, vform) = False Then
        '    vform.ShowDialog()
        'End If
        'ComboJb1.Select()
        Dim bllmz As New BLL.BllMz
        Dim mdlmz As New MdlMz
        mdlmz = bllmz.GetModel(Rrow("Mz_Code"))
        If mdlmz Is Nothing Then
            MessageBox.Show("请先保存处方!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Exit Sub
        End If
        Dim blldzbl As New BLL.BllDzbL
        Dim mdldzbl As New MdlDzbL
        If ZTHisPara.PublicConfig.RegLinkEMR Then
            mdldzbl = blldzbl.GetModelByGh(Rrow("Mz_Code"), Rrow("MzGh_Code"))
        Else
            mdldzbl = blldzbl.GetModel(Rrow("Mz_Code"))
        End If
        Dim fBl As New ZTHisOutpatient.MzBlMx(mdlmz, mdldzbl, Nothing)
        fBl.Tag = mdlmz.Mz_Code
        BaseFunc.BaseFunc.addTabControl(fBl, "门诊电子病历-" + mdlmz.Ry_Name)
    End Sub

#End Region


    Private Sub JbComobo1_KeyDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles ComboJb1.KeyDown
        If e.KeyValue = 13 Then

            C1Command4.PerformClick()
        End If
    End Sub

#Region "Combo2动作"

    Private Sub C1Combo2_RowChange(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1Combo2.RowChange
        If C1Combo2.WillChangeToValue = "" Then
            XmTextBox.Text = ""
            SfzhTextBox.Text = ""
            SingleSex1.SelectedValue = 0
            NlC1NumericEdit.Value = Nothing
            AddressTextBox.Text = ""
            ComboBxlb1.SelectedValue = 0
            YlCodeTextBox.Text = ""
            ComboKs1.SelectedValue = -1
            TxtTel.Text = ""
            DateRyBirthday.Value = Nothing
            comboIdentityType.SelectedValue = "0001"
        Else
            XmTextBox.Text = C1Combo2.Columns("Ry_Name").Value & ""
            SfzhTextBox.Text = C1Combo2.Columns("Ry_Sfzh").Value & ""
            SingleSex1.Text = C1Combo2.Columns("Ry_Sex").Value & ""
            NlC1NumericEdit.Value = C1Combo2.Columns("Ry_Age").Value & ""
            AddressTextBox.Text = C1Combo2.Columns("Ry_Address").Value & ""
            ComboBxlb1.SelectedValue = C1Combo2.Columns("Bxlb_Code").Value & ""
            YlCodeTextBox.Text = C1Combo2.Columns("Ry_YlCode").Value & ""
            ComboKs1.SelectedValue = C1Combo2.Columns("Ks_Code").Value & ""
            TxtTel.Text = C1Combo2.Columns("Ry_Tel").Value
            DateRyBirthday.Value = C1Combo2.Columns("Ry_Birthday").Value
            comboIdentityType.SelectedValue = C1Combo2.Columns("IdentityType").Value

        End If
    End Sub

    Private Sub C1Combo2_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles C1Combo2.KeyUp

        If (e.KeyValue > 47 And e.KeyValue < 58) Or (e.KeyValue > 96 And e.KeyValue < 123) Or (e.KeyValue > 64 And e.KeyValue < 91) Or (e.KeyValue = 8) Or (e.KeyValue = 189) Or (e.KeyValue = 190) Then
            Dim s As String = C1Combo2.Text
            If C1Combo2.Text = "" Then
                C1Combo2.DataSource.RowFilter = ""
            Else
                C1Combo2.DataSource.RowFilter = "Ry_Jc like '*" & C1Combo2.Text & "*'"
            End If
            If (e.KeyValue = 8) Then
                If C1Combo2.Text <> "" Then
                    C1Combo2.DroppedDown = False
                    C1Combo2.DroppedDown = True
                Else
                    C1Combo2.DroppedDown = False
                End If
            End If

            C1Combo2.Text = s
            C1Combo2.SelectionStart = C1Combo2.Text.Length
            C1Combo2.SelectionLength = 0
        End If

    End Sub

    Private Sub C1Combo2_KeyDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles C1Combo2.KeyDown
        If e.KeyValue = 13 Then
            If C1Combo2.WillChangeToIndex < 1 Then
                If (CType(C1Combo2.DataSource, DataView).Count) = 0 Then
                    MsgBox("患者: '" + Me.C1Combo2.Text + "' 不存在！", MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
                    System.Windows.Forms.SendKeys.Send("^{A}")
                    Exit Sub
                End If
                C1Combo2.SelectedIndex = -1
                C1Combo2.SelectedIndex = 0
            Else
                Dim index As Integer
                index = C1Combo2.WillChangeToIndex
                C1Combo2.SelectedIndex = -1
                C1Combo2.SelectedIndex = index
            End If
            e.Handled = True
            System.Windows.Forms.SendKeys.Send("{Tab}")
        End If
    End Sub
#End Region
    Private Sub DoctorCombo_RowChange(ByVal sender As Object, ByVal e As System.EventArgs) Handles ComboYs1.RowChange
        If PublicConfig.MzYszChangeYs Then
            If ComboYs1.WillChangeToValue = "" Then
            Else
                ComboKs1.SelectedValue = ComboYs1.Columns("Ks_Code").Value
            End If
        End If
    End Sub
#Region "Combo6动作"

    Private Sub C1Combo6_RowChange(ByVal sender As Object, ByVal e As System.EventArgs) Handles ComboYf1.RowChange
        If ComboYf1.WillChangeToValue = "" Then

        ElseIf ComboYf1.WillChangeToValue = iniOperate.iniopreate.GetINI("Personal", "默认药房", "", HisVar.HisVar.Parapath & "\Config.Ini") Then
            iniOperate.iniopreate.WriteINI("Personal", "默认药房", ComboYf1.SelectedValue, HisVar.HisVar.Parapath & "\Config.Ini")
        Else
            '
            If My_Dataset.Tables("药品卫材") IsNot Nothing Then
                If My_Dataset.Tables("药品卫材").Rows.Count <> 0 Then
                    ComboYf1.SelectedValue = iniOperate.iniopreate.GetINI("Personal", "默认药房", "", HisVar.HisVar.Parapath & "\Config.Ini")
                    MsgBox("请先删除药品再更换药房！", MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
                    Exit Sub
                End If
            End If
            iniOperate.iniopreate.WriteINI("Personal", "默认药房", ComboYf1.SelectedValue, HisVar.HisVar.Parapath & "\Config.Ini")
        End If
    End Sub


#End Region

#Region "DBGrid动作"

    Private Sub C1TrueDBGrid1_MouseDown(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles C1TrueDBGrid1.MouseDown, C1TrueDBGrid2.MouseDown
        If e.Button = Windows.Forms.MouseButtons.Right Then
            If Trim(XmTextBox.Text) = "" Then
                Beep()
                MsgBox("病人姓名不能为空，按任意键返回!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                XmTextBox.Select()

            ElseIf ComboKs1.SelectedValue = "" Then
                Beep()
                MsgBox("科室名称不能为空,按任意键返回!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                ComboKs1.Select()
            ElseIf ComboYf1.SelectedValue = "" Then
                Beep()
                MsgBox("药房名称不能为空,按任意键返回!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                ComboYf1.Select()
            Else
                Call Cb_Edit()
            End If
        End If
    End Sub


    Private Sub C1TrueDBGrid1_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles C1TrueDBGrid1.KeyDown, C1TrueDBGrid2.KeyDown

        Select Case e.KeyCode
            Case Keys.Return
                If XmTextBox.Text.Trim = "" Then
                    Beep()
                    MsgBox("门诊患者姓名不能为空!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    XmTextBox.Select()
                    Exit Sub
                End If

                If SfzhTextBox.Text <> "" Then
                    If Len(SfzhTextBox.Text) <> 15 And Len(SfzhTextBox.Text) <> 18 Then
                        MsgBox("请输入有效身份证号！", MsgBoxStyle.Critical, "提示")
                        SfzhTextBox.Select()
                        Exit Sub
                    End If
                    Dim R As Regex
                    R = New Regex("^\d{18}$|^\d{17}[a-zA-Z]{1}$|^\d{15}$")
                    If R.IsMatch(SfzhTextBox.Text) = False Then
                        MsgBox("请输入有效身份证号！", MsgBoxStyle.Critical, "提示")
                        SfzhTextBox.Select()
                        Exit Sub
                    End If
                End If


                If ComboKs1.SelectedValue = "" Then
                    Beep()
                    MsgBox("科室不能为空!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    ComboKs1.Select()
                    Exit Sub
                End If

                If ComboYf1.SelectedValue = "" Then
                    Beep()
                    MsgBox("药房不能为空!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    ComboYf1.Select()
                    Exit Sub
                End If

                Call Cb_Edit()

            Case Keys.Delete

                If RadioButton1.Checked = True Then
                    If (C1TrueDBGrid1.Row + 1) > C1TrueDBGrid1.RowCount Then
                    Else

                        Cb_Row_Yp = Cb_Cm_Yp.List(C1TrueDBGrid1.Row).Row
                        If HisVar.HisVar.Sqldal.GetSingle("select count (*) from mz_czd where mz_code='" & lblMz_Code.Text & "' and xx_code='" & Cb_Row_Yp.Item("XX_code") & "'") > 0 Then
                            MsgBox("该药品已经分配处置单，请先删除处置单中相关数据，再进行删除！")
                            Exit Sub
                        End If
                        If MsgBox("是否删除:" + Cb_Row_Yp.Item("Yp_Name") + " ", MsgBoxStyle.Information + MsgBoxStyle.OkCancel, "提示:") = MsgBoxResult.Cancel Then Exit Sub
                        HisVar.HisVar.Sqldal.ExecuteSql("Delete from Mz_Yp where Mz_Id='" & Cb_Row_Yp.Item("Mz_Id") & "'")
                        C1TrueDBGrid1.Delete()
                        Cb_Row_Yp.AcceptChanges()
                    End If
                Else
                    If (C1TrueDBGrid2.Row + 1) > C1TrueDBGrid2.RowCount Then
                    Else
                        Cb_Row_Xm = Cb_Cm_Xm.List(C1TrueDBGrid2.Row).Row
                        If (Cb_Row_Xm("Templet_Code") & "").ToString().Trim() = "" Then
                            If MsgBox("是否删除:" + Cb_Row_Xm.Item("Xm_Name") + " ", MsgBoxStyle.Information + MsgBoxStyle.OkCancel, "提示:") = MsgBoxResult.Cancel Then Exit Sub
                            HisVar.HisVar.Sqldal.ExecuteSql("Delete from Mz_Xm where Mz_Id='" & Cb_Row_Xm.Item("Mz_Id") & "'")
                            C1TrueDBGrid2.Delete()
                            Cb_Row_Xm.AcceptChanges()
                        Else
                            If MsgBox(" " + Cb_Row_Xm.Item("Xm_Name") + " 由项目模板生成，是否删除该模板下所有项目？", MsgBoxStyle.Information + MsgBoxStyle.OkCancel, "提示:") = MsgBoxResult.Cancel Then Exit Sub
                            HisVar.HisVar.Sqldal.ExecuteSql("Delete from Mz_Xm where Mz_Code='" & Cb_Row_Xm.Item("Mz_Code") & "' and Templet_Code='" & Cb_Row_Xm.Item("Templet_Code") & "'")
                            P_Data_Show()

                        End If
                    End If
                End If

                Call F_Sum()
                Call UpdateCfMoney()


        End Select

    End Sub


#End Region

#End Region

#Region "从表__编辑"

    Private Sub Init_Data1()     '从表数据

        Dim Str_Insert As String = "Insert Into Mz_Xm(Yy_Code,Mz_Code,Xm_Code,Mz_Sl,Mz_Dj,Mz_Money,Xm_Discount,Xm_Original_Money,Mz_Lb,Templet_Code)Values(@Yy_Code,@Mz_Code,@Xm_Code,@Mz_Sl,@mz_Dj,@Mz_Money,@Xm_Discount,@Xm_Original_Money,@Mz_Lb,@Templet_Code)"
        Dim Str_Update As String = "Update Mz_Xm Set Xm_Code=@Xm_Code,Mz_Sl=@Mz_Sl,Mz_Dj=@Mz_Dj,Mz_Money=@Mz_Money,Xm_Discount=@Xm_Discount,Xm_Original_Money=@Xm_Original_Money,Mz_Lb=@Mz_Lb Where Mz_Id=@Old_Mz_Id "
        With My_Adapter

            .InsertCommand = New SqlCommand(Str_Insert, My_Cn)
            With .InsertCommand.Parameters
                .Add("@Yy_Code", SqlDbType.VarChar, 4)
                .Add("@Mz_Code", SqlDbType.VarChar, 14)                 '出库编码
                .Add("@Xm_Code", SqlDbType.VarChar, 12)                 '类别编码
                .Add("@Mz_Sl", SqlDbType.Decimal)                   '入库数量
                .Add("@Mz_Dj", SqlDbType.Decimal)                   '入库单价
                .Add("@Mz_Money", SqlDbType.Decimal, 10, 2)
                .Add("@Xm_Discount", SqlDbType.Decimal, 10, 2)
                .Add("@Xm_Original_Money", SqlDbType.Decimal, 10, 2)
                .Add("@Mz_Lb", SqlDbType.VarChar, 50)
                .Add("@Templet_Code", SqlDbType.VarChar, 8)
            End With

            .UpdateCommand = New SqlCommand(Str_Update, My_Cn)
            With .UpdateCommand.Parameters
                .Add("@Xm_Code", SqlDbType.VarChar, 12)                 '类别编码
                .Add("@Mz_Sl", SqlDbType.Decimal)                   '入库数量
                .Add("@Mz_Dj", SqlDbType.Decimal)                   '入库单价
                .Add("@Mz_Money", SqlDbType.Decimal, 10, 2)
                .Add("@Xm_Discount", SqlDbType.Decimal, 10, 2)
                .Add("@Xm_Original_Money", SqlDbType.Decimal, 10, 2)
                .Add("@Mz_Lb", SqlDbType.VarChar, 50)
                .Add("@Old_Mz_Id", SqlDbType.Int, 4, "Mz_Id")
            End With

        End With
    End Sub

#End Region

#Region "自定义函数"

    Public Overrides Sub F_Sum()
        Dim V_YpWc As Double = IIf(My_Dataset.Tables("药品卫材").Compute("Sum(Mz_Money)", "") Is DBNull.Value, 0, My_Dataset.Tables("药品卫材").Compute("Sum(Mz_Money)", ""))
        Dim V_Xm As Double = IIf(My_Dataset.Tables("诊疗项目").Compute("Sum(Mz_Money)", "") Is DBNull.Value, 0, My_Dataset.Tables("诊疗项目").Compute("Sum(Mz_Money)", ""))
        T_Label5.Text = "费用总额：" & Trim(Format(V_YpWc + V_Xm, "########0.00") + "元") & "   其中  药品卫材：" & Trim(Format(V_YpWc, "########0.00") + "元") & "  诊疗项目：" & Trim(Format(V_Xm, "#######0.00") + "元")
    End Sub
    Private Sub UpdateCfMoney()
        Dim mdlMz As New MdlMz
        mdlMz = _bllMz.GetModel(Rrow("Mz_Code"))
        mdlMz.Mz_YpMoney = _bllMzYp.GetCfYpMoney(Rrow("Mz_Code"))
        mdlMz.Mz_XmMoney = _bllMzXm.GetCfXmMoney(Rrow("Mz_Code"))
        mdlMz.Mz_Money = _bllMzYp.GetCfYpMoney(Rrow("Mz_Code")) + _bllMzXm.GetCfXmMoney(Rrow("Mz_Code"))
        _bllMz.UpdateCfMoney(mdlMz)
    End Sub
    Private Sub GridMove(moveType As String)
        If C1TrueDBGrid1.RowCount = 0 Then
            Return
        End If
        Select Case moveType
            Case "最前"
                C1TrueDBGrid1.MoveFirst()
                Exit Select
            Case "上移"
                C1TrueDBGrid1.MovePrevious()
                Exit Select
            Case "下移"
                C1TrueDBGrid1.MoveNext()
                Exit Select
            Case "最后"
                C1TrueDBGrid1.MoveLast()
                Exit Select
            Case Else
                Dim index As Integer
                If Integer.TryParse(moveType, index) Then
                    C1TrueDBGrid1.Row = index
                End If
                Exit Select
        End Select
        Call F_Sum()
        Call UpdateCfMoney()
    End Sub
    Private Sub GridMove2(moveType As String)
        If C1TrueDBGrid2.RowCount = 0 Then
            Return
        End If
        Select Case moveType
            Case "最前"
                C1TrueDBGrid2.MoveFirst()
                Exit Select
            Case "上移"
                C1TrueDBGrid2.MovePrevious()
                Exit Select
            Case "下移"
                C1TrueDBGrid2.MoveNext()
                Exit Select
            Case "最后"
                C1TrueDBGrid2.MoveLast()
                Exit Select
            Case Else
                Dim index As Integer
                If Integer.TryParse(moveType, index) Then
                    C1TrueDBGrid2.Row = index
                End If
                Exit Select
        End Select
        Call F_Sum()
        Call UpdateCfMoney()
    End Sub
    Private Sub Cb_Edit()
        '判断主表是否存在

        If Zb_Save() = False Then                        '主表存盘
            Exit Sub
        End If

        If HisVar.HisVar.Sqldal.GetSingle("Select Isnull(Mz_Print,0)|Isnull(MzCf_Ok,0)|Isnull(Mz_FyQr,0) from Mz where  Mz_Code='" & Rrow.Item("Mz_Code") & "'") = True Then
            MsgBox("处方状态有误，请先关闭窗口，刷新后从新打开进行明细录入！", MsgBoxStyle.Information, "提示")
            Exit Sub
        End If

        If RadioButton1.Checked = True Then
            If (C1TrueDBGrid1.Row + 1) > C1TrueDBGrid1.RowCount Then
                V_Insert = True
            Else
                V_Insert = False
                Cb_Row_Yp = Cb_Cm_Yp.List(C1TrueDBGrid1.Row).Row
                If HisVar.HisVar.Sqldal.GetSingle("select count (*) from mz_czd where mz_code='" & lblMz_Code.Text & "' and xx_code='" & Cb_Row_Yp.Item("XX_code") & "'") > 0 Then
                    MsgBox("该药品已经分配处置单，请先删除处置单中相关数据，再进行修改！", MsgBoxStyle.Information, "提示")
                    Exit Sub
                End If
            End If

            'Dim vform As New Xs_Mz31(Me, V_Insert, Cb_Row_Yp, My_Dataset.Tables("药品卫材"), C1TrueDBGrid1, lblMz_Code.Text, ComboYf1.SelectedValue)
            'If BaseFunc.BaseFunc.CheckOwnForm(Me, vform) = False Then
            '    vform.ShowDialog()
            'End If
            Dim vform As New ZTHisOutpatient.MzCfYp(lblMz_Code.Text, ComboYf1.SelectedValue, V_Insert, Cb_Row_Yp, My_Dataset.Tables("药品卫材"), System.[Enum].Parse(GetType(ZTHisEnum.MzCfZxType), SingleMzCfZxType1.Text))
            vform.MyTransmitTxt = _transmitTxt
            vform.ShowDialog()
            Call P_Data_Show()
            C1TrueDBGrid1.Select()

        Else
            If (C1TrueDBGrid2.Row + 1) > C1TrueDBGrid2.RowCount Then
                V_Insert = True
            Else
                V_Insert = False
                Cb_Row_Xm = Cb_Cm_Xm.List(C1TrueDBGrid2.Row).Row
            End If

            If V_Insert = False Then
                If Not String.IsNullOrWhiteSpace((Cb_Row_Xm("Templet_Code") + "").ToString().Trim()) Then
                    MsgBox("模板里的项目不允许修改", MsgBoxStyle.Information, "提示")
                    Exit Sub
                End If
            End If

            'Dim vform As New Xs_Mz34(Me, V_Insert, Rrow, Cb_Row_Xm, My_Dataset.Tables("诊疗项目"), C1TrueDBGrid2, My_Adapter)
            'If BaseFunc.BaseFunc.CheckOwnForm(Me, vform) = False Then
            '    vform.ShowDialog()
            'End If
            Dim vform As New MzCfXm(lblMz_Code.Text, ComboYf1.SelectedValue + "", V_Insert, Cb_Row_Xm, My_Dataset.Tables("诊疗项目"))
            vform.MyTransmitTxt = _transmitTxt2
            vform.ShowDialog()
            Call P_Data_Show()
            C1TrueDBGrid2.Select()
        End If
        Call UpdateCfMoney()
    End Sub

    Private Function F_MaxCode(ByVal V_MaxCode As String)   '出库编码
        Dim My_Cc As New BaseClass.C_Cc()
        Dim My_Cd As New BaseClass.C_Cc()
        Dim V_Code As String = V_MaxCode

        My_Cc.Get_MaxCode("Mz", "Mz_Code", 14, "Left(Mz_Code,10)", HisVar.HisVar.WsyCode & V_MaxCode)
        My_Cd.Get_MaxCode("Mz_Sum", "Mz_Code", 14, "Left(Mz_Code,10)", HisVar.HisVar.WsyCode & V_MaxCode)

        If My_Cc.编码 <= My_Cd.编码 Then
            F_MaxCode = My_Cd.编码
        Else
            F_MaxCode = My_Cc.编码
        End If

        If Mid(F_MaxCode, 1, 6) = "000000" Then F_MaxCode = V_MaxCode + Mid(My_Cc.编码, 7)
        Return F_MaxCode
    End Function

    Private Sub GenCzd(ByRef mdlMzInfusion As MdlMz_Infusion, ByRef listGroup1 As List(Of MdlMz_InfusionGroup1), ByRef listGroup2 As List(Of MdlMz_InfusionGroup2), ByRef listMzCzd As List(Of MdlMz_Czd))
        mdlMzInfusion.Mz_Code = lblMz_Code.Text
        For Each mdl As MdlMz_Czd In listMzCzd
            mdl.Mz_Code = lblMz_Code.Text
        Next
        For Each mdl As MdlMz_InfusionGroup1 In listGroup1
            mdl.Mz_Code = lblMz_Code.Text
        Next
        For Each mdl As MdlMz_InfusionGroup2 In listGroup2
            mdl.Mz_Code = lblMz_Code.Text
        Next
    End Sub

#End Region

#Region "中西药、草药、卫材切换"

    Private Sub RadioButton1_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles RadioButton1.CheckedChanged, RadioButton4.CheckedChanged
        If Me.Visible = False Then Exit Sub
        If Rinsert = True Then
            lblMz_Code.Text = F_MaxCode(Format(Rdate, "yyMMdd"))
        End If
        If RadioButton1.Checked = True Then
            '初始化TDBGrid
            SplitContainer1.Panel2Collapsed = True '隐藏诊疗, 显示药品
            C1Command1.Enabled = True
            C1NumericEdit1.Enabled = True
            Call P_Data_Show()
            C1TrueDBGrid1.Select()
        End If

        If RadioButton4.Checked = True Then
            Call Init_Data1()
            '初始化TDBGrid
            SplitContainer1.Panel1Collapsed = True
            C1Command1.Enabled = False
            C1NumericEdit1.Enabled = False
            Call P_Data_Show()
            C1TrueDBGrid2.Select()
        End If
    End Sub
#End Region

    Private Sub Key_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles C1Combo2.KeyPress
        If e.KeyChar <> Chr(Keys.Return) Then Exit Sub
        e.Handled = True
        System.Windows.Forms.SendKeys.Send("{Tab}")
    End Sub

#Region "输入法设置"
    '中文
    Private Sub C1TextBox1_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs)
        InputLanguage.CurrentInputLanguage = HisVar.HisVar.InputCode
    End Sub

    Private Sub C1Numeric1_KeyDown(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ComboJb1.GotFocus
        InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages(0)
    End Sub

#End Region


    Private Sub BtnPrintSqd_Click(sender As Object, e As EventArgs) Handles BtnPrintSqd.Click
        If Rrow IsNot Nothing Then
            Dim print As New ZTHisLis.Print
            print.PrintMzYjsqd(Rrow("Mz_Code"))
        End If
    End Sub
    Private Sub BtnSaveTemplate_Click(sender As Object, e As EventArgs) Handles BtnSaveTemplate.Click
        Dim mdlmztemplate As New MdlMz_Template
        mdlmztemplate = Common.DataTableToList.ToModel(Of MdlMz_Template)(Rrow)
        Dim list As New List(Of MdlMz_TemplateMx)
        list = Common.DataTableToList.ToList(Of MdlMz_TemplateMx)(My_Dataset.Tables("药品卫材"))
        For Each yp As MdlMz_TemplateMx In list
            yp.Type = "Yp"
            yp.Mx_Code = yp.Xx_Code.Substring(0, 11)
            yp.Yf_Code = ComboYf1.SelectedValue + ""
        Next
        For Each xmrow As DataRow In My_Dataset.Tables("诊疗项目").Rows
            Dim xm As New MdlMz_TemplateMx
            xm.Type = "Xm"
            xm.Yp_Name = xmrow("Xm_Name")
            xm.Mx_Code = xmrow("Xm_Code")
            xm.Mz_Sl = xmrow("Mz_Sl")
            xm.Mx_Gg = xmrow("Xm_Dw")
            list.Add(xm)
        Next
        Dim frm As New ZTHisOutpatient.MzTemplate2(mdlmztemplate, Nothing, MzData1.CfTemplatedt, list)
        frm.ShowDialog()
    End Sub
    Private Sub ToolBarReadCard1_CmdReadCardClick(patientInfo As DTO.DtoPatientInfo) Handles ToolBarReadCard1.CmdReadCardClick
        If HisPara.PublicConfig.MzGh = "是" Then
            Dim row As DataRow()
            row = My_Dataset.Tables("挂号人员").Select($"Ry_Sfzh='{patientInfo.ID_No}'")
            If row.Length = 0 Then
                MsgBox("患者没有进行挂号！", vbInformation + vbOKOnly + vbDefaultButton1, "提示:")
                Return
            End If
            C1Combo2.SelectedValue = row("Mz_Code")
        Else
            XmTextBox.Text = patientInfo.Pat_Name
        End If
        Dim chs2Spell As New Common.Chs2Spell()
        'JcLabel1.Text = chs2Spell.GetPy(XmTextBox.Text)
        SfzhTextBox.Text = patientInfo.ID_No
        SingleSex1.Text = If(patientInfo.Pat_Sex = "1", "男", "女")
        AddressTextBox.Text = patientInfo.Pat_Address
        'DtcboNation.Text = patientInfo.Pat_Nation
        Common.ModelTools.ToNewModel(patientInfo, _patientInfo)
        Dim dt As DateTime
        If DateTime.TryParse(SfzhTextBox.Text.Substring(6, 4) + "-" + SfzhTextBox.Text.Substring(10, 2) + "-" + SfzhTextBox.Text.Substring(12, 2), dt) Then
            DateRyBirthday.Value = dt
            NlC1NumericEdit.Value = DateTime.Now.Year - dt.Year
        End If
        If patientInfo.mdtrt_cert_type = "03" Then
            YlCodeTextBox.Text = patientInfo.mdtrt_cert_no
        End If
        Dim bxlbdt As DataTable
        Dim bllbxlb As New BLL.BllZd_Bxlb
        If patientInfo.insutype = "310" Then
            bxlbdt = bllbxlb.GetList("BxLb_Name='城镇职工'").Tables(0)
            If bxlbdt.Rows.Count > 0 Then
                ComboBxlb1.SelectedValue = bxlbdt.Rows(0).Item("Bxlb_Code")
            End If
        Else
            bxlbdt = bllbxlb.GetList("BxLb_Name in ('城镇居民','城乡居民')").Tables(0)
            If bxlbdt.Rows.Count > 0 Then
                ComboBxlb1.SelectedValue = bxlbdt.Rows(0).Item("Bxlb_Code")
            End If
        End If
        If Not String.IsNullOrEmpty(patientInfo.balc) Then
            lblBalc.Text = "医保余额：" & patientInfo.balc
        Else
            lblBalc.Text = ""
        End If
        ComboJb1.Select()
    End Sub

    Private Sub SfzhTextBox_Validated(sender As Object, e As EventArgs) Handles SfzhTextBox.Validated
        If String.IsNullOrEmpty(_patientInfo.Pat_Name) And Common.RegexHelper.IsIDCard(SfzhTextBox.Text) Then
            Dim dt As DateTime
            If DateTime.TryParse(SfzhTextBox.Text.Substring(6, 4) + "-" + SfzhTextBox.Text.Substring(10, 2) + "-" + SfzhTextBox.Text.Substring(12, 2), dt) Then
                DateRyBirthday.Value = dt
                NlC1NumericEdit.Value = DateTime.Now.Year - dt.Year
            End If
        End If
    End Sub

    Private Sub MzData1_LsCfMouseDoubleClick(mdlMzYp As Model.MdlMz_Yp, mdlMzXm As Model.MdlMz_Xm) Handles MzData1.LsCfMouseDoubleClick
        If Trim(XmTextBox.Text) = "" Then
            Beep()
            MsgBox("病人姓名不能为空，按任意键返回!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
            XmTextBox.Select()
        ElseIf ComboKs1.SelectedValue = "" Then
            Beep()
            MsgBox("科室名称不能为空,按任意键返回!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
            ComboKs1.Select()
        ElseIf ComboYf1.SelectedValue = "" Then
            Beep()
            MsgBox("药房名称不能为空,按任意键返回!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
            ComboYf1.Select()
        Else
            If Zb_Save() = False Then                        '主表存盘
                Exit Sub
            End If
            Dim bllMz_Yp = New BllMz_Yp
            Dim bllMz_Xm = New BllMz_Xm
            If mdlMzYp IsNot Nothing Then
                mdlMzYp.Mz_Code = lblMz_Code.Text
                mdlMzYp.Group_Id = bllMz_Yp.GetGroupId(mdlMzYp.Mz_Code)
                Dim id As Integer = bllMz_Yp.Add(mdlMzYp)
                MzData1.DicMz_Id(mdlMzYp.Mz_Id) = id
            End If
            If mdlMzXm IsNot Nothing Then
                mdlMzXm.Mz_Code = lblMz_Code.Text
                bllMz_Xm.Add(mdlMzXm)
            End If
            P_Data_Show()
            F_Sum()
            UpdateCfMoney()
        End If
    End Sub

    Private Sub MzData1_PatientInfoClick(mdlMz As Model.MdlMz) Handles MzData1.PatientInfoClick
        XmTextBox.Text = mdlMz.Ry_Name
        SfzhTextBox.Text = mdlMz.Ry_Sfzh
        AddressTextBox.Text = mdlMz.Ry_Address
        TxtTel.Text = mdlMz.Ry_Tell
    End Sub

    Private Sub MzData1_CfTemplateMouseDoubleClick(Yf_Code As String, listYp As List(Of MdlMz_Yp), listXm As List(Of MdlMz_Xm)) Handles MzData1.CfTemplateMouseDoubleClick
        If String.IsNullOrEmpty(Yf_Code) = False Then
            ComboYf1.SelectedValue = Yf_Code
        End If
        If Trim(XmTextBox.Text) = "" Then
            Beep()
            MsgBox("病人姓名不能为空，按任意键返回!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
            XmTextBox.Select()
        ElseIf ComboKs1.SelectedValue = "" Then
            Beep()
            MsgBox("科室名称不能为空,按任意键返回!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
            ComboKs1.Select()
        ElseIf ComboYf1.SelectedValue = "" Then
            Beep()
            MsgBox("药房名称不能为空,按任意键返回!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
            ComboYf1.Select()
        Else
            If Zb_Save() = False Then                        '主表存盘
                Exit Sub
            End If
            Dim bllMz_Yp = New BllMz_Yp
            Dim bllMz_Xm = New BllMz_Xm
            For Each mdlMzYp As MdlMz_Yp In listYp
                mdlMzYp.Mz_Code = lblMz_Code.Text
                mdlMzYp.Group_Id = bllMz_Yp.GetGroupId(mdlMzYp.Mz_Code)
                bllMz_Yp.Add(mdlMzYp)
            Next
            For Each mdlMzXm As MdlMz_Xm In listXm
                mdlMzXm.Mz_Code = lblMz_Code.Text
                bllMz_Xm.Add(mdlMzXm)
            Next
            P_Data_Show()
            F_Sum()
            UpdateCfMoney()
        End If
    End Sub

    Private Sub C1DockingTab1_AutoHidingChanged(sender As Object, e As EventArgs) Handles C1DockingTab1.AutoHidingChanged
        Dim autohide As Boolean = C1DockingTab1.AutoHiding
        Common.WinFormVar.Var.IniFileHelper.IniWriteValue("参数", "门诊医生站辅助框", autohide)
    End Sub

    Private Sub SingleMzCfZxType1_RowChange(sender As Object, e As EventArgs) Handles SingleMzCfZxType1.RowChange
        If (Not String.IsNullOrEmpty(SingleMzCfZxType1.Text)) Then MzData1.MzCfZxType = System.[Enum].Parse(GetType(ZTHisEnum.MzCfZxType), SingleMzCfZxType1.Text)
    End Sub
End Class