﻿/**  版本信息模板在安装目录下，可自行修改。
* D_Emr_Mb.cs
*
* 功 能： N/A
* 类 名： D_Emr_Mb
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/8/10 11:31:32   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using System.Collections.Generic;

namespace DAL
{
	/// <summary>
	/// 数据访问类:D_Emr_Mb
	/// </summary>
	public partial class D_Emr_Mb
	{
		public D_Emr_Mb()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string Mb_Code)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from Emr_Mb");
			strSql.Append(" where Mb_Code=@Mb_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Mb_Code", SqlDbType.Char,10)			};
			parameters[0].Value = Mb_Code;

			return HisVar.HisVar.Sqldal.Exists(strSql.ToString(),parameters);
		}

        public string MaxCode()
        {
            string Max = HisVar.HisVar.Sqldal.F_MaxCode("SELECT MAX(Mb_Code) FROM dbo.Emr_Mb", 10);
            return Max;
        }
        public DataSet GetListmblb(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select * ");
            strSql.Append(" FROM Emr_Mb,Emr_Mblb ");
            strSql.Append(" where Emr_Mb.Mblb_Code=Emr_Mblb.Mblb_Code ");
            if (strWhere.Trim() != "")
            {
                strSql.Append("and " + strWhere);
            }
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }
		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ModelOld.M_Emr_Mb model)
		{
            try
            {
                StringBuilder strSql = new StringBuilder();
                strSql.Append("insert into Emr_Mb(");
                strSql.Append("Mb_Code,Mblb_Code,Mb_Name,Mb_Jc,Mb_Nr,Mb_Sex,isMust,isMulti,AgeLimit,isStandard,Ks_Code,Ys_Code,Mb_Memo)");
                strSql.Append(" values (");
                strSql.Append("@Mb_Code,@Mblb_Code,@Mb_Name,@Mb_Jc,@Mb_Nr,@Mb_Sex,@isMust,@isMulti,@AgeLimit,@isStandard,@Ks_Code,@Ys_Code,@Mb_Memo)");
                SqlParameter[] parameters = {
					new SqlParameter("@Mb_Code", SqlDbType.Char,10),
					new SqlParameter("@Mblb_Code", SqlDbType.Char,5),
					new SqlParameter("@Mb_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Mb_Jc", SqlDbType.VarChar,50),
					new SqlParameter("@Mb_Nr", SqlDbType.Image),
					new SqlParameter("@Mb_Sex", SqlDbType.Char,1),
					new SqlParameter("@isMust", SqlDbType.Bit,1),
					new SqlParameter("@isMulti", SqlDbType.Bit,1),
					new SqlParameter("@AgeLimit", SqlDbType.Int,4),
					new SqlParameter("@isStandard", SqlDbType.Bit,1),
					new SqlParameter("@Ks_Code", SqlDbType.Char,6),
					new SqlParameter("@Ys_Code", SqlDbType.Char,7),
					new SqlParameter("@Mb_Memo", SqlDbType.VarChar,50)};
                parameters[0].Value = model.Mb_Code;
                parameters[1].Value = model.Mblb_Code;
                parameters[2].Value = model.Mb_Name;
                parameters[3].Value = model.Mb_Jc;
                parameters[4].Value = Common.Tools.IsValueNull(model.Mb_Nr);
                parameters[5].Value = model.Mb_Sex;
                parameters[6].Value = model.isMust;
                parameters[7].Value = model.isMulti;
                parameters[8].Value = model.AgeLimit;
                parameters[9].Value = model.isStandard;
                parameters[10].Value = Common.Tools.IsValueNull(model.Ks_Code);
                parameters[11].Value = Common.Tools.IsValueNull(model.Ys_Code);
                parameters[12].Value = Common.Tools.IsValueNull(model.Mb_Memo);

                int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(), parameters);
                if (rows > 0)
                {
                    return true;
                }
                else
                {
                    return false;
                }
            
            }
            catch (Exception ex)
            { return false; }
		
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ModelOld.M_Emr_Mb model)
		{
            try
            {
                StringBuilder strSql = new StringBuilder();
                strSql.Append("update Emr_Mb set ");
                strSql.Append("Mblb_Code=@Mblb_Code,");
                strSql.Append("Mb_Name=@Mb_Name,");
                strSql.Append("Mb_Jc=@Mb_Jc,");
                strSql.Append("Mb_Nr=@Mb_Nr,");
                strSql.Append("Mb_Sex=@Mb_Sex,");
                strSql.Append("isMust=@isMust,");
                strSql.Append("isMulti=@isMulti,");
                strSql.Append("AgeLimit=@AgeLimit,");
                strSql.Append("isStandard=@isStandard,");
                strSql.Append("Ks_Code=@Ks_Code,");
                strSql.Append("Ys_Code=@Ys_Code,");
                strSql.Append("Mb_Memo=@Mb_Memo");
                strSql.Append(" where Mb_Code=@Mb_Code ");
                SqlParameter[] parameters = {
					new SqlParameter("@Mblb_Code", SqlDbType.Char,5),
					new SqlParameter("@Mb_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Mb_Jc", SqlDbType.VarChar,50),
					new SqlParameter("@Mb_Nr", SqlDbType.Image),
					new SqlParameter("@Mb_Sex", SqlDbType.Char,1),
					new SqlParameter("@isMust", SqlDbType.Bit,1),
					new SqlParameter("@isMulti", SqlDbType.Bit,1),
					new SqlParameter("@AgeLimit", SqlDbType.Int,4),
					new SqlParameter("@isStandard", SqlDbType.Bit,1),
					new SqlParameter("@Ks_Code", SqlDbType.Char,6),
					new SqlParameter("@Ys_Code", SqlDbType.Char,7),
					new SqlParameter("@Mb_Memo", SqlDbType.VarChar,50),
					new SqlParameter("@Mb_Code", SqlDbType.Char,10)};
                parameters[0].Value = model.Mblb_Code;
                parameters[1].Value = model.Mb_Name;
                parameters[2].Value = model.Mb_Jc;
                parameters[3].Value = Common.Tools.IsValueNull(model.Mb_Nr);
                parameters[4].Value = model.Mb_Sex;
                parameters[5].Value = model.isMust;
                parameters[6].Value = model.isMulti;
                parameters[7].Value = model.AgeLimit;
                parameters[8].Value = model.isStandard;
                parameters[9].Value = Common.Tools.IsValueNull(model.Ks_Code);
                parameters[10].Value = Common.Tools.IsValueNull(model.Ys_Code);
                parameters[11].Value = Common.Tools.IsValueNull(model.Mb_Memo);
                parameters[12].Value = model.Mb_Code;

                int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(), parameters);
                if (rows > 0)
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
            catch (Exception ex)
            { return false; }
			
		}



        public bool Update(string Mblb_Code,List<string> MbList)
        {
            List<string> cmdList=new List<string>() ;

            StringBuilder strSql = new StringBuilder();
            strSql.Append("update Emr_Mb set ");
            strSql.Append("Mblb_Code=@Mblb_Code");
            strSql.Append(" where Mb_Code=@Mb_Code ");

            List<SqlParameter[]> sqlParaList=new List<SqlParameter[]>();
        

            foreach (string Mb_Code in MbList)
            {
                cmdList.Add(strSql.ToString());
                SqlParameter[] parameters = {
					new SqlParameter("@Mblb_Code", SqlDbType.Char,5),
					new SqlParameter("@Mb_Code", SqlDbType.Char,10)};
                parameters[0].Value = Mblb_Code;
                parameters[1].Value = Mb_Code;
                sqlParaList.Add(parameters);
            }


            int rows = HisVar.HisVar.Sqldal.ExecuteSql(cmdList,sqlParaList );
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string Mb_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Emr_Mb ");
			strSql.Append(" where Mb_Code=@Mb_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Mb_Code", SqlDbType.Char,10)			};
			parameters[0].Value = Mb_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


        /// <summary>
        /// 删除一组数据
        /// </summary>
        public bool DeleteLb(string Mblb_Code)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from Emr_Mb ");
            strSql.Append(" where Mblb_Code=@Mblb_Code ");
            SqlParameter[] parameters = {
					new SqlParameter("@Mblb_Code", SqlDbType.Char,5)			};
            parameters[0].Value = Mblb_Code;

            int rows = HisVar.HisVar .Sqldal.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string Mb_Codelist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Emr_Mb ");
			strSql.Append(" where Mb_Code in ("+Mb_Codelist + ")  ");
			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Emr_Mb GetModel(string Mb_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 Mb_Code,Mblb_Code,Mb_Name,Mb_Jc,Mb_Nr,Mb_Sex,isMust,isMulti,AgeLimit,isStandard,Ks_Code,Ys_Code,Mb_Memo from Emr_Mb ");
			strSql.Append(" where Mb_Code=@Mb_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Mb_Code", SqlDbType.Char,10)			};
			parameters[0].Value = Mb_Code;
            ModelOld.M_Emr_Mb model=new ModelOld.M_Emr_Mb();
          	DataSet ds=HisVar.HisVar.Sqldal.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Emr_Mb DataRowToModel(DataRow row)
		{
			ModelOld.M_Emr_Mb model=new ModelOld.M_Emr_Mb();
			if (row != null)
			{
				if(row["Mb_Code"]!=null)
				{
					model.Mb_Code=row["Mb_Code"].ToString();
				}
				if(row["Mblb_Code"]!=null)
				{
					model.Mblb_Code=row["Mblb_Code"].ToString();
				}
				if(row["Mb_Name"]!=null)
				{
					model.Mb_Name=row["Mb_Name"].ToString();
				}
				if(row["Mb_Jc"]!=null)
				{
					model.Mb_Jc=row["Mb_Jc"].ToString();
				}
				if(row["Mb_Nr"]!=null && row["Mb_Nr"].ToString()!="")
				{
					model.Mb_Nr=(byte[])row["Mb_Nr"];
				}
				if(row["Mb_Sex"]!=null)
				{
					model.Mb_Sex=row["Mb_Sex"].ToString();
				}
				if(row["isMust"]!=null && row["isMust"].ToString()!="")
				{
					if((row["isMust"].ToString()=="1")||(row["isMust"].ToString().ToLower()=="true"))
					{
						model.isMust=true;
					}
					else
					{
						model.isMust=false;
					}
				}
				if(row["isMulti"]!=null && row["isMulti"].ToString()!="")
				{
					if((row["isMulti"].ToString()=="1")||(row["isMulti"].ToString().ToLower()=="true"))
					{
						model.isMulti=true;
					}
					else
					{
						model.isMulti=false;
					}
				}
				if(row["AgeLimit"]!=null && row["AgeLimit"].ToString()!="")
				{
					model.AgeLimit=int.Parse(row["AgeLimit"].ToString());
				}
				if(row["isStandard"]!=null && row["isStandard"].ToString()!="")
				{
					if((row["isStandard"].ToString()=="1")||(row["isStandard"].ToString().ToLower()=="true"))
					{
						model.isStandard=true;
					}
					else
					{
						model.isStandard=false;
					}
				}
				if(row["Ks_Code"]!=null)
				{
					model.Ks_Code=row["Ks_Code"].ToString();
				}
				if(row["Ys_Code"]!=null)
				{
					model.Ys_Code=row["Ys_Code"].ToString();
				}
				if(row["Mb_Memo"]!=null)
				{
					model.Mb_Memo=row["Mb_Memo"].ToString();
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
            strSql.Append("select Mb_Code,Emr_Mb.Mblb_Code,Mblb_Name,Mb_Name,Mb_Jc,Mb_Nr, Mb_Sex,isMust,isMulti,AgeLimit,isStandard,Emr_Mb.Ks_Code,ks_name,Emr_Mb.Ys_Code,ys_name,Mb_Memo ");
            strSql.Append(" FROM Emr_Mb join Emr_Mblb on Emr_Mb.mblb_code=Emr_Mblb.mblb_code LEFT JOIN dbo.Zd_YyKs ON Zd_YyKs.Ks_Code = Emr_Mb.Ks_Code LEFT JOIN dbo.Zd_YyYs ON Zd_YyYs.Ys_Code = Emr_Mb.Ys_Code");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

        public DataSet Getks()
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select distinct Emr_Mb.Ks_Code,ks_name,ks_jc");
            strSql.Append(" FROM Emr_Mb join Emr_Mblb on Emr_Mb.mblb_code=Emr_Mblb.mblb_code LEFT JOIN dbo.Zd_YyKs ON Zd_YyKs.Ks_Code = Emr_Mb.Ks_Code LEFT JOIN dbo.Zd_YyYs ON Zd_YyYs.Ys_Code = Emr_Mb.Ys_Code");
            strSql.Append(" order by  ks_name");
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetListExport(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT  Emr_Mblb.Mblb_Name, Emr_Mblb.Mblb_Jc, Father_Code,Mb_Code,Emr_Mb.Mblb_Code, Mb_Name, ");
            strSql.Append("Mb_Jc, Mb_Nr, Mb_Sex, isMust, isMulti, AgeLimit, isStandard, Ks_Code, Ys_Code, Mb_Memo");
            strSql.Append("  FROM dbo.Emr_Mb JOIN dbo.Emr_Mblb ON Emr_Mblb.Mblb_Code = Emr_Mb.Mblb_Code");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }




		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" Mb_Code,Mblb_Code,Mb_Name,Mb_Jc,Mb_Nr,Mb_Sex,isMust,isMulti,AgeLimit,isStandard,Ks_Code,Ys_Code,Mb_Memo ");
			strSql.Append(" FROM Emr_Mb ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM Emr_Mb ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.Mb_Code desc");
			}
			strSql.Append(")AS Row, T.*  from Emr_Mb T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Emr_Mb";
			parameters[1].Value = "Mb_Code";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return HisVar.HisVar.Sqldal.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

