﻿Imports System.Data.SqlClient
Imports System.Windows.Forms
Imports System.Drawing

Public Class MaterialsDict11

#Region "初始化"
    Dim rootOrNot As Boolean = True

    Dim My_Dataset As New DataSet
    Dim My_View As New DataView

    Dim V_Finish As Boolean = False
    Dim V_SelectCode As String = ""
    Dim V_FileName As String = ""
    Dim V_FindMaterialsStr As String = ""
    Dim noClassFatherCode As String = "00000"
    Dim V_Count As Integer

    Public My_Table As New DataTable
    Public My_Cm As CurrencyManager
    Public My_Row As DataRow
    Public V_Insert As Boolean
    Public V_FirstLoad As Boolean

    Dim BllMtClassDict As New BLLOld.B_Materials_Class_Dict
    Dim BllMtDict As New BLLOld.B_Materials_Dict
    Dim ModelMtDict As New ModelOld.M_Materials_Dict
    Dim JCSpell As New BaseClass.Chs2Spell


    Dim m_Rc As New BaseClass.C_RowChange
#End Region

    Private Sub MaterialsDict11_load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Call Form_Init()
        Call Tree_Init()
        Call Fill_Grid(Init_Data(""))
        AddHandler m_Rc.GridMoveEvent, AddressOf GridMove
    End Sub

#Region "窗体初始化"
    Private Sub Form_Init()
        Panel1.BorderStyle = BorderStyle.None
        Panel1.Height = 28
        ToolBar1.Location = New Point(1, 2)
        Filter_Tb.Location = New Point(ToolBar1.Right + 2, ToolBar1.Top + 1)
        T_Label.Location = New Point(Filter_Tb.Right + 2, ToolBar1.Top + 3)

        Comm1.Enabled = False
        CommDr.Enabled = False
        Init_Grid()
    End Sub

    Private Sub Init_Grid()
        With MyGrid1
            .Init_Grid()
            .Dock = System.Windows.Forms.DockStyle.Fill
            .Init_Column("编码", "Materials_Code", 100, "中", "", False)
            .Init_Column("名称", "Materials_Name", 200, "中", "", False)
            .Init_Column("拼音简称", "Materials_Py", 90, "中", "", False)
            .Init_Column("规格", "Materials_Spec", 90, "中", "", False)
            .Init_Column("包装单位", "Pack_Unit", 90, "中", "", False)
            .Init_Column("拆分比例", "Convert_Ratio", 90, "中", "", False)
            .Init_Column("散装单位", "Bulk_Unit", 90, "中", "", False)
            .Init_Column("生产厂家", "MateManu_Name", 100, "中", "", False)
            .Init_Column("父类别", "Class_Name", 100, "中", "", False)
            .Init_Column("是否启用", "IsUse", 100, "中", "", False)
            .Init_Column("备注", "Materials_Memo", 200, "左", "", False)
            .Splits(0).DisplayColumns("IsUse").FetchStyle = True
            .AllowSort = False
            .MultiSelect = True
        End With
    End Sub


    Private Sub Tree_Init()
        V_Finish = False
        With TreeView1
            .Nodes.Clear()
            .FullRowSelect = True
            .HideSelection = False
            .HotTracking = True
            .Scrollable = True
            .ShowRootLines = False
            .Sorted = False
        End With
        V_Count = BllMtClassDict.GetRecordCount("Class_Father='" & noClassFatherCode & "'")

        Dim My_root As New TreeNode
        With My_root
            .Tag = noClassFatherCode
            .Text = "物资类别(" & V_Count & ")"
            .ImageIndex = 0
        End With
        TreeView1.Nodes.Add(My_root)
        HaveChild(My_root)
        With TreeView1
            .TopNode.Expand()
            .Select()
            .SelectedNode = TreeView1.TopNode
        End With
        V_Finish = True
    End Sub

    Private Function Init_Data(ByVal classCode As String) As DataTable
        Dim tmpDt As New DataTable
        tmpDt = BllMtDict.GetListForDataInit(classCode).Tables(0)
        tmpDt.PrimaryKey = New DataColumn() {tmpDt.Columns("Materials_Code")}
        Return tmpDt
    End Function

    Private Sub Fill_Grid(ByVal dt As DataTable)
        My_Table = dt
        With Me.MyGrid1
            My_Cm = CType(BindingContext(My_Table, ""), CurrencyManager)
            .DataTable = My_Table
            T_Label.Text = "∑=" + .Splits(0).Rows.Count.ToString
        End With
        My_View = My_Cm.List
    End Sub

    Private Sub MaterialsDict11_FormClosing(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosingEventArgs) Handles Me.FormClosing
        My_Dataset.Dispose()
    End Sub

#End Region

#Region "控件__动作"

    Private Sub Edit_Materials_CMS_Opening(ByVal sender As System.Object, ByVal e As System.ComponentModel.CancelEventArgs) Handles Edit_Materials_CMS.Opening
        If MyGrid1.SelectedRows.Count > 0 Then
            MateriaEdit.Enabled = False
            MaterialMove.Enabled = True
        Else
            MateriaEdit.Enabled = True
            MaterialMove.Enabled = False
        End If
    End Sub

    Private Sub MateriaEdit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MateriaEdit.Click, MaterialMove.Click
        Select Case sender.tag
            Case "Edit"
                Call P_EditMaterial("DBGrid")
            Case "Move"
                Dim Vform As New Form
                Vform = New MaterialsMove(MyGrid1)
                If BaseFunc.BaseFunc.CheckOwnForm(Me, Vform) = False Then
                    Vform.Owner = Me
                    Vform.ShowDialog()
                    Vform.Dispose()
                End If
                Call Fill_Grid(Init_Sub_Data())
                Call changeNodeText(TreeView1.TopNode)
        End Select
    End Sub

    Private Sub CMenuStrip1_Opening(ByVal sender As System.Object, ByVal e As System.ComponentModel.CancelEventArgs) Handles CMenuStrip1.Opening
        Dim haveMaterials As Integer = BllMtDict.GetRecordCount(" Class_Code='" & TreeView1.SelectedNode.Tag & "'")
        Dim haveClass As Integer = BllMtClassDict.GetRecordCount(" Class_Father='" & TreeView1.SelectedNode.Tag & "'")
        Dim haveChild As Boolean
        If TreeView1.SelectedNode.Tag = noClassFatherCode Then
            haveChild = True
        Else
            haveChild = BllMtClassDict.GetModel(TreeView1.SelectedNode.Tag.ToString).HaveChild
        End If

        If haveChild = True Then
            AddClass.Enabled = True
        Else
            AddClass.Enabled = False
        End If

        If haveClass > 0 Or haveMaterials > 0 Or TreeView1.SelectedNode.Tag = noClassFatherCode Then
            DelClass.Enabled = False
        Else
            DelClass.Enabled = True
        End If

        If TreeView1.SelectedNode.Tag = noClassFatherCode Then
            EditClass.Enabled = False
        Else
            EditClass.Enabled = True
        End If
    End Sub

    Private Sub MenuClass_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles AddClass.Click, DelClass.Click, EditClass.Click, ExAllClass.Click
        Select Case sender.text
            Case "增加类别"
                Call P_EditClass("增加类别")
            Case "删除类别"
                P_Del_Class()
            Case "修改类别"
                Call P_EditClass("修改类别")
            Case "全部展开"
                TreeView1.ExpandAll()
        End Select
    End Sub

    Private Sub TreeView1_AfterSelect(ByVal sender As System.Object, ByVal e As System.Windows.Forms.TreeViewEventArgs) Handles TreeView1.AfterSelect
        If V_Finish = False Then Exit Sub
        Dim tag As Boolean
        If TreeView1.SelectedNode.Tag = noClassFatherCode Then
            tag = True
        Else
            tag = BllMtClassDict.GetModel(TreeView1.SelectedNode.Tag.ToString).HaveChild
        End If
        V_SelectCode = "Materials_Class_Dict.Class_Code ='" & Trim(e.Node.Tag) & "'"
        If tag = True Then
            CommDr.Enabled = False
            Comm1.Enabled = False
            Call Fill_Grid(Init_Sub_Data())
        Else
            CommDr.Enabled = True
            Comm1.Enabled = True
            Call Fill_Grid(Init_Data(V_SelectCode))
        End If
    End Sub

    Private Sub C1TrueDBGrid1_RowColChange_1(ByVal sender As System.Object, ByVal e As C1.Win.C1TrueDBGrid.RowColChangeEventArgs) Handles MyGrid1.RowColChange
        If (MyGrid1.Row + 1) > MyGrid1.RowCount Then
        Else
            My_Row = My_Cm.List(MyGrid1.Row).Row
            m_Rc.ChangeRow(My_Row)
        End If
    End Sub

    Private Sub GridMove(ByVal s As String)
        With MyGrid1
            If .RowCount = 0 Then Exit Sub
            Select Case s
                Case "最前"
                    .MoveFirst()
                Case "上移"
                    .MovePrevious()
                Case "下移"
                    .MoveNext()
                Case "最后"
                    .MoveLast()
                    T_Label.Text = "∑=" + .Splits(0).Rows.Count.ToString
            End Select
        End With
    End Sub

    Private Sub Comm_Click(ByVal sender As Object, ByVal e As C1.Win.C1Command.ClickEventArgs) Handles Comm1.Click, Comm2.Click, Comm3.Click, CommDr.Click, CommDc.Click
        Select Case sender.text
            Case "增加"
                Call P_EditMaterial("增加")
            Case "删除"
                Beep()
                If MyGrid1.Splits(0).Rows.Count = 0 Then Exit Sub
                Call P_Del_Materials()
            Case "更新"
                Call Tree_Init()
                Call Init_Grid()
                Call Fill_Grid(Init_Data(""))
                MyGrid1.Select()
                CommDr.Enabled = False
            Case "导入"
                Dim folderBrowserDialog As New System.Windows.Forms.OpenFileDialog()
                With folderBrowserDialog
                    .InitialDirectory = Environment.SpecialFolder.Desktop
                    .Filter = "xls文件|*.xls"
                    .Title = "选择文件"
                End With
                Dim result As System.Windows.Forms.DialogResult = folderBrowserDialog.ShowDialog()
                If result = System.Windows.Forms.DialogResult.OK Then
                    V_FileName = folderBrowserDialog.FileName
                    Dr_Excel()
                End If
                TreeView1.SelectedNode.Text = Mid(TreeView1.SelectedNode.Text, 1, InStrRev(TreeView1.SelectedNode.Text, "(")) & BllMtDict.GetRecordCount("Class_Code='" & TreeView1.SelectedNode.Tag.ToString & "'") & ")"

            Case "导出"
                If MyGrid1.RowCount > 0 Then
                    dataExport()
                Else
                    MsgBox("没有要导出的数据！", MsgBoxStyle.Exclamation, "提示")
                End If
        End Select

    End Sub

    Private Sub Filter_Tb_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Filter_Tb.TextChanged
        Dim V_Str As String = ""
        If Trim(Filter_Tb.Text & "") = "" Then
            V_Str = ""
        Else
            V_Str = "Materials_Name Like '*" & Trim(Filter_Tb.Text) & "*' or Materials_Py Like '*" & Trim(Filter_Tb.Text) & "*'"
        End If
        With My_View
            .Sort = "Materials_Code Asc"
            .RowFilter = V_Str
        End With
        T_Label.Text = "∑=" + MyGrid1.Splits(0).Rows.Count.ToString
    End Sub

    Private Sub C1TrueDBGrid1_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles MyGrid1.KeyDown
        Select Case e.KeyCode
            Case Keys.Return
                Call P_EditMaterial("DBGrid")
            Case Keys.Delete
                If Me.MyGrid1.RowCount > 0 Then Call P_Del_Materials()
            Case Keys.Insert
                Call P_EditMaterial("增加")
        End Select
    End Sub

    Private Sub MyGrid1_FetchCellStyle(ByVal sender As System.Object, ByVal e As C1.Win.C1TrueDBGrid.FetchCellStyleEventArgs) Handles MyGrid1.FetchCellStyle
        If e.Column.Name = "是否启用" Then
            Dim flag As String = MyGrid1.Columns("IsUse").CellValue(e.Row).ToString
            If flag = "True" Then
                e.CellStyle.ForegroundImage = My.Resources.启用16
            ElseIf flag = "False" Then
                e.CellStyle.ForegroundImage = My.Resources.停用16
            End If
            e.CellStyle.ForeGroundPicturePosition = C1.Win.C1TrueDBGrid.ForeGroundPicturePositionEnum.PictureOnly
        End If
    End Sub

#End Region

#Region "自定义函数"

    Private Function Init_Sub_Data() As DataTable
        V_FindMaterialsStr = " Materials_Class_Dict.Class_Code='" & TreeView1.SelectedNode.Tag & "'"
        WalkNode(TreeView1.SelectedNode)
        Return Init_Data(V_FindMaterialsStr)
    End Function

    Private Sub WalkNode(ByVal node As TreeNode)
        If node.Nodes.Count > 0 Then
            For Each child As TreeNode In node.Nodes
                V_FindMaterialsStr = V_FindMaterialsStr + (" OR Materials_Class_Dict.Class_Code ='" & child.Tag & "'")
                WalkNode(child)
            Next child
        End If
    End Sub



    Private Sub HaveChild(ByVal fatherCode As TreeNode)
        Dim childDtclass As DataTable
        childDtclass = BllMtClassDict.GetList("Class_Father='" & fatherCode.Tag & "'").Tables(0)
        If childDtclass.Rows.Count > 0 Then
            For i = 0 To childDtclass.Rows.Count - 1
                Dim My_node As New TreeNode
                With My_node
                    .Tag = childDtclass.Rows(i).Item("Class_Code")
                    If childDtclass.Rows(i).Item("HaveChild") = True Then
                        .Text = childDtclass.Rows(i).Item("Class_Name").ToString & "(" & BllMtClassDict.GetRecordCount("Class_Father='" & childDtclass.Rows(i).Item("Class_Code") & "'") & ")"
                        .ImageIndex = 1
                        .SelectedImageIndex = 2
                    Else
                        .Text = childDtclass.Rows(i).Item("Class_Name").ToString & "(" & BllMtDict.GetRecordCount("Class_Code ='" & childDtclass.Rows(i).Item("Class_Code") & "'") & ")"
                        .ImageIndex = 3
                        .SelectedImageIndex = 4
                    End If
                End With
                fatherCode.Nodes.Add(My_node)
                HaveChild(My_node)
            Next
        End If
    End Sub

    Private Sub P_EditMaterial(ByVal V_Lb As String)
        If V_Lb = "增加" Then
            V_Insert = True
        Else
            If MyGrid1.RowCount > 0 Then V_Insert = False Else V_Insert = True
        End If

        If V_Insert = False Then
            My_Row = My_Cm.List(MyGrid1.Row).Row
        End If

        Dim tag As Boolean
        If TreeView1.SelectedNode.Tag = noClassFatherCode Then
            tag = True
        Else
            tag = BllMtClassDict.GetModel(TreeView1.SelectedNode.Tag.ToString).HaveChild
        End If

        Dim allowInsert As Boolean
        If tag = True Then
            allowInsert = False
        Else
            allowInsert = True
        End If


        If MyGrid1.RowCount = 0 And tag = True Then
        Else
            Dim Vform As New Form
            Vform = New MaterialsDict13(V_Insert, My_Row, My_Table, m_Rc, TreeView1, allowInsert)

            If BaseFunc.BaseFunc.CheckOwnForm(Me, Vform) = False Then
                Vform.Owner = Me
                Vform.ShowDialog()
                Vform.Dispose()
            End If
            MyGrid1.Select()
        End If

    End Sub

    Private Sub P_EditClass(ByVal V_Lb As String)
        If V_Lb = "增加类别" Then
            V_Insert = True
        Else
            V_Insert = False
        End If

        Dim Vform As New Form
        Vform = New MaterialsDict12(V_Insert, TreeView1)

        If BaseFunc.BaseFunc.CheckOwnForm(Me, Vform) = False Then
            Vform.Owner = Me
            Vform.ShowDialog()
            Vform.Dispose()
        End If
        MyGrid1.Select()
    End Sub

    Private Sub changeNodeText(ByVal node As TreeNode)
        Dim haveChild As Boolean
        If node.Nodes.Count > 0 Then
            For Each child As TreeNode In node.Nodes
                If child.Tag = noClassFatherCode Then
                    haveChild = True
                Else
                    haveChild = BllMtClassDict.GetModel(child.Tag.ToString).HaveChild
                End If
                If haveChild = True Then
                    changeNodeText(child)
                Else
                    child.Text = Mid(child.Text, 1, InStrRev(child.Text, "(")) & BllMtDict.GetRecordCount("Class_Code='" & child.Tag & "'") & ")"
                End If
            Next
        End If

    End Sub

    Private Sub changeNodeTextWhenDel(ByVal node As TreeNode, ByVal f_Tag As String)
        If node.Nodes.Count > 0 Then
            For Each child As TreeNode In node.Nodes
                If child.Tag = f_Tag Then
                    child.Text = Mid(child.Text, 1, InStrRev(child.Text, "(")) & BllMtDict.GetRecordCount("Class_Code='" & child.Tag & "'") & ")"
                Else
                    changeNodeTextWhenDel(child, f_Tag)
                End If
            Next child
        End If
    End Sub

    Private Sub P_Del_Materials()
        Beep()
        If MsgBox("是否删除:物资=" + Me.MyGrid1.Columns("Materials_Name").Value + " ", MsgBoxStyle.Information + MsgBoxStyle.OkCancel, "提示:") = MsgBoxResult.Cancel Then Exit Sub
        My_Row = My_Cm.List(MyGrid1.Row).Row
        BllMtDict.Delete(My_Row("Materials_Code"))
        changeNodeTextWhenDel(TreeView1.SelectedNode, My_Row("Class_Code"))
        MyGrid1.Delete()
        My_Row.AcceptChanges()
        T_Label.Text = "∑=" + MyGrid1.Splits(0).Rows.Count.ToString
    End Sub

    Private Sub P_Del_Class()
        Beep()
        Dim TagNumber As Integer
        If BllMtClassDict.GetModel(TreeView1.SelectedNode.Tag.ToString).HaveChild = True Then
            TagNumber = BllMtClassDict.GetRecordCount("Class_Father = '" & TreeView1.SelectedNode.Tag & "'")
        ElseIf BllMtClassDict.GetModel(TreeView1.SelectedNode.Tag.ToString).HaveChild = False Then
            TagNumber = BllMtDict.GetRecordCount("Class_Code = '" & TreeView1.SelectedNode.Tag & "'")
        End If
        If MsgBox("是否删除:物资类别=" & TreeView1.SelectedNode.Text & " ", MsgBoxStyle.Information + MsgBoxStyle.OkCancel, "提示:") = MsgBoxResult.Cancel Then Exit Sub
        If TagNumber <> 0 Then
            MsgBox("该类别下含有关联项，不能删除!若删除，请先清空该类别的关联项！", MsgBoxStyle.Information, "提示")
            Exit Sub
        Else
            BllMtClassDict.Delete(TreeView1.SelectedNode.Tag.ToString)
            TreeView1.Nodes.Remove(TreeView1.SelectedNode)
        End If

    End Sub

    Private Sub Dr_Excel()
        Dim count, drCount, countRepeat As Integer
        Dim file1 As New System.IO.FileInfo(V_FileName)
        count = 0
        drCount = 0
        countRepeat = 0
        Dim dtDr As DataTable = BllMtDict.GetListForDr.Tables(0)
        Try
            BaseFunc.BaseFunc.RenderDataTableFromExcel(V_FileName, 0, 0, dtDr, 7, 0)
            drCount = dtDr.Rows.Count
            If drCount = 0 Then
                MsgBox("没有数据！", MsgBoxStyle.Information, "提示")
                Exit Sub
            End If
            PSetText(file1.Name & "中包含" & drCount & "条,存在重复" & countRepeat & "条,成功导入" & count)
            For Each row In dtDr.Rows
                Try
                    If BllMtDict.GetRecordCount("Materials_Name='" & row("Materials_Name") & "'") > 0 Then
                        countRepeat += 1
                        PSetText(file1.Name & "中包含" & drCount & "条,存在重复" & countRepeat & "条,成功导入" & count)
                        Continue For
                    End If


                    With ModelMtDict
                        .Materials_Code = BllMtDict.MaxCode
                        .Materials_Name = row("Materials_Name") & ""
                        .Materials_Py = JCSpell.GetPy(row("Materials_Name") & "")
                        .Materials_Spec = row("Materials_Spec") & ""
                        .Bulk_Unit = row("Bulk_Unit") & ""
                        .Pack_Unit = row("Pack_Unit") & ""
                        .Convert_Ratio = row("Convert_Ratio")
                        .MateManu_Name = row("MateManu_Name") & ""
                        .Materials_Memo = row("Materials_Memo") & ""
                        .Class_Code = TreeView1.SelectedNode.Tag
                        .IsUse = True
                    End With

                    Dim r As DataRow = My_Table.NewRow
                    With r
                        .Item("Materials_Code") = ModelMtDict.Materials_Code
                        .Item("Materials_Name") = ModelMtDict.Materials_Name
                        .Item("Materials_Py") = ModelMtDict.Materials_Py
                        .Item("Materials_Spec") = ModelMtDict.Materials_Spec
                        .Item("Bulk_Unit") = ModelMtDict.Bulk_Unit
                        .Item("Pack_Unit") = ModelMtDict.Pack_Unit
                        .Item("Convert_Ratio") = ModelMtDict.Convert_Ratio
                        .Item("MateManu_Name") = ModelMtDict.MateManu_Name
                        .Item("Class_Code") = ModelMtDict.Class_Code
                        .Item("Materials_Memo") = ModelMtDict.Materials_Memo
                        .Item("IsUse") = True
                    End With
                    BllMtDict.Add(ModelMtDict)
                    My_Table.Rows.Add(r)
                    My_Table.AcceptChanges()
                    count += 1
                    PSetText(file1.Name & "中包含" & drCount & "条,存在重复" & countRepeat & "条,成功导入" & count)
                Catch ex As Exception
                    Beep()
                    MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
                    Exit Sub
                Finally
                End Try
            Next

        Catch ex As Exception
            MsgBox("导入错误：" & ex.ToString)
        End Try
        MsgBox("导入成功", MsgBoxStyle.Information, "提示")
    End Sub

    Private Delegate Sub delSetText(ByVal txt As String)
    Private Sub PSetText(ByVal txt As String)
        If T_Label.InvokeRequired = True Then
            Dim del As New delSetText(AddressOf PSetText)
            T_Label.Invoke(del, txt)
        Else
            T_Label.Text = txt
        End If
    End Sub

    Private Sub dataExport()
        BaseFunc.BaseFunc.ExportExcel(Me.Text, MyGrid1, "", True)
    End Sub

#End Region

End Class
