﻿/**  版本信息模板在安装目录下，可自行修改。
* M_Bl_TWD1.cs
*
* 功 能： N/A
* 类 名： M_Bl_TWD1
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/6/23 15:47:22   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace ModelOld
{
	/// <summary>
	/// M_Bl_TWD1:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class M_Bl_TWD1
	{
		public M_Bl_TWD1()
		{}
		#region Model
		private string _bl_code;
		private string _cl_code;
		private DateTime? _cl_date;
		private int? _height;
		private decimal? _weight;
		private decimal? _zrl;
		private decimal? _zcl;
		private decimal? _yll;
		private string _dbcs;
		private int? _xbcs;
		private decimal? _xbl;
		private string _gmyw1;
		private string _psjg;
		private string _xy1;
		private string _xy2;
		private bool _ispregnant;
		private string _jsr_code;
		private DateTime? _lr_date= DateTime.Now;
		/// <summary>
		/// 病历编码
		/// </summary>
		public string Bl_Code
		{
			set{ _bl_code=value;}
			get{return _bl_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string CL_Code
		{
			set{ _cl_code=value;}
			get{return _cl_code;}
		}
		/// <summary>
		/// 测量日期
		/// </summary>
		public DateTime? CL_Date
		{
			set{ _cl_date=value;}
			get{return _cl_date;}
		}
		/// <summary>
		/// 
		/// </summary>
		public int? Height
		{
			set{ _height=value;}
			get{return _height;}
		}
		/// <summary>
		/// 
		/// </summary>
		public decimal? Weight
		{
			set{ _weight=value;}
			get{return _weight;}
		}
		/// <summary>
		/// 总入量
		/// </summary>
		public decimal? ZRL
		{
			set{ _zrl=value;}
			get{return _zrl;}
		}
		/// <summary>
		/// 总出量
		/// </summary>
		public decimal? ZCL
		{
			set{ _zcl=value;}
			get{return _zcl;}
		}
		/// <summary>
		/// 引流量
		/// </summary>
		public decimal? YLL
		{
			set{ _yll=value;}
			get{return _yll;}
		}
		/// <summary>
		/// 大便次数
		/// </summary>
		public string DBCS
		{
			set{ _dbcs=value;}
			get{return _dbcs;}
		}
		/// <summary>
		/// 小便次数
		/// </summary>
		public int? XBCS
		{
			set{ _xbcs=value;}
			get{return _xbcs;}
		}
		/// <summary>
		/// 尿量
		/// </summary>
		public decimal? XBL
		{
			set{ _xbl=value;}
			get{return _xbl;}
		}
		/// <summary>
		/// 过敏药物1
		/// </summary>
		public string GMYW1
		{
			set{ _gmyw1=value;}
			get{return _gmyw1;}
		}
		/// <summary>
		/// 过敏药物2
		/// </summary>
        public string   PSJG    
		{
            set { _psjg = value; }
            get { return _psjg; }
		}
		/// <summary>
		/// 血压1
		/// </summary>
		public string XY1
		{
			set{ _xy1=value;}
			get{return _xy1;}
		}
		/// <summary>
		/// 血压2
		/// </summary>
		public string XY2
		{
			set{ _xy2=value;}
			get{return _xy2;}
		}
		/// <summary>
		/// 
		/// </summary>
		public bool IsPregnant
		{
			set{ _ispregnant=value;}
			get{return _ispregnant;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Jsr_Code
		{
			set{ _jsr_code=value;}
			get{return _jsr_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public DateTime? Lr_Date
		{
			set{ _lr_date=value;}
			get{return _lr_date;}
		}
		#endregion Model

	}
}

