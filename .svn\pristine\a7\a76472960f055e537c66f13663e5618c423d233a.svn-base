﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>
    </SchemaVersion>
    <ProjectGuid>{D05310B8-9C6A-4961-8664-8133AE171F15}</ProjectGuid>
    <OutputType>Library</OutputType>
    <RootNamespace>ZtHis.Emr</RootNamespace>
    <AssemblyName>ZtHis.Emr</AssemblyName>
    <FileAlignment>512</FileAlignment>
    <MyType>Windows</MyType>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <OutputPath>bin\Debug\</OutputPath>
    <DocumentationFile>ZtHis.Emr.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
    <PlatformTarget>x86</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DocumentationFile>ZtHis.Emr.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
  </PropertyGroup>
  <PropertyGroup>
    <OptionExplicit>On</OptionExplicit>
  </PropertyGroup>
  <PropertyGroup>
    <OptionCompare>Binary</OptionCompare>
  </PropertyGroup>
  <PropertyGroup>
    <OptionStrict>Off</OptionStrict>
  </PropertyGroup>
  <PropertyGroup>
    <OptionInfer>On</OptionInfer>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="C1.Win.C1Command.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=e808566f358766d8, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.C1FlexGrid.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.C1Input.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=7e7ff60f0c214f9a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.C1List.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=6b24f8f981dbd7bc, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.C1Ribbon.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.C1TrueDBGrid.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=75ae3fb0e2b1e0da, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="oledbDalHelper, Version=1.0.0.2, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\Dll\oledbDalHelper.dll</HintPath>
    </Reference>
    <Reference Include="SqlDal">
      <HintPath>..\Dll\SqlDal.dll</HintPath>
    </Reference>
    <Reference Include="Stimulsoft.Report, Version=2011.2.1026.0, Culture=neutral, PublicKeyToken=ebe6666cba19647a, processorArchitecture=MSIL">
      <HintPath>..\output\Stimulsoft.Report.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Import Include="Microsoft.VisualBasic" />
    <Import Include="System" />
    <Import Include="System.Collections" />
    <Import Include="System.Collections.Generic" />
    <Import Include="System.Data" />
    <Import Include="System.Diagnostics" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="1.模板管理\MblbTree.Designer.vb">
      <DependentUpon>MblbTree.vb</DependentUpon>
    </Compile>
    <Compile Include="1.模板管理\MblbTree.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="3.病历归档\BlGuiDang.Designer.vb">
      <DependentUpon>BlGuiDang.vb</DependentUpon>
    </Compile>
    <Compile Include="3.病历归档\BlGuiDang.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="4.质控审查\ZkSc.Designer.vb">
      <DependentUpon>ZkSc.vb</DependentUpon>
    </Compile>
    <Compile Include="4.质控审查\ZkSc.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="4.质控审查\ZkSc2.Designer.vb">
      <DependentUpon>ZkSc2.vb</DependentUpon>
    </Compile>
    <Compile Include="4.质控审查\ZkSc2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="6.质控评分等级\ZkDj1.designer.vb">
      <DependentUpon>ZkDj1.vb</DependentUpon>
    </Compile>
    <Compile Include="6.质控评分等级\ZkDj1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="6.质控评分等级\ZkDj2.designer.vb">
      <DependentUpon>ZkDj2.vb</DependentUpon>
    </Compile>
    <Compile Include="6.质控评分等级\ZkDj2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="1.模板管理\MbElement.Designer.vb">
      <DependentUpon>MbElement.vb</DependentUpon>
    </Compile>
    <Compile Include="1.模板管理\MbElement.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="1.模板管理\MbDesign.Designer.vb">
      <DependentUpon>MbDesign.vb</DependentUpon>
    </Compile>
    <Compile Include="1.模板管理\MbDesign.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="1.模板管理\MbExport.Designer.vb">
      <DependentUpon>MbExport.vb</DependentUpon>
    </Compile>
    <Compile Include="1.模板管理\MbExport.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="1.模板管理\MblbChange.Designer.vb">
      <DependentUpon>MblbChange.vb</DependentUpon>
    </Compile>
    <Compile Include="1.模板管理\MblbChange.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="2.质控标准\Zd_ZhiKong2.designer.vb">
      <DependentUpon>Zd_ZhiKong2.vb</DependentUpon>
    </Compile>
    <Compile Include="2.质控标准\Zd_ZhiKong2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="2.质控标准\Zd_ZhiKong3.Designer.vb">
      <DependentUpon>Zd_ZhiKong3.vb</DependentUpon>
    </Compile>
    <Compile Include="2.质控标准\Zd_ZhiKong3.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="2.质控标准\Zd_ZhiKong1.designer.vb">
      <DependentUpon>Zd_ZhiKong1.vb</DependentUpon>
    </Compile>
    <Compile Include="2.质控标准\Zd_ZhiKong1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="5.基础设置\EmrBasic1.Designer.vb">
      <DependentUpon>EmrBasic1.vb</DependentUpon>
    </Compile>
    <Compile Include="5.基础设置\EmrBasic1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="5.基础设置\EmrBasic2.Designer.vb">
      <DependentUpon>EmrBasic2.vb</DependentUpon>
    </Compile>
    <Compile Include="5.基础设置\EmrBasic2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="5.基础设置\EmrBasic3.Designer.vb">
      <DependentUpon>EmrBasic3.vb</DependentUpon>
    </Compile>
    <Compile Include="5.基础设置\EmrBasic3.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="7.时控设置\Zd_ShiKongMain.designer.vb">
      <DependentUpon>Zd_ShiKongMain.vb</DependentUpon>
    </Compile>
    <Compile Include="7.时控设置\Zd_ShiKongMain.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="7.时控设置\Zd_ShiKongTwo.designer.vb">
      <DependentUpon>Zd_ShiKongTwo.vb</DependentUpon>
    </Compile>
    <Compile Include="7.时控设置\Zd_ShiKongTwo.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="8.医生站工作台\EmrAddBl.Designer.vb">
      <DependentUpon>EmrAddBl.vb</DependentUpon>
    </Compile>
    <Compile Include="8.医生站工作台\EmrAddBl.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="8.医生站工作台\EmrEditBl.Designer.vb">
      <DependentUpon>EmrEditBl.vb</DependentUpon>
    </Compile>
    <Compile Include="8.医生站工作台\EmrEditBl.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="9.时限查询\ShiXianCx.Designer.vb">
      <DependentUpon>ShiXianCx.vb</DependentUpon>
    </Compile>
    <Compile Include="9.时限查询\ShiXianCx.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="My Project\AssemblyInfo.vb" />
    <Compile Include="My Project\Application.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
    </Compile>
    <Compile Include="My Project\Resources.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="My Project\Settings.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <Compile Include="1.模板管理\EmrMb1.designer.vb">
      <DependentUpon>EmrMb1.vb</DependentUpon>
    </Compile>
    <Compile Include="1.模板管理\EmrMb1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="1.模板管理\EmrMb2.designer.vb">
      <DependentUpon>EmrMb2.vb</DependentUpon>
    </Compile>
    <Compile Include="1.模板管理\EmrMb2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="1.模板管理\EmrMb3.designer.vb">
      <DependentUpon>EmrMb3.vb</DependentUpon>
    </Compile>
    <Compile Include="1.模板管理\EmrMb3.vb">
      <SubType>Form</SubType>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="1.模板管理\MblbTree.resx">
      <DependentUpon>MblbTree.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="3.病历归档\BlGuiDang.resx">
      <DependentUpon>BlGuiDang.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="4.质控审查\ZkSc.resx">
      <DependentUpon>ZkSc.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="4.质控审查\ZkSc2.resx">
      <DependentUpon>ZkSc2.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="6.质控评分等级\ZkDj1.resx">
      <DependentUpon>ZkDj1.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="6.质控评分等级\ZkDj2.resx">
      <DependentUpon>ZkDj2.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="1.模板管理\MbElement.resx">
      <DependentUpon>MbElement.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="1.模板管理\MbDesign.resx">
      <DependentUpon>MbDesign.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="1.模板管理\MbExport.resx">
      <DependentUpon>MbExport.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="1.模板管理\MblbChange.resx">
      <DependentUpon>MblbChange.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="2.质控标准\Zd_ZhiKong2.resx">
      <DependentUpon>Zd_ZhiKong2.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="2.质控标准\Zd_ZhiKong3.resx">
      <DependentUpon>Zd_ZhiKong3.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="2.质控标准\Zd_ZhiKong1.resx">
      <DependentUpon>Zd_ZhiKong1.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="5.基础设置\EmrBasic1.resx">
      <DependentUpon>EmrBasic1.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="5.基础设置\EmrBasic2.resx">
      <DependentUpon>EmrBasic2.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="5.基础设置\EmrBasic3.resx">
      <DependentUpon>EmrBasic3.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="7.时控设置\Zd_ShiKongMain.resx">
      <DependentUpon>Zd_ShiKongMain.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="7.时控设置\Zd_ShiKongTwo.resx">
      <DependentUpon>Zd_ShiKongTwo.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="8.医生站工作台\EmrAddBl.resx">
      <DependentUpon>EmrAddBl.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="8.医生站工作台\EmrEditBl.resx">
      <DependentUpon>EmrEditBl.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="9.时限查询\ShiXianCx.resx">
      <DependentUpon>ShiXianCx.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="My Project\licenses.licx" />
    <EmbeddedResource Include="My Project\Resources.resx">
      <Generator>VbMyResourcesResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.vb</LastGenOutput>
      <CustomToolNamespace>My.Resources</CustomToolNamespace>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="1.模板管理\EmrMb1.resx">
      <DependentUpon>EmrMb1.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="1.模板管理\EmrMb2.resx">
      <DependentUpon>EmrMb2.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="1.模板管理\EmrMb3.resx">
      <DependentUpon>EmrMb3.vb</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
    <None Include="My Project\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <CustomToolNamespace>My</CustomToolNamespace>
      <LastGenOutput>Settings.Designer.vb</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\BaseClass\BaseClass.vbproj">
      <Project>{48964D0C-25C0-413C-A94A-C2246ADE46A1}</Project>
      <Name>BaseClass</Name>
    </ProjectReference>
    <ProjectReference Include="..\BaseFunc\BaseFunc.vbproj">
      <Project>{C7865854-F754-41AC-9912-829068BE5C2A}</Project>
      <Name>BaseFunc</Name>
    </ProjectReference>
    <ProjectReference Include="..\BLLOld\BLLOld.csproj">
      <Project>{42c23254-ba1f-4222-895b-276fc06bf59c}</Project>
      <Name>BLLOld</Name>
    </ProjectReference>
    <ProjectReference Include="..\CustomControl\CustomControl.csproj">
      <Project>{12BF4168-D60E-4A6C-85BF-926130EE6A6D}</Project>
      <Name>CustomControl</Name>
    </ProjectReference>
    <ProjectReference Include="..\HisControl\HisControl.vbproj">
      <Project>{EF778791-6E30-4A07-83B9-2D73FD08810A}</Project>
      <Name>HisControl</Name>
    </ProjectReference>
    <ProjectReference Include="..\HisPara\HisPara.vbproj">
      <Project>{3E790840-B7EB-4875-A334-D0386A5633CB}</Project>
      <Name>HisPara</Name>
    </ProjectReference>
    <ProjectReference Include="..\HisVar\HisVar.vbproj">
      <Project>{4DAD5E14-6224-46B2-886D-6D22CE3886BC}</Project>
      <Name>HisVar</Name>
    </ProjectReference>
    <ProjectReference Include="..\ModelOld\ModelOld.csproj">
      <Project>{1FAF80FE-D42E-4FEB-853A-B9846C1862AE}</Project>
      <Name>ModelOld</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\删除.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\刷新.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\增加.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\修改.png" />
    <None Include="Resources\import_48px_1133993_easyicon.net.png" />
    <None Include="Resources\export_48px_1133978_easyicon.net.png" />
    <None Include="Resources\copy_48px_1133968_easyicon.net.png" />
    <None Include="Resources\Icon_1469.png" />
  </ItemGroup>
  <ItemGroup>
    <COMReference Include="AxNsoOfficeLib">
      <Guid>{B21A69CE-901E-42FF-8F96-39616D2F16D1}</Guid>
      <VersionMajor>1</VersionMajor>
      <VersionMinor>0</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>aximp</WrapperTool>
      <Isolated>False</Isolated>
    </COMReference>
    <COMReference Include="NsoOfficeLib">
      <Guid>{B21A69CE-901E-42FF-8F96-39616D2F16D1}</Guid>
      <VersionMajor>1</VersionMajor>
      <VersionMinor>0</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>tlbimp</WrapperTool>
      <Isolated>False</Isolated>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </COMReference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Resources\关闭审阅.png" />
    <Content Include="Resources\关闭痕迹.png" />
    <Content Include="Resources\开启审阅.png" />
    <Content Include="Resources\开启痕迹.png" />
    <Content Include="Resources\打印.bmp">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <None Include="Resources\导出.png" />
    <None Include="Resources\导入.png" />
    <None Include="Resources\反选.png" />
    <None Include="Resources\全选.png" />
    <Content Include="Resources\已出院.png" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.VisualBasic.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>