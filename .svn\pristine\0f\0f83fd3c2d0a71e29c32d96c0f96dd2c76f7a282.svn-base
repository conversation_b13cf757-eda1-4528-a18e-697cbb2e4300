﻿Imports C1.Win.C1Command

Module Module1
    Private Declare Function WritePrivateProfileString Lib "kernel32" Alias "WritePrivateProfileStringA" (ByVal lpApplicationName As String, ByVal lpKeyName As String, ByVal lpString As String, ByVal lpFileName As String) As Int32
    Public Sub RemoveTap(ByVal MyForm As Form)
        If HisVar.HisVar.DockTab IsNot Nothing Then
            For Each con As C1DockingTabPage In HisVar.HisVar.DockTab.TabPages
                Dim tab As C1DockingTabPage = DirectCast(con, C1DockingTabPage)
                For Each _ctrl In tab.Controls
                    If _ctrl Is MyForm Then
                        HisVar.HisVar.DockTab.Invoke(New Action(Function() 
                            HisVar.HisVar.DockTab.Close(tab)
                        End Function))
                    End If
                Next
            Next
        End If
    End Sub
    Public Function WriteINI(ByVal Section As String, ByVal AppName As String, ByVal lpDefault As String, ByVal FileName As String) As Long
        WriteINI = WritePrivateProfileString(Section, AppName, lpDefault, FileName)
    End Function
End Module
