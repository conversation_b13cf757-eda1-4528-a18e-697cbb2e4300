﻿Imports C1.Win.C1Input
Imports C1.Win.C1TrueDBGrid
Imports System.Data.SqlClient
Imports BaseClass

Public Class Zd_Yp12

#Region "变量定义"
    Dim My_Button As C_Button                                       '按扭初始化
    Dim V_Name As String                                            '简称是否发生变化
    Dim My_Cc As New BaseClass.C_Cc()

#End Region

#Region "传参"
    Dim R<PERSON>ert As Boolean
    Dim Rrow As DataRow
    Dim RZbtb As DataTable
    Dim Rtdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Dim Rlb As C1.Win.C1Input.C1Label
    Dim Rrc As C_RowChange
    Dim Rtree As TreeView

#End Region

    Public Sub New(ByVal tinsert As Boolean, ByVal trow As DataRow, ByVal tZbtb As DataTable, ByVal ttdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid, ByVal tlb As C1.Win.C1Input.C1Label, ByRef trc As C_RowChange, ByRef ttree As TreeView)

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        Rinsert = tinsert
        'Rdate = tdate
        Rrow = trow
        RZbtb = tZbtb
        Rtdbgrid = ttdbgrid
        Rlb = tlb
        Rrc = trc
        Rtree = ttree


        ' 在 InitializeComponent() 调用之后添加任何初始化。
    End Sub

    Private Sub Zd_Yp12_FormClosed(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosedEventArgs) Handles Me.FormClosed
        RemoveHandler Rrc.RowChanged, New BaseClass.RowChangedHandler(AddressOf Data_Show)
        Me.Dispose()
    End Sub

    Private Sub Zd_Yp12_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        AddHandler Rrc.RowChanged, New BaseClass.RowChangedHandler(AddressOf Data_Show)
        Call Form_Init()

        If Rinsert = True Then Call Data_Clear() Else Call Data_Show(Rrow)

    End Sub

#Region "窗体初始化"

    Private Sub Form_Init()
        Panel1.Height = 29
        ToolBar1.Location = New Point(2, 5)

        'Escape 不接收修改
        C1TextBox1.AcceptsEscape = False
        C1TextBox2.AcceptsEscape = False
        C1TextBox3.AcceptsEscape = False

        '按扭初始化
        My_Button = New C_Button(Comm1, Comm2)
        My_Button.Init_Button(ToolBar1.Left + ToolBar1.Width + 37, 4)

    End Sub

#End Region

#Region "数据__操作"

    Private Sub Data_Clear()
        Move5.Enabled = False
        My_Cc.Get_MaxCode("Zd_Ml_Yp1", "Dl_Code", 2, "", "")
        L_Dl_Code.Text = My_Cc.编码                  '最大编码
        C1TextBox1.Text = ""
        C1TextBox2.Text = ""
        C1TextBox3.Text = ""
        Call P_Show_Label()
    End Sub

    Private Sub Data_Show(ByVal tmp_Row As DataRow)
        Move5.Enabled = True
        Rrow = tmp_Row
        With Rrow
            V_Name = .Item("Dl_Name") & ""
            L_Dl_Code.Text = .Item("Dl_Code") & ""
            C1TextBox1.Text = .Item("Dl_Name") & ""
            C1TextBox2.Text = .Item("Dl_Jc") & ""
            C1TextBox3.Text = .Item("Dl_Memo") & ""
        End With
        Call P_Show_Label()
    End Sub

    Private Sub P_Show_Label()
        Dim V_Count As Integer = Rtdbgrid.Splits(0).Rows.Count()
        If Move5.Enabled = False Then                                           '新增记录
            Rinsert = True
            T_Label2.Text = "新增"
        Else
            Rinsert = False
            T_Label2.Text = IIf(V_Count = 0, "0", CStr((Rtdbgrid.Row) + 1))
        End If
        T_Label3.Text = "∑=" + V_Count.ToString
        C1TextBox1.SelectAll()
        C1TextBox1.Select()
    End Sub

#End Region

#Region "控件__动作"

    Private Sub Comm_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.Click, Comm2.Click
        Select Case sender.tag

            Case "保存"
                If Trim(C1TextBox1.Text & "") = "" Then
                    Beep()
                    MsgBox("药品类别名称不能为空,按任意键返回！", MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
                    C1TextBox1.Focus()
                    Exit Sub
                End If

                If Rinsert = True Then Call Data_Add() Else Call Data_Edit()

            Case "取消"
                Me.Close()

        End Select
    End Sub

    Private Sub Move_Click(ByVal sender As Object, ByVal e As C1.Win.C1Command.ClickEventArgs) Handles Move1.Click, Move2.Click, Move3.Click, Move4.Click, Move5.Click
        If sender.text = "新增" Then                            '增加状态
            Call Data_Clear()
        Else
            With Rtdbgrid
                If .RowCount = 0 Then Exit Sub
                Select Case sender.text
                    Case "最前"
                        .MoveFirst()
                    Case "上移"
                        .MovePrevious()
                    Case "下移"
                        .MoveNext()
                    Case "最后"
                        .MoveLast()
                End Select
                'Call Data_Show()
            End With
        End If
    End Sub

    Private Sub TextBox_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles C1TextBox1.KeyPress, C1TextBox2.KeyPress, C1TextBox3.KeyPress
        If e.KeyChar <> Chr(Keys.Return) Then Exit Sub
        e.Handled = True
        System.Windows.Forms.SendKeys.Send("{Tab}")
    End Sub

    Private Sub C1TextBox1_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1TextBox1.Validated
        My_Cc.Get_Py(Me.C1TextBox1.Text & "")
        C1TextBox2.Text = My_Cc.简拚.ToString
    End Sub

#End Region

#Region "数据__编辑"

    Private Sub Data_Add()
        Dim My_NewRow As DataRow = RZbtb.NewRow
        With My_NewRow
            My_Cc.Get_MaxCode("Zd_Ml_Yp1", "Dl_Code", 2, "", "")
            .Item("Dl_Code") = My_Cc.编码
            .Item("Dl_Name") = Trim(C1TextBox1.Text & "")
            .Item("Dl_Jc") = Trim(C1TextBox2.Text & "")
            .Item("Dl_Memo") = Trim(C1TextBox3.Text & "")
        End With
        Call Data_SaveAdd(My_NewRow)

        'Treeview1更新
        Dim My_Node As New TreeNode
        With My_Node
            .Tag = L_Dl_Code.Text & ""
            .Text = Me.C1TextBox1.Text & "(0)"
            .ImageIndex = 2
            .SelectedImageIndex = 1
        End With

        With Rtree
            .SelectedNode = .TopNode
            .SelectedNode.Nodes.Add(My_Node)    '增加下一级节点
        End With

        Call Data_Clear()
    End Sub

    Private Sub Data_Edit()
        Dim My_Row As DataRow = Rrow
        With My_Row
            .BeginEdit()
            .Item("Dl_Name") = Trim(C1TextBox1.Text & "")
            .Item("Dl_Jc") = Trim(C1TextBox2.Text & "")
            .Item("Dl_Memo") = Trim(C1TextBox3.Text & "")
            .EndEdit()
        End With

        Dim Para(3) As SqlClient.SqlParameter

        Para(0) = New SqlParameter("@Dl_Name", SqlDbType.VarChar)
        Para(1) = New SqlParameter("@Dl_Jc", SqlDbType.VarChar)
        Para(2) = New SqlParameter("@Dl_Memo", SqlDbType.VarChar)
        Para(3) = New SqlParameter("@Old_Dl_Code", SqlDbType.Char)


        Para(0).Value = My_Row.Item("Dl_Name")
        Para(1).Value = My_Row.Item("Dl_Jc")
        Para(2).Value = My_Row.Item("Dl_Memo")
        Para(3).Value = My_Row.Item("Old_Dl_Code")

        HisVar.HisVar.Sqldal.ExecuteSql("Update Zd_Ml_Yp1 Set Dl_Name=@Dl_Name,Dl_Jc=@Dl_Jc,Dl_Memo=@Dl_Memo Where Dl_Code=@Old_Dl_Code", Para)
        My_Row.AcceptChanges()
        'Treeview1更新
        With Rtree
            .SelectedNode = .TopNode()
            For Each My_Node As TreeNode In .SelectedNode.Nodes
                If Trim(My_Node.Tag) = Trim(L_Dl_Code.Text) Then
                    My_Node.Text = C1TextBox1.Text & "(" & HisVar.HisVar.Sqldal.GetSingle("select count(Yp_Code) as Xm_Count From Zd_Ml_Yp2 Where Dl_Code='" & L_Dl_Code.Text & "'") & ")"
                    Exit For
                End If
            Next
        End With

    End Sub

    Private Sub Data_SaveAdd(ByVal My_Row As DataRow)        '数据保存
        Try
            RZbtb.Rows.Add(My_Row)
            Rtdbgrid.MoveLast()
            Rlb.Text = "∑=" + Rtdbgrid.Splits(0).Rows.Count.ToString
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            C1TextBox1.Select()
            Exit Sub
        End Try

        Dim Para(3) As SqlClient.SqlParameter
        Para(0) = New SqlParameter("@Dl_Code", SqlDbType.Char)
        Para(1) = New SqlParameter("@Dl_Name", SqlDbType.VarChar)
        Para(2) = New SqlParameter("@Dl_Jc", SqlDbType.VarChar)
        Para(3) = New SqlParameter("@Dl_Memo", SqlDbType.VarChar)


        Para(0).Value = My_Row.Item("Dl_Code")
        Para(1).Value = My_Row.Item("Dl_Name")
        Para(2).Value = My_Row.Item("Dl_Jc")
        Para(3).Value = My_Row.Item("Dl_Memo")

        HisVar.HisVar.Sqldal.ExecuteSql("Insert Into Zd_Ml_Yp1(Dl_Code,Dl_Name,Dl_Jc,Dl_Memo)Values(@Dl_Code,@Dl_Name,@Dl_Jc,@Dl_Memo)", Para)

        My_Row.AcceptChanges()
    End Sub


#End Region

#Region "自定义按扭"

    Private Sub Comm_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles Comm1.KeyDown, Comm2.KeyDown
        If e.KeyCode = Keys.Enter Or e.KeyCode = Keys.Space Then My_Button.KeyDown(sender.tag)
    End Sub

    Private Sub Comm_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles Comm1.KeyUp, Comm2.KeyUp
        If e.KeyCode = Keys.Enter Or e.KeyCode = Keys.Space Then My_Button.KeyUp(sender.tag)
    End Sub

    Private Sub Comm_MouseEnter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.MouseEnter, Comm2.MouseEnter
        My_Button.MouseEnter(sender.tag)
    End Sub

    Private Sub Comm_MouseLeave(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.MouseLeave, Comm2.MouseLeave
        My_Button.MouseLeave(sender.tag)
    End Sub

    Private Sub Comm_MouseDown(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm1.MouseDown, Comm2.MouseDown
        My_Button.MouseDown(sender.tag)
    End Sub

    Private Sub Comm_MouseUp(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm1.MouseUp, Comm2.MouseUp
        My_Button.MouseUp(sender.tag)
    End Sub

#End Region

End Class