﻿
Imports System.Windows.Forms

Public Class EmrBasic3


#Region "变量初始化"
    Private My_Table As New DataTable            '药品字典
    Private My_Cm As CurrencyManager             '同步指针
    'Private My_Row As DataRow                    '当 前 行
    'Private V_Insert As Boolean                  '增加记录

    Private BllEmr_BasicElementTree As New BLLOld.B_Emr_BasicElementTree

#End Region
#Region "传递参数"
    Private rRc As New BaseClass.C_RowChange
#End Region

    Public Sub New(ByVal tRc As BaseClass.C_RowChange)

        ' 此调用是设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        rRc = tRc
    End Sub

    Private Sub BedAllocation_Load(sender As System.Object, e As System.EventArgs) Handles MyBase.Load
        Call Form_Init()
        Call Init_Data()
    End Sub


#Region "窗体初始化"

    Private Sub Form_Init()

        '初始化MyGrid1

        With MyGrid1
            .Init_Column("元素编码", "Ele_Code", 0, "左", "", False)
            .Init_Column("元素名称", "Ele_Name", 150, "中", "", False)
            .Init_Column("元素类型", "EleType", 120, "中", "combobox", False)
            .Columns("EleType").ValueItems.Values.Add(New C1.Win.C1TrueDBGrid.ValueItem("0", "目录类型"))
            .Columns("EleType").ValueItems.Values.Add(New C1.Win.C1TrueDBGrid.ValueItem("1", "选择类型"))
            .Columns("EleType").ValueItems.Values.Add(New C1.Win.C1TrueDBGrid.ValueItem("2", "输入类型"))
            .Columns("EleType").ValueItems.Values.Add(New C1.Win.C1TrueDBGrid.ValueItem("3", "数据类型"))
        End With
        NameMyTextBox1.Focus()
    End Sub

    Private Sub Init_Data()

        My_Table = BllEmr_BasicElementTree.GetList("").Tables(0)
        With My_Table
            .PrimaryKey = New DataColumn() {.Columns("Ele_Code")}
        End With
        With Me.MyGrid1
            My_Cm = CType(BindingContext(My_Table, ""), CurrencyManager)
            .DataTable = My_Table
        End With

    End Sub


#End Region


#Region "控件动作"
    Private Sub NameMyTextBox1_TextChanged(sender As Object, e As System.EventArgs) Handles NameMyTextBox1.TextChanged
        Dim view As DataView = My_Cm.List
        view.RowFilter = "Ele_Name like '*" & NameMyTextBox1.Text & "*'"
    End Sub

    Private Sub MyGrid1_DoubleClick(sender As Object, e As System.EventArgs) Handles MyGrid1.DoubleClick
        If MyGrid1.RowCount > 0 Then
            rRc.AddChange(MyGrid1.Columns("Ele_code").Value)
            Me.Close()
        End If
    End Sub
#End Region




End Class