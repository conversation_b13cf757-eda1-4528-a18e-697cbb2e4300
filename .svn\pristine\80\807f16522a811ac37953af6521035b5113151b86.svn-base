﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class Cq_Cf2
    Inherits HisControl.BaseForm

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(Cq_Cf2))
        Me.C1Holder1 = New C1.Win.C1Command.C1CommandHolder()
        Me.C1Command1 = New C1.Win.C1Command.C1Command()
        Me.C1Command2 = New C1.Win.C1Command.C1Command()
        Me.Panel2 = New System.Windows.Forms.Panel()
        Me.comm4 = New System.Windows.Forms.Button()
        Me.Comm2 = New System.Windows.Forms.Button()
        Me.Comm1 = New System.Windows.Forms.Button()
        Me.T_Line1 = New System.Windows.Forms.Label()
        Me.C1ToolBar2 = New C1.Win.C1Command.C1ToolBar()
        Me.Label16 = New System.Windows.Forms.Label()
        Me.T_Label4 = New System.Windows.Forms.Label()
        Me.T_Label5 = New System.Windows.Forms.Label()
        Me.T_Line2 = New System.Windows.Forms.Label()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel()
        Me.Label11 = New System.Windows.Forms.Label()
        Me.Label12 = New System.Windows.Forms.Label()
        Me.Label7 = New System.Windows.Forms.Label()
        Me.C1Combo3 = New C1.Win.C1List.C1Combo()
        Me.Label20 = New System.Windows.Forms.Label()
        Me.Label18 = New System.Windows.Forms.Label()
        Me.Label6 = New System.Windows.Forms.Label()
        Me.Label22 = New System.Windows.Forms.Label()
        Me.Label10 = New System.Windows.Forms.Label()
        Me.GroupBox1 = New System.Windows.Forms.GroupBox()
        Me.YfComobo1 = New CustomControl.MyDtComobo()
        Me.C1ToolBar1 = New C1.Win.C1Command.C1ToolBar()
        Me.C1CommandLink1 = New C1.Win.C1Command.C1CommandLink()
        Me.C1CommandLink2 = New C1.Win.C1Command.C1CommandLink()
        Me.C1NumericEdit1 = New C1.Win.C1Input.C1NumericEdit()
        Me.Label8 = New System.Windows.Forms.Label()
        Me.RadioButton4 = New System.Windows.Forms.RadioButton()
        Me.RadioButton1 = New System.Windows.Forms.RadioButton()
        Me.Label17 = New System.Windows.Forms.Label()
        Me.DoctorCombo = New CustomControl.MyDtComobo()
        Me.KsCombo = New CustomControl.MyDtComobo()
        Me.StartDateEdit = New CustomControl.MyDateEdit()
        Me.EndDateEdit = New CustomControl.MyDateEdit()
        Me.Label5 = New System.Windows.Forms.Label()
        Me.IntervalNumEdit = New CustomControl.MyNumericEdit()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.TimesNumEdit = New CustomControl.MyNumericEdit()
        Me.FirstDateEdit = New CustomControl.MyDateEdit()
        Me.C1TrueDBGrid1 = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        CType(Me.C1Holder1,System.ComponentModel.ISupportInitialize).BeginInit
        Me.Panel2.SuspendLayout
        Me.Panel1.SuspendLayout
        Me.TableLayoutPanel1.SuspendLayout
        CType(Me.C1Combo3,System.ComponentModel.ISupportInitialize).BeginInit
        Me.GroupBox1.SuspendLayout
        CType(Me.C1NumericEdit1,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.C1TrueDBGrid1,System.ComponentModel.ISupportInitialize).BeginInit
        Me.SuspendLayout
        '
        'C1Holder1
        '
        Me.C1Holder1.Commands.Add(Me.C1Command1)
        Me.C1Holder1.Commands.Add(Me.C1Command2)
        Me.C1Holder1.Owner = Me
        Me.C1Holder1.VisualStyle = C1.Win.C1Command.VisualStyle.Classic
        '
        'C1Command1
        '
        Me.C1Command1.Name = "C1Command1"
        Me.C1Command1.ShortcutText = ""
        Me.C1Command1.Text = "乘"
        '
        'C1Command2
        '
        Me.C1Command2.Name = "C1Command2"
        Me.C1Command2.ShortcutText = ""
        Me.C1Command2.Text = "除"
        '
        'Panel2
        '
        Me.Panel2.Controls.Add(Me.comm4)
        Me.Panel2.Controls.Add(Me.Comm2)
        Me.Panel2.Controls.Add(Me.Comm1)
        Me.Panel2.Controls.Add(Me.T_Line1)
        Me.Panel2.Controls.Add(Me.C1ToolBar2)
        Me.Panel2.Controls.Add(Me.Label16)
        Me.Panel2.Controls.Add(Me.T_Label4)
        Me.Panel2.Controls.Add(Me.T_Label5)
        Me.Panel2.Controls.Add(Me.T_Line2)
        Me.Panel2.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.Panel2.Location = New System.Drawing.Point(0, 523)
        Me.Panel2.Name = "Panel2"
        Me.Panel2.Size = New System.Drawing.Size(958, 30)
        Me.Panel2.TabIndex = 37
        '
        'comm4
        '
        Me.comm4.Location = New System.Drawing.Point(452, 3)
        Me.comm4.Name = "comm4"
        Me.comm4.Size = New System.Drawing.Size(70, 24)
        Me.comm4.TabIndex = 55
        Me.comm4.Tag = "处置单"
        Me.comm4.Text = "处置单"
        Me.comm4.UseVisualStyleBackColor = false
        '
        'Comm2
        '
        Me.Comm2.DialogResult = System.Windows.Forms.DialogResult.Cancel
        Me.Comm2.Location = New System.Drawing.Point(400, 3)
        Me.Comm2.Name = "Comm2"
        Me.Comm2.Size = New System.Drawing.Size(50, 24)
        Me.Comm2.TabIndex = 1
        Me.Comm2.Tag = "取消"
        Me.Comm2.Text = "取消"
        Me.Comm2.UseVisualStyleBackColor = false
        '
        'Comm1
        '
        Me.Comm1.Location = New System.Drawing.Point(348, 3)
        Me.Comm1.Name = "Comm1"
        Me.Comm1.Size = New System.Drawing.Size(50, 24)
        Me.Comm1.TabIndex = 0
        Me.Comm1.Tag = "保存"
        Me.Comm1.Text = "保存"
        Me.Comm1.UseVisualStyleBackColor = false
        '
        'T_Line1
        '
        Me.T_Line1.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.T_Line1.Location = New System.Drawing.Point(269, -5)
        Me.T_Line1.Name = "T_Line1"
        Me.T_Line1.Size = New System.Drawing.Size(2, 40)
        Me.T_Line1.TabIndex = 42
        '
        'C1ToolBar2
        '
        Me.C1ToolBar2.AccessibleName = "Tool Bar"
        Me.C1ToolBar2.ButtonLookHorz = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image),C1.Win.C1Command.ButtonLookFlags)
        Me.C1ToolBar2.CommandHolder = Me.C1Holder1
        Me.C1ToolBar2.Location = New System.Drawing.Point(222, 3)
        Me.C1ToolBar2.Name = "C1ToolBar2"
        Me.C1ToolBar2.Size = New System.Drawing.Size(24, 24)
        Me.C1ToolBar2.Text = "C1ToolBar2"
        Me.C1ToolBar2.VisualStyle = C1.Win.C1Command.VisualStyle.Classic
        Me.C1ToolBar2.VisualStyleBase = C1.Win.C1Command.VisualStyle.Classic
        '
        'Label16
        '
        Me.Label16.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.Label16.Location = New System.Drawing.Point(213, -4)
        Me.Label16.Name = "Label16"
        Me.Label16.Size = New System.Drawing.Size(2, 40)
        Me.Label16.TabIndex = 38
        '
        'T_Label4
        '
        Me.T_Label4.AutoSize = true
        Me.T_Label4.Location = New System.Drawing.Point(550, 9)
        Me.T_Label4.Name = "T_Label4"
        Me.T_Label4.Size = New System.Drawing.Size(47, 12)
        Me.T_Label4.TabIndex = 36
        Me.T_Label4.Text = "∑本日="
        Me.T_Label4.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'T_Label5
        '
        Me.T_Label5.AutoSize = true
        Me.T_Label5.BackColor = System.Drawing.SystemColors.ButtonFace
        Me.T_Label5.ForeColor = System.Drawing.Color.DarkRed
        Me.T_Label5.Location = New System.Drawing.Point(603, 9)
        Me.T_Label5.Name = "T_Label5"
        Me.T_Label5.Size = New System.Drawing.Size(53, 12)
        Me.T_Label5.TabIndex = 35
        Me.T_Label5.Text = "T_Label5"
        Me.T_Label5.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'T_Line2
        '
        Me.T_Line2.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.T_Line2.Location = New System.Drawing.Point(526, -5)
        Me.T_Line2.Name = "T_Line2"
        Me.T_Line2.Size = New System.Drawing.Size(2, 40)
        Me.T_Line2.TabIndex = 29
        '
        'Panel1
        '
        Me.Panel1.Controls.Add(Me.TableLayoutPanel1)
        Me.Panel1.Dock = System.Windows.Forms.DockStyle.Top
        Me.Panel1.Location = New System.Drawing.Point(0, 0)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(958, 174)
        Me.Panel1.TabIndex = 39
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.ColumnCount = 11
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 10!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 60!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 140!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 60!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 140!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 60!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 150!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 61!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 55!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 199!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 49!))
        Me.TableLayoutPanel1.Controls.Add(Me.Label11, 1, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.Label12, 2, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.Label7, 1, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.C1Combo3, 2, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.Label20, 2, 3)
        Me.TableLayoutPanel1.Controls.Add(Me.Label18, 1, 3)
        Me.TableLayoutPanel1.Controls.Add(Me.Label6, 3, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.Label22, 5, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.Label10, 4, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.GroupBox1, 9, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.Label17, 6, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.DoctorCombo, 1, 4)
        Me.TableLayoutPanel1.Controls.Add(Me.KsCombo, 5, 4)
        Me.TableLayoutPanel1.Controls.Add(Me.StartDateEdit, 1, 5)
        Me.TableLayoutPanel1.Controls.Add(Me.EndDateEdit, 3, 5)
        Me.TableLayoutPanel1.Controls.Add(Me.Label5, 5, 6)
        Me.TableLayoutPanel1.Controls.Add(Me.IntervalNumEdit, 6, 6)
        Me.TableLayoutPanel1.Controls.Add(Me.Label4, 8, 6)
        Me.TableLayoutPanel1.Controls.Add(Me.TimesNumEdit, 3, 6)
        Me.TableLayoutPanel1.Controls.Add(Me.FirstDateEdit, 1, 6)
        Me.TableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(0, 0)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.RowCount = 7
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 15!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 26!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 26!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 26!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 26!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 20!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 20!))
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(958, 174)
        Me.TableLayoutPanel1.TabIndex = 40
        '
        'Label11
        '
        Me.Label11.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom)  _
            Or System.Windows.Forms.AnchorStyles.Left)  _
            Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.Label11.AutoSize = true
        Me.Label11.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label11.Location = New System.Drawing.Point(13, 15)
        Me.Label11.Name = "Label11"
        Me.Label11.Size = New System.Drawing.Size(54, 26)
        Me.Label11.TabIndex = 158
        Me.Label11.Text = "医嘱编码"
        Me.Label11.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Label12
        '
        Me.Label12.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.Label12.BackColor = System.Drawing.SystemColors.Info
        Me.Label12.Location = New System.Drawing.Point(73, 20)
        Me.Label12.Name = "Label12"
        Me.Label12.Size = New System.Drawing.Size(134, 16)
        Me.Label12.TabIndex = 159
        Me.Label12.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'Label7
        '
        Me.Label7.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom)  _
            Or System.Windows.Forms.AnchorStyles.Left)  _
            Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.Label7.AutoSize = true
        Me.Label7.Location = New System.Drawing.Point(13, 41)
        Me.Label7.Name = "Label7"
        Me.Label7.Size = New System.Drawing.Size(54, 26)
        Me.Label7.TabIndex = 180
        Me.Label7.Text = "患    者"
        Me.Label7.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'C1Combo3
        '
        Me.C1Combo3.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.C1Combo3.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1Combo3.Caption = ""
        Me.C1Combo3.CaptionHeight = 17
        Me.C1Combo3.CharacterCasing = System.Windows.Forms.CharacterCasing.Normal
        Me.C1Combo3.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.C1Combo3.Images.Add(CType(resources.GetObject("C1Combo3.Images"),System.Drawing.Image))
        Me.C1Combo3.ItemHeight = 15
        Me.C1Combo3.Location = New System.Drawing.Point(73, 46)
        Me.C1Combo3.MatchEntryTimeout = CType(2000,Long)
        Me.C1Combo3.MaxDropDownItems = CType(5,Short)
        Me.C1Combo3.MaxLength = 32767
        Me.C1Combo3.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.C1Combo3.Name = "C1Combo3"
        Me.C1Combo3.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.C1Combo3.Size = New System.Drawing.Size(134, 16)
        Me.C1Combo3.TabIndex = 0
        Me.C1Combo3.PropBag = resources.GetString("C1Combo3.PropBag")
        '
        'Label20
        '
        Me.Label20.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.Label20.BackColor = System.Drawing.SystemColors.Info
        Me.TableLayoutPanel1.SetColumnSpan(Me.Label20, 7)
        Me.Label20.Location = New System.Drawing.Point(73, 71)
        Me.Label20.Name = "Label20"
        Me.Label20.Size = New System.Drawing.Size(660, 17)
        Me.Label20.TabIndex = 171
        Me.Label20.TextAlign = System.Drawing.ContentAlignment.BottomLeft
        '
        'Label18
        '
        Me.Label18.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom)  _
            Or System.Windows.Forms.AnchorStyles.Left)  _
            Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.Label18.AutoSize = true
        Me.Label18.Location = New System.Drawing.Point(13, 67)
        Me.Label18.Name = "Label18"
        Me.Label18.Size = New System.Drawing.Size(54, 26)
        Me.Label18.TabIndex = 172
        Me.Label18.Text = "疾病全称"
        Me.Label18.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Label6
        '
        Me.Label6.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom)  _
            Or System.Windows.Forms.AnchorStyles.Left)  _
            Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.Label6.AutoSize = true
        Me.Label6.Location = New System.Drawing.Point(213, 41)
        Me.Label6.Name = "Label6"
        Me.Label6.Size = New System.Drawing.Size(54, 26)
        Me.Label6.TabIndex = 77
        Me.Label6.Text = "性别"
        Me.Label6.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Label22
        '
        Me.Label22.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom)  _
            Or System.Windows.Forms.AnchorStyles.Left)  _
            Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.Label22.AutoSize = true
        Me.Label22.Location = New System.Drawing.Point(413, 41)
        Me.Label22.Name = "Label22"
        Me.Label22.Size = New System.Drawing.Size(54, 26)
        Me.Label22.TabIndex = 176
        Me.Label22.Text = "床    位"
        Me.Label22.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Label10
        '
        Me.Label10.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.Label10.BackColor = System.Drawing.SystemColors.Info
        Me.Label10.Location = New System.Drawing.Point(273, 45)
        Me.Label10.Name = "Label10"
        Me.Label10.Size = New System.Drawing.Size(134, 17)
        Me.Label10.TabIndex = 175
        Me.Label10.TextAlign = System.Drawing.ContentAlignment.BottomLeft
        '
        'GroupBox1
        '
        Me.GroupBox1.Controls.Add(Me.YfComobo1)
        Me.GroupBox1.Controls.Add(Me.C1ToolBar1)
        Me.GroupBox1.Controls.Add(Me.C1NumericEdit1)
        Me.GroupBox1.Controls.Add(Me.Label8)
        Me.GroupBox1.Controls.Add(Me.RadioButton4)
        Me.GroupBox1.Controls.Add(Me.RadioButton1)
        Me.GroupBox1.Location = New System.Drawing.Point(739, 3)
        Me.GroupBox1.Name = "GroupBox1"
        Me.TableLayoutPanel1.SetRowSpan(Me.GroupBox1, 7)
        Me.GroupBox1.Size = New System.Drawing.Size(193, 163)
        Me.GroupBox1.TabIndex = 69
        Me.GroupBox1.TabStop = false
        Me.GroupBox1.Text = "药材类别"
        '
        'YfComobo1
        '
        Me.YfComobo1.BackColor = System.Drawing.SystemColors.Control
        Me.YfComobo1.Bookmark = -1
        Me.YfComobo1.Captain = "药  房"
        Me.YfComobo1.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.YfComobo1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.YfComobo1.CaptainWidth = 60!
        Me.YfComobo1.DataSource = Nothing
        Me.YfComobo1.ItemHeight = 16
        Me.YfComobo1.ItemTextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.YfComobo1.Location = New System.Drawing.Point(1, 39)
        Me.YfComobo1.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.YfComobo1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.YfComobo1.Name = "YfComobo1"
        Me.YfComobo1.ReadOnly = false
        Me.YfComobo1.Row = 0
        Me.YfComobo1.Size = New System.Drawing.Size(187, 20)
        Me.YfComobo1.TabIndex = 0
        Me.YfComobo1.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        '
        'C1ToolBar1
        '
        Me.C1ToolBar1.AccessibleName = "Tool Bar"
        Me.C1ToolBar1.Border.Style = C1.Win.C1Command.BorderStyleEnum.Ridge
        Me.C1ToolBar1.CommandHolder = Me.C1Holder1
        Me.C1ToolBar1.CommandLinks.AddRange(New C1.Win.C1Command.C1CommandLink() {Me.C1CommandLink1, Me.C1CommandLink2})
        Me.C1ToolBar1.Location = New System.Drawing.Point(136, 115)
        Me.C1ToolBar1.Name = "C1ToolBar1"
        Me.C1ToolBar1.Size = New System.Drawing.Size(52, 28)
        Me.C1ToolBar1.Text = "C1ToolBar1"
        Me.C1ToolBar1.VisualStyle = C1.Win.C1Command.VisualStyle.Classic
        Me.C1ToolBar1.VisualStyleBase = C1.Win.C1Command.VisualStyle.Classic
        '
        'C1CommandLink1
        '
        Me.C1CommandLink1.ButtonLook = C1.Win.C1Command.ButtonLookFlags.Text
        Me.C1CommandLink1.Command = Me.C1Command1
        '
        'C1CommandLink2
        '
        Me.C1CommandLink2.ButtonLook = C1.Win.C1Command.ButtonLookFlags.Text
        Me.C1CommandLink2.Command = Me.C1Command2
        Me.C1CommandLink2.SortOrder = 1
        '
        'C1NumericEdit1
        '
        Me.C1NumericEdit1.AutoSize = false
        Me.C1NumericEdit1.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1NumericEdit1.ImagePadding = New System.Windows.Forms.Padding(0)
        Me.C1NumericEdit1.Location = New System.Drawing.Point(66, 119)
        Me.C1NumericEdit1.Name = "C1NumericEdit1"
        Me.C1NumericEdit1.Size = New System.Drawing.Size(64, 16)
        Me.C1NumericEdit1.TabIndex = 3
        Me.C1NumericEdit1.Tag = Nothing
        Me.C1NumericEdit1.TextAlign = System.Windows.Forms.HorizontalAlignment.Right
        Me.C1NumericEdit1.VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.None
        '
        'Label8
        '
        Me.Label8.AutoSize = true
        Me.Label8.Location = New System.Drawing.Point(8, 121)
        Me.Label8.Name = "Label8"
        Me.Label8.Size = New System.Drawing.Size(53, 12)
        Me.Label8.TabIndex = 3
        Me.Label8.Text = "草药副数"
        '
        'RadioButton4
        '
        Me.RadioButton4.AutoSize = true
        Me.RadioButton4.Location = New System.Drawing.Point(12, 74)
        Me.RadioButton4.Name = "RadioButton4"
        Me.RadioButton4.Size = New System.Drawing.Size(83, 16)
        Me.RadioButton4.TabIndex = 4
        Me.RadioButton4.Text = "&1.诊疗项目"
        Me.RadioButton4.UseVisualStyleBackColor = true
        '
        'RadioButton1
        '
        Me.RadioButton1.AutoSize = true
        Me.RadioButton1.Checked = true
        Me.RadioButton1.Location = New System.Drawing.Point(104, 74)
        Me.RadioButton1.Name = "RadioButton1"
        Me.RadioButton1.Size = New System.Drawing.Size(83, 16)
        Me.RadioButton1.TabIndex = 0
        Me.RadioButton1.TabStop = true
        Me.RadioButton1.Text = "&2.药品卫材"
        Me.RadioButton1.UseVisualStyleBackColor = true
        '
        'Label17
        '
        Me.Label17.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.Label17.BackColor = System.Drawing.SystemColors.Info
        Me.TableLayoutPanel1.SetColumnSpan(Me.Label17, 3)
        Me.Label17.Location = New System.Drawing.Point(473, 45)
        Me.Label17.Name = "Label17"
        Me.Label17.Size = New System.Drawing.Size(260, 17)
        Me.Label17.TabIndex = 177
        Me.Label17.TextAlign = System.Drawing.ContentAlignment.BottomLeft
        '
        'DoctorCombo
        '
        Me.DoctorCombo.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.DoctorCombo.Bookmark = -1
        Me.DoctorCombo.Captain = "医生名称"
        Me.DoctorCombo.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.DoctorCombo.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.DoctorCombo.CaptainWidth = 60!
        Me.TableLayoutPanel1.SetColumnSpan(Me.DoctorCombo, 4)
        Me.DoctorCombo.Cursor = System.Windows.Forms.Cursors.Default
        Me.DoctorCombo.DataSource = Nothing
        Me.DoctorCombo.ItemHeight = 16
        Me.DoctorCombo.ItemTextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.DoctorCombo.Location = New System.Drawing.Point(13, 96)
        Me.DoctorCombo.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.DoctorCombo.MinimumSize = New System.Drawing.Size(0, 20)
        Me.DoctorCombo.Name = "DoctorCombo"
        Me.DoctorCombo.ReadOnly = false
        Me.DoctorCombo.Row = 0
        Me.DoctorCombo.Size = New System.Drawing.Size(394, 20)
        Me.DoctorCombo.TabIndex = 1
        Me.DoctorCombo.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        '
        'KsCombo
        '
        Me.KsCombo.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.KsCombo.Bookmark = -1
        Me.KsCombo.Captain = "科室名称"
        Me.KsCombo.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.KsCombo.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.KsCombo.CaptainWidth = 60!
        Me.TableLayoutPanel1.SetColumnSpan(Me.KsCombo, 4)
        Me.KsCombo.Cursor = System.Windows.Forms.Cursors.Default
        Me.KsCombo.DataSource = Nothing
        Me.KsCombo.ItemHeight = 16
        Me.KsCombo.ItemTextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.KsCombo.Location = New System.Drawing.Point(413, 96)
        Me.KsCombo.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.KsCombo.MinimumSize = New System.Drawing.Size(0, 20)
        Me.KsCombo.Name = "KsCombo"
        Me.KsCombo.ReadOnly = false
        Me.KsCombo.Row = 0
        Me.KsCombo.Size = New System.Drawing.Size(320, 20)
        Me.KsCombo.TabIndex = 2
        Me.KsCombo.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        '
        'StartDateEdit
        '
        Me.StartDateEdit.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.StartDateEdit.Captain = "开始时间"
        Me.StartDateEdit.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.StartDateEdit.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.StartDateEdit.CaptainWidth = 60!
        Me.TableLayoutPanel1.SetColumnSpan(Me.StartDateEdit, 2)
        Me.StartDateEdit.InitialSelection = C1.Win.C1Input.InitialSelectionEnum.CaretAtEnd
        Me.StartDateEdit.Location = New System.Drawing.Point(13, 122)
        Me.StartDateEdit.MaximumSize = New System.Drawing.Size(100000000, 20)
        Me.StartDateEdit.MinimumSize = New System.Drawing.Size(0, 20)
        Me.StartDateEdit.Name = "StartDateEdit"
        Me.StartDateEdit.ReadOnly = false
        Me.StartDateEdit.Size = New System.Drawing.Size(194, 20)
        Me.StartDateEdit.TabIndex = 3
        Me.StartDateEdit.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.StartDateEdit.ValueIsDbNull = false
        Me.StartDateEdit.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.StartDateEdit.VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.DropDown
        '
        'EndDateEdit
        '
        Me.EndDateEdit.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.EndDateEdit.Captain = "结束时间"
        Me.EndDateEdit.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.EndDateEdit.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.EndDateEdit.CaptainWidth = 60!
        Me.TableLayoutPanel1.SetColumnSpan(Me.EndDateEdit, 2)
        Me.EndDateEdit.InitialSelection = C1.Win.C1Input.InitialSelectionEnum.CaretAtEnd
        Me.EndDateEdit.Location = New System.Drawing.Point(213, 122)
        Me.EndDateEdit.MaximumSize = New System.Drawing.Size(100000000, 20)
        Me.EndDateEdit.MinimumSize = New System.Drawing.Size(0, 20)
        Me.EndDateEdit.Name = "EndDateEdit"
        Me.EndDateEdit.ReadOnly = false
        Me.EndDateEdit.Size = New System.Drawing.Size(194, 20)
        Me.EndDateEdit.TabIndex = 4
        Me.EndDateEdit.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.EndDateEdit.ValueIsDbNull = false
        Me.EndDateEdit.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.EndDateEdit.VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.DropDown
        '
        'Label5
        '
        Me.Label5.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom)  _
            Or System.Windows.Forms.AnchorStyles.Left)  _
            Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.Label5.AutoSize = true
        Me.Label5.ForeColor = System.Drawing.Color.Maroon
        Me.Label5.Location = New System.Drawing.Point(413, 139)
        Me.Label5.Name = "Label5"
        Me.Label5.Size = New System.Drawing.Size(54, 35)
        Me.Label5.TabIndex = 7
        Me.Label5.Text = "次/每天"
        Me.Label5.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'IntervalNumEdit
        '
        Me.IntervalNumEdit.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.IntervalNumEdit.Captain = "执行间隔"
        Me.IntervalNumEdit.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.IntervalNumEdit.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.IntervalNumEdit.CaptainWidth = 60!
        Me.TableLayoutPanel1.SetColumnSpan(Me.IntervalNumEdit, 2)
        Me.IntervalNumEdit.Location = New System.Drawing.Point(473, 146)
        Me.IntervalNumEdit.MaximumSize = New System.Drawing.Size(100000, 20)
        Me.IntervalNumEdit.MinimumSize = New System.Drawing.Size(0, 20)
        Me.IntervalNumEdit.Name = "IntervalNumEdit"
        Me.IntervalNumEdit.NumFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.IntervalNumEdit.ReadOnly = false
        Me.IntervalNumEdit.Size = New System.Drawing.Size(205, 20)
        Me.IntervalNumEdit.TabIndex = 8
        Me.IntervalNumEdit.ValueIsDbNull = false
        '
        'Label4
        '
        Me.Label4.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom)  _
            Or System.Windows.Forms.AnchorStyles.Left)  _
            Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.Label4.AutoSize = true
        Me.Label4.ForeColor = System.Drawing.Color.Maroon
        Me.Label4.Location = New System.Drawing.Point(684, 139)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(49, 35)
        Me.Label4.TabIndex = 189
        Me.Label4.Text = "小时"
        Me.Label4.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'TimesNumEdit
        '
        Me.TimesNumEdit.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.TimesNumEdit.Captain = "执行频率"
        Me.TimesNumEdit.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.TimesNumEdit.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.TimesNumEdit.CaptainWidth = 60!
        Me.TableLayoutPanel1.SetColumnSpan(Me.TimesNumEdit, 2)
        Me.TimesNumEdit.Location = New System.Drawing.Point(213, 146)
        Me.TimesNumEdit.MaximumSize = New System.Drawing.Size(100000, 20)
        Me.TimesNumEdit.MinimumSize = New System.Drawing.Size(0, 20)
        Me.TimesNumEdit.Name = "TimesNumEdit"
        Me.TimesNumEdit.NumFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.TimesNumEdit.ReadOnly = false
        Me.TimesNumEdit.Size = New System.Drawing.Size(194, 20)
        Me.TimesNumEdit.TabIndex = 6
        Me.TimesNumEdit.ValueIsDbNull = false
        '
        'FirstDateEdit
        '
        Me.FirstDateEdit.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.FirstDateEdit.Captain = "首次时间"
        Me.FirstDateEdit.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.FirstDateEdit.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.FirstDateEdit.CaptainWidth = 60!
        Me.TableLayoutPanel1.SetColumnSpan(Me.FirstDateEdit, 2)
        Me.FirstDateEdit.InitialSelection = C1.Win.C1Input.InitialSelectionEnum.CaretAtEnd
        Me.FirstDateEdit.Location = New System.Drawing.Point(13, 146)
        Me.FirstDateEdit.MaximumSize = New System.Drawing.Size(100000000, 20)
        Me.FirstDateEdit.MinimumSize = New System.Drawing.Size(0, 20)
        Me.FirstDateEdit.Name = "FirstDateEdit"
        Me.FirstDateEdit.ReadOnly = false
        Me.FirstDateEdit.Size = New System.Drawing.Size(194, 20)
        Me.FirstDateEdit.TabIndex = 5
        Me.FirstDateEdit.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.FirstDateEdit.ValueIsDbNull = false
        Me.FirstDateEdit.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.FirstDateEdit.VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.DropDown
        '
        'C1TrueDBGrid1
        '
        Me.C1TrueDBGrid1.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.C1TrueDBGrid1.GroupByCaption = "Drag a column header here to group by that column"
        Me.C1TrueDBGrid1.Images.Add(CType(resources.GetObject("C1TrueDBGrid1.Images"),System.Drawing.Image))
        Me.C1TrueDBGrid1.Location = New System.Drawing.Point(5, 324)
        Me.C1TrueDBGrid1.Name = "C1TrueDBGrid1"
        Me.C1TrueDBGrid1.PreviewInfo.Caption = "PrintPreview窗口"
        Me.C1TrueDBGrid1.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.C1TrueDBGrid1.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.C1TrueDBGrid1.PreviewInfo.ZoomFactor = 75R
        Me.C1TrueDBGrid1.PrintInfo.MeasurementDevice = C1.Win.C1TrueDBGrid.PrintInfo.MeasurementDeviceEnum.Screen
        Me.C1TrueDBGrid1.PrintInfo.MeasurementPrinterName = Nothing
        Me.C1TrueDBGrid1.Size = New System.Drawing.Size(405, 153)
        Me.C1TrueDBGrid1.TabIndex = 0
        Me.C1TrueDBGrid1.Text = "C1TrueDBGrid1"
        Me.C1TrueDBGrid1.PropBag = resources.GetString("C1TrueDBGrid1.PropBag")
        '
        'Cq_Cf2
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6!, 12!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(958, 553)
        Me.Controls.Add(Me.C1TrueDBGrid1)
        Me.Controls.Add(Me.Panel1)
        Me.Controls.Add(Me.Panel2)
        Me.Icon = CType(resources.GetObject("$this.Icon"),System.Drawing.Icon)
        Me.Name = "Cq_Cf2"
        Me.ShowInTaskbar = false
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "长期医嘱"
        CType(Me.C1Holder1,System.ComponentModel.ISupportInitialize).EndInit
        Me.Panel2.ResumeLayout(false)
        Me.Panel2.PerformLayout
        Me.Panel1.ResumeLayout(false)
        Me.TableLayoutPanel1.ResumeLayout(false)
        Me.TableLayoutPanel1.PerformLayout
        CType(Me.C1Combo3,System.ComponentModel.ISupportInitialize).EndInit
        Me.GroupBox1.ResumeLayout(false)
        Me.GroupBox1.PerformLayout
        CType(Me.C1NumericEdit1,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.C1TrueDBGrid1,System.ComponentModel.ISupportInitialize).EndInit
        Me.ResumeLayout(false)

End Sub
    Friend WithEvents C1Holder1 As C1.Win.C1Command.C1CommandHolder
    Friend WithEvents Panel2 As System.Windows.Forms.Panel
    Friend WithEvents T_Label4 As System.Windows.Forms.Label
    Friend WithEvents T_Label5 As System.Windows.Forms.Label
    Friend WithEvents T_Line2 As System.Windows.Forms.Label
    Friend WithEvents C1Command1 As C1.Win.C1Command.C1Command
    Friend WithEvents C1Command2 As C1.Win.C1Command.C1Command
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents Label6 As System.Windows.Forms.Label
    Friend WithEvents GroupBox1 As System.Windows.Forms.GroupBox
    Friend WithEvents RadioButton4 As System.Windows.Forms.RadioButton
    Friend WithEvents RadioButton1 As System.Windows.Forms.RadioButton
    Friend WithEvents Label12 As System.Windows.Forms.Label
    Friend WithEvents Label11 As System.Windows.Forms.Label
    Friend WithEvents C1Combo3 As C1.Win.C1List.C1Combo
    Friend WithEvents C1TrueDBGrid1 As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents C1ToolBar2 As C1.Win.C1Command.C1ToolBar
    Friend WithEvents Label16 As System.Windows.Forms.Label
    Friend WithEvents Label18 As System.Windows.Forms.Label
    Friend WithEvents Label20 As System.Windows.Forms.Label
    Friend WithEvents C1ToolBar1 As C1.Win.C1Command.C1ToolBar
    Friend WithEvents C1CommandLink1 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents C1CommandLink2 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents C1NumericEdit1 As C1.Win.C1Input.C1NumericEdit
    Friend WithEvents Label8 As System.Windows.Forms.Label
    Friend WithEvents T_Line1 As System.Windows.Forms.Label
    Friend WithEvents Comm2 As System.Windows.Forms.Button
    Friend WithEvents Comm1 As System.Windows.Forms.Button
    Friend WithEvents Label10 As System.Windows.Forms.Label
    Friend WithEvents Label17 As System.Windows.Forms.Label
    Friend WithEvents Label22 As System.Windows.Forms.Label
    Friend WithEvents Label7 As System.Windows.Forms.Label
    Friend WithEvents TableLayoutPanel1 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents DoctorCombo As CustomControl.MyDtComobo
    Friend WithEvents KsCombo As CustomControl.MyDtComobo
    Friend WithEvents YfComobo1 As CustomControl.MyDtComobo
    Friend WithEvents Label4 As System.Windows.Forms.Label
    Friend WithEvents Label5 As System.Windows.Forms.Label
    Friend WithEvents TimesNumEdit As CustomControl.MyNumericEdit
    Friend WithEvents IntervalNumEdit As CustomControl.MyNumericEdit
    Friend WithEvents FirstDateEdit As CustomControl.MyDateEdit
    Friend WithEvents StartDateEdit As CustomControl.MyDateEdit
    Friend WithEvents EndDateEdit As CustomControl.MyDateEdit
    Friend WithEvents comm4 As System.Windows.Forms.Button
End Class
