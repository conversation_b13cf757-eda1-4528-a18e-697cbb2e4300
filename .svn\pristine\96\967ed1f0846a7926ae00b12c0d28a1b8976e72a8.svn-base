﻿Imports BaseClass
Imports System.Windows.Forms
Imports System.Drawing

Public Class ZkDj2

#Region "变量定义"
    Dim V_Name As String                                            '简称是否发生变化
    Dim V_LbCount As Integer

    Dim Emr_ZkDjModel As New ModelOld.M_Emr_ZkDj
    Dim Emr_ZkDjBll As New BLLOld.B_Emr_ZkDj
    Dim My_Cc As New BaseClass.C_Cc

    Dim C_Py As New BaseClass.Chs2Spell
    Dim mark As Integer = 1
#End Region

#Region "传参"
    Dim Rinsert As Boolean
    Dim Rrow As DataRow
    Dim RZbtb As DataTable
    Dim Rrc As C_RowChange
    Dim Rrow1 As DataRow
#End Region
    Public Sub New(ByVal tinsert As Boolean, ByVal trow As DataRow, ByVal tZbtb As DataTable, ByRef trc As C_RowChange, ByVal trow1 As DataRow)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        Rinsert = tinsert
        Rrow = trow
        RZbtb = tZbtb
        Rrc = trc
        Rrow1 = trow1

        ' 在 InitializeComponent() 调用之后添加任何初始化。
    End Sub

    Private Sub ZkDj2_FormClosed(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosedEventArgs) Handles Me.FormClosed
        RemoveHandler Rrc.RowChanged, AddressOf Data_Show
    End Sub

    Private Sub ZkDj2_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Me.Load
        AddHandler Rrc.RowChanged, AddressOf Data_Show
        Call Form_Init()
        If Rinsert = True Then Call Data_Clear(Rrow1) Else Call Data_Show(Rrow)


    End Sub

#Region "窗体__事件"

    Private Sub Form_Init()
        Panel1.Height = 27
        ToolBar1.Location = New Point(2, 4)

        '按扭初始化
        Comm1.Top = 2
        Comm2.Location = New Point(Comm1.Left + Comm1.Width + 2, Comm1.Top)

        Code_TextBox.Enabled = False
        Jc_TextBox.Enabled = False

        'If Rinsert = False Then
        '    MaxValue_TextBox.Enabled = False
        'End If

        MaxValue_TextBox.Enabled = False
        Kf_TextBox.Enabled = False


    End Sub

#End Region

#Region "数据__操作"

    Private Sub Data_Clear(ByVal tmp_Row As DataRow)
        Rinsert = True
        Rrow1 = tmp_Row
        My_Cc.Get_MaxCode("Emr_ZkDj", "ZkDj_Code", 2, "", "")
        Code_TextBox.Text = My_Cc.编码                   '最大编码

        If Code_TextBox.Text = "01" Then
            ' If Rrow1 Is Nothing Then
            MaxValue_TextBox.Text = "100"
            Name_TextBox.Text = ""
            Jc_TextBox.Text = ""
            Kf_TextBox.Text = "0"
            MinValue_TextBox.Text = "0"
            Memo_TextBox.Text = ""
        Else
            With Rrow1
                MaxValue_TextBox.Text = .Item("ZkDj_MinValue") - "1"
                Name_TextBox.Text = ""
                Jc_TextBox.Text = ""
                Kf_TextBox.Text = "100" - MaxValue_TextBox.Text
                MinValue_TextBox.Text = "0"
                Memo_TextBox.Text = ""
            End With

        End If

        Call P_Show_Label()
    End Sub

    Private Sub Data_Show(ByVal tmp_Row As DataRow)
        Rinsert = False
        Rrow = tmp_Row
        With Rrow
            Code_TextBox.Text = .Item("ZkDj_Code") & ""
            Name_TextBox.Text = .Item("ZkDj_Name") & ""
            Jc_TextBox.Text = .Item("ZkDj_Jc") & ""
            MaxValue_TextBox.Text = .Item("ZkDj_MaxValue") & ""
            MinValue_TextBox.Text = .Item("ZkDj_MinValue") & ""
            Kf_TextBox.Text = .Item("ZkDj_Kf") & ""
            Memo_TextBox.Text = .Item("ZkDj_Memo") & ""

        End With
        Call P_Show_Label()
    End Sub

    Private Sub P_Show_Label()

        If Rinsert = True Then
            Move5.Enabled = False                                           '新增记录
            T_Label2.Text = "新增"
        Else
            Move5.Enabled = True                                            '新增记录
            T_Label2.Text = IIf(RZbtb.Rows.Count() = 0, "0", CStr((RZbtb.Rows.IndexOf(Rrow)) + 1))
        End If
        T_Label3.Text = "∑=" & RZbtb.Rows.Count
        Name_TextBox.Select()
    End Sub

#End Region

#Region "控件__动作"

    Private Sub Comm_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.Click, Comm2.Click
        Select Case sender.tag
            Case "保存"
                If Rinsert = True Then
                    mark = 0
                    If Name_TextBox.Text.Trim = "" Then
                        MsgBox("请输入等级名称！", MsgBoxStyle.Exclamation, "提示")
                        Name_TextBox.Select()
                        Exit Sub
                    End If

                    If MaxValue_TextBox.Text.Trim = "" Then
                        MsgBox("请输入最大值！", MsgBoxStyle.Exclamation, "提示")
                        MaxValue_TextBox.Select()
                        Exit Sub
                    End If

                    If MinValue_TextBox.Text.Trim = "" Then
                        MsgBox("请输入最小值！", MsgBoxStyle.Exclamation, "提示")
                        MinValue_TextBox.Select()
                        Exit Sub
                    End If

                    Call Data_Add()
                    'End If
                    If Name_TextBox.Text.Trim <> "" And Name_TextBox.Text.Trim <> V_Name Then

                        V_LbCount = Emr_ZkDjBll.GetRecordCount("ZkDj_Name = '" & Trim(Name_TextBox.Text) & "'")
                        If V_LbCount > 0 Then
                            MsgBox("该质控评分等级已经存在！", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示")
                            Name_TextBox.Select()
                            Exit Sub
                        End If
                    End If

                Else

                    Call Data_Edit()
                End If
            Case "取消"
                Me.Close()
        End Select
    End Sub

    Private Sub Move_Click(ByVal sender As Object, ByVal e As C1.Win.C1Command.ClickEventArgs) Handles Move1.Click, Move2.Click, Move3.Click, Move4.Click, Move5.Click
        If sender.text = "新增" Then                            '增加状态
            Call Data_Clear(Rrow)
        Else
            Rrc.GridMove(sender.text)
        End If
    End Sub

    'Private Sub Name_TextBox_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles Name_TextBox.Validated
    '    My_Cc.Get_Py(Me.Name_TextBox.Text & "")
    '    Me.Jc_TextBox.Text = My_Cc.简拚
    'End Sub

    Private Sub Name_TextBox_Validated(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Name_TextBox.Validated
        If Rinsert = True Then
            Jc_TextBox.Text = C_Py.GetPy(Name_TextBox.Text)
        Else
            If V_Name = Name_TextBox.Text Then

            Else
                Jc_TextBox.Text = C_Py.GetPy(Name_TextBox.Text)
            End If
        End If
    End Sub
#End Region

#Region "数据__编辑"

    Private Sub Data_Add()

        My_Cc.Get_MaxCode("Emr_ZkDj", "ZkDj_Code", 2, "", "")

        Dim My_NewRow As DataRow = RZbtb.NewRow

        With My_NewRow
            .Item("ZkDj_Code") = My_Cc.编码.ToString
            .Item("ZkDj_Name") = Trim(Name_TextBox.Text & "")
            .Item("ZkDj_Jc") = Jc_TextBox.Text & ""
            .Item("ZkDj_MaxValue") = Trim(MaxValue_TextBox.Text & "")

            If .Item("ZkDj_MaxValue") > "100" Then
                MsgBox("最大分数为100！", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示")
                Exit Sub
            End If

            .Item("ZkDj_MinValue") = Trim(MinValue_TextBox.Text & "")
            If .Item("ZkDj_MinValue") < "0" Then
                MsgBox("最小分数为0！", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示")
                Exit Sub
            End If

            If .Item("ZkDj_MinValue") > .Item("ZkDj_MaxValue") Then
                MsgBox("最小值大于最大值!请先修改最小值!", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示")
                Exit Sub
            End If

            .Item("ZkDj_Kf") = "100" - Trim(MaxValue_TextBox.Text & "")
            .Item("ZkDj_Memo") = Trim(Memo_TextBox.Text & "")

        End With

        '数据保存
        Try
            RZbtb.Rows.Add(My_NewRow)
            Rrc.GridMove("最后")
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            Exit Sub
        Finally
            Name_TextBox.Select()
        End Try

        '数据更新
        Try
            With Emr_ZkDjModel
                .ZkDj_Code = My_NewRow.Item("ZkDj_Code")
                .ZkDj_Name = My_NewRow.Item("ZkDj_Name")
                .ZkDj_Jc = My_NewRow.Item("ZkDj_Jc")
                .ZkDj_MaxValue = My_NewRow.Item("ZkDj_MaxValue")
                .ZkDj_MinValue = My_NewRow.Item("ZkDj_MinValue")
                .ZkDj_Kf = My_NewRow.Item("ZkDj_Kf")
                .ZkDj_Memo = My_NewRow.Item("ZkDj_Memo")
            End With
            Emr_ZkDjBll.Add(Emr_ZkDjModel)
            My_NewRow.AcceptChanges()
            RZbtb.AcceptChanges()
            MsgBox("添加成功！", MsgBoxStyle.Exclamation, "提示:")
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            Name_TextBox.Select()
            Exit Sub
        Finally
        End Try

        '数据清空
        Call Data_Clear(My_NewRow)

    End Sub



    Private Sub Data_Edit()

        Dim My_Row As DataRow = Rrow
        Dim My_Row1 As DataRow = Rrow1
        Try
            With My_Row
                .BeginEdit()
                .Item("ZkDj_Name") = Trim(Name_TextBox.Text & "")
                .Item("ZkDj_Jc") = Trim(Jc_TextBox.Text & "")
                .Item("ZkDj_MaxValue") = Trim(MaxValue_TextBox.Text & "")
                .Item("ZkDj_MinValue") = Trim(MinValue_TextBox.Text & "")
                .Item("ZkDj_Kf") = "100" - .Item("ZkDj_MaxValue")
                .Item("ZkDj_Memo") = Trim(Memo_TextBox.Text & "")


                If My_Row.Item("ZkDj_Code") <> My_Row1.Item("ZkDj_Code") Then
                    If .Item("ZkDj_MinValue") - 1 < My_Row1.Item("ZkDj_MinValue") Then
                        MsgBox("下一项最大值小于最小值,请重新输入！", MsgBoxStyle.Exclamation, "提示:")
                        'MsgBox("最大值小于下一项最小值或下一项最大值小于最小值,请重新输入！", MsgBoxStyle.Exclamation, "提示:")
                        Exit Sub
                    End If
                End If

                If .Item("ZkDj_MinValue") > .Item("ZkDj_MaxValue") Then
                    MsgBox("最小值大于最大值!请先修改最小值!", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示")
                    Exit Sub
                End If


                .EndEdit()
            End With

        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Critical, "提示:")
            My_Row.CancelEdit()
            Exit Sub
        Finally
            MinValue_TextBox.Select()
            'Name_TextBox.Select()
        End Try

        '数据更新
        Try
            With Emr_ZkDjModel
                .ZkDj_Code = Rrow.Item("ZkDj_Code")
                .ZkDj_Name = Rrow.Item("ZkDj_Name")
                .ZkDj_Jc = Rrow.Item("ZkDj_Jc")
                .ZkDj_MaxValue = Rrow.Item("ZkDj_MaxValue")
                .ZkDj_MinValue = Rrow.Item("ZkDj_MinValue")
                .ZkDj_Kf = Rrow.Item("ZkDj_Kf")
                .ZkDj_Memo = Rrow.Item("ZkDj_Memo")
            End With
            Emr_ZkDjBll.Update(Emr_ZkDjModel)
            My_Row.AcceptChanges()
            V_Name = My_Row.Item("ZkDj_Name")
            MsgBox("更新成功！", MsgBoxStyle.Exclamation, "提示:")

            If My_Row.Item("ZkDj_Code") <> My_Row1.Item("ZkDj_Code") Then
                With My_Row1
                    .BeginEdit()
                    .Item("ZkDj_Code") = .Item("ZkDj_Code") '+ "1"
                    .Item("ZkDj_Name") = .Item("ZkDj_Name")
                    .Item("ZkDj_Jc") = .Item("ZkDj_Jc")
                    .Item("ZkDj_MaxValue") = My_Row.Item("ZkDj_MinValue") - "1"
                    .Item("ZkDj_MinValue") = .Item("ZkDj_MinValue")
                    .Item("ZkDj_Kf") = "100" - .Item("ZkDj_MaxValue")
                    .Item("ZkDj_Memo") = .Item("ZkDj_Memo")
                    .EndEdit()
                End With
                With Emr_ZkDjModel
                    .ZkDj_Code = My_Row1.Item("ZkDj_Code")
                    .ZkDj_Name = My_Row1.Item("ZkDj_Name")
                    .ZkDj_Jc = My_Row1.Item("ZkDj_Jc")
                    .ZkDj_MaxValue = My_Row1.Item("ZkDj_MaxValue")
                    .ZkDj_MinValue = My_Row1.Item("ZkDj_MinValue")
                    .ZkDj_Kf = My_Row1.Item("ZkDj_Kf")
                    .ZkDj_Memo = My_Row1.Item("ZkDj_Memo")
                End With
                Emr_ZkDjBll.Update1(Emr_ZkDjModel)
                My_Row1.AcceptChanges()
                V_Name = My_Row1.Item("ZkDj_Name")
                'MsgBox("更新成功！", MsgBoxStyle.Exclamation, "提示:")
            End If



        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Information + MsgBoxStyle.OkOnly, "提示:")
        Finally
            'MinValue_TextBox.Select()
            Name_TextBox.Select()
        End Try
    End Sub

#End Region


    'Private Sub ZhongWen_TextBox_GotFocus(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Name_TextBox.GotFocus, Name_TextBox.GotFocus, Memo_TextBox.GotFocus, MaxValue_TextBox.GotFocus, Fzr_TextBox.GotFocus, Add_TextBox.GotFocus
    '    InputLanguage.CurrentInputLanguage = HisVar.HisVar.InputCode
    'End Sub
    'Private Sub YingWen_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles OrgCode_TextBox.GotFocus, LxrTell_TextBox.GotFocus, FzrTell_TextBox.GotFocus
    '    InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages(0)
    'End Sub
    Private Sub ZhongWen_TextBox_GotFocus(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Name_TextBox.GotFocus, Memo_TextBox.GotFocus
        InputLanguage.CurrentInputLanguage = HisVar.HisVar.InputCode
    End Sub

End Class