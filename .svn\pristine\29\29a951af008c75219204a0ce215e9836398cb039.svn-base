﻿Imports System
Imports System.Data
Imports System.Data.SqlClient
Imports C1.Win.C1FlexGrid
Public Class Zd_JjSave

#Region "变量初始化"
    Dim My_Dataset As New DataSet
    Dim Rrow As DataRow
    Dim Form_Lb As String
#End Region

    Public Sub New(ByVal ttrow As DataRow, ByVal formlb As String)
        ' 此调用是设计器所必需的。
        InitializeComponent()
        Rrow = ttrow
        Form_Lb = formlb
        ' 在 InitializeComponent() 调用之后添加任何初始化。
    End Sub

    Private Sub Zd_Jbjj_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        If Form_Lb = "住院票据分类" Then
            Me.Text = Rrow.Item("Lb_Name") & "明细"
            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "Select Xm_Code,Xm_Name,Xm_Jc From Zd_Ml_Xm3 where Xm_Code not in (select Mx_Code from Zd_JkFl2 where Yy_Code='" & HisVar.HisVar.WsyCode & "') and Xm_Dj<>0 Order By Xm_Jc", "左表", True)
            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "select Mx_Code as Xm_Code,Xm_Name,Xm_Jc from Zd_Ml_Xm3,Zd_JkFl2 where Zd_Ml_Xm3.Xm_Code=Zd_JkFl2.Mx_Code and Zd_JkFl2.Lb_Code='" & Rrow.Item("Lb_Code") & "'  order by Zd_JkFl2.Lb_code", "右表", True)
            Dim My_Flex1 As New BaseClass.C_Flex(C1FlexGrid1, My_Dataset.Tables("左表").DefaultView)
            With My_Flex1
                .Init_Flex()
                .Init_Column("项目编码", "Xm_Code", 85, "中", "")
                .Init_Column("项目名称", "Xm_Name", 85, "中", "")
                .Init_Column("项目简称", "Xm_Jc", 0, "中", "")
            End With

            Dim My_Flex2 As New BaseClass.C_Flex(C1FlexGrid2, My_Dataset.Tables("右表").DefaultView)
            With My_Flex2
                .Init_Flex()
                .Init_Column("项目编码", "Xm_Code", 85, "中", "")
                .Init_Column("项目名称", "Xm_Name", 85, "中", "")
                .Init_Column("项目简称", "Xm_Jc", 0, "中", "")
            End With

            '----------------------------------------
        ElseIf Form_Lb = "门诊票据分类" Then
            Me.Text = Rrow.Item("Lb_Name") & "明细"
            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "Select Xm_Code,Xm_Name,Xm_Jc From Zd_Ml_Xm3 where Xm_Code not in (select Xm_Code from Zd_MzFp2 ) and Xm_Dj<>0 Order By Xm_Jc", "左表", True)
            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "select Zd_MzFp2.Xm_Code as Xm_Code,Xm_Name,Xm_Jc from Zd_Ml_Xm3,Zd_MzFp2 where Zd_Ml_Xm3.Xm_Code=Zd_MzFp2.Xm_Code and Zd_MzFp2.Lb_Code='" & Rrow.Item("Lb_Code") & "'  order by Zd_MzFp2.Lb_code", "右表", True)
            Dim My_Flex1 As New BaseClass.C_Flex(C1FlexGrid1, My_Dataset.Tables("左表").DefaultView)
            With My_Flex1
                .Init_Flex()
                .Init_Column("项目编码", "Xm_Code", 85, "中", "")
                .Init_Column("项目名称", "Xm_Name", 85, "中", "")
                .Init_Column("项目简称", "Xm_Jc", 0, "中", "")
            End With

            Dim My_Flex2 As New BaseClass.C_Flex(C1FlexGrid2, My_Dataset.Tables("右表").DefaultView)
            With My_Flex2
                .Init_Flex()
                .Init_Column("项目编码", "Xm_Code", 85, "中", "")
                .Init_Column("项目名称", "Xm_Name", 85, "中", "")
                .Init_Column("项目简称", "Xm_Jc", 0, "中", "")
            End With
            '--------------------------------------------------
        ElseIf Form_Lb = "病区字典" Then
            Me.Text = Rrow.Item("Bq_Name") & "明细"
            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "select Bc_Code,Bc_Name,Bc_Jc from Zd_Yybc where Bc_Code not in (Select Bc_Code From Zd_Yybq2 Where Yy_Code='" & HisVar.HisVar.WsyCode & "') order by Bc_code", "左表", True)
            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "select Zd_Yybq2.Bc_Code,Bc_Name,Bc_Jc from Zd_Yybc,Zd_Yybq2 where Zd_Yybc.Bc_Code=Zd_Yybq2.Bc_Code and Zd_Yybq2.Bq_Code='" & Rrow.Item("Bq_Code") & "' and Yy_Code = '" & HisVar.HisVar.WsyCode & "' order by Zd_Yybq2.Bc_code", "右表", True)
            Dim My_Flex1 As New BaseClass.C_Flex(C1FlexGrid1, My_Dataset.Tables("左表").DefaultView)
            With My_Flex1
                .Init_Flex()
                .Init_Column("病床编码", "Bc_Code", 85, "中", "")
                .Init_Column("病床名称", "Bc_Name", 85, "中", "")
                .Init_Column("病床简称", "Bc_Jc", 0, "中", "")
            End With

            Dim My_Flex2 As New BaseClass.C_Flex(C1FlexGrid2, My_Dataset.Tables("右表").DefaultView)
            With My_Flex2
                .Init_Flex()
                .Init_Column("病床编码", "Bc_Code", 85, "中", "")
                .Init_Column("病床名称", "Bc_Name", 85, "中", "")
                .Init_Column("病床简称", "Bc_Jc", 0, "中", "")
            End With
        ElseIf Form_Lb = "科室字典" Then
            Me.Text = Rrow.Item("Ks_Name") & "明细"
            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "select Xm_Code,Xm_Name,Xm_Jc from Zd_Ml_Xm3 where Xm_Code not in (Select Xm_Code From Zd_YyKs_Xm ) and Xm_Dj <> '0' order by Xm_Code", "左表", True)
            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "select Zd_YyKs_Xm.Xm_Code,Xm_Name,Xm_Jc from Zd_Ml_Xm3,Zd_YyKs_Xm where Zd_Ml_Xm3.Xm_Code=Zd_YyKs_Xm.Xm_Code and Zd_YyKs_Xm.Ks_Code='" & Rrow.Item("Ks_Code") & "' order by Zd_YyKs_Xm.Xm_Code", "右表", True)
            Dim My_Flex1 As New BaseClass.C_Flex(C1FlexGrid1, My_Dataset.Tables("左表").DefaultView)
            With My_Flex1
                .Init_Flex()
                .Init_Column("病床编码", "Xm_Code", 85, "中", "")
                .Init_Column("病床名称", "Xm_Name", 85, "中", "")
                .Init_Column("病床简称", "Xm_Jc", 0, "中", "")
            End With

            Dim My_Flex2 As New BaseClass.C_Flex(C1FlexGrid2, My_Dataset.Tables("右表").DefaultView)
            With My_Flex2
                .Init_Flex()
                .Init_Column("病床编码", "Xm_Code", 85, "中", "")
                .Init_Column("病床名称", "Xm_Name", 85, "中", "")
                .Init_Column("病床简称", "Xm_Jc", 0, "中", "")
            End With
        End If
      

        T_Label.Text = "未归类项目数=" & (C1FlexGrid1.Rows.Count.ToString - 1)
        C1Label1.Text = "包含项目数=" & (C1FlexGrid2.Rows.Count.ToString - 1)
    End Sub

    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click

        If C1FlexGrid1.Rows.Selected.Count = 0 Then Exit Sub

        C1FlexGrid1.Redraw = False
        C1FlexGrid2.Redraw = False
        Dim R As Row
        For Each R In Me.C1FlexGrid1.Rows.Selected
            Dim My_NewRow As DataRow = My_Dataset.Tables("右表").NewRow
            If Form_Lb = "住院票据分类" Then
                HisVar.HisVar.Sqldal.ExecuteSql("Insert into Zd_JkFl2(Yy_Code,Lb_Code,Mx_Code)Values('" & HisVar.HisVar.WsyCode & "','" & Rrow.Item("Lb_Code") & "','" & R.Item("Xm_Code") & "')")
                My_NewRow.Item("Xm_Code") = R.Item("Xm_Code")
                My_NewRow.Item("Xm_Name") = R.Item("Xm_Name")
                My_NewRow.Item("Xm_Jc") = R.Item("Xm_Jc")
            ElseIf Form_Lb = "门诊票据分类" Then
                HisVar.HisVar.Sqldal.ExecuteSql("Insert into Zd_MzFp2(Yy_Code,Lb_Code,Xm_Code)Values('" & HisVar.HisVar.WsyCode & "','" & Rrow.Item("Lb_Code") & "','" & R.Item("Xm_Code") & "')")
                My_NewRow.Item("Xm_Code") = R.Item("Xm_Code")
                My_NewRow.Item("Xm_Name") = R.Item("Xm_Name")
                My_NewRow.Item("Xm_Jc") = R.Item("Xm_Jc")
            ElseIf Form_Lb = "病区字典" Then
                HisVar.HisVar.Sqldal.ExecuteSql("Insert into Zd_YyBq2(Bq_Code,Bc_Code)Values('" & Rrow.Item("Bq_Code") & "','" & R.Item("Bc_Code") & "')")
                My_NewRow.Item("Bc_Code") = R.Item("Bc_Code")
                My_NewRow.Item("Bc_Name") = R.Item("Bc_Name")
                My_NewRow.Item("Bc_Jc") = R.Item("Bc_Jc")
            ElseIf Form_Lb = "科室字典" Then
                HisVar.HisVar.Sqldal.ExecuteSql("Insert into Zd_YyKs_Xm(Ks_Code,Xm_Code)Values('" & Rrow.Item("Ks_Code") & "','" & R.Item("Xm_Code") & "')")
                My_NewRow.Item("Xm_Code") = R.Item("Xm_Code")
                My_NewRow.Item("Xm_Name") = R.Item("Xm_Name")
                My_NewRow.Item("Xm_Jc") = R.Item("Xm_Jc")
            End If

            My_Dataset.Tables("右表").Rows.Add(My_NewRow)
            My_NewRow.AcceptChanges()
            C1FlexGrid1.Rows.Remove(R.Index)
        Next
        T_Label.Text = "未归类项目数=" & (C1FlexGrid1.Rows.Count.ToString - 1)
        C1Label1.Text = "包含项目数=" & (C1FlexGrid2.Rows.Count.ToString - 1)
        C1FlexGrid1.Redraw = True
        C1FlexGrid2.Redraw = True
    End Sub


    Private Sub Button2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button2.Click

        If C1FlexGrid2.Rows.Selected.Count = 0 Then
            Exit Sub
        Else
            C1FlexGrid1.Redraw = False
            C1FlexGrid2.Redraw = False
        End If

        Dim R As Row
        For Each R In Me.C1FlexGrid2.Rows.Selected
            Dim My_NewRow As DataRow = My_Dataset.Tables("左表").NewRow
            If Form_Lb = "住院票据分类" Then
                HisVar.HisVar.Sqldal.ExecuteSql("Delete from Zd_JkFl2 Where Mx_Code = '" & R.Item("Xm_Code") & "' and Lb_Code='" & Rrow.Item("Lb_Code") & "'")
                My_NewRow.Item("Xm_Code") = R.Item("Xm_Code")
                My_NewRow.Item("Xm_Name") = R.Item("Xm_Name")
                My_NewRow.Item("Xm_Jc") = R.Item("Xm_Jc")
            ElseIf Form_Lb = "门诊票据分类" Then
                HisVar.HisVar.Sqldal.ExecuteSql("Delete from Zd_MzFp2 Where Xm_Code = '" & R.Item("Xm_Code") & "' and Lb_Code='" & Rrow.Item("Lb_Code") & "'")
                My_NewRow.Item("Xm_Code") = R.Item("Xm_Code")
                My_NewRow.Item("Xm_Name") = R.Item("Xm_Name")
                My_NewRow.Item("Xm_Jc") = R.Item("Xm_Jc")
            ElseIf Form_Lb = "病区字典" Then
                HisVar.HisVar.Sqldal.ExecuteSql("Delete from Zd_YyBq2 Where Bc_Code = '" & R.Item("Bc_Code") & "' and Bq_Code='" & Rrow.Item("Bq_Code") & "'")
                My_NewRow.Item("Bc_Code") = R.Item("Bc_Code")
                My_NewRow.Item("Bc_Name") = R.Item("Bc_Name")
                My_NewRow.Item("Bc_Jc") = R.Item("Bc_Jc")
            ElseIf Form_Lb = "科室字典" Then
                HisVar.HisVar.Sqldal.ExecuteSql("Delete from Zd_YyKs_Xm Where Xm_Code = '" & R.Item("Xm_Code") & "' and Ks_Code='" & Rrow.Item("Ks_Code") & "'")
                My_NewRow.Item("Xm_Code") = R.Item("Xm_Code")
                My_NewRow.Item("Xm_Name") = R.Item("Xm_Name")
                My_NewRow.Item("Xm_Jc") = R.Item("Xm_Jc")
            End If
            My_Dataset.Tables("左表").Rows.Add(My_NewRow)
            My_NewRow.AcceptChanges()
            C1FlexGrid2.Rows.Remove(R.Index)
        Next
        T_Label.Text = "未归类项目数=" & (C1FlexGrid1.Rows.Count.ToString - 1)
        C1Label1.Text = "包含项目数=" & (C1FlexGrid2.Rows.Count.ToString - 1)
        C1FlexGrid1.Redraw = True
        C1FlexGrid2.Redraw = True

        Me.C1FlexGrid1.Sort(SortFlags.Ascending, 0)

    End Sub
    Private Sub TextBox1_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles TextBox1.TextChanged
        If Trim(TextBox1.Text & "") = "" Then
            My_Dataset.Tables("左表").DefaultView.RowFilter = ""
        Else
            If Form_Lb = "住院票据分类" Or Form_Lb = "科室字典" Or Form_Lb = "门诊票据分类" Then
                My_Dataset.Tables("左表").DefaultView.RowFilter = "Xm_Name like '*" + TextBox1.Text + "*' or Xm_Jc like '*" + TextBox1.Text + "*'"
            ElseIf Form_Lb = "病区字典" Then
                My_Dataset.Tables("左表").DefaultView.RowFilter = "Bc_Name like '*" + TextBox1.Text + "*' or Bc_Jc like '*" + TextBox1.Text + "*'"
            End If

        End If
        T_Label.Text = "未归类项目数=" & (C1FlexGrid1.Rows.Count.ToString - 1)
    End Sub


    Private Sub TextBox2_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles TextBox2.TextChanged
        If Trim(TextBox2.Text & "") = "" Then
            My_Dataset.Tables("右表").DefaultView.RowFilter = ""
        Else
            If Form_Lb = "住院票据分类" Or Form_Lb = "科室字典" Or Form_Lb = "门诊票据分类" Then
                My_Dataset.Tables("右表").DefaultView.RowFilter = "Xm_Name like '*" + TextBox2.Text + "*' or Xm_Jc like '*" + TextBox2.Text + "*'"
            ElseIf Form_Lb = "病区字典" Then
                My_Dataset.Tables("右表").DefaultView.RowFilter = "Bc_Name like '*" + TextBox2.Text + "*' or Bc_Jc like '*" + TextBox2.Text + "*'"
            End If
        End If
        C1Label1.Text = "包含项目数=" & (C1FlexGrid2.Rows.Count.ToString - 1)
    End Sub


End Class
