﻿/**  版本信息模板在安装目录下，可自行修改。
* D_Yb_ConfigFile.cs
*
* 功 能： N/A
* 类 名： D_Yb_ConfigFile
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/11/19 9:11:03   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
namespace DAL
{
	/// <summary>
	/// 数据访问类:D_Yb_ConfigFile
	/// </summary>
	public partial class D_Yb_ConfigFile
	{
		public D_Yb_ConfigFile()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string Yy_Code)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from Yb_ConfigFile");
			strSql.Append(" where Yy_Code=@Yy_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Yy_Code", SqlDbType.Char,4)			};
			parameters[0].Value = Yy_Code;

			return HisVar.HisVar.Sqldal.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(Model.M_Yb_ConfigFile model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into Yb_ConfigFile(");
			strSql.Append("Yy_Code,ZG_Port,ZG_Baud,ZG_dkqlx,ZG_PassPort,ZG_passBaud,ZG_KWDMOD,ZG_SoftwareMode,ZG_AAB034,CZJM_Port,CZJM_Baud,CZJM_dkqlx,CZJM_XJPLX,CZJM_PassPort,CZJM_KWDMOD,CZJM_passBaud,CZJM_SoftwareMode,CZJM_AAB034,GS_Port,GS_Baud,GS_dkqlx,GS_PassPort,GS_KWDMOD,GS_SoftwareMode,GS_AAB034,ZG_MMJPLX )");
			strSql.Append(" values (");
			strSql.Append("@Yy_Code,@ZG_Port,@ZG_Baud,@ZG_dkqlx,@ZG_PassPort,@ZG_passBaud,@ZG_KWDMOD,@ZG_SoftwareMode,@ZG_AAB034,@CZJM_Port,@CZJM_Baud,@CZJM_dkqlx,@CZJM_XJPLX,@CZJM_PassPort,@CZJM_KWDMOD,@CZJM_passBaud,@CZJM_SoftwareMode,@CZJM_AAB034,@GS_Port,@GS_Baud,@GS_dkqlx,@GS_PassPort,@GS_KWDMOD,@GS_SoftwareMode,@GS_AAB034,@ZG_MMJPLX)");
			SqlParameter[] parameters = {
					new SqlParameter("@Yy_Code", SqlDbType.Char,4),
					new SqlParameter("@ZG_Port", SqlDbType.Char,1),
					new SqlParameter("@ZG_Baud", SqlDbType.VarChar,5),
					new SqlParameter("@ZG_dkqlx", SqlDbType.Char,1),
					new SqlParameter("@ZG_PassPort", SqlDbType.Char,1),
					new SqlParameter("@ZG_passBaud", SqlDbType.VarChar,5),
					new SqlParameter("@ZG_KWDMOD", SqlDbType.Char,1),
					new SqlParameter("@ZG_SoftwareMode", SqlDbType.Char,1),
					new SqlParameter("@ZG_AAB034", SqlDbType.Char,6),
					new SqlParameter("@CZJM_Port", SqlDbType.Char,1),
					new SqlParameter("@CZJM_Baud", SqlDbType.VarChar,5),
					new SqlParameter("@CZJM_dkqlx", SqlDbType.Char,1),
					new SqlParameter("@CZJM_XJPLX", SqlDbType.Char,1),
					new SqlParameter("@CZJM_PassPort", SqlDbType.Char,1),
					new SqlParameter("@CZJM_KWDMOD", SqlDbType.Char,1),
					new SqlParameter("@CZJM_passBaud", SqlDbType.VarChar,5),
					new SqlParameter("@CZJM_SoftwareMode", SqlDbType.Char,1),
					new SqlParameter("@CZJM_AAB034", SqlDbType.Char,6),
					new SqlParameter("@GS_Port", SqlDbType.Char,1),
					new SqlParameter("@GS_Baud", SqlDbType.VarChar,5),
					new SqlParameter("@GS_dkqlx", SqlDbType.Char,1),
					new SqlParameter("@GS_PassPort", SqlDbType.Char,1),
					new SqlParameter("@GS_KWDMOD", SqlDbType.Char,1),
					new SqlParameter("@GS_SoftwareMode", SqlDbType.Char,1),
					new SqlParameter("@GS_AAB034", SqlDbType.Char,6),
		      	    new SqlParameter("@ZG_MMJPLX", SqlDbType.Char,1)

            };
			parameters[0].Value = model.Yy_Code;
			parameters[1].Value = model.ZG_Port;
			parameters[2].Value = model.ZG_Baud;
			parameters[3].Value = model.ZG_dkqlx;
			parameters[4].Value = model.ZG_PassPort;
			parameters[5].Value = model.ZG_passBaud;
			parameters[6].Value = model.ZG_KWDMOD;
			parameters[7].Value = model.ZG_SoftwareMode;
			parameters[8].Value = model.ZG_AAB034;
			parameters[9].Value = model.CZJM_Port;
			parameters[10].Value = model.CZJM_Baud;
			parameters[11].Value = model.CZJM_dkqlx;
			parameters[12].Value = model.CZJM_XJPLX;
			parameters[13].Value = model.CZJM_PassPort;
			parameters[14].Value = model.CZJM_KWDMOD;
			parameters[15].Value = model.CZJM_passBaud;
			parameters[16].Value = model.CZJM_SoftwareMode;
			parameters[17].Value = model.CZJM_AAB034;
			parameters[18].Value = model.GS_Port;
			parameters[19].Value = model.GS_Baud;
			parameters[20].Value = model.GS_dkqlx;
			parameters[21].Value = model.GS_PassPort;
			parameters[22].Value = model.GS_KWDMOD;
			parameters[23].Value = model.GS_SoftwareMode;
			parameters[24].Value = model.GS_AAB034;
		    parameters[25].Value = model.ZG_MMJPLX;

            int rows =HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.M_Yb_ConfigFile model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update Yb_ConfigFile set ");
			strSql.Append("ZG_Port=@ZG_Port,");
			strSql.Append("ZG_Baud=@ZG_Baud,");
			strSql.Append("ZG_dkqlx=@ZG_dkqlx,");
			strSql.Append("ZG_PassPort=@ZG_PassPort,");
			strSql.Append("ZG_passBaud=@ZG_passBaud,");
			strSql.Append("ZG_KWDMOD=@ZG_KWDMOD,");
			strSql.Append("ZG_SoftwareMode=@ZG_SoftwareMode,");
			strSql.Append("ZG_AAB034=@ZG_AAB034,");
			strSql.Append("CZJM_Port=@CZJM_Port,");
			strSql.Append("CZJM_Baud=@CZJM_Baud,");
			strSql.Append("CZJM_dkqlx=@CZJM_dkqlx,");
			strSql.Append("CZJM_XJPLX=@CZJM_XJPLX,");
			strSql.Append("CZJM_PassPort=@CZJM_PassPort,");
			strSql.Append("CZJM_KWDMOD=@CZJM_KWDMOD,");
			strSql.Append("CZJM_passBaud=@CZJM_passBaud,");
			strSql.Append("CZJM_SoftwareMode=@CZJM_SoftwareMode,");
			strSql.Append("CZJM_AAB034=@CZJM_AAB034,");
			strSql.Append("GS_Port=@GS_Port,");
			strSql.Append("GS_Baud=@GS_Baud,");
			strSql.Append("GS_dkqlx=@GS_dkqlx,");
			strSql.Append("GS_PassPort=@GS_PassPort,");
			strSql.Append("GS_KWDMOD=@GS_KWDMOD,");
			strSql.Append("GS_SoftwareMode=@GS_SoftwareMode,");
			strSql.Append("GS_AAB034=@GS_AAB034,ZG_MMJPLX=@ZG_MMJPLX ");
			strSql.Append(" where Yy_Code=@Yy_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@ZG_Port", SqlDbType.Char,1),
					new SqlParameter("@ZG_Baud", SqlDbType.VarChar,5),
					new SqlParameter("@ZG_dkqlx", SqlDbType.Char,1),
					new SqlParameter("@ZG_PassPort", SqlDbType.Char,1),
					new SqlParameter("@ZG_passBaud", SqlDbType.VarChar,5),
					new SqlParameter("@ZG_KWDMOD", SqlDbType.Char,1),
					new SqlParameter("@ZG_SoftwareMode", SqlDbType.Char,1),
					new SqlParameter("@ZG_AAB034", SqlDbType.Char,6),
					new SqlParameter("@CZJM_Port", SqlDbType.Char,1),
					new SqlParameter("@CZJM_Baud", SqlDbType.VarChar,5),
					new SqlParameter("@CZJM_dkqlx", SqlDbType.Char,1),
					new SqlParameter("@CZJM_XJPLX", SqlDbType.Char,1),
					new SqlParameter("@CZJM_PassPort", SqlDbType.Char,1),
					new SqlParameter("@CZJM_KWDMOD", SqlDbType.Char,1),
					new SqlParameter("@CZJM_passBaud", SqlDbType.VarChar,5),
					new SqlParameter("@CZJM_SoftwareMode", SqlDbType.Char,1),
					new SqlParameter("@CZJM_AAB034", SqlDbType.Char,6),
					new SqlParameter("@GS_Port", SqlDbType.Char,1),
					new SqlParameter("@GS_Baud", SqlDbType.VarChar,5),
					new SqlParameter("@GS_dkqlx", SqlDbType.Char,1),
					new SqlParameter("@GS_PassPort", SqlDbType.Char,1),
					new SqlParameter("@GS_KWDMOD", SqlDbType.Char,1),
					new SqlParameter("@GS_SoftwareMode", SqlDbType.Char,1),
					new SqlParameter("@GS_AAB034", SqlDbType.Char,6),
					new SqlParameter("@Yy_Code", SqlDbType.Char,4),
			        new SqlParameter("@ZG_MMJPLX", SqlDbType.Char,1)
            };
			parameters[0].Value = model.ZG_Port;
			parameters[1].Value = model.ZG_Baud;
			parameters[2].Value = model.ZG_dkqlx;
			parameters[3].Value = model.ZG_PassPort;
			parameters[4].Value = model.ZG_passBaud;
			parameters[5].Value = model.ZG_KWDMOD;
			parameters[6].Value = model.ZG_SoftwareMode;
			parameters[7].Value = model.ZG_AAB034;
			parameters[8].Value = model.CZJM_Port;
			parameters[9].Value = model.CZJM_Baud;
			parameters[10].Value = model.CZJM_dkqlx;
			parameters[11].Value = model.CZJM_XJPLX;
			parameters[12].Value = model.CZJM_PassPort;
			parameters[13].Value = model.CZJM_KWDMOD;
			parameters[14].Value = model.CZJM_passBaud;
			parameters[15].Value = model.CZJM_SoftwareMode;
			parameters[16].Value = model.CZJM_AAB034;
			parameters[17].Value = model.GS_Port;
			parameters[18].Value = model.GS_Baud;
			parameters[19].Value = model.GS_dkqlx;
			parameters[20].Value = model.GS_PassPort;
			parameters[21].Value = model.GS_KWDMOD;
			parameters[22].Value = model.GS_SoftwareMode;
			parameters[23].Value = model.GS_AAB034;
			parameters[24].Value = model.Yy_Code;
		    parameters[25].Value = model.ZG_MMJPLX;

            int rows =HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string Yy_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Yb_ConfigFile ");
			strSql.Append(" where Yy_Code=@Yy_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Yy_Code", SqlDbType.Char,4)			};
			parameters[0].Value = Yy_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string Yy_Codelist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Yb_ConfigFile ");
			strSql.Append(" where Yy_Code in ("+Yy_Codelist + ")  ");
			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.M_Yb_ConfigFile GetModel(string Yy_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 Yy_Code,ZG_Port,ZG_MMJPLX,ZG_Baud,ZG_dkqlx,ZG_PassPort,ZG_passBaud,ZG_KWDMOD,ZG_SoftwareMode,ZG_AAB034,CZJM_Port,CZJM_Baud,CZJM_dkqlx,CZJM_XJPLX,CZJM_PassPort,CZJM_KWDMOD,CZJM_passBaud,CZJM_SoftwareMode,CZJM_AAB034,GS_Port,GS_Baud,GS_dkqlx,GS_PassPort,GS_KWDMOD,GS_SoftwareMode,GS_AAB034 from Yb_ConfigFile ");
			strSql.Append(" where Yy_Code=@Yy_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Yy_Code", SqlDbType.Char,4)			};
			parameters[0].Value = Yy_Code;

			Model.M_Yb_ConfigFile model=new Model.M_Yb_ConfigFile();
			DataSet ds=HisVar.HisVar.Sqldal.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.M_Yb_ConfigFile DataRowToModel(DataRow row)
		{
			Model.M_Yb_ConfigFile model=new Model.M_Yb_ConfigFile();
			if (row != null)
			{
				if(row["Yy_Code"]!=null)
				{
					model.Yy_Code=row["Yy_Code"].ToString();
				}
				if(row["ZG_Port"]!=null)
				{
					model.ZG_Port=row["ZG_Port"].ToString();
				}
				if(row["ZG_Baud"]!=null)
				{
					model.ZG_Baud=row["ZG_Baud"].ToString();
				}
				if(row["ZG_dkqlx"]!=null)
				{
					model.ZG_dkqlx=row["ZG_dkqlx"].ToString();
				}
				if(row["ZG_PassPort"]!=null)
				{
					model.ZG_PassPort=row["ZG_PassPort"].ToString();
				}
				if(row["ZG_passBaud"]!=null)
				{
					model.ZG_passBaud=row["ZG_passBaud"].ToString();
				}
				if(row["ZG_KWDMOD"]!=null)
				{
					model.ZG_KWDMOD=row["ZG_KWDMOD"].ToString();
				}
				if(row["ZG_SoftwareMode"]!=null)
				{
					model.ZG_SoftwareMode=row["ZG_SoftwareMode"].ToString();
				}
				if(row["ZG_AAB034"]!=null)
				{
					model.ZG_AAB034=row["ZG_AAB034"].ToString();
				}
				if(row["CZJM_Port"]!=null)
				{
					model.CZJM_Port=row["CZJM_Port"].ToString();
				}
				if(row["CZJM_Baud"]!=null)
				{
					model.CZJM_Baud=row["CZJM_Baud"].ToString();
				}
				if(row["CZJM_dkqlx"]!=null)
				{
					model.CZJM_dkqlx=row["CZJM_dkqlx"].ToString();
				}
				if(row["CZJM_XJPLX"]!=null)
				{
					model.CZJM_XJPLX=row["CZJM_XJPLX"].ToString();
				}
				if(row["CZJM_PassPort"]!=null)
				{
					model.CZJM_PassPort=row["CZJM_PassPort"].ToString();
				}
				if(row["CZJM_KWDMOD"]!=null)
				{
					model.CZJM_KWDMOD=row["CZJM_KWDMOD"].ToString();
				}
				if(row["CZJM_passBaud"]!=null)
				{
					model.CZJM_passBaud=row["CZJM_passBaud"].ToString();
				}
				if(row["CZJM_SoftwareMode"]!=null)
				{
					model.CZJM_SoftwareMode=row["CZJM_SoftwareMode"].ToString();
				}
				if(row["CZJM_AAB034"]!=null)
				{
					model.CZJM_AAB034=row["CZJM_AAB034"].ToString();
				}
				if(row["GS_Port"]!=null)
				{
					model.GS_Port=row["GS_Port"].ToString();
				}
				if(row["GS_Baud"]!=null)
				{
					model.GS_Baud=row["GS_Baud"].ToString();
				}
				if(row["GS_dkqlx"]!=null)
				{
					model.GS_dkqlx=row["GS_dkqlx"].ToString();
				}
				if(row["GS_PassPort"]!=null)
				{
					model.GS_PassPort=row["GS_PassPort"].ToString();
				}
				if(row["GS_KWDMOD"]!=null)
				{
					model.GS_KWDMOD=row["GS_KWDMOD"].ToString();
				}
				if(row["GS_SoftwareMode"]!=null)
				{
					model.GS_SoftwareMode=row["GS_SoftwareMode"].ToString();
				}
				if(row["GS_AAB034"]!=null)
				{
					model.GS_AAB034=row["GS_AAB034"].ToString();
				}
			    if (row["ZG_MMJPLX"] != null)
			    {
			        model.ZG_MMJPLX = row["ZG_MMJPLX"].ToString();
			    }
            }
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select Yy_Code,ZG_Port,ZG_MMJPLX,ZG_Baud,ZG_dkqlx,ZG_PassPort,ZG_passBaud,ZG_KWDMOD,ZG_SoftwareMode,ZG_AAB034,CZJM_Port,CZJM_Baud,CZJM_dkqlx,CZJM_XJPLX,CZJM_PassPort,CZJM_KWDMOD,CZJM_passBaud,CZJM_SoftwareMode,CZJM_AAB034,GS_Port,GS_Baud,GS_dkqlx,GS_PassPort,GS_KWDMOD,GS_SoftwareMode,GS_AAB034 ");
			strSql.Append(" FROM Yb_ConfigFile ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" Yy_Code,ZG_Port,ZG_MMJPLX,ZG_Baud,ZG_dkqlx,ZG_PassPort,ZG_passBaud,ZG_KWDMOD,ZG_SoftwareMode,ZG_AAB034,CZJM_Port,CZJM_Baud,CZJM_dkqlx,CZJM_XJPLX,CZJM_PassPort,CZJM_KWDMOD,CZJM_passBaud,CZJM_SoftwareMode,CZJM_AAB034,GS_Port,GS_Baud,GS_dkqlx,GS_PassPort,GS_KWDMOD,GS_SoftwareMode,GS_AAB034 ");
			strSql.Append(" FROM Yb_ConfigFile ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM Yb_ConfigFile ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.Yy_Code desc");
			}
			strSql.Append(")AS Row, T.*  from Yb_ConfigFile T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Yb_ConfigFile";
			parameters[1].Value = "Yy_Code";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return HisVar.HisVar.Sqldal.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

