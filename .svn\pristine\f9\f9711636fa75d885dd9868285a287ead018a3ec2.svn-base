﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class Cm_XmSj
    Inherits HisControl.BaseForm

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(Cm_XmSj))
        Me.C1TrueDBGrid1 = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.TreeView1 = New System.Windows.Forms.TreeView()
        Me.Image1 = New System.Windows.Forms.ImageList(Me.components)
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.Button1 = New System.Windows.Forms.Button()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.ToolBar1 = New C1.Win.C1Command.C1ToolBar()
        Me.T_Textbox = New C1.Win.C1Input.C1TextBox()
        Me.T_Label = New C1.Win.C1Input.C1Label()
        CType(Me.C1TrueDBGrid1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Panel1.SuspendLayout()
        CType(Me.T_Textbox, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T_Label, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'C1TrueDBGrid1
        '
        Me.C1TrueDBGrid1.GroupByCaption = "Drag a column header here to group by that column"
        Me.C1TrueDBGrid1.Images.Add(CType(resources.GetObject("C1TrueDBGrid1.Images"), System.Drawing.Image))
        Me.C1TrueDBGrid1.Location = New System.Drawing.Point(190, 74)
        Me.C1TrueDBGrid1.Name = "C1TrueDBGrid1"
        Me.C1TrueDBGrid1.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.C1TrueDBGrid1.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.C1TrueDBGrid1.PreviewInfo.ZoomFactor = 75.0R
        Me.C1TrueDBGrid1.PrintInfo.PageSettings = CType(resources.GetObject("C1TrueDBGrid1.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.C1TrueDBGrid1.Size = New System.Drawing.Size(412, 295)
        Me.C1TrueDBGrid1.TabIndex = 5
        Me.C1TrueDBGrid1.Text = "C1TrueDBGrid1"
        Me.C1TrueDBGrid1.PropBag = resources.GetString("C1TrueDBGrid1.PropBag")
        '
        'TreeView1
        '
        Me.TreeView1.Dock = System.Windows.Forms.DockStyle.Left
        Me.TreeView1.FullRowSelect = True
        Me.TreeView1.HotTracking = True
        Me.TreeView1.ImageIndex = 0
        Me.TreeView1.ImageList = Me.Image1
        Me.TreeView1.Location = New System.Drawing.Point(0, 32)
        Me.TreeView1.Name = "TreeView1"
        Me.TreeView1.SelectedImageIndex = 0
        Me.TreeView1.Size = New System.Drawing.Size(175, 402)
        Me.TreeView1.TabIndex = 7
        '
        'Image1
        '
        Me.Image1.ImageStream = CType(resources.GetObject("Image1.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.Image1.TransparentColor = System.Drawing.Color.White
        Me.Image1.Images.SetKeyName(0, "")
        Me.Image1.Images.SetKeyName(1, "")
        Me.Image1.Images.SetKeyName(2, "")
        '
        'Panel1
        '
        Me.Panel1.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Panel1.Controls.Add(Me.Button1)
        Me.Panel1.Controls.Add(Me.Label1)
        Me.Panel1.Controls.Add(Me.ToolBar1)
        Me.Panel1.Controls.Add(Me.T_Textbox)
        Me.Panel1.Controls.Add(Me.T_Label)
        Me.Panel1.Dock = System.Windows.Forms.DockStyle.Top
        Me.Panel1.Location = New System.Drawing.Point(0, 0)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(668, 32)
        Me.Panel1.TabIndex = 6
        '
        'Button1
        '
        Me.Button1.Location = New System.Drawing.Point(239, 5)
        Me.Button1.Name = "Button1"
        Me.Button1.Size = New System.Drawing.Size(75, 23)
        Me.Button1.TabIndex = 11
        Me.Button1.Text = "打印"
        Me.Button1.UseVisualStyleBackColor = True
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.Location = New System.Drawing.Point(11, 10)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(29, 12)
        Me.Label1.TabIndex = 9
        Me.Label1.Text = "检索"
        '
        'ToolBar1
        '
        Me.ToolBar1.AccessibleName = "Tool Bar"
        Me.ToolBar1.CommandHolder = Nothing
        Me.ToolBar1.Location = New System.Drawing.Point(1, 0)
        Me.ToolBar1.MinButtonSize = 22
        Me.ToolBar1.Movable = False
        Me.ToolBar1.Name = "ToolBar1"
        Me.ToolBar1.Size = New System.Drawing.Size(22, 22)
        Me.ToolBar1.Text = "C1ToolBar1"
        Me.ToolBar1.VisualStyle = C1.Win.C1Command.VisualStyle.Classic
        Me.ToolBar1.VisualStyleBase = C1.Win.C1Command.VisualStyle.Classic
        '
        'T_Textbox
        '
        Me.T_Textbox.Location = New System.Drawing.Point(46, 7)
        Me.T_Textbox.Name = "T_Textbox"
        Me.T_Textbox.Size = New System.Drawing.Size(125, 19)
        Me.T_Textbox.TabIndex = 3
        Me.T_Textbox.TabStop = False
        Me.T_Textbox.Tag = Nothing
        Me.T_Textbox.TextDetached = True
        Me.T_Textbox.TrimStart = True
        Me.T_Textbox.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        '
        'T_Label
        '
        Me.T_Label.AutoSize = True
        Me.T_Label.BackColor = System.Drawing.SystemColors.Control
        Me.T_Label.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.T_Label.ForeColor = System.Drawing.Color.Maroon
        Me.T_Label.Location = New System.Drawing.Point(186, 10)
        Me.T_Label.Name = "T_Label"
        Me.T_Label.Size = New System.Drawing.Size(47, 12)
        Me.T_Label.TabIndex = 2
        Me.T_Label.Tag = Nothing
        Me.T_Label.Text = "T_Label"
        Me.T_Label.TextAlign = System.Drawing.ContentAlignment.BottomLeft
        Me.T_Label.TextDetached = True
        Me.T_Label.TrimStart = True
        '
        'Cm_XmSj
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(668, 434)
        Me.Controls.Add(Me.C1TrueDBGrid1)
        Me.Controls.Add(Me.TreeView1)
        Me.Controls.Add(Me.Panel1)
        Me.Icon = CType(resources.GetObject("$this.Icon"), System.Drawing.Icon)
        Me.Name = "Cm_XmSj"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "项目售价查询"
        CType(Me.C1TrueDBGrid1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Panel1.ResumeLayout(False)
        Me.Panel1.PerformLayout()
        CType(Me.T_Textbox, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T_Label, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents C1TrueDBGrid1 As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents TreeView1 As System.Windows.Forms.TreeView
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents ToolBar1 As C1.Win.C1Command.C1ToolBar
    Friend WithEvents T_Textbox As C1.Win.C1Input.C1TextBox
    Friend WithEvents T_Label As C1.Win.C1Input.C1Label
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents Image1 As System.Windows.Forms.ImageList
    Friend WithEvents Button1 As System.Windows.Forms.Button
End Class
