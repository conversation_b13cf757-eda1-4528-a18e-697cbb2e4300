﻿Imports System.Drawing
Imports System.Windows.Forms
Imports System.Net.Sockets
Imports System.Net
Imports System.Text
Imports System.Management
Imports System.Runtime.InteropServices
Imports C1.Win.C1Command
Imports C1.C1Excel
Imports C1.Win.C1TrueDBGrid
Imports System.IO
Imports CustomControl
Imports NPOI.SS.UserModel
Imports NPOI.HSSF.UserModel

Public Class BaseFunc

    Public Shared Function CheckOwnForm(ByVal Parentform As HisControl.BaseForm, ByVal ChildForm As HisControl.BaseChild) As Boolean
        Dim V_Form As Object ' HisControl.Base
        For Each V_Form In Parentform.OwnedForms
            If V_Form.Name = ChildForm.Name Then
                V_Form.Activate()
                Return True
            End If
        Next
        Return False
    End Function

    Public Shared Function CheckForm(ByVal MyForm As Form) As Boolean
        If ErgodicModiForm(MyForm) = -1 Then
            Return False
        Else
            Return True
        End If
    End Function

    Public Shared Sub Init_Datetimepicker(ByRef V_datetime As DateTimePicker)
        V_datetime.MaxDate = "2079-06-06"
        V_datetime.MinDate = "1900-01-01"
    End Sub

    Public Shared Function Edit_Col0(ByVal My_Table As DataTable, ByVal V_Id As Integer, ByVal RowCount As Integer) As Integer '处方

        Dim My_Row As DataRow
        Dim i As Integer = 0
        For Each My_Row In My_Table.Rows
            If My_Row.RowState = DataRowState.Modified Then Continue For
            With My_Row
                .BeginEdit()
                .Item(0) = i \ RowCount + V_Id
                .EndEdit()
            End With
            i = i + 1
        Next

        Edit_Col0 = i \ RowCount + V_Id


    End Function

    Public Shared Function Look_Pwd(ByVal PassWord As String) As String
        Dim V_Str As String = PassWord
        Dim V_Pwd1 As String = ""
        Dim V_Pwd As String = ""
        If Len(PassWord) = 9 Then

            V_Pwd1 = Mid(V_Str, 1, 1) & Mid(V_Str, 8, 1) & Mid(V_Str, 6, 1) & Mid(V_Str, 3, 1) & Mid(V_Str, 9, 1) & Mid(V_Str, 5, 1) & Mid(V_Str, 2, 1) & Mid(V_Str, 7, 1) & Mid(V_Str, 4, 1)
            If Mid(V_Pwd1, 9, 1) = "A" Then
                V_Pwd = V_Pwd & Mid(V_Pwd1, 1, 8)
            ElseIf Mid(V_Pwd1, 8, 2) = "AB" Then
                V_Pwd = V_Pwd & Mid(V_Pwd1, 1, 7)
            ElseIf Mid(V_Pwd1, 7, 3) = "ABC" Then
                V_Pwd = V_Pwd & Mid(V_Pwd1, 1, 6)
            ElseIf Mid(V_Pwd1, 6, 4) = "ABCD" Then
                V_Pwd = V_Pwd & Mid(V_Pwd1, 1, 5)
            ElseIf Mid(V_Pwd1, 5, 5) = "ABCDE" Then
                V_Pwd = V_Pwd & Mid(V_Pwd1, 1, 4)
            ElseIf Mid(V_Pwd1, 4, 6) = "ABCDEF" Then
                V_Pwd = V_Pwd & Mid(V_Pwd1, 1, 3)
            ElseIf Mid(V_Pwd1, 3, 7) = "ABCDEFG" Then
                V_Pwd = V_Pwd & Mid(V_Pwd1, 1, 2)
            ElseIf Mid(V_Pwd1, 2, 8) = "ABCDEFGH" Then
                V_Pwd = V_Pwd & Mid(V_Pwd1, 1, 1)
            Else
                V_Pwd = V_Pwd & Mid(V_Pwd1, 1, 9)
            End If


        ElseIf Len(PassWord) = 18 Then
            V_Pwd1 = Mid(V_Str, 1, 1) & Mid(V_Str, 15, 1) & Mid(V_Str, 11, 1) & Mid(V_Str, 5, 1) & Mid(V_Str, 17, 1) & Mid(V_Str, 9, 1) & Mid(V_Str, 3, 1) & Mid(V_Str, 13, 1) & Mid(V_Str, 7, 1) & Mid(V_Str, 2, 1) & Mid(V_Str, 16, 1) & Mid(V_Str, 12, 1) & Mid(V_Str, 6, 1) & Mid(V_Str, 18, 1) & Mid(V_Str, 10, 1) & Mid(V_Str, 4, 1) & Mid(V_Str, 14, 1) & Mid(V_Str, 8, 1)
            If Mid(V_Pwd1, 18, 1) = "A" Then
                V_Pwd = V_Pwd & Mid(V_Pwd1, 1, 17)
            ElseIf Mid(V_Pwd1, 17, 2) = "AB" Then
                V_Pwd = V_Pwd & Mid(V_Pwd1, 1, 16)
            ElseIf Mid(V_Pwd1, 16, 3) = "ABC" Then
                V_Pwd = V_Pwd & Mid(V_Pwd1, 1, 15)
            ElseIf Mid(V_Pwd1, 15, 4) = "ABCD" Then
                V_Pwd = V_Pwd & Mid(V_Pwd1, 1, 14)
            ElseIf Mid(V_Pwd1, 14, 5) = "ABCDE" Then
                V_Pwd = V_Pwd & Mid(V_Pwd1, 1, 13)
            ElseIf Mid(V_Pwd1, 13, 6) = "ABCDEF" Then
                V_Pwd = V_Pwd & Mid(V_Pwd1, 1, 12)
            ElseIf Mid(V_Pwd1, 12, 7) = "ABCDEFG" Then
                V_Pwd = V_Pwd & Mid(V_Pwd1, 1, 11)
            ElseIf Mid(V_Pwd1, 11, 8) = "ABCDEFGH" Then
                V_Pwd = V_Pwd & Mid(V_Pwd1, 1, 10)
            Else
                V_Pwd = V_Pwd & Mid(V_Pwd1, 1, 18)
            End If




        ElseIf Len(PassWord) = 27 Then
            V_Pwd1 = Mid(V_Str, 1, 1) & Mid(V_Str, 22, 1) & Mid(V_Str, 16, 1) & Mid(V_Str, 7, 1) & Mid(V_Str, 25, 1) & Mid(V_Str, 13, 1) & Mid(V_Str, 4, 1) & Mid(V_Str, 19, 1) & Mid(V_Str, 10, 1) & Mid(V_Str, 2, 1) & Mid(V_Str, 23, 1) & Mid(V_Str, 17, 1) & Mid(V_Str, 8, 1) & Mid(V_Str, 26, 1) & Mid(V_Str, 14, 1) & Mid(V_Str, 5, 1) & Mid(V_Str, 20, 1) & Mid(V_Str, 11, 1) & Mid(V_Str, 3, 1) & Mid(V_Str, 24, 1) & Mid(V_Str, 18, 1) & Mid(V_Str, 9, 1) & Mid(V_Str, 27, 1) & Mid(V_Str, 15, 1) & Mid(V_Str, 6, 1) & Mid(V_Str, 21, 1) & Mid(V_Str, 12, 1)
            If Mid(V_Pwd1, 27, 1) = "A" Then
                V_Pwd = V_Pwd & Mid(V_Pwd1, 1, 26)
            ElseIf Mid(V_Pwd1, 26, 2) = "AB" Then
                V_Pwd = V_Pwd & Mid(V_Pwd1, 1, 25)
            ElseIf Mid(V_Pwd1, 25, 3) = "ABC" Then
                V_Pwd = V_Pwd & Mid(V_Pwd1, 1, 24)
            ElseIf Mid(V_Pwd1, 24, 4) = "ABCD" Then
                V_Pwd = V_Pwd & Mid(V_Pwd1, 1, 23)
            ElseIf Mid(V_Pwd1, 23, 5) = "ABCDE" Then
                V_Pwd = V_Pwd & Mid(V_Pwd1, 1, 22)
            ElseIf Mid(V_Pwd1, 22, 6) = "ABCDEF" Then
                V_Pwd = V_Pwd & Mid(V_Pwd1, 1, 21)
            ElseIf Mid(V_Pwd1, 21, 7) = "ABCDEFG" Then
                V_Pwd = V_Pwd & Mid(V_Pwd1, 1, 20)
            ElseIf Mid(V_Pwd1, 20, 8) = "ABCDEFGH" Then
                V_Pwd = V_Pwd & Mid(V_Pwd1, 1, 19)
            Else
                V_Pwd = V_Pwd & Mid(V_Pwd1, 1, 18)
            End If


        End If
        Return V_Pwd
    End Function

'    Shared mysocket As Socket
'    Public Shared Function check(ByVal IP As String, ByVal V_Id As String) As String
'        Try
'            mysocket = New Socket(AddressFamily.InterNetwork, SocketType.Stream, ProtocolType.Tcp)
'            mysocket.Connect(IPAddress.Parse(IP), "19999")
'            Dim lngAcceptdata As Long
'            Dim buf(65500) As Byte
'            If mysocket Is Nothing Then
'                Return 0
'            End If
'            lngAcceptdata = mysocket.Send(Encoding.Default.GetBytes(Trim(V_Id)))
'            mysocket.Receive(buf)
'            Dim str As String = Encoding.Default.GetString(buf)
'            mysocket.Close()
'            check = str.Trim
'        Catch ex As Exception
'            check = ex.ToString()
'        End Try
'
'    End Function

    Private Shared Function ErgodicModiForm(ByVal MyForm As Form) As Integer
        Return ErgodicModiForm(MyForm.Name, MyForm.Text)
    End Function

    Public Shared Function ErgodicModiForm(ByVal name As String, ByVal text As String) As Integer
        '遍历选项卡判断是否存在该子窗体
        Dim pageindex As Integer = -1
        For Each con As C1DockingTabPage In HisVar.HisVar.DockTab.TabPages
            pageindex = pageindex + 1
            Dim tab As C1DockingTabPage = DirectCast(con, C1DockingTabPage)
            For Each _ctrl In tab.Controls
                If DirectCast(_ctrl, Form).Name = name And DirectCast(_ctrl, Form).Text = text Then
                    '存在
                    Return pageindex
                End If
            Next
        Next
        Return -1
        '不存在
    End Function

    Public Shared Function CheckFormExist(ByVal name As String, ByVal text As String) As Boolean
        '遍历选项卡判断是否存在该子窗体
        Dim pageindex As Integer = -1
        For Each con As C1DockingTabPage In HisVar.HisVar.DockTab.TabPages
            pageindex = pageindex + 1
            Dim tab As C1DockingTabPage = DirectCast(con, C1DockingTabPage)
            For Each _ctrl In tab.Controls
                If DirectCast(_ctrl, Form).Name.Contains(name) And DirectCast(_ctrl, Form).Text.Contains(text) Then
                    '存在
                    Return True
                End If
            Next
        Next
        Return False
        '不存在
    End Function

    Public Shared Sub addTabControl(ByVal objfrm As Form, ByVal Captain As String, Optional MenuCode As String = "0")
        HisVar.HisVar.FMain.Cursor = Cursors.WaitCursor
        If HisVar.HisVar.DockTab.Visible = False Then
            HisVar.HisVar.DockTab.Visible = True
        End If
        objfrm.Text = Captain
        objfrm.Tag = MenuCode
        Dim pageindex As Integer
        pageindex = ErgodicModiForm(objfrm)
        If pageindex = -1 Then
            objfrm.MdiParent = HisVar.HisVar.FMain
            '声明一个选项卡对象
            Dim tabPage As New C1DockingTabPage
            tabPage.Dock = DockStyle.Fill

            '选项卡的名称
            tabPage.Name = objfrm.Name
            '选项卡的文本
            tabPage.Text = Captain ' objfrm.Text
            tabPage.Parent = HisVar.HisVar.DockTab

            '子窗体显示
            objfrm.TopLevel = False

            objfrm.FormBorderStyle = FormBorderStyle.None
            If objfrm.GetType.BaseType.FullName = "HisControl.BaseForm" Or
               objfrm.GetType.BaseType.FullName = "Common.BaseForm.BaseFather" Or
               objfrm.GetType.BaseType.FullName = "Common.BaseForm.OneFormRk1" Or
               objfrm.GetType.BaseType.FullName = "Common.BaseForm.BaseDict1" Or
               objfrm.GetType.BaseType.FullName = "Common.BaseForm.DoubleFormRK1" Or
               objfrm.GetType.BaseType.FullName = "Common.BaseForm.RecursionBaseDict21" Or
               objfrm.GetType.BaseType.FullName = "Common.BaseForm.BaseDict21" Then
                objfrm.Dock = DockStyle.Fill
            Else
                objfrm.Location = New Point((tabPage.Width - objfrm.Width) / 2, (tabPage.Height - objfrm.Height) / 2 - 20)
            End If

            '将子窗体添加到选项卡中
            tabPage.Controls.Add(objfrm)
            tabPage.ImageIndex = 0

            objfrm.Show()
            '设置当前选项卡为新增选项卡
            HisVar.HisVar.DockTab.TabPages.Add(tabPage)
            '选中当前窗体选项页
            HisVar.HisVar.DockTab.SelectedTab = tabPage
        Else
            HisVar.HisVar.DockTab.SelectedIndex = pageindex
            HisVar.HisVar.DockTab.SelectedTab.Name = objfrm.Name
            HisVar.HisVar.DockTab.SelectedTab.Text = objfrm.Text
        End If
        Dim moudlebll As New BLL.BllZd_QxModule
        If moudlebll.ExistsByName(Captain) = True And MenuCode <> "0" Then
            If HisVar.HisVar.Sqllite.GetSingle("Select Count(MoudleName) from OftenMoudle where MenuCode='" & MenuCode & "' And MoudleName='" & Captain & "'") > 0 Then
                HisVar.HisVar.Sqllite.ExecuteSql("Update OftenMoudle set MoudleTimes=MoudleTimes+1 where MenuCode='" & MenuCode & "' And MoudleName='" & Captain & "'")
            Else
                HisVar.HisVar.Sqllite.ExecuteSql("insert into OftenMoudle values('" & MenuCode & "','" & Captain & "',1)")
            End If
        End If

        HisVar.HisVar.FMain.Cursor = Cursors.Default
    End Sub

    Public Shared Sub ExportExcel(ByVal name As String, ByVal Tdbgrid As CustomControl.MyGrid, ByRef filepath As String,
                              ByVal isOpen As Boolean)
        Dim save As New SaveFileDialog
        Dim filename As String
        With save
            .Filter = "Excel文件(.Xls)|*.Xls"
            If name.ToString.Trim = "" Then
                .FileName = name & Format(Now, "yyMMdd") & ".Xls"
            Else
                .FileName = name & ".Xls"
                If filepath = "" Then
                    filename = .FileName
                End If
            End If
            If filepath = "" Then
                If .ShowDialog = Windows.Forms.DialogResult.Cancel Then
                    Exit Sub
                End If
                filepath = .FileName.Replace(filename, "")
            Else
                .FileName = filepath + .FileName
            End If

            .AddExtension = True
        End With
        Dim i As Integer
        ' step 1: create a new workbook
        Dim book As New C1XLBook()
        ' step 2: create styles for odd and even values
        Dim styleMain As New XLStyle(book)
        Dim styleHeader As New XLStyle(book)
        Dim styleGroupHeader As New XLStyle(book)

        ' step 3: write content and styles into some cells
        Dim sheet As XLSheet = book.Sheets(0)

        Dim row As Integer = 0
        ' Pointer for looping through the rows
        Dim col As Integer = 0
        Dim xcol As Integer = 0
        'C1.Win.C1TrueDBGrid.C1DisplayColumn CurrentDColumn = default(C1.Win.C1TrueDBGrid.C1DisplayColumn);
        ' Pointer for looping through the columns
        Dim blnContinue As Boolean = True
        Dim arrayGroupedColumnHeaders As New ArrayList()

        Dim cell As XLCell = Nothing
        Dim gr As C1.Win.C1TrueDBGrid.GroupRow = Nothing
        Dim Sum As Decimal = Nothing
        Dim MinMax As Decimal = Nothing
        Dim Denominator As Decimal = Nothing

        i = 0
        While i <= Tdbgrid.GroupedColumns.Count - 1
            arrayGroupedColumnHeaders.Add(String.Empty)
            System.Math.Max(System.Threading.Interlocked.Increment(i), i - 1)
        End While



        For Each CurrentDColumn As C1DisplayColumn In Tdbgrid.Splits(0).DisplayColumns
            If (Tdbgrid.Splits(0).DisplayColumns(col).Visible = True) AndAlso (Tdbgrid.Splits(0).DisplayColumns(col).Width <> 0) Then
                cell = sheet(0, xcol)
                sheet.Rows(0).Height = 500
                cell.Value = CurrentDColumn.DataColumn.Caption

                cell.Style = New C1.C1Excel.XLStyle(book)
                'cell.Style.BackColor = Color.FromArgb(148, 221, 239)
                cell.Style.ForeColor = Color.Black
                cell.Style.Font = New Font("宋体", 10, FontStyle.Bold)

                cell.Style.BorderBottom = XLLineStyleEnum.Thin
                cell.Style.BorderTop = XLLineStyleEnum.Thin
                cell.Style.BorderLeft = XLLineStyleEnum.Thin
                cell.Style.BorderRight = XLLineStyleEnum.Thin

                'cell.Style = styleHeader
                cell.Style.AlignHorz = CType(CurrentDColumn.HeadingStyle.HorizontalAlignment, XLAlignHorzEnum)
                cell.Style.AlignVert = CType(CurrentDColumn.HeadingStyle.VerticalAlignment, XLAlignVertEnum)
                xcol = xcol + 1
            End If
            col = col + 1
        Next
        Dim SelectedRowsOnly As Integer = Tdbgrid.SelectedRows.Count
        If (SelectedRowsOnly = 0) Or (Tdbgrid.GroupedColumns.Count > 0) Then
            'Rows
            row = 0
            While row <= (Tdbgrid.Splits(0).Rows.Count - 1)
                col = 0
                xcol = 0

                For Each CurrentDColumn As C1DisplayColumn In Tdbgrid.Splits(0).DisplayColumns
                    If (Tdbgrid.Splits(0).DisplayColumns(col).Visible = True) AndAlso (Tdbgrid.Splits(0).DisplayColumns(col).Width <> 0) Then
                        cell = sheet(row + 1, xcol)
                        sheet.Rows(row + 1).Height = 400
                        cell.Style = New C1.C1Excel.XLStyle(book)
                        cell.Style.Font = New Font("宋体", 9, FontStyle.Regular)
                        cell.Style.ForeColor = Color.Black
                        cell.Style.BorderBottom = XLLineStyleEnum.Thin
                        cell.Style.BorderTop = XLLineStyleEnum.Thin
                        cell.Style.BorderLeft = XLLineStyleEnum.Thin
                        cell.Style.BorderRight = XLLineStyleEnum.Thin
                        If Tdbgrid.Splits(0).Rows(row).RowType = C1.Win.C1TrueDBGrid.RowTypeEnum.DataRow Then
                            'Regular Data row
                            'cell.Style = styleMain

                            If TypeOf CurrentDColumn.DataColumn.CellValue(Tdbgrid.RowBookmark(row)) Is DateTime Then
                                cell.Value = CType(CurrentDColumn.DataColumn.CellText(Tdbgrid.RowBookmark(row)), String)
                            Else
                                If CurrentDColumn.DataColumn.NumberFormat <> String.Empty And CurrentDColumn.DataColumn.NumberFormat <> "Percent" Then
                                    If CurrentDColumn.DataColumn.CellText(Tdbgrid.RowBookmark(row)) <> String.Empty Then
                                        If TypeOf CurrentDColumn.DataColumn.CellValue(Tdbgrid.RowBookmark(row)) Is Decimal Then
                                            cell.Style.Format = CurrentDColumn.DataColumn.NumberFormat
                                            cell.Value = CType(CurrentDColumn.DataColumn.CellText(Tdbgrid.RowBookmark(row)), Decimal)
                                        Else
                                            cell.Value = CurrentDColumn.DataColumn.CellText(Tdbgrid.RowBookmark(row))
                                        End If


                                    End If
                                Else
                                    cell.Value = CurrentDColumn.DataColumn.CellValue(Tdbgrid.RowBookmark(row))
                                End If
                            End If
                            sheet.Columns(xcol).Width = Tdbgrid.Splits(0).DisplayColumns(col).Width * 18
                            cell.Style.AlignHorz = CType(CurrentDColumn.Style.HorizontalAlignment, XLAlignHorzEnum)


                            If (gr IsNot Nothing) And xcol = 0 Then
                                i = 0
                                While i <= gr.Level
                                    cell.Value = " " + cell.Value
                                    cell.Style.AlignHorz = XLAlignHorzEnum.Left
                                    System.Math.Max(System.Threading.Interlocked.Increment(i), i - 1)
                                End While
                            End If
                        Else
                            cell.Style = New C1.C1Excel.XLStyle(book)
                            cell.Style.BackColor = Color.FromArgb(218, 238, 243)
                            cell.Style.ForeColor = Color.Black
                            cell.Style.Font = New Font("宋体", 9, FontStyle.Bold)

                            'Group Header row
                            If xcol = 0 Then
                                gr = CType(Tdbgrid.Splits(0).Rows(row), GroupRow)

                                cell.Value = (If(gr.GroupedText = String.Empty, "No " & Tdbgrid.GroupedColumns(gr.Level).Caption, gr.GroupedText)) & " (" & gr.Count & ")"

                                i = 0
                                While i <= gr.Level - 1
                                    cell.Value = "       " + cell.Value
                                    System.Math.Max(System.Threading.Interlocked.Increment(i), i - 1)
                                End While

                                cell.Style.AlignHorz = XLAlignHorzEnum.Left
                            Else
                                Select Case Tdbgrid.Columns(CurrentDColumn.DataColumn.DataField).Aggregate
                                    Case C1.Win.C1TrueDBGrid.AggregateEnum.Sum
                                        Sum = 0
                                        i = gr.StartIndex
                                        While i <= gr.EndIndex
                                            If Not Convert.IsDBNull(Tdbgrid.Columns(CurrentDColumn.DataColumn.DataField).CellValue(i)) Then
                                                Sum += Convert.ToDecimal(Tdbgrid.Columns(CurrentDColumn.DataColumn.DataField).CellValue(i))
                                            End If
                                            System.Math.Max(System.Threading.Interlocked.Increment(i), i - 1)
                                        End While

                                        cell.Value = Sum
                                        Exit Select
                                    Case C1.Win.C1TrueDBGrid.AggregateEnum.Average
                                        Sum = 0
                                        Denominator = 0
                                        i = gr.StartIndex
                                        While i <= gr.EndIndex
                                            If Not Convert.IsDBNull(Tdbgrid.Columns(CurrentDColumn.DataColumn.DataField).CellValue(i)) Then
                                                Sum += Convert.ToDecimal(Tdbgrid.Columns(CurrentDColumn.DataColumn.DataField).CellValue(i))
                                                Denominator += 1
                                            End If
                                            System.Math.Max(System.Threading.Interlocked.Increment(i), i - 1)
                                        End While

                                        If Denominator > 0 Then
                                            If Sum / Denominator = Math.Round(Sum / Denominator, 0) Then
                                                cell.Value = Sum / Denominator
                                            ElseIf Sum / Denominator = Math.Round(Sum / Denominator, 1) Then
                                                cell.Value = [String].Format("##,###,##0.0", Sum / Denominator)
                                            ElseIf Sum / Denominator = Math.Round(Sum / Denominator, 1) Then
                                                cell.Value = [String].Format("##,###,##0.00", Sum / Denominator)
                                            Else
                                                cell.Value = [String].Format("##,###,##0.000", Sum / Denominator)
                                            End If
                                        Else
                                            cell.Value = 0
                                        End If

                                        Exit Select
                                    Case C1.Win.C1TrueDBGrid.AggregateEnum.Min
                                        MinMax = Convert.ToDecimal(Tdbgrid.Columns(CurrentDColumn.DataColumn.DataField).CellValue(gr.StartIndex))
                                        i = gr.StartIndex
                                        While i <= gr.EndIndex
                                            If Not Convert.IsDBNull(Tdbgrid.Columns(CurrentDColumn.DataColumn.DataField).CellValue(i)) Then
                                                If CType(Tdbgrid.Columns(CurrentDColumn.DataColumn.DataField).CellValue(i), [Decimal]) < MinMax Then
                                                    MinMax = Convert.ToDecimal(Tdbgrid.Columns(CurrentDColumn.DataColumn.DataField).CellValue(i))
                                                End If
                                            End If
                                            System.Math.Max(System.Threading.Interlocked.Increment(i), i - 1)
                                        End While

                                        cell.Value = MinMax
                                        Exit Select
                                    Case C1.Win.C1TrueDBGrid.AggregateEnum.Max
                                        MinMax = Convert.ToDecimal(Tdbgrid.Columns(CurrentDColumn.DataColumn.DataField).CellValue(gr.StartIndex))
                                        i = gr.StartIndex
                                        While i <= gr.EndIndex
                                            If Not Convert.IsDBNull(Tdbgrid.Columns(CurrentDColumn.DataColumn.DataField).CellValue(i)) Then
                                                If CType(Tdbgrid.Columns(CurrentDColumn.DataColumn.DataField).CellValue(i), Integer) > MinMax Then
                                                    MinMax = Convert.ToDecimal(Tdbgrid.Columns(CurrentDColumn.DataColumn.DataField).CellValue(i))
                                                End If
                                            End If
                                            System.Math.Max(System.Threading.Interlocked.Increment(i), i - 1)
                                        End While

                                        cell.Value = MinMax
                                        Exit Select
                                    Case C1.Win.C1TrueDBGrid.AggregateEnum.[Custom]
                                        Dim strGridName As String = Tdbgrid.Name
                                        Select Case strGridName
                                            Case "Grid_Cl_Xs_Lr"
                                                Dim sumlr As Decimal = 0
                                                Dim sumXs As Decimal = 0
                                                Select Case CurrentDColumn.DataColumn.DataField
                                                    Case "Lr_Percent"
                                                        i = gr.StartIndex
                                                        While i <= gr.EndIndex
                                                            sumlr = sumlr + Convert.ToDecimal(Tdbgrid.Columns("Lr").CellValue(i))
                                                            sumXs = sumXs + Convert.ToDecimal(Tdbgrid.Columns("XsMx_Money").CellValue(i))
                                                            System.Math.Max(System.Threading.Interlocked.Increment(i), i - 1)
                                                        End While
                                                        cell.Value = Format(sumlr / sumXs * 100, "##,###,##0.00")
                                                        Exit Select
                                                End Select
                                                Exit Select
                                        End Select
                                        Exit Select
                                    Case C1.Win.C1TrueDBGrid.AggregateEnum.None

                                        Exit Select
                                    Case Else
                                        cell.Value = Tdbgrid.Columns(CurrentDColumn.DataColumn.DataField).Aggregate.ToString()
                                        Exit Select
                                End Select
                                cell.Style.AlignHorz = CType(CurrentDColumn.Style.HorizontalAlignment, XLAlignHorzEnum)
                            End If
                        End If
                        xcol = xcol + 1
                    End If
                    col = col + 1
                Next
                System.Math.Max(System.Threading.Interlocked.Increment(row), row - 1)
            End While
        Else
            'Rows
            row = 0
            While row <= (Tdbgrid.SelectedRows.Count - 1)
                col = 0
                xcol = 0

                For Each CurrentDColumn As C1DisplayColumn In Tdbgrid.Splits(0).DisplayColumns
                    If (Tdbgrid.Splits(0).DisplayColumns(col).Visible = True) AndAlso (Tdbgrid.Splits(0).DisplayColumns(col).Width <> 0) Then
                        cell = sheet(row + 1, xcol)
                        If TypeOf CurrentDColumn.DataColumn.CellValue(Tdbgrid.SelectedRows(row)) Is DateTime Then
                            cell.Value = CType(CurrentDColumn.DataColumn.CellText(Tdbgrid.SelectedRows(row)), String)
                        Else
                            If CurrentDColumn.DataColumn.NumberFormat <> String.Empty And CurrentDColumn.DataColumn.NumberFormat <> "Percent" Then
                                If CurrentDColumn.DataColumn.CellText(Tdbgrid.SelectedRows(row)) <> String.Empty Then
                                    cell.Value = Convert.ToDecimal(CurrentDColumn.DataColumn.CellText(Tdbgrid.SelectedRows(row)))
                                End If
                            Else
                                cell.Value = CurrentDColumn.DataColumn.CellValue(Tdbgrid.SelectedRows(row))
                            End If
                        End If
                        'cell.Style = styleMain
                        cell.Style = New C1.C1Excel.XLStyle(book)
                        cell.Style.Font = New Font("宋体", 9, FontStyle.Regular)
                        cell.Style.ForeColor = Color.Black

                        sheet.Columns(xcol).Width = Tdbgrid.Splits(0).DisplayColumns(col).Width * 18
                        cell.Style.AlignHorz = CType(CurrentDColumn.Style.HorizontalAlignment, XLAlignHorzEnum)
                        xcol = xcol + 1
                    End If
                    col = col + 1
                Next
                System.Math.Max(System.Threading.Interlocked.Increment(row), row - 1)
            End While
        End If

        'Footers
        col = 0
        xcol = 0
        'If SelectedRowsOnly = 0 And Tdbgrid.GroupedColumns.Count = 0 Then

        If SelectedRowsOnly = 0 Then
            For Each CurrentDColumn As C1DisplayColumn In Tdbgrid.Splits(0).DisplayColumns
                If (Tdbgrid.Splits(0).DisplayColumns(col).Visible = True) AndAlso (Tdbgrid.Splits(0).DisplayColumns(col).Width <> 0) Then
                    Debug.Write(CurrentDColumn.DataColumn.Caption & " - " & Tdbgrid.Splits(0).DisplayColumns(col).Width & vbLf)
                    cell = sheet(row + 1, xcol)
                    cell.Value = CurrentDColumn.DataColumn.FooterText
                    cell.Style = New C1.C1Excel.XLStyle(book)
                    cell.Style.AlignHorz = CType(CurrentDColumn.Style.HorizontalAlignment, XLAlignHorzEnum)
                    xcol = xcol + 1
                End If
                col = col + 1
            Next
        End If

        If blnContinue Then
            book.Save(save.FileName)
        End If
        If isOpen = True Then
            System.Diagnostics.Process.Start(save.FileName)
        End If

    End Sub

    Public Shared Sub setConfig(ByVal cont As Object, ByVal name As String, ByVal modulename As String)
        If HisVar.HisVar.Sqllite.GetSingle("Select Count(ConfName) from Config where ConfName='" & name & "' and ModuleName='" & modulename & "'") > 0 Then
            HisVar.HisVar.Sqllite.ExecuteSql("Update Config set ConfCont='" & cont & "' where ConfName='" & name & "' and ModuleName='" & modulename & "'")
        Else
            HisVar.HisVar.Sqllite.ExecuteSql("insert into Config values('" & modulename & "','" & name & "','" & cont & "')")
        End If
    End Sub

    Public Shared Function getConfig(ByVal name As String, ByVal modulename As String) As Object
        getConfig = HisVar.HisVar.Sqllite.GetSingle("Select ConfCont from Config where ConfName='" & name & "' and ModuleName='" & modulename & "'") & ""
    End Function
    ''' <summary>   
    ''' 从Excel中获取数据到DataTable   
    ''' </summary>   
    ''' <param name="strFileName">Excel文件全路径(服务器路径)</param>   
    ''' <param name="SheetIndex">要获取数据的工作表序号(从0开始)</param>   
    ''' <param name="HeaderRowIndex">工作表标题行所在行号(从0开始)</param>   
    ''' <param name="table">需要插入的表名</param>  
    ''' <param name="ColCount">需要导入的列数</param>  
    ''' <param name="NotNullCol">必填列</param>  
    ''' <returns></returns>   
    Public Shared Function RenderDataTableFromExcel(ByVal strFileName As String, ByVal SheetIndex As Integer, ByVal HeaderRowIndex As Integer, ByVal table As DataTable, ByVal ColCount As Integer, ByVal NotNullCol As Integer) As String
        Using file As New FileStream(strFileName, FileMode.Open, FileAccess.Read)
            Dim workbook As IWorkbook = New HSSFWorkbook(file)
            Dim SheetName As String = workbook.GetSheetName(SheetIndex)
            Return RenderDataTableFromExcel(workbook, SheetName, HeaderRowIndex, table, ColCount, NotNullCol)
        End Using
    End Function
    ''' <summary>   
    ''' 从Excel中获取数据到DataTable   
    ''' </summary>   
    ''' <param name="workbook">要处理的工作薄</param>   
    ''' <param name="SheetName">要获取数据的工作表名称</param>   
    ''' <param name="HeaderRowIndex">工作表标题行所在行号(从0开始)</param>   
    ''' <param name="table">需要插入的表名</param>  
    ''' <param name="ColCount">需要导入的列数</param>  
    ''' <param name="NotNullCol">必填列</param>  
    ''' <returns></returns>   
    Public Shared Function RenderDataTableFromExcel(ByVal workbook As IWorkbook, ByVal SheetName As String, ByVal HeaderRowIndex As Integer, ByVal table As DataTable, ByVal ColCount As Integer, ByVal NotNullCol As Integer) As String
        Dim sheet As ISheet = workbook.GetSheet(SheetName)
        Dim i As Integer
        Try
            If table IsNot Nothing Then table.Clear()
            Dim rowCount As Integer = sheet.LastRowNum

            '#region 循环各行各列,写入数据到DataTable
            i = (HeaderRowIndex + 1)
            While i < sheet.LastRowNum + 1
                Dim row As IRow = sheet.GetRow(i)

                Try
                    If row.GetCell(NotNullCol) Is Nothing Then
                        Exit While
                    End If
                    If row.GetCell(NotNullCol).ToString = "" Then
                        Exit While
                    End If
                Catch ex As Exception
                    Exit While
                End Try

                Dim dataRow As DataRow = table.NewRow()
                Dim j As Integer = row.FirstCellNum
                While j < ColCount
                    Dim cell As ICell = row.GetCell(j)
                    If cell Is Nothing Then
                        dataRow(j) = Nothing
                    Else
                        'dataRow[j] = cell.ToString();   
                        Select Case cell.CellType
                            Case CellType.Blank
                                dataRow(j) = DBNull.Value
                                Exit Select
                            Case CellType.[Boolean]
                                dataRow(j) = cell.BooleanCellValue
                                Exit Select
                            Case CellType.Numeric
                                dataRow(j) = cell.NumericCellValue
                                Exit Select
                            Case CellType.[String]
                                dataRow(j) = cell.StringCellValue
                                Exit Select
                            Case CellType.[Error]
                                dataRow(j) = cell.ErrorCellValue
                                Exit Select
                            Case CellType.Formula
                                dataRow(j) = cell.NumericCellValue '.CellFormula
                                Exit Select
                        End Select
                    End If
                    System.Math.Max(System.Threading.Interlocked.Increment(j), j - 1)
                End While
                'dataRow[j] = row.GetCell(j).ToString();   
                table.Rows.Add(dataRow)
                System.Math.Max(System.Threading.Interlocked.Increment(i), i - 1)
            End While
        Catch ex As System.Exception
            table.Clear()
            'table.Columns.Clear()

            'table.Columns.Add("出错了")
            'Dim dr As DataRow = table.NewRow()
            'dr(0) = ex.Message
            'table.Rows.Add(dr)
            Return "失败"
        Finally
            'sheet.Dispose();   
            workbook = Nothing
            sheet = Nothing
        End Try
        '#region 清除最后的空行
        i = table.Rows.Count - 1
        While i > 0
            Dim isnull As Boolean = True
            Dim j As Integer = 0
            While j < table.Columns.Count
                If table.Rows(i)(j) IsNot Nothing Then
                    If table.Rows(i)(j).ToString() <> "" Then
                        isnull = False
                        Exit While
                    End If
                End If
                System.Math.Max(System.Threading.Interlocked.Increment(j), j - 1)
            End While
            If isnull Then
                table.Rows(i).Delete()
            End If
            System.Math.Max(System.Threading.Interlocked.Decrement(i), i + 1)
        End While
        Return "成功"
    End Function

End Class
