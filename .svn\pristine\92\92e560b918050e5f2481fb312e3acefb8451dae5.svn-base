﻿/**  版本信息模板在安装目录下，可自行修改。
* M_Materials_Check1.cs
*
* 功 能： N/A
* 类 名： M_Materials_Check1
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016-11-26 11:27:50   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace ModelOld
{
	/// <summary>
	/// 物资盘点主表
	/// </summary>
	[Serializable]
	public partial class M_Materials_Check1
	{
		public M_Materials_Check1()
		{}
		#region Model
		private string _m_check_code;
		private string _materialswh_code;
        private DateTime? _Check_Date;
		private DateTime? _input_date;
		private DateTime? _finish_date;
		private string _Jsr_Code;
        private string _jsr_name;
		private string _M_Check_Memo;
		private string _ordersstatus;
        private decimal? _totalmoney;
		/// <summary>
		/// yyMMdd+5位流水号
		/// </summary>
		public string M_Check_Code
		{
			set{ _m_check_code=value;}
			get{return _m_check_code;}
		}
		/// <summary>
		/// 库房编码
		/// </summary>
		public string MaterialsWh_Code
		{
			set{ _materialswh_code=value;}
			get{return _materialswh_code;}
		}
        public DateTime? Check_Date
        {
            get { return _Check_Date; }
            set { _Check_Date = value; }
        }
		/// <summary>
		/// 录入时间
		/// </summary>
		public DateTime? Input_Date
		{
			set{ _input_date=value;}
			get{return _input_date;}
		}
		/// <summary>
		/// 完成时间
		/// </summary>
		public DateTime? Finish_Date
		{
			set{ _finish_date=value;}
			get{return _finish_date;}
		}
		/// <summary>
		/// 工作人员编码
		/// </summary>
		public string Jsr_Code
		{
			set{ _Jsr_Code=value;}
			get{return _Jsr_Code;}
		}
        /// <summary>
        /// 
        /// </summary>
        public string Jsr_Name
        {
            set { _jsr_name = value; }
            get { return _jsr_name; }
        }
        /// <summary>
        /// 总金额
        /// </summary>
        public decimal? TotalMoney
        {
            set { _totalmoney = value; }
            get { return _totalmoney; }
        }
		/// <summary>
		/// 备注
		/// </summary>
		public string M_Check_Memo
		{
			set{ _M_Check_Memo=value;}
			get{return _M_Check_Memo;}
		}
		/// <summary>
		/// 单据状态
		/// </summary>
		public string OrdersStatus
		{
			set{ _ordersstatus=value;}
			get{return _ordersstatus;}
		}
		#endregion Model

	}
}

