﻿Imports BaseClass
Imports System.Windows.Forms

Public Class MaterialsOtherInQuery

    Dim m_Rc As C_RowChange

    Dim Zb_Table As DataTable                       '从表
    Dim Zb_Cm As CurrencyManager                     '同步指针
    Dim Zb_Row As DataRow                            '当前选择行

    Dim B_Materials_Other_In1 As New BLLOld.B_Materials_Other_In1

    Dim B_Materials_Warehouse_Dict As New BLLOld.B_Materials_Warehouse_Dict
    Dim B_Materials_InOut_Class_Dict As New BLLOld.B_Materials_InOut_Class_Dict
    Dim B_Zd_YyJsr As New BLLOld.B_Zd_YyJsr

    Dim IsLoadOk As Boolean
    Dim IsSearchOk As Boolean

    Public Sub New(ByRef rc As C_RowChange)

        ' 此调用是设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        m_Rc = rc
    End Sub

    Private Sub MaterialsOtherInQuery_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        IsLoadOk = False
        Call Form_Init()
        Call Data_Init()
        IsLoadOk = True
    End Sub

    Private Sub Form_Init()

        '库房
        With Kf_DtCom
            .DataView = B_Materials_Warehouse_Dict.GetAllList.Tables(0).DefaultView
            .Init_Colum("MaterialsWh_Code", "库房编码", 0, "左")
            .Init_Colum("MaterialsWh_Name", "库房名称", 120, "左")
            .Init_Colum("MaterialsWh_Py", "库房简称", 60, "左")
            .DisplayMember = "MaterialsWh_Name"
            .ValueMember = "MaterialsWh_Code"
            .DroupDownWidth = .Width
            .MaxDropDownItems = 15
            .SelectedIndex = -1
            .RowFilterTextNull = ""
            .RowFilterNotTextNull = "MaterialsWh_Py"
        End With

        '出入库类别
        With InOutLb_DtCom
            .DataView = B_Materials_InOut_Class_Dict.GetList("InOutType = '入库'").Tables(0).DefaultView
            .Init_Colum("MaterialsInOut_Code", "类别编码", 0, "左")
            .Init_Colum("MaterialsInOut_Name", "类别名称", 120, "左")
            .Init_Colum("MaterialsInOut_Py", "类别简称", 60, "左")
            .DisplayMember = "MaterialsInOut_Name"
            .ValueMember = "MaterialsInOut_Code"
            .DroupDownWidth = .Width
            .MaxDropDownItems = 15
            .SelectedIndex = -1
            .RowFilterTextNull = ""
            .RowFilterNotTextNull = "MaterialsInOut_Py"
        End With

        Rk_Date1.Value = Format(Now, "yyyy-MM-dd")
        Rk_Date2.Value = Format(Now, "yyyy-MM-dd")

        With Dj_State_SingleCom
            .Additem = "全部"
            .Additem = "录入"
            .Additem = "完成"
            .DroupDownWidth = .Width - .CaptainWidth
            .DisplayColumns(1).Visible = False
            .SelectedIndex = 0
        End With

        With Cx_State_SingleCom
            .Additem = "全部"
            .Additem = "未冲销"
            .Additem = "冲销"
            .Additem = "被冲销"
            .DroupDownWidth = .Width - .CaptainWidth
            .DisplayColumns(1).Visible = False
            .SelectedIndex = 0
        End With


        With Jsr_DtCom
            .DataView = B_Zd_YyJsr.GetAllList.Tables(0).DefaultView
            .Init_Colum("Jsr_Name", "名称", 60, "左")
            .Init_Colum("Jsr_Jc", "简称", 60, "左")
            .Init_Colum("Jsr_Code", "编码", 0, "左")
            .DisplayMember = "Jsr_Name"
            .ValueMember = "Jsr_Code"
            .DroupDownWidth = .Width
            .MaxDropDownItems = 15
            .SelectedIndex = -1
            .RowFilterTextNull = ""
            .RowFilterNotTextNull = "Jsr_Jc"
        End With

        '初始化TDBGrid
        With MyGrid1
            .Init_Grid()
            .Init_Column("状态", "OrdersStatus", "40", "中", "", False)
            .Init_Column("冲销", "WriteOffStatus", "40", "中", "", False)
            .Init_Column("单据编码", "M_OtherIn_Code", "100", "左", "", False)

            .Init_Column("库房编码", "MaterialsWh_Code", "0", "左", "", False)
            .Init_Column("库房名称", "MaterialsWh_Name", "120", "左", "", False)

            .Init_Column("入库类别编码", "MaterialsInOut_Code", "0", "左", "", False)
            .Init_Column("入库类别", "MaterialsInOut_Name", "100", "左", "", False)

            .Init_Column("入库日期", "OtherIn_Date", "150", "中", "yyyy-MM-dd HH:mm:ss", False)
            .Init_Column("录入日期", "Input_Date", "0", "中", "yyyy-MM-dd HH:mm:ss", False)
            .Init_Column("完成日期", "Finish_Date", "150", "中", "yyyy-MM-dd HH:mm:ss", False)

            .Init_Column("经手人编码", "Jsr_Code", "0", "左", "", False)
            .Init_Column("经手人名称", "Jsr_Name", "80", "左", "", False)

            .Init_Column("总金额", "TotalMoney", "80", "右", "#######0.00##", False)

            .Init_Column("冲销编码", "WriteOff_Code", "80", "左", "", False)

            .Init_Column("备注", "M_OtherIn_Memo", "55", "左", "", False)
            .Splits(0).DisplayColumns("OrdersStatus").FetchStyle = True
            .Splits(0).DisplayColumns("WriteOffStatus").FetchStyle = True
            '.FetchRowStyles = True
        End With
    End Sub

    Private Sub Data_Init()
        IsSearchOk = False
        '获取查询条件
        Dim select_string As String
        select_string = GetCondition()
        Zb_Table = B_Materials_Other_In1.GetList(select_string).Tables(0)
        Zb_Table.PrimaryKey = New DataColumn() {Zb_Table.Columns("M_OtherIn_Code")}
        Zb_Cm = CType(BindingContext(Zb_Table, ""), CurrencyManager)

        MyGrid1.DataTable = Zb_Table
        IsSearchOk = True
    End Sub

    '获取查询条件
    Private Function GetCondition() As String
        Dim condition As String = " CONVERT(VARCHAR(10),OtherIn_Date,126) >='" & Format(Rk_Date1.Value, "yyyy-MM-dd") & "' AND CONVERT(VARCHAR(10),OtherIn_Date,126) <='" & Format(Rk_Date2.Value, "yyyy-MM-dd") & "' "
        '单据编码
        If Dj_Code_Text.Text <> "" Then
            condition = condition & " AND M_OtherIn_Code = '" & Dj_Code_Text.Text.Trim & "' "
        End If
        '冲销编码
        If Cx_Code_Text.Text <> "" Then
            condition = condition & " AND WriteOff_Code = '" & Cx_Code_Text.Text.Trim & "' "
        End If
        '完成状态
        If Dj_State_SingleCom.Text <> "全部" Then
            condition = condition & " AND OrdersStatus = '" & Dj_State_SingleCom.Text.Trim & "' "
        End If
        '冲销状态
        If Cx_State_SingleCom.Text <> "全部" Then
            If Cx_State_SingleCom.Text = "未冲销" Then
                condition = condition & " AND ISNULL(WriteOffStatus,'') = '' "
            Else
                condition = condition & " AND WriteOffStatus = '" & Cx_State_SingleCom.Text.Trim & "' "
            End If

        End If
        '库房编码
        If Kf_DtCom.SelectedValue IsNot Nothing Then
            condition = condition & " AND Materials_Other_In1.MaterialsWh_Code = '" & Kf_DtCom.SelectedValue & "' "
        End If
        '入库类别
        If InOutLb_DtCom.SelectedValue IsNot Nothing Then
            condition = condition & " AND Materials_Other_In1.MaterialsInOut_Code = '" & Kf_DtCom.SelectedValue & "' "
        End If
        '经手人
        If Jsr_DtCom.SelectedValue IsNot Nothing Then
            condition = condition & " AND Materials_Other_In1.Jsr_Code = '" & Jsr_DtCom.SelectedValue & "' "
        End If

        Return condition
    End Function

    Private Sub Search_Btn_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Search_Btn.Click
        Data_Init()
    End Sub

    Private Sub Clear_Btn_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Clear_Btn.Click
        Dj_Code_Text.Text = ""
        Cx_Code_Text.Text = ""
        Dj_State_SingleCom.Text = "全部"
        Cx_State_SingleCom.Text = "未冲销"
        Kf_DtCom.SelectedIndex = -1
        InOutLb_DtCom.SelectedIndex = -1
        Jsr_DtCom.SelectedValue = -1
        Rk_Date1.Value = Format(Now, "yyyy-MM-dd")
        Rk_Date2.Value = Format(Now, "yyyy-MM-dd")
    End Sub


    Private Sub MyGrid1_RowColChange(ByVal sender As System.Object, ByVal e As C1.Win.C1TrueDBGrid.RowColChangeEventArgs) Handles MyGrid1.RowColChange
        If Not IsLoadOk Then Exit Sub
        If Not IsSearchOk Then Exit Sub
        If (MyGrid1.Row + 1) > MyGrid1.RowCount Then
        Else
            Zb_Row = Zb_Cm.List(MyGrid1.Row).Row
            m_Rc.ChangeRow(Zb_Row)
        End If
    End Sub


    Private Sub MyGrid1_MouseUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles MyGrid1.MouseUp
        'If Not IsLoadOk Then Exit Sub
        'If Not IsSearchOk Then Exit Sub
        'If (MyGrid1.Row + 1) > MyGrid1.RowCount Then
        'Else
        '    Zb_Row = Zb_Cm.List(MyGrid1.Row).Row
        '    m_Rc.ChangeRow(Zb_Row)
        'End If
    End Sub

    Private Sub MyGrid1_FetchCellStyle(ByVal sender As System.Object, ByVal e As C1.Win.C1TrueDBGrid.FetchCellStyleEventArgs) Handles MyGrid1.FetchCellStyle

        e.CellStyle.ForeGroundPicturePosition = C1.Win.C1TrueDBGrid.ForeGroundPicturePositionEnum.PictureOnly

        If MyGrid1.Columns(e.Col).DataField = "OrdersStatus" Then
            Dim CellValue As String = MyGrid1.Columns("OrdersStatus").CellValue(e.Row).ToString()
            If CellValue = "完成" Then
                e.CellStyle.ForegroundImage = My.Resources.Resources.完成16
            Else
                e.CellStyle.ForegroundImage = My.Resources.Resources.录入16
            End If
        End If

        If MyGrid1.Columns(e.Col).DataField = "WriteOffStatus" Then
            Dim CellValue As String = MyGrid1.Columns("WriteOffStatus").CellValue(e.Row).ToString()
            If CellValue = "冲销" Then
                e.CellStyle.ForegroundImage = My.Resources.Resources.冲销16

            ElseIf CellValue = "被冲销" Then
                e.CellStyle.ForegroundImage = My.Resources.Resources.被冲销16

            Else
                e.CellStyle.ForeGroundPicturePosition = C1.Win.C1TrueDBGrid.ForeGroundPicturePositionEnum.TextOnly
            End If
        End If

    End Sub
End Class
