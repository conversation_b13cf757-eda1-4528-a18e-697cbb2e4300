﻿/**  版本信息模板在安装目录下，可自行修改。
* M_Zd_QxMenu2.cs
*
* 功 能： N/A
* 类 名： M_Zd_QxMenu2
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016-08-15 09:56:33   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace ModelOld
{
	/// <summary>
	/// M_Zd_QxMenu2:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class M_Zd_QxMenu2
	{
		public M_Zd_QxMenu2()
		{}
		#region Model
		private string _menu_code;
		private string _secondmenu_code;
		private string _secondname_name;
		private int? _secondmenu_order;
		/// <summary>
		/// 
		/// </summary>
		public string Menu_Code
		{
			set{ _menu_code=value;}
			get{return _menu_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string SecondMenu_Code
		{
			set{ _secondmenu_code=value;}
			get{return _secondmenu_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string SecondName_Name
		{
			set{ _secondname_name=value;}
			get{return _secondname_name;}
		}
		/// <summary>
		/// 
		/// </summary>
		public int? SecondMenu_Order
		{
			set{ _secondmenu_order=value;}
			get{return _secondmenu_order;}
		}
		#endregion Model

	}
}

