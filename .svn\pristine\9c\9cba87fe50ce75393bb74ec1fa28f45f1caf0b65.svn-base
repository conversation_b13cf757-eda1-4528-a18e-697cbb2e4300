﻿/**  版本信息模板在安装目录下，可自行修改。
* D_KC21.cs
*
* 功 能： N/A
* 类 名： D_KC21
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/8/24 08:44:38   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using System.Collections;
namespace DAL
{
    /// <summary>
    /// 数据访问类:D_KC21
    /// </summary>
    public partial class D_KC21
    {
        public D_KC21()
        { }
        #region  BasicMethod

        ///// <summary>
        ///// 是否存在该记录
        ///// </summary>
        //public bool Exists(string AKC190)
        //{
        //    StringBuilder strSql=new StringBuilder();
        //    strSql.Append("select count(1) from KC21");
        //    strSql.Append(" where AKC190=@AKC190 ");
        //    SqlParameter[] parameters = {
        //            new SqlParameter("@AKC190", SqlDbType.VarChar,18)			};
        //    parameters[0].Value = AKC190;

        //    return  Common.WinFormVar.Var.DbHelper.Exists(strSql.ToString(),parameters);
        //}

        ///// <summary>
        ///// 获取入院日期为 今天 的 单据号的 最大流水号    单据号：AKC190 + 4 位流水号
        ///// </summary>
        public string GetDjMaxCode(string AKC190)
        {

            string Max = AKC190.Substring(0, 2) + AKC190.Substring(6, AKC190.Length - 6) + Common.WinFormVar.Var.DbHelper.F_MaxCode("SELECT MAX(SUBSTRING(Dj_Code,LEN(Dj_Code)-1,2)) FROM dbo.KC21 WHERE AKC190 = '" + AKC190 + "'", 2);
            return Max;
        }

        /// <summary>
        /// 就诊记录插入KC21
        /// </summary>
        public bool Add(string Lb)
        {


            if (Lb == "门诊")
            {
                return MzDataDr();
            }
            else
            {
                //return ZyDataDr();
                //return ZyData_Dr();
                return ZyDataDr_OldVersion();


            }
        }

        private bool MzDataDr()
        {
            ArrayList arry = new ArrayList();
            //删除未结算未上传数据
            arry.Add("delete KC22 where CKC126=0  and editstate=0 and substring(AKC190,1,2)='MZ'");
            arry.Add("delete kc21 where isjs=0 and CKC126=0 and substring(AKC190,1,2)='MZ' and not EXISTS (select 1 from KC22 where KC22.akc190=KC21.akc190)");
            //添加就诊信息
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into KC21(AKC190 ,AKA130,AKC192 ,AKC193,AAE011,AAE036,ZKC271 ,ZKC272,ZKC274,BLH,CKC126,BKF040,BKF050,Ry_Bxlb,IsJs)");
            strSql.Append(" SELECT 'MZ'+Mz_Code,'',Mz_Date,Jb_Code,'" + HisVar.HisVar.JsrName + "',GETDATE(),Ys_Name,Ks_Name,Jb_Name,'MZ'+Mz_Code,0,Mz.Ks_Code,Mz.Ys_Code,Bxlb_Name,0 FROM dbo.Mz,Zd_YyYs,Zd_YyKs ,dbo.Zd_Bxlb");
            strSql.Append(" WHERE Mz.Bxlb_Code = Zd_Bxlb.Bxlb_Code AND Bxlb_Name IN ( '城镇职工', '城镇居民', '工伤医疗' ) AND Zd_YyYs.Ys_Code = Mz.Ys_Code  AND Zd_YyKs.Ks_Code = Mz.Ks_Code  AND NOT EXISTS (SELECT 1 FROM dbo.KC21 WHERE AKC190='MZ'+Mz_Code) ");
            strSql.Append("AND CONVERT(VARCHAR(10), Mz_Date, 126) = CONVERT(VARCHAR(10), GETDATE(), 126) ");
            strSql.Append(" and Mz_Print=0 and Mz_Money>0");
            if (HisPara.PublicConfig.MzYsz == "是")
            {
                strSql.Append("  and MzCf_Ok=1");
            }

            arry.Add(strSql.ToString());
            ////添加费用明细
            strSql = new StringBuilder();
            strSql.Append("INSERT INTO dbo.KC22 ");
            strSql.Append("(AKC190,AKC220,AKC221,AKC515,AKC223,AKC224,AKC225,AKC226,AKC227,AKA070,AKA071,AKA076,AKA072,AKA073,AKC229,ZKA100,CKC126,EditState,BKF050,AKC008,BKF040,AKC025) ");
            //strSql.Append("SELECT 'MZ'+Mz_Yp.Mz_Code,'MZ'+Mz_Yp.Mz_Code,Mz_Date,Mx_Code,Yp_Name,'1',SUM(Mz_Yp.Mz_Money)/SUM(Mz_Sl),SUM(Mz_Sl), ");
            strSql.Append("SELECT 'MZ'+Mz_Yp.Mz_Code,'MZ'+Mz_Yp.Mz_Code,Mz_Date,Mx_Code,Yp_Name,CASE WHEN Dl_Name = '卫生材料' THEN  '2' ELSE '1' END,SUM(Mz_Yp.Mz_Money)/SUM(Mz_Sl),SUM(Mz_Sl), ");
            strSql.Append("SUM(Mz_Yp.Mz_Money),Jx_Name,NULL,NULL,NULL,NULL,NULL,Mx_Gg,0,0,Mz.Ys_Code,Ys_Name,Mz.Ks_Code,Ks_Name FROM dbo.Mz_Yp,dbo.Mz,dbo.V_Yp,Zd_YyYs,Zd_YyKs WHERE Mz_Yp.Mz_Code=Mz.Mz_Code AND Mx_Code=SUBSTRING(Xx_Code,1,11)   and Mz.Ys_Code =zd_YyYs.Ys_Code and  Mz.Ks_Code =zd_YyKs.Ks_Code ");
            strSql.Append("AND EXISTS (SELECT 1 FROM dbo.KC21 WHERE isjs=0 and CKC126=0 AND 'MZ'+Mz.Mz_Code=AKC190) ");
            //strSql.Append("GROUP BY  Mz_Yp.Mz_Code,Mz_Yp.Mz_Code,Mz_Date,Mx_Code,Yp_Name,Jx_Name,Mx_Gg  ");
            strSql.Append("GROUP BY  Mz_Yp.Mz_Code,Mz_Yp.Mz_Code,Mz_Date,Mx_Code,Yp_Name,Jx_Name,Mx_Gg,Dl_Name,Mz.Ys_Code,Ys_Name,Mz.Ks_Code,Ks_Name  ");
            strSql.Append(" having SUM(Mz_Yp.Mz_Money)>0 and SUM(Mz_Sl)>0 ");
            arry.Add(strSql.ToString());

            strSql = new StringBuilder();
            strSql.Append("INSERT INTO dbo.KC22 ");
            strSql.Append("(AKC190,AKC220,AKC221,AKC515,AKC223,AKC224,AKC225,AKC226,AKC227,AKA070,AKA071,AKA076,AKA072,AKA073,AKC229,ZKA100,CKC126,EditState,BKF050,AKC008,BKF040,AKC025) ");
            strSql.Append("SELECT 'MZ'+Mz_Xm.Mz_Code,'MZ'+Mz_Xm.Mz_Code,Mz_Date,Mz_Xm.Xm_Code,Xm_Name,'2',SUM(Mz_Xm.Mz_Money)/SUM(Mz_Sl),SUM(Mz_Sl), ");
            strSql.Append("SUM(Mz_Xm.Mz_Money),NULL,NULL,NULL,NULL,NULL,NULL,Xm_Dw,0,0,Mz.Ys_Code,Ys_Name,Mz.Ks_Code,Ks_Name  FROM dbo.Mz_Xm,dbo.Mz,dbo.Zd_Ml_Xm3,Zd_YyYs,Zd_YyKs WHERE Mz_Xm.Mz_Code=Mz.Mz_Code AND Mz_Xm.Xm_Code=Zd_Ml_Xm3.Xm_Code  and Mz.Ys_Code =zd_YyYs.Ys_Code and  Mz.Ks_Code =zd_YyKs.Ks_Code ");
            strSql.Append("AND EXISTS (SELECT 1 FROM dbo.KC21 WHERE isjs=0 and CKC126=0 AND 'MZ'+Mz.Mz_Code=AKC190)  ");
            strSql.Append("and xm_name not like '%床位费%'");
            strSql.Append("GROUP BY  Mz_Xm.Mz_Code,Mz_Date,Mz_Xm.Xm_Code,Xm_Name,Xm_Dw,Mz.Ys_Code,Ys_Name,Mz.Ks_Code,Ks_Name  ");
            strSql.Append(" having SUM(Mz_Xm.Mz_Money)>0 and SUM(Mz_Sl)>0 ");
            arry.Add(strSql.ToString());

            strSql = new StringBuilder();
            strSql.Append("INSERT INTO dbo.KC22 ");
            strSql.Append("(AKC190,AKC220,AKC221,AKC515,AKC223,AKC224,AKC225,AKC226,AKC227,AKA070,AKA071,AKA076,AKA072,AKA073,AKC229,ZKA100,CKC126,EditState,BKF050,AKC008,BKF040,AKC025) ");
            strSql.Append("SELECT 'MZ'+Mz_Xm.Mz_Code,'MZ'+Mz_Xm.Mz_Code,Mz_Date,Mz_Xm.Xm_Code,Xm_Name,'3',SUM(Mz_Xm.Mz_Money)/SUM(Mz_Sl),SUM(Mz_Sl), ");
            strSql.Append("SUM(Mz_Xm.Mz_Money),NULL,NULL,NULL,NULL,NULL,NULL,Xm_Dw,0,0,Mz.Ys_Code,Ys_Name,Mz.Ks_Code,Ks_Name  FROM dbo.Mz_Xm,dbo.Mz,dbo.Zd_Ml_Xm3,Zd_YyYs,Zd_YyKs WHERE Mz_Xm.Mz_Code=Mz.Mz_Code AND Mz_Xm.Xm_Code=Zd_Ml_Xm3.Xm_Code  and Mz.Ys_Code =zd_YyYs.Ys_Code and  Mz.Ks_Code =zd_YyKs.Ks_Code ");
            strSql.Append("AND EXISTS (SELECT 1 FROM dbo.KC21 WHERE isjs=0 and CKC126=0 AND 'MZ'+Mz.Mz_Code=AKC190)  ");
            strSql.Append("and xm_name like '%床位费%'");
            strSql.Append("GROUP BY  Mz_Xm.Mz_Code,Mz_Date,Mz_Xm.Xm_Code,Xm_Name,Xm_Dw,Mz.Ys_Code,Ys_Name,Mz.Ks_Code,Ks_Name  ");
            strSql.Append(" having SUM(Mz_Xm.Mz_Money)>0 and SUM(Mz_Sl)>0 ");
            arry.Add(strSql.ToString());
            try
            {
                Common.WinFormVar.Var.DbHelper.ExecuteSqlTran(arry);
                return true;
            }
            catch (Exception e)
            {
                Common.Log.LogHelper.Error(e);
                return false;
            }
        }



        /// <summary>
        /// 费用明细 只有删除功能，没有上传功能
        /// 每次更新数据 对未上传的都重新生成处方号，处方日期
        /// 添加年终结算
        /// </summary>
        /// <returns></returns>
        private bool ZyData_Dr()
        {
            ArrayList arry = new ArrayList();
            StringBuilder sb;
            string Cf_Code_New = DateTime.Now.ToString("yyMMddHHmmssff");
            string Cf_Date_New = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

            //1.删除kc22未上传 明细
            arry.Add("DELETE KC22 WHERE CKC126 = 0 AND SUBSTRING(AKC190,1,2) = 'ZY'");
            //2.删除kc21未上传,就诊记录
            arry.Add("DELETE KC21 WHERE CKC126 = 0 AND SUBSTRING(AKC190,1,2) = 'ZY' AND NOT EXISTS(SELECT 1 FROM KC22 WHERE KC22.AKC190 =KC21.AKC190 )");
            //3.将His中未上传就诊信息 导入 kc21
            sb = new StringBuilder();
            sb.Append("INSERT INTO KC21(");
            sb.Append("AKC190,AKA130,AKC192,AKC193,AKC194,AKC195,AKC196,AAE011,AAE036,CKC126,ZKC271,ZKC272,ZKC274,ZKC275,CKA040,CKA041,");
            sb.Append("ZHUZHI,ZHIYE,PHONE,BLH,AMC026,AMC100,AMC001,AMC013,AMC008,AAE050,AAE013,AKC120,");
            sb.Append("Ry_Bxlb,isJs)");
            sb.Append("SELECT 'ZY'+Bl_Code,'',Ry_RyDate,Jb_Code,Ry_CyDate,NULL,NULL,'" + HisVar.HisVar.JsrName + "',GETDATE(),0,Ys_Name,Ks_Name,Jb_Name,NULL,NULL,Bc_Name, ");
            sb.Append("NULL,NULL,NULL,'ZY'+Bl_Code,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,Bxlb_Name,0 FROM Bl,Zd_YyYs,Zd_YyKs,Zd_YyBc,Zd_Bxlb  ");
            sb.Append("WHERE Bl.Bc_Code=Zd_YyBc.Bc_Code AND  Bl.Ks_Code=Zd_YyKs.Ks_Code AND Bl.Ys_Code=Zd_YyYs.Ys_Code AND Bl.Bxlb_Code=Zd_Bxlb.Bxlb_Code   ");
            sb.Append("AND Bxlb_Name IN ('城镇职工', '城镇居民', '工伤医疗')   AND  NOT EXISTS (SELECT 1 FROM KC21 WHERE AKC190='ZY'+Bl_Code)   ");
            arry.Add(sb.ToString());
            //4.将His中未上传药品信息 导入kc22
            sb = new StringBuilder();
            sb.Append(" INSERT  INTO KC22( AKC190 ,AKC220 ,AKC221 ,AKC515 ,AKC223 ,AKC224 ,AKC225 ,AKC226 ,AKC227 ,AKA070 ,AKA071 ,AKA076 ,AKA072 ,AKA073 ,AKC229 ,ZKA100 ,CKC126 ,EditState) ");
            sb.Append(" SELECT  'ZY' + Bl_Cf.Bl_Code ,'" + Cf_Code_New + "','" + Cf_Date_New + "',Mx_Code ,Yp_Name ,CASE WHEN Dl_Name = '卫生材料' THEN '2' ELSE '1'END ,SUM(Bl_Cfyp.Cf_Money) / SUM(Cf_Sl) ,SUM(Cf_Sl) ,SUM(Bl_Cfyp.Cf_Money) ,Jx_Name ,NULL ,NULL ,NULL ,NULL ,NULL ,Mx_Gg ,0 ,0 ");
            sb.Append(" FROM Bl_Cfyp ,V_Yp ,Bl_Cf ");
            sb.Append(" WHERE Mx_Code = SUBSTRING(Xx_Code, 1, 11)AND Bl_Cf.Cf_Code = Bl_Cfyp.Cf_Code AND Cf_Qr = '是' ");
            sb.Append(" AND EXISTS ( SELECT 1 FROM   KC21 WHERE  isJs = 0 AND 'ZY' + Bl_Cf.Bl_Code = AKC190 ) ");
            sb.Append(" AND NOT EXISTS ( SELECT 1 FROM KC22 WHERE AKC190 = 'ZY' + Bl_Cf.Bl_Code AND Mx_Code = AKC515 ) ");
            sb.Append(" GROUP BY Bl_Cf.Bl_Code ,Mx_Code ,Yp_Name,Dl_Name,Jx_Name,Mx_Gg HAVING SUM(Cf_Sl)<> 0 ");
            arry.Add(sb.ToString());
            //5.将His中未上传诊疗信息(非床位费)导入kc22
            sb = new StringBuilder();
            sb.Append(" INSERT INTO KC22( AKC190 ,AKC220 ,AKC221 ,AKC515 ,AKC223 ,AKC224 ,AKC225 ,AKC226 ,AKC227 ,AKA070 ,AKA071 ,AKA076 ,AKA072 ,AKA073 ,AKC229 ,ZKA100 ,CKC126 ,EditState) ");
            sb.Append(" SELECT  'ZY' + Bl_Cf.Bl_Code ,'" + Cf_Code_New + "' ,'" + Cf_Date_New + "' ,Bl_Cfxm.Xm_Code ,Xm_Name ,'2' ,SUM(Bl_Cfxm.Cf_Money) / SUM(Cf_Sl) ,SUM(Cf_Sl) ,SUM(Bl_Cfxm.Cf_Money) ,NULL ,NULL ,NULL ,NULL ,NULL ,NULL ,Xm_Dw ,0 ,0 ");
            sb.Append(" FROM Bl_Cfxm ,Zd_Ml_Xm3 ,Bl_Cf ");
            sb.Append(" WHERE Bl_Cfxm.Xm_Code = Zd_Ml_Xm3.Xm_Code AND Bl_Cf.Cf_Code = Bl_Cfxm.Cf_Code AND Cf_Qr = '是' ");
            sb.Append(" AND EXISTS ( SELECT 1 FROM KC21 WHERE  isJs = 0 AND 'ZY' + Bl_Cf.Bl_Code = AKC190 ) ");
            sb.Append(" AND NOT EXISTS ( SELECT 1 FROM KC22 WHERE AKC190 = 'ZY' + Bl_Cf.Bl_Code AND Bl_Cfxm.Xm_Code = AKC515 )AND Xm_Name NOT LIKE '%床位费%' ");
            sb.Append(" GROUP BY Bl_Cf.Bl_Code ,Bl_Cfxm.Xm_Code ,Xm_Name ,Xm_Dw HAVING SUM(Cf_Sl)<> 0 ");
            arry.Add(sb.ToString());
            //6.将His中未上传床位费信息 导入kc22
            sb = new StringBuilder();
            sb.Append(" INSERT INTO KC22 (AKC190,AKC220,AKC221,AKC515,AKC223,AKC224,AKC225,AKC226,AKC227,AKA070,AKA071,AKA076,AKA072,AKA073,AKC229,ZKA100,CKC126,EditState) ");
            sb.Append(" SELECT  'ZY' + Bl_Cf.Bl_Code ,'" + Cf_Code_New + "' ,'" + Cf_Date_New + "' ,Bl_Cfxm.Xm_Code ,Xm_Name ,'2' ,SUM(Bl_Cfxm.Cf_Money) / SUM(Cf_Sl) ,SUM(Cf_Sl) ,SUM(Bl_Cfxm.Cf_Money) ,NULL ,NULL ,NULL ,NULL ,NULL ,NULL ,Xm_Dw ,0 ,0 ");
            sb.Append(" FROM Bl_Cfxm ,Zd_Ml_Xm3 ,Bl_Cf");
            sb.Append(" WHERE Bl_Cfxm.Xm_Code = Zd_Ml_Xm3.Xm_Code AND Cf_Qr = '是' AND Bl_Cf.Cf_Code = Bl_Cfxm.Cf_Code ");
            sb.Append(" AND EXISTS ( SELECT 1 FROM KC21 WHERE isJs = 0 AND 'ZY' + Bl_Cf.Bl_Code = AKC190 ) ");
            sb.Append(" AND NOT EXISTS ( SELECT 1 FROM KC22 WHERE AKC190 = 'ZY' + Bl_Cf.Bl_Code AND Bl_Cfxm.Xm_Code = AKC515 )AND Xm_Name LIKE '%床位费%' ");
            sb.Append(" GROUP BY Bl_Cf.Bl_Code ,Bl_Cfxm.Xm_Code ,Xm_Name ,Xm_Dw HAVING SUM(Cf_Sl)<> 0 ");
            arry.Add(sb.ToString());

            //7 统计His中已经上传明细信息 A ,统计kc22中已经上传明细信息 B, 将A与B进行比较 得到发生改变结果 Result
            sb = new StringBuilder();
            sb.Append(" SELECT  * ,( ISNULL(A.AKC226,0) - ISNULL(B.Cf_Sl,0)) SlChaE ,( ISNULL(A.AKC227,0) - ISNULL(B.Cf_Money,0)) MoneyChaE FROM  ");
            sb.Append(" ( ");
            sb.Append(" SELECT 'ZY' + Bl_Code AS Bl_Code ,SUBSTRING(Xx_Code, 1, 11) Mx_Code ,SUM(Cf_Sl) Cf_Sl ,SUM(Bl_Cfyp.Cf_Money) Cf_Money ");
            sb.Append(" FROM Bl_Cf ,Bl_Cfyp ");
            sb.Append(" WHERE Bl_Cf.Cf_Code = Bl_Cfyp.Cf_Code AND Cf_Qr = '是'  ");
            sb.Append(" AND EXISTS ( SELECT 1 FROM KC21 WHERE  AKC190 = 'ZY' + Bl_Code )");

            sb.Append(" AND EXISTS (SELECT 1 FROM KC22 WHERE AKC190 = 'ZY' + Bl_Code AND  SUBSTRING(Xx_Code, 1, 11) = AKC515 AND CKC126 = 1) ");
            sb.Append(" GROUP BY  Bl_Code ,SUBSTRING(Xx_Code, 1, 11) ");
            sb.Append(" UNION ALL ");
            sb.Append(" SELECT 'ZY' + Bl_Code ,Xm_Code ,SUM(Cf_Sl) Cf_Sl ,SUM(Bl_Cfxm.Cf_Money) Cf_Money ");
            sb.Append(" FROM Bl_Cf ,Bl_Cfxm ");
            sb.Append(" WHERE Bl_Cf.Cf_Code = Bl_Cfxm.Cf_Code AND Cf_Qr = '是' ");
            sb.Append(" AND EXISTS ( SELECT 1 FROM KC21 WHERE AKC190 = 'ZY' + Bl_Code ) ");

            sb.Append(" AND EXISTS(SELECT 1 FROM KC22 WHERE AKC190 = 'ZY' + Bl_Code AND Xm_Code = AKC515 AND CKC126 = 1)");
            sb.Append(" GROUP BY  Bl_Code ,Xm_Code ");
            sb.Append(" ) B FULL join ");
            sb.Append(" ( SELECT AKC190 ,AKC515 ,SUM(AKC226) AKC226 ,SUM(AKC227) AKC227 FROM KC22 WHERE EditState =1 AND SUBSTRING(AKC190,1,2) = 'ZY' GROUP BY AKC190 ,AKC515) A ");
            sb.Append(" ON	a.AKC190 = B.Bl_Code AND B.Mx_Code = a.AKC515 ");
            sb.Append(" WHERE  ISNULL(A.AKC226, 0) <> ISNULL(B.Cf_Sl,0) ");

            DataSet ds = new DataSet();

            Common.WinFormVar.Var.DbHelper.QueryDt(ds, sb.ToString(), "dt", true, false);
            DataTable dt = ds.Tables["dt"];
            foreach (DataRow _row in dt.Rows)
            {
                float SlChaE = float.Parse(_row["SlChaE"].ToString());
                float MoneyChaE = float.Parse(_row["MoneyChaE"].ToString());

                string temp_AKC190 = "";
                string temp_AKC515 = "";

                if (_row["Bl_Code"] == DBNull.Value)
                {
                    temp_AKC190 = (string)_row["AKC190"];
                }
                else
                {
                    temp_AKC190 = (string)_row["Bl_Code"];
                }

                if (_row["Mx_Code"] == DBNull.Value)
                {
                    temp_AKC515 = (string)_row["AKC515"];
                }
                else
                {
                    temp_AKC515 = (string)_row["Mx_Code"];
                }

                DataSet mxds = new DataSet();
                mxds = Common.WinFormVar.Var.DbHelper.Query("SELECT * FROM KC22 WHERE AKC190='" + temp_AKC190 + "' AND AKC515='" + temp_AKC515 + "' and editstate = 1 AND IsYearEnd ='0' ");

                if (mxds.Tables[0].Rows.Count == 0)
                {
                    sb = new StringBuilder();
                    sb.Append(" INSERT  INTO KC22( AKC190 ,AKC220 ,AKC221 ,AKC515 ,AKC223 ,AKC224 ,AKC225 ,AKC226 ,AKC227 ,AKA070 ,AKA071 ,AKA076 ,AKA072 ,AKA073 ,AKC229 ,ZKA100 ,CKC126 ,EditState) ");
                    sb.Append(" SELECT TOP 1 AKC190 ,'" + Cf_Code_New + "' ,'" + Cf_Date_New + "' ,AKC515 ,AKC223 ,AKC224 ," + (MoneyChaE / SlChaE) + " ," + (-SlChaE) + " ," + (-MoneyChaE) + " ,AKA070 ,NULL ,NULL ,NULL ,NULL ,NULL ,ZKA100 ,0 ,0");
                    sb.Append(" FROM KC22 WHERE AKC190 = '" + temp_AKC190 + "'AND AKC515 = '" + temp_AKC515 + "' ");
                    arry.Add(sb.ToString());
                    continue;
                }
                else
                {
                    foreach (DataRow mxrow in mxds.Tables[0].Rows)
                    {
                        if (SlChaE == 0)
                        {
                            break;
                        }
                        if (float.Parse(mxrow["AKC226"].ToString()) == SlChaE)
                        {
                            arry.Add("UPDATE KC22 SET EditState = 3 WHERE AKC190 = '" + mxrow["AKC190"] + "'AND AKC220 = '" + mxrow["AKC220"] + "'AND AKC221 = '" + mxrow["AKC221"] + "' AND AKC515 = '" + mxrow["AKC515"] + "' and IsYearEnd = '0'");
                            SlChaE = 0;
                        }
                        else if (float.Parse(mxrow["AKC226"].ToString()) > SlChaE)
                        {

                            arry.Add("UPDATE KC22 SET EditState = 3 WHERE AKC190 = '" + mxrow["AKC190"] + "'AND AKC220 = '" + mxrow["AKC220"] + "'AND AKC221 = '" + mxrow["AKC221"] + "' AND AKC515 = '" + mxrow["AKC515"] + "'  and IsYearEnd = '0'");
                            sb = new StringBuilder();
                            sb.Append("INSERT INTO KC22( AKC190 ,AKC220 ,AKC221 ,AKC515 ,AKC223 ,AKC224 ,AKC225 ,AKC226 ,AKC227 ,AKA070 ,AKA071 ,AKA076 ,AKA072 ,AKA073 ,AKC229 ,ZKA100 ,CKC126 ,EditState )");
                            sb.Append(" SELECT AKC190 ," + Cf_Code_New + " ,'" + Cf_Date_New + "' ,AKC515 ,AKC223 ,AKC224 ,AKC225 ," + mxrow["AKC226"] + " - " + SlChaE + " ," + mxrow["AKC227"] + " - " + MoneyChaE + " ,AKA070 ,AKA071 ,AKA076 ,AKA072 ,AKA073 ,AKC229 ,ZKA100 ,0 ,0 ");
                            sb.Append(" FROM KC22 WHERE AKC190='" + mxrow["AKC190"] + "' and AKC220='" + mxrow["AKC220"] + "' and AKC515='" + mxrow["AKC515"] + "' AND AKC221 = '" + mxrow["AKC221"] + "'");
                            arry.Add(sb.ToString());

                            SlChaE = 0;
                        }
                        else
                        {

                            arry.Add("update KC22 set editstate=3  WHERE AKC190='" + mxrow["AKC190"] + "' and AKC220='" + mxrow["AKC220"] + "' and AKC515='" + mxrow["AKC515"] + "' AND AKC221 = '" + mxrow["AKC221"] + "'  and IsYearEnd = '0'");

                            SlChaE = SlChaE - float.Parse(mxrow["AKC226"].ToString());
                            MoneyChaE = MoneyChaE - float.Parse(mxrow["AKC227"].ToString());

                        }
                    }

                }

            }

            try
            {
                Common.WinFormVar.Var.DbHelper.ExecuteSqlTran(arry);
                return true;
            }
            catch (Exception e)
            {
                return false;
            }

        }

        private bool ZyDataDr_OldVersion()
        {
            ArrayList arry = new ArrayList();
            StringBuilder sb;
            string Cf_Code_New = DateTime.Now.ToString("yyMMddHHmmssff");
            string Cf_Date_New = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

            //1.删除kc22未上传 明细
            arry.Add("DELETE KC22 WHERE CKC126 = 0 AND SUBSTRING(AKC190,1,2) = 'ZY' AND EXISTS (SELECT 1 FROM KC21 WHERE KC21.AKC190 = KC22.AKC190 AND IsNewVersion = '0')");
            //2.删除kc21未上传,就诊记录
            arry.Add("DELETE KC21 WHERE CKC126 = 0 AND SUBSTRING(AKC190,1,2) = 'ZY' AND IsNewVersion = '0' AND NOT EXISTS(SELECT 1 FROM KC22 WHERE KC22.AKC190 =KC21.AKC190 )");
            ////3.将His中未上传就诊信息 导入 kc21
            //sb = new StringBuilder();
            //sb.Append("INSERT INTO KC21(");
            //sb.Append("AKC190,AKA130,AKC192,AKC193,AKC194,AKC195,AKC196,AAE011,AAE036,CKC126,ZKC271,ZKC272,ZKC274,ZKC275,CKA040,CKA041,");
            //sb.Append("ZHUZHI,ZHIYE,PHONE,BLH,AMC026,AMC100,AMC001,AMC013,AMC008,AAE050,AAE013,AKC120,");
            //sb.Append("Ry_Bxlb,isJs)");
            //sb.Append("SELECT 'ZY'+Bl_Code,'',Ry_RyDate,Jb_Code,Ry_CyDate,NULL,NULL,'" + HisVar.HisVar.JsrName + "',GETDATE(),0,Ys_Name,Ks_Name,Jb_Name,NULL,NULL,Bc_Name, ");
            //sb.Append("NULL,NULL,NULL,'ZY'+Bl_Code,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,Bxlb_Name,0 FROM Bl,Zd_YyYs,Zd_YyKs,Zd_YyBc,Zd_Bxlb  ");
            //sb.Append("WHERE Bl.Bc_Code=Zd_YyBc.Bc_Code AND  Bl.Ks_Code=Zd_YyKs.Ks_Code AND Bl.Ys_Code=Zd_YyYs.Ys_Code AND Bl.Bxlb_Code=Zd_Bxlb.Bxlb_Code   ");
            //sb.Append("AND Bxlb_Name IN ('城镇职工', '城镇居民', '工伤医疗')   AND  NOT EXISTS (SELECT 1 FROM KC21 WHERE AKC190='ZY'+Bl_Code)   ");
            //arry.Add(sb.ToString());
            //4.将His中未上传药品信息 导入kc22
            //20210325 修改医保kc22明细表

            sb = new StringBuilder();
            sb.Append(" INSERT  INTO KC22( AKC190 ,AKC220 ,AKC221 ,AKC515 ,AKC223 ,AKC224 ,AKC225 ,AKC226 ,AKC227 ,AKA070 ,AKA071 ,AKA076 ,AKA072 ,AKA073 ,AKC229 ,ZKA100 ,CKC126 ,EditState,ZKC258,BKF050,AKC008,BKF040,AKC025) ");
            sb.Append(" SELECT  'ZY' + Bl_Cf.Bl_Code ,'" + Cf_Code_New + "','" + Cf_Date_New + "',Mx_Code ,Yp_Name ,CASE WHEN Dl_Name = '卫生材料' THEN '2' ELSE '1'END ,SUM(Bl_Cfyp.Cf_Money) / SUM(Cf_Sl) ,SUM(Cf_Sl) ,SUM(Bl_Cfyp.Cf_Money) ,Jx_Name ,NULL ,NULL ,NULL ,NULL ,NULL ,Mx_Gg ,0 ,0 ");
            sb.Append(" FROM Bl_Cfyp ,V_Yp ,Bl_Cf ,Zd_YyYs,Zd_YyKs ");
            sb.Append(" WHERE Mx_Code = SUBSTRING(Xx_Code, 1, 11)AND Bl_Cf.Cf_Code = Bl_Cfyp.Cf_Code AND Cf_Qr = '是' and Bl_Cf.Ys_Code =zd_YyYs.Ys_Code and  Bl_Cf.Ks_Code =zd_YyKs.Ks_Code ");
            sb.Append(" AND EXISTS ( SELECT 1 FROM   KC21 WHERE  isJs = 0 AND IsNewVersion = '0' AND 'ZY' + Bl_Cf.Bl_Code = AKC190 ) ");
            sb.Append(" AND NOT EXISTS ( SELECT 1 FROM KC22 WHERE AKC190 = 'ZY' + Bl_Cf.Bl_Code AND Mx_Code = AKC515 ) ");
            sb.Append(" GROUP BY Bl_Cf.Bl_Code ,Mx_Code ,Yp_Name,Dl_Name,Jx_Name,Mx_Gg,Bl_cf.Ys_Code,Ys_Name,Bl_cf.Ks_Code,Ks_Name HAVING SUM(Cf_Sl)<> 0 ");
            arry.Add(sb.ToString());
            //5.将His中未上传诊疗信息(非床位费)导入kc22
            sb = new StringBuilder();
            sb.Append(" INSERT INTO KC22( AKC190 ,AKC220 ,AKC221 ,AKC515 ,AKC223 ,AKC224 ,AKC225 ,AKC226 ,AKC227 ,AKA070 ,AKA071 ,AKA076 ,AKA072 ,AKA073 ,AKC229 ,ZKA100 ,CKC126 ,EditState,ZKC258,BKF050,AKC008,BKF040,AKC025) ");
            sb.Append(" SELECT  'ZY' + Bl_Cf.Bl_Code ,'" + Cf_Code_New + "' ,'" + Cf_Date_New + "' ,Bl_Cfxm.Xm_Code ,Xm_Name ,'2' ,SUM(Bl_Cfxm.Cf_Money) / SUM(Cf_Sl) ,SUM(Cf_Sl) ,SUM(Bl_Cfxm.Cf_Money) ,NULL ,NULL ,NULL ,NULL ,NULL ,NULL ,Xm_Dw ,0 ,0 ");
            sb.Append(" FROM Bl_Cfxm ,Zd_Ml_Xm3 ,Bl_Cf,Zd_YyYs,Zd_YyKs ");
            sb.Append(" WHERE Bl_Cfxm.Xm_Code = Zd_Ml_Xm3.Xm_Code AND Bl_Cf.Cf_Code = Bl_Cfxm.Cf_Code AND Cf_Qr = '是' and Bl_Cf.Ys_Code =zd_YyYs.Ys_Code and  Bl_Cf.Ks_Code =zd_YyKs.Ks_Code ");
            sb.Append(" AND EXISTS ( SELECT 1 FROM KC21 WHERE  isJs = 0 AND IsNewVersion = '0' AND 'ZY' + Bl_Cf.Bl_Code = AKC190 ) ");
            sb.Append(" AND NOT EXISTS ( SELECT 1 FROM KC22 WHERE AKC190 = 'ZY' + Bl_Cf.Bl_Code AND Bl_Cfxm.Xm_Code = AKC515 )AND Xm_Name NOT LIKE '%床位费%' ");
            sb.Append(" GROUP BY Bl_Cf.Bl_Code ,Bl_Cfxm.Xm_Code ,Xm_Name ,Xm_Dw,Bl_cf.Ys_Code,Ys_Name,Bl_cf.Ks_Code,Ks_Name HAVING SUM(Cf_Sl)<> 0 ");
            arry.Add(sb.ToString());
            //6.将His中未上传床位费信息 导入kc22
            sb = new StringBuilder();
            sb.Append(" INSERT INTO KC22 (AKC190,AKC220,AKC221,AKC515,AKC223,AKC224,AKC225,AKC226,AKC227,AKA070,AKA071,AKA076,AKA072,AKA073,AKC229,ZKA100,CKC126,EditState,ZKC258,BKF050,AKC008,BKF040,AKC025) ");
            sb.Append(" SELECT  'ZY' + Bl_Cf.Bl_Code ,'" + Cf_Code_New + "' ,'" + Cf_Date_New + "' ,Bl_Cfxm.Xm_Code ,Xm_Name ,'2' ,SUM(Bl_Cfxm.Cf_Money) / SUM(Cf_Sl) ,SUM(Cf_Sl) ,SUM(Bl_Cfxm.Cf_Money) ,NULL ,NULL ,NULL ,NULL ,NULL ,NULL ,Xm_Dw ,0 ,0 ");
            sb.Append(" FROM Bl_Cfxm ,Zd_Ml_Xm3 ,Bl_Cf,Zd_YyYs,Zd_YyKs ");
            sb.Append(" WHERE Bl_Cfxm.Xm_Code = Zd_Ml_Xm3.Xm_Code AND Cf_Qr = '是' AND Bl_Cf.Cf_Code = Bl_Cfxm.Cf_Code and Bl_Cf.Ys_Code =zd_YyYs.Ys_Code and  Bl_Cf.Ks_Code =zd_YyKs.Ks_Code ");
            sb.Append(" AND EXISTS ( SELECT 1 FROM KC21 WHERE isJs = 0 AND IsNewVersion = '0' AND 'ZY' + Bl_Cf.Bl_Code = AKC190 ) ");
            sb.Append(" AND NOT EXISTS ( SELECT 1 FROM KC22 WHERE AKC190 = 'ZY' + Bl_Cf.Bl_Code AND Bl_Cfxm.Xm_Code = AKC515 )AND Xm_Name LIKE '%床位费%' ");
            sb.Append(" GROUP BY Bl_Cf.Bl_Code ,Bl_Cfxm.Xm_Code ,Xm_Name ,Xm_Dw,Bl_cf.Ys_Code,Ys_Name,Bl_cf.Ks_Code,Ks_Name HAVING SUM(Cf_Sl)<> 0 ");
            arry.Add(sb.ToString());

            //7 统计His中已经上传明细信息 A ,统计kc22中已经上传明细信息 B, 将A与B进行比较 得到发生改变结果 Result
            sb = new StringBuilder();
            sb.Append(" SELECT  * ,( ISNULL(A.AKC226,0) - ISNULL(B.Cf_Sl,0)) SlChaE ,( ISNULL(A.AKC227,0) - ISNULL(B.Cf_Money,0)) MoneyChaE FROM  ");
            sb.Append(" ( ");
            sb.Append(" SELECT 'ZY' + Bl_Code AS Bl_Code ,SUBSTRING(Xx_Code, 1, 11) Mx_Code ,SUM(Cf_Sl) Cf_Sl ,SUM(Bl_Cfyp.Cf_Money) Cf_Money ");
            sb.Append(" FROM Bl_Cf ,Bl_Cfyp ");
            sb.Append(" WHERE Bl_Cf.Cf_Code = Bl_Cfyp.Cf_Code AND Cf_Qr = '是'  ");
            sb.Append(" AND EXISTS ( SELECT 1 FROM KC21 WHERE  AKC190 = 'ZY' + Bl_Code AND IsNewVersion = '0')");

            sb.Append(" AND EXISTS (SELECT 1 FROM KC22 WHERE AKC190 = 'ZY' + Bl_Code AND  SUBSTRING(Xx_Code, 1, 11) = AKC515 AND CKC126 = 1) ");
            sb.Append(" GROUP BY  Bl_Code ,SUBSTRING(Xx_Code, 1, 11) ");
            sb.Append(" UNION ALL ");
            sb.Append(" SELECT 'ZY' + Bl_Code ,Xm_Code ,SUM(Cf_Sl) Cf_Sl ,SUM(Bl_Cfxm.Cf_Money) Cf_Money ");
            sb.Append(" FROM Bl_Cf ,Bl_Cfxm ");
            sb.Append(" WHERE Bl_Cf.Cf_Code = Bl_Cfxm.Cf_Code AND Cf_Qr = '是' ");
            sb.Append(" AND EXISTS ( SELECT 1 FROM KC21 WHERE AKC190 = 'ZY' + Bl_Code AND IsNewVersion = '0') ");

            sb.Append(" AND EXISTS(SELECT 1 FROM KC22 WHERE AKC190 = 'ZY' + Bl_Code AND Xm_Code = AKC515 AND CKC126 = 1)");
            sb.Append(" GROUP BY  Bl_Code ,Xm_Code ");
            sb.Append(" ) B FULL join ");
            sb.Append(" ( SELECT AKC190 ,AKC515 ,SUM(AKC226) AKC226 ,SUM(AKC227) AKC227 FROM KC22 WHERE EditState =1 AND SUBSTRING(AKC190,1,2) = 'ZY' AND EXISTS (SELECT 1 FROM KC21 WHERE KC21.AKC190 = KC22.AKC190 AND IsNewVersion = '0') GROUP BY AKC190 ,AKC515) A ");
            sb.Append(" ON	a.AKC190 = B.Bl_Code AND B.Mx_Code = a.AKC515 ");
            sb.Append(" WHERE  ISNULL(A.AKC226, 0) <> ISNULL(B.Cf_Sl,0) ");

            DataSet ds = new DataSet();

            Common.WinFormVar.Var.DbHelper.QueryDt(ds, sb.ToString(), "dt", true, false);
            DataTable dt = ds.Tables["dt"];
            foreach (DataRow _row in dt.Rows)
            {
                float SlChaE = float.Parse(_row["SlChaE"].ToString());
                float MoneyChaE = float.Parse(_row["MoneyChaE"].ToString());

                string temp_AKC190 = "";
                string temp_AKC515 = "";

                if (_row["Bl_Code"] == DBNull.Value)
                {
                    temp_AKC190 = (string)_row["AKC190"];
                }
                else
                {
                    temp_AKC190 = (string)_row["Bl_Code"];
                }

                if (_row["Mx_Code"] == DBNull.Value)
                {
                    temp_AKC515 = (string)_row["AKC515"];
                }
                else
                {
                    temp_AKC515 = (string)_row["Mx_Code"];
                }

                DataSet mxds = new DataSet();
                mxds = Common.WinFormVar.Var.DbHelper.Query("SELECT * FROM KC22 WHERE AKC190='" + temp_AKC190 + "' AND AKC515='" + temp_AKC515 + "' and editstate = 1 AND IsYearEnd ='0' ");

                if (mxds.Tables[0].Rows.Count == 0)
                {
                    sb = new StringBuilder();
                    sb.Append(" INSERT  INTO KC22( AKC190 ,AKC220 ,AKC221 ,AKC515 ,AKC223 ,AKC224 ,AKC225 ,AKC226 ,AKC227 ,AKA070 ,AKA071 ,AKA076 ,AKA072 ,AKA073 ,AKC229 ,ZKA100 ,CKC126 ,EditState) ");
                    sb.Append(" SELECT TOP 1 AKC190 ,'" + Cf_Code_New + "' ,'" + Cf_Date_New + "' ,AKC515 ,AKC223 ,AKC224 ," + (MoneyChaE / SlChaE) + " ," + (-SlChaE) + " ," + (-MoneyChaE) + " ,AKA070 ,NULL ,NULL ,NULL ,NULL ,NULL ,ZKA100 ,0 ,0");
                    sb.Append(" FROM KC22 WHERE AKC190 = '" + temp_AKC190 + "'AND AKC515 = '" + temp_AKC515 + "' ");
                    arry.Add(sb.ToString());
                    continue;
                }
                else
                {
                    foreach (DataRow mxrow in mxds.Tables[0].Rows)
                    {
                        if (SlChaE == 0)
                        {
                            break;
                        }
                        if (float.Parse(mxrow["AKC226"].ToString()) == SlChaE)
                        {
                            arry.Add("UPDATE KC22 SET EditState = 3 WHERE AKC190 = '" + mxrow["AKC190"] + "'AND AKC220 = '" + mxrow["AKC220"] + "'AND AKC221 = '" + mxrow["AKC221"] + "' AND AKC515 = '" + mxrow["AKC515"] + "' and IsYearEnd = '0'");
                            SlChaE = 0;
                        }
                        else if (float.Parse(mxrow["AKC226"].ToString()) > SlChaE)
                        {

                            arry.Add("UPDATE KC22 SET EditState = 3 WHERE AKC190 = '" + mxrow["AKC190"] + "'AND AKC220 = '" + mxrow["AKC220"] + "'AND AKC221 = '" + mxrow["AKC221"] + "' AND AKC515 = '" + mxrow["AKC515"] + "'  and IsYearEnd = '0'");
                            sb = new StringBuilder();
                            sb.Append("INSERT INTO KC22( AKC190 ,AKC220 ,AKC221 ,AKC515 ,AKC223 ,AKC224 ,AKC225 ,AKC226 ,AKC227 ,AKA070 ,AKA071 ,AKA076 ,AKA072 ,AKA073 ,AKC229 ,ZKA100 ,CKC126 ,EditState )");
                            sb.Append(" SELECT AKC190 ," + Cf_Code_New + " ,'" + Cf_Date_New + "' ,AKC515 ,AKC223 ,AKC224 ,AKC225 ," + mxrow["AKC226"] + " - " + SlChaE + " ," + mxrow["AKC227"] + " - " + MoneyChaE + " ,AKA070 ,AKA071 ,AKA076 ,AKA072 ,AKA073 ,AKC229 ,ZKA100 ,0 ,0 ");
                            sb.Append(" FROM KC22 WHERE AKC190='" + mxrow["AKC190"] + "' and AKC220='" + mxrow["AKC220"] + "' and AKC515='" + mxrow["AKC515"] + "' AND AKC221 = '" + mxrow["AKC221"] + "'");
                            arry.Add(sb.ToString());

                            SlChaE = 0;
                        }
                        else
                        {

                            arry.Add("update KC22 set editstate=3  WHERE AKC190='" + mxrow["AKC190"] + "' and AKC220='" + mxrow["AKC220"] + "' and AKC515='" + mxrow["AKC515"] + "' AND AKC221 = '" + mxrow["AKC221"] + "'  and IsYearEnd = '0'");

                            SlChaE = SlChaE - float.Parse(mxrow["AKC226"].ToString());
                            MoneyChaE = MoneyChaE - float.Parse(mxrow["AKC227"].ToString());

                        }
                    }

                }

            }

            try
            {
                Common.WinFormVar.Var.DbHelper.ExecuteSqlTran(arry);
                return true;
            }
            catch (Exception e)
            {
                return false;
            }

        }
        public bool ZyDataDr_NewVersion()
        {
            ArrayList arry = new ArrayList();
            StringBuilder sb;
            //如果一次住院，已经有费用明细上传 则部清除该次住院费用明细，如果没有费用明细上传则 清除 该次住院费用明细
            arry.Add("DELETE KC22 WHERE SUBSTRING(KC22.AKC190,1,2) = 'ZY' AND  NOT EXISTS(SELECT 1 FROM KC22 T WHERE T.CKC126 = 1 AND T.AKC190 = kc22.AKC190) AND EXISTS (SELECT 1 FROM KC21 WHERE KC21.AKC190 = KC22.AKC190 AND IsNewVersion = '1')");
            //删除  一条费用明细都没上传，并且本身没有上传的就诊信息
            arry.Add("DELETE KC21 WHERE SUBSTRING(AKC190,1,2) = 'ZY' AND CKC126 = 0 AND IsNewVersion = '1' AND NOT EXISTS (SELECT 1 FROM KC22 WHERE KC22.AKC190 = KC21.AKC190)");
            //导入就诊信息
            sb = new StringBuilder();
            sb.Append(" INSERT INTO KC21(");
            sb.Append(" AKC190,AKA130,AKC192,AKC193,AKC194,AKC195,AKC196,AAE011,AAE036,CKC126,ZKC271,ZKC272,ZKC274,ZKC275,CKA040,CKA041,");
            sb.Append(" ZHUZHI,ZHIYE,PHONE,BLH,AMC026,AMC100,AMC001,AMC013,AMC008,AAE050,AAE013,AKC120,");
            sb.Append(" Ry_Bxlb,isJs,IsNewVersion)");
            sb.Append(" SELECT 'ZY'+Bl_Code,'',Ry_RyDate,Jb_Code,Ry_CyDate,NULL,NULL,'" + HisVar.HisVar.JsrName + "',GETDATE(),0,Ys_Name,Ks_Name,Jb_Name,NULL,NULL,Bc_Name, ");
            sb.Append(" NULL,NULL,NULL,'ZY'+Bl_Code,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,Bxlb_Name,0,'1' FROM Bl,Zd_YyYs,Zd_YyKs,Zd_YyBc,Zd_Bxlb");
            sb.Append(" WHERE Bl.Bc_Code=Zd_YyBc.Bc_Code AND  Bl.Ks_Code=Zd_YyKs.Ks_Code AND Bl.Ys_Code=Zd_YyYs.Ys_Code AND Bl.Bxlb_Code=Zd_Bxlb.Bxlb_Code ");
            sb.Append(" AND Bxlb_Name IN ('城镇职工', '城镇居民', '工伤医疗')   AND  NOT EXISTS (SELECT 1 FROM KC21 WHERE AKC190='ZY'+Bl_Code) ");
            arry.Add(sb.ToString());
            //导入费用明细 药品信息
            sb = new StringBuilder();
            sb.Append(" INSERT INTO KC22( AKC190 ,AKC220 ,AKC221 ,AKC515 ,AKC223 ,AKC224 ,AKC225 ,HisCount,AKC226 ,AKC227 ,AKA070 ,AKA071 ,AKA076 ,AKA072 ,AKA073 ,AKC229 ,ZKA100 ,CKC126 ,EditState)");
            sb.Append(" SELECT  'ZY' + BL_CF.BL_CODE ,'ZY' + BL_CF.CF_CODE ,CF_DATE ,Mx_Code ,Yp_Name ,");
            sb.Append(" CASE WHEN Dl_Name = '卫生材料' THEN '2'ELSE '1'END ,");
            //sb.Append(" SUM(BL_CFYP.CF_MONEY) / SUM(CF_SL) ,SUM(CF_SL) ,SUM(CF_SL) ,SUM(BL_CFYP.CF_MONEY) ,");
            sb.Append(" CF_DJ ,SUM(CF_SL) ,SUM(CF_SL) ,SUM(BL_CFYP.CF_MONEY) ,");
            sb.Append(" Jx_Name ,NULL ,NULL ,NULL ,NULL ,NULL ,Mx_Gg ,0 ,0");
            sb.Append(" FROM    BL_CFYP ,V_Yp ,BL_CF");
            sb.Append(" WHERE   BL_CF.CF_CODE = BL_CFYP.CF_CODE AND SUBSTRING(BL_CFYP.XX_CODE, 1, 11) = V_Yp.Mx_Code AND BL_CF.CF_QR = '是'");
            sb.Append(" AND EXISTS ( SELECT 1 FROM KC21 WHERE  isJs = 0 AND IsNewVersion = '1' AND 'ZY' + BL_CF.BL_CODE = AKC190 )");
            sb.Append(" AND NOT EXISTS ( SELECT 1 FROM KC22 WHERE  AKC190 = 'ZY' + BL_CF.BL_CODE AND AKC220 = 'ZY' + BL_CF.CF_CODE AND V_Yp.Mx_Code = AKC515 )");
            sb.Append(" GROUP BY BL_CF.BL_CODE ,BL_CF.CF_CODE ,CF_DATE ,Mx_Code ,Yp_Name ,Jx_Name ,Mx_Gg ,Dl_Name ,CF_DJ ");
            arry.Add(sb.ToString());
            //导入诊疗非床位费
            sb = new StringBuilder();
            sb.Append(" INSERT  INTO KC22( AKC190 ,AKC220 ,AKC221 ,AKC515 ,AKC223 ,AKC224 ,AKC225 ,HisCount,AKC226 ,AKC227 ,AKA070 ,AKA071 ,AKA076 ,AKA072 ,AKA073 ,AKC229 ,ZKA100 ,CKC126 ,EditState)");
            sb.Append(" SELECT  'ZY' + BL_CF.BL_CODE ,'ZY' + BL_CF.CF_CODE ,CF_DATE ,BL_CFXM.XM_CODE ,XM_NAME ,'2' ,");
            //sb.Append(" SUM(BL_CFXM.CF_MONEY) / SUM(CF_SL) ,SUM(CF_SL) ,SUM(CF_SL) ,SUM(BL_CFXM.CF_MONEY) ,");
            sb.Append(" CF_DJ ,SUM(CF_SL) ,SUM(CF_SL) ,SUM(BL_CFXM.CF_MONEY) ,");
            sb.Append(" NULL ,NULL ,NULL ,NULL ,NULL ,NULL ,XM_DW ,0 ,0");
            sb.Append(" FROM    BL_CF,BL_CFXM ,ZD_ML_XM3 ");
            sb.Append(" WHERE   BL_CF.CF_CODE = BL_CFXM.CF_CODE AND BL_CFXM.XM_CODE = ZD_ML_XM3.XM_CODE  AND CF_QR = '是'  AND XM_NAME NOT LIKE '%床位费%'");
            sb.Append(" AND EXISTS ( SELECT 1 FROM KC21 WHERE isJs = 0 AND IsNewVersion = '1' AND 'ZY' + BL_CF.BL_CODE = AKC190 )");
            sb.Append(" AND NOT EXISTS ( SELECT 1 FROM KC22 WHERE  AKC220 = 'ZY' + BL_CF.CF_CODE AND AKC190 = 'ZY' + BL_CF.BL_CODE AND BL_CFXM.XM_CODE = AKC515 )");
            sb.Append(" GROUP BY BL_CF.BL_CODE ,BL_CF.CF_CODE ,CF_DATE ,BL_CFXM.XM_CODE ,XM_NAME ,XM_DW ,CF_DJ");
            arry.Add(sb.ToString());
            //--导入诊疗床位费
            sb = new StringBuilder();
            sb.Append(" INSERT  INTO KC22( AKC190 ,AKC220 ,AKC221 ,AKC515 ,AKC223 ,AKC224 ,AKC225 ,HisCount,AKC226 ,AKC227 ,AKA070 ,AKA071 ,AKA076 ,AKA072 ,AKA073 ,AKC229 ,ZKA100 ,CKC126 ,EditState)");
            sb.Append(" SELECT  'ZY' + BL_CF.BL_CODE ,'ZY' + BL_CF.CF_CODE ,CF_DATE ,BL_CFXM.XM_CODE ,XM_NAME ,'2' ,");
            //sb.Append(" SUM(BL_CFXM.CF_MONEY) / SUM(CF_SL) ,SUM(CF_SL) ,SUM(CF_SL) ,SUM(BL_CFXM.CF_MONEY) ,");
            sb.Append(" CF_DJ ,SUM(CF_SL) ,SUM(CF_SL) ,SUM(BL_CFXM.CF_MONEY) ,");
            sb.Append(" NULL ,NULL ,NULL ,NULL ,NULL ,NULL ,XM_DW ,0 ,0");
            sb.Append(" FROM BL_CF,BL_CFXM ,ZD_ML_XM3 ");
            sb.Append(" WHERE BL_CF.CF_CODE = BL_CFXM.CF_CODE AND BL_CFXM.XM_CODE = ZD_ML_XM3.XM_CODE  AND CF_QR = '是' AND XM_NAME LIKE '%床位费%'");
            sb.Append(" AND EXISTS ( SELECT 1 FROM   KC21 WHERE  isJs = 0 AND IsNewVersion = '1' AND 'ZY' + BL_CF.BL_CODE = AKC190 )");
            sb.Append(" AND NOT EXISTS ( SELECT 1 FROM   KC22 WHERE  AKC220 = 'ZY' + BL_CF.CF_CODE AND AKC190 = 'ZY' + BL_CF.BL_CODE AND BL_CFXM.XM_CODE = AKC515 )");
            sb.Append(" GROUP BY BL_CF.BL_CODE ,BL_CF.CF_CODE ,CF_DATE ,BL_CFXM.XM_CODE ,XM_NAME ,XM_DW ,CF_DJ");
            arry.Add(sb.ToString());

            try
            {
                Common.WinFormVar.Var.DbHelper.ExecuteSqlTran(arry);

            }
            catch (Exception e)
            {
                return false;
            }

            DataTable minusDt = null;
            //负数费用明细
            try
            {
                string minusDataSQL = "SELECT AKC190 ,AKC220 ,AKC221 ,AKC515 ,AKC223 ,AKC224 ,AKC225 ,AKC226 ,AKC227 ,AKA070 ,AKA071 ,AKA076 ,AKA072 ,AKA073 ,AKC229 ,ZKA100 ,CKC126 ,EditState ,old_AKC221 ,IsYearEnd FROM KC22 WHERE SUBSTRING(KC22.AKC190,1,2) = 'ZY' AND IsYearEnd = '0' AND CKC126 = 0 AND EditState = '0' AND AKC226 <0 AND EXISTS(SELECT 1 FROM KC21 WHERE KC21.AKC190 = KC22.AKC190 AND IsNewVersion = '1' AND isJs = 0 )";
                minusDt = Common.WinFormVar.Var.DbHelper.Query(minusDataSQL).Tables[0];
            }
            catch (Exception e)
            {
                return false;

            }

            //处理负数费用明细


            foreach (DataRow minusrow in minusDt.Rows)
            {
                DataTable mxDt = null;
                StringBuilder mxSQL = new StringBuilder();
                mxSQL.Append(" SELECT AKC190 ,AKC220 ,AKC221 ,AKC515 ,AKC223 ,AKC224 ,AKC225 ,AKC226 ,AKC227 ,AKA070 ,AKA071 ,AKA076 ,AKA072 ,AKA073 ,AKC229 ,ZKA100 ,CKC126 ,EditState ,old_AKC221 ,IsYearEnd ");
                mxSQL.Append(" FROM KC22 WHERE SUBSTRING(AKC190,1,2) = 'ZY' AND  IsYearEnd = '0' AND EditState <> 3 AND AKC226 > 0 ");
                mxSQL.Append(" AND EXISTS(SELECT 1 FROM KC21 WHERE KC21.AKC190 = KC22.AKC190 AND IsNewVersion = '1' AND isJs = 0)");
                mxSQL.Append(" AND AKC190 ='" + minusrow["AKC190"] + "' AND AKC515 = '" + minusrow["AKC515"] + "'  ");
                mxSQL.Append(" ORDER BY EditState asc,AKC221 desc");
                mxDt = Common.WinFormVar.Var.DbHelper.Query(mxSQL.ToString()).Tables[0];


                arry.Clear();
                foreach (DataRow mxrow in mxDt.Rows)
                {
                    if ((decimal)minusrow["AKC226"] >= 0)
                    {
                        break;
                    }

                    decimal sum = (decimal)mxrow["AKC226"] + (decimal)minusrow["AKC226"];

                    if (sum == 0)
                    {
                        if ((string)mxrow["EditState"] == "0")
                        {
                            arry.Add(" UPDATE KC22 SET AKC226 = 0,AKC227 = 0 , IsEnable = '0' WHERE AKC190 = '" + mxrow["AKC190"] + "' AND AKC220 = '" + mxrow["AKC220"] + "' AND AKC221 = '" + mxrow["AKC221"] + "' AND AKC515 = '" + mxrow["AKC515"] + "'");
                        }
                        if ((string)mxrow["EditState"] == "1")
                        {
                            arry.Add(" UPDATE KC22 SET AKC226 = 0 ,AKC227 = 0,EditState = '3' WHERE AKC190 = '" + mxrow["AKC190"] + "' AND AKC220 = '" + mxrow["AKC220"] + "' AND AKC221 = '" + mxrow["AKC221"] + "' AND AKC515 = '" + mxrow["AKC515"] + "'");
                        }


                        arry.Add(" UPDATE KC22 SET AKC226 = 0,AKC227 = 0 , IsEnable = '0' WHERE AKC190 = '" + minusrow["AKC190"] + "' AND AKC220 = '" + minusrow["AKC220"] + "' AND AKC221 = '" + minusrow["AKC221"] + "' AND AKC515 = '" + minusrow["AKC515"] + "'");
                        minusrow["AKC226"] = 0;

                    }
                    else if (sum < 0)
                    {
                        if ((string)mxrow["EditState"] == "0")
                        {
                            arry.Add(" UPDATE KC22 SET AKC226 = 0,AKC227 = 0 , IsEnable = '0' WHERE AKC190 = '" + mxrow["AKC190"] + "' AND AKC220 = '" + mxrow["AKC220"] + "' AND AKC221 = '" + mxrow["AKC221"] + "' AND AKC515 = '" + mxrow["AKC515"] + "'");
                        }
                        if ((string)mxrow["EditState"] == "1")
                        {
                            arry.Add(" UPDATE KC22 SET AKC226 = 0 ,AKC227 = 0,EditState = '3' WHERE AKC190 = '" + mxrow["AKC190"] + "' AND AKC220 = '" + mxrow["AKC220"] + "' AND AKC221 = '" + mxrow["AKC221"] + "' AND AKC515 = '" + mxrow["AKC515"] + "'");
                        }


                        arry.Add(" UPDATE KC22 SET AKC226 = " + sum + ",AKC227 = " + sum + " * AKC225 WHERE AKC190 = '" + minusrow["AKC190"] + "' AND AKC220 = '" + minusrow["AKC220"] + "' AND AKC221 = '" + minusrow["AKC221"] + "' AND AKC515 = '" + minusrow["AKC515"] + "'");
                        minusrow["AKC226"] = sum;
                    }
                    else //sum > 0
                    {
                        if ((string)mxrow["EditState"] == "0")
                        {
                            arry.Add(" UPDATE KC22 SET AKC226 = " + sum + ",AKC227 = " + sum + " * AKC225 WHERE AKC190 = '" + mxrow["AKC190"] + "' AND AKC220 = '" + mxrow["AKC220"] + "' AND AKC221 = '" + mxrow["AKC221"] + "' AND AKC515 = '" + mxrow["AKC515"] + "'");
                            arry.Add(" UPDATE KC22 SET AKC226 = 0,AKC227 = 0 , IsEnable = '0' WHERE AKC190 = '" + minusrow["AKC190"] + "' AND AKC220 = '" + minusrow["AKC220"] + "' AND AKC221 = '" + minusrow["AKC221"] + "' AND AKC515 = '" + minusrow["AKC515"] + "'");
                            minusrow["AKC226"] = 0;

                        }
                        if ((string)mxrow["EditState"] == "1")
                        {
                            arry.Add(" UPDATE KC22 SET AKC226 = 0 ,AKC227 = 0,EditState = '3' WHERE AKC190 = '" + mxrow["AKC190"] + "' AND AKC220 = '" + mxrow["AKC220"] + "' AND AKC221 = '" + mxrow["AKC221"] + "' AND AKC515 = '" + mxrow["AKC515"] + "'");
                            arry.Add(" UPDATE KC22 SET AKC226 = " + sum + ", AKC227 = " + sum + " * AKC225 WHERE AKC190 = '" + minusrow["AKC190"] + "' AND AKC220 = '" + minusrow["AKC220"] + "' AND AKC221 = '" + minusrow["AKC221"] + "' AND AKC515 = '" + minusrow["AKC515"] + "'");
                            minusrow["AKC226"] = sum;

                        }

                    }

                }

                try
                {
                    Common.WinFormVar.Var.DbHelper.ExecuteSqlTran(arry);
                }
                catch (Exception e)
                {
                    return false;
                }


            }

            return true;
        }
        /// <summary>
        /// 导入住院就诊信息
        /// </summary>
        /// <returns></returns>
        public bool ImportZyJzxx(ref string result)
        {
            ArrayList arry = new ArrayList();
            StringBuilder sb;
            //如果一次住院，已经有费用明细上传 则部清除该次住院费用明细，如果没有费用明细上传则 清除 该次住院费用明细
            arry.Add("DELETE KC22 WHERE SUBSTRING(KC22.AKC190,1,2) = 'ZY' AND  NOT EXISTS(SELECT 1 FROM KC22 T WHERE T.CKC126 = 1 AND T.AKC190 = kc22.AKC190) AND EXISTS (SELECT 1 FROM KC21 WHERE KC21.AKC190 = KC22.AKC190 AND IsNewVersion = '1')");
            //删除  一条费用明细都没上传，并且本身没有上传的就诊信息
            arry.Add("DELETE KC21 WHERE SUBSTRING(AKC190,1,2) = 'ZY' AND CKC126 = 0 AND IsNewVersion = '1' AND NOT EXISTS (SELECT 1 FROM KC22 WHERE KC22.AKC190 = KC21.AKC190)");
            //导入就诊信息
            sb = new StringBuilder();
            sb.Append(" INSERT INTO KC21(");
            sb.Append(" AKC190,AKA130,AKC192,AKC193,AKC194,AKC195,AKC196,AAE011,AAE036,CKC126,ZKC271,ZKC272,ZKC274,ZKC275,CKA040,CKA041,");
            sb.Append(" ZHUZHI,ZHIYE,PHONE,BLH,AMC026,AMC100,AMC001,AMC013,AMC008,AAE050,AAE013,AKC120,BKF040,BKF050,");
            sb.Append(" Ry_Bxlb,isJs,IsNewVersion)");
            sb.Append(" SELECT 'ZY'+Bx_BlCode,'',Ry_RyDate,Jb_Code,Ry_CyDate,NULL,NULL,'" + HisVar.HisVar.JsrName + "',GETDATE(),0,Ys_Name,Ks_Name,Jb_Name,NULL,NULL,Bc_Name, ");
            sb.Append(" NULL,NULL,NULL,'ZY'+Bx_BlCode,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,Bl.Ks_Code,Bl.Ys_Code,Bxlb_Name,0,'1' FROM Bl,Zd_YyYs,Zd_YyKs,Zd_YyBc,Zd_Bxlb");
            sb.Append(" WHERE Bl.Bc_Code=Zd_YyBc.Bc_Code AND  Bl.Ks_Code=Zd_YyKs.Ks_Code AND Bl.Ys_Code=Zd_YyYs.Ys_Code AND Bl.Bxlb_Code=Zd_Bxlb.Bxlb_Code ");
            sb.Append(" AND Bxlb_Name IN ('城镇职工', '城镇居民', '工伤医疗')   AND  NOT EXISTS (SELECT 1 FROM KC21 WHERE (AKC190='ZY'+Bx_BlCode OR AKC190='ZY'+Bl_Code)) ");
            arry.Add(sb.ToString());
            try
            {
                Common.WinFormVar.Var.DbHelper.ExecuteSqlTran(arry);
                result = "";
                return true;

            }
            catch (Exception e)
            {
                result = e.ToString();
                return false;
            }
        }

        /// <summary>
        /// 按照akc190 导入费用明细
        /// </summary>
        /// <param name="akc190"></param>
        /// <returns></returns>
        public bool ImportFymmByAKC190(string akc190,ref string result)
        {
            ArrayList arry = new ArrayList();
            StringBuilder sb;
            //如果一次住院，已经有费用明细上传 则部清除该次住院费用明细，如果没有费用明细上传则 清除 该次住院费用明细
            arry.Add("DELETE KC22 WHERE SUBSTRING(KC22.AKC190,1,2) = 'ZY' AND  NOT EXISTS(SELECT 1 FROM KC22 T WHERE T.CKC126 = 1 AND T.AKC190 = kc22.AKC190) AND EXISTS (SELECT 1 FROM KC21 WHERE KC21.AKC190 = KC22.AKC190 AND IsNewVersion = '1') AND AKC190 = '" + akc190 + "'");

            //导入费用明细 药品信息
            sb = new StringBuilder();
            sb.Append(" INSERT INTO KC22( AKC190 ,AKC220 ,AKC221 ,AKC515 ,AKC223 ,AKC224 ,AKC225 ,HisCount,AKC226 ,AKC227 ,AKA070 ,AKA071 ,AKA076 ,AKA072 ,AKA073 ,AKC229 ,ZKA100 ,CKC126 ,EditState ,BKF050,AKC008,BKF040,AKC025)");
            sb.Append(" SELECT  'ZY' + BL.Bx_BlCode ,'ZY' + BL_CF.CF_CODE ,CF_DATE ,Mx_Code ,Yp_Name ,");
            sb.Append(" CASE WHEN Dl_Name = '卫生材料' THEN '2'ELSE '1'END ,");
            sb.Append(" CF_DJ,SUM(CF_SL) ,SUM(CF_SL) ,SUM(BL_CFYP.CF_MONEY) ,");
            sb.Append(" Jx_Name ,NULL ,NULL ,NULL ,NULL ,NULL ,Mx_Gg ,0 ,0,Bl_Cf.Ys_Code,Ys_Name,Bl_Cf.Ks_Code,Ks_Name");
            sb.Append(" FROM BL_CFYP ,V_Yp ,BL_CF,BL,Zd_YyYs,Zd_YyKs ");
            sb.Append(" WHERE BL.BL_CODE = BL_CF.BL_CODE AND BL_CF.CF_CODE = BL_CFYP.CF_CODE AND SUBSTRING(BL_CFYP.XX_CODE, 1, 11) = V_Yp.Mx_Code AND BL_CF.CF_QR = '是' and Bl_Cf.Ys_Code =zd_YyYs.Ys_Code and  Bl_Cf.Ks_Code =zd_YyKs.Ks_Code ");
            sb.Append(" AND EXISTS ( SELECT 1 FROM KC21 WHERE  isJs = 0 AND IsNewVersion = '1' AND 'ZY' + BL.Bx_BlCode = AKC190 )");
            sb.Append(" AND NOT EXISTS ( SELECT 1 FROM KC22 WHERE  AKC190 = 'ZY' + BL.Bx_BlCode AND AKC220 = 'ZY' + BL_CF.CF_CODE AND V_Yp.Mx_Code = AKC515 )");
            sb.Append(" AND 'ZY' +BL.Bx_BlCode = '" + akc190 + "' ");
            sb.Append(" GROUP BY BL.Bx_BlCode ,BL_CF.CF_CODE ,CF_DATE ,Mx_Code ,Yp_Name ,Jx_Name ,Mx_Gg ,Dl_Name ,CF_DJ,Bl_Cf.Ys_Code,Ys_Name,Bl_Cf.Ks_Code,Ks_Name");
            arry.Add(sb.ToString());
            //导入诊疗非床位费
            sb = new StringBuilder();
            sb.Append(" INSERT  INTO KC22( AKC190 ,AKC220 ,AKC221 ,AKC515 ,AKC223 ,AKC224 ,AKC225 ,HisCount,AKC226 ,AKC227 ,AKA070 ,AKA071 ,AKA076 ,AKA072 ,AKA073 ,AKC229 ,ZKA100 ,CKC126 ,EditState ,BKF050,AKC008,BKF040,AKC025)");
            sb.Append(" SELECT  'ZY' + BL.Bx_BlCode ,'ZY' + BL_CF.CF_CODE ,CF_DATE ,BL_CFXM.XM_CODE ,XM_NAME ,'2' ,");
            //sb.Append(" SUM(BL_CFXM.CF_MONEY) / SUM(CF_SL) ,SUM(CF_SL) ,SUM(CF_SL) ,SUM(BL_CFXM.CF_MONEY) ,");
            sb.Append(" CF_DJ ,SUM(CF_SL) ,SUM(CF_SL) ,SUM(BL_CFXM.CF_MONEY) ,");
            sb.Append(" NULL ,NULL ,NULL ,NULL ,NULL ,NULL ,XM_DW ,0 ,0,Bl_Cf.Ys_Code,Ys_Name,Bl_Cf.Ks_Code,Ks_Name");
            sb.Append(" FROM    BL_CF,BL_CFXM ,ZD_ML_XM3,BL,Zd_YyYs,Zd_YyKs  ");
            sb.Append(" WHERE   BL.BL_CODE = BL_CF.BL_CODE AND BL_CF.CF_CODE = BL_CFXM.CF_CODE AND BL_CFXM.XM_CODE = ZD_ML_XM3.XM_CODE  AND CF_QR = '是'  AND XM_NAME NOT LIKE '%床位费%' and Bl_Cf.Ys_Code =zd_YyYs.Ys_Code and  Bl_Cf.Ks_Code =zd_YyKs.Ks_Code ");
            sb.Append(" AND EXISTS ( SELECT 1 FROM KC21 WHERE isJs = 0 AND IsNewVersion = '1' AND 'ZY' + BL.Bx_BlCode  = AKC190 )");
            sb.Append(" AND NOT EXISTS ( SELECT 1 FROM KC22 WHERE  AKC220 = 'ZY' + BL_CF.CF_CODE AND AKC190 = 'ZY' + BL.Bx_BlCode  AND BL_CFXM.XM_CODE = AKC515 )");
            sb.Append(" AND 'ZY' +BL.Bx_BlCode  = '" + akc190 + "' ");
            sb.Append(" GROUP BY BL.Bx_BlCode  ,BL_CF.CF_CODE ,CF_DATE ,BL_CFXM.XM_CODE ,XM_NAME ,XM_DW ,CF_DJ,Bl_Cf.Ys_Code,Ys_Name,Bl_Cf.Ks_Code,Ks_Name");
            arry.Add(sb.ToString());
            //--导入诊疗床位费
            sb = new StringBuilder();
            sb.Append(" INSERT  INTO KC22( AKC190 ,AKC220 ,AKC221 ,AKC515 ,AKC223 ,AKC224 ,AKC225 ,HisCount,AKC226 ,AKC227 ,AKA070 ,AKA071 ,AKA076 ,AKA072 ,AKA073 ,AKC229 ,ZKA100 ,CKC126 ,EditState ,BKF050,AKC008,BKF040,AKC025)");
            sb.Append(" SELECT  'ZY' + BL.Bx_BlCode  ,'ZY' + BL_CF.CF_CODE ,CF_DATE ,BL_CFXM.XM_CODE ,XM_NAME ,'3' ,");
            //sb.Append(" SUM(BL_CFXM.CF_MONEY) / SUM(CF_SL) ,SUM(CF_SL) ,SUM(CF_SL) ,SUM(BL_CFXM.CF_MONEY) ,");
            sb.Append(" CF_DJ ,SUM(CF_SL) ,SUM(CF_SL) ,SUM(BL_CFXM.CF_MONEY) ,");
            sb.Append(" NULL ,NULL ,NULL ,NULL ,NULL ,NULL ,XM_DW ,0 ,0,Bl_Cf.Ys_Code,Ys_Name,Bl_Cf.Ks_Code,Ks_Name");
            sb.Append(" FROM BL_CF,BL_CFXM ,ZD_ML_XM3,BL,Zd_YyYs,Zd_YyKs  ");
            sb.Append(" WHERE BL.BL_CODE = BL_CF.BL_CODE AND BL_CF.CF_CODE = BL_CFXM.CF_CODE AND BL_CFXM.XM_CODE = ZD_ML_XM3.XM_CODE  AND CF_QR = '是' AND XM_NAME LIKE '%床位费%' and Bl_Cf.Ys_Code =zd_YyYs.Ys_Code and  Bl_Cf.Ks_Code =zd_YyKs.Ks_Code ");
            sb.Append(" AND EXISTS ( SELECT 1 FROM   KC21 WHERE  isJs = 0 AND IsNewVersion = '1' AND 'ZY' + BL.Bx_BlCode = AKC190 )");
            sb.Append(" AND NOT EXISTS ( SELECT 1 FROM   KC22 WHERE  AKC220 = 'ZY' + BL_CF.CF_CODE AND AKC190 = 'ZY' + BL.Bx_BlCode AND BL_CFXM.XM_CODE = AKC515 )");
            sb.Append(" AND 'ZY' +BL.Bx_BlCode = '" + akc190 + "' ");
            sb.Append(" GROUP BY BL.Bx_BlCode ,BL_CF.CF_CODE ,CF_DATE ,BL_CFXM.XM_CODE ,XM_NAME ,XM_DW ,CF_DJ,Bl_Cf.Ys_Code,Ys_Name,Bl_Cf.Ks_Code,Ks_Name");
            arry.Add(sb.ToString());

            try
            {
                Common.WinFormVar.Var.DbHelper.ExecuteSqlTran(arry);

            }
            catch (Exception e)
            {
                result = e.ToString();
                return false;
            }

            DataTable minusDt = null;
            //负数费用明细
            try
            {
                string minusDataSQL = "SELECT AKC190 ,AKC220 ,AKC221 ,AKC515 ,AKC223 ,AKC224 ,AKC225 ,AKC226 ,AKC227 ,AKA070 ,AKA071 ,AKA076 ,AKA072 ,AKA073 ,AKC229 ,ZKA100 ,CKC126 ,EditState ,old_AKC221 ,IsYearEnd FROM KC22 WHERE SUBSTRING(KC22.AKC190,1,2) = 'ZY' AND IsYearEnd = '0' AND CKC126 = 0 AND EditState = '0' AND AKC226 <0 AND EXISTS(SELECT 1 FROM KC21 WHERE KC21.AKC190 = KC22.AKC190 AND IsNewVersion = '1' AND isJs = 0 ) AND AKC190 = '" + akc190 + "'";
                minusDt = Common.WinFormVar.Var.DbHelper.Query(minusDataSQL).Tables[0];
            }
            catch (Exception e)
            {
                result = e.ToString();
                return false;

            }

            //处理负数费用明细


            foreach (DataRow minusrow in minusDt.Rows)
            {
                DataTable mxDt = null;
                StringBuilder mxSQL = new StringBuilder();
                mxSQL.Append(" SELECT AKC190 ,AKC220 ,AKC221 ,AKC515 ,AKC223 ,AKC224 ,AKC225 ,AKC226 ,AKC227 ,AKA070 ,AKA071 ,AKA076 ,AKA072 ,AKA073 ,AKC229 ,ZKA100 ,CKC126 ,EditState ,old_AKC221 ,IsYearEnd ");
                mxSQL.Append(" FROM KC22 WHERE SUBSTRING(AKC190,1,2) = 'ZY' AND  IsYearEnd = '0' AND EditState <> 3 AND AKC226 > 0 ");
                mxSQL.Append(" AND EXISTS(SELECT 1 FROM KC21 WHERE KC21.AKC190 = KC22.AKC190 AND IsNewVersion = '1' AND isJs = 0)");
                mxSQL.Append(" AND AKC190 ='" + minusrow["AKC190"] + "' AND AKC515 = '" + minusrow["AKC515"] + "' AND AKC225 = " + minusrow["AKC225"] + " ");
                mxSQL.Append(" ORDER BY EditState asc,AKC221 desc");
                mxDt = Common.WinFormVar.Var.DbHelper.Query(mxSQL.ToString()).Tables[0];


                arry.Clear();
                foreach (DataRow mxrow in mxDt.Rows)
                {
                    if ((decimal)minusrow["AKC226"] >= 0)
                    {
                        break;
                    }

                    decimal sum = (decimal)mxrow["AKC226"] + (decimal)minusrow["AKC226"];

                    if (sum == 0)
                    {
                        if ((string)mxrow["EditState"] == "0")
                        {
                            arry.Add(" UPDATE KC22 SET AKC226 = 0,AKC227 = 0 , IsEnable = '0' WHERE AKC190 = '" + mxrow["AKC190"] + "' AND AKC220 = '" + mxrow["AKC220"] + "' AND AKC221 = '" + mxrow["AKC221"] + "' AND AKC515 = '" + mxrow["AKC515"] + "'");
                        }
                        if ((string)mxrow["EditState"] == "1")
                        {
                            arry.Add(" UPDATE KC22 SET AKC226 = 0 ,AKC227 = 0,EditState = '3' WHERE AKC190 = '" + mxrow["AKC190"] + "' AND AKC220 = '" + mxrow["AKC220"] + "' AND AKC221 = '" + mxrow["AKC221"] + "' AND AKC515 = '" + mxrow["AKC515"] + "'");
                        }


                        arry.Add(" UPDATE KC22 SET AKC226 = 0,AKC227 = 0 , IsEnable = '0' WHERE AKC190 = '" + minusrow["AKC190"] + "' AND AKC220 = '" + minusrow["AKC220"] + "' AND AKC221 = '" + minusrow["AKC221"] + "' AND AKC515 = '" + minusrow["AKC515"] + "'");
                        minusrow["AKC226"] = 0;

                    }
                    else if (sum < 0)
                    {
                        if ((string)mxrow["EditState"] == "0")
                        {
                            arry.Add(" UPDATE KC22 SET AKC226 = 0,AKC227 = 0 , IsEnable = '0' WHERE AKC190 = '" + mxrow["AKC190"] + "' AND AKC220 = '" + mxrow["AKC220"] + "' AND AKC221 = '" + mxrow["AKC221"] + "' AND AKC515 = '" + mxrow["AKC515"] + "'");
                        }
                        if ((string)mxrow["EditState"] == "1")
                        {
                            arry.Add(" UPDATE KC22 SET AKC226 = 0 ,AKC227 = 0,EditState = '3' WHERE AKC190 = '" + mxrow["AKC190"] + "' AND AKC220 = '" + mxrow["AKC220"] + "' AND AKC221 = '" + mxrow["AKC221"] + "' AND AKC515 = '" + mxrow["AKC515"] + "'");
                        }


                        arry.Add(" UPDATE KC22 SET AKC226 = " + sum + ",AKC227 = " + sum + " * AKC225 WHERE AKC190 = '" + minusrow["AKC190"] + "' AND AKC220 = '" + minusrow["AKC220"] + "' AND AKC221 = '" + minusrow["AKC221"] + "' AND AKC515 = '" + minusrow["AKC515"] + "'");
                        minusrow["AKC226"] = sum;
                    }
                    else //sum > 0
                    {
                        if ((string)mxrow["EditState"] == "0")
                        {
                            arry.Add(" UPDATE KC22 SET AKC226 = " + sum + ",AKC227 = " + sum + " * AKC225 WHERE AKC190 = '" + mxrow["AKC190"] + "' AND AKC220 = '" + mxrow["AKC220"] + "' AND AKC221 = '" + mxrow["AKC221"] + "' AND AKC515 = '" + mxrow["AKC515"] + "'");
                            arry.Add(" UPDATE KC22 SET AKC226 = 0,AKC227 = 0 , IsEnable = '0' WHERE AKC190 = '" + minusrow["AKC190"] + "' AND AKC220 = '" + minusrow["AKC220"] + "' AND AKC221 = '" + minusrow["AKC221"] + "' AND AKC515 = '" + minusrow["AKC515"] + "'");
                            minusrow["AKC226"] = 0;

                        }
                        if ((string)mxrow["EditState"] == "1")
                        {
                            arry.Add(" UPDATE KC22 SET AKC226 = 0 ,AKC227 = 0,EditState = '3' WHERE AKC190 = '" + mxrow["AKC190"] + "' AND AKC220 = '" + mxrow["AKC220"] + "' AND AKC221 = '" + mxrow["AKC221"] + "' AND AKC515 = '" + mxrow["AKC515"] + "'");
                            arry.Add(" UPDATE KC22 SET AKC226 = " + sum + ", AKC227 = " + sum + " * AKC225 WHERE AKC190 = '" + minusrow["AKC190"] + "' AND AKC220 = '" + minusrow["AKC220"] + "' AND AKC221 = '" + minusrow["AKC221"] + "' AND AKC515 = '" + minusrow["AKC515"] + "'");
                            minusrow["AKC226"] = sum;

                        }

                    }

                }

                try
                {
                    Common.WinFormVar.Var.DbHelper.ExecuteSqlTran(arry);
                }
                catch (Exception e)
                {
                    result = e.ToString();
                    return false;
                }


            }

            return true;
        }



        #region"注释部分"
        //private bool ZyData_Dr_New()
        //{
        //    ArrayList arry = new ArrayList();
        //    StringBuilder sb;
        //    string Cf_Code_New = DateTime.Now.ToString("yyMMddHHmmssff");
        //    string Cf_Date_New = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

        //    //1.删除kc22未上传明细
        //    arry.Add("DELETE KC22 WHERE CKC126 = 0 AND SUBSTRING(AKC190,1,2) = 'ZY'");
        //    //2.删除kc21未上传就诊记录
        //    arry.Add("DELETE KC21 WHERE CKC126 = 0 AND SUBSTRING(AKC190,1,2) = 'ZY' AND NOT EXISTS(SELECT 1 FROM KC22 WHERE KC22.AKC190 =KC21.AKC190 )");
        //    //3.将His中未上传就诊信息 导入 kc21
        //    sb = new StringBuilder();
        //    sb.Append("INSERT INTO KC21(");
        //    sb.Append("AKC190,AKA130,AKC192,AKC193,AKC194,AKC195,AKC196,AAE011,AAE036,CKC126,ZKC271,ZKC272,ZKC274,ZKC275,CKA040,CKA041,");
        //    sb.Append("ZHUZHI,ZHIYE,PHONE,BLH,AMC026,AMC100,AMC001,AMC013,AMC008,AAE050,AAE013,AKC120,");
        //    sb.Append("Ry_Bxlb,isJs)");
        //    sb.Append("SELECT 'ZY'+Bl_Code,'',Ry_RyDate,Jb_Code,Ry_CyDate,NULL,NULL,'" + HisVar.HisVar.JsrName + "',GETDATE(),0,Ys_Name,Ks_Name,Jb_Name,NULL,NULL,Bc_Name, ");
        //    sb.Append("NULL,NULL,NULL,'ZY'+Bl_Code,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,Bxlb_Name,0 FROM Bl,Zd_YyYs,Zd_YyKs,Zd_YyBc,Zd_Bxlb  ");
        //    sb.Append("WHERE Bl.Bc_Code=Zd_YyBc.Bc_Code AND  Bl.Ks_Code=Zd_YyKs.Ks_Code AND Bl.Ys_Code=Zd_YyYs.Ys_Code AND Bl.Bxlb_Code=Zd_Bxlb.Bxlb_Code   ");
        //    sb.Append("AND Bxlb_Name IN ('城镇职工', '城镇居民', '工伤医疗')   AND  NOT EXISTS (SELECT 1 FROM KC21 WHERE AKC190='ZY'+Bl_Code)   ");
        //    arry.Add(sb.ToString());

        //    //7 统计His中已经上传明细信息 A ,统计kc22中已经上传明细信息 B, 将A与B进行比较 得到发生改变结果 Result
        //    sb = new StringBuilder();
        //    sb.Append(" SELECT  * ,( ISNULL(A.AKC226,0) - ISNULL(B.Cf_Sl,0)) SlChaE ,( ISNULL(A.AKC227,0) - ISNULL(B.Cf_Money,0)) MoneyChaE FROM  ");
        //    sb.Append(" ( ");
        //    sb.Append(" SELECT 'ZY' + Bl_Code AS Bl_Code ,SUBSTRING(Xx_Code, 1, 11) Mx_Code ,CASE WHEN Dl_Name = '卫生材料' THEN '2'ELSE '1'END AS Lb ,SUM(Cf_Sl) Cf_Sl ,SUM(Bl_Cfyp.Cf_Money) Cf_Money ");
        //    sb.Append(" FROM Bl_Cf ,Bl_Cfyp,V_Yp ");
        //    sb.Append(" WHERE Bl_Cf.Cf_Code = Bl_Cfyp.Cf_Code AND Cf_Qr = '是' AND Mx_Code = SUBSTRING(Xx_Code, 1, 11)  ");
        //    sb.Append(" AND EXISTS ( SELECT 1 FROM KC21 WHERE  AKC190 = 'ZY' + Bl_Code )");
        //    sb.Append(" GROUP BY  Bl_Code ,SUBSTRING(Xx_Code, 1, 11),Dl_Name ");
        //    sb.Append(" UNION ALL ");
        //    sb.Append(" SELECT 'ZY' + Bl_Code ,Xm_Code ,'2' AS Lb ,SUM(Cf_Sl) Cf_Sl ,SUM(Bl_Cfxm.Cf_Money) Cf_Money ");
        //    sb.Append(" FROM Bl_Cf ,Bl_Cfxm ");
        //    sb.Append(" WHERE Bl_Cf.Cf_Code = Bl_Cfxm.Cf_Code AND Cf_Qr = '是' ");
        //    sb.Append(" AND EXISTS ( SELECT 1 FROM KC21 WHERE AKC190 = 'ZY' + Bl_Code ) ");
        //    sb.Append(" GROUP BY  Bl_Code ,Xm_Code ");
        //    sb.Append(" ) B FULL join ");
        //    sb.Append(" ( SELECT AKC190 ,AKC515 ,AKC224 ,SUM(AKC226) AKC226 ,SUM(AKC227) AKC227 FROM KC22 WHERE EditState =1 GROUP BY AKC190 ,AKC515,AKC224) A ");
        //    sb.Append(" ON	a.AKC190 = B.Bl_Code AND B.Mx_Code = a.AKC515 ");
        //    sb.Append(" WHERE  ISNULL(A.AKC226, 0) <> ISNULL(B.Cf_Sl,0) ");

        //    DataSet ds = new DataSet();
        //    ds = Common.WinFormVar.Var.DbHelper.Query(sb.ToString());

        //    foreach (DataRow _row in ds.Tables[0].Rows)
        //    {
        //        float SlChaE = float.Parse(_row["SlChaE"].ToString());
        //        float MoneyChaE = float.Parse(_row["MoneyChaE"].ToString());

        //        string temp_AKC190 = "";
        //        string temp_AKC515 = "";
        //        string temp_AKC224 = "";

        //        if (_row["Bl_Code"] == DBNull.Value)
        //        {
        //            temp_AKC190 = (string)_row["AKC190"];
        //        }
        //        else
        //        {
        //            temp_AKC190 = (string)_row["Bl_Code"];
        //        }

        //        if (_row["Mx_Code"] == DBNull.Value)
        //        {
        //            temp_AKC515 = (string)_row["AKC515"];
        //        }
        //        else
        //        {
        //            temp_AKC515 = (string)_row["Mx_Code"];
        //        }

        //        if (_row["AKC224"] == DBNull.Value)
        //        {
        //            temp_AKC224 = (string)_row["AKC224"];
        //        }
        //        else
        //        {
        //            temp_AKC224 = (string)_row["Lb"];
        //        }


        //        DataSet mxds = new DataSet();
        //        mxds = Common.WinFormVar.Var.DbHelper.Query("SELECT * FROM KC22 WHERE AKC190='" + temp_AKC190 + "' AND AKC515='" + temp_AKC515 + "' and editstate = 1");

        //        if (mxds.Tables[0].Rows.Count == 0)
        //        {
        //            sb = new StringBuilder();
        //            sb.Append(" INSERT  INTO KC22( AKC190 ,AKC220 ,AKC221 ,AKC515 ,AKC223 ,AKC224 ,AKC225 ,AKC226 ,AKC227 ,AKA070 ,AKA071 ,AKA076 ,AKA072 ,AKA073 ,AKC229 ,ZKA100 ,CKC126 ,EditState) ");
        //            sb.Append(" SELECT TOP 1 AKC190 ,'" + Cf_Code_New + "' ,'" + Cf_Date_New + "' ,AKC515 ,AKC223 ,AKC224 ," + (MoneyChaE / SlChaE) + " ," + (-SlChaE) + " ," + (-MoneyChaE) + " ,AKA070 ,NULL ,NULL ,NULL ,NULL ,NULL ,ZKA100 ,0 ,0");
        //            sb.Append(" FROM KC22 WHERE AKC190 = '" + temp_AKC190 + "'AND AKC515 = '" + temp_AKC515 + "' ");
        //            arry.Add(sb.ToString());
        //            continue;
        //        }
        //        else
        //        {
        //            foreach (DataRow mxrow in mxds.Tables[0].Rows)
        //            {
        //                if (SlChaE == 0)
        //                {
        //                    break;
        //                }
        //                if (float.Parse(mxrow["AKC226"].ToString()) == SlChaE)
        //                {
        //                    arry.Add("UPDATE KC22 SET EditState = 3 WHERE AKC190 = '" + mxrow["AKC190"] + "'AND AKC220 = '" + mxrow["AKC220"] + "'AND AKC221 = '" + mxrow["AKC221"] + "' AND AKC515 = '" + mxrow["AKC515"] + "'");
        //                    SlChaE = 0;
        //                }
        //                else if (float.Parse(mxrow["AKC226"].ToString()) > SlChaE)
        //                {

        //                    arry.Add("UPDATE KC22 SET EditState = 3 WHERE AKC190 = '" + mxrow["AKC190"] + "'AND AKC220 = '" + mxrow["AKC220"] + "'AND AKC221 = '" + mxrow["AKC221"] + "' AND AKC515 = '" + mxrow["AKC515"] + "'");
        //                    sb = new StringBuilder();
        //                    sb.Append("INSERT INTO KC22( AKC190 ,AKC220 ,AKC221 ,AKC515 ,AKC223 ,AKC224 ,AKC225 ,AKC226 ,AKC227 ,AKA070 ,AKA071 ,AKA076 ,AKA072 ,AKA073 ,AKC229 ,ZKA100 ,CKC126 ,EditState )");
        //                    sb.Append(" SELECT AKC190 ," + Cf_Code_New + " ,'" + Cf_Date_New + "' ,AKC515 ,AKC223 ,AKC224 ,AKC225 ," + mxrow["AKC226"] + " - " + SlChaE + " ," + mxrow["AKC227"] + " - " + MoneyChaE + " ,AKA070 ,AKA071 ,AKA076 ,AKA072 ,AKA073 ,AKC229 ,ZKA100 ,0 ,0 ");
        //                    sb.Append(" FROM KC22 WHERE AKC190='" + mxrow["AKC190"] + "' and AKC220='" + mxrow["AKC220"] + "' and AKC515='" + mxrow["AKC515"] + "' AND AKC221 = '" + mxrow["AKC221"] + "'");
        //                    arry.Add(sb.ToString());

        //                    SlChaE = 0;
        //                }
        //                else
        //                {

        //                    arry.Add("update KC22 set editstate=3  WHERE AKC190='" + mxrow["AKC190"] + "' and AKC220='" + mxrow["AKC220"] + "' and AKC515='" + mxrow["AKC515"] + "' AND AKC221 = '" + mxrow["AKC221"] + "'");

        //                    SlChaE = SlChaE - float.Parse(mxrow["AKC226"].ToString());
        //                    MoneyChaE = MoneyChaE - float.Parse(mxrow["AKC227"].ToString());

        //                }
        //            }

        //        }

        //    }
        //    return false;
        //}

        /// <summary>
        /// 费用明细修改 带明细xml，即 医保 将明细xml 在费用明细修改是，先删除原来明细，在上传新明细
        /// 例如 10 个阿莫西林  退掉4个， 可以生成6个的明细，调用接口是，将6个明细作为参数，医保在掉用接口是 会先把10个阿莫西林删除 在将6个阿莫西林上传
        /// </summary>
        /// <returns></returns>

        //private bool ZyDataDr()
        //{
        //    ArrayList arry = new ArrayList();
        //    //删除未结算未上传数据
        //    arry.Add("delete KC22 where CKC126=0 and EditState=0 and substring(AKC190,1,2)='ZY'");
        //    arry.Add("delete kc21 where isjs=0 and CKC126=0 and substring(AKC190,1,2)='ZY' and not EXISTS (select 1 from KC22 where KC22.akc190=KC21.akc190)");
        //    //导入住院患者信息
        //    StringBuilder strSql = new StringBuilder();
        //    strSql.Append("INSERT INTO KC21   ");
        //    strSql.Append("(AKC190,AKA130,AKC192,AKC193,AKC194,AKC195,AKC196,AAE011,AAE036,CKC126,ZKC271,ZKC272,ZKC274,ZKC275,CKA040,CKA041, ");
        //    strSql.Append("ZHUZHI,ZHIYE,PHONE,BLH,AMC026,AMC100,AMC001,AMC013,AMC008,AAE050,AAE013,AKC120,Ry_Bxlb,isJs)   ");
        //    strSql.Append("SELECT   'ZY'+Bl_Code,'',Ry_RyDate,Jb_Code,Ry_CyDate,NULL,NULL,'" + HisVar.HisVar.JsrName + "',GETDATE(),0,Ys_Name,Ks_Name,Jb_Name,NULL,NULL,Bc_Name, ");
        //    strSql.Append("NULL,NULL,NULL,'ZY'+Bl_Code,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,Bxlb_Name,0 FROM Bl,Zd_YyYs,Zd_YyKs,Zd_YyBc,Zd_Bxlb  ");
        //    strSql.Append("WHERE Bl.Bc_Code=Zd_YyBc.Bc_Code AND  Bl.Ks_Code=Zd_YyKs.Ks_Code AND Bl.Ys_Code=Zd_YyYs.Ys_Code AND Bl.Bxlb_Code=Zd_Bxlb.Bxlb_Code   ");
        //    strSql.Append("AND Bxlb_Name IN ('城镇职工', '城镇居民', '工伤医疗')   AND  NOT EXISTS (SELECT 1 FROM dbo.KC21 WHERE AKC190='ZY'+Bl_Code)   ");
        //    arry.Add(strSql.ToString());
        //    //导入药品信息（第一次）
        //    strSql = new StringBuilder();
        //    strSql.Append("INSERT INTO KC22   ");
        //    strSql.Append("(AKC190,AKC220,AKC221,AKC515,AKC223,AKC224,AKC225,AKC226,AKC227,AKA070,AKA071,AKA076,AKA072,AKA073,AKC229,ZKA100,CKC126,EditState)   ");
        //    //strSql.Append("SELECT 'ZY'+Bl_Cf.Bl_Code,'ZY'+Bl_Cf.Cf_Code,cf_Date,Mx_Code,Yp_Name,'1',SUM(Bl_Cfyp.Cf_Money)/SUM(Cf_Sl),SUM(Cf_Sl), SUM(Bl_Cfyp.Cf_Money), ");
        //    strSql.Append("SELECT 'ZY'+Bl_Cf.Bl_Code,'ZY'+Bl_Cf.Cf_Code,cf_Date,Mx_Code,Yp_Name,CASE WHEN Dl_Name = '卫生材料' THEN  '2' ELSE '1' END,SUM(Bl_Cfyp.Cf_Money)/SUM(Cf_Sl),SUM(Cf_Sl), SUM(Bl_Cfyp.Cf_Money), ");
        //    strSql.Append("Jx_Name,NULL,NULL,NULL,NULL,NULL,Mx_Gg,0,0  FROM Bl_Cfyp,V_Yp,Bl_Cf  WHERE Mx_Code=SUBSTRING(Xx_Code,1,11) AND   ");
        //    strSql.Append("Bl_Cf.Cf_Code=Bl_Cfyp.Cf_Code  ");
        //    strSql.Append("AND EXISTS (SELECT 1 FROM KC21 WHERE isjs=0  AND 'ZY'+Bl_Cf.Bl_Code=AKC190) AND Cf_Qr='是'    ");
        //    strSql.Append("AND NOT EXISTS (SELECT 1 FROM KC22 WHERE AKC220='ZY'+Bl_Cf.Cf_Code and AKC190='ZY'+Bl_Cf.Bl_Code AND Mx_Code=AKC515)   ");
        //    //strSql.Append("GROUP BY  Bl_Cf.Bl_Code,Bl_Cf.Cf_Code,Cf_Date,Mx_Code,Yp_Name,Jx_Name,Mx_Gg HAVING SUM(Cf_Sl)>0   ");
        //    strSql.Append("GROUP BY  Bl_Cf.Bl_Code,Bl_Cf.Cf_Code,Cf_Date,Mx_Code,Yp_Name,Jx_Name,Mx_Gg,Dl_Name HAVING SUM(Cf_Sl)>0   ");
        //    arry.Add(strSql.ToString());
        //    ////原来的数据修改
        //    //strSql = new StringBuilder();
        //    //strSql.Append("UPDATE KC22 SET EditState=2,AKC225=a.Cf_Dj,AKC226=a.Cf_Sl,AKC227=a.Cf_Money  ");
        //    //strSql.Append("FROM ( SELECT Bl_Cf.Cf_Code,SUBSTRING(Xx_Code,1,11)Mx_Code,CONVERT(numeric(18,4),SUM(Bl_Cfyp.Cf_Money)/SUM(Cf_Sl)) Cf_Dj, SUM(Cf_Sl)Cf_Sl,SUM(Bl_Cfyp.Cf_Money)Cf_Money  ");
        //    //strSql.Append("FROM Bl_Cfyp,Bl_Cf WHERE EXISTS (SELECT 1 FROM KC22 WHERE 'ZY'+Bl_Cf.Cf_Code=AKC220 AND SUBSTRING(Xx_Code,1,11)=AKC515)    ");
        //    //strSql.Append("AND Bl_Cfyp.Cf_Code=Bl_Cf.Cf_Code GROUP BY Bl_Cf.Cf_Code,Cf_Date,SUBSTRING(Xx_Code,1,11) HAVING SUM(Cf_Sl)>0)a  ");
        //    //strSql.Append("WHERE 'ZY'+a.Cf_Code=AKC220 AND a.Mx_Code=AKC515  AND (AKC225<>Cf_Dj OR a.Cf_Sl<>AKC226 OR AKC227<>a.Cf_Money) AND akc224=1  and EditState=1  ");
        //    //arry.Add(strSql.ToString());

        //    //处理处方已经删除的数据
        //    strSql = new StringBuilder();
        //    strSql.Append("UPDATE KC22 SET EditState=3  ");
        //    strSql.Append("WHERE  NOT EXISTS (SELECT 1 FROM Bl_Cfyp,Bl_Cf WHERE Bl_Cf.Cf_Code=Bl_Cfyp.Cf_Code AND  'ZY'+Bl_Cf.Cf_Code=AKC220 and 'ZY'+Bl_Cf.Bl_Code=AKC190  AND SUBSTRING(Xx_Code,1,11)=AKC515)   ");
        //    strSql.Append("AND substring(AKC190,1,2)='ZY' AND akc224=1   ");
        //    arry.Add(strSql.ToString());

        //    //导入诊疗非床位费（第一次）
        //    strSql = new StringBuilder();
        //    strSql.Append("INSERT INTO KC22  (AKC190,AKC220,AKC221,AKC515,AKC223,AKC224,AKC225,AKC226,AKC227,AKA070,AKA071,AKA076,AKA072,AKA073,AKC229,ZKA100,CKC126,EditState)  ");
        //    strSql.Append("SELECT 'ZY'+Bl_Cf.Bl_Code,'ZY'+Bl_Cf.Cf_Code,cf_Date,Bl_Cfxm.Xm_Code,Xm_Name,'2',SUM(Bl_Cfxm.Cf_Money)/SUM(Cf_Sl),SUM(Cf_Sl),  SUM(Bl_Cfxm.Cf_Money), ");
        //    strSql.Append("NULL,NULL,NULL,NULL,NULL,NULL,Xm_Dw,0,0  FROM dbo.Bl_Cfxm,dbo.Zd_Ml_Xm3,dbo.Bl_Cf  WHERE Bl_Cfxm.Xm_Code=Zd_Ml_Xm3.Xm_Code AND  Bl_Cf.Cf_Code=Bl_Cfxm.Cf_Code  ");
        //    strSql.Append("AND EXISTS (SELECT 1 FROM dbo.KC21 WHERE isjs=0 AND 'ZY'+Bl_Cf.Bl_Code=AKC190) AND Cf_Qr='是'    ");
        //    strSql.Append("AND NOT EXISTS (SELECT 1 FROM dbo.KC22 WHERE AKC220='ZY'+Bl_Cf.Cf_Code and AKC190='ZY'+Bl_Cf.Bl_Code AND Bl_Cfxm.Xm_Code=AKC515)  ");
        //    strSql.Append("AND xm_name not like '%床位费%'   ");
        //    strSql.Append("GROUP BY  Bl_Cf.Bl_Code,Bl_Cf.Cf_Code,Cf_Date,Bl_Cfxm.Xm_Code,Xm_Name,Xm_Dw   HAVING SUM(Cf_Sl)>0   ");
        //    arry.Add(strSql.ToString());

        //    //导入诊疗床位费（第一次）
        //    strSql = new StringBuilder();
        //    strSql.Append("INSERT INTO KC22  (AKC190,AKC220,AKC221,AKC515,AKC223,AKC224,AKC225,AKC226,AKC227,AKA070,AKA071,AKA076,AKA072,AKA073,AKC229,ZKA100,CKC126,EditState)  ");
        //    strSql.Append("SELECT 'ZY'+Bl_Cf.Bl_Code,'ZY'+Bl_Cf.Cf_Code,cf_Date,Bl_Cfxm.Xm_Code,Xm_Name,'2',SUM(Bl_Cfxm.Cf_Money)/SUM(Cf_Sl),SUM(Cf_Sl),  SUM(Bl_Cfxm.Cf_Money), ");
        //    strSql.Append("NULL,NULL,NULL,NULL,NULL,NULL,Xm_Dw,0,0  FROM dbo.Bl_Cfxm,dbo.Zd_Ml_Xm3,dbo.Bl_Cf  WHERE Bl_Cfxm.Xm_Code=Zd_Ml_Xm3.Xm_Code AND  Bl_Cf.Cf_Code=Bl_Cfxm.Cf_Code  ");
        //    strSql.Append("AND EXISTS (SELECT 1 FROM dbo.KC21 WHERE isjs=0 AND 'ZY'+Bl_Cf.Bl_Code=AKC190) AND Cf_Qr='是'    ");
        //    strSql.Append("AND NOT EXISTS (SELECT 1 FROM dbo.KC22 WHERE AKC220='ZY'+Bl_Cf.Cf_Code and AKC190='ZY'+Bl_Cf.Bl_Code AND Bl_Cfxm.Xm_Code=AKC515)  ");
        //    strSql.Append("AND xm_name like '%床位费%'   ");
        //    strSql.Append("GROUP BY  Bl_Cf.Bl_Code,Bl_Cf.Cf_Code,Cf_Date,Bl_Cfxm.Xm_Code,Xm_Name,Xm_Dw   HAVING SUM(Cf_Sl)>0   ");
        //    arry.Add(strSql.ToString());

        //    //strSql = new StringBuilder();
        //    //strSql.Append("UPDATE KC22 SET EditState=2,AKC225=a.Cf_Dj,AKC226=a.Cf_Sl,AKC227=a.Cf_Money  ");
        //    //strSql.Append("FROM ( SELECT Bl_Cf.Cf_Code,Xm_Code AS Mx_Code,CONVERT(numeric(18,4),SUM(Bl_Cfxm.Cf_Money)/SUM(Cf_Sl)) Cf_Dj, SUM(Cf_Sl)Cf_Sl,SUM(Bl_Cfxm.Cf_Money)Cf_Money  ");
        //    //strSql.Append("FROM Bl_Cfxm,Bl_Cf WHERE EXISTS (SELECT 1 FROM KC22 WHERE 'ZY'+Bl_Cf.Cf_Code=AKC220 AND Xm_Code=AKC515)    ");
        //    //strSql.Append("AND Bl_Cfxm.Cf_Code=Bl_Cf.Cf_Code GROUP BY Bl_Cf.Cf_Code,Xm_Code HAVING SUM(Cf_Sl)>0)a  ");
        //    //strSql.Append("WHERE 'ZY'+a.Cf_Code=AKC220 AND a.Mx_Code=AKC515  AND (AKC225<>Cf_Dj OR a.Cf_Sl<>AKC226 OR AKC227<>a.Cf_Money) AND (akc224=2 OR akc224=3)  and EditState=1 ");
        //    //arry.Add(strSql.ToString());

        //    //诊疗被删除的
        //    strSql = new StringBuilder();
        //    strSql.Append("UPDATE KC22 SET EditState=3  ");
        //    strSql.Append("WHERE  NOT EXISTS (SELECT 1 FROM Bl_Cfxm,Bl_Cf WHERE Bl_Cf.Cf_Code=Bl_Cfxm.Cf_Code AND  'ZY'+Bl_Cf.Cf_Code=AKC220 and 'ZY'+Bl_Cf.Bl_Code=AKC190  AND Xm_Code=AKC515)   ");
        //    strSql.Append("AND substring(AKC190,1,2)='ZY' AND (akc224=2 OR akc224=3)  ");
        //    strSql.Append("AND NOT EXISTS (SELECT 1 FROM Bl_Cfyp,Bl_Cf WHERE Bl_Cf.Cf_Code=Bl_Cfyp.Cf_Code AND  'ZY'+Bl_Cf.Cf_Code=AKC220 and 'ZY'+Bl_Cf.Bl_Code=AKC190  AND SUBSTRING(Xx_Code,1,11)=AKC515) ");
        //    arry.Add(strSql.ToString());

        //    try
        //    {
        //        Common.WinFormVar.Var.DbHelper.ExecuteSqlTran(arry);
        //    }
        //    catch (Exception e)
        //    {
        //        return false;
        //    }
        //    arry.Clear();
        //    //处理负数 获取医保和HIS有差别的数据
        //    strSql = new StringBuilder();
        //    strSql.Append("SELECT *,(a.AKC226-b.Cf_Sl)SlChaE,(a.AKC227-b.Cf_Money)MoneyChaE FROM  ");
        //    strSql.Append("(SELECT AKC190,akc515,SUM(AKC226)AKC226,SUM(AKC227) AKC227 FROM dbo.KC22 where editstate<>3 GROUP BY AKC190,akc515)a, ");
        //    strSql.Append("( ");
        //    strSql.Append("SELECT 'ZY'+Bl_Code AS Bl_Code,SUBSTRING(Xx_Code,1,11)Mx_Code,SUM(Cf_Sl)Cf_Sl,SUM(Bl_Cfyp.Cf_Money)Cf_Money FROM Bl_Cf,Bl_Cfyp WHERE Bl_Cf.Cf_Code=Bl_Cfyp.Cf_Code AND Cf_Qr='是' ");
        //    strSql.Append("AND EXISTS (SELECT 1 FROM KC21 WHERE AKC190='ZY'+Bl_Code)  ");
        //    strSql.Append("GROUP BY Bl_Code,SUBSTRING(Xx_Code,1,11) ");
        //    strSql.Append("UNION ALL	 ");
        //    strSql.Append("SELECT 'ZY'+Bl_Code ,Xm_Code,SUM(Cf_Sl)Cf_Sl,SUM(Bl_Cfxm.Cf_Money)Cf_Money FROM Bl_Cf,Bl_Cfxm WHERE Bl_Cf.Cf_Code=Bl_Cfxm.Cf_Code AND Cf_Qr='是' ");
        //    strSql.Append("AND EXISTS (SELECT 1 FROM KC21 WHERE AKC190='ZY'+Bl_Code)  ");
        //    strSql.Append("GROUP BY Bl_Code,Xm_Code ");
        //    strSql.Append(")b WHERE a.AKC190=Bl_Code AND b.Mx_Code=a.AKC515 AND (a.AKC226<>b.Cf_Sl  OR a.AKC227<>b.Cf_Money) ");
        //    DataSet ds = new DataSet();
        //    ds = Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
        //    foreach (DataRow _row in ds.Tables[0].Rows)
        //    {
        //        float SlChaE = float.Parse(_row["SlChaE"].ToString());
        //        float MoneyChaE = float.Parse(_row["MoneyChaE"].ToString());
        //        DataSet mxds = new DataSet();
        //        mxds = Common.WinFormVar.Var.DbHelper.Query("SELECT * FROM KC22 WHERE AKC190='" + _row["AKC190"] + "' AND AKC515='" + _row["AKC515"] + "' and editstate<>3");
        //        foreach (DataRow mxrow in mxds.Tables[0].Rows)
        //        {
        //            if (SlChaE == 0 && MoneyChaE==0)
        //            {
        //                break;
        //            }

        //            if (float.Parse(mxrow["AKC226"].ToString()) >= SlChaE && float.Parse(mxrow["AKC227"].ToString()) >= MoneyChaE)
        //            {
        //                if (mxrow["EditState"].ToString() == "1")
        //                {
        //                    arry.Add("update KC22 set editstate=2,AKC226=AKC226-(" + SlChaE + "),AKC227=AKC227-(" + MoneyChaE + "),akc221=CONVERT(VARCHAR(10),akc221,126)+' '+SUBSTRING(CONVERT(VARCHAR(19),GETDATE(),126),12,8),old_AKC221=akc221,CKC126=0 where AKC190='" + _row["AKC190"]+"' and  AKC220='" + mxrow["AKC220"] + "' and AKC515='" + mxrow["AKC515"] + "'");
        //                }
        //                if (mxrow["EditState"].ToString() == "0")
        //                {
        //                    arry.Add("update KC22 set AKC226=AKC226-(" + SlChaE + "),AKC227=AKC227-(" + MoneyChaE + ") where AKC190='" + _row["AKC190"] + "' and AKC220='" + mxrow["AKC220"] + "' and AKC515='" + mxrow["AKC515"] + "'");
        //                }
        //                arry.Add("update KC22 set AKC225=AKC227/AKC226  where AKC190='" + _row["AKC190"] + "' and AKC220='" + mxrow["AKC220"] + "' and AKC515='" + mxrow["AKC515"] + "' and akc226<>0");
        //                SlChaE = 0;
        //                MoneyChaE = 0;
        //            }
        //            else
        //            {
        //                if (mxrow["EditState"].ToString() == "1")
        //                {
        //                    arry.Add("update KC22 set editstate=3,AKC226=0,AKC227=0 where AKC190='" + _row["AKC190"] + "' and AKC220='" + mxrow["AKC220"] + "' and AKC515='" + mxrow["AKC515"] + "'");
        //                }
        //                if (mxrow["EditState"].ToString() == "0")
        //                {
        //                    arry.Add("update KC22 set AKC226=0,AKC227=0 where AKC190='" + _row["AKC190"] + "' and AKC220='" + mxrow["AKC220"] + "' and AKC515='" + mxrow["AKC515"] + "'");
        //                }
        //                SlChaE = SlChaE - float.Parse(mxrow["AKC226"].ToString());
        //                MoneyChaE = MoneyChaE - float.Parse(mxrow["AKC227"].ToString());
        //            }
        //        }
        //    }
        //    try
        //    {
        //        Common.WinFormVar.Var.DbHelper.ExecuteSqlTran(arry);
        //        return true;
        //    }
        //    catch (Exception e)
        //    {
        //        return false;
        //    }
        //}

        /// <summary>
        /// 费用明细 只有删除功能，没有上传功能
        /// 每次更新数据 对未上传的都重新生成处方号，处方日期
        /// </summary>
        /// <returns></returns>
        //private bool ZyData_Dr1()
        //{
        //    ArrayList arry = new ArrayList();
        //    StringBuilder sb;
        //    string Cf_Code_New = DateTime.Now.ToString("yyMMddHHmmssff");
        //    string Cf_Date_New = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

        //    //1.删除kc22未上传明细
        //    arry.Add("DELETE KC22 WHERE CKC126 = 0 AND SUBSTRING(AKC190,1,2) = 'ZY'");
        //    //2.删除kc21未上传就诊记录
        //    arry.Add("DELETE KC21 WHERE CKC126 = 0 AND SUBSTRING(AKC190,1,2) = 'ZY' AND NOT EXISTS(SELECT 1 FROM KC22 WHERE KC22.AKC190 =KC21.AKC190 )");
        //    //3.将His中未上传就诊信息 导入 kc21
        //    sb = new StringBuilder();
        //    sb.Append("INSERT INTO KC21(");
        //    sb.Append("AKC190,AKA130,AKC192,AKC193,AKC194,AKC195,AKC196,AAE011,AAE036,CKC126,ZKC271,ZKC272,ZKC274,ZKC275,CKA040,CKA041,");
        //    sb.Append("ZHUZHI,ZHIYE,PHONE,BLH,AMC026,AMC100,AMC001,AMC013,AMC008,AAE050,AAE013,AKC120,");
        //    sb.Append("Ry_Bxlb,isJs)");
        //    sb.Append("SELECT 'ZY'+Bl_Code,'',Ry_RyDate,Jb_Code,Ry_CyDate,NULL,NULL,'" + HisVar.HisVar.JsrName + "',GETDATE(),0,Ys_Name,Ks_Name,Jb_Name,NULL,NULL,Bc_Name, ");
        //    sb.Append("NULL,NULL,NULL,'ZY'+Bl_Code,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,Bxlb_Name,0 FROM Bl,Zd_YyYs,Zd_YyKs,Zd_YyBc,Zd_Bxlb  ");
        //    sb.Append("WHERE Bl.Bc_Code=Zd_YyBc.Bc_Code AND  Bl.Ks_Code=Zd_YyKs.Ks_Code AND Bl.Ys_Code=Zd_YyYs.Ys_Code AND Bl.Bxlb_Code=Zd_Bxlb.Bxlb_Code   ");
        //    sb.Append("AND Bxlb_Name IN ('城镇职工', '城镇居民', '工伤医疗')   AND  NOT EXISTS (SELECT 1 FROM KC21 WHERE AKC190='ZY'+Bl_Code)   ");
        //    arry.Add(sb.ToString());
        //    //4.将His中未上传药品信息 导入kc22
        //    sb = new StringBuilder();
        //    sb.Append(" INSERT  INTO KC22( AKC190 ,AKC220 ,AKC221 ,AKC515 ,AKC223 ,AKC224 ,AKC225 ,AKC226 ,AKC227 ,AKA070 ,AKA071 ,AKA076 ,AKA072 ,AKA073 ,AKC229 ,ZKA100 ,CKC126 ,EditState) ");
        //    sb.Append(" SELECT  'ZY' + Bl_Cf.Bl_Code ,'" + Cf_Code_New + "','" + Cf_Date_New + "',Mx_Code ,Yp_Name ,CASE WHEN Dl_Name = '卫生材料' THEN '2' ELSE '1'END ,SUM(Bl_Cfyp.Cf_Money) / SUM(Cf_Sl) ,SUM(Cf_Sl) ,SUM(Bl_Cfyp.Cf_Money) ,Jx_Name ,NULL ,NULL ,NULL ,NULL ,NULL ,Mx_Gg ,0 ,0 ");
        //    sb.Append(" FROM Bl_Cfyp ,V_Yp ,Bl_Cf ");
        //    sb.Append(" WHERE Mx_Code = SUBSTRING(Xx_Code, 1, 11)AND Bl_Cf.Cf_Code = Bl_Cfyp.Cf_Code AND Cf_Qr = '是' ");
        //    sb.Append(" AND EXISTS ( SELECT 1 FROM   KC21 WHERE  isJs = 0 AND 'ZY' + Bl_Cf.Bl_Code = AKC190 ) ");
        //    sb.Append(" AND NOT EXISTS ( SELECT 1 FROM KC22 WHERE AKC190 = 'ZY' + Bl_Cf.Bl_Code AND Mx_Code = AKC515 ) ");
        //    sb.Append(" GROUP BY Bl_Cf.Bl_Code ,Mx_Code ,Yp_Name,Dl_Name,Jx_Name,Mx_Gg HAVING SUM(Cf_Sl)<> 0 ");
        //    arry.Add(sb.ToString());
        //    //5.将His中未上传诊疗信息(非床位费)导入kc22
        //    sb = new StringBuilder();
        //    sb.Append(" INSERT INTO KC22( AKC190 ,AKC220 ,AKC221 ,AKC515 ,AKC223 ,AKC224 ,AKC225 ,AKC226 ,AKC227 ,AKA070 ,AKA071 ,AKA076 ,AKA072 ,AKA073 ,AKC229 ,ZKA100 ,CKC126 ,EditState) ");
        //    sb.Append(" SELECT  'ZY' + Bl_Cf.Bl_Code ,'" + Cf_Code_New + "' ,'" + Cf_Date_New + "' ,Bl_Cfxm.Xm_Code ,Xm_Name ,'2' ,SUM(Bl_Cfxm.Cf_Money) / SUM(Cf_Sl) ,SUM(Cf_Sl) ,SUM(Bl_Cfxm.Cf_Money) ,NULL ,NULL ,NULL ,NULL ,NULL ,NULL ,Xm_Dw ,0 ,0 ");
        //    sb.Append(" FROM Bl_Cfxm ,Zd_Ml_Xm3 ,Bl_Cf ");
        //    sb.Append(" WHERE Bl_Cfxm.Xm_Code = Zd_Ml_Xm3.Xm_Code AND Bl_Cf.Cf_Code = Bl_Cfxm.Cf_Code AND Cf_Qr = '是' ");
        //    sb.Append(" AND EXISTS ( SELECT 1 FROM KC21 WHERE  isJs = 0 AND 'ZY' + Bl_Cf.Bl_Code = AKC190 ) ");
        //    sb.Append(" AND NOT EXISTS ( SELECT 1 FROM KC22 WHERE AKC190 = 'ZY' + Bl_Cf.Bl_Code AND Bl_Cfxm.Xm_Code = AKC515 )AND Xm_Name NOT LIKE '%床位费%' ");
        //    sb.Append(" GROUP BY Bl_Cf.Bl_Code ,Bl_Cfxm.Xm_Code ,Xm_Name ,Xm_Dw HAVING SUM(Cf_Sl)<> 0 ");
        //    arry.Add(sb.ToString());
        //    //6.将His中未上传床位费信息 导入kc22
        //    sb = new StringBuilder();
        //    sb.Append(" INSERT INTO KC22 (AKC190,AKC220,AKC221,AKC515,AKC223,AKC224,AKC225,AKC226,AKC227,AKA070,AKA071,AKA076,AKA072,AKA073,AKC229,ZKA100,CKC126,EditState) ");
        //    sb.Append(" SELECT  'ZY' + Bl_Cf.Bl_Code ,'" + Cf_Code_New + "' ,'" + Cf_Date_New + "' ,Bl_Cfxm.Xm_Code ,Xm_Name ,'2' ,SUM(Bl_Cfxm.Cf_Money) / SUM(Cf_Sl) ,SUM(Cf_Sl) ,SUM(Bl_Cfxm.Cf_Money) ,NULL ,NULL ,NULL ,NULL ,NULL ,NULL ,Xm_Dw ,0 ,0 ");
        //    sb.Append(" FROM Bl_Cfxm ,Zd_Ml_Xm3 ,Bl_Cf");
        //    sb.Append(" WHERE Bl_Cfxm.Xm_Code = Zd_Ml_Xm3.Xm_Code AND Cf_Qr = '是' AND Bl_Cf.Cf_Code = Bl_Cfxm.Cf_Code ");
        //    sb.Append(" AND EXISTS ( SELECT 1 FROM KC21 WHERE isJs = 0 AND 'ZY' + Bl_Cf.Bl_Code = AKC190 ) ");
        //    sb.Append(" AND NOT EXISTS ( SELECT 1 FROM KC22 WHERE AKC190 = 'ZY' + Bl_Cf.Bl_Code AND Bl_Cfxm.Xm_Code = AKC515 )AND Xm_Name LIKE '%床位费%' ");
        //    sb.Append(" GROUP BY Bl_Cf.Bl_Code ,Bl_Cfxm.Xm_Code ,Xm_Name ,Xm_Dw HAVING SUM(Cf_Sl)<> 0 ");
        //    arry.Add(sb.ToString());
        //    //try
        //    //{
        //    //    Common.WinFormVar.Var.DbHelper.ExecuteSqlTran(arry);
        //    //}
        //    //catch (Exception e)
        //    //{
        //    //    return false;
        //    //}
        //    //arry.Clear();

        //    //7 统计His中已经上传明细信息 A ,统计kc22中已经上传明细信息 B, 将A与B进行比较 得到发生改变结果 Result
        //    sb = new StringBuilder();
        //    sb.Append(" SELECT  * ,( ISNULL(A.AKC226,0) - ISNULL(B.Cf_Sl,0)) SlChaE ,( ISNULL(A.AKC227,0) - ISNULL(B.Cf_Money,0)) MoneyChaE FROM  ");
        //    sb.Append(" ( ");
        //    sb.Append(" SELECT 'ZY' + Bl_Code AS Bl_Code ,SUBSTRING(Xx_Code, 1, 11) Mx_Code ,SUM(Cf_Sl) Cf_Sl ,SUM(Bl_Cfyp.Cf_Money) Cf_Money ");
        //    sb.Append(" FROM Bl_Cf ,Bl_Cfyp ");
        //    sb.Append(" WHERE Bl_Cf.Cf_Code = Bl_Cfyp.Cf_Code AND Cf_Qr = '是'  ");
        //    sb.Append(" AND EXISTS ( SELECT 1 FROM KC21 WHERE  AKC190 = 'ZY' + Bl_Code )");
        //    //sb.Append(" AND NOT EXISTS(SELECT 1 FROM KC22 WHERE AKC190 = 'ZY' + Bl_Code AND SUBSTRING(Xx_Code, 1, 11) = AKC515 AND CKC126 = 0) ");
        //    sb.Append(" AND EXISTS (SELECT 1 FROM KC22 WHERE AKC190 = 'ZY' + Bl_Code AND  SUBSTRING(Xx_Code, 1, 11) = AKC515 AND CKC126 = 1) ");
        //    sb.Append(" GROUP BY  Bl_Code ,SUBSTRING(Xx_Code, 1, 11) ");
        //    sb.Append(" UNION ALL ");
        //    sb.Append(" SELECT 'ZY' + Bl_Code ,Xm_Code ,SUM(Cf_Sl) Cf_Sl ,SUM(Bl_Cfxm.Cf_Money) Cf_Money ");
        //    sb.Append(" FROM Bl_Cf ,Bl_Cfxm ");
        //    sb.Append(" WHERE Bl_Cf.Cf_Code = Bl_Cfxm.Cf_Code AND Cf_Qr = '是' ");
        //    sb.Append(" AND EXISTS ( SELECT 1 FROM KC21 WHERE AKC190 = 'ZY' + Bl_Code ) ");
        //    //sb.Append(" AND NOT EXISTS(SELECT 1 FROM KC22 WHERE AKC190 = 'ZY' + Bl_Code AND Xm_Code = AKC515 AND CKC126 = 0) ");
        //    sb.Append(" AND EXISTS(SELECT 1 FROM KC22 WHERE AKC190 = 'ZY' + Bl_Code AND Xm_Code = AKC515 AND CKC126 = 1)");
        //    sb.Append(" GROUP BY  Bl_Code ,Xm_Code ");
        //    sb.Append(" ) B FULL join ");
        //    sb.Append(" ( SELECT AKC190 ,AKC515 ,SUM(AKC226) AKC226 ,SUM(AKC227) AKC227 FROM KC22 WHERE EditState =1 AND SUBSTRING(AKC190,1,2) = 'ZY' GROUP BY AKC190 ,AKC515) A ");
        //    sb.Append(" ON	a.AKC190 = B.Bl_Code AND B.Mx_Code = a.AKC515 ");
        //    sb.Append(" WHERE  ISNULL(A.AKC226, 0) <> ISNULL(B.Cf_Sl,0) ");

        //    DataSet ds = new DataSet();
        //    ds = Common.WinFormVar.Var.DbHelper.Query(sb.ToString());

        //    foreach (DataRow _row in ds.Tables[0].Rows)
        //    {
        //        float SlChaE = float.Parse(_row["SlChaE"].ToString());
        //        float MoneyChaE = float.Parse(_row["MoneyChaE"].ToString());

        //        string temp_AKC190 = "";
        //        string temp_AKC515 = "";

        //        if (_row["Bl_Code"] == DBNull.Value)
        //        {
        //            temp_AKC190 = (string)_row["AKC190"];
        //        }
        //        else
        //        {
        //            temp_AKC190 = (string)_row["Bl_Code"];
        //        }

        //        if (_row["Mx_Code"] == DBNull.Value)
        //        {
        //            temp_AKC515 = (string)_row["AKC515"];
        //        }
        //        else
        //        {
        //            temp_AKC515 = (string)_row["Mx_Code"];
        //        }

        //        DataSet mxds = new DataSet();
        //        mxds = Common.WinFormVar.Var.DbHelper.Query("SELECT * FROM KC22 WHERE AKC190='" + temp_AKC190 + "' AND AKC515='" + temp_AKC515 + "' and editstate = 1");

        //        if (mxds.Tables[0].Rows.Count == 0)
        //        {
        //            sb = new StringBuilder();
        //            sb.Append(" INSERT  INTO KC22( AKC190 ,AKC220 ,AKC221 ,AKC515 ,AKC223 ,AKC224 ,AKC225 ,AKC226 ,AKC227 ,AKA070 ,AKA071 ,AKA076 ,AKA072 ,AKA073 ,AKC229 ,ZKA100 ,CKC126 ,EditState) ");
        //            sb.Append(" SELECT TOP 1 AKC190 ,'" + Cf_Code_New + "' ,'" + Cf_Date_New + "' ,AKC515 ,AKC223 ,AKC224 ," + (MoneyChaE / SlChaE) + " ," + (-SlChaE) + " ," + (-MoneyChaE) + " ,AKA070 ,NULL ,NULL ,NULL ,NULL ,NULL ,ZKA100 ,0 ,0");
        //            sb.Append(" FROM KC22 WHERE AKC190 = '" + temp_AKC190 + "'AND AKC515 = '" + temp_AKC515 + "' ");
        //            arry.Add(sb.ToString());
        //            continue;
        //        }
        //        else
        //        {
        //            foreach (DataRow mxrow in mxds.Tables[0].Rows)
        //            {
        //                if (SlChaE == 0)
        //                {
        //                    break;
        //                }
        //                if (float.Parse(mxrow["AKC226"].ToString()) == SlChaE)
        //                {
        //                    arry.Add("UPDATE KC22 SET EditState = 3 WHERE AKC190 = '" + mxrow["AKC190"] + "'AND AKC220 = '" + mxrow["AKC220"] + "'AND AKC221 = '" + mxrow["AKC221"] + "' AND AKC515 = '" + mxrow["AKC515"] + "'");
        //                    SlChaE = 0;
        //                }
        //                else if (float.Parse(mxrow["AKC226"].ToString()) > SlChaE)
        //                {

        //                    arry.Add("UPDATE KC22 SET EditState = 3 WHERE AKC190 = '" + mxrow["AKC190"] + "'AND AKC220 = '" + mxrow["AKC220"] + "'AND AKC221 = '" + mxrow["AKC221"] + "' AND AKC515 = '" + mxrow["AKC515"] + "'");
        //                    sb = new StringBuilder();
        //                    sb.Append("INSERT INTO KC22( AKC190 ,AKC220 ,AKC221 ,AKC515 ,AKC223 ,AKC224 ,AKC225 ,AKC226 ,AKC227 ,AKA070 ,AKA071 ,AKA076 ,AKA072 ,AKA073 ,AKC229 ,ZKA100 ,CKC126 ,EditState )");
        //                    sb.Append(" SELECT AKC190 ," + Cf_Code_New + " ,'" + Cf_Date_New + "' ,AKC515 ,AKC223 ,AKC224 ,AKC225 ," + mxrow["AKC226"] + " - " + SlChaE + " ," + mxrow["AKC227"] + " - " + MoneyChaE + " ,AKA070 ,AKA071 ,AKA076 ,AKA072 ,AKA073 ,AKC229 ,ZKA100 ,0 ,0 ");
        //                    sb.Append(" FROM KC22 WHERE AKC190='" + mxrow["AKC190"] + "' and AKC220='" + mxrow["AKC220"] + "' and AKC515='" + mxrow["AKC515"] + "' AND AKC221 = '" + mxrow["AKC221"] + "'");
        //                    arry.Add(sb.ToString());

        //                    SlChaE = 0;
        //                }
        //                else
        //                {

        //                    arry.Add("update KC22 set editstate=3  WHERE AKC190='" + mxrow["AKC190"] + "' and AKC220='" + mxrow["AKC220"] + "' and AKC515='" + mxrow["AKC515"] + "' AND AKC221 = '" + mxrow["AKC221"] + "'");

        //                    SlChaE = SlChaE - float.Parse(mxrow["AKC226"].ToString());
        //                    MoneyChaE = MoneyChaE - float.Parse(mxrow["AKC227"].ToString());

        //                }
        //            }

        //        }

        //    }

        //    try
        //    {
        //        Common.WinFormVar.Var.DbHelper.ExecuteSqlTran(arry);
        //        return true;
        //    }
        //    catch (Exception e)
        //    {
        //        return false;
        //    }

        //}


        #endregion

        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(Model.M_KC21 model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into KC21(");
            strSql.Append("AKC190,AKA130,AKC192,AKC193,AKC194,AKC195,AKC196,AAE011,AAE036,CKC126,ZKC271,ZKC272,ZKC274,ZKC275,CKA040,CKA041,ZHUZHI,ZHIYE,PHONE,BLH,AMC026,AMC100,AMC001,AMC013,AMC008,AAE050,AAE013,AKC120)");
            strSql.Append(" values (");
            strSql.Append("@AKC190,@AKA130,@AKC192,@AKC193,@AKC194,@AKC195,@AKC196,@AAE011,@AAE036,@CKC126,@ZKC271,@ZKC272,@ZKC274,@ZKC275,@CKA040,@CKA041,@ZHUZHI,@ZHIYE,@PHONE,@BLH,@AMC026,@AMC100,@AMC001,@AMC013,@AMC008,@AAE050,@AAE013,@AKC120)");
            SqlParameter[] parameters = {
                    new SqlParameter("@AKC190", SqlDbType.VarChar,18),
                    new SqlParameter("@AKA130", SqlDbType.VarChar,3),
                    new SqlParameter("@AKC192", SqlDbType.DateTime),
                    new SqlParameter("@AKC193", SqlDbType.VarChar,20),
                    new SqlParameter("@AKC194", SqlDbType.DateTime),
                    new SqlParameter("@AKC195", SqlDbType.VarChar,3),
                    new SqlParameter("@AKC196", SqlDbType.VarChar,20),
                    new SqlParameter("@AAE011", SqlDbType.VarChar,20),
                    new SqlParameter("@AAE036", SqlDbType.DateTime),
                    new SqlParameter("@CKC126", SqlDbType.Decimal,5),
                    new SqlParameter("@ZKC271", SqlDbType.VarChar,20),
                    new SqlParameter("@ZKC272", SqlDbType.VarChar,50),
                    new SqlParameter("@ZKC274", SqlDbType.VarChar,100),
                    new SqlParameter("@ZKC275", SqlDbType.VarChar,100),
                    new SqlParameter("@CKA040", SqlDbType.VarChar,10),
                    new SqlParameter("@CKA041", SqlDbType.VarChar,10),
                    new SqlParameter("@ZHUZHI", SqlDbType.VarChar,50),
                    new SqlParameter("@ZHIYE", SqlDbType.VarChar,50),
                    new SqlParameter("@PHONE", SqlDbType.VarChar,50),
                    new SqlParameter("@BLH", SqlDbType.VarChar,20),
                    new SqlParameter("@AMC026", SqlDbType.VarChar,3),
                    new SqlParameter("@AMC100", SqlDbType.Decimal,5),
                    new SqlParameter("@AMC001", SqlDbType.VarChar,20),
                    new SqlParameter("@AMC013", SqlDbType.Decimal,5),
                    new SqlParameter("@AMC008", SqlDbType.VarChar,20),
                    new SqlParameter("@AAE050", SqlDbType.VarChar,500),
                    new SqlParameter("@AAE013", SqlDbType.VarChar,100),
                    new SqlParameter("@AKC120", SqlDbType.VarChar,3),
                    };
            parameters[0].Value = model.AKC190;
            parameters[1].Value = model.AKA130;
            parameters[2].Value = model.AKC192;
            parameters[3].Value = model.AKC193;
            parameters[4].Value = model.AKC194;
            parameters[5].Value = model.AKC195;
            parameters[6].Value = model.AKC196;
            parameters[7].Value = model.AAE011;
            parameters[8].Value = model.AAE036;
            parameters[9].Value = model.CKC126;
            parameters[10].Value = model.ZKC271;
            parameters[11].Value = model.ZKC272;
            parameters[12].Value = model.ZKC274;
            parameters[13].Value = model.ZKC275;
            parameters[14].Value = model.CKA040;
            parameters[15].Value = model.CKA041;
            parameters[16].Value = model.ZHUZHI;
            parameters[17].Value = model.ZHIYE;
            parameters[18].Value = model.PHONE;
            parameters[19].Value = model.BLH;
            parameters[20].Value = model.AMC026;
            parameters[21].Value = model.AMC100;
            parameters[22].Value = model.AMC001;
            parameters[23].Value = model.AMC013;
            parameters[24].Value = model.AMC008;
            parameters[25].Value = model.AAE050;
            parameters[26].Value = model.AAE013;
            parameters[27].Value = model.AKC120;

            int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 更新Js_Date
        /// </summary>

        public bool UpdateJsDate(string AKC190, string Js_Date)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("UPDATE KC21 SET Js_Date = '" + Js_Date + "' ");
            if (!string.IsNullOrEmpty(AKC190))
            {
                strSql.Append(" WHERE AKC190 = '" + AKC190 + "'");
            }
            int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }

        }


        /// <summary>
        /// 更新RY_YLCODE
        /// </summary>

        public bool UpdateRY_YLCODE(string AKC190, string RY_YLCODE)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("UPDATE KC21 SET RY_YLCODE = '" + RY_YLCODE + "' ");
            if (string.IsNullOrEmpty(AKC190))
            {
                return false;
            }
            else
            {
                strSql.Append(" WHERE AKC190 = '" + AKC190 + "'");
            }
            int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }

        }

        /// <summary>
        /// 更新Kc21，kc22 表中传输标记
        /// </summary>
        public bool Update(string AKC190, int mark)
        {
            ArrayList arry = new ArrayList();
            arry.Add("UPDATE dbo.KC21 SET	CKC126 = " + mark + " WHERE AKC190 = '" + AKC190 + "'");
            arry.Add("UPDATE dbo.KC22 SET CKC126 = " + mark + " WHERE AKC190 = '" + AKC190 + "'");

            try
            {
                Common.WinFormVar.Var.DbHelper.ExecuteSqlTran(arry);
                return true;
            }
            catch (Exception e)
            {
                return false;
            }

        }
        /// <summary>
        /// 按条件 更新一条数据
        /// </summary>
        public bool Update(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("UPDATE dbo.KC21 SET CKC126 = '1' ");
            if (!string.IsNullOrEmpty(strWhere))
            {
                strSql.Append(" WHERE " + strWhere);
            }

            int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(Model.M_KC21 model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update KC21 set ");
            strSql.Append("AKA130=@AKA130,");
            strSql.Append("AKC192=@AKC192,");
            strSql.Append("AKC193=@AKC193,");
            strSql.Append("AKC194=@AKC194,");
            strSql.Append("AKC195=@AKC195,");
            strSql.Append("AKC196=@AKC196,");
            strSql.Append("AAE011=@AAE011,");
            strSql.Append("AAE036=@AAE036,");
            strSql.Append("CKC126=@CKC126,");
            strSql.Append("ZKC271=@ZKC271,");
            strSql.Append("ZKC272=@ZKC272,");
            strSql.Append("ZKC274=@ZKC274,");
            strSql.Append("ZKC275=@ZKC275,");
            strSql.Append("CKA040=@CKA040,");
            strSql.Append("CKA041=@CKA041,");
            strSql.Append("ZHUZHI=@ZHUZHI,");
            strSql.Append("ZHIYE=@ZHIYE,");
            strSql.Append("PHONE=@PHONE,");
            strSql.Append("BLH=@BLH,");
            strSql.Append("AMC026=@AMC026,");
            strSql.Append("AMC100=@AMC100,");
            strSql.Append("AMC001=@AMC001,");
            strSql.Append("AMC013=@AMC013,");
            strSql.Append("AMC008=@AMC008,");
            strSql.Append("AAE050=@AAE050,");
            strSql.Append("AAE013=@AAE013,");
            strSql.Append("AKC120=@AKC120,");
            //200614 接口更新增加字段  AMC020,LIVE,RING,TUBE
            strSql.Append(" AMC020=@AMC020,");
            strSql.Append(" LIVE=@LIVE,");
            strSql.Append(" RING=@RING,");
            strSql.Append(" TUBE=@TUBE,");

            strSql.Append("ISDK=@ISDK,");
            strSql.Append("RY_YLCODE=@RY_YLCODE,");
            strSql.Append("ZY_JSFS=@ZY_JSFS,");
            strSql.Append("JF_MONEY=@JF_MONEY,");
            strSql.Append("GrZhJe=@GrZhJe");
            //strSql.Append("Dj_Code=@Dj_Code");
            strSql.Append(" where AKC190=@AKC190 ");
            SqlParameter[] parameters = {
                    new SqlParameter("@AKA130", SqlDbType.VarChar,3),
                    new SqlParameter("@AKC192", SqlDbType.DateTime),
                    new SqlParameter("@AKC193", SqlDbType.VarChar,20),
                    new SqlParameter("@AKC194", SqlDbType.DateTime),
                    new SqlParameter("@AKC195", SqlDbType.VarChar,3),
                    new SqlParameter("@AKC196", SqlDbType.VarChar,20),
                    new SqlParameter("@AAE011", SqlDbType.VarChar,20),
                    new SqlParameter("@AAE036", SqlDbType.DateTime),
                    new SqlParameter("@CKC126", SqlDbType.Decimal,5),
                    new SqlParameter("@ZKC271", SqlDbType.VarChar,20),
                    new SqlParameter("@ZKC272", SqlDbType.VarChar,50),
                    new SqlParameter("@ZKC274", SqlDbType.VarChar,100),
                    new SqlParameter("@ZKC275", SqlDbType.VarChar,100),
                    new SqlParameter("@CKA040", SqlDbType.VarChar,10),
                    new SqlParameter("@CKA041", SqlDbType.VarChar,10),
                    new SqlParameter("@ZHUZHI", SqlDbType.VarChar,50),
                    new SqlParameter("@ZHIYE", SqlDbType.VarChar,50),
                    new SqlParameter("@PHONE", SqlDbType.VarChar,50),
                    new SqlParameter("@BLH", SqlDbType.VarChar,20),
                    new SqlParameter("@AMC026", SqlDbType.VarChar,3),
                    new SqlParameter("@AMC100", SqlDbType.Decimal,5),
                    new SqlParameter("@AMC001", SqlDbType.VarChar,20),
                    new SqlParameter("@AMC013", SqlDbType.Decimal,5),
                    new SqlParameter("@AMC008", SqlDbType.VarChar,20),
                    new SqlParameter("@AAE050", SqlDbType.VarChar,500),
                    new SqlParameter("@AAE013", SqlDbType.VarChar,100),
                    new SqlParameter("@AKC120", SqlDbType.VarChar,3),
                    // //200614 接口更新增加字段
                    new SqlParameter("@AMC020", SqlDbType.DateTime),
                    new SqlParameter("@LIVE", SqlDbType.VarChar,3),
                    new SqlParameter("@RING", SqlDbType.VarChar,3),
                    new SqlParameter("@TUBE", SqlDbType.VarChar,3),


                    new SqlParameter("@AKC190", SqlDbType.VarChar,18),
                    new SqlParameter("@ISDK", SqlDbType.VarChar,2),
                    new SqlParameter("@RY_YLCODE", SqlDbType.VarChar,50),
                    new SqlParameter("@ZY_JSFS", SqlDbType.VarChar,50),
                    new SqlParameter("@JF_MONEY", SqlDbType.VarChar,50),
                    new SqlParameter("@GrZhJe", SqlDbType.Decimal,18),
                    //new SqlParameter("@Dj_Code", SqlDbType.VarChar,30)
                   };
            parameters[0].Value = model.AKA130;
            parameters[1].Value = model.AKC192;
            if (model.AKC193 == null)
            {
                parameters[2].Value = DBNull.Value;
            }
            else
            {
                parameters[2].Value = model.AKC193;
            }
            if (model.AKC194 == null)
            {
                parameters[3].Value = DBNull.Value;
            }
            else
            {
                parameters[3].Value = model.AKC194;
            }
            if (model.AKC195 == null)
            {
                parameters[4].Value = DBNull.Value;
            }
            else
            {
                parameters[4].Value = model.AKC195;
            }
            if (model.AKC196 == null)
            {
                parameters[5].Value = DBNull.Value;
            }
            else
            {
                parameters[5].Value = model.AKC196;
            }

            parameters[6].Value = model.AAE011;
            parameters[7].Value = model.AAE036;
            parameters[8].Value = model.CKC126;
            parameters[9].Value = model.ZKC271;
            parameters[10].Value = model.ZKC272;
            if (model.ZKC274 == null)
            {
                parameters[11].Value = DBNull.Value;
            }
            else
            {
                parameters[11].Value = model.ZKC274;
            }
            if (model.ZKC275 == null)
            {
                parameters[12].Value = DBNull.Value;
            }
            else
            {
                parameters[12].Value = model.ZKC275;
            }
            if (model.CKA040 == null)
            {
                parameters[13].Value = DBNull.Value;
            }
            else
            {
                parameters[13].Value = model.CKA040;
            }
            if (model.CKA041 == null)
            {
                parameters[14].Value = DBNull.Value;
            }
            else
            {
                parameters[14].Value = model.CKA041;
            }
            if (model.ZHUZHI == null)
            {
                parameters[15].Value = DBNull.Value;
            }
            else
            {
                parameters[15].Value = model.ZHUZHI;
            }
            if (model.ZHIYE == null)
            {
                parameters[16].Value = DBNull.Value;
            }
            else
            {
                parameters[16].Value = model.ZHIYE;
            }
            if (model.PHONE == null)
            {
                parameters[17].Value = DBNull.Value;
            }
            else
            {
                parameters[17].Value = model.PHONE;
            }

            parameters[18].Value = model.BLH;
            if (model.AMC026 == null)
            {
                parameters[19].Value = DBNull.Value;
            }
            else
            {
                parameters[19].Value = model.AMC026;
            }

            if (model.AMC100 == null)
            {
                parameters[20].Value = DBNull.Value;
            }
            else
            {
                parameters[20].Value = model.AMC100;
            }
            if (model.AMC001 == null)
            {
                parameters[21].Value = DBNull.Value;
            }
            else
            {
                parameters[21].Value = model.AMC001;
            }

            if (model.AMC013 == null)
            {
                parameters[22].Value = DBNull.Value;
            }
            else
            {
                parameters[22].Value = model.AMC013;
            }
            if (model.AMC008 == null)
            {
                parameters[23].Value = DBNull.Value;
            }
            else
            {
                parameters[23].Value = model.AMC008;
            }
            if (model.AAE050 == null)
            {
                parameters[24].Value = DBNull.Value;
            }
            else
            {
                parameters[24].Value = model.AAE050;
            }
            if (model.AAE013 == null)
            {
                parameters[25].Value = DBNull.Value;
            }
            else
            {
                parameters[25].Value = model.AAE013;
            }
            if (model.AKC120 == null)
            {
                parameters[26].Value = DBNull.Value;
            }
            else
            {
                parameters[26].Value = model.AKC120;
            }


            //200614 接口更新增加字段
            if (model.AMC020 == null)
            {
                parameters[27].Value = DBNull.Value;
            }
            else
            {
                parameters[27].Value = model.AMC020;
            }
            if (model.LIVE == null)
            {
                parameters[28].Value = DBNull.Value;
            }
            else
            {
                parameters[28].Value = model.LIVE;
            }
            if (model.RING == null)
            {
                parameters[29].Value = DBNull.Value;
            }
            else
            {
                parameters[29].Value = model.RING;
            }
            if (model.TUBE == null)
            {
                parameters[30].Value = DBNull.Value;
            }
            else
            {
                parameters[30].Value = model.TUBE;
            }



            parameters[31].Value = model.AKC190;
            parameters[32].Value = model.ISDK;
            if (model.RY_YLCODE == null)
            {
                parameters[33].Value = DBNull.Value;
            }
            else
            {
                parameters[33].Value = model.RY_YLCODE;
            }
            parameters[34].Value = Common.Tools.IsValueNull(model.ZY_JSFS);
            parameters[35].Value = model.JF_MONEY;
            parameters[36].Value = Common.Tools.IsValueNull(model.GrZhJe);

            //if (model.Dj_Code == null)
            //{
            //    parameters[32].Value = DBNull.Value;
            //}
            //else
            //{
            //    parameters[32].Value = model.Dj_Code;
            //}
            int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 更新单据号
        /// </summary>

        public bool UpdateDj_Code(string AKC190, string Dj_Code)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(" UPDATE dbo.KC21 SET ");
            strSql.Append(" Dj_Code = @Dj_Code ");
            strSql.Append(" WHERE AKC190 = @AKC190 ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Dj_Code", SqlDbType.VarChar,18),
                    new SqlParameter("@AKC190", SqlDbType.VarChar,18)           };
            if (Dj_Code == null)
            {
                parameters[0].Value = DBNull.Value;
            }
            else
            {
                parameters[0].Value = Dj_Code;
            }
            parameters[1].Value = AKC190;
            int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(string AKC190)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from KC21 ");
            strSql.Append(" where AKC190=@AKC190 ");
            SqlParameter[] parameters = {
                    new SqlParameter("@AKC190", SqlDbType.VarChar,18)           };
            parameters[0].Value = AKC190;

            int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 删除就诊信息和费用明细
        /// </summary>
        /// <param name="AKC190"></param>
        /// <returns></returns>
        public bool DeleteAll(string AKC190)
        {
            ArrayList arry = new ArrayList();
            arry.Add(" DELETE KC22 WHERE AKC190 = '" + AKC190 + "'");
            arry.Add(" DELETE KC21 WHERE AKC190 = '" + AKC190 + "'");
            try
            {
                Common.WinFormVar.Var.DbHelper.ExecuteSqlTran(arry);
                return true;
            }
            catch (Exception e)
            {
                return false;
            }
        }

        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string AKC190list)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from KC21 ");
            strSql.Append(" where AKC190 in (" + AKC190list + ")  ");
            int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.M_KC21 GetModel(string AKC190)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 AKC190,AKA130,AKC192,AKC193,AKC194,AKC195,AKC196,AAE011,AAE036,CKC126,ZKC271,ZKC272,ZKC274,ZKC275,CKA040,CKA041,ZHUZHI,ZHIYE,PHONE,BLH,AMC026,AMC100,AMC001,AMC013,AMC008,AAE050,AAE013,AKC120,AMC020,LIVE,RING,TUBE,AKE205,BKF040,BKF050,Ry_Bxlb,ISDK,RY_YLCODE,JF_MONEY,ZY_JSFS from KC21 ");
            strSql.Append(" where AKC190=@AKC190 ");
            SqlParameter[] parameters = {
                    new SqlParameter("@AKC190", SqlDbType.VarChar,18)           };
            parameters[0].Value = AKC190;

            Model.M_KC21 model = new Model.M_KC21();
            DataSet ds = Common.WinFormVar.Var.DbHelper.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.M_KC21 DataRowToModel(DataRow row)
        {
            Model.M_KC21 model = new Model.M_KC21();
            if (row != null)
            {
                if (row["AKC190"] != null)
                {
                    model.AKC190 = row["AKC190"].ToString();
                }
                if (row["AKA130"] != null)
                {
                    model.AKA130 = row["AKA130"].ToString();
                }
                if (row["AKC192"] != null && row["AKC192"].ToString() != "")
                {
                    model.AKC192 = DateTime.Parse(row["AKC192"].ToString());
                }
                if (row["AKC193"] != null)
                {
                    model.AKC193 = row["AKC193"].ToString();
                }
                if (row["AKC194"] != null && row["AKC194"].ToString() != "")
                {
                    model.AKC194 = DateTime.Parse(row["AKC194"].ToString());
                }
                if (row["AKC195"] != null)
                {
                    model.AKC195 = row["AKC195"].ToString();
                }
                if (row["AKC196"] != null)
                {
                    model.AKC196 = row["AKC196"].ToString();
                }
                if (row["AAE011"] != null)
                {
                    model.AAE011 = row["AAE011"].ToString();
                }
                if (row["AAE036"] != null && row["AAE036"].ToString() != "")
                {
                    model.AAE036 = DateTime.Parse(row["AAE036"].ToString());
                }
                if (row["CKC126"] != null && row["CKC126"].ToString() != "")
                {
                    model.CKC126 = decimal.Parse(row["CKC126"].ToString());
                }
                if (row["ZKC271"] != null)
                {
                    model.ZKC271 = row["ZKC271"].ToString();
                }
                if (row["ZKC272"] != null)
                {
                    model.ZKC272 = row["ZKC272"].ToString();
                }
                if (row["ZKC274"] != null)
                {
                    model.ZKC274 = row["ZKC274"].ToString();
                }
                if (row["ZKC275"] != null)
                {
                    model.ZKC275 = row["ZKC275"].ToString();
                }
                if (row["CKA040"] != null)
                {
                    model.CKA040 = row["CKA040"].ToString();
                }
                if (row["CKA041"] != null)
                {
                    model.CKA041 = row["CKA041"].ToString();
                }
                if (row["ZHUZHI"] != null)
                {
                    model.ZHUZHI = row["ZHUZHI"].ToString();
                }
                if (row["ZHIYE"] != null)
                {
                    model.ZHIYE = row["ZHIYE"].ToString();
                }
                if (row["PHONE"] != null)
                {
                    model.PHONE = row["PHONE"].ToString();
                }
                if (row["BLH"] != null)
                {
                    model.BLH = row["BLH"].ToString();
                }
                if (row["AMC026"] != null)
                {
                    model.AMC026 = row["AMC026"].ToString();
                }
                if (row["AMC100"] != null && row["AMC100"].ToString() != "")
                {
                    model.AMC100 = decimal.Parse(row["AMC100"].ToString());
                }
                if (row["AMC001"] != null)
                {
                    model.AMC001 = row["AMC001"].ToString();
                }
                if (row["AMC013"] != null && row["AMC013"].ToString() != "")
                {
                    model.AMC013 = decimal.Parse(row["AMC013"].ToString());
                }
                if (row["AMC008"] != null)
                {
                    model.AMC008 = row["AMC008"].ToString();
                }
                if (row["AAE050"] != null)
                {
                    model.AAE050 = row["AAE050"].ToString();
                }
                if (row["AAE013"] != null)
                {
                    model.AAE013 = row["AAE013"].ToString();
                }
                if (row["AKC120"] != null)
                {
                    model.AKC120 = row["AKC120"].ToString();
                }

                //200614 接口更新增加字段  AMC020,LIVE,RING,TUBE

                if (row["AMC020"] != null && row["AMC020"].ToString() != "")
                {
                    model.AMC020 = DateTime.Parse(row["AMC020"].ToString());
                }
                if (row["LIVE"] != null)
                {
                    model.LIVE = row["LIVE"].ToString();
                }
                if (row["RING"] != null)
                {
                    model.RING = row["RING"].ToString();
                }
                if (row["TUBE"] != null)
                {
                    model.TUBE = row["TUBE"].ToString();
                }
                if (row["AKE205"] != null)
                {
                    model.AKE205 = row["AKE205"].ToString();
                }
                if (row["BKF040"] != null)
                {
                    model.BKF040 = row["BKF040"].ToString();
                }
                if (row["BKF050"] != null)
                {
                    model.BKF050 = row["BKF050"].ToString();
                }
                //

                if (row["Ry_Bxlb"] != null)
                {
                    model.Ry_Bxlb = row["Ry_Bxlb"].ToString();
                }
                if (row["ISDK"] != null)
                {
                    model.ISDK = row["ISDK"].ToString();
                }
                if (row["RY_YLCODE"] != null)
                {
                    model.RY_YLCODE = row["RY_YLCODE"].ToString();
                }
                if (row["JF_MONEY"] != null)
                {
                    model.JF_MONEY = row["JF_MONEY"].ToString();
                }
                if (row["ZY_JSFS"] != null)
                {
                    model.ZY_JSFS = row["ZY_JSFS"].ToString();
                }
            }
            return model;
        }

        /// <summary>
        /// 获得门诊、住院报销数据列表 以kc21表 为主
        /// </summary>
        public DataSet GetList(string Lb, string strWhere)
        {

            StringBuilder Sb = new StringBuilder();
            //Kc21表 医保原始字段
            Sb.Append("SELECT AKC190 ,AKA130 ,AKC192 ,AKC193 ,AKC194 ,AKC195 ,AKC196 ,AAE011 ,AAE036 ,CKC126 ,ZKC271 ,ZKC272 ,ZKC274 ,ZKC275 ,CKA040 ,CKA041 ,ZHUZHI ,ZHIYE ,PHONE ,BLH ,AMC026 ,AMC100 ,AMC001 ,AMC013 ,AMC008 ,AAE050 ,AAE013 ,AKC120   ,AMC020,LIVE,RING,TUBE,AKE205,BKF040,BKF050 ");
            //Kc21表 自定义字段
            Sb.Append(" ,Ry_Bxlb ,ISDK ,KC21.RY_YLCODE ,JF_MONEY ,ZY_JSFS ,isJs,Dj_Code,Js_Date ");

            Sb.Append(" ,Ry_Name ,CASE AKA130 WHEN'11'THEN'普通门诊'WHEN'15'THEN'门诊特殊病'WHEN '19'THEN '生育门诊'WHEN'21'THEN'普通住院'WHEN'51'THEN'生育住院'ELSE''END AS Yllb_Name");
            if (Lb == "门诊")
            {
                Sb.Append(" FROM KC21,(SELECT RY_NAME,MZ_CODE FROM MZ UNION ALL SELECT RY_NAME,MZ_CODE  FROM MZ_SUM ) as Mz ");
                Sb.Append("  WHERE KC21.AKC190 = 'MZ' + Mz.Mz_Code ");
            }
            if (Lb == "住院")
            {
                Sb.Append(" ,Bc_Name FROM KC21, Bl ,Zd_YyBc ");
                Sb.Append("  WHERE KC21.AKC190 = 'ZY' + Bl.Bl_Code AND Bl.Bc_Code = Zd_YyBc.Bc_Code AND IsNewVersion = '0'");
            }

            if (!string.IsNullOrEmpty(strWhere))
            {
                Sb.Append(" AND " + strWhere);
            }
            return Common.WinFormVar.Var.DbHelper.Query(Sb.ToString());
        }

        /// <summary>
        /// 获取新版 住院就诊信息
        /// </summary>
        public DataSet GetLis_Zy_NewVersion(string strWhere)
        {
            StringBuilder Sb = new StringBuilder();
            //Kc21表 医保原始字段
            Sb.Append(" SELECT AKC190 ,AKA130 ,AKC192 ,AKC193 ,AKC194 ,AKC195 ,AKC196 ,AAE011 ,AAE036 ,CKC126 ,ZKC271 ,ZKC272 ,ZKC274 ,ZKC275 ,CKA040 ,CKA041 ,ZHUZHI ,ZHIYE ,PHONE ,BLH ,AMC026 ,AMC100 ,AMC001 ,AMC013 ,AMC008 ,AAE050 ,AAE013 ,AKC120 ,AMC020,LIVE,RING,TUBE,AKE205,BKF040,BKF050");
            //Kc21表 自定义字段
            Sb.Append(" ,Ry_Bxlb ,ISDK ,KC21.RY_YLCODE ,JF_MONEY ,ZY_JSFS ,isJs,Dj_Code,Js_Date,GrZhJe ");

            Sb.Append(" ,Ry_Name ,CASE AKA130 WHEN'11'THEN'普通门诊'WHEN'15'THEN'门诊特殊病'WHEN '19'THEN '生育门诊'WHEN'21'THEN'普通住院'WHEN'51'THEN'生育住院'ELSE''END AS Yllb_Name");

            Sb.Append(" FROM KC21, Bl ");
            Sb.Append(" WHERE KC21.AKC190 = 'ZY' + Bl.Bx_BlCode AND IsNewVersion = '1'");

            if (!string.IsNullOrEmpty(strWhere))
            {
                Sb.Append(" AND " + strWhere);
            }
            return Common.WinFormVar.Var.DbHelper.Query(Sb.ToString());
        }




        /// <summary>
        /// 获取报表统计明细
        /// </summary>
        public DataSet GetPrintMx(string AKC190)
        {

            if (string.IsNullOrEmpty(AKC190))
            {
                return null;
            }
            StringBuilder Sb = new StringBuilder();

            Sb.Append(" SELECT Dl_Name 类别,SUM(AKC226)数量,SUM(AKC227) 金额 FROM dbo.KC22,dbo.V_Yp WHERE kc22.AKC515=dbo.V_Yp.Mx_Code  AND AKC190 = '" + AKC190 + "' GROUP BY Dl_Name ");
            Sb.Append(" UNION ALL ");
            Sb.Append(" SELECT Lb_Name 类别,SUM(AKC226)数量,SUM(AKC227) 金额 FROM dbo.Zd_JkFl1,dbo.Zd_JkFl2,dbo.KC22 WHERE dbo.Zd_JkFl1.Lb_Code=dbo.Zd_JkFl2.Lb_Code AND dbo.Zd_JkFl2.Mx_Code=kc22.AKC515 AND AKC190 = '" + AKC190 + "' GROUP BY Lb_Name ");

            return Common.WinFormVar.Var.DbHelper.Query(Sb.ToString());
        }


        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" AKC190,AKA130,AKC192,AKC193,AKC194,AKC195,AKC196,AAE011,AAE036,CKC126,ZKC271,ZKC272,ZKC274,ZKC275,CKA040,CKA041,ZHUZHI,ZHIYE,PHONE,BLH,AMC026,AMC100,AMC001,AMC013,AMC008,AAE050,AAE013,AKC120,Ry_Bxlb,ISDK,RY_YLCODE,JF_MONEY,ZY_JSFS ");
            strSql.Append(" FROM KC21 ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM KC21 ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.AKC190 desc");
            }
            strSql.Append(")AS Row, T.*  from KC21 T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
        }


        /// <summary>
        /// 门诊结算成功
        /// </summary>
        public bool UpdateMzOk(string AKC190)
        {
            ArrayList arry = new ArrayList();
            arry.Add("UPDATE dbo.KC21 SET CKC126 = " + 1 + ",isJs=1 WHERE AKC190 = '" + AKC190 + "'");
            arry.Add("UPDATE dbo.KC22 SET CKC126 = " + 1 + ",EditState=1 WHERE AKC190 = '" + AKC190 + "'");
            arry.Add("UPDATE Mz SET Mz_Print = " + 1 + ",Mz_Jffs='刷卡',Mz_SsMoney=Mz_Money,Mz_ThMoney=0,Jsr_Code = '" + HisVar.HisVar.JsrCode + "' WHERE Mz_Code = '" + AKC190.ToString().Substring(2) + "'");
            arry.Add("Update Mz_Yp Set Mz_Ph='" + AKC190.ToString().Substring(2) + "' Where Mz_Code='" + AKC190.ToString().Substring(2) + "' ");
            arry.Add("Update Mz_Xm Set Mz_Ph='" + AKC190.ToString().Substring(2) + "' Where Mz_Code='" + AKC190.ToString().Substring(2) + "' ");
            arry.Add("Update Mz Set Mz_FyQr=" + 1 + ",Mz_FyQr_Date=getdate(),Fy_JsrCode='" + HisVar.HisVar.JsrCode + "'  where Mz_Code In (Select Mz_Code From Mz_Xm) And Mz_Code not In (Select Mz_Code From Mz_Yp) And Mz_Code='" + AKC190.ToString().Substring(2) + "'");

            try
            {
                Common.WinFormVar.Var.DbHelper.ExecuteSqlTran(arry);
                return true;
            }
            catch (Exception e)
            {
                return false;
            }

        }

        /// <summary>
        /// 门诊结算回退
        /// </summary>
        public bool UpdateMzRollback(string AKC190)
        {
            ArrayList arry = new ArrayList();
            arry.Add("UPDATE dbo.KC21 Set isJs=2 WHERE AKC190 = '" + AKC190 + "'");
            try
            {
                Common.WinFormVar.Var.DbHelper.ExecuteSqlTran(arry);
                return true;
            }
            catch (Exception e)
            {
                return false;
            }

        }

        /// <summary>
        /// 住院信息修改
        /// </summary>
        public bool UpdateZyOk(string lb, string AKC190)
        {
            ArrayList arry = new ArrayList();
            if (lb == "住院登记" || lb == "住院录费预结算")
            {
                arry.Add("UPDATE dbo.KC21 SET CKC126 = 1  WHERE AKC190 = '" + AKC190 + "'");
            }


            if (lb == "住院录费预结算")
            {
                arry.Add("UPDATE dbo.KC22 SET CKC126 =1,EditState=1 WHERE AKC190 = '" + AKC190 + "' and EditState=0");
            }

            //arry.Add("UPDATE Mz SET Mz_Print = " + 1 + ",Mz_Jffs='刷卡',Mz_SsMoney=Mz_Money,Mz_ThMoney=0 WHERE Mz_Code = '" + AKC190.ToString().Substring(2) + "'");
            try
            {
                Common.WinFormVar.Var.DbHelper.ExecuteSqlTran(arry);
                return true;
            }
            catch (Exception e)
            {
                return false;
            }
        }
        /// <summary>
        /// 普通住院结算成功后，信息更新
        /// </summary>
        /// <param name="row"></param>
        /// <returns></returns>
        public bool UpdateCommonJs(DataRow row)
        {
            ArrayList arry = new ArrayList();
            arry.Add("UPDATE dbo.KC21 SET CKC126 = 1,IsJs=1, Dj_Code = '" + row["Dj_Code"] + "'  WHERE AKC190 = '" + row["AKC190"] + "'");
            arry.Add("delete dbo.KC22  WHERE AKC190 = '" + row["AKC190"] + "' and EditState=3");
            arry.Add("UPDATE dbo.KC22 SET CKC126 =1,EditState=1 WHERE AKC190 = '" + row["AKC190"] + "' and EditState<>3");
            try
            {
                Common.WinFormVar.Var.DbHelper.ExecuteSqlTran(arry);
                return true;
            }
            catch (Exception e)
            {
                return false;
            }
        }

        /// <summary>
        /// 新版本 普通住院结算成功后，信息更新
        /// </summary>
        /// <param name="row"></param>
        /// <returns></returns>
        public bool UpdateCommonJs_NewVersion(DataRow row)
        {
            ArrayList arry = new ArrayList();
            arry.Add("UPDATE KC21 SET CKC126 = 1,IsJs=1, Dj_Code = '" + row["Dj_Code"] + "'  WHERE AKC190 = '" + row["AKC190"] + "'");

            StringBuilder sb = new StringBuilder();
            sb.Append(" INSERT INTO DRYB_ZYJS( AKC190 ,AKA130 ,Dj_Code ,ZY_JSFS ,JF_MONEY ) ");
            sb.Append(" VALUES  ( ");
            sb.Append(" '" + row["AKC190"] + "',");
            sb.Append(" '" + row["AKA130"] + "',");
            sb.Append(" '" + row["Dj_Code"] + "',");
            sb.Append(" '" + row["ZY_JSFS"] + "',");
            sb.Append(" '" + row["JF_MONEY"] + "')");
            arry.Add(sb.ToString());
            try
            {
                Common.WinFormVar.Var.DbHelper.ExecuteSqlTran(arry);
                return true;
            }
            catch (Exception e)
            {
                return false;
            }
        }

        /// <summary>
        /// 年终结算成功后 信息更新
        /// </summary>
        /// <param name="row"></param>
        /// <returns></returns>
        public bool UpdateYearEnd(DataRow row)
        {
            //IsJs 0 未结算 1 普通结算 2 回退
            ArrayList arry = new ArrayList();
            arry.Add("UPDATE dbo.KC21 SET CKC126 = 1, Dj_Code = '" + row["Dj_Code"] + "' WHERE AKC190 = '" + row["AKC190"] + "'");
            //arry.Add("delete dbo.KC22  WHERE AKC190 = '" + row["AKC190"] + "' and EditState=3");
            arry.Add("UPDATE dbo.KC22 SET CKC126 =1,EditState=1,IsYearEnd ='1' WHERE AKC190 = '" + row["AKC190"] + "' and EditState<>3");

            StringBuilder sb = new StringBuilder();
            sb.Append(" INSERT INTO dbo.DRYB_ZYJS( AKC190 ,AKA130 ,Dj_Code ,ZY_JSFS ,JF_MONEY ) ");
            sb.Append(" VALUES  ( ");
            sb.Append(" '" + row["AKC190"] + "',");
            sb.Append(" '" + row["AKA130"] + "',");
            sb.Append(" '" + row["Dj_Code"] + "',");
            sb.Append(" '" + row["ZY_JSFS"] + "',");
            sb.Append(" '" + row["JF_MONEY"] + "')");
            arry.Add(sb.ToString());

            try
            {
                Common.WinFormVar.Var.DbHelper.ExecuteSqlTran(arry);
                return true;
            }
            catch (Exception e)
            {
                return false;
            }
        }


        /// <summary>
        /// 新版本 年终结算成功后 信息更新
        /// </summary>
        /// <param name="row"></param>
        /// <returns></returns>
        public bool UpdateYearEnd_NewVersion(DataRow row, string lb)
        {
            //IsJs 0 未结算 1 普通结算 2 回退
            ArrayList arry = new ArrayList();
            arry.Add("UPDATE KC21 SET CKC126 = 1, Dj_Code = '" + row["Dj_Code"] + "' WHERE AKC190 = '" + row["AKC190"] + "'");
            arry.Add("UPDATE KC22 SET IsYearEnd ='" + lb + "' WHERE AKC190 = '" + row["AKC190"] + "' AND IsYearEnd = '0'");


            StringBuilder sb = new StringBuilder();
            sb.Append(" INSERT INTO dbo.DRYB_ZYJS( AKC190 ,AKA130 ,Dj_Code ,ZY_JSFS ,JF_MONEY ) ");
            sb.Append(" VALUES  ( ");
            sb.Append(" '" + row["AKC190"] + "',");
            sb.Append(" '" + row["AKA130"] + "',");
            sb.Append(" '" + row["Dj_Code"] + "',");
            sb.Append(" '" + row["ZY_JSFS"] + "',");
            sb.Append(" '" + row["JF_MONEY"] + "')");
            arry.Add(sb.ToString());

            try
            {
                Common.WinFormVar.Var.DbHelper.ExecuteSqlTran(arry);
                return true;
            }
            catch (Exception e)
            {
                return false;
            }
        }

        /// <summary>
        /// 住院预结算成功后修改明细状态
        /// </summary>
        /// <param name="dt"></param>
        /// <returns></returns>

        public bool UpdateKc22ByDataTable(DataTable dt)
        {
            ArrayList arry = new ArrayList();
            string AKC190 = "";

            foreach (DataRow _row in dt.Rows)
            {
                if (AKC190 == "")
                {
                    AKC190 = _row["AKC190"].ToString();
                }
                StringBuilder sb = new StringBuilder();
                sb.Append("UPDATE KC22 SET CKC126 =1,EditState=1 WHERE AKC190 = '" + _row["AKC190"] + "' AND AKC220 = '" + _row["AKC220"] + "' AND AKC221 = '" + _row["AKC221"] + "' AND AKC515 = '" + _row["AKC515"] + "' AND EditState=0");
                arry.Add(sb.ToString());
            }

            arry.Add("UPDATE KC21 SET CKC126 = 1  WHERE AKC190 = '" + AKC190 + "'");
            try
            {
                Common.WinFormVar.Var.DbHelper.ExecuteSqlTran(arry);
                return true;
            }
            catch (Exception e)
            {
                return false;
            }
        }

        /// <summary>
        /// 住院结算回退
        /// </summary>
        public bool UpdateZyRollback(string AKC190)
        {
            ArrayList arry = new ArrayList();
            arry.Add("UPDATE dbo.KC21 Set isJs='0' WHERE AKC190 = '" + AKC190 + "'");
            try
            {
                Common.WinFormVar.Var.DbHelper.ExecuteSqlTran(arry);
                return true;
            }
            catch (Exception e)
            {
                return false;
            }

        }

        /// <summary>
        /// 判断 住院号是否做过年终结算
        /// </summary>
        /// <param name="AKC190"></param>
        /// <returns></returns>
        public bool IsYearEnd(string AKC190)
        {
            return Common.WinFormVar.Var.DbHelper.Exists("SELECT COUNT(1) FROM KC22 WHERE AKC190 = '" + AKC190 + "' AND IsYearEnd in( '1','2')");
        }

        /// <summary>
        /// 无费出院
        /// </summary>
        public bool UpdateZyWfcy(string AKC190)
        {
            ArrayList arry = new ArrayList();
            arry.Add("UPDATE dbo.KC21 Set CKC126=0, isJs=0 WHERE AKC190 = '" + AKC190 + "'");
            arry.Add("UPDATE dbo.KC22 Set CKC126=0,EditState=0 WHERE AKC190 = '" + AKC190 + "'");
            try
            {
                Common.WinFormVar.Var.DbHelper.ExecuteSqlTran(arry);
                return true;
            }
            catch (Exception e)
            {
                return false;
            }

        }

        /// <summary>
        /// 无费出院 新版本
        /// </summary>
        public bool UpdateZyWfcy_NewVersion(string AKC190)
        {
            ArrayList arry = new ArrayList();
            arry.Add("UPDATE dbo.KC21 Set CKC126=0, isJs=0 WHERE AKC190 = '" + AKC190 + "'");
            arry.Add("UPDATE dbo.KC22 Set CKC126=0,EditState=0,IsEnable = '1',AKC226 = HisCount,AKC227 = HisCount * AKC225  WHERE AKC190 = '" + AKC190 + "' AND IsYearEnd = '0'");
            try
            {
                Common.WinFormVar.Var.DbHelper.ExecuteSqlTran(arry);
                return true;
            }
            catch (Exception e)
            {
                return false;
            }
        }




        /*
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetList(int PageSize,int PageIndex,string strWhere)
        {
            SqlParameter[] parameters = {
                    new SqlParameter("@tblName", SqlDbType.VarChar, 255),
                    new SqlParameter("@fldName", SqlDbType.VarChar, 255),
                    new SqlParameter("@PageSize", SqlDbType.Int),
                    new SqlParameter("@PageIndex", SqlDbType.Int),
                    new SqlParameter("@IsReCount", SqlDbType.Bit),
                    new SqlParameter("@OrderType", SqlDbType.Bit),
                    new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
                    };
            parameters[0].Value = "KC21";
            parameters[1].Value = "AKC190";
            parameters[2].Value = PageSize;
            parameters[3].Value = PageIndex;
            parameters[4].Value = 0;
            parameters[5].Value = 0;
            parameters[6].Value = strWhere;	
            return  Common.WinFormVar.Var.DbHelper.RunProcedure("UP_GetRecordByPage",parameters,"ds");
        }*/

        #endregion  BasicMethod
        #region  ExtensionMethod
        /// <summary>
        /// 判断是否能够 更改 医保报销 住院号
        /// </summary>
        /// <param name="bl_Code"></param>
        /// <returns></returns>
        public bool IsCanChangeBxBlCode(string bl_Code)
        {
            bl_Code = "ZY" + bl_Code;
            return !Common.WinFormVar.Var.DbHelper.Exists("SELECT COUNT(1) FROM KC21 WHERE SUBSTRING(AKC190,1,16) = '" + bl_Code + "'");
        }
        #endregion  ExtensionMethod
    }
}

