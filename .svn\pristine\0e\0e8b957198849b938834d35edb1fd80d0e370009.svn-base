﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="C1Holder1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>252, 17</value>
  </metadata>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="CommDr.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADrwAAA68AZW8ckkAAAUISURBVGhD7ZprTFtlGICJRrOp8zLd1LmMZYvx8mOJl7Gb
        tx8aE/3jL+Mvnf6UEcyimXPeFmgrw3YgGZnJbKZREhMcjEsL9EKN6A9xEwasQGlLS0uhF0oppZTba9+v
        PSdtPZwL/Q79szd5cnoO3/e+71M+zvlOtiIpoVQq61UqFRQS7CHdjvTABOmPBYu8esDJwWAQbDZbQcDa
        eQuEQiEYGxsrCFj7lgAmsdvtBYGKwMzMDDgcjoKAtW8JYBKn01kQqAiEw2EYHx/PorGxURRcY3Nz8YG1
        ZRHYLKgIzM7OgsvlEsTh5L6eD1ibioDb7ebltz9G4fYPfidHrp9vFCoCkUgEJiYmeHlL8w98Z54iEld6
        bJxjNgLWpiLg8XiyaGpqyqLouAXUvctQY0pJKC+1co5DmOuZ+dZDNoFcUODNxgSUm5ZYiSt/jnGOlQIV
        gbm5OfB6vVk0Nzez4DkKvP5rHPZdjEG5McFKqH5oY8fnzuO6hueZYG0qApOTk7ygwGsNMdhdF4W9F6JQ
        1hlPSviIRPNfds45YqAiEI1GwefzZdHS0pIFCrz6yzw8VhshFNdFkhILrMS5y+3sWGZ+bp7M/AxYm1/g
        PUs9FqfBEW0Edp0Ps+ypDUOZPgYaQ0qi7W8nTE1NSUJYIFnYGV6BkWB+WAMrsL82BI98G8xityaYlIiC
        2jBJJKov66C1tRWmp6fJkQHPuZifnxcWeEU7Azur/LKxq9oPJ9rmQN2VktD1usDv94tClMDLlwKwQ+mT
        lUe/8UFpyywrof5JD+3t7RAIBMiRAc8zicViwgIvfT8ND1Z4ZOdhhQc+vDqTlPASCf01N3lp50OUwIsX
        fbD9rHtT2FnhhhNXQ6BJS/gDQdDpdAR8fcxlYWFBWODYBS/c/6VzU3jo63E4oJmAd38cJwKBYIi8tKyH
        KIEjdRNw7xm77Gz/wgFPVrngmHoUbnsfl5AHOjo6CLjv5yIej4sQqHHBtk9HZeWBz2zwhNIBR6tHSPMd
        171kqyyEKIFDGgfc/YlVNu47NQyPV9rg6DkraV79swE6Ozv/B27ccllcXBQWeFvrhoPV9rx4WmGDHaet
        sPXkUBbbPr4J+8+OwOGqm6T5zr7U/kYswgIUtxJPVQzDlo9usNxzcgD2fWWFw6qhZPMWON9gBIPBwILb
        hMzPXCQSCQEBgcDJmASfiHygwHOqYbizrI9wV3k/7P18CA4pB0nzhv4pznlCUBFYWloiD5RMjEZjFijw
        rNIKd5Reh61l/0LxmQEoUQ6Q5msaTOw4Zi5XDuZ6JlibigDej/lAgWcqh2BL6TUoPt0PJYobpHnjwDTn
        eLFsqsDzikHYc6oPSir7qTSPUBFYXl4m92M+UOCN2mE4WJFq3jTg5xwnFaxNRQBvZ3y8Uz8ILyhTzZsH
        A5xjNgIVgZWVFXI3yKS7uzsL81Aw1XzymDuWC5yj1WpJc3zo9XpyTLcjPXAyCuBaFCK+mOC8vh5iGuvp
        6cF/pexNn0oPKQJSwdy4zjO3Dvjwwj9eXD4WiwWbH1QoFAfS7UgPLLK6ukrWIm0wN97rma0zbt7w4YUC
        uMTybh6DEcDfAm0wNzaMr4748oJ7HxSi1jyG3ALYNL6847ePy8dsNtNrHgOLrK2tEQnaYG5c9/jt49Fk
        MtFtHoMRkAPMzax/3JFSbx5DbgH89mVrHgOLpD9SD3wX7urqkq95jGRyWf+7TTJ/r7Tmi4r+AwSvxGZU
        o/dqAAAAAElFTkSuQmCC
</value>
  </data>
  <metadata name="Image1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>607, 17</value>
  </metadata>
  <data name="Image1.ImageStream" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>
        AAEAAAD/////AQAAAAAAAAAMAgAAAFdTeXN0ZW0uV2luZG93cy5Gb3JtcywgVmVyc2lvbj0yLjAuMC4w
        LCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAACZTeXN0
        ZW0uV2luZG93cy5Gb3Jtcy5JbWFnZUxpc3RTdHJlYW1lcgEAAAAERGF0YQcCAgAAAAkDAAAADwMAAAAy
        BAAAAk1TRnQBSQFMAgEBAwEAAWwBAAFsAQABEAEAARABAAT/ARkBAAj/AUIBTQE2BwABNgMAASgDAAFA
        AwABEAMAAQEBAAEYBgABDNIAKoCWAAOAJAADgJYAA4ADwA8AA8APAAaAAwAqgAwAJIA5AAOAA8ADgAkA
        A8ABAAKAAQAC/wEAAv8BAAL/AQAC/wMAA4ADAAOABAAC/wPAAQAC/wPAAQAC/wPAAQAC/wPAAQAC/wPA
        AQAC/wOADAADgAQAAv8DwAEAAv8DwAEAAv8DwAEAAv8DwAEAAv8DgDkAA4ADwAOADAADwAEAAoABAAL/
        AQACgAEAAoADAAPAAwADgAMAA8ABAAL/A8ABAAL/A8ABAAL/A8ABAAL/A8ABAAL/A8ADgAkAA4AEAAL/
        A8ABAAL/A8ABAAL/A8ABAAL/A8ABAAL/A8ADAAOAOQADgAPAA4AJAAPAAQACgAEAAoAMwAMAA4AEAAL/
        A8ABAAL/A8ABAAL/A8ABAAL/A8ABAAL/A8ABAAL/A4AJAAOAAwADwAEAAv8DwAEAAv8DwAEAAv8DwAEA
        Av8DwAOAAwADgDkAIYADAAOAAwADgAMAA8ABAAL/A8ABAAL/A8ABAAL/A8ABAAL/A8ABAAL/A8ADgAYA
        A4ADAAPAAQAC/wPAAQAC/wPAAQAC/wPAAQAC/wPAAQAC/wMABoBjAAOABAAC/wPAAQAC/wPAAQAC/wPA
        AQAC/wPAAQAC/wPAAQAC/wOABgADgB4AA4ADAAPAA4BjAAOAAwADwAEAAv8DwAEAAv8DwAEAAv8DwAEA
        Av8DwAEAAv8DwAOABgAngAEAAv8DgGMAA4AEAAL/A8ABAAL/A8ABAAL/A8ABAAL/A8ABAAL/A8ABAAL/
        A4AJAAOAAwADwAEAAv8DwAEAAv8DwAEAAv8DwAEAAv8DwAEAAv8DwAOAYwADgCQAA4AJAAOABAAC/wPA
        AQAC/wPAAQAC/wPADwADgEUAAoABAAL/AQACgAEAAoATAAOAA8ABAAL/A8ABAAL/A8ABAAL/A8ASgAkA
        A4ADAAPAAQAC/wPAAQAC/wPAAwASgEgAAoABAAKAGQADgAPAAQAC/wPAAQAC/wPAA4AeAAOADwADgFoA
        AoABAAKAHAAPgCQAD4BdAAKAqQABQgFNAT4HAAE+AwABKAMAAUADAAEQAwABAQEAAQEFAAGAFwAD/wIA
        AQME/wMAAQEE/wIAAX8B+QGAAQEB4AYAAQEBwAMAAYwBAAFAAQEB0AMAAY4BAAFAAQEBoAMAAcYBAAFA
        AQEBoAMAAcABAAFAAQEBQAMAAf8B+wFAAQEBfwHgAgAB/wH9AUABAQQAAf8B/QFAAQEBoAMAAfgBDgF/
        AfkBoAF8AgAB/AEZAQABAwGgAYECAAH+AScBgAH/Ad8BfwIAAf4BHwHBAf8B4AH/AgAB/gF/BP8CAAs=
</value>
  </data>
</root>