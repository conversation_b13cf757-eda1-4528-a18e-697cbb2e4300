﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class Ys_Cf2
    Inherits HisControl.BaseForm
    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(Ys_Cf2))
        Me.C1Holder1 = New C1.Win.C1Command.C1CommandHolder()
        Me.C1Command1 = New C1.Win.C1Command.C1Command()
        Me.C1Command4 = New C1.Win.C1Command.C1Command()
        Me.C1Command5 = New C1.Win.C1Command.C1Command()
        Me.C1Command9 = New C1.Win.C1Command.C1Command()
        Me.C1Command7 = New C1.Win.C1Command.C1Command()
        Me.Panel2 = New System.Windows.Forms.Panel()
        Me.BtnSaveTemplate = New System.Windows.Forms.Button()
        Me.BtnPrintSqd = New System.Windows.Forms.Button()
        Me.comm5 = New System.Windows.Forms.Button()
        Me.T_Label5 = New System.Windows.Forms.Label()
        Me.comm4 = New System.Windows.Forms.Button()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.Comm3 = New System.Windows.Forms.Button()
        Me.Comm2 = New System.Windows.Forms.Button()
        Me.Comm1 = New System.Windows.Forms.Button()
        Me.T_Line1 = New System.Windows.Forms.Label()
        Me.T_Line2 = New System.Windows.Forms.Label()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel()
        Me.BtnAI = New Sunny.UI.UISymbolButton()
        Me.comboIdentityType = New ZTHisControl.ComboCommon()
        Me.ComboBxlb1 = New ZTHisControl.ComboBxlb()
        Me.YlCodeTextBox = New CustomControl.MyTextBox()
        Me.NlC1NumericEdit = New CustomControl.MyNumericEdit()
        Me.SingleSex1 = New ZTHisControl.SingleSex()
        Me.SfzhTextBox = New CustomControl.MyTextBox()
        Me.XmTextBox = New CustomControl.MyTextBox()
        Me.DateRyBirthday = New CustomControl.MyDateEdit()
        Me.GroupBox1 = New System.Windows.Forms.GroupBox()
        Me.ComboYf1 = New ZTHisControl.ComboYf()
        Me.C1ToolBar1 = New C1.Win.C1Command.C1ToolBar()
        Me.C1CommandLink1 = New C1.Win.C1Command.C1CommandLink()
        Me.C1NumericEdit1 = New C1.Win.C1Input.C1NumericEdit()
        Me.Label8 = New System.Windows.Forms.Label()
        Me.RadioButton4 = New System.Windows.Forms.RadioButton()
        Me.RadioButton1 = New System.Windows.Forms.RadioButton()
        Me.C1ToolBar4 = New C1.Win.C1Command.C1ToolBar()
        Me.C1CommandLink7 = New C1.Win.C1Command.C1CommandLink()
        Me.C1CommandLink8 = New C1.Win.C1Command.C1CommandLink()
        Me.C1CommandLink9 = New C1.Win.C1Command.C1CommandLink()
        Me.Label18 = New System.Windows.Forms.Label()
        Me.Label11 = New System.Windows.Forms.Label()
        Me.lblMz_Code = New System.Windows.Forms.Label()
        Me.Label20 = New System.Windows.Forms.Label()
        Me.C1Combo2 = New C1.Win.C1List.C1Combo()
        Me.ComboJb1 = New ZTHisControl.ComboJb()
        Me.C1ToolBar3 = New C1.Win.C1Command.C1ToolBar()
        Me.C1CommandLink5 = New C1.Win.C1Command.C1CommandLink()
        Me.FbDate = New CustomControl.MyDateEdit()
        Me.ComboYs1 = New ZTHisControl.ComboYs()
        Me.ComboKs1 = New ZTHisControl.ComboKs()
        Me.TxtTel = New CustomControl.MyTextBox()
        Me.lblHzName = New System.Windows.Forms.Label()
        Me.ComDiagType = New ZTHisControl.SingleZXY_DIAGNOSE_MARK()
        Me.SingleMzCfZxType1 = New ZTHisControl.SingleMzCfZxType()
        Me.lblBalc = New System.Windows.Forms.Label()
        Me.AddressTextBox = New CustomControl.MyTextBox()
        Me.NumAgeDay = New CustomControl.MyNumericEdit()
        Me.SplitContainer1 = New System.Windows.Forms.SplitContainer()
        Me.C1TrueDBGrid1 = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.C1TrueDBGrid2 = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.C1CommandDock1 = New C1.Win.C1Command.C1CommandDock()
        Me.C1DockingTab1 = New C1.Win.C1Command.C1DockingTab()
        Me.C1DockingTabPage1 = New C1.Win.C1Command.C1DockingTabPage()
        Me.ToolBarReadCard1 = New ZTHisPublicForm.ToolBarReadCard()
        Me.MzData1 = New ZTHisOutpatient.MzData()
        CType(Me.C1Holder1,System.ComponentModel.ISupportInitialize).BeginInit
        Me.Panel2.SuspendLayout
        Me.Panel1.SuspendLayout
        Me.TableLayoutPanel1.SuspendLayout
        Me.GroupBox1.SuspendLayout
        CType(Me.C1NumericEdit1,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.C1Combo2,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.SplitContainer1,System.ComponentModel.ISupportInitialize).BeginInit
        Me.SplitContainer1.Panel1.SuspendLayout
        Me.SplitContainer1.Panel2.SuspendLayout
        Me.SplitContainer1.SuspendLayout
        CType(Me.C1TrueDBGrid1,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.C1TrueDBGrid2,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.C1CommandDock1,System.ComponentModel.ISupportInitialize).BeginInit
        Me.C1CommandDock1.SuspendLayout
        CType(Me.C1DockingTab1,System.ComponentModel.ISupportInitialize).BeginInit
        Me.C1DockingTab1.SuspendLayout
        Me.C1DockingTabPage1.SuspendLayout
        Me.SuspendLayout
        '
        'C1Holder1
        '
        Me.C1Holder1.Commands.Add(Me.C1Command1)
        Me.C1Holder1.Commands.Add(Me.C1Command4)
        Me.C1Holder1.Commands.Add(Me.C1Command5)
        Me.C1Holder1.Commands.Add(Me.C1Command9)
        Me.C1Holder1.Commands.Add(Me.C1Command7)
        Me.C1Holder1.Owner = Me
        '
        'C1Command1
        '
        Me.C1Command1.Name = "C1Command1"
        Me.C1Command1.ShortcutText = ""
        Me.C1Command1.Text = "乘"
        '
        'C1Command4
        '
        Me.C1Command4.Image = CType(resources.GetObject("C1Command4.Image"),System.Drawing.Image)
        Me.C1Command4.Name = "C1Command4"
        Me.C1Command4.ShortcutText = ""
        Me.C1Command4.Text = "增加疾病"
        '
        'C1Command5
        '
        Me.C1Command5.Image = CType(resources.GetObject("C1Command5.Image"),System.Drawing.Image)
        Me.C1Command5.Name = "C1Command5"
        Me.C1Command5.ShortcutText = ""
        Me.C1Command5.Text = "撤销"
        '
        'C1Command9
        '
        Me.C1Command9.Image = CType(resources.GetObject("C1Command9.Image"),System.Drawing.Image)
        Me.C1Command9.Name = "C1Command9"
        Me.C1Command9.ShortcutText = ""
        Me.C1Command9.Text = "清除所有疾病"
        '
        'C1Command7
        '
        Me.C1Command7.Image = CType(resources.GetObject("C1Command7.Image"),System.Drawing.Image)
        Me.C1Command7.Name = "C1Command7"
        Me.C1Command7.ShortcutText = ""
        Me.C1Command7.Text = "填写病历"
        '
        'Panel2
        '
        Me.Panel2.Controls.Add(Me.BtnSaveTemplate)
        Me.Panel2.Controls.Add(Me.BtnPrintSqd)
        Me.Panel2.Controls.Add(Me.comm5)
        Me.Panel2.Controls.Add(Me.T_Label5)
        Me.Panel2.Controls.Add(Me.comm4)
        Me.Panel2.Controls.Add(Me.Label1)
        Me.Panel2.Controls.Add(Me.Comm3)
        Me.Panel2.Controls.Add(Me.Comm2)
        Me.Panel2.Controls.Add(Me.Comm1)
        Me.Panel2.Controls.Add(Me.T_Line1)
        Me.Panel2.Controls.Add(Me.T_Line2)
        Me.Panel2.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.Panel2.Location = New System.Drawing.Point(0, 512)
        Me.Panel2.Name = "Panel2"
        Me.Panel2.Size = New System.Drawing.Size(1146, 30)
        Me.Panel2.TabIndex = 37
        '
        'BtnSaveTemplate
        '
        Me.BtnSaveTemplate.Location = New System.Drawing.Point(643, 3)
        Me.BtnSaveTemplate.Name = "BtnSaveTemplate"
        Me.BtnSaveTemplate.Size = New System.Drawing.Size(75, 24)
        Me.BtnSaveTemplate.TabIndex = 60
        Me.BtnSaveTemplate.Tag = "保存成模版"
        Me.BtnSaveTemplate.Text = "保存成模版"
        Me.BtnSaveTemplate.UseVisualStyleBackColor = false
        '
        'BtnPrintSqd
        '
        Me.BtnPrintSqd.Location = New System.Drawing.Point(543, 3)
        Me.BtnPrintSqd.Name = "BtnPrintSqd"
        Me.BtnPrintSqd.Size = New System.Drawing.Size(100, 24)
        Me.BtnPrintSqd.TabIndex = 59
        Me.BtnPrintSqd.Tag = "打印医技申请单"
        Me.BtnPrintSqd.Text = "打印医技申请单"
        Me.BtnPrintSqd.UseVisualStyleBackColor = false
        '
        'comm5
        '
        Me.comm5.Location = New System.Drawing.Point(718, 3)
        Me.comm5.Name = "comm5"
        Me.comm5.Size = New System.Drawing.Size(72, 24)
        Me.comm5.TabIndex = 57
        Me.comm5.Tag = "数据结算"
        Me.comm5.Text = "数据结算"
        Me.comm5.UseVisualStyleBackColor = false
        '
        'T_Label5
        '
        Me.T_Label5.AutoSize = true
        Me.T_Label5.BackColor = System.Drawing.SystemColors.ButtonFace
        Me.T_Label5.ForeColor = System.Drawing.Color.DarkRed
        Me.T_Label5.Location = New System.Drawing.Point(844, 9)
        Me.T_Label5.Name = "T_Label5"
        Me.T_Label5.Size = New System.Drawing.Size(53, 12)
        Me.T_Label5.TabIndex = 56
        Me.T_Label5.Text = "T_Label5"
        Me.T_Label5.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'comm4
        '
        Me.comm4.Location = New System.Drawing.Point(473, 3)
        Me.comm4.Name = "comm4"
        Me.comm4.Size = New System.Drawing.Size(70, 24)
        Me.comm4.TabIndex = 55
        Me.comm4.Tag = "处置单"
        Me.comm4.Text = "处置单"
        Me.comm4.UseVisualStyleBackColor = false
        '
        'Label1
        '
        Me.Label1.AutoSize = true
        Me.Label1.BackColor = System.Drawing.SystemColors.ButtonFace
        Me.Label1.ForeColor = System.Drawing.Color.DarkRed
        Me.Label1.Location = New System.Drawing.Point(9, 9)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(257, 12)
        Me.Label1.TabIndex = 54
        Me.Label1.Text = "诊疗快捷键F1、药品F2、完成处方F3、处置单F4"
        Me.Label1.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Comm3
        '
        Me.Comm3.Location = New System.Drawing.Point(394, 3)
        Me.Comm3.Name = "Comm3"
        Me.Comm3.Size = New System.Drawing.Size(79, 24)
        Me.Comm3.TabIndex = 0
        Me.Comm3.Tag = "完成处方"
        Me.Comm3.Text = "完成处方"
        Me.Comm3.UseVisualStyleBackColor = false
        '
        'Comm2
        '
        Me.Comm2.DialogResult = System.Windows.Forms.DialogResult.Cancel
        Me.Comm2.Location = New System.Drawing.Point(344, 3)
        Me.Comm2.Name = "Comm2"
        Me.Comm2.Size = New System.Drawing.Size(50, 24)
        Me.Comm2.TabIndex = 46
        Me.Comm2.Tag = "取消"
        Me.Comm2.Text = "取消"
        Me.Comm2.UseVisualStyleBackColor = false
        '
        'Comm1
        '
        Me.Comm1.Location = New System.Drawing.Point(294, 3)
        Me.Comm1.Name = "Comm1"
        Me.Comm1.Size = New System.Drawing.Size(50, 24)
        Me.Comm1.TabIndex = 0
        Me.Comm1.Tag = "保存"
        Me.Comm1.Text = "保存"
        Me.Comm1.UseVisualStyleBackColor = false
        '
        'T_Line1
        '
        Me.T_Line1.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.T_Line1.Location = New System.Drawing.Point(285, -5)
        Me.T_Line1.Name = "T_Line1"
        Me.T_Line1.Size = New System.Drawing.Size(2, 40)
        Me.T_Line1.TabIndex = 42
        '
        'T_Line2
        '
        Me.T_Line2.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.T_Line2.Location = New System.Drawing.Point(800, -4)
        Me.T_Line2.Name = "T_Line2"
        Me.T_Line2.Size = New System.Drawing.Size(2, 40)
        Me.T_Line2.TabIndex = 29
        '
        'Panel1
        '
        Me.Panel1.Controls.Add(Me.TableLayoutPanel1)
        Me.Panel1.Dock = System.Windows.Forms.DockStyle.Top
        Me.Panel1.Location = New System.Drawing.Point(0, 28)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(1146, 161)
        Me.Panel1.TabIndex = 39
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.ColumnCount = 13
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 60!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 120!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 60!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 60!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 60!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 45!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 60!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 100!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 49!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 130!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 120!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 207!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 162!))
        Me.TableLayoutPanel1.Controls.Add(Me.BtnAI, 10, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.comboIdentityType, 6, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.ComboBxlb1, 2, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.YlCodeTextBox, 6, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.NlC1NumericEdit, 4, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.SingleSex1, 2, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.SfzhTextBox, 8, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.XmTextBox, 2, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.DateRyBirthday, 0, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.GroupBox1, 11, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.C1ToolBar4, 7, 3)
        Me.TableLayoutPanel1.Controls.Add(Me.Label18, 0, 4)
        Me.TableLayoutPanel1.Controls.Add(Me.Label11, 0, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.lblMz_Code, 1, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.Label20, 1, 4)
        Me.TableLayoutPanel1.Controls.Add(Me.C1Combo2, 9, 4)
        Me.TableLayoutPanel1.Controls.Add(Me.ComboJb1, 0, 3)
        Me.TableLayoutPanel1.Controls.Add(Me.C1ToolBar3, 10, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.FbDate, 8, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.ComboYs1, 0, 5)
        Me.TableLayoutPanel1.Controls.Add(Me.ComboKs1, 2, 5)
        Me.TableLayoutPanel1.Controls.Add(Me.TxtTel, 0, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.lblHzName, 9, 5)
        Me.TableLayoutPanel1.Controls.Add(Me.ComDiagType, 4, 3)
        Me.TableLayoutPanel1.Controls.Add(Me.SingleMzCfZxType1, 6, 5)
        Me.TableLayoutPanel1.Controls.Add(Me.lblBalc, 12, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.AddressTextBox, 8, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.NumAgeDay, 6, 1)
        Me.TableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(0, 0)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.RowCount = 7
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 26!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 26!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 26!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 26!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 26!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 26!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 20!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 20!))
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(1146, 161)
        Me.TableLayoutPanel1.TabIndex = 0
        '
        'BtnAI
        '
        Me.BtnAI.Cursor = System.Windows.Forms.Cursors.Hand
        Me.BtnAI.FillColor = System.Drawing.Color.FromArgb(CType(CType(110,Byte),Integer), CType(CType(190,Byte),Integer), CType(CType(40,Byte),Integer))
        Me.BtnAI.FillColor2 = System.Drawing.Color.FromArgb(CType(CType(110,Byte),Integer), CType(CType(190,Byte),Integer), CType(CType(40,Byte),Integer))
        Me.BtnAI.FillHoverColor = System.Drawing.Color.FromArgb(CType(CType(139,Byte),Integer), CType(CType(203,Byte),Integer), CType(CType(83,Byte),Integer))
        Me.BtnAI.FillPressColor = System.Drawing.Color.FromArgb(CType(CType(88,Byte),Integer), CType(CType(152,Byte),Integer), CType(CType(32,Byte),Integer))
        Me.BtnAI.FillSelectedColor = System.Drawing.Color.FromArgb(CType(CType(88,Byte),Integer), CType(CType(152,Byte),Integer), CType(CType(32,Byte),Integer))
        Me.BtnAI.Font = New System.Drawing.Font("微软雅黑", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.BtnAI.Image = CType(resources.GetObject("BtnAI.Image"),System.Drawing.Image)
        Me.BtnAI.LightStyle = true
        Me.BtnAI.Location = New System.Drawing.Point(747, 55)
        Me.BtnAI.MinimumSize = New System.Drawing.Size(1, 1)
        Me.BtnAI.Name = "BtnAI"
        Me.BtnAI.RectColor = System.Drawing.Color.FromArgb(CType(CType(110,Byte),Integer), CType(CType(190,Byte),Integer), CType(CType(40,Byte),Integer))
        Me.BtnAI.RectHoverColor = System.Drawing.Color.FromArgb(CType(CType(139,Byte),Integer), CType(CType(203,Byte),Integer), CType(CType(83,Byte),Integer))
        Me.BtnAI.RectPressColor = System.Drawing.Color.FromArgb(CType(CType(88,Byte),Integer), CType(CType(152,Byte),Integer), CType(CType(32,Byte),Integer))
        Me.BtnAI.RectSelectedColor = System.Drawing.Color.FromArgb(CType(CType(88,Byte),Integer), CType(CType(152,Byte),Integer), CType(CType(32,Byte),Integer))
        Me.TableLayoutPanel1.SetRowSpan(Me.BtnAI, 2)
        Me.BtnAI.Size = New System.Drawing.Size(100, 35)
        Me.BtnAI.Style = Sunny.UI.UIStyle.Green
        Me.BtnAI.TabIndex = 183
        Me.BtnAI.Text = "AI辅助"
        Me.BtnAI.TipsFont = New System.Drawing.Font("微软雅黑", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.BtnAI.ZoomScaleRect = New System.Drawing.Rectangle(0, 0, 0, 0)
        '
        'comboIdentityType
        '
        Me.comboIdentityType.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.comboIdentityType.Bookmark = -1
        Me.comboIdentityType.Captain = "证件类别"
        Me.comboIdentityType.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.comboIdentityType.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.comboIdentityType.CaptainWidth = 70!
        Me.comboIdentityType.ColumnCaptionHeight = 18
        Me.TableLayoutPanel1.SetColumnSpan(Me.comboIdentityType, 2)
        Me.comboIdentityType.DataSource = Nothing
        Me.comboIdentityType.DataView = Nothing
        Me.comboIdentityType.ItemHeight = 16
        Me.comboIdentityType.ItemTextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.comboIdentityType.Location = New System.Drawing.Point(408, 3)
        Me.comboIdentityType.MaximumSize = New System.Drawing.Size(10000, 23)
        Me.comboIdentityType.MinimumSize = New System.Drawing.Size(0, 20)
        Me.comboIdentityType.Name = "comboIdentityType"
        Me.comboIdentityType.ReadOnly = false
        Me.comboIdentityType.Row = 0
        Me.comboIdentityType.Size = New System.Drawing.Size(154, 20)
        Me.comboIdentityType.TabIndex = 2
        Me.comboIdentityType.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        '
        'ComboBxlb1
        '
        Me.ComboBxlb1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.ComboBxlb1.Bookmark = -1
        Me.ComboBxlb1.Captain = "患者类别"
        Me.ComboBxlb1.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.ComboBxlb1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.ComboBxlb1.CaptainWidth = 70!
        Me.ComboBxlb1.ColumnCaptionHeight = 18
        Me.TableLayoutPanel1.SetColumnSpan(Me.ComboBxlb1, 4)
        Me.ComboBxlb1.DataSource = Nothing
        Me.ComboBxlb1.DataView = Nothing
        Me.ComboBxlb1.ItemHeight = 16
        Me.ComboBxlb1.ItemTextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.ComboBxlb1.Location = New System.Drawing.Point(183, 55)
        Me.ComboBxlb1.MaximumSize = New System.Drawing.Size(10000, 23)
        Me.ComboBxlb1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.ComboBxlb1.Name = "ComboBxlb1"
        Me.ComboBxlb1.ReadOnly = false
        Me.ComboBxlb1.Row = 0
        Me.ComboBxlb1.Size = New System.Drawing.Size(219, 20)
        Me.ComboBxlb1.TabIndex = 10
        Me.ComboBxlb1.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        '
        'YlCodeTextBox
        '
        Me.YlCodeTextBox.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.YlCodeTextBox.Captain = "医保卡号"
        Me.YlCodeTextBox.CaptainBackColor = System.Drawing.Color.Transparent
        Me.YlCodeTextBox.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.YlCodeTextBox.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.YlCodeTextBox.CaptainWidth = 70!
        Me.TableLayoutPanel1.SetColumnSpan(Me.YlCodeTextBox, 2)
        Me.YlCodeTextBox.ContentForeColor = System.Drawing.Color.Black
        Me.YlCodeTextBox.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer))
        Me.YlCodeTextBox.EditMask = Nothing
        Me.YlCodeTextBox.Location = New System.Drawing.Point(408, 55)
        Me.YlCodeTextBox.Multiline = false
        Me.YlCodeTextBox.Name = "YlCodeTextBox"
        Me.YlCodeTextBox.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.YlCodeTextBox.ReadOnly = false
        Me.YlCodeTextBox.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.YlCodeTextBox.SelectionStart = 0
        Me.YlCodeTextBox.SelectStart = 0
        Me.YlCodeTextBox.Size = New System.Drawing.Size(154, 20)
        Me.YlCodeTextBox.TabIndex = 11
        Me.YlCodeTextBox.TextAlign = System.Windows.Forms.HorizontalAlignment.Left
        Me.YlCodeTextBox.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.YlCodeTextBox.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.YlCodeTextBox.Watermark = Nothing
        '
        'NlC1NumericEdit
        '
        Me.NlC1NumericEdit.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.NlC1NumericEdit.Captain = "年龄"
        Me.NlC1NumericEdit.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.NlC1NumericEdit.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.NlC1NumericEdit.CaptainWidth = 40!
        Me.TableLayoutPanel1.SetColumnSpan(Me.NlC1NumericEdit, 2)
        Me.NlC1NumericEdit.Location = New System.Drawing.Point(303, 29)
        Me.NlC1NumericEdit.MaximumSize = New System.Drawing.Size(100000, 80)
        Me.NlC1NumericEdit.MinimumSize = New System.Drawing.Size(0, 20)
        Me.NlC1NumericEdit.Name = "NlC1NumericEdit"
        Me.NlC1NumericEdit.NumericInputKeys = CType(((((C1.Win.C1Input.NumericInputKeyFlags.F9 Or C1.Win.C1Input.NumericInputKeyFlags.Minus)  _
            Or C1.Win.C1Input.NumericInputKeyFlags.Plus)  _
            Or C1.Win.C1Input.NumericInputKeyFlags.[Decimal])  _
            Or C1.Win.C1Input.NumericInputKeyFlags.X),C1.Win.C1Input.NumericInputKeyFlags)
        Me.NlC1NumericEdit.NumFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.NlC1NumericEdit.ReadOnly = false
        Me.NlC1NumericEdit.Size = New System.Drawing.Size(99, 20)
        Me.NlC1NumericEdit.TabIndex = 6
        Me.NlC1NumericEdit.ValueIsDbNull = true
        '
        'SingleSex1
        '
        Me.SingleSex1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.SingleSex1.Captain = "性    别"
        Me.SingleSex1.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.SingleSex1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.SingleSex1.CaptainWidth = 59!
        Me.TableLayoutPanel1.SetColumnSpan(Me.SingleSex1, 2)
        Me.SingleSex1.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        Me.SingleSex1.ItemHeight = 16
        Me.SingleSex1.ItemTextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.SingleSex1.Location = New System.Drawing.Point(183, 29)
        Me.SingleSex1.MaximumSize = New System.Drawing.Size(10000, 23)
        Me.SingleSex1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.SingleSex1.Name = "SingleSex1"
        Me.SingleSex1.ReadOnly = false
        Me.SingleSex1.Size = New System.Drawing.Size(114, 20)
        Me.SingleSex1.TabIndex = 5
        Me.SingleSex1.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        '
        'SfzhTextBox
        '
        Me.SfzhTextBox.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.SfzhTextBox.Captain = "证件号码"
        Me.SfzhTextBox.CaptainBackColor = System.Drawing.Color.Transparent
        Me.SfzhTextBox.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.SfzhTextBox.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.SfzhTextBox.CaptainWidth = 70!
        Me.TableLayoutPanel1.SetColumnSpan(Me.SfzhTextBox, 2)
        Me.SfzhTextBox.ContentForeColor = System.Drawing.Color.Black
        Me.SfzhTextBox.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer))
        Me.SfzhTextBox.EditMask = Nothing
        Me.SfzhTextBox.Location = New System.Drawing.Point(568, 3)
        Me.SfzhTextBox.Multiline = false
        Me.SfzhTextBox.Name = "SfzhTextBox"
        Me.SfzhTextBox.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.SfzhTextBox.ReadOnly = false
        Me.SfzhTextBox.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.SfzhTextBox.SelectionStart = 0
        Me.SfzhTextBox.SelectStart = 0
        Me.SfzhTextBox.Size = New System.Drawing.Size(173, 20)
        Me.SfzhTextBox.TabIndex = 3
        Me.SfzhTextBox.TextAlign = System.Windows.Forms.HorizontalAlignment.Left
        Me.SfzhTextBox.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.SfzhTextBox.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.SfzhTextBox.Watermark = Nothing
        '
        'XmTextBox
        '
        Me.XmTextBox.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.XmTextBox.Captain = "病人姓名"
        Me.XmTextBox.CaptainBackColor = System.Drawing.Color.Transparent
        Me.XmTextBox.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.XmTextBox.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.XmTextBox.CaptainWidth = 70!
        Me.TableLayoutPanel1.SetColumnSpan(Me.XmTextBox, 4)
        Me.XmTextBox.ContentForeColor = System.Drawing.Color.Black
        Me.XmTextBox.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer))
        Me.XmTextBox.EditMask = Nothing
        Me.XmTextBox.Location = New System.Drawing.Point(183, 3)
        Me.XmTextBox.Multiline = false
        Me.XmTextBox.Name = "XmTextBox"
        Me.XmTextBox.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.XmTextBox.ReadOnly = false
        Me.XmTextBox.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.XmTextBox.SelectionStart = 0
        Me.XmTextBox.SelectStart = 0
        Me.XmTextBox.Size = New System.Drawing.Size(219, 20)
        Me.XmTextBox.TabIndex = 1
        Me.XmTextBox.TextAlign = System.Windows.Forms.HorizontalAlignment.Left
        Me.XmTextBox.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.XmTextBox.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.XmTextBox.Watermark = Nothing
        '
        'DateRyBirthday
        '
        Me.DateRyBirthday.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.DateRyBirthday.Captain = "出生日期"
        Me.DateRyBirthday.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.DateRyBirthday.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.DateRyBirthday.CaptainWidth = 70!
        Me.TableLayoutPanel1.SetColumnSpan(Me.DateRyBirthday, 2)
        Me.DateRyBirthday.InitialSelection = C1.Win.C1Input.InitialSelectionEnum.CaretAtStart
        Me.DateRyBirthday.Location = New System.Drawing.Point(3, 29)
        Me.DateRyBirthday.MaximumSize = New System.Drawing.Size(100000000, 23)
        Me.DateRyBirthday.MinimumSize = New System.Drawing.Size(0, 20)
        Me.DateRyBirthday.Name = "DateRyBirthday"
        Me.DateRyBirthday.ReadOnly = false
        Me.DateRyBirthday.Size = New System.Drawing.Size(174, 20)
        Me.DateRyBirthday.TabIndex = 4
        Me.DateRyBirthday.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.DateRyBirthday.ValueIsDbNull = false
        Me.DateRyBirthday.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.DateRyBirthday.VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.DropDown
        '
        'GroupBox1
        '
        Me.GroupBox1.Controls.Add(Me.ComboYf1)
        Me.GroupBox1.Controls.Add(Me.C1ToolBar1)
        Me.GroupBox1.Controls.Add(Me.C1NumericEdit1)
        Me.GroupBox1.Controls.Add(Me.Label8)
        Me.GroupBox1.Controls.Add(Me.RadioButton4)
        Me.GroupBox1.Controls.Add(Me.RadioButton1)
        Me.GroupBox1.Location = New System.Drawing.Point(867, 3)
        Me.GroupBox1.Name = "GroupBox1"
        Me.TableLayoutPanel1.SetRowSpan(Me.GroupBox1, 5)
        Me.GroupBox1.Size = New System.Drawing.Size(194, 124)
        Me.GroupBox1.TabIndex = 69
        Me.GroupBox1.TabStop = false
        Me.GroupBox1.Text = "药材类别"
        '
        'ComboYf1
        '
        Me.ComboYf1.Bookmark = -1
        Me.ComboYf1.Captain = "药    房"
        Me.ComboYf1.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.ComboYf1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.ComboYf1.CaptainWidth = 70!
        Me.ComboYf1.ColumnCaptionHeight = 18
        Me.ComboYf1.DataSource = Nothing
        Me.ComboYf1.DataView = Nothing
        Me.ComboYf1.ItemHeight = 16
        Me.ComboYf1.ItemTextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.ComboYf1.Location = New System.Drawing.Point(3, 23)
        Me.ComboYf1.MaximumSize = New System.Drawing.Size(10000, 23)
        Me.ComboYf1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.ComboYf1.Name = "ComboYf1"
        Me.ComboYf1.ReadOnly = false
        Me.ComboYf1.Row = 0
        Me.ComboYf1.Size = New System.Drawing.Size(184, 23)
        Me.ComboYf1.TabIndex = 183
        Me.ComboYf1.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        '
        'C1ToolBar1
        '
        Me.C1ToolBar1.AccessibleName = "Tool Bar"
        Me.C1ToolBar1.Border.Style = C1.Win.C1Command.BorderStyleEnum.Ridge
        Me.C1ToolBar1.CommandHolder = Me.C1Holder1
        Me.C1ToolBar1.CommandLinks.AddRange(New C1.Win.C1Command.C1CommandLink() {Me.C1CommandLink1})
        Me.C1ToolBar1.Location = New System.Drawing.Point(135, 87)
        Me.C1ToolBar1.Name = "C1ToolBar1"
        Me.C1ToolBar1.Size = New System.Drawing.Size(28, 28)
        Me.C1ToolBar1.Text = "C1ToolBar1"
        Me.C1ToolBar1.VisualStyle = C1.Win.C1Command.VisualStyle.Classic
        Me.C1ToolBar1.VisualStyleBase = C1.Win.C1Command.VisualStyle.Classic
        '
        'C1CommandLink1
        '
        Me.C1CommandLink1.ButtonLook = C1.Win.C1Command.ButtonLookFlags.Text
        Me.C1CommandLink1.Command = Me.C1Command1
        '
        'C1NumericEdit1
        '
        Me.C1NumericEdit1.AutoSize = false
        Me.C1NumericEdit1.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1NumericEdit1.ImagePadding = New System.Windows.Forms.Padding(0)
        Me.C1NumericEdit1.Location = New System.Drawing.Point(66, 96)
        Me.C1NumericEdit1.Name = "C1NumericEdit1"
        Me.C1NumericEdit1.Size = New System.Drawing.Size(53, 16)
        Me.C1NumericEdit1.TabIndex = 70
        Me.C1NumericEdit1.Tag = Nothing
        Me.C1NumericEdit1.TextAlign = System.Windows.Forms.HorizontalAlignment.Right
        Me.C1NumericEdit1.VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.None
        '
        'Label8
        '
        Me.Label8.AutoSize = true
        Me.Label8.Location = New System.Drawing.Point(7, 98)
        Me.Label8.Name = "Label8"
        Me.Label8.Size = New System.Drawing.Size(53, 12)
        Me.Label8.TabIndex = 69
        Me.Label8.Text = "草药副数"
        '
        'RadioButton4
        '
        Me.RadioButton4.AutoSize = true
        Me.RadioButton4.Location = New System.Drawing.Point(9, 69)
        Me.RadioButton4.Name = "RadioButton4"
        Me.RadioButton4.Size = New System.Drawing.Size(83, 16)
        Me.RadioButton4.TabIndex = 62
        Me.RadioButton4.Text = "&1.诊疗项目"
        Me.RadioButton4.UseVisualStyleBackColor = true
        '
        'RadioButton1
        '
        Me.RadioButton1.AutoSize = true
        Me.RadioButton1.Location = New System.Drawing.Point(102, 69)
        Me.RadioButton1.Name = "RadioButton1"
        Me.RadioButton1.Size = New System.Drawing.Size(83, 16)
        Me.RadioButton1.TabIndex = 62
        Me.RadioButton1.Text = "&2.药品卫材"
        Me.RadioButton1.UseVisualStyleBackColor = true
        '
        'C1ToolBar4
        '
        Me.C1ToolBar4.AccessibleName = "Tool Bar"
        Me.C1ToolBar4.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom)  _
            Or System.Windows.Forms.AnchorStyles.Left),System.Windows.Forms.AnchorStyles)
        Me.C1ToolBar4.AutoSize = false
        Me.TableLayoutPanel1.SetColumnSpan(Me.C1ToolBar4, 3)
        Me.C1ToolBar4.CommandHolder = Me.C1Holder1
        Me.C1ToolBar4.CommandLinks.AddRange(New C1.Win.C1Command.C1CommandLink() {Me.C1CommandLink7, Me.C1CommandLink8, Me.C1CommandLink9})
        Me.C1ToolBar4.Location = New System.Drawing.Point(468, 81)
        Me.C1ToolBar4.Movable = false
        Me.C1ToolBar4.Name = "C1ToolBar4"
        Me.C1ToolBar4.Size = New System.Drawing.Size(253, 20)
        Me.C1ToolBar4.Text = "C1ToolBar4"
        Me.C1ToolBar4.VisualStyle = C1.Win.C1Command.VisualStyle.Classic
        Me.C1ToolBar4.VisualStyleBase = C1.Win.C1Command.VisualStyle.Classic
        '
        'C1CommandLink7
        '
        Me.C1CommandLink7.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image),C1.Win.C1Command.ButtonLookFlags)
        Me.C1CommandLink7.Command = Me.C1Command4
        Me.C1CommandLink7.Text = "增加疾病"
        '
        'C1CommandLink8
        '
        Me.C1CommandLink8.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image),C1.Win.C1Command.ButtonLookFlags)
        Me.C1CommandLink8.Command = Me.C1Command5
        Me.C1CommandLink8.SortOrder = 1
        '
        'C1CommandLink9
        '
        Me.C1CommandLink9.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image),C1.Win.C1Command.ButtonLookFlags)
        Me.C1CommandLink9.Command = Me.C1Command9
        Me.C1CommandLink9.SortOrder = 2
        '
        'Label18
        '
        Me.Label18.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom)  _
            Or System.Windows.Forms.AnchorStyles.Left)  _
            Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.Label18.AutoSize = true
        Me.Label18.Location = New System.Drawing.Point(3, 104)
        Me.Label18.Name = "Label18"
        Me.Label18.Size = New System.Drawing.Size(54, 26)
        Me.Label18.TabIndex = 172
        Me.Label18.Text = "全部疾病"
        Me.Label18.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Label11
        '
        Me.Label11.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom)  _
            Or System.Windows.Forms.AnchorStyles.Left)  _
            Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.Label11.AutoSize = true
        Me.Label11.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label11.Location = New System.Drawing.Point(3, 0)
        Me.Label11.Name = "Label11"
        Me.Label11.Size = New System.Drawing.Size(54, 26)
        Me.Label11.TabIndex = 158
        Me.Label11.Text = "门诊编码"
        Me.Label11.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'lblMz_Code
        '
        Me.lblMz_Code.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.lblMz_Code.BackColor = System.Drawing.SystemColors.Info
        Me.lblMz_Code.Location = New System.Drawing.Point(63, 5)
        Me.lblMz_Code.Name = "lblMz_Code"
        Me.lblMz_Code.Size = New System.Drawing.Size(114, 16)
        Me.lblMz_Code.TabIndex = 0
        Me.lblMz_Code.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'Label20
        '
        Me.Label20.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.Label20.BackColor = System.Drawing.SystemColors.Info
        Me.TableLayoutPanel1.SetColumnSpan(Me.Label20, 8)
        Me.Label20.Location = New System.Drawing.Point(63, 108)
        Me.Label20.Name = "Label20"
        Me.Label20.Size = New System.Drawing.Size(548, 17)
        Me.Label20.TabIndex = 171
        Me.Label20.TextAlign = System.Drawing.ContentAlignment.BottomLeft
        '
        'C1Combo2
        '
        Me.C1Combo2.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.C1Combo2.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1Combo2.Caption = ""
        Me.C1Combo2.CaptionHeight = 17
        Me.C1Combo2.CharacterCasing = System.Windows.Forms.CharacterCasing.Normal
        Me.C1Combo2.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.C1Combo2.Images.Add(CType(resources.GetObject("C1Combo2.Images"),System.Drawing.Image))
        Me.C1Combo2.ItemHeight = 15
        Me.C1Combo2.Location = New System.Drawing.Point(617, 109)
        Me.C1Combo2.MatchEntryTimeout = CType(2000,Long)
        Me.C1Combo2.MaxDropDownItems = CType(5,Short)
        Me.C1Combo2.MaxLength = 32767
        Me.C1Combo2.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.C1Combo2.Name = "C1Combo2"
        Me.C1Combo2.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.C1Combo2.Size = New System.Drawing.Size(124, 16)
        Me.C1Combo2.TabIndex = 1
        Me.C1Combo2.PropBag = resources.GetString("C1Combo2.PropBag")
        '
        'ComboJb1
        '
        Me.ComboJb1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.ComboJb1.Bookmark = -1
        Me.ComboJb1.Captain = "疾病诊断"
        Me.ComboJb1.CaptainFont = New System.Drawing.Font("宋体", 9!)
        Me.ComboJb1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.ComboJb1.CaptainWidth = 70!
        Me.ComboJb1.ColumnCaptionHeight = 18
        Me.TableLayoutPanel1.SetColumnSpan(Me.ComboJb1, 4)
        Me.ComboJb1.DataSource = Nothing
        Me.ComboJb1.DataView = Nothing
        Me.ComboJb1.ItemHeight = 16
        Me.ComboJb1.ItemTextFont = New System.Drawing.Font("宋体", 9!)
        Me.ComboJb1.Location = New System.Drawing.Point(3, 81)
        Me.ComboJb1.MaximumSize = New System.Drawing.Size(10000, 23)
        Me.ComboJb1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.ComboJb1.Name = "ComboJb1"
        Me.ComboJb1.ReadOnly = false
        Me.ComboJb1.Row = 0
        Me.ComboJb1.Size = New System.Drawing.Size(294, 20)
        Me.ComboJb1.TabIndex = 13
        Me.ComboJb1.TextFont = New System.Drawing.Font("宋体", 9!)
        '
        'C1ToolBar3
        '
        Me.C1ToolBar3.AccessibleName = "Tool Bar"
        Me.C1ToolBar3.CommandHolder = Me.C1Holder1
        Me.C1ToolBar3.CommandLinks.AddRange(New C1.Win.C1Command.C1CommandLink() {Me.C1CommandLink5})
        Me.C1ToolBar3.Location = New System.Drawing.Point(747, 3)
        Me.C1ToolBar3.Name = "C1ToolBar3"
        Me.C1ToolBar3.Size = New System.Drawing.Size(83, 24)
        Me.C1ToolBar3.Text = "C1ToolBar3"
        Me.C1ToolBar3.VisualStyle = C1.Win.C1Command.VisualStyle.Classic
        Me.C1ToolBar3.VisualStyleBase = C1.Win.C1Command.VisualStyle.Classic
        '
        'C1CommandLink5
        '
        Me.C1CommandLink5.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image),C1.Win.C1Command.ButtonLookFlags)
        Me.C1CommandLink5.Command = Me.C1Command7
        '
        'FbDate
        '
        Me.FbDate.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.FbDate.Captain = "发病时间"
        Me.FbDate.CaptainFont = New System.Drawing.Font("宋体", 9!)
        Me.FbDate.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.FbDate.CaptainWidth = 70!
        Me.TableLayoutPanel1.SetColumnSpan(Me.FbDate, 2)
        Me.FbDate.InitialSelection = C1.Win.C1Input.InitialSelectionEnum.CaretAtStart
        Me.FbDate.Location = New System.Drawing.Point(568, 55)
        Me.FbDate.MaximumSize = New System.Drawing.Size(100000000, 23)
        Me.FbDate.MinimumSize = New System.Drawing.Size(0, 20)
        Me.FbDate.Name = "FbDate"
        Me.FbDate.ReadOnly = false
        Me.FbDate.Size = New System.Drawing.Size(173, 20)
        Me.FbDate.TabIndex = 12
        Me.FbDate.TextFont = New System.Drawing.Font("宋体", 9!)
        Me.FbDate.ValueIsDbNull = false
        Me.FbDate.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.FbDate.VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.DropDown
        '
        'ComboYs1
        '
        Me.ComboYs1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.ComboYs1.Bookmark = -1
        Me.ComboYs1.Captain = "医    生"
        Me.ComboYs1.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.ComboYs1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.ComboYs1.CaptainWidth = 70!
        Me.ComboYs1.ColumnCaptionHeight = 18
        Me.TableLayoutPanel1.SetColumnSpan(Me.ComboYs1, 2)
        Me.ComboYs1.DataSource = Nothing
        Me.ComboYs1.DataView = Nothing
        Me.ComboYs1.ItemHeight = 16
        Me.ComboYs1.ItemTextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.ComboYs1.Location = New System.Drawing.Point(3, 133)
        Me.ComboYs1.MaximumSize = New System.Drawing.Size(10000, 23)
        Me.ComboYs1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.ComboYs1.Name = "ComboYs1"
        Me.ComboYs1.ReadOnly = false
        Me.ComboYs1.Row = 0
        Me.ComboYs1.Size = New System.Drawing.Size(174, 20)
        Me.ComboYs1.TabIndex = 15
        Me.ComboYs1.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        '
        'ComboKs1
        '
        Me.ComboKs1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.ComboKs1.Bookmark = -1
        Me.ComboKs1.Captain = "科    室"
        Me.ComboKs1.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.ComboKs1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.ComboKs1.CaptainWidth = 70!
        Me.ComboKs1.ColumnCaptionHeight = 18
        Me.TableLayoutPanel1.SetColumnSpan(Me.ComboKs1, 4)
        Me.ComboKs1.DataSource = Nothing
        Me.ComboKs1.DataView = Nothing
        Me.ComboKs1.ItemHeight = 16
        Me.ComboKs1.ItemTextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.ComboKs1.Location = New System.Drawing.Point(183, 133)
        Me.ComboKs1.MaximumSize = New System.Drawing.Size(10000, 23)
        Me.ComboKs1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.ComboKs1.Name = "ComboKs1"
        Me.ComboKs1.ReadOnly = false
        Me.ComboKs1.Row = 0
        Me.ComboKs1.Size = New System.Drawing.Size(219, 20)
        Me.ComboKs1.TabIndex = 16
        Me.ComboKs1.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        '
        'TxtTel
        '
        Me.TxtTel.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.TxtTel.Captain = "联系方式"
        Me.TxtTel.CaptainBackColor = System.Drawing.Color.Transparent
        Me.TxtTel.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.TxtTel.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.TxtTel.CaptainWidth = 70!
        Me.TableLayoutPanel1.SetColumnSpan(Me.TxtTel, 2)
        Me.TxtTel.ContentForeColor = System.Drawing.Color.Black
        Me.TxtTel.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer))
        Me.TxtTel.EditMask = Nothing
        Me.TxtTel.Location = New System.Drawing.Point(3, 55)
        Me.TxtTel.Multiline = false
        Me.TxtTel.Name = "TxtTel"
        Me.TxtTel.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.TxtTel.ReadOnly = false
        Me.TxtTel.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.TxtTel.SelectionStart = 0
        Me.TxtTel.SelectStart = 0
        Me.TxtTel.Size = New System.Drawing.Size(174, 20)
        Me.TxtTel.TabIndex = 9
        Me.TxtTel.TextAlign = System.Windows.Forms.HorizontalAlignment.Left
        Me.TxtTel.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.TxtTel.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.TxtTel.Watermark = Nothing
        '
        'lblHzName
        '
        Me.lblHzName.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom)  _
            Or System.Windows.Forms.AnchorStyles.Left)  _
            Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.lblHzName.AutoSize = true
        Me.lblHzName.Location = New System.Drawing.Point(617, 130)
        Me.lblHzName.Name = "lblHzName"
        Me.lblHzName.Size = New System.Drawing.Size(124, 26)
        Me.lblHzName.TabIndex = 174
        Me.lblHzName.Text = "病人姓名"
        Me.lblHzName.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'ComDiagType
        '
        Me.ComDiagType.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.ComDiagType.Captain = "诊断类型"
        Me.ComDiagType.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.ComDiagType.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.ComDiagType.CaptainWidth = 59!
        Me.TableLayoutPanel1.SetColumnSpan(Me.ComDiagType, 3)
        Me.ComDiagType.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        Me.ComDiagType.ItemHeight = 16
        Me.ComDiagType.ItemTextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.ComDiagType.Location = New System.Drawing.Point(303, 81)
        Me.ComDiagType.MaximumSize = New System.Drawing.Size(10000, 23)
        Me.ComDiagType.MinimumSize = New System.Drawing.Size(0, 20)
        Me.ComDiagType.Name = "ComDiagType"
        Me.ComDiagType.ReadOnly = false
        Me.ComDiagType.Size = New System.Drawing.Size(159, 20)
        Me.ComDiagType.TabIndex = 14
        Me.ComDiagType.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        '
        'SingleMzCfZxType1
        '
        Me.SingleMzCfZxType1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.SingleMzCfZxType1.Captain = "执行性质"
        Me.SingleMzCfZxType1.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.SingleMzCfZxType1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.SingleMzCfZxType1.CaptainWidth = 59!
        Me.TableLayoutPanel1.SetColumnSpan(Me.SingleMzCfZxType1, 2)
        Me.SingleMzCfZxType1.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        Me.SingleMzCfZxType1.ItemHeight = 16
        Me.SingleMzCfZxType1.ItemTextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.SingleMzCfZxType1.Location = New System.Drawing.Point(408, 133)
        Me.SingleMzCfZxType1.MaximumSize = New System.Drawing.Size(10000, 23)
        Me.SingleMzCfZxType1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.SingleMzCfZxType1.Name = "SingleMzCfZxType1"
        Me.SingleMzCfZxType1.ReadOnly = false
        Me.SingleMzCfZxType1.Size = New System.Drawing.Size(154, 20)
        Me.SingleMzCfZxType1.TabIndex = 17
        Me.SingleMzCfZxType1.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        '
        'lblBalc
        '
        Me.lblBalc.AutoSize = true
        Me.lblBalc.Font = New System.Drawing.Font("宋体", 14.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.lblBalc.Location = New System.Drawing.Point(1074, 0)
        Me.lblBalc.Name = "lblBalc"
        Me.TableLayoutPanel1.SetRowSpan(Me.lblBalc, 2)
        Me.lblBalc.Size = New System.Drawing.Size(0, 19)
        Me.lblBalc.TabIndex = 177
        '
        'AddressTextBox
        '
        Me.AddressTextBox.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.AddressTextBox.Captain = "家庭住址"
        Me.AddressTextBox.CaptainBackColor = System.Drawing.Color.Transparent
        Me.AddressTextBox.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.AddressTextBox.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.AddressTextBox.CaptainWidth = 70!
        Me.TableLayoutPanel1.SetColumnSpan(Me.AddressTextBox, 3)
        Me.AddressTextBox.ContentForeColor = System.Drawing.Color.Black
        Me.AddressTextBox.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer))
        Me.AddressTextBox.EditMask = Nothing
        Me.AddressTextBox.Location = New System.Drawing.Point(568, 29)
        Me.AddressTextBox.Multiline = false
        Me.AddressTextBox.Name = "AddressTextBox"
        Me.AddressTextBox.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.AddressTextBox.ReadOnly = false
        Me.AddressTextBox.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.AddressTextBox.SelectionStart = 0
        Me.AddressTextBox.SelectStart = 0
        Me.AddressTextBox.Size = New System.Drawing.Size(293, 20)
        Me.AddressTextBox.TabIndex = 8
        Me.AddressTextBox.TextAlign = System.Windows.Forms.HorizontalAlignment.Left
        Me.AddressTextBox.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.AddressTextBox.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.AddressTextBox.Watermark = Nothing
        '
        'NumAgeDay
        '
        Me.NumAgeDay.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.NumAgeDay.Captain = "年龄（天）"
        Me.NumAgeDay.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.NumAgeDay.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.NumAgeDay.CaptainWidth = 40!
        Me.TableLayoutPanel1.SetColumnSpan(Me.NumAgeDay, 2)
        Me.NumAgeDay.Location = New System.Drawing.Point(408, 29)
        Me.NumAgeDay.MaximumSize = New System.Drawing.Size(100000, 80)
        Me.NumAgeDay.MinimumSize = New System.Drawing.Size(0, 20)
        Me.NumAgeDay.Name = "NumAgeDay"
        Me.NumAgeDay.NumericInputKeys = CType(((((C1.Win.C1Input.NumericInputKeyFlags.F9 Or C1.Win.C1Input.NumericInputKeyFlags.Minus)  _
            Or C1.Win.C1Input.NumericInputKeyFlags.Plus)  _
            Or C1.Win.C1Input.NumericInputKeyFlags.[Decimal])  _
            Or C1.Win.C1Input.NumericInputKeyFlags.X),C1.Win.C1Input.NumericInputKeyFlags)
        Me.NumAgeDay.NumFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.NumAgeDay.ReadOnly = false
        Me.NumAgeDay.Size = New System.Drawing.Size(154, 20)
        Me.NumAgeDay.TabIndex = 7
        Me.NumAgeDay.ValueIsDbNull = true
        '
        'SplitContainer1
        '
        Me.SplitContainer1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.SplitContainer1.Location = New System.Drawing.Point(0, 189)
        Me.SplitContainer1.Name = "SplitContainer1"
        '
        'SplitContainer1.Panel1
        '
        Me.SplitContainer1.Panel1.Controls.Add(Me.C1TrueDBGrid1)
        '
        'SplitContainer1.Panel2
        '
        Me.SplitContainer1.Panel2.Controls.Add(Me.C1TrueDBGrid2)
        Me.SplitContainer1.Size = New System.Drawing.Size(651, 323)
        Me.SplitContainer1.SplitterDistance = 215
        Me.SplitContainer1.TabIndex = 42
        '
        'C1TrueDBGrid1
        '
        Me.C1TrueDBGrid1.GroupByCaption = "将列头拖拽到这里以便按照该列进行分组"
        Me.C1TrueDBGrid1.Images.Add(CType(resources.GetObject("C1TrueDBGrid1.Images"),System.Drawing.Image))
        Me.C1TrueDBGrid1.Location = New System.Drawing.Point(81, 71)
        Me.C1TrueDBGrid1.Name = "C1TrueDBGrid1"
        Me.C1TrueDBGrid1.PreviewInfo.Caption = "PrintPreview窗口"
        Me.C1TrueDBGrid1.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.C1TrueDBGrid1.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.C1TrueDBGrid1.PreviewInfo.ZoomFactor = 75R
        Me.C1TrueDBGrid1.PrintInfo.MeasurementDevice = C1.Win.C1TrueDBGrid.PrintInfo.MeasurementDeviceEnum.Screen
        Me.C1TrueDBGrid1.PrintInfo.MeasurementPrinterName = Nothing
        Me.C1TrueDBGrid1.Size = New System.Drawing.Size(185, 150)
        Me.C1TrueDBGrid1.TabIndex = 0
        Me.C1TrueDBGrid1.UseCompatibleTextRendering = false
        Me.C1TrueDBGrid1.PropBag = resources.GetString("C1TrueDBGrid1.PropBag")
        '
        'C1TrueDBGrid2
        '
        Me.C1TrueDBGrid2.GroupByCaption = "将列头拖拽到这里以便按照该列进行分组"
        Me.C1TrueDBGrid2.Images.Add(CType(resources.GetObject("C1TrueDBGrid2.Images"),System.Drawing.Image))
        Me.C1TrueDBGrid2.Location = New System.Drawing.Point(145, 59)
        Me.C1TrueDBGrid2.Name = "C1TrueDBGrid2"
        Me.C1TrueDBGrid2.PreviewInfo.Caption = "PrintPreview窗口"
        Me.C1TrueDBGrid2.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.C1TrueDBGrid2.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.C1TrueDBGrid2.PreviewInfo.ZoomFactor = 75R
        Me.C1TrueDBGrid2.PrintInfo.MeasurementDevice = C1.Win.C1TrueDBGrid.PrintInfo.MeasurementDeviceEnum.Screen
        Me.C1TrueDBGrid2.PrintInfo.MeasurementPrinterName = Nothing
        Me.C1TrueDBGrid2.Size = New System.Drawing.Size(92, 150)
        Me.C1TrueDBGrid2.TabIndex = 0
        Me.C1TrueDBGrid2.UseCompatibleTextRendering = false
        Me.C1TrueDBGrid2.PropBag = resources.GetString("C1TrueDBGrid2.PropBag")
        '
        'C1CommandDock1
        '
        Me.C1CommandDock1.Controls.Add(Me.C1DockingTab1)
        Me.C1CommandDock1.Dock = System.Windows.Forms.DockStyle.Right
        Me.C1CommandDock1.Id = 1
        Me.C1CommandDock1.Location = New System.Drawing.Point(651, 189)
        Me.C1CommandDock1.Name = "C1CommandDock1"
        Me.C1CommandDock1.Size = New System.Drawing.Size(495, 323)
        '
        'C1DockingTab1
        '
        Me.C1DockingTab1.Alignment = System.Windows.Forms.TabAlignment.Right
        Me.C1DockingTab1.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1DockingTab1.CanAutoHide = true
        Me.C1DockingTab1.CanMoveTabs = true
        Me.C1DockingTab1.Controls.Add(Me.C1DockingTabPage1)
        Me.C1DockingTab1.Location = New System.Drawing.Point(0, 0)
        Me.C1DockingTab1.Name = "C1DockingTab1"
        Me.C1DockingTab1.ShowCaption = true
        Me.C1DockingTab1.Size = New System.Drawing.Size(495, 323)
        Me.C1DockingTab1.TabIndex = 0
        Me.C1DockingTab1.TabSizeMode = C1.Win.C1Command.TabSizeModeEnum.Fit
        Me.C1DockingTab1.TabsSpacing = 5
        Me.C1DockingTab1.VisualStyle = C1.Win.C1Command.VisualStyle.Office2010Blue
        Me.C1DockingTab1.VisualStyleBase = C1.Win.C1Command.VisualStyle.Office2010Blue
        '
        'C1DockingTabPage1
        '
        Me.C1DockingTabPage1.CaptionVisible = true
        Me.C1DockingTabPage1.Controls.Add(Me.MzData1)
        Me.C1DockingTabPage1.Font = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.C1DockingTabPage1.Location = New System.Drawing.Point(3, 0)
        Me.C1DockingTabPage1.Name = "C1DockingTabPage1"
        Me.C1DockingTabPage1.Size = New System.Drawing.Size(467, 323)
        Me.C1DockingTabPage1.TabIndex = 0
        Me.C1DockingTabPage1.Text = "门诊辅助"
        '
        'ToolBarReadCard1
        '
        Me.ToolBarReadCard1.AutoSize = true
        Me.ToolBarReadCard1.Dock = System.Windows.Forms.DockStyle.Top
        Me.ToolBarReadCard1.Font = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.ToolBarReadCard1.Location = New System.Drawing.Point(0, 0)
        Me.ToolBarReadCard1.Name = "ToolBarReadCard1"
        Me.ToolBarReadCard1.Size = New System.Drawing.Size(1146, 28)
        Me.ToolBarReadCard1.TabIndex = 43
        '
        'MzData1
        '
        Me.MzData1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.MzData1.Location = New System.Drawing.Point(0, 23)
        Me.MzData1.Margin = New System.Windows.Forms.Padding(0)
        Me.MzData1.MzCfZxType = ZTHisEnum.MzCfZxType.正常
        Me.MzData1.Name = "MzData1"
        Me.MzData1.Size = New System.Drawing.Size(467, 300)
        Me.MzData1.TabIndex = 0
        '
        'Ys_Cf2
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6!, 12!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1146, 542)
        Me.Controls.Add(Me.SplitContainer1)
        Me.Controls.Add(Me.C1CommandDock1)
        Me.Controls.Add(Me.Panel1)
        Me.Controls.Add(Me.Panel2)
        Me.Controls.Add(Me.ToolBarReadCard1)
        Me.Icon = CType(resources.GetObject("$this.Icon"),System.Drawing.Icon)
        Me.Name = "Ys_Cf2"
        Me.ShowInTaskbar = false
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "处方录入"
        CType(Me.C1Holder1,System.ComponentModel.ISupportInitialize).EndInit
        Me.Panel2.ResumeLayout(false)
        Me.Panel2.PerformLayout
        Me.Panel1.ResumeLayout(false)
        Me.TableLayoutPanel1.ResumeLayout(false)
        Me.TableLayoutPanel1.PerformLayout
        Me.GroupBox1.ResumeLayout(false)
        Me.GroupBox1.PerformLayout
        CType(Me.C1NumericEdit1,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.C1Combo2,System.ComponentModel.ISupportInitialize).EndInit
        Me.SplitContainer1.Panel1.ResumeLayout(false)
        Me.SplitContainer1.Panel2.ResumeLayout(false)
        CType(Me.SplitContainer1,System.ComponentModel.ISupportInitialize).EndInit
        Me.SplitContainer1.ResumeLayout(false)
        CType(Me.C1TrueDBGrid1,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.C1TrueDBGrid2,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.C1CommandDock1,System.ComponentModel.ISupportInitialize).EndInit
        Me.C1CommandDock1.ResumeLayout(false)
        CType(Me.C1DockingTab1,System.ComponentModel.ISupportInitialize).EndInit
        Me.C1DockingTab1.ResumeLayout(false)
        Me.C1DockingTabPage1.ResumeLayout(false)
        Me.ResumeLayout(false)
        Me.PerformLayout

End Sub
    Friend WithEvents C1Holder1 As C1.Win.C1Command.C1CommandHolder
    Friend WithEvents Panel2 As System.Windows.Forms.Panel
    Friend WithEvents T_Line2 As System.Windows.Forms.Label
    Friend WithEvents C1Command1 As C1.Win.C1Command.C1Command
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents T_Line1 As System.Windows.Forms.Label
    Friend WithEvents Comm2 As System.Windows.Forms.Button
    Friend WithEvents Comm1 As System.Windows.Forms.Button
    Friend WithEvents Comm3 As System.Windows.Forms.Button
    Friend WithEvents C1Command4 As C1.Win.C1Command.C1Command
    Friend WithEvents C1Command5 As C1.Win.C1Command.C1Command
    Friend WithEvents C1Command9 As C1.Win.C1Command.C1Command
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents comm4 As System.Windows.Forms.Button
    Friend WithEvents C1Command7 As C1.Win.C1Command.C1Command
    Friend WithEvents T_Label5 As System.Windows.Forms.Label
    Friend WithEvents C1TrueDBGrid1 As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents SplitContainer1 As System.Windows.Forms.SplitContainer
    Friend WithEvents C1TrueDBGrid2 As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents TableLayoutPanel1 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents GroupBox1 As System.Windows.Forms.GroupBox
    Friend WithEvents C1ToolBar1 As C1.Win.C1Command.C1ToolBar
    Friend WithEvents C1CommandLink1 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents C1NumericEdit1 As C1.Win.C1Input.C1NumericEdit
    Friend WithEvents Label8 As System.Windows.Forms.Label
    Friend WithEvents RadioButton4 As System.Windows.Forms.RadioButton
    Friend WithEvents RadioButton1 As System.Windows.Forms.RadioButton
    Friend WithEvents C1ToolBar4 As C1.Win.C1Command.C1ToolBar
    Friend WithEvents C1CommandLink7 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents C1CommandLink8 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents C1CommandLink9 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Label18 As System.Windows.Forms.Label
    Friend WithEvents Label11 As System.Windows.Forms.Label
    Friend WithEvents C1ToolBar3 As C1.Win.C1Command.C1ToolBar
    Friend WithEvents C1CommandLink5 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents lblMz_Code As System.Windows.Forms.Label
    Friend WithEvents C1Combo2 As C1.Win.C1List.C1Combo
    Friend WithEvents Label20 As System.Windows.Forms.Label
    Friend WithEvents comm5 As System.Windows.Forms.Button
    Friend WithEvents FbDate As CustomControl.MyDateEdit
    Friend WithEvents BtnPrintSqd As Button
    Friend WithEvents ComboJb1 As ZTHisControl.ComboJb
    Friend WithEvents ToolBarReadCard1 As ZTHisPublicForm.ToolBarReadCard
    Friend WithEvents DateRyBirthday As CustomControl.MyDateEdit
    Friend WithEvents BtnSaveTemplate As Button
    Friend WithEvents C1CommandDock1 As C1.Win.C1Command.C1CommandDock
    Friend WithEvents C1DockingTab1 As C1.Win.C1Command.C1DockingTab
    Friend WithEvents C1DockingTabPage1 As C1.Win.C1Command.C1DockingTabPage
    Friend WithEvents MzData1 As ZTHisOutpatient.MzData
    Friend WithEvents XmTextBox As CustomControl.MyTextBox
    Friend WithEvents SfzhTextBox As CustomControl.MyTextBox
    Friend WithEvents SingleSex1 As ZTHisControl.SingleSex
    Friend WithEvents NlC1NumericEdit As CustomControl.MyNumericEdit
    Friend WithEvents AddressTextBox As CustomControl.MyTextBox
    Friend WithEvents YlCodeTextBox As CustomControl.MyTextBox
    Friend WithEvents ComboKs1 As ZTHisControl.ComboKs
    Friend WithEvents ComboBxlb1 As ZTHisControl.ComboBxlb
    Friend WithEvents ComboYf1 As ZTHisControl.ComboYf
    Friend WithEvents TxtTel As CustomControl.MyTextBox
    Friend WithEvents ComboYs1 As ZTHisControl.ComboYs
    Friend WithEvents lblHzName As Label
    Friend WithEvents ComDiagType As ZTHisControl.SingleZXY_DIAGNOSE_MARK
    Friend WithEvents SingleMzCfZxType1 As ZTHisControl.SingleMzCfZxType
    Friend WithEvents lblBalc As Label
    Friend WithEvents NumAgeDay As CustomControl.MyNumericEdit
    Friend WithEvents comboIdentityType As ZTHisControl.ComboCommon
    Friend WithEvents BtnAI As Sunny.UI.UISymbolButton
End Class
