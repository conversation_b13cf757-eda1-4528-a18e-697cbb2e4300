﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="0" />
    <DataSources isList="true" count="0" />
    <Relations isList="true" count="0" />
    <Report isRef="0" />
    <Variables isList="true" count="110">
      <value>,名称,名称,名称,System.String,,False,False,False</value>
      <value>,日期,日期,System.String,,False,False</value>
      <value>,个人编号,个人编号,System.String,,False,False</value>
      <value>,姓名,姓名,System.String,,False,False</value>
      <value>,性别,性别,System.String,,False,False</value>
      <value>,年龄,年龄,System.String,,False,False</value>
      <value>,人员类别,人员类别,System.String,,False,False</value>
      <value>,工作单位,工作单位,System.String,,False,False</value>
      <value>,账户余额,账户余额,System.String,,False,False</value>
      <value>,待遇类别,待遇类别,System.String,,False,False</value>
      <value>,公务员类别,公务员类别,System.String,,False,False</value>
      <value>,医疗类别,医疗类别,System.String,,False,False</value>
      <value>,就诊医院,就诊医院,System.String,,False,False</value>
      <value>,疾病诊断,疾病诊断,System.String,,False,False</value>
      <value>,入院日期,入院日期,System.String,,False,False</value>
      <value>,出院日期,出院日期,System.String,,False,False</value>
      <value>,本年住院次数,本年住院次数,System.String,,False,False</value>
      <value>,押金金额,押金金额,System.String,,False,False</value>
      <value>,截止上次本年医疗费总额,截止上次本年医疗费总额,System.String,,False,False</value>
      <value>,截止上次本年住院符合基本医疗费用累计,截止上次本年住院符合基本医疗费用累计,System.String,,False,False</value>
      <value>,截止上次本年统筹基金支付累计,截止上次本年统筹基金支付累计,System.String,,False,False</value>
      <value>,本次医疗费用总额,本次医疗费用总额,System.String,,False,False</value>
      <value>,本次符合基本医疗费用合计,本次符合基本医疗费用合计,System.String,,False,False</value>
      <value>,起付标准,起付标准,System.String,,False,False</value>
      <value>,转诊先自付,转诊先自付,System.String,,False,False</value>
      <value>,本次不符合基本医疗费用合计,本次不符合基本医疗费用合计,System.String,,False,False</value>
      <value>,丙类自费,丙类自费,System.String,,False,False</value>
      <value>,超限价自费,超限价自费,System.String,,False,False</value>
      <value>,蒙中药报销比例1,蒙中药报销比例1,System.String,,False,False</value>
      <value>,蒙中药报销比例2,蒙中药报销比例2,System.String,,False,False</value>
      <value>,蒙中药报销比例3,蒙中药报销比例3,System.String,,False,False</value>
      <value>,分段基金支付1,分段基金支付1,System.String,,False,False</value>
      <value>,分段基金支付2,分段基金支付2,System.String,,False,False</value>
      <value>,分段基金支付3,分段基金支付3,System.String,,False,False</value>
      <value>,分段公务员补助1,分段公务员补助1,System.String,,False,False</value>
      <value>,分段公务员补助2,分段公务员补助2,System.String,,False,False</value>
      <value>,分段公务员补助3,分段公务员补助3,System.String,,False,False</value>
      <value>,蒙中药分段个人自付1,蒙中药分段个人自付1,System.String,,False,False</value>
      <value>,蒙中药分段个人自付2,蒙中药分段个人自付2,System.String,,False,False</value>
      <value>,蒙中药分段个人自付3,蒙中药分段个人自付3,System.String,,False,False</value>
      <value>,超大额封顶线部分,超大额封顶线部分,System.String,,False,False</value>
      <value>,超大额封顶线公务员补助,超大额封顶线公务员补助,System.String,,False,False</value>
      <value>,超大额封顶线个人自付,超大额封顶线个人自付,System.String,,False,False</value>
      <value>,统筹基金支付,统筹基金支付,System.String,,False,False</value>
      <value>,大额基金支付,大额基金支付,System.String,,False,False</value>
      <value>,公务员补助支付,公务员补助支付,System.String,,False,False</value>
      <value>,个人账户支付,个人账户支付,System.String,,False,False</value>
      <value>,个人自付,个人自付,System.String,,False,False</value>
      <value>,本次报销总额,本次报销总额,System.String,,False,False</value>
      <value>,现金支付,现金支付,System.String,,False,False</value>
      <value>,本次报销总额大写,本次报销总额大写,System.String,,False,False</value>
      <value>,合计,合计,System.String,,False,False</value>
      <value>,年度报销累计,年度报销累计,System.String,,False,False</value>
      <value>,统筹基金支付累计,统筹基金支付累计,System.String,,False,False</value>
      <value>,大额基金支付累计,大额基金支付累计,System.String,,False,False</value>
      <value>,超大额封顶线公务员补助累计,超大额封顶线公务员补助累计,System.String,,False,False</value>
      <value>,住院单号,住院单号,System.String,,False,False</value>
      <value>,编号,编号,System.String,,False,False</value>
      <value>,扶贫商业补充保险,扶贫商业补充保险,System.String,,False,False</value>
      <value>,民政商业保险,民政商业保险,System.String,,False,False</value>
      <value>,民政医疗救助,民政医疗救助,System.String,,False,False</value>
      <value>,政府大病兜底保证,政府大病兜底保证,System.String,,False,False</value>
      <value>,本次医疗蒙中医药及蒙中医诊疗项目费用总额,本次医疗蒙中医药及蒙中医诊疗项目费用总额,System.String,,False,False</value>
      <value>,本次医疗蒙中医药及蒙中医诊疗项目费用占比,本次医疗蒙中医药及蒙中医诊疗项目费用占比,System.String,,False,False</value>
      <value>,蒙中医药进入分段费用总额1,蒙中医药进入分段费用总额1,System.String,,False,False</value>
      <value>,蒙中医药进入分段费用总额2,蒙中医药进入分段费用总额2,System.String,,False,False</value>
      <value>,蒙中医药进入分段费用总额3,蒙中医药进入分段费用总额3,System.String,,False,False</value>
      <value>,蒙中医药分段基金支付1,蒙中医药分段基金支付1,System.String,,False,False</value>
      <value>,蒙中医药分段基金支付2,蒙中医药分段基金支付2,System.String,,False,False</value>
      <value>,蒙中医药分段基金支付3,蒙中医药分段基金支付3,System.String,,False,False</value>
      <value>,西医药进入分段费用总额1,西医药进入分段费用总额1,System.String,,False,False</value>
      <value>,西医药进入分段费用总额2,西医药进入分段费用总额2,System.String,,False,False</value>
      <value>,西医药进入分段费用总额3,西医药进入分段费用总额3,System.String,,False,False</value>
      <value>,西医药报销比例1,西医药报销比例1,System.String,,False,False</value>
      <value>,西医药报销比例2,西医药报销比例2,System.String,,False,False</value>
      <value>,西医药报销比例3,西医药报销比例3,System.String,,False,False</value>
      <value>,西医药分段基金支付1,西医药分段基金支付1,System.String,,False,False</value>
      <value>,西医药分段基金支付2,西医药分段基金支付2,System.String,,False,False</value>
      <value>,西医药分段基金支付3,西医药分段基金支付3,System.String,,False,False</value>
      <value>,西医药分段个人自付1,西医药分段个人自付1,System.String,,False,False</value>
      <value>,西医药分段个人自付2,西医药分段个人自付2,System.String,,False,False</value>
      <value>,西医药分段个人自付3,西医药分段个人自付3,System.String,,False,False</value>
      <value>,截止上次本年个人分段自付支付累计,截止上次本年个人分段自付支付累计,System.String,,False,False</value>
      <value>,本次费用个人分段支付合计,本次费用个人分段支付合计,System.String,,False,False</value>
      <value>,大病进入分段费用总额1,大病进入分段费用总额1,System.String,,False,False</value>
      <value>,大病进入分段费用总额2,大病进入分段费用总额2,System.String,,False,False</value>
      <value>,大病进入分段费用总额3,大病进入分段费用总额3,System.String,,False,False</value>
      <value>,大病进入分段费用总额4,大病进入分段费用总额4,System.String,,False,False</value>
      <value>,大病报销比例1,大病报销比例1,System.String,,False,False</value>
      <value>,大病报销比例2,大病报销比例2,System.String,,False,False</value>
      <value>,大病报销比例3,大病报销比例3,System.String,,False,False</value>
      <value>,大病报销比例4,大病报销比例4,System.String,,False,False</value>
      <value>,大病分段基金支付1,大病分段基金支付1,System.String,,False,False</value>
      <value>,大病分段基金支付2,大病分段基金支付2,System.String,,False,False</value>
      <value>,大病分段基金支付3,大病分段基金支付3,System.String,,False,False</value>
      <value>,大病分段基金支付4,大病分段基金支付4,System.String,,False,False</value>
      <value>,超最高支付限额个人自付,超最高支付限额个人自付,System.String,,False,False</value>
      <value>,大病基金支付,大病基金支付,System.String,,False,False</value>
      <value>,大病基金支付累计,大病基金支付累计,System.String,,False,False</value>
      <value>,截止上次本年住院类民政救助支付累计,截止上次本年住院类民政救助支付累计,System.String,,False,False</value>
      <value>,截止上次民政救助住院类符合基本医疗累计,截止上次民政救助住院类符合基本医疗累计,System.String,,False,False</value>
      <value>,大病分段个人自付1,大病分段个人自付1,System.String,,False,False</value>
      <value>,大病分段个人自付2,大病分段个人自付2,System.String,,False,False</value>
      <value>,大病分段个人自付3,大病分段个人自付3,System.String,,False,False</value>
      <value>,大病分段个人自付4,大病分段个人自付4,System.String,,False,False</value>
      <value>,其中蒙中医药符合基本医疗费用,其中蒙中医药符合基本医疗费用,System.String,,False,False</value>
      <value>,自费费用,自费费用,System.String,,False,False</value>
      <value>,其中西医药符合基本医疗费用,其中西医药符合基本医疗费用,System.String,,False,False</value>
      <value>,乙类自理,乙类自理,System.String,,False,False</value>
      <value>,进入大病基金分段支付起付标准,进入大病基金分段支付起付标准,System.String,,False,False</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="2" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="387">
        <Text2 Ref="3" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0.05,2.39,1.87,0.76</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text2</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>个人编号</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text2>
        <Text3 Ref="4" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>5.93,2.59,0.76,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text3</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>姓名</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text3>
        <Text4 Ref="5" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>9.7,2.53,1.1,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text4</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>性别</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text4>
        <Text5 Ref="6" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>12.16,2.5,0.65,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text5</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>年龄</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text5>
        <Text6 Ref="7" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>14.42,2.5,1.52,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text6</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>人员类别</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text6>
        <Text7 Ref="8" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0.06,3.34,1.84,0.7</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text7</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>工作单位</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text7>
        <Text8 Ref="9" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>10.09,3.46,1.52,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text8</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>待遇类别</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text8>
        <Text9 Ref="10" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>14.37,3.2,1.52,0.9</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text9</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>公务员类别</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text9>
        <Text10 Ref="11" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0.04,4.28,1.94,0.62</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text10</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>医疗类别</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text10>
        <Text11 Ref="12" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>5.84,4.3,1.52,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text11</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>就诊医院</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text11>
        <Text12 Ref="13" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>11.33,4.34,1.52,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text12</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>疾病诊断</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text12>
        <Text13 Ref="14" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0.06,5.12,1.96,0.7</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text13</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>入院日期</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text13>
        <Text14 Ref="15" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>5.83,5.25,1.52,0.46</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text14</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>出院日期</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text14>
        <Text15 Ref="16" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>11.01,5.21,2,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text15</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>本年住院次数</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text15>
        <Text16 Ref="17" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>15.24,5.26,1.52,0.43</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text16</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>押金金额</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text16>
        <Text17 Ref="18" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0.08,5.91,2.33,0.86</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text17</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>截止上次本年
医疗费总额</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text17>
        <Text18 Ref="19" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>5.96,6.06,2.99,0.73</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Width</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text18</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>截止上次本年住院符
合基本医疗费用累计				
</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
        </Text18>
        <Text19 Ref="20" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>13.75,6.03,2.29,0.72</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Width</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text19</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>截止上次本年统
筹基金支付累计		
</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
        </Text19>
        <Text20 Ref="21" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0.12,6.86,2.33,0.74</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text20</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>本次医疗费用
总额	
</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
        </Text20>
        <Text21 Ref="22" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>5.98,6.91,2.88,0.71</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text21</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>本次符合基本医疗费
用合计				
</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
        </Text21>
        <Text22 Ref="23" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>11.77,7.02,1.52,1.41</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text22</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>起付标准</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text22>
        <Text24 Ref="24" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0.02,8.93,2.43,0.66</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text24</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>本次不符合基本
医疗费用合计
</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
        </Text24>
        <Text25 Ref="25" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>5.77,9.09,1.52,0.4</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text25</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>其中:</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
        </Text25>
        <Text26 Ref="26" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>7.6,9.02,1.52,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text26</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>乙类自理:</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text26>
        <Text27 Ref="27" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>11.11,9.02,1.52,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text27</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>丙类自费:</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text27>
        <Text28 Ref="28" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>14.67,9.03,1.52,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text28</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>超限价自费</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text28>
        <Text29 Ref="29" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0.7,9.85,2.1,0.71</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text29</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>符合基本医疗
费用</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text29>
        <Text35 Ref="30" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0.02,9.73,0.75,3.12</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text35</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>统筹
基金
分段
计算
明细
</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text35>
        <Text56 Ref="31" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>6.51,3.32,1.5,0.64</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text56</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>账户余额</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text56>
        <Text57 Ref="32" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0.05,1.72,0.83,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text57</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>编号:</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
        </Text57>
        <Text58 Ref="33" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>6.34,1.75,1.52,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text58</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>住院单号:</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
        </Text58>
        <Text59 Ref="34" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>17.24,1.72,1.52,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text59</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>单位:元</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
        </Text59>
        <Text60 Ref="35" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0.92,20.69,1.91,0.57</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text60</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>初审人签字:</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text60>
        <Text61 Ref="36" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>6.71,20.61,1.78,0.59</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text61</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>复审人签字:</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text61>
        <Text62 Ref="37" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>12.74,20.75,1.94,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text62</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>领款人签字:</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text62>
        <Text63 Ref="38" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>2.61,21.31,2.11,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text63</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>    年    月    日	
</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text63>
        <Text64 Ref="39" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>8.7,21.3,2.11,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>93652a6ea611421188a6ec41248df72d</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text64</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>    年    月    日	
</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text64>
        <Text65 Ref="40" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>14.35,21.37,2.11,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>6cf962a16e7a46c89f1cceba59eb7665</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text65</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>    年    月    日	
</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text65>
        <Text66 Ref="41" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>12.67,1.62,1.52,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text66</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{日期}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text66>
        <Text67 Ref="42" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>2.14,2.55,3.48,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text67</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{个人编号}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text67>
        <Text68 Ref="43" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>7.03,2.5,2.59,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text68</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{姓名}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text68>
        <Text69 Ref="44" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>10.96,2.55,0.88,0.44</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text69</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{性别}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text69>
        <Text70 Ref="45" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>13.06,2.51,0.91,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text70</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{年龄}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text70>
        <Text71 Ref="46" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>16.27,2.5,2.51,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text71</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{人员类别}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text71>
        <Text72 Ref="47" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>2.1,3.36,4.15,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text72</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{工作单位}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text72>
        <Text73 Ref="48" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>8.12,3.44,1.69,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text73</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{账户余额}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text73>
        <Text74 Ref="49" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>11.88,3.45,2.2,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text74</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{待遇类别}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text74>
        <Text75 Ref="50" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>16.29,3.38,2.46,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text75</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{公务员类别}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text75>
        <Text76 Ref="51" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>2.17,4.32,3.4,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text76</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{医疗类别}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text76>
        <Text77 Ref="52" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>7.63,4.32,3.17,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text77</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{就诊医院}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text77>
        <Text78 Ref="53" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>13.45,4.33,5.28,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text78</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{疾病诊断}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text78>
        <Text79 Ref="54" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>2.11,5.19,3.45,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text79</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{入院日期}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text79>
        <Text80 Ref="55" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>7.7,5.17,3.08,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text80</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{出院日期}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text80>
        <Text81 Ref="56" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>13.37,5.21,1.7,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text81</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{本年住院次数}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text81>
        <Text82 Ref="57" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>16.98,5.23,1.75,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text82</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{押金金额}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text82>
        <Text83 Ref="58" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>2.82,6.16,2.73,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text83</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{截止上次本年医疗费总额}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text83>
        <Text84 Ref="59" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>9.26,6.19,3.99,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text84</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{截止上次本年住院符合基本医疗费用累计}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text84>
        <Text85 Ref="60" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>16.49,6.19,2.28,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text85</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{截止上次本年统筹基金支付累计}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text85>
        <Text86 Ref="61" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>2.74,6.95,2.85,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text86</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{本次医疗费用总额}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text86>
        <Text87 Ref="62" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>9.18,7,2.44,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text87</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{本次符合基本医疗费用合计}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text87>
        <Text88 Ref="63" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>13.41,7.05,1.64,1.41</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text88</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{起付标准}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text88>
        <Text89 Ref="64" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>17.2,7.05,1.52,1.41</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text89</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{转诊先自付}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text89>
        <Text90 Ref="65" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>2.84,9.02,2.74,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text90</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{本次不符合基本医疗费用合计}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text90>
        <Text91 Ref="66" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>9.26,9,1.73,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text91</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{乙类自理}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text91>
        <Text92 Ref="67" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>12.66,9.01,1.86,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text92</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{丙类自费}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text92>
        <Text93 Ref="68" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>16.17,9.03,2.54,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text93</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{超限价自费}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text93>
        <Text94 Ref="69" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0.8,10.71,1.9,0.7</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text94</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>起付线--15000
（含15000）</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text94>
        <Text95 Ref="70" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0.8,11.45,2,0.71</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text95</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>15000--50000
（含50000）</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text95>
        <Text96 Ref="71" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0.8,12.29,1.9,0.61</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text96</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>50000以上</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text96>
        <Text100 Ref="72" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>5.3,10.81,1.6,0.4</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text100</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{蒙中药报销比例1}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text100>
        <Text101 Ref="73" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>5.3,11.46,1.6,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text101</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{蒙中药报销比例2}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text101>
        <Text102 Ref="74" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>5.3,12.33,1.6,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text102</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{蒙中药报销比例3}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text102>
        <Text109 Ref="75" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>9,10.74,2,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text109</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{蒙中药分段个人自付1}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text109>
        <Text110 Ref="76" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>9.1,11.46,1.8,0.61</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text110</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{蒙中药分段个人自付2}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text110>
        <Text111 Ref="77" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>9.1,12.29,1.8,0.61</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text111</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{蒙中药分段个人自付3}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text111>
        <Text130 Ref="78" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>7.86,1.69,3.49,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text130</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{住院单号}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text130>
        <Text131 Ref="79" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0.92,1.67,4.49,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text131</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{编号}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text131>
        <Text23 Ref="80" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>15.18,7.09,1.74,1.4</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text23</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>转诊先自付</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text23>
        <Text141 Ref="81" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0.1,7.8,2.53,0.94</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>570ff397bdb54981b96185a981d86efd</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text141</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>本次医疗蒙中医药
及蒙中医诊疗项目
费用总额</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
        </Text141>
        <Text142 Ref="82" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>2.8,7.9,2.73,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>82d7c948c0c441de884264048cf7a4e4</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text142</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{本次医疗蒙中医药及蒙中医诊疗项目费用总额}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text142>
        <Text140 Ref="83" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>6,7.8,2.83,0.94</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>71065d02996b4351ad8f7b825b9b7a1b</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text140</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>本次医疗蒙中医药及
蒙中医诊疗项目费用
占比</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
        </Text140>
        <Text144 Ref="84" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>9.2,8,2.44,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>bb82cbd8e24b42928a3d0126fb6297c0</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text144</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{本次医疗蒙中医药及蒙中医诊疗项目费用占比}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text144>
        <Text143 Ref="85" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>2.9,9.7,6.1,0.41</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>ce4a842b8ef84a7b88db5efccda4f02c</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text143</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>其中蒙中医药符合基本医疗费用</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text143>
        <Text145 Ref="86" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>11.1,9.7,5.7,0.41</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>4bb22b04663b402a89a222ff2172ca3a</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text145</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>其中西医药符合基本医疗费用</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text145>
        <Text146 Ref="87" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>2.9,10,2.4,0.61</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,6</Font>
          <Guid>e80fd02e1eec48319b1b104c574d051c</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text146</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>蒙中医药进入分
段费用总额</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text146>
        <Text147 Ref="88" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>5.3,10,1.6,0.61</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>3c93b92081d34ff29afebd64ef3c88bc</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text147</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>报销比例</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text147>
        <Text148 Ref="89" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>6.9,10,2.1,0.61</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>5e65bc2084264f09b7019450ce8a1fe1</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text148</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>分段基金支付</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text148>
        <Text149 Ref="90" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>9,10,2,0.61</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>b4aa3119a60a41f58acbe4ffa5ba8994</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text149</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>分段个人自付</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text149>
        <Text150 Ref="91" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>11.1,10.1,2,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,6</Font>
          <Guid>32a84f23866b4373a41060e5ade110c2</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text150</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>西医药进入分
段费用总额</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text150>
        <Text151 Ref="92" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>13.2,10.1,1.4,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>f38872112f0f4dfbbc5fdc8c6b498c89</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text151</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>报销比例</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text151>
        <Text152 Ref="93" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>14.8,10.1,1.9,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>a6252e20e90e40a5ad41e72e87da6052</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text152</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>分段基金支付</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text152>
        <Text153 Ref="94" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>16.8,10.1,2,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>a8b85d8cf25c484f89b7ff2ef047bd61</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text153</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>分段个人自付</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text153>
        <Text31 Ref="95" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>16.8,10.8,1.9,0.4</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>3432bd187d5046a2b551c157d17043a8</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text31</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{西医药分段个人自付1}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text31>
        <Text34 Ref="96" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>16.8,11.5,1.9,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>b21eaf6026b64380a9c2813c1b49c4d4</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text34</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{西医药分段个人自付2}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text34>
        <Text154 Ref="97" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>16.8,12.3,1.9,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>b78b030102014a168708c1d5cf58267e</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text154</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{西医药分段个人自付3}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text154>
        <Text155 Ref="98" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>13.2,10.8,1.4,0.4</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>c5f019b354274daaae5a5e8cb8edce1a</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text155</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{西医药报销比例1}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text155>
        <Text156 Ref="99" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>13.2,11.5,1.4,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>b96f7b3888d544e9adb3271c3c3505f7</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text156</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{西医药报销比例2}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text156>
        <Text157 Ref="100" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>13.2,12.3,1.4,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>afe37509e8ba4bdea98cfc73826fefa8</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text157</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{西医药报销比例3}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text157>
        <Text159 Ref="101" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>2.9,10.8,2.4,0.4</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>03cea89820834d89ac7157a0648535e4</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text159</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{蒙中医药进入分段费用总额1}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text159>
        <Text158 Ref="102" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>2.9,11.5,2.3,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>d97efb051a1d4b809d3fd2f36c8994cb</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text158</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{蒙中医药进入分段费用总额2}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text158>
        <Text160 Ref="103" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>2.9,12.3,2.3,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>ed02b073cc3c4f5e94265218623c4bda</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text160</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{蒙中医药进入分段费用总额3}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text160>
        <Text161 Ref="104" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>11.1,10.8,2,0.4</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>067f49296c404aa08fceb7905742abe1</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text161</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{西医药进入分段费用总额1}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text161>
        <Text162 Ref="105" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>11.1,11.5,2,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>3c814b3b52d7418a8387d140fd3dd4ce</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text162</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{西医药进入分段费用总额2}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text162>
        <Text163 Ref="106" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>11.1,12.3,2,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>ca75e101a9344911be8870c4f417e26a</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text163</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{西医药进入分段费用总额3}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text163>
        <Text164 Ref="107" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>7,10.8,1.8,0.4</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>02d08e5790334db09c6e3fa4be70b1a3</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text164</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{蒙中医药分段基金支付1}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text164>
        <Text165 Ref="108" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>7,11.5,1.8,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>2eb58735f3444e3d8a146f35fd9d4426</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text165</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{蒙中医药分段基金支付2}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text165>
        <Text166 Ref="109" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>7,12.3,1.8,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>a9a2c51c0a9e4d339b51803c87f80365</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text166</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{蒙中医药分段基金支付3}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text166>
        <Text167 Ref="110" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>14.8,10.8,1.9,0.4</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>5820daedf559401eb94927444f550cf2</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text167</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{西医药分段基金支付1}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text167>
        <Text168 Ref="111" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>14.8,11.5,1.9,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>2ddef9ad68074bafb24d62ec664c3369</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text168</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{西医药分段基金支付2}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text168>
        <Text169 Ref="112" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>14.8,12.3,1.9,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>614d7bc1fd624f21860de9f5f9570460</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text169</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{西医药分段基金支付3}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text169>
        <Text32 Ref="113" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,13.05,5,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>9f4be278e3094e80917df71191aefeae</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text32</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>截止上次本年个人分段自付支付累计</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text32>
        <Text33 Ref="114" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>7.1,13.05,3.6,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>86d7625ea8074c5e977e9dffbbb7738e</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text33</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>本次费用个人分段支付合计</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text33>
        <Text103 Ref="115" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>12.6,13.05,4.6,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>0ba27884d35f4268895b1b1f4e158df7</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text103</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>进入大病基金分段支付起付标准</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text103>
        <Text104 Ref="116" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>5.1,13.05,2,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text104</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{截止上次本年个人分段自付支付累计}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
        </Text104>
        <Text105 Ref="117" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>10.7,13.05,1.8,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>5b39521eff0f41789db3619b29168733</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text105</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{本次费用个人分段支付合计}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
        </Text105>
        <Text106 Ref="118" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>17.2,13.05,1.5,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>6ae862d20fcb4582847be287c1a02ae8</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text106</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{进入大病基金分段支付起付标准}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
        </Text106>
        <Text107 Ref="119" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>1.2,13.6,2.1,0.7</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>d2540ae890164cac96ea9b6e9b356762</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text107</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>符合基本医疗
费用</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text107>
        <Text108 Ref="120" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>3.4,13.6,2.9,0.7</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>63b91db9eba34d76babff58c4edf0d0a</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text108</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>进入分段费用总额</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text108>
        <Text170 Ref="121" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,13.4,1.1,3.8</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>cb80921a9dda42b98527d469672f4cab</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text170</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>大病
基金
分段
计算
明细</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text170>
        <Text171 Ref="122" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>1.3,14.4,1.9,0.7</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>e182c0ae28cd4495b6558aacb1a0fa98</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text171</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>14000-30000
（含30000）</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text171>
        <Text172 Ref="123" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>1.3,15.2,2,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>35bac288691542b99e3deae445364f12</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text172</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>30000--50000
（含50000）</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text172>
        <Text173 Ref="124" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>1.3,15.9,1.9,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>60705f59dd424a3aaed86c2b0733c873</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text173</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>50000-80000
（含80000）</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text173>
        <Text174 Ref="125" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>3.4,14.4,2.9,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>e598fbfab97a49db8b8f10d4cdb8d11c</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text174</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{大病进入分段费用总额1}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text174>
        <Text175 Ref="126" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>3.4,15.2,2.9,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>8ddcf72f13594c4583a6372b7d253e46</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text175</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{大病进入分段费用总额2}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text175>
        <Text176 Ref="127" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>3.4,16,2.9,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>498a5f1e8c4d49cfaf907d3913ab90ec</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text176</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{大病进入分段费用总额3}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text176>
        <Text177 Ref="128" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>6.4,14.5,2.4,0.4</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>fdbbc313caa2480f8680e8a87e3174c1</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text177</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{大病报销比例1}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text177>
        <Text178 Ref="129" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>6.4,16,2.4,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>1cca26d74f0a4b5ebc6bae2f8484b1c8</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text178</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{大病报销比例3}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text178>
        <Text179 Ref="130" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>11.4,14.4,2.1,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>4e0da7101f4c4dfa9e32d629d086e1b1</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text179</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{大病分段个人自付1}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text179>
        <Text180 Ref="131" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>11.4,15.2,2.1,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>dd4ef4189f3e430da3ebe58fda7e937f</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text180</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{大病分段个人自付2}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text180>
        <Text181 Ref="132" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>11.4,16,2.1,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>e58731e9a697418fac1902ffa50b95b2</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text181</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{大病分段个人自付3}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text181>
        <Text184 Ref="133" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>6.4,13.6,2.4,0.7</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>9e4f99f2895a49b8978d3c4b2761c771</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text184</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>报销比例</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text184>
        <Text185 Ref="134" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>11.4,13.6,2.2,0.7</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>d5ccf73aac7b4cc29ddc60633c92314f</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text185</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>分段个人自付</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text185>
        <Text189 Ref="135" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>8.9,15.2,2.4,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>fad1fc99e243489c8fbadad28e7a9a72</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text189</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{大病分段基金支付2}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text189>
        <Text190 Ref="136" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>8.9,16,2.4,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>990ba76ab0a247f1aea49ffce4e08e89</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text190</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{大病分段基金支付3}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text190>
        <Text192 Ref="137" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>13.6,13.6,1.6,3.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>5422f181d5b7445d9081e13e22e0474c</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text192</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>个人负担部分</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text192>
        <Text193 Ref="138" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>15.3,13.6,1.6,0.7</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>a0b605a74d414bcb93f67b842cf7328f</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text193</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>乙类自理</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text193>
        <Text194 Ref="139" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>6.4,15.2,2.4,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>0281591305b74312b2021efd06e9ecdb</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text194</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{大病报销比例2}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text194>
        <Text195 Ref="140" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>8.8,13.6,2.5,0.7</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>e31c487ba1a24f4c835ab2896b8d6517</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text195</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>分段基金支付</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text195>
        <Text196 Ref="141" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>8.9,14.5,2.4,0.4</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>c6d3a2fc4717466290348dd5fb2b0647</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text196</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{大病分段基金支付1}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text196>
        <Text197 Ref="142" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>1.3,16.6,1.9,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>265faf3a21d341079d19772d7695756e</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text197</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>80000以上</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text197>
        <Text198 Ref="143" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>3.4,16.7,2.9,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>28454f5fedd54ce2becbfb1f197e6258</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text198</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{大病进入分段费用总额4}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text198>
        <Text199 Ref="144" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>6.4,16.7,2.4,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>cc789456439249e3935f62363b870494</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text199</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{大病报销比例4}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text199>
        <Text200 Ref="145" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>11.4,16.7,2.1,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>b693ab2df2934be5997368e2afbd4608</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text200</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{大病分段个人自付3}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text200>
        <Text202 Ref="146" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>8.9,16.7,2.4,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>88951a1a7f23468c936d2c83c67baafb</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text202</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{大病分段基金支付4}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text202>
        <Text203 Ref="147" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>15.3,14.4,1.6,0.7</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>8842c256ef1c400db3c3c8846072c6d3</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text203</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>自费费用</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text203>
        <Text204 Ref="148" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>15.3,15.2,1.6,0.7</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>9396ce05031a41328564c7a8aa698555</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text204</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>个人自付</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text204>
        <Text205 Ref="149" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>15.3,16.1,1.6,1.1</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>5b9218f2508a4c8582a2ade1f6680e66</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text205</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>合计:</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text205>
        <Text182 Ref="150" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>17,13.7,1.7,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>61943401c0c244efaff9d09d9aae7c07</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text182</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{乙类自理}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text182>
        <Text183 Ref="151" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>17,14.45,1.8,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>a4d4607621f747eda0c9065230a20994</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text183</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{自费费用}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text183>
        <Text186 Ref="152" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>17,15.21,1.8,0.71</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>d32218e4c26046818a932db3372395fd</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text186</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{个人自付}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text186>
        <Text187 Ref="153" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>17,16.14,1.7,1.01</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>11475f43b4f64399aac7b53b4fd5d451</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text187</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{合计}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text187>
        <Text188 Ref="154" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,17.2,3.5,0.7</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>7f2e576d04b34a0893a9aa3af61ee80c</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text188</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>超最高支付限额个人自付</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text188>
        <Text191 Ref="155" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>3.5,17.2,2.1,0.7</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>d4137e41dc614a5f8915adf0f4f74019</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text191</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{超最高支付限额个人自付}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text191>
        <Text201 Ref="156" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>5.7,17.2,2.2,0.7</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>89c7ebec8cf6474d81ee2e4791d2df87</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text201</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>本次报销总额</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text201>
        <Text206 Ref="157" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>8,17.2,1.5,0.7</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>bae2b6b6b151417093239856a40a01d9</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text206</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{本次报销总额}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text206>
        <Text207 Ref="158" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>9.6,17.2,2.63,0.7</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>cd9bb073ba2c436b86da1af4ab64ad92</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text207</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>本次报销总额(大写)</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text207>
        <Text208 Ref="159" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>12.35,17.2,6.38,0.7</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>02cbe0c7e38d4c52b7ba6cea533cc474</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text208</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{本次报销总额大写}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text208>
        <Text36 Ref="160" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,18.1,2.5,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>563bb37d02b645d48154854398f37e7a</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text36</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>统筹基金支付累计</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text36>
        <Text37 Ref="161" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>2.55,18.1,1,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>f1118d73e29d4b879bbe9f524f62bc65</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text37</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{统筹基金支付累计}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text37>
        <Text38 Ref="162" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>3.55,18.1,2.5,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>9ba099cb9af64072b102584ac4f112da</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text38</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>大病基金支付累计</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text38>
        <Text39 Ref="163" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>6,18.1,1,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>91dd0c73981b408eaf74409e157b644d</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text39</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{大病基金支付累计}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text39>
        <Text45 Ref="164" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>7.05,18.1,1.49,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>99f1ae644365444783b68fcc6768a227</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text45</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>现金支付</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text45>
        <Text123 Ref="165" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>8.6,18.1,1.2,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>00f90d35a694450bbe08cefd2e3eac5d</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text123</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{现金支付}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text123>
        <Text40 Ref="166" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>13,18.01,1.9,1.49</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>ad8176253dc54a758c4571e0380c92f2</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text40</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>本次基金支付</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text40>
        <Text41 Ref="167" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>15.2,18.87,1.8,0.65</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>bf097f5e8f6c4160b2a8fd9e62b9f13b</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text41</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>大病基金支付</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text41>
        <Text115 Ref="168" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>17.1,18.1,1.5,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>bf6ba998bc364fd4a08acbff789482ee</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text115</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{统筹基金支付}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text115>
        <Text116 Ref="169" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>17.1,18.89,1.5,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>01f10b44fb3d4ad18d26e1a544dfb4c0</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text116</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{大病基金支付}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text116>
        <Text42 Ref="170" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,18.7,3.9,0.9</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>f1dd7fe9cb8e45cb8146c3dcc12ce7eb</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text42</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>截止上次本年住院类
民政救助支付累计</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text42>
        <Text43 Ref="171" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>4.2,18.9,1.8,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>3324c4ab07464f58945ebe806609e076</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text43</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{截止上次本年住院类民政救助支付累计}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text43>
        <Text44 Ref="172" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>6.4,18.7,4.2,0.9</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>8d1a4d70025c4fb19c0fe0a2c43a8368</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text44</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>截止上次本年住院类
民政救助支付累计</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text44>
        <Text46 Ref="173" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>10.8,18.9,1.8,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>8a142360473e48ea856d23512dd3113e</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text46</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{截止上次民政救助住院类符合基本医疗累计}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text46>
        <Text47 Ref="174" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>9.85,18.1,1.83,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>5f5cb806c9f54f439917dbe7b00a134d</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text47</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>年度支付累计</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text47>
        <Text48 Ref="175" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>11.7,18.1,1.23,0.55</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>264e03e89f38458db8dc36360b2814c3</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text48</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{年度报销累计}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text48>
        <Text49 Ref="176" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0.2,19.73,2.34,0.61</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>3b9795a2f33f4765a79ec01f5692bc8b</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text49</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>扶贫商业补充保险</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text49>
        <Text50 Ref="177" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>2.61,19.8,2.06,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>00e5888a9dd0470ea0dba21411a8e12e</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text50</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{扶贫商业补充保险}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text50>
        <Text51 Ref="178" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>4.8,19.7,1.8,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>9ea4ed106ed144b18e5a2f29c779dd3d</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text51</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>民政商业保险</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text51>
        <Text112 Ref="179" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>6.7,19.8,1.9,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>74f7c5d0d2df43f489c5fd4a9e7384a9</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text112</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{民政商业保险}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text112>
        <Text113 Ref="180" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>15,19.7,1.4,0.7</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>afe977872ee441c987e1859f33751939</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text113</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>政府大病
兜底保证</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text113>
        <Text114 Ref="181" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>8.8,19.7,2.8,0.7</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>6bf614965bec488cbd395ede2ee514b1</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text114</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>民政医疗救助</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text114>
        <Text117 Ref="182" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>16.5,19.7,1.6,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>d9a124db0023408e9691d85ea2be5c9a</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text117</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{政府大病兜底保证}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text117>
        <Text118 Ref="183" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>12.1,19.8,2.2,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>a4bf456b3b15415c946c6b4e737c7d77</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text118</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{民政医疗救助}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text118>
        <Text52 Ref="184" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>9,9.7,2,0.41</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>9f8a404b5ccb47138d07f959f04ef603</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text52</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{其中蒙中医药符合基本医疗费用}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text52>
        <Text53 Ref="185" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>16.7,9.7,2.1,0.41</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Guid>47b1d14f782d4452b6d6034a8606dd14</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text53</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{其中西医药符合基本医疗费用}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text53>
        <Text55 Ref="186" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>15,18.1,2,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text55</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>统筹基金支付</Text>
          <TextBrush>Black</TextBrush>
        </Text55>
        <ReportTitleBand1 Ref="187" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,18.8,0.8</ClientRectangle>
          <Components isList="true" count="1">
            <Text1 Ref="188" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>6.82,0,5.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text1</Name>
              <Page isRef="2" />
              <Parent isRef="187" />
              <Text>{名称}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text1>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>ReportTitleBand1</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
        </ReportTitleBand1>
        <HorizontalLinePrimitive1 Ref="189" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>0,2.3,18.8,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="190" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Name>HorizontalLinePrimitive1</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="191" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive1>
        <VerticalLinePrimitive1 Ref="192" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>0,2.3,0.0254,18.05</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="193" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>168bd8311aae458bbb9560ebf77ba1ab</Guid>
          <Name>VerticalLinePrimitive1</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="194" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive1>
        <StartPointPrimitive1 Ref="195" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>0,2.3,0,0</ClientRectangle>
          <Name>StartPointPrimitive1</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>168bd8311aae458bbb9560ebf77ba1ab</ReferenceToGuid>
        </StartPointPrimitive1>
        <EndPointPrimitive1 Ref="196" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>0,20.35,0,0</ClientRectangle>
          <Name>EndPointPrimitive1</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>168bd8311aae458bbb9560ebf77ba1ab</ReferenceToGuid>
        </EndPointPrimitive1>
        <HorizontalLinePrimitive2 Ref="197" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>0,3.2,18.8,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="198" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Name>HorizontalLinePrimitive2</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="199" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive2>
        <VerticalLinePrimitive2 Ref="200" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>18.8,2.3,0.0254,18.05</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="201" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>7015fe4a909241b9b0a758edad0cdb8a</Guid>
          <Name>VerticalLinePrimitive2</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="202" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive2>
        <StartPointPrimitive2 Ref="203" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>18.8,2.3,0,0</ClientRectangle>
          <Name>StartPointPrimitive2</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>7015fe4a909241b9b0a758edad0cdb8a</ReferenceToGuid>
        </StartPointPrimitive2>
        <EndPointPrimitive2 Ref="204" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>18.8,20.35,0,0</ClientRectangle>
          <Name>EndPointPrimitive2</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>7015fe4a909241b9b0a758edad0cdb8a</ReferenceToGuid>
        </EndPointPrimitive2>
        <VerticalLinePrimitive3 Ref="205" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>2.05,2.3,0.0254,3.6</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="206" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>b19207f24b2f401bae839152c398722d</Guid>
          <Name>VerticalLinePrimitive3</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="207" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive3>
        <StartPointPrimitive3 Ref="208" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>2.05,2.3,0,0</ClientRectangle>
          <Name>StartPointPrimitive3</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>b19207f24b2f401bae839152c398722d</ReferenceToGuid>
        </StartPointPrimitive3>
        <EndPointPrimitive3 Ref="209" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>2.05,5.9,0,0</ClientRectangle>
          <Name>EndPointPrimitive3</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>b19207f24b2f401bae839152c398722d</ReferenceToGuid>
        </EndPointPrimitive3>
        <VerticalLinePrimitive4 Ref="210" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>5.7,2.3,0.0254,0.9</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="211" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>449ffca869bf4b919207a70849c66624</Guid>
          <Name>VerticalLinePrimitive4</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="212" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive4>
        <StartPointPrimitive4 Ref="213" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>5.7,2.3,0,0</ClientRectangle>
          <Name>StartPointPrimitive4</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>449ffca869bf4b919207a70849c66624</ReferenceToGuid>
        </StartPointPrimitive4>
        <EndPointPrimitive4 Ref="214" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>5.7,3.2,0,0</ClientRectangle>
          <Name>EndPointPrimitive4</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>449ffca869bf4b919207a70849c66624</ReferenceToGuid>
        </EndPointPrimitive4>
        <VerticalLinePrimitive5 Ref="215" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>6.9,2.3,0.0254,0.9</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="216" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>ed90a0714dd94688947e0918f876ecd9</Guid>
          <Name>VerticalLinePrimitive5</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="217" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive5>
        <StartPointPrimitive5 Ref="218" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>6.9,2.3,0,0</ClientRectangle>
          <Name>StartPointPrimitive5</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>ed90a0714dd94688947e0918f876ecd9</ReferenceToGuid>
        </StartPointPrimitive5>
        <EndPointPrimitive5 Ref="219" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>6.9,3.2,0,0</ClientRectangle>
          <Name>EndPointPrimitive5</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>ed90a0714dd94688947e0918f876ecd9</ReferenceToGuid>
        </EndPointPrimitive5>
        <VerticalLinePrimitive6 Ref="220" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>9.65,2.3,0.0254,0.9</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="221" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>ae3d668dd285457faf191066f6ad61d6</Guid>
          <Name>VerticalLinePrimitive6</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="222" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive6>
        <StartPointPrimitive6 Ref="223" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>9.65,2.3,0,0</ClientRectangle>
          <Name>StartPointPrimitive6</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>ae3d668dd285457faf191066f6ad61d6</ReferenceToGuid>
        </StartPointPrimitive6>
        <EndPointPrimitive6 Ref="224" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>9.7,3.2,0,0</ClientRectangle>
          <Name>EndPointPrimitive6</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>ae3d668dd285457faf191066f6ad61d6</ReferenceToGuid>
        </EndPointPrimitive6>
        <VerticalLinePrimitive7 Ref="225" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>10.85,2.3,0.0254,0.9</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="226" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>1505fe22d3004949a335cd33818201d1</Guid>
          <Name>VerticalLinePrimitive7</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="227" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive7>
        <StartPointPrimitive7 Ref="228" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>10.85,2.3,0,0</ClientRectangle>
          <Name>StartPointPrimitive7</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>1505fe22d3004949a335cd33818201d1</ReferenceToGuid>
        </StartPointPrimitive7>
        <EndPointPrimitive7 Ref="229" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>10.85,3.2,0,0</ClientRectangle>
          <Name>EndPointPrimitive7</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>1505fe22d3004949a335cd33818201d1</ReferenceToGuid>
        </EndPointPrimitive7>
        <VerticalLinePrimitive8 Ref="230" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>12,2.3,0.0254,0.9</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="231" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>0fb939c850154397ad17f6ae4ca7a17a</Guid>
          <Name>VerticalLinePrimitive8</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="232" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive8>
        <StartPointPrimitive8 Ref="233" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>12,2.3,0,0</ClientRectangle>
          <Name>StartPointPrimitive8</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>0fb939c850154397ad17f6ae4ca7a17a</ReferenceToGuid>
        </StartPointPrimitive8>
        <EndPointPrimitive8 Ref="234" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>13.1,3.2,0,0</ClientRectangle>
          <Name>EndPointPrimitive8</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>0fb939c850154397ad17f6ae4ca7a17a</ReferenceToGuid>
        </EndPointPrimitive8>
        <VerticalLinePrimitive9 Ref="235" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>13,2.3,0.0254,0.9</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="236" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>b07a7db015d84f8296ff70d4029279e3</Guid>
          <Name>VerticalLinePrimitive9</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="237" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive9>
        <StartPointPrimitive9 Ref="238" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>13,2.3,0,0</ClientRectangle>
          <Name>StartPointPrimitive9</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>b07a7db015d84f8296ff70d4029279e3</ReferenceToGuid>
        </StartPointPrimitive9>
        <EndPointPrimitive9 Ref="239" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>14.2,3.2,0,0</ClientRectangle>
          <Name>EndPointPrimitive9</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>b07a7db015d84f8296ff70d4029279e3</ReferenceToGuid>
        </EndPointPrimitive9>
        <VerticalLinePrimitive10 Ref="240" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>14.15,2.3,0.0254,1.8</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="241" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>e2cd15a85ff44e9f967f6a8c67967d2a</Guid>
          <Name>VerticalLinePrimitive10</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="242" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive10>
        <StartPointPrimitive10 Ref="243" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>14.15,2.3,0,0</ClientRectangle>
          <Name>StartPointPrimitive10</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>e2cd15a85ff44e9f967f6a8c67967d2a</ReferenceToGuid>
        </StartPointPrimitive10>
        <EndPointPrimitive10 Ref="244" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>14.15,4.1,0,0</ClientRectangle>
          <Name>EndPointPrimitive10</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>e2cd15a85ff44e9f967f6a8c67967d2a</ReferenceToGuid>
        </EndPointPrimitive10>
        <VerticalLinePrimitive11 Ref="245" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>16.2,2.3,0.0254,1.8</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="246" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>0e73fea7a5b74f6baf66bfa65241b28b</Guid>
          <Name>VerticalLinePrimitive11</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="247" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive11>
        <StartPointPrimitive11 Ref="248" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>16.2,2.3,0,0</ClientRectangle>
          <Name>StartPointPrimitive11</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>0e73fea7a5b74f6baf66bfa65241b28b</ReferenceToGuid>
        </StartPointPrimitive11>
        <EndPointPrimitive11 Ref="249" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>16.2,4.1,0,0</ClientRectangle>
          <Name>EndPointPrimitive11</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>0e73fea7a5b74f6baf66bfa65241b28b</ReferenceToGuid>
        </EndPointPrimitive11>
        <HorizontalLinePrimitive3 Ref="250" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>0,4.1,18.8,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="251" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Name>HorizontalLinePrimitive3</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="252" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive3>
        <HorizontalLinePrimitive4 Ref="253" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>0,5,18.8,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="254" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Name>HorizontalLinePrimitive4</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="255" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive4>
        <HorizontalLinePrimitive5 Ref="256" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>0,5.9,18.8,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="257" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Name>HorizontalLinePrimitive5</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="258" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive5>
        <VerticalLinePrimitive12 Ref="259" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>9.86,3.2,0.0254,0.91</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="260" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>48419022ec16489ebb5cf0d491155d8f</Guid>
          <Name>VerticalLinePrimitive12</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="261" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive12>
        <StartPointPrimitive12 Ref="262" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>9.86,3.2,0,0</ClientRectangle>
          <Name>StartPointPrimitive12</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>48419022ec16489ebb5cf0d491155d8f</ReferenceToGuid>
        </StartPointPrimitive12>
        <EndPointPrimitive12 Ref="263" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>9.66,4.11,0,0</ClientRectangle>
          <Name>EndPointPrimitive12</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>48419022ec16489ebb5cf0d491155d8f</ReferenceToGuid>
        </EndPointPrimitive12>
        <VerticalLinePrimitive13 Ref="264" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>5.65,4.1,0.0254,4.7</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="265" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>c4407cb76e0640e4b3d9431d9b63a75f</Guid>
          <Name>VerticalLinePrimitive13</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="266" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive13>
        <StartPointPrimitive13 Ref="267" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>5.65,4.1,0,0</ClientRectangle>
          <Name>StartPointPrimitive13</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>c4407cb76e0640e4b3d9431d9b63a75f</ReferenceToGuid>
        </StartPointPrimitive13>
        <EndPointPrimitive13 Ref="268" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>5.65,8.8,0,0</ClientRectangle>
          <Name>EndPointPrimitive13</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>c4407cb76e0640e4b3d9431d9b63a75f</ReferenceToGuid>
        </EndPointPrimitive13>
        <VerticalLinePrimitive14 Ref="269" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>7.55,4.09,0.0254,1.8</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="270" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>9ea75a36916248f99eb127f200b9d8ca</Guid>
          <Name>VerticalLinePrimitive14</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="271" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive14>
        <StartPointPrimitive14 Ref="272" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>7.55,4.09,0,0</ClientRectangle>
          <Name>StartPointPrimitive14</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>9ea75a36916248f99eb127f200b9d8ca</ReferenceToGuid>
        </StartPointPrimitive14>
        <EndPointPrimitive14 Ref="273" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>7.55,5.89,0,0</ClientRectangle>
          <Name>EndPointPrimitive14</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>9ea75a36916248f99eb127f200b9d8ca</ReferenceToGuid>
        </EndPointPrimitive14>
        <VerticalLinePrimitive15 Ref="274" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>13.35,4.1,0.0254,4.7</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="275" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>d3b7e8751da647e8996b87e69c6984bc</Guid>
          <Name>VerticalLinePrimitive15</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="276" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive15>
        <StartPointPrimitive15 Ref="277" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>13.35,4.1,0,0</ClientRectangle>
          <Name>StartPointPrimitive15</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>d3b7e8751da647e8996b87e69c6984bc</ReferenceToGuid>
        </StartPointPrimitive15>
        <EndPointPrimitive15 Ref="278" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>13.35,8.8,0,0</ClientRectangle>
          <Name>EndPointPrimitive15</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>d3b7e8751da647e8996b87e69c6984bc</ReferenceToGuid>
        </EndPointPrimitive15>
        <VerticalLinePrimitive16 Ref="279" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>15.1,5,0.0254,0.9</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="280" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>e5b65bb9bb5c437b945c940daf8b91e9</Guid>
          <Name>VerticalLinePrimitive16</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="281" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive16>
        <StartPointPrimitive16 Ref="282" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>15.1,5,0,0</ClientRectangle>
          <Name>StartPointPrimitive16</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>e5b65bb9bb5c437b945c940daf8b91e9</ReferenceToGuid>
        </StartPointPrimitive16>
        <EndPointPrimitive16 Ref="283" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>15.1,5.9,0,0</ClientRectangle>
          <Name>EndPointPrimitive16</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>e5b65bb9bb5c437b945c940daf8b91e9</ReferenceToGuid>
        </EndPointPrimitive16>
        <VerticalLinePrimitive17 Ref="284" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>16.9,5,0.0254,0.9</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="285" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>09048c5300fe4d82bae2d5ee0b237cd1</Guid>
          <Name>VerticalLinePrimitive17</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="286" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive17>
        <StartPointPrimitive17 Ref="287" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>16.9,5,0,0</ClientRectangle>
          <Name>StartPointPrimitive17</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>09048c5300fe4d82bae2d5ee0b237cd1</ReferenceToGuid>
        </StartPointPrimitive17>
        <EndPointPrimitive17 Ref="288" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>16.9,5.9,0,0</ClientRectangle>
          <Name>EndPointPrimitive17</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>09048c5300fe4d82bae2d5ee0b237cd1</ReferenceToGuid>
        </EndPointPrimitive17>
        <VerticalLinePrimitive18 Ref="289" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>2.75,5.9,0.0254,2.9</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="290" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>9cf8dd166a254d97882a3d33b415aa79</Guid>
          <Name>VerticalLinePrimitive18</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="291" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive18>
        <StartPointPrimitive18 Ref="292" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>2.75,5.9,0,0</ClientRectangle>
          <Name>StartPointPrimitive18</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>9cf8dd166a254d97882a3d33b415aa79</ReferenceToGuid>
        </StartPointPrimitive18>
        <EndPointPrimitive18 Ref="293" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>2.75,8.8,0,0</ClientRectangle>
          <Name>EndPointPrimitive18</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>9cf8dd166a254d97882a3d33b415aa79</ReferenceToGuid>
        </EndPointPrimitive18>
        <HorizontalLinePrimitive6 Ref="294" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>0,6.8,18.8,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="295" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Name>HorizontalLinePrimitive6</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="296" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive6>
        <HorizontalLinePrimitive7 Ref="297" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>0,8.8,18.8,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="298" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Name>HorizontalLinePrimitive7</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="299" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive7>
        <HorizontalLinePrimitive8 Ref="300" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>0,9.7,18.8,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="301" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Name>HorizontalLinePrimitive8</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="302" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive8>
        <VerticalLinePrimitive19 Ref="303" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>9.15,5.9,0.0254,2.9</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="304" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>f13723d5c75f4377a57d4702c1911f6c</Guid>
          <Name>VerticalLinePrimitive19</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="305" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive19>
        <StartPointPrimitive19 Ref="306" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>9.15,5.9,0,0</ClientRectangle>
          <Name>StartPointPrimitive19</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>f13723d5c75f4377a57d4702c1911f6c</ReferenceToGuid>
        </StartPointPrimitive19>
        <EndPointPrimitive19 Ref="307" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>9.15,8.8,0,0</ClientRectangle>
          <Name>EndPointPrimitive19</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>f13723d5c75f4377a57d4702c1911f6c</ReferenceToGuid>
        </EndPointPrimitive19>
        <VerticalLinePrimitive20 Ref="308" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>16.45,5.9,0.0254,0.9</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="309" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>e7865635d5c54bd0a74af05d21f963dc</Guid>
          <Name>VerticalLinePrimitive20</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="310" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive20>
        <StartPointPrimitive20 Ref="311" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>16.45,5.9,0,0</ClientRectangle>
          <Name>StartPointPrimitive20</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>e7865635d5c54bd0a74af05d21f963dc</ReferenceToGuid>
        </StartPointPrimitive20>
        <EndPointPrimitive20 Ref="312" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>16.45,6.8,0,0</ClientRectangle>
          <Name>EndPointPrimitive20</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>e7865635d5c54bd0a74af05d21f963dc</ReferenceToGuid>
        </EndPointPrimitive20>
        <VerticalLinePrimitive21 Ref="313" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>11.7,6.8,0.0254,2</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="314" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>5372ffe9613241ba964626108ae50589</Guid>
          <Name>VerticalLinePrimitive21</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="315" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive21>
        <StartPointPrimitive21 Ref="316" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>11.7,6.8,0,0</ClientRectangle>
          <Name>StartPointPrimitive21</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>5372ffe9613241ba964626108ae50589</ReferenceToGuid>
        </StartPointPrimitive21>
        <EndPointPrimitive21 Ref="317" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>11.7,8.8,0,0</ClientRectangle>
          <Name>EndPointPrimitive21</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>5372ffe9613241ba964626108ae50589</ReferenceToGuid>
        </EndPointPrimitive21>
        <VerticalLinePrimitive22 Ref="318" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>15.1,6.8,0.0254,2</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="319" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>3299b54df8bd4ba7b1e8f5c74f58f1e3</Guid>
          <Name>VerticalLinePrimitive22</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="320" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive22>
        <StartPointPrimitive22 Ref="321" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>15.1,6.8,0,0</ClientRectangle>
          <Name>StartPointPrimitive22</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>3299b54df8bd4ba7b1e8f5c74f58f1e3</ReferenceToGuid>
        </StartPointPrimitive22>
        <EndPointPrimitive22 Ref="322" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>15.1,8.8,0,0</ClientRectangle>
          <Name>EndPointPrimitive22</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>3299b54df8bd4ba7b1e8f5c74f58f1e3</ReferenceToGuid>
        </EndPointPrimitive22>
        <VerticalLinePrimitive23 Ref="323" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>17.15,6.8,0.0254,2</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="324" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>134d1c2231354998a310f25c163b63c1</Guid>
          <Name>VerticalLinePrimitive23</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="325" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive23>
        <StartPointPrimitive23 Ref="326" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>17.15,6.8,0,0</ClientRectangle>
          <Name>StartPointPrimitive23</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>134d1c2231354998a310f25c163b63c1</ReferenceToGuid>
        </StartPointPrimitive23>
        <EndPointPrimitive23 Ref="327" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>17.15,8.8,0,0</ClientRectangle>
          <Name>EndPointPrimitive23</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>134d1c2231354998a310f25c163b63c1</ReferenceToGuid>
        </EndPointPrimitive23>
        <VerticalLinePrimitive24 Ref="328" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>0.75,9.7,0.0254,3.3</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="329" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>9bc4eb0b80044a77b0188ea237c41c5c</Guid>
          <Name>VerticalLinePrimitive24</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="330" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive24>
        <StartPointPrimitive24 Ref="331" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>0.75,9.7,0,0</ClientRectangle>
          <Name>StartPointPrimitive24</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>9bc4eb0b80044a77b0188ea237c41c5c</ReferenceToGuid>
        </StartPointPrimitive24>
        <EndPointPrimitive24 Ref="332" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>0.75,13,0,0</ClientRectangle>
          <Name>EndPointPrimitive24</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>9bc4eb0b80044a77b0188ea237c41c5c</ReferenceToGuid>
        </EndPointPrimitive24>
        <HorizontalLinePrimitive9 Ref="333" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>0,13,18.8,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="334" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Name>HorizontalLinePrimitive9</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="335" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive9>
        <HorizontalLinePrimitive10 Ref="336" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>0.85,10.6,18.05,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="337" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Name>HorizontalLinePrimitive10</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="338" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive10>
        <HorizontalLinePrimitive11 Ref="339" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>0.75,11.4,18.05,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="340" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>d8230717a9844d69a8657cf8c81d040d</Guid>
          <Name>HorizontalLinePrimitive11</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="341" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive11>
        <HorizontalLinePrimitive12 Ref="342" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>0.75,12.2,18.05,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="343" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>8e079da3d2ac46129f8c5c63689f2d3b</Guid>
          <Name>HorizontalLinePrimitive12</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="344" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive12>
        <VerticalLinePrimitive25 Ref="345" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>2.85,9.7,0.0254,3.3</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="346" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>c1c9bee6366f484a8e52cdb06bb7fab8</Guid>
          <Name>VerticalLinePrimitive25</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="347" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive25>
        <StartPointPrimitive25 Ref="348" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>2.85,9.7,0,0</ClientRectangle>
          <Name>StartPointPrimitive25</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>c1c9bee6366f484a8e52cdb06bb7fab8</ReferenceToGuid>
        </StartPointPrimitive25>
        <EndPointPrimitive25 Ref="349" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>2.85,13,0,0</ClientRectangle>
          <Name>EndPointPrimitive25</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>c1c9bee6366f484a8e52cdb06bb7fab8</ReferenceToGuid>
        </EndPointPrimitive25>
        <VerticalLinePrimitive27 Ref="350" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>11.05,9.7,0.0254,3.3</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="351" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>aaccc76961e74a2b94a5f162b00c5a8b</Guid>
          <Name>VerticalLinePrimitive27</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="352" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive27>
        <StartPointPrimitive27 Ref="353" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>11.05,9.7,0,0</ClientRectangle>
          <Name>StartPointPrimitive27</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>aaccc76961e74a2b94a5f162b00c5a8b</ReferenceToGuid>
        </StartPointPrimitive27>
        <EndPointPrimitive27 Ref="354" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>11.05,13,0,0</ClientRectangle>
          <Name>EndPointPrimitive27</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>aaccc76961e74a2b94a5f162b00c5a8b</ReferenceToGuid>
        </EndPointPrimitive27>
        <HorizontalLinePrimitive13 Ref="355" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>0,13.6,18.8,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="356" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Name>HorizontalLinePrimitive13</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="357" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive13>
        <VerticalLinePrimitive40 Ref="358" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>6.31,3.2,0.0254,0.9</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="359" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>2ba56b89eb934b248afb5f530cd96682</Guid>
          <Name>VerticalLinePrimitive40</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="360" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive40>
        <StartPointPrimitive40 Ref="361" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>6.31,3.2,0,0</ClientRectangle>
          <Name>StartPointPrimitive40</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>2ba56b89eb934b248afb5f530cd96682</ReferenceToGuid>
        </StartPointPrimitive40>
        <EndPointPrimitive40 Ref="362" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>6.11,4.1,0,0</ClientRectangle>
          <Name>EndPointPrimitive40</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>2ba56b89eb934b248afb5f530cd96682</ReferenceToGuid>
        </EndPointPrimitive40>
        <VerticalLinePrimitive41 Ref="363" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>8.1,3.2,0.0254,0.9</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="364" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>1f24d9feb9de4dd38d78b138baedf21f</Guid>
          <Name>VerticalLinePrimitive41</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="365" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive41>
        <StartPointPrimitive41 Ref="366" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>8.1,3.2,0,0</ClientRectangle>
          <Name>StartPointPrimitive41</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>1f24d9feb9de4dd38d78b138baedf21f</ReferenceToGuid>
        </StartPointPrimitive41>
        <EndPointPrimitive41 Ref="367" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>7.9,4.1,0,0</ClientRectangle>
          <Name>EndPointPrimitive41</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>1f24d9feb9de4dd38d78b138baedf21f</ReferenceToGuid>
        </EndPointPrimitive41>
        <VerticalLinePrimitive42 Ref="368" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>11.79,3.2,0.0254,0.9</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="369" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>7ff800edf3d94b58ab1cdbf26018b8cc</Guid>
          <Name>VerticalLinePrimitive42</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="370" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive42>
        <StartPointPrimitive42 Ref="371" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>11.79,3.2,0,0</ClientRectangle>
          <Name>StartPointPrimitive42</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>7ff800edf3d94b58ab1cdbf26018b8cc</ReferenceToGuid>
        </StartPointPrimitive42>
        <EndPointPrimitive42 Ref="372" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>11.79,4.1,0,0</ClientRectangle>
          <Name>EndPointPrimitive42</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>7ff800edf3d94b58ab1cdbf26018b8cc</ReferenceToGuid>
        </EndPointPrimitive42>
        <VerticalLinePrimitive43 Ref="373" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>10.85,4.1,0.0254,1.8</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="374" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>c54156cf2da940059273501362ac78d6</Guid>
          <Name>VerticalLinePrimitive43</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="375" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive43>
        <StartPointPrimitive43 Ref="376" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>10.85,4.1,0,0</ClientRectangle>
          <Name>StartPointPrimitive43</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>c54156cf2da940059273501362ac78d6</ReferenceToGuid>
        </StartPointPrimitive43>
        <EndPointPrimitive43 Ref="377" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>10.85,5.9,0,0</ClientRectangle>
          <Name>EndPointPrimitive43</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>c54156cf2da940059273501362ac78d6</ReferenceToGuid>
        </EndPointPrimitive43>
        <HorizontalLinePrimitive23 Ref="378" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>0,7.7,11.7,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="379" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>1382e4b9c4dd4784aecc648f1b338065</Guid>
          <Name>HorizontalLinePrimitive23</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="380" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive23>
        <HorizontalLinePrimitive24 Ref="381" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>2.85,10.05,15.95,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="382" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>ce935ec58c9441bb833f9d033e045514</Guid>
          <Name>HorizontalLinePrimitive24</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="383" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive24>
        <VerticalLinePrimitive50 Ref="384" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>7,10.05,0.0254,2.95</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="385" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>72c746a811c7460d8097afdba67649a3</Guid>
          <Name>VerticalLinePrimitive50</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="386" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive50>
        <StartPointPrimitive50 Ref="387" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>7,10.05,0,0</ClientRectangle>
          <Name>StartPointPrimitive50</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>72c746a811c7460d8097afdba67649a3</ReferenceToGuid>
        </StartPointPrimitive50>
        <EndPointPrimitive50 Ref="388" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>7,13,0,0</ClientRectangle>
          <Name>EndPointPrimitive50</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>72c746a811c7460d8097afdba67649a3</ReferenceToGuid>
        </EndPointPrimitive50>
        <VerticalLinePrimitive51 Ref="389" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>8.9,10.05,0.0254,2.95</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="390" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>7043ee00d25c499e8e5cf915f2dc8af4</Guid>
          <Name>VerticalLinePrimitive51</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="391" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive51>
        <StartPointPrimitive51 Ref="392" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>8.9,10.05,0,0</ClientRectangle>
          <Name>StartPointPrimitive51</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>7043ee00d25c499e8e5cf915f2dc8af4</ReferenceToGuid>
        </StartPointPrimitive51>
        <EndPointPrimitive51 Ref="393" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>8.9,13,0,0</ClientRectangle>
          <Name>EndPointPrimitive51</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>7043ee00d25c499e8e5cf915f2dc8af4</ReferenceToGuid>
        </EndPointPrimitive51>
        <VerticalLinePrimitive52 Ref="394" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>5.3,10.05,0.0254,2.95</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="395" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>808980c8167b4c0aaef8a0f8dae3a182</Guid>
          <Name>VerticalLinePrimitive52</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="396" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive52>
        <StartPointPrimitive52 Ref="397" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>5.3,10.05,0,0</ClientRectangle>
          <Name>StartPointPrimitive52</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>808980c8167b4c0aaef8a0f8dae3a182</ReferenceToGuid>
        </StartPointPrimitive52>
        <EndPointPrimitive52 Ref="398" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>5.3,13,0,0</ClientRectangle>
          <Name>EndPointPrimitive52</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>808980c8167b4c0aaef8a0f8dae3a182</ReferenceToGuid>
        </EndPointPrimitive52>
        <VerticalLinePrimitive54 Ref="399" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>13.15,10.05,0.0254,2.95</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="400" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>17902987c04549a5bd942796c43ce60d</Guid>
          <Name>VerticalLinePrimitive54</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="401" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive54>
        <VerticalLinePrimitive55 Ref="402" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>14.65,10.05,0.0254,2.95</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="403" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>e6b2b3b3a3944a62b61612a5a29ccfe2</Guid>
          <Name>VerticalLinePrimitive55</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="404" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive55>
        <VerticalLinePrimitive56 Ref="405" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>16.75,10.05,0.0254,2.95</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="406" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>c0b6e59b77f545bea97dbdc879d6447a</Guid>
          <Name>VerticalLinePrimitive56</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="407" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive56>
        <StartPointPrimitive54 Ref="408" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>13.15,10.05,0,0</ClientRectangle>
          <Name>StartPointPrimitive54</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>17902987c04549a5bd942796c43ce60d</ReferenceToGuid>
        </StartPointPrimitive54>
        <EndPointPrimitive54 Ref="409" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>13.15,13,0,0</ClientRectangle>
          <Name>EndPointPrimitive54</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>17902987c04549a5bd942796c43ce60d</ReferenceToGuid>
        </EndPointPrimitive54>
        <StartPointPrimitive55 Ref="410" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>14.65,10.05,0,0</ClientRectangle>
          <Name>StartPointPrimitive55</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>e6b2b3b3a3944a62b61612a5a29ccfe2</ReferenceToGuid>
        </StartPointPrimitive55>
        <EndPointPrimitive55 Ref="411" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>14.65,13,0,0</ClientRectangle>
          <Name>EndPointPrimitive55</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>e6b2b3b3a3944a62b61612a5a29ccfe2</ReferenceToGuid>
        </EndPointPrimitive55>
        <StartPointPrimitive56 Ref="412" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>16.75,10.05,0,0</ClientRectangle>
          <Name>StartPointPrimitive56</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>c0b6e59b77f545bea97dbdc879d6447a</ReferenceToGuid>
        </StartPointPrimitive56>
        <EndPointPrimitive56 Ref="413" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>16.75,13,0,0</ClientRectangle>
          <Name>EndPointPrimitive56</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>c0b6e59b77f545bea97dbdc879d6447a</ReferenceToGuid>
        </EndPointPrimitive56>
        <VerticalLinePrimitive57 Ref="414" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>2.7,8.8,0.0254,0.9</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="415" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>cdf77592c0324a8e96efdcc417c68001</Guid>
          <Name>VerticalLinePrimitive57</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="416" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive57>
        <StartPointPrimitive57 Ref="417" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>2.7,8.8,0,0</ClientRectangle>
          <Name>StartPointPrimitive57</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>cdf77592c0324a8e96efdcc417c68001</ReferenceToGuid>
        </StartPointPrimitive57>
        <EndPointPrimitive57 Ref="418" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>2.7,9.7,0,0</ClientRectangle>
          <Name>EndPointPrimitive57</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>cdf77592c0324a8e96efdcc417c68001</ReferenceToGuid>
        </EndPointPrimitive57>
        <VerticalLinePrimitive48 Ref="419" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>5.05,13,0.0254,0.6</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="420" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>66c45c3aa2c5485eb61203638b25c0ff</Guid>
          <Name>VerticalLinePrimitive48</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="421" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive48>
        <StartPointPrimitive48 Ref="422" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>5.05,13,0,0</ClientRectangle>
          <Name>StartPointPrimitive48</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>66c45c3aa2c5485eb61203638b25c0ff</ReferenceToGuid>
        </StartPointPrimitive48>
        <EndPointPrimitive48 Ref="423" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>5.05,13.6,0,0</ClientRectangle>
          <Name>EndPointPrimitive48</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>66c45c3aa2c5485eb61203638b25c0ff</ReferenceToGuid>
        </EndPointPrimitive48>
        <VerticalLinePrimitive49 Ref="424" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>7.1,13,0.0254,0.6</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="425" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>83e11295b9ca4bb6bf2988e10828e7d0</Guid>
          <Name>VerticalLinePrimitive49</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="426" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive49>
        <StartPointPrimitive49 Ref="427" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>7.1,13,0,0</ClientRectangle>
          <Name>StartPointPrimitive49</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>83e11295b9ca4bb6bf2988e10828e7d0</ReferenceToGuid>
        </StartPointPrimitive49>
        <EndPointPrimitive49 Ref="428" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>7.1,13.6,0,0</ClientRectangle>
          <Name>EndPointPrimitive49</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>83e11295b9ca4bb6bf2988e10828e7d0</ReferenceToGuid>
        </EndPointPrimitive49>
        <VerticalLinePrimitive58 Ref="429" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>10.7,13,0.0254,0.6</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="430" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>135fdd61d7eb474f98eac051cff30a40</Guid>
          <Name>VerticalLinePrimitive58</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="431" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive58>
        <StartPointPrimitive58 Ref="432" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>10.7,13,0,0</ClientRectangle>
          <Name>StartPointPrimitive58</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>135fdd61d7eb474f98eac051cff30a40</ReferenceToGuid>
        </StartPointPrimitive58>
        <EndPointPrimitive58 Ref="433" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>10.7,13.6,0,0</ClientRectangle>
          <Name>EndPointPrimitive58</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>135fdd61d7eb474f98eac051cff30a40</ReferenceToGuid>
        </EndPointPrimitive58>
        <VerticalLinePrimitive59 Ref="434" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>12.55,13,0.0254,0.6</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="435" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>6e8714b4339d4781b39e650f6d4777ae</Guid>
          <Name>VerticalLinePrimitive59</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="436" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive59>
        <StartPointPrimitive59 Ref="437" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>12.55,13,0,0</ClientRectangle>
          <Name>StartPointPrimitive59</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>6e8714b4339d4781b39e650f6d4777ae</ReferenceToGuid>
        </StartPointPrimitive59>
        <EndPointPrimitive59 Ref="438" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>12.55,13.6,0,0</ClientRectangle>
          <Name>EndPointPrimitive59</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>6e8714b4339d4781b39e650f6d4777ae</ReferenceToGuid>
        </EndPointPrimitive59>
        <VerticalLinePrimitive60 Ref="439" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>17.2,13,0.0254,0.6</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="440" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>336c13e3e8d74792a9c90aee08105318</Guid>
          <Name>VerticalLinePrimitive60</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="441" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive60>
        <StartPointPrimitive60 Ref="442" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>17.2,13,0,0</ClientRectangle>
          <Name>StartPointPrimitive60</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>336c13e3e8d74792a9c90aee08105318</ReferenceToGuid>
        </StartPointPrimitive60>
        <EndPointPrimitive60 Ref="443" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>17.2,13.6,0,0</ClientRectangle>
          <Name>EndPointPrimitive60</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>336c13e3e8d74792a9c90aee08105318</ReferenceToGuid>
        </EndPointPrimitive60>
        <HorizontalLinePrimitive25 Ref="444" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>1.15,14.3,12.45,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="445" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>1bea8b54330e41999758c6dc63f41940</Guid>
          <Name>HorizontalLinePrimitive25</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="446" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive25>
        <HorizontalLinePrimitive26 Ref="447" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>1.15,15.1,12.45,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="448" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>6303de66fac54c7da5f67e5748d36f1b</Guid>
          <Name>HorizontalLinePrimitive26</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="449" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive26>
        <HorizontalLinePrimitive27 Ref="450" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>1.15,15.85,12.45,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="451" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>4eb3d341d43d4c44b3160982f478574b</Guid>
          <Name>HorizontalLinePrimitive27</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="452" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive27>
        <HorizontalLinePrimitive28 Ref="453" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>1.15,16.6,12.45,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="454" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>aa1d3a44facf4ab98cc3ca19e72a9a22</Guid>
          <Name>HorizontalLinePrimitive28</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="455" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive28>
        <HorizontalLinePrimitive29 Ref="456" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>15.3,14.3,3.5,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="457" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>33e8bece159a45f3ab244ad7b0df002e</Guid>
          <Name>HorizontalLinePrimitive29</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="458" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive29>
        <HorizontalLinePrimitive30 Ref="459" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>15.3,15.1,3.5,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="460" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>e70db826dc054e87a7d837db14c03548</Guid>
          <Name>HorizontalLinePrimitive30</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="461" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive30>
        <HorizontalLinePrimitive31 Ref="462" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>15.3,16,3.5,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="463" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>750f030fabf74099b04b354ca2849267</Guid>
          <Name>HorizontalLinePrimitive31</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="464" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive31>
        <VerticalLinePrimitive61 Ref="465" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>3.3,13.6,0.0254,3.6</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="466" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>c7448c5d95aa44529740e55cc83d8b41</Guid>
          <Name>VerticalLinePrimitive61</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="467" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive61>
        <StartPointPrimitive61 Ref="468" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>3.3,13.6,0,0</ClientRectangle>
          <Name>StartPointPrimitive61</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>c7448c5d95aa44529740e55cc83d8b41</ReferenceToGuid>
        </StartPointPrimitive61>
        <EndPointPrimitive61 Ref="469" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>3.3,17.2,0,0</ClientRectangle>
          <Name>EndPointPrimitive61</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>c7448c5d95aa44529740e55cc83d8b41</ReferenceToGuid>
        </EndPointPrimitive61>
        <VerticalLinePrimitive62 Ref="470" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>6.35,13.6,0.0254,3.6</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="471" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>d11cce4820f447cab44ace40a9c1176a</Guid>
          <Name>VerticalLinePrimitive62</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="472" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive62>
        <StartPointPrimitive62 Ref="473" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>6.35,13.6,0,0</ClientRectangle>
          <Name>StartPointPrimitive62</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>d11cce4820f447cab44ace40a9c1176a</ReferenceToGuid>
        </StartPointPrimitive62>
        <EndPointPrimitive62 Ref="474" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>6.4,17.2,0,0</ClientRectangle>
          <Name>EndPointPrimitive62</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>d11cce4820f447cab44ace40a9c1176a</ReferenceToGuid>
        </EndPointPrimitive62>
        <VerticalLinePrimitive64 Ref="475" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>8.85,13.6,0.0254,3.6</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="476" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>b91fce9194af46809592f6e8dc319df8</Guid>
          <Name>VerticalLinePrimitive64</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="477" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive64>
        <StartPointPrimitive64 Ref="478" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>8.85,13.6,0,0</ClientRectangle>
          <Name>StartPointPrimitive64</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>b91fce9194af46809592f6e8dc319df8</ReferenceToGuid>
        </StartPointPrimitive64>
        <EndPointPrimitive64 Ref="479" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>8.85,17.2,0,0</ClientRectangle>
          <Name>EndPointPrimitive64</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>b91fce9194af46809592f6e8dc319df8</ReferenceToGuid>
        </EndPointPrimitive64>
        <VerticalLinePrimitive65 Ref="480" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>11.35,13.6,0.0254,3.6</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="481" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>9b94668ea9854fffb46e33cd2a53d6c5</Guid>
          <Name>VerticalLinePrimitive65</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="482" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive65>
        <StartPointPrimitive65 Ref="483" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>11.35,13.6,0,0</ClientRectangle>
          <Name>StartPointPrimitive65</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>9b94668ea9854fffb46e33cd2a53d6c5</ReferenceToGuid>
        </StartPointPrimitive65>
        <EndPointPrimitive65 Ref="484" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>11.35,17.2,0,0</ClientRectangle>
          <Name>EndPointPrimitive65</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>9b94668ea9854fffb46e33cd2a53d6c5</ReferenceToGuid>
        </EndPointPrimitive65>
        <VerticalLinePrimitive66 Ref="485" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>13.6,13.6,0.0254,3.6</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="486" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>842b59ef374b4d4db881db76eb0b7448</Guid>
          <Name>VerticalLinePrimitive66</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="487" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive66>
        <StartPointPrimitive66 Ref="488" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>13.6,13.6,0,0</ClientRectangle>
          <Name>StartPointPrimitive66</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>842b59ef374b4d4db881db76eb0b7448</ReferenceToGuid>
        </StartPointPrimitive66>
        <EndPointPrimitive66 Ref="489" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>13.6,17.2,0,0</ClientRectangle>
          <Name>EndPointPrimitive66</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>842b59ef374b4d4db881db76eb0b7448</ReferenceToGuid>
        </EndPointPrimitive66>
        <VerticalLinePrimitive67 Ref="490" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>15.25,13.6,0.0254,3.6</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="491" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>a513ca79ba7e4a97beefc63ee2c9cc8d</Guid>
          <Name>VerticalLinePrimitive67</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="492" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive67>
        <StartPointPrimitive67 Ref="493" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>15.25,13.6,0,0</ClientRectangle>
          <Name>StartPointPrimitive67</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>a513ca79ba7e4a97beefc63ee2c9cc8d</ReferenceToGuid>
        </StartPointPrimitive67>
        <EndPointPrimitive67 Ref="494" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>15.2,17.2,0,0</ClientRectangle>
          <Name>EndPointPrimitive67</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>a513ca79ba7e4a97beefc63ee2c9cc8d</ReferenceToGuid>
        </EndPointPrimitive67>
        <VerticalLinePrimitive68 Ref="495" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>16.95,13.6,0.0254,3.6</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="496" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>80a7563cffec417982ec9704e2a62d35</Guid>
          <Name>VerticalLinePrimitive68</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="497" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive68>
        <StartPointPrimitive68 Ref="498" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>16.95,13.6,0,0</ClientRectangle>
          <Name>StartPointPrimitive68</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>80a7563cffec417982ec9704e2a62d35</ReferenceToGuid>
        </StartPointPrimitive68>
        <EndPointPrimitive68 Ref="499" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>16.9,17.2,0,0</ClientRectangle>
          <Name>EndPointPrimitive68</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>80a7563cffec417982ec9704e2a62d35</ReferenceToGuid>
        </EndPointPrimitive68>
        <HorizontalLinePrimitive32 Ref="500" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>0,17.2,18.8,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="501" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>5f5724057c3b413388d22f411c645e2f</Guid>
          <Name>HorizontalLinePrimitive32</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="502" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive32>
        <VerticalLinePrimitive69 Ref="503" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>1.15,13.6,0.0254,3.6</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="504" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>0574e3f71ba24078bd285bb210eccd66</Guid>
          <Name>VerticalLinePrimitive69</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="505" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive69>
        <StartPointPrimitive69 Ref="506" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>1.15,13.6,0,0</ClientRectangle>
          <Name>StartPointPrimitive69</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>0574e3f71ba24078bd285bb210eccd66</ReferenceToGuid>
        </StartPointPrimitive69>
        <EndPointPrimitive69 Ref="507" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>1.1,17.2,0,0</ClientRectangle>
          <Name>EndPointPrimitive69</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>0574e3f71ba24078bd285bb210eccd66</ReferenceToGuid>
        </EndPointPrimitive69>
        <HorizontalLinePrimitive33 Ref="508" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>0,17.95,18.8,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="509" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>97baa6d866994b8ab0b2befe088699c6</Guid>
          <Name>HorizontalLinePrimitive33</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="510" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive33>
        <VerticalLinePrimitive63 Ref="511" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>12.3,17.2,0.0254,0.75</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="512" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>7877a79967554df5a385f831bb741392</Guid>
          <Name>VerticalLinePrimitive63</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="513" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive63>
        <StartPointPrimitive63 Ref="514" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>12.3,17.2,0,0</ClientRectangle>
          <Name>StartPointPrimitive63</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>7877a79967554df5a385f831bb741392</ReferenceToGuid>
        </StartPointPrimitive63>
        <EndPointPrimitive63 Ref="515" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>12.3,17.95,0,0</ClientRectangle>
          <Name>EndPointPrimitive63</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>7877a79967554df5a385f831bb741392</ReferenceToGuid>
        </EndPointPrimitive63>
        <VerticalLinePrimitive70 Ref="516" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>7.95,17.2,0.0254,0.75</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="517" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>ed06eb9ecf1644c896d90e3bf9e28394</Guid>
          <Name>VerticalLinePrimitive70</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="518" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive70>
        <StartPointPrimitive70 Ref="519" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>7.95,17.2,0,0</ClientRectangle>
          <Name>StartPointPrimitive70</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>ed06eb9ecf1644c896d90e3bf9e28394</ReferenceToGuid>
        </StartPointPrimitive70>
        <EndPointPrimitive70 Ref="520" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>7.95,17.95,0,0</ClientRectangle>
          <Name>EndPointPrimitive70</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>ed06eb9ecf1644c896d90e3bf9e28394</ReferenceToGuid>
        </EndPointPrimitive70>
        <VerticalLinePrimitive71 Ref="521" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>5.65,17.2,0.0254,0.75</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="522" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>cd1ca27033174222a7fe6c8d362547e5</Guid>
          <Name>VerticalLinePrimitive71</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="523" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive71>
        <StartPointPrimitive71 Ref="524" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>5.65,17.2,0,0</ClientRectangle>
          <Name>StartPointPrimitive71</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>cd1ca27033174222a7fe6c8d362547e5</ReferenceToGuid>
        </StartPointPrimitive71>
        <EndPointPrimitive71 Ref="525" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>5.65,17.95,0,0</ClientRectangle>
          <Name>EndPointPrimitive71</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>cd1ca27033174222a7fe6c8d362547e5</ReferenceToGuid>
        </EndPointPrimitive71>
        <VerticalLinePrimitive72 Ref="526" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>3.45,17.2,0.0254,0.75</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="527" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>0fcb3f2bd0844b9fbd60eb79cc30d504</Guid>
          <Name>VerticalLinePrimitive72</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="528" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive72>
        <StartPointPrimitive72 Ref="529" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>3.45,17.2,0,0</ClientRectangle>
          <Name>StartPointPrimitive72</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>0fcb3f2bd0844b9fbd60eb79cc30d504</ReferenceToGuid>
        </StartPointPrimitive72>
        <EndPointPrimitive72 Ref="530" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>3.45,17.95,0,0</ClientRectangle>
          <Name>EndPointPrimitive72</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>0fcb3f2bd0844b9fbd60eb79cc30d504</ReferenceToGuid>
        </EndPointPrimitive72>
        <VerticalLinePrimitive73 Ref="531" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>9.55,17.2,0.0254,0.75</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="532" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>988026b1858f474fb198648ca1a807b9</Guid>
          <Name>VerticalLinePrimitive73</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="533" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive73>
        <StartPointPrimitive73 Ref="534" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>9.55,17.2,0,0</ClientRectangle>
          <Name>StartPointPrimitive73</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>988026b1858f474fb198648ca1a807b9</ReferenceToGuid>
        </StartPointPrimitive73>
        <EndPointPrimitive73 Ref="535" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>9.55,17.95,0,0</ClientRectangle>
          <Name>EndPointPrimitive73</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>988026b1858f474fb198648ca1a807b9</ReferenceToGuid>
        </EndPointPrimitive73>
        <HorizontalLinePrimitive14 Ref="536" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>0,18.7,12.9,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="537" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>fe9ef4e7e2d744bcb626fbc24f36ec75</Guid>
          <Name>HorizontalLinePrimitive14</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="538" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive14>
        <HorizontalLinePrimitive15 Ref="539" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>0,19.6,18.8,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="540" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>7ffbbf2e2bc04a10a3a3ae2a0cf4598e</Guid>
          <Name>HorizontalLinePrimitive15</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="541" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive15>
        <HorizontalLinePrimitive16 Ref="542" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>15,18.7,3.8,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="543" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>006eb932e1664a659040b54d4b198929</Guid>
          <Name>HorizontalLinePrimitive16</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="544" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive16>
        <VerticalLinePrimitive28 Ref="545" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>15,17.95,0.0254,1.65</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="546" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>26540688fe844d7ab6cc941303a7ee93</Guid>
          <Name>VerticalLinePrimitive28</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="547" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive28>
        <StartPointPrimitive28 Ref="548" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>15,17.95,0,0</ClientRectangle>
          <Name>StartPointPrimitive28</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>26540688fe844d7ab6cc941303a7ee93</ReferenceToGuid>
        </StartPointPrimitive28>
        <EndPointPrimitive28 Ref="549" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>15,19.6,0,0</ClientRectangle>
          <Name>EndPointPrimitive28</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>26540688fe844d7ab6cc941303a7ee93</ReferenceToGuid>
        </EndPointPrimitive28>
        <VerticalLinePrimitive29 Ref="550" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>17.05,17.95,0.0254,1.65</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="551" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>6a1448aca1b34e54aaf6868be83cfa5a</Guid>
          <Name>VerticalLinePrimitive29</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="552" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive29>
        <StartPointPrimitive29 Ref="553" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>17.05,17.95,0,0</ClientRectangle>
          <Name>StartPointPrimitive29</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>6a1448aca1b34e54aaf6868be83cfa5a</ReferenceToGuid>
        </StartPointPrimitive29>
        <EndPointPrimitive29 Ref="554" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>17.05,19.6,0,0</ClientRectangle>
          <Name>EndPointPrimitive29</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>6a1448aca1b34e54aaf6868be83cfa5a</ReferenceToGuid>
        </EndPointPrimitive29>
        <VerticalLinePrimitive30 Ref="555" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>12.95,17.95,0.0254,1.65</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="556" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>74a57574651f484fa2660adb91a62704</Guid>
          <Name>VerticalLinePrimitive30</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="557" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive30>
        <StartPointPrimitive30 Ref="558" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>12.95,17.95,0,0</ClientRectangle>
          <Name>StartPointPrimitive30</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>74a57574651f484fa2660adb91a62704</ReferenceToGuid>
        </StartPointPrimitive30>
        <EndPointPrimitive30 Ref="559" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>12.95,19.6,0,0</ClientRectangle>
          <Name>EndPointPrimitive30</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>74a57574651f484fa2660adb91a62704</ReferenceToGuid>
        </EndPointPrimitive30>
        <HorizontalLinePrimitive17 Ref="560" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>0,20.4,18.8,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="561" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>81287f53ddc44721b14ae9ce272da2ad</Guid>
          <Name>HorizontalLinePrimitive17</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="562" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive17>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>a602b0ce0a8b4f73b56e7924e9f50042</Guid>
      <Margins>1.1,1.1,1,1</Margins>
      <Name>Page1</Name>
      <PageHeight>29.7</PageHeight>
      <PageWidth>21</PageWidth>
      <Report isRef="0" />
      <Watermark Ref="563" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page1>
  </Pages>
  <PrinterSettings Ref="564" type="Stimulsoft.Report.Print.StiPrinterSettings" isKey="true" />
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>Report</ReportAlias>
  <ReportChanged>8/15/2019 3:31:48 PM</ReportChanged>
  <ReportCreated>5/22/2019 2:37:22 PM</ReportCreated>
  <ReportFile>D:\调试dll通辽程序\新建文件夹\his2010\Rpt\通辽市医疗保险住院费用结算单(居民).mrt</ReportFile>
  <ReportGuid>b441fa80da334af4a21a93ead412ba0b</ReportGuid>
  <ReportName>Report</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2011.2.1026</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class Report : Stimulsoft.Report.StiReport
    {
        public Report()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
        #endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>