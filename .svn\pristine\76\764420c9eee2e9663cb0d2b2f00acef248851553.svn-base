
ALTER VIEW [dbo].[V_Yp]
AS
SELECT   dbo.Zd_Ml_Yp2.Yp_Code, dbo.Zd_Ml_Yp2.Yp_Name, dbo.Zd_Ml_Yp2.Yp_Jc, dbo.Zd_Ml_Yp3.Mx_Code, 
                dbo.Zd_Ml_Yp3.Mx_Gg, dbo.Zd_Ml_Yp3.Mx_CgDw, dbo.Zd_Ml_Yp3.Jx_Code, dbo.Zd_Ml_Yp3.Mx_Cd, 
                dbo.Zd_Ml_Yp3.Mx_XsDw, dbo.Zd_Ml_Yp3.Mx_Cfbl, dbo.Zd_Ml_Yp3.Mx_Gyzz, dbo.Zd_Ml_Ypjx.Jx_Name, 
                dbo.Zd_Ml_Yp2.Yp_Memo, dbo.Zd_Ml_Yp3.Mx_Memo, dbo.Zd_Ml_Yp2.Dl_Code, dbo.Zd_Ml_Yp1.Dl_Name, 
                dbo.Zd_Ml_Yp3.HzylMz_Code, dbo.Zd_Ml_Yp2.<PERSON>Jb, dbo.Zd_Ml_Yp2.Special, dbo.Zd_Ml_Yp2.IsUse, 
                dbo.Zd_Ml_Yp1Xl.Xl_Code, dbo.Zd_Ml_Yp1Xl.Xl_Name, dbo.Zd_Ml_Yp3.Amount_Per_Package, 
                dbo.Zd_Ml_Yp3.Min_Units, dbo.Zd_Ml_Yp3.Dose_Per_Unit, dbo.Zd_Ml_Yp3.Dose_Units, dbo.Zd_Ml_Yp3.Max_Dosage, 
                dbo.Zd_Ml_Yp3.Administration_Code, dbo.Zd_Ml_Yp3.Freq_Code, dbo.Zd_Ml_Yp3.Frequency, 
                dbo.Zd_Ml_Yp3.Freq_Interval, dbo.Zd_Ml_Yp3.Freq_Interval_Unit, dbo.Zd_Ml_Yp3.Freq_Counter, 
                dbo.Zd_Ml_Yp3.Per_Dosage, dbo.Zd_Ml_Yp3.Max_Outp_Abidance, dbo.Dict_Administration.Administration_Name
FROM      dbo.Zd_Ml_Yp2 INNER JOIN
                dbo.Zd_Ml_Yp3 ON dbo.Zd_Ml_Yp2.Yp_Code = dbo.Zd_Ml_Yp3.Yp_Code INNER JOIN
                dbo.Zd_Ml_Yp1 ON dbo.Zd_Ml_Yp2.Dl_Code = dbo.Zd_Ml_Yp1.Dl_Code LEFT OUTER JOIN
                dbo.Dict_Administration ON 
                dbo.Zd_Ml_Yp3.Administration_Code = dbo.Dict_Administration.Administration_Code LEFT OUTER JOIN
                dbo.Zd_Ml_Yp1Xl ON dbo.Zd_Ml_Yp2.Xl_Code = dbo.Zd_Ml_Yp1Xl.Xl_Code LEFT OUTER JOIN
                dbo.Zd_Ml_Ypjx ON dbo.Zd_Ml_Yp3.Jx_Code = dbo.Zd_Ml_Ypjx.Jx_Code
GO


ALTER VIEW [dbo].[V_YpKc]
AS
SELECT   dbo.V_Yp.Yp_Code, dbo.V_Yp.Yp_Name, dbo.V_Yp.Yp_Jc, dbo.V_Yp.Mx_Code, dbo.V_Yp.Mx_Gg, dbo.V_Yp.Mx_CgDw, 
                dbo.V_Yp.Jx_Code, dbo.V_Yp.Mx_Cd, dbo.V_Yp.Mx_XsDw, dbo.V_Yp.Mx_Cfbl, dbo.V_Yp.Mx_Gyzz, dbo.V_Yp.Jx_Name, 
                dbo.V_Yp.Yp_Memo, dbo.V_Yp.Mx_Memo, dbo.Zd_Ml_Yp4.Yk_Sl, dbo.Zd_Ml_Yp4.Yk_Cgj, dbo.Zd_Ml_Yp4.Yk_Pfj, 
                dbo.Zd_Ml_Yp4.Yk_Xsj, dbo.Zd_Ml_Yp4.Yf_Sl1, dbo.Zd_Ml_Yp4.Yf_Lsj1, dbo.Zd_Ml_Yp4.Yf_Sl2, 
                dbo.Zd_Ml_Yp4.Yf_Lsj2, dbo.Zd_Ml_Yp4.Yf_Sl3, dbo.Zd_Ml_Yp4.Yf_Lsj3, dbo.Zd_Ml_Yp4.Yf_Sl4, 
                dbo.Zd_Ml_Yp4.Yf_Lsj4, dbo.Zd_Ml_Yp4.Yf_Sl5, dbo.Zd_Ml_Yp4.Yf_Lsj5, dbo.Zd_Ml_Yp4.Yy_Code, 
                dbo.V_Yp.Dl_Code, dbo.V_Yp.Dl_Name, dbo.Zd_Ml_Yp4.Xx_Code, dbo.Zd_Ml_Yp4.Yp_Ph, dbo.Zd_Ml_Yp4.Yp_Yxq, 
                dbo.Zd_Ml_Yp4.Yp_Scrq, dbo.V_Yp.IsJb, dbo.V_Yp.Special, dbo.Zd_Ml_Yp4.Yf_Sl6, dbo.Zd_Ml_Yp4.Yf_Lsj6, 
                dbo.Zd_Ml_Yp4.Yf_Sl7, dbo.Zd_Ml_Yp4.Yf_Lsj7, dbo.Zd_Ml_Yp4.Yf_Sl8, dbo.Zd_Ml_Yp4.Yf_Lsj8, 
                dbo.Zd_Ml_Yp4.Yf_Sl9, dbo.Zd_Ml_Yp4.Yf_Lsj9, dbo.V_Yp.Xl_Code, dbo.V_Yp.Xl_Name, 
                dbo.V_Yp.Amount_Per_Package, dbo.V_Yp.Min_Units, dbo.V_Yp.Dose_Per_Unit, dbo.V_Yp.Dose_Units, 
                dbo.V_Yp.Max_Dosage, dbo.V_Yp.Administration_Code, dbo.V_Yp.Freq_Code, dbo.V_Yp.Frequency, 
                dbo.V_Yp.Freq_Interval, dbo.V_Yp.Freq_Interval_Unit, dbo.V_Yp.Freq_Counter, dbo.V_Yp.Per_Dosage, 
                dbo.V_Yp.Max_Outp_Abidance, dbo.V_Yp.Administration_Name
FROM      dbo.Zd_Ml_Yp4 INNER JOIN
                dbo.V_Yp ON dbo.Zd_Ml_Yp4.Mx_Code = dbo.V_Yp.Mx_Code
GO