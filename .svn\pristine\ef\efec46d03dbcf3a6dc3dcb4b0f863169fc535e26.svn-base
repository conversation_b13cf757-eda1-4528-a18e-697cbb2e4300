﻿/**  版本信息模板在安装目录下，可自行修改。
* M_Emr_DataField.cs
*
* 功 能： N/A
* 类 名： M_Emr_DataField
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/8/16 星期二 下午 1:38:16   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace ModelOld
{
	/// <summary>
	/// M_Emr_DataField:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class M_Emr_DataField
	{
		public M_Emr_DataField()
		{}
		#region Model
		private string _datafield;
		private string _dataname;
		private string _datajc;
		/// <summary>
		/// 
		/// </summary>
		public string DataField
		{
			set{ _datafield=value;}
			get{return _datafield;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string DataName
		{
			set{ _dataname=value;}
			get{return _dataname;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string DataJc
		{
			set{ _datajc=value;}
			get{return _datajc;}
		}
		#endregion Model

	}
}

