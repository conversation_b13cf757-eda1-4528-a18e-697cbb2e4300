﻿Imports System.Collections.Generic
Imports System.Text
Imports System.Xml
Imports System.Windows.Forms

Public Class Chs2Spell

    ''' <summary>
    ''' 返回最大编码
    ''' </summary>
    ''' <returns></returns>
    Public Function GetMaxCode(ByVal V_TableName As String, ByVal V_KeyName As String, ByVal V_Length As Integer, ByVal V_Where_Key As String, ByVal V_Where_Code As String) As String
        Dim V_MaxId As String
        Dim V_MaxCode As String
        Dim V_Fill As String = "000000000000000000000000000000000000000000000000000000000"
        Dim V_Sql As String

        V_Sql = "Select IsNull(RTrim(LTrim(Max(" & V_KeyName & "))),Left(" & V_Fill & ", " & V_Length & ")) From " & V_TableName & "	"
        If V_Where_Key.Trim <> "" Then
            V_Sql = V_Sql & " Where " & V_Where_Key & "='" & V_Where_Code & "'"
        End If

        V_MaxId = HisVar.HisVar.Sqldal.GetSingle(V_Sql)

        If V_MaxId = 0 Then
            V_MaxCode = V_Where_Code + Left(V_Fill, V_Length - Len(V_Where_Code) - 1) & "1"
        Else
            V_MaxCode = Right(V_Fill & CType((CType(Right(V_MaxId, Len(V_MaxId)), Double) + 1), String), Len(V_MaxId))
        End If

        Return V_MaxCode

    End Function


#Region "变量"

    ''' <summary>
    ''' XML文件读取实例
    ''' </summary>
    Private reader As XmlReader = Nothing

    ''' <summary>
    ''' XML文件中数据
    ''' </summary>
    Private strXmlData As String() = Nothing

    '记录XML中五笔码开始位置！
    Private wbCodeStation As Integer = 26

    '记录XML中结束位置！
    Private outStation As Integer = 100
#End Region
#Region "构造函数"
    ''' <summary>
    ''' 构造函数，初始化XMLREADER
    ''' </summary>
    Public Sub New()
        Dim path As String = Application.StartupPath
        Try
            reader = XmlReader.Create(path + "\Conf\CodeConfig.xml")
            Me.strXmlData = getXmlData()
            'MessageBox.Show(e.Message);
        Catch e As Exception
            '处理空白文件
            'reader.WhitespaceHandling = WhitespaceHandling.None;  
        End Try
    End Sub
#End Region

#Region "私有方法"

    ''' <summary>
    ''' 读取XML文件中数据
    ''' </summary>
    ''' <returns>返回String[]</returns>
    Private Function getXmlData() As String()
        '这里本应该开辟52个空间就够了，防止以后添加XML节点，故开辟多些空间
        Dim strValue As StringBuilder() = New StringBuilder(100) {}
        Dim result As String() = New String(100) {}
        Dim i As Integer = 0
        Try
            While reader.Read()
                Select Case reader.NodeType
                    Case XmlNodeType.Element
                        If reader.Name <> "CodeConfig" AndAlso reader.Name <> "SpellCode" AndAlso reader.Name <> "WBCode" Then

                            strValue(i) = New StringBuilder()
                            strValue(i).Append(reader.Name)
                        End If
                        If reader.Name = "WBCode" Then
                            Me.wbCodeStation = i
                        End If
                        Exit Select
                    Case XmlNodeType.Text
                        strValue(i).Append(reader.Value)
                        Exit Select
                    Case XmlNodeType.EndElement
                        If reader.Name <> "CodeConfig" AndAlso reader.Name <> "SpellCode" AndAlso reader.Name <> "WBCode" Then
                            result(i) = strValue(i).ToString()
                            System.Math.Max(System.Threading.Interlocked.Increment(i), i - 1)
                        End If
                        If reader.Name = "CodeConfig" Then
                            Me.outStation = i
                        End If
                        Exit Select
                End Select
            End While
        Finally
            If reader IsNot Nothing Then
                reader.Close()
            End If
        End Try
        Return result
    End Function

    ''' <summary>
    ''' 查找汉字
    ''' </summary>
    ''' <param name="strName">汉字</param>
    ''' <param name="start">搜索的开始位置</param>
    ''' <param name="end">搜索的结束位置</param>
    ''' <returns>汉语反义成字符串，该字符串只包含大写的英文字母</returns>
    Private Function searchWord(ByVal strName As String, ByVal start As Integer, ByVal [end] As Integer) As String
        strName = strName.Trim().Replace(" ", "")
        If String.IsNullOrEmpty(strName) Then
            Return strName
        End If
        Dim myStr As New StringBuilder()
        For Each vChar As Char In strName
            ' 若是字母或数字则直接输出

            Dim Ascii As Integer = AscW(vChar)
            If (Ascii >= 33 AndAlso Ascii <= 126) Then '(vChar >= "a"c AndAlso vChar <= "z"c) OrElse (vChar >= "A"c AndAlso vChar <= "Z"c) OrElse (vChar >= "0"c AndAlso vChar <= "9"c) Then
                myStr.Append(Char.ToUpper(vChar))
            Else
                ' 若字符Unicode编码在编码范围则 查汉字列表进行转换输出
                Dim strList As String = Nothing
                Dim i As Integer
                i = start
                While i < [end]
                    strList = Me.strXmlData(i)
                    If strList.IndexOf(vChar) > 0 Then
                        myStr.Append(strList(0))
                        Exit While
                    End If
                    System.Math.Max(System.Threading.Interlocked.Increment(i), i - 1)
                End While
            End If
        Next
        Return myStr.ToString()
    End Function
#End Region

#Region "公开方法"
    ''' <summary>
    ''' 获得汉语的拼音码
    ''' </summary>
    ''' <param name="strName">汉字</param>
    ''' <returns>汉语拼音码,该字符串只包含大写的英文字母</returns>
    Public Function GetPy(ByVal strName As String) As String
        Return Me.searchWord(strName, 0, Me.wbCodeStation)
    End Function
    ''' <summary>
    ''' 获得汉语的五笔码
    ''' </summary>
    ''' <param name="strName">汉字</param>
    ''' <returns>汉语五笔码,该字符串只包含大写的英文字母</returns>
    Public Function GetWb(ByVal strName As String) As String
        Return Me.searchWord(strName, Me.wbCodeStation, Me.outStation)
    End Function
#End Region


End Class
