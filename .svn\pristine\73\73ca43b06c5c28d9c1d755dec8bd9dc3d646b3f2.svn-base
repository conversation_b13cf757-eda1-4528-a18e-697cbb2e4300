﻿Public Class msg
    Inherits System.ComponentModel.Component
    '当前显示的提示框的数量
    Private Shared _Count As Integer = 0
    '提示框宽度
    Private Shared _WidthMax As Integer = 250
    '提示框高度
    Private Shared _HeightMax As Integer = 170
    '提示框停留时间（单位：毫秒）
    Private Shared _StayTime As Integer = 5000
    ' 提示框宽度
    Public Shared Property WidthMax() As Integer
        Get
            Return _WidthMax
        End Get
        Set(ByVal Value As Integer)
            _WidthMax = Value
        End Set
    End Property

    ''' <summary>
    ''' 提示框高度
    ''' </summary>
    Public Shared Property HeightMax() As Integer
        Get
            Return _HeightMax
        End Get
        Set(ByVal Value As Integer)
            _HeightMax = Value
        End Set
    End Property

    ''' <summary>
    ''' 提示框停留时间（单位：秒）
    ''' </summary>
    Public Shared Property StayTime() As Integer
        Get
            Return _StayTime / 1000
        End Get
        Set(ByVal Value As Integer)
            _StayTime = Value
        End Set
    End Property

    Public Shared Sub Show(ByVal text As String)
        Dim messageNotifyForm As MsgNotify = New MsgNotify()
        messageNotifyForm.Width = _WidthMax
        messageNotifyForm.HeightMax = _HeightMax
        messageNotifyForm.StayTime = _StayTime
        messageNotifyForm.ShowMessage(text)
    End Sub

    Public Shared Sub Show(ByVal text As String, ByVal caption As String)
        Dim messageNotifyForm As MsgNotify = New MsgNotify()
        messageNotifyForm.Width = _WidthMax
        messageNotifyForm.HeightMax = _HeightMax
        messageNotifyForm.StayTime = _StayTime
        messageNotifyForm.ShowMessage(text, caption)
    End Sub

    ''' <summary>
    ''' 
    ''' </summary>
    ''' <param name="text"></param>
    ''' <param name="caption"></param>
    ''' <param name="StayTime">停留的秒数</param>
    ''' <remarks></remarks>
    Public Shared Sub Show(ByVal text As String, ByVal caption As String, ByVal StayTime As Integer)
        Dim messageNotifyForm As MsgNotify = New MsgNotify()
        messageNotifyForm.Width = _WidthMax
        messageNotifyForm.HeightMax = _HeightMax
        messageNotifyForm.StayTime = StayTime * 1000
        messageNotifyForm.ShowMessage(text, caption)
        Try
            'Syslog.WriteLog(text)
        Catch ex As Exception

        End Try
    End Sub

    Public Shared Sub Show(ByVal text As String, ByVal StayTime As Integer)
        Dim messageNotifyForm As MsgNotify = New MsgNotify()
        messageNotifyForm.Width = _WidthMax
        messageNotifyForm.HeightMax = _HeightMax
        messageNotifyForm.StayTime = StayTime * 1000
        messageNotifyForm.ShowMessage(text)
    End Sub
End Class
