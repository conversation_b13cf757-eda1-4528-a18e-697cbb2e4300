﻿Imports System.Data.SqlClient
Imports DataDynamics.ActiveReports
Imports Stimulsoft.Report
Imports Stimulsoft.Report.Components

Public Class Cx_Hzyy1
    Dim My_Dataset As New DataSet
    Public My_Adapter As New SqlDataAdapter
    Public V_Count As Integer
    Public Q As Object  '查询的人数
    Private Sub Cx_Hzyy1_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load

        BaseFunc.BaseFunc.Init_Datetimepicker(DateTimePicker1)
        BaseFunc.BaseFunc.Init_Datetimepicker(DateTimePicker2)

        DateTimePicker1.Value = Format(Now, "yyyy-MM-dd 00:00:00")
        DateTimePicker2.Value = Format(Now, "yyyy-MM-dd 23:59:59")

        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "Select Ks_Code,Ks_Jc,Ks_Name from zd_yyKs", "科室名称", True)

        Dim My_Combo3 As New BaseClass.C_Combo2(Me.C1<PERSON>ombo3, My_Dataset.Tables("科室名称").<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, "Ks_Name", "Ks_Code", 336)
        With My_Combo3
            .Init_TDBCombo()
            .Init_Colum("Ks_Jc", "简称", 60, "左")
            .Init_Colum("Ks_Name", "姓名", 60, "左")
            .Init_Colum("Ks_Code", "", 0, "中")
            .MaxDropDownItems(17)
            .SelectedIndex(-1)
        End With
        C1Combo3.AutoCompletion = False
        C1Combo3.AutoSelect = False
        C1Combo3.Height = 22

        Dim R As String = "select Ry_Jc,Ry_Name,Ry_RyDate,Ry_CyDate,Bl_Code From Bl Where Bl_Code In (Select Bl_Code From Bl_Cf Where Cf_Qr='是' and Cf_date>='" & Format(CDate(Me.DateTimePicker1.Value), "yyyy-MM-dd HH:mm:ss") & "' And Cf_date<='" & Format(CDate(Me.DateTimePicker2.Value), "yyyy-MM-dd HH:mm:ss") & "') "

        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, R, "病人字典", True)

        My_Dataset.Tables("病人字典").PrimaryKey = New DataColumn() {My_Dataset.Tables("病人字典").Columns("Bl_Code")}

        Dim My_Combo As New BaseClass.C_Combo2(Me.C1Combo1, My_Dataset.Tables("病人字典").DefaultView, "Ry_Name", "Bl_Code", 320)
        With My_Combo
            .Init_TDBCombo()
            .Init_Colum("Ry_Jc", "简称", 60, "左")
            .Init_Colum("Ry_Name", "姓名", 60, "左")
            .Init_Colum("Ry_RyDate", "入院日期", 80, "中")
            .Init_Colum("Ry_CyDate", "出院日期", 80, "中")
            .Init_Colum("Bl_Code", "", 0, "中")
            .MaxDropDownItems(17)
            .SelectedIndex(-1)
        End With
        With C1Combo1
            .BorderStyle = BorderStyle.Fixed3D
            .Height = 22
            .Columns(2).NumberFormat = "yyyy-MM-dd"
            .Columns(3).NumberFormat = "yyyy-MM-dd"
        End With
        C1Combo3.AutoCompletion = False
        C1Combo3.AutoSelect = False

        Dim My_Combo2 As New BaseClass.C_Combo1(Me.C1Combo4)

        With My_Combo2
            .Init_TDBCombo()
            .AddItem("A4纸打印")
            .AddItem("热敏纸（80mm）")
            .AddItem("热敏纸（75mm）")
            .AddItem("连续打印一")
            .AddItem("连续打印二")
            .SelectedIndex(0)
        End With
        With C1Combo4
            .DropDownWidth = 136
            .Width = 136
            .ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
            .Height = 22
        End With

        Dim My_Combo5 As New BaseClass.C_Combo1(Me.C1Combo5)

        With My_Combo5
            .Init_TDBCombo()
            .AddItem("9.5")
            .AddItem("10")
            .AddItem("10.5")
            .AddItem("11")
            .AddItem("11.5")
            .AddItem("12")
            .AddItem("12.5")
            .SelectedIndex(0)
        End With
        With C1Combo5
            .DropDownWidth = 138
            .Width = 138
            .ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
            .Height = 22
        End With

        Dim My_Combo1 As New BaseClass.C_Combo1(Me.C1Combo2)

        With My_Combo1
            .Init_TDBCombo()
            .AddItem("用药明细")
            .AddItem("用药汇总")
            .SelectedIndex(0)
        End With
        With C1Combo2
            .DropDownWidth = 138
            .Width = 138
            .Height = 22
        End With
        C1Combo2.Enabled = False

        Dim My_Combo6 As New BaseClass.C_Combo1(Me.C1Combo6)

        With My_Combo6
            .Init_TDBCombo()
            .AddItem("不设置合计类型")
            .AddItem("按字典项目合计")
            .AddItem("按发票项目合计")
            .SelectedIndex(0)
        End With
        With C1Combo6
            .DropDownWidth = 136
            .Width = 136
            .ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
            .Height = 22
        End With
        C1Combo6.Enabled = False

        Dim My_Combo7 As New BaseClass.C_Combo1(Me.C1Combo7)

        With My_Combo7
            .Init_TDBCombo()
            .AddItem("全部患者")
            .AddItem("在院患者")
            .AddItem("出院患者")
            .SelectedIndex(0)
        End With
        With C1Combo7
            .DropDownWidth = 136
            .Width = 136
            .ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
            .Height = 22
        End With





    End Sub


    Private Sub C1Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Button1.Click
        Q = 0


        Dim str As String = ""
        Dim Tj_Str As String = ""
        If C1Combo7.Text = "全部患者" Then
            Tj_Str = " and 1=1 "
        ElseIf C1Combo7.Text = "在院患者" Then
            Tj_Str = " and Ry_CyDate is null "
        ElseIf C1Combo7.Text = "出院患者" Then
            Tj_Str = " and Ry_CyDate is not null "

        End If

        If RadioButton2.Checked = True Then
            If C1Combo6.Text = "按字典项目合计" Or C1Combo6.Text = "不设置合计类型" Then
                If Me.C1Combo2.Text = "用药明细" Then
                    str = "select * from (SELECT  '1' V_Order,Ry_Name,Bl_Cf.Bl_Code,Bl_Cfyp.Xx_Code,Yp_Name,Mx_Gg,Mx_Cd,Mx_XsDw,Cf_Date+Cf_Time as Cf_Date,Cf_Sl,Cf_Dj,Bl_Cfyp.Cf_Money,Ks_Name,Bc_Name,Convert(varchar(10),isnull(Ry_cydate,'2066-06-06'),126) as Ry_CyDate,Ry_CyJsr,Ry_BlCode,Case isnull(Ry_Zyts,9999) When 9999 then Datediff(day,Ry_Rydate,Getdate()) else Ry_ZyTs end as Ry_ZyTs,Bxlb_Name,Cf_Lb,'X'+Dl_Code as Dl_Code  FROM Zd_Bxlb,V_YpKc,Bl_Cfyp,Bl_Cf,Bl,Zd_YyBc,Zd_Yyks Where Zd_Bxlb.Bxlb_Code=Bl.Bxlb_Code and Cf_Qr='是' and Bl.Bc_Code=Zd_YyBc.Bc_Code and Zd_Yyks.Ks_Code=Bl.Ks_Code and Bl.bl_Code=Bl_CF.Bl_Code and Bl_Cfyp.Cf_Code=Bl_Cf.Cf_Code And Cf_date+Cf_Time>='" & Format(CDate(DateTimePicker1.Text), "yyyy-MM-dd HH:mm:ss") & "' And Cf_date+Cf_Time<='" & Format(CDate(DateTimePicker2.Text), "yyyy-MM-dd HH:mm:ss") & "' And Bl_Cfyp.Xx_Code=V_YpKc.Xx_Code And Bl_Cf.Bl_Code like '%" & C1Combo1.SelectedValue & "%' and Bl.Ks_Code like '%" & C1Combo3.SelectedValue & "%' " & Tj_Str & _
              "  union all SELECT  '2' V_Order,Ry_Name,Bl_Cf.Bl_Code,Zd_Ml_Xm3.Xm_Code,Xm_Name,'' Mx_Gg,''Mx_Cd,Xm_Dw as Mx_XsDw,Cf_Date,Cf_Sl,Cf_Dj,Bl_Cfxm.Cf_Money,Ks_Name,Bc_Name,Convert(varchar(10),isnull(Ry_cydate,'2066-06-06'),126) as Ry_CyDate,Ry_CyJsr,Ry_BlCode,Case isnull(Ry_Zyts,9999) When 9999 then Datediff(day,Ry_Rydate,Getdate()) else Ry_ZyTs end as Ry_ZyTs,Bxlb_Name,Cf_Lb,'Y'+Zd_Ml_Xm3.Xmlb_Code as Dl_Code FROM Zd_Bxlb,Zd_Ml_Xm3,Bl_Cfxm,Bl_Cf,Bl ,Zd_YyBc,Zd_Yyks Where  Zd_Bxlb.Bxlb_Code=Bl.Bxlb_Code and Cf_Qr='是' and Bl.Bc_Code=Zd_YyBc.Bc_Code and Zd_Yyks.Ks_Code=Bl.Ks_Code and Bl.bl_Code=Bl_CF.Bl_Code and Bl_Cfxm.Cf_Code=Bl_Cf.Cf_Code And Cf_date+Cf_Time>='" & Format(CDate(DateTimePicker1.Text), "yyyy-MM-dd HH:mm:ss") & "' And Cf_date+Cf_Time<='" & Format(CDate(DateTimePicker2.Text), "yyyy-MM-dd HH:mm:ss") & "' And Bl_Cfxm.Xm_Code=Zd_Ml_Xm3.Xm_Code  And Bl_Cf.Bl_Code like '%" & C1Combo1.SelectedValue & "%'  and Bl.Ks_Code like '%" & C1Combo3.SelectedValue & "%' " & Tj_Str & ")a Order By Bl_Code,Cf_Date"
                Else
                    str = "SELECT  '1' V_Order,Ry_Name,Bl_Cf.Bl_Code,Yp_Name,Mx_Gg,Mx_Cd,Mx_XsDw,Sum(Cf_Sl) AS Cf_Sl,Cf_Dj,Sum(Bl_Cfyp.Cf_Money) as Cf_Money,Ks_Name,Bc_Name,Convert(varchar(10),isnull(Ry_cydate,'2066-06-06'),126) as Ry_CyDate ,Ry_CyJsr,Ry_BlCode,Case isnull(Ry_Zyts,9999) When 9999 then Datediff(day,Ry_Rydate,Getdate()) else Ry_ZyTs end as Ry_ZyTs,Bxlb_Name,Cf_Lb,'X'+Dl_Code as Dl_Code FROM Zd_Bxlb,V_YpKc,Bl_Cfyp,Bl_Cf,Bl,Zd_YyBc,Zd_Yyks Where Zd_Bxlb.Bxlb_Code=Bl.Bxlb_Code and Cf_Qr='是' and Bl.Bc_Code=Zd_YyBc.Bc_Code and Zd_Yyks.Ks_Code=Bl.Ks_Code and Bl.bl_Code=Bl_CF.Bl_Code and Bl_Cfyp.Cf_Code=Bl_Cf.Cf_Code And Cf_date+Cf_Time>='" & Format(CDate(DateTimePicker1.Text), "yyyy-MM-dd HH:mm:ss") & "' And Cf_date+Cf_Time<='" & Format(CDate(DateTimePicker2.Text), "yyyy-MM-dd HH:mm:ss") & "' And Bl_Cfyp.Xx_Code=V_YpKc.Xx_Code And Bl_Cf.Bl_Code like '%" & C1Combo1.SelectedValue & "%' and Bl.Ks_Code like '%" & C1Combo3.SelectedValue & "%' " & Tj_Str & " Group By Ry_Name,Ry_BlCode,Ry_Zyts,Bxlb_Name,Ry_Rydate,Bl_Cf.Bl_Code,Yp_Name,Cf_Lb,Cf_Dj,Mx_Gg,Mx_Cd,Mx_XsDw,Ks_Name,Bc_Name,Ry_CyDate,Dl_Code,Ry_CyJsr " & _
                       " union all SELECT  '2' V_Order,Ry_Name,Bl_Cf.Bl_Code,Xm_Name,''Mx_Gg,''Mx_Cd,Xm_Dw as Mx_XsDw,Sum(Cf_Sl) AS Cf_Sl,Cf_Dj,Sum(Bl_Cfxm.Cf_Money) as Cf_Money,Ks_Name,Bc_Name,Convert(varchar(10),isnull(Ry_cydate,'2066-06-06'),126) as Ry_CyDate,Ry_CyJsr,Ry_BlCode,Case isnull(Ry_Zyts,9999) When 9999 then Datediff(day,Ry_Rydate,Getdate()) else Ry_ZyTs end as Ry_ZyTs,Bxlb_Name,Cf_Lb,'Y'+Xmlb_Code as Dl_Code  FROM Zd_Bxlb,Zd_Ml_Xm3,Bl_Cfxm,Bl_Cf,Bl,Zd_YyBc,Zd_Yyks  Where   Zd_Bxlb.Bxlb_Code=Bl.Bxlb_Code and Cf_Qr='是' and Bl.Bc_Code=Zd_YyBc.Bc_Code and Zd_Yyks.Ks_Code=Bl.Ks_Code and Bl.bl_Code=Bl_CF.Bl_Code and Bl_Cfxm.Cf_Code=Bl_Cf.Cf_Code And Cf_date+Cf_Time>='" & Format(CDate(DateTimePicker1.Text), "yyyy-MM-dd HH:mm:ss") & "' And Cf_date+Cf_Time<='" & Format(CDate(DateTimePicker2.Text), "yyyy-MM-dd HH:mm:ss") & "' And Bl_Cfxm.Xm_Code=Zd_Ml_Xm3.Xm_Code  And Bl_Cf.Bl_Code like '%" & C1Combo1.SelectedValue & "%'  and Bl.Ks_Code like '%" & C1Combo3.SelectedValue & "%' " & Tj_Str & " Group By Ry_Name,Ry_BlCode,Ry_Zyts,Bxlb_Name,Ry_Rydate,Bl_Cf.bl_Code,Xm_Name,Cf_Lb,Cf_Dj,Xm_Dw,Ks_Name,Bc_Name,Ry_CyDate,Xmlb_Code,Ry_CyJsr Order By Bl_Cf.Bl_Code,V_Order,Ry_Name"

                End If
            ElseIf C1Combo6.Text = "按发票项目合计" Then
                If Me.C1Combo2.Text = "用药明细" Then
                    str = "SELECT  '1' V_Order,Ry_Name,Bl_Cf.Bl_Code,Bl_Cfyp.Xx_Code,Yp_Name,Mx_Gg,Mx_Cd,Mx_XsDw,Cf_Date+Cf_Time as Cf_Date,Cf_Sl,Cf_Dj,Bl_Cfyp.Cf_Money,Ks_Name,Bc_Name,Convert(varchar(10),isnull(Ry_cydate,'2066-06-06'),126) as Ry_CyDate,Ry_CyJsr,Ry_BlCode,Case isnull(Ry_Zyts,9999) When 9999 then Datediff(day,Ry_Rydate,Getdate()) else Ry_ZyTs end as Ry_ZyTs,Bxlb_Name,'X'+Dl_Code as Dl_Code , Cf_Lb FROM Zd_Bxlb,V_YpKc,Bl_Cfyp,Bl_Cf,Bl,Zd_YyBc,Zd_Yyks Where Zd_Bxlb.Bxlb_Code=Bl.Bxlb_Code and Cf_Qr='是' and Bl.Bc_Code=Zd_YyBc.Bc_Code and Zd_Yyks.Ks_Code=Bl.Ks_Code and Bl.bl_Code=Bl_CF.Bl_Code and Bl_Cfyp.Cf_Code=Bl_Cf.Cf_Code And Cf_date+Cf_Time>='" & Format(CDate(DateTimePicker1.Text), "yyyy-MM-dd HH:mm:ss") & "' And Cf_date+Cf_Time<='" & Format(CDate(DateTimePicker2.Text), "yyyy-MM-dd HH:mm:ss") & "' And Bl_Cfyp.Xx_Code=V_YpKc.Xx_Code And Bl_Cf.Bl_Code like '%" & C1Combo1.SelectedValue & "%' and Bl.Ks_Code like '%" & C1Combo3.SelectedValue & "%' " & Tj_Str & _
              "  union all SELECT  '2' V_Order,Ry_Name,Bl_Cf.Bl_Code,Zd_Ml_Xm3.Xm_Code,Xm_Name,'' Mx_Gg,''Mx_Cd,Xm_Dw as Mx_XsDw,Cf_Date,Cf_Sl,Cf_Dj,Bl_Cfxm.Cf_Money,Ks_Name,Bc_Name,Convert(varchar(10),isnull(Ry_cydate,'2066-06-06'),126) as Ry_CyDate,Ry_CyJsr,Ry_BlCode,Case isnull(Ry_Zyts,9999) When 9999 then Datediff(day,Ry_Rydate,Getdate()) else Ry_ZyTs end as Ry_ZyTs,Bxlb_Name,'Y'+Zd_Jkfl2.Lb_Code as Dl_Code,Lb_Name as Cf_Lb FROM Zd_Bxlb,Zd_Ml_Xm3,Bl_Cfxm,Bl_Cf,Bl ,Zd_YyBc,Zd_Yyks,Zd_Jkfl1,Zd_Jkfl2 Where Zd_Jkfl1.Lb_Code=Zd_Jkfl2.Lb_Code and Zd_Jkfl2.Mx_Code=Zd_Ml_Xm3.Xm_Code and Zd_Bxlb.Bxlb_Code=Bl.Bxlb_Code and Cf_Qr='是' and Bl.Bc_Code=Zd_YyBc.Bc_Code and Zd_Yyks.Ks_Code=Bl.Ks_Code and Bl.bl_Code=Bl_CF.Bl_Code and Bl_Cfxm.Cf_Code=Bl_Cf.Cf_Code And Cf_date+Cf_Time>='" & Format(CDate(DateTimePicker1.Text), "yyyy-MM-dd HH:mm:ss") & "' And Cf_date+Cf_Time<='" & Format(CDate(DateTimePicker2.Text), "yyyy-MM-dd HH:mm:ss") & "' And Bl_Cfxm.Xm_Code=Zd_Ml_Xm3.Xm_Code  And Bl_Cf.Bl_Code like '%" & C1Combo1.SelectedValue & "%'  and Bl.Ks_Code like '%" & C1Combo3.SelectedValue & "%' " & Tj_Str & " Order By Bl_Cf.Bl_Code,Cf_Date"
                Else
                    str = "SELECT  '1' V_Order,Ry_Name,Bl_Cf.Bl_Code,Yp_Name,Mx_Gg,Mx_Cd,Mx_XsDw,Sum(Cf_Sl) AS Cf_Sl,Cf_Dj,Sum(Bl_Cfyp.Cf_Money) as Cf_Money,Ks_Name,Bc_Name,Convert(varchar(10),isnull(Ry_cydate,'2066-06-06'),126) as Ry_CyDate ,Ry_CyJsr,Ry_BlCode,Case isnull(Ry_Zyts,9999) When 9999 then Datediff(day,Ry_Rydate,Getdate()) else Ry_ZyTs end as Ry_ZyTs,Bxlb_Name,'X'+Dl_Code as Dl_Code , Cf_Lb FROM Zd_Bxlb,V_YpKc,Bl_Cfyp,Bl_Cf,Bl,Zd_YyBc,Zd_Yyks Where Zd_Bxlb.Bxlb_Code=Bl.Bxlb_Code and Cf_Qr='是' and Bl.Bc_Code=Zd_YyBc.Bc_Code and Zd_Yyks.Ks_Code=Bl.Ks_Code and Bl.bl_Code=Bl_CF.Bl_Code and Bl_Cfyp.Cf_Code=Bl_Cf.Cf_Code And Cf_date+Cf_Time>='" & Format(CDate(DateTimePicker1.Text), "yyyy-MM-dd HH:mm:ss") & "' And Cf_date+Cf_Time<='" & Format(CDate(DateTimePicker2.Text), "yyyy-MM-dd HH:mm:ss") & "' And Bl_Cfyp.Xx_Code=V_YpKc.Xx_Code And Bl_Cf.Bl_Code like '%" & C1Combo1.SelectedValue & "%' and Bl.Ks_Code like '%" & C1Combo3.SelectedValue & "%' " & Tj_Str & " Group By Ry_Name,Ry_BlCode,Ry_Zyts,Bxlb_Name,Ry_Rydate,Bl_Cf.Bl_Code,Yp_Name,Cf_Lb,Cf_Dj,Mx_Gg,Mx_Cd,Mx_XsDw,Ks_Name,Bc_Name,Ry_CyDate,Dl_Code,Ry_CyJsr " & _
                       " union all SELECT  '2' V_Order,Ry_Name,Bl_Cf.Bl_Code,Xm_Name,''Mx_Gg,''Mx_Cd,Xm_Dw as Mx_XsDw,Sum(Cf_Sl) AS Cf_Sl,Cf_Dj,Sum(Bl_Cfxm.Cf_Money) as Cf_Money,Ks_Name,Bc_Name,Convert(varchar(10),isnull(Ry_cydate,'2066-06-06'),126) as Ry_CyDate,Ry_CyJsr,Ry_BlCode,Case isnull(Ry_Zyts,9999) When 9999 then Datediff(day,Ry_Rydate,Getdate()) else Ry_ZyTs end as Ry_ZyTs,Bxlb_Name,'Y'+Zd_Jkfl2.Lb_Code as Dl_Code,Lb_Name as Cf_Lb  FROM Zd_Bxlb,Zd_Ml_Xm3,Bl_Cfxm,Bl_Cf,Bl,Zd_YyBc,Zd_Yyks,Zd_Jkfl1,Zd_Jkfl2 Where Zd_Jkfl1.Lb_Code=Zd_Jkfl2.Lb_Code and Zd_Jkfl2.Mx_Code=Zd_Ml_Xm3.Xm_Code and Zd_Bxlb.Bxlb_Code=Bl.Bxlb_Code and Cf_Qr='是' and Bl.Bc_Code=Zd_YyBc.Bc_Code and Zd_Yyks.Ks_Code=Bl.Ks_Code and Bl.bl_Code=Bl_CF.Bl_Code and Bl_Cfxm.Cf_Code=Bl_Cf.Cf_Code And Cf_date+Cf_Time>='" & Format(CDate(DateTimePicker1.Text), "yyyy-MM-dd HH:mm:ss") & "' And Cf_date+Cf_Time<='" & Format(CDate(DateTimePicker2.Text), "yyyy-MM-dd HH:mm:ss") & "' And Bl_Cfxm.Xm_Code=Zd_Ml_Xm3.Xm_Code  And Bl_Cf.Bl_Code like '%" & C1Combo1.SelectedValue & "%'  and Bl.Ks_Code like '%" & C1Combo3.SelectedValue & "%' " & Tj_Str & " Group By Ry_Name,Ry_BlCode,Ry_Zyts,Bxlb_Name,Ry_Rydate,Bl_Cf.bl_Code,Xm_Name,Cf_Dj,Xm_Dw,Ks_Name,Bc_Name,Ry_CyDate,Zd_Jkfl2.Lb_Code,Lb_Name,Ry_CyJsr Order By Bl_Cf.Bl_Code,V_Order,Ry_Name,Cf_Lb"

                End If
            End If

            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, str, "标准版", True)


            Dim ing As String = "select Isnull(Jf_Money,0)-isnull(Xf_Money,0) AS New_Money,Isnull(Jf_Money,0) Jf_Money,isnull(Xf_Money,0) as Xf_Money,a.Bl_Code From Bl, (Select Sum(Cf_Money) AS Xf_Money,Bl_Code From Bl_Cf where Cf_Qr='是' Group By Bl_Code)a left join (Select Sum(Jf_Money) AS Jf_Money,Bl_Code From Bl_Jf Group By Bl_Code) b on a.Bl_Code = b.Bl_Code where Bl.Bl_Code=A.bl_Code  "

            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, ing, "押金及余额", True)

            Dim StiRpt As New StiReport

            If C1Combo4.Text = "A4纸打印" Then
                StiRpt.Load(".\Rpt\患者用药清单标准版.mrt")
                StiRpt.ReportName = "患者用药清单标准版"
                StiRpt.RegData(My_Dataset.Tables("标准版"))
                StiRpt.RegData(My_Dataset.Tables("押金及余额"))
                StiRpt.Pages(0).UnlimitedHeight = False
                StiRpt.Pages(0).UnlimitedBreakable = True
                StiRpt.Pages(0).Margins.Top = 1
                StiRpt.Pages(0).Margins.Bottom = 1
            ElseIf C1Combo4.Text = "连续打印一" Then
                StiRpt.Load(".\Rpt\患者用药清单标准版.mrt")
                StiRpt.ReportName = "患者用药清单标准版"
                StiRpt.RegData(My_Dataset.Tables("标准版"))
                StiRpt.RegData(My_Dataset.Tables("押金及余额"))
                StiRpt.Pages(0).UnlimitedHeight = True
                StiRpt.Pages(0).UnlimitedBreakable = False
            ElseIf C1Combo4.Text = "连续打印二" Then
                If C1Combo1.Text = "" Then
                    Q = My_Dataset.Tables("病人字典").Rows.Count
                Else
                    Q = 1
                End If

                StiRpt.Load(".\Rpt\患者用药清单标准版二.mrt")
                StiRpt.ReportName = "患者用药清单标准版"
                StiRpt.RegData(My_Dataset.Tables("标准版"))
                StiRpt.RegData(My_Dataset.Tables("押金及余额"))
                StiRpt.Pages(0).PaperSize = Printing.PaperKind.Custom

                Dim P_H As Integer = Q * (3.8 + 0.5) + My_Dataset.Tables("标准版").Rows.Count * 0.6 + 1
                Dim prt As New System.Drawing.Printing.PrinterSettings
                If prt.DefaultPageSettings.PrinterSettings.PrinterName.Contains("Epson LQ-1600K") And P_H > 231 Then
                    Dim P As Integer
                    For P = 2 To 50
                        If P_H / P < 231 Then
                            StiRpt.Pages(0).Margins.Top = 1.5
                            StiRpt.Pages(0).PageHeight = P_H / P + 2.0048
                            Exit For
                        End If
                    Next
                Else

                    StiRpt.Pages(0).PageHeight = P_H


                End If
            End If

            '表头字体

            Call ChangeFontSize(StiRpt, "Text7", C1Combo5.Text)
            Call ChangeFontSize(StiRpt, "Text27", C1Combo5.Text)
            Call ChangeFontSize(StiRpt, "Text8", C1Combo5.Text)
            Call ChangeFontSize(StiRpt, "Text2", C1Combo5.Text)
            Call ChangeFontSize(StiRpt, "Text9", C1Combo5.Text)
            Call ChangeFontSize(StiRpt, "Text10", C1Combo5.Text)
            Call ChangeFontSize(StiRpt, "Text11", C1Combo5.Text)
            Call ChangeFontSize(StiRpt, "Text5", C1Combo5.Text)
            Call ChangeFontSize(StiRpt, "Text12", C1Combo5.Text)
            Call ChangeFontSize(StiRpt, "Text28", C1Combo5.Text)
            Call ChangeFontSize(StiRpt, "Text16", C1Combo5.Text)
            Call ChangeFontSize(StiRpt, "Text18", C1Combo5.Text)
            Call ChangeFontSize(StiRpt, "Text30", C1Combo5.Text)
            Call ChangeFontSize(StiRpt, "Text20", C1Combo5.Text)
            Call ChangeFontSize(StiRpt, "Text22", C1Combo5.Text)

            '表体字体
            Call ChangeFontSize(StiRpt, "Text4", C1Combo5.Text)
            Call ChangeFontSize(StiRpt, "Text6", C1Combo5.Text)
            Call ChangeFontSize(StiRpt, "Text13", C1Combo5.Text)
            Call ChangeFontSize(StiRpt, "Text15", C1Combo5.Text)
            Call ChangeFontSize(StiRpt, "Text17", C1Combo5.Text)
            Call ChangeFontSize(StiRpt, "Text31", C1Combo5.Text)
            Call ChangeFontSize(StiRpt, "Text19", C1Combo5.Text)
            Call ChangeFontSize(StiRpt, "Text21", C1Combo5.Text)
            '表尾字体
            Call ChangeFontSize(StiRpt, "Text23", C1Combo5.Text)
            Call ChangeFontSize(StiRpt, "Text24", C1Combo5.Text)
            Call ChangeFontSize(StiRpt, "Text14", C1Combo5.Text)
            Call ChangeFontSize(StiRpt, "Text26", C1Combo5.Text)
            Call ChangeFontSize(StiRpt, "Text25", C1Combo5.Text)
            If C1Combo6.Text = "不设置合计类型" Then
                StiRpt.GetComponents("GroupHeaderBand2").Enabled = False
                StiRpt.GetComponents("GroupFooterBand2").Enabled = False
            End If
            StiRpt.Compile()

            StiRpt("查询时间") = Format(DateTimePicker1.Value, "yyyy年MM月dd日 HH:mm:ss") & "  至  " & Format(DateTimePicker2.Value, "yyyy年MM月dd日 HH:mm:ss")
            StiRpt("经手人") = HisVar.HisVar.JsrName
            StiRpt("打印时间") = Format(Now, "yyyy年MM月dd日")
            'StiRpt.Design()
            StiRpt.Show()

        Else
            str = "SELECT  '1' V_Order,Ry_Name,Convert(varchar(10),isnull(Ry_cydate,'2066-06-06'),126) as Ry_CyDate,Ry_CyJsr,Bl_Cf.Bl_Code,Bl_Cfyp.Xx_Code,Yp_Name,Sum(Cf_Sl) AS Cf_Sl,Cf_Dj,Sum(Bl_Cfyp.Cf_Money) as Cf_Money,Cf_Lb,Ks_Name,Bc_Name,Ry_BlCode,Case isnull(Ry_Zyts,9999) When 9999 then Datediff(day,Ry_Rydate,Getdate()) else Ry_ZyTs end as Ry_ZyTs,Bxlb_Name FROM Zd_Bxlb,V_YpKc,Bl_Cfyp,Bl_Cf,Bl,Zd_YyBc,Zd_Yyks Where Zd_Bxlb.Bxlb_Code=Bl.Bxlb_Code and Cf_Qr='是' and Bl.Bc_Code=Zd_YyBc.Bc_Code and Zd_Yyks.Ks_Code=Bl.Ks_Code and Bl.bl_Code=Bl_CF.Bl_Code and Bl_Cfyp.Cf_Code=Bl_Cf.Cf_Code And Cf_date+Cf_Time>='" & Format(CDate(DateTimePicker1.Text), "yyyy-MM-dd HH:mm:ss") & "' And Cf_date+Cf_Time<='" & Format(CDate(DateTimePicker2.Text), "yyyy-MM-dd HH:mm:ss") & "' And Bl_Cfyp.Xx_Code=V_YpKc.Xx_Code And Bl_Cf.Bl_Code like '%" & C1Combo1.SelectedValue & "%' and Bl.Ks_Code like '%" & C1Combo3.SelectedValue & "%' " & Tj_Str & " Group By Ry_Name,Ry_BlCode,Ry_Zyts,Bxlb_Name,Ry_Rydate,Bl_Cf.Bl_Code,Bl_Cfyp.Xx_Code,Yp_Name,Cf_Lb,Cf_Dj,Ks_Name,Bc_Name,Ry_CyDate,Ry_CyJsr " & _
        " union all SELECT  '2' V_Order,Ry_Name,Convert(varchar(10),isnull(Ry_cydate,'2066-06-06'),126) as Ry_CyDate,Ry_CyJsr,Bl_Cf.Bl_Code,Bl_Cfxm.Xm_Code,Xm_Name,Sum(Cf_Sl) AS Cf_Sl,Cf_Dj,Sum(Bl_Cfxm.Cf_Money) as Cf_Money,Cf_Lb,Ks_Name,Bc_Name,Ry_BlCode,Case isnull(Ry_Zyts,9999) When 9999 then Datediff(day,Ry_Rydate,Getdate()) else Ry_ZyTs end as Ry_ZyTs,Bxlb_Name FROM Zd_Bxlb,Zd_Ml_Xm3,Bl_Cfxm,Bl_Cf,Bl,Zd_YyBc,Zd_Yyks Where Zd_Bxlb.Bxlb_Code=Bl.Bxlb_Code and Cf_Qr='是' and Bl.Bc_Code=Zd_YyBc.Bc_Code and Zd_Yyks.Ks_Code=Bl.Ks_Code and Bl.bl_Code=Bl_CF.Bl_Code and Bl_Cfxm.Cf_Code=Bl_Cf.Cf_Code And Cf_date+Cf_Time>='" & Format(CDate(DateTimePicker1.Text), "yyyy-MM-dd HH:mm:ss") & "' And Cf_date+Cf_Time<='" & Format(CDate(DateTimePicker2.Text), "yyyy-MM-dd HH:mm:ss") & "' And Bl_Cfxm.Xm_Code=Zd_Ml_Xm3.Xm_Code  And Bl_Cf.Bl_Code like '%" & C1Combo1.SelectedValue & "%'  and Bl.Ks_Code like '%" & C1Combo3.SelectedValue & "%' " & Tj_Str & " Group By Ry_Name,Ry_BlCode,Ry_Zyts,Bxlb_Name,Ry_Rydate,Bl_Cf.Bl_Code,Bl_Cfxm.Xm_Code,Xm_Name,Cf_Lb,Cf_Dj,Ks_Name,Bc_Name,Ry_CyDate,Ry_CyJsr Order By Bl_Cf.Bl_Code,V_Order,Ry_Name,Cf_Lb"


            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, str, "简化版", True)

            Dim I As Integer
            Dim M As Integer = My_Dataset.Tables("简化版").Rows.Count - 1
            Dim K As Integer = 0  '同一编码的行数
            Dim Str1 As String
            Dim Row1 As DataRow
            Dim Row2 As DataRow
            Dim V_NewRow As DataRow
            For I = 0 To M
                K = K + 1
                Row1 = My_Dataset.Tables("简化版").Rows.Item(I)
                If I = M Then
                    Str1 = True
                Else
                    Row2 = My_Dataset.Tables("简化版").Rows.Item(I + 1)
                    Str1 = Row1.Item("Bl_Code") <> Row2.Item("Bl_Code")
                End If
                If Str1 Then
                    If K Mod 2 <> 0 Then
                        V_NewRow = My_Dataset.Tables("简化版").NewRow

                        With V_NewRow
                            .Item("V_Order") = Row1.Item("V_Order")
                            .Item("Ry_CyDate") = Row1.Item("Ry_CyDate")
                            .Item("Ry_CyJsr") = Row1.Item("Ry_CyJsr")
                            .Item("Bl_Code") = Row1.Item("Bl_Code")
                            .Item("Yp_Name") = DBNull.Value
                            .Item("Cf_Sl") = DBNull.Value
                            .Item("Cf_Dj") = DBNull.Value
                            .Item("Cf_Money") = DBNull.Value
                        End With
                        My_Dataset.Tables("简化版").Rows.Add(V_NewRow)
                        V_NewRow.AcceptChanges()
                    End If
                    K = 0
                    Q = Q + 1 '人数
                End If

            Next
            My_Dataset.Tables("简化版").DefaultView.Sort = "Bl_Code"



            Dim ing As String = "select Isnull(Jf_Money,0)-isnull(Xf_Money,0) AS New_Money,Isnull(Jf_Money,0) Jf_Money,isnull(Xf_Money,0) as Xf_Money,a.Bl_Code From Bl, (Select Sum(Cf_Money) AS Xf_Money,Bl_Code From Bl_Cf where Cf_Qr='是' Group By Bl_Code)a left join (Select Sum(Jf_Money) AS Jf_Money,Bl_Code From Bl_Jf Group By Bl_Code) b on a.Bl_Code = b.Bl_Code where Bl.Bl_Code=A.bl_Code "

            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, ing, "押金及余额", True)

            Dim StiRpt As New StiReport


            If C1Combo4.Text = "A4纸打印" Then
                StiRpt.Load(".\Rpt\患者用药清单简化版.mrt")
                StiRpt.ReportName = "患者用药清单简化版"
                StiRpt.RegData(My_Dataset.Tables("简化版"))
                StiRpt.RegData(My_Dataset.Tables("押金及余额"))
                StiRpt.Pages(0).UnlimitedHeight = False
                StiRpt.Pages(0).UnlimitedBreakable = True
                StiRpt.Pages(0).Margins.Top = 1
                StiRpt.Pages(0).Margins.Bottom = 1
            ElseIf C1Combo4.Text = "热敏纸（80mm）" Or C1Combo4.Text = "热敏纸（75mm）" Then
                If C1Combo4.Text = "热敏纸（80mm）" Then
                    StiRpt.Load(".\Rpt\患者用药清单热敏打印.mrt")
                ElseIf C1Combo4.Text = "热敏纸（75mm）" Then
                    StiRpt.Load(".\Rpt\患者用药清单热敏打印75mm.mrt")
                End If

                StiRpt.ReportName = "患者用药清单热敏打印版"
                StiRpt.RegData(My_Dataset.Tables("简化版"))
                StiRpt.Pages(0).PaperSize = Printing.PaperKind.Custom

                Dim P_H As Integer = Q * (3.8 + 0.5) + (My_Dataset.Tables("简化版").Rows.Count) * (0.6) + 1
                StiRpt.Pages(0).PageHeight = P_H
                StiRpt.Compile()

                StiRpt("查询时间") = Format(DateTimePicker1.Value, "yyyy-MM-dd HH:mm") & "至" & Format(DateTimePicker2.Value, "yyyy-MM-dd HH:mm")

                'StiRpt.Design()
                StiRpt.Show()
                Exit Sub
            ElseIf C1Combo4.Text = "连续打印一" Then
                StiRpt.Load(".\Rpt\患者用药清单简化版.mrt")
                StiRpt.ReportName = "患者用药清单简化版"
                StiRpt.RegData(My_Dataset.Tables("简化版"))
                StiRpt.RegData(My_Dataset.Tables("押金及余额"))
                StiRpt.Pages(0).UnlimitedHeight = True
                StiRpt.Pages(0).UnlimitedBreakable = False
            ElseIf C1Combo4.Text = "连续打印二" Then




                StiRpt.Load(".\Rpt\患者用药清单简化版二.mrt")
                StiRpt.ReportName = "患者用药清单简化版"
                StiRpt.RegData(My_Dataset.Tables("简化版"))
                StiRpt.RegData(My_Dataset.Tables("押金及余额"))
                StiRpt.Pages(0).PaperSize = Printing.PaperKind.Custom

                Dim P_H As Integer = Q * (3.8 + 0.5) + (My_Dataset.Tables("简化版").Rows.Count / 2) * (0.6) + 2

                Dim prt As New System.Drawing.Printing.PrinterSettings
                If prt.DefaultPageSettings.PrinterSettings.PrinterName.Contains("Epson LQ-1600K") And P_H > 231 Then
                    Dim P As Integer
                    For P = 2 To 50
                        If P_H / P < 231 Then
                            StiRpt.Pages(0).Margins.Top = 1.5
                            StiRpt.Pages(0).PageHeight = P_H / P + 2.5
                            Exit For
                        End If
                    Next
                Else

                    StiRpt.Pages(0).PageHeight = P_H


                End If


            End If

            '表头字体

            Call ChangeFontSize(StiRpt, "Text7", C1Combo5.Text)
            Call ChangeFontSize(StiRpt, "Text27", C1Combo5.Text)
            Call ChangeFontSize(StiRpt, "Text8", C1Combo5.Text)
            Call ChangeFontSize(StiRpt, "Text2", C1Combo5.Text)
            Call ChangeFontSize(StiRpt, "Text9", C1Combo5.Text)
            Call ChangeFontSize(StiRpt, "Text10", C1Combo5.Text)
            Call ChangeFontSize(StiRpt, "Text11", C1Combo5.Text)
            Call ChangeFontSize(StiRpt, "Text5", C1Combo5.Text)
            Call ChangeFontSize(StiRpt, "Text18", C1Combo5.Text)
            Call ChangeFontSize(StiRpt, "Text20", C1Combo5.Text)
            Call ChangeFontSize(StiRpt, "Text22", C1Combo5.Text)
            Call ChangeFontSize(StiRpt, "Text6", C1Combo5.Text)
            Call ChangeFontSize(StiRpt, "Text12", C1Combo5.Text)
            Call ChangeFontSize(StiRpt, "Text13", C1Combo5.Text)
            Call ChangeFontSize(StiRpt, "Text14", C1Combo5.Text)

            '表体字体
            Call ChangeFontSize(StiRpt, "Text4", C1Combo5.Text)
            Call ChangeFontSize(StiRpt, "Text17", C1Combo5.Text)
            Call ChangeFontSize(StiRpt, "Text19", C1Combo5.Text)
            Call ChangeFontSize(StiRpt, "Text21", C1Combo5.Text)
            '表尾字体
            Call ChangeFontSize(StiRpt, "Text23", C1Combo5.Text)
            Call ChangeFontSize(StiRpt, "Text24", C1Combo5.Text)
            Call ChangeFontSize(StiRpt, "Text15", C1Combo5.Text)
            Call ChangeFontSize(StiRpt, "Text26", C1Combo5.Text)
            Call ChangeFontSize(StiRpt, "Text25", C1Combo5.Text)

            StiRpt.Compile()

            StiRpt("查询时间") = Format(DateTimePicker1.Value, "yyyy年MM月dd日 HH:mm:ss") & "  至  " & Format(DateTimePicker2.Value, "yyyy年MM月dd日 HH:mm:ss")
            StiRpt("经手人") = HisVar.HisVar.JsrName
            StiRpt("打印时间") = Format(Now, "yyyy年MM月dd日")
            'StiRpt.Design()
            StiRpt.Show()



        End If


    End Sub
    Private Sub ChangeFontSize(ByVal StiRpt As StiReport, ByVal Text_Name As String, ByVal Siz As String)

        Dim Font As IStiFont = TryCast(StiRpt.Pages(0).GetComponents.Item(Text_Name), StiText)

        Font.Font = Stimulsoft.Base.Drawing.StiFontUtils.ChangeFontSize(Font.Font, Siz)

    End Sub

#Region "输入法设置"
    '中文
    Private Sub C1TextBox1_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Combo2.GotFocus
        InputLanguage.CurrentInputLanguage = HisVar.HisVar.InputCode
    End Sub

    Private Sub C1Numeric1_KeyDown(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Combo1.GotFocus, DateTimePicker1.GotFocus, DateTimePicker2.GotFocus
        InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages(0)
    End Sub

#End Region

    Private Sub DateTimePicker1_ValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles DateTimePicker1.ValueChanged
        Dim R As String = "select Ry_Jc,Ry_Name,Ry_RyDate,Ry_CyDate,Bl_Code From Bl Where Bl_Code In (Select Bl_Code From Bl_Cf Where Cf_Qr='是' and Cf_date>='" & Format(CDate(Me.DateTimePicker1.Value), "yyyy-MM-dd HH:mm:ss") & "' And Cf_date<='" & Format(CDate(Me.DateTimePicker2.Value), "yyyy-MM-dd HH:mm:ss") & "')   and Ks_Code like '%" & C1Combo3.SelectedValue & "%'"
        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, R, "病人字典", True)
        My_Dataset.Tables("病人字典").PrimaryKey = New DataColumn() {My_Dataset.Tables("病人字典").Columns("Bl_Code")}
        C1Combo1.SelectedIndex = -1
    End Sub

    Private Sub RadioButton1_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles RadioButton1.CheckedChanged
        If Me.Visible = False Then Exit Sub
        If Me.RadioButton1.Checked = True Then
            C1Combo2.SelectedIndex = 1
            C1Combo2.Enabled = False
            C1Combo6.SelectedIndex = 0
            C1Combo6.Enabled = False
        End If
    End Sub

    Private Sub RadioButton2_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles RadioButton2.CheckedChanged
        If Me.Visible = False Then Exit Sub
        If Me.RadioButton2.Checked = True Then
            C1Combo2.SelectedIndex = 0
            C1Combo2.Enabled = True
            C1Combo6.SelectedIndex = 0
            C1Combo6.Enabled = True
        End If
    End Sub

    Private Sub C1Combo1_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles C1Combo1.KeyUp

        If (e.KeyValue > 47 And e.KeyValue < 58) Or (e.KeyValue > 96 And e.KeyValue < 123) Or (e.KeyValue > 64 And e.KeyValue < 91) Or (e.KeyValue = 8) Or (e.KeyValue = 189) Or (e.KeyValue = 190) Then
            Dim s As String = C1Combo1.Text
            If C1Combo1.Text = "" Then

                If C1Combo7.Text = "全部患者" Then
                    C1Combo1.DataSource.RowFilter = ""
                ElseIf C1Combo7.Text = "在院患者" Then
                    C1Combo1.DataSource.RowFilter = "  Ry_CyDate is null "
                ElseIf C1Combo7.Text = "出院患者" Then
                    C1Combo1.DataSource.RowFilter = "  Ry_CyDate is not null "

                End If

            Else
                If C1Combo7.Text = "全部患者" Then
                    C1Combo1.DataSource.RowFilter = "Ry_Jc like '*" & C1Combo1.Text.Replace("*", "[*]") & "*'"
                ElseIf C1Combo7.Text = "在院患者" Then
                    C1Combo1.DataSource.RowFilter = "  Ry_CyDate is null and Ry_Jc like '*" & C1Combo1.Text.Replace("*", "[*]") & "*'"
                ElseIf C1Combo7.Text = "出院患者" Then
                    C1Combo1.DataSource.RowFilter = "  Ry_CyDate is not null and Ry_Jc like '*" & C1Combo1.Text.Replace("*", "[*]") & "*'"

                End If
            End If
            If (e.KeyValue = 8) Then
                C1Combo1.DroppedDown = False
                C1Combo1.DroppedDown = True
            End If

            C1Combo1.Text = s
            C1Combo1.SelectionStart = C1Combo1.Text.Length
            C1Combo1.SelectionLength = 0
        End If

    End Sub

    Private Sub C1Combo1_KeyDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles C1Combo1.KeyDown
        If e.KeyValue = 13 Then
            If C1Combo1.WillChangeToIndex < 1 Then
                If (CType(C1Combo1.DataSource, DataView).Count) = 0 Then
                    MsgBox("患者: '" + Me.C1Combo1.Text + "' 不存在！", MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
                    System.Windows.Forms.SendKeys.Send("^{A}")
                    Exit Sub
                End If
                C1Combo1.SelectedIndex = -1
                C1Combo1.SelectedIndex = 0
            Else
                Dim index As Integer
                index = C1Combo1.WillChangeToIndex
                C1Combo1.SelectedIndex = -1
                C1Combo1.SelectedIndex = index
            End If
            e.Handled = True
            System.Windows.Forms.SendKeys.Send("{Tab}")
        End If
    End Sub

  

    Private Sub C1Combo3_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1Combo3.TextChanged
       
        Dim R As String = "select Ry_Jc,Ry_Name,Ry_RyDate,Ry_CyDate,Bl_Code From Bl Where Bl_Code In (Select Bl_Code From Bl_Cf Where Cf_Qr='是' and  Cf_date>='" & Format(CDate(Me.DateTimePicker1.Value), "yyyy-MM-dd HH:mm:ss") & "' And Cf_date<='" & Format(CDate(Me.DateTimePicker2.Value), "yyyy-MM-dd HH:mm:ss") & "')   and Ks_Code like '%" & C1Combo3.SelectedValue & "%'"
        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, R, "病人字典", True)
        My_Dataset.Tables("病人字典").PrimaryKey = New DataColumn() {My_Dataset.Tables("病人字典").Columns("Bl_Code")}
        C1Combo1.SelectedIndex = -1
    End Sub

    Private Sub C1Combo3_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles C1Combo3.KeyUp

        If (e.KeyValue > 47 And e.KeyValue < 58) Or (e.KeyValue > 96 And e.KeyValue < 123) Or (e.KeyValue > 64 And e.KeyValue < 91) Or (e.KeyValue = 8) Or (e.KeyValue = 189) Or (e.KeyValue = 190) Then
            Dim s As String = C1Combo3.Text
            If C1Combo3.Text = "" Then
                C1Combo3.DataSource.RowFilter = ""
            Else
                C1Combo3.DataSource.RowFilter = "Ks_Jc like '*" & C1Combo3.Text.Replace("*", "[*]") & "*'"
            End If
            If (e.KeyValue = 8) Then
                C1Combo3.DroppedDown = False
                C1Combo3.DroppedDown = True
            End If

            C1Combo3.Text = s
            C1Combo3.SelectionStart = C1Combo3.Text.Length
            C1Combo3.SelectionLength = 0
        End If

    End Sub

    Private Sub C1Combo3_KeyDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles C1Combo3.KeyDown
        If e.KeyValue = 13 Then
            If C1Combo3.WillChangeToIndex < 1 Then
                If (CType(C1Combo3.DataSource, DataView).Count) = 0 Then
                    MsgBox("科室: '" + Me.C1Combo3.Text + "' 不存在！", MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
                    System.Windows.Forms.SendKeys.Send("^{A}")
                    Exit Sub
                End If
                C1Combo3.SelectedIndex = -1
                C1Combo3.SelectedIndex = 0
            Else
                Dim index As Integer
                index = C1Combo3.WillChangeToIndex
                C1Combo3.SelectedIndex = -1
                C1Combo3.SelectedIndex = index
            End If
            e.Handled = True
            System.Windows.Forms.SendKeys.Send("{Tab}")
        End If
    End Sub


    Private Sub C1Combo7_SelectedValueChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1Combo7.SelectedValueChanged
        If C1Combo7.Text = "全部患者" Then
            My_Dataset.Tables("病人字典").DefaultView.RowFilter = ""

        ElseIf C1Combo7.Text = "在院患者" Then
            My_Dataset.Tables("病人字典").DefaultView.RowFilter = "Ry_CyDate is null"
        ElseIf C1Combo7.Text = "出院患者" Then
            My_Dataset.Tables("病人字典").DefaultView.RowFilter = "Ry_CyDate is not null"
        End If
        C1Combo1.SelectedIndex = -1
    End Sub

    Private Sub C1Combo4_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Combo4.TextChanged
        If C1Combo4.Text = "热敏纸（80mm）" Then
            RadioButton1.Checked = True
            RadioButton2.Enabled = False
        Else
            RadioButton2.Enabled = True
        End If
    End Sub

End Class