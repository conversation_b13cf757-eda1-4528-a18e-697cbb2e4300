﻿
namespace ERX
{
    partial class Erx_Config
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.panel1 = new System.Windows.Forms.Panel();
            this.myButton1 = new CustomControl.MyButton();
            this.TxtAPIUrl = new CustomControl.MyTextBox();
            this.TxtappId = new CustomControl.MyTextBox();
            this.Txtappkey = new CustomControl.MyTextBox();
            this.TxtSecretKey = new CustomControl.MyTextBox();
            this.tableLayoutPanel1 = new System.Windows.Forms.TableLayoutPanel();
            this.panel1.SuspendLayout();
            this.tableLayoutPanel1.SuspendLayout();
            this.SuspendLayout();
            // 
            // panel1
            // 
            this.tableLayoutPanel1.SetColumnSpan(this.panel1, 4);
            this.panel1.Controls.Add(this.myButton1);
            this.panel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panel1.Location = new System.Drawing.Point(13, 119);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(544, 50);
            this.panel1.TabIndex = 5;
            // 
            // myButton1
            // 
            this.myButton1.ButtonImageSize = CustomControl.MyButton.imageSize.large;
            this.myButton1.DialogResult = System.Windows.Forms.DialogResult.None;
            this.myButton1.Font = new System.Drawing.Font("宋体", 10.5F);
            this.myButton1.Location = new System.Drawing.Point(232, 4);
            this.myButton1.Name = "myButton1";
            this.myButton1.Size = new System.Drawing.Size(80, 40);
            this.myButton1.TabIndex = 12;
            this.myButton1.Text = "保存";
            this.myButton1.Click += new System.EventHandler(this.myButton1_Click);
            // 
            // TxtAPIUrl
            // 
            this.TxtAPIUrl.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.TxtAPIUrl.Captain = "电子处方中台地址";
            this.TxtAPIUrl.CaptainBackColor = System.Drawing.Color.Transparent;
            this.TxtAPIUrl.CaptainFont = new System.Drawing.Font("宋体", 10.5F);
            this.TxtAPIUrl.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.TxtAPIUrl.CaptainWidth = 120F;
            this.tableLayoutPanel1.SetColumnSpan(this.TxtAPIUrl, 4);
            this.TxtAPIUrl.ContentForeColor = System.Drawing.Color.Black;
            this.TxtAPIUrl.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.TxtAPIUrl.EditMask = null;
            this.TxtAPIUrl.Location = new System.Drawing.Point(13, 3);
            this.TxtAPIUrl.Multiline = false;
            this.TxtAPIUrl.Name = "TxtAPIUrl";
            this.TxtAPIUrl.PasswordChar = '\0';
            this.TxtAPIUrl.ReadOnly = false;
            this.TxtAPIUrl.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.TxtAPIUrl.SelectionStart = 0;
            this.TxtAPIUrl.SelectStart = 0;
            this.TxtAPIUrl.Size = new System.Drawing.Size(544, 23);
            this.TxtAPIUrl.TabIndex = 1;
            this.TxtAPIUrl.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.TxtAPIUrl.TextFont = new System.Drawing.Font("宋体", 10.5F);
            this.TxtAPIUrl.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.TxtAPIUrl.Watermark = null;
            // 
            // TxtappId
            // 
            this.TxtappId.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.TxtappId.Captain = "电子处方密钥id";
            this.TxtappId.CaptainBackColor = System.Drawing.Color.Transparent;
            this.TxtappId.CaptainFont = new System.Drawing.Font("宋体", 10.5F);
            this.TxtappId.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.TxtappId.CaptainWidth = 120F;
            this.tableLayoutPanel1.SetColumnSpan(this.TxtappId, 4);
            this.TxtappId.ContentForeColor = System.Drawing.Color.Black;
            this.TxtappId.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.TxtappId.EditMask = null;
            this.TxtappId.Location = new System.Drawing.Point(13, 32);
            this.TxtappId.Multiline = false;
            this.TxtappId.Name = "TxtappId";
            this.TxtappId.PasswordChar = '\0';
            this.TxtappId.ReadOnly = false;
            this.TxtappId.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.TxtappId.SelectionStart = 0;
            this.TxtappId.SelectStart = 0;
            this.TxtappId.Size = new System.Drawing.Size(544, 23);
            this.TxtappId.TabIndex = 2;
            this.TxtappId.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.TxtappId.TextFont = new System.Drawing.Font("宋体", 10.5F);
            this.TxtappId.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.TxtappId.Watermark = null;
            // 
            // Txtappkey
            // 
            this.Txtappkey.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.Txtappkey.Captain = "电子处方密钥key";
            this.Txtappkey.CaptainBackColor = System.Drawing.Color.Transparent;
            this.Txtappkey.CaptainFont = new System.Drawing.Font("宋体", 10.5F);
            this.Txtappkey.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.Txtappkey.CaptainWidth = 120F;
            this.tableLayoutPanel1.SetColumnSpan(this.Txtappkey, 4);
            this.Txtappkey.ContentForeColor = System.Drawing.Color.Black;
            this.Txtappkey.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.Txtappkey.EditMask = null;
            this.Txtappkey.Location = new System.Drawing.Point(13, 61);
            this.Txtappkey.Multiline = false;
            this.Txtappkey.Name = "Txtappkey";
            this.Txtappkey.PasswordChar = '\0';
            this.Txtappkey.ReadOnly = false;
            this.Txtappkey.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.Txtappkey.SelectionStart = 0;
            this.Txtappkey.SelectStart = 0;
            this.Txtappkey.Size = new System.Drawing.Size(544, 23);
            this.Txtappkey.TabIndex = 3;
            this.Txtappkey.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.Txtappkey.TextFont = new System.Drawing.Font("宋体", 10.5F);
            this.Txtappkey.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.Txtappkey.Watermark = null;
            // 
            // TxtSecretKey
            // 
            this.TxtSecretKey.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.TxtSecretKey.Captain = "电子处方私钥";
            this.TxtSecretKey.CaptainBackColor = System.Drawing.Color.Transparent;
            this.TxtSecretKey.CaptainFont = new System.Drawing.Font("宋体", 10.5F);
            this.TxtSecretKey.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.TxtSecretKey.CaptainWidth = 120F;
            this.tableLayoutPanel1.SetColumnSpan(this.TxtSecretKey, 4);
            this.TxtSecretKey.ContentForeColor = System.Drawing.Color.Black;
            this.TxtSecretKey.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.TxtSecretKey.EditMask = null;
            this.TxtSecretKey.Location = new System.Drawing.Point(13, 90);
            this.TxtSecretKey.Multiline = false;
            this.TxtSecretKey.Name = "TxtSecretKey";
            this.TxtSecretKey.PasswordChar = '\0';
            this.TxtSecretKey.ReadOnly = false;
            this.TxtSecretKey.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.TxtSecretKey.SelectionStart = 0;
            this.TxtSecretKey.SelectStart = 0;
            this.TxtSecretKey.Size = new System.Drawing.Size(544, 23);
            this.TxtSecretKey.TabIndex = 3;
            this.TxtSecretKey.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.TxtSecretKey.TextFont = new System.Drawing.Font("宋体", 10.5F);
            this.TxtSecretKey.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.TxtSecretKey.Watermark = null;
            // 
            // tableLayoutPanel1
            // 
            this.tableLayoutPanel1.ColumnCount = 6;
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 150F));
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 150F));
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 160F));
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 90F));
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.tableLayoutPanel1.Controls.Add(this.panel1, 1, 4);
            this.tableLayoutPanel1.Controls.Add(this.TxtAPIUrl, 1, 0);
            this.tableLayoutPanel1.Controls.Add(this.TxtappId, 1, 1);
            this.tableLayoutPanel1.Controls.Add(this.Txtappkey, 1, 2);
            this.tableLayoutPanel1.Controls.Add(this.TxtSecretKey, 1, 3);
            this.tableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tableLayoutPanel1.Location = new System.Drawing.Point(0, 0);
            this.tableLayoutPanel1.Name = "tableLayoutPanel1";
            this.tableLayoutPanel1.RowCount = 5;
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 20F));
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 20F));
            this.tableLayoutPanel1.Size = new System.Drawing.Size(571, 172);
            this.tableLayoutPanel1.TabIndex = 1;
            // 
            // Erx_Config
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(571, 172);
            this.Controls.Add(this.tableLayoutPanel1);
            this.Name = "Erx_Config";
            this.Text = "电子处方参数设置";
            this.Load += new System.EventHandler(this.Erx_Config_Load);
            this.panel1.ResumeLayout(false);
            this.tableLayoutPanel1.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel1;
        private CustomControl.MyTextBox TxtAPIUrl;
        private CustomControl.MyTextBox TxtappId;
        private CustomControl.MyTextBox Txtappkey;
        private CustomControl.MyTextBox TxtSecretKey;
        private CustomControl.MyButton myButton1;
    }
}