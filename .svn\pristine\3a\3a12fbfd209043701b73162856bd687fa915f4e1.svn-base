﻿Imports System.Windows.Forms
Public Class MblbTree

#Region "变量初始化"

    Dim V_Finish As Boolean = False             '初始化完成
    Friend Shared My_Table As New DataTable            '药品字典
    Dim dtSearch As DataTable
    Dim m_Rc As New BaseClass.C_RowChange
    Dim V_Insert As Boolean                  '增加记录
    Dim completeFlag As Boolean
    Public Shared vSelectedNodeTag As String
    Dim vSelectedNodeText As String

    Dim BllEmr_Mblb As New BLLOld.B_Emr_Mblb
    Dim BllEmr_Mb As New BLLOld.B_Emr_Mb

    Dim intNum, intFrequency As Integer
    Dim rState As String
#End Region
    Public Sub New(ByVal rRc As BaseClass.C_RowChange, ByVal tState As String)

        ' 此调用是设计器所必需的。
        InitializeComponent()
        m_Rc = rRc
        rState = tState
        ' 在 InitializeComponent() 调用之后添加任何初始化。

    End Sub




    Private Sub MblbTree_Load(sender As Object, e As System.EventArgs) Handles Me.Load
        Call Form_Init()
        AddHandler m_Rc.DrDcEvent, AddressOf Tree_Edit
    End Sub

    Private Sub MblbTree_FormClosing(sender As Object, e As System.Windows.Forms.FormClosingEventArgs) Handles Me.FormClosing
        RemoveHandler m_Rc.DrDcEvent, AddressOf Tree_Edit
    End Sub



#Region "窗体初始化"
    Private Sub Form_Init()
        Me.StartPosition = FormStartPosition.WindowsDefaultBounds
        'Treeview初始化
        With Me.TreeView1
            .Nodes.Clear()
            .FullRowSelect = True
            .HideSelection = False
            .HotTracking = True
            .Scrollable = True
            .ShowRootLines = False
            .Sorted = False
        End With
        Init_Tree()
    End Sub
#End Region


#Region "Tree"
    Private Sub Init_Tree()
        V_Finish = False
        '根节点

        TreeView1.Nodes.Clear()
        Dim My_Node As New TreeNode
        With My_Node
            .Tag = "00000"
            .Text = "模板类别" & "(" & BllEmr_Mb.GetRecordCount("") & ")"
            .ImageIndex = 0
            .SelectedImageIndex = 0
        End With
        TreeView1.Nodes.Add(My_Node)
        My_Table = BllEmr_Mblb.GetAllList().Tables(0)
        dtSearch = My_Table.Clone
        Product_Node(My_Node)
        '一级数据
        V_Finish = True
        With Me.TreeView1
            .SelectedNode = TreeView1.TopNode

            .SelectedNode.Expand()
            .Select()
        End With
    End Sub



    'Mblb_Code, Mblb_Name, Father_Code
    'Mblb_Code, Mb_Code, Mb_Name

    Private Sub Product_Node(ByVal FatherNode As TreeNode)

        For Each row As DataRow In My_Table.Select("father_code='" & FatherNode.Tag & "'")
            Dim My_Node As New TreeNode
            With My_Node
                .Tag = row("Mblb_Code").ToString
                .Text = row("Mblb_Name").ToString & "(" & BllEmr_Mb.GetRecordCount("Mblb_Code='" & row("Mblb_Code").ToString.Trim & "'") & ")"
                .ImageIndex = 1
                .SelectedImageIndex = 2
            End With
            FatherNode.Nodes.Add(My_Node)
            Product_Node(My_Node)
        Next

    End Sub

    Private Sub Tree_Edit(ByVal V_Key As String, ByVal V_Text As String, Optional ByVal InsertFlag As String = "")
        If InsertFlag = "" Then
            Dim My_Node As New TreeNode
            If V_Insert = True Then
                My_Node.Tag = V_Key
                My_Node.Text = V_Text
                My_Node.ImageIndex = 1
                My_Node.SelectedImageIndex = 2
                TreeView1.SelectedNode.Nodes.Add(My_Node)
                If TreeView1.SelectedNode.IsExpanded = False Then
                    TreeView1.SelectedNode.Expand()
                End If
                P_Count()

            Else
                TreeView1.SelectedNode.Text = V_Text
            End If
        ElseIf InsertFlag = "dr" Then
            completeFlag = False
            Find_Node(V_Key, TreeView1.TopNode)
            P_Count()
        ElseIf InsertFlag = "update" Then
            completeFlag = False
            Find_Node(V_Key, TreeView1.TopNode)
            TreeView1.SelectedNode.Text = V_Text
            If My_Table.Rows.Count > 0 Then
                For Each row In My_Table.Rows
                    row("Mblb_name") = Mid(V_Text, 1, InStr(V_Text, "(") - 1)
                Next
            End If

        Else
            completeFlag = False
            Find_Node(InsertFlag, TreeView1.TopNode)
            Dim My_Node As New TreeNode
            My_Node.Tag = V_Key
            My_Node.Text = V_Text
            My_Node.ImageIndex = 1
            My_Node.SelectedImageIndex = 2
            TreeView1.SelectedNode.Nodes.Add(My_Node)
            TreeView1.SelectedNode = My_Node
            P_Count()
        End If

    End Sub

    Private Sub Find_Node(ByVal V_Key As String, ByVal node As TreeNode)

        If node.Tag = V_Key Then
            TreeView1.SelectedNode = node
            completeFlag = True
            Exit Sub
        End If

        If node.Nodes.Count > 0 Then
            For Each My_Node As TreeNode In node.Nodes
                If completeFlag = True Then
                    Exit Sub
                End If

                If My_Node.Tag = V_Key Then
                    If TreeView1.SelectedNode.Tag = My_Node.Tag Then
                        TreeView1_AfterSelect(Nothing, Nothing)
                    Else
                        TreeView1.SelectedNode = My_Node
                    End If
                    completeFlag = True
                    Exit Sub
                End If

                Find_Node(V_Key, My_Node)
            Next
        End If
    End Sub


    Private Sub CheckMbExist(ByVal node As TreeNode)
        If node.Nodes.Count = 0 Then
            If BllEmr_Mb.GetRecordCount("Mblb_Code='" & node.Tag & "'") > 0 Then
                completeFlag = True
            End If
        Else
            For Each childNode In node.Nodes
                Call CheckMbExist(childNode)
                If completeFlag = True Then Exit Sub
            Next
        End If
    End Sub


    Private Sub P_Delete_Node(ByVal node As TreeNode)
        If node.Tag = TreeView1.TopNode.Tag Then
            MsgBox("无法删除根节点！", MsgBoxStyle.Critical, "提示")
        Else

            Dim par_Node As TreeNode = node.Parent
            Delete_node(par_Node, node)
            TreeView1.SelectedNode = par_Node
            P_Count()
        End If

    End Sub

    Private Sub Delete_node(ByVal Parent_Node As TreeNode, ByVal Child_Node As TreeNode)
        If Child_Node.Nodes.Count = 0 Then
            Parent_Node.Nodes.Remove(Child_Node)
            BllEmr_Mblb.Delete(Child_Node.Tag)
        Else
            Delete_node(Child_Node, Child_Node.FirstNode)
            Delete_node(Parent_Node, Child_Node)
        End If

    End Sub
#End Region

#Region "控件动作"

    Private Sub NameTextBox1_TextChanged(sender As System.Object, e As System.EventArgs) Handles NameTextBox1.TextChanged
        intNum = 0
        intFrequency = 0
        dtSearch.Rows.Clear()
        For Each row In My_Table.Select("mblb_name like '*" & NameTextBox1.Text & "*'")
            Dim newRow As DataRow = dtSearch.NewRow
            dtSearch.Rows.Add(row.ItemArray)
            intNum += 1
        Next
    End Sub

    Private Sub SearchMyButton1_Click(sender As System.Object, e As System.EventArgs) Handles SearchMyButton1.Click
        If dtSearch.Rows.Count > 0 Then
            completeFlag = False
            Find_Node(dtSearch.Rows(intFrequency Mod intNum).Item("mblb_code"), TreeView1.TopNode)
            intFrequency += 1
        End If
    End Sub

    Private Sub TreeView1_AfterSelect(ByVal sender As System.Object, ByVal e As System.Windows.Forms.TreeViewEventArgs) Handles TreeView1.AfterSelect

        If V_Finish = False Then Exit Sub '     '初始化完成

        vSelectedNodeTag = TreeView1.SelectedNode.Tag
        vSelectedNodeText = TreeView1.SelectedNode.Text
    End Sub

    Private Sub EnsureButton1_Click(sender As System.Object, e As System.EventArgs) Handles EnsureButton1.Click
        If BllEmr_Mblb.GetRecordCount(" Father_Code='" & TreeView1.SelectedNode.Tag & "'") > 0 Then
            MsgBox("无法在该模板类别下增加模板！", MsgBoxStyle.Critical, "提示")
            Exit Sub
        End If
        If rState = "导入" Then
            MbExport.vMblbCode = vSelectedNodeTag
            Me.DialogResult = Windows.Forms.DialogResult.OK
        Else
            If MsgBox("您确定要把模板换到" & vSelectedNodeText & "吗？", MsgBoxStyle.OkCancel + MsgBoxStyle.DefaultButton1, "提示：") = MsgBoxResult.Ok Then
                EmrMb1.vSelectedNodeTag = vSelectedNodeTag
                Me.DialogResult = Windows.Forms.DialogResult.OK
            End If
        End If
    End Sub


    Private Sub ContextMenuStrip1_Opening(sender As Object, e As System.ComponentModel.CancelEventArgs) Handles ContextMenuStrip1.Opening
        If BllEmr_Mb.GetRecordCount(" Emr_Mb.Mblb_Code='" & TreeView1.SelectedNode.Tag & "'") > 0 Then
            AddNode.Enabled = False
        Else
            AddNode.Enabled = True
        End If


        If TreeView1.SelectedNode.Tag = "00000" Then
            UpdateNode.Enabled = False
        Else
            UpdateNode.Enabled = True
        End If
    End Sub

    Private Sub Node_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles AddNode.Click, UpdateNode.Click
        Select Case sender.text
            Case "增加"
                P_ShowMx("增加节点")
            Case "修改"
                If TreeView1.SelectedNode.Tag = "00000" Then
                    MsgBox("不能修改跟节点", MsgBoxStyle.Exclamation, "提示")
                    Exit Sub
                End If
                P_ShowMx("修改节点")
        End Select
    End Sub
#End Region

#Region "自定义函数"

    Private Sub P_ShowMx(ByVal V_Lb As String)
        ' showFlag = V_Lb
        If V_Lb = "增加节点" Then
            V_Insert = True
        Else
            V_Insert = False
        End If

        Dim vform As Form
        vSelectedNodeTag = TreeView1.SelectedNode.Tag
        vSelectedNodeText = Mid(TreeView1.SelectedNode.Text, 1, InStr(TreeView1.SelectedNode.Text, "(") - 1)
        If V_Lb = "增加节点" Then
            vform = New EmrMb2(m_Rc, True, "", "DataTable", vSelectedNodeTag, vSelectedNodeText)
        Else
            vform = New EmrMb2(m_Rc, False, vSelectedNodeTag, vSelectedNodeText,
                    TreeView1.SelectedNode.Parent.Tag,
                    Mid(TreeView1.SelectedNode.Parent.Text, 1, InStr(TreeView1.SelectedNode.Parent.Text, "(") - 1))
        End If
        vform.Show()

    End Sub

    Private Sub P_Count()
        TreeView1.TopNode.Text = "模板类别" & "(" & BllEmr_Mb.GetRecordCount("") & ")" '修改Treeview节点
        completeFlag = False
        Find_Node(vSelectedNodeTag, TreeView1.TopNode)
        If TreeView1.SelectedNode.Tag <> "00000" Then
            '  TreeView1.SelectedNode.Text = Mid(TreeView1.SelectedNode.Text, 1, InStr(TreeView1.SelectedNode.Text, "(") - 1) & "(" & MyGrid1.Splits(0).Rows.Count & ")"
            TreeView1.SelectedNode.Text = Mid(TreeView1.SelectedNode.Text, 1, InStr(TreeView1.SelectedNode.Text, "(") - 1) & "(" &
                BllEmr_Mb.GetRecordCount("mblb_code='" & vSelectedNodeTag & "'") & ")"
        End If
    End Sub
#End Region






End Class