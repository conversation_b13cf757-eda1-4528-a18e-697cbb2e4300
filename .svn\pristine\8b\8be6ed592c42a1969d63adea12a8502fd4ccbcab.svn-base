﻿Imports System.Data.SqlClient
Imports BaseClass

Public Class Zd_Qx2

    Dim My_Cc As New BaseClass.C_Cc()    '取最大编码及简称的类

#Region "传参"
    Dim <PERSON><PERSON>ert As Boolean
    Dim Rrow As DataRow
    Dim RZbtb As DataTable
    Dim Rtdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Dim Rlb As C1.Win.C1Input.C1Label
    Dim Rzbadt As SqlDataAdapter
    Dim Rrc As C_RowChange
    Dim Rtree As TreeView
#End Region

    Public Sub New(ByVal tinsert As Boolean, ByVal trow As DataRow, ByVal tZbtb As DataTable, ByVal ttdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid, ByVal tlb As C1.Win.C1Input.C1Label, ByVal tzbadt As SqlDataAdapter, ByRef trc As C_RowChange, ByVal tTree As TreeView)

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        Rinsert = tinsert
        'Rdate = tdate
        Rrow = trow
        RZbtb = tZbtb
        Rtdbgrid = ttdbgrid
        Rlb = tlb
        Rzbadt = tzbadt
        Rrc = trc
        Rtree = tTree
        ' 在 InitializeComponent() 调用之后添加任何初始化。
    End Sub


    Protected Overrides Sub OnKeyDown(ByVal e As System.Windows.Forms.KeyEventArgs)
        MyBase.OnKeyDown(e)
        If e.Control = True And e.KeyCode = Keys.S Then
            Call Comm_Click(Comm1, Nothing)
        End If
    End Sub

    Private Sub Zd_Qx2_FormClosed(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosedEventArgs) Handles Me.FormClosed
        RemoveHandler Rrc.RowChanged, New BaseClass.RowChangedHandler(AddressOf Data_Show)
        Me.Dispose()
    End Sub

    Private Sub Zd_Qx2_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        AddHandler Rrc.RowChanged, New BaseClass.RowChangedHandler(AddressOf Data_Show)
        Call Form_Init()

        If Rinsert = True Then
            Call Data_Clear()
        Else
            Call Data_Show(Rrow)
        End If

    End Sub

#Region "窗体__事件"

    Private Sub Form_Init()
        Panel1.Height = 26
        ToolBar1.Location = New Point(1, 4)
        '按扭初始化
        Call P_Comm(Me.Comm1)
        Call P_Comm(Me.Comm2)
    End Sub

#End Region

#Region "数据__编辑"

    Private Sub Data_Add()
        Dim My_NewRow As DataRow
        My_NewRow = RZbtb.NewRow

        My_Cc.Get_MaxCode("Zd_Qx1", "Glz_Code", 7, "Yy_Code", HisVar.HisVar.WsyCode) ' Now.Date.ToString("yyMMdd"))
        L_LpLb_Code.Text = My_Cc.编码                            '最大编码

        With My_NewRow
            .Item("Yy_Code") = HisVar.HisVar.WsyCode
            .Item("Glz_Code") = L_LpLb_Code.Text & ""
            .Item("Glz_Name") = Trim(C1TextBox2.Text & "")
            .Item("Glz_Memo") = Trim(C1TextBox3.Text & "")
            .Item("Glz_Jc") = L_LpLb_Jc.Text & ""
        End With

        '数据保存
        Try

            Rlb.Text = "∑=" + Rtdbgrid.Splits(0).Rows.Count.ToString

            Call Tree_Edit(L_LpLb_Code.Text, Me.C1TextBox2.Text)      '增加树节点
            RZbtb.Rows.Add(My_NewRow)
            Rtdbgrid.MoveLast()

            Call Data_Update("增加", My_NewRow)                     '保存记录
            Call Data_Clear()                                       '清空记录


        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
        End Try

    End Sub

    Private Sub Data_Edit()
        Try
            With Rrow
                .BeginEdit()
                .Item("Yy_Code") = HisVar.HisVar.WsyCode
                .Item("Glz_Code") = L_LpLb_Code.Text & ""
                .Item("Glz_Name") = Trim(C1TextBox2.Text & "")
                .Item("Glz_Jc") = Trim(L_LpLb_Jc.Text & "")
                .Item("Glz_Memo") = Trim(C1TextBox3.Text & "")

                .EndEdit()
            End With

            Call Tree_Edit(L_LpLb_Code.Text, Me.C1TextBox2.Text)    '编辑树节点
            Call Data_Update("保存", Nothing)

        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Critical, "错误提示:")
            Rrow.CancelEdit()
        Finally
            C1TextBox2.SelectAll()
            C1TextBox2.Focus()
        End Try


    End Sub

    Private Sub Data_Update(ByVal V_Lb As String, ByVal My_Row As DataRow)  '更新数据库
        If My_Cn.State = ConnectionState.Closed Then My_Cn.Open()

        If V_Lb = "增加" Then
            With Rzbadt.InsertCommand
                Try
                    .Parameters(0).Value = My_Row.Item("Yy_Code") & ""
                    .Parameters(1).Value = My_Row.Item("Glz_Code") & ""
                    .Parameters(2).Value = My_Row.Item("Glz_Name") & ""
                    .Parameters(3).Value = My_Row.Item("Glz_Jc") & ""
                    .Parameters(4).Value = My_Row.Item("Glz_Memo") & ""
                    .ExecuteNonQuery()
                    My_Row.AcceptChanges()
                Catch ex As Exception
                    Beep()
                    MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
                End Try
            End With
        Else
            With Rzbadt.UpdateCommand
                .Parameters(0).Value = Rrow.Item(0)
                .Parameters(1).Value = Rrow.Item(1)
                .Parameters(2).Value = Rrow.Item(2)
                .Parameters(3).Value = Rrow.Item(3)
                .Parameters(4).Value = Rrow.Item(0, DataRowVersion.Original)
                Try
                    .ExecuteNonQuery()
                    Rrow.AcceptChanges()
                Catch ex As Exception
                    Beep()
                    MsgBox(ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
                End Try
            End With
        End If


    End Sub

#End Region

#Region "数据__操作"

    Private Sub Data_Clear()
        Move5.Enabled = False                               '新增记录
        Rinsert = True
        My_Cc.Get_MaxCode("Zd_Qx1", "Glz_Code", 7, "Yy_Code", HisVar.HisVar.WsyCode) ' Now.Date.ToString("yyMMdd"))
        L_LpLb_Code.Text = My_Cc.编码                            '最大编码

        C1TextBox2.Value = ""
        C1TextBox3.Value = ""
        L_LpLb_Jc.Text = ""                                   '材料简称
        C1TextBox2.Focus()
        C1TextBox2.Select()
        T_Label2.Text = IIf(Rtdbgrid.Splits(0).Rows.Count() = 0, "0", CStr((Rtdbgrid.Row) + 1))
        T_Label3.Text = "∑=" + Rtdbgrid.Splits(0).Rows.Count.ToString
    End Sub

    Private Sub Data_Show(ByVal tmp_Row As DataRow)
        Move5.Enabled = True
        Rinsert = False
        Rrow = tmp_Row

        With Rrow
            L_LpLb_Code.Text = .Item("Glz_Code") & ""
            C1TextBox2.Value = .Item("Glz_Name") & ""
            L_LpLb_Jc.Text = .Item("Glz_Jc") & ""
            C1TextBox3.Value = .Item("Glz_Memo") & ""

        End With
        C1TextBox2.SelectAll()
        C1TextBox2.Focus()
        T_Label2.Text = CStr((Rtdbgrid.Row) + 1)
        T_Label3.Text = "∑=" + Rtdbgrid.Splits(0).Rows.Count.ToString


    End Sub

    Private Sub Data_Save()

        If Trim(C1TextBox2.Text) = "" Then
            MsgBox("请输入管理组名称！", MsgBoxStyle.Exclamation, "提示")
            C1TextBox2.Select()
            Exit Sub
        End If

            If Rinsert = True Then  '增加记录
            Call Data_Add()
        Else                            '编辑记录
            Call Data_Edit()
        End If

    End Sub

#End Region

#Region "控件__动作"

    Private Sub Comm_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.Click, Comm2.Click
        Select Case sender.tag
            Case "保存"
                Call Data_Save()

            Case "取消"
                Me.Close()
        End Select
    End Sub

    Private Sub Move_Click(ByVal sender As Object, ByVal e As C1.Win.C1Command.ClickEventArgs) Handles Move1.Click, Move2.Click, Move3.Click, Move4.Click, Move5.Click
        If sender.text = "新增" Then                            '增加状态
            Move5.Enabled = False
            Rinsert = True
            Call Data_Clear()
        Else
            Rinsert = False                         '编辑状态
            If Move5.Enabled = False Then Move5.Enabled = True
            Select Case sender.text
                Case "最前"
                    Rtdbgrid.MoveFirst()
                Case "上移"
                    Rtdbgrid.MovePrevious()
                Case "下移"
                    Rtdbgrid.MoveNext()
                Case "最后"
                    Rtdbgrid.MoveLast()
            End Select
        End If

    End Sub

    Private Sub TextBox_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles C1TextBox2.KeyPress, C1TextBox3.KeyPress
        If e.KeyChar <> Chr(Keys.Return) Then Exit Sub
        e.Handled = True
        System.Windows.Forms.SendKeys.Send("{Tab}")
    End Sub

    Private Sub C1TextBox2_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1TextBox2.Validated
        My_Cc.Get_Py(Me.C1TextBox2.Text & "")
        Me.L_LpLb_Jc.Text = My_Cc.简拚
    End Sub

#End Region

#Region "自定义函数"

    Private Sub Tree_Edit(ByVal V_Key As String, ByVal V_Text As String)
        Rtree.SelectedNode = Rtree.TopNode()

        Dim My_Node As New TreeNode
        If Rinsert = True Then
            My_Node.Tag = L_LpLb_Code.Text & ""
            My_Node.Text = C1TextBox2.Text & ""
            My_Node.ImageIndex = 1
            My_Node.SelectedImageIndex = 1
            Rtree.SelectedNode.Nodes.Add(My_Node)
        Else
            For Each My_Node In Rtree.SelectedNode.Nodes
                If My_Node.Tag = V_Key Then
                    My_Node.Text = V_Text
                    Exit For
                End If
            Next
        End If
    End Sub

#End Region

#Region "自定义按扭"

    Private Sub P_Comm(ByVal Bt As Button)
        With Bt
            Select Case .Tag
                Case "保存"
                    .Top = 3
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")
                    .Text = "            &B"

                Case "取消"
                    .Location = New Point(Comm1.Left + Comm1.Width + 4, Comm1.Top)
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
                    .Width = Me.Comm1.Width
                    .Text = "            &C"
            End Select
            .FlatStyle = FlatStyle.Flat
            .FlatAppearance.BorderSize = 0
            .BackgroundImageLayout = ImageLayout.Center
        End With

    End Sub

    Private Sub Comm_MouseEnter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.MouseEnter, Comm2.MouseEnter
        Select Case sender.tag
            Case "保存"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存2")
            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消2")
        End Select

    End Sub

    Private Sub Comm_MouseLeave(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.MouseLeave, Comm2.MouseLeave
        Select Case sender.tag
            Case "保存"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")
            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
        End Select
    End Sub

    Private Sub Comm_MouseDown(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm1.MouseDown, Comm2.MouseDown
        Select Case sender.tag
            Case "保存"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存3")

            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消3")
        End Select
    End Sub

    Private Sub Comm_MouseUp(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm1.MouseUp, Comm2.MouseUp
        Select Case sender.tag
            Case "保存"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")
            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
        End Select
    End Sub

#End Region


    '中文
    Private Sub C1TextBox1_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1TextBox2.GotFocus, C1TextBox3.GotFocus
        InputLanguage.CurrentInputLanguage = HisVar.HisVar.InputCode
    End Sub
End Class