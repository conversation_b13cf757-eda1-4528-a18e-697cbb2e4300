﻿/**  版本信息模板在安装目录下，可自行修改。
* D_Materials_Move1.cs
*
* 功 能： N/A
* 类 名： D_Materials_Move1
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016-12-05 13:54:39   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using System.Collections.Generic;
using ModelOld;

namespace DAL
{
    /// <summary>
    /// 数据访问类:D_Materials_Move1
    /// </summary>
    public partial class D_Materials_Move1
    {
        public D_Materials_Move1()
        { }
        #region  BasicMethod
        public string MaxCode(string date)
        {
            string max = date + (string)(HisVar.HisVar.Sqldal.F_MaxCode("select right(max(M_Move_Code),5) from Materials_Move1 where left(M_Move_Code,6)='" + date + "'", 5));
            return max;
        }
        public double GetSumMoney(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT isnull(SUM(TotalMoney),0) FROM dbo.Materials_Move1  where OrdersStatus='完成'");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" and " + strWhere);
            }
            try
            {
                double douSum = Double.Parse(HisVar.HisVar.Sqldal.GetSingle(strSql.ToString()).ToString());
                return douSum;
            }
            catch (Exception)
            {
                return 0;
            }

        }
        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(string M_Move_Code)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from Materials_Move1");
            strSql.Append(" where M_Move_Code=@M_Move_Code ");
            SqlParameter[] parameters = {
					new SqlParameter("@M_Move_Code", SqlDbType.Char,11)			};
            parameters[0].Value = M_Move_Code;

            return HisVar.HisVar.Sqldal.Exists(strSql.ToString(), parameters);
        }

        public DataSet GetHzList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(" SELECT *,a.MaterialsWh_Name as Old_MaterialsWh_Name,b.MaterialsWh_Name as New_MaterialsWh_Name,oldstock.MaterialsStore_Num AS Old_MaterialsStore_Num ,newstock.MaterialsStore_Num AS New_MaterialsStore_Num ");
            strSql.Append(" FROM dbo.Materials_Move1 LEFT JOIN dbo.Materials_Warehouse_Dict a ON Old_MaterialsWh_Code=a.MaterialsWh_Code LEFT JOIN dbo.Zd_YyJsr ON Zd_YyJsr.Jsr_Code = Materials_Move1.Jsr_Code LEFT JOIN Materials_Warehouse_Dict b ON New_MaterialsWh_Code=b.MaterialsWh_Code, ");
            strSql.Append(" dbo.Materials_Move2 LEFT JOIN dbo.Materials_Stock oldstock ON oldstock.MaterialsStock_Code = Materials_Move2.Old_MaterialsStock_Code ");
            strSql.Append(" LEFT JOIN dbo.Materials_Stock newstock ON newstock.MaterialsStock_Code = Materials_Move2.New_MaterialsStock_Code LEFT JOIN dbo.Materials_Dict ON Materials_Dict.Materials_Code = Materials_Move2.Materials_Code");
            strSql.Append(" WHERE Materials_Move2.M_Move_Code=Materials_Move1.M_Move_Code ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(strWhere);
            }
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ModelOld.M_Materials_Move1 model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into Materials_Move1(");
            strSql.Append("M_Move_Code,Old_MaterialsWh_Code,New_MaterialsWh_Code,Move_Date,Input_Date,Finish_Date,Jsr_Code,M_Move_Memo,OrdersStatus,TotalMoney)");
            strSql.Append(" values (");
            strSql.Append("@M_Move_Code,@Old_MaterialsWh_Code,@New_MaterialsWh_Code,@Move_Date,@Input_Date,@Finish_Date,@Jsr_Code,@M_Move_Memo,@OrdersStatus,@TotalMoney)");
            SqlParameter[] parameters = {
					new SqlParameter("@M_Move_Code", SqlDbType.Char,11),
					new SqlParameter("@Old_MaterialsWh_Code", SqlDbType.Char,2),
					new SqlParameter("@New_MaterialsWh_Code", SqlDbType.Char,2),
					new SqlParameter("@Move_Date", SqlDbType.SmallDateTime),
					new SqlParameter("@Input_Date", SqlDbType.SmallDateTime),
					new SqlParameter("@Finish_Date", SqlDbType.SmallDateTime),
					new SqlParameter("@Jsr_Code", SqlDbType.Char,7),
					new SqlParameter("@M_Move_Memo", SqlDbType.VarChar,50),
					new SqlParameter("@OrdersStatus", SqlDbType.VarChar,10),
					new SqlParameter("@TotalMoney", SqlDbType.Decimal,5)};
            parameters[0].Value = model.M_Move_Code;
            parameters[1].Value = model.Old_MaterialsWh_Code;
            parameters[2].Value = Common.Tools.IsValueNull(model.New_MaterialsWh_Code);
            parameters[3].Value = model.Move_Date;
            parameters[4].Value = model.Input_Date;
            parameters[5].Value = Common.Tools.IsValueNull(model.Finish_Date);
            parameters[6].Value = model.Jsr_Code;
            parameters[7].Value = model.M_Move_Memo;
            parameters[8].Value = model.OrdersStatus;
            parameters[9].Value = Common.Tools.IsValueNull(model.TotalMoney);

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ModelOld.M_Materials_Move1 model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update Materials_Move1 set ");
            strSql.Append("Old_MaterialsWh_Code=@Old_MaterialsWh_Code,");
            strSql.Append("New_MaterialsWh_Code=@New_MaterialsWh_Code,");
            strSql.Append("Move_Date=@Move_Date,");
            strSql.Append("Input_Date=@Input_Date,");
            strSql.Append("Finish_Date=@Finish_Date,");
            strSql.Append("Jsr_Code=@Jsr_Code,");
            strSql.Append("M_Move_Memo=@M_Move_Memo,");
            strSql.Append("OrdersStatus=@OrdersStatus,");
            strSql.Append("TotalMoney=@TotalMoney");
            strSql.Append(" where M_Move_Code=@M_Move_Code ");
            SqlParameter[] parameters = {
					new SqlParameter("@Old_MaterialsWh_Code", SqlDbType.Char,2),
					new SqlParameter("@New_MaterialsWh_Code", SqlDbType.Char,2),
					new SqlParameter("@Move_Date", SqlDbType.SmallDateTime),
					new SqlParameter("@Input_Date", SqlDbType.SmallDateTime),
					new SqlParameter("@Finish_Date", SqlDbType.SmallDateTime),
					new SqlParameter("@Jsr_Code", SqlDbType.Char,7),
					new SqlParameter("@M_Move_Memo", SqlDbType.VarChar,50),
					new SqlParameter("@OrdersStatus", SqlDbType.VarChar,10),
					new SqlParameter("@TotalMoney", SqlDbType.Decimal,5),
					new SqlParameter("@M_Move_Code", SqlDbType.Char,11)};
            parameters[0].Value = model.Old_MaterialsWh_Code;
            parameters[1].Value = Common.Tools.IsValueNull(model.New_MaterialsWh_Code);
            parameters[2].Value = model.Move_Date;
            parameters[3].Value = model.Input_Date;
            parameters[4].Value = Common.Tools.IsValueNull(model.Finish_Date);
            parameters[5].Value = model.Jsr_Code;
            parameters[6].Value = model.M_Move_Memo;
            parameters[7].Value = model.OrdersStatus;
            parameters[8].Value = Common.Tools.IsValueNull(model.TotalMoney);
            parameters[9].Value = model.M_Move_Code;

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 单据完成,库存表插入新的记录
        /// </summary>
        public bool Complete(ModelOld.M_Materials_Move1 model)
        {
            List<string> sqlList = new List<string>();
            List<SqlParameter[]> parametersList = new List<SqlParameter[]>();

            //单据状态改成完成,填写完成时间
            StringBuilder strSql1 = new StringBuilder();
            strSql1.Append("update Materials_Move1 set ");
            strSql1.Append("OrdersStatus=@OrdersStatus,");
            strSql1.Append("New_MaterialsWh_Code=@New_MaterialsWh_Code,");
            strSql1.Append("Finish_Date=@Finish_Date");
            strSql1.Append(" where M_Move_Code=@M_Move_Code ");
            SqlParameter[] parameters1 = {
					new SqlParameter("@OrdersStatus", SqlDbType.VarChar,10),
                    new SqlParameter("@New_MaterialsWh_Code", SqlDbType.Char,2),
                    new SqlParameter("@Finish_Date", SqlDbType.SmallDateTime),
					new SqlParameter("@M_Move_Code", SqlDbType.Char,11)};
            parameters1[0].Value = model.OrdersStatus;
            parameters1[1].Value = model.New_MaterialsWh_Code;
            parameters1[2].Value = model.Finish_Date;
            parameters1[3].Value = model.M_Move_Code;

            sqlList.Add(strSql1.ToString());
            parametersList.Add(parameters1);

            //库存减掉移库数量
            StringBuilder strSql2 = new StringBuilder();
            strSql2.Append("UPDATE Materials_Stock SET MaterialsStore_Num=MaterialsStore_Num-M_Move_Num   ");
            strSql2.Append("FROM Materials_Move2 ");
            strSql2.Append("WHERE Materials_Stock.MaterialsStock_Code=Materials_Move2.Old_MaterialsStock_Code ");
            strSql2.Append("AND M_Move_Code=@M_Move_Code ");
            SqlParameter[] parameter2 = {
					new SqlParameter("@M_Move_Code", SqlDbType.Char,11)};
            parameter2[0].Value = model.M_Move_Code;
            sqlList.Add(strSql2.ToString());
            parametersList.Add(parameter2);

            D_Materials_Stock _Materials_Stock = new D_Materials_Stock();
            D_Materials_Move2 _Materials_Move2 = new D_Materials_Move2();
            //插入新的库存
            List<ModelOld.M_Materials_Move2> move2List = new List<M_Materials_Move2>();
            move2List = _Materials_Move2.GetModelList(model.M_Move_Code);
            foreach (ModelOld.M_Materials_Move2 move2 in move2List)
            {

                if (_Materials_Stock.Exists(move2.Materials_Code, model.New_MaterialsWh_Code, move2.MaterialsLot, move2.MaterialsExpiryDate.Value, move2.M_Move_Price.Value))
                {
                    //新库存存在直接增加库存
                    move2.New_MaterialsStock_Code = _Materials_Stock.MaterialsStock_Code(move2.Materials_Code,
                        model.New_MaterialsWh_Code, move2.MaterialsLot, move2.MaterialsExpiryDate.Value,
                        move2.M_Move_Price.Value);
                    StringBuilder strSql3 = new StringBuilder();
                    strSql3.Append("UPDATE Materials_Stock SET MaterialsStore_Num=MaterialsStore_Num+@M_Move_Num   ");
                    strSql3.Append("WHERE MaterialsStock_Code=@MaterialsStock_Code");
                    SqlParameter[] parameter3 =
                    {
                        new SqlParameter("@M_Move_Num", SqlDbType.Decimal,5),
                        new SqlParameter("@MaterialsStock_Code", SqlDbType.Char, 16)
                    };
                    parameter3[0].Value = move2.M_Move_Num;
                    parameter3[1].Value = move2.New_MaterialsStock_Code;
                    sqlList.Add(strSql3.ToString());
                    parametersList.Add(parameter3);
                }
                else
                {
                    StringBuilder strSql3 = new StringBuilder();
                    strSql3.Append("insert into Materials_Stock(");
                    strSql3.Append("Materials_Code,MaterialsWh_Code,MaterialsStock_Code,MaterialsLot,MaterialsExpiryDate,MaterialsStore_Num,MaterialsStore_Price,MaterialsStore_Money)");
                    strSql3.Append(" values (");
                    strSql3.Append("@Materials_Code,@MaterialsWh_Code,@MaterialsStock_Code,@MaterialsLot,@MaterialsExpiryDate,@MaterialsStore_Num,@MaterialsStore_Price,@MaterialsStore_Money)");
                    SqlParameter[] parameter3 = {
					new SqlParameter("@Materials_Code", SqlDbType.Char,10),
					new SqlParameter("@MaterialsWh_Code", SqlDbType.Char,2),
					new SqlParameter("@MaterialsStock_Code", SqlDbType.Char,16),
					new SqlParameter("@MaterialsLot", SqlDbType.VarChar,50),
					new SqlParameter("@MaterialsExpiryDate", SqlDbType.SmallDateTime),
					new SqlParameter("@MaterialsStore_Num", SqlDbType.Decimal,5),
					new SqlParameter("@MaterialsStore_Price", SqlDbType.Decimal,5),
					new SqlParameter("@MaterialsStore_Money", SqlDbType.Decimal,5)};
                    parameter3[0].Value = move2.Materials_Code;
                    parameter3[1].Value = model.New_MaterialsWh_Code;
                    move2.New_MaterialsStock_Code = _Materials_Stock.MaxCode(move2.Materials_Code);
                    parameter3[2].Value = move2.New_MaterialsStock_Code;
                    parameter3[3].Value = move2.MaterialsLot;
                    parameter3[4].Value = move2.MaterialsExpiryDate;
                    parameter3[5].Value = move2.M_Move_Num;
                    parameter3[6].Value = move2.M_Move_Price;
                    parameter3[7].Value = move2.M_Move_Money;
                    sqlList.Add(strSql3.ToString());
                    parametersList.Add(parameter3);
                }

                StringBuilder strSql4 = new StringBuilder();
                strSql4.Append("update Materials_Move2 Set New_MaterialsStock_Code=@New_MaterialsStock_Code ");
                strSql4.Append("WHERE M_Move_Detail_Code=@M_Move_Detail_Code ");
                SqlParameter[] parameter4 ={
                    new SqlParameter("@New_MaterialsStock_Code", SqlDbType.Char,16),
					new SqlParameter("@M_Move_Detail_Code", SqlDbType.Char,15)};

                parameter4[0].Value = move2.New_MaterialsStock_Code;
                parameter4[1].Value = move2.M_Move_Detail_Code;
                sqlList.Add(strSql4.ToString());
                parametersList.Add(parameter4);
            }



            int rows = HisVar.HisVar.Sqldal.ExecuteSql(sqlList, parametersList);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(string M_Move_Code)
        {
            List<string> sqlList = new List<string>();
            List<SqlParameter[]> parametersList = new List<SqlParameter[]>();


            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from Materials_Move2 ");
            strSql.Append(" where M_Move_Code=@M_Move_Code ");
            SqlParameter[] parameters = {
					new SqlParameter("@M_Move_Code", SqlDbType.Char,11)			};
            parameters[0].Value = M_Move_Code;
            sqlList.Add(strSql.ToString());
            parametersList.Add(parameters);


            strSql = new StringBuilder();
            strSql.Append("delete from Materials_Move1 ");
            strSql.Append(" where M_Move_Code=@M_Move_Code ");
            parameters = new SqlParameter[] {
					new SqlParameter("@M_Move_Code", SqlDbType.Char,11)			};
            parameters[0].Value = M_Move_Code;


            sqlList.Add(strSql.ToString());
            parametersList.Add(parameters);

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(sqlList, parametersList);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }



        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string M_Move_Codelist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from Materials_Move1 ");
            strSql.Append(" where M_Move_Code in (" + M_Move_Codelist + ")  ");
            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ModelOld.M_Materials_Move1 GetModel(string M_Move_Code)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 M_Move_Code,Old_MaterialsWh_Code,New_MaterialsWh_Code,Move_Date,Input_Date,Finish_Date,Materials_Move1.Jsr_Code,Jsr_Name,M_Move_Memo,OrdersStatus,TotalMoney from Materials_Move1 JOIN dbo.Zd_YyJsr ON dbo.Materials_Move1.Jsr_Code = dbo.Zd_YyJsr.Jsr_Code");
            strSql.Append(" where M_Move_Code=@M_Move_Code ");
            SqlParameter[] parameters = {
					new SqlParameter("@M_Move_Code", SqlDbType.Char,11)			};
            parameters[0].Value = M_Move_Code;

            ModelOld.M_Materials_Move1 model = new ModelOld.M_Materials_Move1();
            DataSet ds = HisVar.HisVar.Sqldal.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ModelOld.M_Materials_Move1 DataRowToModel(DataRow row)
        {
            ModelOld.M_Materials_Move1 model = new ModelOld.M_Materials_Move1();
            if (row != null)
            {
                if (row["M_Move_Code"] != null)
                {
                    model.M_Move_Code = row["M_Move_Code"].ToString();
                }
                if (row["Old_MaterialsWh_Code"] != null)
                {
                    model.Old_MaterialsWh_Code = row["Old_MaterialsWh_Code"].ToString();
                }
                if (row["New_MaterialsWh_Code"] != null)
                {
                    model.New_MaterialsWh_Code = row["New_MaterialsWh_Code"].ToString();
                }
                if (row["Move_Date"] != null && row["Move_Date"].ToString() != "")
                {
                    model.Move_Date = DateTime.Parse(row["Move_Date"].ToString());
                }
                if (row["Input_Date"] != null && row["Input_Date"].ToString() != "")
                {
                    model.Input_Date = DateTime.Parse(row["Input_Date"].ToString());
                }
                if (row["Finish_Date"] != null && row["Finish_Date"].ToString() != "")
                {
                    model.Finish_Date = DateTime.Parse(row["Finish_Date"].ToString());
                }
                if (row["Jsr_Code"] != null)
                {
                    model.Jsr_Code = row["Jsr_Code"].ToString();
                }
                if (row["Jsr_Name"] != null)
                {
                    model.Jsr_Name = row["Jsr_Name"].ToString();
                }
                if (row["M_Move_Memo"] != null)
                {
                    model.M_Move_Memo = row["M_Move_Memo"].ToString();
                }
                if (row["OrdersStatus"] != null)
                {
                    model.OrdersStatus = row["OrdersStatus"].ToString();
                }
                if (row["TotalMoney"] != null && row["TotalMoney"].ToString() != "")
                {
                    model.TotalMoney = decimal.Parse(row["TotalMoney"].ToString());
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT M_Move_Code, Old_MaterialsWh_Code,a.MaterialsWh_Name OldMaterialsWh_Name, New_MaterialsWh_Code,b.MaterialsWh_Name NewMaterialsWh_Name, Move_Date, Input_Date, Finish_Date,dbo.Materials_Move1. Jsr_Code,Jsr_Name, M_Move_Memo, OrdersStatus, TotalMoney ");
            strSql.Append(" FROM dbo.Materials_Move1 JOIN dbo.Zd_YyJsr ON dbo.Materials_Move1.Jsr_Code = dbo.Zd_YyJsr.Jsr_Code  JOIN dbo.Materials_Warehouse_Dict a ON Old_MaterialsWh_Code=a.MaterialsWh_Code JOIN dbo.Materials_Warehouse_Dict b ON New_MaterialsWh_Code=b.MaterialsWh_Code ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" M_Move_Code,Old_MaterialsWh_Code,New_MaterialsWh_Code,Move_Date,Input_Date,Finish_Date,Jsr_Code,M_Move_Memo,OrdersStatus,TotalMoney ");
            strSql.Append(" FROM Materials_Move1 ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM Materials_Move1 ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.M_Move_Code desc");
            }
            strSql.Append(")AS Row, T.*  from Materials_Move1 T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

        /*
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetList(int PageSize,int PageIndex,string strWhere)
        {
            SqlParameter[] parameters = {
                    new SqlParameter("@tblName", SqlDbType.VarChar, 255),
                    new SqlParameter("@fldName", SqlDbType.VarChar, 255),
                    new SqlParameter("@PageSize", SqlDbType.Int),
                    new SqlParameter("@PageIndex", SqlDbType.Int),
                    new SqlParameter("@IsReCount", SqlDbType.Bit),
                    new SqlParameter("@OrderType", SqlDbType.Bit),
                    new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
                    };
            parameters[0].Value = "Materials_Move1";
            parameters[1].Value = "M_Move_Code";
            parameters[2].Value = PageSize;
            parameters[3].Value = PageIndex;
            parameters[4].Value = 0;
            parameters[5].Value = 0;
            parameters[6].Value = strWhere;	
            return HisVar.HisVar.Sqldal.RunProcedure("UP_GetRecordByPage",parameters,"ds");
        }*/

        #endregion  BasicMethod
        #region  ExtensionMethod

        #endregion  ExtensionMethod
    }
}

