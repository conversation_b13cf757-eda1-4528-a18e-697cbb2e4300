﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class YkYf_Bsby
    Inherits HisControl.BaseForm

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(YkYf_Bsby))
        Me.C1Holder1 = New C1.Win.C1Command.C1CommandHolder()
        Me.Move1 = New C1.Win.C1Command.C1Command()
        Me.Move2 = New C1.Win.C1Command.C1Command()
        Me.Move3 = New C1.Win.C1Command.C1Command()
        Me.Move4 = New C1.Win.C1Command.C1Command()
        Me.Control1 = New C1.Win.C1Command.C1CommandControl()
        Me.Control2 = New C1.Win.C1Command.C1CommandControl()
        Me.Control3 = New C1.Win.C1Command.C1CommandControl()
        Me.C1Command1 = New C1.Win.C1Command.C1Command()
        Me.C1Command2 = New C1.Win.C1Command.C1Command()
        Me.Label19 = New System.Windows.Forms.Label()
        Me.C1TextBox12 = New C1.Win.C1Input.C1TextBox()
        Me.C1TextBox10 = New C1.Win.C1Input.C1TextBox()
        Me.C1TextBox9 = New C1.Win.C1Input.C1TextBox()
        Me.C1TextBox8 = New C1.Win.C1Input.C1TextBox()
        Me.C1TextBox7 = New C1.Win.C1Input.C1TextBox()
        Me.C1TextBox11 = New C1.Win.C1Input.C1TextBox()
        Me.C1TextBox13 = New C1.Win.C1Input.C1TextBox()
        Me.C1TextBox14 = New C1.Win.C1Input.C1TextBox()
        Me.C1TextBox15 = New C1.Win.C1Input.C1TextBox()
        Me.C1TextBox16 = New C1.Win.C1Input.C1TextBox()
        Me.T_Textbox = New C1.Win.C1Input.C1TextBox()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.Label5 = New System.Windows.Forms.Label()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.Label6 = New System.Windows.Forms.Label()
        Me.C1Combo2 = New C1.Win.C1List.C1Combo()
        Me.Button2 = New System.Windows.Forms.Button()
        Me.CheckBox1 = New System.Windows.Forms.CheckBox()
        Me.C1NumericEdit1 = New C1.Win.C1Input.C1NumericEdit()
        Me.Button1 = New System.Windows.Forms.Button()
        Me.Comm1 = New System.Windows.Forms.Button()
        Me.DateTimePicker2 = New System.Windows.Forms.DateTimePicker()
        Me.DateTimePicker1 = New System.Windows.Forms.DateTimePicker()
        Me.Label12 = New System.Windows.Forms.Label()
        Me.C1TextBox23 = New C1.Win.C1Input.C1TextBox()
        Me.C1TextBox22 = New C1.Win.C1Input.C1TextBox()
        Me.C1TextBox21 = New C1.Win.C1Input.C1TextBox()
        Me.C1TextBox20 = New C1.Win.C1Input.C1TextBox()
        Me.C1TextBox18 = New C1.Win.C1Input.C1TextBox()
        Me.Label11 = New System.Windows.Forms.Label()
        Me.Label10 = New System.Windows.Forms.Label()
        Me.Label9 = New System.Windows.Forms.Label()
        Me.Label8 = New System.Windows.Forms.Label()
        Me.C1Combo1 = New C1.Win.C1List.C1Combo()
        Me.C1TextBox1 = New C1.Win.C1Input.C1TextBox()
        Me.C1TextBox2 = New C1.Win.C1Input.C1TextBox()
        Me.C1TextBox3 = New C1.Win.C1Input.C1TextBox()
        Me.C1TextBox4 = New C1.Win.C1Input.C1TextBox()
        Me.C1TextBox5 = New C1.Win.C1Input.C1TextBox()
        Me.C1TextBox6 = New C1.Win.C1Input.C1TextBox()
        Me.C1TrueDBGrid1 = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        CType(Me.C1Holder1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1TextBox12, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1TextBox10, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1TextBox9, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1TextBox8, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1TextBox7, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1TextBox11, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1TextBox13, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1TextBox14, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1TextBox15, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1TextBox16, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T_Textbox, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Panel1.SuspendLayout()
        CType(Me.C1Combo2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1NumericEdit1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1TextBox23, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1TextBox22, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1TextBox21, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1TextBox20, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1TextBox18, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1Combo1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1TextBox1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1TextBox2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1TextBox3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1TextBox4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1TextBox5, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1TextBox6, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1TrueDBGrid1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'C1Holder1
        '
        Me.C1Holder1.Commands.Add(Me.Move1)
        Me.C1Holder1.Commands.Add(Me.Move2)
        Me.C1Holder1.Commands.Add(Me.Move3)
        Me.C1Holder1.Commands.Add(Me.Move4)
        Me.C1Holder1.Commands.Add(Me.Control1)
        Me.C1Holder1.Commands.Add(Me.Control2)
        Me.C1Holder1.Commands.Add(Me.Control3)
        Me.C1Holder1.Commands.Add(Me.C1Command1)
        Me.C1Holder1.Commands.Add(Me.C1Command2)
        Me.C1Holder1.Owner = Me
        '
        'Move1
        '
        Me.Move1.Image = CType(resources.GetObject("Move1.Image"), System.Drawing.Image)
        Me.Move1.Name = "Move1"
        Me.Move1.Shortcut = System.Windows.Forms.Shortcut.F2
        Me.Move1.ShortcutText = ""
        Me.Move1.Text = "最前"
        Me.Move1.ToolTipText = "移到最前记录"
        '
        'Move2
        '
        Me.Move2.Image = CType(resources.GetObject("Move2.Image"), System.Drawing.Image)
        Me.Move2.Name = "Move2"
        Me.Move2.Shortcut = System.Windows.Forms.Shortcut.F3
        Me.Move2.ShortcutText = ""
        Me.Move2.ShowShortcut = False
        Me.Move2.Text = "上移"
        Me.Move2.ToolTipText = "上移记录"
        '
        'Move3
        '
        Me.Move3.Image = CType(resources.GetObject("Move3.Image"), System.Drawing.Image)
        Me.Move3.Name = "Move3"
        Me.Move3.Shortcut = System.Windows.Forms.Shortcut.F4
        Me.Move3.ShortcutText = ""
        Me.Move3.Text = "下移"
        Me.Move3.ToolTipText = "下移记录"
        '
        'Move4
        '
        Me.Move4.Image = CType(resources.GetObject("Move4.Image"), System.Drawing.Image)
        Me.Move4.Name = "Move4"
        Me.Move4.Shortcut = System.Windows.Forms.Shortcut.F5
        Me.Move4.ShortcutText = ""
        Me.Move4.Text = "最后"
        Me.Move4.ToolTipText = "移至最后记录"
        '
        'Control1
        '
        Me.Control1.HotFrame = C1.Win.C1Command.HotFrameEnum.None
        Me.Control1.Name = "Control1"
        Me.Control1.ShortcutText = ""
        Me.Control1.ShowShortcut = False
        Me.Control1.ShowTextAsToolTip = False
        '
        'Control2
        '
        Me.Control2.HotFrame = C1.Win.C1Command.HotFrameEnum.None
        Me.Control2.Name = "Control2"
        Me.Control2.ShortcutText = ""
        Me.Control2.ShowShortcut = False
        Me.Control2.ShowTextAsToolTip = False
        '
        'Control3
        '
        Me.Control3.HotFrame = C1.Win.C1Command.HotFrameEnum.None
        Me.Control3.Name = "Control3"
        Me.Control3.ShortcutText = ""
        Me.Control3.ShowShortcut = False
        Me.Control3.ShowTextAsToolTip = False
        '
        'C1Command1
        '
        Me.C1Command1.Name = "C1Command1"
        Me.C1Command1.ShortcutText = ""
        Me.C1Command1.Text = "乘"
        '
        'C1Command2
        '
        Me.C1Command2.Name = "C1Command2"
        Me.C1Command2.ShortcutText = ""
        Me.C1Command2.Text = "除"
        '
        'Label19
        '
        Me.Label19.AutoSize = True
        Me.Label19.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label19.Location = New System.Drawing.Point(5, 85)
        Me.Label19.Name = "Label19"
        Me.Label19.Size = New System.Drawing.Size(59, 12)
        Me.Label19.TabIndex = 77
        Me.Label19.Text = "备    注:"
        '
        'C1TextBox12
        '
        Me.C1TextBox12.Location = New System.Drawing.Point(68, 81)
        Me.C1TextBox12.Multiline = True
        Me.C1TextBox12.Name = "C1TextBox12"
        Me.C1TextBox12.Size = New System.Drawing.Size(402, 21)
        Me.C1TextBox12.TabIndex = 3
        Me.C1TextBox12.Tag = Nothing
        Me.C1TextBox12.TextDetached = True
        '
        'C1TextBox10
        '
        Me.C1TextBox10.BackColor = System.Drawing.SystemColors.Info
        Me.C1TextBox10.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1TextBox10.Location = New System.Drawing.Point(564, 10)
        Me.C1TextBox10.Name = "C1TextBox10"
        Me.C1TextBox10.ReadOnly = True
        Me.C1TextBox10.Size = New System.Drawing.Size(208, 14)
        Me.C1TextBox10.TabIndex = 69
        Me.C1TextBox10.Tag = Nothing
        Me.C1TextBox10.TextDetached = True
        Me.C1TextBox10.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.C1TextBox10.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'C1TextBox9
        '
        Me.C1TextBox9.BackColor = System.Drawing.SystemColors.Info
        Me.C1TextBox9.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1TextBox9.Location = New System.Drawing.Point(69, 34)
        Me.C1TextBox9.Name = "C1TextBox9"
        Me.C1TextBox9.ReadOnly = True
        Me.C1TextBox9.Size = New System.Drawing.Size(123, 14)
        Me.C1TextBox9.TabIndex = 71
        Me.C1TextBox9.Tag = Nothing
        Me.C1TextBox9.TextDetached = True
        Me.C1TextBox9.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.C1TextBox9.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'C1TextBox8
        '
        Me.C1TextBox8.BackColor = System.Drawing.SystemColors.Info
        Me.C1TextBox8.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1TextBox8.Location = New System.Drawing.Point(276, 34)
        Me.C1TextBox8.Name = "C1TextBox8"
        Me.C1TextBox8.ReadOnly = True
        Me.C1TextBox8.Size = New System.Drawing.Size(208, 14)
        Me.C1TextBox8.TabIndex = 73
        Me.C1TextBox8.Tag = Nothing
        Me.C1TextBox8.TextDetached = True
        Me.C1TextBox8.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.C1TextBox8.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'C1TextBox7
        '
        Me.C1TextBox7.BackColor = System.Drawing.SystemColors.Info
        Me.C1TextBox7.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1TextBox7.Location = New System.Drawing.Point(564, 35)
        Me.C1TextBox7.Name = "C1TextBox7"
        Me.C1TextBox7.ReadOnly = True
        Me.C1TextBox7.Size = New System.Drawing.Size(208, 14)
        Me.C1TextBox7.TabIndex = 75
        Me.C1TextBox7.Tag = Nothing
        Me.C1TextBox7.TextDetached = True
        Me.C1TextBox7.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.C1TextBox7.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'C1TextBox11
        '
        Me.C1TextBox11.BackColor = System.Drawing.SystemColors.Info
        Me.C1TextBox11.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1TextBox11.Location = New System.Drawing.Point(276, 9)
        Me.C1TextBox11.Name = "C1TextBox11"
        Me.C1TextBox11.ReadOnly = True
        Me.C1TextBox11.Size = New System.Drawing.Size(208, 14)
        Me.C1TextBox11.TabIndex = 81
        Me.C1TextBox11.Tag = Nothing
        Me.C1TextBox11.TextDetached = True
        Me.C1TextBox11.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.C1TextBox11.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'C1TextBox13
        '
        Me.C1TextBox13.BackColor = System.Drawing.SystemColors.Info
        Me.C1TextBox13.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1TextBox13.Location = New System.Drawing.Point(69, 59)
        Me.C1TextBox13.Name = "C1TextBox13"
        Me.C1TextBox13.ReadOnly = True
        Me.C1TextBox13.Size = New System.Drawing.Size(123, 14)
        Me.C1TextBox13.TabIndex = 82
        Me.C1TextBox13.Tag = Nothing
        Me.C1TextBox13.TextDetached = True
        Me.C1TextBox13.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.C1TextBox13.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'C1TextBox14
        '
        Me.C1TextBox14.BackColor = System.Drawing.SystemColors.Info
        Me.C1TextBox14.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1TextBox14.Location = New System.Drawing.Point(276, 59)
        Me.C1TextBox14.Name = "C1TextBox14"
        Me.C1TextBox14.ReadOnly = True
        Me.C1TextBox14.Size = New System.Drawing.Size(80, 14)
        Me.C1TextBox14.TabIndex = 83
        Me.C1TextBox14.Tag = Nothing
        Me.C1TextBox14.TextDetached = True
        Me.C1TextBox14.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.C1TextBox14.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'C1TextBox15
        '
        Me.C1TextBox15.BackColor = System.Drawing.SystemColors.Info
        Me.C1TextBox15.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1TextBox15.Location = New System.Drawing.Point(419, 59)
        Me.C1TextBox15.Name = "C1TextBox15"
        Me.C1TextBox15.ReadOnly = True
        Me.C1TextBox15.Size = New System.Drawing.Size(65, 14)
        Me.C1TextBox15.TabIndex = 84
        Me.C1TextBox15.Tag = Nothing
        Me.C1TextBox15.TextDetached = True
        Me.C1TextBox15.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.C1TextBox15.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'C1TextBox16
        '
        Me.C1TextBox16.BackColor = System.Drawing.SystemColors.Info
        Me.C1TextBox16.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1TextBox16.Location = New System.Drawing.Point(564, 59)
        Me.C1TextBox16.Name = "C1TextBox16"
        Me.C1TextBox16.ReadOnly = True
        Me.C1TextBox16.Size = New System.Drawing.Size(80, 14)
        Me.C1TextBox16.TabIndex = 85
        Me.C1TextBox16.Tag = Nothing
        Me.C1TextBox16.TextDetached = True
        Me.C1TextBox16.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.C1TextBox16.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'T_Textbox
        '
        Me.T_Textbox.AutoSize = False
        Me.T_Textbox.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.T_Textbox.Location = New System.Drawing.Point(715, 59)
        Me.T_Textbox.Name = "T_Textbox"
        Me.T_Textbox.Size = New System.Drawing.Size(57, 14)
        Me.T_Textbox.TabIndex = 39
        Me.T_Textbox.TabStop = False
        Me.T_Textbox.Tag = Nothing
        Me.T_Textbox.TextAlign = System.Windows.Forms.HorizontalAlignment.Right
        Me.T_Textbox.TextDetached = True
        Me.T_Textbox.TrimStart = True
        Me.T_Textbox.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label2.Location = New System.Drawing.Point(5, 12)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(59, 12)
        Me.Label2.TabIndex = 63
        Me.Label2.Text = "药品简称:"
        '
        'Label3
        '
        Me.Label3.AutoSize = True
        Me.Label3.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label3.Location = New System.Drawing.Point(476, 59)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(59, 12)
        Me.Label3.TabIndex = 68
        Me.Label3.Text = "查询时间:"
        '
        'Label4
        '
        Me.Label4.AutoSize = True
        Me.Label4.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label4.Location = New System.Drawing.Point(162, 36)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(59, 12)
        Me.Label4.TabIndex = 70
        Me.Label4.Text = "药品规格:"
        '
        'Label5
        '
        Me.Label5.AutoSize = True
        Me.Label5.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label5.Location = New System.Drawing.Point(162, 59)
        Me.Label5.Name = "Label5"
        Me.Label5.Size = New System.Drawing.Size(59, 12)
        Me.Label5.TabIndex = 72
        Me.Label5.Text = "药品产地:"
        '
        'Panel1
        '
        Me.Panel1.Controls.Add(Me.Label6)
        Me.Panel1.Controls.Add(Me.C1Combo2)
        Me.Panel1.Controls.Add(Me.Button2)
        Me.Panel1.Controls.Add(Me.CheckBox1)
        Me.Panel1.Controls.Add(Me.C1NumericEdit1)
        Me.Panel1.Controls.Add(Me.Button1)
        Me.Panel1.Controls.Add(Me.Comm1)
        Me.Panel1.Controls.Add(Me.DateTimePicker2)
        Me.Panel1.Controls.Add(Me.DateTimePicker1)
        Me.Panel1.Controls.Add(Me.Label12)
        Me.Panel1.Controls.Add(Me.C1TextBox23)
        Me.Panel1.Controls.Add(Me.C1TextBox22)
        Me.Panel1.Controls.Add(Me.C1TextBox21)
        Me.Panel1.Controls.Add(Me.C1TextBox20)
        Me.Panel1.Controls.Add(Me.C1TextBox18)
        Me.Panel1.Controls.Add(Me.Label11)
        Me.Panel1.Controls.Add(Me.Label10)
        Me.Panel1.Controls.Add(Me.Label9)
        Me.Panel1.Controls.Add(Me.Label8)
        Me.Panel1.Controls.Add(Me.Label19)
        Me.Panel1.Controls.Add(Me.C1Combo1)
        Me.Panel1.Controls.Add(Me.C1TextBox12)
        Me.Panel1.Controls.Add(Me.Label5)
        Me.Panel1.Controls.Add(Me.Label4)
        Me.Panel1.Controls.Add(Me.Label3)
        Me.Panel1.Controls.Add(Me.Label2)
        Me.Panel1.Dock = System.Windows.Forms.DockStyle.Top
        Me.Panel1.Location = New System.Drawing.Point(0, 0)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(793, 113)
        Me.Panel1.TabIndex = 38
        '
        'Label6
        '
        Me.Label6.AutoSize = True
        Me.Label6.ForeColor = System.Drawing.Color.Maroon
        Me.Label6.Location = New System.Drawing.Point(476, 11)
        Me.Label6.Name = "Label6"
        Me.Label6.Size = New System.Drawing.Size(59, 12)
        Me.Label6.TabIndex = 239
        Me.Label6.Text = "损益类型:"
        '
        'C1Combo2
        '
        Me.C1Combo2.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.C1Combo2.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1Combo2.Caption = ""
        Me.C1Combo2.CaptionHeight = 17
        Me.C1Combo2.CharacterCasing = System.Windows.Forms.CharacterCasing.Normal
        Me.C1Combo2.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.C1Combo2.EditorBackColor = System.Drawing.SystemColors.Window
        Me.C1Combo2.EditorFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.C1Combo2.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.C1Combo2.Images.Add(CType(resources.GetObject("C1Combo2.Images"), System.Drawing.Image))
        Me.C1Combo2.ItemHeight = 15
        Me.C1Combo2.Location = New System.Drawing.Point(541, 9)
        Me.C1Combo2.MatchEntryTimeout = CType(2000, Long)
        Me.C1Combo2.MaxDropDownItems = CType(5, Short)
        Me.C1Combo2.MaxLength = 32767
        Me.C1Combo2.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.C1Combo2.Name = "C1Combo2"
        Me.C1Combo2.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.C1Combo2.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.C1Combo2.Size = New System.Drawing.Size(150, 16)
        Me.C1Combo2.TabIndex = 1
        Me.C1Combo2.PropBag = resources.GetString("C1Combo2.PropBag")
        '
        'Button2
        '
        Me.Button2.Location = New System.Drawing.Point(700, 78)
        Me.Button2.Name = "Button2"
        Me.Button2.Size = New System.Drawing.Size(78, 22)
        Me.Button2.TabIndex = 179
        Me.Button2.Tag = "打印"
        Me.Button2.Text = "打 印"
        Me.Button2.UseVisualStyleBackColor = False
        '
        'CheckBox1
        '
        Me.CheckBox1.AutoSize = True
        Me.CheckBox1.Location = New System.Drawing.Point(700, 10)
        Me.CheckBox1.Name = "CheckBox1"
        Me.CheckBox1.Size = New System.Drawing.Size(84, 16)
        Me.CheckBox1.TabIndex = 178
        Me.CheckBox1.Text = "显示零库存"
        Me.CheckBox1.UseVisualStyleBackColor = True
        '
        'C1NumericEdit1
        '
        Me.C1NumericEdit1.Location = New System.Drawing.Point(541, 31)
        Me.C1NumericEdit1.Name = "C1NumericEdit1"
        Me.C1NumericEdit1.Size = New System.Drawing.Size(150, 21)
        Me.C1NumericEdit1.TabIndex = 2
        Me.C1NumericEdit1.Tag = Nothing
        Me.C1NumericEdit1.TextAlign = System.Windows.Forms.HorizontalAlignment.Right
        Me.C1NumericEdit1.Value = New Decimal(New Integer() {0, 0, 0, 0})
        Me.C1NumericEdit1.VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.None
        '
        'Button1
        '
        Me.Button1.Location = New System.Drawing.Point(700, 54)
        Me.Button1.Name = "Button1"
        Me.Button1.Size = New System.Drawing.Size(78, 22)
        Me.Button1.TabIndex = 175
        Me.Button1.Tag = "查询"
        Me.Button1.Text = "查 询"
        Me.Button1.UseVisualStyleBackColor = False
        '
        'Comm1
        '
        Me.Comm1.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Comm1.Location = New System.Drawing.Point(700, 30)
        Me.Comm1.Name = "Comm1"
        Me.Comm1.Size = New System.Drawing.Size(78, 22)
        Me.Comm1.TabIndex = 3
        Me.Comm1.Tag = "确认"
        Me.Comm1.Text = "确 认"
        Me.Comm1.UseVisualStyleBackColor = False
        '
        'DateTimePicker2
        '
        Me.DateTimePicker2.CustomFormat = "yyyy-MM-dd HH:mm:ss"
        Me.DateTimePicker2.Format = System.Windows.Forms.DateTimePickerFormat.Custom
        Me.DateTimePicker2.Location = New System.Drawing.Point(541, 79)
        Me.DateTimePicker2.Name = "DateTimePicker2"
        Me.DateTimePicker2.Size = New System.Drawing.Size(150, 21)
        Me.DateTimePicker2.TabIndex = 173
        '
        'DateTimePicker1
        '
        Me.DateTimePicker1.CustomFormat = "yyyy-MM-dd HH:mm:ss"
        Me.DateTimePicker1.Format = System.Windows.Forms.DateTimePickerFormat.Custom
        Me.DateTimePicker1.Location = New System.Drawing.Point(541, 55)
        Me.DateTimePicker1.Name = "DateTimePicker1"
        Me.DateTimePicker1.Size = New System.Drawing.Size(150, 21)
        Me.DateTimePicker1.TabIndex = 172
        '
        'Label12
        '
        Me.Label12.AutoSize = True
        Me.Label12.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label12.Location = New System.Drawing.Point(512, 83)
        Me.Label12.Name = "Label12"
        Me.Label12.Size = New System.Drawing.Size(23, 12)
        Me.Label12.TabIndex = 171
        Me.Label12.Text = "至:"
        '
        'C1TextBox23
        '
        Me.C1TextBox23.BackColor = System.Drawing.SystemColors.Info
        Me.C1TextBox23.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1TextBox23.Location = New System.Drawing.Point(68, 35)
        Me.C1TextBox23.Name = "C1TextBox23"
        Me.C1TextBox23.ReadOnly = True
        Me.C1TextBox23.Size = New System.Drawing.Size(90, 14)
        Me.C1TextBox23.TabIndex = 168
        Me.C1TextBox23.TabStop = False
        Me.C1TextBox23.Tag = Nothing
        Me.C1TextBox23.TextDetached = True
        Me.C1TextBox23.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.C1TextBox23.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'C1TextBox22
        '
        Me.C1TextBox22.BackColor = System.Drawing.SystemColors.Info
        Me.C1TextBox22.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1TextBox22.Location = New System.Drawing.Point(391, 11)
        Me.C1TextBox22.Name = "C1TextBox22"
        Me.C1TextBox22.ReadOnly = True
        Me.C1TextBox22.Size = New System.Drawing.Size(79, 14)
        Me.C1TextBox22.TabIndex = 167
        Me.C1TextBox22.TabStop = False
        Me.C1TextBox22.Tag = Nothing
        Me.C1TextBox22.TextAlign = System.Windows.Forms.HorizontalAlignment.Right
        Me.C1TextBox22.TextDetached = True
        Me.C1TextBox22.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.C1TextBox22.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'C1TextBox21
        '
        Me.C1TextBox21.BackColor = System.Drawing.SystemColors.Info
        Me.C1TextBox21.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1TextBox21.Location = New System.Drawing.Point(68, 59)
        Me.C1TextBox21.Name = "C1TextBox21"
        Me.C1TextBox21.ReadOnly = True
        Me.C1TextBox21.Size = New System.Drawing.Size(90, 14)
        Me.C1TextBox21.TabIndex = 166
        Me.C1TextBox21.TabStop = False
        Me.C1TextBox21.Tag = Nothing
        Me.C1TextBox21.TextDetached = True
        Me.C1TextBox21.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.C1TextBox21.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'C1TextBox20
        '
        Me.C1TextBox20.BackColor = System.Drawing.SystemColors.Info
        Me.C1TextBox20.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1TextBox20.Location = New System.Drawing.Point(225, 59)
        Me.C1TextBox20.Name = "C1TextBox20"
        Me.C1TextBox20.ReadOnly = True
        Me.C1TextBox20.Size = New System.Drawing.Size(245, 14)
        Me.C1TextBox20.TabIndex = 165
        Me.C1TextBox20.TabStop = False
        Me.C1TextBox20.Tag = Nothing
        Me.C1TextBox20.TextDetached = True
        Me.C1TextBox20.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.C1TextBox20.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'C1TextBox18
        '
        Me.C1TextBox18.BackColor = System.Drawing.SystemColors.Info
        Me.C1TextBox18.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1TextBox18.Location = New System.Drawing.Point(225, 35)
        Me.C1TextBox18.Name = "C1TextBox18"
        Me.C1TextBox18.ReadOnly = True
        Me.C1TextBox18.Size = New System.Drawing.Size(245, 14)
        Me.C1TextBox18.TabIndex = 163
        Me.C1TextBox18.TabStop = False
        Me.C1TextBox18.Tag = Nothing
        Me.C1TextBox18.TextDetached = True
        Me.C1TextBox18.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.C1TextBox18.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label11
        '
        Me.Label11.AutoSize = True
        Me.Label11.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label11.Location = New System.Drawing.Point(476, 35)
        Me.Label11.Name = "Label11"
        Me.Label11.Size = New System.Drawing.Size(59, 12)
        Me.Label11.TabIndex = 162
        Me.Label11.Text = "损益数量:"
        '
        'Label10
        '
        Me.Label10.AutoSize = True
        Me.Label10.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label10.Location = New System.Drawing.Point(5, 36)
        Me.Label10.Name = "Label10"
        Me.Label10.Size = New System.Drawing.Size(59, 12)
        Me.Label10.TabIndex = 161
        Me.Label10.Text = "采购单位:"
        '
        'Label9
        '
        Me.Label9.AutoSize = True
        Me.Label9.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label9.Location = New System.Drawing.Point(347, 13)
        Me.Label9.Name = "Label9"
        Me.Label9.Size = New System.Drawing.Size(47, 12)
        Me.Label9.TabIndex = 160
        Me.Label9.Text = "原库存:"
        '
        'Label8
        '
        Me.Label8.AutoSize = True
        Me.Label8.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label8.Location = New System.Drawing.Point(5, 60)
        Me.Label8.Name = "Label8"
        Me.Label8.Size = New System.Drawing.Size(59, 12)
        Me.Label8.TabIndex = 159
        Me.Label8.Text = "有 效 期:"
        '
        'C1Combo1
        '
        Me.C1Combo1.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.C1Combo1.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1Combo1.Caption = ""
        Me.C1Combo1.CaptionHeight = 17
        Me.C1Combo1.CharacterCasing = System.Windows.Forms.CharacterCasing.Normal
        Me.C1Combo1.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.C1Combo1.EditorBackColor = System.Drawing.SystemColors.Window
        Me.C1Combo1.EditorFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.C1Combo1.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.C1Combo1.Images.Add(CType(resources.GetObject("C1Combo1.Images"), System.Drawing.Image))
        Me.C1Combo1.ItemHeight = 15
        Me.C1Combo1.Location = New System.Drawing.Point(68, 10)
        Me.C1Combo1.MatchEntryTimeout = CType(2000, Long)
        Me.C1Combo1.MaxDropDownItems = CType(5, Short)
        Me.C1Combo1.MaxLength = 32767
        Me.C1Combo1.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.C1Combo1.Name = "C1Combo1"
        Me.C1Combo1.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.C1Combo1.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.C1Combo1.Size = New System.Drawing.Size(273, 16)
        Me.C1Combo1.TabIndex = 0
        Me.C1Combo1.TabStop = False
        Me.C1Combo1.PropBag = resources.GetString("C1Combo1.PropBag")
        '
        'C1TextBox1
        '
        Me.C1TextBox1.BackColor = System.Drawing.SystemColors.Window
        Me.C1TextBox1.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1TextBox1.Location = New System.Drawing.Point(69, 9)
        Me.C1TextBox1.Name = "C1TextBox1"
        Me.C1TextBox1.Size = New System.Drawing.Size(123, 14)
        Me.C1TextBox1.TabIndex = 0
        Me.C1TextBox1.Tag = Nothing
        Me.C1TextBox1.TextDetached = True
        Me.C1TextBox1.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.C1TextBox1.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'C1TextBox2
        '
        Me.C1TextBox2.BackColor = System.Drawing.SystemColors.Info
        Me.C1TextBox2.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1TextBox2.Location = New System.Drawing.Point(276, 9)
        Me.C1TextBox2.Name = "C1TextBox2"
        Me.C1TextBox2.ReadOnly = True
        Me.C1TextBox2.Size = New System.Drawing.Size(123, 14)
        Me.C1TextBox2.TabIndex = 67
        Me.C1TextBox2.Tag = Nothing
        Me.C1TextBox2.TextDetached = True
        Me.C1TextBox2.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.C1TextBox2.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'C1TextBox3
        '
        Me.C1TextBox3.BackColor = System.Drawing.SystemColors.Info
        Me.C1TextBox3.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1TextBox3.Location = New System.Drawing.Point(476, 9)
        Me.C1TextBox3.Name = "C1TextBox3"
        Me.C1TextBox3.ReadOnly = True
        Me.C1TextBox3.Size = New System.Drawing.Size(123, 14)
        Me.C1TextBox3.TabIndex = 69
        Me.C1TextBox3.Tag = Nothing
        Me.C1TextBox3.TextDetached = True
        Me.C1TextBox3.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.C1TextBox3.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'C1TextBox4
        '
        Me.C1TextBox4.BackColor = System.Drawing.SystemColors.Info
        Me.C1TextBox4.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1TextBox4.Location = New System.Drawing.Point(69, 34)
        Me.C1TextBox4.Name = "C1TextBox4"
        Me.C1TextBox4.ReadOnly = True
        Me.C1TextBox4.Size = New System.Drawing.Size(123, 14)
        Me.C1TextBox4.TabIndex = 71
        Me.C1TextBox4.Tag = Nothing
        Me.C1TextBox4.TextDetached = True
        Me.C1TextBox4.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.C1TextBox4.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'C1TextBox5
        '
        Me.C1TextBox5.BackColor = System.Drawing.SystemColors.Info
        Me.C1TextBox5.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1TextBox5.Location = New System.Drawing.Point(276, 34)
        Me.C1TextBox5.Name = "C1TextBox5"
        Me.C1TextBox5.ReadOnly = True
        Me.C1TextBox5.Size = New System.Drawing.Size(123, 14)
        Me.C1TextBox5.TabIndex = 73
        Me.C1TextBox5.Tag = Nothing
        Me.C1TextBox5.TextDetached = True
        Me.C1TextBox5.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.C1TextBox5.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'C1TextBox6
        '
        Me.C1TextBox6.BackColor = System.Drawing.SystemColors.Info
        Me.C1TextBox6.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1TextBox6.Location = New System.Drawing.Point(476, 34)
        Me.C1TextBox6.Name = "C1TextBox6"
        Me.C1TextBox6.ReadOnly = True
        Me.C1TextBox6.Size = New System.Drawing.Size(123, 14)
        Me.C1TextBox6.TabIndex = 75
        Me.C1TextBox6.Tag = Nothing
        Me.C1TextBox6.TextDetached = True
        Me.C1TextBox6.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.C1TextBox6.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'C1TrueDBGrid1
        '
        Me.C1TrueDBGrid1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.C1TrueDBGrid1.GroupByCaption = "Drag a column header here to group by that column"
        Me.C1TrueDBGrid1.Images.Add(CType(resources.GetObject("C1TrueDBGrid1.Images"), System.Drawing.Image))
        Me.C1TrueDBGrid1.Location = New System.Drawing.Point(0, 113)
        Me.C1TrueDBGrid1.Name = "C1TrueDBGrid1"
        Me.C1TrueDBGrid1.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.C1TrueDBGrid1.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.C1TrueDBGrid1.PreviewInfo.ZoomFactor = 75.0R
        Me.C1TrueDBGrid1.PrintInfo.PageSettings = CType(resources.GetObject("C1TrueDBGrid1.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.C1TrueDBGrid1.Size = New System.Drawing.Size(793, 399)
        Me.C1TrueDBGrid1.TabIndex = 40
        Me.C1TrueDBGrid1.Text = "C1TrueDBGrid1"
        Me.C1TrueDBGrid1.PropBag = resources.GetString("C1TrueDBGrid1.PropBag")
        '
        'YkYf_Bsby
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(793, 512)
        Me.Controls.Add(Me.C1TrueDBGrid1)
        Me.Controls.Add(Me.Panel1)
        Me.Icon = CType(resources.GetObject("$this.Icon"), System.Drawing.Icon)
        Me.Name = "YkYf_Bsby"
        Me.ShowInTaskbar = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "药库药品报损报益"
        CType(Me.C1Holder1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1TextBox12, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1TextBox10, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1TextBox9, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1TextBox8, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1TextBox7, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1TextBox11, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1TextBox13, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1TextBox14, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1TextBox15, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1TextBox16, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T_Textbox, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Panel1.ResumeLayout(False)
        Me.Panel1.PerformLayout()
        CType(Me.C1Combo2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1NumericEdit1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1TextBox23, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1TextBox22, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1TextBox21, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1TextBox20, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1TextBox18, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1Combo1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1TextBox1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1TextBox2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1TextBox3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1TextBox4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1TextBox5, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1TextBox6, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1TrueDBGrid1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents C1Holder1 As C1.Win.C1Command.C1CommandHolder
    Friend WithEvents Move1 As C1.Win.C1Command.C1Command
    Friend WithEvents Move2 As C1.Win.C1Command.C1Command
    Friend WithEvents Move3 As C1.Win.C1Command.C1Command
    Friend WithEvents Move4 As C1.Win.C1Command.C1Command
    Friend WithEvents Control1 As C1.Win.C1Command.C1CommandControl
    Friend WithEvents Control2 As C1.Win.C1Command.C1CommandControl
    Friend WithEvents Control3 As C1.Win.C1Command.C1CommandControl
    Friend WithEvents C1Command1 As C1.Win.C1Command.C1Command
    Friend WithEvents C1Command2 As C1.Win.C1Command.C1Command
    Friend WithEvents Label19 As System.Windows.Forms.Label
    Friend WithEvents C1TextBox12 As C1.Win.C1Input.C1TextBox
    Friend WithEvents C1TextBox10 As C1.Win.C1Input.C1TextBox
    Friend WithEvents C1TextBox9 As C1.Win.C1Input.C1TextBox
    Friend WithEvents C1TextBox8 As C1.Win.C1Input.C1TextBox
    Friend WithEvents C1TextBox7 As C1.Win.C1Input.C1TextBox
    Friend WithEvents C1TextBox11 As C1.Win.C1Input.C1TextBox
    Friend WithEvents C1TextBox13 As C1.Win.C1Input.C1TextBox
    Friend WithEvents C1TextBox14 As C1.Win.C1Input.C1TextBox
    Friend WithEvents C1TextBox15 As C1.Win.C1Input.C1TextBox
    Friend WithEvents C1TextBox16 As C1.Win.C1Input.C1TextBox
    Friend WithEvents T_Textbox As C1.Win.C1Input.C1TextBox
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents Label5 As System.Windows.Forms.Label
    Friend WithEvents Label4 As System.Windows.Forms.Label
    Friend WithEvents Label3 As System.Windows.Forms.Label
    Friend WithEvents Label2 As System.Windows.Forms.Label
    Friend WithEvents C1TextBox1 As C1.Win.C1Input.C1TextBox
    Friend WithEvents C1TextBox2 As C1.Win.C1Input.C1TextBox
    Friend WithEvents C1TextBox3 As C1.Win.C1Input.C1TextBox
    Friend WithEvents C1TextBox4 As C1.Win.C1Input.C1TextBox
    Friend WithEvents C1TextBox5 As C1.Win.C1Input.C1TextBox
    Friend WithEvents C1TextBox6 As C1.Win.C1Input.C1TextBox
    Friend WithEvents C1Combo1 As C1.Win.C1List.C1Combo
    Friend WithEvents Label11 As System.Windows.Forms.Label
    Friend WithEvents Label10 As System.Windows.Forms.Label
    Friend WithEvents Label9 As System.Windows.Forms.Label
    Friend WithEvents Label8 As System.Windows.Forms.Label
    Friend WithEvents C1TextBox22 As C1.Win.C1Input.C1TextBox
    Friend WithEvents C1TextBox21 As C1.Win.C1Input.C1TextBox
    Friend WithEvents C1TextBox20 As C1.Win.C1Input.C1TextBox
    Friend WithEvents C1TextBox18 As C1.Win.C1Input.C1TextBox
    Friend WithEvents C1TextBox23 As C1.Win.C1Input.C1TextBox
    Friend WithEvents DateTimePicker1 As System.Windows.Forms.DateTimePicker
    Friend WithEvents Label12 As System.Windows.Forms.Label
    Friend WithEvents DateTimePicker2 As System.Windows.Forms.DateTimePicker
    Friend WithEvents Button1 As System.Windows.Forms.Button
    Friend WithEvents Comm1 As System.Windows.Forms.Button
    Friend WithEvents C1NumericEdit1 As C1.Win.C1Input.C1NumericEdit
    Friend WithEvents CheckBox1 As System.Windows.Forms.CheckBox
    Friend WithEvents C1TrueDBGrid1 As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents Button2 As System.Windows.Forms.Button
    Friend WithEvents Label6 As System.Windows.Forms.Label
    Friend WithEvents C1Combo2 As C1.Win.C1List.C1Combo
End Class
