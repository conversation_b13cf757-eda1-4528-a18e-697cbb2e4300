﻿Imports DCSoft.WinForms.Controls
Imports System.ComponentModel
Imports System.Text
Imports Stimulsoft.Report

Partial Public Class NurseStation
    Dim dt As New DataTable
    Dim iCount As Integer  '查询出的病人数
    Dim bllBl As New BLLOld.B_Bl
    Private _CurrentPatient As ModelOld.M_PatientInfo = Nothing
    Private CurrentItem As DCCardListViewItem = Nothing

    Private jnyj As Decimal

    Private Sub lvwPatients_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles lvwPatients.Load
        Call InitDCCardListViewTemplate()     '初始化控件
        ' lvwPatients.ShowCardShade = True
        AddHandler lvwPatients.MouseClick, AddressOf _cardListViewControl_MouseClick

    End Sub

    Private Sub NurseStation_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        btnRefresh_Click(Nothing, Nothing)
    End Sub

#Region "控件动作"
    Private Sub btnRefresh_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnRefresh.Click, QueryButton1.Click
        If sender Is Nothing Then
            NameTextbox.Text = ""
        ElseIf sender.Text = "刷新" Then
            NameTextbox.Text = ""
        End If
        Dim thread As New Threading.Thread(AddressOf Inital)
        thread.IsBackground = True
        thread.Start()
    End Sub


    Sub _cardListViewControl_MouseClick(ByVal sender As Object, ByVal e As MouseEventArgs)
        If e.Button = System.Windows.Forms.MouseButtons.Right Then
            _CurrentPatient = Nothing
            CurrentItem = Me.lvwPatients.GetItemAt(e.X, e.Y) '获取条目位置
            If CurrentItem Is Nothing Then Exit Sub
            If CurrentItem.GetValue("ShenqingCy") IsNot Nothing Then
                cmOut.Enabled = False
                cmTWD.Enabled = False
                cmYJ.Enabled = False
                cmCKD.Enabled = False

                换床ToolStripMenuItem.Enabled = False
                配床ToolStripMenuItem.Enabled = False
                退床ToolStripMenuItem.Enabled = False
            Else
                If HisVar.HisVar.Sqldal.GetSingle(" SELECT count(1) FROM dbo.Zd_Qx2 JOIN dbo.Zd_QxModule " &
        " ON Zd_QxModule.Module_Code = Zd_Qx2.Module_Code WHERE " &
        " Zd_Qx2.Glz_Code='" & HisVar.HisVar.GlzCode & "' and Dl_name='住院管理' AND Xl_Name='缴纳押金'") = 0 Then
                    cmYJ.Enabled = False
                Else
                    cmYJ.Enabled = True
                End If

                If HisVar.HisVar.Sqldal.GetSingle(" SELECT count(1) FROM dbo.Zd_Qx2 JOIN dbo.Zd_QxModule " &
        " ON Zd_QxModule.Module_Code = Zd_Qx2.Module_Code WHERE " &
        " Zd_Qx2.Glz_Code='" & HisVar.HisVar.GlzCode & "' and Dl_name='护士站' AND Xl_Name='体温单'") = 0 Then
                    cmTWD.Enabled = False
                Else
                    cmTWD.Enabled = True
                End If

                If HisVar.HisVar.Sqldal.GetSingle(" SELECT count(1) FROM dbo.Zd_Qx2 JOIN dbo.Zd_QxModule " &
        " ON Zd_QxModule.Module_Code = Zd_Qx2.Module_Code WHERE " &
        " Zd_Qx2.Glz_Code='" & HisVar.HisVar.GlzCode & "' and Dl_name='护士站' AND Xl_Name='申请出院'") = 0 Then
                    cmOut.Enabled = False
                Else
                    cmOut.Enabled = True
                End If

                If HisVar.HisVar.Sqldal.GetSingle(" SELECT count(1) FROM dbo.Zd_Qx2 JOIN dbo.Zd_QxModule " &
        " ON Zd_QxModule.Module_Code = Zd_Qx2.Module_Code WHERE " &
        " Zd_Qx2.Glz_Code='" & HisVar.HisVar.GlzCode & "' and Dl_name='住院查询' AND Xl_Name='在院患者费用'") = 0 Then
                    cmCKD.Enabled = False
                Else
                    cmCKD.Enabled = True
                End If

            End If

            If Trim(CurrentItem.GetValue("name") & "") <> "" And CurrentItem.GetValue("ShenqingCy") Is Nothing Then
                换床ToolStripMenuItem.Enabled = True
                配床ToolStripMenuItem.Enabled = False
                退床ToolStripMenuItem.Enabled = True
            End If

            If Trim(CurrentItem.GetValue("name") & "") = "" Then
                cmOut.Enabled = False
                cmTWD.Enabled = False
                cmYJ.Enabled = False
                cmCKD.Enabled = False

                换床ToolStripMenuItem.Enabled = False
                配床ToolStripMenuItem.Enabled = True
                退床ToolStripMenuItem.Enabled = False
            End If
            _CurrentPatient = TryCast(CurrentItem.DataBoundItem, ModelOld.M_PatientInfo)
            myContextMenu.Show(Me.lvwPatients, e.X, e.Y)
        End If
    End Sub


    Private Sub cmViewDetails_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmYJ.Click
        If Me._CurrentPatient IsNot Nothing Then
            Dim jnyj As New Xs_Zy_Yj12(_CurrentPatient, Now, AddressOf JfjeFreshItem)

            jnyj.ShowDialog()

            'MessageBox.Show(Me, "查看" + _CurrentPatient.Bl_Code + "的详细信息")
        End If
    End Sub

    Private Sub cmTWD_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmTWD.Click
        If Me._CurrentPatient IsNot Nothing Then
            Dim frm As New Temparature(_CurrentPatient.Bl_Code)
            BaseFunc.BaseFunc.addTabControl(frm, "体温单-" & _CurrentPatient.Bl_Code)
        End If
    End Sub

    Private Sub cmOut_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmOut.Click
        If Me._CurrentPatient IsNot Nothing Then
            Dim frm As New Xs_Zy_Sqcy(_CurrentPatient, AddressOf reFreshItem)
            BaseFunc.BaseFunc.addTabControl(frm, frm.Text)
        End If
    End Sub

    Private Sub 换床ToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles 换床ToolStripMenuItem.Click
        '        Dim frm As New BedExchange(_CurrentPatient)
        Dim frm As New ZTHisNurse.BedChange(_CurrentPatient.Bl_Code, "", "病人换床")
        If frm.ShowDialog() = Windows.Forms.DialogResult.OK Then
            btnRefresh_Click(Nothing, Nothing)
        End If
    End Sub

    Private Sub 配床ToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles 配床ToolStripMenuItem.Click
        '        Dim frm As New BedAllocation(_CurrentPatient)
        Dim frm As New ZTHisNurse.BedChange("", _CurrentPatient.BedID, "病人配床")
        If frm.ShowDialog() = Windows.Forms.DialogResult.OK Then
            btnRefresh_Click(Nothing, Nothing)
        End If

    End Sub

    Private Sub 退床ToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles 退床ToolStripMenuItem.Click
        If _CurrentPatient IsNot Nothing Then
            If MsgBox("您确定要给" & _CurrentPatient.Name & "退床吗？", MsgBoxStyle.OkCancel + MsgBoxStyle.DefaultButton1, "提示：") = MsgBoxResult.Ok Then
                HisVar.HisVar.Sqldal.ExecuteSql("update bl set bc_code=null where bl_code='" & _CurrentPatient.Bl_Code & "'")
            End If

            btnRefresh_Click(Nothing, Nothing)
        End If
    End Sub

    Private Sub cmCKD_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmCKD.Click

        Dim My_Dataset As New DataSet
        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "SELECT Bl.Bl_Code,Ry_YlCode,Bxlb_Name,Bq_Name,Bc_Name,Ry_Name,Ry_Jc,Ry_Sex,Ry_Sfzh,Ry_BlCode,Ry_Csdate,Ry_Address,Ks_Name,Ry_RyDate,Jsr_Code,Ry_Memo,Bl.Bxlb_Code,Jb_Name,DATEDIFF(yyyy,Ry_Csdate,getdate()) AS Ry_Age,Ry_BlCode,isnull(Jf_Money,0)Jf_Money,Isnull(Xf_YpMoney,0)Xf_YpMoney,Isnull(Xf_Money,0)-Isnull(Xf_YpMoney,0) Xf_XmMoney,Isnull(Xf_Money,0)Xf_Money,isnull(Jf_Money,0)-isnull(Xf_Money,0) AS New_Money,Jb_Code,Datediff(day,Ry_Rydate,Isnull(Ry_CyDate,getdate())) as Ts FROM Zd_YyKs,Zd_Bxlb,Bl Left Join V_YyBc on V_YyBc.Bc_Code=Bl.Bc_Code left join (Select Sum(Cf_Money) AS Xf_Money,Sum(Cf_YpMoney) as Xf_YpMoney,Bl_Code From Bl_Cf where Cf_Qr='是' Group By Bl_Code) a on Bl.Bl_Code=a.Bl_Code left join(Select Sum(Jf_Money) AS Jf_Money,Bl_Code From Bl_Jf Group By Bl_Code) b on Bl.Bl_Code = b.Bl_Code Where Zd_Bxlb.Bxlb_Code=Bl.Bxlb_Code And Isnull(Ry_CyJsr,'')=''   And Zd_YyKs.Ks_Code=Bl.Ks_Code  and Bl.Bl_Code='" & _CurrentPatient.Bl_Code & "'", "催款单", True)

        Dim StiRpt As New StiReport
        StiRpt.Load(".\Rpt\催款单.mrt")
        StiRpt.ReportName = "催款单"
        StiRpt.RegData(My_Dataset.Tables("催款单"))
        StiRpt.Compile()
        StiRpt("打单人") = HisVar.HisVar.JsrName
        StiRpt("打印时间") = Format(Now, "yyyy-MM-dd HH:mm:ss")

        'StiRpt.Design()
        StiRpt.Show()
    End Sub
#End Region

#Region "控件初始化"

    Private Sub InitDCCardListViewTemplate()

        lvwPatients.CardBorderColor = Color.Blue
        lvwPatients.CardBorderWith = 2
        lvwPatients.ShowCardShade = True
        lvwPatients.ImageAnimateInterval = 100
        lvwPatients.CardWidth = 155
        lvwPatients.CardHeight = 180
        lvwPatients.TooltipWidth = 290
        lvwPatients.TooltipHeight = 180

        lvwPatients.CardTemplate.AddImage("FaceImage", Nothing, 3, 3, 71, 63)
        Dim ChuangWeiHao As DCCardStringItem = Me.lvwPatients.CardTemplate.AddString("Area", "病区", 80, 10, 80, 21)
        ChuangWeiHao.Align = StringAlignment.Near
        ChuangWeiHao.FontName = "宋体"
        ChuangWeiHao.FontSize = 12
        ChuangWeiHao.FontStyle = FontStyle.Bold
        ChuangWeiHao.Color = Color.Blue

        Dim NianLing As DCCardStringItem = Me.lvwPatients.CardTemplate.AddString("BedName", "病床", 80, 31, 80, 21)
        NianLing.Align = StringAlignment.Near
        NianLing.FontName = "宋体"
        NianLing.FontSize = 12
        NianLing.FontStyle = FontStyle.Bold


        Dim XingMing As DCCardStringItem = Me.lvwPatients.CardTemplate.AddString("Name", Nothing, 2, 70, 130, 21)
        XingMing.Align = StringAlignment.Near
        XingMing.FontName = "宋体"
        XingMing.FontSize = 12
        XingMing.FontStyle = FontStyle.Bold


        Dim JingBing As DCCardStringItem = Me.lvwPatients.CardTemplate.AddString("Diagnose", Nothing, 2, 91, 151, 21)
        JingBing.Align = StringAlignment.Near
        JingBing.FontName = "宋体"
        JingBing.FontSize = 12


        Dim YuJiaoJin_Label As DCCardStringItem = Me.lvwPatients.CardTemplate.AddString(Nothing, "押金：", 2, 112, 75, 21)
        YuJiaoJin_Label.Align = StringAlignment.Near
        YuJiaoJin_Label.FontName = "宋体"
        YuJiaoJin_Label.FontSize = 12

        Dim YuJiaoJin As DCCardStringItem = Me.lvwPatients.CardTemplate.AddString("TotalCost", Nothing, 65, 112, 90, 21)
        YuJiaoJin.Align = StringAlignment.Near
        YuJiaoJin.FontName = "宋体"
        YuJiaoJin.FontSize = 12

        Dim ShiJiFeiYong_Label As DCCardStringItem = Me.lvwPatients.CardTemplate.AddString(Nothing, "总费用：", 2, 133, 75, 21)
        ShiJiFeiYong_Label.Align = StringAlignment.Near
        ShiJiFeiYong_Label.FontName = "宋体"
        ShiJiFeiYong_Label.FontSize = 12

        Dim ShiJiFeiYong As DCCardStringItem = Me.lvwPatients.CardTemplate.AddString("SpendCost", Nothing, 65, 133, 90, 21)
        ShiJiFeiYong.Align = StringAlignment.Near
        ShiJiFeiYong.FontName = "宋体"
        ShiJiFeiYong.FontSize = 12

        Dim ShiJiYuE_Label As DCCardStringItem = Me.lvwPatients.CardTemplate.AddString(Nothing, "余额：", 2, 154, 75, 21)
        ShiJiYuE_Label.Align = StringAlignment.Near
        ShiJiYuE_Label.FontName = "宋体"
        ShiJiYuE_Label.FontSize = 12
        ShiJiYuE_Label.FontStyle = FontStyle.Bold

        Dim ShiJiYuE As DCCardStringItem = Me.lvwPatients.CardTemplate.AddString("Balance", Nothing, 65, 154, 90, 21)
        ShiJiYuE.Align = StringAlignment.Near
        ShiJiYuE.FontName = "宋体"
        ShiJiYuE.FontSize = 12
        ShiJiYuE.FontStyle = FontStyle.Bold
        lvwPatients.CardTemplate.AddImage("ShenqingCy", Nothing, 23, 46, 107, 87)


        Dim xm_Label As DCCardStringItem = Me.lvwPatients.TooltipContentItems.AddString(Nothing, "姓  名：", 40, 10, 90, 21)
        xm_Label.FontName = "宋体"
        xm_Label.FontSize = 13
        xm_Label.FontStyle = FontStyle.Bold
        xm_Label.Color = Color.Blue

        Dim xm As DCCardStringItem = Me.lvwPatients.TooltipContentItems.AddString("Name", Nothing, 130, 10, 130, 21)
        xm.FontName = "宋体"
        xm.FontSize = 13
        xm.FontStyle = FontStyle.Bold
        xm.Color = Color.Blue

        Dim Ry_RyDate_Label As DCCardStringItem = Me.lvwPatients.TooltipContentItems.AddString(Nothing, "入院时间：", 2, 31, 90, 21)
        Ry_RyDate_Label.FontName = "宋体"
        Ry_RyDate_Label.FontSize = 12
        Ry_RyDate_Label.FontStyle = FontStyle.Bold

        Dim Ry_RyDate As DCCardStringItem = Me.lvwPatients.TooltipContentItems.AddString("Ry_RyDate", Nothing, 90, 31, 160, 21)
        Ry_RyDate.FontName = "宋体"
        Ry_RyDate.FontSize = 12
        Ry_RyDate.FontStyle = FontStyle.Bold

        Dim Ks_Name_Label As DCCardStringItem = Me.lvwPatients.TooltipContentItems.AddString(Nothing, "科    室：", 2, 52, 90, 21)
        Ks_Name_Label.FontName = "宋体"
        Ks_Name_Label.FontSize = 12
        Ks_Name_Label.FontStyle = FontStyle.Bold

        Dim Ks_Name As DCCardStringItem = Me.lvwPatients.TooltipContentItems.AddString("Ks_Name", Nothing, 90, 52, 125, 21)
        Ks_Name.FontName = "宋体"
        Ks_Name.FontSize = 12
        Ks_Name.FontStyle = FontStyle.Bold

        Dim Ys_Name_Label As DCCardStringItem = Me.lvwPatients.TooltipContentItems.AddString(Nothing, "主治医师：", 2, 73, 90, 21)
        Ys_Name_Label.FontName = "宋体"
        Ys_Name_Label.FontSize = 12
        Ys_Name_Label.FontStyle = FontStyle.Bold

        Dim Ys_Name As DCCardStringItem = Me.lvwPatients.TooltipContentItems.AddString("Ys_Name", Nothing, 90, 73, 125, 21)
        Ys_Name.FontName = "宋体"
        Ys_Name.FontSize = 12
        Ys_Name.FontStyle = FontStyle.Bold

        Dim Bxlb_Name_Label As DCCardStringItem = Me.lvwPatients.TooltipContentItems.AddString(Nothing, "病人类别：", 2, 94, 90, 21)
        Bxlb_Name_Label.FontName = "宋体"
        Bxlb_Name_Label.FontSize = 12
        Bxlb_Name_Label.FontStyle = FontStyle.Bold

        Dim Bxlb_Name As DCCardStringItem = Me.lvwPatients.TooltipContentItems.AddString("Bxlb_Name", Nothing, 90, 94, 125, 21)
        Bxlb_Name.FontName = "宋体"
        Bxlb_Name.FontSize = 12
        Bxlb_Name.FontStyle = FontStyle.Bold

        Dim Ry_Address_Label As DCCardStringItem = Me.lvwPatients.TooltipContentItems.AddString(Nothing, "家庭地址：", 2, 115, 90, 21)
        Ry_Address_Label.FontName = "宋体"
        Ry_Address_Label.FontSize = 12
        Ry_Address_Label.FontStyle = FontStyle.Bold

        Dim Ry_Address As DCCardStringItem = Me.lvwPatients.TooltipContentItems.AddString("Ry_Address", Nothing, 90, 115, 125, 21)
        Ry_Address.FontName = "宋体"
        Ry_Address.FontSize = 12
        Ry_Address.FontStyle = FontStyle.Bold

        Dim HospitalDay_Label As DCCardStringItem = Me.lvwPatients.TooltipContentItems.AddString(Nothing, "在院天数：", 2, 136, 90, 21)
        HospitalDay_Label.FontName = "宋体"
        HospitalDay_Label.FontSize = 12
        HospitalDay_Label.FontStyle = FontStyle.Bold

        Dim HospitalDay As DCCardStringItem = Me.lvwPatients.TooltipContentItems.AddString("HospitalDay", Nothing, 90, 136, 125, 21)
        HospitalDay.FontName = "宋体"
        HospitalDay.FontSize = 12
        HospitalDay.FontStyle = FontStyle.Bold

    End Sub

#End Region

#Region "自定义函数"
    Delegate Sub MyInvoke()
    Private Sub Inital()
        If Me.InvokeRequired = True Then
            Dim del As New MyInvoke(AddressOf Inital)
            Me.BeginInvoke(del)
        Else
            Call Data_Query()
            Dim ds As List(Of ModelOld.M_PatientInfo) = GetPatientEntities() '导入病人信息
            lvwPatients.DataSource = ds
            Dim ZyCount As Integer = bllBl.GetRecordCount(" Ry_CyJsr IS  NULL")
            T_Label.Text = "管辖已配床患者:" & iCount & "人，" & "管辖未配床患者:" &
                IIf(HisPara.PublicConfig.ZyCfKsXz = "是", HisVar.HisVar.Sqldal.GetSingle("SELECT COUNT(1) FROM bl WHERE Ry_CyJsr IS NULL  AND  Bc_Code IS NULL AND Ks_Code='" & HisVar.HisVar.XmKs & "'"),
                 HisVar.HisVar.Sqldal.GetSingle("SELECT COUNT(1) FROM bl WHERE Ry_CyJsr IS NULL  AND  Bc_Code IS NULL ")) &
                 "人，在院患者:" & ZyCount & "人，病床:" &
                HisVar.HisVar.Sqldal.GetSingle("select count(1) from Zd_YyBc") & "张，空病床:" &
                HisVar.HisVar.Sqldal.GetSingle("select count(1) from Zd_YyBc") - ZyCount +
                HisVar.HisVar.Sqldal.GetSingle("SELECT COUNT(1) FROM bl WHERE Ry_CyJsr IS NULL AND  isnull(Bc_Code,'')='' ") & "张"
            Call Deposit_Caution()
        End If
    End Sub

    Private Sub Data_Query()
        Dim strSql As New StringBuilder
        strSql.Append("SELECT  bl.Yy_code,bl.bl_code,bl.Ks_Code,Ks_Name,V_YyBc.Bc_Code,Bc_Name,Bc_Jc,Bq_Code,Bq_Name,")
        strSql.Append("Jb_Code,Ry_Name,Ry_Jc,Ry_Sex,Ry_Sfzh,Ry_Address,Jb_Code,Jb_Name ,bl.Ys_Code,Ys_Name,Bl.Bxlb_Code,Bxlb_Name,Ry_RyDate,")
        strSql.Append("isnull(Jf_Money,0)Jf_Money,Isnull(Xf_YpMoney,0)Xf_YpMoney,Isnull(Xf_Money,0)-Isnull(Xf_YpMoney,0) Xf_XmMoney,")
        strSql.Append("ISNULL(Xf_Money,0)Xf_Money,isnull(Jf_Money,0)-isnull(Xf_Money,0) AS New_Money,DATEDIFF(DAY,Ry_RyDate,GETDATE()) HospitalDay,Cy_Qr")
        strSql.Append(" FROM (SELECT * FROM dbo.Bl WHERE Ry_CyJsr IS  NULL ")

        If Trim(NameTextbox.Text & "") <> "" Then
            strSql.Append("and (Ry_Jc like '%" & NameTextbox.Text & "%' or Ry_Name like '%" & NameTextbox.Text & "%')")
        End If

        If ZTHisPara.PublicConfig.HsKsXz Then
            strSql.Append(" and bl.Ks_Code='" & HisVar.HisVar.XmKs & "' ")
        End If
        strSql.Append(" ) Bl LEFT JOIN dbo.Zd_YyKs ON  Zd_YyKs.Ks_Code = Bl.Ks_Code")
        strSql.Append(" LEFT JOIN dbo.Zd_YyYs ON Zd_YyYs.Ys_Code = Bl.Ys_Code ")
        strSql.Append(" LEFT JOIN dbo.Zd_Bxlb ON  Zd_Bxlb.Bxlb_Code = Bl.Bxlb_Code")
        strSql.Append(" right join dbo.V_YyBc ON  Bl.Bc_Code=dbo.V_YyBc.Bc_Code ")
        strSql.Append(" LEFT join (Select Sum(Cf_Money) AS Xf_Money,Sum(Cf_YpMoney) as Xf_YpMoney,Bl_Code From Bl_Cf where Cf_Qr='是' Group By Bl_Code) a on Bl.Bl_Code=a.Bl_Code ")
        strSql.Append(" left join(Select Sum(Jf_Money) AS Jf_Money,Bl_Code From Bl_Jf Group By Bl_Code) b on Bl.Bl_Code = b.Bl_Code")

        dt = HisVar.HisVar.Sqldal.Query(strSql.ToString()).Tables(0)
    End Sub

    Private Function GetPatientEntities() As List(Of ModelOld.M_PatientInfo)
        Dim image1 As Image = MyResources.C_Resources.getimage("boy")
        Dim image2 As Image = MyResources.C_Resources.getimage("girl")
        Dim entities As New List(Of ModelOld.M_PatientInfo)()
        iCount = 0
        For Each row In dt.Rows
            Dim pa As New ModelOld.M_PatientInfo()
            With pa
                .Bl_Code = row("Bl_Code") & ""
                .Area = row("Bq_Name") & ""
                .Name = row("Ry_Name") & ""
                .Sex = Microsoft.VisualBasic.Switch(Trim(row("Ry_Sex") & "") = "男", 1, Trim(row("Ry_Sex") & "") = "女", 0, Trim(row("Ry_Sex") & "") = "", 2)
                .BedID = row("Bc_code") & ""
                .BedName = row("Bc_Name") & ""
                .Diagnose = row("Jb_Name") & ""
                If Trim(row("Bl_Code") & "") = "" Then
                    .SpendCost = Nothing
                Else
                    .SpendCost = row("Xf_Money")
                    iCount += 1
                End If
                .TotalCost = IIf(Trim(row("Bl_Code") & "") = "", Nothing, row("Jf_Money"))
                .Balance = IIf(Trim(row("Bl_Code") & "") = "", Nothing, row("New_Money"))
                .Ry_RyDate = row("Ry_RyDate") & ""
                .Ks_Name = row("Ks_Name") & ""
                .Ys_Name = row("Ys_Name") & ""
                .Bxlb_Name = row("Bxlb_Name") & ""
                .Ry_Address = row("Ry_Address") & ""
                .HospitalDay = IIf(row("HospitalDay") & "" <> "", row("HospitalDay") & "天", row("HospitalDay") & "")
                .Cy_Qr = row("Cy_Qr") & ""
                If .Sex = 1 Then
                    .FaceImage = image1
                ElseIf .Sex = 0 Then
                    .FaceImage = image2
                Else
                    .FaceImage = Nothing
                End If
                If .Cy_Qr = "是" Then
                    .ShenqingCy = MyResources.C_Resources.getimage("申请出院")
                Else
                    .ShenqingCy = Nothing
                End If
            End With
            entities.Add(pa)
        Next
        Return entities
    End Function

    Private Sub Deposit_Caution()
        For Each item In lvwPatients.Items
            If Trim(item.GetValue("name") & "") = "" Then
                item.SetValue("TotalCost", "")
                item.SetValue("SpendCost", "")
                item.SetValue("Balance", "")
            ElseIf Trim(item.GetValue("name") & "") <> "" And Trim(item.GetValue("Balance") & "") < HisPara.PublicConfig.Qfed And
                item.GetValue("ShenqingCy") Is Nothing Then
                item.BorderColor = Color.Red
                item.BorderWidth = 3
                item.Invalidate()
            End If
        Next
    End Sub

    Private Sub reFreshItem(ByVal lb As String)
        Select Case lb
            Case "申请出院"
                CurrentItem.SetValue("Cy_Qr", _CurrentPatient.Cy_Qr)
                CurrentItem.SetValue("ShenqingCy", _CurrentPatient.ShenqingCy)
                CurrentItem.BorderColor = Color.Blue
                CurrentItem.BorderWidth = 1
                CurrentItem.SetValue("Ry_Address", "eqweqwe")

        End Select

        CurrentItem.Invalidate()
    End Sub
    Private Sub JfjeFreshItem(ByVal m_Jf As ModelOld.M_Bl_Jf)
        jnyj = m_Jf.Jf_Money

        _CurrentPatient.TotalCost = jnyj + _CurrentPatient.TotalCost
        _CurrentPatient.Balance = _CurrentPatient.Balance + jnyj
        CurrentItem.SetValue("TotalCost", _CurrentPatient.TotalCost)
        CurrentItem.SetValue("Balance", _CurrentPatient.Balance)
        CurrentItem.BorderColor = Color.Blue
        CurrentItem.BorderWidth = 1

        CurrentItem.Invalidate()
    End Sub

#End Region



End Class





