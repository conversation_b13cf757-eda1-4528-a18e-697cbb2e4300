﻿Imports BaseClass
Imports System.Drawing
Imports System.Windows.Forms
Imports Common
Public Class JYYQGLDic12

#Region "变量定义"
    Dim My_Cc As New C_Cc
    Dim BllYq As New BLLOld.B_LIS_DictDev
    Dim ModelYq As New ModelOld.M_LIS_DictDev
    Dim BllEleOne As New BLLOld.B_LIS_Element1
    Dim BllKs As New BLLOld.B_Zd_YyKs
    Dim BllEleTwoYq As New BLLOld.B_LIS_Element2
    Dim ModelKs As New ModelOld.M_Zd_YyKs

    Dim oldInterfacePro As String
#End Region

#Region "传参"

    Dim Rinsert As Boolean
    Dim Rrow As DataRow
    Dim RZbtb As DataTable
    Dim Rrc As C_RowChange
#End Region

    Public Sub New(ByVal tinsert As Boolean, ByVal trow As DataRow, ByVal tZbtb As DataTable, ByRef trc As C_RowChange)
        InitializeComponent()
        Rinsert = tinsert
        Rrow = trow
        RZbtb = tZbtb
        Rrc = trc
    End Sub

    Private Sub JYYQGLDic12_FormClosed(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosedEventArgs) Handles Me.FormClosed
        RemoveHandler Rrc.RowChanged, AddressOf Data_Show
    End Sub

    Private Sub JYYQGLDic12_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        AddHandler Rrc.RowChanged, AddressOf Data_Show
        Call Form_Init()
        If Rinsert = True Then Call Data_Clear() Else Call Data_Show(Rrow)
    End Sub


#Region "窗体__事件"

    Private Sub Form_Init()
        Panel1.Height = 27
        ToolBar1.Location = New Point(2, 4)

        YqCodeTb.Enabled = False
        YqJcTb.Enabled = False
        YqFileTb.Enabled = False
        YqFileNameTb.Enabled = False

        With YqInterfaceCb
            .Additem = "串口"
            .Additem = "TCP/IP"
            .Additem = "中间库"
            .Additem = "中间文件"
            .Additem = "接口程序"
            .DisplayColumns(1).Visible = False
            .DroupDownWidth = .Width - .CaptainWidth
            .SelectedIndex = 0
        End With

        With YqBaudRateCb
            .Additem = "75"
            .Additem = "110"
            .Additem = "134"
            .Additem = "150"
            .Additem = "300"
            .Additem = "600"
            .Additem = "1200"
            .Additem = "1800"
            .Additem = "2400"
            .Additem = "4800"
            .Additem = "7200"
            .Additem = "9600"
            .Additem = "14400"
            .Additem = "19200"
            .Additem = "38400"
            .Additem = "57600"
            .Additem = "115200"
            .Additem = "128000"
            .DisplayColumns(1).Visible = False
            .DroupDownWidth = .Width - .CaptainWidth
            .SelectedIndex = 0
        End With

        With YqDataBitsCb
            .Additem = "4"
            .Additem = "5"
            .Additem = "6"
            .Additem = "7"
            .Additem = "8"
            .DisplayColumns(1).Visible = False
            .DroupDownWidth = .Width - .CaptainWidth
            .SelectedIndex = 0
        End With

        With YqCheckBitsCb
            .Additem = "N"
            .Additem = "O"
            .Additem = "E"
            .DisplayColumns(1).Visible = False
            .DroupDownWidth = .Width - .CaptainWidth
            .SelectedIndex = 0
        End With

        With YqStopBitsCb
            .Additem = "1"
            .Additem = "1.5"
            .Additem = "2"
            .DisplayColumns(1).Visible = False
            .DroupDownWidth = .Width - .CaptainWidth
            .SelectedIndex = 0
        End With

        With YqKsCb
            .DataView = BllKs.GetAllList().Tables(0).DefaultView
            .Init_Colum("Ks_Code", "科室编码", 0, "左")
            .Init_Colum("Ks_Name", "科室名称", 120, "左")
            .RowFilterNotTextNull = "Ks_Jc"
            .DisplayMember = "Ks_Name"
            .DroupDownWidth = 120
            .MaxDropDownItems = 15
            .SelectedValue = -1
        End With

        With YqLxCb
            .DataView = BllEleTwoYq.GetListForCombo(" LIS_Element1.Elementlb_Name = '设备类型'").Tables(0).DefaultView
            .Init_Colum("Element_Code", "编码", 0, "左")
            .Init_Colum("Element_Name", "名称", 120, "左")
            .RowFilterNotTextNull = "Element_Jc"
            .DisplayMember = "Element_Name"
            .DroupDownWidth = 150
            .MaxDropDownItems = 15
            .SelectedValue = -1
        End With

        With YqLbCb
            .DataView = BllEleTwoYq.GetListForCombo(" LIS_Element1.Elementlb_Name = '设备类别'").Tables(0).DefaultView
            .Init_Colum("Element_Code", "编码", 0, "左")
            .Init_Colum("Element_Name", "名称", 120, "左")
            .RowFilterNotTextNull = "Element_Jc"
            .DisplayMember = "Element_Name"
            .DroupDownWidth = 150
            .MaxDropDownItems = 15
            .SelectedValue = -1
        End With


        Button1.Top = 1
        Button2.Location = New Point(Button1.Left + Button1.Width + 2, Button1.Top)

    End Sub

#End Region

#Region "数据__操作"

    Private Sub Data_Clear()
        Move5.Enabled = False                                   '新增记录
        Rinsert = True

        My_Cc.Get_MaxCode("LIS_DictDev", "Dev_Code", 10, "", "")
        YqCodeTb.Text = My_Cc.编码
        YqJcTb.Text = ""
        YqNameTb.Text = ""
        YqLxCb.Text = ""
        YqLbCb.Text = ""
        YqModelTb.Text = ""
        YqManufacturerTb.Text = ""
        YqKsCb.Text = ""
        YqInterfaceCb.Text = ""
        YqBaudRateCb.Text = ""
        YqDataBitsCb.Text = ""
        YqStopBitsCb.Text = ""
        YqCheckBitsCb.Text = ""
        YqComPortTb.Text = ""
        YqIPTb.Text = ""
        YqPortTb.Text = ""
        YqFileTb.Text = ""
        YqFileNameTb.Text = ""
        YqMemoTb.Text = ""

        YqBaudRateCb.Enabled = True
        YqDataBitsCb.Enabled = True
        YqStopBitsCb.Enabled = True
        YqCheckBitsCb.Enabled = True
        YqComPortTb.Enabled = True
        YqIPTb.Enabled = True
        YqPortTb.Enabled = True
        YqFileBt.Enabled = True

        Call Show_Label()
        Me.YqNameTb.Select()

    End Sub

    Private Sub Data_Show(ByVal tmp_Row As DataRow)
        Move5.Enabled = True
        Rrow = tmp_Row
        With Rrow

            YqCodeTb.Text = .Item("Dev_Code") & ""
            YqJcTb.Text = .Item("Dev_Jc") & ""
            YqNameTb.Text = .Item("Dev_Name") & ""
            YqLxCb.Text = .Item("Dev_Lx") & ""
            YqLbCb.Text = .Item("Dev_Lb") & ""
            YqModelTb.Text = .Item("Dev_Model") & ""
            YqManufacturerTb.Text = .Item("Dev_Manufacturer") & ""
            YqKsCb.Text = .Item("Ks_Name") & ""
            If .Item("InterfaceType") & "" = "串口" Then
                YqInterfaceCb.SelectedIndex = 0
            ElseIf .Item("InterfaceType") & "" = "TCP/IP" Then
                YqInterfaceCb.SelectedIndex = 1
            ElseIf .Item("InterfaceType") & "" = "中间库" Then
                YqInterfaceCb.SelectedIndex = 2
            Else
                YqInterfaceCb.SelectedIndex = -1
            End If
            YqBaudRateCb.Text = .Item("BaudRate") & ""
            YqDataBitsCb.Text = .Item("DataBits") & ""
            YqStopBitsCb.Text = .Item("StopBits") & ""
            YqCheckBitsCb.Text = .Item("CheckBits") & ""
            YqComPortTb.Text = .Item("ComPort") & ""
            YqIPTb.Text = .Item("IP") & ""
            YqPortTb.Text = .Item("Port") & ""
            YqFileTb.Text = .Item("FilePath") & ""
            YqFileNameTb.Text = .Item("InterfacePrograme") & ""
            YqMemoTb.Text = .Item("Memo") & ""

        End With
        Call Show_Label()
        Me.YqNameTb.Select()
        oldInterfacePro = YqFileNameTb.Text
    End Sub

    Private Sub Show_Label()
        YqNameTb.Select()
        If Rinsert = True Then
            Label2.Text = "新增"
        Else

            Label2.Text = IIf(RZbtb.Rows.Count() = 0, "0", CStr((RZbtb.Rows.IndexOf(Rrow)) + 1))
        End If
        Label3.Text = "∑=" & RZbtb.Rows.Count
    End Sub

#End Region

#Region "控件__动作"

    Private Sub PathBt_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles YqFileBt.Click, YqFileNameBt.Click

        Select Case sender.tag
            Case "全路径"
                Dim folderBrowserDialog As New System.Windows.Forms.OpenFileDialog()
                With folderBrowserDialog
                    .InitialDirectory = Environment.SpecialFolder.Desktop
                    .Multiselect = False
                End With
                Dim result As System.Windows.Forms.DialogResult = folderBrowserDialog.ShowDialog()
                If result = System.Windows.Forms.DialogResult.OK Then
                    YqFileTb.Text = folderBrowserDialog.FileName
                End If
            Case "文件名"
                Dim folderBrowserDialogfileName As New System.Windows.Forms.OpenFileDialog()
                With folderBrowserDialogfileName
                    .InitialDirectory = Environment.SpecialFolder.Desktop
                    .Multiselect = False
                    .Filter = "exe文件|*.exe"
                End With
                Dim result As System.Windows.Forms.DialogResult = folderBrowserDialogfileName.ShowDialog()
                If result = System.Windows.Forms.DialogResult.OK Then
                    Dim GetFileNameNoExt As String
                    Dim i As Integer, J As Integer, k As Integer
                    i = Len(folderBrowserDialogfileName.FileName)
                    J = InStrRev(folderBrowserDialogfileName.FileName, "\")
                    k = InStrRev(folderBrowserDialogfileName.FileName, ".")
                    If k = 0 Then
                        GetFileNameNoExt = Mid(folderBrowserDialogfileName.FileName, J + 1, i - J)
                    Else
                        GetFileNameNoExt = Mid(folderBrowserDialogfileName.FileName, J + 1, k - J - 1)
                    End If
                    YqFileNameTb.Text = GetFileNameNoExt
                End If
        End Select
    End Sub




    Private Sub Comm_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click, Button2.Click
        Select Case sender.tag
            Case "保存"
                If YqKsCb.Text = "" Then
                    Beep()
                    MsgBox("请选择使用科室", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示")
                    YqKsCb.Select()
                Else
                    Dim Ifhave As Int32
                    Ifhave = BllYq.GetList(" InterfacePrograme='" & YqFileNameTb.Text & "'").Tables(0).Rows.Count
                    If Rinsert = True Then '增加记录

                        If Ifhave = 0 Or YqFileNameTb.Text <> "" Then
                            Call Data_Add()
                        Else
                            Beep()
                            MsgBox("该接口程序已使用,请重新选择", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示")
                            YqFileNameBt.Select()
                        End If

                    Else '编辑记录
                        If (oldInterfacePro = YqFileNameTb.Text And Ifhave = 1) Or (Ifhave = 0) Or (oldInterfacePro = "" And YqFileNameTb.Text = "") Then
                            Call Data_Edit()
                            oldInterfacePro = YqFileNameTb.Text
                        Else
                            Beep()
                            MsgBox("该接口程序已使用,请重新选择", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示")
                            YqFileNameBt.Select()
                        End If
                    End If
                End If
            Case "取消"
                Me.Close()
        End Select
    End Sub

    Private Sub Move_Click(ByVal sender As Object, ByVal e As C1.Win.C1Command.ClickEventArgs) Handles Move1.Click, Move2.Click, Move3.Click, Move4.Click, Move5.Click
        If sender.text = "新增" Then                            '增加状态
            Call Data_Clear()
        Else
            Rinsert = False
            Rrc.GridMove(sender.text)

        End If
    End Sub

    Private Sub TextBox_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs)
        If e.KeyChar <> Chr(Keys.Return) Then Exit Sub
        e.Handled = True
        System.Windows.Forms.SendKeys.Send("{Tab}")
    End Sub

    Private Sub DevNameTb_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles YqNameTb.Validated
        My_Cc.Get_Py(Me.YqNameTb.Text & "")
        YqJcTb.Text = My_Cc.简拚.ToString
    End Sub

    Private Sub Combo_Changed(ByVal sender As Object, ByVal e As System.EventArgs) Handles YqInterfaceCb.RowChange

        Select Case YqInterfaceCb.Text
            Case "串口"
                YqBaudRateCb.Enabled = True
                YqDataBitsCb.Enabled = True
                YqStopBitsCb.Enabled = True
                YqCheckBitsCb.Enabled = True
                YqComPortTb.Enabled = True
                YqIPTb.Enabled = False
                YqPortTb.Enabled = False
                YqFileBt.Enabled = False
            Case "TCP/IP"
                YqBaudRateCb.Enabled = False
                YqDataBitsCb.Enabled = False
                YqStopBitsCb.Enabled = False
                YqCheckBitsCb.Enabled = False
                YqComPortTb.Enabled = False
                YqIPTb.Enabled = True
                YqPortTb.Enabled = True
                YqFileBt.Enabled = False
            Case "中间库"
                YqBaudRateCb.Enabled = False
                YqDataBitsCb.Enabled = False
                YqStopBitsCb.Enabled = False
                YqCheckBitsCb.Enabled = False
                YqComPortTb.Enabled = False
                YqIPTb.Enabled = False
                YqPortTb.Enabled = False
                YqFileBt.Enabled = True
            Case Else
                YqBaudRateCb.Enabled = True
                YqDataBitsCb.Enabled = True
                YqStopBitsCb.Enabled = True
                YqCheckBitsCb.Enabled = True
                YqComPortTb.Enabled = True
                YqIPTb.Enabled = True
                YqPortTb.Enabled = True
                YqFileBt.Enabled = True
        End Select

    End Sub

#End Region

#Region "数据__编辑"

    Private Sub Data_Add()
        My_Cc.Get_MaxCode("LIS_DictDev", "Dev_Code", 10, "", "")
        Dim My_NewRow As DataRow = RZbtb.NewRow
        With My_NewRow

            .Item("Dev_Name") = Trim(YqNameTb.Text & "")
            .Item("Dev_Code") = My_Cc.编码.ToString
            .Item("Dev_Jc") = Trim(YqJcTb.Text & "")
            .Item("Dev_Lx") = Trim(YqLxCb.Text & "")
            .Item("Dev_Lb") = Trim(YqLbCb.Text & "")
            .Item("Dev_Model") = Trim(YqModelTb.Text & "")
            .Item("Dev_Manufacturer") = Trim(YqManufacturerTb.Text & "")
            .Item("Ks_Name") = Trim(YqKsCb.Text & "")
            .Item("Ks_Code") = BllKs.GetList("Ks_Name='" & Trim(YqKsCb.Text & "") & "'").Tables(0).Rows(0).Item("Ks_Code").ToString()
            .Item("InterfaceType") = Trim(YqInterfaceCb.Text & "")
            .Item("BaudRate") = Trim(YqBaudRateCb.Text & "")
            .Item("DataBits") = Trim(YqDataBitsCb.Text & "")
            .Item("StopBits") = Trim(YqStopBitsCb.Text & "")
            .Item("CheckBits") = Trim(YqCheckBitsCb.Text & "")
            .Item("ComPort") = Trim(YqComPortTb.Text & "")
            .Item("IP") = Trim(YqIPTb.Text & "")
            .Item("Port") = Trim(YqPortTb.Text & "")
            .Item("FilePath") = Trim(YqFileTb.Text & "")
            .Item("InterfacePrograme") = Trim(YqFileNameTb.Text & "")
            .Item("Memo") = Trim(YqMemoTb.Text & "")

        End With

        '数据保存
        Try
            RZbtb.Rows.Add(My_NewRow)
            Rrc.GridMove("最后")
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            Exit Sub
        Finally
            Me.YqNameTb.Select()
        End Try

        '数据更新
        Try
            With ModelYq

                .Dev_Code = My_NewRow("Dev_Code")
                .Dev_Name = My_NewRow("Dev_Name")
                .Dev_Jc = My_NewRow("Dev_Jc")
                .Dev_Lx = My_NewRow("Dev_Lx")
                .Dev_Lb = My_NewRow("Dev_Lb")
                .Dev_Model = My_NewRow("Dev_Model")
                .Dev_Manufacturer = My_NewRow("Dev_Manufacturer")
                .Ks_Code = My_NewRow("Ks_Code")
                .InterfaceType = My_NewRow("InterfaceType")
                .BaudRate = My_NewRow("BaudRate")
                .DataBits = My_NewRow("DataBits")
                .StopBits = My_NewRow("StopBits")
                .CheckBits = My_NewRow("CheckBits")
                .ComPort = My_NewRow("ComPort")
                .IP = My_NewRow("IP")
                .Port = My_NewRow("Port")
                .FilePath = My_NewRow("FilePath")
                .InterfacePrograme = My_NewRow("InterfacePrograme")
                .Memo = My_NewRow("Memo")

            End With
            BllYq.Add(ModelYq)



            My_NewRow.AcceptChanges()
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            Exit Sub
        Finally
            YqNameTb.Select()
        End Try

        '数据清空
        Call Data_Clear()

    End Sub

    Private Sub Data_Edit()
        Try
            With Rrow
                .BeginEdit()

                .Item("Dev_Name") = Trim(YqNameTb.Text & "")
                .Item("Dev_Code") = Trim(YqCodeTb.Text & "")
                .Item("Dev_Jc") = Trim(YqJcTb.Text & "")
                .Item("Dev_Lx") = Trim(YqLxCb.Text & "")
                .Item("Dev_Lb") = Trim(YqLbCb.Text & "")
                .Item("Dev_Model") = Trim(YqModelTb.Text & "")
                .Item("Dev_Manufacturer") = Trim(YqManufacturerTb.Text & "")
                .Item("Ks_Name") = Trim(YqKsCb.Text & "")
                .Item("Ks_Code") = BllKs.GetList("Ks_Name='" & Trim(YqKsCb.Text & "") & "'").Tables(0).Rows(0).Item("Ks_Code").ToString()
                .Item("InterfaceType") = Trim(YqInterfaceCb.Text & "")
                .Item("BaudRate") = Trim(YqBaudRateCb.Text & "")
                .Item("DataBits") = Trim(YqDataBitsCb.Text & "")
                .Item("StopBits") = Trim(YqStopBitsCb.Text & "")
                .Item("CheckBits") = Trim(YqCheckBitsCb.Text & "")
                .Item("ComPort") = Trim(YqComPortTb.Text & "")
                .Item("IP") = Trim(YqIPTb.Text & "")
                .Item("Port") = Trim(YqPortTb.Text & "")
                .Item("FilePath") = Trim(YqFileTb.Text & "")
                .Item("InterfacePrograme") = Trim(YqFileNameTb.Text & "")
                .Item("Memo") = Trim(YqMemoTb.Text & "")

                .EndEdit()
            End With
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Critical, "提示:")
            Rrow.CancelEdit()
            Exit Sub
        Finally
            YqNameTb.Select()
        End Try

        '数据更新
        Try
            With ModelYq

                .Dev_Code = Rrow("Dev_Code")
                .Dev_Name = Rrow("Dev_Name")
                .Dev_Jc = Rrow("Dev_Jc")
                .Dev_Lx = Rrow("Dev_Lx")
                .Dev_Lb = Rrow("Dev_Lb")
                .Dev_Model = Rrow("Dev_Model")
                .Dev_Manufacturer = Rrow("Dev_Manufacturer")
                .Ks_Code = Rrow("Ks_Code")
                .InterfaceType = Rrow("InterfaceType")
                .BaudRate = Rrow("BaudRate")
                .DataBits = Rrow("DataBits")
                .StopBits = Rrow("StopBits")
                .CheckBits = Rrow("CheckBits")
                .ComPort = Rrow("ComPort")
                .IP = Rrow("IP")
                .Port = Rrow("Port")
                .FilePath = Rrow("FilePath")
                .InterfacePrograme = Rrow("InterfacePrograme")
                .Memo = Rrow("Memo")
            End With
            BllYq.Update(ModelYq)
            Rrow.AcceptChanges()
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
        Finally
            YqNameTb.Select()
        End Try
        ' End With

    End Sub

#End Region

#Region "输入法设置"
    '中文
    Private Sub C1TextBox1_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles YqNameTb.GotFocus, YqNameTb.GotFocus, YqManufacturerTb.GotFocus, YqMemoTb.GotFocus
        InputLanguage.CurrentInputLanguage = HisVar.HisVar.InputCode
    End Sub

    '英文
    Private Sub C1TextBox2_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles YqModelTb.GotFocus, YqModelTb.GotFocus, YqLxCb.GotFocus, YqLbCb.GotFocus, YqInterfaceCb.GotFocus, YqComPortTb.GotFocus, YqPortTb.GotFocus, YqCheckBitsCb.GotFocus, YqDataBitsCb.GotFocus, YqStopBitsCb.GotFocus, YqIPTb.GotFocus
        InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages(0)
    End Sub
#End Region

End Class