﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="0" />
    <DataSources isList="true" count="1">
      <分组1 Ref="2" type="DataTableSource" isKey="true">
        <Alias>分组1</Alias>
        <Columns isList="true" count="18">
          <value>Czd_Id,System.Int64</value>
          <value>Cf_Code,System.String</value>
          <value>Z_Name,System.String</value>
          <value>Xx_Code,System.String</value>
          <value>Use_Num,System.Decimal</value>
          <value>Czd_Memo,System.String</value>
          <value>Yp_Name,System.String</value>
          <value>Mx_Gg,System.String</value>
          <value>Mx_Cd,System.String</value>
          <value>Yp_Yxq,System.DateTime</value>
          <value>Ys_Name,System.String</value>
          <value>Ry_Name,System.String</value>
          <value>Bc_Name,System.String</value>
          <value>Bl_Code,System.String</value>
          <value>Ry_Blcode,System.String</value>
          <value>Mx_Dw,System.String</value>
          <value>bl_code,System.String</value>
          <value>Mz_Code,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>分组1</Name>
        <NameInSource>分组1</NameInSource>
      </分组1>
    </DataSources>
    <Relations isList="true" count="0" />
    <Report isRef="0" />
    <Variables isList="true" count="1">
      <value>,打印时间,打印时间,System.String,,False,False</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="3" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="5">
        <GroupHeaderBand1 Ref="4" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,20,2.7</ClientRectangle>
          <Components isList="true" count="20">
            <Text1 Ref="5" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.9,0,13.1,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>黑体,15,Regular,Point,False,134</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text1</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>输 液 卡</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text1>
            <Text2 Ref="6" type="Text" isKey="true">
              <Border>AdvBlack;2;Solid;Black;1;Solid;Black;2;Solid;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.9,0.7,1.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text2</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>人员编号</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text2>
            <Text3 Ref="7" type="Text" isKey="true">
              <Border>AdvBlack;2;Solid;Black;1;Solid;Black;1;Solid;Black;2;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13.2,0.7,2.8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <Guid>d6bbf7e41ef64523bac0b4324caeb0c0</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text3</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text3>
            <Text4 Ref="8" type="Text" isKey="true">
              <Border>AdvBlack;1;Solid;Black;1;Solid;Black;2;Solid;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.9,1.2,1.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <Guid>5555b9669ab64e769a5a627237938935</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text4</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>姓    名</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text4>
            <Text10 Ref="9" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.4,1.2,3.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <Guid>ff602ed3f9c54fd6ade7743eb3020ba8</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text10</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>{分组1.Ry_Name}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text10>
            <Text6 Ref="10" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>8,1.2,0.8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <Guid>e256012137604f7593f9daf9e7ebd4b0</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text6</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>床号</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text6>
            <Text11 Ref="11" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>8.8,1.2,2.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <Guid>9f3b6b6557fe42c283c905216d68563c</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text11</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>{分组1.Bc_Name}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text11>
            <Text5 Ref="12" type="Text" isKey="true">
              <Border>AdvBlack;2;Solid;Black;1;Solid;Black;1;Solid;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>11.7,0.7,1.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <Guid>bbca4ed4396e4ac48e00b4126865cb4f</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text5</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>开始时间</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text5>
            <Text12 Ref="13" type="Text" isKey="true">
              <Border>AdvBlack;1;Solid;Black;1;Solid;Black;1;Solid;Black;2;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13.2,1.2,2.8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <Guid>b226d89b944c44fe8b987fd0b873f4b3</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text12</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>{分组1.Ys_Name}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text12>
            <Text16 Ref="14" type="Text" isKey="true">
              <Border>AdvBlack;1;Solid;Black;1;None;Black;2;Solid;Black;2;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.9,2.2,13.1,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <Guid>c24cc8ec1b5a4292abe08a42b8d829cf</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text16</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>分   类：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text16>
            <Text7 Ref="15" type="Text" isKey="true">
              <Border>AdvBlack;1;Solid;Black;1;Solid;Black;2;Solid;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.9,1.7,1.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <Guid>fd93a36264374c598e68b922da084171</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text7</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>液体总量</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text7>
            <Text8 Ref="16" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.4,1.7,3.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <Guid>eb10dacae7c54331ab6c81742bc8a55b</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text8</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text8>
            <Text9 Ref="17" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>8,1.7,0.8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <Guid>b70e67760c2e41a2a316d53403549daf</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text9</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>皮试</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text9>
            <Text13 Ref="18" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>8.8,1.7,2.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <Guid>628bde0ce6a84d569552f2b34a42438d</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text13</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text13>
            <Text14 Ref="19" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>11.7,1.7,1.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <Guid>604e51e5c608413690146c5236b0b09e</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text14</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>滴速/分</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text14>
            <Text15 Ref="20" type="Text" isKey="true">
              <Border>AdvBlack;1;Solid;Black;1;Solid;Black;1;Solid;Black;2;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13.2,1.7,2.8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <Guid>905a9fe6ae0b449da969f75a4b90745b</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text15</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text15>
            <Text18 Ref="21" type="Text" isKey="true">
              <Border>AdvBlack;2;Solid;Black;1;Solid;Black;1;Solid;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>8.8,0.7,2.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <Guid>af8907ef627048a9861ac76981e184a7</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text18</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>{打印时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text18>
            <Text19 Ref="22" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>11.7,1.2,1.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <Guid>5357181a25094d9491db915e5aab352d</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text19</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>处方医生</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text19>
            <Text20 Ref="23" type="Text" isKey="true">
              <Border>AdvBlack;2;Solid;Black;1;Solid;Black;1;Solid;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.4,0.7,3.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <Guid>e27f57ad64ae444ea1c3b5f542478000</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text20</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>{分组1.Bl_Code}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text20>
            <Text24 Ref="24" type="Text" isKey="true">
              <Border>AdvBlack;2;Solid;Black;1;Solid;Black;1;Solid;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>8,0.7,0.8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <Guid>46db0c44fac74c64ad34ae1db7a21afe</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text24</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>日期</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text24>
          </Components>
          <Condition>{分组1.Cf_Code}{分组1.Ry_Name}{分组1.Z_Name}</Condition>
          <Conditions isList="true" count="0" />
          <Name>GroupHeaderBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </GroupHeaderBand1>
        <GroupHeaderBand2 Ref="25" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,3.9,20,0.5</ClientRectangle>
          <Components isList="true" count="1">
            <Text26 Ref="26" type="Text" isKey="true">
              <Border>AdvBlack;1;None;Black;1;None;Black;2;Solid;Black;2;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.9,0,13.1,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <Guid>b1601dbc7a6248ea9c9d4ebddcdd6ea1</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text26</Name>
              <Page isRef="3" />
              <Parent isRef="25" />
              <Text>{分组1.Z_Name}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text26>
          </Components>
          <Condition>{分组1.Z_Name}</Condition>
          <Conditions isList="true" count="0" />
          <Name>GroupHeaderBand2</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </GroupHeaderBand2>
        <DataBand1 Ref="27" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,5.2,20,0.5</ClientRectangle>
          <Components isList="true" count="5">
            <Text21 Ref="28" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>3.6,0,5.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>68524c51f9024b62891638c0faa25706</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text21</Name>
              <Page isRef="3" />
              <Parent isRef="27" />
              <Text>{分组1.Yp_Name}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text21>
            <Text22 Ref="29" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>9.3,0,4.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>ed4b9e3cea02441f8bd8b382d7dc5810</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text22</Name>
              <Page isRef="3" />
              <Parent isRef="27" />
              <Text>{分组1.Mx_Gg}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="30" type="CustomFormat" isKey="true">
                <StringFormat>yyyy-MM-dd</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text22>
            <Text23 Ref="31" type="Text" isKey="true">
              <Border>AdvBlack;1;None;Black;1;None;Black;1;None;Black;2;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15,0,1,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>792221464d3741c5839aa0ecdb28729e</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text23</Name>
              <NullValue>             </NullValue>
              <Page isRef="3" />
              <Parent isRef="27" />
              <Text>{分组1.Mx_Dw}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="32" type="CustomFormat" isKey="true">
                <StringFormat>0.#####</StringFormat>
              </TextFormat>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text23>
            <Text17 Ref="33" type="Text" isKey="true">
              <Border>AdvBlack;1;None;Black;1;None;Black;2;Solid;Black;1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.9,0,0.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>ab38909a1ed94d69a1f3b331f0dc8906</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text17</Name>
              <Page isRef="3" />
              <Parent isRef="27" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text17>
            <Text25 Ref="34" type="Text" isKey="true">
              <Border>AdvBlack;1;None;Black;1;None;Black;1;None;Black;2;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13.9,0,1.1,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>d2ac3d58ec2c4f44930f2ebc505e7e24</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text25</Name>
              <NullValue>             </NullValue>
              <Page isRef="3" />
              <Parent isRef="27" />
              <Text>{分组1.Use_Num}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="35" type="CustomFormat" isKey="true">
                <StringFormat>0.#####</StringFormat>
              </TextFormat>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text25>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>分组1</DataSourceName>
          <Filters isList="true" count="0" />
          <Name>DataBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Sort isList="true" count="2">
            <value>DESC</value>
            <value>Yp_Name</value>
          </Sort>
        </DataBand1>
        <GroupFooterBand2 Ref="36" type="GroupFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,6.5,20,0</ClientRectangle>
          <Components isList="true" count="0" />
          <Conditions isList="true" count="0" />
          <Name>GroupFooterBand2</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </GroupFooterBand2>
        <GroupFooterBand1 Ref="37" type="GroupFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,7.3,20,1.2</ClientRectangle>
          <Components isList="true" count="4">
            <Text29 Ref="38" type="Text" isKey="true">
              <Border>AdvBlack;1;Solid;Black;2;Solid;Black;2;Solid;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.9,0,1.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <Guid>6ad632e9aab74155a8e7268343483a7f</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text29</Name>
              <Page isRef="3" />
              <Parent isRef="37" />
              <Text>执 行 者</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text29>
            <Text30 Ref="39" type="Text" isKey="true">
              <Border>AdvBlack;1;Solid;Black;2;Solid;Black;1;Solid;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.4,0,7.3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <Guid>84f5a401a4744092aaa81a5de172f808</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text30</Name>
              <Page isRef="3" />
              <Parent isRef="37" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text30>
            <Text32 Ref="40" type="Text" isKey="true">
              <Border>AdvBlack;1;Solid;Black;2;Solid;Black;1;Solid;Black;2;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13.2,0,2.8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <Guid>c88e62c601334d5dbfaf105a52b60145</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text32</Name>
              <Page isRef="3" />
              <Parent isRef="37" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text32>
            <Text31 Ref="41" type="Text" isKey="true">
              <Border>AdvBlack;1;Solid;Black;2;Solid;Black;1;Solid;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>11.7,0,1.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <Guid>eef4346f87c44095b74367b905b4d0c9</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text31</Name>
              <Page isRef="3" />
              <Parent isRef="37" />
              <Text>结束时间</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text31>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>GroupFooterBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </GroupFooterBand1>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>77c73957ba6d40769b606b8c85438f05</Guid>
      <Margins>0.5,0.5,0.5,0.5</Margins>
      <Name>Page1</Name>
      <PageHeight>27.9</PageHeight>
      <PageWidth>21</PageWidth>
      <Report isRef="0" />
      <Watermark Ref="42" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page1>
  </Pages>
  <PrinterSettings Ref="43" type="Stimulsoft.Report.Print.StiPrinterSettings" isKey="true" />
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>输液卡</ReportAlias>
  <ReportChanged>2/23/2016 9:15:59 AM</ReportChanged>
  <ReportCreated>8/1/2012 11:17:25 AM</ReportCreated>
  <ReportFile>C:\Users\<USER>\Desktop\his2010\his2010\Rpt\输液卡（连打）.mrt</ReportFile>
  <ReportGuid>d75670e8cdbd4c148cfa37e0b55fc548</ReportGuid>
  <ReportName>输液卡</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2011.2.1026</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class Report : Stimulsoft.Report.StiReport
    {
        public Report()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
        #endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>