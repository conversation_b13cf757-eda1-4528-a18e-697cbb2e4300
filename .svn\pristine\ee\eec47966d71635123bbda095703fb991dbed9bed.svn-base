﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="0" />
    <DataSources isList="true" count="2">
      <从表2 Ref="2" type="DataTableSource" isKey="true">
        <Alias>从表</Alias>
        <Columns isList="true" count="13">
          <value>Xx_Code,System.String</value>
          <value>Yp_Name,System.String</value>
          <value>Mx_Gg,System.String</value>
          <value>Mx_Cd,System.String</value>
          <value>Mx_Gyzz,System.String</value>
          <value>Mx_XsDw,System.String</value>
          <value>Cf_Sl,System.Decimal</value>
          <value>Cf_Dj,System.Decimal</value>
          <value>Cf_Money,System.Decimal</value>
          <value>Cf_Lb,System.String</value>
          <value>Yp_Ph,System.String</value>
          <value>Yp_Yxq,System.DateTime</value>
          <value>Yf_Sl1,System.Decimal</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>从表2</Name>
        <NameInSource>从表</NameInSource>
      </从表2>
      <从表 Ref="3" type="DataTableSource" isKey="true">
        <Alias>从表</Alias>
        <Columns isList="true" count="9">
          <value>Yp_Name,System.String</value>
          <value>Mx_Gg,System.String</value>
          <value>Mx_Cd,System.String</value>
          <value>Yp_Ph,System.String</value>
          <value>Yp_Yxq,System.String</value>
          <value>Mz_Sl,System.String</value>
          <value>Mx_XsDw,System.String</value>
          <value>Mz_Dj,System.String</value>
          <value>Mz_Money,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>从表</Name>
        <NameInSource>dt</NameInSource>
      </从表>
    </DataSources>
    <Relations isList="true" count="0" />
    <Report isRef="0" />
    <Variables isList="true" count="5">
      <value>,打印时间,打印时间,System.DateTime,,False,False</value>
      <value>,经手人,经手人,System.String,,False,False</value>
      <value>,汇总发药方式,汇总发药方式,System.String,,False,False</value>
      <value>,分类,分类,System.String,,False,False</value>
      <value>,属性,属性,System.String,,False,False</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="4" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="5">
        <PageFooterBand1 Ref="5" type="PageFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,27.2,19.5,0.5</ClientRectangle>
          <Components isList="true" count="1">
            <Text36 Ref="6" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,19,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,0</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text36</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <Text>第{PageNumber}页</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text36>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>PageFooterBand1</Name>
          <Page isRef="4" />
          <Parent isRef="4" />
        </PageFooterBand1>
        <ReportTitleBand1 Ref="7" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,19.5,1.2</ClientRectangle>
          <Components isList="true" count="5">
            <Text1 Ref="8" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,19,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>黑体,15,Regular,Point,False,134</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text1</Name>
              <Page isRef="4" />
              <Parent isRef="7" />
              <Text>{汇总发药方式}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text1>
            <Text2 Ref="9" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>13.5,0.7,5.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text2</Name>
              <Page isRef="4" />
              <Parent isRef="7" />
              <Text>打印时间：{打印时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text2>
            <Text37 Ref="10" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.7,5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text37</Name>
              <Page isRef="4" />
              <Parent isRef="7" />
              <Text>打印经手人：{经手人}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text37>
            <Text22 Ref="11" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>5.2,0.7,2.2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,0</Font>
              <Guid>b10eeecc50a442ab99ecc1527851896a</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text22</Name>
              <Page isRef="4" />
              <Parent isRef="7" />
              <Text>{分类}：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text22>
            <Text24 Ref="12" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7.4,0.7,5.4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <Guid>cc2d9dfde0ff4ef880f41f4cf72b41f4</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text24</Name>
              <Page isRef="4" />
              <Parent isRef="7" />
              <Text>{属性}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text24>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>ReportTitleBand1</Name>
          <Page isRef="4" />
          <Parent isRef="4" />
          <Restrictions>AllowMove, AllowResize, AllowChange, AllowDelete</Restrictions>
        </ReportTitleBand1>
        <HeaderBand1 Ref="13" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,2.4,19.5,0.5</ClientRectangle>
          <Components isList="true" count="9">
            <Text4 Ref="14" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,5.3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text4</Name>
              <Page isRef="4" />
              <Parent isRef="13" />
              <Text>药品名称</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text4>
            <Text6 Ref="15" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>5.3,0,2.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text6</Name>
              <Page isRef="4" />
              <Parent isRef="13" />
              <Text>规格</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text6>
            <Text8 Ref="16" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>10.3,0,2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text8</Name>
              <Page isRef="4" />
              <Parent isRef="13" />
              <Text>生产批号</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text8>
            <Text10 Ref="17" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>12.3,0,2.2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text10</Name>
              <Page isRef="4" />
              <Parent isRef="13" />
              <Text>有效期</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text10>
            <Text12 Ref="18" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>14.5,0,1.4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text12</Name>
              <Page isRef="4" />
              <Parent isRef="13" />
              <Text>数量</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text12>
            <Text14 Ref="19" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15.9,0,0.8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text14</Name>
              <Page isRef="4" />
              <Parent isRef="13" />
              <Text>单位</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text14>
            <Text16 Ref="20" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>16.7,0,1.3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text16</Name>
              <Page isRef="4" />
              <Parent isRef="13" />
              <Text>单价</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text16>
            <Text18 Ref="21" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>18,0,1.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text18</Name>
              <Page isRef="4" />
              <Parent isRef="13" />
              <Text>金额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text18>
            <Text20 Ref="22" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>8,0,2.3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text20</Name>
              <Page isRef="4" />
              <Parent isRef="13" />
              <Text>产地</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text20>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>HeaderBand1</Name>
          <Page isRef="4" />
          <Parent isRef="4" />
          <PrintIfEmpty>True</PrintIfEmpty>
        </HeaderBand1>
        <DataBand1 Ref="23" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,3.7,19.5,0.5</ClientRectangle>
          <Components isList="true" count="9">
            <Text3 Ref="24" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,5.3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>0,0,0,0</Margins>
              <Name>Text3</Name>
              <Page isRef="4" />
              <Parent isRef="23" />
              <Text>{从表.Yp_Name}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text3>
            <Text7 Ref="25" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>10.3,0,2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>a19430421fe14654b43babc72c2ef9ee</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text7</Name>
              <Page isRef="4" />
              <Parent isRef="23" />
              <Text>{从表.Yp_Ph}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text7>
            <Text5 Ref="26" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>5.3,0,2.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>1c4a85b9a1db4e7f962c31030a4cba33</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text5</Name>
              <Page isRef="4" />
              <Parent isRef="23" />
              <Text>{从表.Mx_Gg}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text5>
            <Text9 Ref="27" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>12.3,0,2.2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>fa7505b6cbb448e4a28c2923e20b0628</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text9</Name>
              <Page isRef="4" />
              <Parent isRef="23" />
              <Text>{从表.Yp_Yxq}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="28" type="CustomFormat" isKey="true">
                <StringFormat>yyyy-MM-dd</StringFormat>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text9>
            <Text11 Ref="29" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>14.5,0,1.4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>be8371f4c27e453ca957dc36c2896f71</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text11</Name>
              <Page isRef="4" />
              <Parent isRef="23" />
              <Text>{从表.Mz_Sl}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="30" type="CustomFormat" isKey="true">
                <StringFormat>0.####</StringFormat>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text11>
            <Text13 Ref="31" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>15.9,0,0.8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text13</Name>
              <Page isRef="4" />
              <Parent isRef="23" />
              <Text>{从表.Mx_XsDw}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text13>
            <Text15 Ref="32" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>16.7,0,1.3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>717c3126fe794f698172817f7c56e1e4</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text15</Name>
              <Page isRef="4" />
              <Parent isRef="23" />
              <Text>{从表.Mz_Dj}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="33" type="CustomFormat" isKey="true">
                <StringFormat>0.00##</StringFormat>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text15>
            <Text17 Ref="34" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>18,0,1.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>f7b27023cf0d4aaea9aea4c5c9e9a11e</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text17</Name>
              <Page isRef="4" />
              <Parent isRef="23" />
              <Text>{从表.Mz_Money}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="35" type="CustomFormat" isKey="true">
                <StringFormat>0.00##</StringFormat>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text17>
            <Text19 Ref="36" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>8,0,2.3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>cde115f4b0da43b6b5716e61b0b9720f</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text19</Name>
              <Page isRef="4" />
              <Parent isRef="23" />
              <Text>{从表.Mx_Cd}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="37" type="CustomFormat" isKey="true">
                <StringFormat>0.####</StringFormat>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text19>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>从表</DataSourceName>
          <Filters isList="true" count="0" />
          <Name>DataBand1</Name>
          <Page isRef="4" />
          <Parent isRef="4" />
          <Sort isList="true" count="0" />
        </DataBand1>
        <ReportSummaryBand1 Ref="38" type="ReportSummaryBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,5,19.5,0.6</ClientRectangle>
          <Components isList="true" count="2">
            <Text21 Ref="39" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,5.3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text21</Name>
              <Page isRef="4" />
              <Parent isRef="38" />
              <Text>合    计：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text21>
            <Text23 Ref="40" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>5.3,0,14.2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>797aadff87ac421ca6a4e5ae045eadfe</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text23</Name>
              <Page isRef="4" />
              <Parent isRef="38" />
              <Text>{Sum(DataBand1,从表.Mz_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text23>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>ReportSummaryBand1</Name>
          <Page isRef="4" />
          <Parent isRef="4" />
        </ReportSummaryBand1>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>3caecf95e2f248dfb5c77693af3b3f48</Guid>
      <Margins>1,0.5,1,1</Margins>
      <Name>Page1</Name>
      <PageHeight>29.7</PageHeight>
      <PageWidth>21</PageWidth>
      <Report isRef="0" />
      <Watermark Ref="41" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page1>
  </Pages>
  <PrinterSettings Ref="42" type="Stimulsoft.Report.Print.StiPrinterSettings" isKey="true" />
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>住院发药汇总表</ReportAlias>
  <ReportChanged>11/14/2016 3:52:14 PM</ReportChanged>
  <ReportCreated>12/24/2011 2:56:39 PM</ReportCreated>
  <ReportFile>D:\项目\HospitalInformationSystem\his2010v3(修改主窗体，增加电子病历)\his2010\Rpt\药房门诊发药汇总.mrt</ReportFile>
  <ReportGuid>cc1ee990b68f412caa05f4d6f631bab0</ReportGuid>
  <ReportName>住院发药汇总表</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2011.2.1026</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class Report : Stimulsoft.Report.StiReport
    {
        public Report()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
        #endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>