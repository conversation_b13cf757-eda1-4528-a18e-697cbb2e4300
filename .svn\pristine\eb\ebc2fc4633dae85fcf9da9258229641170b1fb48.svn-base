﻿Public Class ZyRj_Print
    Dim V_Print As Boolean
    Private Sub StiViewerControl1_ClickPrintButton(ByVal sender As Object, ByVal e As System.EventArgs) Handles StiViewerControl1.ClickPrintButton
        V_Print = True
        Me.StiViewerControl1.Report.Print()
    End Sub

    Private Sub ZyRj_Print_FormClosing(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosingEventArgs) Handles Me.FormClosing
        If V_Print = False Then
            MsgBox("日结单没有打印，本次日结数据被取消。", vbExclamation + vbOKOnly + vbDefaultButton1, "提示:")

            HisVar.HisVar.Sqldal.ExecuteSql("Update Bl_Jf Set Jz_Code=Null Where Isnull(Jz_Code,'')='" & Xs_Zy_Jz.V_Jz_Code & "'")
            HisVar.HisVar.Sqldal.ExecuteSql("Update Bl    Set Jz_Code=Null Where Isnull(Jz_Code,'')='" & Xs_Zy_Jz.V_Jz_Code & "'")
            HisVar.HisVar.Sqldal.ExecuteSql("Update Bl_Cf       Set Jz_Code=Null Where Isnull(Jz_Code,'')='" & Xs_Zy_Jz.V_Jz_Code & "'")
            HisVar.HisVar.Sqldal.ExecuteSql("Update Bl_Zh    Set Jz_Code=Null Where Isnull(Jz_Code,'')='" & Xs_Zy_Jz.V_Jz_Code & "'")
            HisVar.HisVar.Sqldal.ExecuteSql("Delete From Bl_Jz Where Isnull(Jz_Code,'')='" & Xs_Zy_Jz.V_Jz_Code & "'")
            Xs_Zy_Jz.C1Button1.Enabled = True
            Exit Sub
        Else
            Xs_Zy_Jz.C1Button1.Enabled = False
        End If
    End Sub
   

    Private Sub MzRj_Print_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        V_Print = False
    End Sub
End Class