﻿/**  版本信息模板在安装目录下，可自行修改。
* M_Jkk_Cz.cs
*
* 功 能： N/A
* 类 名： M_Jkk_Cz
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/7/2 10:28:15   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace ModelOld
{
	/// <summary>
	/// M_Jkk_Cz:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class M_Jkk_Cz
	{
		public M_Jkk_Cz()
		{}
		#region Model
		private int _id;
		private string _ry_name;
		private string _ry_sfzh;
		private string _jsr_code;
		private string _jkk_code;
		private int? _czid;
		private decimal? _cz_money;
		private DateTime? _cz_date;
		private string _lb;
		private string _jkklb;
		private string _jz_code;
		private string _czbankno;
		private string _jz_cwcode;
		private string _cz_lb;
		private string _mzzy_code;
		/// <summary>
		/// 
		/// </summary>
		public int Id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Ry_Name
		{
			set{ _ry_name=value;}
			get{return _ry_name;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Ry_sfzh
		{
			set{ _ry_sfzh=value;}
			get{return _ry_sfzh;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Jsr_Code
		{
			set{ _jsr_code=value;}
			get{return _jsr_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Jkk_Code
		{
			set{ _jkk_code=value;}
			get{return _jkk_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public int? CzId
		{
			set{ _czid=value;}
			get{return _czid;}
		}
		/// <summary>
		/// 
		/// </summary>
		public decimal? Cz_Money
		{
			set{ _cz_money=value;}
			get{return _cz_money;}
		}
		/// <summary>
		/// 
		/// </summary>
		public DateTime? Cz_Date
		{
			set{ _cz_date=value;}
			get{return _cz_date;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Lb
		{
			set{ _lb=value;}
			get{return _lb;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string JkkLb
		{
			set{ _jkklb=value;}
			get{return _jkklb;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Jz_Code
		{
			set{ _jz_code=value;}
			get{return _jz_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string CzBankNo
		{
			set{ _czbankno=value;}
			get{return _czbankno;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Jz_CwCode
		{
			set{ _jz_cwcode=value;}
			get{return _jz_cwcode;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Cz_Lb
		{
			set{ _cz_lb=value;}
			get{return _cz_lb;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string MzZy_Code
		{
			set{ _mzzy_code=value;}
			get{return _mzzy_code;}
		}
		#endregion Model

	}
}

