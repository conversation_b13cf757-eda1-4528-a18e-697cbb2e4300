﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
namespace ModelOld
{
    public class M_PatientInfo
    {
        /// <summary>
        /// 初始化对象
        /// </summary>
        public M_PatientInfo()
        {
        }

        public enum PatientSexType
        {
            /// <summary>
            /// 女性
            /// </summary>

            Female = 0,
            /// <summary>
            /// 男性
            /// </summary>

            Male = 1,
            /// <summary>
            /// 未知
            /// </summary>

            Unknow = 2
        }

        private Image _FaceImage = null;
        /// <summary>
        /// 图标图片
        /// </summary>
        public Image FaceImage
        {
            get { return _FaceImage; }
            set { _FaceImage = value; }
        }

        private Image _ShenqingCy = null;
        /// <summary>
        /// 图标图片
        /// </summary>
        public Image ShenqingCy
        {
            get { return _ShenqingCy; }
            set { _ShenqingCy = value; }
        }

        private string _Cy_Qr;
        /// <summary>
        /// 图标图片
        /// </summary>
        public string Cy_Qr
        {
            get { return _Cy_Qr; }
            set { _Cy_Qr = value; }
        }

        private string _Bl_Code = null;
        /// <summary>
        /// 病历号
        /// </summary>

        public string Bl_Code
        {
            get { return _Bl_Code; }
            set { _Bl_Code = value; }
        }

        private string _Bl_id = null;
        /// <summary>
        /// 病历id
        /// </summary>

        public string Bl_id
        {
            get { return _Bl_id; }
            set { _Bl_id = value; }
        }

        private string _Mb_code = null;
        /// <summary>
        /// 病历id
        /// </summary>

        public string Mb_code
        {
            get { return _Mb_code; }
            set { _Mb_code = value; }
        }


        private string _Mb_name = null;
        /// <summary>
        /// 病历id
        /// </summary>

        public string Mb_name
        {
            get { return _Mb_name; }
            set { _Mb_name = value; }
        }

        private string _Name = null;
        /// <summary>
        /// 姓名
        /// </summary>

        public string Name
        {
            get { return _Name; }
            set { _Name = value; }
        }



        private bool _insertflage = false;
        /// <summary>
        /// 插入或修改标志
        /// </summary>
        public bool insertflage
        {
            get { return _insertflage; }
            set { _insertflage = value; }
        }

        private string _Diagnose = null;
        /// <summary>
        /// 诊断
        /// </summary>

        public string Diagnose
        {
            get { return _Diagnose; }
            set { _Diagnose = value; }
        }

        private string _Area = null;
        /// <summary>
        /// 病区
        /// </summary>

        public string Area
        {
            get { return _Area; }
            set { _Area = value; }
        }


        private string _BedID = null;
        public string BedID
        {
            get { return _BedID; }
            set { _BedID = value; }
        }


        private string _BedName = null;
        public string BedName
        {
            get { return _BedName; }
            set { _BedName = value; }
        }

        private PatientSexType _Sex = PatientSexType.Unknow;
        /// <summary>
        /// 性别
        /// </summary>
        public PatientSexType Sex
        {
            get { return _Sex; }
            set { _Sex = value; }
        }


        private float _Age = 0;
        /// <summary>
        /// 年龄
        /// </summary>

        public float Age
        {
            get { return _Age; }
            set { _Age = value; }
        }



        private decimal _TotalCost = 0;
        /// <summary>
        /// 总金额
        /// </summary>

        public decimal TotalCost
        {
            get { return _TotalCost; }
            set { _TotalCost = value; }
        }

        private decimal _SpendCost = 0;
        /// <summary>
        /// 花费的金额
        /// </summary>
        public decimal SpendCost
        {
            get { return _SpendCost; }
            set { _SpendCost = value; }
        }

        private decimal _Balance = 0;
        /// <summary>
        /// 余额
        /// </summary>

        public decimal Balance
        {
            get { return _Balance; }
            set { _Balance = value; }
        }


        private string _Ry_RyDate;
        /// <summary>
        /// 入院日期
        /// </summary>

        public string Ry_RyDate
        {
            get { return _Ry_RyDate; }
            set { _Ry_RyDate = value; }
        }

        private string _Ks_Name = null;
        /// <summary>
        /// 科室
        /// </summary>

        public string Ks_Name
        {
            get { return _Ks_Name; }
            set { _Ks_Name = value; }
        }

        private string _Ys_Name = null;
        /// <summary>
        /// 主治医师
        /// </summary>

        public string Ys_Name
        {
            get { return _Ys_Name; }
            set { _Ys_Name = value; }
        }

        private string _Ys_code = null;
        /// <summary>
        /// 主治医师编码
        /// </summary>

        public string Ys_code
        {
            get { return _Ys_code; }
            set { _Ys_code = value; }
        }

        private string _Ks_code = null;
        /// <summary>
        /// 主治医师编码
        /// </summary>

        public string Ks_code
        {
            get { return _Ks_code; }
            set { _Ks_code = value; }
        }

        private string _Bxlb_Name = null;
        /// <summary>
        /// 病人类别
        /// </summary>

        public string Bxlb_Name
        {
            get { return _Bxlb_Name; }
            set { _Bxlb_Name = value; }
        }

        private string _Ry_Address = null;
        /// <summary>
        /// 家庭地址
        /// </summary>

        public string Ry_Address
        {
            get { return _Ry_Address; }
            set { _Ry_Address = value; }
        }

        private string _HospitalDay = null;
        /// <summary>
        /// 在院天数
        /// </summary>

        public string HospitalDay
        {
            get { return _HospitalDay; }
            set { _HospitalDay = value; }
        }


        private bool _DepositInsufficient = true;
        /// <summary>
        /// 在院天数
        /// </summary>

        public bool DepositInsufficient
        {
            get { return _DepositInsufficient; }
            set { _DepositInsufficient = value; }
        }

        private bool _Emr_GuiDang = true;
        /// <summary>
        /// 在院天数
        /// </summary>

        public bool Emr_GuiDang
        {
            get { return _Emr_GuiDang; }
            set { _Emr_GuiDang = value; }
        }

    }
}
