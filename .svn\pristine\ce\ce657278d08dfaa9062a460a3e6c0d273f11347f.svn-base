﻿Imports System.Data.SqlClient
Imports Stimulsoft.Report
Imports HisControl

Public Class Cx_ZyYjMx

#Region "变量初始化"
    Dim My_View As New DataView                 '数据视图
    Dim My_Date As String
    Public My_Dataset As New DataSet
    Public My_Adapter As New SqlDataAdapter
    Public My_Table As New DataTable            '药品字典
    Public My_Cm As CurrencyManager             '同步指针
    Public My_Row As DataRow                    '当 前 行
    Public V_Insert As Boolean                  '增加记录
    Public V_FirstLoad As Boolean               '首次调入明细表
    Public My_Datetime As New SqlDataAdapter             '交费时间

#End Region

#Region "传参"
    'Dim Rform As BaseForm
    'Dim Rinsert As Boolean
    'Dim Rdate As Date
    Dim Rrow As DataRow
    'Dim RZbtb As DataTable
    'Dim Rtdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    'Dim Rlb As C1.Win.C1Input.C1Label
    'Dim Rzbadt As SqlDataAdapter
    'Dim Rlx As String
#End Region

    Public Sub New(ByVal tform As BaseForm, ByVal trow As DataRow)

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        'Rform = tform
        Rrow = trow

    End Sub

    Private Sub Cx_ZyYjMx_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        'Rform.Visible = False
        Call Form_Init()
        Call Init_Data()
        Call F_Sum()
    End Sub

#Region "窗体初始化"

    Private Sub Form_Init()
        '初始化TDBGrid
        Dim My_Grid As New BaseClass.C_Grid(Me.C1TrueDBGrid1)
        With My_Grid
            .Init_Grid()
            .P_Dock(BaseClass.C_Grid.Dock.Fill)
            .Init_Column("缴费编码", "Jf_Code", 0, "中", "")
            .Init_Column("病人姓名", "Ry_Name", 70, "左", "")
            .Init_Column("科室", "Ks_Name", 80, "左", "")
            .Init_Column("医生", "Ys_Name", 70, "左", "")
            .Init_Column("交费金额", "Jf_Money", 70, "右", "0.00")
            .Init_Column("交费日期", "Jf_Date", 180, "中", "yyyy-MM-dd HH:mm:ss")
            .Init_Column("经手人", "Jsr_Name", 70, "左", "")
        End With
        C1TrueDBGrid1.Splits(0).HScrollBar.Style = C1.Win.C1TrueDBGrid.ScrollBarStyleEnum.Automatic
    End Sub

    Private Sub Init_Data()
        Call Show_Data()
    End Sub

    Private Sub Xs_Zy_Yj1_FormClosing(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosingEventArgs) Handles Me.FormClosing
        My_Dataset.Dispose()
        'Rform.Visible = True
    End Sub

#End Region

    Private Sub Show_Data()
        '主表数据
        Dim Str_Select As String = "SELECT Bl_Jf.Yy_Code,Bl_Jf.Jf_Code,Bl.Bl_Code,Jf_Money,Jf_Memo,Jf_Date,Bl_Jf.Jsr_Code,Bl.Ry_Name,Jsr_Name,Bl.Ks_Code,Bl.Ys_Code,Ks_Name,Ys_Name FROM Zd_YyJsr,Bl,Bl_Jf,Zd_YyKs,Zd_YyYs where Zd_YyJsr.Jsr_Code=Bl_Jf.Jsr_Code And Bl.Bl_Code = Bl_Jf.Bl_Code And Bl.Ks_Code=Zd_YyKs.Ks_Code And Bl.Ys_Code=Zd_YyYs.Ys_Code  And Bl_Jf.Yy_Code='" & HisVar.HisVar.WsyCode & "'  and Bl_Jf.bl_code='" & Rrow("Bl_Code") & "'order by Bl_Jf.Jf_Code"

        If My_Dataset.Tables("病人押金") IsNot Nothing Then My_Dataset.Tables("病人押金").Clear()
        My_Adapter.SelectCommand = New SqlCommand(Str_Select, My_Cn)
        My_Adapter.Fill(My_Dataset, "病人押金")
        My_Table = My_Dataset.Tables("病人押金")
        My_Table.PrimaryKey = New DataColumn() {My_Table.Columns("Jf_Code")}

        'TDBGrid初始化
        C1TrueDBGrid1.SetDataBinding(My_Dataset, "病人押金", True)
        My_Cm = CType(BindingContext(C1TrueDBGrid1.DataSource, C1TrueDBGrid1.DataMember), CurrencyManager)
    End Sub

#Region "自定义函数"


    Private Sub C1DateEdit2_KeyPress(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyPressEventArgs)
        If e.KeyChar = Chr(Keys.Enter) Then
            e.Handled = True
            C1TrueDBGrid1.Select()
        End If
    End Sub

    Public Overrides Sub F_Sum()
        If My_Dataset.Tables("病人押金").Rows.Count = 0 Then Exit Sub
        Dim Sum1 As Double = 0
        Sum1 = Format(My_Dataset.Tables("病人押金").Compute("Sum(Jf_Money)", ""), "###0.00")
        With C1TrueDBGrid1
            .ColumnFooters = True

            .Columns(4).FooterText = Format(Sum1, "###0.00")
        End With
    End Sub
#End Region


End Class
