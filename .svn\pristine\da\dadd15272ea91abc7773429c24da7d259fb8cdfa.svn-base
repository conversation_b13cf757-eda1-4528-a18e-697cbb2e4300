﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="0" />
    <DataSources isList="true" count="1">
      <view交易明细 Ref="2" type="Stimulsoft.Report.Dictionary.StiDataViewSource" isKey="true">
        <Alias>view交易明细</Alias>
        <Columns isList="true" count="11">
          <value>V_Lb,System.Int32</value>
          <value>Cz_Lb,System.String</value>
          <value>Ry_Name,System.String</value>
          <value>Ry_Sfzh,System.String</value>
          <value>Jkk_Code,System.String</value>
          <value>Cz_Date,System.DateTime</value>
          <value>Lb,System.String</value>
          <value>JkkLb,System.String</value>
          <value>Cz_Money,System.Decimal</value>
          <value>CzId,System.Int32</value>
          <value>Jsr_Name,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>view交易明细</Name>
        <NameInSource>view交易明细</NameInSource>
      </view交易明细>
    </DataSources>
    <Relations isList="true" count="0" />
    <Report isRef="0" />
    <Variables isList="true" count="3">
      <value>,标题,标题,System.String,,False,False</value>
      <value>,打印时间,打印时间,System.String,,False,False</value>
      <value>,查询时间,查询时间,System.String,,False,False</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="3" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="7">
        <ReportTitleBand1 Ref="4" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,19.5,2</ClientRectangle>
          <Components isList="true" count="10">
            <Text1 Ref="5" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,19.5,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,14.25,Bold,Point,False,0</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text1</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>{标题}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text1>
            <Text3 Ref="6" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.4,2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,10.5,Regular,Point,False,0</Font>
              <Guid>e6f4042e463c4e999be2fca8fe9f5bce</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text3</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>持卡人</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text3>
            <Text4 Ref="7" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2,1.4,3.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,10.5,Regular,Point,False,0</Font>
              <Guid>3c49027c3d304d1d92b8749345387049</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text4</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>身份证号</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text4>
            <Text5 Ref="8" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>5.8,1.4,4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,10.5,Regular,Point,False,0</Font>
              <Guid>945a8550c97342ce868b6b850a48aff9</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text5</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>健康卡号</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text5>
            <Text6 Ref="9" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9.8,1.4,3.5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,10.5,Regular,Point,False,0</Font>
              <Guid>9ed3a85ae88a4826ba5b37fc575a295e</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text6</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>交易时间</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text6>
            <Text7 Ref="10" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13.3,1.4,3.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,10.5,Regular,Point,False,0</Font>
              <Guid>e9c0818123f544f7b289ff3f9dfc94b0</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text7</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>交易方式</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text7>
            <Text8 Ref="11" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>16.5,1.4,1.3,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,10.5,Regular,Point,False,0</Font>
              <Guid>ee091e0fc2d34aa8bd30f70459a0ec72</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text8</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>卡类别</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text8>
            <Text9 Ref="12" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>17.8,1.4,1.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,10.5,Regular,Point,False,0</Font>
              <Guid>8737078ed9cf4f69b06cbbd7ba25c94f</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text9</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>交易金额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text9>
            <Text21 Ref="13" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>13.3,0.8,6.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,10.5,Regular,Point,False,0</Font>
              <Guid>7192402dfbbd4f419fb09eb3204891d5</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text21</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>{打印时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text21>
            <Text22 Ref="14" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.8,13.3,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,10.5,Regular,Point,False,0</Font>
              <Guid>dd91bfb36e9a40c5ba9c6c75235da77a</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text22</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>{查询时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text22>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>ReportTitleBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </ReportTitleBand1>
        <GroupHeaderBand1 Ref="15" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,3.2,19.5,0.6</ClientRectangle>
          <Components isList="true" count="1">
            <Text2 Ref="16" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,19.5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,10.5,Bold,Point,False,0</Font>
              <Guid>abe95391e6ff40b0a2b54214877db0c4</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text2</Name>
              <Page isRef="3" />
              <Parent isRef="15" />
              <Text>经手人：{view交易明细.Jsr_Name}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text2>
          </Components>
          <Condition>{view交易明细.Jsr_Name}</Condition>
          <Conditions isList="true" count="0" />
          <Name>GroupHeaderBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </GroupHeaderBand1>
        <GroupHeaderBand2 Ref="17" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,4.6,19.5,0.6</ClientRectangle>
          <Components isList="true" count="1">
            <Text23 Ref="18" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,19.5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,10.5,Bold,Point,False,0</Font>
              <Guid>abe46d484a064325b49bcd800ee38946</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text23</Name>
              <Page isRef="3" />
              <Parent isRef="17" />
              <Text>交易类别：{view交易明细.Cz_Lb}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text23>
          </Components>
          <Condition>{view交易明细.Cz_Lb}</Condition>
          <Conditions isList="true" count="0" />
          <Name>GroupHeaderBand2</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </GroupHeaderBand2>
        <DataBand1 Ref="19" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,6,19.5,0.6</ClientRectangle>
          <Components isList="true" count="7">
            <Text10 Ref="20" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,10.5,Regular,Point,False,0</Font>
              <Guid>c6b080206f6d4354873a069c9371928a</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text10</Name>
              <Page isRef="3" />
              <Parent isRef="19" />
              <Text>{view交易明细.Ry_Name}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text10>
            <Text11 Ref="21" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2,0,3.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,10.5,Regular,Point,False,0</Font>
              <Guid>9b10b3ad3a0e4a2eb2c17e7e6ee11050</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text11</Name>
              <Page isRef="3" />
              <Parent isRef="19" />
              <Text>{view交易明细.Ry_Sfzh}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text11>
            <Text12 Ref="22" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>5.8,0,4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,10.5,Regular,Point,False,0</Font>
              <Guid>cba4ca16efd944aea4fd85a8c74dc320</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text12</Name>
              <Page isRef="3" />
              <Parent isRef="19" />
              <Text>{view交易明细.Jkk_Code}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text12>
            <Text13 Ref="23" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9.8,0,3.5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,10.5,Regular,Point,False,0</Font>
              <Guid>59493ec550ad480b911f02798782cb30</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text13</Name>
              <Page isRef="3" />
              <Parent isRef="19" />
              <Text>{view交易明细.Cz_Date}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="24" type="CustomFormat" isKey="true">
                <StringFormat>yyyy-MM-dd HH:mm:ss</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text13>
            <Text14 Ref="25" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>16.5,0,1.3,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,10.5,Regular,Point,False,0</Font>
              <Guid>51c75f9942a646ee9c74ca0db7ff340a</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text14</Name>
              <Page isRef="3" />
              <Parent isRef="19" />
              <Text>{view交易明细.JkkLb}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text14>
            <Text15 Ref="26" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>17.8,0,1.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,10.5,Regular,Point,False,0</Font>
              <Guid>2b9526ec42ab41d0b834a60e0c32d242</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text15</Name>
              <Page isRef="3" />
              <Parent isRef="19" />
              <Text>{view交易明细.Cz_Money}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text15>
            <Text16 Ref="27" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13.3,0,3.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,10.5,Regular,Point,False,0</Font>
              <Guid>53881b38509643ebb4d09c89cf05a2fc</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text16</Name>
              <Page isRef="3" />
              <Parent isRef="19" />
              <Text>{view交易明细.Lb}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text16>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>view交易明细</DataSourceName>
          <Filters isList="true" count="0" />
          <Name>DataBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Sort isList="true" count="0" />
        </DataBand1>
        <GroupFooterBand2 Ref="28" type="GroupFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,7.4,19.5,0.6</ClientRectangle>
          <Components isList="true" count="2">
            <Text17 Ref="29" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,10.5,Regular,Point,False,0</Font>
              <Guid>fbaf68105c7141448cca3add1db3407a</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text17</Name>
              <Page isRef="3" />
              <Parent isRef="28" />
              <Text>小计</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text17>
            <Text18 Ref="30" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2,0,17.5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,10.5,Regular,Point,False,0</Font>
              <Guid>8dbbfa8629264e3e92e435ee35847017</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text18</Name>
              <Page isRef="3" />
              <Parent isRef="28" />
              <Text>{Sum(GroupHeaderBand2,view交易明细.Cz_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text18>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>GroupFooterBand2</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </GroupFooterBand2>
        <GroupFooterBand1 Ref="31" type="GroupFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,8.8,19.5,0.6</ClientRectangle>
          <Components isList="true" count="2">
            <Text19 Ref="32" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,10.5,Regular,Point,False,0</Font>
              <Guid>2acf7f6498a14afba0cd235fd5df2514</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text19</Name>
              <Page isRef="3" />
              <Parent isRef="31" />
              <Text>合计</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text19>
            <Text20 Ref="33" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2,0,17.5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,10.5,Regular,Point,False,0</Font>
              <Guid>e4153b7ed70c437a9e0a47b431210bd9</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text20</Name>
              <Page isRef="3" />
              <Parent isRef="31" />
              <Text>{Sum(GroupHeaderBand1,view交易明细.Cz_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text20>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>GroupFooterBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </GroupFooterBand1>
        <ReportSummaryBand1 Ref="34" type="ReportSummaryBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,10.2,19.5,0.6</ClientRectangle>
          <Components isList="true" count="2">
            <Text24 Ref="35" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,10.5,Regular,Point,False,0</Font>
              <Guid>c3f5fd45ce0e454b8c18d1af1a4afbc2</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text24</Name>
              <Page isRef="3" />
              <Parent isRef="34" />
              <Text>总计</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text24>
            <Text25 Ref="36" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2,0,17.5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,10.5,Regular,Point,False,0</Font>
              <Guid>66b0706e25814c5e916d00b2e518d295</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text25</Name>
              <Page isRef="3" />
              <Parent isRef="34" />
              <Text>{Sum(DataBand1,view交易明细.Cz_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text25>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>ReportSummaryBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </ReportSummaryBand1>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>2b5fe4dca3ad49f59793036f63d4ad23</Guid>
      <Margins>1,0.5,1,1</Margins>
      <Name>Page1</Name>
      <PageHeight>29.7</PageHeight>
      <PageWidth>21</PageWidth>
      <Report isRef="0" />
      <Watermark Ref="37" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page1>
  </Pages>
  <PrinterSettings Ref="38" type="Stimulsoft.Report.Print.StiPrinterSettings" isKey="true" />
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>健康卡交易明细表</ReportAlias>
  <ReportChanged>9/9/2015 11:11:17 AM</ReportChanged>
  <ReportCreated>9/9/2015 9:04:55 AM</ReportCreated>
  <ReportFile>E:\正在进行时\唐山\正在修改版\his2010\his2010\Rpt\健康卡交易明细表.mrt</ReportFile>
  <ReportGuid>9ea2163a82874447b91907d6b295bd29</ReportGuid>
  <ReportName>健康卡交易明细表</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2011.2.1026</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class Report : Stimulsoft.Report.StiReport
    {
        public Report()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
        #endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>