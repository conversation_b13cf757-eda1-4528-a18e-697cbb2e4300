﻿Imports System.Drawing
Public Class C_Combo1

    Private My_Combo As C1.Win.C1List.C1Combo

    Public Sub New(ByVal TDBCombo As C1.Win.C1List.C1Combo) ' 定义一个公用构造函数来初始化C1Combo
        My_Combo = TDBCombo
    End Sub


    Public Sub Init_TDBCombo()
        With My_Combo
            .ClearItems()
            .AllowSort = True
            .AutoDropDown = True
            .AutoCompletion = True
            .AutoSelect = True
            .AutoSize = False
            .ColumnHeaders = False
            .SuperBack = True

            '.Width = 75
            .DropDownWidth = .Width
            .ItemHeight = 16

            .MaxDropDownItems = 10
            .DataMode = C1.Win.C1List.DataModeEnum.AddItem
            .ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
            .Style.VerticalAlignment = C1.Win.C1List.AlignVertEnum.Bottom

            .AddItemCols = 1
            .AddItemTitles("列名")

            .Splits(0).ExtendRightColumn = True
            .Splits(0).ColumnCaptionHeight = 20
            .Splits(0).DisplayColumns(0).Width = 10

            '.RowDivider.Color = Color.FromArgb(0, 84, 227)


            With .HighLightRowStyle
                .ForeColor = Color.FromArgb(255, 255, 255)
                .BackColor = Color.FromArgb(205, 92, 92)
                .VerticalAlignment = C1.Win.C1List.AlignVertEnum.Bottom
            End With

            With .SelectedStyle
                .ForeColor = Color.FromArgb(255, 255, 255)
                .BackColor = Color.FromArgb(205, 92, 92)
                .VerticalAlignment = C1.Win.C1List.AlignVertEnum.Bottom
            End With

            .RowDivider.Style = C1.Win.C1List.LineStyleEnum.Single
            .DropdownPosition = C1.Win.C1List.DropdownPositionEnum.LeftDown

            .DisplayMember = "列名"
            .ValueMember = "列名"

            .MatchCol = C1.Win.C1List.MatchColEnum.DisplayMember
            .MatchEntry = C1.Win.C1List.MatchEntryEnum.Standard
        End With




    End Sub

    '重写方法
    Public Overridable Sub AddItem(ByVal V_Item As String)
        My_Combo.AddItem(V_Item)
    End Sub

    '重写方法
    Public Overridable Sub SelectedIndex(ByVal V_SelectIndex As Integer)
        Me.My_Combo.SelectedIndex = V_SelectIndex
    End Sub

    '重写方法
    Public Overridable Sub MaxDropDownItems(ByVal V_Max As Integer)
        Me.My_Combo.MaxDropDownItems = V_Max
    End Sub


End Class
