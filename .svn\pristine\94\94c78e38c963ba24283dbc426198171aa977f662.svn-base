﻿Imports System.Data.SqlClient
Imports Common.Delegate
Imports HisControl
Imports Model
Imports SQLServerDAL

Public Class Zy_Cf2

#Region "定义__变量"
    Dim My_Adapter As New SqlDataAdapter                '从表适配器
    Dim My_Table As New DataTable                       '从表
    Dim My_Reader1 As SqlClient.SqlDataReader

    Public Cb_Cm As CurrencyManager                     '同步指针
    Public Cb_Row As DataRow                            '当前选择行
    Public V_Cf_Code As String                          '出库编码
    Public V_Insert As Boolean                          '增加记录


    Dim Str_Select As String
    Public Yp_Lb As String
    Dim My_Dataset As New DataSet

    Public V_Yf_Sl As String
    Dim bllblcfyp As New BLL.BllBl_Cfyp
    Dim bllblcfxm As New BLL.BllBl_Cfxm
    Dim bllblcf As New BLL.BllBl_Cf
    Private _transmitTxt As Common.Delegate.TransmitTxt = New TransmitTxt()
#End Region

#Region "传参"
    Dim Rform As BaseForm
    Dim Rinsert As Boolean
    Dim Rdate As Date
    Dim Rrow As DataRow
    Dim RZbtb As DataTable
    Dim Rtdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Dim Rlb As C1.Win.C1Input.C1Label
    Dim Rzbadt As SqlDataAdapter
    Dim Rlx As String
#End Region

    Public Sub New(ByVal tform As BaseForm, ByVal tinsert As Boolean, ByVal tdate As Date, ByVal trow As DataRow, ByVal tZbtb As DataTable, ByVal ttdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid, ByVal tlb As C1.Win.C1Input.C1Label, ByVal tzbadt As SqlDataAdapter, ByVal tdataset As DataSet, ByVal tlx As String)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        Rform = tform
        Rinsert = tinsert
        Rdate = tdate
        Rrow = trow
        RZbtb = tZbtb
        Rtdbgrid = ttdbgrid
        Rlb = tlb
        Rzbadt = tzbadt
        My_Dataset = tdataset
        Rlx = tlx
        ' 在 InitializeComponent() 调用之后添加任何初始化。

    End Sub

    Protected Overrides Sub OnKeyDown(ByVal e As System.Windows.Forms.KeyEventArgs)
        MyBase.OnKeyDown(e)
        If e.KeyCode = Keys.F1 Then
            RadioButton4.Checked = True
        ElseIf e.KeyCode = Keys.F2 Then
            RadioButton1.Checked = True
        ElseIf e.KeyCode = Keys.F3 Then
            Comm3.Select()
            Call Comm_Click(Comm3, Nothing)
        ElseIf e.KeyCode = Keys.F4 Then
            comm4.Select()
            Call Comm_Click(comm4, Nothing)
        ElseIf e.Control = True And e.KeyCode = Keys.S Then
            Comm1.Select()
            Call Comm_Click(Comm1, Nothing)
        End If
    End Sub

    Private Sub Zy_Cf_Yp_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        'Rform.Visible = False
        RadioButton1.Checked = True
        Call Init_Data()
        Call Form_Init() '窗体初始化

        ComboBox1.Items.Add("不需要汇总领药")
        ComboBox1.Items.Add("需要汇总领药")
        ComboBox1.DropDownStyle = ComboBoxStyle.DropDownList
        If HisPara.PublicConfig.ZyHsz = "是" Then
            If HisPara.PublicConfig.ZyYsz = "是" Then
                ComboBox1.Enabled = False
                ComboBox1.BackColor = SystemColors.Info
                ComboBox1.SelectedIndex = 1
            Else
                ComboBox1.SelectedIndex = IIf(iniOperate.iniopreate.GetINI("汇总领药", "医嘱是否汇总", "", HisVar.HisVar.Parapath & "\Config.ini") & "" = "", 1, iniOperate.iniopreate.GetINI("汇总领药", "医嘱是否汇总", "", HisVar.HisVar.Parapath & "\Config.ini"))
            End If

        Else
            ComboBox1.Enabled = False
            ComboBox1.BackColor = SystemColors.Info
            ComboBox1.SelectedIndex = 0
        End If


        AddHandler Me._transmitTxt.SetText, AddressOf GridMove
        If Rinsert = True Then           '主表
            Call Zb_Clear()                 '清空数据
        Else
            Call Zb_Show()                  '显示数据
        End If

    End Sub

#Region "窗体__事件"

    Private Sub Form_Init()
        Panel2.Height = 30

        '按扭初始化
        Call P_Comm(Me.Comm1)
        Call P_Comm(Me.Comm2)
        Call P_Comm(Me.Comm3)
        Call P_Comm(Me.comm4)
        T_Line2.Location = New Point(comm4.Left + comm4.Width + 4, 5)
        ComboBox1.Location = New Point(T_Line2.Left + 4, 5)
        T_Label4.Location = New Point(ComboBox1.Left + ComboBox1.Width + 4, 5)
        T_Label5.Location = New Point(T_Label4.Left + T_Label4.Width, 5)

        C1NumericEdit1.Enabled = False
        C1Command1.Enabled = False
        C1Command2.Enabled = False


        With KsCombo
            .DataView = DAL.DAL_Dict.GetDepartmentDict.DefaultView
            .Init_Colum("Ks_Jc", "简称", 84, "左")
            .Init_Colum("Ks_Name", "名称", 100, "左")
            .Init_Colum("Ks_Code", "编码", 0, "左")
            .DisplayMember = "Ks_Name"
            .ValueMember = "Ks_Code"
            .DroupDownWidth = 400
            .MaxDropDownItems = 15
            .SelectedIndex = -1
            .RowFilterNotTextNull = "Ks_Jc"
            .RowFilterTextNull = ""
        End With

        With DoctorCombo
            .DataView = DAL.DAL_Dict.GetDoctorDict.DefaultView
            .Init_Colum("Ys_Jc", "简称", 84, "左")
            .Init_Colum("Ys_Name", "姓名", 100, "左")
            .Init_Colum("ks_Name", "科室", 100, "左")
            .Init_Colum("Ys_Code", "编码", 0, "左")
            .Init_Colum("Ks_Code", "科室编码", 0, "左")
            .DisplayMember = "Ys_Name"
            .ValueMember = "Ys_Code"
            .DroupDownWidth = 400
            .MaxDropDownItems = 15
            .SelectedIndex = -1
            .RowFilterNotTextNull = "Ys_Jc"
            .RowFilterTextNull = ""
        End With






        Dim v_select As String
        If HisPara.PublicConfig.ZyHsz = "否" Then
            v_select = "SELECT Bl_Code,Ry_Jc,Ry_Name,Ry_Sex,Jb_Name,Bq_Name,Bc_Name,Ys_Code,Ks_Code,Ry_BlCode,Bxlb_Name FROM Bl,V_YyBc,Zd_Bxlb Where Bl.Bxlb_Code=Zd_Bxlb.Bxlb_Code and Bl.Bc_Code=V_YyBc.Bc_Code  And Isnull(Ry_CyDate,'1900-01-01')='1900-01-01' And Bl.Yy_Code='" & HisVar.HisVar.WsyCode & "' Order By Ry_Jc"
        Else
            v_select = "SELECT Bl_Code,Ry_Jc,Ry_Name,Ry_Sex,Jb_Name,Bq_Name,Bc_Name,Ys_Code,Ks_Code,Ry_BlCode,Bxlb_Name FROM Bl,V_YyBc,Zd_Bxlb Where Bl.Bxlb_Code=Zd_Bxlb.Bxlb_Code and Bl.Bc_Code=V_YyBc.Bc_Code  And Isnull(Ry_CyDate,'1900-01-01')='1900-01-01' And Bl.Yy_Code='" & HisVar.HisVar.WsyCode & "' And Cy_Qr<>'是' Order By Ry_Jc"
        End If
        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, v_select, "患者字典", True)

        If HisPara.PublicConfig.ZyCfKsXz = "是" Then
            My_Dataset.Tables("患者字典").DefaultView.RowFilter = "Ks_Code like '%" & HisVar.HisVar.XmKs & "%'"
        Else
            My_Dataset.Tables("患者字典").DefaultView.RowFilter = ""
        End If


        Dim My_Combo3 As New BaseClass.C_Combo2(Me.C1Combo3, My_Dataset.Tables("患者字典").DefaultView, "Ry_Name", "Bl_Code", 600)
        With My_Combo3
            .Init_TDBCombo()
            .Init_Colum("Ry_Jc", "患者简称", 60, "左")
            .Init_Colum("Bl_Code", "患者编码", 0, "左")
            .Init_Colum("Ry_Name", "患者姓名", 65, "左")
            .Init_Colum("Ry_Sex", "性别", 35, "中")
            .Init_Colum("Jb_Name", "疾病名称", 0, "左")
            .Init_Colum("Bq_Name", "病区", 85, "左")
            .Init_Colum("Bc_Name", "床位", 95, "左")
            .Init_Colum("Ks_Code", "", 0, "左")
            .Init_Colum("Ys_Code", "", 0, "左")
            .Init_Colum("Ry_BlCode", "病例编码", 100, "左")
            .Init_Colum("Bxlb_Name", "患者类别", 80, "左")
            .MaxDropDownItems(17)
            .SelectedIndex(-1)
        End With
        With C1Combo3
            .AutoCompletion = False
            .AutoSelect = False
        End With

        With YfComobo1
            .DataView = DAL.DAL_Dict.GetYfDict.DefaultView
            .Init_Colum("Yf_Jc", "药房简称", 100, "左")
            .Init_Colum("Yf_Code", "药房编码", 0, "左")
            .Init_Colum("Yf_Name", "药房名称", 100, "左")
            .DisplayMember = "Yf_Name"
            .ValueMember = "Yf_Code"
            .DroupDownWidth = 400
            .MaxDropDownItems = 15
            .SelectedIndex = -1
            .RowFilterNotTextNull = "Yf_Jc"
            .RowFilterTextNull = ""
        End With
        YfComobo1.SelectedValue = iniOperate.iniopreate.GetINI("Personal", "默认药房", "", HisVar.HisVar.Parapath & "\Config.Ini") & ""

        If HisPara.PublicConfig.ZyYsz = "是" And HisVar.HisVar.JsrYsCode & "" <> "" Then
            DoctorCombo.SelectedValue = HisVar.HisVar.JsrYsCode
            DoctorCombo.Enabled = False
        End If

    End Sub

    Private Sub Zy_Cf2_FormClosing(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosingEventArgs) Handles Me.FormClosing
        If e.CloseReason = CloseReason.UserClosing Then
            If Rinsert = False Then
                If HisVar.HisVar.Sqldal.GetSingle("Select Count(*) from Bl_Cf where (Cf_Print='是' or Cf_Qr='是') and  Cf_Code='" & Rrow.Item("Cf_Code") & "'") = 0 Then
                    Call Zb_Save() '更新主表(主表已经保存过,取消操作)
                Else
                    Call Rform.F_Sum()
                End If
            End If
        End If
        'Rform.Visible = True
    End Sub

#End Region

#Region "清空__显示"

    Private Sub Zb_Clear()
        V_Cf_Code = F_MaxCode(Format(Rdate, "yyMMdd"))       '出库编码
        '客户编码

        C1Combo3.Text = ""

        Label10.Text = ""
        Label17.Text = ""
        Label24.Text = ""
        Label20.Text = ""


        C1NumericEdit1.Value = 1

        '备注
        Label12.Text = V_Cf_Code                                                '客户编码

        C1Combo3.SelectedIndex = -1

        If RadioButton1.Checked = True Then
            Yp_Lb = Mid(RadioButton1.Text, 4)
            C1TrueDBGrid_Init_Zxy()
            C1Command1.Enabled = True
            C1Command2.Enabled = True
            C1NumericEdit1.Enabled = True
        End If
        If RadioButton4.Checked = True Then
            Yp_Lb = Mid(RadioButton1.Text, 4)
            C1TrueDBGrid_Init_Xm()
            C1Command1.Enabled = False
            C1Command2.Enabled = False
            C1NumericEdit1.Enabled = False
        End If
        Call P_Data_Show()

        C1Combo3.Select()
    End Sub

    Private Sub Zb_Show()   '显示记录

        With Rrow
            V_Cf_Code = .Item("Cf_Code") & ""                                   '出库编码
            Label12.Text = V_Cf_Code
            C1Combo3.SelectedValue = .Item("Bl_Code") & ""
            DoctorCombo.SelectedValue = .Item("Ys_Code") & ""
            KsCombo.SelectedValue = .Item("Ks_Code") & ""
            YfComobo1.SelectedValue = .Item("Yf_Code") & ""
        End With

        C1NumericEdit1.Value = 1

        If RadioButton1.Checked = True Then
            Yp_Lb = Mid(RadioButton1.Text, 4)
            C1TrueDBGrid_Init_Zxy()
            C1Command1.Enabled = True
            C1Command2.Enabled = True
            C1NumericEdit1.Enabled = True
        End If

        If RadioButton4.Checked = True Then
            Yp_Lb = Mid(RadioButton4.Text, 4)
            C1TrueDBGrid_Init_Xm()
            C1Command1.Enabled = False
            C1Command2.Enabled = False
            C1NumericEdit1.Enabled = False
        End If

        '出库编码

        Call P_Data_Show()
        C1TrueDBGrid1.Select()
        C1TrueDBGrid1.MoveLast()
    End Sub

    Private Sub P_Data_Show()   '从表数据
        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "Select Bl_Cfxm.*,Xm_Dw,Xm_Name From Bl_Cfxm,Zd_Ml_Xm3 Where  Bl_Cfxm.Xm_Code=Zd_Ml_Xm3.Xm_Code And Cf_Code='" & V_Cf_Code & "' Order By Cf_Id", "诊疗项目", True)
        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "Select Bl_Cfyp.*,Mx_Gg,Mx_Cd,Yp_Name,Mx_XsDw,V_YpKc.Yp_Ph,V_YpKc.Yp_Yxq From Bl_Cfyp,V_YpKc Where Bl_Cfyp.Xx_Code=V_YpKc.Xx_Code And Cf_Code='" & V_Cf_Code & "' Order By Cf_Id", "药品卫材", True)
        If Yp_Lb = "诊疗项目" Then
            My_Table = My_Dataset.Tables("诊疗项目")
        ElseIf Yp_Lb = "药品卫材" Then
            My_Table = My_Dataset.Tables("药品卫材")
        End If

        My_Table.PrimaryKey = New DataColumn() {My_Table.Columns("Cf_Id")}

        '列的唯一性
        Dim My_Column As DataColumn = My_Table.Columns("Cf_Id")
        With My_Column
            .AutoIncrement = True
            .AutoIncrementSeed = .AutoIncrementSeed
            .AutoIncrementStep = 1
        End With

        '主表记录
        Cb_Cm = CType(BindingContext(My_Dataset, My_Table.TableName), CurrencyManager)
        C1TrueDBGrid1.SetDataBinding(My_Dataset, My_Table.TableName, True)

        Call P_SumMoney()

    End Sub

#End Region

#Region "主表__编辑"

    Private Sub Zb_Save()                       '主表保存
        If C1Combo3.SelectedValue = "" Then
            Beep()
            MsgBox("患者不能为空!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
            C1Combo3.Select()
            Exit Sub
        End If

        If KsCombo.SelectedValue = "" Then
            Beep()
            MsgBox("医生名称不能为空!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
            KsCombo.Select()
            Exit Sub
        End If
        If KsCombo.SelectedValue = "" Then
            Beep()
            MsgBox("科室名称不能为空!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
            KsCombo.Select()
            Exit Sub
        End If

        If YfComobo1.SelectedValue = "" Then
            Beep()
            MsgBox("药房名称不能为空!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
            YfComobo1.Select()
            Exit Sub
        End If

        If Rinsert = True Then     '增加记录
            Call Zb_Add()
        Else                                '编辑记录
            Call Zb_Edit()
        End If
        Rform.F_Sum()
    End Sub

    Private Sub Zb_Add()    '增加记录
        If RadioButton1.Checked = True Then
            Yp_Lb = Mid(RadioButton1.Text, 4)
        End If
        Dim My_Tb As DataTable = RZbtb
        Dim My_NewRow As DataRow = My_Tb.NewRow
        With My_NewRow

            .Item("Yy_Code") = HisVar.HisVar.WsyCode
            .Item("Cf_Code") = F_MaxCode(Format(Rdate, "yyMMdd"))        '出库编码
            V_Cf_Code = .Item("Cf_Code")
            Label12.Text = V_Cf_Code
            .Item("Yf_Code") = YfComobo1.SelectedValue
            .Item("Ks_Code") = KsCombo.SelectedValue
            .Item("Ys_Code") = DoctorCombo.SelectedValue
            .Item("Cf_Date") = Format(Rdate, "yyyy-MM-dd")               '入库日期
            .Item("Cf_Time") = Format(Now, "HH:mm:ss")
            .Item("Jsr_Code") = HisVar.HisVar.JsrCode
            '.Item("Jsr_Name") = HisVar.HisVar.JsrName
            .Item("Bl_Code") = C1Combo3.SelectedValue


            .Item("Cf_YpMoney") = Format(bllblcfyp.GetCfYpMoney(V_Cf_Code), "###,###,##0.00##")
            .Item("Cf_Money") = Format(bllblcfyp.GetCfYpMoney(V_Cf_Code) + bllblcfxm.GetCfXmMoney(V_Cf_Code), "###,###,##0.00##")



            .Item("Ry_Name") = C1Combo3.Text
            .Item("Ks_Name") = KsCombo.Text
            .Item("Ys_Name") = DoctorCombo.Text

            .Item("Ry_Sex") = Label10.Text
            .Item("Bc_Name") = Label17.Text
            .Item("Bxlb_Name") = C1Combo3.Columns("Bxlb_Name").Value
            .Item("Yf_Name") = YfComobo1.Text

            If Rinsert = True Then
                .Item("Cf_Print") = "否"
                .Item("Cf_Qr") = "否"
            End If

        End With
        Try
            My_Tb.Rows.Add(My_NewRow)
            Rrow = My_NewRow
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            C1Combo3.Select()
            Exit Sub
        End Try

        '显示增加后的状态
        Rtdbgrid.MoveLast()
        Rlb.Text = "∑=" + Rtdbgrid.RowCount.ToString

        '更新主表
        Call Zb_Update("增加", My_NewRow)

    End Sub

    Private Sub Zb_Edit()   '编辑记录
        If RadioButton1.Checked = True Then
            Yp_Lb = Mid(RadioButton1.Text, 4)
        End If
        Try
            With Rrow
                .BeginEdit()
                .Item("Yf_Code") = YfComobo1.SelectedValue
                .Item("Ks_Code") = KsCombo.SelectedValue
                .Item("Ys_Code") = DoctorCombo.SelectedValue
                .Item("Cf_Date") = Format(Rdate, "yyyy-MM-dd")               '入库日期
                If Rrow.Item("AutoCf_Code") & "" = "" Then
                    .Item("Cf_Time") = Format(Now, "HH:mm:ss")
                End If

                .Item("Jsr_Code") = HisVar.HisVar.JsrCode
                '.Item("Jsr_Name") = HisVar.HisVar.JsrName
                .Item("Bl_Code") = C1Combo3.SelectedValue

                .Item("Cf_YpMoney") = Format(bllblcfyp.GetCfYpMoney(Rrow("Cf_Code")), "###,###,##0.00##")
                .Item("Cf_Money") = Format(bllblcfyp.GetCfYpMoney(Rrow("Cf_Code")) + bllblcfxm.GetCfXmMoney(Rrow("Cf_Code")), "###,###,##0.00##")

                .Item("Ry_Name") = C1Combo3.Text
                .Item("Ks_Name") = KsCombo.Text
                .Item("Ys_Name") = DoctorCombo.Text

                .Item("Ry_Sex") = Label10.Text
                .Item("Bc_Name") = Label17.Text
                .Item("Bxlb_Name") = C1Combo3.Columns("Bxlb_Name").Value
                .Item("Yf_Name") = YfComobo1.Text

                .EndEdit()
            End With

        Catch ex As Exception
            Beep()
            Rrow.CancelEdit()
            MsgBox(ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
            Exit Sub
        End Try

        '更新主表
        Call Zb_Update("保存", Rrow)

    End Sub

    Private Sub Zb_Update(ByVal V_Lb As String, ByVal V_Row As DataRow)     '更新主表
        Call P_Conn(True)

        If V_Lb = "增加" Then                                               '增加主表
            With Rzbadt.InsertCommand
                .Parameters(0).Value = V_Row.Item("Yy_Code")
                .Parameters(1).Value = V_Row.Item("Cf_Code")
                .Parameters(2).Value = V_Row.Item("Yf_Code")
                .Parameters(3).Value = V_Row.Item("Bl_Code")
                .Parameters(4).Value = V_Row.Item("Ks_Code")
                .Parameters(5).Value = V_Row.Item("Ys_Code")
                .Parameters(6).Value = V_Row.Item("Cf_Date")
                .Parameters(7).Value = V_Row.Item("Cf_Time")
                .Parameters(8).Value = V_Row.Item("Jsr_Code")
                .Parameters(9).Value = V_Row.Item("Cf_Memo")
                .Parameters(10).Value = V_Row.Item("Cf_YpMoney")
                .Parameters(11).Value = V_Row.Item("Cf_Money")
                Try
                    .ExecuteNonQuery()
                    Rrow.AcceptChanges()
                    Rinsert = False                                '不充许增加

                Catch ex As Exception
                    Beep()
                    MsgBox(ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
                Finally
                    Call P_Conn(False)
                End Try
            End With
        Else                                                                '编辑主表
            With Rzbadt.UpdateCommand
                .Parameters(0).Value = V_Row.Item("Yy_Code")
                .Parameters(1).Value = V_Row.Item("Cf_Code")
                .Parameters(2).Value = V_Row.Item("Yf_Code")
                .Parameters(3).Value = V_Row.Item("Bl_Code")
                .Parameters(4).Value = V_Row.Item("Ks_Code")
                .Parameters(5).Value = V_Row.Item("Ys_Code")
                .Parameters(6).Value = V_Row.Item("Cf_Date")
                .Parameters(7).Value = V_Row.Item("Cf_Time")
                .Parameters(8).Value = V_Row.Item("Jsr_Code")
                .Parameters(9).Value = V_Row.Item("Cf_Memo")
                .Parameters(10).Value = V_Row.Item("Cf_YpMoney")
                .Parameters(11).Value = V_Row.Item("Cf_Money")
                .Parameters(12).Value = V_Row.Item("Cf_Code", DataRowVersion.Original)
                Try
                    .ExecuteNonQuery()
                    Rrow.AcceptChanges()
                Catch ex As Exception
                    Beep()
                    MsgBox(ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
                Finally
                    Call P_Conn(False)
                End Try
            End With
        End If
        C1TrueDBGrid1.Select()
    End Sub

#End Region


#Region "其它__控件"

    Private Sub Comm_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.Click, Comm2.Click, Comm3.Click, comm4.Click
        Select Case sender.tag

            Case "保存", "处置单"
                If C1Combo3.SelectedValue = "" Then
                    Beep()
                    MsgBox("患者不能为空!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    C1Combo3.Select()
                    Exit Sub
                End If

                If DoctorCombo.SelectedValue = "" Then
                    Beep()
                    MsgBox("医生姓名不能为空!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    DoctorCombo.Select()
                    Exit Sub
                End If
                If KsCombo.SelectedValue = "" Then
                    Beep()
                    MsgBox("科室名称不能为空!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    KsCombo.Select()
                    Exit Sub
                End If

                If YfComobo1.SelectedValue = "" Then
                    Beep()
                    MsgBox("药房名称不能为空!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    YfComobo1.Select()
                    Exit Sub
                End If

                Call Zb_Save()                          '主表存盘
                If sender.tag = "处置单" Then
                    Dim vform As New 护士站.Hsz_Syk(Rrow, "住院")
                    If BaseFunc.BaseFunc.CheckOwnForm(Me, vform) = False Then
                        vform.ShowDialog()
                    End If
                End If

            Case "取消"
                Me.Close()
            Case "完成处方"
                If C1Combo3.SelectedValue = "" Then
                    Beep()
                    MsgBox("患者不能为空!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    C1Combo3.Select()
                    Exit Sub
                End If

                If DoctorCombo.SelectedValue = "" Then
                    Beep()
                    MsgBox("医生名称不能为空!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    DoctorCombo.Select()
                    Exit Sub
                End If
                If KsCombo.SelectedValue = "" Then
                    Beep()
                    MsgBox("科室名称不能为空!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    KsCombo.Select()
                    Exit Sub
                End If

                If YfComobo1.SelectedValue = "" Then
                    Beep()
                    MsgBox("药房名称不能为空!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    YfComobo1.Select()
                    Exit Sub
                End If

                If My_Dataset.Tables("诊疗项目").Rows.Count = 0 And My_Dataset.Tables("药品卫材").Rows.Count = 0 Then
                    MsgBox("没有药品及诊疗数据不能完成处方！", MsgBoxStyle.OkOnly, "提示")
                    Exit Sub
                End If

                If HisVar.HisVar.Sqldal.GetSingle("Select Count(Bl_Code) from Bl where Ry_CyDate is not null and Bl_Code='" & C1Combo3.SelectedValue & "'") > 0 Then
                    MsgBox("该患者已经申请或办理出院，不能完成处方！", MsgBoxStyle.OkOnly, "提示")
                    Exit Sub

                End If



                Call Zb_Save()

                Dim V_YpMoney As Decimal = HisVar.HisVar.Sqldal.GetSingle("select Isnull(Sum(Cf_Money),0) from Bl_CfYp where Cf_Code='" & Label12.Text & "'")
                Dim V_XmMoney As Decimal = HisVar.HisVar.Sqldal.GetSingle("select Isnull(Sum(Cf_Money),0) from Bl_CfXm where Cf_Code='" & Label12.Text & "'")
                Dim V_Money As Decimal = HisVar.HisVar.Sqldal.GetSingle("select Isnull(Sum(Cf_Money),0) from Bl_Cf where Cf_Code='" & Label12.Text & "'")
                If V_YpMoney + V_XmMoney <> V_Money Then
                    MsgBox("金额计算有误，请重新点击数据结算！", MsgBoxStyle.Information, "提示")
                    Exit Sub
                End If

                Dim YeMoney As Decimal = HisVar.HisVar.Sqldal.GetSingle("Select (Select Isnull(Sum(Jf_Money),0) From Bl_Jf Where Bl_Code=Bl.Bl_Code)-(Select Isnull(Sum(Bl_Cfyp.Cf_Money),0) From Bl_Cfyp,Bl_Cf Where Cf_Print='是' and Bl_Cfyp.Cf_Code=Bl_Cf.Cf_Code And Bl_Code=Bl.Bl_Code)-(Select Isnull(Sum(Bl_Cfxm.Cf_Money),0) From Bl_Cfxm,Bl_Cf Where Cf_Print='是' and  Bl_Cfxm.Cf_Code=Bl_Cf.Cf_Code And Bl_Code=Bl.Bl_Code) New_Money FROM Bl where Bl_Code='" & C1Combo3.SelectedValue & "'")
                If YeMoney - V_Money < Val(HisPara.PublicConfig.Qfed) Then
                    MsgBox("该患者费用已达押金警戒线不能完成处方！", MsgBoxStyle.Information, "提示:")
                    Exit Sub
                End If

                Try


                    If ComboBox1.Text = "不需要汇总领药" Then
                        HisVar.HisVar.Sqldal.ExecuteSql("If Exists(Select Cf_Code from Bl_CfXm where Cf_Code='" & V_Cf_Code & "' ) and Not Exists(Select Cf_Code from Bl_CfYp where Cf_Code='" & V_Cf_Code & "') Update Bl_Cf set Cf_Print='是',Cf_Qr='是',Ly_Wc='是',Jsr_Code_Qr='" & HisVar.HisVar.JsrCode & "',Cf_Qr_Date=getdate() where Cf_Code='" & V_Cf_Code & "' else Update Bl_Cf set Cf_Print='是',Ly_Wc='是' where Cf_Code='" & V_Cf_Code & "'")
                    Else
                        HisVar.HisVar.Sqldal.ExecuteSql("If Exists(Select Cf_Code from Bl_CfXm where Cf_Code='" & V_Cf_Code & "' ) and Not Exists(Select Cf_Code from Bl_CfYp where Cf_Code='" & V_Cf_Code & "') Update Bl_Cf set Cf_Print='是',Cf_Qr='是',Ly_Wc='是',Jsr_Code_Qr='" & HisVar.HisVar.JsrCode & "',Cf_Qr_Date=getdate() where Cf_Code='" & V_Cf_Code & "' else Update Bl_Cf set Cf_Print='是' where Cf_Code='" & V_Cf_Code & "'")
                    End If

                    Rrow.Item("Cf_Print") = "是"
                    If My_Dataset.Tables("诊疗项目").Rows.Count = 0 Then
                        Rrow.Item("Cf_Qr") = "否"
                    Else
                        If My_Dataset.Tables("药品卫材").Rows.Count = 0 Then
                            Rrow.Item("Cf_Qr") = "是"
                        Else
                            Rrow.Item("Cf_Qr") = "否"
                        End If
                    End If

                    'DalZcjhLisUsp.ExecLisUspZy(Rrow)
                    DalZcjhLisUsp.GetListXmlbZy(Rrow)
                    My_Table.AcceptChanges()
                    CustomSunnyUI.MyShowNotifier.ShowSuccessNotifier("临时医嘱已完成!")
                    Me.Close()

                Catch ex As Exception
                    MsgBox(ex.Message.ToString, MsgBoxStyle.Information, "提示")
                End Try

        End Select

    End Sub


#End Region

    Private Sub C1Combo2_RowChange(ByVal sender As Object, ByVal e As System.EventArgs) Handles DoctorCombo.RowChange
        If DoctorCombo.WillChangeToValue = "" Then
        Else
            KsCombo.SelectedValue = DoctorCombo.Columns("Ks_Code").Value
        End If
    End Sub


#Region "Combo3动作"


    Private Sub C1Combo3_RowChange(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1Combo3.RowChange
        If C1Combo3.WillChangeToValue = "" Then
            Label10.Text = ""
            Label20.Text = ""
            Label17.Text = ""
        Else

            Label10.Text = Trim(C1Combo3.Columns("Ry_Sex").Text)
            Label20.Text = Trim(C1Combo3.Columns("Jb_Name").Text)
            Label17.Text = Trim(C1Combo3.Columns("Bc_Name").Text)
            If HisPara.PublicConfig.ZyYsz = "否" Or HisVar.HisVar.JsrYsCode & "" = "" Then
                DoctorCombo.SelectedValue = C1Combo3.Columns("Ys_Code").Value
            End If


            My_Reader1 = HisVar.HisVar.Sqldal.ExecuteReader("Select (Select Isnull(Sum(Jf_Money),0) From Bl_Jf Where Bl_Code=Bl.Bl_Code)-(Select Isnull(Sum(Bl_Cfyp.Cf_Money),0) From Bl_Cfyp,Bl_Cf Where Cf_Print='是' And Bl_Cfyp.Cf_Code=Bl_Cf.Cf_Code And Bl_Code=Bl.Bl_Code)-(Select Isnull(Sum(Bl_Cfxm.Cf_Money),0) From Bl_Cfxm,Bl_Cf Where Cf_Print='是' And Bl_Cfxm.Cf_Code=Bl_Cf.Cf_Code And Bl_Code=Bl.Bl_Code) New_Money FROM Bl where Bl_Code='" & C1Combo3.SelectedValue & "'")

            My_Reader1.Read()
            If My_Reader1.HasRows = True Then
                Label24.Text = Format(My_Reader1.Item("New_Money"), "0.00")
            End If
            My_Reader1.Close()

            If Label24.Text <= 0 Then
                Label24.ForeColor = Color.Red
            Else
                Label24.ForeColor = Color.Black
            End If
        End If
    End Sub

    Private Sub C1Combo3_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles C1Combo3.KeyUp

        If (e.KeyValue > 47 And e.KeyValue < 58) Or (e.KeyValue > 96 And e.KeyValue < 123) Or (e.KeyValue > 64 And e.KeyValue < 91) Or (e.KeyValue = 8) Or (e.KeyValue = 189) Or (e.KeyValue = 190) Then
            Dim s As String = C1Combo3.Text
            If C1Combo3.Text = "" Then
                C1Combo3.DataSource.RowFilter = "1=1"
            Else
                C1Combo3.DataSource.RowFilter = "Ry_Jc like '*" & C1Combo3.Text.Replace("*", "[*]") & "*'"
            End If

            If (e.KeyValue = 8) Then
                C1Combo3.DroppedDown = False
                C1Combo3.DroppedDown = True
            End If

            C1Combo3.Text = s
            C1Combo3.SelectionStart = C1Combo3.Text.Length
            C1Combo3.SelectionLength = 0
        End If

    End Sub

    Private Sub C1Combo3_KeyDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles C1Combo3.KeyDown
        If e.KeyValue = 13 Then
            If C1Combo3.WillChangeToIndex < 1 Then
                If (CType(C1Combo3.DataSource, DataView).Count) = 0 Then
                    MsgBox("患者: '" + Me.C1Combo3.Text + "' 不存在！", MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
                    System.Windows.Forms.SendKeys.Send("^{A}")
                    Exit Sub
                End If
                C1Combo3.SelectedIndex = -1
                C1Combo3.SelectedIndex = 0
            Else
                Dim index As Integer
                index = C1Combo3.WillChangeToIndex
                C1Combo3.SelectedIndex = -1
                C1Combo3.SelectedIndex = index
            End If
            e.Handled = True
            System.Windows.Forms.SendKeys.Send("{Tab}")
        End If
    End Sub

#End Region

#Region "Combo6动作"


    Private Sub C1Combo6_RowChange(ByVal sender As Object, ByVal e As System.EventArgs) Handles YfComobo1.RowChange
        If YfComobo1.WillChangeToValue = "" Then

        ElseIf YfComobo1.WillChangeToValue = iniOperate.iniopreate.GetINI("Personal", "默认药房", "", HisVar.HisVar.Parapath & "\Config.Ini") Then

            iniOperate.iniopreate.WriteINI("Personal", "默认药房", YfComobo1.SelectedValue, HisVar.HisVar.Parapath & "\Config.Ini")
        Else
            If My_Dataset.Tables("药品卫材") IsNot Nothing Then

                If My_Dataset.Tables("药品卫材").Rows.Count = 0 Then
                Else
                    If My_Dataset.HasChanges(DataRowState.Deleted) = True Then
                        If My_Dataset.Tables("药品卫材").Rows.Count = My_Dataset.Tables("药品卫材").GetChanges(DataRowState.Deleted).Rows.Count Then
                        Else
                            YfComobo1.SelectedValue = iniOperate.iniopreate.GetINI("Personal", "默认药房", "", HisVar.HisVar.Parapath & "\Config.Ini")
                            MsgBox("请先删除药品再更换药房！", MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
                            Exit Sub
                        End If
                    Else
                        YfComobo1.SelectedValue = iniOperate.iniopreate.GetINI("Personal", "默认药房", "", HisVar.HisVar.Parapath & "\Config.Ini")
                        MsgBox("请先删除药品再更换药房！", MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
                        Exit Sub
                    End If
                End If

            End If

            iniOperate.iniopreate.WriteINI("Personal", "默认药房", YfComobo1.SelectedValue, HisVar.HisVar.Parapath & "\Config.Ini")
        End If
    End Sub


#End Region

#Region "DBGrid动作"

    Private Sub C1TrueDBGrid1_MouseDown(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles C1TrueDBGrid1.MouseDown
        If e.Button = MouseButtons.Right Then
            If C1Combo3.SelectedValue = "" Then
                Beep()
                MsgBox("患者不能为空,按任意键返回!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                C1Combo3.Select()

            ElseIf DoctorCombo.SelectedValue = "" Then
                Beep()
                MsgBox("医生名称不能为空,按任意键返回!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                DoctorCombo.Select()
            ElseIf KsCombo.SelectedValue = "" Then
                Beep()
                MsgBox("科室名称不能为空,按任意键返回!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                KsCombo.Select()
            ElseIf YfComobo1.SelectedValue = "" Then
                Beep()
                MsgBox("药房名称不能为空,按任意键返回!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                YfComobo1.Select()
            Else
                Call Cb_Edit()
            End If
        End If
    End Sub

    Private Sub C1TrueDBGrid1_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles C1TrueDBGrid1.KeyDown
        Select Case e.KeyCode

            Case Keys.Return
                Dim V_Yp_Code As String = C1TrueDBGrid1.Columns(0).Text & ""
                If C1Combo3.SelectedValue = "" Then
                    Beep()
                    MsgBox("患者不能为空,按任意键返回!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    C1Combo3.Select()
                ElseIf DoctorCombo.SelectedValue = "" Then
                    Beep()
                    MsgBox("医生名称不能为空,按任意键返回!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    DoctorCombo.Select()
                ElseIf KsCombo.SelectedValue = "" Then
                    Beep()
                    MsgBox("科室名称不能为空,按任意键返回!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    KsCombo.Select()
                ElseIf YfComobo1.SelectedValue = "" Then
                    Beep()
                    MsgBox("药房名称不能为空,按任意键返回!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    YfComobo1.Select()
                Else
                    Call Cb_Edit()
                End If
            Case Keys.Delete
                If (C1TrueDBGrid1.Row + 1) > C1TrueDBGrid1.RowCount Then
                Else
                    Dim Del_Row As DataRow = Cb_Cm.List(C1TrueDBGrid1.Row).Row
                    If RadioButton1.Checked = True Then
                        If HisVar.HisVar.Sqldal.GetSingle("select count (*) from bl_czd where cf_code='" & V_Cf_Code & "' and xx_code='" & Del_Row.Item("Xx_code") & "'") > 0 Then
                            MsgBox("该药品已经分配处置单，请先删除处置单中相关数据，再进行删除！")
                            Exit Sub
                        End If
                        If MsgBox("是否删除:" + Me.C1TrueDBGrid1.Columns("Yp_Name").Value + " ", MsgBoxStyle.Information + MsgBoxStyle.OkCancel, "提示:") = MsgBoxResult.Cancel Then Exit Sub
                        bllblcfyp.Delete(Del_Row.Item("Cf_Id"))
                        My_Table.Rows.Remove(Del_Row)
                    Else
                        If String.IsNullOrEmpty(Del_Row("Templet_Code") & "") Then
                            If MsgBox("是否删除:" + Me.C1TrueDBGrid1.Columns("Xm_Name").Value + " ", MsgBoxStyle.Information + MsgBoxStyle.OkCancel, "提示:") = MsgBoxResult.Cancel Then Exit Sub
                            bllblcfxm.Delete(Del_Row.Item("Cf_Id"))
                            My_Table.Rows.Remove(Del_Row)
                        Else
                            If MsgBox(" " + Me.C1TrueDBGrid1.Columns("Xm_Name").Value + " 由项目模板生成，是否删除该模板下所有项目？", MsgBoxStyle.Information + MsgBoxStyle.OkCancel, "提示:") = MsgBoxResult.Cancel Then Exit Sub
                            bllblcfxm.Delete(Del_Row.Item("Cf_Code"), Del_Row.Item("Templet_Code"))
                            P_Data_Show()
                        End If
                        'If MsgBox("是否删除:" + Me.C1TrueDBGrid1.Columns("Xm_Name").Value + " ", MsgBoxStyle.Information + MsgBoxStyle.OkCancel, "提示:") = MsgBoxResult.Cancel Then Exit Sub
                        'HisVar.HisVar.Sqldal.ExecuteSql("Delete From Bl_Cfxm Where Cf_Id='" & Del_Row.Item("Cf_Id") & "'")
                    End If

                    C1TrueDBGrid1.Refresh()
                    Call P_SumMoney()
                    Call UpdateCfMoney()
                End If
        End Select

    End Sub


#End Region

#Region "从表__编辑"

    Private Sub Init_Data()     '从表数据
        Dim Str_Insert As String = "Insert Into Bl_Cfyp(Yy_Code,Cf_Code,Xx_Code,Cf_Sl,Cf_Dj,Cf_Money,Cf_Lb,Yp_Yfyl)Values(@Yy_Code,@Cf_Code,@Xx_Code,@Cf_Sl,@Cf_Dj,@Cf_Money,@Cf_Lb,@Yp_Yfyl);select @Cf_Id=IDENT_CURRENT('Bl_Cfyp')"
        Dim Str_Update As String = "Update Bl_Cfyp Set Xx_Code=@Xx_Code,Cf_Sl=@Cf_Sl,Cf_Dj=@Cf_Dj,Cf_Money=@Cf_Money,Cf_Lb=@Cf_Lb,Yp_Yfyl=@Yp_Yfyl Where Cf_Id=@Old_Cf_Id"
        With My_Adapter

            .InsertCommand = New SqlCommand(Str_Insert, My_Cn)
            With .InsertCommand.Parameters
                .Add("@Yy_Code", SqlDbType.VarChar, 4)
                .Add("@Cf_Code", SqlDbType.VarChar, 14)                 '出库编码
                .Add("@Xx_Code", SqlDbType.VarChar, 16)                 '类别编码
                .Add("@Cf_Sl", SqlDbType.Decimal)                   '入库数量
                .Add("@Cf_Dj", SqlDbType.Decimal)                   '入库单价
                .Add("@Cf_Money", SqlDbType.Decimal, 10, 2)
                .Add("@Cf_Lb", SqlDbType.VarChar, 50)
                .Add("@Yp_Yfyl", SqlDbType.VarChar)
                .Add("@Cf_Id", SqlDbType.Int)
            End With

            .UpdateCommand = New SqlCommand(Str_Update, My_Cn)
            With .UpdateCommand.Parameters
                .Add("@Xx_Code", SqlDbType.VarChar, 16)                 '类别编码
                .Add("@Cf_Sl", SqlDbType.Decimal)                   '入库数量
                .Add("@Cf_Dj", SqlDbType.Decimal)                   '入库单价
                .Add("@Cf_Money", SqlDbType.Decimal, 10, 2)
                .Add("@Cf_Lb", SqlDbType.VarChar, 50)
                .Add("@Yp_Yfyl", SqlDbType.VarChar)
                .Add("@Old_Cf_Id", SqlDbType.Int, 4, "Cf_Id")
            End With


        End With

    End Sub

    Private Sub Init_Data1()     '从表数据
        Dim Str_Insert As String = "Insert Into Bl_Cfxm(Yy_Code,Cf_Code,Xm_Code,Cf_Sl,Cf_Dj,Cf_Money,Cf_Lb,Templet_Code)Values(@Yy_Code,@Cf_Code,@Xm_Code,@Cf_Sl,@Cf_Dj,@Cf_Money,@Cf_Lb,@Templet_Code)"
        Dim Str_Update As String = "Update Bl_Cfxm Set Xm_Code=@Xm_Code,Cf_Sl=@Cf_Sl,Cf_Dj=@Cf_Dj,Cf_Money=@Cf_Money,Cf_Lb=@Cf_Lb Where Cf_Id=@Old_Cf_Id"
        With My_Adapter

            .InsertCommand = New SqlCommand(Str_Insert, My_Cn)
            With .InsertCommand.Parameters
                .Add("@Yy_Code", SqlDbType.VarChar, 4)
                .Add("@Cf_Code", SqlDbType.VarChar, 14)                 '出库编码
                .Add("@Xm_Code", SqlDbType.VarChar, 12)                 '类别编码
                .Add("@Cf_Sl", SqlDbType.Decimal)                   '入库数量
                .Add("@Cf_Dj", SqlDbType.Decimal)                   '入库单价
                .Add("@Cf_Money", SqlDbType.Decimal, 10, 2)
                .Add("@Cf_Lb", SqlDbType.VarChar, 50)
                .Add("@Templet_Code", SqlDbType.VarChar, 8)
            End With

            .UpdateCommand = New SqlCommand(Str_Update, My_Cn)
            With .UpdateCommand.Parameters
                .Add("@Xm_Code", SqlDbType.VarChar, 12)                 '类别编码
                .Add("@Cf_Sl", SqlDbType.Decimal)                   '入库数量
                .Add("@Cf_Dj", SqlDbType.Decimal)                   '入库单价
                .Add("@Cf_Money", SqlDbType.Decimal, 10, 2)
                .Add("@Cf_Lb", SqlDbType.VarChar, 50)
                .Add("@Old_Cf_Id", SqlDbType.Int, 4, "Cf_Id")
            End With

        End With
    End Sub


#End Region

#Region "自定义函数"

    Private Sub Cb_Edit()

        If Label24.Text <= Val(HisPara.PublicConfig.Qfed) Then
            MsgBox("押金低于最低限不能开具处方！", MsgBoxStyle.Information, "提示！")
            Exit Sub
        End If

        Call Zb_Save()

        If (C1TrueDBGrid1.Row + 1) > C1TrueDBGrid1.RowCount Then
            V_Insert = True
        Else
            V_Insert = False
            Cb_Row = Cb_Cm.List(C1TrueDBGrid1.Row).Row
        End If

        Select Case Yp_Lb
            Case "药品卫材"
                If V_Insert = False Then
                    If HisVar.HisVar.Sqldal.GetSingle("select count (*) from bl_czd where cf_code='" & V_Cf_Code & "' and xx_code='" & Cb_Row.Item("XX_code") & "'") > 0 Then
                        MsgBox("该药品已经分配处置单，请先删除处置单中相关数据，再进行修改！")
                        C1TrueDBGrid1.Select()
                        Exit Sub
                    End If
                End If

                'Dim vform As New Zy_Cf31(Me, V_Insert, Cb_Row, My_Dataset.Tables(Yp_Lb), T_Label5, C1TrueDBGrid1, My_Adapter, My_Dataset, V_Cf_Code, YfComobo1.SelectedValue, C1Combo3.SelectedValue, Label24.Text)
                'If BaseFunc.BaseFunc.CheckOwnForm(Me, vform) = False Then
                '    vform.ShowDialog()
                'End If
                Dim vform As New ZTHisInpatient.Bl_CfYp(C1Combo3.SelectedValue + "", V_Cf_Code, YfComobo1.SelectedValue + "", V_Insert, Cb_Row, My_Dataset.Tables(Yp_Lb))
                vform.MyTransmitTxt = _transmitTxt
                vform.ShowDialog()
            Case "诊疗项目"
                If V_Insert = False Then
                    If Cb_Row("Templet_Code") IsNot DBNull.Value AndAlso Cb_Row("Templet_Code").Trim() <> "" Then
                        MsgBox("模板里的项目不允许修改", MsgBoxStyle.Information, "提示")
                        Exit Sub
                    End If
                End If
                'Dim vform As New Zy_Cf34(Me, V_Insert, Cb_Row, My_Dataset.Tables(Yp_Lb), T_Label5, C1TrueDBGrid1, My_Adapter, My_Dataset, V_Cf_Code, C1Combo3.SelectedValue, Label24.Text)
                'If BaseFunc.BaseFunc.CheckOwnForm(Me, vform) = False Then
                '    vform.ShowDialog()
                'End If
                Dim vform As New ZTHisInpatient.Bl_CfXm(C1Combo3.SelectedValue + "", V_Cf_Code, YfComobo1.SelectedValue + "", V_Insert, Cb_Row, My_Dataset.Tables(Yp_Lb))
                vform.MyTransmitTxt = _transmitTxt
                vform.ShowDialog()
        End Select
        ''重新计算

        Call P_SumMoney()
        Call UpdateCfMoney()
        Call P_Data_Show()
        C1TrueDBGrid1.Select()
        C1TrueDBGrid1.MoveLast()

    End Sub

    Private Sub P_SumMoney()

        T_Label5.Text = Format(IIf(My_Table.Compute("Sum(Cf_Money)", "") Is DBNull.Value, 0, My_Table.Compute("Sum(Cf_Money)", "")), "###,###,###0.00") + "元"

    End Sub
    Private Sub UpdateCfMoney()
        Dim mdlblcf As New MdlBl_Cf
        mdlblcf = bllblcf.GetModel(Rrow("Cf_Code"))
        mdlblcf.Cf_YpMoney = bllblcfyp.GetCfYpMoney(Rrow("Cf_Code"))
        mdlblcf.Cf_Money = bllblcfyp.GetCfYpMoney(Rrow("Cf_Code")) + bllblcfxm.GetCfXmMoney(Rrow("Cf_Code"))
        bllblcf.UpdateCfMoney(mdlblcf)
    End Sub

    Private Function F_MaxCode(ByVal V_MaxCode As String)   '出库编码
        Dim My_Cc As New BaseClass.C_Cc()
        Dim V_Code As String = V_MaxCode
        My_Cc.Get_MaxCode("Bl_Cf", "Cf_Code", 14, "Left(Cf_Code,10)", HisVar.HisVar.WsyCode & V_MaxCode)
        F_MaxCode = My_Cc.编码
        If Mid(F_MaxCode, 1, 6) = "000000" Then F_MaxCode = V_MaxCode + Mid(My_Cc.编码, 7)
        Return F_MaxCode
    End Function

    Private Sub GridMove(moveType As String)
        If C1TrueDBGrid1.RowCount = 0 Then
            Return
        End If
        Select Case moveType
            Case "最前"
                C1TrueDBGrid1.MoveFirst()
                Exit Select
            Case "上移"
                C1TrueDBGrid1.MovePrevious()
                Exit Select
            Case "下移"
                C1TrueDBGrid1.MoveNext()
                Exit Select
            Case "最后"
                C1TrueDBGrid1.MoveLast()
                Exit Select
            Case Else
                Dim index As Integer
                If Integer.TryParse(moveType, index) Then
                    C1TrueDBGrid1.Row = index
                End If
                Exit Select
        End Select
        Call P_SumMoney()
        Call UpdateCfMoney()
    End Sub
#End Region

#Region "鼠标__外观"
    Private Sub P_Comm(ByVal Bt As Button)
        With Bt
            Select Case .Tag
                Case "保存"
                    .Location = New Point(T_Line1.Left + 10, 4)
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")
                    .Text = "            &B"

                Case "取消"
                    .Location = New Point(Comm1.Left + Comm1.Width + 4, 4)
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
                    .Width = Me.Comm1.Width
                    .Text = "            &C"
                Case "完成处方"
                    .Location = New Point(Comm2.Left + Comm2.Width + 4, 4)
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_完成处方1")
                    .Width = Me.Comm3.Width
                    .Text = "            &F"
                Case "处置单"
                    .Location = New Point(Comm3.Left + Comm3.Width + 4, 4)
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_处置单1")
                    .Width = Me.comm4.Width
                    .Text = "            &X"
                    T_Line2.Location = New Point(Me.comm4.Left + Me.comm4.Width + 8, 5)
            End Select
            .FlatStyle = FlatStyle.Flat
            .FlatAppearance.BorderSize = 0
            .BackgroundImageLayout = ImageLayout.Center
        End With
    End Sub

    Private Sub Comm_MouseEnter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.MouseEnter, Comm2.MouseEnter, Comm3.MouseEnter, comm4.MouseEnter
        Select Case sender.tag
            Case "保存"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存2")
                Comm1.Cursor = Cursors.Hand
            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消2")
                Comm2.Cursor = Cursors.Hand
            Case "完成处方"
                Comm3.BackgroundImage = MyResources.C_Resources.getimage("命令_完成处方2")
                Comm3.Cursor = Cursors.Hand
            Case "处置单"
                comm4.BackgroundImage = MyResources.C_Resources.getimage("命令_处置单2")
                comm4.Cursor = Cursors.Hand

        End Select

    End Sub

    Private Sub Comm_MouseLeave(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.MouseLeave, Comm2.MouseLeave, Comm3.MouseLeave, comm4.MouseLeave
        Select Case sender.tag
            Case "保存"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")
                Comm1.Cursor = Cursors.Default
            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
                Comm2.Cursor = Cursors.Default
            Case "完成处方"
                Comm3.BackgroundImage = MyResources.C_Resources.getimage("命令_完成处方1")
                Comm3.Cursor = Cursors.Hand
            Case "处置单"
                comm4.BackgroundImage = MyResources.C_Resources.getimage("命令_处置单1")
                comm4.Cursor = Cursors.Hand

        End Select
    End Sub

    Private Sub Comm_MouseDown(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm1.MouseDown, Comm2.MouseDown, Comm3.MouseDown, comm4.MouseDown
        Select Case sender.tag
            Case "保存"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存3")
                Comm1.Cursor = Cursors.Default

            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消3")
                Comm2.Cursor = Cursors.Default
            Case "完成处方"
                Comm3.BackgroundImage = MyResources.C_Resources.getimage("命令_完成处方3")
                Comm3.Cursor = Cursors.Hand
            Case "处置单"
                comm4.BackgroundImage = MyResources.C_Resources.getimage("命令_处置单3")
                comm4.Cursor = Cursors.Hand

        End Select
    End Sub

    Private Sub Comm_MouseUp(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm1.MouseUp, Comm2.MouseUp, Comm3.MouseUp, comm4.MouseUp
        Select Case sender.tag
            Case "保存"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")
                Comm1.Cursor = Cursors.Hand
            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
                Comm2.Cursor = Cursors.Hand
            Case "完成处方"
                Comm3.BackgroundImage = MyResources.C_Resources.getimage("命令_完成处方1")
                Comm3.Cursor = Cursors.Hand
            Case "处置单"
                comm4.BackgroundImage = MyResources.C_Resources.getimage("命令_处置单1")
                comm4.Cursor = Cursors.Hand

        End Select
    End Sub
#End Region

#Region "中西药、草药、卫材切换"

    Private Sub RadioButton1_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles RadioButton1.CheckedChanged, RadioButton4.CheckedChanged
        If Me.Visible = False Then Exit Sub

        If Rinsert = True Then
            V_Cf_Code = F_MaxCode(Format(Rdate, "yyMMdd"))
            Label12.Text = V_Cf_Code
        End If

        If RadioButton1.Checked = True Then
            Call Init_Data()
            C1TrueDBGrid_Init_Zxy()
            Yp_Lb = Mid(RadioButton1.Text, 4)
            C1Command1.Enabled = True
            C1Command2.Enabled = True
            C1NumericEdit1.Enabled = True
        End If
        If RadioButton4.Checked = True Then
            Call Init_Data1()
            C1TrueDBGrid_Init_Xm()
            Yp_Lb = Mid(RadioButton4.Text, 4)
            C1Command1.Enabled = False
            C1Command2.Enabled = False
            C1NumericEdit1.Enabled = False
        End If
        Call P_Data_Show()
        C1TrueDBGrid1.Select()
    End Sub

#End Region

#Region "中西药、草药、卫材C1Truedbgrid初始化"
    Private Sub C1TrueDBGrid_Init_Zxy()
        '初始化TDBGrid
        Dim My_Grid As New BaseClass.C_Grid(Me.C1TrueDBGrid1)
        With My_Grid
            .Init_Grid()
            .AllAddNew(True)
            .AllDelete(True)
            .AllUpdate(False)
            .AllSort(False)
            '.P_MarqueeEnum(BaseClass.C_Grid.MarqueeEnum.FloatingEditor)
            .P_Dock(BaseClass.C_Grid.Dock.Fill)
            .Init_Column("药品名称", "Yp_Name", 250, "左", "")
            .Init_Column("规格", "Mx_Gg", 130, "左", "")
            .Init_Column("产地", "Mx_Cd", 200, "左", "")
            .Init_Column("有效期", "Yp_Yxq", 80, "中", "yyyy-MM-dd")
            .Init_Column("数量", "Cf_Sl", 60, "右", "###,###,##0.00##")
            .Init_Column("单位", "Mx_XsDw", 45, "左", "")
            .Init_Column("单价", "Cf_Dj", 70, "右", "###,###,##0.00####")
            .Init_Column("金额", "Cf_Money", 80, "右", "###,###,##0.00##")
            .Init_Column("用法用量", "Yp_Yfyl", 200, "左", "")
            .Init_Column("类别", "Cf_Lb", 70, "中", "")
        End With
        C1TrueDBGrid1.VScrollBar.Style = C1.Win.C1TrueDBGrid.ScrollBarStyleEnum.Automatic
    End Sub

    Private Sub C1TrueDBGrid_Init_Xm()
        '初始化TDBGrid
        Dim My_Grid As New BaseClass.C_Grid(Me.C1TrueDBGrid1)
        With My_Grid
            .Init_Grid()
            .AllAddNew(True)
            .AllDelete(True)
            .AllUpdate(False)
            .AllSort(False)

            .P_Dock(BaseClass.C_Grid.Dock.Fill)
            .Init_Column("项目名称", "Xm_Name", 410, "左", "")
            .Init_Column("项目单位", "Xm_Dw", 100, "左", "")

            .Init_Column("数量", "Cf_Sl", 60, "右", "###,###,##0")
            .Init_Column("单价", "Cf_Dj", 60, "右", "###,###,##0.00")
            .Init_Column("金额", "Cf_Money", 60, "右", "###,###,##0.00")
            .Init_Column("类别", "Cf_Lb", 70, "右", "###,###,##0.00")
        End With
        C1TrueDBGrid1.VScrollBar.Style = C1.Win.C1TrueDBGrid.ScrollBarStyleEnum.Automatic
    End Sub
#End Region

    Private Sub C1Command1_Click(ByVal sender As Object, ByVal e As C1.Win.C1Command.ClickEventArgs) Handles C1Command1.Click, C1Command2.Click
        C1ToolBar1.Select()
        If sender.text = "乘" Then
            If C1TrueDBGrid1.RowCount = 0 Then Exit Sub
            If C1TrueDBGrid1.Columns("数量").Value = 0 Then Exit Sub

            Dim m_Select As String
            Dim m_Str As String

            m_Str = ""
            V_Yf_Sl = "Yf_Sl" & Mid(YfComobo1.SelectedValue, 6)
            m_Select = "select " & V_Yf_Sl & "-Bl_Cfyp.Cf_Sl*" & C1NumericEdit1.Value & ",Yp_Name from Bl_Cfyp,(SELECT  (" & V_Yf_Sl & "-isnull(Mz_Sl,0)-Isnull(Cf_Sl,0)) As " & V_Yf_Sl & ",V_Ypkc.Xx_Code,Yp_Name From  V_Ypkc left join (select Xx_Code,Sum(Mz_Sl) AS Mz_Sl from mz_yp,Mz where Mz.Mz_Code=mz_yp.Mz_Code and Mz_FyQr=0 And Mz.Yy_Code='" & HisVar.HisVar.WsyCode & "' group By Xx_Code ) a on V_Ypkc.Xx_Code=a.Xx_Code Left Join (select Xx_Code,Sum(Cf_Sl) AS Cf_Sl from Bl_Cfyp,Bl_Cf where Bl_Cfyp.Cf_Code=Bl_Cf.Cf_Code and Cf_Qr='否' and Bl_Cf.Cf_Code<>'" & Label12.Text & "' And Bl_Cf.Yy_Code='" & HisVar.HisVar.WsyCode & "' group By Xx_Code ) b on V_Ypkc.Xx_Code=b.Xx_Code where  Yy_Code='" & HisVar.HisVar.WsyCode & "')b where Bl_Cfyp.xx_code=b.xx_code and  Cf_code='" & Label12.Text & "' And  (" & V_Yf_Sl & "-Bl_Cfyp.Cf_Sl*" & C1NumericEdit1.Value & ")<0 And Cf_Lb='中草药'"
            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, m_Select, "草药副数", True)

            For Each m_Row In My_Dataset.Tables("草药副数").Rows
                m_Str = m_Str & vbCrLf & m_Row("Yp_Name")
            Next

            If m_Str <> "" Then
                m_Str = m_Str & vbCrLf
                MsgBox("中草药:" & m_Str & "库存不足!", vbCritical, "提示")
                Exit Sub
            End If


            HisVar.HisVar.Sqldal.ExecuteSql("Update Bl_Cfyp Set Cf_Sl=Cf_Sl*" & C1NumericEdit1.Value & ",Cf_Money=Cf_Sl*" & C1NumericEdit1.Value & "*Cf_Dj Where Cf_Code='" & Label12.Text & "' And Cf_Lb like '%草药%'")
            Call P_Data_Show()
            Call UpdateCfMoney()
        End If
        If sender.text = "除" Then
            If C1TrueDBGrid1.RowCount = 0 Then Exit Sub
            If C1TrueDBGrid1.Columns("数量").Value = 0 Then Exit Sub
            HisVar.HisVar.Sqldal.ExecuteSql("Update Bl_Cfyp Set Cf_Sl=Cf_Sl/" & C1NumericEdit1.Value & ",Cf_Money=Cf_Sl/" & C1NumericEdit1.Value & "*Cf_Dj Where Cf_Code='" & Label12.Text & "' And Cf_Lb like '%草药%'")
            Call P_Data_Show()
            Call UpdateCfMoney()
        End If
    End Sub

#Region "输入法设置"
    '中文


    Private Sub C1Combo1_GotFocus(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles DoctorCombo.GotFocus, C1Combo3.GotFocus, KsCombo.GotFocus, YfComobo1.GotFocus
        InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages(0)
    End Sub

#End Region


    Private Sub ComboBox1_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ComboBox1.SelectedIndexChanged
        iniOperate.iniopreate.WriteINI("汇总领药", "医嘱是否汇总", ComboBox1.SelectedIndex, HisVar.HisVar.Parapath & "\Config.ini")
    End Sub

    Private Sub KsCombo_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles KsCombo.KeyPress
        If e.KeyChar <> Chr(Keys.Return) Then Exit Sub
        e.Handled = True
        C1NumericEdit1.Select()
    End Sub
End Class