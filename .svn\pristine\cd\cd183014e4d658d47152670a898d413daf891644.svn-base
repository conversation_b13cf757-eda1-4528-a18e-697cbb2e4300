﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class PublicForm
    Inherits HisControl.BaseChild

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.QXButton = New CustomControl.MyButton()
        Me.EnsureButton = New CustomControl.MyButton()
        Me.FXButton = New CustomControl.MyButton()
        Me.FilterTextBox = New CustomControl.MyTextBox()
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel()
        Me.MyGrid1 = New CustomControl.MyGrid()
        Me.TableLayoutPanel1.SuspendLayout()
        Me.SuspendLayout()
        '
        'QXButton
        '
        Me.QXButton.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.QXButton.DialogResult = System.Windows.Forms.DialogResult.None
        Me.QXButton.Dock = System.Windows.Forms.DockStyle.Fill
        Me.QXButton.Location = New System.Drawing.Point(3, 33)
        Me.QXButton.Name = "QXButton"
        Me.QXButton.Size = New System.Drawing.Size(58, 24)
        Me.QXButton.TabIndex = 0
        Me.QXButton.Text = "全选"
        '
        'EnsureButton
        '
        Me.EnsureButton.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.EnsureButton.DialogResult = System.Windows.Forms.DialogResult.OK
        Me.EnsureButton.Dock = System.Windows.Forms.DockStyle.Fill
        Me.EnsureButton.Location = New System.Drawing.Point(317, 33)
        Me.EnsureButton.Name = "EnsureButton"
        Me.EnsureButton.Size = New System.Drawing.Size(58, 24)
        Me.EnsureButton.TabIndex = 1
        Me.EnsureButton.Text = "确定"
        '
        'FXButton
        '
        Me.FXButton.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.FXButton.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.FXButton.DialogResult = System.Windows.Forms.DialogResult.None
        Me.FXButton.Location = New System.Drawing.Point(67, 33)
        Me.FXButton.Name = "FXButton"
        Me.FXButton.Size = New System.Drawing.Size(58, 24)
        Me.FXButton.TabIndex = 1
        Me.FXButton.Text = "反选"
        '
        'FilterTextBox
        '
        Me.FilterTextBox.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.FilterTextBox.Captain = "医院过滤"
        Me.FilterTextBox.CaptainBackColor = System.Drawing.Color.Transparent
        Me.FilterTextBox.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.FilterTextBox.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.FilterTextBox.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.FilterTextBox, 4)
        Me.FilterTextBox.ContentForeColor = System.Drawing.Color.Black
        Me.FilterTextBox.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.FilterTextBox.Location = New System.Drawing.Point(3, 5)
        Me.FilterTextBox.Multiline = False
        Me.FilterTextBox.Name = "FilterTextBox"
        Me.FilterTextBox.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.FilterTextBox.ReadOnly = False
        Me.FilterTextBox.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.FilterTextBox.SelectionStart = 0
        Me.FilterTextBox.SelectStart = 0
        Me.FilterTextBox.Size = New System.Drawing.Size(568, 20)
        Me.FilterTextBox.TabIndex = 2
        Me.FilterTextBox.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.FilterTextBox.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.FilterTextBox.Watermark = Nothing
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.ColumnCount = 4
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 64.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 250.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 64.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 76.0!))
        Me.TableLayoutPanel1.Controls.Add(Me.QXButton, 0, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.EnsureButton, 2, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.FXButton, 1, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.FilterTextBox, 0, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.MyGrid1, 0, 2)
        Me.TableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(0, 0)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.RowCount = 4
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 30.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 30.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 260.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 20.0!))
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(574, 330)
        Me.TableLayoutPanel1.TabIndex = 1
        '
        'MyGrid1
        '
        Me.MyGrid1.CanCustomCol = False
        Me.MyGrid1.Col = 0
        Me.MyGrid1.ColumnFooters = False
        Me.TableLayoutPanel1.SetColumnSpan(Me.MyGrid1, 4)
        Me.MyGrid1.DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveRight
        Me.MyGrid1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.MyGrid1.FetchRowStyles = False
        Me.MyGrid1.Location = New System.Drawing.Point(0, 60)
        Me.MyGrid1.Margin = New System.Windows.Forms.Padding(0)
        Me.MyGrid1.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.MyGrid1.Name = "MyGrid1"
        Me.MyGrid1.Size = New System.Drawing.Size(574, 260)
        Me.MyGrid1.TabAction = C1.Win.C1TrueDBGrid.TabActionEnum.ControlNavigation
        Me.MyGrid1.TabIndex = 3
        Me.MyGrid1.Xmlpath = Nothing
        '
        'PublicForm
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(574, 330)
        Me.Controls.Add(Me.TableLayoutPanel1)
        Me.Name = "PublicForm"
        Me.Text = "UpDataFile"
        Me.TableLayoutPanel1.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents QXButton As CustomControl.MyButton
    Friend WithEvents EnsureButton As CustomControl.MyButton
    Friend WithEvents FXButton As CustomControl.MyButton
    Friend WithEvents FilterTextBox As CustomControl.MyTextBox
    Friend WithEvents TableLayoutPanel1 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents MyGrid1 As CustomControl.MyGrid
End Class
