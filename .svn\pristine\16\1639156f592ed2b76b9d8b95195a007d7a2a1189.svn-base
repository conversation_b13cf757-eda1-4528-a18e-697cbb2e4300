﻿/**  版本信息模板在安装目录下，可自行修改。
* M_Emr_SiKong.cs
*
* 功 能： N/A
* 类 名： M_Emr_SiKong
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/8/11 11:19:16   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace ModelOld
{
	/// <summary>
	/// M_Emr_SiKong:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class M_Emr_SiKong
	{
		public M_Emr_SiKong()
		{}
		#region Model
		private string _sk_code;
		private string _mblb_code;
		private string _mb_code;
		private string _yl_event;
		private int? _days;
		private int? _hours;
		private string _memo;
		/// <summary>
		/// 
		/// </summary>
		public string Sk_Code
		{
			set{ _sk_code=value;}
			get{return _sk_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Mblb_Code
		{
			set{ _mblb_code=value;}
			get{return _mblb_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Mb_Code
		{
			set{ _mb_code=value;}
			get{return _mb_code;}
		}
		/// <summary>
		/// 入院、出院
		/// </summary>
		public string Yl_Event
		{
			set{ _yl_event=value;}
			get{return _yl_event;}
		}
		/// <summary>
		/// 
		/// </summary>
		public int? Days
		{
			set{ _days=value;}
			get{return _days;}
		}
		/// <summary>
		/// 小时
		/// </summary>
		public int? Hours
		{
			set{ _hours=value;}
			get{return _hours;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Memo
		{
			set{ _memo=value;}
			get{return _memo;}
		}
		#endregion Model

	}
}

