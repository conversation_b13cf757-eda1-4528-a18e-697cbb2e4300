﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class DcCondition
    Inherits HisControl.BaseChild

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.MyButton1 = New CustomControl.MyButton()
        Me.MyButton2 = New CustomControl.MyButton()
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel()
        Me.MaterialsClassTextBox = New CustomControl.MyTextBox()
        Me.WhComboBox = New CustomControl.MyDtComobo()
        Me.MaterialsClassMyButton = New CustomControl.MyButton()
        Me.MaterialsTextBox1 = New CustomControl.MyTextBox()
        Me.MaterialsMyButton3 = New CustomControl.MyButton()
        Me.TableLayoutPanel1.SuspendLayout()
        Me.SuspendLayout()
        '
        'MyButton1
        '
        Me.MyButton1.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.MyButton1.DialogResult = System.Windows.Forms.DialogResult.None
        Me.MyButton1.Location = New System.Drawing.Point(413, 38)
        Me.MyButton1.Name = "MyButton1"
        Me.MyButton1.Size = New System.Drawing.Size(77, 25)
        Me.MyButton1.TabIndex = 5
        Me.MyButton1.Text = "确定"
        '
        'MyButton2
        '
        Me.MyButton2.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.MyButton2.DialogResult = System.Windows.Forms.DialogResult.None
        Me.MyButton2.Location = New System.Drawing.Point(413, 73)
        Me.MyButton2.Name = "MyButton2"
        Me.MyButton2.Size = New System.Drawing.Size(77, 25)
        Me.MyButton2.TabIndex = 6
        Me.MyButton2.Text = "取消"
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.ColumnCount = 6
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 60.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 140.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 35.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 140.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 35.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 50.0!))
        Me.TableLayoutPanel1.Controls.Add(Me.MaterialsClassTextBox, 0, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.WhComboBox, 0, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.MaterialsClassMyButton, 4, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.MaterialsTextBox1, 0, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.MaterialsMyButton3, 4, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.MyButton2, 5, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.MyButton1, 5, 1)
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(3, 1)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.RowCount = 3
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35.0!))
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(503, 103)
        Me.TableLayoutPanel1.TabIndex = 24
        '
        'MaterialsClassTextBox
        '
        Me.MaterialsClassTextBox.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.MaterialsClassTextBox.Captain = "物资类别"
        Me.MaterialsClassTextBox.CaptainBackColor = System.Drawing.Color.Transparent
        Me.MaterialsClassTextBox.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MaterialsClassTextBox.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.MaterialsClassTextBox.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.MaterialsClassTextBox, 4)
        Me.MaterialsClassTextBox.ContentForeColor = System.Drawing.Color.Black
        Me.MaterialsClassTextBox.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.MaterialsClassTextBox.Location = New System.Drawing.Point(3, 42)
        Me.MaterialsClassTextBox.Multiline = False
        Me.MaterialsClassTextBox.Name = "MaterialsClassTextBox"
        Me.MaterialsClassTextBox.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.MaterialsClassTextBox.ReadOnly = False
        Me.MaterialsClassTextBox.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.MaterialsClassTextBox.SelectionStart = 0
        Me.MaterialsClassTextBox.SelectStart = 0
        Me.MaterialsClassTextBox.Size = New System.Drawing.Size(369, 20)
        Me.MaterialsClassTextBox.TabIndex = 1
        Me.MaterialsClassTextBox.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MaterialsClassTextBox.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.MaterialsClassTextBox.Watermark = Nothing
        '
        'WhComboBox
        '
        Me.WhComboBox.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.WhComboBox.Captain = "仓库名称"
        Me.WhComboBox.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.WhComboBox.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.WhComboBox.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.WhComboBox, 3)
        Me.WhComboBox.DataSource = Nothing
        Me.WhComboBox.ItemHeight = 18
        Me.WhComboBox.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.WhComboBox.Location = New System.Drawing.Point(3, 7)
        Me.WhComboBox.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.WhComboBox.MinimumSize = New System.Drawing.Size(0, 20)
        Me.WhComboBox.Name = "WhComboBox"
        Me.WhComboBox.ReadOnly = False
        Me.WhComboBox.Size = New System.Drawing.Size(229, 20)
        Me.WhComboBox.TabIndex = 0
        Me.WhComboBox.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'MaterialsClassMyButton
        '
        Me.MaterialsClassMyButton.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.MaterialsClassMyButton.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.MaterialsClassMyButton.DialogResult = System.Windows.Forms.DialogResult.None
        Me.MaterialsClassMyButton.Location = New System.Drawing.Point(378, 42)
        Me.MaterialsClassMyButton.Name = "MaterialsClassMyButton"
        Me.MaterialsClassMyButton.Size = New System.Drawing.Size(29, 20)
        Me.MaterialsClassMyButton.TabIndex = 2
        Me.MaterialsClassMyButton.Tag = "物资类别"
        Me.MaterialsClassMyButton.Text = ".."
        '
        'MaterialsTextBox1
        '
        Me.MaterialsTextBox1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.MaterialsTextBox1.Captain = "物资名称"
        Me.MaterialsTextBox1.CaptainBackColor = System.Drawing.Color.Transparent
        Me.MaterialsTextBox1.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MaterialsTextBox1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.MaterialsTextBox1.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.MaterialsTextBox1, 4)
        Me.MaterialsTextBox1.ContentForeColor = System.Drawing.Color.Black
        Me.MaterialsTextBox1.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.MaterialsTextBox1.Location = New System.Drawing.Point(3, 77)
        Me.MaterialsTextBox1.Multiline = False
        Me.MaterialsTextBox1.Name = "MaterialsTextBox1"
        Me.MaterialsTextBox1.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.MaterialsTextBox1.ReadOnly = False
        Me.MaterialsTextBox1.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.MaterialsTextBox1.SelectionStart = 0
        Me.MaterialsTextBox1.SelectStart = 0
        Me.MaterialsTextBox1.Size = New System.Drawing.Size(369, 20)
        Me.MaterialsTextBox1.TabIndex = 3
        Me.MaterialsTextBox1.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MaterialsTextBox1.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.MaterialsTextBox1.Watermark = Nothing
        '
        'MaterialsMyButton3
        '
        Me.MaterialsMyButton3.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.MaterialsMyButton3.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.MaterialsMyButton3.DialogResult = System.Windows.Forms.DialogResult.None
        Me.MaterialsMyButton3.Location = New System.Drawing.Point(378, 77)
        Me.MaterialsMyButton3.Name = "MaterialsMyButton3"
        Me.MaterialsMyButton3.Size = New System.Drawing.Size(29, 20)
        Me.MaterialsMyButton3.TabIndex = 4
        Me.MaterialsMyButton3.Tag = "物资"
        Me.MaterialsMyButton3.Text = ".."
        '
        'DcCondition
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.ClientSize = New System.Drawing.Size(507, 111)
        Me.Controls.Add(Me.TableLayoutPanel1)
        Me.Name = "DcCondition"
        Me.Text = "价格类型"
        Me.TableLayoutPanel1.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents MyButton1 As CustomControl.MyButton
    Friend WithEvents MyButton2 As CustomControl.MyButton
    Friend WithEvents TableLayoutPanel1 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents MaterialsClassTextBox As CustomControl.MyTextBox
    Friend WithEvents MaterialsClassMyButton As CustomControl.MyButton
    Friend WithEvents WhComboBox As CustomControl.MyDtComobo
    Friend WithEvents MaterialsTextBox1 As CustomControl.MyTextBox
    Friend WithEvents MaterialsMyButton3 As CustomControl.MyButton

End Class
