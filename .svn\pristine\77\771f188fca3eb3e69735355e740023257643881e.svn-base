﻿Imports System.Data.SqlClient
Imports System.Windows.Forms
Imports BaseClass
Imports System.Drawing

Public Class TestSq1
    Dim BllLIS_Test1 As New BLLOld.B_LIS_Test1
    Dim ModelTest1 As New ModelOld.M_LIS_Test1
    Private bllKs As New BLLOld.B_Zd_YyKs
    Private bllBc As New BLLOld.B_V_YyBc
    Private bllYs As New BLLOld.B_Zd_YyYs
    Private bllBxlb As New BLLOld.B_Zd_Bxlb
    Private bllTestXm As New BLLOld.B_LIS_TestXm

    Dim My_Dataset As New DataSet
    Dim My_View As New DataView
    Public My_Table As New DataTable
    Public My_Cm As CurrencyManager
    Public My_Row As DataRow
    Public V_Insert As Boolean
    Dim My_Cc As New C_Cc
    Dim Rrc As C_RowChange

    Private Sub TestSq1_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Call Form_Init()
        Call P_Data_Show("")
        Call MyGrid1_RowColChange(Nothing, Nothing)
        Call Data_Clear()

    End Sub

#Region "窗体初始化"
    Private Sub Form_Init()
        With MyGrid1
            .Clear()

            .Init_Column("检验编码", "Test_Code", "150", "中", "", False)
            .Init_Column("姓名", "Ry_Name", "120", "中", "", False)
            .Init_Column("性别", "Ry_Sex", "60", "中", "", False)
            .Init_Column("身份证", "Ry_Sfzh", "150 ", "左", "", False)
            .Init_Column("健康卡号", "JkkNo", "150", "中", "", False)
            .Init_Column("申请人", "SendDoctor", "100 ", "中", "", False)
            .Init_Column("检验状态", "TestState", "120 ", "中", "", False)
            .Init_Column("状态", "State", "80 ", "中", "", False)
        End With
        MyGrid1.Splits(0).DisplayColumns("State").FetchStyle = True

        With RySex
            .Additem = "男"
            .Additem = "女"
            .DisplayColumns(1).Visible = False
            .DroupDownWidth = .Width
            .SelectedIndex = 0
        End With
        With TestState
            .Additem = "全部"
            .Additem = "录入"
            .Additem = "待采集"
            .DisplayColumns(1).Visible = False
            .DroupDownWidth = .Width
            .SelectedIndex = 0
        End With
        With SendJsr
            .DataView = bllYs.GetList("Yy_Code='" & HisVar.HisVar.WsyCode & "'").Tables(0).DefaultView
            .Init_Colum("Ys_name", "医生姓名", 120, "中")
            .Init_Colum("Ys_code", "医生编码", 0, "中")
            .Init_Colum("Ys_jc", "医生简称", 0, "中")
            .ValueMember = "Ys_code"
            .DisplayMember = "Ys_name"
            .RowFilterNotTextNull = "Ys_jc"
            .DroupDownWidth = .Width
            .SelectedIndex = -1
        End With

        With TestXm
            .DataView = bllTestXm.GetList("").Tables(0).DefaultView
            .Init_Colum("TestXm_name", "项目名称", 120, "中")
            .Init_Colum("TestXm_code", "项目编码", 0, "中")
            .Init_Colum("TestXm_jc", "项目简称", 0, "中")
            .ValueMember = "TestXm_code"
            .DisplayMember = "TestXm_name"
            .RowFilterNotTextNull = "TestXm_jc"
            .DroupDownWidth = .Width
            .SelectedIndex = -1
        End With
        TestCode.Enabled = False
    End Sub

    Private Sub P_Data_Show(ByVal str As String)
        Dim state As String = "1=1 "
        If TestState.Text = "全部" Then
            state = state + " and (TestState='录入' or TestState='待采集')"
        ElseIf TestState.Text = "录入" Then
            state = state + " and TestState='录入' "
        ElseIf TestState.Text = "待采集" Then
            state = state + " and TestState='待采集' "
        End If

        My_Dataset = BllLIS_Test1.GetList("" & state & " and SendTime between '" & Format(MyDateEdit1.Value, "yyyy-MM-dd 00:00:00") & "' and '" & Format(MyDateEdit2.Value, "yyyy-MM-dd 23:59:59") & "' " & str & "")

        My_Table = My_Dataset.Tables(0)

        My_Table.PrimaryKey = New DataColumn() {My_Table.Columns("Test_Code")}

        With Me.MyGrid1
            My_Cm = CType(BindingContext(My_Table, ""), CurrencyManager)
            .DataTable = My_Table
        End With
        My_View = My_Cm.List

    End Sub
#End Region

#Region "控件动作"
    '读健康卡
    Private Sub BtnRead_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnRead.Click
        Dim CardLb As String = MzZy.HD_ConnectDevice(True, True, True, False, False, False, False, False)
        If CardLb <> "读卡失败" Then

            If BllLIS_Test1.GetRecordCount("JkkNo='" & Model.Jkk.BankNo.ToString() & "'") = 0 Then
                MsgBox("未找到该患者检查项目申请记录，不能读取，请直接录入！", MsgBoxStyle.Information, "提示")
                Exit Sub
            Else
                Call P_Data_Show(" and JkkNo='" & Model.Jkk.BankNo.ToString() & "' ")
            End If

        Else
            MsgBox("读取信息失败，请检查卡及设备后重新读取！", MsgBoxStyle.Information, "提示")
        End If
        MzZy.CloseDevice()
    End Sub

    Private Sub BtnSave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnSave.Click, BtnOK.Click
        If RyName.Text = "" Then
            MsgBox("姓名不能为空！", MsgBoxStyle.Information, "提示")
            RyName.Select()
            Exit Sub
        End If
        If SendJsr.Text = "" Then
            MsgBox("申请人不能为空！", MsgBoxStyle.Information, "提示")
            SendJsr.Select()
            Exit Sub
        End If
        If TestXm.Text = "" Then
            MsgBox("申请项目不能为空！", MsgBoxStyle.Information, "提示")
            TestXm.Select()
            Exit Sub
        End If
        Select Case sender.tag
            Case "保存"
                Call Data_Add("录入")
            Case "申请"
                Call Data_Add("待采集")
        End Select


    End Sub


    Private Sub BtnClear_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnClear.Click, BtnDel.Click, BtnSx.Click, BtnQuery.Click
        Select Case sender.text
            Case "清空"
                Call Data_Clear()
            Case "删除"
                Call Del()
            Case "刷新"
                Call P_Data_Show("")
            Case "查询"
                Call P_Data_Show("  ")
        End Select

    End Sub

    Private Sub MyGrid1_RowColChange(ByVal sender As System.Object, ByVal e As C1.Win.C1TrueDBGrid.RowColChangeEventArgs) Handles MyGrid1.RowColChange
        If (MyGrid1.Row + 1) > MyGrid1.RowCount Then
        Else
            My_Row = My_Cm.List(MyGrid1.Row).Row
            If My_Row("TestState") = "录入" Then
                V_Insert = False
            End If
            If My_Row("TestState") = "待采集" Then
                Call Data_Clear()
                Exit Sub
            End If
            RyName.Text = My_Row("Ry_Name") & ""
            RySex.Text = My_Row("RY_SEX") & ""
            RySfzh.Text = My_Row("Ry_Sfzh") & ""
            JkkNo.Text = My_Row("jkkno") & ""
            TestCode.Text = My_Row("Test_Code") & ""
            SendJsr.SelectedValue = My_Row("SendJsr") & ""
            TestXm.SelectedValue = My_Row("TestXm_Code") & ""
        End If
    End Sub

    Private Sub MyGrid1_FetchCellStyle(ByVal sender As System.Object, ByVal e As C1.Win.C1TrueDBGrid.FetchCellStyleEventArgs) Handles MyGrid1.FetchCellStyle
        If e.Column.Name = "状态" Then
            Dim falg As String = MyGrid1.Columns("TestState").CellValue(e.Row).ToString
            If falg = "录入" Then
                e.CellStyle.ForegroundImage = ZtHis.Lis.My.Resources.录入
            ElseIf falg = "待采集" Then
                e.CellStyle.ForegroundImage = ZtHis.Lis.My.Resources.待采集
            ElseIf falg = "待检验" Then
                e.CellStyle.ForegroundImage = ZtHis.Lis.My.Resources.待检验
            ElseIf falg = "待审核" Then
                e.CellStyle.ForegroundImage = ZtHis.Lis.My.Resources.待审核
            ElseIf falg = "拒绝申请" Then
                e.CellStyle.ForegroundImage = ZtHis.Lis.My.Resources.拒检
            End If
            e.CellStyle.ForeGroundPicturePosition = C1.Win.C1TrueDBGrid.ForeGroundPicturePositionEnum.PictureOnly
        End If
    End Sub

    Private Sub Del()

        If MsgBox("是否删除【" + Me.MyGrid1.Columns("Ry_Name").Value + "】的检验记录？", MsgBoxStyle.Information + MsgBoxStyle.OkCancel + MsgBoxStyle.DefaultButton1, "提示:") = MsgBoxResult.Cancel Then Exit Sub
        My_Row = My_Cm.List(MyGrid1.Row).Row
        If My_Row.Item("TestState") = "待采集" Then
            MsgBox("待采集记录不允许删除！", MsgBoxStyle.Information, "提示")
            Exit Sub
        End If
        BllLIS_Test1.Delete(My_Row.Item("Test_Code"), "体检", "")

        MyGrid1.Delete()
        My_Row.AcceptChanges()
        Call Data_Clear()
    End Sub
#End Region

#Region "数据操作"
    Private Sub Data_Clear()
        V_Insert = True
        TestCode.Enabled = False
        TestCode.Text = BllLIS_Test1.MaxCode()
        RyName.Text = ""
        RySex.SelectedIndex = 0
        RySfzh.Text = ""
        JkkNo.Text = ""
        RyAge.Value = 0
        RyAgeMonth.Value = 0
        RyAgeWeek.Value = 0
        SendJsr.Text = ""
        TestXm.SelectedIndex = -1
        MyDateEdit1.Value = Format(Now, "yyyy-MM-dd")
        MyDateEdit2.Value = Format(Now, "yyyy-MM-dd")

        RyName.Enabled = True
        RySex.Enabled = True
        RySfzh.Enabled = True
        JkkNo.Enabled = True
        RyAge.Enabled = True
        RyAgeMonth.Enabled = True
        RyAgeWeek.Enabled = True
        SendJsr.Enabled = True
        TestXm.Enabled = True

        Me.RyName.Select()

    End Sub

    Private Sub Data_Show(ByVal tmp_Row As DataRow)
        My_Row = tmp_Row
        With My_Row

            TestCode.Text = .Item("Test_Code") & ""
            RyName.Text = .Item("Ry_Name") & ""
            RySex.Text = .Item("Ry_Sex") & ""
            RySfzh.Text = .Item("Ry_Sfzh") & ""
            JkkNo.Text = .Item("JkkNo") & ""
            RyAge.Text = .Item("Ry_Age")
            RyAgeMonth.Text = .Item("Ry_Age_Month")
            RyAgeWeek.Text = .Item("Ry_Age_Week")
            SendJsr.Text = .Item("SendDoctor") & ""
            TestXm.SelectedValue = .Item("TestXm_code") & ""
        End With
        Me.RyName.Select()

    End Sub
#End Region


#Region "数据__编辑"

    Private Sub Data_Add(ByVal str As String)
        Dim My_NewRow As DataRow
        If V_Insert = True Then
            My_NewRow = My_Table.NewRow
        Else
            My_NewRow = My_Row
        End If
        With My_NewRow
            If V_Insert = True Then
                .Item("Test_Code") = BllLIS_Test1.MaxCode()
            Else
                .Item("Test_Code") = TestCode.Text
            End If

            .Item("Ry_Name") = Trim(RyName.Text & "")
            .Item("Ry_Sex") = Trim(RySex.Text & "")
            .Item("Ry_Sfzh") = Trim(RySfzh.Text & "")
            .Item("JkkNo") = Trim(JkkNo.Text & "")
            .Item("Ry_Age") = RyAge.Text
            .Item("Ry_Age_Month") = RyAgeMonth.Text
            .Item("Ry_Age_Week") = RyAgeWeek.Text
            .Item("SendDoctor") = Trim(SendJsr.Columns("ys_name").Value & "")
            .Item("TestXm_code") = Trim(TestXm.SelectedValue & "")
            .Item("TestState") = str
            .Item("Test_Lb") = "体检"
            .Item("SendJsr") = Trim(SendJsr.SelectedValue & "")
            .Item("SendTime") = Now

        End With
        '数据保存
        Try
            '
            If V_Insert = True Then
                My_Table.Rows.Add(My_NewRow)
                HisVar.HisVar.Sqldal.ExecuteSql("Insert into LIS_Test1 (Test_Code,Test_Lb,His_Code,Ry_Name,Ry_Sfzh,Ry_Sex,JKkNo,Ry_Age,Ry_Age_Month,Ry_Age_Week,SendJsr,SendDoctor,SendTime,TestXm_code,Bxlb_Code,Ks_Code,Bc_Code,TestState) values ('" & My_NewRow.Item("Test_Code") & "','" & My_NewRow.Item("Test_Lb") & "','','" & My_NewRow.Item("Ry_Name") & "','" & My_NewRow.Item("Ry_Sfzh") & "','" & My_NewRow.Item("Ry_Sex") & "','" & My_NewRow.Item("JKkNo") & "','" & My_NewRow.Item("Ry_Age") & "','" & My_NewRow.Item("Ry_Age_Month") & "','" & My_NewRow.Item("Ry_Age_Week") & "','" & My_NewRow.Item("SendJsr") & "','" & My_NewRow.Item("SendDoctor") & "','" & My_NewRow.Item("sendtime") & "','" & My_NewRow.Item("TestXm_code") & "',null,null,null,'" & My_NewRow.Item("TestState") & "')")
            Else
                HisVar.HisVar.Sqldal.ExecuteSql("Update LIS_Test1 set  Ry_Name='" & My_NewRow.Item("Ry_Name") & "',Ry_Sfzh='" & My_NewRow.Item("Ry_Sfzh") & "',Ry_Sex='" & My_NewRow.Item("Ry_Sex") & "',JKkNo='" & My_NewRow.Item("JKkNo") & "',Ry_Age='" & My_NewRow.Item("Ry_Age") & "',Ry_Age_Month='" & My_NewRow.Item("Ry_Age_Month") & "',Ry_Age_Week='" & My_NewRow.Item("Ry_Age_Week") & "',SendJsr='" & My_NewRow.Item("SendJsr") & "',SendDoctor='" & My_NewRow.Item("SendDoctor") & "',SendTime='" & My_NewRow.Item("sendtime") & "',TestXm_code='" & My_NewRow.Item("TestXm_code") & "',TestState='" & My_NewRow.Item("TestState") & "' where Test_Code='" & TestCode.Text & "'")
            End If
            My_NewRow.AcceptChanges()
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            Exit Sub
        Finally
            Me.RyName.Select()
        End Try

        '数据清空
        Call Data_Clear()

    End Sub

    

#End Region

#Region "输入法设置"
    '中文
    Private Sub C1TextBox7_GotFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles RyName.GotFocus
        InputLanguage.CurrentInputLanguage = HisVar.HisVar.InputCode
    End Sub

    Private Sub C1Numeric1_KeyDown(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles RySfzh.GotFocus, TestXm.GotFocus, JkkNo.GotFocus
        InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages(0)
    End Sub

#End Region


End Class