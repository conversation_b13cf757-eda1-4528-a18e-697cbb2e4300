﻿using System;
namespace ModelOld
{
    /// <summary>
    /// M_Zd_Ml_Xm3:实体类(属性说明自动提取数据库字段的描述信息)
    /// </summary>
    [Serializable]
    public partial class M_Zd_Ml_Xm3
    {
        public M_Zd_Ml_Xm3()
        { }
        #region Model
        private string _xmlb_code;
        private string _xm_code;
        private string _xm_name;
        private string _xm_jc;
        private string _xm_dw;
        private decimal? _xm_dj;
        private string _xm_memo;
        /// <summary>
        /// 
        /// </summary>
        public string Xmlb_Code
        {
            set { _xmlb_code = value; }
            get { return _xmlb_code; }
        }
        /// <summary>
        /// 项目编码
        /// </summary>
        public string Xm_Code
        {
            set { _xm_code = value; }
            get { return _xm_code; }
        }
        /// <summary>
        /// 项目名称
        /// </summary>
        public string Xm_Name
        {
            set { _xm_name = value; }
            get { return _xm_name; }
        }
        /// <summary>
        /// 项目简称
        /// </summary>
        public string Xm_Jc
        {
            set { _xm_jc = value; }
            get { return _xm_jc; }
        }
        /// <summary>
        /// 单位
        /// </summary>
        public string Xm_Dw
        {
            set { _xm_dw = value; }
            get { return _xm_dw; }
        }
        /// <summary>
        /// 单价
        /// </summary>
        public decimal? Xm_Dj
        {
            set { _xm_dj = value; }
            get { return _xm_dj; }
        }
        /// <summary>
        /// 备注
        /// </summary>
        public string Xm_Memo
        {
            set { _xm_memo = value; }
            get { return _xm_memo; }
        }
        #endregion Model

    }
}

