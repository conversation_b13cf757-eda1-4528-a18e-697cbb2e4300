﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class JYYQGLDic12
    Inherits HisControl.BaseChild

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(JYYQGLDic12))
        Me.Label3 = New C1.Win.C1Input.C1Label()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.Button2 = New CustomControl.MyButton()
        Me.Button1 = New CustomControl.MyButton()
        Me.ToolBar1 = New C1.Win.C1Command.C1ToolBar()
        Me.Link6 = New C1.Win.C1Command.C1CommandLink()
        Me.Control1 = New C1.Win.C1Command.C1CommandControl()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.Link1 = New C1.Win.C1Command.C1CommandLink()
        Me.Move1 = New C1.Win.C1Command.C1Command()
        Me.Link2 = New C1.Win.C1Command.C1CommandLink()
        Me.Move2 = New C1.Win.C1Command.C1Command()
        Me.Link7 = New C1.Win.C1Command.C1CommandLink()
        Me.Control2 = New C1.Win.C1Command.C1CommandControl()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.Link3 = New C1.Win.C1Command.C1CommandLink()
        Me.Move3 = New C1.Win.C1Command.C1Command()
        Me.Link4 = New C1.Win.C1Command.C1CommandLink()
        Me.Move4 = New C1.Win.C1Command.C1Command()
        Me.Link5 = New C1.Win.C1Command.C1CommandLink()
        Me.Move5 = New C1.Win.C1Command.C1Command()
        Me.C1CommandHolder1 = New C1.Win.C1Command.C1CommandHolder()
        Me.Comm1 = New C1.Win.C1Command.C1Command()
        Me.Comm3 = New C1.Win.C1Command.C1Command()
        Me.Comm2 = New C1.Win.C1Command.C1Command()
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel()
        Me.T_Line1 = New System.Windows.Forms.Label()
        Me.T_Line2 = New System.Windows.Forms.Label()
        Me.YqJcTb = New CustomControl.MyTextBox()
        Me.YqNameTb = New CustomControl.MyTextBox()
        Me.YqCodeTb = New CustomControl.MyTextBox()
        Me.YqMemoTb = New CustomControl.MyTextBox()
        Me.YqFileNameTb = New CustomControl.MyTextBox()
        Me.YqFileNameBt = New CustomControl.MyButton()
        Me.YqFileTb = New CustomControl.MyTextBox()
        Me.YqFileBt = New CustomControl.MyButton()
        Me.YqIPTb = New CustomControl.MyTextBox()
        Me.YqPortTb = New CustomControl.MyTextBox()
        Me.YqComPortTb = New CustomControl.MyTextBox()
        Me.YqBaudRateCb = New CustomControl.MySingleComobo()
        Me.YqDataBitsCb = New CustomControl.MySingleComobo()
        Me.YqModelTb = New CustomControl.MyTextBox()
        Me.YqManufacturerTb = New CustomControl.MyTextBox()
        Me.YqLbCb = New CustomControl.MyDtComobo()
        Me.YqKsCb = New CustomControl.MyDtComobo()
        Me.YqLxCb = New CustomControl.MyDtComobo()
        Me.YqCheckBitsCb = New CustomControl.MySingleComobo()
        Me.YqStopBitsCb = New CustomControl.MySingleComobo()
        Me.YqInterfaceCb = New CustomControl.MySingleComobo()
        Me.Label1 = New System.Windows.Forms.Label()
        CType(Me.Label3,System.ComponentModel.ISupportInitialize).BeginInit
        Me.Panel1.SuspendLayout
        Me.ToolBar1.SuspendLayout
        CType(Me.C1CommandHolder1,System.ComponentModel.ISupportInitialize).BeginInit
        Me.TableLayoutPanel1.SuspendLayout
        Me.SuspendLayout
        '
        'Label3
        '
        Me.Label3.Anchor = System.Windows.Forms.AnchorStyles.Bottom
        Me.Label3.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.Label3.Location = New System.Drawing.Point(220, 8)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(35, 15)
        Me.Label3.TabIndex = 4
        Me.Label3.Tag = Nothing
        Me.Label3.Text = "Σ=1"
        Me.Label3.TextAlign = System.Drawing.ContentAlignment.BottomCenter
        Me.Label3.TextDetached = true
        '
        'Panel1
        '
        Me.Panel1.Controls.Add(Me.Label3)
        Me.Panel1.Controls.Add(Me.Button2)
        Me.Panel1.Controls.Add(Me.Button1)
        Me.Panel1.Controls.Add(Me.ToolBar1)
        Me.Panel1.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.Panel1.Location = New System.Drawing.Point(0, 360)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(636, 29)
        Me.Panel1.TabIndex = 105
        '
        'Button2
        '
        Me.Button2.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.Button2.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.Button2.DialogResult = System.Windows.Forms.DialogResult.Cancel
        Me.Button2.Location = New System.Drawing.Point(551, 1)
        Me.Button2.Name = "Button2"
        Me.Button2.Size = New System.Drawing.Size(67, 27)
        Me.Button2.TabIndex = 1
        Me.Button2.Tag = "取消"
        Me.Button2.Text = "取消"
        '
        'Button1
        '
        Me.Button1.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.Button1.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.Button1.DialogResult = System.Windows.Forms.DialogResult.None
        Me.Button1.Location = New System.Drawing.Point(477, 1)
        Me.Button1.Name = "Button1"
        Me.Button1.Size = New System.Drawing.Size(67, 27)
        Me.Button1.TabIndex = 0
        Me.Button1.Tag = "保存"
        Me.Button1.Text = "保存"
        '
        'ToolBar1
        '
        Me.ToolBar1.AccessibleName = "Tool Bar"
        Me.ToolBar1.BackColor = System.Drawing.Color.Transparent
        Me.ToolBar1.CommandHolder = Nothing
        Me.ToolBar1.CommandLinks.AddRange(New C1.Win.C1Command.C1CommandLink() {Me.Link6, Me.Link1, Me.Link2, Me.Link7, Me.Link3, Me.Link4, Me.Link5})
        Me.ToolBar1.Controls.Add(Me.Label2)
        Me.ToolBar1.Controls.Add(Me.Label4)
        Me.ToolBar1.Location = New System.Drawing.Point(1, 3)
        Me.ToolBar1.MinButtonSize = 22
        Me.ToolBar1.Movable = false
        Me.ToolBar1.Name = "ToolBar1"
        Me.ToolBar1.Size = New System.Drawing.Size(207, 22)
        Me.ToolBar1.Text = "C1ToolBar2"
        Me.ToolBar1.VisualStyle = C1.Win.C1Command.VisualStyle.Custom
        Me.ToolBar1.VisualStyleBase = C1.Win.C1Command.VisualStyle.Classic
        '
        'Link6
        '
        Me.Link6.Command = Me.Control1
        '
        'Control1
        '
        Me.Control1.Control = Me.Label4
        Me.Control1.HotFrame = C1.Win.C1Command.HotFrameEnum.None
        Me.Control1.Name = "Control1"
        Me.Control1.ShortcutText = ""
        Me.Control1.ShowShortcut = false
        Me.Control1.ShowTextAsToolTip = false
        '
        'Label4
        '
        Me.Label4.Anchor = System.Windows.Forms.AnchorStyles.Bottom
        Me.Label4.AutoSize = true
        Me.Label4.BackColor = System.Drawing.Color.Transparent
        Me.Label4.ImageAlign = System.Drawing.ContentAlignment.BottomCenter
        Me.Label4.Location = New System.Drawing.Point(1, 5)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(29, 12)
        Me.Label4.TabIndex = 159
        Me.Label4.Text = "记录"
        Me.Label4.TextAlign = System.Drawing.ContentAlignment.BottomCenter
        '
        'Link1
        '
        Me.Link1.Command = Me.Move1
        Me.Link1.Delimiter = true
        Me.Link1.SortOrder = 1
        '
        'Move1
        '
        Me.Move1.Image = CType(resources.GetObject("Move1.Image"),System.Drawing.Image)
        Me.Move1.Name = "Move1"
        Me.Move1.ShortcutText = ""
        Me.Move1.Text = "最前"
        Me.Move1.ToolTipText = "移至最前记录"
        '
        'Link2
        '
        Me.Link2.Command = Me.Move2
        Me.Link2.SortOrder = 2
        Me.Link2.Text = "上移"
        Me.Link2.ToolTipText = "上移记录"
        '
        'Move2
        '
        Me.Move2.Image = CType(resources.GetObject("Move2.Image"),System.Drawing.Image)
        Me.Move2.Name = "Move2"
        Me.Move2.ShortcutText = ""
        Me.Move2.Text = "上移"
        Me.Move2.ToolTipText = "上移记录"
        '
        'Link7
        '
        Me.Link7.Command = Me.Control2
        Me.Link7.SortOrder = 3
        '
        'Control2
        '
        Me.Control2.Control = Me.Label2
        Me.Control2.HotFrame = C1.Win.C1Command.HotFrameEnum.None
        Me.Control2.Name = "Control2"
        Me.Control2.ShortcutText = ""
        Me.Control2.ShowShortcut = false
        Me.Control2.ShowTextAsToolTip = false
        '
        'Label2
        '
        Me.Label2.Anchor = System.Windows.Forms.AnchorStyles.Bottom
        Me.Label2.AutoSize = true
        Me.Label2.BackColor = System.Drawing.Color.Transparent
        Me.Label2.ImageAlign = System.Drawing.ContentAlignment.BottomCenter
        Me.Label2.Location = New System.Drawing.Point(80, 5)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(23, 12)
        Me.Label2.TabIndex = 159
        Me.Label2.Text = "(1)"
        Me.Label2.TextAlign = System.Drawing.ContentAlignment.BottomCenter
        '
        'Link3
        '
        Me.Link3.Command = Me.Move3
        Me.Link3.SortOrder = 4
        '
        'Move3
        '
        Me.Move3.Image = CType(resources.GetObject("Move3.Image"),System.Drawing.Image)
        Me.Move3.Name = "Move3"
        Me.Move3.ShortcutText = ""
        Me.Move3.Text = "下移"
        Me.Move3.ToolTipText = "下移记录"
        '
        'Link4
        '
        Me.Link4.Command = Me.Move4
        Me.Link4.SortOrder = 5
        '
        'Move4
        '
        Me.Move4.Image = CType(resources.GetObject("Move4.Image"),System.Drawing.Image)
        Me.Move4.Name = "Move4"
        Me.Move4.ShortcutText = ""
        Me.Move4.Text = "最后"
        Me.Move4.ToolTipText = "移至最后记录"
        '
        'Link5
        '
        Me.Link5.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image),C1.Win.C1Command.ButtonLookFlags)
        Me.Link5.Command = Me.Move5
        Me.Link5.Delimiter = true
        Me.Link5.SortOrder = 6
        Me.Link5.Text = "新增"
        Me.Link5.ToolTipText = "新增记录"
        '
        'Move5
        '
        Me.Move5.Image = CType(resources.GetObject("Move5.Image"),System.Drawing.Image)
        Me.Move5.Name = "Move5"
        Me.Move5.ShortcutText = ""
        Me.Move5.Text = "新增"
        '
        'C1CommandHolder1
        '
        Me.C1CommandHolder1.Commands.Add(Me.Control1)
        Me.C1CommandHolder1.Commands.Add(Me.Move1)
        Me.C1CommandHolder1.Commands.Add(Me.Move2)
        Me.C1CommandHolder1.Commands.Add(Me.Control2)
        Me.C1CommandHolder1.Commands.Add(Me.Move3)
        Me.C1CommandHolder1.Commands.Add(Me.Move4)
        Me.C1CommandHolder1.Commands.Add(Me.Move5)
        Me.C1CommandHolder1.Commands.Add(Me.Comm1)
        Me.C1CommandHolder1.Commands.Add(Me.Comm3)
        Me.C1CommandHolder1.Commands.Add(Me.Comm2)
        Me.C1CommandHolder1.Owner = Me
        '
        'Comm1
        '
        Me.Comm1.Name = "Comm1"
        Me.Comm1.ShortcutText = ""
        Me.Comm1.Text = "增加"
        Me.Comm1.ToolTipText = "增加记录"
        '
        'Comm3
        '
        Me.Comm3.Name = "Comm3"
        Me.Comm3.ShortcutText = ""
        Me.Comm3.Text = "刷新"
        '
        'Comm2
        '
        Me.Comm2.Name = "Comm2"
        Me.Comm2.ShortcutText = ""
        Me.Comm2.Text = "删除"
        Me.Comm2.ToolTipText = "删除记录"
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.ColumnCount = 7
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 20!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 200!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 200!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 160!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 40!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 200!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle())
        Me.TableLayoutPanel1.Controls.Add(Me.T_Line1, 0, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.T_Line2, 0, 12)
        Me.TableLayoutPanel1.Controls.Add(Me.YqJcTb, 2, 3)
        Me.TableLayoutPanel1.Controls.Add(Me.YqNameTb, 1, 3)
        Me.TableLayoutPanel1.Controls.Add(Me.YqCodeTb, 1, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.YqMemoTb, 1, 11)
        Me.TableLayoutPanel1.Controls.Add(Me.YqFileNameTb, 1, 10)
        Me.TableLayoutPanel1.Controls.Add(Me.YqFileNameBt, 4, 10)
        Me.TableLayoutPanel1.Controls.Add(Me.YqFileTb, 1, 9)
        Me.TableLayoutPanel1.Controls.Add(Me.YqFileBt, 4, 9)
        Me.TableLayoutPanel1.Controls.Add(Me.YqIPTb, 1, 8)
        Me.TableLayoutPanel1.Controls.Add(Me.YqPortTb, 2, 8)
        Me.TableLayoutPanel1.Controls.Add(Me.YqComPortTb, 1, 6)
        Me.TableLayoutPanel1.Controls.Add(Me.YqBaudRateCb, 2, 6)
        Me.TableLayoutPanel1.Controls.Add(Me.YqDataBitsCb, 3, 6)
        Me.TableLayoutPanel1.Controls.Add(Me.YqModelTb, 3, 3)
        Me.TableLayoutPanel1.Controls.Add(Me.YqManufacturerTb, 1, 4)
        Me.TableLayoutPanel1.Controls.Add(Me.YqLbCb, 2, 4)
        Me.TableLayoutPanel1.Controls.Add(Me.YqKsCb, 1, 5)
        Me.TableLayoutPanel1.Controls.Add(Me.YqLxCb, 3, 4)
        Me.TableLayoutPanel1.Controls.Add(Me.YqInterfaceCb, 2, 5)
        Me.TableLayoutPanel1.Controls.Add(Me.YqStopBitsCb, 1, 7)
        Me.TableLayoutPanel1.Controls.Add(Me.YqCheckBitsCb, 2, 7)
        Me.TableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(0, 2)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.RowCount = 14
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 10!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 5!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 90!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 5!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle())
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 20!))
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(636, 387)
        Me.TableLayoutPanel1.TabIndex = 0
        '
        'T_Line1
        '
        Me.T_Line1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.T_Line1.BackColor = System.Drawing.SystemColors.Control
        Me.T_Line1.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.TableLayoutPanel1.SetColumnSpan(Me.T_Line1, 7)
        Me.T_Line1.Location = New System.Drawing.Point(3, 39)
        Me.T_Line1.Name = "T_Line1"
        Me.T_Line1.Size = New System.Drawing.Size(833, 2)
        Me.T_Line1.TabIndex = 133
        Me.T_Line1.Text = "Label1"
        '
        'T_Line2
        '
        Me.T_Line2.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.T_Line2.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.TableLayoutPanel1.SetColumnSpan(Me.T_Line2, 7)
        Me.T_Line2.Location = New System.Drawing.Point(3, 358)
        Me.T_Line2.Name = "T_Line2"
        Me.T_Line2.Size = New System.Drawing.Size(833, 2)
        Me.T_Line2.TabIndex = 158
        '
        'YqJcTb
        '
        Me.YqJcTb.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.YqJcTb.Captain = "简    称"
        Me.YqJcTb.CaptainBackColor = System.Drawing.Color.Transparent
        Me.YqJcTb.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.YqJcTb.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.YqJcTb.CaptainWidth = 60!
        Me.YqJcTb.ContentForeColor = System.Drawing.Color.Black
        Me.YqJcTb.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer))
        Me.YqJcTb.EditMask = Nothing
        Me.YqJcTb.Location = New System.Drawing.Point(223, 47)
        Me.YqJcTb.Multiline = false
        Me.YqJcTb.Name = "YqJcTb"
        Me.YqJcTb.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.YqJcTb.ReadOnly = false
        Me.YqJcTb.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.YqJcTb.SelectionStart = 0
        Me.YqJcTb.SelectStart = 0
        Me.YqJcTb.Size = New System.Drawing.Size(194, 20)
        Me.YqJcTb.TabIndex = 1
        Me.YqJcTb.TextAlign = System.Windows.Forms.HorizontalAlignment.Left
        Me.YqJcTb.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.YqJcTb.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.YqJcTb.Watermark = Nothing
        '
        'YqNameTb
        '
        Me.YqNameTb.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.YqNameTb.Captain = "仪器名称"
        Me.YqNameTb.CaptainBackColor = System.Drawing.Color.Transparent
        Me.YqNameTb.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.YqNameTb.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.YqNameTb.CaptainWidth = 60!
        Me.YqNameTb.ContentForeColor = System.Drawing.Color.Black
        Me.YqNameTb.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer))
        Me.YqNameTb.EditMask = Nothing
        Me.YqNameTb.Location = New System.Drawing.Point(23, 47)
        Me.YqNameTb.Multiline = false
        Me.YqNameTb.Name = "YqNameTb"
        Me.YqNameTb.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.YqNameTb.ReadOnly = false
        Me.YqNameTb.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.YqNameTb.SelectionStart = 0
        Me.YqNameTb.SelectStart = 0
        Me.YqNameTb.Size = New System.Drawing.Size(194, 20)
        Me.YqNameTb.TabIndex = 0
        Me.YqNameTb.TextAlign = System.Windows.Forms.HorizontalAlignment.Left
        Me.YqNameTb.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.YqNameTb.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.YqNameTb.Watermark = Nothing
        '
        'YqCodeTb
        '
        Me.YqCodeTb.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.YqCodeTb.Captain = "仪器编号"
        Me.YqCodeTb.CaptainBackColor = System.Drawing.Color.Transparent
        Me.YqCodeTb.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.YqCodeTb.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.YqCodeTb.CaptainWidth = 60!
        Me.YqCodeTb.ContentForeColor = System.Drawing.Color.Black
        Me.YqCodeTb.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer))
        Me.YqCodeTb.EditMask = Nothing
        Me.YqCodeTb.Location = New System.Drawing.Point(23, 14)
        Me.YqCodeTb.Multiline = false
        Me.YqCodeTb.Name = "YqCodeTb"
        Me.YqCodeTb.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.YqCodeTb.ReadOnly = false
        Me.YqCodeTb.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.YqCodeTb.SelectionStart = 0
        Me.YqCodeTb.SelectStart = 0
        Me.YqCodeTb.Size = New System.Drawing.Size(194, 20)
        Me.YqCodeTb.TabIndex = 0
        Me.YqCodeTb.TextAlign = System.Windows.Forms.HorizontalAlignment.Left
        Me.YqCodeTb.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.YqCodeTb.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.YqCodeTb.Watermark = Nothing
        '
        'YqMemoTb
        '
        Me.YqMemoTb.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom)  _
            Or System.Windows.Forms.AnchorStyles.Left)  _
            Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.YqMemoTb.Captain = "备    注"
        Me.YqMemoTb.CaptainBackColor = System.Drawing.Color.Transparent
        Me.YqMemoTb.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.YqMemoTb.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.YqMemoTb.CaptainWidth = 60!
        Me.TableLayoutPanel1.SetColumnSpan(Me.YqMemoTb, 4)
        Me.YqMemoTb.ContentForeColor = System.Drawing.Color.Black
        Me.YqMemoTb.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer))
        Me.YqMemoTb.EditMask = Nothing
        Me.YqMemoTb.Location = New System.Drawing.Point(23, 270)
        Me.YqMemoTb.Multiline = true
        Me.YqMemoTb.Name = "YqMemoTb"
        Me.YqMemoTb.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.YqMemoTb.ReadOnly = false
        Me.YqMemoTb.ScrollBars = System.Windows.Forms.ScrollBars.Vertical
        Me.YqMemoTb.SelectionStart = 0
        Me.YqMemoTb.SelectStart = 0
        Me.YqMemoTb.Size = New System.Drawing.Size(594, 84)
        Me.YqMemoTb.TabIndex = 16
        Me.YqMemoTb.TextAlign = System.Windows.Forms.HorizontalAlignment.Left
        Me.YqMemoTb.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.YqMemoTb.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Top
        Me.YqMemoTb.Watermark = Nothing
        '
        'YqFileNameTb
        '
        Me.YqFileNameTb.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.YqFileNameTb.Captain = "接口程序名"
        Me.YqFileNameTb.CaptainBackColor = System.Drawing.Color.Transparent
        Me.YqFileNameTb.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.YqFileNameTb.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.YqFileNameTb.CaptainWidth = 100!
        Me.TableLayoutPanel1.SetColumnSpan(Me.YqFileNameTb, 3)
        Me.YqFileNameTb.ContentForeColor = System.Drawing.Color.Black
        Me.YqFileNameTb.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer))
        Me.YqFileNameTb.EditMask = Nothing
        Me.YqFileNameTb.Location = New System.Drawing.Point(23, 243)
        Me.YqFileNameTb.Multiline = false
        Me.YqFileNameTb.Name = "YqFileNameTb"
        Me.YqFileNameTb.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.YqFileNameTb.ReadOnly = false
        Me.YqFileNameTb.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.YqFileNameTb.SelectionStart = 0
        Me.YqFileNameTb.SelectStart = 0
        Me.YqFileNameTb.Size = New System.Drawing.Size(554, 20)
        Me.YqFileNameTb.TabIndex = 16
        Me.YqFileNameTb.TextAlign = System.Windows.Forms.HorizontalAlignment.Left
        Me.YqFileNameTb.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.YqFileNameTb.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.YqFileNameTb.Watermark = Nothing
        '
        'YqFileNameBt
        '
        Me.YqFileNameBt.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.YqFileNameBt.DialogResult = System.Windows.Forms.DialogResult.None
        Me.YqFileNameBt.Location = New System.Drawing.Point(583, 242)
        Me.YqFileNameBt.Name = "YqFileNameBt"
        Me.YqFileNameBt.Size = New System.Drawing.Size(34, 22)
        Me.YqFileNameBt.TabIndex = 15
        Me.YqFileNameBt.Tag = "文件名"
        Me.YqFileNameBt.Text = "..."
        '
        'YqFileTb
        '
        Me.YqFileTb.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.YqFileTb.Captain = "文件提取路径"
        Me.YqFileTb.CaptainBackColor = System.Drawing.Color.Transparent
        Me.YqFileTb.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.YqFileTb.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.YqFileTb.CaptainWidth = 100!
        Me.TableLayoutPanel1.SetColumnSpan(Me.YqFileTb, 3)
        Me.YqFileTb.ContentForeColor = System.Drawing.Color.Black
        Me.YqFileTb.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer))
        Me.YqFileTb.EditMask = Nothing
        Me.YqFileTb.Location = New System.Drawing.Point(23, 215)
        Me.YqFileTb.Multiline = false
        Me.YqFileTb.Name = "YqFileTb"
        Me.YqFileTb.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.YqFileTb.ReadOnly = false
        Me.YqFileTb.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.YqFileTb.SelectionStart = 0
        Me.YqFileTb.SelectStart = 0
        Me.YqFileTb.Size = New System.Drawing.Size(554, 20)
        Me.YqFileTb.TabIndex = 14
        Me.YqFileTb.TextAlign = System.Windows.Forms.HorizontalAlignment.Left
        Me.YqFileTb.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.YqFileTb.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.YqFileTb.Watermark = Nothing
        '
        'YqFileBt
        '
        Me.YqFileBt.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.YqFileBt.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.YqFileBt.DialogResult = System.Windows.Forms.DialogResult.None
        Me.YqFileBt.Location = New System.Drawing.Point(583, 214)
        Me.YqFileBt.Name = "YqFileBt"
        Me.YqFileBt.Size = New System.Drawing.Size(34, 22)
        Me.YqFileBt.TabIndex = 14
        Me.YqFileBt.Tag = "全路径"
        Me.YqFileBt.Text = "..."
        '
        'YqIPTb
        '
        Me.YqIPTb.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.YqIPTb.Captain = "通 讯 IP"
        Me.YqIPTb.CaptainBackColor = System.Drawing.Color.Transparent
        Me.YqIPTb.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.YqIPTb.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.YqIPTb.CaptainWidth = 60!
        Me.YqIPTb.ContentForeColor = System.Drawing.Color.Black
        Me.YqIPTb.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer))
        Me.YqIPTb.EditMask = Nothing
        Me.YqIPTb.Location = New System.Drawing.Point(23, 187)
        Me.YqIPTb.Multiline = false
        Me.YqIPTb.Name = "YqIPTb"
        Me.YqIPTb.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.YqIPTb.ReadOnly = false
        Me.YqIPTb.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.YqIPTb.SelectionStart = 0
        Me.YqIPTb.SelectStart = 0
        Me.YqIPTb.Size = New System.Drawing.Size(194, 20)
        Me.YqIPTb.TabIndex = 12
        Me.YqIPTb.TextAlign = System.Windows.Forms.HorizontalAlignment.Left
        Me.YqIPTb.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.YqIPTb.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.YqIPTb.Watermark = Nothing
        '
        'YqPortTb
        '
        Me.YqPortTb.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.YqPortTb.Captain = "通讯端口"
        Me.YqPortTb.CaptainBackColor = System.Drawing.Color.Transparent
        Me.YqPortTb.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.YqPortTb.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.YqPortTb.CaptainWidth = 60!
        Me.YqPortTb.ContentForeColor = System.Drawing.Color.Black
        Me.YqPortTb.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer))
        Me.YqPortTb.EditMask = Nothing
        Me.YqPortTb.Location = New System.Drawing.Point(223, 187)
        Me.YqPortTb.Multiline = false
        Me.YqPortTb.Name = "YqPortTb"
        Me.YqPortTb.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.YqPortTb.ReadOnly = false
        Me.YqPortTb.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.YqPortTb.SelectionStart = 0
        Me.YqPortTb.SelectStart = 0
        Me.YqPortTb.Size = New System.Drawing.Size(194, 20)
        Me.YqPortTb.TabIndex = 13
        Me.YqPortTb.TextAlign = System.Windows.Forms.HorizontalAlignment.Left
        Me.YqPortTb.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.YqPortTb.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.YqPortTb.Watermark = Nothing
        '
        'YqComPortTb
        '
        Me.YqComPortTb.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.YqComPortTb.Captain = "通 讯 口"
        Me.YqComPortTb.CaptainBackColor = System.Drawing.Color.Transparent
        Me.YqComPortTb.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.YqComPortTb.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.YqComPortTb.CaptainWidth = 60!
        Me.YqComPortTb.ContentForeColor = System.Drawing.Color.Black
        Me.YqComPortTb.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer))
        Me.YqComPortTb.EditMask = Nothing
        Me.YqComPortTb.Location = New System.Drawing.Point(23, 131)
        Me.YqComPortTb.Multiline = false
        Me.YqComPortTb.Name = "YqComPortTb"
        Me.YqComPortTb.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.YqComPortTb.ReadOnly = false
        Me.YqComPortTb.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.YqComPortTb.SelectionStart = 0
        Me.YqComPortTb.SelectStart = 0
        Me.YqComPortTb.Size = New System.Drawing.Size(194, 20)
        Me.YqComPortTb.TabIndex = 7
        Me.YqComPortTb.TextAlign = System.Windows.Forms.HorizontalAlignment.Left
        Me.YqComPortTb.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.YqComPortTb.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.YqComPortTb.Watermark = Nothing
        '
        'YqBaudRateCb
        '
        Me.YqBaudRateCb.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.YqBaudRateCb.Captain = "波 特 率"
        Me.YqBaudRateCb.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.YqBaudRateCb.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.YqBaudRateCb.CaptainWidth = 60!
        Me.YqBaudRateCb.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        Me.YqBaudRateCb.ItemHeight = 16
        Me.YqBaudRateCb.ItemTextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.YqBaudRateCb.Location = New System.Drawing.Point(223, 131)
        Me.YqBaudRateCb.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.YqBaudRateCb.MinimumSize = New System.Drawing.Size(0, 20)
        Me.YqBaudRateCb.Name = "YqBaudRateCb"
        Me.YqBaudRateCb.ReadOnly = false
        Me.YqBaudRateCb.Size = New System.Drawing.Size(194, 20)
        Me.YqBaudRateCb.TabIndex = 8
        Me.YqBaudRateCb.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        '
        'YqDataBitsCb
        '
        Me.YqDataBitsCb.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.YqDataBitsCb.Captain = "数 据 位"
        Me.YqDataBitsCb.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.YqDataBitsCb.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.YqDataBitsCb.CaptainWidth = 60!
        Me.TableLayoutPanel1.SetColumnSpan(Me.YqDataBitsCb, 2)
        Me.YqDataBitsCb.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        Me.YqDataBitsCb.ItemHeight = 16
        Me.YqDataBitsCb.ItemTextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.YqDataBitsCb.Location = New System.Drawing.Point(423, 131)
        Me.YqDataBitsCb.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.YqDataBitsCb.MinimumSize = New System.Drawing.Size(0, 20)
        Me.YqDataBitsCb.Name = "YqDataBitsCb"
        Me.YqDataBitsCb.ReadOnly = false
        Me.YqDataBitsCb.Size = New System.Drawing.Size(194, 20)
        Me.YqDataBitsCb.TabIndex = 9
        Me.YqDataBitsCb.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        '
        'YqModelTb
        '
        Me.YqModelTb.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.YqModelTb.Captain = "仪器型号"
        Me.YqModelTb.CaptainBackColor = System.Drawing.Color.Transparent
        Me.YqModelTb.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.YqModelTb.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.YqModelTb.CaptainWidth = 60!
        Me.TableLayoutPanel1.SetColumnSpan(Me.YqModelTb, 2)
        Me.YqModelTb.ContentForeColor = System.Drawing.Color.Black
        Me.YqModelTb.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer))
        Me.YqModelTb.EditMask = Nothing
        Me.YqModelTb.Location = New System.Drawing.Point(423, 47)
        Me.YqModelTb.Multiline = false
        Me.YqModelTb.Name = "YqModelTb"
        Me.YqModelTb.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.YqModelTb.ReadOnly = false
        Me.YqModelTb.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.YqModelTb.SelectionStart = 0
        Me.YqModelTb.SelectStart = 0
        Me.YqModelTb.Size = New System.Drawing.Size(194, 20)
        Me.YqModelTb.TabIndex = 1
        Me.YqModelTb.TextAlign = System.Windows.Forms.HorizontalAlignment.Left
        Me.YqModelTb.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.YqModelTb.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.YqModelTb.Watermark = Nothing
        '
        'YqManufacturerTb
        '
        Me.YqManufacturerTb.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.YqManufacturerTb.Captain = "生产厂家"
        Me.YqManufacturerTb.CaptainBackColor = System.Drawing.Color.Transparent
        Me.YqManufacturerTb.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.YqManufacturerTb.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.YqManufacturerTb.CaptainWidth = 60!
        Me.YqManufacturerTb.ContentForeColor = System.Drawing.Color.Black
        Me.YqManufacturerTb.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer))
        Me.YqManufacturerTb.EditMask = Nothing
        Me.YqManufacturerTb.Location = New System.Drawing.Point(23, 75)
        Me.YqManufacturerTb.Multiline = false
        Me.YqManufacturerTb.Name = "YqManufacturerTb"
        Me.YqManufacturerTb.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.YqManufacturerTb.ReadOnly = false
        Me.YqManufacturerTb.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.YqManufacturerTb.SelectionStart = 0
        Me.YqManufacturerTb.SelectStart = 0
        Me.YqManufacturerTb.Size = New System.Drawing.Size(194, 20)
        Me.YqManufacturerTb.TabIndex = 2
        Me.YqManufacturerTb.TextAlign = System.Windows.Forms.HorizontalAlignment.Left
        Me.YqManufacturerTb.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.YqManufacturerTb.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.YqManufacturerTb.Watermark = Nothing
        '
        'YqLbCb
        '
        Me.YqLbCb.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.YqLbCb.Captain = "仪器类别"
        Me.YqLbCb.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.YqLbCb.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.YqLbCb.CaptainWidth = 60!
        Me.YqLbCb.DataSource = Nothing
        Me.YqLbCb.ItemHeight = 18
        Me.YqLbCb.ItemTextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.YqLbCb.Location = New System.Drawing.Point(223, 75)
        Me.YqLbCb.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.YqLbCb.MinimumSize = New System.Drawing.Size(0, 20)
        Me.YqLbCb.Name = "YqLbCb"
        Me.YqLbCb.ReadOnly = false
        Me.YqLbCb.Size = New System.Drawing.Size(194, 20)
        Me.YqLbCb.TabIndex = 3
        Me.YqLbCb.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        '
        'YqKsCb
        '
        Me.YqKsCb.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.YqKsCb.Captain = "使用科室"
        Me.YqKsCb.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.YqKsCb.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.YqKsCb.CaptainWidth = 60!
        Me.YqKsCb.DataSource = Nothing
        Me.YqKsCb.ItemHeight = 18
        Me.YqKsCb.ItemTextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.YqKsCb.Location = New System.Drawing.Point(23, 103)
        Me.YqKsCb.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.YqKsCb.MinimumSize = New System.Drawing.Size(0, 20)
        Me.YqKsCb.Name = "YqKsCb"
        Me.YqKsCb.ReadOnly = false
        Me.YqKsCb.Size = New System.Drawing.Size(194, 20)
        Me.YqKsCb.TabIndex = 5
        Me.YqKsCb.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        '
        'YqLxCb
        '
        Me.YqLxCb.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.YqLxCb.Captain = "仪器类型"
        Me.YqLxCb.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.YqLxCb.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.YqLxCb.CaptainWidth = 60!
        Me.TableLayoutPanel1.SetColumnSpan(Me.YqLxCb, 2)
        Me.YqLxCb.DataSource = Nothing
        Me.YqLxCb.ItemHeight = 18
        Me.YqLxCb.ItemTextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.YqLxCb.Location = New System.Drawing.Point(423, 75)
        Me.YqLxCb.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.YqLxCb.MinimumSize = New System.Drawing.Size(0, 20)
        Me.YqLxCb.Name = "YqLxCb"
        Me.YqLxCb.ReadOnly = false
        Me.YqLxCb.Size = New System.Drawing.Size(194, 20)
        Me.YqLxCb.TabIndex = 4
        Me.YqLxCb.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        '
        'YqCheckBitsCb
        '
        Me.YqCheckBitsCb.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.YqCheckBitsCb.Captain = "检 验 位"
        Me.YqCheckBitsCb.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.YqCheckBitsCb.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.YqCheckBitsCb.CaptainWidth = 60!
        Me.YqCheckBitsCb.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        Me.YqCheckBitsCb.ItemHeight = 16
        Me.YqCheckBitsCb.ItemTextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.YqCheckBitsCb.Location = New System.Drawing.Point(223, 159)
        Me.YqCheckBitsCb.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.YqCheckBitsCb.MinimumSize = New System.Drawing.Size(0, 20)
        Me.YqCheckBitsCb.Name = "YqCheckBitsCb"
        Me.YqCheckBitsCb.ReadOnly = false
        Me.YqCheckBitsCb.Size = New System.Drawing.Size(194, 20)
        Me.YqCheckBitsCb.TabIndex = 10
        Me.YqCheckBitsCb.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        '
        'YqStopBitsCb
        '
        Me.YqStopBitsCb.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.YqStopBitsCb.Captain = "停 止 位"
        Me.YqStopBitsCb.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.YqStopBitsCb.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.YqStopBitsCb.CaptainWidth = 60!
        Me.YqStopBitsCb.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        Me.YqStopBitsCb.ItemHeight = 16
        Me.YqStopBitsCb.ItemTextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.YqStopBitsCb.Location = New System.Drawing.Point(23, 159)
        Me.YqStopBitsCb.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.YqStopBitsCb.MinimumSize = New System.Drawing.Size(0, 20)
        Me.YqStopBitsCb.Name = "YqStopBitsCb"
        Me.YqStopBitsCb.ReadOnly = false
        Me.YqStopBitsCb.Size = New System.Drawing.Size(194, 20)
        Me.YqStopBitsCb.TabIndex = 11
        Me.YqStopBitsCb.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        '
        'YqInterfaceCb
        '
        Me.YqInterfaceCb.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.YqInterfaceCb.Captain = "接口类型"
        Me.YqInterfaceCb.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.YqInterfaceCb.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.YqInterfaceCb.CaptainWidth = 60!
        Me.YqInterfaceCb.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        Me.YqInterfaceCb.ItemHeight = 16
        Me.YqInterfaceCb.ItemTextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.YqInterfaceCb.Location = New System.Drawing.Point(223, 103)
        Me.YqInterfaceCb.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.YqInterfaceCb.MinimumSize = New System.Drawing.Size(0, 20)
        Me.YqInterfaceCb.Name = "YqInterfaceCb"
        Me.YqInterfaceCb.ReadOnly = false
        Me.YqInterfaceCb.Size = New System.Drawing.Size(194, 20)
        Me.YqInterfaceCb.TabIndex = 6
        Me.YqInterfaceCb.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        '
        'Label1
        '
        Me.Label1.BackColor = System.Drawing.Color.Transparent
        Me.Label1.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.Label1.Dock = System.Windows.Forms.DockStyle.Top
        Me.Label1.Location = New System.Drawing.Point(0, 0)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(636, 2)
        Me.Label1.TabIndex = 103
        Me.Label1.Text = "Label2"
        '
        'JYYQGLDic12
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6!, 12!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(636, 389)
        Me.Controls.Add(Me.Panel1)
        Me.Controls.Add(Me.TableLayoutPanel1)
        Me.Controls.Add(Me.Label1)
        Me.Name = "JYYQGLDic12"
        Me.Text = "检验仪器明细"
        CType(Me.Label3,System.ComponentModel.ISupportInitialize).EndInit
        Me.Panel1.ResumeLayout(false)
        Me.ToolBar1.ResumeLayout(false)
        Me.ToolBar1.PerformLayout
        CType(Me.C1CommandHolder1,System.ComponentModel.ISupportInitialize).EndInit
        Me.TableLayoutPanel1.ResumeLayout(false)
        Me.ResumeLayout(false)

End Sub
    Friend WithEvents Label3 As C1.Win.C1Input.C1Label
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents Button2 As CustomControl.MyButton
    Friend WithEvents Button1 As CustomControl.MyButton
    Friend WithEvents ToolBar1 As C1.Win.C1Command.C1ToolBar
    Friend WithEvents Link6 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link1 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link2 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link7 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link3 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link4 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link5 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents C1CommandHolder1 As C1.Win.C1Command.C1CommandHolder
    Friend WithEvents Comm1 As C1.Win.C1Command.C1Command
    Friend WithEvents Comm3 As C1.Win.C1Command.C1Command
    Friend WithEvents Comm2 As C1.Win.C1Command.C1Command
    Friend WithEvents TableLayoutPanel1 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents T_Line1 As System.Windows.Forms.Label
    Friend WithEvents T_Line2 As System.Windows.Forms.Label
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents YqJcTb As CustomControl.MyTextBox
    Friend WithEvents YqNameTb As CustomControl.MyTextBox
    Friend WithEvents YqCodeTb As CustomControl.MyTextBox
    Friend WithEvents YqModelTb As CustomControl.MyTextBox
    Friend WithEvents YqManufacturerTb As CustomControl.MyTextBox
    Friend WithEvents YqKsCb As CustomControl.MyDtComobo
    Friend WithEvents YqInterfaceCb As CustomControl.MySingleComobo
    Friend WithEvents YqFileTb As CustomControl.MyTextBox
    Friend WithEvents YqFileBt As CustomControl.MyButton
    Friend WithEvents YqIPTb As CustomControl.MyTextBox
    Friend WithEvents YqPortTb As CustomControl.MyTextBox
    Friend WithEvents YqFileNameTb As CustomControl.MyTextBox
    Friend WithEvents YqFileNameBt As CustomControl.MyButton
    Friend WithEvents YqMemoTb As CustomControl.MyTextBox
    Friend WithEvents YqComPortTb As CustomControl.MyTextBox
    Friend WithEvents Label4 As System.Windows.Forms.Label
    Friend WithEvents Label2 As System.Windows.Forms.Label
    Private WithEvents Move1 As C1.Win.C1Command.C1Command
    Private WithEvents Move2 As C1.Win.C1Command.C1Command
    Private WithEvents Move3 As C1.Win.C1Command.C1Command
    Private WithEvents Move4 As C1.Win.C1Command.C1Command
    Private WithEvents Move5 As C1.Win.C1Command.C1Command
    Private WithEvents Control1 As C1.Win.C1Command.C1CommandControl
    Private WithEvents Control2 As C1.Win.C1Command.C1CommandControl
    Friend WithEvents YqLxCb As CustomControl.MyDtComobo
    Friend WithEvents YqLbCb As CustomControl.MyDtComobo
    Friend WithEvents YqBaudRateCb As CustomControl.MySingleComobo
    Friend WithEvents YqDataBitsCb As CustomControl.MySingleComobo
    Friend WithEvents YqCheckBitsCb As CustomControl.MySingleComobo
    Friend WithEvents YqStopBitsCb As CustomControl.MySingleComobo
End Class
