﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class YkYf_Crk2
    Inherits HisControl.BaseForm

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(YkYf_Crk2))
        Me.C1Holder1 = New C1.Win.C1Command.C1CommandHolder()
        Me.C1Command1 = New C1.Win.C1Command.C1Command()
        Me.C1Command2 = New C1.Win.C1Command.C1Command()
        Me.C1Command3 = New C1.Win.C1Command.C1Command()
        Me.C1Command4 = New C1.Win.C1Command.C1Command()
        Me.C1Command5 = New C1.Win.C1Command.C1Command()
        Me.C1Command6 = New C1.Win.C1Command.C1Command()
        Me.Panel2 = New System.Windows.Forms.Panel()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.C1ToolBar1 = New C1.Win.C1Command.C1ToolBar()
        Me.C1CommandLink1 = New C1.Win.C1Command.C1CommandLink()
        Me.C1CommandLink2 = New C1.Win.C1Command.C1CommandLink()
        Me.C1CommandLink3 = New C1.Win.C1Command.C1CommandLink()
        Me.C1CommandLink4 = New C1.Win.C1Command.C1CommandLink()
        Me.C1CommandLink5 = New C1.Win.C1Command.C1CommandLink()
        Me.C1CommandLink6 = New C1.Win.C1Command.C1CommandLink()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.T_Label4 = New System.Windows.Forms.Label()
        Me.T_Label5 = New System.Windows.Forms.Label()
        Me.T_Line2 = New System.Windows.Forms.Label()
        Me.C1Combo2 = New C1.Win.C1List.C1Combo()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.Label12 = New System.Windows.Forms.Label()
        Me.C1TextBox1 = New C1.Win.C1Input.C1TextBox()
        Me.Label16 = New System.Windows.Forms.Label()
        Me.Label11 = New System.Windows.Forms.Label()
        Me.C1TrueDBGrid1 = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel()
        Me.CgkhCombo = New CustomControl.MyDtComobo()
        CType(Me.C1Holder1,System.ComponentModel.ISupportInitialize).BeginInit
        Me.Panel2.SuspendLayout
        CType(Me.C1Combo2,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.C1TextBox1,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.C1TrueDBGrid1,System.ComponentModel.ISupportInitialize).BeginInit
        Me.TableLayoutPanel1.SuspendLayout
        Me.SuspendLayout
        '
        'C1Holder1
        '
        Me.C1Holder1.Commands.Add(Me.C1Command1)
        Me.C1Holder1.Commands.Add(Me.C1Command2)
        Me.C1Holder1.Commands.Add(Me.C1Command3)
        Me.C1Holder1.Commands.Add(Me.C1Command4)
        Me.C1Holder1.Commands.Add(Me.C1Command5)
        Me.C1Holder1.Commands.Add(Me.C1Command6)
        Me.C1Holder1.Owner = Me
        '
        'C1Command1
        '
        Me.C1Command1.Image = CType(resources.GetObject("C1Command1.Image"),System.Drawing.Image)
        Me.C1Command1.Name = "C1Command1"
        Me.C1Command1.ShortcutText = ""
        Me.C1Command1.Text = "单据打印"
        '
        'C1Command2
        '
        Me.C1Command2.Image = CType(resources.GetObject("C1Command2.Image"),System.Drawing.Image)
        Me.C1Command2.Name = "C1Command2"
        Me.C1Command2.ShortcutText = ""
        Me.C1Command2.Text = "保存"
        '
        'C1Command3
        '
        Me.C1Command3.Image = CType(resources.GetObject("C1Command3.Image"),System.Drawing.Image)
        Me.C1Command3.Name = "C1Command3"
        Me.C1Command3.ShortcutText = ""
        Me.C1Command3.Text = "取消"
        '
        'C1Command4
        '
        Me.C1Command4.Image = CType(resources.GetObject("C1Command4.Image"),System.Drawing.Image)
        Me.C1Command4.Name = "C1Command4"
        Me.C1Command4.ShortcutText = ""
        Me.C1Command4.Text = "单据完成"
        '
        'C1Command5
        '
        Me.C1Command5.Image = CType(resources.GetObject("C1Command5.Image"),System.Drawing.Image)
        Me.C1Command5.Name = "C1Command5"
        Me.C1Command5.ShortcutText = ""
        Me.C1Command5.Text = "全部调拨"
        '
        'C1Command6
        '
        Me.C1Command6.Image = CType(resources.GetObject("C1Command6.Image"),System.Drawing.Image)
        Me.C1Command6.Name = "C1Command6"
        Me.C1Command6.ShortcutText = ""
        Me.C1Command6.Text = "查看统采药品"
        Me.C1Command6.Visible = false
        '
        'Panel2
        '
        Me.Panel2.Controls.Add(Me.Label3)
        Me.Panel2.Controls.Add(Me.C1ToolBar1)
        Me.Panel2.Controls.Add(Me.Label2)
        Me.Panel2.Controls.Add(Me.T_Label4)
        Me.Panel2.Controls.Add(Me.T_Label5)
        Me.Panel2.Controls.Add(Me.T_Line2)
        Me.Panel2.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.Panel2.Location = New System.Drawing.Point(0, 471)
        Me.Panel2.Name = "Panel2"
        Me.Panel2.Size = New System.Drawing.Size(850, 30)
        Me.Panel2.TabIndex = 37
        '
        'Label3
        '
        Me.Label3.AutoSize = true
        Me.Label3.BackColor = System.Drawing.SystemColors.ButtonFace
        Me.Label3.ForeColor = System.Drawing.Color.DarkRed
        Me.Label3.Location = New System.Drawing.Point(12, 9)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(101, 12)
        Me.Label3.TabIndex = 54
        Me.Label3.Text = "单据完成快捷键F3"
        Me.Label3.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'C1ToolBar1
        '
        Me.C1ToolBar1.AccessibleName = "Tool Bar"
        Me.C1ToolBar1.CommandHolder = Me.C1Holder1
        Me.C1ToolBar1.CommandLinks.AddRange(New C1.Win.C1Command.C1CommandLink() {Me.C1CommandLink1, Me.C1CommandLink2, Me.C1CommandLink3, Me.C1CommandLink4, Me.C1CommandLink5, Me.C1CommandLink6})
        Me.C1ToolBar1.Location = New System.Drawing.Point(224, 3)
        Me.C1ToolBar1.Name = "C1ToolBar1"
        Me.C1ToolBar1.Size = New System.Drawing.Size(475, 24)
        Me.C1ToolBar1.Text = "C1ToolBar1"
        Me.C1ToolBar1.VisualStyle = C1.Win.C1Command.VisualStyle.Classic
        Me.C1ToolBar1.VisualStyleBase = C1.Win.C1Command.VisualStyle.Classic
        '
        'C1CommandLink1
        '
        Me.C1CommandLink1.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image),C1.Win.C1Command.ButtonLookFlags)
        Me.C1CommandLink1.Command = Me.C1Command1
        '
        'C1CommandLink2
        '
        Me.C1CommandLink2.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image),C1.Win.C1Command.ButtonLookFlags)
        Me.C1CommandLink2.Command = Me.C1Command2
        Me.C1CommandLink2.SortOrder = 1
        '
        'C1CommandLink3
        '
        Me.C1CommandLink3.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image),C1.Win.C1Command.ButtonLookFlags)
        Me.C1CommandLink3.Command = Me.C1Command3
        Me.C1CommandLink3.SortOrder = 2
        '
        'C1CommandLink4
        '
        Me.C1CommandLink4.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image),C1.Win.C1Command.ButtonLookFlags)
        Me.C1CommandLink4.Command = Me.C1Command4
        Me.C1CommandLink4.SortOrder = 3
        '
        'C1CommandLink5
        '
        Me.C1CommandLink5.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image),C1.Win.C1Command.ButtonLookFlags)
        Me.C1CommandLink5.Command = Me.C1Command5
        Me.C1CommandLink5.SortOrder = 4
        '
        'C1CommandLink6
        '
        Me.C1CommandLink6.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image),C1.Win.C1Command.ButtonLookFlags)
        Me.C1CommandLink6.Command = Me.C1Command6
        Me.C1CommandLink6.SortOrder = 5
        '
        'Label2
        '
        Me.Label2.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.Label2.Location = New System.Drawing.Point(216, -5)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(2, 40)
        Me.Label2.TabIndex = 38
        '
        'T_Label4
        '
        Me.T_Label4.AutoSize = true
        Me.T_Label4.Location = New System.Drawing.Point(732, 9)
        Me.T_Label4.Name = "T_Label4"
        Me.T_Label4.Size = New System.Drawing.Size(47, 12)
        Me.T_Label4.TabIndex = 36
        Me.T_Label4.Text = "∑本日="
        Me.T_Label4.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'T_Label5
        '
        Me.T_Label5.AutoSize = true
        Me.T_Label5.BackColor = System.Drawing.SystemColors.ButtonFace
        Me.T_Label5.ForeColor = System.Drawing.Color.DarkRed
        Me.T_Label5.Location = New System.Drawing.Point(785, 9)
        Me.T_Label5.Name = "T_Label5"
        Me.T_Label5.Size = New System.Drawing.Size(53, 12)
        Me.T_Label5.TabIndex = 35
        Me.T_Label5.Text = "T_Label5"
        Me.T_Label5.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'T_Line2
        '
        Me.T_Line2.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.T_Line2.Location = New System.Drawing.Point(713, -6)
        Me.T_Line2.Name = "T_Line2"
        Me.T_Line2.Size = New System.Drawing.Size(2, 40)
        Me.T_Line2.TabIndex = 29
        '
        'C1Combo2
        '
        Me.C1Combo2.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.C1Combo2.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1Combo2.Caption = ""
        Me.C1Combo2.CaptionHeight = 17
        Me.C1Combo2.CharacterCasing = System.Windows.Forms.CharacterCasing.Normal
        Me.C1Combo2.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.C1Combo2.Images.Add(CType(resources.GetObject("C1Combo2.Images"),System.Drawing.Image))
        Me.C1Combo2.ItemHeight = 15
        Me.C1Combo2.Location = New System.Drawing.Point(443, 5)
        Me.C1Combo2.MatchEntryTimeout = CType(2000,Long)
        Me.C1Combo2.MaxDropDownItems = CType(5,Short)
        Me.C1Combo2.MaxLength = 32767
        Me.C1Combo2.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.C1Combo2.Name = "C1Combo2"
        Me.C1Combo2.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.C1Combo2.Size = New System.Drawing.Size(124, 16)
        Me.C1Combo2.TabIndex = 1
        Me.C1Combo2.PropBag = resources.GetString("C1Combo2.PropBag")
        '
        'Label1
        '
        Me.Label1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom)  _
            Or System.Windows.Forms.AnchorStyles.Left)  _
            Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.Label1.AutoSize = true
        Me.Label1.ForeColor = System.Drawing.Color.Maroon
        Me.Label1.Location = New System.Drawing.Point(383, 0)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(54, 26)
        Me.Label1.TabIndex = 39
        Me.Label1.Text = "付款情况"
        Me.Label1.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Label12
        '
        Me.Label12.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.Label12.BackColor = System.Drawing.SystemColors.Info
        Me.Label12.Location = New System.Drawing.Point(253, 4)
        Me.Label12.Margin = New System.Windows.Forms.Padding(3)
        Me.Label12.Name = "Label12"
        Me.Label12.Size = New System.Drawing.Size(124, 17)
        Me.Label12.TabIndex = 61
        Me.Label12.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'C1TextBox1
        '
        Me.C1TextBox1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.C1TextBox1.AutoSize = false
        Me.C1TextBox1.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.TableLayoutPanel1.SetColumnSpan(Me.C1TextBox1, 3)
        Me.C1TextBox1.Location = New System.Drawing.Point(63, 30)
        Me.C1TextBox1.Name = "C1TextBox1"
        Me.C1TextBox1.Size = New System.Drawing.Size(314, 17)
        Me.C1TextBox1.TabIndex = 2
        Me.C1TextBox1.Tag = Nothing
        Me.C1TextBox1.TextDetached = true
        Me.C1TextBox1.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        '
        'Label16
        '
        Me.Label16.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom)  _
            Or System.Windows.Forms.AnchorStyles.Left)  _
            Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.Label16.AutoSize = true
        Me.Label16.Location = New System.Drawing.Point(3, 26)
        Me.Label16.Name = "Label16"
        Me.Label16.Size = New System.Drawing.Size(54, 26)
        Me.Label16.TabIndex = 7
        Me.Label16.Text = "备    注"
        Me.Label16.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Label11
        '
        Me.Label11.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom)  _
            Or System.Windows.Forms.AnchorStyles.Left)  _
            Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.Label11.AutoSize = true
        Me.Label11.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label11.Location = New System.Drawing.Point(193, 0)
        Me.Label11.Name = "Label11"
        Me.Label11.Size = New System.Drawing.Size(54, 26)
        Me.Label11.TabIndex = 2
        Me.Label11.Text = "编    码"
        Me.Label11.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'C1TrueDBGrid1
        '
        Me.C1TrueDBGrid1.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.C1TrueDBGrid1.GroupByCaption = "Drag a column header here to group by that column"
        Me.C1TrueDBGrid1.Images.Add(CType(resources.GetObject("C1TrueDBGrid1.Images"),System.Drawing.Image))
        Me.C1TrueDBGrid1.Location = New System.Drawing.Point(179, 126)
        Me.C1TrueDBGrid1.Name = "C1TrueDBGrid1"
        Me.C1TrueDBGrid1.PreviewInfo.Caption = "PrintPreview窗口"
        Me.C1TrueDBGrid1.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.C1TrueDBGrid1.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.C1TrueDBGrid1.PreviewInfo.ZoomFactor = 75R
        Me.C1TrueDBGrid1.PrintInfo.MeasurementDevice = C1.Win.C1TrueDBGrid.PrintInfo.MeasurementDeviceEnum.Screen
        Me.C1TrueDBGrid1.PrintInfo.MeasurementPrinterName = Nothing
        Me.C1TrueDBGrid1.Size = New System.Drawing.Size(405, 183)
        Me.C1TrueDBGrid1.TabIndex = 3
        Me.C1TrueDBGrid1.Text = "C1TrueDBGrid1"
        Me.C1TrueDBGrid1.UseCompatibleTextRendering = false
        Me.C1TrueDBGrid1.PropBag = resources.GetString("C1TrueDBGrid1.PropBag")
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.ColumnCount = 7
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 60!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 130!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 60!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 130!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 60!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 130!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 762!))
        Me.TableLayoutPanel1.Controls.Add(Me.C1Combo2, 5, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.Label12, 3, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.Label1, 4, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.Label16, 0, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.C1TextBox1, 1, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.Label11, 2, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.CgkhCombo, 0, 0)
        Me.TableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Top
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(0, 0)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.RowCount = 3
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 26!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 26!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 20!))
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(850, 58)
        Me.TableLayoutPanel1.TabIndex = 39
        '
        'CgkhCombo
        '
        Me.CgkhCombo.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.CgkhCombo.Bookmark = -1
        Me.CgkhCombo.Captain = "供 应 商"
        Me.CgkhCombo.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.CgkhCombo.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.CgkhCombo.CaptainWidth = 60!
        Me.CgkhCombo.ColumnCaptionHeight = 18
        Me.TableLayoutPanel1.SetColumnSpan(Me.CgkhCombo, 2)
        Me.CgkhCombo.DataSource = Nothing
        Me.CgkhCombo.DataView = Nothing
        Me.CgkhCombo.ItemHeight = 16
        Me.CgkhCombo.ItemTextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.CgkhCombo.Location = New System.Drawing.Point(3, 3)
        Me.CgkhCombo.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.CgkhCombo.MinimumSize = New System.Drawing.Size(0, 20)
        Me.CgkhCombo.Name = "CgkhCombo"
        Me.CgkhCombo.ReadOnly = false
        Me.CgkhCombo.Row = 0
        Me.CgkhCombo.Size = New System.Drawing.Size(184, 20)
        Me.CgkhCombo.TabIndex = 0
        Me.CgkhCombo.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        '
        'YkYf_Crk2
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6!, 12!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(850, 501)
        Me.Controls.Add(Me.C1TrueDBGrid1)
        Me.Controls.Add(Me.TableLayoutPanel1)
        Me.Controls.Add(Me.Panel2)
        Me.Icon = CType(resources.GetObject("$this.Icon"),System.Drawing.Icon)
        Me.Name = "YkYf_Crk2"
        Me.ShowInTaskbar = false
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "入库单"
        CType(Me.C1Holder1,System.ComponentModel.ISupportInitialize).EndInit
        Me.Panel2.ResumeLayout(false)
        Me.Panel2.PerformLayout
        CType(Me.C1Combo2,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.C1TextBox1,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.C1TrueDBGrid1,System.ComponentModel.ISupportInitialize).EndInit
        Me.TableLayoutPanel1.ResumeLayout(false)
        Me.TableLayoutPanel1.PerformLayout
        Me.ResumeLayout(false)

End Sub
    Friend WithEvents C1Holder1 As C1.Win.C1Command.C1CommandHolder
    Friend WithEvents Panel2 As System.Windows.Forms.Panel
    Friend WithEvents T_Label4 As System.Windows.Forms.Label
    Friend WithEvents T_Label5 As System.Windows.Forms.Label
    Friend WithEvents T_Line2 As System.Windows.Forms.Label
    Friend WithEvents Label12 As System.Windows.Forms.Label
    Friend WithEvents C1TextBox1 As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label16 As System.Windows.Forms.Label
    Friend WithEvents Label11 As System.Windows.Forms.Label
    Friend WithEvents C1TrueDBGrid1 As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents Label2 As System.Windows.Forms.Label
    Friend WithEvents C1ToolBar1 As C1.Win.C1Command.C1ToolBar
    Friend WithEvents C1CommandLink1 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents C1Command1 As C1.Win.C1Command.C1Command
    Friend WithEvents C1Command2 As C1.Win.C1Command.C1Command
    Friend WithEvents C1Command3 As C1.Win.C1Command.C1Command
    Friend WithEvents C1Command4 As C1.Win.C1Command.C1Command
    Friend WithEvents C1CommandLink2 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents C1CommandLink3 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents C1CommandLink4 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents C1Combo2 As C1.Win.C1List.C1Combo
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents TableLayoutPanel1 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents CgkhCombo As CustomControl.MyDtComobo
    Friend WithEvents C1Command5 As C1.Win.C1Command.C1Command
    Friend WithEvents C1CommandLink5 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Label3 As System.Windows.Forms.Label
    Friend WithEvents C1Command6 As C1.Win.C1Command.C1Command
    Friend WithEvents C1CommandLink6 As C1.Win.C1Command.C1CommandLink
End Class
