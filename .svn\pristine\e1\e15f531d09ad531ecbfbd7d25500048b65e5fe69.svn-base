﻿Imports Stimulsoft.Report

Public Class Cx_YkYf_Alar
    Public My_Data As New DataSet

    Dim V_Str2 As String

    dim V_Str3 As String
    Dim My_Cm As CurrencyManager             '同步指针
    Dim My_View As New DataView

    Dim YfCode As String
    Dim YfName As String
    Dim Formlb As String

    Public Sub New(ByVal m_YfCode As String, ByVal m_YfName As String, ByVal m_Formlb As String)

        ' 此调用是设计器所必需的。
        InitializeComponent()
        YfCode = m_YfCode
        YfName = m_YfName
        Formlb = m_Formlb
        ' 在 InitializeComponent() 调用之后添加任何初始化。

    End Sub

    Private Sub Alar_Cx1_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Call Init_Form()

        C1NumericEdit1.Value = 0
        C1NumericEdit2.Value = 0
        CheckBox1.Checked = False

        CheckBox3.Checked = True
        C1NumericEdit1.Enabled = True

        CheckBox5.Checked = False
        C1NumericEdit2.Enabled = False

        CheckBox4.Checked = False
        CheckBox2.Enabled = False
        C1TextBox1.Enabled = False
        DateTimePicker1.Enabled = False
        BaseFunc.BaseFunc.Init_Datetimepicker(DateTimePicker1)

        DateTimePicker1.Value = Now

        C1NumericEdit1.Select()
    End Sub

    Private Sub Init_Form()
        C1TextBox1.Text = ""
        Dim My_Grid As New BaseClass.C_Grid(Me.C1TrueDBGrid1)
        With My_Grid
            .Init_Grid()
            .Init_Column("药品编码", "Xx_Code", 0, "中", "")
            .Init_Column("药品名称", "Yp_Name", 240, "左", "")
            .Init_Column("规格", "Mx_Gg", 110, "左", "")
            .Init_Column("产地", "Mx_Cd", 120, "左", "")
            .Init_Column("产品批号", "Yp_Ph", 80, "中", "")
            .Init_Column("有效期", "Yp_Yxq", 75, "中", "yyyy-MM-dd")
            .Init_Column("单位", "Yp_Dw", 35, "中", "")
            .Init_Column("库存", "Yp_Sl", 60, "右", "0.####")

        End With
        C1TrueDBGrid1.HScrollBar.Style = C1.Win.C1TrueDBGrid.ScrollBarStyleEnum.Automatic
        C1TrueDBGrid1.RecordSelectors = False

        Dim My_Combo As New BaseClass.C_Combo1(Me.C1Combo1)
        With My_Combo
            .Init_TDBCombo()
            .AddItem("药品简称")
            .AddItem("药品名称")

        End With
        C1Combo1.Width = 125
        C1Combo1.DropDownWidth = 125
    End Sub
    Private Sub C1TextBox1_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1TextBox1.TextChanged
        My_View = My_Cm.List

        Dim V_Sort As String = ""
        Dim V_RowFilter As String = ""

        If Trim(Me.C1TextBox1.Text & "") = "" Then
            V_Sort = "Xx_Code Asc"
            V_RowFilter = ""
        Else
            Select Case Me.C1Combo1.Text
                Case "药品简称"
                    V_Sort = "Yp_Jc Asc"
                    V_RowFilter = "Yp_Jc like '*" & Trim(C1TextBox1.Text) & "*'"
                Case "药品名称"
                    V_Sort = "Yp_Name Asc"
                    V_RowFilter = "Yp_Name Like '*" & Trim(C1TextBox1.Text) & "*'"
                Case "药品编码"
                    V_Sort = "Xx_Code"
                    V_RowFilter = "Xx_Code like '" & Trim(C1TextBox1.Text) & "*'"
            End Select
        End If

        My_View.Sort = V_Sort
        My_View.RowFilter = V_RowFilter
    End Sub


    Private Sub CheckBox3_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox3.CheckedChanged
        If CheckBox3.Checked = True Then
            C1Command1.Enabled = True
            C1NumericEdit1.Enabled = True
            CheckBox1.Enabled = True
        Else
            C1NumericEdit1.Value = 0
            C1NumericEdit1.Enabled = False
            If CheckBox5.Checked = True Then
                CheckBox1.Enabled = True
            Else
                CheckBox1.Enabled = False
                If CheckBox4.Checked = False Then
                    C1Command1.Enabled = False
                Else
                End If
            End If
        End If

    End Sub

    Private Sub CheckBox5_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox5.CheckedChanged
        If CheckBox5.Checked = True Then
            C1Command1.Enabled = True
            C1NumericEdit2.Enabled = True
            CheckBox1.Enabled = True
        Else
            C1NumericEdit2.Value = 0
            C1NumericEdit2.Enabled = False
            If CheckBox3.Checked = True Then
                CheckBox1.Enabled = True
            Else
                CheckBox1.Enabled = False
                If CheckBox4.Checked = False Then
                    C1Command1.Enabled = False
                Else
                End If
            End If
        End If

    End Sub

    Private Sub CheckBox4_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox4.CheckedChanged
        If CheckBox4.Checked = True Then
            C1Command1.Enabled = True
            CheckBox2.Enabled = True
            DateTimePicker1.Enabled = True
        Else
            CheckBox2.Enabled = False
            DateTimePicker1.Value = Now
            DateTimePicker1.Enabled = False

            If CheckBox3.Checked = False And CheckBox5.Checked = False Then
                C1Command1.Enabled = False
            Else
            End If
        End If

    End Sub

    Private Sub C1Command1_Click(ByVal sender As System.Object, ByVal e As C1.Win.C1Command.ClickEventArgs) Handles C1Command1.Click, C1Command2.Click

        C1TrueDBGrid1.Select()
        Select Case sender.text
            Case "查询"

                If CheckBox3.Checked = True And CheckBox5.Checked = True And C1NumericEdit2.Value > C1NumericEdit1.Value Then
                    Beep()
                    MsgBox("警戒线查询范围最小值不能大于最大值，请修正！", MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
                    C1NumericEdit2.Select()
                    Exit Sub
                End If

                C1TextBox1.Enabled = True
                Dim Yp_Sl As String = ""
                If Formlb = "药库警戒线查询" Then
                    Yp_Sl = "Yk_Sl"
                ElseIf Formlb = "药房警戒线查询" Then
                    Yp_Sl = "Yf_Sl" & CDbl(Mid(YfCode, 5, 2))
                End If

                If CheckBox3.Checked = True Then
                    If CheckBox1.Checked = False Then
                        If CheckBox5.Checked = False Then
                            V_Str2 = " and " & Yp_Sl & "<>'0' and " & Yp_Sl & "<='" & C1NumericEdit1.Value & "'"
                        Else
                            V_Str2 = " and " & Yp_Sl & "<>'0' and " & Yp_Sl & ">='" & C1NumericEdit2.Value & "' and " & Yp_Sl & "<='" & C1NumericEdit1.Value & "'"
                        End If
                    Else
                        If CheckBox5.Checked = False Then
                            V_Str2 = " and " & Yp_Sl & "<='" & C1NumericEdit1.Value & "'"
                        Else
                            V_Str2 = " and " & Yp_Sl & ">='" & C1NumericEdit2.Value & "' and " & Yp_Sl & "<='" & C1NumericEdit1.Value & "'"
                        End If
                    End If
                Else
                    If CheckBox5.Checked = False Then
                        V_Str2 = " and 1=1"
                    Else
                        If CheckBox1.Checked = False Then
                            V_Str2 = " and " & Yp_Sl & "<>'0' and " & Yp_Sl & ">='" & C1NumericEdit2.Value & "' "
                        Else
                            V_Str2 = " and " & Yp_Sl & ">='" & C1NumericEdit2.Value & "' "
                        End If
                    End If
                End If


                If CheckBox4.Checked = True Then
                    If CheckBox2.Checked = False Then
                        V_Str3 = " and Yp_yxq>='" & Format(Now, "yyyy-MM-dd") & "' and Yp_yxq<='" & Format(DateTimePicker1.Value, "yyyy-MM-dd") & "' "
                    Else
                        V_Str3 = " and Yp_yxq<='" & Format(DateTimePicker1.Value, "yyyy-MM-dd") & "' "
                    End If
                Else
                    V_Str3 = "and 1=1"
                End If

                '查询
                Dim V_Str1 As String = ""
                If Formlb = "药库警戒线查询" Then
                    V_Str1 = "select Dl_Code,Dl_Name,Xx_Code,Yp_Name,IsJb,Mx_Gg,Mx_Cd,Mx_Gyzz,Yp_Ph,Yp_Yxq,Mx_CgDw as Yp_Dw,Yk_Sl as Yp_Sl from V_YpKc where 1=1 " & V_Str2 & V_Str3 & "order by Dl_code,Yp_Jc"
                ElseIf Formlb = "药房警戒线查询" Then
                    V_Str1 = "select Dl_Code,Dl_Name,Xx_Code,Yp_Name,IsJb,Mx_Gg,Mx_Cd,Mx_Gyzz,Yp_Ph,Yp_Yxq,Mx_XsDw as Yp_Dw," & Yp_Sl & " as Yp_Sl from V_YpKc where 1=1 " & V_Str2 & V_Str3 & "order by Dl_code,Yp_Jc"
                End If


                HisVar.HisVar.Sqldal.QueryDt(My_Data, V_Str1, "警戒线", True)

                With C1TrueDBGrid1
                    My_Cm = CType(BindingContext(My_Data, "警戒线"), CurrencyManager)
                    .SetDataBinding(My_Data, "警戒线", True)

                    My_View = My_Cm.List
                    My_View.Sort = "Xx_Code Asc"
                End With
            Case "打印"

                If C1TrueDBGrid1.RowCount = 0 Then Exit Sub
                Dim StiRpt As New StiReport
                StiRpt.Load(".\Rpt\药品警戒线查询.mrt")
                If Formlb = "药库警戒线查询" Then
                    StiRpt.ReportName = "药库药品警戒线"
                ElseIf Formlb = "药房警戒线查询" Then
                    StiRpt.ReportName = YfName & "药品警戒线"
                End If
                StiRpt.RegData(My_View)

                StiRpt.Compile()
                StiRpt("制表人") = HisVar.HisVar.JsrName
                StiRpt("标题") = StiRpt.ReportName
                StiRpt("打印时间") = Format(Now, "yyyy-MM-dd HH:mm:ss")

                'StiRpt.Design()
                StiRpt.Show()
        End Select
    End Sub


  
End Class