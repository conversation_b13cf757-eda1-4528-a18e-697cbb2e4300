﻿Imports System.Data.SqlClient
Imports System.Drawing.Printing

Public Class Person_Config
    Dim My_Adapter As New SqlDataAdapter
    Dim db_dy As New DataTable
    Public Zd_Msg2 As New BLLOld.B_Zd_Msg2
    Dim color As Color
    Dim BllJsr As New BLLOld.B_Zd_YyJsr

    Private Sub Person_Config_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        With Me
            .TextBox1.Text = HisVar.HisVar.JsrName
            .TextBox2.Text = ""
            .TextBox3.Text = ""
            .TextBox4.Text = ""
            .TextBox2.Focus()
        End With

        Form_Init()
        Data_Init()
        Load_LocalPrinter()
    End Sub


    Protected Overrides Sub OnKeyDown(ByVal e As System.Windows.Forms.KeyEventArgs)
        MyBase.OnKeyDown(e)
        If e.Control = True And e.KeyCode = Keys.S Then
            Call C1Button1_Click(C1Button1, Nothing)
        End If
    End Sub

#Region "密码设置"

    Private Sub C1Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Button1.Click
        If Trim(Me.TextBox3.Text) = "" Then
            MsgBox("新密码不能为空!", MsgBoxStyle.Critical, "提示")
            Exit Sub
        End If

        If Trim(Me.TextBox3.Text) <> Trim(Me.TextBox4.Text) Then
            MsgBox("密码验证不能通过，请重新输入！", MsgBoxStyle.Critical, "提示")
            Exit Sub
        End If

        Dim Czy_Old_Pass As String = ""
        Dim New_Old_Pass As String = ""

        P_Conn(True)

        Dim My_Com As New SqlCommand
        With My_Com
            .Connection = My_Cn
            .CommandText = "Select Jsr_Password From Zd_YyJsr Where Jsr_Code='" & HisVar.HisVar.JsrCode & "'"
            Czy_Old_Pass = .ExecuteScalar & ""
        End With

        If encode.F_Encode(Trim(Me.TextBox2.Text)) <> Czy_Old_Pass Then
            MsgBox("原始口令不正确，请重新输入！", MsgBoxStyle.Critical, "提示")
            Me.TextBox2.Text = ""
            Me.TextBox2.Focus()
            Exit Sub
        End If

        Try
            With My_Com
                .CommandText = "Update  Zd_YyJsr Set Jsr_Password='" & encode.F_Encode(Trim(Me.TextBox3.Text)) & "' Where Jsr_Code= '" & HisVar.HisVar.JsrCode & "'"
                .ExecuteNonQuery()
            End With

            P_Conn(False)

            MsgBox("操作成功完成！", MsgBoxStyle.Information, "提示")
            Me.Close()
        Catch ex As Exception


        End Try
    End Sub

    Private Sub C1Button2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Button2.Click
        Me.Close()
    End Sub

    Private Sub TextBox2_KeyPress(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles TextBox2.KeyPress, TextBox3.KeyPress, TextBox4.KeyPress
        If e.KeyChar <> Chr(Keys.Return) Then Exit Sub
        e.Handled = True
        System.Windows.Forms.SendKeys.Send("{Tab}")
    End Sub
#End Region

#Region "输入法设置"

    Private Sub Form_Init()
        With inputCombo
            .Additem = "通用,0"
            For Each il As InputLanguage In InputLanguage.InstalledInputLanguages
                .Additem = il.LayoutName
            Next
            .SelectedIndex = -1
            .DisplayColumns(1).Visible = False
        End With

        If HisVar.HisVar.InputCode IsNot Nothing Then
            inputCombo.SelectedText = HisVar.HisVar.InputCode.LayoutName
        End If

        With MyGrid1
            .Clear()
            .Init_Grid()
            .Init_Column("编码", "msg_Code", 0, "左", "", False)
            .Init_Column("提醒内容", "msg_Content", 300, "左", "", False)
            .Init_Column("是否订阅", "Check", 50, "中", "", True)
            .AllowAddNew = False
            .ColumnFooters = True
        End With

        Me.MyGrid1.Columns("是否订阅").ValueItems.Presentation = C1.Win.C1TrueDBGrid.PresentationEnum.CheckBox
        Me.MyGrid1.AllowUpdate = True
        Me.MyGrid1.Splits(0).DisplayColumns(1).Locked = True

        colorlbl.BackColor = HisVar.HisVar.JsrColor
    End Sub

    Private Sub Data_Init()

        Dim Str_Update As String = "Update Zd_YyJsr Set Jsr_Py=@Jsr_Py,EmrColor=@EmrColor Where Jsr_Code=@Old_Jsr_Code"

        With My_Adapter
            .UpdateCommand = New SqlCommand(Str_Update, My_Cn)
            With .UpdateCommand.Parameters
                .Add("@Jsr_Py", SqlDbType.VarChar, 50, "Jsr_Py")
                .Add("@EmrColor", SqlDbType.VarChar, 50, "EmrColor")
                .Add("@Old_Jsr_Code", SqlDbType.VarChar, 7, "Jsr_Code")
            End With
        End With

        db_dy = HisVar.HisVar.Sqldal.Query("select zd_msg1.msg_Code,msg_Content,case isnull(Jsr_Code,'') when '' then 'False' else 'True' end AS 'Check' from zd_msg1 left Join Zd_Msg2 on zd_msg1.Msg_Code=Zd_Msg2.Msg_Code and Jsr_Code='" & HisVar.HisVar.JsrCode & "'").Tables(0)
        db_dy.Columns("Check").ReadOnly = False
        'My_DataSet.Tables("提醒订阅").Columns("Check")
        'With Me.MyGrid1
        '    .SetDataBinding(My_DataSet, "提醒订阅", True)
        'End With
        MyGrid1.DataTable = db_dy
    End Sub

    Private Sub C1Button4_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Button4.Click
        If HisVar.HisVar.JsrColor <> Nothing Then
            If BllJsr.GetRecordCount("Jsr_code<>'" & HisVar.HisVar.JsrCode & "' and EmrColor='" & HisVar.HisVar.JsrColor.A.ToString() & "," & HisVar.HisVar.JsrColor.R.ToString() & "," & HisVar.HisVar.JsrColor.G.ToString() & "," & HisVar.HisVar.JsrColor.B.ToString() & "'") > 1 Then
                MessageBox.Show("该颜色已被使用，请换一种颜色!", "提示", MessageBoxButtons.OK)
                Exit Sub
            End If
        End If

        With My_Adapter.UpdateCommand
            .Parameters(0).Value = inputCombo.Text
            If HisVar.HisVar.JsrColor <> Nothing Then
                .Parameters(1).Value = HisVar.HisVar.JsrColor.A.ToString() & "," & HisVar.HisVar.JsrColor.R.ToString() & "," & HisVar.HisVar.JsrColor.G.ToString() & "," & HisVar.HisVar.JsrColor.B.ToString()
            Else
                .Parameters(1).Value = DBNull.Value
            End If
            .Parameters(2).Value = HisVar.HisVar.JsrCode
            P_Conn(True)
            .ExecuteNonQuery()
            P_Conn(False)
        End With

        Dim m_inputcode As InputLanguage
        For Each m_inputcode In InputLanguage.InstalledInputLanguages
            If m_inputcode.LayoutName = inputCombo.Text Then
                HisVar.HisVar.InputCode = m_inputcode
                Exit For
            End If
        Next

        MsgBox("输入法参数保存成功！", MsgBoxStyle.Information, "提示")
        Me.Close()
    End Sub

    Private Sub C1Button3_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Button3.Click
        Me.Close()
    End Sub

    Private Sub MyButton1_Click(sender As System.Object, e As System.EventArgs) Handles btnColorSelect.Click
        Dim colordlg As New ColorDialog
        colordlg.AllowFullOpen = True
        colordlg.ShowHelp = False
        colordlg.ShowDialog()
        color = colordlg.Color
        colorlbl.BackColor = color
        HisVar.HisVar.JsrColor = color
    End Sub

#End Region

    Private Sub MyGrid1_AfterColEdit(ByVal sender As System.Object, ByVal e As C1.Win.C1TrueDBGrid.ColEventArgs) Handles MyGrid1.AfterColEdit
        If e.ColIndex <> 2 Then Exit Sub
        Dim Zd_Msg_Model As New Modelold.M_Zd_Msg2
        Zd_Msg_Model.Jsr_Code = HisVar.HisVar.JsrCode
        Zd_Msg_Model.Msg_Code = sender.Columns(e.ColIndex - 2).Value
        If sender.Columns(e.ColIndex).Value = True Then
            Zd_Msg2.Add(Zd_Msg_Model)
        Else
            Zd_Msg2.Delete(HisVar.HisVar.JsrCode, sender.Columns(e.ColIndex - 2).Value)
        End If
        HisPara.PublicConfig.ReadConfig()
    End Sub

#Region "打印机设置"
    Private Sub Load_LocalPrinter()
        Try

            Dim Print_Name As String = ""
            Dim prtdoc As PrintDocument = New PrintDocument
            For Each Print_Name In PrinterSettings.InstalledPrinters
                MySingleComobo1.Additem = Print_Name
                MySingleComobo2.Additem = Print_Name
            Next
            MySingleComobo1.DroupDownWidth = 400
            MySingleComobo1.DisplayColumns(1).Visible = False
            MySingleComobo1.Text = IIf(iniOperate.iniopreate.GetINI("打印机设置", "门诊打印机", "", HisVar.HisVar.Parapath & "\Config.ini") & "" = "", prtdoc.PrinterSettings.PrinterName, iniOperate.iniopreate.GetINI("打印机设置", "门诊打印机", "", HisVar.HisVar.Parapath & "\Config.ini"))

            MySingleComobo2.DroupDownWidth = 400
            MySingleComobo2.DisplayColumns(1).Visible = False
            MySingleComobo2.Text = IIf(iniOperate.iniopreate.GetINI("打印机设置", "住院打印机", "", HisVar.HisVar.Parapath & "\Config.ini") & "" = "", prtdoc.PrinterSettings.PrinterName, iniOperate.iniopreate.GetINI("打印机设置", "住院打印机", "", HisVar.HisVar.Parapath & "\Config.ini"))

            MySingleComobo3.Additem = "80列(241mm*279.4mm)三分割(9.3)"
            MySingleComobo3.Additem = "80列(241mm*279.4mm)二分割(13.95)"
            MySingleComobo3.Additem = "80列(241mm*279.4mm)未分割(27.9)"
            MySingleComobo3.DisplayColumns(1).Visible = False
            MySingleComobo3.Text = IIf(iniOperate.iniopreate.GetINI("打印机设置", "药库管理纸型", "", HisVar.HisVar.Parapath & "\Config.ini") & "" = "", "80列(241mm*279.4mm)三分割(9.3)", iniOperate.iniopreate.GetINI("打印机设置", "药库管理纸型", "", HisVar.HisVar.Parapath & "\Config.ini"))

            MySingleComobo4.Additem = "80列(241mm*279.4mm)三分割(9.3)"
            MySingleComobo4.Additem = "80列(241mm*279.4mm)二分割(13.95)"
            MySingleComobo4.Additem = "80列(241mm*279.4mm)未分割(27.9)"
            MySingleComobo4.DisplayColumns(1).Visible = False
            MySingleComobo4.Text = IIf(iniOperate.iniopreate.GetINI("打印机设置", "药房管理纸型", "", HisVar.HisVar.Parapath & "\Config.ini") & "" = "", "80列(241mm*279.4mm)三分割(9.3)", iniOperate.iniopreate.GetINI("打印机设置", "药房管理纸型", "", HisVar.HisVar.Parapath & "\Config.ini"))

        Catch ex As Exception
            '   MsgBox(ex.Message.ToString & "加载打印机有误，请检查是否已安装打印设备！", MsgBoxStyle.Information, "提示")
            Button1.Enabled = False
        End Try
    End Sub

    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click

        iniOperate.iniopreate.WriteINI("打印机设置", "门诊打印机", MySingleComobo1.Text, HisVar.HisVar.Parapath & "\Config.ini")
        iniOperate.iniopreate.WriteINI("打印机设置", "住院打印机", MySingleComobo2.Text, HisVar.HisVar.Parapath & "\Config.ini")
        iniOperate.iniopreate.WriteINI("打印机设置", "药库管理纸型", MySingleComobo3.Text, HisVar.HisVar.Parapath & "\Config.ini")
        iniOperate.iniopreate.WriteINI("打印机设置", "药房管理纸型", MySingleComobo4.Text, HisVar.HisVar.Parapath & "\Config.ini")
    End Sub

#End Region

  
End Class