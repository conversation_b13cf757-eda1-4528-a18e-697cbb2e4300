﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="0" />
    <DataSources isList="true" count="1">
      <医院费用统计 Ref="2" type="DataTableSource" isKey="true">
        <Alias>医院费用统计</Alias>
        <Columns isList="true" count="8">
          <value>lb,System.String</value>
          <value>lb_Order,System.Int32</value>
          <value>lb2,System.String</value>
          <value>sl,System.Decimal</value>
          <value>S_Money,System.Decimal</value>
          <value>S_lb,System.String</value>
          <value>N_Order,System.Int32</value>
          <value>S_money,System.Decimal</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>医院费用统计</Name>
        <NameInSource>医院费用统计</NameInSource>
      </医院费用统计>
    </DataSources>
    <Relations isList="true" count="0" />
    <Report isRef="0" />
    <Variables isList="true" count="1">
      <value>,统计时间,统计时间,System.String,,False,False</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="3" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="1">
        <ReportTitleBand1 Ref="4" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,19,1.6</ClientRectangle>
          <Components isList="true" count="4">
            <Text1 Ref="5" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,19,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>黑体,15.75,Regular,Point,False,134</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text1</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>基 层 卫 生 院 收 入 情 况 汇 总 表</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text1>
            <Text2 Ref="6" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1,12.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text2</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>{统计时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text2>
            <Text3 Ref="7" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>12.6,1,5.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>92ceb34ecb164ff3b8ab693a0507312d</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text3</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>单位：元   </Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text3>
            <CrossTab1 Ref="8" type="Stimulsoft.Report.CrossTab.StiCrossTab" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.6,19,7.4</ClientRectangle>
              <Components isList="true" count="11">
                <CrossTab1_RowTotal2 Ref="9" type="CrossRowTotal" isKey="true">
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>[255:255:255]</Brush>
                  <ClientRectangle>1.6,1.9,0,0</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>宋体,10.5,Regular,Point,False,134</Font>
                  <Guid>2f7055cae0544148b1dc0f57de86c988</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_RowTotal2</Name>
                  <Page isRef="3" />
                  <Parent isRef="8" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <Text>合计</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                </CrossTab1_RowTotal2>
                <CrossTab1_Row1_Title Ref="10" type="CrossTitle" isKey="true">
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>White</Brush>
                  <ClientRectangle>0,0.65,1.6,0.6</ClientRectangle>
                  <Font>宋体,10.5,Regular,Point,False,134</Font>
                  <Guid>855021c72f28422ea4cd724f33ba342e</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_Row1_Title</Name>
                  <Page isRef="3" />
                  <Parent isRef="8" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <Text>收费类别</Text>
                  <TextBrush>[105:105:105]</TextBrush>
                  <Type>Expression</Type>
                  <TypeOfComponent>Row:CrossTab1_Row1</TypeOfComponent>
                </CrossTab1_Row1_Title>
                <CrossTab1_ColTotal1 Ref="11" type="CrossColumnTotal" isKey="true">
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>[255:255:255]</Brush>
                  <ClientRectangle>3.85,0.65,1,0.6</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>宋体,10.5,Regular,Point,False,134</Font>
                  <Guid>8ce4a043bf5548f392fb52c768ed455b</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_ColTotal1</Name>
                  <Page isRef="3" />
                  <Parent isRef="8" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <Text>合计</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                </CrossTab1_ColTotal1>
                <CrossTab1_LeftTitle Ref="12" type="CrossTitle" isKey="true">
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>White</Brush>
                  <ClientRectangle>0,0,3.2,0.6</ClientRectangle>
                  <Font>宋体,10.5,Regular,Point,False,134</Font>
                  <Guid>f04c9954faaa4368bfa985d09d1db2dd</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_LeftTitle</Name>
                  <Page isRef="3" />
                  <Parent isRef="8" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <TextBrush>[105:105:105]</TextBrush>
                  <Type>Expression</Type>
                  <TypeOfComponent>LeftTitle</TypeOfComponent>
                </CrossTab1_LeftTitle>
                <CrossTab1_RowTotal1 Ref="13" type="CrossRowTotal" isKey="true">
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>[255:255:255]</Brush>
                  <ClientRectangle>0,1.9,3.2,0.6</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>宋体,10.5,Regular,Point,False,134</Font>
                  <Guid>bbe54684ead847a5ad9e637fb11675be</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_RowTotal1</Name>
                  <Page isRef="3" />
                  <Parent isRef="8" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <Text>总计</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                </CrossTab1_RowTotal1>
                <CrossTab1_Row2_Title Ref="14" type="CrossTitle" isKey="true">
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>White</Brush>
                  <ClientRectangle>1.6,0.65,1.6,0.6</ClientRectangle>
                  <Font>宋体,10.5,Regular,Point,False,134</Font>
                  <Guid>08eac7c887c1458f88fe50bc41dad09b</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_Row2_Title</Name>
                  <Page isRef="3" />
                  <Parent isRef="8" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <Text>收费项目</Text>
                  <TextBrush>[105:105:105]</TextBrush>
                  <Type>Expression</Type>
                  <TypeOfComponent>Row:CrossTab1_Row2</TypeOfComponent>
                </CrossTab1_Row2_Title>
                <CrossTab1_Row1 Ref="15" type="CrossRow" isKey="true">
                  <Alias>lb2</Alias>
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>White</Brush>
                  <ClientRectangle>0,1.3,1.6,0.6</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <DisplayValue>{医院费用统计.lb2}</DisplayValue>
                  <Font>宋体,10.5,Regular,Point,False,134</Font>
                  <Guid>5d7e3d4018f7468aa1c98d09b776353f</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_Row1</Name>
                  <Page isRef="3" />
                  <Parent isRef="8" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <SortType>ByValue</SortType>
                  <Text>lb2</Text>
                  <TextBrush>[105:105:105]</TextBrush>
                  <TotalGuid>bbe54684ead847a5ad9e637fb11675be</TotalGuid>
                  <Value>{医院费用统计.lb_Order}</Value>
                </CrossTab1_Row1>
                <CrossTab1_Row2 Ref="16" type="CrossRow" isKey="true">
                  <Alias>S_lb</Alias>
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>White</Brush>
                  <ClientRectangle>1.6,1.3,1.6,0.6</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <DisplayValue>{医院费用统计.S_lb}</DisplayValue>
                  <Font>宋体,10.5,Regular,Point,False,134</Font>
                  <Guid>27c9af3dfe574b18bdd1c7987c485896</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_Row2</Name>
                  <Page isRef="3" />
                  <Parent isRef="8" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <ShowTotal>False</ShowTotal>
                  <Text>S_lb</Text>
                  <TextBrush>[105:105:105]</TextBrush>
                  <TotalGuid>2f7055cae0544148b1dc0f57de86c988</TotalGuid>
                  <Value>{医院费用统计.S_lb}</Value>
                </CrossTab1_Row2>
                <CrossTab1_Column1 Ref="17" type="CrossColumn" isKey="true">
                  <Alias>lb</Alias>
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>White</Brush>
                  <ClientRectangle>3.25,0.65,0.6,0.6</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <DisplayValue>{医院费用统计.lb}</DisplayValue>
                  <Font>宋体,10.5,Regular,Point,False,134</Font>
                  <Guid>e7ac615c25224945846a184c0946f433</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_Column1</Name>
                  <Page isRef="3" />
                  <Parent isRef="8" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <Text>lb</Text>
                  <TextBrush>[105:105:105]</TextBrush>
                  <TotalGuid>8ce4a043bf5548f392fb52c768ed455b</TotalGuid>
                  <Value>{医院费用统计.lb}</Value>
                </CrossTab1_Column1>
                <CrossTab1_Sum1 Ref="18" type="CrossSummary" isKey="true">
                  <Alias>S_money</Alias>
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>[255:255:255]</Brush>
                  <ClientRectangle>3.25,1.3,0.6,0.6</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>宋体,10.5,Regular,Point,False,134</Font>
                  <Guid>46b1b5c49be24a82a4e0ea1819afb055</Guid>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_Sum1</Name>
                  <Page isRef="3" />
                  <Parent isRef="8" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <Text>0</Text>
                  <TextBrush>Black</TextBrush>
                  <Value>{医院费用统计.S_money}</Value>
                </CrossTab1_Sum1>
                <CrossTab1_RightTitle Ref="19" type="CrossTitle" isKey="true">
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>White</Brush>
                  <ClientRectangle>3.25,0,1.6,0.6</ClientRectangle>
                  <Font>宋体,10.5,Regular,Point,False,0</Font>
                  <Guid>b323cc1cff8142df9ab9964f82671c5c</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_RightTitle</Name>
                  <Page isRef="3" />
                  <Parent isRef="8" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <Text>类别</Text>
                  <TextBrush>[105:105:105]</TextBrush>
                  <Type>Expression</Type>
                  <TypeOfComponent>RightTitle</TypeOfComponent>
                </CrossTab1_RightTitle>
              </Components>
              <Conditions isList="true" count="0" />
              <DataRelationName />
              <DataSourceName>医院费用统计</DataSourceName>
              <EmptyValue />
              <Filters isList="true" count="0" />
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Width</HorAlignment>
              <Name>CrossTab1</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Sort isList="true" count="0" />
            </CrossTab1>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>ReportTitleBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </ReportTitleBand1>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>140e719a1fad4b49bcde72b3c70dd461</Guid>
      <Margins>1,1,1,1</Margins>
      <Name>Page1</Name>
      <PageHeight>29.7</PageHeight>
      <PageWidth>21</PageWidth>
      <Report isRef="0" />
      <Watermark Ref="20" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page1>
  </Pages>
  <PrinterSettings Ref="21" type="Stimulsoft.Report.Print.StiPrinterSettings" isKey="true" />
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>医院费用统计</ReportAlias>
  <ReportChanged>8/1/2012 9:56:44 AM</ReportChanged>
  <ReportCreated>7/13/2012 3:39:50 PM</ReportCreated>
  <ReportFile>Rpt\医院费用统计.mrt</ReportFile>
  <ReportGuid>8100d86f9bf4466c9b8735c613d9db65</ReportGuid>
  <ReportName>医院费用统计</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2011.2.1026</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class Report : Stimulsoft.Report.StiReport
    {
        public Report()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
        #endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>