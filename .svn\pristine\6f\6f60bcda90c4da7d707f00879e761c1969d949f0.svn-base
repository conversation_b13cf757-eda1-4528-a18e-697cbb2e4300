﻿Imports BaseClass
Imports System.Windows.Forms
Imports System.Drawing

Public Class Zd_ZhiKong3

#Region "变量定义"
    Dim V_Name As String                                            '简称是否发生变化
    Dim V_LbCount As Integer

    Dim ZkDjBll As New BLLOld.B_Emr_ZkDj
    Dim Emr_ZhiKong2Model As New ModelOld.M_Emr_ZhiKong2
    Dim Emr_ZhiKong2Bll As New BLLOld.B_Emr_ZhiKong2

#End Region

#Region "传参"
    Dim Rinsert As Boolean
    Dim Rrow As DataRow
    Dim RZbtb As DataTable
    Dim Rrc As C_RowChange
    Dim RZk As String
#End Region
    Public Sub New(ByVal tinsert As Boolean, ByVal trow As DataRow, ByVal tZbtb As DataTable, ByRef trc As C_RowChange, ByVal tZk As String)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        Rinsert = tinsert
        Rrow = trow
        RZbtb = tZbtb
        Rrc = trc
        RZk = tZk

        ' 在 InitializeComponent() 调用之后添加任何初始化。
    End Sub

    Private Sub ZkDj2_FormClosed(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosedEventArgs) Handles Me.FormClosed
        RemoveHandler Rrc.RowChanged, AddressOf Data_Show
    End Sub

    Private Sub ZkDj2_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Me.Load
        AddHandler Rrc.RowChanged, AddressOf Data_Show
        Call Form_Init()
        If Rinsert = True Then Call Data_Clear() Else Call Data_Show(Rrow)


    End Sub

#Region "窗体__事件"

    Private Sub Form_Init()
        Panel1.Height = 27
        ToolBar1.Location = New Point(2, 4)

        '按扭初始化
        Comm1.Top = 2
        Comm2.Location = New Point(Comm1.Left + Comm1.Width + 2, Comm1.Top)
        Code_TextBox.Enabled = False

        With KfStyleMySingleComobo1
            .Additem = "按等级扣分,1"
            .Additem = "单项扣分,2"
            .Additem = "多项扣分,3"
            .DisplayColumns(1).Visible = False
            .SelectedIndex = 0
            .DroupDownWidth = .Width
        End With

    End Sub

#End Region

#Region "数据__操作"

    Private Sub Data_Clear()
        Rinsert = True
        Code_TextBox.Text = RZk
        Name_TextBox.Text = ""
        KfStyleMySingleComobo1.SelectedIndex = 0
        NrMyTextBox1.Text = ""
        KfNumericEdit1.Value = 0
        Memo_TextBox.Text = ""
        Call P_Show_Label()
    End Sub

    Private Sub Data_Show(ByVal tmp_Row As DataRow)
        Rinsert = False
        Rrow = tmp_Row
        With Rrow
            Code_TextBox.Text = .Item("Zk_Code") & ""
            Name_TextBox.Text = .Item("Mx_Name") & ""
            KfStyleMySingleComobo1.SelectedValue = .Item("Kf_Pz") & ""
            If KfStyleMySingleComobo1.Text = "按等级扣分" Then
                GradeMyDtComobo1.SelectedValue = .Item("ZkDj_Code")
            Else
                NrMyTextBox1.Text = .Item("Kf_PzNr")
            End If
            KfNumericEdit1.Value = .Item("Zk_Kf")
            Memo_TextBox.Text = .Item("Memo") & ""
            V_Name = Name_TextBox.Text
        End With
        Call P_Show_Label()
    End Sub

    Private Sub P_Show_Label()

        If Rinsert = True Then
            Move5.Enabled = False                                           '新增记录
            T_Label2.Text = "新增"
        Else
            Move5.Enabled = True                                            '新增记录
            T_Label2.Text = IIf(RZbtb.Rows.Count() = 0, "0", CStr((RZbtb.Rows.IndexOf(Rrow)) + 1))
        End If
        T_Label3.Text = "∑=" & RZbtb.Rows.Count
        Name_TextBox.Select()
    End Sub

#End Region

#Region "控件__动作"

    Private Sub KfStyleMySingleComobo1_RowChange(sender As Object, e As System.EventArgs) Handles KfStyleMySingleComobo1.RowChange
        If KfStyleMySingleComobo1.Text = "按等级扣分" Then
            SplitContainer1.Panel1Collapsed = False
            SplitContainer1.Panel2Collapsed = True
            With GradeMyDtComobo1
                .DataView = ZkDjBll.GetList("").Tables(0).DefaultView
                .Init_Colum("ZkDj_Name", "等级名称", 120, "左")
                .Init_Colum("ZkDj_Jc", "等级简称", 0, "左")
                .Init_Colum("ZkDj_Code", "等级编码", 0, "中")
                .DisplayMember = "ZkDj_Name"
                .ValueMember = "ZkDj_Code"
                .RowFilterNotTextNull = "ZkDj_Jc"
                .RowFilterTextNull = ""
                .DroupDownWidth = .Width - .CaptainWidth
                .MaxDropDownItems = 15
                .SelectedValue = -1
            End With
            KfNumericEdit1.Enabled = False
        Else
            SplitContainer1.Panel1Collapsed = True
            SplitContainer1.Panel2Collapsed = False
            KfNumericEdit1.Enabled = True
        End If
        KfNumericEdit1.Value = 0
    End Sub

    Private Sub GradeMySingleComobo1_RowChange(sender As Object, e As System.EventArgs) Handles GradeMyDtComobo1.RowChange
        If SplitContainer1.Panel1Collapsed = False And GradeMyDtComobo1.SelectedIndex >= 0 Then
            KfNumericEdit1.Value = ZkDjBll.GetDeductedScore(GradeMyDtComobo1.Text)
        End If
    End Sub

    Private Sub Comm_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.Click, Comm2.Click
        Select Case sender.tag
            Case "保存"
                If Name_TextBox.Text.Trim = "" Then
                    MsgBox("请输入质控内容！", MsgBoxStyle.Exclamation, "提示")
                    Name_TextBox.Select()
                    Exit Sub
                End If
                V_LbCount = Emr_ZhiKong2Bll.GetRecordCount("Mx_Name = '" & Trim(Name_TextBox.Text) & "'")
                If Rinsert = True Then
                    If V_LbCount > 0 Then
                        MsgBox("该质控内容已经存在！", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示")
                        Name_TextBox.Select()
                        Exit Sub
                    End If
                    Call Data_Add()


                Else
                    If Name_TextBox.Text.Trim <> "" And Name_TextBox.Text.Trim <> V_Name Then
                        If V_LbCount > 0 Then
                            MsgBox("该质控扣分等级已经存在！", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示")
                            Name_TextBox.Select()
                            Exit Sub
                        End If
                    End If
                    Call Data_Edit()
                End If
            Case "取消"
                Me.Close()
        End Select
    End Sub

    Private Sub Move_Click(ByVal sender As Object, ByVal e As C1.Win.C1Command.ClickEventArgs) Handles Move1.Click, Move2.Click, Move3.Click, Move4.Click, Move5.Click
        If sender.text = "新增" Then                            '增加状态
            Call Data_Clear()
        Else
            Rrc.GridMove(sender.text)
        End If
    End Sub




#End Region

#Region "数据__编辑"

    Private Sub Data_Add()
        Dim My_NewRow As DataRow = RZbtb.NewRow

        With My_NewRow
            .Item("id") = Emr_ZhiKong2Bll.GetMaxId()
            .Item("Zk_Code") = RZk
            .Item("Mx_Name") = Trim(Name_TextBox.Text & "")
            .Item("Kf_Pz") = KfStyleMySingleComobo1.SelectedValue
            If KfStyleMySingleComobo1.Text = "按等级扣分" Then
                .Item("Kf_PzNr") = GradeMyDtComobo1.Text
            Else
                .Item("Kf_PzNr") = NrMyTextBox1.Text
            End If
            .Item("Zk_Kf") = KfNumericEdit1.Value
            .Item("Memo") = Trim(Memo_TextBox.Text & "")

        End With

        '数据保存
        Try
            RZbtb.Rows.Add(My_NewRow)
            Rrc.GridMove("最后")
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            Exit Sub
        Finally
            Name_TextBox.Select()
        End Try

        '数据更新
        Try
            With Emr_ZhiKong2Model
                .id = My_NewRow.Item("id")
                .Zk_Code = My_NewRow.Item("Zk_Code")
                .Mx_Name = My_NewRow.Item("Mx_Name")
                .Kf_Pz = My_NewRow.Item("Kf_Pz")
                .Kf_PzNr = My_NewRow.Item("Kf_PzNr")
                If My_NewRow.Item("Zk_Kf") & "" <> "" Then
                    .Zk_Kf = My_NewRow.Item("Zk_Kf")
                End If

                .Memo = My_NewRow.Item("Memo")
            End With
            Emr_ZhiKong2Bll.Add(Emr_ZhiKong2Model)
            My_NewRow.AcceptChanges()
            RZbtb.AcceptChanges()
            MsgBox("添加成功！", MsgBoxStyle.Exclamation, "提示:")
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            Name_TextBox.Select()
            Exit Sub
        Finally
        End Try

        '数据清空
        Call Data_Clear()

    End Sub



    Private Sub Data_Edit()

        Dim My_Row As DataRow = Rrow

        Try
            With My_Row
                .BeginEdit()
                .Item("Zk_Code") = RZk
                .Item("Mx_Name") = Trim(Name_TextBox.Text & "")
                .Item("Kf_Pz") = KfStyleMySingleComobo1.SelectedValue
                If KfStyleMySingleComobo1.Text = "按等级扣分" Then
                    .Item("Kf_PzNr") = GradeMyDtComobo1.Text
                Else
                    .Item("Kf_PzNr") = NrMyTextBox1.Text
                End If
                .Item("Zk_Kf") = KfNumericEdit1.Value
                .Item("Memo") = Trim(Memo_TextBox.Text & "")
                .EndEdit()
            End With

        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Critical, "提示:")
            My_Row.CancelEdit()
            Exit Sub
        Finally
            Name_TextBox.Select()
        End Try

        '数据更新
        Try
            With Emr_ZhiKong2Model
                .id = My_Row.Item("id")
                .Zk_Code = My_Row.Item("Zk_Code")
                .Mx_Name = My_Row.Item("Mx_Name")
                .Kf_Pz = My_Row.Item("Kf_Pz")
                .Kf_PzNr = My_Row.Item("Kf_PzNr")
                If My_Row.Item("Zk_Kf") & "" <> "" Then
                    .Zk_Kf = My_Row.Item("Zk_Kf")
                End If

                .Memo = My_Row.Item("Memo")
            End With
            Emr_ZhiKong2Bll.Update(Emr_ZhiKong2Model)
            My_Row.AcceptChanges()
            V_Name = My_Row.Item("Mx_Name")
            MsgBox("更新成功！", MsgBoxStyle.Exclamation, "提示:")

        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Information + MsgBoxStyle.OkOnly, "提示:")
        Finally
            'MinValue_TextBox.Select()
            Name_TextBox.Select()
        End Try
    End Sub

#End Region


    'Private Sub ZhongWen_TextBox_GotFocus(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Name_TextBox.GotFocus, Name_TextBox.GotFocus, Memo_TextBox.GotFocus, MaxValue_TextBox.GotFocus, Fzr_TextBox.GotFocus, Add_TextBox.GotFocus
    '    InputLanguage.CurrentInputLanguage = HisVar.HisVar.InputCode
    'End Sub
    'Private Sub YingWen_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles OrgCode_TextBox.GotFocus, LxrTell_TextBox.GotFocus, FzrTell_TextBox.GotFocus
    '    InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages(0)
    'End Sub
    Private Sub ZhongWen_TextBox_GotFocus(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Name_TextBox.GotFocus, Memo_TextBox.GotFocus
        InputLanguage.CurrentInputLanguage = HisVar.HisVar.InputCode
    End Sub


End Class