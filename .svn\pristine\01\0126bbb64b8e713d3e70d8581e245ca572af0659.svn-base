﻿/**  版本信息模板在安装目录下，可自行修改。
* Jkda_User.cs
*
* 功 能： N/A
* 类 名： Jkda_User
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2018/10/22 9:07:57   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace ModelOld
{
	/// <summary>
	/// Jkda_User:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class M_Jkda_User
	{
		public M_Jkda_User()
		{}
		#region Model
		private string _jsr_code;
		private string _jkda_username;
		private string _jkda_password;
		private string _jkda_token;
		/// <summary>
		/// 
		/// </summary>
		public string Jsr_Code
		{
			set{ _jsr_code=value;}
			get{return _jsr_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Jkda_Username
		{
			set{ _jkda_username=value;}
			get{return _jkda_username;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Jkda_Password
		{
			set{ _jkda_password=value;}
			get{return _jkda_password;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Jkda_Token
		{
			set{ _jkda_token=value;}
			get{return _jkda_token;}
		}
		#endregion Model

	}
}

