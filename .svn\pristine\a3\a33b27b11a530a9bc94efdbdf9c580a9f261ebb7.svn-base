﻿Imports System.Data.SqlClient
Imports System.Data.OleDb
Imports System.Diagnostics
Imports C1.Win.C1FlexGrid
Imports System.Security.Cryptography
Imports System.Text
Imports Stimulsoft.Report

Module F_Module
    Public My_Cn As New SqlConnection
    Public Login_Cn As New SqlConnection  '登陆验证连接
    Public V_PassWord As String
    Public V_LoginCode As String
'    Public V_GlzCode As String
'    Public V_Ybzl_FdLx As String = "无"
'    Public V_Ybzl_BcDj As Double = 0
'    Public V_Ybzl_FdJe As Double = 0
    Dim My_Ada As New SqlDataAdapter
    Public AllQx_Dst As New DataSet
    Public encode As New EnCode.EnCode

    Sub Main()

        '处理未捕获的异常
        Application.SetUnhandledExceptionMode(UnhandledExceptionMode.CatchException)
        '处理UI线程异常
        AddHandler Application.ThreadException, New System.Threading.ThreadExceptionEventHandler(AddressOf Application_ThreadException)
        '处理非UI线程异常
        AddHandler AppDomain.CurrentDomain.UnhandledException, New UnhandledExceptionEventHandler(AddressOf CurrentDomain_UnhandledException)

        StiConfig.LoadLocalization(Application.StartupPath & "\zh-CHS.xml")
        HisVar.HisVar.Parapath = Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData) & "\TsztXzHisPara\"

        If System.IO.Directory.Exists(HisVar.HisVar.Parapath) Then

        Else
            System.IO.Directory.CreateDirectory(HisVar.HisVar.Parapath)
        End If


        Call CreateDesktopShortCut()
        Application.EnableVisualStyles()
        Application.VisualStyleState = System.Windows.Forms.VisualStyles.VisualStyleState.ClientAndNonClientAreasEnabled

        'If UBound(Process.GetProcessesByName(Process.GetCurrentProcess.ProcessName)) > 0 Then
        '    MsgBox("一台电脑只允许同时运行一个医院管理系统实例!", MsgBoxStyle.Critical, "警告")
        '    Exit Sub
        '    'Application.Exit()
        'End If

        If F_Login.ShowDialog = DialogResult.Cancel Then
            Application.Exit()
        Else
            Version_Init()
        End If
    End Sub

#Region "公用函数"

    Private Sub CreateDesktopShortCut()

        Dim path As String = System.Environment.GetFolderPath(Environment.SpecialFolder.StartMenu)
        If Not path.EndsWith("\") Then
            path += "\"
        End If
        path += "程序\中软智通（唐山）科技有限公司"
        If System.IO.Directory.Exists(path) Then
            Dim desktop As String = System.Environment.GetFolderPath(Environment.SpecialFolder.DesktopDirectory)
            If Not desktop.EndsWith("\") Then
                desktop += "\"
            End If
            For Each file As [String] In System.IO.Directory.GetFiles(path)
                Dim fi As New System.IO.FileInfo(file)
                If Not System.IO.File.Exists(desktop & fi.Name) Then
                    fi.CopyTo(desktop & fi.Name)
                End If
            Next
        End If
    End Sub

    Private Sub Version_Init()
        Dim frm As New MainForm
        'Dim frm As New F_Main
        If My.Application.IsNetworkDeployed Then
            frm.Text = "【" + HisVar.HisVar.WsyName + "】医院管理系统(版本号: " &
                        My.Application.Deployment.CurrentVersion.Major & "." &
                        My.Application.Deployment.CurrentVersion.Minor & "." &
                        My.Application.Deployment.CurrentVersion.MajorRevision & "." &
                        My.Application.Deployment.CurrentVersion.MinorRevision & ")当前操作员:【" & HisVar.HisVar.JsrName & "】"

        Else
            frm.Text = "【" + HisVar.HisVar.WsyName + "】医院管理系统(版本号: " & My.Application.Info.Version.ToString() & ")当前操作员:【" & HisVar.HisVar.JsrName & "】"
        End If
        Application.Run(frm)
    End Sub

    Public Sub P_Conn(ByVal V_State As Boolean)
        If V_State = True Then
            If My_Cn.State = ConnectionState.Closed Then My_Cn.Open()
        Else
            If My_Cn.State = ConnectionState.Open Then My_Cn.Close()
        End If

    End Sub

    Public Sub P_Column(ByVal V_Table As DataTable, ByVal V_Name As String, ByVal V_Type As String)        '数据类型:Boolean,Byte,Char
        Dim V_Column As DataColumn = New DataColumn()
        V_Column.ColumnName = V_Name

        Select Case V_Type
            Case "Boolean"
                V_Column.DataType = System.Type.GetType("System.Boolean")
            Case "Byte"
                V_Column.DataType = System.Type.GetType("System.Byte")
            Case "Char"
                V_Column.DataType = System.Type.GetType("System.Char")
            Case "String"
                V_Column.DataType = System.Type.GetType("System.String")
            Case "Int16"
                V_Column.DataType = System.Type.GetType("System.Int16")
            Case "Int32"
                V_Column.DataType = System.Type.GetType("System.Int32")
            Case "Int64"
                V_Column.DataType = System.Type.GetType("System.Int64")
            Case "Single"
                V_Column.DataType = System.Type.GetType("System.Single")
            Case "Double"
                V_Column.DataType = System.Type.GetType("System.Double")
            Case "Decimal"
                V_Column.DataType = System.Type.GetType("System.Decimal")
            Case "DateTime"
                V_Column.DataType = System.Type.GetType("System.DateTime")
        End Select

        V_Table.Columns.Add(V_Column)

    End Sub

    Public Sub Flex_Init(ByVal My_Flex As C1.Win.C1FlexGrid.C1FlexGrid, ByVal Col_Count As Integer)
        With My_Flex
            .Clear()
            .VisualStyle = VisualStyle.Office2010Blue
            ' .Dock = DockStyle.Right
            .AllowDelete = False
            .AllowEditing = False
            .AutoResize = True
            .AllowSorting = AllowSortingEnum.SingleColumn
            .AllowDragging = AllowDraggingEnum.None
            .AllowResizing = C1.Win.C1FlexGrid.AllowResizingEnum.None
            .AllowMerging = C1.Win.C1FlexGrid.AllowMergingEnum.Free
            .BorderStyle = C1.Win.C1FlexGrid.Util.BaseControls.BorderStyleEnum.Fixed3D

            .ExtendLastCol = True
            .FocusRect = FocusRectEnum.None
            .SelectionMode = C1.Win.C1FlexGrid.SelectionModeEnum.ListBox
            With .Tree
                .Column = 0
                .Indent = 20
                .Style = TreeStyleFlags.CompleteLeaf
                .LineColor = Color.DarkRed
                .LineStyle = Drawing2D.DashStyle.Solid
            End With

            '类型()
            With .Styles.Fixed
                .WordWrap = False
                .Border.Style = C1.Win.C1FlexGrid.BorderStyleEnum.Raised
                .TextAlign = TextAlignEnum.CenterCenter
                .Margins.Top = 1
                .Margins.Bottom = 0
                '.BackColor = Color.FromArgb(0, 78, 152)
                '.ForeColor = Color.FromArgb(255, 255, 255)
            End With

            With .Styles.Highlight
                '.ForeColor = Color.FromArgb(255, 255, 255)
                '.BackColor = Color.FromArgb(49, 106, 197)
            End With

            .Rows.Count = 1
            .Cols.Count = Col_Count
            .Rows.Fixed = 1
            .Cols.Fixed = 0
            .Rows(0).Height = 20
            .Redraw = True
        End With

    End Sub

    Public Sub Flex_Col(ByVal My_Flex As C1.Win.C1FlexGrid.C1FlexGrid, ByVal Col As Integer, ByVal Caption As String, ByVal width As Integer, ByVal Align As String, ByVal Visible As Boolean)

        With My_Flex
            With .Cols(Col)
                .Caption = Caption
                .Width = width
                .AllowMerging = True
                .AllowDragging = True
                ' .Style.WordWrap = True
                .Sort = C1.Win.C1FlexGrid.SortFlags.UseColSort
                .Visible = Visible

                If Caption.Contains("时间") Then
                    .Format = "yyyy-MM-dd"
                End If

                Select Case Align
                    Case "左"
                        .TextAlign = C1.Win.C1FlexGrid.TextAlignEnum.LeftCenter

                    Case "中"
                        .TextAlign = C1.Win.C1FlexGrid.TextAlignEnum.CenterCenter
                    Case "右"
                        .TextAlign = C1.Win.C1FlexGrid.TextAlignEnum.RightCenter

                End Select
            End With
        End With


    End Sub

    Public Sub Flex_Data(ByVal V_Str1 As String, ByVal V_Str2 As String, ByVal Flex_Grid As C1.Win.C1FlexGrid.C1FlexGrid, ByVal Col_Count As Integer)
        P_Conn(True)
        Dim My_Cmd1 As New SqlCommand(V_Str1, My_Cn)
        Dim My_Cmd2 As New SqlCommand(V_Str2, My_Cn)
        Dim V_Count As Integer = My_Cmd1.ExecuteScalar
        Dim My_Reader As SqlDataReader
        Flex_Grid.Rows.Count = V_Count + 1
        Dim I As Integer
        P_Conn(True)
        My_Reader = My_Cmd2.ExecuteReader
        While My_Reader.Read()
            I = I + 1
            Dim J As Integer
            For J = 0 To Col_Count - 1
                Flex_Grid.Item(I, J) = My_Reader.Item(J).ToString
            Next
        End While
        My_Cmd1.Dispose()
        My_Cmd2.Dispose()
        My_Reader.Close()
        P_Conn(False)
    End Sub



#End Region

#Region "权限"
    Public Sub Qx_Init(ByVal Glz_Code As String)
        Dim Str_Select As String = "Select Dl_Name From Zd_QxModule where Module_Code in (Select Module_Code From Zd_Qx2 where Glz_Code='" & Glz_Code & "') Group BY Dl_Name"
        With My_Ada
            .SelectCommand = New SqlCommand(Str_Select, My_Cn)
            .Fill(AllQx_Dst, "大类权限")
        End With

        Str_Select = "Select Xl_Name From Zd_QxModule where Module_Code in (Select Module_Code From Zd_Qx2 where Glz_Code='" & Glz_Code & "')"
        With My_Ada
            .SelectCommand = New SqlCommand(Str_Select, My_Cn)
            .Fill(AllQx_Dst, "小类权限")
        End With
    End Sub

    Public Function Check_DlQx(ByVal Sendtext As String) As Boolean
        For Each My_Row In AllQx_Dst.Tables("大类权限").Rows
            If My_Row.Item("Dl_Name") & "" = Sendtext Then
                Return True
            End If
        Next
    End Function

    Public Function Check_XlQx(ByVal Sendtext As String) As Boolean
        For Each My_Row In AllQx_Dst.Tables("小类权限").Rows
            If My_Row.Item("Xl_Name") = Sendtext Then
                Return True
            End If
        Next
    End Function
#End Region

    Private Sub Application_ThreadException(ByVal sender As Object, ByVal e As System.Threading.ThreadExceptionEventArgs)
        Dim str As String = ""
        Dim strDateInfo As String = "出现应用程序未处理的异常：" + DateTime.Now.ToString() + vbCr & vbLf
        Dim [error] As Exception = TryCast(e.Exception, Exception)
        If [error] IsNot Nothing Then
            str = String.Format(strDateInfo + "异常类型：{0}" & vbCr & vbLf & "异常消息：{1}" & vbCr & vbLf & "异常信息：{2}" & vbCr & vbLf, [error].[GetType]().Name, [error].Message, [error].StackTrace)
        Else
            str = String.Format("应用程序线程错误:{0}", e)
        End If

        MessageBox.Show(str, "系统错误", MessageBoxButtons.OK, MessageBoxIcon.Error)

    End Sub

    Private Sub CurrentDomain_UnhandledException(ByVal sender As Object, ByVal e As UnhandledExceptionEventArgs)
        Dim str As String = ""
        Dim [error] As Exception = TryCast(e.ExceptionObject, Exception)
        Dim strDateInfo As String = "出现应用程序未处理的异常：" + DateTime.Now.ToString() + vbCr & vbLf
        If [error] IsNot Nothing Then
            str = String.Format(strDateInfo + "Application UnhandledException:{0};" & vbLf & vbCr & "堆栈信息:{1}", [error].Message, [error].StackTrace)
        Else
            str = String.Format("Application UnhandledError:{0}", e)
        End If


        MessageBox.Show(str, "系统错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
    End Sub

End Module