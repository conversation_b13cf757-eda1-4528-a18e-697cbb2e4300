/****** Object:  Table [dbo].[Country_YB_JiLinQs]    Script Date: 23-6-15 10:07:23 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[Country_YB_JiLinQs](
	[Qs_Id] [INT] IDENTITY(1,1) NOT NULL,
	[clr_ym] [CHAR](6) NULL,
	[clr_type] [VARCHAR](30) NULL,
	[medfee_sumamt] [DECIMAL](18, 2) NULL,
	[med_sumfee] [DECIMAL](18, 2) NULL,
	[fund_appy_sum] [DECIMAL](18, 2) NULL,
	[cash_payamt] [DECIMAL](18, 2) NULL,
	[acct_pay] [DECIMAL](18, 2) NULL,
	[begndate] [DATETIME] NULL,
	[enddate] [DATETIME] NULL,
	[clr_optins] [CHAR](6) NULL,
	[clr_stas] [VARCHAR](10) NULL,
	[clr_appy_evt_id] [VARCHAR](30) NULL,
	[Jsr_Code] [CHAR](7) NULL,
	[Sq_Date] [DATETIME] NULL,
	[ZfJsr_Code] [CHAR](7) NULL,
	[Zf_Date] [DATETIME] NULL,
 CONSTRAINT [PK_Country_YB_JiLinQs] PRIMARY KEY CLUSTERED 
(
	[Qs_Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO


