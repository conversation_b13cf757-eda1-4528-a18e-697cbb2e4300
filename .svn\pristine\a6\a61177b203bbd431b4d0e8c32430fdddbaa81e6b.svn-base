﻿Imports HisControl

Public Class Cw_Cx_MzZy_Rj2

    Dim My_Cm As CurrencyManager             '同步指针
    Dim My_View As New DataView
#Region "传参"
    'Dim Rform As BaseChild
    'Di<PERSON> As Boolean
    'Dim Rdate As Date
    'Dim <PERSON>row As DataRow
    'Dim RZbtb As DataTable
    'Dim Rtdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    'Dim Rlb As C1.Win.C1Input.C1Label
    'Dim Rzbadt As SqlDataAdapter
    'Dim Rlx As String
    Dim Rrb1 As Boolean
    Dim Rcombo As String
    Dim Rds As DataSet
#End Region

    Public Sub New(ByVal tform As BaseChild, ByVal trb1 As Boolean, ByVal tcombo As String, ByVal tDs As DataSet)

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        'Rform = tform
       
        Rrb1 = trb1
        Rcombo = tcombo
        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Rds = tDs
    End Sub



    '窗体加载事件
    Private Sub Cw_Cx_MzZy_Rj2_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        'Rform.Visible = False
        Call C1TrueDBGrid1_OnInit()
        Call Init_Data()
        Me.Text = Rcombo & "--" & "日结信息"
    End Sub


#Region "控件__动作"

    '查询文本框内容发生改变的事件
    Private Sub C1TextBox1_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1TextBox1.TextChanged
        My_View = My_Cm.List

        Dim V_RowFilter As String = ""

        If Trim(Me.C1TextBox1.Text & "") = "" Then
            V_RowFilter = ""
        Else
            V_RowFilter = "Jsr_Name Like '%" & LTrim(RTrim(C1TextBox1.Text)) & "%'"
        End If

        My_View.RowFilter = V_RowFilter
        Call F_Sum()
    End Sub

    '关闭窗体事件
    Private Sub Cw_Cx_MzZy_Rj2_FormClosed(ByVal sender As System.Object, ByVal e As System.Windows.Forms.FormClosedEventArgs) Handles MyBase.FormClosed
        'Rform.Visible = True
        C1TextBox1.Text = ""
    End Sub

    '详细信息按钮的单击事件
    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click
        Dim My_Row As DataRow
        My_Row = My_Cm.List(C1TrueDBGrid1.Row).Row
        Dim vform As New Cw_Cx_MzZy_Rj3(Me, Rrb1, C1TrueDBGrid1, Rds)
        vform.Tag = "Cw_Cx_MzZy_Rj2"
        BaseFunc.BaseFunc.addTabControl(vform, vform.Text)
    End Sub
#End Region

#Region "自定义方法"

    '数据
    Private Sub Init_Data()

        'C1TrueDBGrid1的初始化
        With C1TrueDBGrid1
            My_Cm = CType(BindingContext(Rds, "门诊住院日结查询"), CurrencyManager)
            .SetDataBinding(Rds, "门诊住院日结查询", True)

            My_View = My_Cm.List
        End With

        'C1Combo1的初始化

    End Sub

    '根据条件添加C1TrueDBGrid1
    Private Sub C1TrueDBGrid1_OnInit()

        Dim My_Grid As New BaseClass.C_Grid(Me.C1TrueDBGrid1)
        With My_Grid
            .Init_Grid()
            .Init_Column("查询类别", "V_Lb", 0, "左", "")
            .Init_Column("结账编码", "Jz_Code", 0, "左", "")
            .Init_Column("日结日期", "Jz_Date", 80, "中", "yyyy-MM-dd")
            .Init_Column("结账时间", "Jz_Time", 80, "中", "HH-mm-ss")
            If Rcombo = "住院" Then
                .Init_Column("出院人次", "Jz_Cy_Rs", 80, "右", "")
                .Init_Column("实收金额", "Jz_Sj_M", 100, "右", "###0.00")
                .Init_Column("上帐金额", "Jz_Sz_M", 100, "右", "###0.00")
            ElseIf Rcombo = "门诊" Then
                .Init_Column("收费金额", "Jz_Mz_Money", 100, "右", "###0.00")
                .Init_Column("退费金额", "Jz_Th_Money", 100, "右", "###0.00")
                .Init_Column("实收金额", "Jz_Sq_Money", 100, "右", "###0.00")
            End If

            .Init_Column("经 手 人", "Jsr_Name", 60, "左", "")
        End With
        C1TrueDBGrid1.Splits(0).DisplayColumns("V_Lb").Visible = False
        C1TrueDBGrid1.Splits(0).DisplayColumns("Jz_Code").Visible = False


        Call Init_Data()
        Call F_Sum()
    End Sub

    Public Overrides Sub F_Sum()
        Dim Sum1 As Double = 0
        Dim Sum2 As Double = 0
        Dim Sum3 As Double = 0
        If Rcombo = "住院" Then
            Sum1 = IIf(My_View.Table.Compute("Sum(Jz_Cy_Rs)", My_View.RowFilter) Is DBNull.Value, 0, My_View.Table.Compute("Sum(Jz_Cy_Rs)", My_View.RowFilter))
            Sum2 = IIf(My_View.Table.Compute("Sum(Jz_Sj_M)", My_View.RowFilter) Is DBNull.Value, 0, My_View.Table.Compute("Sum(Jz_Sj_M)", My_View.RowFilter))
            Sum3 = IIf(My_View.Table.Compute("Sum(Jz_Sz_M)", My_View.RowFilter) Is DBNull.Value, 0, My_View.Table.Compute("Sum(Jz_Sz_M)", My_View.RowFilter))
        ElseIf Rcombo = "门诊" Then
            Sum1 = IIf(My_View.Table.Compute("Sum(Jz_Mz_Money)", My_View.RowFilter) Is DBNull.Value, 0, My_View.Table.Compute("Sum(Jz_Mz_Money)", My_View.RowFilter))
            Sum2 = IIf(My_View.Table.Compute("Sum(Jz_Th_Money)", My_View.RowFilter) Is DBNull.Value, 0, My_View.Table.Compute("Sum(Jz_Th_Money)", My_View.RowFilter))
            Sum3 = IIf(My_View.Table.Compute("Sum(Jz_Sq_Money)", My_View.RowFilter) Is DBNull.Value, 0, My_View.Table.Compute("Sum(Jz_Sq_Money)", My_View.RowFilter))
        End If

        With C1TrueDBGrid1
            .ColumnFooters = True
            .Columns(4).FooterText = Format(Sum1, "###0")
            .Columns(5).FooterText = Format(Sum2, "###0.00")
            .Columns(6).FooterText = Format(Sum3, "###0.00")
        End With


    End Sub
#End Region

End Class