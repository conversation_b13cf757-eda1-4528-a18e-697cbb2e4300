﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class pupdate
    Inherits System.Windows.Forms.Form

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.downloadStatus = New System.Windows.Forms.Label
        Me.ProgressStatus = New System.Windows.Forms.ProgressBar
        Me.SuspendLayout()
        '
        'downloadStatus
        '
        Me.downloadStatus.AutoSize = True
        Me.downloadStatus.Location = New System.Drawing.Point(2, 9)
        Me.downloadStatus.Name = "downloadStatus"
        Me.downloadStatus.Size = New System.Drawing.Size(41, 12)
        Me.downloadStatus.TabIndex = 0
        Me.downloadStatus.Text = "Label1"
        '
        'ProgressStatus
        '
        Me.ProgressStatus.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.ProgressStatus.Location = New System.Drawing.Point(0, 26)
        Me.ProgressStatus.Name = "ProgressStatus"
        Me.ProgressStatus.Size = New System.Drawing.Size(479, 23)
        Me.ProgressStatus.TabIndex = 1
        '
        'pupdate
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(479, 49)
        Me.Controls.Add(Me.ProgressStatus)
        Me.Controls.Add(Me.downloadStatus)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.Name = "pupdate"
        Me.ShowInTaskbar = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "自动更新程序"
        Me.TopMost = True
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents downloadStatus As System.Windows.Forms.Label
    Friend WithEvents ProgressStatus As System.Windows.Forms.ProgressBar
End Class
