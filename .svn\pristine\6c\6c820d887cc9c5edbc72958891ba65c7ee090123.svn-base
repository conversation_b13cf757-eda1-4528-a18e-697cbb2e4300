﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="0" />
    <DataSources isList="true" count="1">
      <台账统计 Ref="2" type="DataTableSource" isKey="true">
        <Alias>台账统计</Alias>
        <Columns isList="true" count="29">
          <value>Mx_Code,System.String</value>
          <value>yp_name,System.String</value>
          <value>Mx_Gg,System.String</value>
          <value>Mx_Cd,System.String</value>
          <value>Mx_CgDw,System.String</value>
          <value>Dl_Name,System.String</value>
          <value>Dl_Code,System.String</value>
          <value>Mx_Money,System.Decimal</value>
          <value>Rk_Money,System.Decimal</value>
          <value>Pf_Money,System.Decimal</value>
          <value>Yf_Money,System.Decimal</value>
          <value>Ks_Money,System.Decimal</value>
          <value>Pd_Money,System.Decimal</value>
          <value>Tk_Money,System.Decimal</value>
          <value>YfTk_Money,System.Decimal</value>
          <value>BsBy_Money,System.Decimal</value>
          <value>Crk_Money,System.Decimal</value>
          <value>Yk_Sl,System.Decimal</value>
          <value>Rk_Sl,System.Decimal</value>
          <value>Pf_Sl,System.Decimal</value>
          <value>Yf_Sl,System.Decimal</value>
          <value>Ks_Sl,System.Decimal</value>
          <value>Pd_Sl,System.Decimal</value>
          <value>Tk_Sl,System.Decimal</value>
          <value>YfTk_Sl,System.Decimal</value>
          <value>BsBy_Sl,System.Decimal</value>
          <value>Crk_Sl,System.Decimal</value>
          <value>Yp_Code,System.String</value>
          <value>Mx_CgDwMx_Money,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>台账统计</Name>
        <NameInSource>台账统计</NameInSource>
      </台账统计>
    </DataSources>
    <Relations isList="true" count="0" />
    <Report isRef="0" />
    <Variables isList="true" count="2">
      <value>,查询时间,查询时间,System.String,,False,False</value>
      <value>,打印时间,打印时间,System.String,,False,False</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="2">
    <Page1 Ref="3" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="9">
        <PageFooterBand1 Ref="4" type="PageFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,18.5,27.7,0.5</ClientRectangle>
          <Components isList="true" count="1">
            <Text38 Ref="5" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,27.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,0</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text38</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>第{PageNumber}页</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text38>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>PageFooterBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </PageFooterBand1>
        <ReportTitleBand1 Ref="6" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,27.7,1.3</ClientRectangle>
          <Components isList="true" count="3">
            <Text7 Ref="7" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.7,13.5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text7</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Text>查询时间：{查询时间}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="8" type="CustomFormat" isKey="true">
                <StringFormat>yyyy-MM-dd HH:mm:ss</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text7>
            <Text8 Ref="9" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,27.7,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>黑体,15,Regular,Point,False,134</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text8</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Text>药库台账统计</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text8>
            <Text17 Ref="10" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>15.3,0.7,12.4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text17</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Text>打印时间：{打印时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text17>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>ReportTitleBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </ReportTitleBand1>
        <HeaderBand1 Ref="11" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,2.5,27.7,0.6</ClientRectangle>
          <Components isList="true" count="12">
            <Text2 Ref="12" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,4.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text2</Name>
              <Page isRef="3" />
              <Parent isRef="11" />
              <Text>药品名称</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text2>
            <Text10 Ref="13" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9.9,0,2.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text10</Name>
              <Page isRef="3" />
              <Parent isRef="11" />
              <Text>采购入库</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text10>
            <Text12 Ref="14" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>12.1,0,2.1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text12</Name>
              <Page isRef="3" />
              <Parent isRef="11" />
              <Text>药品批发</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text12>
            <Text16 Ref="15" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>14.2,0,2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text16</Name>
              <Page isRef="3" />
              <Parent isRef="11" />
              <Text>调拨药房</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text16>
            <Text20 Ref="16" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>18.1,0,2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text20</Name>
              <Page isRef="3" />
              <Parent isRef="11" />
              <Text>药库盘点</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text20>
            <Text26 Ref="17" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>20.1,0,1.9,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text26</Name>
              <Page isRef="3" />
              <Parent isRef="11" />
              <Text>药库退库</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text26>
            <Text28 Ref="18" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>22,0,1.9,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text28</Name>
              <Page isRef="3" />
              <Parent isRef="11" />
              <Text>药房退库</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text28>
            <Text30 Ref="19" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>23.9,0,1.9,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text30</Name>
              <Page isRef="3" />
              <Parent isRef="11" />
              <Text>报损报益</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text30>
            <Text14 Ref="20" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>16.2,0,1.9,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text14</Name>
              <Page isRef="3" />
              <Parent isRef="11" />
              <Text>科室支领</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text14>
            <Text33 Ref="21" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.3,0,2.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <Guid>19172cf0358244eea7c560b2f1550b9c</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text33</Name>
              <Page isRef="3" />
              <Parent isRef="11" />
              <Text>产地</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text33>
            <Text34 Ref="22" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.8,0,2.5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <Guid>bc3686ea282942eb87d7febf3eab610c</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text34</Name>
              <Page isRef="3" />
              <Parent isRef="11" />
              <Text>规格</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text34>
            <Text37 Ref="23" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>25.8,0,1.9,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <Guid>586f27d10f06453f8050d9050958f554</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text37</Name>
              <Page isRef="3" />
              <Parent isRef="11" />
              <Text>出入合计</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text37>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>HeaderBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </HeaderBand1>
        <GroupHeaderBand3 Ref="24" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,3.9,27.7,0.5</ClientRectangle>
          <Components isList="true" count="1">
            <Text67 Ref="25" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,27.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>5f643b975e6145b2be53d5189c1a138e</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text67</Name>
              <Page isRef="3" />
              <Parent isRef="24" />
              <Text>{台账统计.Dl_Name}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="26" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text67>
          </Components>
          <Condition>{台账统计.Dl_Code}</Condition>
          <Conditions isList="true" count="0" />
          <Name>GroupHeaderBand3</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </GroupHeaderBand3>
        <GroupHeaderBand1 Ref="27" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,5.2,27.7,0</ClientRectangle>
          <Components isList="true" count="0" />
          <Condition>{台账统计.yp_name}{台账统计.Mx_Gg}{台账统计.Mx_Cd}</Condition>
          <Conditions isList="true" count="0" />
          <Name>GroupHeaderBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </GroupHeaderBand1>
        <DataBand1 Ref="28" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,6,27.7,0</ClientRectangle>
          <Components isList="true" count="0" />
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>台账统计</DataSourceName>
          <Filters isList="true" count="0" />
          <Name>DataBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Sort isList="true" count="0" />
        </DataBand1>
        <GroupFooterBand1 Ref="29" type="GroupFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,6.8,27.7,0.5</ClientRectangle>
          <Components isList="true" count="12">
            <Text9 Ref="30" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>9.9,0,2.2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text9</Name>
              <Page isRef="3" />
              <Parent isRef="29" />
              <Text>{Sum(GroupHeaderBand1,台账统计.Rk_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="31" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text9>
            <Text11 Ref="32" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>12.1,0,2.1,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text11</Name>
              <Page isRef="3" />
              <Parent isRef="29" />
              <Text>{Sum(GroupHeaderBand1,台账统计.Pf_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="33" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text11>
            <Text15 Ref="34" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>14.2,0,2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text15</Name>
              <Page isRef="3" />
              <Parent isRef="29" />
              <Text>{Sum(GroupHeaderBand1,台账统计.Yf_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="35" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text15>
            <Text19 Ref="36" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>18.1,0,2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text19</Name>
              <Page isRef="3" />
              <Parent isRef="29" />
              <Text>{Sum(GroupHeaderBand1,台账统计.Pd_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="37" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text19>
            <Text25 Ref="38" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>20.1,0,1.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text25</Name>
              <Page isRef="3" />
              <Parent isRef="29" />
              <Text>{Sum(GroupHeaderBand1,台账统计.Tk_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="39" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text25>
            <Text27 Ref="40" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>22,0,1.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text27</Name>
              <Page isRef="3" />
              <Parent isRef="29" />
              <Text>{Sum(GroupHeaderBand1,台账统计.YfTk_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="41" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text27>
            <Text29 Ref="42" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>23.9,0,1.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text29</Name>
              <Page isRef="3" />
              <Parent isRef="29" />
              <Text>{Sum(GroupHeaderBand1,台账统计.BsBy_Sl)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="43" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text29>
            <Text13 Ref="44" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>16.2,0,1.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text13</Name>
              <Page isRef="3" />
              <Parent isRef="29" />
              <Text>{Sum(GroupHeaderBand1,台账统计.Ks_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="45" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text13>
            <Text1 Ref="46" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,4.8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>86bc8fe3127b4958801a76d2f3e362d7</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text1</Name>
              <Page isRef="3" />
              <Parent isRef="29" />
              <Text>{台账统计.yp_name}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="47" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text1>
            <Text35 Ref="48" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>4.8,0,2.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>9979164f64a74b918fb4638edcb7ba53</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text35</Name>
              <Page isRef="3" />
              <Parent isRef="29" />
              <Text>{台账统计.Mx_Gg}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="49" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text35>
            <Text36 Ref="50" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>7.3,0,2.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>260d05e1879c4abf9899b90b810fcde1</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text36</Name>
              <Page isRef="3" />
              <Parent isRef="29" />
              <Text>{台账统计.Mx_Cd}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="51" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text36>
            <Text39 Ref="52" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>25.8,0,1.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>bc28325e59cc4ed98d106bd8b0abdf01</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text39</Name>
              <Page isRef="3" />
              <Parent isRef="29" />
              <Text>{Sum(GroupHeaderBand1,台账统计.Crk_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="53" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text39>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>GroupFooterBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </GroupFooterBand1>
        <GroupFooterBand3 Ref="54" type="GroupFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,8.1,27.7,0.5</ClientRectangle>
          <Components isList="true" count="10">
            <Text68 Ref="55" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,9.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>29098d34c6ce4cd8ba5cbf682d24a554</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text68</Name>
              <OnlyText>True</OnlyText>
              <Page isRef="3" />
              <Parent isRef="54" />
              <Text>金额小计</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text68>
            <Text69 Ref="56" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>9.9,0,2.2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>b5f4d3faf0cd4cacb51a3e9c315dbc34</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text69</Name>
              <Page isRef="3" />
              <Parent isRef="54" />
              <Text>{Sum(GroupHeaderBand3,台账统计.Rk_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="57" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text69>
            <Text70 Ref="58" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>12.1,0,2.1,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>e7b48cb48d6c4f1286ad3e478d24b9f3</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text70</Name>
              <Page isRef="3" />
              <Parent isRef="54" />
              <Text>{Sum(GroupHeaderBand3,台账统计.Pf_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="59" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text70>
            <Text71 Ref="60" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>14.2,0,2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>58b7ed2297744bbdbef1d2513027f23e</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text71</Name>
              <Page isRef="3" />
              <Parent isRef="54" />
              <Text>{Sum(GroupHeaderBand3,台账统计.Yf_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="61" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text71>
            <Text72 Ref="62" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>18.1,0,2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>9ad8e1cc9bdc4453a8506064d7b2c43a</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text72</Name>
              <Page isRef="3" />
              <Parent isRef="54" />
              <Text>{Sum(GroupHeaderBand3,台账统计.Pd_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="63" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text72>
            <Text73 Ref="64" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>20.1,0,1.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>e6a1937f06764aaeb81af8342fb3a585</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text73</Name>
              <Page isRef="3" />
              <Parent isRef="54" />
              <Text>{Sum(GroupHeaderBand3,台账统计.Tk_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="65" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text73>
            <Text74 Ref="66" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>22,0,1.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>7eead3ae338444efa5499f6cdd5b417e</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text74</Name>
              <Page isRef="3" />
              <Parent isRef="54" />
              <Text>{Sum(GroupHeaderBand3,台账统计.YfTk_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="67" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text74>
            <Text75 Ref="68" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>23.9,0,1.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>8872656d30164518b45d22f684af326e</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text75</Name>
              <Page isRef="3" />
              <Parent isRef="54" />
              <Text>{Sum(GroupHeaderBand3,台账统计.BsBy_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="69" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text75>
            <Text76 Ref="70" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>16.2,0,1.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>e3688f2f8ef94de1b8191e0519f67998</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text76</Name>
              <Page isRef="3" />
              <Parent isRef="54" />
              <Text>{Sum(GroupHeaderBand3,台账统计.Ks_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="71" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text76>
            <Text79 Ref="72" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>25.8,0,1.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>58875420ba2d40458eb97bdf42c83e50</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text79</Name>
              <Page isRef="3" />
              <Parent isRef="54" />
              <Text>{Sum(GroupHeaderBand3,台账统计.Crk_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="73" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text79>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>GroupFooterBand3</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </GroupFooterBand3>
        <ReportSummaryBand1 Ref="74" type="ReportSummaryBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,9.4,27.7,0.8</ClientRectangle>
          <Components isList="true" count="10">
            <Text43 Ref="75" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,9.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>1fa5d2ca9445495884b6118a7837e07a</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text43</Name>
              <OnlyText>True</OnlyText>
              <Page isRef="3" />
              <Parent isRef="74" />
              <Text>金额合计</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text43>
            <Text6 Ref="76" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>9.9,0,2.2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>5c80a5c39b1948b0855dbc8182eb5973</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text6</Name>
              <Page isRef="3" />
              <Parent isRef="74" />
              <Text>{Sum(DataBand1,台账统计.Rk_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="77" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text6>
            <Text18 Ref="78" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>12.1,0,2.1,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>cb4ade05c3c94958a2fa093df6cd4941</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text18</Name>
              <Page isRef="3" />
              <Parent isRef="74" />
              <Text>{Sum(DataBand1,台账统计.Pf_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="79" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text18>
            <Text21 Ref="80" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>14.2,0,2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>abab103837c74740b6cf1373595af7e1</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text21</Name>
              <Page isRef="3" />
              <Parent isRef="74" />
              <Text>{Sum(DataBand1,台账统计.Yf_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="81" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text21>
            <Text22 Ref="82" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>18.1,0,2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>6e72301087ba491e882888af3cf9e0b8</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text22</Name>
              <Page isRef="3" />
              <Parent isRef="74" />
              <Text>{Sum(DataBand1,台账统计.Pd_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="83" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text22>
            <Text23 Ref="84" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>20.1,0,1.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>8c4184b94af64da69ccfbdca1387cd40</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text23</Name>
              <Page isRef="3" />
              <Parent isRef="74" />
              <Text>{Sum(DataBand1,台账统计.Tk_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="85" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text23>
            <Text24 Ref="86" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>22,0,1.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>53d85b303e4d4e8cab75a4639e456ebf</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text24</Name>
              <Page isRef="3" />
              <Parent isRef="74" />
              <Text>{Sum(DataBand1,台账统计.YfTk_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="87" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text24>
            <Text31 Ref="88" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>23.9,0,1.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>e54eb505d7f94cab9d8e48b576625e00</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text31</Name>
              <Page isRef="3" />
              <Parent isRef="74" />
              <Text>{Sum(DataBand1,台账统计.BsBy_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="89" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text31>
            <Text32 Ref="90" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>16.2,0,1.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>38ca93a7ef164e50bd4c4bb9f1f54cb9</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text32</Name>
              <Page isRef="3" />
              <Parent isRef="74" />
              <Text>{Sum(DataBand1,台账统计.Ks_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="91" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text32>
            <Text40 Ref="92" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>25.8,0,1.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>6ccf75af9795457a867d20e1a766209b</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text40</Name>
              <Page isRef="3" />
              <Parent isRef="74" />
              <Text>{Sum(DataBand1,台账统计.Crk_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="93" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text40>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>ReportSummaryBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </ReportSummaryBand1>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>b34eae1e5edd44cdb0bd8cc5d316eea3</Guid>
      <Margins>1,1,1,1</Margins>
      <Name>Page1</Name>
      <Orientation>Landscape</Orientation>
      <PageHeight>21</PageHeight>
      <PageWidth>29.7</PageWidth>
      <PaperSize>A4</PaperSize>
      <Report isRef="0" />
      <Watermark Ref="94" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page1>
    <Page2 Ref="95" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="7">
        <PageFooterBand2 Ref="96" type="PageFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,18.5,27.7,0.5</ClientRectangle>
          <Components isList="true" count="1">
            <Text3 Ref="97" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,25.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,0</Font>
              <Guid>b653c9fac0e64675a762dcb7bddf3c75</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text3</Name>
              <Page isRef="95" />
              <Parent isRef="96" />
              <Text>第{PageNumber}页</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text3>
          </Components>
          <Conditions isList="true" count="0" />
          <Guid>db11d8cf177641c88feb0bb671ca01a9</Guid>
          <Name>PageFooterBand2</Name>
          <Page isRef="95" />
          <Parent isRef="95" />
        </PageFooterBand2>
        <ReportTitleBand2 Ref="98" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,27.7,1.3</ClientRectangle>
          <Components isList="true" count="3">
            <Text4 Ref="99" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.7,13.5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <Guid>093170a26f7f4a79b28a1c305a5f398f</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text4</Name>
              <Page isRef="95" />
              <Parent isRef="98" />
              <Text>查询时间：{查询时间}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="100" type="CustomFormat" isKey="true">
                <StringFormat>yyyy-MM-dd HH:mm:ss</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text4>
            <Text5 Ref="101" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,27.7,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>黑体,15,Regular,Point,False,134</Font>
              <Guid>e4bcfd29a8a74bcc8bb7bc0303d74c58</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text5</Name>
              <Page isRef="95" />
              <Parent isRef="98" />
              <Text>药库台账统计（数量统计）</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text5>
            <Text41 Ref="102" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>15.3,0.7,12.4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <Guid>233fa42a290549d298e851e65be24942</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text41</Name>
              <Page isRef="95" />
              <Parent isRef="98" />
              <Text>打印时间：{打印时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text41>
          </Components>
          <Conditions isList="true" count="0" />
          <Guid>5e07ec1647444fe689109dc328edfd33</Guid>
          <Name>ReportTitleBand2</Name>
          <Page isRef="95" />
          <Parent isRef="95" />
        </ReportTitleBand2>
        <HeaderBand2 Ref="103" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,2.5,27.7,0.6</ClientRectangle>
          <Components isList="true" count="13">
            <Text42 Ref="104" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,4.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <Guid>207d250ad8e34e01a64f4bfc7d387af5</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text42</Name>
              <Page isRef="95" />
              <Parent isRef="103" />
              <Text>药品名称</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text42>
            <Text44 Ref="105" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9.9,0,2.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <Guid>f3138f98997e4192b1d0324d6733dafa</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text44</Name>
              <Page isRef="95" />
              <Parent isRef="103" />
              <Text>采购入库</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text44>
            <Text45 Ref="106" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>12.1,0,2.1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <Guid>f804924b78a8497fa7d77cb8e7454d2f</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text45</Name>
              <Page isRef="95" />
              <Parent isRef="103" />
              <Text>药品批发</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text45>
            <Text46 Ref="107" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>14.2,0,2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <Guid>3cc062da83644c04b20d6a0d1e4f0c77</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text46</Name>
              <Page isRef="95" />
              <Parent isRef="103" />
              <Text>调拨药房</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text46>
            <Text47 Ref="108" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>18.1,0,2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <Guid>845c27a38c654274a8c76e1b5f93bd4a</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text47</Name>
              <Page isRef="95" />
              <Parent isRef="103" />
              <Text>药库盘点</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text47>
            <Text51 Ref="109" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>16.2,0,1.9,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <Guid>174fa415194f4f309e9948fff8416b5a</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text51</Name>
              <Page isRef="95" />
              <Parent isRef="103" />
              <Text>科室支领</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text51>
            <Text52 Ref="110" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6.3,0,2.5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <Guid>2b1cd71840f24fe2a84b847394f945e3</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text52</Name>
              <Page isRef="95" />
              <Parent isRef="103" />
              <Text>产地</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text52>
            <Text53 Ref="111" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.2,0,2.1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <Guid>11a2c0e559a7499c9119a441c1e5770f</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text53</Name>
              <Page isRef="95" />
              <Parent isRef="103" />
              <Text>规格</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text53>
            <Text48 Ref="112" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>20.1,0,1.9,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <Guid>704ab1e6b2eb488a8c87143e0d96e843</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text48</Name>
              <Page isRef="95" />
              <Parent isRef="103" />
              <Text>药库退库</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text48>
            <Text49 Ref="113" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>22,0,1.9,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <Guid>fecf0fc2b03f4b4c83831eb921a6034b</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text49</Name>
              <Page isRef="95" />
              <Parent isRef="103" />
              <Text>药房退库</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text49>
            <Text50 Ref="114" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>23.9,0,1.9,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <Guid>8a9c174b84bb4938b526bdc96da38d1b</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text50</Name>
              <Page isRef="95" />
              <Parent isRef="103" />
              <Text>报损报益</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text50>
            <Text54 Ref="115" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>25.8,0,1.9,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <Guid>059d47174a1743b284af2ca7383cdb29</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text54</Name>
              <Page isRef="95" />
              <Parent isRef="103" />
              <Text>出入合计</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text54>
            <Text77 Ref="116" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>8.8,0,1.1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <Guid>43d5402931704911bdfb4b5a296ee995</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text77</Name>
              <Page isRef="95" />
              <Parent isRef="103" />
              <Text>单位</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text77>
          </Components>
          <Conditions isList="true" count="0" />
          <Guid>580d038f957341a6933298d304f4acf1</Guid>
          <Name>HeaderBand2</Name>
          <Page isRef="95" />
          <Parent isRef="95" />
        </HeaderBand2>
        <GroupHeaderBand4 Ref="117" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,3.9,27.7,0.5</ClientRectangle>
          <Components isList="true" count="1">
            <Text80 Ref="118" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,27.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>92f6119876d64e4daceba4d3213f513f</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text80</Name>
              <Page isRef="95" />
              <Parent isRef="117" />
              <Text>{台账统计.Dl_Name}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="119" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text80>
          </Components>
          <Condition>{台账统计.Dl_Code}</Condition>
          <Conditions isList="true" count="0" />
          <Guid>65d421955db640aa8e5f5421aa42077c</Guid>
          <Name>GroupHeaderBand4</Name>
          <Page isRef="95" />
          <Parent isRef="95" />
        </GroupHeaderBand4>
        <GroupHeaderBand2 Ref="120" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,5.2,27.7,0</ClientRectangle>
          <Components isList="true" count="0" />
          <Condition>{台账统计.yp_name}{台账统计.Mx_CgDw}{台账统计.Mx_Gg}{台账统计.Mx_Cd}</Condition>
          <Conditions isList="true" count="0" />
          <Guid>f6cf1c3bb1d64cdfbbdebe18fc87503c</Guid>
          <Name>GroupHeaderBand2</Name>
          <Page isRef="95" />
          <Parent isRef="95" />
        </GroupHeaderBand2>
        <DataBand2 Ref="121" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,6,27.7,0</ClientRectangle>
          <Components isList="true" count="0" />
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>台账统计</DataSourceName>
          <Filters isList="true" count="0" />
          <Guid>73dbae88108b464d841d07eef748fc0d</Guid>
          <Name>DataBand2</Name>
          <Page isRef="95" />
          <Parent isRef="95" />
          <Sort isList="true" count="0" />
        </DataBand2>
        <GroupFooterBand2 Ref="122" type="GroupFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,6.8,27.7,0.5</ClientRectangle>
          <Components isList="true" count="13">
            <Text55 Ref="123" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>9.9,0,2.2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>a8f11f0430f04b61b603dec2320fbe53</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text55</Name>
              <Page isRef="95" />
              <Parent isRef="122" />
              <Text>{Sum(DataBand2,台账统计.Rk_Sl)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="124" type="CustomFormat" isKey="true">
                <StringFormat>0.#####</StringFormat>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text55>
            <Text56 Ref="125" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>12.1,0,2.1,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>261a9090a35f49dbac1fff38f59ea88a</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text56</Name>
              <Page isRef="95" />
              <Parent isRef="122" />
              <Text>{Sum(DataBand2,台账统计.Pf_Sl)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="126" type="CustomFormat" isKey="true">
                <StringFormat>0.#####</StringFormat>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text56>
            <Text57 Ref="127" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>14.2,0,2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>d335310b393c459e9e45f4a8830653d6</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text57</Name>
              <Page isRef="95" />
              <Parent isRef="122" />
              <Text>{Sum(DataBand2,台账统计.Yf_Sl)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="128" type="CustomFormat" isKey="true">
                <StringFormat>0.#####</StringFormat>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text57>
            <Text58 Ref="129" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>18.1,0,2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>c16cee6e53f94e1a856c0559408317b2</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text58</Name>
              <Page isRef="95" />
              <Parent isRef="122" />
              <Text>{Sum(DataBand2,台账统计.Pd_Sl)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="130" type="CustomFormat" isKey="true">
                <StringFormat>0.#####</StringFormat>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text58>
            <Text62 Ref="131" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>16.2,0,1.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>2b160241b61b4e6fa6016833d36ca732</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text62</Name>
              <Page isRef="95" />
              <Parent isRef="122" />
              <Text>{Sum(DataBand2,台账统计.Ks_Sl)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="132" type="CustomFormat" isKey="true">
                <StringFormat>0.#####</StringFormat>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text62>
            <Text63 Ref="133" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,4.2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>508038a5a65d46d1b0377b065320ea4a</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text63</Name>
              <Page isRef="95" />
              <Parent isRef="122" />
              <Text>{台账统计.yp_name}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="134" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text63>
            <Text64 Ref="135" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>4.2,0,2.1,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>7add6281125844758e34a79d9c9dff7a</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text64</Name>
              <Page isRef="95" />
              <Parent isRef="122" />
              <Text>{台账统计.Mx_Gg}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="136" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text64>
            <Text65 Ref="137" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>6.3,0,2.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>9618b42305014ed7b8077963ecf5f2b8</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text65</Name>
              <Page isRef="95" />
              <Parent isRef="122" />
              <Text>{台账统计.Mx_Cd}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="138" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text65>
            <Text59 Ref="139" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>20.1,0,1.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>4650266588834e88b83cc38658cf694d</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text59</Name>
              <Page isRef="95" />
              <Parent isRef="122" />
              <Text>{Sum(DataBand2,台账统计.Tk_Sl)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="140" type="CustomFormat" isKey="true">
                <StringFormat>0.#####</StringFormat>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text59>
            <Text60 Ref="141" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>22,0,1.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>5f952b3109234788a78a785b00c9d542</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text60</Name>
              <Page isRef="95" />
              <Parent isRef="122" />
              <Text>{Sum(DataBand2,台账统计.YfTk_Sl)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="142" type="CustomFormat" isKey="true">
                <StringFormat>0.#####</StringFormat>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text60>
            <Text61 Ref="143" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>23.9,0,1.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>57c3e829c94345ab81edc9927a42a8ae</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text61</Name>
              <Page isRef="95" />
              <Parent isRef="122" />
              <Text>{Sum(DataBand2,台账统计.BsBy_Sl)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="144" type="CustomFormat" isKey="true">
                <StringFormat>0.#####</StringFormat>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text61>
            <Text66 Ref="145" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>25.8,0,1.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>6a8e96b469ed494b8bfd95ffc7447344</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text66</Name>
              <Page isRef="95" />
              <Parent isRef="122" />
              <Text>{Sum(DataBand2,台账统计.Crk_Sl)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="146" type="CustomFormat" isKey="true">
                <StringFormat>0.#####</StringFormat>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text66>
            <Text78 Ref="147" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>8.8,0,1.1,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>262c74c0de96416f8d4bc1af630e5af9</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text78</Name>
              <Page isRef="95" />
              <Parent isRef="122" />
              <Text>{台账统计.Mx_CgDw}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="148" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text78>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>GroupFooterBand2</Name>
          <Page isRef="95" />
          <Parent isRef="95" />
        </GroupFooterBand2>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>d3d7b5013e6f4ed3aaa3a74bd382e4c2</Guid>
      <Margins>1,1,1,1</Margins>
      <Name>Page2</Name>
      <Orientation>Landscape</Orientation>
      <PageHeight>21</PageHeight>
      <PageWidth>29.7</PageWidth>
      <PaperSize>A4</PaperSize>
      <Report isRef="0" />
      <Watermark Ref="149" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page2>
  </Pages>
  <PrinterSettings Ref="150" type="Stimulsoft.Report.Print.StiPrinterSettings" isKey="true" />
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>药库台账统计</ReportAlias>
  <ReportChanged>7/28/2014 3:10:56 PM</ReportChanged>
  <ReportCreated>12/8/2011 4:39:33 PM</ReportCreated>
  <ReportFile>E:\正在进行时\唐山\正在修改版\his2010\his2010\Rpt\药库台账统计.mrt</ReportFile>
  <ReportGuid>31eb7280fc5c43b1b20e7d683f249795</ReportGuid>
  <ReportName>药库台账统计</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2011.2.1026</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class Report : Stimulsoft.Report.StiReport
    {
        public Report()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
        #endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>