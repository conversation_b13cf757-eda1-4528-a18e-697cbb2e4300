﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="EmbedLink" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\EmbedLink.gif;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Icon_1005" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\Icon_1005.png;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Icon_1047" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\Icon_1047.png;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Icon_1469" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\Icon_1469.png;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="TXT11" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\TXT11.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="WIZMENU" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\WIZMENU.BMP;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Z_Edit" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\Z_Edit.BMP;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Z_PROP" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\Z_PROP.BMP;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Z_REPORT" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\Z_REPORT.BMP;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Z_SHOW" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\Z_SHOW.BMP;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Z_编辑" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\Z_编辑.BMP;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="_2" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\2.png;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="_3" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\3.png;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="全部价格修改1" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\全部价格修改1.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="全部价格修改2" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\全部价格修改2.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="全部价格修改3" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\全部价格修改3.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="分娩1" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\分娩1.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="分娩2" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\分娩2.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="分娩3" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\分娩3.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="删除" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\删除.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="删除1" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\删除.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="发送" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\发送.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_保存1" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_保存1.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_保存11" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_保存1.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_保存2" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_保存2.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_保存21" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_保存2.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_保存3" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_保存3.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_保存31" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_保存3.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_全部调拨1" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_全部调拨1.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_全部调拨2" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_全部调拨2.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_全部调拨3" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_全部调拨3.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_发药1" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_发药1.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_发药2" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_发药2.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_发药3" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_发药3.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_取消1" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_取消1.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_取消11" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_取消1.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_取消2" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_取消2.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_取消21" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_取消2.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_取消3" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_取消3.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_取消31" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_取消3.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_完成处方1" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_完成处方1.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_完成处方11" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_完成处方1.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_完成处方2" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_完成处方2.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_完成处方21" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_完成处方2.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_完成处方3" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_完成处方3.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_完成处方31" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_完成处方3.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_完成调拨1" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_完成调拨1.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_完成调拨2" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_完成调拨2.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_完成调拨3" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_完成调拨3.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_快速打印1" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_快速打印1.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_快速打印2" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_快速打印2.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_快速打印3" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_快速打印3.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_接收1" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_接收1.jpg;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_接收2" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_接收2.jpg;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_接收3" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_接收3.jpg;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_数据结算1" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_数据结算1.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_数据结算11" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_数据结算1.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_数据结算2" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_数据结算2.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_数据结算21" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_数据结算2.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_数据结算3" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_数据结算3.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_数据结算31" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_数据结算3.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_查找1" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_查找1.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_查找2" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_查找2.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_查找3" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_查找3.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_申请出院1" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_申请出院1.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_申请出院2" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_申请出院2.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_申请出院3" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_申请出院3.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_药库价格修改1" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_药库价格修改1.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_药库价格修改2" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_药库价格修改2.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_药库价格修改3" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_药库价格修改3.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_药房价格修改1" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_药房价格修改1.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_药房价格修改2" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_药房价格修改2.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_药房价格修改3" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_药房价格修改3.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_请求发药1" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_请求发药1.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_请求发药2" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_请求发药2.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_请求发药3" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_请求发药3.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_退回1" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_退回1.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_退回2" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_退回2.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_退回3" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_退回3.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="增加" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\增加.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="增加1" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\增加.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="完成退库1" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\完成退库1.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="完成退库2" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\完成退库2.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="完成退库3" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\完成退库3.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="打印" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\打印.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="打印处方1" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\打印处方1.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="打印处方11" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\打印处方1.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="打印处方2" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\打印处方2.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="打印处方21" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\打印处方2.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="打印处方3" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\打印处方3.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="打印处方31" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\打印处方3.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="拒收1" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\拒收1.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="拒收2" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\拒收2.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="拒收3" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\拒收3.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="拒绝出院1" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\拒绝出院1.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="拒绝出院2" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\拒绝出院2.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="拒绝出院3" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\拒绝出院3.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="普通住院1" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\普通住院1.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="普通住院2" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\普通住院2.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="普通住院3" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\普通住院3.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="更新" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\更新.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="更新1" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\更新.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="查找" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\查找.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_处置单1" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_处置单1.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_处置单2" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_处置单2.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="命令_处置单3" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\命令_处置单3.bmp;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="写健康卡1" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\写健康卡1.gif;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="写健康卡2" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\写健康卡2.gif;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="写健康卡3" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\写健康卡3.gif;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="boy" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\boy.png;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="girl" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\girl.png;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="申请出院" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\申请出院.png;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="欠费" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\欠费.png;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="已出院" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>Resources\已出院.png;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
</root>