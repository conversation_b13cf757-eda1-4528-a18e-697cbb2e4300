﻿/**  版本信息模板在安装目录下，可自行修改。
* M_LIS_DevPara.cs
*
* 功 能： N/A
* 类 名： M_LIS_DevPara
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/10/17 星期一 下午 1:38:51   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace ModelOld
{
	/// <summary>
	/// M_LIS_DevPara:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class M_LIS_DevPara
	{
		public M_LIS_DevPara()
		{}
		#region Model
		private string _devpara_code;
		private string _dev_name;
		private string _dev_jc;
		private string _dev_model;
		private string _dev_manufacturer;
		private string _paraitem;
		/// <summary>
		/// 
		/// </summary>
		public string DevPara_Code
		{
			set{ _devpara_code=value;}
			get{return _devpara_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Dev_Name
		{
			set{ _dev_name=value;}
			get{return _dev_name;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Dev_Jc
		{
			set{ _dev_jc=value;}
			get{return _dev_jc;}
		}
		/// <summary>
		/// 设备型号
		/// </summary>
		public string Dev_Model
		{
			set{ _dev_model=value;}
			get{return _dev_model;}
		}
		/// <summary>
		/// 生产厂家
		/// </summary>
		public string Dev_Manufacturer
		{
			set{ _dev_manufacturer=value;}
			get{return _dev_manufacturer;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string ParaItem
		{
			set{ _paraitem=value;}
			get{return _paraitem;}
		}
		#endregion Model

	}
}

