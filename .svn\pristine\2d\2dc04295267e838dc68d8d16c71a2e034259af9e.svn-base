﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class Test_Jy1
    Inherits HisControl.BaseForm

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.TestCode = New CustomControl.MyTextBox()
        Me.RyName = New CustomControl.MyTextBox()
        Me.RySfzh = New CustomControl.MyTextBox()
        Me.JkkNo = New CustomControl.MyTextBox()
        Me.RySex = New CustomControl.MySingleComobo()
        Me.TabControlEx1 = New CustomControl.TabControlEx()
        Me.TabPage1 = New System.Windows.Forms.TabPage()
        Me.MyGrid2 = New CustomControl.MyGrid()
        Me.TabPage2 = New System.Windows.Forms.TabPage()
        Me.MyGrid3 = New CustomControl.MyGrid()
        Me.GroupBox1 = New System.Windows.Forms.GroupBox()
        Me.BtnTh = New CustomControl.MyButton()
        Me.BtnClear = New CustomControl.MyButton()
        Me.BtnOk = New CustomControl.MyButton()
        Me.TestState = New System.Windows.Forms.Label()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.TestXm = New CustomControl.MyDtComobo()
        Me.BtnSh = New CustomControl.MyButton()
        Me.Memo = New CustomControl.MyTextBox()
        Me.Diagnose = New CustomControl.MyTextBox()
        Me.CheckTime = New CustomControl.MyDateEdit()
        Me.CheckJsr = New CustomControl.MyDtComobo()
        Me.TestTime = New CustomControl.MyDateEdit()
        Me.TestJsr = New CustomControl.MyDtComobo()
        Me.GetTime = New CustomControl.MyDateEdit()
        Me.GetJsr = New CustomControl.MyDtComobo()
        Me.SendTime = New CustomControl.MyDateEdit()
        Me.TestLb = New CustomControl.MySingleComobo()
        Me.HisCode = New CustomControl.MyTextBox()
        Me.TestSample = New CustomControl.MyDtComobo()
        Me.SendJsr = New CustomControl.MyDtComobo()
        Me.BtnDk = New CustomControl.MyButton()
        Me.BtnPrint = New CustomControl.MyButton()
        Me.BcComobo = New CustomControl.MyDtComobo()
        Me.KsComobo = New CustomControl.MyDtComobo()
        Me.Bxlb = New CustomControl.MyDtComobo()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.RyAgeWeek = New CustomControl.MyNumericEdit()
        Me.RyAgeMonth = New CustomControl.MyNumericEdit()
        Me.RyAge = New CustomControl.MyNumericEdit()
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel()
        Me.BtnSaveValue = New CustomControl.MyButton()
        Me.MyGrid1 = New CustomControl.MyGrid()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.BtnReturn = New CustomControl.MyButton()
        Me.TabControlEx1.SuspendLayout()
        Me.TabPage1.SuspendLayout()
        Me.TabPage2.SuspendLayout()
        Me.GroupBox1.SuspendLayout()
        Me.TableLayoutPanel1.SuspendLayout()
        Me.SuspendLayout()
        '
        'TestCode
        '
        Me.TestCode.Captain = "检验编码"
        Me.TestCode.CaptainBackColor = System.Drawing.Color.Transparent
        Me.TestCode.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.TestCode.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.TestCode.CaptainWidth = 60.0!
        Me.TestCode.ContentForeColor = System.Drawing.Color.Black
        Me.TestCode.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.TestCode.Location = New System.Drawing.Point(6, 36)
        Me.TestCode.Multiline = False
        Me.TestCode.Name = "TestCode"
        Me.TestCode.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.TestCode.ReadOnly = False
        Me.TestCode.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.TestCode.SelectionStart = 0
        Me.TestCode.SelectStart = 0
        Me.TestCode.Size = New System.Drawing.Size(180, 22)
        Me.TestCode.TabIndex = 0
        Me.TestCode.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.TestCode.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        '
        'RyName
        '
        Me.RyName.Captain = "姓    名"
        Me.RyName.CaptainBackColor = System.Drawing.Color.Transparent
        Me.RyName.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.RyName.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.RyName.CaptainWidth = 60.0!
        Me.RyName.ContentForeColor = System.Drawing.Color.Black
        Me.RyName.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.RyName.Location = New System.Drawing.Point(6, 92)
        Me.RyName.Multiline = False
        Me.RyName.Name = "RyName"
        Me.RyName.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.RyName.ReadOnly = False
        Me.RyName.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.RyName.SelectionStart = 0
        Me.RyName.SelectStart = 0
        Me.RyName.Size = New System.Drawing.Size(180, 22)
        Me.RyName.TabIndex = 2
        Me.RyName.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.RyName.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        '
        'RySfzh
        '
        Me.RySfzh.Captain = "身份证号"
        Me.RySfzh.CaptainBackColor = System.Drawing.Color.Transparent
        Me.RySfzh.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.RySfzh.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.RySfzh.CaptainWidth = 60.0!
        Me.RySfzh.ContentForeColor = System.Drawing.Color.Black
        Me.RySfzh.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.RySfzh.Location = New System.Drawing.Point(192, 92)
        Me.RySfzh.Multiline = False
        Me.RySfzh.Name = "RySfzh"
        Me.RySfzh.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.RySfzh.ReadOnly = False
        Me.RySfzh.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.RySfzh.SelectionStart = 0
        Me.RySfzh.SelectStart = 0
        Me.RySfzh.Size = New System.Drawing.Size(208, 22)
        Me.RySfzh.TabIndex = 3
        Me.RySfzh.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.RySfzh.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        '
        'JkkNo
        '
        Me.JkkNo.Captain = "健康卡号"
        Me.JkkNo.CaptainBackColor = System.Drawing.Color.Transparent
        Me.JkkNo.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.JkkNo.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.JkkNo.CaptainWidth = 60.0!
        Me.JkkNo.ContentForeColor = System.Drawing.Color.Black
        Me.JkkNo.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.JkkNo.Location = New System.Drawing.Point(192, 120)
        Me.JkkNo.Multiline = False
        Me.JkkNo.Name = "JkkNo"
        Me.JkkNo.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.JkkNo.ReadOnly = False
        Me.JkkNo.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.JkkNo.SelectionStart = 0
        Me.JkkNo.SelectStart = 0
        Me.JkkNo.Size = New System.Drawing.Size(208, 22)
        Me.JkkNo.TabIndex = 4
        Me.JkkNo.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.JkkNo.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        '
        'RySex
        '
        Me.RySex.Captain = "性    别"
        Me.RySex.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.RySex.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.RySex.CaptainWidth = 60.0!
        Me.RySex.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        Me.RySex.ItemHeight = 16
        Me.RySex.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.RySex.Location = New System.Drawing.Point(6, 120)
        Me.RySex.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.RySex.MinimumSize = New System.Drawing.Size(0, 20)
        Me.RySex.Name = "RySex"
        Me.RySex.ReadOnly = False
        Me.RySex.Size = New System.Drawing.Size(180, 20)
        Me.RySex.TabIndex = 5
        Me.RySex.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'TabControlEx1
        '
        Me.TabControlEx1.ArrowColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(79, Byte), Integer), CType(CType(125, Byte), Integer))
        Me.TabControlEx1.Controls.Add(Me.TabPage1)
        Me.TabControlEx1.Controls.Add(Me.TabPage2)
        Me.TabControlEx1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TabControlEx1.Location = New System.Drawing.Point(3, 40)
        Me.TabControlEx1.Name = "TabControlEx1"
        Me.TabControlEx1.SelectedIndex = 0
        Me.TabControlEx1.Size = New System.Drawing.Size(612, 555)
        Me.TabControlEx1.TabIndex = 1
        '
        'TabPage1
        '
        Me.TabPage1.Controls.Add(Me.MyGrid2)
        Me.TabPage1.Location = New System.Drawing.Point(4, 26)
        Me.TabPage1.Name = "TabPage1"
        Me.TabPage1.Padding = New System.Windows.Forms.Padding(3)
        Me.TabPage1.Size = New System.Drawing.Size(604, 525)
        Me.TabPage1.TabIndex = 0
        Me.TabPage1.Text = "待检人员"
        Me.TabPage1.UseVisualStyleBackColor = True
        '
        'MyGrid2
        '
        Me.MyGrid2.CanCustomCol = False
        Me.MyGrid2.Col = 0
        Me.MyGrid2.ColumnFooters = False
        Me.MyGrid2.DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveRight
        Me.MyGrid2.Dock = System.Windows.Forms.DockStyle.Fill
        Me.MyGrid2.FetchRowStyles = False
        Me.MyGrid2.Location = New System.Drawing.Point(3, 3)
        Me.MyGrid2.Margin = New System.Windows.Forms.Padding(0)
        Me.MyGrid2.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.MyGrid2.Name = "MyGrid2"
        Me.MyGrid2.Size = New System.Drawing.Size(598, 519)
        Me.MyGrid2.TabAction = C1.Win.C1TrueDBGrid.TabActionEnum.ControlNavigation
        Me.MyGrid2.TabIndex = 0
        Me.MyGrid2.Xmlpath = Nothing
        '
        'TabPage2
        '
        Me.TabPage2.Controls.Add(Me.MyGrid3)
        Me.TabPage2.Location = New System.Drawing.Point(4, 26)
        Me.TabPage2.Name = "TabPage2"
        Me.TabPage2.Padding = New System.Windows.Forms.Padding(3)
        Me.TabPage2.Size = New System.Drawing.Size(604, 525)
        Me.TabPage2.TabIndex = 1
        Me.TabPage2.Text = "待审人员"
        Me.TabPage2.UseVisualStyleBackColor = True
        '
        'MyGrid3
        '
        Me.MyGrid3.CanCustomCol = False
        Me.MyGrid3.Col = 0
        Me.MyGrid3.ColumnFooters = False
        Me.MyGrid3.DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveRight
        Me.MyGrid3.Dock = System.Windows.Forms.DockStyle.Fill
        Me.MyGrid3.FetchRowStyles = False
        Me.MyGrid3.Location = New System.Drawing.Point(3, 3)
        Me.MyGrid3.Margin = New System.Windows.Forms.Padding(0)
        Me.MyGrid3.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.MyGrid3.Name = "MyGrid3"
        Me.MyGrid3.Size = New System.Drawing.Size(186, 64)
        Me.MyGrid3.TabAction = C1.Win.C1TrueDBGrid.TabActionEnum.ControlNavigation
        Me.MyGrid3.TabIndex = 0
        Me.MyGrid3.Xmlpath = Nothing
        '
        'GroupBox1
        '
        Me.GroupBox1.Controls.Add(Me.BtnTh)
        Me.GroupBox1.Controls.Add(Me.BtnClear)
        Me.GroupBox1.Controls.Add(Me.BtnOk)
        Me.GroupBox1.Controls.Add(Me.TestState)
        Me.GroupBox1.Controls.Add(Me.Label2)
        Me.GroupBox1.Controls.Add(Me.TestXm)
        Me.GroupBox1.Controls.Add(Me.BtnSh)
        Me.GroupBox1.Controls.Add(Me.Memo)
        Me.GroupBox1.Controls.Add(Me.Diagnose)
        Me.GroupBox1.Controls.Add(Me.CheckTime)
        Me.GroupBox1.Controls.Add(Me.CheckJsr)
        Me.GroupBox1.Controls.Add(Me.TestTime)
        Me.GroupBox1.Controls.Add(Me.TestJsr)
        Me.GroupBox1.Controls.Add(Me.GetTime)
        Me.GroupBox1.Controls.Add(Me.GetJsr)
        Me.GroupBox1.Controls.Add(Me.SendTime)
        Me.GroupBox1.Controls.Add(Me.TestLb)
        Me.GroupBox1.Controls.Add(Me.HisCode)
        Me.GroupBox1.Controls.Add(Me.TestSample)
        Me.GroupBox1.Controls.Add(Me.SendJsr)
        Me.GroupBox1.Controls.Add(Me.BtnDk)
        Me.GroupBox1.Controls.Add(Me.BtnPrint)
        Me.GroupBox1.Controls.Add(Me.BcComobo)
        Me.GroupBox1.Controls.Add(Me.KsComobo)
        Me.GroupBox1.Controls.Add(Me.Bxlb)
        Me.GroupBox1.Controls.Add(Me.Label1)
        Me.GroupBox1.Controls.Add(Me.RyAgeWeek)
        Me.GroupBox1.Controls.Add(Me.RyAgeMonth)
        Me.GroupBox1.Controls.Add(Me.RyAge)
        Me.GroupBox1.Controls.Add(Me.TestCode)
        Me.GroupBox1.Controls.Add(Me.JkkNo)
        Me.GroupBox1.Controls.Add(Me.RySex)
        Me.GroupBox1.Controls.Add(Me.RySfzh)
        Me.GroupBox1.Controls.Add(Me.RyName)
        Me.GroupBox1.Location = New System.Drawing.Point(621, 40)
        Me.GroupBox1.Name = "GroupBox1"
        Me.GroupBox1.Size = New System.Drawing.Size(409, 529)
        Me.GroupBox1.TabIndex = 0
        Me.GroupBox1.TabStop = False
        Me.GroupBox1.Text = "标本信息"
        '
        'BtnTh
        '
        Me.BtnTh.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.BtnTh.DialogResult = System.Windows.Forms.DialogResult.None
        Me.BtnTh.Location = New System.Drawing.Point(294, 455)
        Me.BtnTh.Name = "BtnTh"
        Me.BtnTh.Size = New System.Drawing.Size(79, 30)
        Me.BtnTh.TabIndex = 35
        Me.BtnTh.Text = "拒绝申请"
        '
        'BtnClear
        '
        Me.BtnClear.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.BtnClear.DialogResult = System.Windows.Forms.DialogResult.None
        Me.BtnClear.Location = New System.Drawing.Point(287, 36)
        Me.BtnClear.Name = "BtnClear"
        Me.BtnClear.Size = New System.Drawing.Size(60, 22)
        Me.BtnClear.TabIndex = 34
        Me.BtnClear.Text = "清  空"
        '
        'BtnOk
        '
        Me.BtnOk.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.BtnOk.DialogResult = System.Windows.Forms.DialogResult.None
        Me.BtnOk.Location = New System.Drawing.Point(38, 455)
        Me.BtnOk.Name = "BtnOk"
        Me.BtnOk.Size = New System.Drawing.Size(79, 30)
        Me.BtnOk.TabIndex = 33
        Me.BtnOk.Text = "检验"
        '
        'TestState
        '
        Me.TestState.AutoSize = True
        Me.TestState.Location = New System.Drawing.Point(70, 421)
        Me.TestState.Name = "TestState"
        Me.TestState.Size = New System.Drawing.Size(41, 12)
        Me.TestState.TabIndex = 32
        Me.TestState.Text = "Label3"
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.Location = New System.Drawing.Point(11, 421)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(53, 12)
        Me.Label2.TabIndex = 31
        Me.Label2.Text = "标本状态"
        '
        'TestXm
        '
        Me.TestXm.Captain = "检验项目"
        Me.TestXm.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.TestXm.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.TestXm.CaptainWidth = 60.0!
        Me.TestXm.DataSource = Nothing
        Me.TestXm.ItemHeight = 18
        Me.TestXm.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.TestXm.Location = New System.Drawing.Point(8, 227)
        Me.TestXm.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.TestXm.MinimumSize = New System.Drawing.Size(0, 20)
        Me.TestXm.Name = "TestXm"
        Me.TestXm.ReadOnly = False
        Me.TestXm.Size = New System.Drawing.Size(392, 20)
        Me.TestXm.TabIndex = 30
        Me.TestXm.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'BtnSh
        '
        Me.BtnSh.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.BtnSh.DialogResult = System.Windows.Forms.DialogResult.None
        Me.BtnSh.Location = New System.Drawing.Point(123, 455)
        Me.BtnSh.Name = "BtnSh"
        Me.BtnSh.Size = New System.Drawing.Size(80, 30)
        Me.BtnSh.TabIndex = 28
        Me.BtnSh.Text = "审核"
        '
        'Memo
        '
        Me.Memo.Captain = "备    注"
        Me.Memo.CaptainBackColor = System.Drawing.Color.Transparent
        Me.Memo.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Memo.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.Memo.CaptainWidth = 60.0!
        Me.Memo.ContentForeColor = System.Drawing.Color.Black
        Me.Memo.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.Memo.Location = New System.Drawing.Point(8, 385)
        Me.Memo.Multiline = False
        Me.Memo.Name = "Memo"
        Me.Memo.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.Memo.ReadOnly = False
        Me.Memo.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.Memo.SelectionStart = 0
        Me.Memo.SelectStart = 0
        Me.Memo.Size = New System.Drawing.Size(392, 21)
        Me.Memo.TabIndex = 27
        Me.Memo.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Memo.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        '
        'Diagnose
        '
        Me.Diagnose.Captain = "诊    断"
        Me.Diagnose.CaptainBackColor = System.Drawing.Color.Transparent
        Me.Diagnose.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Diagnose.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.Diagnose.CaptainWidth = 60.0!
        Me.Diagnose.ContentForeColor = System.Drawing.Color.Black
        Me.Diagnose.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.Diagnose.Location = New System.Drawing.Point(8, 358)
        Me.Diagnose.Multiline = False
        Me.Diagnose.Name = "Diagnose"
        Me.Diagnose.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.Diagnose.ReadOnly = False
        Me.Diagnose.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.Diagnose.SelectionStart = 0
        Me.Diagnose.SelectStart = 0
        Me.Diagnose.Size = New System.Drawing.Size(392, 21)
        Me.Diagnose.TabIndex = 26
        Me.Diagnose.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Diagnose.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        '
        'CheckTime
        '
        Me.CheckTime.Captain = "核对时间"
        Me.CheckTime.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.CheckTime.CaptainWidth = 60.0!
        Me.CheckTime.InitialSelection = C1.Win.C1Input.InitialSelectionEnum.CaretAtEnd
        Me.CheckTime.Location = New System.Drawing.Point(198, 332)
        Me.CheckTime.MaximumSize = New System.Drawing.Size(100000000, 20)
        Me.CheckTime.MinimumSize = New System.Drawing.Size(0, 20)
        Me.CheckTime.Name = "CheckTime"
        Me.CheckTime.Size = New System.Drawing.Size(202, 20)
        Me.CheckTime.TabIndex = 25
        Me.CheckTime.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.CheckTime.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.CheckTime.VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.DropDown
        '
        'CheckJsr
        '
        Me.CheckJsr.Captain = "核对医师"
        Me.CheckJsr.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.CheckJsr.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.CheckJsr.CaptainWidth = 60.0!
        Me.CheckJsr.DataSource = Nothing
        Me.CheckJsr.ItemHeight = 18
        Me.CheckJsr.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.CheckJsr.Location = New System.Drawing.Point(8, 332)
        Me.CheckJsr.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.CheckJsr.MinimumSize = New System.Drawing.Size(0, 20)
        Me.CheckJsr.Name = "CheckJsr"
        Me.CheckJsr.ReadOnly = False
        Me.CheckJsr.Size = New System.Drawing.Size(178, 20)
        Me.CheckJsr.TabIndex = 24
        Me.CheckJsr.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'TestTime
        '
        Me.TestTime.Captain = "报告时间"
        Me.TestTime.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.TestTime.CaptainWidth = 60.0!
        Me.TestTime.InitialSelection = C1.Win.C1Input.InitialSelectionEnum.CaretAtEnd
        Me.TestTime.Location = New System.Drawing.Point(198, 306)
        Me.TestTime.MaximumSize = New System.Drawing.Size(100000000, 20)
        Me.TestTime.MinimumSize = New System.Drawing.Size(0, 20)
        Me.TestTime.Name = "TestTime"
        Me.TestTime.Size = New System.Drawing.Size(202, 20)
        Me.TestTime.TabIndex = 23
        Me.TestTime.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.TestTime.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.TestTime.VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.DropDown
        '
        'TestJsr
        '
        Me.TestJsr.Captain = "检验医师"
        Me.TestJsr.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.TestJsr.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.TestJsr.CaptainWidth = 60.0!
        Me.TestJsr.DataSource = Nothing
        Me.TestJsr.ItemHeight = 18
        Me.TestJsr.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.TestJsr.Location = New System.Drawing.Point(8, 306)
        Me.TestJsr.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.TestJsr.MinimumSize = New System.Drawing.Size(0, 20)
        Me.TestJsr.Name = "TestJsr"
        Me.TestJsr.ReadOnly = False
        Me.TestJsr.Size = New System.Drawing.Size(178, 20)
        Me.TestJsr.TabIndex = 22
        Me.TestJsr.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'GetTime
        '
        Me.GetTime.Captain = "采样时间"
        Me.GetTime.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.GetTime.CaptainWidth = 60.0!
        Me.GetTime.InitialSelection = C1.Win.C1Input.InitialSelectionEnum.CaretAtEnd
        Me.GetTime.Location = New System.Drawing.Point(198, 280)
        Me.GetTime.MaximumSize = New System.Drawing.Size(100000000, 20)
        Me.GetTime.MinimumSize = New System.Drawing.Size(0, 20)
        Me.GetTime.Name = "GetTime"
        Me.GetTime.Size = New System.Drawing.Size(202, 20)
        Me.GetTime.TabIndex = 21
        Me.GetTime.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.GetTime.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.GetTime.VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.DropDown
        '
        'GetJsr
        '
        Me.GetJsr.Captain = "采样医师"
        Me.GetJsr.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.GetJsr.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.GetJsr.CaptainWidth = 60.0!
        Me.GetJsr.DataSource = Nothing
        Me.GetJsr.ItemHeight = 18
        Me.GetJsr.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.GetJsr.Location = New System.Drawing.Point(8, 280)
        Me.GetJsr.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.GetJsr.MinimumSize = New System.Drawing.Size(0, 20)
        Me.GetJsr.Name = "GetJsr"
        Me.GetJsr.ReadOnly = False
        Me.GetJsr.Size = New System.Drawing.Size(178, 20)
        Me.GetJsr.TabIndex = 20
        Me.GetJsr.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'SendTime
        '
        Me.SendTime.Captain = "送检时间"
        Me.SendTime.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.SendTime.CaptainWidth = 60.0!
        Me.SendTime.InitialSelection = C1.Win.C1Input.InitialSelectionEnum.CaretAtEnd
        Me.SendTime.Location = New System.Drawing.Point(198, 254)
        Me.SendTime.MaximumSize = New System.Drawing.Size(100000000, 20)
        Me.SendTime.MinimumSize = New System.Drawing.Size(0, 20)
        Me.SendTime.Name = "SendTime"
        Me.SendTime.Size = New System.Drawing.Size(202, 20)
        Me.SendTime.TabIndex = 19
        Me.SendTime.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.SendTime.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.SendTime.VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.DropDown
        '
        'TestLb
        '
        Me.TestLb.Captain = "病人来源"
        Me.TestLb.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.TestLb.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.TestLb.CaptainWidth = 60.0!
        Me.TestLb.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        Me.TestLb.ItemHeight = 16
        Me.TestLb.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.TestLb.Location = New System.Drawing.Point(6, 64)
        Me.TestLb.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.TestLb.MinimumSize = New System.Drawing.Size(0, 20)
        Me.TestLb.Name = "TestLb"
        Me.TestLb.ReadOnly = False
        Me.TestLb.Size = New System.Drawing.Size(180, 20)
        Me.TestLb.TabIndex = 18
        Me.TestLb.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'HisCode
        '
        Me.HisCode.Captain = "处方编码"
        Me.HisCode.CaptainBackColor = System.Drawing.Color.Transparent
        Me.HisCode.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.HisCode.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.HisCode.CaptainWidth = 60.0!
        Me.HisCode.ContentForeColor = System.Drawing.Color.Black
        Me.HisCode.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.HisCode.Location = New System.Drawing.Point(193, 64)
        Me.HisCode.Multiline = False
        Me.HisCode.Name = "HisCode"
        Me.HisCode.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.HisCode.ReadOnly = False
        Me.HisCode.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.HisCode.SelectionStart = 0
        Me.HisCode.SelectStart = 0
        Me.HisCode.Size = New System.Drawing.Size(207, 22)
        Me.HisCode.TabIndex = 17
        Me.HisCode.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.HisCode.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        '
        'TestSample
        '
        Me.TestSample.Captain = "标本类型"
        Me.TestSample.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.TestSample.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.TestSample.CaptainWidth = 60.0!
        Me.TestSample.DataSource = Nothing
        Me.TestSample.ItemHeight = 18
        Me.TestSample.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.TestSample.Location = New System.Drawing.Point(198, 198)
        Me.TestSample.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.TestSample.MinimumSize = New System.Drawing.Size(0, 20)
        Me.TestSample.Name = "TestSample"
        Me.TestSample.ReadOnly = False
        Me.TestSample.Size = New System.Drawing.Size(202, 20)
        Me.TestSample.TabIndex = 0
        Me.TestSample.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'SendJsr
        '
        Me.SendJsr.Captain = "送检医师"
        Me.SendJsr.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.SendJsr.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.SendJsr.CaptainWidth = 60.0!
        Me.SendJsr.DataSource = Nothing
        Me.SendJsr.ItemHeight = 18
        Me.SendJsr.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.SendJsr.Location = New System.Drawing.Point(8, 254)
        Me.SendJsr.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.SendJsr.MinimumSize = New System.Drawing.Size(0, 20)
        Me.SendJsr.Name = "SendJsr"
        Me.SendJsr.ReadOnly = False
        Me.SendJsr.Size = New System.Drawing.Size(178, 20)
        Me.SendJsr.TabIndex = 16
        Me.SendJsr.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'BtnDk
        '
        Me.BtnDk.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.BtnDk.DialogResult = System.Windows.Forms.DialogResult.None
        Me.BtnDk.Location = New System.Drawing.Point(218, 36)
        Me.BtnDk.Name = "BtnDk"
        Me.BtnDk.Size = New System.Drawing.Size(60, 22)
        Me.BtnDk.TabIndex = 15
        Me.BtnDk.Text = "读  卡"
        '
        'BtnPrint
        '
        Me.BtnPrint.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.BtnPrint.DialogResult = System.Windows.Forms.DialogResult.None
        Me.BtnPrint.Location = New System.Drawing.Point(209, 455)
        Me.BtnPrint.Name = "BtnPrint"
        Me.BtnPrint.Size = New System.Drawing.Size(79, 30)
        Me.BtnPrint.TabIndex = 13
        Me.BtnPrint.Text = "打印"
        '
        'BcComobo
        '
        Me.BcComobo.Captain = "床    位"
        Me.BcComobo.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.BcComobo.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.BcComobo.CaptainWidth = 60.0!
        Me.BcComobo.DataSource = Nothing
        Me.BcComobo.ItemHeight = 18
        Me.BcComobo.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.BcComobo.Location = New System.Drawing.Point(6, 201)
        Me.BcComobo.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.BcComobo.MinimumSize = New System.Drawing.Size(0, 20)
        Me.BcComobo.Name = "BcComobo"
        Me.BcComobo.ReadOnly = False
        Me.BcComobo.Size = New System.Drawing.Size(180, 20)
        Me.BcComobo.TabIndex = 12
        Me.BcComobo.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'KsComobo
        '
        Me.KsComobo.Captain = "科    室"
        Me.KsComobo.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.KsComobo.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.KsComobo.CaptainWidth = 60.0!
        Me.KsComobo.DataSource = Nothing
        Me.KsComobo.ItemHeight = 18
        Me.KsComobo.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.KsComobo.Location = New System.Drawing.Point(195, 172)
        Me.KsComobo.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.KsComobo.MinimumSize = New System.Drawing.Size(0, 20)
        Me.KsComobo.Name = "KsComobo"
        Me.KsComobo.ReadOnly = False
        Me.KsComobo.Size = New System.Drawing.Size(205, 20)
        Me.KsComobo.TabIndex = 11
        Me.KsComobo.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'Bxlb
        '
        Me.Bxlb.Captain = "报销类型"
        Me.Bxlb.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Bxlb.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.Bxlb.CaptainWidth = 60.0!
        Me.Bxlb.DataSource = Nothing
        Me.Bxlb.ItemHeight = 18
        Me.Bxlb.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Bxlb.Location = New System.Drawing.Point(8, 172)
        Me.Bxlb.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.Bxlb.MinimumSize = New System.Drawing.Size(0, 20)
        Me.Bxlb.Name = "Bxlb"
        Me.Bxlb.ReadOnly = False
        Me.Bxlb.Size = New System.Drawing.Size(178, 20)
        Me.Bxlb.TabIndex = 10
        Me.Bxlb.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.Location = New System.Drawing.Point(356, 151)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(17, 12)
        Me.Label1.TabIndex = 9
        Me.Label1.Text = "周"
        '
        'RyAgeWeek
        '
        Me.RyAgeWeek.Captain = "月"
        Me.RyAgeWeek.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.RyAgeWeek.CaptainWidth = 20.0!
        Me.RyAgeWeek.Location = New System.Drawing.Point(259, 146)
        Me.RyAgeWeek.MaximumSize = New System.Drawing.Size(100000, 80)
        Me.RyAgeWeek.MinimumSize = New System.Drawing.Size(0, 20)
        Me.RyAgeWeek.Name = "RyAgeWeek"
        Me.RyAgeWeek.NumFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.RyAgeWeek.ReadOnly = False
        Me.RyAgeWeek.Size = New System.Drawing.Size(91, 20)
        Me.RyAgeWeek.TabIndex = 8
        Me.RyAgeWeek.ValueIsDbNull = False
        '
        'RyAgeMonth
        '
        Me.RyAgeMonth.Captain = "岁"
        Me.RyAgeMonth.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.RyAgeMonth.CaptainWidth = 20.0!
        Me.RyAgeMonth.Location = New System.Drawing.Point(167, 146)
        Me.RyAgeMonth.MaximumSize = New System.Drawing.Size(100000, 80)
        Me.RyAgeMonth.MinimumSize = New System.Drawing.Size(0, 20)
        Me.RyAgeMonth.Name = "RyAgeMonth"
        Me.RyAgeMonth.NumFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.RyAgeMonth.ReadOnly = False
        Me.RyAgeMonth.Size = New System.Drawing.Size(85, 20)
        Me.RyAgeMonth.TabIndex = 7
        Me.RyAgeMonth.ValueIsDbNull = False
        '
        'RyAge
        '
        Me.RyAge.Captain = "年    龄"
        Me.RyAge.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.RyAge.CaptainWidth = 60.0!
        Me.RyAge.Location = New System.Drawing.Point(6, 146)
        Me.RyAge.MaximumSize = New System.Drawing.Size(100000, 80)
        Me.RyAge.MinimumSize = New System.Drawing.Size(0, 20)
        Me.RyAge.Name = "RyAge"
        Me.RyAge.NumFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.RyAge.ReadOnly = False
        Me.RyAge.Size = New System.Drawing.Size(155, 20)
        Me.RyAge.TabIndex = 6
        Me.RyAge.ValueIsDbNull = False
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.ColumnCount = 4
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 618.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 415.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 129.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 255.0!))
        Me.TableLayoutPanel1.Controls.Add(Me.BtnSaveValue, 2, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.TabControlEx1, 0, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.GroupBox1, 1, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.MyGrid1, 2, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.Label3, 0, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.BtnReturn, 3, 0)
        Me.TableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(0, 0)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.RowCount = 2
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 37.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 13.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 20.0!))
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(1417, 598)
        Me.TableLayoutPanel1.TabIndex = 2
        '
        'BtnSaveValue
        '
        Me.BtnSaveValue.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.BtnSaveValue.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.BtnSaveValue.DialogResult = System.Windows.Forms.DialogResult.None
        Me.BtnSaveValue.Location = New System.Drawing.Point(1058, 3)
        Me.BtnSaveValue.Name = "BtnSaveValue"
        Me.BtnSaveValue.Size = New System.Drawing.Size(79, 30)
        Me.BtnSaveValue.TabIndex = 35
        Me.BtnSaveValue.Text = "保存结果"
        '
        'MyGrid1
        '
        Me.MyGrid1.CanCustomCol = False
        Me.MyGrid1.Col = 0
        Me.MyGrid1.ColumnFooters = False
        Me.TableLayoutPanel1.SetColumnSpan(Me.MyGrid1, 2)
        Me.MyGrid1.DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveRight
        Me.MyGrid1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.MyGrid1.FetchRowStyles = False
        Me.MyGrid1.Location = New System.Drawing.Point(1033, 37)
        Me.MyGrid1.Margin = New System.Windows.Forms.Padding(0)
        Me.MyGrid1.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.MyGrid1.Name = "MyGrid1"
        Me.MyGrid1.Size = New System.Drawing.Size(384, 561)
        Me.MyGrid1.TabAction = C1.Win.C1TrueDBGrid.TabActionEnum.ControlNavigation
        Me.MyGrid1.TabIndex = 2
        Me.MyGrid1.Xmlpath = Nothing
        '
        'Label3
        '
        Me.Label3.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.Label3.AutoSize = True
        Me.Label3.Location = New System.Drawing.Point(246, 12)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(125, 12)
        Me.Label3.TabIndex = 0
        Me.Label3.Text = "注：双击选择检验人员"
        '
        'BtnReturn
        '
        Me.BtnReturn.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.BtnReturn.DialogResult = System.Windows.Forms.DialogResult.None
        Me.BtnReturn.Location = New System.Drawing.Point(1165, 3)
        Me.BtnReturn.Name = "BtnReturn"
        Me.BtnReturn.Size = New System.Drawing.Size(76, 30)
        Me.BtnReturn.TabIndex = 36
        Me.BtnReturn.Text = "刷新"
        '
        'Test_Jy1
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1417, 598)
        Me.Controls.Add(Me.TableLayoutPanel1)
        Me.Name = "Test_Jy1"
        Me.Text = "检验申请"
        Me.TabControlEx1.ResumeLayout(False)
        Me.TabPage1.ResumeLayout(False)
        Me.TabPage2.ResumeLayout(False)
        Me.GroupBox1.ResumeLayout(False)
        Me.GroupBox1.PerformLayout()
        Me.TableLayoutPanel1.ResumeLayout(False)
        Me.TableLayoutPanel1.PerformLayout()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents TestCode As CustomControl.MyTextBox
    Friend WithEvents RySfzh As CustomControl.MyTextBox
    Friend WithEvents JkkNo As CustomControl.MyTextBox
    Friend WithEvents RySex As CustomControl.MySingleComobo
    Friend WithEvents RyName As CustomControl.MyTextBox
    Friend WithEvents TabControlEx1 As CustomControl.TabControlEx
    Friend WithEvents TabPage1 As System.Windows.Forms.TabPage
    Friend WithEvents TabPage2 As System.Windows.Forms.TabPage
    Friend WithEvents GroupBox1 As System.Windows.Forms.GroupBox
    Friend WithEvents SendJsr As CustomControl.MyDtComobo
    Friend WithEvents BtnDk As CustomControl.MyButton
    Friend WithEvents BtnPrint As CustomControl.MyButton
    Friend WithEvents BcComobo As CustomControl.MyDtComobo
    Friend WithEvents KsComobo As CustomControl.MyDtComobo
    Friend WithEvents Bxlb As CustomControl.MyDtComobo
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents RyAgeWeek As CustomControl.MyNumericEdit
    Friend WithEvents RyAgeMonth As CustomControl.MyNumericEdit
    Friend WithEvents RyAge As CustomControl.MyNumericEdit
    Friend WithEvents TestSample As CustomControl.MyDtComobo
    Friend WithEvents HisCode As CustomControl.MyTextBox
    Friend WithEvents TableLayoutPanel1 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents MyGrid1 As CustomControl.MyGrid
    Friend WithEvents TestLb As CustomControl.MySingleComobo
    Friend WithEvents BtnSh As CustomControl.MyButton
    Friend WithEvents Memo As CustomControl.MyTextBox
    Friend WithEvents Diagnose As CustomControl.MyTextBox
    Friend WithEvents CheckTime As CustomControl.MyDateEdit
    Friend WithEvents CheckJsr As CustomControl.MyDtComobo
    Friend WithEvents TestTime As CustomControl.MyDateEdit
    Friend WithEvents TestJsr As CustomControl.MyDtComobo
    Friend WithEvents GetTime As CustomControl.MyDateEdit
    Friend WithEvents GetJsr As CustomControl.MyDtComobo
    Friend WithEvents SendTime As CustomControl.MyDateEdit
    Friend WithEvents MyGrid2 As CustomControl.MyGrid
    Friend WithEvents MyGrid3 As CustomControl.MyGrid
    Friend WithEvents TestXm As CustomControl.MyDtComobo
    Friend WithEvents TestState As System.Windows.Forms.Label
    Friend WithEvents Label2 As System.Windows.Forms.Label
    Friend WithEvents BtnOk As CustomControl.MyButton
    Friend WithEvents Label3 As System.Windows.Forms.Label
    Friend WithEvents BtnClear As CustomControl.MyButton
    Friend WithEvents BtnSaveValue As CustomControl.MyButton
    Friend WithEvents BtnReturn As CustomControl.MyButton
    Friend WithEvents BtnTh As CustomControl.MyButton
End Class
