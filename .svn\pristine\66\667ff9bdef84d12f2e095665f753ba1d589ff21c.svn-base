﻿Imports System.Data.SqlClient
Imports BaseClass

Public Class Zd_Czy2

    Dim My_Cc As New BaseClass.C_Cc()                         '取最大编码及简称的类
    Dim My_Dataset As New DataSet

    Private BllKs As New BLLOld.B_Zd_YyKs
    Private BllYs As New BLLOld.B_Zd_YyYs
#Region "传参"
    Di<PERSON> As Boolean
    Dim Rrow As DataRow
    Dim RZbtb As DataTable
    Dim Rtdbgrid As CustomControl.MyGrid
    Dim Rlb As C1.Win.C1Input.C1Label
    Dim Rzbadt As SqlDataAdapter
    Dim Rrc As C_RowChange
    Dim Rdst As DataSet
#End Region

    Public Sub New(ByVal tinsert As Boolean, ByVal trow As DataRow, ByVal tZbtb As DataTable, ByVal ttdbgrid As CustomControl.MyGrid, ByVal tlb As C1.Win.C1Input.C1Label, ByVal tzbadt As SqlDataAdapter, ByRef trc As C_RowChange, ByVal tdst As DataSet)

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        Rinsert = tinsert
        'Rdate = tdate
        Rrow = trow
        RZbtb = tZbtb
        Rtdbgrid = ttdbgrid
        Rlb = tlb
        Rzbadt = tzbadt
        Rrc = trc
        Rdst = tdst
        ' 在 InitializeComponent() 调用之后添加任何初始化。
    End Sub


    Protected Overrides Sub OnKeyDown(ByVal e As System.Windows.Forms.KeyEventArgs)
        MyBase.OnKeyDown(e)
        If e.Control = True And e.KeyCode = Keys.S Then
            Comm1.Select()
            Call Comm_Click(Comm1, Nothing)
        End If
    End Sub

    Private Sub Zd_Czy2_FormClosed(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosedEventArgs) Handles Me.FormClosed
        RemoveHandler Rrc.RowChanged, New BaseClass.RowChangedHandler(AddressOf Data_Show)
        Me.Dispose()
    End Sub

    Private Sub Zd_Czy2_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        AddHandler Rrc.RowChanged, New BaseClass.RowChangedHandler(AddressOf Data_Show)
        Call Form_Init()
        If Rinsert = True Then Call Data_Clear() Else Call Data_Show(Rrow)
    End Sub

#Region "窗体__事件"

    Private Sub Form_Init()
        Panel1.Height = 27

        Me.C1TextBox1.AcceptsEscape = False
        '按扭初始化
        Call P_Comm(Me.Comm1)
        Call P_Comm(Me.Comm2)

        Dim Glz_Combo As New BaseClass.C_Combo2(C1Combo1, Rdst.Tables("管理组权限").DefaultView, "Glz_Name", "Glz_Code", 127)
        With Glz_Combo
            .Init_TDBCombo()
            .Init_Colum("Glz_Name", "管理组", 84, "左")
            .Init_Colum("Glz_Code", "编码", 0, "中")
            .MaxDropDownItems(15)
            .SelectedIndex(-1)
        End With
        With Me.C1Combo1
            .ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
            .Splits(0).DisplayColumns("Glz_Code").Visible = False
            .DropDownWidth = 127
            .Height = 22
            .BorderStyle = BorderStyle.Fixed3D
        End With

        Dim Yf_Combo As New BaseClass.C_Combo2(C1Combo2, Rdst.Tables("药房").DefaultView, "Yf_Name", "Yf_Code", 90)
        With Yf_Combo
            .Init_TDBCombo()
            .Init_Colum("Yf_Name", "药房", 84, "左")
            .Init_Colum("Yf_Code", "编码", 40, "中")
            .MaxDropDownItems(15)
            .SelectedIndex(0)
        End With
        With Me.C1Combo2
            .Splits(0).DisplayColumns("Yf_Code").Visible = False
            .DropDownWidth = 127
            .Height = 22
            .BorderStyle = BorderStyle.Fixed3D
            .ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        End With

        With DoctorDtComobo1
            .DataView = BllYs.GetList("yy_code='" & HisVar.HisVar.WsyCode & "'").Tables(0).DefaultView
            .Init_Colum("Ys_Name", "医生名称", 120, "左")
            .Init_Colum("Ys_Jc", "医生简称", 0, "左")
            .Init_Colum("Ys_Code", "医生编码", 0, "中")
            .DisplayMember = "Ys_Name"
            .ValueMember = "Ys_Code"
            .RowFilterNotTextNull = "Ys_Jc"
            .RowFilterTextNull = ""
            .DroupDownWidth = .Width - .CaptainWidth
            .MaxDropDownItems = 15
            .SelectedValue = -1
        End With

        With KsCombo
            .DataView = BllKs.GetList("yy_code='" & HisVar.HisVar.WsyCode & "'").Tables(0).DefaultView
            .Init_Colum("ks_Name", "科室名称", 120, "左")
            .Init_Colum("ks_Jc", "科室简称", 0, "左")
            .Init_Colum("ks_Code", "科室编码", 0, "中")
            .DisplayMember = "ks_Name"
            .ValueMember = "ks_Code"
            .RowFilterNotTextNull = "ks_Jc"
            .RowFilterTextNull = ""
            .DroupDownWidth = .Width - .CaptainWidth
            .MaxDropDownItems = 15
            .SelectedValue = -1
        End With

    End Sub



#End Region

#Region "数据__操作"

    Private Sub Data_Clear()

        Rinsert = True
        My_Cc.Get_MaxCode("Zd_YyJsr", "Jsr_Code", 7, "Yy_Code", HisVar.HisVar.WsyCode)
        C1TextBox5.Text = My_Cc.编码
        C1TextBox1.Text = ""
        C1TextBox2.Text = ""
        'C1TextBox3.Text = "000"          '初始密码
        C1Combo1.SelectedIndex = -1
        C1Combo2.SelectedIndex = 0
        DoctorDtComobo1.SelectedIndex = -1
        KsCombo.SelectedIndex = -1
        Me.CheckBox1.Checked = False
        L_Dl_Jc.Text = ""

        C1TextBox2.Focus()
        Me.C1TextBox2.Select()
    End Sub

    Private Sub Data_Show(ByVal tmp_Row As DataRow)
        Rinsert = False
        Rrow = tmp_Row
        With Rrow
            C1TextBox5.Text = .Item("Jsr_Code") & ""
            C1TextBox2.Text = .Item("Jsr_Name") & ""
            L_Dl_Jc.Text = .Item("Jsr_Jc") & ""
            C1TextBox1.Text = .Item("Login_Code") & ""
            Me.C1Combo1.SelectedValue = .Item("Glz_Code") & ""
            Me.C1Combo2.SelectedValue = .Item("Yf_Code") & ""
  
            Me.DoctorDtComobo1.SelectedValue = .Item("Ys_Code") & ""
            Me.KsCombo.SelectedValue = .Item("Xm_Ks") & ""
            If .Item("Ys_Code") & "" = "" Then
                Me.CheckBox1.Checked = False
                DoctorDtComobo1.Text = ""
                DoctorDtComobo1.SelectedValue = ""
                DoctorDtComobo1.Enabled = False
            Else
                Me.CheckBox1.Checked = True
            End If
        End With
        Me.C1TextBox2.Select()
    End Sub

#End Region

#Region "控件__动作"

    Private Sub Comm_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.Click, Comm2.Click
        Select Case sender.tag
            Case "保存"


                If C1TextBox2.Text = "" Then
                    MsgBox("请输入操作员姓名！", MsgBoxStyle.Information, "提示")
                    C1TextBox2.Select()
                    Exit Sub
                End If

                If C1TextBox1.Text = "" Then
                    MsgBox("请输入操作员工号！", MsgBoxStyle.Information, "提示")
                    C1TextBox1.Select()
                    Exit Sub
                End If

                If C1Combo1.Text = "" Then
                    MsgBox("请选择权限组！", MsgBoxStyle.Information, "提示")
                    C1Combo1.Select()
                    Exit Sub
                End If

                If CheckBox1.Checked = True And DoctorDtComobo1.SelectedValue & "" = "" Then
                    MsgBox("请选择对应处方医生！", MsgBoxStyle.Information, "提示")
                    DoctorDtComobo1.Select()
                    Exit Sub
                End If

                If HisVar.HisVar.Sqldal.GetSingle("Select Count(*) from Zd_YyJsr where Ys_Code='" & DoctorDtComobo1.SelectedValue & "' and Jsr_Code<>'" & C1TextBox5.Text & "' ") > 0 Then
                    MsgBox("已经存在该医生与操作员的对应！", MsgBoxStyle.Information, "提示")
                    DoctorDtComobo1.Select()
                    Exit Sub
                End If

                If Rinsert = True Then     '增加记录
                    Call Data_Add()
                Else                                '编辑记录
                    Call Data_Edit()
                End If

            Case "取消"
                Me.Close()
        End Select
    End Sub

    Private Sub TextBox_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles C1TextBox1.KeyPress, C1TextBox5.KeyPress, C1Combo1.KeyPress, C1TextBox2.KeyPress, C1Combo2.KeyPress
        If e.KeyChar <> Chr(Keys.Return) Then Exit Sub
        e.Handled = True
        System.Windows.Forms.SendKeys.Send("{Tab}")
    End Sub

#End Region

#Region "数据__编辑"

    Private Sub Data_Add()
        My_Cc.Get_MaxCode("Zd_YyJsr", "Jsr_Code", 7, "Yy_Code", HisVar.HisVar.WsyCode)
        If Trim(C1TextBox1.Text) = "" Then
            MsgBox("请填写用户账号!", MsgBoxStyle.Information, "提示")
            C1TextBox1.Focus()
            Exit Sub
        End If

        P_Conn(True)
        Dim C_LoginCode As Integer
        Dim My_Com As New SqlCommand("select Count(Login_Code) AS Login_Count from Zd_YyJsr where Login_Code='" & Trim(Me.C1TextBox1.Text) & "'", My_Cn)
        Dim My_Read As SqlDataReader = My_Com.ExecuteReader
        While My_Read.Read
            C_LoginCode = My_Read.Item("Login_Count")
        End While
        My_Read.Close()
        P_Conn(False)

        If C_LoginCode > 0 Then
            MsgBox("操作员账号已被使用,请重新输入!", MsgBoxStyle.Exclamation, "提示")
            C1TextBox1.Select()
            Exit Sub
        End If

        Dim My_NewRow As DataRow = RZbtb.NewRow
        With My_NewRow
            .Item("Yy_Code") = HisVar.HisVar.WsyCode
            .Item("Jsr_Code") = Trim(C1TextBox5.Text & "")
            .Item("Jsr_Name") = Trim(C1TextBox2.Text & "")
            .Item("Login_Code") = Trim(C1TextBox1.Text & "")
            .Item("Jsr_Password") = encode.F_Encode("000")
            .Item("Glz_Code") = Me.C1Combo1.SelectedValue
            .Item("Glz_Name") = Me.C1Combo1.Columns("Glz_Name").Value
            .Item("Yf_Code") = Me.C1Combo2.SelectedValue
            .Item("Yf_Name") = Me.C1Combo2.Text
            .Item("Ys_Code") = Me.DoctorDtComobo1.SelectedValue
            .Item("Ys_Name") = Me.DoctorDtComobo1.Text
            .Item("Jsr_Jc") = Trim(L_Dl_Jc.Text & "")
            .Item("Xm_Ks") = Me.KsCombo.SelectedValue
            .Item("Ks_Name") = Me.KsCombo.Text
        End With

        '数据保存

        Try
            RZbtb.Rows.Add(My_NewRow)
            Rtdbgrid.MoveLast()
            Rlb.Text = "∑=" + Rtdbgrid.Splits(0).Rows.Count.ToString
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            Exit Sub
        Finally
            Me.C1TextBox2.Select()
        End Try


        '数据更新
        Try
            With Rzbadt.InsertCommand
                .Parameters(0).Value = My_NewRow.Item("Yy_Code")
                .Parameters(1).Value = My_NewRow.Item("Jsr_Code")
                .Parameters(2).Value = My_NewRow.Item("Jsr_Name")
                .Parameters(3).Value = My_NewRow.Item("Jsr_Jc")
                .Parameters(4).Value = My_NewRow.Item("Login_Code")
                .Parameters(5).Value = My_NewRow.Item("Jsr_Password")
                .Parameters(6).Value = My_NewRow.Item("Glz_Code")
                .Parameters(7).Value = My_NewRow.Item("Yf_Code")
                .Parameters(8).Value = My_NewRow.Item("Ys_Code")
                .Parameters(9).Value = My_NewRow.Item("Xm_Ks")

                Call P_Conn(True)
                .ExecuteNonQuery()
            End With
            My_NewRow.AcceptChanges()
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            Exit Sub
        Finally
            C1TextBox2.Select()
            Call P_Conn(False)
        End Try

        '数据清空
        Call Data_Clear()

    End Sub

    Private Sub Data_Edit()
        Dim My_Row As DataRow = Rrow

        If Trim(C1TextBox1.Text) = "" Then
            MsgBox("请填写用户账号!", MsgBoxStyle.Information, "提示")
            C1TextBox1.Focus()
            Exit Sub
        End If

        P_Conn(True)
        Dim C_LoginCode As Integer
        Dim My_Com As New SqlCommand("select Count(Login_Code) AS Login_Count from Zd_YyJsr where Login_Code='" & Trim(Me.C1TextBox1.Text) & "' and Jsr_Code<> '" & My_Row.Item("Jsr_Code", DataRowVersion.Original) & "'", My_Cn)
        Dim My_Read As SqlDataReader = My_Com.ExecuteReader
        While My_Read.Read
            C_LoginCode = My_Read.Item("Login_Count")
        End While
        My_Read.Close()
        P_Conn(False)

        If C_LoginCode > 0 Then
            MsgBox("操作员账号已被使用,请重新输入!", MsgBoxStyle.Exclamation, "提示")
            C1TextBox1.Select()
            Exit Sub
        End If


        Try
            With My_Row
                .BeginEdit()
                .Item("Yy_Code") = HisVar.HisVar.WsyCode
                .Item("Jsr_Code") = Trim(C1TextBox5.Text & "")
                .Item("Jsr_Name") = Trim(C1TextBox2.Text & "")
                .Item("Jsr_Jc") = Trim(L_Dl_Jc.Text & "")
                .Item("Login_Code") = Trim(C1TextBox1.Text & "")
                .Item("Glz_Code") = Me.C1Combo1.SelectedValue
                .Item("Yf_Code") = Me.C1Combo2.SelectedValue
                .Item("Ys_Code") = Me.DoctorDtComobo1.SelectedValue
                .Item("Ys_Name") = Me.DoctorDtComobo1.Text
                .Item("Yf_Name") = Me.C1Combo2.Text
                .Item("Glz_Name") = Me.C1Combo1.Columns("Glz_Name").Value
                .Item("Xm_Ks") = Me.KsCombo.SelectedValue
                .Item("Ks_Name") = Me.KsCombo.Text
                .EndEdit()
            End With
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Critical, "提示:")
            My_Row.CancelEdit()
            Exit Sub
        Finally
            C1TextBox2.Select()
        End Try

        '数据更新
        Call P_Conn(True)
        With Rzbadt.UpdateCommand
            .Parameters(0).Value = My_Row.Item("Yy_Code")
            .Parameters(1).Value = My_Row.Item("Jsr_Code")
            .Parameters(2).Value = My_Row.Item("Jsr_Name")
            .Parameters(3).Value = My_Row.Item("Jsr_Jc")
            .Parameters(4).Value = My_Row.Item("Login_Code")
            .Parameters(5).Value = My_Row.Item("Glz_Code")
            .Parameters(6).Value = My_Row.Item("Yf_Code")
            .Parameters(7).Value = My_Row.Item("Ys_Code")
            .Parameters(8).Value = My_Row.Item("Xm_Ks")
            .Parameters(9).Value = My_Row.Item("Jsr_Code", DataRowVersion.Original)

            Try
                .ExecuteNonQuery()
                My_Row.AcceptChanges()
            Catch ex As Exception
                Beep()
                MsgBox(ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
            Finally
                Call P_Conn(False)
                C1TextBox2.Select()
            End Try
        End With

    End Sub

#End Region

#Region "自定义按扭"

    Private Sub P_Comm(ByVal Bt As Button)
        With Bt
            Select Case .Tag
                Case "保存"
                    .Top = 3
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")
                    .Size = New Size(52, 24)
                    .Text = "            &B"
                Case "取消"
                    .Location = New Point(Comm1.Left + Comm1.Width, Comm1.Top)
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
                    .Size = New Size(52, 24)
                    .Text = "            &C"
            End Select
            .FlatStyle = FlatStyle.Flat
            .FlatAppearance.BorderSize = 0
            .BackgroundImageLayout = ImageLayout.Center
        End With

    End Sub

    Private Sub Comm_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles Comm1.KeyDown, Comm2.KeyDown
        If e.KeyCode = Keys.Space Then
            Select Case sender.tag
                Case "保存"
                    Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存3")

                Case "取消"
                    Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消3")
            End Select
        End If
    End Sub

    Private Sub Comm_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles Comm1.KeyUp, Comm2.KeyUp
        If e.KeyCode = Keys.Enter Or e.KeyCode = Keys.Space Then
            Select Case sender.tag
                Case "保存"
                    Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")
                Case "取消"
                    Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
            End Select
        End If

    End Sub

    Private Sub Comm_MouseEnter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.MouseEnter, Comm2.MouseEnter
        Select Case sender.tag
            Case "保存"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存2")
            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消2")
        End Select
    End Sub

    Private Sub Comm_MouseLeave(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.MouseLeave, Comm2.MouseLeave
        Select Case sender.tag
            Case "保存"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")
            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
        End Select
    End Sub

    Private Sub Comm_MouseDown(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm1.MouseDown, Comm2.MouseDown
        Select Case sender.tag
            Case "保存"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存3")

            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消3")
        End Select
    End Sub

    Private Sub Comm_MouseUp(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm1.MouseUp, Comm2.MouseUp
        Select Case sender.tag
            Case "保存"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")
            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
        End Select
    End Sub

#End Region

    Private Sub C1TextBox2_Validated(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1TextBox2.Validated
        My_Cc.Get_Py(Me.C1TextBox2.Text & "")
        L_Dl_Jc.Text = My_Cc.简拚.ToString
    End Sub


    Private Function F_Max_Code(ByVal My_Table As String, ByVal My_Coloum As String, ByVal Start_Str As String)
        Dim Str As String = "Select  Max( " & My_Coloum & ") from " & My_Table & "  where left( " & My_Coloum & ",4) = '" & Start_Str & "'"
        Dim My_Code As Integer
        Dim Cmd As New SqlCommand(Str, My_Cn)
        P_Conn(True)
        If Cmd.ExecuteScalar.ToString = "" Then
            My_Code = 0
        Else
            My_Code = Cmd.ExecuteScalar
        End If
        P_Conn(False)
        Return My_Code + 1

    End Function

    '中文
    Private Sub C1TextBox2_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1TextBox2.GotFocus, C1Combo1.GotFocus, C1Combo2.GotFocus
        InputLanguage.CurrentInputLanguage = HisVar.HisVar.InputCode
    End Sub


    Private Sub CheckBox1_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox1.CheckedChanged
        If Me.CheckBox1.Checked = True Then
            DoctorDtComobo1.Enabled = True
        Else
            DoctorDtComobo1.Text = ""
            DoctorDtComobo1.SelectedValue = ""
            DoctorDtComobo1.Enabled = False
        End If
    End Sub

   
    Private Sub DoctorDtComobo1_RowChange(sender As System.Object, e As System.EventArgs) Handles DoctorDtComobo1.RowChange
        KsCombo.SelectedValue = DoctorDtComobo1.Columns("Ks_Code").Value
    End Sub
End Class