﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class MsgNotify
    Inherits CustomControl.SkinForm

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Me.TimerUp = New System.Windows.Forms.Timer(Me.components)
        Me.TimerStay = New System.Windows.Forms.Timer(Me.components)
        Me.TimerDown = New System.Windows.Forms.Timer(Me.components)
        Me.Content = New System.Windows.Forms.Label()
        Me.SuspendLayout()
        '
        'TimerUp
        '
        Me.TimerUp.Interval = 10
        '
        'TimerStay
        '
        Me.TimerStay.Interval = 10
        '
        'TimerDown
        '
        Me.TimerDown.Interval = 10
        '
        'Content
        '
        Me.Content.BackColor = System.Drawing.SystemColors.ButtonHighlight
        Me.Content.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Content.Location = New System.Drawing.Point(3, 24)
        Me.Content.Name = "Content"
        Me.Content.Size = New System.Drawing.Size(246, 173)
        Me.Content.TabIndex = 0
        Me.Content.Text = "Label1"
        '
        'MsgNotify
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(252, 200)
        Me.Controls.Add(Me.Content)
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.Name = "MsgNotify"
        Me.ShowInTaskbar = False
        Me.Text = "系统提示"
        Me.TopMost = True
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents TimerUp As System.Windows.Forms.Timer
    Friend WithEvents TimerStay As System.Windows.Forms.Timer
    Friend WithEvents TimerDown As System.Windows.Forms.Timer
    Friend WithEvents Content As System.Windows.Forms.Label
End Class
