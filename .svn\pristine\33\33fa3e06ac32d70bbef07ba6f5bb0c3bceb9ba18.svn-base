﻿Imports System.Data.SqlClient
Imports Stimulsoft.Report
Imports C1.Win.C1FlexGrid
Imports System.Text
Imports ZTHisPublicFunction
Imports BLL
Imports DTO.Invoice
Imports Model
Imports ZTHisEnum

Public Class Cy_Zh1
    Public My_Adapter As New SqlDataAdapter
    Public My_Dataset As New DataSet
    Public My_Cm As CurrencyManager
    Public My_Row As DataRow
    Dim My_View As DataView
    Dim bllBl_Cfyp As New BllBl_Cfyp()
    Dim bllBl_Cfxm As New BllBl_Cfxm()
    Dim reuslt As String
    Dim _mdlBl As New MdlBl()
    Private Sub Cy_Zh1_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Call Form_Init()
        Call Init_Data()
    End Sub

    Private Sub Form_Init()
        Dim My_Grid As New BaseClass.C_Grid(Me.C1TrueDBGrid1)
        With My_Grid
            .Init_Grid()
            .P_Dock(BaseClass.C_Grid.Dock.Fill)
            .Init_Column("入院编码", "Bl_Code", 100, "中", "")
            .Init_Column("病历号", "Ry_BlCode", 70, "左", "")
            .Init_Column("姓名", "Ry_Name", 65, "左", "")
            .Init_Column("性别", "Ry_Sex", 40, "中", "")
            .Init_Column("患者类别", "Bxlb_Name", 60, "中", "")
            .Init_Column("主治医师", "Ys_Name", 90, "左", "")
            .Init_Column("科室", "Ks_Name", 90, "左", "")
            .Init_Column("疾病", "Jb_Name", 120, "左", "")
            .Init_Column("住院费用", "Bl_M_Sk", 70, "右", "0.00###")
            .Init_Column("预交押金", "Bl_M_Yj", 70, "右", "0.00###")
            .Init_Column("退补押金", "Bl_M_Th", 70, "右", "0.00###")
            .Init_Column("入院日期", "Ry_RyDate", 80, "中", "yyyy-MM-dd")
            .Init_Column("出院日期", "Ry_CyDate", 80, "中", "yyyy-MM-dd")
            .Init_Column("住院天数", "Ry_ZyTs", 60, "右", "0.##")
            .Init_Column("出院经手人", "Jsr_Name", 70, "左", "")
            .Init_Column("日结编码", "Jz_Code", 0, "中", "")

        End With
        C1TrueDBGrid1.Splits(0).DisplayColumns("Jz_Code").Visible = False
        C1TrueDBGrid1.HScrollBar.Style = C1.Win.C1TrueDBGrid.ScrollBarStyleEnum.Automatic
    End Sub

    Private Sub Init_Data()
        Dim Str_Update As String = "Update Bl Set Ry_YlCode=@Ry_YlCode,Bxlb_Code=@Bxlb_Code,Ry_Name=@Ry_Name,Ry_Jc=@Ry_Jc,Ry_Sex=@Ry_Sex,Ry_Sfzh=@Ry_Sfzh,Ry_Csdate=@Ry_Csdate,Ry_Address=@Ry_Address Where Bl_Code=@Bl_Code and Bl.Yy_code='" & HisVar.HisVar.WsyCode & "'"
        With My_Adapter
            .UpdateCommand = New SqlCommand(Str_Update, My_Cn)
            With .UpdateCommand.Parameters
                .Add("@Ry_YlCode", SqlDbType.VarChar, 50, "Ry_YlCode")
                .Add("@Bxlb_Code", SqlDbType.Char, 6, "Bxlb_Code")
                .Add("@Ry_Name", SqlDbType.VarChar, 20, "Ry_Name")
                .Add("@Ry_Jc", SqlDbType.VarChar, 50, "Ry_Jc")
                .Add("@Ry_Sex", SqlDbType.Char, 2, "Ry_Sex")
                .Add("@Ry_Sfzh", SqlDbType.VarChar, 18, "Ry_Sfzh")
                .Add("@Ry_Csdate", SqlDbType.SmallDateTime, 4, "Ry_Csdate")
                .Add("@Ry_Address", SqlDbType.VarChar, 100, "Ry_Address")
                .Add("@Bl_Code", SqlDbType.Char, 14, "Bl_Code")
            End With
        End With

        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "Select Bl_Code,Ry_BlCode,Ry_Name,Ry_Jc,Ry_CsDate,Ry_Tele,Ry_Sfzh,Ry_Address,Bl.Bxlb_Code,Ry_Sex,Bxlb_Name,Ry_YlCode,Ks_Name,Bl_M_Sk,Bl_M_Yj,Bl_M_Th,Ry_RyDate,Ry_CyDate,Ry_ZyTs,Jz_Code,Jsr_Name,Bl_M_Yj as Jf_Money,Bc_Name,DATEDIFF(yyyy,Ry_Csdate,getdate()) AS Ry_Age,Jb_Name,Ys_Name,Ry_MinZu From Zd_Yyks,Zd_YyJsr,Zd_Bxlb,Zd_YyYs,Bl left join Zd_YyBc On Bl.Bc_Code=Zd_YyBc.Bc_Code Where   Bl.Bxlb_Code=Zd_Bxlb.Bxlb_Code and Bl.Ks_Code=Zd_Yyks.Ks_Code and Bl.Yy_Code=Zd_YyKs.Yy_Code and Bl.Yy_Code=Zd_YyJsr.Yy_Code And Bl.Ry_CyJsr=Zd_YyJsr.Jsr_Code AND Bl.Ys_Code=Zd_YyYs.Ys_Code And Isnull(Ry_CyJsr,'')<>'' And Ry_CyDate>='" & Format(DateTimePicker1.Value, "yyyy-MM-dd 00:00:00") & "' And Ry_CyDate<='" & Format(DateTimePicker2.Value, "yyyy-MM-dd 23:59:59") & "'", "出院明细表", True)
        With Me.C1TrueDBGrid1
            My_Cm = CType(BindingContext(My_Dataset, "出院明细表"), CurrencyManager)
            .SetDataBinding(My_Dataset, "出院明细表", True)
            My_View = My_Cm.List
            Label3.Text = "出院人数∑=" + .Splits(0).Rows.Count.ToString
        End With
        Call Footer_Sum()
    End Sub

    Private Sub C1Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Button1.Click
        Call Init_Data()
        If My_Dataset.Tables("出院明细表").Rows.Count = 0 Then
            MsgBox("时间段:" & Format(DateTimePicker1.Value, "yyyy-MM-dd") & "至" & Format(DateTimePicker2.Value, "yyyy-MM-dd") & "未找到出院患者!", MsgBoxStyle.Information, "提示")
        End If
    End Sub

    Private Sub C1Button2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Button2.Click
        If C1TrueDBGrid1.RowCount = 0 Then Exit Sub
        My_Row = My_Cm.List(C1TrueDBGrid1.Row).Row
        If MsgBox("是否确认对该病人进行召回？", vbQuestion + vbYesNo + vbDefaultButton1, "提示:") = vbYes Then
            HisVar.HisVar.Sqldal.ExecuteSql("Insert Into Bl_Zh (Yy_Code,Bl_Code,Cy_Date,Cy_Time,Jsr_Code,Cy_Lb,Bl_M_Th) values ('" & HisVar.HisVar.WsyCode & "','" & My_Row.Item("Bl_Code") & "','" & Format(My_Row.Item("Ry_CyDate"), "yyyy-MM-dd") & "','" & Format(My_Row.Item("Ry_CyDate"), "HH:mm:ss") & "','" & HisVar.HisVar.JsrCode & "','召回'," & My_Row.Item("Bl_M_Th") * -1 & ")")

            If HisPara.PublicConfig.ZyHsz = "是" Then
                HisVar.HisVar.Sqldal.ExecuteSql("Update Bl Set Ry_CyJsr=null,Ry_CyDate=Null,Cy_Qr='否',Ry_ZyTs=null Where Yy_Code='" & HisVar.HisVar.WsyCode & "' and Bl_Code='" & My_Row.Item("Bl_Code") & "'")
            Else
                HisVar.HisVar.Sqldal.ExecuteSql("Update Bl Set Ry_CyJsr=null,Ry_CyDate=Null,Ry_ZyTs=Null Where Yy_Code='" & HisVar.HisVar.WsyCode & "' and Bl_Code='" & My_Row.Item("Bl_Code") & "'")
            End If

            My_Row.Delete()
        End If
        'If My_Row("Jz_Code") & "" <> "" Then
        '    MsgBox("病人住院信息已日结封帐，如需召回请重新办理住院手续!", MsgBoxStyle.Critical, "提示")
        '    Exit Sub
        'End If

    End Sub

    Private Sub C1Button3_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Button3.Click
        If C1TrueDBGrid1.RowCount = 0 Then Exit Sub
        My_Row = My_Cm.List(C1TrueDBGrid1.Row).Row

        '        Dim vform As New Cy_Zh21(My_Row, My_Adapter)
        '        If BaseFunc.BaseFunc.CheckOwnForm(Me, vform) = False Then
        '            vform.ShowDialog()
        '        End If
        Dim vform As New ZTHisInpatient.BlEdit(My_Row)
        vform.ShowDialog()
    End Sub

    Private Sub C1Button4_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Button4.Click
        If C1TrueDBGrid1.RowCount = 0 Then Exit Sub
        My_Row = My_Cm.List(C1TrueDBGrid1.Row).Row

        Dim outInvoicePara_NeiMengGu As New InvoicePara()
        outInvoicePara_NeiMengGu.患者姓名 = My_Row("Ry_Name")
        outInvoicePara_NeiMengGu.病人ID = My_Row("Bl_Code")
        outInvoicePara_NeiMengGu.住院号 = My_Row("Ry_BlCode")
        outInvoicePara_NeiMengGu.科室 = My_Row("Ks_Name")
        outInvoicePara_NeiMengGu.性别 = My_Row("Ry_Sex")
        outInvoicePara_NeiMengGu.入院日期 = Format(My_Row("Ry_RyDate"), "yyyy-MM-dd")
        outInvoicePara_NeiMengGu.出院日期 = Format(My_Row("Ry_CyDate"), "yyyy-MM-dd")
        outInvoicePara_NeiMengGu.住院天数 = My_Row("Ry_ZyTs")
        outInvoicePara_NeiMengGu.预交金总额 = My_Row("Bl_M_Yj")
        outInvoicePara_NeiMengGu.结算总额 = My_Row("Bl_M_Sk")
        outInvoicePara_NeiMengGu.应退额 = My_Row("Bl_M_Th")
        outInvoicePara_NeiMengGu.患者类别 = My_Row("Bxlb_Name")
        outInvoicePara_NeiMengGu.社会保障号 = My_Row("Ry_YlCode") & ""
        Select Case True
            Case HisPara.PublicConfig.XqName.Contains("通辽"),
                HisPara.PublicConfig.XqName.Contains("唐山")
                Dim zyfp As ZTHisPublicFunction.InpatientInvoice.IInpatientInvoice
                zyfp = ZTHisPublicFunction.InpatientInvoice.InpatientInvoiceFactory.CreateInpatientInvoiceObject()
                Dim StiRpt As StiReport = zyfp.GetInpatientInvoice(outInvoicePara_NeiMengGu, Nothing, Nothing, reuslt)
                If reuslt <> "OK" Then
                    MessageBox.Show(reuslt, "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
                    Return
                End If
                StiRpt.Show()
            Case HisPara.PublicConfig.XqName.Contains("长春")

                If My_Dataset.Tables("明细表") IsNot Nothing Then My_Dataset.Tables("明细表").Clear()
                With My_Adapter
                    .SelectCommand = New SqlCommand("SELECT '1' AS V_Order,Sum(Cf_Sl) AS Cf_Sl,Sum(Bl_CfXm.Cf_Money) as Cf_Money,Lb_Name as Cf_Lb,Bl_Code FROM Zd_JkFl2,Zd_JkFl1,Bl_Cf,Bl_CfXm Where Zd_JkFl2.Lb_Code = Zd_JkFl1.Lb_Code and Bl_Cf.Cf_Code = Bl_CfXm.Cf_Code and Zd_JkFl2.Mx_Code = Bl_CfXm.Xm_Code and Zd_JkFl2.Yy_Code = Bl_Cf.Yy_Code and Bl_Code='" & My_Row.Item("Bl_Code") & "' Group By Lb_Name,Bl_Code", My_Cn)
                    .Fill(My_Dataset, "明细表")
                    .SelectCommand = New SqlCommand("SELECT '2' AS V_Order,Sum(Cf_Sl) AS Cf_Sl,Sum(Bl_Cfyp.Cf_Money) as Cf_Money,Cf_Lb,Bl_Code FROM Bl_Cf,Bl_Cfyp  Where Bl_Cf.Cf_Code = dbo.Bl_Cfyp.Cf_Code and Bl_Code='" & My_Row.Item("Bl_Code") & "' And Cf_Qr='是' Group By Cf_Lb,Bl_Code", My_Cn)
                    .Fill(My_Dataset, "明细表")
                End With

                Dim Zy_top As Double = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\中软智通", "住院上边距", Nothing)
                Dim Zy_left As Double = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\中软智通", "住院左边距", Nothing)

                Dim StiRpt As New StiReport
                'Dim Fp_Row As DataRow

                StiRpt.Load(".\Rpt\吉林省医疗机构住院收费专用票据.mrt")
                StiRpt.ReportName = "吉林省医疗机构住院收费专用票据"

                StiRpt.Pages(0).Margins.Top = Zy_top
                StiRpt.Pages(0).Margins.Bottom = Zy_left


                StiRpt.PrinterSettings.PrinterName = IIf(iniOperate.iniopreate.GetINI("打印机设置", "住院打印机", "", HisVar.HisVar.Parapath & "\Config.ini") & "" = "", StiRpt.PrinterSettings.PrinterName, iniOperate.iniopreate.GetINI("打印机设置", "住院打印机", "", HisVar.HisVar.Parapath & "\Config.ini"))
                StiRpt.Compile()
                StiRpt("医院名称") = ZTHisVar.Var.HosName
                StiRpt("经手人") = HisVar.HisVar.JsrName
                StiRpt("姓名") = My_Row.Item("Ry_Name")
                StiRpt("住院编码") = My_Row.Item("Bl_Code")
                StiRpt("住院号") = My_Row.Item("Ry_BlCode")
                StiRpt("科别") = My_Row.Item("Ks_Name")
                StiRpt("入院日期") = Format(My_Row.Item("Ry_RyDate"), "yyyy-MM-dd HH:mm")
                StiRpt("出院日期") = Format(My_Row.Item("Ry_CyDate"), "yyyy-MM-dd HH:mm")
                StiRpt("打印时间") = Format(Now, "yyyy-MM-dd")
                StiRpt("住院天数") = Format(My_Row.Item("Ry_ZyTs"), "#0")
                StiRpt("费用小写") = Format(My_Row.Item("Bl_M_Sk"), "#0.00")
                StiRpt("住院押金") = Format(My_Row.Item("Bl_M_Yj"), "#0.00")
                StiRpt("退找金额") = Format(My_Row.Item("Bl_M_Th"), "#0.00")

                For Each Me.My_Row In My_Dataset.Tables("明细表").Rows
                    Select Case My_Row("Cf_Lb")
                        Case "床位费"
                            StiRpt("床位费") = CDbl(My_Row.Item("Cf_Money"))
                        Case "取暖费"
                            StiRpt("取暖费") = CDbl(My_Row.Item("Cf_Money"))
                        Case "检查费"
                            StiRpt("检查费") = CDbl(My_Row.Item("Cf_Money"))
                        Case "治疗费"
                            StiRpt("治疗费") = CDbl(My_Row.Item("Cf_Money"))
                        Case "手术费"
                            StiRpt("手术费") = CDbl(My_Row.Item("Cf_Money"))
                        Case "护理费"
                            StiRpt("护理费") = CDbl(My_Row.Item("Cf_Money"))
                        Case "化验费"
                            StiRpt("化验费") = CDbl(My_Row.Item("Cf_Money"))
                        Case "放射费"
                            StiRpt("放射费") = CDbl(My_Row.Item("Cf_Money"))
                        Case "处置费"
                            StiRpt("处置费") = CDbl(My_Row.Item("Cf_Money"))
                        Case "输氧费"
                            StiRpt("输氧费") = CDbl(My_Row.Item("Cf_Money"))
                        Case "输血费"
                            StiRpt("输血费") = CDbl(My_Row.Item("Cf_Money"))
                        Case "诊察费"
                            StiRpt("诊察费") = CDbl(My_Row.Item("Cf_Money"))
                        Case "接生费"
                            StiRpt("接生费") = CDbl(My_Row.Item("Cf_Money"))
                        Case "麻醉费"
                            StiRpt("麻醉费") = CDbl(My_Row.Item("Cf_Money"))
                        Case "西药"
                            StiRpt("西药费") = CDbl(StiRpt("化验费") + My_Row.Item("Cf_Money"))         '有疑问
                        Case "中成药"
                            StiRpt("中成药") = CDbl(My_Row.Item("Cf_Money"))
                        Case "中草药"
                            StiRpt("中草药") = CDbl(My_Row.Item("Cf_Money"))
                        Case "卫生材料"
                            StiRpt("西药费") = CDbl(StiRpt("西药费") + My_Row.Item("Cf_Money"))
                    End Select
                Next

                Dim H_Money As Double = StiRpt("费用小写")

                Dim Money_Dx As New BaseClass.ChineseNum
                If H_Money >= 0 Then
                    Money_Dx.InputString = H_Money
                Else
                    Money_Dx.InputString = -H_Money
                End If

                If Money_Dx.Valiad = True Then
                    If H_Money >= 0 Then
                        StiRpt("大写") = Money_Dx.OutString
                    Else
                        StiRpt("大写") = "负" & Money_Dx.OutString
                    End If
                Else
                    MsgBox("输入金额有误,请检查后重新打印", MsgBoxStyle.Critical, "提示")
                    Exit Sub
                End If

                StiRpt.Show()
            Case HisPara.PublicConfig.XqName.Contains("连山区"),
                 HisPara.PublicConfig.XqName.Contains("龙港区"),
                HisPara.PublicConfig.XqName.Contains("南票区"),
                HisPara.PublicConfig.XqName.Contains("杨家杖子经济开发区"),
                HisPara.PublicConfig.XqName.Contains("建昌县"),
                HisPara.PublicConfig.XqName.Contains("兴城市")
                Dim Zy_top As Double = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\中软智通", "住院上边距", Nothing)
                Dim Zy_left As Double = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\中软智通", "住院左边距", Nothing)

                Dim Str As String = "Select Sum(Cf_Sl) as Cf_Sl,Sum(Cf_Money) as Cf_Money,Cf_Lb,Bl_Code from " &
                      "(SELECT Cf_Sl, Bl_Cfxm.Cf_Money,Zd_JkFl1.Lb_Name AS Cf_Lb,Bl_Cf.Bl_Code from Zd_JkFl2,Zd_JkFl1,Bl_Cf,Bl_Cfxm Where Zd_JkFl2.Lb_Code = Zd_JkFl1.Lb_Code and Bl_Cf.Cf_Code = Bl_Cfxm.Cf_Code and Zd_JkFl2.Mx_Code = Bl_Cfxm.Xm_Code AND  Zd_JkFl2.Yy_Code = Bl_Cfxm.Yy_Code and Bl_Code='" & My_Row.Item("Bl_Code") & "' " &
                     "  Union all SELECT Cf_Sl,Bl_Cfyp.Cf_Money as Cf_Money,Case Cf_Lb when '西药' then '西药费' else Cf_Lb end as Cf_Lb,Bl_Code FROM Bl_Cf INNER JOIN Bl_Cfyp ON Bl_Cf.Cf_Code = dbo.Bl_Cfyp.Cf_Code Where Bl_Code='" & My_Row.Item("Bl_Code") & "' And Cf_Qr='是') a Group By Cf_Lb,Bl_Code"
                HisVar.HisVar.Sqldal.QueryDt(My_Dataset, Str, "明细表", True)

                Dim StiRpt As New StiReport
                If HisPara.PublicConfig.ZyFpStyle = 0 Then
                    StiRpt.Load(".\Rpt\辽宁省住院收费票据老版.mrt")
                ElseIf HisPara.PublicConfig.ZyFpStyle = 1 Then
                    StiRpt.Load(".\Rpt\辽宁省住院收费票据.mrt")
                End If
                StiRpt.ReportName = "辽宁省住院收费票据"

                StiRpt.Pages(0).Margins.Top = Zy_top
                StiRpt.Pages(0).Margins.Bottom = Zy_left
                StiRpt.PrinterSettings.PrinterName = IIf(iniOperate.iniopreate.GetINI("打印机设置", "住院打印机", "", HisVar.HisVar.Parapath & "\Config.ini") & "" = "", StiRpt.PrinterSettings.PrinterName, iniOperate.iniopreate.GetINI("打印机设置", "住院打印机", "", HisVar.HisVar.Parapath & "\Config.ini"))
                StiRpt.Compile()
                StiRpt("医疗机构") = ZTHisVar.Var.HosName
                StiRpt("科室") = My_Row.Item("Ks_Name")
                StiRpt("流水号") = My_Row.Item("Bl_Code")
                StiRpt("住院号") = My_Row.Item("Ry_BlCode")
                If HisPara.PublicConfig.ZyFpStyle = 0 Then
                    StiRpt("住院时间") = " " & Format(My_Row.Item("Ry_RyDate"), "yyyy") & "-" & Format(My_Row.Item("Ry_RyDate"), "MM") & "-" & Format(My_Row.Item("Ry_RyDate"), "dd") & "至" & Format(My_Row.Item("Ry_CyDate"), "yyyy") & "-" & Format(My_Row.Item("Ry_CyDate"), "MM") & "-" & Format(My_Row.Item("Ry_CyDate"), "dd")
                ElseIf HisPara.PublicConfig.ZyFpStyle = 1 Then
                    StiRpt("住院时间") = " " & Format(My_Row.Item("Ry_RyDate"), "yyyy") & "    " & Format(My_Row.Item("Ry_RyDate"), "MM") & "    " & Format(My_Row.Item("Ry_RyDate"), "dd") & "     " & Format(My_Row.Item("Ry_CyDate"), "yyyy") & "  " & Format(My_Row.Item("Ry_CyDate"), "MM") & "    " & Format(My_Row.Item("Ry_CyDate"), "dd")
                End If
                StiRpt("住院天数") = Format(My_Row.Item("Ry_ZyTs"), "#0")
                StiRpt("姓名") = My_Row.Item("Ry_Name")
                StiRpt("性别") = My_Row.Item("Ry_Sex")
                StiRpt("医保类型") = My_Row.Item("Bxlb_Name") & ""
                StiRpt("社保号码") = My_Row.Item("Ry_YlCode") & ""

                Dim H_Money As Double = Format(My_Row.Item("Bl_M_Sk"), "#0.00")

                Dim Money_Dx As New BaseClass.ChineseNum
                If H_Money >= 0 Then
                    Money_Dx.InputString = H_Money
                Else
                    Money_Dx.InputString = -H_Money
                End If

                If Money_Dx.Valiad = True Then
                    If H_Money >= 0 Then
                        StiRpt("合计大写") = Money_Dx.OutString
                    Else
                        StiRpt("合计大写") = "负" & Money_Dx.OutString
                    End If
                Else
                    MsgBox("输入金额有误,请检查后重新打印", MsgBoxStyle.Critical, "提示")
                    Exit Sub
                End If

                StiRpt("合计") = Format(My_Row.Item("Bl_M_Sk"), "#0.00")
                StiRpt("预缴金额") = Format(My_Row.Item("Bl_M_Yj"), "#0.00")
                StiRpt("补缴金额") = Format(0, "0.00")
                StiRpt("退费金额") = Format(My_Row.Item("Bl_M_Th"), "#0.00")
                StiRpt("收款人") = HisVar.HisVar.JsrName

                StiRpt("打印时间") = Format(Now, "yyyy-MM-dd HH:mm")


                StiRpt.RegData(My_Dataset.Tables("明细表"))

                '将住院票据打印明细设定为固定个数
                Dim V_Newrow As DataRow

                Dim Tbrowcount As Integer = My_Dataset.Tables("明细表").Rows.Count
                If Tbrowcount Mod 21 <> 0 Then
                    For V_TbRowCount = 1 To 21 - (Tbrowcount Mod 21)
                        V_Newrow = My_Dataset.Tables("明细表").NewRow
                        With V_Newrow
                            .Item(0) = DBNull.Value
                            .Item(1) = DBNull.Value
                        End With
                        My_Dataset.Tables("明细表").Rows.Add(V_Newrow)
                        V_Newrow.AcceptChanges()
                    Next
                End If
                'StiRpt.Design()
                StiRpt.Show()
        End Select
    End Sub

    Private Sub C1Button5_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Button5.Click
        Dim StiRpt As New StiReport
        StiRpt.Load("Rpt\出院明细表.mrt")
        StiRpt.ReportName = "出院明细表"
        StiRpt.RegData(My_View)
        StiRpt.Compile()
        StiRpt("查询时间") = "查询时间:" & Format(DateTimePicker1.Value, "yy年MM月dd日") & "至" & Format(DateTimePicker2.Value, "yy年MM月dd日")
        'StiRpt.Design()
        StiRpt.Show()
    End Sub

    Private Sub C1Button6_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Button6.Click
        If My_Dataset.Tables("出院明细表").Rows.Count = 0 Then Exit Sub
        My_Row = My_Cm.List(C1TrueDBGrid1.Row).Row

        Dim vform As New Cx_Zyhz2(Me, My_Row)
        'Me.Cursor = Cursors.WaitCursor
        vform.Tag = "Cy_Zh1"
        ''vform.Owner = Me
        'vform.MdiParent = F_Main
        'vform.Show()
        'Me.Cursor = Cursors.Default
        BaseFunc.BaseFunc.addTabControl(vform, "住院用药明细-" & My_Row("Bl_Code"))
    End Sub

    Private Sub C1Button7_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Button7.Click
        If My_Dataset.Tables("出院明细表").Rows.Count = 0 Then Exit Sub
        My_Row = My_Cm.List(C1TrueDBGrid1.Row).Row
        Dim vform As New Cx_ZyYjMx(Me, My_Row)
        'Me.Cursor = Cursors.WaitCursor
        vform.Tag = "Cy_Zh1"
        ''vform.Owner = Me
        'vform.MdiParent = F_Main
        'vform.Show()
        'Me.Cursor = Cursors.Default
        BaseFunc.BaseFunc.addTabControl(vform, vform.Text)
    End Sub

    Private Sub C1Button8_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Button8.Click
        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, " select Ks_Name,'X'+Dl_Code as Dl_Code ,Dl_Name,Ry_Name,Yp_Name,Bl_CfYp.Cf_Money ,Bxlb_Name from Bl,Bl_Cf,Bl_CfYp,V_Ypkc,Zd_Yyks,Zd_Bxlb where  Bl.Bxlb_Code=Zd_Bxlb.Bxlb_Code and Bl.Bl_Code=Bl_Cf.Bl_Code And Bl.Yy_Code='" & HisVar.HisVar.WsyCode & "' And Bl_Cf.Cf_Code=Bl_CfYp.Cf_Code and Bl.Ks_Code=Zd_Yyks.Ks_Code and Bl_CfYp.Xx_Code=V_YpKc.Xx_Code  And Isnull(Ry_CyJsr,'')<>''   And Ry_CyDate>='" & Format(DateTimePicker1.Value, "yyyy-MM-dd 00:00:00") & "' And Ry_CyDate<='" & Format(DateTimePicker2.Value, "yyyy-MM-dd 23:59:59") & "'    union all select Ks_Name,'Y'+Zd_Ml_Xm3.Xmlb_Code as Dl_Code,Xmlb_Name as Dl_Name,Ry_Name,Xm_Name as Yp_Name,Bl_CfXm.Cf_Money ,Bxlb_Name from Bl,Bl_Cf,Bl_CfXm,Zd_Ml_Xm3,Zd_Yyks,Zd_Ml_Xm1,Zd_Bxlb where Bl.Bxlb_Code=Zd_Bxlb.Bxlb_Code and Bl.Bl_Code=Bl_Cf.Bl_Code And Bl.Yy_Code='" & HisVar.HisVar.WsyCode & "' And Bl_Cf.Cf_Code=Bl_CfXm.Cf_Code and Bl.Ks_Code=Zd_Yyks.Ks_Code and Bl_CfXm.Xm_Code=Zd_Ml_Xm3.Xm_Code and Zd_Ml_Xm1.Xmlb_Code=Zd_ML_Xm3.Xmlb_Code And Isnull(Ry_CyJsr,'')<>''  And Ry_CyDate>='" & Format(DateTimePicker1.Value, "yyyy-MM-dd 00:00:00") & "' And Ry_CyDate<='" & Format(DateTimePicker2.Value, "yyyy-MM-dd 23:59:59") & "'   Order by Dl_Code", "患者费用统计表", True)

        Dim StiRpt As New StiReport
        StiRpt.Load(".\Rpt\患者费用统计表.mrt")
        StiRpt.ReportName = "患者费用统计表"
        StiRpt.RegData(My_Dataset.Tables("患者费用统计表"))
        StiRpt.Compile()
        StiRpt("查询时间") = "查询时间:" & Format(DateTimePicker1.Value, "yyyy年MM月dd日") & "至" & Format(DateTimePicker2.Value, "yyyy年MM月dd日")
        StiRpt("打印时间") = "打印时间:" & Format(Now, "yyyy-MM-dd HH:mm:ss")
        StiRpt("标题") = "出院患者费用统计"


        ' StiRpt.Design()
        StiRpt.Show()

    End Sub

    Private Sub TextBox1_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles TextBox1.TextChanged
        My_View.RowFilter = " Ry_Jc Like '%" & TextBox1.Text & "%' or  Ry_Name Like '%" & TextBox1.Text & "%' or  Ry_Sfzh Like '%" & TextBox1.Text & "%' or  Ks_Name Like '%" & TextBox1.Text & "%' or  Jsr_Name Like '%" & TextBox1.Text & "%'"
        Call Footer_Sum()
    End Sub

    Private Sub Footer_Sum()
        Dim Sum1 As Double = 0
        Dim Sum2 As Double = 0
        Dim Sum3 As Double = 0
        Dim Sum4 As Double = 0

        Sum1 = IIf(My_Dataset.Tables("出院明细表").Compute("Sum(Bl_M_Sk)", "") Is DBNull.Value, 0, My_Dataset.Tables("出院明细表").Compute("Sum(Bl_M_Sk)", ""))
        Sum2 = IIf(My_Dataset.Tables("出院明细表").Compute("Sum(Bl_M_Yj)", "") Is DBNull.Value, 0, My_Dataset.Tables("出院明细表").Compute("Sum(Bl_M_Yj)", ""))
        Sum3 = IIf(My_Dataset.Tables("出院明细表").Compute("Sum(Bl_M_Th)", "") Is DBNull.Value, 0, My_Dataset.Tables("出院明细表").Compute("Sum(Bl_M_Th)", ""))
        Sum4 = IIf(My_Dataset.Tables("出院明细表").Compute("Sum(Ry_ZyTs)", "") Is DBNull.Value, 0, My_Dataset.Tables("出院明细表").Compute("Sum(Ry_ZyTs)", ""))


        With C1TrueDBGrid1
            .ColumnFooters = True
            .Columns("Bl_M_Sk").FooterText = Format(Sum1, "###0.00")
            .Columns("Bl_M_Yj").FooterText = Format(Sum2, "###0.00")
            .Columns("Bl_M_Th").FooterText = Format(Sum3, "###0.00")
            .Columns("Ry_ZyTs").FooterText = Format(Sum4, "###0.##")

        End With
    End Sub




End Class