﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="0" />
    <DataSources isList="true" count="1">
      <ds Ref="2" type="DataTableSource" isKey="true">
        <Alias>ds</Alias>
        <Columns isList="true" count="21">
          <value>M_OtherOut_Code,System.String</value>
          <value>Materials_Code,System.String</value>
          <value>Materials_Name,System.String</value>
          <value>MaterialsWh_Code,System.String</value>
          <value>MaterialsWh_Name,System.String</value>
          <value>MaterialsStock_Code,System.String</value>
          <value>MaterialsStore_Num,System.Decimal</value>
          <value>M_OtherOut_Detail_Code,System.String</value>
          <value>MaterialsLot,System.String</value>
          <value>MaterialsExpiryDate,System.DateTime</value>
          <value>M_OtherOut_Num,System.Decimal</value>
          <value>M_OtherOut_WriteoffNo,System.Decimal</value>
          <value>M_OtherOut_RealNo,System.Decimal</value>
          <value>M_OtherOut_Price,System.Decimal</value>
          <value>M_OtherOut_Money,System.Decimal</value>
          <value>M_OtherOut_RealMoney,System.Decimal</value>
          <value>M_OtherOutDetail_Memo,System.String</value>
          <value>OtherOut_Date,System.DateTime</value>
          <value>MaterialsInOut_Code,System.String</value>
          <value>Materials_Spec,System.String</value>
          <value>isSelected,System.Boolean</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>ds</Name>
        <NameInSource>ds</NameInSource>
      </ds>
    </DataSources>
    <Relations isList="true" count="0" />
    <Report isRef="0" />
    <Variables isList="true" count="6">
      <value>,出库日期,出库日期,System.String,,False,False</value>
      <value>,仓库,仓库,System.String,,False,False</value>
      <value>,单号,单号,System.String,,False,False</value>
      <value>,单位,单位,System.String,,False,False</value>
      <value>,打印时间,打印时间,System.String,,False,False</value>
      <value>,操作员,操作员,System.String,,False,False</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="3" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="4">
        <PageFooterBand1 Ref="4" type="PageFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,27,19,0.7</ClientRectangle>
          <Components isList="true" count="1">
            <Text47 Ref="5" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,19,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,0</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text47</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>第{PageNumber}页</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text47>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>PageFooterBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </PageFooterBand1>
        <HeaderBand1 Ref="6" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,19,2.7</ClientRectangle>
          <Components isList="true" count="12">
            <Text5 Ref="7" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,2.1,5.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text5</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Text>物资名称</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text5>
            <Text6 Ref="8" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>5.8,2.1,3.5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10,Bold</Font>
              <Guid>1b5e736784db4901a0d3b79bfeb82786</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text6</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Text>规格</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text6>
            <Text7 Ref="9" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9.3,2.1,2.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10,Bold</Font>
              <Guid>6a11f2efc00a49f9ba03d7411c614c09</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text7</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Text>批号</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text7>
            <Text9 Ref="10" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>12.1,2.1,2.3,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10,Bold</Font>
              <Guid>a4ad7483276f4f9abe47e9669e9f8b5f</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text9</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Text>数量</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text9>
            <Text10 Ref="11" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>14.4,2.1,2.3,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10,Bold</Font>
              <Guid>3a8acab5d37d4018b51e290dc6e7a153</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text10</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Text>单价</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text10>
            <Text11 Ref="12" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>16.7,2.1,2.3,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10,Bold</Font>
              <Guid>79b28d0262994302a171c52a20d38a7c</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text11</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Text>金额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text11>
            <Text1 Ref="13" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0.1,0,18.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>黑体,15,Regular,Point,False,134</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text1</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Text>物资出库单</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text1>
            <Text3 Ref="14" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>12.22,1.4,6.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9</Font>
              <Guid>789493629ac347f3abf04efff0f8a79d</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text3</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Text>打印时间：{打印时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text3>
            <Text4 Ref="15" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.8,5.5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text4</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Text>出库单号:{单号}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text4>
            <Text49 Ref="16" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>5.5,0.8,13.4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text49</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Text>出库仓库：{仓库}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text49>
            <Text2 Ref="17" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>5.5,1.4,6.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text2</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Text>出库日期:{出库日期}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text2>
            <Text19 Ref="18" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0.1,1.4,5.4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9</Font>
              <Guid>25eba5ba15924b619c75df7f433ed569</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text19</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Text>操作员：{操作员}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text19>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>HeaderBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </HeaderBand1>
        <DataBand1 Ref="19" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,3.9,19,0.6</ClientRectangle>
          <Components isList="true" count="6">
            <Text14 Ref="20" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,5.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,0</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>0,0,0,0</Margins>
              <Name>Text14</Name>
              <Page isRef="3" />
              <Parent isRef="19" />
              <Text>{ds.Materials_Name}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text14>
            <Text21 Ref="21" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>14.4,0,2.3,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,0</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>6352f8ef5c054bbfa40d0da7a813401b</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text21</Name>
              <Page isRef="3" />
              <Parent isRef="19" />
              <Text>{ds.M_OtherOut_Price}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="22" type="CustomFormat" isKey="true">
                <StringFormat>0.00##</StringFormat>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text21>
            <Text22 Ref="23" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>16.7,0,2.3,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,0</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>bf770c9dafc342ac8ce3efe6755db7d1</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text22</Name>
              <Page isRef="3" />
              <Parent isRef="19" />
              <Text>{ds.M_OtherOut_RealMoney}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="24" type="CustomFormat" isKey="true">
                <StringFormat>0.00##</StringFormat>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text22>
            <Text15 Ref="25" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>5.8,0,3.5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,0</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>075ca86bed634428a0ccaabe1b8b16ed</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text15</Name>
              <Page isRef="3" />
              <Parent isRef="19" />
              <Text>{ds.Materials_Spec}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="26" type="CustomFormat" isKey="true">
                <StringFormat>##.##</StringFormat>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text15>
            <Text16 Ref="27" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>9.3,0,2.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,0</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>b992839add7d4c32af7e1e6502837bb6</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text16</Name>
              <Page isRef="3" />
              <Parent isRef="19" />
              <Text>{ds.MaterialsLot}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="28" type="CustomFormat" isKey="true">
                <StringFormat>##.##</StringFormat>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text16>
            <Text29 Ref="29" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>12.1,0,2.3,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,0</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>5cea31de09364308af745ed36d6ff576</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text29</Name>
              <Page isRef="3" />
              <Parent isRef="19" />
              <Text>{ds.M_OtherOut_RealNo}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="30" type="CustomFormat" isKey="true">
                <StringFormat>0.####</StringFormat>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text29>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>ds</DataSourceName>
          <Filters isList="true" count="0" />
          <Name>DataBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Sort isList="true" count="0" />
        </DataBand1>
        <ReportSummaryBand1 Ref="31" type="ReportSummaryBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,5.3,19,0.6</ClientRectangle>
          <Components isList="true" count="2">
            <Text12 Ref="32" type="Text" isKey="true">
              <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,3.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10</Font>
              <Guid>aa50ea5820f845ba9c9bc66f7f042bde</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text12</Name>
              <Page isRef="3" />
              <Parent isRef="31" />
              <Text>总金额：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text12>
            <Text13 Ref="33" type="Text" isKey="true">
              <Border>Top, Right, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.2,0,15.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,0</Font>
              <Guid>4da8f381867f472a9e5eb138ce9f89ed</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text13</Name>
              <Page isRef="3" />
              <Parent isRef="31" />
              <Text>{Sum(DataBand1,ds.M_OtherOut_RealMoney)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="34" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text13>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>ReportSummaryBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </ReportSummaryBand1>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>f9c8eab4601d4d9f89df0e7a12c5106a</Guid>
      <Margins>1,1,1,1</Margins>
      <Name>Page1</Name>
      <PageHeight>29.7</PageHeight>
      <PageWidth>21</PageWidth>
      <PaperSize>A4</PaperSize>
      <Report isRef="0" />
      <Watermark Ref="35" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page1>
  </Pages>
  <PrinterSettings Ref="36" type="Stimulsoft.Report.Print.StiPrinterSettings" isKey="true" />
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>采购入库单</ReportAlias>
  <ReportChanged>12/9/2016 4:03:59 PM</ReportChanged>
  <ReportCreated>3/22/2012 11:59:29 AM</ReportCreated>
  <ReportFile>C:\Users\<USER>\Desktop\his2010v300000\his2010\Rpt\物资出库单.mrt</ReportFile>
  <ReportGuid>fba5eb2576f14a9fab8b40868c69dd42</ReportGuid>
  <ReportName>采购入库单</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2011.2.1026</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class Report : Stimulsoft.Report.StiReport
    {
        public Report()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify#endregion StiReport Designer generated code - do not modify
		
		public	string NumberCn(decimal ANumber) 

		{

			const string cPointCn = "点十百千万十百千亿十百千";

			const string cNumberCn = "零一二三四五六七八九";

			string S = ANumber.ToString();

			if (S == "0") return "" + cPointCn[0];

			if (!S.Contains(".")) S += ".";

			int P = S.IndexOf(".");

			string Result = "";

    

			for (int i = 0; i &lt; S.Length; i++)

			{

				if (P == i)

				{

					Result = Result.Replace("零十零", "零");

					Result = Result.Replace("零百零", "零");

					Result = Result.Replace("零千零", "零");

					Result = Result.Replace("零十", "零");

					Result = Result.Replace("零百", "零");

					Result = Result.Replace("零千", "零");

					Result = Result.Replace("零万", "万");

					Result = Result.Replace("零亿", "亿");

					Result = Result.Replace("亿万", "亿");

					Result = Result.Replace("零点", "点");

				}

				else

				{

					if (P &gt; i)

						Result += "" + cNumberCn[S[i] - '0'] + cPointCn[P - i - 1];

					else Result += "" + cNumberCn[S[i] - '0'];

				}

			}

			if (Result.Substring(Result.Length - 1, 1) == "" + cPointCn[0])

				Result = Result.Remove(Result.Length - 1); // 一点-&gt; 一

    

			if (Result[0] == cPointCn[0])

				Result = cNumberCn[0] + Result; // 点三-&gt; 零点三

 

			if ((Result.Length &gt; 1) &amp;&amp; (Result[1] == cPointCn[1]) &amp;&amp; 

				(Result[0] == cNumberCn[1]))

				Result = Result.Remove(0, 1); // 一十三-&gt; 十三

			return Result;

		}

 

		public	string MoneyCn(decimal ANumber)

		{

			string V_Fs="";
			if (ANumber &lt; 0)
			{
				ANumber = -ANumber;
				V_Fs="负";
			}
			else
			{
				V_Fs = "";
			}

			
			if (ANumber == 0) return "零";

			string Result = NumberCn(Math.Truncate(ANumber * 100) / 100);

			Result = Result.Replace("一", "壹");

			Result = Result.Replace("二", "贰");

			Result = Result.Replace("三", "叁");

			Result = Result.Replace("四", "肆");

			Result = Result.Replace("五", "伍");

			Result = Result.Replace("六", "陆");

			Result = Result.Replace("七", "柒");

			Result = Result.Replace("八", "捌");

			Result = Result.Replace("九", "玖");

			Result = Result.Replace("九", "玖");

			Result = Result.Replace("十", "拾");

			Result = Result.Replace("百", "佰");

			Result = Result.Replace("千", "仟");

			if (Result.Contains("点"))

			{

				int P = Result.IndexOf("点");
				if (P + 3 &gt; Result.Length)
				{
					Result = Result+ "分";
				}
				else

				{
					Result = Result.Insert(P + 3, "分");
				}
				//	Result = Result.Insert(P + 3, "分");

				Result = Result.Insert(P + 2, "角");

				Result = Result.Replace("点", "圆");

				Result = Result.Replace("角分", "角");

				Result = Result.Replace("零分", "");

				Result = Result.Replace("零角", "");

				Result = Result.Replace("分角", "");

				if (Result.Substring(0, 2) == "零圆")

					Result = Result.Replace("零圆", "");

			} else Result += "圆整";

			Result =  V_Fs+Result;

			return Result;

		}

    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>