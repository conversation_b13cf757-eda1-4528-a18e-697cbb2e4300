﻿Imports System.Windows.Forms
Imports System.Data.SqlClient
Imports System.Drawing


Public Class Test_Cj

    Private bllSample As New BLLOld.B_LIS_Element2
    Private bllYs As New BLLOld.B_Zd_YyYs
    Dim BllLIS_Test1 As New BLLOld.B_LIS_Test1

    Dim My_Dataset As New DataSet
    Public My_Cm As CurrencyManager             '同步指针
    Public My_Row As DataRow


    Private Sub Test_Cj_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Call Form_init()
        Call Data_Init("")
        Call Data_Clear()
    End Sub

#Region "窗体初始化"
    Private Sub Form_init()
        With MyGrid1
            .Clear()
            .Init_Column("姓名", "Ry_Name", "150", "中", "", False)
            .Init_Column("性别", "Ry_Sex", "120", "中", "", False)
            .Init_Column("标本来源", "Test_Lb", "100 ", "中", "", False)
            .Init_Column("检验编码", "Test_Code", "100 ", "中", "", False)
            .Init_Column("检验状态", "TestState", "100 ", "中", "", False)
            .Init_Column("状态", "State", "80 ", "中", "", False)

        End With
        MyGrid1.Splits(0).DisplayColumns("State").FetchStyle = True
        With TestLb
            .Additem = "门诊"
            .Additem = "住院"
            .Additem = "体检"
            .DisplayColumns(1).Visible = False
            .DroupDownWidth = .Width
            .SelectedIndex = 0
        End With
        With RySex
            .Additem = "男"
            .Additem = "女"
            .DisplayColumns(1).Visible = False
            .DroupDownWidth = .Width
            .SelectedIndex = 0
        End With


        With TestSample
            .DataView = bllSample.GetList("Elementlb_Code='003'").Tables(0).DefaultView
            .Init_Colum("Element_name", "标本类型", 120, "中")
            .Init_Colum("Element_Code", "标本编码", 0, "中")
            .Init_Colum("Element_jc", "标本简称", 0, "中")
            .ValueMember = "Element_Code"
            .DisplayMember = "Element_name"
            .RowFilterNotTextNull = "Element_jc"
            .DroupDownWidth = .Width
            .SelectedIndex = 0
        End With

        With GetJsr
            .DataView = bllYs.GetList("Yy_Code='" & HisVar.HisVar.WsyCode & "'").Tables(0).DefaultView
            .Init_Colum("Ys_name", "科室名称", 120, "中")
            .Init_Colum("Ys_code", "科室编码", 0, "中")
            .Init_Colum("Ys_jc", "科室简称", 0, "中")
            .ValueMember = "Ys_code"
            .DisplayMember = "Ys_name"
            .RowFilterNotTextNull = "Ys_jc"
            .DroupDownWidth = .Width
            .SelectedIndex = -1
        End With
       
    End Sub

    Private Sub Data_Init(ByVal str As String)
        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "SELECT Test_Code,Test_Lb,His_Code,Ry_Name,Ry_Sfzh,Ry_Sex,JKkNo,Ry_Age,Ry_Age_Month,Ry_Age_Week,Bxlb_Code,Ks_Code,Bc_Code,TestXm_Code,TestSample,TestSample_BarCode" & _
                    ",SendJsr,SendDoctor,SendTime,GetJsr,GetDoctor,GetTime,TestJsr,TestTime,CheckJsr,CheckTime,LisPrintTimes,SelfPrintTimes,Diagnose,Memo,TestItem1,TestState FROM LIS_Test1 where  (TestState='待采集' or TestState='待检验' or TestState='拒绝申请')   and SendTime between '" & Format(MyDateEdit1.Value, "yyyy-MM-dd 00:00:00") & "' and '" & Format(MyDateEdit2.Value, "yyyy-MM-dd 23:59:59") & "' " & str & " order by TestState,Test_Lb,Ry_Name", "采集", True)
        My_Cm = CType(BindingContext(My_Dataset, "采集"), CurrencyManager)
        MyGrid1.DataTable = My_Dataset.Tables("采集")

    End Sub

    Private Sub Data_Clear()
        TestCode.Enabled = False
        RyName.Enabled = False
        RySex.Enabled = False
        TestLb.Enabled = False
        TestCode.Text = ""
        RyName.Text = ""
        RySex.SelectedIndex = 0
        TestLb.SelectedIndex = 0
        TestSample.SelectedIndex = -1
        GetJsr.SelectedIndex = -1
        BarCode.Text = ""
    End Sub
#End Region

#Region "按钮动作"
    '读健康卡直接获取人员信息
    Private Sub BtnDk_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnDk.Click
        Dim CardLb As String = MzZy.HD_ConnectDevice(True, True, True, False, False, False, False, False)
        If CardLb <> "读卡失败" Then

            If HisVar.HisVar.Sqldal.GetSingle("select count (*) from LIS_Test1 where JkkNo='" & Model.Jkk.BankNo.ToString() & "'") = 0 Then
                MsgBox("未找到该患者检查项目申请记录，不能读取，请直接录入！")
                Exit Sub
            Else
                Call Data_Init(" and JkkNo='" & Model.Jkk.BankNo.ToString() & "' ")
            End If

        Else
            MsgBox("读取信息失败，请检查卡及设备后重新读取！", MsgBoxStyle.Information, "提示")
        End If
        MzZy.CloseDevice()
    End Sub

    Private Sub Btn_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BtnBar.Click, BtnOk.Click, BtnPrint.Click, BtnFz.Click, BtnQuery.Click
        Select Case sender.tag
            Case "复制"
                Dim TestCodeMax As String = BllLIS_Test1.MaxCode()
                HisVar.HisVar.Sqldal.ExecuteSql("Insert into LIS_Test1 (Test_Code,Test_Lb,Ry_Name,Ry_Sfzh,Ry_Sex,JKkNo,Ry_Age,Ry_Age_Month,Ry_Age_Week,SendJsr,SendDoctor,SendTime,TestXm_code,Bxlb_Code,Ks_Code,Bc_Code,TestState) " & _
                                                " SELECT " & TestCodeMax & ",Test_Lb,Ry_Name,Ry_Sfzh,Ry_Sex,JKkNo,Ry_Age,Ry_Age_Month,Ry_Age_Week,SendJsr,SendDoctor,SendTime,TestXm_code,Bxlb_Code,Ks_Code,Bc_Code,'待采集'  FROM LIS_Test1 where Test_Code='" & TestCode.Text & "'")
                Call Data_Init("")

            Case "确定"
                If GetJsr.Text = "" Then
                    MsgBox("采集医师不能为空！")
                    GetJsr.Select()
                    Exit Sub
                End If
                If TestSample.Text = "" Then
                    MsgBox("标本类型不能为空！")
                    TestSample.Select()
                    Exit Sub
                End If
                HisVar.HisVar.Sqldal.ExecuteSql("Update LIS_Test1 set GetJsr='" & GetJsr.SelectedValue & "',GetDoctor='" & GetJsr.Text & "',GetTime='" & GetTime.Value & "',TestSample='" & TestSample.Text & "',TestSample_BarCode='',TestState='待检验' where Test_Code='" & TestCode.Text & "'")
                Call Data_Init("")
            Case "生成条码"

            Case "打印"

            Case "查询"
                Call Data_Init("")
        End Select
    End Sub
#End Region


    Private Sub MyGrid1_RowColChange(ByVal sender As System.Object, ByVal e As C1.Win.C1TrueDBGrid.RowColChangeEventArgs) Handles MyGrid1.RowColChange
        If (MyGrid1.Row + 1) > MyGrid1.RowCount Then
        Else
            My_Row = My_Cm.List(MyGrid1.Row).Row
            If My_Row("TestState") = "待检验" Then
                Call Data_Clear()
                Exit Sub
            End If
            If My_Row("TestState") = "拒绝申请" Then
                BtnFz.Enabled = True
                BtnOk.Enabled = False
                BtnPrint.Enabled = False
                Call Data_Clear()
                Exit Sub
            Else
                BtnFz.Enabled = False
                BtnOk.Enabled = True
                BtnPrint.Enabled = True
            End If
            TestCode.Text = My_Row("Test_Code") & ""
            RyName.Text = My_Row("Ry_Name") & ""
            RySex.Text = My_Row("Ry_Sex") & ""
            TestLb.Text = My_Row("Test_Lb") & ""
            TestSample.Text = My_Row("TestSample") & ""
            GetJsr.SelectedValue = ""
            GetTime.Value = Format(Now, "yyyy-MM-dd HH:mm:ss")
            BarCode.Text = ""
        End If
    End Sub

    Private Sub MyGrid1_FetchCellStyle(ByVal sender As System.Object, ByVal e As C1.Win.C1TrueDBGrid.FetchCellStyleEventArgs) Handles MyGrid1.FetchCellStyle
        If e.Column.Name = "状态" Then
            Dim falg As String = MyGrid1.Columns("TestState").CellValue(e.Row).ToString
            If falg = "录入" Then
                e.CellStyle.ForegroundImage = ZtHis.Lis.My.Resources.录入
            ElseIf falg = "待采集" Then
                e.CellStyle.ForegroundImage = ZtHis.Lis.My.Resources.待采集
            ElseIf falg = "待检验" Then
                e.CellStyle.ForegroundImage = ZtHis.Lis.My.Resources.待检验
            ElseIf falg = "待审核" Then
                e.CellStyle.ForegroundImage = ZtHis.Lis.My.Resources.待审核
            ElseIf falg = "拒绝申请" Then
                e.CellStyle.ForegroundImage = ZtHis.Lis.My.Resources.拒检
            End If
            e.CellStyle.ForeGroundPicturePosition = C1.Win.C1TrueDBGrid.ForeGroundPicturePositionEnum.PictureOnly
        End If
    End Sub


#Region "输入法设置"
    '中文
    Private Sub C1TextBox7_GotFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles RyName.GotFocus
        InputLanguage.CurrentInputLanguage = HisVar.HisVar.InputCode
    End Sub

    Private Sub C1Numeric1_KeyDown(ByVal sender As System.Object, ByVal e As System.EventArgs)
        InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages(0)
    End Sub

#End Region




End Class