﻿
namespace ERX
{
    partial class SelectYs
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.BtnSave = new CustomControl.MyButton();
            this.combo_doctor_name1 = new YBControl.Combo_doctor_name();
            this.SuspendLayout();
            // 
            // BtnSave
            // 
            this.BtnSave.ButtonImageSize = CustomControl.MyButton.imageSize.large;
            this.BtnSave.DialogResult = System.Windows.Forms.DialogResult.None;
            this.BtnSave.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.BtnSave.Location = new System.Drawing.Point(189, 69);
            this.BtnSave.Name = "BtnSave";
            this.BtnSave.Size = new System.Drawing.Size(70, 35);
            this.BtnSave.TabIndex = 1;
            this.BtnSave.Text = "确定";
            this.BtnSave.Click += new System.EventHandler(this.BtnSave_Click);
            // 
            // combo_doctor_name1
            // 
            this.combo_doctor_name1.Bookmark = -1;
            this.combo_doctor_name1.Captain = "审核医生";
            this.combo_doctor_name1.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.combo_doctor_name1.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.combo_doctor_name1.CaptainWidth = 70F;
            this.combo_doctor_name1.ColumnCaptionHeight = 20;
            this.combo_doctor_name1.DataSource = null;
            this.combo_doctor_name1.DataView = null;
            this.combo_doctor_name1.ItemHeight = 18;
            this.combo_doctor_name1.ItemTextFont = new System.Drawing.Font("宋体", 10.5F);
            this.combo_doctor_name1.Location = new System.Drawing.Point(51, 21);
            this.combo_doctor_name1.MaximumSize = new System.Drawing.Size(10000, 23);
            this.combo_doctor_name1.MinimumSize = new System.Drawing.Size(0, 20);
            this.combo_doctor_name1.Name = "combo_doctor_name1";
            this.combo_doctor_name1.ReadOnly = false;
            this.combo_doctor_name1.Row = 0;
            this.combo_doctor_name1.Size = new System.Drawing.Size(347, 23);
            this.combo_doctor_name1.TabIndex = 2;
            this.combo_doctor_name1.TextFont = new System.Drawing.Font("宋体", 10.5F);
            // 
            // SelectYs
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.ClientSize = new System.Drawing.Size(448, 127);
            this.Controls.Add(this.combo_doctor_name1);
            this.Controls.Add(this.BtnSave);
            this.Name = "SelectYs";
            this.Text = "选择审核医生";
            this.Load += new System.EventHandler(this.SelectYs_Load);
            this.ResumeLayout(false);

        }

        #endregion
        private CustomControl.MyButton BtnSave;
        private YBControl.Combo_doctor_name combo_doctor_name1;
    }
}