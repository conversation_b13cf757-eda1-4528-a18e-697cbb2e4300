﻿Option Explicit On
Public Class Yk_Yf4
    Public V_YpLb As String
    Public V_Confirm As Boolean = False
   
    Private Sub Yk_Yf4_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        C1Combo1.Text = ""
        Dim My_Combo1 As New BaseClass.C_Combo1(Me.C1Combo1)
        With My_Combo1
            .Init_TDBCombo()
            .AddItem("全部药品")
            .AddItem("西药")
            .AddItem("中成药")
            .AddItem("中草药")
            .AddItem("卫生材料")
            .SelectedIndex(0)
        End With

        C1Combo1.Width = 126
        C1Combo1.DropDownWidth = 126
    End Sub

    Private Sub C1Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Button1.Click
        V_Confirm = True
        V_YpLb = C1Combo1.Text
        Me.Close()
    End Sub

    Private Sub C1Button2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Button2.Click
        V_Confirm = False
        Me.Close()
    End Sub

   
End Class