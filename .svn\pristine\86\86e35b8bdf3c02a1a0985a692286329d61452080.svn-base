﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class Zd_Yp13
    Inherits HisControl.BaseChild

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(Zd_Yp13))
        Me.Comm2 = New System.Windows.Forms.Button()
        Me.Comm1 = New System.Windows.Forms.Button()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.T_Line3 = New System.Windows.Forms.Label()
        Me.C1TextBox1 = New C1.Win.C1Input.C1TextBox()
        Me.T_Line1 = New System.Windows.Forms.Label()
        Me.L_Dl_Code = New System.Windows.Forms.Label()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.ToolTip1 = New System.Windows.Forms.ToolTip()
        Me.C1TextBox2 = New C1.Win.C1Input.C1TextBox()
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel()
        Me.C1TextBox3 = New C1.Win.C1Input.C1TextBox()
        Me.C1Combo1 = New C1.Win.C1List.C1Combo()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.Label6 = New System.Windows.Forms.Label()
        Me.C1Combo4 = New C1.Win.C1List.C1Combo()
        Me.C1Combo2 = New C1.Win.C1List.C1Combo()
        Me.Label10 = New System.Windows.Forms.Label()
        Me.Label8 = New System.Windows.Forms.Label()
        Me.Label5 = New System.Windows.Forms.Label()
        Me.C1Combo3 = New C1.Win.C1List.C1Combo()
        Me.Panel1.SuspendLayout
        CType(Me.C1TextBox1,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.C1TextBox2,System.ComponentModel.ISupportInitialize).BeginInit
        Me.TableLayoutPanel1.SuspendLayout
        CType(Me.C1TextBox3,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.C1Combo1,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.C1Combo4,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.C1Combo2,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.C1Combo3,System.ComponentModel.ISupportInitialize).BeginInit
        Me.SuspendLayout
        '
        'Comm2
        '
        Me.Comm2.DialogResult = System.Windows.Forms.DialogResult.Cancel
        Me.Comm2.Location = New System.Drawing.Point(343, 2)
        Me.Comm2.Name = "Comm2"
        Me.Comm2.Size = New System.Drawing.Size(50, 24)
        Me.Comm2.TabIndex = 1
        Me.Comm2.Tag = "取消"
        Me.Comm2.Text = "取消             &C"
        Me.ToolTip1.SetToolTip(Me.Comm2, "取消存盘并退出(&C)")
        Me.Comm2.UseVisualStyleBackColor = false
        '
        'Comm1
        '
        Me.Comm1.Location = New System.Drawing.Point(288, 2)
        Me.Comm1.Name = "Comm1"
        Me.Comm1.Size = New System.Drawing.Size(50, 24)
        Me.Comm1.TabIndex = 0
        Me.Comm1.Tag = "保存"
        Me.Comm1.Text = "保存           &S"
        Me.ToolTip1.SetToolTip(Me.Comm1, "数据存盘(&S)")
        Me.Comm1.UseVisualStyleBackColor = false
        '
        'Panel1
        '
        Me.Panel1.Controls.Add(Me.T_Line3)
        Me.Panel1.Controls.Add(Me.Comm2)
        Me.Panel1.Controls.Add(Me.Comm1)
        Me.Panel1.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.Panel1.Location = New System.Drawing.Point(0, 234)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(412, 27)
        Me.Panel1.TabIndex = 115
        '
        'T_Line3
        '
        Me.T_Line3.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.T_Line3.Dock = System.Windows.Forms.DockStyle.Top
        Me.T_Line3.Location = New System.Drawing.Point(0, 0)
        Me.T_Line3.Name = "T_Line3"
        Me.T_Line3.Size = New System.Drawing.Size(412, 2)
        Me.T_Line3.TabIndex = 101
        Me.T_Line3.Text = "Label2"
        '
        'C1TextBox1
        '
        Me.C1TextBox1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.TableLayoutPanel1.SetColumnSpan(Me.C1TextBox1, 2)
        Me.C1TextBox1.Location = New System.Drawing.Point(65, 38)
        Me.C1TextBox1.Name = "C1TextBox1"
        Me.C1TextBox1.Size = New System.Drawing.Size(170, 19)
        Me.C1TextBox1.TabIndex = 0
        Me.C1TextBox1.Tag = Nothing
        Me.C1TextBox1.TextDetached = true
        Me.C1TextBox1.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        '
        'T_Line1
        '
        Me.T_Line1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.T_Line1.BackColor = System.Drawing.SystemColors.Control
        Me.T_Line1.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.TableLayoutPanel1.SetColumnSpan(Me.T_Line1, 5)
        Me.T_Line1.Location = New System.Drawing.Point(3, 30)
        Me.T_Line1.Name = "T_Line1"
        Me.T_Line1.Size = New System.Drawing.Size(406, 2)
        Me.T_Line1.TabIndex = 119
        Me.T_Line1.Text = "Label1"
        '
        'L_Dl_Code
        '
        Me.L_Dl_Code.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.L_Dl_Code.BackColor = System.Drawing.SystemColors.Info
        Me.L_Dl_Code.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.L_Dl_Code.Location = New System.Drawing.Point(65, 6)
        Me.L_Dl_Code.Name = "L_Dl_Code"
        Me.L_Dl_Code.Size = New System.Drawing.Size(98, 17)
        Me.L_Dl_Code.TabIndex = 125
        Me.L_Dl_Code.TextAlign = System.Drawing.ContentAlignment.BottomCenter
        '
        'Label1
        '
        Me.Label1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom)  _
            Or System.Windows.Forms.AnchorStyles.Left)  _
            Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.Label1.AutoSize = true
        Me.Label1.ForeColor = System.Drawing.Color.DarkRed
        Me.Label1.Location = New System.Drawing.Point(3, 0)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(56, 30)
        Me.Label1.TabIndex = 126
        Me.Label1.Text = "药品编码"
        Me.Label1.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Label2
        '
        Me.Label2.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom)  _
            Or System.Windows.Forms.AnchorStyles.Left)  _
            Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.Label2.AutoSize = true
        Me.Label2.Location = New System.Drawing.Point(3, 32)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(56, 32)
        Me.Label2.TabIndex = 127
        Me.Label2.Text = "药品名称"
        Me.Label2.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Label3
        '
        Me.Label3.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom)  _
            Or System.Windows.Forms.AnchorStyles.Left)  _
            Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.Label3.AutoSize = true
        Me.Label3.Location = New System.Drawing.Point(241, 32)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(58, 32)
        Me.Label3.TabIndex = 128
        Me.Label3.Text = "药品简称"
        Me.Label3.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'C1TextBox2
        '
        Me.C1TextBox2.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.C1TextBox2.Location = New System.Drawing.Point(305, 38)
        Me.C1TextBox2.Name = "C1TextBox2"
        Me.C1TextBox2.Size = New System.Drawing.Size(104, 19)
        Me.C1TextBox2.TabIndex = 1
        Me.C1TextBox2.Tag = Nothing
        Me.C1TextBox2.TextDetached = true
        Me.C1TextBox2.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.ColumnCount = 5
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 62!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 104!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 72!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 64!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100!))
        Me.TableLayoutPanel1.Controls.Add(Me.Label1, 0, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.T_Line1, 0, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.Label2, 0, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.C1TextBox1, 1, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.L_Dl_Code, 1, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.C1TextBox3, 1, 7)
        Me.TableLayoutPanel1.Controls.Add(Me.C1Combo1, 1, 3)
        Me.TableLayoutPanel1.Controls.Add(Me.Label3, 3, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.C1TextBox2, 4, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.Label4, 0, 3)
        Me.TableLayoutPanel1.Controls.Add(Me.Label6, 0, 7)
        Me.TableLayoutPanel1.Controls.Add(Me.C1Combo4, 3, 4)
        Me.TableLayoutPanel1.Controls.Add(Me.C1Combo2, 1, 4)
        Me.TableLayoutPanel1.Controls.Add(Me.Label10, 2, 4)
        Me.TableLayoutPanel1.Controls.Add(Me.Label8, 0, 4)
        Me.TableLayoutPanel1.Controls.Add(Me.Label5, 0, 5)
        Me.TableLayoutPanel1.Controls.Add(Me.C1Combo3, 1, 5)
        Me.TableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Top
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(0, 0)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.RowCount = 9
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 30!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 2!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 32!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 20!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 31!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 31!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 8!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 20!))
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(412, 229)
        Me.TableLayoutPanel1.TabIndex = 130
        '
        'C1TextBox3
        '
        Me.C1TextBox3.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom)  _
            Or System.Windows.Forms.AnchorStyles.Left)  _
            Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.TableLayoutPanel1.SetColumnSpan(Me.C1TextBox3, 4)
        Me.C1TextBox3.Location = New System.Drawing.Point(65, 157)
        Me.C1TextBox3.Multiline = true
        Me.C1TextBox3.Name = "C1TextBox3"
        Me.C1TextBox3.ScrollBars = System.Windows.Forms.ScrollBars.Vertical
        Me.C1TextBox3.Size = New System.Drawing.Size(344, 49)
        Me.C1TextBox3.TabIndex = 4
        Me.C1TextBox3.Tag = Nothing
        Me.C1TextBox3.TextDetached = true
        '
        'C1Combo1
        '
        Me.C1Combo1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.C1Combo1.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1Combo1.Caption = ""
        Me.C1Combo1.CaptionHeight = 17
        Me.C1Combo1.CharacterCasing = System.Windows.Forms.CharacterCasing.Normal
        Me.TableLayoutPanel1.SetColumnSpan(Me.C1Combo1, 4)
        Me.C1Combo1.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.C1Combo1.Images.Add(CType(resources.GetObject("C1Combo1.Images"),System.Drawing.Image))
        Me.C1Combo1.ItemHeight = 15
        Me.C1Combo1.Location = New System.Drawing.Point(65, 67)
        Me.C1Combo1.MatchEntryTimeout = CType(2000,Long)
        Me.C1Combo1.MaxDropDownItems = CType(5,Short)
        Me.C1Combo1.MaxLength = 32767
        Me.C1Combo1.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.C1Combo1.Name = "C1Combo1"
        Me.C1Combo1.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.C1Combo1.Size = New System.Drawing.Size(344, 16)
        Me.C1Combo1.TabIndex = 2
        Me.C1Combo1.PropBag = resources.GetString("C1Combo1.PropBag")
        '
        'Label4
        '
        Me.Label4.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom)  _
            Or System.Windows.Forms.AnchorStyles.Left)  _
            Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.Label4.AutoSize = true
        Me.Label4.Location = New System.Drawing.Point(3, 64)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(56, 20)
        Me.Label4.TabIndex = 130
        Me.Label4.Text = "功    效"
        Me.Label4.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Label6
        '
        Me.Label6.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom)  _
            Or System.Windows.Forms.AnchorStyles.Left)  _
            Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.Label6.AutoSize = true
        Me.Label6.Location = New System.Drawing.Point(3, 154)
        Me.Label6.Name = "Label6"
        Me.Label6.Size = New System.Drawing.Size(56, 55)
        Me.Label6.TabIndex = 129
        Me.Label6.Text = "备    注"
        Me.Label6.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'C1Combo4
        '
        Me.C1Combo4.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.C1Combo4.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1Combo4.Caption = ""
        Me.C1Combo4.CaptionHeight = 17
        Me.C1Combo4.CharacterCasing = System.Windows.Forms.CharacterCasing.Normal
        Me.TableLayoutPanel1.SetColumnSpan(Me.C1Combo4, 2)
        Me.C1Combo4.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.C1Combo4.Images.Add(CType(resources.GetObject("C1Combo4.Images"),System.Drawing.Image))
        Me.C1Combo4.ItemHeight = 15
        Me.C1Combo4.Location = New System.Drawing.Point(241, 91)
        Me.C1Combo4.MatchEntryTimeout = CType(2000,Long)
        Me.C1Combo4.MaxDropDownItems = CType(5,Short)
        Me.C1Combo4.MaxLength = 32767
        Me.C1Combo4.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.C1Combo4.Name = "C1Combo4"
        Me.C1Combo4.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.C1Combo4.Size = New System.Drawing.Size(168, 16)
        Me.C1Combo4.TabIndex = 142
        Me.C1Combo4.PropBag = resources.GetString("C1Combo4.PropBag")
        '
        'C1Combo2
        '
        Me.C1Combo2.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.C1Combo2.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1Combo2.Caption = ""
        Me.C1Combo2.CaptionHeight = 17
        Me.C1Combo2.CharacterCasing = System.Windows.Forms.CharacterCasing.Normal
        Me.C1Combo2.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.C1Combo2.Images.Add(CType(resources.GetObject("C1Combo2.Images"),System.Drawing.Image))
        Me.C1Combo2.ItemHeight = 15
        Me.C1Combo2.Location = New System.Drawing.Point(65, 91)
        Me.C1Combo2.MatchEntryTimeout = CType(2000,Long)
        Me.C1Combo2.MaxDropDownItems = CType(5,Short)
        Me.C1Combo2.MaxLength = 32767
        Me.C1Combo2.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.C1Combo2.Name = "C1Combo2"
        Me.C1Combo2.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.C1Combo2.Size = New System.Drawing.Size(98, 16)
        Me.C1Combo2.TabIndex = 3
        Me.C1Combo2.PropBag = resources.GetString("C1Combo2.PropBag")
        '
        'Label10
        '
        Me.Label10.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom)  _
            Or System.Windows.Forms.AnchorStyles.Left)  _
            Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.Label10.AutoSize = true
        Me.Label10.Location = New System.Drawing.Point(169, 84)
        Me.Label10.Name = "Label10"
        Me.Label10.Size = New System.Drawing.Size(66, 31)
        Me.Label10.TabIndex = 141
        Me.Label10.Text = "特殊药品"
        Me.Label10.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Label8
        '
        Me.Label8.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom)  _
            Or System.Windows.Forms.AnchorStyles.Left)  _
            Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.Label8.AutoSize = true
        Me.Label8.Location = New System.Drawing.Point(3, 84)
        Me.Label8.Name = "Label8"
        Me.Label8.Size = New System.Drawing.Size(56, 31)
        Me.Label8.TabIndex = 135
        Me.Label8.Text = "基本药品"
        Me.Label8.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Label5
        '
        Me.Label5.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom)  _
            Or System.Windows.Forms.AnchorStyles.Left)  _
            Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.Label5.AutoSize = true
        Me.Label5.Location = New System.Drawing.Point(3, 115)
        Me.Label5.Name = "Label5"
        Me.Label5.Size = New System.Drawing.Size(56, 31)
        Me.Label5.TabIndex = 143
        Me.Label5.Text = "药品类型"
        Me.Label5.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'C1Combo3
        '
        Me.C1Combo3.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.C1Combo3.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1Combo3.Caption = ""
        Me.C1Combo3.CaptionHeight = 17
        Me.C1Combo3.CharacterCasing = System.Windows.Forms.CharacterCasing.Normal
        Me.C1Combo3.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.C1Combo3.Images.Add(CType(resources.GetObject("C1Combo3.Images"),System.Drawing.Image))
        Me.C1Combo3.ItemHeight = 15
        Me.C1Combo3.Location = New System.Drawing.Point(65, 122)
        Me.C1Combo3.MatchEntryTimeout = CType(2000,Long)
        Me.C1Combo3.MaxDropDownItems = CType(5,Short)
        Me.C1Combo3.MaxLength = 32767
        Me.C1Combo3.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.C1Combo3.Name = "C1Combo3"
        Me.C1Combo3.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.C1Combo3.Size = New System.Drawing.Size(98, 16)
        Me.C1Combo3.TabIndex = 144
        Me.C1Combo3.PropBag = resources.GetString("C1Combo3.PropBag")
        '
        'Zd_Yp13
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6!, 12!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.CancelButton = Me.Comm2
        Me.ClientSize = New System.Drawing.Size(412, 261)
        Me.Controls.Add(Me.TableLayoutPanel1)
        Me.Controls.Add(Me.Panel1)
        Me.Icon = CType(resources.GetObject("$this.Icon"),System.Drawing.Icon)
        Me.Name = "Zd_Yp13"
        Me.Text = "药品名称"
        Me.Panel1.ResumeLayout(false)
        CType(Me.C1TextBox1,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.C1TextBox2,System.ComponentModel.ISupportInitialize).EndInit
        Me.TableLayoutPanel1.ResumeLayout(false)
        Me.TableLayoutPanel1.PerformLayout
        CType(Me.C1TextBox3,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.C1Combo1,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.C1Combo4,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.C1Combo2,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.C1Combo3,System.ComponentModel.ISupportInitialize).EndInit
        Me.ResumeLayout(false)

End Sub
    Friend WithEvents Comm2 As System.Windows.Forms.Button
    Friend WithEvents Comm1 As System.Windows.Forms.Button
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents T_Line3 As System.Windows.Forms.Label
    Friend WithEvents C1TextBox1 As C1.Win.C1Input.C1TextBox
    Friend WithEvents T_Line1 As System.Windows.Forms.Label
    Friend WithEvents L_Dl_Code As System.Windows.Forms.Label
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents Label3 As System.Windows.Forms.Label
    Friend WithEvents Label2 As System.Windows.Forms.Label
    Friend WithEvents ToolTip1 As System.Windows.Forms.ToolTip
    Friend WithEvents C1TextBox2 As C1.Win.C1Input.C1TextBox
    Friend WithEvents TableLayoutPanel1 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents Label4 As System.Windows.Forms.Label
    Friend WithEvents C1Combo1 As C1.Win.C1List.C1Combo
    Friend WithEvents Label6 As System.Windows.Forms.Label
    Friend WithEvents C1TextBox3 As C1.Win.C1Input.C1TextBox
    Friend WithEvents C1Combo2 As C1.Win.C1List.C1Combo
    Friend WithEvents Label8 As System.Windows.Forms.Label
    Friend WithEvents Label10 As System.Windows.Forms.Label
    Friend WithEvents C1Combo4 As C1.Win.C1List.C1Combo
    Friend WithEvents Label5 As Label
    Friend WithEvents C1Combo3 As C1.Win.C1List.C1Combo
End Class
