﻿Imports System.Data.SqlClient
Imports System.Windows.Forms
Imports System.Drawing
Public Class EmrBasic1
#Region "变量初始化"

    Dim V_Finish As Boolean = False             '初始化完成

    Dim My_Table As New DataTable            '药品字典

    Dim My_Cm As CurrencyManager             '同步指针
    Dim m_Rc As New BaseClass.C_RowChange
    '当 前 行
    Dim V_Insert As Boolean                  '增加记录
    Dim completeFlag As Boolean                  '条件判断
    Dim isMulti As Boolean
    Private dtTree As DataTable
    Private vSelectedNodeTag, vSelectedNodeText, vSelectedType, vFatherNodeTag, vFatherNodeText As String
    Private vType As String
    Dim Emr_BasicElementTreeBLL As New BLLOld.B_Emr_BasicElementTree
    Dim Emr_BasicElementListBLL As New BLLOld.B_Emr_BasicElementList
    Dim Emr_BasicElementValueBLL As New BLLOld.B_Emr_BasicElementValue
    Dim Emr_DataFieldBLL As New BLLOld.B_Emr_DataField

    Dim Emr_BasicElementTreeModel As New ModelOld.M_Emr_BasicElementTree
    Dim Emr_BasicElementListModel As New ModelOld.M_Emr_BasicElementList
    Dim Emr_BasicElementValueModel As New ModelOld.M_Emr_BasicElementValue
#End Region


    Private Sub EmrBasic_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Call Form_Init()
        AddHandler m_Rc.TreeAddEvent, AddressOf Tree_Edit
        AddHandler m_Rc.AddChangeEvent, AddressOf Find_Node
    End Sub

    Private Sub EmrBasic1_FormClosed(sender As Object, e As System.Windows.Forms.FormClosedEventArgs) Handles Me.FormClosed
        RemoveHandler m_Rc.TreeAddEvent, AddressOf Tree_Edit
        RemoveHandler m_Rc.AddChangeEvent, AddressOf Find_Node
    End Sub

#Region "窗体初始化"

    Private Sub Form_Init()

        'Treeview初始化()
        With Me.TreeView1
            .Nodes.Clear()
            .FullRowSelect = True
            .HideSelection = False
            .HotTracking = True
            .Scrollable = True
            .ShowRootLines = False
            .Sorted = False
        End With

        With DataFieldComobo1
            .DataView = Emr_DataFieldBLL.GetList("").Tables(0).DefaultView
            .Init_Colum("DataName", "医生名称", 120, "左")
            .Init_Colum("DataJc", "医生简称", 0, "左")
            .Init_Colum("DataField", "医生编码", 0, "中")
            .DisplayMember = "DataName"
            .ValueMember = "DataField"
            .RowFilterNotTextNull = "DataJc"
            .RowFilterTextNull = ""
            .DroupDownWidth = .Width - .CaptainWidth
            .MaxDropDownItems = 15
            .ItemHeight = 20
            .SelectedValue = -1
        End With

        Init_Tree()

    End Sub

#End Region

#Region "Tree"
    Private Sub Init_Tree()
        V_Finish = False
        '根目录()
        TreeView1.Nodes.Clear()
        Dim My_Node As New TreeNode
        With My_Node
            .Tag = "0000000000" & "0"
            .Text = "模板元素"
            .ImageIndex = 0
            .SelectedImageIndex = 0
        End With
        TreeView1.Nodes.Add(My_Node)
        dtTree = Emr_BasicElementTreeBLL.GetAllList().Tables(0)
        Product_Node(My_Node)

        '一级数据()
        V_Finish = True
        With Me.TreeView1
            .SelectedNode = TreeView1.TopNode
            .SelectedNode.Expand()
            .Select()
        End With
    End Sub


    Private Sub Product_Node(ByVal FatherNode As TreeNode)

        For Each row As DataRow In dtTree.Select("Father_Code='" & Strings.Left(FatherNode.Tag, 10) & "'")
            Dim My_Node As New TreeNode
            With My_Node
                .Tag = row("Ele_Code").ToString & row("EleType")
                .Text = row("Ele_Name").ToString
                If .Tag.ToString().Substring(10, 1) <> 0 Then
                    .ImageIndex = 3
                    .SelectedImageIndex = 3
                Else
                    .ImageIndex = 1
                    .SelectedImageIndex = 2
                End If

            End With
            FatherNode.Nodes.Add(My_Node)
            Product_Node(My_Node)
        Next
    End Sub

    Private Sub Tree_Edit(ByVal V_Key As String, ByVal V_Text As String)
        Dim My_Node As New TreeNode
        If V_Insert = True Then
            My_Node.Tag = V_Key
            My_Node.Text = V_Text
            If V_Key.ToString().Substring(10, 1) <> 0 Then
                My_Node.ImageIndex = 3
                My_Node.SelectedImageIndex = 3
            Else
                My_Node.ImageIndex = 1
                My_Node.SelectedImageIndex = 2
            End If
            TreeView1.SelectedNode.Nodes.Add(My_Node)
            If TreeView1.SelectedNode.IsExpanded = False Then
                TreeView1.SelectedNode.Expand()
            End If
        Else
            TreeView1.SelectedNode.Text = V_Text
        End If
    End Sub

    Private Sub Find_Node(ByVal V_Key As String)
        completeFlag = False
        Find_Node(V_Key, TreeView1.TopNode)
    End Sub

    Private Sub Find_Node(ByVal V_Key As String, ByVal node As TreeNode)

        If Strings.Left(node.Tag, 10) = V_Key Then
            TreeView1.SelectedNode = node
            completeFlag = True
            Exit Sub
        End If

        If node.Nodes.Count > 0 Then
            For Each My_Node In node.Nodes
                If completeFlag = True Then
                    Exit Sub
                End If

                Find_Node(V_Key, My_Node)
            Next
        End If
    End Sub


    Private Sub P_Delete_Node(ByVal node As TreeNode)
        If node.Tag = TreeView1.TopNode.Tag Then
            MsgBox("无法删除根目录！", MsgBoxStyle.Critical, "提示")
        ElseIf Strings.Right(TreeView1.SelectedNode.Tag, 1) <> "0" Then
            Emr_BasicElementTreeBLL.Delete(Strings.Left(node.Tag, 10))
            node.Parent.Nodes.Remove(node)
        Else
            If node.Nodes.Count > 0 Then
                MsgBox("目录下还有节点无法删除！", MsgBoxStyle.Critical, "提示")
            Else
                Emr_BasicElementTreeBLL.Delete(Strings.Left(node.Tag, 10))
                node.Parent.Nodes.Remove(node)
            End If

        End If


    End Sub

    Private Sub Delete_node(ByVal Parent_Node As TreeNode, ByVal Child_Node As TreeNode)
        If Child_Node.Nodes.Count = 0 Then
            Emr_BasicElementTreeBLL.Delete(Strings.Left(Child_Node.Tag, 10), Strings.Right(Child_Node.Tag, 1))
            Parent_Node.Nodes.Remove(Child_Node)
        Else
            Delete_node(Child_Node, Child_Node.FirstNode)
            Delete_node(Parent_Node, Child_Node)
        End If

    End Sub
#End Region

#Region "条件检查"
    Private Sub MenuItemDisplay(ByVal v As Integer)
        DeleNode.Height = v
        UpdateNode.Height = v
    End Sub

    Private Sub ConditionCheck(ByVal node As TreeNode)
        If Strings.Left(node.Tag, 10) = "0000000000" Then
            AddNode.Enabled = True
            InputButton.Enabled = False
            DataFieldButton.Enabled = False
            SelectButton.Enabled = False
        Else

            Select Case vSelectedType
                Case "0"
                    AddNode.Enabled = True
                    InputButton.Enabled = True
                    DataFieldButton.Enabled = True
                    SelectButton.Enabled = True
                Case Else
                    AddNode.Enabled = False
                    InputButton.Enabled = False
                    DataFieldButton.Enabled = False
                    SelectButton.Enabled = False
            End Select
        End If

    End Sub

    Private Sub DeleteConditonCheck(ByVal Node As TreeNode)
        If Strings.Right(Node.Tag, 1) <> "0" Then
            completeFlag = False
            Exit Sub
        End If

        If Node.Nodes.Count > 0 Then
            For Each My_Node In Node.Nodes
                If completeFlag = False Then
                    Exit Sub
                End If
                DeleteConditonCheck(My_Node)
            Next
        End If

    End Sub
#End Region


#Region "控件__动作"



    Private Sub EmrBasic1_KeyDown(sender As Object, e As System.Windows.Forms.KeyEventArgs) Handles Me.KeyDown
        If e.Control = True And e.KeyCode = Keys.S Then
            ElementSave()
        End If
    End Sub

    Private Sub TreeView1_AfterSelect(ByVal sender As System.Object, ByVal e As System.Windows.Forms.TreeViewEventArgs) Handles TreeView1.AfterSelect
        If V_Finish = False Then Exit Sub '     '初始化完成

        vSelectedNodeTag = Strings.Left(TreeView1.SelectedNode.Tag, 10)
        vSelectedNodeText = TreeView1.SelectedNode.Text
        vSelectedType = Strings.Right(TreeView1.SelectedNode.Tag, 1)
        If TreeView1.SelectedNode.Tag = "00000000000" Then
            vFatherNodeTag = Strings.Left(TreeView1.SelectedNode.Tag, 10)
            vFatherNodeText = TreeView1.SelectedNode.Text
        Else
            vFatherNodeTag = Strings.Left(TreeView1.SelectedNode.Parent.Tag, 10)
            vFatherNodeText = TreeView1.SelectedNode.Parent.Text
        End If

        Call P_Init_Data1(Me.TreeView1.SelectedNode.Tag)
        Call ElementShow()
    End Sub

    Private Sub ContextMenuStrip1_Opening(sender As System.Object, e As System.ComponentModel.CancelEventArgs) Handles ContextMenuStrip1.Opening
        ConditionCheck(TreeView1.SelectedNode)
        MenuItemDisplay(22)
    End Sub

    Private Sub TreeView1_KeyUp(sender As System.Object, e As System.Windows.Forms.KeyEventArgs) Handles TreeView1.KeyUp
        If e.KeyCode = Keys.Insert Then
            If vSelectedType = "0" Then MenuItem_Click(AddNode, Nothing)
        ElseIf e.KeyCode = Keys.Delete Then
            If TreeView1.SelectedNode.Tag = "00000000000" Then
                MsgBox("不能删除根目录", MsgBoxStyle.Exclamation, "提示")
                Exit Sub
            Else
                P_Delete_Node(TreeView1.SelectedNode)
            End If
        End If
    End Sub

    Private Sub MenuItem_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles UpdateNode.Click, SelectButton.Click, InputButton.Click, DeleNode.Click, DataFieldButton.Click, AddNode.Click
        If sender.text = "删除" Then
            P_Delete_Node(TreeView1.SelectedNode)
        Else
            P_ShowMx(sender.Text)
        End If
    End Sub



    Private Sub AddMyButton1_Click(sender As Object, e As System.EventArgs) Handles AddMyButton1.Click

        ContextMenuStrip1.Show(HisVar.HisVar.FMain.Location.X + 15, HisVar.HisVar.FMain.Location.Y + AddMyButton1.Location.Y + HisVar.HisVar.DockTab.Location.Y + 30)
        MenuItemDisplay(0)
    End Sub

    Private Sub MyButton_Click(sender As System.Object, e As System.EventArgs) Handles MyButton1.Click, MyButton2.Click, MyButton3.Click, MyButton4.Click, MyButton5.Click
        Select Case sender.Text
            Case "删除"
                P_Delete_Node(TreeView1.SelectedNode)
            Case "修改"
                P_ShowMx("修改")
            Case "查询"
                Dim frm As New EmrBasic3(m_Rc)
                frm.Owner = Me
                frm.ShowDialog()
            Case "保存"
                Call ElementSave()
            Case "取消"
                Me.Close()
        End Select
    End Sub

#End Region

#Region "自定义函数"

    Private Sub P_ShowMx(ByVal V_Lb As String)
        ' showFlag = V_Lb
        Dim vform As Form
        If V_Lb Like "增加*" Then
            V_Insert = True

            Select Case V_Lb
                Case "增加目录"
                    vType = 0
                Case "增加输入类元素"
                    vType = 2
                Case "增加数据类元素"
                    vType = 3
                Case "增加选择类元素"
                    vType = 1
            End Select

            vform = New EmrBasic2(m_Rc, V_Insert, "", "", vType, vSelectedNodeTag, vSelectedNodeText)
        Else
            If TreeView1.SelectedNode.Tag = "00000000000" Then
                MsgBox("不能修改根目录", MsgBoxStyle.Exclamation, "提示")
                Exit Sub
            End If
            V_Insert = False

            vform = New EmrBasic2(m_Rc, V_Insert, vSelectedNodeTag, vSelectedNodeText, vType, vFatherNodeTag, vFatherNodeText)
        End If

        vform.Owner = Me
        vform.ShowDialog()
        'MyGrid1.Select()
    End Sub

    Private Sub P_Init_Data1(ByVal V_Ele_Code As String)
        Select Case Strings.Right(V_Ele_Code, 1)
            Case "1"
                My_Table = Emr_BasicElementListBLL.GetList("Ele_Code='" & Strings.Left(V_Ele_Code, 10) & "'").Tables(0)
                Emr_BasicElementTreeModel = Emr_BasicElementTreeBLL.GetModel(Strings.Left(V_Ele_Code, 10))
            Case "2"
                My_Table = Emr_BasicElementValueBLL.GetList("Ele_Code='" & Strings.Left(V_Ele_Code, 10) & "'").Tables(0)
            Case "3"
                Emr_BasicElementTreeModel = Emr_BasicElementTreeBLL.GetModel(Strings.Left(V_Ele_Code, 10))
        End Select

    End Sub

    Private Sub ElementShow()
        Select Case vSelectedType
            Case "0"
                SplitContainer1.Panel1Collapsed = True
                SplitContainer1.Panel2Collapsed = False
                SplitContainer2.Panel1Collapsed = False
                SplitContainer2.Panel2Collapsed = True

                DefaultTextBox1.Enabled = False
                DefaultTextBox1.Captain = "目录："
                DefaultTextBox1.Text = TreeView1.SelectedNode.Text
            Case "1"
                SplitContainer1.Panel1Collapsed = False
                SplitContainer1.Panel2Collapsed = True
                SplitContainer2.Panel1Collapsed = True
                SplitContainer2.Panel2Collapsed = True


                If Emr_BasicElementTreeModel.isMultiSelect = True Then
                    SingleRadioButton1.Checked = False
                    DoubleRadioButton2.Checked = True
                Else
                    SingleRadioButton1.Checked = True
                    DoubleRadioButton2.Checked = False
                End If
                ListTextBox1.Text = ""
                For Each row In My_Table.Rows
                    ListTextBox1.Text += row("ItemText") & vbCrLf
                Next
                ListTextBox1.Focus()
            Case "2"
                SplitContainer1.Panel1Collapsed = True
                SplitContainer1.Panel2Collapsed = False
                SplitContainer2.Panel1Collapsed = False
                SplitContainer2.Panel2Collapsed = True


                DefaultTextBox1.Enabled = True
                DefaultTextBox1.Captain = "默 认 值"
                If My_Table.Rows.Count > 0 Then
                    DefaultTextBox1.Text = My_Table.Rows(0).Item("DefaultValue") & ""
                Else
                    DefaultTextBox1.Text = ""
                End If
                DefaultTextBox1.Select()
            Case "3"
                SplitContainer1.Panel1Collapsed = True
                SplitContainer1.Panel2Collapsed = False
                SplitContainer2.Panel1Collapsed = True
                SplitContainer2.Panel2Collapsed = False

                DataFieldComobo1.SelectedValue = Emr_BasicElementTreeModel.DataField
                DataFieldComobo1.Select()
        End Select
    End Sub

    Private Sub ElementSave()
        If vSelectedType <> "0" Then
            Select Case vSelectedType
                Case "1"
                    Emr_BasicElementTreeModel.isMultiSelect = IIf(SingleRadioButton1.Checked, False, True)
                    Emr_BasicElementTreeBLL.Update(Emr_BasicElementTreeModel)

                    Dim contentList As New List(Of String)
                    Dim arrSelect As Array = ListTextBox1.Text.Split(vbCrLf)
                    For Each arr In arrSelect
                        If Replace(Replace(Replace(arr, " ", ""), vbLf, ""), vbCr, "") = "" Then
                            Continue For
                        End If
                        contentList.Add(Replace(Replace(arr, vbLf, ""), vbCr, ""))
                    Next

                    Emr_BasicElementListBLL.Update(vSelectedNodeTag, contentList)
                Case "2"

                    Emr_BasicElementValueBLL.Update(vSelectedNodeTag, DefaultTextBox1.Text)

                Case "3"
                    Emr_BasicElementTreeModel.DataField = DataFieldComobo1.SelectedValue
                    Emr_BasicElementTreeBLL.Update(Emr_BasicElementTreeModel)

            End Select
            HisControl.msg.Show("数据保存成功", "提示")
        End If

    End Sub


#End Region




End Class