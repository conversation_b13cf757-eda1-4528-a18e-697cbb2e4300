﻿/**  版本信息模板在安装目录下，可自行修改。
* D_LIS_Element2.cs
*
* 功 能： N/A
* 类 名： D_LIS_Element2
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/10/17 星期一 下午 1:38:52   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;

namespace DAL
{
	/// <summary>
	/// 数据访问类:D_LIS_Element2
	/// </summary>
	public partial class D_LIS_Element2
	{
		public D_LIS_Element2()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string Element_Code)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from LIS_Element2");
			strSql.Append(" where Element_Code=@Element_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Element_Code", SqlDbType.Char,7)			};
			parameters[0].Value = Element_Code;

			return HisVar.HisVar.Sqldal.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ModelOld.M_LIS_Element2 model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into LIS_Element2(");
			strSql.Append("Elementlb_Code,Element_Code,Element_Name,Element_Jc,Element_Memo)");
			strSql.Append(" values (");
			strSql.Append("@Elementlb_Code,@Element_Code,@Element_Name,@Element_Jc,@Element_Memo)");
			SqlParameter[] parameters = {
					new SqlParameter("@Elementlb_Code", SqlDbType.Char,3),
					new SqlParameter("@Element_Code", SqlDbType.Char,7),
					new SqlParameter("@Element_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Element_Jc", SqlDbType.VarChar,50),
					new SqlParameter("@Element_Memo", SqlDbType.VarChar,50)};
			parameters[0].Value = model.Elementlb_Code;
			parameters[1].Value = model.Element_Code;
			parameters[2].Value = model.Element_Name;
			parameters[3].Value = model.Element_Jc;
			parameters[4].Value = model.Element_Memo;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ModelOld.M_LIS_Element2 model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update LIS_Element2 set ");
			strSql.Append("Elementlb_Code=@Elementlb_Code,");
			strSql.Append("Element_Name=@Element_Name,");
			strSql.Append("Element_Jc=@Element_Jc,");
			strSql.Append("Element_Memo=@Element_Memo");
			strSql.Append(" where Element_Code=@Element_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Elementlb_Code", SqlDbType.Char,3),
					new SqlParameter("@Element_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Element_Jc", SqlDbType.VarChar,50),
					new SqlParameter("@Element_Memo", SqlDbType.VarChar,50),
					new SqlParameter("@Element_Code", SqlDbType.Char,7)};
			parameters[0].Value = model.Elementlb_Code;
			parameters[1].Value = model.Element_Name;
			parameters[2].Value = model.Element_Jc;
			parameters[3].Value = model.Element_Memo;
			parameters[4].Value = model.Element_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string Element_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from LIS_Element2 ");
			strSql.Append(" where Element_Code=@Element_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Element_Code", SqlDbType.Char,7)			};
			parameters[0].Value = Element_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string Element_Codelist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from LIS_Element2 ");
			strSql.Append(" where Element_Code in ("+Element_Codelist + ")  ");
			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_LIS_Element2 GetModel(string Element_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 Elementlb_Code,Element_Code,Element_Name,Element_Jc,Element_Memo from LIS_Element2 ");
			strSql.Append(" where Element_Code=@Element_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Element_Code", SqlDbType.Char,7)			};
			parameters[0].Value = Element_Code;

			ModelOld.M_LIS_Element2 model=new ModelOld.M_LIS_Element2();
			DataSet ds=HisVar.HisVar.Sqldal.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_LIS_Element2 DataRowToModel(DataRow row)
		{
			ModelOld.M_LIS_Element2 model=new ModelOld.M_LIS_Element2();
			if (row != null)
			{
				if(row["Elementlb_Code"]!=null)
				{
					model.Elementlb_Code=row["Elementlb_Code"].ToString();
				}
				if(row["Element_Code"]!=null)
				{
					model.Element_Code=row["Element_Code"].ToString();
				}
				if(row["Element_Name"]!=null)
				{
					model.Element_Name=row["Element_Name"].ToString();
				}
				if(row["Element_Jc"]!=null)
				{
					model.Element_Jc=row["Element_Jc"].ToString();
				}
				if(row["Element_Memo"]!=null)
				{
					model.Element_Memo=row["Element_Memo"].ToString();
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select Elementlb_Code,Element_Code,Element_Name,Element_Jc,Element_Memo ");
			strSql.Append(" FROM LIS_Element2 ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

        public DataSet GetListForCombo(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();

            strSql.Append(" select LIS_Element1.Elementlb_Code,Elementlb_Name,Element_Code,Element_Name,Element_Jc,Element_Memo ");
            strSql.Append(" FROM LIS_Element1,LIS_Element2 ");
            strSql.Append(" WHERE LIS_Element1.Elementlb_Code=LIS_Element2.Elementlb_Code");

            if (strWhere.Trim() != "")
            {
                strSql.Append(" and " + strWhere);
            }
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }



		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" Elementlb_Code,Element_Code,Element_Name,Element_Jc,Element_Memo ");
			strSql.Append(" FROM LIS_Element2 ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM LIS_Element2 ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.Element_Code desc");
			}
			strSql.Append(")AS Row, T.*  from LIS_Element2 T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "LIS_Element2";
			parameters[1].Value = "Element_Code";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return HisVar.HisVar.Sqldal.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/




		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

