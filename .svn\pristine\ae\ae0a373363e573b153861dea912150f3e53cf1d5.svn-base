﻿/**  版本信息模板在安装目录下，可自行修改。
* D_Materials_Dict.cs
*
* 功 能： N/A
* 类 名： D_Materials_Dict
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016-12-03 15:06:33   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
namespace DAL
{
	/// <summary>
	/// 数据访问类:D_Materials_Dict
	/// </summary>
	public partial class D_Materials_Dict
	{
		public D_Materials_Dict()
		{}
		#region  BasicMethod


        public string MaxCode()
        {
            string max = (string)(HisVar.HisVar.Sqldal.F_MaxCode("select max(Materials_Code) from Materials_Dict", 10));
            return max;
        }


		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string Materials_Code)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from Materials_Dict");
			strSql.Append(" where Materials_Code=@Materials_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Materials_Code", SqlDbType.Char,10)			};
			parameters[0].Value = Materials_Code;

			return HisVar.HisVar.Sqldal.Exists(strSql.ToString(),parameters);
		}
        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetAList()
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(" SELECT DISTINCT Materials_Name,Materials_Py FROM materials_dict ");
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ModelOld.M_Materials_Dict model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into Materials_Dict(");
			strSql.Append("Materials_Code,Materials_Name,Materials_Py,Materials_Wb,Materials_Spec,Pack_Unit,Convert_Ratio,Bulk_Unit,MateManu_Code,MateManu_Name,Class_Code,IsUse,Materials_Memo)");
			strSql.Append(" values (");
			strSql.Append("@Materials_Code,@Materials_Name,@Materials_Py,@Materials_Wb,@Materials_Spec,@Pack_Unit,@Convert_Ratio,@Bulk_Unit,@MateManu_Code,@MateManu_Name,@Class_Code,@IsUse,@Materials_Memo)");
			SqlParameter[] parameters = {
					new SqlParameter("@Materials_Code", SqlDbType.Char,10),
					new SqlParameter("@Materials_Name", SqlDbType.VarChar,200),
					new SqlParameter("@Materials_Py", SqlDbType.VarChar,200),
					new SqlParameter("@Materials_Wb", SqlDbType.VarChar,200),
					new SqlParameter("@Materials_Spec", SqlDbType.VarChar,50),
					new SqlParameter("@Pack_Unit", SqlDbType.VarChar,10),
					new SqlParameter("@Convert_Ratio", SqlDbType.Int,4),
					new SqlParameter("@Bulk_Unit", SqlDbType.VarChar,10),
					new SqlParameter("@MateManu_Code", SqlDbType.Char,5),
					new SqlParameter("@MateManu_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Class_Code", SqlDbType.Char,5),
					new SqlParameter("@IsUse", SqlDbType.Bit,1),
					new SqlParameter("@Materials_Memo", SqlDbType.VarChar,200)};
			parameters[0].Value = model.Materials_Code;
			parameters[1].Value = model.Materials_Name;
			parameters[2].Value = model.Materials_Py;
            parameters[3].Value = Common.Tools.IsValueNull(model.Materials_Wb);
			parameters[4].Value = model.Materials_Spec;
			parameters[5].Value = model.Pack_Unit;
            parameters[6].Value = Common.Tools.IsValueNull(model.Convert_Ratio);
			parameters[7].Value = model.Bulk_Unit;
            parameters[8].Value = Common.Tools.IsValueNull(model.MateManu_Code);
			parameters[9].Value = model.MateManu_Name;
			parameters[10].Value = model.Class_Code;
			parameters[11].Value = model.IsUse;
			parameters[12].Value = model.Materials_Memo;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ModelOld.M_Materials_Dict model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update Materials_Dict set ");
			strSql.Append("Materials_Name=@Materials_Name,");
			strSql.Append("Materials_Py=@Materials_Py,");
			strSql.Append("Materials_Wb=@Materials_Wb,");
			strSql.Append("Materials_Spec=@Materials_Spec,");
			strSql.Append("Pack_Unit=@Pack_Unit,");
			strSql.Append("Convert_Ratio=@Convert_Ratio,");
			strSql.Append("Bulk_Unit=@Bulk_Unit,");
			strSql.Append("MateManu_Code=@MateManu_Code,");
			strSql.Append("MateManu_Name=@MateManu_Name,");
			strSql.Append("Class_Code=@Class_Code,");
			strSql.Append("IsUse=@IsUse,");
			strSql.Append("Materials_Memo=@Materials_Memo");
			strSql.Append(" where Materials_Code=@Materials_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Materials_Name", SqlDbType.VarChar,200),
					new SqlParameter("@Materials_Py", SqlDbType.VarChar,200),
					new SqlParameter("@Materials_Wb", SqlDbType.VarChar,200),
					new SqlParameter("@Materials_Spec", SqlDbType.VarChar,50),
					new SqlParameter("@Pack_Unit", SqlDbType.VarChar,10),
					new SqlParameter("@Convert_Ratio", SqlDbType.Int,4),
					new SqlParameter("@Bulk_Unit", SqlDbType.VarChar,10),
					new SqlParameter("@MateManu_Code", SqlDbType.Char,5),
					new SqlParameter("@MateManu_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Class_Code", SqlDbType.Char,5),
					new SqlParameter("@IsUse", SqlDbType.Bit,1),
					new SqlParameter("@Materials_Memo", SqlDbType.VarChar,200),
					new SqlParameter("@Materials_Code", SqlDbType.Char,10)};
			parameters[0].Value = model.Materials_Name;
			parameters[1].Value = model.Materials_Py;
            parameters[2].Value = Common.Tools.IsValueNull(model.Materials_Wb);
			parameters[3].Value = model.Materials_Spec;
			parameters[4].Value = model.Pack_Unit;
            parameters[5].Value = Common.Tools.IsValueNull(model.Convert_Ratio);
			parameters[6].Value = model.Bulk_Unit;
            parameters[7].Value = DBNull.Value;
                //Common.Tools.IsValueNull(model.MateManu_Code);
			parameters[8].Value = model.MateManu_Name;
			parameters[9].Value = model.Class_Code;
			parameters[10].Value = model.IsUse;
			parameters[11].Value = model.Materials_Memo;
			parameters[12].Value = model.Materials_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string Materials_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Materials_Dict ");
			strSql.Append(" where Materials_Code=@Materials_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Materials_Code", SqlDbType.Char,10)			};
			parameters[0].Value = Materials_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string Materials_Codelist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Materials_Dict ");
			strSql.Append(" where Materials_Code in ("+Materials_Codelist + ")  ");
			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Materials_Dict GetModel(string Materials_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 Materials_Code,Materials_Name,Materials_Py,Materials_Wb,Materials_Spec,Pack_Unit,Convert_Ratio,Bulk_Unit,MateManu_Code,MateManu_Name,Class_Code,IsUse,Materials_Memo from Materials_Dict ");
			strSql.Append(" where Materials_Code=@Materials_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Materials_Code", SqlDbType.Char,10)			};
			parameters[0].Value = Materials_Code;

			ModelOld.M_Materials_Dict model=new ModelOld.M_Materials_Dict();
			DataSet ds=HisVar.HisVar.Sqldal.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}
		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Materials_Dict DataRowToModel(DataRow row)
		{
			ModelOld.M_Materials_Dict model=new ModelOld.M_Materials_Dict();
			if (row != null)
			{
				if(row["Materials_Code"]!=null)
				{
					model.Materials_Code=row["Materials_Code"].ToString();
				}
				if(row["Materials_Name"]!=null)
				{
					model.Materials_Name=row["Materials_Name"].ToString();
				}
				if(row["Materials_Py"]!=null)
				{
					model.Materials_Py=row["Materials_Py"].ToString();
				}
				if(row["Materials_Wb"]!=null)
				{
					model.Materials_Wb=row["Materials_Wb"].ToString();
				}
				if(row["Materials_Spec"]!=null)
				{
					model.Materials_Spec=row["Materials_Spec"].ToString();
				}
				if(row["Pack_Unit"]!=null)
				{
					model.Pack_Unit=row["Pack_Unit"].ToString();
				}
				if(row["Convert_Ratio"]!=null && row["Convert_Ratio"].ToString()!="")
				{
					model.Convert_Ratio=int.Parse(row["Convert_Ratio"].ToString());
				}
				if(row["Bulk_Unit"]!=null)
				{
					model.Bulk_Unit=row["Bulk_Unit"].ToString();
				}
				if(row["MateManu_Code"]!=null)
				{
					model.MateManu_Code=row["MateManu_Code"].ToString();
				}
				if(row["MateManu_Name"]!=null)
				{
					model.MateManu_Name=row["MateManu_Name"].ToString();
				}
				if(row["Class_Code"]!=null)
				{
					model.Class_Code=row["Class_Code"].ToString();
				}
				if(row["IsUse"]!=null && row["IsUse"].ToString()!="")
				{
					if((row["IsUse"].ToString()=="1")||(row["IsUse"].ToString().ToLower()=="true"))
					{
						model.IsUse=true;
					}
					else
					{
						model.IsUse=false;
					}
				}
				if(row["Materials_Memo"]!=null)
				{
					model.Materials_Memo=row["Materials_Memo"].ToString();
				}
			}
			return model;
		}
        public DataSet GetListForDr()
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Materials_Name,Materials_Spec,Pack_Unit,Convert_Ratio,Bulk_Unit,MateManu_Name,Materials_Memo ");
            strSql.Append(" FROM Materials_Dict ");
            strSql.Append(" where 1=2");

            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

        public DataSet GetListForGridInit(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(" SELECT Materials_Code,Materials_Name,Materials_Py,Materials_Spec,Pack_Unit,Convert_Ratio,Bulk_Unit,MateManu_Name,Materials_Class_Dict.Class_Code,Class_Name,IsUse,Materials_Memo ");
            strSql.Append(" FROM Materials_Dict,Materials_Class_Dict ");
            strSql.Append(" WHERE Materials_Dict.Class_Code=Materials_Class_Dict.Class_Code");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" and (" + strWhere + ")");
            }
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select Materials_Code,Materials_Name,Materials_Py,Materials_Wb,Materials_Spec,Pack_Unit,Convert_Ratio,Bulk_Unit,MateManu_Code,MateManu_Name,Class_Code,IsUse,Materials_Memo ");
			strSql.Append(" FROM Materials_Dict ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" Materials_Code,Materials_Name,Materials_Py,Materials_Wb,Materials_Spec,Pack_Unit,Convert_Ratio,Bulk_Unit,MateManu_Code,MateManu_Name,Class_Code,IsUse,Materials_Memo ");
			strSql.Append(" FROM Materials_Dict ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM Materials_Dict ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.Materials_Code desc");
			}
			strSql.Append(")AS Row, T.*  from Materials_Dict T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Materials_Dict";
			parameters[1].Value = "Materials_Code";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return HisVar.HisVar.Sqldal.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

