﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Windows.Forms;
using Common;
using ERX.Model;

namespace ERX
{
    public partial class ErxQuery : Common.BaseForm.BaseFather
    {
        private MdlhospRxDetlQueryOut mdlhospRxDetlQueryOut = new MdlhospRxDetlQueryOut();
        public ErxQuery(MdlhospRxDetlQueryOut mdlhospRxDetlQueryOut)
        {
            InitializeComponent();
            this.mdlhospRxDetlQueryOut = mdlhospRxDetlQueryOut;
        }

        private void ErxQuery_Load(object sender, EventArgs e)
        {
            myGrid1.Init_Grid(); //处方信息
            myGrid1.Init_Column("医保处方编号", "hiRxno", 112, "左", "", false);
            myGrid1.Init_Column("定点医疗机构编号 ", "fixmedinsCode", 222, "左", "", false);
            myGrid1.Init_Column("定点医药机构名称", "fixmedinsName", 222, "左", "", false);
            myGrid1.Init_Column("医保处方状态编码", "rxStasCodg", 222, "左", "", false);
            myGrid1.Init_Column("医保处方状态名称", "rxStasName", 222, "左", "", false);
            myGrid1.Init_Column("医保处方使用状态编码", "rxUsedStasCodg", 222, "左", "", false);
            myGrid1.Init_Column("医保处方使用状态名称", "rxUsedStasName", 222, "左", "", false);
            myGrid1.Init_Column("开方时间", "prscTime", 112, "左", "", false);
            myGrid1.Init_Column("药品数量(剂数)", "rxDrugCnt", 200, "左", "", false);
            myGrid1.Init_Column("处方整剂用法编号", "rxUsedWayCodg", 222, "左", "", false);
            myGrid1.Init_Column("处方整剂用法名称", "rxUsedWayName", 222, "左", "", false);
            myGrid1.Init_Column("处方整剂频次编号", "rxFrquCodg", 222, "左", "", false);
            myGrid1.Init_Column("处方整剂频次名称", "rxFrquName", 222, "左", "", false);
            myGrid1.Init_Column("处方整剂剂量单位", "rxDosunt", 222, "左", "", false);
            myGrid1.Init_Column("处方整剂单次剂量数", "rxDoscnt", 222, "左", "", false);
            myGrid1.Init_Column("方整剂医嘱说明", "rxDrordDscr", 222, "左", "", false);
            myGrid1.Init_Column("处方有效天数 ", "valiDays", 112, "左", "", false);
            myGrid1.Init_Column("有效截止时间", "valiEndTime", 112, "左", "", false);
            myGrid1.Init_Column("复用(多次)使用标志", "reptFlag", 222, "左", "", false);
            myGrid1.Init_Column("最大复用次数 ", "maxReptCnt", 112, "左", "", false);
            myGrid1.Init_Column("己复用次数 ", "reptdCnt", 112, "左", "", false);
            myGrid1.Init_Column("使用最小间隔(天数)", "minInrvDays", 222, "左", "", false);
            myGrid1.Init_Column("处方类别编号 ", "rxTypeCode", 112, "左", "", false);
            myGrid1.Init_Column("处方类别名称 ", "rxTypeName", 112, "左", "", false);
            myGrid1.Init_Column("长期处方标志", "longRxFlag", 112, "左", "", false);
            myGrid1.ExtendRightColumn = true;
            myGrid1.AllowSort = true;
            List<MdlhospRxDetlQueryOut> list1 = new List<MdlhospRxDetlQueryOut>();
            list1.Add(mdlhospRxDetlQueryOut);   
            DataTable dt = DataTableToList.ToDataTable(list1);
            myGrid1.DataTable = dt;

            myGrid2.Init_Grid(); //处方明细信息
            myGrid2.Init_Column("医疗目录编码 ", "medListCodg", 200, "左", "", false);
            myGrid2.Init_Column("定点医药机构目录编号", "fixmedinsHilistld", 222, "左", "", false);
            myGrid2.Init_Column("院内制剂标志", "hospPrepFlag", 200, "左", "", false);
            myGrid2.Init_Column("处方项目分类编码", "rxItemTypeCode", 222, "左", "", false);
            myGrid2.Init_Column("处方项目分类名称", "rxItemTypeName", 222, "左", "", false);
            myGrid2.Init_Column("中药类别名称", "tcmdrugTypeName", 112, "左", "", false);
            myGrid2.Init_Column("中药类别代码", "tcmdrugTypeCode", 112, "左", "", false);
            myGrid2.Init_Column("草药脚注", "tcmherbFoote", 112, "左", "", false);
            myGrid2.Init_Column("药物类型代码", "mednTypeCode", 112, "左", "", false);
            myGrid2.Init_Column("药物类型", "mednTypeName", 112, "左", "", false);
            myGrid2.Init_Column("主要用药标志 ", "mainMedcFlag", 222, "左", "", false);
            myGrid2.Init_Column("加急标志 ", "urgtFlag", 112, "左", "", false);
            myGrid2.Init_Column("基本药物标志", "basMednFlag", 200, "左", "", false);
            myGrid2.Init_Column("是否进口药品", "impDrugFlag", 112, "左", "", false);
            myGrid2.Init_Column("药品商品名", "drugProdname", 112, "左", "", false);
            myGrid2.Init_Column("通用名编码", "gennameCodg", 112, "左", "", false);
            myGrid2.Init_Column("药品通用名", "drugGenname", 112, "左", "", false);
            myGrid2.Init_Column("药品剂型", "drugDosform", 112, "左", "", false);
            myGrid2.Init_Column("药品规格", "drugSpec", 200, "左", "", false);
            myGrid2.Init_Column("生产厂家", "prdrName", 112, "左", "", false);
            myGrid2.Init_Column("药品单价", "drugPric", 112, "左", "", false);
            myGrid2.Init_Column("药品总金额", "drugSumamt", 112, "左", "", false);
            myGrid2.Init_Column("用药途径代码 ", "medcWayCodg", 200, "左", "", false);
            myGrid2.Init_Column("用药途径描述", "medcWayDscr", 200, "左", "", false);
            myGrid2.Init_Column("用药开始时间", "medcBegntime", 200, "左", "", false);
            myGrid2.Init_Column("用药结束时间", "medcEndtime", 200, "左", "", false);
            myGrid2.Init_Column("用药天数", "medcDays", 112, "左", "", false);
            myGrid2.Init_Column("药品发药数量", "drugCnt", 200, "左", "", false);
            myGrid2.Init_Column("药品发药单位 ", "drugDosunt", 200, "左", "", false);
            myGrid2.Init_Column("单次用量", "sinDoscnt", 112, "左", "", false);
            myGrid2.Init_Column("单次剂量单位", "sinDosunt", 200, "左", "", false);
            myGrid2.Init_Column("使用频次编码", "usedFrquCodg", 200, "左", "", false);
            myGrid2.Init_Column("使用频次名称", "usedFrquName", 200, "左", "", false);
            myGrid2.Init_Column("用药总量", "drugTotlcnt", 112, "左", "", false);
            myGrid2.Init_Column("用药总量单位", "drugTotlcntEmp", 200, "左", "", false);
            myGrid2.Init_Column("医院审批标志", "hospApprFlag", 200, "左", "", false);
            myGrid2.ExtendRightColumn = true;
            myGrid2.AllowSort = true;
            DataTable dt_CfMx = DataTableToList.ToDataTable(mdlhospRxDetlQueryOut.rxDetlList);
            myGrid2.DataTable = dt_CfMx;

            myGrid3.Init_Grid();//就诊信息
            myGrid3.Init_Column("医疗类别", "medType", 112, "左", "", false);
            myGrid3.Init_Column("住院/门诊号", "iptOpNo", 200, "左", "", false);
            myGrid3.Init_Column("门诊住院标志", "otpIptFlag", 200, "左", "", false);
            myGrid3.Init_Column("患者姓名", "patnName", 112, "左", "", false);
            myGrid3.Init_Column("年龄", "patnAge", 112, "左", "", false);
            myGrid3.Init_Column("患者身高 ", "patnHgt", 112, "左", "", false);
            myGrid3.Init_Column("患者体重", "patnWt", 112, "左", "", false);
            myGrid3.Init_Column("性别", "gend", 112, "左", "", false);
            myGrid3.Init_Column("妊娠（孕周)", "gesoVal", 112, "左", "", false);
            myGrid3.Init_Column("新生儿标志 ", "nwbFlag", 112, "左", "", false);
            myGrid3.Init_Column("新生儿日、月龄", "nwbAge", 112, "左", "", false);
            myGrid3.Init_Column("哺乳期标志 ", "suckPrdFag", 112, "左", "", false);
            myGrid3.Init_Column("过敏史", "algsHis", 112, "左", "", false);
            myGrid3.Init_Column("险种类型", "insutype", 112, "左", "", false);
            myGrid3.Init_Column("开方科室名称", "prscDeptName", 200, "左", "", false);
            myGrid3.Init_Column("开方医师姓名", "prscDrName", 200, "左", "", false);
            myGrid3.Init_Column("药师姓名", "pharName", 112, "左", "", false);
            myGrid3.Init_Column("医疗机构药师审方时间", "pharChkTime", 222, "左", "", false);
            myGrid3.Init_Column("就诊时间 ", "mdtrtTime", 112, "左", "", false);
            myGrid3.Init_Column("病种编码", "diseCodg", 112, "左", "", false);
            myGrid3.Init_Column("病种名称 ", "diseName", 112, "左", "", false);
            myGrid3.Init_Column("是否特殊病种 ", "spDiseFlag", 200, "左", "", false);
            myGrid3.Init_Column("主诊断代码", "maindiagCode", 222, "左", "", false);
            myGrid3.Init_Column("主诊断名称 ", "maindiagName", 222, "左", "", false);
            myGrid3.Init_Column("疾病病情描述", "diseCondDscr", 222, "左", "", false);
            myGrid3.Init_Column("是否初诊", "fstdiagFlag", 112, "左", "", false);
            myGrid3.ExtendRightColumn = true;
            myGrid3.AllowSort = true;
            List<MdlrxOtpinfoOut> list2 = new List<MdlrxOtpinfoOut>();
            list2.Add(mdlhospRxDetlQueryOut.rxOtpinfo);
            DataTable dt_JzXx = DataTableToList.ToDataTable(list2);
            myGrid3.DataTable = dt_JzXx;

            myGrid4.Init_Grid();//诊断信息
            myGrid4.Init_Column("诊断类别", "diagType", 112, "左", "", false);
            myGrid4.Init_Column("主诊断标志", "maindiagFlag", 112, "左", "", false);
            myGrid4.Init_Column("诊断排序号", "diagSrtNo", 112, "左", "", false);
            myGrid4.Init_Column("诊断代码", "diagCode", 112, "左", "", false);
            myGrid4.Init_Column("诊断名称", "diagName", 112, "左", "", false);
            myGrid4.Init_Column("诊断科室", "diagDept", 112, "左", "", false);
            myGrid4.Init_Column("诊断医生编码", "diagDrNo", 200, "左", "", false);
            myGrid4.Init_Column("诊断医生姓名", "diagDrName", 200, "左", "", false);
            myGrid4.Init_Column("诊断时间 ", "diagTime", 112, "左", "", false);
            myGrid4.Init_Column("中医病名代码 ", "tcmDiseCode", 200, "左", "", false);
            myGrid4.Init_Column("中医病名", "tcmDiseName", 112, "左", "", false);
            myGrid4.Init_Column("中医症候代码 ", "tcmsympCode", 200, "左", "", false);
            myGrid4.Init_Column("中医症候", "tcmsymp", 112, "左", "", false);
            myGrid4.ExtendRightColumn = true;
            myGrid4.AllowSort = true;
            DataTable dt_ZdXx = DataTableToList.ToDataTable(mdlhospRxDetlQueryOut.rxDiseList);
            myGrid4.DataTable = dt_ZdXx;

        }
    }
}
