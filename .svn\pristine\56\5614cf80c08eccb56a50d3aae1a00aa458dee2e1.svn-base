﻿Imports System.Data.SqlClient
Imports System.Windows.Forms
Imports System.Drawing
Public Class MaterialsBuyInSearch
    Dim My_Table As New DataTable
    Public My_Dataset As New DataSet
    Public Zb_Cm As CurrencyManager             '同步指针
    Public Zb_Row As DataRow                    '选 择 行
    Private layoutreset As Boolean
    Dim str_select As String
    Dim bllMaterialsBuyIn As New BLLOld.B_Materials_Buy_In1

    Private Sub MaterialsStockSearch_Load(sender As System.Object, e As System.EventArgs) Handles MyBase.Load
        Form_Init()
        Me.Focus()

    End Sub
#Region "初始化窗体"

    Private Sub Form_Init()
        commandLabel.Text = "∑=" + MyGrid1.Splits(0).Rows.Count.ToString

        '初始化信息列表

        With MyGrid1
            .Init_Grid()
            .Init_Column("单据状态", "OrdersStatus", "40", "中", "", False)
            .Init_Column("冲销状态", "WriteOffStatus", "40", "中", "", False)
            .Init_Column("采购编码", "M_Buy_Code", "0", "左", "", False)
            .Init_Column("供应商名称", "MaterialsSup_Name", "180", "左", "", False)
            .Init_Column("库房名称", "MaterialsWh_Name", "180", "左", "", False)
            .Init_Column("订货日期", "Order_Date", "150", "中", "yyyy-MM-dd", False)
            .Init_Column("到货日期", "Arrival_Date", "150", "中", "yyyy-MM-dd", False)
            .Init_Column("录入日期", "Input_Date", "150", "中", "yyyy-MM-dd", False)
            .Init_Column("完成日期", "Finish_Date", "150", "中", "yyyy-MM-dd", False)
            .Init_Column("经手人", "Jsr_Name", "120", "左", "", False)
            .Init_Column("物资名称", "Materials_Name", "180", "左", "", False)
            .Init_Column("物资批号", "MaterialsLot", "100", "中", "", False)
            .Init_Column("物资有效期", "MaterialsExpiryDate", "150", "中", "yyyy-MM-dd", False)
            .Init_Column("采购数量", "M_Buy_Num", 100, "右", "", False)
            .Init_Column("购买冲销数量", "M_Buy_WriteoffNo", 100, "右", "######", False)
            .Init_Column("购买实际数量", "M_Buy_RealNo", 100, "右", "######", False)
            .Init_Column("入库数量", "M_BuyIn_Num", 100, "右", "######", False)
            .Init_Column("入库冲销数量", "M_BuyIn_WriteoffNo", 100, "右", "######", False)
            .Init_Column("入库实际数量", "M_BuyIn_RealNo", 100, "右", "######", False)
            .Init_Column("入库单价", "M_BuyIn_Price", 100, "右", "0.####", False)
            .Init_Column("采购单价", "M_Buy_Price", 100, "右", "0.####", False)
            .Init_Column("采购金额", "M_Buy_Money", 100, "右", "0.####", False)
            .Init_Column("实际采购金额", "M_Buy_RealMoney", 100, "右", "0.####", False)
            .Init_Column("包装单位", "Pack_Unit", 100, "中", "", False)
            .Init_Column("拆分比例", "Convert_Ratio", 100, "中", "", False)
            .Init_Column("散装单位", "Bulk_Unit", 100, "中", "", False)
            .Init_Column("采购备注", "M_BuyDetail_Memo", 200, "左", "", False)
            .Xmlpath = HisVar.HisVar.Parapath & "\" & Me.Name & MyGrid1.Name & ".xml"
            .CanCustomCol = True
            .CanGroup = True
            .ColumnFooters = True
            .AllowUpdate = True
        End With

        'Drag a column header here to group by that column
        MyGrid1.Splits(0).DisplayColumns("OrdersStatus").FetchStyle = True
        MyGrid1.Splits(0).DisplayColumns("WriteOffStatus").FetchStyle = True
        MyGrid1.Columns("M_Buy_RealNo").Aggregate = C1.Win.C1TrueDBGrid.AggregateEnum.Sum
        MyGrid1.Columns("M_BuyIn_RealNo").Aggregate = C1.Win.C1TrueDBGrid.AggregateEnum.Sum
        MyGrid1.Columns("M_Buy_RealMoney").Aggregate = C1.Win.C1TrueDBGrid.AggregateEnum.Sum
        MyGrid1.Columns("M_Buy_Num").Aggregate = C1.Win.C1TrueDBGrid.AggregateEnum.Sum

       
    End Sub
#End Region
#Region "控件动作"

    Private Sub commandCz_Click(sender As Object, e As C1.Win.C1Command.ClickEventArgs) Handles commandCz.Click
        Try
            System.IO.File.Delete(HisVar.HisVar.Parapath & "" & Me.Name & MyGrid1.Name & ".xml")
            layoutreset = True
            MsgBox("本地样式重置成功,请关闭窗体重新打开", vbOKOnly, "提示")
        Catch ex As Exception
        End Try
    End Sub

    Private Sub commandPrint_Click(sender As Object, e As C1.Win.C1Command.ClickEventArgs) Handles commandPrint.Click
          Utilities.Utilities.PrintGrid(Me.Text, MyGrid1)
    End Sub


    Private Sub commandDcExcel_Click(sender As Object, e As C1.Win.C1Command.ClickEventArgs) Handles commandDcExcel.Click
        BaseFunc.BaseFunc.ExportExcel(Me.Text, MyGrid1, "", False)
    End Sub
    Private Sub Kc_CxKeyDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles MyBase.KeyDown
        If e.KeyData = Keys.F5 Then
            Call BD_C1_Grid()
        End If
    End Sub


    Private Sub commandCx_Click(sender As Object, e As C1.Win.C1Command.ClickEventArgs) Handles commandCx.Click
        Call BD_C1_Grid()
    End Sub
    Private Sub MyGrid1_DoubleClick(sender As System.Object, e As System.EventArgs) Handles MyGrid1.DoubleClick
        'Dim model As New List(Of ModelOld.M_Materials_Buy_In2WriteOff)
        'model = bllMaterialsBuyIn.GetWOModelList(" and Materials_Buy_In1.M_Buy_Code= '" & MyGrid1.Columns("M_Buy_Code").Value & "'")
        'Dim frm As New Materials_Buy_In1(model)
        'frm.Name = frm.Name & model(0).M_Buy_Code
        'BaseFunc.BaseFunc.addTabControl(frm, "采购单-" & model(0).M_Buy_Code)

    End Sub
    Private Sub commandSx_Click(sender As Object, e As C1.Win.C1Command.ClickEventArgs) Handles commandSx.Click
        If str_select Is Nothing Then
            MsgBox("请先查询后刷新", vbOKOnly, "提示")
            Exit Sub
        End If
        My_Table = bllMaterialsBuyIn.GetHzList(str_select).Tables(0)
        MyGrid1.DataTable = My_Table
        commandLabel.Text = "∑=" + MyGrid1.Splits(0).Rows.Count.ToString

    End Sub

    Private Sub MyGrid1_FetchCellStyle(sender As Object, e As C1.Win.C1TrueDBGrid.FetchCellStyleEventArgs) Handles MyGrid1.FetchCellStyle
        If MyGrid1.Columns("OrdersStatus").CellValue(e.Row) Is Nothing Then
            Return
        End If

        Dim strFlag As String
        If e.Column.DataColumn.DataField = "OrdersStatus" Then
            strFlag = MyGrid1.Columns("OrdersStatus").CellValue(MyGrid1.RowBookmark(e.Row)).ToString()
            If strFlag = "录入" Then
                e.CellStyle.ForegroundImage = My.Resources.Resources.录入16
            End If
            If strFlag = "完成" Then
                e.CellStyle.ForegroundImage = My.Resources.Resources.完成16
            End If

            e.CellStyle.ForeGroundPicturePosition = C1.Win.C1TrueDBGrid.ForeGroundPicturePositionEnum.PictureOnly
        End If
        If Me.MyGrid1.Columns("WriteOffStatus").CellValue(e.Row) Is Nothing Then
            Return
        End If
        If e.Column.DataColumn.DataField = "WriteOffStatus" Then
            strFlag = MyGrid1.Columns("WriteOffStatus").CellValue(MyGrid1.RowBookmark(e.Row)).ToString()
            If strFlag = "冲销" Then
                e.CellStyle.ForegroundImage = My.Resources.Resources.冲销16
            ElseIf strFlag = "被冲销" Then
                e.CellStyle.ForegroundImage = My.Resources.Resources.被冲销16
            End If
            e.CellStyle.ForeGroundPicturePosition = C1.Win.C1TrueDBGrid.ForeGroundPicturePositionEnum.PictureOnly
        End If
    End Sub

#End Region

#Region "自定义函数"

    '查询

    Private Sub BD_C1_Grid()

        Dim vform As New MBISCondition
        If vform.ShowDialog = DialogResult.OK Then
            str_select = vform.outputstr
            'vform.Dispose()
            My_Table = bllMaterialsBuyIn.GetHzList(str_select).Tables(0)
            MyGrid1.DataTable = My_Table
            commandLabel.Text = "∑=" + MyGrid1.Splits(0).Rows.Count.ToString
            F_Sum()
        End If
    End Sub

    Public Overrides Sub F_Sum()
        Dim Sum1 As Double = 0
        Dim Sum2 As Double = 0
        Dim Sum3 As Double = 0
        Dim Sum4 As Double = 0
        Dim Sum5 As Double = 0
        If MyGrid1.RowCount <> 0 Then
            Sum1 = IIf(My_Table.Compute("Sum(M_Buy_RealNo)", "") Is DBNull.Value, 0, My_Table.Compute("Sum(M_Buy_RealNo)", ""))
            Sum2 = IIf(My_Table.Compute("Sum(M_BuyIn_RealNo)", "") Is DBNull.Value, 0, My_Table.Compute("Sum(M_BuyIn_RealNo)", ""))
            Sum3 = IIf(My_Table.Compute("Sum(M_Buy_RealMoney)", "") Is DBNull.Value, 0, My_Table.Compute("Sum(M_Buy_RealMoney)", ""))
            Sum4 = IIf(My_Table.Compute("Sum(M_Buy_Num)", "") Is DBNull.Value, 0, My_Table.Compute("Sum(M_Buy_Num)", ""))

        End If

        With MyGrid1
            .Columns("M_Buy_RealNo").FooterText = Format(Sum1, "#####,##0.####")
            .Columns("M_BuyIn_RealNo").FooterText = Format(Sum2, "#####,##0.####")
            .Columns("M_Buy_RealMoney").FooterText = Format(Sum3, "#####,##0.00##")
            .Columns("M_Buy_Num").FooterText = Format(Sum4, "#####,##0.####")

        End With
    End Sub
#End Region

End Class