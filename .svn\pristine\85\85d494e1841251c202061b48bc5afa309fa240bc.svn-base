﻿Imports System.Data.SqlClient
Imports System.Text

Public Class DcCondition


#Region "变量__定义"
    Dim My_Adapter As New SqlDataAdapter
    Dim My_Dataset As New DataSet
    Dim My_Table As New DataTable
    Dim strMaterialsClass As String
    Dim strMaterials As String
    'Dim My_Cm As CurrencyManager
    Dim Materials_StockBll As New BLLOld.B_Materials_Stock
    Public str_where As String
#End Region



    Private Sub Cl_Dc_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Call Form_Init()
    End Sub

    Private Sub C1Button2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyButton2.Click
        Me.DialogResult = Windows.Forms.DialogResult.Cancel
    End Sub

    Public Sub Form_Init()

        MaterialsClassTextBox.Text = ""


        Dim MaterialsWhBll As New BLLOld.B_Materials_Warehouse_Dict
        With WhComboBox
            .DataView = MaterialsWhBll.GetList("isuse=1").Tables(0).DefaultView
            .Init_Colum("MaterialsWh_Py", "物资仓库简称", 100, "左")
            .Init_Colum("MaterialsWh_Name", "物资仓库名称", 210, "左")
            .Init_Colum("MaterialsWh_Code", "编码", 0, "左")
            .DisplayMember = "MaterialsWh_Name"
            .ValueMember = "MaterialsWh_Code"
            .DroupDownWidth = .Width - .CaptainWidth
            .MaxDropDownItems = 15
            .SelectedIndex = -1
            .RowFilterTextNull = ""
            .RowFilterNotTextNull = "MaterialsWh_Py"
        End With

        My_Table.Columns.Add("Materials_Name")
        My_Table.Columns.Add("Materials_Code")
        My_Table.Columns.Add("MaterialsStock_Code")
        My_Table.Columns.Add("MaterialsWh_Code")
        My_Table.Columns.Add("MaterialsWh_Name")
        My_Table.Columns.Add("MaterialsLot")
        My_Table.Columns.Add("MaterialsExpiryDate")
        My_Table.Columns.Add("Materials_Spec")
        My_Table.Columns.Add("M_Paper_Num")
        My_Table.Columns.Add("M_Real_Num")
        My_Table.Columns.Add("M_Check_Price")
        My_Table.Columns.Add("M_CheckDetail_Memo")
        WhComboBox.Focus()
    End Sub

    Public Sub BD_C1_Grid()
        Dim str As String = " Materials_Stock.MaterialsWh_Code='" & WhComboBox.SelectedValue & "'"
        If MaterialsClassTextBox.Text.Trim <> "" Then
            str += " and Materials_Class_Dict.class_code in " & strMaterialsClass
        End If

        If MaterialsTextBox1.Text.Trim <> "" Then
            str += " and Materials_Dict.Materials_code in " & strMaterials
        End If

        My_Table = Materials_StockBll.GetListDc(str).Tables(0)
        If My_Table.Columns.Count = 0 Then
            Exit Sub
        Else
            Call Dc_Excel()
        End If

    End Sub

    Private Sub Dc_Excel()

        Dim dir As New Dictionary(Of String, Common.MdlExcel)()
        dir.Add("Materials_Name", New Common.MdlExcel(11, "", "物资名称", True))
        dir.Add("Materials_Code",  New Common.MdlExcel(9, "", "物资编码", True))
        dir.Add("MaterialsStock_Code",  New Common.MdlExcel(9, "", "库存号", True))
        dir.Add("MaterialsWh_Code",  New Common.MdlExcel(9, "", "库房号", True))
        dir.Add("MaterialsWh_Name",  New Common.MdlExcel(11, "", "库房名称", True))
        dir.Add("MaterialsLot",  New Common.MdlExcel(14, "", "物资批号", True))
        dir.Add("MaterialsExpiryDate",  New Common.MdlExcel(16, "", "物资有效期", True))
        dir.Add("Materials_Spec",  New Common.MdlExcel(11, "", "物资规格", True))
        dir.Add("M_Paper_Num",  New Common.MdlExcel(5, "", "账面数量", True))
        dir.Add("M_Real_Num",  New Common.MdlExcel(5, "", "盘点数量", True))
        dir.Add("M_Check_Price",  New Common.MdlExcel(5, "", "单价", True))
        dir.Add("M_CheckDetail_Memo",  New Common.MdlExcel(10, "", "备注", True))
        Common.ExcelHelper.Export(My_Table, "物资盘点表", dir)

    End Sub



#Region "控件__动作"

    Private Sub MyButton1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyButton1.Click
        If WhComboBox.SelectedIndex = -1 Then
            MsgBox("请选择仓库！", MsgBoxStyle.Exclamation, "提示")
            WhComboBox.Focus()
            Exit Sub
        End If
        Call BD_C1_Grid()
        Me.DialogResult = Windows.Forms.DialogResult.OK
    End Sub

    Private Sub MyButton_Kq_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MaterialsClassMyButton.Click, MaterialsMyButton3.Click
        If WhComboBox.SelectedIndex = -1 Then
            MsgBox("请选择仓库！", MsgBoxStyle.Exclamation, "提示")
            WhComboBox.Focus()
            Exit Sub
        End If
        Dim str As String = " Materials_Stock.MaterialsWh_Code='" & WhComboBox.SelectedValue & "'"
        If sender.tag = "物资类别" Then
        Else
            If MaterialsClassTextBox.Text.Trim <> "" Then
                str += " and Materials_Class_Dict.class_code in " & strMaterialsClass
            End If
        End If
        Dim f As New PublicForm(sender.tag, str)
        f.Owner = Me
        If f.ShowDialog = Windows.Forms.DialogResult.OK Then
            If sender.tag = "物资类别" Then
                strMaterialsClass = f.str_where
                MaterialsClassTextBox.Text = f.str_text
            Else
                strMaterials = f.str_where
                MaterialsTextBox1.Text = f.str_text
            End If
     
        End If
    End Sub

#End Region

    Private Sub WhComboBox_RowChange(sender As Object, e As System.EventArgs) Handles WhComboBox.RowChange
        strMaterialsClass = ""
        strMaterials = ""
        MaterialsClassTextBox.Text = ""
        MaterialsTextBox1.Text = ""
    End Sub
End Class
