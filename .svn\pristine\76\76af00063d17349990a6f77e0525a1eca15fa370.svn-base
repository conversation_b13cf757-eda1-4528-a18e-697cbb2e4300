﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="ContextMenuStrip1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="AddNode.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADrwAAA68AZW8ckkAAAAGYktHRAD/AP8A/6C9p5MAAALuSURBVDhPdVJbSJNhGH7n
        YWoyLLXDnFCR5mlaaVikkocIK/SiqwiVSsEbJcXQMksxckJdVHNzmydSE6WiPGSIGlq4eT5tIomEkog2
        u5A2mFA+fd8/jSJ84IGX5/D+3/f/P/0NrzByT1HJkgtbInWKzug1RecZW/FbuS2v2fd7qtpVl/SIkn0i
        yH0r/i/2hpP3lccHy3vm8s1jq3noXUhE51wMWmcj8dp0EjWGEyh46Wk+X0LlvqfIe6tmx75jJMmtjugY
        XS1C72IcdHoXqD4RlP12qtlcbXBD03gAFF0yXC537JCFk0Qoex4hUXyRKPPd3LXNblZWDTigcoCgM0jQ
        /TlDoE4vgUZPjI5oGPNDSceBzfh7lOnFuhR6VRTyoC3C2L+UBI1BDO0gCawbCYbZYsKa1YS60eA/unZQ
        jBfjcmTUOJmOp5CcLhS6pVXpz663zByGmgVUjEoDoWo4EN8s0wL5zDXuaYYI9RNS3G+VrsfnUxol3nIt
        a5qMhW7MGephEfq+ZGNgsQhDX5/BYluBZWNFmLnGPZ7h2af9IYjLoTJKyBUpaocjoeULRpzx85cNO4F7
        KpbRjTtD+TEU8TmkoNibVKZk26omXaAeJXxYyIJ+qRRDyyr8YE+3bKxieFktaNxTj7ITTIjxpC8YsXxB
        dBZll7T5WOuN+6GZYPccJ1SMsa8wyd6BdRpmq5EtDxI0FSPPPGfZ4jaZNYZ1KeASBaepHKYap+TQTomh
        mWJvmrHWGMjK9gV1piBBs3ti1E/KwTusG0JefrQrKovKHnb5oNHkh0ojCzLqjB7oWbzBmI4qNnONk2d4
        lnd4V/iZRE60J/EuvS9tl240GI+iemY3C4tQMc2uw8jn6hkPcK+03WeDZx2cyVMob+NQFIUl5FPL7Tfe
        m1qDP16wYzfPygXyWTvojzvMO8cyPLtV+w8eARcpPaGAXqUqHeevV4oXOFOVTvNc4x7P2KM7QyyRkdT3
        NEUxxm0ximvcs0e2QfQbOO7szSe6BFwAAAAASUVORK5CYII=
</value>
  </data>
  <data name="UpdateNode.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADrwAAA68AZW8ckkAAAAGYktHRAD/AP8A/6C9p5MAAAKdSURBVDhPjZJdSFNhGMef
        42dL0UjToaYZlGJSM4aWmYmJQl9UoGZKhEYXZcQiXENNbZipgZUs58caU9KmS+ZH+xK/SJuO1KVgN0U3
        XnTRVRddlf/e086cI4N+8Oe8PM/7P/9z3uel/4HzJdGl1vB15WDuY6G0NWIJcVm1VHSygfSblV1PA7d0
        u9fVk2k/awYuyITt3qRco1NXnsesPjRKYXMWYfzDOVicyTA5E2Fe3o/hJQn07/eiyZL4q9Na+USwudh3
        hiRXVXFrRudF9DnioZkNhsbuA808bahjziWVPQjTqxoIVqKEsxSVeZ9mRj/noNMZBN1KBFreilFj3Qm5
        2RdyC6HCTFBOsBcss3V/7NrUotEg2In6Jltsstfi79pPwej9sgeNU0moe1mI1v5qLxVrCNVD0ShrPpLJ
        bJzLzTAvaKCwilDPPk3lSMexCnrGyiJX1wWbQtBpRUQ7Mx8XSh6s73rVmtEHG0kxaZQgtDbDJwakllKY
        ezK5TVxbltxPyjd9mPz4DfxT1pEXV9IWfpvzIX++6UZaTv5pd6jKtFgAkyMbrW+SMTzf5TlMN9X68/bL
        qmj4R9GuE48oIC6PtqffpZijclLUDaZ+NToOo3OMw71XhKqeQrVgI5pZMeU3DOXYDQtSNI4mobw7xCVd
        GG7qIqGdScXgwkG0TxPUU4SMSpqPzaA4wU5kW+rD04l4vLAHQzu3A92OEKZQpjDo5iNZPQRddg7qWcJ1
        HSG5mIq3hf75dRcWu1ZfMUAfNUuEZpaiHN+kMUIduw+KYZasoMUDBVTKLPzZeQgIJVFKPuXX2uhbWQ95
        3QG3OoxKiA9Rkm8gBQq2v+BKFJIbss6sH2wdzsRPgp8OL37tnboVBoMhdmRkpIjj/p3igeg3mmBEue5h
        1ucAAAAASUVORK5CYII=
</value>
  </data>
  <metadata name="Image1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>607, 17</value>
  </metadata>
  <data name="Image1.ImageStream" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>
        AAEAAAD/////AQAAAAAAAAAMAgAAAFdTeXN0ZW0uV2luZG93cy5Gb3JtcywgVmVyc2lvbj0yLjAuMC4w
        LCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAACZTeXN0
        ZW0uV2luZG93cy5Gb3Jtcy5JbWFnZUxpc3RTdHJlYW1lcgEAAAAERGF0YQcCAgAAAAkDAAAADwMAAAAy
        BAAAAk1TRnQBSQFMAgEBAwEAAZwBAAGcAQABEAEAARABAAT/ARkBAAj/AUIBTQE2BwABNgMAASgDAAFA
        AwABEAMAAQEBAAEYBgABDNIAKoCWAAOAJAADgJYAA4ADwA8AA8APAAaAAwAqgAwAJIA5AAOAA8ADgAkA
        A8ABAAKAAQAC/wEAAv8BAAL/AQAC/wMAA4ADAAOABAAC/wPAAQAC/wPAAQAC/wPAAQAC/wPAAQAC/wPA
        AQAC/wOADAADgAQAAv8DwAEAAv8DwAEAAv8DwAEAAv8DwAEAAv8DgDkAA4ADwAOADAADwAEAAoABAAL/
        AQACgAEAAoADAAPAAwADgAMAA8ABAAL/A8ABAAL/A8ABAAL/A8ABAAL/A8ABAAL/A8ADgAkAA4AEAAL/
        A8ABAAL/A8ABAAL/A8ABAAL/A8ABAAL/A8ADAAOAOQADgAPAA4AJAAPAAQACgAEAAoAMwAMAA4AEAAL/
        A8ABAAL/A8ABAAL/A8ABAAL/A8ABAAL/A8ABAAL/A4AJAAOAAwADwAEAAv8DwAEAAv8DwAEAAv8DwAEA
        Av8DwAOAAwADgDkAIYADAAOAAwADgAMAA8ABAAL/A8ABAAL/A8ABAAL/A8ABAAL/A8ABAAL/A8ADgAYA
        A4ADAAPAAQAC/wPAAQAC/wPAAQAC/wPAAQAC/wPAAQAC/wMABoBjAAOABAAC/wPAAQAC/wPAAQAC/wPA
        AQAC/wPAAQAC/wPAAQAC/wOABgADgB4AA4ADAAPAA4BjAAOAAwADwAEAAv8DwAEAAv8DwAEAAv8DwAEA
        Av8DwAEAAv8DwAOABgAngAEAAv8DgGMAA4AEAAL/A8ABAAL/A8ABAAL/A8ABAAL/A8ABAAL/A8ABAAL/
        A4AJAAOAAwADwAEAAv8DwAEAAv8DwAEAAv8DwAEAAv8DwAEAAv8DwAOAYwADgCQAA4AJAAOABAAC/wPA
        AQAC/wPAAQAC/wPADwADgEUAAoABAAL/AQACgAEAAoATAAOAA8ABAAL/A8ABAAL/A8ABAAL/A8ASgAkA
        A4ADAAPAAQAC/wPAAQAC/wPAAwASgEgAAoABAAKAGQADgAPAAQAC/wPAAQAC/wPAA4AeAAOADwADgFoA
        AoABAAKAHAAPgCQAD4BdAAKAqQABQgFNAT4HAAE+AwABKAMAAUADAAEQAwABAQEAAQEFAAGAFwAD/wIA
        AQME/wMAAQEE/wIAAX8B+QGAAQEB4AYAAQEBwAMAAYwBAAFAAQEB0AMAAY4BAAFAAQEBoAMAAcYBAAFA
        AQEBoAMAAcABAAFAAQEBQAMAAf8B+wFAAQEBfwHgAgAB/wH9AUABAQQAAf8B/QFAAQEBoAMAAfgBDgF/
        AfkBoAF8AgAB/AEZAQABAwGgAYECAAH+AScBgAH/Ad8BfwIAAf4BHwHBAf8B4AH/AgAB/gF/BP8CAAs=
</value>
  </data>
</root>