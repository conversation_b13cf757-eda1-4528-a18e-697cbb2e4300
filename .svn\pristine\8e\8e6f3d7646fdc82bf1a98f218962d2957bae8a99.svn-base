﻿/**  版本信息模板在安装目录下，可自行修改。
* M_KC22.cs
*
* 功 能： N/A
* 类 名： M_KC22
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/8/24 08:44:38   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace ModelOld
{
	/// <summary>
	/// M_KC22:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class M_KC22
	{
		public M_KC22()
		{}
		#region Model
		private string _akc190;
		private string _akc220;
		private DateTime _akc221;
		private string _akc515;
		private string _akc223;
		private string _akc224;
		private decimal _akc225;
		private decimal _akc226;
		private decimal _akc227;
		private string _aka070;
		private decimal? _aka071;
		private string _aka076;
		private string _aka072;
		private string _aka073;
		private decimal? _akc229;
		private string _zka100;
		private decimal? _ckc126;
		/// <summary>
		/// 住院号（门诊号）
		/// </summary>
		public string AKC190
		{
			set{ _akc190=value;}
			get{return _akc190;}
		}
		/// <summary>
		/// 处方号
		/// </summary>
		public string AKC220
		{
			set{ _akc220=value;}
			get{return _akc220;}
		}
		/// <summary>
		/// 处方日期
		/// </summary>
		public DateTime AKC221
		{
			set{ _akc221=value;}
			get{return _akc221;}
		}
		/// <summary>
		/// 医院收费项目编码
		/// </summary>
		public string AKC515
		{
			set{ _akc515=value;}
			get{return _akc515;}
		}
		/// <summary>
		/// 收费项目名称
		/// </summary>
		public string AKC223
		{
			set{ _akc223=value;}
			get{return _akc223;}
		}
		/// <summary>
		/// 药品/诊疗/床位费详见指标代码标AKC224
		/// </summary>
		public string AKC224
		{
			set{ _akc224=value;}
			get{return _akc224;}
		}
		/// <summary>
		/// 单价
		/// </summary>
		public decimal AKC225
		{
			set{ _akc225=value;}
			get{return _akc225;}
		}
		/// <summary>
		/// 数量
		/// </summary>
		public decimal AKC226
		{
			set{ _akc226=value;}
			get{return _akc226;}
		}
		/// <summary>
		/// 金额
		/// </summary>
		public decimal AKC227
		{
			set{ _akc227=value;}
			get{return _akc227;}
		}
		/// <summary>
		/// 剂型
		/// </summary>
		public string AKA070
		{
			set{ _aka070=value;}
			get{return _aka070;}
		}
		/// <summary>
		/// 每次用量（工伤必录项）
		/// </summary>
		public decimal? AKA071
		{
			set{ _aka071=value;}
			get{return _aka071;}
		}
		/// <summary>
		/// 每次用量单位（工伤必录项）
		/// </summary>
		public string AKA076
		{
			set{ _aka076=value;}
			get{return _aka076;}
		}
		/// <summary>
		/// 使用频次（工伤必录项）
		/// </summary>
		public string AKA072
		{
			set{ _aka072=value;}
			get{return _aka072;}
		}
		/// <summary>
		/// 用法
		/// </summary>
		public string AKA073
		{
			set{ _aka073=value;}
			get{return _aka073;}
		}
		/// <summary>
		/// 执行天数（工伤必录项）
		/// </summary>
		public decimal? AKC229
		{
			set{ _akc229=value;}
			get{return _akc229;}
		}
		/// <summary>
		/// 规格名称
		/// </summary>
		public string ZKA100
		{
			set{ _zka100=value;}
			get{return _zka100;}
		}
		/// <summary>
		/// 传输标志详见指标代码标CKC126
		/// </summary>
		public decimal? CKC126
		{
			set{ _ckc126=value;}
			get{return _ckc126;}
		}
		#endregion Model

	}
}

