﻿using System;
using System.Collections.Generic;
using System.Text;

namespace ModelOld
{
    [Serializable]
   public class M_ZkAndBlAndMb
    {
        public M_ZkAndBlAndMb()
		{}
		#region Model
		private string _bl_code;
		private int _id;
        private string _mb_name;
        private string _mb_code;
		private int? _kf_count;
		private decimal? _kf;
        private string _kf_pz;
        private string _mx_name;
        private string _kf_pznr;
        private decimal? _zk_kf;
		/// <summary>
		/// 
		/// </summary>
		public string Bl_Code
		{
			set{ _bl_code=value;}
			get{return _bl_code;}
		}
        /// <summary>
        /// 1：按等级；2：单项；3：多项
        /// </summary>
        public string Kf_Pz
        {
            set { _kf_pz = value; }
            get { return _kf_pz; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string Mx_Name
        {
            set { _mx_name = value; }
            get { return _mx_name; }
        }
		/// <summary>
		/// 
		/// </summary>
		public int id
		{
			set{ _id=value;}
			get{return _id;}
		}
        /// <summary>
        /// 
        /// </summary>
        public string Mb_Name
        {
            set { _mb_name = value; }
            get { return _mb_name; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string Mb_Code
        {
            set { _mb_code = value; }
            get { return _mb_code; }
        }
		/// <summary>
		/// 
		/// </summary>
		public int? Kf_Count
		{
			set{ _kf_count=value;}
			get{return _kf_count;}
		}
		/// <summary>
		/// 
		/// </summary>
		public decimal? Kf
		{
			set{ _kf=value;}
			get{return _kf;}
		}
        /// <summary>
        /// 扣分标准
        /// </summary>
        public string Kf_PzNr
        {
            set { _kf_pznr = value; }
            get { return _kf_pznr; }
        }
        /// <summary>
        /// 扣分
        /// </summary>
        public decimal? Zk_Kf
        {
            set { _zk_kf = value; }
            get { return _zk_kf; }
        }
		#endregion Model


    }
}
