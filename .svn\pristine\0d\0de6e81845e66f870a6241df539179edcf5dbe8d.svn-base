﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>
    </SchemaVersion>
    <ProjectGuid>{A9F1F12F-836F-4D3A-BC60-7526BFFAA57C}</ProjectGuid>
    <OutputType>Library</OutputType>
    <RootNamespace>ZtHis.Lis</RootNamespace>
    <AssemblyName>ZtHis.Lis</AssemblyName>
    <FileAlignment>512</FileAlignment>
    <MyType>Windows</MyType>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <OutputPath>bin\Debug\</OutputPath>
    <DocumentationFile>ZtHis.Lis.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
    <PlatformTarget>x86</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DocumentationFile>ZtHis.Lis.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
  </PropertyGroup>
  <PropertyGroup>
    <OptionExplicit>On</OptionExplicit>
  </PropertyGroup>
  <PropertyGroup>
    <OptionCompare>Binary</OptionCompare>
  </PropertyGroup>
  <PropertyGroup>
    <OptionStrict>Off</OptionStrict>
  </PropertyGroup>
  <PropertyGroup>
    <OptionInfer>On</OptionInfer>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="C1.Win.C1Command.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=e808566f358766d8, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.C1Input.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=7e7ff60f0c214f9a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.C1List.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=6b24f8f981dbd7bc, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.C1Ribbon.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.C1TrueDBGrid.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=75ae3fb0e2b1e0da, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="SqlDal">
      <HintPath>..\Dll\SqlDal.dll</HintPath>
    </Reference>
    <Reference Include="Stimulsoft.Base, Version=2011.2.1026.0, Culture=neutral, PublicKeyToken=ebe6666cba19647a, processorArchitecture=MSIL" />
    <Reference Include="Stimulsoft.Controls, Version=2011.2.1026.0, Culture=neutral, PublicKeyToken=ebe6666cba19647a, processorArchitecture=MSIL" />
    <Reference Include="Stimulsoft.Controls.Win, Version=2011.2.1026.0, Culture=neutral, PublicKeyToken=ebe6666cba19647a, processorArchitecture=MSIL" />
    <Reference Include="Stimulsoft.Design, Version=2011.2.1026.0, Culture=neutral, PublicKeyToken=ebe6666cba19647a, processorArchitecture=MSIL" />
    <Reference Include="Stimulsoft.Editor, Version=2011.2.1026.0, Culture=neutral, PublicKeyToken=ebe6666cba19647a, processorArchitecture=MSIL" />
    <Reference Include="Stimulsoft.Report, Version=2011.2.1026.0, Culture=neutral, PublicKeyToken=ebe6666cba19647a, processorArchitecture=MSIL" />
    <Reference Include="Stimulsoft.Report.Design, Version=2011.2.1026.0, Culture=neutral, PublicKeyToken=ebe6666cba19647a, processorArchitecture=MSIL" />
    <Reference Include="Stimulsoft.Report.Win, Version=2011.2.1026.0, Culture=neutral, PublicKeyToken=ebe6666cba19647a, processorArchitecture=MSIL" />
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Import Include="Microsoft.VisualBasic" />
    <Import Include="System" />
    <Import Include="System.Collections" />
    <Import Include="System.Collections.Generic" />
    <Import Include="System.Data" />
    <Import Include="System.Diagnostics" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="2.检验项目管理\LIS_TestXm1.designer.vb">
      <DependentUpon>LIS_TestXm1.vb</DependentUpon>
    </Compile>
    <Compile Include="2.检验项目管理\LIS_TestXm1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="2.检验项目管理\LIS_TestXm2.designer.vb">
      <DependentUpon>LIS_TestXm2.vb</DependentUpon>
    </Compile>
    <Compile Include="2.检验项目管理\LIS_TestXm2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="2.检验项目管理\LIS_TestItem.designer.vb">
      <DependentUpon>LIS_TestItem.vb</DependentUpon>
    </Compile>
    <Compile Include="2.检验项目管理\LIS_TestItem.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="2.检验项目管理\Lis_TestXmRpt.designer.vb">
      <DependentUpon>Lis_TestXmRpt.vb</DependentUpon>
    </Compile>
    <Compile Include="2.检验项目管理\Lis_TestXmRpt.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="4. 检验项目关联\JySfDy.Designer.vb">
      <DependentUpon>JySfDy.vb</DependentUpon>
    </Compile>
    <Compile Include="4. 检验项目关联\JySfDy.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="My Project\AssemblyInfo.vb" />
    <Compile Include="My Project\Application.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
    </Compile>
    <Compile Include="My Project\Resources.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="My Project\Settings.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <Compile Include="3.检验元字典\LISMetaDic11.Designer.vb">
      <DependentUpon>LISMetaDic11.vb</DependentUpon>
    </Compile>
    <Compile Include="3.检验元字典\LISMetaDic11.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="3.检验元字典\LISMetaDic12.Designer.vb">
      <DependentUpon>LISMetaDic12.vb</DependentUpon>
    </Compile>
    <Compile Include="3.检验元字典\LISMetaDic12.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="检验工作站\Test_Jy1.Designer.vb">
      <DependentUpon>Test_Jy1.vb</DependentUpon>
    </Compile>
    <Compile Include="检验工作站\Test_Jy1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="检验申请\TestSq1.Designer.vb">
      <DependentUpon>TestSq1.vb</DependentUpon>
    </Compile>
    <Compile Include="检验申请\TestSq1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="采集工作站\Test_Cj.Designer.vb">
      <DependentUpon>Test_Cj.vb</DependentUpon>
    </Compile>
    <Compile Include="采集工作站\Test_Cj.vb">
      <SubType>Form</SubType>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="2.检验项目管理\LIS_TestXm1.resx">
      <DependentUpon>LIS_TestXm1.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="2.检验项目管理\LIS_TestXm2.resx">
      <DependentUpon>LIS_TestXm2.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="2.检验项目管理\LIS_TestItem.resx">
      <DependentUpon>LIS_TestItem.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="2.检验项目管理\Lis_TestXmRpt.resx">
      <DependentUpon>Lis_TestXmRpt.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="4. 检验项目关联\JySfDy.resx">
      <DependentUpon>JySfDy.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="My Project\licenses.licx" />
    <EmbeddedResource Include="My Project\Resources.resx">
      <Generator>VbMyResourcesResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.vb</LastGenOutput>
      <CustomToolNamespace>My.Resources</CustomToolNamespace>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="3.检验元字典\LISMetaDic11.resx">
      <DependentUpon>LISMetaDic11.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="3.检验元字典\LISMetaDic12.resx">
      <DependentUpon>LISMetaDic12.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="检验工作站\Test_Jy1.resx">
      <DependentUpon>Test_Jy1.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="检验申请\TestSq1.resx">
      <DependentUpon>TestSq1.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="采集工作站\Test_Cj.resx">
      <DependentUpon>Test_Cj.vb</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
    <None Include="My Project\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <CustomToolNamespace>My</CustomToolNamespace>
      <LastGenOutput>Settings.Designer.vb</LastGenOutput>
    </None>
    <None Include="Resources\上.png" />
    <None Include="Resources\下.png" />
    <None Include="Resources\空.png" />
    <None Include="Resources\拒检.png" />
    <None Include="Resources\待采集.png" />
    <None Include="Resources\待检验.png" />
    <None Include="Resources\待审核.png" />
    <None Include="Resources\录入.png" />
    <Content Include="项目报告单.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\BaseClass\BaseClass.vbproj">
      <Project>{48964D0C-25C0-413C-A94A-C2246ADE46A1}</Project>
      <Name>BaseClass</Name>
    </ProjectReference>
    <ProjectReference Include="..\BaseFunc\BaseFunc.vbproj">
      <Project>{C7865854-F754-41AC-9912-829068BE5C2A}</Project>
      <Name>BaseFunc</Name>
    </ProjectReference>
    <ProjectReference Include="..\BLLOld\BLLOld.csproj">
      <Project>{42c23254-ba1f-4222-895b-276fc06bf59c}</Project>
      <Name>BLLOld</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common\Common.csproj">
      <Project>{92E350A0-3691-4B8D-A07E-EBB0F10E6997}</Project>
      <Name>Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\CustomControl\CustomControl.csproj">
      <Project>{12BF4168-D60E-4A6C-85BF-926130EE6A6D}</Project>
      <Name>CustomControl</Name>
    </ProjectReference>
    <ProjectReference Include="..\HisControl\HisControl.vbproj">
      <Project>{EF778791-6E30-4A07-83B9-2D73FD08810A}</Project>
      <Name>HisControl</Name>
    </ProjectReference>
    <ProjectReference Include="..\HisVar\HisVar.vbproj">
      <Project>{4DAD5E14-6224-46B2-886D-6D22CE3886BC}</Project>
      <Name>HisVar</Name>
    </ProjectReference>
    <ProjectReference Include="..\Jkk\Jkk.csproj">
      <Project>{ADEC5669-F0C3-435D-937D-D2CB079EE994}</Project>
      <Name>Jkk</Name>
    </ProjectReference>
    <ProjectReference Include="..\ModelOld\ModelOld.csproj">
      <Project>{1FAF80FE-D42E-4FEB-853A-B9846C1862AE}</Project>
      <Name>ModelOld</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\copy_48px_1133968_easyicon.net.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\export_48px_1133978_easyicon.net.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Icon_1469.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\import_48px_1133993_easyicon.net.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\打印.bmp" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\导出.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\导入.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\反选.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\关闭痕迹.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\关闭审阅.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\开启痕迹.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\开启审阅.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\全选.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\删除.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\刷新.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\修改.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\已出院.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\增加.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\否.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\是.png" />
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.VisualBasic.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>