﻿/**  版本信息模板在安装目录下，可自行修改。
* M_Materials_Other_In1.cs
*
* 功 能： N/A
* 类 名： M_Materials_Other_In1
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2017/1/4 16:37:28   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace ModelOld
{
	/// <summary>
	/// 物资其他入库主表
	/// </summary>
	[Serializable]
	public partial class M_Materials_Other_In1
	{
		public M_Materials_Other_In1()
		{}
		#region Model
		private string _m_otherin_code;
		private string _materialsinout_code;
		private string _materialswh_code;
		private DateTime? _otherin_date;
		private DateTime? _input_date;
		private DateTime? _finish_date;
		private string _jsr_code;
        private string _jsr_name;
		private decimal? _totalmoney;
		private string _m_otherin_memo;
		private string _ordersstatus;
		private string _writeoff_code;
		private string _writeoffstatus;
		/// <summary>
		/// yyMMdd+5位流水号
		/// </summary>
		public string M_OtherIn_Code
		{
			set{ _m_otherin_code=value;}
			get{return _m_otherin_code;}
		}
		/// <summary>
		/// 出入类别编码
		/// </summary>
		public string MaterialsInOut_Code
		{
			set{ _materialsinout_code=value;}
			get{return _materialsinout_code;}
		}
		/// <summary>
		/// 库房编码
		/// </summary>
		public string MaterialsWh_Code
		{
			set{ _materialswh_code=value;}
			get{return _materialswh_code;}
		}
		/// <summary>
		/// 入库日期
		/// </summary>
		public DateTime? OtherIn_Date
		{
			set{ _otherin_date=value;}
			get{return _otherin_date;}
		}
		/// <summary>
		/// 录入时间
		/// </summary>
		public DateTime? Input_Date
		{
			set{ _input_date=value;}
			get{return _input_date;}
		}
		/// <summary>
		/// 完成时间
		/// </summary>
		public DateTime? Finish_Date
		{
			set{ _finish_date=value;}
			get{return _finish_date;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Jsr_Code
		{
			set{ _jsr_code=value;}
			get{return _jsr_code;}
		}
        /// <summary>
        /// 
        /// </summary>
        public string Jsr_Name
        {
            set { _jsr_name = value; }
            get { return _jsr_name; }
        }
		/// <summary>
		/// 总金额
		/// </summary>
		public decimal? TotalMoney
		{
			set{ _totalmoney=value;}
			get{return _totalmoney;}
		}
		/// <summary>
		/// 备注
		/// </summary>
		public string M_OtherIn_Memo
		{
			set{ _m_otherin_memo=value;}
			get{return _m_otherin_memo;}
		}
		/// <summary>
		/// 单据状态
		/// </summary>
		public string OrdersStatus
		{
			set{ _ordersstatus=value;}
			get{return _ordersstatus;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string WriteOff_Code
		{
			set{ _writeoff_code=value;}
			get{return _writeoff_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string WriteOffStatus
		{
			set{ _writeoffstatus=value;}
			get{return _writeoffstatus;}
		}
		#endregion Model

	}
}

