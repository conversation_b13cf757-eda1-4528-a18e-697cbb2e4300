﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="0" />
    <DataSources isList="true" count="1">
      <日结明细 Ref="2" type="DataTableSource" isKey="true">
        <Alias>日结明细</Alias>
        <Columns isList="true" count="10">
          <value>Ks_Name,System.String</value>
          <value>Ys_Name,System.String</value>
          <value>Ry_Name,System.String</value>
          <value>Bxlb_Name,System.String</value>
          <value>Yp_Name,System.String</value>
          <value>Mz_Lb,System.String</value>
          <value>Mz_Date,System.DateTime</value>
          <value>Mz_Time,System.String</value>
          <value>Mz_Sl,System.Decimal</value>
          <value>M,System.Decimal</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>日结明细</Name>
        <NameInSource>日结明细</NameInSource>
      </日结明细>
    </DataSources>
    <Relations isList="true" count="0" />
    <Report isRef="0" />
    <Variables isList="true" count="2">
      <value>,统计人,统计人,System.String,,False,False</value>
      <value>,打印时间,打印时间,System.String,,False,False</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="3" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="1">
        <ReportTitleBand1 Ref="4" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,27.7,1.2</ClientRectangle>
          <Components isList="true" count="5">
            <Text1 Ref="5" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,27.7,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>黑体,18,Regular,Point,False,134</Font>
              <Guid>290b941340fd44759ed2129bc5b8b578</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text1</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>医生日报表</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text1>
            <Text3 Ref="6" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.7,9.3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,0</Font>
              <Guid>b2997702f5a04a62951c0ed3b04ee9cb</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text3</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>{统计人}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text3>
            <Text4 Ref="7" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>9.3,0.7,9.2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,0</Font>
              <Guid>cd5ee2394ac146f996aba2f5496b9936</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text4</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>财务核对人：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text4>
            <Text5 Ref="8" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>18.5,0.7,9.2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,0</Font>
              <Guid>51780d8363894105b83573fd3dd4a551</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text5</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>{打印时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text5>
            <CrossTab1 Ref="9" type="Stimulsoft.Report.CrossTab.StiCrossTab" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.2,27.7,7.8</ClientRectangle>
              <Components isList="true" count="14">
                <CrossTab1_ColTotal1 Ref="10" type="CrossColumnTotal" isKey="true">
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>[255:255:255]</Brush>
                  <ClientRectangle>6.05,0.45,0.9,0.5</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>宋体,10.5,Regular,Point,False,134</Font>
                  <Guid>c6af7d718f0145fab77a69aca9c143b7</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_ColTotal1</Name>
                  <Page isRef="3" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <Text>合计</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                </CrossTab1_ColTotal1>
                <CrossTab1_RowTotal1 Ref="11" type="CrossRowTotal" isKey="true">
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>[255:255:255]</Brush>
                  <ClientRectangle>0,2.5,4.9,0.5</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>宋体,10.5,Regular,Point,False,134</Font>
                  <Guid>110cdb6045c14a29990c770b43052ad1</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_RowTotal1</Name>
                  <Page isRef="3" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <Text>总计</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                </CrossTab1_RowTotal1>
                <CrossTab1_Row1_Title Ref="12" type="CrossTitle" isKey="true">
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>White</Brush>
                  <ClientRectangle>0,0.45,1.6,0.5</ClientRectangle>
                  <Font>宋体,10.5,Regular,Point,False,134</Font>
                  <Guid>f562c6b3a8d740308d41b987c6af31d8</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_Row1_Title</Name>
                  <Page isRef="3" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <Text>科室名称</Text>
                  <TextBrush>[105:105:105]</TextBrush>
                  <Type>Expression</Type>
                  <TypeOfComponent>Row:CrossTab1_Row1</TypeOfComponent>
                </CrossTab1_Row1_Title>
                <CrossTab1_LeftTitle Ref="13" type="CrossTitle" isKey="true">
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>White</Brush>
                  <ClientRectangle>0,0,4.9,0.4</ClientRectangle>
                  <Enabled>False</Enabled>
                  <Font>Arial,8</Font>
                  <Guid>4a5052e67a334aeb9907d2f52d1e5639</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_LeftTitle</Name>
                  <Page isRef="3" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <Text>日结明细</Text>
                  <TextBrush>[105:105:105]</TextBrush>
                  <TypeOfComponent>LeftTitle</TypeOfComponent>
                </CrossTab1_LeftTitle>
                <CrossTab1_RowTotal2 Ref="14" type="CrossRowTotal" isKey="true">
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>[255:255:255]</Brush>
                  <ClientRectangle>1.6,2,3.3,0.5</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>宋体,10.5,Regular,Point,False,134</Font>
                  <Guid>c05242e5a6424057b6321ca0fc813d65</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_RowTotal2</Name>
                  <Page isRef="3" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <Text>合计</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                </CrossTab1_RowTotal2>
                <CrossTab1_Row2_Title Ref="15" type="CrossTitle" isKey="true">
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>White</Brush>
                  <ClientRectangle>1.6,0.45,1.6,0.5</ClientRectangle>
                  <Font>宋体,10.5,Regular,Point,False,134</Font>
                  <Guid>27dafd36811a4cd198e881e804fc600f</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_Row2_Title</Name>
                  <Page isRef="3" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <Text>医生姓名</Text>
                  <TextBrush>[105:105:105]</TextBrush>
                  <Type>Expression</Type>
                  <TypeOfComponent>Row:CrossTab1_Row2</TypeOfComponent>
                </CrossTab1_Row2_Title>
                <CrossTab1_RowTotal3 Ref="16" type="CrossRowTotal" isKey="true">
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>[255:255:255]</Brush>
                  <ClientRectangle>3.2,1.5,1.7,0.5</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>宋体,10.5,Regular,Point,False,134</Font>
                  <Guid>971721349786401198172454fb884a5f</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_RowTotal3</Name>
                  <Page isRef="3" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <Text>小计</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                </CrossTab1_RowTotal3>
                <CrossTab1_Row3_Title Ref="17" type="CrossTitle" isKey="true">
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>White</Brush>
                  <ClientRectangle>3.2,0.45,1.7,0.5</ClientRectangle>
                  <Font>宋体,10.5,Regular,Point,False,134</Font>
                  <Guid>7ecece2de9df4330b0d848148b58031d</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_Row3_Title</Name>
                  <Page isRef="3" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <Text>患者姓名</Text>
                  <TextBrush>[105:105:105]</TextBrush>
                  <Type>Expression</Type>
                  <TypeOfComponent>Row:CrossTab1_Row3</TypeOfComponent>
                </CrossTab1_Row3_Title>
                <CrossTab1_Row1 Ref="18" type="CrossRow" isKey="true">
                  <Alias>Ks_Name</Alias>
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>White</Brush>
                  <ClientRectangle>0,1,1.6,1.5</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <DisplayValue>{日结明细.Ks_Name}</DisplayValue>
                  <Font>宋体,10.5,Regular,Point,False,134</Font>
                  <Guid>d5c26d3683b24d73964cce8b26f45360</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_Row1</Name>
                  <Page isRef="3" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <Text>Ks_Name</Text>
                  <TextBrush>[105:105:105]</TextBrush>
                  <TotalGuid>110cdb6045c14a29990c770b43052ad1</TotalGuid>
                  <Value>{日结明细.Ks_Name}</Value>
                </CrossTab1_Row1>
                <CrossTab1_Row2 Ref="19" type="CrossRow" isKey="true">
                  <Alias>Ys_Name</Alias>
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>White</Brush>
                  <ClientRectangle>1.6,1,1.6,1</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <DisplayValue>{日结明细.Ys_Name}</DisplayValue>
                  <Font>宋体,10.5,Regular,Point,False,134</Font>
                  <Guid>db2a2cac1fef4e92bdd9039b5633dd04</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_Row2</Name>
                  <Page isRef="3" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <Text>Ys_Name</Text>
                  <TextBrush>[105:105:105]</TextBrush>
                  <TotalGuid>c05242e5a6424057b6321ca0fc813d65</TotalGuid>
                  <Value>{日结明细.Ys_Name}</Value>
                </CrossTab1_Row2>
                <CrossTab1_Row3 Ref="20" type="CrossRow" isKey="true">
                  <Alias>Ry_Name</Alias>
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>White</Brush>
                  <ClientRectangle>3.2,1,1.7,0.5</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <DisplayValue>{日结明细.Ry_Name}</DisplayValue>
                  <Font>宋体,12</Font>
                  <Guid>9b5d1e610ce840319d0f7a5b1c935322</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_Row3</Name>
                  <Page isRef="3" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <Text>Ry_Name</Text>
                  <TextBrush>[105:105:105]</TextBrush>
                  <TotalGuid>971721349786401198172454fb884a5f</TotalGuid>
                  <Value>{日结明细.Ry_Name}</Value>
                </CrossTab1_Row3>
                <CrossTab1_Column1 Ref="21" type="CrossColumn" isKey="true">
                  <Alias>Mz_Lb</Alias>
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>White</Brush>
                  <ClientRectangle>4.95,0.45,1.1,0.5</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <DisplayValue>{日结明细.Mz_Lb}</DisplayValue>
                  <Font>宋体,10.5,Regular,Point,False,134</Font>
                  <Guid>2a9d3c66cfdc4b47a977b1368e7a9f0b</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_Column1</Name>
                  <Page isRef="3" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <Text>Mz_Lb</Text>
                  <TextBrush>[105:105:105]</TextBrush>
                  <TotalGuid>c6af7d718f0145fab77a69aca9c143b7</TotalGuid>
                  <Value>{日结明细.Mz_Lb}</Value>
                </CrossTab1_Column1>
                <CrossTab1_Sum1 Ref="22" type="CrossSummary" isKey="true">
                  <Alias>M</Alias>
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>[255:255:255]</Brush>
                  <ClientRectangle>4.95,1,1.1,0.5</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>宋体,12</Font>
                  <Guid>2d0027b287f24a6e803bf477acfdf92d</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_Sum1</Name>
                  <Page isRef="3" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <Text>0</Text>
                  <TextBrush>Black</TextBrush>
                  <Value>{日结明细.M}</Value>
                </CrossTab1_Sum1>
                <CrossTab1_RightTitle Ref="23" type="CrossTitle" isKey="true">
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>White</Brush>
                  <ClientRectangle>4.95,0,2,0.4</ClientRectangle>
                  <Enabled>False</Enabled>
                  <Font>Arial,8</Font>
                  <Guid>0a9a55139da9406c8db0cf44568dc550</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_RightTitle</Name>
                  <Page isRef="3" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <Text>Mz_Lb</Text>
                  <TextBrush>[105:105:105]</TextBrush>
                  <TypeOfComponent>RightTitle</TypeOfComponent>
                </CrossTab1_RightTitle>
              </Components>
              <Conditions isList="true" count="0" />
              <DataRelationName />
              <DataSourceName>日结明细</DataSourceName>
              <EmptyValue />
              <Filters isList="true" count="0" />
              <Guid>e666086efce44f88a2f3ea69ca725495</Guid>
              <HorAlignment>Center</HorAlignment>
              <Name>CrossTab1</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Sort isList="true" count="0" />
            </CrossTab1>
          </Components>
          <Conditions isList="true" count="0" />
          <Guid>f27596eccfee412287e5fd9231fccbeb</Guid>
          <Name>ReportTitleBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </ReportTitleBand1>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>477ece7d68bf4bce89cdc720ea797a9e</Guid>
      <Margins>1,1,1,1</Margins>
      <Name>Page1</Name>
      <Orientation>Landscape</Orientation>
      <PageHeight>21</PageHeight>
      <PageWidth>29.7</PageWidth>
      <PaperSize>A4</PaperSize>
      <Report isRef="0" />
      <Watermark Ref="24" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page1>
  </Pages>
  <PrinterSettings Ref="25" type="Stimulsoft.Report.Print.StiPrinterSettings" isKey="true" />
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>科室日报表2</ReportAlias>
  <ReportChanged>4/1/2014 11:32:27 AM</ReportChanged>
  <ReportCreated>10/25/2012 11:09:13 AM</ReportCreated>
  <ReportFile>E:\正在进行时\唐山\正在修改版\his2010\his2010\Rpt\科室日报表.mrt</ReportFile>
  <ReportGuid>505ac4b59d9e4e3faa50cf5d594202f7</ReportGuid>
  <ReportName>科室日报表2</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2011.2.1026</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class Report : Stimulsoft.Report.StiReport
    {
        public Report()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
        #endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>