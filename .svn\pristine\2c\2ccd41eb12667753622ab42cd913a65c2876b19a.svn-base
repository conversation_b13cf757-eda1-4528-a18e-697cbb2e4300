﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class MaterialsReturnSearch
    Inherits HisControl.BaseForm

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.C1CommandLink4 = New C1.Win.C1Command.C1CommandLink()
        Me.commandSx = New C1.Win.C1Command.C1Command()
        Me.C1CommandHolder1 = New C1.Win.C1Command.C1CommandHolder()
        Me.commandCx = New C1.Win.C1Command.C1Command()
        Me.commandLabel = New C1.Win.C1Command.C1Command()
        Me.commandDcExcel = New C1.Win.C1Command.C1Command()
        Me.commandPrint = New C1.Win.C1Command.C1Command()
        Me.MyGrid1 = New CustomControl.MyGrid()
        Me.C1CommandLink1 = New C1.Win.C1Command.C1CommandLink()
        Me.C1CommandLink6 = New C1.Win.C1Command.C1CommandLink()
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.C1ToolBar1 = New C1.Win.C1Command.C1ToolBar()
        Me.C1CommandLink3 = New C1.Win.C1Command.C1CommandLink()
        Me.C1CommandLink2 = New C1.Win.C1Command.C1CommandLink()
        Me.SaveFileDialog1 = New System.Windows.Forms.SaveFileDialog()
        CType(Me.C1CommandHolder1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.TableLayoutPanel1.SuspendLayout()
        Me.Panel1.SuspendLayout()
        Me.SuspendLayout()
        '
        'C1CommandLink4
        '
        Me.C1CommandLink4.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image), C1.Win.C1Command.ButtonLookFlags)
        Me.C1CommandLink4.Command = Me.commandSx
        Me.C1CommandLink4.SortOrder = 3
        '
        'commandSx
        '
        Me.commandSx.Image = Global.ZtHis.Materials.My.Resources.Resources.miBarRefresh_Glyph
        Me.commandSx.Name = "commandSx"
        Me.commandSx.ShortcutText = ""
        Me.commandSx.Text = "刷新数据"
        '
        'C1CommandHolder1
        '
        Me.C1CommandHolder1.Commands.Add(Me.commandSx)
        Me.C1CommandHolder1.Commands.Add(Me.commandCx)
        Me.C1CommandHolder1.Commands.Add(Me.commandLabel)
        Me.C1CommandHolder1.Commands.Add(Me.commandDcExcel)
        Me.C1CommandHolder1.Commands.Add(Me.commandPrint)
        Me.C1CommandHolder1.Owner = Me
        '
        'commandCx
        '
        Me.commandCx.Image = Global.ZtHis.Materials.My.Resources.Resources.miBarSearch_Glyph
        Me.commandCx.Name = "commandCx"
        Me.commandCx.ShortcutText = ""
        Me.commandCx.Text = "数据查询"
        '
        'commandLabel
        '
        Me.commandLabel.Name = "commandLabel"
        Me.commandLabel.ShortcutText = ""
        Me.commandLabel.Text = "label"
        '
        'commandDcExcel
        '
        Me.commandDcExcel.Image = Global.ZtHis.Materials.My.Resources.Resources.miBarExport_Glyph
        Me.commandDcExcel.Name = "commandDcExcel"
        Me.commandDcExcel.ShortcutText = ""
        Me.commandDcExcel.Text = "新命令"
        '
        'commandPrint
        '
        Me.commandPrint.Image = Global.ZtHis.Materials.My.Resources.Resources.miBarParamPrint_Glyph
        Me.commandPrint.Name = "commandPrint"
        Me.commandPrint.ShortcutText = ""
        Me.commandPrint.Text = "新命令"
        '
        'MyGrid1
        '
        Me.MyGrid1.CanCustomCol = False
        Me.MyGrid1.Col = 0
        Me.MyGrid1.ColumnFooters = False
        Me.MyGrid1.DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveRight
        Me.MyGrid1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.MyGrid1.FetchRowStyles = False
        Me.MyGrid1.GroupByAreaVisible = False
        Me.MyGrid1.Location = New System.Drawing.Point(0, 44)
        Me.MyGrid1.Margin = New System.Windows.Forms.Padding(0)
        Me.MyGrid1.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.MyGrid1.Name = "MyGrid1"
        Me.MyGrid1.Size = New System.Drawing.Size(919, 350)
        Me.MyGrid1.TabAction = C1.Win.C1TrueDBGrid.TabActionEnum.ControlNavigation
        Me.MyGrid1.TabIndex = 1
        Me.MyGrid1.Xmlpath = Nothing
        '
        'C1CommandLink1
        '
        Me.C1CommandLink1.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image), C1.Win.C1Command.ButtonLookFlags)
        Me.C1CommandLink1.Command = Me.commandCx
        Me.C1CommandLink1.OwnerDraw = True
        '
        'C1CommandLink6
        '
        Me.C1CommandLink6.ButtonLook = C1.Win.C1Command.ButtonLookFlags.Text
        Me.C1CommandLink6.Command = Me.commandLabel
        Me.C1CommandLink6.Delimiter = True
        Me.C1CommandLink6.SortOrder = 4
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.ColumnCount = 1
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanel1.Controls.Add(Me.Panel1, 0, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.MyGrid1, 0, 1)
        Me.TableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(0, 0)
        Me.TableLayoutPanel1.Margin = New System.Windows.Forms.Padding(0)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.RowCount = 2
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 44.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(919, 394)
        Me.TableLayoutPanel1.TabIndex = 2
        '
        'Panel1
        '
        Me.Panel1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Panel1.Controls.Add(Me.C1ToolBar1)
        Me.Panel1.Location = New System.Drawing.Point(0, 0)
        Me.Panel1.Margin = New System.Windows.Forms.Padding(0)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(919, 44)
        Me.Panel1.TabIndex = 0
        '
        'C1ToolBar1
        '
        Me.C1ToolBar1.AccessibleName = "Tool Bar"
        Me.C1ToolBar1.BackColor = System.Drawing.Color.Transparent
        Me.C1ToolBar1.ButtonLayoutHorz = C1.Win.C1Command.ButtonLayoutEnum.TextBelow
        Me.C1ToolBar1.CommandHolder = Nothing
        Me.C1ToolBar1.CommandLinks.AddRange(New C1.Win.C1Command.C1CommandLink() {Me.C1CommandLink1, Me.C1CommandLink3, Me.C1CommandLink2, Me.C1CommandLink4, Me.C1CommandLink6})
        Me.C1ToolBar1.Location = New System.Drawing.Point(3, 3)
        Me.C1ToolBar1.Margin = New System.Windows.Forms.Padding(0)
        Me.C1ToolBar1.Name = "C1ToolBar1"
        Me.C1ToolBar1.Size = New System.Drawing.Size(288, 41)
        Me.C1ToolBar1.Text = "C1ToolBar1"
        Me.C1ToolBar1.VisualStyle = C1.Win.C1Command.VisualStyle.Custom
        Me.C1ToolBar1.VisualStyleBase = C1.Win.C1Command.VisualStyle.Classic
        '
        'C1CommandLink3
        '
        Me.C1CommandLink3.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image), C1.Win.C1Command.ButtonLookFlags)
        Me.C1CommandLink3.Command = Me.commandDcExcel
        Me.C1CommandLink3.SortOrder = 1
        Me.C1CommandLink3.Text = "导出Excel"
        '
        'C1CommandLink2
        '
        Me.C1CommandLink2.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image), C1.Win.C1Command.ButtonLookFlags)
        Me.C1CommandLink2.Command = Me.commandPrint
        Me.C1CommandLink2.SortOrder = 2
        Me.C1CommandLink2.Text = "打印表格"
        '
        'MaterialsReturnSearch
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(919, 394)
        Me.Controls.Add(Me.TableLayoutPanel1)
        Me.Name = "MaterialsReturnSearch"
        Me.Text = "MaterialsReturnSearch"
        CType(Me.C1CommandHolder1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.TableLayoutPanel1.ResumeLayout(False)
        Me.Panel1.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents C1CommandLink4 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents C1CommandHolder1 As C1.Win.C1Command.C1CommandHolder
    Friend WithEvents commandCx As C1.Win.C1Command.C1Command
    Friend WithEvents commandLabel As C1.Win.C1Command.C1Command
    Private WithEvents commandDcExcel As C1.Win.C1Command.C1Command
    Friend WithEvents commandPrint As C1.Win.C1Command.C1Command
    Friend WithEvents TableLayoutPanel1 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents C1ToolBar1 As C1.Win.C1Command.C1ToolBar
    Friend WithEvents C1CommandLink1 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents C1CommandLink3 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents C1CommandLink2 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents C1CommandLink6 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents MyGrid1 As CustomControl.MyGrid
    Friend WithEvents SaveFileDialog1 As System.Windows.Forms.SaveFileDialog
    Private WithEvents commandSx As C1.Win.C1Command.C1Command
End Class
