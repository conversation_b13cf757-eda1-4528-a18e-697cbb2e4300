﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="0" />
    <DataSources isList="true" count="1">
      <ds Ref="2" type="DataTableSource" isKey="true">
        <Alias>ds</Alias>
        <Columns isList="true" count="15">
          <value>M_Use_Code,System.String</value>
          <value>Materials_Code,System.String</value>
          <value>MaterialsStock_Code,System.String</value>
          <value>M_Use_Detail_Code,System.String</value>
          <value>MaterialsLot,System.String</value>
          <value>MaterialsExpiryDate,System.DateTime</value>
          <value>M_Use_Num,System.Decimal</value>
          <value>M_Use_WriteoffNo,System.Decimal</value>
          <value>M_Use_RealNo,System.Decimal</value>
          <value>M_Use_Price,System.Decimal</value>
          <value>M_Use_Money,System.Decimal</value>
          <value>M_Use_RealMoney,System.Decimal</value>
          <value>M_UseDetail_Memo,System.String</value>
          <value>Materials_Name,System.String</value>
          <value>MaterialsStore_Num,System.Decimal</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>ds</Name>
        <NameInSource>ds</NameInSource>
      </ds>
    </DataSources>
    <Relations isList="true" count="0" />
    <Report isRef="0" />
    <Variables isList="true" count="7">
      <value>,打印时间,打印时间,System.DateTime,_x0031_2_x002F_7_x002F_2016_x0020_10:07:50_x0020_AM,False,False</value>
      <value>,支领时间,支领时间,System.DateTime,_x0031_2_x002F_7_x002F_2016_x0020_10:18:11_x0020_AM,False,False</value>
      <value>,支领科室,支领科室,System.String,,False,False</value>
      <value>,物资仓库,物资仓库,System.String,,False,False</value>
      <value>,支领编码,支领编码,System.String,,False,False</value>
      <value>,操作人,操作人,System.String,,False,False</value>
      <value>,支领人,支领人,System.String,,False,False</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="3" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="6">
        <PageFooterBand1 Ref="4" type="PageFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,26.9,19,0.8</ClientRectangle>
          <Components isList="true" count="1">
            <Text30 Ref="5" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,19,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,0</Font>
              <Guid>d43dfb1a9c304cb1b84e778c375b4d84</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text30</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>第{PageNumberThrough}页</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text30>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>PageFooterBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </PageFooterBand1>
        <Text15 Ref="6" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>19.8,-1.4,1.6,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,9.75</Font>
          <Guid>b309c34984a2411fa0ce691f7496759f</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text15</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Text>打印时间</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text15>
        <ReportTitleBand1 Ref="7" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,19,2.4</ClientRectangle>
          <Components isList="true" count="15">
            <Text1 Ref="8" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,19,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,15,Regular,Point,False,0</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text1</Name>
              <Page isRef="3" />
              <Parent isRef="7" />
              <Text>物 资 支 领 单</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text1>
            <Text2 Ref="9" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.6,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9.75</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text2</Name>
              <Page isRef="3" />
              <Parent isRef="7" />
              <Text>支领编码：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text2>
            <Text3 Ref="10" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>12.4,1.2,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9.75</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text3</Name>
              <Page isRef="3" />
              <Parent isRef="7" />
              <Text>支领时间：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text3>
            <Text33 Ref="11" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.2,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9.75</Font>
              <Guid>608cc8aaa4934118875ec9cbf3e24c46</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text33</Name>
              <Page isRef="3" />
              <Parent isRef="7" />
              <Text>支 领 人：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text33>
            <Text36 Ref="12" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.8,1.2,4.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9.75</Font>
              <Guid>cce28d59c67b4cd2a868fb46fe060abf</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text36</Name>
              <Page isRef="3" />
              <Parent isRef="7" />
              <Text>{支领人}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text36>
            <Text4 Ref="13" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.8,0.6,4.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9.75</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text4</Name>
              <Page isRef="3" />
              <Parent isRef="7" />
              <Text>{支领编码}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text4>
            <Text37 Ref="14" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>6,1.2,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9.75</Font>
              <Guid>e8be7594327143feae6023e7d405af28</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text37</Name>
              <Page isRef="3" />
              <Parent isRef="7" />
              <Text>支领科室：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text37>
            <Text38 Ref="15" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7.8,1.2,4.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9.75</Font>
              <Guid>acaef43406d1443e8078bba44473d861</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text38</Name>
              <Page isRef="3" />
              <Parent isRef="7" />
              <Text>{支领科室}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text38>
            <Text5 Ref="16" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>6,1.8,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9.75</Font>
              <Guid>6715083e700d4bad97866efad81553e8</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text5</Name>
              <Page isRef="3" />
              <Parent isRef="7" />
              <Text>物资仓库：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text5>
            <Text8 Ref="17" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7.8,1.8,4.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9.75</Font>
              <Guid>3ec8798d7b754c30bfa821b8f9f456ff</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text8</Name>
              <Page isRef="3" />
              <Parent isRef="7" />
              <Text>{物资仓库}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text8>
            <Text14 Ref="18" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>14.2,1.2,4.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9.75</Font>
              <Guid>a836bd688ef140fc80ebc0e5e250ed2d</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text14</Name>
              <Page isRef="3" />
              <Parent isRef="7" />
              <Text>{支领时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text14>
            <Text16 Ref="19" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>12.4,1.8,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9.75</Font>
              <Guid>2b682d1941364a7a945a6de6a603f79f</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text16</Name>
              <Page isRef="3" />
              <Parent isRef="7" />
              <Text>打印时间：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text16>
            <Text28 Ref="20" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>14.2,1.8,4.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9.75</Font>
              <Guid>b23fa16da20a48769d05e997dece19ef</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text28</Name>
              <Page isRef="3" />
              <Parent isRef="7" />
              <Text>{打印时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text28>
            <Text29 Ref="21" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.8,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9.75</Font>
              <Guid>064e442dbc4842839f8de4c1cea775d6</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text29</Name>
              <Page isRef="3" />
              <Parent isRef="7" />
              <Text>操 作 人：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text29>
            <Text31 Ref="22" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.8,1.8,4.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9.75</Font>
              <Guid>47b19c65da2b484b93bed329696c3256</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text31</Name>
              <Page isRef="3" />
              <Parent isRef="7" />
              <Text>{操作人}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text31>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>ReportTitleBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </ReportTitleBand1>
        <GroupHeaderBand1 Ref="23" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,3.6,19,0.8</ClientRectangle>
          <Components isList="true" count="9">
            <Text17 Ref="24" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,8.5,Bold</Font>
              <Guid>07af9543ba2641b6b457086b33d9b230</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text17</Name>
              <Page isRef="3" />
              <Parent isRef="23" />
              <Text>物资名称</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text17>
            <Text19 Ref="25" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.2,0,2.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,8.5,Bold</Font>
              <Guid>46fb93dc23e048a580d81d2509168d9d</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text19</Name>
              <Page isRef="3" />
              <Parent isRef="23" />
              <Text>物资有效期</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text19>
            <Text20 Ref="26" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7,0,2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,8.5,Bold</Font>
              <Guid>e34c006987db41b2a9afb31e2ccb31ae</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text20</Name>
              <Page isRef="3" />
              <Parent isRef="23" />
              <Text>支领数量</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text20>
            <Text32 Ref="27" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,8.5,Bold</Font>
              <Guid>24d1a626344940b4a1a8f026e591f598</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text32</Name>
              <Page isRef="3" />
              <Parent isRef="23" />
              <Text>物资批号</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text32>
            <Text18 Ref="28" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9,0,2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,8.5,Bold</Font>
              <Guid>5d541aae67764d059093d20d1d5c04c8</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text18</Name>
              <Page isRef="3" />
              <Parent isRef="23" />
              <Text>冲销数量</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text18>
            <Text21 Ref="29" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>11,0,2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,8.5,Bold</Font>
              <Guid>882bb3b19a924f2199352650d4661124</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text21</Name>
              <Page isRef="3" />
              <Parent isRef="23" />
              <Text>实际支领数量</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text21>
            <Text22 Ref="30" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13,0,2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,8.5,Bold</Font>
              <Guid>a0a49afdb3784ccba28e0406273d7ec7</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text22</Name>
              <Page isRef="3" />
              <Parent isRef="23" />
              <Text>单价</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text22>
            <Text23 Ref="31" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15,0,2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,8.5,Bold</Font>
              <Guid>7458709d791c43da8ed1bdb3037aadf5</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text23</Name>
              <Page isRef="3" />
              <Parent isRef="23" />
              <Text>支领金额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text23>
            <Text24 Ref="32" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>17,0,2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,8.5,Bold</Font>
              <Guid>cf6ee28c93ad447e8e38aa4c9e69bb21</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text24</Name>
              <Page isRef="3" />
              <Parent isRef="23" />
              <Text>实际支领金额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text24>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>GroupHeaderBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </GroupHeaderBand1>
        <DataBand1 Ref="33" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,5.2,19,0.8</ClientRectangle>
          <Components isList="true" count="9">
            <Text7 Ref="34" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,8</Font>
              <Guid>57c841efa692450abf60a59fda504c0b</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text7</Name>
              <Page isRef="3" />
              <Parent isRef="33" />
              <Text>{ds.Materials_Name}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text7>
            <Text9 Ref="35" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2,0,2.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,8</Font>
              <Guid>da4e917a8e374de49251332686fa7d2f</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text9</Name>
              <Page isRef="3" />
              <Parent isRef="33" />
              <Text>{ds.MaterialsLot}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text9>
            <Text10 Ref="36" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.2,0,2.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,8</Font>
              <Guid>bc60e3914660422083b3ee4bc158e9ba</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text10</Name>
              <Page isRef="3" />
              <Parent isRef="33" />
              <Text>{ds.MaterialsExpiryDate}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text10>
            <Text12 Ref="37" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7,0,2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,8</Font>
              <Guid>9db4fbf8417247b48dbcf8d73a11f993</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text12</Name>
              <Page isRef="3" />
              <Parent isRef="33" />
              <Text>{ds.M_Use_Num}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text12>
            <Text25 Ref="38" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9,0,2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,8</Font>
              <Guid>3277d0571f78402b8dcc620b3d38b241</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text25</Name>
              <Page isRef="3" />
              <Parent isRef="33" />
              <Text>{ds.M_Use_WriteoffNo}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text25>
            <Text26 Ref="39" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>11,0,2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,8</Font>
              <Guid>671d453c15ac4f738eece0446929f8a8</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text26</Name>
              <Page isRef="3" />
              <Parent isRef="33" />
              <Text>{ds.M_Use_RealNo}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text26>
            <Text27 Ref="40" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13,0,2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,8</Font>
              <Guid>f60f3e6c82664dad9962aca72276c3d5</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text27</Name>
              <Page isRef="3" />
              <Parent isRef="33" />
              <Text>{ds.M_Use_Price}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text27>
            <Text35 Ref="41" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15,0,2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,8</Font>
              <Guid>64496e01c5b7475696cda67dbb42f204</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text35</Name>
              <Page isRef="3" />
              <Parent isRef="33" />
              <Text>{ds.M_Use_Money}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text35>
            <Text43 Ref="42" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>17,0,2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,8</Font>
              <Guid>ac9161e1b55745a38ca2b5e05e3ded0d</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text43</Name>
              <Page isRef="3" />
              <Parent isRef="33" />
              <Text>{ds.M_Use_RealMoney}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text43>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>ds</DataSourceName>
          <Filters isList="true" count="0" />
          <Name>DataBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Sort isList="true" count="0" />
        </DataBand1>
        <GroupFooterBand1 Ref="43" type="GroupFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,6.8,19,1.6</ClientRectangle>
          <Components isList="true" count="4">
            <Text6 Ref="44" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4,0,15,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9,Bold,Point,False,0</Font>
              <Guid>f88d9d4893e746db81aa84ce7dbe3a84</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text6</Name>
              <Page isRef="3" />
              <Parent isRef="43" />
              <Text>{Sum(DataBand1,ds.M_Use_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text6>
            <Text13 Ref="45" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9,Bold,Point,False,0</Font>
              <Guid>b5579ae8d7c24320bde362bcea096f69</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text13</Name>
              <Page isRef="3" />
              <Parent isRef="43" />
              <Text>支领总金额：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text13>
            <Text39 Ref="46" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4,0.8,15,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9,Bold,Point,False,0</Font>
              <Guid>6141bf9b74854216a7c28d6534945710</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text39</Name>
              <Page isRef="3" />
              <Parent isRef="43" />
              <Text>{Sum(DataBand1,ds.M_Use_RealMoney)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text39>
            <Text40 Ref="47" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.8,4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9,Bold,Point,False,0</Font>
              <Guid>e8a821dd7b80482f8f025f67616d3563</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text40</Name>
              <Page isRef="3" />
              <Parent isRef="43" />
              <Text>实际支领总金额：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text40>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>GroupFooterBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </GroupFooterBand1>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>f1923558814e4988aca9db335330b49d</Guid>
      <Margins>1,1,1,1</Margins>
      <Name>Page1</Name>
      <PageHeight>29.7</PageHeight>
      <PageWidth>21</PageWidth>
      <PaperSize>A4</PaperSize>
      <Report isRef="0" />
      <Watermark Ref="48" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page1>
  </Pages>
  <PrinterSettings Ref="49" type="Stimulsoft.Report.Print.StiPrinterSettings" isKey="true" />
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>物资支领单</ReportAlias>
  <ReportChanged>1/16/2017 5:01:53 PM</ReportChanged>
  <ReportCreated>12/6/2016 4:32:22 PM</ReportCreated>
  <ReportFile>.\Rpt\物资支领表.mrt</ReportFile>
  <ReportGuid>5847ad1ed6384cf3a26e5919f795d038</ReportGuid>
  <ReportName>物资支领单</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2011.2.1026</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class Report : Stimulsoft.Report.StiReport
    {
        public Report()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
        #endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>