﻿Imports System.Threading
Imports System.Text
Imports System.Net.Sockets
Imports System.Net
Imports C1.Win.C1Command
Imports C1.Win.C1Ribbon
Imports System.Deployment.Application
Imports System.Data.SqlClient
Imports System.Text.RegularExpressions
Imports ZtHis.Emr
Imports Common
Imports ZTHisPharmacy

Public Class MainForm
    Delegate Sub Action()
    Private showTimeThread As Thread
    Dim Dir As New Dictionary(Of String, String)
    Dim thread As Threading.Thread
    Dim _bllZdQxModule As New BLL.BllZd_QxModule

    '声音提示
    Private player As New System.Media.SoundPlayer()
    Private p_timer3Start As Boolean
    Private p_Flag As String
    Private m_Icon1 As Icon
    Private m_Icon2 As Icon
    Private m_bFlag As Boolean
    Private Sub F_Main_FormClosed(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosedEventArgs) Handles MyBase.FormClosed
        AbortTimeThread()
    End Sub

    Private Sub F_Main_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        thread = New Threading.Thread(AddressOf Init)
        thread.IsBackground = True
        thread.Start()

        Dim ischeck As String = iniOperate.iniopreate.GetINI("查看更新说明", "check", "", ".\System.Ini")
        If ischeck = "" Or ischeck = "0" Then
            Dim frm As New ZTHisSysManage.UpdateContent
            frm.ShowDialog()
        End If
    End Sub


#Region "界面初始化"
    Delegate Sub MyInvoke()
    Public Sub Init()
        '拷贝到通辽市民卡Dll
        Try
            FileOperateHelper.FileCopy("./通辽市民卡Dll/ZTreadCard/" & HisVar.HisVar.WsyCode & "ZTReadCard.ini", "./通辽市民卡Dll/ZTReadCard.ini")
        Catch ex As Exception
        End Try
        '拷贝到根目录
        Try
            FileOperateHelper.FileCopy("./通辽市民卡Dll/ZTreadCard/" & HisVar.HisVar.WsyCode & "ZTReadCard.ini", "./ZTReadCard.ini")
        Catch ex As Exception
        End Try

        If Me.InvokeRequired = True Then
            Dim del As New MyInvoke(AddressOf Init)
            Me.Invoke(del)
        Else
            Try
                Me.Visible = False
                Gd_News()

                HisVar.HisVar.FMain = Me
                HisVar.HisVar.DockTab = C1DockingTab1
                HisVar.HisVar.DockTab.Visible = False
                Common.WinFormVar.Var.MainForm = Me
                Common.WinFormVar.Var.MainTab = C1DockingTab1
                Me.VisualStyle = C1.Win.C1Ribbon.VisualStyle.Windows7
                C1StatusBar1.VisualStyle = C1.Win.C1Ribbon.VisualStyle.Windows7

                RibbonInit()

                RibbonLabel1.Text = "所属药房:" & ZTHisVar.Var.YfName
                RibbonLabel2.Text = "所属科室:" & ZTHisVar.Var.KsName
                RibbonLabel4.Text = "关联医生:" & HisVar.HisVar.JsrYsName + IIf(String.IsNullOrWhiteSpace(ZTHisInsuranceAPI.Yb_Info.YbYs_Code), String.Empty, $"【医保医师代码:{ZTHisInsuranceAPI.Yb_Info.YbYs_Code}】")
                RibbonLabel3.Text = "登录用户:" & HisVar.HisVar.JsrName
                Me.Text = Me.Text + $"  【{ZTHisInsuranceAPI.Yb_Info.fixmedins_code}|{ZTHisInsuranceAPI.Yb_Info.fixmedins_name}】"
                Me.BackgroundImage = Image.FromFile("./背景.jpg")
                Me.BackgroundImageLayout = ImageLayout.Stretch
                Me.Visible = True
                Call get_mesaage()
            Catch ex As Exception
                MessageBox.Show(ex.ToString())
            End Try
        End If
    End Sub

    Public Sub RibbonInit()
        ' Create a new instance of C1Ribbon and add it to the form.

        C1Ribbon1.Qat.Visible = False
        C1Ribbon1.RestrictedGroupSizing = True    '限制大小

        Dim dldst As New DataSet
        Dim Menu_Name As String = ""
        Dim SecondMenu_Name As String = ""
        dldst = _bllZdQxModule.GetList(" Glz_Code='" & HisVar.HisVar.GlzCode & "'")
        Dim homeTab As RibbonTab = Nothing
        Dim fontGroup As RibbonGroup = Nothing

        CheckParaAndQx(dldst.Tables(0))

        For Each tmp_row In dldst.Tables(0).Rows
            If Menu_Name <> tmp_row("Menu_Name") Then
                homeTab = New RibbonTab
                homeTab = C1Ribbon1.Tabs.Add(tmp_row("Menu_Name"))
                homeTab.Image = ZTHisResources.C_Resources.GetMenuImage(tmp_row("Menu_Name"), 16)
                Menu_Name = tmp_row("Menu_Name")
                If Menu_Name = "护士站" Then
                    CheckHszStation()
                End If
            End If

            If SecondMenu_Name <> tmp_row("SecondMenu_Name") Then
                fontGroup = New RibbonGroup
                fontGroup = homeTab.Groups.Add(tmp_row("SecondMenu_Name"))
                SecondMenu_Name = tmp_row("SecondMenu_Name")
                fontGroup.Items.Capacity = 15
            End If

            If tmp_row.Item("Module_Order") IsNot DBNull.Value Then
                Dim rtb As New RibbonButton
                Try
                    Dim img As Image = ZTHisResources.C_Resources.GetImage32(tmp_row("Xl_Name"))
                    rtb.LargeImage = img
                    rtb.TextImageRelation = TextImageRelation.ImageAboveText
                Catch ex As Exception
                End Try
                rtb.ID = tmp_row("Menu_Code") & "-" & tmp_row("Module_Code")
                rtb.Text = tmp_row("Xl_Name")
                Dir.Add(rtb.ID, tmp_row("Xl_Name"))
                AddHandler rtb.Click, AddressOf BtnClick
                fontGroup.Items.Add(rtb)
            End If

        Next

        For Each _row In HisVar.HisVar.Sqllite.Query("Select * from OftenMoudle  order by MoudleTimes desc LIMIT 10").Tables(0).Rows
            If Dir.ContainsKey(_row("MenuCode")) Then
                Dim Rbn As New RibbonButton
                Rbn.ID = _row.item("MenuCode")
                Rbn.Text = _row.item("MoudleName")
                Dim img As Image = ZTHisResources.C_Resources.GetImage32(_row.item("MoudleName"))
                Rbn.SmallImage = img
                Dim listItem As RibbonListItem = New RibbonListItem(Rbn)
                C1Ribbon1.ApplicationMenu.RightPaneItems.Add(listItem)
                AddHandler listItem.Click, AddressOf BtnClick
            End If
        Next

        Dim m_flag As String = ""
        m_flag = BaseFunc.BaseFunc.getConfig("最小化功能区", "主窗体模块")
        If m_flag = "" Then
            C1Ribbon1.Minimized = True
        Else
            C1Ribbon1.Minimized = m_flag
        End If

    End Sub

#Region "根据系统配置检测权限"

    Private Sub CheckParaAndQx(ByVal DataTable As DataTable)
        Dim _Row As DataRow()

        '未启用门诊药房审核等于是的去掉药房审核选项
        If ZTHisPara.PublicConfig.EnablePharmacyAudit = False Then
            _Row = DataTable.Select("Module_Code='0212'")
            For i As Integer = 0 To _Row.Length - 1
                DataTable.Rows.Remove(_Row(i))
            Next
        End If

        '判断是否启用门诊挂号
        If ZTHisPara.PublicConfig.EnableRegister = False Then
            _Row = DataTable.Select("Module_Code='0406'")
            For i As Integer = 0 To _Row.Length - 1
                DataTable.Rows.Remove(_Row(i))
            Next
        End If

        '判断是否启用护士站
        If HisPara.PublicConfig.ZyHsz <> "是" Then
            _Row = DataTable.Select("Menu_Code='04'")
            For i As Integer = 0 To _Row.Length - 1
                DataTable.Rows.Remove(_Row(i))
            Next
        End If
        '是否启用门诊医生站
        If HisPara.PublicConfig.MzYsz <> "是" Then
            _Row = DataTable.Select("Module_Code='0403' or Module_Code='0404' ")
            For i As Integer = 0 To _Row.Length - 1
                DataTable.Rows.Remove(_Row(i))
            Next
        Else
            _Row = DataTable.Select("Module_Code='0401'")
            For i As Integer = 0 To _Row.Length - 1
                DataTable.Rows.Remove(_Row(i))
            Next
        End If
    End Sub


    Private Sub CheckHszStation()
        If HisPara.PublicConfig.NurseStation = True And HisPara.PublicConfig.ZyHsz = "是" Then
            Dim frm As New 护士站.NurseStation
            BaseFunc.BaseFunc.addTabControl(frm, frm.Text)
        End If
    End Sub

#End Region

    ''' <summary>
    ''' 显示时间
    ''' </summary>
    ''' 
    Private Sub ShowTimeThread2()
        Dim threadDelegate As New ThreadStart(AddressOf Work.DoWork)
        Dim newThread As New Thread(threadDelegate)
        showTimeThread = New Thread(New ThreadStart(AddressOf ShowTimeThread3))
        showTimeThread.IsBackground = True
        showTimeThread.Start()
    End Sub
    Private Sub ShowTimeThread3()
        While True
            Me.Invoke(New Action(AddressOf ShowTimeThread4))
            Thread.Sleep(1000)
        End While
    End Sub
    Private Sub ShowTimeThread4()
        barSysTime.Text = "本地时间：" + DateTime.Now.ToString("yyyy年MM月dd dddd HH时mm分ss秒 ")
    End Sub

    ''' <summary>
    ''' 关闭时间线程
    ''' </summary>
    Private Sub AbortTimeThread()
        If showTimeThread IsNot Nothing Then
            showTimeThread.Abort()
        End If
    End Sub
    Private Class Work
        Shared Sub DoWork()
        End Sub
    End Class
#End Region

#Region "按钮"
    '药库管理
    Private Sub Comm1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Dim frm As Form = Nothing
        Select Case sender.text
            Case "药品采购录入"
                frm = New ZTHisDrugStore.Yk_Rk1()
            Case "药品批发录入"
                frm = New ZTHisDrugStore.Yk_Pf1()
            Case "药品调拨药房"
                frm = New YkYf_Crk1("药库调拨药房")
                frm.Name = "Yk_Yf1"
                frm.Text = "药库调拨药房"
            Case "药品科室支领"
                frm = New YkYf_Crk1("药库科室支领")
                frm.Name = "Yk_Ks1"
                frm.Text = "药库科室支领"
            Case "药库退供应商"
                frm = New YkYf_Crk1("药库退供应商")
                frm.Name = "Yk_TkPf1"
                frm.Text = "药库退供应商"
            Case "药品调价录入"
                frm = New Yk_Tj1()
            Case "药品调价生效"
                frm = New Yk_TjQp1()
            Case "药库数据盘点"
                frm = New YkYf_Pd1(Nothing, Nothing, "药库数据盘点")
                frm.Name = "Yk_Pd"
                frm.Text = "药库数据盘点"
            Case "接收药房退库"
                frm = New Yk_Js1()
            Case "药库报损报益"
                frm = New YkYf_Bsby(Nothing, Nothing, "药库报损报溢")
                frm.Name = "Yk_Bsby"
                frm.Text = "药库报损报溢"
        End Select
        If frm IsNot Nothing Then
            BaseFunc.BaseFunc.addTabControl(frm, sender.Text, CType(sender, RibbonButton).ID)
        End If
    End Sub
    '药房管理
    Private Sub Comm2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        If Regex.IsMatch(sender.text, "(药房接收调拨|住院药房发药|门诊药房发药|药房退回药库|门诊退费录入|住院退药录入|药房数据盘点|药房发药日结|药房报损报益|药房科室支领|门诊药房审核|药房批发录入|药房采购入库|药房退供应商)") And ZTHisVar.Var.YfCode.IsNullOrEmpty() Then
            MessageBox.Show("未关联所属药房,请先在操作员管理中关联所属药房", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
            Exit Sub
        End If
        Dim frm As Form = Nothing
        Select Case sender.text
            Case "药房接收调拨"
                frm = New Yf_Js1()
            Case "住院药房发药"
                frm = New ZTHisPharmacy.Zy_Fy()
            Case "门诊药房发药"
                frm = New ZTHisPharmacy.Mz_Fy()
            Case "药房退回药库"
                frm = New YkYf_Crk1("药房退回药库")
                frm.Name = "Yf_Tk1"
                frm.Text = "药房退回药库"
            Case "门诊退费录入"
                frm = New ZTHisPharmacy.Mz_Ty()
            Case "住院退药录入"
                frm = New Zy_Ty
            Case "药房数据盘点"
                frm = New YkYf_Pd1(HisVar.HisVar.YfCode, HisVar.HisVar.YfName, "药房数据盘点")
                frm.Name = "Yf_Pd"
                frm.Text = "药房数据盘点"
            Case "药房发药日结"
                frm = New Yf_Jz(ZTHisVar.Var.YfCode, ZTHisVar.Var.YfName)
            Case "药房报损报益"
                frm = New YkYf_Bsby(HisVar.HisVar.YfCode, HisVar.HisVar.YfName, "药房报损报溢")
            Case "门诊药房审核"
                frm = New ZTHisPharmacy.Yf_Sh()
            Case "药房科室支领"
                frm = New YkYf_Crk1("药房科室支领")
                frm.Name = "Yf_Ks1"
                frm.Text = "药房科室支领"
            Case "药房批发录入"
                frm = New ZTHisPharmacy.Yf_Pf1()
            Case "药房采购入库"
                frm = New ZTHisPharmacy.Yf_Rk1()
            Case "药房退供应商"
                frm = New YkYf_Crk1("药房退供应商")
                frm.Name = "Yf_TkPf1"
                frm.Text = "药房退供应商"
        End Select
        If frm IsNot Nothing Then
            BaseFunc.BaseFunc.addTabControl(frm, sender.Text, CType(sender, RibbonButton).ID)
        End If
    End Sub
    '住院管理
    Private Sub Comm3_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Dim frm As Form = Nothing
        Select Case sender.text
            Case "病例录入"
                'frm = New Zd_Bl11
                frm = New ZTHisInpatient.Zd_Bl11
            Case "缴纳押金"
                frm = New 护士站.Xs_Zy_Yj11
            Case "临时医嘱"
                frm = New Zy_Cf1
            Case "病人出院"
                frm = New Xs_Zy_Cy1
            Case "出院召回"
                frm = New Cy_Zh1
            Case "住院日结"
                frm = New Xs_Zy_Jz
            Case "长期医嘱"
                frm = New Cq_Cf1
            Case "日结作废"
                frm = New Zy_Jz_Zf
                'Case "病历添加"
                '    frm = New Zy_Dzbl1
                'Case "病历封存"
                '    frm = New Bl_Lock
        End Select
        If frm IsNot Nothing Then
            BaseFunc.BaseFunc.addTabControl(frm, sender.Text, CType(sender, RibbonButton).ID)
        End If
    End Sub
    '护士站
    Private Sub Comm4_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Dim frm As Form = Nothing
        Select Case sender.text
            Case "床位分配"
                frm = New 护士站.Zd_Bcfp
            Case "申请出院"
                frm = New 护士站.Xs_Zy_Sqcy(Nothing, Nothing)
            Case "汇总领药"
                frm = New 护士站.Hsz_Hzly
            Case "体温单"
                frm = New 护士站.Temparature("")
            Case "汇总领药查询"
                frm = New 护士站.Cx_Hzly
            Case "病人换床"
                frm = New ZTHisNurse.BedChange("", "", "病人换床")
            Case "住院医嘱"
                frm = New ZTHisNurse.Zy_Yz
        End Select

        If frm IsNot Nothing Then
            BaseFunc.BaseFunc.addTabControl(frm, sender.Text, CType(sender, RibbonButton).ID)
        End If
    End Sub
    '电子病历
    Private Sub Comm5_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Dim frm As Form = Nothing
        Select Case sender.Text
            Case "模板管理"
                frm = New EmrMb1
            Case "质控标准"
                frm = New Zd_ZhiKong1
            Case "质控评分等级"
                frm = New ZkDj1()
            Case "时控设置"
                frm = New Zd_ShiKongMain
            Case "基础元素"
                frm = New EmrBasic1
            Case "添加病历"
                frm = New 护士站.EmrStation
            Case "时限查询"
                frm = New ShiXianCx()
            Case "病历归档"
                frm = New BlGuiDang
            Case "质控审查"
                frm = New ZkSc
        End Select
        If frm IsNot Nothing Then
            BaseFunc.BaseFunc.addTabControl(frm, sender.Text, CType(sender, RibbonButton).ID)
        End If
    End Sub
    '门诊管理
    Private Sub Comm06_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Dim frm As Form = Nothing
        Select Case sender.text
            Case "门诊挂号"
                frm = New ZTHisOutpatient.Mz_Gh(Nothing)
            Case "门诊录入"
                frm = New Xs_Mz1("门诊录入")
                frm.Name = "Xs_Mz1"
                frm.Text = "门诊录入"
            Case "门诊日结"
                frm = New ZTHisOutpatient.Mz_Jz()
            Case "医生开处方"
                If HisVar.HisVar.JsrYsCode = "" Then
                    MsgBox("没有指定处方医生或者该医生已被禁用,请与系统管理员联系!", MsgBoxStyle.Information, "提示")
                    Exit Sub
                End If
                frm = New Xs_Mz1("医生开处方")
                frm.Name = "Ys_Cf2"
                frm.Text = "医生开处方"
            Case "门诊收费"
                frm = New ZTHisOutpatient.Mz_Sf(Nothing)
            Case "退费接收"
                frm = New ZTHisOutpatient.MzTf_Js()
            Case "诊疗退费"
                frm = New ZTHisOutpatient.Mz_TfLr_Xm()
            Case "体检录入"
                frm = New ZTHisOutpatient.Tj_Lr1(Nothing)
            Case "门诊模版管理"
                frm = New ZTHisOutpatient.MzTemplate1()
            Case "门诊输液"
                frm = New ZTHisOutpatient.MzInfusion1()
            Case "门诊病历词条"
                If String.IsNullOrWhiteSpace(ZTHisVar.Var.YsCode) Or String.IsNullOrWhiteSpace(ZTHisVar.Var.KsCode) Then
                    MsgBox($"请先关联医生才能编辑{sender.text}!", MsgBoxStyle.Information, "提示")
                    Exit Sub
                End If
                frm = New ZTHisOutpatient.Zd_MzBlTemplate1()
            Case "门诊病历模版"
                If String.IsNullOrWhiteSpace(ZTHisVar.Var.YsCode) Or String.IsNullOrWhiteSpace(ZTHisVar.Var.KsCode) Then
                    MsgBox($"请先关联医生才能编辑{sender.text}!", MsgBoxStyle.Information, "提示")
                    Exit Sub
                End If
                frm = New ZTHisOutpatient.Zd_MzBlTemplateComb1()
        End Select
        If frm IsNot Nothing Then
            BaseFunc.BaseFunc.addTabControl(frm, sender.Text, CType(sender, RibbonButton).ID)
        End If
    End Sub
    '检验管理
    'Private Sub CommLis_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
    '    Dim frm As Form = Nothing
    '    Select Case sender.text
    '        Case "检验项目管理"
    '            frm = New LIS_TestXm1
    '        Case "检验元字典"
    '            frm = New LISMetaDic11
    '        Case "检验项目关联"
    '            frm = New JySfDy
    '        Case "检验工作站"
    '            frm = New Test_Jy1
    '        Case "采集工作站"
    '            frm = New Test_Cj
    '        Case "检验申请"
    '            frm = New TestSq1
    '    End Select
    '    If frm IsNot Nothing Then
    '        BaseFunc.BaseFunc.addTabControl(frm, sender.Text)
    '    End If
    'End Sub
    '科室工作站
    Private Sub Comm02_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Dim frm As Form = Nothing
        Select Case Mid(sender.text, InStr(sender.text, ".") + 1, sender.text.Length)
            Case "项目检查"
                frm = New ZTHisLis.Jc_Xm()
            Case "检验仪器管理"
                frm = New ZTHisLis.JYYQGLDic11()
        End Select
        If frm IsNot Nothing Then
            BaseFunc.BaseFunc.addTabControl(frm, sender.Text, CType(sender, RibbonButton).ID)
        End If
    End Sub
    '物资管理
    Private Sub Comm08_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Dim frm As Form = Nothing
        Select Case sender.text
            Case "物资字典"
                frm = New ZtHis.Materials.MaterialsDict11
            Case "物资盘点"
                frm = New ZtHis.Materials.Marterials_Check(Nothing)
            Case "物资移库"
                frm = New ZtHis.Materials.Marterials_Move(Nothing)
            Case "物资退库"
                frm = New ZtHis.Materials.Marterials_Return(Nothing)
            Case "物资采购入库"
                frm = New ZtHis.Materials.Materials_Buy_In1(Nothing)
            Case "物资其他出库"
                frm = New ZtHis.Materials.Materials_Other_Out(Nothing)
            Case "物资供应商"
                frm = New ZTHisMaterials.MaterialsSupDict11
            Case "物资出入库类别"
                frm = New ZTHisMaterials.MaterialsInOutClassDict11
            Case "物资仓库"
                frm = New ZTHisMaterials.MaterialsWarehouseDict11
            Case "物资库存查询"
                frm = New ZTHisMaterials.MaterialsStockSearch
            Case "物资采购查询"
                frm = New ZTHisMaterials.MaterialsBuyInSearch
            Case "物资领用查询"
                frm = New ZtHis.Materials.MaterialsUseOutSearch
            Case "物资盘点查询"
                frm = New ZtHis.Materials.MaterialsCheckSearch
            Case "物资退库查询"
                frm = New ZtHis.Materials.MaterialsReturnSearch
            Case "物资其他入库查询"
                frm = New ZTHisMaterials.MaterialsOtherInSearch
            Case "物资其他出库查询"
                frm = New ZtHis.Materials.MaterialsOtherOutSearch
            Case "物资移库查询"
                frm = New ZtHis.Materials.MaterialsMoveSearch
            Case "物资其他入库"
                frm = New ZtHis.Materials.MaterialsOtherIn(Nothing)
            Case "物资领用"
                frm = New ZtHis.Materials.MaterialsUseOut(Nothing)
        End Select
        If frm IsNot Nothing Then
            BaseFunc.BaseFunc.addTabControl(frm, sender.Text, CType(sender, RibbonButton).ID)
        End If
    End Sub
    '数据查询
    Private Sub Comm09_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        If Regex.IsMatch(sender.text, "(药房信息查询|药房警戒线查询|药房出入库台账|住院门诊发药综合查询|住院门诊诊疗综合查询|药房盘点查询|药房退库查询|药房科室支领查询|药房采购入库查询|药房批发查询|药房退供应商查询|药房审核查询)") And ZTHisVar.Var.YfCode.IsNullOrEmpty() Then
            MessageBox.Show("未关联所属药房,请先在操作员管理中关联所属药房", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
            Exit Sub
        End If
        Dim frm As Form = Nothing
        Select Case sender.text
            Case "在院患者费用"
                frm = New Cx_Zyhz1
            Case "患者用药清单"
                frm = New Cx_Hzyy1
            Case "住院日结查询"
                frm = New Zy_Rj_Cx
            Case "未请求发药查询"
                frm = New ZTHisInpatient.Cx_WZyHz1()
            Case "住院床位统计"
                frm = New Cw_Tj
            Case "缴纳押金查询"
                frm = New ZTHisInpatient.Cx_Yj()
            Case "在院患者诊疗查询"
                frm = New ZTHisInpatient.ZyXmCx()
            Case "病案首页导出"
                frm = New ZTHisInpatient.BASYExport()
#Region "门诊查询"
                '门诊查询
            Case "门诊日结查询"
                frm = New ZTHisOutpatient.Mz_Jz_Cx()
            Case "门诊患者用药查询"
                frm = New ZTHisOutpatient.Mz_YyCx()
            Case "未交费门诊患者查询"
                frm = New ZTHisOutpatient.WjzHz_Cx()
            Case "门诊退费查询"
                frm = New ZTHisOutpatient.Mz_TyCx()
            Case "挂号信息查询"
                frm = New ZTHisOutpatient.Gh_Cx()
            Case "门诊收费查询"
                frm = New ZTHisOutpatient.MzSfCx()
#End Region
                '药库查询
            Case "药库信息查询"
                frm = New ZTHisDrugStore.Yk_Cx()
            Case "药库入库查询"
                frm = New ZTHisDrugStore.YkRkCx()
            Case "药库批发查询"
                frm = New ZTHisDrugStore.YkPfCx()
            Case "科室支领查询"
                frm = New ZTHisDrugStore.YkKsCx()
            Case "调拨药房查询"
                frm = New ZTHisDrugStore.YkYfCx()
            Case "药库退库查询"
                frm = New ZTHisDrugStore.YkTkCx()
            Case "药库调价查询"
                frm = New Yk_Tj_Cx1
                frm.Name = "Yk_Tj_Cx1"
                frm.Text = "药库调价查询"
            Case "药库警戒线查询"
                frm = New Cx_YkYf_Alar(Nothing, Nothing, "药库警戒线查询")
                frm.Name = "Alar_Cx1"
                frm.Text = "药库警戒线查询"
            Case "药库出入库台账"
                frm = New Yp_Cr_Tz
                frm.Name = "Yp_Cr_Tz"
                frm.Text = "药库出入库台账查询"
            Case "药库盘点查询"
                frm = New ZTHisDrugStore.YkPdCx()
            Case "各药房信息查询"
                frm = New Yk_Cx_AllYf
            Case "药房信息查询"
                frm = New ZTHisPharmacy.Yf_Cx(True, ZTHisVar.Var.YfCode)
            Case "药房警戒线查询"
                frm = New Cx_YkYf_Alar(HisVar.HisVar.YfCode, HisVar.HisVar.YfName, "药房警戒线查询")
                frm.Name = "Alar_YfCx"
                frm.Text = HisVar.HisVar.YfName & "警戒线查询"
            Case "药房出入库台账"
                frm = New ZTHisPharmacy.YfLs(True, ZTHisVar.Var.YfCode)
                frm.Text = ZTHisVar.Var.YfName & "出入库台账"
            Case "住院门诊发药综合查询"
                frm = New ZTHisPharmacy.Cx_ZyMz_Fy(True, ZTHisVar.Var.YfCode)
            Case "住院门诊诊疗综合查询"
                frm = New ZTHisPharmacy.Cx_ZyMz_Xm()
            Case "药房盘点查询"
                frm = New ZTHisPharmacy.YfPdCx(True, ZTHisVar.Var.YfCode)
            Case "药房退库查询"
                frm = New ZTHisPharmacy.YfTkCx(True, ZTHisVar.Var.YfCode)
            Case "药房科室支领查询"
                frm = New ZTHisPharmacy.YfKsCx(True, ZTHisVar.Var.YfCode)
            Case "药房采购入库查询"
                frm = New ZTHisPharmacy.YfRkCx(True, ZTHisVar.Var.YfCode)
            Case "药房批发查询"
                frm = New ZTHisPharmacy.YfPfCx(True, ZTHisVar.Var.YfCode)
            Case "药房退供应商查询"
                frm = New ZTHisPharmacy.YfTgysCx(True, ZTHisVar.Var.YfCode)
            Case "药房审核查询"
                frm = New ZTHisPharmacy.YfSh_Cx()
                '财务查询
            Case "门诊住院日结综合查询"
                frm = New Cw_Cx_MzZy_Rj1
            Case "药品销售统计"
                frm = New ZTHisFinance.Cw_LrTj()
            Case "医院工作量统计"
                frm = New Work_Tj
            Case "处方科室收入统计"
                frm = New ZTHisFinance.CfKsSrTj()
            Case "医技科室收入统计"
                frm = New JcKs_Money_Tj
            Case "项目售价查询"
                frm = New ZTHisFinance.XmSjCx()
        End Select
        If frm IsNot Nothing Then
            BaseFunc.BaseFunc.addTabControl(frm, sender.Text, CType(sender, RibbonButton).ID)
        End If
    End Sub
    '基础数据
    Private Sub Comm10_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Dim frm As Form = Nothing
        Select Case sender.text
            Case "剂型字典"
                frm = New ZTHisBaseDict.Zd_Ml_Ypjx1()
                frm.Text = sender.text
            Case "功效字典"
                frm = New ZTHisBaseDict.Zd_Ml_YpGx1()
                frm.Text = sender.text
            Case "药材字典"
                frm = New ZTHisBaseDict.Zd_Ml_Yp1()
                frm.Text = sender.text
            Case "身份字典"
                frm = New ZTHisBaseDict.Zd_Identity1()
                frm.Text = sender.text
            Case "诊疗字典"
                frm = New ZTHisBaseDict.Zd_Ml_Xm1()
                frm.Text = sender.text
            Case "疾病字典"
                frm = New ZTHisBaseDict.Zd_Ml_Jb1()
                frm.Text = sender.text
            Case "给药方式"
                frm = New ZTHisBaseDict.Zd_Administration1()
                frm.Text = sender.text
            Case "医嘱执行频率"
                frm = New ZTHisBaseDict.Zd_Perform_Freq1()
                frm.Text = sender.text
            Case "手术字典"
                frm = New ZTHisBaseDict.Dict_Operation1()
                frm.Text = sender.text
            Case "药品供应商"
                frm = New ZTHisBaseDict.Zd_Kh_Rk1()
                frm.Text = sender.text
            Case "销售客户"
                frm = New ZTHisBaseDict.Zd_Kh_Xs1()
                frm.Text = sender.text
            Case "科室字典"
                frm = New ZTHisBaseDict.Zd_YyKs1()
                frm.Text = sender.text
            Case "医护字典"
                frm = New ZTHisBaseDict.Zd_YyYs1()
                frm.Text = sender.text
            Case "领导字典"
                frm = New ZTHisBaseDict.Zd_YyLd1()
                frm.Text = sender.text
            Case "床位字典"
                frm = New ZTHisBaseDict.Zd_YyBc1()
                frm.Text = sender.text
            Case "病例类别"
                frm = New ZTHisBaseDict.zd_bxlb1()
                frm.Text = sender.text
            Case "药房字典"
                frm = New ZTHisBaseDict.Zd_Yyyf1()
                frm.Text = sender.text
            Case "用法用量"
                frm = New ZTHisBaseDict.Zd_Yfyl1()
                frm.Text = sender.text
            Case "病区字典"
                'frm = New Zd_Dict1("病区字典")
                'frm.Name = "Zd_Bq1"
                'frm.Text = "病区字典"
                frm = New ZTHisBaseDict.Zd_Bq1()
                frm.Text = sender.text
            Case "诊疗模板"
                frm = New ZTHisBaseDict.Zd_Templet1()
                frm.Text = sender.text
            Case "就诊磁卡"
                frm = New ZTHisBaseDict.Zd_Ry1()
                frm.Text = sender.text
            Case "挂号媒体"
                frm = New ZTHisBaseDict.Zd_GhMt1()
                frm.Text = sender.text
            Case "住院票据分类"
                'frm = New Zd_Dict1("住院票据分类")
                'frm.Name = "Zd_JkFl1"
                'frm.Text = "住院票据分类"
                frm = New ZTHisBaseDict.Zd_ZyPj1()
                frm.Text = sender.text
            Case "发票边距调整"
                frm = New Fp_Tz
            Case "门诊发票合并"
                frm = New Zd_MzFpHb1
            Case "门诊票据分类"
                If ZTHisPara.PublicConfig.OutpatientInvoice = ZTHisEnum.OutpatientInvoiceEnum.JiLinInvoice Then
                    frm = New Zd_MzFp1
                Else
                    frm = New Zd_Dict1("门诊票据分类")
                    frm.Name = "Zd_MzFp1"
                    frm.Text = "门诊票据分类"
                End If
            Case "配伍禁忌"
                frm = New ZTHisBaseDict.Zd_Incompatibility1()
                frm.Text = sender.text
            Case "管理职务"
                frm = New ZTHisBaseDict.Zd_Manage1()
        End Select
        If frm IsNot Nothing Then
            BaseFunc.BaseFunc.addTabControl(frm, sender.Text, CType(sender, RibbonButton).ID)
        End If
    End Sub


    ''' <summary>
    ''' 内蒙古医保
    ''' </summary>
    ''' <param name="sender"></param>
    ''' <param name="e"></param>
    '    Private Sub Comm11_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
    '        If CType(sender, RibbonButton).ID.Substring(0, 2) <> 17 Then
    '            Exit Sub
    '        End If
    '        ZTHisInsurance_NMG.YB_API.AutoImportYBFiles()
    '        Dim frm As Form = Nothing
    '        Select Case sender.text
    '            Case "内蒙入参参数"
    '                frm = New ZTHisInsurance_NMG.NMG_YB_InputData
    '            Case "内蒙门诊报销"
    '                frm = New ZTHisInsurance_NMG.NMG_YB_Mz
    '            Case "内蒙住院报销"
    '                frm = New ZTHisInsurance_NMG.NMG_YB_Zy
    '            Case "内蒙医保目录对照"
    '                frm = New ZTHisInsurance_NMG.NMGYPXM_DY1
    '            Case "内蒙古医保对账"
    '                frm = New ZTHisInsurance_NMG.NMG_YB_Count
    '            Case "医保对账单"
    '                frm = New ZTHisInsurance_NMG.NMG_YB_Ybdzd("医保对账单")
    '            Case "居民医保对账单"
    '                frm = New ZTHisInsurance_NMG.NMG_YB_Ybdzd("居民医保对账单")
    '            Case "医保本地数据对账明细"
    '                frm = New ZTHisInsurance_NMG.NMG_YB_Ybdzd("医保本地数据对账明细")
    '            Case "医保审批结果查询"
    '                frm = New ZTHisInsurance_NMG.NMG_YB_Spxx()
    '        End Select
    '        If frm IsNot Nothing Then
    '            BaseFunc.BaseFunc.addTabControl(frm, sender.Text, CType(sender, RibbonButton).ID)
    '        End If
    '    End Sub
    '系统设置
    Private Sub Comm99_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Dim frm As Form = Nothing
        Select Case sender.text
            Case "权限组设置"
                frm = New ZTHisSysManage.PermissionGroup1
            Case "操作员管理"
                frm = New ZTHisSysManage.Zd_YyJsr1
            Case "报表设计"
                frm = New ZTHisSysManage.RptDesign()
            Case "软件设置"
                frm = New ZTHisSysManage.SysConfig()
            Case "数据库维护"
                frm = New ZTHisSysManage.SysMaintenance()
        End Select
        If frm IsNot Nothing Then
            BaseFunc.BaseFunc.addTabControl(frm, sender.Text, CType(sender, RibbonButton).ID)
        End If
    End Sub
    ''' <summary>
    ''' 国家医保接口按钮
    ''' </summary>
    ''' <param name="sender"></param>
    ''' <param name="e"></param>
    Private Sub CommNewYb_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        If CType(sender, RibbonButton).ID.Substring(0, 2) <> 50 Then
            Exit Sub
        End If
        Select Case sender.text
            Case "门诊报销查询"
            Case "住院报销查询"
            Case "报销明细查询"
            Case "医保参数设置"
            Case "电子处方参数"
            Case Else
                If Not ZTHisInsuranceAPI.YB_Config.YB_Init(ZTHisVar.Var.JsrCode, ZTHisVar.Var.JsrName, sender.text) Then
                    Return
                End If
        End Select
        Dim frm As Form = Nothing
        Select Case sender.text
            Case "医保门诊报销"
                frm = New ZTHisInsurance.YB_Mz1()
            Case "医保住院报销"
                frm = New ZTHisInsurance.YB_Zy1("")
            Case "门诊结算上传"
                frm = New ZTHisInsurance.YBJsListUpload("门诊")
            Case "住院结算上传"
                frm = New ZTHisInsurance.YBJsListUpload("住院")
            Case "医保结算对账"
                frm = New ZTHisInsurance.YB_Dz1()
            Case "医保清算申请"
                frm = New ZTHisInsurance.YbQs()
            Case "门诊自费上传"
                frm = New ZTHisInsurance.MzZfHzUpload()
            Case "住院自费上传"
                frm = New ZTHisInsurance.ZyZfHzUpload()
            Case "转院备案"
                frm = New ZTHisInsurance.YBZyBa(Nothing)
            Case "慢特病备案"
                frm = New ZTHisInsurance.YBRyMbBa(Nothing)
            Case "定点备案"
                frm = New ZTHisInsurance.YBRyDdBa(Nothing)
            Case "意外伤害备案"
                frm = New ZTHisInsurance.YBRyYwShBa(Nothing)
            Case "医保就诊查询"
                frm = New ZTHisInsurance.YbHzJzcx1()
            Case "慢特病用药查询"
                frm = New ZTHisInsurance.MtbyyCx()
            Case "人员累计信息查询"
                frm = New ZTHisInsurance.YBRyLjXxCx()
            Case "慢特病备案查询"
                frm = New ZTHisInsurance.MtbbaCx()
            Case "人员定点查询"
                frm = New ZTHisInsurance.RyDdCx()
            Case "在院信息查询"
                frm = New ZTHisInsurance.YbZyCx()
            Case "转院信息查询"
                frm = New ZTHisInsurance.YbZhuanYuanCx()
            Case "门诊报销查询"
                frm = New ZTHisInsurance.MzBx_Query()
            Case "住院报销查询"
                frm = New ZTHisInsurance.ZyBx_Query()
            Case "报销明细查询"
                frm = New ZTHisInsurance.BxMx_Query()
            Case "医保目录对照"
                frm = New ZTHisInsurance.Mldz1()
            Case "医保目录下载"
                frm = New ZTHisInsurance.MlDownload()
            Case "医保科室管理"
                frm = New ZTHisInsurance.YBKsYs()
            Case "进销存管理"
                frm = New ZTHisInsurance.Sp_Jxc()
            Case "医保参数设置"
                frm = New ZTHisInsurance.Country_YB_CONFIG()
            Case "冲正交易"
                frm = New ZTHisInsurance.YBReversal("", "", "", "")
            Case "电子处方上传"
                frm = New ERX.ERxUpload()
            Case "电子处方参数"
                frm = New ERX.Erx_Config()
        End Select
        If frm IsNot Nothing Then
            BaseFunc.BaseFunc.addTabControl(frm, sender.Text, CType(sender, RibbonButton).ID)
        End If
    End Sub

    '三方接口
    Private Sub CommThird_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        If CType(sender, RibbonButton).ID.Substring(0, 2) <> 51 Then
            Exit Sub
        End If
        Dim frm As Form = Nothing
        Select Case sender.text
'            Case "单病种参数"
'                frm = New SingleDisease.Single_Disease_Config()
'            Case "单病种上报"
'                frm = New SingleDisease.SingleDiseaseFrm()
            Case "电子票据参数"
                frm = New E_Invoice.E_Invoice_Config()
            Case "电子票据归类"
                frm = New E_Invoice.EInvoiceXmFl1()
            Case "门诊电子票据查询"
                frm = New E_Invoice.MzEInvoice()
            Case "住院电子票据查询"
                frm = New E_Invoice.ZyEInvoice()
        End Select
        If frm IsNot Nothing Then
            BaseFunc.BaseFunc.addTabControl(frm, sender.Text, CType(sender, RibbonButton).ID)
        End If
    End Sub
#End Region

#Region "按钮动作"
    Private Sub BtnClick(ByVal sender As System.Object, ByVal e As System.EventArgs)
        If sender.GetType().Name = "RibbonListItem" Then
            sender = DirectCast(sender, RibbonListItem).Items(0)
        End If
        Comm1_Click(sender, e)
        Comm2_Click(sender, e)
        Comm3_Click(sender, e)
        Comm4_Click(sender, e)
        Comm5_Click(sender, e)
        Comm02_Click(sender, e)
        Comm06_Click(sender, e)
        Comm08_Click(sender, e)
        Comm09_Click(sender, e)
        Comm10_Click(sender, e)
        '        Comm11_Click(sender, e)
        Comm99_Click(sender, e)
        CommNewYb_Click(sender, e)
        CommThird_Click(sender, e)
    End Sub

    Private Sub C1Ribbon1_MinimizedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Ribbon1.MinimizedChanged
        BaseFunc.BaseFunc.setConfig(CType(sender, C1Ribbon).Minimized, "最小化功能区", "主窗体模块")
    End Sub

#Region "左侧按钮"
    Private Sub fixBtnHelp_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles fixBtnHelp.Click
        Dim helpfile As String = "唐山His2010帮助文档.chm"
        Help.ShowHelp(Me, helpfile)
    End Sub

    Private Sub fixBtnUpdate_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles fixBtnUpdate.Click

    End Sub

    Private Sub fixBtnExit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles fixBtnExit.Click
        Application.Exit()
    End Sub

    Private Sub fixBtnUpdateMemo_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles fixBtnUpdateMemo.Click
        Dim frm As New ZTHisSysManage.UpdateContent
        frm.ShowDialog()
    End Sub

    Private Sub fixBtnPerson_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles fixBtnPerson.Click
        Dim frm As New Person_Config
        BaseFunc.BaseFunc.addTabControl(frm, sender.Text)
    End Sub

#End Region

#End Region

#Region "滚动条"

    Private Sub Gd_News()
        If HisPara.PublicConfig.ZyHsz = "是" Then
            'If Menu3.Visible = True Then
            ScrollPanel1.Text = ""
            ScrollPanel1.TimerEnabled = True
            'Else
            '    Panel1.Visible = False
            'End If
        Else
            ScrollPanel1.Visible = False
        End If
    End Sub

    Private Sub Timer2_Tick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Timer2.Tick
        Dim m_str As String = ""
        Dim m_Reader As SqlDataReader
        m_Reader = HisVar.HisVar.Sqldal.ExecuteReader("Select Ry_Name From Bl Where Cy_Qr='是' And Isnull(Ry_CyJsr,'')=''")
        If m_Reader.HasRows = True Then
            While m_Reader.Read
                m_str = m_str & "," & m_Reader.Item("Ry_Name")
            End While
            ScrollPanel1.Text = Mid(m_str, 2) & "已确认申请出院!"
        Else
            ScrollPanel1.Text = ""
        End If
        m_Reader.Close()
    End Sub

#End Region

#Region "选项卡动作"

    Private Sub C1DockingTab1_TabPageClosed(ByVal sender As System.Object, ByVal e As C1.Win.C1Command.TabPageEventArgs) Handles C1DockingTab1.TabPageClosed
        '关闭tabpage的同时关闭窗体
        Dim frm As Form = Nothing
        For Each frm In Application.OpenForms
            For Each _ctrl In e.TabPage.Controls
                If _ctrl Is frm Then
                    GoTo A
                End If
            Next
        Next
        Exit Sub
A:      If frm IsNot Nothing Then
            frm.Close()
        End If
    End Sub

    Private Sub C1DockingTab1_MouseDoubleClick(ByVal sender As System.Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles C1DockingTab1.MouseDoubleClick
        If e.Button = MouseButtons.Left Then
            C1DockingTab1.Close(C1DockingTab1.SelectedTab)
        End If
    End Sub

    Private Sub C1DockingTab1_MouseDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles C1DockingTab1.MouseDown
        If e.Button = MouseButtons.Right Then
            C1DockingTab1.ContextMenuStrip = ContextMenuStrip1
        End If
    End Sub

    Private Sub C1DockingTab1_MouseLeave(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1DockingTab1.MouseLeave
        C1DockingTab1.ContextMenuStrip = Nothing
    End Sub

    Private Sub ToolStripMenuItem1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ToolStripMenuItem1.Click
        C1DockingTab1.Close(C1DockingTab1.SelectedTab)
    End Sub

    Private Sub ToolStripMenuItem2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ToolStripMenuItem2.Click
        Dim taparray As New ArrayList
        For Each tap As C1DockingTabPage In C1DockingTab1.TabPages
            If tap IsNot C1DockingTab1.SelectedTab Then
                taparray.Add(tap)
            End If
        Next
        For index = 0 To taparray.Count - 1
            C1DockingTab1.Close(taparray(index))
        Next
    End Sub

    Private Sub ToolStripMenuItem3_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ToolStripMenuItem3.Click
        Dim taparray As New ArrayList
        For pageindex = 0 To C1DockingTab1.TabPages.Count - 1
            If pageindex < C1DockingTab1.SelectedIndex Then
                taparray.Add(C1DockingTab1.TabPages(pageindex))
            End If
        Next
        For index = 0 To taparray.Count - 1
            C1DockingTab1.Close(taparray(index))
        Next
    End Sub

    Private Sub ToolStripMenuItem4_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ToolStripMenuItem4.Click
        Dim taparray As New ArrayList
        For pageindex = 0 To C1DockingTab1.TabPages.Count - 1
            If pageindex > C1DockingTab1.SelectedIndex Then
                taparray.Add(C1DockingTab1.TabPages(pageindex))
            End If
        Next
        For index = 0 To taparray.Count - 1
            C1DockingTab1.Close(taparray(index))
        Next
    End Sub

    Private Sub ToolStripMenuItem5_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ToolStripMenuItem5.Click
        Dim taparray As New ArrayList
        For Each tap As C1DockingTabPage In C1DockingTab1.TabPages
            taparray.Add(tap)
        Next
        For index = 0 To taparray.Count - 1
            C1DockingTab1.Close(taparray(index))
        Next
    End Sub

    Private Sub C1DockingTab1_TabPageClosing(ByVal sender As System.Object, ByVal e As C1.Win.C1Command.TabPageCancelEventArgs) Handles C1DockingTab1.TabPageClosing
        If e.TabPage.Name = "NurseStation" Then
            e.Cancel = True
        End If

    End Sub

#End Region

#Region "提醒"

    Private Sub get_mesaage()

        If HisPara.PublicConfig.MsgYkYxq = True Then
            If HisVar.HisVar.Sqldal.GetSingle("Select Count(Xx_Code) From V_Ypkc where Yk_Sl>0 And datediff(D,getdate(),Yp_Yxq)<=270  And Yy_Code='" & HisVar.HisVar.WsyCode & "'") > 0 Then
                Dim frm As New ZTHisDrugStore.YkYxqAlert()
                BaseFunc.BaseFunc.addTabControl(frm, frm.Text)
            End If
        End If

        If HisPara.PublicConfig.MsgYfYxq = True And HisVar.HisVar.YfCode.Trim & "" <> "" Then
            If HisVar.HisVar.Sqldal.GetSingle("Select Count(Xx_Code) From V_Ypkc where Yf_Sl" & Mid(HisVar.HisVar.YfCode, 6, 1) & ">0 And datediff(D,getdate(),Yp_Yxq)<=270   ") > 0 Then
                Dim frm As New ZTHisPharmacy.YfYxqAlert(ZTHisVar.Var.YfCode)
                BaseFunc.BaseFunc.addTabControl(frm, frm.Text)
            End If
        End If

        If (HisPara.PublicConfig.MsgYfZyfy = True And HisVar.HisVar.YfCode.Trim & "" <> "" And HisVar.HisVar.Sqldal.GetSingle("Select Count(1) From Zd_QxModule,Zd_Qx2 where Zd_Qx2.Module_Code = Zd_QxModule.Module_Code and Zd_Qx2.glz_code = '" & HisVar.HisVar.GlzCode & "' and Xl_Name = '住院药房发药'  ") > 0) Or
           (HisPara.PublicConfig.MsgYfMzfy = True And HisVar.HisVar.YfCode.Trim & "" <> "" And HisVar.HisVar.Sqldal.GetSingle("Select Count(1) From Zd_QxModule,Zd_Qx2 where Zd_Qx2.Module_Code = Zd_QxModule.Module_Code and Zd_Qx2.glz_code = '" & HisVar.HisVar.GlzCode & "' and Xl_Name = '药房门诊发药'  ") > 0) Or
           (HisPara.PublicConfig.MsgYfJsdb = True And HisVar.HisVar.YfCode.Trim & "" <> "" And HisVar.HisVar.Sqldal.GetSingle("Select Count(1) From Zd_QxModule,Zd_Qx2 where Zd_Qx2.Module_Code = Zd_QxModule.Module_Code and Zd_Qx2.glz_code = '" & HisVar.HisVar.GlzCode & "' and Xl_Name = '药房接收调拨'  ") > 0) Then
            m_bFlag = True
            m_Icon1 = New Icon("img\09553.ico ")
            '导入图标文件 
            m_Icon2 = New Icon("img\09574.ico")
            '默认图标
            p_timer3Start = False
            Timer4.Start()
        End If

        ' 门诊处方收费提醒
        If HisPara.PublicConfig.MsgMzCfSf = True And HisVar.HisVar.Sqldal.GetSingle("Select Count(1) From Zd_QxModule,Zd_Qx2 where Zd_Qx2.Module_Code = Zd_QxModule.Module_Code and Zd_Qx2.glz_code = '" & HisVar.HisVar.GlzCode & "' and Xl_Name = '门诊收费'  ") > 0 Then
            m_bFlag = True
            m_Icon1 = New Icon("img\09553.ico ")
            '导入图标文件 
            m_Icon2 = New Icon("img\09574.ico")
            '默认图标
            p_timer3Start = False
            Timer4.Start()
        End If

        ' 护士汇总领药提醒
        If HisPara.PublicConfig.MsgHsHzLy = True And HisVar.HisVar.Sqldal.GetSingle("Select Count(1) From Zd_QxModule,Zd_Qx2 where Zd_Qx2.Module_Code = Zd_QxModule.Module_Code and Zd_Qx2.glz_code = '" & HisVar.HisVar.GlzCode & "' and Xl_Name = '汇总领药'  ") > 0 Then
            m_bFlag = True
            m_Icon1 = New Icon("img\09553.ico ")
            '导入图标文件 
            m_Icon2 = New Icon("img\09574.ico")
            '默认图标
            p_timer3Start = False
            Timer4.Start()
        End If

        ' 项目检查提醒
        If HisPara.PublicConfig.MsgXmJc = True And HisVar.HisVar.Sqldal.GetSingle("Select Count(1) From Zd_QxModule,Zd_Qx2 where Zd_Qx2.Module_Code = Zd_QxModule.Module_Code and Zd_Qx2.glz_code = '" & HisVar.HisVar.GlzCode & "' and Xl_Name = '项目检查'  ") > 0 Then
            m_bFlag = True
            m_Icon1 = New Icon("img\09553.ico ")
            '导入图标文件 
            m_Icon2 = New Icon("img\09574.ico")
            '默认图标
            p_timer3Start = False
            Timer4.Start()
        End If


    End Sub

    Private Str_Select As String

    Private Sub Timer4_Tick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Timer4.Tick
        'p_Flag = "无"
        If (HisPara.PublicConfig.MsgYfZyfy = True And HisVar.HisVar.YfCode.Trim & "" <> "" And HisVar.HisVar.Sqldal.GetSingle("Select Count(1) From Zd_QxModule,Zd_Qx2 where Zd_Qx2.Module_Code = Zd_QxModule.Module_Code and Zd_Qx2.glz_code = '" & HisVar.HisVar.GlzCode & "' and Xl_Name = '住院药房发药'  ") > 0) Then
            Dim Str As String = ""
            If HisPara.PublicConfig.ZyHsz = "是" Then
                Str = "select Count(Cf_Code) From Bl_Cf Where Yy_Code='" & HisVar.HisVar.WsyCode & "' And Isnull(Cf_YpMoney,0)<>0 And Cf_Qr='否' And Cf_Print='是'  and Ly_Wc='是' and Yf_Code='" & HisVar.HisVar.YfCode & "' And Cf_Date='" & Format(Now, "yyyy-MM-dd") & "'"

            Else
                Str = "select Count(Cf_Code) From Bl_Cf Where Yy_Code='" & HisVar.HisVar.WsyCode & "' And Isnull(Cf_YpMoney,0)<>0 And Cf_Qr='否' And Cf_Print='是'  and Yf_Code='" & HisVar.HisVar.YfCode & "' And Cf_Date='" & Format(Now, "yyyy-MM-dd") & "'"
            End If
            If HisVar.HisVar.Sqldal.GetSingle(Str) > 0 Then
                If p_timer3Start = False And Not Common.BaseForm.PublicFunc.IsChildFormOpen(Of ZTHisPharmacy.Zy_Fy)() Then
                    p_Flag = "住院发药"
                    Me.NotifyIcon1.Icon = m_Icon1
                    '判断是否有临时数据
                    Timer3.Interval = 1
                    Timer3.Enabled = True
                    Timer3.Start()

                    '声音提示
                    Me.NotifyIcon1.ShowBalloonTip(200, "您有新的住院发药需要处理！", vbCr & vbLf & "    您可以双击该图标来查看住院处方发药内容！" & vbCr & vbLf & vbCr & vbLf & "    单击此处可以关闭该提醒", System.Windows.Forms.ToolTipIcon.Info)
                    Me.music()
                    Exit Sub
                End If
            End If
        End If

        If (HisPara.PublicConfig.MsgYfMzfy = True And HisVar.HisVar.YfCode.Trim & "" <> "" And HisVar.HisVar.Sqldal.GetSingle("Select Count(1) From Zd_QxModule,Zd_Qx2 where Zd_Qx2.Module_Code = Zd_QxModule.Module_Code and Zd_Qx2.glz_code = '" & HisVar.HisVar.GlzCode & "' and Xl_Name = '门诊药房发药'  ") > 0) Then
            If HisVar.HisVar.Sqldal.GetSingle("select Count(Mz_Code) From Mz Where Yy_Code='" & HisVar.HisVar.WsyCode & "' And Isnull(Mz_YpMoney,0)<>0 And Mz_FyQr='0' And Mz_Print='1' and Yf_Code='" & HisVar.HisVar.YfCode & "' And Mz_Date='" & Format(Now, "yyyy-MM-dd") & "'") > 0 Then
                If p_timer3Start = False And Not Common.BaseForm.PublicFunc.IsChildFormOpen(Of ZTHisPharmacy.Mz_Fy)() Then
                    p_Flag = "门诊药房发药"
                    Me.NotifyIcon1.Icon = m_Icon1
                    '判断是否有临时数据
                    Timer3.Interval = 1
                    Timer3.Enabled = True
                    Timer3.Start()

                    '声音提示
                    Me.NotifyIcon1.ShowBalloonTip(200, "您有新的门诊发药需要处理！", vbCr & vbLf & "    您可以双击该图标来查看门诊处方发药内容！" & vbCr & vbLf & vbCr & vbLf & "    单击此处可以关闭该提醒", System.Windows.Forms.ToolTipIcon.Info)
                    Me.music()
                    Exit Sub
                End If
            End If
        End If

        If (HisPara.PublicConfig.MsgYfJsdb = True And HisVar.HisVar.YfCode.Trim & "" <> "" And HisVar.HisVar.Sqldal.GetSingle("Select Count(1) From Zd_QxModule,Zd_Qx2 where Zd_Qx2.Module_Code = Zd_QxModule.Module_Code and Zd_Qx2.glz_code = '" & HisVar.HisVar.GlzCode & "' and Xl_Name = '药房接收调拨'  ") > 0) Then
            If HisVar.HisVar.Sqldal.GetSingle("Select Count(Ck_Code) from Yk_Yf1 where Ck_Ok=1 And Ck_Qr=0 And Yk_Yf1.Yy_Code='" & HisVar.HisVar.WsyCode & "' And Yk_Yf1.Yf_Code='" & HisVar.HisVar.YfCode & "' And CONVERT(varchar(10),Ck_Date,120)='" & Format(Now, "yyyy-MM-dd") & "'") > 0 Then
                If p_timer3Start = False And BaseFunc.BaseFunc.CheckForm(Yf_Js1) = False Then
                    p_Flag = "药房接收药库调拨"
                    Me.NotifyIcon1.Icon = m_Icon1
                    '判断是否有临时数据
                    Timer3.Interval = 1
                    Timer3.Enabled = True
                    Timer3.Start()

                    '声音提示
                    Me.NotifyIcon1.ShowBalloonTip(200, "您有新的药库调拨单需要处理！", vbCr & vbLf & "    您可以双击该图标来查看调拨单明细！" & vbCr & vbLf & vbCr & vbLf & "    单击此处可以关闭该提醒", System.Windows.Forms.ToolTipIcon.Info)
                    Me.music()
                    Exit Sub
                End If
            End If
        End If

        ' 门诊处方收费提醒
        If HisPara.PublicConfig.MsgMzCfSf = True And HisVar.HisVar.Sqldal.GetSingle("Select Count(1) From Zd_QxModule,Zd_Qx2 where Zd_Qx2.Module_Code = Zd_QxModule.Module_Code and Zd_Qx2.glz_code = '" & HisVar.HisVar.GlzCode & "' and Xl_Name = '门诊收费'  ") > 0 Then
            If HisVar.HisVar.Sqldal.GetSingle("Select Count(Mz_Code) From Mz where MzCf_Ok = '1' and Mz_Print = '0' and Mz_Date =CONVERT(varchar(100), GETDATE(), 23) And Yy_Code='" & HisVar.HisVar.WsyCode & "' ") > 0 Then
                If p_timer3Start = False And Not Common.BaseForm.PublicFunc.IsChildFormOpen(Of ZTHisOutpatient.Mz_Sf)() Then
                    p_Flag = "门诊收费"
                    Me.NotifyIcon1.Icon = m_Icon1
                    '判断是否有临时数据
                    Timer3.Interval = 1
                    Timer3.Enabled = True
                    Timer3.Start()

                    '声音提示
                    Me.NotifyIcon1.ShowBalloonTip(200, "您有新的处方收费需要处理！", vbCr & vbLf & "    您可以双击该图标来查看处方收费明细！" & vbCr & vbLf & vbCr & vbLf & "    单击此处可以关闭该提醒", System.Windows.Forms.ToolTipIcon.Info)
                    Me.music()
                    Exit Sub
                End If
            End If
        End If

        ' 护士汇总领药提醒
        If HisPara.PublicConfig.MsgHsHzLy = True And HisVar.HisVar.Sqldal.GetSingle("Select Count(1) From Zd_QxModule,Zd_Qx2 where Zd_Qx2.Module_Code = Zd_QxModule.Module_Code and Zd_Qx2.glz_code = '" & HisVar.HisVar.GlzCode & "' and Xl_Name = '汇总领药'  ") > 0 Then
            If HisVar.HisVar.Sqldal.GetSingle("Select Count(Cf_Code) From Bl_Cf where Cf_Print = '是' and Ly_Wc = '否' and CF_Date =CONVERT(varchar(100), GETDATE(), 23) And Yy_Code='" & HisVar.HisVar.WsyCode & "'") > 0 Then
                If p_timer3Start = False And Not Common.BaseForm.PublicFunc.IsChildFormOpen(Of 护士站.Hsz_Hzly)() Then
                    p_Flag = "汇总领药"
                    Me.NotifyIcon1.Icon = m_Icon1
                    '判断是否有临时数据
                    Timer3.Interval = 1
                    Timer3.Enabled = True
                    Timer3.Start()

                    '声音提示
                    Me.NotifyIcon1.ShowBalloonTip(200, "您有新的医嘱需要处理！", vbCr & vbLf & "    您可以双击该图标来进行汇总领药！" & vbCr & vbLf & vbCr & vbLf & "    单击此处可以关闭该提醒", System.Windows.Forms.ToolTipIcon.Info)
                    Me.music()
                    Exit Sub
                End If
            End If
        End If

        ' 项目检查提醒
        If HisPara.PublicConfig.MsgXmJc = True And HisVar.HisVar.Sqldal.GetSingle("Select Count(1) From Zd_QxModule,Zd_Qx2 where Zd_Qx2.Module_Code = Zd_QxModule.Module_Code and Zd_Qx2.glz_code = '" & HisVar.HisVar.GlzCode & "' and Xl_Name = '项目检查'  ") > 0 Then
            If (HisVar.HisVar.Sqldal.GetSingle("select Count(Bl_Cfxm.Cf_Code)  from bl_cf,Bl_Cfxm,Zd_YyKs_Xm where Bl_Cfxm.Xm_Code=Zd_YyKs_Xm.Xm_Code and bl_cf.cf_code = bl_cfxm.cf_code and Cf_Print = '是' and Xm_Wc ='否' and CF_Date =CONVERT(varchar(100), GETDATE(), 23) And Bl_Cfxm.Yy_Code='" & HisVar.HisVar.WsyCode & "' and Zd_YyKs_Xm.Ks_Code='" & HisVar.HisVar.XmKs & "'   ") > 0) Or
                (HisVar.HisVar.Sqldal.GetSingle("select Count(Mz_Xm.Mz_Code) from Mz,Mz_Xm,Zd_YyKs_Xm where Mz.Mz_Code = Mz_Xm.Mz_Code and Mz_Xm.Xm_Code = Zd_YyKs_Xm.Xm_code and Mz_Print = '1' and Xm_Wc = '否'  and  Mz_Date =CONVERT(varchar(100), GETDATE(), 23) And Mz_Xm.Yy_Code='" & HisVar.HisVar.WsyCode & "' and Zd_YyKs_Xm.Ks_Code='" & HisVar.HisVar.XmKs & "'   ") > 0) Or
                (HisVar.HisVar.Sqldal.GetSingle("select Count(Mz_Xm_Sum.Mz_Code) from Mz_Sum,Mz_Xm_Sum,Zd_YyKs_Xm where Mz_Sum.Mz_Code = Mz_Xm_Sum.Mz_Code and Mz_Xm_Sum.Xm_Code = Zd_YyKs_Xm.Xm_code and Mz_Print = '1' and Xm_Wc = '否'  and  Mz_Date =CONVERT(varchar(100), GETDATE(), 23) And Mz_Xm_Sum.Yy_Code='" & HisVar.HisVar.WsyCode & "' and Zd_YyKs_Xm.Ks_Code='" & HisVar.HisVar.XmKs & "'    ") > 0) Then
                If p_timer3Start = False And Not Common.BaseForm.PublicFunc.IsChildFormOpen(Of ZTHisLis.Jc_Xm)() Then
                    p_Flag = "项目检查"
                    Me.NotifyIcon1.Icon = m_Icon1
                    '判断是否有临时数据
                    Timer3.Interval = 1
                    Timer3.Enabled = True
                    Timer3.Start()

                    '声音提示
                    Me.NotifyIcon1.ShowBalloonTip(200, "您有新的项目检查需要处理！", vbCr & vbLf & "    您可以双击该图标来进行项目检查！" & vbCr & vbLf & vbCr & vbLf & "    单击此处可以关闭该提醒", System.Windows.Forms.ToolTipIcon.Info)
                    Me.music()
                    Exit Sub
                End If

            End If
        End If
    End Sub

    Private Sub Timer3_Tick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Timer3.Tick
        p_timer3Start = True
        Timer3.Interval = 500
        If m_bFlag = True Then
            Me.NotifyIcon1.Icon = m_Icon2
            m_bFlag = False
        Else
            Me.NotifyIcon1.Icon = m_Icon1
            m_bFlag = True
        End If

    End Sub

    Private Sub music()
        Dim ThePath As String = System.IO.Path.GetDirectoryName(Application.ExecutablePath)
        player.SoundLocation = ThePath & "\sound\msg.wav"
        player.LoadAsync()
        player.PlaySync()
    End Sub

    Private Sub NotifyIcon1_MouseClick(ByVal sender As System.Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles NotifyIcon1.MouseClick
        Dim frm As Form = Nothing
        Me.Cursor = Cursors.WaitCursor
        Me.RibbonLabel2.Text = "正在打开窗体...请稍后"
        Select Case p_Flag
            Case "住院发药"
                frm = New ZTHisPharmacy.Zy_Fy()
            Case "门诊药房发药"
                frm = New ZTHisPharmacy.Mz_Fy()
            Case "药房接收药库调拨"
                frm = New Yf_Js1()
            Case "门诊收费"
                frm = New ZTHisOutpatient.Mz_Sf(Nothing)
            Case "汇总领药"
                frm = New 护士站.Hsz_Hzly
            Case "项目检查"
                frm = New ZTHisLis.Jc_Xm()
        End Select

        BaseFunc.BaseFunc.addTabControl(frm, p_Flag)

        Me.Cursor = Cursors.Default

        p_timer3Start = False
        Timer3.Stop()
        NotifyIcon1.Icon = Nothing
    End Sub

    Private Sub MainForm_KeyDown(sender As Object, e As KeyEventArgs) Handles MyBase.KeyDown
        If e.Control And e.Alt And e.KeyCode = Keys.H Then
            Dim frm As New Test
            BaseFunc.BaseFunc.addTabControl(frm, "测试")
        End If
    End Sub


#End Region

End Class