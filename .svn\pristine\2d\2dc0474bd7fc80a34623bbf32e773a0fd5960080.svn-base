﻿/**  版本信息模板在安装目录下，可自行修改。
* Zd_Ry.cs
*
* 功 能： N/A
* 类 名： Zd_Ry
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2018/10/24 10:08:10   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace ModelOld
{
	/// <summary>
	/// Zd_Ry:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class M_Zd_Ry
	{
		public M_Zd_Ry()
		{}
		#region Model
		private string _jkda_code;

		private string _xzqh_code;
		private string _ry_name;
		private string _ry_xzz;
		private string _ry_hjzz;
		private string _ry_lxdh;
		private string _jd_dw;
		private string _jd_jdr;
		private string _jd_zrys;
		private string _jd_date;
		private string _ry_sex;
		private string _ry_sfzh;
		private string _ry_birth;
		private string _ry_age;
		private string _ry_gzdw;
		private string _ry_brdh;
		private string _ry_lxrxm;
		private string _ry_lxrdh;
		private string _ry_xx;
		private string _ry_rhyx;
		private string _ry_whcd;
		private string _ry_czlx= "0";
		private string _ry_mz;
	    private string _ry_ssmz;     		
		private string _ry_zy;
		private string _ry_hyzk;
		private string _ry_zffs;
		private string _ry_qtzffs;		
		private string _ry_ywgmmc;
		private string _ry_ywgmqt;
		private string _ry_blsmc;
		private string _jb1;
		private string _jb2;
		private string _jb3;
		private string _jb4;
		private string _jb5;
		private string _jb6;
		private string _jb1_qzsj;
		private string _jb2_qzsj;
		private string _jb3_qzsj;
		private string _jb4_qzsj;
		private string _jb5_qzsj;
		private string _jb6_qzsj;
		private string _jb_zlmc;
		private string _jb_zybmc;
		private string _jb_qtmc;
		private string _ss= "0";
		private string _ss_mc1;
		private string _ss_mc2;
		private string _ss_sj1;
		private string _ss_sj2;
		private string _ws= "0";
		private string _ws_mc1;
		private string _ws_sj1;
		private string _ws_mc2;
		private string _ws_sj2;
		private string _sx= "0";
		private string _sx_yy1;
		private string _sx_sj1;
		private string _sx_yy2;
		private string _sx_sj2;
		private string _jzs_fqjb;
		private string _jzs_fqqt;
		private string _jzs_mqjb;
		private string _jzs_mqqt;
		private string _jzs_xdjm;
		private string _jzs_xdjmqt;
		private string _jzs_znjb;
		private string _jzs_znqt;
		private string _ycb= "0";
		private string _ycb_mc;
		private string _cjqk;
		private string _cjqk_qtcj;
		private string _shhj_cfpfss;
		private string _shhj_rllx;
		private string _shhj_ys;
		private string _shhj_cs;
		private string _shhj_qcl;	
		private string _jsr_code;
		private string _jsr_date;
		private string _yy_code;		
		/// <summary>
		/// 健康档案编码
		/// </summary>
		public string Jkda_Code
		{
			set{ _jkda_code=value;}
			get{return _jkda_code;}
		}
		
		/// <summary>
		/// 行政区划
		/// </summary>
		public string Xzqh_Code
		{
			set{ _xzqh_code=value;}
			get{return _xzqh_code;}
		}
		/// <summary>
		/// 姓名
		/// </summary>
		public string Ry_Name
		{
			set{ _ry_name=value;}
			get{return _ry_name;}
		}
		/// <summary>
		/// 现住址
		/// </summary>
		public string Ry_Xzz
		{
			set{ _ry_xzz=value;}
			get{return _ry_xzz;}
		}
		/// <summary>
		/// 户籍住址
		/// </summary>
		public string Ry_Hjzz
		{
			set{ _ry_hjzz=value;}
			get{return _ry_hjzz;}
		}
		/// <summary>
		/// 联系电话
		/// </summary>
		public string Ry_Lxdh
		{
			set{ _ry_lxdh=value;}
			get{return _ry_lxdh;}
		}
		/// <summary>
		/// 建档单位
		/// </summary>
		public string Jd_Dw
		{
			set{ _jd_dw=value;}
			get{return _jd_dw;}
		}
		/// <summary>
		/// 建党人名称
		/// </summary>
		public string Jd_Jdr
		{
			set{ _jd_jdr=value;}
			get{return _jd_jdr;}
		}
		/// <summary>
		/// 责任医生
		/// </summary>
		public string Jd_Zrys
		{
			set{ _jd_zrys=value;}
			get{return _jd_zrys;}
		}
		/// <summary>
		/// 建档日期
		/// </summary>
		public string Jd_Date
		{
			set{ _jd_date=value;}
			get{return _jd_date;}
		}
		/// <summary>
		/// 性别 0.未知的性别 1.男 2.女 9.未说明的性别
		/// </summary>
		public string Ry_Sex
		{
			set{ _ry_sex=value;}
			get{return _ry_sex;}
		}
		/// <summary>
		/// 身份证号
		/// </summary>
		public string Ry_Sfzh
		{
			set{ _ry_sfzh=value;}
			get{return _ry_sfzh;}
		}
		/// <summary>
		/// 出生日期
		/// </summary>
		public string Ry_Birth
		{
			set{ _ry_birth=value;}
			get{return _ry_birth;}
		}
		/// <summary>
		/// 年龄
		/// </summary>
		public string Ry_Age
		{
			set{ _ry_age=value;}
			get{return _ry_age;}
		}
		/// <summary>
		/// 工作单位
		/// </summary>
		public string Ry_Gzdw
		{
			set{ _ry_gzdw=value;}
			get{return _ry_gzdw;}
		}
		/// <summary>
		/// 本人电话
		/// </summary>
		public string Ry_Brdh
		{
			set{ _ry_brdh=value;}
			get{return _ry_brdh;}
		}
		/// <summary>
		/// 联系人姓名
		/// </summary>
		public string Ry_Lxrxm
		{
			set{ _ry_lxrxm=value;}
			get{return _ry_lxrxm;}
		}
		/// <summary>
		/// 联系人电话
		/// </summary>
		public string Ry_Lxrdh
		{
			set{ _ry_lxrdh=value;}
			get{return _ry_lxrdh;}
		}
		/// <summary>
		/// 血型 1.A型 2.B型 3.O型 4.AB型 5.不详
		/// </summary>
		public string Ry_Xx
		{
			set{ _ry_xx=value;}
			get{return _ry_xx;}
		}
		/// <summary>
		/// 1否  2是  3不详
		/// </summary>
		public string Ry_Rhyx
		{
			set{ _ry_rhyx=value;}
			get{return _ry_rhyx;}
		}
		/// <summary>
		/// 文化程度 1文盲及半文盲  2小学  3初中  4高中/技校/中专  5大学专科及以上  6不详
		/// </summary>
		public string Ry_Whcd
		{
			set{ _ry_whcd=value;}
			get{return _ry_whcd;}
		}
		/// <summary>
		/// 常住类型 0.户籍 1.非户籍
		/// </summary>
		public string Ry_Czlx
		{
			set{ _ry_czlx=value;}
			get{return _ry_czlx;}
		}
		/// <summary>
		/// 民族 1.汉族 2.少数民族
		/// </summary>
		public string Ry_Mz
		{
			set{ _ry_mz=value;}
			get{return _ry_mz;}
		}	
		/// <summary>
		/// 少数民族名称
		/// </summary>
		public string Ry_Ssmz
		{
			set{ _ry_ssmz=value;}
			get{return _ry_ssmz;}
		}
		/// <summary>
		/// 职业 1国家机关、党群组织、企业、事业单位负责人 2专业技术人员 3办事人员和有关人员  4商业、服务业人员  5 农、林、牧、渔、水利业生产人员  6生产、运输设备操作人员及有关人员  7军人  8不便分类的其他从业人员
		/// </summary>
		public string Ry_Zy
		{
			set{ _ry_zy=value;}
			get{return _ry_zy;}
		}
		/// <summary>
		/// 婚姻状况 1未婚  2 已婚  3丧偶  4离婚  5未说明的婚姻状况
		/// </summary>
		public string Ry_Hyzk
		{
			set{ _ry_hyzk=value;}
			get{return _ry_hyzk;}
		}
		/// <summary>
		/// 医疗费用支付方式1 1城镇职工基本医疗保险  2城镇居民基本医疗保险  3新型农村合作医疗 
		/// </summary>
		public string Ry_Zffs
		{
			set{ _ry_zffs=value;}
			get{return _ry_zffs;}
		}
		/// <summary>
		/// 其他支付方式
		/// </summary>
		public string Ry_Qtzffs
		{
			set{ _ry_qtzffs=value;}
			get{return _ry_qtzffs;}
		}
		
		/// <summary>
		/// 药物过敏史1  2青霉素  3磺胺   4链霉素   5其他
		/// </summary>
		public string Ry_Ywgmmc
		{
			set{ _ry_ywgmmc=value;}
			get{return _ry_ywgmmc;}
		}
		/// <summary>
		/// 药物过敏史4  2青霉素  3磺胺   4链霉素   5其他
		/// </summary>
		public string Ry_Ywgmqt
		{
			set{ _ry_ywgmqt=value;}
			get{return _ry_ywgmqt;}
		}
		
		/// <summary>
		/// 暴露史 2化学品    3毒物    4射线
		/// </summary>
		public string Ry_Blsmc
		{
			set{ _ry_blsmc=value;}
			get{return _ry_blsmc;}
		}
		/// <summary>
		/// 既往史 疾病1 1无   2高血压   3糖尿病   4冠心病   5慢性阻塞性肺疾病 　6恶性肿瘤  
        ///7脑卒中  8重性精神疾病　9结核病  10肝炎  11其他法定传染病 12职业病
        ///13其他
		/// </summary>
		public string Jb1
		{
			set{ _jb1=value;}
			get{return _jb1;}
		}
		/// <summary>
		/// 既往史 疾病2 1无   2高血压   3糖尿病   4冠心病   5慢性阻塞性肺疾病 　6恶性肿瘤  
      ///7脑卒中  8重性精神疾病　9结核病  10肝炎  11其他法定传染病 12职业病
      ///13其他
		/// </summary>
		public string Jb2
		{
			set{ _jb2=value;}
			get{return _jb2;}
		}
		/// <summary>
		/// 既往史 疾病3 1无   2高血压   3糖尿病   4冠心病   5慢性阻塞性肺疾病 　6恶性肿瘤  
       ///7脑卒中  8重性精神疾病　9结核病  10肝炎  11其他法定传染病 12职业病
       ///13其他
		/// </summary>
		public string Jb3
		{
			set{ _jb3=value;}
			get{return _jb3;}
		}
		/// <summary>
		/// 既往史 疾病4 1无   2高血压   3糖尿病   4冠心病   5慢性阻塞性肺疾病 　6恶性肿瘤  
       ///7脑卒中  8重性精神疾病　9结核病  10肝炎  11其他法定传染病 12职业病
       ///13其他
		/// </summary>
		public string Jb4
		{
			set{ _jb4=value;}
			get{return _jb4;}
		}
		/// <summary>
		/// 既往史 疾病5 1无   2高血压   3糖尿病   4冠心病   5慢性阻塞性肺疾病 　6恶性肿瘤  
        ///7脑卒中  8重性精神疾病　9结核病  10肝炎  11其他法定传染病 12职业病
        ///13其他
		/// </summary>
		public string Jb5
		{
			set{ _jb5=value;}
			get{return _jb5;}
		}
		/// <summary>
		/// 既往史 疾病6 1无   2高血压   3糖尿病   4冠心病   5慢性阻塞性肺疾病 　6恶性肿瘤  
       ///7脑卒中  8重性精神疾病　9结核病  10肝炎  11其他法定传染病 12职业病
       ///13其他
		/// </summary>
		public string Jb6
		{
			set{ _jb6=value;}
			get{return _jb6;}
		}
		/// <summary>
		/// 疾病1 确诊时间 格式：yyyy-MM
		/// </summary>
		public string Jb1_Qzsj
		{
			set{ _jb1_qzsj=value;}
			get{return _jb1_qzsj;}
		}
		/// <summary>
		/// 疾病2 确诊时间 格式：yyyy-MM
		/// </summary>
		public string Jb2_Qzsj
		{
			set{ _jb2_qzsj=value;}
			get{return _jb2_qzsj;}
		}
		/// <summary>
		/// 疾病3 确诊时间 格式：yyyy-MM
		/// </summary>
		public string Jb3_Qzsj
		{
			set{ _jb3_qzsj=value;}
			get{return _jb3_qzsj;}
		}
		/// <summary>
		/// 疾病4 确诊时间 格式：yyyy-MM
		/// </summary>
		public string Jb4_Qzsj
		{
			set{ _jb4_qzsj=value;}
			get{return _jb4_qzsj;}
		}
		/// <summary>
		/// 疾病5 确诊时间 格式：yyyy-MM
		/// </summary>
		public string Jb5_Qzsj
		{
			set{ _jb5_qzsj=value;}
			get{return _jb5_qzsj;}
		}
		/// <summary>
		/// 疾病6 确诊时间 格式：yyyy-MM
		/// </summary>
		public string Jb6_Qzsj
		{
			set{ _jb6_qzsj=value;}
			get{return _jb6_qzsj;}
		}
		/// <summary>
		/// 恶性肿瘤名称
		/// </summary>
		public string Jb_Zlmc
		{
			set{ _jb_zlmc=value;}
			get{return _jb_zlmc;}
		}
		/// <summary>
		/// 职业病名称
		/// </summary>
		public string Jb_Zybmc
		{
			set{ _jb_zybmc=value;}
			get{return _jb_zybmc;}
		}
		/// <summary>
		/// 其他疾病
		/// </summary>
		public string Jb_Qtmc
		{
			set{ _jb_qtmc=value;}
			get{return _jb_qtmc;}
		}
		/// <summary>
		/// 是否手术 0.无 1.有
		/// </summary>
		public string Ss
		{
			set{ _ss=value;}
			get{return _ss;}
		}
		/// <summary>
		/// 手术名称1
		/// </summary>
		public string Ss_Mc1
		{
			set{ _ss_mc1=value;}
			get{return _ss_mc1;}
		}
		/// <summary>
		/// 手术名称2
		/// </summary>
		public string Ss_Mc2
		{
			set{ _ss_mc2=value;}
			get{return _ss_mc2;}
		}
		/// <summary>
		/// 手术时间1
		/// </summary>
		public string Ss_Sj1
		{
			set{ _ss_sj1=value;}
			get{return _ss_sj1;}
		}
		/// <summary>
		/// 手术时间2
		/// </summary>
		public string Ss_Sj2
		{
			set{ _ss_sj2=value;}
			get{return _ss_sj2;}
		}
		/// <summary>
		/// 有无外伤 0.无 1.有
		/// </summary>
		public string Ws
		{
			set{ _ws=value;}
			get{return _ws;}
		}
		/// <summary>
		/// 外伤名称1
		/// </summary>
		public string Ws_Mc1
		{
			set{ _ws_mc1=value;}
			get{return _ws_mc1;}
		}
		/// <summary>
		/// 外伤时间1
		/// </summary>
		public string Ws_Sj1
		{
			set{ _ws_sj1=value;}
			get{return _ws_sj1;}
		}
		/// <summary>
		/// 外伤名称2
		/// </summary>
		public string Ws_Mc2
		{
			set{ _ws_mc2=value;}
			get{return _ws_mc2;}
		}
		/// <summary>
		/// 外伤时间2
		/// </summary>
		public string Ws_Sj2
		{
			set{ _ws_sj2=value;}
			get{return _ws_sj2;}
		}
		/// <summary>
		/// 有无输血 0.无 1.有
		/// </summary>
		public string Sx
		{
			set{ _sx=value;}
			get{return _sx;}
		}
		/// <summary>
		/// 输血原因1
		/// </summary>
		public string Sx_Yy1
		{
			set{ _sx_yy1=value;}
			get{return _sx_yy1;}
		}
		/// <summary>
		/// 输血时间1
		/// </summary>
		public string Sx_Sj1
		{
			set{ _sx_sj1=value;}
			get{return _sx_sj1;}
		}
		/// <summary>
		/// 输血原因2
		/// </summary>
		public string Sx_Yy2
		{
			set{ _sx_yy2=value;}
			get{return _sx_yy2;}
		}
		/// <summary>
		/// 输血时间2
		/// </summary>
		public string Sx_Sj2
		{
			set{ _sx_sj2=value;}
			get{return _sx_sj2;}
		}
		/// <summary>
		/// 父亲病史1  01无  02高血压  03糖尿病  04冠心病  05慢性阻塞性肺疾病  06恶性肿瘤  07脑卒中  0
       ///8重性精神疾病  09结核病  10肝炎  11先天畸形  12其他
		/// </summary>
		public string Jzs_Fqjb
		{
			set{ _jzs_fqjb=value;}
			get{return _jzs_fqjb;}
		}
		/// <summary>
		/// 父亲其他病史
		/// </summary>
		public string Jzs_Fqqt
		{
			set{ _jzs_fqqt=value;}
			get{return _jzs_fqqt;}
		}
		/// <summary>
		/// 母亲病史1  01无  02高血压  03糖尿病  04冠心病  05慢性阻塞性肺疾病  06恶性肿瘤  07脑卒中  0
        ///8重性精神疾病  09结核病  10肝炎  11先天畸形  12其他
		/// </summary>
		public string Jzs_Mqjb
		{
			set{ _jzs_mqjb=value;}
			get{return _jzs_mqjb;}
		}
		/// <summary>
		/// 母亲其他病史
		/// </summary>
		public string Jzs_Mqqt
		{
			set{ _jzs_mqqt=value;}
			get{return _jzs_mqqt;}
		}
		/// <summary>
		/// 兄弟姐妹病史1  01无  02高血压  03糖尿病  04冠心病  05慢性阻塞性肺疾病  06恶性肿瘤  07脑卒中  0
        ///8重性精神疾病  09结核病  10肝炎  11先天畸形  12其他
		/// </summary>
		public string Jzs_Xdjm
		{
			set{ _jzs_xdjm=value;}
			get{return _jzs_xdjm;}
		}
		/// <summary>
		/// 兄弟姐妹其他病史
		/// </summary>
		public string Jzs_Xdjmqt
		{
			set{ _jzs_xdjmqt=value;}
			get{return _jzs_xdjmqt;}
		}
		/// <summary>
		/// 子女病史1  01无  02高血压  03糖尿病  04冠心病  05慢性阻塞性肺疾病  06恶性肿瘤  07脑卒中  0
         ///8重性精神疾病  09结核病  10肝炎  11先天畸形  12其他
		/// </summary>
		public string Jzs_Znjb
		{
			set{ _jzs_znjb=value;}
			get{return _jzs_znjb;}
		}
		/// <summary>
		/// 子女其他病史
		/// </summary>
		public string Jzs_Znqt
		{
			set{ _jzs_znqt=value;}
			get{return _jzs_znqt;}
		}
		/// <summary>
		/// 有无遗传病 0.无 1.有
		/// </summary>
		public string Ycb
		{
			set{ _ycb=value;}
			get{return _ycb;}
		}
		/// <summary>
		/// 遗传病名称
		/// </summary>
		public string Ycb_Mc
		{
			set{ _ycb_mc=value;}
			get{return _ycb_mc;}
		}
		/// <summary>
		/// 残疾情况1 1无残疾 2 视力残疾 3听力残疾 4言语残疾 5 肢体残疾
        ///6智力残疾 7精神残疾  8其他残疾
		/// </summary>
		public string Cjqk
		{
			set{ _cjqk=value;}
			get{return _cjqk;}
		}
		/// <summary>
		/// 其他残疾
		/// </summary>
		public string Cjqk_Qtcj
		{
			set{ _cjqk_qtcj=value;}
			get{return _cjqk_qtcj;}
		}
		/// <summary>
		/// 厨房排风设施 1无  2油烟机   3换气扇   4烟囱
		/// </summary>
		public string Shhj_CfPfss
		{
			set{ _shhj_cfpfss=value;}
			get{return _shhj_cfpfss;}
		}
		/// <summary>
		/// 燃料类型  1液化气  2煤  3天然气  4沼气   5柴火  6其他
		/// </summary>
		public string Shhj_RlLx
		{
			set{ _shhj_rllx=value;}
			get{return _shhj_rllx;}
		}
		/// <summary>
		/// 饮水 1自来水   2经净化过滤的水   3井水  4河湖水  5塘水 6其他
		/// </summary>
		public string Shhj_Ys
		{
			set{ _shhj_ys=value;}
			get{return _shhj_ys;}
		}
		/// <summary>
		/// 厕所 1卫生厕所 2一格或二格粪池式 3马桶  4露天粪坑  5简易棚厕
		/// </summary>
		public string Shhj_Cs
		{
			set{ _shhj_cs=value;}
			get{return _shhj_cs;}
		}
		/// <summary>
		/// 禽畜栏 1单设  2室内   3室外
		/// </summary>
		public string Shhj_Qcl
		{
			set{ _shhj_qcl=value;}
			get{return _shhj_qcl;}
		}
		
		/// <summary>
		/// 经手人编码
		/// </summary>
		public string Jsr_Code
		{
			set{ _jsr_code=value;}
			get{return _jsr_code;}
		}
		/// <summary>
		/// 录入日期
		/// </summary>
		public string Jsr_Date
		{
			set{ _jsr_date=value;}
			get{return _jsr_date;}
		}
		/// <summary>
		/// 医院编码 与Zd_Yy关联
		/// </summary>
		public string Yy_Code
		{
			set{ _yy_code=value;}
			get{return _yy_code;}
		}						
		#endregion Model

	}
}

