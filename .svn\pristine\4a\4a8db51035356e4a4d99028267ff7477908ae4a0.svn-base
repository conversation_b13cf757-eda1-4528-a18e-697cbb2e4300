﻿Imports System.Data.SqlClient
Imports System.Text.RegularExpressions
Imports BaseClass
Imports ZTHisOutpatient

Public Class Zd_Bl12

    Dim V_Lr As String = "简称"                 '药品__辅助录入

    Dim My_Cc As New BaseClass.C_Cc()                         '取最大编码及简称的类
    Dim My_Dataset As New DataSet
    Dim Yh_Table As DataTable
    Dim V_Jb_Code As String
    Dim Load_Ok As Boolean
    Dim Tb As DataTable
    Dim _bllbl As New BLL.BllBl
#Region "传参"
    Dim Rinsert As Boolean
    Dim Rrow As DataRow
    Dim RZbtb As DataTable
    Dim Rtdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Dim Rlb As C1.Win.C1Input.C1Label
    Dim Rzbadt As SqlDataAdapter

#End Region

    Public Sub New(ByVal tinsert As Boolean, ByVal trow As DataRow, ByVal tZbtb As DataTable, ByVal ttdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid, ByVal tlb As C1.Win.C1Input.C1Label, ByVal tzbadt As SqlDataAdapter)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        Rinsert = tinsert
        'Rdate = tdate
        Rrow = trow
        RZbtb = tZbtb
        Rtdbgrid = ttdbgrid
        Rlb = tlb
        Rzbadt = tzbadt

        ' 在 InitializeComponent() 调用之后添加任何初始化。
    End Sub

    Protected Overrides Sub OnKeyDown(ByVal e As System.Windows.Forms.KeyEventArgs)
        MyBase.OnKeyDown(e)
        If e.Control = True And e.KeyCode = Keys.S Then
            Comm1.Select()
            Call Comm_Click(Comm1, Nothing)
        End If
    End Sub

    Private Sub Zd_Bl12_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Call Form_Init()
        Call Combo_Init()

        Dim My_Combo As New BaseClass.C_Combo1(Me.C1Combo2)
        My_Combo.Init_TDBCombo()
        With C1Combo2
            .AddItem("男")
            .AddItem("女")
            .SelectedIndex = 0
            .DropDownWidth = 103
            .Width = 113
        End With
        If HisPara.PublicConfig.ZyHsz = "是" Then
            Label18.Visible = False
            C1Combo3.Visible = False
        Else
            Label18.Visible = True
            C1Combo3.Visible = True
        End If



        If Rinsert = True Then Call Data_Clear() Else Call Data_Show(Rrow)

    End Sub

#Region "窗体__事件"

    Private Sub Form_Init()
        Panel1.Height = 27


        '按扭初始化
        Call P_Comm(Me.Comm1)
        Call P_Comm(Me.Comm2)

        With Me.C1DateEdit1
            '.Value = DBNull.Value
            .DateTimeInput = False     '显示日期的开关
            .AutoChangePosition = False
            .CaseSensitive = True
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .InitialSelection = C1.Win.C1Input.InitialSelectionEnum.CaretAtStart

            .VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.DropDown

            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat


            .EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .EditFormat.CustomFormat = "yyyy-MM-dd HH:mm"

            .DisplayFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .DisplayFormat.CustomFormat = "yyyy-MM-dd HH:mm"

            .MaskInfo.EditMask = "####-##-## ##:##"
            .MaskInfo.AutoTabWhenFilled = True
            .AcceptsTab = True
            .EmptyAsNull = True
            .ValueIsDbNull = True
            With .ErrorInfo
                .BeepOnError = True
                .CanLoseFocus = False
                .ErrorAction = C1.Win.C1Input.ErrorActionEnum.None
            End With
            .Value = Now
        End With

        With Me.C1DateEdit2

            .DateTimeInput = False      '显示日期的开关
            .AutoChangePosition = False
            .CaseSensitive = True
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .InitialSelection = C1.Win.C1Input.InitialSelectionEnum.CaretAtStart
            .VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.DropDown
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat


            .EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .EditFormat.CustomFormat = "yyyy-MM-dd"

            .DisplayFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .DisplayFormat.CustomFormat = "yyyy-MM-dd"

            .MaskInfo.EditMask = "####-##-##"
            .MaskInfo.AutoTabWhenFilled = True
            .AcceptsTab = True
            .EmptyAsNull = True
            .ValueIsDbNull = True
            With .ErrorInfo
                .BeepOnError = True
                .CanLoseFocus = False
                .ErrorAction = C1.Win.C1Input.ErrorActionEnum.None
            End With
            .Value = Now
        End With

        Button7.Visible = False

    End Sub

    Private Sub Combo_Init()

        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "Select Bxlb_Name,Bxlb_Code From Zd_Bxlb where Yy_Code='" & HisVar.HisVar.WsyCode & "'", "病人类别", True)
        Dim My_Combo As BaseClass.C_Combo2 = New BaseClass.C_Combo2(T_Combo, My_Dataset.Tables("病人类别").DefaultView, "Bxlb_Name", "Bxlb_Code", 100)
        With My_Combo
            .Init_TDBCombo()
            .Init_Colum("Bxlb_Name", "类别", 95, "中")
            .Init_Colum("Bxlb_Code", "编码", 0, "左")

            .MaxDropDownItems(15)
            .SelectedIndex(-1)
        End With
        T_Combo.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList


        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "Select Jb_Jc,Jb_Name,Jb_Code From Zd_Ml_Jb3 Order BY Jb_Jc", "疾病字典", True)
        Dim My_Combo1 As BaseClass.C_Combo2 = New BaseClass.C_Combo2(C1Combo1, My_Dataset.Tables("疾病字典").DefaultView, "Jb_Name", "Jb_Code", 465)
        With My_Combo1
            .Init_TDBCombo()
            .Init_Colum("Jb_Jc", "疾病简称", 115, "左")
            .Init_Colum("Jb_Name", "疾病全称", 190, "左")
            .Init_Colum("Jb_Code", "", 0, "左")
            .MaxDropDownItems(14)
            .SelectedIndex(-1)
        End With
        My_Dataset.Tables("疾病字典").DefaultView.Sort = "Jb_Jc Asc"
        C1Combo1.AutoSelect = False
        C1Combo1.AutoCompletion = False


        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "select Zd_YyBc.Bc_Code,Bc_Jc,Bq_Name,Bc_Name,Bc_Memo From Zd_YyBc left join Zd_YyBq2 On Zd_YyBc.Bc_Code=Zd_YyBq2.Bc_Code Left Join Zd_YyBq1 On Zd_YyBq2.Bq_Code=Zd_YyBq1.Bq_Code  where  Not Exists (Select  Bc_Code from Bl where Bl.Bc_Code=Zd_YyBc.Bc_Code and  Ry_CyDate is null) Order By Zd_YyBc.Bc_Code", "床位字典", True)

        Dim My_Combo3 As BaseClass.C_Combo2 = New BaseClass.C_Combo2(C1Combo3, My_Dataset.Tables("床位字典").DefaultView, "Bc_Name", "Bc_Code", 295)
        With My_Combo3
            .Init_TDBCombo()
            .Init_Colum("Bc_Jc", "床位简称", 60, "左")
            .Init_Colum("Bq_Name", "病区名称", 95, "左")
            .Init_Colum("Bc_Name", "床位名称", 115, "左")
            .Init_Colum("Bc_Memo", "备注", 0, "左")
            .Init_Colum("Bc_Code", "", 0, "左")
            .MaxDropDownItems(15)
            .SelectedIndex(-1)
        End With
        C1Combo3.AutoSelect = False
        C1Combo3.AutoCompletion = False



        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "select Ys_Code,Ys_Jc,Ys_Name,Ks_Name from Zd_YyYs,Zd_Yyks where Zd_YyYs.Ks_Code=Zd_YyKs.Ks_Code  and Ys_Use= '是' Order By Ys_Code", "医生字典", True)

        Dim My_Combo4 As BaseClass.C_Combo2 = New BaseClass.C_Combo2(C1Combo4, My_Dataset.Tables("医生字典").DefaultView, "Ys_Name", "Ys_Code", 245)
        With My_Combo4
            .Init_TDBCombo()
            .Init_Colum("Ys_Jc", "医生简称", 60, "左")
            .Init_Colum("Ys_Name", "医生姓名", 80, "左")
            .Init_Colum("ks_Name", "科室", 80, "左")
            .Init_Colum("Ys_Code", "", 0, "左")
            .MaxDropDownItems(15)
            .SelectedIndex(-1)
        End With
        C1Combo4.AutoSelect = False
        C1Combo4.AutoCompletion = False

        Dim My_Combo5 As BaseClass.C_Combo2 = New BaseClass.C_Combo2(C1Combo5, DAL.DAL_Dict.GetDepartmentDict.DefaultView, "Ks_name", "Ks_Code", 150)
        With My_Combo5
            .Init_TDBCombo()
            .Init_Colum("ks_Jc", "科室简称", 0, "左")
            .Init_Colum("Ks_Name", "科室姓名", 130, "左")
            .Init_Colum("Ks_Code", "", 0, "左")
            .MaxDropDownItems(15)
            .SelectedIndex(-1)
        End With
        C1Combo5.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList



    End Sub
    '门诊转住院
    Private Sub DataSearch()
        Dim f As New RkSearch()
        f.Owner = Me

        If f.ShowDialog <> DialogResult.OK Then
            Exit Sub
        End If
        My_Cc.Get_MaxCode("Bl", "Bl_Code", 14, "Left(Bl_Code,10)", HisVar.HisVar.WsyCode & Format(Now, "yyMMdd"))
        L_Dl_Code.Text = My_Cc.编码
        C1TextBox4.Text = f._mdlMz.Ry_YlCode
        Me.T_Combo.SelectedValue = f._mdlMz.Bxlb_Code
        C1TextBox1.Text = f._mdlMz.Ry_Name
        C1Combo2.Text = f._mdlMz.Ry_Sex
        C1TextBox3.Text = f._mdlMz.Ry_Sfzh
        C1TextBox6.Text = f._mdlMz.Ry_Address
        C1TextBox2.Text = ""
        V_Jb_Code = f._mdlMz.Jb_Code
        Label17.Text = f._mdlMz.Jb_Name
        C1Combo1.SelectedIndex = -1
        C1TextBox7.Text = f._mdlMz.Mz_Code
        My_Cc.Get_Py(Me.C1TextBox1.Text & "")
        L_Dl_Jc.Text = My_Cc.简拚.ToString

    End Sub

#End Region

#Region "数据__操作"

    Private Sub Data_Clear()

        Rinsert = True

        My_Cc.Get_MaxCode("Bl", "Bl_Code", 14, "Left(Bl_Code,10)", HisVar.HisVar.WsyCode & Format(Now, "yyMMdd"))
        L_Dl_Code.Text = My_Cc.编码                             '最大编码
        'C1DateEdit2.Value = DBNull.Value
        T_Combo.SelectedIndex = 0
        C1TextBox1.Text = ""
        C1TextBox2.Text = ""
        L_Dl_Jc.Text = ""
        C1TextBox3.Text = ""
        C1TextBox4.Text = ""
        C1TextBox5.Text = ""
        C1TextBox6.Text = ""
        C1DateEdit1.Value = Now
        C1DateEdit2.Value = Now
        V_Jb_Code = ""
        Label17.Text = ""
        C1TextBox9.Text = _bllbl.MaxRyBlCode
        C1Combo4.SelectedIndex = -1
        C1Combo5.SelectedIndex = -1
        C1TextBox8.Text = ""
        C1TextBox9.Text = _bllbl.MaxRyBlCode
        C1Combo3.SelectedIndex = -1
        C1Combo1.SelectedIndex = -1
        C1Combo1.Text = ""
        If (T_Combo.Text = "合作医疗" Or T_Combo.Text = "城乡居民") Then
            C1TextBox4.Enabled = True
            C1TextBox4.BackColor = Color.White
        Else
            C1TextBox4.Enabled = False
            C1TextBox4.BackColor = SystemColors.Info
        End If

        Me.C1TextBox3.Select()
    End Sub

    Private Sub Data_Show(ByVal tmp_Row As DataRow)
        Rinsert = False
        Rrow = tmp_Row
        With Rrow
            If My_Dataset.Tables("床位字典").Select("Bc_Code='" & Rrow.Item("Bc_Code") & "'").Length = 0 Then
                Dim New_Row As DataRow
                New_Row = My_Dataset.Tables("床位字典").NewRow
                Dim My_Reader As SqlDataReader = HisVar.HisVar.Sqldal.ExecuteReader("Select Bc_Code,Bc_Jc,Bq_Name,Bc_Name,Bc_Memo from V_YyBc where  Bc_Code='" & Rrow.Item("Bc_Code") & "' ")
                While My_Reader.Read
                    New_Row.Item("Bc_Code") = My_Reader.Item("Bc_Code")
                    New_Row.Item("Bc_Jc") = My_Reader.Item("Bc_Jc")
                    New_Row.Item("Bq_Name") = My_Reader.Item("Bq_Name")
                    New_Row.Item("Bc_Name") = My_Reader.Item("Bc_Name")
                    New_Row.Item("Bc_Memo") = My_Reader.Item("Bc_Memo")
                    My_Dataset.Tables("床位字典").Rows.Add(New_Row)
                End While



            End If

            L_Dl_Code.Text = .Item("Bl_Code") & ""
            C1TextBox4.Text = .Item("Ry_YlCode") & ""
            Me.T_Combo.SelectedValue = .Item("Bxlb_code") & ""
            C1TextBox1.Text = .Item("Ry_Name") & ""
            L_Dl_Jc.Text = .Item("Ry_Jc") & ""
            C1Combo2.Text = .Item("Ry_Sex") & ""
            C1TextBox3.Text = .Item("Ry_Sfzh") & ""
            C1DateEdit2.Value = tmp_Row.Item("Ry_Csdate")
            C1TextBox6.Text = .Item("Ry_Address") & ""
            C1DateEdit1.Value = .Item("Ry_RyDate")
            C1TextBox2.Text = .Item("Ry_Memo") & ""
            V_Jb_Code = .Item("Jb_Code") & ""
            Label17.Text = .Item("Jb_Name") & ""
            C1Combo1.SelectedIndex = -1
            C1TextBox8.Text = .Item("Ry_Tele") & ""
            If .Item("Ry_BlCode") & "" = "" Then
                C1TextBox9.Text = _bllbl.MaxRyBlCode '.Item("Ry_BlCode") & ""
            Else
                C1TextBox9.Text = .Item("Ry_BlCode") & "" '.Item("Ry_BlCode") & ""
            End If
            C1Combo3.SelectedValue = .Item("Bc_Code")
            C1Combo4.SelectedValue = .Item("Ys_Code")
            C1Combo5.SelectedValue = .Item("Ks_Code")
        End With

        Me.C1TextBox3.Select()
    End Sub


#End Region

#Region "控件__动作"


    Private Sub Comm_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.Click, Comm2.Click
        Select Case sender.tag
            Case "保存"
                If T_Combo.Text = "" Then
                    MsgBox("请选择病人类别！", MsgBoxStyle.Critical, "提示")
                    T_Combo.Select()
                    Exit Sub
                End If

                If C1TextBox3.Text <> "" Then
                    Dim R As Regex
                    R = New Regex("^\d{18}$|^\d{17}[a-zA-Z]{1}$|^\d{15}$")
                    If R.IsMatch(C1TextBox3.Text) = False Then
                        MsgBox("请输入有效身份证号！", MsgBoxStyle.Critical, "提示")
                        C1TextBox3.Select()
                        Exit Sub
                    End If
                End If

                If LTrim(RTrim(C1TextBox1.Text)) = "" Then
                    Beep()
                    MsgBox("病人姓名不能为空,按任意键返回!", vbOKOnly + vbExclamation, "提示:")
                    C1TextBox1.Select()

                    Exit Sub

                End If

                If (T_Combo.Text = "合作医疗" Or T_Combo.Text = "城乡居民") Then
                    If LTrim(RTrim(C1TextBox4.Text)) = "" Then
                        MsgBox("请正确输入合作医疗编码。", vbExclamation + vbOKOnly + vbDefaultButton1, "提示:")
                        T_Combo.Select()


                        Exit Sub

                    End If
                End If

                If (T_Combo.Text <> "合作医疗" And T_Combo.Text <> "城乡居民") Then
                    C1TextBox4.Text = ""
                End If

                If C1DateEdit2.ValueIsDbNull = True Then
                    MsgBox("请输入病人出生日期。", vbExclamation + vbOKOnly + vbDefaultButton1, "提示:")
                    C1DateEdit2.Select()

                    Exit Sub

                End If


                If Me.C1Combo4.Text = "" Then
                    MsgBox("请选择医生!", vbExclamation + vbOKOnly + vbDefaultButton1, "提示:")
                    C1Combo4.Select()
                    Exit Sub

                End If

                If Me.C1Combo5.Text = "" Then
                    MsgBox("请选择科室!", vbExclamation + vbOKOnly + vbDefaultButton1, "提示:")
                    C1Combo5.Select()

                    Exit Sub

                End If

                If HisPara.PublicConfig.ZyHsz <> "是" Then
                    If Me.C1Combo3.SelectedValue = "" Then
                        MsgBox("请选择病人床位!", vbExclamation + vbOKOnly + vbDefaultButton1, "提示:")
                        C1Combo3.Text = ""
                        C1Combo3.Select()
                        Exit Sub
                    End If
                End If

                If Me.Label17.Text = "" Then
                    MsgBox("请选择病人疾病!", vbExclamation + vbOKOnly + vbDefaultButton1, "提示:")
                    C1Combo1.Select()

                    Exit Sub

                End If


                '去掉医疗证号判断，因为葫芦岛的医疗证号在his里存的是医疗本号，如果一家的两个人同时住院，本号是相同的    15/07/21彭改
                If HisVar.HisVar.Sqldal.GetSingle("Select Count(*) from Bl where    (Ry_Sfzh='" & C1TextBox3.Text & "' and Ry_Sfzh<>'' )   and Ry_Cydate Is null and Bl_Code<>'" & L_Dl_Code.Text & "'") > 0 Then
                    MsgBox("该身份证号已被在院患者使用，请检查是否为重复登记或输入错误！", MsgBoxStyle.Information, "提示")
                    Exit Sub
                End If

                If Rinsert = True Then     '增加记录
                    Call Data_Add()

                Else                                '编辑记录
                    Call Data_Edit()
                End If


            Case "取消"
                Me.Close()
        End Select
    End Sub

    Private Sub TextBox_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles C1TextBox1.KeyPress, C1TextBox2.KeyPress, C1TextBox4.KeyPress, C1Combo1.KeyPress, C1Combo3.KeyPress, C1DateEdit1.KeyPress, C1DateEdit2.KeyPress, C1TextBox3.KeyPress, C1Combo2.KeyPress, T_Combo.KeyPress, C1TextBox6.KeyPress, C1TextBox8.KeyPress, C1TextBox9.KeyPress, C1Combo4.KeyPress, C1Combo5.KeyPress
        If e.KeyChar <> Chr(Keys.Return) Then Exit Sub
        e.Handled = True
        System.Windows.Forms.SendKeys.Send("{Tab}")
    End Sub

    Private Sub C1TextBox2_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1TextBox1.Validated
        My_Cc.Get_Py(Me.C1TextBox1.Text & "")
        L_Dl_Jc.Text = My_Cc.简拚.ToString
    End Sub


#End Region

#Region "数据__编辑"

    Private Sub Data_Add()

        My_Cc.Get_MaxCode("Bl", "Bl_Code", 14, "Left(Bl_Code,10)", HisVar.HisVar.WsyCode & Format(Now, "yyMMdd"))
        Dim My_NewRow As DataRow = RZbtb.NewRow
        With My_NewRow
            .Item("Yy_Code") = HisVar.HisVar.WsyCode
            .Item("Bl_Code") = My_Cc.编码 & ""
            .Item("Ry_YlCode") = Trim(C1TextBox4.Text & "")
            .Item("BxLB_Code") = Me.T_Combo.SelectedValue
            .Item("Bxlb_Name") = Trim(Me.T_Combo.Text & "")
            .Item("Ry_Name") = C1TextBox1.Text & ""
            .Item("Ry_Jc") = L_Dl_Jc.Text & ""
            .Item("Ry_Sex") = C1Combo2.Text
            .Item("Ry_Sfzh") = Trim(C1TextBox3.Text & "")
            .Item("Ry_Csdate") = Format(C1DateEdit2.Value, "yyyy-MM-dd")
            .Item("Ry_Address") = C1TextBox6.Text & ""
            .Item("Ys_Code") = C1Combo4.SelectedValue & ""
            .Item("Ys_Name") = C1Combo4.Columns("Ys_Name").Value & ""
            .Item("Ks_Code") = C1Combo5.SelectedValue & ""
            .Item("Ks_Name") = C1Combo5.Columns("Ks_Name").Value & ""
            .Item("Ry_RyDate") = Format(C1DateEdit1.Value, "yyyy-MM-dd HH:mm")
            .Item("Jsr_Code") = HisVar.HisVar.JsrCode
            .Item("Ry_Memo") = C1TextBox2.Text & ""
            If Label17.Text = "" Then
                .Item("Jb_Code") = DBNull.Value
                .Item("Jb_Name") = ""
            Else
                .Item("Jb_Code") = V_Jb_Code
                .Item("Jb_Name") = Label17.Text
            End If
            .Item("Bc_Code") = C1Combo3.SelectedValue
            .Item("Ry_blCode") = C1TextBox9.Text & ""
            .Item("Ry_Tele") = C1TextBox8.Text & ""


        End With

        '数据保存

        Try
            RZbtb.Rows.Add(My_NewRow)
            Rtdbgrid.MoveLast()
            Rlb.Text = "∑=" + Rtdbgrid.Splits(0).Rows.Count.ToString
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            Exit Sub
        Finally
            Me.C1TextBox1.Select()
        End Try


        '数据更新
        Try
            With Rzbadt.InsertCommand

                .Parameters(0).Value = My_NewRow.Item("Yy_Code")
                .Parameters(1).Value = My_NewRow.Item("Bl_Code")
                .Parameters(2).Value = My_NewRow.Item("Ry_YlCode")
                .Parameters(3).Value = My_NewRow.Item("Bxlb_Code")
                .Parameters(4).Value = My_NewRow.Item("Ry_Name")
                .Parameters(5).Value = My_NewRow.Item("Ry_Jc")
                .Parameters(6).Value = My_NewRow.Item("Ry_Sex")
                .Parameters(7).Value = My_NewRow.Item("Ry_Sfzh")
                .Parameters(8).Value = My_NewRow.Item("Ry_Csdate")
                .Parameters(9).Value = My_NewRow.Item("Ry_Address")
                .Parameters(10).Value = My_NewRow.Item("Ys_Code")
                .Parameters(11).Value = My_NewRow.Item("Ks_Code")
                .Parameters(12).Value = My_NewRow.Item("Ry_RyDate")
                .Parameters(13).Value = My_NewRow.Item("Jsr_Code")
                .Parameters(14).Value = My_NewRow.Item("Ry_Memo")
                .Parameters(15).Value = My_NewRow.Item("Jb_Code")
                .Parameters(16).Value = My_NewRow.Item("Bc_Code")
                .Parameters(17).Value = My_NewRow.Item("Ry_BlCode")
                .Parameters(18).Value = My_NewRow.Item("Ry_Tele")
                .Parameters(19).Value = My_NewRow.Item("Jb_Name")
                .Parameters(20).Value = C1TextBox7.Text & ""
                Call P_Conn(True)
                .ExecuteNonQuery()
            End With
            My_NewRow.AcceptChanges()
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            Exit Sub
        Finally
            C1TextBox1.Select()
            Call P_Conn(False)
        End Try



        '数据清空
        Call Data_Clear()

    End Sub

    Private Sub Data_Edit()

        Dim My_Row As DataRow = Rrow
        Try
            With My_Row
                .BeginEdit()
                .Item("Bl_Code") = Trim(Me.L_Dl_Code.Text & "")
                .Item("Ry_YlCode") = Trim(C1TextBox4.Text & "")
                .Item("Bxlb_code") = Me.T_Combo.SelectedValue & ""
                .Item("Bxlb_Name") = Trim(Me.T_Combo.Text & "")
                .Item("Ry_Name") = C1TextBox1.Text & ""
                .Item("Ry_Jc") = L_Dl_Jc.Text & ""
                .Item("Ry_Sex") = C1Combo2.Text & ""
                .Item("Ry_Sfzh") = Trim(C1TextBox3.Text & "")
                .Item("Ry_Csdate") = Format(C1DateEdit2.Value, "yyyy-MM-dd")
                .Item("Ry_Address") = C1TextBox6.Text & ""
                .Item("Ys_Code") = C1Combo4.SelectedValue & ""
                .Item("Ys_Name") = C1Combo4.Columns("Ys_Name").Value & ""
                .Item("Ks_Code") = C1Combo5.SelectedValue & ""
                .Item("Ks_Name") = C1Combo5.Columns("Ks_Name").Value & ""
                .Item("Ry_RyDate") = Format(C1DateEdit1.Value, "yyyy-MM-dd HH:mm")
                .Item("Jsr_Code") = HisVar.HisVar.JsrCode
                .Item("Ry_Memo") = C1TextBox2.Text & ""
                If Label17.Text = "" Then
                    .Item("Jb_Code") = DBNull.Value
                    .Item("Jb_Name") = ""
                Else
                    .Item("Jb_Code") = V_Jb_Code
                    .Item("Jb_Name") = Label17.Text
                End If
                .Item("Bc_Code") = C1Combo3.SelectedValue
                .Item("Ry_blCode") = C1TextBox9.Text & ""
                .Item("Ry_Tele") = C1TextBox8.Text & ""
                .EndEdit()
            End With

        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Critical, "提示:")
            My_Row.CancelEdit()
            Exit Sub
        Finally
            C1TextBox1.Select()
        End Try

        '数据更新
        Call P_Conn(True)
        With Rzbadt.UpdateCommand
            .Parameters(0).Value = My_Row.Item("Bl_Code")
            .Parameters(1).Value = My_Row.Item("Ry_YlCode")
            .Parameters(2).Value = My_Row.Item("Bxlb_Code")
            .Parameters(3).Value = My_Row.Item("Ry_Name")
            .Parameters(4).Value = My_Row.Item("Ry_Jc")
            .Parameters(5).Value = My_Row.Item("Ry_Sex")
            .Parameters(6).Value = My_Row.Item("Ry_Sfzh")
            .Parameters(7).Value = My_Row.Item("Ry_Csdate")
            .Parameters(8).Value = My_Row.Item("Ry_Address")
            .Parameters(9).Value = My_Row.Item("Ys_Code")
            .Parameters(10).Value = My_Row.Item("Ks_Code")
            .Parameters(11).Value = My_Row.Item("Ry_RyDate")
            .Parameters(12).Value = My_Row.Item("Jsr_Code")
            .Parameters(13).Value = My_Row.Item("Ry_Memo")
            .Parameters(14).Value = My_Row.Item("Jb_Code")
            .Parameters(15).Value = My_Row.Item("Bc_Code")
            .Parameters(16).Value = My_Row.Item("Ry_BlCode")
            .Parameters(17).Value = My_Row.Item("Ry_Tele")
            .Parameters(18).Value = My_Row.Item("Jb_Name")
            .Parameters(19).Value = My_Row.Item("Bl_Code", DataRowVersion.Original)
            Try
                .ExecuteNonQuery()
                My_Row.AcceptChanges()
            Catch ex As Exception
                Beep()
                MsgBox(ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
            Finally
                Call P_Conn(False)
                C1TextBox1.Select()
            End Try
        End With

    End Sub

#End Region

#Region "自定义按扭"

    Private Sub P_Comm(ByVal Bt As Button)
        With Bt
            Select Case .Tag
                Case "保存"
                    .Top = 3
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")
                    .Size = New Size(52, 24)
                    .Text = "            &B"
                Case "取消"
                    .Location = New Point(Comm1.Left + Comm1.Width, Comm1.Top)
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
                    .Size = New Size(52, 24)
                    .Text = "            &C"
            End Select
            .FlatStyle = FlatStyle.Flat
            .FlatAppearance.BorderSize = 0
            .BackgroundImageLayout = ImageLayout.Center
        End With

    End Sub

    Private Sub Comm_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles Comm1.KeyDown, Comm2.KeyDown
        If e.KeyCode = Keys.Space Then
            Select Case sender.tag
                Case "保存"
                    Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存3")

                Case "取消"
                    Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消3")
            End Select
        End If
    End Sub

    Private Sub Comm_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles Comm1.KeyUp, Comm2.KeyUp
        If e.KeyCode = Keys.Enter Or e.KeyCode = Keys.Space Then
            Select Case sender.tag
                Case "保存"
                    Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")
                Case "取消"
                    Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
            End Select
        End If

    End Sub

    Private Sub Comm_MouseEnter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.MouseEnter, Comm2.MouseEnter
        Select Case sender.tag
            Case "保存"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存2")
            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消2")
        End Select
    End Sub

    Private Sub Comm_MouseLeave(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.MouseLeave, Comm2.MouseLeave
        Select Case sender.tag
            Case "保存"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")
            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
        End Select
    End Sub

    Private Sub Comm_MouseDown(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm1.MouseDown, Comm2.MouseDown
        Select Case sender.tag
            Case "保存"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存3")

            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消3")
        End Select
    End Sub

    Private Sub Comm_MouseUp(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm1.MouseUp, Comm2.MouseUp
        Select Case sender.tag
            Case "保存"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")
            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
        End Select
    End Sub

#End Region

    Private Sub C1Combo1_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles C1Combo1.KeyUp

        If (e.KeyValue > 47 And e.KeyValue < 58) Or (e.KeyValue > 96 And e.KeyValue < 123) Or (e.KeyValue > 64 And e.KeyValue < 91) Or (e.KeyValue = 8) Or (e.KeyValue = 189) Or (e.KeyValue = 190) Then
            Dim s As String = C1Combo1.Text
            If C1Combo1.Text = "" Then
                C1Combo1.DataSource.RowFilter = ""
            Else
                C1Combo1.DataSource.RowFilter = "Jb_Jc like '*" & C1Combo1.Text.Replace("*", "[*]") & "*'"
            End If
            If (e.KeyValue = 8) Then
                C1Combo1.DroppedDown = False
                C1Combo1.DroppedDown = True
            End If

            C1Combo1.Text = s
            C1Combo1.SelectionStart = C1Combo1.Text.Length
            C1Combo1.SelectionLength = 0
        End If

    End Sub

    Private Sub C1Combo1_KeyDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles C1Combo1.KeyDown
        If e.KeyValue = 13 Then
            If C1Combo1.WillChangeToIndex < 1 Then
                If (CType(C1Combo1.DataSource, DataView).Count) = 0 Then
                    MsgBox("疾病: '" + Me.C1Combo1.Text + "' 不存在！", MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
                    System.Windows.Forms.SendKeys.Send("^{A}")
                    Exit Sub
                End If

                If C1Combo1.Text = "" Then
                    C1Combo1.SelectedIndex = -1
                Else
                    C1Combo1.SelectedIndex = 0
                    If Label17.Text = "" Then
                        V_Jb_Code = C1Combo1.SelectedValue & ""
                        Label17.Text = C1Combo1.Columns("Jb_Name").Value & ""
                        C1TextBox2.Text = C1Combo1.Columns("Jb_Name").Value & ""
                    Else
                        Label17.Text = Label17.Text & "," & C1Combo1.Columns("Jb_Name").Value & ""
                        C1TextBox2.Text = C1TextBox2.Text & "," & C1Combo1.Columns("Jb_Name").Value & ""
                    End If
                    C1Combo1.SelectedIndex = -1
                End If


            Else
                Dim index As Integer
                index = C1Combo1.WillChangeToIndex
                C1Combo1.SelectedIndex = -1
                C1Combo1.SelectedIndex = index
                If Label17.Text = "" Then
                    V_Jb_Code = C1Combo1.SelectedValue & ""
                    Label17.Text = C1Combo1.Columns("Jb_Name").Value & ""
                    C1TextBox2.Text = C1Combo1.Columns("Jb_Name").Value & ""
                Else
                    Label17.Text = Label17.Text & "," & C1Combo1.Columns("Jb_Name").Value & ""
                    C1TextBox2.Text = C1TextBox2.Text & "," & C1Combo1.Columns("Jb_Name").Value & ""
                End If
                C1Combo1.SelectedIndex = -1
            End If
            e.Handled = True
            System.Windows.Forms.SendKeys.Send("{Tab}")
        End If
    End Sub

    Private Sub C1Combo3_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles C1Combo3.KeyUp

        If (e.KeyValue > 47 And e.KeyValue < 58) Or (e.KeyValue > 96 And e.KeyValue < 123) Or (e.KeyValue > 64 And e.KeyValue < 91) Or (e.KeyValue = 8) Or (e.KeyValue = 189) Or (e.KeyValue = 190) Then
            Dim s As String = C1Combo3.Text
            If C1Combo3.Text = "" Then
                C1Combo3.DataSource.RowFilter = ""
            Else
                C1Combo3.DataSource.RowFilter = "Bc_Jc like '*" & C1Combo3.Text.Replace("*", "[*]") & "*'"
            End If
            If (e.KeyValue = 8) Then
                C1Combo3.DroppedDown = False
                C1Combo3.DroppedDown = True
            End If

            C1Combo3.Text = s
            C1Combo3.SelectionStart = C1Combo3.Text.Length
            C1Combo3.SelectionLength = 0
        End If

    End Sub

    Private Sub C1Combo3_KeyDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles C1Combo3.KeyDown
        If e.KeyValue = 13 Then
            If C1Combo3.WillChangeToIndex < 1 Then
                If (CType(C1Combo3.DataSource, DataView).Count) = 0 Then
                    MsgBox("床位: '" + Me.C1Combo3.Text + "' 不存在！", MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
                    System.Windows.Forms.SendKeys.Send("^{A}")
                    Exit Sub
                End If
                C1Combo3.SelectedIndex = -1
                C1Combo3.SelectedIndex = 0
            Else
                Dim index As Integer
                index = C1Combo3.WillChangeToIndex
                C1Combo3.SelectedIndex = -1
                C1Combo3.SelectedIndex = index
            End If
            e.Handled = True
            System.Windows.Forms.SendKeys.Send("{Tab}")
        End If
    End Sub

#Region "输入法设置"
    '中文
    Private Sub C1TextBox1_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1TextBox6.GotFocus, C1TextBox1.GotFocus, T_Combo.GotFocus, C1Combo2.GotFocus
        InputLanguage.CurrentInputLanguage = HisVar.HisVar.InputCode
    End Sub
    '英文
    Private Sub C1DateEdit1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1TextBox4.GotFocus, C1TextBox3.GotFocus, C1DateEdit2.GotFocus, C1DateEdit1.GotFocus, C1Combo1.GotFocus, C1Combo3.GotFocus, C1Combo4.GotFocus, C1Combo5.GotFocus, C1TextBox8.GotFocus, C1TextBox9.GotFocus
        InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages(0)
    End Sub
#End Region

    Private Sub C1Combo4_RowChange(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1Combo4.RowChange

        If C1Combo4.WillChangeToValue = "" Then

            C1Combo5.Text = ""
        Else

            C1Combo5.Text = C1Combo4.Columns("Ks_Name").Value
        End If
    End Sub

    Private Sub C1Combo4_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles C1Combo4.KeyUp

        If (e.KeyValue > 47 And e.KeyValue < 58) Or (e.KeyValue > 96 And e.KeyValue < 123) Or (e.KeyValue > 64 And e.KeyValue < 91) Or (e.KeyValue = 8) Or (e.KeyValue = 189) Or (e.KeyValue = 190) Then
            Dim s As String = C1Combo4.Text
            If C1Combo4.Text = "" Then
                C1Combo4.DataSource.RowFilter = ""
            Else
                C1Combo4.DataSource.RowFilter = "Ys_Jc like '*" & C1Combo4.Text.Replace("*", "[*]") & "*'"
            End If
            If (e.KeyValue = 8) Then
                C1Combo4.DroppedDown = False
                C1Combo4.DroppedDown = True
            End If

            C1Combo4.Text = s
            C1Combo4.SelectionStart = C1Combo4.Text.Length
            C1Combo4.SelectionLength = 0
        End If

    End Sub

    Private Sub C1Combo4_KeyDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles C1Combo4.KeyDown
        If e.KeyValue = 13 Then
            If C1Combo4.WillChangeToIndex < 1 Then
                If (CType(C1Combo4.DataSource, DataView).Count) = 0 Then
                    MsgBox("医生: '" + Me.C1Combo4.Text + "' 不存在！", MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
                    System.Windows.Forms.SendKeys.Send("^{A}")
                    Exit Sub
                End If
                C1Combo4.SelectedIndex = -1
                C1Combo4.SelectedIndex = 0
            Else
                Dim index As Integer
                index = C1Combo4.WillChangeToIndex
                C1Combo4.SelectedIndex = -1
                C1Combo4.SelectedIndex = index
            End If
            e.Handled = True
            System.Windows.Forms.SendKeys.Send("{Tab}")
        End If
    End Sub

    Private Sub T_Combo_SelectedValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles T_Combo.SelectedValueChanged
        If T_Combo.Text = "合作医疗" Or T_Combo.Text = "城乡居民" Then
            If HisPara.PublicConfig.ZyHzxg = "是" Then
                If HisPara.PublicConfig.Sfzqsfs = "1" Or HisPara.PublicConfig.Sfzqsfs = "3" Then
                    C1TextBox1.Enabled = False
                    C1TextBox1.BackColor = SystemColors.Info
                    C1DateEdit2.Enabled = False
                    C1DateEdit2.BackColor = SystemColors.Info
                    C1Combo2.Enabled = False
                    C1Combo2.EditorBackColor = SystemColors.Info
                    C1TextBox4.Enabled = False
                    C1TextBox4.BackColor = SystemColors.Info
                    C1TextBox6.Enabled = False
                    C1TextBox6.BackColor = SystemColors.Info
                    C1TextBox3.Enabled = False
                    C1TextBox3.BackColor = SystemColors.Info

                Else
                    If Rinsert = False Then
                        Dim Jf_Count As Integer = HisVar.HisVar.Sqldal.GetSingle("select count(Jf_Code) from  Bl_Jf where Bl_Code='" & Rrow.Item("Bl_Code") & "'")
                        Dim Cf_Count As Integer = HisVar.HisVar.Sqldal.GetSingle("select count(Cf_Code) from  Bl_Cf where Bl_Code='" & Rrow.Item("Bl_Code") & "'")
                        Dim Zh_Code As Integer = HisVar.HisVar.Sqldal.GetSingle("select count(*) from  Bl_Zh where Bl_Code='" & Rrow.Item("Bl_Code") & "' ")
                        If Jf_Count + Cf_Count > 0 And Zh_Code = 0 Then
                            C1TextBox1.Enabled = False
                            C1TextBox1.BackColor = SystemColors.Info
                            C1DateEdit2.Enabled = False
                            C1DateEdit2.BackColor = SystemColors.Info
                            C1Combo2.Enabled = False
                            C1Combo2.EditorBackColor = SystemColors.Info
                            C1TextBox6.Enabled = False
                            C1TextBox6.BackColor = SystemColors.Info
                            C1TextBox3.Enabled = False
                            C1TextBox3.BackColor = SystemColors.Info
                            C1TextBox4.Enabled = False
                            C1TextBox4.BackColor = SystemColors.Info

                        Else
                            C1TextBox1.Enabled = False
                            C1TextBox1.BackColor = SystemColors.Info  '姓名不允许修改       
                            C1DateEdit2.Enabled = True
                            C1DateEdit2.BackColor = SystemColors.Window
                            C1Combo2.Enabled = True
                            C1Combo2.EditorBackColor = SystemColors.Window
                            C1TextBox6.Enabled = True
                            C1TextBox6.BackColor = SystemColors.Window
                            C1TextBox3.Enabled = True
                            C1TextBox3.BackColor = SystemColors.Window
                            C1TextBox4.Enabled = False
                            C1TextBox4.BackColor = SystemColors.Info

                        End If
                    Else
                        C1TextBox4.Enabled = True
                        C1TextBox4.BackColor = Color.White

                    End If
                End If
            Else
                C1TextBox4.Enabled = True
                C1TextBox4.BackColor = Color.White

            End If


        Else

            C1TextBox1.Enabled = True
            C1TextBox1.BackColor = Color.White
            C1DateEdit2.Enabled = True
            C1DateEdit2.BackColor = Color.White
            C1Combo2.Enabled = True
            C1Combo2.EditorBackColor = Color.White
            C1TextBox6.Enabled = True
            C1TextBox6.BackColor = Color.White
            C1TextBox3.Enabled = True
            C1TextBox3.BackColor = Color.White
            C1TextBox4.Enabled = False
            C1TextBox4.BackColor = SystemColors.Info
            C1TextBox4.Text = ""


        End If

        If HisPara.PublicConfig.JzkXd = "是" Then
            C1TextBox3.Enabled = False
            C1TextBox3.BackColor = SystemColors.Info
            C1TextBox1.Enabled = False
            C1TextBox1.BackColor = SystemColors.Info
            C1DateEdit2.Enabled = False
            C1DateEdit2.BackColor = SystemColors.Info
            C1Combo2.Enabled = False
            C1Combo2.EditorBackColor = SystemColors.Info
            C1TextBox8.Enabled = False
            C1TextBox8.BackColor = SystemColors.Info
            C1TextBox6.Enabled = False
            C1TextBox6.BackColor = SystemColors.Info
        End If

    End Sub

    Private Sub Button2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button2.Click

        Load_Ok = False
        If HisPara.PublicConfig.Sfzqsfs = "1" Or HisPara.PublicConfig.Sfzqsfs = "3" Then
            Me.Cursor = Cursors.WaitCursor
'            Tb = ZtXnh.Get_NhRyJtXx()
'            Dim My_Combo6 As BaseClass.C_Combo2 = New BaseClass.C_Combo2(C1Combo6, Tb.DefaultView, "Ry_Name", "Ry_Code", 400)
'            With My_Combo6
'                .Init_TDBCombo()
'                .Init_Colum("Ry_Name", "人员姓名", 60, "左")
'                .Init_Colum("Ry_Sex", "性别", 50, "左")
'                .Init_Colum("Ry_Address", "家庭住址", 80, "左")
'                .Init_Colum("Ry_Sfzh", "身份证号", 120, "左")
'                .Init_Colum("Ry_Code", "参保编码", 75, "左")
'                .MaxDropDownItems(15)
'                .SelectedIndex(-1)
'
'            End With
'            C1Combo6.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
'            C1Combo6.Select()
'            C1Combo6.OpenCombo()
            Me.Cursor = Cursors.Default

        Else
            If C1TextBox3.Text & "" = "" Then
                Dim Ryxx(4) As String
                If HisPara.PublicConfig.XqName.Contains("丰润") Then
                    Ryxx = BaseClass.PtCpIDMR02TG.Dkq_RyXx
                Else
                    Ryxx = BaseClass.XzxDkq116D.Dkq_RyXx
                End If
                If Ryxx(0) = "读取身份证信息失败" Or Ryxx(0) = "打开端口失败" Then
                    MsgBox(Ryxx(0), MsgBoxStyle.Information, "提示:")
                    Exit Sub
                Else
                    C1TextBox1.Text = Ryxx(0)   '人员姓名
                    My_Cc.Get_Py(Me.C1TextBox1.Text & "")
                    L_Dl_Jc.Text = My_Cc.简拚.ToString
                    C1Combo2.Text = Ryxx(1)      '人员性别
                    C1TextBox3.Text = Ryxx(4)
                    C1DateEdit2.Value = CDate(Mid(Ryxx(4), 7, 4) & "-" & Mid(Ryxx(4), 11, 2) & "-" & Mid(Ryxx(4), 13, 2))
                    C1TextBox6.Text = Ryxx(3)
                End If
            Else
            End If
        End If
        Load_Ok = True
   End Sub

    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click
        If C1TextBox5.Text & "" = "" Then
      
        Else
            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "SELECT Ry_Name,Ry_Jc,Ry_Sex,Ry_Sfzh,datediff(year,Ry_CsDate,getdate())+1 as Ry_Age,Ry_Address,Ry_Tele,Ry_Memo FROM Zd_Ry where Kh='" & C1TextBox5.Text & "' ", "就诊卡信息", True)
            If My_Dataset.Tables("就诊卡信息").Rows.Count = 1 Then
                C1TextBox1.Text = My_Dataset.Tables("就诊卡信息").Rows(0).Item("Ry_Name")
                L_Dl_Jc.Text = My_Dataset.Tables("就诊卡信息").Rows(0).Item("Ry_Jc")
                C1Combo2.Text = My_Dataset.Tables("就诊卡信息").Rows(0).Item("Ry_Sex")
                C1TextBox6.Text = My_Dataset.Tables("就诊卡信息").Rows(0).Item("Ry_Address")
                C1TextBox3.Text = My_Dataset.Tables("就诊卡信息").Rows(0).Item("Ry_Sfzh")
            Else
                MsgBox("读取信息有误，请重试！", vbInformation + vbOKOnly + vbDefaultButton1, "提示:")
                C1TextBox4.Select()
                Exit Sub
            End If
        End If
    End Sub

    Private Sub C1Command1_Click(ByVal sender As System.Object, ByVal e As C1.Win.C1Command.ClickEventArgs) Handles C1Command1.Click, C1Command2.Click, C1Command3.Click

        Select Case sender.text
            Case "增加疾病"
                If C1Combo1.SelectedValue = "" Then
                    MsgBox("请选择要增加的疾病！", MsgBoxStyle.Information, "提示：")
                    Exit Sub
                End If
                If Label17.Text = "" Then
                    V_Jb_Code = C1Combo1.SelectedValue & ""
                    Label17.Text = C1Combo1.Columns("Jb_Name").Value & ""
                    C1TextBox2.Text = C1Combo1.Columns("Jb_Name").Value & ""
                Else
                    Label17.Text = Label17.Text & "," & C1Combo1.Columns("Jb_Name").Value & ""
                    C1TextBox2.Text = C1TextBox2.Text & "," & C1Combo1.Columns("Jb_Name").Value & ""
                End If


            Case "撤销"
                Dim I As Integer = Label17.Text.LastIndexOf(",")
                If I = -1 Then
                    V_Jb_Code = ""
                    Label17.Text = ""
                    C1TextBox2.Text = ""
                Else
                    Label17.Text = Mid(Label17.Text, 1, I)
                End If


            Case "清除所有疾病"
                V_Jb_Code = ""
                Label17.Text = ""
                C1TextBox2.Text = ""
        End Select
        C1Combo1.SelectedIndex = -1
        C1Combo1.Select()
    End Sub


    Private Sub Button5_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button5.Click
        If C1TextBox3.Text = "" Or C1TextBox1.Text = "" Then Exit Sub
        Dim V_RyBlCode As String = ""
        V_RyBlCode = HisVar.HisVar.Sqldal.GetSingle("Select Top 1 Ry_BlCode from Bl where Ry_Sfzh='" & C1TextBox3.Text & "' and Ry_Name='" & C1TextBox1.Text & "' Order by Bl_Code desc ") & ""
        If V_RyBlCode <> "" Then
            C1TextBox9.Text = V_RyBlCode
        Else

        End If

    End Sub

    Private Sub Button7_Click(sender As Object, e As EventArgs) Handles Button7.Click
        DataSearch()
    End Sub
End Class