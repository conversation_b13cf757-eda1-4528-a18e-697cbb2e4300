﻿Imports System.Data.SqlClient

Public Class C_Cc   '自定义类

    Private My_Code As String                           '定义1个私用字段----最大编码
    Private My_Py As String                             '定义1个私用字段----拚音简称

    Private P_GetCode As New SqlClient.SqlCommand
    Private P_GetPy As New SqlClient.SqlCommand
    Private My_Cn As New SqlConnection
    Protected Shared __connectionString As String

    Public Shared WriteOnly Property ConnectionString() As String
        Set(ByVal value As String)
            __connectionString = value
        End Set
    End Property

    Public Sub New() ' 定义一个公用构造函数来初始化字段
        With P_GetCode
            My_Cn.ConnectionString = __connectionString
            .CommandType = CommandType.StoredProcedure
            .Connection = My_Cn
            .CommandText = "P_GetCode"
            .Parameters.Add("@TableName", SqlDbType.VarChar, 100)
            .Parameters.Add("@KeyName", SqlDbType.VarChar, 50)
            .Parameters.Add("@Length", SqlDbType.Int)
            .Parameters.Add("@Where_Key", SqlDbType.VarChar, 100)
            .Parameters.Add("@Where_Code", SqlDbType.VarChar, 100)
            .Parameters.Add("@MaxCode", SqlDbType.VarChar, 100)
            .Parameters("@MaxCode").Direction = ParameterDirection.Output
        End With

        With P_GetPy
            .CommandType = CommandType.StoredProcedure
            .Connection = My_Cn
            .CommandText = "P_GetPy"
            .Parameters.Add("@V_Hz", SqlDbType.VarChar, 100)
            .Parameters.Add("@V_Py", SqlDbType.VarChar, 100)
            .Parameters("@V_Py").Direction = ParameterDirection.Output
        End With

    End Sub

#Region "最大编码"

    ' 定义一个公用方法
    Public Sub Get_MaxCode(ByVal V_TableName As String, ByVal V_KeyName As String, ByVal V_Length As Integer, ByVal V_Where_Key As String, ByVal V_Where_Code As String)
        With P_GetCode
            .Parameters("@TableName").Value = Trim(V_TableName)
            .Parameters("@KeyName").Value = Trim(V_KeyName)
            .Parameters("@Length").Value = V_Length
            .Parameters("@Where_Key").Value = Trim(V_Where_Key)
            .Parameters("@Where_Code").Value = Trim(V_Where_Code)
        End With

        My_Cn.Open()
        P_GetCode.ExecuteNonQuery()
        My_Code = P_GetCode.Parameters("@MaxCode").Value
        My_Cn.Close()
    End Sub

    Public ReadOnly Property 编码() As String    ' 定义一个只读属性---最大编码
        Get
            Return My_Code
        End Get
    End Property
#End Region

#Region "汉语拚音"

    ' 定义一个公用方法
    Public Sub Get_Py(ByVal V_Hz As String)
        With P_GetPy
            .Parameters("@V_Hz").Value = Trim(V_Hz)
        End With
        My_Cn.Open()
        P_GetPy.ExecuteNonQuery()
        My_Py = P_GetPy.Parameters("@V_Py").Value
        My_Cn.Close()
    End Sub

    Public ReadOnly Property 简拚() As String    ' 定义一个只读属性---最大编码
        Get
            Return My_Py
        End Get
    End Property
#End Region

End Class
