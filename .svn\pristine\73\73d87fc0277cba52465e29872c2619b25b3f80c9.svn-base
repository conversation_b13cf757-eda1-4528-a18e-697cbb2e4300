﻿Imports System.Data.SqlClient
Imports Stimulsoft.Report
Imports ZTHisPublicFunction
Imports BLL
Imports Model
Imports ZTHisEnum
Imports DTO.Invoice

Public Class Xs_Zy_Cy1

#Region "定义__变量"
    Public My_Adapter As New SqlDataAdapter                '从表适配器
    Dim V_Sum As Double                                 '金额小计
    Dim Zb_Sum As Double
    Dim Cb_Row As DataRow                            '当前选择行
    Dim bllBl_Cfyp As New BllBl_Cfyp()
    Dim bllBl_Cfxm As New BllBl_Cfxm()
    Dim reuslt As String
    Dim _mdlBl As New MdlBl()
    Public V_Bl_Code As String
    Public My_Dataset As New DataSet

    Dim Sendtag As String

#End Region

    Private Sub Xs_Zy_Cy1_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        If HisPara.PublicConfig.ZyHsz = "是" Then
            DateTimePicker1.Enabled = False
        Else
            DateTimePicker1.Enabled = True
        End If
        Me.DateTimePicker1.Value = Now
        Call Data_Init(DateTimePicker1.Value)
        Call Form_Init()
    End Sub

    Protected Overrides Sub OnKeyDown(ByVal e As System.Windows.Forms.KeyEventArgs)
        MyBase.OnKeyDown(e)
        If e.Control = True And e.KeyCode = Keys.P Then
            Call Comm_Click(Comm3, Nothing)
        End If
    End Sub

#Region "窗体__事件"

    Private Sub Data_Init(ByVal V_Date As Date)
        '病人出院
        Dim V_Str As String = ""
        Dim Sql As String = ""

        If HisPara.PublicConfig.ZyHsz = "是" Then
            V_Str = " and Cy_Qr='是'"
        End If

        Sql = "Select Bl.Bl_Code,Ry_Jc,Ry_Name,Ry_Sex,Ry_Sfzh,Ry_RyDate,Ks_Name,Ry_BlCode,Isnull(Jf_Money,0)Jf_Money, Ry_ZyTs,Ry_CyDate,Bl_M_Th,Bxlb_Name,Ry_YlCode FROM Zd_YyKs,Zd_Bxlb,Bl Left Join (Select Sum(Jf_Money) AS Jf_Money,Bl_Code From Bl_Jf Group By Bl_Code) B On Bl.Bl_Code = B.Bl_Code Where Bl.Ks_Code=Zd_YyKs.Ks_Code and Bl.Bxlb_Code=Zd_Bxlb.Bxlb_Code  And Isnull(Ry_CyJsr,'')=''  " & V_Str

        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, Sql, "病人出院", True)
        My_Dataset.Tables("病人出院").PrimaryKey = New DataColumn() {My_Dataset.Tables("病人出院").Columns("Bl_Code")}
    End Sub

    Private Sub Form_Init()
        Panel2.Height = 30

        '按扭初始化
        If HisPara.PublicConfig.ZyHsz = "是" Then
            Comm4.Visible = True
        Else
            Comm4.Visible = False
        End If
        Call P_Comm(Me.Comm1)
        Call P_Comm(Me.Comm2)
        Call P_Comm(Me.Comm3)
        Call P_Comm(Me.Comm4)

        With BrCyComobo
            .DataView = My_Dataset.Tables("病人出院").DefaultView
            .Init_Colum("Ry_Jc", "简称", 60, "左")
            .Init_Colum("Ry_Name", "姓名", 80, "左")
            .Init_Colum("Jf_Money", "0", 0, "右")
            .Init_Colum("Ry_Sex", "性别", 40, "中")
            .Init_Colum("Ry_Sfzh", "身份证号", 100, "中")
            .Init_Colum("Bl_Code", "", 0, "左")
            .Init_Colum("Ry_RyDate", "入院日期", 70, "中")
            .Init_Colum("Ks_Name", "科室", 70, "左")
            .Init_Colum("Ry_BlCode", "住院号", 0, "中")
            .Init_Colum("Ry_CyDate", "出院日期", 0, "中")
            .Init_Colum("Ry_ZyTs", "住院天数", 0, "中")
            .Init_Colum("Bl_M_Th", "退回金额", 0, "中")
            .Init_Colum("Bxlb_Name", "患者类别", 0, "中")
            .Init_Colum("Ry_YlCode", "医疗证号", 0, "中")
            .DisplayMember = "Ry_Name"
            .ValueMember = "Bl_Code"
            .RowFilterTextNull = ""
            .RowFilterNotTextNull = "Ry_Jc" '过滤字段
            .DroupDownWidth = 430
            .MaxDropDownItems = 17
            .SelectedIndex = -1
        End With
        BrCyComobo.Columns("Ry_RyDate").NumberFormat = "yyyy-MM-dd"


        'Dim My_Combo3 As New BaseClass.C_Combo2(Me.C1Combo3, My_Dataset.Tables("病人出院").DefaultView, "Ry_Jc", "Bl_Code", 450)
        'With My_Combo3
        '    .Init_TDBCombo()
        '    .Init_Colum("Ry_Jc", "简称", 60, "左")
        '    .Init_Colum("Ry_Name", "姓名", 80, "左")
        '    .Init_Colum("Jf_Money", "0", 0, "右")
        '    .Init_Colum("Ry_Sex", "性别", 40, "中")
        '    .Init_Colum("Ry_Sfzh", "身份证号", 100, "中")
        '    .Init_Colum("Bl_Code", "", 0, "左")
        '    .Init_Colum("Ry_RyDate", "入院日期", 70, "中")
        '    .Init_Colum("Ks_Name", "科室", 70, "左")
        '    .Init_Colum("Ry_BlCode", "住院号", 0, "中")
        '    .Init_Colum("Ry_CyDate", "出院日期", 0, "中")
        '    .Init_Colum("Ry_ZyTs", "住院天数", 0, "中")
        '    .Init_Colum("Bl_M_Th", "退回金额", 0, "中")
        '    .Init_Colum("Bxlb_Name", "患者类别", 0, "中")
        '    .Init_Colum("Ry_YlCode", "医疗证号", 0, "中")
        '    .MaxDropDownItems(17)
        '    .SelectedIndex(-1)
        'End With
        'C1Combo3.Columns("Ry_RyDate").NumberFormat = "yyyy-MM-dd"

        Dim My_Grid As New BaseClass.C_Grid(Me.C1TrueDBGrid1)
        With My_Grid
            .Init_Grid()
            .AllSort(False)
            .AllDelete(False)
            .P_Dock(BaseClass.C_Grid.Dock.Fill)
            .Init_Column("类别", "Cf_Lb", 180, "左", "")
            .Init_Column("数量", "Cf_Sl", 120, "右", "###,###,##0")
            .Init_Column("金额", "Cf_Money", 70, "右", "###,###,##0.00")
        End With

    End Sub

#End Region

#Region "用药__显示"

    Private Sub P_Data_Show(ByVal Bl_Code As String)   '从表数据

        Dim Str As String = "Select Sum(Cf_Sl) as Cf_Sl,Sum(Cf_Money) as Cf_Money,Cf_Lb,Bl_Code from " & _
                               "(SELECT Cf_Sl, Bl_Cfxm.Cf_Money,Zd_JkFl1.Lb_Name AS Cf_Lb,Bl_Cf.Bl_Code from Zd_JkFl2,Zd_JkFl1,Bl_Cf,Bl_Cfxm Where Zd_JkFl2.Lb_Code = Zd_JkFl1.Lb_Code and Bl_Cf.Cf_Code = Bl_Cfxm.Cf_Code and Zd_JkFl2.Mx_Code = Bl_Cfxm.Xm_Code AND  Zd_JkFl2.Yy_Code = Bl_Cfxm.Yy_Code and Bl_Code='" & Bl_Code & "' And Cf_Qr='是'" & _
                              "  Union all SELECT Cf_Sl,Bl_Cfyp.Cf_Money as Cf_Money,Case Cf_Lb when '西药' then '西药费' else Cf_Lb end as Cf_Lb,Bl_Code FROM Bl_Cf INNER JOIN Bl_Cfyp ON Bl_Cf.Cf_Code = dbo.Bl_Cfyp.Cf_Code Where Bl_Code='" & Bl_Code & "' And Cf_Qr='是') a Group By Cf_Lb,Bl_Code"
        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, Str, "明细表", True)
        C1TrueDBGrid1.SetDataBinding(My_Dataset, "明细表", True)

        C1TextBox18.Text = Format(IIf(My_Dataset.Tables("明细表").Compute("Sum(Cf_Money)", "") Is DBNull.Value, 0, My_Dataset.Tables("明细表").Compute("Sum(Cf_Money)", "")), "0.00")
        C1TextBox19.Text = Format(IIf(Label10.Text = "", 0, Label10.Text) - C1TextBox18.Text, "0.00")
        If C1TextBox19.Text < 0 Then
            C1TextBox19.ForeColor = Color.Red
        Else
            C1TextBox19.ForeColor = Color.Black
        End If

    End Sub

#End Region

#Region "控件__动作"

#Region "其它__控件"

    Private Sub Comm_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.Click, Comm2.Click, Comm3.Click, Comm4.Click

        Sendtag = sender.tag

        Select Case sender.tag
            Case "取消"
                Me.Close()
            Case "拒绝出院"
                If BrCyComobo.SelectedValue = "" Then
                    Beep()
                    MsgBox("出院病人不能为空!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    BrCyComobo.Select()
                    Exit Sub
                End If

                Dim Str_Update1 As String = "Update Bl Set Cy_Qr='否',Ry_CyDate=Null,Ry_ZyTs=null where Bl.Bl_Code='" & BrCyComobo.Columns("Bl_Code").Value & "'"
                HisVar.HisVar.Sqldal.ExecuteSql(Str_Update1)
                Call Data_Init(DateTimePicker1.Value)
                BrCyComobo.SelectedIndex = -1
                If My_Dataset.Tables("明细表") IsNot Nothing Then My_Dataset.Tables("明细表").Clear()
                MsgBox("拒绝出院成功", MsgBoxStyle.Information + MsgBoxStyle.OkOnly, "提示")
            Case "数据结算", "快速打印"

                If BrCyComobo.SelectedValue = "" Then
                    Beep()
                    MsgBox("出院病人不能为空!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    BrCyComobo.Select()
                    Exit Sub
                End If

                If HisPara.PublicConfig.Qfcy <> "是" And C1TextBox19.Text < 0 Then
                    Beep()
                    MsgBox("出院病人押金不足!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    BrCyComobo.SelectedIndex = -1
                    Me.Close()
                    Exit Sub
                End If

                If Format(DateTimePicker1.Value, "yyyy-MM-dd") > Format(Now, "yyyy-MM-dd") Then
                    MsgBox("出院日期不能大于当前日期", MsgBoxStyle.Information, "提示")
                    Exit Sub
                End If

                If HisVar.HisVar.Sqldal.GetSingle("select Count(*) from Bl_Cf where Cf_Date+Cf_Time>'" & Format(DateTimePicker1.Value, "yyyy-MM-dd HH:mm:ss") & "' and Bl_Code='" & BrCyComobo.Columns("Bl_Code").Value & "'") > 0 Then
                    MsgBox("该患者存在晚于当前时间的处方，不能办理出院！", MsgBoxStyle.Information, "提示")
                    Exit Sub
                End If
                If ZTHisPara.PublicConfig.CheckBedFee = True Then
                    If InpatFunc.ChekBedFee(Label11.Text, BrCyComobo.Columns("Bl_Code").Value) = False Then
                        Exit Sub
                    End If
                End If
                Dim m_Str As String = ""

                HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "select distinct Bl_Cfxm.Xm_Code,Xm_Name From Bl_Cfxm,Bl_Cf,Zd_Ml_Xm3 where Bl_Cfxm.Xm_Code=Zd_Ml_Xm3.Xm_Code And Bl_Cfxm.Cf_Code=Bl_Cf.Cf_Code And not exists (Select Mx_Code From zd_Jkfl2 where zd_Jkfl2.Mx_Code=Bl_Cfxm.Xm_Code) and Bl_Code='" & BrCyComobo.Columns("Bl_Code").Value & "'", "未维护项目", True)

                For Each m_row In My_Dataset.Tables("未维护项目").Rows
                    m_Str = m_Str & vbCrLf & m_row("Xm_Name")
                Next

                If m_Str <> "" Then
                    m_Str = m_Str & vbCrLf
                    MsgBox("诊疗项目:" & m_Str & "未维护到住院发票类别,请先维护才能办理出院!", MsgBoxStyle.Critical, "提示")
                    BrCyComobo.SelectedIndex = -1
                    Exit Sub
                End If

                HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "Select Cf_Date  from Bl_Cf where Bl_Code='" & BrCyComobo.Columns("Bl_Code").Value & "' and (Cf_Qr='否' or Cf_Print='否')", "未完成处方", True)
                For Each m_row In My_Dataset.Tables("未完成处方").Rows
                    m_Str = m_Str & vbCrLf & Format(m_row("Cf_Date"), "yyyy-MM-dd")
                Next

                If m_Str <> "" Then
                    m_Str = m_Str & vbCrLf
                    MsgBox("存在未完成处方:" & m_Str, MsgBoxStyle.Critical, "提示")
                    BrCyComobo.SelectedIndex = -1
                    Exit Sub
                End If


                If HisPara.PublicConfig.ZyFpStyle = 0 Then  '老板发票
                    If HisPara.PublicConfig.XqName.Contains("长春") Or HisPara.PublicConfig.XqName.Contains("长春市") Or HisPara.PublicConfig.XqName.Contains("辽源市区") Or HisPara.PublicConfig.XqName.Contains("龙山区") Or HisPara.PublicConfig.XqName.Contains("西安区") Or HisPara.PublicConfig.XqName.Contains("东丰县") Or HisPara.PublicConfig.XqName.Contains("东辽县") Then
                        Call Cc_Fp(sender.text)
                    ElseIf HisPara.PublicConfig.XqName.Contains("通辽") Then
                        Call Tl_Fp(sender.text)
                    ElseIf HisPara.PublicConfig.XqName.Contains("连山区") Or HisPara.PublicConfig.XqName.Contains("龙港区") Or HisPara.PublicConfig.XqName.Contains("南票区") Or HisPara.PublicConfig.XqName.Contains("杨家杖子经济开发区") Or HisPara.PublicConfig.XqName.Contains("建昌县") Or HisPara.PublicConfig.XqName.Contains("兴城市") Then
                        Call Hld_Fp_Lb(sender.text)
                    End If

                ElseIf HisPara.PublicConfig.ZyFpStyle = 1 Then '2013新发票
                    If HisPara.PublicConfig.XqName.Contains("连山区") Or HisPara.PublicConfig.XqName.Contains("龙港区") Or HisPara.PublicConfig.XqName.Contains("南票区") Or HisPara.PublicConfig.XqName.Contains("杨家杖子经济开发区") Or HisPara.PublicConfig.XqName.Contains("建昌县") Or HisPara.PublicConfig.XqName.Contains("兴城市") Then
                        Call Hld_Fp(sender.text)
                    ElseIf HisPara.PublicConfig.XqName.Contains("通辽") Then
                        Call Tl_Fp(sender.text)
                    End If
                Else
                    Call Tl_Fp(sender.text)
                End If
        End Select

    End Sub

#End Region

#Region "Combo3动作"


    Private Sub BrCyComobo_RowChange(ByVal sender As Object, ByVal e As System.EventArgs) Handles BrCyComobo.RowChange
        If Me.Visible = False Then
            Label7.Text = ""
            Label1.Text = ""
            Label3.Text = ""
            Label10.Text = "0"
            Label11.Text = ""
            Exit Sub
        End If

        If BrCyComobo.WillChangeToValue = "" Then
            Label7.Text = ""
            Label1.Text = ""
            Label3.Text = ""
            Label10.Text = ""
            Label11.Text = ""
            C1TextBox18.Text = ""
            C1TextBox19.Text = ""
            If My_Dataset.Tables("明细表") IsNot Nothing Then My_Dataset.Tables("明细表").Clear()
        Else
            Label7.Text = Trim(BrCyComobo.Columns("Ry_Name").Value)
            Label10.Text = Trim(BrCyComobo.Columns("Jf_Money").Value)
            Label1.Text = Trim(BrCyComobo.Columns("Ry_Sex").Value)
            Label3.Text = Trim(BrCyComobo.Columns("Ry_Sfzh").Value + "")

            If HisPara.PublicConfig.ZyHsz = "是" Then
                DateTimePicker1.Value = Trim(BrCyComobo.Columns("Ry_Cydate").Value)
            End If

            Label11.Text = ZTHisPublicFunction.InpatFunc.CalculateHospitalDays(Convert.ToDateTime(BrCyComobo.Columns("Ry_RyDate").Value), Convert.ToDateTime(DateTimePicker1.Value))

            P_Data_Show(BrCyComobo.Columns("Bl_Code").Value)
        End If
    End Sub

    Private Sub BrCyComobo_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles BrCyComobo.KeyPress
        Select Case e.KeyChar
            Case Chr(Keys.Enter)
                e.Handled = True
                Call BrCyComobo_Validating("Enter", Nothing)
            Case Chr(Keys.Escape)
                e.Handled = True
                Call BrCyComobo_Validating("Escape", Nothing)
        End Select
    End Sub

    Private Sub BrCyComobo_Validating(ByVal sender As Object, ByVal e As System.ComponentModel.CancelEventArgs) Handles BrCyComobo.Validating
        Dim V_Code As String = Me.BrCyComobo.WillChangeToValue

        Select Case sender.ToString
            Case "Enter"
                If V_Code = "" And BrCyComobo.Text <> "" And My_Dataset.Tables("病人出院").Rows.Count <> 0 Then         '客户编码不存在,但有数据输入
                    Beep()
                    MsgBox("病人简称: '" + Me.BrCyComobo.Text + "' 不存在！", MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
                    System.Windows.Forms.SendKeys.Send("^{A}")
                    BrCyComobo.Select()
                Else
                    BrCyComobo.Select()
                    P_Data_Show(BrCyComobo.Columns("Bl_Code").Value)
                End If
            Case "Escape"
                If V_Code = "" Then                                 '客户简称不存在
                    If V_Bl_Code = "" Then
                        If BrCyComobo.Text <> "" Then
                            BrCyComobo.Text = ""
                            Label7.Text = ""
                        Else
                            Call Comm_Click(Comm2, Nothing)         '调用取消按键
                        End If
                    Else
                        BrCyComobo.SelectedValue = V_Bl_Code          '恢复到原来的状态
                    End If
                Else                                                '客户简称存在
                    If V_Code = V_Bl_Code Then                      '客户简称没有发生变化    
                        Call Comm_Click(Comm2, Nothing)             '调用取消按键
                    Else
                        BrCyComobo.SelectedValue = V_Bl_Code           '恢复到原来的状态
                    End If
                End If
            Case Else
                If V_Code = "" And Me.BrCyComobo.Text <> "" And My_Dataset.Tables("病人出院").Rows.Count <> 0 Then      '处理Tab及其它键---输入简称但简称不存在
                    e.Cancel = True
                    MsgBox("病人简称: '" + Me.BrCyComobo.Text + "' 不存在！", MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
                    System.Windows.Forms.SendKeys.Send("^{A}")
                    BrCyComobo.Select()
                Else
                    P_Data_Show(BrCyComobo.Columns("Bl_Code").Value)
                End If
        End Select

    End Sub

#End Region

#End Region

#Region "自定义函数"
    
    Private Sub Tl_Fp(ByVal tag As String)

        V_Bl_Code = BrCyComobo.Columns("Bl_Code").Value



        If MsgBox("是否确认此患者出院？", vbQuestion + vbYesNo, "提示:") = vbNo Then Exit Sub
        P_Data_Show(V_Bl_Code)

        HisVar.HisVar.Sqldal.ExecuteSql("Update Bl Set Bl_M_Sk=" & C1TextBox18.Text & ",Bl_M_Yj=" & Label10.Text & ",Bl_M_Th=" & C1TextBox19.Text & " Where Bl_Code='" & BrCyComobo.Columns("Bl_Code").Value & "'")
        Dim StiRpt As New StiReport

        Dim outInvoicePara_NeiMengGu As New InvoicePara()
        outInvoicePara_NeiMengGu.患者姓名 = Label7.Text
        outInvoicePara_NeiMengGu.性别 = Label1.Text
        outInvoicePara_NeiMengGu.病人ID = BrCyComobo.Columns("Bl_Code").Value
        outInvoicePara_NeiMengGu.住院号 = BrCyComobo.Columns("Ry_BlCode").Value
        outInvoicePara_NeiMengGu.科室 = BrCyComobo.Columns("Ks_Name").Value
        outInvoicePara_NeiMengGu.入院日期 = Format(BrCyComobo.Columns("Ry_RyDate").Value, "yyyy-MM-dd")
        outInvoicePara_NeiMengGu.出院日期 = DateTimePicker1.Text
        outInvoicePara_NeiMengGu.住院天数 = Label11.Text
        outInvoicePara_NeiMengGu.预交金总额 = Label10.Text
        outInvoicePara_NeiMengGu.结算总额 = C1TextBox18.Text
        outInvoicePara_NeiMengGu.应退额 = C1TextBox19.Text
        outInvoicePara_NeiMengGu.患者类别 = BrCyComobo.Columns("Bxlb_Name").Value & ""

        'If HisPara.PublicConfig.ZyFpStyle = 0 Then
        '    StiRpt = PrintOutInvoice_NeiMengGu.Print(outInvoicePara_NeiMengGu, listOutInvoiceItem_NeiMengGuYp, listOutInvoiceItem_NeiMengGuXm, reuslt)
        'ElseIf HisPara.PublicConfig.ZyFpStyle = 1 Then
        '    StiRpt = PrintOutInvoice_NeiMengGu.Print2(outInvoicePara_NeiMengGu, listOutInvoiceItem_NeiMengGuYp, listOutInvoiceItem_NeiMengGuXm, reuslt)
        'End If
        Dim zyfp As ZTHisPublicFunction.InpatientInvoice.IInpatientInvoice
        zyfp = ZTHisPublicFunction.InpatientInvoice.InpatientInvoiceFactory.CreateInpatientInvoiceObject()
        StiRpt = zyfp.GetInpatientInvoice(outInvoicePara_NeiMengGu, Nothing, Nothing, reuslt)
       
        If reuslt<>"OK" then
            MessageBox.Show(reuslt,"提示",MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return
        End If

        Dim My_Reader As SqlDataReader
        Dim My_Row As DataRow = My_Dataset.Tables("病人出院").Rows.Find(BrCyComobo.Columns("Bl_Code").Value)

        With My_Row
            .BeginEdit()
            .Item("Ry_CyDate") = DateTimePicker1.Value
            .Item("Ry_ZyTs") = Label11.Text
            .Item("Bl_M_Th") = C1TextBox19.Text
            .EndEdit()
        End With
        Dim vform As New PublicForm.PrintForm(Me.Name, "出院打印", Nothing, My_Row)
        StiRpt.Render()
        vform.StiViewerControl1.Report = StiRpt

        Select Case Sendtag

            Case "数据结算"


                vform.ShowDialog()
            Case "快速打印"
                '写入出院时间
                If HisPara.PublicConfig.ZyHsz = "否" Then
                    HisVar.HisVar.Sqldal.ExecuteSql("Update Bl Set Ry_CyDate='" & Format(DateTimePicker1.Value, "yyyy-MM-dd HH:mm:ss") & "',Ry_CyJsr='" & HisVar.HisVar.JsrCode & "',Ry_ZyTs='" & Label11.Text & "' Where  Bl_Code='" & BrCyComobo.Columns("Bl_Code").Value & "'")

                Else
                    HisVar.HisVar.Sqldal.ExecuteSql("Update Bl Set Ry_CyJsr='" & HisVar.HisVar.JsrCode & "' Where Yy_Code='" & HisVar.HisVar.WsyCode & "' and Bl_Code='" & BrCyComobo.Columns("Bl_Code").Value & "'")

                End If                    '当是被召回的病人出院时，需要再插入一条出院信息
                My_Reader = HisVar.HisVar.Sqldal.ExecuteReader("Select  * From Bl_Zh Where Yy_Code='" & HisVar.HisVar.WsyCode & "' and Bl_Code='" & BrCyComobo.Columns("Bl_Code").Value & "' And Cy_Lb='召回' order by Cy_Id desc")
                My_Reader.Read()  '取最近一次召回
                If My_Reader.HasRows = True Then
                    Dim Th As String
                    Th = My_Reader.Item("Bl_M_Th") + C1TextBox19.Text
                    My_Reader.Close()
                    If HisPara.PublicConfig.ZyHsz = "否" Then
                        HisVar.HisVar.Sqldal.ExecuteSql("Insert Into Bl_Zh(Yy_Code,Bl_Code,Cy_Date,Cy_Time,Jsr_Code,Cy_Lb,Bl_M_Th) Values('" & HisVar.HisVar.WsyCode & "','" & BrCyComobo.Columns("Bl_Code").Value & "','" & Format(DateTimePicker1.Value, "yyyy-MM-dd") & "','" & Format(TimeOfDay, "HH:mm:ss") & "','" & HisVar.HisVar.JsrCode & "','出院'," & Th * -1 & ")")

                    Else
                        HisVar.HisVar.Sqldal.ExecuteSql("Insert Into Bl_Zh(Yy_Code,Bl_Code,Cy_Date,Cy_Time,Jsr_Code,Cy_Lb,Bl_M_Th) Values('" & HisVar.HisVar.WsyCode & "','" & BrCyComobo.Columns("Bl_Code").Value & "','" & Format(BrCyComobo.Columns("Ry_CyDate").Value, "yyyy-MM-dd") & "','" & Format(BrCyComobo.Columns("Ry_CyDate").Value, "HH:mm:ss") & "','" & HisVar.HisVar.JsrCode & "','出院'," & Th * -1 & ")")

                    End If
                End If
                My_Reader.Close()
                Call P_Conn(False)
                HisVar.HisVar.Sqldal.ExecuteSql("Update Auto_Cf set Auto_Zt='停止',End_Date='" & Format(Now, "yyyy-MM-dd HH:mm") & "'  where Bl_Code='" & BrCyComobo.Columns("Bl_Code").Value & "' and Auto_Zt<>'停止'")
                vform.StiViewerControl1.Report.Print()
        End Select

        Me.Close()
    End Sub

    Private Sub Cc_Fp(ByVal tag As String)



        V_Bl_Code = BrCyComobo.Columns("Bl_Code").Value

        If MsgBox("是否确认此患者出院？", vbQuestion + vbYesNo, "提示:") = vbNo Then Exit Sub
        P_Data_Show(V_Bl_Code)

        HisVar.HisVar.Sqldal.ExecuteSql("Update Bl Set Bl_M_Sk=" & C1TextBox18.Text & ",Bl_M_Yj=" & Label10.Text & ",Bl_M_Th=" & C1TextBox19.Text & " Where Bl_Code='" & BrCyComobo.Columns("Bl_Code").Value & "'")

        Dim Zy_top As Double = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\中软智通", "住院上边距", Nothing)
        Dim Zy_left As Double = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\中软智通", "住院左边距", Nothing)

        Dim StiRpt As New StiReport

        StiRpt.Load(".\Rpt\吉林省医疗机构住院收费专用票据.mrt")
        StiRpt.ReportName = "吉林省医疗机构住院收费专用票据"

        StiRpt.Pages(0).Margins.Top = Zy_top
        StiRpt.Pages(0).Margins.Bottom = Zy_left
        StiRpt.PrinterSettings.PrinterName = IIf(iniOperate.iniopreate.GetINI("打印机设置", "住院打印机", "", HisVar.HisVar.Parapath & "\Config.ini") & "" = "", StiRpt.PrinterSettings.PrinterName, iniOperate.iniopreate.GetINI("打印机设置", "住院打印机", "", HisVar.HisVar.Parapath & "\Config.ini"))
        StiRpt.Compile()
        StiRpt("医院名称") = ZTHisVar.Var.HosName
        StiRpt("经手人") = HisVar.HisVar.JsrName
        StiRpt("姓名") = Label7.Text
        StiRpt("住院编码") = BrCyComobo.Columns("Bl_Code").Value
        StiRpt("住院号") = BrCyComobo.Columns("Ry_BlCode").Value
        StiRpt("科室") = BrCyComobo.Columns("Ks_Name").Value
        StiRpt("入院日期") = Format(BrCyComobo.Columns("Ry_RyDate").Value, "yyyy-MM-dd")
        StiRpt("出院日期") = Format(DateTimePicker1.Value, "yyyy-MM-dd")
        StiRpt("打印时间") = Format(Now, "yyyy-MM-dd")
        StiRpt("住院天数") = Label11.Text
        StiRpt("应收金额") = C1TextBox18.Text
        StiRpt("实收金额") = Label10.Text
        StiRpt("退找金额") = C1TextBox19.Text


        For Each Mx_row In My_Dataset.Tables("明细表").Rows
            Select Case Mx_row("Cf_Lb")
                Case "床位费"
                    StiRpt("床位费") = CDbl(Mx_row.Item("Cf_Money"))
                Case "取暖费"
                    StiRpt("取暖费") = CDbl(Mx_row.Item("Cf_Money"))
                Case "检查费"
                    StiRpt("检查费") = CDbl(Mx_row.Item("Cf_Money"))
                Case "治疗费"
                    StiRpt("治疗费") = CDbl(Mx_row.Item("Cf_Money"))
                Case "手术费"
                    StiRpt("手术费") = CDbl(Mx_row.Item("Cf_Money"))
                Case "护理费"
                    StiRpt("护理费") = CDbl(Mx_row.Item("Cf_Money"))
                Case "化验费"
                    StiRpt("化验费") = CDbl(Mx_row.Item("Cf_Money"))
                Case "放射费"
                    StiRpt("放射费") = CDbl(Mx_row.Item("Cf_Money"))
                Case "处置费"
                    StiRpt("处置费") = CDbl(Mx_row.Item("Cf_Money"))
                Case "输氧费"
                    StiRpt("输氧费") = CDbl(Mx_row.Item("Cf_Money"))
                Case "输血费"
                    StiRpt("输血费") = CDbl(Mx_row.Item("Cf_Money"))
                Case "诊察费"
                    StiRpt("诊察费") = CDbl(Mx_row.Item("Cf_Money"))
                Case "接生费"
                    StiRpt("接生费") = CDbl(Mx_row.Item("Cf_Money"))
                Case "麻醉费"
                    StiRpt("麻醉费") = CDbl(Mx_row.Item("Cf_Money"))
                Case "西药"
                    StiRpt("西药费") = CDbl(StiRpt("化验费") + Mx_row.Item("Cf_Money"))         '有疑问
                Case "中成药"
                    StiRpt("中成药") = CDbl(Mx_row.Item("Cf_Money"))
                Case "中草药"
                    StiRpt("中草药") = CDbl(Mx_row.Item("Cf_Money"))
                Case "卫生材料"
                    StiRpt("西药费") = CDbl(StiRpt("西药费") + Mx_row.Item("Cf_Money"))
            End Select
        Next

        Dim H_Money As Double = C1TextBox18.Text
        Dim Money_Dx As New BaseClass.ChineseNum
        If H_Money >= 0 Then
            Money_Dx.InputString = H_Money
        Else
            Money_Dx.InputString = -H_Money
        End If

        If Money_Dx.Valiad = True Then
            If H_Money >= 0 Then
                StiRpt("大写") = Money_Dx.OutString
            Else
                StiRpt("大写") = "负" & Money_Dx.OutString
            End If
        Else
            MsgBox("输入金额有误,请检查后重新打印", MsgBoxStyle.Critical, "提示")
            Exit Sub
        End If

        Dim My_Reader As SqlDataReader
        Dim My_Row As DataRow = My_Dataset.Tables("病人出院").Rows.Find(BrCyComobo.Columns("Bl_Code").Value)

        With My_Row
            .BeginEdit()
            .Item("Ry_CyDate") = DateTimePicker1.Value
            .Item("Ry_ZyTs") = Label11.Text
            .Item("Bl_M_Th") = C1TextBox19.Text
            .EndEdit()
        End With
        Dim vform As New PublicForm.PrintForm(Me.Name, "出院打印", Nothing, My_Row)
        StiRpt.Render()
        vform.StiViewerControl1.Report = StiRpt

        Select Case Sendtag

            Case "数据结算"

                vform.ShowDialog()
            Case "快速打印"
                '写入出院时间
                If HisPara.PublicConfig.ZyHsz = "否" Then
                    HisVar.HisVar.Sqldal.ExecuteSql("Update Bl Set Ry_CyDate='" & Format(DateTimePicker1.Value, "yyyy-MM-dd HH:mm:ss") & "',Ry_CyJsr='" & HisVar.HisVar.JsrCode & "',Ry_ZyTs='" & Label11.Text & "' Where  Bl_Code='" & BrCyComobo.Columns("Bl_Code").Value & "'")

                Else
                    HisVar.HisVar.Sqldal.ExecuteSql("Update Bl Set Ry_CyJsr='" & HisVar.HisVar.JsrCode & "' Where Yy_Code='" & HisVar.HisVar.WsyCode & "' and Bl_Code='" & BrCyComobo.Columns("Bl_Code").Value & "'")

                End If
                '当是被召回的病人出院时，需要再插入一条出院信息
                My_Reader = HisVar.HisVar.Sqldal.ExecuteReader("Select  * From Bl_Zh Where Yy_Code='" & HisVar.HisVar.WsyCode & "' and Bl_Code='" & BrCyComobo.Columns("Bl_Code").Value & "' And Cy_Lb='召回' order by Cy_Id desc")
                My_Reader.Read()  '取最近一次召回
                If My_Reader.HasRows = True Then
                    Dim Th As String
                    'Dim Zh_Jf As Object = Sqldal.GetSingle("select Jf_Money from Bl_Jf where Jf_Date>'" & My_Reader.Item("Cy_Date") + My_Reader.Item("Cy_Time") & "' ")
                    Th = My_Reader.Item("Bl_M_Th") + C1TextBox19.Text
                    My_Reader.Close()
                    If HisPara.PublicConfig.ZyHsz = "否" Then
                        HisVar.HisVar.Sqldal.ExecuteSql("Insert Into Bl_Zh(Yy_Code,Bl_Code,Cy_Date,Cy_Time,Jsr_Code,Cy_Lb,Bl_M_Th) Values('" & HisVar.HisVar.WsyCode & "','" & BrCyComobo.Columns("Bl_Code").Value & "','" & Format(DateTimePicker1.Value, "yyyy-MM-dd") & "','" & Format(TimeOfDay, "HH:mm:ss") & "','" & HisVar.HisVar.JsrCode & "','出院'," & Th * -1 & ")")

                    Else
                        HisVar.HisVar.Sqldal.ExecuteSql("Insert Into Bl_Zh(Yy_Code,Bl_Code,Cy_Date,Cy_Time,Jsr_Code,Cy_Lb,Bl_M_Th) Values('" & HisVar.HisVar.WsyCode & "','" & BrCyComobo.Columns("Bl_Code").Value & "','" & Format(BrCyComobo.Columns("Ry_CyDate").Value, "yyyy-MM-dd") & "','" & Format(BrCyComobo.Columns("Ry_CyDate").Value, "HH:mm:ss") & "','" & HisVar.HisVar.JsrCode & "','出院'," & Th * -1 & ")")

                    End If
                End If
                My_Reader.Close()
                Call P_Conn(False)
                HisVar.HisVar.Sqldal.ExecuteSql("Update Auto_Cf set Auto_Zt='停止',End_Date='" & Format(Now, "yyyy-MM-dd HH:mm") & "'  where Bl_Code='" & BrCyComobo.Columns("Bl_Code").Value & "' and Auto_Zt<>'停止'")
                vform.StiViewerControl1.Report.Print()
        End Select

        Me.Close()

    End Sub

    Private Sub Hld_Fp(ByVal tag As String)


        V_Bl_Code = BrCyComobo.Columns("Bl_Code").Value


        If MsgBox("是否确认此患者出院？", vbQuestion + vbYesNo, "提示:") = vbNo Then Exit Sub
        P_Data_Show(V_Bl_Code)

        HisVar.HisVar.Sqldal.ExecuteSql("Update Bl Set Bl_M_Sk=" & C1TextBox18.Text & ",Bl_M_Yj=" & Label10.Text & ",Bl_M_Th=" & C1TextBox19.Text & " Where Bl_Code='" & BrCyComobo.Columns("Bl_Code").Value & "'")

        Dim Zy_top As Double = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\中软智通", "住院上边距", Nothing)
        Dim Zy_left As Double = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\中软智通", "住院左边距", Nothing)

        Dim StiRpt As New StiReport

        StiRpt.Load(".\Rpt\辽宁省住院收费票据.mrt")
        StiRpt.ReportName = "辽宁省住院收费票据"

        StiRpt.Pages(0).Margins.Top = Zy_top
        StiRpt.Pages(0).Margins.Bottom = Zy_left
        StiRpt.PrinterSettings.PrinterName = IIf(iniOperate.iniopreate.GetINI("打印机设置", "住院打印机", "", HisVar.HisVar.Parapath & "\Config.ini") & "" = "", StiRpt.PrinterSettings.PrinterName, iniOperate.iniopreate.GetINI("打印机设置", "住院打印机", "", HisVar.HisVar.Parapath & "\Config.ini"))
        StiRpt.Compile()
        StiRpt("医疗机构") = ZTHisVar.Var.HosName
        StiRpt("科室") = BrCyComobo.Columns("Ks_Name").Value
        StiRpt("流水号") = BrCyComobo.Columns("Bl_Code").Value
        StiRpt("住院号") = BrCyComobo.Columns("Ry_BlCode").Value
        StiRpt("住院时间") = " " & Format(BrCyComobo.Columns("Ry_RyDate").Value, "yyyy") & "    " & Format(BrCyComobo.Columns("Ry_RyDate").Value, "MM") & " " & Format(BrCyComobo.Columns("Ry_RyDate").Value, "dd") & "     " & Format(DateTimePicker1.Value, "yyyy") & "   " & Format(DateTimePicker1.Value, "MM") & "   " & Format(DateTimePicker1.Value, "dd")
        StiRpt("住院天数") = Label11.Text
        StiRpt("姓名") = Label7.Text
        StiRpt("性别") = Label1.Text
        StiRpt("医保类型") = BrCyComobo.Columns("Bxlb_Name").Value & ""
        StiRpt("社保号码") = BrCyComobo.Columns("Ry_YlCode").Value & ""

        Dim H_Money As Double = C1TextBox18.Text

        Dim Money_Dx As New BaseClass.ChineseNum
        If H_Money >= 0 Then
            Money_Dx.InputString = H_Money
        Else
            Money_Dx.InputString = -H_Money
        End If

        If Money_Dx.Valiad = True Then
            If H_Money >= 0 Then
                StiRpt("合计大写") = Money_Dx.OutString
            Else
                StiRpt("合计大写") = "负" & Money_Dx.OutString
            End If
        Else
            MsgBox("输入金额有误,请检查后重新打印", MsgBoxStyle.Critical, "提示")
            Exit Sub
        End If

        StiRpt("合计") = C1TextBox18.Text
        StiRpt("预缴金额") = Label10.Text
        StiRpt("补缴金额") = Format(0, "0.00")
        StiRpt("退费金额") = C1TextBox19.Text
        StiRpt("收款人") = HisVar.HisVar.JsrName

        StiRpt("打印时间") = Format(Now, "yyyy-MM-dd HH:mm")


        StiRpt.RegData(My_Dataset.Tables("明细表"))

        '将住院票据打印明细设定为固定个数
        Dim V_Newrow As DataRow

        Dim Tbrowcount As Integer = My_Dataset.Tables("明细表").Rows.Count
        If Tbrowcount Mod 21 <> 0 Then
            For V_TbRowCount = 1 To 21 - (Tbrowcount Mod 21)
                V_Newrow = My_Dataset.Tables("明细表").NewRow
                With V_Newrow
                    .Item(0) = DBNull.Value
                    .Item(1) = DBNull.Value
                End With
                My_Dataset.Tables("明细表").Rows.Add(V_Newrow)
                V_Newrow.AcceptChanges()
            Next
        End If



        Dim My_Row As DataRow = My_Dataset.Tables("病人出院").Rows.Find(BrCyComobo.Columns("Bl_Code").Value)

        With My_Row
            .BeginEdit()
            .Item("Ry_CyDate") = DateTimePicker1.Value
            .Item("Ry_ZyTs") = Label11.Text
            .Item("Bl_M_Th") = C1TextBox19.Text
            .EndEdit()
        End With
        Dim vform As New PublicForm.PrintForm(Me.Name, "出院打印", Nothing, My_Row)
        StiRpt.Render()
        vform.StiViewerControl1.Report = StiRpt

        Select Case Sendtag

            Case "数据结算"

                'StiRpt.Design()
                vform.ShowDialog()
            Case "快速打印"
                '写入出院时间
                If HisPara.PublicConfig.ZyHsz = "否" Then
                    HisVar.HisVar.Sqldal.ExecuteSql("Update Bl Set Ry_CyDate='" & Format(DateTimePicker1.Value, "yyyy-MM-dd HH:mm:ss") & "',Ry_CyJsr='" & HisVar.HisVar.JsrCode & "',Ry_ZyTs='" & Label11.Text & "' Where  Bl_Code='" & BrCyComobo.Columns("Bl_Code").Value & "'")

                Else
                    HisVar.HisVar.Sqldal.ExecuteSql("Update Bl Set Ry_CyJsr='" & HisVar.HisVar.JsrCode & "' Where  Bl_Code='" & BrCyComobo.Columns("Bl_Code").Value & "'")

                End If                    '当是被召回的病人出院时，需要再插入一条出院信息
                Dim My_Reader As SqlDataReader = HisVar.HisVar.Sqldal.ExecuteReader("Select  * From Bl_Zh Where  Bl_Code='" & BrCyComobo.Columns("Bl_Code").Value & "' And Cy_Lb='召回' order by Cy_Id desc")
                My_Reader.Read()  '取最近一次召回
                If My_Reader.HasRows = True Then
                    Dim Th As String
                    Th = My_Reader.Item("Bl_M_Th") + C1TextBox19.Text
                    My_Reader.Close()
                    If HisPara.PublicConfig.ZyHsz = "否" Then
                        HisVar.HisVar.Sqldal.ExecuteSql("Insert Into Bl_Zh(Yy_Code,Bl_Code,Cy_Date,Cy_Time,Jsr_Code,Cy_Lb,Bl_M_Th) Values('" & HisVar.HisVar.WsyCode & "','" & BrCyComobo.Columns("Bl_Code").Value & "','" & Format(DateTimePicker1.Value, "yyyy-MM-dd") & "','" & Format(TimeOfDay, "HH:mm:ss") & "','" & HisVar.HisVar.JsrCode & "','出院'," & Th * -1 & ")")

                    Else
                        HisVar.HisVar.Sqldal.ExecuteSql("Insert Into Bl_Zh(Yy_Code,Bl_Code,Cy_Date,Cy_Time,Jsr_Code,Cy_Lb,Bl_M_Th) Values('" & HisVar.HisVar.WsyCode & "','" & BrCyComobo.Columns("Bl_Code").Value & "','" & Format(BrCyComobo.Columns("Ry_CyDate").Value, "yyyy-MM-dd") & "','" & Format(BrCyComobo.Columns("Ry_CyDate").Value, "HH:mm:ss") & "','" & HisVar.HisVar.JsrCode & "','出院'," & Th * -1 & ")")

                    End If
                End If
                My_Reader.Close()
                Call P_Conn(False)
                HisVar.HisVar.Sqldal.ExecuteSql("Update Auto_Cf set Auto_Zt='停止',End_Date='" & Format(Now, "yyyy-MM-dd HH:mm") & "'  where Bl_Code='" & BrCyComobo.Columns("Bl_Code").Value & "' and Auto_Zt<>'停止'")
                vform.StiViewerControl1.Report.Print()
        End Select

        Me.Close()

    End Sub

    Private Sub Hld_Fp_Lb(ByVal tag As String)



        V_Bl_Code = BrCyComobo.Columns("Bl_Code").Value


        If MsgBox("是否确认此患者出院？", vbQuestion + vbYesNo, "提示:") = vbNo Then Exit Sub
        P_Data_Show(V_Bl_Code)

        HisVar.HisVar.Sqldal.ExecuteSql("Update Bl Set Bl_M_Sk=" & C1TextBox18.Text & ",Bl_M_Yj=" & Label10.Text & ",Bl_M_Th=" & C1TextBox19.Text & " Where Bl_Code='" & BrCyComobo.Columns("Bl_Code").Value & "'")

        Dim Zy_top As Double = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\中软智通", "住院上边距", Nothing)
        Dim Zy_left As Double = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\中软智通", "住院左边距", Nothing)

        Dim StiRpt As New StiReport
        StiRpt.Load(".\Rpt\辽宁省住院收费票据老版.mrt")

        StiRpt.ReportName = "辽宁省住院收费票据"

        StiRpt.Pages(0).Margins.Top = Zy_top
        StiRpt.Pages(0).Margins.Bottom = Zy_left
        StiRpt.PrinterSettings.PrinterName = IIf(iniOperate.iniopreate.GetINI("打印机设置", "住院打印机", "", HisVar.HisVar.Parapath & "\Config.ini") & "" = "", StiRpt.PrinterSettings.PrinterName, iniOperate.iniopreate.GetINI("打印机设置", "住院打印机", "", HisVar.HisVar.Parapath & "\Config.ini"))
        StiRpt.Compile()
        StiRpt("医疗机构") = ZTHisVar.Var.HosName
        StiRpt("科室") = BrCyComobo.Columns("Ks_Name").Value
        StiRpt("流水号") = BrCyComobo.Columns("Bl_Code").Value
        StiRpt("住院号") = BrCyComobo.Columns("Ry_BlCode").Value
        StiRpt("住院时间") = " " & Format(BrCyComobo.Columns("Ry_RyDate").Value, "yyyy") & "-" & Format(BrCyComobo.Columns("Ry_RyDate").Value, "MM") & "-" & Format(BrCyComobo.Columns("Ry_RyDate").Value, "dd") & "至" & Format(DateTimePicker1.Value, "yyyy") & "-" & Format(DateTimePicker1.Value, "MM") & "-" & Format(DateTimePicker1.Value, "dd")
        StiRpt("住院天数") = Label11.Text
        StiRpt("姓名") = Label7.Text
        StiRpt("性别") = Label1.Text
        StiRpt("医保类型") = BrCyComobo.Columns("Bxlb_Name").Value & ""
        StiRpt("社保号码") = BrCyComobo.Columns("Ry_YlCode").Value & ""

        Dim H_Money As Double = C1TextBox18.Text

        Dim Money_Dx As New BaseClass.ChineseNum
        If H_Money >= 0 Then
            Money_Dx.InputString = H_Money
        Else
            Money_Dx.InputString = -H_Money
        End If

        If Money_Dx.Valiad = True Then
            If H_Money >= 0 Then
                StiRpt("合计大写") = Money_Dx.OutString
            Else
                StiRpt("合计大写") = "负" & Money_Dx.OutString
            End If
        Else
            MsgBox("输入金额有误,请检查后重新打印", MsgBoxStyle.Critical, "提示")
            Exit Sub
        End If

        StiRpt("合计") = C1TextBox18.Text
        StiRpt("预缴金额") = Label10.Text
        StiRpt("补缴金额") = Format(0, "0.00")
        StiRpt("退费金额") = C1TextBox19.Text
        StiRpt("收款人") = HisVar.HisVar.JsrName

        StiRpt("打印时间") = Format(Now, "yyyy-MM-dd HH:mm")


        StiRpt.RegData(My_Dataset.Tables("明细表"))

        '将住院票据打印明细设定为固定个数
        Dim V_Newrow As DataRow

        Dim Tbrowcount As Integer = My_Dataset.Tables("明细表").Rows.Count
        If Tbrowcount Mod 20 <> 0 Then
            For V_TbRowCount = 1 To 20 - (Tbrowcount Mod 20)
                V_Newrow = My_Dataset.Tables("明细表").NewRow
                With V_Newrow
                    .Item(0) = DBNull.Value
                    .Item(1) = DBNull.Value
                End With
                My_Dataset.Tables("明细表").Rows.Add(V_Newrow)
                V_Newrow.AcceptChanges()
            Next
        End If



        Dim My_Row As DataRow = My_Dataset.Tables("病人出院").Rows.Find(BrCyComobo.Columns("Bl_Code").Value)

        With My_Row
            .BeginEdit()
            .Item("Ry_CyDate") = DateTimePicker1.Value
            .Item("Ry_ZyTs") = Label11.Text
            .Item("Bl_M_Th") = C1TextBox19.Text
            .EndEdit()
        End With
        Dim vform As New PublicForm.PrintForm(Me.Name, "出院打印", Nothing, My_Row)
        StiRpt.Render()
        vform.StiViewerControl1.Report = StiRpt

        Select Case Sendtag

            Case "数据结算"

                'StiRpt.Design()
                vform.ShowDialog()
            Case "快速打印"
                '写入出院时间
                If HisPara.PublicConfig.ZyHsz = "否" Then
                    HisVar.HisVar.Sqldal.ExecuteSql("Update Bl Set Ry_CyDate='" & Format(DateTimePicker1.Value, "yyyy-MM-dd HH:mm:ss") & "',Ry_CyJsr='" & HisVar.HisVar.JsrCode & "',Ry_ZyTs='" & Label11.Text & "' Where  Bl_Code='" & BrCyComobo.Columns("Bl_Code").Value & "'")

                Else
                    HisVar.HisVar.Sqldal.ExecuteSql("Update Bl Set Ry_CyJsr='" & HisVar.HisVar.JsrCode & "' Where  Bl_Code='" & BrCyComobo.Columns("Bl_Code").Value & "'")

                End If                    '当是被召回的病人出院时，需要再插入一条出院信息
                Dim My_Reader As SqlDataReader = HisVar.HisVar.Sqldal.ExecuteReader("Select  * From Bl_Zh Where  Bl_Code='" & BrCyComobo.Columns("Bl_Code").Value & "' And Cy_Lb='召回' order by Cy_Id desc")
                My_Reader.Read()  '取最近一次召回
                If My_Reader.HasRows = True Then
                    Dim Th As String
                    Th = My_Reader.Item("Bl_M_Th") + C1TextBox19.Text
                    My_Reader.Close()
                    If HisPara.PublicConfig.ZyHsz = "否" Then
                        HisVar.HisVar.Sqldal.ExecuteSql("Insert Into Bl_Zh(Yy_Code,Bl_Code,Cy_Date,Cy_Time,Jsr_Code,Cy_Lb,Bl_M_Th) Values('" & HisVar.HisVar.WsyCode & "','" & BrCyComobo.Columns("Bl_Code").Value & "','" & Format(DateTimePicker1.Value, "yyyy-MM-dd") & "','" & Format(TimeOfDay, "HH:mm:ss") & "','" & HisVar.HisVar.JsrCode & "','出院'," & Th * -1 & ")")

                    Else
                        HisVar.HisVar.Sqldal.ExecuteSql("Insert Into Bl_Zh(Yy_Code,Bl_Code,Cy_Date,Cy_Time,Jsr_Code,Cy_Lb,Bl_M_Th) Values('" & HisVar.HisVar.WsyCode & "','" & BrCyComobo.Columns("Bl_Code").Value & "','" & Format(BrCyComobo.Columns("Ry_CyDate").Value, "yyyy-MM-dd") & "','" & Format(BrCyComobo.Columns("Ry_CyDate").Value, "HH:mm:ss") & "','" & HisVar.HisVar.JsrCode & "','出院'," & Th * -1 & ")")

                    End If
                End If
                My_Reader.Close()
                Call P_Conn(False)
                HisVar.HisVar.Sqldal.ExecuteSql("Update Auto_Cf set Auto_Zt='停止',End_Date='" & Format(Now, "yyyy-MM-dd HH:mm") & "'  where Bl_Code='" & BrCyComobo.Columns("Bl_Code").Value & "' and Auto_Zt<>'停止'")
                vform.StiViewerControl1.Report.Print()
        End Select

        Me.Close()

    End Sub

#End Region

#Region "鼠标__外观"
    Private Sub P_Comm(ByVal Bt As Button)
        With Bt
            Select Case .Tag
                Case "数据结算"
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_数据结算1")
                    .Text = "                     &B"
                Case "取消"
                    .Location = New Point(Comm1.Left + Comm1.Width + 4, 4)
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
                    .Width = 50
                    .Text = "            &C"
                Case "快速打印"
                    .Location = New Point(Comm2.Left + Comm2.Width + 4, 4)
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_快速打印1")
                    .Text = "                     &K"

                Case "拒绝出院"
                    .Location = New Point(Comm3.Left + Comm3.Width + 4, 4)
                    .BackgroundImage = MyResources.C_Resources.getimage("拒绝出院1")
                    .Text = "            &J"
                Case "写健康卡"
                    .Location = New Point(Comm4.Left + Comm4.Width + 4, 4)
                    .BackgroundImage = MyResources.C_Resources.getimage("写健康卡1")
                    .Text = "            &D"
            End Select
            .FlatStyle = FlatStyle.Flat
            .FlatAppearance.BorderSize = 0
            .BackgroundImageLayout = ImageLayout.Center
        End With
    End Sub

    Private Sub Comm_MouseEnter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.MouseEnter, Comm2.MouseEnter, Comm3.MouseEnter, Comm4.MouseEnter
        Select Case sender.tag
            Case "数据结算"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_数据结算2")
                Comm1.Cursor = Cursors.Hand
            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消2")
                Comm2.Cursor = Cursors.Hand
            Case "快速打印"
                Comm3.BackgroundImage = MyResources.C_Resources.getimage("命令_快速打印2")
                Comm3.Cursor = Cursors.Hand
            Case "拒绝出院"
                Comm4.BackgroundImage = MyResources.C_Resources.getimage("拒绝出院2")
                Comm4.Cursor = Cursors.Hand
        End Select

    End Sub

    Private Sub Comm_MouseLeave(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.MouseLeave, Comm2.MouseLeave, Comm3.MouseLeave, Comm4.MouseLeave
        Select Case sender.tag
            Case "数据结算"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_数据结算1")
                Comm1.Cursor = Cursors.Default
            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
                Comm2.Cursor = Cursors.Default
            Case "快速打印"
                Comm3.BackgroundImage = MyResources.C_Resources.getimage("命令_快速打印1")
                Comm3.Cursor = Cursors.Default
            Case "拒绝出院"
                Comm4.BackgroundImage = MyResources.C_Resources.getimage("拒绝出院1")
                Comm4.Cursor = Cursors.Hand

        End Select
    End Sub

    Private Sub Comm_MouseDown(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm1.MouseDown, Comm2.MouseDown, Comm3.MouseDown, Comm4.MouseDown
        Select Case sender.tag
            Case "数据结算"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_数据结算3")
                Comm1.Cursor = Cursors.Default
            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消3")
                Comm2.Cursor = Cursors.Default
            Case "快速打印"
                Comm3.BackgroundImage = MyResources.C_Resources.getimage("命令_快速打印3")
                Comm3.Cursor = Cursors.Default
            Case "拒绝出院"
                Comm4.BackgroundImage = MyResources.C_Resources.getimage("拒绝出院3")
                Comm4.Cursor = Cursors.Hand
        End Select
    End Sub

    Private Sub Comm_MouseUp(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm1.MouseUp, Comm2.MouseUp, Comm3.MouseUp, Comm4.MouseUp
        Select Case sender.tag
            Case "数据结算"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_数据结算1")
                Comm1.Cursor = Cursors.Hand
            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
                Comm2.Cursor = Cursors.Hand
            Case "快速打印"
                Comm3.BackgroundImage = MyResources.C_Resources.getimage("命令_快速打印1")
                Comm3.Cursor = Cursors.Hand
            Case "拒绝出院"
                Comm4.BackgroundImage = MyResources.C_Resources.getimage("拒绝出院1")
                Comm4.Cursor = Cursors.Hand
        End Select
    End Sub
#End Region

    '英文
    Private Sub C1Combo1_Open(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BrCyComobo.GotFocus
        InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages(0)
    End Sub

    Private Sub DateTimePicker1_ValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles DateTimePicker1.ValueChanged
        If HisPara.PublicConfig.ZyHsz = "是" Or BrCyComobo.SelectedValue = "" Then Exit Sub
        Label11.Text = ZTHisPublicFunction.InpatFunc.CalculateHospitalDays(Convert.ToDateTime(BrCyComobo.Columns("Ry_RyDate").Value), Convert.ToDateTime(DateTimePicker1.Value))

    End Sub

 
   
End Class