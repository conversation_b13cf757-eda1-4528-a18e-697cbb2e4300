﻿Imports Stimulsoft.Report
Imports Stimulsoft.Report.Components
Imports System.Data.SqlClient
Public Class Cx_Hzly
    Dim My_Dataset As New DataSet
    Dim My_Table As DataTable
    Dim V_TreeFinish As Boolean
    Dim bllBlCflyd As New BLL.BllBl_Cflyd
    Private Sub Cx_Hzly_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        BaseFunc.BaseFunc.Init_Datetimepicker(DateTimePicker1)
        BaseFunc.BaseFunc.Init_Datetimepicker(DateTimePicker2)
        DateTimePicker1.Value = Format(Date.Today, "yyyy-MM-dd 00:00")
        DateTimePicker2.Value = Format(Date.Today, "yyyy-MM-dd 23:59")

        Call Form_int()
        Call Init_Tree()
        Call Show_Data()
    End Sub

    Private Sub Form_int()
        Panel1.BorderStyle = BorderStyle.None
        Panel1.Height = 32
        Label1.Location = New Point(TreeView1.Right, 3)
        DateTimePicker1.Location = New Point(Label1.Right + 2, 3)
        Label2.Location = New Point(DateTimePicker1.Left + DateTimePicker1.Width + 2, 3)
        DateTimePicker2.Location = New Point(Label2.Left + Label2.Width + 2, 3)
        Button1.Location = New Point(DateTimePicker2.Left + DateTimePicker2.Width + 5, 3)
        Button2.Location = New Point(Button1.Left + Button1.Width + 2, 3)
        Button3.Location = New Point(Button2.Left + Button2.Width + 2, 3)
        '初始化TDBGrid
        Dim My_Grid1 As New BaseClass.C_Grid(Me.C1TrueDBGrid1)
        With My_Grid1
            .Init_Grid()
            .AllAddNew(False)
            .AllDelete(False)
            .AllUpdate(False)
            .AllSort(False)
            .P_Dock(BaseClass.C_Grid.Dock.Fill)
            .Init_Column("处方编码", "Cf_Code", 120, "中", "")
            .Init_Column("患者姓名", "Ry_Name", 80, "左", "")
            .Init_Column("药品名称", "Yp_Name", 230, "左", "")
            .Init_Column("规格", "Mx_Gg", 110, "左", "")
            .Init_Column("产地", "Mx_Cd", 120, "左", "")
            .Init_Column("生产批号", "Yp_Ph", 75, "左", "")
            .Init_Column("有效期", "Yp_Yxq", 75, "中", "yyyy-MM-dd")
            .Init_Column("单位", "Mx_XsDw", 45, "中", "")
            .Init_Column("数量", "Cf_Sl", 60, "右", "###,###,##0.####")
            .Init_Column("单价", "Cf_Dj", 70, "右", "###,###,##0.00##")
            .Init_Column("金额", "Cf_Money", 60, "右", "###,###,##0.00##")

        End With
        C1TrueDBGrid1.Splits(0).DisplayColumns("Cf_Code").Merge = C1.Win.C1TrueDBGrid.ColumnMergeEnum.Restricted
        C1TrueDBGrid1.Splits(0).DisplayColumns("Cf_Code").Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
        C1TrueDBGrid1.Splits(0).DisplayColumns("Cf_Code").Style.VerticalAlignment = C1.Win.C1TrueDBGrid.AlignVertEnum.Center
        C1TrueDBGrid1.Splits(0).DisplayColumns("Ry_Name").Merge = C1.Win.C1TrueDBGrid.ColumnMergeEnum.Restricted
        C1TrueDBGrid1.Splits(0).DisplayColumns("Ry_Name").Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
        C1TrueDBGrid1.Splits(0).DisplayColumns("Ry_Name").Style.VerticalAlignment = C1.Win.C1TrueDBGrid.AlignVertEnum.Center
        C1TrueDBGrid1.Splits(0).MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightCell
        C1TrueDBGrid1.Columns("Cf_Money").Aggregate = C1.Win.C1TrueDBGrid.AggregateEnum.Sum

    End Sub

    Private Sub Init_Tree()

        With Me.TreeView1
            .Nodes.Clear()
            .FullRowSelect = True
            .HideSelection = False
            .HotTracking = True
            .Scrollable = True
            .ShowRootLines = False
            .Sorted = False
        End With

        V_TreeFinish = False

        '根节点
        Dim My_Reader As SqlDataReader

        Dim My_Root As New TreeNode
        With My_Root
            .Tag = "00"
            .Text = "领药单号(经手人)"
            .ImageIndex = 0
        End With

        TreeView1.Nodes.Clear()
        TreeView1.Nodes.Add(My_Root)

        My_Reader = HisVar.HisVar.Sqldal.ExecuteReader("Select distinct Lyd_Code,Jsr_Name From Bl_Cflyd,Zd_YyJsr Where Bl_CfLyd.Jsr_Code=Zd_YyJsr.Jsr_Code and Left(Lyd_Code,12) between '" & Format(DateTimePicker1.Value, "yyyyMMddHHmm") & "' and '" & Format(DateTimePicker2.Value, "yyyyMMddHHmm") & "'")
        While My_Reader.Read()
            Dim My_Node As New TreeNode
            With My_Node
                .Tag = My_Reader.Item(0).ToString
                .Text = My_Reader.Item(0).ToString + "(" + My_Reader.Item("Jsr_Name").ToString + ")"
                .ImageIndex = 1
                .SelectedImageIndex = 1
            End With
            TreeView1.SelectedNode = My_Root
            TreeView1.SelectedNode.Nodes.Add(My_Node)

        End While
        My_Reader.Close()

        With Me.TreeView1
            .SelectedNode = TreeView1.TopNode
            .SelectedNode.Expand()
            .Select()
        End With
        V_TreeFinish = True

    End Sub
    Private Sub Show_Data()
        Dim V_Code As String = "''"
        If TreeView1.SelectedNode.Tag = "00" Then
            For Each code In TreeView1.SelectedNode.Nodes
                V_Code = V_Code + ",'" & code.tag & "' "
            Next
        Else
            V_Code = TreeView1.SelectedNode.Tag
        End If
        'Dim Str_Select = "Select Bl_CfLyd.Cf_Code,Bl.Bl_Code,Ry_BlCode,Bl_Cflyd.Ry_Name,Cf_Sl,Bl_Cfyp.Cf_Money,Cf_Dj,Mx_Gg,Mx_Cd,Yp_Name,Mx_XsDw,Yp_Ph,Yp_Yxq,Bc_Name,Ks_Name from Bl,Zd_YyBc,Bl_Cflyd,Bl_Cf,Bl_Cfyp,V_YpKc  Where Bl.Bl_Code=Bl_Cf.Bl_Code and Bl.Bc_Code=Zd_YyBc.Bc_Code and Bl_CfLyd.Cf_Code=Bl_Cf.Cf_Code and Bl_Cf.Cf_Code=Bl_CfYp.Cf_Code and Bl_CfYp.Xx_Code=V_YpKc.Xx_Code And Ly_Qr='是'  and Lyd_Code in (" & V_Code & ")"

        'My_Dataset.EnforceConstraints = False

        'HisVar.HisVar.Sqldal.QueryDt(My_Dataset, Str_Select, "汇总领药查询", True)

        'My_Table = My_Dataset.Tables("汇总领药查询")
        My_Table = bllBlCflyd.GetList("Lyd_Code in (" & V_Code & ")").Tables(0)
        My_Table.TableName = "汇总领药"
        With Me.C1TrueDBGrid1
            .SetDataBinding(My_Table, "", True)
        End With

    End Sub
    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click
        Call Init_Tree()
        Call Show_Data()
    End Sub

    Private Sub TreeView1_AfterSelect(ByVal sender As System.Object, ByVal e As System.Windows.Forms.TreeViewEventArgs) Handles TreeView1.AfterSelect
        If V_TreeFinish = False Then Exit Sub
        Call Show_Data()
    End Sub


    Private Sub Button2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button2.Click
        If TreeView1.SelectedNode.Tag = "00" Then
            MsgBox("请选择要打印的领药单！")
            Exit Sub
        Else
            Dim print As New ZTHisNurse.Print
            print.PrintHzlyd(TreeView1.SelectedNode.Tag, "按患者汇总打印", My_Table)
            'Dim StiRpt As New StiReport
            'StiRpt.RegData(My_Dataset.Tables("汇总领药查询"))
            'StiRpt.Load(".\Rpt\护士站汇总领药.mrt")
            'StiRpt.ReportName = "护士站汇总领药"
            'StiRpt.Compile()
            'StiRpt("标题") = "汇 总 领 药 单"
            'StiRpt("操作员") = "操作员:" & HisVar.HisVar.JsrName
            'StiRpt("医嘱数量") = "医嘱数量:" & HisVar.HisVar.Sqldal.GetSingle("Select Count(*) from Bl_CfLyd where Lyd_Code='" & TreeView1.SelectedNode.Tag & "'")
            'StiRpt("领药日期") = "领药日期:" & Format(Now, "yyyy年MM月dd日")
            '' StiRpt.Design()
            'StiRpt.Show()
        End If
    End Sub

    Private Sub Button3_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button3.Click
        If TreeView1.SelectedNode.Tag = "00" Then
            MsgBox("请选择要打印的领药单！")
            Exit Sub
        Else
            Dim print As New ZTHisNurse.Print
            print.PrintHzlyd(TreeView1.SelectedNode.Tag, "按药品汇总打印", My_Table)
            'Dim StiRpt As New StiReport
            'StiRpt.RegData(My_Dataset.Tables("汇总领药查询"))
            'StiRpt.Load(".\Rpt\护士站汇总领药.mrt")
            'StiRpt.ReportName = "护士站汇总领药"
            'Dim groupBand As New StiGroupHeaderBand
            'groupBand = StiRpt.GetComponents("GroupHeaderBand1")
            'groupBand.Condition.Value = ""
            'groupBand.Height = 0

            'TryCast(StiRpt.Pages(0).GetComponents.Item("Text21"), StiText).Text = ""
            'TryCast(StiRpt.Pages(0).GetComponents.Item("Text20"), StiText).Text = ""
            'TryCast(StiRpt.Pages(0).GetComponents.Item("Text22"), StiText).Text = ""
            'TryCast(StiRpt.Pages(0).GetComponents.Item("Text23"), StiText).Text = ""
            'StiRpt.GetComponents("Text21").Enabled = False
            'StiRpt.GetComponents("Text20").Enabled = False
            'StiRpt.GetComponents("Text22").Enabled = False
            'StiRpt.GetComponents("Text23").Enabled = False

            'StiRpt.Compile()
            'StiRpt("标题") = "汇 总 领 药 单"
            'StiRpt("操作员") = "操作员:" & HisVar.HisVar.JsrName
            'StiRpt("医嘱数量") = "医嘱数量:" & HisVar.HisVar.Sqldal.GetSingle("Select Count(*) from Bl_CfLyd where Lyd_Code='" & TreeView1.SelectedNode.Tag & "'")
            'StiRpt("领药日期") = "领药日期:" & Format(Now, "yyyy年MM月dd日")
            '' StiRpt.Design()
            'StiRpt.Show()
        End If
    End Sub

    Private Sub Label2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Label2.Click

    End Sub
End Class