﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class MaterialsDict11
    Inherits HisControl.BaseForm

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(MaterialsDict11))
        Me.Link2 = New C1.Win.C1Command.C1CommandLink()
        Me.Comm2 = New C1.Win.C1Command.C1Command()
        Me.C1CommandHolder1 = New C1.Win.C1Command.C1CommandHolder()
        Me.Comm1 = New C1.Win.C1Command.C1Command()
        Me.Comm3 = New C1.Win.C1Command.C1Command()
        Me.CommDr = New C1.Win.C1Command.C1Command()
        Me.CommDc = New C1.Win.C1Command.C1Command()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.T_Line1 = New System.Windows.Forms.Label()
        Me.ToolBar1 = New C1.Win.C1Command.C1ToolBar()
        Me.Link1 = New C1.Win.C1Command.C1CommandLink()
        Me.C1CommandLink1 = New C1.Win.C1Command.C1CommandLink()
        Me.C1CommandLink2 = New C1.Win.C1Command.C1CommandLink()
        Me.Link3 = New C1.Win.C1Command.C1CommandLink()
        Me.T_Label = New C1.Win.C1Input.C1Label()
        Me.Image1 = New System.Windows.Forms.ImageList(Me.components)
        Me.CMenuStrip1 = New System.Windows.Forms.ContextMenuStrip(Me.components)
        Me.AddClass = New System.Windows.Forms.ToolStripMenuItem()
        Me.DelClass = New System.Windows.Forms.ToolStripMenuItem()
        Me.EditClass = New System.Windows.Forms.ToolStripMenuItem()
        Me.ExAllClass = New System.Windows.Forms.ToolStripMenuItem()
        Me.TreeView1 = New System.Windows.Forms.TreeView()
        Me.MyGrid1 = New CustomControl.MyGrid()
        Me.Edit_Materials_CMS = New System.Windows.Forms.ContextMenuStrip(Me.components)
        Me.MateriaEdit = New System.Windows.Forms.ToolStripMenuItem()
        Me.MaterialMove = New System.Windows.Forms.ToolStripMenuItem()
        Me.Filter_Tb = New CustomControl.MyTextBox()
        CType(Me.C1CommandHolder1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Panel1.SuspendLayout()
        CType(Me.T_Label, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.CMenuStrip1.SuspendLayout()
        Me.Edit_Materials_CMS.SuspendLayout()
        Me.SuspendLayout()
        '
        'Link2
        '
        Me.Link2.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image), C1.Win.C1Command.ButtonLookFlags)
        Me.Link2.Command = Me.Comm2
        Me.Link2.SortOrder = 1
        Me.Link2.Text = "删除"
        '
        'Comm2
        '
        Me.Comm2.Image = Global.ZtHis.Materials.My.Resources.Resources.miBarDelete_Glyph
        Me.Comm2.Name = "Comm2"
        Me.Comm2.Shortcut = System.Windows.Forms.Shortcut.Del
        Me.Comm2.ShortcutText = ""
        Me.Comm2.Text = "删除"
        Me.Comm2.ToolTipText = "删除记录"
        '
        'C1CommandHolder1
        '
        Me.C1CommandHolder1.Commands.Add(Me.Comm2)
        Me.C1CommandHolder1.Commands.Add(Me.Comm1)
        Me.C1CommandHolder1.Commands.Add(Me.Comm3)
        Me.C1CommandHolder1.Commands.Add(Me.CommDr)
        Me.C1CommandHolder1.Commands.Add(Me.CommDc)
        Me.C1CommandHolder1.Owner = Me
        '
        'Comm1
        '
        Me.Comm1.Image = Global.ZtHis.Materials.My.Resources.Resources.miBarAdd_Glyph
        Me.Comm1.Name = "Comm1"
        Me.Comm1.Shortcut = System.Windows.Forms.Shortcut.Ins
        Me.Comm1.ShortcutText = ""
        Me.Comm1.Text = "增加"
        Me.Comm1.ToolTipText = "增加记录"
        '
        'Comm3
        '
        Me.Comm3.Image = Global.ZtHis.Materials.My.Resources.Resources.miBarRefresh_Glyph
        Me.Comm3.Name = "Comm3"
        Me.Comm3.Shortcut = System.Windows.Forms.Shortcut.F5
        Me.Comm3.ShortcutText = ""
        Me.Comm3.Text = "更新"
        Me.Comm3.ToolTipText = "更新记录"
        '
        'CommDr
        '
        Me.CommDr.Image = Global.ZtHis.Materials.My.Resources.Resources.导入
        Me.CommDr.Name = "CommDr"
        Me.CommDr.ShortcutText = ""
        Me.CommDr.Text = "导入"
        '
        'CommDc
        '
        Me.CommDc.Image = Global.ZtHis.Materials.My.Resources.Resources.导出
        Me.CommDc.Name = "CommDc"
        Me.CommDc.ShortcutText = ""
        Me.CommDc.Text = "导出"
        '
        'Panel1
        '
        Me.Panel1.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Panel1.Controls.Add(Me.Filter_Tb)
        Me.Panel1.Controls.Add(Me.T_Line1)
        Me.Panel1.Controls.Add(Me.ToolBar1)
        Me.Panel1.Controls.Add(Me.T_Label)
        Me.Panel1.Dock = System.Windows.Forms.DockStyle.Top
        Me.Panel1.Location = New System.Drawing.Point(0, 0)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(993, 28)
        Me.Panel1.TabIndex = 127
        '
        'T_Line1
        '
        Me.T_Line1.BackColor = System.Drawing.SystemColors.Control
        Me.T_Line1.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.T_Line1.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.T_Line1.Location = New System.Drawing.Point(0, 24)
        Me.T_Line1.Name = "T_Line1"
        Me.T_Line1.Size = New System.Drawing.Size(991, 2)
        Me.T_Line1.TabIndex = 120
        Me.T_Line1.Text = "Label1"
        '
        'ToolBar1
        '
        Me.ToolBar1.AccessibleName = "Tool Bar"
        Me.ToolBar1.BackColor = System.Drawing.Color.Transparent
        Me.ToolBar1.CommandHolder = Nothing
        Me.ToolBar1.CommandLinks.AddRange(New C1.Win.C1Command.C1CommandLink() {Me.Link1, Me.Link2, Me.C1CommandLink1, Me.C1CommandLink2, Me.Link3})
        Me.ToolBar1.Location = New System.Drawing.Point(3, 3)
        Me.ToolBar1.MinButtonSize = 22
        Me.ToolBar1.Movable = False
        Me.ToolBar1.Name = "ToolBar1"
        Me.ToolBar1.Size = New System.Drawing.Size(275, 22)
        Me.ToolBar1.Text = "C1ToolBar1"
        Me.ToolBar1.VisualStyle = C1.Win.C1Command.VisualStyle.Custom
        Me.ToolBar1.VisualStyleBase = C1.Win.C1Command.VisualStyle.Classic
        '
        'Link1
        '
        Me.Link1.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image), C1.Win.C1Command.ButtonLookFlags)
        Me.Link1.Command = Me.Comm1
        '
        'C1CommandLink1
        '
        Me.C1CommandLink1.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image), C1.Win.C1Command.ButtonLookFlags)
        Me.C1CommandLink1.Command = Me.CommDr
        Me.C1CommandLink1.SortOrder = 2
        '
        'C1CommandLink2
        '
        Me.C1CommandLink2.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image), C1.Win.C1Command.ButtonLookFlags)
        Me.C1CommandLink2.Command = Me.CommDc
        Me.C1CommandLink2.SortOrder = 3
        '
        'Link3
        '
        Me.Link3.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image), C1.Win.C1Command.ButtonLookFlags)
        Me.Link3.Command = Me.Comm3
        Me.Link3.SortOrder = 4
        '
        'T_Label
        '
        Me.T_Label.BackColor = System.Drawing.Color.Transparent
        Me.T_Label.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.T_Label.ForeColor = System.Drawing.Color.Black
        Me.T_Label.Location = New System.Drawing.Point(581, 4)
        Me.T_Label.Name = "T_Label"
        Me.T_Label.Size = New System.Drawing.Size(237, 16)
        Me.T_Label.TabIndex = 2
        Me.T_Label.Tag = Nothing
        Me.T_Label.Text = "T_Label"
        Me.T_Label.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.T_Label.TextDetached = True
        Me.T_Label.TrimStart = True
        Me.T_Label.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Image1
        '
        Me.Image1.ImageStream = CType(resources.GetObject("Image1.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.Image1.TransparentColor = System.Drawing.Color.White
        Me.Image1.Images.SetKeyName(0, "")
        Me.Image1.Images.SetKeyName(1, "")
        Me.Image1.Images.SetKeyName(2, "")
        Me.Image1.Images.SetKeyName(3, "layout_sidebar.png")
        Me.Image1.Images.SetKeyName(4, "layout_edit.png")
        '
        'CMenuStrip1
        '
        Me.CMenuStrip1.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.AddClass, Me.DelClass, Me.EditClass, Me.ExAllClass})
        Me.CMenuStrip1.Name = "CMenuStrip1"
        Me.CMenuStrip1.Size = New System.Drawing.Size(125, 92)
        '
        'AddClass
        '
        Me.AddClass.Image = Global.ZtHis.Materials.My.Resources.Resources.miBarAdd_Glyph
        Me.AddClass.Name = "AddClass"
        Me.AddClass.Size = New System.Drawing.Size(124, 22)
        Me.AddClass.Text = "增加类别"
        '
        'DelClass
        '
        Me.DelClass.Image = Global.ZtHis.Materials.My.Resources.Resources.miBarDelete_Glyph
        Me.DelClass.Name = "DelClass"
        Me.DelClass.Size = New System.Drawing.Size(124, 22)
        Me.DelClass.Text = "删除类别"
        '
        'EditClass
        '
        Me.EditClass.Image = Global.ZtHis.Materials.My.Resources.Resources.miBarEdit_Glyph
        Me.EditClass.Name = "EditClass"
        Me.EditClass.Size = New System.Drawing.Size(124, 22)
        Me.EditClass.Text = "修改类别"
        '
        'ExAllClass
        '
        Me.ExAllClass.Image = Global.ZtHis.Materials.My.Resources.Resources.miBarSearch_Glyph
        Me.ExAllClass.Name = "ExAllClass"
        Me.ExAllClass.Size = New System.Drawing.Size(124, 22)
        Me.ExAllClass.Text = "全部展开"
        '
        'TreeView1
        '
        Me.TreeView1.ContextMenuStrip = Me.CMenuStrip1
        Me.TreeView1.Dock = System.Windows.Forms.DockStyle.Left
        Me.TreeView1.ImageIndex = 0
        Me.TreeView1.ImageList = Me.Image1
        Me.TreeView1.Location = New System.Drawing.Point(0, 28)
        Me.TreeView1.Name = "TreeView1"
        Me.TreeView1.SelectedImageIndex = 0
        Me.TreeView1.Size = New System.Drawing.Size(234, 405)
        Me.TreeView1.TabIndex = 134
        '
        'MyGrid1
        '
        Me.MyGrid1.CanCustomCol = False
        Me.MyGrid1.Col = 0
        Me.MyGrid1.ColumnFooters = False
        Me.MyGrid1.ContextMenuStrip = Me.Edit_Materials_CMS
        Me.MyGrid1.DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveRight
        Me.MyGrid1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.MyGrid1.FetchRowStyles = False
        Me.MyGrid1.GroupByAreaVisible = False
        Me.MyGrid1.Location = New System.Drawing.Point(234, 28)
        Me.MyGrid1.Margin = New System.Windows.Forms.Padding(0)
        Me.MyGrid1.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.MyGrid1.Name = "MyGrid1"
        Me.MyGrid1.Size = New System.Drawing.Size(759, 405)
        Me.MyGrid1.TabAction = C1.Win.C1TrueDBGrid.TabActionEnum.ControlNavigation
        Me.MyGrid1.TabIndex = 135
        Me.MyGrid1.Xmlpath = Nothing
        '
        'Edit_Materials_CMS
        '
        Me.Edit_Materials_CMS.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.MateriaEdit, Me.MaterialMove})
        Me.Edit_Materials_CMS.Name = "Edit_Materials_CMS"
        Me.Edit_Materials_CMS.Size = New System.Drawing.Size(122, 48)
        '
        'MateriaEdit
        '
        Me.MateriaEdit.Image = Global.ZtHis.Materials.My.Resources.Resources.miBarEdit_Glyph
        Me.MateriaEdit.Name = "MateriaEdit"
        Me.MateriaEdit.Size = New System.Drawing.Size(121, 22)
        Me.MateriaEdit.Tag = "Edit"
        Me.MateriaEdit.Text = "编辑"
        '
        'MaterialMove
        '
        Me.MaterialMove.Image = Global.ZtHis.Materials.My.Resources.Resources.miBarExport_Glyph
        Me.MaterialMove.Name = "MaterialMove"
        Me.MaterialMove.Size = New System.Drawing.Size(121, 22)
        Me.MaterialMove.Tag = "Move"
        Me.MaterialMove.Text = "移动到..."
        '
        'Filter_Tb
        '
        Me.Filter_Tb.Captain = "过滤框"
        Me.Filter_Tb.CaptainBackColor = System.Drawing.Color.Transparent
        Me.Filter_Tb.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Filter_Tb.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.Filter_Tb.CaptainWidth = 50.0!
        Me.Filter_Tb.ContentForeColor = System.Drawing.Color.Black
        Me.Filter_Tb.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.Filter_Tb.Location = New System.Drawing.Point(305, 3)
        Me.Filter_Tb.Multiline = False
        Me.Filter_Tb.Name = "Filter_Tb"
        Me.Filter_Tb.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.Filter_Tb.ReadOnly = False
        Me.Filter_Tb.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.Filter_Tb.SelectionStart = 0
        Me.Filter_Tb.SelectStart = 0
        Me.Filter_Tb.Size = New System.Drawing.Size(256, 20)
        Me.Filter_Tb.TabIndex = 131
        Me.Filter_Tb.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Filter_Tb.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.Filter_Tb.Watermark = "可以根据名称或者简称进行过滤"
        '
        'MaterialsDict11
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(993, 433)
        Me.Controls.Add(Me.MyGrid1)
        Me.Controls.Add(Me.TreeView1)
        Me.Controls.Add(Me.Panel1)
        Me.Name = "MaterialsDict11"
        Me.Text = "物资字典"
        CType(Me.C1CommandHolder1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Panel1.ResumeLayout(False)
        CType(Me.T_Label, System.ComponentModel.ISupportInitialize).EndInit()
        Me.CMenuStrip1.ResumeLayout(False)
        Me.Edit_Materials_CMS.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents Link2 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents C1CommandHolder1 As C1.Win.C1Command.C1CommandHolder
    Friend WithEvents Comm1 As C1.Win.C1Command.C1Command
    Friend WithEvents Comm3 As C1.Win.C1Command.C1Command
    Friend WithEvents CommDr As C1.Win.C1Command.C1Command
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents T_Line1 As System.Windows.Forms.Label
    Friend WithEvents ToolBar1 As C1.Win.C1Command.C1ToolBar
    Friend WithEvents Link1 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link3 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents T_Label As C1.Win.C1Input.C1Label
    Friend WithEvents Image1 As System.Windows.Forms.ImageList
    Private WithEvents Comm2 As C1.Win.C1Command.C1Command
    Friend WithEvents C1CommandLink1 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents CommDc As C1.Win.C1Command.C1Command
    Friend WithEvents C1CommandLink2 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents CMenuStrip1 As System.Windows.Forms.ContextMenuStrip
    Friend WithEvents AddClass As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents DelClass As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents EditClass As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents MyGrid1 As CustomControl.MyGrid
    Friend WithEvents TreeView1 As System.Windows.Forms.TreeView
    Friend WithEvents ExAllClass As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents Edit_Materials_CMS As System.Windows.Forms.ContextMenuStrip
    Friend WithEvents MateriaEdit As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents MaterialMove As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents Filter_Tb As CustomControl.MyTextBox
End Class
