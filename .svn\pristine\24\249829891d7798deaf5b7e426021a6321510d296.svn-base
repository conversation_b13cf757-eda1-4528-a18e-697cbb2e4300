/****** Object:  Table [dbo].[Country_YB_SpKc_Drugtracinfo]    Script Date: 2024/7/21 22:22:37 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Country_YB_SpKc_Drugtracinfo](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[jxc_code] [varchar](100) NULL,
	[drug_trac_codg] [varchar](100) NULL,
 CONSTRAINT [PK_Country_YB_SpKc_Drugtracinfo] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Country_YB_SpXs_Drugtracinfo]    Script Date: 2024/7/21 22:22:37 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Country_YB_SpXs_Drugtracinfo](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[jxc_code] [varchar](100) NULL,
	[drug_trac_codg] [varchar](100) NULL,
 CONSTRAINT [PK_Country_YB_SpXs_Drugtracinfo] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Country_YB_SpXsTh_Drugtracinfo]    Script Date: 2024/7/21 22:22:37 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Country_YB_SpXsTh_Drugtracinfo](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[jxc_code] [varchar](100) NULL,
	[drug_trac_codg] [varchar](100) NULL,
 CONSTRAINT [PK_Country_YB_SpXsTh_Drugtracinfo] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO