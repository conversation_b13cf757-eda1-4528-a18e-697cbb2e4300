﻿/**  版本信息模板在安装目录下，可自行修改。
* D_Materials_Buy_In1.cs
*
* 功 能： N/A
* 类 名： D_Materials_Buy_In1
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/11/29 13:55:41   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using System.Collections;
using System.Collections.Generic;
using ModelOld;

namespace DAL
{
    /// <summary>
    /// 数据访问类:D_Materials_Buy_In1
    /// </summary>
    public partial class D_Materials_Buy_In1
    {
        public D_Materials_Buy_In1()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(string M_Buy_Code)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from Materials_Buy_In1");
            strSql.Append(" where M_Buy_Code=@M_Buy_Code ");
            SqlParameter[] parameters = {
                    new SqlParameter("@M_Buy_Code", SqlDbType.Char,11)          };
            parameters[0].Value = M_Buy_Code;

            return HisVar.HisVar.Sqldal.Exists(strSql.ToString(), parameters);
        }
        public string MaxCode()
        {
            string max = DateTime.Now.ToString("yyMMdd") + (string)(HisVar.HisVar.Sqldal.F_MaxCode("select max(substring(M_Buy_Code,7,5)) from Materials_Buy_In1 where substring(M_Buy_Code,1,6)='" + DateTime.Now.ToString("yyMMdd") + "'", 5));
            return max;
        }
        /// <summary>
        /// 完成单据
        /// </summary>
        public bool UpdateWc(string M_Buy_Code)
        {
            decimal TotalMoney = (decimal)HisVar.HisVar.Sqldal.GetSingle("select SUM(M_Buy_RealMoney ) from Materials_Buy_In2 where M_Buy_Code ='" + M_Buy_Code + "'");
            ArrayList arry = new ArrayList();
            arry.Add("update Materials_Buy_In1 set Finish_Date=getdate() , OrdersStatus='完成',TotalMoney='" + TotalMoney + "' where M_Buy_Code='" + M_Buy_Code + "'");
            arry.Add("Update Materials_Stock");
            int rows = HisVar.HisVar.Sqldal.ExecuteSql("");
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        ///总金额
        /// </summary>
        public double GetSumMoney(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT SUM(TotalMoney)  FROM dbo.Materials_Buy_In1  where OrdersStatus='完成'");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" and " + strWhere);
            }
            try
            {
                var douSum= HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
                if (douSum == null)
                {
                    return 0;
                }
                else
                {
                    return (double) douSum;
                }
            }
            catch (Exception)
            {
                return 0;
            }

        }
        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ModelOld.M_Materials_Buy_In1 model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into Materials_Buy_In1(");
            strSql.Append("M_Buy_Code,MaterialsSup_Code,MaterialsWh_Code,Order_Date,Arrival_Date,Input_Date,Finish_Date,Jsr_Code,TotalMoney,M_Buy_Memo,OrdersStatus,WriteOff_Code,WriteOffStatus)");
            strSql.Append(" values (");
            strSql.Append("@M_Buy_Code,@MaterialsSup_Code,@MaterialsWh_Code,@Order_Date,@Arrival_Date,@Input_Date,@Finish_Date,@Jsr_Code,@TotalMoney,@M_Buy_Memo,@OrdersStatus,@WriteOff_Code,@WriteOffStatus)");
            SqlParameter[] parameters = {
                    new SqlParameter("@M_Buy_Code", SqlDbType.Char,11),
                    new SqlParameter("@MaterialsSup_Code", SqlDbType.Char,5),
                    new SqlParameter("@MaterialsWh_Code", SqlDbType.Char,2),
                    new SqlParameter("@Order_Date", SqlDbType.SmallDateTime),
                    new SqlParameter("@Arrival_Date", SqlDbType.SmallDateTime),
                    new SqlParameter("@Input_Date", SqlDbType.SmallDateTime),
                    new SqlParameter("@Finish_Date", SqlDbType.SmallDateTime),
                    new SqlParameter("@Jsr_Code", SqlDbType.Char,7),
                    new SqlParameter("@TotalMoney", SqlDbType.Decimal,5),
                    new SqlParameter("@M_Buy_Memo", SqlDbType.VarChar,50),
                    new SqlParameter("@OrdersStatus", SqlDbType.VarChar,10),
                    new SqlParameter("@WriteOff_Code", SqlDbType.Char,11),
                    new SqlParameter("@WriteOffStatus", SqlDbType.VarChar,10)};
            parameters[0].Value = model.M_Buy_Code;
            parameters[1].Value = model.MaterialsSup_Code;
            parameters[2].Value = model.MaterialsWh_Code;
            parameters[3].Value = model.Order_Date;
            parameters[4].Value = model.Arrival_Date;
            parameters[5].Value = model.Input_Date;
            parameters[6].Value = Common.Tools.IsValueNull(model.Finish_Date);
            parameters[7].Value = model.Jsr_Code;
            parameters[8].Value = model.TotalMoney;
            parameters[9].Value = model.M_Buy_Memo;
            parameters[10].Value = model.OrdersStatus;
            parameters[11].Value = Common.Tools.IsValueNull(model.WriteOff_Code);
            parameters[12].Value = Common.Tools.IsValueNull(model.WriteOffStatus);

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ModelOld.M_Materials_Buy_In1 model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update Materials_Buy_In1 set ");
            strSql.Append("MaterialsSup_Code=@MaterialsSup_Code,");
            strSql.Append("MaterialsWh_Code=@MaterialsWh_Code,");
            strSql.Append("Order_Date=@Order_Date,");
            strSql.Append("Arrival_Date=@Arrival_Date,");
            strSql.Append("Input_Date=@Input_Date,");
            strSql.Append("Finish_Date=@Finish_Date,");
            strSql.Append("Jsr_Code=@Jsr_Code,");
            strSql.Append("TotalMoney=@TotalMoney,");
            strSql.Append("M_Buy_Memo=@M_Buy_Memo,");
            strSql.Append("OrdersStatus=@OrdersStatus,");
            strSql.Append("WriteOff_Code=@WriteOff_Code,");
            strSql.Append("WriteOffStatus=@WriteOffStatus");
            strSql.Append(" where M_Buy_Code=@M_Buy_Code ");
            SqlParameter[] parameters = {
                    new SqlParameter("@MaterialsSup_Code", SqlDbType.Char,5),
                    new SqlParameter("@MaterialsWh_Code", SqlDbType.Char,2),
                    new SqlParameter("@Order_Date", SqlDbType.SmallDateTime),
                    new SqlParameter("@Arrival_Date", SqlDbType.SmallDateTime),
                    new SqlParameter("@Input_Date", SqlDbType.SmallDateTime),
                    new SqlParameter("@Finish_Date", SqlDbType.SmallDateTime),
                    new SqlParameter("@Jsr_Code", SqlDbType.Char,7),
                    new SqlParameter("@TotalMoney", SqlDbType.Decimal,5),
                    new SqlParameter("@M_Buy_Memo", SqlDbType.VarChar,50),
                    new SqlParameter("@OrdersStatus", SqlDbType.VarChar,10),
                    new SqlParameter("@WriteOff_Code", SqlDbType.Char,11),
                    new SqlParameter("@WriteOffStatus", SqlDbType.VarChar,10),
                    new SqlParameter("@M_Buy_Code", SqlDbType.Char,11)};
            parameters[0].Value = model.MaterialsSup_Code;
            parameters[1].Value = model.MaterialsWh_Code;
            parameters[2].Value = model.Order_Date;
            parameters[3].Value = model.Arrival_Date;
            parameters[4].Value = model.Input_Date;
            parameters[5].Value = Common.Tools.IsValueNull(model.Finish_Date);
            parameters[6].Value = model.Jsr_Code;
            parameters[7].Value = model.TotalMoney;
            parameters[8].Value = model.M_Buy_Memo;
            parameters[9].Value = model.OrdersStatus;
            parameters[10].Value = Common.Tools.IsValueNull(model.WriteOff_Code);
            parameters[11].Value = Common.Tools.IsValueNull(model.WriteOffStatus);
            parameters[12].Value = model.M_Buy_Code;

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 单据完成
        /// </summary>
        public bool Complete(ModelOld.M_Materials_Buy_In1 model)
        {
            List<string> sqlList = new List<string>();
            List<SqlParameter[]> parametersList = new List<SqlParameter[]>();

            //单据状态改成完成,填写完成时间
            StringBuilder strSql1 = new StringBuilder();
            strSql1.Append("update Materials_Buy_In1 set ");
            strSql1.Append("OrdersStatus=@OrdersStatus,");
            strSql1.Append("Finish_Date=@Finish_Date");
            strSql1.Append(" where M_Buy_Code=@M_Buy_Code ");
            SqlParameter[] parameters1 = {
                    new SqlParameter("@OrdersStatus", SqlDbType.VarChar,10),
                    new SqlParameter("@Finish_Date", SqlDbType.SmallDateTime),
                    new SqlParameter("@M_Buy_Code", SqlDbType.Char,11)};
            parameters1[0].Value = model.OrdersStatus;
            parameters1[1].Value = model.Finish_Date;
            parameters1[2].Value = model.M_Buy_Code;

            sqlList.Add(strSql1.ToString());
            parametersList.Add(parameters1);


            //修改 被冲销单 
            if (model.WriteOffStatus == "冲销")
            {
                StringBuilder sb = new StringBuilder();
                sb.Append(" update Materials_Buy_In1 set ");
                sb.Append(" WriteOffStatus=@WriteOffStatus ");
                sb.Append(" where M_Buy_Code=@M_Buy_Code ");
                SqlParameter[] sp = {
					new SqlParameter("@WriteOffStatus", SqlDbType.VarChar,10),
					new SqlParameter("@M_Buy_Code", SqlDbType.Char,11)};
                sp[0].Value = "被冲销";
                sp[1].Value = model.WriteOff_Code;

                sqlList.Add(sb.ToString());
                parametersList.Add(sp);
            }



            D_Materials_Stock _Materials_Stock = new D_Materials_Stock();
            D_Materials_Buy_In2 _Materials_Buy2 = new D_Materials_Buy_In2();
            //插入新的库存
            List<ModelOld.M_Materials_Buy_In2> modelList = new List<M_Materials_Buy_In2>();
            modelList = _Materials_Buy2.GetModelList(model.M_Buy_Code);

            foreach (ModelOld.M_Materials_Buy_In2 model2 in modelList)
            {

                if (_Materials_Stock.Exists(model2.Materials_Code, model.MaterialsWh_Code, model2.MaterialsLot,
                    model2.MaterialsExpiryDate.Value, model2.M_BuyIn_Price.Value) == false)
                {
                    StringBuilder strSql3 = new StringBuilder();
                    strSql3.Append("insert into Materials_Stock(");
                    strSql3.Append(
                        "Materials_Code,MaterialsWh_Code,MaterialsStock_Code,MaterialsLot,MaterialsExpiryDate,MaterialsStore_Num,MaterialsStore_Price,MaterialsStore_Money)");
                    strSql3.Append(" values (");
                    strSql3.Append(
                        "@Materials_Code,@MaterialsWh_Code,@MaterialsStock_Code,@MaterialsLot,@MaterialsExpiryDate,@MaterialsStore_Num,@MaterialsStore_Price,@MaterialsStore_Money)");
                    SqlParameter[] parameter3 =
                    {
                        new SqlParameter("@Materials_Code", SqlDbType.Char, 10),
                        new SqlParameter("@MaterialsWh_Code", SqlDbType.Char, 2),
                        new SqlParameter("@MaterialsStock_Code", SqlDbType.Char, 16),
                        new SqlParameter("@MaterialsLot", SqlDbType.VarChar, 50),
                        new SqlParameter("@MaterialsExpiryDate", SqlDbType.SmallDateTime),
                        new SqlParameter("@MaterialsStore_Num", SqlDbType.Decimal),
                        new SqlParameter("@MaterialsStore_Price", SqlDbType.Decimal),
                        new SqlParameter("@MaterialsStore_Money", SqlDbType.Decimal)
                    };
                    parameter3[0].Value = model2.Materials_Code;
                    parameter3[1].Value = model.MaterialsWh_Code;
                    model2.MaterialsStock_Code = _Materials_Stock.MaxCode(model2.Materials_Code);
                    parameter3[2].Value = model2.MaterialsStock_Code;
                    parameter3[3].Value = model2.MaterialsLot;
                    parameter3[4].Value = model2.MaterialsExpiryDate;
                    parameter3[5].Value = 0;
                    parameter3[6].Value = model2.M_BuyIn_Price;
                    parameter3[7].Value = 0;

                    int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql3.ToString(), parameter3);
                    if (rows <= 0)
                    {
                        return false;
                    }
                }

                //新库存存在直接增加库存
                model2.MaterialsStock_Code = _Materials_Stock.MaterialsStock_Code(model2.Materials_Code,
                    model.MaterialsWh_Code, model2.MaterialsLot, model2.MaterialsExpiryDate.Value,
                    model2.M_BuyIn_Price.Value);
                StringBuilder strSql4 = new StringBuilder();
                //strSql4.Append("UPDATE Materials_Stock SET MaterialsStore_Num=MaterialsStore_Num+@M_BuyIn_Num,MaterialsStore_Money = MaterialsStore_Money + @M_BuyIn_Num * MaterialsStore_Price   ");
                strSql4.Append("UPDATE Materials_Stock SET MaterialsStore_Num=MaterialsStore_Num+@M_BuyIn_Num,MaterialsStore_Money = MaterialsStore_Money + @M_Buy_Money   ");
                strSql4.Append("WHERE MaterialsStock_Code=@MaterialsStock_Code");
                SqlParameter[] parameter4 =
                    {
                        new SqlParameter("@M_BuyIn_Num", SqlDbType.Decimal),
                        new SqlParameter("@MaterialsStock_Code", SqlDbType.Char, 16),
                        new SqlParameter("@M_Buy_Money", SqlDbType.Decimal,5)
                    };
                parameter4[0].Value = model2.M_BuyIn_Num;
                parameter4[1].Value = model2.MaterialsStock_Code;
                parameter4[2].Value = model2.M_Buy_Money;
                sqlList.Add(strSql4.ToString());
                parametersList.Add(parameter4);

                StringBuilder strSql5 = new StringBuilder();
                strSql5.Append("update Materials_Buy_In2 Set MaterialsStock_Code=@MaterialsStock_Code ");
                strSql5.Append("WHERE M_Buy_Detail_Code=@M_Buy_Detail_Code ");
                SqlParameter[] parameter5 ={
                    new SqlParameter("@MaterialsStock_Code", SqlDbType.Char,16),
                    new SqlParameter("@M_Buy_Detail_Code", SqlDbType.Char,15)};

                parameter5[0].Value = model2.MaterialsStock_Code;
                parameter5[1].Value = model2.M_Buy_Detail_Code;
                sqlList.Add(strSql5.ToString());
                parametersList.Add(parameter5);

                 //修改 被冲销单 
                if (model.WriteOffStatus == "冲销")
                {
                    StringBuilder sb2 = new StringBuilder();
                    sb2.Append(" UPDATE Materials_Buy_In2 SET ");
                    sb2.Append(" M_Buy_WriteoffNo = M_Buy_WriteoffNo + @M_Buy_Num , ");
                    sb2.Append(" M_Buy_RealNo = M_Buy_RealNo + @M_Buy_Num , ");
                    sb2.Append(" M_Buy_RealMoney = M_Buy_RealMoney + @M_Buy_Num * M_Buy_Price, ");
                    sb2.Append(" M_BuyIn_WriteoffNo = M_BuyIn_WriteoffNo + @M_Buy_Num * Convert_Ratio , ");
                    sb2.Append(" M_BuyIn_RealNo = M_BuyIn_RealNo + @M_Buy_Num * Convert_Ratio ");
                    sb2.Append(" WHERE   M_Buy_Code = @M_Buy_Code  ");
                    sb2.Append(" AND MaterialsStock_Code = @MaterialsStock_Code  ");

                    SqlParameter[] sp2 ={
                    new SqlParameter("@M_Buy_Num", SqlDbType.Decimal,5),
                    new SqlParameter("@M_Buy_Code", SqlDbType.Char,11),
                    new SqlParameter("@MaterialsStock_Code", SqlDbType.Char,16)
                                        };


                    sp2[0].Value = model2.M_Buy_Num;
                    sp2[1].Value = model.WriteOff_Code; ;
                    sp2[2].Value = model2.MaterialsStock_Code;

                    sqlList.Add(sb2.ToString());
                    parametersList.Add(sp2);
                }


            }

            int counts = HisVar.HisVar.Sqldal.ExecuteSql(sqlList, parametersList);
            if (counts > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 生成冲销数据
        /// </summary>
        public bool AddWriteOff(ModelOld.M_Materials_Buy_In1 model)
        {
            List<string> sqlList = new List<string>();
            List<SqlParameter[]> parametersList = new List<SqlParameter[]>();

            StringBuilder strSql1 = new StringBuilder();
            strSql1.Append("insert into Materials_Buy_In1(");
            strSql1.Append("M_Buy_Code,MaterialsSup_Code,MaterialsWh_Code,Order_Date,Arrival_Date,Input_Date,Finish_Date,Jsr_Code,TotalMoney,M_Buy_Memo,OrdersStatus,WriteOff_Code,WriteOffStatus)");
            strSql1.Append(" values (");
            strSql1.Append("@M_Buy_Code,@MaterialsSup_Code,@MaterialsWh_Code,@Order_Date,@Arrival_Date,@Input_Date,@Finish_Date,@Jsr_Code,@TotalMoney,@M_Buy_Memo,@OrdersStatus,@WriteOff_Code,@WriteOffStatus)");
            SqlParameter[] parameters1 = {
                    new SqlParameter("@M_Buy_Code", SqlDbType.Char,11),
                    new SqlParameter("@MaterialsSup_Code", SqlDbType.Char,5),
                    new SqlParameter("@MaterialsWh_Code", SqlDbType.Char,2),
                    new SqlParameter("@Order_Date", SqlDbType.SmallDateTime),
                    new SqlParameter("@Arrival_Date", SqlDbType.SmallDateTime),
                    new SqlParameter("@Input_Date", SqlDbType.SmallDateTime),
                    new SqlParameter("@Finish_Date", SqlDbType.SmallDateTime),
                    new SqlParameter("@Jsr_Code", SqlDbType.Char,7),
                    new SqlParameter("@TotalMoney", SqlDbType.Decimal,5),
                    new SqlParameter("@M_Buy_Memo", SqlDbType.VarChar,50),
                    new SqlParameter("@OrdersStatus", SqlDbType.VarChar,10),
                    new SqlParameter("@WriteOff_Code", SqlDbType.Char,11),
                    new SqlParameter("@WriteOffStatus", SqlDbType.VarChar,10)};
            parameters1[0].Value = model.M_Buy_Code;
            parameters1[1].Value = model.MaterialsSup_Code;
            parameters1[2].Value = model.MaterialsWh_Code;
            parameters1[3].Value = model.Order_Date;
            parameters1[4].Value = model.Arrival_Date;
            parameters1[5].Value = model.Input_Date;
            parameters1[6].Value = Common.Tools.IsValueNull(model.Finish_Date);
            parameters1[7].Value = model.Jsr_Code;
            parameters1[8].Value = model.TotalMoney;
            parameters1[9].Value = model.M_Buy_Memo;
            parameters1[10].Value = model.OrdersStatus;
            parameters1[11].Value = Common.Tools.IsValueNull(model.WriteOff_Code);
            parameters1[12].Value = Common.Tools.IsValueNull(model.WriteOffStatus);

            sqlList.Add(strSql1.ToString());
            parametersList.Add(parameters1);

            StringBuilder strSql2 = new StringBuilder();
            strSql2.Append("INSERT INTO Materials_Buy_In2 ");
            strSql2.Append("SELECT @M_Buy_Code,Materials_Code,MaterialsStock_Code, ");
            strSql2.Append("@M_Buy_Code+SUBSTRING(M_Buy_Detail_Code,12,4),MaterialsLot,MaterialsExpiryDate,");
            strSql2.Append("-M_Buy_Num,0,0,-M_BuyIn_Num,0,0,M_Buy_Price,M_BuyIn_Price,-M_Buy_Money,0,Pack_Unit,Convert_Ratio,Bulk_Unit,");
            strSql2.Append("M_BuyDetail_Memo ");
            strSql2.Append("FROM Materials_Buy_In2 where M_Buy_Code=@WriteOff_Code");
            SqlParameter[] parameters2 =
            {
                new SqlParameter("@M_Buy_Code", SqlDbType.Char, 11),
                new SqlParameter("@WriteOff_Code", SqlDbType.Char, 11)
            };
            parameters2[0].Value = model.M_Buy_Code;
            parameters2[1].Value = model.WriteOff_Code;
            sqlList.Add(strSql2.ToString());
            parametersList.Add(parameters2);

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(sqlList, parametersList);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }

        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(string M_Buy_Code)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from Materials_Buy_In1 ");
            strSql.Append(" where M_Buy_Code=@M_Buy_Code ");
            SqlParameter[] parameters = {
                    new SqlParameter("@M_Buy_Code", SqlDbType.Char,11)          };
            parameters[0].Value = M_Buy_Code;

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string M_Buy_Codelist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from Materials_Buy_In1 ");
            strSql.Append(" where M_Buy_Code in (" + M_Buy_Codelist + ")  ");
            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        public DataSet GetHzList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(" SELECT * FROM dbo.Materials_Buy_In1 ,dbo.Materials_Buy_In2 ,dbo.Materials_Sup_Dict ,Materials_Warehouse_Dict ,dbo.Zd_YyJsr ,Materials_Dict  ");
            strSql.Append(" WHERE Materials_Buy_In2.M_Buy_Code = Materials_Buy_In1.M_Buy_Code AND Materials_Buy_In1.MaterialsSup_Code = Materials_Sup_Dict.MaterialsSup_Code ");
            strSql.Append(" AND Materials_Buy_In1.MaterialsWh_Code = Materials_Warehouse_Dict.MaterialsWh_Code AND Materials_Buy_In1.Jsr_Code = Zd_YyJsr.Jsr_Code ");
            strSql.Append(" AND Materials_Buy_In2.Materials_Code = Materials_Dict.Materials_Code ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(strWhere);
            }
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ModelOld.M_Materials_Buy_In1 GetModel(string M_Buy_Code)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 M_Buy_Code,MaterialsSup_Code,MaterialsWh_Code,Order_Date,Arrival_Date,Input_Date,Finish_Date,Jsr_Code,TotalMoney,M_Buy_Memo,OrdersStatus,WriteOff_Code,WriteOffStatus from Materials_Buy_In1 ");
            strSql.Append(" where M_Buy_Code=@M_Buy_Code ");
            SqlParameter[] parameters = {
                    new SqlParameter("@M_Buy_Code", SqlDbType.Char,11)          };
            parameters[0].Value = M_Buy_Code;

            ModelOld.M_Materials_Buy_In1 model = new ModelOld.M_Materials_Buy_In1();
            DataSet ds = HisVar.HisVar.Sqldal.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ModelOld.M_Materials_Buy_In1 DataRowToModel(DataRow row)
        {
            ModelOld.M_Materials_Buy_In1 model = new ModelOld.M_Materials_Buy_In1();
            if (row != null)
            {
                if (row["M_Buy_Code"] != null)
                {
                    model.M_Buy_Code = row["M_Buy_Code"].ToString();
                }
                if (row["MaterialsSup_Code"] != null)
                {
                    model.MaterialsSup_Code = row["MaterialsSup_Code"].ToString();
                }
                if (row["MaterialsWh_Code"] != null)
                {
                    model.MaterialsWh_Code = row["MaterialsWh_Code"].ToString();
                }
                if (row["Order_Date"] != null && row["Order_Date"].ToString() != "")
                {
                    model.Order_Date = DateTime.Parse(row["Order_Date"].ToString());
                }
                if (row["Arrival_Date"] != null && row["Arrival_Date"].ToString() != "")
                {
                    model.Arrival_Date = DateTime.Parse(row["Arrival_Date"].ToString());
                }
                if (row["Input_Date"] != null && row["Input_Date"].ToString() != "")
                {
                    model.Input_Date = DateTime.Parse(row["Input_Date"].ToString());
                }
                if (row["Finish_Date"] != null && row["Finish_Date"].ToString() != "")
                {
                    model.Finish_Date = DateTime.Parse(row["Finish_Date"].ToString());
                }
                if (row["Jsr_Code"] != null)
                {
                    model.Jsr_Code = row["Jsr_Code"].ToString();
                }
                if (row["TotalMoney"] != null && row["TotalMoney"].ToString() != "")
                {
                    model.TotalMoney = decimal.Parse(row["TotalMoney"].ToString());
                }
                if (row["M_Buy_Memo"] != null)
                {
                    model.M_Buy_Memo = row["M_Buy_Memo"].ToString();
                }
                if (row["OrdersStatus"] != null)
                {
                    model.OrdersStatus = row["OrdersStatus"].ToString();
                }
                if (row["WriteOff_Code"] != null)
                {
                    model.WriteOff_Code = row["WriteOff_Code"].ToString();
                }
                if (row["WriteOffStatus"] != null)
                {
                    model.WriteOffStatus = row["WriteOffStatus"].ToString();
                }
            }
            return model;
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ModelOld.M_Materials_Buy_In2WriteOff DataWORowToModel(DataRow row)
        {
            ModelOld.M_Materials_Buy_In2WriteOff model = new ModelOld.M_Materials_Buy_In2WriteOff();
            if (row != null)
            {
                if (row["M_Buy_Code"] != null)
                {
                    model.M_Buy_Code = row["M_Buy_Code"].ToString();
                }
                if (row["MaterialsSup_Code"] != null)
                {
                    model.MaterialsSup_Code = row["MaterialsSup_Code"].ToString();
                }
                if (row["MaterialsWh_Code"] != null)
                {
                    model.MaterialsWh_Code = row["MaterialsWh_Code"].ToString();
                }
                if (row["Order_Date"] != null && row["Order_Date"].ToString() != "")
                {
                    model.Order_Date = DateTime.Parse(row["Order_Date"].ToString());
                }
                if (row["Arrival_Date"] != null && row["Arrival_Date"].ToString() != "")
                {
                    model.Arrival_Date = DateTime.Parse(row["Arrival_Date"].ToString());
                }
                if (row["Input_Date"] != null && row["Input_Date"].ToString() != "")
                {
                    model.Input_Date = DateTime.Parse(row["Input_Date"].ToString());
                }
                if (row["Finish_Date"] != null && row["Finish_Date"].ToString() != "")
                {
                    model.Finish_Date = DateTime.Parse(row["Finish_Date"].ToString());
                }
                if (row["Materials_Code"] != null)
                {
                    model.Materials_Code = row["Materials_Code"].ToString();
                }
                if (row["M_Buy_Num"] != null && row["M_Buy_Num"].ToString() != "")
                {
                    model.M_Buy_Num = decimal.Parse(row["M_Buy_Num"].ToString());
                }
                if (row["M_Buy_WriteoffNo"] != null && row["M_Buy_WriteoffNo"].ToString() != "")
                {
                    model.M_Buy_WriteoffNo = decimal.Parse(row["M_Buy_WriteoffNo"].ToString());
                }
                if (row["M_Buy_RealNo"] != null && row["M_Buy_RealNo"].ToString() != "")
                {
                    model.M_Buy_RealNo = decimal.Parse(row["M_Buy_RealNo"].ToString());
                }
                if (row["M_BuyIn_Num"] != null && row["M_BuyIn_Num"].ToString() != "")
                {
                    model.M_BuyIn_Num = decimal.Parse(row["M_BuyIn_Num"].ToString());
                }
                if (row["M_BuyIn_WriteoffNo"] != null && row["M_BuyIn_WriteoffNo"].ToString() != "")
                {
                    model.M_BuyIn_WriteoffNo = decimal.Parse(row["M_BuyIn_WriteoffNo"].ToString());
                }
                if (row["M_BuyIn_RealNo"] != null && row["M_BuyIn_RealNo"].ToString() != "")
                {
                    model.M_BuyIn_RealNo = decimal.Parse(row["M_BuyIn_RealNo"].ToString());
                }
                if (row["M_Buy_Price"] != null && row["M_Buy_Price"].ToString() != "")
                {
                    model.M_Buy_Price = decimal.Parse(row["M_Buy_Price"].ToString());
                }
                if (row["M_BuyIn_Price"] != null && row["M_BuyIn_Price"].ToString() != "")
                {
                    model.M_BuyIn_Price = decimal.Parse(row["M_BuyIn_Price"].ToString());
                }
                if (row["M_Buy_Money"] != null && row["M_Buy_Money"].ToString() != "")
                {
                    model.M_Buy_Money = decimal.Parse(row["M_Buy_Money"].ToString());
                }
                if (row["M_Buy_RealMoney"] != null && row["M_Buy_RealMoney"].ToString() != "")
                {
                    model.M_Buy_RealMoney = decimal.Parse(row["M_Buy_RealMoney"].ToString());
                }
                if (row["MaterialsSup_Name"] != null)
                {
                    model.MaterialsSup_Name = row["MaterialsSup_Name"].ToString();
                }
                if (row["OrdersStatus"] != null)
                {
                    model.OrdersStatus = row["OrdersStatus"].ToString();
                }
                if (row["WriteOff_Code"] != null)
                {
                    model.WriteOff_Code = row["WriteOff_Code"].ToString();
                }
                if (row["WriteOffStatus"] != null)
                {
                    model.WriteOffStatus = row["WriteOffStatus"].ToString();
                }
                if (row["Materials_Name"] != null)
                {
                    model.Materials_Name = row["Materials_Name"].ToString();
                }
                if (row["MaterialsWh_Name"] != null)
                {
                    model.MaterialsWh_Name = row["MaterialsWh_Name"].ToString();
                }
                if (row["MaterialsStock_Code"] != null)
                {
                    model.MaterialsStock_Code = row["MaterialsStock_Code"].ToString();
                }
                if (row["M_Buy_Detail_Code"] != null)
                {
                    model.M_Buy_Detail_Code = row["M_Buy_Detail_Code"].ToString();
                }
                if (row["MaterialsLot"] != null)
                {
                    model.MaterialsLot = row["MaterialsLot"].ToString();
                }
                if (row["MaterialsExpiryDate"] != null && row["MaterialsExpiryDate"].ToString() != "")
                {
                    model.MaterialsExpiryDate = DateTime.Parse(row["MaterialsExpiryDate"].ToString());
                }
                if (row["M_Buy_Detail_Code"] != null)
                {
                    model.M_Buy_Detail_Code = row["M_Buy_Detail_Code"].ToString();
                }
                if (row["Pack_Unit"] != null)
                {
                    model.Pack_Unit = row["Pack_Unit"].ToString();
                }
                if (row["Bulk_Unit"] != null)
                {
                    model.Bulk_Unit = row["Bulk_Unit"].ToString();
                }
                if (row["M_BuyDetail_Memo"] != null)
                {
                    model.M_BuyDetail_Memo = row["M_BuyDetail_Memo"].ToString();
                }
                if (row["Convert_Ratio"] != null && row["Convert_Ratio"].ToString() != "")
                {
                    model.Convert_Ratio = int.Parse(row["Convert_Ratio"].ToString());
                }

            }
            return model;
        }
        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select M_Buy_Code,Materials_Sup_Dict.MaterialsSup_Code,MaterialsSup_Name,Materials_Warehouse_Dict.MaterialsWh_Code,MaterialsWh_Name,Order_Date,Arrival_Date,Input_Date,Finish_Date,Materials_Buy_In1.Jsr_Code,Jsr_Name,TotalMoney,M_Buy_Memo,OrdersStatus,WriteOff_Code,WriteOffStatus ");
            strSql.Append(" FROM dbo.Materials_Buy_In1  ,dbo.Materials_Sup_Dict ,Materials_Warehouse_Dict ,dbo.Zd_YyJsr where Materials_Buy_In1.MaterialsSup_Code = Materials_Sup_Dict.MaterialsSup_Code  AND Materials_Buy_In1.MaterialsWh_Code = Materials_Warehouse_Dict.MaterialsWh_Code AND Materials_Buy_In1.Jsr_Code = Zd_YyJsr.Jsr_Code ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" and  " + strWhere);
            }
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" M_Buy_Code,MaterialsSup_Code,MaterialsWh_Code,Order_Date,Arrival_Date,Input_Date,Finish_Date,Jsr_Code,TotalMoney,M_Buy_Memo,OrdersStatus,WriteOff_Code,WriteOffStatus ");
            strSql.Append(" FROM Materials_Buy_In1 ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM Materials_Buy_In1 ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.M_Buy_Code desc");
            }
            strSql.Append(")AS Row, T.*  from Materials_Buy_In1 T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Materials_Buy_In1";
			parameters[1].Value = "M_Buy_Code";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return HisVar.HisVar.Sqldal.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod
        #region  ExtensionMethod

        #endregion  ExtensionMethod
    }
}

