﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="0" />
    <DataSources isList="true" count="1">
      <view药库药房出入库查询 Ref="2" type="Stimulsoft.Report.Dictionary.StiDataViewSource" isKey="true">
        <Alias>view药库药房出入库查询</Alias>
        <Columns isList="true" count="25">
          <value>Crk_Date,System.DateTime</value>
          <value>Crk_Code,System.String</value>
          <value>Xx_code,System.String</value>
          <value>Kh_Name,System.String</value>
          <value>Jsr_Name,System.String</value>
          <value>Dl_Name,System.String</value>
          <value>Yp_Name,System.String</value>
          <value>Yp_Jc,System.String</value>
          <value>Mx_Gg,System.String</value>
          <value>Mx_Cd,System.String</value>
          <value>Jx_Name,System.String</value>
          <value>Sl,System.Decimal</value>
          <value>Mx_Cgj,System.Decimal</value>
          <value>Cg_Money,System.Decimal</value>
          <value>Mx_Xsj,System.Decimal</value>
          <value>Xs_Money,System.Decimal</value>
          <value>Mx_Pfj,System.Decimal</value>
          <value>Pf_Money,System.Decimal</value>
          <value>Tk_Dj,System.Int32</value>
          <value>Tk_Money,System.Int32</value>
          <value>Yp_ph,System.String</value>
          <value>Yp_Yxq,System.DateTime</value>
          <value>IsJb,System.String</value>
          <value>Dl_Code,System.String</value>
          <value>Dw,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>view药库药房出入库查询</Name>
        <NameInSource>view药库药房出入库查询</NameInSource>
      </view药库药房出入库查询>
    </DataSources>
    <Relations isList="true" count="0" />
    <Report isRef="0" />
    <Variables isList="true" count="4">
      <value>,查询时间,查询时间,System.String,,False,False</value>
      <value>,打印时间,打印时间,System.String,,False,False</value>
      <value>,制表人,制表人,System.String,,False,False</value>
      <value>,标题,标题,System.String,,False,False</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="3" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="5">
        <PageFooterBand1 Ref="4" type="PageFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,27.2,19,0.5</ClientRectangle>
          <Components isList="true" count="1">
            <Text15 Ref="5" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,19,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,0</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text15</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>第{PageNumber}页</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text15>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>PageFooterBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </PageFooterBand1>
        <ReportTitleBand1 Ref="6" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,19,1.9</ClientRectangle>
          <Components isList="true" count="4">
            <Text1 Ref="7" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,19,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>黑体,15,Regular,Point,False,134</Font>
              <Guid>e5b675b6f236412e9f66e398ac9f7bfd</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text1</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Text>{标题}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text1>
            <Text3 Ref="8" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.7,9.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <Guid>00ba8becd6a94971815d0706462ad849</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text3</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Text>{查询时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text3>
            <Text5 Ref="9" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>9.6,0.7,4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <Guid>51df8bfbfd214ce0b7b96d11f84d551d</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text5</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Text>{制表人}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text5>
            <Text7 Ref="10" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>13.6,0.7,5.4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <Guid>af8d109b6bca46cbb24db6831ba68382</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text7</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Text>{打印时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text7>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>ReportTitleBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </ReportTitleBand1>
        <GroupHeaderBand1 Ref="11" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,3.1,19,0</ClientRectangle>
          <Components isList="true" count="0" />
          <Conditions isList="true" count="0" />
          <Enabled>False</Enabled>
          <Name>GroupHeaderBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </GroupHeaderBand1>
        <DataBand1 Ref="12" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,3.9,19,0</ClientRectangle>
          <Components isList="true" count="0" />
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>view药库药房出入库查询</DataSourceName>
          <Filters isList="true" count="0" />
          <Name>DataBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Sort isList="true" count="0" />
        </DataBand1>
        <GroupFooterBand1 Ref="13" type="GroupFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <CanBreak>True</CanBreak>
          <ClientRectangle>0,4.7,19,4.6</ClientRectangle>
          <Components isList="true" count="37">
            <Text9 Ref="14" type="Text" isKey="true">
              <Border>Left, Right, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.5,3,3.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>d1c118ea02554f9199003785dc8832e0</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text9</Name>
              <Page isRef="3" />
              <Parent isRef="13" />
              <Text>{Sum(DataBand1,view药库药房出入库查询.Cg_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="15" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text9>
            <Text10 Ref="16" type="Text" isKey="true">
              <Border>Right, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>5.7,3,3.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>7fc59a3e45b9449db4ead56fe9c263ad</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text10</Name>
              <Page isRef="3" />
              <Parent isRef="13" />
              <Text>{Sum(DataBand1,view药库药房出入库查询.Xs_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="17" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text10>
            <Text16 Ref="18" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0.5,3,2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>fa22f7ce2b074a939830cc2279d211b2</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text16</Name>
              <Page isRef="3" />
              <Parent isRef="13" />
              <Text>合计</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text16>
            <Text28 Ref="19" type="Text" isKey="true">
              <Border>Left, Right, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.5,0.6,3.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>6d3b890363dd4d3f9f94b5a9f05e55eb</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text28</Name>
              <Page isRef="3" />
              <Parent isRef="13" />
              <Text>{SumIf(DataBand1,view药库药房出入库查询.Cg_Money,view药库药房出入库查询.Dl_Name=="西药")}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="20" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text28>
            <Text29 Ref="21" type="Text" isKey="true">
              <Border>Left, Right, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.5,1.2,3.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>3f8342f5e3704075ba0bac940c1c56cd</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text29</Name>
              <Page isRef="3" />
              <Parent isRef="13" />
              <Text>{SumIf(DataBand1,view药库药房出入库查询.Cg_Money,view药库药房出入库查询.Dl_Name=="中成药")}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="22" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text29>
            <Text30 Ref="23" type="Text" isKey="true">
              <Border>Left, Right, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.5,1.8,3.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>9752642466d2429a908b66128b04d12e</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text30</Name>
              <Page isRef="3" />
              <Parent isRef="13" />
              <Text>{SumIf(DataBand1,view药库药房出入库查询.Cg_Money,view药库药房出入库查询.Dl_Name=="中草药")}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="24" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text30>
            <Text31 Ref="25" type="Text" isKey="true">
              <Border>Left, Right, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.5,2.4,3.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>816a5ba42ef84135befc0efb9d72cc16</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text31</Name>
              <Page isRef="3" />
              <Parent isRef="13" />
              <Text>{SumIf(DataBand1,view药库药房出入库查询.Cg_Money,view药库药房出入库查询.Dl_Name=="卫生材料")}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="26" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text31>
            <Text32 Ref="27" type="Text" isKey="true">
              <Border>Right, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>5.7,0.6,3.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>128884f32b9f45d9ab6c7e6f4ade40bb</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text32</Name>
              <Page isRef="3" />
              <Parent isRef="13" />
              <Text>{SumIf(DataBand1,view药库药房出入库查询.Xs_Money,view药库药房出入库查询.Dl_Name=="西药")}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="28" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text32>
            <Text33 Ref="29" type="Text" isKey="true">
              <Border>Right, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>5.7,1.2,3.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>3ec7bb571a614e2291a503437edc32cc</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text33</Name>
              <Page isRef="3" />
              <Parent isRef="13" />
              <Text>{SumIf(DataBand1,view药库药房出入库查询.Xs_Money,view药库药房出入库查询.Dl_Name=="中成药")}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="30" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text33>
            <Text34 Ref="31" type="Text" isKey="true">
              <Border>Right, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>5.7,1.8,3.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>037132e9fc9842c0926da9496134195d</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text34</Name>
              <Page isRef="3" />
              <Parent isRef="13" />
              <Text>{SumIf(DataBand1,view药库药房出入库查询.Xs_Money,view药库药房出入库查询.Dl_Name=="中草药")}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="32" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text34>
            <Text35 Ref="33" type="Text" isKey="true">
              <Border>Right, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>5.7,2.4,3.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>2e564d725b3e4450a6202653977949d7</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text35</Name>
              <Page isRef="3" />
              <Parent isRef="13" />
              <Text>{SumIf(DataBand1,view药库药房出入库查询.Xs_Money,view药库药房出入库查询.Dl_Name=="卫生材料")}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="34" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text35>
            <Text42 Ref="35" type="Text" isKey="true">
              <Border>Right, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>12.1,3,3.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>b4dbb9ac93284122a4be8cce34be93af</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text42</Name>
              <Page isRef="3" />
              <Parent isRef="13" />
              <Text>{Sum(DataBand1,view药库药房出入库查询.Xs_Money)-Sum(DataBand1,view药库药房出入库查询.Cg_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="36" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text42>
            <Text43 Ref="37" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>8.9,3,3.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>2bcef49fc7d54c7f85228dbc9ed998ef</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text43</Name>
              <Page isRef="3" />
              <Parent isRef="13" />
              <Text>{Sum(DataBand1,view药库药房出入库查询.Pf_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="38" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text43>
            <Text44 Ref="39" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>8.9,0.6,3.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>58023f31debf4e1aa2a6e9c7f19c5bf9</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text44</Name>
              <Page isRef="3" />
              <Parent isRef="13" />
              <Text>{SumIf(DataBand1,view药库药房出入库查询.Pf_Money,view药库药房出入库查询.Dl_Name=="西药")}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="40" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text44>
            <Text45 Ref="41" type="Text" isKey="true">
              <Border>Top, Right, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>8.9,1.2,3.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>2712defbc851459bab80039af7958a3e</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text45</Name>
              <Page isRef="3" />
              <Parent isRef="13" />
              <Text>{SumIf(DataBand1,view药库药房出入库查询.Pf_Money,view药库药房出入库查询.Dl_Name=="中成药")}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="42" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text45>
            <Text46 Ref="43" type="Text" isKey="true">
              <Border>Top, Right, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>8.9,1.8,3.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>5a6ea6426f204a2eab3ac0d2a7187c6d</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text46</Name>
              <Page isRef="3" />
              <Parent isRef="13" />
              <Text>{SumIf(DataBand1,view药库药房出入库查询.Pf_Money,view药库药房出入库查询.Dl_Name=="中草药")}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="44" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text46>
            <Text47 Ref="45" type="Text" isKey="true">
              <Border>Top, Right, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>8.9,2.4,3.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>4c7e77dd17944cee8c1a4d4f90241a8b</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text47</Name>
              <Page isRef="3" />
              <Parent isRef="13" />
              <Text>{SumIf(DataBand1,view药库药房出入库查询.Pf_Money,view药库药房出入库查询.Dl_Name=="卫生材料")}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="46" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text47>
            <Text49 Ref="47" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0.5,0.6,2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>d92eb08ff94e46d48bcb3f17ba1a3c8f</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text49</Name>
              <Page isRef="3" />
              <Parent isRef="13" />
              <Text>西药</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text49>
            <Text50 Ref="48" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0.5,1.2,2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>4bdd2980ac6e48b482c78e828da27530</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text50</Name>
              <Page isRef="3" />
              <Parent isRef="13" />
              <Text>中成药</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text50>
            <Text51 Ref="49" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0.5,1.8,2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>e958c575e2c44d48805ea5bdeef3f024</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text51</Name>
              <Page isRef="3" />
              <Parent isRef="13" />
              <Text>中草药</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text51>
            <Text52 Ref="50" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0.5,2.4,2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>97bf9ea3e4044d8b89f8c4afdfcec1a8</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text52</Name>
              <Page isRef="3" />
              <Parent isRef="13" />
              <Text>卫生材料</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text52>
            <Text4 Ref="51" type="Text" isKey="true">
              <Border>Right, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>12.1,0.6,3.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>d12889ba32064d62881b9df330cb402e</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text4</Name>
              <Page isRef="3" />
              <Parent isRef="13" />
              <Text>{SumIf(DataBand1,view药库药房出入库查询.Xs_Money,view药库药房出入库查询.Dl_Name=="西药")-SumIf(DataBand1,view药库药房出入库查询.Cg_Money,view药库药房出入库查询.Dl_Name=="西药")}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="52" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text4>
            <Text8 Ref="53" type="Text" isKey="true">
              <Border>Right, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>12.1,1.2,3.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>86c7ef02170345bb8e932e49266affcd</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text8</Name>
              <Page isRef="3" />
              <Parent isRef="13" />
              <Text>{SumIf(DataBand1,view药库药房出入库查询.Xs_Money,view药库药房出入库查询.Dl_Name=="中成药")-SumIf(DataBand1,view药库药房出入库查询.Cg_Money,view药库药房出入库查询.Dl_Name=="中成药")}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="54" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text8>
            <Text11 Ref="55" type="Text" isKey="true">
              <Border>Right, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>12.1,1.8,3.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>4f59c519df644198b4d29398b6b81d6b</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text11</Name>
              <Page isRef="3" />
              <Parent isRef="13" />
              <Text>{SumIf(DataBand1,view药库药房出入库查询.Xs_Money,view药库药房出入库查询.Dl_Name=="中草药")-SumIf(DataBand1,view药库药房出入库查询.Cg_Money,view药库药房出入库查询.Dl_Name=="中草药")}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="56" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text11>
            <Text17 Ref="57" type="Text" isKey="true">
              <Border>Right, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>12.1,2.4,3.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>eb1ce4c4b4d64c49bac74fa01f937d96</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text17</Name>
              <Page isRef="3" />
              <Parent isRef="13" />
              <Text>{SumIf(DataBand1,view药库药房出入库查询.Xs_Money,view药库药房出入库查询.Dl_Name=="卫生材料")-SumIf(DataBand1,view药库药房出入库查询.Cg_Money,view药库药房出入库查询.Dl_Name=="卫生材料")}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="58" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text17>
            <Text14 Ref="59" type="Text" isKey="true">
              <Border>Right, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15.3,3,3.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>3de56aff53ea465b9169f9cc524d0b41</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text14</Name>
              <Page isRef="3" />
              <Parent isRef="13" />
              <Text>{Sum(DataBand1,view药库药房出入库查询.Pf_Money)-Sum(DataBand1,view药库药房出入库查询.Cg_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="60" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text14>
            <Text18 Ref="61" type="Text" isKey="true">
              <Border>Right, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15.3,0.6,3.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>0ab6c81d84bf4ef991a898a57bba2761</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text18</Name>
              <Page isRef="3" />
              <Parent isRef="13" />
              <Text>{SumIf(DataBand1,view药库药房出入库查询.Pf_Money,view药库药房出入库查询.Dl_Name=="西药")-SumIf(DataBand1,view药库药房出入库查询.Cg_Money,view药库药房出入库查询.Dl_Name=="西药")}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="62" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text18>
            <Text19 Ref="63" type="Text" isKey="true">
              <Border>Right, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15.3,1.2,3.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>bd49a774284f472b9e17b0685458d15d</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text19</Name>
              <Page isRef="3" />
              <Parent isRef="13" />
              <Text>{SumIf(DataBand1,view药库药房出入库查询.Pf_Money,view药库药房出入库查询.Dl_Name=="中成药")-SumIf(DataBand1,view药库药房出入库查询.Cg_Money,view药库药房出入库查询.Dl_Name=="中成药")}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="64" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text19>
            <Text20 Ref="65" type="Text" isKey="true">
              <Border>Right, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15.3,1.8,3.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>78274597ff5848558191c00ee6189f7d</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text20</Name>
              <Page isRef="3" />
              <Parent isRef="13" />
              <Text>{SumIf(DataBand1,view药库药房出入库查询.Pf_Money,view药库药房出入库查询.Dl_Name=="中草药")-SumIf(DataBand1,view药库药房出入库查询.Cg_Money,view药库药房出入库查询.Dl_Name=="中草药")}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="66" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text20>
            <Text21 Ref="67" type="Text" isKey="true">
              <Border>Right, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15.3,2.4,3.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>ffe789416bda4e978a33d97b30726cc7</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text21</Name>
              <Page isRef="3" />
              <Parent isRef="13" />
              <Text>{SumIf(DataBand1,view药库药房出入库查询.Pf_Money,view药库药房出入库查询.Dl_Name=="卫生材料")-SumIf(DataBand1,view药库药房出入库查询.Cg_Money,view药库药房出入库查询.Dl_Name=="卫生材料")}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="68" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text21>
            <Text2 Ref="69" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0.5,0,2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>93e3c5b8d61e439fa752cfecd095dab8</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text2</Name>
              <Page isRef="3" />
              <Parent isRef="13" />
              <Text>类别</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text2>
            <Text6 Ref="70" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.5,0,3.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>92d58562654a46ea82705a8607828992</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text6</Name>
              <Page isRef="3" />
              <Parent isRef="13" />
              <Text>采购金额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text6>
            <Text12 Ref="71" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>5.7,0,3.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>45009998a441499bb6d391e001f9207c</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text12</Name>
              <Page isRef="3" />
              <Parent isRef="13" />
              <Text>销售金额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text12>
            <Text13 Ref="72" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>8.9,0,3.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>3903a07f0de74eb698bd4729ba46c0e2</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text13</Name>
              <Page isRef="3" />
              <Parent isRef="13" />
              <Text>批发金额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text13>
            <Text25 Ref="73" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>12.1,0,3.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>56fee9f209e04b1199c8ffccaa42bf7d</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text25</Name>
              <Page isRef="3" />
              <Parent isRef="13" />
              <Text>采销差额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text25>
            <Text26 Ref="74" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15.3,0,3.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>18e0d630ee40491e945e343fa4c0def7</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text26</Name>
              <Page isRef="3" />
              <Parent isRef="13" />
              <Text>采批差额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text26>
            <CrossTab1 Ref="75" type="Stimulsoft.Report.CrossTab.StiCrossTab" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,4.6,19,7.1</ClientRectangle>
              <Components isList="true" count="13">
                <CrossTab1_RowTotal1 Ref="76" type="CrossRowTotal" isKey="true">
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>[255:255:255]</Brush>
                  <ClientRectangle>0,2.1,1.7,0</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,8</Font>
                  <Guid>8c1fb41a526f48138d2bf06737ee0307</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_RowTotal1</Name>
                  <Page isRef="3" />
                  <Parent isRef="75" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <Text>Total</Text>
                  <TextBrush>Black</TextBrush>
                </CrossTab1_RowTotal1>
                <CrossTab1_Row1_Title Ref="77" type="CrossTitle" isKey="true">
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>White</Brush>
                  <ClientRectangle>0,0.55,3,0.5</ClientRectangle>
                  <Font>宋体,10.5,Regular,Point,False,134</Font>
                  <Guid>2369de3b608f4fe2b1a424537e35a1f1</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_Row1_Title</Name>
                  <Page isRef="3" />
                  <Parent isRef="75" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <Text>客户</Text>
                  <TextBrush>[105:105:105]</TextBrush>
                  <TypeOfComponent>Row:CrossTab1_Row1</TypeOfComponent>
                </CrossTab1_Row1_Title>
                <CrossTab1_ColTotal1 Ref="78" type="CrossColumnTotal" isKey="true">
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>[255:255:255]</Brush>
                  <ClientRectangle>4.65,0.55,0.9,0.5</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>宋体,10.5,Regular,Point,False,134</Font>
                  <Guid>aafd8527e8894ce7846a45d01a4b63b7</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_ColTotal1</Name>
                  <Page isRef="3" />
                  <Parent isRef="75" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <Text>合计</Text>
                  <TextBrush>Black</TextBrush>
                </CrossTab1_ColTotal1>
                <CrossTab1_LeftTitle Ref="79" type="CrossTitle" isKey="true">
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>White</Brush>
                  <ClientRectangle>0,0,3,0.5</ClientRectangle>
                  <Font>Arial,8</Font>
                  <Guid>f2a567a368074514b8d79529bbe42bf2</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_LeftTitle</Name>
                  <Page isRef="3" />
                  <Parent isRef="75" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <TextBrush>[105:105:105]</TextBrush>
                  <Type>Expression</Type>
                  <TypeOfComponent>LeftTitle</TypeOfComponent>
                </CrossTab1_LeftTitle>
                <CrossTab1_SumHeader1 Ref="80" type="Stimulsoft.Report.CrossTab.StiCrossSummaryHeader" isKey="true">
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>White</Brush>
                  <ClientRectangle>1.4,1.1,1.6,0.5</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>宋体,10.5,Regular,Point,False,134</Font>
                  <Guid>36e20fbb95b9478ca1dd012d6311c42b</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_SumHeader1</Name>
                  <Page isRef="3" />
                  <Parent isRef="75" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <Text>采购金额</Text>
                  <TextBrush>[105:105:105]</TextBrush>
                </CrossTab1_SumHeader1>
                <CrossTab1_SumHeader2 Ref="81" type="Stimulsoft.Report.CrossTab.StiCrossSummaryHeader" isKey="true">
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>White</Brush>
                  <ClientRectangle>1.4,1.6,1.6,0.5</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>宋体,10.5,Regular,Point,False,134</Font>
                  <Guid>b7296c4b98f84c1cb43ab7c5cb6361e7</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_SumHeader2</Name>
                  <Page isRef="3" />
                  <Parent isRef="75" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <Text>销售金额</Text>
                  <TextBrush>[105:105:105]</TextBrush>
                </CrossTab1_SumHeader2>
                <CrossTab1_SumHeader3 Ref="82" type="Stimulsoft.Report.CrossTab.StiCrossSummaryHeader" isKey="true">
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>White</Brush>
                  <ClientRectangle>1.4,2.1,1.6,0.5</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>宋体,10.5,Regular,Point,False,134</Font>
                  <Guid>5ee46feb68dd42bbb698cd8380c59bc7</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_SumHeader3</Name>
                  <Page isRef="3" />
                  <Parent isRef="75" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <Text>批发金额</Text>
                  <TextBrush>[105:105:105]</TextBrush>
                </CrossTab1_SumHeader3>
                <CrossTab1_Row1 Ref="83" type="CrossRow" isKey="true">
                  <Alias>Kh_Name</Alias>
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>White</Brush>
                  <ClientRectangle>0,1.1,1.4,1.5</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <DisplayValue>{view药库药房出入库查询.Kh_Name}</DisplayValue>
                  <Font>宋体,9,Regular,Point,False,134</Font>
                  <Guid>6843fb43d5bf4d08b1a4b2cfdbe41570</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_Row1</Name>
                  <Page isRef="3" />
                  <Parent isRef="75" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <ShowTotal>False</ShowTotal>
                  <Text>Kh_Name</Text>
                  <TextBrush>[105:105:105]</TextBrush>
                  <TotalGuid>8c1fb41a526f48138d2bf06737ee0307</TotalGuid>
                  <Value>{view药库药房出入库查询.Kh_Name}</Value>
                </CrossTab1_Row1>
                <CrossTab1_Column1 Ref="84" type="CrossColumn" isKey="true">
                  <Alias>Dl_Name</Alias>
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>White</Brush>
                  <ClientRectangle>3.05,0.55,1.6,0.5</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <DisplayValue>{view药库药房出入库查询.Dl_Name}</DisplayValue>
                  <Font>宋体,10.5,Regular,Point,False,134</Font>
                  <Guid>d8f42b7574b246dca715c3ca56e843ba</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_Column1</Name>
                  <Page isRef="3" />
                  <Parent isRef="75" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <SortType>ByValue</SortType>
                  <Text>Dl_Name</Text>
                  <TextBrush>[105:105:105]</TextBrush>
                  <TotalGuid>aafd8527e8894ce7846a45d01a4b63b7</TotalGuid>
                  <Value>{view药库药房出入库查询.Dl_Code}</Value>
                </CrossTab1_Column1>
                <CrossTab1_Sum1 Ref="85" type="CrossSummary" isKey="true">
                  <Alias>Cg_Money</Alias>
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>[255:255:255]</Brush>
                  <ClientRectangle>3.05,1.1,1.6,0.5</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>宋体,10.5,Regular,Point,False,134</Font>
                  <Guid>f28b3543cb0e42a1b89cdd6192351e78</Guid>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_Sum1</Name>
                  <Page isRef="3" />
                  <Parent isRef="75" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <Text>0</Text>
                  <TextBrush>Black</TextBrush>
                  <TextFormat Ref="86" type="CustomFormat" isKey="true">
                    <StringFormat>0.00##</StringFormat>
                  </TextFormat>
                  <Value>{view药库药房出入库查询.Cg_Money}</Value>
                </CrossTab1_Sum1>
                <CrossTab1_Sum2 Ref="87" type="CrossSummary" isKey="true">
                  <Alias>Xs_Money</Alias>
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>[255:255:255]</Brush>
                  <ClientRectangle>3.05,1.6,1.6,0.5</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>宋体,10.5,Regular,Point,False,134</Font>
                  <Guid>e00d9848e98b46a0984e353a4705dfbf</Guid>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_Sum2</Name>
                  <Page isRef="3" />
                  <Parent isRef="75" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <Text>0</Text>
                  <TextBrush>Black</TextBrush>
                  <TextFormat Ref="88" type="CustomFormat" isKey="true">
                    <StringFormat>0.00##</StringFormat>
                  </TextFormat>
                  <Value>{view药库药房出入库查询.Xs_Money}</Value>
                </CrossTab1_Sum2>
                <CrossTab1_Sum3 Ref="89" type="CrossSummary" isKey="true">
                  <Alias>Pf_Money</Alias>
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>[255:255:255]</Brush>
                  <ClientRectangle>3.05,2.1,1.6,0.5</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>宋体,10.5,Regular,Point,False,134</Font>
                  <Guid>269bca42f28448d4bafb18c5b88a7af2</Guid>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_Sum3</Name>
                  <Page isRef="3" />
                  <Parent isRef="75" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <Text>0</Text>
                  <TextBrush>Black</TextBrush>
                  <TextFormat Ref="90" type="CustomFormat" isKey="true">
                    <StringFormat>0.00##</StringFormat>
                  </TextFormat>
                  <Value>{view药库药房出入库查询.Pf_Money}</Value>
                </CrossTab1_Sum3>
                <CrossTab1_RightTitle Ref="91" type="CrossTitle" isKey="true">
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>White</Brush>
                  <ClientRectangle>3.05,0,2.5,0.5</ClientRectangle>
                  <Font>宋体,10.5,Regular,Point,False,134</Font>
                  <Guid>df1f191579fe4ec99735f85d4f5b85e7</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_RightTitle</Name>
                  <Page isRef="3" />
                  <Parent isRef="75" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <Text>类别</Text>
                  <TextBrush>[105:105:105]</TextBrush>
                  <TypeOfComponent>RightTitle</TypeOfComponent>
                </CrossTab1_RightTitle>
              </Components>
              <Conditions isList="true" count="0" />
              <DataRelationName />
              <DataSourceName>view药库药房出入库查询</DataSourceName>
              <EmptyValue />
              <Filters isList="true" count="0" />
              <HorAlignment>Center</HorAlignment>
              <KeepCrossTabTogether>True</KeepCrossTabTogether>
              <Name>CrossTab1</Name>
              <Page isRef="3" />
              <Parent isRef="13" />
              <Sort isList="true" count="0" />
            </CrossTab1>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>GroupFooterBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </GroupFooterBand1>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>ecc777f54f114c38a326af45b37dc38a</Guid>
      <Margins>1,1,1,1</Margins>
      <Name>Page1</Name>
      <PageHeight>29.7</PageHeight>
      <PageWidth>21</PageWidth>
      <Report isRef="0" />
      <Watermark Ref="92" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page1>
  </Pages>
  <PrinterSettings Ref="93" type="Stimulsoft.Report.Print.StiPrinterSettings" isKey="true" />
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>药库采购入库查询精简汇总</ReportAlias>
  <ReportChanged>2/17/2015 9:59:28 AM</ReportChanged>
  <ReportCreated>12/29/2011 4:10:05 PM</ReportCreated>
  <ReportFile>E:\正在进行时\唐山\正在修改版\his2010\his2010\Rpt\药库药房退供应商查询清单简化.mrt</ReportFile>
  <ReportGuid>ded70459abc44aaba2600e776847fac0</ReportGuid>
  <ReportName>药库采购入库查询精简汇总</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2011.2.1026</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class Report : Stimulsoft.Report.StiReport
    {
        public Report()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
        #endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>