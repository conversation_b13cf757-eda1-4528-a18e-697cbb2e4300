﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class MblbTree
    Inherits System.Windows.Forms.Form

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(MblbTree))
        Me.ContextMenuStrip1 = New System.Windows.Forms.ContextMenuStrip(Me.components)
        Me.AddNode = New System.Windows.Forms.ToolStripMenuItem()
        Me.UpdateNode = New System.Windows.Forms.ToolStripMenuItem()
        Me.Image1 = New System.Windows.Forms.ImageList(Me.components)
        Me.EnsureButton1 = New CustomControl.MyButton()
        Me.SearchMyButton1 = New CustomControl.MyButton()
        Me.NameTextBox1 = New CustomControl.MyTextBox()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.TreeView1 = New System.Windows.Forms.TreeView()
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel()
        Me.ContextMenuStrip1.SuspendLayout()
        Me.Panel1.SuspendLayout()
        Me.TableLayoutPanel1.SuspendLayout()
        Me.SuspendLayout()
        '
        'ContextMenuStrip1
        '
        Me.ContextMenuStrip1.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.AddNode, Me.UpdateNode})
        Me.ContextMenuStrip1.Name = "ContextMenuStrip1"
        Me.ContextMenuStrip1.Size = New System.Drawing.Size(101, 48)
        '
        'AddNode
        '
        Me.AddNode.Image = CType(resources.GetObject("AddNode.Image"), System.Drawing.Image)
        Me.AddNode.Name = "AddNode"
        Me.AddNode.Size = New System.Drawing.Size(100, 22)
        Me.AddNode.Text = "增加"
        '
        'UpdateNode
        '
        Me.UpdateNode.Image = CType(resources.GetObject("UpdateNode.Image"), System.Drawing.Image)
        Me.UpdateNode.Name = "UpdateNode"
        Me.UpdateNode.Size = New System.Drawing.Size(100, 22)
        Me.UpdateNode.Text = "修改"
        '
        'Image1
        '
        Me.Image1.ImageStream = CType(resources.GetObject("Image1.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.Image1.TransparentColor = System.Drawing.Color.White
        Me.Image1.Images.SetKeyName(0, "")
        Me.Image1.Images.SetKeyName(1, "")
        Me.Image1.Images.SetKeyName(2, "")
        '
        'EnsureButton1
        '
        Me.EnsureButton1.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.EnsureButton1.DialogResult = System.Windows.Forms.DialogResult.None
        Me.EnsureButton1.Location = New System.Drawing.Point(302, 5)
        Me.EnsureButton1.Name = "EnsureButton1"
        Me.EnsureButton1.Size = New System.Drawing.Size(70, 27)
        Me.EnsureButton1.TabIndex = 2
        Me.EnsureButton1.Text = "确定"
        '
        'SearchMyButton1
        '
        Me.SearchMyButton1.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.SearchMyButton1.DialogResult = System.Windows.Forms.DialogResult.None
        Me.SearchMyButton1.Location = New System.Drawing.Point(226, 5)
        Me.SearchMyButton1.Name = "SearchMyButton1"
        Me.SearchMyButton1.Size = New System.Drawing.Size(70, 27)
        Me.SearchMyButton1.TabIndex = 1
        Me.SearchMyButton1.Text = "查询"
        '
        'NameTextBox1
        '
        Me.NameTextBox1.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.NameTextBox1.Captain = "类别名称"
        Me.NameTextBox1.CaptainBackColor = System.Drawing.Color.Transparent
        Me.NameTextBox1.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.NameTextBox1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.NameTextBox1.CaptainWidth = 60.0!
        Me.NameTextBox1.ContentForeColor = System.Drawing.Color.Black
        Me.NameTextBox1.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.NameTextBox1.Location = New System.Drawing.Point(9, 5)
        Me.NameTextBox1.Multiline = False
        Me.NameTextBox1.Name = "NameTextBox1"
        Me.NameTextBox1.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.NameTextBox1.ReadOnly = False
        Me.NameTextBox1.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.NameTextBox1.SelectionStart = 0
        Me.NameTextBox1.SelectStart = 0
        Me.NameTextBox1.Size = New System.Drawing.Size(204, 27)
        Me.NameTextBox1.TabIndex = 0
        Me.NameTextBox1.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.NameTextBox1.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        '
        'Panel1
        '
        Me.Panel1.Controls.Add(Me.NameTextBox1)
        Me.Panel1.Controls.Add(Me.SearchMyButton1)
        Me.Panel1.Controls.Add(Me.EnsureButton1)
        Me.Panel1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel1.Location = New System.Drawing.Point(3, 3)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(379, 34)
        Me.Panel1.TabIndex = 1
        '
        'TreeView1
        '
        Me.TreeView1.ContextMenuStrip = Me.ContextMenuStrip1
        Me.TreeView1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TreeView1.ImageIndex = 0
        Me.TreeView1.ImageList = Me.Image1
        Me.TreeView1.Location = New System.Drawing.Point(3, 43)
        Me.TreeView1.Name = "TreeView1"
        Me.TreeView1.SelectedImageIndex = 0
        Me.TreeView1.Size = New System.Drawing.Size(379, 362)
        Me.TreeView1.TabIndex = 0
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.ColumnCount = 1
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanel1.Controls.Add(Me.TreeView1, 0, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.Panel1, 0, 0)
        Me.TableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(0, 0)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.RowCount = 2
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 40.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(385, 408)
        Me.TableLayoutPanel1.TabIndex = 2
        '
        'MblbTree
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(385, 408)
        Me.Controls.Add(Me.TableLayoutPanel1)
        Me.Name = "MblbTree"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent
        Me.Text = "模板类别选择"
        Me.ContextMenuStrip1.ResumeLayout(False)
        Me.Panel1.ResumeLayout(False)
        Me.TableLayoutPanel1.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents ContextMenuStrip1 As System.Windows.Forms.ContextMenuStrip
    Friend WithEvents AddNode As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents UpdateNode As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents SearchMyButton1 As CustomControl.MyButton
    Friend WithEvents NameTextBox1 As CustomControl.MyTextBox
    Friend WithEvents Image1 As System.Windows.Forms.ImageList
    Friend WithEvents EnsureButton1 As CustomControl.MyButton
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents TreeView1 As System.Windows.Forms.TreeView
    Friend WithEvents TableLayoutPanel1 As System.Windows.Forms.TableLayoutPanel
End Class
