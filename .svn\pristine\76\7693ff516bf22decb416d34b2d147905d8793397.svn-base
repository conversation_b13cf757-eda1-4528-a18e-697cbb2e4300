﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class Zd_MzFp1
    Inherits HisControl.BaseForm

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(Zd_MzFp1))
        Me.C1Holder1 = New C1.Win.C1Command.C1CommandHolder()
        Me.Comm1 = New C1.Win.C1Command.C1Command()
        Me.Comm2 = New C1.Win.C1Command.C1Command()
        Me.Comm3 = New C1.Win.C1Command.C1Command()
        Me.C1TrueDBGrid1 = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.TreeView1 = New System.Windows.Forms.TreeView()
        Me.Image1 = New System.Windows.Forms.ImageList(Me.components)
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.C1Button3 = New C1.Win.C1Input.C1Button()
        Me.ToolBar1 = New C1.Win.C1Command.C1ToolBar()
        Me.Link1 = New C1.Win.C1Command.C1CommandLink()
        Me.Link2 = New C1.Win.C1Command.C1CommandLink()
        Me.Link3 = New C1.Win.C1Command.C1CommandLink()
        Me.T_Line1 = New System.Windows.Forms.Label()
        Me.T_Line2 = New System.Windows.Forms.Label()
        Me.T_Combo = New C1.Win.C1List.C1Combo()
        Me.T_Textbox = New C1.Win.C1Input.C1TextBox()
        Me.T_Label = New C1.Win.C1Input.C1Label()
        CType(Me.C1Holder1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1TrueDBGrid1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Panel1.SuspendLayout()
        CType(Me.C1Button3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T_Combo, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T_Textbox, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T_Label, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'C1Holder1
        '
        Me.C1Holder1.Commands.Add(Me.Comm1)
        Me.C1Holder1.Commands.Add(Me.Comm2)
        Me.C1Holder1.Commands.Add(Me.Comm3)
        Me.C1Holder1.Owner = Me
        '
        'Comm1
        '
        Me.Comm1.Image = CType(resources.GetObject("Comm1.Image"), System.Drawing.Image)
        Me.Comm1.Name = "Comm1"
        Me.Comm1.Shortcut = System.Windows.Forms.Shortcut.F2
        Me.Comm1.ShortcutText = ""
        Me.Comm1.Text = "增加"
        Me.Comm1.ToolTipText = "增加记录"
        '
        'Comm2
        '
        Me.Comm2.Image = CType(resources.GetObject("Comm2.Image"), System.Drawing.Image)
        Me.Comm2.Name = "Comm2"
        Me.Comm2.Shortcut = System.Windows.Forms.Shortcut.F3
        Me.Comm2.ShortcutText = ""
        Me.Comm2.Text = "删除"
        Me.Comm2.ToolTipText = "删除记录"
        '
        'Comm3
        '
        Me.Comm3.Image = CType(resources.GetObject("Comm3.Image"), System.Drawing.Image)
        Me.Comm3.Name = "Comm3"
        Me.Comm3.Shortcut = System.Windows.Forms.Shortcut.F4
        Me.Comm3.ShortcutText = ""
        Me.Comm3.Text = "更新"
        Me.Comm3.ToolTipText = "更新记录"
        '
        'C1TrueDBGrid1
        '
        Me.C1TrueDBGrid1.GroupByCaption = "Drag a column header here to group by that column"
        Me.C1TrueDBGrid1.Images.Add(CType(resources.GetObject("C1TrueDBGrid1.Images"), System.Drawing.Image))
        Me.C1TrueDBGrid1.Location = New System.Drawing.Point(226, 74)
        Me.C1TrueDBGrid1.Name = "C1TrueDBGrid1"
        Me.C1TrueDBGrid1.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.C1TrueDBGrid1.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.C1TrueDBGrid1.PreviewInfo.ZoomFactor = 75.0R
        Me.C1TrueDBGrid1.PrintInfo.MeasurementDevice = C1.Win.C1TrueDBGrid.PrintInfo.MeasurementDeviceEnum.Screen
        Me.C1TrueDBGrid1.PrintInfo.MeasurementPrinterName = Nothing
        Me.C1TrueDBGrid1.PrintInfo.PageSettings = CType(resources.GetObject("C1TrueDBGrid1.PrintInfo.PageSettings"), System.Drawing.Printing.PageSettings)
        Me.C1TrueDBGrid1.Size = New System.Drawing.Size(211, 182)
        Me.C1TrueDBGrid1.TabIndex = 5
        Me.C1TrueDBGrid1.Text = "C1TrueDBGrid1"
        Me.C1TrueDBGrid1.UseCompatibleTextRendering = False
        Me.C1TrueDBGrid1.PropBag = resources.GetString("C1TrueDBGrid1.PropBag")
        '
        'TreeView1
        '
        Me.TreeView1.Dock = System.Windows.Forms.DockStyle.Left
        Me.TreeView1.FullRowSelect = True
        Me.TreeView1.HotTracking = True
        Me.TreeView1.ImageIndex = 0
        Me.TreeView1.ImageList = Me.Image1
        Me.TreeView1.Location = New System.Drawing.Point(0, 25)
        Me.TreeView1.Name = "TreeView1"
        Me.TreeView1.SelectedImageIndex = 0
        Me.TreeView1.Size = New System.Drawing.Size(167, 434)
        Me.TreeView1.TabIndex = 7
        '
        'Image1
        '
        Me.Image1.ImageStream = CType(resources.GetObject("Image1.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.Image1.TransparentColor = System.Drawing.Color.White
        Me.Image1.Images.SetKeyName(0, "")
        Me.Image1.Images.SetKeyName(1, "")
        Me.Image1.Images.SetKeyName(2, "")
        '
        'Panel1
        '
        Me.Panel1.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Panel1.Controls.Add(Me.C1Button3)
        Me.Panel1.Controls.Add(Me.ToolBar1)
        Me.Panel1.Controls.Add(Me.T_Line1)
        Me.Panel1.Controls.Add(Me.T_Line2)
        Me.Panel1.Controls.Add(Me.T_Combo)
        Me.Panel1.Controls.Add(Me.T_Textbox)
        Me.Panel1.Controls.Add(Me.T_Label)
        Me.Panel1.Dock = System.Windows.Forms.DockStyle.Top
        Me.Panel1.Location = New System.Drawing.Point(0, 0)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(726, 25)
        Me.Panel1.TabIndex = 6
        '
        'C1Button3
        '
        Me.C1Button3.Location = New System.Drawing.Point(569, 1)
        Me.C1Button3.Name = "C1Button3"
        Me.C1Button3.Size = New System.Drawing.Size(143, 21)
        Me.C1Button3.TabIndex = 22
        Me.C1Button3.Text = "门诊发票第三联分类"
        Me.C1Button3.UseVisualStyleBackColor = True
        Me.C1Button3.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.C1Button3.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'ToolBar1
        '
        Me.ToolBar1.AccessibleName = "Tool Bar"
        Me.ToolBar1.CommandHolder = Me.C1Holder1
        Me.ToolBar1.CommandLinks.AddRange(New C1.Win.C1Command.C1CommandLink() {Me.Link1, Me.Link2, Me.Link3})
        Me.ToolBar1.Location = New System.Drawing.Point(1, 0)
        Me.ToolBar1.MinButtonSize = 22
        Me.ToolBar1.Movable = False
        Me.ToolBar1.Name = "ToolBar1"
        Me.ToolBar1.Size = New System.Drawing.Size(165, 22)
        Me.ToolBar1.Text = "C1ToolBar1"
        Me.ToolBar1.VisualStyle = C1.Win.C1Command.VisualStyle.Classic
        Me.ToolBar1.VisualStyleBase = C1.Win.C1Command.VisualStyle.Classic
        '
        'Link1
        '
        Me.Link1.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image), C1.Win.C1Command.ButtonLookFlags)
        Me.Link1.Command = Me.Comm1
        '
        'Link2
        '
        Me.Link2.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image), C1.Win.C1Command.ButtonLookFlags)
        Me.Link2.Command = Me.Comm2
        Me.Link2.SortOrder = 1
        Me.Link2.Text = "删除"
        '
        'Link3
        '
        Me.Link3.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image), C1.Win.C1Command.ButtonLookFlags)
        Me.Link3.Command = Me.Comm3
        Me.Link3.SortOrder = 2
        '
        'T_Line1
        '
        Me.T_Line1.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.T_Line1.Location = New System.Drawing.Point(172, 0)
        Me.T_Line1.Name = "T_Line1"
        Me.T_Line1.Size = New System.Drawing.Size(2, 27)
        Me.T_Line1.TabIndex = 4
        Me.T_Line1.Text = "Label1"
        '
        'T_Line2
        '
        Me.T_Line2.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.T_Line2.Location = New System.Drawing.Point(341, -1)
        Me.T_Line2.Name = "T_Line2"
        Me.T_Line2.Size = New System.Drawing.Size(2, 27)
        Me.T_Line2.TabIndex = 6
        Me.T_Line2.Text = "Label2"
        '
        'T_Combo
        '
        Me.T_Combo.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.T_Combo.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.T_Combo.Caption = ""
        Me.T_Combo.CaptionHeight = 17
        Me.T_Combo.CharacterCasing = System.Windows.Forms.CharacterCasing.Normal
        Me.T_Combo.ColumnCaptionHeight = 18
        Me.T_Combo.ColumnFooterHeight = 18
        Me.T_Combo.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.T_Combo.EditorBackColor = System.Drawing.SystemColors.Window
        Me.T_Combo.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.T_Combo.Images.Add(CType(resources.GetObject("T_Combo.Images"), System.Drawing.Image))
        Me.T_Combo.ItemHeight = 15
        Me.T_Combo.Location = New System.Drawing.Point(180, 3)
        Me.T_Combo.MatchEntryTimeout = CType(2000, Long)
        Me.T_Combo.MaxDropDownItems = CType(5, Short)
        Me.T_Combo.MaxLength = 32767
        Me.T_Combo.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.T_Combo.Name = "T_Combo"
        Me.T_Combo.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.T_Combo.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.T_Combo.Size = New System.Drawing.Size(76, 16)
        Me.T_Combo.TabIndex = 2
        Me.T_Combo.TabStop = False
        Me.T_Combo.PropBag = resources.GetString("T_Combo.PropBag")
        '
        'T_Textbox
        '
        Me.T_Textbox.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.T_Textbox.Location = New System.Drawing.Point(260, 3)
        Me.T_Textbox.Name = "T_Textbox"
        Me.T_Textbox.Size = New System.Drawing.Size(77, 14)
        Me.T_Textbox.TabIndex = 3
        Me.T_Textbox.TabStop = False
        Me.T_Textbox.Tag = Nothing
        Me.T_Textbox.TextDetached = True
        Me.T_Textbox.TrimStart = True
        Me.T_Textbox.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        '
        'T_Label
        '
        Me.T_Label.AutoSize = True
        Me.T_Label.BackColor = System.Drawing.SystemColors.Control
        Me.T_Label.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.T_Label.ForeColor = System.Drawing.Color.Maroon
        Me.T_Label.Location = New System.Drawing.Point(361, 5)
        Me.T_Label.Name = "T_Label"
        Me.T_Label.Size = New System.Drawing.Size(47, 12)
        Me.T_Label.TabIndex = 2
        Me.T_Label.Tag = Nothing
        Me.T_Label.Text = "T_Label"
        Me.T_Label.TextAlign = System.Drawing.ContentAlignment.BottomLeft
        Me.T_Label.TextDetached = True
        Me.T_Label.TrimStart = True
        '
        'Zd_MzFp1
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(726, 459)
        Me.Controls.Add(Me.C1TrueDBGrid1)
        Me.Controls.Add(Me.TreeView1)
        Me.Controls.Add(Me.Panel1)
        Me.Icon = CType(resources.GetObject("$this.Icon"), System.Drawing.Icon)
        Me.Name = "Zd_MzFp1"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "门诊票据分类"
        CType(Me.C1Holder1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1TrueDBGrid1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Panel1.ResumeLayout(False)
        Me.Panel1.PerformLayout()
        CType(Me.C1Button3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T_Combo, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T_Textbox, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T_Label, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents C1Holder1 As C1.Win.C1Command.C1CommandHolder
    Friend WithEvents Comm1 As C1.Win.C1Command.C1Command
    Friend WithEvents Comm2 As C1.Win.C1Command.C1Command
    Friend WithEvents Comm3 As C1.Win.C1Command.C1Command
    Friend WithEvents C1TrueDBGrid1 As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents TreeView1 As System.Windows.Forms.TreeView
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents ToolBar1 As C1.Win.C1Command.C1ToolBar
    Friend WithEvents Link1 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link2 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link3 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents T_Line1 As System.Windows.Forms.Label
    Friend WithEvents T_Line2 As System.Windows.Forms.Label
    Friend WithEvents T_Combo As C1.Win.C1List.C1Combo
    Friend WithEvents T_Textbox As C1.Win.C1Input.C1TextBox
    Friend WithEvents T_Label As C1.Win.C1Input.C1Label
    Friend WithEvents Image1 As System.Windows.Forms.ImageList
    Friend WithEvents C1Button3 As C1.Win.C1Input.C1Button
End Class
