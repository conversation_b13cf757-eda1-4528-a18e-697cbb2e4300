﻿/**  版本信息模板在安装目录下，可自行修改。
* B_Bl.cs
*
* 功 能： N/A
* 类 名： B_Bl
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/6/23 15:47:21   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Collections.Generic;
using Model;
namespace BLLOld
{
	/// <summary>
	/// B_Bl
	/// </summary>
	public partial class B_Bl
	{
        private readonly DAL.D_Bl dal = new DAL.D_Bl();
		public B_Bl()
		{}
		#region  BasicMethod
		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string Yy_code,string Bl_Code)
		{
			return dal.Exists(Yy_code,Bl_Code);
		}

		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(Model.M_Bl model)
		{
			return dal.Add(model);
		}

		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.M_Bl model)
		{
			return dal.Update(model);
		}

                /// <summary>
        /// 更新一组数据
        /// </summary>
        public bool Update(Boolean  Emr_GuiDang, string codes)
        {
            return dal.Update(Emr_GuiDang, codes);
        }
		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string Yy_code,string Bl_Code)
		{
			
			return dal.Delete(Yy_code,Bl_Code);
		}

		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.M_Bl GetModel(string Yy_code,string Bl_Code)
		{
			
			return dal.GetModel(Yy_code,Bl_Code);
		}

        ///// <summary>
        ///// 得到一个对象实体，从缓存中
        ///// </summary>
        //public Model.M_Bl GetModelByCache(string Yy_code,string Bl_Code)
        //{
			
        //    string CacheKey = "M_BlModel-" + Yy_code+Bl_Code;
        //    object objModel = Common.DataCache.GetCache(CacheKey);
        //    if (objModel == null)
        //    {
        //        try
        //        {
        //            objModel = dal.GetModel(Yy_code,Bl_Code);
        //            if (objModel != null)
        //            {
        //                int ModelCache = Common.ConfigHelper.GetConfigInt("ModelCache");
        //                Common.DataCache.SetCache(CacheKey, objModel, DateTime.Now.AddMinutes(ModelCache), TimeSpan.Zero);
        //            }
        //        }
        //        catch{}
        //    }
        //    return (Model.M_Bl)objModel;
        //}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			return dal.GetList(strWhere);
		}

        public DataSet GetListRy()
        {
            return dal.GetListRy();
        }

        public DataSet GetListCy()
        {
            return dal.GetListCy();
        }
		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			return dal.GetList(Top,strWhere,filedOrder);
		}
		/// <summary>
		/// 获得数据列表
		/// </summary>
		public List<Model.M_Bl> GetModelList(string strWhere)
		{
			DataSet ds = dal.GetList(strWhere);
			return DataTableToList(ds.Tables[0]);
		}
		/// <summary>
		/// 获得数据列表
		/// </summary>
		public List<Model.M_Bl> DataTableToList(DataTable dt)
		{
			List<Model.M_Bl> modelList = new List<Model.M_Bl>();
			int rowsCount = dt.Rows.Count;
			if (rowsCount > 0)
			{
				Model.M_Bl model;
				for (int n = 0; n < rowsCount; n++)
				{
					model = dal.DataRowToModel(dt.Rows[n]);
					if (model != null)
					{
						modelList.Add(model);
					}
				}
			}
			return modelList;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetAllList()
		{
			return GetList("");
		}

		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			return dal.GetRecordCount(strWhere);
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			return dal.GetListByPage( strWhere,  orderby,  startIndex,  endIndex);
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		//public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		//{
			//return dal.GetList(PageSize,PageIndex,strWhere);
		//}

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

