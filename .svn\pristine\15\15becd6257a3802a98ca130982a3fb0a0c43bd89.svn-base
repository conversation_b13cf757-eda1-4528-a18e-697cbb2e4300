﻿Imports System
Imports System.Data
Imports System.Data.SqlClient
Imports C1.Win.C1FlexGrid
Imports System.Math

Public Class Zd_MzFpHb3

#Region "变量初始化"
    Dim V_Col9 As Integer = 30
    Dim V_Col0 As Integer = 70
    Dim V_Col1 As Integer = 130
    Dim V_Col2 As Integer = 90
    Dim V_Str1 As String
    Dim V_Str2 As String
    Dim V_Lb_Code As String
#End Region

    Private Sub Form_Init()

        With Zd_MzFpHb1
            .My_Row = .My_Cm.List(.C1TrueDBGrid1.Row).Row
            With .My_Row
                V_Lb_Code = .Item("Lb_Code") & ""
            End With
        End With

        Dim My_Combo1 As New BaseClass.C_Combo1(Me.Combo1)
        My_Combo1.Init_TDBCombo()
        With Combo1
            .AddItem("简称")
            .AddItem("名称")
            .SelectedIndex = 0
            .DropDownWidth = 55
            .Text = "简称"
            .Width = 55
        End With
        Dim My_Combo2 As New BaseClass.C_Combo1(Me.Combo2)
        My_Combo2.Init_TDBCombo()
        With Combo2
            .AddItem("简称")
            .AddItem("名称")
            .SelectedIndex = 0
            .DropDownWidth = 55
            .Text = "简称"
            .Width = 55
        End With

        T_Textbox.Text = ""
        C1TextBox1.Text = ""
    End Sub

    Private Sub Zd_Jbjj_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load

        Form_Init()
        Flex1_Init()
        Flex2_Init()

        Call F_Reader("select Count(*) from (select 'Y'+Dl_Code as Dl_Code,Dl_Name,Dl_Jc from zd_Ml_Yp1 union all select 'X'+Xmlb_Code,Xmlb_Name,Xmlb_Jc from zd_ml_xm1)a where a.dl_code not in (select fp_code from Zd_Mzfphb2 )", "select * from (select 'Y'+Dl_Code as Dl_Code,Dl_Name,Dl_Jc from zd_Ml_Yp1 union all select 'X'+Xmlb_Code,Xmlb_Name,Xmlb_Jc from zd_ml_xm1)a where a.dl_code not in (select fp_code from Zd_Mzfphb2) order by Dl_code", C1FlexGrid1)
        Call F_Reader("select Count(*) from Zd_MzFpHb2 Where Lb_Code='" & V_Lb_Code & "'", "select Fp_Code,Fp_Name,Fp_Jc from Zd_MzFpHb1,Zd_MzFpHb2 where Zd_MzFpHb1.Lb_Code=Zd_MzFpHb2.Lb_Code and Zd_MzFpHb2.Lb_Code='" & V_Lb_Code & "' and Yy_Code = '" & HisVar.HisVar.WsyCode & "'", C1FlexGrid2)

    End Sub

    Private Sub Flex1_Init()
        With Me.C1FlexGrid1
            .Clear()

            .AllowDelete = False
            .AllowEditing = False
            .AutoResize = True
            .AllowSorting = AllowSortingEnum.SingleColumn
            .AllowDragging = AllowDraggingEnum.Columns
            .AllowResizing = C1.Win.C1FlexGrid.AllowResizingEnum.Both
            .AllowMerging = C1.Win.C1FlexGrid.AllowMergingEnum.None
            .BorderStyle = C1.Win.C1FlexGrid.Util.BaseControls.BorderStyleEnum.Fixed3D

            .ExtendLastCol = True
            .FocusRect = FocusRectEnum.None
            .SelectionMode = SelectionModeEnum.ListBox
            With .Tree
                .Column = 0
                .Indent = 20
                .Style = TreeStyleFlags.CompleteLeaf
                .LineColor = Color.DarkRed
                .LineStyle = Drawing2D.DashStyle.Solid
            End With

            '类型()
            With .Styles.Fixed
                .WordWrap = False
                .Border.Style = C1.Win.C1FlexGrid.BorderStyleEnum.Raised
                .TextAlign = TextAlignEnum.CenterCenter
                .Margins.Top = 1
                .Margins.Bottom = 0
                .BackColor = Color.FromArgb(0, 78, 152)
                .ForeColor = Color.FromArgb(255, 255, 255)
            End With

            With .Styles.Highlight
                .ForeColor = Color.FromArgb(255, 255, 255)
                .BackColor = Color.FromArgb(49, 106, 197)
            End With


            .Rows.Count = 1
            .Cols.Count = 4
            .Rows.Fixed = 1
            .Cols.Fixed = 0
            .Rows(0).Height = 20


            With .Cols(0)
                .Caption = "类别编码"
                .Width = V_Col0
                .AllowMerging = True
                .AllowDragging = True
                .TextAlign = C1.Win.C1FlexGrid.TextAlignEnum.LeftCenter
                .Style.WordWrap = True
                .Sort = C1.Win.C1FlexGrid.SortFlags.UseColSort
                .Visible = True
            End With

            With .Cols(1)
                .Caption = "类别名称"
                .Width = V_Col1
                .AllowMerging = True
                .AllowDragging = True
                .TextAlign = C1.Win.C1FlexGrid.TextAlignEnum.LeftCenter
                .Style.WordWrap = True
                .Sort = C1.Win.C1FlexGrid.SortFlags.UseColSort
                .Visible = True
            End With
            With .Cols(2)
                .Caption = "类别简称"
                .Width = V_Col2
                .AllowMerging = True
                .AllowDragging = True
                .TextAlign = C1.Win.C1FlexGrid.TextAlignEnum.LeftCenter
                .Style.WordWrap = True
                .Sort = C1.Win.C1FlexGrid.SortFlags.UseColSort
                .Visible = True
            End With
            'With .Cols(3)
            '    .Caption = "病区编码"
            '    .Width = V_Col0
            '    .AllowMerging = True
            '    .AllowDragging = True
            '    .TextAlign = C1.Win.C1FlexGrid.TextAlignEnum.LeftCenter
            '    .Style.WordWrap = True
            '    .Sort = C1.Win.C1FlexGrid.SortFlags.UseColSort
            '    .Visible = False
            'End With

            .Redraw = True
        End With

    End Sub

    Private Sub Flex2_Init()
        With Me.C1FlexGrid2
            .Clear()

            .AllowDelete = True
            .AllowEditing = False
            .AutoResize = True
            .AllowSorting = AllowSortingEnum.SingleColumn
            .AllowDragging = AllowDraggingEnum.Columns
            .AllowResizing = C1.Win.C1FlexGrid.AllowResizingEnum.Both
            .AllowMerging = C1.Win.C1FlexGrid.AllowMergingEnum.None
            .BorderStyle = C1.Win.C1FlexGrid.Util.BaseControls.BorderStyleEnum.Fixed3D

            .ExtendLastCol = True
            .FocusRect = FocusRectEnum.None
            .SelectionMode = SelectionModeEnum.ListBox
            With .Tree
                .Column = 0
                .Indent = 20
                .Style = TreeStyleFlags.CompleteLeaf
                .LineColor = Color.DarkRed
                .LineStyle = Drawing2D.DashStyle.Solid
            End With

            '类型()
            With .Styles.Fixed
                .WordWrap = False
                .Border.Style = C1.Win.C1FlexGrid.BorderStyleEnum.Raised
                .TextAlign = TextAlignEnum.CenterCenter
                .Margins.Top = 1
                .Margins.Bottom = 0
                .BackColor = Color.FromArgb(0, 78, 152)
                .ForeColor = Color.FromArgb(255, 255, 255)
            End With

            With .Styles.Highlight
                .ForeColor = Color.FromArgb(255, 255, 255)
                .BackColor = Color.FromArgb(49, 106, 197)
            End With

            .Rows.Count = 1
            .Cols.Count = 4
            .Rows.Fixed = 1
            .Cols.Fixed = 0
            .Rows(0).Height = 20


            With .Cols(0)
                .Caption = "类别编码"
                .Width = V_Col0
                .AllowMerging = True
                .AllowDragging = True
                .TextAlign = C1.Win.C1FlexGrid.TextAlignEnum.LeftCenter
                .Style.WordWrap = True
                .Sort = C1.Win.C1FlexGrid.SortFlags.UseColSort
                .Visible = True
            End With

            With .Cols(1)
                .Caption = "类别名称"
                .Width = V_Col1
                .AllowMerging = True
                .AllowDragging = True
                .TextAlign = C1.Win.C1FlexGrid.TextAlignEnum.LeftCenter
                .Style.WordWrap = True
                .Sort = C1.Win.C1FlexGrid.SortFlags.UseColSort
                .Visible = True
            End With
            With .Cols(2)
                .Caption = "类别简称"
                .Width = V_Col2
                .AllowMerging = True
                .AllowDragging = True
                .TextAlign = C1.Win.C1FlexGrid.TextAlignEnum.LeftCenter
                .Style.WordWrap = True
                .Sort = C1.Win.C1FlexGrid.SortFlags.UseColSort
                .Visible = True
            End With
            'With .Cols(3)
            '    .Caption = "病床简称"
            '    .Width = V_Col0
            '    .AllowMerging = True
            '    .AllowDragging = True
            '    .TextAlign = C1.Win.C1FlexGrid.TextAlignEnum.LeftCenter
            '    .Style.WordWrap = True
            '    .Sort = C1.Win.C1FlexGrid.SortFlags.UseColSort
            '    .Visible = True
            'End With

            .Redraw = True
        End With

    End Sub

    Private Sub F_Reader(ByVal V_Str1 As String, ByVal V_Str2 As String, ByVal Control As C1FlexGrid)
        Call P_Conn(True)
        Dim My_Cmd1 As New SqlCommand(V_Str1, My_Cn)
        Dim My_Cmd2 As New SqlCommand(V_Str2, My_Cn)
        Dim V_Count As Integer = My_Cmd1.ExecuteScalar
        Dim My_Reader As SqlDataReader = My_Cmd2.ExecuteReader
        Control.Rows.Count = V_Count + 1
        T_Label.Text = "未合并类别数=" & (C1FlexGrid1.Rows.Count.ToString - 1)
        C1Label1.Text = "包含类别数=" & (C1FlexGrid2.Rows.Count.ToString - 1)
        Dim I As Integer
        While My_Reader.Read()
            I = I + 1
            Control.Item(I, 0) = My_Reader.Item(0).ToString
            Control.Item(I, 1) = My_Reader.Item(1).ToString
            Control.Item(I, 2) = My_Reader.Item(2).ToString
            'Control.Item(I, 3) = My_Reader.Item(3).ToString
        End While
        My_Cmd1.Dispose()
        My_Cmd2.Dispose()
        My_Reader.Close()
        My_Cn.Close()


    End Sub

    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click

        Dim V_Count As Integer = C1FlexGrid1.Rows.Selected.Count

        If V_Count = 0 Then Exit Sub

        C1FlexGrid1.Redraw = False
        C1FlexGrid2.Redraw = False
        Dim J As Integer = C1FlexGrid2.Rows.Count
        C1FlexGrid2.Rows.Count = J + V_Count
        Dim R As Row
        Dim I As Integer
        For Each R In Me.C1FlexGrid1.Rows.Selected

            If Mid(R.Item(0), 1, 1) = "X" Then
                Dim m_Count As Integer
                m_Count = HisVar.HisVar.Sqldal.GetSingle("Select Count(Fp_Code) From Zd_MzFpHb2 Where Lb_Code='" & V_Lb_Code & "' And Substring(Fp_Code,1,1)='Y'")
                If m_Count > 0 Then
                    MsgBox("诊疗项目和药材不能分到同一类别")
                    T_Label.Text = "未合并类别数=" & (C1FlexGrid1.Rows.Count.ToString - 1)
                    C1Label1.Text = "包含类别数=" & (C1FlexGrid2.Rows.Count.ToString - 1)
                    C1FlexGrid1.Redraw = True
                    C1FlexGrid2.Redraw = True
                    C1FlexGrid2.Rows.Count = J + I
                    Exit Sub
                End If
            End If

            If Mid(R.Item(0), 1, 1) = "Y" Then
                Dim m_Count As Integer
                m_Count = HisVar.HisVar.Sqldal.GetSingle("Select Count(Fp_Code) From Zd_MzFpHb2 Where Lb_Code='" & V_Lb_Code & "' And Substring(Fp_Code,1,1)='X'")
                If m_Count > 0 Then
                    MsgBox("诊疗项目和药材不能分到同一类别")
                    T_Label.Text = "未合并类别数=" & (C1FlexGrid1.Rows.Count.ToString - 1)
                    C1Label1.Text = "包含类别数=" & (C1FlexGrid2.Rows.Count.ToString - 1)
                    C1FlexGrid1.Redraw = True
                    C1FlexGrid2.Redraw = True
                    C1FlexGrid2.Rows.Count = J + I
                    Exit Sub
                End If
            End If


            HisVar.HisVar.Sqldal.ExecuteSql("Insert into Zd_MzFpHb2(Lb_Code,Fp_Code,Fp_Name,Fp_Jc)Values('" & V_Lb_Code & "','" & R.Item(0) & "','" & R.Item(1) & "','" & R.Item(2) & "')")
            C1FlexGrid2.Item(J + I, 0) = R.Item(0).ToString
            C1FlexGrid2.Item(J + I, 1) = R.Item(1).ToString
            C1FlexGrid2.Item(J + I, 2) = R.Item(2).ToString
            'C1FlexGrid2.Item(J + I, 3) = R.Item(3).ToString
            I = I + 1
            C1FlexGrid1.Rows.Remove(R.Index)
        Next
        T_Label.Text = "未合并类别数=" & (C1FlexGrid1.Rows.Count.ToString - 1)
        C1Label1.Text = "包含类别数=" & (C1FlexGrid2.Rows.Count.ToString - 1)
        C1FlexGrid1.Redraw = True
        C1FlexGrid2.Redraw = True
    End Sub



    Private Sub Button2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button2.Click

        Dim V_Count As Integer = C1FlexGrid2.Rows.Selected.Count
        Dim J As Integer = C1FlexGrid1.Rows.Count

        If V_Count = 0 Then
            Exit Sub
        Else
            C1FlexGrid1.Redraw = False
            C1FlexGrid2.Redraw = False
            C1FlexGrid1.Rows.Count = J + V_Count
        End If

        Dim R As Row
        Dim I As Integer
        For Each R In Me.C1FlexGrid2.Rows.Selected
            HisVar.HisVar.Sqldal.ExecuteSql("Delete from Zd_MzFpHb2 Where Fp_Code = '" & R.Item(0) & "'")
            C1FlexGrid1.Item(J + I, 0) = R.Item(0).ToString
            C1FlexGrid1.Item(J + I, 1) = R.Item(1).ToString
            C1FlexGrid1.Item(J + I, 2) = R.Item(2).ToString
            'C1FlexGrid1.Item(J + I, 3) = R.Item(3).ToString
            C1FlexGrid2.Rows.Remove(R.Index)

            If I = V_Count Then
                C1FlexGrid1.Row = V_Count
            End If

            I = I + 1
        Next
        T_Label.Text = "未合并类别数=" & (C1FlexGrid1.Rows.Count.ToString - 1)
        C1Label1.Text = "包含类别数=" & (C1FlexGrid2.Rows.Count.ToString - 1)
        C1FlexGrid1.Redraw = True
        C1FlexGrid2.Redraw = True

        Me.C1FlexGrid1.Sort(SortFlags.Ascending, 0)

    End Sub



    Private Sub Button5_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button5.Click
        F_Xr()
    End Sub

    Private Sub Button6_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button6.Click
        F_Sx()
    End Sub

    Private Sub F_Xr()  '已修改
        '
        If (C1FlexGrid1.Rows.Count <> 0) Then
            C1FlexGrid1.Rows.RemoveRange(1, C1FlexGrid1.Rows.Count - 1)
        End If

        Call P_Conn(True)
        Select Case Combo1.Text
            Case "简称"
                V_Str1 = "select Count(*) from (select 'Y'+Dl_Code as Dl_Code,Dl_Name,Dl_Jc from zd_Ml_Yp1 union all select 'X'+Xmlb_Code,Xmlb_Name,Xmlb_Jc from zd_ml_xm1)a where a.dl_code not in (select fp_code from Zd_Mzfphb2 ) And Dl_Jc Like '%" & T_Textbox.Text & "%'"
                V_Str2 = "select Dl_Code,Dl_Name,Dl_Jc from (select 'Y'+Dl_Code as Dl_Code,Dl_Name,Dl_Jc from zd_Ml_Yp1 union all select 'X'+Xmlb_Code,Xmlb_Name,Xmlb_Jc from zd_ml_xm1)a where a.dl_code not in (select fp_code from Zd_Mzfphb2) And Dl_Jc Like '%" & T_Textbox.Text & "%'"
            Case "名称"
                V_Str1 = "select Count(*) from (select 'Y'+Dl_Code as Dl_Code,Dl_Name,Dl_Jc from zd_Ml_Yp1 union all select 'X'+Xmlb_Code,Xmlb_Name,Xmlb_Jc from zd_ml_xm1)a where a.dl_code not in (select fp_code from Zd_Mzfphb2 ) And Dl_Name Like '%" & T_Textbox.Text & "%'"
                V_Str2 = "select Dl_Code,Dl_Name,Dl_Jc from (select 'Y'+Dl_Code as Dl_Code,Dl_Name,Dl_Jc from zd_Ml_Yp1 union all select 'X'+Xmlb_Code,Xmlb_Name,Xmlb_Jc from zd_ml_xm1)a where a.dl_code not in (select fp_code from Zd_Mzfphb2) And Dl_Name Like '%" & T_Textbox.Text & "%'"
        End Select

        Dim My_Cmd1 As New SqlCommand(V_Str1, My_Cn)
        Dim My_Cmd2 As New SqlCommand(V_Str2, My_Cn)
        Dim V_Count As Integer = My_Cmd1.ExecuteScalar
        Dim My_Reader As SqlDataReader = My_Cmd2.ExecuteReader
        Me.C1FlexGrid1.Rows.Count = V_Count + 1
        Dim I As Integer
        While My_Reader.Read()
            I = I + 1
            C1FlexGrid1.Item(I, 0) = My_Reader.Item(0).ToString
            C1FlexGrid1.Item(I, 1) = My_Reader.Item(1).ToString
            C1FlexGrid1.Item(I, 2) = My_Reader.Item(2).ToString
            'C1FlexGrid1.Item(I, 3) = My_Reader.Item(3).ToString
        End While
        My_Cmd1.Dispose()
        My_Cmd2.Dispose()
        My_Reader.Close()
        My_Cn.Close()



    End Sub

    Private Sub F_Sx()
        '
        If (C1FlexGrid2.Rows.Count <> 0) Then
            C1FlexGrid2.Rows.RemoveRange(1, C1FlexGrid2.Rows.Count - 1)
        End If

        Call P_Conn(True)
        Select Case Combo2.Text
            Case "简称"
                V_Str1 = "select Count(*) from Zd_MzFpHb2 Where Lb_Code='" & V_Lb_Code & "' and Fp_Jc Like '%" & C1TextBox1.Text & "%'"
                V_Str2 = "select Fp_Code,Fp_Name,Fp_Jc from Zd_MzFpHb1,Zd_MzFpHb2 where Zd_MzFpHb1.Lb_Code=Zd_MzFpHb2.Lb_Code and Zd_MzFpHb2.Lb_Code='" & V_Lb_Code & "' and Yy_Code = '" & HisVar.HisVar.WsyCode & "' and Fp_Jc Like '%" & C1TextBox1.Text & "%'"
            Case "名称"
                V_Str1 = "select Count(*) from Zd_MzFpHb2 Where Lb_Code='" & V_Lb_Code & "' and Fp_Name Like '%" & C1TextBox1.Text & "%'"
                V_Str2 = "select Fp_Code,Fp_Name,Fp_Jc from Zd_MzFpHb1,Zd_MzFpHb2 where Zd_MzFpHb1.Lb_Code=Zd_MzFpHb2.Lb_Code and Zd_MzFpHb2.Lb_Code='" & V_Lb_Code & "' and Yy_Code = '" & HisVar.HisVar.WsyCode & "' and Fp_Name Like '%" & C1TextBox1.Text & "%'"
        End Select

        Dim My_Cmd1 As New SqlCommand(V_Str1, My_Cn)
        Dim My_Cmd2 As New SqlCommand(V_Str2, My_Cn)
        Dim V_Count As Integer = My_Cmd1.ExecuteScalar
        Dim My_Reader As SqlDataReader = My_Cmd2.ExecuteReader
        Me.C1FlexGrid2.Rows.Count = V_Count + 1
        Dim I As Integer
        While My_Reader.Read()
            I = I + 1
            C1FlexGrid2.Item(I, 0) = My_Reader.Item(0).ToString
            C1FlexGrid2.Item(I, 1) = My_Reader.Item(1).ToString
            C1FlexGrid2.Item(I, 2) = My_Reader.Item(2).ToString
            'C1FlexGrid2.Item(I, 3) = My_Reader.Item(3).ToString
        End While
        My_Cmd1.Dispose()
        My_Cmd2.Dispose()
        My_Reader.Close()
        My_Cn.Close()
    End Sub

    Private Sub T_Textbox_KeyPress(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles T_Textbox.KeyPress
        If e.KeyChar = Chr(Keys.Enter) Then
            F_Xr()
        End If
    End Sub

    Private Sub C1Textbox1_KeyPress(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles C1TextBox1.KeyPress
        If e.KeyChar = Chr(Keys.Enter) Then
            F_Sx()
        End If
    End Sub

    Private Sub Button7_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button7.Click
        Me.Close()
    End Sub

End Class
