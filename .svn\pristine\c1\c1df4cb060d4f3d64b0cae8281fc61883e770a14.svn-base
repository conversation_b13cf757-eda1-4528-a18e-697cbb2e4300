﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="0" />
    <DataSources isList="true" count="1">
      <药房台账统计 Ref="2" type="DataTableSource" isKey="true">
        <Alias>药房台账统计</Alias>
        <Columns isList="true" count="34">
          <value>Mx_Code,System.String</value>
          <value>yp_name,System.String</value>
          <value>Mx_Gg,System.String</value>
          <value>Mx_Cd,System.String</value>
          <value>Dw,System.String</value>
          <value>Dl_Name,System.String</value>
          <value>Dl_Code,System.String</value>
          <value>Mx_Money,System.Decimal</value>
          <value>YkYf_Money,System.Decimal</value>
          <value>Rk_Money,System.Decimal</value>
          <value>Mz_Money,System.Decimal</value>
          <value>BlCf_Money,System.Decimal</value>
          <value>Ks_Money,System.Decimal</value>
          <value>Pf_Money,System.Decimal</value>
          <value>Pd_Money,System.Decimal</value>
          <value>TkYk_Money,System.Decimal</value>
          <value>BsBy_Money,System.Decimal</value>
          <value>Tk_Money,System.Decimal</value>
          <value>Crk_Money,System.Decimal</value>
          <value>Yfsl,System.Decimal</value>
          <value>YkYfsl,System.Decimal</value>
          <value>RkSl,System.Decimal</value>
          <value>Mzsl,System.Decimal</value>
          <value>Blcfsl,System.Decimal</value>
          <value>Kssl,System.Decimal</value>
          <value>Pfsl,System.Decimal</value>
          <value>Pdsl,System.Decimal</value>
          <value>TkYksl,System.Decimal</value>
          <value>Bsbysl,System.Decimal</value>
          <value>Tksl,System.Decimal</value>
          <value>Crksl,System.Decimal</value>
          <value>Yp_Code,System.String</value>
          <value>Mx_XsDw,System.String</value>
          <value>MzTy_Money,System.Decimal</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>药房台账统计</Name>
        <NameInSource>药房台账统计</NameInSource>
      </药房台账统计>
    </DataSources>
    <Relations isList="true" count="0" />
    <Report isRef="0" />
    <Variables isList="true" count="2">
      <value>,查询时间,查询时间,System.String,,False,False</value>
      <value>,打印时间,打印时间,System.String,,False,False</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="2">
    <Page1 Ref="3" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="9">
        <PageFooterBand1 Ref="4" type="PageFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,18.5,27.7,0.5</ClientRectangle>
          <Components isList="true" count="1">
            <Text38 Ref="5" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,25.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,0</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text38</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>第{PageNumber}页</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text38>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>PageFooterBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </PageFooterBand1>
        <ReportTitleBand1 Ref="6" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,27.7,1.3</ClientRectangle>
          <Components isList="true" count="3">
            <Text7 Ref="7" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.7,13.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text7</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Text>查询时间：{查询时间}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="8" type="CustomFormat" isKey="true">
                <StringFormat>yyyy-MM-dd HH:mm:ss</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text7>
            <Text8 Ref="9" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,27.7,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>黑体,15,Regular,Point,False,134</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text8</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Text>药房台账统计</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text8>
            <Text17 Ref="10" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>15.6,0.7,12.1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text17</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Text>打印时间：{打印时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text17>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>ReportTitleBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </ReportTitleBand1>
        <HeaderBand2 Ref="11" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,2.5,27.7,0.6</ClientRectangle>
          <Components isList="true" count="14">
            <Text34 Ref="12" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>8.4,0,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <Guid>f634bd5d2c0344d6b7ecaeee0c5759fb</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text34</Name>
              <Page isRef="3" />
              <Parent isRef="11" />
              <Text>采购入库</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text34>
            <Text35 Ref="13" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>10.2,0,2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <Guid>e0a75344958f4588aedae41c98131a64</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text35</Name>
              <Page isRef="3" />
              <Parent isRef="11" />
              <Text>药库调拨</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text35>
            <Text36 Ref="14" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>19.1,0,1.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <Guid>7a234335c26b487181b3a2c210d80a02</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text36</Name>
              <Page isRef="3" />
              <Parent isRef="11" />
              <Text>药房盘点</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text36>
            <Text37 Ref="15" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>20.8,0,1.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <Guid>10c969acabe14435ae1aba3487948339</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text37</Name>
              <Page isRef="3" />
              <Parent isRef="11" />
              <Text>退供应商</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text37>
            <Text39 Ref="16" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>22.5,0,1.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <Guid>c65e02457d1a401f8a348fb74ef185e9</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text39</Name>
              <Page isRef="3" />
              <Parent isRef="11" />
              <Text>药房退库</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text39>
            <Text40 Ref="17" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>24.2,0,1.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <Guid>6925c376987c4fc08da716328f68c743</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text40</Name>
              <Page isRef="3" />
              <Parent isRef="11" />
              <Text>报损报益</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text40>
            <Text41 Ref="18" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>17.5,0,1.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <Guid>1e78b7e89cb04ca4ab39320cdfb2bf8f</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text41</Name>
              <Page isRef="3" />
              <Parent isRef="11" />
              <Text>科室支领</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text41>
            <Text42 Ref="19" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15.8,0,1.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <Guid>1f5c4da462f04cc2b556657f28cf1353</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text42</Name>
              <Page isRef="3" />
              <Parent isRef="11" />
              <Text>药品批发</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text42>
            <Text44 Ref="20" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>12.2,0,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <Guid>6e5faccea87a462aa479422a34be0a48</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text44</Name>
              <Page isRef="3" />
              <Parent isRef="11" />
              <Text>门诊发药</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text44>
            <Text45 Ref="21" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>14,0,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <Guid>be01a31c63524f8990b3f9fd19869570</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text45</Name>
              <Page isRef="3" />
              <Parent isRef="11" />
              <Text>住院发药</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text45>
            <Text2 Ref="22" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,4.1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <Guid>915794992c554483aa27ccd86ef7aca5</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text2</Name>
              <Page isRef="3" />
              <Parent isRef="11" />
              <Text>药品名称</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text2>
            <Text16 Ref="23" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.1,0,2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <Guid>2a8d7194c2fb487b966c40721cc462f4</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text16</Name>
              <Page isRef="3" />
              <Parent isRef="11" />
              <Text>规格</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text16>
            <Text20 Ref="24" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6.1,0,2.3,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <Guid>d2e5c595fb0b4ff3a90ec6e8143569e5</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text20</Name>
              <Page isRef="3" />
              <Parent isRef="11" />
              <Text>产地</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text20>
            <Text30 Ref="25" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>25.9,0,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <Guid>a4fb75195e094caaaa076d65d0d26383</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text30</Name>
              <Page isRef="3" />
              <Parent isRef="11" />
              <Text>出入合计</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text30>
          </Components>
          <Conditions isList="true" count="0" />
          <Guid>2790c41c2d444b608885075a9a000e65</Guid>
          <Name>HeaderBand2</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </HeaderBand2>
        <GroupHeaderBand3 Ref="26" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,3.9,27.7,0.5</ClientRectangle>
          <Components isList="true" count="1">
            <Text63 Ref="27" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,27.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>0149dd595959434c8872bc18e91a8821</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text63</Name>
              <Page isRef="3" />
              <Parent isRef="26" />
              <Text>{药房台账统计.Dl_Name}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text63>
          </Components>
          <Condition>{药房台账统计.Dl_Code}</Condition>
          <Conditions isList="true" count="0" />
          <Name>GroupHeaderBand3</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </GroupHeaderBand3>
        <GroupHeaderBand1 Ref="28" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,5.2,27.7,0</ClientRectangle>
          <Components isList="true" count="0" />
          <Condition>{药房台账统计.yp_name}{药房台账统计.Mx_Gg}{药房台账统计.Mx_Cd}</Condition>
          <Conditions isList="true" count="0" />
          <Name>GroupHeaderBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </GroupHeaderBand1>
        <DataBand1 Ref="29" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,6,27.7,0</ClientRectangle>
          <Components isList="true" count="0" />
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>药房台账统计</DataSourceName>
          <Filters isList="true" count="0" />
          <Name>DataBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Sort isList="true" count="0" />
        </DataBand1>
        <GroupFooterBand1 Ref="30" type="GroupFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,6.8,27.7,0.5</ClientRectangle>
          <Components isList="true" count="14">
            <Text1 Ref="31" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,4.1,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>0,0,0,0</Margins>
              <Name>Text1</Name>
              <Page isRef="3" />
              <Parent isRef="30" />
              <Text>{药房台账统计.yp_name}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text1>
            <Text9 Ref="32" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>8.4,0,1.8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text9</Name>
              <Page isRef="3" />
              <Parent isRef="30" />
              <Text>{Sum(GroupHeaderBand1,药房台账统计.Rk_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="33" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text9>
            <Text11 Ref="34" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>10.2,0,2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text11</Name>
              <Page isRef="3" />
              <Parent isRef="30" />
              <Text>{Sum(GroupHeaderBand1,药房台账统计.YkYf_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="35" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text11>
            <Text15 Ref="36" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>12.2,0,1.8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text15</Name>
              <Page isRef="3" />
              <Parent isRef="30" />
              <Text>{Sum(GroupHeaderBand1,药房台账统计.Mz_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="37" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text15>
            <Text19 Ref="38" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>15.8,0,1.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text19</Name>
              <Page isRef="3" />
              <Parent isRef="30" />
              <Text>{Sum(GroupHeaderBand1,药房台账统计.Pf_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="39" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text19>
            <Text25 Ref="40" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>17.5,0,1.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text25</Name>
              <Page isRef="3" />
              <Parent isRef="30" />
              <Text>{Sum(GroupHeaderBand1,药房台账统计.Ks_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="41" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text25>
            <Text27 Ref="42" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>19.1,0,1.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text27</Name>
              <Page isRef="3" />
              <Parent isRef="30" />
              <Text>{Sum(GroupHeaderBand1,药房台账统计.Pd_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="43" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text27>
            <Text29 Ref="44" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>24.2,0,1.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text29</Name>
              <Page isRef="3" />
              <Parent isRef="30" />
              <Text>{Sum(GroupHeaderBand1,药房台账统计.BsBy_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="45" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text29>
            <Text13 Ref="46" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>14,0,1.8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text13</Name>
              <Page isRef="3" />
              <Parent isRef="30" />
              <Text>{Sum(GroupHeaderBand1,药房台账统计.BlCf_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="47" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text13>
            <Text4 Ref="48" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>22.5,0,1.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>265df52d1cc04b21978eb72021318569</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text4</Name>
              <Page isRef="3" />
              <Parent isRef="30" />
              <Text>{Sum(GroupHeaderBand1,药房台账统计.TkYk_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="49" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text4>
            <Text10 Ref="50" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>20.8,0,1.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>92b403a7ada64412a2a4cf7126e9689f</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text10</Name>
              <Page isRef="3" />
              <Parent isRef="30" />
              <Text>{Sum(GroupHeaderBand1,药房台账统计.Tk_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="51" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text10>
            <Text26 Ref="52" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>4.1,0,2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>5ee65986011f4f2b84b645e1c7057261</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text26</Name>
              <Page isRef="3" />
              <Parent isRef="30" />
              <Text>{药房台账统计.Mx_Gg}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="53" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text26>
            <Text28 Ref="54" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>6.1,0,2.3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>568a48c13232489185f9577093aa3e91</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text28</Name>
              <Page isRef="3" />
              <Parent isRef="30" />
              <Text>{药房台账统计.Mx_Cd}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="55" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text28>
            <Text46 Ref="56" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>25.9,0,1.8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>c056c077fe434a638fc90b1ef3ed2f8a</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text46</Name>
              <Page isRef="3" />
              <Parent isRef="30" />
              <Text>{Sum(GroupHeaderBand1,药房台账统计.Crk_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="57" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text46>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>GroupFooterBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </GroupFooterBand1>
        <GroupFooterBand3 Ref="58" type="GroupFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,8.1,27.7,0.5</ClientRectangle>
          <Components isList="true" count="12">
            <Text64 Ref="59" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,8.4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>5c042e695462471fa5e94f4b6020624f</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text64</Name>
              <OnlyText>True</OnlyText>
              <Page isRef="3" />
              <Parent isRef="58" />
              <Text>金额小计</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text64>
            <Text65 Ref="60" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>8.4,0,1.8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>01579fc655fa49368a61db3633c55674</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text65</Name>
              <Page isRef="3" />
              <Parent isRef="58" />
              <Text>{Sum(GroupHeaderBand3,药房台账统计.Rk_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="61" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text65>
            <Text66 Ref="62" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>14,0,1.8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>9b742403246c4550adcf48a98d439e69</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text66</Name>
              <Page isRef="3" />
              <Parent isRef="58" />
              <Text>{Sum(GroupHeaderBand3,药房台账统计.BlCf_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="63" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text66>
            <Text67 Ref="64" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>15.8,0,1.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>32312fc55f0849718cbcdc7eac1dffde</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text67</Name>
              <Page isRef="3" />
              <Parent isRef="58" />
              <Text>{Sum(GroupHeaderBand3,药房台账统计.Pf_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="65" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text67>
            <Text68 Ref="66" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>19.1,0,1.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>10e43c2c0f7c4fc9939515aaf5d8a7ad</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text68</Name>
              <Page isRef="3" />
              <Parent isRef="58" />
              <Text>{Sum(GroupHeaderBand3,药房台账统计.Pd_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="67" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text68>
            <Text69 Ref="68" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>20.8,0,1.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>b3ef51abf461467bb7ca305458b8c40f</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text69</Name>
              <Page isRef="3" />
              <Parent isRef="58" />
              <Text>{Sum(GroupHeaderBand3,药房台账统计.Tk_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="69" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text69>
            <Text70 Ref="70" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>22.5,0,1.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>5aa7b153d3df48bc8d0979d8e1721424</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text70</Name>
              <Page isRef="3" />
              <Parent isRef="58" />
              <Text>{Sum(GroupHeaderBand3,药房台账统计.TkYk_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="71" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text70>
            <Text71 Ref="72" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>25.9,0,1.8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>e814378fee5c486a80e25e682c3f4b4c</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text71</Name>
              <Page isRef="3" />
              <Parent isRef="58" />
              <Text>{Sum(GroupHeaderBand3,药房台账统计.Crk_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="73" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text71>
            <Text72 Ref="74" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>17.5,0,1.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>da51644525254eea88bcf5e0b007543d</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text72</Name>
              <Page isRef="3" />
              <Parent isRef="58" />
              <Text>{Sum(GroupHeaderBand3,药房台账统计.Ks_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="75" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text72>
            <Text73 Ref="76" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>10.2,0,2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>5ead7114384942659d498f0550be7f05</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text73</Name>
              <Page isRef="3" />
              <Parent isRef="58" />
              <Text>{Sum(GroupHeaderBand3,药房台账统计.YkYf_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="77" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text73>
            <Text74 Ref="78" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>12.2,0,1.8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>d68346a12fc14c5e94daa4cfac5b0cda</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text74</Name>
              <Page isRef="3" />
              <Parent isRef="58" />
              <Text>{Sum(GroupHeaderBand3,药房台账统计.Mz_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="79" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text74>
            <Text75 Ref="80" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>24.2,0,1.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>6b7f54a7fd0e44fca8433df95d1dcd78</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text75</Name>
              <Page isRef="3" />
              <Parent isRef="58" />
              <Text>{Sum(GroupHeaderBand3,药房台账统计.BsBy_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="81" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text75>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>GroupFooterBand3</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </GroupFooterBand3>
        <ReportSummaryBand1 Ref="82" type="ReportSummaryBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,9.4,27.7,0.8</ClientRectangle>
          <Components isList="true" count="12">
            <Text43 Ref="83" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,8.4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>1fa5d2ca9445495884b6118a7837e07a</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text43</Name>
              <OnlyText>True</OnlyText>
              <Page isRef="3" />
              <Parent isRef="82" />
              <Text>金额合计</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text43>
            <Text6 Ref="84" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>8.4,0,1.8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>5c80a5c39b1948b0855dbc8182eb5973</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text6</Name>
              <Page isRef="3" />
              <Parent isRef="82" />
              <Text>{Sum(DataBand1,药房台账统计.Rk_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="85" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text6>
            <Text18 Ref="86" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>14,0,1.8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>cb4ade05c3c94958a2fa093df6cd4941</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text18</Name>
              <Page isRef="3" />
              <Parent isRef="82" />
              <Text>{Sum(DataBand1,药房台账统计.BlCf_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="87" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text18>
            <Text21 Ref="88" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>15.8,0,1.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>abab103837c74740b6cf1373595af7e1</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text21</Name>
              <Page isRef="3" />
              <Parent isRef="82" />
              <Text>{Sum(DataBand1,药房台账统计.Pf_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="89" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text21>
            <Text22 Ref="90" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>19.1,0,1.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>6e72301087ba491e882888af3cf9e0b8</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text22</Name>
              <Page isRef="3" />
              <Parent isRef="82" />
              <Text>{Sum(DataBand1,药房台账统计.Pd_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="91" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text22>
            <Text23 Ref="92" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>20.8,0,1.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>8c4184b94af64da69ccfbdca1387cd40</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text23</Name>
              <Page isRef="3" />
              <Parent isRef="82" />
              <Text>{Sum(DataBand1,药房台账统计.Tk_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="93" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text23>
            <Text24 Ref="94" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>22.5,0,1.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>53d85b303e4d4e8cab75a4639e456ebf</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text24</Name>
              <Page isRef="3" />
              <Parent isRef="82" />
              <Text>{Sum(DataBand1,药房台账统计.TkYk_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="95" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text24>
            <Text31 Ref="96" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>25.9,0,1.8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>e54eb505d7f94cab9d8e48b576625e00</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text31</Name>
              <Page isRef="3" />
              <Parent isRef="82" />
              <Text>{Sum(DataBand1,药房台账统计.Crk_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="97" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text31>
            <Text32 Ref="98" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>17.5,0,1.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>38ca93a7ef164e50bd4c4bb9f1f54cb9</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text32</Name>
              <Page isRef="3" />
              <Parent isRef="82" />
              <Text>{Sum(DataBand1,药房台账统计.Ks_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="99" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text32>
            <Text12 Ref="100" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>10.2,0,2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>b8647c517d0d4387adfa6b3604faca78</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text12</Name>
              <Page isRef="3" />
              <Parent isRef="82" />
              <Text>{Sum(DataBand1,药房台账统计.YkYf_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="101" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text12>
            <Text14 Ref="102" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>12.2,0,1.8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>964fb1ae3b984be897e2c986978109e6</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text14</Name>
              <Page isRef="3" />
              <Parent isRef="82" />
              <Text>{Sum(DataBand1,药房台账统计.Mz_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="103" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text14>
            <Text47 Ref="104" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>24.2,0,1.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>c2b48353dc8447fdb036c5e40616aae9</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text47</Name>
              <Page isRef="3" />
              <Parent isRef="82" />
              <Text>{Sum(DataBand1,药房台账统计.BsBy_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="105" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
                <UseGroupSeparator>False</UseGroupSeparator>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text47>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>ReportSummaryBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </ReportSummaryBand1>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>b34eae1e5edd44cdb0bd8cc5d316eea3</Guid>
      <Margins>1,1,1,1</Margins>
      <Name>Page1</Name>
      <Orientation>Landscape</Orientation>
      <PageHeight>21</PageHeight>
      <PageWidth>29.7</PageWidth>
      <PaperSize>A4</PaperSize>
      <Report isRef="0" />
      <Watermark Ref="106" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page1>
    <Page2 Ref="107" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="7">
        <PageFooterBand2 Ref="108" type="PageFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,18.5,27.7,0.5</ClientRectangle>
          <Components isList="true" count="1">
            <Text3 Ref="109" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,25.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,0</Font>
              <Guid>72e0a3dddad345cc9af5cc88a4c022ad</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text3</Name>
              <Page isRef="107" />
              <Parent isRef="108" />
              <Text>第{PageNumber}页</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text3>
          </Components>
          <Conditions isList="true" count="0" />
          <Guid>d0e0c6886dae42ba997f774c0dedd17e</Guid>
          <Name>PageFooterBand2</Name>
          <Page isRef="107" />
          <Parent isRef="107" />
        </PageFooterBand2>
        <ReportTitleBand2 Ref="110" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,27.7,1.3</ClientRectangle>
          <Components isList="true" count="3">
            <Text5 Ref="111" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.7,14.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <Guid>e5b21a7b2adc4c6b9ca857242f92b8a3</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text5</Name>
              <Page isRef="107" />
              <Parent isRef="110" />
              <Text>查询时间：{查询时间}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="112" type="CustomFormat" isKey="true">
                <StringFormat>yyyy-MM-dd HH:mm:ss</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text5>
            <Text33 Ref="113" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,27.7,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>黑体,15,Regular,Point,False,134</Font>
              <Guid>a6d2bb07598e45a1ba70af892913c945</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text33</Name>
              <Page isRef="107" />
              <Parent isRef="110" />
              <Text>药房台账统计（数量统计）</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text33>
            <Text48 Ref="114" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>14.2,0.7,13.5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <Guid>879406c2850e475c8b24169f2e6733df</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text48</Name>
              <Page isRef="107" />
              <Parent isRef="110" />
              <Text>打印时间：{打印时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text48>
          </Components>
          <Conditions isList="true" count="0" />
          <Guid>f86d6d6245904d4aa9442161b476d5ad</Guid>
          <Name>ReportTitleBand2</Name>
          <Page isRef="107" />
          <Parent isRef="107" />
        </ReportTitleBand2>
        <HeaderBand1 Ref="115" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,2.5,27.7,0.6</ClientRectangle>
          <Components isList="true" count="15">
            <Text49 Ref="116" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>8.4,0,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <Guid>b8ba7aec1a2847cfac7957e58738259e</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text49</Name>
              <Page isRef="107" />
              <Parent isRef="115" />
              <Text>采购入库</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text49>
            <Text50 Ref="117" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>10.2,0,2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <Guid>87866519c3aa4c4194d080b63f34f124</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text50</Name>
              <Page isRef="107" />
              <Parent isRef="115" />
              <Text>药库调拨</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text50>
            <Text55 Ref="118" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>17.5,0,1.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <Guid>6ea631209bd445c1970eea3c65f45048</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text55</Name>
              <Page isRef="107" />
              <Parent isRef="115" />
              <Text>科室支领</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text55>
            <Text56 Ref="119" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15.8,0,1.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <Guid>21891dd2d33847be99538c0e6438897f</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text56</Name>
              <Page isRef="107" />
              <Parent isRef="115" />
              <Text>药品批发</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text56>
            <Text57 Ref="120" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>12.2,0,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <Guid>ff623cee4243474ebbec21e4f81d2065</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text57</Name>
              <Page isRef="107" />
              <Parent isRef="115" />
              <Text>门诊发药</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text57>
            <Text58 Ref="121" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>14,0,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <Guid>aeec12206ac841e28bac73ad5c1785a3</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text58</Name>
              <Page isRef="107" />
              <Parent isRef="115" />
              <Text>住院发药</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text58>
            <Text59 Ref="122" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,3.5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <Guid>61666f76876740a8b8a772506da4eae5</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text59</Name>
              <Page isRef="107" />
              <Parent isRef="115" />
              <Text>药品名称</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text59>
            <Text60 Ref="123" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.5,0,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <Guid>d26085521aff4ac89857dcc04e183fec</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text60</Name>
              <Page isRef="107" />
              <Parent isRef="115" />
              <Text>规格</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text60>
            <Text61 Ref="124" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>5.3,0,2.1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <Guid>ff3315f740004f0582802649e23c3d40</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text61</Name>
              <Page isRef="107" />
              <Parent isRef="115" />
              <Text>产地</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text61>
            <Text51 Ref="125" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>19.1,0,1.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <Guid>54ef0c75af6349e6b89c1f9a77bf15c7</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text51</Name>
              <Page isRef="107" />
              <Parent isRef="115" />
              <Text>药房盘点</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text51>
            <Text52 Ref="126" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>20.8,0,1.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <Guid>9948a2f922f1453084fedd0165b4f604</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text52</Name>
              <Page isRef="107" />
              <Parent isRef="115" />
              <Text>退供应商</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text52>
            <Text53 Ref="127" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>22.5,0,1.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <Guid>610765721fdd4bde812b7d6014610650</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text53</Name>
              <Page isRef="107" />
              <Parent isRef="115" />
              <Text>药房退库</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text53>
            <Text54 Ref="128" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>24.2,0,1.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <Guid>635bc66956114a718ba3e5303f01a779</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text54</Name>
              <Page isRef="107" />
              <Parent isRef="115" />
              <Text>报损报益</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text54>
            <Text62 Ref="129" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>25.9,0,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <Guid>f27cf1db4c574a60b5cfc5f299c22a25</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text62</Name>
              <Page isRef="107" />
              <Parent isRef="115" />
              <Text>出入合计</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text62>
            <Text90 Ref="130" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.4,0,1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <Guid>4780589b4e644770a91ad5bea5e971d1</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text90</Name>
              <Page isRef="107" />
              <Parent isRef="115" />
              <Text>单位</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text90>
          </Components>
          <Conditions isList="true" count="0" />
          <Guid>8103e82f2bee48bf82a081332486eca1</Guid>
          <Name>HeaderBand1</Name>
          <Page isRef="107" />
          <Parent isRef="107" />
        </HeaderBand1>
        <GroupHeaderBand4 Ref="131" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,3.9,27.7,0.5</ClientRectangle>
          <Components isList="true" count="1">
            <Text76 Ref="132" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,27.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>96d25a9992684b8a9083771b2cd88c53</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text76</Name>
              <Page isRef="107" />
              <Parent isRef="131" />
              <Text>{药房台账统计.Dl_Name}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text76>
          </Components>
          <Condition>{药房台账统计.Dl_Code}</Condition>
          <Conditions isList="true" count="0" />
          <Name>GroupHeaderBand4</Name>
          <Page isRef="107" />
          <Parent isRef="107" />
        </GroupHeaderBand4>
        <GroupHeaderBand2 Ref="133" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,5.2,27.7,0</ClientRectangle>
          <Components isList="true" count="0" />
          <Condition>{药房台账统计.yp_name}{药房台账统计.Dw}{药房台账统计.Mx_Gg}{药房台账统计.Mx_Cd}</Condition>
          <Conditions isList="true" count="0" />
          <Guid>a39f11decee74849b3a8abd9f5c35df0</Guid>
          <Name>GroupHeaderBand2</Name>
          <Page isRef="107" />
          <Parent isRef="107" />
        </GroupHeaderBand2>
        <DataBand2 Ref="134" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,6,27.7,0</ClientRectangle>
          <Components isList="true" count="0" />
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>药房台账统计</DataSourceName>
          <Filters isList="true" count="0" />
          <Guid>3980595e2ab543a9a428956e34e45b32</Guid>
          <Name>DataBand2</Name>
          <Page isRef="107" />
          <Parent isRef="107" />
          <Sort isList="true" count="0" />
        </DataBand2>
        <GroupFooterBand2 Ref="135" type="GroupFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,6.8,27.7,0.5</ClientRectangle>
          <Components isList="true" count="15">
            <Text77 Ref="136" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,3.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>e8449aa9fb914fc3935b8ba0d2eb9183</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text77</Name>
              <Page isRef="107" />
              <Parent isRef="135" />
              <Text>{药房台账统计.yp_name}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text77>
            <Text78 Ref="137" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>8.4,0,1.8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>5c5d16abdcea44559d73a2891d2490a0</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text78</Name>
              <Page isRef="107" />
              <Parent isRef="135" />
              <Text>{Sum(GroupHeaderBand2,药房台账统计.RkSl)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="138" type="CustomFormat" isKey="true">
                <StringFormat>0.####</StringFormat>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text78>
            <Text79 Ref="139" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>10.2,0,2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>ddaea779f8ec4ef389b3cded08451787</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text79</Name>
              <Page isRef="107" />
              <Parent isRef="135" />
              <Text>{Sum(GroupHeaderBand2,药房台账统计.YkYfsl)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="140" type="CustomFormat" isKey="true">
                <StringFormat>0.#####</StringFormat>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text79>
            <Text80 Ref="141" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>12.2,0,1.8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>433c9842229a4314820852c9c9ff6fe1</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text80</Name>
              <Page isRef="107" />
              <Parent isRef="135" />
              <Text>{Sum(GroupHeaderBand2,药房台账统计.Mzsl)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="142" type="CustomFormat" isKey="true">
                <StringFormat>0.#####</StringFormat>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text80>
            <Text81 Ref="143" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>15.8,0,1.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>7b28c117a856421584f5b8bc528c674e</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text81</Name>
              <Page isRef="107" />
              <Parent isRef="135" />
              <Text>{Sum(GroupHeaderBand2,药房台账统计.Pfsl)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="144" type="CustomFormat" isKey="true">
                <StringFormat>0.#####</StringFormat>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text81>
            <Text82 Ref="145" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>17.5,0,1.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>691f7dc2aeae407f9fdf40842be6f480</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text82</Name>
              <Page isRef="107" />
              <Parent isRef="135" />
              <Text>{Sum(GroupHeaderBand2,药房台账统计.Kssl)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="146" type="CustomFormat" isKey="true">
                <StringFormat>0.#####</StringFormat>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text82>
            <Text83 Ref="147" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>14,0,1.8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>94502fedca55408b901f5fb2c43caa21</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text83</Name>
              <Page isRef="107" />
              <Parent isRef="135" />
              <Text>{Sum(GroupHeaderBand2,药房台账统计.Blcfsl)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="148" type="CustomFormat" isKey="true">
                <StringFormat>0.#####</StringFormat>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text83>
            <Text84 Ref="149" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>3.5,0,1.8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>3725b5a33b4243ac8655cbd7258a0879</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text84</Name>
              <Page isRef="107" />
              <Parent isRef="135" />
              <Text>{药房台账统计.Mx_Gg}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="150" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text84>
            <Text85 Ref="151" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>5.3,0,2.1,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>aa07e094cdc74269b59d3e2bc5b5b8d4</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text85</Name>
              <Page isRef="107" />
              <Parent isRef="135" />
              <Text>{药房台账统计.Mx_Cd}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="152" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text85>
            <Text86 Ref="153" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>19.1,0,1.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>40ef2f08aa95429b8f690c2e63dbfa47</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text86</Name>
              <Page isRef="107" />
              <Parent isRef="135" />
              <Text>{Sum(GroupHeaderBand2,药房台账统计.Pdsl)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="154" type="CustomFormat" isKey="true">
                <StringFormat>0.#####</StringFormat>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text86>
            <Text87 Ref="155" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>24.2,0,1.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>5668af27b80543a89d8022ebb7d1fe60</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text87</Name>
              <Page isRef="107" />
              <Parent isRef="135" />
              <Text>{Sum(GroupHeaderBand2,药房台账统计.Bsbysl)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="156" type="CustomFormat" isKey="true">
                <StringFormat>0.#####</StringFormat>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text87>
            <Text88 Ref="157" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>22.5,0,1.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>c44891a0e8234ab18a69a9b84ed9cd54</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text88</Name>
              <Page isRef="107" />
              <Parent isRef="135" />
              <Text>{Sum(GroupHeaderBand2,药房台账统计.TkYksl)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="158" type="CustomFormat" isKey="true">
                <StringFormat>0.#####</StringFormat>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text88>
            <Text91 Ref="159" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>20.8,0,1.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>1bc7720d8f254b8c81fbd2aa0b177094</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text91</Name>
              <Page isRef="107" />
              <Parent isRef="135" />
              <Text>{Sum(GroupHeaderBand2,药房台账统计.Tksl)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="160" type="CustomFormat" isKey="true">
                <StringFormat>0.#####</StringFormat>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text91>
            <Text92 Ref="161" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>25.9,0,1.8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>15feb4319c00475c94006b9c56978b10</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text92</Name>
              <Page isRef="107" />
              <Parent isRef="135" />
              <Text>{Sum(GroupHeaderBand2,药房台账统计.Crksl)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="162" type="CustomFormat" isKey="true">
                <StringFormat>0.#####</StringFormat>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text92>
            <Text93 Ref="163" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>7.4,0,1,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>359b9e8bfae64401acca66597de7ca78</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text93</Name>
              <Page isRef="107" />
              <Parent isRef="135" />
              <Text>{药房台账统计.Dw}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="164" type="NumberFormat" isKey="true">
                <GroupSeparator>,</GroupSeparator>
                <NegativePattern>1</NegativePattern>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text93>
          </Components>
          <Conditions isList="true" count="0" />
          <Guid>ff7d7502fde9474ea43c0689fb6ab561</Guid>
          <Name>GroupFooterBand2</Name>
          <Page isRef="107" />
          <Parent isRef="107" />
        </GroupFooterBand2>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>f120b725d09f4ca2ba72bb3a09762086</Guid>
      <Margins>1,1,1,1</Margins>
      <Name>Page2</Name>
      <Orientation>Landscape</Orientation>
      <PageHeight>21</PageHeight>
      <PageWidth>29.7</PageWidth>
      <PaperSize>A4</PaperSize>
      <Report isRef="0" />
      <Watermark Ref="165" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page2>
  </Pages>
  <PrinterSettings Ref="166" type="Stimulsoft.Report.Print.StiPrinterSettings" isKey="true" />
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>药房台账统计</ReportAlias>
  <ReportChanged>7/28/2014 3:25:23 PM</ReportChanged>
  <ReportCreated>12/8/2011 4:39:33 PM</ReportCreated>
  <ReportFile>E:\正在进行时\唐山\正在修改版\his2010\his2010\Rpt\药房台账统计.mrt</ReportFile>
  <ReportGuid>036c3014e7634d1bb5a3e5617619cba4</ReportGuid>
  <ReportName>药房台账统计</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2011.2.1026</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class Report : Stimulsoft.Report.StiReport
    {
        public Report()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
        #endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>