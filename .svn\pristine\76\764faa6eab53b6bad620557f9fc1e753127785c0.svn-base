﻿/**  版本信息模板在安装目录下，可自行修改。
* M_Emr_BlZk1.cs
*
* 功 能： N/A
* 类 名： M_Emr_BlZk1
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/9/19 17:08:27   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace ModelOld
{
	/// <summary>
	/// M_Emr_BlZk1:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class M_Emr_BlZk1
	{
		public M_Emr_BlZk1()
		{}
		#region Model
		private string _bl_code;
		private string _jsr_code;
		private DateTime? _zk_date;
		private decimal? _zk_pf;
		private string _zkdj_code;
		/// <summary>
		/// 
		/// </summary>
		public string Bl_Code
		{
			set{ _bl_code=value;}
			get{return _bl_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Jsr_Code
		{
			set{ _jsr_code=value;}
			get{return _jsr_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public DateTime? Zk_Date
		{
			set{ _zk_date=value;}
			get{return _zk_date;}
		}
		/// <summary>
		/// 
		/// </summary>
		public decimal? Zk_Pf
		{
			set{ _zk_pf=value;}
			get{return _zk_pf;}
		}
		/// <summary>
		/// 
		/// </summary>
        public string Zkdj_Code
		{
            set { _zkdj_code = value; }
            get { return _zkdj_code; }
		}
		#endregion Model

	}
}

