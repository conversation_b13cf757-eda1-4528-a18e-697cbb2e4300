﻿Imports System.Windows.Forms
Imports System.Drawing
Imports Stimulsoft.Report
Imports HisControl
Imports BaseClass
Imports ModelOld

Public Class Materials_Buy_In1

#Region "定义__变量"
    Dim My_Table As New DataTable
    Dim V_TotalMoney As Double
    Dim Cb_Cm As CurrencyManager
    Dim flag_Error As Boolean
    Dim Cb_Row As DataRow                            '当前选择行
    Dim Rinsert As Boolean = True                      '增加记录
    Dim Rk1Bll As New BLLOld.B_Materials_Buy_In1
    Dim Rk2Bll As New BLLOld.B_Materials_Buy_In2
    Dim Rk1Model As New ModelOld.M_Materials_Buy_In1
    Dim CanSave As Boolean = True
    Dim KcBll As New BLLOld.B_Materials_Stock
    Dim m_Rc As New BaseClass.C_RowChange
#End Region

    Public Sub New(ByVal model As M_Materials_Buy_In1)
        ' 此调用是设计器所必需的。
        InitializeComponent()
        Rk1Model = model
        ' 在 InitializeComponent() 调用之后添加任何初始化。
    End Sub

    Private Sub Yk_Rk2_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Call Form_Init()                '窗体初始化
        If Rk1Model Is Nothing Then
            Call Zb_Clear()
        Else
            Call Zb_Show()
        End If
        Call statisticsDataShow()
    End Sub


#Region "窗体__事件"

    Private Sub Form_Init()
        Panel2.Height = 35
        Comm_Del.Location = New Point(100, 1)
        Comm_DelAll.Location = New Point(Comm_Del.Left + Comm_Del.Width + 2, 1)
        Comm_New.Location = New Point(Comm_DelAll.Left + Comm_DelAll.Width + 2, 1)
        Comm_Save.Location = New Point(Comm_New.Left + Comm_New.Width + 2, 1)
        Comm_Complete.Location = New Point(Comm_Save.Left + Comm_Save.Width + 2, 1)
        Comm_Print.Location = New Point(Comm_Complete.Left + Comm_Complete.Width + 2, 1)
        Comm_Search.Location = New Point(Comm_Print.Left + Comm_Print.Width + 2, 1)
        Comm_WriteOffAll.Location = New Point(Comm_Search.Left + Comm_Search.Width + 2, 1)
        Comm_WriteOffPart.Location = New Point(Comm_WriteOffAll.Left + Comm_WriteOffAll.Width + 2, 1)
        Comm_Close.Location = New Point(Comm_WriteOffPart.Left + Comm_WriteOffPart.Width + 2, 1)
        '订货日期
        With Order_Date
            .CustomFormat = "yyyy-MM-dd HH:mm"
            .EditFormat = "yyyy-MM-dd"
            .DisplayFormat = "yyyy-MM-dd"
        End With
        '到货日期
        With Arrival_Date
            .CustomFormat = "yyyy-MM-dd HH:mm"
            .EditFormat = "yyyy-MM-dd"
            .DisplayFormat = "yyyy-MM-dd"
        End With


        '供货单位字典
        Dim SupBll As New BLLOld.B_Materials_Sup_Dict
        With Suplier_DtCom
            .DataView = SupBll.GetList("IsUse='1'").Tables(0).DefaultView
            .Init_Colum("MaterialsSup_Py", "供货商简称", 100, "左")
            .Init_Colum("MaterialsSup_Name", "供货商名称", 210, "左")
            .Init_Colum("MaterialsSup_Code", "编码", 0, "左")
            .Init_Colum("MaterialsSup_Wb", "--", 0, "左")
            .Init_Colum("Contact_Person", "--", 0, "左")
            .Init_Colum("Contact_Phone", "--", 0, "左")
            .Init_Colum("Bank", "--", 0, "左")
            .Init_Colum("BankNo", "--", 0, "左")
            .Init_Colum("MaterialsSup_Add", "--", 0, "左")
            .Init_Colum("MaterialsSup_Memo", "--", 0, "左")
            .Init_Colum("Serial_No", "--", 0, "左")
            .Init_Colum("IsUse", "--", 0, "左")
            .DisplayMember = "MaterialsSup_Name"
            .ValueMember = "MaterialsSup_Code"
            .DroupDownWidth = 510
            .MaxDropDownItems = 15
            .SelectedIndex = -1
            .RowFilterTextNull = ""
            .RowFilterNotTextNull = "MaterialsSup_Py"
        End With

        '库房
        Dim KFBll As New BLLOld.B_Materials_Warehouse_Dict
        With WareHouse_DtCom
            .DataView = KFBll.GetList("IsUse='1' ").Tables(0).DefaultView
            .Init_Colum("MaterialsWh_Py", "简称", 70, "左")
            .Init_Colum("MaterialsWh_Name", "名称", 80, "左")
            .Init_Colum("MaterialsWh_Code", "编码", 0, "左")
            .Init_Colum("MaterialsWh_Wb", "--", 0, "左")
            .Init_Colum("MaterialsWh_Memo", "--", 0, "左")
            .Init_Colum("Serial_No", "--", 0, "左")
            .Init_Colum("IsUse", "--", 0, "左")
            .DisplayMember = "MaterialsWh_Name"
            .ValueMember = "MaterialsWh_Code"
            .DroupDownWidth = 250
            .MaxDropDownItems = 15
            .SelectedIndex = -1
            .RowFilterTextNull = ""
            .RowFilterNotTextNull = "MaterialsWh_Py"
        End With
        Code_Text.Enabled = False
        Jsr_Text.Text = HisVar.HisVar.JsrName
        Jsr_Text.Enabled = False
    End Sub

    Private Sub BtnState()
        MyGrid_Init()
        '录入或者新单按钮状态
        If Rk1Model.OrdersStatus Is Nothing OrElse Rk1Model.OrdersStatus = "录入" Then
            Comm_Del.Enabled = True
            Comm_DelAll.Enabled = True
            Comm_New.Enabled = True
            Comm_Save.Enabled = True
            Comm_Complete.Enabled = True
            Comm_Print.Enabled = False
            Comm_Search.Enabled = True
            Comm_Close.Enabled = True
            Comm_WriteOffAll.Enabled = False
            Comm_WriteOffPart.Enabled = False

            MyGrid1.Tag = True
            If Rk1Model.WriteOffStatus = "冲销" Then
                Suplier_DtCom.Enabled = False
                WareHouse_DtCom.Enabled = False
            Else
                Suplier_DtCom.Enabled = True
                WareHouse_DtCom.Enabled = True
            End If
            Order_Date.Enabled = True
            Arrival_Date.Enabled = True
            Memo_Text.Enabled = True
            Exit Sub
        End If

        '非本人的单据不可以修改
        If Rk1Model.Jsr_Code <> HisVar.HisVar.JsrCode Then
            Comm_Del.Enabled = False
            Comm_DelAll.Enabled = False
            Comm_New.Enabled = True
            Comm_Save.Enabled = False
            Comm_Complete.Enabled = False
            Comm_Print.Enabled = False
            Comm_Search.Enabled = True
            Comm_Close.Enabled = True
            Comm_WriteOffAll.Enabled = False
            Comm_WriteOffPart.Enabled = False

            MyGrid1.Tag = False
            Suplier_DtCom.Enabled = False
            WareHouse_DtCom.Enabled = False
            Order_Date.Enabled = False
            Arrival_Date.Enabled = False
            Memo_Text.Enabled = False
            Exit Sub
        End If

        '完成的只有冲销可以使用，被冲销的一键冲销不能使用
        If Rk1Model.OrdersStatus = "完成" Then
            Comm_Del.Enabled = False
            Comm_DelAll.Enabled = False
            Comm_New.Enabled = True
            Comm_Save.Enabled = False
            Comm_Complete.Enabled = False
            Comm_Print.Enabled = True
            Comm_Search.Enabled = True
            Comm_Close.Enabled = True

            MyGrid1.Tag = False
            Suplier_DtCom.Enabled = False
            WareHouse_DtCom.Enabled = False
            Order_Date.Enabled = False
            Arrival_Date.Enabled = False
            Memo_Text.Enabled = False

            Select Case Rk1Model.WriteOffStatus & ""
                Case "冲销"      '冲销单不能被冲销
                    Comm_WriteOffAll.Enabled = False
                    Comm_WriteOffPart.Enabled = False
                Case "被冲销"    '被冲销单不能一键冲销
                    Comm_WriteOffAll.Enabled = False
                    Comm_WriteOffPart.Enabled = True
                Case "全部被冲销" '全部被冲销单不能被冲销
                    Comm_WriteOffAll.Enabled = False
                    Comm_WriteOffPart.Enabled = False
                Case ""
                    Comm_WriteOffAll.Enabled = True
                    Comm_WriteOffPart.Enabled = True
            End Select
            Exit Sub
        End If

    End Sub

    Private Sub MyGrid_Init()
        Dim ColEdit As Boolean
        If Rk1Model.OrdersStatus = "完成" Then
            ColEdit = False
        Else
            ColEdit = True
        End If

        With MyGrid1
            .Init_Grid()
            .Init_Column("采购明细编码", "M_Buy_Detail_Code", "0", "左", "", False)
            .Init_Column("采购编码", "M_Buy_Code", "0", "左", "", False)
            .Init_Column("物资编码", "Materials_Code", "0", "左", "", False)
            .Init_Column("物资明细编码", "MaterialsStock_Code", 0, "左", "", False)
            .Init_Column("物资名称", "Materials_Name", "100", "左", "", False)
            .Init_Column("批号", "MaterialsLot", "120", "左", "", False)
            .Init_Column("有效期", "MaterialsExpiryDate", "100", "左", "yyyy-MM-dd", False)
            .Init_Column("包装单位", "Pack_Unit", "80", "右", "", False)
            .Init_Column("拆分比例", "Convert_Ratio", "80", "右", "", False)
            .Init_Column("散装单位", "Bulk_Unit", "80", "右", "", False)

            If Rk1Model.WriteOffStatus = "冲销" Then
                .Init_Column("可冲数量", "Can_RealWriteOffNo", "100", "右", "####0.####", False)
                .Init_Column("数量", "M_Buy_Num", "80", "右", "####0.####", ColEdit)
                .Init_Column("采购单价", "M_Buy_Price", "70", "右", "####0.00##", False)
                .Init_Column("入库数量", "M_BuyIn_Num", "100", "左", "", False)
                .Init_Column("入库单价", "M_BuyIn_Price", "90 ", "左", "####0.00##", False)
                .Init_Column("冲销金额", "M_Buy_Money", "100", "右", "##,##0.00##", False)
            Else
                If Rk1Model.OrdersStatus = "完成" Then
                    .Init_Column("数量", "M_Buy_Num", "80", "右", "####0.####", ColEdit)
                    .Init_Column("冲销数量", "M_Buy_WriteoffNo", "80", "右", "####0.####", False)
                    .Init_Column("实际入库库数量", "M_Buy_RealNo", "100", "右", "####0.####", False)
                    .Init_Column("采购单价", "M_Buy_Price", "70", "右", "####0.00##", ColEdit)
                    .Init_Column("入库数量", "M_BuyIn_Num", "100", "左", "", False)
                    .Init_Column("入库冲销数量", "M_BuyIn_WriteoffNo", "100", "左", "", False)
                    .Init_Column("入库实际数量", "M_BuyIn_RealNo", "100", "左", "", False)
                    .Init_Column("入库单价", "M_BuyIn_Price", "90 ", "左", "####0.##", False)
                    .Init_Column("采购金额", "M_Buy_Money", "100", "右", "##,##0.00##", False)
                    .Init_Column("实际采购金额", "M_Buy_RealMoney", "120", "右", "##,##0.00##", False)
                Else
                    .Init_Column("数量", "M_Buy_Num", "80", "右", "####0.####", ColEdit)
                    .Init_Column("采购单价", "M_Buy_Price", "70", "右", "####0.00##", ColEdit)
                    .Init_Column("入库数量", "M_BuyIn_Num", "100", "左", "####0.####", False)
                    .Init_Column("入库单价", "M_BuyIn_Price", "90 ", "左", "####0.00##", False)
                    .Init_Column("采购金额", "M_Buy_Money", "100", "右", "##,##0.00##", False)
                End If
            End If
            .Init_Column("备注", "M_ReturnDetail_Memo", "200", "左", "", ColEdit) '0
            .MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightCell
            If Rk1Model.WriteOffStatus = "冲销" Then
                .AllowAddNew = False '冲销单不允许增加新行
            Else
                .AllowAddNew = ColEdit
            End If
            .ColumnFooters = True
        End With
    End Sub

    Private Sub Yk_Rk2_FormClosing(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosingEventArgs) Handles Me.FormClosing
        If e.CloseReason = CloseReason.UserClosing Then
            Try
                MyGrid1.UpdateData()
                If My_Table.DataSet.HasChanges = True Then '只有录入状态的单据才能保存
                    If Rk1Bll.GetRecordCount("M_Return_Code='" & Code_Text.Text.Trim & "' and OrdersStatus='录入'") = 1 Then
                        If MessageBox.Show("数据尚未保存,是否保存数据?", "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1) = Windows.Forms.DialogResult.OK Then
                            Call Data_Save("保存")
                        End If
                    End If
                End If
            Catch ex As Exception
                MsgBox("当前编辑行数据因未填写完整，将不能保存！", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示：")
                MyGrid1.Delete()
            End Try
        End If
    End Sub
#End Region

#Region "清空__显示"

    Private Sub Zb_Clear()
        Rinsert = True
        Rk1Model = New ModelOld.M_Materials_Buy_In1()
        Rk1Model.M_Buy_Code = Rk1Bll.MaxCode()

        Code_Text.Text = Rk1Model.M_Buy_Code
        Jsr_Text.Text = HisVar.HisVar.JsrName
        Suplier_DtCom.SelectedIndex = -1
        WareHouse_DtCom.SelectedIndex = -1
        Order_Date.Value = Now
        Arrival_Date.Value = Now
        Memo_Text.Text = ""
        V_TotalMoney = 0

        Call BtnState()
        Call P_Data_Show()
        Suplier_DtCom.Select()
    End Sub

    Private Sub Zb_Show()   '显示记录
        Rinsert = False

        With Rk1Model
            Code_Text.Text = .M_Buy_Code
            Jsr_Text.Text = .Jsr_Name
            Suplier_DtCom.SelectedValue = .MaterialsSup_Code
            WareHouse_DtCom.SelectedValue = .MaterialsWh_Code
            Order_Date.Value = .Order_Date
            Arrival_Date.Value = .Arrival_Date
            Memo_Text.Text = .M_Buy_Memo
        End With

        Call BtnState()
        Call P_Data_Show()
    End Sub

    Private Sub P_Data_Show()   '从表数据
        If Rk1Model.WriteOffStatus = "冲销" Then
            My_Table = Rk2Bll.GetWriteOffList("Materials_Buy_In2.M_Buy_Code='" & Rk1Model.M_Buy_Code & "'").Tables(0)
        Else
            My_Table = Rk2Bll.GetList("Materials_Buy_In2.M_Buy_Code='" & Rk1Model.M_Buy_Code & "'").Tables(0)
        End If
        My_Table.Columns("M_Buy_Code").AllowDBNull = True
        My_Table.Columns("M_Buy_Detail_Code").AllowDBNull = True
        My_Table.Columns("MaterialsStock_Code").AllowDBNull = True
        My_Table.Constraints.Clear()
        MyGrid1.DataTable = My_Table
        Cb_Cm = CType(BindingContext(My_Table.DataSet, My_Table.TableName), CurrencyManager)
        Call F_Sum()
        MyGrid1.Focus()
    End Sub

    Private Sub StatisticsDataShow()
        Dim str As String = "Finish_Date BETWEEN '" & Format(Now, "yyyy-MM-dd 00:00:00") & "' AND '" & Format(Now, "yyyy-MM-dd 23:59:59") & "'"
        TodayFormsNo_Lbl.Text = "今日入库单数：" & Rk1Bll.GetRecordCount(str)
        TodayMoney_Lbl.Text = "今日退库总金额：" & Rk1Bll.GetSumMoeny(str)
        str += "AND Jsr_Code='" & HisVar.HisVar.JsrCode & "'"
        BrTodayFormsNo_Lbl.Text = "本人入库单数：" & Rk1Bll.GetRecordCount(str)
        BrTodayMoney_Lbl.Text = "本人退库总金额：" & Rk1Bll.GetSumMoeny(str)
    End Sub
#End Region

#Region "控件__动作"

#Region "按钮"

    Private Sub Comm_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm_Del.Click, Comm_DelAll.Click, Comm_Save.Click,
         Comm_New.Click, Comm_Print.Click, Comm_Close.Click, Comm_Search.Click, Comm_Complete.Click, Comm_WriteOffPart.Click, Comm_WriteOffAll.Click
        Select Case sender.tag
            Case "删除行"

                If MyGrid1.Row >= MyGrid1.RowCount Then
                    MsgBox("请选择一条记录！", MsgBoxStyle.Exclamation, "提示")
                    Exit Sub
                End If

                If Rk1Bll.GetRecordCount("  M_Buy_Code='" & Code_Text.Text.Trim & "' and OrdersStatus ='完成'") > 0 Then
                    MsgBox("已完成单据不能删除！", MsgBoxStyle.Exclamation, "提示")
                    Exit Sub
                End If
                Data_Delete()
            Case "删除单"
                If Rk1Model.Input_Date Is Nothing Then
                    MsgBox("数据尚未保存,无法删除!", MsgBoxStyle.Exclamation, "提示")
                    Exit Sub
                End If
                If Rk1Bll.GetRecordCount("  M_Buy_Code='" & Code_Text.Text.Trim & "' and OrdersStatus ='完成'") > 0 Then
                    MsgBox("已完成单据不能删除！", MsgBoxStyle.Exclamation, "提示")
                    Exit Sub
                End If
                Call Data_DeleteAll()
            Case "新单"
                Data_New()
            Case "保存", "完成"
                Call Data_Save(sender.tag)
            Case "打印"
                If Rk1Model Is Nothing Then
                    MsgBox("数据尚未保存,无法打印!", MsgBoxStyle.Exclamation, "提示")
                    Exit Sub
                End If
                Call Data_Print()
            Case "速查"
                Dim t_Rc As New BaseClass.C_RowChange
                AddHandler t_Rc.ModelChangeEvent, AddressOf ChangeModel
                Dim f As New Materials_Buy_Search(t_Rc)
                f.Owner = Me
                f.ShowDialog()
            Case "退出"
                Me.Close()
            Case "冲销", "一键冲销"
                If Rk1Bll.GetRecordCount("M_Buy_Code='" & Code_Text.Text & "' and OrdersStatus='完成'") = 0 Then
                    MsgBox("未完成单据不能冲销", MsgBoxStyle.Exclamation, "提示")
                    Exit Sub
                End If

                If Rk2Bll.GetRecordCount("M_Buy_Code='" & Code_Text.Text & "' and M_Buy_RealNo>0") = 0 Then
                    MsgBox("该单据已全部被冲销，不能冲销", MsgBoxStyle.Exclamation, "提示")
                    Exit Sub
                End If

                If MsgBox("是否" + sender.Text + "供应商" & Suplier_DtCom.Text & "，采购金额" & V_TotalMoney.ToString("#####0.00##") & "，单号" & Code_Text.Text & "的物资采购单？", MsgBoxStyle.Question + MsgBoxStyle.YesNo + MsgBoxStyle.DefaultButton1, "提示") = DialogResult.Yes Then
                    Dim model As New ModelOld.M_Materials_Buy_In1
                    With model
                        .M_Buy_Code = Rk1Bll.MaxCode()
                        .WriteOffStatus = "冲销"
                        .Input_Date = Now
                        .Jsr_Code = HisVar.HisVar.JsrCode
                        .Jsr_Name = HisVar.HisVar.JsrName
                        .MaterialsSup_Code = Rk1Model.MaterialsSup_Code
                        .MaterialsWh_Code = Rk1Model.MaterialsWh_Code
                        .Order_Date = Rk1Model.Order_Date
                        .Arrival_Date = Rk1Model.Arrival_Date
                        .WriteOff_Code = Rk1Model.M_Buy_Code
                        .TotalMoney = 0
                        .M_Buy_Memo = ""
                        .OrdersStatus = "录入"
                    End With

                    If Rk1Bll.AddWriteOff(model) = True Then
                        If sender.Text = "一键冲销" Then
                            model.OrdersStatus = "完成"
                            model.Finish_Date = Now
                            If Not Rk1Bll.Complete(model) Then
                                model.OrdersStatus = "录入"
                                model.Finish_Date = Nothing
                                MsgBox("完成操作失败!", MsgBoxStyle.Exclamation, "提示")
                                Exit Sub
                            End If
                        End If
                        Dim V_Form As New Materials.Materials_Buy_In1(model)
                        V_Form.Name = V_Form.Name & model.M_Buy_Code
                        BaseFunc.BaseFunc.addTabControl(V_Form, "采购入库冲销-" & Code_Text.Text)
                    End If
                End If
        End Select
    End Sub

    Private Sub Memo_Text_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles Memo_Text.Validated
        MyGrid1.Focus()
    End Sub

#End Region

#Region "DBGrid动作"
    Private Sub MyGrid1_AfterColUpdate(ByVal sender As System.Object, ByVal e As C1.Win.C1TrueDBGrid.ColEventArgs) Handles MyGrid1.AfterColUpdate
        Dim up_Row As DataRow                    '当 前 行
        up_Row = Cb_Cm.List(MyGrid1.Row).Row
        Select Case MyGrid1.Splits(0).DisplayColumns(e.ColIndex).Name
            Case "数量", "采购单价"
                If up_Row("M_Buy_Num") IsNot DBNull.Value And up_Row("M_Buy_Price") IsNot DBNull.Value Then
                    up_Row("M_Buy_Money") = up_Row("M_Buy_Price") * up_Row("M_Buy_Num")
                    up_Row("M_BuyIn_Num") = up_Row("M_Buy_Num") * up_Row("Convert_Ratio")
                    up_Row("M_BuyIn_Price") = up_Row("M_Buy_Price") / up_Row("Convert_Ratio")
                    up_Row("M_Buy_RealMoney") = up_Row("M_BuyIn_Num") * up_Row("M_BuyIn_Price")
                End If
        End Select
    End Sub

    Private Sub MyGrid1_AfterUpdate(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyGrid1.AfterUpdate
        Call F_Sum()
    End Sub

    Private Sub MyGrid1_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles MyGrid1.KeyDown

        Select Case e.KeyCode
            Case Keys.Escape, Keys.F2, Keys.F3, Keys.F4, Keys.F5, Keys.F6, Keys.Up, Keys.Down, Keys.Left, Keys.Right
            Case Keys.Delete
                If Rk1Model.Input_Date IsNot Nothing AndAlso Rk1Model.OrdersStatus = "完成" Then
                    Exit Sub
                End If
                Data_Delete()
            Case Keys.Enter
                If Rk1Model.Input_Date IsNot Nothing AndAlso Rk1Model.WriteOffStatus = "冲销" Then
                    Exit Sub
                End If

                If Rk1Model.OrdersStatus = "完成" Then
                    Exit Sub
                End If

                If MyGrid1.Columns(MyGrid1.Col).DataField <> "M_Buy_Num" And MyGrid1.Columns(MyGrid1.Col).DataField <> "M_Buy_Price" Then
                    Cb_Edit()
                End If
        End Select
    End Sub

    Private Sub MyGrid1_MouseDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles MyGrid1.MouseDown
        If Rk1Model.Input_Date IsNot Nothing AndAlso Rk1Model.WriteOffStatus = "冲销" Then
            Exit Sub
        End If

        If Rk1Model.OrdersStatus = "完成" Then
            Exit Sub
        End If

        If e.Button = Windows.Forms.MouseButtons.Right Then
            Cb_Edit()
        End If
    End Sub

    Private Sub MyGrid1_GotFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles MyGrid1.GotFocus
        Me.CancelButton = Nothing
    End Sub

    Private Sub MyGrid1_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles MyGrid1.Validated
        Me.CancelButton = Comm_Close
    End Sub

    Private Sub MyGrid1_BeforeColUpdate(ByVal sender As System.Object, ByVal e As C1.Win.C1TrueDBGrid.BeforeColUpdateEventArgs) Handles MyGrid1.BeforeColUpdate
        If Zb_Check() = False Then Exit Sub
        If e.Column.Name = "数量" Then
            If Not VerifyNum(MyGrid1.Columns("M_Buy_Num").Value, MyGrid1.Row) Then
                e.Cancel = True
                CanSave = False
            Else
                CanSave = True
            End If
        End If
        If e.Column.Name = "采购单价" Then
            If Not VerifyPrice(MyGrid1.Columns("M_Buy_Price").Value) Then
                e.Cancel = True
                CanSave = False
            Else
                CanSave = True
            End If
        End If
    End Sub

    'Private Sub MyGrid1_Error(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.ErrorEventArgs) Handles MyGrid1.Error
    '    e.Handled = True
    '    flag_Error = True
    '    If StrConv(e.Exception.Message, VbStrConv.Narrow) = "列""M_Buy_Num""不允许 nulls?" Then
    '        MessageBox.Show("包装数量不能为空!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
    '        MyGrid1.SetActiveCell(MyGrid1.Row, MyGrid1.Splits(0).DisplayColumns.IndexOf(MyGrid1.Columns("M_Buy_Num")))
    '    End If

    '    If StrConv(e.Exception.Message, VbStrConv.Narrow) = "列""M_Buy_Price""不允许 nulls?" Then
    '        MessageBox.Show("采购单价不能为空!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
    '        MyGrid1.SetActiveCell(MyGrid1.Row, MyGrid1.Splits(0).DisplayColumns.IndexOf(MyGrid1.Columns("M_Buy_Price")))
    '    End If
    '    Me.MyGrid1.Row = Me.MyGrid1.Row - 1
    'End Sub

#End Region

#Region "快捷键"
    Private Sub PjCl_Rk2_KeyDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles MyBase.KeyDown
        If e.Control = True And e.KeyCode = Keys.S And Comm_Save.Enabled = True Then
            Call Data_Save("保存")
        End If

        If e.KeyData = Keys.F2 And Comm_New.Enabled = True Then
            MyGrid1.Focus()
            sender.tag = "新单"
            Call Comm_Click(sender, Nothing)
        End If

        If e.KeyData = Keys.F3 And Comm_Save.Enabled = True Then
            MyGrid1.Focus()
            sender.tag = "保存"
            Call Comm_Click(sender, Nothing)
        End If

        If e.KeyData = Keys.F4 And Comm_Complete.Enabled = True Then
            MyGrid1.Focus()
            sender.tag = "完成"
            Call Comm_Click(sender, Nothing)
        End If

        If e.KeyData = Keys.F5 And Comm_Print.Enabled = True Then
            MyGrid1.Focus()
            sender.tag = "打印"
            Call Comm_Click(sender, Nothing)
        End If
        If e.KeyData = Keys.F6 And Comm_Search.Enabled = True Then
            MyGrid1.Focus()
            sender.tag = "速查"
            Call Comm_Click(sender, Nothing)
        End If
    End Sub
#End Region

#End Region

#Region "自定义函数"

#Region "验证函数"
    Private Function Zb_Check() As Boolean
        If Suplier_DtCom.SelectedIndex = -1 Then
            Suplier_DtCom.Select()
            MessageBox.Show("请选择供应商!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
            Return False

        ElseIf WareHouse_DtCom.SelectedIndex = -1 Then
            WareHouse_DtCom.Select()
            MessageBox.Show("请选择仓库!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
            Return False
        Else
            Return True
        End If
    End Function

    Private Function Zb_CheckWc() As Boolean
        If Rk1Bll.GetRecordCount("M_Buy_Code='" & Code_Text.Text & "' And OrdersStatus='完成'") > 0 Then
            MsgBox("此次采购已经完成!", MsgBoxStyle.Exclamation, "提示")
            Return False
        End If
        Return True
    End Function

    Private Function Cb_CheckRowCounts() As Boolean
        If MyGrid1.RowCount = 0 Then
            MsgBox("尚未录入数据", MsgBoxStyle.Exclamation, "提示")
            Return False
        End If
        Return True
    End Function

    Private Function Cb_Check() As Boolean
        MyGrid1.UpdateData()
        Dim i As Integer = 0
        For Each _Row In My_Table.Rows
            If _Row.RowState = DataRowState.Deleted Then
                Continue For
            End If
            If VerifyNum(_Row("M_Buy_Num"), i) = False Then
                MyGrid1.Focus()
                MyGrid1.SetActiveCell(i, MyGrid1.Splits(0).DisplayColumns.IndexOf(MyGrid1.Columns("M_Buy_Num")))
                Return False
            End If
            If Not VerifyPrice(_Row("M_Buy_Price")) Then
                MyGrid1.Focus()
                MyGrid1.SetActiveCell(MyGrid1.Row, MyGrid1.Splits(0).DisplayColumns.IndexOf(MyGrid1.Columns("M_Buy_Price")))
                Return False
            End If
            If Rk1Model.WriteOffStatus = "冲销" Then
                _Row("M_Buy_RealNo") = 0
                _Row("M_Buy_WriteoffNo") = 0
                _Row("M_BuyIn_RealNo") = 0
                _Row("M_BuyIn_WriteoffNo") = 0
                _Row("M_Buy_Money") = _Row("M_Buy_Num") * _Row("M_Buy_Price")
                _Row("M_Buy_RealMoney") = 0
            Else
                _Row("M_Buy_RealNo") = _Row("M_Buy_Num") + _Row("M_Buy_WriteoffNo")
                _Row("M_BuyIn_RealNo") = _Row("M_BuyIn_Num") + _Row("M_BuyIn_WriteoffNo")
                _Row("M_Buy_Money") = CDec(_Row("M_Buy_Num")) * CDec(_Row("M_Buy_Price"))
                _Row("M_Buy_RealMoney") = CDec(_Row("M_Buy_RealNo")) * CDec(_Row("M_Buy_Price"))
            End If
            i = i + 1
        Next
        Return True
    End Function

    '验证退库数量是否正确
    Private Function VerifyNum(ByVal Num As String, ByVal Index As Integer) As Boolean
        If Common.Tools.IsNumber(Num) = False Then
            HisControl.msg.Show("请输入正确数字!", "提示")
            Return False
        End If
        If Rk1Model.WriteOffStatus & "" <> "冲销" Then
            If CDbl(Num) <= 0 Then
                HisControl.msg.Show("采购数量必须大于0!", "提示")
                Return False
            End If
        Else
            If CDbl(Num) >= 0 Then
                HisControl.msg.Show("冲销数量必须小于0!", "提示")
                'MessageBox.Show("冲销数量必须小于0!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
                Return False
            End If
        End If

        If Rk1Model.WriteOffStatus & "" = "冲销" Then '冲销单据不能超过可冲数量
            Dim row As DataRow = Cb_Cm.List(Index).row
            If -CDbl(Num) > Rk2Bll.GetModelwhere("M_Buy_Code='" & Rk1Model.WriteOff_Code & "' and MaterialsStock_Code='" & row("MaterialsStock_Code") & "'").M_Buy_RealNo Then
                HisControl.msg.Show("冲销数量不能大于可冲数量!", "提示")
                Return False
            End If

            If -CDbl(Num) > KcBll.GetModel(row("MaterialsStock_Code")).MaterialsStore_Num Then
                HisControl.msg.Show("冲销数量不能大于库存数量!", "提示")
                Return False
            End If
        End If
        Return True
    End Function

    '验证退库数量是否正确
    Private Function VerifyPrice(ByVal Num As String) As Boolean
        If  Common.Tools.IsNumber(Num) = False Then
            HisControl.msg.Show("请输入正确数字!", "提示")
            Return False
        End If
        If CDbl(Num) < 0 Then
            HisControl.msg.Show("采购单价必须大于0!", "提示")
            Return False
        End If
        Return True
    End Function

#End Region

#Region "按钮函数"

    Private Sub Data_Delete()
        If MessageBox.Show("确认是否删除当前记录,如果删除该记录将不可恢复!", "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Error, MessageBoxDefaultButton.Button2) = Windows.Forms.DialogResult.OK Then
            Try
                MyGrid1.Delete()
                Call F_Sum()
            Catch ex As Exception
                If ex.Message.ToString = "索引 -1 不是为负数，就是大于行数。" Then
                    MsgBox("未选中任何行！", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
                End If
            End Try

        End If
    End Sub

    Private Sub Data_DeleteAll()
        If MessageBox.Show("确认是否删除当前单据,如果删除该单据将不可恢复!", "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Error, MessageBoxDefaultButton.Button2) = Windows.Forms.DialogResult.OK Then
            Rk1Bll.Delete(Rk1Model.M_Buy_Code)
            Zb_Clear()
        End If
    End Sub

    Private Sub Data_New()
        MyGrid1.UpdateData()
        If My_Table.DataSet.HasChanges = True Then
            If Rk1Bll.GetRecordCount("  M_Buy_Code='" & Code_Text.Text.Trim & "' and OrdersStatus='录入'") = 1 Then
                If MessageBox.Show("数据尚未保存,是否保存数据?", "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1) = Windows.Forms.DialogResult.OK Then
                    Call Data_Save("保存")
                End If
            End If
        End If
        Call Zb_Clear()
    End Sub

    Private Sub Data_Save(ByVal arg As String)
        If Not CanSave Then
            Exit Sub
        End If
        If Not Zb_CheckWc() Then Exit Sub
        If Not Zb_Check() Then Exit Sub
        If Not Cb_CheckRowCounts() Then Exit Sub
        If Not Cb_Check() Then Exit Sub

        If arg = "保存" Then
            Call Zb_Save()
        ElseIf arg = "完成" Then
            Call Zb_Save()
            Call Data_Complete()
        End If
    End Sub

    Private Sub Data_Complete()
        If MsgBox("是否完成此采购单", MsgBoxStyle.Information + MsgBoxStyle.OkCancel + MsgBoxStyle.DefaultButton1, "提示:") = MsgBoxResult.Cancel Then Exit Sub
        If Not Zb_CheckWc() Then Exit Sub

        Try
            Rk1Model.OrdersStatus = "完成"
            Rk1Model.Finish_Date = Now
            If Not Rk1Bll.Complete(Rk1Model) Then
                With Rk1Model
                    .Finish_Date = Nothing
                    .OrdersStatus = "录入"
                End With
            End If
            StatisticsDataShow()
            HisControl.msg.Show("采购完成!", "提示")
            Call BtnState()
            Call P_Data_Show()
            Focus()
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            Suplier_DtCom.Select()
            Exit Sub
        End Try

    End Sub

    Private Sub Data_Print()
        Dim StiRpt As New StiReport
        StiRpt.Load(".\Rpt\物资采购入库单.mrt")

        StiRpt.ReportName = "采购入库单"
        StiRpt.RegData(My_Table)
        StiRpt.Pages(0).PaperSize = Printing.PaperKind.A4
        StiRpt.Pages(0).Margins.Top = 1
        StiRpt.Pages(0).Margins.Bottom = 1
        StiRpt.Pages(0).Margins.Left = 1
        StiRpt.Pages(0).Margins.Right = 1
        StiRpt.Compile()

        StiRpt("采购仓库") = WareHouse_DtCom.Text
        StiRpt("供应商") = Suplier_DtCom.Text
        StiRpt("采购单号") = Code_Text.Text
        StiRpt("采购日期") = Format(Arrival_Date.Value, "yyyy年MM月dd日")
        StiRpt("打印时间") = Format(Now, "yyyy-MM-dd HH:mm:ss")
        StiRpt("采购员") = HisVar.HisVar.JsrName

        StiRpt.Render()
        'StiRpt.Design()
        StiRpt.Show()
    End Sub

#End Region

#Region "数据库更改"

#Region "主表__编辑"

    Private Sub Zb_Save()                       '主表保存
        If Rinsert = True Then     '增加记录
            Call Zb_Add()
        Else                                '编辑记录
            Call Zb_Edit()
        End If
        Call Cb_Update()
        HisControl.msg.Show("数据保存成功!", "提示")
        MyGrid1.Focus()
    End Sub

    Private Sub Zb_Add()    '增加记录
        Try
            With Rk1Model
                .M_Buy_Code = Rk1Bll.MaxCode()
                .MaterialsSup_Code = Suplier_DtCom.SelectedValue
                .MaterialsWh_Code = WareHouse_DtCom.SelectedValue
                .Order_Date = Format(Order_Date.Value, "yyyy-MM-dd HH:mm:ss")
                .Arrival_Date = Format(Arrival_Date.Value, "yyyy-MM-dd HH:mm:ss")
                .Input_Date = Format(Now, "yyyy-MM-dd HH:mm:ss")
                .TotalMoney = V_TotalMoney
                .Jsr_Code = HisVar.HisVar.JsrCode
                .M_Buy_Memo = Trim(Memo_Text.Text & "")
                .OrdersStatus = "录入"
                Code_Text.Text = .M_Buy_Code
            End With
            Rk1Bll.Add(Rk1Model)
            Rinsert = False
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            Suplier_DtCom.Select()
            Exit Sub
        End Try
    End Sub

    Private Sub Zb_Edit()   '编辑记录
        Try
            With Rk1Model
                .MaterialsSup_Code = Suplier_DtCom.SelectedValue
                .MaterialsWh_Code = WareHouse_DtCom.SelectedValue
                .Order_Date = Format(Order_Date.Value, "yyyy-MM-dd HH:mm:ss")
                .Arrival_Date = Format(Arrival_Date.Value, "yyyy-MM-dd HH:mm:ss")
                .TotalMoney = V_TotalMoney
                .Jsr_Code = HisVar.HisVar.JsrCode
                .M_Buy_Memo = Trim(Memo_Text.Text & "")
            End With
            Rk1Bll.Update(Rk1Model)
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
            Exit Sub
        End Try
    End Sub

#End Region

#Region "从表__编辑"
    Private Sub Cb_Edit()
        If Rk1Bll.GetRecordCount("M_Buy_Code='" & Code_Text.Text & "' And OrdersStatus='完成'") > 0 Then
            MsgBox("此次采购已经完成!", MsgBoxStyle.Exclamation, "提示")
            Exit Sub
        End If
        Dim V_Insert As Boolean
        If Rk1Model.WriteOffStatus <> "冲销" And Rk1Bll.GetRecordCount("M_Buy_Code='" & Code_Text.Text & "' and OrdersStatus='完成'") = 0 Then
            If Zb_Check() = False Then Exit Sub
        End If
        If (MyGrid1.Row + 1) > MyGrid1.RowCount Then
            V_Insert = True
        Else
            V_Insert = False
            Cb_Row = Cb_Cm.List(MyGrid1.Row).Row
        End If
        Dim vform As New BaseChild
        vform = New Materials_Buy_In2(V_Insert, Cb_Row, My_Table, MyGrid1, Rk1Model.M_Buy_Code, m_Rc)
        If BaseFunc.BaseFunc.CheckOwnForm(Me, vform) = False Then
            vform.Owner = Me
            vform.Show()
        End If
    End Sub

    Private Sub Cb_Update()   '从表更新
        For Each _row As DataRow In My_Table.Rows
            If _row.RowState = DataRowState.Added Then
                CbData_Insert(_row)
            End If
            If _row.RowState = DataRowState.Modified Then
                CbData_Update(_row)
            End If
            If _row.RowState = DataRowState.Deleted Then
                CbData_Delete(_row)
            End If
        Next
        My_Table.AcceptChanges()
    End Sub

    Private Sub CbData_Update(ByVal Cb_Row As DataRow)   '从表更新
        Dim model As ModelOld.M_Materials_Buy_In2 = Rk2Bll.GetModel(Cb_Row("M_Buy_Detail_Code"))
        With model
            .M_Buy_Code = Cb_Row.Item("M_Buy_Code")
            .Materials_Code = Cb_Row.Item("Materials_Code")
            .M_Buy_Detail_Code = Cb_Row.Item("M_Buy_Detail_Code")
            .MaterialsLot = Cb_Row.Item("MaterialsLot")
            .MaterialsExpiryDate = Cb_Row.Item("MaterialsExpiryDate")
            .M_Buy_Num = Cb_Row.Item("M_Buy_Num")
            .M_Buy_WriteoffNo = Cb_Row.Item("M_Buy_WriteoffNo")
            .M_Buy_RealNo = Cb_Row.Item("M_Buy_RealNo")
            .M_BuyIn_Num = Cb_Row.Item("M_BuyIn_Num")
            .M_BuyIn_WriteoffNo = Cb_Row.Item("M_BuyIn_WriteoffNo")
            .M_BuyIn_RealNo = Cb_Row.Item("M_BuyIn_RealNo")
            .M_Buy_Price = Cb_Row.Item("M_Buy_Price")
            .M_BuyIn_Price = Cb_Row.Item("M_BuyIn_Price")
            .M_Buy_Money = Cb_Row.Item("M_Buy_Money")
            .M_Buy_RealMoney = Cb_Row.Item("M_Buy_RealMoney")
            .Pack_Unit = Cb_Row.Item("Pack_Unit")
            .Convert_Ratio = CInt(Cb_Row.Item("Convert_Ratio"))
            .Bulk_Unit = Cb_Row.Item("Bulk_Unit")
            .M_BuyDetail_Memo = Cb_Row.Item("M_BuyDetail_Memo")
        End With
        Rk2Bll.Update(model)
    End Sub

    Private Sub CbData_Insert(ByVal Cb_Row As DataRow)    '从表增加
        Cb_Row("M_Buy_Code") = Rk1Model.M_Buy_Code
        Cb_Row("M_Buy_Detail_Code") = Rk2Bll.MaxCode(Rk1Model.M_Buy_Code)

        Dim model As New ModelOld.M_Materials_Buy_In2
        With model
            .M_Buy_Code = Cb_Row.Item("M_Buy_Code")
            .Materials_Code = Cb_Row.Item("Materials_Code")
            .M_Buy_Detail_Code = Cb_Row.Item("M_Buy_Detail_Code")
            .MaterialsLot = Cb_Row.Item("MaterialsLot")
            .MaterialsExpiryDate = Cb_Row.Item("MaterialsExpiryDate")
            .M_Buy_Num = Cb_Row.Item("M_Buy_Num")
            .M_Buy_WriteoffNo = Cb_Row.Item("M_Buy_WriteoffNo")
            .M_Buy_RealNo = Cb_Row.Item("M_Buy_RealNo")
            .M_BuyIn_Num = Cb_Row.Item("M_BuyIn_Num")
            .M_BuyIn_WriteoffNo = Cb_Row.Item("M_BuyIn_WriteoffNo")
            .M_BuyIn_RealNo = Cb_Row.Item("M_BuyIn_RealNo")
            .M_Buy_Price = Cb_Row.Item("M_Buy_Price")
            .M_BuyIn_Price = Cb_Row.Item("M_BuyIn_Price")
            .M_Buy_Money = Cb_Row.Item("M_Buy_Money")
            .M_Buy_RealMoney = Cb_Row.Item("M_Buy_RealMoney")
            .Pack_Unit = Cb_Row.Item("Pack_Unit")
            .Convert_Ratio = CInt(Cb_Row.Item("Convert_Ratio"))
            .Bulk_Unit = Cb_Row.Item("Bulk_Unit")
            .M_BuyDetail_Memo = Cb_Row.Item("M_BuyDetail_Memo")
        End With
        Rk2Bll.Add(model)
    End Sub

    Private Sub CbData_Delete(ByVal Cb_Row As DataRow)
        Rk2Bll.Delete(Cb_Row.Item("M_Buy_Detail_Code", DataRowVersion.Original))
    End Sub
#End Region

#End Region

    '生成 指定库房，指定物资，指定批号的 库存编码（如果在Materials_Stock 表中 已经存在指定库房，指定物资，指定批号 的库存编码 则使用该库存编码MaterialsStock_Code，否则生成一个新的库存编码，并将信息插入Materials_Stock表中）
    Private Function GetMaterialsStockCode(ByVal _row As DataRow) As String

        Try
            Dim MaterialsStock_Code As String = KcBll.GetMaterialsStockCode(_row("Materials_Code"), WareHouse_DtCom.SelectedValue, _row("MaterialsLot"), _row("MaterialsExpiryDate"), _row("M_BuyIn_Price"))

            If String.IsNullOrEmpty(MaterialsStock_Code) Then
                MaterialsStock_Code = KcBll.MaxCode(_row("Materials_Code"))

                Dim model As New ModelOld.M_Materials_Stock
                With model
                    .Materials_Code = _row("Materials_Code")
                    .MaterialsWh_Code = WareHouse_DtCom.SelectedValue
                    .MaterialsStock_Code = MaterialsStock_Code
                    .MaterialsLot = _row("MaterialsLot")
                    .MaterialsExpiryDate = Format(_row("MaterialsExpiryDate"), "yyyy-MM-dd")
                    .MaterialsStore_Num = 0
                    .MaterialsStore_Price = _row("M_BuyIn_Price")
                    .MaterialsStore_Money = 0
                End With

                If Not KcBll.Add(model) Then Return Nothing

            End If
            Return MaterialsStock_Code
        Catch ex As Exception
            MsgBox(ex.ToString, MsgBoxStyle.Exclamation, "提示")
            Return Nothing
        End Try

    End Function

    Private Sub ChangeModel(ByVal model As ModelOld.M_Materials_Buy_In1)
        Rk1Model = model
        If Rk1Model Is Nothing Then Exit Sub
        Call Zb_Show()
    End Sub

    Public Overrides Sub F_Sum()
        If MyGrid1.RowCount = 0 Then
            WareHouse_DtCom.Enabled = True
        Else
            WareHouse_DtCom.Enabled = False
        End If
        Dim Total_Buy_Money As Double
        Total_Buy_Money = IIf(My_Table.Compute("Sum(M_Buy_Money)", "") Is DBNull.Value, 0, My_Table.Compute("Sum(M_Buy_Money)", ""))
        V_TotalMoney = IIf(My_Table.Compute("Sum(M_Buy_RealMoney)", "") Is DBNull.Value, 0, My_Table.Compute("Sum(M_Buy_RealMoney)", ""))
        MyGrid1.Columns("M_Buy_Money").FooterText = Total_Buy_Money.ToString("0.00##")
        If Rk1Model.OrdersStatus = "完成" And Rk1Model.WriteOffStatus <> "冲销" Then MyGrid1.Columns("M_Buy_RealMoney").FooterText = CDbl(Rk1Model.TotalMoney).ToString("0.00##")
    End Sub

#End Region

#Region "输入法设置"
    Private Sub Chs_Input(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Memo_Text.GotFocus
        InputLanguage.CurrentInputLanguage = HisVar.HisVar.InputCode
    End Sub
    '英文
    Private Sub Eng_Input(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Suplier_DtCom.GotFocus, WareHouse_DtCom.GotFocus
        InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages(0)
    End Sub
#End Region


End Class