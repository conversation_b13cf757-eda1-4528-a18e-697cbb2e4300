﻿/**  版本信息模板在安装目录下，可自行修改。
* M_Zd_YyYs.cs
*
* 功 能： N/A
* 类 名： M_Zd_YyYs
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/8/11 9:17:05   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace ModelOld
{
	/// <summary>
	/// M_Zd_YyYs:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class M_Zd_YyYs
	{
		public M_Zd_YyYs()
		{}
		#region Model
		private string _yy_code;
		private string _ys_code;
		private string _ks_code;
		private string _ys_name;
		private string _ys_jc;
		private string _ys_sex;
		private string _ys_memo;
		private string _ys_use;
		/// <summary>
		/// 
		/// </summary>
		public string Yy_Code
		{
			set{ _yy_code=value;}
			get{return _yy_code;}
		}
		/// <summary>
		/// 医生编码
		/// </summary>
		public string Ys_Code
		{
			set{ _ys_code=value;}
			get{return _ys_code;}
		}
		/// <summary>
		/// 科室编码
		/// </summary>
		public string Ks_Code
		{
			set{ _ks_code=value;}
			get{return _ks_code;}
		}
		/// <summary>
		/// 医生名称
		/// </summary>
		public string Ys_Name
		{
			set{ _ys_name=value;}
			get{return _ys_name;}
		}
		/// <summary>
		/// 简称
		/// </summary>
		public string Ys_Jc
		{
			set{ _ys_jc=value;}
			get{return _ys_jc;}
		}
		/// <summary>
		/// 性别
		/// </summary>
		public string Ys_Sex
		{
			set{ _ys_sex=value;}
			get{return _ys_sex;}
		}
		/// <summary>
		/// 备注
		/// </summary>
		public string Ys_Memo
		{
			set{ _ys_memo=value;}
			get{return _ys_memo;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Ys_Use
		{
			set{ _ys_use=value;}
			get{return _ys_use;}
		}
		#endregion Model

	}
}

