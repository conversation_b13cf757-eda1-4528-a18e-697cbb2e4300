﻿/**  版本信息模板在安装目录下，可自行修改。
* D_Materials_Class_Dict.cs
*
* 功 能： N/A
* 类 名： D_Materials_Class_Dict
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016-11-26 11:32:26   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;

namespace DAL
{
	/// <summary>
	/// 数据访问类:D_Materials_Class_Dict
	/// </summary>
	public partial class D_Materials_Class_Dict
	{
		public D_Materials_Class_Dict()
		{}
		#region  BasicMethod

        public string MaxCode()
        {
            string max = (string)(HisVar.HisVar.Sqldal.F_MaxCode("select max(Class_Code) from Materials_Class_Dict", 5));
            return max;
        }

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string Class_Code)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from Materials_Class_Dict");
			strSql.Append(" where Class_Code=@Class_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Class_Code", SqlDbType.Char,5)			};
			parameters[0].Value = Class_Code;

			return HisVar.HisVar.Sqldal.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ModelOld.M_Materials_Class_Dict model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into Materials_Class_Dict(");
			strSql.Append("Class_Code,Class_Name,Class_Py,Class_Wb,Class_Father,HaveChild,Serial_No)");
			strSql.Append(" values (");
			strSql.Append("@Class_Code,@Class_Name,@Class_Py,@Class_Wb,@Class_Father,@HaveChild,@Serial_No)");
            SqlParameter[] parameters = {
					new SqlParameter("@Class_Code", SqlDbType.Char,5),
					new SqlParameter("@Class_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Class_Py", SqlDbType.VarChar,50),
					new SqlParameter("@Class_Wb", SqlDbType.VarChar,50),
					new SqlParameter("@Class_Father", SqlDbType.Char,5),
					new SqlParameter("@HaveChild", SqlDbType.Bit,1),
					new SqlParameter("@Serial_No", SqlDbType.Int,4)};
			parameters[0].Value = model.Class_Code;
			parameters[1].Value = model.Class_Name;
			parameters[2].Value = model.Class_Py;
            parameters[3].Value = Common.Tools.IsValueNull(model.Class_Wb);
			parameters[4].Value = model.Class_Father;
			parameters[5].Value = model.HaveChild;
            parameters[6].Value = Common.Tools.IsValueNull(model.Serial_No);

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ModelOld.M_Materials_Class_Dict model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update Materials_Class_Dict set ");
			strSql.Append("Class_Name=@Class_Name,");
			strSql.Append("Class_Py=@Class_Py,");
			strSql.Append("Class_Wb=@Class_Wb,");
			strSql.Append("Class_Father=@Class_Father,");
			strSql.Append("HaveChild=@HaveChild,");
			strSql.Append("Serial_No=@Serial_No");
			strSql.Append(" where Class_Code=@Class_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Class_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Class_Py", SqlDbType.VarChar,50),
					new SqlParameter("@Class_Wb", SqlDbType.VarChar,50),
					new SqlParameter("@Class_Father", SqlDbType.Char,5),
					new SqlParameter("@HaveChild", SqlDbType.Bit,1),
					new SqlParameter("@Serial_No", SqlDbType.Int,4),
					new SqlParameter("@Class_Code", SqlDbType.Char,5)};
			parameters[0].Value = model.Class_Name;
			parameters[1].Value = model.Class_Py;
            parameters[2].Value = Common.Tools.IsValueNull(model.Class_Wb);
			parameters[3].Value = model.Class_Father;
			parameters[4].Value = model.HaveChild;
            parameters[5].Value = Common.Tools.IsValueNull(model.Serial_No);
			parameters[6].Value = model.Class_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string Class_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Materials_Class_Dict ");
			strSql.Append(" where Class_Code=@Class_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Class_Code", SqlDbType.Char,5)			};
			parameters[0].Value = Class_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string Class_Codelist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Materials_Class_Dict ");
			strSql.Append(" where Class_Code in ("+Class_Codelist + ")  ");
			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Materials_Class_Dict GetModel(string Class_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 Class_Code,Class_Name,Class_Py,Class_Wb,Class_Father,HaveChild,Serial_No from Materials_Class_Dict ");
			strSql.Append(" where Class_Code=@Class_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Class_Code", SqlDbType.Char,5)			};
			parameters[0].Value = Class_Code;

			ModelOld.M_Materials_Class_Dict model=new ModelOld.M_Materials_Class_Dict();
			DataSet ds=HisVar.HisVar.Sqldal.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Materials_Class_Dict DataRowToModel(DataRow row)
		{
			ModelOld.M_Materials_Class_Dict model=new ModelOld.M_Materials_Class_Dict();
			if (row != null)
			{
				if(row["Class_Code"]!=null)
				{
					model.Class_Code=row["Class_Code"].ToString();
				}
				if(row["Class_Name"]!=null)
				{
					model.Class_Name=row["Class_Name"].ToString();
				}
				if(row["Class_Py"]!=null)
				{
					model.Class_Py=row["Class_Py"].ToString();
				}
				if(row["Class_Wb"]!=null)
				{
					model.Class_Wb=row["Class_Wb"].ToString();
				}
				if(row["Class_Father"]!=null)
				{
					model.Class_Father=row["Class_Father"].ToString();
				}
				if(row["HaveChild"]!=null && row["HaveChild"].ToString()!="")
				{
					if((row["HaveChild"].ToString()=="1")||(row["HaveChild"].ToString().ToLower()=="true"))
					{
						model.HaveChild=true;
					}
					else
					{
						model.HaveChild=false;
					}
				}
				if(row["Serial_No"]!=null && row["Serial_No"].ToString()!="")
				{
					model.Serial_No=int.Parse(row["Serial_No"].ToString());
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select Class_Code,Class_Name,Class_Py,Class_Wb,Class_Father,HaveChild,Serial_No ");
			strSql.Append(" FROM Materials_Class_Dict ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" Class_Code,Class_Name,Class_Py,Class_Wb,Class_Father,HaveChild,Serial_No");
			strSql.Append(" FROM Materials_Class_Dict ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}


		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) FROM Materials_Class_Dict ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}


		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.Class_Code desc");
			}
			strSql.Append(")AS Row, T.*  from Materials_Class_Dict T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Materials_Class_Dict";
			parameters[1].Value = "Class_Code";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return HisVar.HisVar.Sqldal.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

