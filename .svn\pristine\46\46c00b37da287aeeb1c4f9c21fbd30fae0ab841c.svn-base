﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="0" />
    <DataSources isList="true" count="1">
      <ds Ref="2" type="DataTableSource" isKey="true">
        <Alias>ds</Alias>
        <Columns isList="true" count="23">
          <value>id,System.Int32</value>
          <value>B<PERSON><PERSON>rzhZfJe,System.Decimal</value>
          <value>BcTcZfJe,System.Decimal</value>
          <value>BcXjZfJe,System.Decimal</value>
          <value>MzZy_Code,System.String</value>
          <value>Ry_Name,System.String</value>
          <value>Lb,System.String</value>
          <value>YbJz_Code,System.String</value>
          <value>Jsr_Code,System.String</value>
          <value>JSR_NAME,System.String</value>
          <value>Lr_Date,System.DateTime</value>
          <value>Tb_Lb,System.String</value>
          <value>Id,System.Int32</value>
          <value>BcFhJbYlbxFyJe,System.Decimal</value>
          <value>BcFhJbYlbxWZfJe,System.Decimal</value>
          <value>BcGwyBzZfJe,System.Decimal</value>
          <value>BcJrDbBf,System.Decimal</value>
          <value>JshICYe,System.Decimal</value>
          <value>YlFyZe,System.Decimal</value>
          <value>BnZyCs,System.Decimal</value>
          <value>BcQfxBz,System.Decimal</value>
          <value>Js_Zt,System.Boolean</value>
          <value>Js_Zt_CHS,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>ds</Name>
        <NameInSource>ds</NameInSource>
      </ds>
    </DataSources>
    <Relations isList="true" count="0" />
    <Report isRef="0" />
    <Variables isList="true" count="14">
      <value>,操作员,操作员,System.String,,False,False</value>
      <value>,标题,标题,System.String,,False,False</value>
      <value>,结算时间,结算时间,System.String,,False,False</value>
      <value>,打印时间,打印时间,System.String,,False,False</value>
      <value>,报销笔数,报销笔数,System.String,,False,False</value>
      <value>,日结后报销金额,日结后报销金额,System.String,,False,False</value>
      <value>,日结后个人账户支付金额,日结后个人账户支付金额,System.String,,False,False</value>
      <value>,回退笔数,回退笔数,System.String,,False,False</value>
      <value>,实际笔数,实际笔数,System.String,,False,False</value>
      <value>,日结后笔数,日结后笔数,System.String,,False,False</value>
      <value>,报销金额,报销金额,System.String,,False,False</value>
      <value>,回退金额,回退金额,System.String,,False,False</value>
      <value>,日结后现金,日结后现金,System.String,,False,False</value>
      <value>,实际金额,实际金额,System.String,,False,False</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="3" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="6">
        <ReportTitleBand1 Ref="4" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,18.6,1.9</ClientRectangle>
          <Components isList="true" count="9">
            <Text1 Ref="5" type="Text" isKey="true">
              <Border>Bottom;Black;1;Double;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,18.6,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>黑体,15,Regular,Point,False,134</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text1</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>{标题}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text1>
            <Text2 Ref="6" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>12.6,0.7,6,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text2</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>打印时间:{打印时间}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="7" type="CustomFormat" isKey="true">
                <StringFormat>yyyy-MM-dd HH:mm:ss</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text2>
            <Text4 Ref="8" type="Text" isKey="true">
              <Border>None;Black;1;Double;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.5,0.7,8.1,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text4</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>结算时间:{结算时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text4>
            <Text3 Ref="9" type="Text" isKey="true">
              <Border>None;Black;1;Double;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.7,4.5,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <Guid>fc83a7b8ee974958b52d9fe9bbcdfa0c</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text3</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>操作员:{操作员}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text3>
            <Text6 Ref="10" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.4,3.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>a9c93574fa97455cba2e7d24beab4a11</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text6</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>HIS编码</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text6>
            <Text7 Ref="11" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.5,1.4,2.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>c28733852ffd44fe93543f54d6d120d8</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text7</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>姓名</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text7>
            <Text8 Ref="12" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6.4,1.4,4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>26d8f4e6a8e24c20ae3000d70c8b4af5</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text8</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>个人帐户支付金额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text8>
            <Text9 Ref="13" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>10.4,1.4,4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>c1802fdc39144e84851e517d781354fb</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text9</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>本次统筹支付金额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text9>
            <Text10 Ref="14" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>14.4,1.4,4.2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>95fb4e0dd58a40efab71a820721ff924</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text10</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>本次现金支付金额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text10>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>ReportTitleBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </ReportTitleBand1>
        <GroupHeaderBand1 Ref="15" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,3.1,18.6,0.5</ClientRectangle>
          <Components isList="true" count="1">
            <Text15 Ref="16" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,18.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text15</Name>
              <Page isRef="3" />
              <Parent isRef="15" />
              <Text>{ds.Lb}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text15>
          </Components>
          <Condition>{ds.Lb}</Condition>
          <Conditions isList="true" count="0" />
          <Name>GroupHeaderBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </GroupHeaderBand1>
        <DataBand1 Ref="17" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,4.4,18.6,0.5</ClientRectangle>
          <Components isList="true" count="5">
            <Text45 Ref="18" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,3.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>42b8fba5617d4dbfbaf9580a6963cc0a</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text45</Name>
              <Page isRef="3" />
              <Parent isRef="17" />
              <Text>{ds.MzZy_Code}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text45>
            <Text46 Ref="19" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.5,0,2.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>1d24309d915e4a54be53c348b2dc345c</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text46</Name>
              <Page isRef="3" />
              <Parent isRef="17" />
              <Text>{ds.Ry_Name}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text46>
            <Text47 Ref="20" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6.4,0,4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>3e3e82a6ac58446da5f8bca19e7ba0e5</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text47</Name>
              <Page isRef="3" />
              <Parent isRef="17" />
              <Text>{ds.BcGrzhZfJe}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text47>
            <Text48 Ref="21" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>10.4,0,4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>12456637c3d0462d800138738d45c5da</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text48</Name>
              <Page isRef="3" />
              <Parent isRef="17" />
              <Text>{ds.BcTcZfJe}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text48>
            <Text5 Ref="22" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>14.4,0,4.2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>bec2c222252d4c5b9ffd7651d8a57763</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text5</Name>
              <Page isRef="3" />
              <Parent isRef="17" />
              <Text>{ds.BcXjZfJe}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text5>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>ds</DataSourceName>
          <Filters isList="true" count="0" />
          <Name>DataBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Sort isList="true" count="0" />
        </DataBand1>
        <GroupFooterBand1 Ref="23" type="GroupFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,5.7,18.6,0.5</ClientRectangle>
          <Components isList="true" count="4">
            <Text16 Ref="24" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6.4,0,4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>be501a0a2a83477ab2a67e39c9e313a5</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text16</Name>
              <Page isRef="3" />
              <Parent isRef="23" />
              <Text>{Sum(GroupHeaderBand1,ds.BcGrzhZfJe)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text16>
            <Text17 Ref="25" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>10.4,0,4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>053f44c433474d908ea2f10cd0e050ea</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text17</Name>
              <Page isRef="3" />
              <Parent isRef="23" />
              <Text>{Sum(GroupHeaderBand1,ds.BcTcZfJe)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text17>
            <Text18 Ref="26" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>14.4,0,4.2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>6dc2af1060e04434becaca8f968799d7</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text18</Name>
              <Page isRef="3" />
              <Parent isRef="23" />
              <Text>{Sum(GroupHeaderBand1,ds.BcXjZfJe)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text18>
            <Text19 Ref="27" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,6.4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>6305f73f34f84cb9bd0659d81791004d</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text19</Name>
              <Page isRef="3" />
              <Parent isRef="23" />
              <Text>合计：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text19>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>GroupFooterBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </GroupFooterBand1>
        <FooterBand1 Ref="28" type="FooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,7,18.6,0.5</ClientRectangle>
          <Components isList="true" count="4">
            <Text11 Ref="29" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6.4,0,4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>33c9d40b49e14125b36c311b13408f10</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text11</Name>
              <Page isRef="3" />
              <Parent isRef="28" />
              <Text>{Sum(DataBand1,ds.BcGrzhZfJe)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text11>
            <Text12 Ref="30" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>10.4,0,4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>f977bc3bf75a4cb3910d959863b6543e</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text12</Name>
              <Page isRef="3" />
              <Parent isRef="28" />
              <Text>{Sum(GroupHeaderBand1,ds.BcTcZfJe)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text12>
            <Text13 Ref="31" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>14.4,0,4.2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>29c6f3e0e1a9439a9742c7b8b74fb68a</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text13</Name>
              <Page isRef="3" />
              <Parent isRef="28" />
              <Text>{Sum(GroupHeaderBand1,ds.BcXjZfJe)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text13>
            <Text14 Ref="32" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,6.4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,0</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>1d8b22827df44d71a669cfc3a6fe3747</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text14</Name>
              <Page isRef="3" />
              <Parent isRef="28" />
              <Text>合计：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text14>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>FooterBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </FooterBand1>
        <ReportSummaryBand1 Ref="33" type="ReportSummaryBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,8.3,18.6,1.3</ClientRectangle>
          <Components isList="true" count="3">
            <Text21 Ref="34" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0.8,0.1,16,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8.25,Bold,Point,False,0</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text21</Name>
              <Page isRef="3" />
              <Parent isRef="33" />
              <Text>注：共报销 {报销笔数} 笔，回退 {回退笔数} 笔，实际报销 {实际笔数} 笔，其中His日结后报销 {日结后笔数} 笔
</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text21>
            <Text20 Ref="35" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0.8,0.5,16,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8.25,Bold,Point,False,0</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text20</Name>
              <Page isRef="3" />
              <Parent isRef="33" />
              <Text>       共报销金额 {报销金额} 元，回退金额 {回退金额} 元，实际报销金额 {实际金额} 元，</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text20>
            <Text22 Ref="36" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0.8,0.9,16,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8.25,Bold,Point,False,0</Font>
              <Guid>19155602f7164ca5a0942084b0ef914a</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text22</Name>
              <Page isRef="3" />
              <Parent isRef="33" />
              <Text>        His日结后报销金额 {日结后报销金额} 元，个人账户金额 {日结后个人账户支付金额} 元，现金 {日结后现金} 元，</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text22>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>ReportSummaryBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </ReportSummaryBand1>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>2a1844cd22fa45a493e6260cccb64b3b</Guid>
      <Margins>1.3,1.1,1,1</Margins>
      <Name>Page1</Name>
      <PageHeight>29.7</PageHeight>
      <PageWidth>21</PageWidth>
      <Report isRef="0" />
      <Watermark Ref="37" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page1>
  </Pages>
  <PrinterSettings Ref="38" type="Stimulsoft.Report.Print.StiPrinterSettings" isKey="true" />
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>医保日结</ReportAlias>
  <ReportChanged>3/10/2017 2:10:34 PM</ReportChanged>
  <ReportCreated>12/22/2011 4:28:53 PM</ReportCreated>
  <ReportFile>F:\最新程序\his2010v3_20170310测试\his2010v3_20170310\his2010v3_20170308\his2010\Rpt\医保日结.mrt</ReportFile>
  <ReportGuid>f839a427744740e988e6f97c1673ca51</ReportGuid>
  <ReportName>医保日结</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2011.2.1026</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class Report : Stimulsoft.Report.StiReport
    {
        public Report()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
        #endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>