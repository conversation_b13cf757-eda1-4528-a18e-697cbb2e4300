﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class EmrAddBl
    Inherits HisControl.BaseForm

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(EmrAddBl))
        Me.C1CommandDock1 = New C1.Win.C1Command.C1CommandDock()
        Me.C1DockingTab1 = New C1.Win.C1Command.C1DockingTab()
        Me.C1DockingTabPage1 = New C1.Win.C1Command.C1DockingTabPage()
        Me.TableLayoutPanel2 = New System.Windows.Forms.TableLayoutPanel()
        Me.TreeView1 = New System.Windows.Forms.TreeView()
        Me.Image1 = New System.Windows.Forms.ImageList(Me.components)
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.T_Label = New C1.Win.C1Input.C1Label()
        Me.cxButton = New CustomControl.MyButton()
        Me.GroupBox1 = New System.Windows.Forms.GroupBox()
        Me.BCheckBox = New System.Windows.Forms.CheckBox()
        Me.GCheckBox = New System.Windows.Forms.CheckBox()
        Me.TyCheckBox = New System.Windows.Forms.CheckBox()
        Me.MbcxTextBox = New CustomControl.MyTextBox()
        Me.MyGrid1 = New CustomControl.MyGrid()
        Me.Panel2 = New System.Windows.Forms.Panel()
        Me.MyButton2 = New CustomControl.MyButton()
        Me.MyButton1 = New CustomControl.MyButton()
        CType(Me.C1CommandDock1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.C1CommandDock1.SuspendLayout()
        CType(Me.C1DockingTab1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.C1DockingTab1.SuspendLayout()
        Me.C1DockingTabPage1.SuspendLayout()
        Me.TableLayoutPanel2.SuspendLayout()
        Me.TableLayoutPanel1.SuspendLayout()
        Me.Panel1.SuspendLayout()
        CType(Me.T_Label, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox1.SuspendLayout()
        Me.Panel2.SuspendLayout()
        Me.SuspendLayout()
        '
        'C1CommandDock1
        '
        Me.C1CommandDock1.Controls.Add(Me.C1DockingTab1)
        Me.C1CommandDock1.Dock = System.Windows.Forms.DockStyle.Left
        Me.C1CommandDock1.Id = 2
        Me.C1CommandDock1.Location = New System.Drawing.Point(0, 0)
        Me.C1CommandDock1.Name = "C1CommandDock1"
        Me.C1CommandDock1.Size = New System.Drawing.Size(297, 639)
        '
        'C1DockingTab1
        '
        Me.C1DockingTab1.Alignment = System.Windows.Forms.TabAlignment.Bottom
        Me.C1DockingTab1.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1DockingTab1.CanAutoHide = True
        Me.C1DockingTab1.CanCloseTabs = True
        Me.C1DockingTab1.CanMoveTabs = True
        Me.C1DockingTab1.Controls.Add(Me.C1DockingTabPage1)
        Me.C1DockingTab1.Location = New System.Drawing.Point(0, 0)
        Me.C1DockingTab1.Name = "C1DockingTab1"
        Me.C1DockingTab1.ShowCaption = True
        Me.C1DockingTab1.ShowSingleTab = False
        Me.C1DockingTab1.Size = New System.Drawing.Size(297, 639)
        Me.C1DockingTab1.TabIndex = 0
        Me.C1DockingTab1.TabSizeMode = C1.Win.C1Command.TabSizeModeEnum.Fit
        Me.C1DockingTab1.TabsSpacing = 0
        '
        'C1DockingTabPage1
        '
        Me.C1DockingTabPage1.CaptionVisible = True
        Me.C1DockingTabPage1.Controls.Add(Me.TableLayoutPanel2)
        Me.C1DockingTabPage1.Location = New System.Drawing.Point(0, 0)
        Me.C1DockingTabPage1.Name = "C1DockingTabPage1"
        Me.C1DockingTabPage1.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.C1DockingTabPage1.Size = New System.Drawing.Size(294, 638)
        Me.C1DockingTabPage1.TabIndex = 0
        Me.C1DockingTabPage1.Text = "第1页"
        '
        'TableLayoutPanel2
        '
        Me.TableLayoutPanel2.ColumnCount = 1
        Me.TableLayoutPanel2.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanel2.Controls.Add(Me.TreeView1, 0, 0)
        Me.TableLayoutPanel2.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TableLayoutPanel2.Location = New System.Drawing.Point(0, 19)
        Me.TableLayoutPanel2.Name = "TableLayoutPanel2"
        Me.TableLayoutPanel2.RowCount = 1
        Me.TableLayoutPanel2.RowStyles.Add(New System.Windows.Forms.RowStyle())
        Me.TableLayoutPanel2.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 20.0!))
        Me.TableLayoutPanel2.Size = New System.Drawing.Size(294, 616)
        Me.TableLayoutPanel2.TabIndex = 2
        '
        'TreeView1
        '
        Me.TreeView1.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom), System.Windows.Forms.AnchorStyles)
        Me.TreeView1.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.TreeView1.ImageIndex = 0
        Me.TreeView1.ImageList = Me.Image1
        Me.TreeView1.Location = New System.Drawing.Point(3, 3)
        Me.TreeView1.Name = "TreeView1"
        Me.TreeView1.SelectedImageIndex = 0
        Me.TreeView1.Size = New System.Drawing.Size(288, 610)
        Me.TreeView1.TabIndex = 1
        '
        'Image1
        '
        Me.Image1.ImageStream = CType(resources.GetObject("Image1.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.Image1.TransparentColor = System.Drawing.Color.White
        Me.Image1.Images.SetKeyName(0, "")
        Me.Image1.Images.SetKeyName(1, "")
        Me.Image1.Images.SetKeyName(2, "")
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.ColumnCount = 1
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanel1.Controls.Add(Me.Panel1, 0, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.MyGrid1, 0, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.Panel2, 0, 2)
        Me.TableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(297, 0)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.RowCount = 3
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 100.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 40.0!))
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(1120, 639)
        Me.TableLayoutPanel1.TabIndex = 1
        '
        'Panel1
        '
        Me.Panel1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Panel1.Controls.Add(Me.T_Label)
        Me.Panel1.Controls.Add(Me.cxButton)
        Me.Panel1.Controls.Add(Me.GroupBox1)
        Me.Panel1.Controls.Add(Me.MbcxTextBox)
        Me.Panel1.Location = New System.Drawing.Point(3, 3)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(1114, 94)
        Me.Panel1.TabIndex = 0
        '
        'T_Label
        '
        Me.T_Label.AutoSize = True
        Me.T_Label.BackColor = System.Drawing.Color.Transparent
        Me.T_Label.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.T_Label.ForeColor = System.Drawing.Color.Black
        Me.T_Label.Location = New System.Drawing.Point(388, 58)
        Me.T_Label.Name = "T_Label"
        Me.T_Label.Size = New System.Drawing.Size(47, 12)
        Me.T_Label.TabIndex = 3
        Me.T_Label.Tag = Nothing
        Me.T_Label.Text = "T_Label"
        Me.T_Label.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.T_Label.TextDetached = True
        Me.T_Label.TrimStart = True
        Me.T_Label.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'cxButton
        '
        Me.cxButton.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.cxButton.DialogResult = System.Windows.Forms.DialogResult.None
        Me.cxButton.Location = New System.Drawing.Point(397, 7)
        Me.cxButton.Name = "cxButton"
        Me.cxButton.Size = New System.Drawing.Size(80, 27)
        Me.cxButton.TabIndex = 2
        Me.cxButton.Text = "查询"
        '
        'GroupBox1
        '
        Me.GroupBox1.Controls.Add(Me.BCheckBox)
        Me.GroupBox1.Controls.Add(Me.GCheckBox)
        Me.GroupBox1.Controls.Add(Me.TyCheckBox)
        Me.GroupBox1.Location = New System.Drawing.Point(16, 36)
        Me.GroupBox1.Name = "GroupBox1"
        Me.GroupBox1.Size = New System.Drawing.Size(348, 55)
        Me.GroupBox1.TabIndex = 1
        Me.GroupBox1.TabStop = False
        Me.GroupBox1.Text = "模版类别"
        '
        'BCheckBox
        '
        Me.BCheckBox.AutoSize = True
        Me.BCheckBox.Checked = True
        Me.BCheckBox.CheckState = System.Windows.Forms.CheckState.Checked
        Me.BCheckBox.Location = New System.Drawing.Point(243, 26)
        Me.BCheckBox.Name = "BCheckBox"
        Me.BCheckBox.Size = New System.Drawing.Size(72, 16)
        Me.BCheckBox.TabIndex = 2
        Me.BCheckBox.Text = "男性模版"
        Me.BCheckBox.UseVisualStyleBackColor = True
        '
        'GCheckBox
        '
        Me.GCheckBox.Checked = True
        Me.GCheckBox.CheckState = System.Windows.Forms.CheckState.Checked
        Me.GCheckBox.Location = New System.Drawing.Point(130, 22)
        Me.GCheckBox.Name = "GCheckBox"
        Me.GCheckBox.Size = New System.Drawing.Size(104, 24)
        Me.GCheckBox.TabIndex = 1
        Me.GCheckBox.Text = "女性模版"
        Me.GCheckBox.UseVisualStyleBackColor = True
        '
        'TyCheckBox
        '
        Me.TyCheckBox.AutoSize = True
        Me.TyCheckBox.Checked = True
        Me.TyCheckBox.CheckState = System.Windows.Forms.CheckState.Checked
        Me.TyCheckBox.Location = New System.Drawing.Point(17, 27)
        Me.TyCheckBox.Name = "TyCheckBox"
        Me.TyCheckBox.Size = New System.Drawing.Size(72, 16)
        Me.TyCheckBox.TabIndex = 0
        Me.TyCheckBox.Text = "通用模版"
        Me.TyCheckBox.UseVisualStyleBackColor = True
        '
        'MbcxTextBox
        '
        Me.MbcxTextBox.Captain = "模版名称"
        Me.MbcxTextBox.CaptainBackColor = System.Drawing.Color.Transparent
        Me.MbcxTextBox.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MbcxTextBox.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.MbcxTextBox.CaptainWidth = 60.0!
        Me.MbcxTextBox.ContentForeColor = System.Drawing.Color.Black
        Me.MbcxTextBox.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.MbcxTextBox.Location = New System.Drawing.Point(16, 9)
        Me.MbcxTextBox.Multiline = False
        Me.MbcxTextBox.Name = "MbcxTextBox"
        Me.MbcxTextBox.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.MbcxTextBox.ReadOnly = False
        Me.MbcxTextBox.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.MbcxTextBox.SelectionStart = 0
        Me.MbcxTextBox.SelectStart = 0
        Me.MbcxTextBox.Size = New System.Drawing.Size(348, 20)
        Me.MbcxTextBox.TabIndex = 0
        Me.MbcxTextBox.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MbcxTextBox.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        '
        'MyGrid1
        '
        Me.MyGrid1.CanCustomCol = False
        Me.MyGrid1.Col = 0
        Me.MyGrid1.ColumnFooters = False
        Me.MyGrid1.DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveRight
        Me.MyGrid1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.MyGrid1.FetchRowStyles = False
        Me.MyGrid1.Location = New System.Drawing.Point(0, 100)
        Me.MyGrid1.Margin = New System.Windows.Forms.Padding(0)
        Me.MyGrid1.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.MyGrid1.Name = "MyGrid1"
        Me.MyGrid1.Size = New System.Drawing.Size(1120, 499)
        Me.MyGrid1.TabAction = C1.Win.C1TrueDBGrid.TabActionEnum.ControlNavigation
        Me.MyGrid1.TabIndex = 1
        Me.MyGrid1.Xmlpath = Nothing
        '
        'Panel2
        '
        Me.Panel2.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Panel2.Controls.Add(Me.MyButton2)
        Me.Panel2.Controls.Add(Me.MyButton1)
        Me.Panel2.Location = New System.Drawing.Point(3, 602)
        Me.Panel2.Name = "Panel2"
        Me.Panel2.Size = New System.Drawing.Size(1114, 34)
        Me.Panel2.TabIndex = 2
        '
        'MyButton2
        '
        Me.MyButton2.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.MyButton2.DialogResult = System.Windows.Forms.DialogResult.None
        Me.MyButton2.Location = New System.Drawing.Point(532, 4)
        Me.MyButton2.Name = "MyButton2"
        Me.MyButton2.Size = New System.Drawing.Size(70, 27)
        Me.MyButton2.TabIndex = 1
        Me.MyButton2.Text = "取消"
        '
        'MyButton1
        '
        Me.MyButton1.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.MyButton1.DialogResult = System.Windows.Forms.DialogResult.None
        Me.MyButton1.Location = New System.Drawing.Point(422, 3)
        Me.MyButton1.Name = "MyButton1"
        Me.MyButton1.Size = New System.Drawing.Size(72, 27)
        Me.MyButton1.TabIndex = 0
        Me.MyButton1.Text = "确定"
        '
        'EmrAddBl
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1417, 639)
        Me.Controls.Add(Me.TableLayoutPanel1)
        Me.Controls.Add(Me.C1CommandDock1)
        Me.Name = "EmrAddBl"
        Me.Text = "添加病例"
        CType(Me.C1CommandDock1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.C1CommandDock1.ResumeLayout(False)
        CType(Me.C1DockingTab1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.C1DockingTab1.ResumeLayout(False)
        Me.C1DockingTabPage1.ResumeLayout(False)
        Me.TableLayoutPanel2.ResumeLayout(False)
        Me.TableLayoutPanel1.ResumeLayout(False)
        Me.Panel1.ResumeLayout(False)
        Me.Panel1.PerformLayout()
        CType(Me.T_Label, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox1.ResumeLayout(False)
        Me.GroupBox1.PerformLayout()
        Me.Panel2.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents C1CommandDock1 As C1.Win.C1Command.C1CommandDock
    Friend WithEvents C1DockingTab1 As C1.Win.C1Command.C1DockingTab
    Friend WithEvents C1DockingTabPage1 As C1.Win.C1Command.C1DockingTabPage
    Friend WithEvents TableLayoutPanel1 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents cxButton As CustomControl.MyButton
    Friend WithEvents GroupBox1 As System.Windows.Forms.GroupBox
    Friend WithEvents BCheckBox As System.Windows.Forms.CheckBox
    Friend WithEvents GCheckBox As System.Windows.Forms.CheckBox
    Friend WithEvents TyCheckBox As System.Windows.Forms.CheckBox
    Friend WithEvents MbcxTextBox As CustomControl.MyTextBox
    Friend WithEvents MyGrid1 As CustomControl.MyGrid
    Friend WithEvents Panel2 As System.Windows.Forms.Panel
    Friend WithEvents MyButton2 As CustomControl.MyButton
    Friend WithEvents MyButton1 As CustomControl.MyButton
    Friend WithEvents TreeView1 As System.Windows.Forms.TreeView
    Friend WithEvents T_Label As C1.Win.C1Input.C1Label
    Friend WithEvents TableLayoutPanel2 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents Image1 As System.Windows.Forms.ImageList
End Class
