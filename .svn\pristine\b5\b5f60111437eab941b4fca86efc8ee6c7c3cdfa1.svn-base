﻿Imports System.Diagnostics
Imports System.Windows.Forms
Imports System.Drawing

Public Class HisVar
    Private Shared m_YpNetCgservice As New YpNetCg.WebService
    ''' <summary>
    '''健康卡Service
    ''' </summary>
    Public Shared Property YpNetCgservice() As YpNetCg.WebService
        Get
            Return m_YpNetCgservice
        End Get
        Set(ByVal value As YpNetCg.WebService)
            m_YpNetCgservice = value
        End Set
    End Property

'    Private Shared m_Jkkservice As New JkkService.WebService1
'    ''' <summary>
'    '''健康卡Service
'    ''' </summary>
'    Public Shared Property Jkkservice() As JkkService.WebService1
'        Get
'            Return m_Jkkservice
'        End Get
'        Set(ByVal value As JkkService.WebService1)
'            m_Jkkservice = value
'        End Set
'    End Property
'
'    Private Shared m_HisDBservice As New CenterBase.DotNetService
'    ''' <summary>
'    '''中心库Service
'    ''' </summary>
'    Public Shared Property HisDBservice() As CenterBase.DotNetService
'        Get
'            Return m_HisDBservice
'        End Get
'        Set(ByVal value As CenterBase.DotNetService)
'            m_HisDBservice = value
'        End Set
'    End Property

'    Private Shared m_MztcXyDBservice As New MztcXyDb.OracleService
'    ''' <summary>
'    '''Orlc乡医调拨Service
'    ''' </summary>
'    Public Shared Property MztcXyDBservice() As MztcXyDb.OracleService
'        Get
'            Return m_MztcXyDBservice
'        End Get
'        Set(ByVal value As MztcXyDb.OracleService)
'            m_MztcXyDBservice = value
'        End Set
'    End Property

    Private Shared m_DzblPrintservice As New DzblFileSend.DzblFileSend
    ''' <summary>
    '''电子病历打印Service
    ''' </summary>
    Public Shared Property DzblPrintservice() As DzblFileSend.DzblFileSend
        Get
            Return m_DzblPrintservice
        End Get
        Set(ByVal value As DzblFileSend.DzblFileSend)
            m_DzblPrintservice = value
        End Set
    End Property


    Private Shared m_YyOledb As New oledbDalHelper.dzhOleDbDal
    ''' <summary>
    '''登陆医院Access
    ''' </summary>
    Public Shared Property YyOledb() As oledbDalHelper.dzhOleDbDal
        Get
            Return m_YyOledb
        End Get
        Set(ByVal value As oledbDalHelper.dzhOleDbDal)
            m_YyOledb = value
        End Set
    End Property


'    Private Shared m_HdDy_Dal As New SqlDal.dzhSqlDal
'    ''' <summary>
'    '''邯郸对应库
'    ''' </summary>
'    Public Shared Property HdDy_Dal() As SqlDal.dzhSqlDal
'        Get
'            Return m_HdDy_Dal
'        End Get
'        Set(ByVal value As SqlDal.dzhSqlDal)
'            m_HdDy_Dal = value
'        End Set
'    End Property

'    Private Shared m_ZtXyOraclDal As New DDTekOracleDalHelper.DDTekOracleDal
'    ''' <summary>
'    '''智通农合乡医
'    ''' </summary>
'    Public Shared Property ZtXyOraclDal() As DDTekOracleDalHelper.DDTekOracleDal
'        Get
'            Return m_ZtXyOraclDal
'        End Get
'        Set(ByVal value As DDTekOracleDalHelper.DDTekOracleDal)
'            m_ZtXyOraclDal = value
'        End Set
'    End Property

'    Private Shared m_ZtBxOraclDal As New DDTekOracleDalHelper.DDTekOracleDal
'    ''' <summary>
'    '''智通农合报销
'    ''' </summary>
'    Public Shared Property ZtBxOraclDal() As DDTekOracleDalHelper.DDTekOracleDal
'        Get
'            Return m_ZtBxOraclDal
'        End Get
'        Set(ByVal value As DDTekOracleDalHelper.DDTekOracleDal)
'            m_ZtBxOraclDal = value
'        End Set
'    End Property

'    Private Shared m_ZtBxDyOraclDal As New DDTekOracleDalHelper.DDTekOracleDal
'    ''' <summary>
'    '''智通农合对应
'    ''' </summary>
'    Public Shared Property ZtBxDyOraclDal() As DDTekOracleDalHelper.DDTekOracleDal
'        Get
'            Return m_ZtBxDyOraclDal
'        End Get
'        Set(ByVal value As DDTekOracleDalHelper.DDTekOracleDal)
'            m_ZtBxDyOraclDal = value
'        End Set
'    End Property

'    Private Shared m_DongRuanZgOraclDal As New DDTekOracleDalHelper.DDTekOracleDal
'    ''' <summary>
'    '''东软城镇职工医保医院二次录入库
'    ''' </summary>
'    Public Shared Property DongRuanZgOraclDal() As DDTekOracleDalHelper.DDTekOracleDal
'        Get
'            Return m_DongRuanZgOraclDal
'        End Get
'        Set(ByVal value As DDTekOracleDalHelper.DDTekOracleDal)
'            m_DongRuanZgOraclDal = value
'        End Set
'    End Property
'
'    Private Shared m_DongRuanJmOraclDal As New DDTekOracleDalHelper.DDTekOracleDal
'    ''' <summary>
'    '''东软城镇居民医保医院二次录入库
'    ''' </summary>
'    Public Shared Property DongRuanJmOraclDal() As DDTekOracleDalHelper.DDTekOracleDal
'        Get
'            Return m_DongRuanJmOraclDal
'        End Get
'        Set(ByVal value As DDTekOracleDalHelper.DDTekOracleDal)
'            m_DongRuanJmOraclDal = value
'        End Set
'    End Property

    Private Shared m_Sqldal As New SqlDal.dzhSqlDal
    ''' <summary>
    '''本地连接
    ''' </summary>
    Public Shared Property Sqldal() As SqlDal.dzhSqlDal
        Get
            Return m_Sqldal
        End Get
        Set(ByVal value As SqlDal.dzhSqlDal)
            m_Sqldal = value
        End Set
    End Property

    Private Shared m_WsyCode As String
    ''' <summary>
    '''卫生院编码
    ''' </summary>
    Public Shared Property WsyCode() As String
        Get
            Return m_WsyCode
        End Get
        Set(ByVal value As String)
            m_WsyCode = value
        End Set
    End Property

    Private Shared m_WsyName As String
    ''' <summary>
    '''卫生院名称
    ''' </summary>
    Public Shared Property WsyName() As String
        Get
            Return m_WsyName
        End Get
        Set(ByVal value As String)
            m_WsyName = value
        End Set
    End Property

    Private Shared m_JsrCode As String
    ''' <summary>
    '''登录人编码
    ''' </summary>
    Public Shared Property JsrCode() As String
        Get
            Return m_JsrCode
        End Get
        Set(ByVal value As String)
            m_JsrCode = value
        End Set
    End Property

    Private Shared m_JsrColor As Color
    ''' <summary>
    '''登录人颜色
    ''' </summary>
    Public Shared Property JsrColor() As Color
        Get
            Return m_JsrColor
        End Get
        Set(ByVal value As Color)
            m_JsrColor = value
        End Set
    End Property

    Private Shared m_JsrName As String
    ''' <summary>
    '''登录人姓名
    ''' </summary>
    Public Shared Property JsrName() As String
        Get
            Return m_JsrName
        End Get
        Set(ByVal value As String)
            m_JsrName = value
        End Set
    End Property


    Private Shared m_JsrYsCode As String
    ''' <summary>
    '''登录人关联医生编码
    ''' </summary>
    Public Shared Property JsrYsCode() As String
        Get
            Return m_JsrYsCode
        End Get
        Set(ByVal value As String)
            m_JsrYsCode = value
        End Set
    End Property

    Private Shared m_JsrYsName As String
    ''' <summary>
    '''登录人关联医生姓名
    ''' </summary>
    Public Shared Property JsrYsName() As String
        Get
            Return m_JsrYsName
        End Get
        Set(ByVal value As String)
            m_JsrYsName = value
        End Set
    End Property

    Private Shared m_YfCode As String
    ''' <summary>
    '''登录人所属药房编码
    ''' </summary>
    Public Shared Property YfCode() As String
        Get
            Return m_YfCode
        End Get
        Set(ByVal value As String)
            m_YfCode = value
        End Set
    End Property

    Private Shared m_YfName As String
    ''' <summary>
    '''登录人所属药房名称
    ''' </summary>
    Public Shared Property YfName() As String
        Get
            Return m_YfName
        End Get
        Set(ByVal value As String)
            m_YfName = value
        End Set
    End Property

    Private Shared m_GlzCode As String
    ''' <summary>
    '''登录人所属权限组
    ''' </summary>
    Public Shared Property GlzCode() As String
        Get
            Return m_GlzCode
        End Get
        Set(ByVal value As String)
            m_GlzCode = value
        End Set
    End Property

    Private Shared m_InputCode As InputLanguage
    ''' <summary>
    '''默认中文输入法
    ''' </summary>
    Public Shared Property InputCode() As InputLanguage
        Get
            Return m_InputCode
        End Get
        Set(ByVal value As InputLanguage)
            m_InputCode = value
        End Set
    End Property

    Private Shared m_XmKs As String
    ''' <summary>
    '''登录人所属科室
    ''' </summary>
    Public Shared Property XmKs() As String
        Get
            Return m_XmKs
        End Get
        Set(ByVal value As String)
            m_XmKs = value
        End Set
    End Property

'    Private Shared m_Ks_Name As String
'    ''' <summary>
'    '''登录人所属科室名称
'    ''' </summary>
'    Public Shared Property Ks_Name() As String
'        Get
'            Return m_Ks_Name
'        End Get
'        Set(ByVal value As String)
'            m_Ks_Name = value
'        End Set
'    End Property

    Private Shared m_F_Main As Form
    ''' <summary>
    '''主窗体
    ''' </summary>
    Public Shared Property FMain() As Form
        Get
            Return m_F_Main
        End Get
        Set(ByVal value As Form)
            m_F_Main = value
        End Set
    End Property

    Private Shared m_DockTab As C1.Win.C1Command.C1DockingTab
    ''' <summary>
    '''主DockTab
    ''' </summary>
    Public Shared Property DockTab() As C1.Win.C1Command.C1DockingTab
        Get
            Return m_DockTab
        End Get
        Set(ByVal value As C1.Win.C1Command.C1DockingTab)
            m_DockTab = value
        End Set
    End Property
    Private Shared m_parapath As String
    ''' <summary>
    '''系统参数文件路径
    ''' </summary>
    Public Shared Property Parapath() As String
        Get
            Return m_parapath
        End Get
        Set(ByVal value As String)
            m_parapath = value
        End Set
    End Property

'    Private Shared m_IsYbzl As String
'    ''' <summary>
'    '''系统参数文件路径
'    ''' </summary>
'    Public Shared Property IsYbzl() As String
'        Get
'            Return m_IsYbzl
'        End Get
'        Set(ByVal value As String)
'            m_IsYbzl = value
'        End Set
'    End Property

'    Private Shared m_IsYbYdjy As String
'    ''' <summary>
'    '''是否启用 医保异地就医
'    ''' </summary>
'    Public Shared Property IsYbYdjy() As String
'        Get
'            Return m_IsYbYdjy
'        End Get
'        Set(ByVal value As String)
'            m_IsYbYdjy = value
'        End Set
'    End Property

    Private Shared m_ConfPath As String
    ''' <summary>
    '''系统参数文件路径
    ''' </summary>
    Public Shared Property ConfPath() As String
        Get
            Return m_ConfPath
        End Get
        Set(ByVal value As String)
            m_ConfPath = value
        End Set
    End Property

    Private Shared tmp_Sqllite As New SqlLiteHelper.dzhSqlliteDal()
    Public Shared Property Sqllite() As SqlLiteHelper.dzhSqlliteDal
        Get
            Return tmp_Sqllite
        End Get
        Set(value As SqlLiteHelper.dzhSqlliteDal)
            tmp_Sqllite = value
        End Set
    End Property

    Private Shared tmp_SqlliteCon As New SqlLiteHelper.dzhSqlliteDal()
    Public Shared Property SqlliteCon() As SqlLiteHelper.dzhSqlliteDal
        Get
            Return tmp_SqlliteCon
        End Get
        Set(value As SqlLiteHelper.dzhSqlliteDal)
            tmp_SqlliteCon = value
        End Set
    End Property



End Class
