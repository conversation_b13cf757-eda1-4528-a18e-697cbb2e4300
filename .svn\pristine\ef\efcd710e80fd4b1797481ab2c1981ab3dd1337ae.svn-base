﻿/**  版本信息模板在安装目录下，可自行修改。
* D_Materials_Check2.cs
*
* 功 能： N/A
* 类 名： D_Materials_Check2
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016-11-26 11:27:50   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;

namespace DAL
{
	/// <summary>
	/// 数据访问类:D_Materials_Check2
	/// </summary>
	public partial class D_Materials_Check2
	{
		public D_Materials_Check2()
		{}
		#region  BasicMethod
        public string MaxCode(string checkCode)
        {
            string max = checkCode + (string)(HisVar.HisVar.Sqldal.F_MaxCode("select right(max(M_Check_Detail_Code),4) from Materials_Check2 where left(M_Check_Detail_Code,11)='" + checkCode + "'", 4));
            return max;
        }
		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string M_Check_Detail_Code)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from Materials_Check2");
			strSql.Append(" where M_Check_Detail_Code=@M_Check_Detail_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@M_Check_Detail_Code", SqlDbType.Char,15)			};
			parameters[0].Value = M_Check_Detail_Code;

			return HisVar.HisVar.Sqldal.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ModelOld.M_Materials_Check2 model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into Materials_Check2(");
			strSql.Append("M_Check_Code,Materials_Code,MaterialsStock_Code,M_Check_Detail_Code,MaterialsLot,MaterialsExpiryDate,M_Paper_Num,M_Real_Num,M_Check_Num,M_Check_Price,M_Check_Money,M_CheckDetail_Memo)");
			strSql.Append(" values (");
			strSql.Append("@M_Check_Code,@Materials_Code,@MaterialsStock_Code,@M_Check_Detail_Code,@MaterialsLot,@MaterialsExpiryDate,@M_Paper_Num,@M_Real_Num,@M_Check_Num,@M_Check_Price,@M_Check_Money,@M_CheckDetail_Memo)");
			SqlParameter[] parameters = {
					new SqlParameter("@M_Check_Code", SqlDbType.Char,11),
					new SqlParameter("@Materials_Code", SqlDbType.Char,10),
					new SqlParameter("@MaterialsStock_Code", SqlDbType.Char,16),
					new SqlParameter("@M_Check_Detail_Code", SqlDbType.Char,15),
					new SqlParameter("@MaterialsLot", SqlDbType.VarChar,50),
					new SqlParameter("@MaterialsExpiryDate", SqlDbType.SmallDateTime),
					new SqlParameter("@M_Paper_Num", SqlDbType.Decimal),
					new SqlParameter("@M_Real_Num", SqlDbType.Decimal),
					new SqlParameter("@M_Check_Num", SqlDbType.Decimal),
					new SqlParameter("@M_Check_Price", SqlDbType.Decimal),
					new SqlParameter("@M_Check_Money", SqlDbType.Decimal),
					new SqlParameter("@M_CheckDetail_Memo", SqlDbType.VarChar,200),
                         new SqlParameter("@Materials_Spec", SqlDbType.VarChar,50)};
			parameters[0].Value = model.M_Check_Code;
			parameters[1].Value = model.Materials_Code;
			parameters[2].Value = model.MaterialsStock_Code;
			parameters[3].Value = model.M_Check_Detail_Code;
			parameters[4].Value = model.MaterialsLot;
			parameters[5].Value = model.MaterialsExpiryDate;
			parameters[6].Value = model.M_Paper_Num;
			parameters[7].Value = model.M_Real_Num;
			parameters[8].Value = model.M_Check_Num;
			parameters[9].Value = model.M_Check_Price;
			parameters[10].Value = model.M_Check_Money;
			parameters[11].Value = model.M_CheckDetail_Memo;
            parameters[12].Value = model.Materials_Spec;
			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ModelOld.M_Materials_Check2 model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update Materials_Check2 set ");
			strSql.Append("M_Check_Code=@M_Check_Code,");
			strSql.Append("Materials_Code=@Materials_Code,");
			strSql.Append("MaterialsStock_Code=@MaterialsStock_Code,");
			strSql.Append("MaterialsLot=@MaterialsLot,");
			strSql.Append("MaterialsExpiryDate=@MaterialsExpiryDate,");
			strSql.Append("M_Paper_Num=@M_Paper_Num,");
			strSql.Append("M_Real_Num=@M_Real_Num,");
			strSql.Append("M_Check_Num=@M_Check_Num,");
			strSql.Append("M_Check_Price=@M_Check_Price,");
			strSql.Append("M_Check_Money=@M_Check_Money,");
			strSql.Append("M_CheckDetail_Memo=@M_CheckDetail_Memo,");
            strSql.Append("Materials_Spec=@Materials_Spec");
			strSql.Append(" where M_Check_Detail_Code=@M_Check_Detail_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@M_Check_Code", SqlDbType.Char,11),
					new SqlParameter("@Materials_Code", SqlDbType.Char,10),
					new SqlParameter("@MaterialsStock_Code", SqlDbType.Char,16),
					new SqlParameter("@MaterialsLot", SqlDbType.VarChar,50),
					new SqlParameter("@MaterialsExpiryDate", SqlDbType.SmallDateTime),
					new SqlParameter("@M_Paper_Num", SqlDbType.Decimal,5),
					new SqlParameter("@M_Real_Num", SqlDbType.Decimal,5),
					new SqlParameter("@M_Check_Num", SqlDbType.Decimal,5),
					new SqlParameter("@M_Check_Price", SqlDbType.Decimal,5),
					new SqlParameter("@M_Check_Money", SqlDbType.Decimal,5),
					new SqlParameter("@M_CheckDetail_Memo", SqlDbType.VarChar,200),
                    new SqlParameter("@Materials_Spec", SqlDbType.VarChar,50),
					new SqlParameter("@M_Check_Detail_Code", SqlDbType.Char,15)};
			parameters[0].Value = model.M_Check_Code;
			parameters[1].Value = model.Materials_Code;
			parameters[2].Value = model.MaterialsStock_Code;
			parameters[3].Value = model.MaterialsLot;
			parameters[4].Value = model.MaterialsExpiryDate;
			parameters[5].Value = model.M_Paper_Num;
			parameters[6].Value = model.M_Real_Num;
			parameters[7].Value = model.M_Check_Num;
			parameters[8].Value = model.M_Check_Price;
			parameters[9].Value = model.M_Check_Money;
			parameters[10].Value = model.M_CheckDetail_Memo;
            parameters[11].Value = model.Materials_Spec ;
			parameters[12].Value = model.M_Check_Detail_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string M_Check_Detail_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Materials_Check2 ");
			strSql.Append(" where M_Check_Detail_Code=@M_Check_Detail_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@M_Check_Detail_Code", SqlDbType.Char,15)			};
			parameters[0].Value = M_Check_Detail_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string M_Check_Detail_Codelist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Materials_Check2 ");
			strSql.Append(" where M_Check_Detail_Code in ("+M_Check_Detail_Codelist + ")  ");
			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Materials_Check2 GetModel(string M_Check_Detail_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
            strSql.Append("select  top 1 M_Check_Code,Materials_Code,MaterialsStock_Code,M_Check_Detail_Code,MaterialsLot,MaterialsExpiryDate,Materials_Spec,M_Paper_Num,M_Real_Num,M_Check_Num,M_Check_Price,M_Check_Money,M_CheckDetail_Memo from Materials_Check2 ");
			strSql.Append(" where M_Check_Detail_Code=@M_Check_Detail_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@M_Check_Detail_Code", SqlDbType.Char,15)			};
			parameters[0].Value = M_Check_Detail_Code;

			ModelOld.M_Materials_Check2 model=new ModelOld.M_Materials_Check2();
			DataSet ds=HisVar.HisVar.Sqldal.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Materials_Check2 DataRowToModel(DataRow row)
		{
			ModelOld.M_Materials_Check2 model=new ModelOld.M_Materials_Check2();
			if (row != null)
			{
				if(row["M_Check_Code"]!=null)
				{
					model.M_Check_Code=row["M_Check_Code"].ToString();
				}
				if(row["Materials_Code"]!=null)
				{
					model.Materials_Code=row["Materials_Code"].ToString();
				}
				if(row["MaterialsStock_Code"]!=null)
				{
					model.MaterialsStock_Code=row["MaterialsStock_Code"].ToString();
				}
				if(row["M_Check_Detail_Code"]!=null)
				{
					model.M_Check_Detail_Code=row["M_Check_Detail_Code"].ToString();
				}
				if(row["MaterialsLot"]!=null)
				{
					model.MaterialsLot=row["MaterialsLot"].ToString();
				}
				if(row["MaterialsExpiryDate"]!=null && row["MaterialsExpiryDate"].ToString()!="")
				{
					model.MaterialsExpiryDate=DateTime.Parse(row["MaterialsExpiryDate"].ToString());
				}
                if (row["Materials_Spec"] != null)
                {
                    model.Materials_Spec = row["Materials_Spec"].ToString();
                }
				if(row["M_Paper_Num"]!=null && row["M_Paper_Num"].ToString()!="")
				{
					model.M_Paper_Num=decimal.Parse(row["M_Paper_Num"].ToString());
				}
				if(row["M_Real_Num"]!=null && row["M_Real_Num"].ToString()!="")
				{
					model.M_Real_Num=decimal.Parse(row["M_Real_Num"].ToString());
				}
				if(row["M_Check_Num"]!=null && row["M_Check_Num"].ToString()!="")
				{
					model.M_Check_Num=decimal.Parse(row["M_Check_Num"].ToString());
				}
				if(row["M_Check_Price"]!=null && row["M_Check_Price"].ToString()!="")
				{
					model.M_Check_Price=decimal.Parse(row["M_Check_Price"].ToString());
				}
				if(row["M_Check_Money"]!=null && row["M_Check_Money"].ToString()!="")
				{
					model.M_Check_Money=decimal.Parse(row["M_Check_Money"].ToString());
				}
				if(row["M_CheckDetail_Memo"]!=null)
				{
					model.M_CheckDetail_Memo=row["M_CheckDetail_Memo"].ToString();
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
            strSql.Append("SELECT M_Check_Code, Materials_Check2.Materials_Code,Materials_Name, MaterialsStock_Code, M_Check_Detail_Code, MaterialsLot, MaterialsExpiryDate,Materials_Check2.Materials_Spec, M_Paper_Num, M_Real_Num, M_Check_Num, M_Check_Price, M_Check_Money, M_CheckDetail_Memo");
            strSql.Append("  FROM dbo.Materials_Check2 JOIN dbo.Materials_Dict ON dbo.Materials_Check2.Materials_Code = dbo.Materials_Dict.Materials_Code");
            
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

        public DataSet GetStockList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(" SELECT Materials_Check2.M_Check_Code, Materials_Check2.Materials_Code,Materials_Name,Materials_Check1.MaterialsWh_Code,MaterialsWh_Name, MaterialsStock_Code, M_Check_Detail_Code, MaterialsLot, MaterialsExpiryDate,Materials_Check2.Materials_Spec,  M_Paper_Num, M_Real_Num, M_Check_Num, M_Check_Price, M_Check_Money, M_CheckDetail_Memo");
            strSql.Append("  FROM dbo.Materials_Check1 JOIN  dbo.Materials_Check2 ON dbo.Materials_Check1.M_Check_Code = dbo.Materials_Check2.M_Check_Code    ");
            strSql.Append("   JOIN dbo.Materials_Dict ON dbo.Materials_Check2.Materials_Code = dbo.Materials_Dict.Materials_Code  ");
            strSql.Append("    JOIN dbo.Materials_Warehouse_Dict ON dbo.Materials_Check1.MaterialsWh_Code=dbo.Materials_Warehouse_Dict.MaterialsWh_Code ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }


		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
            strSql.Append(" M_Check_Code,Materials_Code,MaterialsStock_Code,M_Check_Detail_Code,MaterialsLot,MaterialsExpiryDate,Materials_Check2.Materials_Spec, M_Paper_Num,M_Real_Num,M_Check_Num,M_Check_Price,M_Check_Money,M_CheckDetail_Memo ");
			strSql.Append(" FROM Materials_Check2 ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM Materials_Check2 ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.M_Check_Detail_Code desc");
			}
			strSql.Append(")AS Row, T.*  from Materials_Check2 T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Materials_Check2";
			parameters[1].Value = "M_Check_Detail_Code";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return HisVar.HisVar.Sqldal.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

