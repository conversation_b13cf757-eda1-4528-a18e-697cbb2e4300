﻿Imports System.Data.SqlClient
Imports System.Reflection
Imports Stimulsoft.Report
Imports HisControl
Imports ZTHisOutpatient

Public Class Xs_Mz3

#Region "定义__变量"
    Dim My_Adapter As New SqlDataAdapter                '从表适配器
    Dim My_Dataset As New DataSet
    Dim _bllMz_Yp As BLL.BllMz_Yp = New BLL.BllMz_Yp()
    Dim _mdlmz As New Model.MdlMz
    Dim _bllmz As New BLL.BllMz
#End Region

#Region "传参"
    Dim Rform As BaseForm
    Dim Rrow As DataRow
    Dim Zb_Cm As CurrencyManager             '同步指针

#End Region

    Public Sub New(ByVal tform As BaseForm, ByVal trow As DataRow)

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        Rform = tform
        Rrow = trow
        ' 在 InitializeComponent() 调用之后添加任何初始化。

    End Sub

    Private Sub Xs_Mz3_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        If Rform.Name = "Ys_Cf1" Then
            C1Button1.Visible = False
        End If
        Call Form_Init()
        Call Zb_Show()
        Call Load_Dict()
    End Sub

#Region "窗体__事件"

    Private Sub Form_Init()
        Panel2.Height = 30
        '按扭初始化
        ' Call P_Comm(Me.Comm1)
        C1NumericEdit1.Enabled = False
        C1NumericEdit2.Enabled = False
        C1NumericEdit3.Enabled = False

        Dim My_Grid As New BaseClass.C_Grid(Me.C1TrueDBGrid1)
        With My_Grid
            .Init_Grid()
            .P_Dock(BaseClass.C_Grid.Dock.Fill)
            .Init_Column("名称", "Xm_Name", 160, "左", "")
            .Init_Column("规格", "Mx_Gg", 100, "左", "")
            .Init_Column("产地", "Mx_Cd", 150, "左", "")
            .Init_Column("有效期", "Yp_Yxq", 80, "中", "yyyy-MM-dd")
            .Init_Column("单位", "Xm_Dw", 70, "左", "")
            .Init_Column("数量", "Mz_Sl", 70, "右", "###,###,##0.00##")
            .Init_Column("单价", "Mz_Dj", 70, "右", "###,###,##0.00####")
            .Init_Column("金额", "Mz_Money", 70, "右", "###,###,##0.00##")
            .Init_Column("是否检查", "Xm_Wc", 70, "中", "")
        End With

        C1TrueDBGrid1.Splits(0).DisplayColumns("Xm_Wc").OwnerDraw = True
    End Sub

    Private Sub C1TrueDBGrid1_OwnerDrawCell(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.OwnerDrawCellEventArgs) Handles C1TrueDBGrid1.OwnerDrawCell
        If e.Column.Name = "是否检查" Then
            If e.Text = "否" Then
                e.Style.ForeColor = Color.Red
            End If
        End If

    End Sub

#End Region

#Region "清空__显示"

    Private Sub Load_Dict()
        C1NumericEdit1.Value = Rrow.Item("Mz_Money")
        C1NumericEdit2.Value = Rrow.Item("Mz_SsMoney")
        C1NumericEdit3.Value = Rrow.Item("Mz_ThMoney")
    End Sub

    Private Sub Zb_Show()   '显示记录
        With Rrow
            C1TextBox1.Text = .Item("Mz_Code") & ""                                   '出库编码
            C1TextBox2.Text = .Item("Ry_Name") & ""                                   '客户编码
            C1TextBox3.Text = .Item("Ks_Name") & ""
            C1TextBox4.Text = .Item("Ry_Sex") & ""
            C1TextBox5.Text = .Item("Ys_Name") & ""
            C1TextBox6.Text = .Item("Ry_Address") & ""

            C1TextBox1.ReadOnly = True
            C1TextBox1.BackColor = SystemColors.Info
            C1TextBox2.ReadOnly = True
            C1TextBox2.BackColor = SystemColors.Info
            C1TextBox3.ReadOnly = True
            C1TextBox3.BackColor = SystemColors.Info
            C1TextBox4.ReadOnly = True
            C1TextBox4.BackColor = SystemColors.Info
            C1TextBox5.ReadOnly = True
            C1TextBox5.BackColor = SystemColors.Info
            C1TextBox6.ReadOnly = True
            C1TextBox6.BackColor = SystemColors.Info

        End With

        Call P_Data_Show()

    End Sub

    Private Sub P_Data_Show()   '从表数据
        Dim Str_Select As String = " Select '1' as V_Lb, Mz_Id,Yp_Code AS Xm_Code,Mz_Dj,Mz_Sl,Mz_Yp.Mz_Money,Mx_Gyzz,Mx_Gg,Mx_Cd ,Mx_XsDw as Xm_Dw,Yp_Name as Xm_Name,Mz_Lb,V_YpKc.Yp_Ph,V_YpKc.Yp_Yxq,Dl_Code,'' as Xm_Wc,Mz.Mz_Code,Ry_Name,Ks_Name,Ys_Name,Mz_Date+Mz_Time as Mz_Date,Mz_Jffs,Bxlb_Name From Mz,Zd_YyKs,Zd_YyYs,Zd_Bxlb, Mz_Yp,V_YpKc Where Mz.Mz_Code=Mz_Yp.Mz_Code and Mz.Ys_Code=Zd_YyYs.Ys_Code and Mz.Ks_Code=Zd_YyKs.Ks_Code and Mz.Bxlb_Code=Zd_Bxlb.Bxlb_Code and Mz_Yp.Xx_Code=V_YpKc.Xx_Code And Mz.Mz_Code='" & Rrow.Item("Mz_Code") & "'  Union All Select '2' as V_Lb, Mz_Id,Mz_Xm.Xm_Code,Mz_Dj,Mz_Sl,Mz_Xm.Mz_Money,''Mx_Gyzz,'' As Mx_Gg,''Mx_Cd,Xm_Dw,Xm_Name,Mz_Lb,''Yp_Ph,Null Yp_Yxq,XmLb_Code as Dl_Code,Xm_Wc,Mz.Mz_Code,Ry_Name,Ks_Name,Ys_Name,Mz_Date+Mz_Time as Mz_Date,Mz_Jffs,Bxlb_Name From Mz,Zd_Yyks,Zd_YyYs,Zd_Bxlb,Mz_Xm,Zd_Ml_Xm3 Where Mz.Mz_Code=Mz_Xm.Mz_Code and Mz.Ys_Code=Zd_YyYs.Ys_Code and Mz.Ks_Code=Zd_YyKs.Ks_Code and Mz.Bxlb_Code=Zd_Bxlb.Bxlb_Code and Mz_Xm.Xm_Code=Zd_Ml_Xm3.Xm_Code And Mz.Mz_Code='" & Rrow.Item("Mz_Code") & "' Order By Mz_Id"
        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, Str_Select, "患者用药详单", True)
        C1TrueDBGrid1.SetDataBinding(My_Dataset, "患者用药详单", True)
        Zb_Cm = CType(BindingContext(C1TrueDBGrid1.DataSource, C1TrueDBGrid1.DataMember), CurrencyManager)

    End Sub



#End Region



    Private Sub C1Button2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Button2.Click
        ZTHisPublicFunction.MzPrintFunc.PrintMzXd(My_Dataset.Tables("患者用药详单"))
    End Sub

    Private Sub C1Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Button1.Click
        _mdlmz = _bllmz.GetModel(Rrow.Item("Mz_Code") & "")
        PublicFunction.MzFpPrint(_mdlmz, False)
    End Sub

    Private Sub Ts_Fp()
        Dim mz_top As Double = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\中软智通", "门诊上边距", Nothing)
        Dim mz_bottom As Double = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\中软智通", "门诊下边距", Nothing)
        Dim mz_left As Double = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\中软智通", "门诊左边距", Nothing)



        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "Select '0' as Fp_Id,Mz.Mz_Code,Ry_Name,Ry_Address,Ry_YlCode,Mz_Ph,Ks_Name,Mz_Dl AS Mz_Lb,Sum(Mz_Yp.Mz_Money) As Mz_Money From Mz_Yp,Mz,Zd_YyKs Where Mz_Yp.Mz_Code=Mz.Mz_Code And Mz.Ks_Code=Zd_YyKs.Ks_Code And Mz.Mz_Code='" & C1TextBox1.Text & "' Group by Mz.Mz_Code,Ry_Name,Ry_Address,Ry_YlCode,Ks_Name,Mz_Ph,Mz_Dl Union All Select '0' as Fp_Id,Mz.Mz_Code,Ry_Name,Ry_Address,Ry_YlCode,Mz_Ph,Ks_Name,Mz_Dl AS Mz_Lb,Sum(Mz_Xm.Mz_Money) As Mz_Money From Mz_Xm,Mz,Zd_YyKs Where Mz_Xm.Mz_Code=Mz.Mz_Code And Mz.Ks_Code=Zd_YyKs.Ks_Code And Mz.Mz_Code='" & C1TextBox1.Text & "' Group by Mz.Mz_Code,Ry_Name,Ry_Address,Ks_Name,Mz_Ph,Ry_YlCode,Mz_Dl", "收据明细", True)
        Dim vform As New PublicForm.Pr_Cfj(Me.Name, "重打", Nothing, Nothing)

        If IIf(iniOperate.iniopreate.GetINI("打印选项", "收据打印方式", "", HisVar.HisVar.Parapath & "\Config.ini") & "" = "", "每一大类打印一张收据", iniOperate.iniopreate.GetINI("打印选项", "收据打印方式", "", HisVar.HisVar.Parapath & "\Config.ini")) = "每一大类打印一张收据" Then
            Dim Rpt As New Ar_Mz_Print

            With Rpt
                .PageSettings.PaperKind = Printing.PaperKind.Custom

                .PageSettings.Margins.Left = mz_left
                .PageSettings.Margins.Right = 0
                .PageSettings.Margins.Top = mz_top
                .PageSettings.Margins.Bottom = mz_bottom
                .PageSettings.PaperHeight = 2.646 + mz_top + mz_bottom + 0.01
                '.PageSettings.PaperHeight = Rpt.Detail.Height + mz_top + 0.01 + mz_bottom
                .PageSettings.PaperWidth = Rpt.PrintWidth + mz_left + 0.1
                .A1.Text = HisVar.HisVar.WsyName
                .A10.Text = HisVar.HisVar.JsrName
                .A11.Text = Format(Rrow("Mz_Date"), "yyyy-MM-dd")
                .B1.Text = HisVar.HisVar.WsyName
                .B10.Text = HisVar.HisVar.JsrName
                .B11.Text = Format(Rrow("Mz_Date"), "yyyy-MM-dd")
                .C1.Text = HisVar.HisVar.WsyName
                .C10.Text = HisVar.HisVar.JsrName
                .C11.Text = Format(Rrow("Mz_Date"), "yyyy-MM-dd")
            End With



            Rpt.DataSource = My_Dataset.Tables("收据明细")

            '获取指定打印机
            Rpt.Document.Printer.PrinterName = IIf(iniOperate.iniopreate.GetINI("打印机设置", "门诊打印机", "", HisVar.HisVar.Parapath & "\Config.ini") & "" = "", Rpt.Document.Printer.PrinterName, iniOperate.iniopreate.GetINI("打印机设置", "门诊打印机", "", HisVar.HisVar.Parapath & "\Config.ini"))



            vform.Viewer1.Document = Rpt.Document
            Rpt.Run()
            vform.ShowDialog()
        Else

            Dim Rpt As New Ar_Mz_Print_Hz
            If My_Dataset.Tables("收据明细").Rows.Count Mod 5 <> 0 Then
                Dim NewRow As DataRow
                Dim K As Integer
                For K = 1 To 5 - My_Dataset.Tables("收据明细").Rows.Count Mod 5
                    NewRow = My_Dataset.Tables("收据明细").NewRow
                    NewRow.Item("Mz_Code") = C1TextBox1.Text
                    NewRow.Item("Ry_Name") = ""
                    NewRow.Item("Ry_Address") = ""
                    NewRow.Item("Ry_YlCode") = ""
                    NewRow.Item("Mz_Ph") = ""
                    NewRow.Item("Ks_Name") = ""
                    NewRow.Item("Mz_Lb") = ""
                    NewRow.Item("Mz_Money") = DBNull.Value
                    My_Dataset.Tables("收据明细").Rows.Add(NewRow)
                Next

            End If
            Dim V_ColId As Integer
            My_Dataset.Tables("收据明细").Columns("Fp_Id").ReadOnly = False
            V_ColId = BaseFunc.BaseFunc.Edit_Col0(My_Dataset.Tables("收据明细"), 0, 5)

            With Rpt
                .PageSettings.PaperKind = Printing.PaperKind.Custom

                .PageSettings.Margins.Left = mz_left
                .PageSettings.Margins.Right = 0
                .PageSettings.Margins.Top = mz_top
                .PageSettings.Margins.Bottom = mz_bottom
                .PageSettings.PaperHeight = 2.646 + mz_top + mz_bottom + 0.01
                '.PageSettings.PaperHeight = Rpt.Detail.Height + mz_top + 0.01 + mz_bottom
                .PageSettings.PaperWidth = Rpt.PrintWidth + mz_left + 0.1
                .A1.Text = HisVar.HisVar.WsyName
                .A10.Text = HisVar.HisVar.JsrName
                .A11.Text = Format(Rrow("Mz_Date"), "yyyy-MM-dd")
                .B1.Text = HisVar.HisVar.WsyName
                .B10.Text = HisVar.HisVar.JsrName
                .B11.Text = Format(Rrow("Mz_Date"), "yyyy-MM-dd")
                .C1.Text = HisVar.HisVar.WsyName
                .C10.Text = HisVar.HisVar.JsrName
                .C11.Text = Format(Rrow("Mz_Date"), "yyyy-MM-dd")
            End With

            '获取指定打印机
            Rpt.Document.Printer.PrinterName = IIf(iniOperate.iniopreate.GetINI("打印机设置", "门诊打印机", "", HisVar.HisVar.Parapath & "\Config.ini") & "" = "", Rpt.Document.Printer.PrinterName, iniOperate.iniopreate.GetINI("打印机设置", "门诊打印机", "", HisVar.HisVar.Parapath & "\Config.ini"))


            Rpt.DataSource = My_Dataset.Tables("收据明细")

            vform.Viewer1.Document = Rpt.Document
            Rpt.Run()
            vform.ShowDialog()
        End If


    End Sub

    Private Sub Qz_Fp()
        Dim StiRpt As New StiReport
        Dim Fp_Row As DataRow
        Dim Qt_Money As Double = 0.0
        Dim mz_top As Double = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\中软智通", "门诊上边距", Nothing)
        Dim mz_bottom As Double = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\中软智通", "门诊下边距", Nothing)
        Dim mz_left As Double = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\中软智通", "门诊左边距", Nothing)

        StiRpt.Load(".\Rpt\邯郸曲周门诊票据.mrt")
        StiRpt.ReportName = "邯郸曲周门诊票据"

        StiRpt.Pages(0).Margins.Left = mz_left + 2.7
        StiRpt.Pages(0).Margins.Top = mz_top
        StiRpt.Pages(0).Margins.Bottom = mz_bottom

        StiRpt.Pages(0).PageHeight = mz_top + mz_bottom + 14.5
        StiRpt.Pages(0).PageWidth = mz_left + 2.7 + 2.1 + 14.5
        '获取指定打印机
        StiRpt.PrinterSettings.PrinterName = IIf(iniOperate.iniopreate.GetINI("打印机设置", "门诊打印机", "", HisVar.HisVar.Parapath & "\Config.ini") & "" = "", StiRpt.PrinterSettings.PrinterName, iniOperate.iniopreate.GetINI("打印机设置", "门诊打印机", "", HisVar.HisVar.Parapath & "\Config.ini"))

        StiRpt.Compile()
        StiRpt("医院名称") = HisVar.HisVar.WsyName
        StiRpt("科别") = C1TextBox3.Text
        StiRpt("医生") = C1TextBox5.Text
        StiRpt("姓名") = C1TextBox2.Text
        StiRpt("经手人") = "经手人:" & HisVar.HisVar.JsrCode
        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "Select Sum(Mz_Money)Mz_Money,Mz_lb from Mz_Yp where  Mz_Code='" & C1TextBox1.Text & "' group by Mz_lb  ", "邯郸曲周发票药品卫材", True)
        For Each Fp_Row In My_Dataset.Tables("邯郸曲周发票药品卫材").Rows
            Select Case Fp_Row.Item("Mz_lb")
                Case "西药"
                    StiRpt("西药") = Format(Fp_Row.Item("Mz_Money"), "0.00")
                Case "中成药"
                    StiRpt("中成") = Format(Fp_Row.Item("Mz_Money"), "0.00")
                Case "中草药"
                    StiRpt("中草") = Format(Fp_Row.Item("Mz_Money"), "0.00")
                Case "卫生材料"
                    Qt_Money = Format(Fp_Row.Item("Mz_Money"), "0.00")
            End Select

        Next
        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "Select Sum(Mz_Money)Mz_Money,Lb_Name from Mz_Xm,Zd_Jkfl1,Zd_Jkfl2 where Zd_Jkfl1.Lb_Code=Zd_Jkfl2.Lb_Code and Mz_Xm.Xm_Code=Zd_Jkfl2.Mx_Code and  Mz_Code='" & C1TextBox1.Text & "' group by Lb_Name", "邯郸曲周发票诊疗项目", True)
        For Each Fp_Row In My_Dataset.Tables("邯郸曲周发票诊疗项目").Rows
            Select Case Fp_Row.Item("Lb_Name")
                Case "检查"
                    StiRpt("检查") = Format(Fp_Row.Item("Mz_Money"), "0.00")
                Case "治疗"
                    StiRpt("治疗") = Format(Fp_Row.Item("Mz_Money"), "0.00")
                Case "放射"
                    StiRpt("X光") = Format(Fp_Row.Item("Mz_Money"), "0.00")
                Case "手术"
                    StiRpt("手术") = Format(Fp_Row.Item("Mz_Money"), "0.00")
                Case "化验"
                    StiRpt("化验") = Format(Fp_Row.Item("Mz_Money"), "0.00")
                Case "血费"
                    StiRpt("输血") = Format(Fp_Row.Item("Mz_Money"), "0.00")
                Case "氧气"
                    StiRpt("输氧") = Format(Fp_Row.Item("Mz_Money"), "0.00")
                Case Else
                    Qt_Money = Qt_Money + Format(Fp_Row.Item("Mz_Money"), "0.00")
            End Select

        Next
        StiRpt("其它") = Format(Qt_Money, "0.00")
        StiRpt("门诊编码") = "编码:" & C1TextBox1.Text
        StiRpt("打印时间") = Format(Now, "yyyy-MM-dd HH:mm:ss")
        Dim H_Money As Double = HisVar.HisVar.Sqldal.GetSingle("Select Sum(Mz_Money) From Mz where Mz_Code='" & C1TextBox1.Text & "'")
        StiRpt("合计") = Format(H_Money, "0.00")

        Dim Money_Dx As New BaseClass.ChineseNum
        If H_Money >= 0 Then
            Money_Dx.InputString = H_Money
        Else
            Money_Dx.InputString = -H_Money
        End If

        If Money_Dx.Valiad = True Then
            If H_Money >= 0 Then
                StiRpt("大写") = Money_Dx.OutString

            Else
                StiRpt("大写") = "负" & Money_Dx.OutString

            End If
        Else
            MsgBox("输入金额有误,请检查后重新打印", MsgBoxStyle.Critical, "提示")
            Exit Sub
        End If

        StiRpt.Show()

    End Sub

    Private Sub Tl_Fp()

        Dim StiRpt As New StiReport
        'Dim Fp_Row As DataRow
        Dim Qt_Money As Double = 0.0

        Dim mz_top As Double = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\中软智通", "门诊上边距", Nothing)
        Dim mz_bottom As Double = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\中软智通", "门诊下边距", Nothing)
        Dim mz_left As Double = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\中软智通", "门诊左边距", Nothing)

        StiRpt.Load(".\Rpt\内蒙古通辽门诊票据.mrt")
        StiRpt.ReportName = "内蒙古通辽门诊票据"

        StiRpt.Pages(0).Margins.Left = mz_left + 1
        StiRpt.Pages(0).Margins.Top = mz_top
        StiRpt.Pages(0).Margins.Bottom = mz_bottom

        'StiRpt.Pages(0).PageHeight = mz_top + mz_bottom + 14.5
        'StiRpt.Pages(0).PageWidth = mz_left + 1.5 + 1.5 + 14.5
        '获取指定打印机
        StiRpt.PrinterSettings.PrinterName = IIf(iniOperate.iniopreate.GetINI("打印机设置", "门诊打印机", "", HisVar.HisVar.Parapath & "\Config.ini") & "" = "", StiRpt.PrinterSettings.PrinterName, iniOperate.iniopreate.GetINI("打印机设置", "门诊打印机", "", HisVar.HisVar.Parapath & "\Config.ini"))

        StiRpt.Compile()
        StiRpt("医院名称") = HisVar.HisVar.WsyName
        StiRpt("科别") = C1TextBox3.Text
        StiRpt("医生") = C1TextBox5.Text
        StiRpt("姓名") = C1TextBox2.Text
        StiRpt("经手人") = HisVar.HisVar.JsrName
        StiRpt("门诊编码") = C1TextBox1.Text
        StiRpt("打印时间") = Format(Now, "yyyy-MM-dd HH:mm:ss")
        StiRpt("应收") = C1NumericEdit1.Text
        StiRpt("实收") = C1NumericEdit2.Text
        StiRpt("退找") = C1NumericEdit3.Text

        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "Select Sum(Mz_Money)Mz_Money,Mz_lb from Mz_Yp where  Mz_Code='" & C1TextBox1.Text & "' group by Mz_lb  Union all Select Sum(Mz_Money)Mz_Money,Lb_Name from Mz_Xm,Zd_Jkfl1,Zd_Jkfl2 where Zd_Jkfl1.Lb_Code=Zd_Jkfl2.Lb_Code and Mz_Xm.Xm_Code=Zd_Jkfl2.Mx_Code and  Mz_Code='" & C1TextBox1.Text & "' group by Lb_Name ", "内蒙古通辽门诊票据", True)



        '将门诊票据打印明细设定为固定个数
        Dim V_Newrow As DataRow

        Dim Tbrowcount As Integer = My_Dataset.Tables("内蒙古通辽门诊票据").Rows.Count
        If Tbrowcount Mod 8 <> 0 Then
            For V_TbRowCount = 1 To 8 - (Tbrowcount Mod 8)
                V_Newrow = My_Dataset.Tables("内蒙古通辽门诊票据").NewRow
                With V_Newrow
                    .Item(0) = DBNull.Value
                    .Item(1) = DBNull.Value
                End With
                My_Dataset.Tables("内蒙古通辽门诊票据").Rows.Add(V_Newrow)
                V_Newrow.AcceptChanges()
            Next
        End If

        StiRpt.RegData(My_Dataset.Tables("内蒙古通辽门诊票据"))


        Dim Money_Dx As New BaseClass.ChineseNum
        If C1NumericEdit1.Text >= 0 Then
            Money_Dx.InputString = C1NumericEdit1.Text
        Else
            Money_Dx.InputString = -C1NumericEdit1.Text
        End If

        If Money_Dx.Valiad = True Then
            If C1NumericEdit1.Text >= 0 Then
                StiRpt("大写") = Money_Dx.OutString

            Else
                StiRpt("大写") = "负" & Money_Dx.OutString
            End If
        Else
            MsgBox("输入金额有误,请检查后重新打印", MsgBoxStyle.Critical, "提示")
            Exit Sub
        End If

        StiRpt.Show()
        'StiRpt.Design()

    End Sub

    Private Sub Cc_Fp()

        Dim StiRpt As New StiReport              '打所有门诊数据

        Dim mz_top As Double = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\中软智通", "门诊上边距", Nothing)
        Dim mz_bottom As Double = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\中软智通", "门诊下边距", Nothing)
        Dim mz_left As Double = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\中软智通", "门诊左边距", Nothing)

        StiRpt.Load(".\Rpt\吉林省医疗机构门诊收费专用票据.mrt")
        StiRpt.ReportName = "吉林省医疗机构门诊收费专用票据"

        StiRpt.Pages(0).Margins.Left = mz_left
        StiRpt.Pages(0).Margins.Top = mz_top
        StiRpt.Pages(0).Margins.Bottom = mz_bottom


        '获取指定打印机
        StiRpt.PrinterSettings.PrinterName = IIf(iniOperate.iniopreate.GetINI("打印机设置", "门诊打印机", "", HisVar.HisVar.Parapath & "\Config.ini") & "" = "", StiRpt.PrinterSettings.PrinterName, iniOperate.iniopreate.GetINI("打印机设置", "门诊打印机", "", HisVar.HisVar.Parapath & "\Config.ini"))

        StiRpt.Compile()
        StiRpt("医院名称") = HisVar.HisVar.WsyName
        StiRpt("科别") = C1TextBox3.Text
        StiRpt("医生") = C1TextBox5.Text

        StiRpt("患者类别") = HisVar.HisVar.Sqldal.GetSingle("Select Bxlb_Name from Mz,Zd_Bxlb where Mz.Bxlb_Code=Zd_Bxlb.Bxlb_Code and Mz_Code='" & C1TextBox1.Text & "'")

        StiRpt("患者姓名") = C1TextBox2.Text
        StiRpt("患者姓名1") = C1TextBox2.Text
        StiRpt("患者姓名2") = C1TextBox2.Text
        StiRpt("患者姓名3") = C1TextBox2.Text

        StiRpt("打印编号") = C1TextBox1.Text
        StiRpt("打印编号1") = C1TextBox1.Text
        StiRpt("打印编号2") = C1TextBox1.Text
        StiRpt("打印编号3") = C1TextBox1.Text

        StiRpt("打印时间") = Now
        StiRpt("打印时间1") = Now
        StiRpt("打印时间2") = Now
        StiRpt("打印时间3") = Now

        StiRpt("收款员") = HisVar.HisVar.JsrName
        StiRpt("应收金额") = CDbl(C1NumericEdit1.Text)

        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "Select  Mz_Lb,Sum(Mz_Money) AS Mz_Money From Mz_Yp  Where  Mz_Code='" & C1TextBox1.Text & "' Group By Mz_Lb", "药品类别", True)
        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "Select  Lb_Name,Sum(Mz_Money) AS Mz_Money From Mz_Xm,Zd_MzFp1,Zd_MzFp2 Where Zd_MzFp2.Yy_Code=Mz_Xm.Yy_Code And Mz_Xm.Xm_Code=Zd_MzFp2.Xm_Code And Zd_MzFp1.Lb_Code=Zd_MzFp2.Lb_Code And Mz_Code='" & C1TextBox1.Text & "' Group By Lb_Name", "服务类别", True)
        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "Select Fl_Code,Fl_Name  from Zd_Mzfp3 where Yy_Code='" & HisVar.HisVar.WsyCode & "' ", "科室类别", True)

        Dim XM As Double = 0
        Dim WM As Double = 0
        Dim CzM As Double = 0
        Dim H_Money As Double = 0

        For Each my_row In My_Dataset.Tables("药品类别").Rows
            Select Case my_row("Mz_Lb")
                Case "西药"
                    StiRpt("西药费") = CDbl(StiRpt("西药费") + my_row.Item("Mz_Money"))
                    XM = CDbl(XM + my_row("Mz_Money"))
                Case "中成药"
                    StiRpt("中成药") = CDbl(my_row.Item("Mz_Money"))
                Case "中草药"
                    StiRpt("中草药") = CDbl(my_row.Item("Mz_Money"))
                Case "卫生材料"
                    StiRpt("西药费") = CDbl(StiRpt("西药费") + my_row.Item("Mz_Money"))
                    WM = CDbl(WM + my_row("Mz_Money"))
            End Select
            'H_Money = CDbl(H_Money + my_row("Mz_Money"))
        Next

        For Each my_row In My_Dataset.Tables("服务类别").Rows
            Select Case my_row("Lb_Name")
                Case "化验费"
                    StiRpt("化验费") = CDbl(my_row.Item("Mz_Money"))
                Case "X光费"
                    StiRpt("X光费") = CDbl(my_row.Item("Mz_Money"))
                Case "电诊费"
                    StiRpt("电诊费") = CDbl(my_row.Item("Mz_Money"))
                Case "CT费"
                    StiRpt("CT费") = CDbl(my_row.Item("Mz_Money"))
                Case "磁共振"
                    StiRpt("磁共振") = CDbl(my_row.Item("Mz_Money"))
                Case "检查费"
                    StiRpt("检查费") = CDbl(my_row.Item("Mz_Money"))
                Case "手术费"
                    StiRpt("手术费") = CDbl(my_row.Item("Mz_Money"))
                Case "输氧费"
                    StiRpt("输氧费") = CDbl(my_row.Item("Mz_Money"))
                Case "输血费"
                    StiRpt("输血费") = CDbl(my_row.Item("Mz_Money"))
                Case "处置费"
                    StiRpt("处置费") = CDbl(my_row.Item("Mz_Money"))
                    CzM = CDbl(CzM + my_row("Mz_Money"))
                Case "注射费"
                    StiRpt("注射费") = CDbl(my_row.Item("Mz_Money"))
                Case "治疗费"
                    StiRpt("治疗费") = CDbl(my_row.Item("Mz_Money"))
            End Select
            'H_Money = CDbl(H_Money + my_row("Mz_Money"))
        Next

        Dim Row_I As Integer
        Dim Row_H As Integer
        For Row_I = 0 To My_Dataset.Tables("科室类别").Rows.Count - 1
            If My_Dataset.Tables("类别") IsNot Nothing Then My_Dataset.Tables("类别").Clear()
            With My_Adapter
                .SelectCommand = New SqlCommand("Select  Fl_Name,Mc_name,Mc_Code From Zd_MzFp3,Zd_MzFp31 Where Zd_MzFp31.Fl_Code=Zd_MzFp3.Fl_Code and Zd_MzFp31.Fl_Code='" & My_Dataset.Tables("科室类别").Rows(Row_I).Item("Fl_Code") & "' and Zd_MzFp31.Yy_Code='" & HisVar.HisVar.WsyCode & "'    ", My_Cn)
                .Fill(My_Dataset, "类别")
            End With

            Select Case Row_I
                Case 0
                    StiRpt("科室1") = My_Dataset.Tables("科室类别").Rows(0).Item("Fl_Name")

                    For Row_H = 0 To My_Dataset.Tables("类别").Rows.Count - 1
                        If My_Dataset.Tables("科室类别明细") IsNot Nothing Then My_Dataset.Tables("科室类别明细").Clear()
                        With My_Adapter
                            .SelectCommand = New SqlCommand("Select  Mc_Name,Lb_name From Zd_MzFp31,Zd_MzFp4,Zd_MzFp1 Where Zd_MzFp31.Mc_Code='" & My_Dataset.Tables("类别").Rows(Row_H).Item("Mc_Code") & "' And Zd_MzFp31.Mc_Code=Zd_MzFp4.Mc_Code And Zd_MzFp1.Lb_Code=Zd_MzFp4.Lb_Code and Zd_MzFp31.Yy_Code=Zd_MzFp4.Yy_Code and Zd_MzFp31.Yy_Code='" & HisVar.HisVar.WsyCode & "'  union all Select  Mc_Name,Dl_name From Zd_MzFp31,Zd_MzFp4,Zd_Ml_Yp1 Where Zd_MzFp31.Mc_Code='" & My_Dataset.Tables("类别").Rows(Row_H).Item("Mc_Code") & "' And Zd_MzFp31.Mc_Code=Zd_MzFp4.Mc_Code And 'Y'+Zd_Ml_Yp1.Dl_Code=Zd_MzFp4.Lb_Code and Zd_MzFp31.Yy_Code=Zd_MzFp4.Yy_Code and Zd_MzFp31.Yy_Code='" & HisVar.HisVar.WsyCode & "'  ", My_Cn)
                            .Fill(My_Dataset, "科室类别明细")
                        End With
                        If My_Dataset.Tables("科室类别明细").Rows.Count <> 0 Then
                            Select Case Row_H
                                Case 0
                                    StiRpt("类别1") = My_Dataset.Tables("类别").Rows(0).Item("Mc_Name")
                                    For Each my_row In My_Dataset.Tables("科室类别明细").Rows
                                        Select Case my_row("Lb_name")
                                            Case "西药"
                                                StiRpt("类别金额1") = Val(StiRpt("类别金额1")) + XM
                                            Case "中成药"
                                                StiRpt("类别金额1") = Val(StiRpt("类别金额1")) + Val(StiRpt("中成药"))
                                            Case "中草药"
                                                StiRpt("类别金额1") = Val(StiRpt("类别金额1")) + Val(StiRpt("中草药"))
                                            Case "卫生材料"
                                                StiRpt("类别金额1") = Val(StiRpt("类别金额1")) + WM
                                            Case "化验费"
                                                StiRpt("类别金额1") = Val(StiRpt("类别金额1")) + Val(StiRpt("化验费"))
                                            Case "X光费"
                                                StiRpt("类别金额1") = Val(StiRpt("类别金额1")) + Val(StiRpt("X光费"))
                                            Case "电诊费"
                                                StiRpt("类别金额1") = Val(StiRpt("类别金额1")) + Val(StiRpt("电诊费"))
                                            Case "CT费"
                                                StiRpt("类别金额1") = Val(StiRpt("类别金额1")) + Val(StiRpt("CT费"))
                                            Case "磁共振"
                                                StiRpt("类别金额1") = Val(StiRpt("类别金额1")) + Val(StiRpt("磁共振"))
                                            Case "检查费"
                                                StiRpt("类别金额1") = Val(StiRpt("类别金额1")) + Val(StiRpt("检查费"))
                                            Case "手术费"
                                                StiRpt("类别金额1") = Val(StiRpt("类别金额1")) + Val(StiRpt("手术费"))
                                            Case "输氧费"
                                                StiRpt("类别金额1") = Val(StiRpt("类别金额1")) + Val(StiRpt("输氧费"))
                                            Case "输血费"
                                                StiRpt("类别金额1") = Val(StiRpt("类别金额1")) + Val(StiRpt("输血费"))
                                            Case "处置费"
                                                StiRpt("类别金额1") = Val(StiRpt("类别金额1")) + CzM
                                            Case "注射费"
                                                StiRpt("类别金额1") = Val(StiRpt("类别金额1")) + Val(StiRpt("注射费"))
                                            Case "治疗费"
                                                StiRpt("类别金额1") = Val(StiRpt("类别金额1")) + Val(StiRpt("治疗费"))
                                        End Select
                                    Next
                                Case 1
                                    StiRpt("类别2") = My_Dataset.Tables("类别").Rows(1).Item("Mc_Name")
                                    For Each my_row In My_Dataset.Tables("科室类别明细").Rows
                                        Select Case my_row("Lb_name")
                                            Case "西药"
                                                StiRpt("类别金额2") = Val(StiRpt("类别金额2")) + XM
                                            Case "中成药"
                                                StiRpt("类别金额2") = Val(StiRpt("类别金额2")) + Val(StiRpt("中成药"))
                                            Case "中草药"
                                                StiRpt("类别金额2") = Val(StiRpt("类别金额2")) + Val(StiRpt("中草药"))
                                            Case "卫生材料"
                                                StiRpt("类别金额2") = Val(StiRpt("类别金额2")) + WM
                                            Case "化验费"
                                                StiRpt("类别金额2") = Val(StiRpt("类别金额2")) + Val(StiRpt("化验费"))
                                            Case "X光费"
                                                StiRpt("类别金额2") = Val(StiRpt("类别金额2")) + Val(StiRpt("X光费"))
                                            Case "电诊费"
                                                StiRpt("类别金额2") = Val(StiRpt("类别金额2")) + Val(StiRpt("电诊费"))
                                            Case "CT费"
                                                StiRpt("类别金额2") = Val(StiRpt("类别金额2")) + Val(StiRpt("CT费"))
                                            Case "磁共振"
                                                StiRpt("类别金额2") = Val(StiRpt("类别金额2")) + Val(StiRpt("磁共振"))
                                            Case "检查费"
                                                StiRpt("类别金额2") = Val(StiRpt("类别金额2")) + Val(StiRpt("检查费"))
                                            Case "手术费"
                                                StiRpt("类别金额2") = Val(StiRpt("类别金额2")) + Val(StiRpt("手术费"))
                                            Case "输氧费"
                                                StiRpt("类别金额2") = Val(StiRpt("类别金额2")) + Val(StiRpt("输氧费"))
                                            Case "输血费"
                                                StiRpt("类别金额2") = Val(StiRpt("类别金额2")) + Val(StiRpt("输血费"))
                                            Case "处置费"
                                                StiRpt("类别金额2") = Val(StiRpt("类别金额2")) + CzM
                                            Case "注射费"
                                                StiRpt("类别金额2") = Val(StiRpt("类别金额2")) + Val(StiRpt("注射费"))
                                            Case "治疗费"
                                                StiRpt("类别金额2") = Val(StiRpt("类别金额2")) + Val(StiRpt("治疗费"))
                                        End Select
                                    Next
                            End Select
                            StiRpt("合计1") = StiRpt("类别金额1") + StiRpt("类别金额2")
                        End If
                    Next
                Case 1
                    StiRpt("科室2") = My_Dataset.Tables("科室类别").Rows(1).Item("Fl_Name")

                    For Row_H = 0 To My_Dataset.Tables("类别").Rows.Count - 1
                        If My_Dataset.Tables("科室类别明细") IsNot Nothing Then My_Dataset.Tables("科室类别明细").Clear()
                        With My_Adapter
                            .SelectCommand = New SqlCommand("Select  Mc_Name,Lb_name From Zd_MzFp31,Zd_MzFp4,Zd_MzFp1 Where Zd_MzFp31.Mc_Code='" & My_Dataset.Tables("类别").Rows(Row_H).Item("Mc_Code") & "' And Zd_MzFp31.Mc_Code=Zd_MzFp4.Mc_Code And Zd_MzFp1.Lb_Code=Zd_MzFp4.Lb_Code and Zd_MzFp31.Yy_Code=Zd_MzFp4.Yy_Code and Zd_MzFp31.Yy_Code='" & HisVar.HisVar.WsyCode & "'  union all Select  Mc_Name,Dl_name From Zd_MzFp31,Zd_MzFp4,Zd_Ml_Yp1 Where Zd_MzFp31.Mc_Code='" & My_Dataset.Tables("类别").Rows(Row_H).Item("Mc_Code") & "' And Zd_MzFp31.Mc_Code=Zd_MzFp4.Mc_Code And 'Y'+Zd_Ml_Yp1.Dl_Code=Zd_MzFp4.Lb_Code and Zd_MzFp31.Yy_Code=Zd_MzFp4.Yy_Code and Zd_MzFp31.Yy_Code='" & HisVar.HisVar.WsyCode & "'  ", My_Cn)
                            .Fill(My_Dataset, "科室类别明细")
                        End With
                        If My_Dataset.Tables("科室类别明细").Rows.Count <> 0 Then
                            Select Case Row_H
                                Case 0
                                    StiRpt("类别3") = My_Dataset.Tables("类别").Rows(0).Item("Mc_Name")
                                    For Each my_row In My_Dataset.Tables("科室类别明细").Rows
                                        Select Case my_row("Lb_name")
                                            Case "西药"
                                                StiRpt("类别金额3") = Val(StiRpt("类别金额3")) + XM
                                            Case "中成药"
                                                StiRpt("类别金额3") = Val(StiRpt("类别金额3")) + Val(StiRpt("中成药"))
                                            Case "中草药"
                                                StiRpt("类别金额3") = Val(StiRpt("类别金额3")) + Val(StiRpt("中草药"))
                                            Case "卫生材料"
                                                StiRpt("类别金额3") = Val(StiRpt("类别金额3")) + WM
                                            Case "化验费"
                                                StiRpt("类别金额3") = Val(StiRpt("类别金额3")) + Val(StiRpt("化验费"))
                                            Case "X光费"
                                                StiRpt("类别金额3") = Val(StiRpt("类别金额3")) + Val(StiRpt("X光费"))
                                            Case "电诊费"
                                                StiRpt("类别金额3") = Val(StiRpt("类别金额3")) + Val(StiRpt("电诊费"))
                                            Case "CT费"
                                                StiRpt("类别金额3") = Val(StiRpt("类别金额3")) + Val(StiRpt("CT费"))
                                            Case "磁共振"
                                                StiRpt("类别金额3") = Val(StiRpt("类别金额3")) + Val(StiRpt("磁共振"))
                                            Case "检查费"
                                                StiRpt("类别金额3") = Val(StiRpt("类别金额3")) + Val(StiRpt("检查费"))
                                            Case "手术费"
                                                StiRpt("类别金额3") = Val(StiRpt("类别金额3")) + Val(StiRpt("手术费"))
                                            Case "输氧费"
                                                StiRpt("类别金额3") = Val(StiRpt("类别金额3")) + Val(StiRpt("输氧费"))
                                            Case "输血费"
                                                StiRpt("类别金额3") = Val(StiRpt("类别金额3")) + Val(StiRpt("输血费"))
                                            Case "处置费"
                                                StiRpt("类别金额3") = Val(StiRpt("类别金额3")) + CzM
                                            Case "注射费"
                                                StiRpt("类别金额3") = Val(StiRpt("类别金额3")) + Val(StiRpt("注射费"))
                                            Case "治疗费"
                                                StiRpt("类别金额3") = Val(StiRpt("类别金额3")) + Val(StiRpt("治疗费"))
                                        End Select
                                    Next
                                Case 1
                                    StiRpt("类别4") = My_Dataset.Tables("类别").Rows(1).Item("Mc_Name")
                                    For Each my_row In My_Dataset.Tables("科室类别明细").Rows
                                        Select Case my_row("Lb_name")
                                            Case "西药"
                                                StiRpt("类别金额4") = Val(StiRpt("类别金额4")) + XM
                                            Case "中成药"
                                                StiRpt("类别金额4") = Val(StiRpt("类别金额4")) + Val(StiRpt("中成药"))
                                            Case "中草药"
                                                StiRpt("类别金额4") = Val(StiRpt("类别金额4")) + Val(StiRpt("中草药"))
                                            Case "卫生材料"
                                                StiRpt("类别金额4") = Val(StiRpt("类别金额4")) + WM
                                            Case "化验费"
                                                StiRpt("类别金额4") = Val(StiRpt("类别金额4")) + Val(StiRpt("化验费"))
                                            Case "X光费"
                                                StiRpt("类别金额4") = Val(StiRpt("类别金额4")) + Val(StiRpt("X光费"))
                                            Case "电诊费"
                                                StiRpt("类别金额4") = Val(StiRpt("类别金额4")) + Val(StiRpt("电诊费"))
                                            Case "CT费"
                                                StiRpt("类别金额4") = Val(StiRpt("类别金额4")) + Val(StiRpt("CT费"))
                                            Case "磁共振"
                                                StiRpt("类别金额4") = Val(StiRpt("类别金额4")) + Val(StiRpt("磁共振"))
                                            Case "检查费"
                                                StiRpt("类别金额4") = Val(StiRpt("类别金额4")) + Val(StiRpt("检查费"))
                                            Case "手术费"
                                                StiRpt("类别金额4") = Val(StiRpt("类别金额4")) + Val(StiRpt("手术费"))
                                            Case "输氧费"
                                                StiRpt("类别金额4") = Val(StiRpt("类别金额4")) + Val(StiRpt("输氧费"))
                                            Case "输血费"
                                                StiRpt("类别金额4") = Val(StiRpt("类别金额4")) + Val(StiRpt("输血费"))
                                            Case "处置费"
                                                StiRpt("类别金额4") = Val(StiRpt("类别金额4")) + CzM
                                            Case "注射费"
                                                StiRpt("类别金额4") = Val(StiRpt("类别金额4")) + Val(StiRpt("注射费"))
                                            Case "治疗费"
                                                StiRpt("类别金额4") = Val(StiRpt("类别金额4")) + Val(StiRpt("治疗费"))
                                        End Select
                                    Next
                            End Select
                            StiRpt("合计2") = StiRpt("类别金额3") + StiRpt("类别金额4")
                        End If
                    Next
                Case 2
                    StiRpt("科室3") = My_Dataset.Tables("科室类别").Rows(2).Item("Fl_Name")

                    For Row_H = 0 To My_Dataset.Tables("类别").Rows.Count - 1
                        If My_Dataset.Tables("科室类别明细") IsNot Nothing Then My_Dataset.Tables("科室类别明细").Clear()
                        With My_Adapter
                            .SelectCommand = New SqlCommand("Select  Mc_Name,Lb_name From Zd_MzFp31,Zd_MzFp4,Zd_MzFp1 Where Zd_MzFp31.Mc_Code='" & My_Dataset.Tables("类别").Rows(Row_H).Item("Mc_Code") & "' And Zd_MzFp31.Mc_Code=Zd_MzFp4.Mc_Code And Zd_MzFp1.Lb_Code=Zd_MzFp4.Lb_Code and Zd_MzFp31.Yy_Code=Zd_MzFp4.Yy_Code and Zd_MzFp31.Yy_Code='" & HisVar.HisVar.WsyCode & "'  union all Select  Mc_Name,Dl_name From Zd_MzFp31,Zd_MzFp4,Zd_Ml_Yp1 Where Zd_MzFp31.Mc_Code='" & My_Dataset.Tables("类别").Rows(Row_H).Item("Mc_Code") & "' And Zd_MzFp31.Mc_Code=Zd_MzFp4.Mc_Code And 'Y'+Zd_Ml_Yp1.Dl_Code=Zd_MzFp4.Lb_Code and Zd_MzFp31.Yy_Code=Zd_MzFp4.Yy_Code and Zd_MzFp31.Yy_Code='" & HisVar.HisVar.WsyCode & "'  ", My_Cn)
                            .Fill(My_Dataset, "科室类别明细")
                        End With
                        If My_Dataset.Tables("科室类别明细").Rows.Count <> 0 Then
                            Select Case Row_H
                                Case 0
                                    StiRpt("类别5") = My_Dataset.Tables("类别").Rows(0).Item("Mc_Name")
                                    For Each my_row In My_Dataset.Tables("科室类别明细").Rows
                                        Select Case my_row("Lb_name")
                                            Case "西药"
                                                StiRpt("类别金额5") = Val(StiRpt("类别金额5")) + XM
                                            Case "中成药"
                                                StiRpt("类别金额5") = Val(StiRpt("类别金额5")) + Val(StiRpt("中成药"))
                                            Case "中草药"
                                                StiRpt("类别金额5") = Val(StiRpt("类别金额5")) + Val(StiRpt("中草药"))
                                            Case "卫生材料"
                                                StiRpt("类别金额5") = Val(StiRpt("类别金额5")) + WM
                                            Case "化验费"
                                                StiRpt("类别金额5") = Val(StiRpt("类别金额5")) + Val(StiRpt("化验费"))
                                            Case "X光费"
                                                StiRpt("类别金额5") = Val(StiRpt("类别金额5")) + Val(StiRpt("X光费"))
                                            Case "电诊费"
                                                StiRpt("类别金额5") = Val(StiRpt("类别金额5")) + Val(StiRpt("电诊费"))
                                            Case "CT费"
                                                StiRpt("类别金额5") = Val(StiRpt("类别金额5")) + Val(StiRpt("CT费"))
                                            Case "磁共振"
                                                StiRpt("类别金额5") = Val(StiRpt("类别金额5")) + Val(StiRpt("磁共振"))
                                            Case "检查费"
                                                StiRpt("类别金额5") = Val(StiRpt("类别金额5")) + Val(StiRpt("检查费"))
                                            Case "手术费"
                                                StiRpt("类别金额5") = Val(StiRpt("类别金额5")) + Val(StiRpt("手术费"))
                                            Case "输氧费"
                                                StiRpt("类别金额5") = Val(StiRpt("类别金额5")) + Val(StiRpt("输氧费"))
                                            Case "输血费"
                                                StiRpt("类别金额5") = Val(StiRpt("类别金额5")) + Val(StiRpt("输血费"))
                                            Case "处置费"
                                                StiRpt("类别金额5") = Val(StiRpt("类别金额5")) + CzM
                                            Case "注射费"
                                                StiRpt("类别金额5") = Val(StiRpt("类别金额5")) + Val(StiRpt("注射费"))
                                            Case "治疗费"
                                                StiRpt("类别金额5") = Val(StiRpt("类别金额5")) + Val(StiRpt("治疗费"))
                                        End Select
                                    Next
                                Case 1
                                    StiRpt("类别6") = My_Dataset.Tables("类别").Rows(1).Item("Mc_Name")
                                    For Each my_row In My_Dataset.Tables("科室类别明细").Rows
                                        Select Case my_row("Lb_name")
                                            Case "西药"
                                                StiRpt("类别金额6") = Val(StiRpt("类别金额6")) + XM
                                            Case "中成药"
                                                StiRpt("类别金额6") = Val(StiRpt("类别金额6")) + Val(StiRpt("中成药"))
                                            Case "中草药"
                                                StiRpt("类别金额6") = Val(StiRpt("类别金额6")) + Val(StiRpt("中草药"))
                                            Case "卫生材料"
                                                StiRpt("类别金额6") = Val(StiRpt("类别金额6")) + WM
                                            Case "化验费"
                                                StiRpt("类别金额6") = Val(StiRpt("类别金额6")) + Val(StiRpt("化验费"))
                                            Case "X光费"
                                                StiRpt("类别金额6") = Val(StiRpt("类别金额6")) + Val(StiRpt("X光费"))
                                            Case "电诊费"
                                                StiRpt("类别金额6") = Val(StiRpt("类别金额6")) + Val(StiRpt("电诊费"))
                                            Case "CT费"
                                                StiRpt("类别金额6") = Val(StiRpt("类别金额6")) + Val(StiRpt("CT费"))
                                            Case "磁共振"
                                                StiRpt("类别金额6") = Val(StiRpt("类别金额6")) + Val(StiRpt("磁共振"))
                                            Case "检查费"
                                                StiRpt("类别金额6") = Val(StiRpt("类别金额6")) + Val(StiRpt("检查费"))
                                            Case "手术费"
                                                StiRpt("类别金额6") = Val(StiRpt("类别金额6")) + Val(StiRpt("手术费"))
                                            Case "输氧费"
                                                StiRpt("类别金额6") = Val(StiRpt("类别金额6")) + Val(StiRpt("输氧费"))
                                            Case "输血费"
                                                StiRpt("类别金额6") = Val(StiRpt("类别金额6")) + Val(StiRpt("输血费"))
                                            Case "处置费"
                                                StiRpt("类别金额6") = Val(StiRpt("类别金额6")) + CzM
                                            Case "注射费"
                                                StiRpt("类别金额6") = Val(StiRpt("类别金额6")) + Val(StiRpt("注射费"))
                                            Case "治疗费"
                                                StiRpt("类别金额6") = Val(StiRpt("类别金额6")) + Val(StiRpt("治疗费"))
                                        End Select
                                    Next
                            End Select
                            StiRpt("合计3") = StiRpt("类别金额5") + StiRpt("类别金额6")
                        End If
                    Next
            End Select
        Next

        '--------------------------------------------------------
        If StiRpt("科室1") = "" Or (StiRpt("类别1") = "" And StiRpt("类别2") = "") Or (Val(StiRpt("类别金额1")) = 0 And Val(StiRpt("类别金额2")) = 0) Then
            StiRpt("打印编号1") = ""
            StiRpt("患者姓名1") = ""
            StiRpt("科室1") = ""
            StiRpt("类别1") = ""
            StiRpt("类别2") = ""
            StiRpt("作废1") = "作   废"

        Else
            StiRpt("作废1") = ""
            If Val(StiRpt("类别金额1")) = 0 And Val(StiRpt("类别金额2")) <> 0 Then
                StiRpt("类别1") = StiRpt("类别2")
                StiRpt("类别金额1") = Val(StiRpt("类别金额2"))
                StiRpt("类别2") = ""
                StiRpt("类别金额2") = 0
            End If

            If Val(StiRpt("类别金额2")) = 0 Then
                StiRpt("类别2") = ""
            End If

        End If
        '--------------------------------------------------------
        If StiRpt("科室2") = "" Or (StiRpt("类别3") = "" And StiRpt("类别4") = "") Or (Val(StiRpt("类别金额3")) = 0 And Val(StiRpt("类别金额4")) = 0) Then
            StiRpt("打印编号2") = ""
            StiRpt("患者姓名2") = ""
            StiRpt("科室2") = ""
            StiRpt("类别3") = ""
            StiRpt("类别4") = ""
            StiRpt("作废2") = "作   废"

        Else
            StiRpt("作废2") = ""
            If Val(StiRpt("类别金额3")) = 0 And Val(StiRpt("类别金额4")) <> 0 Then
                StiRpt("类别3") = StiRpt("类别4")
                StiRpt("类别金额3") = Val(StiRpt("类别金额4"))
                StiRpt("类别4") = ""
                StiRpt("类别金额4") = 0
            End If

            If Val(StiRpt("类别金额4")) = 0 Then
                StiRpt("类别4") = ""
            End If

        End If
        '--------------------------------------------------------
        If StiRpt("科室3") = "" Or (StiRpt("类别5") = "" And StiRpt("类别6") = "") Or (Val(StiRpt("类别金额5")) = 0 And Val(StiRpt("类别金额6")) = 0) Then
            StiRpt("打印编号3") = ""
            StiRpt("患者姓名3") = ""
            StiRpt("科室3") = ""
            StiRpt("类别5") = ""
            StiRpt("类别6") = ""
            StiRpt("作废3") = "作   废"

        Else
            StiRpt("作废3") = ""
            If Val(StiRpt("类别金额5")) = 0 And Val(StiRpt("类别金额6")) <> 0 Then
                StiRpt("类别5") = StiRpt("类别6")
                StiRpt("类别金额5") = Val(StiRpt("类别金额6"))
                StiRpt("类别6") = ""
                StiRpt("类别金额6") = 0
            End If

            If Val(StiRpt("类别金额6")) = 0 Then
                StiRpt("类别6") = ""
            End If

        End If
        '--------------------------------------------------------

        StiRpt.Show()
    End Sub

    Private Sub Ts_Fp2013()
        Dim StiRpt As New StiReport
        'Dim Fp_Row As DataRow
        Dim Qt_Money As Double = 0.0

        Dim mz_top As Double = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\中软智通", "门诊上边距", Nothing)
        Dim mz_bottom As Double = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\中软智通", "门诊下边距", Nothing)
        Dim mz_left As Double = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\中软智通", "门诊左边距", Nothing)

        StiRpt.Load(".\Rpt\2013年河北省门诊收费票据.mrt")
        StiRpt.ReportName = "2013年河北省门诊收费票据"

        StiRpt.Pages(0).Margins.Left = mz_left + 2
        StiRpt.Pages(0).Margins.Top = mz_top
        StiRpt.Pages(0).Margins.Bottom = mz_bottom


        '获取指定打印机
        StiRpt.PrinterSettings.PrinterName = IIf(iniOperate.iniopreate.GetINI("打印机设置", "门诊打印机", "", HisVar.HisVar.Parapath & "\Config.ini") & "" = "", StiRpt.PrinterSettings.PrinterName, iniOperate.iniopreate.GetINI("打印机设置", "门诊打印机", "", HisVar.HisVar.Parapath & "\Config.ini"))


        StiRpt.Compile()
        StiRpt("医疗机构") = HisVar.HisVar.WsyName
        StiRpt("科室") = Rrow.Item("Ks_Name")
        StiRpt("业务流水号") = Rrow.Item("Mz_Code")
        StiRpt("姓名") = Rrow.Item("Ry_Name")
        StiRpt("性别") = Rrow.Item("Ry_Sex")
        StiRpt("医保类型") = Rrow.Item("Bxlb_Name") & ""
        StiRpt("社保号码") = Rrow.Item("Ry_YlCode") & ""
        StiRpt("收款人") = HisVar.HisVar.JsrName
        StiRpt("打印时间") = Format(Now, "yyyy-MM-dd HH:mm:ss")


        If (HisPara.PublicConfig.XqName.Contains("迁安") And HisVar.HisVar.WsyName <> "迁安钢城医院") Or HisPara.PublicConfig.XqName.Contains("迁西") Or HisPara.PublicConfig.XqName.Contains("丰润") Or HisPara.PublicConfig.XqName.Contains("曲周") Then
            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "Select '0' as V_Group,Yp_Name as Mz_Lb,Mz_Sl,Mz_Money  from Mz_Yp,V_YpKc where Mz_Yp.Xx_Code=V_YpKc.Xx_Code and  Mz_Code='" & C1TextBox1.Text & "'  Union all Select Row_Number() over(order by Xm_Name) as V_Group,Xm_Name,Mz_Sl,Mz_Money from Mz_Xm,Zd_Ml_Xm3 where Mz_Xm.Xm_Code=Zd_Ml_Xm3.Xm_Code and  Mz_Code='" & C1TextBox1.Text & "' ", "收费明细", True)
        ElseIf HisPara.PublicConfig.XqName.Contains("博野") Then
            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "Select '0' as V_Group,Yp_Name as Mz_Lb,Mz_Sl,Mz_Money  from Mz_Yp,V_YpKc where Mz_Yp.Xx_Code=V_YpKc.Xx_Code and  Mz_Code='" & C1TextBox1.Text & "'  Union all Select Zd_Jkfl1.Lb_Code as V_Group, Xm_Name as Mz_Lb,Null as Mz_Sl,Sum(Mz_Money)Mz_Money from Mz_Xm,Zd_Ml_Xm3 left join Zd_Jkfl2 On Zd_Ml_Xm3.Xm_Code=Zd_Jkfl2.Mx_Code left join Zd_Jkfl1 on Zd_Jkfl1.Lb_Code=Zd_Jkfl2.Lb_Code where   Mz_Xm.Xm_Code=Zd_Ml_Xm3.Xm_Code and  Mz_Code='" & C1TextBox1.Text & "'  group by Zd_JkFl1.Lb_Code, Xm_Name ", "收费明细", True)

        Else
            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "Select '0' as V_Group, Case Mz_Lb when '西药' then '西药费' else Mz_lb end as Mz_Lb,Null as Mz_Sl,Sum(Mz_Money)Mz_Money  from Mz_Yp where  Mz_Code='" & C1TextBox1.Text & "' group by Mz_lb  Union all Select  Row_Number() over(order by Lb_Name) as V_Group,Lb_Name,Null as Mz_Sl,Sum(Mz_Money)Mz_Money from Mz_Xm,Zd_Jkfl1,Zd_Jkfl2 where Zd_Jkfl1.Lb_Code=Zd_Jkfl2.Lb_Code and Mz_Xm.Xm_Code=Zd_Jkfl2.Mx_Code and  Mz_Code='" & C1TextBox1.Text & "' and Zd_Jkfl2.Yy_Code='" & HisVar.HisVar.WsyCode & "' group by Lb_Name ", "收费明细", True)
        End If

        '将门诊打印明细设定为固定个数
        Dim V_Newrow As DataRow
        If HisPara.PublicConfig.XqName.Contains("博野") Then

            Dim Yprowcount As Integer = My_Dataset.Tables("收费明细").Select("V_Group=0").Length
            If Yprowcount <> 0 Then
                If Yprowcount Mod 22 <> 0 Then
                    For V_TbRowCount = 1 To 22 - (Yprowcount Mod 22)
                        V_Newrow = My_Dataset.Tables("收费明细").NewRow
                        With V_Newrow
                            .Item(0) = 0
                            .Item(1) = DBNull.Value
                            .Item(2) = DBNull.Value
                            .Item(3) = DBNull.Value
                        End With
                        My_Dataset.Tables("收费明细").Rows.Add(V_Newrow)
                        V_Newrow.AcceptChanges()
                    Next

                End If

            End If

            My_Dataset.Tables("收费明细").Columns("V_Group").ReadOnly = False
            Dim I As Integer = 0
            For Each V_Newrow In My_Dataset.Tables("收费明细").Select("V_Group=0")
                V_Newrow.Item("V_Group") = I \ 22
                I = I + 1
                V_Newrow.AcceptChanges()
            Next


            Dim Row As DataRow
            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "Select Lb_Code from Zd_Jkfl1", "发票类别编码", True)
            For Each Row In My_Dataset.Tables("发票类别编码").Rows
                Dim Xmrowcount As Integer = My_Dataset.Tables("收费明细").Select("V_Group='" & Row.Item("Lb_Code") & "'").Length
                If Xmrowcount <> 0 Then
                    If Xmrowcount Mod 22 <> 0 Then
                        For V_TbRowCount = 1 To 22 - (Xmrowcount Mod 22)
                            V_Newrow = My_Dataset.Tables("收费明细").NewRow
                            With V_Newrow
                                .Item(0) = Row.Item("Lb_Code")
                                .Item(1) = DBNull.Value
                                .Item(2) = DBNull.Value
                                .Item(3) = DBNull.Value
                            End With
                            My_Dataset.Tables("收费明细").Rows.Add(V_Newrow)
                            V_Newrow.AcceptChanges()
                        Next
                    End If
                End If
            Next
        Else
            Dim Tbrowcount As Integer = My_Dataset.Tables("收费明细").Rows.Count
            If Tbrowcount Mod 22 <> 0 Then
                For V_TbRowCount = 1 To 22 - (Tbrowcount Mod 22)
                    V_Newrow = My_Dataset.Tables("收费明细").NewRow
                    With V_Newrow
                        .Item(0) = DBNull.Value
                        .Item(1) = DBNull.Value
                        .Item(2) = DBNull.Value
                        .Item(3) = DBNull.Value
                    End With
                    My_Dataset.Tables("收费明细").Rows.Add(V_Newrow)
                    V_Newrow.AcceptChanges()
                Next

            End If
            My_Dataset.Tables("收费明细").Columns("V_Group").ReadOnly = False
            BaseFunc.BaseFunc.Edit_Col0(My_Dataset.Tables("收费明细"), 0, 22)
        End If

        StiRpt.RegData(My_Dataset.Tables("收费明细"))



        StiRpt.Show()
    End Sub

    Private Sub Hld_Fp_Lb()
        Dim StiRpt As New StiReport
        'Dim Fp_Row As DataRow
        Dim Qt_Money As Double = 0.0

        Dim mz_top As Double = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\中软智通", "门诊上边距", Nothing)
        Dim mz_bottom As Double = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\中软智通", "门诊下边距", Nothing)
        Dim mz_left As Double = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\中软智通", "门诊左边距", Nothing)

        StiRpt.Load(".\Rpt\辽宁省门诊收费票据老版.mrt")
        StiRpt.ReportName = "辽宁省门诊收费票据"

        StiRpt.Pages(0).Margins.Left = mz_left + 2
        StiRpt.Pages(0).Margins.Top = mz_top
        StiRpt.Pages(0).Margins.Bottom = mz_bottom


        '获取指定打印机
        StiRpt.PrinterSettings.PrinterName = IIf(iniOperate.iniopreate.GetINI("打印机设置", "门诊打印机", "", HisVar.HisVar.Parapath & "\Config.ini") & "" = "", StiRpt.PrinterSettings.PrinterName, iniOperate.iniopreate.GetINI("打印机设置", "门诊打印机", "", HisVar.HisVar.Parapath & "\Config.ini"))


        StiRpt.Compile()
        StiRpt("医疗机构") = HisVar.HisVar.WsyName
        StiRpt("科室") = Rrow.Item("Ks_Name")
        StiRpt("业务流水号") = Rrow.Item("Mz_Code")
        StiRpt("姓名") = Rrow.Item("Ry_Name")
        StiRpt("性别") = Rrow.Item("Ry_Sex")
        StiRpt("医保类型") = Rrow.Item("Bxlb_Name") & ""
        StiRpt("社保号码") = Rrow.Item("Ry_YlCode") & ""
        StiRpt("收款人") = HisVar.HisVar.JsrName
        StiRpt("打印时间") = Format(Now, "yyyy-MM-dd HH:mm:ss")


        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "Select '0' as V_Group, Case Mz_Lb when '西药' then '西药费' else Mz_lb end as Mz_Lb,Null as Mz_Sl,Sum(Mz_Money)Mz_Money  from Mz_Yp where  Mz_Code='" & C1TextBox1.Text & "' group by Mz_lb  Union all Select  Row_Number() over(order by Lb_Name) as V_Group,Lb_Name,Null as Mz_Sl,Sum(Mz_Money)Mz_Money from Mz_Xm,Zd_Jkfl1,Zd_Jkfl2 where Zd_Jkfl1.Lb_Code=Zd_Jkfl2.Lb_Code and Mz_Xm.Xm_Code=Zd_Jkfl2.Mx_Code and  Mz_Code='" & C1TextBox1.Text & "' and Zd_Jkfl2.Yy_Code='" & HisVar.HisVar.WsyCode & "' group by Lb_Name ", "收费明细", True)

        '将门诊打印明细设定为固定个数
        Dim V_Newrow As DataRow

        Dim Tbrowcount As Integer = My_Dataset.Tables("收费明细").Rows.Count
        If Tbrowcount Mod 22 <> 0 Then
            For V_TbRowCount = 1 To 22 - (Tbrowcount Mod 22)
                V_Newrow = My_Dataset.Tables("收费明细").NewRow
                With V_Newrow
                    .Item(0) = DBNull.Value
                    .Item(1) = DBNull.Value
                    .Item(2) = DBNull.Value
                    .Item(3) = DBNull.Value
                End With
                My_Dataset.Tables("收费明细").Rows.Add(V_Newrow)
                V_Newrow.AcceptChanges()
            Next
            My_Dataset.Tables("收费明细").Columns("V_Group").ReadOnly = False
            BaseFunc.BaseFunc.Edit_Col0(My_Dataset.Tables("收费明细"), 0, 22)
        End If


        StiRpt.RegData(My_Dataset.Tables("收费明细"))



        StiRpt.Show()
    End Sub

    Private Sub Hld_Fp()
        Dim StiRpt As New StiReport
        'Dim Fp_Row As DataRow
        Dim Qt_Money As Double = 0.0

        Dim mz_top As Double = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\中软智通", "门诊上边距", Nothing)
        Dim mz_bottom As Double = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\中软智通", "门诊下边距", Nothing)
        Dim mz_left As Double = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\中软智通", "门诊左边距", Nothing)

        StiRpt.Load(".\Rpt\辽宁省门诊收费票据.mrt")
        StiRpt.ReportName = "辽宁省门诊收费票据"

        StiRpt.Pages(0).Margins.Left = mz_left + 2
        StiRpt.Pages(0).Margins.Top = mz_top
        StiRpt.Pages(0).Margins.Bottom = mz_bottom


        '获取指定打印机
        StiRpt.PrinterSettings.PrinterName = IIf(iniOperate.iniopreate.GetINI("打印机设置", "门诊打印机", "", HisVar.HisVar.Parapath & "\Config.ini") & "" = "", StiRpt.PrinterSettings.PrinterName, iniOperate.iniopreate.GetINI("打印机设置", "门诊打印机", "", HisVar.HisVar.Parapath & "\Config.ini"))


        StiRpt.Compile()
        StiRpt("医疗机构") = HisVar.HisVar.WsyName
        StiRpt("科室") = Rrow.Item("Ks_Name")
        StiRpt("业务流水号") = Rrow.Item("Mz_Code")
        StiRpt("姓名") = Rrow.Item("Ry_Name")
        StiRpt("性别") = Rrow.Item("Ry_Sex")
        StiRpt("医保类型") = Rrow.Item("Bxlb_Name") & ""
        StiRpt("社保号码") = Rrow.Item("Ry_YlCode") & ""
        StiRpt("收款人") = HisVar.HisVar.JsrName
        StiRpt("打印时间") = Format(Now, "yyyy-MM-dd HH:mm:ss")



        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "Select '0' as V_Group, Case Mz_Lb when '西药' then '西药费' else Mz_lb end as Mz_Lb,Null as Mz_Sl,Sum(Mz_Money)Mz_Money  from Mz_Yp where  Mz_Code='" & C1TextBox1.Text & "' group by Mz_lb  Union all Select  Row_Number() over(order by Lb_Name) as V_Group,Lb_Name,Null as Mz_Sl,Sum(Mz_Money)Mz_Money from Mz_Xm,Zd_Jkfl1,Zd_Jkfl2 where Zd_Jkfl1.Lb_Code=Zd_Jkfl2.Lb_Code and Mz_Xm.Xm_Code=Zd_Jkfl2.Mx_Code and  Mz_Code='" & C1TextBox1.Text & "' and Zd_Jkfl2.Yy_Code='" & HisVar.HisVar.WsyCode & "' group by Lb_Name ", "收费明细", True)


        '将门诊打印明细设定为固定个数
        Dim V_Newrow As DataRow

        Dim Tbrowcount As Integer = My_Dataset.Tables("收费明细").Rows.Count
        If Tbrowcount Mod 22 <> 0 Then
            For V_TbRowCount = 1 To 22 - (Tbrowcount Mod 22)
                V_Newrow = My_Dataset.Tables("收费明细").NewRow
                With V_Newrow
                    .Item(0) = DBNull.Value
                    .Item(1) = DBNull.Value
                    .Item(2) = DBNull.Value
                    .Item(3) = DBNull.Value
                End With
                My_Dataset.Tables("收费明细").Rows.Add(V_Newrow)
                V_Newrow.AcceptChanges()
            Next
            My_Dataset.Tables("收费明细").Columns("V_Group").ReadOnly = False
            BaseFunc.BaseFunc.Edit_Col0(My_Dataset.Tables("收费明细"), 0, 22)
        End If


        StiRpt.RegData(My_Dataset.Tables("收费明细"))



        StiRpt.Show()
    End Sub

    Private Sub C1Button3_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Button3.Click
        Dim str As String = "Select Mz_Id as Cf_Id,Ks_Name as Xm_KsName,Xm_Name,Xm_Dw,Mz_Sl as Cf_Sl,Mz_Money as Cf_Money from Zd_Ml_Xm3, Mz_Xm,Zd_Yyks_xm,Zd_YyKs  where Zd_Yyks_xm.Ks_Code=Zd_YyKs.Ks_Code and Mz_Xm.Xm_Code=Zd_Yyks_xm.Xm_Code and Mz_Xm.Xm_Code=Zd_Ml_Xm3.Xm_Code and Mz_Code='" & C1TextBox1.Text & "'"
        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, str, "诊疗卡", True)
        Dim StiRpt As New StiReport
        StiRpt.Load(".\Rpt\诊疗卡.mrt")
        StiRpt.ReportName = "诊疗卡"
        StiRpt.RegData(My_Dataset.Tables("诊疗卡"))
        StiRpt.Compile()
        StiRpt("患者姓名") = Rrow.Item("Ry_Name") & ""
        StiRpt("性别") = Rrow.Item("Ry_Sex") & ""
        StiRpt("年龄") = Rrow.Item("Ry_Age") & ""
        StiRpt("床位") = "门诊"
        StiRpt("处方科室") = Rrow.Item("Ks_Name") & ""
        StiRpt("处方医生") = Rrow.Item("Ys_Name") & ""
        StiRpt("疾病") = Rrow.Item("Jb_Name") & ""
        StiRpt("打印时间") = Format(Now, "yyyy-MM-dd")
        ' StiRpt.Design()
        StiRpt.Show()
    End Sub

    Private Sub C1Button4_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Button4.Click
        Dim StiRpt As New StiReport
        Dim mzcf As ZTHisPublicFunction.OutpatientPrescription.IOutpatientPrescription
        mzcf = ZTHisPublicFunction.OutpatientPrescription.OutpatientPrescriptionFactory.CreateOutpatientPrescriptionObject()
        Dim prescriptionPara As New ZTHisPublicFunction.prescriptionPara()
        prescriptionPara.Mz_Code = C1TextBox1.Text '门诊编号
        prescriptionPara.Bxlb_Name = Rrow("Bxlb_Name") & "" '类别
        prescriptionPara.Jb_Name = Rrow("Jb_Name") & "" '疾病
        prescriptionPara.Ks_Name = C1TextBox3.Text '科室
        prescriptionPara.Mz_Money = Double.Parse(Rrow("Mz_Money"))
        prescriptionPara.Ry_Age = Rrow("Ry_Age") & "" '年龄
        prescriptionPara.Ry_Name = C1TextBox2.Text '患者姓名
        prescriptionPara.Ry_Sex = Rrow("Ry_Sex") & "" '年龄
        prescriptionPara.Ys_Name = C1TextBox5.Text
        prescriptionPara.Ys_Code = Rrow("Ys_Code") & ""
        prescriptionPara.Dp_Name = ZTHisVar.Var.JsrName
        prescriptionPara.Jz_Code = ""
        prescriptionPara.Mz_Date = Rrow("Mz_Date") & ""
        prescriptionPara.Mz_Time = Rrow("Mz_Time") & ""
        prescriptionPara.Ry_Tell = Rrow("Ry_Tell") & ""
        prescriptionPara.Ry_Address = Rrow("Ry_Address") & ""
        prescriptionPara.MzCfZxType = System.[Enum].Parse(GetType(ZTHisEnum.MzCfZxType), Rrow("MzCfZxType"))
        StiRpt = mzcf.Print(prescriptionPara)
        StiRpt.Show()


        'Dim V_ColId As Integer
        'Dim V_TbRowCount As Integer
        'Dim V_Newrow As DataRow

        'If My_Dataset.Tables("处方明细1") IsNot Nothing Then My_Dataset.Tables("处方明细1").Clear()

        'Dim Str As String = "select Mx_Code,Mz_Id,Mx_Gg+'   ×' as Mx_Gg,Mz_Sl,Mx_XsDw,'用法： '+Yp_Yfyl as Yp_Yfyl,Mz_Money,IsJb,Yp_Name from Mz_Yp,V_Ypkc where Mz_Yp.Xx_Code=V_Ypkc.Xx_Code And Mz_Code='" & C1TextBox1.Text & "'  and Dl_Code='01'  Order By Mz_Id"

        'Dim My_Adapter As SqlDataAdapter = New SqlDataAdapter(Str, My_Cn)
        'My_Adapter.Fill(My_Dataset, "处方明细1")



        'If My_Dataset.Tables("处方明细1").Rows.Count Mod 5 <> 0 Then
        '    For V_TbRowCount = 1 To 5 - (My_Dataset.Tables("处方明细1").Rows.Count Mod 5)
        '        V_Newrow = My_Dataset.Tables("处方明细1").NewRow
        '        With V_Newrow

        '            .Item("Mx_Code") = DBNull.Value
        '            .Item("Mz_id") = 2147483647
        '            .Item("Yp_Name") = DBNull.Value
        '            .Item("Mx_Gg") = DBNull.Value
        '            .Item("Mz_Sl") = DBNull.Value
        '            .Item("Mx_XsDw") = DBNull.Value
        '            .Item("Yp_Yfyl") = DBNull.Value
        '            .Item("Mz_Money") = DBNull.Value
        '            '.Item("IsJb1") = DBNull.Value
        '        End With
        '        My_Dataset.Tables("处方明细1").Rows.Add(V_Newrow)
        '        V_Newrow.AcceptChanges()
        '    Next
        'End If
        'V_ColId = BaseFunc.BaseFunc.Edit_Col0(My_Dataset.Tables("处方明细1"), 0, 5)


        ''Str = "select Mx_Code,Mx_Gg+'   ×' as Mx_Gg,sum(Mz_Sl) as Mz_Sl,Mx_XsDw,'用法： '+Yp_Yfyl as Yp_Yfyl,Sum(Mz_Money) as Mz_Money,IsJb,Yp_Name from Mz_Yp,V_Ypkc where Mz_Yp.Xx_Code=V_Ypkc.Xx_Code And Mz_Code='" & C1TextBox1.Text & "'  and Dl_Code='02' Group by Mx_Code,Yp_Name,Mx_Gg,Mx_XsDw,Yp_Yfyl,isjb "
        'Str = "select Mx_Code,Mz_Id,Mx_Gg+'   ×' as Mx_Gg,Mz_Sl,Mx_XsDw,'用法： '+Yp_Yfyl as Yp_Yfyl,Mz_Money,IsJb,Yp_Name from Mz_Yp,V_Ypkc where Mz_Yp.Xx_Code=V_Ypkc.Xx_Code And Mz_Code='" & C1TextBox1.Text & "'  and Dl_Code='02' Order By Mz_Id "

        'My_Adapter = New SqlDataAdapter(Str, My_Cn)
        'My_Adapter.Fill(My_Dataset, "处方明细1")



        'If My_Dataset.Tables("处方明细1").Rows.Count Mod 5 <> 0 Then
        '    For V_TbRowCount = 1 To 5 - (My_Dataset.Tables("处方明细1").Rows.Count Mod 5)
        '        V_Newrow = My_Dataset.Tables("处方明细1").NewRow
        '        With V_Newrow

        '            .Item("Mx_Code") = DBNull.Value
        '            .Item("Mz_id") = 2147483647
        '            .Item("Yp_Name") = DBNull.Value
        '            .Item("Mx_Gg") = DBNull.Value
        '            .Item("Mz_Sl") = DBNull.Value
        '            .Item("Mx_XsDw") = DBNull.Value
        '            .Item("Yp_Yfyl") = DBNull.Value
        '            .Item("Mz_Money") = DBNull.Value

        '        End With
        '        My_Dataset.Tables("处方明细1").Rows.Add(V_Newrow)
        '        V_Newrow.AcceptChanges()
        '    Next
        'End If


        'V_ColId = BaseFunc.BaseFunc.Edit_Col0(My_Dataset.Tables("处方明细1"), V_ColId + 1, 5)


        'Str = "select Mx_Code,Mz_Id,Mx_Gg+'   ×' as Mx_Gg,Mz_Sl,Mx_XsDw,'用法： '+Yp_Yfyl as Yp_Yfyl,Mz_DfSl,Mz_Money,IsJb,Yp_Name from Mz_Yp,V_Ypkc where Mz_Yp.Xx_Code=V_Ypkc.Xx_Code And Mz_Code='" & C1TextBox1.Text & "'  and Dl_Code='03' order by mz_id "

        'HisVar.HisVar.Sqldal.QueryDt(My_Dataset, Str, "中药处方", True)

        ''中药处方单张方上药品数量无限制，尽可能多
        'If My_Dataset.Tables("中药处方").Rows.Count Mod 40 <> 0 Then
        '    For V_TbRowCount = 1 To 40 - (My_Dataset.Tables("中药处方").Rows.Count Mod 40)
        '        V_Newrow = My_Dataset.Tables("中药处方").NewRow
        '        With V_Newrow

        '            .Item("Mx_Code") = V_TbRowCount
        '            .Item("Mz_id") = 2147483500 + V_TbRowCount
        '            .Item("Yp_Name") = DBNull.Value
        '            .Item("Mx_Gg") = DBNull.Value
        '            .Item("Mz_Sl") = DBNull.Value
        '            .Item("Mx_XsDw") = DBNull.Value
        '            .Item("Yp_Yfyl") = DBNull.Value
        '            .Item("Mz_Money") = DBNull.Value

        '        End With
        '        My_Dataset.Tables("中药处方").Rows.Add(V_Newrow)
        '        V_Newrow.AcceptChanges()
        '    Next
        'End If

        'V_ColId = BaseFunc.BaseFunc.Edit_Col0(My_Dataset.Tables("中药处方"), 0, 40)


        'Str = "select Mx_Code,Mz_Id,Mx_Gg+'   ×' as Mx_Gg,Mz_Sl,Mx_XsDw, Yp_Yfyl,Mz_Money, Yp_Name from Mz_Yp,V_Ypkc where Mz_Yp.Xx_Code=V_Ypkc.Xx_Code And Mz_Code='" & C1TextBox1.Text & "'  and Dl_Code='04'  order by mz_id  "

        'HisVar.HisVar.Sqldal.QueryDt(My_Dataset, Str, "卫材处方", True)
        'If My_Dataset.Tables("卫材处方").Rows.Count Mod 20 <> 0 Then
        '    For V_TbRowCount = 1 To 20 - (My_Dataset.Tables("卫材处方").Rows.Count Mod 20)
        '        V_Newrow = My_Dataset.Tables("卫材处方").NewRow
        '        With V_Newrow

        '            .Item("Mx_Code") = V_TbRowCount
        '            .Item("Mz_id") = 2147483500 + V_TbRowCount
        '            .Item("Yp_Name") = DBNull.Value
        '            .Item("Mx_Gg") = DBNull.Value
        '            .Item("Mz_Sl") = DBNull.Value
        '            .Item("Mx_XsDw") = DBNull.Value
        '            .Item("Yp_Yfyl") = DBNull.Value
        '            .Item("Mz_Money") = DBNull.Value

        '        End With
        '        My_Dataset.Tables("卫材处方").Rows.Add(V_Newrow)
        '        V_Newrow.AcceptChanges()
        '    Next
        'End If
        'V_ColId = BaseFunc.BaseFunc.Edit_Col0(My_Dataset.Tables("卫材处方"), 0, 20)


        'Str = " select Mz_Xm.Xm_Code as Mx_Code,Mz_Id,'' as Mx_Gg,Mz_Sl,Xm_Dw as Mx_XsDw, '' Yp_Yfyl,Mz_Money,Xm_Name as  Yp_Name from Mz_Xm,Zd_Ml_Xm3 where Mz_Xm.Xm_Code=Zd_Ml_Xm3.Xm_Code And Mz_Code='" & C1TextBox1.Text & "'   order by mz_id "

        'HisVar.HisVar.Sqldal.QueryDt(My_Dataset, Str, "诊疗处方", True)

        'If My_Dataset.Tables("诊疗处方").Rows.Count Mod 20 <> 0 Then
        '    For V_TbRowCount = 1 To 20 - (My_Dataset.Tables("诊疗处方").Rows.Count Mod 20)
        '        V_Newrow = My_Dataset.Tables("诊疗处方").NewRow
        '        With V_Newrow

        '            .Item("Mx_Code") = V_TbRowCount
        '            .Item("Mz_id") = 2147483500 + V_TbRowCount
        '            .Item("Yp_Name") = DBNull.Value
        '            .Item("Mx_Gg") = DBNull.Value
        '            .Item("Mz_Sl") = DBNull.Value
        '            .Item("Mx_XsDw") = DBNull.Value
        '            .Item("Yp_Yfyl") = DBNull.Value
        '            .Item("Mz_Money") = DBNull.Value

        '        End With
        '        My_Dataset.Tables("诊疗处方").Rows.Add(V_Newrow)
        '        V_Newrow.AcceptChanges()
        '    Next
        'End If
        'V_ColId = BaseFunc.BaseFunc.Edit_Col0(My_Dataset.Tables("诊疗处方"), 0, 20)

        'Dim StiRpt As New StiReport
        'StiRpt.Load(".\Rpt\门诊处方表(二分之一A4).mrt")
        '' StiRpt.Load(".\Rpt\门诊处方表.mrt")
        'StiRpt.ReportName = "门诊处方表"
        'StiRpt.RegData(My_Dataset.Tables("处方明细1"))
        'StiRpt.RegData(My_Dataset.Tables("中药处方"))
        'StiRpt.RegData(My_Dataset.Tables("卫材处方"))
        'StiRpt.RegData(My_Dataset.Tables("诊疗处方"))

        'If My_Dataset.Tables("处方明细1").Rows.Count > 0 Then
        '    StiRpt.Pages(0).Enabled = True
        'Else
        '    StiRpt.Pages(0).Enabled = False
        'End If

        'If My_Dataset.Tables("中药处方").Rows.Count > 0 Then
        '    StiRpt.Pages(1).Enabled = True
        'Else
        '    StiRpt.Pages(1).Enabled = False
        'End If

        'If My_Dataset.Tables("卫材处方").Rows.Count > 0 Then
        '    StiRpt.Pages(2).Enabled = True
        'Else
        '    StiRpt.Pages(2).Enabled = False
        'End If

        'If My_Dataset.Tables("诊疗处方").Rows.Count > 0 Then
        '    StiRpt.Pages(3).Enabled = True
        'Else
        '    StiRpt.Pages(3).Enabled = False
        'End If

        'StiRpt.Compile()
        'StiRpt("标题") = HisVar.HisVar.WsyName & "门诊处方笺"
        'StiRpt("门诊编号") = C1TextBox1.Text '门诊编号
        'StiRpt("打印时间") = Format(Now, "yyyy年MM月dd日")
        'StiRpt("患者姓名") = C1TextBox2.Text '患者姓名
        'StiRpt("患者性别") = C1TextBox4.Text '患者性别
        'StiRpt("年龄") = Rrow("Ry_Age") & "" '年龄
        'StiRpt("类别") = Rrow("Bxlb_Name") & "" '类别
        'If My_Dataset.Tables("中药处方").Rows.Count > 0 Then

        '    Dim YfYl As String = _bllMz_Yp.getYfyl(C1TextBox1.Text)
        '    StiRpt("用法用量") = YfYl
        'End If
        'StiRpt("科室") = C1TextBox3.Text '科室
        'StiRpt("疾病") = Rrow("Jb_Name") & "" '疾病
        'StiRpt("医生姓名") = C1TextBox5.Text
        'StiRpt("金额") = Format(CDbl(Rrow("Mz_Money")), "#0.00")
        'Dim My_Reader As SqlDataReader = HisVar.HisVar.Sqldal.ExecuteReader("Select Jsr_Name from Mz,Zd_Yyjsr where Fy_JsrCode=Zd_Yyjsr.Jsr_Code and Mz_Code='" & C1TextBox1.Text & "'")
        'While My_Reader.Read
        '    StiRpt("经手人") = My_Reader.Item("Jsr_Name")
        'End While
        'My_Reader.Close()
        'Call P_Conn(False)

        ''StiRpt.Design()
        'StiRpt.Show()
    End Sub

    Private Sub C1TrueDBGrid1_Click(sender As Object, e As EventArgs) Handles C1TrueDBGrid1.MouseDoubleClick
        Dim Zb_Row As DataRow
        Zb_Row = Zb_Cm.List(C1TrueDBGrid1.Row).Row
        If Zb_Row("Xm_Wc").ToString = "否" Then
            Return
        End If
        Dim LisResult As New ZTHisLis.LisResult(Zb_Row("Xm_Code").ToString, Zb_Row("Mz_Code").ToString)
        LisResult.ShowDialog()
    End Sub

    Private Sub BtnPrintSqd_Click(sender As Object, e As EventArgs) Handles BtnPrintSqd.Click
        Dim print As New ZTHisLis.Print
        print.PrintMzYjsqd(Rrow("Mz_Code"))
    End Sub
End Class