ALTER TABLE dbo.Zd_Ml_Yp3 DROP CONSTRAINT FK_Zd_Ml_Yp3_Zd_Ml_Ypjx;
ALTER TABLE dbo.Zd_Ml_Ypjx DROP CONSTRAINT PK_Zd_Ml_Ypjx;
ALTER TABLE dbo.Zd_Ml_Ypjx ALTER COLUMN Jx_Code CHAR(3) NOT NULL;
ALTER TABLE dbo.Zd_Ml_Yp3 ALTER COLUMN Jx_Code CHAR(3);
UPDATE dbo.Zd_Ml_Ypjx SET Jx_Code='0'+Jx_Code;
UPDATE dbo.Zd_Ml_Yp3 SET Jx_Code='0'+Jx_Code;
ALTER TABLE dbo.Zd_Ml_Ypjx
ADD CONSTRAINT PK_Zd_Ml_Ypjx PRIMARY KEY CLUSTERED(Jx_Code)WITH(STATISTICS_NORECOMPUTE=OFF, IGNORE_DUP_KEY=OFF, ALLOW_ROW_LOCKS=ON, ALLOW_PAGE_LOCKS=ON)ON [PRIMARY];
ALTER TABLE dbo.Zd_Ml_Yp3
ADD CONSTRAINT FK_Zd_Ml_Yp3_Zd_Ml_Ypjx FOREIGN KEY(Jx_Code)REFERENCES dbo.Zd_Ml_Ypjx(Jx_Code)ON UPDATE CASCADE ON DELETE CASCADE;

