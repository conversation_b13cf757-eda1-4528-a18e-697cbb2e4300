﻿Imports System.Data.SqlClient
Imports Common.Delegate
Imports HisControl

Public Class Cq_Cf2

#Region "定义__变量"
    Dim My_Adapter As New SqlDataAdapter                '从表适配器
    Dim My_Table As New DataTable                       '从表
    Dim My_Reader1 As SqlClient.SqlDataReader

    Dim V_GridCol As Integer                            '当前TDBGrid所在的列

    Dim V_Sum As Double                                 '金额小计
    Dim Zb_Sum As Double
    Dim Zb_XmSum As Double
    Dim Zb_YpSum As Double

    Public Cb_Cm As CurrencyManager                     '同步指针
    Public Cb_Row As DataRow                            '当前选择行
    Public V_Cf_Code As String                          '出库编码
    Public V_Insert As Boolean                          '增加记录

    Public Ck3_FirstLoad As Boolean                     '第一次调入明细表

    Dim Str_Select As String
    Public Yp_Lb As String
    Dim My_Dataset As New DataSet

    Public V_Yf_Sl As String
    Public V_Yf_Lsj As String
    Public V_Flag As String  '如果只有诊疗则自动发药
    Dim V_Cf_Print As String '请求发药
    Private _transmitTxt As Common.Delegate.TransmitTxt = New TransmitTxt()
#End Region

#Region "传参"
    Dim Rform As BaseForm
    Dim Rinsert As Boolean
    Dim Rrow As DataRow
    Dim RZbtb As DataTable
    Dim Rtdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Dim Rlb As C1.Win.C1Input.C1Label
    Dim Rzbadt As SqlDataAdapter
    'Dim Rdataset As DataSet
    Dim Rlx As String
#End Region

    Public Sub New(ByVal tform As BaseForm, ByVal tinsert As Boolean, ByVal trow As DataRow, ByVal tZbtb As DataTable, ByVal ttdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid, ByVal tlb As C1.Win.C1Input.C1Label, ByVal tzbadt As SqlDataAdapter, ByVal tdataset As DataSet, ByVal tlx As String)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        Rform = tform
        Rinsert = tinsert
        Rrow = trow
        RZbtb = tZbtb
        Rtdbgrid = ttdbgrid
        Rlb = tlb
        Rzbadt = tzbadt
        My_Dataset = tdataset
        Rlx = tlx
        ' 在 InitializeComponent() 调用之后添加任何初始化。

    End Sub

    Protected Overrides Sub OnKeyDown(ByVal e As System.Windows.Forms.KeyEventArgs)
        MyBase.OnKeyDown(e)
        If e.Control = True And e.KeyCode = Keys.S Then
            Comm1.Select()
            Call Comm_Click(Comm1, Nothing)
        End If
    End Sub

    Private Sub Cq_Cf_Yp_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        'Rform.Visible = False
        RadioButton1.Checked = True
        Call Init_Data()
        Call Form_Init() '窗体初始化

        YfComobo1.SelectedValue = iniOperate.iniopreate.GetINI("Personal", "默认药房", "", HisVar.HisVar.Parapath & "\Config.Ini") & ""

        AddHandler Me._transmitTxt.SetText, AddressOf GridMove
        If Rinsert = True Then           '主表
            Call Zb_Clear()                 '清空数据
        Else
            Call Zb_Show()                  '显示数据
        End If

    End Sub

#Region "窗体__事件"

    Private Sub Form_Init()
        Panel2.Height = 30

        '按扭初始化
        Call P_Comm(Me.Comm1)
        Call P_Comm(Me.Comm2)
        Call P_Comm(Me.comm4)
        C1NumericEdit1.Enabled = False
        C1Command1.Enabled = False
        C1Command2.Enabled = False
        StartDateEdit.CustomFormat = "yyyy-MM-dd HH:mm"
        StartDateEdit.DisplayFormat = "yyyy-MM-dd HH:mm"
        StartDateEdit.EditFormat = "yyyy-MM-dd HH:mm"
        EndDateEdit.CustomFormat = "yyyy-MM-dd HH:mm"
        EndDateEdit.DisplayFormat = "yyyy-MM-dd HH:mm"
        EndDateEdit.EditFormat = "yyyy-MM-dd HH:mm"
        FirstDateEdit.CustomFormat = "HH:mm"
        FirstDateEdit.DisplayFormat = "HH:mm"
        FirstDateEdit.EditFormat = "HH:mm"
        TimesNumEdit.CustomFormat = "0"
        TimesNumEdit.Value = 1

        With KsCombo
            .DataView = DAL.DAL_Dict.GetDepartmentDict.DefaultView
            .Init_Colum("Ks_Jc", "简称", 84, "左")
            .Init_Colum("Ks_Name", "名称", 100, "左")
            .Init_Colum("Ks_Code", "编码", 0, "左")
            .DisplayMember = "Ks_Name"
            .ValueMember = "Ks_Code"
            .DroupDownWidth = 400
            .MaxDropDownItems = 15
            .SelectedIndex = -1
            .RowFilterNotTextNull = "Ks_Jc"
            .RowFilterTextNull = ""
        End With

        With DoctorCombo
            .DataView = DAL.DAL_Dict.GetDoctorDict.DefaultView
            .Init_Colum("Ys_Jc", "简称", 84, "左")
            .Init_Colum("Ys_Name", "姓名", 100, "左")
            .Init_Colum("Ks_Name", "科室", 100, "左")
            .Init_Colum("Ys_Code", "编码", 0, "左")
            .Init_Colum("Ks_Code", "科室编码", 0, "左")
            .DisplayMember = "Ys_Name"
            .ValueMember = "Ys_Code"
            .DroupDownWidth = 400
            .MaxDropDownItems = 15
            .SelectedIndex = -1
            .RowFilterNotTextNull = "Ys_Jc"
            .RowFilterTextNull = ""
        End With

        If HisPara.PublicConfig.ZyCfKsXz = "是" Then
            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "SELECT Bl_Code,Ry_Jc,Ry_Name,Ry_Sex,Jb_Name,Bc_Name,Ys_Code,Bl.Ks_Code,Bxlb_Name FROM Bl,Zd_YyBc,Zd_Bxlb Where Bl.Bxlb_Code=Zd_Bxlb.Bxlb_Code and Bl.Bc_Code=Zd_YyBc.Bc_Code  And Isnull(Ry_CyDate,'1900-01-01')='1900-01-01' and Bl.Ks_Code like '%" & HisVar.HisVar.XmKs & "%'  Order By Ry_Jc", "患者字典", True)
        Else
            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "SELECT Bl_Code,Ry_Jc,Ry_Name,Ry_Sex,Jb_Name,Bc_Name,Ys_Code,Bl.Ks_Code,Bxlb_Name FROM Bl,Zd_YyBc,Zd_Bxlb Where Bl.Bxlb_Code=Zd_Bxlb.Bxlb_Code and Bl.Bc_Code=Zd_YyBc.Bc_Code  And Isnull(Ry_CyDate,'1900-01-01')='1900-01-01'  Order By Ry_Jc", "患者字典", True)
        End If


        Dim My_Combo3 As New BaseClass.C_Combo2(Me.C1Combo3, My_Dataset.Tables("患者字典").DefaultView, "Ry_Name", "Bl_Code", 560)
        With My_Combo3
            .Init_TDBCombo()
            .Init_Colum("Ry_Jc", "患者简称", 70, "左")
            .Init_Colum("Bl_Code", "患者编码", 0, "左")
            .Init_Colum("Ry_Name", "患者姓名", 80, "左")
            .Init_Colum("Ry_Sex", "性别", 50, "中")
            .Init_Colum("Jb_Name", "疾病名称", 150, "左")
            .Init_Colum("Bc_Name", "床位", 100, "中")

            .Init_Colum("Ks_Code", "", 0, "左")
            .Init_Colum("Ys_Code", "", 0, "左")
            .Init_Colum("Bxlb_Name", "患者类别", 70, "左")
            .MaxDropDownItems(17)
            .SelectedIndex(-1)
        End With
        C1Combo3.AutoCompletion = False
        C1Combo3.AutoSelect = False


        With YfComobo1
            .DataView = DAL.DAL_Dict.GetYfDict.DefaultView
            .Init_Colum("Yf_Jc", "药房简称", 100, "左")
            .Init_Colum("Yf_Code", "药房编码", 0, "左")
            .Init_Colum("Yf_Name", "药房名称", 100, "左")
            .DisplayMember = "Yf_Name"
            .ValueMember = "Yf_Code"
            .DroupDownWidth = 400
            .MaxDropDownItems = 15
            .SelectedIndex = -1
            .RowFilterNotTextNull = "Yf_Jc"
            .RowFilterTextNull = ""
        End With

        If HisPara.PublicConfig.ZyYsz = "是" And HisVar.HisVar.JsrYsCode & "" <> "" Then
            DoctorCombo.SelectedValue = HisVar.HisVar.JsrYsCode
            DoctorCombo.Enabled = False
        End If

    End Sub

    Private Sub Cq_Cf_Yp_FormClosing(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosingEventArgs) Handles Me.FormClosing
        If e.CloseReason = CloseReason.UserClosing Then
            If Rinsert = False Then Call Zb_Save() '更新主表(主表已经保存过,取消操作)
        End If
        'Rform.Visible = True
    End Sub

#End Region

#Region "清空__显示"

    Private Sub Zb_Clear()
        V_Sum = 0

        '新增记录
        V_Cf_Code = F_MaxCode(Format(Now, "yyMMdd"))       '出库编码


        C1Combo3.Text = ""
        Zb_Sum = 0
        Zb_XmSum = 0
        Zb_YpSum = 0

        Label10.Text = ""
        Label17.Text = ""

        Label20.Text = ""
        C1NumericEdit1.Value = 1
        '备注
        Label12.Text = V_Cf_Code                                                '客户编码
        C1Combo3.SelectedIndex = -1

        If RadioButton1.Checked = True Then
            Yp_Lb = Mid(RadioButton1.Text, 4)
            C1TrueDBGrid_Init_Zxy()
            C1Command1.Enabled = True
            C1Command2.Enabled = True
            C1NumericEdit1.Enabled = True
        End If
        If RadioButton4.Checked = True Then
            Yp_Lb = Mid(RadioButton1.Text, 4)
            C1TrueDBGrid_Init_Xm()
            C1Command1.Enabled = False
            C1Command2.Enabled = False
            C1NumericEdit1.Enabled = False
        End If
        Call P_Data_Show()

        If My_Dataset.Tables("药品卫材") IsNot Nothing Then
            My_Dataset.Tables("药品卫材").Clear()
        End If
        If My_Dataset.Tables("诊疗项目") IsNot Nothing Then
            My_Dataset.Tables("诊疗项目").Clear()
        End If
        C1Combo3.Select()
    End Sub

    Private Sub Zb_Show()   '显示记录
        V_Sum = 0

        With Rrow
            V_Cf_Code = .Item("AutoCf_Code") & ""                                   '出库编码
            Label12.Text = V_Cf_Code

            C1Combo3.SelectedValue = .Item("Bl_Code") & ""
            DoctorCombo.SelectedValue = .Item("Ys_Code") & ""
            KsCombo.SelectedValue = .Item("Ks_Code") & ""
            YfComobo1.SelectedValue = .Item("Yf_Code") & ""
            FirstDateEdit.Value = .Item("First_Execut_Time")
            IntervalNumEdit.Value = .Item("Remind_interval")
            TimesNumEdit.Value = .Item("Everyday_Times")
            StartDateEdit.Value = .Item("Start_Date")
            EndDateEdit.Value = .Item("End_Date")
        End With

        C1NumericEdit1.Value = 1

        If RadioButton1.Checked = True Then
            Yp_Lb = Mid(RadioButton1.Text, 4)
            C1TrueDBGrid_Init_Zxy()
            C1Command1.Enabled = True
            C1Command2.Enabled = True
            C1NumericEdit1.Enabled = True
        End If

        If RadioButton4.Checked = True Then
            Yp_Lb = Mid(RadioButton4.Text, 4)
            C1TrueDBGrid_Init_Xm()
            C1Command1.Enabled = False
            C1Command2.Enabled = False
            C1NumericEdit1.Enabled = False
        End If

        '出库编码

        Call P_Data_Show()

    End Sub

    Private Sub P_Data_Show()   '从表数据
        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "Select Auto_Cfxm.*,Xm_Dw,Xm_Name From Auto_Cfxm,Zd_Ml_Xm3 Where  Auto_Cfxm.Xm_Code=Zd_Ml_Xm3.Xm_Code And AutoCf_Code='" & V_Cf_Code & "' Order By Cf_Id", "诊疗项目", True)
        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "Select Auto_Cfyp.*,Mx_Gg,Mx_Cd,Yp_Name,Mx_XsDw,V_YpKc.Yp_Ph,V_YpKc.Yp_Yxq From Auto_Cfyp,V_YpKc Where Auto_Cfyp.Xx_Code=V_YpKc.Xx_Code And AutoCf_Code='" & V_Cf_Code & "' Order By Cf_Id", "药品卫材", True)
        If Yp_Lb = "诊疗项目" Then
            My_Table = My_Dataset.Tables("诊疗项目")
        ElseIf Yp_Lb = "药品卫材" Then
            My_Table = My_Dataset.Tables("药品卫材")
        End If


        My_Table.PrimaryKey = New DataColumn() {My_Table.Columns("Cf_Id")}

        '列的唯一性
        Dim My_Column As DataColumn = My_Table.Columns("Cf_Id")
        With My_Column
            .AutoIncrement = True
            .AutoIncrementSeed = .AutoIncrementSeed
            .AutoIncrementStep = 1
        End With

        '主表记录
        Cb_Cm = CType(BindingContext(My_Dataset, My_Table.TableName), CurrencyManager)
        C1TrueDBGrid1.SetDataBinding(My_Dataset, My_Table.TableName, True)


        Call P_SumMoney()


    End Sub

#End Region

#Region "主表__编辑"

    Private Sub Zb_Save()                       '主表保存

        If Rinsert = True Then     '增加记录
            Call Zb_Add()
        Else                                '编辑记录
            Call Zb_Edit()
        End If
        Rform.F_Sum()
    End Sub

    Private Sub Zb_Add()    '增加记录
        If RadioButton1.Checked = True Then
            Yp_Lb = Mid(RadioButton1.Text, 4)
        End If
        Dim My_Tb As DataTable = My_Dataset.Tables("主表")
        Dim My_NewRow As DataRow = My_Tb.NewRow
        With My_NewRow

            .Item("Yy_Code") = HisVar.HisVar.WsyCode
            .Item("AutoCf_Code") = F_MaxCode(Format(Now, "yyMMdd"))        '出库编码
            V_Cf_Code = .Item("AutoCf_Code")
            Label12.Text = V_Cf_Code
            .Item("Yf_Code") = YfComobo1.SelectedValue
            .Item("Bl_Code") = C1Combo3.SelectedValue
            .Item("Ks_Code") = KsCombo.SelectedValue
            .Item("Ys_Code") = DoctorCombo.SelectedValue
            .Item("Jsr_Code") = HisVar.HisVar.JsrCode
            .Item("Jsr_Name") = HisVar.HisVar.JsrName
            .Item("Cf_Date") = Format(Now, "yyyy-MM-dd")               '入库日期
            .Item("Cf_Time") = Format(Now, "HH:mm:ss")
            .Item("Cf_Memo") = ""
            .Item("Cf_YpMoney") = Format(IIf(My_Dataset.Tables("药品卫材").Compute("Sum(Cf_Money)", "") Is DBNull.Value, 0, My_Dataset.Tables("药品卫材").Compute("Sum(Cf_Money)", "")), "###,###,##0.00##")
            .Item("Cf_Money") = Format(IIf(My_Dataset.Tables("药品卫材").Compute("Sum(Cf_Money)", "") Is DBNull.Value, 0, My_Dataset.Tables("药品卫材").Compute("Sum(Cf_Money)", "")) + IIf(My_Dataset.Tables("诊疗项目").Compute("Sum(Cf_Money)", "") Is DBNull.Value, 0, My_Dataset.Tables("诊疗项目").Compute("Sum(Cf_Money)", "")), "###,###,##0.00##")
            .Item("Start_Date") = StartDateEdit.Value
            .Item("End_Date") = EndDateEdit.Value
            .Item("Everyday_Times") = TimesNumEdit.Value
            .Item("Remind_interval") = IntervalNumEdit.Value
            .Item("First_Execut_Time") = Format(FirstDateEdit.Value, "HH:mm")


            .Item("Auto_Zt") = "未开始"

            .Item("Ry_Name") = C1Combo3.Text
            .Item("Ks_Name") = KsCombo.Text
            .Item("Ys_Name") = DoctorCombo.Text
            .Item("Bxlb_Name") = C1Combo3.Columns("Bxlb_Name").Value

        End With
        Try
            My_Tb.Rows.Add(My_NewRow)
            Rrow = My_NewRow
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            C1Combo3.Select()
            Exit Sub
        End Try

        '显示增加后的状态
        Rtdbgrid.MoveLast()
        Rlb.Text = "∑=" + Rtdbgrid.Splits(0).Rows.Count.ToString


        '更新主表
        Call Zb_Update("增加", My_NewRow)

    End Sub

    Private Sub Zb_Edit()   '编辑记录
        If RadioButton1.Checked = True Then
            Yp_Lb = Mid(RadioButton1.Text, 4)
        End If
        Try
            With Rrow
                .BeginEdit()
                .Item("Yf_Code") = YfComobo1.SelectedValue
                .Item("Bl_Code") = C1Combo3.SelectedValue
                .Item("Ks_Code") = KsCombo.SelectedValue
                .Item("Ys_Code") = DoctorCombo.SelectedValue
                .Item("Jsr_Code") = HisVar.HisVar.JsrCode
                .Item("Jsr_Name") = HisVar.HisVar.JsrName
                .Item("Cf_Date") = Format(Now, "yyyy-MM-dd")               '入库日期
                .Item("Cf_Time") = Format(Now, "HH:mm:ss")
                .Item("Cf_Memo") = ""
                .Item("Cf_YpMoney") = Format(IIf(My_Dataset.Tables("药品卫材").Compute("Sum(Cf_Money)", "") Is DBNull.Value, 0, My_Dataset.Tables("药品卫材").Compute("Sum(Cf_Money)", "")), "###,###,##0.00##")
                .Item("Cf_Money") = Format(IIf(My_Dataset.Tables("药品卫材").Compute("Sum(Cf_Money)", "") Is DBNull.Value, 0, My_Dataset.Tables("药品卫材").Compute("Sum(Cf_Money)", "")) + IIf(My_Dataset.Tables("诊疗项目").Compute("Sum(Cf_Money)", "") Is DBNull.Value, 0, My_Dataset.Tables("诊疗项目").Compute("Sum(Cf_Money)", "")), "###,###,##0.00##")
                .Item("Start_Date") = StartDateEdit.Value
                .Item("End_Date") = EndDateEdit.Value
                .Item("Everyday_Times") = TimesNumEdit.Value
                .Item("Remind_interval") = IntervalNumEdit.Value
                .Item("First_Execut_Time") = Format(FirstDateEdit.Value, "HH:mm")

                .Item("Auto_Zt") = "未开始"

                .Item("Ry_Name") = C1Combo3.Text
                .Item("Ks_Name") = KsCombo.Text
                .Item("Ys_Name") = DoctorCombo.Text
                .Item("Bxlb_Name") = C1Combo3.Columns("Bxlb_Name").Value
                .EndEdit()
            End With

        Catch ex As Exception
            Beep()
            Rrow.CancelEdit()
            MsgBox(ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
            Exit Sub
        End Try

        '更新主表
        Call Zb_Update("保存", Rrow)


    End Sub

    Private Sub Zb_Update(ByVal V_Lb As String, ByVal V_Row As DataRow)     '更新主表
        Call P_Conn(True)

        If V_Lb = "增加" Then                                               '增加主表
            With Rzbadt.InsertCommand
                .Parameters(0).Value = V_Row.Item("Yy_Code")
                .Parameters(1).Value = V_Row.Item("AutoCf_Code")
                .Parameters(2).Value = V_Row.Item("Yf_Code")
                .Parameters(3).Value = V_Row.Item("Bl_Code")
                .Parameters(4).Value = V_Row.Item("Ks_Code")
                .Parameters(5).Value = V_Row.Item("Ys_Code")
                .Parameters(6).Value = V_Row.Item("Jsr_Code")
                .Parameters(7).Value = V_Row.Item("Cf_Date")
                .Parameters(8).Value = V_Row.Item("Cf_Time")
                .Parameters(9).Value = V_Row.Item("Cf_Memo")
                .Parameters(10).Value = V_Row.Item("Cf_Money")
                .Parameters(11).Value = V_Row.Item("Cf_YpMoney")
                .Parameters(12).Value = V_Row.Item("Start_Date")
                .Parameters(13).Value = V_Row.Item("End_Date")
                .Parameters(14).Value = V_Row.Item("Auto_Zt")
                .Parameters(15).Value = V_Row.Item("Everyday_Times")
                .Parameters(16).Value = V_Row.Item("Remind_interval")
                .Parameters(17).Value = V_Row.Item("First_Execut_Time")
                Try
                    .ExecuteNonQuery()
                    Rrow.AcceptChanges()
                    Rinsert = False                                '不充许增加

                Catch ex As Exception
                    Beep()
                    MsgBox(ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
                Finally
                    Call P_Conn(False)
                End Try
            End With
        Else                                                                '编辑主表
            With Rzbadt.UpdateCommand

                .Parameters(0).Value = V_Row.Item("Yf_Code")
                .Parameters(1).Value = V_Row.Item("Bl_Code")
                .Parameters(2).Value = V_Row.Item("Ks_Code")
                .Parameters(3).Value = V_Row.Item("Ys_Code")
                .Parameters(4).Value = V_Row.Item("Jsr_Code")
                .Parameters(5).Value = V_Row.Item("Cf_Date")
                .Parameters(6).Value = V_Row.Item("Cf_Time")
                .Parameters(7).Value = V_Row.Item("Cf_Memo")
                .Parameters(8).Value = V_Row.Item("Cf_Money")
                .Parameters(9).Value = V_Row.Item("Cf_YpMoney")
                .Parameters(10).Value = V_Row.Item("Start_Date")
                .Parameters(11).Value = V_Row.Item("End_Date")
                .Parameters(12).Value = V_Row.Item("Auto_Zt")
                .Parameters(13).Value = V_Row.Item("Everyday_Times")
                .Parameters(14).Value = V_Row.Item("Remind_interval")
                .Parameters(15).Value = V_Row.Item("First_Execut_Time")
                .Parameters(16).Value = V_Row.Item("AutoCf_Code", DataRowVersion.Original)
                Try
                    .ExecuteNonQuery()
                    Rrow.AcceptChanges()
                Catch ex As Exception
                    Beep()
                    MsgBox(ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
                Finally
                    Call P_Conn(False)
                End Try
            End With
        End If
        C1TrueDBGrid1.Select()
        Me.Text = "医嘱明细-" & Rrow("AutoCf_Code")
    End Sub

#End Region


#Region "其它__控件"


    Private Sub Comm_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.Click, Comm2.Click, comm4.Click
        Select Case sender.tag

            Case "保存", "处置单"

                If C1Combo3.SelectedValue = "" Then
                    Beep()
                    MsgBox("患者不能为空!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    C1Combo3.Select()
                    Exit Sub
                End If

                If DoctorCombo.SelectedValue = "" Then
                    Beep()
                    MsgBox("医生姓名不能为空!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    DoctorCombo.Select()
                    Exit Sub
                End If
                If KsCombo.SelectedValue = "" Then
                    Beep()
                    MsgBox("科室名称不能为空!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    KsCombo.Select()
                    Exit Sub
                End If

                If YfComobo1.SelectedValue = "" Then
                    Beep()
                    MsgBox("药房名称不能为空!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    YfComobo1.Select()
                    Exit Sub
                End If

                If TimesNumEdit.Value < 1 Then
                    MsgBox("执行频率填写有误！", MsgBoxStyle.Information, "提示")
                    TimesNumEdit.Value = 1
                    Exit Sub
                End If

                If IntervalNumEdit.Value < 0 Or (TimesNumEdit.Value = 1 And IntervalNumEdit.Value > 0) Then
                    MsgBox("执行间隔填写有误！", MsgBoxStyle.Information, "提示")
                    IntervalNumEdit.Value = 0
                    Exit Sub
                End If

                If TimesNumEdit.Value > 1 And IntervalNumEdit.Value = 0 Then
                    MsgBox("执行间隔填写有误！", MsgBoxStyle.Information, "提示")
                    TimesNumEdit.Value = 1
                    Exit Sub
                End If

                If DateAdd(DateInterval.Hour, IntervalNumEdit.Value * (TimesNumEdit.Value - 1), FirstDateEdit.Value) > Format(Now, "yyyy-MM-dd 23:59:59") Then
                    MsgBox("超出24点时间范围，请调整执行频率与执行间隔！", MsgBoxStyle.Information, "提示：")
                    TimesNumEdit.Value = 1
                    TimesNumEdit.Select()
                    Exit Sub
                End If

                Call Zb_Save()                          '主表存盘

                If sender.tag = "处置单" Then
                    Dim vform As New 护士站.Hsz_Syk(Rrow, "长期医嘱")
                    If BaseFunc.BaseFunc.CheckOwnForm(Me, vform) = False Then
                        vform.ShowDialog()
                    End If
                End If

            Case "取消"
                Me.Close()


        End Select

    End Sub


#End Region

#Region "Combo2动作"


    Private Sub DoctorCombo_RowChange(ByVal sender As Object, ByVal e As System.EventArgs) Handles DoctorCombo.RowChange
        If DoctorCombo.WillChangeToValue = "" Then
        Else
            KsCombo.SelectedValue = DoctorCombo.Columns("Ks_Code").Value
        End If
    End Sub



#Region "Combo3动作"

    Private Sub C1Combo3_RowChange(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1Combo3.RowChange
        If C1Combo3.WillChangeToValue = "" Then

            Label10.Text = ""
            Label20.Text = ""
            Label17.Text = ""
            DoctorCombo.SelectedValue = DBNull.Value
        Else
            Label10.Text = Trim(C1Combo3.Columns("Ry_Sex").Value & "")
            Label20.Text = Trim(C1Combo3.Columns("Jb_Name").Value & "")
            Label17.Text = Trim(C1Combo3.Columns("Bc_Name").Value & "")

            If HisPara.PublicConfig.ZyYsz = "否" Or HisVar.HisVar.JsrYsCode & "" = "" Then
                DoctorCombo.SelectedValue = C1Combo3.Columns("Ys_Code").Value
            End If

            'My_Reader1 = F_Reader("Select (Select Isnull(Sum(Jf_Money),0) From Bl_Jf Where Bl_Code=Bl.Bl_Code)-(Select Isnull(Sum(Bl_Cfyp.Cf_Money),0) From Bl_Cfyp,Bl_Cf Where Bl_Cfyp.Cf_Code=Bl_Cf.Cf_Code And Bl_Code=Bl.Bl_Code)-(Select Isnull(Sum(Bl_Cfxm.Cf_Money),0) From Bl_Cfxm,Bl_Cf Where Bl_Cfxm.Cf_Code=Bl_Cf.Cf_Code And Bl_Code=Bl.Bl_Code) New_Money FROM Bl where Bl_Code='" & C1Combo3.SelectedValue & "'")
            'My_Reader1.Read()
            'If My_Reader1.HasRows = True Then
            '    Label24.Text = Format(My_Reader1.Item("New_Money"), "0.00")
            'End If
            'Call P_Conn(False)
            'My_Reader1.Close()

            'If Label24.Text <= 0 Then
            '    Label24.ForeColor = Color.Red
            'Else
            '    Label24.ForeColor = Color.Black
            'End If

        End If
    End Sub

    Private Sub C1Combo3_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles C1Combo3.KeyUp

        If (e.KeyValue > 47 And e.KeyValue < 58) Or (e.KeyValue > 96 And e.KeyValue < 123) Or (e.KeyValue > 64 And e.KeyValue < 91) Or (e.KeyValue = 8) Or (e.KeyValue = 189) Or (e.KeyValue = 190) Then
            Dim s As String = C1Combo3.Text
            If C1Combo3.Text = "" Then
                C1Combo3.DataSource.RowFilter = "1=1"
            Else
                C1Combo3.DataSource.RowFilter = "Ry_Jc like '*" & C1Combo3.Text.Replace("*", "[*]") & "*'"
            End If

            If (e.KeyValue = 8) Then
                C1Combo3.DroppedDown = False
                C1Combo3.DroppedDown = True
            End If

            C1Combo3.Text = s
            C1Combo3.SelectionStart = C1Combo3.Text.Length
            C1Combo3.SelectionLength = 0
        End If

    End Sub

    Private Sub C1Combo3_KeyDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles C1Combo3.KeyDown
        If e.KeyValue = 13 Then
            If C1Combo3.WillChangeToIndex < 1 Then
                If (CType(C1Combo3.DataSource, DataView).Count) = 0 Then
                    MsgBox("患者: '" + Me.C1Combo3.Text + "' 不存在！", MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
                    System.Windows.Forms.SendKeys.Send("^{A}")
                    Exit Sub
                End If
                C1Combo3.SelectedIndex = -1
                C1Combo3.SelectedIndex = 0
            Else
                Dim index As Integer
                index = C1Combo3.WillChangeToIndex
                C1Combo3.SelectedIndex = -1
                C1Combo3.SelectedIndex = index
            End If
            e.Handled = True
            System.Windows.Forms.SendKeys.Send("{Tab}")
        End If
    End Sub

#End Region



    Private Sub C1Combo6_RowChange(ByVal sender As Object, ByVal e As System.EventArgs) Handles YfComobo1.RowChange
        If YfComobo1.WillChangeToValue = "" Then

        ElseIf YfComobo1.WillChangeToValue = iniOperate.iniopreate.GetINI("Personal", "默认药房", "", HisVar.HisVar.Parapath & "\Config.Ini") Then

            iniOperate.iniopreate.WriteINI("Personal", "默认药房", YfComobo1.SelectedValue, HisVar.HisVar.Parapath & "\Config.Ini")
        Else
            If My_Dataset.Tables("药品卫材") IsNot Nothing Then

                If My_Dataset.Tables("药品卫材").Rows.Count = 0 Then
                Else
                    If My_Dataset.HasChanges(DataRowState.Deleted) = True Then
                        If My_Dataset.Tables("药品卫材").Rows.Count = My_Dataset.Tables("药品卫材").GetChanges(DataRowState.Deleted).Rows.Count Then
                        Else
                            YfComobo1.SelectedValue = iniOperate.iniopreate.GetINI("Personal", "默认药房", "", HisVar.HisVar.Parapath & "\Config.Ini")
                            MsgBox("请先删除药品再更换药房！", MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
                            Exit Sub
                        End If
                    Else
                        YfComobo1.SelectedValue = iniOperate.iniopreate.GetINI("Personal", "默认药房", "", HisVar.HisVar.Parapath & "\Config.Ini")
                        MsgBox("请先删除药品再更换药房！", MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
                        Exit Sub
                    End If
                End If

            End If

            iniOperate.iniopreate.WriteINI("Personal", "默认药房", YfComobo1.SelectedValue, HisVar.HisVar.Parapath & "\Config.Ini")
        End If
    End Sub




#Region "DBGrid动作"


    Private Sub C1TrueDBGrid1_MouseDown(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles C1TrueDBGrid1.MouseDown
        If e.Button = MouseButtons.Right Then

            Call Cb_Edit()

        End If
    End Sub

    Private Sub C1TrueDBGrid1_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles C1TrueDBGrid1.KeyDown
        Select Case e.KeyCode
            Case Keys.Return


                Call Cb_Edit()


            Case Keys.Delete
                If (C1TrueDBGrid1.Row + 1) > C1TrueDBGrid1.RowCount Then
                Else
                    Dim Del_Row As DataRow = Cb_Cm.List(C1TrueDBGrid1.Row).Row


                    If RadioButton1.Checked = True Then
                        If HisVar.HisVar.Sqldal.GetSingle("select count (*) from Auto_Czd where AutoCf_code='" & V_Cf_Code & "' and xx_code='" & Del_Row.Item("Xx_code") & "'") > 0 Then
                            MsgBox("该药品已经分配处置单，请先删除处置单中相关数据，再进行删除！")
                            Exit Sub
                        End If
                        If MsgBox("是否删除:" + Me.C1TrueDBGrid1.Columns("Yp_Name").Value + " ", MsgBoxStyle.Information + MsgBoxStyle.OkCancel, "提示:") = MsgBoxResult.Cancel Then Exit Sub
                        HisVar.HisVar.Sqldal.ExecuteSql("Delete From Auto_Cfyp Where Cf_Id='" & Del_Row.Item("Cf_Id") & "'")
                    Else
                        If String.IsNullOrEmpty(Del_Row("Templet_Code") & "") Then
                            If MsgBox("是否删除:" + Me.C1TrueDBGrid1.Columns("Xm_Name").Value + " ", MsgBoxStyle.Information + MsgBoxStyle.OkCancel, "提示:") = MsgBoxResult.Cancel Then Exit Sub
                            HisVar.HisVar.Sqldal.ExecuteSql("Delete From Auto_Cfxm Where Cf_Id='" & Del_Row.Item("Cf_Id") & "'")
                            My_Table.Rows.Remove(Del_Row)
                            C1TrueDBGrid1.Refresh()
                        Else
                            If MsgBox(" " + Me.C1TrueDBGrid1.Columns("Xm_Name").Value + " 由项目模板生成，是否删除该模板下所有项目？", MsgBoxStyle.Information + MsgBoxStyle.OkCancel, "提示:") = MsgBoxResult.Cancel Then Exit Sub
                            HisVar.HisVar.Sqldal.ExecuteSql("Delete From Auto_Cfxm Where AutoCf_Code='" & Del_Row.Item("AutoCf_Code") & "' and Templet_Code='" & Del_Row.Item("Templet_Code") & "'")
                            P_Data_Show()
                        End If
                    End If

                    Call P_SumMoney()
                End If

        End Select

    End Sub


#End Region

#End Region

#Region "从表__编辑"

    Private Sub Init_Data()     '从表数据
        Dim Str_Insert As String = "Insert Into Auto_Cfyp(Yy_Code,AutoCf_Code,Xx_Code,Cf_Sl,Cf_Dj,Cf_Money,Cf_Lb)Values(@Yy_Code,@AutoCf_Code,@Xx_Code,@Cf_Sl,@Cf_Dj,@Cf_Money,@Cf_Lb)"
        Dim Str_Update As String = "Update Auto_Cfyp Set Xx_Code=@Xx_Code,Cf_Sl=@Cf_Sl,Cf_Dj=@Cf_Dj,Cf_Money=@Cf_Money,Cf_Lb=@Cf_Lb Where Cf_Id=@Old_Cf_Id"
        With My_Adapter

            .InsertCommand = New SqlCommand(Str_Insert, My_Cn)
            With .InsertCommand.Parameters
                .Add("@Yy_Code", SqlDbType.VarChar, 4)
                .Add("@AutoCf_Code", SqlDbType.VarChar, 14)                 '出库编码
                .Add("@Xx_Code", SqlDbType.VarChar, 16)                 '类别编码
                .Add("@Cf_Sl", SqlDbType.Decimal)                   '入库数量
                .Add("@Cf_Dj", SqlDbType.Decimal)                   '入库单价
                .Add("@Cf_Money", SqlDbType.Decimal, 10, 2)
                .Add("@Cf_Lb", SqlDbType.VarChar, 50)
            End With

            .UpdateCommand = New SqlCommand(Str_Update, My_Cn)
            With .UpdateCommand.Parameters
                .Add("@Xx_Code", SqlDbType.VarChar, 16)                 '类别编码
                .Add("@Cf_Sl", SqlDbType.Decimal)                   '入库数量
                .Add("@Cf_Dj", SqlDbType.Decimal)                   '入库单价
                .Add("@Cf_Money", SqlDbType.Decimal, 10, 2)
                .Add("@Cf_Lb", SqlDbType.VarChar, 50)
                .Add("@Old_Cf_Id", SqlDbType.Int, 4, "Cf_Id")
            End With



        End With

    End Sub

    Private Sub Init_Data1()     '从表数据
        Dim Str_Insert As String = "Insert Into Auto_Cfxm(Yy_Code,AutoCf_Code,Xm_Code,Cf_Sl,Cf_Dj,Cf_Money,Cf_Lb)Values(@Yy_Code,@AutoCf_Code,@Xm_Code,@Cf_Sl,@Cf_Dj,@Cf_Money,@Cf_Lb)"
        Dim Str_Update As String = "Update Auto_Cfxm Set Xm_Code=@Xm_Code,Cf_Sl=@Cf_Sl,Cf_Dj=@Cf_Dj,Cf_Money=@Cf_Money,Cf_Lb=@Cf_Lb Where Cf_Id=@Old_Cf_Id"
        With My_Adapter

            .InsertCommand = New SqlCommand(Str_Insert, My_Cn)
            With .InsertCommand.Parameters
                .Add("@Yy_Code", SqlDbType.VarChar, 4)
                .Add("@AutoCf_Code", SqlDbType.VarChar, 14)                 '出库编码
                .Add("@Xm_Code", SqlDbType.VarChar, 12)                 '类别编码
                .Add("@Cf_Sl", SqlDbType.Decimal)                   '入库数量
                .Add("@Cf_Dj", SqlDbType.Decimal)                   '入库单价
                .Add("@Cf_Money", SqlDbType.Decimal, 10, 2)
                .Add("@Cf_Lb", SqlDbType.VarChar, 50)
            End With

            .UpdateCommand = New SqlCommand(Str_Update, My_Cn)
            With .UpdateCommand.Parameters
                .Add("@Xm_Code", SqlDbType.VarChar, 12)                 '类别编码
                .Add("@Cf_Sl", SqlDbType.Decimal)                   '入库数量
                .Add("@Cf_Dj", SqlDbType.Decimal)                   '入库单价
                .Add("@Cf_Money", SqlDbType.Decimal, 10, 2)
                .Add("@Cf_Lb", SqlDbType.VarChar, 50)
                .Add("@Old_Cf_Id", SqlDbType.Int, 4, "Cf_Id")
            End With

        End With
    End Sub


#End Region

#Region "自定义函数"

    Private Sub Cb_Edit()
        If C1Combo3.SelectedValue = "" Then
            Beep()
            MsgBox("患者不能为空!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
            C1Combo3.Select()
            Exit Sub
        End If

        If DoctorCombo.SelectedValue = "" Then
            Beep()
            MsgBox("医生姓名不能为空!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
            DoctorCombo.Select()
            Exit Sub
        End If
        If KsCombo.SelectedValue = "" Then
            Beep()
            MsgBox("科室名称不能为空!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
            KsCombo.Select()
            Exit Sub
        End If

        If YfComobo1.SelectedValue = "" Then
            Beep()
            MsgBox("药房名称不能为空!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
            YfComobo1.Select()
            Exit Sub
        End If

        If TimesNumEdit.Value < 1 Then
            MsgBox("执行频率填写有误！", MsgBoxStyle.Information, "提示")
            TimesNumEdit.Value = 1
            Exit Sub
        End If

        If IntervalNumEdit.Value < 0 Or (TimesNumEdit.Value = 1 And IntervalNumEdit.Value > 0) Then
            MsgBox("执行间隔填写有误！", MsgBoxStyle.Information, "提示")
            IntervalNumEdit.Value = 0
            Exit Sub
        End If


        If DateAdd(DateInterval.Hour, IntervalNumEdit.Value * (TimesNumEdit.Value - 1), FirstDateEdit.Value) > Format(Now, "yyyy-MM-dd 23:59:59") Then
            MsgBox("超出24点时间范围，请调整执行频率与执行间隔！", MsgBoxStyle.Information, "提示：")
            TimesNumEdit.Value = 1
            TimesNumEdit.Select()
            Exit Sub
        End If

        V_Yf_Sl = "Yf_Sl" & Mid(YfComobo1.SelectedValue, 6)
        V_Yf_Lsj = "Yf_Lsj" & Mid(YfComobo1.SelectedValue, 6)

        '判断主表是否存在
        Call Zb_Save()

        If (C1TrueDBGrid1.Row + 1) > C1TrueDBGrid1.RowCount Then
            V_Insert = True
        Else
            V_Insert = False
            Cb_Row = Cb_Cm.List(C1TrueDBGrid1.Row).Row
        End If

        Select Case Yp_Lb
            Case "药品卫材"
                If V_Insert = False Then
                    If HisVar.HisVar.Sqldal.GetSingle("select count (*) from Auto_Czd where AutoCf_code='" & V_Cf_Code & "' and xx_code='" & Cb_Row.Item("XX_code") & "'") > 0 Then
                        MsgBox("该药品已经分配处置单，请先删除处置单中相关数据，再进行修改！")
                        C1TrueDBGrid1.Select()
                        Exit Sub
                    End If
                End If
                'Dim vform As New Cq_Cf31(Me, V_Insert, Cb_Row, My_Dataset.Tables(Yp_Lb), T_Label5, C1TrueDBGrid1, My_Adapter, My_Dataset, V_Cf_Code, YfComobo1.SelectedValue, C1Combo3.SelectedValue)
                'If BaseFunc.BaseFunc.CheckOwnForm(Me, vform) = False Then
                '    vform.ShowDialog()
                'End If
                Dim vform As New ZTHisInpatient.Cq_CfYp(V_Cf_Code, YfComobo1.SelectedValue + "", V_Insert, Cb_Row, My_Dataset.Tables(Yp_Lb))
                vform.MyTransmitTxt = _transmitTxt
                vform.ShowDialog()
            Case "诊疗项目"
                If V_Insert = False Then
                    If Cb_Row("Templet_Code") IsNot DBNull.Value AndAlso Cb_Row("Templet_Code").Trim() <> "" Then
                        MsgBox("模板里的项目不允许修改", MsgBoxStyle.Information, "提示")
                        Exit Sub
                    End If
                End If
                'Dim vform As New Cq_Cf34(Me, V_Insert, Cb_Row, My_Dataset.Tables(Yp_Lb), T_Label5, C1TrueDBGrid1, My_Adapter, My_Dataset, V_Cf_Code, C1Combo3.SelectedValue)
                'If BaseFunc.BaseFunc.CheckOwnForm(Me, vform) = False Then
                '    vform.ShowDialog()
                'End If
                Dim vform As New ZTHisInpatient.Cq_CfXm(V_Cf_Code, YfComobo1.SelectedValue + "", V_Insert, Cb_Row, My_Dataset.Tables(Yp_Lb))
                vform.MyTransmitTxt = _transmitTxt
                vform.ShowDialog()
        End Select
        ''重新计算
        Call P_SumMoney()
        Call P_Data_Show()
        C1TrueDBGrid1.Select()
        C1TrueDBGrid1.MoveLast()
    End Sub

    Private Sub P_SumMoney()
        T_Label5.Text = Format(IIf(My_Table.Compute("Sum(Cf_Money)", "") Is DBNull.Value, 0, My_Table.Compute("Sum(Cf_Money)", "")), "###,###,###0.00") + "元"
    End Sub

    Private Function F_MaxCode(ByVal V_MaxCode As String)   '出库编码
        Dim My_Cc As New BaseClass.C_Cc()
        Dim V_Code As String = V_MaxCode
        My_Cc.Get_MaxCode("Auto_Cf", "AutoCf_Code", 14, "Left(AutoCf_Code,10)", HisVar.HisVar.WsyCode & V_MaxCode)
        F_MaxCode = My_Cc.编码
        If Mid(F_MaxCode, 1, 6) = "000000" Then F_MaxCode = V_MaxCode + Mid(My_Cc.编码, 7)
        Return F_MaxCode
    End Function

    Private Sub GridMove(moveType As String)
        If C1TrueDBGrid1.RowCount = 0 Then
            Return
        End If
        Select Case moveType
            Case "最前"
                C1TrueDBGrid1.MoveFirst()
                Exit Select
            Case "上移"
                C1TrueDBGrid1.MovePrevious()
                Exit Select
            Case "下移"
                C1TrueDBGrid1.MoveNext()
                Exit Select
            Case "最后"
                C1TrueDBGrid1.MoveLast()
                Exit Select
            Case Else
                Dim index As Integer
                If Integer.TryParse(moveType, index) Then
                    C1TrueDBGrid1.Row = index
                End If
                Exit Select
        End Select
        Call P_SumMoney()
    End Sub

#End Region

#Region "鼠标__外观"
    Private Sub P_Comm(ByVal Bt As Button)
        With Bt
            Select Case .Tag
                Case "保存"
                    .Location = New Point(T_Line1.Left + 10, 4)
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")
                    .Text = "            &B"

                Case "取消"
                    .Location = New Point(Comm1.Left + Comm1.Width + 4, 4)
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
                    .Width = Me.Comm1.Width
                    .Text = "            &C"
                Case "处置单"
                    .Location = New Point(Comm2.Left + Comm2.Width + 4, 4)
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_处置单1")
                    .Width = Me.comm4.Width
                    .Text = "            &X"
                    T_Line2.Location = New Point(Me.comm4.Left + Me.comm4.Width + 8, 5)

            End Select
            .FlatStyle = FlatStyle.Flat
            .FlatAppearance.BorderSize = 0
            .BackgroundImageLayout = ImageLayout.Center
        End With
    End Sub

    Private Sub Comm_MouseEnter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.MouseEnter, Comm2.MouseEnter, comm4.MouseEnter
        Select Case sender.tag
            Case "保存"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存2")
                Comm1.Cursor = Cursors.Hand
            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消2")
                Comm2.Cursor = Cursors.Hand
            Case "处置单"
                comm4.BackgroundImage = MyResources.C_Resources.getimage("命令_处置单2")
                comm4.Cursor = Cursors.Hand
        End Select

    End Sub

    Private Sub Comm_MouseLeave(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.MouseLeave, Comm2.MouseLeave, comm4.MouseLeave
        Select Case sender.tag
            Case "保存"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")
                Comm1.Cursor = Cursors.Default
            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
                Comm2.Cursor = Cursors.Default
            Case "处置单"
                comm4.BackgroundImage = MyResources.C_Resources.getimage("命令_处置单1")
                comm4.Cursor = Cursors.Default
        End Select
    End Sub

    Private Sub Comm_MouseDown(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm1.MouseDown, Comm2.MouseDown, comm4.MouseDown
        Select Case sender.tag
            Case "保存"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存3")
                Comm1.Cursor = Cursors.Default

            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消3")
                Comm2.Cursor = Cursors.Default
            Case "处置单"
                comm4.BackgroundImage = MyResources.C_Resources.getimage("命令_处置单3")
                comm4.Cursor = Cursors.Default

        End Select
    End Sub

    Private Sub Comm_MouseUp(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm1.MouseUp, Comm2.MouseUp, comm4.MouseUp
        Select Case sender.tag
            Case "保存"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")
                Comm1.Cursor = Cursors.Hand
            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
                Comm2.Cursor = Cursors.Hand
            Case "处置单"
                comm4.BackgroundImage = MyResources.C_Resources.getimage("命令_处置单1")
                comm4.Cursor = Cursors.Hand
        End Select
    End Sub

#End Region

#Region "中西药、草药、卫材切换"

    Private Sub RadioButton1_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles RadioButton1.CheckedChanged, RadioButton4.CheckedChanged
        If Me.Visible = False Then Exit Sub

        If Rinsert = True Then
            V_Cf_Code = F_MaxCode(Format(Now, "yyMMdd"))
            Label12.Text = V_Cf_Code
        End If

        If RadioButton1.Checked = True Then
            Call Init_Data()
            C1TrueDBGrid_Init_Zxy()
            Yp_Lb = Mid(RadioButton1.Text, 4)
            C1Command1.Enabled = True
            C1Command2.Enabled = True
            C1NumericEdit1.Enabled = True
        End If
        If RadioButton4.Checked = True Then
            Call Init_Data1()
            C1TrueDBGrid_Init_Xm()
            Yp_Lb = Mid(RadioButton4.Text, 4)
            C1Command1.Enabled = False
            C1Command2.Enabled = False
            C1NumericEdit1.Enabled = False
        End If
        Call P_Data_Show()
        C1TrueDBGrid1.Select()
    End Sub

#End Region

#Region "中西药、草药、卫材C1Truedbgrid初始化"

    Private Sub C1TrueDBGrid_Init_Zxy()
        '初始化TDBGrid
        Dim My_Grid As New BaseClass.C_Grid(Me.C1TrueDBGrid1)
        With My_Grid
            .Init_Grid()
            .AllAddNew(True)
            .AllDelete(True)
            .AllUpdate(False)
            .AllSort(False)
            '.P_MarqueeEnum(BaseClass.C_Grid.MarqueeEnum.FloatingEditor)
            .P_Dock(BaseClass.C_Grid.Dock.Fill)
            .Init_Column("药品名称", "Yp_Name", 200, "左", "")
            .Init_Column("规格", "Mx_Gg", 130, "左", "")
            .Init_Column("产地", "Mx_Cd", 200, "左", "")
            .Init_Column("有效期", "Yp_Yxq", 100, "中", "yyyy-MM-dd")

            .Init_Column("数量", "Cf_Sl", 60, "右", "###,###,##0.00##")
            .Init_Column("单位", "Mx_XsDw", 45, "左", "")
            .Init_Column("单价", "Cf_Dj", 70, "右", "###,###,##0.00####")
            .Init_Column("金额", "Cf_Money", 80, "右", "###,###,##0.00##")
            .Init_Column("用法用量", "Yp_Yfyl", 200, "左", "")
            .Init_Column("类别", "Cf_Lb", 70, "中", "")

        End With
    End Sub

    Private Sub C1TrueDBGrid_Init_Xm()
        '初始化TDBGrid
        Dim My_Grid As New BaseClass.C_Grid(Me.C1TrueDBGrid1)
        With My_Grid
            .Init_Grid()
            .AllAddNew(True)
            .AllDelete(True)
            .AllUpdate(False)
            .AllSort(False)

            .P_Dock(BaseClass.C_Grid.Dock.Fill)
            .Init_Column("项目名称", "Xm_Name", 380, "左", "")
            .Init_Column("项目单位", "Xm_Dw", 100, "左", "")

            .Init_Column("数量", "Cf_Sl", 70, "右", "###,###,##0")
            .Init_Column("单价", "Cf_Dj", 70, "右", "###,###,##0.00")
            .Init_Column("金额", "Cf_Money", 70, "右", "###,###,##0.00")
            .Init_Column("类别", "Cf_Lb", 70, "中", "###,###,##0.00")
        End With
    End Sub
#End Region

    Private Sub C1Command1_Click(ByVal sender As Object, ByVal e As C1.Win.C1Command.ClickEventArgs) Handles C1Command1.Click, C1Command2.Click
        If sender.text = "乘" Then
            If C1TrueDBGrid1.RowCount = 0 Then Exit Sub
            If C1TrueDBGrid1.Columns("数量").Value = 0 Then Exit Sub
            HisVar.HisVar.Sqldal.ExecuteSql("Update Auto_Cfyp Set Cf_Sl=Cf_Sl*" & C1NumericEdit1.Value & ",Cf_Money=Cf_Sl*" & C1NumericEdit1.Value & "*Cf_Dj Where AutoCf_Code='" & Label12.Text & "' And Cf_Lb like '%草药%'")
            Call P_Data_Show()
        End If
        If sender.text = "除" Then
            If C1TrueDBGrid1.RowCount = 0 Then Exit Sub
            If C1TrueDBGrid1.Columns("数量").Value = 0 Then Exit Sub
            HisVar.HisVar.Sqldal.ExecuteSql("Update Auto_Cfyp Set Cf_Sl=Cf_Sl/" & C1NumericEdit1.Value & ",Cf_Money=Cf_Sl/" & C1NumericEdit1.Value & "*Cf_Dj Where AutoCf_Code='" & Label12.Text & "' And Cf_Lb like '%草药%'")
            Call P_Data_Show()
        End If
    End Sub

#Region "输入法设置"
    '中文
    Private Sub C1Numeric1_KeyDown(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Combo3.GotFocus, DoctorCombo.GotFocus, KsCombo.GotFocus, YfComobo1.GotFocus
        InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages(0)
    End Sub

#End Region


    Private Sub TimesNumEdit_ValueChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles TimesNumEdit.ValueChanged
        If TimesNumEdit.Value <= 1 Then
            IntervalNumEdit.Value = 0
        End If
    End Sub

    Private Sub IntervalNumEdit_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles IntervalNumEdit.KeyPress
        If e.KeyChar <> Chr(Keys.Return) Then Exit Sub
        e.Handled = True
        C1NumericEdit1.Select()
    End Sub

End Class