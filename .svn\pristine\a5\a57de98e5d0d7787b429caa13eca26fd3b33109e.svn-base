﻿/**  版本信息模板在安装目录下，可自行修改。
* B_KC21.cs
*
* 功 能： N/A
* 类 名： B_KC21
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/8/24 08:44:38   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Collections.Generic;
using ModelOld;
namespace BLLOld
{
    /// <summary>
    /// B_KC21
    /// </summary>
    public partial class B_KC21
    {
        private readonly DAL.D_KC21 dal = new DAL.D_KC21();
        public B_KC21()
        { }
        #region  BasicMethod
        ///// <summary>
        ///// 是否存在该记录
        ///// </summary>
        //public bool Exists(string AKC190)
        //{
        //    return dal.Exists(AKC190);
        //}

        public string GetDjMaxCode(string AKC190)
        {
            return dal.GetDjMaxCode(AKC190);
        }
        /// <summary>
        /// 就诊记录插入KC21
        /// </summary>
        public bool Add(string Lb)
        {
            return dal.Add(Lb);
        }

        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ModelOld.M_KC21 model)
        {
            return dal.Add(model);
        }

        /// <summary>
        /// 更新Kc21，kc22 表中传输标记
        /// </summary>
        public bool Update(string AKC190, int mark)
        {
            return dal.Update(AKC190, mark);
        }
        /// <summary>
        /// 按条件 更新一条数据
        /// </summary>
        public bool Update(string strWhere)
        {
            return dal.Update(strWhere);
        }
        
        /// <summary>
        /// 更新Js_Date
        /// </summary>

        public bool UpdateJsDate(string AKC190, string Js_Date)
        {
            return dal.UpdateJsDate(AKC190, Js_Date);
        }
        /// <summary>
        /// 更新RY_YLCODE
        /// </summary>
        public bool UpdateRY_YLCODE(string AKC190, string RY_YLCODE)
        {
            return dal.UpdateRY_YLCODE(AKC190, RY_YLCODE);
        }

        /// <summary>
        /// 更新单据号
        /// </summary>

        public bool UpdateDj_Code(string AKC190, string Dj_Code)
        {
            return dal.UpdateDj_Code(AKC190, Dj_Code);
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ModelOld.M_KC21 model)
        {
            return dal.Update(model);
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(string AKC190)
        {

            return dal.Delete(AKC190);
        }
        /// <summary>
        /// 删除就诊信息和费用明细
        /// </summary>
        /// <param name="AKC190"></param>
        /// <returns></returns>
        public bool DeleteAll(string AKC190)
        {
            return dal.DeleteAll(AKC190);
        }
        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool DeleteList(string AKC190list)
        {
            return dal.DeleteList(AKC190list);
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ModelOld.M_KC21 GetModel(string AKC190)
        {

            return dal.GetModel(AKC190);
        }

        ///// <summary>
        ///// 得到一个对象实体，从缓存中
        ///// </summary>
        //public ModelOld.M_KC21 GetModelByCache(string AKC190)
        //{

        //    string CacheKey = "M_KC21Model-" + AKC190;
        //    object objModel = Common.DataCache.GetCache(CacheKey);
        //    if (objModel == null)
        //    {
        //        try
        //        {
        //            objModel = dal.GetModel(AKC190);
        //            if (objModel != null)
        //            {
        //                int ModelCache = Common.ConfigHelper.GetConfigInt("ModelCache");
        //                Common.DataCache.SetCache(CacheKey, objModel, DateTime.Now.AddMinutes(ModelCache), TimeSpan.Zero);
        //            }
        //        }
        //        catch{}
        //    }
        //    return (ModelOld.M_KC21)objModel;
        //}

        /// <summary>
        /// 获取报表统计明细
        /// </summary>
        public DataSet GetPrintMx(string AKC190)
         {
            return dal.GetPrintMx(AKC190);
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string Lb, string strWhere)
        {
            return dal.GetList(Lb, strWhere);
        }
        /// <summary>
        /// 获取新版 住院就诊信息
        /// </summary>
        public DataSet GetLis_Zy_NewVersion(string strWhere)
        {
            return dal.GetLis_Zy_NewVersion(strWhere);
        }
        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            return dal.GetList(Top, strWhere, filedOrder);
        }
        /// <summary>
        /// 获得数据列表
        /// </summary>
        public List<ModelOld.M_KC21> GetModelList(string Lb, string strWhere)
        {
            DataSet ds = dal.GetList(Lb, strWhere);
            return DataTableToList(ds.Tables[0]);
        }
        /// <summary>
        /// 获得数据列表
        /// </summary>
        public List<ModelOld.M_KC21> DataTableToList(DataTable dt)
        {
            List<ModelOld.M_KC21> modelList = new List<ModelOld.M_KC21>();
            int rowsCount = dt.Rows.Count;
            if (rowsCount > 0)
            {
                ModelOld.M_KC21 model;
                for (int n = 0; n < rowsCount; n++)
                {
                    model = dal.DataRowToModel(dt.Rows[n]);
                    if (model != null)
                    {
                        modelList.Add(model);
                    }
                }
            }
            return modelList;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetAllList(string Lb)
        {
            return GetList(Lb, "");
        }

        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            return dal.GetRecordCount(strWhere);
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            return dal.GetListByPage(strWhere, orderby, startIndex, endIndex);
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        //public DataSet GetList(int PageSize,int PageIndex,string strWhere)
        //{
        //return dal.GetList(PageSize,PageIndex,strWhere);
        //}

        /// <summary>
        /// 门诊结算成功
        /// </summary>
        public bool UpdateMzOk(string AKC190)
        {
            return dal.UpdateMzOk(AKC190);
        }

        /// <summary>
        /// 门诊结算回退
        /// </summary>
        public bool UpdateMzRollback(string AKC190)
        {
            return dal.UpdateMzRollback(AKC190);
        }

        /// <summary>
        /// 住院登记
        /// </summary>
        public bool UpdateZyDjOk(string AKC190)
        {
            return dal.UpdateZyOk("住院登记",AKC190);
        }

        /// <summary>
        /// 住院录费预结算
        /// </summary>
        public bool UpdateZyYjsOk(string AKC190)
        {
            return dal.UpdateZyOk("住院录费预结算", AKC190);
        }

        /// <summary>
        /// 住院录费预结算
        /// </summary>
        public bool UpdateKc22ByDataTable(DataTable dt) 
        {
            return dal.UpdateKc22ByDataTable(dt);
        }

        /// <summary>
        /// 普通住院结算成功后，信息更新
        /// </summary>
        /// <param name="row"></param>
        /// <returns></returns>
        public bool UpdateCommonJs(DataRow row)
        {
            return dal.UpdateCommonJs(row);
        }

        /// <summary>
        /// 新版本 普通住院结算成功后，信息更新
        /// </summary>
        /// <param name="row"></param>
        /// <returns></returns>
        public bool UpdateCommonJs_NewVersion(DataRow row)
        {
            return dal.UpdateCommonJs_NewVersion(row);
        }

        /// <summary>
        /// 年终结算成功后 信息更新
        /// </summary>
        /// <param name="row"></param>
        /// <returns></returns>
        public bool UpdateYearEnd(DataRow row)
        {
            return dal.UpdateYearEnd(row);
        }

        /// <summary>
        /// 新版本 年终结算成功后 信息更新
        /// </summary>
        /// <param name="row"></param>
        /// <returns></returns>
        public bool UpdateYearEnd_NewVersion(DataRow row)
        {
            return dal.UpdateYearEnd_NewVersion(row);
        }

        /// <summary>
        /// 住院结算回退
        /// </summary>
        public bool UpdateZyRollback(string AKC190)
        {
            return dal.UpdateZyRollback(AKC190);

        }
        /// <summary>
        /// 判断 住院号是否做过年终结算
        /// </summary>
        /// <param name="AKC190"></param>
        /// <returns></returns>
        public bool IsYearEnd(string AKC190)
        {
            return dal.IsYearEnd(AKC190);
        }

        /// <summary>
        /// 无费出院
        /// </summary>
        public bool UpdateZyWfcy(string AKC190)
        {
            return dal.UpdateZyWfcy(AKC190);
        }
        /// <summary>
        /// 无费出院 新版本
        /// </summary>
        public bool UpdateZyWfcy_NewVersion(string AKC190)
        {
            return dal.UpdateZyWfcy_NewVersion(AKC190);
        }
        /// <summary>
        /// 新版本
        /// 导入 、更新 住院就诊信息 、费用明细
        /// </summary>
        /// <returns></returns>
        public bool ZyDataDr_NewVersion()
        {
            return dal.ZyDataDr_NewVersion();
        }

        /// <summary>
        /// 导入住院就诊信息
        /// </summary>
        /// <returns></returns>
        public bool ImportZyJzxx()
        {
            return dal.ImportZyJzxx();
        }

        /// <summary>
        /// 按照akc190 导入费用明细
        /// </summary>
        /// <param name="akc190"></param>
        /// <returns></returns>
        public bool ImportFymmByAKC190(string akc190)
        {
            return dal.ImportFymmByAKC190(akc190);
        }

            #endregion  BasicMethod
            #region  ExtensionMethod

            #endregion  ExtensionMethod
        }
}

