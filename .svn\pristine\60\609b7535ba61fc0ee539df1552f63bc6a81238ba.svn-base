﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class EmrStation
    Inherits HisControl.BaseForm

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(EmrStation))
        Me.C1CommandDock1 = New C1.Win.C1Command.C1CommandDock()
        Me.C1DockingTab1 = New C1.Win.C1Command.C1DockingTab()
        Me.C1DockingTabPage1 = New C1.Win.C1Command.C1DockingTabPage()
        Me.TableLayoutPanel2 = New System.Windows.Forms.TableLayoutPanel()
        Me.DzblTreeView = New System.Windows.Forms.TreeView()
        Me.Image1 = New System.Windows.Forms.ImageList(Me.components)
        Me.MyButton1 = New CustomControl.MyButton()
        Me.ContextMenuStrip1 = New System.Windows.Forms.ContextMenuStrip(Me.components)
        Me.DeleNode = New System.Windows.Forms.ToolStripMenuItem()
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel()
        Me.DcCardListViewControl1 = New DCSoft.WinForms.Controls.DCCardListViewControl()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.JsDateEdit = New CustomControl.MyDateEdit()
        Me.KsDateEdit = New CustomControl.MyDateEdit()
        Me.bqDtComobo = New CustomControl.MyDtComobo()
        Me.ksDtComobo = New CustomControl.MyDtComobo()
        Me.CxButton = New CustomControl.MyButton()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.ZtSingleComobo = New CustomControl.MySingleComobo()
        Me.XmTextBox = New CustomControl.MyTextBox()
        Me.CwTextBox = New CustomControl.MyTextBox()
        CType(Me.C1CommandDock1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.C1CommandDock1.SuspendLayout()
        CType(Me.C1DockingTab1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.C1DockingTab1.SuspendLayout()
        Me.C1DockingTabPage1.SuspendLayout()
        Me.TableLayoutPanel2.SuspendLayout()
        Me.ContextMenuStrip1.SuspendLayout()
        Me.TableLayoutPanel1.SuspendLayout()
        Me.Panel1.SuspendLayout()
        Me.SuspendLayout()
        '
        'C1CommandDock1
        '
        Me.C1CommandDock1.Controls.Add(Me.C1DockingTab1)
        Me.C1CommandDock1.Dock = System.Windows.Forms.DockStyle.Left
        Me.C1CommandDock1.DockingStyle = C1.Win.C1Command.DockingStyle.VS2010
        Me.C1CommandDock1.Id = 1
        Me.C1CommandDock1.Location = New System.Drawing.Point(0, 0)
        Me.C1CommandDock1.Name = "C1CommandDock1"
        Me.C1CommandDock1.Size = New System.Drawing.Size(300, 584)
        '
        'C1DockingTab1
        '
        Me.C1DockingTab1.Alignment = System.Windows.Forms.TabAlignment.Bottom
        Me.C1DockingTab1.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1DockingTab1.CanAutoHide = True
        Me.C1DockingTab1.CanCloseTabs = True
        Me.C1DockingTab1.CanMoveTabs = True
        Me.C1DockingTab1.Controls.Add(Me.C1DockingTabPage1)
        Me.C1DockingTab1.Location = New System.Drawing.Point(0, 0)
        Me.C1DockingTab1.Name = "C1DockingTab1"
        Me.C1DockingTab1.ShowCaption = True
        Me.C1DockingTab1.ShowSingleTab = False
        Me.C1DockingTab1.Size = New System.Drawing.Size(300, 584)
        Me.C1DockingTab1.TabIndex = 0
        Me.C1DockingTab1.TabSizeMode = C1.Win.C1Command.TabSizeModeEnum.Fit
        Me.C1DockingTab1.TabsSpacing = 5
        Me.C1DockingTab1.TabStyle = C1.Win.C1Command.TabStyleEnum.Office2010
        Me.C1DockingTab1.VisualStyle = C1.Win.C1Command.VisualStyle.Office2010Blue
        Me.C1DockingTab1.VisualStyleBase = C1.Win.C1Command.VisualStyle.Office2010Blue
        '
        'C1DockingTabPage1
        '
        Me.C1DockingTabPage1.CaptionVisible = True
        Me.C1DockingTabPage1.Controls.Add(Me.TableLayoutPanel2)
        Me.C1DockingTabPage1.Location = New System.Drawing.Point(0, 0)
        Me.C1DockingTabPage1.Name = "C1DockingTabPage1"
        Me.C1DockingTabPage1.Size = New System.Drawing.Size(297, 583)
        Me.C1DockingTabPage1.TabIndex = 0
        Me.C1DockingTabPage1.Text = "病历列表"
        '
        'TableLayoutPanel2
        '
        Me.TableLayoutPanel2.ColumnCount = 1
        Me.TableLayoutPanel2.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanel2.Controls.Add(Me.DzblTreeView, 0, 1)
        Me.TableLayoutPanel2.Controls.Add(Me.MyButton1, 0, 0)
        Me.TableLayoutPanel2.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TableLayoutPanel2.Location = New System.Drawing.Point(0, 19)
        Me.TableLayoutPanel2.Margin = New System.Windows.Forms.Padding(0)
        Me.TableLayoutPanel2.Name = "TableLayoutPanel2"
        Me.TableLayoutPanel2.RowCount = 2
        Me.TableLayoutPanel2.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 67.0!))
        Me.TableLayoutPanel2.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanel2.Size = New System.Drawing.Size(297, 564)
        Me.TableLayoutPanel2.TabIndex = 3
        '
        'DzblTreeView
        '
        Me.DzblTreeView.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom), System.Windows.Forms.AnchorStyles)
        Me.DzblTreeView.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.DzblTreeView.ImageIndex = 0
        Me.DzblTreeView.ImageList = Me.Image1
        Me.DzblTreeView.Location = New System.Drawing.Point(4, 67)
        Me.DzblTreeView.Margin = New System.Windows.Forms.Padding(0)
        Me.DzblTreeView.Name = "DzblTreeView"
        Me.DzblTreeView.SelectedImageIndex = 0
        Me.DzblTreeView.Size = New System.Drawing.Size(288, 497)
        Me.DzblTreeView.TabIndex = 0
        '
        'Image1
        '
        Me.Image1.ImageStream = CType(resources.GetObject("Image1.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.Image1.TransparentColor = System.Drawing.Color.White
        Me.Image1.Images.SetKeyName(0, "man.png")
        Me.Image1.Images.SetKeyName(1, "")
        Me.Image1.Images.SetKeyName(2, "")
        Me.Image1.Images.SetKeyName(3, "nv_user.ico")
        Me.Image1.Images.SetKeyName(4, "Icon_1361.png")
        Me.Image1.Images.SetKeyName(5, "")
        '
        'MyButton1
        '
        Me.MyButton1.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.MyButton1.ButtonImageSize = CustomControl.MyButton.imageSize.huge
        Me.MyButton1.DialogResult = System.Windows.Forms.DialogResult.None
        Me.MyButton1.Font = New System.Drawing.Font("黑体", 24.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MyButton1.Location = New System.Drawing.Point(8, 5)
        Me.MyButton1.Margin = New System.Windows.Forms.Padding(8, 0, 8, 0)
        Me.MyButton1.Name = "MyButton1"
        Me.MyButton1.Size = New System.Drawing.Size(281, 57)
        Me.MyButton1.TabIndex = 1
        Me.MyButton1.Text = "添加病例"
        '
        'ContextMenuStrip1
        '
        Me.ContextMenuStrip1.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.DeleNode})
        Me.ContextMenuStrip1.Name = "ContextMenuStrip1"
        Me.ContextMenuStrip1.Size = New System.Drawing.Size(101, 26)
        '
        'DeleNode
        '
        Me.DeleNode.Image = CType(resources.GetObject("DeleNode.Image"), System.Drawing.Image)
        Me.DeleNode.Name = "DeleNode"
        Me.DeleNode.Size = New System.Drawing.Size(100, 22)
        Me.DeleNode.Text = "删除"
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.CellBorderStyle = System.Windows.Forms.TableLayoutPanelCellBorderStyle.[Single]
        Me.TableLayoutPanel1.ColumnCount = 1
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 1182.0!))
        Me.TableLayoutPanel1.Controls.Add(Me.DcCardListViewControl1, 0, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.Panel1, 0, 0)
        Me.TableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(300, 0)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.RowCount = 2
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 40.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 800.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle())
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(1351, 584)
        Me.TableLayoutPanel1.TabIndex = 1
        '
        'DcCardListViewControl1
        '
        Me.DcCardListViewControl1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.DcCardListViewControl1.AutoScroll = True
        Me.DcCardListViewControl1.AutoScrollMinSize = New System.Drawing.Size(11, 10)
        Me.DcCardListViewControl1.Location = New System.Drawing.Point(4, 45)
        Me.DcCardListViewControl1.Name = "DcCardListViewControl1"
        Me.DcCardListViewControl1.Size = New System.Drawing.Size(1343, 535)
        Me.DcCardListViewControl1.TabIndex = 0
        '
        'Panel1
        '
        Me.Panel1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Panel1.Controls.Add(Me.JsDateEdit)
        Me.Panel1.Controls.Add(Me.KsDateEdit)
        Me.Panel1.Controls.Add(Me.bqDtComobo)
        Me.Panel1.Controls.Add(Me.ksDtComobo)
        Me.Panel1.Controls.Add(Me.CxButton)
        Me.Panel1.Controls.Add(Me.Label2)
        Me.Panel1.Controls.Add(Me.Label1)
        Me.Panel1.Controls.Add(Me.ZtSingleComobo)
        Me.Panel1.Controls.Add(Me.XmTextBox)
        Me.Panel1.Controls.Add(Me.CwTextBox)
        Me.Panel1.Location = New System.Drawing.Point(4, 4)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(1343, 34)
        Me.Panel1.TabIndex = 1
        '
        'JsDateEdit
        '
        Me.JsDateEdit.Captain = "至"
        Me.JsDateEdit.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.JsDateEdit.CaptainWidth = 20.0!
        Me.JsDateEdit.InitialSelection = C1.Win.C1Input.InitialSelectionEnum.CaretAtEnd
        Me.JsDateEdit.Location = New System.Drawing.Point(1015, 6)
        Me.JsDateEdit.MaximumSize = New System.Drawing.Size(100000000, 20)
        Me.JsDateEdit.MinimumSize = New System.Drawing.Size(0, 20)
        Me.JsDateEdit.Name = "JsDateEdit"
        Me.JsDateEdit.Size = New System.Drawing.Size(131, 20)
        Me.JsDateEdit.TabIndex = 10
        Me.JsDateEdit.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.JsDateEdit.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.JsDateEdit.VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.DropDown
        '
        'KsDateEdit
        '
        Me.KsDateEdit.Captain = "出院日期"
        Me.KsDateEdit.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.KsDateEdit.CaptainWidth = 60.0!
        Me.KsDateEdit.InitialSelection = C1.Win.C1Input.InitialSelectionEnum.CaretAtEnd
        Me.KsDateEdit.Location = New System.Drawing.Point(838, 6)
        Me.KsDateEdit.MaximumSize = New System.Drawing.Size(100000000, 20)
        Me.KsDateEdit.MinimumSize = New System.Drawing.Size(0, 20)
        Me.KsDateEdit.Name = "KsDateEdit"
        Me.KsDateEdit.Size = New System.Drawing.Size(171, 20)
        Me.KsDateEdit.TabIndex = 10
        Me.KsDateEdit.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.KsDateEdit.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.KsDateEdit.VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.DropDown
        '
        'bqDtComobo
        '
        Me.bqDtComobo.Captain = "病    区"
        Me.bqDtComobo.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.bqDtComobo.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.bqDtComobo.CaptainWidth = 60.0!
        Me.bqDtComobo.DataSource = Nothing
        Me.bqDtComobo.ItemHeight = 18
        Me.bqDtComobo.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.bqDtComobo.Location = New System.Drawing.Point(171, 6)
        Me.bqDtComobo.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.bqDtComobo.MinimumSize = New System.Drawing.Size(0, 20)
        Me.bqDtComobo.Name = "bqDtComobo"
        Me.bqDtComobo.ReadOnly = False
        Me.bqDtComobo.Size = New System.Drawing.Size(157, 20)
        Me.bqDtComobo.TabIndex = 9
        Me.bqDtComobo.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'ksDtComobo
        '
        Me.ksDtComobo.Captain = "科    室"
        Me.ksDtComobo.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.ksDtComobo.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.ksDtComobo.CaptainWidth = 60.0!
        Me.ksDtComobo.DataSource = Nothing
        Me.ksDtComobo.ItemHeight = 16
        Me.ksDtComobo.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.ksDtComobo.Location = New System.Drawing.Point(7, 6)
        Me.ksDtComobo.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.ksDtComobo.MinimumSize = New System.Drawing.Size(0, 20)
        Me.ksDtComobo.Name = "ksDtComobo"
        Me.ksDtComobo.ReadOnly = False
        Me.ksDtComobo.Size = New System.Drawing.Size(157, 20)
        Me.ksDtComobo.TabIndex = 8
        Me.ksDtComobo.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'CxButton
        '
        Me.CxButton.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.CxButton.DialogResult = System.Windows.Forms.DialogResult.None
        Me.CxButton.Location = New System.Drawing.Point(1271, 5)
        Me.CxButton.Name = "CxButton"
        Me.CxButton.Size = New System.Drawing.Size(60, 24)
        Me.CxButton.TabIndex = 6
        Me.CxButton.Text = "查询"
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.Location = New System.Drawing.Point(1228, 11)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(23, 12)
        Me.Label2.TabIndex = 5
        Me.Label2.Text = "0人"
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.Location = New System.Drawing.Point(1162, 11)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(65, 12)
        Me.Label1.TabIndex = 4
        Me.Label1.Text = "病 人 数："
        '
        'ZtSingleComobo
        '
        Me.ZtSingleComobo.Captain = "状    态"
        Me.ZtSingleComobo.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.ZtSingleComobo.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.ZtSingleComobo.CaptainWidth = 60.0!
        Me.ZtSingleComobo.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        Me.ZtSingleComobo.ItemHeight = 16
        Me.ZtSingleComobo.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.ZtSingleComobo.Location = New System.Drawing.Point(671, 6)
        Me.ZtSingleComobo.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.ZtSingleComobo.MinimumSize = New System.Drawing.Size(0, 20)
        Me.ZtSingleComobo.Name = "ZtSingleComobo"
        Me.ZtSingleComobo.ReadOnly = False
        Me.ZtSingleComobo.Size = New System.Drawing.Size(157, 20)
        Me.ZtSingleComobo.TabIndex = 3
        Me.ZtSingleComobo.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'XmTextBox
        '
        Me.XmTextBox.Captain = "姓    名"
        Me.XmTextBox.CaptainBackColor = System.Drawing.Color.Transparent
        Me.XmTextBox.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.XmTextBox.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.XmTextBox.CaptainWidth = 60.0!
        Me.XmTextBox.ContentForeColor = System.Drawing.Color.Black
        Me.XmTextBox.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.XmTextBox.Location = New System.Drawing.Point(504, 6)
        Me.XmTextBox.Multiline = False
        Me.XmTextBox.Name = "XmTextBox"
        Me.XmTextBox.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.XmTextBox.ReadOnly = False
        Me.XmTextBox.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.XmTextBox.SelectionStart = 0
        Me.XmTextBox.SelectStart = 0
        Me.XmTextBox.Size = New System.Drawing.Size(157, 20)
        Me.XmTextBox.TabIndex = 2
        Me.XmTextBox.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.XmTextBox.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        '
        'CwTextBox
        '
        Me.CwTextBox.Captain = "床    位"
        Me.CwTextBox.CaptainBackColor = System.Drawing.Color.Transparent
        Me.CwTextBox.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.CwTextBox.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.CwTextBox.CaptainWidth = 60.0!
        Me.CwTextBox.ContentForeColor = System.Drawing.Color.Black
        Me.CwTextBox.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.CwTextBox.Location = New System.Drawing.Point(337, 6)
        Me.CwTextBox.Multiline = False
        Me.CwTextBox.Name = "CwTextBox"
        Me.CwTextBox.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.CwTextBox.ReadOnly = False
        Me.CwTextBox.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.CwTextBox.SelectionStart = 0
        Me.CwTextBox.SelectStart = 0
        Me.CwTextBox.Size = New System.Drawing.Size(157, 20)
        Me.CwTextBox.TabIndex = 0
        Me.CwTextBox.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.CwTextBox.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        '
        'EmrStation
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1651, 584)
        Me.Controls.Add(Me.TableLayoutPanel1)
        Me.Controls.Add(Me.C1CommandDock1)
        Me.Name = "EmrStation"
        Me.Text = "Emr_Station"
        CType(Me.C1CommandDock1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.C1CommandDock1.ResumeLayout(False)
        CType(Me.C1DockingTab1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.C1DockingTab1.ResumeLayout(False)
        Me.C1DockingTabPage1.ResumeLayout(False)
        Me.TableLayoutPanel2.ResumeLayout(False)
        Me.ContextMenuStrip1.ResumeLayout(False)
        Me.TableLayoutPanel1.ResumeLayout(False)
        Me.Panel1.ResumeLayout(False)
        Me.Panel1.PerformLayout()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents C1CommandDock1 As C1.Win.C1Command.C1CommandDock
    Friend WithEvents C1DockingTab1 As C1.Win.C1Command.C1DockingTab
    Friend WithEvents TableLayoutPanel1 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents DcCardListViewControl1 As DCSoft.WinForms.Controls.DCCardListViewControl
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents CwTextBox As CustomControl.MyTextBox
    Friend WithEvents CxButton As CustomControl.MyButton
    Friend WithEvents Label2 As System.Windows.Forms.Label
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents ZtSingleComobo As CustomControl.MySingleComobo
    Friend WithEvents XmTextBox As CustomControl.MyTextBox
    Friend WithEvents C1DockingTabPage1 As C1.Win.C1Command.C1DockingTabPage
    Friend WithEvents TableLayoutPanel2 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents DzblTreeView As System.Windows.Forms.TreeView
    Friend WithEvents MyButton1 As CustomControl.MyButton
    Friend WithEvents ksDtComobo As CustomControl.MyDtComobo
    Friend WithEvents Image1 As System.Windows.Forms.ImageList
    Friend WithEvents bqDtComobo As CustomControl.MyDtComobo
    Friend WithEvents ContextMenuStrip1 As System.Windows.Forms.ContextMenuStrip
    Friend WithEvents DeleNode As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents JsDateEdit As CustomControl.MyDateEdit
    Friend WithEvents KsDateEdit As CustomControl.MyDateEdit

End Class
