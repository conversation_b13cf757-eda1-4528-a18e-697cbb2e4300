﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="0" />
    <DataSources isList="true" count="3">
      <门诊日结单 Ref="2" type="DataTableSource" isKey="true">
        <Alias>门诊日结单</Alias>
        <Columns isList="true" count="10">
          <value>V_Lb,System.String</value>
          <value>Mz_Lb,System.String</value>
          <value>Mz_Xj<PERSON>oney,System.Decimal</value>
          <value>Mz_QtMoney,System.Decimal</value>
          <value>Mz_QzMoney,System.Decimal</value>
          <value>Mz_JKKMoney,System.Decimal</value>
          <value>XjTy_Money,System.Decimal</value>
          <value>QtTy_Money,System.Decimal</value>
          <value>QzTy_Money,System.Decimal</value>
          <value>JkkTy_Money,System.Decimal</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>门诊日结单</Name>
        <NameInSource>门诊日结单</NameInSource>
      </门诊日结单>
      <门诊按收费方式日结单 Ref="3" type="DataTableSource" isKey="true">
        <Alias>门诊按收费方式日结单</Alias>
        <Columns isList="true" count="4">
          <value>Pay_Way_Name,System.String</value>
          <value>Pay_Way_Code,System.String</value>
          <value>Sf_Money,System.Decimal</value>
          <value>Tf_Money,System.Decimal</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>门诊按收费方式日结单</Name>
        <NameInSource>门诊按收费方式日结单</NameInSource>
      </门诊按收费方式日结单>
      <门诊按患者类别日结单 Ref="4" type="DataTableSource" isKey="true">
        <Alias>门诊按患者类别日结单</Alias>
        <Columns isList="true" count="4">
          <value>Bxlb_Code,System.String</value>
          <value>Bxlb_Name,System.String</value>
          <value>Mz_SfMoney,System.Decimal</value>
          <value>Mz_Tfmoney,System.Decimal</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>门诊按患者类别日结单</Name>
        <NameInSource>门诊按患者类别日结单</NameInSource>
      </门诊按患者类别日结单>
    </DataSources>
    <Relations isList="true" count="0" />
    <Report isRef="0" />
    <Variables isList="true" count="15">
      <value>,操作员,操作员,System.String,,False,False</value>
      <value>,标题,标题,System.String,,False,False</value>
      <value>,结算时间,结算时间,System.String,,False,False</value>
      <value>,收费笔数,收费笔数,System.String,,False,False</value>
      <value>,退费笔数,退费笔数,System.String,,False,False</value>
      <value>,起始门诊编号,起始门诊编号,System.String,,False,False</value>
      <value>,终止门诊编号,终止门诊编号,System.String,,False,False</value>
      <value>,收入总额,收入总额,System.String,,False,False</value>
      <value>,现金收入,现金收入,System.String,,False,False</value>
      <value>,非现金收入,非现金收入,System.String,,False,False</value>
      <value>,现金退费,现金退费,System.String,,False,False</value>
      <value>,刷卡退费,刷卡退费,System.String,,False,False</value>
      <value>,收费处,收费处,System.String,,False,False</value>
      <value>,退费发票号,退费发票号,System.String,,False,False</value>
      <value>,打印日期,打印日期,System.String,,False,False</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="5" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="10">
        <ReportTitleBand1 Ref="6" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,18.6,2</ClientRectangle>
          <Components isList="true" count="9">
            <Text1 Ref="7" type="Text" isKey="true">
              <Border>Bottom;Black;1;Double;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,18.6,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>黑体,15,Regular,Point,False,134</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text1</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>{标题}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text1>
            <Text2 Ref="8" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>12,0.7,6.6,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text2</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>打印日期：{打印日期}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="9" type="CustomFormat" isKey="true">
                <StringFormat>yyyy-MM-dd HH:mm:ss</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text2>
            <Text4 Ref="10" type="Text" isKey="true">
              <Border>None;Black;1;Double;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2,0.7,10,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text4</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>结算时间：{结算时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text4>
            <Text3 Ref="11" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.4,1.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,0</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text3</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>收费笔数：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text3>
            <Text6 Ref="12" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.6,1.4,3,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text6</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>{收费笔数}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text6>
            <Text8 Ref="13" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.6,1.4,1.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,0</Font>
              <Guid>19461d0b8f3c4772968276d3ab7391e9</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text8</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>退费笔数：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text8>
            <Text10 Ref="14" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>6.2,1.4,3,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>cf79843f70f041a49b8b259a58b26580</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text10</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>{退费笔数}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text10>
            <Text23 Ref="15" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>10.2,1.4,1.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,134</Font>
              <Guid>44cff650198a4a57beb13724f5fa0091</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text23</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>实收总额：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text23>
            <Text24 Ref="16" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>11.8,1.4,2.5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>394d8376a80c4e14bf372862bf984c4f</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text24</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>{收入总额}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="17" type="CustomFormat" isKey="true">
                <StringFormat>0.00####</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text24>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>ReportTitleBand1</Name>
          <Page isRef="5" />
          <Parent isRef="5" />
        </ReportTitleBand1>
        <GroupHeaderBand1 Ref="18" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,3.2,18.6,1.5</ClientRectangle>
          <Components isList="true" count="11">
            <Text40 Ref="19" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,18.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,134</Font>
              <Guid>aeefa320dba4479cb7949e64f73c0d91</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text40</Name>
              <Page isRef="5" />
              <Parent isRef="18" />
              <Text>按缴费方式（现金、非现金）</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text40>
            <Text12 Ref="20" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.5,2.3,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,0</Font>
              <Guid>8b57b939c78a457e99acf4a46ceea9cc</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text12</Name>
              <Page isRef="5" />
              <Parent isRef="18" />
              <Text>项目类别</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text12>
            <Text14 Ref="21" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.3,0.5,5.4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,0</Font>
              <Guid>44857952b83049089ae8a4deaef20e9c</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text14</Name>
              <Page isRef="5" />
              <Parent isRef="18" />
              <Text>收费方式</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text14>
            <Text16 Ref="22" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.7,0.5,5.4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,0</Font>
              <Guid>bc301a38d0e248e8b449e5479e2eb4ce</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text16</Name>
              <Page isRef="5" />
              <Parent isRef="18" />
              <Text>退费方式</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text16>
            <Text29 Ref="23" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.3,1,2.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,0</Font>
              <Guid>ec757d45f5184ec08f1954a22d50fa3c</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text29</Name>
              <Page isRef="5" />
              <Parent isRef="18" />
              <Text>现金</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text29>
            <Text30 Ref="24" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>5,1,2.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,0</Font>
              <Guid>0e7053dab55b4caa8dbcf20cf609761d</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text30</Name>
              <Page isRef="5" />
              <Parent isRef="18" />
              <Text>非现金</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text30>
            <Text17 Ref="25" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.7,1,2.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,0</Font>
              <Guid>31fc12bc85684789839fdf4b585022ad</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text17</Name>
              <Page isRef="5" />
              <Parent isRef="18" />
              <Text>现金</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text17>
            <Text18 Ref="26" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>10.4,1,2.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,0</Font>
              <Guid>56b40c4a943844b097867495d2bd63d5</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text18</Name>
              <Page isRef="5" />
              <Parent isRef="18" />
              <Text>非现金</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text18>
            <Text65 Ref="27" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13.1,0.5,5.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,134</Font>
              <Guid>409707704898465ea6ca94f3271c8f95</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text65</Name>
              <Page isRef="5" />
              <Parent isRef="18" />
              <Text>费用合计</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text65>
            <Text66 Ref="28" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13.1,1,2.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,134</Font>
              <Guid>e004dfcd8e6b4253879a55d7157f23d9</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text66</Name>
              <Page isRef="5" />
              <Parent isRef="18" />
              <Text>现金</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text66>
            <Text67 Ref="29" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15.8,1,2.8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,134</Font>
              <Guid>e9d876388a2947bb9e138297a2baac1e</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text67</Name>
              <Page isRef="5" />
              <Parent isRef="18" />
              <Text>非现金</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text67>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>GroupHeaderBand1</Name>
          <Page isRef="5" />
          <Parent isRef="5" />
        </GroupHeaderBand1>
        <DataBand1 Ref="30" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,5.5,18.6,0.5</ClientRectangle>
          <Columns>1</Columns>
          <Components isList="true" count="7">
            <Text5 Ref="31" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2.3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text5</Name>
              <Page isRef="5" />
              <Parent isRef="30" />
              <Text>{门诊日结单.Mz_Lb}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text5>
            <Text7 Ref="32" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.3,0,2.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <HideZeros>True</HideZeros>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text7</Name>
              <NullValue>-</NullValue>
              <Page isRef="5" />
              <Parent isRef="30" />
              <Text>{门诊日结单.Mz_XjMoney}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="33" type="CustomFormat" isKey="true">
                <StringFormat>0.00##</StringFormat>
              </TextFormat>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text7>
            <Text9 Ref="34" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>5,0,2.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>f4884ead2475439aa24686a819614b4c</Guid>
              <HideZeros>True</HideZeros>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text9</Name>
              <NullValue>-</NullValue>
              <Page isRef="5" />
              <Parent isRef="30" />
              <Text>{门诊日结单.Mz_QtMoney}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="35" type="CustomFormat" isKey="true">
                <StringFormat>0.00##</StringFormat>
              </TextFormat>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text9>
            <Text11 Ref="36" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.7,0,2.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>cf5dee34adbc4772968870ce9b1f6314</Guid>
              <HideZeros>True</HideZeros>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text11</Name>
              <NullValue>-</NullValue>
              <Page isRef="5" />
              <Parent isRef="30" />
              <Text>{门诊日结单.XjTy_Money}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="37" type="CustomFormat" isKey="true">
                <StringFormat>0.00##</StringFormat>
              </TextFormat>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text11>
            <Text13 Ref="38" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>10.4,0,2.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>00763b6455124c13a2c033715be00e91</Guid>
              <HideZeros>True</HideZeros>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text13</Name>
              <NullValue>-</NullValue>
              <Page isRef="5" />
              <Parent isRef="30" />
              <Text>{门诊日结单.QtTy_Money}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="39" type="CustomFormat" isKey="true">
                <StringFormat>0.00##</StringFormat>
              </TextFormat>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text13>
            <Text69 Ref="40" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13.1,0,2.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>ff86910c1e4d4c9facac356db1fa8cdf</Guid>
              <HideZeros>True</HideZeros>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text69</Name>
              <NullValue>-</NullValue>
              <Page isRef="5" />
              <Parent isRef="30" />
              <Text>{门诊日结单.Mz_XjMoney-门诊日结单.XjTy_Money}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="41" type="CustomFormat" isKey="true">
                <StringFormat>0.00##</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text69>
            <Text70 Ref="42" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15.8,0,2.8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>622cdad71e1a43aca15c1d07a11e0f36</Guid>
              <HideZeros>True</HideZeros>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text70</Name>
              <NullValue>-</NullValue>
              <Page isRef="5" />
              <Parent isRef="30" />
              <Text>{门诊日结单.Mz_QtMoney-门诊日结单.QtTy_Money}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="43" type="CustomFormat" isKey="true">
                <StringFormat>0.00##</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text70>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>门诊日结单</DataSourceName>
          <FilterMode>Or</FilterMode>
          <Filters isList="true" count="4">
            <value>Mz_XjMoney,NotEqualTo,_x0030_,,Numeric</value>
            <value>Mz_QtMoney,NotEqualTo,_x0030_,,Numeric</value>
            <value>XjTy_Money,NotEqualTo,_x0030_,,Numeric</value>
            <value>QtTy_Money,NotEqualTo,_x0030_,,Numeric</value>
          </Filters>
          <Name>DataBand1</Name>
          <Page isRef="5" />
          <Parent isRef="5" />
          <Sort isList="true" count="4">
            <value>ASC</value>
            <value>V_Lb</value>
            <value>ASC</value>
            <value>Mz_Lb</value>
          </Sort>
        </DataBand1>
        <GroupFooterBand1 Ref="44" type="GroupFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,6.8,18.6,1.6</ClientRectangle>
          <Components isList="true" count="11">
            <Text31 Ref="45" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2.3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>2ece465b0c8441b0a5400c23975ba64f</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text31</Name>
              <Page isRef="5" />
              <Parent isRef="44" />
              <Text>金额合计</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text31>
            <Text32 Ref="46" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.3,0,2.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>a6f6f650d53d48d4b24f359bd6c84998</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text32</Name>
              <NullValue>-</NullValue>
              <Page isRef="5" />
              <Parent isRef="44" />
              <Text>{Sum(DataBand1,门诊日结单.Mz_XjMoney)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="47" type="CustomFormat" isKey="true">
                <StringFormat>0.00##</StringFormat>
              </TextFormat>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text32>
            <Text33 Ref="48" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>5,0,2.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>7d079740802d4f01aa70e7dbd9aeb3b9</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text33</Name>
              <NullValue>-</NullValue>
              <Page isRef="5" />
              <Parent isRef="44" />
              <Text>{Sum(DataBand1,门诊日结单.Mz_QtMoney)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="49" type="CustomFormat" isKey="true">
                <StringFormat>0.00##</StringFormat>
              </TextFormat>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text33>
            <Text34 Ref="50" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.7,0,2.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>359480692d0d4df787e867cd29850714</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text34</Name>
              <NullValue>-</NullValue>
              <Page isRef="5" />
              <Parent isRef="44" />
              <Text>{Sum(DataBand1,门诊日结单.XjTy_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="51" type="CustomFormat" isKey="true">
                <StringFormat>0.00##</StringFormat>
              </TextFormat>
              <Type>Totals</Type>
              <VertAlignment>Center</VertAlignment>
            </Text34>
            <Text35 Ref="52" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>10.4,0,2.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>547ee6d8789b48e1a0afccf1fb73dc18</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text35</Name>
              <NullValue>-</NullValue>
              <Page isRef="5" />
              <Parent isRef="44" />
              <Text>{Sum(DataBand1,门诊日结单.QtTy_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="53" type="CustomFormat" isKey="true">
                <StringFormat>0.00##</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text35>
            <Text72 Ref="54" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13.1,0,2.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>9d1eedea501d41fda8b8b1b886acb251</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text72</Name>
              <NullValue>-</NullValue>
              <Page isRef="5" />
              <Parent isRef="44" />
              <Text>{Sum(DataBand1,门诊日结单.Mz_XjMoney)-Sum(DataBand1,门诊日结单.XjTy_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="55" type="CustomFormat" isKey="true">
                <StringFormat>0.00##</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text72>
            <Text73 Ref="56" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15.8,0,2.8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>8bc96ee796d042cf911dc705236902e9</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text73</Name>
              <NullValue>-</NullValue>
              <Page isRef="5" />
              <Parent isRef="44" />
              <Text>{Sum(DataBand1,门诊日结单.Mz_QtMoney)-Sum(DataBand1,门诊日结单.QtTy_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="57" type="CustomFormat" isKey="true">
                <StringFormat>0.00##</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text73>
            <Text25 Ref="58" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4,0.8,1.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,134</Font>
              <Guid>8af1ed517d504d2e9507d64bac651cbd</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text25</Name>
              <Page isRef="5" />
              <Parent isRef="44" />
              <Text>现金实收：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text25>
            <Text41 Ref="59" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>5.6,0.8,3.9,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>c72e697265764c8c8b5eba5140f60a10</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text41</Name>
              <Page isRef="5" />
              <Parent isRef="44" />
              <Text>{现金收入}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="60" type="CustomFormat" isKey="true">
                <StringFormat>0.00####</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text41>
            <Text42 Ref="61" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>10.2,0.8,2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,134</Font>
              <Guid>c9fd50596c4842d2b9c37f517bb9dae5</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text42</Name>
              <Page isRef="5" />
              <Parent isRef="44" />
              <Text>非现金实收：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text42>
            <Text43 Ref="62" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>12.2,0.8,3.3,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>d3c4454765cb43b2b196d6707223c7bc</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text43</Name>
              <Page isRef="5" />
              <Parent isRef="44" />
              <Text>{非现金收入}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="63" type="CustomFormat" isKey="true">
                <StringFormat>0.00####</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text43>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>GroupFooterBand1</Name>
          <Page isRef="5" />
          <Parent isRef="5" />
        </GroupFooterBand1>
        <GroupHeaderBand3 Ref="64" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,9.2,18.6,1</ClientRectangle>
          <Components isList="true" count="9">
            <Text86 Ref="65" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,18.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,134</Font>
              <Guid>39649ef1f69341f298163ecae0cf02dd</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text86</Name>
              <Page isRef="5" />
              <Parent isRef="64" />
              <Text>按收费方式</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text86>
            <Text26 Ref="66" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.5,2.3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,0</Font>
              <Guid>614c5b81b2074c51876004ad700ea2c0</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text26</Name>
              <Page isRef="5" />
              <Parent isRef="64" />
              <Text>收费方式</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text26>
            <Text36 Ref="67" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.3,0.5,2.3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,0</Font>
              <Guid>26db16710a404c86be3672373fd0d684</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text36</Name>
              <Page isRef="5" />
              <Parent isRef="64" />
              <Text>收费金额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text36>
            <Text37 Ref="68" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.6,0.5,2.3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,0</Font>
              <Guid>603a47d7a02f42e4ac723e2cc1638f59</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text37</Name>
              <Page isRef="5" />
              <Parent isRef="64" />
              <Text>退费金额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text37>
            <Text44 Ref="69" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6.9,0.5,2.4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,0</Font>
              <Guid>387da80577774e5996a19627da24c808</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text44</Name>
              <Page isRef="5" />
              <Parent isRef="64" />
              <Text>合计</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text44>
            <Text45 Ref="70" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9.3,0.5,2.3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,0</Font>
              <Guid>a70890c8a8e442abb237cdceeba94dbb</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text45</Name>
              <Page isRef="5" />
              <Parent isRef="64" />
              <Text>收费方式</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text45>
            <Text68 Ref="71" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>11.6,0.5,2.3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,0</Font>
              <Guid>d9202b89ea4e4a5181ae37cfb5e14a34</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text68</Name>
              <Page isRef="5" />
              <Parent isRef="64" />
              <Text>收费金额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text68>
            <Text74 Ref="72" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13.9,0.5,2.3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,0</Font>
              <Guid>ae0fbd495da049b8b169de1640336748</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text74</Name>
              <Page isRef="5" />
              <Parent isRef="64" />
              <Text>退费金额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text74>
            <Text75 Ref="73" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>16.2,0.5,2.4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,0</Font>
              <Guid>360ee6533f434c529184a3e661c879b8</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text75</Name>
              <Page isRef="5" />
              <Parent isRef="64" />
              <Text>合计</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text75>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>GroupHeaderBand3</Name>
          <Page isRef="5" />
          <Parent isRef="5" />
        </GroupHeaderBand3>
        <DataBand3 Ref="74" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,11,18.6,0.5</ClientRectangle>
          <Columns>2</Columns>
          <Components isList="true" count="4">
            <Text15 Ref="75" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2.3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>2364a2216038445f839b88bec4af58ec</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text15</Name>
              <Page isRef="5" />
              <Parent isRef="74" />
              <Text>{门诊按收费方式日结单.Pay_Way_Name}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text15>
            <Text27 Ref="76" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.3,0,2.3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>bd4303832d1d4365ad4929c2be3c1e06</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text27</Name>
              <NullValue> </NullValue>
              <Page isRef="5" />
              <Parent isRef="74" />
              <Text>{门诊按收费方式日结单.Sf_Money}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="77" type="CustomFormat" isKey="true">
                <StringFormat>0.00####</StringFormat>
              </TextFormat>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text27>
            <Text28 Ref="78" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.6,0,2.3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>3f236e8fd6e94b97a0eda9afda3c5f11</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text28</Name>
              <NullValue> </NullValue>
              <Page isRef="5" />
              <Parent isRef="74" />
              <Text>{门诊按收费方式日结单.Tf_Money}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="79" type="CustomFormat" isKey="true">
                <StringFormat>0.00####</StringFormat>
              </TextFormat>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text28>
            <Text71 Ref="80" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6.9,0,2.4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>1fde3056b2544c6c976c2ec8ffaabdf3</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text71</Name>
              <NullValue>    </NullValue>
              <Page isRef="5" />
              <Parent isRef="74" />
              <Text>{门诊按收费方式日结单.Sf_Money-门诊按收费方式日结单.Tf_Money}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="81" type="CustomFormat" isKey="true">
                <StringFormat>0.00####</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text71>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>门诊按收费方式日结单</DataSourceName>
          <FilterMode>Or</FilterMode>
          <Filters isList="true" count="0" />
          <Name>DataBand3</Name>
          <Page isRef="5" />
          <Parent isRef="5" />
          <Sort isList="true" count="0" />
        </DataBand3>
        <GroupHeaderBand2 Ref="82" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,12.3,18.6,1</ClientRectangle>
          <Components isList="true" count="9">
            <Text39 Ref="83" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,18.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,134</Font>
              <Guid>d38060dc8b1a41a1918b3765edaaf068</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text39</Name>
              <Page isRef="5" />
              <Parent isRef="82" />
              <Text>按患者类别</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text39>
            <Text46 Ref="84" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.5,2.3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,0</Font>
              <Guid>73239bdf360541fab9a9195a27794c33</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text46</Name>
              <Page isRef="5" />
              <Parent isRef="82" />
              <Text>患者类别</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text46>
            <Text47 Ref="85" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.3,0.5,2.3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,0</Font>
              <Guid>d4f317e2ecc1422e95519d7f9c38fdfd</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text47</Name>
              <Page isRef="5" />
              <Parent isRef="82" />
              <Text>收费金额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text47>
            <Text56 Ref="86" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.6,0.5,2.3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,0</Font>
              <Guid>9193d5a6e204444e96903b759519c99c</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text56</Name>
              <Page isRef="5" />
              <Parent isRef="82" />
              <Text>退费金额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text56>
            <Text62 Ref="87" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6.9,0.5,2.4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,0</Font>
              <Guid>197078c2b9474d60ab0b1ff60e8909b5</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text62</Name>
              <Page isRef="5" />
              <Parent isRef="82" />
              <Text>合计</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text62>
            <Text57 Ref="88" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9.3,0.5,2.3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,0</Font>
              <Guid>fefbfa01cdee4ce4a1cb81ead2146070</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text57</Name>
              <Page isRef="5" />
              <Parent isRef="82" />
              <Text>患者类别</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text57>
            <Text58 Ref="89" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>11.6,0.5,2.3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,0</Font>
              <Guid>6d0aed8d041b4744bb286d26d08e5fe6</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text58</Name>
              <Page isRef="5" />
              <Parent isRef="82" />
              <Text>收费金额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text58>
            <Text59 Ref="90" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13.9,0.5,2.3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,0</Font>
              <Guid>085dd5f24bcb4fc9acc21c29473f1341</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text59</Name>
              <Page isRef="5" />
              <Parent isRef="82" />
              <Text>退费金额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text59>
            <Text64 Ref="91" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>16.2,0.5,2.4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,0</Font>
              <Guid>ee24c826dcd74cc1b1daa20a96ad8794</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text64</Name>
              <Page isRef="5" />
              <Parent isRef="82" />
              <Text>合计</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text64>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>GroupHeaderBand2</Name>
          <Page isRef="5" />
          <Parent isRef="5" />
        </GroupHeaderBand2>
        <DataBand2 Ref="92" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,14.1,18.6,0.5</ClientRectangle>
          <Columns>2</Columns>
          <Components isList="true" count="4">
            <Text38 Ref="93" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2.3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>ac0a30b3f9de4e2cbf803efa8ceb5379</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text38</Name>
              <Page isRef="5" />
              <Parent isRef="92" />
              <Text>{门诊按患者类别日结单.Bxlb_Name}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text38>
            <Text60 Ref="94" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.3,0,2.3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>9d56cfd6d968406b8315d42401beba08</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text60</Name>
              <NullValue> </NullValue>
              <Page isRef="5" />
              <Parent isRef="92" />
              <Text>{门诊按患者类别日结单.Mz_SfMoney}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="95" type="CustomFormat" isKey="true">
                <StringFormat>0.00####</StringFormat>
              </TextFormat>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text60>
            <Text61 Ref="96" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.6,0,2.3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>0370b00fa4534fa38d93d98099076cc1</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text61</Name>
              <NullValue> </NullValue>
              <Page isRef="5" />
              <Parent isRef="92" />
              <Text>{门诊按患者类别日结单.Mz_Tfmoney}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="97" type="CustomFormat" isKey="true">
                <StringFormat>0.00####</StringFormat>
              </TextFormat>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text61>
            <Text63 Ref="98" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6.9,0,2.4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>1a96fe00f1f84ae4afd37916bfefc5ec</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text63</Name>
              <NullValue>    </NullValue>
              <Page isRef="5" />
              <Parent isRef="92" />
              <Text>{IIF(门诊按患者类别日结单.Bxlb_Code=="9999","-",门诊按患者类别日结单.Mz_SfMoney-门诊按患者类别日结单.Mz_Tfmoney)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="99" type="CustomFormat" isKey="true">
                <StringFormat>0.00####</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text63>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>门诊按患者类别日结单</DataSourceName>
          <Filters isList="true" count="0" />
          <Name>DataBand2</Name>
          <Page isRef="5" />
          <Parent isRef="5" />
          <Sort isList="true" count="2">
            <value>ASC</value>
            <value>Bxlb_Code</value>
          </Sort>
        </DataBand2>
        <GroupFooterBand2 Ref="100" type="GroupFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,15.4,18.6,0</ClientRectangle>
          <Components isList="true" count="0" />
          <Conditions isList="true" count="0" />
          <Name>GroupFooterBand2</Name>
          <Page isRef="5" />
          <Parent isRef="5" />
        </GroupFooterBand2>
        <ReportSummaryBand1 Ref="101" type="ReportSummaryBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,16.2,18.6,1.9</ClientRectangle>
          <Components isList="true" count="12">
            <Text19 Ref="102" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.1,2.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,0</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text19</Name>
              <Page isRef="5" />
              <Parent isRef="101" />
              <Text>起始门诊编号：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text19>
            <Text20 Ref="103" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.2,0.1,5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text20</Name>
              <Page isRef="5" />
              <Parent isRef="101" />
              <Text>{起始门诊编号}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text20>
            <Text21 Ref="104" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7.2,0.1,2.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,0</Font>
              <Guid>f771592ddd5e45569e13a861e1251b9c</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text21</Name>
              <Page isRef="5" />
              <Parent isRef="101" />
              <Text>终止门诊编号：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text21>
            <Text22 Ref="105" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>9.4,0.1,5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>6a861c1b153943be85af8561489e72dd</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text22</Name>
              <Page isRef="5" />
              <Parent isRef="101" />
              <Text>{终止门诊编号}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text22>
            <Text48 Ref="106" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.7,2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,0</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text48</Name>
              <Page isRef="5" />
              <Parent isRef="101" />
              <Text>退费发票号：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text48>
            <Text49 Ref="107" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2,0.7,16.5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text49</Name>
              <Page isRef="5" />
              <Parent isRef="101" />
              <Text>{退费发票号}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text49>
            <Text50 Ref="108" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.3,1.4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,0</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text50</Name>
              <Page isRef="5" />
              <Parent isRef="101" />
              <Text>收费处：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text50>
            <Text51 Ref="109" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>5.4,1.3,1.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,0</Font>
              <Guid>369f219ad2a2493288969724e9fe33d5</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text51</Name>
              <Page isRef="5" />
              <Parent isRef="101" />
              <Text>药房签字：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text51>
            <Text52 Ref="110" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>11,1.3,1.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,0</Font>
              <Guid>c686db24119b48dba4221964f5f2863a</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text52</Name>
              <Page isRef="5" />
              <Parent isRef="101" />
              <Text>会计签字：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text52>
            <Text53 Ref="111" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.4,1.3,4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,0</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text53</Name>
              <Page isRef="5" />
              <Parent isRef="101" />
              <Text>{收费处}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text53>
            <Text54 Ref="112" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7,1.3,4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>2ab1848e61874f21a836eb529a5cdbe8</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text54</Name>
              <Page isRef="5" />
              <Parent isRef="101" />
              <TextBrush>Black</TextBrush>
            </Text54>
            <Text55 Ref="113" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>12.6,1.3,4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>10066f814e1345fa9e701643d6c32064</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text55</Name>
              <Page isRef="5" />
              <Parent isRef="101" />
              <TextBrush>Black</TextBrush>
            </Text55>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>ReportSummaryBand1</Name>
          <Page isRef="5" />
          <Parent isRef="5" />
        </ReportSummaryBand1>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>2a1844cd22fa45a493e6260cccb64b3b</Guid>
      <Margins>1.3,1.1,1,1</Margins>
      <Name>Page1</Name>
      <PageHeight>29.7</PageHeight>
      <PageWidth>21</PageWidth>
      <Report isRef="0" />
      <Watermark Ref="114" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page1>
  </Pages>
  <PrinterSettings Ref="115" type="Stimulsoft.Report.Print.StiPrinterSettings" isKey="true" />
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>门诊日结单（181103)</ReportAlias>
  <ReportChanged>11/3/2018 2:21:14 PM</ReportChanged>
  <ReportCreated>12/22/2011 4:28:53 PM</ReportCreated>
  <ReportFile>.\Rpt\门诊日结new.mrt</ReportFile>
  <ReportGuid>0c6e7d2795f44d5f87d901c888e928ae</ReportGuid>
  <ReportName>门诊日结单（181103)</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2011.2.1026</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class Report : Stimulsoft.Report.StiReport
    {
        public Report()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
        #endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>