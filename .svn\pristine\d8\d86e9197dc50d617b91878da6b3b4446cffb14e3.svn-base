﻿/**  版本信息模板在安装目录下，可自行修改。
* M_LIS_TestXmDy.cs
*
* 功 能： N/A
* 类 名： M_LIS_TestXmDy
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/10/17 星期一 下午 1:38:53   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace ModelOld
{
	/// <summary>
	/// M_LIS_TestXmDy:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class M_LIS_TestXmDy
	{
		public M_LIS_TestXmDy()
		{}
		#region Model
		private string _xm_code;
		private string _testxm_code;
		/// <summary>
		/// 
		/// </summary>
		public string Xm_Code
		{
			set{ _xm_code=value;}
			get{return _xm_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string TestXm_Code
		{
			set{ _testxm_code=value;}
			get{return _testxm_code;}
		}
		#endregion Model

	}
}

