﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{472BFECE-5FDB-4EB3-8973-F9734FAF896B}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>ERX</RootNamespace>
    <AssemblyName>ERX</AssemblyName>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\output\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>x86</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="BouncyCastle.Crypto, Version=1.9.0.0, Culture=neutral, PublicKeyToken=0e99375e54769942, processorArchitecture=MSIL">
      <HintPath>..\packages\Portable.BouncyCastle.1.9.0\lib\net40\BouncyCastle.Crypto.dll</HintPath>
    </Reference>
    <Reference Include="C1.Win.4, Version=4.0.20222.566, Culture=neutral, PublicKeyToken=944ae1ea0e47ca04, processorArchitecture=MSIL" />
    <Reference Include="C1.Win.C1Command.4, Version=4.0.20222.566, Culture=neutral, PublicKeyToken=e808566f358766d8, processorArchitecture=MSIL" />
    <Reference Include="C1.Win.C1Input.4, Version=4.0.20222.566, Culture=neutral, PublicKeyToken=7e7ff60f0c214f9a, processorArchitecture=MSIL" />
    <Reference Include="C1.Win.C1List.4, Version=4.0.20222.566, Culture=neutral, PublicKeyToken=6b24f8f981dbd7bc, processorArchitecture=MSIL" />
    <Reference Include="C1.Win.C1Ribbon.4, Version=4.0.20222.566, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL" />
    <Reference Include="C1.Win.C1SplitContainer.4, Version=4.0.20222.566, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL" />
    <Reference Include="C1.Win.C1TrueDBGrid.4, Version=4.0.20222.566, Culture=neutral, PublicKeyToken=75ae3fb0e2b1e0da, processorArchitecture=MSIL" />
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.1\lib\net40\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="Stimulsoft.Report, Version=2011.2.1026.0, Culture=neutral, PublicKeyToken=ebe6666cba19647a, processorArchitecture=MSIL" />
    <Reference Include="Stimulsoft.Report.Win, Version=2011.2.1026.0, Culture=neutral, PublicKeyToken=ebe6666cba19647a, processorArchitecture=MSIL" />
    <Reference Include="SunnyUI, Version=3.1.9.0, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb, processorArchitecture=MSIL">
      <HintPath>..\packages\SunnyUI.3.1.9\lib\net40\SunnyUI.dll</HintPath>
    </Reference>
    <Reference Include="SunnyUI.Common, Version=3.1.2.0, Culture=neutral, PublicKeyToken=5a271fb7ba597231, processorArchitecture=MSIL">
      <HintPath>..\packages\SunnyUI.Common.3.1.2\lib\net40\SunnyUI.Common.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Design" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
    <Reference Include="YBControl, Version=1.0.0.0, Culture=neutral, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\output\YBControl.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="ERXApi.cs" />
    <Compile Include="ERXConfig.cs" />
    <Compile Include="Form\ErxQuery.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form\ErxQuery.Designer.cs">
      <DependentUpon>ErxQuery.cs</DependentUpon>
    </Compile>
    <Compile Include="Form\ERxQyJgQuery.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form\ERxQyJgQuery.Designer.cs">
      <DependentUpon>ERxQyJgQuery.cs</DependentUpon>
    </Compile>
    <Compile Include="Form\ERxUpload.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form\ERxUpload.Designer.cs">
      <DependentUpon>ERxUpload.cs</DependentUpon>
    </Compile>
    <Compile Include="Form\ErxypmlQuery.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form\ErxypmlQuery.Designer.cs">
      <DependentUpon>ErxypmlQuery.cs</DependentUpon>
    </Compile>
    <Compile Include="Form\Erx_Config.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form\Erx_Config.designer.cs">
      <DependentUpon>Erx_Config.cs</DependentUpon>
    </Compile>
    <Compile Include="Form\SelectYs.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form\SelectYs.Designer.cs">
      <DependentUpon>SelectYs.cs</DependentUpon>
    </Compile>
    <Compile Include="Model\MdlcircDrugQuery.cs" />
    <Compile Include="Model\MdlERX.cs" />
    <Compile Include="Model\MdlhospRxDetlQuery.cs" />
    <Compile Include="Model\MdlrxChkInfoQuery.cs" />
    <Compile Include="Model\MdlrxFileUpld.cs" />
    <Compile Include="Model\MdlrxFixmedinsSign.cs" />
    <Compile Include="Model\MdlrxSetlInfoQuery.cs" />
    <Compile Include="Model\MdlrxUndo.cs" />
    <Compile Include="Model\MdluploadChk.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\BLL\BLL.csproj">
      <Project>{46B795C2-6EFA-41E6-948E-66F92E591B6A}</Project>
      <Name>BLL</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common.BaseForm\Common.BaseForm.csproj">
      <Project>{1dd7020c-8603-438a-8015-34702dabc229}</Project>
      <Name>Common.BaseForm</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common.WinFormVar\Common.WinFormVar.csproj">
      <Project>{e267bdd2-634a-405b-bdbf-55354adbc027}</Project>
      <Name>Common.WinFormVar</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common\Common.csproj">
      <Project>{92e350a0-3691-4b8d-a07e-ebb0f10e6997}</Project>
      <Name>Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\CustomControl\CustomControl.csproj">
      <Project>{12bf4168-d60e-4a6c-85bf-926130ee6a6d}</Project>
      <Name>CustomControl</Name>
    </ProjectReference>
    <ProjectReference Include="..\CustomSunnyUI\CustomSunnyUI.csproj">
      <Project>{e12e3d1c-d546-447d-9cd7-1ac63e8d7f18}</Project>
      <Name>CustomSunnyUI</Name>
    </ProjectReference>
    <ProjectReference Include="..\YBBLL\YBBLL.csproj">
      <Project>{41e5b5ba-abbd-445c-90b1-ce381afbc5ce}</Project>
      <Name>YBBLL</Name>
    </ProjectReference>
    <ProjectReference Include="..\YBModel\YBModel.csproj">
      <Project>{3cb4dc25-92a0-49f4-a946-d9988b409c7c}</Project>
      <Name>YBModel</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisControl\ZTHisControl.csproj">
      <Project>{3cc56b11-c172-4753-89c8-eea972402626}</Project>
      <Name>ZTHisControl</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisEnum\ZTHisEnum.csproj">
      <Project>{940cdbcc-e9a4-4771-be47-343404a40123}</Project>
      <Name>ZTHisEnum</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisInsuranceAPI\ZTHisInsuranceAPI.csproj">
      <Project>{90f6db9b-772d-412e-8e79-c403cc58b9af}</Project>
      <Name>ZTHisInsuranceAPI</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisInsurance\ZTHisInsurance.csproj">
      <Project>{f6740b5c-0483-4def-9ede-849dae0923e0}</Project>
      <Name>ZTHisInsurance</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisInsurance_Enum\ZTHisInsurance_Enum.csproj">
      <Project>{731ec1b9-0bdd-413b-9ea5-441ec8b96031}</Project>
      <Name>ZTHisInsurance_Enum</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisPublicFunction\ZTHisPublicFunction.csproj">
      <Project>{484f5b0c-f19f-448d-b819-e183bfc2fd96}</Project>
      <Name>ZTHisPublicFunction</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisResources\ZTHisResources.csproj">
      <Project>{e7e57f38-534a-4aea-841e-9a869e3738ff}</Project>
      <Name>ZTHisResources</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisVar\ZTHisVar.csproj">
      <Project>{26bf0cc0-ae2a-459a-b41b-73f691f5299d}</Project>
      <Name>ZTHisVar</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Form\ErxQuery.resx">
      <DependentUpon>ErxQuery.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form\ERxQyJgQuery.resx">
      <DependentUpon>ERxQyJgQuery.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form\ERxUpload.resx">
      <DependentUpon>ERxUpload.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Form\ErxypmlQuery.resx">
      <DependentUpon>ErxypmlQuery.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form\Erx_Config.resx">
      <DependentUpon>Erx_Config.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form\SelectYs.resx">
      <DependentUpon>SelectYs.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\licenses.licx" />
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\查询.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\挂号撤销.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\批量上传.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\上传.png" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>