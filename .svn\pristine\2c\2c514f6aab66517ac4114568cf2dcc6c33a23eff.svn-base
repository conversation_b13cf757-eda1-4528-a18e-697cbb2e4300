﻿Imports System.Data.SqlClient
Imports System.Windows.Forms
Imports System.Drawing
Public Class LISMetaDic11

#Region "初始化"
    Dim rootOrNot As Boolean = True

    Dim My_Dataset As New DataSet
    Dim My_View As New DataView

    Dim V_Finish As Boolean = False             '初始化完成
    Dim V_SelectCode As String = ""

    Public My_Table As New DataTable
    Public My_Cm As CurrencyManager
    Public My_Row As DataRow
    Public V_Insert As Boolean
    Public V_FirstLoad As Boolean
    Dim BllEleOne As New BLLOld.B_LIS_Element1
    Dim BllEleTwo As New BLLOld.B_LIS_Element2

    Dim m_Rc As New BaseClass.C_RowChange
#End Region



    Private Sub LISMetaDic11_load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Call Form_Init()
        Call Tree_Init()
        Call Init_Data()
        AddHandler m_Rc.GridMoveEvent, AddressOf GridMove
    End Sub

#Region "窗体初始化"
    Private Sub Form_Init()
        Panel1.BorderStyle = BorderStyle.None
        Panel1.Height = 28
        ToolBar1.Location = New Point(1, 2)
        EleSxCb.Location = New Point(ToolBar1.Right + 2, ToolBar1.Top)
        T_Textbox.Location = New Point(EleSxCb.Right + 2, ToolBar1.Top + 1)
        T_Label.Location = New Point(T_Textbox.Right + 2, ToolBar1.Top + 4)
        T_Label.Width = HisVar.HisVar.DockTab.Width - T_Textbox.Right - 20

        With EleSxCb
            .Additem = "名称"
            .Additem = "简称"
            .DisplayColumns(1).Visible = False
            .DroupDownWidth = .Width - .CaptainWidth
            .SelectedIndex = 0
        End With

        Init_Grid()
    End Sub


    Private Sub Init_Grid()
        With MyGrid1
            .Init_Column("编号", "Element_Code", 100, "中", "", False)
            .Init_Column("名称", "Element_Name", 100, "中", "", False)
            .Init_Column("简称", "Element_Jc", 100, "中", "", False)
            .Init_Column("备注", "Element_Memo", 250, "中", "", False)
        End With
    End Sub


    Private Sub Tree_Init()
        With LISMetaDicTv
            .Nodes.Clear()
            .FullRowSelect = True
            .HideSelection = False
            .HotTracking = True
            .Scrollable = False
            .ShowRootLines = False
            .Sorted = False
        End With

        Dim My_Root As New TreeNode
        With My_Root
            .Tag = "00"
            .Text = "元字典"
            .ImageIndex = 0
        End With
        LISMetaDicTv.Nodes.Add(My_Root)

        Dim My_Reader As SqlDataReader
        My_Reader = HisVar.HisVar.Sqldal.ExecuteReader("SELECT Elementlb_Code,Elementlb_Name FROM LIS_Element1 order by Elementlb_Code")

        While My_Reader.Read
            Dim My_Node As New TreeNode
            With My_Node
                .Tag = My_Reader.Item(0).ToString
                .Text = My_Reader.Item(1).ToString
                .ImageIndex = 2
                .SelectedImageIndex = 1
            End With
            LISMetaDicTv.SelectedNode = My_Root
            LISMetaDicTv.SelectedNode.Nodes.Add(My_Node)
        End While
        My_Reader.Close()

        With LISMetaDicTv
            .SelectedNode = LISMetaDicTv.TopNode
            .SelectedNode.Expand()
            .Select()
        End With
        V_Finish = True
        rootOrNot = True
    End Sub

    Private Sub Init_Data()

        My_Dataset = BllEleTwo.GetAllList()

        My_Table = My_Dataset.Tables(0)

        My_Table.PrimaryKey = New DataColumn() {My_Table.Columns("Element_Code")}

        With Me.MyGrid1
            My_Cm = CType(BindingContext(My_Table, ""), CurrencyManager)
            .DataTable = My_Table

            T_Label.Text = "∑=" + .Splits(0).Rows.Count.ToString
        End With

        My_View = My_Cm.List
        T_Textbox.Text = ""
    End Sub

    Private Sub P_Init_Data(ByVal V_Dl_Code As String)
        Dim SelectSet As New DataSet
        SelectSet = BllEleTwo.GetList(" Elementlb_Code=" & V_Dl_Code & "")

        My_Table = SelectSet.Tables(0)
        My_Table.PrimaryKey = New DataColumn() {My_Table.Columns("Element_Code")}

        With Me.MyGrid1
            My_Cm = CType(BindingContext(My_Table, ""), CurrencyManager)
            .DataTable = My_Table

            T_Label.Text = "∑=" + .Splits(0).Rows.Count.ToString
        End With
        My_View = My_Cm.List
        T_Textbox.Text = ""
    End Sub


    Private Sub LISMetaDic11_FormClosing(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosingEventArgs)
        My_Dataset.Dispose()
    End Sub

#End Region


#Region "控件__动作"

    Private Sub TreeView1_AfterSelect(ByVal sender As System.Object, ByVal e As System.Windows.Forms.TreeViewEventArgs) Handles LISMetaDicTv.AfterSelect
        If V_Finish = False Then Exit Sub '     '初始化完成
        V_SelectCode = Trim(e.Node.Tag)         '获取选中节点
        If e.Node.Tag = "00" Then
            Call Init_Data()
            rootOrNot = True
        Else
            Call P_Init_Data(V_SelectCode)
            rootOrNot = False
        End If
    End Sub



    Private Sub C1TrueDBGrid1_RowColChange_1(ByVal sender As System.Object, ByVal e As C1.Win.C1TrueDBGrid.RowColChangeEventArgs) Handles MyGrid1.RowColChange
        If (MyGrid1.Row + 1) > MyGrid1.RowCount Then
        Else
            My_Row = My_Cm.List(MyGrid1.Row).Row
            m_Rc.ChangeRow(My_Row)
        End If
    End Sub

    Private Sub GridMove(ByVal s As String)
        With MyGrid1
            If .RowCount = 0 Then Exit Sub
            Select Case s
                Case "最前"
                    .MoveFirst()
                Case "上移"
                    .MovePrevious()
                Case "下移"
                    .MoveNext()
                Case "最后"
                    .MoveLast()
                    T_Label.Text = "∑=" + .Splits(0).Rows.Count.ToString
            End Select
        End With
    End Sub

    Private Sub Comm_Click(ByVal sender As Object, ByVal e As C1.Win.C1Command.ClickEventArgs) Handles Comm1.Click, Comm2.Click, Comm3.Click
        Select Case sender.text
            Case "增加"
                If rootOrNot = False Then
                    Call P_ShowData("增加")
                Else
                End If

            Case "删除"
                Beep()
                If MyGrid1.Splits(0).Rows.Count = 0 Then Exit Sub
                Call P_Del_Data()

            Case "更新"
                Call Init_Data()
                MyGrid1.Select()
                MyGrid1.MoveFirst()

                V_Finish = False
                Call Tree_Init()
                V_Finish = True
        End Select

    End Sub

    Private Sub T_Combo_Close(ByVal sender As Object, ByVal e As System.EventArgs)
        T_Textbox.Select()
    End Sub

    Private Sub C1TextBox1_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles T_Textbox.TextChanged
        Dim V_Str As String = ""
        If Trim(T_Textbox.Text & "") = "" Then
            V_Str = ""
        Else
            Select Case EleSxCb.Text

                Case "名称"
                    V_Str = "Element_Name Like '*" & Trim(T_Textbox.Text) & "*'"

                Case "简称"
                    V_Str = "Element_Jc Like '*" & Trim(T_Textbox.Text) & "*'"

            End Select
        End If

        With My_View
            .Sort = "Element_Code Asc"
            .RowFilter = V_Str
        End With
        T_Label.Text = "∑=" + MyGrid1.Splits(0).Rows.Count.ToString
    End Sub

    Private Sub C1TrueDBGrid1_MouseUp(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles MyGrid1.MouseUp
        If e.Button = Windows.Forms.MouseButtons.Right Then Call P_ShowData("DBGrid")
    End Sub

    Private Sub C1TrueDBGrid1_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles MyGrid1.KeyDown
        Select Case e.KeyCode
            Case Keys.Return
                Call P_ShowData("DBGrid")
            Case Keys.Delete
                If Me.MyGrid1.RowCount > 0 Then Call P_Del_Data()
        End Select
    End Sub


#End Region

#Region "自定义函数"



    Private Sub P_ShowData(ByVal V_Lb As String)
        If rootOrNot = False Then

            Dim codeForDic As String
            codeForDic = LISMetaDicTv.SelectedNode.Tag

            If V_Lb = "增加" Then
                V_Insert = True
            Else
                If Me.MyGrid1.RowCount > 0 Then V_Insert = False Else V_Insert = True
            End If

            If V_Insert = False Then
                My_Row = My_Cm.List(MyGrid1.Row).Row
            End If

            Dim vform As New LISMetaDic12(V_Insert, My_Row, My_Table, m_Rc, codeForDic)
            vform.Owner = Me
            vform.Show()
            MyGrid1.Select()
        Else
            Dim codeForDic As String
            codeForDic = My_Row.Item("Elementlb_Code")
            Dim vform As New LISMetaDic12(V_Insert, My_Row, My_Table, m_Rc, codeForDic)
            vform.Owner = Me
            vform.Show()
            MyGrid1.Select()
        End If

    End Sub

    Private Sub P_Del_Data()
        Beep()
        If MsgBox("是否删除:名称=" + Me.MyGrid1.Columns("Element_Name").Value + " ", MsgBoxStyle.Information + MsgBoxStyle.OkCancel + MsgBoxStyle.DefaultButton1, "提示:") = MsgBoxResult.Cancel Then Exit Sub
        My_Row = My_Cm.List(MyGrid1.Row).Row
        Try
            BllEleTwo.Delete(My_Row("Element_Code"))
            MyGrid1.Delete()
            My_Row.AcceptChanges()
            T_Label.Text = "∑=" + MyGrid1.Splits(0).Rows.Count.ToString
        Catch ex As Exception
            Beep()
            MsgBox("错误:" + ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
        Finally
            MyGrid1.Select()
        End Try
    End Sub



#End Region

End Class