﻿Imports System.Data.SqlClient
Imports Stimulsoft.Report


Public Class Cm_XmSj

#Region "变量初始化"

    Public My_Dataset As New DataSet
    Dim My_View As New DataView                 '视图
    Dim V_Finish As Boolean = False             '初始化完成

    Dim V_Jb As Integer              'Treeview1的选择级别
    Dim V_Code As String               '选择级别的代码
    Dim Str1 As String

    Public My_Adapter1 As New SqlDataAdapter
    Public My_Adapter2 As New SqlDataAdapter
    Public My_Table As New DataTable
    Public My_Cm As CurrencyManager             '同步指针
    Public My_Row As DataRow                    '当前选择行
    Public V_FirstLoad As Boolean               '第一次调入明细表
    Public V_Count As Integer                   '节点数量
#End Region

    '加载事件
    Private Sub Zd_Jb1_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Call Init_TDBGrid()
        Call Init_Tree()
        Call Init_Data()
    End Sub

#Region "窗体初始化"

    '单击树形菜单事件
    Private Sub TreeView1_AfterSelect(ByVal sender As System.Object, ByVal e As System.Windows.Forms.TreeViewEventArgs) Handles TreeView1.AfterSelect

        If V_Finish = False Then Exit Sub '     '初始化完成

        If Me.TreeView1.SelectedNode.Tag = "0" Then
            My_View.RowFilter = ""
        Else
            My_View.RowFilter = "Xmlb_Code='" & TreeView1.SelectedNode.Tag & "'"
        End If
    End Sub


    '树形菜单控制事件
    Private Sub TreeView1_BeforeCollapse(ByVal sender As Object, ByVal e As System.Windows.Forms.TreeViewCancelEventArgs) Handles TreeView1.BeforeCollapse
        '如果为TopNode不进行折叠
        If e.Node.Tag = TreeView1.TopNode.Tag Then e.Cancel = True
    End Sub

    '文本框内容发生改变的事件
    Private Sub T_Textbox_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles T_Textbox.TextChanged
        If V_Finish = True Then Call P_Filter()
    End Sub

#End Region



#Region "自定义方法"
    Private Sub Init_TDBGrid()

        '初始化TDBGrid
        Dim My_Grid As New BaseClass.C_Grid(Me.C1TrueDBGrid1)
        With My_Grid
            .Init_Grid()
            .P_Dock(BaseClass.C_Grid.Dock.Fill)
            .Init_Column("编码", "Xm_Code", 0, "中", "")
            .Init_Column("简称", "Xm_Jc", 100, "左", "")
            .Init_Column("名称", "Xm_Name", 200, "左", "")
            .Init_Column("计价单位", "Xm_Dw", 60, "左", "")
            .Init_Column("单价", "Xm_Dj", 70, "右", "###0.00")
            .AllSort(False)
        End With
        C1TrueDBGrid1.HScrollBar.Style = C1.Win.C1TrueDBGrid.ScrollBarStyleEnum.Automatic
    End Sub

    '树形菜单
    Private Sub Init_Tree()
        V_Finish = False

        With Me.TreeView1
            .Nodes.Clear()
            .FullRowSelect = True
            .HideSelection = False
            .HotTracking = True
            .Scrollable = False
            .ShowRootLines = False
            .Sorted = False
            .Width = 175
        End With

        '根节点
        Dim My_Reader As SqlDataReader
        My_Reader = HisVar.HisVar.Sqldal.ExecuteReader("select Zd_Ml_Xm3.* From Zd_Ml_Xm3,Zd_Ml_Xm1 where Zd_Ml_Xm1.Xmlb_Code = Zd_Ml_Xm3.Xmlb_Code and Xm_Dj<>0")
        My_Reader.Read()
        V_Count = My_Reader.Item(0)


        Dim My_Root As New TreeNode
        With My_Root
            .Tag = "0"
            .Text = "项目类别"
            .ImageIndex = 0
        End With
        My_Reader.Close()
        TreeView1.Nodes.Add(My_Root)

        '一级数据
        My_Reader = HisVar.HisVar.Sqldal.ExecuteReader("select Zd_Ml_Xm1.Xmlb_Code,Xmlb_Name from Zd_Ml_Xm1")

        While My_Reader.Read()
            Dim My_Node As New TreeNode
            With My_Node
                .Tag = My_Reader.Item(0).ToString
                .Text = My_Reader.Item("Xmlb_Name").ToString
                .ImageIndex = 1
                .SelectedImageIndex = 2
            End With
            TreeView1.SelectedNode = My_Root
            TreeView1.SelectedNode.Nodes.Add(My_Node)
        End While
        My_Reader.Close()
        My_Cn.Close()

        With TreeView1
            .SelectedNode = TreeView1.TopNode
            .SelectedNode.Expand()
            .Select()
        End With
        V_Finish = True

    End Sub

    'Load数据加载
    Private Sub Init_Data()
        Dim Str_Select As String = "select Zd_Ml_Xm1.Xmlb_Code,XmLb_Name,Xm_Code,Xm_Name,Xm_Jc,Xm_Dj,Xm_Dw From Zd_Ml_Xm3,Zd_Ml_Xm1 where Zd_Ml_Xm1.Xmlb_Code = Zd_Ml_Xm3.Xmlb_Code and xm_dj<>0"

        With My_Adapter1

            .SelectCommand = New SqlCommand(Str_Select, My_Cn)
            .Fill(My_Dataset, "项目类别")

            .AcceptChangesDuringFill = True
            .MissingSchemaAction = MissingSchemaAction.AddWithKey

        End With

        My_Table = My_Dataset.Tables("项目类别")

        With Me.C1TrueDBGrid1
            My_Cm = CType(BindingContext(My_Dataset, "项目类别"), CurrencyManager)
            .SetDataBinding(My_Dataset, "项目类别", True)

            T_Label.Text = "∑=" + .Splits(0).Rows.Count.ToString
        End With

        My_View = My_Cm.List
        T_Textbox.Text = ""

    End Sub

    '检索方法
    Private Sub P_Filter()
        Dim V_Str As String = ""
        Select Case TreeView1.SelectedNode.Tag
            Case "0"
                If Trim(T_Textbox.Text & "") = "" Then
                    V_Str = ""
                    My_View.Sort = "Xmlb_Code"
                Else
                    V_Str = "Xm_Jc Like '*" & Trim(T_Textbox.Text) & "*' or Xm_Name Like '*" & Trim(T_Textbox.Text) & "*'"
                End If


            Case Else
                If Trim(T_Textbox.Text & "") = "" Then
                    V_Str = " Xmlb_Code='" & TreeView1.SelectedNode.Tag & "'"
                    My_View.Sort = "Xmlb_Code"
                Else
                    V_Str = "SubString(Xmlb_Code,1,2)='" & TreeView1.SelectedNode.Tag & "' And (Xm_Jc Like '*" & Trim(T_Textbox.Text) & "*' or Xm_Name Like '*" & Trim(T_Textbox.Text) & "*')"
                End If
        End Select

        My_View.RowFilter = V_Str
        C1TrueDBGrid1.MoveFirst()
        T_Label.Text = "∑=" + C1TrueDBGrid1.Splits(0).Rows.Count.ToString

    End Sub

#End Region

    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click
        Dim Stirpt As New StiReport
        Stirpt.Load(".\Rpt\诊疗字典.mrt")
        Stirpt.ReportName = "诊疗字典"

        Stirpt.RegData(My_View)

        Stirpt.Compile()
        Stirpt("标题") = "诊疗项目价格表"
        Stirpt("打印时间") = Format(Now, "yyyy-MM-dd HH:mm:ss")

        ' Stirpt.Design()
        Stirpt.Show()
    End Sub
End Class