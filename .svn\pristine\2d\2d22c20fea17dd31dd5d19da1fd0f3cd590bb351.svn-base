﻿Imports System.Data.SqlClient
Imports Stimulsoft.Report
Imports BaseClass
Imports System.Text
Imports Model


Public Class Xs_Zy_Yj12

    'Public yjdlb As String
    Dim Bl_Jf As New BLLOld.B_Bl_Jf
    Dim Bl_Jf_Model As New ModelOld.M_Bl_Jf
    Dim Jkk_Consume As New BLLOld.B_Jkk_Consume
    Dim JKK_Consume_Model As New ModelOld.M_Jkk_Consume
    Dim Jkk_Cz_Model As New ModelOld.M_Jkk_Cz
    Dim Jkk_Cz As New BLLOld.B_Jkk_Cz
    Dim _bllZd_Identity As New BLL.BllZd_Identity



#Region "传参"
    Dim Rdate As Date
    Dim model_PatientInfo As ModelOld.M_PatientInfo
    Dim nurseDel As BaseClass.DelegateList.JnyjDelegate
#End Region

    Public Sub New(ByRef patient As ModelOld.M_PatientInfo, ByVal tdate As Date, ByVal _nurseDel As BaseClass.DelegateList.JnyjDelegate)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        Rdate = tdate
        model_PatientInfo = patient
        nurseDel = _nurseDel
        ' 在 InitializeComponent() 调用之后添加任何初始化。
    End Sub


    Protected Overrides Sub OnKeyDown(ByVal e As System.Windows.Forms.KeyEventArgs)
        MyBase.OnKeyDown(e)
        If e.Control = True And e.KeyCode = Keys.S Then
            BcComm.Select()
            Call BcComm_Click(BcComm, Nothing)
        End If
    End Sub

    Private Sub Xs_Zy_Yj12_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Call Form_Init()
        Call Data_Clear()
    End Sub

#Region "窗体__事件"

    Private Sub Form_Init()
        Panel1.Height = 28
        JfbmTextBox.Enabled = False
        '按扭初始化

        Call Form_Data()
    End Sub

    Private Sub Form_Data()     '病人字典
        With ByjcDtComobo
            .DataView = Bl_Jf.GetBrzdList(HisVar.HisVar.WsyCode).Tables(0).DefaultView
            .Init_Colum("Bl_Code", "编码", 0, "左")
            .Init_Colum("Ry_Jc", "简称", 60, "左")
            .Init_Colum("Ry_Name", "病人名称", 90, "左")
            .Init_Colum("Bc_Name", "床位名称", 90, "左")
            .Init_Colum("Ks_Name", "科室名称", 80, "左")
            .Init_Colum("Ys_Name", "医生名称", 80, "左")
            .Init_Colum("Yj_Money", "剩余押金", 60, "左")
            .Init_Colum("Ry_BlCode", "住院号", 60, "左")
            .Init_Colum("Identity_Code", "病人身份", 0, "左")
            .DisplayMember = "Ry_Name"
            .ValueMember = "Bl_Code"
            .RowFilterTextNull = ""
            .RowFilterNotTextNull = "Ry_Jc" '过滤字段
            .DroupDownWidth = 520
            .MaxDropDownItems = 17
            .SelectedIndex = -1
        End With

        With JffsComobo
            .Additem = "现金,1"
            .DisplayColumns(1).Visible = False
            .DroupDownWidth = .Width - .CaptainWidth
            .SelectedIndex = 0
        End With
    End Sub

#End Region

#Region "数据__操作"

    Private Sub Data_Clear()
        JfbmTextBox.Enabled = False

        JfbmTextBox.Text = Bl_Jf.MaxCode() 'My_Cc.编码

        MyNumericEdit1.Value = 0
        BzTextBox.Text = ""


        JffsComobo.SelectedIndex = 0
        Label7.Text = ""
        If model_PatientInfo IsNot Nothing Then
            ByjcDtComobo.SelectedValue = model_PatientInfo.Bl_Code
            MyNumericEdit1.Select()
        Else
            ByjcDtComobo.Text = ""
            ByjcDtComobo.SelectedIndex = -1
            ByjcDtComobo.Select()
        End If
    End Sub


#End Region

#Region "控件__动作"

    Private Sub BcComm_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BcComm.Click

        If Me.ByjcDtComobo.SelectedValue & "" = "" Then
            MessageBox.Show("请选择缴费病人！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Exit Sub
        End If
        If MyNumericEdit1.Text = "" Then
            MessageBox.Show("缴费金额不能为空！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Exit Sub
        End If
        Dim mdl As New MdlZd_Identity
        mdl = _bllZd_Identity.GetModel(ByjcDtComobo.Columns("Identity_Code").Value & "")
        If mdl IsNot Nothing AndAlso mdl.IsTip = True Then
            If MessageBox.Show($"该患者身份类别是【{mdl.Identity_Name}】，是否继续缴纳押金?", "提示", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) = DialogResult.No Then
                Exit Sub
            End If
        End If
        Call Data_Add()

        If MsgBox("是否打印押金单？", MsgBoxStyle.OkCancel + MsgBoxStyle.DefaultButton1, "提示：") = MsgBoxResult.Ok Then

            Dim StiRpt As New StiReport
            If iniOperate.iniopreate.GetINI("参数", "押金单样式", "", HisVar.HisVar.Parapath & "\Config.ini") = "套打押金单" Then
                StiRpt.Load("Rpt\住院押金缴费单(丰南).mrt")
                StiRpt.ReportName = "住院押金缴费单"

                Dim Yj_left As Double = IIf(iniOperate.iniopreate.GetINI("套打边距", "押金左边距", "", HisVar.HisVar.Parapath & "\Config.ini") = "", 0, iniOperate.iniopreate.GetINI("套打边距", "押金左边距", "", HisVar.HisVar.Parapath & "\Config.ini")) / 10
                Dim Yj_top As Double = IIf(iniOperate.iniopreate.GetINI("套打边距", "押金上边距", "", HisVar.HisVar.Parapath & "\Config.ini") = "", 0, iniOperate.iniopreate.GetINI("套打边距", "押金上边距", "", HisVar.HisVar.Parapath & "\Config.ini")) / 10
                StiRpt.Pages(0).Margins.Left = StiRpt.Pages(0).Margins.Left + Yj_left
                StiRpt.Pages(0).Margins.Top = StiRpt.Pages(0).Margins.Top + Yj_top
                StiRpt.Pages(0).PageHeight = StiRpt.Pages(0).PageHeight + Yj_top
                StiRpt.Pages(0).PageWidth = StiRpt.Pages(0).PageWidth + Yj_left

                StiRpt.Compile()
                StiRpt("入院编码") = ByjcDtComobo.Columns("Bl_Code").Value & ""
                StiRpt("单位名称") = HisVar.HisVar.WsyName
                StiRpt("交费日期") = CDate(Format(Now, "yyyy-MM-dd HH:mm"))
                StiRpt("单据号码") = "JF" & JfbmTextBox.Text
                StiRpt("姓名") = ByjcDtComobo.Columns("Ry_Name").Value & ""
                StiRpt("病历编码") = ByjcDtComobo.Columns("Ry_BlCode").Value & ""
                StiRpt("科室名称") = ByjcDtComobo.Columns("Ks_Name").Value & ""
                StiRpt("床位") = ByjcDtComobo.Columns("Bc_Name").Value & ""
                StiRpt("金额") = "￥" & MyNumericEdit1.Value
                StiRpt("收款人") = HisVar.HisVar.JsrName
            Else
                StiRpt.Load("Rpt\住院押金缴费单.mrt")
                StiRpt.ReportName = "住院押金缴费单"
                StiRpt.Compile()
                StiRpt("标题") = HisVar.HisVar.WsyName & "住院押金缴费单"
                StiRpt("打印日期") = "打印日期：" + Format(Now, "yyyy年MM月dd日")
                StiRpt("单据号码") = "JF" & JfbmTextBox.Text
                StiRpt("交费日期") = CDate(Format(Now, "yyyy-MM-dd HH:mm"))
                StiRpt("姓名") = ByjcDtComobo.Columns("Ry_Name").Value & ""
                StiRpt("入院编码") = ByjcDtComobo.Columns("Bl_Code").Value & ""
                StiRpt("病历编码") = ByjcDtComobo.Columns("Ry_BlCode").Value & ""
                StiRpt("科室名称") = ByjcDtComobo.Columns("Ks_Name").Value & ""
                StiRpt("金额") = "￥" & MyNumericEdit1.Value
                StiRpt("打印人") = "打印人：" & HisVar.HisVar.JsrName
                StiRpt("收款人") = HisVar.HisVar.JsrName
            End If

            Dim Money_Dx As New BaseClass.ChineseNum

            If MyNumericEdit1.Value >= 0 Then
                Money_Dx.InputString = MyNumericEdit1.Value
            Else
                Money_Dx.InputString = -MyNumericEdit1.Value
            End If

            If Money_Dx.Valiad = True Then
                If MyNumericEdit1.Value >= 0 Then
                    StiRpt("大写") = Money_Dx.OutString
                Else
                    StiRpt("大写") = "负" & Money_Dx.OutString

                End If
            Else
                MsgBox("输入金额有误,请检查后重新打印", MsgBoxStyle.Critical, "提示")
                Exit Sub
            End If

            'StiRpt.Design()
            StiRpt.Print(False, False)

        End If
        Call Data_Clear()
        MsgBox("保存完成！", MsgBoxStyle.Information, "提示")

    End Sub
    Private Sub QxComm_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles QxComm.Click
        Me.Close()
    End Sub

    Private Sub TextBox_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles MyNumericEdit1.KeyPress, JffsComobo.KeyPress, BzTextBox.KeyPress
        If e.KeyChar <> Chr(Keys.Return) Then Exit Sub
        e.Handled = True
        System.Windows.Forms.SendKeys.Send("{Tab}")
    End Sub

#Region "ByjcDtComobo"
    Private Sub ByjcDtComobo_RowChange(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ByjcDtComobo.RowChange
        If ByjcDtComobo.WillChangeToValue = "" Then
            Label7.Text = ""
        Else
            Label7.Text = Trim(ByjcDtComobo.Columns("Yj_Money").Value)
        End If
    End Sub

#End Region

#End Region

#Region "数据__编辑"

    Private Sub Data_Add()
        With Bl_Jf_Model
            .Yy_Code = HisVar.HisVar.WsyCode & ""
            .Bl_Code = ByjcDtComobo.SelectedValue & ""
            .Jf_Code = Bl_Jf.MaxCode()
            JfbmTextBox.Text = .Jf_Code
            .Jf_Date = Format(Rdate, "yyyy-MM-dd") & " " & Format(Now, "HH:mm:ss") & ""
            .Jsr_Code = HisVar.HisVar.JsrCode
            .Jf_Money = MyNumericEdit1.Value
            .Jf_Memo = Trim(BzTextBox.Text & "")
            .Bl_Jffs = JffsComobo.Text
            .Ry_Name = ByjcDtComobo.Text
            .Ks_Name = ByjcDtComobo.Columns("Ks_Name").Value & ""
            .Ys_Name = ByjcDtComobo.Columns("Ys_Name").Value & ""
            .Jsr_Name = HisVar.HisVar.JsrName
            .Ry_BlCode = ByjcDtComobo.Columns("Ry_BlCode").Value & ""
        End With
        '数据更新
        Try
            Bl_Jf.Add(Bl_Jf_Model)
            nurseDel(Bl_Jf_Model)
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            Exit Sub
        End Try
        '数据清空
    End Sub

#End Region



#Region "输入法设置"
    '中文
    Private Sub JfbmTextBox_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles JfbmTextBox.GotFocus
        InputLanguage.CurrentInputLanguage = HisVar.HisVar.InputCode
    End Sub
    '英文
    Private Sub C1DateEdit1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyNumericEdit1.GotFocus, ByjcDtComobo.GotFocus
        InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages(0)
    End Sub
#End Region
    
End Class
