﻿Imports BaseClass
Imports System.Drawing
Imports System.Windows.Forms
Public Class LISMetaDic12

#Region "变量定义"
    Dim My_Cc As New C_Cc
    Dim ModelElet As New ModelOld.M_LIS_Element2
    Dim BllElet As New BLLOld.B_LIS_Element2

#End Region

#Region "传参"

    Dim R<PERSON>ert As Boolean
    Dim Rrow As DataRow
    Dim RZbtb As DataTable
    Dim Rrc As C_RowChange
    Dim V_UP_Code As String
#End Region

    Public Sub New(ByVal tinsert As Boolean, ByVal trow As DataRow, ByVal tZbtb As DataTable, ByRef trc As C_RowChange, ByVal CodeForDic As String)
        InitializeComponent()
        Rinsert = tinsert
        Rrow = trow
        RZbtb = tZbtb
        Rrc = trc
        V_UP_Code = CodeForDic
    End Sub

    Private Sub JYYQGLDic12_FormClosed(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosedEventArgs) Handles Me.FormClosed
        RemoveHandler Rrc.RowChanged, AddressOf Data_Show
    End Sub

    Private Sub JYYQGLDic12_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        AddHandler Rrc.RowChanged, AddressOf Data_Show
        Call Form_Init()
        If Rinsert = True Then Call Data_Clear() Else Call Data_Show(Rrow)
    End Sub


#Region "窗体__事件"

    Private Sub Form_Init()
        Panel1.Height = 28
        ToolBar1.Location = New Point(2, 4)

        EleCodeTb.Enabled = False
        EleJcTb.Enabled = False

        Button1.Top = 1
        Button2.Location = New Point(Button1.Left + Button1.Width + 2, Button1.Top)

    End Sub

#End Region

#Region "数据__操作"

    Private Sub Data_Clear()
        Move5.Enabled = False                                   '新增记录
        Rinsert = True

        My_Cc.Get_MaxCode("LIS_Element2", "Element_Code", 7, "Elementlb_Code", V_UP_Code)
        EleCodeTb.Text = My_Cc.编码
        EleJcTb.Text = ""
        EleNameTb.Text = ""
        EleMemoTb.Text = ""


        Call Show_Label()
        Me.EleNameTb.Select()

    End Sub

    Private Sub Data_Show(ByVal tmp_Row As DataRow)
        Move5.Enabled = True
        Rrow = tmp_Row
        With Rrow

            EleCodeTb.Text = .Item("Element_Code") & ""
            EleJcTb.Text = .Item("Element_Jc") & ""
            EleNameTb.Text = .Item("Element_Name") & ""
            EleMemoTb.Text = .Item("Element_Memo") & ""

        End With
        Call Show_Label()
        Me.EleNameTb.Select()

    End Sub

    Private Sub Show_Label()
        EleNameTb.Select()
        If Rinsert = True Then
            Label2.Text = "新增"
        Else

            Label2.Text = IIf(RZbtb.Rows.Count() = 0, "0", CStr((RZbtb.Rows.IndexOf(Rrow)) + 1))
        End If
        Label3.Text = "∑=" & RZbtb.Rows.Count
    End Sub

#End Region

#Region "控件__动作"
    Private Sub Comm_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click, Button2.Click
        Select Case sender.tag
            Case "保存"
                If Rinsert = True Then     '增加记录
                    Call Data_Add()
                Else                                '编辑记录
                    Call Data_Edit()
                End If

            Case "取消"
                Me.Close()
        End Select
    End Sub

    Private Sub Move_Click(ByVal sender As Object, ByVal e As C1.Win.C1Command.ClickEventArgs) Handles Move1.Click, Move2.Click, Move3.Click, Move4.Click, Move5.Click
        If sender.text = "新增" Then                            '增加状态
            Call Data_Clear()
        Else
            Rinsert = False
            Rrc.GridMove(sender.text)

        End If
    End Sub

    Private Sub TextBox_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs)
        If e.KeyChar <> Chr(Keys.Return) Then Exit Sub
        e.Handled = True
        System.Windows.Forms.SendKeys.Send("{Tab}")
    End Sub

    Private Sub DevNameTb_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles EleNameTb.Validated
        My_Cc.Get_Py(Me.EleNameTb.Text & "")
        EleJcTb.Text = My_Cc.简拚.ToString
    End Sub
#End Region

#Region "数据__编辑"

    Private Sub Data_Add()
        My_Cc.Get_MaxCode("LIS_Element2", "Element_Code", 7, "Elementlb_Code", V_UP_Code)
        Dim My_NewRow As DataRow = RZbtb.NewRow
        With My_NewRow

            .Item("Elementlb_Code") = V_UP_Code
            .Item("Element_Name") = Trim(EleNameTb.Text & "")
            .Item("Element_Code") = My_Cc.编码
            .Item("Element_Jc") = Trim(EleJcTb.Text & "")
            .Item("Element_Memo") = Trim(EleMemoTb.Text & "")

        End With

        '数据保存
        Try
            RZbtb.Rows.Add(My_NewRow)
            Rrc.GridMove("最后")
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            Exit Sub
        Finally
            Me.EleNameTb.Select()
        End Try

        '数据更新
        Try
            With ModelElet
                .Elementlb_Code = My_NewRow("Elementlb_Code")
                .Element_Code = My_NewRow("Element_Code")
                .Element_Name = My_NewRow("Element_Name")
                .Element_Jc = My_NewRow("Element_Jc")
                .Element_Memo = My_NewRow("Element_Memo")

            End With
            BllElet.Add(ModelElet)



            My_NewRow.AcceptChanges()
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            Exit Sub
        Finally
            EleNameTb.Select()
        End Try

        '数据清空
        Call Data_Clear()

    End Sub

    Private Sub Data_Edit()
        Try
            With Rrow
                .BeginEdit()
                .Item("Elementlb_Code") = V_UP_Code
                .Item("Element_Name") = Trim(EleNameTb.Text & "")
                .Item("Element_Code") = Trim(EleCodeTb.Text & "")
                .Item("Element_Jc") = Trim(EleJcTb.Text & "")
                .Item("Element_Memo") = Trim(EleMemoTb.Text & "")

                .EndEdit()
            End With
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Critical, "提示:")
            Rrow.CancelEdit()
            Exit Sub
        Finally
            EleNameTb.Select()
        End Try

        '数据更新
        Try
            With ModelElet
                .Elementlb_Code = Rrow("Elementlb_Code")
                .Element_Code = Rrow("Element_Code")
                .Element_Name = Rrow("Element_Name")
                .Element_Jc = Rrow("Element_Jc")
                .Element_Memo = Rrow("Element_Memo")
            End With
            BllElet.Update(ModelElet)
            Rrow.AcceptChanges()
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
        Finally
            EleNameTb.Select()
        End Try
        ' End With

    End Sub

#End Region

#Region "输入法设置"
    '中文
    Private Sub C1TextBox1_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles EleNameTb.GotFocus, EleMemoTb.GotFocus
        InputLanguage.CurrentInputLanguage = HisVar.HisVar.InputCode
    End Sub

    '英文
    Private Sub C1TextBox2_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs)
        InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages(0)
    End Sub
#End Region

End Class