﻿/**  版本信息模板在安装目录下，可自行修改。
* D_Zd_Ml_Xm3.cs
*
* 功 能： N/A
* 类 名： D_Zd_Ml_Xm3
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016-10-21 11:03:50   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
namespace DAL
{
	/// <summary>
	/// 数据访问类:D_Zd_Ml_Xm3
	/// </summary>
	public partial class D_Zd_Ml_Xm3
	{
		public D_Zd_Ml_Xm3()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string Xm_Code)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from Zd_Ml_Xm3");
			strSql.Append(" where Xm_Code=@Xm_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Xm_Code", SqlDbType.Char,12)			};
			parameters[0].Value = Xm_Code;

			return HisVar.HisVar.Sqldal.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ModelOld.M_Zd_Ml_Xm3 model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into Zd_Ml_Xm3(");
			strSql.Append("Xmlb_Code,Xm_Code,Xm_Name,Xm_Jc,Xm_Dw,Xm_Dj,Xm_Memo)");
			strSql.Append(" values (");
			strSql.Append("@Xmlb_Code,@Xm_Code,@Xm_Name,@Xm_Jc,@Xm_Dw,@Xm_Dj,@Xm_Memo)");
			SqlParameter[] parameters = {
					new SqlParameter("@Xmlb_Code", SqlDbType.Char,2),
					new SqlParameter("@Xm_Code", SqlDbType.Char,12),
					new SqlParameter("@Xm_Name", SqlDbType.VarChar,200),
					new SqlParameter("@Xm_Jc", SqlDbType.VarChar,50),
					new SqlParameter("@Xm_Dw", SqlDbType.VarChar,200),
					new SqlParameter("@Xm_Dj", SqlDbType.Decimal,9),
					new SqlParameter("@Xm_Memo", SqlDbType.VarChar,500)};
			parameters[0].Value = model.Xmlb_Code;
			parameters[1].Value = model.Xm_Code;
			parameters[2].Value = model.Xm_Name;
			parameters[3].Value = model.Xm_Jc;
			parameters[4].Value = model.Xm_Dw;
			parameters[5].Value = model.Xm_Dj;
			parameters[6].Value = model.Xm_Memo;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ModelOld.M_Zd_Ml_Xm3 model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update Zd_Ml_Xm3 set ");
			strSql.Append("Xmlb_Code=@Xmlb_Code,");
			strSql.Append("Xm_Name=@Xm_Name,");
			strSql.Append("Xm_Jc=@Xm_Jc,");
			strSql.Append("Xm_Dw=@Xm_Dw,");
			strSql.Append("Xm_Dj=@Xm_Dj,");
			strSql.Append("Xm_Memo=@Xm_Memo");
			strSql.Append(" where Xm_Code=@Xm_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Xmlb_Code", SqlDbType.Char,2),
					new SqlParameter("@Xm_Name", SqlDbType.VarChar,200),
					new SqlParameter("@Xm_Jc", SqlDbType.VarChar,50),
					new SqlParameter("@Xm_Dw", SqlDbType.VarChar,200),
					new SqlParameter("@Xm_Dj", SqlDbType.Decimal,9),
					new SqlParameter("@Xm_Memo", SqlDbType.VarChar,500),
					new SqlParameter("@Xm_Code", SqlDbType.Char,12)};
			parameters[0].Value = model.Xmlb_Code;
			parameters[1].Value = model.Xm_Name;
			parameters[2].Value = model.Xm_Jc;
			parameters[3].Value = model.Xm_Dw;
			parameters[4].Value = model.Xm_Dj;
			parameters[5].Value = model.Xm_Memo;
			parameters[6].Value = model.Xm_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string Xm_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Zd_Ml_Xm3 ");
			strSql.Append(" where Xm_Code=@Xm_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Xm_Code", SqlDbType.Char,12)			};
			parameters[0].Value = Xm_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string Xm_Codelist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Zd_Ml_Xm3 ");
			strSql.Append(" where Xm_Code in ("+Xm_Codelist + ")  ");
			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Zd_Ml_Xm3 GetModel(string Xm_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 Xmlb_Code,Xm_Code,Xm_Name,Xm_Jc,Xm_Dw,Xm_Dj,Xm_Memo from Zd_Ml_Xm3 ");
			strSql.Append(" where Xm_Code=@Xm_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Xm_Code", SqlDbType.Char,12)			};
			parameters[0].Value = Xm_Code;

			ModelOld.M_Zd_Ml_Xm3 model=new ModelOld.M_Zd_Ml_Xm3();
			DataSet ds=HisVar.HisVar.Sqldal.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Zd_Ml_Xm3 DataRowToModel(DataRow row)
		{
			ModelOld.M_Zd_Ml_Xm3 model=new ModelOld.M_Zd_Ml_Xm3();
			if (row != null)
			{
				if(row["Xmlb_Code"]!=null)
				{
					model.Xmlb_Code=row["Xmlb_Code"].ToString();
				}
				if(row["Xm_Code"]!=null)
				{
					model.Xm_Code=row["Xm_Code"].ToString();
				}
				if(row["Xm_Name"]!=null)
				{
					model.Xm_Name=row["Xm_Name"].ToString();
				}
				if(row["Xm_Jc"]!=null)
				{
					model.Xm_Jc=row["Xm_Jc"].ToString();
				}
				if(row["Xm_Dw"]!=null)
				{
					model.Xm_Dw=row["Xm_Dw"].ToString();
				}
				if(row["Xm_Dj"]!=null && row["Xm_Dj"].ToString()!="")
				{
					model.Xm_Dj=decimal.Parse(row["Xm_Dj"].ToString());
				}
				if(row["Xm_Memo"]!=null)
				{
					model.Xm_Memo=row["Xm_Memo"].ToString();
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select Xmlb_Code,Xm_Code,Xm_Name,Xm_Jc,Xm_Dw,Xm_Dj,Xm_Memo ");
			strSql.Append(" FROM Zd_Ml_Xm3 ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" Xmlb_Code,Xm_Code,Xm_Name,Xm_Jc,Xm_Dw,Xm_Dj,Xm_Memo ");
			strSql.Append(" FROM Zd_Ml_Xm3 ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.Xm_Code desc");
			}
			strSql.Append(")AS Row, T.*  from Zd_Ml_Xm3 T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Zd_Ml_Xm3";
			parameters[1].Value = "Xm_Code";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return HisVar.HisVar.Sqldal.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

