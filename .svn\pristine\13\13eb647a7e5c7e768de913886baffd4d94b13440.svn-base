﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="0" />
    <DataSources isList="true" count="1">
      <view主表 Ref="2" type="Stimulsoft.Report.Dictionary.StiDataViewSource" isKey="true">
        <Alias>view主表</Alias>
        <Columns isList="true" count="6">
          <value>Bq_Code,System.String</value>
          <value>Bq_Name,System.String</value>
          <value>Bc_Zs,System.Decimal</value>
          <value>Zy_Rs,System.Decimal</value>
          <value>Bc_Syl,System.Decimal</value>
          <value>Bc_Wyl,System.Decimal</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>view主表</Name>
        <NameInSource>view主表</NameInSource>
      </view主表>
    </DataSources>
    <Relations isList="true" count="0" />
    <Report isRef="0" />
    <Variables isList="true" count="2">
      <value>,打印时间,打印时间,System.DateTime,,False,False</value>
      <value>,操作员,操作员,System.String,,False,False</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="3" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="4">
        <PageHeaderBand1 Ref="4" type="PageHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,19,1.4</ClientRectangle>
          <Components isList="true" count="3">
            <Text1 Ref="5" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>5.8,0,6.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>黑体,18,Regular,Point,False,134</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text1</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>医院床位使用单</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text1>
            <Text2 Ref="6" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.8,6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9.75,Regular,Point,False,0</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text2</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>打印时间：{打印时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text2>
            <Text3 Ref="7" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>14.6,0.8,4.4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9.75,Regular,Point,False,0</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text3</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>{操作员}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text3>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>PageHeaderBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </PageHeaderBand1>
        <HeaderBand1 Ref="8" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,2.6,19,0.6</ClientRectangle>
          <Components isList="true" count="5">
            <Text4 Ref="9" type="Text" isKey="true">
              <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,10.5,Bold,Point,False,0</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text4</Name>
              <Page isRef="3" />
              <Parent isRef="8" />
              <Text>医院病区</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text4>
            <Text5 Ref="10" type="Text" isKey="true">
              <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4,0,3.5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,10.5,Bold,Point,False,0</Font>
              <Guid>6dbcfac073fb42ce85c7f81e5747b2fd</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text5</Name>
              <Page isRef="3" />
              <Parent isRef="8" />
              <Text>病区总床位数</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text5>
            <Text6 Ref="11" type="Text" isKey="true">
              <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.5,0,3.5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,10.5,Bold,Point,False,0</Font>
              <Guid>e75326bdae3d41e1a58c054bd6870214</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text6</Name>
              <Page isRef="3" />
              <Parent isRef="8" />
              <Text>住院患者占用数</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text6>
            <Text7 Ref="12" type="Text" isKey="true">
              <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>11,0,4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,10.5,Bold,Point,False,0</Font>
              <Guid>c471cd3ed9df4d6bba3803d006df4bc9</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text7</Name>
              <Page isRef="3" />
              <Parent isRef="8" />
              <Text>住院患者占用率(%)</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text7>
            <Text8 Ref="13" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15,0,4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,10.5,Bold,Point,False,0</Font>
              <Guid>f1ba04b8116e495d9a1836fc58b75fbc</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text8</Name>
              <Page isRef="3" />
              <Parent isRef="8" />
              <Text>医院床位未使用率(%)</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text8>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>HeaderBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </HeaderBand1>
        <DataBand1 Ref="14" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,4,19,0.4</ClientRectangle>
          <Components isList="true" count="5">
            <Text9 Ref="15" type="Text" isKey="true">
              <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>16c31d2da21b49909b8e2373accc4106</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text9</Name>
              <Page isRef="3" />
              <Parent isRef="14" />
              <Text>{view主表.Bq_Name}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text9>
            <Text11 Ref="16" type="Text" isKey="true">
              <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.5,0,3.5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>baae9ef3701b4463a7cb7d028199babb</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text11</Name>
              <Page isRef="3" />
              <Parent isRef="14" />
              <Text>{view主表.Zy_Rs}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text11>
            <Text12 Ref="17" type="Text" isKey="true">
              <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>11,0,4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>baa724169ec8414e9d45a800d4510bc8</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text12</Name>
              <Page isRef="3" />
              <Parent isRef="14" />
              <Text>{view主表.Bc_Syl}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text12>
            <Text13 Ref="18" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15,0,4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>1ff5ec7472e3490ca0a98b3eddc02718</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text13</Name>
              <Page isRef="3" />
              <Parent isRef="14" />
              <Text>{view主表.Bc_Wyl}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text13>
            <Text10 Ref="19" type="Text" isKey="true">
              <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4,0,3.5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>85b2b200c9744c3b817f1b49c94a7dda</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text10</Name>
              <Page isRef="3" />
              <Parent isRef="14" />
              <Text>{view主表.Bc_Zs}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text10>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>view主表</DataSourceName>
          <Filters isList="true" count="0" />
          <Name>DataBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Sort isList="true" count="0" />
        </DataBand1>
        <ReportSummaryBand1 Ref="20" type="ReportSummaryBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,5.2,19,0.6</ClientRectangle>
          <Components isList="true" count="5">
            <Text14 Ref="21" type="Text" isKey="true">
              <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,0</Font>
              <Guid>7973bf10bd6b4fd3ab343f887df90e37</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text14</Name>
              <Page isRef="3" />
              <Parent isRef="20" />
              <Text>医院总合计</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text14>
            <Text15 Ref="22" type="Text" isKey="true">
              <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4,0,3.5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,0</Font>
              <Guid>b6c282ec232e41b2adb9d5940de9fdb9</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text15</Name>
              <Page isRef="3" />
              <Parent isRef="20" />
              <Text>{Sum(view主表.Bc_Zs)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text15>
            <Text16 Ref="23" type="Text" isKey="true">
              <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.5,0,3.5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,0</Font>
              <Guid>0784e628dd3a44c897fc8f4494e0591f</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text16</Name>
              <Page isRef="3" />
              <Parent isRef="20" />
              <Text>{Sum(view主表.Zy_Rs)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text16>
            <Text17 Ref="24" type="Text" isKey="true">
              <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>11,0,4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,0</Font>
              <Guid>de6c362897ba440fb9b7638843fd5bc6</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text17</Name>
              <Page isRef="3" />
              <Parent isRef="20" />
              <Text>{(Sum(view主表.Zy_Rs)/Sum(view主表.Bc_Zs))*100}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="25" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text17>
            <Text18 Ref="26" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15,0,4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,0</Font>
              <Guid>9a7ae8569b3d45aabe8e54d912f22db2</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text18</Name>
              <Page isRef="3" />
              <Parent isRef="20" />
              <Text>{((Sum(view主表.Bc_Zs)-Sum(view主表.Zy_Rs))/Sum(view主表.Bc_Zs))*100}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="27" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text18>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>ReportSummaryBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </ReportSummaryBand1>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>e4f89850f9c340e9b8e1fa0533361c1f</Guid>
      <Margins>1,1,1,1</Margins>
      <Name>Page1</Name>
      <PageHeight>9.9</PageHeight>
      <PageWidth>21</PageWidth>
      <Report isRef="0" />
      <TitleBeforeHeader>True</TitleBeforeHeader>
      <Watermark Ref="28" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page1>
  </Pages>
  <PrinterSettings Ref="29" type="Stimulsoft.Report.Print.StiPrinterSettings" isKey="true" />
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>医院床位使用单</ReportAlias>
  <ReportChanged>12/12/2011 4:19:05 PM</ReportChanged>
  <ReportCreated>12/10/2011 1:10:45 PM</ReportCreated>
  <ReportFile>.\Rpt\住院床位统计.mrt</ReportFile>
  <ReportGuid>17dbb1a5f8e84b6087fcd06231bc1cbb</ReportGuid>
  <ReportName>医院床位使用单</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2011.2.1026</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class Report : Stimulsoft.Report.StiReport
    {
        public Report()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
        #endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>