﻿Imports Stimulsoft.Report
Imports System.Drawing
Imports System.Windows.Forms

Public Class Marterials_Check

#Region "定义__变量"
    Dim My_Table As New DataTable                       '从表
    Dim Dt_Excel As New DataTable
    Dim V_TotalMoney As Double
    Dim Cb_Cm As CurrencyManager                     '同步指针
    Dim flag_Error As Boolean
    Dim Rinsert As Boolean = True
    Dim Materials_Check1Bll As New BLLOld.B_Materials_Check1
    Dim Materials_Check2Bll As New BLLOld.B_Materials_Check2
    Dim Materials_Check1Model As New ModelOld.M_Materials_Check1
    Dim Materials_Check2Model As New ModelOld.M_Materials_Check2
  Dim MaterialsStockBll As New BLLOld.B_Materials_Stock
#End Region

    Public Sub New(ByVal tModel As ModelOld.M_Materials_Check1)
        ' 此调用是设计器所必需的。
        InitializeComponent()
        Materials_Check1Model = tModel
        ' 在 InitializeComponent() 调用之后添加任何初始化。
    End Sub

    Private Sub Yk_Materials_Check2_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Me.Load
        Call Form_Init()                '窗体初始化
        If Materials_Check1Model Is Nothing Then           '主表
            Call Zb_Clear()                 '清空数据
        Else
            Call Zb_Show()
        End If
        Call statisticsDataShow()
    End Sub

#Region "窗体__事件"

    Private Sub Form_Init()
        Panel2.Height = 35
        Comm_Del.Location = New Point(100, 1)
        Comm_DelAll.Location = New Point(Comm_Del.Left + Comm_Del.Width + 2, 1)
        Comm_New.Location = New Point(Comm_DelAll.Left + Comm_DelAll.Width + 2, 1)
        Comm_Save.Location = New Point(Comm_New.Left + Comm_New.Width + 2, 1)
        Comm_Complete.Location = New Point(Comm_Save.Left + Comm_Save.Width + 2, 1)
        Comm_Print.Location = New Point(Comm_Complete.Left + Comm_Complete.Width + 2, 1)
        Comm_Search.Location = New Point(Comm_Print.Left + Comm_Print.Width + 2, 1)
        Comm_Dr.Location = New Point(Comm_Search.Left + Comm_Search.Width + 2, 1)
        Comm_Dc.Location = New Point(Comm_Dr.Left + Comm_Dr.Width + 2, 1)
        Comm_Close.Location = New Point(Comm_Dc.Left + Comm_Dc.Width + 2, 1)

        '当前日期
        With Form_Date
            .CustomFormat = "yyyy-MM-dd HH:mm"
            .EditFormat = "yyyy-MM-dd"
            .DisplayFormat = "yyyy-MM-dd"
            .Value = Format(Now, "yyyy-MM-dd") & " " & Format(Now, "HH:mm:ss")
        End With

        '物资仓库字典
        Dim MaterialsWhBll As New BLLOld.B_Materials_Warehouse_Dict
        With WareHouse_DtCom
            .DataView = MaterialsWhBll.GetList("isuse=1").Tables(0).DefaultView
            .Init_Colum("MaterialsWh_Py", "物资仓库简称", 0, "左")
            .Init_Colum("MaterialsWh_Name", "物资仓库名称", 210, "左")
            .Init_Colum("MaterialsWh_Code", "编码", 0, "左")
            .DisplayMember = "MaterialsWh_Name"
            .ValueMember = "MaterialsWh_Code"
            .DroupDownWidth = .Width - .CaptainWidth
            .MaxDropDownItems = 15
            .SelectedIndex = -1
            .RowFilterTextNull = ""
            .RowFilterNotTextNull = "MaterialsWh_Py"
        End With

        Jsr_Text.Text = HisVar.HisVar.JsrName
        Code_Text.Enabled = False
        Jsr_Text.Enabled = False

        Dt_Excel.Columns.Add("Materials_Name")
        Dt_Excel.Columns.Add("Materials_Code")
        Dt_Excel.Columns.Add("MaterialsStock_Code")
        Dt_Excel.Columns.Add("MaterialsWh_Code")
        Dt_Excel.Columns.Add("MaterialsWh_Name")
        Dt_Excel.Columns.Add("MaterialsLot")
        Dt_Excel.Columns.Add("MaterialsExpiryDate")
        Dt_Excel.Columns.Add("Materials_Spec")
        Dt_Excel.Columns.Add("M_Paper_Num")
        Dt_Excel.Columns.Add("M_Real_Num")
        Dt_Excel.Columns.Add("M_Check_Price")
        Dt_Excel.Columns.Add("M_CheckDetail_Memo")
    End Sub

    Private Sub BtnState()
        MyGrid_Init()
        If Materials_Check1Model.OrdersStatus Is Nothing OrElse Materials_Check1Model.OrdersStatus = "录入" Then
            Comm_Del.Enabled = True
            Comm_DelAll.Enabled = True
            Comm_New.Enabled = True
            Comm_Save.Enabled = True
            Comm_Complete.Enabled = True
            Comm_Print.Enabled = False
            Comm_Search.Enabled = True
            Comm_Close.Enabled = True
            Comm_Dr.Enabled = True
            WareHouse_DtCom.Enabled = True
            Form_Date.Enabled = True
            Memo_Text.Enabled = True
            MyGrid1.Tag = True
            Comm_DelAll.Enabled = True
            Exit Sub
        End If

        '非本人的单据不可以修改
        If Materials_Check1Model.Jsr_Code <> HisVar.HisVar.JsrCode Then
            Comm_Del.Enabled = False
            Comm_DelAll.Enabled = False
            Comm_New.Enabled = True
            Comm_Save.Enabled = False
            Comm_Complete.Enabled = False
            Comm_Print.Enabled = False
            Comm_Search.Enabled = True
            Comm_Close.Enabled = True
            Comm_Dr.Enabled = False
            WareHouse_DtCom.Enabled = False
            Form_Date.Enabled = False
            Memo_Text.Enabled = False
            MyGrid1.Tag = False
            Exit Sub
        End If

        If Materials_Check1Model.OrdersStatus = "完成" Then
            Comm_Del.Enabled = False
            Comm_DelAll.Enabled = False
            Comm_New.Enabled = True
            Comm_Save.Enabled = False
            Comm_Complete.Enabled = False
            Comm_Print.Enabled = True
            Comm_Search.Enabled = True
            Comm_Close.Enabled = True
            Comm_Dr.Enabled = False
            WareHouse_DtCom.Enabled = False
            Form_Date.Enabled = False
            Memo_Text.Enabled = False
            MyGrid1.Tag = False
            Comm_DelAll.Enabled = False
            Exit Sub
        End If
    End Sub
    Private Sub MyGrid_Init()
        Dim ColEdit As Boolean
        If Materials_Check1Model.OrdersStatus = "完成" Then
            ColEdit = False
        Else
            ColEdit = True
        End If
        With MyGrid1
            .Init_Grid()
            .Init_Column("盘点编码", "M_Check_Code", "0", "左", "", False) '0-
            .Init_Column("明细编码", "M_Check_Detail_Code", "0", "左", "", False) '0
            .Init_Column("物资名称", "Materials_Name", "200", "左", "", ColEdit) '4-
            .Init_Column("物资编码", "Materials_Code", "0", "左", "", False) '0
            .Init_Column("库存编码", "MaterialsStock_Code", "0", "左", "", False) '2-
            .Init_Column("物资批号", "MaterialsLot", "150", "左", "", False) '3-
            .Init_Column("物资有效期", "MaterialsExpiryDate", "110", "中", "yyyy-MM-dd", False) '1-
            .Init_Column("物资规格", "Materials_Spec", "150", "左", "", False) '1-
            .Init_Column("账面数量", "M_Paper_Num", "80", "右", "####0.####", False) '0
            .Init_Column("盘点数量", "M_Real_Num", "80", "右", "####0.####", ColEdit) '0
            .Init_Column("盈亏数量", "M_Check_Num", "80", "右", "####0.####", ColEdit) '0
            .Init_Column("单价", "M_Check_Price", "80", "右", "####0.00##", False) '0
            .Init_Column("盈亏金额", "M_Check_Money", "80", "右", "####0.00##", False) '0-
            .Init_Column("备注", "M_CheckDetail_Memo", "100", "左", "", ColEdit) '0
            .MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightCell
            If Materials_Check1Model.OrdersStatus = "完成" Then
                .AllowAddNew = False '完成单据不允许增加新行
            Else
                .AllowAddNew = ColEdit
            End If
            .ColumnFooters = True
            MyGrid1.Splits(0).DisplayColumns("Materials_Name").Locked = True
        End With
    End Sub
    Private Sub Yk_Materials_Check2_FormClosing(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosingEventArgs) Handles Me.FormClosing
        If e.CloseReason = CloseReason.UserClosing Then
            Try
                MyGrid1.UpdateData()
                If My_Table.DataSet.HasChanges = True Then
                    If Materials_Check1Bll.GetRecordCount("  M_Check_Code='" & Code_Text.Text.Trim & "' and OrdersStatus ='完成'") = 0 Then
                        If MessageBox.Show("数据尚未保存,是否保存数据?", "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1) = Windows.Forms.DialogResult.OK Then
                            Call Data_Save("保存")
                        End If
                    End If
                End If
            Catch ex As Exception
                MsgBox("当前编辑行数据因未填写完整，将不能保存！", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示：")
                MyGrid1.Delete()
            End Try
        End If
    End Sub

#End Region

#Region "清空__显示"

    Private Sub Zb_Clear()
        Rinsert = True
        Materials_Check1Model = New ModelOld.M_Materials_Check1
        Materials_Check1Model.M_Check_Code = Materials_Check1Bll.MaxCode(Format(Now, "yyMMdd"))

        Code_Text.Text = Materials_Check1Model.M_Check_Code
        Jsr_Text.Text = HisVar.HisVar.JsrName

        V_TotalMoney = 0
        WareHouse_DtCom.SelectedIndex = -1
        Form_Date.Value = Now


        Call BtnState()
        Call P_Data_Show()
        WareHouse_DtCom.Select()
    End Sub

    Private Sub Zb_Show()   '显示记录
        Rinsert = False
        With Materials_Check1Model
            Code_Text.Text = .M_Check_Code                                 '入库编码
            Jsr_Text.Text = .Jsr_Name
            WareHouse_DtCom.DataSource.RowFilter = ""
            WareHouse_DtCom.SelectedValue = .MaterialsWh_Code
            Form_Date.Value = .Check_Date
            V_TotalMoney = .TotalMoney
            Memo_Text.Text = .M_Check_Memo
        End With
        BtnState()
        Call P_Data_Show()
    End Sub

    Private Sub P_Data_Show()   '从表数据
        My_Table = Materials_Check2Bll.GetList("Materials_Check2.M_Check_Code='" & Materials_Check1Model.M_Check_Code & "'").Tables(0)
        My_Table.Columns("M_Check_Code").AllowDBNull = True
        My_Table.Columns("M_Check_Detail_Code").AllowDBNull = True
        My_Table.Columns("MaterialsStock_Code").AllowDBNull = False
        My_Table.Columns("Materials_Code").AllowDBNull = False
        My_Table.Columns("M_Check_Num").AllowDBNull = True
        My_Table.Columns("M_Check_Price").AllowDBNull = True
        My_Table.Columns("M_Real_Num").AllowDBNull = True
        My_Table.Columns("M_Paper_Num").AllowDBNull = True
        My_Table.Constraints.Clear()
        '列的唯一性
        '主表记录
        MyGrid1.DataTable = My_Table
        Cb_Cm = CType(BindingContext(MyGrid1.DataSource, MyGrid1.DataMember), CurrencyManager)
        Call F_Sum()
        MyGrid1.Focus()
    End Sub

    Private Sub statisticsDataShow()
        Dim str As String = "Finish_Date BETWEEN '" & Format(Now, "yyyy-MM-dd 00:00:00") & "' AND '" & Format(Now, "yyyy-MM-dd 23:59:59") & "'"
        TodayTimesLabel1.Text = "今日盘点单数：" & Materials_Check1Bll.GetRecordCount(str)
        TodayTotalMoneyLabel1.Text = "今日盘点总金额：" & Materials_Check1Bll.GetSumMoeny(str)
        str += "AND Jsr_Code='" & HisVar.HisVar.JsrCode & "'"
        JsrTimesLabel2.Text = "本人盘点单数：" & Materials_Check1Bll.GetRecordCount(str)
        JsrTotalMoneyLabel3.Text = "本人盘点总金额：" & Materials_Check1Bll.GetSumMoeny(str)
    End Sub

#End Region

#Region "控件__动作"

#Region "按钮"

    Private Sub Comm_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm_Del.Click, Comm_DelAll.Click, Comm_Save.Click,
        Comm_Complete.Click, Comm_New.Click, Comm_Print.Click, Comm_Close.Click, Comm_Dc.Click, Comm_Dr.Click, Comm_Search.Click
        Select Case sender.tag
            Case "删除行"
                Data_Delete()
            Case "删除单"
                If Materials_Check1Model Is Nothing Then
                    MsgBox("数据尚未保存,无法删除!", MsgBoxStyle.Exclamation, "提示")
                    Exit Sub
                End If
                If Materials_Check1Bll.GetRecordCount("  M_Check_Code='" & Code_Text.Text.Trim & "' and OrdersStatus ='录入'") = 0 Then
                    MsgBox("未保存单据不能删除！", MsgBoxStyle.Exclamation, "提示")
                    Exit Sub
                End If
                Call Data_DeleteAll(Materials_Check1Model)
            Case "新单"
                Call Data_New()
            Case "保存", "完成"
                Call Data_Save(sender.tag)
            Case "打印"
                If Materials_Check1Model.Check_Date Is Nothing Then
                    MsgBox("数据尚未保存,无法打印!", MsgBoxStyle.Exclamation, "提示")
                    Exit Sub
                End If
                If Materials_Check1Model.OrdersStatus = "录入" Then
                    MsgBox("未完成单据不能打印", MsgBoxStyle.Exclamation, "提示")
                    Exit Sub
                End If
                Call Data_Print()
            Case "速查"
                Dim t_Rc As New BaseClass.C_RowChange
                AddHandler t_Rc.ModelChangeEvent, AddressOf ChangeModel
                Dim f As New Check_Search("盘点速查", t_Rc)
                f.Owner = Me
                f.ShowDialog()
            Case "导入"
                If Materials_Check1Bll.GetRecordCount("  M_Check_Code='" & Code_Text.Text.Trim & "' and OrdersStatus ='完成'") = 1 Then
                    MsgBox("已完成单据不允许导入！", MsgBoxStyle.Exclamation, "提示")
                    Exit Sub
                End If
                Dr_Excel()
            Case "导出"
                Dim frm As New DcCondition
                frm.ShowDialog()
            Case "退出"
                Me.Close()
        End Select
    End Sub

    Private Sub Memo_Text_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles Memo_Text.Validated
        MyGrid1.Focus()
    End Sub

#End Region

#Region "DBGrid动作"

    Private Sub MyGrid1_AfterColUpdate(ByVal sender As System.Object, ByVal e As C1.Win.C1TrueDBGrid.ColEventArgs) Handles MyGrid1.AfterColUpdate
        Dim up_Row As DataRow                    '当 前 行
        up_Row = Cb_Cm.List(MyGrid1.Row).Row
        Select Case MyGrid1.Splits(0).DisplayColumns(e.ColIndex).Name
            Case "盈亏数量"
                If up_Row("M_Check_Num") IsNot DBNull.Value And up_Row("M_Check_Price") IsNot DBNull.Value Then
                    up_Row("M_Check_Money") = up_Row("M_Check_Num") * up_Row("M_Check_Price")
                End If
                up_Row("M_Real_Num") = up_Row("M_Paper_Num") + up_Row("M_Check_Num")
            Case "盘点数量"
                up_Row("M_Check_Num") = up_Row("M_Real_Num") - up_Row("M_Paper_Num")
                If up_Row("M_Check_Num") IsNot DBNull.Value And up_Row("M_Check_Price") IsNot DBNull.Value Then
                    up_Row("M_Check_Money") = up_Row("M_Check_Num") * up_Row("M_Check_Price")
                End If

        End Select
    End Sub

    Private Sub MyGrid1_AfterUpdate(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyGrid1.AfterUpdate
        Call F_Sum()
    End Sub

    Private Sub MyGrid1_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles MyGrid1.KeyDown
        Select Case e.KeyCode
            Case Keys.Escape, Keys.F2, Keys.F3, Keys.F4, Keys.F5, Keys.F6, Keys.Up, Keys.Down, Keys.Left, Keys.Right

            Case Keys.Delete
                Data_Delete()
            Case Else
                If Zb_Check() = False Then Exit Sub
                If MyGrid1.Columns(MyGrid1.Col).DataField = "Materials_Name" Then '物资输入
                    Call Input("物资")
                    Exit Sub
                End If
                If e.KeyCode = Keys.Enter Then
                    If MyGrid1.Columns(MyGrid1.Col).DataField = "M_Real_Num" Then
                        Me.MyGrid1.DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveNone
                        Me.MyGrid1.Col = MyGrid1.Splits(0).DisplayColumns.IndexOf(MyGrid1.Columns("M_Check_Num"))
                        Exit Sub
                    End If
                    If MyGrid1.Columns(MyGrid1.Col).DataField = "M_Check_Num" Then
                        Me.MyGrid1.DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveNone
                        Me.MyGrid1.Col = MyGrid1.Splits(0).DisplayColumns.IndexOf(MyGrid1.Columns("M_CheckDetail_Memo"))
                        Exit Sub
                    End If
                    If MyGrid1.Columns(MyGrid1.Col).DataField = "M_CheckDetail_Memo" Then
                        Me.MyGrid1.DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveNone
                        Me.MyGrid1.Row = Me.MyGrid1.Row + 1
                        If flag_Error = True Then
                            flag_Error = False
                            Exit Sub
                        End If
                        Me.MyGrid1.Col = MyGrid1.Splits(0).DisplayColumns.IndexOf(MyGrid1.Columns("Materials_Name"))
                    Else
                        Me.MyGrid1.DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveRight
                    End If
                End If
        End Select
    End Sub


    Private Sub MyGrid1_MouseDoubleClick(ByVal sender As System.Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles MyGrid1.MouseDoubleClick
        If Materials_Check1Bll.GetRecordCount("M_Check_Code='" & Code_Text.Text & "' and OrdersStatus='完成'") = 0 Then
            If Zb_Check() = False Then Exit Sub
            If MyGrid1.Columns(MyGrid1.Col).DataField = "Materials_Name" Then '物资输入
                Call Input("物资")
                Exit Sub
            End If
        End If
    End Sub



    Private Sub MyGrid1_GotFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles MyGrid1.GotFocus
        Me.CancelButton = Nothing
    End Sub

    Private Sub MyGrid1_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles MyGrid1.Validated
        Me.CancelButton = Comm_Close
    End Sub

    Private Sub MyGrid1_BeforeColUpdate(ByVal sender As System.Object, ByVal e As C1.Win.C1TrueDBGrid.BeforeColUpdateEventArgs) Handles MyGrid1.BeforeColUpdate
        If Zb_Check() = False Then Exit Sub
        If e.Column.Name = "物资名称" Then
            If VerifyMaterialsName(MyGrid1.Columns("Materials_Name").Text) = False Then
                e.Cancel = True
            End If
        End If

        If e.Column.Name = "盘点数量" Then
            If verifyRealNum(MyGrid1.Columns("M_Real_Num").Value) = False Then
                e.Cancel = True
            End If
        End If
        If e.Column.Name = "盈亏数量" Then
            If verifyCheckNum(MyGrid1.Columns("MaterialsStock_Code").Value, MyGrid1.Columns("M_Check_Num").Value) = False Then
                e.Cancel = True
            End If
        End If
    End Sub

    Private Sub MyGrid1_Error(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.ErrorEventArgs) Handles MyGrid1.Error
        e.Handled = True
        flag_Error = True

        If StrConv(e.Exception.Message, VbStrConv.Narrow) = "列""Materials_Name""不允许 nulls?" Then
            MessageBox.Show("物资不能为空!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
            MyGrid1.SetActiveCell(MyGrid1.Row, MyGrid1.Splits(0).DisplayColumns.IndexOf(MyGrid1.Columns("Materials_Name")))
        End If

        If StrConv(e.Exception.Message, VbStrConv.Narrow) = "列""M_Check_Num""不允许 nulls?" Then
            MessageBox.Show("数量不能为空!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
            MyGrid1.SetActiveCell(MyGrid1.Row, MyGrid1.Splits(0).DisplayColumns.IndexOf(MyGrid1.Columns("M_Check_Num")))
        End If

        Me.MyGrid1.Row = Me.MyGrid1.Row - 1
    End Sub

#End Region

#Region "快捷键"
    Private Sub PjCl_Materials_Check2_KeyDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles MyBase.KeyDown
        If e.Control = True And e.KeyCode = Keys.S And Comm_Save.Enabled = True Then
            Call Data_Save("保存")
        End If

        If e.KeyData = Keys.F2 And Comm_New.Enabled = True Then
            MyGrid1.Focus()
            sender.tag = "新单"
            Call Comm_Click(sender, Nothing)
        End If

        If e.KeyData = Keys.F3 And Comm_Save.Enabled = True Then
            MyGrid1.Focus()
            sender.tag = "保存"
            Call Comm_Click(sender, Nothing)
        End If

        If e.KeyData = Keys.F4 And Comm_Complete.Enabled = True Then
            MyGrid1.Focus()
            sender.tag = "完成"
            Call Comm_Click(sender, Nothing)
        End If

        If e.KeyData = Keys.F5 And Comm_Print.Enabled = True Then
            MyGrid1.Focus()
            sender.tag = "打印"
            Call Comm_Click(sender, Nothing)
        End If
        If e.KeyData = Keys.F6 And Comm_Search.Enabled = True Then
            MyGrid1.Focus()
            sender.tag = "速查"
            Call Comm_Click(sender, Nothing)
        End If

        If e.KeyData = Keys.F7 And Comm_Dr.Enabled = True Then
            MyGrid1.Focus()
            sender.tag = "导入"
            Call Comm_Click(sender, Nothing)
        End If
        If e.KeyData = Keys.F8 And Comm_Dc.Enabled = True Then
            MyGrid1.Focus()
            sender.tag = "导出"
            Call Comm_Click(sender, Nothing)
        End If
    End Sub
#End Region

#End Region

#Region "自定义函数"

#Region "验证函数"

    Private Function Zb_Check() As Boolean
        If WareHouse_DtCom.SelectedIndex = -1 Then
            WareHouse_DtCom.Select()
            MessageBox.Show("请选择仓库!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
            Return False
        Else
            Return True
        End If
    End Function

    Private Function Zb_CheckWc() As Boolean
        If Materials_Check1Bll.GetRecordCount("M_Check_Code='" & Code_Text.Text & "' And OrdersStatus='完成'") > 0 Then
            MsgBox("此次盘点已经完成!", MsgBoxStyle.Exclamation, "提示")
            Return False
        End If
        Return True
    End Function

    Private Function Cb_CheckRowCounts() As Boolean
        If MyGrid1.RowCount = 0 Then
            MsgBox("尚未录入数据", MsgBoxStyle.Exclamation, "提示")
            Return False
        End If
        Return True
    End Function

    Private Function Cb_Check() As Boolean
        MyGrid1.UpdateData()
        Dim _Row As DataRow
        Dim i As Integer = 0

        For Each _Row In My_Table.Rows
            If _Row.RowState = DataRowState.Deleted Then
                Continue For
            End If

            If verifyRealNum(_Row("M_Real_Num")) = False Then
                MyGrid1.Focus()
                MyGrid1.SetActiveCell(MyGrid1.Row, MyGrid1.Splits(0).DisplayColumns.IndexOf(MyGrid1.Columns("M_Check_Num")))
                Return False
            End If

            If verifyCheckNum(_Row("MaterialsStock_Code"), _Row("M_Check_Num")) & "" = "" Then
                MyGrid1.Focus()
                MyGrid1.SetActiveCell(MyGrid1.Row, MyGrid1.Splits(0).DisplayColumns.IndexOf(MyGrid1.Columns("M_Check_Num")))
                Return False
            End If
            _Row("M_Check_Money") = _Row("M_Check_Num") * _Row("M_Check_Price")
            i = i + 1
        Next
        Return True
    End Function

    Private Function verifyRealNum(ByVal CheckNum As String) As Boolean
        If  Common.Tools.IsNumber(CheckNum) = False Then
            HisControl.msg.Show("请输入正确数字!", "提示")
            Return False
        End If
        If CDbl(CheckNum) < 0 Then
            HisControl.msg.Show("数量必须不能小于0!", "提示")
            Return False
        End If
        Return True
    End Function

    Private Function verifyCheckNum(ByVal MaterialsStock_Code As String, ByVal CheckNum As String) As Boolean
        If  Common.Tools.IsNumber(CheckNum) = False Then
            HisControl.msg.Show("请输入正确数字!", "提示")
            Return False
        End If
        If -CDbl(CheckNum) > MaterialsStockBll.GetModel(MaterialsStock_Code).MaterialsStore_Num Then
            HisControl.msg.Show("盘亏数量不能大于库存数量!", "提示")
            Return False
        End If
        Return True
    End Function

    '验证物资
    Private Function VerifyMaterialsName(ByVal Materials_Name As String) As Boolean
        If Materials_Name = "" Then
            HisControl.msg.Show("物资不能为空!", "提示")
            Return False
        End If
        Return True
    End Function
#End Region

#Region "按钮函数"

    Private Sub Data_Delete()
        If MessageBox.Show("确认是否删除当前记录,如果删除该记录将不可恢复!", "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Error, MessageBoxDefaultButton.Button2) = Windows.Forms.DialogResult.OK Then
            Try
                MyGrid1.Delete()
                Call F_Sum()
            Catch ex As Exception
                If ex.Message.ToString = "索引 -1 不是为负数，就是大于行数。" Then
                    MsgBox("未选中任何行！", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
                End If
            End Try

        End If
    End Sub

    Private Sub Data_DeleteAll(ByVal t_Row As ModelOld.M_Materials_Check1)
        If MessageBox.Show("确认是否删除当前单据,如果删除该单据将不可恢复!", "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Error, MessageBoxDefaultButton.Button2) = Windows.Forms.DialogResult.OK Then
            Materials_Check1Bll.Delete(t_Row.M_Check_Code)
            Zb_Clear()
        End If
    End Sub

    Private Sub Data_New()
        Try
            MyGrid1.UpdateData()
            If My_Table.DataSet.HasChanges = True Then
                If Materials_Check1Bll.GetRecordCount("  M_Check_Code='" & Code_Text.Text.Trim & "' and OrdersStatus in ('录入','完成')") = 0 Then
                    If MessageBox.Show("数据尚未保存,是否保存数据?", "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1) = Windows.Forms.DialogResult.OK Then
                        Call Data_Save("保存")
                    End If
                End If
            End If
            Call Zb_Clear()
        Catch ex As Exception
            Call Zb_Clear()
        End Try
    End Sub

    Private Sub Data_Save(ByVal arg As String)
        If Not Zb_CheckWc() Then Exit Sub
        If Not Zb_Check() Then Exit Sub
        If Not Cb_CheckRowCounts() Then Exit Sub
        If Not Cb_Check() Then Exit Sub

        If arg = "保存" Then
            Call Zb_Save()
        ElseIf arg = "完成" Then
            Call Zb_Save()
            Call Data_Complete()
        End If
    End Sub

    Private Sub Data_Complete()
        If MsgBox("是否完成此次盘点？", MsgBoxStyle.Information + MsgBoxStyle.OkCancel + MsgBoxStyle.DefaultButton1, "提示:") = MsgBoxResult.Cancel Then Exit Sub
        Try
            With Materials_Check1Model
                .OrdersStatus = "完成"
                .Finish_Date = Now
                .TotalMoney = V_TotalMoney
            End With
            Materials_Check1Bll.Complete(Materials_Check1Model)
            statisticsDataShow()
            HisControl.msg.Show("盘点完成!", "提示")
            Call BtnState()
            Call P_Data_Show()
            MyGrid1.Focus()
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            WareHouse_DtCom.Select()
            Exit Sub
        End Try

    End Sub

    Private Sub Data_Print()
        Dim StiRpt As New StiReport
        StiRpt.Load(".\Rpt\物资盘点表.mrt")

        StiRpt.ReportName = "物资盘点单"

        StiRpt.RegData(My_Table)

        StiRpt.Pages(0).PaperSize = Printing.PaperKind.A4
        StiRpt.Pages(0).Margins.Top = 1
        StiRpt.Pages(0).Margins.Bottom = 1
        StiRpt.Pages(0).Margins.Left = 1
        StiRpt.Pages(0).Margins.Right = 1

        StiRpt.Compile()
        StiRpt("盘点编码") = Code_Text.Text
        StiRpt("盘点时间") = CDate(Form_Date.Value.ToString)
        StiRpt("打印时间") = Now
        StiRpt("操作人") = Jsr_Text.Text
        StiRpt("物资仓库") = WareHouse_DtCom.Text
        'StiRpt.Render()
        'StiRpt.Design()
        StiRpt.Show()
    End Sub

    Private Sub Dr_Excel()
        Dt_Excel.Clear()
        Dim V_FileName As String
        With Me.OpenFileDialog1
            .Filter = "电子表格(Excel)|*.Xls"
            .FileName = ""
            If .ShowDialog = Windows.Forms.DialogResult.Cancel Then
                Exit Sub
            End If
            V_FileName = .FileName
        End With
        Dim tmpDt As DataTable = Dt_Excel.Copy
        Common.ExcelHelper.RenderDataTableFromExcel(V_FileName, 0, 1, tmpDt, 12, 1)
        If tmpDt.Rows.Count > 0 Then Call Save_AddExcel(tmpDt)
    End Sub

    Private Sub Save_AddExcel(ByVal ExcelDTb As DataTable)
        My_Table.Clear()
        For Each ExcelRow In ExcelDTb.Rows
            If ExcelRow.Item("Materials_Code").ToString.Contains("编码") = False Then
                Dim My_NewRow As DataRow = My_Table.NewRow
                With My_NewRow
                    .BeginEdit()
                    .Item("Materials_Code") = ExcelRow.Item("Materials_Code") & ""
                    .Item("Materials_Name") = ExcelRow.Item("Materials_Name") & ""
                    .Item("MaterialsStock_Code") = ExcelRow.Item("MaterialsStock_Code") & ""
                    .Item("MaterialsLot") = ExcelRow.Item("MaterialsLot") & ""
                    .Item("MaterialsExpiryDate") = ExcelRow.Item("MaterialsExpiryDate")
                    .Item("Materials_Spec") = ExcelRow.Item("Materials_Spec") & ""

                    If IsNumeric(ExcelRow.Item("M_Paper_Num")) = True Then
                        .Item("M_Paper_Num") = ExcelRow.Item("M_Paper_Num")
                    Else
                        .Item("M_Paper_Num") = 0
                    End If

                    If IsNumeric(ExcelRow.Item("M_Real_Num")) = True Then
                        .Item("M_Real_Num") = ExcelRow.Item("M_Real_Num")
                    Else
                        .Item("M_Real_Num") = 0
                    End If
                    .Item("M_Check_Num") = .Item("M_Real_Num") - .Item("M_Paper_Num")
                    .Item("M_Check_Price") = ExcelRow.Item("M_Check_Price")
                    .Item("M_Check_Money") = .Item("M_Check_Num") * .Item("M_Check_Price")
                    .Item("M_CheckDetail_Memo") = ExcelRow.Item("M_CheckDetail_Memo") & ""
                End With

                '数据保存
                Try
                    My_Table.Rows.Add(My_NewRow)
                Catch ex As Exception
                    Beep()
                    MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
                End Try
            End If
        Next
        WareHouse_DtCom.SelectedValue = ExcelDTb.Rows(0).Item("MaterialsWh_Code")
        With Me.MyGrid1
            Cb_Cm = CType(BindingContext(My_Table, ""), CurrencyManager)
            MyGrid1.DataTable = My_Table
        End With
    End Sub

#End Region

#Region "数据库更改"

#Region "主表__编辑"

    Private Sub Zb_Save()                       '主表保存
        If Rinsert = True Then     '增加记录
            Call Zb_Add()
        Else                                '编辑记录
            Call Zb_Edit()
        End If
        Call Cb_Update()
        HisControl.msg.Show("数据保存成功!", "提示")
        MyGrid1.Focus()
    End Sub

    Private Sub Zb_Add()    '增加记录
        Try
            With Materials_Check1Model
                .M_Check_Code = Materials_Check1Bll.MaxCode(Format(Now, "yyMMdd"))
                Code_Text.Text = .M_Check_Code
                .Input_Date = Now
                .Check_Date = Format(Form_Date.Value, "yyyy-MM-dd HH:mm:ss")
                .MaterialsWh_Code = WareHouse_DtCom.SelectedValue
                .Jsr_Code = HisVar.HisVar.JsrCode
                .TotalMoney = V_TotalMoney
                .M_Check_Memo = Memo_Text.Text
                .OrdersStatus = "录入"
            End With
            Materials_Check1Bll.Add(Materials_Check1Model)
            Rinsert = False
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            WareHouse_DtCom.Select()
            Exit Sub
        End Try
    End Sub

    Private Sub Zb_Edit()   '编辑记录
        Try
            With Materials_Check1Model
                .M_Check_Code = Code_Text.Text
                .Input_Date = Now
                .Check_Date = Format(Form_Date.Value, "yyyy-MM-dd HH:mm:ss")
                .MaterialsWh_Code = WareHouse_DtCom.SelectedValue
                .Jsr_Code = HisVar.HisVar.JsrCode
                .M_Check_Memo = Memo_Text.Text
                .OrdersStatus = .OrdersStatus
                .TotalMoney = V_TotalMoney
            End With
            Materials_Check1Bll.Update(Materials_Check1Model)
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
            Exit Sub
        End Try
    End Sub
#End Region

#Region "从表__编辑"

    Private Sub Cb_Update()   '从表更新
        MyGrid1.UpdateData()
        For Each _row As DataRow In My_Table.Rows
            If _row.RowState = DataRowState.Added Then
                CbData_Insert(_row)
            End If
            If _row.RowState = DataRowState.Modified Then
                CbData_Update(_row)
            End If
            If _row.RowState = DataRowState.Deleted Then
                CbData_Delete(_row)
            End If
        Next
        My_Table.AcceptChanges()
    End Sub

    Private Sub CbData_Update(ByVal Cb_Row As DataRow)   '从表更新
        Materials_Check2Model = Materials_Check2Bll.GetModel(Cb_Row("M_Check_Detail_Code"))
        With Materials_Check2Model
            .Materials_Code = Cb_Row("Materials_Code") & ""
            .MaterialsStock_Code = Cb_Row("MaterialsStock_Code") & ""
            .MaterialsLot = Cb_Row("MaterialsLot") & ""
            .MaterialsExpiryDate = Cb_Row("MaterialsExpiryDate")
            .Materials_Spec = Cb_Row("Materials_Spec") & ""
            .M_Paper_Num = Cb_Row("M_Paper_Num")
            .M_Real_Num = Cb_Row("M_Real_Num")
            .M_Check_Num = Cb_Row("M_Check_Num")
            .M_Check_Price = Cb_Row("M_Check_Price")
            .M_Check_Money = Cb_Row("M_Check_Money")
            .M_CheckDetail_Memo = Cb_Row("M_CheckDetail_Memo") & ""
        End With
        Materials_Check2Bll.Update(Materials_Check2Model)
    End Sub

    Private Sub CbData_Insert(ByVal Cb_Row As DataRow)   '从表增加
        Cb_Row("M_Check_Code") = Materials_Check1Model.M_Check_Code
        Cb_Row("M_Check_Detail_Code") = Materials_Check2Bll.MaxCode(Materials_Check1Model.M_Check_Code)
        With Materials_Check2Model
            .M_Check_Code = Cb_Row("M_Check_Code")
            .M_Check_Detail_Code = Cb_Row("M_Check_Detail_Code")
            .Materials_Code = Cb_Row("Materials_Code") & ""
            .MaterialsStock_Code = Cb_Row("MaterialsStock_Code") & ""
            .MaterialsLot = Cb_Row("MaterialsLot") & ""
            .MaterialsExpiryDate = Cb_Row("MaterialsExpiryDate")
            .Materials_Spec = Cb_Row("Materials_Spec") & ""
            .M_Paper_Num = Cb_Row("M_Paper_Num")
            .M_Real_Num = Cb_Row("M_Real_Num")
            .M_Check_Num = Cb_Row("M_Check_Num")
            .M_Check_Price = Cb_Row("M_Check_Price")
            .M_Check_Money = Cb_Row("M_Check_Money")
            .M_CheckDetail_Memo = Cb_Row("M_CheckDetail_Memo") & ""
        End With
        Materials_Check2Bll.Add(Materials_Check2Model)
    End Sub

    Private Sub CbData_Delete(ByVal Cb_Row As DataRow)
        Materials_Check2Bll.Delete(Cb_Row.Item("M_Check_Detail_Code", DataRowVersion.Original))
    End Sub

#End Region

#End Region

    Private Sub ChangeModel(ByVal model As Object)
        Materials_Check1Model = TryCast(model, ModelOld.M_Materials_Check1)
        If Materials_Check1Model Is Nothing Then Exit Sub
        Call Zb_Show()
    End Sub

    Public Overrides Sub F_Sum()
        If MyGrid1.RowCount = 0 Then
            WareHouse_DtCom.Enabled = True
        Else
            WareHouse_DtCom.Enabled = False
        End If
        V_TotalMoney = IIf(My_Table.Compute("Sum(M_Check_Money)", "") Is DBNull.Value, 0, My_Table.Compute("Sum(M_Check_Money)", ""))
        MyGrid1.Columns("M_Check_Money").FooterText = V_TotalMoney.ToString("0.00##")
    End Sub

    Private Sub Input(ByVal V_Lb As String)
        Dim strwhere As String = ""
        For Each _row In My_Table.Rows
            If _row.RowState = DataRowState.Deleted Then
                Continue For
            End If
            strwhere = strwhere & "'" & _row("MaterialsStock_Code") & "',"
        Next

        Dim f As New MaterialsStockSelect(WareHouse_DtCom.SelectedValue, strwhere)
        f.Owner = Me
        If f.ShowDialog = Windows.Forms.DialogResult.OK Then
            If My_Table.Select("MaterialsStock_Code='" & f.ModelMaterials_Stock.MaterialsStock_Code & "'").Length > 0 Then
                MsgBox("已经存在此条记录!", MsgBoxStyle.Exclamation, "提示")
                Exit Sub
            End If
            MyGrid1.Columns("M_Check_Code").Value = Materials_Check1Model.M_Check_Code
            Dim up_Row As DataRow
            up_Row = Cb_Cm.List(MyGrid1.Row).Row
            Select Case V_Lb
                Case "物资"
                    With f.ModelMaterials_Stock
                        up_Row("Materials_Code") = .Materials_Code
                        up_Row("Materials_Name") = .Materials_Name
                        up_Row("MaterialsStock_Code") = .MaterialsStock_Code
                        up_Row("MaterialsLot") = .MaterialsLot
                        up_Row("MaterialsExpiryDate") = .MaterialsExpiryDate
                        up_Row("Materials_Spec") = .Materials_Spec
                        up_Row("M_Paper_Num") = .MaterialsStore_Num
                        up_Row("M_Check_Price") = .MaterialsStore_Price
                    End With
                    MyGrid1.SetActiveCell(MyGrid1.Row, MyGrid1.Splits(0).DisplayColumns.IndexOf(MyGrid1.Columns("M_Real_Num")))
            End Select
        End If
    End Sub

#End Region

#Region "输入法设置"
    '中文
    Private Sub CodeTextBox1_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Memo_Text.GotFocus
        InputLanguage.CurrentInputLanguage = HisVar.HisVar.InputCode
    End Sub
    '英文
    Private Sub yw_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles WareHouse_DtCom.GotFocus
        InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages(0)
    End Sub

#End Region
  
End Class