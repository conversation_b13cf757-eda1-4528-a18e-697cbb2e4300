﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class MaterialsOtherInMx
    Inherits HisControl.BaseChild

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel()
        Me.T_Line1 = New System.Windows.Forms.Label()
        Me.PutInNo_Num = New CustomControl.MyNumericEdit()
        Me.Materials_DtCom = New CustomControl.MyDtComobo()
        Me.Lot_DtCom = New CustomControl.MyDtComobo()
        Me.Expiry_Date = New CustomControl.MyDateEdit()
        Me.Tips_TextBox = New System.Windows.Forms.Label()
        Me.PriceMoney_Num = New CustomControl.MyNumericEdit()
        Me.PutInMoney_Num = New CustomControl.MyNumericEdit()
        Me.Memo_Text = New CustomControl.MyTextBox()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.GG_MyTextBox1 = New CustomControl.MyTextBox()
        Me.SCCJ_MyTextBox2 = New CustomControl.MyTextBox()
        Me.Save_Btn = New CustomControl.MyButton()
        Me.Cancel_Btn = New CustomControl.MyButton()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.TableLayoutPanel1.SuspendLayout()
        Me.Panel1.SuspendLayout()
        Me.SuspendLayout()
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.ColumnCount = 7
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 63.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 123.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 63.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 121.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 65.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 123.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 20.0!))
        Me.TableLayoutPanel1.Controls.Add(Me.SCCJ_MyTextBox2, 2, 3)
        Me.TableLayoutPanel1.Controls.Add(Me.GG_MyTextBox1, 0, 3)
        Me.TableLayoutPanel1.Controls.Add(Me.T_Line1, 0, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.PutInNo_Num, 0, 6)
        Me.TableLayoutPanel1.Controls.Add(Me.Materials_DtCom, 0, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.Lot_DtCom, 0, 5)
        Me.TableLayoutPanel1.Controls.Add(Me.Expiry_Date, 2, 5)
        Me.TableLayoutPanel1.Controls.Add(Me.Tips_TextBox, 2, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.PriceMoney_Num, 2, 6)
        Me.TableLayoutPanel1.Controls.Add(Me.PutInMoney_Num, 4, 6)
        Me.TableLayoutPanel1.Controls.Add(Me.Memo_Text, 0, 7)
        Me.TableLayoutPanel1.Controls.Add(Me.Label1, 0, 4)
        Me.TableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(0, 0)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.RowCount = 9
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 4.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 27.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 4.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 27.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 4.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 27.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 27.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 180.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 8.0!))
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(561, 318)
        Me.TableLayoutPanel1.TabIndex = 2
        '
        'T_Line1
        '
        Me.T_Line1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.T_Line1.BackColor = System.Drawing.SystemColors.Control
        Me.T_Line1.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.TableLayoutPanel1.SetColumnSpan(Me.T_Line1, 6)
        Me.T_Line1.Location = New System.Drawing.Point(3, 32)
        Me.T_Line1.Name = "T_Line1"
        Me.T_Line1.Size = New System.Drawing.Size(552, 2)
        Me.T_Line1.TabIndex = 120
        Me.T_Line1.Text = "Label1"
        '
        'PutInNo_Num
        '
        Me.PutInNo_Num.Captain = "入库数量"
        Me.PutInNo_Num.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.PutInNo_Num.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.PutInNo_Num, 2)
        Me.PutInNo_Num.Location = New System.Drawing.Point(3, 96)
        Me.PutInNo_Num.MaximumSize = New System.Drawing.Size(100000, 80)
        Me.PutInNo_Num.MinimumSize = New System.Drawing.Size(0, 20)
        Me.PutInNo_Num.Name = "PutInNo_Num"
        Me.PutInNo_Num.NumFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.PutInNo_Num.ReadOnly = False
        Me.PutInNo_Num.Size = New System.Drawing.Size(180, 20)
        Me.PutInNo_Num.TabIndex = 3
        Me.PutInNo_Num.ValueIsDbNull = False
        '
        'Materials_DtCom
        '
        Me.Materials_DtCom.Captain = "物    资"
        Me.Materials_DtCom.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Materials_DtCom.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.Materials_DtCom.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.Materials_DtCom, 2)
        Me.Materials_DtCom.DataSource = Nothing
        Me.Materials_DtCom.ItemHeight = 18
        Me.Materials_DtCom.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Materials_DtCom.Location = New System.Drawing.Point(3, 7)
        Me.Materials_DtCom.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.Materials_DtCom.MinimumSize = New System.Drawing.Size(0, 20)
        Me.Materials_DtCom.Name = "Materials_DtCom"
        Me.Materials_DtCom.ReadOnly = False
        Me.Materials_DtCom.Size = New System.Drawing.Size(180, 20)
        Me.Materials_DtCom.TabIndex = 0
        Me.Materials_DtCom.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'Lot_DtCom
        '
        Me.Lot_DtCom.Captain = "批    号"
        Me.Lot_DtCom.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Lot_DtCom.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.Lot_DtCom.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.Lot_DtCom, 2)
        Me.Lot_DtCom.DataSource = Nothing
        Me.Lot_DtCom.ItemHeight = 18
        Me.Lot_DtCom.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Lot_DtCom.Location = New System.Drawing.Point(3, 69)
        Me.Lot_DtCom.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.Lot_DtCom.MinimumSize = New System.Drawing.Size(0, 20)
        Me.Lot_DtCom.Name = "Lot_DtCom"
        Me.Lot_DtCom.ReadOnly = False
        Me.Lot_DtCom.Size = New System.Drawing.Size(180, 20)
        Me.Lot_DtCom.TabIndex = 1
        Me.Lot_DtCom.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'Expiry_Date
        '
        Me.Expiry_Date.Captain = "有 效 期"
        Me.Expiry_Date.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Expiry_Date.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.Expiry_Date, 2)
        Me.Expiry_Date.InitialSelection = C1.Win.C1Input.InitialSelectionEnum.CaretAtEnd
        Me.Expiry_Date.Location = New System.Drawing.Point(189, 69)
        Me.Expiry_Date.MaximumSize = New System.Drawing.Size(100000000, 20)
        Me.Expiry_Date.MinimumSize = New System.Drawing.Size(0, 20)
        Me.Expiry_Date.Name = "Expiry_Date"
        Me.Expiry_Date.Size = New System.Drawing.Size(178, 20)
        Me.Expiry_Date.TabIndex = 2
        Me.Expiry_Date.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Expiry_Date.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.Expiry_Date.VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.DropDown
        '
        'Tips_TextBox
        '
        Me.Tips_TextBox.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Tips_TextBox.AutoSize = True
        Me.TableLayoutPanel1.SetColumnSpan(Me.Tips_TextBox, 4)
        Me.Tips_TextBox.Font = New System.Drawing.Font("宋体", 10.5!)
        Me.Tips_TextBox.Location = New System.Drawing.Point(189, 10)
        Me.Tips_TextBox.Name = "Tips_TextBox"
        Me.Tips_TextBox.Size = New System.Drawing.Size(366, 14)
        Me.Tips_TextBox.TabIndex = 132
        Me.Tips_TextBox.Text = "提示：相同物资，批号相同，价格与有效期也相同"
        Me.Tips_TextBox.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'PriceMoney_Num
        '
        Me.PriceMoney_Num.Captain = "单    价"
        Me.PriceMoney_Num.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.PriceMoney_Num.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.PriceMoney_Num, 2)
        Me.PriceMoney_Num.Location = New System.Drawing.Point(189, 96)
        Me.PriceMoney_Num.MaximumSize = New System.Drawing.Size(100000, 80)
        Me.PriceMoney_Num.MinimumSize = New System.Drawing.Size(0, 20)
        Me.PriceMoney_Num.Name = "PriceMoney_Num"
        Me.PriceMoney_Num.NumFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.PriceMoney_Num.ReadOnly = False
        Me.PriceMoney_Num.Size = New System.Drawing.Size(178, 20)
        Me.PriceMoney_Num.TabIndex = 4
        Me.PriceMoney_Num.ValueIsDbNull = False
        '
        'PutInMoney_Num
        '
        Me.PutInMoney_Num.Captain = "入库金额"
        Me.PutInMoney_Num.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.PutInMoney_Num.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.PutInMoney_Num, 2)
        Me.PutInMoney_Num.Location = New System.Drawing.Point(373, 96)
        Me.PutInMoney_Num.MaximumSize = New System.Drawing.Size(100000, 80)
        Me.PutInMoney_Num.MinimumSize = New System.Drawing.Size(0, 20)
        Me.PutInMoney_Num.Name = "PutInMoney_Num"
        Me.PutInMoney_Num.NumFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.PutInMoney_Num.ReadOnly = False
        Me.PutInMoney_Num.Size = New System.Drawing.Size(180, 20)
        Me.PutInMoney_Num.TabIndex = 128
        Me.PutInMoney_Num.ValueIsDbNull = False
        '
        'Memo_Text
        '
        Me.Memo_Text.Captain = "备    注"
        Me.Memo_Text.CaptainBackColor = System.Drawing.Color.Transparent
        Me.Memo_Text.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Memo_Text.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.Memo_Text.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.Memo_Text, 6)
        Me.Memo_Text.ContentForeColor = System.Drawing.Color.Black
        Me.Memo_Text.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.Memo_Text.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Memo_Text.EditMask = Nothing
        Me.Memo_Text.Location = New System.Drawing.Point(3, 123)
        Me.Memo_Text.Multiline = True
        Me.Memo_Text.Name = "Memo_Text"
        Me.Memo_Text.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.Memo_Text.ReadOnly = False
        Me.Memo_Text.ScrollBars = System.Windows.Forms.ScrollBars.Vertical
        Me.Memo_Text.SelectionStart = 0
        Me.Memo_Text.SelectStart = 0
        Me.Memo_Text.Size = New System.Drawing.Size(552, 174)
        Me.Memo_Text.TabIndex = 131
        Me.Memo_Text.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Memo_Text.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Top
        Me.Memo_Text.Watermark = Nothing
        '
        'Label1
        '
        Me.Label1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label1.BackColor = System.Drawing.SystemColors.Control
        Me.Label1.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.TableLayoutPanel1.SetColumnSpan(Me.Label1, 6)
        Me.Label1.Location = New System.Drawing.Point(3, 63)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(552, 2)
        Me.Label1.TabIndex = 121
        Me.Label1.Text = "Label1"
        '
        'GG_MyTextBox1
        '
        Me.GG_MyTextBox1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GG_MyTextBox1.Captain = "规    格"
        Me.GG_MyTextBox1.CaptainBackColor = System.Drawing.Color.Transparent
        Me.GG_MyTextBox1.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.GG_MyTextBox1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.GG_MyTextBox1.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.GG_MyTextBox1, 2)
        Me.GG_MyTextBox1.ContentForeColor = System.Drawing.Color.Black
        Me.GG_MyTextBox1.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.GG_MyTextBox1.EditMask = Nothing
        Me.GG_MyTextBox1.Location = New System.Drawing.Point(3, 38)
        Me.GG_MyTextBox1.Multiline = False
        Me.GG_MyTextBox1.Name = "GG_MyTextBox1"
        Me.GG_MyTextBox1.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.GG_MyTextBox1.ReadOnly = False
        Me.GG_MyTextBox1.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.GG_MyTextBox1.SelectionStart = 0
        Me.GG_MyTextBox1.SelectStart = 0
        Me.GG_MyTextBox1.Size = New System.Drawing.Size(180, 21)
        Me.GG_MyTextBox1.TabIndex = 4
        Me.GG_MyTextBox1.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.GG_MyTextBox1.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.GG_MyTextBox1.Watermark = Nothing
        '
        'SCCJ_MyTextBox2
        '
        Me.SCCJ_MyTextBox2.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SCCJ_MyTextBox2.Captain = "生产厂家"
        Me.SCCJ_MyTextBox2.CaptainBackColor = System.Drawing.Color.Transparent
        Me.SCCJ_MyTextBox2.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.SCCJ_MyTextBox2.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.SCCJ_MyTextBox2.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.SCCJ_MyTextBox2, 4)
        Me.SCCJ_MyTextBox2.ContentForeColor = System.Drawing.Color.Black
        Me.SCCJ_MyTextBox2.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.SCCJ_MyTextBox2.EditMask = Nothing
        Me.SCCJ_MyTextBox2.Location = New System.Drawing.Point(189, 38)
        Me.SCCJ_MyTextBox2.Multiline = False
        Me.SCCJ_MyTextBox2.Name = "SCCJ_MyTextBox2"
        Me.SCCJ_MyTextBox2.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.SCCJ_MyTextBox2.ReadOnly = False
        Me.SCCJ_MyTextBox2.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.SCCJ_MyTextBox2.SelectionStart = 0
        Me.SCCJ_MyTextBox2.SelectStart = 0
        Me.SCCJ_MyTextBox2.Size = New System.Drawing.Size(366, 21)
        Me.SCCJ_MyTextBox2.TabIndex = 4
        Me.SCCJ_MyTextBox2.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.SCCJ_MyTextBox2.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.SCCJ_MyTextBox2.Watermark = Nothing
        '
        'Save_Btn
        '
        Me.Save_Btn.ButtonImageSize = CustomControl.MyButton.imageSize.large
        Me.Save_Btn.DialogResult = System.Windows.Forms.DialogResult.None
        Me.Save_Btn.Location = New System.Drawing.Point(319, 5)
        Me.Save_Btn.Name = "Save_Btn"
        Me.Save_Btn.Size = New System.Drawing.Size(75, 30)
        Me.Save_Btn.TabIndex = 0
        Me.Save_Btn.Tag = "保存"
        Me.Save_Btn.Text = "保存"
        '
        'Cancel_Btn
        '
        Me.Cancel_Btn.ButtonImageSize = CustomControl.MyButton.imageSize.large
        Me.Cancel_Btn.DialogResult = System.Windows.Forms.DialogResult.None
        Me.Cancel_Btn.Location = New System.Drawing.Point(417, 5)
        Me.Cancel_Btn.Name = "Cancel_Btn"
        Me.Cancel_Btn.Size = New System.Drawing.Size(75, 30)
        Me.Cancel_Btn.TabIndex = 7
        Me.Cancel_Btn.Tag = "取消"
        Me.Cancel_Btn.Text = "取消"
        '
        'Panel1
        '
        Me.Panel1.BackColor = System.Drawing.Color.FromArgb(CType(CType(239, Byte), Integer), CType(CType(239, Byte), Integer), CType(CType(239, Byte), Integer))
        Me.Panel1.Controls.Add(Me.Cancel_Btn)
        Me.Panel1.Controls.Add(Me.Save_Btn)
        Me.Panel1.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.Panel1.Location = New System.Drawing.Point(0, 278)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(561, 40)
        Me.Panel1.TabIndex = 3
        '
        'MaterialsOtherInMx
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.ClientSize = New System.Drawing.Size(561, 318)
        Me.Controls.Add(Me.Panel1)
        Me.Controls.Add(Me.TableLayoutPanel1)
        Me.Name = "MaterialsOtherInMx"
        Me.Text = "其他入库明细"
        Me.TableLayoutPanel1.ResumeLayout(False)
        Me.TableLayoutPanel1.PerformLayout()
        Me.Panel1.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents TableLayoutPanel1 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents Memo_Text As CustomControl.MyTextBox
    Friend WithEvents T_Line1 As System.Windows.Forms.Label
    Friend WithEvents PutInNo_Num As CustomControl.MyNumericEdit
    Friend WithEvents PriceMoney_Num As CustomControl.MyNumericEdit
    Friend WithEvents PutInMoney_Num As CustomControl.MyNumericEdit
    Friend WithEvents Materials_DtCom As CustomControl.MyDtComobo
    Friend WithEvents Lot_DtCom As CustomControl.MyDtComobo
    Friend WithEvents Expiry_Date As CustomControl.MyDateEdit
    Friend WithEvents Tips_TextBox As System.Windows.Forms.Label
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents GG_MyTextBox1 As CustomControl.MyTextBox
    Friend WithEvents SCCJ_MyTextBox2 As CustomControl.MyTextBox
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents Cancel_Btn As CustomControl.MyButton
    Friend WithEvents Save_Btn As CustomControl.MyButton

End Class
