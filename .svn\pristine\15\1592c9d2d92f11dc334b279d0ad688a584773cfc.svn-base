﻿Imports System.Data.SqlClient
Imports System.Windows.Forms

Public Class Zd_Cssz1

    Dim V_HKey As String = "HKEY_CURRENT_USER\中软智通"             '添加注册表用户
    Dim My_Dataset As New DataSet

    Private Sub Zd_Cssz1_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load

        HisVar.HisVar.YyOledb.QueryDt(My_Dataset, "Select Sj_Name,Sj_Code From Zd_Sj order by Sj_Code", "市级字典", True)
        Dim My_Combo As New BaseClass.C_Combo2(SjCombo, My_Dataset.Tables("市级字典").DefaultView, "Sj_Name", "Sj_Code", 250)
        With My_Combo
            .Init_TDBCombo()
            .Init_Colum("Sj_Name", "市级名称", 230, "左")
            .Init_Colum("Sj_Code", "市级编码", 0, "左")
            .MaxDropDownItems(12)
            .SelectedIndex(-1)
        End With
        SjCombo.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList

        HisVar.HisVar.YyOledb.QueryDt(My_Dataset, "Select Xq_Name,Xq_Code,Sj_Code From Zd_Xq order by Xq_Code", "县区字典", True)
        My_Dataset.Tables("县区字典").DefaultView.RowFilter = ""
        Dim My_Combo1 As New BaseClass.C_Combo2(XqCombo, My_Dataset.Tables("县区字典").DefaultView, "Xq_Name", "Xq_Code", 250)
        With My_Combo1
            .Init_TDBCombo()
            .Init_Colum("Xq_Name", "县区名称", 230, "左")
            .Init_Colum("Xq_Code", "县区编码", 0, "左")
            .Init_Colum("Sj_Code", "市级编码", 0, "左")
            .MaxDropDownItems(12)
            .SelectedIndex(-1)
        End With
        XqCombo.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList

        With My.Computer.Registry
            If .GetValue(V_HKey, "", Nothing) Is Nothing Then
                .CurrentUser.CreateSubKey("中软智通")
                .SetValue(V_HKey, "", "")
                .SetValue(V_HKey, "医院编码", "")
                .SetValue(V_HKey, "县区编码", "")
                .SetValue(V_HKey, "医院名称", "")

            Else
                XqCombo.SelectedValue = .GetValue(V_HKey, "县区编码", Nothing) & ""
                C1TextBox1.Text = .GetValue(V_HKey, "医院编码", Nothing) & ""
                C1TextBox3.Text = .GetValue(V_HKey, "医院名称", Nothing) & ""
                SjCombo.SelectedValue = XqCombo.Columns("Sj_Code").Value & ""
                '   My_Dataset.Tables("县区字典").DefaultView.RowFilter = "Sj_Code='" & SjCombo.SelectedValue & "'"
            End If
        End With


    End Sub

#Region "控件动作"


    Private Sub save_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles save.Click
        If SjCombo.SelectedValue = Nothing Then
            MsgBox("请选择医院所在城市!", MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            SjCombo.Select()
            Exit Sub
        End If

        If XqCombo.SelectedValue = Nothing Then
            MsgBox("请选择医院所在县区!", MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            XqCombo.Select()
            Exit Sub
        End If
        If C1TextBox1.Text = "" Then
            MsgBox("医院编码不能为空,请重新输入!", MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            C1TextBox1.Select()
            Exit Sub
        End If

        If HisVar.HisVar.YyOledb.GetSingle("Select Count(Yy_Name) From Zd_Yy Where Yy_Code='" & C1TextBox1.Text.Trim & "' And Xq_Code='" & XqCombo.SelectedValue & "'") = 0 Then
            MsgBox("医院编码不正确或与所选县区不匹配!", MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            C1TextBox1.Select()
            Exit Sub
        End If

        C1TextBox3.Text = Trim(HisVar.HisVar.YyOledb.GetSingle("Select Yy_Name From Zd_Yy Where Yy_Code='" & C1TextBox1.Text.Trim & "' And Xq_Code='" & XqCombo.SelectedValue & "'") & "")

        If MessageBox.Show("请确认您的医院名称是【" & Trim(C1TextBox3.Text & "") & "】", "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Question) = DialogResult.OK Then
            With My.Computer.Registry
                .SetValue(V_HKey, "医院编码", Trim(C1TextBox1.Text & ""))
                .SetValue(V_HKey, "医院名称", Trim(C1TextBox3.Text & ""))
                .SetValue(V_HKey, "县区编码", Trim(XqCombo.SelectedValue & ""))
            End With
            If My.Settings.DebugLb = "正式" Then
                Dim _ds As New DataSet
                _ds = HisVar.HisVar.YyOledb.Query("Select * From Zd_Yy  Where Yy_Code='" & My.Computer.Registry.GetValue(V_HKey, "医院编码", Nothing) & "" & "' And Xq_Code='" & My.Computer.Registry.GetValue(V_HKey, "县区编码", Nothing) & "'")
                My.Settings.DB_Ip = _ds.Tables(0).Rows(0).Item("Yy_Ip")
                My.Settings.DB_Name = _ds.Tables(0).Rows(0).Item("Yy_Db")
                My.Settings.DB_Pwd = _ds.Tables(0).Rows(0).Item("Yy_Pass")
                My.Settings.DB_Id = _ds.Tables(0).Rows(0).Item("DB_Id")
                My.Settings.Is_Ybzl = _ds.Tables(0).Rows(0).Item("Is_Ybzl").ToString & ""

                Dim Initial_Dal As New SqlDal.dzhSqlDal
                Initial_Dal.ConnectionString = "Data Source=" & My.Settings.DB_Ip & ";Initial Catalog=" & My.Settings.DB_Name & ";Persist Security Info=True;User Id=" & My.Settings.DB_Id & ";Pwd=" & My.Settings.DB_Pwd & ""
                Try
                    Me.Cursor = Cursors.WaitCursor
                    If Initial_Dal.GetSingle("Select Count(*) from Zd_Yy ") = 0 Then
                        Dim arr As New ArrayList
                        arr.Add("  Insert Into Zd_Yy(Yy_Code,Yy_Name,Yy_Use)Values('" & Trim(C1TextBox1.Text & "") & "','" & Trim(C1TextBox3.Text & "") & "','1')")
                        arr.Add("Insert Into Zd_Qx1 (Glz_Code,Glz_Name,Glz_Jc,Yy_Code) values ('" & Trim(C1TextBox1.Text & "") & "001','管理员','GLY','" & Trim(C1TextBox1.Text & "") & "')")
                        '赋予全部权限
                        arr.Add("Insert Into Zd_Qx2 (Glz_Code,Module_Code) Select Glz_Code,Module_Code From Zd_Qx1,Zd_QxModule Where Yy_Code='" & Trim(C1TextBox1.Text & "") & "'")
                        '增加药房
                        arr.Add("Insert Into Zd_YyYf(Yy_Code,Yf_Code,Yf_Name,Yf_Jc,Yf_Use) values ('" & Trim(C1TextBox1.Text & "") & "','" & Trim(C1TextBox1.Text & "") & "01','药房','YF',1)")
                        '增加经手人
                        arr.Add("Insert Into Zd_YyJsr(Yy_Code,Jsr_Code,Login_Code,Jsr_Name,Jsr_Jc,Jsr_Password,Glz_Code,Yf_Code) values ('" & Trim(C1TextBox1.Text & "") & "','" & Trim(C1TextBox1.Text & "") & "001','" & Trim(C1TextBox1.Text & "") & "','管理员','GLY','0DAFC0E0B','" & Trim(C1TextBox1.Text & "") & "001','" & Trim(C1TextBox1.Text & "") & "01')")
                        '报销类别
                        arr.Add("Insert Into Zd_Bxlb(Yy_Code,Bxlb_Code,Bxlb_Jc,Bxlb_Name) values ('" & Trim(C1TextBox1.Text & "") & "','" & Trim(C1TextBox1.Text & "") & "01','PTHZ','普通患者')")
                        arr.Add("Insert Into Zd_Bxlb(Yy_Code,Bxlb_Code,Bxlb_Jc,Bxlb_Name) values ('" & Trim(C1TextBox1.Text & "") & "','" & Trim(C1TextBox1.Text & "") & "02','HZYL','合作医疗')")
                        arr.Add("Insert Into Zd_Bxlb(Yy_Code,Bxlb_Code,Bxlb_Jc,Bxlb_Name) values ('" & Trim(C1TextBox1.Text & "") & "','" & Trim(C1TextBox1.Text & "") & "03','CZZG','城镇职工')")
                        arr.Add("Insert Into Zd_Bxlb(Yy_Code,Bxlb_Code,Bxlb_Jc,Bxlb_Name) values ('" & Trim(C1TextBox1.Text & "") & "','" & Trim(C1TextBox1.Text & "") & "04','CZJM','城镇居民')")
                        arr.Add("Insert Into Zd_Bxlb(Yy_Code,Bxlb_Code,Bxlb_Jc,Bxlb_Name) values ('" & Trim(C1TextBox1.Text & "") & "','" & Trim(C1TextBox1.Text & "") & "05','CXJM','城乡居民')")
                        '农合编码及县区
                        arr.Add("update Zd_YyPara set Para_Value='" & Trim(C1TextBox1.Text & "") & "' where Para_Name='农合编码'")
                        arr.Add("update Zd_YyPara set Para_Value='" & Trim(SjCombo.Text & "") & Trim(XqCombo.Text & "") & "' where Para_Name='所属县区'")
                        Initial_Dal.ExecuteSqlTran(arr)
                    End If

                Catch ex As Exception
                    Me.Cursor = Cursors.Default
                    MsgBox(ex.Message.ToString)
                    'MsgBox("未能正常连接数据库,  请检查医院参数设置是否正确")
                    Me.DialogResult = DialogResult.Cancel
                    Exit Sub
                End Try
            End If
            Me.Cursor = Cursors.Default
            Me.DialogResult = DialogResult.OK
        End If
    End Sub


    Private Sub TextBox_KeyPress(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles C1TextBox1.KeyPress, C1TextBox3.KeyPress
        If e.KeyChar <> Chr(Keys.Return) Then Exit Sub
        e.Handled = True
        System.Windows.Forms.SendKeys.Send("{Tab}")
    End Sub
#End Region


    Private Sub C1TextBox1_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1TextBox1.Validated

        If HisVar.HisVar.YyOledb.GetSingle("Select Count(Yy_Name) From Zd_Yy Where Yy_Code='" & C1TextBox1.Text.Trim & "' And Xq_Code='" & XqCombo.SelectedValue & "'") = 0 And C1TextBox1.Text.Trim <> "" Then
            MsgBox("医院编码不正确或与所选县区不匹配!", MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            C1TextBox1.Text = ""
            C1TextBox3.Text = ""
            Exit Sub
        End If

        C1TextBox3.Text = Trim(HisVar.HisVar.YyOledb.GetSingle("Select Yy_Name From Zd_Yy Where Yy_Code='" & C1TextBox1.Text.Trim & "' And Xq_Code='" & XqCombo.SelectedValue & "'") & "")
    End Sub


    Private Sub SjCombo_SelectedValueChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles SjCombo.SelectedValueChanged
        If SjCombo.SelectedValue <> "" Then

            If XqCombo.Text <> "" And XqCombo.Columns("Sj_Code").Value = SjCombo.SelectedValue Then
            Else
                My_Dataset.Tables("县区字典").DefaultView.RowFilter = "Sj_Code='" & SjCombo.SelectedValue & "'"
                XqCombo.SelectedIndex = -1
                C1TextBox1.Text = ""
                C1TextBox3.Text = ""
            End If
        End If
    End Sub
End Class