﻿/**  版本信息模板在安装目录下，可自行修改。
* M_Yb_InfoDy.cs
*
* 功 能： N/A
* 类 名： M_Yb_InfoDy
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/9/23 09:50:31   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace ModelOld
{
	/// <summary>
	/// M_Yb_InfoDy:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class M_Yb_InfoDy
	{
		public M_Yb_InfoDy()
		{}
		#region Model
		private string _his_code;
		private string _lb;
		private string _his_name;
		private string _his_gg;
		private string _his_cd;
		private string _his_jx;
		private string _zg_fydj;
		private string _zg_mzbl;
		private string _zg_zybl;
		private string _zg_dj;
		private string _jm_fydj;
		private string _jm_mzbl;
		private string _jm_zybl;
		private string _jm_dj;
		private string _gs_fydj;
		private string _gs_mzbl;
		private string _gs_zybl;
		private string _gs_dj;
		/// <summary>
		/// His 中药品，诊疗项目的 编码
		/// </summary>
		public string His_Code
		{
			set{ _his_code=value;}
			get{return _his_code;}
		}
		/// <summary>
		/// 项目分类：1-药品 2-诊疗
		/// </summary>
		public string Lb
		{
			set{ _lb=value;}
			get{return _lb;}
		}
		/// <summary>
		/// His 药品 诊疗名称
		/// </summary>
		public string His_Name
		{
			set{ _his_name=value;}
			get{return _his_name;}
		}
		/// <summary>
		/// His 药品 诊疗规格
		/// </summary>
		public string His_Gg
		{
			set{ _his_gg=value;}
			get{return _his_gg;}
		}
		/// <summary>
		/// His 药品 诊疗产地
		/// </summary>
		public string His_Cd
		{
			set{ _his_cd=value;}
			get{return _his_cd;}
		}
		/// <summary>
		/// His 药品 诊疗剂型
		/// </summary>
		public string His_Jx
		{
			set{ _his_jx=value;}
			get{return _his_jx;}
		}
		/// <summary>
		/// 城镇职工医保--费用等级
		/// </summary>
		public string Zg_FyDj
		{
			set{ _zg_fydj=value;}
			get{return _zg_fydj;}
		}
		/// <summary>
		/// 城镇职工医保--门诊自付比例
		/// </summary>
		public string Zg_MzBl
		{
			set{ _zg_mzbl=value;}
			get{return _zg_mzbl;}
		}
		/// <summary>
		/// 城镇职工--住院自付比例
		/// </summary>
		public string Zg_ZyBl
		{
			set{ _zg_zybl=value;}
			get{return _zg_zybl;}
		}
		/// <summary>
		/// 城镇职工--标准价格
		/// </summary>
		public string Zg_Dj
		{
			set{ _zg_dj=value;}
			get{return _zg_dj;}
		}
		/// <summary>
		/// 城镇居民医保--费用等级
		/// </summary>
		public string Jm_FyDj
		{
			set{ _jm_fydj=value;}
			get{return _jm_fydj;}
		}
		/// <summary>
		/// 城镇居民医保--门诊自付比例
		/// </summary>
		public string Jm_MzBl
		{
			set{ _jm_mzbl=value;}
			get{return _jm_mzbl;}
		}
		/// <summary>
		/// 城镇居民医保--住院自付比例
		/// </summary>
		public string Jm_ZyBl
		{
			set{ _jm_zybl=value;}
			get{return _jm_zybl;}
		}
		/// <summary>
		/// 城镇居民医保--标准价格
		/// </summary>
		public string Jm_Dj
		{
			set{ _jm_dj=value;}
			get{return _jm_dj;}
		}
		/// <summary>
		/// 工伤医疗--费用等级
		/// </summary>
		public string Gs_FyDj
		{
			set{ _gs_fydj=value;}
			get{return _gs_fydj;}
		}
		/// <summary>
		/// 工伤医疗--门诊自付比例
		/// </summary>
		public string Gs_MzBl
		{
			set{ _gs_mzbl=value;}
			get{return _gs_mzbl;}
		}
		/// <summary>
		/// 工伤医疗--住院自付比例
		/// </summary>
		public string Gs_ZyBl
		{
			set{ _gs_zybl=value;}
			get{return _gs_zybl;}
		}
		/// <summary>
		/// 工伤医疗--标准价格
		/// </summary>
		public string Gs_Dj
		{
			set{ _gs_dj=value;}
			get{return _gs_dj;}
		}
		#endregion Model

	}
}

