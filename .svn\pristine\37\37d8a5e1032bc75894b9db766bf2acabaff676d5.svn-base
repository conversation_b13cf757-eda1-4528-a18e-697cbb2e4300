﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="0" />
    <DataSources isList="true" count="0" />
    <Relations isList="true" count="0" />
    <Report isRef="0" />
    <Variables isList="true" count="10">
      <value>,充值金额,充值金额,System.Double,,False,False</value>
      <value>,充值卡号,充值卡号,System.String,,False,False</value>
      <value>,充值大写,充值大写,System.String,,False,False</value>
      <value>,打印时间,打印时间,System.String,,False,False</value>
      <value>,充值单位,充值单位,System.String,,False,False</value>
      <value>,经手人,经手人,System.String,,False,False</value>
      <value>,姓名,姓名,System.String,,False,False</value>
      <value>,类别,类别,System.String,,False,False</value>
      <value>,标题,标题,System.String,,False,False</value>
      <value>,充值ID,充值ID,System.String,,False,False</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="2" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="17">
        <Text1 Ref="3" type="Text" isKey="true">
          <Border>All;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>3.1,3.7,14,0.7</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>宋体,11</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text1</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{充值大写}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text1>
        <Text2 Ref="4" type="Text" isKey="true">
          <Border>All;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>1.3,3.7,1.8,0.7</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>宋体,11</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text2</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>充值大写:</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text2>
        <Text3 Ref="5" type="Text" isKey="true">
          <Border>All;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>3.1,3,14,0.7</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>宋体,11</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text3</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{Format("{0:N2}", 充值金额)}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text3>
        <Text4 Ref="6" type="Text" isKey="true">
          <Border>All;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>1.3,3,1.8,0.7</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>宋体,11</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text4</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>充值金额:</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text4>
        <Text11 Ref="7" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0,18.9,0.9</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>宋体,18,Bold</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text11</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{标题}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text11>
        <Text12 Ref="8" type="Text" isKey="true">
          <Border>All;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>3.1,1.6,7.4,0.7</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>宋体,10.5,Regular,Point,False,134</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text12</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{充值单位}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text12>
        <Text13 Ref="9" type="Text" isKey="true">
          <Border>All;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>1.3,1.6,1.8,0.7</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>宋体,10.5,Regular,Point,False,134</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text13</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>充值单位:</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text13>
        <Text16 Ref="10" type="Text" isKey="true">
          <Border>All;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>12.3,1.6,4.8,0.7</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>宋体,10.5,Regular,Point,False,134</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text16</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{打印时间}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text16>
        <Text17 Ref="11" type="Text" isKey="true">
          <Border>All;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>10.5,1.6,1.8,0.7</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>宋体,10.5,Regular,Point,False,134</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text17</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>打印时间:</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text17>
        <Text20 Ref="12" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>3.1,4.5,4.9,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>宋体,10.5,Regular,Point,False,134</Font>
          <Guid>9ad13818dcd04a4ca2462c7d6b1d9ac2</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text20</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{经手人}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Bottom</VertAlignment>
        </Text20>
        <Text21 Ref="13" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>1.3,4.5,1.8,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>宋体,10.5,Regular,Point,False,134</Font>
          <Guid>fa5d51c390f149e1b0c9877d1f3a0c85</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text21</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>经 手 人:</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Bottom</VertAlignment>
        </Text21>
        <Text7 Ref="14" type="Text" isKey="true">
          <Border>All;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>10.5,2.3,1.8,0.7</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>宋体,11</Font>
          <Guid>c3b57964682547a3a695a1ea4ecbbb32</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text7</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>充值卡号:</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text7>
        <Text8 Ref="15" type="Text" isKey="true">
          <Border>All;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>12.3,2.3,4.8,0.7</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>宋体,11</Font>
          <Guid>5319c5e11ba84d03a67e08ddf7667ddb</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text8</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{Format("{0:N2}", 充值卡号)}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text8>
        <Text9 Ref="16" type="Text" isKey="true">
          <Border>All;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>1.3,2.3,1.8,0.7</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>宋体,11</Font>
          <Guid>7196c5e112864a9d95853196f8cfc81f</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text9</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>姓    名:</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text9>
        <Text10 Ref="17" type="Text" isKey="true">
          <Border>All;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>3.1,2.3,3.2,0.7</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>宋体,11</Font>
          <Guid>9e320f349c5b48738850c77b9255d3ca</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text10</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{姓名}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text10>
        <Text5 Ref="18" type="Text" isKey="true">
          <Border>All;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>6.3,2.3,1.6,0.7</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>宋体,11</Font>
          <Guid>d2be0fc1328b464d884bcd43111d6b65</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text5</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>充值ID:</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text5>
        <Text6 Ref="19" type="Text" isKey="true">
          <Border>All;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>7.9,2.3,2.6,0.7</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>宋体,11</Font>
          <Guid>6182ca9422b448789813ea1ae05d291b</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text6</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{充值ID}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text6>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>7de5f689c7a24b1396110ca263fd2ad4</Guid>
      <Margins>1,1,1,1</Margins>
      <Name>Page1</Name>
      <PageHeight>9.3</PageHeight>
      <PageWidth>21</PageWidth>
      <Report isRef="0" />
      <Watermark Ref="20" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>宋体,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page1>
  </Pages>
  <PrinterSettings Ref="21" type="Stimulsoft.Report.Print.StiPrinterSettings" isKey="true" />
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>Report</ReportAlias>
  <ReportChanged>5/23/2015 1:08:07 PM</ReportChanged>
  <ReportCreated>12/12/2014 11:43:51 AM</ReportCreated>
  <ReportFile>E:\正在进行时\唐山\正在修改版\his2010\his2010\Rpt\健康卡充值打印.mrt</ReportFile>
  <ReportGuid>f4cc19bc6eef4979a937adb8e1362d4e</ReportGuid>
  <ReportName>Report</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2011.2.1026</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class Report : Stimulsoft.Report.StiReport
    {
        public Report()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
        #endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>