<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:tm="http://microsoft.com/wsdl/mime/textMatching/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:tns="http://tempuri.org/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:s="http://www.w3.org/2001/XMLSchema" xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <s:schema elementFormDefault="qualified" targetNamespace="http://tempuri.org/">
      <s:element name="HelloWorld">
        <s:complexType />
      </s:element>
      <s:element name="HelloWorldResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="HelloWorldResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetNhZs">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="NhYyCode" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetNhZsResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetNhZsResult">
              <s:complexType>
                <s:sequence>
                  <s:any minOccurs="0" maxOccurs="unbounded" namespace="http://www.w3.org/2001/XMLSchema" processContents="lax" />
                  <s:any minOccurs="1" namespace="urn:schemas-microsoft-com:xml-diffgram-v1" processContents="lax" />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="Yp_Download">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="V_GxTable">
              <s:complexType>
                <s:sequence>
                  <s:any minOccurs="0" maxOccurs="unbounded" namespace="http://www.w3.org/2001/XMLSchema" processContents="lax" />
                  <s:any minOccurs="1" namespace="urn:schemas-microsoft-com:xml-diffgram-v1" processContents="lax" />
                </s:sequence>
              </s:complexType>
            </s:element>
            <s:element minOccurs="0" maxOccurs="1" name="V_JxTable">
              <s:complexType>
                <s:sequence>
                  <s:any minOccurs="0" maxOccurs="unbounded" namespace="http://www.w3.org/2001/XMLSchema" processContents="lax" />
                  <s:any minOccurs="1" namespace="urn:schemas-microsoft-com:xml-diffgram-v1" processContents="lax" />
                </s:sequence>
              </s:complexType>
            </s:element>
            <s:element minOccurs="0" maxOccurs="1" name="V_YpDlTable">
              <s:complexType>
                <s:sequence>
                  <s:any minOccurs="0" maxOccurs="unbounded" namespace="http://www.w3.org/2001/XMLSchema" processContents="lax" />
                  <s:any minOccurs="1" namespace="urn:schemas-microsoft-com:xml-diffgram-v1" processContents="lax" />
                </s:sequence>
              </s:complexType>
            </s:element>
            <s:element minOccurs="0" maxOccurs="1" name="V_YpNameTable">
              <s:complexType>
                <s:sequence>
                  <s:any minOccurs="0" maxOccurs="unbounded" namespace="http://www.w3.org/2001/XMLSchema" processContents="lax" />
                  <s:any minOccurs="1" namespace="urn:schemas-microsoft-com:xml-diffgram-v1" processContents="lax" />
                </s:sequence>
              </s:complexType>
            </s:element>
            <s:element minOccurs="0" maxOccurs="1" name="V_YpMxTable">
              <s:complexType>
                <s:sequence>
                  <s:any minOccurs="0" maxOccurs="unbounded" namespace="http://www.w3.org/2001/XMLSchema" processContents="lax" />
                  <s:any minOccurs="1" namespace="urn:schemas-microsoft-com:xml-diffgram-v1" processContents="lax" />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="Yp_DownloadResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Yp_DownloadResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="Xm_Download">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="V_XmlbTable">
              <s:complexType>
                <s:sequence>
                  <s:any minOccurs="0" maxOccurs="unbounded" namespace="http://www.w3.org/2001/XMLSchema" processContents="lax" />
                  <s:any minOccurs="1" namespace="urn:schemas-microsoft-com:xml-diffgram-v1" processContents="lax" />
                </s:sequence>
              </s:complexType>
            </s:element>
            <s:element minOccurs="0" maxOccurs="1" name="V_XmNameTable">
              <s:complexType>
                <s:sequence>
                  <s:any minOccurs="0" maxOccurs="unbounded" namespace="http://www.w3.org/2001/XMLSchema" processContents="lax" />
                  <s:any minOccurs="1" namespace="urn:schemas-microsoft-com:xml-diffgram-v1" processContents="lax" />
                </s:sequence>
              </s:complexType>
            </s:element>
            <s:element minOccurs="0" maxOccurs="1" name="V_NhYyCode" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="Xm_DownloadResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Xm_DownloadResult" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="Mztc_XyDb">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="V_Hzyl_Zs_Code" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Zs_Name" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_CkCode" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="V_CkMoney" type="s:double" />
            <s:element minOccurs="0" maxOccurs="1" name="V_CkMemo" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_JsrCode" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_JsrName" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Db_Tb">
              <s:complexType>
                <s:sequence>
                  <s:any minOccurs="0" maxOccurs="unbounded" namespace="http://www.w3.org/2001/XMLSchema" processContents="lax" />
                  <s:any minOccurs="1" namespace="urn:schemas-microsoft-com:xml-diffgram-v1" processContents="lax" />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="Mztc_XyDbResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Mztc_XyDbResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="string" nillable="true" type="s:string" />
      <s:element name="DataTable" nillable="true">
        <s:complexType>
          <s:sequence>
            <s:any minOccurs="0" maxOccurs="unbounded" namespace="http://www.w3.org/2001/XMLSchema" processContents="lax" />
            <s:any minOccurs="1" namespace="urn:schemas-microsoft-com:xml-diffgram-v1" processContents="lax" />
          </s:sequence>
        </s:complexType>
      </s:element>
    </s:schema>
  </wsdl:types>
  <wsdl:message name="HelloWorldSoapIn">
    <wsdl:part name="parameters" element="tns:HelloWorld" />
  </wsdl:message>
  <wsdl:message name="HelloWorldSoapOut">
    <wsdl:part name="parameters" element="tns:HelloWorldResponse" />
  </wsdl:message>
  <wsdl:message name="GetNhZsSoapIn">
    <wsdl:part name="parameters" element="tns:GetNhZs" />
  </wsdl:message>
  <wsdl:message name="GetNhZsSoapOut">
    <wsdl:part name="parameters" element="tns:GetNhZsResponse" />
  </wsdl:message>
  <wsdl:message name="Yp_DownloadSoapIn">
    <wsdl:part name="parameters" element="tns:Yp_Download" />
  </wsdl:message>
  <wsdl:message name="Yp_DownloadSoapOut">
    <wsdl:part name="parameters" element="tns:Yp_DownloadResponse" />
  </wsdl:message>
  <wsdl:message name="Xm_DownloadSoapIn">
    <wsdl:part name="parameters" element="tns:Xm_Download" />
  </wsdl:message>
  <wsdl:message name="Xm_DownloadSoapOut">
    <wsdl:part name="parameters" element="tns:Xm_DownloadResponse" />
  </wsdl:message>
  <wsdl:message name="Mztc_XyDbSoapIn">
    <wsdl:part name="parameters" element="tns:Mztc_XyDb" />
  </wsdl:message>
  <wsdl:message name="Mztc_XyDbSoapOut">
    <wsdl:part name="parameters" element="tns:Mztc_XyDbResponse" />
  </wsdl:message>
  <wsdl:message name="HelloWorldHttpGetIn" />
  <wsdl:message name="HelloWorldHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="GetNhZsHttpGetIn">
    <wsdl:part name="NhYyCode" type="s:string" />
  </wsdl:message>
  <wsdl:message name="GetNhZsHttpGetOut">
    <wsdl:part name="Body" element="tns:DataTable" />
  </wsdl:message>
  <wsdl:message name="HelloWorldHttpPostIn" />
  <wsdl:message name="HelloWorldHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="GetNhZsHttpPostIn">
    <wsdl:part name="NhYyCode" type="s:string" />
  </wsdl:message>
  <wsdl:message name="GetNhZsHttpPostOut">
    <wsdl:part name="Body" element="tns:DataTable" />
  </wsdl:message>
  <wsdl:portType name="OracleServiceSoap">
    <wsdl:operation name="HelloWorld">
      <wsdl:input message="tns:HelloWorldSoapIn" />
      <wsdl:output message="tns:HelloWorldSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetNhZs">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;返回卫生室字典</wsdl:documentation>
      <wsdl:input message="tns:GetNhZsSoapIn" />
      <wsdl:output message="tns:GetNhZsSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="Yp_Download">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;同步卫生室药品目录</wsdl:documentation>
      <wsdl:input message="tns:Yp_DownloadSoapIn" />
      <wsdl:output message="tns:Yp_DownloadSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="Xm_Download">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;同步卫生室药品目录</wsdl:documentation>
      <wsdl:input message="tns:Xm_DownloadSoapIn" />
      <wsdl:output message="tns:Xm_DownloadSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="Mztc_XyDb">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;同步卫生室调拨</wsdl:documentation>
      <wsdl:input message="tns:Mztc_XyDbSoapIn" />
      <wsdl:output message="tns:Mztc_XyDbSoapOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:portType name="OracleServiceHttpGet">
    <wsdl:operation name="HelloWorld">
      <wsdl:input message="tns:HelloWorldHttpGetIn" />
      <wsdl:output message="tns:HelloWorldHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="GetNhZs">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;返回卫生室字典</wsdl:documentation>
      <wsdl:input message="tns:GetNhZsHttpGetIn" />
      <wsdl:output message="tns:GetNhZsHttpGetOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:portType name="OracleServiceHttpPost">
    <wsdl:operation name="HelloWorld">
      <wsdl:input message="tns:HelloWorldHttpPostIn" />
      <wsdl:output message="tns:HelloWorldHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="GetNhZs">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;返回卫生室字典</wsdl:documentation>
      <wsdl:input message="tns:GetNhZsHttpPostIn" />
      <wsdl:output message="tns:GetNhZsHttpPostOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="OracleServiceSoap" type="tns:OracleServiceSoap">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="HelloWorld">
      <soap:operation soapAction="http://tempuri.org/HelloWorld" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetNhZs">
      <soap:operation soapAction="http://tempuri.org/GetNhZs" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Yp_Download">
      <soap:operation soapAction="http://tempuri.org/Yp_Download" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Xm_Download">
      <soap:operation soapAction="http://tempuri.org/Xm_Download" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Mztc_XyDb">
      <soap:operation soapAction="http://tempuri.org/Mztc_XyDb" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="OracleServiceSoap12" type="tns:OracleServiceSoap">
    <soap12:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="HelloWorld">
      <soap12:operation soapAction="http://tempuri.org/HelloWorld" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetNhZs">
      <soap12:operation soapAction="http://tempuri.org/GetNhZs" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Yp_Download">
      <soap12:operation soapAction="http://tempuri.org/Yp_Download" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Xm_Download">
      <soap12:operation soapAction="http://tempuri.org/Xm_Download" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Mztc_XyDb">
      <soap12:operation soapAction="http://tempuri.org/Mztc_XyDb" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="OracleServiceHttpGet" type="tns:OracleServiceHttpGet">
    <http:binding verb="GET" />
    <wsdl:operation name="HelloWorld">
      <http:operation location="/HelloWorld" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetNhZs">
      <http:operation location="/GetNhZs" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="OracleServiceHttpPost" type="tns:OracleServiceHttpPost">
    <http:binding verb="POST" />
    <wsdl:operation name="HelloWorld">
      <http:operation location="/HelloWorld" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetNhZs">
      <http:operation location="/GetNhZs" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="OracleService">
    <wsdl:port name="OracleServiceSoap" binding="tns:OracleServiceSoap">
      <soap:address location="http://localhost/Qx_Database/OracleService.asmx" />
    </wsdl:port>
    <wsdl:port name="OracleServiceSoap12" binding="tns:OracleServiceSoap12">
      <soap12:address location="http://localhost/Qx_Database/OracleService.asmx" />
    </wsdl:port>
    <wsdl:port name="OracleServiceHttpGet" binding="tns:OracleServiceHttpGet">
      <http:address location="http://localhost/Qx_Database/OracleService.asmx" />
    </wsdl:port>
    <wsdl:port name="OracleServiceHttpPost" binding="tns:OracleServiceHttpPost">
      <http:address location="http://localhost/Qx_Database/OracleService.asmx" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>