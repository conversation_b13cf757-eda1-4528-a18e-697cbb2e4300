﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="0" />
    <DataSources isList="true" count="5">
      <西药 Ref="2" type="DataTableSource" isKey="true">
        <Alias>西药</Alias>
        <Columns isList="true" count="10">
          <value>Mx_Code,System.String</value>
          <value>Mz_id,System.Int32</value>
          <value>Mx_Gg,System.String</value>
          <value>Mz_Sl,System.Decimal</value>
          <value>Mx_XsDw,System.String</value>
          <value>Yp_Yfyl,System.String</value>
          <value>Mz_Money,System.Decimal</value>
          <value><PERSON>J<PERSON>,System.String</value>
          <value>Yp_Name,System.String</value>
          <value>Special,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>西药</Name>
        <NameInSource>西药</NameInSource>
      </西药>
      <中成药 Ref="3" type="DataTableSource" isKey="true">
        <Alias>中成药</Alias>
        <Columns isList="true" count="10">
          <value>Mx_Code,System.String</value>
          <value>Mz_id,System.Int32</value>
          <value>Mx_Gg,System.String</value>
          <value>Mz_Sl,System.Decimal</value>
          <value>Mx_XsDw,System.String</value>
          <value>Yp_Yfyl,System.String</value>
          <value>Mz_Money,System.Decimal</value>
          <value>IsJb,System.String</value>
          <value>Yp_Name,System.String</value>
          <value>Special,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>中成药</Name>
        <NameInSource>中成药</NameInSource>
      </中成药>
      <中药处方 Ref="4" type="DataTableSource" isKey="true">
        <Alias>中药处方</Alias>
        <Columns isList="true" count="10">
          <value>Mx_Code,System.String</value>
          <value>Mz_id,System.Int32</value>
          <value>Mx_Gg,System.String</value>
          <value>Mz_Sl,System.Decimal</value>
          <value>Mx_XsDw,System.String</value>
          <value>Yp_Yfyl,System.String</value>
          <value>Mz_Money,System.Decimal</value>
          <value>IsJb,System.String</value>
          <value>Yp_Name,System.String</value>
          <value>Special,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>中药处方</Name>
        <NameInSource>中药处方</NameInSource>
      </中药处方>
      <卫材处方 Ref="5" type="DataTableSource" isKey="true">
        <Alias>卫材处方</Alias>
        <Columns isList="true" count="10">
          <value>Mx_Code,System.String</value>
          <value>Mz_id,System.Int32</value>
          <value>Mx_Gg,System.String</value>
          <value>Mz_Sl,System.Decimal</value>
          <value>Mx_XsDw,System.String</value>
          <value>Yp_Yfyl,System.String</value>
          <value>Mz_Money,System.Decimal</value>
          <value>IsJb,System.String</value>
          <value>Yp_Name,System.String</value>
          <value>Special,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>卫材处方</Name>
        <NameInSource>卫材处方</NameInSource>
      </卫材处方>
      <诊疗处方 Ref="6" type="DataTableSource" isKey="true">
        <Alias>诊疗处方</Alias>
        <Columns isList="true" count="8">
          <value>Mx_Code,System.String</value>
          <value>Mz_id,System.Int32</value>
          <value>Mx_Gg,System.String</value>
          <value>Mz_Sl,System.Decimal</value>
          <value>Mx_XsDw,System.String</value>
          <value>Yp_Yfyl,System.String</value>
          <value>Mz_Money,System.Decimal</value>
          <value>Yp_Name,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>诊疗处方</Name>
        <NameInSource>诊疗处方</NameInSource>
      </诊疗处方>
    </DataSources>
    <Relations isList="true" count="0" />
    <Report isRef="0" />
    <Variables isList="true" count="17">
      <value>,经手人,经手人,System.String,,False,False</value>
      <value>,门诊编号,门诊编号,System.String,,False,False</value>
      <value>,科室,科室,System.String,,False,False</value>
      <value>,打印时间,打印时间,System.String,,False,False</value>
      <value>,患者姓名,患者姓名,System.String,,False,False</value>
      <value>,患者性别,患者性别,System.String,,False,False</value>
      <value>,年龄,年龄,System.String,,False,False</value>
      <value>,类别,类别,System.String,,False,False</value>
      <value>,临床诊断,临床诊断,System.String,,False,False</value>
      <value>,金额,金额,System.String,,False,False</value>
      <value>,标题,标题,System.String,,False,False</value>
      <value>,处方类别2,处方类别2,System.String,,False,False</value>
      <value>,处方类别1,处方类别1,System.String,,False,False</value>
      <value>,处方类别3,处方类别3,System.String,,False,False</value>
      <value>,医生签名,医生签名,System.Drawing.Image,,False,False</value>
      <value>,药房医生,药房医生,System.Drawing.Image,,False,False</value>
      <value>,医生签名字,医生签名字,System.String,,False,False</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="5">
    <Page1 Ref="7" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="6">
        <Text27 Ref="8" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>12.3,9.2,1,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>宋体,10.5</Font>
          <Guid>808943933f7249519419e61a9e7eb63f</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text27</Name>
          <Page isRef="7" />
          <Parent isRef="7" />
          <Text>金额</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Bottom</VertAlignment>
        </Text27>
        <Text30 Ref="9" type="Text" isKey="true">
          <Border>Bottom;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>13.3,9.2,2.8,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>宋体,10.5,Regular,Point,False,134</Font>
          <Guid>d35c598bc8b040b3a899c23b2a10978d</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text30</Name>
          <Page isRef="7" />
          <Parent isRef="7" />
          <Text>{Sum(GroupHeaderBand1,西药.Mz_Money)}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Totals</Type>
          <VertAlignment>Bottom</VertAlignment>
        </Text30>
        <ReportTitleBand1 Ref="10" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,10.5,0</ClientRectangle>
          <Components isList="true" count="0" />
          <Conditions isList="true" count="0" />
          <Guid>651739b2c3d840d4b6b05fcfedb47a12</Guid>
          <Name>ReportTitleBand1</Name>
          <Page isRef="7" />
          <Parent isRef="7" />
        </ReportTitleBand1>
        <GroupHeaderBand1 Ref="11" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,1.2,10.5,4.8</ClientRectangle>
          <Components isList="true" count="17">
            <Text3 Ref="12" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.6,10.5,0.79</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,14.25,Bold,Point,False,134</Font>
              <Guid>373a84218f7f4d0e9cb98424a105bc39</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text3</Name>
              <Page isRef="7" />
              <Parent isRef="11" />
              <Text>{标题}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text3>
            <Text4 Ref="13" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.4,1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>843e853605874d01b372d01f815ddfcf</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text4</Name>
              <Page isRef="7" />
              <Parent isRef="11" />
              <Text>科别:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text4>
            <Text6 Ref="14" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>3.7,1.4,6.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>2ce147c089e94901adfd9dcf1ea503c6</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text6</Name>
              <Page isRef="7" />
              <Parent isRef="11" />
              <Text>打印时间：{打印时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text6>
            <Text5 Ref="15" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.1,1.4,2.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>bc4302cb138a4599a13394731c5c6158</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text5</Name>
              <Page isRef="7" />
              <Parent isRef="11" />
              <Text>{科室}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text5>
            <Text9 Ref="16" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,2.7,1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>e239b6efbf3b4b90b93997ba2912f223</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text9</Name>
              <Page isRef="7" />
              <Parent isRef="11" />
              <Text>姓名</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text9>
            <Text12 Ref="17" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1,2.7,2.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>0223bc4051aa4238a08e0ab773236515</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text12</Name>
              <Page isRef="7" />
              <Parent isRef="11" />
              <Text>{患者姓名}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text12>
            <Text13 Ref="18" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.7,2.7,1.1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>70b8d262ead8450fa8b75b9d484c4ceb</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text13</Name>
              <Page isRef="7" />
              <Parent isRef="11" />
              <Text>年龄</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text13>
            <Text14 Ref="19" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.8,2.7,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>dd0fede85ae24ce8b91e32fd4daf3fc6</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text14</Name>
              <Page isRef="7" />
              <Parent isRef="11" />
              <Text>{年龄}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text14>
            <Text15 Ref="20" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6.6,2.7,1.3,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>b40dfcaa438a450b97dca30c40a67694</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text15</Name>
              <Page isRef="7" />
              <Parent isRef="11" />
              <Text>性别</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text15>
            <Text16 Ref="21" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.9,2.7,2.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>0b9437b2cdf74e0b86028f322a4d1d9f</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text16</Name>
              <Page isRef="7" />
              <Parent isRef="11" />
              <Text>{患者性别}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text16>
            <Text18 Ref="22" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,3.9,10.5,0.9</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Times New Roman,21.75,Bold</Font>
              <Guid>4bd9191f990c4c088455976e81922396</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text18</Name>
              <Page isRef="7" />
              <Parent isRef="11" />
              <Text>R:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text18>
            <Text1 Ref="23" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>3.5,2,7,0.65</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>ae1056b6222f44548451a630475d9696</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text1</Name>
              <Page isRef="7" />
              <Parent isRef="11" />
              <Text>NO.{门诊编号}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text1>
            <Text7 Ref="24" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,2,1,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>3256b6ca0058477eb8d09bd7fdb7011d</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text7</Name>
              <Page isRef="7" />
              <Parent isRef="11" />
              <Text>费别:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text7>
            <Text8 Ref="25" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1,2,2.5,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>49d0be77f0dc404487d2193b361ac6c8</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text8</Name>
              <Page isRef="7" />
              <Parent isRef="11" />
              <Text>{类别}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text8>
            <Text11 Ref="26" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,3.3,1.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>c91a17fefb844d6bb87f13a7ec32aea1</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text11</Name>
              <Page isRef="7" />
              <Parent isRef="11" />
              <Text>临床诊断</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text11>
            <Text17 Ref="27" type="Text" isKey="true">
              <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.7,3.3,8.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>edb57b29db5b4cc885969bad9922abe5</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text17</Name>
              <Page isRef="7" />
              <Parent isRef="11" />
              <Text>{临床诊断}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text17>
            <Text171 Ref="28" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9.5,0,1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,10.5,Regular,Point,False,0</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text171</Name>
              <Page isRef="7" />
              <Parent isRef="11" />
              <Text>{处方类别1}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Bottom</VertAlignment>
            </Text171>
          </Components>
          <Condition>{西药.Mx_Code}</Condition>
          <Conditions isList="true" count="0" />
          <Guid>a749f17ac18945828b0409dc59e41855</Guid>
          <Name>GroupHeaderBand1</Name>
          <Page isRef="7" />
          <Parent isRef="7" />
        </GroupHeaderBand1>
        <DataBand1 Ref="29" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,6.8,10.5,1.8</ClientRectangle>
          <Columns>1</Columns>
          <Components isList="true" count="5">
            <Text160 Ref="30" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.01,5.4,1.2</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>fc4f342b4d5541b38bec90ccbf17314d</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text160</Name>
              <Page isRef="7" />
              <Parent isRef="29" />
              <Text>      {西药.Yp_Name}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text160>
            <Text161 Ref="31" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>5.4,0.01,2.5,1.2</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>bbe13674d2524c9aa127e2ad272b1ddc</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text161</Name>
              <Page isRef="7" />
              <Parent isRef="29" />
              <Text>{西药.Mx_Gg}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text161>
            <Text162 Ref="32" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7.9,0.01,1.3,1.2</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>bd059bac922e41cf99819ef00ad441a4</Guid>
              <HideZeros>True</HideZeros>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text162</Name>
              <NullValue> </NullValue>
              <Page isRef="7" />
              <Parent isRef="29" />
              <Text>{西药.Mz_Sl}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="33" type="CustomFormat" isKey="true">
                <StringFormat>0.####</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text162>
            <Text163 Ref="34" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>9.2,0.01,1.3,1.2</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>39e1b930a248470d88895c495ce02b2c</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text163</Name>
              <Page isRef="7" />
              <Parent isRef="29" />
              <Text>{西药.Mx_XsDw}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text163>
            <Text164 Ref="35" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.2,10.5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>075694dd6bd24eba87db603af2d5cd98</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text164</Name>
              <Page isRef="7" />
              <Parent isRef="29" />
              <Text>{西药.Yp_Yfyl}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text164>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>西药</DataSourceName>
          <Filters isList="true" count="1">
            <value>,NotEqualTo,,,String</value>
          </Filters>
          <Guid>d9b30d61dac8424f94442ca49d33c876</Guid>
          <Name>DataBand1</Name>
          <Page isRef="7" />
          <Parent isRef="7" />
          <Sort isList="true" count="0" />
        </DataBand1>
        <GroupFooterBand1 Ref="36" type="GroupFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,9.4,10.5,3.5</ClientRectangle>
          <Components isList="true" count="20">
            <Text31 Ref="37" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.3,0.6,2.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>0d3ab5c73b7543d18fcde5d9305994e7</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text31</Name>
              <Page isRef="7" />
              <Parent isRef="36" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text31>
            <Text22 Ref="38" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>6,0.6,0.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>db469fbe118448789e08e5d3a82127fe</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text22</Name>
              <Page isRef="7" />
              <Parent isRef="36" />
              <Text>审核</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text22>
            <Text19 Ref="39" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0.1,0.6,0.9,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>590cfd4cefa5495d9f52e53fb003c419</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text19</Name>
              <Page isRef="7" />
              <Parent isRef="36" />
              <Text>医师</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text19>
            <Text20 Ref="40" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>6,1.8,0.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>14d0abc8059542ccb41b2f34664a68d5</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text20</Name>
              <Page isRef="7" />
              <Parent isRef="36" />
              <Text>调配</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text20>
            <Text25 Ref="41" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.3,1.8,2.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>e6d3a42a23e64807a91c784df48103a2</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text25</Name>
              <Page isRef="7" />
              <Parent isRef="36" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text25>
            <Image2 Ref="42" type="Image" isKey="true">
              <AspectRatio>True</AspectRatio>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.2,0.1,2.6,1.1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <HorAlignment>Center</HorAlignment>
              <ImageData>{医生签名}</ImageData>
              <Name>Image2</Name>
              <Page isRef="7" />
              <Parent isRef="36" />
              <Stretch>True</Stretch>
              <VertAlignment>Center</VertAlignment>
            </Image2>
            <Text2 Ref="43" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.2,1.1,2.6,0.1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>ea0e90a0ca8249c18b04cd6d07486139</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text2</Name>
              <Page isRef="7" />
              <Parent isRef="36" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text2>
            <Text23 Ref="44" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0.1,1.8,0.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>149ccb1fea6a4a15b67daf4eaaa7b47b</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text23</Name>
              <Page isRef="7" />
              <Parent isRef="36" />
              <Text>核对</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text23>
            <Text24 Ref="45" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.2,1.8,2.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>f7648559e5134a5386f60d0883a67041</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text24</Name>
              <Page isRef="7" />
              <Parent isRef="36" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text24>
            <Text26 Ref="46" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.7,2.9,0.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>f8b7d5f80de247d0b2ff2af240acd473</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text26</Name>
              <Page isRef="7" />
              <Parent isRef="36" />
              <Text>发药</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text26>
            <Text28 Ref="47" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.8,2.9,2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>3c98001e3297421db905ce5e382dea37</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text28</Name>
              <Page isRef="7" />
              <Parent isRef="36" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text28>
            <Image6 Ref="48" type="Image" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7.3,0.1,2.7,1.1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <ImageData>{药房医生}</ImageData>
              <Name>Image6</Name>
              <Page isRef="7" />
              <Parent isRef="36" />
            </Image6>
            <Image7 Ref="49" type="Image" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.2,1.3,2.6,1.1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Guid>e6e9df41e4814195bb02ad35701ac00e</Guid>
              <ImageData>{药房医生}</ImageData>
              <Name>Image7</Name>
              <Page isRef="7" />
              <Parent isRef="36" />
            </Image7>
            <Image8 Ref="50" type="Image" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7.3,1.3,2.7,1.1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Guid>164d6bd54cb7425eaa4d99458305cff5</Guid>
              <ImageData>{药房医生}</ImageData>
              <Name>Image8</Name>
              <Page isRef="7" />
              <Parent isRef="36" />
            </Image8>
            <Image9 Ref="51" type="Image" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>3.8,2.4,2.6,1.1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Guid>b977b6e8d2e541c4b3254d87ec60d398</Guid>
              <ImageData>{药房医生}</ImageData>
              <Name>Image9</Name>
              <Page isRef="7" />
              <Parent isRef="36" />
            </Image9>
            <Text126 Ref="52" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.2,0.5,2.7,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text126</Name>
              <Page isRef="7" />
              <Parent isRef="36" />
              <Text>{医生签名字}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Bottom</VertAlignment>
            </Text126>
            <Text127 Ref="53" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7.3,0.5,2.7,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>3b868fd8780246ceb45896f6bfb09158</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text127</Name>
              <Page isRef="7" />
              <Parent isRef="36" />
              <Text>{医生签名字}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Bottom</VertAlignment>
            </Text127>
            <Text165 Ref="54" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.2,1.7,2.7,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>6480987b4282402e92475eea85160d69</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text165</Name>
              <Page isRef="7" />
              <Parent isRef="36" />
              <Text>{医生签名字}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Bottom</VertAlignment>
            </Text165>
            <Text166 Ref="55" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7.3,1.7,2.7,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>ad787239c4c34947b0eb7b13fd0020c7</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text166</Name>
              <Page isRef="7" />
              <Parent isRef="36" />
              <Text>{医生签名字}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Bottom</VertAlignment>
            </Text166>
            <Text167 Ref="56" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>3.8,2.8,2.7,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>2b621183d1fc481fb785e5570737fa29</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text167</Name>
              <Page isRef="7" />
              <Parent isRef="36" />
              <Text>{医生签名字}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Bottom</VertAlignment>
            </Text167>
          </Components>
          <Conditions isList="true" count="0" />
          <Guid>fe7e19ad84744d48a19c766f037e0da7</Guid>
          <Name>GroupFooterBand1</Name>
          <Page isRef="7" />
          <Parent isRef="7" />
        </GroupFooterBand1>
      </Components>
      <Conditions isList="true" count="0" />
      <Enabled>False</Enabled>
      <Guid>e4e1e4db58f545af984c08e14b4348db</Guid>
      <Margins>1,1,0.8,1</Margins>
      <Name>Page1</Name>
      <PageHeight>21</PageHeight>
      <PageWidth>12.5</PageWidth>
      <Report isRef="0" />
      <Watermark Ref="57" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page1>
    <Page5 Ref="58" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="4">
        <ReportTitleBand5 Ref="59" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,10.5,0</ClientRectangle>
          <Components isList="true" count="0" />
          <Conditions isList="true" count="0" />
          <Guid>651739b2c3d840d4b6b05fcfedb47a12</Guid>
          <Name>ReportTitleBand5</Name>
          <Page isRef="58" />
          <Parent isRef="58" />
        </ReportTitleBand5>
        <GroupHeaderBand5 Ref="60" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,1.2,10.5,5.1</ClientRectangle>
          <Components isList="true" count="17">
            <Text129 Ref="61" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.8,10.5,0.79</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,14.25,Bold,Point,False,134</Font>
              <Guid>2a5a313f04704cc7accc92b1b566a49e</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text129</Name>
              <Page isRef="58" />
              <Parent isRef="60" />
              <Text>{标题}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text129>
            <Text130 Ref="62" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.7,1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>e573d3e87cfd409c9b3784bafa1eba0f</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text130</Name>
              <Page isRef="58" />
              <Parent isRef="60" />
              <Text>科别:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text130>
            <Text131 Ref="63" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>3.6,1.7,6.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>9dace1dc40af47be8cf99002798ba19d</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text131</Name>
              <Page isRef="58" />
              <Parent isRef="60" />
              <Text>打印时间：{打印时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text131>
            <Text132 Ref="64" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1,1.7,2.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>1d183d17e4dc47408d177791ed040d53</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text132</Name>
              <Page isRef="58" />
              <Parent isRef="60" />
              <Text>{科室}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text132>
            <Text133 Ref="65" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,3,1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>5688536f6ccd48eab1a8e482399cf270</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text133</Name>
              <Page isRef="58" />
              <Parent isRef="60" />
              <Text>姓名</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text133>
            <Text134 Ref="66" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1,3,2.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>e7723ba0298047e48fc62a7650dc3e5c</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text134</Name>
              <Page isRef="58" />
              <Parent isRef="60" />
              <Text>{患者姓名}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text134>
            <Text136 Ref="67" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.7,3,1.1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>5674287dba484290a951656641ffd4d9</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text136</Name>
              <Page isRef="58" />
              <Parent isRef="60" />
              <Text>年龄</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text136>
            <Text137 Ref="68" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.8,3,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>1f825c20ab6241f8b8c5a0f809ebe3b4</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text137</Name>
              <Page isRef="58" />
              <Parent isRef="60" />
              <Text>{年龄}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text137>
            <Text138 Ref="69" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6.6,3,1.3,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>521d05291bb347c3b56abaa16307fa01</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text138</Name>
              <Page isRef="58" />
              <Parent isRef="60" />
              <Text>性别</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text138>
            <Text139 Ref="70" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.9,3,2.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>3f4ae234f71a41d29643c8584cdf541e</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text139</Name>
              <Page isRef="58" />
              <Parent isRef="60" />
              <Text>{患者性别}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text139>
            <Text140 Ref="71" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,4.2,10.5,0.9</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Times New Roman,21.75,Bold</Font>
              <Guid>aba8a255194141e287e8a097e7e1f715</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text140</Name>
              <Page isRef="58" />
              <Parent isRef="60" />
              <Text>R:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text140>
            <Text141 Ref="72" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>3.5,2.3,7,0.65</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>36c7b98c78864e87973cfd2a9548c36a</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text141</Name>
              <Page isRef="58" />
              <Parent isRef="60" />
              <Text>NO.{门诊编号}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text141>
            <Text142 Ref="73" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,2.3,1,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>f4ca4ccee9674e78a45f9b871719f86c</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text142</Name>
              <Page isRef="58" />
              <Parent isRef="60" />
              <Text>费别:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text142>
            <Text143 Ref="74" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1,2.3,2.5,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>aa3b33224d614a62881595a26c25973e</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text143</Name>
              <Page isRef="58" />
              <Parent isRef="60" />
              <Text>{类别}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text143>
            <Text144 Ref="75" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,3.6,1.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>65d5d9ebbb2941069df083a89a49ec03</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text144</Name>
              <Page isRef="58" />
              <Parent isRef="60" />
              <Text>临床诊断</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text144>
            <Text145 Ref="76" type="Text" isKey="true">
              <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.7,3.6,8.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>ecd796e8962641a68ab94f873c589bac</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text145</Name>
              <Page isRef="58" />
              <Parent isRef="60" />
              <Text>{临床诊断}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text145>
            <Text146 Ref="77" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9.5,0.1,1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,10.5,Regular,Point,False,0</Font>
              <Guid>c5e94eee63d84863aabe455560109c6c</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text146</Name>
              <Page isRef="58" />
              <Parent isRef="60" />
              <Text>{处方类别1}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Bottom</VertAlignment>
            </Text146>
          </Components>
          <Condition>{中成药.Mx_Code}</Condition>
          <Conditions isList="true" count="0" />
          <Guid>32df8316d8354f0a9b9a8678ad7f0c2b</Guid>
          <Name>GroupHeaderBand5</Name>
          <Page isRef="58" />
          <Parent isRef="58" />
        </GroupHeaderBand5>
        <DataBand5 Ref="78" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,7.1,10.5,1.8</ClientRectangle>
          <Columns>1</Columns>
          <Components isList="true" count="5">
            <Text152 Ref="79" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.01,5.4,1.2</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>335d237023c640f48874584ffcb3882b</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text152</Name>
              <Page isRef="58" />
              <Parent isRef="78" />
              <Text>      {中成药.Yp_Name}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text152>
            <Text153 Ref="80" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>5.4,0.01,2.5,1.2</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>17baed80d9c440aab61542cd4ee9153f</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text153</Name>
              <Page isRef="58" />
              <Parent isRef="78" />
              <Text>{中成药.Mx_Gg}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text153>
            <Text154 Ref="81" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7.9,0.01,1.3,1.2</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>67eae76af2ca4747948d105808e0a1f9</Guid>
              <HideZeros>True</HideZeros>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text154</Name>
              <NullValue> </NullValue>
              <Page isRef="58" />
              <Parent isRef="78" />
              <Text>{中成药.Mz_Sl}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="82" type="CustomFormat" isKey="true">
                <StringFormat>0.####</StringFormat>
              </TextFormat>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text154>
            <Text155 Ref="83" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>9.2,0.01,1.3,1.2</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>66bfc1af0a874e828c35b08e61114287</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text155</Name>
              <Page isRef="58" />
              <Parent isRef="78" />
              <Text>{中成药.Mx_XsDw}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text155>
            <Text156 Ref="84" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.2,10.5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text156</Name>
              <Page isRef="58" />
              <Parent isRef="78" />
              <Text>{中成药.Yp_Yfyl}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text156>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>中成药</DataSourceName>
          <Filters isList="true" count="1">
            <value>,NotEqualTo,,,String</value>
          </Filters>
          <Guid>c6a9e6061c3e4378892221439d6e2dcf</Guid>
          <Name>DataBand5</Name>
          <Page isRef="58" />
          <Parent isRef="58" />
          <Sort isList="true" count="2">
            <value>ASC</value>
            <value>Mz_id</value>
          </Sort>
        </DataBand5>
        <GroupFooterBand5 Ref="85" type="GroupFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,9.7,10.5,3.5</ClientRectangle>
          <Components isList="true" count="20">
            <Text29 Ref="86" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.3,0.6,2.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>a61e95f43a724950beb7c628d651ef74</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text29</Name>
              <Page isRef="58" />
              <Parent isRef="85" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text29>
            <Text35 Ref="87" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>6,0.6,0.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>6770a0314c1c453783104d7cb1ee8af8</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text35</Name>
              <Page isRef="58" />
              <Parent isRef="85" />
              <Text>审核</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text35>
            <Text147 Ref="88" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0.1,0.6,0.9,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>81875a9a46774c8e824a0db05f43ec0c</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text147</Name>
              <Page isRef="58" />
              <Parent isRef="85" />
              <Text>医师</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text147>
            <Text148 Ref="89" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>6,1.8,0.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>e6a2ab5d0b564ab28e16d3d7a7fab1c0</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text148</Name>
              <Page isRef="58" />
              <Parent isRef="85" />
              <Text>调配</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text148>
            <Text149 Ref="90" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.3,1.8,2.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>ed1706f9e03b4105ba5dbeff6fcc757d</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text149</Name>
              <Page isRef="58" />
              <Parent isRef="85" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text149>
            <Image1 Ref="91" type="Image" isKey="true">
              <AspectRatio>True</AspectRatio>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.2,0.1,2.6,1.1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Guid>8314e44de5a54f65ac3d7254239f0ff5</Guid>
              <HorAlignment>Center</HorAlignment>
              <ImageData>{医生签名}</ImageData>
              <Name>Image1</Name>
              <Page isRef="58" />
              <Parent isRef="85" />
              <Stretch>True</Stretch>
              <VertAlignment>Center</VertAlignment>
            </Image1>
            <Text150 Ref="92" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.2,1.1,2.6,0.1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>88f02b17f9c0452fa84126ddafcb31b8</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text150</Name>
              <Page isRef="58" />
              <Parent isRef="85" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text150>
            <Text151 Ref="93" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0.1,1.8,0.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>5da5562eed8b43cd91ac422b816d6c9c</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text151</Name>
              <Page isRef="58" />
              <Parent isRef="85" />
              <Text>核对</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text151>
            <Text157 Ref="94" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.2,1.8,2.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>f100f44cdd7841b8b37d72649fd11d30</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text157</Name>
              <Page isRef="58" />
              <Parent isRef="85" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text157>
            <Text158 Ref="95" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.7,2.9,0.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>e76262747ea24beaa7c2bc42e4b83b76</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text158</Name>
              <Page isRef="58" />
              <Parent isRef="85" />
              <Text>发药</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text158>
            <Text159 Ref="96" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.8,2.9,2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>24873e049c5f4b6ab380661c4797980b</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text159</Name>
              <Page isRef="58" />
              <Parent isRef="85" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text159>
            <Image10 Ref="97" type="Image" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7.3,0.1,2.7,1.1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Guid>98fe4f7297fa46d4abd752f93721ce98</Guid>
              <ImageData>{药房医生}</ImageData>
              <Name>Image10</Name>
              <Page isRef="58" />
              <Parent isRef="85" />
            </Image10>
            <Image11 Ref="98" type="Image" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.2,1.3,2.6,1.1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Guid>1c0d3934428d4916a58da16fd72f0784</Guid>
              <ImageData>{药房医生}</ImageData>
              <Name>Image11</Name>
              <Page isRef="58" />
              <Parent isRef="85" />
            </Image11>
            <Image12 Ref="99" type="Image" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7.3,1.3,2.7,1.1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Guid>a1dbfc12d3cf4c14af6c12d841d6cd07</Guid>
              <ImageData>{药房医生}</ImageData>
              <Name>Image12</Name>
              <Page isRef="58" />
              <Parent isRef="85" />
            </Image12>
            <Image13 Ref="100" type="Image" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>3.8,2.4,2.6,1.1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Guid>d55f4a468518465fa821faa968406fb3</Guid>
              <ImageData>{药房医生}</ImageData>
              <Name>Image13</Name>
              <Page isRef="58" />
              <Parent isRef="85" />
            </Image13>
            <Text168 Ref="101" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.2,0.5,2.7,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>343ee432c4404e58b48494c23c4930c7</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text168</Name>
              <Page isRef="58" />
              <Parent isRef="85" />
              <Text>{医生签名字}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Bottom</VertAlignment>
            </Text168>
            <Text169 Ref="102" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7.3,0.5,2.7,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>8c677d29f3b34722ac070019cce3be59</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text169</Name>
              <Page isRef="58" />
              <Parent isRef="85" />
              <Text>{医生签名字}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Bottom</VertAlignment>
            </Text169>
            <Text170 Ref="103" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.2,1.7,2.7,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>a37153dc4e5f42daa1797ca2d83150ca</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text170</Name>
              <Page isRef="58" />
              <Parent isRef="85" />
              <Text>{医生签名字}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Bottom</VertAlignment>
            </Text170>
            <Text172 Ref="104" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7.3,1.7,2.7,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>31cf53647c5e4818bc5ed4759064c351</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text172</Name>
              <Page isRef="58" />
              <Parent isRef="85" />
              <Text>{医生签名字}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Bottom</VertAlignment>
            </Text172>
            <Text173 Ref="105" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>3.8,2.8,2.7,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>fbeec373bae246518f8abb041b7f615b</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text173</Name>
              <Page isRef="58" />
              <Parent isRef="85" />
              <Text>{医生签名字}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Bottom</VertAlignment>
            </Text173>
          </Components>
          <Conditions isList="true" count="0" />
          <Guid>c4da96c8a99543388e51c28a35a3366c</Guid>
          <Name>GroupFooterBand5</Name>
          <Page isRef="58" />
          <Parent isRef="58" />
        </GroupFooterBand5>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>3d831539b0cc425db6d09faa776083d2</Guid>
      <Margins>1,1,0.8,1</Margins>
      <Name>Page5</Name>
      <PageHeight>21</PageHeight>
      <PageWidth>12.5</PageWidth>
      <Report isRef="0" />
      <Watermark Ref="106" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page5>
    <Page2 Ref="107" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="8">
        <Text58 Ref="108" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>12.3,10,1,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>宋体,10.5</Font>
          <Guid>d85f38c0a3544511b6851d5004327295</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text58</Name>
          <Page isRef="107" />
          <Parent isRef="107" />
          <Text>金额</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Bottom</VertAlignment>
        </Text58>
        <Text59 Ref="109" type="Text" isKey="true">
          <Border>Bottom;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>13.3,10,2.8,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>宋体,10.5,Regular,Point,False,134</Font>
          <Guid>1ec08265e0f440718d350118c3747b28</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text59</Name>
          <Page isRef="107" />
          <Parent isRef="107" />
          <Text>{Sum(GroupHeaderBand2,中药处方.Mz_Money)}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Totals</Type>
          <VertAlignment>Bottom</VertAlignment>
        </Text59>
        <Text62 Ref="110" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>13.3,9,0.9,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>宋体,10.5</Font>
          <Guid>3a2c32c0d0cb4515a603f4aa13436a72</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text62</Name>
          <Page isRef="107" />
          <Parent isRef="107" />
          <Text>发药</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Bottom</VertAlignment>
        </Text62>
        <Text68 Ref="111" type="Text" isKey="true">
          <Border>Bottom;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>14.5,9,2,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>宋体,10.5,Regular,Point,False,134</Font>
          <Guid>db48a1bb842240728b58bbd32e04300c</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text68</Name>
          <Page isRef="107" />
          <Parent isRef="107" />
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Bottom</VertAlignment>
        </Text68>
        <ReportTitleBand2 Ref="112" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,10.5,0</ClientRectangle>
          <Components isList="true" count="0" />
          <Conditions isList="true" count="0" />
          <Guid>651739b2c3d840d4b6b05fcfedb47a12</Guid>
          <Name>ReportTitleBand2</Name>
          <Page isRef="107" />
          <Parent isRef="107" />
        </ReportTitleBand2>
        <GroupHeaderBand2 Ref="113" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,1.2,10.5,5.1</ClientRectangle>
          <Components isList="true" count="18">
            <Text21 Ref="114" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.8,10.5,0.79</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,14.25,Bold,Point,False,134</Font>
              <Guid>95f93e83c83c45b6894908ceefcd51e7</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text21</Name>
              <Page isRef="107" />
              <Parent isRef="113" />
              <Text>{标题}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text21>
            <Text32 Ref="115" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.7,1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>8770bc12456644e1a668c505953ed948</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text32</Name>
              <Page isRef="107" />
              <Parent isRef="113" />
              <Text>科别:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text32>
            <Text33 Ref="116" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>3.6,1.7,6.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>791876b87c5240d48a35613faa732491</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text33</Name>
              <Page isRef="107" />
              <Parent isRef="113" />
              <Text>打印时间：{打印时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text33>
            <Text34 Ref="117" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1,1.7,2.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>50d86fc14d604dccbe505bd9c0337d3f</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text34</Name>
              <Page isRef="107" />
              <Parent isRef="113" />
              <Text>{科室}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text34>
            <Text36 Ref="118" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,3,1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>9d410e9f0e7d404ba869f9f25e643bf6</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text36</Name>
              <Page isRef="107" />
              <Parent isRef="113" />
              <Text>姓名</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text36>
            <Text37 Ref="119" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1,3,2.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>1109cc68a3014372adb297525a1fb173</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text37</Name>
              <Page isRef="107" />
              <Parent isRef="113" />
              <Text>{患者姓名}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text37>
            <Text38 Ref="120" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.7,3,1.1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>d4557998c808400cae6f897e974e5b14</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text38</Name>
              <Page isRef="107" />
              <Parent isRef="113" />
              <Text>年龄</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text38>
            <Text39 Ref="121" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.8,3,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>85a3317c2bfa47ef995f264ae309e700</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text39</Name>
              <Page isRef="107" />
              <Parent isRef="113" />
              <Text>{年龄}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text39>
            <Text40 Ref="122" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6.6,3,1.3,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>1f62973817e84f71995ae682ccd78998</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text40</Name>
              <Page isRef="107" />
              <Parent isRef="113" />
              <Text>性别</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text40>
            <Text41 Ref="123" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.9,3,2.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>119e475f6a6a487ab8247c2488d07593</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text41</Name>
              <Page isRef="107" />
              <Parent isRef="113" />
              <Text>{患者性别}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text41>
            <Text42 Ref="124" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,4.2,10.5,0.9</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Times New Roman,21.75,Bold</Font>
              <Guid>a459a933add3433db0324918692655a6</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text42</Name>
              <Page isRef="107" />
              <Parent isRef="113" />
              <Text>R:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text42>
            <Text43 Ref="125" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>3.5,2.3,7,0.65</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>fa502eadea70407ca7f27a9add0e0771</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text43</Name>
              <Page isRef="107" />
              <Parent isRef="113" />
              <Text>NO.{门诊编号}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text43>
            <Text44 Ref="126" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,2.3,1,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>5e827608bbfe4af498e8c6f192fa0f27</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text44</Name>
              <Page isRef="107" />
              <Parent isRef="113" />
              <Text>费别:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text44>
            <Text45 Ref="127" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1,2.3,2.5,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>4f39ecf2665149e983e9f7fcb5025dd0</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text45</Name>
              <Page isRef="107" />
              <Parent isRef="113" />
              <Text>{类别}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text45>
            <Text46 Ref="128" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,3.6,1.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>b10de04f277d44fb8bd40f265492f4c2</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text46</Name>
              <Page isRef="107" />
              <Parent isRef="113" />
              <Text>临床诊断</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text46>
            <Text47 Ref="129" type="Text" isKey="true">
              <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.7,3.6,8.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>7a522a347337493790fb042be16aee32</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text47</Name>
              <Page isRef="107" />
              <Parent isRef="113" />
              <Text>{临床诊断}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text47>
            <Text10 Ref="130" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>9.5,0.1,1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,10.5,Regular,Point,False,0</Font>
              <Guid>b60596fb008a4e7ea161ecc0583d5f9b</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text10</Name>
              <Page isRef="107" />
              <Parent isRef="113" />
              <Text>{处方类别3}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Bottom</VertAlignment>
            </Text10>
            <Shape2 Ref="131" type="Shape" isKey="true">
              <BorderColor>Black</BorderColor>
              <Brush>Transparent</Brush>
              <ClientRectangle>9.5,0,1,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Guid>797ca0b7f6f14ceda1a5dbfe9064c0f6</Guid>
              <Name>Shape2</Name>
              <Page isRef="107" />
              <Parent isRef="113" />
              <ShapeType Ref="132" type="Stimulsoft.Report.Components.ShapeTypes.StiOvalShapeType" isKey="true" />
            </Shape2>
          </Components>
          <Condition>{中药处方.Mx_Code}</Condition>
          <Conditions isList="true" count="0" />
          <Guid>6d9f37df644f48b184ee36466acca9a3</Guid>
          <Name>GroupHeaderBand2</Name>
          <Page isRef="107" />
          <Parent isRef="107" />
        </GroupHeaderBand2>
        <DataBand2 Ref="133" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,7.1,10.5,0.5</ClientRectangle>
          <Columns>2</Columns>
          <Components isList="true" count="3">
            <Text52 Ref="134" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.01,2.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>335d237023c640f48874584ffcb3882b</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text52</Name>
              <Page isRef="107" />
              <Parent isRef="133" />
              <Text>      {中药处方.Yp_Name}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text52>
            <Text54 Ref="135" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.7,0.01,1,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>67eae76af2ca4747948d105808e0a1f9</Guid>
              <HideZeros>True</HideZeros>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text54</Name>
              <NullValue> </NullValue>
              <Page isRef="107" />
              <Parent isRef="133" />
              <Text>{中药处方.Mz_Sl}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="136" type="CustomFormat" isKey="true">
                <StringFormat>0.####</StringFormat>
              </TextFormat>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text54>
            <Text55 Ref="137" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>3.7,0.01,1.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>66bfc1af0a874e828c35b08e61114287</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text55</Name>
              <Page isRef="107" />
              <Parent isRef="133" />
              <Text>{中药处方.Mx_XsDw}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text55>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>中药处方</DataSourceName>
          <Filters isList="true" count="1">
            <value>,NotEqualTo,,,String</value>
          </Filters>
          <Guid>c6a9e6061c3e4378892221439d6e2dcf</Guid>
          <Name>DataBand2</Name>
          <Page isRef="107" />
          <Parent isRef="107" />
          <Sort isList="true" count="2">
            <value>ASC</value>
            <value>Mz_Id</value>
          </Sort>
        </DataBand2>
        <GroupFooterBand2 Ref="138" type="GroupFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,8.4,10.5,3.5</ClientRectangle>
          <Components isList="true" count="20">
            <Text48 Ref="139" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.3,0.6,2.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>d76231681d59495ea5be85aca385e2de</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text48</Name>
              <Page isRef="107" />
              <Parent isRef="138" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text48>
            <Text49 Ref="140" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>6,0.6,0.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>4b56ccee23954af1a70f88c028e11ce3</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text49</Name>
              <Page isRef="107" />
              <Parent isRef="138" />
              <Text>审核</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text49>
            <Text50 Ref="141" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0.1,0.6,0.9,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>f84fe3a423e7473c95fc7049fa832882</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text50</Name>
              <Page isRef="107" />
              <Parent isRef="138" />
              <Text>医师</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text50>
            <Text51 Ref="142" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>6,1.8,0.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>28c6d857dcdc479987980246c5d2f027</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text51</Name>
              <Page isRef="107" />
              <Parent isRef="138" />
              <Text>调配</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text51>
            <Text57 Ref="143" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.3,1.8,2.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>7a7f513696a14bbeb15453f367b8457f</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text57</Name>
              <Page isRef="107" />
              <Parent isRef="138" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text57>
            <Image3 Ref="144" type="Image" isKey="true">
              <AspectRatio>True</AspectRatio>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.2,0.1,2.6,1.1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Guid>3a1cfaf9905c431884588d840824cdd7</Guid>
              <HorAlignment>Center</HorAlignment>
              <ImageData>{医生签名}</ImageData>
              <Name>Image3</Name>
              <Page isRef="107" />
              <Parent isRef="138" />
              <Stretch>True</Stretch>
              <VertAlignment>Center</VertAlignment>
            </Image3>
            <Text60 Ref="145" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.2,1.1,2.6,0.1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>37a94e16933742d3919df13f17f9af44</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text60</Name>
              <Page isRef="107" />
              <Parent isRef="138" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text60>
            <Text61 Ref="146" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0.1,1.8,0.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>8c66e4d9b9f749b8a722c8d16fc366b0</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text61</Name>
              <Page isRef="107" />
              <Parent isRef="138" />
              <Text>核对</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text61>
            <Text63 Ref="147" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.2,1.8,2.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>8549c24697024e82b324ea2717aba95b</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text63</Name>
              <Page isRef="107" />
              <Parent isRef="138" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text63>
            <Text64 Ref="148" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.7,2.9,0.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>67149394969f43bfb62b0a343818984e</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text64</Name>
              <Page isRef="107" />
              <Parent isRef="138" />
              <Text>发药</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text64>
            <Text65 Ref="149" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.8,2.9,2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>371939ff99b9424cb11023adf7ee6b1d</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text65</Name>
              <Page isRef="107" />
              <Parent isRef="138" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text65>
            <Image14 Ref="150" type="Image" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7.3,0.1,2.7,1.1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Guid>065dd397109f4d05aaff6deb8dc1c8b1</Guid>
              <ImageData>{药房医生}</ImageData>
              <Name>Image14</Name>
              <Page isRef="107" />
              <Parent isRef="138" />
            </Image14>
            <Image15 Ref="151" type="Image" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.2,1.3,2.6,1.1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Guid>9eb40f55fb8447939def70e9952b4f64</Guid>
              <ImageData>{药房医生}</ImageData>
              <Name>Image15</Name>
              <Page isRef="107" />
              <Parent isRef="138" />
            </Image15>
            <Image16 Ref="152" type="Image" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7.3,1.3,2.7,1.1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Guid>15eb0310d25f4622bd6932bec3218797</Guid>
              <ImageData>{药房医生}</ImageData>
              <Name>Image16</Name>
              <Page isRef="107" />
              <Parent isRef="138" />
            </Image16>
            <Image17 Ref="153" type="Image" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>3.8,2.4,2.6,1.1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Guid>12a83c12a8134be480e43f146e97a699</Guid>
              <ImageData>{药房医生}</ImageData>
              <Name>Image17</Name>
              <Page isRef="107" />
              <Parent isRef="138" />
            </Image17>
            <Text174 Ref="154" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.2,0.5,2.7,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>5c014c26d7ae414c94bbad84d3203162</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text174</Name>
              <Page isRef="107" />
              <Parent isRef="138" />
              <Text>{医生签名字}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Bottom</VertAlignment>
            </Text174>
            <Text175 Ref="155" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7.3,0.5,2.7,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>32d8a879317d48e7832a3b33d8ae0533</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text175</Name>
              <Page isRef="107" />
              <Parent isRef="138" />
              <Text>{医生签名字}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Bottom</VertAlignment>
            </Text175>
            <Text176 Ref="156" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.2,1.7,2.7,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>2cfc622a66ab4ea8a22ce5c62d24a33d</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text176</Name>
              <Page isRef="107" />
              <Parent isRef="138" />
              <Text>{医生签名字}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Bottom</VertAlignment>
            </Text176>
            <Text177 Ref="157" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7.3,1.7,2.7,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>7654309db05a4e648452dbfc63b32edf</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text177</Name>
              <Page isRef="107" />
              <Parent isRef="138" />
              <Text>{医生签名字}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Bottom</VertAlignment>
            </Text177>
            <Text178 Ref="158" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>3.8,2.8,2.7,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>c4c980b1ae094551b60daf12d861a7d2</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text178</Name>
              <Page isRef="107" />
              <Parent isRef="138" />
              <Text>{医生签名字}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Bottom</VertAlignment>
            </Text178>
          </Components>
          <Conditions isList="true" count="0" />
          <Guid>153519eab4df4e80b99bba2aea4a7c81</Guid>
          <Name>GroupFooterBand2</Name>
          <Page isRef="107" />
          <Parent isRef="107" />
        </GroupFooterBand2>
      </Components>
      <Conditions isList="true" count="0" />
      <Enabled>False</Enabled>
      <Guid>d2c75988f3994cd0b281677fa37b6f98</Guid>
      <Margins>1,1,0.8,1</Margins>
      <Name>Page2</Name>
      <PageHeight>21</PageHeight>
      <PageWidth>12.5</PageWidth>
      <Report isRef="0" />
      <Watermark Ref="159" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page2>
    <Page3 Ref="160" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="4">
        <ReportTitleBand3 Ref="161" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,10.5,0</ClientRectangle>
          <Components isList="true" count="0" />
          <Conditions isList="true" count="0" />
          <Guid>651739b2c3d840d4b6b05fcfedb47a12</Guid>
          <Name>ReportTitleBand3</Name>
          <Page isRef="160" />
          <Parent isRef="160" />
        </ReportTitleBand3>
        <GroupHeaderBand3 Ref="162" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,1.2,10.5,5.1</ClientRectangle>
          <Components isList="true" count="16">
            <Text53 Ref="163" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.8,10.5,0.79</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,14.25,Bold,Point,False,134</Font>
              <Guid>382581c7180c4c5086bc22ce4a7c6d1e</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text53</Name>
              <Page isRef="160" />
              <Parent isRef="162" />
              <Text>{标题}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text53>
            <Text56 Ref="164" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.7,1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>63f8b0a39f5f4381a420376ec8bcbfe4</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text56</Name>
              <Page isRef="160" />
              <Parent isRef="162" />
              <Text>科别:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text56>
            <Text66 Ref="165" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>3.6,1.7,6.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>66af96eaf0dd4a21b8273266946c2e28</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text66</Name>
              <Page isRef="160" />
              <Parent isRef="162" />
              <Text>打印时间：{打印时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text66>
            <Text67 Ref="166" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1,1.7,2.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>b0f460b9cf164a23a183a670aeb34b60</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text67</Name>
              <Page isRef="160" />
              <Parent isRef="162" />
              <Text>{科室}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text67>
            <Text71 Ref="167" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,3,1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>177c07b46968459a9cdaf638f8967c69</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text71</Name>
              <Page isRef="160" />
              <Parent isRef="162" />
              <Text>姓名</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text71>
            <Text72 Ref="168" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1,3,2.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>5f9f28870a0b4e018a3de0667d55f321</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text72</Name>
              <Page isRef="160" />
              <Parent isRef="162" />
              <Text>{患者姓名}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text72>
            <Text73 Ref="169" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.7,3,1.1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>482c107efd034ec0b8b3095f4c8e97a8</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text73</Name>
              <Page isRef="160" />
              <Parent isRef="162" />
              <Text>年龄</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text73>
            <Text74 Ref="170" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.8,3,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>b4ffd9a0ff3c4f8785e991260ab51bd9</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text74</Name>
              <Page isRef="160" />
              <Parent isRef="162" />
              <Text>{年龄}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text74>
            <Text75 Ref="171" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6.6,3,1.3,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>cbdd9545bd7b4ba4a47ee9e86cc24fc9</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text75</Name>
              <Page isRef="160" />
              <Parent isRef="162" />
              <Text>性别</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text75>
            <Text76 Ref="172" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.9,3,2.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>a33836cc753040e0924b89ad450c74e1</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text76</Name>
              <Page isRef="160" />
              <Parent isRef="162" />
              <Text>{患者性别}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text76>
            <Text77 Ref="173" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,4.2,10.5,0.9</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Times New Roman,21.75,Bold</Font>
              <Guid>8d556e4ee13a4696b63b229a9fc100d8</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text77</Name>
              <Page isRef="160" />
              <Parent isRef="162" />
              <Text>R:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text77>
            <Text78 Ref="174" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>3.5,2.3,7,0.65</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>c4f87b0460b84399b68c50c218e23a3d</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text78</Name>
              <Page isRef="160" />
              <Parent isRef="162" />
              <Text>NO.{门诊编号}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text78>
            <Text79 Ref="175" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,2.3,1,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>f07cbb764f4a4952b1c9c698a516508b</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text79</Name>
              <Page isRef="160" />
              <Parent isRef="162" />
              <Text>费别:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text79>
            <Text80 Ref="176" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1,2.3,2.5,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>72ca545a99c848fdb7662409c3ed6c23</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text80</Name>
              <Page isRef="160" />
              <Parent isRef="162" />
              <Text>{类别}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text80>
            <Text81 Ref="177" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,3.6,1.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>8eec2a2e13b04703a99a3de98ff04585</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text81</Name>
              <Page isRef="160" />
              <Parent isRef="162" />
              <Text>临床诊断</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text81>
            <Text82 Ref="178" type="Text" isKey="true">
              <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.7,3.6,8.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>f74fe686272241deb84715e36df8f029</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text82</Name>
              <Page isRef="160" />
              <Parent isRef="162" />
              <Text>{临床诊断}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text82>
          </Components>
          <Condition>{卫材处方.Mx_Code}</Condition>
          <Conditions isList="true" count="0" />
          <Guid>54bd79b00e924262a597d2af4a5a3e87</Guid>
          <Name>GroupHeaderBand3</Name>
          <Page isRef="160" />
          <Parent isRef="160" />
        </GroupHeaderBand3>
        <DataBand3 Ref="179" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,7.1,10.5,0.5</ClientRectangle>
          <Columns>1</Columns>
          <Components isList="true" count="4">
            <Text85 Ref="180" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.01,6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>335d237023c640f48874584ffcb3882b</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text85</Name>
              <Page isRef="160" />
              <Parent isRef="179" />
              <Text>      {卫材处方.Yp_Name}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text85>
            <Text86 Ref="181" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>8.3,0.01,0.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>67eae76af2ca4747948d105808e0a1f9</Guid>
              <HideZeros>True</HideZeros>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text86</Name>
              <NullValue> </NullValue>
              <Page isRef="160" />
              <Parent isRef="179" />
              <Text>{卫材处方.Mz_Sl}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="182" type="CustomFormat" isKey="true">
                <StringFormat>0.####</StringFormat>
              </TextFormat>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text86>
            <Text87 Ref="183" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>9,0.01,1.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>66bfc1af0a874e828c35b08e61114287</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text87</Name>
              <Page isRef="160" />
              <Parent isRef="179" />
              <Text>{卫材处方.Mx_XsDw}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text87>
            <Text135 Ref="184" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>6,0,2.3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>b26c9c0296cd47e797c22a8b5b910962</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text135</Name>
              <NullValue> </NullValue>
              <Page isRef="160" />
              <Parent isRef="179" />
              <Text>{卫材处方.Mx_Gg}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="185" type="CustomFormat" isKey="true">
                <StringFormat>0.####</StringFormat>
              </TextFormat>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text135>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>卫材处方</DataSourceName>
          <Filters isList="true" count="1">
            <value>,NotEqualTo,,,String</value>
          </Filters>
          <Guid>c6a9e6061c3e4378892221439d6e2dcf</Guid>
          <Name>DataBand3</Name>
          <Page isRef="160" />
          <Parent isRef="160" />
          <Sort isList="true" count="2">
            <value>ASC</value>
            <value>Mz_Id</value>
          </Sort>
        </DataBand3>
        <GroupFooterBand3 Ref="186" type="GroupFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,8.4,10.5,3.5</ClientRectangle>
          <Components isList="true" count="20">
            <Text69 Ref="187" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.3,0.6,2.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>32217e0a427a4370b9c9a54b59ed5008</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text69</Name>
              <Page isRef="160" />
              <Parent isRef="186" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text69>
            <Text70 Ref="188" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>6,0.6,0.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>f7f2c785261a4f499558e7fbdff121fa</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text70</Name>
              <Page isRef="160" />
              <Parent isRef="186" />
              <Text>审核</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text70>
            <Text84 Ref="189" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0.1,0.6,0.9,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>3539e9923c6c47c1a8f387c5de40998e</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text84</Name>
              <Page isRef="160" />
              <Parent isRef="186" />
              <Text>医师</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text84>
            <Text88 Ref="190" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>6,1.8,0.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>36da76eb945b4bbaa517b8c1f2cf53f6</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text88</Name>
              <Page isRef="160" />
              <Parent isRef="186" />
              <Text>调配</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text88>
            <Text89 Ref="191" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.3,1.8,2.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>0b7032b89a134853b6366f5ccf233511</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text89</Name>
              <Page isRef="160" />
              <Parent isRef="186" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text89>
            <Image4 Ref="192" type="Image" isKey="true">
              <AspectRatio>True</AspectRatio>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.2,0.1,2.6,1.1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Guid>06b9e1e6841f4beaa8a7842854fddaa1</Guid>
              <HorAlignment>Center</HorAlignment>
              <ImageData>{医生签名}</ImageData>
              <Name>Image4</Name>
              <Page isRef="160" />
              <Parent isRef="186" />
              <Stretch>True</Stretch>
              <VertAlignment>Center</VertAlignment>
            </Image4>
            <Text90 Ref="193" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.2,1.1,2.6,0.1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>01bc6b3f92fd4de59f2f4cbf680451cb</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text90</Name>
              <Page isRef="160" />
              <Parent isRef="186" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text90>
            <Text91 Ref="194" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0.1,1.8,0.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>a4e5d7b01edd48a68a853c24ec2c0376</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text91</Name>
              <Page isRef="160" />
              <Parent isRef="186" />
              <Text>核对</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text91>
            <Text92 Ref="195" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.2,1.8,2.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>43f86a1fb7a14a20a3511328189732af</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text92</Name>
              <Page isRef="160" />
              <Parent isRef="186" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text92>
            <Text93 Ref="196" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.7,2.9,0.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>dcd50a4497a240098b640d3d80254f18</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text93</Name>
              <Page isRef="160" />
              <Parent isRef="186" />
              <Text>发药</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text93>
            <Text94 Ref="197" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.8,2.9,2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>1071155d1cf741d59b74dd3bf2a5917c</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text94</Name>
              <Page isRef="160" />
              <Parent isRef="186" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text94>
            <Image18 Ref="198" type="Image" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7.3,0.1,2.7,1.1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Guid>46783d9caf6948479c3f31cf5ad9a5fc</Guid>
              <ImageData>{药房医生}</ImageData>
              <Name>Image18</Name>
              <Page isRef="160" />
              <Parent isRef="186" />
            </Image18>
            <Image19 Ref="199" type="Image" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.2,1.3,2.6,1.1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Guid>2af4996f34934bfc9c2f619a26e8cd3f</Guid>
              <ImageData>{药房医生}</ImageData>
              <Name>Image19</Name>
              <Page isRef="160" />
              <Parent isRef="186" />
            </Image19>
            <Image20 Ref="200" type="Image" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7.3,1.3,2.7,1.1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Guid>8934cfc75be9402d89cd9b07e6babe62</Guid>
              <ImageData>{药房医生}</ImageData>
              <Name>Image20</Name>
              <Page isRef="160" />
              <Parent isRef="186" />
            </Image20>
            <Image21 Ref="201" type="Image" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>3.8,2.4,2.6,1.1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Guid>9a1a8d3b9d454c69a9872a5e66d173a4</Guid>
              <ImageData>{药房医生}</ImageData>
              <Name>Image21</Name>
              <Page isRef="160" />
              <Parent isRef="186" />
            </Image21>
            <Text179 Ref="202" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.2,0.5,2.7,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>a09bbc6460644dff86adacaf9e93dc7e</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text179</Name>
              <Page isRef="160" />
              <Parent isRef="186" />
              <Text>{医生签名字}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Bottom</VertAlignment>
            </Text179>
            <Text180 Ref="203" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7.3,0.5,2.7,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>2aa01a5aadaa4d42aaf22fe9fb79d41a</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text180</Name>
              <Page isRef="160" />
              <Parent isRef="186" />
              <Text>{医生签名字}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Bottom</VertAlignment>
            </Text180>
            <Text181 Ref="204" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.2,1.7,2.7,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>2a185750d471489fa3a9cd8ce0fcaa1e</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text181</Name>
              <Page isRef="160" />
              <Parent isRef="186" />
              <Text>{医生签名字}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Bottom</VertAlignment>
            </Text181>
            <Text182 Ref="205" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7.3,1.7,2.7,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>2dc209d8748349abb28d2fdb679377e3</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text182</Name>
              <Page isRef="160" />
              <Parent isRef="186" />
              <Text>{医生签名字}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Bottom</VertAlignment>
            </Text182>
            <Text183 Ref="206" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>3.8,2.8,2.7,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>fa3d4bc149984db89d26990af98e5b1f</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text183</Name>
              <Page isRef="160" />
              <Parent isRef="186" />
              <Text>{医生签名字}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Bottom</VertAlignment>
            </Text183>
          </Components>
          <Conditions isList="true" count="0" />
          <Guid>3f03d9359f9543aaaaf973855d84a1b9</Guid>
          <Name>GroupFooterBand3</Name>
          <Page isRef="160" />
          <Parent isRef="160" />
        </GroupFooterBand3>
      </Components>
      <Conditions isList="true" count="0" />
      <Enabled>False</Enabled>
      <Guid>c8958f96882344c9bb48cab985a8c3c4</Guid>
      <Margins>1,1,0.8,1</Margins>
      <Name>Page3</Name>
      <PageHeight>21</PageHeight>
      <PageWidth>12.5</PageWidth>
      <Report isRef="0" />
      <Watermark Ref="207" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page3>
    <Page4 Ref="208" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="8">
        <Text99 Ref="209" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>12.3,10,1,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>宋体,10.5</Font>
          <Guid>3c065e6258f1422c813901568569d680</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text99</Name>
          <Page isRef="208" />
          <Parent isRef="208" />
          <Text>金额</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Bottom</VertAlignment>
        </Text99>
        <Text100 Ref="210" type="Text" isKey="true">
          <Border>Bottom;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>13.3,10,2.8,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>宋体,10.5,Regular,Point,False,134</Font>
          <Guid>5f24f0331f14448d8390823fea33ef17</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text100</Name>
          <Page isRef="208" />
          <Parent isRef="208" />
          <Text>{Sum(GroupHeaderBand4,诊疗处方.Mz_Money)}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Totals</Type>
          <VertAlignment>Bottom</VertAlignment>
        </Text100>
        <Text122 Ref="211" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>13.3,9,0.9,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>宋体,10.5</Font>
          <Guid>6bb7fe492e7b4b4d84a9f1669359f10f</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text122</Name>
          <Page isRef="208" />
          <Parent isRef="208" />
          <Text>发药</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Bottom</VertAlignment>
        </Text122>
        <Text128 Ref="212" type="Text" isKey="true">
          <Border>Bottom;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>14.5,9,2,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>宋体,10.5,Regular,Point,False,134</Font>
          <Guid>8588a3ac209a491590a3e7e60bcf1799</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text128</Name>
          <Page isRef="208" />
          <Parent isRef="208" />
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Bottom</VertAlignment>
        </Text128>
        <ReportTitleBand4 Ref="213" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,10.5,0</ClientRectangle>
          <Components isList="true" count="0" />
          <Conditions isList="true" count="0" />
          <Guid>651739b2c3d840d4b6b05fcfedb47a12</Guid>
          <Name>ReportTitleBand4</Name>
          <Page isRef="208" />
          <Parent isRef="208" />
        </ReportTitleBand4>
        <GroupHeaderBand4 Ref="214" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,1.2,10.5,5.1</ClientRectangle>
          <Components isList="true" count="16">
            <Text95 Ref="215" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.8,10.5,0.79</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,14.25,Bold,Point,False,134</Font>
              <Guid>9c14493c4d4845fba7e36e6550feed9e</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text95</Name>
              <Page isRef="208" />
              <Parent isRef="214" />
              <Text>{标题}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text95>
            <Text96 Ref="216" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.7,1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>02fa0057611b465ca48c8ef74c7e2f2e</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text96</Name>
              <Page isRef="208" />
              <Parent isRef="214" />
              <Text>科别:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text96>
            <Text97 Ref="217" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>3.6,1.7,6.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>a0d3cb698e454b6c8c4594ece81c84aa</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text97</Name>
              <Page isRef="208" />
              <Parent isRef="214" />
              <Text>打印时间：{打印时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text97>
            <Text102 Ref="218" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1,1.7,2.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>a699e866cc9a47b18b79c89def3b5525</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text102</Name>
              <Page isRef="208" />
              <Parent isRef="214" />
              <Text>{科室}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text102>
            <Text103 Ref="219" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,3,1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>370ba0171c064f2e986620a179602dbb</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text103</Name>
              <Page isRef="208" />
              <Parent isRef="214" />
              <Text>姓名</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text103>
            <Text104 Ref="220" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1,3,2.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>9ea7acbea8824bba905b0a99d1160aa5</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text104</Name>
              <Page isRef="208" />
              <Parent isRef="214" />
              <Text>{患者姓名}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text104>
            <Text105 Ref="221" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.7,3,1.1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>0b305d1aa0694140b733cfb2277bc092</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text105</Name>
              <Page isRef="208" />
              <Parent isRef="214" />
              <Text>年龄</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text105>
            <Text106 Ref="222" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.8,3,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>c8e38472252f43df8f4924ed6a43d854</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text106</Name>
              <Page isRef="208" />
              <Parent isRef="214" />
              <Text>{年龄}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text106>
            <Text107 Ref="223" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6.6,3,1.3,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>7fedb39bbd93448290ff2af272c1571a</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text107</Name>
              <Page isRef="208" />
              <Parent isRef="214" />
              <Text>性别</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text107>
            <Text108 Ref="224" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.9,3,2.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>ef148b701ed841959bf238e7d75fbca5</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text108</Name>
              <Page isRef="208" />
              <Parent isRef="214" />
              <Text>{患者性别}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text108>
            <Text109 Ref="225" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,4.2,10.5,0.9</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Times New Roman,21.75,Bold</Font>
              <Guid>1f84867ee6ef43129b9139ec3540fe23</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text109</Name>
              <Page isRef="208" />
              <Parent isRef="214" />
              <Text>R:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text109>
            <Text110 Ref="226" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>3.5,2.3,7,0.65</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>d441808cef694f1cbf6f954d74a7c2c7</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text110</Name>
              <Page isRef="208" />
              <Parent isRef="214" />
              <Text>NO.{门诊编号}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text110>
            <Text111 Ref="227" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,2.3,1,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>02d6e4ecdf844ff5971094d3821c348a</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text111</Name>
              <Page isRef="208" />
              <Parent isRef="214" />
              <Text>费别:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text111>
            <Text112 Ref="228" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1,2.3,2.5,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>b66e17cb312c498ea0596e71dafd7fa1</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text112</Name>
              <Page isRef="208" />
              <Parent isRef="214" />
              <Text>{类别}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text112>
            <Text113 Ref="229" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,3.6,1.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>8391822690e7497ebabe7957a16e1eb2</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text113</Name>
              <Page isRef="208" />
              <Parent isRef="214" />
              <Text>临床诊断</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text113>
            <Text114 Ref="230" type="Text" isKey="true">
              <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.7,3.6,8.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>ac049b166f7c42c3ba1229d2ee7b8f6f</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text114</Name>
              <Page isRef="208" />
              <Parent isRef="214" />
              <Text>{临床诊断}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text114>
          </Components>
          <Condition>{诊疗处方.Mx_Code}</Condition>
          <Conditions isList="true" count="0" />
          <Guid>c009cab14cb144a292559a2e454379c0</Guid>
          <Name>GroupHeaderBand4</Name>
          <Page isRef="208" />
          <Parent isRef="208" />
        </GroupHeaderBand4>
        <DataBand4 Ref="231" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,7.1,10.5,0.5</ClientRectangle>
          <Columns>1</Columns>
          <Components isList="true" count="3">
            <Text118 Ref="232" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.01,8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>335d237023c640f48874584ffcb3882b</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text118</Name>
              <Page isRef="208" />
              <Parent isRef="231" />
              <Text>      {诊疗处方.Yp_Name}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text118>
            <Text119 Ref="233" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>8,0.01,1,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>67eae76af2ca4747948d105808e0a1f9</Guid>
              <HideZeros>True</HideZeros>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text119</Name>
              <NullValue> </NullValue>
              <Page isRef="208" />
              <Parent isRef="231" />
              <Text>{诊疗处方.Mz_Sl}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="234" type="CustomFormat" isKey="true">
                <StringFormat>0.####</StringFormat>
              </TextFormat>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text119>
            <Text120 Ref="235" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>9,0.01,1.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>66bfc1af0a874e828c35b08e61114287</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text120</Name>
              <Page isRef="208" />
              <Parent isRef="231" />
              <Text>{诊疗处方.Mx_XsDw}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text120>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>诊疗处方</DataSourceName>
          <Filters isList="true" count="1">
            <value>,NotEqualTo,,,String</value>
          </Filters>
          <Guid>c6a9e6061c3e4378892221439d6e2dcf</Guid>
          <Name>DataBand4</Name>
          <Page isRef="208" />
          <Parent isRef="208" />
          <Sort isList="true" count="2">
            <value>ASC</value>
            <value>Mz_Id</value>
          </Sort>
        </DataBand4>
        <GroupFooterBand4 Ref="236" type="GroupFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,8.4,10.5,3.5</ClientRectangle>
          <Components isList="true" count="20">
            <Text83 Ref="237" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.3,0.6,2.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>8c0176777c7046e0a7e79079a459aec9</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text83</Name>
              <Page isRef="208" />
              <Parent isRef="236" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text83>
            <Text98 Ref="238" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>6,0.6,0.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>c7ef050f24b24960a0cf69d8718c5d43</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text98</Name>
              <Page isRef="208" />
              <Parent isRef="236" />
              <Text>审核</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text98>
            <Text101 Ref="239" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0.1,0.6,0.9,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>9e47e487853343bfa6570a2d61f6b2a2</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text101</Name>
              <Page isRef="208" />
              <Parent isRef="236" />
              <Text>医师</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text101>
            <Text115 Ref="240" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>6,1.8,0.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>3acc2f543c7c434aa0ebf44be5f9e990</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text115</Name>
              <Page isRef="208" />
              <Parent isRef="236" />
              <Text>调配</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text115>
            <Text116 Ref="241" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.3,1.8,2.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>13ce983c89c9415f995e8691bee29f6f</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text116</Name>
              <Page isRef="208" />
              <Parent isRef="236" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text116>
            <Image5 Ref="242" type="Image" isKey="true">
              <AspectRatio>True</AspectRatio>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.2,0.1,2.6,1.1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Guid>84e32ccd242542918e33a4254d534f9d</Guid>
              <HorAlignment>Center</HorAlignment>
              <ImageData>{医生签名}</ImageData>
              <Name>Image5</Name>
              <Page isRef="208" />
              <Parent isRef="236" />
              <Stretch>True</Stretch>
              <VertAlignment>Center</VertAlignment>
            </Image5>
            <Text117 Ref="243" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.2,1.1,2.6,0.1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>e63da7a216c24edfaf3832e5ea812375</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text117</Name>
              <Page isRef="208" />
              <Parent isRef="236" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text117>
            <Text121 Ref="244" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0.1,1.8,0.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>659fd624912f4b63a28df24ae9301437</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text121</Name>
              <Page isRef="208" />
              <Parent isRef="236" />
              <Text>核对</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text121>
            <Text123 Ref="245" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.2,1.8,2.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>d8ffe7ac6b0e472fb52b769502e8b0e0</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text123</Name>
              <Page isRef="208" />
              <Parent isRef="236" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text123>
            <Text124 Ref="246" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.7,2.9,0.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Guid>de2cd9051f064b188863952325d5fe15</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text124</Name>
              <Page isRef="208" />
              <Parent isRef="236" />
              <Text>发药</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text124>
            <Text125 Ref="247" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.8,2.9,2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>7eede4daeab747f1aed200e4aeefdfad</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text125</Name>
              <Page isRef="208" />
              <Parent isRef="236" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text125>
            <Image22 Ref="248" type="Image" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7.3,0.1,2.7,1.1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Guid>db0d8f7157224c31bcf0cfebf7ad9862</Guid>
              <ImageData>{药房医生}</ImageData>
              <Name>Image22</Name>
              <Page isRef="208" />
              <Parent isRef="236" />
            </Image22>
            <Image23 Ref="249" type="Image" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.2,1.3,2.6,1.1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Guid>42d4115d5d44400d9665c10cfa2fd4e2</Guid>
              <ImageData>{药房医生}</ImageData>
              <Name>Image23</Name>
              <Page isRef="208" />
              <Parent isRef="236" />
            </Image23>
            <Image24 Ref="250" type="Image" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7.3,1.3,2.7,1.1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Guid>9e87c4b47a0f433c90a3a5010923a801</Guid>
              <ImageData>{药房医生}</ImageData>
              <Name>Image24</Name>
              <Page isRef="208" />
              <Parent isRef="236" />
            </Image24>
            <Image25 Ref="251" type="Image" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>3.8,2.4,2.6,1.1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Guid>deac6ccbd857434f8f1f9d3eb413cc10</Guid>
              <ImageData>{药房医生}</ImageData>
              <Name>Image25</Name>
              <Page isRef="208" />
              <Parent isRef="236" />
            </Image25>
            <Text184 Ref="252" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.2,0.5,2.7,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>c97176f100214ff2a8dff359f566fde9</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text184</Name>
              <Page isRef="208" />
              <Parent isRef="236" />
              <Text>{医生签名字}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Bottom</VertAlignment>
            </Text184>
            <Text185 Ref="253" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7.3,0.5,2.7,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>8ab2fdcc3fbd46fd968941f5697ec0e9</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text185</Name>
              <Page isRef="208" />
              <Parent isRef="236" />
              <Text>{医生签名字}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Bottom</VertAlignment>
            </Text185>
            <Text186 Ref="254" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.2,1.7,2.7,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>1ffaa1a1912b4bf687d414d3daadb061</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text186</Name>
              <Page isRef="208" />
              <Parent isRef="236" />
              <Text>{医生签名字}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Bottom</VertAlignment>
            </Text186>
            <Text187 Ref="255" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7.3,1.7,2.7,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>b9b4656b45f34a9196430b03f27d5952</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text187</Name>
              <Page isRef="208" />
              <Parent isRef="236" />
              <Text>{医生签名字}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Bottom</VertAlignment>
            </Text187>
            <Text188 Ref="256" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>3.8,2.8,2.7,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <Guid>456bae1cf8ab4fc094f940ec2b720ef0</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text188</Name>
              <Page isRef="208" />
              <Parent isRef="236" />
              <Text>{医生签名字}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Bottom</VertAlignment>
            </Text188>
          </Components>
          <Conditions isList="true" count="0" />
          <Guid>e97b1b20f9014b2c9683aae3ddb121ce</Guid>
          <Name>GroupFooterBand4</Name>
          <Page isRef="208" />
          <Parent isRef="208" />
        </GroupFooterBand4>
      </Components>
      <Conditions isList="true" count="0" />
      <Enabled>False</Enabled>
      <Guid>e65e92008fb04715ad4470c9d2d0e4c4</Guid>
      <Margins>1,1,0.8,1</Margins>
      <Name>Page4</Name>
      <PageHeight>21</PageHeight>
      <PageWidth>12.5</PageWidth>
      <Report isRef="0" />
      <Watermark Ref="257" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page4>
  </Pages>
  <PrinterSettings Ref="258" type="Stimulsoft.Report.Print.StiPrinterSettings" isKey="true" />
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>门诊处方表</ReportAlias>
  <ReportChanged>1/8/2019 11:12:48 AM</ReportChanged>
  <ReportCreated>12/7/2012 3:16:11 PM</ReportCreated>
  <ReportFile>E:\ZTHis5\ZTHisOutpatient\Rpt\门诊处方表(二分之一A4)new(竖版).mrt</ReportFile>
  <ReportGuid>33c25364ca7d4b178e38dad1b2a1b2cd</ReportGuid>
  <ReportName>门诊处方表</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2011.2.1026</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class Report : Stimulsoft.Report.StiReport
    {
        public Report()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
        public string 医生姓名;
        public string 经手人;
        public string 门诊编号;
        public string 科室;
        public string 打印时间;
        public string 患者姓名;
        public string 患者性别;
        public string 年龄;
        public string 类别;
        public string 疾病;
        public string 金额;
        public string 标题;
        public Stimulsoft.Report.Components.StiPage Page1;
        public Stimulsoft.Report.Components.StiReportTitleBand ReportTitleBand1;
        public Stimulsoft.Report.Components.StiGroupHeaderBand GroupHeaderBand1;
        public Stimulsoft.Report.Components.StiText Text1;
        public Stimulsoft.Report.Components.StiText Text2;
        public Stimulsoft.Report.Components.StiText Text3;
        public Stimulsoft.Report.Components.StiText Text4;
        public Stimulsoft.Report.Components.StiText Text6;
        public Stimulsoft.Report.Components.StiText Text5;
        public Stimulsoft.Report.Components.StiText Text7;
        public Stimulsoft.Report.Components.StiText Text8;
        public Stimulsoft.Report.Components.StiText Text9;
        public Stimulsoft.Report.Components.StiText Text12;
        public Stimulsoft.Report.Components.StiText Text13;
        public Stimulsoft.Report.Components.StiText Text14;
        public Stimulsoft.Report.Components.StiText Text15;
        public Stimulsoft.Report.Components.StiText Text16;
        public Stimulsoft.Report.Components.StiText Text11;
        public Stimulsoft.Report.Components.StiText Text17;
        public Stimulsoft.Report.Components.StiText Text18;
        public Stimulsoft.Report.Components.StiDataBand DataBand1;
        public Stimulsoft.Report.Components.StiText Text21;
        public Stimulsoft.Report.Components.StiText Text32;
        public Stimulsoft.Report.Components.StiText Text34;
        public Stimulsoft.Report.Components.StiText Text10;
        public Stimulsoft.Report.Components.StiText Text33;
        public Stimulsoft.Report.Components.StiGroupFooterBand GroupFooterBand1;
        public Stimulsoft.Report.Components.StiText Text20;
        public Stimulsoft.Report.Components.StiText Text23;
        public Stimulsoft.Report.Components.StiText Text25;
        public Stimulsoft.Report.Components.StiText Text27;
        public Stimulsoft.Report.Components.StiText Text30;
        public Stimulsoft.Report.Dictionary.StiSumDecimalFunctionService Text30_Sum;
        public Stimulsoft.Report.Components.StiText Text31;
        public Stimulsoft.Report.Components.StiText Text22;
        public Stimulsoft.Report.Components.StiText Text26;
        public Stimulsoft.Report.Components.StiText Text29;
        public Stimulsoft.Report.Components.StiText Text24;
        public Stimulsoft.Report.Components.StiText Text28;
        public Stimulsoft.Report.Components.StiText Text19;
        public Stimulsoft.Report.Components.StiWatermark Page1_Watermark;
        public Stimulsoft.Report.Print.StiPrinterSettings 门诊处方表_PrinterSettings;
        public 处方明细1DataSource 处方明细1;
        
        public void GroupHeaderBand1__GetValue(object sender, Stimulsoft.Report.Events.StiValueEventArgs e)
        {
            e.Value = 处方明细1.Mx_Code;
        }
        
        public void Text1__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "NO." + ToString(sender, 门诊编号, true);
        }
        
        public void Text3__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 标题, true);
        }
        
        public void Text4__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "科别";
        }
        
        public void Text6__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 打印时间, true);
        }
        
        public void Text5__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 科室, true);
        }
        
        public void Text7__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "费别";
        }
        
        public void Text8__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 类别, true);
        }
        
        public void Text9__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "姓名";
        }
        
        public void Text12__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 患者姓名, true);
        }
        
        public void Text13__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "年龄";
        }
        
        public void Text14__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 年龄, true);
        }
        
        public void Text15__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "性别";
        }
        
        public void Text16__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 患者性别, true);
        }
        
        public void Text11__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "临床诊断";
        }
        
        public void Text17__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 疾病, true);
        }
        
        public void Text18__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "RP:";
        }
        
        public void Text21__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 处方明细1.Yp_Name, true);
        }
        
        public void Text32__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 处方明细1.Mx_Gg, true);
        }
        
        public void Text34__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            if (StiNullValuesHelper.IsNull(this, "处方明细1.Mz_Sl"))
            {
                e.Value = " ";
            }
            else
            {
                e.Value = System.String.Format("{0:0.####}", 处方明细1.Mz_Sl);
            }
        }
        
        public void Text10__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 处方明细1.Mx_XsDw, true);
        }
        

			public void Text20__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
			{
			e.Value = ToString(sender, 医生姓名, true);
			}
       
			public void Text23__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
			{
			e.Value = "核对发药";
			}
       
			public void Text25__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
			{
			e.Value = ToString(sender, 经手人, true);
			}
       
			public void Text27__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
			{
			e.Value = "金额";
			}
       
			public void Text30__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
			{
			e.Value = "#%#{Sum(GroupHeaderBand1,处方明细1.Mz_Money)}";
			e.StoreToPrinted = true;
			}
       
			public System.String Text30_GetValue_End(Stimulsoft.Report.Components.StiComponent sender)
			{
			return ToString(sender, ((decimal)(StiReport.ChangeType(this.Text30_Sum.GetValue(), typeof(decimal), true))), true);
			}
       
			public void Text22__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
			{
			e.Value = "审计调配";
			}
       
			public void Text26__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
			{
			e.Value = "签字";
			}
       
			public void Text24__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
			{
			e.Value = ToString(sender, 经手人, true);
			}
       
			public void Text28__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
			{
			e.Value = "签字";
			}
       
			public void Text19__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
			{
			e.Value = "医师";
			}
       
			public void GroupHeaderBand1__BeginRender(object sender, System.EventArgs e)
			{
			this.Text30_Sum.Init();
			this.Text30.TextValue = "";
			}
       
			public void GroupHeaderBand1__EndRender(object sender, System.EventArgs e)
			{
			this.Text30.SetText(new Stimulsoft.Report.Components.StiGetValue(this.Text30_GetValue_End));
			}
       
			public void GroupHeaderBand1__Rendering(object sender, System.EventArgs e)
			{
			this.Text30_Sum.CalcItem(处方明细1.Mz_Money);
			}
       
			private void InitializeComponent()
			{
			this.处方明细1 = new 处方明细1DataSource();
			this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "医生姓名", "医生姓名", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
			this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "经手人", "经手人", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
			this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "门诊编号", "门诊编号", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
			this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "科室", "科室", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
			this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "打印时间", "打印时间", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
			this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "患者姓名", "患者姓名", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
			this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "患者性别", "患者性别", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
			this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "年龄", "年龄", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
			this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "类别", "类别", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
			this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "疾病", "疾病", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
			this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "金额", "金额", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
			this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "标题", "标题", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
			this.NeedsCompiling = false;
			this.Text30_Sum = new Stimulsoft.Report.Dictionary.StiSumDecimalFunctionService();
			// 
			// Variables init
			// 
			this.医生姓名 = "";
			this.经手人 = "";
			this.门诊编号 = "";
			this.科室 = "";
			this.打印时间 = "";
			this.患者姓名 = "";
			this.患者性别 = "";
			this.年龄 = "";
			this.类别 = "";
			this.疾病 = "";
			this.金额 = "";
			this.标题 = "";
			this.EngineVersion = Stimulsoft.Report.Engine.StiEngineVersion.EngineV2;
			this.ReferencedAssemblies = new System.String[] {
				"System.Dll",
				"System.Drawing.Dll",
				"System.Windows.Forms.Dll",
				"System.Data.Dll",
				"System.Xml.Dll",
				"Stimulsoft.Controls.Dll",
				"Stimulsoft.Base.Dll",
				"Stimulsoft.Report.Dll"};
			this.ReportAlias = "门诊处方表";
			// 
			// ReportChanged
			// 
			this.ReportChanged = new DateTime(2012, 12, 18, 17, 31, 36, 921);
			// 
			// ReportCreated
			// 
			this.ReportCreated = new DateTime(2012, 12, 7, 15, 16, 11, 0);
			this.ReportFile = ".\\Rpt\\门诊处方表.mrt";
			this.ReportGuid = "b5cdf9b297c2492da62d2c20a761515d";
			this.ReportName = "门诊处方表";
			this.ReportUnit = Stimulsoft.Report.StiReportUnitType.Centimeters;
			this.ReportVersion = "2011.2.1026";
			this.ScriptLanguage = Stimulsoft.Report.StiReportLanguageType.CSharp;
			// 
			// Page1
			// 
			this.Page1 = new Stimulsoft.Report.Components.StiPage();
			this.Page1.Guid = "e4e1e4db58f545af984c08e14b4348db";
			this.Page1.Name = "Page1";
			this.Page1.PageHeight = 20;
			this.Page1.PageWidth = 10;
			this.Page1.PaperSize = System.Drawing.Printing.PaperKind.A4;
			this.Page1.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 2, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Page1.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			// 
			// ReportTitleBand1
			// 
			this.ReportTitleBand1 = new Stimulsoft.Report.Components.StiReportTitleBand();
			this.ReportTitleBand1.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 0.4, 9.6, 0);
			this.ReportTitleBand1.Guid = "651739b2c3d840d4b6b05fcfedb47a12";
			this.ReportTitleBand1.Name = "ReportTitleBand1";
			this.ReportTitleBand1.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.ReportTitleBand1.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.ReportTitleBand1.Interaction = null;
			// 
			// GroupHeaderBand1
			// 
			this.GroupHeaderBand1 = new Stimulsoft.Report.Components.StiGroupHeaderBand();
			this.GroupHeaderBand1.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 1.2, 9.6, 4.8);
			this.GroupHeaderBand1.GetValue += new Stimulsoft.Report.Events.StiValueEventHandler(this.GroupHeaderBand1__GetValue);
			this.GroupHeaderBand1.Guid = "a749f17ac18945828b0409dc59e41855";
			this.GroupHeaderBand1.Name = "GroupHeaderBand1";
			this.GroupHeaderBand1.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.GroupHeaderBand1.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			// 
			// Text1
			// 
			this.Text1 = new Stimulsoft.Report.Components.StiText();
			this.Text1.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 0, 9.6, 0.45);
			this.Text1.Guid = "ae1056b6222f44548451a630475d9696";
			this.Text1.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
			this.Text1.Name = "Text1";
			this.Text1.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text1__GetValue);
			this.Text1.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text1.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
			this.Text1.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text1.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text1.Font = new System.Drawing.Font("宋体", 10.5F);
			this.Text1.Indicator = null;
			this.Text1.Interaction = null;
			this.Text1.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text1.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text1.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text2
			// 
			this.Text2 = new Stimulsoft.Report.Components.StiText();
			this.Text2.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 0.45, 9.6, 0.55);
			this.Text2.Guid = "e8cbb9d1b5d04f889d1347409f8607ff";
			this.Text2.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
			this.Text2.Name = "Text2";
			this.Text2.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text2.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
			this.Text2.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text2.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text2.Font = new System.Drawing.Font("宋体", 14.25F);
			this.Text2.Indicator = null;
			this.Text2.Interaction = null;
			this.Text2.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text2.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text2.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text3
			// 
			this.Text3 = new Stimulsoft.Report.Components.StiText();
			this.Text3.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 1, 9.6, 0.79);
			this.Text3.Guid = "373a84218f7f4d0e9cb98424a105bc39";
			this.Text3.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
			this.Text3.Name = "Text3";
			this.Text3.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text3__GetValue);
			this.Text3.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text3.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
			this.Text3.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text3.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text3.Font = new System.Drawing.Font("宋体", 18F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
			this.Text3.Indicator = null;
			this.Text3.Interaction = null;
			this.Text3.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text3.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text3.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text4
			// 
			this.Text4 = new Stimulsoft.Report.Components.StiText();
			this.Text4.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 1.8, 1, 0.6);
			this.Text4.Guid = "843e853605874d01b372d01f815ddfcf";
			this.Text4.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
			this.Text4.Name = "Text4";
			this.Text4.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text4__GetValue);
			this.Text4.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text4.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
			this.Text4.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text4.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text4.Font = new System.Drawing.Font("宋体", 10.5F);
			this.Text4.Indicator = null;
			this.Text4.Interaction = null;
			this.Text4.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text4.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text4.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text6
			// 
			this.Text6 = new Stimulsoft.Report.Components.StiText();
			this.Text6.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(3.8, 1.8, 3, 0.6);
			this.Text6.Guid = "2ce147c089e94901adfd9dcf1ea503c6";
			this.Text6.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
			this.Text6.Name = "Text6";
			this.Text6.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text6__GetValue);
			this.Text6.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text6.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
			this.Text6.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text6.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text6.Font = new System.Drawing.Font("宋体", 10.5F);
			this.Text6.Indicator = null;
			this.Text6.Interaction = null;
			this.Text6.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text6.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text6.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text5
			// 
			this.Text5 = new Stimulsoft.Report.Components.StiText();
			this.Text5.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(1, 1.8, 2.8, 0.6);
			this.Text5.Guid = "bc4302cb138a4599a13394731c5c6158";
			this.Text5.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
			this.Text5.Name = "Text5";
			this.Text5.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text5__GetValue);
			this.Text5.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text5.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
			this.Text5.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text5.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text5.Font = new System.Drawing.Font("宋体", 10.5F);
			this.Text5.Indicator = null;
			this.Text5.Interaction = null;
			this.Text5.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text5.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text5.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text7
			// 
			this.Text7 = new Stimulsoft.Report.Components.StiText();
			this.Text7.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(6.8, 1.8, 1, 0.6);
			this.Text7.Guid = "3256b6ca0058477eb8d09bd7fdb7011d";
			this.Text7.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
			this.Text7.Name = "Text7";
			this.Text7.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text7__GetValue);
			this.Text7.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text7.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
			this.Text7.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text7.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text7.Font = new System.Drawing.Font("宋体", 10.5F);
			this.Text7.Indicator = null;
			this.Text7.Interaction = null;
			this.Text7.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text7.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text7.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text8
			// 
			this.Text8 = new Stimulsoft.Report.Components.StiText();
			this.Text8.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(7.8, 1.8, 1.8, 0.6);
			this.Text8.Guid = "49d0be77f0dc404487d2193b361ac6c8";
			this.Text8.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
			this.Text8.Name = "Text8";
			this.Text8.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text8__GetValue);
			this.Text8.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text8.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
			this.Text8.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text8.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text8.Font = new System.Drawing.Font("宋体", 10.5F);
			this.Text8.Indicator = null;
			this.Text8.Interaction = null;
			this.Text8.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text8.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text8.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text9
			// 
			this.Text9 = new Stimulsoft.Report.Components.StiText();
			this.Text9.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 2.4, 1, 0.6);
			this.Text9.Guid = "e239b6efbf3b4b90b93997ba2912f223";
			this.Text9.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
			this.Text9.Name = "Text9";
			this.Text9.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text9__GetValue);
			this.Text9.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text9.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
			this.Text9.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.All, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text9.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text9.Font = new System.Drawing.Font("宋体", 10.5F);
			this.Text9.Indicator = null;
			this.Text9.Interaction = null;
			this.Text9.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text9.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text9.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text12
			// 
			this.Text12 = new Stimulsoft.Report.Components.StiText();
			this.Text12.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(1, 2.4, 2.7, 0.6);
			this.Text12.Guid = "0223bc4051aa4238a08e0ab773236515";
			this.Text12.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
			this.Text12.Name = "Text12";
			this.Text12.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text12__GetValue);
			this.Text12.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text12.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
			this.Text12.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.All, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text12.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text12.Font = new System.Drawing.Font("宋体", 10.5F);
			this.Text12.Indicator = null;
			this.Text12.Interaction = null;
			this.Text12.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text12.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text12.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text13
			// 
			this.Text13 = new Stimulsoft.Report.Components.StiText();
			this.Text13.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(3.7, 2.4, 1.1, 0.6);
			this.Text13.Guid = "70b8d262ead8450fa8b75b9d484c4ceb";
			this.Text13.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
			this.Text13.Name = "Text13";
			this.Text13.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text13__GetValue);
			this.Text13.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text13.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
			this.Text13.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.All, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text13.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text13.Font = new System.Drawing.Font("宋体", 10.5F);
			this.Text13.Indicator = null;
			this.Text13.Interaction = null;
			this.Text13.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text13.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text13.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text14
			// 
			this.Text14 = new Stimulsoft.Report.Components.StiText();
			this.Text14.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(4.8, 2.4, 1.3, 0.6);
			this.Text14.Guid = "dd0fede85ae24ce8b91e32fd4daf3fc6";
			this.Text14.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
			this.Text14.Name = "Text14";
			this.Text14.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text14__GetValue);
			this.Text14.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text14.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
			this.Text14.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.All, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text14.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text14.Font = new System.Drawing.Font("宋体", 10.5F);
			this.Text14.Indicator = null;
			this.Text14.Interaction = null;
			this.Text14.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text14.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text14.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text15
			// 
			this.Text15 = new Stimulsoft.Report.Components.StiText();
			this.Text15.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(6.1, 2.4, 1.1, 0.6);
			this.Text15.Guid = "b40dfcaa438a450b97dca30c40a67694";
			this.Text15.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
			this.Text15.Name = "Text15";
			this.Text15.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text15__GetValue);
			this.Text15.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text15.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
			this.Text15.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.All, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text15.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text15.Font = new System.Drawing.Font("宋体", 10.5F);
			this.Text15.Indicator = null;
			this.Text15.Interaction = null;
			this.Text15.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text15.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text15.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text16
			// 
			this.Text16 = new Stimulsoft.Report.Components.StiText();
			this.Text16.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(7.2, 2.4, 2.4, 0.6);
			this.Text16.Guid = "0b9437b2cdf74e0b86028f322a4d1d9f";
			this.Text16.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
			this.Text16.Name = "Text16";
			this.Text16.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text16__GetValue);
			this.Text16.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text16.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
			this.Text16.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.All, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text16.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text16.Font = new System.Drawing.Font("宋体", 10.5F);
			this.Text16.Indicator = null;
			this.Text16.Interaction = null;
			this.Text16.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text16.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text16.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text11
			// 
			this.Text11 = new Stimulsoft.Report.Components.StiText();
			this.Text11.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 3, 1.9, 0.9);
			this.Text11.Guid = "c91a17fefb844d6bb87f13a7ec32aea1";
			this.Text11.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
			this.Text11.Name = "Text11";
			this.Text11.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text11__GetValue);
			this.Text11.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text11.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
			this.Text11.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.Bottom, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text11.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text11.Font = new System.Drawing.Font("宋体", 10.5F);
			this.Text11.Indicator = null;
			this.Text11.Interaction = null;
			this.Text11.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text11.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text11.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text17
			// 
			this.Text17 = new Stimulsoft.Report.Components.StiText();
			this.Text17.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(1.9, 3, 7.7, 0.9);
			this.Text17.Guid = "edb57b29db5b4cc885969bad9922abe5";
			this.Text17.Name = "Text17";
			this.Text17.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text17__GetValue);
			this.Text17.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text17.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
			this.Text17.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.Bottom, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text17.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text17.Font = new System.Drawing.Font("宋体", 10.5F);
			this.Text17.Indicator = null;
			this.Text17.Interaction = null;
			this.Text17.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text17.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text17.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text18
			// 
			this.Text18 = new Stimulsoft.Report.Components.StiText();
			this.Text18.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 3.9, 9.6, 0.9);
			this.Text18.Guid = "4bd9191f990c4c088455976e81922396";
			this.Text18.Name = "Text18";
			this.Text18.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text18__GetValue);
			this.Text18.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text18.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
			this.Text18.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text18.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text18.Font = new System.Drawing.Font("Times New Roman", 21.75F, System.Drawing.FontStyle.Bold);
			this.Text18.Indicator = null;
			this.Text18.Interaction = null;
			this.Text18.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text18.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text18.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			this.GroupHeaderBand1.Interaction = null;
			// 
			// DataBand1
			// 
			this.DataBand1 = new Stimulsoft.Report.Components.StiDataBand();
			this.DataBand1.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 6.8, 9.6, 1.8);
			this.DataBand1.DataSourceName = "处方明细1";
			this.DataBand1.Guid = "c6a9e6061c3e4378892221439d6e2dcf";
			this.DataBand1.Name = "DataBand1";
			this.DataBand1.Sort = new System.String[] {
				"DESC",
				"Mz_Id"};
			this.DataBand1.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.DataBand1.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.DataBand1.BusinessObjectGuid = null;
			// 
			// Text21
			// 
			this.Text21 = new Stimulsoft.Report.Components.StiText();
			this.Text21.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 0.01, 4.7, 1.2);
			this.Text21.Guid = "335d237023c640f48874584ffcb3882b";
			this.Text21.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
			this.Text21.Name = "Text21";
			this.Text21.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text21__GetValue);
			this.Text21.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text21.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
			this.Text21.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text21.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text21.Font = new System.Drawing.Font("宋体", 10.5F);
			this.Text21.Indicator = null;
			this.Text21.Interaction = null;
			this.Text21.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text21.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text21.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text32
			// 
			this.Text32 = new Stimulsoft.Report.Components.StiText();
			this.Text32.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(4.7, 0.01, 2.9, 1.2);
			this.Text32.Guid = "17baed80d9c440aab61542cd4ee9153f";
			this.Text32.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
			this.Text32.Name = "Text32";
			this.Text32.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text32__GetValue);
			this.Text32.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text32.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
			this.Text32.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text32.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text32.Font = new System.Drawing.Font("宋体", 10.5F);
			this.Text32.Indicator = null;
			this.Text32.Interaction = null;
			this.Text32.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text32.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text32.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text34
			// 
			this.Text34 = new Stimulsoft.Report.Components.StiText();
			this.Text34.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(7.6, 0.01, 1, 1.2);
			this.Text34.Guid = "67eae76af2ca4747948d105808e0a1f9";
			this.Text34.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Right;
			this.Text34.Name = "Text34";
			this.Text34.NullValue = " ";
			this.Text34.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text34__GetValue);
			this.Text34.Type = Stimulsoft.Report.Components.StiSystemTextType.DataColumn;
			this.Text34.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
			this.Text34.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text34.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text34.Font = new System.Drawing.Font("宋体", 10.5F);
			this.Text34.Indicator = null;
			this.Text34.Interaction = null;
			this.Text34.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text34.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text34.TextFormat = new Stimulsoft.Report.Components.TextFormats.StiCustomFormatService("0.####");
			this.Text34.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text10
			// 
			this.Text10 = new Stimulsoft.Report.Components.StiText();
			this.Text10.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(8.6, 0.01, 1, 1.2);
			this.Text10.Guid = "66bfc1af0a874e828c35b08e61114287";
			this.Text10.Name = "Text10";
			this.Text10.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text10__GetValue);
			this.Text10.Type = Stimulsoft.Report.Components.StiSystemTextType.DataColumn;
			this.Text10.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
			this.Text10.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text10.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text10.Font = new System.Drawing.Font("宋体", 10.5F);
			this.Text10.Indicator = null;
			this.Text10.Interaction = null;
			this.Text10.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text10.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text10.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text33
			// 
			this.Text33 = new Stimulsoft.Report.Components.StiText();
			this.Text33.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 1.2, 9.6, 0.6);
			this.Text33.Name = "Text33";
			this.Text33.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text33__GetValue);
			this.Text33.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text33.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text33.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text33.Font = new System.Drawing.Font("宋体", 10.5F);
			this.Text33.Guid = null;
			this.Text33.Indicator = null;
			this.Text33.Interaction = null;
			this.Text33.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text33.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text33.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			this.DataBand1.DataRelationName = null;
			this.DataBand1.Interaction = null;
			this.DataBand1.MasterComponent = null;
			// 
			// GroupFooterBand1
			// 
			this.GroupFooterBand1 = new Stimulsoft.Report.Components.StiGroupFooterBand();
			this.GroupFooterBand1.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 9.4, 9.6, 3);
			this.GroupFooterBand1.Guid = "fe7e19ad84744d48a19c766f037e0da7";
			this.GroupFooterBand1.Name = "GroupFooterBand1";
			this.GroupFooterBand1.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.GroupFooterBand1.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			// 
			// Text20
			// 
			this.Text20 = new Stimulsoft.Report.Components.StiText();
			this.Text20.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(1.4, 1.11, 1.8, 0.6);
			this.Text20.Guid = "37f37e18fc2e4e248008d335c7ce7b47";
			this.Text20.Name = "Text20";
			this.Text20.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text20__GetValue);
			this.Text20.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text20.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
			this.Text20.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.Bottom, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text20.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text20.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 134);
			this.Text20.Indicator = null;
			this.Text20.Interaction = null;
			this.Text20.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text20.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text20.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text23
			// 
			this.Text23 = new Stimulsoft.Report.Components.StiText();
			this.Text23.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(3.2, 1.7, 1.5, 0.6);
			this.Text23.Guid = "149ccb1fea6a4a15b67daf4eaaa7b47b";
			this.Text23.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
			this.Text23.Name = "Text23";
			this.Text23.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text23__GetValue);
			this.Text23.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text23.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
			this.Text23.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text23.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text23.Font = new System.Drawing.Font("宋体", 10.5F);
			this.Text23.Indicator = null;
			this.Text23.Interaction = null;
			this.Text23.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text23.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text23.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text25
			// 
			this.Text25 = new Stimulsoft.Report.Components.StiText();
			this.Text25.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(4.7, 0.51, 1.5, 0.6);
			this.Text25.Guid = "e79139bf1f7b43b5b98231da53f67ae7";
			this.Text25.Name = "Text25";
			this.Text25.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text25__GetValue);
			this.Text25.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text25.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
			this.Text25.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.Bottom, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text25.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text25.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 134);
			this.Text25.Indicator = null;
			this.Text25.Interaction = null;
			this.Text25.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text25.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text25.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text27
			// 
			this.Text27 = new Stimulsoft.Report.Components.StiText();
			this.Text27.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(6.2, 1.11, 1, 0.6);
			this.Text27.Guid = "808943933f7249519419e61a9e7eb63f";
			this.Text27.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
			this.Text27.Name = "Text27";
			this.Text27.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text27__GetValue);
			this.Text27.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text27.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Bottom;
			this.Text27.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text27.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text27.Font = new System.Drawing.Font("宋体", 10.5F);
			this.Text27.Indicator = null;
			this.Text27.Interaction = null;
			this.Text27.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text27.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text27.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text30
			// 
			this.Text30 = new Stimulsoft.Report.Components.StiText();
			this.Text30.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(7.2, 1.11, 1.9, 0.6);
			this.Text30.Guid = "d35c598bc8b040b3a899c23b2a10978d";
			this.Text30.Name = "Text30";
			// 
			// Text30_Sum
			// 
			this.Text30.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text30__GetValue);
			this.Text30.Type = Stimulsoft.Report.Components.StiSystemTextType.Totals;
			this.Text30.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Bottom;
			this.Text30.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.Bottom, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text30.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text30.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 134);
			this.Text30.Indicator = null;
			this.Text30.Interaction = null;
			this.Text30.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text30.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text30.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text31
			// 
			this.Text31 = new Stimulsoft.Report.Components.StiText();
			this.Text31.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(7.2, 0.51, 1.9, 0.6);
			this.Text31.Guid = "0d3ab5c73b7543d18fcde5d9305994e7";
			this.Text31.Name = "Text31";
			this.Text31.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text31.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Bottom;
			this.Text31.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.Bottom, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text31.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text31.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 134);
			this.Text31.Indicator = null;
			this.Text31.Interaction = null;
			this.Text31.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text31.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text31.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text22
			// 
			this.Text22 = new Stimulsoft.Report.Components.StiText();
			this.Text22.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(3.2, 0.51, 1.5, 0.6);
			this.Text22.Guid = "db469fbe118448789e08e5d3a82127fe";
			this.Text22.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
			this.Text22.Name = "Text22";
			this.Text22.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text22__GetValue);
			this.Text22.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text22.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
			this.Text22.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text22.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text22.Font = new System.Drawing.Font("宋体", 10.5F);
			this.Text22.Indicator = null;
			this.Text22.Interaction = null;
			this.Text22.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text22.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text22.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text26
			// 
			this.Text26 = new Stimulsoft.Report.Components.StiText();
			this.Text26.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(6.2, 1.71, 1, 0.6);
			this.Text26.Guid = "f8b7d5f80de247d0b2ff2af240acd473";
			this.Text26.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
			this.Text26.Name = "Text26";
			this.Text26.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text26__GetValue);
			this.Text26.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text26.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Bottom;
			this.Text26.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text26.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text26.Font = new System.Drawing.Font("宋体", 10.5F);
			this.Text26.Indicator = null;
			this.Text26.Interaction = null;
			this.Text26.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text26.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text26.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text29
			// 
			this.Text29 = new Stimulsoft.Report.Components.StiText();
			this.Text29.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(7.2, 1.71, 1.9, 0.6);
			this.Text29.Guid = "d5248e1de5f348889595e826aa476c39";
			this.Text29.Name = "Text29";
			this.Text29.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text29.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Bottom;
			this.Text29.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.Bottom, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text29.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text29.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 134);
			this.Text29.Indicator = null;
			this.Text29.Interaction = null;
			this.Text29.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text29.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text29.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text24
			// 
			this.Text24 = new Stimulsoft.Report.Components.StiText();
			this.Text24.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(4.7, 1.7, 1.5, 0.6);
			this.Text24.Guid = "3ae4157d046e4e2f8cd32c03076c071f";
			this.Text24.Name = "Text24";
			this.Text24.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text24__GetValue);
			this.Text24.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text24.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
			this.Text24.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.Bottom, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text24.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text24.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 134);
			this.Text24.Indicator = null;
			this.Text24.Interaction = null;
			this.Text24.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text24.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text24.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text28
			// 
			this.Text28 = new Stimulsoft.Report.Components.StiText();
			this.Text28.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(6.2, 0.51, 1, 0.6);
			this.Text28.Guid = "71d876062e6241c7aebf603ba01f6c05";
			this.Text28.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
			this.Text28.Name = "Text28";
			this.Text28.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text28__GetValue);
			this.Text28.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text28.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Bottom;
			this.Text28.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text28.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text28.Font = new System.Drawing.Font("宋体", 10.5F);
			this.Text28.Indicator = null;
			this.Text28.Interaction = null;
			this.Text28.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text28.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text28.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			// 
			// Text19
			// 
			this.Text19 = new Stimulsoft.Report.Components.StiText();
			this.Text19.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0.5, 1.11, 0.9, 0.6);
			this.Text19.Guid = "590cfd4cefa5495d9f52e53fb003c419";
			this.Text19.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
			this.Text19.Name = "Text19";
			this.Text19.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text19__GetValue);
			this.Text19.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
			this.Text19.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
			this.Text19.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
			this.Text19.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
			this.Text19.Font = new System.Drawing.Font("宋体", 10.5F);
			this.Text19.Indicator = null;
			this.Text19.Interaction = null;
			this.Text19.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
			this.Text19.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
			this.Text19.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
			this.GroupFooterBand1.Interaction = null;
			this.Page1.ExcelSheetValue = null;
			this.Page1.Interaction = null;
			this.Page1.Margins = new Stimulsoft.Report.Components.StiMargins(0.2, 0.2, 1, 1);
			this.Page1_Watermark = new Stimulsoft.Report.Components.StiWatermark();
			this.Page1_Watermark.Font = new System.Drawing.Font("Arial", 100F);
			this.Page1_Watermark.Image = null;
			this.Page1_Watermark.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.FromArgb(50, 0, 0, 0));
			this.门诊处方表_PrinterSettings = new Stimulsoft.Report.Print.StiPrinterSettings();
			this.PrinterSettings = this.门诊处方表_PrinterSettings;
			this.Page1.Report = this;
			this.Page1.Watermark = this.Page1_Watermark;
			this.ReportTitleBand1.Page = this.Page1;
			this.ReportTitleBand1.Parent = this.Page1;
			this.GroupHeaderBand1.Page = this.Page1;
			this.GroupHeaderBand1.Parent = this.Page1;
			this.Text1.Page = this.Page1;
			this.Text1.Parent = this.GroupHeaderBand1;
			this.Text2.Page = this.Page1;
			this.Text2.Parent = this.GroupHeaderBand1;
			this.Text3.Page = this.Page1;
			this.Text3.Parent = this.GroupHeaderBand1;
			this.Text4.Page = this.Page1;
			this.Text4.Parent = this.GroupHeaderBand1;
			this.Text6.Page = this.Page1;
			this.Text6.Parent = this.GroupHeaderBand1;
			this.Text5.Page = this.Page1;
			this.Text5.Parent = this.GroupHeaderBand1;
			this.Text7.Page = this.Page1;
			this.Text7.Parent = this.GroupHeaderBand1;
			this.Text8.Page = this.Page1;
			this.Text8.Parent = this.GroupHeaderBand1;
			this.Text9.Page = this.Page1;
			this.Text9.Parent = this.GroupHeaderBand1;
			this.Text12.Page = this.Page1;
			this.Text12.Parent = this.GroupHeaderBand1;
			this.Text13.Page = this.Page1;
			this.Text13.Parent = this.GroupHeaderBand1;
			this.Text14.Page = this.Page1;
			this.Text14.Parent = this.GroupHeaderBand1;
			this.Text15.Page = this.Page1;
			this.Text15.Parent = this.GroupHeaderBand1;
			this.Text16.Page = this.Page1;
			this.Text16.Parent = this.GroupHeaderBand1;
			this.Text11.Page = this.Page1;
			this.Text11.Parent = this.GroupHeaderBand1;
			this.Text17.Page = this.Page1;
			this.Text17.Parent = this.GroupHeaderBand1;
			this.Text18.Page = this.Page1;
			this.Text18.Parent = this.GroupHeaderBand1;
			this.DataBand1.Page = this.Page1;
			this.DataBand1.Parent = this.Page1;
			this.Text21.Page = this.Page1;
			this.Text21.Parent = this.DataBand1;
			this.Text32.Page = this.Page1;
			this.Text32.Parent = this.DataBand1;
			this.Text34.Page = this.Page1;
			this.Text34.Parent = this.DataBand1;
			this.Text10.Page = this.Page1;
			this.Text10.Parent = this.DataBand1;
			this.Text33.Page = this.Page1;
			this.Text33.Parent = this.DataBand1;
			this.GroupFooterBand1.Page = this.Page1;
			this.GroupFooterBand1.Parent = this.Page1;
			this.Text20.Page = this.Page1;
			this.Text20.Parent = this.GroupFooterBand1;
			this.Text23.Page = this.Page1;
			this.Text23.Parent = this.GroupFooterBand1;
			this.Text25.Page = this.Page1;
			this.Text25.Parent = this.GroupFooterBand1;
			this.Text27.Page = this.Page1;
			this.Text27.Parent = this.GroupFooterBand1;
			this.Text30.Page = this.Page1;
			this.Text30.Parent = this.GroupFooterBand1;
			this.Text31.Page = this.Page1;
			this.Text31.Parent = this.GroupFooterBand1;
			this.Text22.Page = this.Page1;
			this.Text22.Parent = this.GroupFooterBand1;
			this.Text26.Page = this.Page1;
			this.Text26.Parent = this.GroupFooterBand1;
			this.Text29.Page = this.Page1;
			this.Text29.Parent = this.GroupFooterBand1;
			this.Text24.Page = this.Page1;
			this.Text24.Parent = this.GroupFooterBand1;
			this.Text28.Page = this.Page1;
			this.Text28.Parent = this.GroupFooterBand1;
			this.Text19.Page = this.Page1;
			this.Text19.Parent = this.GroupFooterBand1;
			this.GroupHeaderBand1.BeginRender += new System.EventHandler(this.GroupHeaderBand1__BeginRender);
			this.GroupHeaderBand1.EndRender += new System.EventHandler(this.GroupHeaderBand1__EndRender);
			this.GroupHeaderBand1.Rendering += new System.EventHandler(this.GroupHeaderBand1__Rendering);
			this.AggregateFunctions = new object[] {
				this.Text30_Sum};
			// 
			// Add to GroupHeaderBand1.Components
			// 
			this.GroupHeaderBand1.Components.Clear();
			this.GroupHeaderBand1.Components.AddRange(new Stimulsoft.Report.Components.StiComponent[] {
				this.Text1,
				this.Text2,
				this.Text3,
				this.Text4,
				this.Text6,
				this.Text5,
				this.Text7,
				this.Text8,
				this.Text9,
				this.Text12,
				this.Text13,
				this.Text14,
				this.Text15,
				this.Text16,
				this.Text11,
				this.Text17,
				this.Text18});
			// 
			// Add to DataBand1.Components
			// 
			this.DataBand1.Components.Clear();
			this.DataBand1.Components.AddRange(new Stimulsoft.Report.Components.StiComponent[] {
				this.Text21,
				this.Text32,
				this.Text34,
				this.Text10,
				this.Text33});
			// 
			// Add to GroupFooterBand1.Components
			// 
			this.GroupFooterBand1.Components.Clear();
			this.GroupFooterBand1.Components.AddRange(new Stimulsoft.Report.Components.StiComponent[] {
				this.Text20,
				this.Text23,
				this.Text25,
				this.Text27,
				this.Text30,
				this.Text31,
				this.Text22,
				this.Text26,
				this.Text29,
				this.Text24,
				this.Text28,
				this.Text19});
			// 
			// Add to Page1.Components
			// 
			this.Page1.Components.Clear();
			this.Page1.Components.AddRange(new Stimulsoft.Report.Components.StiComponent[] {
				this.ReportTitleBand1,
				this.GroupHeaderBand1,
				this.DataBand1,
				this.GroupFooterBand1});
			// 
			// Add to Pages
			// 
			this.Pages.Clear();
			this.Pages.AddRange(new Stimulsoft.Report.Components.StiPage[] {
				this.Page1});
			this.处方明细1.Columns.AddRange(new Stimulsoft.Report.Dictionary.StiDataColumn[] {
				new Stimulsoft.Report.Dictionary.StiDataColumn("Mx_Code", "Mx_Code", "Mx_Code", typeof(string)),
				new Stimulsoft.Report.Dictionary.StiDataColumn("Mz_Id", "Mz_Id", "Mz_Id", typeof(int)),
				new Stimulsoft.Report.Dictionary.StiDataColumn("Mx_Gg", "Mx_Gg", "Mx_Gg", typeof(string)),
				new Stimulsoft.Report.Dictionary.StiDataColumn("Mz_Sl", "Mz_Sl", "Mz_Sl", typeof(decimal)),
				new Stimulsoft.Report.Dictionary.StiDataColumn("Mx_XsDw", "Mx_XsDw", "Mx_XsDw", typeof(string)),
				new Stimulsoft.Report.Dictionary.StiDataColumn("Yp_Yfyl", "Yp_Yfyl", "Yp_Yfyl", typeof(string)),
				new Stimulsoft.Report.Dictionary.StiDataColumn("Mz_Money", "Mz_Money", "Mz_Money", typeof(decimal)),
				new Stimulsoft.Report.Dictionary.StiDataColumn("IsJb", "IsJb", "IsJb", typeof(string)),
				new Stimulsoft.Report.Dictionary.StiDataColumn("Yp_Name", "Yp_Name", "Yp_Name", typeof(string))});
			this.DataSources.Add(this.处方明细1);
			}
       
			#region DataSource 处方明细1
			public class 处方明细1DataSource : Stimulsoft.Report.Dictionary.StiDataTableSource
			{
           
			public 处方明细1DataSource() : 
			base("处方明细1", "处方明细1")
			{
			}
           
			public virtual string Mx_Code
			{
			get
			{
			return ((string)(StiReport.ChangeType(this["Mx_Code"], typeof(string), true)));
			}
			}
           
			public virtual int Mz_Id
			{
			get
			{
			return ((int)(StiReport.ChangeType(this["Mz_Id"], typeof(int), true)));
			}
			}
           
			public virtual string Mx_Gg
			{
			get
			{
			return ((string)(StiReport.ChangeType(this["Mx_Gg"], typeof(string), true)));
			}
			}
           
			public virtual decimal Mz_Sl
			{
			get
			{
			return ((decimal)(StiReport.ChangeType(this["Mz_Sl"], typeof(decimal), true)));
			}
			}
           
			public virtual string Mx_XsDw
			{
			get
			{
			return ((string)(StiReport.ChangeType(this["Mx_XsDw"], typeof(string), true)));
			}
			}
           
			public virtual string Yp_Yfyl
			{
			get
			{
			return ((string)(StiReport.ChangeType(this["Yp_Yfyl"], typeof(string), true)));
			}
			}
           
			public virtual decimal Mz_Money
			{
			get
			{
			return ((decimal)(StiReport.ChangeType(this["Mz_Money"], typeof(decimal), true)));
			}
			}
           
			public virtual string IsJb
			{
			get
			{
			return ((string)(StiReport.ChangeType(this["IsJb"], typeof(string), true)));
			}
			}
           
			public virtual string Yp_Name
			{
			get
			{
			return ((string)(StiReport.ChangeType(this["Yp_Name"], typeof(string), true)));
			}
			}
			}
			#endregion DataSource 处方明细1
			#endregion StiReport Designer generated code - do not modify
			}
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>