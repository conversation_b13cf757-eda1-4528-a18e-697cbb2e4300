﻿Imports BaseClass
Imports System.Windows.Forms
Imports System.Drawing
Public Class MCSCondition
    Public outputstr As String
    Dim str_Select As String
    Dim My_Dataset As New DataSet            '数据库
    Dim BllMaterDict As New BLLOld.B_Materials_Dict
    Dim bllMateWarehDict As New BLLOld.B_Materials_Warehouse_Dict
    Dim bllYYJsr As New BLLOld.B_Zd_YyJsr
    Dim firstinit As Boolean

    Private Sub MCSCondition_Load(sender As System.Object, e As System.EventArgs) Handles MyBase.Load
        FormInt()
    End Sub
#Region "自定义函数"

    Private Function cx()
        str_Select = ""
        If PDCheckBox1.Checked = True Then
            If PDDoubleDateEdit.StartValue Is DBNull.Value And PDDoubleDateEdit.EndValue Is DBNull.Value Then
            ElseIf PDDoubleDateEdit.StartValue Is DBNull.Value Then
                str_Select += " and Check_Date <= '" & PDDoubleDateEdit.EndValue & "' "
            ElseIf PDDoubleDateEdit.EndValue Is DBNull.Value Then
                str_Select += " and Check_Date >= '" & PDDoubleDateEdit.StartValue & "' "
            Else
                str_Select += " and  Check_Date BETWEEN '" & PDDoubleDateEdit.StartValue & "' AND '" & PDDoubleDateEdit.EndValue & "' "
            End If

        End If
        If SCCJTextBox1.Text <> "" Then
            str_Select += " and MateManu_Name = '" & SCCJTextBox1.Text & "' "
        End If
        If lrCheckBox3.Checked = True Then
            If LrDoubleDateEdit.StartValue Is DBNull.Value And LrDoubleDateEdit.EndValue Is DBNull.Value Then
            ElseIf LrDoubleDateEdit.StartValue Is DBNull.Value Then
                str_Select += " and Input_Date <= '" & LrDoubleDateEdit.EndValue & "' "
            ElseIf LrDoubleDateEdit.EndValue Is DBNull.Value Then
                str_Select += " and Input_Date >= '" & LrDoubleDateEdit.StartValue & "' "
            Else
                str_Select += " and  Input_Date BETWEEN '" & LrDoubleDateEdit.StartValue & "' AND '" & LrDoubleDateEdit.EndValue & "' "
            End If
        End If
        If wcCheckBox4.Checked = True Then
            If WCDoubleDateEdit.StartValue Is DBNull.Value And WCDoubleDateEdit.EndValue Is DBNull.Value Then
            ElseIf WCDoubleDateEdit.StartValue Is DBNull.Value Then
                str_Select += " and Finish_Date <= '" & WCDoubleDateEdit.EndValue & "' "
            ElseIf WCDoubleDateEdit.EndValue Is DBNull.Value Then
                str_Select += " and Finish_Date >= '" & WCDoubleDateEdit.StartValue & "' "
            Else
                str_Select += " and  Finish_Date BETWEEN '" & WCDoubleDateEdit.StartValue & "' AND '" & WCDoubleDateEdit.EndValue & "' "
            End If
        End If
       
        If DJZTSingleComobo.SelectedText <> "全部" Then
            str_Select += " and OrdersStatus='" & DJZTSingleComobo.Text & "' "
        End If
     
        If KFDtComobo.Text <> "" Then
            str_Select += " and Materials_Check1.MaterialsWh_Code='" & KFDtComobo.SelectedValue & "' "
        End If
        If WZMCDtComobo.Text <> "" Then
            str_Select += " and Materials_Name='" & WZMCDtComobo.Text & "' "
        End If

        If JSRDtComobo.Text <> "" Then
            str_Select += " and Materials_Check1.Jsr_Code='" & JSRDtComobo.SelectedValue & "' "
        End If
        If WZPHTextBox1.Text <> "" Then
            str_Select += " and Materials_Check2.MaterialsLot='" & WZPHTextBox1.Text & "' "
        End If
        If GGTextBox.Text <> "" Then
            str_Select += " and Materials_Spec='" & GGTextBox.Text & "' "
        End If
        If MAXykTextBox1.Text <> "" Then
            str_Select += " and M_Check_Num<='" & MAXykTextBox1.Text & "' "
        End If
        If MINykTextBox2.Text <> "" Then
            str_Select += " and Materials_Check2.M_Check_Num>='" & MINykTextBox2.Text & "' "
        End If
        Return str_Select
    End Function
    Private Sub ccconfig()

        iniOperate.iniopreate.WriteINI("盘点查询参数", "盘点日期1", PDDoubleDateEdit.StartValue & "", HisVar.HisVar.Parapath & "\Config.ini")
        iniOperate.iniopreate.WriteINI("盘点查询参数", "盘点日期2", PDDoubleDateEdit.EndValue & "", HisVar.HisVar.Parapath & "\Config.ini")

        iniOperate.iniopreate.WriteINI("盘点查询参数", "录入日期1", LrDoubleDateEdit.StartValue & "", HisVar.HisVar.Parapath & "\Config.ini")
        iniOperate.iniopreate.WriteINI("盘点查询参数", "录入日期2", LrDoubleDateEdit.EndValue & "", HisVar.HisVar.Parapath & "\Config.ini")
        iniOperate.iniopreate.WriteINI("盘点查询参数", "完成日期1", WCDoubleDateEdit.StartValue & "", HisVar.HisVar.Parapath & "\Config.ini")
        iniOperate.iniopreate.WriteINI("盘点查询参数", "完成日期2", WCDoubleDateEdit.EndValue & "", HisVar.HisVar.Parapath & "\Config.ini")
        iniOperate.iniopreate.WriteINI("盘点查询参数", "规格", GGTextBox.Text, HisVar.HisVar.Parapath & "\Config.ini")
        iniOperate.iniopreate.WriteINI("盘点查询参数", "单据状态", DJZTSingleComobo.SelectedIndex, HisVar.HisVar.Parapath & "\Config.ini")
        iniOperate.iniopreate.WriteINI("盘点查询参数", "库房名称", KFDtComobo.SelectedValue, HisVar.HisVar.Parapath & "\Config.ini")
        iniOperate.iniopreate.WriteINI("盘点查询参数", "物资名称", WZMCDtComobo.Text, HisVar.HisVar.Parapath & "\Config.ini")
        iniOperate.iniopreate.WriteINI("盘点查询参数", "经手人", JSRDtComobo.SelectedValue, HisVar.HisVar.Parapath & "\Config.ini")
        iniOperate.iniopreate.WriteINI("盘点查询参数", "最大盈亏", MAXykTextBox1.Text, HisVar.HisVar.Parapath & "\Config.ini")
        iniOperate.iniopreate.WriteINI("盘点查询参数", "最小盈亏", MINykTextBox2.Text, HisVar.HisVar.Parapath & "\Config.ini")
        iniOperate.iniopreate.WriteINI("盘点查询参数", "物资批号", WZPHTextBox1.Text, HisVar.HisVar.Parapath & "\Config.ini")
        iniOperate.iniopreate.WriteINI("盘点查询参数", "生产厂家", SCCJTextBox1.Text, HisVar.HisVar.Parapath & "\Config.ini")
        iniOperate.iniopreate.WriteINI("盘点查询参数", "盘点日期", PDCheckBox1.Checked, HisVar.HisVar.Parapath & "\Config.ini")
        iniOperate.iniopreate.WriteINI("盘点查询参数", "录入日期", lrCheckBox3.Checked, HisVar.HisVar.Parapath & "\Config.ini")
        iniOperate.iniopreate.WriteINI("盘点查询参数", "完成日期", wcCheckBox4.Checked, HisVar.HisVar.Parapath & "\Config.ini")

    End Sub

#End Region
#Region "初始化窗体"

    Private Sub FormInt()
        PDDoubleDateEdit.Enabled = False
        LrDoubleDateEdit.Enabled = False
        WCDoubleDateEdit.Enabled = False

        If iniOperate.iniopreate.GetINI("盘点查询参数", "盘点日期", "", HisVar.HisVar.Parapath & "\Config.ini") & "" <> "" Then
            PDCheckBox1.Checked = iniOperate.iniopreate.GetINI("盘点查询参数", "盘点日期", "", HisVar.HisVar.Parapath & "\Config.ini") & ""
        Else
            PDCheckBox1.Checked = False
        End If

        If iniOperate.iniopreate.GetINI("盘点查询参数", "录入日期", "", HisVar.HisVar.Parapath & "\Config.ini") & "" <> "" Then
            lrCheckBox3.Checked = iniOperate.iniopreate.GetINI("盘点查询参数", "录入日期", "", HisVar.HisVar.Parapath & "\Config.ini") & ""
        Else
            lrCheckBox3.Checked = False
        End If
        If iniOperate.iniopreate.GetINI("盘点查询参数", "完成日期", "", HisVar.HisVar.Parapath & "\Config.ini") & "" <> "" Then
            wcCheckBox4.Checked = iniOperate.iniopreate.GetINI("盘点查询参数", "完成日期", "", HisVar.HisVar.Parapath & "\Config.ini") & ""
        Else
            wcCheckBox4.Checked = False
        End If

        PDDoubleDateEdit.StartValue = iniOperate.iniopreate.GetINI("盘点查询参数", "盘点日期1", "", HisVar.HisVar.Parapath & "\Config.ini") & ""
        PDDoubleDateEdit.EndValue = iniOperate.iniopreate.GetINI("盘点查询参数", "盘点日期2", "", HisVar.HisVar.Parapath & "\Config.ini") & ""

        LrDoubleDateEdit.StartValue = iniOperate.iniopreate.GetINI("盘点查询参数", "录入日期1", "", HisVar.HisVar.Parapath & "\Config.ini") & ""
        LrDoubleDateEdit.EndValue = iniOperate.iniopreate.GetINI("盘点查询参数", "录入日期2", "", HisVar.HisVar.Parapath & "\Config.ini") & ""

        WCDoubleDateEdit.StartValue = iniOperate.iniopreate.GetINI("盘点查询参数", "完成日期1", "", HisVar.HisVar.Parapath & "\Config.ini") & ""
        WCDoubleDateEdit.EndValue = iniOperate.iniopreate.GetINI("盘点查询参数", "完成日期2", "", HisVar.HisVar.Parapath & "\Config.ini") & ""

        With WZMCDtComobo
            .DataView = BllMaterDict.GetAList().Tables(0).DefaultView
            .Init_Colum("Materials_Name", "物资名称", 80, "左")
            .Init_Colum("Materials_Py", "物资拼音", 60, "左")
            .DisplayMember = "Materials_Name"
            .RowFilterNotTextNull = "Materials_Py"
            .RowFilterTextNull = ""
            .DroupDownWidth = .Width - 40
            .MaxDropDownItems = 15
            .SelectedValue = -1
            If iniOperate.iniopreate.GetINI("盘点查询参数", "物资名称", "", HisVar.HisVar.Parapath & "\Config.ini") & "" <> "" Then
                .Text = iniOperate.iniopreate.GetINI("盘点查询参数", "物资名称", "", HisVar.HisVar.Parapath & "\Config.ini")
            Else
                .SelectedValue = -1
            End If
        End With

        With KFDtComobo
            .DataView = bllMateWarehDict.GetAllList().Tables(0).DefaultView
            .Init_Colum("MaterialsWh_Code", "库房编码", 0, "左")
            .Init_Colum("MaterialsWh_Name", "库房名称", 80, "左")
            .Init_Colum("MaterialsWh_Py", "库房拼音", 60, "左")
            .DisplayMember = "MaterialsWh_Name"
            .ValueMember = "MaterialsWh_Code"
            .RowFilterNotTextNull = "MaterialsWh_Py"
            .RowFilterTextNull = ""
            .DroupDownWidth = .Width - 40
            .MaxDropDownItems = 15
            If iniOperate.iniopreate.GetINI("盘点查询参数", "库房名称", "", HisVar.HisVar.Parapath & "\Config.ini") & "" <> "" Then
                .SelectedValue = iniOperate.iniopreate.GetINI("盘点查询参数", "库房名称", "", HisVar.HisVar.Parapath & "\Config.ini")
            Else
                .SelectedValue = -1
            End If

        End With


        With JSRDtComobo
            .DataView = bllYYJsr.GetAllList().Tables(0).DefaultView
            .Init_Colum("Jsr_Code", "经手人编码", 0, "左")
            .Init_Colum("Jsr_Name", "经手人名称", 80, "左")
            .Init_Colum("Jsr_Jc", "经手人拼音", 60, "左")
            .DisplayMember = "Jsr_Name"
            .ValueMember = "Jsr_Code"
            .RowFilterNotTextNull = "Jsr_Jc"
            .RowFilterTextNull = ""
            .DroupDownWidth = .Width - 40
            .MaxDropDownItems = 15
            If iniOperate.iniopreate.GetINI("盘点查询参数", "经手人", "", HisVar.HisVar.Parapath & "\Config.ini") & "" <> "" Then
                .SelectedValue = iniOperate.iniopreate.GetINI("盘点查询参数", "经手人", "", HisVar.HisVar.Parapath & "\Config.ini")
            Else
                .SelectedValue = -1
            End If

        End With
        With DJZTSingleComobo
            .Additem = "全部"
            .Additem = "录入"
            .Additem = "完成"
            .DisplayColumns(1).Visible = False
            If iniOperate.iniopreate.GetINI("盘点查询参数", "单据状态", "", HisVar.HisVar.Parapath & "\Config.ini") & "" <> "" Then
                .SelectedIndex = iniOperate.iniopreate.GetINI("盘点查询参数", "单据状态", "", HisVar.HisVar.Parapath & "\Config.ini")
            Else
                .SelectedIndex = 0
            End If
        End With

        If iniOperate.iniopreate.GetINI("盘点查询参数", "物资批号", "", HisVar.HisVar.Parapath & "\Config.ini") & "" <> "" Then
            WZPHTextBox1.Text = iniOperate.iniopreate.GetINI("盘点查询参数", "物资批号", "", HisVar.HisVar.Parapath & "\Config.ini")
        Else
            WZPHTextBox1.Text = ""
        End If
        If iniOperate.iniopreate.GetINI("盘点查询参数", "规格", "", HisVar.HisVar.Parapath & "\Config.ini") & "" <> "" Then
            GGTextBox.Text = iniOperate.iniopreate.GetINI("盘点查询参数", "规格", "", HisVar.HisVar.Parapath & "\Config.ini")
        Else
            GGTextBox.Text = ""
        End If
        If iniOperate.iniopreate.GetINI("盘点查询参数", "生产厂家", "", HisVar.HisVar.Parapath & "\Config.ini") & "" <> "" Then
            SCCJTextBox1.Text = iniOperate.iniopreate.GetINI("盘点查询参数", "生产厂家", "", HisVar.HisVar.Parapath & "\Config.ini")
        Else
            SCCJTextBox1.Text = ""
        End If
        If iniOperate.iniopreate.GetINI("盘点查询参数", "最大盈亏", "", HisVar.HisVar.Parapath & "\Config.ini") & "" <> "" Then
            MAXykTextBox1.Text = iniOperate.iniopreate.GetINI("盘点查询参数", "最大盈亏", "", HisVar.HisVar.Parapath & "\Config.ini")
        Else
            MAXykTextBox1.Text = ""
        End If
        If iniOperate.iniopreate.GetINI("盘点查询参数", "最小盈亏", "", HisVar.HisVar.Parapath & "\Config.ini") & "" <> "" Then
            MINykTextBox2.Text = iniOperate.iniopreate.GetINI("盘点查询参数", "最小盈亏", "", HisVar.HisVar.Parapath & "\Config.ini")
        Else
            MINykTextBox2.Text = ""
        End If

    End Sub

#End Region
#Region "动作按钮"

    Private Sub PDlCheckBox1_CheckedChanged(sender As Object, e As System.EventArgs) Handles PDCheckBox1.CheckedChanged
        If PDCheckBox1.Checked = False Then
            PDDoubleDateEdit.Enabled = False
        Else
            PDDoubleDateEdit.Enabled = True
        End If
    End Sub

    Private Sub MyButton1_Click(sender As System.Object, e As System.EventArgs) Handles MyButton1.Click
        PDDoubleDateEdit.StartValue = Now
        PDDoubleDateEdit.EndValue = Now
        LrDoubleDateEdit.StartValue = Now
        LrDoubleDateEdit.EndValue = Now
        WCDoubleDateEdit.StartValue = Now
        WCDoubleDateEdit.EndValue = Now
        PDCheckBox1.Checked = False
        lrCheckBox3.Checked = False
        wcCheckBox4.Checked = False
        WZMCDtComobo.Text = ""
        KFDtComobo.SelectedValue = -1
        JSRDtComobo.SelectedValue = -1
        DJZTSingleComobo.SelectedIndex = 0
        WZPHTextBox1.Text = ""
        GGTextBox.Text = ""
        SCCJTextBox1.Text = ""
        MAXykTextBox1.Text = ""
        MINykTextBox2.Text = ""
    End Sub

    Private Sub lrCheckBox3_CheckedChanged(sender As Object, e As System.EventArgs) Handles lrCheckBox3.CheckedChanged
        If lrCheckBox3.Checked = False Then
            LrDoubleDateEdit.Enabled = False
        Else
            LrDoubleDateEdit.Enabled = True
        End If
    End Sub

    Private Sub wcCheckBox4_CheckedChanged(sender As Object, e As System.EventArgs) Handles wcCheckBox4.CheckedChanged
        If wcCheckBox4.Checked = False Then
            WCDoubleDateEdit.Enabled = False

        Else
            WCDoubleDateEdit.Enabled = True
        End If
    End Sub



    Private Sub QXButton_Click(sender As System.Object, e As System.EventArgs) Handles QXButton.Click
        Me.Close()
    End Sub
    Private Sub QDButton_Click(sender As System.Object, e As System.EventArgs) Handles QDButton.Click
        ccconfig()
        outputstr = cx()
    End Sub
#End Region
#Region "输入法设置"
    '中文
    Private Sub C1TextBox1_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles WZPHTextBox1.GotFocus, MINykTextBox2.GotFocus, MAXykTextBox1.GotFocus, GGTextBox.GotFocus, SCCJTextBox1.GotFocus, MINykTextBox2.GotFocus, MAXykTextBox1.GotFocus
        InputLanguage.CurrentInputLanguage = HisVar.HisVar.InputCode
    End Sub
    '英文
    Private Sub yw_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles KFDtComobo.GotFocus, WZMCDtComobo.GotFocus, JSRDtComobo.GotFocus
        InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages(0)
    End Sub

#End Region

   
   
End Class