﻿Imports System.Data.SqlClient
Imports Stimulsoft.Report

Public Class YkYf_KcCx

#Region "变量初始化"

    Dim My_View As New DataView                 '视图
    Dim V_Finish As Boolean = False             '初始化完成
    Dim V_SelectCode As String = "00"           '选中的节点编码
    Public My_Dataset As New DataSet
    Public My_Adapter As New SqlDataAdapter
    Public My_Table As New DataTable            '药品明细
    Public My_Cm As CurrencyManager             '同步指针
    Public My_Row As DataRow                    '当前选择行
    Public V_SelectNode As String = "全部"      '选中的节点级别

    Dim YfCode As String
    Dim YfName As String
    Dim Formlb As String

#End Region

    Public Sub New(ByVal m_YfCode As String, ByVal m_YfName As String, ByVal m_Formlb As String)

        ' 此调用是设计器所必需的。
        InitializeComponent()
        YfCode = m_YfCode
        YfName = m_YfName
        Formlb = m_Formlb
        ' 在 InitializeComponent() 调用之后添加任何初始化。

    End Sub

    Private Sub YkYf_KcCx_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load


        Call Form_Init()
        Call Init_Data()        '数据初始化
        Call Init_Tree()        'TreeView初始化
        Call F_Sum()
        CheckBox1.Checked = False


    End Sub

#Region "窗体初始化"

    Private Sub Form_Init()

        '初始化TDBGrid
        Dim My_Grid As New BaseClass.C_Grid(Me.C1TrueDBGrid1)
        With My_Grid
            .Init_Grid()
            .Init_Column("药品编码", "Xx_Code", 110, "左", "")
            .Init_Column("药品名称", "Yp_Name", 140, "左", "")
            .Init_Column("药品规格", "Mx_Gg", 80, "左", "")
            .Init_Column("产地", "Mx_Cd", 90, "左", "")
            .Init_Column("生产批号", "Yp_Ph", 65, "左", "")
            .Init_Column("有效期", "Yp_Yxq", 75, "中", "yyyy-MM-dd")
            .Init_Column("生产日期", "Yp_Scrq", 0, "中", "yyyy-MM-dd")
            .Init_Column("单位", "Yp_Dw", 45, "中", "")
            .Init_Column("库存", "Yp_Sl", 60, "右", "0.####")
            If Formlb = "药库信息查询" Then
                .Init_Column("采购价", "Yp_Cgj", 60, "右", "0.00##")
                .Init_Column("批发价", "Yp_Pfj", 60, "右", "0.00##")
            ElseIf Formlb = "药房信息查询" And HisPara.PublicConfig.XqName = "丰润" Then '丰润允许药房看到采购价
                .Init_Column("采购价", "Yp_Cgj", 60, "右", "0.00##")
                .Init_Column("采购金额", "Cg_Money", 60, "右", "0.00##")
            End If
            .Init_Column("销售价", "Yp_Xsj", 60, "右", "0.00##")


            If Formlb = "药库信息查询" Then
                .Init_Column("采购金额", "Cg_Money", 60, "右", "0.00##")
                .Init_Column("批发金额", "Pf_Money", 60, "右", "0.00##")
            End If
          
            .Init_Column("销售金额", "Xs_Money", 60, "右", "0.00##")
            .Init_Column("基本药品", "IsJb", 80, "中", "")
            .Init_Column("预警天数", "Alter_Ts", 0, "中", "")
            .P_Dock(BaseClass.C_Grid.Dock.Fill)
        End With
        C1TrueDBGrid1.Splits(0).DisplayColumns("Yp_Scrq").Visible = False

        C1TrueDBGrid1.Splits(0).DisplayColumns("Alter_Ts").Visible = False
        C1TrueDBGrid1.HScrollBar.Style = C1.Win.C1TrueDBGrid.ScrollBarStyleEnum.Automatic
        C1TrueDBGrid1.RecordSelectors = False

        C1TrueDBGrid1.FetchRowStyles = True

        'Treeview初始化
        With Me.TreeView1
            .Nodes.Clear()
            .FullRowSelect = True
            .HideSelection = False
            .HotTracking = True
            .Scrollable = False
            .ShowRootLines = False
            .Sorted = False
        End With
    End Sub

    Private Sub Init_Data()
        Dim Str_Select As String = ""
        If Formlb = "药库信息查询" Then
            C1Label1.Text = "选中记录，点鼠标右键修改入库参数。红色为已过期药品，橘黄色为一个月内即将过期"
            Str_Select = "select Dl_Code,Dl_Name,Xx_Code,Yp_Name,Yp_Jc,Mx_Code,Mx_Gg,Mx_Cd,Mx_Gyzz,Yp_Ph,Yp_Yxq,Datediff(day,Getdate(),Yp_Yxq) Alter_Ts, Mx_CgDw as Yp_Dw,Yk_Cgj as Yp_Cgj,Yk_Pfj as Yp_Pfj,Yk_Xsj as Yp_Xsj,Yk_Sl as Yp_Sl,Yk_Cgj*Yk_Sl as Cg_Money,Yk_Pfj*Yk_Sl as Pf_Money,Yk_Xsj*Yk_Sl as Xs_Money,IsJb from V_YpKc  "
        ElseIf Formlb = "药房信息查询" Then
            C1Label1.Text = "红色为已过期药品，橘黄色为一个月内即将过期"
            C1TrueDBGrid1.ContextMenuStrip = Nothing

            Str_Select = "select Dl_Code,Dl_Name,Xx_Code,Yp_Name,Yp_Jc,Mx_Code,Mx_Gg,Mx_Cd,Mx_Gyzz,Yp_Ph,Yp_Yxq,Datediff(day,Getdate(),Yp_Yxq) Alter_Ts, Mx_XsDw as Yp_Dw,Yk_Cgj/Mx_Cfbl as Yp_Cgj,Yk_Pfj/Mx_Cfbl as Yp_Pfj,Yk_Xsj/Mx_Cfbl as Yp_Xsj,Yf_Sl" & CDbl(Mid(YfCode, 5, 2)) & " as Yp_Sl,Yk_Cgj/Mx_Cfbl*Yf_Sl" & CDbl(Mid(YfCode, 5, 2)) & " as Cg_Money,Yk_Pfj/Mx_Cfbl*Yf_Sl" & CDbl(Mid(YfCode, 5, 2)) & " as Pf_Money,Yk_Xsj/Mx_Cfbl*Yf_Sl" & CDbl(Mid(YfCode, 5, 2)) & " as Xs_Money,IsJb from V_YpKc  "

        End If
        With My_Adapter
            .SelectCommand = New SqlCommand(Str_Select, My_Cn)
            .Fill(My_Dataset, "明细")
            .AcceptChangesDuringFill = True
            .MissingSchemaAction = MissingSchemaAction.AddWithKey

        End With

        My_Table = My_Dataset.Tables("明细")
        My_Table.PrimaryKey = New DataColumn() {My_Table.Columns("Xx_Code")}

        With Me.C1TrueDBGrid1
            My_Cm = CType(BindingContext(My_Dataset, "明细"), CurrencyManager)
            .SetDataBinding(My_Dataset, "明细", True)
            T_Label.Text = "∑=" + .Splits(0).Rows.Count.ToString

        End With

        My_View = My_Cm.List
        My_View.Sort = "Isjb,Dl_Code,Yp_Name"



    End Sub

    Private Sub Init_Tree()
        TreeView1.Scrollable = True
        V_Finish = False
        TreeView1.Nodes.Clear()

        '根节点
        Dim My_Root As New TreeNode
        With My_Root
            .Tag = "00"
            .Text = "类别"
            .ImageIndex = 0
        End With
        TreeView1.Nodes.Add(My_Root)

        '一级数据
        Dim My_Reader As SqlDataReader = HisVar.HisVar.Sqldal.ExecuteReader("Select Dl_Code, Dl_Name from Zd_Ml_Yp1")
        While My_Reader.Read()
            Dim My_Node As New TreeNode
            With My_Node
                .Tag = My_Reader.Item(0).ToString
                .Text = My_Reader.Item(1).ToString
                .ImageIndex = 1
                .SelectedImageIndex = 2
            End With
            TreeView1.SelectedNode = My_Root
            TreeView1.SelectedNode.Nodes.Add(My_Node)

        End While
        My_Reader.Close()
        My_Cn.Close()

        With Me.TreeView1
            .SelectedNode = My_Root
            .SelectedNode.Expand()
            .Select()
        End With

        V_Finish = True
        V_SelectNode = "全部"


    End Sub

#End Region

#Region "控件__动作"

    Private Sub Comm_Click(ByVal sender As Object, ByVal e As C1.Win.C1Command.ClickEventArgs) Handles Comm1.Click, Comm3.Click, comm2.Click
        Select Case sender.text

            Case "打印"
                If C1TrueDBGrid1.RowCount = 0 Then Exit Sub
                Dim Stirpt As New StiReport
                If Formlb = "药库信息查询" Then
                    Stirpt.Load(".\Rpt\药库库存表.mrt")
                    Stirpt.ReportName = "药库库存表"
                ElseIf Formlb = "药房信息查询" And HisPara.PublicConfig.XqName = "丰润" Then
                    Stirpt.Load(".\Rpt\药房库存表（带采购价）.mrt")
                    Stirpt.ReportName = YfName & "库存表"
                ElseIf Formlb = "药房信息查询" And HisPara.PublicConfig.XqName <> "丰润" Then
                    Stirpt.Load(".\Rpt\药房库存表.mrt")
                    Stirpt.ReportName = YfName & "库存表"
                End If
             
                Stirpt.RegData(My_View)

                Stirpt.Compile()
                Stirpt("标题") = Stirpt.ReportName
                Stirpt("操作员") = "制表人:" & HisVar.HisVar.JsrName
                Stirpt("打印时间") = Now

                ' Stirpt.Design()
                Stirpt.Show()
            Case "更新"
                My_Table.AcceptChanges()
                My_Adapter.Fill(My_Dataset, "明细")    '刷新记录
                Me.C1TrueDBGrid1.Select()
                Me.C1TrueDBGrid1.MoveFirst()

                Call Init_Tree()
                Call P_Filter()
            Case "盘点手抄单"
                If C1TrueDBGrid1.RowCount = 0 Then Exit Sub
                Dim Stirpt As New StiReport
                Stirpt.Load(".\Rpt\盘点手抄单.mrt")
                If Formlb = "药库信息查询" Then
                    Stirpt.ReportName = "药库盘点手抄单"
                ElseIf Formlb = "药房信息查询" Then
                    Stirpt.ReportName = YfName & "盘点手抄单"
                End If
                'Stirpt.Dictionary.Databases.Clear()
                'Stirpt.RegData(My_View)
                'Stirpt.Dictionary.Synchronize()

                'TryCast(Stirpt.Pages(0).GetComponents.Item("Text16"), StiText).Text = "{view明细.Yf_Sl}"
                'TryCast(Stirpt.Pages(0).GetComponents.Item("Text10"), StiText).Text = "{view明细.Mx_XsDw}"
                Stirpt.RegData(My_View)
                Stirpt.Compile()
                Stirpt("打印时间") = Format(Now, "yyyy-MM-dd HH:mm:ss")
                Stirpt("标题") = Stirpt.ReportName
                ' Stirpt.Design()
                Stirpt.Show()
            Case "导出EXCEL"
                Try
                    With Me.SaveFileDialog1
                        .Filter = "Excel文件(.Xls)|*.Xls"
                        .FileName = HisVar.HisVar.WsyName + Me.Text + "库存信息" & Format(Now, "yyMMdd") & ".Xls"
                        If .ShowDialog = DialogResult.Cancel Then
                            Exit Sub
                        End If
                        .AddExtension = True
                    End With
                    Dim dc As C1.Win.C1TrueDBGrid.C1DisplayColumn
                    For Each dc In C1TrueDBGrid1.Splits(0).DisplayColumns
                        dc.Merge = C1.Win.C1TrueDBGrid.ColumnMergeEnum.None
                    Next
                    C1TrueDBGrid1.ExportToExcel(SaveFileDialog1.FileName, False)

                Catch
                    MsgBox("Excel表生成失败,请重新生成", MsgBoxStyle.Critical, "警告")
                    Exit Sub
                End Try
                MessageBox.Show("Excel导出完毕！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
        End Select

    End Sub



    Private Sub C1TextBox1_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles T_Textbox.TextChanged
        If V_Finish = True Then Call P_Filter()

        Call F_Sum()
    End Sub


#Region "Treeview 动作"


    Private Sub TreeView1_AfterSelect(ByVal sender As System.Object, ByVal e As System.Windows.Forms.TreeViewEventArgs) Handles TreeView1.AfterSelect

        If V_Finish = False Then Exit Sub
        V_SelectCode = Trim(e.Node.Tag)     '获取选中节点

        If e.Node.Tag = "00" Then
            V_SelectNode = "全部"
        Else
            V_SelectNode = "大类"
        End If
        Call P_Filter()                     '条件过滤
        Call F_Sum()
    End Sub

    Private Sub TreeView1_BeforeCollapse(ByVal sender As Object, ByVal e As System.Windows.Forms.TreeViewCancelEventArgs) Handles TreeView1.BeforeCollapse
        '如果为TopNode不进行折叠
        If e.Node.Tag = TreeView1.TopNode.Tag Then e.Cancel = True

    End Sub

#End Region


    Private Sub C1TrueDBGrid1_FetchRowStyle(ByVal sender As System.Object, ByVal e As C1.Win.C1TrueDBGrid.FetchRowStyleEventArgs) Handles C1TrueDBGrid1.FetchRowStyle
        If C1TrueDBGrid1.RowCount = 0 Then Exit Sub

        If Me.C1TrueDBGrid1.Columns("Alter_Ts").CellText(C1TrueDBGrid1.RowBookmark(e.Row)) <= 0 Then
            e.CellStyle.BackColor = Color.FromArgb(251, 190, 206)
        ElseIf Me.C1TrueDBGrid1.Columns("Alter_Ts").CellText(C1TrueDBGrid1.RowBookmark(e.Row)) <= 30 And Me.C1TrueDBGrid1.Columns("Alter_Ts").CellText(C1TrueDBGrid1.RowBookmark(e.Row)) > 0 Then
            e.CellStyle.BackColor = Color.Orange
        ElseIf Me.C1TrueDBGrid1.Columns("Alter_Ts").CellText(C1TrueDBGrid1.RowBookmark(e.Row)) <= 60 And Me.C1TrueDBGrid1.Columns("Alter_Ts").CellText(C1TrueDBGrid1.RowBookmark(e.Row)) > 30 Then
            'e.CellStyle.BackColor = Color.Yellow
        ElseIf Me.C1TrueDBGrid1.Columns("Alter_Ts").CellText(C1TrueDBGrid1.RowBookmark(e.Row)) <= 90 And Me.C1TrueDBGrid1.Columns("Alter_Ts").CellText(C1TrueDBGrid1.RowBookmark(e.Row)) > 60 Then
            'e.CellStyle.BackColor = Color.FromArgb(154, 223, 252)
        End If



    End Sub

#End Region

#Region "自定义函数"

    Private Sub P_Filter()

        Dim V_Str As String = ""
        Select Case V_SelectNode
            Case "全部"
                If CheckBox1.Checked = True Then
                    If Trim(T_Textbox.Text & "") = "" Then
                        V_Str = ""
                    Else

                        V_Str = "Yp_Jc Like '*" & Trim(T_Textbox.Text) & "*' or Yp_Name Like '*" & Trim(T_Textbox.Text) & "*'"
                    End If

                Else
                    If Trim(T_Textbox.Text & "") = "" Then
                        V_Str = "Yp_Sl <> 0"
                    Else
                        V_Str = "Yp_Sl <> 0 and   (Yp_Jc Like '*" & Trim(T_Textbox.Text) & "*' or Yp_Name Like '*" & Trim(T_Textbox.Text) & "*') "
                    End If
                End If

            Case "大类"
                If CheckBox1.Checked = True Then
                    If Trim(T_Textbox.Text & "") = "" Then
                        V_Str = "Dl_Code='" & V_SelectCode & "'"
                    Else

                        V_Str = "Dl_Code='" & V_SelectCode & "' And (Yp_Jc Like '*" & Trim(T_Textbox.Text) & "*' or Yp_Name Like '*" & Trim(T_Textbox.Text) & "*')"
                    End If
                Else
                    If Trim(T_Textbox.Text & "") = "" Then
                        V_Str = "Dl_Code='" & V_SelectCode & "' and Yp_Sl<>0"
                    Else

                        V_Str = "Dl_Code='" & V_SelectCode & "' And (Yp_Jc Like '*" & Trim(T_Textbox.Text) & "*' or Yp_Name Like '*" & Trim(T_Textbox.Text) & "*') and Yp_Sl <> 0 "
                    End If
                End If

        End Select

        My_View.RowFilter = V_Str
        C1TrueDBGrid1.MoveFirst()
        T_Label.Text = "∑=" + C1TrueDBGrid1.Splits(0).Rows.Count.ToString

    End Sub

    Private Sub CheckBox1_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox1.CheckedChanged
        If My_Dataset.Tables("明细") IsNot Nothing Then

            Call P_Filter()
        End If

    End Sub
#End Region


    Public Overrides Sub F_Sum()
        Dim Sum1 As Double = 0
        Dim Sum2 As Double = 0
        Dim Sum3 As Double = 0
        Sum1 = IIf(My_View.Table.Compute("Sum(Cg_Money)", My_View.RowFilter) Is DBNull.Value, 0, My_View.Table.Compute("Sum(Cg_Money)", My_View.RowFilter))
        Sum2 = IIf(My_View.Table.Compute("Sum(Pf_Money)", My_View.RowFilter) Is DBNull.Value, 0, My_View.Table.Compute("Sum(Pf_Money)", My_View.RowFilter))
        Sum3 = IIf(My_View.Table.Compute("Sum(Xs_Money)", My_View.RowFilter) Is DBNull.Value, 0, My_View.Table.Compute("Sum(Xs_Money)", My_View.RowFilter))


        With C1TrueDBGrid1
            .ColumnFooters = True
            If Formlb = "药库信息查询" Then
                .Columns("Cg_Money").FooterText = Format(Sum1, "###0.00")
                .Columns("Pf_Money").FooterText = Format(Sum2, "###0.00")
            ElseIf Formlb = "药房信息查询" And HisPara.PublicConfig.XqName = "丰润" Then
                .Columns("Cg_Money").FooterText = Format(Sum1, "###0.00")
            End If
          
            .Columns("Xs_Money").FooterText = Format(Sum3, "###0.00")
        End With
    


    End Sub

    Private Sub ToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ToolStripMenuItem.Click
        If C1TrueDBGrid1.RowCount <> 0 Then
            My_Row = My_Cm.List(C1TrueDBGrid1.Row).Row
            Dim vform As New Yp_Xx(My_Row, My_Dataset.Tables("明细"))
            vform.ShowDialog()
        End If
    End Sub


  
End Class
