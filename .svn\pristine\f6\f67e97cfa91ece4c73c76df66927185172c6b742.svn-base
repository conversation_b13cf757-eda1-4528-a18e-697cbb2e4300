﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ERX.Model
{
    /// <summary>
    /// 电子处方撤销
    /// </summary>
    public class MdlrxUndoIn
    {
        public string hiRxno { get; set; }   //医保处方编号 
        public string fixmedinsCode { get; set; }   //定点医疗机构编号
        public string drCode { get; set; }   //撤销医师的医保医师代码
        public string undoDrName { get; set; }   //撤销医师姓名 
        public string undoDrCertType { get; set; }   //撤销医师证件类型
        public string undoDrCertno { get; set; }   //撤销医师证件号码 
        public string undoRea { get; set; }   //撤销原因描述 
        public string undoTime { get; set; }   //撤销时间


    }
    public class MdlrxUndoOut
    {
        public string hiRxno { get; set; }   //医保处方编号 
        public string rxStasCodg { get; set; }   //医保处方状态编码 
        public string rxStasName { get; set; }   //医保处方状态名称

    }
}
