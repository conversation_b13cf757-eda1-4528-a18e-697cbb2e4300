﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="0" />
    <DataSources isList="true" count="1">
      <药品入库情况汇总表 Ref="2" type="DataTableSource" isKey="true">
        <Alias>药品入库情况汇总表</Alias>
        <Columns isList="true" count="7">
          <value>Yy_Name,System.String</value>
          <value>Lb,System.String</value>
          <value>Sl,System.Decimal</value>
          <value>S_Money,System.Decimal</value>
          <value>S_Lb,System.String</value>
          <value>lb,System.String</value>
          <value>sl,System.Decimal</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>药品入库情况汇总表</Name>
        <NameInSource>药品入库情况汇总表</NameInSource>
      </药品入库情况汇总表>
    </DataSources>
    <Relations isList="true" count="0" />
    <Report isRef="0" />
    <Variables isList="true" count="1">
      <value>,统计时间,统计时间,System.String,,False,False</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="3" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="1">
        <ReportTitleBand1 Ref="4" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,27.7,1.6</ClientRectangle>
          <Components isList="true" count="4">
            <Text1 Ref="5" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,27.6,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>黑体,15.75,Regular,Point,False,134</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text1</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>药 品 入 库 情 况 汇 总 表</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text1>
            <Text2 Ref="6" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.6,1,12.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,0</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text2</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>{统计时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text2>
            <Text3 Ref="7" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>15.4,1,9.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,0</Font>
              <Guid>58b29f0b5f694fefa084ddd773fc6cda</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text3</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>单位：元</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text3>
            <CrossTab1 Ref="8" type="Stimulsoft.Report.CrossTab.StiCrossTab" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.6,27.6,5.6</ClientRectangle>
              <Components isList="true" count="13">
                <CrossTab1_RowTotal1 Ref="9" type="CrossRowTotal" isKey="true">
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>[255:255:255]</Brush>
                  <ClientRectangle>0,3.1,2.6,0</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Enabled>False</Enabled>
                  <Font>宋体,10.5,Regular,Point,False,0</Font>
                  <Guid>9e3879a93bc44470b92e103d67a68b49</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_RowTotal1</Name>
                  <Page isRef="3" />
                  <Parent isRef="8" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <Text>Total</Text>
                  <TextBrush>Black</TextBrush>
                </CrossTab1_RowTotal1>
                <CrossTab1_Row1_Title Ref="10" type="CrossTitle" isKey="true">
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>White</Brush>
                  <ClientRectangle>0,0.65,2.6,1.2</ClientRectangle>
                  <Font>宋体,10.5,Regular,Point,False,0</Font>
                  <Guid>074a1a3ddfda4f46a29baecbb24b5571</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_Row1_Title</Name>
                  <Page isRef="3" />
                  <Parent isRef="8" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <Text>医院名称</Text>
                  <TextBrush>[105:105:105]</TextBrush>
                  <Type>Expression</Type>
                  <TypeOfComponent>Row:CrossTab1_Row1</TypeOfComponent>
                </CrossTab1_Row1_Title>
                <CrossTab1_ColTotal1 Ref="11" type="CrossColumnTotal" isKey="true">
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>[255:255:255]</Brush>
                  <ClientRectangle>4.65,0.65,0,1.2</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>宋体,10.5,Regular,Point,False,0</Font>
                  <Guid>fa14d8aa8e1346019d0032166a76aed8</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_ColTotal1</Name>
                  <Page isRef="3" />
                  <Parent isRef="8" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <Text>总计</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                </CrossTab1_ColTotal1>
                <CrossTab1_LeftTitle Ref="12" type="CrossTitle" isKey="true">
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>White</Brush>
                  <ClientRectangle>0,0,2.6,0.6</ClientRectangle>
                  <Enabled>False</Enabled>
                  <Font>Arial,8</Font>
                  <Guid>0e346e1d7ac54802abd744c33ad477dc</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_LeftTitle</Name>
                  <Page isRef="3" />
                  <Parent isRef="8" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <TextBrush>[105:105:105]</TextBrush>
                  <Type>Expression</Type>
                  <TypeOfComponent>LeftTitle</TypeOfComponent>
                </CrossTab1_LeftTitle>
                <CrossTab1_ColTotal2 Ref="13" type="CrossColumnTotal" isKey="true">
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>[255:255:255]</Brush>
                  <ClientRectangle>3.65,1.25,1,0.6</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>宋体,10.5,Regular,Point,False,0</Font>
                  <Guid>321d9dfff0de49bb877e9da62720597f</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_ColTotal2</Name>
                  <Page isRef="3" />
                  <Parent isRef="8" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <Text>合计</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                </CrossTab1_ColTotal2>
                <CrossTab1_SumHeader1 Ref="14" type="Stimulsoft.Report.CrossTab.StiCrossSummaryHeader" isKey="true">
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>White</Brush>
                  <ClientRectangle>1.6,1.9,1,0.6</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>宋体,10.5,Regular,Point,False,0</Font>
                  <Guid>37e86a1467184cfd833c73cef4b77815</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_SumHeader1</Name>
                  <Page isRef="3" />
                  <Parent isRef="8" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <Text>数量</Text>
                  <TextBrush>[105:105:105]</TextBrush>
                  <Type>Expression</Type>
                </CrossTab1_SumHeader1>
                <CrossTab1_SumHeader2 Ref="15" type="Stimulsoft.Report.CrossTab.StiCrossSummaryHeader" isKey="true">
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>White</Brush>
                  <ClientRectangle>1.6,2.5,1,0.6</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>宋体,10.5,Regular,Point,False,0</Font>
                  <Guid>75799fa5396f40f480eb7456d9e5e6dc</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_SumHeader2</Name>
                  <Page isRef="3" />
                  <Parent isRef="8" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <Text>金额</Text>
                  <TextBrush>[105:105:105]</TextBrush>
                  <Type>Expression</Type>
                </CrossTab1_SumHeader2>
                <CrossTab1_Row1 Ref="16" type="CrossRow" isKey="true">
                  <Alias>Yy_Name</Alias>
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>White</Brush>
                  <ClientRectangle>0,1.9,1.6,1.2</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <DisplayValue>{药品入库情况汇总表.Yy_Name}</DisplayValue>
                  <Font>宋体,10.5,Regular,Point,False,0</Font>
                  <Guid>a80efe87a7b449dd86e4609472639552</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_Row1</Name>
                  <Page isRef="3" />
                  <Parent isRef="8" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <ShowTotal>False</ShowTotal>
                  <Text>Yy_Name</Text>
                  <TextBrush>[105:105:105]</TextBrush>
                  <TotalGuid>9e3879a93bc44470b92e103d67a68b49</TotalGuid>
                  <Value>{药品入库情况汇总表.Yy_Name}</Value>
                </CrossTab1_Row1>
                <CrossTab1_Column1 Ref="17" type="CrossColumn" isKey="true">
                  <Alias>lb</Alias>
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>White</Brush>
                  <ClientRectangle>2.65,0.65,2,0.6</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <DisplayValue>{药品入库情况汇总表.lb}</DisplayValue>
                  <Font>宋体,10.5,Regular,Point,False,0</Font>
                  <Guid>89d3d308ec604323b0ae0c973eae1c6e</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_Column1</Name>
                  <Page isRef="3" />
                  <Parent isRef="8" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <ShowTotal>False</ShowTotal>
                  <Text>lb</Text>
                  <TextBrush>[105:105:105]</TextBrush>
                  <TotalGuid>fa14d8aa8e1346019d0032166a76aed8</TotalGuid>
                  <Value>{药品入库情况汇总表.lb}</Value>
                </CrossTab1_Column1>
                <CrossTab1_Column2 Ref="18" type="CrossColumn" isKey="true">
                  <Alias>S_Lb</Alias>
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>White</Brush>
                  <ClientRectangle>2.65,1.25,1,0.6</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <DisplayValue>{药品入库情况汇总表.S_Lb}</DisplayValue>
                  <Font>宋体,10.5,Regular,Point,False,0</Font>
                  <Guid>b00a81f232f644839f747811aac0d018</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_Column2</Name>
                  <Page isRef="3" />
                  <Parent isRef="8" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <Text>S_Lb</Text>
                  <TextBrush>[105:105:105]</TextBrush>
                  <TotalGuid>321d9dfff0de49bb877e9da62720597f</TotalGuid>
                  <Value>{药品入库情况汇总表.S_Lb}</Value>
                </CrossTab1_Column2>
                <CrossTab1_Sum1 Ref="19" type="CrossSummary" isKey="true">
                  <Alias>sl</Alias>
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>[255:255:255]</Brush>
                  <ClientRectangle>2.65,1.9,1,0.6</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>宋体,10.5,Regular,Point,False,0</Font>
                  <Guid>345a53aa9a7247b394a269e16e17321a</Guid>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_Sum1</Name>
                  <Page isRef="3" />
                  <Parent isRef="8" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <Text>0</Text>
                  <TextBrush>Black</TextBrush>
                  <TextFormat Ref="20" type="CustomFormat" isKey="true">
                    <StringFormat>##00.##</StringFormat>
                  </TextFormat>
                  <Value>{药品入库情况汇总表.sl}</Value>
                </CrossTab1_Sum1>
                <CrossTab1_Sum2 Ref="21" type="CrossSummary" isKey="true">
                  <Alias>S_Money</Alias>
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>[255:255:255]</Brush>
                  <ClientRectangle>2.65,2.5,1,0.6</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>宋体,10.5,Regular,Point,False,0</Font>
                  <Guid>e8e63db77fb64bd988463c37a9e4818d</Guid>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_Sum2</Name>
                  <Page isRef="3" />
                  <Parent isRef="8" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <Text>0</Text>
                  <TextBrush>Black</TextBrush>
                  <Value>{药品入库情况汇总表.S_Money}</Value>
                </CrossTab1_Sum2>
                <CrossTab1_RightTitle Ref="22" type="CrossTitle" isKey="true">
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>White</Brush>
                  <ClientRectangle>2.65,0,2,0.6</ClientRectangle>
                  <Enabled>False</Enabled>
                  <Font>宋体,10.5,Regular,Point,False,0</Font>
                  <Guid>2a2aa776342846aea9d7cabde4293787</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_RightTitle</Name>
                  <Page isRef="3" />
                  <Parent isRef="8" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <Text>lb, S_Lb</Text>
                  <TextBrush>[105:105:105]</TextBrush>
                  <TypeOfComponent>RightTitle</TypeOfComponent>
                </CrossTab1_RightTitle>
              </Components>
              <Conditions isList="true" count="0" />
              <DataRelationName />
              <DataSourceName>药品入库情况汇总表</DataSourceName>
              <EmptyValue />
              <Filters isList="true" count="0" />
              <HorAlignment>Width</HorAlignment>
              <Name>CrossTab1</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Sort isList="true" count="0" />
            </CrossTab1>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>ReportTitleBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </ReportTitleBand1>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>140e719a1fad4b49bcde72b3c70dd461</Guid>
      <Margins>1,1,1,1</Margins>
      <Name>Page1</Name>
      <Orientation>Landscape</Orientation>
      <PageHeight>21</PageHeight>
      <PageWidth>29.7</PageWidth>
      <Report isRef="0" />
      <Watermark Ref="23" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page1>
  </Pages>
  <PrinterSettings Ref="24" type="Stimulsoft.Report.Print.StiPrinterSettings" isKey="true" />
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>药品入库情况汇总表</ReportAlias>
  <ReportChanged>7/31/2012 3:34:04 PM</ReportChanged>
  <ReportCreated>7/13/2012 3:39:50 PM</ReportCreated>
  <ReportFile>Rpt\药品入库情况汇总表.mrt</ReportFile>
  <ReportGuid>154212b7eba849ae94fc658b52405b64</ReportGuid>
  <ReportName>药品入库情况汇总表</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2011.2.1026</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class Report : Stimulsoft.Report.StiReport
    {
        public Report()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
        #endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>