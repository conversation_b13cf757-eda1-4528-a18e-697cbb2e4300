﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class MBISCondition
    Inherits HisControl.BaseChild

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel()
        Me.CXZTSingleComobo = New CustomControl.MySingleComobo()
        Me.JSRDtComobo = New CustomControl.MyDtComobo()
        Me.GGTextBox = New CustomControl.MyTextBox()
        Me.GYSDtComobo = New CustomControl.MyDtComobo()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.MyButton1 = New CustomControl.MyButton()
        Me.QXButton = New CustomControl.MyButton()
        Me.QDButton = New CustomControl.MyButton()
        Me.KFDtComobo = New CustomControl.MyDtComobo()
        Me.WZMCDtComobo = New CustomControl.MyDtComobo()
        Me.DJZTSingleComobo = New CustomControl.MySingleComobo()
        Me.WZPHTextBox1 = New CustomControl.MyTextBox()
        Me.dhCheckBox1 = New System.Windows.Forms.CheckBox()
        Me.dahCheckBox2 = New System.Windows.Forms.CheckBox()
        Me.lrCheckBox3 = New System.Windows.Forms.CheckBox()
        Me.wcCheckBox4 = New System.Windows.Forms.CheckBox()
        Me.DHDoubleDateEdit = New CustomControl.DoubleDateEdit()
        Me.DAHDoubleDateEdit = New CustomControl.DoubleDateEdit()
        Me.LrDoubleDateEdit = New CustomControl.DoubleDateEdit()
        Me.WCDoubleDateEdit = New CustomControl.DoubleDateEdit()
        Me.SCCJTextBox1 = New CustomControl.MyTextBox()
        Me.WZLBDtComobo1 = New CustomControl.MyDtComobo()
        Me.TableLayoutPanel1.SuspendLayout()
        Me.Panel1.SuspendLayout()
        Me.SuspendLayout()
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.ColumnCount = 6
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 8.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 20.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 50.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 50.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 20.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 9.0!))
        Me.TableLayoutPanel1.Controls.Add(Me.CXZTSingleComobo, 3, 7)
        Me.TableLayoutPanel1.Controls.Add(Me.JSRDtComobo, 3, 6)
        Me.TableLayoutPanel1.Controls.Add(Me.GGTextBox, 3, 8)
        Me.TableLayoutPanel1.Controls.Add(Me.GYSDtComobo, 3, 5)
        Me.TableLayoutPanel1.Controls.Add(Me.Panel1, 2, 10)
        Me.TableLayoutPanel1.Controls.Add(Me.KFDtComobo, 1, 5)
        Me.TableLayoutPanel1.Controls.Add(Me.WZMCDtComobo, 1, 6)
        Me.TableLayoutPanel1.Controls.Add(Me.DJZTSingleComobo, 1, 7)
        Me.TableLayoutPanel1.Controls.Add(Me.WZPHTextBox1, 1, 8)
        Me.TableLayoutPanel1.Controls.Add(Me.dhCheckBox1, 1, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.dahCheckBox2, 1, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.lrCheckBox3, 1, 3)
        Me.TableLayoutPanel1.Controls.Add(Me.wcCheckBox4, 1, 4)
        Me.TableLayoutPanel1.Controls.Add(Me.DHDoubleDateEdit, 2, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.DAHDoubleDateEdit, 2, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.LrDoubleDateEdit, 2, 3)
        Me.TableLayoutPanel1.Controls.Add(Me.WCDoubleDateEdit, 2, 4)
        Me.TableLayoutPanel1.Controls.Add(Me.SCCJTextBox1, 1, 9)
        Me.TableLayoutPanel1.Controls.Add(Me.WZLBDtComobo1, 3, 9)
        Me.TableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(0, 0)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.RowCount = 12
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 5.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 40.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 5.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 20.0!))
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(512, 299)
        Me.TableLayoutPanel1.TabIndex = 0
        '
        'CXZTSingleComobo
        '
        Me.CXZTSingleComobo.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.CXZTSingleComobo.Captain = "冲销状态"
        Me.CXZTSingleComobo.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.CXZTSingleComobo.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.CXZTSingleComobo.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.CXZTSingleComobo, 2)
        Me.CXZTSingleComobo.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        Me.CXZTSingleComobo.ItemHeight = 16
        Me.CXZTSingleComobo.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.CXZTSingleComobo.Location = New System.Drawing.Point(258, 177)
        Me.CXZTSingleComobo.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.CXZTSingleComobo.MinimumSize = New System.Drawing.Size(0, 20)
        Me.CXZTSingleComobo.Name = "CXZTSingleComobo"
        Me.CXZTSingleComobo.ReadOnly = False
        Me.CXZTSingleComobo.Size = New System.Drawing.Size(241, 20)
        Me.CXZTSingleComobo.TabIndex = 1
        Me.CXZTSingleComobo.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'JSRDtComobo
        '
        Me.JSRDtComobo.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.JSRDtComobo.Captain = "经手人"
        Me.JSRDtComobo.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.JSRDtComobo.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.JSRDtComobo.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.JSRDtComobo, 2)
        Me.JSRDtComobo.DataSource = Nothing
        Me.JSRDtComobo.ItemHeight = 18
        Me.JSRDtComobo.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.JSRDtComobo.Location = New System.Drawing.Point(258, 149)
        Me.JSRDtComobo.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.JSRDtComobo.MinimumSize = New System.Drawing.Size(0, 20)
        Me.JSRDtComobo.Name = "JSRDtComobo"
        Me.JSRDtComobo.ReadOnly = False
        Me.JSRDtComobo.Size = New System.Drawing.Size(241, 20)
        Me.JSRDtComobo.TabIndex = 0
        Me.JSRDtComobo.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'GGTextBox
        '
        Me.GGTextBox.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GGTextBox.Captain = "规    格"
        Me.GGTextBox.CaptainBackColor = System.Drawing.Color.Transparent
        Me.GGTextBox.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.GGTextBox.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.GGTextBox.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.GGTextBox, 2)
        Me.GGTextBox.ContentForeColor = System.Drawing.Color.Black
        Me.GGTextBox.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.GGTextBox.Location = New System.Drawing.Point(258, 205)
        Me.GGTextBox.Multiline = False
        Me.GGTextBox.Name = "GGTextBox"
        Me.GGTextBox.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.GGTextBox.ReadOnly = False
        Me.GGTextBox.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.GGTextBox.SelectionStart = 0
        Me.GGTextBox.SelectStart = 0
        Me.GGTextBox.Size = New System.Drawing.Size(241, 20)
        Me.GGTextBox.TabIndex = 5
        Me.GGTextBox.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.GGTextBox.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.GGTextBox.Watermark = Nothing
        '
        'GYSDtComobo
        '
        Me.GYSDtComobo.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GYSDtComobo.Captain = "供应商"
        Me.GYSDtComobo.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.GYSDtComobo.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.GYSDtComobo.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.GYSDtComobo, 2)
        Me.GYSDtComobo.DataSource = Nothing
        Me.GYSDtComobo.ItemHeight = 18
        Me.GYSDtComobo.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.GYSDtComobo.Location = New System.Drawing.Point(258, 121)
        Me.GYSDtComobo.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.GYSDtComobo.MinimumSize = New System.Drawing.Size(0, 20)
        Me.GYSDtComobo.Name = "GYSDtComobo"
        Me.GYSDtComobo.ReadOnly = False
        Me.GYSDtComobo.Size = New System.Drawing.Size(241, 20)
        Me.GYSDtComobo.TabIndex = 0
        Me.GYSDtComobo.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'Panel1
        '
        Me.TableLayoutPanel1.SetColumnSpan(Me.Panel1, 2)
        Me.Panel1.Controls.Add(Me.MyButton1)
        Me.Panel1.Controls.Add(Me.QXButton)
        Me.Panel1.Controls.Add(Me.QDButton)
        Me.Panel1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel1.Location = New System.Drawing.Point(31, 260)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(448, 34)
        Me.Panel1.TabIndex = 6
        '
        'MyButton1
        '
        Me.MyButton1.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.MyButton1.DialogResult = System.Windows.Forms.DialogResult.None
        Me.MyButton1.Location = New System.Drawing.Point(294, 3)
        Me.MyButton1.Name = "MyButton1"
        Me.MyButton1.Size = New System.Drawing.Size(60, 27)
        Me.MyButton1.TabIndex = 2
        Me.MyButton1.Text = "清空"
        '
        'QXButton
        '
        Me.QXButton.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.QXButton.DialogResult = System.Windows.Forms.DialogResult.Cancel
        Me.QXButton.Location = New System.Drawing.Point(186, 3)
        Me.QXButton.Name = "QXButton"
        Me.QXButton.Size = New System.Drawing.Size(60, 27)
        Me.QXButton.TabIndex = 0
        Me.QXButton.Text = "取消"
        '
        'QDButton
        '
        Me.QDButton.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.QDButton.DialogResult = System.Windows.Forms.DialogResult.OK
        Me.QDButton.Location = New System.Drawing.Point(82, 3)
        Me.QDButton.Name = "QDButton"
        Me.QDButton.Size = New System.Drawing.Size(60, 27)
        Me.QDButton.TabIndex = 0
        Me.QDButton.Text = "确定"
        '
        'KFDtComobo
        '
        Me.KFDtComobo.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.KFDtComobo.Captain = "库    房"
        Me.KFDtComobo.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.KFDtComobo.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.KFDtComobo.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.KFDtComobo, 2)
        Me.KFDtComobo.DataSource = Nothing
        Me.KFDtComobo.ItemHeight = 18
        Me.KFDtComobo.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.KFDtComobo.Location = New System.Drawing.Point(11, 121)
        Me.KFDtComobo.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.KFDtComobo.MinimumSize = New System.Drawing.Size(0, 20)
        Me.KFDtComobo.Name = "KFDtComobo"
        Me.KFDtComobo.ReadOnly = False
        Me.KFDtComobo.Size = New System.Drawing.Size(241, 20)
        Me.KFDtComobo.TabIndex = 1
        Me.KFDtComobo.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'WZMCDtComobo
        '
        Me.WZMCDtComobo.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.WZMCDtComobo.Captain = "物资名称"
        Me.WZMCDtComobo.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.WZMCDtComobo.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.WZMCDtComobo.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.WZMCDtComobo, 2)
        Me.WZMCDtComobo.DataSource = Nothing
        Me.WZMCDtComobo.ItemHeight = 18
        Me.WZMCDtComobo.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.WZMCDtComobo.Location = New System.Drawing.Point(11, 149)
        Me.WZMCDtComobo.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.WZMCDtComobo.MinimumSize = New System.Drawing.Size(0, 20)
        Me.WZMCDtComobo.Name = "WZMCDtComobo"
        Me.WZMCDtComobo.ReadOnly = False
        Me.WZMCDtComobo.Size = New System.Drawing.Size(241, 20)
        Me.WZMCDtComobo.TabIndex = 1
        Me.WZMCDtComobo.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'DJZTSingleComobo
        '
        Me.DJZTSingleComobo.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.DJZTSingleComobo.Captain = "单据状态"
        Me.DJZTSingleComobo.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.DJZTSingleComobo.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.DJZTSingleComobo.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.DJZTSingleComobo, 2)
        Me.DJZTSingleComobo.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        Me.DJZTSingleComobo.ItemHeight = 16
        Me.DJZTSingleComobo.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.DJZTSingleComobo.Location = New System.Drawing.Point(11, 177)
        Me.DJZTSingleComobo.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.DJZTSingleComobo.MinimumSize = New System.Drawing.Size(0, 20)
        Me.DJZTSingleComobo.Name = "DJZTSingleComobo"
        Me.DJZTSingleComobo.ReadOnly = False
        Me.DJZTSingleComobo.Size = New System.Drawing.Size(241, 20)
        Me.DJZTSingleComobo.TabIndex = 1
        Me.DJZTSingleComobo.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'WZPHTextBox1
        '
        Me.WZPHTextBox1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.WZPHTextBox1.Captain = "物资批号"
        Me.WZPHTextBox1.CaptainBackColor = System.Drawing.Color.Transparent
        Me.WZPHTextBox1.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.WZPHTextBox1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.WZPHTextBox1.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.WZPHTextBox1, 2)
        Me.WZPHTextBox1.ContentForeColor = System.Drawing.Color.Black
        Me.WZPHTextBox1.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.WZPHTextBox1.Location = New System.Drawing.Point(11, 205)
        Me.WZPHTextBox1.Multiline = False
        Me.WZPHTextBox1.Name = "WZPHTextBox1"
        Me.WZPHTextBox1.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.WZPHTextBox1.ReadOnly = False
        Me.WZPHTextBox1.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.WZPHTextBox1.SelectionStart = 0
        Me.WZPHTextBox1.SelectStart = 0
        Me.WZPHTextBox1.Size = New System.Drawing.Size(241, 20)
        Me.WZPHTextBox1.TabIndex = 4
        Me.WZPHTextBox1.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.WZPHTextBox1.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.WZPHTextBox1.Watermark = Nothing
        '
        'dhCheckBox1
        '
        Me.dhCheckBox1.AutoSize = True
        Me.dhCheckBox1.Location = New System.Drawing.Point(11, 8)
        Me.dhCheckBox1.Name = "dhCheckBox1"
        Me.dhCheckBox1.Size = New System.Drawing.Size(14, 14)
        Me.dhCheckBox1.TabIndex = 7
        Me.dhCheckBox1.UseVisualStyleBackColor = True
        '
        'dahCheckBox2
        '
        Me.dahCheckBox2.AutoSize = True
        Me.dahCheckBox2.Location = New System.Drawing.Point(11, 36)
        Me.dahCheckBox2.Name = "dahCheckBox2"
        Me.dahCheckBox2.Size = New System.Drawing.Size(14, 14)
        Me.dahCheckBox2.TabIndex = 7
        Me.dahCheckBox2.UseVisualStyleBackColor = True
        '
        'lrCheckBox3
        '
        Me.lrCheckBox3.AutoSize = True
        Me.lrCheckBox3.Location = New System.Drawing.Point(11, 64)
        Me.lrCheckBox3.Name = "lrCheckBox3"
        Me.lrCheckBox3.Size = New System.Drawing.Size(14, 14)
        Me.lrCheckBox3.TabIndex = 7
        Me.lrCheckBox3.UseVisualStyleBackColor = True
        '
        'wcCheckBox4
        '
        Me.wcCheckBox4.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.wcCheckBox4.AutoSize = True
        Me.wcCheckBox4.Location = New System.Drawing.Point(11, 96)
        Me.wcCheckBox4.Name = "wcCheckBox4"
        Me.wcCheckBox4.Size = New System.Drawing.Size(14, 14)
        Me.wcCheckBox4.TabIndex = 7
        Me.wcCheckBox4.UseVisualStyleBackColor = True
        '
        'DHDoubleDateEdit
        '
        Me.DHDoubleDateEdit.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.DHDoubleDateEdit.Captain = "订货时间"
        Me.DHDoubleDateEdit.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.DHDoubleDateEdit, 3)
        Me.DHDoubleDateEdit.Location = New System.Drawing.Point(28, 8)
        Me.DHDoubleDateEdit.Margin = New System.Windows.Forms.Padding(0)
        Me.DHDoubleDateEdit.MaximumSize = New System.Drawing.Size(1000000000, 22)
        Me.DHDoubleDateEdit.MinimumSize = New System.Drawing.Size(0, 22)
        Me.DHDoubleDateEdit.Name = "DHDoubleDateEdit"
        Me.DHDoubleDateEdit.Size = New System.Drawing.Size(474, 22)
        Me.DHDoubleDateEdit.TabIndex = 8
        '
        'DAHDoubleDateEdit
        '
        Me.DAHDoubleDateEdit.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.DAHDoubleDateEdit.Captain = "到货时间"
        Me.DAHDoubleDateEdit.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.DAHDoubleDateEdit, 3)
        Me.DAHDoubleDateEdit.Location = New System.Drawing.Point(28, 36)
        Me.DAHDoubleDateEdit.Margin = New System.Windows.Forms.Padding(0)
        Me.DAHDoubleDateEdit.MaximumSize = New System.Drawing.Size(1000000000, 22)
        Me.DAHDoubleDateEdit.MinimumSize = New System.Drawing.Size(0, 22)
        Me.DAHDoubleDateEdit.Name = "DAHDoubleDateEdit"
        Me.DAHDoubleDateEdit.Size = New System.Drawing.Size(474, 22)
        Me.DAHDoubleDateEdit.TabIndex = 8
        '
        'LrDoubleDateEdit
        '
        Me.LrDoubleDateEdit.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LrDoubleDateEdit.Captain = "录入时间"
        Me.LrDoubleDateEdit.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.LrDoubleDateEdit, 3)
        Me.LrDoubleDateEdit.Location = New System.Drawing.Point(28, 64)
        Me.LrDoubleDateEdit.Margin = New System.Windows.Forms.Padding(0)
        Me.LrDoubleDateEdit.MaximumSize = New System.Drawing.Size(1000000000, 22)
        Me.LrDoubleDateEdit.MinimumSize = New System.Drawing.Size(0, 22)
        Me.LrDoubleDateEdit.Name = "LrDoubleDateEdit"
        Me.LrDoubleDateEdit.Size = New System.Drawing.Size(474, 22)
        Me.LrDoubleDateEdit.TabIndex = 8
        '
        'WCDoubleDateEdit
        '
        Me.WCDoubleDateEdit.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.WCDoubleDateEdit.Captain = "完成时间"
        Me.WCDoubleDateEdit.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.WCDoubleDateEdit, 3)
        Me.WCDoubleDateEdit.Location = New System.Drawing.Point(28, 92)
        Me.WCDoubleDateEdit.Margin = New System.Windows.Forms.Padding(0)
        Me.WCDoubleDateEdit.MaximumSize = New System.Drawing.Size(1000000000, 22)
        Me.WCDoubleDateEdit.MinimumSize = New System.Drawing.Size(0, 22)
        Me.WCDoubleDateEdit.Name = "WCDoubleDateEdit"
        Me.WCDoubleDateEdit.Size = New System.Drawing.Size(474, 22)
        Me.WCDoubleDateEdit.TabIndex = 8
        '
        'SCCJTextBox1
        '
        Me.SCCJTextBox1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SCCJTextBox1.Captain = "生产厂家"
        Me.SCCJTextBox1.CaptainBackColor = System.Drawing.Color.Transparent
        Me.SCCJTextBox1.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.SCCJTextBox1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.SCCJTextBox1.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.SCCJTextBox1, 2)
        Me.SCCJTextBox1.ContentForeColor = System.Drawing.Color.Black
        Me.SCCJTextBox1.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.SCCJTextBox1.Location = New System.Drawing.Point(11, 233)
        Me.SCCJTextBox1.Multiline = False
        Me.SCCJTextBox1.Name = "SCCJTextBox1"
        Me.SCCJTextBox1.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.SCCJTextBox1.ReadOnly = False
        Me.SCCJTextBox1.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.SCCJTextBox1.SelectionStart = 0
        Me.SCCJTextBox1.SelectStart = 0
        Me.SCCJTextBox1.Size = New System.Drawing.Size(241, 20)
        Me.SCCJTextBox1.TabIndex = 4
        Me.SCCJTextBox1.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.SCCJTextBox1.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.SCCJTextBox1.Watermark = Nothing
        '
        'WZLBDtComobo1
        '
        Me.WZLBDtComobo1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.WZLBDtComobo1.Captain = "物资类别"
        Me.WZLBDtComobo1.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.WZLBDtComobo1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.WZLBDtComobo1.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.WZLBDtComobo1, 2)
        Me.WZLBDtComobo1.DataSource = Nothing
        Me.WZLBDtComobo1.ItemHeight = 18
        Me.WZLBDtComobo1.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.WZLBDtComobo1.Location = New System.Drawing.Point(258, 233)
        Me.WZLBDtComobo1.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.WZLBDtComobo1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.WZLBDtComobo1.Name = "WZLBDtComobo1"
        Me.WZLBDtComobo1.ReadOnly = False
        Me.WZLBDtComobo1.Size = New System.Drawing.Size(241, 20)
        Me.WZLBDtComobo1.TabIndex = 9
        Me.WZLBDtComobo1.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'MBISCondition
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(512, 299)
        Me.Controls.Add(Me.TableLayoutPanel1)
        Me.Name = "MBISCondition"
        Me.Text = "物资采购查询条件"
        Me.TableLayoutPanel1.ResumeLayout(False)
        Me.TableLayoutPanel1.PerformLayout()
        Me.Panel1.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents TableLayoutPanel1 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents GYSDtComobo As CustomControl.MyDtComobo
    Friend WithEvents KFDtComobo As CustomControl.MyDtComobo
    Friend WithEvents JSRDtComobo As CustomControl.MyDtComobo
    Friend WithEvents WZMCDtComobo As CustomControl.MyDtComobo
    Friend WithEvents WZPHTextBox1 As CustomControl.MyTextBox
    Friend WithEvents GGTextBox As CustomControl.MyTextBox
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents QXButton As CustomControl.MyButton
    Friend WithEvents QDButton As CustomControl.MyButton
    Friend WithEvents DJZTSingleComobo As CustomControl.MySingleComobo
    Friend WithEvents CXZTSingleComobo As CustomControl.MySingleComobo
    Friend WithEvents dhCheckBox1 As System.Windows.Forms.CheckBox
    Friend WithEvents dahCheckBox2 As System.Windows.Forms.CheckBox
    Friend WithEvents lrCheckBox3 As System.Windows.Forms.CheckBox
    Friend WithEvents wcCheckBox4 As System.Windows.Forms.CheckBox
    Friend WithEvents DHDoubleDateEdit As CustomControl.DoubleDateEdit
    Friend WithEvents DAHDoubleDateEdit As CustomControl.DoubleDateEdit
    Friend WithEvents LrDoubleDateEdit As CustomControl.DoubleDateEdit
    Friend WithEvents WCDoubleDateEdit As CustomControl.DoubleDateEdit
    Friend WithEvents SCCJTextBox1 As CustomControl.MyTextBox
    Friend WithEvents WZLBDtComobo1 As CustomControl.MyDtComobo
    Friend WithEvents MyButton1 As CustomControl.MyButton
End Class
