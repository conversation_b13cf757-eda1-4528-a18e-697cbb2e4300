﻿Imports System.Data.SqlClient
Imports BaseClass


Public Class Zd_MzFp1

#Region "变量初始化"

    Public My_Dataset As New DataSet
    Dim My_View As New DataView                 '视图
    Dim V_Finish As Boolean = False             '初始化完成

    Public My_Adapter1 As New SqlDataAdapter
    Public My_Adapter2 As New SqlDataAdapter
    Public My_Table As New DataTable            '药品小类
    Public My_Cm As CurrencyManager             '同步指针
    Public My_Row As DataRow                    '当前选择行
    Public V_Insert As Boolean                  '增加记录
    Public V_FirstLoad As Boolean               '第一次调入明细表
    Public V_Count As Integer                   '节点数量
#End Region

    Private Sub Zd_MzFp1_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Call Form_Init()
        Call Init_Tree()
        Call Init_Data2()
        Call Init_Data1()
    End Sub

#Region "窗体初始化"

    Private Sub Form_Init()
        Panel1.BorderStyle = BorderStyle.None
        Panel1.Height = 25
        ToolBar1.Location = New Point(1, 1)
        T_Line1.Location = New Point(ToolBar1.Width + 2, 0)
        T_Combo.Location = New Point(T_Line1.Left + 4, 4)
        T_Textbox.Location = New Point(T_Combo.Left + T_Combo.Width, 5)
        T_Line2.Location = New Point(T_Textbox.Left + T_Textbox.Width + 3, 0)
        T_Label.Location = New Point(T_Line2.Left + 2, 2)

        Dim My_Combo As New C_Combo1(Me.T_Combo)
        My_Combo.Init_TDBCombo()
        With T_Combo
            .AddItem("项目简称")
            .AddItem("项目名称")
            .SelectedIndex = 0
        End With
        Call Init_TDBGrid1()
    End Sub
    Private Sub Init_TDBGrid1()
        '初始化TDBGrid
        Dim My_Grid As New C_Grid(Me.C1TrueDBGrid1)
        With My_Grid
            .Init_Grid()
            .P_Dock(C_Grid.Dock.Fill)
            .Init_Column("编码", "Lb_Code", 40, "中", "")
            .Init_Column("项目类别", "Lb_Name", 250, "左", "")
            .Init_Column("类别简称", "Lb_Jc", 120, "左", "")
            .Init_Column("备注", "Lb_Memo", 100, "左", "")
            .AllSort(False)
        End With
    End Sub
    Private Sub Init_TDBGrid2()
        Dim My_Grid As New C_Grid(Me.C1TrueDBGrid1)
        With My_Grid
            .Init_Grid()
            .P_Dock(C_Grid.Dock.Fill)
            .Init_Column("编码", "Xm_Code", 0, "中", "")
            .Init_Column("项目名称", "Xm_Name", 250, "左", "")
            .Init_Column("项目简称", "Xm_Jc", 120, "左", "")
        End With
    End Sub
    Private Sub Init_Tree()
        V_Finish = False

        With Me.TreeView1
            .Nodes.Clear()
            .FullRowSelect = True
            .HideSelection = False
            .HotTracking = True
            .Scrollable = False
            .ShowRootLines = False
            .Sorted = False
        End With

        '根节点
        Dim My_Reader As SqlDataReader
        My_Reader = HisVar.HisVar.Sqldal.ExecuteReader("select count(Xm_Code) as Xm_Count From Zd_MzFp2 where Yy_Code='" & HisVar.HisVar.WsyCode & "' ")
        My_Reader.Read()
        V_Count = My_Reader.Item(0)


        Dim My_Root As New TreeNode
        With My_Root
            .Tag = "0"
            .Text = "项目类别(" + V_Count.ToString + ")"
            .ImageIndex = 0
        End With
        My_Reader.Close()
        TreeView1.Nodes.Add(My_Root)

        '一级数据
        My_Reader = HisVar.HisVar.Sqldal.ExecuteReader("select Zd_MzFp1.Lb_Code,Lb_Name, isnull(Xm_Count,0) as Xm_Count   from Zd_MzFp1 left join (select Lb_Code,  count(*)   as Xm_Count from Zd_MzFp2 Where Yy_Code='" & HisVar.HisVar.WsyCode & "' group by Lb_Code ) Xm_Sl on Zd_MzFp1.Lb_Code=Xm_Sl.Lb_Code")

        While My_Reader.Read()
            Dim My_Node As New TreeNode
            With My_Node
                .Tag = My_Reader.Item(0).ToString
                .Text = My_Reader.Item("Lb_Name").ToString + "(" + My_Reader.Item("Xm_Count").ToString + ")"
                .ImageIndex = 1
                .SelectedImageIndex = 2
            End With
            TreeView1.SelectedNode = My_Root
            TreeView1.SelectedNode.Nodes.Add(My_Node)
        End While
        My_Reader.Close()
        My_Cn.Close()

        With TreeView1
            .SelectedNode = TreeView1.TopNode
            .SelectedNode.Expand()
            .Select()
        End With
        V_Finish = True

    End Sub

    Private Sub Init_Data1()

        Dim Str_Select As String = "Select Lb_Code,Lb_Name,Lb_Jc,Lb_Memo From Zd_MzFp1 Order By Lb_Code"
        Dim Str_Update As String = "Update Zd_MzFp1 Set Lb_Code=@Lb_Code,Lb_Name=@Lb_Name,Lb_Jc=@Lb_Jc,Lb_Memo=@Lb_Memo Where Lb_Code=@Old_Lb_Code"
        Dim Str_Insert As String = "Insert Into Zd_MzFp1(Lb_Code,Lb_Name,Lb_Jc,Lb_Memo)Values(@Lb_Code,@Lb_Name,@Lb_Jc,@Lb_Memo)"
        Dim Str_Delete As String = "Delete From Zd_MzFp1 Where Lb_Code=@Lb_Code"

        With My_Adapter1
            .UpdateCommand = New SqlCommand(Str_Update, My_Cn)
            With .UpdateCommand.Parameters
                .Add("@Lb_Code", SqlDbType.Char, 2, "Lb_Code")
                .Add("@Lb_Name", SqlDbType.VarChar, 100, "Lb_Name")
                .Add("@Lb_Jc", SqlDbType.VarChar, 50, "Lb_Jc")
                .Add("@Lb_Memo", SqlDbType.VarChar, 200, "Lb_Memo")
                .Add("@Old_Lb_Code", SqlDbType.Char, 2, "Lb_Code")
            End With

            .InsertCommand = New SqlCommand(Str_Insert, My_Cn)
            With .InsertCommand.Parameters
                .Add("@Lb_Code", SqlDbType.Char, 2, "Lb_Code")
                .Add("@Lb_Name", SqlDbType.VarChar, 100, "Lb_Name")
                .Add("@Lb_Jc", SqlDbType.VarChar, 50, "Lb_Jc")
                .Add("@Lb_Memo", SqlDbType.VarChar, 200, "Lb_Memo")
            End With

            .DeleteCommand = New SqlCommand(Str_Delete, My_Cn)
            With .DeleteCommand.Parameters
                .Add("@Lb_Code", SqlDbType.Char, 2, "Lb_Code")
            End With

            .SelectCommand = New SqlCommand(Str_Select, My_Cn)
            .Fill(My_Dataset, "项目类别")

            .AcceptChangesDuringFill = True
            .MissingSchemaAction = MissingSchemaAction.AddWithKey

        End With

        My_Table = My_Dataset.Tables("项目类别")
        My_Table.PrimaryKey = New DataColumn() {My_Table.Columns("Lb_Code")}

        With Me.C1TrueDBGrid1
            My_Cm = CType(BindingContext(My_Dataset, "项目类别"), CurrencyManager)
            .SetDataBinding(My_Dataset, "项目类别", True)

            T_Label.Text = "∑=" + .Splits(0).Rows.Count.ToString
        End With

        My_View = My_Cm.List
        T_Textbox.Text = ""

    End Sub

    Private Sub Init_Data2()
        Dim Str_Insert As String = "Insert Into Zd_MzFp2(Yy_Code,Lb_Code,Xm_Code)Values(@Yy_Code,@Lb_Code,@Xm_Code)"
        Dim Str_Update As String = "Update Zd_MzFp2 Set Yy_Code=@Yy_Code,Lb_Code=@Lb_Code,Xm_Code=@Xm_Code Where Xm_Code=@Old_Xm_Code and Yy_Code='" & HisVar.HisVar.WsyCode & "'"
        Dim Str_Delete As String = "Delete From Zd_MzFp2 Where Xm_Code=@Xm_Code and Yy_Code='" & HisVar.HisVar.WsyCode & "'"

        With My_Adapter2
            .UpdateCommand = New SqlCommand(Str_Update, My_Cn)
            With .UpdateCommand.Parameters
                .Add("@Yy_Code", SqlDbType.Char, 4)
                .Add("@Lb_Code", SqlDbType.Char, 2)
                .Add("@Xm_Code", SqlDbType.Char, 12)
                .Add("@Old_Xm_Code", SqlDbType.Char, 12, "Xm_Code")
            End With

            .InsertCommand = New SqlCommand(Str_Insert, My_Cn)
            With .InsertCommand.Parameters
                .Add("@Yy_Code", SqlDbType.Char, 4)
                .Add("@Lb_Code", SqlDbType.Char, 2)
                .Add("@Xm_Code", SqlDbType.Char, 12)


            End With

            .DeleteCommand = New SqlCommand(Str_Delete, My_Cn)
            With .DeleteCommand.Parameters
                .Add("@Xm_Code", SqlDbType.Char, 12)
            End With
        End With
    End Sub

    Private Sub P_Init_Data1()
        Dim Str_Select As String = "Select Lb_Code,Lb_Name,Lb_Jc,Lb_Memo From Zd_MzFp1 Order By Lb_Code"

        With My_Adapter1
            .SelectCommand = New SqlCommand(Str_Select, My_Cn)
            If My_Dataset.Tables("项目类别") IsNot Nothing Then My_Dataset.Tables("项目类别").Clear()
            .Fill(My_Dataset, "项目类别")
            .AcceptChangesDuringFill = True
            .MissingSchemaAction = MissingSchemaAction.AddWithKey
        End With

        My_Table = My_Dataset.Tables("项目类别")
        My_Table.PrimaryKey = New DataColumn() {My_Table.Columns("Lb_Code")}

        With Me.C1TrueDBGrid1
            My_Cm = CType(BindingContext(My_Dataset, "项目类别"), CurrencyManager)
            .SetDataBinding(My_Dataset, "项目类别", True)

            My_View = My_Cm.List
            T_Label.Text = "∑=" + .Splits(0).Rows.Count.ToString
        End With
        T_Textbox.Text = ""
        Call P_Filter()
    End Sub

    Private Sub P_Init_Data2(ByVal V_Lb_Code As String, ByVal V_All As Boolean)
        Dim Str_Select As String = ""

        If V_All = True Then
            Str_Select = "Select Zd_MzFp2.Yy_Code,Lb_Code,Zd_MzFp2.Xm_Code,Xm_Name,Xm_Jc From Zd_MzFp2 inner join Zd_Ml_Xm3 on Zd_MzFp2.Xm_Code=Zd_Ml_Xm3.Xm_Code  and Zd_MzFp2.Yy_Code='" & HisVar.HisVar.WsyCode & "' Order By Zd_MzFp2.Xm_Code"
        Else
            Str_Select = "Select Zd_MzFp2.Yy_Code,Lb_Code,Zd_MzFp2.Xm_Code,Xm_Name,Xm_Jc From Zd_MzFp2 inner join Zd_Ml_Xm3 on Zd_MzFp2.Xm_Code=Zd_Ml_Xm3.Xm_Code  and Zd_MzFp2.Yy_Code='" & HisVar.HisVar.WsyCode & "' Where Lb_Code='" & V_Lb_Code & "' And Zd_MzFp2.Yy_Code='" & HisVar.HisVar.WsyCode & "'Order By Zd_MzFp2.Xm_Code"
        End If

        With My_Adapter2
            .SelectCommand = New SqlCommand(Str_Select, My_Cn)
            If My_Dataset.Tables("项目小类") IsNot Nothing Then My_Dataset.Tables("项目小类").Clear()
            .Fill(My_Dataset, "项目小类")
            .AcceptChangesDuringFill = True
            .MissingSchemaAction = MissingSchemaAction.AddWithKey
        End With

        My_Table = My_Dataset.Tables("项目小类")
        My_Table.PrimaryKey = New DataColumn() {My_Table.Columns("Xm_Code")}

        With Me.C1TrueDBGrid1
            My_Cm = CType(BindingContext(My_Dataset, "项目小类"), CurrencyManager)
            .SetDataBinding(My_Dataset, "项目小类", True)

            My_View = My_Cm.List
            T_Label.Text = "∑=" + .Splits(0).Rows.Count.ToString
        End With
        T_Textbox.Text = ""
        Call P_Filter()
    End Sub

    Private Sub Zd_MzFp1_FormClosing(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosingEventArgs) Handles Me.FormClosing
        My_Dataset.Dispose()
    End Sub

#End Region

#Region "控件__动作"

    Private Sub Comm_Click(ByVal sender As Object, ByVal e As C1.Win.C1Command.ClickEventArgs) Handles Comm1.Click, Comm2.Click, Comm3.Click
        Select Case sender.text
            Case "增加"
                Call P_ShowMx("增加")
            Case "删除"
                Beep()
                If C1TrueDBGrid1.Splits(0).Rows.Count = 0 Then Exit Sub
                Call P_Del_Data()

            Case "更新"
                My_Table.AcceptChanges()
                If TreeView1.SelectedNode.Tag = "0" Then
                    My_Adapter1.Fill(My_Dataset, "项目类别")    '刷新记录()
                Else
                    My_Adapter2.Fill(My_Dataset, "项目小类")    '刷新记录
                End If

                C1TrueDBGrid1.Select()
                C1TrueDBGrid1.MoveFirst()
                Call Init_Tree()
                Call Init_TDBGrid1()
                Call Init_Data1()
               
                'Comm1.Enabled = False
                'Comm2.Enabled = False
                Call P_Filter()

        End Select
    End Sub

    Private Sub T_Combo_Close(ByVal sender As Object, ByVal e As System.EventArgs) Handles T_Combo.Close
        T_Textbox.Select()
    End Sub

    Private Sub C1TextBox1_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles T_Textbox.TextChanged
        If V_Finish = True Then Call P_Filter()
    End Sub

    Private Sub C1TrueDBGrid1_MouseUp(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles C1TrueDBGrid1.MouseUp
        If e.Button = MouseButtons.Right Then Call P_ShowMx("DBGrid")
    End Sub

    Private Sub C1TrueDBGrid1_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles C1TrueDBGrid1.KeyDown
        Select Case e.KeyCode
            Case Keys.Return
                Call P_ShowMx("DBGrid")
            Case Keys.Delete
                If Me.C1TrueDBGrid1.RowCount > 0 Then Call P_Del_Data()
            Case Keys.Insert
                If Comm1.Enabled = False Then Exit Sub
                Call P_ShowMx("增加")
        End Select
    End Sub

    Private Sub TreeView1_AfterSelect(ByVal sender As System.Object, ByVal e As System.Windows.Forms.TreeViewEventArgs) Handles TreeView1.AfterSelect
        If V_Finish = False Then Exit Sub '     '初始化完成
        If Me.TreeView1.SelectedNode.Tag = "0" Then
            Call Init_TDBGrid1()
            Call P_Init_Data1()
        Else
            Comm1.Enabled = True
            Comm2.Enabled = True
            Call Init_TDBGrid2()
            Call P_Init_Data2(Me.TreeView1.SelectedNode.Tag, False)
        End If
        Call P_Filter()                         '条件过滤
        If C1TrueDBGrid1.RowCount > 0 Then
            My_Row = My_Cm.List(C1TrueDBGrid1.Row).Row
        End If

    End Sub

    Private Sub TreeView1_BeforeCollapse(ByVal sender As Object, ByVal e As System.Windows.Forms.TreeViewCancelEventArgs) Handles TreeView1.BeforeCollapse
        '如果为TopNode不进行折叠
        If e.Node.Tag = TreeView1.TopNode.Tag Then e.Cancel = True
    End Sub

    Dim m_Rc As New BaseClass.C_RowChange
    Private Sub C1TrueDBGrid1_RowColChange(ByVal sender As System.Object, ByVal e As C1.Win.C1TrueDBGrid.RowColChangeEventArgs) Handles C1TrueDBGrid1.RowColChange
        If (C1TrueDBGrid1.Row + 1) > C1TrueDBGrid1.RowCount Then

        Else
            My_Row = My_Cm.List(C1TrueDBGrid1.Row).Row
            m_Rc.ChangeRow(My_Row)
        End If
    End Sub

#End Region

#Region "自定义函数"

    Private Sub P_ShowMx(ByVal V_Lb As String)
        If V_Lb = "增加" Then
            V_Insert = True
        Else
            If C1TrueDBGrid1.RowCount > 0 Then V_Insert = False Else V_Insert = True
        End If
        If TreeView1.SelectedNode.Tag = "0" Then
            Dim vform As New Zd_MzFp2(V_Insert, My_Row, My_Table, C1TrueDBGrid1, T_Label, My_Adapter1, m_Rc, TreeView1)
            If BaseFunc.BaseFunc.CheckOwnForm(Me, vform) = False Then
                vform.Owner = Me
                vform.Show()
            End If

        Else
            Dim vform As New Zd_MzFp3(V_Insert, My_Row, My_Table, C1TrueDBGrid1, T_Label, My_Adapter2, m_Rc, TreeView1, V_Count)
            If BaseFunc.BaseFunc.CheckOwnForm(Me, vform) = False Then
                vform.Owner = Me
                vform.Show()
            End If
        End If
        C1TrueDBGrid1.Select()

    End Sub

    Private Sub P_Del_Data()
        Beep()
        Dim V_Del_Node As String = ""
        If TreeView1.SelectedNode.Tag = "0" Then
            Dim j As Integer = HisVar.HisVar.Sqldal.GetSingle("Select count(*) from Zd_MzFp2 Where Lb_Code='" & My_Row.Item("Lb_Code") & "'")
            If j > 0 Then
                MsgBox("该类别已使用,请先删除明细信息!", MsgBoxStyle.Information, "提示")
                Exit Sub
            End If

            If MsgBox("是否删除:项目类别=" + Me.C1TrueDBGrid1.Columns("Lb_Name").Value + " ", MsgBoxStyle.Information + MsgBoxStyle.OkCancel, "提示:") = MsgBoxResult.Cancel Then Exit Sub
            My_Row = My_Cm.List(C1TrueDBGrid1.Row).Row
            V_Del_Node = My_Row.Item("Lb_Code")
            My_Adapter1.DeleteCommand.Parameters(0).Value = My_Row.Item("Lb_Code")
        Else
            If MsgBox("是否删除:项目=" + Me.C1TrueDBGrid1.Columns("Xm_Name").Value + " ", MsgBoxStyle.Information + MsgBoxStyle.OkCancel, "提示:") = MsgBoxResult.Cancel Then Exit Sub
            My_Row = My_Cm.List(C1TrueDBGrid1.Row).Row
            My_Adapter2.DeleteCommand.Parameters(0).Value = My_Row.Item("Xm_Code")
        End If
        Call P_Conn(True)
        Try
            If TreeView1.SelectedNode.Tag = "0" Then
                My_Adapter1.DeleteCommand.ExecuteNonQuery()
                C1TrueDBGrid1.Delete()
                My_Row.AcceptChanges()
                T_Label.Text = "∑=" + C1TrueDBGrid1.Splits(0).Rows.Count.ToString
                Call P_Delete_Node(V_Del_Node)
            Else
                My_Adapter2.DeleteCommand.ExecuteNonQuery()
                C1TrueDBGrid1.Delete()
                My_Row.AcceptChanges()
                T_Label.Text = "∑=" + C1TrueDBGrid1.Splits(0).Rows.Count.ToString
                Call P_Del_Tree()
            End If

        Catch ex As Exception
            Beep()
            MsgBox("错误:" + ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
        Finally
            Call P_Conn(False)
            C1TrueDBGrid1.Select()
        End Try

    End Sub

    Private Sub P_Del_Tree()                                                                    '修改Treeview节点
        V_Count = V_Count - 1
        TreeView1.TopNode.Text = "项目类别(" + V_Count.ToString + ")"

        Dim V_NodeText As String = TreeView1.SelectedNode.Text                                  '当前选中的节点
        V_NodeText = Mid(V_NodeText, 1, InStr(V_NodeText, "(") - 1)
        TreeView1.SelectedNode.Text = V_NodeText + "(" + C1TrueDBGrid1.Splits(0).Rows.Count.ToString + ")"
    End Sub

    Private Sub P_Filter()
        Dim V_Str As String = ""
        Select Case TreeView1.SelectedNode.Tag
            Case "0"

                If Trim(T_Textbox.Text & "") = "" Then
                    V_Str = ""
                    My_View.Sort = "Lb_Code"
                Else
                    If T_Combo.Text = "项目名称" Then My_View.Sort = "Lb_Name" : V_Str = "Lb_Name Like '*" & Trim(T_Textbox.Text) & "*'"
                    If T_Combo.Text = "项目简称" Then My_View.Sort = "Lb_Jc  " : V_Str = "Lb_Jc   Like '*" & Trim(T_Textbox.Text) & "*'"
                End If



            Case Else
                If Trim(T_Textbox.Text & "") = "" Then
                    V_Str = " Lb_Code='" & TreeView1.SelectedNode.Tag & "'"
                    My_View.Sort = "Xm_Code"
                Else
                    If T_Combo.Text = "项目名称" Then My_View.Sort = "Xm_Name" : V_Str = "Lb_Code='" & TreeView1.SelectedNode.Tag & "' And Xm_Name Like '*" & Trim(T_Textbox.Text) & "*'"
                    If T_Combo.Text = "项目简称" Then My_View.Sort = "Xm_Jc  " : V_Str = "Lb_Code='" & TreeView1.SelectedNode.Tag & "' And Xm_Jc   Like '*" & Trim(T_Textbox.Text) & "*'"
                End If
        End Select

        My_View.RowFilter = V_Str
        C1TrueDBGrid1.MoveFirst()
        T_Label.Text = "∑=" + C1TrueDBGrid1.Splits(0).Rows.Count.ToString

    End Sub

#End Region

    Private Sub P_Delete_Node(ByVal V_Str As String)  '删除Treeview的节点
        TreeView1.SelectedNode = TreeView1.TopNode()
        Dim My_Node As TreeNode
        For Each My_Node In TreeView1.SelectedNode.Nodes
            If My_Node.Tag = V_Str Then
                TreeView1.Nodes.Remove(My_Node)
                Exit For
            End If
        Next
    End Sub

   


    Private Sub C1Button3_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Button3.Click

        '调入Zd_MzFp11窗体
        If Zd_MzFp11 Is Nothing Then                       '窗体没有调入
            V_FirstLoad = True                        '第一次调入明细表
            Zd_MzFp11.Owner = Me
        End If

        Me.Cursor = Cursors.WaitCursor

        Zd_MzFp11.ShowDialog()
        Me.Cursor = Cursors.Default
    End Sub
End Class