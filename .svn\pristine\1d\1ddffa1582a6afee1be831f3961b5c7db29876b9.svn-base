﻿Imports System.Data.SqlClient
Imports HisControl

Public Class Yk_TjQp2

#Region "定义__变量"
    Dim My_Adapter As New SqlDataAdapter                '从表适配器
    Dim My_Table As New DataTable                       '从表

    Dim V_GridCol As Integer                            '当前TDBGrid所在的列

    Public Cb_Cm As CurrencyManager                     '同步指针
    Public Cb_Row As DataRow                            '当前选择行
    Public V_Tj_Code As String                          '出库编码
    Public V_Insert As Boolean                          '增加记录

    Public Ck3_FirstLoad As Boolean                     '第一次调入明细表

    Dim Str_Select As String
    Public Yp_Lb As String
    Dim My_Dataset As New DataSet
#End Region

#Region "传参"
    Dim Rrow As DataRow
    Dim Rtdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid
#End Region
    Public Sub New(ByVal tform As BaseForm, ByVal trow As DataRow, ByVal ttdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid)

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        Rrow = trow
        Rtdbgrid = ttdbgrid

        ' 在 InitializeComponent() 调用之后添加任何初始化。

    End Sub

    Private Sub Yk_TjQp2_FormClosing(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosingEventArgs) Handles Me.FormClosing
    End Sub

    Private Sub Zd_Dict_Tj2_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Call Form_Init()                '窗体初始化

        Call Zb_Show()                  '显示数据

    End Sub

#Region "窗体__事件"

    Private Sub Form_Init()
        Panel2.Height = 30
        '按扭初始化
        Call P_Comm(Me.Comm1)
        Call P_Comm(Me.Comm2)
        Call P_Comm(Me.Comm3)

        Dim My_Grid As New BaseClass.C_Grid(Me.C1TrueDBGrid1)
        With My_Grid
            .Init_Grid()
            .AllSort(False)
            '.P_MultiSelect(BaseClass.C_Grid.MultiSelect.Extended)
            '.P_MarqueeEnum(BaseClass.C_Grid.MarqueeEnum.FloatingEditor)
            .P_Dock(BaseClass.C_Grid.Dock.Fill)
            .Init_Column("药品名称", "Yp_Name", 300, "左", "")
            .Init_Column("生产厂家", "Mx_Cd", 150, "左", "")
            .Init_Column("规格", "Mx_Gg", 150, "左", "")
            .Init_Column("原药房售价", "Tj_Lsj_Old", 90, "右", "##0.00####")
            .Init_Column("原批发价", "Tj_Pfj_Old", 90, "右", "##0.00####")
            .Init_Column("新药房售价", "Tj_Lsj_New", 90, "右", "##0.00####")
            .Init_Column("新批发价", "Tj_Pfj_New", 90, "右", "##0.00####")
        End With


        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "SELECT Ld_Code,Ld_Jc,Ld_Name,Ld_Zw FROM Zd_YyLd   Order By Ld_Jc", "领导字典", True)

        Dim My_Combo As BaseClass.C_Combo2 = New BaseClass.C_Combo2(C1Combo1, My_Dataset.Tables("领导字典").DefaultView, "Ld_Name", "Ld_Code", 240)
        With My_Combo
            .Init_TDBCombo()
            .Init_Colum("Ld_Code", "领导编码", 0, "左")
            .Init_Colum("Ld_Jc", "领导简称", 60, "左")
            .Init_Colum("Ld_Name", "领导姓名", 100, "左")
            .Init_Colum("Ld_Zw", "领导职位", 60, "左")
            .MaxDropDownItems(8)
            .SelectedIndex(-1)
        End With
        C1Combo1.AutoCompletion = False
        C1Combo1.AutoSelect = False
    End Sub
#End Region

#Region "清空__显示"

    Private Sub Zb_Show()   '显示记录
        With Rrow
            V_Tj_Code = .Item("Tj_Code") & ""
            Me.C1TextBox7.Text = .Item("Tj_Memo")
        End With

        Label12.Text = V_Tj_Code                                                '出库编码
        Call P_Data_Show()
    End Sub

    Private Sub P_Data_Show()   '从表数据
        If My_Dataset.Tables("从表") IsNot Nothing Then
            My_Dataset.Tables("从表").Clear()
        End If
        With My_Adapter
            Str_Select = "Select Yk_Tj2.*,Mx_Gg,Mx_Cd,Yp_Name,Mx_Cfbl  From Yk_Tj2,V_YPkc Where Yk_Tj2.Xx_Code=V_YPkc.Xx_Code  And Tj_Code='" & V_Tj_Code & "'  Order By Tj_Id"
            .SelectCommand = New SqlCommand(Str_Select, My_Cn)
            .Fill(My_Dataset, "从表")

        End With
        My_Table = My_Dataset.Tables("从表")
        My_Dataset.Tables("从表").PrimaryKey = New DataColumn() {My_Dataset.Tables("从表").Columns("Zy_Code"), My_Dataset.Tables("从表").Columns("Xx_Code")}

        '列的唯一性
        Dim My_Column As DataColumn = My_Table.Columns("Tj_Id")
        With My_Column
            .AutoIncrement = True
            .AutoIncrementSeed = .AutoIncrementSeed
            .AutoIncrementStep = 1
        End With

        '主表记录
        Cb_Cm = CType(BindingContext(My_Dataset, "从表"), CurrencyManager)
        C1TrueDBGrid1.SetDataBinding(My_Dataset, "从表", True)


        Call P_Sum()
        C1Combo1.Select()

    End Sub


#End Region

#Region "控件__动作"

#Region "其它__控件"
    Private Sub C1Combo1_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles C1Combo1.KeyUp

        If (e.KeyValue > 47 And e.KeyValue < 58) Or (e.KeyValue > 96 And e.KeyValue < 123) Or (e.KeyValue > 64 And e.KeyValue < 91) Or (e.KeyValue = 8) Or (e.KeyValue = 189) Or (e.KeyValue = 190) Then
            Dim s As String = C1Combo1.Text
            If C1Combo1.Text = "" Then
                C1Combo1.DataSource.RowFilter = ""
            Else
                C1Combo1.DataSource.RowFilter = "Ld_Jc like '*" & C1Combo1.Text.Replace("*", "[*]") & "*'"
            End If
            If (e.KeyValue = 8) Then
                C1Combo1.DroppedDown = False
                C1Combo1.DroppedDown = True
            End If

            C1Combo1.Text = s
            C1Combo1.SelectionStart = C1Combo1.Text.Length
            C1Combo1.SelectionLength = 0
        End If

    End Sub

    Private Sub C1Combo1_KeyDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles C1Combo1.KeyDown
        If e.KeyValue = 13 Then
            If C1Combo1.WillChangeToIndex < 1 Then
                If (CType(C1Combo1.DataSource, DataView).Count) = 0 Then
                    MsgBox("领导: '" + Me.C1Combo1.Text + "' 不存在！", MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
                    System.Windows.Forms.SendKeys.Send("^{A}")
                    Exit Sub
                End If
                C1Combo1.SelectedIndex = -1
                C1Combo1.SelectedIndex = 0
            Else
                Dim index As Integer
                index = C1Combo1.WillChangeToIndex
                C1Combo1.SelectedIndex = -1
                C1Combo1.SelectedIndex = index
            End If
            e.Handled = True
            System.Windows.Forms.SendKeys.Send("{Tab}")
        End If
    End Sub


    Private Sub Comm_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.Click, Comm2.Click, Comm3.Click
        If Trim(C1Combo1.Text) = "" Then MessageBox.Show("请输入签批领导姓名！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Error) : Exit Sub
        Select Case sender.tag
            Case "全部价格修改"
                If MessageBox.Show("确认是否对调价药品进行全部价格修改?", "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Information) = Windows.Forms.DialogResult.OK Then
                    Data_Update("全部价格修改")
                End If
            Case "零售价修改"
                If MessageBox.Show("确认是否仅对调价药品进行零售价修改?", "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Information) = Windows.Forms.DialogResult.OK Then
                    Data_Update("零售价修改")
                End If
            Case "批发价修改"
                If MessageBox.Show("确认是否仅对调价药品进行批发价修改?", "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Information) = Windows.Forms.DialogResult.OK Then
                    Data_Update("批发价修改")
                End If
        End Select

    End Sub
#End Region


#End Region



#Region "自定义函数"
    Private Sub Data_Update(ByVal Lb As String)
        If My_Table Is Nothing Then Exit Sub
        Select Case Lb
            Case "全部价格修改"
                HisVar.HisVar.Sqldal.ExecuteSql("Update Yk_Tj1 Set Qp_Date='" & Format(Now, "yyyy-MM-dd") & "',Qp_Jsr_Code='" & HisVar.HisVar.JsrCode & "',Ld_Code='" & C1Combo1.SelectedValue & "' Where Tj_Code='" & Label12.Text & "'")
                HisVar.HisVar.Sqldal.ExecuteSql("Update Yk_Tj2 Set Tj_Lsj_Ty=1,Tj_Pfj_Ty=1   Where Tj_Code='" & Label12.Text & "'")
                For Each Me.Cb_Row In My_Table.Rows
                    HisVar.HisVar.Sqldal.ExecuteSql("Update Zd_Ml_Yp4 Set Yk_Pfj=" & Cb_Row.Item("Tj_Pfj_New") & ",Yf_Lsj1=" & Cb_Row.Item("Tj_Lsj_New") & ",Yf_Lsj2=" & Cb_Row.Item("Tj_Lsj_New") & ",Yf_Lsj3=" & Cb_Row.Item("Tj_Lsj_New") & ",Yf_Lsj4=" & Cb_Row.Item("Tj_Lsj_New") & ",Yf_Lsj5=" & Cb_Row.Item("Tj_Lsj_New") & ",Yf_Lsj6=" & Cb_Row.Item("Tj_Lsj_New") & ",Yf_Lsj7=" & Cb_Row.Item("Tj_Lsj_New") & ",Yf_Lsj8=" & Cb_Row.Item("Tj_Lsj_New") & ",Yf_Lsj9=" & Cb_Row.Item("Tj_Lsj_New") & ",Yk_Xsj=" & Cb_Row.Item("Mx_CfBl") * Cb_Row.Item("Tj_Lsj_New") & " Where Xx_Code='" & Cb_Row.Item("Xx_Code") & "'")
                Next
            Case "零售价修改"
                HisVar.HisVar.Sqldal.ExecuteSql("Update Yk_Tj1 Set Qp_Date='" & Format(Now, "yyyy-MM-dd") & "',Qp_Jsr_Code='" & HisVar.HisVar.JsrCode & "',Ld_Code='" & C1Combo1.SelectedValue & "' Where Tj_Code='" & Label12.Text & "'")
                HisVar.HisVar.Sqldal.ExecuteSql("Update Yk_Tj2 Set Tj_Lsj_Ty=1   Where Tj_Code='" & Label12.Text & "'")
                For Each Me.Cb_Row In My_Table.Rows

                    HisVar.HisVar.Sqldal.ExecuteSql("Update Zd_Ml_Yp4 Set Yf_Lsj1=" & Cb_Row.Item("Tj_Lsj_New") & ",Yf_Lsj2=" & Cb_Row.Item("Tj_Lsj_New") & ",Yf_Lsj3=" & Cb_Row.Item("Tj_Lsj_New") & ",Yf_Lsj4=" & Cb_Row.Item("Tj_Lsj_New") & ",Yf_Lsj5=" & Cb_Row.Item("Tj_Lsj_New") & ",Yf_Lsj6=" & Cb_Row.Item("Tj_Lsj_New") & ",Yf_Lsj7=" & Cb_Row.Item("Tj_Lsj_New") & ",Yf_Lsj8=" & Cb_Row.Item("Tj_Lsj_New") & ",Yf_Lsj9=" & Cb_Row.Item("Tj_Lsj_New") & ",Yk_Xsj=" & Cb_Row.Item("Mx_CfBl") * Cb_Row.Item("Tj_Lsj_New") & " Where Xx_Code='" & Cb_Row.Item("Xx_Code") & "' ")
                Next
            Case "批发价修改"
                HisVar.HisVar.Sqldal.ExecuteSql("Update Yk_Tj1 Set Qp_Date='" & Format(Now, "yyyy-MM-dd") & "',Qp_Jsr_Code='" & HisVar.HisVar.JsrCode & "',Ld_Code='" & C1Combo1.SelectedValue & "' Where Tj_Code='" & Label12.Text & "'")
                HisVar.HisVar.Sqldal.ExecuteSql("Update Yk_Tj2 Set Tj_Pfj_Ty=1   Where Tj_Code='" & Label12.Text & "'")
                For Each Me.Cb_Row In My_Table.Rows

                    HisVar.HisVar.Sqldal.ExecuteSql("Update Zd_Ml_Yp4 Set  Yk_Pfj=" & Cb_Row.Item("Tj_Pfj_New") & " Where Xx_Code='" & Cb_Row.Item("Xx_Code") & "'  ")
                Next
        End Select
        Rrow.Delete()

        MessageBox.Show("已调价成功,请继续其他操作!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Me.Close()
    End Sub
    Private Sub P_Sum()
        If My_Table.Rows.Count = 0 Then
            T_Label5.Text = "0条"
        Else
            T_Label5.Text = Trim(My_Table.Rows.Count & "条")
        End If
        T_Label5.Location = New Point(Me.Width - T_Label5.Width - 7, 8)
        T_Label4.Location = New Point(Me.Width - T_Label5.Width - T_Label4.Width - 7, 8)
    End Sub

#End Region

#Region "鼠标__外观"
    Private Sub P_Comm(ByVal Bt As Button)
        With Bt
            Select Case .Tag
                Case "全部价格修改"
                    .Location = New Point(T_Line1.Left + 10, 4)
                    .BackgroundImage = MyResources.C_Resources.getimage("全部价格修改1")
                    .Text = "                      &A"
                Case "药房价格修改"
                    .Location = New Point(Comm1.Left + Comm1.Width + 4, 4)
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_药房价格修改1")
                    .Width = Me.Comm1.Width
                    .Text = "                      &F"
                Case "药库价格修改"
                    .Location = New Point(Comm3.Left + Comm3.Width + 4, 4)
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_药库价格修改1")
                    .Width = Me.Comm1.Width
                    .Text = "                      &K"
                    T_Line2.Location = New Point(Me.Comm2.Left + Me.Comm2.Width + 8, 0)
            End Select
            .FlatStyle = FlatStyle.Flat
            .FlatAppearance.BorderSize = 0
            .BackgroundImageLayout = ImageLayout.Center
        End With
    End Sub

    Private Sub Comm_MouseEnter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.MouseEnter, Comm2.MouseEnter, Comm3.MouseEnter
        Select Case sender.tag
            Case "全部价格修改"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("全部价格修改2")
                Comm1.Cursor = Cursors.Hand
            Case "药库价格修改"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_药库价格修改2")
                Comm2.Cursor = Cursors.Hand
            Case "药房价格修改"
                Comm3.BackgroundImage = MyResources.C_Resources.getimage("命令_药房价格修改2")
                Comm3.Cursor = Cursors.Hand
        End Select

    End Sub

    Private Sub Comm_MouseLeave(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.MouseLeave, Comm2.MouseLeave, Comm3.MouseLeave
        Select Case sender.tag
            Case "全部价格修改"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("全部价格修改1")
                Comm1.Cursor = Cursors.Default
            Case "药库价格修改"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_药库价格修改1")
                Comm2.Cursor = Cursors.Default
            Case "药房价格修改"
                Comm3.BackgroundImage = MyResources.C_Resources.getimage("命令_药房价格修改1")
                Comm3.Cursor = Cursors.Default
        End Select
    End Sub

    Private Sub Comm_MouseDown(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm1.MouseDown, Comm2.MouseDown, Comm3.MouseDown
        Select Case sender.tag
            Case "全部价格修改"

                Comm1.BackgroundImage = MyResources.C_Resources.getimage("全部价格修改3")
                Comm1.Cursor = Cursors.Default
            Case "药库价格修改"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_药库价格修改3")
                Comm2.Cursor = Cursors.Default
            Case "药房价格修改"
                Comm3.BackgroundImage = MyResources.C_Resources.getimage("命令_药房价格修改3")
                Comm3.Cursor = Cursors.Default
        End Select
    End Sub

    Private Sub Comm_MouseUp(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm1.MouseUp, Comm2.MouseUp, Comm3.MouseUp
        Select Case sender.tag
            Case "全部价格修改"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("全部价格修改1")
                Comm1.Cursor = Cursors.Hand
            Case "药库价格修改"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_药库价格修改1")
                Comm2.Cursor = Cursors.Hand
            Case "药房价格修改"
                Comm3.BackgroundImage = MyResources.C_Resources.getimage("命令_药房价格修改1")
                Comm3.Cursor = Cursors.Hand
        End Select
    End Sub
#End Region

End Class