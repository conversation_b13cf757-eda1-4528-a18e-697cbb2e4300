﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class Cx_Hzyy1
    Inherits HisControl.BaseChild

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(Cx_Hzyy1))
        Me.Label1 = New System.Windows.Forms.Label()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.DateTimePicker1 = New System.Windows.Forms.DateTimePicker()
        Me.DateTimePicker2 = New System.Windows.Forms.DateTimePicker()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.C1Combo1 = New C1.Win.C1List.C1Combo()
        Me.Label5 = New System.Windows.Forms.Label()
        Me.C1Button1 = New C1.Win.C1Input.C1Button()
        Me.Label6 = New System.Windows.Forms.Label()
        Me.C1Combo2 = New C1.Win.C1List.C1Combo()
        Me.RadioButton1 = New System.Windows.Forms.RadioButton()
        Me.RadioButton2 = New System.Windows.Forms.RadioButton()
        Me.Label8 = New System.Windows.Forms.Label()
        Me.C1Combo3 = New C1.Win.C1List.C1Combo()
        Me.Label11 = New System.Windows.Forms.Label()
        Me.Label12 = New System.Windows.Forms.Label()
        Me.C1Combo5 = New C1.Win.C1List.C1Combo()
        Me.Label10 = New System.Windows.Forms.Label()
        Me.C1Combo4 = New C1.Win.C1List.C1Combo()
        Me.Label7 = New System.Windows.Forms.Label()
        Me.C1Combo6 = New C1.Win.C1List.C1Combo()
        Me.Label9 = New System.Windows.Forms.Label()
        Me.C1Combo7 = New C1.Win.C1List.C1Combo()
        CType(Me.C1Combo1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1Combo2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1Combo3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1Combo5, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1Combo4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1Combo6, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1Combo7, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'Label1
        '
        Me.Label1.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.Label1.Location = New System.Drawing.Point(-2, 45)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(512, 2)
        Me.Label1.TabIndex = 0
        Me.Label1.Text = "0"
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.Font = New System.Drawing.Font("黑体", 15.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Label2.ForeColor = System.Drawing.Color.Maroon
        Me.Label2.Location = New System.Drawing.Point(109, 13)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(229, 20)
        Me.Label2.TabIndex = 1
        Me.Label2.Text = "患者住院用药及服务清单"
        '
        'Label3
        '
        Me.Label3.AutoSize = True
        Me.Label3.Location = New System.Drawing.Point(25, 63)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(53, 12)
        Me.Label3.TabIndex = 2
        Me.Label3.Text = "用药日期"
        '
        'DateTimePicker1
        '
        Me.DateTimePicker1.CustomFormat = "yyyy-MM-dd HH:mm:ss"
        Me.DateTimePicker1.Format = System.Windows.Forms.DateTimePickerFormat.Custom
        Me.DateTimePicker1.Location = New System.Drawing.Point(84, 59)
        Me.DateTimePicker1.Name = "DateTimePicker1"
        Me.DateTimePicker1.Size = New System.Drawing.Size(150, 21)
        Me.DateTimePicker1.TabIndex = 3
        '
        'DateTimePicker2
        '
        Me.DateTimePicker2.CustomFormat = "yyyy-MM-dd HH:mm:ss"
        Me.DateTimePicker2.Format = System.Windows.Forms.DateTimePickerFormat.Custom
        Me.DateTimePicker2.Location = New System.Drawing.Point(295, 59)
        Me.DateTimePicker2.Name = "DateTimePicker2"
        Me.DateTimePicker2.Size = New System.Drawing.Size(150, 21)
        Me.DateTimePicker2.TabIndex = 5
        '
        'Label4
        '
        Me.Label4.AutoSize = True
        Me.Label4.Location = New System.Drawing.Point(257, 63)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(17, 12)
        Me.Label4.TabIndex = 4
        Me.Label4.Text = "至"
        '
        'C1Combo1
        '
        Me.C1Combo1.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.C1Combo1.Caption = ""
        Me.C1Combo1.CaptionHeight = 17
        Me.C1Combo1.CharacterCasing = System.Windows.Forms.CharacterCasing.Normal
        Me.C1Combo1.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.C1Combo1.EditorBackColor = System.Drawing.SystemColors.Window
        Me.C1Combo1.EditorFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.C1Combo1.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.C1Combo1.Images.Add(CType(resources.GetObject("C1Combo1.Images"), System.Drawing.Image))
        Me.C1Combo1.ItemHeight = 15
        Me.C1Combo1.Location = New System.Drawing.Point(84, 113)
        Me.C1Combo1.MatchEntryTimeout = CType(2000, Long)
        Me.C1Combo1.MaxDropDownItems = CType(5, Short)
        Me.C1Combo1.MaxLength = 32767
        Me.C1Combo1.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.C1Combo1.Name = "C1Combo1"
        Me.C1Combo1.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.C1Combo1.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.C1Combo1.Size = New System.Drawing.Size(150, 22)
        Me.C1Combo1.TabIndex = 6
        Me.C1Combo1.PropBag = resources.GetString("C1Combo1.PropBag")
        '
        'Label5
        '
        Me.Label5.AutoSize = True
        Me.Label5.Location = New System.Drawing.Point(24, 117)
        Me.Label5.Name = "Label5"
        Me.Label5.Size = New System.Drawing.Size(53, 12)
        Me.Label5.TabIndex = 7
        Me.Label5.Text = "用药患者"
        '
        'C1Button1
        '
        Me.C1Button1.Location = New System.Drawing.Point(149, 266)
        Me.C1Button1.Name = "C1Button1"
        Me.C1Button1.Size = New System.Drawing.Size(146, 30)
        Me.C1Button1.TabIndex = 8
        Me.C1Button1.Text = "药品及服务项目清单"
        Me.C1Button1.UseVisualStyleBackColor = True
        Me.C1Button1.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.C1Button1.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label6
        '
        Me.Label6.AutoSize = True
        Me.Label6.Location = New System.Drawing.Point(25, 144)
        Me.Label6.Name = "Label6"
        Me.Label6.Size = New System.Drawing.Size(53, 12)
        Me.Label6.TabIndex = 10
        Me.Label6.Text = "查询类型"
        '
        'C1Combo2
        '
        Me.C1Combo2.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.C1Combo2.Caption = ""
        Me.C1Combo2.CaptionHeight = 17
        Me.C1Combo2.CharacterCasing = System.Windows.Forms.CharacterCasing.Normal
        Me.C1Combo2.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.C1Combo2.EditorBackColor = System.Drawing.SystemColors.Window
        Me.C1Combo2.EditorFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.C1Combo2.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.C1Combo2.Images.Add(CType(resources.GetObject("C1Combo2.Images"), System.Drawing.Image))
        Me.C1Combo2.ItemHeight = 15
        Me.C1Combo2.Location = New System.Drawing.Point(84, 139)
        Me.C1Combo2.MatchEntryTimeout = CType(2000, Long)
        Me.C1Combo2.MaxDropDownItems = CType(5, Short)
        Me.C1Combo2.MaxLength = 32767
        Me.C1Combo2.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.C1Combo2.Name = "C1Combo2"
        Me.C1Combo2.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.C1Combo2.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.C1Combo2.Size = New System.Drawing.Size(150, 22)
        Me.C1Combo2.TabIndex = 9
        Me.C1Combo2.PropBag = resources.GetString("C1Combo2.PropBag")
        '
        'RadioButton1
        '
        Me.RadioButton1.AutoSize = True
        Me.RadioButton1.Checked = True
        Me.RadioButton1.Location = New System.Drawing.Point(161, 234)
        Me.RadioButton1.Name = "RadioButton1"
        Me.RadioButton1.Size = New System.Drawing.Size(59, 16)
        Me.RadioButton1.TabIndex = 14
        Me.RadioButton1.TabStop = True
        Me.RadioButton1.Text = "简化版"
        Me.RadioButton1.UseVisualStyleBackColor = True
        '
        'RadioButton2
        '
        Me.RadioButton2.AutoSize = True
        Me.RadioButton2.Location = New System.Drawing.Point(251, 234)
        Me.RadioButton2.Name = "RadioButton2"
        Me.RadioButton2.Size = New System.Drawing.Size(59, 16)
        Me.RadioButton2.TabIndex = 14
        Me.RadioButton2.Text = "标准版"
        Me.RadioButton2.UseVisualStyleBackColor = True
        '
        'Label8
        '
        Me.Label8.AutoSize = True
        Me.Label8.Location = New System.Drawing.Point(25, 91)
        Me.Label8.Name = "Label8"
        Me.Label8.Size = New System.Drawing.Size(53, 12)
        Me.Label8.TabIndex = 16
        Me.Label8.Text = "科室简称"
        '
        'C1Combo3
        '
        Me.C1Combo3.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.C1Combo3.Caption = ""
        Me.C1Combo3.CaptionHeight = 17
        Me.C1Combo3.CharacterCasing = System.Windows.Forms.CharacterCasing.Normal
        Me.C1Combo3.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.C1Combo3.EditorBackColor = System.Drawing.SystemColors.Window
        Me.C1Combo3.EditorFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.C1Combo3.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.C1Combo3.Images.Add(CType(resources.GetObject("C1Combo3.Images"), System.Drawing.Image))
        Me.C1Combo3.ItemHeight = 15
        Me.C1Combo3.Location = New System.Drawing.Point(84, 86)
        Me.C1Combo3.MatchEntryTimeout = CType(2000, Long)
        Me.C1Combo3.MaxDropDownItems = CType(5, Short)
        Me.C1Combo3.MaxLength = 32767
        Me.C1Combo3.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.C1Combo3.Name = "C1Combo3"
        Me.C1Combo3.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.C1Combo3.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.C1Combo3.Size = New System.Drawing.Size(361, 22)
        Me.C1Combo3.TabIndex = 15
        Me.C1Combo3.PropBag = resources.GetString("C1Combo3.PropBag")
        '
        'Label11
        '
        Me.Label11.AutoSize = True
        Me.Label11.ForeColor = System.Drawing.Color.Maroon
        Me.Label11.Location = New System.Drawing.Point(24, 212)
        Me.Label11.Name = "Label11"
        Me.Label11.Size = New System.Drawing.Size(389, 12)
        Me.Label11.TabIndex = 21
        Me.Label11.Text = "提示：如【连续打印一】不能正常打印请尝试用【连续打印二】进行打印"
        '
        'Label12
        '
        Me.Label12.AutoSize = True
        Me.Label12.Location = New System.Drawing.Point(248, 174)
        Me.Label12.Name = "Label12"
        Me.Label12.Size = New System.Drawing.Size(29, 12)
        Me.Label12.TabIndex = 27
        Me.Label12.Text = "字体"
        '
        'C1Combo5
        '
        Me.C1Combo5.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.C1Combo5.Caption = ""
        Me.C1Combo5.CaptionHeight = 17
        Me.C1Combo5.CharacterCasing = System.Windows.Forms.CharacterCasing.Normal
        Me.C1Combo5.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.C1Combo5.EditorBackColor = System.Drawing.SystemColors.Window
        Me.C1Combo5.EditorFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.C1Combo5.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.C1Combo5.Images.Add(CType(resources.GetObject("C1Combo5.Images"), System.Drawing.Image))
        Me.C1Combo5.ItemHeight = 15
        Me.C1Combo5.Location = New System.Drawing.Point(295, 167)
        Me.C1Combo5.MatchEntryTimeout = CType(2000, Long)
        Me.C1Combo5.MaxDropDownItems = CType(5, Short)
        Me.C1Combo5.MaxLength = 32767
        Me.C1Combo5.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.C1Combo5.Name = "C1Combo5"
        Me.C1Combo5.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.C1Combo5.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.C1Combo5.Size = New System.Drawing.Size(150, 22)
        Me.C1Combo5.TabIndex = 26
        Me.C1Combo5.Text = "12.5"
        Me.C1Combo5.PropBag = resources.GetString("C1Combo5.PropBag")
        '
        'Label10
        '
        Me.Label10.AutoSize = True
        Me.Label10.Location = New System.Drawing.Point(25, 172)
        Me.Label10.Name = "Label10"
        Me.Label10.Size = New System.Drawing.Size(53, 12)
        Me.Label10.TabIndex = 25
        Me.Label10.Text = "打印类型"
        '
        'C1Combo4
        '
        Me.C1Combo4.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.C1Combo4.Caption = ""
        Me.C1Combo4.CaptionHeight = 17
        Me.C1Combo4.CharacterCasing = System.Windows.Forms.CharacterCasing.Normal
        Me.C1Combo4.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.C1Combo4.EditorBackColor = System.Drawing.SystemColors.Window
        Me.C1Combo4.EditorFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.C1Combo4.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.C1Combo4.Images.Add(CType(resources.GetObject("C1Combo4.Images"), System.Drawing.Image))
        Me.C1Combo4.ItemHeight = 15
        Me.C1Combo4.Location = New System.Drawing.Point(84, 167)
        Me.C1Combo4.MatchEntryTimeout = CType(2000, Long)
        Me.C1Combo4.MaxDropDownItems = CType(5, Short)
        Me.C1Combo4.MaxLength = 32767
        Me.C1Combo4.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.C1Combo4.Name = "C1Combo4"
        Me.C1Combo4.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.C1Combo4.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.C1Combo4.Size = New System.Drawing.Size(150, 22)
        Me.C1Combo4.TabIndex = 24
        Me.C1Combo4.PropBag = resources.GetString("C1Combo4.PropBag")
        '
        'Label7
        '
        Me.Label7.AutoSize = True
        Me.Label7.Location = New System.Drawing.Point(240, 144)
        Me.Label7.Name = "Label7"
        Me.Label7.Size = New System.Drawing.Size(53, 12)
        Me.Label7.TabIndex = 29
        Me.Label7.Text = "合计方式"
        '
        'C1Combo6
        '
        Me.C1Combo6.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.C1Combo6.Caption = ""
        Me.C1Combo6.CaptionHeight = 17
        Me.C1Combo6.CharacterCasing = System.Windows.Forms.CharacterCasing.Normal
        Me.C1Combo6.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.C1Combo6.EditorBackColor = System.Drawing.SystemColors.Window
        Me.C1Combo6.EditorFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.C1Combo6.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.C1Combo6.Images.Add(CType(resources.GetObject("C1Combo6.Images"), System.Drawing.Image))
        Me.C1Combo6.ItemHeight = 15
        Me.C1Combo6.Location = New System.Drawing.Point(295, 139)
        Me.C1Combo6.MatchEntryTimeout = CType(2000, Long)
        Me.C1Combo6.MaxDropDownItems = CType(5, Short)
        Me.C1Combo6.MaxLength = 32767
        Me.C1Combo6.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.C1Combo6.Name = "C1Combo6"
        Me.C1Combo6.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.C1Combo6.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.C1Combo6.Size = New System.Drawing.Size(150, 22)
        Me.C1Combo6.TabIndex = 28
        Me.C1Combo6.PropBag = resources.GetString("C1Combo6.PropBag")
        '
        'Label9
        '
        Me.Label9.AutoSize = True
        Me.Label9.Location = New System.Drawing.Point(240, 117)
        Me.Label9.Name = "Label9"
        Me.Label9.Size = New System.Drawing.Size(53, 12)
        Me.Label9.TabIndex = 31
        Me.Label9.Text = "患者类型"
        '
        'C1Combo7
        '
        Me.C1Combo7.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.C1Combo7.Caption = ""
        Me.C1Combo7.CaptionHeight = 17
        Me.C1Combo7.CharacterCasing = System.Windows.Forms.CharacterCasing.Normal
        Me.C1Combo7.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.C1Combo7.EditorBackColor = System.Drawing.SystemColors.Window
        Me.C1Combo7.EditorFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.C1Combo7.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.C1Combo7.Images.Add(CType(resources.GetObject("C1Combo7.Images"), System.Drawing.Image))
        Me.C1Combo7.ItemHeight = 15
        Me.C1Combo7.Location = New System.Drawing.Point(295, 112)
        Me.C1Combo7.MatchEntryTimeout = CType(2000, Long)
        Me.C1Combo7.MaxDropDownItems = CType(5, Short)
        Me.C1Combo7.MaxLength = 32767
        Me.C1Combo7.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.C1Combo7.Name = "C1Combo7"
        Me.C1Combo7.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.C1Combo7.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.C1Combo7.Size = New System.Drawing.Size(150, 22)
        Me.C1Combo7.TabIndex = 30
        Me.C1Combo7.PropBag = resources.GetString("C1Combo7.PropBag")
        '
        'Cx_Hzyy1
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(457, 307)
        Me.Controls.Add(Me.Label9)
        Me.Controls.Add(Me.C1Combo7)
        Me.Controls.Add(Me.Label7)
        Me.Controls.Add(Me.C1Combo6)
        Me.Controls.Add(Me.Label12)
        Me.Controls.Add(Me.C1Combo5)
        Me.Controls.Add(Me.Label10)
        Me.Controls.Add(Me.C1Combo4)
        Me.Controls.Add(Me.Label11)
        Me.Controls.Add(Me.Label8)
        Me.Controls.Add(Me.C1Combo3)
        Me.Controls.Add(Me.RadioButton2)
        Me.Controls.Add(Me.RadioButton1)
        Me.Controls.Add(Me.Label6)
        Me.Controls.Add(Me.C1Combo2)
        Me.Controls.Add(Me.C1Button1)
        Me.Controls.Add(Me.Label5)
        Me.Controls.Add(Me.C1Combo1)
        Me.Controls.Add(Me.DateTimePicker2)
        Me.Controls.Add(Me.Label4)
        Me.Controls.Add(Me.DateTimePicker1)
        Me.Controls.Add(Me.Label3)
        Me.Controls.Add(Me.Label2)
        Me.Controls.Add(Me.Label1)
        Me.Icon = CType(resources.GetObject("$this.Icon"), System.Drawing.Icon)
        Me.Name = "Cx_Hzyy1"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "患者住院用药及服务清单"
        CType(Me.C1Combo1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1Combo2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1Combo3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1Combo5, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1Combo4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1Combo6, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1Combo7, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents Label2 As System.Windows.Forms.Label
    Friend WithEvents Label3 As System.Windows.Forms.Label
    Friend WithEvents DateTimePicker1 As System.Windows.Forms.DateTimePicker
    Friend WithEvents DateTimePicker2 As System.Windows.Forms.DateTimePicker
    Friend WithEvents Label4 As System.Windows.Forms.Label
    Friend WithEvents C1Combo1 As C1.Win.C1List.C1Combo
    Friend WithEvents Label5 As System.Windows.Forms.Label
    Friend WithEvents C1Button1 As C1.Win.C1Input.C1Button
    Friend WithEvents Label6 As System.Windows.Forms.Label
    Friend WithEvents C1Combo2 As C1.Win.C1List.C1Combo
    Friend WithEvents RadioButton1 As System.Windows.Forms.RadioButton
    Friend WithEvents RadioButton2 As System.Windows.Forms.RadioButton
    Friend WithEvents Label8 As System.Windows.Forms.Label
    Friend WithEvents C1Combo3 As C1.Win.C1List.C1Combo
    Friend WithEvents Label11 As System.Windows.Forms.Label
    Friend WithEvents Label12 As System.Windows.Forms.Label
    Friend WithEvents C1Combo5 As C1.Win.C1List.C1Combo
    Friend WithEvents Label10 As System.Windows.Forms.Label
    Friend WithEvents C1Combo4 As C1.Win.C1List.C1Combo
    Friend WithEvents Label7 As System.Windows.Forms.Label
    Friend WithEvents C1Combo6 As C1.Win.C1List.C1Combo
    Friend WithEvents Label9 As System.Windows.Forms.Label
    Friend WithEvents C1Combo7 As C1.Win.C1List.C1Combo
End Class
