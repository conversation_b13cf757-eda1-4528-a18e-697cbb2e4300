﻿/**  版本信息模板在安装目录下，可自行修改。
* D_Emr_Bl.cs
*
* 功 能： N/A
* 类 名： D_Emr_Bl
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/8/22 3:39:01   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;

namespace DAL
{
	/// <summary>
	/// 数据访问类:D_Emr_Bl
	/// </summary>
	public partial class D_Emr_Bl
	{
		public D_Emr_Bl()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(long id)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from Emr_Bl");
			strSql.Append(" where id=@id ");
			SqlParameter[] parameters = {
					new SqlParameter("@id", SqlDbType.BigInt,8)			};
			parameters[0].Value = id;

			return HisVar.HisVar.Sqldal.Exists(strSql.ToString(),parameters);
		}
       
        public int GetMaxId()
        {
            return HisVar.HisVar.Sqldal.GetMaxID("id", "Emr_Bl");
        }

        public int GetOneManMaxId(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select max(id) id ");
            strSql.Append(" FROM Emr_Bl");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return  Convert.ToInt32(HisVar.HisVar.Sqldal.Query(strSql.ToString()).Tables[0].Rows[0]["id"].ToString());

            
        }
		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ref ModelOld.M_Emr_Bl model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into Emr_Bl(");
            strSql.Append("id,Bl_Code,Mb_Code,Mb_Name,Mb_Nr,Jsr_Code,Ys_Code,AddDate,Ks_Code,Print_Row,Print_Page)");
			strSql.Append(" values (");
            strSql.Append("@id,@Bl_Code,@Mb_Code,@Mb_Name,@Mb_Nr,@Jsr_Code,@Ys_Code,@AddDate,@Ks_Code,@Print_Row,@Print_Page)");
			SqlParameter[] parameters = {
					new SqlParameter("@id", SqlDbType.BigInt,8),
					new SqlParameter("@Bl_Code", SqlDbType.Char,14),
					new SqlParameter("@Mb_Code", SqlDbType.Char,10),
					new SqlParameter("@Mb_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Mb_Nr", SqlDbType.Image),
					new SqlParameter("@Jsr_Code", SqlDbType.Char,7),
					new SqlParameter("@Ys_Code", SqlDbType.Char,7),
					new SqlParameter("@AddDate", SqlDbType.SmallDateTime),
                    new SqlParameter("@Ks_Code", SqlDbType.Char,7),
                                        new SqlParameter("@Print_Row", SqlDbType.BigInt,8),
                                        new SqlParameter("@Print_Page", SqlDbType.BigInt,8)};
		    model.id = GetMaxId();
            parameters[0].Value = model.id;
			parameters[1].Value = model.Bl_Code;
			parameters[2].Value = model.Mb_Code;
			parameters[3].Value = model.Mb_Name;
			parameters[4].Value = model.Mb_Nr;
			parameters[5].Value = model.Jsr_Code;
			parameters[6].Value = model.Ys_Code;
			parameters[7].Value = model.AddDate;
            parameters[8].Value = model.Ks_Code;
            parameters[9].Value = model.Print_Row;
            parameters[10].Value = model.Print_Page;
			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ModelOld.M_Emr_Bl model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update Emr_Bl set ");
			strSql.Append("Bl_Code=@Bl_Code,");
			strSql.Append("Mb_Code=@Mb_Code,");
			strSql.Append("Mb_Name=@Mb_Name,");
			strSql.Append("Mb_Nr=@Mb_Nr,");
			strSql.Append("Jsr_Code=@Jsr_Code,");
			strSql.Append("Ys_Code=@Ys_Code,");
			strSql.Append("AddDate=@AddDate,");
            strSql.Append("Ks_Code=@Ks_Code,");
            strSql.Append("Print_Row=@Print_Row,");
            strSql.Append("Print_Page=@Print_Page");
			strSql.Append(" where id=@id ");

			SqlParameter[] parameters = {
					new SqlParameter("@Bl_Code", SqlDbType.Char,14),
					new SqlParameter("@Mb_Code", SqlDbType.Char,10),
					new SqlParameter("@Mb_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Mb_Nr", SqlDbType.Image),
					new SqlParameter("@Jsr_Code", SqlDbType.Char,7),
					new SqlParameter("@Ys_Code", SqlDbType.Char,7),
					new SqlParameter("@AddDate", SqlDbType.SmallDateTime),
                    new SqlParameter("@Ks_Code", SqlDbType.Char,7),
                                        new SqlParameter("@Print_Row", SqlDbType.BigInt,8),
                                        new SqlParameter("@Print_Page", SqlDbType.BigInt,8),
					new SqlParameter("@id", SqlDbType.BigInt,8)};
			parameters[0].Value = model.Bl_Code;
			parameters[1].Value = model.Mb_Code;
			parameters[2].Value = model.Mb_Name;
			parameters[3].Value = model.Mb_Nr;
			parameters[4].Value = model.Jsr_Code;
			parameters[5].Value = model.Ys_Code;
			parameters[6].Value = model.AddDate;
            parameters[7].Value = model.Ks_Code;
            parameters[8].Value = model.Print_Row;
            parameters[9].Value = model.Print_Page;
			parameters[10].Value = model.id;
           
			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(long id)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Emr_Bl ");
			strSql.Append(" where id=@id ");
			SqlParameter[] parameters = {
					new SqlParameter("@id", SqlDbType.BigInt,8)			};
			parameters[0].Value = id;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string idlist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Emr_Bl ");
			strSql.Append(" where id in ("+idlist + ")  ");
			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Emr_Bl GetModel(long id)
		{
			
			StringBuilder strSql=new StringBuilder();
            strSql.Append("select  top 1 id,Bl_Code,Mb_Code,Mb_Name,Mb_Nr,Jsr_Code,Ys_Code,AddDate,Ks_Code,Print_Row,Print_Page from Emr_Bl ");
			strSql.Append(" where id=@id ");
			SqlParameter[] parameters = {
					new SqlParameter("@id", SqlDbType.BigInt,8)			};
			parameters[0].Value = id;

			ModelOld.M_Emr_Bl model=new ModelOld.M_Emr_Bl();
			DataSet ds=HisVar.HisVar.Sqldal.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
                return DataRowToModelemrbl(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}

        public ModelOld.M_Emr_Bl DataRowToModelemrbl(DataRow row)
        {
            ModelOld.M_Emr_Bl model = new ModelOld.M_Emr_Bl();
            if (row != null)
            {
                if (row["id"] != null && row["id"].ToString() != "")
                {
                    model.id = long.Parse(row["id"].ToString());
                }
                if (row["Bl_Code"] != null)
                {
                    model.Bl_Code = row["Bl_Code"].ToString();
                }
                if (row["Mb_Code"] != null)
                {
                    model.Mb_Code = row["Mb_Code"].ToString();
                }
                if (row["Mb_Name"] != null)
                {
                    model.Mb_Name = row["Mb_Name"].ToString();
                }
                if (row["Mb_Nr"] != null && row["Mb_Nr"].ToString() != "")
                {
                    model.Mb_Nr = (byte[])row["Mb_Nr"];
                }
                if (row["Jsr_Code"] != null)
                {
                    model.Jsr_Code = row["Jsr_Code"].ToString();
                }
                if (row["Ys_Code"] != null)
                {
                    model.Ys_Code = row["Ys_Code"].ToString();
                }
                if (row["AddDate"] != null && row["AddDate"].ToString() != "")
                {
                    model.AddDate = DateTime.Parse(row["AddDate"].ToString());
                }
                if (row["Ks_Code"] != null)
                {
                    model.Ks_Code = row["Ks_Code"].ToString();
                }
                if (row["Print_Row"] != null && row["Print_Row"].ToString() != "")
                {
                    model.Print_Row = long.Parse(row["Print_Row"].ToString());
                }
                if (row["Print_Page"] != null && row["Print_Page"].ToString() != "")
                {
                    model.Print_Page = long.Parse(row["Print_Page"].ToString());
                }
            }
            return model;
        }
		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Emr_Bl DataRowToModel(DataRow row)
		{
			ModelOld.M_Emr_Bl model=new ModelOld.M_Emr_Bl();
			if (row != null)
			{
				if(row["id"]!=null && row["id"].ToString()!="")
				{
					model.id=long.Parse(row["id"].ToString());
				}
				if(row["Bl_Code"]!=null)
				{
					model.Bl_Code=row["Bl_Code"].ToString();
				}
				if(row["Mb_Code"]!=null)
				{
					model.Mb_Code=row["Mb_Code"].ToString();
				}
				if(row["Mb_Name"]!=null)
				{
					model.Mb_Name=row["Mb_Name"].ToString();
				}
				if(row["Mb_Nr"]!=null && row["Mb_Nr"].ToString()!="")
				{
					model.Mb_Nr=(byte[])row["Mb_Nr"];
				}
				if(row["Jsr_Code"]!=null)
				{
					model.Jsr_Code=row["Jsr_Code"].ToString();
				}
				if(row["Ys_Code"]!=null)
				{
					model.Ys_Code=row["Ys_Code"].ToString();
				}
				if(row["AddDate"]!=null && row["AddDate"].ToString()!="")
				{
					model.AddDate=DateTime.Parse(row["AddDate"].ToString());
				}
                if (row["Ks_Code"] != null)
                {
                    model.Ks_Code = row["Ks_Code"].ToString();
                }
                if (row["Mblb_Code"] != null && row["Mblb_Code"].ToString() != "")
                {
                    model.Mblb_Code = row["Mblb_Code"].ToString();
                }
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
            strSql.Append("select id,Bl_Code,Mb_Code,Mb_Name,Emr_Bl.Mb_Nr,Jsr_Code,Ys_Code,AddDate,Ks_Code ");
            strSql.Append(" FROM Emr_Bl");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

        public DataSet GetListzk(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select id,Bl_Code,Mb_Code,Mb_Name,Emr_Bl.Mb_Nr,Jsr_Code,Ys_Code,AddDate,Ks_Code ");
            strSql.Append(" FROM Emr_Bl");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }


        public DataSet GetListnode(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  id,Bl_Code,Emr_Bl.Mb_Code,Emr_Mb.Mb_Name,Emr_Bl.Mb_Nr,Emr_Bl.Jsr_Code,Emr_Bl.Ys_Code,AddDate,Emr_Mb.Mblb_Code,Mblb_Name,Father_Code,Emr_Bl.ks_code ");
            strSql.Append(" FROM dbo.Emr_Bl,dbo.Emr_Mb,dbo.Emr_Mblb ");
            strSql.Append(" where Emr_Bl.mb_code=emr_mb.mb_code AND emr_mb.mblb_code=emr_mblb.mblb_code ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" and " + strWhere);
            }
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());

        }

        public DataSet GetListSkMb(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(" SELECT Sk_Code,dbo.Emr_SiKong.Mblb_Code,Mblb_Name,dbo.Emr_SiKong.Mb_Code,Mb_Name,");
            strSql.Append(" Yl_Event,days*24+Hours MaxInterval");
            strSql.Append("  FROM dbo.Emr_SiKong JOIN dbo.Emr_Mblb ON Emr_Mblb.Mblb_Code = Emr_SiKong.Mblb_Code");
            strSql.Append(" LEFT JOIN dbo.Emr_Mb ON Emr_Mb.Mb_Code = Emr_SiKong.Mb_Code");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());

        }

        public DataSet GetListblandmb(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT Emr_Bl.*,isMulti FROM Emr_Bl,Emr_Mb ");
            strSql.Append(" WHERE dbo.Emr_Mb.Mb_Code=dbo.Emr_Bl.Mb_Code ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" and " + strWhere);
            }
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
            strSql.Append(" id,Bl_Code,Mb_Code,Mb_Name,Mb_Nr,Jsr_Code,Ys_Code,AddDate,Ks_Code ");
			strSql.Append(" FROM Emr_Bl ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}


		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM Emr_Bl ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.id desc");
			}
			strSql.Append(")AS Row, T.*  from Emr_Bl T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Emr_Bl";
			parameters[1].Value = "id";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return HisVar.HisVar.Sqldal.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

