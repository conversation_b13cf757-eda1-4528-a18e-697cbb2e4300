﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class Temparature
    Inherits HisControl.BaseChild

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel()
        Me.MyGrid1 = New CustomControl.MyGrid()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.T_Label = New System.Windows.Forms.Label()
        Me.T_Textbox = New CustomControl.MyTextBox()
        Me.TableLayoutPanel2 = New System.Windows.Forms.TableLayoutPanel()
        Me.ZCLMyNumericEdit4 = New CustomControl.MyNumericEdit()
        Me.ZRLMyNumericEdit3 = New CustomControl.MyNumericEdit()
        Me.XBLMyNumericEdit7 = New CustomControl.MyNumericEdit()
        Me.HeightMyNumericEdit1 = New CustomControl.MyNumericEdit()
        Me.WeightMyNumericEdit2 = New CustomControl.MyNumericEdit()
        Me.GMYW1MyTextBox1 = New CustomControl.MyTextBox()
        Me.PrintDaysNumericEdit1 = New CustomControl.MyNumericEdit()
        Me.Panel4 = New System.Windows.Forms.Panel()
        Me.DBCSComboBox1 = New System.Windows.Forms.ComboBox()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.PSJGCombo = New CustomControl.MySingleComobo()
        Me.XYTextBox1 = New CustomControl.MyTextBox()
        Me.MyGrid2 = New CustomControl.MyGrid()
        Me.Panel3 = New System.Windows.Forms.Panel()
        Me.ExitBtn = New CustomControl.MyButton()
        Me.SaveMyButton1 = New CustomControl.MyButton()
        Me.DeleteMyButton2 = New CustomControl.MyButton()
        Me.PrintButton1 = New CustomControl.MyButton()
        Me.Panel2 = New System.Windows.Forms.Panel()
        Me.MyButton2 = New CustomControl.MyButton()
        Me.MyButton1 = New CustomControl.MyButton()
        Me.ClDateMyDateEdit1 = New CustomControl.MyDateEdit()
        Me.TableLayoutPanel1.SuspendLayout
        Me.Panel1.SuspendLayout
        Me.TableLayoutPanel2.SuspendLayout
        Me.Panel4.SuspendLayout
        Me.Panel3.SuspendLayout
        Me.Panel2.SuspendLayout
        Me.SuspendLayout
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.ColumnCount = 2
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 220!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100!))
        Me.TableLayoutPanel1.Controls.Add(Me.MyGrid1, 0, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.Panel1, 0, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.TableLayoutPanel2, 1, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.Panel2, 1, 0)
        Me.TableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(0, 0)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.RowCount = 2
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 40!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100!))
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(1110, 535)
        Me.TableLayoutPanel1.TabIndex = 0
        '
        'MyGrid1
        '
        Me.MyGrid1.CanCustomCol = false
        Me.MyGrid1.Caption = ""
        Me.MyGrid1.ChildGrid = Nothing
        Me.MyGrid1.Col = 0
        Me.MyGrid1.ColumnFooters = false
        Me.MyGrid1.DataMember = ""
        Me.MyGrid1.DataSource = Nothing
        Me.MyGrid1.DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveRight
        Me.MyGrid1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.MyGrid1.FetchRowStyles = false
        Me.MyGrid1.GroupByAreaVisible = true
        Me.MyGrid1.Location = New System.Drawing.Point(0, 40)
        Me.MyGrid1.Margin = New System.Windows.Forms.Padding(0)
        Me.MyGrid1.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.MyGrid1.Name = "MyGrid1"
        Me.MyGrid1.Size = New System.Drawing.Size(220, 495)
        Me.MyGrid1.TabAction = C1.Win.C1TrueDBGrid.TabActionEnum.ControlNavigation
        Me.MyGrid1.TabIndex = 3
        Me.MyGrid1.Xmlpath = Nothing
        '
        'Panel1
        '
        Me.Panel1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom)  _
            Or System.Windows.Forms.AnchorStyles.Left)  _
            Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.Panel1.Controls.Add(Me.T_Label)
        Me.Panel1.Controls.Add(Me.T_Textbox)
        Me.Panel1.Location = New System.Drawing.Point(0, 0)
        Me.Panel1.Margin = New System.Windows.Forms.Padding(0)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(220, 40)
        Me.Panel1.TabIndex = 4
        '
        'T_Label
        '
        Me.T_Label.AutoSize = true
        Me.T_Label.Location = New System.Drawing.Point(160, 14)
        Me.T_Label.Name = "T_Label"
        Me.T_Label.Size = New System.Drawing.Size(17, 12)
        Me.T_Label.TabIndex = 2
        Me.T_Label.Text = "∑"
        '
        'T_Textbox
        '
        Me.T_Textbox.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.T_Textbox.Captain = "患    者"
        Me.T_Textbox.CaptainBackColor = System.Drawing.Color.Transparent
        Me.T_Textbox.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.T_Textbox.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.T_Textbox.CaptainWidth = 60!
        Me.T_Textbox.ContentForeColor = System.Drawing.Color.Black
        Me.T_Textbox.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer))
        Me.T_Textbox.EditMask = Nothing
        Me.T_Textbox.Location = New System.Drawing.Point(0, 10)
        Me.T_Textbox.Multiline = false
        Me.T_Textbox.Name = "T_Textbox"
        Me.T_Textbox.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.T_Textbox.ReadOnly = false
        Me.T_Textbox.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.T_Textbox.SelectionStart = 0
        Me.T_Textbox.SelectStart = 0
        Me.T_Textbox.Size = New System.Drawing.Size(154, 20)
        Me.T_Textbox.TabIndex = 1
        Me.T_Textbox.TextAlign = System.Windows.Forms.HorizontalAlignment.Left
        Me.T_Textbox.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.T_Textbox.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.T_Textbox.Watermark = Nothing
        '
        'TableLayoutPanel2
        '
        Me.TableLayoutPanel2.ColumnCount = 6
        Me.TableLayoutPanel2.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 160!))
        Me.TableLayoutPanel2.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 160!))
        Me.TableLayoutPanel2.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 160!))
        Me.TableLayoutPanel2.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 160!))
        Me.TableLayoutPanel2.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 131!))
        Me.TableLayoutPanel2.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 119!))
        Me.TableLayoutPanel2.Controls.Add(Me.ZCLMyNumericEdit4, 1, 2)
        Me.TableLayoutPanel2.Controls.Add(Me.ZRLMyNumericEdit3, 0, 2)
        Me.TableLayoutPanel2.Controls.Add(Me.XBLMyNumericEdit7, 1, 1)
        Me.TableLayoutPanel2.Controls.Add(Me.HeightMyNumericEdit1, 2, 1)
        Me.TableLayoutPanel2.Controls.Add(Me.WeightMyNumericEdit2, 3, 1)
        Me.TableLayoutPanel2.Controls.Add(Me.GMYW1MyTextBox1, 0, 3)
        Me.TableLayoutPanel2.Controls.Add(Me.PrintDaysNumericEdit1, 2, 3)
        Me.TableLayoutPanel2.Controls.Add(Me.Panel4, 0, 1)
        Me.TableLayoutPanel2.Controls.Add(Me.PSJGCombo, 1, 3)
        Me.TableLayoutPanel2.Controls.Add(Me.XYTextBox1, 2, 2)
        Me.TableLayoutPanel2.Controls.Add(Me.MyGrid2, 0, 0)
        Me.TableLayoutPanel2.Controls.Add(Me.Panel3, 0, 4)
        Me.TableLayoutPanel2.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TableLayoutPanel2.Location = New System.Drawing.Point(220, 40)
        Me.TableLayoutPanel2.Margin = New System.Windows.Forms.Padding(0)
        Me.TableLayoutPanel2.Name = "TableLayoutPanel2"
        Me.TableLayoutPanel2.RowCount = 5
        Me.TableLayoutPanel2.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 190!))
        Me.TableLayoutPanel2.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 30!))
        Me.TableLayoutPanel2.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 30!))
        Me.TableLayoutPanel2.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 30!))
        Me.TableLayoutPanel2.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 60!))
        Me.TableLayoutPanel2.Size = New System.Drawing.Size(890, 495)
        Me.TableLayoutPanel2.TabIndex = 0
        '
        'ZCLMyNumericEdit4
        '
        Me.ZCLMyNumericEdit4.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.ZCLMyNumericEdit4.Captain = " 总出量"
        Me.ZCLMyNumericEdit4.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.ZCLMyNumericEdit4.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.ZCLMyNumericEdit4.CaptainWidth = 60!
        Me.ZCLMyNumericEdit4.Location = New System.Drawing.Point(163, 225)
        Me.ZCLMyNumericEdit4.MaximumSize = New System.Drawing.Size(100000, 20)
        Me.ZCLMyNumericEdit4.MinimumSize = New System.Drawing.Size(0, 20)
        Me.ZCLMyNumericEdit4.Name = "ZCLMyNumericEdit4"
        Me.ZCLMyNumericEdit4.NumFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.ZCLMyNumericEdit4.ReadOnly = false
        Me.ZCLMyNumericEdit4.Size = New System.Drawing.Size(154, 20)
        Me.ZCLMyNumericEdit4.TabIndex = 6
        Me.ZCLMyNumericEdit4.ValueIsDbNull = true
        '
        'ZRLMyNumericEdit3
        '
        Me.ZRLMyNumericEdit3.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.ZRLMyNumericEdit3.Captain = " 总入量"
        Me.ZRLMyNumericEdit3.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.ZRLMyNumericEdit3.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.ZRLMyNumericEdit3.CaptainWidth = 60!
        Me.ZRLMyNumericEdit3.Location = New System.Drawing.Point(3, 225)
        Me.ZRLMyNumericEdit3.MaximumSize = New System.Drawing.Size(100000, 20)
        Me.ZRLMyNumericEdit3.MinimumSize = New System.Drawing.Size(0, 20)
        Me.ZRLMyNumericEdit3.Name = "ZRLMyNumericEdit3"
        Me.ZRLMyNumericEdit3.NumFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.ZRLMyNumericEdit3.ReadOnly = false
        Me.ZRLMyNumericEdit3.Size = New System.Drawing.Size(154, 20)
        Me.ZRLMyNumericEdit3.TabIndex = 5
        Me.ZRLMyNumericEdit3.ValueIsDbNull = true
        '
        'XBLMyNumericEdit7
        '
        Me.XBLMyNumericEdit7.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.XBLMyNumericEdit7.Captain = "尿    量"
        Me.XBLMyNumericEdit7.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.XBLMyNumericEdit7.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.XBLMyNumericEdit7.CaptainWidth = 60!
        Me.XBLMyNumericEdit7.Location = New System.Drawing.Point(163, 195)
        Me.XBLMyNumericEdit7.MaximumSize = New System.Drawing.Size(100000, 20)
        Me.XBLMyNumericEdit7.MinimumSize = New System.Drawing.Size(0, 20)
        Me.XBLMyNumericEdit7.Name = "XBLMyNumericEdit7"
        Me.XBLMyNumericEdit7.NumFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.XBLMyNumericEdit7.ReadOnly = false
        Me.XBLMyNumericEdit7.Size = New System.Drawing.Size(154, 20)
        Me.XBLMyNumericEdit7.TabIndex = 2
        Me.XBLMyNumericEdit7.ValueIsDbNull = true
        '
        'HeightMyNumericEdit1
        '
        Me.HeightMyNumericEdit1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.HeightMyNumericEdit1.Captain = "身    高"
        Me.HeightMyNumericEdit1.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.HeightMyNumericEdit1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.HeightMyNumericEdit1.CaptainWidth = 60!
        Me.HeightMyNumericEdit1.Location = New System.Drawing.Point(323, 195)
        Me.HeightMyNumericEdit1.MaximumSize = New System.Drawing.Size(100000, 20)
        Me.HeightMyNumericEdit1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.HeightMyNumericEdit1.Name = "HeightMyNumericEdit1"
        Me.HeightMyNumericEdit1.NumFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.HeightMyNumericEdit1.ReadOnly = false
        Me.HeightMyNumericEdit1.Size = New System.Drawing.Size(154, 20)
        Me.HeightMyNumericEdit1.TabIndex = 3
        Me.HeightMyNumericEdit1.ValueIsDbNull = true
        '
        'WeightMyNumericEdit2
        '
        Me.WeightMyNumericEdit2.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.WeightMyNumericEdit2.Captain = "体    重"
        Me.WeightMyNumericEdit2.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.WeightMyNumericEdit2.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.WeightMyNumericEdit2.CaptainWidth = 60!
        Me.WeightMyNumericEdit2.Location = New System.Drawing.Point(483, 195)
        Me.WeightMyNumericEdit2.MaximumSize = New System.Drawing.Size(100000, 20)
        Me.WeightMyNumericEdit2.MinimumSize = New System.Drawing.Size(0, 20)
        Me.WeightMyNumericEdit2.Name = "WeightMyNumericEdit2"
        Me.WeightMyNumericEdit2.NumFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.WeightMyNumericEdit2.ReadOnly = false
        Me.WeightMyNumericEdit2.Size = New System.Drawing.Size(154, 20)
        Me.WeightMyNumericEdit2.TabIndex = 4
        Me.WeightMyNumericEdit2.ValueIsDbNull = true
        '
        'GMYW1MyTextBox1
        '
        Me.GMYW1MyTextBox1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.GMYW1MyTextBox1.Captain = "皮试信息"
        Me.GMYW1MyTextBox1.CaptainBackColor = System.Drawing.Color.Transparent
        Me.GMYW1MyTextBox1.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.GMYW1MyTextBox1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.GMYW1MyTextBox1.CaptainWidth = 60!
        Me.GMYW1MyTextBox1.ContentForeColor = System.Drawing.Color.Black
        Me.GMYW1MyTextBox1.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer))
        Me.GMYW1MyTextBox1.EditMask = Nothing
        Me.GMYW1MyTextBox1.Location = New System.Drawing.Point(3, 255)
        Me.GMYW1MyTextBox1.Multiline = false
        Me.GMYW1MyTextBox1.Name = "GMYW1MyTextBox1"
        Me.GMYW1MyTextBox1.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.GMYW1MyTextBox1.ReadOnly = false
        Me.GMYW1MyTextBox1.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.GMYW1MyTextBox1.SelectionStart = 0
        Me.GMYW1MyTextBox1.SelectStart = 0
        Me.GMYW1MyTextBox1.Size = New System.Drawing.Size(154, 20)
        Me.GMYW1MyTextBox1.TabIndex = 8
        Me.GMYW1MyTextBox1.TextAlign = System.Windows.Forms.HorizontalAlignment.Left
        Me.GMYW1MyTextBox1.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.GMYW1MyTextBox1.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.GMYW1MyTextBox1.Watermark = Nothing
        '
        'PrintDaysNumericEdit1
        '
        Me.PrintDaysNumericEdit1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.PrintDaysNumericEdit1.Captain = "打印天数"
        Me.PrintDaysNumericEdit1.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.PrintDaysNumericEdit1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.PrintDaysNumericEdit1.CaptainWidth = 60!
        Me.PrintDaysNumericEdit1.Location = New System.Drawing.Point(323, 255)
        Me.PrintDaysNumericEdit1.MaximumSize = New System.Drawing.Size(100000, 80)
        Me.PrintDaysNumericEdit1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.PrintDaysNumericEdit1.Name = "PrintDaysNumericEdit1"
        Me.PrintDaysNumericEdit1.NumFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.PrintDaysNumericEdit1.ReadOnly = false
        Me.PrintDaysNumericEdit1.Size = New System.Drawing.Size(154, 20)
        Me.PrintDaysNumericEdit1.TabIndex = 10
        Me.PrintDaysNumericEdit1.ValueIsDbNull = false
        '
        'Panel4
        '
        Me.Panel4.Controls.Add(Me.DBCSComboBox1)
        Me.Panel4.Controls.Add(Me.Label1)
        Me.Panel4.Location = New System.Drawing.Point(3, 193)
        Me.Panel4.Name = "Panel4"
        Me.Panel4.Size = New System.Drawing.Size(154, 24)
        Me.Panel4.TabIndex = 50
        '
        'DBCSComboBox1
        '
        Me.DBCSComboBox1.FormattingEnabled = true
        Me.DBCSComboBox1.Location = New System.Drawing.Point(61, 1)
        Me.DBCSComboBox1.Name = "DBCSComboBox1"
        Me.DBCSComboBox1.Size = New System.Drawing.Size(93, 20)
        Me.DBCSComboBox1.TabIndex = 1
        Me.DBCSComboBox1.Tag = "DBCSComboBox1"
        '
        'Label1
        '
        Me.Label1.AutoSize = true
        Me.Label1.Location = New System.Drawing.Point(2, 4)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(53, 12)
        Me.Label1.TabIndex = 0
        Me.Label1.Text = "大便次数"
        '
        'PSJGCombo
        '
        Me.PSJGCombo.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.PSJGCombo.Captain = "皮试结果"
        Me.PSJGCombo.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.PSJGCombo.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.PSJGCombo.CaptainWidth = 60!
        Me.PSJGCombo.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        Me.PSJGCombo.ItemHeight = 16
        Me.PSJGCombo.ItemTextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.PSJGCombo.Location = New System.Drawing.Point(163, 255)
        Me.PSJGCombo.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.PSJGCombo.MinimumSize = New System.Drawing.Size(0, 20)
        Me.PSJGCombo.Name = "PSJGCombo"
        Me.PSJGCombo.ReadOnly = false
        Me.PSJGCombo.Size = New System.Drawing.Size(154, 20)
        Me.PSJGCombo.TabIndex = 9
        Me.PSJGCombo.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        '
        'XYTextBox1
        '
        Me.XYTextBox1.Captain = "血    压"
        Me.XYTextBox1.CaptainBackColor = System.Drawing.Color.Transparent
        Me.XYTextBox1.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.XYTextBox1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.XYTextBox1.CaptainWidth = 60!
        Me.XYTextBox1.ContentForeColor = System.Drawing.Color.Black
        Me.XYTextBox1.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer))
        Me.XYTextBox1.EditMask = Nothing
        Me.XYTextBox1.Location = New System.Drawing.Point(323, 223)
        Me.XYTextBox1.Multiline = false
        Me.XYTextBox1.Name = "XYTextBox1"
        Me.XYTextBox1.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.XYTextBox1.ReadOnly = false
        Me.XYTextBox1.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.XYTextBox1.SelectionStart = 0
        Me.XYTextBox1.SelectStart = 0
        Me.XYTextBox1.Size = New System.Drawing.Size(154, 20)
        Me.XYTextBox1.TabIndex = 7
        Me.XYTextBox1.TextAlign = System.Windows.Forms.HorizontalAlignment.Left
        Me.XYTextBox1.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.XYTextBox1.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.XYTextBox1.Watermark = Nothing
        '
        'MyGrid2
        '
        Me.MyGrid2.CanCustomCol = false
        Me.MyGrid2.Caption = ""
        Me.MyGrid2.ChildGrid = Nothing
        Me.MyGrid2.Col = 0
        Me.MyGrid2.ColumnFooters = false
        Me.TableLayoutPanel2.SetColumnSpan(Me.MyGrid2, 6)
        Me.MyGrid2.DataMember = ""
        Me.MyGrid2.DataSource = Nothing
        Me.MyGrid2.DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveRight
        Me.MyGrid2.Dock = System.Windows.Forms.DockStyle.Fill
        Me.MyGrid2.FetchRowStyles = false
        Me.MyGrid2.GroupByAreaVisible = true
        Me.MyGrid2.Location = New System.Drawing.Point(0, 0)
        Me.MyGrid2.Margin = New System.Windows.Forms.Padding(0)
        Me.MyGrid2.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.MyGrid2.Name = "MyGrid2"
        Me.MyGrid2.Size = New System.Drawing.Size(890, 190)
        Me.MyGrid2.TabAction = C1.Win.C1TrueDBGrid.TabActionEnum.ControlNavigation
        Me.MyGrid2.TabIndex = 51
        Me.MyGrid2.Xmlpath = Nothing
        '
        'Panel3
        '
        Me.TableLayoutPanel2.SetColumnSpan(Me.Panel3, 4)
        Me.Panel3.Controls.Add(Me.ExitBtn)
        Me.Panel3.Controls.Add(Me.SaveMyButton1)
        Me.Panel3.Controls.Add(Me.DeleteMyButton2)
        Me.Panel3.Controls.Add(Me.PrintButton1)
        Me.Panel3.Location = New System.Drawing.Point(0, 280)
        Me.Panel3.Margin = New System.Windows.Forms.Padding(0)
        Me.Panel3.Name = "Panel3"
        Me.Panel3.Size = New System.Drawing.Size(640, 50)
        Me.Panel3.TabIndex = 48
        '
        'ExitBtn
        '
        Me.ExitBtn.ButtonImageSize = CustomControl.MyButton.imageSize.large
        Me.ExitBtn.DialogResult = System.Windows.Forms.DialogResult.None
        Me.ExitBtn.Font = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.ExitBtn.Location = New System.Drawing.Point(540, 8)
        Me.ExitBtn.Name = "ExitBtn"
        Me.ExitBtn.Size = New System.Drawing.Size(80, 35)
        Me.ExitBtn.TabIndex = 31
        Me.ExitBtn.Text = "退出"
        '
        'SaveMyButton1
        '
        Me.SaveMyButton1.ButtonImageSize = CustomControl.MyButton.imageSize.large
        Me.SaveMyButton1.DialogResult = System.Windows.Forms.DialogResult.None
        Me.SaveMyButton1.Font = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.SaveMyButton1.Location = New System.Drawing.Point(282, 8)
        Me.SaveMyButton1.Name = "SaveMyButton1"
        Me.SaveMyButton1.Size = New System.Drawing.Size(80, 35)
        Me.SaveMyButton1.TabIndex = 11
        Me.SaveMyButton1.Text = "保存"
        '
        'DeleteMyButton2
        '
        Me.DeleteMyButton2.ButtonImageSize = CustomControl.MyButton.imageSize.large
        Me.DeleteMyButton2.DialogResult = System.Windows.Forms.DialogResult.None
        Me.DeleteMyButton2.Font = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.DeleteMyButton2.Location = New System.Drawing.Point(368, 8)
        Me.DeleteMyButton2.Name = "DeleteMyButton2"
        Me.DeleteMyButton2.Size = New System.Drawing.Size(80, 35)
        Me.DeleteMyButton2.TabIndex = 20
        Me.DeleteMyButton2.Text = "删除"
        '
        'PrintButton1
        '
        Me.PrintButton1.ButtonImageSize = CustomControl.MyButton.imageSize.large
        Me.PrintButton1.DialogResult = System.Windows.Forms.DialogResult.None
        Me.PrintButton1.Font = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.PrintButton1.Location = New System.Drawing.Point(454, 8)
        Me.PrintButton1.Name = "PrintButton1"
        Me.PrintButton1.Size = New System.Drawing.Size(80, 35)
        Me.PrintButton1.TabIndex = 30
        Me.PrintButton1.Text = "打印"
        '
        'Panel2
        '
        Me.Panel2.Controls.Add(Me.MyButton2)
        Me.Panel2.Controls.Add(Me.MyButton1)
        Me.Panel2.Controls.Add(Me.ClDateMyDateEdit1)
        Me.Panel2.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel2.Location = New System.Drawing.Point(220, 0)
        Me.Panel2.Margin = New System.Windows.Forms.Padding(0)
        Me.Panel2.Name = "Panel2"
        Me.Panel2.Size = New System.Drawing.Size(890, 40)
        Me.Panel2.TabIndex = 5
        '
        'MyButton2
        '
        Me.MyButton2.ButtonImageSize = CustomControl.MyButton.imageSize.large
        Me.MyButton2.DialogResult = System.Windows.Forms.DialogResult.None
        Me.MyButton2.Font = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.MyButton2.Location = New System.Drawing.Point(304, 3)
        Me.MyButton2.Name = "MyButton2"
        Me.MyButton2.Size = New System.Drawing.Size(80, 35)
        Me.MyButton2.TabIndex = 53
        Me.MyButton2.Text = "后一天"
        '
        'MyButton1
        '
        Me.MyButton1.ButtonImageSize = CustomControl.MyButton.imageSize.large
        Me.MyButton1.DialogResult = System.Windows.Forms.DialogResult.None
        Me.MyButton1.Font = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.MyButton1.Location = New System.Drawing.Point(206, 3)
        Me.MyButton1.Name = "MyButton1"
        Me.MyButton1.Size = New System.Drawing.Size(80, 35)
        Me.MyButton1.TabIndex = 52
        Me.MyButton1.Text = "前一天"
        '
        'ClDateMyDateEdit1
        '
        Me.ClDateMyDateEdit1.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.ClDateMyDateEdit1.Captain = "测量日期"
        Me.ClDateMyDateEdit1.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.ClDateMyDateEdit1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.ClDateMyDateEdit1.CaptainWidth = 60!
        Me.ClDateMyDateEdit1.InitialSelection = C1.Win.C1Input.InitialSelectionEnum.CaretAtEnd
        Me.ClDateMyDateEdit1.Location = New System.Drawing.Point(23, 10)
        Me.ClDateMyDateEdit1.MaximumSize = New System.Drawing.Size(100000000, 20)
        Me.ClDateMyDateEdit1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.ClDateMyDateEdit1.Name = "ClDateMyDateEdit1"
        Me.ClDateMyDateEdit1.ReadOnly = false
        Me.ClDateMyDateEdit1.Size = New System.Drawing.Size(154, 20)
        Me.ClDateMyDateEdit1.TabIndex = 51
        Me.ClDateMyDateEdit1.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.ClDateMyDateEdit1.ValueIsDbNull = false
        Me.ClDateMyDateEdit1.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.ClDateMyDateEdit1.VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.DropDown
        '
        'Temparature
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6!, 12!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1110, 535)
        Me.Controls.Add(Me.TableLayoutPanel1)
        Me.Name = "Temparature"
        Me.Text = "体温表"
        Me.TableLayoutPanel1.ResumeLayout(false)
        Me.Panel1.ResumeLayout(false)
        Me.Panel1.PerformLayout
        Me.TableLayoutPanel2.ResumeLayout(false)
        Me.Panel4.ResumeLayout(false)
        Me.Panel4.PerformLayout
        Me.Panel3.ResumeLayout(false)
        Me.Panel2.ResumeLayout(false)
        Me.ResumeLayout(false)

End Sub
    Friend WithEvents TableLayoutPanel1 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents T_Textbox As CustomControl.MyTextBox
    Friend WithEvents MyGrid1 As CustomControl.MyGrid
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents T_Label As System.Windows.Forms.Label
    Friend WithEvents TableLayoutPanel2 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents HeightMyNumericEdit1 As CustomControl.MyNumericEdit
    Friend WithEvents WeightMyNumericEdit2 As CustomControl.MyNumericEdit
    Friend WithEvents ZRLMyNumericEdit3 As CustomControl.MyNumericEdit
    Friend WithEvents ZCLMyNumericEdit4 As CustomControl.MyNumericEdit
    Friend WithEvents XBLMyNumericEdit7 As CustomControl.MyNumericEdit
    Friend WithEvents GMYW1MyTextBox1 As CustomControl.MyTextBox
    Friend WithEvents SaveMyButton1 As CustomControl.MyButton
    Friend WithEvents DeleteMyButton2 As CustomControl.MyButton
    Friend WithEvents PrintButton1 As CustomControl.MyButton
    Friend WithEvents PrintDaysNumericEdit1 As CustomControl.MyNumericEdit
    Friend WithEvents Panel3 As System.Windows.Forms.Panel
    Friend WithEvents Panel4 As System.Windows.Forms.Panel
    Friend WithEvents DBCSComboBox1 As System.Windows.Forms.ComboBox
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents PSJGCombo As CustomControl.MySingleComobo
    Friend WithEvents XYTextBox1 As CustomControl.MyTextBox
    Friend WithEvents MyGrid2 As CustomControl.MyGrid
    Friend WithEvents ExitBtn As CustomControl.MyButton
    Friend WithEvents Panel2 As System.Windows.Forms.Panel
    Friend WithEvents MyButton2 As CustomControl.MyButton
    Friend WithEvents MyButton1 As CustomControl.MyButton
    Friend WithEvents ClDateMyDateEdit1 As CustomControl.MyDateEdit
End Class
