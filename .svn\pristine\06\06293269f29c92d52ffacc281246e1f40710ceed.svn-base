﻿/**  版本信息模板在安装目录下，可自行修改。
* M_LIS_DictDev.cs
*
* 功 能： N/A
* 类 名： M_LIS_DictDev
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016-10-19 16:06:34   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace ModelOld
{
	/// <summary>
	/// M_LIS_DictDev:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class M_LIS_DictDev
	{
		public M_LIS_DictDev()
		{}
		#region Model
		private string _dev_code;
		private string _dev_name;
		private string _dev_jc;
		private string _dev_model;
		private string _dev_manufacturer;
		private string _dev_lx;
		private string _dev_lb;
		private string _ks_code;
		private string _interfacetype;
		private string _comport;
		private string _baudrate;
		private string _databits;
		private string _stopbits;
		private string _checkbits;
		private string _ip;
		private string _port;
		private string _filepath;
		private string _interfaceprograme;
		private string _memo;
		/// <summary>
		/// 
		/// </summary>
		public string Dev_Code
		{
			set{ _dev_code=value;}
			get{return _dev_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Dev_Name
		{
			set{ _dev_name=value;}
			get{return _dev_name;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Dev_Jc
		{
			set{ _dev_jc=value;}
			get{return _dev_jc;}
		}
		/// <summary>
		/// 设备型号
		/// </summary>
		public string Dev_Model
		{
			set{ _dev_model=value;}
			get{return _dev_model;}
		}
		/// <summary>
		/// 生产厂家
		/// </summary>
		public string Dev_Manufacturer
		{
			set{ _dev_manufacturer=value;}
			get{return _dev_manufacturer;}
		}
		/// <summary>
		/// 设备类型
		/// </summary>
		public string Dev_Lx
		{
			set{ _dev_lx=value;}
			get{return _dev_lx;}
		}
		/// <summary>
		/// 设备类别
		/// </summary>
		public string Dev_Lb
		{
			set{ _dev_lb=value;}
			get{return _dev_lb;}
		}
		/// <summary>
		/// 使用科室
		/// </summary>
		public string Ks_Code
		{
			set{ _ks_code=value;}
			get{return _ks_code;}
		}
		/// <summary>
		/// 接口类别（串口，TCP/IP,中间库)
		/// </summary>
		public string InterfaceType
		{
			set{ _interfacetype=value;}
			get{return _interfacetype;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string ComPort
		{
			set{ _comport=value;}
			get{return _comport;}
		}
		/// <summary>
		/// 波特率
		/// </summary>
		public string BaudRate
		{
			set{ _baudrate=value;}
			get{return _baudrate;}
		}
		/// <summary>
		/// 数据位
		/// </summary>
		public string DataBits
		{
			set{ _databits=value;}
			get{return _databits;}
		}
		/// <summary>
		/// 停止位
		/// </summary>
		public string StopBits
		{
			set{ _stopbits=value;}
			get{return _stopbits;}
		}
		/// <summary>
		/// 校验位
		/// </summary>
		public string CheckBits
		{
			set{ _checkbits=value;}
			get{return _checkbits;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string IP
		{
			set{ _ip=value;}
			get{return _ip;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Port
		{
			set{ _port=value;}
			get{return _port;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string FilePath
		{
			set{ _filepath=value;}
			get{return _filepath;}
		}
		/// <summary>
		/// 接口程序名
		/// </summary>
		public string InterfacePrograme
		{
			set{ _interfaceprograme=value;}
			get{return _interfaceprograme;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Memo
		{
			set{ _memo=value;}
			get{return _memo;}
		}
		#endregion Model

	}
}

