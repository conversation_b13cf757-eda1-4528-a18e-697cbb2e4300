﻿Imports System.Data.SqlClient
Imports Stimulsoft.Report

Public Class Cw_lrtj1
    Dim My_Cm As CurrencyManager             '同步指针
    Dim My_View As New DataView
    Dim V_TreeFinish As Boolean
    Dim My_Dataset As New DataSet
    Public V_Str1 As String
    Dim str8 As String = ""

    Private Sub Cx_YfKsCk1_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        TextBox1.Text = ""
        DateTimePicker1.Select()

        BaseFunc.BaseFunc.Init_Datetimepicker(DateTimePicker1)
        BaseFunc.BaseFunc.Init_Datetimepicker(DateTimePicker2)
        DateTimePicker1.Value = Format(Now, "yyyy-MM-01 00:00:00")
        DateTimePicker2.Value = Format(Now, "yyyy-MM-dd 23:59:59")

        With Me.C1Numeric1
            .Value = 0
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .CustomFormat = "#############"
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
            .ShowContextMenu = False
            .EmptyAsNull = True
        End With

        Dim My_Combo2 As New BaseClass.C_Combo1(Me.Combo2)
        My_Combo2.Init_TDBCombo()
        With Combo2
            .AddItem("销售量排名")
            .AddItem("利润排名")
            .AddItem("利润率排名")
            .SelectedIndex = 0
            .Width = 90
            .DropDownWidth = 90
        End With

        'Combo2的初始化
        Dim My_Combo3 As New BaseClass.C_Combo1(Me.C1Combo1)
        My_Combo3.Init_TDBCombo()
        With C1Combo1
            .AddItem("全部")
            .AddItem("国家基本药物")
            .AddItem("省增补基本药物")
            .AddItem("非基本药物")
            .AddItem("其他")

            .SelectedIndex = 0
            .Width = 115
            .DropDownWidth = 115
        End With

        Dim My_C1Combo2 As New BaseClass.C_Combo1(Me.C1Combo2)
        My_C1Combo2.Init_TDBCombo()
        With C1Combo2
            .AddItem("按日结时间")
            .AddItem("按发药时间")
            .AddItem("按处方时间")
            .SelectedIndex = 0
            .Width = 90
            .DropDownWidth = 90
        End With
        Dim My_C1Combo3 As New BaseClass.C_Combo1(Me.C1Combo3)
        My_C1Combo3.Init_TDBCombo()
        With C1Combo3
            .AddItem("全部")
            .AddItem("住院")
            .AddItem("门诊")
            .SelectedIndex = 0
            .Width = 60
            .DropDownWidth = 60
        End With
        Dim My_C1Combo4 As New BaseClass.C_Combo1(Me.C1Combo4)
        My_C1Combo4.Init_TDBCombo()
        With C1Combo4
            .AddItem("按日清统计")
            .AddItem("按出院统计")
            .SelectedIndex = 0
            .Width = 90
            .DropDownWidth = 90
        End With

        Dim My_Grid As New BaseClass.C_Grid(Me.C1TrueDBGrid1)
        With My_Grid
            .Init_Grid()
            .AllAddNew(False)
            .AllDelete(False)
            .Init_Column("药品名称", "Yp_Name", 230, "左", "")
            .Init_Column("生产厂家", "Mx_Cd", 170, "左", "")
            .Init_Column("药品规格", "Mx_Gg", 100, "左", "")
            .Init_Column("单位", "Mx_Xsdw", 80, "中", "")
            .Init_Column("药品数量", "Sl", 100, "右", "0.####")
            .Init_Column("成本", "Cb_money", 120, "右", "0.0000")
            .Init_Column("销售金额", "Xs_money", 120, "右", "0.0000")
            .Init_Column("利润", "Lr_Money", 80, "右", "0.0000")
            .Init_Column("销售利润率", "Lrl", 70, "右", "0.0000")

            .P_Dock(BaseClass.C_Grid.Dock.Fill)
        End With
        C1TrueDBGrid1.Splits(0).HScrollBar.Style = C1.Win.C1TrueDBGrid.ScrollBarStyleEnum.Automatic
        C1TrueDBGrid1.Splits(0).VScrollBar.Style = C1.Win.C1TrueDBGrid.ScrollBarStyleEnum.Always
        With C1TrueDBGrid1
            .ColumnFooters = True
            .Splits(0).DisplayColumns(0).FooterDivider = True
            .Splits(0).DisplayColumns(1).FooterDivider = True
            .Splits(0).DisplayColumns(2).FooterDivider = True
            .Splits(0).DisplayColumns(3).FooterDivider = True
            .Splits(0).DisplayColumns(4).FooterDivider = True
            .Splits(0).DisplayColumns(5).FooterDivider = True
            .Splits(0).DisplayColumns(6).FooterDivider = True
            .Splits(0).DisplayColumns(7).FooterDivider = True
            .Splits(0).DisplayColumns(8).FooterDivider = True
        End With
        C1TrueDBGrid1.Splits(0).DisplayColumns("Mx_Cd").Visible = False
        Label4.Text = "记录条数 ∑=" + C1TrueDBGrid1.Splits(0).Rows.Count.ToString
        Call Init_Tree()

    End Sub

    Private Sub TextBox1_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles TextBox1.TextChanged

        Dim V_Str As String = ""
        If Trim(TextBox1.Text & "") = "" Then
            V_Str = ""
        Else
            V_Str = "yp_Name Like '*" & Trim(TextBox1.Text) & "*' "

        End If
        My_View.RowFilter = V_Str
        Label4.Text = "记录条数 ∑=" + C1TrueDBGrid1.Splits(0).Rows.Count.ToString

        Call F_Sum()
    End Sub

    Public Overrides Sub F_Sum()
        Dim Sum1 As Double = 0
        Dim Sum2 As Double = 0
        Dim Sum3 As Double = 0
        Dim Sum4 As Double = 0
        If C1TrueDBGrid1.RowCount <> 0 Then
            Sum1 = IIf(My_View.Table.Compute("Sum(Cb_money)", My_View.RowFilter) Is DBNull.Value, 0, My_View.Table.Compute("Sum(Cb_money)", My_View.RowFilter))
            Sum2 = IIf(My_View.Table.Compute("Sum(Xs_money)", My_View.RowFilter) Is DBNull.Value, 0, My_View.Table.Compute("Sum(Xs_money)", My_View.RowFilter))
            Sum3 = IIf(My_View.Table.Compute("Sum(Sl)", My_View.RowFilter) Is DBNull.Value, 0, My_View.Table.Compute("Sum(Sl)", My_View.RowFilter))
            Sum4 = IIf(My_View.Table.Compute("Sum(Lr_Money)", My_View.RowFilter) Is DBNull.Value, 0, My_View.Table.Compute("Sum(Lr_Money)", My_View.RowFilter))

        End If

        With C1TrueDBGrid1
            .ColumnFooters = True
            .Columns("Cb_money").FooterText = Format(Sum1, "###0.0000")
            .Columns("Xs_money").FooterText = Format(Sum2, "###0.0000")
            .Columns("Sl").FooterText = Format(Sum3, "0.####")
            .Columns("Lr_Money").FooterText = Format(Sum4, "###0.0000")
        End With

    End Sub

    Private Sub Init_Tree()

        With Me.TreeView1
            .Nodes.Clear()
            .FullRowSelect = True
            .HideSelection = False
            .HotTracking = True
            .Scrollable = True
            .ShowRootLines = False
            .Sorted = False
        End With

        V_TreeFinish = False

        '根节点
        Dim My_Reader As SqlDataReader

        Dim My_Root As New TreeNode
        With My_Root
            .Tag = "00"
            .Text = "药品类别"
            .ImageIndex = 0
        End With

        TreeView1.Nodes.Clear()
        TreeView1.Nodes.Add(My_Root)


        '一级数据
        My_Reader = HisVar.HisVar.Sqldal.ExecuteReader("Select Dl_Code,Dl_Name from Zd_Ml_YP1")
        While My_Reader.Read()
            Dim My_Node As New TreeNode
            With My_Node
                .Tag = My_Reader.Item(0).ToString
                .Text = My_Reader.Item(1).ToString
                .ImageIndex = 1
                .SelectedImageIndex = 2
            End With
            TreeView1.SelectedNode = My_Root
            TreeView1.SelectedNode.Nodes.Add(My_Node)

        End While
        TreeView1.SelectedNode.Checked = True
        My_Reader.Close()
        My_Cn.Close()

        TreeView1.ExpandAll()
        TreeView1.SelectedNode = TreeView1.Nodes.Item(0)

        V_TreeFinish = True

    End Sub

    Private Sub TreeView1_AfterCheck(ByVal sender As Object, ByVal e As System.Windows.Forms.TreeViewEventArgs) Handles TreeView1.AfterCheck
        Dim N_Count As Integer


        For N_Count = 0 To e.Node.Nodes.Count - 1
            If e.Node.Checked = True Then

                TreeView1.SelectedNode = e.Node
                TreeView1.SelectedNode.Nodes.Item(N_Count).Checked = True
            Else
                TreeView1.SelectedNode = e.Node
                TreeView1.SelectedNode.Nodes.Item(N_Count).Checked = False
            End If
        Next

    End Sub

    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Button3.Click
        Call Button1()
    End Sub
    Private Sub Button1()
        If My_Dataset.Tables("药品利润统计") IsNot Nothing Then My_Dataset.Tables("药品利润统计").Clear()
        Dim V_Dl As String = ""

        TextBox1.Text = ""
        Dim I As Integer

        TreeView1.SelectedNode = TreeView1.Nodes.Item(0)

        For I = 0 To TreeView1.SelectedNode.Nodes.Count - 1
            If TreeView1.SelectedNode.Nodes.Item(I).Checked = True Then
                V_Dl = V_Dl & ",'" & TreeView1.SelectedNode.Nodes.Item(I).Tag & "'"
            End If
        Next

        V_Dl = "(" & Mid(V_Dl, 2) & ")"
        If V_Dl = "()" Then
            MsgBox("请选择要查询的药品类别，要查询所有请全部勾选！", MsgBoxStyle.Information, "提示")
            Exit Sub

        End If

        Dim str2 As String

        Dim str3 As String = ""
        Dim str4 As String = ""


        If C1Numeric1.Text <> "" Then
            str2 = "top " + C1Numeric1.Text + ""
        Else
            str2 = ""
        End If

        Select Case Combo2.Text
            Case "利润排名"
                str3 = "Order By Lr_Money Desc "
            Case "利润率排名"
                str3 = "Order By Lrl Desc "
            Case "销售量排名"
                str3 = "Order By Sl Desc "
        End Select

        If C1Combo1.Text = "全部" Then
            str4 = " and 1=1"
        ElseIf C1Combo1.Text = "国家基本药物" Then
            str4 = " and  IsJb='国家基本药物'"
        ElseIf C1Combo1.Text = "省增补基本药物" Then
            str4 = " and  IsJb='省增补基本药物'"
        ElseIf C1Combo1.Text = "非基本药物" Then
            str4 = " and  IsJb='非基本药物'"
        ElseIf C1Combo1.Text = "其他" Then
            str4 = " and (IsJb<>'国家基本药物'and IsJb<>'省增补基本药物' and IsJb<>'非基本药物')  "
        End If

        Dim mz As String = ""
        Dim zy As String = ""



        If C1Combo2.Text = "按日结时间" Then
            If C1Combo3.Text = "全部" Or C1Combo3.Text = "住院" Then
                If C1Combo4.Text = "按日清统计" Then
                    zy = " select V_Ypkc.Xx_code,Yp_Name," & str8 & "Mx_Gg  ,Mx_Xsdw,Cf_Sl as Sl,(Cf_Dj*Cf_Sl)Xs_Money, (V_Ypkc.Yk_Cgj/Mx_Cfbl*Cf_sl)Cb_Money,(Cf_Dj-V_Ypkc.Yk_Cgj/Mx_Cfbl )* Cf_sl as lr_money from V_Ypkc,Bl_Cf,Bl_Cfyp,Bl_JZ where  Bl_Cf.Jz_Code=Bl_JZ.Jz_Code  " & str4 & "  And Dl_Code in " & V_Dl & "  and V_Ypkc.Xx_code=Bl_Cfyp.Xx_code  and Bl_Cf.Cf_Code=Bl_Cfyp.Cf_Code   and Jz_Date+Jz_Time>='" & Format(DateTimePicker1.Value, "yyyy-MM-dd HH:mm:ss") & "' and Jz_Date+Jz_Time<='" & Format(DateTimePicker2.Value, "yyyy-MM-dd HH:mm:ss") & "' "
                Else
                    zy = " select V_Ypkc.Xx_code,Yp_Name," & str8 & "Mx_Gg  ,Mx_Xsdw,Cf_Sl as Sl,(Cf_Dj*Cf_Sl)Xs_Money, (V_Ypkc.Yk_Cgj/Mx_Cfbl*Cf_sl)Cb_Money,(Cf_Dj-V_Ypkc.Yk_Cgj/Mx_Cfbl )* Cf_sl as lr_money from V_Ypkc,Bl_Cf,Bl_Cfyp,Bl_JZ,Bl where Bl.Bl_Code=Bl_Cf.Bl_Code   " & str4 & "  And Dl_Code in " & V_Dl & "  and Bl.Jz_Code=Bl_JZ.Jz_Code and V_Ypkc.Xx_code=Bl_Cfyp.Xx_code  and Bl_Cf.Cf_Code=Bl_Cfyp.Cf_Code   and Jz_Date+Jz_Time>='" & Format(DateTimePicker1.Value, "yyyy-MM-dd HH:mm:ss") & "' and Jz_Date+Jz_Time<='" & Format(DateTimePicker2.Value, "yyyy-MM-dd HH:mm:ss") & "' "
                End If
            End If
            If C1Combo3.Text = "全部" Or C1Combo3.Text = "门诊" Then
                If C1Combo3.Text = "全部" Then zy = zy & " UNION ALL "
                mz = " select V_Ypkc.Xx_code,Yp_Name," & str8 & "Mx_Gg  ,Mx_Xsdw,Mz_Sl as Sl,(Mz_Yp_Sum.Mz_Money)Xs_Money, (V_Ypkc.Yk_Cgj/Mx_Cfbl*Mz_sl)Cb_Money,(Mz_Yp_Sum.Mz_Money-V_Ypkc.Yk_Cgj/Mx_Cfbl* Mz_sl)  as lr_money from V_Ypkc,Mz_Sum,Mz_Yp_Sum,Mz_Jz where  Mz_sum.Jz_Code=Mz_Jz.Jz_Code  " & str4 & " and Mz_Ph+Mz_YP_Sum.Mz_Code Not In (Select Mz_Ph+Mz_Code From Mz_Ty_Sum) And  V_Ypkc.Xx_code=Mz_Yp_Sum.Xx_code  And Dl_Code in " & V_Dl & " and Jz_Date+Jz_Time>='" & Format(DateTimePicker1.Value, "yyyy-MM-dd HH:mm:ss") & "' and Jz_Date+Jz_Time<='" & Format(DateTimePicker2.Value, "yyyy-MM-dd HH:mm:ss") & "'    and Mz_Sum.Mz_Code=Mz_Yp_Sum.Mz_Code  "
            End If
            V_Str1 = " select   " & str2 & "  Yp_Name," & str8 & "Mx_Gg   ,Mx_Xsdw,Sum(Xs_Money)xs_money,Sum(Cb_Money)Cb_money,Sum(Lr_Money)Lr_money,case when Sum(Cb_money)=0 then 100 else Sum(Lr_money)/Sum(Cb_money)*100  end   AS Lrl  ,Sum(Sl) as Sl from (" & zy & mz & " )a group by Yp_Name," & str8 & "Mx_Gg,Mx_Xsdw " & str3

        ElseIf C1Combo2.Text = "按发药时间" Then
            If C1Combo3.Text = "全部" Or C1Combo3.Text = "住院" Then
                zy = " select V_Ypkc.Xx_code,Yp_Name," & str8 & "Mx_Gg  ,Mx_Xsdw,Cf_Sl as Sl,(Cf_Dj*Cf_Sl)Xs_Money, (V_Ypkc.Yk_Cgj/Mx_Cfbl*Cf_sl)Cb_Money,(Cf_Dj-V_Ypkc.Yk_Cgj/Mx_Cfbl )* Cf_sl as lr_money from V_Ypkc,Bl_Cf,Bl_Cfyp where  V_Ypkc.Xx_code=Bl_Cfyp.Xx_code  " & str4 & "  And Dl_Code in " & V_Dl & "   and Bl_Cf.Cf_Code=Bl_Cfyp.Cf_Code   and Cf_Qr_Date>='" & Format(DateTimePicker1.Value, "yyyy-MM-dd HH:mm:ss") & "' and Cf_Qr_Date<='" & Format(DateTimePicker2.Value, "yyyy-MM-dd HH:mm:ss") & "' and Cf_Qr='是' "
            End If
            If C1Combo3.Text = "全部" Or C1Combo3.Text = "门诊" Then
                If C1Combo3.Text = "全部" Then zy = zy & " UNION ALL "
                mz = "  select V_Ypkc.Xx_code,Yp_Name," & str8 & "Mx_Gg  ,Mx_Xsdw,Mz_Sl as Sl,(Mz_Yp_Sum.Mz_Money)Xs_Money, (V_Ypkc.Yk_Cgj/Mx_Cfbl*Mz_sl)Cb_Money,(Mz_Yp_Sum.Mz_Money-V_Ypkc.Yk_Cgj/Mx_Cfbl* Mz_sl)  as lr_money from V_Ypkc,Mz_Sum,Mz_Yp_Sum where  V_Ypkc.Xx_code=Mz_Yp_Sum.Xx_code   " & str4 & " and Mz_Ph+Mz_YP_Sum.Mz_Code Not In (Select Mz_Ph+Mz_Code From Mz_Ty_Sum)  And Dl_Code in " & V_Dl & "  and Mz_FyQr_Date>='" & Format(DateTimePicker1.Value, "yyyy-MM-dd HH:mm:ss") & "' and Mz_FyQr_Date<='" & Format(DateTimePicker2.Value, "yyyy-MM-dd HH:mm:ss") & "' And  mz_fyqr='1'   and Mz_Sum.Mz_Code=Mz_Yp_Sum.Mz_Code  " &
            " union all select V_Ypkc.Xx_code,Yp_Name," & str8 & "Mx_Gg  ,Mx_Xsdw,Mz_Sl as Sl,(Mz_Yp.Mz_Money)Xs_Money, (V_Ypkc.Yk_Cgj/Mx_Cfbl*Mz_sl)Cb_Money,(Mz_Yp.Mz_Money-V_Ypkc.Yk_Cgj/Mx_Cfbl* Mz_sl)  as lr_money from V_Ypkc,Mz,Mz_Yp where  V_Ypkc.Xx_code=Mz_Yp.Xx_code  " & str4 & "  and Mz_Ph+Mz_YP.Mz_Code Not In (Select Mz_Ph+Mz_Code From Mz_Ty) And  mz_fyqr='1' And Dl_Code in " & V_Dl & " and Mz_FyQr_Date>='" & Format(DateTimePicker1.Value, "yyyy-MM-dd HH:mm:ss") & "' and Mz_FyQr_Date<='" & Format(DateTimePicker2.Value, "yyyy-MM-dd HH:mm:ss") & "'   and Mz.Mz_Code=Mz_Yp.Mz_Code  "
            End If
            V_Str1 = "select   " & str2 & "  Yp_Name," & str8 & "Mx_Gg   ,Mx_Xsdw,Sum(Xs_Money)xs_money,Sum(Cb_Money)Cb_money,Sum(Lr_Money)Lr_money,case when Sum(Cb_money)=0 then 100 else Sum(Lr_money)/Sum(Cb_money)*100  end   AS Lrl  ,Sum(Sl) as Sl from (" & zy & mz & " )a group by Yp_Name," & str8 & "Mx_Gg,Mx_Xsdw " & str3

        Else
            If C1Combo3.Text = "全部" Or C1Combo3.Text = "住院" Then
                zy = "select V_Ypkc.Xx_code,Yp_Name," & str8 & "Mx_Gg  ,Mx_Xsdw,Cf_Sl as Sl,(Cf_Dj*Cf_Sl)Xs_Money, (V_Ypkc.Yk_Cgj/Mx_Cfbl*Cf_sl)Cb_Money,(Cf_Dj-V_Ypkc.Yk_Cgj/Mx_Cfbl )* Cf_sl as lr_money from V_Ypkc,Bl_Cf,Bl_Cfyp where   Bl_Cf.Cf_Code=Bl_Cfyp.Cf_Code     " & str4 & "  And Dl_Code in " & V_Dl & "   and V_Ypkc.Xx_code=Bl_Cfyp.Xx_code  and Cf_Date+Cf_Time>='" & Format(DateTimePicker1.Value, "yyyy-MM-dd HH:mm:ss") & "' and Cf_Date+Cf_Time<='" & Format(DateTimePicker2.Value, "yyyy-MM-dd HH:mm:ss") & "' and Cf_Qr='是' "
            End If
            If C1Combo3.Text = "全部" Or C1Combo3.Text = "门诊" Then
                If C1Combo3.Text = "全部" Then zy = zy & " UNION ALL "
                mz = "select V_Ypkc.Xx_code,Yp_Name," & str8 & "Mx_Gg  ,Mx_Xsdw,Mz_Sl as Sl,(Mz_Yp_Sum.Mz_Money)Xs_Money, (V_Ypkc.Yk_Cgj/Mx_Cfbl*Mz_sl)Cb_Money,(Mz_Yp_Sum.Mz_Money-V_Ypkc.Yk_Cgj/Mx_Cfbl* Mz_sl)  as lr_money from V_Ypkc,Mz_Sum,Mz_Yp_Sum where  V_Ypkc.Xx_code=Mz_Yp_Sum.Xx_code  " & str4 & " and Mz_Ph+Mz_YP_Sum.Mz_Code Not In (Select Mz_Ph+Mz_Code From Mz_Ty_Sum)   And Dl_Code in " & V_Dl & "  and Mz_Date+Mz_Time>='" & Format(DateTimePicker1.Value, "yyyy-MM-dd HH:mm:ss") & "' and Mz_Date+Mz_Time<='" & Format(DateTimePicker2.Value, "yyyy-MM-dd HH:mm:ss") & "' And  mz_fyqr='1'   and Mz_Sum.Mz_Code=Mz_Yp_Sum.Mz_Code  " &
                " union all select V_Ypkc.Xx_code,Yp_Name," & str8 & "Mx_Gg  ,Mx_Xsdw,Mz_Sl as Sl,(Mz_Yp.Mz_Money)Xs_Money, (V_Ypkc.Yk_Cgj/Mx_Cfbl*Mz_sl)Cb_Money,(Mz_Yp.Mz_Money-V_Ypkc.Yk_Cgj/Mx_Cfbl* Mz_sl)  as lr_money from V_Ypkc,Mz,Mz_Yp where  V_Ypkc.Xx_code=Mz_Yp.Xx_code  " & str4 & " and Mz_Ph+Mz_YP.Mz_Code Not In (Select Mz_Ph+Mz_Code From Mz_Ty) And  mz_fyqr='1'   And Dl_Code in " & V_Dl & " and Mz_Date+Mz_Time>='" & Format(DateTimePicker1.Value, "yyyy-MM-dd HH:mm:ss") & "' and Mz_Date+Mz_Time<='" & Format(DateTimePicker2.Value, "yyyy-MM-dd HH:mm:ss") & "'   and Mz.Mz_Code=Mz_Yp.Mz_Code "
            End If
            V_Str1 = "select   " & str2 & "  Yp_Name," & str8 & "Mx_Gg   ,Mx_Xsdw,Sum(Xs_Money)xs_money,Sum(Cb_Money)Cb_money,Sum(Lr_Money)Lr_money,case when Sum(Cb_money)=0 then 100 else Sum(Lr_money)/Sum(Cb_money)*100  end   AS Lrl  ,Sum(Sl) as Sl from (" & zy & mz & " )a group by Yp_Name," & str8 & "Mx_Gg,Mx_Xsdw " & str3
        End If



      

        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, V_Str1, "药品利润统计", True)


        With C1TrueDBGrid1
            My_Cm = CType(BindingContext(My_Dataset, "药品利润统计"), CurrencyManager)
            .SetDataBinding(My_Dataset, "药品利润统计", True)

            My_View = My_Cm.List

        End With

        Label4.Text = "记录条数 ∑=" + C1TrueDBGrid1.Splits(0).Rows.Count.ToString
        Call F_Sum()
        If My_Dataset.Tables("药品利润统计").Rows.Count = 0 Then MsgBox("没有符合条件的记录", MsgBoxStyle.Information, "提示") : Exit Sub


    End Sub

    Private Sub TreeView1_BeforeCollapse(ByVal sender As Object, ByVal e As System.Windows.Forms.TreeViewCancelEventArgs) Handles TreeView1.BeforeCollapse
        If e.Node.Tag = TreeView1.Nodes.Item(0).Tag Or e.Node.Tag = TreeView1.Nodes.Item(1).Tag Then e.Cancel = True
    End Sub

    Private Sub CheckBox1_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox1.CheckedChanged
        If CheckBox1.Checked = True Then
            C1TrueDBGrid1.Splits(0).DisplayColumns("Mx_Cd").Visible = True
            str8 = "Mx_Cd,"
        Else
            C1TrueDBGrid1.Splits(0).DisplayColumns("Mx_Cd").Visible = False
            str8 = ""
        End If
        If C1TrueDBGrid1.Splits(0).Rows.Count.ToString > 0 Then
            Call Button1()
        End If
    End Sub
    Private Sub C1Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Button1.Click
        If C1TrueDBGrid1.RowCount = 0 Then Exit Sub
        Dim StiRpt As New StiReport
        StiRpt.Load("Rpt\药品销售统计表.mrt")
        StiRpt.ReportName = "药品销售统计表"
        StiRpt.RegData(My_View)
        StiRpt.Compile()
        StiRpt("查询时间") = Format(DateTimePicker1.Value, "yyyy-MM-dd HH:mm:ss") & " 至 " & Format(DateTimePicker2.Value, "yyyy-MM-dd HH:mm:ss")
        StiRpt("打印时间") = Format(Now, "yyyy-MM-dd HH:mm:ss")
        StiRpt("标题") = "药品销售统计表"
        'StiRpt.Design()
        StiRpt.Show()
    End Sub
#Region "输入法设置"
    '中文
    Private Sub C1TextBox1_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles TextBox1.GotFocus
        InputLanguage.CurrentInputLanguage = HisVar.HisVar.InputCode
    End Sub

#End Region


    Private Sub C1Combo2_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Combo2.TextChanged
        If C1Combo2.Text = "按日结时间" Then
            C1Combo4.Enabled = True
        Else
            C1Combo4.Enabled = False
        End If
    End Sub


    Private Sub C1Combo3_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Combo3.TextChanged
        If C1Combo3.Text = "门诊" Then
            C1Combo4.Enabled = False
        Else
            C1Combo4.Enabled = True
        End If
    End Sub
End Class