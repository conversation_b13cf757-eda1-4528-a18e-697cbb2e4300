﻿/**  版本信息模板在安装目录下，可自行修改。
* M_Emr_ZkDj.cs
*
* 功 能： N/A
* 类 名： M_Emr_ZkDj
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/8/10 星期三 下午 3:41:49   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace ModelOld
{
	/// <summary>
	/// M_Emr_ZkDj:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class M_Emr_ZkDj
	{
		public M_Emr_ZkDj()
		{}
		#region Model
		private string _zkdj_code;
		private string _zkdj_name;
		private string _zkdj_jc;
		private decimal? _zkdj_maxvalue;
		private decimal? _zkdj_minvalue;
		private decimal? _zkdj_kf;
		private string _zkdj_memo;
		/// <summary>
		/// 
		/// </summary>
		public string ZkDj_Code
		{
			set{ _zkdj_code=value;}
			get{return _zkdj_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string ZkDj_Name
		{
			set{ _zkdj_name=value;}
			get{return _zkdj_name;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string ZkDj_Jc
		{
			set{ _zkdj_jc=value;}
			get{return _zkdj_jc;}
		}
		/// <summary>
		/// 
		/// </summary>
		public decimal? ZkDj_MaxValue
		{
			set{ _zkdj_maxvalue=value;}
			get{return _zkdj_maxvalue;}
		}
		/// <summary>
		/// 
		/// </summary>
		public decimal? ZkDj_MinValue
		{
			set{ _zkdj_minvalue=value;}
			get{return _zkdj_minvalue;}
		}
		/// <summary>
		/// 
		/// </summary>
		public decimal? ZkDj_Kf
		{
			set{ _zkdj_kf=value;}
			get{return _zkdj_kf;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string ZkDj_Memo
		{
			set{ _zkdj_memo=value;}
			get{return _zkdj_memo;}
		}
		#endregion Model

	}
}

