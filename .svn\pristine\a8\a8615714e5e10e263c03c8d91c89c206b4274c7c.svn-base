﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="0" />
    <DataSources isList="true" count="1">
      <患者费用统计表 Ref="2" type="DataTableSource" isKey="true">
        <Alias>患者费用统计表</Alias>
        <Columns isList="true" count="7">
          <value>Ks_Name,System.String</value>
          <value>Dl_Code,System.String</value>
          <value>Dl_Name,System.String</value>
          <value>Ry_Name,System.String</value>
          <value>Yp_Name,System.String</value>
          <value>Cf_Money,System.Decimal</value>
          <value>Bxlb_Name,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>患者费用统计表</Name>
        <NameInSource>患者费用统计表</NameInSource>
      </患者费用统计表>
    </DataSources>
    <Relations isList="true" count="0" />
    <Report isRef="0" />
    <Variables isList="true" count="3">
      <value>,标题,标题,System.String,,False,False</value>
      <value>,查询时间,查询时间,System.String,,False,False</value>
      <value>,打印时间,打印时间,System.String,,False,False</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="3" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="2">
        <PageHeaderBand1 Ref="4" type="PageHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,27.7,1.2</ClientRectangle>
          <Components isList="true" count="3">
            <Text1 Ref="5" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,27.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,14.25,Bold,Point,False,0</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text1</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>{标题}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text1>
            <Text2 Ref="6" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0.2,0.6,13.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text2</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>{查询时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text2>
            <Text3 Ref="7" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>20.2,0.6,7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text3</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>{打印时间} </Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text3>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>PageHeaderBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </PageHeaderBand1>
        <CrossTab1 Ref="8" type="Stimulsoft.Report.CrossTab.StiCrossTab" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,1.6,27.7,17.9</ClientRectangle>
          <Components isList="true" count="14">
            <CrossTab1_RowTotal2 Ref="9" type="CrossRowTotal" isKey="true">
              <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
              <Brush>[255:255:255]</Brush>
              <ClientRectangle>1.6,2.1,3.2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>89ab0b1c653b44b2925729915aac4bbe</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>CrossTab1_RowTotal2</Name>
              <Page isRef="3" />
              <Parent isRef="8" />
              <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
              <Text>合计</Text>
              <TextBrush>Black</TextBrush>
            </CrossTab1_RowTotal2>
            <CrossTab1_Row1_Title Ref="10" type="CrossTitle" isKey="true">
              <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
              <Brush>White</Brush>
              <ClientRectangle>0,0.55,1.6,0.5</ClientRectangle>
              <Font>宋体,10.5</Font>
              <Guid>149a816e89c6425889504f879d39b481</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>CrossTab1_Row1_Title</Name>
              <Page isRef="3" />
              <Parent isRef="8" />
              <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
              <Text>患者类别</Text>
              <TextBrush>[105:105:105]</TextBrush>
              <Type>Expression</Type>
              <TypeOfComponent>Row:CrossTab1_Row1</TypeOfComponent>
            </CrossTab1_Row1_Title>
            <CrossTab1_RowTotal3 Ref="11" type="CrossRowTotal" isKey="true">
              <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
              <Brush>[255:255:255]</Brush>
              <ClientRectangle>3.2,1.6,1.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>4dfbb6746e3040779327ae063761335d</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>CrossTab1_RowTotal3</Name>
              <Page isRef="3" />
              <Parent isRef="8" />
              <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
              <Text>小计</Text>
              <TextBrush>Black</TextBrush>
            </CrossTab1_RowTotal3>
            <CrossTab1_Row2_Title Ref="12" type="CrossTitle" isKey="true">
              <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
              <Brush>White</Brush>
              <ClientRectangle>1.6,0.55,1.6,0.5</ClientRectangle>
              <Font>宋体,10.5</Font>
              <Guid>860208d6764e410a924df79f47266a87</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>CrossTab1_Row2_Title</Name>
              <Page isRef="3" />
              <Parent isRef="8" />
              <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
              <Text>科室</Text>
              <TextBrush>[105:105:105]</TextBrush>
              <Type>Expression</Type>
              <TypeOfComponent>Row:CrossTab1_Row2</TypeOfComponent>
            </CrossTab1_Row2_Title>
            <CrossTab1_ColTotal1 Ref="13" type="CrossColumnTotal" isKey="true">
              <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
              <Brush>[255:255:255]</Brush>
              <ClientRectangle>6.45,0.55,0.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <Guid>1931e6b78b70499b981f560adf20023e</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>CrossTab1_ColTotal1</Name>
              <Page isRef="3" />
              <Parent isRef="8" />
              <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
              <Text>合计</Text>
              <TextBrush>Black</TextBrush>
            </CrossTab1_ColTotal1>
            <CrossTab1_LeftTitle Ref="14" type="CrossTitle" isKey="true">
              <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
              <Brush>White</Brush>
              <ClientRectangle>0,0,4.8,0.5</ClientRectangle>
              <Font>宋体,10.5</Font>
              <Guid>012d6a5342aa4fb7b8f199c4c4fdea4b</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>CrossTab1_LeftTitle</Name>
              <Page isRef="3" />
              <Parent isRef="8" />
              <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
              <Text>患者费用统计表</Text>
              <TextBrush>[105:105:105]</TextBrush>
              <TypeOfComponent>LeftTitle</TypeOfComponent>
            </CrossTab1_LeftTitle>
            <CrossTab1_RowTotal1 Ref="15" type="CrossRowTotal" isKey="true">
              <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
              <Brush>[255:255:255]</Brush>
              <ClientRectangle>0,2.6,4.8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,10.5,Bold,Point,False,0</Font>
              <Guid>6bad6dac02854274a563eafedde0ae1a</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>CrossTab1_RowTotal1</Name>
              <Page isRef="3" />
              <Parent isRef="8" />
              <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
              <Text>总计</Text>
              <TextBrush>Black</TextBrush>
            </CrossTab1_RowTotal1>
            <CrossTab1_Row3_Title Ref="16" type="CrossTitle" isKey="true">
              <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
              <Brush>White</Brush>
              <ClientRectangle>3.2,0.55,1.6,0.5</ClientRectangle>
              <Font>Arial,10.5,Regular,Point,False,0</Font>
              <Guid>cce259cb73bb4aab8767b8fd059dbda4</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>CrossTab1_Row3_Title</Name>
              <Page isRef="3" />
              <Parent isRef="8" />
              <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
              <Text>患者姓名</Text>
              <TextBrush>[105:105:105]</TextBrush>
              <Type>Expression</Type>
              <TypeOfComponent>Row:CrossTab1_Row3</TypeOfComponent>
            </CrossTab1_Row3_Title>
            <CrossTab1_Row1 Ref="17" type="CrossRow" isKey="true">
              <Alias>Bxlb_Name</Alias>
              <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
              <Brush>White</Brush>
              <ClientRectangle>0,1.1,1.6,1.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <DisplayValue>{患者费用统计表.Bxlb_Name}</DisplayValue>
              <Font>Arial,8</Font>
              <Guid>78a4a708e20e455c812babe2b1c7d933</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>CrossTab1_Row1</Name>
              <Page isRef="3" />
              <Parent isRef="8" />
              <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
              <Text>Bxlb_Name</Text>
              <TextBrush>[105:105:105]</TextBrush>
              <TotalGuid>6bad6dac02854274a563eafedde0ae1a</TotalGuid>
              <Value>{患者费用统计表.Bxlb_Name}</Value>
            </CrossTab1_Row1>
            <CrossTab1_Row2 Ref="18" type="CrossRow" isKey="true">
              <Alias>Ks_Name</Alias>
              <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
              <Brush>White</Brush>
              <ClientRectangle>1.6,1.1,1.6,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <DisplayValue>{患者费用统计表.Ks_Name}</DisplayValue>
              <Font>宋体,10.5</Font>
              <Guid>4dfab9a48f484b4880d0dd6ed5b9c351</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>CrossTab1_Row2</Name>
              <Page isRef="3" />
              <Parent isRef="8" />
              <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
              <Text>Ks_Name</Text>
              <TextBrush>[105:105:105]</TextBrush>
              <TotalGuid>89ab0b1c653b44b2925729915aac4bbe</TotalGuid>
              <Value>{患者费用统计表.Ks_Name}</Value>
            </CrossTab1_Row2>
            <CrossTab1_Row3 Ref="19" type="CrossRow" isKey="true">
              <Alias>Ry_Name</Alias>
              <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
              <Brush>White</Brush>
              <ClientRectangle>3.2,1.1,1.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <DisplayValue>{患者费用统计表.Ry_Name}</DisplayValue>
              <Font>宋体,10.5</Font>
              <Guid>1bd9e71551bd40b19dc1c4fd81113d7b</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>CrossTab1_Row3</Name>
              <Page isRef="3" />
              <Parent isRef="8" />
              <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
              <Text>Ry_Name</Text>
              <TextBrush>[105:105:105]</TextBrush>
              <TotalGuid>4dfbb6746e3040779327ae063761335d</TotalGuid>
              <Value>{患者费用统计表.Ry_Name}</Value>
            </CrossTab1_Row3>
            <CrossTab1_Column1 Ref="20" type="CrossColumn" isKey="true">
              <Alias>Dl_Name</Alias>
              <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
              <Brush>White</Brush>
              <ClientRectangle>4.85,0.55,1.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <DisplayValue>{患者费用统计表.Dl_Name}</DisplayValue>
              <Font>宋体,10.5</Font>
              <Guid>de17711f7c2f4b25a886bd1fc0c30c35</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>CrossTab1_Column1</Name>
              <Page isRef="3" />
              <Parent isRef="8" />
              <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
              <SortType>ByValue</SortType>
              <Text>Dl_Name</Text>
              <TextBrush>[105:105:105]</TextBrush>
              <TotalGuid>1931e6b78b70499b981f560adf20023e</TotalGuid>
              <Value>{患者费用统计表.Dl_Code}</Value>
            </CrossTab1_Column1>
            <CrossTab1_Sum1 Ref="21" type="CrossSummary" isKey="true">
              <Alias>Cf_Money</Alias>
              <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
              <Brush>[255:255:255]</Brush>
              <ClientRectangle>4.85,1.1,1.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9.75,Regular,Point,False,0</Font>
              <Guid>4be890a3dfb7457186b0c5f1a2c20425</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>CrossTab1_Sum1</Name>
              <Page isRef="3" />
              <Parent isRef="8" />
              <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
              <Text>0</Text>
              <TextBrush>Black</TextBrush>
              <Value>{患者费用统计表.Cf_Money}</Value>
            </CrossTab1_Sum1>
            <CrossTab1_RightTitle Ref="22" type="CrossTitle" isKey="true">
              <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
              <Brush>White</Brush>
              <ClientRectangle>4.85,0,2.5,0.5</ClientRectangle>
              <Font>Arial,9.75,Regular,Point,False,0</Font>
              <Guid>eafb7208d68042458109a1ad06bb38ee</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>CrossTab1_RightTitle</Name>
              <Page isRef="3" />
              <Parent isRef="8" />
              <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
              <Text>费用类别</Text>
              <TextBrush>[105:105:105]</TextBrush>
              <Type>Expression</Type>
              <TypeOfComponent>RightTitle</TypeOfComponent>
            </CrossTab1_RightTitle>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName />
          <DataSourceName>患者费用统计表</DataSourceName>
          <DockStyle>Bottom</DockStyle>
          <EmptyValue />
          <Filters isList="true" count="0" />
          <GrowToHeight>True</GrowToHeight>
          <HorAlignment>Width</HorAlignment>
          <Name>CrossTab1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Sort isList="true" count="0" />
        </CrossTab1>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>ab4a11e050e84831915bf6663408f906</Guid>
      <Margins>1,1,0.5,1</Margins>
      <Name>Page1</Name>
      <Orientation>Landscape</Orientation>
      <PageHeight>21</PageHeight>
      <PageWidth>29.7</PageWidth>
      <PaperSize>A4</PaperSize>
      <Report isRef="0" />
      <Watermark Ref="23" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page1>
  </Pages>
  <PrinterSettings Ref="24" type="Stimulsoft.Report.Print.StiPrinterSettings" isKey="true" />
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>患者费用统计表</ReportAlias>
  <ReportChanged>8/24/2013 3:17:16 PM</ReportChanged>
  <ReportCreated>12/6/2011 4:42:32 PM</ReportCreated>
  <ReportFile>D:\正在进行时\唐山\正在修改版\his2010\his2010\Rpt\患者费用统计表.mrt</ReportFile>
  <ReportGuid>77fbf5d7689140f4ad7ae74bd4018f98</ReportGuid>
  <ReportName>患者费用统计表</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2011.2.1026</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class Report : Stimulsoft.Report.StiReport
    {
        public Report()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
        #endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>