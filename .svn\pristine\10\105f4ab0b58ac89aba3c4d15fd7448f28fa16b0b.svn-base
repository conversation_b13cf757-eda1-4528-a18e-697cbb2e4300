﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class MOISCondition
    Inherits HisControl.BaseChild

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.CXZTSingleComobo = New CustomControl.MySingleComobo()
        Me.JSRDtComobo = New CustomControl.MyDtComobo()
        Me.RKLXDtComobo = New CustomControl.MyDtComobo()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.MyButton1 = New CustomControl.MyButton()
        Me.QXButton = New CustomControl.MyButton()
        Me.QDButton = New CustomControl.MyButton()
        Me.KFDtComobo = New CustomControl.MyDtComobo()
        Me.WZMCDtComobo = New CustomControl.MyDtComobo()
        Me.DJZTSingleComobo = New CustomControl.MySingleComobo()
        Me.WZPHTextBox1 = New CustomControl.MyTextBox()
        Me.dhCheckBox1 = New System.Windows.Forms.CheckBox()
        Me.lrCheckBox3 = New System.Windows.Forms.CheckBox()
        Me.wcCheckBox4 = New System.Windows.Forms.CheckBox()
        Me.dqCheckBox5 = New System.Windows.Forms.CheckBox()
        Me.RKDoubleDateEdit = New CustomControl.DoubleDateEdit()
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel()
        Me.LrDoubleDateEdit = New CustomControl.DoubleDateEdit()
        Me.WCDoubleDateEdit = New CustomControl.DoubleDateEdit()
        Me.DQDoubleDateEdit = New CustomControl.DoubleDateEdit()
        Me.WZLBDtComobo1 = New CustomControl.MyDtComobo()
        Me.RKDHTextBox = New CustomControl.MyTextBox()
        Me.SCCJTextBox1 = New CustomControl.MyTextBox()
        Me.GGTextBox = New CustomControl.MyTextBox()
        Me.Panel1.SuspendLayout()
        Me.TableLayoutPanel1.SuspendLayout()
        Me.SuspendLayout()
        '
        'CXZTSingleComobo
        '
        Me.CXZTSingleComobo.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.CXZTSingleComobo.Captain = "冲销状态"
        Me.CXZTSingleComobo.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.CXZTSingleComobo.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.CXZTSingleComobo.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.CXZTSingleComobo, 2)
        Me.CXZTSingleComobo.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        Me.CXZTSingleComobo.ItemHeight = 16
        Me.CXZTSingleComobo.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.CXZTSingleComobo.Location = New System.Drawing.Point(259, 177)
        Me.CXZTSingleComobo.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.CXZTSingleComobo.MinimumSize = New System.Drawing.Size(0, 20)
        Me.CXZTSingleComobo.Name = "CXZTSingleComobo"
        Me.CXZTSingleComobo.ReadOnly = False
        Me.CXZTSingleComobo.Size = New System.Drawing.Size(242, 20)
        Me.CXZTSingleComobo.TabIndex = 1
        Me.CXZTSingleComobo.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'JSRDtComobo
        '
        Me.JSRDtComobo.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.JSRDtComobo.Captain = "经手人"
        Me.JSRDtComobo.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.JSRDtComobo.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.JSRDtComobo.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.JSRDtComobo, 2)
        Me.JSRDtComobo.DataSource = Nothing
        Me.JSRDtComobo.ItemHeight = 18
        Me.JSRDtComobo.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.JSRDtComobo.Location = New System.Drawing.Point(259, 149)
        Me.JSRDtComobo.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.JSRDtComobo.MinimumSize = New System.Drawing.Size(0, 20)
        Me.JSRDtComobo.Name = "JSRDtComobo"
        Me.JSRDtComobo.ReadOnly = False
        Me.JSRDtComobo.Size = New System.Drawing.Size(242, 20)
        Me.JSRDtComobo.TabIndex = 0
        Me.JSRDtComobo.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'RKLXDtComobo
        '
        Me.RKLXDtComobo.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.RKLXDtComobo.Captain = "入库类型"
        Me.RKLXDtComobo.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.RKLXDtComobo.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.RKLXDtComobo.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.RKLXDtComobo, 2)
        Me.RKLXDtComobo.DataSource = Nothing
        Me.RKLXDtComobo.ItemHeight = 18
        Me.RKLXDtComobo.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.RKLXDtComobo.Location = New System.Drawing.Point(259, 121)
        Me.RKLXDtComobo.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.RKLXDtComobo.MinimumSize = New System.Drawing.Size(0, 20)
        Me.RKLXDtComobo.Name = "RKLXDtComobo"
        Me.RKLXDtComobo.ReadOnly = False
        Me.RKLXDtComobo.Size = New System.Drawing.Size(242, 20)
        Me.RKLXDtComobo.TabIndex = 0
        Me.RKLXDtComobo.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'Panel1
        '
        Me.TableLayoutPanel1.SetColumnSpan(Me.Panel1, 2)
        Me.Panel1.Controls.Add(Me.MyButton1)
        Me.Panel1.Controls.Add(Me.QXButton)
        Me.Panel1.Controls.Add(Me.QDButton)
        Me.Panel1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel1.Location = New System.Drawing.Point(31, 288)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(450, 34)
        Me.Panel1.TabIndex = 6
        '
        'MyButton1
        '
        Me.MyButton1.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.MyButton1.DialogResult = System.Windows.Forms.DialogResult.None
        Me.MyButton1.Location = New System.Drawing.Point(292, 3)
        Me.MyButton1.Name = "MyButton1"
        Me.MyButton1.Size = New System.Drawing.Size(60, 27)
        Me.MyButton1.TabIndex = 1
        Me.MyButton1.Text = "清空"
        '
        'QXButton
        '
        Me.QXButton.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.QXButton.DialogResult = System.Windows.Forms.DialogResult.Cancel
        Me.QXButton.Location = New System.Drawing.Point(196, 3)
        Me.QXButton.Name = "QXButton"
        Me.QXButton.Size = New System.Drawing.Size(60, 27)
        Me.QXButton.TabIndex = 0
        Me.QXButton.Text = "取消"
        '
        'QDButton
        '
        Me.QDButton.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.QDButton.DialogResult = System.Windows.Forms.DialogResult.OK
        Me.QDButton.Location = New System.Drawing.Point(104, 3)
        Me.QDButton.Name = "QDButton"
        Me.QDButton.Size = New System.Drawing.Size(60, 27)
        Me.QDButton.TabIndex = 0
        Me.QDButton.Text = "确定"
        '
        'KFDtComobo
        '
        Me.KFDtComobo.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.KFDtComobo.Captain = "库    房"
        Me.KFDtComobo.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.KFDtComobo.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.KFDtComobo.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.KFDtComobo, 2)
        Me.KFDtComobo.DataSource = Nothing
        Me.KFDtComobo.ItemHeight = 18
        Me.KFDtComobo.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.KFDtComobo.Location = New System.Drawing.Point(11, 121)
        Me.KFDtComobo.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.KFDtComobo.MinimumSize = New System.Drawing.Size(0, 20)
        Me.KFDtComobo.Name = "KFDtComobo"
        Me.KFDtComobo.ReadOnly = False
        Me.KFDtComobo.Size = New System.Drawing.Size(242, 20)
        Me.KFDtComobo.TabIndex = 1
        Me.KFDtComobo.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'WZMCDtComobo
        '
        Me.WZMCDtComobo.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.WZMCDtComobo.Captain = "物资名称"
        Me.WZMCDtComobo.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.WZMCDtComobo.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.WZMCDtComobo.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.WZMCDtComobo, 2)
        Me.WZMCDtComobo.DataSource = Nothing
        Me.WZMCDtComobo.ItemHeight = 18
        Me.WZMCDtComobo.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.WZMCDtComobo.Location = New System.Drawing.Point(11, 149)
        Me.WZMCDtComobo.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.WZMCDtComobo.MinimumSize = New System.Drawing.Size(0, 20)
        Me.WZMCDtComobo.Name = "WZMCDtComobo"
        Me.WZMCDtComobo.ReadOnly = False
        Me.WZMCDtComobo.Size = New System.Drawing.Size(242, 20)
        Me.WZMCDtComobo.TabIndex = 1
        Me.WZMCDtComobo.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'DJZTSingleComobo
        '
        Me.DJZTSingleComobo.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.DJZTSingleComobo.Captain = "单据状态"
        Me.DJZTSingleComobo.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.DJZTSingleComobo.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.DJZTSingleComobo.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.DJZTSingleComobo, 2)
        Me.DJZTSingleComobo.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        Me.DJZTSingleComobo.ItemHeight = 16
        Me.DJZTSingleComobo.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.DJZTSingleComobo.Location = New System.Drawing.Point(11, 177)
        Me.DJZTSingleComobo.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.DJZTSingleComobo.MinimumSize = New System.Drawing.Size(0, 20)
        Me.DJZTSingleComobo.Name = "DJZTSingleComobo"
        Me.DJZTSingleComobo.ReadOnly = False
        Me.DJZTSingleComobo.Size = New System.Drawing.Size(242, 20)
        Me.DJZTSingleComobo.TabIndex = 1
        Me.DJZTSingleComobo.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'WZPHTextBox1
        '
        Me.WZPHTextBox1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.WZPHTextBox1.Captain = "物资批号"
        Me.WZPHTextBox1.CaptainBackColor = System.Drawing.Color.Transparent
        Me.WZPHTextBox1.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.WZPHTextBox1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.WZPHTextBox1.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.WZPHTextBox1, 2)
        Me.WZPHTextBox1.ContentForeColor = System.Drawing.Color.Black
        Me.WZPHTextBox1.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.WZPHTextBox1.Location = New System.Drawing.Point(11, 205)
        Me.WZPHTextBox1.Multiline = False
        Me.WZPHTextBox1.Name = "WZPHTextBox1"
        Me.WZPHTextBox1.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.WZPHTextBox1.ReadOnly = False
        Me.WZPHTextBox1.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.WZPHTextBox1.SelectionStart = 0
        Me.WZPHTextBox1.SelectStart = 0
        Me.WZPHTextBox1.Size = New System.Drawing.Size(242, 20)
        Me.WZPHTextBox1.TabIndex = 4
        Me.WZPHTextBox1.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.WZPHTextBox1.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.WZPHTextBox1.Watermark = Nothing
        '
        'dhCheckBox1
        '
        Me.dhCheckBox1.AutoSize = True
        Me.dhCheckBox1.Location = New System.Drawing.Point(11, 8)
        Me.dhCheckBox1.Name = "dhCheckBox1"
        Me.dhCheckBox1.Size = New System.Drawing.Size(14, 14)
        Me.dhCheckBox1.TabIndex = 7
        Me.dhCheckBox1.UseVisualStyleBackColor = True
        '
        'lrCheckBox3
        '
        Me.lrCheckBox3.AutoSize = True
        Me.lrCheckBox3.Location = New System.Drawing.Point(11, 36)
        Me.lrCheckBox3.Name = "lrCheckBox3"
        Me.lrCheckBox3.Size = New System.Drawing.Size(14, 14)
        Me.lrCheckBox3.TabIndex = 7
        Me.lrCheckBox3.UseVisualStyleBackColor = True
        '
        'wcCheckBox4
        '
        Me.wcCheckBox4.AutoSize = True
        Me.wcCheckBox4.Location = New System.Drawing.Point(11, 64)
        Me.wcCheckBox4.Name = "wcCheckBox4"
        Me.wcCheckBox4.Size = New System.Drawing.Size(14, 14)
        Me.wcCheckBox4.TabIndex = 7
        Me.wcCheckBox4.UseVisualStyleBackColor = True
        '
        'dqCheckBox5
        '
        Me.dqCheckBox5.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.dqCheckBox5.AutoSize = True
        Me.dqCheckBox5.Location = New System.Drawing.Point(11, 96)
        Me.dqCheckBox5.Name = "dqCheckBox5"
        Me.dqCheckBox5.Size = New System.Drawing.Size(14, 14)
        Me.dqCheckBox5.TabIndex = 7
        Me.dqCheckBox5.UseVisualStyleBackColor = True
        '
        'RKDoubleDateEdit
        '
        Me.RKDoubleDateEdit.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.RKDoubleDateEdit.Captain = "入库日期"
        Me.RKDoubleDateEdit.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.RKDoubleDateEdit, 3)
        Me.RKDoubleDateEdit.Location = New System.Drawing.Point(28, 8)
        Me.RKDoubleDateEdit.Margin = New System.Windows.Forms.Padding(0)
        Me.RKDoubleDateEdit.MaximumSize = New System.Drawing.Size(1000000000, 22)
        Me.RKDoubleDateEdit.MinimumSize = New System.Drawing.Size(0, 22)
        Me.RKDoubleDateEdit.Name = "RKDoubleDateEdit"
        Me.RKDoubleDateEdit.Size = New System.Drawing.Size(476, 22)
        Me.RKDoubleDateEdit.TabIndex = 8
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.ColumnCount = 6
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 8.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 20.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 50.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 50.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 20.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 8.0!))
        Me.TableLayoutPanel1.Controls.Add(Me.CXZTSingleComobo, 3, 7)
        Me.TableLayoutPanel1.Controls.Add(Me.JSRDtComobo, 3, 6)
        Me.TableLayoutPanel1.Controls.Add(Me.RKLXDtComobo, 3, 5)
        Me.TableLayoutPanel1.Controls.Add(Me.Panel1, 2, 11)
        Me.TableLayoutPanel1.Controls.Add(Me.KFDtComobo, 1, 5)
        Me.TableLayoutPanel1.Controls.Add(Me.WZMCDtComobo, 1, 6)
        Me.TableLayoutPanel1.Controls.Add(Me.DJZTSingleComobo, 1, 7)
        Me.TableLayoutPanel1.Controls.Add(Me.WZPHTextBox1, 1, 8)
        Me.TableLayoutPanel1.Controls.Add(Me.dhCheckBox1, 1, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.lrCheckBox3, 1, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.wcCheckBox4, 1, 3)
        Me.TableLayoutPanel1.Controls.Add(Me.dqCheckBox5, 1, 4)
        Me.TableLayoutPanel1.Controls.Add(Me.RKDoubleDateEdit, 2, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.LrDoubleDateEdit, 2, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.WCDoubleDateEdit, 2, 3)
        Me.TableLayoutPanel1.Controls.Add(Me.DQDoubleDateEdit, 2, 4)
        Me.TableLayoutPanel1.Controls.Add(Me.WZLBDtComobo1, 3, 8)
        Me.TableLayoutPanel1.Controls.Add(Me.RKDHTextBox, 1, 9)
        Me.TableLayoutPanel1.Controls.Add(Me.SCCJTextBox1, 3, 9)
        Me.TableLayoutPanel1.Controls.Add(Me.GGTextBox, 1, 10)
        Me.TableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(0, 0)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.RowCount = 13
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 5.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 40.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 5.0!))
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(512, 329)
        Me.TableLayoutPanel1.TabIndex = 1
        '
        'LrDoubleDateEdit
        '
        Me.LrDoubleDateEdit.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LrDoubleDateEdit.Captain = "录入时间"
        Me.LrDoubleDateEdit.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.LrDoubleDateEdit, 3)
        Me.LrDoubleDateEdit.Location = New System.Drawing.Point(28, 36)
        Me.LrDoubleDateEdit.Margin = New System.Windows.Forms.Padding(0)
        Me.LrDoubleDateEdit.MaximumSize = New System.Drawing.Size(1000000000, 22)
        Me.LrDoubleDateEdit.MinimumSize = New System.Drawing.Size(0, 22)
        Me.LrDoubleDateEdit.Name = "LrDoubleDateEdit"
        Me.LrDoubleDateEdit.Size = New System.Drawing.Size(476, 22)
        Me.LrDoubleDateEdit.TabIndex = 8
        '
        'WCDoubleDateEdit
        '
        Me.WCDoubleDateEdit.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.WCDoubleDateEdit.Captain = "完成时间"
        Me.WCDoubleDateEdit.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.WCDoubleDateEdit, 3)
        Me.WCDoubleDateEdit.Location = New System.Drawing.Point(28, 64)
        Me.WCDoubleDateEdit.Margin = New System.Windows.Forms.Padding(0)
        Me.WCDoubleDateEdit.MaximumSize = New System.Drawing.Size(1000000000, 22)
        Me.WCDoubleDateEdit.MinimumSize = New System.Drawing.Size(0, 22)
        Me.WCDoubleDateEdit.Name = "WCDoubleDateEdit"
        Me.WCDoubleDateEdit.Size = New System.Drawing.Size(476, 22)
        Me.WCDoubleDateEdit.TabIndex = 8
        '
        'DQDoubleDateEdit
        '
        Me.DQDoubleDateEdit.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.DQDoubleDateEdit.Captain = "药品有效期"
        Me.DQDoubleDateEdit.CaptainWidth = 80.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.DQDoubleDateEdit, 3)
        Me.DQDoubleDateEdit.Location = New System.Drawing.Point(28, 92)
        Me.DQDoubleDateEdit.Margin = New System.Windows.Forms.Padding(0)
        Me.DQDoubleDateEdit.MaximumSize = New System.Drawing.Size(1000000000, 22)
        Me.DQDoubleDateEdit.MinimumSize = New System.Drawing.Size(0, 22)
        Me.DQDoubleDateEdit.Name = "DQDoubleDateEdit"
        Me.DQDoubleDateEdit.Size = New System.Drawing.Size(476, 22)
        Me.DQDoubleDateEdit.TabIndex = 8
        '
        'WZLBDtComobo1
        '
        Me.WZLBDtComobo1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.WZLBDtComobo1.Captain = "物资类别"
        Me.WZLBDtComobo1.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.WZLBDtComobo1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.WZLBDtComobo1.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.WZLBDtComobo1, 2)
        Me.WZLBDtComobo1.DataSource = Nothing
        Me.WZLBDtComobo1.ItemHeight = 18
        Me.WZLBDtComobo1.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.WZLBDtComobo1.Location = New System.Drawing.Point(259, 205)
        Me.WZLBDtComobo1.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.WZLBDtComobo1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.WZLBDtComobo1.Name = "WZLBDtComobo1"
        Me.WZLBDtComobo1.ReadOnly = False
        Me.WZLBDtComobo1.Size = New System.Drawing.Size(242, 20)
        Me.WZLBDtComobo1.TabIndex = 0
        Me.WZLBDtComobo1.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'RKDHTextBox
        '
        Me.RKDHTextBox.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.RKDHTextBox.Captain = "入库单号"
        Me.RKDHTextBox.CaptainBackColor = System.Drawing.Color.Transparent
        Me.RKDHTextBox.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.RKDHTextBox.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.RKDHTextBox.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.RKDHTextBox, 2)
        Me.RKDHTextBox.ContentForeColor = System.Drawing.Color.Black
        Me.RKDHTextBox.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.RKDHTextBox.Location = New System.Drawing.Point(11, 233)
        Me.RKDHTextBox.Multiline = False
        Me.RKDHTextBox.Name = "RKDHTextBox"
        Me.RKDHTextBox.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.RKDHTextBox.ReadOnly = False
        Me.RKDHTextBox.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.RKDHTextBox.SelectionStart = 0
        Me.RKDHTextBox.SelectStart = 0
        Me.RKDHTextBox.Size = New System.Drawing.Size(242, 20)
        Me.RKDHTextBox.TabIndex = 4
        Me.RKDHTextBox.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.RKDHTextBox.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.RKDHTextBox.Watermark = Nothing
        '
        'SCCJTextBox1
        '
        Me.SCCJTextBox1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SCCJTextBox1.Captain = "生产厂家"
        Me.SCCJTextBox1.CaptainBackColor = System.Drawing.Color.Transparent
        Me.SCCJTextBox1.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.SCCJTextBox1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.SCCJTextBox1.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.SCCJTextBox1, 2)
        Me.SCCJTextBox1.ContentForeColor = System.Drawing.Color.Black
        Me.SCCJTextBox1.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.SCCJTextBox1.Location = New System.Drawing.Point(259, 233)
        Me.SCCJTextBox1.Multiline = False
        Me.SCCJTextBox1.Name = "SCCJTextBox1"
        Me.SCCJTextBox1.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.SCCJTextBox1.ReadOnly = False
        Me.SCCJTextBox1.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.SCCJTextBox1.SelectionStart = 0
        Me.SCCJTextBox1.SelectStart = 0
        Me.SCCJTextBox1.Size = New System.Drawing.Size(242, 20)
        Me.SCCJTextBox1.TabIndex = 12
        Me.SCCJTextBox1.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.SCCJTextBox1.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.SCCJTextBox1.Watermark = Nothing
        '
        'GGTextBox
        '
        Me.GGTextBox.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GGTextBox.Captain = "规    格"
        Me.GGTextBox.CaptainBackColor = System.Drawing.Color.Transparent
        Me.GGTextBox.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.GGTextBox.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.GGTextBox.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.GGTextBox, 2)
        Me.GGTextBox.ContentForeColor = System.Drawing.Color.Black
        Me.GGTextBox.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.GGTextBox.Location = New System.Drawing.Point(11, 261)
        Me.GGTextBox.Multiline = False
        Me.GGTextBox.Name = "GGTextBox"
        Me.GGTextBox.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.GGTextBox.ReadOnly = False
        Me.GGTextBox.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.GGTextBox.SelectionStart = 0
        Me.GGTextBox.SelectStart = 0
        Me.GGTextBox.Size = New System.Drawing.Size(242, 20)
        Me.GGTextBox.TabIndex = 13
        Me.GGTextBox.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.GGTextBox.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.GGTextBox.Watermark = Nothing
        '
        'MOISCondition
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(512, 329)
        Me.Controls.Add(Me.TableLayoutPanel1)
        Me.Name = "MOISCondition"
        Me.Text = "物资其他入库查询条件"
        Me.Panel1.ResumeLayout(False)
        Me.TableLayoutPanel1.ResumeLayout(False)
        Me.TableLayoutPanel1.PerformLayout()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents CXZTSingleComobo As CustomControl.MySingleComobo
    Friend WithEvents TableLayoutPanel1 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents JSRDtComobo As CustomControl.MyDtComobo
    Friend WithEvents RKLXDtComobo As CustomControl.MyDtComobo
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents QXButton As CustomControl.MyButton
    Friend WithEvents QDButton As CustomControl.MyButton
    Friend WithEvents KFDtComobo As CustomControl.MyDtComobo
    Friend WithEvents WZMCDtComobo As CustomControl.MyDtComobo
    Friend WithEvents DJZTSingleComobo As CustomControl.MySingleComobo
    Friend WithEvents WZPHTextBox1 As CustomControl.MyTextBox
    Friend WithEvents dhCheckBox1 As System.Windows.Forms.CheckBox
    Friend WithEvents lrCheckBox3 As System.Windows.Forms.CheckBox
    Friend WithEvents wcCheckBox4 As System.Windows.Forms.CheckBox
    Friend WithEvents dqCheckBox5 As System.Windows.Forms.CheckBox
    Friend WithEvents RKDoubleDateEdit As CustomControl.DoubleDateEdit
    Friend WithEvents LrDoubleDateEdit As CustomControl.DoubleDateEdit
    Friend WithEvents WCDoubleDateEdit As CustomControl.DoubleDateEdit
    Friend WithEvents DQDoubleDateEdit As CustomControl.DoubleDateEdit
    Friend WithEvents MyButton1 As CustomControl.MyButton
    Friend WithEvents WZLBDtComobo1 As CustomControl.MyDtComobo
    Friend WithEvents RKDHTextBox As CustomControl.MyTextBox
    Friend WithEvents SCCJTextBox1 As CustomControl.MyTextBox
    Friend WithEvents GGTextBox As CustomControl.MyTextBox
End Class
