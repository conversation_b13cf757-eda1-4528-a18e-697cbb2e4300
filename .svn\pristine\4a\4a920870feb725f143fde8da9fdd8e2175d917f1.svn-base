﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class MaterialsOtherInQuery
    Inherits HisControl.BaseForm

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel()
        Me.Cx_Code_Text = New CustomControl.MyTextBox()
        Me.Dj_Code_Text = New CustomControl.MyTextBox()
        Me.Dj_State_SingleCom = New CustomControl.MySingleComobo()
        Me.Rk_Date1 = New CustomControl.MyDateEdit()
        Me.Rk_Date2 = New CustomControl.MyDateEdit()
        Me.Jsr_DtCom = New CustomControl.MyDtComobo()
        Me.Kf_DtCom = New CustomControl.MyDtComobo()
        Me.InOutLb_DtCom = New CustomControl.MyDtComobo()
        Me.Cx_State_SingleCom = New CustomControl.MySingleComobo()
        Me.Search_Btn = New CustomControl.MyButton()
        Me.Clear_Btn = New CustomControl.MyButton()
        Me.MyGrid1 = New CustomControl.MyGrid()
        Me.TableLayoutPanel1.SuspendLayout()
        Me.SuspendLayout()
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.ColumnCount = 10
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 63.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 143.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 63.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 143.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 63.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 93.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 80.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 80.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 87.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 9.0!))
        Me.TableLayoutPanel1.Controls.Add(Me.Cx_Code_Text, 2, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.Dj_Code_Text, 0, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.Dj_State_SingleCom, 4, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.Rk_Date1, 0, 3)
        Me.TableLayoutPanel1.Controls.Add(Me.Rk_Date2, 2, 3)
        Me.TableLayoutPanel1.Controls.Add(Me.Jsr_DtCom, 4, 3)
        Me.TableLayoutPanel1.Controls.Add(Me.Kf_DtCom, 0, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.InOutLb_DtCom, 2, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.Cx_State_SingleCom, 4, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.Search_Btn, 6, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.Clear_Btn, 7, 2)
        Me.TableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Top
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(0, 0)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.RowCount = 5
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 3.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 27.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 27.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 27.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 2.0!))
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(1139, 90)
        Me.TableLayoutPanel1.TabIndex = 3
        '
        'Cx_Code_Text
        '
        Me.Cx_Code_Text.Captain = "冲销编码"
        Me.Cx_Code_Text.CaptainBackColor = System.Drawing.Color.Transparent
        Me.Cx_Code_Text.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Cx_Code_Text.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.Cx_Code_Text.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.Cx_Code_Text, 2)
        Me.Cx_Code_Text.ContentForeColor = System.Drawing.Color.Black
        Me.Cx_Code_Text.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.Cx_Code_Text.EditMask = Nothing
        Me.Cx_Code_Text.Location = New System.Drawing.Point(209, 6)
        Me.Cx_Code_Text.Multiline = False
        Me.Cx_Code_Text.Name = "Cx_Code_Text"
        Me.Cx_Code_Text.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.Cx_Code_Text.ReadOnly = False
        Me.Cx_Code_Text.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.Cx_Code_Text.SelectionStart = 0
        Me.Cx_Code_Text.SelectStart = 0
        Me.Cx_Code_Text.Size = New System.Drawing.Size(200, 20)
        Me.Cx_Code_Text.TabIndex = 1
        Me.Cx_Code_Text.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Cx_Code_Text.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.Cx_Code_Text.Watermark = Nothing
        '
        'Dj_Code_Text
        '
        Me.Dj_Code_Text.Captain = "单据编码"
        Me.Dj_Code_Text.CaptainBackColor = System.Drawing.Color.Transparent
        Me.Dj_Code_Text.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Dj_Code_Text.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.Dj_Code_Text.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.Dj_Code_Text, 2)
        Me.Dj_Code_Text.ContentForeColor = System.Drawing.Color.Black
        Me.Dj_Code_Text.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.Dj_Code_Text.EditMask = Nothing
        Me.Dj_Code_Text.Location = New System.Drawing.Point(3, 6)
        Me.Dj_Code_Text.Multiline = False
        Me.Dj_Code_Text.Name = "Dj_Code_Text"
        Me.Dj_Code_Text.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.Dj_Code_Text.ReadOnly = False
        Me.Dj_Code_Text.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.Dj_Code_Text.SelectionStart = 0
        Me.Dj_Code_Text.SelectStart = 0
        Me.Dj_Code_Text.Size = New System.Drawing.Size(200, 20)
        Me.Dj_Code_Text.TabIndex = 0
        Me.Dj_Code_Text.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Dj_Code_Text.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.Dj_Code_Text.Watermark = Nothing
        '
        'Dj_State_SingleCom
        '
        Me.Dj_State_SingleCom.Captain = "完成状态"
        Me.Dj_State_SingleCom.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Dj_State_SingleCom.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.Dj_State_SingleCom.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.Dj_State_SingleCom, 2)
        Me.Dj_State_SingleCom.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        Me.Dj_State_SingleCom.ItemHeight = 16
        Me.Dj_State_SingleCom.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Dj_State_SingleCom.Location = New System.Drawing.Point(415, 6)
        Me.Dj_State_SingleCom.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.Dj_State_SingleCom.MinimumSize = New System.Drawing.Size(0, 20)
        Me.Dj_State_SingleCom.Name = "Dj_State_SingleCom"
        Me.Dj_State_SingleCom.ReadOnly = False
        Me.Dj_State_SingleCom.Size = New System.Drawing.Size(150, 20)
        Me.Dj_State_SingleCom.TabIndex = 1
        Me.Dj_State_SingleCom.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'Rk_Date1
        '
        Me.Rk_Date1.Captain = "入库日期"
        Me.Rk_Date1.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Rk_Date1.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.Rk_Date1, 2)
        Me.Rk_Date1.InitialSelection = C1.Win.C1Input.InitialSelectionEnum.CaretAtEnd
        Me.Rk_Date1.Location = New System.Drawing.Point(3, 60)
        Me.Rk_Date1.MaximumSize = New System.Drawing.Size(100000000, 20)
        Me.Rk_Date1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.Rk_Date1.Name = "Rk_Date1"
        Me.Rk_Date1.Size = New System.Drawing.Size(200, 20)
        Me.Rk_Date1.TabIndex = 1
        Me.Rk_Date1.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Rk_Date1.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.Rk_Date1.VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.DropDown
        '
        'Rk_Date2
        '
        Me.Rk_Date2.Captain = "至"
        Me.Rk_Date2.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Rk_Date2.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.Rk_Date2, 2)
        Me.Rk_Date2.InitialSelection = C1.Win.C1Input.InitialSelectionEnum.CaretAtEnd
        Me.Rk_Date2.Location = New System.Drawing.Point(209, 60)
        Me.Rk_Date2.MaximumSize = New System.Drawing.Size(100000000, 20)
        Me.Rk_Date2.MinimumSize = New System.Drawing.Size(0, 20)
        Me.Rk_Date2.Name = "Rk_Date2"
        Me.Rk_Date2.Size = New System.Drawing.Size(200, 20)
        Me.Rk_Date2.TabIndex = 3
        Me.Rk_Date2.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Rk_Date2.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.Rk_Date2.VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.DropDown
        '
        'Jsr_DtCom
        '
        Me.Jsr_DtCom.Captain = "经 手 人"
        Me.Jsr_DtCom.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Jsr_DtCom.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.Jsr_DtCom.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.Jsr_DtCom, 2)
        Me.Jsr_DtCom.DataSource = Nothing
        Me.Jsr_DtCom.ItemHeight = 18
        Me.Jsr_DtCom.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Jsr_DtCom.Location = New System.Drawing.Point(415, 60)
        Me.Jsr_DtCom.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.Jsr_DtCom.MinimumSize = New System.Drawing.Size(0, 20)
        Me.Jsr_DtCom.Name = "Jsr_DtCom"
        Me.Jsr_DtCom.ReadOnly = False
        Me.Jsr_DtCom.Size = New System.Drawing.Size(150, 20)
        Me.Jsr_DtCom.TabIndex = 4
        Me.Jsr_DtCom.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'Kf_DtCom
        '
        Me.Kf_DtCom.Captain = "库    房"
        Me.Kf_DtCom.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Kf_DtCom.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.Kf_DtCom.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.Kf_DtCom, 2)
        Me.Kf_DtCom.DataSource = Nothing
        Me.Kf_DtCom.ItemHeight = 18
        Me.Kf_DtCom.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Kf_DtCom.Location = New System.Drawing.Point(3, 33)
        Me.Kf_DtCom.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.Kf_DtCom.MinimumSize = New System.Drawing.Size(0, 20)
        Me.Kf_DtCom.Name = "Kf_DtCom"
        Me.Kf_DtCom.ReadOnly = False
        Me.Kf_DtCom.Size = New System.Drawing.Size(200, 20)
        Me.Kf_DtCom.TabIndex = 5
        Me.Kf_DtCom.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'InOutLb_DtCom
        '
        Me.InOutLb_DtCom.Captain = "入库类别"
        Me.InOutLb_DtCom.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.InOutLb_DtCom.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.InOutLb_DtCom.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.InOutLb_DtCom, 2)
        Me.InOutLb_DtCom.DataSource = Nothing
        Me.InOutLb_DtCom.ItemHeight = 18
        Me.InOutLb_DtCom.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.InOutLb_DtCom.Location = New System.Drawing.Point(209, 33)
        Me.InOutLb_DtCom.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.InOutLb_DtCom.MinimumSize = New System.Drawing.Size(0, 20)
        Me.InOutLb_DtCom.Name = "InOutLb_DtCom"
        Me.InOutLb_DtCom.ReadOnly = False
        Me.InOutLb_DtCom.Size = New System.Drawing.Size(200, 20)
        Me.InOutLb_DtCom.TabIndex = 6
        Me.InOutLb_DtCom.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'Cx_State_SingleCom
        '
        Me.Cx_State_SingleCom.Captain = "冲销状态"
        Me.Cx_State_SingleCom.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Cx_State_SingleCom.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.Cx_State_SingleCom.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.Cx_State_SingleCom, 2)
        Me.Cx_State_SingleCom.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        Me.Cx_State_SingleCom.ItemHeight = 16
        Me.Cx_State_SingleCom.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Cx_State_SingleCom.Location = New System.Drawing.Point(415, 33)
        Me.Cx_State_SingleCom.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.Cx_State_SingleCom.MinimumSize = New System.Drawing.Size(0, 20)
        Me.Cx_State_SingleCom.Name = "Cx_State_SingleCom"
        Me.Cx_State_SingleCom.ReadOnly = False
        Me.Cx_State_SingleCom.Size = New System.Drawing.Size(150, 20)
        Me.Cx_State_SingleCom.TabIndex = 2
        Me.Cx_State_SingleCom.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'Search_Btn
        '
        Me.Search_Btn.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.Search_Btn.DialogResult = System.Windows.Forms.DialogResult.None
        Me.Search_Btn.Location = New System.Drawing.Point(571, 33)
        Me.Search_Btn.Name = "Search_Btn"
        Me.TableLayoutPanel1.SetRowSpan(Me.Search_Btn, 2)
        Me.Search_Btn.Size = New System.Drawing.Size(74, 47)
        Me.Search_Btn.TabIndex = 2
        Me.Search_Btn.Text = "查询"
        '
        'Clear_Btn
        '
        Me.Clear_Btn.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.Clear_Btn.DialogResult = System.Windows.Forms.DialogResult.None
        Me.Clear_Btn.Location = New System.Drawing.Point(651, 33)
        Me.Clear_Btn.Name = "Clear_Btn"
        Me.TableLayoutPanel1.SetRowSpan(Me.Clear_Btn, 2)
        Me.Clear_Btn.Size = New System.Drawing.Size(74, 47)
        Me.Clear_Btn.TabIndex = 7
        Me.Clear_Btn.Text = "清空"
        '
        'MyGrid1
        '
        Me.MyGrid1.CanCustomCol = False
        Me.MyGrid1.Col = 0
        Me.MyGrid1.ColumnFooters = False
        Me.MyGrid1.DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveRight
        Me.MyGrid1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.MyGrid1.FetchRowStyles = False
        Me.MyGrid1.GroupByAreaVisible = False
        Me.MyGrid1.Location = New System.Drawing.Point(0, 90)
        Me.MyGrid1.Margin = New System.Windows.Forms.Padding(0)
        Me.MyGrid1.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.MyGrid1.Name = "MyGrid1"
        Me.MyGrid1.Size = New System.Drawing.Size(1139, 586)
        Me.MyGrid1.TabAction = C1.Win.C1TrueDBGrid.TabActionEnum.ControlNavigation
        Me.MyGrid1.TabIndex = 4
        Me.MyGrid1.Xmlpath = Nothing
        '
        'MaterialsOtherInQuery
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.ClientSize = New System.Drawing.Size(1139, 676)
        Me.Controls.Add(Me.MyGrid1)
        Me.Controls.Add(Me.TableLayoutPanel1)
        Me.Name = "MaterialsOtherInQuery"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "其他入库速查"
        Me.TableLayoutPanel1.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents TableLayoutPanel1 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents Cx_Code_Text As CustomControl.MyTextBox
    Friend WithEvents Dj_Code_Text As CustomControl.MyTextBox
    Friend WithEvents Dj_State_SingleCom As CustomControl.MySingleComobo
    Friend WithEvents Rk_Date1 As CustomControl.MyDateEdit
    Friend WithEvents Rk_Date2 As CustomControl.MyDateEdit
    Friend WithEvents Jsr_DtCom As CustomControl.MyDtComobo
    Friend WithEvents Kf_DtCom As CustomControl.MyDtComobo
    Friend WithEvents InOutLb_DtCom As CustomControl.MyDtComobo
    Friend WithEvents Cx_State_SingleCom As CustomControl.MySingleComobo
    Friend WithEvents Search_Btn As CustomControl.MyButton
    Friend WithEvents Clear_Btn As CustomControl.MyButton
    Friend WithEvents MyGrid1 As CustomControl.MyGrid

End Class
