﻿/**  版本信息模板在安装目录下，可自行修改。
* D_Emr_BasicElementTree.cs
*
* 功 能： N/A
* 类 名： D_Emr_BasicElementTree
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/8/16 星期二 下午 1:38:15   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using System.Collections.Generic;
//
namespace DAL
{
    /// <summary>
    /// 数据访问类:D_Emr_BasicElementTree
    /// </summary>
    public partial class D_Emr_BasicElementTree
    {
        public D_Emr_BasicElementTree()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(string Ele_Code)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from Emr_BasicElementTree");
            strSql.Append(" where Ele_Code=@Ele_Code ");
            SqlParameter[] parameters = {
					new SqlParameter("@Ele_Code", SqlDbType.Char,10)			};
            parameters[0].Value = Ele_Code;

            return HisVar.HisVar.Sqldal.Exists(strSql.ToString(), parameters);
        }


        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ModelOld.M_Emr_BasicElementTree model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into Emr_BasicElementTree(");
            strSql.Append("Ele_Code,Ele_Name,isDir,EleType,isMultiSelect,DataField,Father_Code)");
            strSql.Append(" values (");
            strSql.Append("@Ele_Code,@Ele_Name,@isDir,@EleType,@isMultiSelect,@DataField,@Father_Code)");
            SqlParameter[] parameters = {
					new SqlParameter("@Ele_Code", SqlDbType.Char,10),
					new SqlParameter("@Ele_Name", SqlDbType.VarChar,50),
					new SqlParameter("@isDir", SqlDbType.Bit,1),
					new SqlParameter("@EleType", SqlDbType.Char,1),
					new SqlParameter("@isMultiSelect", SqlDbType.Bit,1),
					new SqlParameter("@DataField", SqlDbType.VarChar,50),
					new SqlParameter("@Father_Code", SqlDbType.Char,10)};
            parameters[0].Value = model.Ele_Code;
            parameters[1].Value = model.Ele_Name;
            parameters[2].Value = model.isDir;
            parameters[3].Value = model.EleType;
            parameters[4].Value = model.isMultiSelect;
            parameters[5].Value = Common.Tools.IsValueNull(model.DataField);
            parameters[6].Value = model.Father_Code;

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ModelOld.M_Emr_BasicElementTree model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update Emr_BasicElementTree set ");
            strSql.Append("Ele_Name=@Ele_Name,");
            strSql.Append("isDir=@isDir,");
            strSql.Append("EleType=@EleType,");
            strSql.Append("isMultiSelect=@isMultiSelect,");
            strSql.Append("DataField=@DataField,");
            strSql.Append("Father_Code=@Father_Code");
            strSql.Append(" where Ele_Code=@Ele_Code ");
            SqlParameter[] parameters = {
					new SqlParameter("@Ele_Name", SqlDbType.VarChar,50),
					new SqlParameter("@isDir", SqlDbType.Bit,1),
					new SqlParameter("@EleType", SqlDbType.Char,1),
					new SqlParameter("@isMultiSelect", SqlDbType.Bit,1),
					new SqlParameter("@DataField", SqlDbType.VarChar,50),
					new SqlParameter("@Father_Code", SqlDbType.Char,10),
					new SqlParameter("@Ele_Code", SqlDbType.Char,10)};
            parameters[0].Value = model.Ele_Name;
            parameters[1].Value = model.isDir;
            parameters[2].Value = model.EleType;
            parameters[3].Value = model.isMultiSelect;
            parameters[4].Value = model.DataField;
            parameters[5].Value = model.Father_Code;
            parameters[6].Value = model.Ele_Code;

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(string Ele_Code)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from Emr_BasicElementTree ");
            strSql.Append(" where Ele_Code=@Ele_Code ");
            SqlParameter[] parameters = {
					new SqlParameter("@Ele_Code", SqlDbType.Char,10)			};
            parameters[0].Value = Ele_Code;

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(string Ele_Code, char ele_type)
        {
            List<string> sqlList = new List<string>();
            List<SqlParameter[]> ParaList = new List<SqlParameter[]>();

            if (ele_type == '1' || ele_type == '2')
            {
                StringBuilder strSql2 = new StringBuilder();
                SqlParameter[] parameters2 = { new SqlParameter("@Ele_Code", SqlDbType.Char, 10) };
                switch (ele_type)
                {
                    case '1':
                        strSql2.Append("delete from Emr_BasicElementList ");
                        strSql2.Append(" where Ele_Code=@Ele_Code  ");
                        parameters2[0].Value = Ele_Code;
                        break;
                    case '2':
                        strSql2.Append("delete from Emr_BasicElementValue ");
                        strSql2.Append(" where Ele_Code=@Ele_Code  ");
                        parameters2[0].Value = Ele_Code;
                        break;
                }
                sqlList.Add(strSql2.ToString());
                ParaList.Add(parameters2);
            }

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from Emr_BasicElementTree ");
            strSql.Append(" where Ele_Code=@Ele_Code ");
            SqlParameter[] parameters = {
					new SqlParameter("@Ele_Code", SqlDbType.Char,10)			};
            parameters[0].Value = Ele_Code;

            sqlList.Add(strSql.ToString());
            ParaList.Add(parameters);

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(sqlList, ParaList);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string Ele_Codelist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from Emr_BasicElementTree ");
            strSql.Append(" where Ele_Code in (" + Ele_Codelist + ")  ");
            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ModelOld.M_Emr_BasicElementTree GetModel(string Ele_Code)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Ele_Code,Ele_Name,isDir,EleType,isMultiSelect,DataField,Father_Code from Emr_BasicElementTree ");
            strSql.Append(" where Ele_Code=@Ele_Code ");
            SqlParameter[] parameters = {
					new SqlParameter("@Ele_Code", SqlDbType.Char,10)			};
            parameters[0].Value = Ele_Code;

            ModelOld.M_Emr_BasicElementTree model = new ModelOld.M_Emr_BasicElementTree();
            DataSet ds = HisVar.HisVar.Sqldal.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ModelOld.M_Emr_BasicElementTree DataRowToModel(DataRow row)
        {
            ModelOld.M_Emr_BasicElementTree model = new ModelOld.M_Emr_BasicElementTree();
            if (row != null)
            {
                if (row["Ele_Code"] != null)
                {
                    model.Ele_Code = row["Ele_Code"].ToString();
                }
                if (row["Ele_Name"] != null)
                {
                    model.Ele_Name = row["Ele_Name"].ToString();
                }
                if (row["isDir"] != null && row["isDir"].ToString() != "")
                {
                    if ((row["isDir"].ToString() == "1") || (row["isDir"].ToString().ToLower() == "true"))
                    {
                        model.isDir = true;
                    }
                    else
                    {
                        model.isDir = false;
                    }
                }
                if (row["EleType"] != null)
                {
                    model.EleType = row["EleType"].ToString();
                }
                if (row["isMultiSelect"] != null && row["isMultiSelect"].ToString() != "")
                {
                    if ((row["isMultiSelect"].ToString() == "1") || (row["isMultiSelect"].ToString().ToLower() == "true"))
                    {
                        model.isMultiSelect = true;
                    }
                    else
                    {
                        model.isMultiSelect = false;
                    }
                }
                if (row["DataField"] != null)
                {
                    model.DataField = row["DataField"].ToString();
                }
                if (row["Father_Code"] != null)
                {
                    model.Father_Code = row["Father_Code"].ToString();
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Ele_Code,Ele_Name,isDir,EleType,isMultiSelect,DataField,Father_Code ");
            strSql.Append(" FROM Emr_BasicElementTree ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

        public DataSet GetEndNodes()
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Ele_Code,Ele_Name,isDir,EleType,isMultiSelect,DataField,Father_Code ");
            strSql.Append(" FROM Emr_BasicElementTree ");
            strSql.Append("  WHERE not EXISTS(SELECT 1 FROM dbo.Emr_BasicElementTree a WHERE a.Father_Code=dbo.Emr_BasicElementTree.Ele_Code) ");
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }


        public string MaxCode()
        {
            string Max = HisVar.HisVar.Sqldal.F_MaxCode("SELECT MAX(Ele_Code) FROM dbo.Emr_BasicElementTree", 10);
            return Max;
        }


        public string Type(string Ele_Code)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  EleType from Emr_BasicElementTree ");
            strSql.Append(" where Ele_Code=@Ele_Code ");
            SqlParameter[] parameters = {
					new SqlParameter("@Ele_Code", SqlDbType.Char,10)			};
            parameters[0].Value = Ele_Code;

            string i = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString(), parameters).ToString();

            return i;
        }
        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" Ele_Code,Ele_Name,isDir,EleType,isMultiSelect,DataField,Father_Code ");
            strSql.Append(" FROM Emr_BasicElementTree ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM Emr_BasicElementTree ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.Ele_Code desc");
            }
            strSql.Append(")AS Row, T.*  from Emr_BasicElementTree T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

        /*
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetList(int PageSize,int PageIndex,string strWhere)
        {
            SqlParameter[] parameters = {
                    new SqlParameter("@tblName", SqlDbType.VarChar, 255),
                    new SqlParameter("@fldName", SqlDbType.VarChar, 255),
                    new SqlParameter("@PageSize", SqlDbType.Int),
                    new SqlParameter("@PageIndex", SqlDbType.Int),
                    new SqlParameter("@IsReCount", SqlDbType.Bit),
                    new SqlParameter("@OrderType", SqlDbType.Bit),
                    new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
                    };
            parameters[0].Value = "Emr_BasicElementTree";
            parameters[1].Value = "Ele_Code";
            parameters[2].Value = PageSize;
            parameters[3].Value = PageIndex;
            parameters[4].Value = 0;
            parameters[5].Value = 0;
            parameters[6].Value = strWhere;	
            return HisVar.HisVar.Sqldal.RunProcedure("UP_GetRecordByPage",parameters,"ds");
        }*/

        #endregion  BasicMethod
        #region  ExtensionMethod

        #endregion  ExtensionMethod
    }
}

