﻿Public Class PublicConfig

'    Private Shared m_MzJkda As String
'    ''' <summary>
'    '''是否使用门诊调用健康档案
'    ''' </summary>
'    Public Shared Property MzJkda() As String
'        Get
'            Return m_MzJkda
'        End Get
'        Set(ByVal value As String)
'            m_MzJkda = value
'        End Set
'    End Property

    Private Shared m_IsCenterDb As String
    ''' <summary>
    '''是否使用中心数据库
    ''' </summary>
    Public Shared Property IsCenterDb() As String
        Get
            Return m_IsCenterDb
        End Get
        Set(ByVal value As String)
            m_IsCenterDb = value
        End Set
    End Property


    'Private Shared m_ZyCfHz As String
    '''' <summary>
    ''''住院日结处方汇总
    '''' </summary>
    'Public Shared Property ZyCfHz() As String
    '    Get
    '        Return m_ZyCfHz
    '    End Get
    '    Set(ByVal value As String)
    '        m_ZyCfHz = value
    '    End Set
    'End Property


'    Private Shared m_ZyHzxg As String
'    ''' <summary>
'    '''住院患者信息修改限制
'    ''' </summary>
'    Public Shared Property ZyHzxg() As String
'        Get
'            Return m_ZyHzxg
'        End Get
'        Set(ByVal value As String)
'            m_ZyHzxg = value
'        End Set
'    End Property

    Private Shared m_MzGh As String
    ''' <summary>
    '''启用挂号
    ''' </summary>
    Public Shared Property MzGh() As String
        Get
            Return m_MzGh
        End Get
        Set(ByVal value As String)
            m_MzGh = value
        End Set
    End Property

    Private Shared m_ZyHsz As String
    ''' <summary>
    '''护士站
    ''' </summary>
    Public Shared Property ZyHsz() As String
        Get
            Return m_ZyHsz
        End Get
        Set(ByVal value As String)
            m_ZyHsz = value
        End Set
    End Property

    Private Shared m_MzYsz As String
    ''' <summary>
    '''门诊医生站
    ''' </summary>
    Public Shared Property MzYsz() As String
        Get
            Return m_MzYsz
        End Get
        Set(ByVal value As String)
            m_MzYsz = value
        End Set
    End Property


    Private Shared m_Qfed As Double
    ''' <summary>
    '''住院余额警戒线
    ''' </summary>
    Public Shared Property Qfed() As Double
        Get
            Return m_Qfed
        End Get
        Set(ByVal value As Double)
            m_Qfed = value
        End Set
    End Property

    Private Shared m_Qfcy As String
    ''' <summary>
    '''住院患者欠费出院
    ''' </summary>
    Public Shared Property Qfcy() As String
        Get
            Return m_Qfcy
        End Get
        Set(ByVal value As String)
            m_Qfcy = value
        End Set
    End Property

    '    Private Shared m_ZyXmDj_Change As String
    '    ''' <summary>
    '    ''' 住院项目单价可变
    '    ''' </summary>
    '    Public Shared Property ZyXmDj_Change() As String
    '        Get
    '            Return m_ZyXmDj_Change
    '        End Get
    '        Set(ByVal value As String)
    '            m_ZyXmDj_Change = value
    '        End Set
    '    End Property

    '    Private Shared myjsd_Type As String
    '    ''' <summary>
    '    ''' 通辽医保结算单样式
    '    ''' </summary>
    '    ''' Public Shared Property ZyHszGztKsXz() As String
    '    Public Shared Property Jsd_Type() As String
    '        Get
    '            Return myjsd_Type
    '        End Get
    '        Set(ByVal value As String)
    '            myjsd_Type = value
    '        End Set
    '    End Property

    'Private Shared m_MzXmDj_Change As String
    '''' <summary>
    '''' 门诊项目单价可变
    '''' </summary>
    'Public Shared Property MzXmDj_Change() As String
    '    Get
    '        Return m_MzXmDj_Change
    '    End Get
    '    Set(ByVal value As String)
    '        m_MzXmDj_Change = value
    '    End Set
    'End Property

    Private Shared m_MsgYkYxq As Boolean
    ''' <summary>
    '''药库过期药提醒
    ''' </summary>
    Public Shared Property MsgYkYxq() As Boolean
        Get
            Return m_MsgYkYxq
        End Get
        Set(ByVal value As Boolean)
            m_MsgYkYxq = value
        End Set
    End Property


    Private Shared m_MsgYfYxq As Boolean
    ''' <summary>
    '''药房过期药提醒
    ''' </summary>
    Public Shared Property MsgYfYxq() As Boolean
        Get
            Return m_MsgYfYxq
        End Get
        Set(ByVal value As Boolean)
            m_MsgYfYxq = value
        End Set
    End Property

    Private Shared m_MsgYfZyfy As Boolean
    ''' <summary>
    '''药房住院发药提醒
    ''' </summary>
    Public Shared Property MsgYfZyfy() As Boolean
        Get
            Return m_MsgYfZyfy
        End Get
        Set(ByVal value As Boolean)
            m_MsgYfZyfy = value
        End Set
    End Property

    Private Shared m_MsgYfMzfy As Boolean
    ''' <summary>
    '''药房门诊发药提醒
    ''' </summary>
    Public Shared Property MsgYfMzfy() As Boolean
        Get
            Return m_MsgYfMzfy
        End Get
        Set(ByVal value As Boolean)
            m_MsgYfMzfy = value
        End Set
    End Property

    Private Shared m_MsgYfJsdb As Boolean
    ''' <summary>
    '''药房接收调拨提醒
    ''' </summary>
    Public Shared Property MsgYfJsdb() As Boolean
        Get
            Return m_MsgYfJsdb
        End Get
        Set(ByVal value As Boolean)
            m_MsgYfJsdb = value
        End Set
    End Property

    Private Shared m_NurseStation As Boolean
    ''' <summary>
    '''启用护士站工作台
    ''' </summary>
    Public Shared Property NurseStation() As Boolean
        Get
            Return m_NurseStation
        End Get
        Set(ByVal value As Boolean)
            m_NurseStation = value
        End Set
    End Property

    Private Shared m_MsgMzCfSf As Boolean
    ''' <summary>
    ''' 门诊处方收费提醒
    ''' </summary>
    Public Shared Property MsgMzCfSf() As Boolean
        Get
            Return m_MsgMzCfSf
        End Get
        Set(ByVal value As Boolean)
            m_MsgMzCfSf = value
        End Set
    End Property

    Private Shared m_MsgHsHzLy As Boolean
    ''' <summary>
    ''' 护士汇总领药提醒
    ''' </summary>
    Public Shared Property MsgHsHzLy() As Boolean
        Get
            Return m_MsgHsHzLy
        End Get
        Set(ByVal value As Boolean)
            m_MsgHsHzLy = value
        End Set
    End Property

    Private Shared m_MsgXmJc As Boolean
    ''' <summary>
    ''' 项目检查提醒
    ''' </summary>
    Public Shared Property MsgXmJc() As Boolean
        Get
            Return m_MsgXmJc
        End Get
        Set(ByVal value As Boolean)
            m_MsgXmJc = value
        End Set
    End Property

    Private Shared m_XqCode As String
    ''' <summary>
    '''县区编码
    ''' </summary>
    Public Shared Property XqCode() As String
        Get
            Return m_XqCode
        End Get
        Set(ByVal value As String)
            m_XqCode = value
        End Set
    End Property

    Private Shared m_XqName As String
    ''' <summary>
    '''县区名称
    ''' </summary>
    Public Shared Property XqName() As String
        Get
            Return m_XqName
        End Get
        Set(ByVal value As String)
            m_XqName = value
        End Set
    End Property

    Private Shared m_NhYyCode As String
    ''' <summary>
    '''农合医院编码
    ''' </summary>
    Public Shared Property NhYyCode() As String
        Get
            Return m_NhYyCode
        End Get
        Set(ByVal value As String)
            m_NhYyCode = value
        End Set
    End Property

    'Private Shared m_JbYp As String
    '''' <summary>
    ''''仅使用基本药物
    '''' </summary>
    'Public Shared Property JbYp() As String
    '    Get
    '        Return m_JbYp
    '    End Get
    '    Set(ByVal value As String)
    '        m_JbYp = value
    '    End Set
    'End Property

    '    Private Shared m_JzkXd As String
    '    ''' <summary>
    '    '''就诊卡限定
    '    ''' </summary>
    '    Public Shared Property JzkXd() As String
    '        Get
    '            Return m_JzkXd
    '        End Get
    '        Set(ByVal value As String)
    '            m_JzkXd = value
    '        End Set
    '    End Property

    '    Private Shared m_Sfzqsfs As String
    '    ''' <summary>
    '    '''身份证取数方式
    '    ''' </summary>
    '    Public Shared Property Sfzqsfs() As String
    '        Get
    '            Return m_Sfzqsfs
    '        End Get
    '        Set(ByVal value As String)
    '            m_Sfzqsfs = value
    '        End Set
    '    End Property

    Private Shared m_ZyYsz As String
    ''' <summary>
    '''住院医生站
    ''' </summary>
    Public Shared Property ZyYsz() As String
        Get
            Return m_ZyYsz
        End Get
        Set(ByVal value As String)
            m_ZyYsz = value
        End Set
    End Property

    Private Shared m_ZyCfKsXz As String
    ''' <summary>
    '''住院处方科室限制
    ''' </summary>
    Public Shared Property ZyCfKsXz() As String
        Get
            Return m_ZyCfKsXz
        End Get
        Set(ByVal value As String)
            m_ZyCfKsXz = value
        End Set
    End Property

    Private Shared m_DzblCkXz As String
    ''' <summary>
    '''电子病历查看限制
    ''' </summary>
    Public Shared Property DzblCkXz() As String
        Get
            Return m_DzblCkXz
        End Get
        Set(ByVal value As String)
            m_DzblCkXz = value
        End Set
    End Property

'    Private Shared m_MzFpStyle As String
'    ''' <summary>
'    '''门诊发票样式
'    ''' </summary>
'    Public Shared Property MzFpStyle() As String
'        Get
'            Return m_MzFpStyle
'        End Get
'        Set(ByVal value As String)
'            m_MzFpStyle = value
'        End Set
'    End Property

    Private Shared m_ZyFpStyle As String
    ''' <summary>
    '''住院发票样式
    ''' </summary>
    Public Shared Property ZyFpStyle() As String
        Get
            Return m_ZyFpStyle
        End Get
        Set(ByVal value As String)
            m_ZyFpStyle = value
        End Set
    End Property

    Private Shared m_ZjJsStyle As String
    ''' <summary>
    '''启用诊间结算 （0 收费处结算，1 诊间点点结算 2 医生站结算扣费）
    ''' </summary>
    Public Shared Property ZjJsStyle() As String
        Get
            Return m_ZjJsStyle
        End Get
        Set(ByVal value As String)
            m_ZjJsStyle = value
        End Set
    End Property

    'Private Shared m_Zlxmsh As String
    '''' <summary>
    ''''启用诊疗项目审核 
    '''' </summary>
    'Public Shared Property Zlxmsh() As String
    '    Get
    '        Return m_Zlxmsh
    '    End Get
    '    Set(ByVal value As String)
    '        m_Zlxmsh = value
    '    End Set
    'End Property

    Private Shared m_GuidangHours As Integer
    ''' <summary>
    '''归档时限 
    ''' </summary>
    Public Shared Property GuidangHours() As Integer
        Get
            Return m_GuidangHours
        End Get
        Set(ByVal value As Integer)
            m_GuidangHours = value
        End Set
    End Property

    Private Shared m_EmrPrintType As Integer
    ''' <summary>
    '''电子病历打印方式 
    ''' </summary>
    Public Shared Property EmrPrintType() As Integer
        Get
            Return m_EmrPrintType
        End Get
        Set(ByVal value As Integer)
            m_EmrPrintType = value
        End Set
    End Property

    Private Shared m_EmrMark As String
    ''' <summary>
    '''电子病历个人痕迹
    ''' </summary>
    Public Shared Property EmrMark() As String
        Get
            Return m_EmrMark
        End Get
        Set(ByVal value As String)
            m_EmrMark = value
        End Set
    End Property

'    Private Shared m_RkXsj As String
    ''' <summary>
    '''药品入库销售价加成
    ''' </summary>
'    Public Shared Property RkXsj() As String
'        Get
'            Return m_RkXsj
'        End Get
'        Set(ByVal value As String)
'            m_RkXsj = value
'        End Set
'    End Property
'
'    Private Shared m_RkPfj As String
'    ''' <summary>
'    '''药品入库批发价加成
'    ''' </summary>
'    Public Shared Property RkPfj() As String
'        Get
'            Return m_RkPfj
'        End Get
'        Set(ByVal value As String)
'            m_RkPfj = value
'        End Set
'    End Property

'    Private Shared m_YfFyKc As String
'    ''' <summary>
'    '''药房发药显示库存数
'    ''' </summary>
'    Public Shared Property YfFyKc() As String
'        Get
'            Return m_YfFyKc
'        End Get
'        Set(ByVal value As String)
'            m_YfFyKc = value
'        End Set
'    End Property

'    Private Shared m_JyKsXz As String
'    ''' <summary>
'    ''' 检验科室限制
'    ''' </summary>
'    '''     Public Shared Property ZyHszGztKsXz() As String
'    Public Shared Property JyKsXz() As String
'        Get
'            Return m_JyKsXz
'        End Get
'        Set(ByVal value As String)
'            m_JyKsXz = value
'        End Set
'    End Property

    Public Shared Function ReadConfig() As Boolean
        Try


            Dim My_Ds As New DataSet
            My_Ds = HisVar.HisVar.Sqldal.Query("Select * from zd_YYpara")
            '读取配置文件
            For Each my_row In My_Ds.Tables(0).Rows

'                If my_row("Para_Code") = "02" Then
'                    m_MzJkda = my_row("Para_Value")
'                End If

                If my_row("Para_Code") = "03" Then
                    m_IsCenterDb = my_row("Para_Value")
                End If
'                If my_row("Para_Code") = "07" Then
'                    m_ZyHzxg = my_row("Para_Value")
'                End If
                'If my_row("Para_Code") = "08" Then
                '    m_ZyCfHz = my_row("Para_Value")
                'End If
                If my_row("Para_Code") = "09" Then
                    m_MzGh = my_row("Para_Value")
                End If
                If my_row("Para_Code") = "10" Then
                    m_ZyHsz = my_row("Para_Value")
                End If
                If my_row("Para_Code") = "11" Then
                    m_MzYsz = my_row("Para_Value")
                End If
                If my_row("Para_Code") = "12" Then
                    m_Qfed = my_row("Para_Value")
                End If
'                If my_row("Para_Code") = "13" Then
'                    If my_row("Para_Value") = 0 Then
'                        m_ZyXmDj_Change = "否"
'                        m_MzXmDj_Change = "否"
'                    ElseIf my_row("Para_Value") = 1 Then
'                        m_ZyXmDj_Change = "否"
'                        m_MzXmDj_Change = "是"
'                    ElseIf my_row("Para_Value") = 2 Then
'                        m_ZyXmDj_Change = "是"
'                        m_MzXmDj_Change = "否"
'                    ElseIf my_row("Para_Value") = 3 Then
'                        m_ZyXmDj_Change = "是"
'                        m_MzXmDj_Change = "是"
'                    End If
'
'                End If
                If my_row("Para_Code") = "14" Then
                    m_Qfcy = my_row("Para_Value")
                End If

                'If my_row("Para_Code") = "15" Then
                '    m_JbYp = my_row("Para_Value")
                'End If

'                If my_row("Para_Code") = "16" Then
'                    m_JzkXd = my_row("Para_Value")
'                End If

'                If my_row("Para_Code") = "17" Then
'                    m_Sfzqsfs = my_row("Para_Value")
'                End If

                If my_row("Para_Code") = "18" Then
                    m_ZyYsz = my_row("Para_Value")
                End If

                If my_row("Para_Code") = "19" Then
                    m_ZyCfKsXz = my_row("Para_Value")
                End If

'                If my_row("Para_Code") = "20" Then
'                    m_MzFpStyle = my_row("Para_Value")
'                End If

                If my_row("Para_Code") = "21" Then
                    m_ZyFpStyle = my_row("Para_Value")
                End If

                If my_row("Para_Code") = "22" Then
                    m_DzblCkXz = my_row("Para_Value")
                End If

                If my_row("Para_Code") = "23" Then
                    m_ZjJsStyle = my_row("Para_Value")
                End If

                'If my_row("Para_Code") = "25" Then
                '    m_Zlxmsh = my_row("Para_Value")
                'End If

                If my_row("Para_Code") = "26" Then
                    m_GuidangHours = my_row("Para_Value")
                End If

                If my_row("Para_Code") = "27" Then
                    m_EmrPrintType = my_row("Para_Value")
                End If

                If my_row("Para_Code") = "28" Then
                    m_EmrMark = my_row("Para_Value")
                End If

'                If my_row("Para_Code") = "29" Then
'                    m_RkXsj = my_row("Para_Value")
'                End If

'                If my_row("Para_Code") = "30" Then
'                    m_RkPfj = my_row("Para_Value")
'                End If

'                If my_row("Para_Code") = "31" Then
'                    m_YfFyKc = my_row("Para_Value")
'                End If

'                If my_row("Para_Code") = "33" Then
'                    m_JyKsXz = my_row("Para_Value")
'                End If

'                If my_row("Para_Code") = "35" Then
'                    myjsd_Type = my_row("Para_Value")
'                End If

                If my_row("Para_Code") = "90" Then
                    m_NhYyCode = my_row("Para_Value")
                End If

                If my_row("Para_Code") = "91" Then
                    m_XqName = my_row("Para_Value")
                End If
            Next


            If m_XqName.Contains("连山区") Then
                m_XqCode = "211102"
            ElseIf m_XqName.Contains("龙港区") Then
                m_XqCode = "211403"
            ElseIf m_XqName.Contains("南票区") Then
                m_XqCode = "211404"
            ElseIf m_XqName.Contains("杨家杖子经济开发区") Then
                m_XqCode = "211407"
            ElseIf m_XqName.Contains("建昌县") Then
                m_XqCode = "211422"
            ElseIf m_XqName.Contains("兴城市") Then
                m_XqCode = "211481"
            ElseIf m_XqName.Contains("丰南") Then
                m_XqCode = "130207"
            ElseIf m_XqName.Contains("丰润") Then
                m_XqCode = "130208"
            ElseIf m_XqName.Contains("滦县") Then
                m_XqCode = "130223"
            ElseIf m_XqName.Contains("滦南") Then
                m_XqCode = "130284"
            ElseIf m_XqName.Contains("迁西") Then
                m_XqCode = "130227"
            ElseIf m_XqName.Contains("玉田") Then
                m_XqCode = "130229"
            ElseIf m_XqName.Contains("迁安") Then
                m_XqCode = "130283"
            ElseIf m_XqName.Contains("路南") Then
                m_XqCode = "130202"
            ElseIf m_XqName.Contains("路北") Then
                m_XqCode = "130203"
            ElseIf m_XqName.Contains("高新区") Then
                m_XqCode = "130296"
            ElseIf m_XqName.Contains("海港") Then
                m_XqCode = "130299"
            ElseIf m_XqName.Contains("乐亭") Then
                m_XqCode = "130225"
            ElseIf m_XqName.Contains("古冶") Then
                m_XqCode = "130204"
            ElseIf m_XqName.Contains("遵化") Then
                m_XqCode = "130281"
            ElseIf m_XqName.Contains("芦台") Then
                m_XqCode = "130297"
            ElseIf m_XqName.Contains("汉沽") Then
                m_XqCode = "130298"
            ElseIf m_XqName.Contains("曲周") Then
                m_XqCode = "130435"
            ElseIf m_XqName.Contains("博野县") Then
                m_XqCode = "130637"
            ElseIf m_XqName.Contains("龙山区") Then
                m_XqCode = "220402"
            ElseIf m_XqName.Contains("灯塔市") Then
                m_XqCode = "211081"
            End If



            My_Ds = HisVar.HisVar.Sqldal.Query("select zd_msg1.msg_Code,case isnull(Jsr_Code,'') when '' then 'False' else 'True' end AS 'Check' from zd_msg1 left Join Zd_Msg2 on zd_msg1.Msg_Code=Zd_Msg2.Msg_Code and Jsr_Code='" & HisVar.HisVar.JsrCode & "'")
            '读取配置文件
            For Each my_row In My_Ds.Tables(0).Rows
                If my_row("msg_Code") = "001" Then
                    m_MsgYkYxq = my_row("Check")
                End If
                If my_row("msg_Code") = "002" Then
                    m_MsgYfYxq = my_row("Check")
                End If
                If my_row("msg_Code") = "003" Then
                    m_MsgYfZyfy = my_row("Check")
                End If
                If my_row("msg_Code") = "004" Then
                    m_MsgYfMzfy = my_row("Check")
                End If
                If my_row("msg_Code") = "005" Then
                    m_MsgYfJsdb = my_row("Check")
                End If
                If my_row("msg_Code") = "006" Then
                    m_NurseStation = my_row("Check")
                End If
                If my_row("msg_Code") = "007" Then
                    m_MsgMzCfSf = my_row("Check")
                End If
                If my_row("msg_Code") = "008" Then
                    m_MsgHsHzLy = my_row("Check")
                End If
                If my_row("msg_Code") = "009" Then
                    m_MsgXmJc = my_row("Check")
                End If
            Next

            Return True
        Catch ex As Exception
            MsgBox(ex.Message)
            Return False
        End Try
    End Function

End Class
