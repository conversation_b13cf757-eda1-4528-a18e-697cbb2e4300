﻿Imports System.Data.SqlClient
Imports HisControl

Public Class Ys_Cf4
    Dim My_Adapter As New SqlDataAdapter
    Dim My_Table As New DataTable
    Dim V_New As Boolean
    Dim My_DataSet As New DataSet

#Region "传参"
    Dim Rform As BaseForm
    'Dim R<PERSON>ert As Boolean
    'Dim Rdate As Date
    Dim Rrow As DataRow
    'Dim RZbtb As DataTable
    'Dim Rtdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    'Dim Rlb As C1.Win.C1Input.C1Label
    'Dim Rzbadt As SqlDataAdapter
    'Dim Rdataset As DataSet
    'Dim Rlx As String
#End Region

    Public Sub New(ByVal tform As BaseForm, ByVal trow As DataRow)

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        Rform = tform
        'Rinsert = tinsert
        'Rdate = tdate
        Rrow = trow
        'RZbtb = tZbtb
        'Rtdbgrid = ttdbgrid
        'Rlb = tlb
        'Rzbadt = tzbadt
        'My_DataSet = tdataset
        'Rlx = tlx
        ' 在 InitializeComponent() 调用之后添加任何初始化。

    End Sub

    Private Sub Ys_Cf4_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Call Form_Init()
        Call Init_Data1()
        Call Data_Show()
    End Sub

#Region "窗体__事件"

    Private Sub Form_Init()
        Call P_Comm(Me.Comm1)
        Call P_Comm(Me.Comm2)
        C1TextBox1.Text = Rrow("Mz_Code") & ""
        C1TextBox3.Text = Rrow("Ry_Name") & ""
        C1TextBox8.Text = Rrow("Ry_Sex") & ""
        C1TextBox9.Text = Rrow("Ry_Age") & ""
        C1TextBox11.Text = Rrow("Bxlb_Name") & ""
        C1TextBox7.Text = Rrow("Ry_Sfzh") & ""
        C1TextBox10.Text = Rrow("Ry_Address") & ""
        C1TextBox12.Text = Rrow("Jb_Name") & ""
        Call Init_Data1()
    End Sub
#End Region

#Region "数据__操作"

    Private Sub Init_Data1()     '从表数据
        Dim Str_Select As String = "select Yy_Code,Mz_Code,Hz_Zs,Hz_Xbs,Hz_Ct,Hz_Zz,Jz_Code from Dzbl where Yy_Code='" & HisVar.HisVar.WsyCode & "' order by Mz_Code"
        Dim Str_Insert As String = "Insert Into DzbL(Yy_Code,Mz_Code,Hz_Zs,Hz_Xbs,Hz_Ct,Hz_Zz)Values(@Yy_Code,@Mz_Code,@Hz_Zs,@Hz_Xbs,@Hz_Ct,@Hz_Zz)"
        Dim Str_Update As String = "Update DzbL Set Yy_Code=@Yy_Code,Mz_Code=@Mz_Code,Hz_Zs=@Hz_Zs,Hz_Xbs=@Hz_Xbs,Hz_Ct=@Hz_Ct,Hz_Zz=@Hz_Zz Where Mz_Code=@Old_Mz_Code"
        Dim Str_Delete As String = "Delete From DzbL WHERE Mz_Code=@Mz_Code"
        With My_Adapter

            .InsertCommand = New SqlCommand(Str_Insert, My_Cn)
            With .InsertCommand.Parameters
                .Add("@Yy_Code", SqlDbType.Char, 4)
                .Add("@Mz_Code", SqlDbType.VarChar, 14)                 '门诊编码
                .Add("@Hz_Zs", SqlDbType.VarChar, 1000)                 '主诉
                .Add("@Hz_Xbs", SqlDbType.VarChar, 1000)                   '现病史
                .Add("@Hz_Ct", SqlDbType.VarChar, 1000)                   '查体
                .Add("@Hz_Zz", SqlDbType.VarChar, 500)                   '症状

            End With

            .UpdateCommand = New SqlCommand(Str_Update, My_Cn)
            With .UpdateCommand.Parameters
                .Add("@Yy_Code", SqlDbType.Char, 4)
                .Add("@Mz_Code", SqlDbType.VarChar, 14)                 '门诊编码
                .Add("@Hz_Zs", SqlDbType.VarChar, 1000)                 '主诉
                .Add("@Hz_Xbs", SqlDbType.VarChar, 1000)                   '现病史
                .Add("@Hz_Ct", SqlDbType.VarChar, 1000)                   '查体
                .Add("@Hz_Zz", SqlDbType.VarChar, 500)                   '症状
                .Add("@Old_Mz_Code", SqlDbType.VarChar, 14, "Mz_Code")
            End With

            .DeleteCommand = New SqlCommand(Str_Delete, My_Cn)
            With .DeleteCommand.Parameters
                .Add("@Mz_Code", SqlDbType.Char, 14, "Mz_Code")
            End With
            .SelectCommand = New SqlCommand(Str_Select, My_Cn)
            .Fill(My_DataSet, "电子病历")
            .AcceptChangesDuringFill = True
            .MissingSchemaAction = MissingSchemaAction.AddWithKey
        End With

        My_Table = My_DataSet.Tables("电子病历")
        My_Table.PrimaryKey = New DataColumn() {My_Table.Columns("Mz_Code")}


    End Sub





#End Region

#Region "控件__动作"

    Private Sub Comm_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.Click, Comm2.Click
        Select Case sender.tag
            Case "保存"
                If V_New = True Then Call Data_Add() Else Call Data_Edit()
                MsgBox("保存成功！", MsgBoxStyle.OkOnly + MsgBoxStyle.Information, "提示:")
                Me.Close()
            Case "取消"
                Me.Close()
        End Select
    End Sub



    Private Sub TextBox_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles C1TextBox2.KeyPress, C1TextBox4.KeyPress, C1TextBox5.KeyPress, C1TextBox6.KeyPress, Comm1.KeyPress
        If e.KeyChar <> Chr(Keys.Return) Then Exit Sub
        e.Handled = True
        System.Windows.Forms.SendKeys.Send("{Tab}")
    End Sub



#End Region

#Region "数据__编辑"
    Private Sub Data_Show()

        Dim My_Reader As SqlDataReader
        My_Reader = HisVar.HisVar.Sqldal.ExecuteReader("select Yy_Code,Mz_Code,Hz_Zs,Hz_Xbs,Hz_Ct,Hz_Zz,Jz_Code from Dzbl where Yy_Code='" & HisVar.HisVar.WsyCode & "' and  Mz_Code='" & C1TextBox1.Text & "' order by Mz_Code")

        If My_Reader.HasRows = True Then
            If My_Reader.Read Then
                C1TextBox2.Text = My_Reader.Item("Hz_Zs") & ""
                C1TextBox4.Text = My_Reader.Item("Hz_Xbs") & ""
                C1TextBox5.Text = My_Reader.Item("Hz_Ct") & ""
                C1TextBox6.Text = My_Reader.Item("Hz_Zz") & ""
                V_New = False
            End If
        Else
            C1TextBox2.Text = ""
            C1TextBox4.Text = ""
            C1TextBox5.Text = ""
            C1TextBox6.Text = ""
            V_New = True
        End If
        My_Reader.Close()
        P_Conn(False)

    End Sub

    Private Sub Data_Add()

        Dim My_NewRow As DataRow = My_Table.NewRow

        With My_NewRow
            .Item("Yy_Code") = HisVar.HisVar.WsyCode
            .Item("Mz_Code") = Trim(C1TextBox1.Text & "")
            .Item("Hz_Zs") = Trim(C1TextBox2.Text & "")
            .Item("Hz_Xbs") = Trim(C1TextBox4.Text & "")
            .Item("Hz_Ct") = Trim(C1TextBox5.Text & "")
            .Item("Hz_Zz") = Trim(C1TextBox6.Text & "")

        End With

        '数据保存

        Try
            My_Table.Rows.Add(My_NewRow)

        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            Exit Sub
        Finally

        End Try


        '数据更新
        Try
            With My_Adapter.InsertCommand
                .Parameters(0).Value = My_NewRow.Item("Yy_Code")
                .Parameters(1).Value = My_NewRow.Item("Mz_Code")
                .Parameters(2).Value = My_NewRow.Item("Hz_Zs")
                .Parameters(3).Value = My_NewRow.Item("Hz_Xbs")
                .Parameters(4).Value = My_NewRow.Item("Hz_Ct")
                .Parameters(5).Value = My_NewRow.Item("Hz_Zz")

                Call P_Conn(True)
                .ExecuteNonQuery()
            End With

            My_NewRow.AcceptChanges()

        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            Exit Sub
        Finally
            Call P_Conn(False)
        End Try



    End Sub

    Private Sub Data_Edit()

        Dim My_Row As DataRow = My_Table.Rows.Find(Trim(C1TextBox1.Text & ""))

        Try
            With My_Row
                .BeginEdit()
                .Item("Yy_Code") = HisVar.HisVar.WsyCode
                .Item("Mz_Code") = Trim(C1TextBox1.Text & "")
                .Item("Hz_Zs") = Trim(C1TextBox2.Text & "")
                .Item("Hz_Xbs") = Trim(C1TextBox4.Text & "")
                .Item("Hz_Ct") = Trim(C1TextBox5.Text & "")
                .Item("Hz_Zz") = Trim(C1TextBox6.Text & "")
                .EndEdit()
            End With
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Critical, "提示:")
            My_Row.CancelEdit()
            Exit Sub
        Finally

        End Try

        '数据更新
        Call P_Conn(True)
        With My_Adapter.UpdateCommand
            Dim I As Integer = 0
            For Each My_Para As SqlParameter In .Parameters
                If I = .Parameters.Count - 1 Then
                    My_Para.Value = My_Row.Item("Mz_Code", DataRowVersion.Original)
                Else
                    My_Para.Value = My_Row.Item(I)
                    I = I + 1
                End If
            Next
            Try
                .ExecuteNonQuery()
                My_Row.AcceptChanges()
            Catch ex As Exception
                Beep()
                MsgBox(ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
            Finally
                Call P_Conn(False)

            End Try
        End With

    End Sub

#End Region

#Region "自定义按扭"

    Private Sub P_Comm(ByVal Bt As Button)
        With Bt
            Select Case .Tag
                Case "保存"
                    .Top = 3
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")
                    .Text = "            &B"

                Case "取消"
                    .Location = New Point(Comm1.Left + Comm1.Width + 4, Comm1.Top)
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
                    .Width = Me.Comm1.Width
                    .Text = "            &C"
            End Select
            .FlatStyle = FlatStyle.Flat
            .FlatAppearance.BorderSize = 0
            .BackgroundImageLayout = ImageLayout.Center
        End With

    End Sub

    Private Sub Comm_MouseEnter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.MouseEnter, Comm2.MouseEnter
        Select Case sender.tag
            Case "保存"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存2")
            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消2")
        End Select

    End Sub

    Private Sub Comm_MouseLeave(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.MouseLeave, Comm2.MouseLeave
        Select Case sender.tag
            Case "保存"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")
            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
        End Select
    End Sub

    Private Sub Comm_MouseDown(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm1.MouseDown, Comm2.MouseDown
        Select Case sender.tag
            Case "保存"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存3")

            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消3")
        End Select
    End Sub

    Private Sub Comm_MouseUp(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm1.MouseUp, Comm2.MouseUp
        Select Case sender.tag
            Case "保存"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")
            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
        End Select
    End Sub

#End Region

#Region "输入法设置"
    '中文
    Private Sub C1TextBox1_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1TextBox2.GotFocus, C1TextBox4.GotFocus, C1TextBox5.GotFocus, C1TextBox6.GotFocus
        InputLanguage.CurrentInputLanguage = HisVar.HisVar.InputCode
    End Sub

#End Region

End Class