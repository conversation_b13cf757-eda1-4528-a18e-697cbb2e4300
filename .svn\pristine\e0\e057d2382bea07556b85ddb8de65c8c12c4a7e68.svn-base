﻿Imports System.Data.SqlClient




Public Class Yk_Js1

#Region "定义__变量"
    Public My_Adapter As New SqlDataAdapter                '从表一适配器
    Dim My_Table As New DataTable                       '从表一
    Public Zb_Cm As CurrencyManager             '同步指针
    Public Zb_Row As DataRow                    '选 择 行
    Public Rk3_FirstLoad As Boolean                     '第一次调入明细表
    Public Zb_Insert As Boolean                 '增加记录
    Public V_Key As String
    Public My_DataSet As New DataSet
    Dim V_Tk_Code1 As String
    Dim My_Adapter1 As New SqlDataAdapter
#End Region

    Private Sub Yk_Js1_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Call Form_Init()
        Call Show_Data()
    End Sub

#Region "窗体事件"

    Private Sub Form_Init()

        Panel1.BorderStyle = BorderStyle.None
        Panel1.Height = 24

      

        '初始化TDBGrid
        Dim My_Grid As New BaseClass.C_Grid(Me.C1TrueDBGrid1)
        With My_Grid
            .Init_Grid()
            .P_Dock(BaseClass.C_Grid.Dock.Fill)
            .AllAddNew(False)
            .AllDelete(False)
            .AllUpdate(False)
            .AllSort(True)

            .Init_Column("退库药房", "Yf_Name", 100, "左", "")
            .Init_Column("退库编码", "Tk_Code", 160, "左", "")
            .Init_Column("经手人", "Jsr_Name", 100, "左", "")
            .Init_Column("票据状态", "Tk_Qr1", 70, "中", "")
            .Init_Column("退库日期", "Tk_Date", 150, "中", "yyyy-MM-dd HH:mm")
            .Init_Column("退库备注", "Tk_Memo", 100, "左", "")
            .Init_Column("药房编码", "Yf_Code", 0, "左", "")

        End With
        C1TrueDBGrid1.Splits(0).DisplayColumns("Yf_Code").Visible = False

    End Sub

#End Region

#Region "数据编辑"

    Private Sub Show_Data()
        If My_DataSet.Tables("主表") IsNot Nothing Then My_DataSet.Tables("主表").Clear()
        Dim Str_Select As String = "Select Tk_Code,Jsr_Name,Tk_Qr,Tk_Date,Tk_Money,Tk_Memo,CASE Tk_Qr WHEN '1' THEN '已接收' WHEN '0' THEN '未接收' END AS Tk_Qr1,Zd_YyYf.Yf_Code,Yf_Name from Yf_Tk1,Zd_YyYf,Zd_YyJsr where Yf_Tk1.Jsr_Code=Zd_YyJsr.Jsr_Code and Yf_Tk1.Yf_Code=Zd_YyYf.Yf_Code and Tk_Qr='0' And Tk_Ok=1  order by Tk_Code"
        With My_Adapter
            .SelectCommand = New SqlCommand(Str_Select, My_Cn)
            .Fill(My_DataSet, "主表")
            .AcceptChangesDuringFill = True
            .MissingSchemaAction = MissingSchemaAction.AddWithKey
        End With
        My_Table = My_DataSet.Tables("主表")
        My_Table.PrimaryKey = New DataColumn() {My_Table.Columns("Tk_Code")}
        My_Table.Columns("Tk_Qr1").ReadOnly = False
        'TDBGrid初始化
        C1TrueDBGrid1.SetDataBinding(My_DataSet, "主表", True)
        Zb_Cm = CType(BindingContext(C1TrueDBGrid1.DataSource, C1TrueDBGrid1.DataMember), CurrencyManager)
    End Sub

#End Region

#Region "控件动作"

    Private Sub C1TrueDBGrid1_MouseUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles C1TrueDBGrid1.MouseUp
        If e.Button = MouseButtons.Right Then Call P_Show_Mx("DBGrid")
    End Sub

#End Region

    Private Sub P_Show_Mx(ByVal V_Lb As String)         '显示明细表
        If C1TrueDBGrid1.RowCount = 0 Then Exit Sub
        Zb_Row = Zb_Cm.List(C1TrueDBGrid1.Row).Row

        Dim vform As New Yk_Js2(Me, Zb_Row, C1TrueDBGrid1)
        vform.Name = vform.Name & Zb_Row("Tk_Code")
        BaseFunc.BaseFunc.addTabControl(vform, Zb_Row("Yf_Name") & "-" & Zb_Row("Jsr_Name"))

    End Sub

    Private Sub Comm2_Click(ByVal sender As System.Object, ByVal e As C1.Win.C1Command.ClickEventArgs) Handles Comm2.Click
        Call Show_Data()
    End Sub

  
End Class