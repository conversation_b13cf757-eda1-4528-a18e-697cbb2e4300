﻿'------------------------------------------------------------------------------
' <auto-generated>
'     此代码由工具生成。
'     运行时版本:4.0.30319.42000
'
'     对此文件的更改可能会导致不正确的行为，并且如果
'     重新生成代码，这些更改将会丢失。
' </auto-generated>
'------------------------------------------------------------------------------

Option Strict On
Option Explicit On

Imports System

Namespace My.Resources
    
    '此类是由 StronglyTypedResourceBuilder
    '类通过类似于 ResGen 或 Visual Studio 的工具自动生成的。
    '若要添加或移除成员，请编辑 .ResX 文件，然后重新运行 ResGen
    '(以 /str 作为命令选项)，或重新生成 VS 项目。
    '''<summary>
    '''  一个强类型的资源类，用于查找本地化的字符串等。
    '''</summary>
    <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "4.0.0.0"),  _
     Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.Runtime.CompilerServices.CompilerGeneratedAttribute()>  _
    Friend Class Resource1
        
        Private Shared resourceMan As Global.System.Resources.ResourceManager
        
        Private Shared resourceCulture As Global.System.Globalization.CultureInfo
        
        <Global.System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")>  _
        Friend Sub New()
            MyBase.New
        End Sub
        
        '''<summary>
        '''  返回此类使用的缓存的 ResourceManager 实例。
        '''</summary>
        <Global.System.ComponentModel.EditorBrowsableAttribute(Global.System.ComponentModel.EditorBrowsableState.Advanced)>  _
        Friend Shared ReadOnly Property ResourceManager() As Global.System.Resources.ResourceManager
            Get
                If Object.ReferenceEquals(resourceMan, Nothing) Then
                    Dim temp As Global.System.Resources.ResourceManager = New Global.System.Resources.ResourceManager("MyResources.Resource1", GetType(Resource1).Assembly)
                    resourceMan = temp
                End If
                Return resourceMan
            End Get
        End Property
        
        '''<summary>
        '''  使用此强类型资源类，为所有资源查找
        '''  重写当前线程的 CurrentUICulture 属性。
        '''</summary>
        <Global.System.ComponentModel.EditorBrowsableAttribute(Global.System.ComponentModel.EditorBrowsableState.Advanced)>  _
        Friend Shared Property Culture() As Global.System.Globalization.CultureInfo
            Get
                Return resourceCulture
            End Get
            Set
                resourceCulture = value
            End Set
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property _2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("_2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property _3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("_3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property boy() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("boy", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property EmbedLink() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("EmbedLink", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property girl() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("girl", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property Icon_1005() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Icon_1005", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property Icon_1047() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Icon_1047", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property Icon_1469() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Icon_1469", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property TXT11() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("TXT11", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property WIZMENU() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("WIZMENU", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property Z_Edit() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Z_Edit", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property Z_PROP() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Z_PROP", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property Z_REPORT() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Z_REPORT", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property Z_SHOW() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Z_SHOW", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property Z_编辑() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Z_编辑", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 全部价格修改1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("全部价格修改1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 全部价格修改2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("全部价格修改2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 全部价格修改3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("全部价格修改3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 写健康卡1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("写健康卡1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 写健康卡2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("写健康卡2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 写健康卡3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("写健康卡3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 分娩1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("分娩1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 分娩2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("分娩2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 分娩3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("分娩3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 删除() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("删除", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 删除1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("删除1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 发送() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("发送", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_保存1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_保存1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_保存11() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_保存11", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_保存2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_保存2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_保存21() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_保存21", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_保存3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_保存3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_保存31() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_保存31", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_全部调拨1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_全部调拨1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_全部调拨2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_全部调拨2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_全部调拨3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_全部调拨3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_发药1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_发药1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_发药2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_发药2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_发药3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_发药3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_取消1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_取消1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_取消11() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_取消11", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_取消2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_取消2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_取消21() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_取消21", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_取消3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_取消3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_取消31() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_取消31", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_处置单1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_处置单1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_处置单2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_处置单2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_处置单3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_处置单3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_完成处方1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_完成处方1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_完成处方11() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_完成处方11", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_完成处方2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_完成处方2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_完成处方21() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_完成处方21", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_完成处方3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_完成处方3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_完成处方31() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_完成处方31", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_完成调拨1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_完成调拨1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_完成调拨2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_完成调拨2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_完成调拨3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_完成调拨3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_快速打印1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_快速打印1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_快速打印2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_快速打印2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_快速打印3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_快速打印3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_接收1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_接收1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_接收2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_接收2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_接收3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_接收3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_数据结算1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_数据结算1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_数据结算11() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_数据结算11", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_数据结算2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_数据结算2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_数据结算21() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_数据结算21", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_数据结算3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_数据结算3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_数据结算31() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_数据结算31", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_查找1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_查找1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_查找2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_查找2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_查找3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_查找3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_申请出院1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_申请出院1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_申请出院2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_申请出院2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_申请出院3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_申请出院3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_药库价格修改1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_药库价格修改1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_药库价格修改2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_药库价格修改2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_药库价格修改3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_药库价格修改3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_药房价格修改1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_药房价格修改1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_药房价格修改2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_药房价格修改2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_药房价格修改3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_药房价格修改3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_请求发药1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_请求发药1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_请求发药2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_请求发药2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_请求发药3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_请求发药3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_退回1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_退回1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_退回2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_退回2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 命令_退回3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("命令_退回3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 增加() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("增加", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 增加1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("增加1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 完成退库1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("完成退库1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 完成退库2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("完成退库2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 完成退库3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("完成退库3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 已出院() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("已出院", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 打印() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("打印", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 打印处方1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("打印处方1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 打印处方11() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("打印处方11", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 打印处方2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("打印处方2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 打印处方21() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("打印处方21", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 打印处方3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("打印处方3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 打印处方31() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("打印处方31", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 拒收1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("拒收1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 拒收2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("拒收2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 拒收3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("拒收3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 拒绝出院1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("拒绝出院1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 拒绝出院2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("拒绝出院2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 拒绝出院3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("拒绝出院3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 普通住院1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("普通住院1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 普通住院2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("普通住院2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 普通住院3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("普通住院3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 更新() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("更新", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 更新1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("更新1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 查找() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("查找", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 欠费() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("欠费", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend Shared ReadOnly Property 申请出院() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("申请出院", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
    End Class
End Namespace
