﻿
namespace ERX
{
    partial class ERxUpload
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.c1ToolBar1 = new C1.Win.C1Command.C1ToolBar();
            this.c1CommandLink1 = new C1.Win.C1Command.C1CommandLink();
            this.CmduploadChk = new C1.Win.C1Command.C1Command();
            this.c1CommandLink2 = new C1.Win.C1Command.C1CommandLink();
            this.CmdrxFixmedinsSignAndUpld = new C1.Win.C1Command.C1Command();
            this.c1CommandLink3 = new C1.Win.C1Command.C1CommandLink();
            this.CmdrxUndo = new C1.Win.C1Command.C1Command();
            this.c1CommandLink4 = new C1.Win.C1Command.C1CommandLink();
            this.CmdhospRxDetlQuery = new C1.Win.C1Command.C1Command();
            this.c1CommandLink5 = new C1.Win.C1Command.C1CommandLink();
            this.CmdrxChkInfoQuery = new C1.Win.C1Command.C1Command();
            this.c1CommandLink6 = new C1.Win.C1Command.C1CommandLink();
            this.CmdrxSetlInfoQuery = new C1.Win.C1Command.C1Command();
            this.c1CommandLink7 = new C1.Win.C1Command.C1CommandLink();
            this.Cmdcfypmucx = new C1.Win.C1Command.C1Command();
            this.c1SplitContainer1 = new C1.Win.C1SplitContainer.C1SplitContainer();
            this.c1SplitterPanel2 = new C1.Win.C1SplitContainer.C1SplitterPanel();
            this.myGrid2 = new CustomControl.MyGrid();
            this.c1SplitterPanel1 = new C1.Win.C1SplitContainer.C1SplitterPanel();
            this.myGrid1 = new CustomControl.MyGrid();
            this.c1CommandDock1 = new C1.Win.C1Command.C1CommandDock();
            this.c1DockingTab1 = new C1.Win.C1Command.C1DockingTab();
            this.c1DockingTabPage1 = new C1.Win.C1Command.C1DockingTabPage();
            this.tableLayoutPanel2 = new System.Windows.Forms.TableLayoutPanel();
            this.panel2 = new System.Windows.Forms.Panel();
            this.BtnClear = new CustomControl.MyButton();
            this.BtnSearch = new CustomControl.MyButton();
            this.TxtHz = new CustomControl.MyTextBox();
            this.TxtCode = new CustomControl.MyTextBox();
            this.comboYs1 = new ZTHisControl.ComboYs();
            this.comboKs1 = new ZTHisControl.ComboKs();
            this.DateEditStart = new CustomControl.MyDateEdit();
            this.DateEditEnd = new CustomControl.MyDateEdit();
            this.single_rxStasCodg1 = new YBControl.Single_rxStasCodg();
            this.panel1 = new System.Windows.Forms.Panel();
            this.c1CommandHolder1 = new C1.Win.C1Command.C1CommandHolder();
            ((System.ComponentModel.ISupportInitialize)(this.c1SplitContainer1)).BeginInit();
            this.c1SplitContainer1.SuspendLayout();
            this.c1SplitterPanel2.SuspendLayout();
            this.c1SplitterPanel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.c1CommandDock1)).BeginInit();
            this.c1CommandDock1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.c1DockingTab1)).BeginInit();
            this.c1DockingTab1.SuspendLayout();
            this.c1DockingTabPage1.SuspendLayout();
            this.tableLayoutPanel2.SuspendLayout();
            this.panel2.SuspendLayout();
            this.panel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.c1CommandHolder1)).BeginInit();
            this.SuspendLayout();
            // 
            // c1ToolBar1
            // 
            this.c1ToolBar1.AccessibleName = "Tool Bar";
            this.c1ToolBar1.BackColor = System.Drawing.Color.Transparent;
            this.c1ToolBar1.ButtonLayoutHorz = C1.Win.C1Command.ButtonLayoutEnum.TextBelow;
            this.c1ToolBar1.ButtonLookHorz = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.c1ToolBar1.CommandHolder = null;
            this.c1ToolBar1.CommandLinks.AddRange(new C1.Win.C1Command.C1CommandLink[] {
            this.c1CommandLink1,
            this.c1CommandLink2,
            this.c1CommandLink3,
            this.c1CommandLink4,
            this.c1CommandLink5,
            this.c1CommandLink6,
            this.c1CommandLink7});
            this.c1ToolBar1.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.c1ToolBar1.Location = new System.Drawing.Point(3, 3);
            this.c1ToolBar1.Movable = false;
            this.c1ToolBar1.Name = "c1ToolBar1";
            this.c1ToolBar1.Size = new System.Drawing.Size(708, 44);
            this.c1ToolBar1.Text = "c1ToolBar1";
            this.c1ToolBar1.VisualStyle = C1.Win.C1Command.VisualStyle.Custom;
            this.c1ToolBar1.VisualStyleBase = C1.Win.C1Command.VisualStyle.Classic;
            // 
            // c1CommandLink1
            // 
            this.c1CommandLink1.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.c1CommandLink1.Command = this.CmduploadChk;
            // 
            // CmduploadChk
            // 
            this.CmduploadChk.Image = global::ERX.Properties.Resources.上传;
            this.CmduploadChk.Name = "CmduploadChk";
            this.CmduploadChk.ShortcutText = "";
            this.CmduploadChk.Text = "上传预核验";
            this.CmduploadChk.Click += new C1.Win.C1Command.ClickEventHandler(this.CmduploadChk_Click);
            // 
            // c1CommandLink2
            // 
            this.c1CommandLink2.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.c1CommandLink2.Command = this.CmdrxFixmedinsSignAndUpld;
            this.c1CommandLink2.SortOrder = 1;
            // 
            // CmdrxFixmedinsSignAndUpld
            // 
            this.CmdrxFixmedinsSignAndUpld.Image = global::ERX.Properties.Resources.批量上传;
            this.CmdrxFixmedinsSignAndUpld.Name = "CmdrxFixmedinsSignAndUpld";
            this.CmdrxFixmedinsSignAndUpld.ShortcutText = "";
            this.CmdrxFixmedinsSignAndUpld.Text = "医保电子签名和上传";
            this.CmdrxFixmedinsSignAndUpld.Click += new C1.Win.C1Command.ClickEventHandler(this.CmdrxFixmedinsSignAndUpld_Click);
            // 
            // c1CommandLink3
            // 
            this.c1CommandLink3.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.c1CommandLink3.Command = this.CmdrxUndo;
            this.c1CommandLink3.SortOrder = 2;
            // 
            // CmdrxUndo
            // 
            this.CmdrxUndo.Image = global::ERX.Properties.Resources.挂号撤销;
            this.CmdrxUndo.Name = "CmdrxUndo";
            this.CmdrxUndo.ShortcutText = "";
            this.CmdrxUndo.Text = "电子处方撤销";
            this.CmdrxUndo.Click += new C1.Win.C1Command.ClickEventHandler(this.CmdrxUndo_Click);
            // 
            // c1CommandLink4
            // 
            this.c1CommandLink4.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.c1CommandLink4.Command = this.CmdhospRxDetlQuery;
            this.c1CommandLink4.SortOrder = 3;
            // 
            // CmdhospRxDetlQuery
            // 
            this.CmdhospRxDetlQuery.Image = global::ERX.Properties.Resources.查询;
            this.CmdhospRxDetlQuery.Name = "CmdhospRxDetlQuery";
            this.CmdhospRxDetlQuery.ShortcutText = "";
            this.CmdhospRxDetlQuery.Text = "处方信息査询";
            this.CmdhospRxDetlQuery.Click += new C1.Win.C1Command.ClickEventHandler(this.CmdhospRxDetlQuery_Click);
            // 
            // c1CommandLink5
            // 
            this.c1CommandLink5.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.c1CommandLink5.Command = this.CmdrxChkInfoQuery;
            this.c1CommandLink5.SortOrder = 4;
            // 
            // CmdrxChkInfoQuery
            // 
            this.CmdrxChkInfoQuery.Image = global::ERX.Properties.Resources.查询;
            this.CmdrxChkInfoQuery.Name = "CmdrxChkInfoQuery";
            this.CmdrxChkInfoQuery.ShortcutText = "";
            this.CmdrxChkInfoQuery.Text = "审核结果査询";
            this.CmdrxChkInfoQuery.Click += new C1.Win.C1Command.ClickEventHandler(this.CmdrxChkInfoQuery_Click);
            // 
            // c1CommandLink6
            // 
            this.c1CommandLink6.ButtonLook = ((C1.Win.C1Command.ButtonLookFlags)((C1.Win.C1Command.ButtonLookFlags.Text | C1.Win.C1Command.ButtonLookFlags.Image)));
            this.c1CommandLink6.Command = this.CmdrxSetlInfoQuery;
            this.c1CommandLink6.SortOrder = 5;
            // 
            // CmdrxSetlInfoQuery
            // 
            this.CmdrxSetlInfoQuery.Image = global::ERX.Properties.Resources.查询;
            this.CmdrxSetlInfoQuery.Name = "CmdrxSetlInfoQuery";
            this.CmdrxSetlInfoQuery.ShortcutText = "";
            this.CmdrxSetlInfoQuery.Text = "取药结果查询";
            this.CmdrxSetlInfoQuery.Click += new C1.Win.C1Command.ClickEventHandler(this.CmdrxSetlInfoQuery_Click);
            // 
            // c1CommandLink7
            // 
            this.c1CommandLink7.Command = this.Cmdcfypmucx;
            this.c1CommandLink7.SortOrder = 6;
            // 
            // Cmdcfypmucx
            // 
            this.Cmdcfypmucx.Name = "Cmdcfypmucx";
            this.Cmdcfypmucx.ShortcutText = "";
            this.Cmdcfypmucx.Text = "药品目录查询";
            this.Cmdcfypmucx.Click += new C1.Win.C1Command.ClickEventHandler(this.Cmdcfypmucx_Click);
            // 
            // c1SplitContainer1
            // 
            this.c1SplitContainer1.AutoSizeElement = C1.Framework.AutoSizeElement.Both;
            this.c1SplitContainer1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(207)))), ((int)(((byte)(221)))), ((int)(((byte)(238)))));
            this.c1SplitContainer1.CollapsingAreaColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(250)))));
            this.c1SplitContainer1.CollapsingCueColor = System.Drawing.Color.LightCoral;
            this.c1SplitContainer1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.c1SplitContainer1.EnlargeCollapsingHandle = true;
            this.c1SplitContainer1.FixedLineColor = System.Drawing.Color.SteelBlue;
            this.c1SplitContainer1.FixedLineWidth = 8;
            this.c1SplitContainer1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(30)))), ((int)(((byte)(57)))), ((int)(((byte)(91)))));
            this.c1SplitContainer1.Location = new System.Drawing.Point(240, 50);
            this.c1SplitContainer1.Name = "c1SplitContainer1";
            this.c1SplitContainer1.Panels.Add(this.c1SplitterPanel2);
            this.c1SplitContainer1.Panels.Add(this.c1SplitterPanel1);
            this.c1SplitContainer1.Size = new System.Drawing.Size(987, 692);
            this.c1SplitContainer1.SplitterColor = System.Drawing.Color.SteelBlue;
            this.c1SplitContainer1.SplitterWidth = 12;
            this.c1SplitContainer1.TabIndex = 3;
            this.c1SplitContainer1.ToolTipGradient = C1.Win.C1SplitContainer.ToolTipGradient.Blue;
            this.c1SplitContainer1.UseParentVisualStyle = false;
            // 
            // c1SplitterPanel2
            // 
            this.c1SplitterPanel2.Collapsible = true;
            this.c1SplitterPanel2.Controls.Add(this.myGrid2);
            this.c1SplitterPanel2.Dock = C1.Win.C1SplitContainer.PanelDockStyle.Bottom;
            this.c1SplitterPanel2.Height = 339;
            this.c1SplitterPanel2.Location = new System.Drawing.Point(0, 364);
            this.c1SplitterPanel2.Name = "c1SplitterPanel2";
            this.c1SplitterPanel2.Size = new System.Drawing.Size(987, 328);
            this.c1SplitterPanel2.SizeRatio = 49.921D;
            this.c1SplitterPanel2.TabIndex = 1;
            // 
            // myGrid2
            // 
            this.myGrid2.AllowColMove = true;
            this.myGrid2.AllowFilter = true;
            this.myGrid2.CanCustomCol = false;
            this.myGrid2.Caption = "";
            this.myGrid2.ChildGrid = null;
            this.myGrid2.Col = 0;
            this.myGrid2.ColumnFooters = false;
            this.myGrid2.ColumnHeaders = true;
            this.myGrid2.DataMember = "";
            this.myGrid2.DataSource = null;
            this.myGrid2.DataView = C1.Win.C1TrueDBGrid.DataViewEnum.Normal;
            this.myGrid2.DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveRight;
            this.myGrid2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.myGrid2.FetchRowStyles = false;
            this.myGrid2.FilterBar = false;
            this.myGrid2.GroupByAreaVisible = true;
            this.myGrid2.Location = new System.Drawing.Point(0, 0);
            this.myGrid2.Margin = new System.Windows.Forms.Padding(0);
            this.myGrid2.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.DottedCellBorder;
            this.myGrid2.Name = "myGrid2";
            this.myGrid2.Size = new System.Drawing.Size(987, 328);
            this.myGrid2.TabAction = C1.Win.C1TrueDBGrid.TabActionEnum.ControlNavigation;
            this.myGrid2.TabIndex = 1;
            this.myGrid2.Xmlpath = null;
            // 
            // c1SplitterPanel1
            // 
            this.c1SplitterPanel1.Controls.Add(this.myGrid1);
            this.c1SplitterPanel1.Height = 341;
            this.c1SplitterPanel1.Location = new System.Drawing.Point(0, 0);
            this.c1SplitterPanel1.Name = "c1SplitterPanel1";
            this.c1SplitterPanel1.Size = new System.Drawing.Size(987, 341);
            this.c1SplitterPanel1.TabIndex = 0;
            // 
            // myGrid1
            // 
            this.myGrid1.AllowColMove = true;
            this.myGrid1.AllowFilter = true;
            this.myGrid1.CanCustomCol = false;
            this.myGrid1.Caption = "";
            this.myGrid1.ChildGrid = null;
            this.myGrid1.Col = 0;
            this.myGrid1.ColumnFooters = false;
            this.myGrid1.ColumnHeaders = true;
            this.myGrid1.DataMember = "";
            this.myGrid1.DataSource = null;
            this.myGrid1.DataView = C1.Win.C1TrueDBGrid.DataViewEnum.Normal;
            this.myGrid1.DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveRight;
            this.myGrid1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.myGrid1.FetchRowStyles = false;
            this.myGrid1.FilterBar = false;
            this.myGrid1.GroupByAreaVisible = true;
            this.myGrid1.Location = new System.Drawing.Point(0, 0);
            this.myGrid1.Margin = new System.Windows.Forms.Padding(0);
            this.myGrid1.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.DottedCellBorder;
            this.myGrid1.Name = "myGrid1";
            this.myGrid1.Size = new System.Drawing.Size(987, 341);
            this.myGrid1.TabAction = C1.Win.C1TrueDBGrid.TabActionEnum.ControlNavigation;
            this.myGrid1.TabIndex = 0;
            this.myGrid1.Xmlpath = null;
            this.myGrid1.RowColChange += new C1.Win.C1TrueDBGrid.RowColChangeEventHandler(this.myGrid1_RowColChange);
            // 
            // c1CommandDock1
            // 
            this.c1CommandDock1.Controls.Add(this.c1DockingTab1);
            this.c1CommandDock1.Dock = System.Windows.Forms.DockStyle.Left;
            this.c1CommandDock1.Id = 3;
            this.c1CommandDock1.Location = new System.Drawing.Point(0, 50);
            this.c1CommandDock1.Margin = new System.Windows.Forms.Padding(0);
            this.c1CommandDock1.Name = "c1CommandDock1";
            this.c1CommandDock1.Size = new System.Drawing.Size(240, 692);
            // 
            // c1DockingTab1
            // 
            this.c1DockingTab1.Alignment = System.Windows.Forms.TabAlignment.Bottom;
            this.c1DockingTab1.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.c1DockingTab1.CanAutoHide = true;
            this.c1DockingTab1.Controls.Add(this.c1DockingTabPage1);
            this.c1DockingTab1.Location = new System.Drawing.Point(0, 0);
            this.c1DockingTab1.Name = "c1DockingTab1";
            this.c1DockingTab1.SelectedIndex = 1;
            this.c1DockingTab1.ShowCaption = true;
            this.c1DockingTab1.ShowSingleTab = false;
            this.c1DockingTab1.Size = new System.Drawing.Size(240, 692);
            this.c1DockingTab1.TabIndex = 0;
            this.c1DockingTab1.TabSizeMode = C1.Win.C1Command.TabSizeModeEnum.Fit;
            this.c1DockingTab1.TabsSpacing = 5;
            this.c1DockingTab1.VisualStyleBase = C1.Win.C1Command.VisualStyle.Office2010Blue;
            // 
            // c1DockingTabPage1
            // 
            this.c1DockingTabPage1.CaptionVisible = true;
            this.c1DockingTabPage1.Controls.Add(this.tableLayoutPanel2);
            this.c1DockingTabPage1.Location = new System.Drawing.Point(0, 0);
            this.c1DockingTabPage1.Name = "c1DockingTabPage1";
            this.c1DockingTabPage1.Size = new System.Drawing.Size(237, 691);
            this.c1DockingTabPage1.TabIndex = 0;
            this.c1DockingTabPage1.Text = "查询条件";
            // 
            // tableLayoutPanel2
            // 
            this.tableLayoutPanel2.ColumnCount = 1;
            this.tableLayoutPanel2.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel2.Controls.Add(this.panel2, 0, 7);
            this.tableLayoutPanel2.Controls.Add(this.TxtHz, 0, 1);
            this.tableLayoutPanel2.Controls.Add(this.TxtCode, 0, 0);
            this.tableLayoutPanel2.Controls.Add(this.comboYs1, 0, 2);
            this.tableLayoutPanel2.Controls.Add(this.comboKs1, 0, 3);
            this.tableLayoutPanel2.Controls.Add(this.DateEditStart, 0, 4);
            this.tableLayoutPanel2.Controls.Add(this.DateEditEnd, 0, 5);
            this.tableLayoutPanel2.Controls.Add(this.single_rxStasCodg1, 0, 6);
            this.tableLayoutPanel2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tableLayoutPanel2.Location = new System.Drawing.Point(0, 23);
            this.tableLayoutPanel2.Name = "tableLayoutPanel2";
            this.tableLayoutPanel2.RowCount = 9;
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 29F));
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 200F));
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 20F));
            this.tableLayoutPanel2.Size = new System.Drawing.Size(237, 668);
            this.tableLayoutPanel2.TabIndex = 7;
            // 
            // panel2
            // 
            this.panel2.Controls.Add(this.BtnClear);
            this.panel2.Controls.Add(this.BtnSearch);
            this.panel2.Dock = System.Windows.Forms.DockStyle.Top;
            this.panel2.Location = new System.Drawing.Point(0, 203);
            this.panel2.Margin = new System.Windows.Forms.Padding(0);
            this.panel2.Name = "panel2";
            this.panel2.Size = new System.Drawing.Size(237, 70);
            this.panel2.TabIndex = 9;
            // 
            // BtnClear
            // 
            this.BtnClear.ButtonImageSize = CustomControl.MyButton.imageSize.large;
            this.BtnClear.DialogResult = System.Windows.Forms.DialogResult.None;
            this.BtnClear.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.BtnClear.Location = new System.Drawing.Point(121, 11);
            this.BtnClear.Name = "BtnClear";
            this.BtnClear.Size = new System.Drawing.Size(80, 40);
            this.BtnClear.TabIndex = 4;
            this.BtnClear.Text = "清空";
            this.BtnClear.Click += new System.EventHandler(this.BtnClear_Click);
            // 
            // BtnSearch
            // 
            this.BtnSearch.ButtonImageSize = CustomControl.MyButton.imageSize.large;
            this.BtnSearch.DialogResult = System.Windows.Forms.DialogResult.None;
            this.BtnSearch.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.BtnSearch.Location = new System.Drawing.Point(35, 11);
            this.BtnSearch.Name = "BtnSearch";
            this.BtnSearch.Size = new System.Drawing.Size(80, 40);
            this.BtnSearch.TabIndex = 3;
            this.BtnSearch.Text = "查询";
            this.BtnSearch.Click += new System.EventHandler(this.BtnSearch_Click);
            // 
            // TxtHz
            // 
            this.TxtHz.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.TxtHz.Captain = "患者姓名";
            this.TxtHz.CaptainBackColor = System.Drawing.Color.Transparent;
            this.TxtHz.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtHz.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.TxtHz.CaptainWidth = 70F;
            this.TxtHz.ContentForeColor = System.Drawing.Color.Black;
            this.TxtHz.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.TxtHz.EditMask = null;
            this.TxtHz.Location = new System.Drawing.Point(3, 32);
            this.TxtHz.Multiline = false;
            this.TxtHz.Name = "TxtHz";
            this.TxtHz.PasswordChar = '\0';
            this.TxtHz.ReadOnly = false;
            this.TxtHz.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.TxtHz.SelectionStart = 0;
            this.TxtHz.SelectStart = 0;
            this.TxtHz.Size = new System.Drawing.Size(231, 23);
            this.TxtHz.TabIndex = 12;
            this.TxtHz.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.TxtHz.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtHz.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.TxtHz.Watermark = null;
            // 
            // TxtCode
            // 
            this.TxtCode.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.TxtCode.Captain = "门诊编号";
            this.TxtCode.CaptainBackColor = System.Drawing.Color.Transparent;
            this.TxtCode.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtCode.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.TxtCode.CaptainWidth = 70F;
            this.TxtCode.ContentForeColor = System.Drawing.Color.Black;
            this.TxtCode.DisabledForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(183)))), ((int)(((byte)(183)))), ((int)(((byte)(183)))));
            this.TxtCode.EditMask = null;
            this.TxtCode.Location = new System.Drawing.Point(3, 3);
            this.TxtCode.Multiline = false;
            this.TxtCode.Name = "TxtCode";
            this.TxtCode.PasswordChar = '\0';
            this.TxtCode.ReadOnly = false;
            this.TxtCode.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.TxtCode.SelectionStart = 0;
            this.TxtCode.SelectStart = 0;
            this.TxtCode.Size = new System.Drawing.Size(231, 23);
            this.TxtCode.TabIndex = 15;
            this.TxtCode.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.TxtCode.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TxtCode.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle;
            this.TxtCode.Watermark = null;
            // 
            // comboYs1
            // 
            this.comboYs1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.comboYs1.Bookmark = -1;
            this.comboYs1.Captain = "医    生";
            this.comboYs1.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.comboYs1.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.comboYs1.CaptainWidth = 70F;
            this.comboYs1.ColumnCaptionHeight = 20;
            this.comboYs1.DataSource = null;
            this.comboYs1.DataView = null;
            this.comboYs1.ItemHeight = 18;
            this.comboYs1.ItemTextFont = new System.Drawing.Font("宋体", 10.5F);
            this.comboYs1.Location = new System.Drawing.Point(3, 61);
            this.comboYs1.MaximumSize = new System.Drawing.Size(11667, 27);
            this.comboYs1.MinimumSize = new System.Drawing.Size(0, 23);
            this.comboYs1.Name = "comboYs1";
            this.comboYs1.ReadOnly = false;
            this.comboYs1.Row = 0;
            this.comboYs1.Size = new System.Drawing.Size(231, 23);
            this.comboYs1.TabIndex = 17;
            this.comboYs1.TextFont = new System.Drawing.Font("宋体", 10.5F);
            // 
            // comboKs1
            // 
            this.comboKs1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.comboKs1.Bookmark = -1;
            this.comboKs1.Captain = "科    室";
            this.comboKs1.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.comboKs1.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.comboKs1.CaptainWidth = 70F;
            this.comboKs1.ColumnCaptionHeight = 20;
            this.comboKs1.DataSource = null;
            this.comboKs1.DataView = null;
            this.comboKs1.ItemHeight = 18;
            this.comboKs1.ItemTextFont = new System.Drawing.Font("宋体", 10.5F);
            this.comboKs1.Location = new System.Drawing.Point(3, 90);
            this.comboKs1.MaximumSize = new System.Drawing.Size(11667, 27);
            this.comboKs1.MinimumSize = new System.Drawing.Size(0, 23);
            this.comboKs1.Name = "comboKs1";
            this.comboKs1.ReadOnly = false;
            this.comboKs1.Row = 0;
            this.comboKs1.Size = new System.Drawing.Size(231, 23);
            this.comboKs1.TabIndex = 18;
            this.comboKs1.TextFont = new System.Drawing.Font("宋体", 10.5F);
            // 
            // DateEditStart
            // 
            this.DateEditStart.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.DateEditStart.Captain = "门诊时间";
            this.DateEditStart.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.DateEditStart.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.DateEditStart.CaptainWidth = 70F;
            this.DateEditStart.InitialSelection = C1.Win.C1Input.InitialSelectionEnum.CaretAtEnd;
            this.DateEditStart.Location = new System.Drawing.Point(3, 119);
            this.DateEditStart.MaximumSize = new System.Drawing.Size(100000000, 23);
            this.DateEditStart.MinimumSize = new System.Drawing.Size(0, 20);
            this.DateEditStart.Name = "DateEditStart";
            this.DateEditStart.ReadOnly = false;
            this.DateEditStart.Size = new System.Drawing.Size(231, 23);
            this.DateEditStart.TabIndex = 5;
            this.DateEditStart.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.DateEditStart.ValueIsDbNull = false;
            this.DateEditStart.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom;
            this.DateEditStart.VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.DropDown;
            // 
            // DateEditEnd
            // 
            this.DateEditEnd.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.DateEditEnd.Captain = "   至   ";
            this.DateEditEnd.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.DateEditEnd.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.DateEditEnd.CaptainWidth = 70F;
            this.DateEditEnd.InitialSelection = C1.Win.C1Input.InitialSelectionEnum.CaretAtEnd;
            this.DateEditEnd.Location = new System.Drawing.Point(3, 148);
            this.DateEditEnd.MaximumSize = new System.Drawing.Size(100000000, 23);
            this.DateEditEnd.MinimumSize = new System.Drawing.Size(0, 20);
            this.DateEditEnd.Name = "DateEditEnd";
            this.DateEditEnd.ReadOnly = false;
            this.DateEditEnd.Size = new System.Drawing.Size(231, 23);
            this.DateEditEnd.TabIndex = 6;
            this.DateEditEnd.TextFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.DateEditEnd.ValueIsDbNull = false;
            this.DateEditEnd.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom;
            this.DateEditEnd.VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.DropDown;
            // 
            // single_rxStasCodg1
            // 
            this.single_rxStasCodg1.Captain = "处方状态";
            this.single_rxStasCodg1.CaptainFont = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.single_rxStasCodg1.CaptainForeColor = System.Drawing.SystemColors.ControlText;
            this.single_rxStasCodg1.CaptainWidth = 69F;
            this.single_rxStasCodg1.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList;
            this.single_rxStasCodg1.ItemHeight = 18;
            this.single_rxStasCodg1.ItemTextFont = new System.Drawing.Font("宋体", 10.5F);
            this.single_rxStasCodg1.Location = new System.Drawing.Point(3, 177);
            this.single_rxStasCodg1.MaximumSize = new System.Drawing.Size(10000, 23);
            this.single_rxStasCodg1.MinimumSize = new System.Drawing.Size(0, 20);
            this.single_rxStasCodg1.Name = "single_rxStasCodg1";
            this.single_rxStasCodg1.ReadOnly = false;
            this.single_rxStasCodg1.Size = new System.Drawing.Size(231, 23);
            this.single_rxStasCodg1.TabIndex = 19;
            this.single_rxStasCodg1.TextFont = new System.Drawing.Font("宋体", 10.5F);
            // 
            // panel1
            // 
            this.panel1.AutoSize = true;
            this.panel1.Controls.Add(this.c1ToolBar1);
            this.panel1.Dock = System.Windows.Forms.DockStyle.Top;
            this.panel1.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.panel1.Location = new System.Drawing.Point(0, 0);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(1227, 50);
            this.panel1.TabIndex = 1;
            // 
            // c1CommandHolder1
            // 
            this.c1CommandHolder1.Commands.Add(this.CmduploadChk);
            this.c1CommandHolder1.Commands.Add(this.CmdrxFixmedinsSignAndUpld);
            this.c1CommandHolder1.Commands.Add(this.CmdrxUndo);
            this.c1CommandHolder1.Commands.Add(this.CmdhospRxDetlQuery);
            this.c1CommandHolder1.Commands.Add(this.CmdrxChkInfoQuery);
            this.c1CommandHolder1.Commands.Add(this.CmdrxSetlInfoQuery);
            this.c1CommandHolder1.Commands.Add(this.Cmdcfypmucx);
            this.c1CommandHolder1.Owner = this;
            // 
            // ERxUpload
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1227, 742);
            this.Controls.Add(this.c1SplitContainer1);
            this.Controls.Add(this.c1CommandDock1);
            this.Controls.Add(this.panel1);
            this.Name = "ERxUpload";
            this.Text = "ERxUpload";
            this.Load += new System.EventHandler(this.ERxUpload_Load);
            ((System.ComponentModel.ISupportInitialize)(this.c1SplitContainer1)).EndInit();
            this.c1SplitContainer1.ResumeLayout(false);
            this.c1SplitterPanel2.ResumeLayout(false);
            this.c1SplitterPanel1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.c1CommandDock1)).EndInit();
            this.c1CommandDock1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.c1DockingTab1)).EndInit();
            this.c1DockingTab1.ResumeLayout(false);
            this.c1DockingTabPage1.ResumeLayout(false);
            this.tableLayoutPanel2.ResumeLayout(false);
            this.panel2.ResumeLayout(false);
            this.panel1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.c1CommandHolder1)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private C1.Win.C1Command.C1ToolBar c1ToolBar1;
        private C1.Win.C1SplitContainer.C1SplitContainer c1SplitContainer1;
        private C1.Win.C1SplitContainer.C1SplitterPanel c1SplitterPanel2;
        private CustomControl.MyGrid myGrid2;
        private C1.Win.C1SplitContainer.C1SplitterPanel c1SplitterPanel1;
        private CustomControl.MyGrid myGrid1;
        private C1.Win.C1Command.C1CommandDock c1CommandDock1;
        private C1.Win.C1Command.C1DockingTab c1DockingTab1;
        private C1.Win.C1Command.C1DockingTabPage c1DockingTabPage1;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel2;
        private CustomControl.MyDateEdit DateEditEnd;
        private CustomControl.MyDateEdit DateEditStart;
        private CustomControl.MyTextBox TxtHz;
        private CustomControl.MyTextBox TxtCode;
        private ZTHisControl.ComboYs comboYs1;
        private ZTHisControl.ComboKs comboKs1;
        private System.Windows.Forms.Panel panel1;
        private C1.Win.C1Command.C1CommandHolder c1CommandHolder1;
        private C1.Win.C1Command.C1Command CmduploadChk;
        private C1.Win.C1Command.C1CommandLink c1CommandLink1;
        private C1.Win.C1Command.C1CommandLink c1CommandLink2;
        private C1.Win.C1Command.C1Command CmdrxFixmedinsSignAndUpld;
        private C1.Win.C1Command.C1CommandLink c1CommandLink3;
        private C1.Win.C1Command.C1Command CmdrxUndo;
        private C1.Win.C1Command.C1CommandLink c1CommandLink4;
        private C1.Win.C1Command.C1Command CmdhospRxDetlQuery;
        private C1.Win.C1Command.C1CommandLink c1CommandLink5;
        private C1.Win.C1Command.C1Command CmdrxChkInfoQuery;
        private C1.Win.C1Command.C1CommandLink c1CommandLink6;
        private C1.Win.C1Command.C1Command CmdrxSetlInfoQuery;
        private System.Windows.Forms.Panel panel2;
        private CustomControl.MyButton BtnClear;
        private CustomControl.MyButton BtnSearch;
        private YBControl.Single_rxStasCodg single_rxStasCodg1;
        private C1.Win.C1Command.C1CommandLink c1CommandLink7;
        private C1.Win.C1Command.C1Command Cmdcfypmucx;
    }
}