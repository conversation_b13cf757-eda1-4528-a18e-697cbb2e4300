﻿/**  版本信息模板在安装目录下，可自行修改。
* M_Zd_YyJsr.cs
*
* 功 能： N/A
* 类 名： M_Zd_YyJsr
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/7/1 14:19:52   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace ModelOld
{
	/// <summary>
	/// M_Zd_YyJsr:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class M_Zd_YyJsr
	{
		public M_Zd_YyJsr()
		{}
		#region Model
		private string _yy_code;
		private string _jsr_code;
		private string _login_code;
		private string _jsr_name;
		private string _jsr_jc;
		private string _jsr_password;
		private string _jsr_memo;
		private string _jsr_py;
		private string _glz_code;
		private string _yf_code;
		private string _ys_code;
		private string _xm_ks;
		/// <summary>
		/// 
		/// </summary>
		public string Yy_Code
		{
			set{ _yy_code=value;}
			get{return _yy_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Jsr_Code
		{
			set{ _jsr_code=value;}
			get{return _jsr_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Login_Code
		{
			set{ _login_code=value;}
			get{return _login_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Jsr_Name
		{
			set{ _jsr_name=value;}
			get{return _jsr_name;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Jsr_Jc
		{
			set{ _jsr_jc=value;}
			get{return _jsr_jc;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Jsr_Password
		{
			set{ _jsr_password=value;}
			get{return _jsr_password;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Jsr_Memo
		{
			set{ _jsr_memo=value;}
			get{return _jsr_memo;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Jsr_Py
		{
			set{ _jsr_py=value;}
			get{return _jsr_py;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Glz_Code
		{
			set{ _glz_code=value;}
			get{return _glz_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Yf_Code
		{
			set{ _yf_code=value;}
			get{return _yf_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Ys_Code
		{
			set{ _ys_code=value;}
			get{return _ys_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Xm_Ks
		{
			set{ _xm_ks=value;}
			get{return _xm_ks;}
		}
		#endregion Model

	}
}

