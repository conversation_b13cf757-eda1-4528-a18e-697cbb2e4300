﻿Imports System.Drawing
Imports System.Windows.Forms
Imports C1.Win.C1FlexGrid

Public Class ZkSc2
    Dim bllzk2 As New BLLOld.B_Emr_BlZk2
    Dim bllzk1 As New BLLOld.B_Emr_BlZk1
    Dim Bl_Code As String
    Dim dt As DataTable
    Dim bllZkDj As New BLLOld.B_Emr_ZkDj
    Dim _dt As DataTable
    Dim dtrow As DataRow

    Public Sub New(ByVal trow As DataRow)
        ' 此调用是设计器所必需的。
        InitializeComponent()
        dtrow = trow
        Bl_Code = dtrow.Item("Bl_Code")
        ' 在 InitializeComponent() 调用之后添加任何初始化。
    End Sub
    Private Sub ZkSc2_Load(sender As System.Object, e As System.EventArgs) Handles MyBase.Load
        dt = bllzk2.GetZkPfList(Bl_Code).Tables(0)
        With MyFlexGrid1
            .Init()
            .DataSource = dt
            .AllowSorting = False
            .Init_Column("Bl_Code", "Bl_Code", 0, "左", "", False, False)
            .Init_Column("id", "id", 0, "左", "", False, False)
            .Init_Column("项目", "Mb_Name", 250, "左", "", False, True)
            .Init_Column("项目明细", "Mx_Name", 300, "左", "", False, False)
            .Init_Column("Kf_Pz", "Kf_Pz", 0, "中", "", False, False)
            .Init_Column("扣分标准", "Kf_PzNr", 100, "中", "", False, False)
            .Init_Column("Zk_Kf", "Zk_Kf", 0, "中", "", False, False)
            .Init_Column("数量", "Kf_Count", 100, "中", "", True, False)
            .Init_Column("分值", "Kf", 100, "中", "", False, False)
            .Init_Column("模版编码", "Mb_Code", 0, "中", "", False, False)
        End With

        Dim rg As CellRange
        Dim rg2 As CellRange
        For i As Integer = 1 To dt.Rows.Count
            rg = MyFlexGrid1.GetCellRange(i, 5)
            rg2 = MyFlexGrid1.GetCellRange(i, 8)
            If rg.Data = 1 Or rg.Data = 2 Then
                rg2.Style = MyFlexGrid1.Styles("bool")
                If rg2.Data Is DBNull.Value Then
                    rg2.Data = 0
                End If

            End If
            If rg.Data = 3 Then
                rg2.Style = MyFlexGrid1.Styles("string")
            End If
        Next

        Dim dtpf As DataTable
        dtpf = bllzk1.GetListwithZkdj("bl_code='" & Bl_Code & "'").Tables(0)
        If dtpf.Rows.Count > 0 Then
            PFLbl.Text = dtpf.Rows(0).Item("Zk_Pf")
            DjLbl.Text = dtpf.Rows(0).Item("zkDj_Name")
        Else
            PFLbl.Text = ""
            DjLbl.Text = ""
        End If
    End Sub




    Private Sub MyFlexGrid1_AfterEdit(sender As System.Object, e As C1.Win.C1FlexGrid.RowColEventArgs) Handles MyFlexGrid1.AfterEdit
        Dim rg1 As CellRange
        Dim rg2 As CellRange
        Dim rg3 As CellRange

        rg1 = MyFlexGrid1.GetCellRange(e.Row, 7)
        rg2 = MyFlexGrid1.GetCellRange(e.Row, 8)
        rg3 = MyFlexGrid1.GetCellRange(e.Row, 9)

        If rg2.Data Is DBNull.Value OrElse rg2.Data = 0 Then
            rg3.Data = Nothing
        Else
            rg3.Data = rg1.Data * rg2.Data
        End If

    End Sub


#Region "自定义函数"
    Private Sub CentTj()

        Dim rg3 As CellRange

        Dim Kf As Decimal = 0
        For i As Integer = 1 To dt.Rows.Count
            rg3 = MyFlexGrid1.GetCellRange(i, 9)
            If rg3.Data IsNot DBNull.Value AndAlso rg3.Data <> 0 Then
                Kf = Kf + rg3.Data
            End If
        Next
        PFLbl.Text = 100 - Kf

        _dt = bllZkDj.GetList("ZkDj_MaxValue>=" & PFLbl.Text & " and ZkDj_MinValue<=" & PFLbl.Text).Tables(0)
        If _dt.Rows.Count > 0 Then
            DjLbl.Text = _dt.Rows(0).Item("ZkDj_Name")
        End If
    End Sub
    Private Sub datazk2_add(ByVal row As DataRow)
        Dim m_emr_blzk2 As New ModelOld.M_Emr_BlZk2
        m_emr_blzk2.Bl_Code = row.Item("Bl_Code")
        m_emr_blzk2.id = row.Item("id")
        m_emr_blzk2.Mb_Code = row.Item("Mb_Code")
        m_emr_blzk2.Kf_Count = row.Item("Kf_Count")
        m_emr_blzk2.Kf = row.Item("kf")
        bllzk2.Add(m_emr_blzk2)
    End Sub
    Private Sub datazk2_edit(ByVal row As DataRow)
        Dim m_emr_blzk2 As New ModelOld.M_Emr_BlZk2
        m_emr_blzk2.Bl_Code = row.Item("Bl_Code")
        m_emr_blzk2.id = row.Item("id")
        m_emr_blzk2.Mb_Code = row.Item("Mb_Code")
        m_emr_blzk2.Kf_Count = row.Item("Kf_Count")
        m_emr_blzk2.Kf = row.Item("kf")
        bllzk2.Update(m_emr_blzk2)
    End Sub
    Private Sub datazk2_delete(ByVal row As DataRow)
        bllzk2.Delete(Bl_Code, row.Item("id"), row.Item("Mb_Code"))
    End Sub
    Private Sub datazk1_edit()
        Dim m_emr_blzk1 As New ModelOld.M_Emr_BlZk1
        If PFLbl.Text & "" = "" Or DjLbl.Text & "" = "" Then
            Try
                CentTj()
            Catch ex As Exception

            End Try

        End If
        m_emr_blzk1.Zk_Pf = PFLbl.Text
        If _dt.Rows.Count = 0 Then
            m_emr_blzk1.Zkdj_Code = ""
        Else
            m_emr_blzk1.Zkdj_Code = _dt.Rows(0).Item("ZkDj_code")
        End If
        m_emr_blzk1.Bl_Code = Bl_Code
        Try
            bllzk1.Update(m_emr_blzk1)
        Catch ex As Exception
            HisControl.msg.Show(ex.Message, "提示")
            Exit Sub
        End Try
        HisControl.msg.Show("保存成功!", "提示")
    End Sub
    Private Sub datazk1_add()
        Dim m_emr_blzk1 As New ModelOld.M_Emr_BlZk1
        If PFLbl.Text & "" = "" Or DjLbl.Text & "" = "" Then
            CentTj()
        End If
        m_emr_blzk1.Jsr_Code = HisVar.HisVar.JsrCode
        m_emr_blzk1.Zk_Date = Now()
        m_emr_blzk1.Zk_Pf = PFLbl.Text
        If _dt.Rows.Count = 0 Then
            m_emr_blzk1.Zkdj_Code = ""
        Else
            m_emr_blzk1.Zkdj_Code = _dt.Rows(0).Item("ZkDj_code")
        End If
        m_emr_blzk1.Bl_Code = Bl_Code
        Try
            bllzk1.Add(m_emr_blzk1)
        Catch ex As Exception
            HisControl.msg.Show(ex.Message, "提示")
            Exit Sub
        End Try
        HisControl.msg.Show("保存成功!", "提示")

    End Sub

    '更新zksc窗体的分数
    Private Sub data_edit()
        Dim My_Row As DataRow = dtrow
        'Dim mbdatatable As New DataTable
        'mbdatatable = zd_Emr_Ebbll.GetListmblb(" Mb_Code='" & Trim(mbMyDtComobo.SelectedValue & "") & "'").Tables(0)
        Try

            With My_Row
                .BeginEdit()
                If PFLbl.Text = "" Then
                    .Item("Zk_Pf") = DBNull.Value
                Else
                    .Item("Zk_Pf") = PFLbl.Text
                End If

                .Item("ZkDj_name") = DjLbl.Text
                .EndEdit()
            End With



        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Critical, "提示:")
            My_Row.CancelEdit()
            Exit Sub
        Finally
            'Name_TextBox.Select()
        End Try
    End Sub

    Private Sub Print_Flex()
        Dim Dlg As Form = MyFlexGrid1.PrintParameters.PrintPreviewDialog
        With Dlg
            .Text = "打印报表"
            .StartPosition = FormStartPosition.Manual
            .WindowState = FormWindowState.Maximized
            .MaximizeBox = True
        End With

        Dim My_Doc As Printing.PrintDocument = MyFlexGrid1.PrintParameters.PrintDocument()
        With My_Doc.DefaultPageSettings
            .Landscape = True
            .Margins.Left = 100
            .Margins.Top = 60
            .Margins.Bottom = 100
        End With

        With MyFlexGrid1
            .PrintParameters.HeaderFont = New Font("宋体", 14, FontStyle.Bold)
            .PrintParameters.FooterFont = New Font("宋体", 9, FontStyle.Regular)
            .PrintGrid("统计", PrintGridFlags.ExtendLastCol + PrintGridFlags.ActualSize + PrintGridFlags.ShowPreviewDialog, Chr(9) + "评分:" + PFLbl.Text + ",等级:" + DjLbl.Text + Chr(9) + "打印日期:" + Now.ToString("yyyy-MM-dd HH:mm:ss"), Chr(9) + Chr(9) + "第{0}/{1}页")
        End With

    End Sub

#End Region

    Private Sub MyButton1_Click(sender As System.Object, e As System.EventArgs) Handles MyButton1.Click
        CentTj()
    End Sub

    Private Sub MyButton3_Click(sender As System.Object, e As System.EventArgs) Handles MyButton3.Click
        Try
            For Each Row As DataRow In MyFlexGrid1.DataSource.Rows
                If bllzk2.Exists(Bl_Code, Row.Item("id"), Row.Item("Mb_Code")) Then
                    '修改
                    If Row.Item("Kf_Count") Is DBNull.Value Or Row.Item("Kf_Count") & "" = "" Or Row.Item("Kf_Count") & "" = 0 Then
                        datazk2_delete(Row)
                    Else
                        If Row.Item("Kf_Pz") & "" = 3 Then
                            datazk2_edit(Row)
                        End If

                    End If

                Else
                    '添加
                    If Row.Item("Kf_Count") Is DBNull.Value Or Row.Item("Kf_Count") & "" = "" Or Row.Item("Kf_Count") & "" = 0 Then

                    Else
                        datazk2_add(Row)
                    End If

                End If
            Next
        Catch ex As Exception

        End Try
        If bllzk1.Exists(Bl_Code) Then
            datazk1_edit()
        Else
            datazk1_add()
        End If
        data_edit()

    End Sub

    Private Sub deleteButton_Click(sender As System.Object, e As System.EventArgs) Handles deleteButton.Click
        Try
            bllzk2.DeleteAllbybl(Bl_Code)
            bllzk1.Delete(Bl_Code)
        Catch ex As Exception
            HisControl.msg.Show(ex.Message, "提示")
            Exit Sub
        End Try
        HisControl.msg.Show("删除成功!", "提示")

        ZkSc2_Load(Nothing, Nothing)
        data_edit()
        Me.Close()
    End Sub

    Private Sub MyButton2_Click(sender As System.Object, e As System.EventArgs) Handles MyButton2.Click
        Print_Flex()
    End Sub
End Class
