﻿Imports System.Data.SqlClient
Imports BaseClass
Imports HisControl

Public Class Cq_Cf31

#Region "定义变量"

    Dim V_Xx_Code As String = ""                '药品__明细编码
    Dim My_FindView As New DataView             '药品__字典视图
    Dim V_Yp_Code As String                     '药品编码
    Dim My_DataSet As New DataSet

    Dim V_Yf_Sl As String
    Dim V_Yf_Lsj As String
    Dim V_Yf_Code As String

    Dim YpDt As DataTable
#End Region

#Region "传参"
    Dim Rform As BaseForm
    Dim Rinsert As Boolean
    'Dim Rdate As Date
    Dim Rrow As DataRow
    Dim RZbtb As DataTable
    Dim Rtdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Dim Rlb As Label
    Dim Rzbadt As SqlDataAdapter
    Dim Rds As DataSet
    Dim Rcode As String
    Dim Rrc As C_RowChange
    Dim RBl_Code As String

#End Region

    Public Sub New(ByVal tform As BaseForm, ByVal tinsert As Boolean, ByVal trow As DataRow, ByVal tZbtb As DataTable, ByVal tlb As Label, ByVal ttdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid, ByVal tzbadt As SqlDataAdapter, ByVal tds As DataSet, ByVal tcode As String, ByVal tyfcode As String, ByVal tBl_Code As String)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        Rform = tform
        Rinsert = tinsert
        'Rdate = tdate
        Rrow = trow
        RZbtb = tZbtb
        Rtdbgrid = ttdbgrid
        Rlb = tlb
        Rzbadt = tzbadt
        Rds = tds
        Rcode = tcode

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        V_Yf_Sl = "Yf_Sl" & Mid(tyfcode, 6)
        V_Yf_Lsj = "Yf_Lsj" & Mid(tyfcode, 6)
        V_Yf_Code = tyfcode

        RBl_Code = tBl_Code

    End Sub

    Protected Overrides Sub OnKeyDown(ByVal e As System.Windows.Forms.KeyEventArgs)
        MyBase.OnKeyDown(e)
        If e.Control = True And e.KeyCode = Keys.S Then
            Comm1.Select()
            Call Comm_Click(Comm1, Nothing)
        End If
    End Sub


    Private Sub Cq_Cf31_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        With Rform
            Call Form_Init()
            Me.Location = New Point(.Left + (.Width - Me.Width) / 2, .Top + (.Height - Me.Height))

            If Rinsert = True Then Call Data_Clear() Else Call Data_Show(Rrow)
        End With
    End Sub

#Region "窗体__事件"

    Private Sub Form_Init()

        '按扭初始化
        Call P_Comm(Me.Comm1)
        Call P_Comm(Me.Comm2)


        With Me.C1Numeric1
            .Value = 0
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .CustomFormat = "###,###,###.####"
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .ShowContextMenu = False
            .EmptyAsNull = False
        End With

        With Me.C1Numeric2
            .Value = 0
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .CustomFormat = "###,###,##0.00##"
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .ShowContextMenu = False
            .EmptyAsNull = False
        End With

        With Me.C1Numeric4
            .Value = 0
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .CustomFormat = "###,###,##0.00##"
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .ShowContextMenu = False
            .EmptyAsNull = False
        End With


        ComboMzZyYp1.Init(V_Yf_Code)
        YpDt = ComboMzZyYp1.DataView.Table
        YpDt.PrimaryKey = New DataColumn() {YpDt.Columns("Xx_Code")}
        YpDt.Columns("Yf_Sl").ReadOnly = False
        ComboMzZyYp1.RowFilterTextNull = "Yf_Sl>0"

        ComboAdministration1.Init()
        ComboFrequency1.Init()
    End Sub

#End Region

#Region "其它项目"


    Private Sub C1Numeric_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles C1Numeric1.KeyPress
        If e.KeyChar <> Chr(Keys.Return) Then Exit Sub
        e.Handled = True
        System.Windows.Forms.SendKeys.Send("{Tab}")
    End Sub

    Private Sub C1Numeric1_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1Numeric1.Validated

        If C1Numeric1.Value > IIf(IsDBNull(ComboMzZyYp1.Columns(V_Yf_Sl).Value), 0, ComboMzZyYp1.Columns(V_Yf_Sl).Value) Then
            MsgBox("销售数量不能大于库存数量,请修改!", MsgBoxStyle.Critical, "提示")
            C1Numeric1.Value = 0
            C1Numeric1.Select()
            Exit Sub
        End If
        C1Numeric4.Value = C1Numeric1.Value * C1Numeric2.Value
    End Sub

#End Region

#Region "控件__动作"

    Private Sub Comm_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.Click, Comm2.Click
        Select Case sender.tag
            Case "保存"
                Dim My_Tb As DataTable = RZbtb
                Dim My_NewRow As DataRow = My_Tb.NewRow

                If ComboMzZyYp1.SelectedValue = "" Then
                    Beep()
                    MsgBox("药品编码不能为空,按任意键返回!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    ComboMzZyYp1.Select()
                    Exit Sub
                End If
                If C1Numeric1.Value = 0 Then
                    Beep()
                    MsgBox("使用数量不能为零！", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示")
                    C1Numeric1.Select()
                    Exit Sub
                End If

                If HisVar.HisVar.Sqldal.GetSingle("select count(1) from Auto_Cfyp where AutoCf_Code = '" & Rcode & "'  and SUBSTRING(XX_CODE, 1, 11) = SUBSTRING('" & ComboMzZyYp1.SelectedValue & "', 1, 11) and SUBSTRING(XX_CODE, 12, 5) <> SUBSTRING('" & ComboMzZyYp1.SelectedValue & "', 12, 5)  ") > 0 Then  '为医保接口做的限定
                    MsgBox("药品名称相同，批号不同,不能开在同一处方！", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示")
                    'C1Numeric1.Select()
                    Exit Sub
                End If

                If Rinsert = True Then      '增加记录
                    Call Save_Add()
                    Rtdbgrid.Refresh()
                Else                                '编辑记录
                    Call Save_Edit()
                End If

            Case "取消"
                ComboMzZyYp1.SelectedValue = -1
                ComboMzZyYp1.Text = ""
                Rtdbgrid.Focus()
                Me.Close()
        End Select
    End Sub

    Private Sub C1Move_Click(ByVal sender As Object, ByVal e As C1.Win.C1Command.ClickEventArgs) Handles Move2.Click, Move3.Click, Move5.Click
        If sender.text = "新增" Then
            Rinsert = True
            Call Data_Clear()
        Else
            Rinsert = False
            Select Case sender.text
                Case "上移"
                    Rtdbgrid.MovePrevious()
                Case "下移"
                    Rtdbgrid.MoveNext()
            End Select
        End If
    End Sub

#Region "C1Combo1"

    Private Sub C1Combo1_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles ComboMzZyYp1.Validated
        Me.CancelButton = Comm2
    End Sub

    Private Sub C1Combo1_RowChange(ByVal sender As Object, ByVal e As System.EventArgs) Handles ComboMzZyYp1.RowChange

        If ComboMzZyYp1.WillChangeToValue = "" Then
            Label4.Text = ""
            Label3.Text = ""
            Label1.Text = ""
            Label5.Text = ""
            Label8.Text = ""
            C1Numeric2.Value = 0
            V_Xx_Code = ""
        Else
            V_Xx_Code = ComboMzZyYp1.Columns("Xx_Code").Value & ""
            Label4.Text = ComboMzZyYp1.Columns("Mx_Gyzz").Value & ""
            Label3.Text = ComboMzZyYp1.Columns("Mx_Cd").Value & ""
            Label1.Text = ComboMzZyYp1.Columns("Mx_Gg").Value & ""
            Label5.Text = ComboMzZyYp1.Columns("Jx_Name").Value & ""
            Label8.Text = Format(ComboMzZyYp1.Columns("Yf_Sl").Value, "###,##0.####") & " " & ComboMzZyYp1.Columns("Mx_Xsdw").Value & ""
            C1Numeric2.Value = ComboMzZyYp1.Columns("Yf_Lsj").Value & ""
        End If
    End Sub

#End Region

#End Region

#Region "数据__编辑"

    Private Sub Data_Clear()
        Rinsert = True
        If Move5.Enabled = True Then Move5.Enabled = False
        V_Xx_Code = ""                                          '药品编码

        ComboMzZyYp1.Enabled = True
        ComboMzZyYp1.SelectedIndex = -1
        ComboMzZyYp1.Text = ""                                      '药品明细编码

        '备注
        C1Numeric1.Value = 1                                    '采购数量
        C1Numeric2.Value = 0                                    '采购单价
        C1Numeric4.Value = 0                                    '采购金额

        Label8.Text = ""

        ComboMzZyYp1.Select()

    End Sub

    Private Sub Data_Show(ByVal tmp_Row As DataRow)
        Rinsert = False
        If Move5.Enabled = False Then Move5.Enabled = True
        T_Label2.Text = CStr((Rtdbgrid.Row) + 1)

        Rrow = tmp_Row
        With Rrow
            ComboMzZyYp1.DataView.RowFilter = "Yf_Sl>0 or Xx_Code='" & .Item("Xx_Code") & "" & "' "
            V_Xx_Code = .Item("Xx_Code") & ""
            '药品名称
            Dim Yf_Row As DataRow
            Yf_Row = YpDt.Rows.Find(V_Xx_Code)
            With Yf_Row
                .BeginEdit()
                .Item("Yf_Sl") = .Item("Yf_Sl") + Rrow.Item("Cf_Sl")
                .EndEdit()
            End With
            YpDt.AcceptChanges()

            '药品名称
            ComboMzZyYp1.SelectedValue = V_Xx_Code
            C1Numeric1.Value = .Item("Cf_Sl")                   '采购数量
            C1Numeric2.Value = .Item("Cf_Dj") & ""
            C1Numeric4.Value = .Item("Cf_Money")                '采购金额
        End With
        ComboMzZyYp1.Select()


    End Sub

    Private Sub Save_Add()
        Dim My_Tb As DataTable = RZbtb
        Dim My_NewRow As DataRow = My_Tb.NewRow
        With My_NewRow
            .BeginEdit()
            .Item("AutoCf_Code") = Rcode
            .Item("Xx_Code") = V_Xx_Code                        '药品明细编码
            .Item("Cf_Sl") = C1Numeric1.Value                   '采购单价
            .Item("Cf_Dj") = C1Numeric2.Value                   '采购单价
            .Item("Cf_Money") = C1Numeric1.Value * C1Numeric2.Value                '采购金额
            .Item("Cf_Lb") = ComboMzZyYp1.Columns("Dl_Name").Value
            .Item("Yp_Yxq") = ComboMzZyYp1.Columns("Yp_Yxq").Value
            .Item("Mx_Gg") = Trim(Label1.Text & "")
            .Item("Mx_Cd") = Trim(Label3.Text & "")
            .Item("Yp_Name") = Trim(ComboMzZyYp1.Text & "")
            .EndEdit()
        End With

        Try

            My_Tb.Rows.Add(My_NewRow)

            With Rzbadt.InsertCommand
                Try

                    .Parameters(0).Value = HisVar.HisVar.WsyCode
                    .Parameters(1).Value = Rcode
                    .Parameters(2).Value = My_NewRow.Item("Xx_Code") & ""
                    .Parameters(3).Value = My_NewRow.Item("Cf_Sl")
                    .Parameters(4).Value = My_NewRow.Item("Cf_Dj")
                    .Parameters(5).Value = My_NewRow.Item("Cf_Money")
                    .Parameters(6).Value = My_NewRow.Item("Cf_Lb")
                    Call P_Conn(True)
                    .ExecuteNonQuery()
                    Call P_Conn(False)

                    My_NewRow.AcceptChanges()
                Catch ex As Exception
                    Beep()
                    MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
                End Try
            End With

            Rtdbgrid.MoveLast()

            Call Data_Clear()                                   '清空记录
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            ComboMzZyYp1.Select()
        End Try
    End Sub

    Private Sub Save_Edit()

        With Rrow
            Try
                .BeginEdit()
                .Item("AutoCf_Code") = Rcode
                .Item("Xx_Code") = V_Xx_Code                        '药品明细编码
                .Item("Cf_Sl") = C1Numeric1.Value                   '采购单价
                .Item("Cf_Dj") = C1Numeric2.Value                   '采购单价
                .Item("Cf_Money") = C1Numeric1.Value * C1Numeric2.Value                '采购金额
                .Item("Cf_Lb") = ComboMzZyYp1.Columns("Dl_Name").Value
                .Item("Yp_Yxq") = ComboMzZyYp1.Columns("Yp_Yxq").Value
                .Item("Mx_Gg") = Trim(Label1.Text & "")
                .Item("Mx_Cd") = Trim(Label3.Text & "")
                .Item("Yp_Name") = Trim(ComboMzZyYp1.Text & "")
                .EndEdit()
            Catch ex As Exception
                Beep()
                MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            End Try
        End With

        With Rzbadt.UpdateCommand

            .Parameters(0).Value = Rrow.Item("Xx_Code")
            .Parameters(1).Value = Rrow.Item("Cf_Sl")
            .Parameters(2).Value = Rrow.Item("Cf_Dj")
            .Parameters(3).Value = Rrow.Item("Cf_Money")
            .Parameters(4).Value = Rrow.Item("Cf_Lb")
            .Parameters(5).Value = Rrow.Item("Cf_Id", DataRowVersion.Original)


            Try
                Call P_Conn(True)
                .ExecuteNonQuery()
                Call P_Conn(False)
                Rrow.AcceptChanges()
            Catch ex As Exception
                Beep()
                MsgBox(ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
            End Try
        End With

    End Sub

#End Region

#Region "鼠标__外观"

    Private Sub P_Comm(ByVal Bt As Button)

        With Bt
            Select Case .Tag
                Case "保存"
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")

                Case "取消"
                    .Left = Me.Comm1.Left + Me.Comm1.Width + 1
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
                    .Width = Me.Comm1.Width
            End Select

            .Text = ""
            .FlatStyle = FlatStyle.Flat
            .FlatAppearance.BorderSize = 0
            .BackgroundImageLayout = ImageLayout.Center
        End With

    End Sub

    Private Sub Comm_MouseEnter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.MouseEnter, Comm2.MouseEnter
        Select Case sender.tag
            Case "保存"
                Me.Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存2")
                Me.Comm1.Cursor = Cursors.Hand

            Case "取消"
                Me.Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消2")
                Me.Comm2.Cursor = Cursors.Hand

        End Select

    End Sub

    Private Sub Comm_MouseLeave(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.MouseLeave, Comm2.MouseLeave
        Select Case sender.tag

            Case "保存"
                Me.Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")
                Me.Comm1.Cursor = Cursors.Default

            Case "取消"
                Me.Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
                Me.Comm2.Cursor = Cursors.Default

        End Select

    End Sub

    Private Sub Comm_MouseDown(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm1.MouseDown, Comm2.MouseDown
        Select Case sender.tag
            Case "保存"
                Me.Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存3")

            Case "取消"
                Me.Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消3")
        End Select
    End Sub

    Private Sub Comm_MouseUp(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm1.MouseUp, Comm2.MouseUp
        Select Case sender.tag
            Case "保存"
                Me.Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存2")

            Case "取消"
                Me.Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消2")
        End Select
    End Sub


#End Region

#Region "输入法设置"

    Private Sub C1Numeric1_KeyDown(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles  C1Numeric1.GotFocus
        InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages(0)
    End Sub

#End Region

End Class