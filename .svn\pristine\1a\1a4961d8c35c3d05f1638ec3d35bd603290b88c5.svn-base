﻿/**  版本信息模板在安装目录下，可自行修改。
* M_Materials_InOut_Class_Dict.cs
*
* 功 能： N/A
* 类 名： M_Materials_InOut_Class_Dict
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016-12-03 09:37:10   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace ModelOld
{
	/// <summary>
	/// 物资出入类别
	/// </summary>
	[Serializable]
	public partial class M_Materials_InOut_Class_Dict
	{
		public M_Materials_InOut_Class_Dict()
		{}
		#region Model
		private string _materialsinout_code;
		private string _materialsinout_name;
		private string _MaterialsInOut_py;
		private string _MaterialsInOut_wb;
		private string _inouttype;
		private string _MaterialsInOut_memo;
		private int? _serial_no;
		private bool _isuse;
		/// <summary>
		/// 出入类别编码
		/// </summary>
		public string MaterialsInOut_Code
		{
			set{ _materialsinout_code=value;}
			get{return _materialsinout_code;}
		}
		/// <summary>
		/// 名称
		/// </summary>
		public string MaterialsInOut_Name
		{
			set{ _materialsinout_name=value;}
			get{return _materialsinout_name;}
		}
		/// <summary>
		/// 拼音
		/// </summary>
		public string MaterialsInOut_Py
		{
			set{ _MaterialsInOut_py=value;}
			get{return _MaterialsInOut_py;}
		}
		/// <summary>
		/// 五笔
		/// </summary>
		public string MaterialsInOut_Wb
		{
			set{ _MaterialsInOut_wb=value;}
			get{return _MaterialsInOut_wb;}
		}
		/// <summary>
		/// 出入库类型(出库、入库)
		/// </summary>
		public string InOutType
		{
			set{ _inouttype=value;}
			get{return _inouttype;}
		}
		/// <summary>
		/// 备注
		/// </summary>
		public string MaterialsInOut_Memo
		{
			set{ _MaterialsInOut_memo=value;}
			get{return _MaterialsInOut_memo;}
		}
		/// <summary>
		/// 排列顺序
		/// </summary>
		public int? Serial_No
		{
			set{ _serial_no=value;}
			get{return _serial_no;}
		}
		/// <summary>
		/// 真为启用，假为停用，默认值为真
		/// </summary>
		public bool IsUse
		{
			set{ _isuse=value;}
			get{return _isuse;}
		}
		#endregion Model

	}
}

