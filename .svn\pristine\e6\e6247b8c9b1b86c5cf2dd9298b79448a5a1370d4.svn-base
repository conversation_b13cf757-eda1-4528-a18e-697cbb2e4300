﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class Materials_Buy_In2
    Inherits HisControl.BaseChild

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel()
        Me.Materials_Comobo = New CustomControl.MyDtComobo()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.GG_MyTextBox1 = New CustomControl.MyTextBox()
        Me.BZDW_MyTextBox3 = New CustomControl.MyTextBox()
        Me.SZDW_MyTextBox4 = New CustomControl.MyTextBox()
        Me.SCCJ_MyTextBox2 = New CustomControl.MyTextBox()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.YXQ_MyDateEdit1 = New CustomControl.MyDateEdit()
        Me.BZSL_MyNumericEdit1 = New CustomControl.MyNumericEdit()
        Me.CGDJ_MyNumericEdit2 = New CustomControl.MyNumericEdit()
        Me.CGJE_MyNumericEdit3 = New CustomControl.MyNumericEdit()
        Me.Memo = New CustomControl.MyTextBox()
        Me.RKSL_MyNumericEdit1 = New CustomControl.MyNumericEdit()
        Me.RKDJMyNumericEdit1 = New CustomControl.MyNumericEdit()
        Me.RKJE_MyNumericEdit2 = New CustomControl.MyNumericEdit()
        Me.CFBL_MyNumericEdit1 = New CustomControl.MyNumericEdit()
        Me.Ph_MyDtComobo1 = New CustomControl.MyDtComobo()
        Me.Save_MyButton1 = New CustomControl.MyButton()
        Me.Cancle_MyButton1 = New CustomControl.MyButton()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.TableLayoutPanel1.SuspendLayout()
        Me.Panel1.SuspendLayout()
        Me.SuspendLayout()
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.ColumnCount = 5
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 8.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 183.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 168.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 161.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 45.0!))
        Me.TableLayoutPanel1.Controls.Add(Me.Materials_Comobo, 1, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.Label3, 1, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.GG_MyTextBox1, 1, 3)
        Me.TableLayoutPanel1.Controls.Add(Me.BZDW_MyTextBox3, 1, 4)
        Me.TableLayoutPanel1.Controls.Add(Me.SZDW_MyTextBox4, 2, 4)
        Me.TableLayoutPanel1.Controls.Add(Me.SCCJ_MyTextBox2, 2, 3)
        Me.TableLayoutPanel1.Controls.Add(Me.Label1, 1, 5)
        Me.TableLayoutPanel1.Controls.Add(Me.YXQ_MyDateEdit1, 2, 6)
        Me.TableLayoutPanel1.Controls.Add(Me.BZSL_MyNumericEdit1, 1, 7)
        Me.TableLayoutPanel1.Controls.Add(Me.CGDJ_MyNumericEdit2, 2, 7)
        Me.TableLayoutPanel1.Controls.Add(Me.CGJE_MyNumericEdit3, 3, 7)
        Me.TableLayoutPanel1.Controls.Add(Me.Memo, 1, 9)
        Me.TableLayoutPanel1.Controls.Add(Me.RKSL_MyNumericEdit1, 1, 8)
        Me.TableLayoutPanel1.Controls.Add(Me.RKDJMyNumericEdit1, 2, 8)
        Me.TableLayoutPanel1.Controls.Add(Me.RKJE_MyNumericEdit2, 3, 8)
        Me.TableLayoutPanel1.Controls.Add(Me.CFBL_MyNumericEdit1, 3, 4)
        Me.TableLayoutPanel1.Controls.Add(Me.Ph_MyDtComobo1, 1, 6)
        Me.TableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(0, 0)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.RowCount = 11
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 3.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 26.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 8.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 26.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 26.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 8.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 26.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 26.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 26.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 95.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 8.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 20.0!))
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(524, 278)
        Me.TableLayoutPanel1.TabIndex = 2
        '
        'Materials_Comobo
        '
        Me.Materials_Comobo.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Materials_Comobo.Captain = "物资名称"
        Me.Materials_Comobo.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Materials_Comobo.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.Materials_Comobo.CaptainWidth = 60.0!
        Me.Materials_Comobo.DataSource = Nothing
        Me.Materials_Comobo.ItemHeight = 18
        Me.Materials_Comobo.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Materials_Comobo.Location = New System.Drawing.Point(11, 6)
        Me.Materials_Comobo.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.Materials_Comobo.MinimumSize = New System.Drawing.Size(0, 20)
        Me.Materials_Comobo.Name = "Materials_Comobo"
        Me.Materials_Comobo.ReadOnly = False
        Me.Materials_Comobo.Size = New System.Drawing.Size(177, 20)
        Me.Materials_Comobo.TabIndex = 0
        Me.Materials_Comobo.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'Label3
        '
        Me.Label3.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label3.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.TableLayoutPanel1.SetColumnSpan(Me.Label3, 3)
        Me.Label3.Location = New System.Drawing.Point(11, 32)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(506, 2)
        Me.Label3.TabIndex = 214
        Me.Label3.Text = "Label1"
        '
        'GG_MyTextBox1
        '
        Me.GG_MyTextBox1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GG_MyTextBox1.Captain = "规    格"
        Me.GG_MyTextBox1.CaptainBackColor = System.Drawing.Color.Transparent
        Me.GG_MyTextBox1.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.GG_MyTextBox1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.GG_MyTextBox1.CaptainWidth = 60.0!
        Me.GG_MyTextBox1.ContentForeColor = System.Drawing.Color.Black
        Me.GG_MyTextBox1.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.GG_MyTextBox1.EditMask = Nothing
        Me.GG_MyTextBox1.Location = New System.Drawing.Point(11, 40)
        Me.GG_MyTextBox1.Multiline = False
        Me.GG_MyTextBox1.Name = "GG_MyTextBox1"
        Me.GG_MyTextBox1.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.GG_MyTextBox1.ReadOnly = False
        Me.GG_MyTextBox1.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.GG_MyTextBox1.SelectionStart = 0
        Me.GG_MyTextBox1.SelectStart = 0
        Me.GG_MyTextBox1.Size = New System.Drawing.Size(177, 20)
        Me.GG_MyTextBox1.TabIndex = 1
        Me.GG_MyTextBox1.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.GG_MyTextBox1.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.GG_MyTextBox1.Watermark = Nothing
        '
        'BZDW_MyTextBox3
        '
        Me.BZDW_MyTextBox3.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.BZDW_MyTextBox3.Captain = "包装单位"
        Me.BZDW_MyTextBox3.CaptainBackColor = System.Drawing.Color.Transparent
        Me.BZDW_MyTextBox3.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.BZDW_MyTextBox3.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.BZDW_MyTextBox3.CaptainWidth = 60.0!
        Me.BZDW_MyTextBox3.ContentForeColor = System.Drawing.Color.Black
        Me.BZDW_MyTextBox3.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.BZDW_MyTextBox3.EditMask = Nothing
        Me.BZDW_MyTextBox3.Location = New System.Drawing.Point(11, 66)
        Me.BZDW_MyTextBox3.Multiline = False
        Me.BZDW_MyTextBox3.Name = "BZDW_MyTextBox3"
        Me.BZDW_MyTextBox3.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.BZDW_MyTextBox3.ReadOnly = False
        Me.BZDW_MyTextBox3.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.BZDW_MyTextBox3.SelectionStart = 0
        Me.BZDW_MyTextBox3.SelectStart = 0
        Me.BZDW_MyTextBox3.Size = New System.Drawing.Size(177, 20)
        Me.BZDW_MyTextBox3.TabIndex = 3
        Me.BZDW_MyTextBox3.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.BZDW_MyTextBox3.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.BZDW_MyTextBox3.Watermark = Nothing
        '
        'SZDW_MyTextBox4
        '
        Me.SZDW_MyTextBox4.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SZDW_MyTextBox4.Captain = "散装单位"
        Me.SZDW_MyTextBox4.CaptainBackColor = System.Drawing.Color.Transparent
        Me.SZDW_MyTextBox4.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.SZDW_MyTextBox4.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.SZDW_MyTextBox4.CaptainWidth = 60.0!
        Me.SZDW_MyTextBox4.ContentForeColor = System.Drawing.Color.Black
        Me.SZDW_MyTextBox4.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.SZDW_MyTextBox4.EditMask = Nothing
        Me.SZDW_MyTextBox4.Location = New System.Drawing.Point(194, 66)
        Me.SZDW_MyTextBox4.Multiline = False
        Me.SZDW_MyTextBox4.Name = "SZDW_MyTextBox4"
        Me.SZDW_MyTextBox4.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.SZDW_MyTextBox4.ReadOnly = False
        Me.SZDW_MyTextBox4.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.SZDW_MyTextBox4.SelectionStart = 0
        Me.SZDW_MyTextBox4.SelectStart = 0
        Me.SZDW_MyTextBox4.Size = New System.Drawing.Size(162, 20)
        Me.SZDW_MyTextBox4.TabIndex = 4
        Me.SZDW_MyTextBox4.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.SZDW_MyTextBox4.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.SZDW_MyTextBox4.Watermark = Nothing
        '
        'SCCJ_MyTextBox2
        '
        Me.SCCJ_MyTextBox2.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SCCJ_MyTextBox2.Captain = "生产厂家"
        Me.SCCJ_MyTextBox2.CaptainBackColor = System.Drawing.Color.Transparent
        Me.SCCJ_MyTextBox2.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.SCCJ_MyTextBox2.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.SCCJ_MyTextBox2.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.SCCJ_MyTextBox2, 2)
        Me.SCCJ_MyTextBox2.ContentForeColor = System.Drawing.Color.Black
        Me.SCCJ_MyTextBox2.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.SCCJ_MyTextBox2.EditMask = Nothing
        Me.SCCJ_MyTextBox2.Location = New System.Drawing.Point(194, 40)
        Me.SCCJ_MyTextBox2.Multiline = False
        Me.SCCJ_MyTextBox2.Name = "SCCJ_MyTextBox2"
        Me.SCCJ_MyTextBox2.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.SCCJ_MyTextBox2.ReadOnly = False
        Me.SCCJ_MyTextBox2.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.SCCJ_MyTextBox2.SelectionStart = 0
        Me.SCCJ_MyTextBox2.SelectStart = 0
        Me.SCCJ_MyTextBox2.Size = New System.Drawing.Size(323, 20)
        Me.SCCJ_MyTextBox2.TabIndex = 2
        Me.SCCJ_MyTextBox2.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.SCCJ_MyTextBox2.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.SCCJ_MyTextBox2.Watermark = Nothing
        '
        'Label1
        '
        Me.Label1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label1.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.TableLayoutPanel1.SetColumnSpan(Me.Label1, 3)
        Me.Label1.Location = New System.Drawing.Point(11, 92)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(506, 2)
        Me.Label1.TabIndex = 220
        Me.Label1.Text = "Label1"
        '
        'YXQ_MyDateEdit1
        '
        Me.YXQ_MyDateEdit1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.YXQ_MyDateEdit1.Captain = "有效期"
        Me.YXQ_MyDateEdit1.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.YXQ_MyDateEdit1.CaptainWidth = 60.0!
        Me.YXQ_MyDateEdit1.InitialSelection = C1.Win.C1Input.InitialSelectionEnum.CaretAtEnd
        Me.YXQ_MyDateEdit1.Location = New System.Drawing.Point(194, 100)
        Me.YXQ_MyDateEdit1.MaximumSize = New System.Drawing.Size(100000000, 20)
        Me.YXQ_MyDateEdit1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.YXQ_MyDateEdit1.Name = "YXQ_MyDateEdit1"
        Me.YXQ_MyDateEdit1.Size = New System.Drawing.Size(162, 20)
        Me.YXQ_MyDateEdit1.TabIndex = 7
        Me.YXQ_MyDateEdit1.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.YXQ_MyDateEdit1.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.YXQ_MyDateEdit1.VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.DropDown
        '
        'BZSL_MyNumericEdit1
        '
        Me.BZSL_MyNumericEdit1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.BZSL_MyNumericEdit1.Captain = "包装数量"
        Me.BZSL_MyNumericEdit1.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.BZSL_MyNumericEdit1.CaptainWidth = 60.0!
        Me.BZSL_MyNumericEdit1.Location = New System.Drawing.Point(11, 126)
        Me.BZSL_MyNumericEdit1.MaximumSize = New System.Drawing.Size(100000, 80)
        Me.BZSL_MyNumericEdit1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.BZSL_MyNumericEdit1.Name = "BZSL_MyNumericEdit1"
        Me.BZSL_MyNumericEdit1.NumFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.BZSL_MyNumericEdit1.ReadOnly = False
        Me.BZSL_MyNumericEdit1.Size = New System.Drawing.Size(177, 20)
        Me.BZSL_MyNumericEdit1.TabIndex = 8
        Me.BZSL_MyNumericEdit1.ValueIsDbNull = False
        '
        'CGDJ_MyNumericEdit2
        '
        Me.CGDJ_MyNumericEdit2.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.CGDJ_MyNumericEdit2.Captain = "采购单价"
        Me.CGDJ_MyNumericEdit2.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.CGDJ_MyNumericEdit2.CaptainWidth = 60.0!
        Me.CGDJ_MyNumericEdit2.Location = New System.Drawing.Point(194, 126)
        Me.CGDJ_MyNumericEdit2.MaximumSize = New System.Drawing.Size(100000, 80)
        Me.CGDJ_MyNumericEdit2.MinimumSize = New System.Drawing.Size(0, 20)
        Me.CGDJ_MyNumericEdit2.Name = "CGDJ_MyNumericEdit2"
        Me.CGDJ_MyNumericEdit2.NumFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.CGDJ_MyNumericEdit2.ReadOnly = False
        Me.CGDJ_MyNumericEdit2.Size = New System.Drawing.Size(162, 20)
        Me.CGDJ_MyNumericEdit2.TabIndex = 9
        Me.CGDJ_MyNumericEdit2.ValueIsDbNull = False
        '
        'CGJE_MyNumericEdit3
        '
        Me.CGJE_MyNumericEdit3.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.CGJE_MyNumericEdit3.Captain = "采购金额"
        Me.CGJE_MyNumericEdit3.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.CGJE_MyNumericEdit3.CaptainWidth = 60.0!
        Me.CGJE_MyNumericEdit3.Location = New System.Drawing.Point(362, 126)
        Me.CGJE_MyNumericEdit3.MaximumSize = New System.Drawing.Size(100000, 80)
        Me.CGJE_MyNumericEdit3.MinimumSize = New System.Drawing.Size(0, 20)
        Me.CGJE_MyNumericEdit3.Name = "CGJE_MyNumericEdit3"
        Me.CGJE_MyNumericEdit3.NumFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.CGJE_MyNumericEdit3.ReadOnly = False
        Me.CGJE_MyNumericEdit3.Size = New System.Drawing.Size(155, 20)
        Me.CGJE_MyNumericEdit3.TabIndex = 10
        Me.CGJE_MyNumericEdit3.ValueIsDbNull = False
        '
        'Memo
        '
        Me.Memo.Captain = "备    注"
        Me.Memo.CaptainBackColor = System.Drawing.Color.Transparent
        Me.Memo.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Memo.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.Memo.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.Memo, 3)
        Me.Memo.ContentForeColor = System.Drawing.Color.Black
        Me.Memo.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.Memo.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Memo.EditMask = Nothing
        Me.Memo.Location = New System.Drawing.Point(11, 178)
        Me.Memo.Multiline = False
        Me.Memo.Name = "Memo"
        Me.Memo.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.Memo.ReadOnly = False
        Me.Memo.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.Memo.SelectionStart = 0
        Me.Memo.SelectStart = 0
        Me.Memo.Size = New System.Drawing.Size(506, 89)
        Me.Memo.TabIndex = 14
        Me.Memo.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Memo.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.Memo.Watermark = Nothing
        '
        'RKSL_MyNumericEdit1
        '
        Me.RKSL_MyNumericEdit1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.RKSL_MyNumericEdit1.Captain = "入库数量"
        Me.RKSL_MyNumericEdit1.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.RKSL_MyNumericEdit1.CaptainWidth = 60.0!
        Me.RKSL_MyNumericEdit1.Location = New System.Drawing.Point(11, 152)
        Me.RKSL_MyNumericEdit1.MaximumSize = New System.Drawing.Size(100000, 80)
        Me.RKSL_MyNumericEdit1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.RKSL_MyNumericEdit1.Name = "RKSL_MyNumericEdit1"
        Me.RKSL_MyNumericEdit1.NumFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.RKSL_MyNumericEdit1.ReadOnly = False
        Me.RKSL_MyNumericEdit1.Size = New System.Drawing.Size(177, 20)
        Me.RKSL_MyNumericEdit1.TabIndex = 11
        Me.RKSL_MyNumericEdit1.ValueIsDbNull = False
        '
        'RKDJMyNumericEdit1
        '
        Me.RKDJMyNumericEdit1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.RKDJMyNumericEdit1.Captain = "入库单价"
        Me.RKDJMyNumericEdit1.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.RKDJMyNumericEdit1.CaptainWidth = 60.0!
        Me.RKDJMyNumericEdit1.Location = New System.Drawing.Point(194, 152)
        Me.RKDJMyNumericEdit1.MaximumSize = New System.Drawing.Size(100000, 80)
        Me.RKDJMyNumericEdit1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.RKDJMyNumericEdit1.Name = "RKDJMyNumericEdit1"
        Me.RKDJMyNumericEdit1.NumFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.RKDJMyNumericEdit1.ReadOnly = False
        Me.RKDJMyNumericEdit1.Size = New System.Drawing.Size(162, 20)
        Me.RKDJMyNumericEdit1.TabIndex = 12
        Me.RKDJMyNumericEdit1.ValueIsDbNull = False
        '
        'RKJE_MyNumericEdit2
        '
        Me.RKJE_MyNumericEdit2.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.RKJE_MyNumericEdit2.Captain = "入库金额"
        Me.RKJE_MyNumericEdit2.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.RKJE_MyNumericEdit2.CaptainWidth = 60.0!
        Me.RKJE_MyNumericEdit2.Location = New System.Drawing.Point(362, 152)
        Me.RKJE_MyNumericEdit2.MaximumSize = New System.Drawing.Size(100000, 80)
        Me.RKJE_MyNumericEdit2.MinimumSize = New System.Drawing.Size(0, 20)
        Me.RKJE_MyNumericEdit2.Name = "RKJE_MyNumericEdit2"
        Me.RKJE_MyNumericEdit2.NumFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.RKJE_MyNumericEdit2.ReadOnly = False
        Me.RKJE_MyNumericEdit2.Size = New System.Drawing.Size(155, 20)
        Me.RKJE_MyNumericEdit2.TabIndex = 13
        Me.RKJE_MyNumericEdit2.ValueIsDbNull = False
        '
        'CFBL_MyNumericEdit1
        '
        Me.CFBL_MyNumericEdit1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.CFBL_MyNumericEdit1.Captain = "拆分比例"
        Me.CFBL_MyNumericEdit1.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.CFBL_MyNumericEdit1.CaptainWidth = 60.0!
        Me.CFBL_MyNumericEdit1.Location = New System.Drawing.Point(362, 66)
        Me.CFBL_MyNumericEdit1.MaximumSize = New System.Drawing.Size(100000, 80)
        Me.CFBL_MyNumericEdit1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.CFBL_MyNumericEdit1.Name = "CFBL_MyNumericEdit1"
        Me.CFBL_MyNumericEdit1.NumFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.CFBL_MyNumericEdit1.ReadOnly = False
        Me.CFBL_MyNumericEdit1.Size = New System.Drawing.Size(155, 20)
        Me.CFBL_MyNumericEdit1.TabIndex = 5
        Me.CFBL_MyNumericEdit1.ValueIsDbNull = False
        '
        'Ph_MyDtComobo1
        '
        Me.Ph_MyDtComobo1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Ph_MyDtComobo1.Captain = "批    号"
        Me.Ph_MyDtComobo1.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Ph_MyDtComobo1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.Ph_MyDtComobo1.CaptainWidth = 60.0!
        Me.Ph_MyDtComobo1.DataSource = Nothing
        Me.Ph_MyDtComobo1.ItemHeight = 18
        Me.Ph_MyDtComobo1.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Ph_MyDtComobo1.Location = New System.Drawing.Point(11, 100)
        Me.Ph_MyDtComobo1.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.Ph_MyDtComobo1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.Ph_MyDtComobo1.Name = "Ph_MyDtComobo1"
        Me.Ph_MyDtComobo1.ReadOnly = False
        Me.Ph_MyDtComobo1.Size = New System.Drawing.Size(177, 20)
        Me.Ph_MyDtComobo1.TabIndex = 6
        Me.Ph_MyDtComobo1.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'Save_MyButton1
        '
        Me.Save_MyButton1.ButtonImageSize = CustomControl.MyButton.imageSize.large
        Me.Save_MyButton1.DialogResult = System.Windows.Forms.DialogResult.None
        Me.Save_MyButton1.Font = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Save_MyButton1.Location = New System.Drawing.Point(288, 6)
        Me.Save_MyButton1.Name = "Save_MyButton1"
        Me.Save_MyButton1.Size = New System.Drawing.Size(75, 30)
        Me.Save_MyButton1.TabIndex = 0
        Me.Save_MyButton1.Tag = "保存"
        Me.Save_MyButton1.Text = "保存"
        '
        'Cancle_MyButton1
        '
        Me.Cancle_MyButton1.ButtonImageSize = CustomControl.MyButton.imageSize.large
        Me.Cancle_MyButton1.DialogResult = System.Windows.Forms.DialogResult.None
        Me.Cancle_MyButton1.Font = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Cancle_MyButton1.Location = New System.Drawing.Point(377, 6)
        Me.Cancle_MyButton1.Name = "Cancle_MyButton1"
        Me.Cancle_MyButton1.Size = New System.Drawing.Size(75, 30)
        Me.Cancle_MyButton1.TabIndex = 1
        Me.Cancle_MyButton1.Tag = "取消"
        Me.Cancle_MyButton1.Text = "取消"
        '
        'Panel1
        '
        Me.Panel1.Controls.Add(Me.Cancle_MyButton1)
        Me.Panel1.Controls.Add(Me.Save_MyButton1)
        Me.Panel1.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.Panel1.Location = New System.Drawing.Point(0, 278)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(524, 40)
        Me.Panel1.TabIndex = 1
        '
        'Materials_Buy_In2
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(524, 318)
        Me.Controls.Add(Me.TableLayoutPanel1)
        Me.Controls.Add(Me.Panel1)
        Me.Name = "Materials_Buy_In2"
        Me.Text = "物资入库明细"
        Me.TableLayoutPanel1.ResumeLayout(False)
        Me.Panel1.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents TableLayoutPanel1 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents Materials_Comobo As CustomControl.MyDtComobo
    Friend WithEvents Label3 As System.Windows.Forms.Label
    Friend WithEvents GG_MyTextBox1 As CustomControl.MyTextBox
    Friend WithEvents BZDW_MyTextBox3 As CustomControl.MyTextBox
    Friend WithEvents SZDW_MyTextBox4 As CustomControl.MyTextBox
    Friend WithEvents SCCJ_MyTextBox2 As CustomControl.MyTextBox
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents YXQ_MyDateEdit1 As CustomControl.MyDateEdit
    Friend WithEvents BZSL_MyNumericEdit1 As CustomControl.MyNumericEdit
    Friend WithEvents CGDJ_MyNumericEdit2 As CustomControl.MyNumericEdit
    Friend WithEvents CGJE_MyNumericEdit3 As CustomControl.MyNumericEdit
    Friend WithEvents RKSL_MyNumericEdit1 As CustomControl.MyNumericEdit
    Friend WithEvents RKDJMyNumericEdit1 As CustomControl.MyNumericEdit
    Friend WithEvents RKJE_MyNumericEdit2 As CustomControl.MyNumericEdit
    Friend WithEvents CFBL_MyNumericEdit1 As CustomControl.MyNumericEdit
    Friend WithEvents Ph_MyDtComobo1 As CustomControl.MyDtComobo
    Friend WithEvents Memo As CustomControl.MyTextBox
    Friend WithEvents Save_MyButton1 As CustomControl.MyButton
    Friend WithEvents Cancle_MyButton1 As CustomControl.MyButton
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
End Class
