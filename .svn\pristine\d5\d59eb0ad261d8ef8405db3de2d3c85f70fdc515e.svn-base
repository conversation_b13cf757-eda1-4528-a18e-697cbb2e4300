﻿Imports System.Data.SqlClient
Imports Stimulsoft.Report

Public Class YkYf_Bsby

#Region "定义__变量"
    Dim My_View As New DataView                         '数据视图
    Dim My_Cc As New BaseClass.C_Cc()                             '编码
    Dim My_Adapter As New SqlDataAdapter                '从表适配器
    Dim My_Table As New DataTable                       '从表
    Dim My_Cm As CurrencyManager                     '同步指针
    Public Cb_Cm As CurrencyManager                     '同步指针
    Public Cb_Row As DataRow                            '当前选择行
    Dim V_Str As String
    Dim Str_Select As String
    Dim My_Dataset As New DataSet

    Dim YfCode As String
    Dim YfName As String
    Dim FormLb As String
#End Region

  

    Public Sub New(ByVal m_YfCode As String, ByVal m_YfName As String, ByVal m_FormLb As String)

        ' 此调用是设计器所必需的。
        InitializeComponent()
        YfCode = m_YfCode
        YfName = m_YfName
        FormLb = m_FormLb
        ' 在 InitializeComponent() 调用之后添加任何初始化。

    End Sub

    Private Sub YkYf_Bsby_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        BaseFunc.BaseFunc.Init_Datetimepicker(DateTimePicker1)
        BaseFunc.BaseFunc.Init_Datetimepicker(DateTimePicker2)

        DateTimePicker1.Value = Format(Now, "yyyy-MM-dd 00:00:00")
        DateTimePicker2.Value = Format(Now, "yyyy-MM-dd 23:59:59")
        Call Form_Init()                '窗体初始化
        Call Show_Data()
        Call Data_Clear()
        Call F_Sum()
    End Sub

#Region "窗体__事件"

    Private Sub Form_Init()

        Dim My_Grid As New BaseClass.C_Grid(Me.C1TrueDBGrid1)
        With My_Grid
            .Init_Grid()
            .AllSort(False)
            .P_MultiSelect(BaseClass.C_Grid.MultiSelect.Extended)
            .P_Dock(BaseClass.C_Grid.Dock.Fill)
            .Init_Column("报损报益编码", "BsBy_Code", 0, "左", "")
            .Init_Column("药品编码", "Xx_Code", 0, "左", "")
            .Init_Column("药品名称", "Yp_Name", 150, "左", "")
            .Init_Column("药品产地", "Mx_Cd", 90, "左", "")
            .Init_Column("药品规格", "Mx_Gg", 100, "左", "")
            .Init_Column("有 效 期", "Yp_Yxq", 75, "中", "yyyy-MM-dd")
            .Init_Column("损益数量", "BsBy_Sl", 60, "右", "##0.0###")
            .Init_Column("采购价", "Yp_Cgj", 50, "右", "##0.00##")
            .Init_Column("损益采购金额", "Sy_CgMoney", 90, "右", "####0.00##")
            .Init_Column("销售价", "Yp_Xsj", 50, "右", "##0.00##")
            .Init_Column("损益销售金额", "Sy_XsMoney", 90, "右", "####0.00##")
            .Init_Column("报损日期", "BsBy_Date", 75, "中", "yyyy-MM-dd")
            .Init_Column("报损时间", "BsBy_Time", 60, "中", "HH:mm:ss")
            .Init_Column("备   注", "BsBy_Memo", 90, "左", "")
            .Init_Column("经手人", "Jsr_Name", 70, "中", "")
        End With
        C1TextBox1.Select()
        C1TrueDBGrid1.HScrollBar.Style = C1.Win.C1TrueDBGrid.ScrollBarStyleEnum.Always
        C1TrueDBGrid1.RecordSelectors = False




        '药品字典
        If FormLb = "药库报损报溢" Then
            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "Select  Yp_Jc,Yp_Name,Jx_Name,Mx_Gyzz,Mx_Gg,Mx_Cd,Mx_Code,Mx_Cgdw as Yp_Dw,Dl_Name,Yp_Code,Yk_Sl as Yp_Sl,Yp_Ph,Yp_Yxq,Xx_Code,Yk_Cgj as Yp_Cgj,Yk_Xsj as Yp_Xsj  From V_YpKc  Order by Yp_Jc", "药品字典", True)
        ElseIf FormLb = "药房报损报溢" Then
            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "Select  Yp_Jc,Yp_Name,Jx_Name,Mx_Gyzz,Mx_Gg,Mx_Cd,Mx_Code,Mx_XsDw as Yp_Dw,Dl_Name,Yp_Code,Yf_Sl" & CDbl(Mid(YfCode, 5, 2)) & " as Yp_Sl,Yp_Ph,Yp_Yxq,Xx_Code,Yk_Cgj/Mx_Cfbl as Yp_Cgj,Yk_Xsj/Mx_Cfbl as Yp_Xsj  From V_YpKc  Order by Yp_Jc", "药品字典", True)
        End If

        My_Dataset.Tables("药品字典").PrimaryKey = New DataColumn() {My_Dataset.Tables("药品字典").Columns("Xx_Code")}
        My_Dataset.Tables("药品字典").DefaultView.RowFilter = "Yp_Sl>0"

        Dim My_Combo As BaseClass.C_Combo2 = New BaseClass.C_Combo2(C1Combo1, My_Dataset.Tables("药品字典").DefaultView, "Yp_Name", "Xx_Code", 750)

        With My_Combo
            .Init_TDBCombo()

            .Init_Colum("Yp_Jc", "简称", 0, "左")
            .Init_Colum("Yp_Name", "名称", 140, "左")
            .Init_Colum("Jx_Name", "剂型", 0, "左")
            .Init_Colum("Mx_Gyzz", "批准文号", 65, "左")
            .Init_Colum("Mx_Gg", "规格", 65, "左")
            .Init_Colum("Mx_Cd", "产地", 85, "左")
            .Init_Colum("Mx_Code", "", 0, "中")
            .Init_Colum("Yp_Dw", "单位", 50, "中")
            .Init_Colum("Dl_Name", "", 0, "中")
            .Init_Colum("Yp_Code", "", 0, "中")
            .Init_Colum("Yp_Sl", "库存", 47, "右")
            .Init_Colum("Yp_Ph", "批号", 65, "左")
            .Init_Colum("Yp_Yxq", "有效期", 75, "中")
            .Init_Colum("Xx_Code", "", 0, "中")
            .Init_Colum("Yp_Cgj", "采购价", 60, "右")
            .Init_Colum("Yp_Xsj", "销售价", 60, "右")
            .MaxDropDownItems(15)
            .SelectedIndex(-1)
        End With

        With C1Combo1
            .Columns("Yp_Sl").NumberFormat = "0.###"
            .Columns("Yp_Yxq").NumberFormat = "yyyy-MM-dd"
            .Columns("Yp_Cgj").NumberFormat = "0.0###"
            .Columns("Yp_Xsj").NumberFormat = "0.0###"
        End With

        C1Combo1.AutoCompletion = False
        C1Combo1.AutoSelect = False


        Dim My_Combo1 As New BaseClass.C_Combo1(Me.C1Combo2)
        My_Combo1.Init_TDBCombo()
        With C1Combo2
            .AddItem("药品报损")
            .AddItem("药品报益")
            .SelectedIndex = 0
            .DropDownWidth = 140
            .Width = 140
        End With

    End Sub

#End Region

#Region "数据_编辑"

    Private Sub Data_Add()
        If My_Dataset.Tables("报损报益") IsNot Nothing Then My_Dataset.Tables("报损报益").Clear()

        Dim My_NewRow As DataRow = Me.My_Table.NewRow
        If C1Combo1.Text = "" Then
            MsgBox("必须选择报损报益药品!", MsgBoxStyle.Critical, "提示")
            C1Combo1.Focus()
            Exit Sub
        End If
        If C1NumericEdit1.Text = 0 Then
            MsgBox("必须输入损报益数量!", MsgBoxStyle.Critical, "提示")
            C1NumericEdit1.Focus()
            Exit Sub
        End If
        With My_NewRow
            .Item("BsBy_Code") = F_MaxCode()
            .Item("Xx_Code") = Trim(C1Combo1.SelectedValue & "")
            If C1Combo2.Text = "药品报损" Then
                .Item("BsBy_Sl") = -Trim(C1NumericEdit1.Text & "")
            Else
                .Item("BsBy_Sl") = Trim(C1NumericEdit1.Text & "")
            End If
            .Item("BsBy_Date") = Format(Now, "yyyy-MM-dd")
            .Item("BsBy_Time") = Format(Now, "HH:mm:ss")
            .Item("BsBy_Memo") = Trim(C1TextBox12.Text & "")
            .Item("Yy_Code") = HisVar.HisVar.WsyCode
            .Item("Jsr_Code") = HisVar.HisVar.JsrCode
            .Item("Yp_Yxq") = C1TextBox21.Text
            .Item("Yp_Name") = C1Combo1.Text
            .Item("Mx_Gg") = C1TextBox18.Text
            .Item("Mx_Cd") = C1TextBox20.Text
            .Item("Jsr_Name") = HisVar.HisVar.JsrName
            If FormLb = "药房报损报溢" Then
                .Item("Yf_Code") = YfCode
            End If
        End With

        '数据保存
        With Me
            Try

                Dim para() As SqlParameter = Nothing
                Dim ilist As List(Of SqlParameter) = New List(Of SqlParameter)()
                ilist.Add(New SqlParameter("@Yy_Code", SqlDbType.Char))
                ilist.Add(New SqlParameter("@BsBy_Code", SqlDbType.VarChar))
                ilist.Add(New SqlParameter("@Xx_Code", SqlDbType.VarChar))
                ilist.Add(New SqlParameter("@BsBy_Sl", SqlDbType.VarChar))
                ilist.Add(New SqlParameter("@BsBy_Date", SqlDbType.VarChar))
                ilist.Add(New SqlParameter("@BsBy_Time", SqlDbType.VarChar))
                ilist.Add(New SqlParameter("@BsBy_Memo", SqlDbType.VarChar))
                ilist.Add(New SqlParameter("@Jsr_Code", SqlDbType.VarChar))

                If FormLb = "药房报损报溢" Then
                    ilist.Add(New SqlParameter("@Yf_Code", SqlDbType.VarChar))
                End If

                para = ilist.ToArray()
                For I = 0 To para.Length - 1
                    para(I).Value = My_NewRow.Item(Mid(para(I).ParameterName, 2))
                Next
                If FormLb = "药库报损报溢" Then
                    HisVar.HisVar.Sqldal.ExecuteSql("Insert Into Yk_Bsby(Yy_Code,BsBy_Code,Xx_Code,BsBy_Sl,BsBy_Date,BsBy_Time,BsBy_Memo,Jsr_Code)Values(@Yy_Code,@BsBy_Code,@Xx_Code,@BsBy_Sl,@BsBy_Date,@BsBy_Time,@BsBy_Memo,@Jsr_Code)", para)
                    If C1Combo2.Text = "药品报损" Then
                        HisVar.HisVar.Sqldal.ExecuteSql("Update Zd_Ml_Yp4 Set Yk_Sl=isnull(Yk_Sl,0)-(" & Trim(C1NumericEdit1.Text) & ") Where Xx_Code='" & Trim(C1Combo1.SelectedValue) & "' And Yy_Code='" & HisVar.HisVar.WsyCode & "'")
                    Else
                        HisVar.HisVar.Sqldal.ExecuteSql("Update Zd_Ml_Yp4 Set Yk_Sl=isnull(Yk_Sl,0)+(" & Trim(C1NumericEdit1.Text) & ") Where Xx_Code='" & Trim(C1Combo1.SelectedValue) & "' And Yy_Code='" & HisVar.HisVar.WsyCode & "'")

                    End If
                ElseIf FormLb = "药房报损报溢" Then
                    HisVar.HisVar.Sqldal.ExecuteSql("Insert Into Yf_BsBy(Yy_Code,BsBy_Code,Xx_Code,BsBy_Sl,BsBy_Date,BsBy_Time,BsBy_Memo,Jsr_Code,Yf_Code)Values(@Yy_Code,@BsBy_Code,@Xx_Code,@BsBy_Sl,@BsBy_Date,@BsBy_Time,@BsBy_Memo,@Jsr_Code,@Yf_Code)", para)
                    If C1Combo2.Text = "药品报损" Then
                        HisVar.HisVar.Sqldal.ExecuteSql("Update Zd_Ml_Yp4 Set " & "Yf_Sl" & CDbl(Mid(YfCode, 5, 2)) & "=isnull(" & "Yf_Sl" & CDbl(Mid(YfCode, 5, 2)) & ",0)-(" & Trim(C1NumericEdit1.Text) & ") Where Xx_Code='" & Trim(C1Combo1.SelectedValue) & "' ")
                    Else
                        HisVar.HisVar.Sqldal.ExecuteSql("Update Zd_Ml_Yp4 Set " & "Yf_Sl" & CDbl(Mid(YfCode, 5, 2)) & "=isnull(" & "Yf_Sl" & CDbl(Mid(YfCode, 5, 2)) & ",0)+(" & Trim(C1NumericEdit1.Text) & ") Where Xx_Code='" & Trim(C1Combo1.SelectedValue) & "' ")

                    End If
                End If

                .My_Table.Rows.Add(My_NewRow)

                '数量改变
                Dim Yf_Row As DataRow
                Yf_Row = Me.My_Dataset.Tables("药品字典").Rows.Find(C1Combo1.SelectedValue)
                With Yf_Row
                    .BeginEdit()
                    If C1Combo2.Text = "药品报损" Then
                        .Item("Yp_Sl") = .Item("Yp_Sl") - Trim(C1NumericEdit1.Text)
                    Else
                        .Item("Yp_Sl") = .Item("Yp_Sl") + Trim(C1NumericEdit1.Text)
                    End If

                    .EndEdit()
                End With
                Yf_Row.AcceptChanges()
                Me.My_Dataset.Tables("药品字典").AcceptChanges()

                .C1TrueDBGrid1.MoveLast()

            Catch ex As Exception
                Beep()
                MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
                Exit Sub
            Finally
                Me.C1TextBox2.Select()
            End Try
        End With

        '数据清空
        Call Show_Data()
        Call Data_Clear()
    End Sub

    Private Sub Show_Data()
        '主表数据
        Dim V_Select As String = ""
        If FormLb = "药库报损报溢" Then
            V_Select = "Select Yk_Bsby.Yy_Code,BsBy_Code,Yk_Bsby.Xx_Code,BsBy_Sl,BsBy_Date,BsBy_Time,BsBy_Memo,Yk_Bsby.Jsr_Code,Yp_Name,Mx_Cd,Mx_Gg,Yp_Yxq,Jsr_Name,V_YpKc.Yk_Cgj as Yp_Cgj,V_YpKc.Yk_Xsj as Yp_Xsj,Yk_Bsby.BsBy_Sl*V_YpKc.Yk_Cgj as Sy_CgMoney,V_YpKc.Yk_Xsj*Yk_Bsby.BsBy_Sl as Sy_XsMoney From Yk_Bsby,V_YpKc,Zd_YyJsr where Zd_YyJsr.Jsr_Code=Yk_Bsby.Jsr_Code and V_YpKc.Xx_Code=Yk_Bsby.Xx_Code  and  Yk_Bsby.BsBy_Date+' '+Yk_BsBy.BsBy_Time>='" & Format(DateTimePicker1.Value, "yyyy-MM-dd HH:mm:ss") & "' and Yk_Bsby.BsBy_Date+' '+Yk_BsBy.BsBy_Time<='" & Format(DateTimePicker2.Value, "yyyy-MM-dd HH:mm:ss") & "' Order By BsBy_Code"
        ElseIf FormLb = "药房报损报溢" Then
            V_Select = "Select Yf_BsBy.Yy_Code,BsBy_Code,Yf_BsBy.Xx_Code,BsBy_Sl,BsBy_Date,BsBy_Time,BsBy_Memo,Yf_BsBy.Jsr_Code,Yf_BsBy.Yf_Code,Yp_Name,Mx_Cd,Mx_Gg,Yp_Yxq,Jsr_Name,V_YpKc.Yk_Cgj/Mx_Cfbl as Yp_Cgj,V_YpKc.Yf_Lsj1 as Yp_Xsj,V_YpKc.Mx_Cfbl,Yf_Bsby.BsBy_Sl*V_YpKc.Yf_Lsj1 as Sy_XsMoney,V_YpKc.Yk_Cgj/V_YpKc.Mx_Cfbl as Yf_Cgj,(V_YpKc.Yk_Cgj/V_YpKc.Mx_Cfbl)*Yf_BsBy.BsBy_Sl as Sy_CgMoney From Yf_BsBy,V_YpKc,Zd_YyJsr where Yf_BsBy.Yf_Code=Zd_YyJsr.Yf_Code and Yf_BsBy.Jsr_Code=Zd_YyJsr.Jsr_Code and  Yf_BsBy.Yf_Code='" & HisVar.HisVar.YfCode & "' and Zd_YyJsr.Jsr_Code=Yf_BsBy.Jsr_Code and V_YpKc.Xx_Code=Yf_BsBy.Xx_Code  and  Yf_BsBy.BsBy_Date+' '+Yf_BsBy.BsBy_Time>='" & Format(DateTimePicker1.Value, "yyyy-MM-dd HH:mm:ss") & "' and Yf_BsBy.BsBy_Date+' '+Yf_BsBy.BsBy_Time<='" & Format(DateTimePicker2.Value, "yyyy-MM-dd HH:mm:ss") & "' Order By BsBy_Code"
        End If

      
        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, V_Select, "报损报益", True)
        My_Table = My_Dataset.Tables("报损报益")
        My_Table.PrimaryKey = New DataColumn() {My_Table.Columns("BsBy_Code")}
        'TDBGrid初始化
        C1TrueDBGrid1.SetDataBinding(My_Dataset, "报损报益", True)
        My_Cm = CType(BindingContext(C1TrueDBGrid1.DataSource, C1TrueDBGrid1.DataMember), CurrencyManager)
        My_View = My_Cm.List
        T_Textbox.Text = ""
    End Sub

    '清空
    Private Sub Data_Clear()
        C1Combo1.Enabled = True
        C1Combo1.SelectedValue = -1
        C1Combo1.Text = ""                                      '药品明细编码
        C1Combo1.Select()

        C1TextBox20.Text = ""
        C1TextBox23.Text = ""
        C1TextBox18.Text = ""
        C1NumericEdit1.Text = 0
        C1TextBox22.Text = ""
        C1TextBox21.Text = ""
        C1TextBox12.Text = ""
    End Sub
#End Region

#Region "控件事件"

    '增加按钮事件
    Private Sub Comm1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.Click
        If C1Combo1.Text = "" Then
            MsgBox("请选择药品!", MsgBoxStyle.Critical, "提示")
            Exit Sub
        End If
        Dim Pd As Object = Nothing
        If FormLb = "药库报损报溢" Then
            Pd = HisVar.HisVar.Sqldal.GetSingle("select distinct Pd_Wc from Zd_YkPd where Pd_Wc='否'")
        ElseIf FormLb = "药房报损报溢" Then
            Pd = HisVar.HisVar.Sqldal.GetSingle("select distinct Pd_Wc from Zd_YfPd where Pd_Wc='否' and Yf_Code='" & YfCode & "'")
        End If

        If Pd Is Nothing Then
        Else
            MsgBox("正在进行盘点，请等待盘点完成后在进行报损报益！", MsgBoxStyle.Information, "提示：")
            Exit Sub
        End If


        Dim Sl As Double = 0
        If C1Combo2.Text = "药品报损" Then
            Sl = Double.Parse(C1TextBox22.Text) - Double.Parse(Trim(C1NumericEdit1.Text))
        Else
            Sl = Double.Parse(C1TextBox22.Text) + Double.Parse(Trim(C1NumericEdit1.Text))

        End If
        If Sl >= 0 And Double.Parse(Trim(C1NumericEdit1.Text)) > 0 Then
            Call Data_Add()
        Else
            MsgBox("必须正确输入药品报损报益数量!", MsgBoxStyle.Critical, "提示")
            C1NumericEdit1.Focus()
            Exit Sub
        End If

        Call F_Sum()
    End Sub

    '查询按钮
    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click
        Call Show_Data()
        Call F_Sum()
    End Sub

    Private Sub C1Combo1_RowChange(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Combo1.RowChange
        If C1Combo1.WillChangeToValue = "" Then

        Else
            C1TextBox18.Text = Trim(C1Combo1.Columns("Mx_Gg").Value & "")
            C1TextBox20.Text = Trim(C1Combo1.Columns("Mx_Cd").Value & "")
            C1TextBox21.Text = Trim(C1Combo1.Columns("Yp_Yxq").Value)
            C1TextBox22.Text = Format(C1Combo1.Columns("Yp_Sl").Value, "0.#####")
            C1TextBox23.Text = Trim(C1Combo1.Columns("Yp_Dw").Value & "")
            C1Combo1.SelectedValue = C1Combo1.Columns("Xx_Code").Value

        End If

    End Sub

    Private Sub C1Combo1_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles C1Combo1.KeyUp

        If (e.KeyValue > 47 And e.KeyValue < 58) Or (e.KeyValue > 96 And e.KeyValue < 123) Or (e.KeyValue > 64 And e.KeyValue < 91) Or (e.KeyValue = 8) Or (e.KeyValue = 189) Or (e.KeyValue = 190) Then
            Dim s As String = C1Combo1.Text
            If C1Combo1.Text = "" Then
                If CheckBox1.Checked = True Then
                    C1Combo1.DataSource.RowFilter = "1=1"
                Else
                    C1Combo1.DataSource.RowFilter = "Yp_Sl>0"
                End If

            Else
                If CheckBox1.Checked = True Then
                    C1Combo1.DataSource.RowFilter = "1=1 and Yp_Jc like '*" & C1Combo1.Text.Replace("*", "[*]") & "*'"
                Else
                    C1Combo1.DataSource.RowFilter = "Yp_Sl>0 and Yp_Jc like '*" & C1Combo1.Text.Replace("*", "[*]") & "*'"
                End If

            End If
            If (e.KeyValue = 8) Then
                C1Combo1.DroppedDown = False
                C1Combo1.DroppedDown = True
            End If

            C1Combo1.Text = s
            C1Combo1.SelectionStart = C1Combo1.Text.Length
            C1Combo1.SelectionLength = 0
        End If

    End Sub

    Private Sub C1Combo1_KeyDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles C1Combo1.KeyDown
        If e.KeyValue = 13 Then
            If C1Combo1.WillChangeToIndex < 1 Then
                If (CType(C1Combo1.DataSource, DataView).Count) = 0 Then
                    MsgBox("药品: '" + Me.C1Combo1.Text + "' 不存在！", MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
                    System.Windows.Forms.SendKeys.Send("^{A}")
                    Exit Sub
                End If
                C1Combo1.SelectedIndex = -1
                C1Combo1.SelectedIndex = 0
            Else
                Dim index As Integer
                index = C1Combo1.WillChangeToIndex
                C1Combo1.SelectedIndex = -1
                C1Combo1.SelectedIndex = index
            End If
            e.Handled = True
            System.Windows.Forms.SendKeys.Send("{Tab}")
        End If
    End Sub

#End Region

#Region "自定义函数"

    Private Function F_MaxCode()   '报损报益编码

        If FormLb = "药库报损报溢" Then
            My_Cc.Get_MaxCode("Yk_Bsby", "BsBy_Code", 14, "Yy_Code", HisVar.HisVar.WsyCode)
        ElseIf FormLb = "药房报损报溢" Then
            My_Cc.Get_MaxCode("Yf_BsBy", "BsBy_Code", 14, "Yy_Code", HisVar.HisVar.WsyCode)
        End If

        F_MaxCode = My_Cc.编码
        Return F_MaxCode
    End Function

#End Region

    Private Sub CheckBox1_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox1.CheckedChanged


        If CheckBox1.Checked = True Then
            My_Dataset.Tables("药品字典").DefaultView.RowFilter = ""
        Else
            My_Dataset.Tables("药品字典").DefaultView.RowFilter = "Yp_Sl>0"
        End If

        C1Combo1.SelectedIndex = -1

    End Sub

    Public Overrides Sub F_Sum()
        Dim Sum1 As Double = 0
        Dim Sum2 As Double = 0
        Dim Sum3 As Double = 0
        Sum1 = IIf(My_View.Table.Compute("Sum(BsBy_Sl)", My_View.RowFilter) Is DBNull.Value, 0, My_View.Table.Compute("Sum(BsBy_Sl)", My_View.RowFilter))
        Sum2 = IIf(My_View.Table.Compute("Sum(Sy_CgMoney)", My_View.RowFilter) Is DBNull.Value, 0, My_View.Table.Compute("Sum(Sy_CgMoney)", My_View.RowFilter))
        Sum3 = IIf(My_View.Table.Compute("Sum(Sy_XsMoney)", My_View.RowFilter) Is DBNull.Value, 0, My_View.Table.Compute("Sum(Sy_XsMoney)", My_View.RowFilter))
        With C1TrueDBGrid1
            .ColumnFooters = True
            .Columns("BsBy_Sl").FooterText = Format(Sum1, "###0.#####")
            .Columns("Sy_CgMoney").FooterText = Format(Sum2, "#####0.00##")
            .Columns("Sy_XsMoney").FooterText = Format(Sum3, "#####0.00##")
        End With

    End Sub

    Private Sub Button2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button2.Click
        If C1TrueDBGrid1.RowCount = 0 Then Exit Sub

        Dim Stirpt As New StiReport

        Stirpt.Load(".\rpt\药库药房报损报益表.mrt")
        Stirpt.ReportName = Me.Text & "表"
        Stirpt.RegData(My_Dataset.Tables("报损报益"))
        Stirpt.Compile()
        Stirpt("标题") = Stirpt.ReportName
        Stirpt("查询时间") = "查询时间:" & Format(DateTimePicker1.Value, "yyyy-MM-dd HH:mm:ss") & " 至 " & Format(DateTimePicker2.Value, "yyyy-MM-dd HH:mm:ss")
        Stirpt("打印时间") = "打印时间:" & Format(Now, "yyyy-MM-dd HH:mm:ss")

        ' Stirpt.Design()
        Stirpt.Show()
    End Sub


    Private Sub C1Combo2_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles C1Combo2.KeyPress
        If e.KeyChar <> Chr(Keys.Return) Then Exit Sub
        e.Handled = True
        System.Windows.Forms.SendKeys.Send("{Tab}")
    End Sub

    Private Sub C1NumericEdit1_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles C1NumericEdit1.KeyPress
        If e.KeyChar <> Chr(Keys.Return) Then Exit Sub
        Comm1.Select()
    End Sub
End Class