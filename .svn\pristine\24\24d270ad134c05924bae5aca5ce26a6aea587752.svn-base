﻿/**  版本信息模板在安装目录下，可自行修改。
* B_Emr_ZkDj.cs
*
* 功 能： N/A
* 类 名： B_Emr_ZkDj
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/8/10 星期三 下午 3:41:49   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Collections.Generic;
//
using ModelOld;
namespace BLLOld
{
	/// <summary>
	/// B_Emr_ZkDj
	/// </summary>
	public partial class B_Emr_ZkDj
	{
		private readonly DAL.D_Emr_ZkDj dal=new DAL.D_Emr_ZkDj();
       // private readonly BaseClass.C_Cc c = new BaseClass.C_Cc();
		public B_Emr_ZkDj()
		{}
		#region  BasicMethod
		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string ZkDj_Code)
		{
			return dal.Exists(ZkDj_Code);
		}

		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ModelOld.M_Emr_ZkDj model)
		{
			return dal.Add(model);
		}

		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ModelOld.M_Emr_ZkDj model)
		{
			return dal.Update(model);
		}

        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update1(ModelOld.M_Emr_ZkDj model)
        {
            return dal.Update1(model);
        }

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string ZkDj_Code)
		{
			
			return dal.Delete(ZkDj_Code);
		}
		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool DeleteList(string ZkDj_Codelist )
		{
			return dal.DeleteList(ZkDj_Codelist );
		}
        public string GetDeductedScore(string grade)
        {
            return dal.GetDeductedScore(grade);
        }
		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Emr_ZkDj GetModel(string ZkDj_Code)
		{
			
			return dal.GetModel(ZkDj_Code);
		}

		/// <summary>
		/// 得到一个对象实体，从缓存中
		/// </summary>
        //public ModelOld.M_Emr_ZkDj GetModelByCache(string ZkDj_Code)
        //{
			
        //    string CacheKey = "M_Emr_ZkDjModel-" + ZkDj_Code;
        //    object objModel = Common.DataCache.GetCache(CacheKey);
        //    if (objModel == null)
        //    {
        //        try
        //        {
        //            objModel = dal.GetModel(ZkDj_Code);
        //            if (objModel != null)
        //            {
        //                int ModelCache = Common.ConfigHelper.GetConfigInt("ModelCache");
        //                Common.DataCache.SetCache(CacheKey, objModel, DateTime.Now.AddMinutes(ModelCache), TimeSpan.Zero);
        //            }
        //        }
        //        catch{}
        //    }
        //    return (ModelOld.M_Emr_ZkDj)objModel;
        //}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			return dal.GetList(strWhere);
		}
		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			return dal.GetList(Top,strWhere,filedOrder);
		}
		/// <summary>
		/// 获得数据列表
		/// </summary>
		public List<ModelOld.M_Emr_ZkDj> GetModelList(string strWhere)
		{
			DataSet ds = dal.GetList(strWhere);
			return DataTableToList(ds.Tables[0]);
		}
		/// <summary>
		/// 获得数据列表
		/// </summary>
		public List<ModelOld.M_Emr_ZkDj> DataTableToList(DataTable dt)
		{
			List<ModelOld.M_Emr_ZkDj> modelList = new List<ModelOld.M_Emr_ZkDj>();
			int rowsCount = dt.Rows.Count;
			if (rowsCount > 0)
			{
				ModelOld.M_Emr_ZkDj model;
				for (int n = 0; n < rowsCount; n++)
				{
					model = dal.DataRowToModel(dt.Rows[n]);
					if (model != null)
					{
						modelList.Add(model);
					}
				}
			}
			return modelList;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetAllList()
		{
			return GetList("");
		}

        /// <summary>
        /// 获得最大值
        /// </summary>
        //public void  MaxCode()
        //{
        //    //return ("");
        //    //string cc=(string)c.Get_MaxCode("Eme_ZkDj", "ZkDj_Code", 2, "", "");
        //    //return 
        //        c.Get_MaxCode("Eme_ZkDj", "ZkDj_Code", 2, "", "");
        //        //L_Dl_Code.Text = c.编码;
        //}

		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			return dal.GetRecordCount(strWhere);
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			return dal.GetListByPage( strWhere,  orderby,  startIndex,  endIndex);
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		//public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		//{
			//return dal.GetList(PageSize,PageIndex,strWhere);
		//}

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

