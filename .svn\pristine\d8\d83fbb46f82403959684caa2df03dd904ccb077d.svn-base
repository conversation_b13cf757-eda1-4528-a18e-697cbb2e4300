﻿'------------------------------------------------------------------------------
' <auto-generated>
'     此代码由工具生成。
'     运行时版本:4.0.30319.42000
'
'     对此文件的更改可能会导致不正确的行为，并且如果
'     重新生成代码，这些更改将会丢失。
' </auto-generated>
'------------------------------------------------------------------------------

Option Strict On
Option Explicit On

Imports System

Namespace My.Resources
    
    '此类是由 StronglyTypedResourceBuilder
    '类通过类似于 ResGen 或 Visual Studio 的工具自动生成的。
    '若要添加或移除成员，请编辑 .ResX 文件，然后重新运行 ResGen
    '(以 /str 作为命令选项)，或重新生成 VS 项目。
    '''<summary>
    '''  一个强类型的资源类，用于查找本地化的字符串等。
    '''</summary>
    <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "4.0.0.0"),  _
     Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.Runtime.CompilerServices.CompilerGeneratedAttribute(),  _
     Global.Microsoft.VisualBasic.HideModuleNameAttribute()>  _
    Friend Module Resources
        
        Private resourceMan As Global.System.Resources.ResourceManager
        
        Private resourceCulture As Global.System.Globalization.CultureInfo
        
        '''<summary>
        '''  返回此类使用的缓存的 ResourceManager 实例。
        '''</summary>
        <Global.System.ComponentModel.EditorBrowsableAttribute(Global.System.ComponentModel.EditorBrowsableState.Advanced)>  _
        Friend ReadOnly Property ResourceManager() As Global.System.Resources.ResourceManager
            Get
                If Object.ReferenceEquals(resourceMan, Nothing) Then
                    Dim temp As Global.System.Resources.ResourceManager = New Global.System.Resources.ResourceManager("ZtHis.Materials.Resources", GetType(Resources).Assembly)
                    resourceMan = temp
                End If
                Return resourceMan
            End Get
        End Property
        
        '''<summary>
        '''  使用此强类型资源类，为所有资源查找
        '''  重写当前线程的 CurrentUICulture 属性。
        '''</summary>
        <Global.System.ComponentModel.EditorBrowsableAttribute(Global.System.ComponentModel.EditorBrowsableState.Advanced)>  _
        Friend Property Culture() As Global.System.Globalization.CultureInfo
            Get
                Return resourceCulture
            End Get
            Set
                resourceCulture = value
            End Set
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property layout_edit() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("layout_edit", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property layout_sidebar() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("layout_sidebar", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property miBaComplete_Glyph() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("miBaComplete_Glyph", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property miBaComplete_Glyph16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("miBaComplete_Glyph16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property miBarAdd_Glyph() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("miBarAdd_Glyph", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property miBarAdd_Glyph16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("miBarAdd_Glyph16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property miBarCancel_Glyph() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("miBarCancel_Glyph", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property miBarCancel_Glyph16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("miBarCancel_Glyph16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property miBarClose_Glyph() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("miBarClose_Glyph", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property miBarDelete_Glyph() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("miBarDelete_Glyph", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property miBarDelete_Glyph16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("miBarDelete_Glyph16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property miBarDeleteAll_Glyph() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("miBarDeleteAll_Glyph", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property miBarDeleteAll_Glyph16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("miBarDeleteAll_Glyph16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property miBarEdit_Glyph() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("miBarEdit_Glyph", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property miBarEdit_Glyph16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("miBarEdit_Glyph16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property miBarExit_Glyph() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("miBarExit_Glyph", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property miBarExport_Glyph() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("miBarExport_Glyph", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property miBarFd_Glyph() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("miBarFd_Glyph", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property miBarFd_Glyph16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("miBarFd_Glyph16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property miBarModify_Glyph() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("miBarModify_Glyph", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property miBarModify_Glyph16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("miBarModify_Glyph16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property miBarParamPrint_Glyph() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("miBarParamPrint_Glyph", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property miBarParamPrint_Glyph16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("miBarParamPrint_Glyph16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property miBarRefresh_Glyph() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("miBarRefresh_Glyph", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property miBarRefresh_Glyph16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("miBarRefresh_Glyph16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property miBarSave_Glyph() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("miBarSave_Glyph", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property miBarSave_Glyph16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("miBarSave_Glyph16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property miBarSearch_Glyph() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("miBarSearch_Glyph", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property miBarSearch_Glyph16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("miBarSearch_Glyph16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property miBarSubmit_Glyph() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("miBarSubmit_Glyph", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property miBarSubmit_Glyph16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("miBarSubmit_Glyph16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property WIZMENU() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("WIZMENU", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property 停用16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("停用16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property 冲销16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("冲销16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property 启用16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("启用16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property 完成16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("完成16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property 导入() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("导入", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property 导出() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("导出", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property 录入16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("录入16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property 被冲销16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("被冲销16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
    End Module
End Namespace
