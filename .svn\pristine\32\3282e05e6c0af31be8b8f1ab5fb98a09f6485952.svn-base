﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="nv_directory" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\nv_directory.ico;System.Drawing.Icon, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="nv_user" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\nv_user.ico;System.Drawing.Icon, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="bgheadtools" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\bgheadtools.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="bodyspin" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\bodyspin.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="bold" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\bold.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="bottombg" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\bottombg.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="btn1" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\btn1.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="btn2" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\btn2.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="btn3" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\btn3.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="bullets" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\bullets.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="bullets1" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\bullets1.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="button_cancel" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\button_cancel.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="button_ok" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\button_ok.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="chongwubg" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\chongwubg.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="close1" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\close1.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="close2" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\close2.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="close3" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\close3.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="close4" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\close4.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="code" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\code.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="colorpen" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\colorpen.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="copy" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\copy.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="cut" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\cut.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="delete" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\delete.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="fenshu" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\fenshu.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="fontcolor" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\fontcolor.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="fullscreen" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\fullscreen.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="indent" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\indent.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="insertimage" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\insertimage.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="insertrule" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\insertrule.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="italic" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\italic.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="justifycenter" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\justifycenter.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="justifycenter1" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\justifycenter1.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="justifyfull" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\justifyfull.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="JustifyLeft" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\JustifyLeft.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="justifyright" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\justifyright.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="k1" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\k1.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Lcase" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Lcase.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="leftspin" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\leftspin.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="lefttitle" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\lefttitle.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="login" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\login.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="max1" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\max1.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="max2" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\max2.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="max3" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\max3.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="max4" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\max4.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="menubarbg" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\menubarbg.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="menubg" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\menubg.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Menuon1" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Menuon1.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="Menuon2" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Menuon2.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="menutoolsbg" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\menutoolsbg.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="menutopbg" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\menutopbg.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="mima" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\mima.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="min1" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\min1.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="min2" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\min2.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="min3" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\min3.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="min4" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\min4.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="mode_design" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\mode_design.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="mode_html" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\mode_html.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="mode_view" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\mode_view.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="msgtitlebg" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\msgtitlebg.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="no" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\no.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="numberedlist" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\numberedlist.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="numberedlist1" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\numberedlist1.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="nv_body_topbg" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\nv_body_topbg.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="nv_choice" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\nv_choice.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="nv_directory1" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\nv_directory.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="nv_left_topbg" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\nv_left_topbg.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="nv_user1" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\nv_user.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="outdent" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\outdent.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="paste" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\paste.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="print" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\print.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="redo" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\redo.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="removeformat" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\removeformat.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="replace" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\replace.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="save" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\save.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="SelectAll" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\SelectAll.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="size_status" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\size_status.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="specialchar" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\specialchar.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="splitterbg" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\splitterbg.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="strikethrough" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\strikethrough.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="style" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\style.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="subscript" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\subscript.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="superscript" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\superscript.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="table" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\table.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="tablebg" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\tablebg.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="time" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\time.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="titlebg" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\titlebg.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="today" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\today.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="toolsbg" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\toolsbg.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="toptitle" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\toptitle.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="UCase" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\UCase.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="UCase1" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\UCase1.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="underline" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\underline.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="undo" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\undo.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="visable1" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\visable1.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="visable2" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\visable2.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="wnl" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\wnl.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="zhanghaobg" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\zhanghaobg.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="_error" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\error.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="_exit" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\exit.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
</root>