﻿Imports System.Data.SqlClient
Imports BaseClass


Public Class Zd_MzFp14

#Region "变量初始化"
    Dim My_View As New DataView                 '数据视图

    Public My_Dataset As New DataSet
    Public My_Adapter As New SqlDataAdapter
    Public My_Table As New DataTable            '药品字典
    Public My_Cm As CurrencyManager             '同步指针
    Public My_Row As DataRow                    '当 前 行
    Public V_Insert As Boolean                  '增加记录
    Public V_FirstLoad As Boolean               '首次调入明细表
    Public V_Mc_Code As String

#End Region

    Private Sub Zd_MzFp14_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        If My_Dataset.Tables("明细") IsNot Nothing Then My_Dataset.Tables("明细").Clear()
        V_Mc_Code = Zd_MzFp11.My_Row.Item("Mc_Code")
        Call Form_Init()
        Call Init_Data()
    End Sub

#Region "窗体初始化"

    Private Sub Form_Init()
        Panel1.BorderStyle = BorderStyle.None
        Panel1.Height = 25
        ToolBar1.Location = New Point(1, 1)
        T_Line1.Location = New Point(ToolBar1.Width + 2, 0)
        T_Label.Location = New Point(T_Line1.Left + 2, 2)

        '初始化TDBGrid
        Dim My_Grid As New C_Grid(Me.C1TrueDBGrid1)
        With My_Grid
            .Init_Grid()
            .P_Dock(C_Grid.Dock.Fill)
            .Init_Column("", "Yy_Code", 0, "左", "")
            .Init_Column("", "Mc_Code", 0, "左", "")
            .Init_Column("", "Lb_Code", 0, "左", "")
            .Init_Column("名称", "Lb_Name", 120, "左", "")
            .Init_Column("简称", "Lb_Jc", 100, "中", "")
            C1TrueDBGrid1.AllowDelete = False
        End With
        C1TrueDBGrid1.Splits(0).DisplayColumns(0).Visible = False
        C1TrueDBGrid1.Splits(0).DisplayColumns(1).Visible = False
        C1TrueDBGrid1.Splits(0).DisplayColumns(2).Visible = False
    End Sub

    Private Sub Init_Data()
        Dim Str_Select As String = "Select Zd_MzFp4.Yy_Code,Zd_MzFp4.Lb_Code,Mc_Code,Lb_Name,Lb_Jc From Zd_MzFp4 inner join Zd_MzFp1 on Zd_MzFp4.Lb_Code=Zd_MzFp1.Lb_Code and  Zd_MzFp4.Yy_Code='" & HisVar.HisVar.WsyCode & "' and Mc_Code='" & Zd_MzFp11.C1TrueDBGrid1.Columns("Mc_Code").Value & "' UNION ALL Select Zd_MzFp4.Yy_Code,Zd_MzFp4.Lb_Code,Mc_Code,Dl_Name,Dl_Jc From Zd_MzFp4 inner join Zd_ML_Yp1 on Zd_MzFp4.Lb_Code='Y'+Zd_ML_Yp1.Dl_Code and  Zd_MzFp4.Yy_Code='" & HisVar.HisVar.WsyCode & "' and Mc_Code='" & Zd_MzFp11.C1TrueDBGrid1.Columns("Mc_Code").Value & "' Order By Zd_MzFp4.Lb_Code"
        Dim Str_Insert As String = "Insert Into Zd_Mzfp4(Yy_Code,Lb_Code,Mc_Code)Values(@Yy_Code,@Lb_Code,@Mc_Code)"
        Dim Str_Delete As String = "Delete From Zd_Mzfp4 WHERE Lb_Code=@Lb_Code and Yy_Code=@Yy_Code"

        With My_Adapter

            .InsertCommand = New SqlCommand(Str_Insert, My_Cn)
            With .InsertCommand.Parameters
                .Add("@Yy_Code", SqlDbType.Char, 4, "Yy_Code")
                .Add("@Lb_Code", SqlDbType.VarChar, 50, "Lb_Code")
                .Add("@Mc_Code", SqlDbType.Char, 8, "Mc_Code")

            End With
            .DeleteCommand = New SqlCommand(Str_Delete, My_Cn)
            With .DeleteCommand.Parameters
                .Add("@Lb_Code", SqlDbType.VarChar, 50, "Lb_Code")
                .Add("@Yy_Code", SqlDbType.Char, 4, "Yy_Code")
            End With
            .SelectCommand = New SqlCommand(Str_Select, My_Cn)
            .Fill(My_Dataset, "明细")
            .AcceptChangesDuringFill = True
            .MissingSchemaAction = MissingSchemaAction.AddWithKey
        End With

        My_Table = My_Dataset.Tables("明细")
        My_Table.PrimaryKey = New DataColumn() {My_Table.Columns("Lb_Code"), My_Table.Columns("Yy_Code")}
        With Me.C1TrueDBGrid1
            My_Cm = CType(BindingContext(My_Dataset, "明细"), CurrencyManager)
            .SetDataBinding(My_Dataset, "明细", True)

            T_Label.Text = "∑=" + .Splits(0).Rows.Count.ToString
        End With

        My_View = My_Cm.List


    End Sub

    Private Sub Zd_MzFp14_FormClosing(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosingEventArgs) Handles Me.FormClosing
        My_Dataset.Dispose()
    End Sub

#End Region

#Region "控件__动作"

    Private Sub Comm_Click(ByVal sender As Object, ByVal e As C1.Win.C1Command.ClickEventArgs) Handles Comm1.Click, Comm2.Click, Comm3.Click
        Select Case sender.text
            Case "增加"
                Call P_ShowData("增加")

            Case "删除"
                Beep()
                If C1TrueDBGrid1.Splits(0).Rows.Count = 0 Then Exit Sub
                Call P_Del_Data()

            Case "更新"
                My_Table.AcceptChanges()
                My_Adapter.Fill(My_Dataset, "明细")
                C1TrueDBGrid1.Select()
                C1TrueDBGrid1.MoveFirst()
        End Select

    End Sub


    Private Sub C1TrueDBGrid1_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles C1TrueDBGrid1.KeyDown
        Select Case e.KeyCode

            Case Keys.Delete
                If Me.C1TrueDBGrid1.RowCount > 0 Then Call P_Del_Data()
            Case Keys.Insert
                P_ShowData("增加")
        End Select
    End Sub

    Dim m_Rc As New BaseClass.C_RowChange
    Private Sub C1TrueDBGrid1_RowColChange(ByVal sender As System.Object, ByVal e As C1.Win.C1TrueDBGrid.RowColChangeEventArgs) Handles C1TrueDBGrid1.RowColChange
        If (C1TrueDBGrid1.Row + 1) > C1TrueDBGrid1.RowCount Then

        Else
            My_Row = My_Cm.List(C1TrueDBGrid1.Row).Row
            m_Rc.ChangeRow(My_Row)
        End If
    End Sub

#End Region

#Region "自定义函数"

    Private Sub P_ShowData(ByVal V_Lb As String)
        If V_Lb = "增加" Then
            V_Insert = True
        Else
            If Me.C1TrueDBGrid1.RowCount > 0 Then V_Insert = False Else V_Insert = True
        End If

        If Zd_MzFp13 Is Nothing Then   '窗体没有调入
            V_FirstLoad = True
            Zd_MzFp13.Owner = Me
        End If
        Zd_MzFp13.ShowDialog()
        C1TrueDBGrid1.Select()
    End Sub

    Private Sub P_Del_Data()
        Beep()
        If MsgBox("是否删除:名称=" + C1TrueDBGrid1.Columns("Lb_Name").Value + " ", MsgBoxStyle.Information + MsgBoxStyle.OkCancel + MsgBoxStyle.DefaultButton1, "提示:") = MsgBoxResult.Cancel Then Exit Sub
        My_Row = My_Cm.List(C1TrueDBGrid1.Row).Row
        My_Adapter.DeleteCommand.Parameters(0).Value = My_Row.Item(1)
        My_Adapter.DeleteCommand.Parameters(1).Value = HisVar.HisVar.WsyCode
        Try
            Call P_Conn(True)
            My_Adapter.DeleteCommand.ExecuteNonQuery()
            C1TrueDBGrid1.Delete()
            My_Row.AcceptChanges()
            T_Label.Text = "∑=" + C1TrueDBGrid1.Splits(0).Rows.Count.ToString
        Catch ex As Exception
            Beep()
            MsgBox("错误:" + ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
        Finally
            Call P_Conn(False)
            C1TrueDBGrid1.Select()
        End Try
    End Sub

#End Region


End Class
