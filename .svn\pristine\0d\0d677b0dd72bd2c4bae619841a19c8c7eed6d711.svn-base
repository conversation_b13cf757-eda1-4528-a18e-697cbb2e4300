﻿Imports BaseClass
Imports System.Windows.Forms
Imports System.Drawing

Public Class Zd_ShikongTwo

#Region "变量定义"
    Dim V_Name As String                                            '简称是否发生变化
    Dim V_LbCount As Integer

    Dim Zd_Emr_SiKong As New ModelOld.M_Emr_SiKong

    Dim Zd_Emr_SiKongbll As New BLLOld.B_Emr_SiKong

    Dim Zd_Emr_Mblbbll As New BLLOld.B_Emr_Mblb
    Dim zd_Emr_Ebbll As New BLLOld.B_Emr_Mb

    Dim C_Py As New BaseClass.Chs2Spell
    Dim mark As Integer = 1
    Dim skdatatable As New DataTable
    Dim dt As DataTable = Zd_Emr_Mblbbll.GetAllList.Tables(0)
#End Region
#Region "传参"
    Dim Rinsert As Boolean
    Dim Rrow As DataRow
    Dim RZbtb As DataTable
    Dim Rrc As C_RowChange
#End Region
    Public Sub New(ByVal tinsert As Boolean, ByVal trow As DataRow, ByVal tZbtb As DataTable, ByRef trc As C_RowChange)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        Rinsert = tinsert
        Rrow = trow
        RZbtb = tZbtb
        Rrc = trc

        ' 在 InitializeComponent() 调用之后添加任何初始化。
    End Sub

    Private Sub Zd_Management2_FormClosed(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosedEventArgs) Handles Me.FormClosed
        RemoveHandler Rrc.RowChanged, AddressOf Data_Show
    End Sub

    Private Sub Zd_Management2_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Me.Load
        AddHandler Rrc.RowChanged, AddressOf Data_Show
        Call Form_Init()
        If Rinsert = True Then Call Data_Clear() Else Call Data_Show(Rrow)


    End Sub

#Region "窗体__事件"

    Private Sub Form_Init()
        Panel1.Height = 27
        ToolBar1.Location = New Point(2, 4)

        '按扭初始化
        Comm1.Top = 2
        Comm2.Location = New Point(Comm1.Left + Comm1.Width + 2, Comm1.Top)
        With ylsjMySingleComobo
            .Additem = "入院"
            .Additem = "出院"
            .DisplayColumns(1).Visible = False
            .SelectedIndex = 0
        End With


        skdatatable.Columns.Add("Mblb_Code")
        skdatatable.Columns.Add("Mblb_Name")
        skdatatable.Columns.Add("Mblb_Jc")
        skdatatable.Columns.Add("Father_Code")
       


        With mblbMyDtComobo
            '.DataView = digui("00000").DefaultView
            .DataView = Zd_Emr_Mblbbll.getyzlis().Tables(0).DefaultView
            .Init_Colum("Mblb_Code", "模版类别编码", 0, "左")
            .Init_Colum("Mblb_Name", "模版类别名称", 80, "左")
            .Init_Colum("Mblb_Jc", "模版类别简称", 60, "左")
            .DisplayMember = "Mblb_Name"
            .ValueMember = "Mblb_Code"
            .RowFilterNotTextNull = "Mblb_Jc"
            .RowFilterTextNull = ""
            .DroupDownWidth = .Width - 40
            .MaxDropDownItems = 15
            .SelectedValue = -1
        End With

        With mbMyDtComobo
            .DataView = zd_Emr_Ebbll.GetAllList.Tables(0).DefaultView
            .Init_Colum("Mb_Code", "模版编码", 0, "左")
            .Init_Colum("Mb_Name", "模版名称", 80, "左")
            .Init_Colum("Mb_Jc", "模版简称", 40, "左")
            .DisplayMember = "Mb_Name"
            .ValueMember = "Mb_Code"
            .RowFilterNotTextNull = "Mb_Jc"
            .RowFilterTextNull = ""
            .DroupDownWidth = .Width - 40
            .MaxDropDownItems = 15
            .SelectedValue = -1
            .DefaultIndex = -1
        End With



       

        skCode_TextBox.Enabled = False
        'Jc_TextBox.Enabled = False


    End Sub

#End Region

#Region "数据__操作"
    Private Function digui(ByVal code) As DataTable

        Dim dtdigui As DataTable = dt


        If Zd_Emr_Mblbbll.Exists(code) Then
            For Each row In dtdigui.Rows
                If code = row("Father_Code").ToString() Then
                    digui(row("Mblb_Code").ToString())
                Else
                    Continue For

                End If
            Next
        Else
            Try
                Dim row1 As DataRow = dt.NewRow()
                row1 = Zd_Emr_Mblbbll.GetList("Mblb_Code='" & code & "'").Tables(0).Rows(0)
                skdatatable.Rows.Add(row1.ItemArray)
            Catch ex As Exception

            End Try

        End If

        Return skdatatable
    End Function


    Private Sub Data_Clear()
        Rinsert = True
        skCode_TextBox.Text = Zd_Emr_SiKongbll.MaxCode()                  '最大编码
       
        ylsjMySingleComobo.SelectedValue = -1
        mblbMyDtComobo.SelectedValue = -1
        mbMyDtComobo.SelectedValue = -1
        dayNumericEdit.Text = 0
        hourNumericEdit.Text = 0

        Memo_TextBox.Text = ""
        Call P_Show_Label()
    End Sub

    Private Sub Data_Show(ByVal tmp_Row As DataRow)
        Rinsert = False
        Rrow = tmp_Row
        With Rrow

            skCode_TextBox.Text = .Item("Sk_Code") & ""
            ylsjMySingleComobo.Text = .Item("Yl_Event") & ""
            mblbMyDtComobo.SelectedValue = .Item("Mblb_Code") & ""
            mbMyDtComobo.SelectedValue = .Item("Mb_Code") & ""
            dayNumericEdit.Text = .Item("Days") & ""
            hourNumericEdit.Text = .Item("Hours") & ""
           

            Memo_TextBox.Text = .Item("Memo") & ""

        End With
        Call P_Show_Label()
    End Sub

    Private Sub P_Show_Label()

        If Rinsert = True Then
            Move5.Enabled = False                                           '新增记录
            T_Label2.Text = "新增"
        Else
            Move5.Enabled = True                                            '新增记录
            T_Label2.Text = IIf(RZbtb.Rows.Count() = 0, "0", CStr((RZbtb.Rows.IndexOf(Rrow)) + 1))
        End If
        T_Label3.Text = "∑=" & RZbtb.Rows.Count
        'Name_TextBox.Select()
    End Sub

#End Region

#Region "控件__动作"

    Private Sub Comm_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.Click, Comm2.Click
        Select Case sender.tag
            Case "保存"
                mark = 0

                If Zd_Emr_Mblbbll.Existsyz(mblbMyDtComobo.SelectedValue & "") = False Then
                    MsgBox("请选择模版类别！", MsgBoxStyle.Exclamation, "提示")
                    mblbMyDtComobo.Select()
                    Exit Sub
                End If
                If ylsjMySingleComobo.Text & "" = "" Then
                    MsgBox("请选择医疗事件！", MsgBoxStyle.Exclamation, "提示")
                    ylsjMySingleComobo.Select()
                    Exit Sub
                End If
                If dayNumericEdit.Text.Trim = "" Then
                    MsgBox("请输入天数！", MsgBoxStyle.Exclamation, "提示")
                    dayNumericEdit.Select()
                    Exit Sub
                End If

                If hourNumericEdit.Text.Trim = "" Then
                    MsgBox("请输入小时数！", MsgBoxStyle.Exclamation, "提示")
                    hourNumericEdit.Select()
                    Exit Sub
                End If

                If Rinsert = True Then
                    Call Data_Add()
                Else
                    Call Data_Edit()
                End If
                mblbMyDtComobo.Select()
            Case "取消"
                Me.Close()
        End Select
    End Sub

    Private Sub Move_Click(ByVal sender As Object, ByVal e As C1.Win.C1Command.ClickEventArgs) Handles Move1.Click, Move2.Click, Move3.Click, Move4.Click, Move5.Click
        If sender.text = "新增" Then                            '增加状态
            Call Data_Clear()
        Else
            Rrc.GridMove(sender.text)
        End If
    End Sub


#End Region

#Region "数据__编辑"

    Private Sub Data_Add()
        Dim mbdatatable As New DataTable
        
        'mbdatatable = zd_Emr_Ebbll.GetListmblb(" Mb_Code='" & Trim(mbMyDtComobo.SelectedValue & "") & "'").Tables(0)
        Dim My_NewRow As DataRow = RZbtb.NewRow
        With My_NewRow
            .Item("Sk_Code") = Trim(skCode_TextBox.Text & "")
            .Item("Yl_Event") = Trim(ylsjMySingleComobo.Text & "")
            .Item("Mblb_Code") = mblbMyDtComobo.SelectedValue
            .Item("Mb_Code") = mbMyDtComobo.SelectedValue
            .Item("Mb_Name") = mbMyDtComobo.Text
            .Item("Mblb_Name") = mblbMyDtComobo.Text
            .Item("Days") = Trim(dayNumericEdit.Text & "")
            .Item("Hours") = Trim(hourNumericEdit.Text & "")
            .Item("Memo") = Trim(Memo_TextBox.Text & "")
        End With

        '数据保存
        Try
            RZbtb.Rows.Add(My_NewRow)
            Rrc.GridMove("最后")
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            Exit Sub
        Finally
            'Name_TextBox.Select()
        End Try

        '数据更新
        Try
            With Zd_Emr_SiKong
                .Sk_Code = My_NewRow.Item("Sk_Code")
                .Mblb_Code = My_NewRow.Item("Mblb_Code")
                .Mb_Code = My_NewRow.Item("Mb_Code")
                .Yl_Event = My_NewRow.Item("Yl_Event")

                .Days = My_NewRow.Item("Days")
                .Hours = My_NewRow.Item("Hours")
                .Memo = My_NewRow.Item("Memo")

            End With
            Zd_Emr_SiKongbll.Add(Zd_Emr_SiKong)
            My_NewRow.AcceptChanges()
            RZbtb.AcceptChanges()
            MsgBox("添加成功！", MsgBoxStyle.Exclamation, "提示:")
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            'Name_TextBox.Select()
            Exit Sub
        Finally
        End Try

        '数据清空
        Call Data_Clear()

    End Sub
    Private Sub Xq_Combo_RowChange(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles mblbMyDtComobo.RowChange
        Dim sql As String = "1=1"
        If mblbMyDtComobo.SelectedValue IsNot Nothing Then
            sql = sql & " AND Emr_Mb.MbLb_Code = '" & mblbMyDtComobo.SelectedValue & "'"
        End If
        If Rinsert = True Then
            With mbMyDtComobo
                .DataView = zd_Emr_Ebbll.GetList(sql).Tables(0).DefaultView
                .Init_Colum("Mb_Code", "模版编码", 0, "左")
                .Init_Colum("Mb_Name", "模版名称", 80, "左")
                .Init_Colum("Mb_Jc", "模版简称", 40, "左")
                .DisplayMember = "Mb_Name"
                .ValueMember = "Mb_Code"
                .RowFilterNotTextNull = "Mb_Jc"
                .RowFilterTextNull = ""
                .DroupDownWidth = .Width
                .MaxDropDownItems = 15
                .SelectedValue = -1
                .DefaultIndex = -1
            End With
        Else
            If mblbMyDtComobo.SelectedValue IsNot Nothing AndAlso mblbMyDtComobo.SelectedValue <> Rrow.Item("Mblb_Code") & "" Then

                With mbMyDtComobo
                    .DataView = zd_Emr_Ebbll.GetList(sql).Tables(0).DefaultView
                    .Init_Colum("Mb_Code", "模版编码", 0, "左")
                    .Init_Colum("Mb_Name", "模版名称", 80, "左")
                    .Init_Colum("Mb_Jc", "模版简称", 40, "左")
                    .DisplayMember = "Mb_Name"
                    .ValueMember = "Mb_Code"
                    .RowFilterNotTextNull = "Mb_Code"
                    .RowFilterTextNull = ""
                    .DroupDownWidth = .Width
                    .MaxDropDownItems = 15
                    .SelectedValue = 0
                End With

                With mbMyDtComobo
                    .DataView = zd_Emr_Ebbll.GetList(sql).Tables(0).DefaultView
                    .Init_Colum("Mb_Code", "模版编码", 0, "左")
                    .Init_Colum("Mb_Name", "模版名称", 80, "左")
                    .Init_Colum("Mb_Jc", "模版简称", 40, "左")
                    .DisplayMember = "Mb_Name"
                    .ValueMember = "Mb_Code"
                    .RowFilterNotTextNull = "Mb_Jc"
                    .RowFilterTextNull = ""
                    .DroupDownWidth = .Width
                    .MaxDropDownItems = 15
                    .SelectedValue = -1
                    .DefaultIndex = -1
                End With
            End If
        End If
        


    End Sub
    Private Sub Data_Edit()

        Dim My_Row As DataRow = Rrow
        'Dim mbdatatable As New DataTable
        'mbdatatable = zd_Emr_Ebbll.GetListmblb(" Mb_Code='" & Trim(mbMyDtComobo.SelectedValue & "") & "'").Tables(0)
        Try

            With My_Row
                .BeginEdit()
                .Item("Sk_Code") = Trim(skCode_TextBox.Text & "")
                .Item("Yl_Event") = Trim(ylsjMySingleComobo.Text & "")
                .Item("Mblb_Code") = mblbMyDtComobo.SelectedValue
                .Item("Mb_Code") = mbMyDtComobo.SelectedValue
                .Item("Mb_Name") = mbMyDtComobo.Text
                .Item("Mblb_Name") = mblbMyDtComobo.Text
                .Item("Days") = Trim(dayNumericEdit.Text & "")
                .Item("Hours") = Trim(hourNumericEdit.Text & "")
                .Item("Memo") = Trim(Memo_TextBox.Text & "")
                .EndEdit()
            End With



        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Critical, "提示:")
            My_Row.CancelEdit()
            Exit Sub
        Finally
            'Name_TextBox.Select()
        End Try

        '数据更新
        Try
            With Zd_Emr_SiKong
                .Sk_Code = My_Row.Item("Sk_Code")
                .Mblb_Code = My_Row.Item("Mblb_Code")
                .Mb_Code = My_Row.Item("Mb_Code")
                .Yl_Event = My_Row.Item("Yl_Event")
                .Days = My_Row.Item("Days")
                .Hours = My_Row.Item("Hours")
                .Memo = My_Row.Item("Memo")
               
            End With
            Zd_Emr_SiKongbll.Update(Zd_Emr_SiKong)
            My_Row.AcceptChanges()

            MsgBox("更新成功！", MsgBoxStyle.Exclamation, "提示:")
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Information + MsgBoxStyle.OkOnly, "提示:")
        Finally
            'Name_TextBox.Select()
        End Try
    End Sub

#End Region



    Private Sub ZhongWen_TextBox_GotFocus(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Memo_TextBox.GotFocus, mblbMyDtComobo.GotFocus, mbMyDtComobo.GotFocus, ylsjMySingleComobo.GotFocus
        InputLanguage.CurrentInputLanguage = HisVar.HisVar.InputCode
    End Sub
    Private Sub YingWen_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs)
        InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages(0)
    End Sub
End Class