﻿/**  版本信息模板在安装目录下，可自行修改。
* M_Emr_Bl.cs
*
* 功 能： N/A
* 类 名： M_Emr_Bl
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/8/22 3:39:01   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace ModelOld
{
	/// <summary>
	/// M_Emr_Bl:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class M_Emr_Bl
	{
		public M_Emr_Bl()
		{}
		#region Model
		private long _id;
        private long _Print_Row;
        private long _Print_Page;
		private string _bl_code;
		private string _mb_code;
		private string _mb_name;
		private byte[] _mb_nr;
		private string _jsr_code;
		private string _ys_code;
		private DateTime? _adddate;
        private string _mblb_code;
        private string _ks_code;
		/// <summary>
		/// 
		/// </summary>
		public long id
		{
			set{ _id=value;}
			get{return _id;}
		}
        /// <summary>
        /// 
        /// </summary>
        public long Print_Row
        {
            set { _Print_Row = value; }
            get { return _Print_Row; }
        }
        /// <summary>
        /// 
        /// </summary>
        public long Print_Page
        {
            set { _Print_Page = value; }
            get { return _Print_Page; }
        }
		/// <summary>
		/// 
		/// </summary>
		public string Bl_Code
		{
			set{ _bl_code=value;}
			get{return _bl_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Mb_Code
		{
			set{ _mb_code=value;}
			get{return _mb_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Mb_Name
		{
			set{ _mb_name=value;}
			get{return _mb_name;}
		}
		/// <summary>
		/// 
		/// </summary>
		public byte[] Mb_Nr
		{
			set{ _mb_nr=value;}
			get{return _mb_nr;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Jsr_Code
		{
			set{ _jsr_code=value;}
			get{return _jsr_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Ys_Code
		{
			set{ _ys_code=value;}
			get{return _ys_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public DateTime? AddDate
		{
			set{ _adddate=value;}
			get{return _adddate;}
		}
        /// <summary>
        /// 
        /// </summary>
        public string Mblb_Code
        {
            set { _mblb_code = value; }
            get { return _mblb_code; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string Ks_Code
        {
            set { _ks_code = value; }
            get { return _ks_code; }
        }
		#endregion Model

	}
}

