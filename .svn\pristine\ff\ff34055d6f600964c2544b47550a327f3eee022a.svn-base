﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ERX.Model
{
    /// <summary>
    /// 电子处方上传
    /// </summary>
    public class MdlrxFileUpldIn
    {
        public string rxTraceCode { get; set; }   // 处方追溯码
        public string hiRxno { get; set; }   // 医保处方编号
        public string mdtrtId { get; set; }   // 医保就诊ID 
        public string patnName { get; set; }   // 患者姓名 
        public string psnCertType { get; set; }   // 人员证件类型 
        public string certno { get; set; }   // 证件号码 
        public string fixmedinsName { get; set; }   // 定点医疗机构名称
        public string fixmedinsCode { get; set; }   // 定点医疗机构编号
        public string drCode { get; set; }   // 开方医保医师代码 
        public string prscDrName { get; set; }   // 开方医师姓名
        public string pharDeptName { get; set; }   // 审方药师科室名称 
        public string pharDeptCode { get; set; }   // 审方药师科室编号 
        public string pharProfttlCodg { get; set; }   // 审方药师职称编码 
        public string pharProfttlName { get; set; }   // 审方药师职称名称
        public string pharCode { get; set; }   // 审方医保药师代码 
        public string pharCertType { get; set; }   // 审方药师证件类型 
        public string pharCertno { get; set; }   // 审方药师证件号码 
        public string pharName { get; set; }   // 审方药师姓名 
        public string pharPracCertNo { get; set; }   // 审方药师执业资格证号 
        public string  pharChkTime { get; set; }   // 医疗机构药师审方时间
        public string rxFile { get; set; }   // 处方原件
        public string signDigest { get; set; }   // 处方信息签名值 
        public string extras { get; set; }   // 扩展字段


    }
    public class MdlrxFileUpldOut
    {
        public string hiRxno { get; set; }   // 医保处方编号
        public string rxStasCodg { get; set; }   // 医保处方状态编码 
        public string rxStasName { get; set; }   // 医保处方状态名称

    }
}
