﻿
Imports System.Data.SqlClient
Imports BaseClass

Public Class Yp_Xx
    Dim V_Xx_Code As String

#Region "传参"
    '<PERSON><PERSON> As Boolean
    Dim Rrow As DataRow
    Dim RZbtb As DataTable

#End Region

    Public Sub New(ByVal trow As DataRow, ByVal tZbtb As DataTable)

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        Rrow = trow
        RZbtb = tZbtb
        ' 在 InitializeComponent() 调用之后添加任何初始化。
    End Sub


    Private Sub Yk_Cx1_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Data_Show(Rrow)
    End Sub

    Private Sub Data_Show(ByVal tmp_Row As DataRow)
        Rrow = tmp_Row
        V_Xx_Code = Rrow("Xx_Code") & ""
        C1TextBox1.Text = Rrow("Yp_Name") & ""
        C1TextBox4.Text = Rrow("Yp_Ph") & ""
        DateTimePicker2.Value = IIf(Rrow("Yp_Yxq") Is DBNull.Value, "1900-01-01", Rrow("Yp_Yxq"))
    End Sub

    Private Sub C1Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Button1.Click
        Dim My_Reader As SqlDataReader = HisVar.HisVar.Sqldal.ExecuteReader("Select Yp_Ph,Yp_Yxq from V_Ypkc where Mx_Code=left('" & V_Xx_Code & "',8) and Yp_Yxq='" & Format(DateTimePicker2.Value, "yyyy-MM-dd") & "' and Yp_Ph='" & C1TextBox4.Text & "' and Xx_Code<>'" & V_Xx_Code & "'")
        While My_Reader.Read
            MsgBox("存在相同批次及有效期的药品！", vbExclamation + vbOKOnly + vbDefaultButton1, "提示:")
            My_Reader.Close()
            My_Cn.Close()
            Exit Sub
        End While
        My_Reader.Close()
        My_Cn.Close()

        With Rrow
            .BeginEdit()
            .Item("Yp_Ph") = C1TextBox4.Text & ""
            .Item("Yp_Yxq") = Format(DateTimePicker2.Value, "yyyy-MM-dd")
            .EndEdit()
        End With
        Rrow.AcceptChanges()
        RZbtb.AcceptChanges()

        HisVar.HisVar.Sqldal.ExecuteSql("Update Zd_Ml_Yp4 set Yp_Ph='" & C1TextBox4.Text & "',Yp_Yxq='" & Format(DateTimePicker2.Value, "yyyy-MM-dd") & "' where Xx_Code='" & V_Xx_Code & "'")
        MsgBox("保存成功！", vbExclamation + vbOKOnly + vbDefaultButton1, "提示:")
        Me.Close()
    End Sub

    Private Sub C1Button2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Button2.Click
        Me.Close()
    End Sub
End Class