﻿/**  版本信息模板在安装目录下，可自行修改。
* D_Emr_BlZk1.cs
*
* 功 能： N/A
* 类 名： D_Emr_BlZk1
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/9/19 17:08:27   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
namespace DAL
{
	/// <summary>
	/// 数据访问类:D_Emr_BlZk1
	/// </summary>
	public partial class D_Emr_BlZk1
	{
		public D_Emr_BlZk1()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string Bl_Code)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from Emr_BlZk1");
			strSql.Append(" where Bl_Code=@Bl_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Bl_Code", SqlDbType.Char,14)			};
			parameters[0].Value = Bl_Code;

			return HisVar.HisVar.Sqldal.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ModelOld.M_Emr_BlZk1 model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into Emr_BlZk1(");
			strSql.Append("Bl_Code,Jsr_Code,Zk_Date,Zk_Pf,zkdj_code)");
			strSql.Append(" values (");
            strSql.Append("@Bl_Code,@Jsr_Code,@Zk_Date,@Zk_Pf,@zkdj_code)");
			SqlParameter[] parameters = {
					new SqlParameter("@Bl_Code", SqlDbType.Char,14),
					new SqlParameter("@Jsr_Code", SqlDbType.Char,7),
					new SqlParameter("@Zk_Date", SqlDbType.SmallDateTime),
					new SqlParameter("@Zk_Pf", SqlDbType.Decimal,9),
					new SqlParameter("@zkdj_code", SqlDbType.VarChar,50)};
			parameters[0].Value = model.Bl_Code;
			parameters[1].Value = model.Jsr_Code;
			parameters[2].Value = model.Zk_Date;
			parameters[3].Value = model.Zk_Pf;
            parameters[4].Value = model.Zkdj_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ModelOld.M_Emr_BlZk1 model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update Emr_BlZk1 set ");
            //strSql.Append("Jsr_Code=@Jsr_Code,");
            //strSql.Append("Zk_Date=@Zk_Date,");
			strSql.Append("Zk_Pf=@Zk_Pf,");
			strSql.Append("zkdj_code=@zkdj_code");
			strSql.Append(" where Bl_Code=@Bl_Code ");
			SqlParameter[] parameters = {
                    //new SqlParameter("@Jsr_Code", SqlDbType.Char,7),
                    //new SqlParameter("@Zk_Date", SqlDbType.SmallDateTime),
					new SqlParameter("@Zk_Pf", SqlDbType.Decimal,9),
					new SqlParameter("@zkdj_code", SqlDbType.VarChar,50),
					new SqlParameter("@Bl_Code", SqlDbType.Char,14)};
            //parameters[0].Value = model.Jsr_Code;
            //parameters[1].Value = model.Zk_Date;
			parameters[0].Value = model.Zk_Pf;
            parameters[1].Value = model.Zkdj_Code;
			parameters[2].Value = model.Bl_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string Bl_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Emr_BlZk1 ");
			strSql.Append(" where Bl_Code=@Bl_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Bl_Code", SqlDbType.Char,14)			};
			parameters[0].Value = Bl_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string Bl_Codelist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Emr_BlZk1 ");
			strSql.Append(" where Bl_Code in ("+Bl_Codelist + ")  ");
			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Emr_BlZk1 GetModel(string Bl_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 Bl_Code,Jsr_Code,Zk_Date,Zk_Pf,zkdj_code from Emr_BlZk1 ");
			strSql.Append(" where Bl_Code=@Bl_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Bl_Code", SqlDbType.Char,14)			};
			parameters[0].Value = Bl_Code;

			ModelOld.M_Emr_BlZk1 model=new ModelOld.M_Emr_BlZk1();
			DataSet ds=HisVar.HisVar.Sqldal.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Emr_BlZk1 DataRowToModel(DataRow row)
		{
			ModelOld.M_Emr_BlZk1 model=new ModelOld.M_Emr_BlZk1();
			if (row != null)
			{
				if(row["Bl_Code"]!=null)
				{
					model.Bl_Code=row["Bl_Code"].ToString();
				}
				if(row["Jsr_Code"]!=null)
				{
					model.Jsr_Code=row["Jsr_Code"].ToString();
				}
				if(row["Zk_Date"]!=null && row["Zk_Date"].ToString()!="")
				{
					model.Zk_Date=DateTime.Parse(row["Zk_Date"].ToString());
				}
				if(row["Zk_Pf"]!=null && row["Zk_Pf"].ToString()!="")
				{
					model.Zk_Pf=decimal.Parse(row["Zk_Pf"].ToString());
				}
				if(row["zkdj_code"]!=null)
				{
                    model.Zkdj_Code = row["zkdj_code"].ToString();
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select Bl_Code,Jsr_Code,Zk_Date,Zk_Pf,zkdj_code ");
			strSql.Append(" FROM Emr_BlZk1 ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}
        /// <summary>
        /// 获得关联数据列表
        /// </summary>
        public DataSet GetListwithZkdj(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Bl_Code,Jsr_Code,Zk_Date,Zk_Pf,Emr_BlZk1.zkdj_code,zkdj_name  ");
            strSql.Append(" FROM Emr_BlZk1 left join emr_zkdj on Emr_BlZk1.zkdj_code=emr_zkdj.zkdj_code ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" Bl_Code,Jsr_Code,Zk_Date,Zk_Pf,zkdj_code ");
			strSql.Append(" FROM Emr_BlZk1 ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM Emr_BlZk1 ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.Bl_Code desc");
			}
			strSql.Append(")AS Row, T.*  from Emr_BlZk1 T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Emr_BlZk1";
			parameters[1].Value = "Bl_Code";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return HisVar.HisVar.Sqldal.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

