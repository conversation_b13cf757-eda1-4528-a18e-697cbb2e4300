﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="0" />
    <DataSources isList="true" count="0" />
    <Relations isList="true" count="0" />
    <Report isRef="0" />
    <Variables isList="true" count="23">
      <value>,标题,标题,System.String,,False,False</value>
      <value>,结账时间,结账时间,System.String,,False,False</value>
      <value>,打印时间,打印时间,System.String,,False,False</value>
      <value>,公务卡正常充值,公务卡正常充值,System.String,,False,False</value>
      <value>,公务卡负数退费,公务卡负数退费,System.String,,False,False</value>
      <value>,普通卡正常充值,普通卡正常充值,System.String,,False,False</value>
      <value>,普通卡负数退费,普通卡负数退费,System.String,,False,False</value>
      <value>,现金正常充值,现金正常充值,System.String,,False,False</value>
      <value>,现金负数退费,现金负数退费,System.String,,False,False</value>
      <value>,退回现金,退回现金,System.String,,False,False</value>
      <value>,退银行卡,退银行卡,System.String,,False,False</value>
      <value>,患者消费,患者消费,System.String,,False,False</value>
      <value>,公务卡充值合计,公务卡充值合计,System.String,,False,False</value>
      <value>,普通卡充值合计,普通卡充值合计,System.String,,False,False</value>
      <value>,现金充值合计,现金充值合计,System.String,,False,False</value>
      <value>,POS充值合计,POS充值合计,System.String,,False,False</value>
      <value>,医院支出,医院支出,System.String,,False,False</value>
      <value>,医院收入,医院收入,System.String,,False,False</value>
      <value>,结算单位,结算单位,System.String,,False,False</value>
      <value>,经手人,经手人,System.String,,False,False</value>
      <value>,消费撤销,消费撤销,System.String,,False,False</value>
      <value>,实际消费,实际消费,System.String,,False,False</value>
      <value>,财务日期,财务日期,System.String,,False,False</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="2" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="2">
        <ReportTitleBand1 Ref="3" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,19,10.5</ClientRectangle>
          <Components isList="true" count="46">
            <Text43 Ref="4" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,19,0.9</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,18,Bold,Point,False,134</Font>
              <Guid>ec1ea93bc9ce493692b525113a2a7d72</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text43</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{标题}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text43>
            <Text58 Ref="5" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>13.3,0.9,5.7,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11</Font>
              <Guid>e6812fbdcb5b4bbeb90790773fd4531c</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text58</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{打印时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text58>
            <Text59 Ref="6" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.9,13.3,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11</Font>
              <Guid>adf689dc89b84ad8a30f47e37ccdb385</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text59</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{结账时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text59>
            <Text23 Ref="7" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.6,19,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11</Font>
              <Guid>766d88c3f90541b2bc7e0ba2301c5e38</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text23</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>交易类别</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text23>
            <Text30 Ref="8" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,2.3,7.1,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11</Font>
              <Guid>b942e752aa784d599621a2badacb3310</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text30</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>POS充值</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text30>
            <Text31 Ref="9" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.1,2.3,3.5,1.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11</Font>
              <Guid>9210210579f64db29f4a2984b222f217</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text31</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>现金充值</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text31>
            <Text32 Ref="10" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,3,3.6,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11</Font>
              <Guid>8c75d6da30a24c56a590d2882b42d943</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text32</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>公务卡充值</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text32>
            <Text33 Ref="11" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.6,3,3.5,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11</Font>
              <Guid>0faf6893a91c4b4bbd6f102d3e96e760</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text33</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>普通卡充值</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text33>
            <Text34 Ref="12" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>10.6,2.3,1.6,2.1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11</Font>
              <Guid>c7a17a751be4483788450977c0dfca60</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text34</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>退回现金</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text34>
            <Text35 Ref="13" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>12.2,2.3,1.6,2.1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11</Font>
              <Guid>90f3dc4eff0341f29e0da072d6c84699</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text35</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>退银行卡</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text35>
            <Text36 Ref="14" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,3.7,1.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11</Font>
              <Guid>ad80e431fc054bc1bc99f1792437655d</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text36</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>正常充值</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text36>
            <Text37 Ref="15" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13.8,2.3,5.2,1.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11</Font>
              <Guid>535f6a8a6fbd4516b479ffb7f1c69395</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text37</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>患者消费</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text37>
            <Text38 Ref="16" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.8,3.7,1.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11</Font>
              <Guid>0380fbd3f3484f9680d4cfdded3c9332</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text38</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>负数退费</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text38>
            <Text39 Ref="17" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.6,3.7,1.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11</Font>
              <Guid>a456b79432874a8a80ec6fc344cd232d</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text39</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>正常充值</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text39>
            <Text40 Ref="18" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>5.4,3.7,1.7,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11</Font>
              <Guid>3169a3f991cb40939eab34642011a0a7</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text40</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>负数退费</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text40>
            <Text41 Ref="19" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.1,3.7,1.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11</Font>
              <Guid>8998340336d9429292dd8349178ba22c</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text41</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>正常充值</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text41>
            <Text42 Ref="20" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>8.9,3.7,1.7,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11</Font>
              <Guid>f2ace4393d4e4027bfe407f9e9a91911</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text42</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>负数退费</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text42>
            <Text46 Ref="21" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,4.4,1.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11</Font>
              <Guid>72139d39df414170b7fde875ec8bf2a1</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text46</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{公务卡正常充值}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="22" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text46>
            <Text48 Ref="23" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.8,4.4,1.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11</Font>
              <Guid>72d5183954a74afead81d943fbffcafe</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text48</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{公务卡负数退费}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="24" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text48>
            <Text49 Ref="25" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.6,4.4,1.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11</Font>
              <Guid>a460216b6f0f4f6fb28baeca99c553e9</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text49</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{普通卡正常充值}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="26" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text49>
            <Text50 Ref="27" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>5.4,4.4,1.7,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11</Font>
              <Guid>14a3f82568da489a80661ef9c0b02d7f</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text50</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{普通卡负数退费}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="28" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text50>
            <Text51 Ref="29" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.1,4.4,1.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11</Font>
              <Guid>a0e8b696ff604926b3c67dc85cfa8a0d</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text51</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{现金正常充值}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="30" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text51>
            <Text52 Ref="31" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>8.9,4.4,1.7,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11</Font>
              <Guid>12e92a67cbaa42069582324a8295ed4d</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text52</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{现金负数退费}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="32" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text52>
            <Text44 Ref="33" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>10.6,4.4,1.6,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11</Font>
              <Guid>1031ac3a9b6b40a8ba63acd43dda2984</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text44</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{退回现金}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="34" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text44>
            <Text45 Ref="35" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>12.2,4.4,1.6,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11</Font>
              <Guid>a4ee408d21a54297a23d2d6630520622</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text45</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{退银行卡}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="36" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text45>
            <Text47 Ref="37" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13.8,4.4,1.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11</Font>
              <Guid>4fc1914860214fc7ad44a1846ad46a6a</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text47</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{患者消费}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="38" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text47>
            <Text61 Ref="39" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,5.1,3.6,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11</Font>
              <Guid>f0234cb4a594453c84cd792981bbb038</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text61</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{公务卡充值合计}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="40" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text61>
            <Text62 Ref="41" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.6,5.1,3.5,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11</Font>
              <Guid>a91d5572db9c48d2ac76c62adea7178c</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text62</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{普通卡充值合计}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="42" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text62>
            <Text63 Ref="43" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.1,5.1,3.5,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11</Font>
              <Guid>60af4c267c9c4d6c9be379a5c1c0121d</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text63</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{现金充值合计}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="44" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text63>
            <Text64 Ref="45" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>10.6,5.1,8.4,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11</Font>
              <Guid>a9b698dd81b947a0af25f64b03203786</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text64</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="46" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text64>
            <Text65 Ref="47" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,5.8,7.1,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11</Font>
              <Guid>39ad9c1cc83d4aa28ad7a01658cf5e02</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text65</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{POS充值合计}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="48" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text65>
            <Text66 Ref="49" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.1,5.8,11.9,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11</Font>
              <Guid>7c7f124db804449bb699031b2a0fd444</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text66</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="50" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text66>
            <Text60 Ref="51" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,8.5,1.8,1.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11</Font>
              <Guid>295732479b6140a4be3b15dfdb9fd373</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text60</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>财务结算</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text60>
            <Text68 Ref="52" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.8,8.5,1.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11</Font>
              <Guid>e150426441d1466ebc381a22b2b53e7f</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text68</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>医院支出</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="53" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text68>
            <Text69 Ref="54" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.8,9.2,1.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11</Font>
              <Guid>168c6467d907402cbed762717cd9796e</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text69</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>医院收入</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="55" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text69>
            <Text70 Ref="56" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.6,8.5,3.7,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11</Font>
              <Guid>b9af0b59f8984294a093946808fd55ca</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text70</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{医院支出}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="57" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text70>
            <Text71 Ref="58" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.6,9.2,3.7,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11</Font>
              <Guid>4620b0e3bec24347a5daf5ce06586da2</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text71</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{医院收入}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="59" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text71>
            <Text72 Ref="60" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.3,8.5,11.7,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11</Font>
              <Guid>eedbb4f8326547bc8e1672a5aca78d74</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text72</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>说明：医院支出=现金充值</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="61" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text72>
            <Text73 Ref="62" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.3,9.2,11.7,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11</Font>
              <Guid>535e2ae366994e50ac6c096bc2859bf4</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text73</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>说明：医院收入=实际消费+退回现金</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="63" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text73>
            <Text1 Ref="64" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13.8,3.7,1.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11</Font>
              <Guid>cf780bd511c24866ba7bdc73d296bbba</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text1</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>患者消费</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text1>
            <Text2 Ref="65" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15.6,3.7,1.6,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11</Font>
              <Guid>f5da17a32ed94ad89183eae7e2cd05cc</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text2</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>消费撤销</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text2>
            <Text3 Ref="66" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>17.2,3.7,1.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11</Font>
              <Guid>a6c6ffdbe98048bdb449789e2e6c2f68</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text3</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>实际消费</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text3>
            <Text4 Ref="67" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15.6,4.4,1.6,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11</Font>
              <Guid>c23d9887cfe44e6ba0e21310b3bdceb5</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text4</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{消费撤销}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text4>
            <Text5 Ref="68" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>17.2,4.4,1.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11</Font>
              <Guid>3e5152faf1d14834be05a7fea0dcc3da</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text5</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{实际消费}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text5>
            <Text6 Ref="69" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,7.8,1.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11</Font>
              <Guid>12a28eb8fa424d1d84e327c31ee1f103</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text6</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>财务日期</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="70" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text6>
            <Text7 Ref="71" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.8,7.8,17.2,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11</Font>
              <Guid>57c34baca4be4447a13c4aa04b33a594</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text7</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{财务日期}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="72" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text7>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>ReportTitleBand1</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
        </ReportTitleBand1>
        <ReportSummaryBand1 Ref="73" type="ReportSummaryBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,11.7,19,0.7</ClientRectangle>
          <Components isList="true" count="3">
            <Text74 Ref="74" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>10.8,0,4.2,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11</Font>
              <Guid>a5bc2f75e1744889a560e685c37eb0ab</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text74</Name>
              <Page isRef="2" />
              <Parent isRef="73" />
              <Text>财务签字：</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="75" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text74>
            <Text75 Ref="76" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,10.8,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11</Font>
              <Guid>940214c2c76043fe9b5c9f589489ea27</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text75</Name>
              <Page isRef="2" />
              <Parent isRef="73" />
              <Text>{结算单位}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="77" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text75>
            <Text76 Ref="78" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>15,0,4,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11</Font>
              <Guid>22aec535d4f54a61bec9ebc58b49679f</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text76</Name>
              <Page isRef="2" />
              <Parent isRef="73" />
              <Text>{经手人}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="79" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text76>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>ReportSummaryBand1</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
        </ReportSummaryBand1>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>87cdeeb6b6ce43e08a4b0995842414b9</Guid>
      <Margins>1,1,1,1</Margins>
      <Name>Page1</Name>
      <PageHeight>14.85</PageHeight>
      <PageWidth>21</PageWidth>
      <PaperSize>A4</PaperSize>
      <Report isRef="0" />
      <Watermark Ref="80" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page1>
  </Pages>
  <PrinterSettings Ref="81" type="Stimulsoft.Report.Print.StiPrinterSettings" isKey="true" />
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>Report</ReportAlias>
  <ReportChanged>9/8/2015 4:00:25 PM</ReportChanged>
  <ReportCreated>12/12/2014 11:43:51 AM</ReportCreated>
  <ReportFile>E:\正在进行时\唐山\正在修改版\his2010\his2010\Rpt\健康卡结账.mrt</ReportFile>
  <ReportGuid>10fa17e8bf0548d4ad52c86efd94cdb8</ReportGuid>
  <ReportName>Report</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2011.2.1026</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class Report : Stimulsoft.Report.StiReport
    {
        public Report()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
        #endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>