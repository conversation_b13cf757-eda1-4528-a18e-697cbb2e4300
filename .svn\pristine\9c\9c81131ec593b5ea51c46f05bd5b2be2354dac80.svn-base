﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="0" />
    <DataSources isList="true" count="0" />
    <Relations isList="true" count="0" />
    <Report isRef="0" />
    <Variables isList="true" count="50">
      <value>,姓名,姓名,System.String,,False,False</value>
      <value>,科别,科别,System.String,,False,False</value>
      <value>,经手人,经手人,System.String,,False,False</value>
      <value>,复核,复核,System.String,,False,False</value>
      <value>,结算,结算,System.String,,False,False</value>
      <value>,NO,NO,System.String,,False,False</value>
      <value>,医院名称,医院名称,System.String,,False,False</value>
      <value>,费用小写,费用小写,System.String,,False,False</value>
      <value>,住院押金,住院押金,System.String,,False,False</value>
      <value>,退找金额,退找金额,System.String,,False,False</value>
      <value>,大写,大写,System.String,,False,False</value>
      <value>,入院日期,入院日期,System.String,,False,False</value>
      <value>,出院日期,出院日期,System.String,,False,False</value>
      <value>,打印时间,打印时间,System.String,,False,False</value>
      <value>,住院天数,住院天数,System.String,,False,False</value>
      <value>,住院号,住院号,System.String,,False,False</value>
      <value>,床位费,床位费,System.Double,,False,False</value>
      <value>,取暖费,取暖费,System.Double,,False,False</value>
      <value>,西药费,西药费,System.Double,,False,False</value>
      <value>,中成药,中成药,System.Double,,False,False</value>
      <value>,中草药,中草药,System.Double,,False,False</value>
      <value>,治疗费,治疗费,System.Double,,False,False</value>
      <value>,手术费,手术费,System.Double,,False,False</value>
      <value>,诊查费,诊查费,System.Double,,False,False</value>
      <value>,放射费,放射费,System.Double,,False,False</value>
      <value>,护理费,护理费,System.Double,,False,False</value>
      <value>,化验费,化验费,System.Double,,False,False</value>
      <value>,处置费,处置费,System.Double,,False,False</value>
      <value>,输氧费,输氧费,System.Double,,False,False</value>
      <value>,输血费,输血费,System.Double,,False,False</value>
      <value>,检查费,检查费,System.Double,,False,False</value>
      <value>,接生费,接生费,System.Double,,False,False</value>
      <value>,麻醉费,麻醉费,System.Double,,False,False</value>
      <value>,个人账户支付,个人账户支付,System.Double,,False,False</value>
      <value>,统筹基金支付,统筹基金支付,System.Double,,False,False</value>
      <value>,公务员补基本医疗部分,公务员补基本医疗部分,System.Double,,False,False</value>
      <value>,公务员补助支付,公务员补助支付,System.Double,,False,False</value>
      <value>,保健对象补助支付,保健对象补助支付,System.Double,,False,False</value>
      <value>,大额保险支付,大额保险支付,System.Double,,False,False</value>
      <value>,离休统筹支付,离休统筹支付,System.String,,False,False</value>
      <value>,起付线,起付线,System.Double,,False,False</value>
      <value>,自付比例,自付比例,System.Double,,False,False</value>
      <value>,自理费用,自理费用,System.Double,,False,False</value>
      <value>,自费合计,自费合计,System.Double,,False,False</value>
      <value>,工伤保险支付,工伤保险支付,System.Double,,False,False</value>
      <value>,原个人账户余额,原个人账户余额,System.Double,,False,False</value>
      <value>,个人账户余额,个人账户余额,System.Double,,False,False</value>
      <value>,生育保险支付,生育保险支付,System.Double,,False,False</value>
      <value>,医疗账单号,医疗账单号,System.String,,False,False</value>
      <value>,社会保险号,社会保险号,System.String,,False,False</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page Ref="2" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="1">
        <ReportTitleBand1 Ref="3" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,24.2,10.2</ClientRectangle>
          <Components isList="true" count="110">
            <Text1 Ref="4" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.4,0.2,11.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,15,Bold,Point,False,0</Font>
              <Guid>06fda6c320f8412c9ac624f8c76cd6f2</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text1</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>吉 林 省 医 疗 机 构 住 院 收 费 专 用 票 据</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text1>
            <Text2 Ref="5" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.6,1.8,1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,11.25,Regular,Point,False,0</Font>
              <Guid>c287c4d02dfd46c197ae69d721fdc900</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text2</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>项目</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text2>
            <Text3 Ref="6" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.6,1.8,1.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,11.25,Regular,Point,False,0</Font>
              <Guid>81c48f8fafae401690c1b84393d366a6</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text3</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>床 位 费</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text3>
            <Text4 Ref="7" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.2,1.8,1.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,11.25,Regular,Point,False,0</Font>
              <Guid>79a7f2e4cffd48f0bd2d445517f65b50</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text4</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>取 暖 费</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text4>
            <Text5 Ref="8" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>5.8,1.8,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,11.25,Regular,Point,False,0</Font>
              <Guid>a3954541b48141c299ac727eeedc64ce</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text5</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>西 药 费</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text5>
            <Text6 Ref="9" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7.6,1.8,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,11.25,Regular,Point,False,0</Font>
              <Guid>dca462a4b96d4c21917fc93f5b740afc</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text6</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>中 成 药</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text6>
            <Text7 Ref="10" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>9.4,1.8,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,11.25,Regular,Point,False,0</Font>
              <Guid>623a9d54a8a542149ba28c54954bd111</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text7</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>中 草 药</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text7>
            <Text8 Ref="11" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>11.2,1.8,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,11.25,Regular,Point,False,0</Font>
              <Guid>d8edaa7030bb40dc8b3045d6c1af744c</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text8</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>检 查 费</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text8>
            <Text9 Ref="12" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>13,1.8,1.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,11.25,Regular,Point,False,0</Font>
              <Guid>3ad01aa07da7441b97a09aeeb4ce973f</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text9</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>治 疗 费</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text9>
            <Text10 Ref="13" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>14.6,1.8,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,11.25,Regular,Point,False,0</Font>
              <Guid>f1fcdb74042f4304a068445ea360f222</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text10</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>手 术 费</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text10>
            <Text11 Ref="14" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>16.4,1.8,1.4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,11.25,Regular,Point,False,0</Font>
              <Guid>cb474a22c997412da8e2692075a963a3</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text11</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>护 理 费</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text11>
            <Text12 Ref="15" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>17.8,1.8,4.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,11.25,Regular,Point,False,0</Font>
              <Guid>f88d8244dd9e4fd1b43057d3c8cd5791</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text12</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>1、  2、  3、  4、  </Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text12>
            <Text13 Ref="16" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.6,2.4,1,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,11.25,Regular,Point,False,0</Font>
              <Guid>f2a93fa1984e45d2abf0e85a8b3a1129</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text13</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>金额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text13>
            <Text15 Ref="17" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.2,2.4,1.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9.75,Regular,Point,False,0</Font>
              <Guid>18865fc9c1dd48b6870cbed9ba79fc2c</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text15</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{取暖费}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="18" type="CustomFormat" isKey="true">
                <StringFormat>#0.00</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text15>
            <Text16 Ref="19" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>5.8,2.4,1.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9.75,Regular,Point,False,0</Font>
              <Guid>ffe716c25afe46b48ce012105ca7bc80</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text16</Name>
              <NullValue>-</NullValue>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{西药费}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="20" type="CustomFormat" isKey="true">
                <StringFormat>#0.00</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text16>
            <Text17 Ref="21" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7.6,2.4,1.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9.75,Regular,Point,False,0</Font>
              <Guid>95162fb03c1f406fbab1cfc9953b4c4a</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text17</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{中成药}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="22" type="CustomFormat" isKey="true">
                <StringFormat>#0.00</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text17>
            <Text18 Ref="23" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>9.4,2.4,1.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9.75,Regular,Point,False,0</Font>
              <Guid>4fc32a8aa07047eda7403ed68519ce22</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text18</Name>
              <NullValue>-</NullValue>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{中草药}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="24" type="CustomFormat" isKey="true">
                <StringFormat>#0.00</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text18>
            <Text19 Ref="25" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>11.2,2.4,1.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9.75,Regular,Point,False,0</Font>
              <Guid>8f0d4d3bbf9e4f49a3e2dfa9ff6276f9</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text19</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{检查费}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="26" type="CustomFormat" isKey="true">
                <StringFormat>#0.00</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text19>
            <Text20 Ref="27" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>13,2.4,1.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9.75,Regular,Point,False,0</Font>
              <Guid>e0de149a70f848cc9c8293bf1568eedb</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text20</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{治疗费}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="28" type="CustomFormat" isKey="true">
                <StringFormat>#0.00</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text20>
            <Text21 Ref="29" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>14.6,2.4,1.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9.75,Regular,Point,False,0</Font>
              <Guid>5fb00d993638472491788a50d3d217a9</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text21</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{手术费}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="30" type="CustomFormat" isKey="true">
                <StringFormat>#0.00</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text21>
            <Text14 Ref="31" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.6,2.4,1.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9.75,Regular,Point,False,0</Font>
              <Guid>49ee72c426c044ce971c297113d2707d</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text14</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{床位费}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="32" type="CustomFormat" isKey="true">
                <StringFormat>#0.00</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text14>
            <Text22 Ref="33" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>16.4,2.4,1.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9.75,Regular,Point,False,0</Font>
              <Guid>c9bbb804aaad457da46721d58d482394</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text22</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{护理费}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="34" type="CustomFormat" isKey="true">
                <StringFormat>#0.00</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text22>
            <Text23 Ref="35" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>17.8,2.4,3.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,11.25,Regular,Point,False,0</Font>
              <Guid>072cde0dda0248528b2628cd9ece9265</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text23</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>个 人 账 户 支 付</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text23>
            <Text24 Ref="36" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>21,2.4,1.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,8</Font>
              <Guid>5966e43b268e4345b1bdd1db094fc466</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text24</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{个人账户支付}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text24>
            <Text25 Ref="37" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.6,3.2,1,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,11.25,Regular,Point,False,0</Font>
              <Guid>ecb2d086291d44aca56dfd862b3d45a0</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text25</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>项目</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text25>
            <Text26 Ref="38" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.6,3.2,1.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,11.25,Regular,Point,False,0</Font>
              <Guid>ad05aa64c78f4eee8c483b9ffb51e867</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text26</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>化 验 费</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text26>
            <Text27 Ref="39" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.2,3.2,1.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,11.25,Regular,Point,False,0</Font>
              <Guid>e99d74972dc240b2bf93a83d70dcb838</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text27</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>放 射 费</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text27>
            <Text28 Ref="40" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>5.8,3.2,1.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,11.25,Regular,Point,False,0</Font>
              <Guid>7ffd0ae620cf48b697dfaf8aed951899</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text28</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>处 置 费</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text28>
            <Text29 Ref="41" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7.6,3.2,1.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,11.25,Regular,Point,False,0</Font>
              <Guid>82280a97588d4b21936176f1490efcd4</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text29</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>输 氧 费</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text29>
            <Text30 Ref="42" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>9.4,3.2,1.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,11.25,Regular,Point,False,0</Font>
              <Guid>ba8b5cce7f7a47018f28ddfe2919690e</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text30</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>输 血 费</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text30>
            <Text31 Ref="43" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>11.2,3.2,1.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,11.25,Regular,Point,False,0</Font>
              <Guid>3c46e429be0d4270b22b3e418a13dabe</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text31</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>诊 查 费</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text31>
            <Text32 Ref="44" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>13,3.2,1.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,11.25,Regular,Point,False,0</Font>
              <Guid>95f09fb666d04949a594425ab8b84b6a</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text32</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>接 生 费</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text32>
            <Text33 Ref="45" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>14.6,3.2,1.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,11.25,Regular,Point,False,0</Font>
              <Guid>009567a9891948c9abdbc6a8675ed134</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text33</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>麻 醉 费</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text33>
            <Text34 Ref="46" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>16.4,3.2,1.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>ff30783defe5406598f76c34cba013aa</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text34</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <TextBrush>Black</TextBrush>
            </Text34>
            <Text35 Ref="47" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>17.8,3.2,3.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,11.25,Regular,Point,False,0</Font>
              <Guid>e65fc12bc3804299bb978098d72aadcb</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text35</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>统 筹 基 金 支 付</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text35>
            <Text36 Ref="48" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>21,3.2,1.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,8</Font>
              <Guid>ad6e688b69c64dddaf5aa3e868cd75d7</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text36</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{统筹基金支付}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text36>
            <Text37 Ref="49" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.6,4,1,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,11.25,Regular,Point,False,0</Font>
              <Guid>000a6e90622b49f49f790f1fff592c0c</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text37</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>金额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text37>
            <Text38 Ref="50" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.6,4,1.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9.75,Regular,Point,False,0</Font>
              <Guid>a07fbf5ad5ea4e38ab691822c0a8b123</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text38</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{化验费}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="51" type="CustomFormat" isKey="true">
                <StringFormat>#0.00</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text38>
            <Text39 Ref="52" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.2,4,1.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9.75,Regular,Point,False,0</Font>
              <Guid>9662d67708c8447a9421930089233344</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text39</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{放射费}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="53" type="CustomFormat" isKey="true">
                <StringFormat>#0.00</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text39>
            <Text40 Ref="54" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>5.8,4,1.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9.75,Regular,Point,False,0</Font>
              <Guid>5e36f0369ab54322aa432d27da4c6075</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text40</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{处置费}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="55" type="CustomFormat" isKey="true">
                <StringFormat>#0.00</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text40>
            <Text41 Ref="56" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7.6,4,1.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9.75,Regular,Point,False,0</Font>
              <Guid>3429f2526d3947ebb103136fec50edff</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text41</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{输氧费}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="57" type="CustomFormat" isKey="true">
                <StringFormat>#0.00</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text41>
            <Text42 Ref="58" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>9.4,4,1.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9.75,Regular,Point,False,0</Font>
              <Guid>34d3fc0549d4432b9754b9905a95d55d</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text42</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{输血费}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="59" type="CustomFormat" isKey="true">
                <StringFormat>#0.00</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text42>
            <Text43 Ref="60" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>11.2,4,1.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9.75,Regular,Point,False,0</Font>
              <Guid>64bc3bd80add41f69838b25016720e80</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text43</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{诊查费}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="61" type="CustomFormat" isKey="true">
                <StringFormat>#0.00</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text43>
            <Text44 Ref="62" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>13,4,1.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9.75,Regular,Point,False,0</Font>
              <Guid>4b3d60e9321c4c0da58652c1612cb143</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text44</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{接生费}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="63" type="CustomFormat" isKey="true">
                <StringFormat>#0.00</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text44>
            <Text45 Ref="64" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>14.6,4,1.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9.75,Regular,Point,False,0</Font>
              <Guid>9072d3f54efa4a6b806c9440cb1afdee</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text45</Name>
              <NullValue>-</NullValue>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{麻醉费}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="65" type="CustomFormat" isKey="true">
                <StringFormat>#0.00</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text45>
            <Text46 Ref="66" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>16.4,4,1.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>c067a1a59df749799c5f952fcce27af7</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text46</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <TextBrush>Black</TextBrush>
            </Text46>
            <Text47 Ref="67" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>17.8,4,3.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,7.5,Regular,Point,False,0</Font>
              <Guid>4d6d8f113a5c407eb885320fc2b67fe1</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text47</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>公务员补基本医疗部分</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text47>
            <Text48 Ref="68" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>21,4,1.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,8</Font>
              <Guid>a13dd74d290149628346839dac2cc1c4</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text48</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{公务员补基本医疗部分}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text48>
            <Text49 Ref="69" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.6,4.8,4.2,1.2</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,11.25,Regular,Point,False,0</Font>
              <Guid>6d9188a291484607948e89ca0f67b1be</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text49</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>  现金支付总额(大写):</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text49>
            <Text50 Ref="70" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>17.8,4.8,3.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,11.25,Regular,Point,False,0</Font>
              <Guid>d48909135ab54f4782837efcccc283e6</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text50</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>公务员补助支付</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text50>
            <Text51 Ref="71" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>5.8,4.8,7.2,1.2</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,12,Regular,Point,False,0</Font>
              <Guid>b38bf66b9c5f4cc69d6769450c780d4b</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text51</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{大写}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text51>
            <Text52 Ref="72" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>13,4.8,4.8,1.2</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9.75,Regular,Point,False,0</Font>
              <Guid>e8e9f7261db14d508b9bfb39e601bf7a</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text52</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{费用小写}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text52>
            <Text53 Ref="73" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>17.8,5.4,3.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,11.25,Regular,Point,False,0</Font>
              <Guid>8606198751254f0b9746ec48b24dfd42</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text53</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>保健对象补助支付</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text53>
            <Text54 Ref="74" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>21,4.8,1.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,8</Font>
              <Guid>69f8c9333ce049de81e505aa55dc25a7</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text54</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{公务员补助支付}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text54>
            <Text55 Ref="75" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>21,5.4,1.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,8</Font>
              <Guid>d487ecbb6f51485f8356b50d54f89d50</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text55</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{保健对象补助支付}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text55>
            <Text56 Ref="76" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.6,6,2.4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,11</Font>
              <Guid>b915166ccf4948b5970fd7d738ddf96e</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text56</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>入 院 时 间</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text56>
            <Text57 Ref="77" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4,6,5.4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9.75,Regular,Point,False,0</Font>
              <Guid>3088e6abe7d74e1bb62930e04b4193c5</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text57</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{入院日期}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="78" type="CustomFormat" isKey="true">
                <StringFormat>yyyy-MM-dd</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text57>
            <Text69 Ref="79" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>9.4,6,1.8,1.2</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,12,Regular,Point,False,0</Font>
              <Guid>f88dac2737b1430cb07512ce68dcba12</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text69</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>住 院
天 数</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text69>
            <Text70 Ref="80" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>11.2,6,1.8,1.2</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9,Regular,Point,False,0</Font>
              <Guid>d2c7ed0c2f954367bd2bb5fb7df34a71</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text70</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{住院天数}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text70>
            <Text71 Ref="81" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>13,6,2.4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,11.25,Regular,Point,False,0</Font>
              <Guid>99cc2be5e9fb49e9bfdf576f1a16f7a3</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text71</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>预交住院押金</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text71>
            <Text72 Ref="82" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>13,6.6,2.4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,11.25,Regular,Point,False,0</Font>
              <Guid>f5ad2f76eb0d4b20813aff0f0a08ad25</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text72</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>退补住院押金</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text72>
            <Text73 Ref="83" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>15.4,6,2.4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9.75,Regular,Point,False,0</Font>
              <Guid>9f94a7c5ed4548e594b14f32a17f99e2</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text73</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{住院押金}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text73>
            <Text74 Ref="84" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>15.4,6.6,2.4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9.75,Regular,Point,False,0</Font>
              <Guid>61009f90944d41718a02dd4ebfa1e7eb</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text74</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{退找金额}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text74>
            <Text75 Ref="85" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.6,6.6,2.4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,11</Font>
              <Guid>4c8c3e6af0024854b0ca883bcd1a5156</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text75</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>出 院 时 间</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text75>
            <Text76 Ref="86" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4,6.6,5.4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9.75,Regular,Point,False,0</Font>
              <Guid>4ed9fc95ce1b4a73a06d51c37a9dc01c</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text76</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{出院日期}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="87" type="CustomFormat" isKey="true">
                <StringFormat>yyyy-MM-dd</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text76>
            <Text86 Ref="88" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>17.8,6,3.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,11.25,Regular,Point,False,0</Font>
              <Guid>ad66f2d6fbbb4a36b134cbf44c48c9a3</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text86</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>大 额 保 险 支 付</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text86>
            <Text87 Ref="89" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>17.8,6.6,3.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,11.25,Regular,Point,False,0</Font>
              <Guid>f2682b5f804e46fc98c3d2a0d1f2e0f1</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text87</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>离 休 统 筹 支 付</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text87>
            <Text88 Ref="90" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>21,6,1.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,9,Regular,Point,False,0</Font>
              <Guid>02a5df32ab76416caebe7f7266c9b58c</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text88</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{大额保险支付}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text88>
            <Text89 Ref="91" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>21,6.6,1.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,9,Regular,Point,False,0</Font>
              <Guid>0132052926fb4b558d14a7b139d3214a</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text89</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{离休统筹支付}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text89>
            <Text90 Ref="92" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.6,7.2,1.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,11</Font>
              <Guid>a283d6d1984749e88652cd13bdfcc4fa</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text90</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>起付线</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text90>
            <Text91 Ref="93" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.8,7.2,1.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,9.75,Regular,Point,False,0</Font>
              <Guid>cb7b756f71754ec2bbc64e2ed23b8a80</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text91</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{起付线}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text91>
            <Text92 Ref="94" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4,7.2,1.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,11.25,Regular,Point,False,0</Font>
              <Guid>ede46b90f92f41128655769464cd159d</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text92</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>自付比例</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text92>
            <Text93 Ref="95" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>5.6,7.2,1.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,9,Regular,Point,False,0</Font>
              <Guid>03f089a680f945888847903a12825e8e</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text93</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{自付比例}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text93>
            <Text94 Ref="96" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7,7.2,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,11.25,Regular,Point,False,0</Font>
              <Guid>beaed8d37ff743f1aac44d0076100103</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text94</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>自 理 费 用</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text94>
            <Text95 Ref="97" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>9.4,7.2,3.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,9.75,Regular,Point,False,0</Font>
              <Guid>4da7293adf39438485e2150af4b7a31e</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text95</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{自理费用}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text95>
            <Text96 Ref="98" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>13,7.2,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,11.25,Regular,Point,False,0</Font>
              <Guid>936be3b190fc4327869d2eb7e34f48f0</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text96</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>自 费 合 计</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text96>
            <Text97 Ref="99" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>15.4,7.2,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,9,Regular,Point,False,0</Font>
              <Guid>2e558f20c1694fbcb11af8f632f38f57</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text97</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{自费合计}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text97>
            <Text98 Ref="100" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>17.8,7.2,3.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,11.25,Regular,Point,False,0</Font>
              <Guid>fd99ab8ed6d248cb8d02ca2c29bd236a</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text98</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>工 伤 保 险 支 付</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text98>
            <Text99 Ref="101" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>21,7.2,1.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,9,Regular,Point,False,0</Font>
              <Guid>c85a1523423842419a909e9e7f857594</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text99</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{工伤保险支付}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text99>
            <Text100 Ref="102" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.6,8,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,11.25,Regular,Point,False,134</Font>
              <Guid>d56fa2a32ac14d939d703aab49077577</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text100</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>住院费用总额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text100>
            <Text101 Ref="103" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4,8,3,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9,Regular,Point,False,0</Font>
              <Guid>3e6759c7eac64f19b4a462dbf4c15430</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text101</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{费用小写}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text101>
            <Text102 Ref="104" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7,8,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,9,Regular,Point,False,0</Font>
              <Guid>0208d6d0a8f141ca8901612365186b4a</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text102</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>原个人账户余额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text102>
            <Text103 Ref="105" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>9.4,8,3.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,9,Regular,Point,False,0</Font>
              <Guid>65f24b0749c44e75a8aacdc096df0cd9</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text103</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{原个人账户余额}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text103>
            <Text104 Ref="106" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>17.8,8,3.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,11.25,Regular,Point,False,0</Font>
              <Guid>7c7caac46fbe4348925f14d6a36af62b</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text104</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>生育保险支付</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text104>
            <Text105 Ref="107" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>21,8,1.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,9,Regular,Point,False,0</Font>
              <Guid>7a27ab289cc048df8e2f5aa478679a10</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text105</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{生育保险支付}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text105>
            <Text106 Ref="108" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>13,8,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,11.25,Regular,Point,False,0</Font>
              <Guid>7ae5eead17d2430f9023a032e11336a7</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text106</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>个人账户余额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text106>
            <Text107 Ref="109" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>15.4,8,2.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,8</Font>
              <Guid>db8f464139794365a2ecae5a3dba75df</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text107</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{个人账户余额}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text107>
            <Text108 Ref="110" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.6,9,1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,11.25,Regular,Point,False,0</Font>
              <Guid>76a719486c724708a7999052849f7d9d</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text108</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>制单:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text108>
            <Text109 Ref="111" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.6,9,2.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9,Regular,Point,False,0</Font>
              <Guid>9dd005f791a14ed2a25b1449c277bf6d</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text109</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{经手人}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text109>
            <Text110 Ref="112" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>5.4,9,1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,11.25,Regular,Point,False,0</Font>
              <Guid>83cdc2651d05408aaa0a16682c877486</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text110</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>收款:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text110>
            <Text111 Ref="113" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>6.4,9,2.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9,Regular,Point,False,0</Font>
              <Guid>82a41eeb7a93461bbaf7f1e1dc15d00e</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text111</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{经手人}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text111>
            <Text112 Ref="114" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>9.2,9,1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,11.25,Regular,Point,False,0</Font>
              <Guid>57042ad581c44ebcbcc94a208e41a7ba</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text112</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>复核:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text112>
            <Text113 Ref="115" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>10.2,9,2.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9.75,Regular,Point,False,0</Font>
              <Guid>e0a04c7139ca44fd8df22d5754929646</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text113</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{复核}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text113>
            <Text114 Ref="116" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>13,9,1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,11.25,Regular,Point,False,0</Font>
              <Guid>4c6e387a9d834beaae7ede8a4ca328e1</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text114</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>结算:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text114>
            <Text115 Ref="117" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>14,9,2.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9.75,Regular,Point,False,0</Font>
              <Guid>861a62cdac3d4cae9f5663a85393c85c</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text115</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{结算}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text115>
            <Text116 Ref="118" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>16.8,9,2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,11.25,Regular,Point,False,0</Font>
              <Guid>e4615f84b3a64b7daebfb088a5c558b7</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text116</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>医院名称:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text116>
            <Text117 Ref="119" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>18.8,9,3.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9.75,Regular,Point,False,0</Font>
              <Guid>98c7938668d549e5bfc5817ee32a43bb</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text117</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{医院名称}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text117>
            <Text118 Ref="120" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.6,1.2,0.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,9,Regular,Point,False,0</Font>
              <Guid>84b7469073f146d4b4399e9521d1ca65</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text118</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>姓名:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text118>
            <Text119 Ref="121" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.4,1.2,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>e6229c92a4a6430b939a31376f4a7069</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text119</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{姓名}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text119>
            <Text120 Ref="122" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.2,1.2,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,9,Regular,Point,False,0</Font>
              <Guid>4988ae1181de45a581a9ef4992548a19</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text120</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>医疗保单号:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text120>
            <Text121 Ref="123" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>6,1.2,1.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>2b08f49b8a1144c086c2f606e64bce7a</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text121</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text121>
            <Text122 Ref="124" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7.6,1.2,1.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,9,Regular,Point,False,0</Font>
              <Guid>3b76412aa05f42f5bac81bb364373a7c</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text122</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>社会保险号:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text122>
            <Text123 Ref="125" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>9.4,1.2,3,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9,Regular,Point,False,0</Font>
              <Guid>a20d05f34a164423a40f431f216eba5b</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text123</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{社会保险号}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text123>
            <Text124 Ref="126" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>12.4,1.2,2.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,9,Regular,Point,False,0</Font>
              <Guid>89c43c3b59b34c1fa0fb3087e488caf7</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text124</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>住院病志号:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text124>
            <Text125 Ref="127" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>14.6,1.2,2.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9,Regular,Point,False,0</Font>
              <Guid>55c26a3a352e441bb493d9a2b35b95d5</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text125</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{住院号}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text125>
            <Text126 Ref="128" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>16.8,1.2,1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,9,Regular,Point,False,0</Font>
              <Guid>5612ca423a0c4b56b750f02b046e02c4</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text126</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>科别:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text126>
            <Text127 Ref="129" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>17.8,1.2,1.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9,Regular,Point,False,0</Font>
              <Guid>518b6b01d5294aa2b399bcc84a5b3429</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text127</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{科别}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text127>
            <Text128 Ref="130" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>19.4,1.2,3.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9.75,Regular,Point,False,0</Font>
              <Guid>38bf9b5134834f8abe0bdc804ef5a096</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text128</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{打印时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text128>
            <Text134 Ref="131" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>17.2,0.4,1,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,11.25,Regular,Point,False,0</Font>
              <Guid>59b313b2e63441de89f0e040271c46e6</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text134</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>NO</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text134>
            <Text135 Ref="132" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>18.4,0.4,4,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,11.25,Regular,Point,False,0</Font>
              <Guid>4b7566f56a8c4d888f1a89512fc34601</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text135</Name>
              <NullValue> </NullValue>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{NO}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text135>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>ReportTitleBand1</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
        </ReportTitleBand1>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>e6d631df77314b11bc4573b4a814d428</Guid>
      <Margins>1,1,0,0</Margins>
      <Name>Page</Name>
      <PageHeight>12.6</PageHeight>
      <PageWidth>26.2</PageWidth>
      <Report isRef="0" />
      <Watermark Ref="133" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page>
  </Pages>
  <PrinterSettings Ref="134" type="Stimulsoft.Report.Print.StiPrinterSettings" isKey="true" />
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>吉林省医疗机构住院收费专用票据</ReportAlias>
  <ReportChanged>6/29/2012 3:19:35 PM</ReportChanged>
  <ReportCreated>5/28/2012 9:12:03 AM</ReportCreated>
  <ReportFile>D:\正在进行时\唐山\正在修改版\his2010\His2010\Rpt\吉林省医疗机构住院收费专用票据.mrt</ReportFile>
  <ReportGuid>53c9d5ac58634b71b4277d4d079f60e3</ReportGuid>
  <ReportName>吉林省医疗机构住院收费专用票据</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2011.2.1026</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class Report : Stimulsoft.Report.StiReport
    {
        public Report()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
        public double 床位费;
        public double 取暖费;
        public double 西药费;
        public double 中成药;
        public double 中草药;
        public double 治疗费;
        public double 手术费;
        public double 护理费;
        public double 个人账户支付;
        public double 统筹基金支付;
        public double 化验费;
        public double 处置费;
        public double 输氧费;
        public double 输血费;
        public double 检查费;
        public double 接生费;
        public double 麻醉费;
        public double 公务员补基本医疗部分;
        public double 现金支付总额_大写_;
        public double 公务员补助支付;
        public double 保健对象补助支付;
        public double 住院天数;
        public double 预交住院押金;
        public double 大额保险支付;
        public double 退补住院押金;
        public string 离休统筹支付;
        public double 起付线;
        public double 自付比例;
        public double 自理费用;
        public double 自费合计;
        public double 工伤保险支付;
        public double 住院总额;
        public double 原个人账户余额;
        public double 个人账户余额;
        public double 生育保险支付;
        public double 制单;
        public double 收款;
        public double 复核;
        public double 结算;
        public string 医院名称;
        public string 姓名;
        public double 医疗账单号;
        public double 社会保险号;
        public double 住院病志号;
        public double 科别;
        public double NO;
        public Stimulsoft.Report.Components.StiPage Page1;
        public Stimulsoft.Report.Components.StiHeaderBand HeaderBand1;
        public Stimulsoft.Report.Components.StiText Text1;
        public Stimulsoft.Report.Components.StiText Text2;
        public Stimulsoft.Report.Components.StiText Text3;
        public Stimulsoft.Report.Components.StiText Text4;
        public Stimulsoft.Report.Components.StiText Text5;
        public Stimulsoft.Report.Components.StiText Text6;
        public Stimulsoft.Report.Components.StiText Text7;
        public Stimulsoft.Report.Components.StiText Text8;
        public Stimulsoft.Report.Components.StiText Text9;
        public Stimulsoft.Report.Components.StiText Text10;
        public Stimulsoft.Report.Components.StiText Text11;
        public Stimulsoft.Report.Components.StiText Text12;
        public Stimulsoft.Report.Components.StiText Text13;
        public Stimulsoft.Report.Components.StiText Text15;
        public Stimulsoft.Report.Components.StiText Text16;
        public Stimulsoft.Report.Components.StiText Text17;
        public Stimulsoft.Report.Components.StiText Text18;
        public Stimulsoft.Report.Components.StiText Text19;
        public Stimulsoft.Report.Components.StiText Text20;
        public Stimulsoft.Report.Components.StiText Text21;
        public Stimulsoft.Report.Components.StiText Text14;
        public Stimulsoft.Report.Components.StiText Text22;
        public Stimulsoft.Report.Components.StiText Text23;
        public Stimulsoft.Report.Components.StiText Text24;
        public Stimulsoft.Report.Components.StiText Text25;
        public Stimulsoft.Report.Components.StiText Text26;
        public Stimulsoft.Report.Components.StiText Text27;
        public Stimulsoft.Report.Components.StiText Text28;
        public Stimulsoft.Report.Components.StiText Text29;
        public Stimulsoft.Report.Components.StiText Text30;
        public Stimulsoft.Report.Components.StiText Text31;
        public Stimulsoft.Report.Components.StiText Text32;
        public Stimulsoft.Report.Components.StiText Text33;
        public Stimulsoft.Report.Components.StiText Text34;
        public Stimulsoft.Report.Components.StiText Text35;
        public Stimulsoft.Report.Components.StiText Text36;
        public Stimulsoft.Report.Components.StiText Text37;
        public Stimulsoft.Report.Components.StiText Text38;
        public Stimulsoft.Report.Components.StiText Text39;
        public Stimulsoft.Report.Components.StiText Text40;
        public Stimulsoft.Report.Components.StiText Text41;
        public Stimulsoft.Report.Components.StiText Text42;
        public Stimulsoft.Report.Components.StiText Text43;
        public Stimulsoft.Report.Components.StiText Text44;
        public Stimulsoft.Report.Components.StiText Text45;
        public Stimulsoft.Report.Components.StiText Text46;
        public Stimulsoft.Report.Components.StiText Text47;
        public Stimulsoft.Report.Components.StiText Text48;
        public Stimulsoft.Report.Components.StiText Text49;
        public Stimulsoft.Report.Components.StiText Text50;
        public Stimulsoft.Report.Components.StiText Text51;
        public Stimulsoft.Report.Components.StiText Text52;
        public Stimulsoft.Report.Components.StiText Text53;
        public Stimulsoft.Report.Components.StiText Text54;
        public Stimulsoft.Report.Components.StiText Text55;
        public Stimulsoft.Report.Components.StiText Text56;
        public Stimulsoft.Report.Components.StiText Text57;
        public Stimulsoft.Report.Components.StiText Text69;
        public Stimulsoft.Report.Components.StiText Text70;
        public Stimulsoft.Report.Components.StiText Text71;
        public Stimulsoft.Report.Components.StiText Text72;
        public Stimulsoft.Report.Components.StiText Text73;
        public Stimulsoft.Report.Components.StiText Text74;
        public Stimulsoft.Report.Components.StiText Text75;
        public Stimulsoft.Report.Components.StiText Text76;
        public Stimulsoft.Report.Components.StiText Text86;
        public Stimulsoft.Report.Components.StiText Text87;
        public Stimulsoft.Report.Components.StiText Text88;
        public Stimulsoft.Report.Components.StiText Text89;
        public Stimulsoft.Report.Components.StiText Text90;
        public Stimulsoft.Report.Components.StiText Text91;
        public Stimulsoft.Report.Components.StiText Text92;
        public Stimulsoft.Report.Components.StiText Text93;
        public Stimulsoft.Report.Components.StiText Text94;
        public Stimulsoft.Report.Components.StiText Text95;
        public Stimulsoft.Report.Components.StiText Text96;
        public Stimulsoft.Report.Components.StiText Text97;
        public Stimulsoft.Report.Components.StiText Text98;
        public Stimulsoft.Report.Components.StiText Text99;
        public Stimulsoft.Report.Components.StiText Text100;
        public Stimulsoft.Report.Components.StiText Text101;
        public Stimulsoft.Report.Components.StiText Text102;
        public Stimulsoft.Report.Components.StiText Text103;
        public Stimulsoft.Report.Components.StiText Text104;
        public Stimulsoft.Report.Components.StiText Text105;
        public Stimulsoft.Report.Components.StiText Text106;
        public Stimulsoft.Report.Components.StiText Text107;
        public Stimulsoft.Report.Components.StiText Text108;
        public Stimulsoft.Report.Components.StiText Text109;
        public Stimulsoft.Report.Components.StiText Text110;
        public Stimulsoft.Report.Components.StiText Text111;
        public Stimulsoft.Report.Components.StiText Text112;
        public Stimulsoft.Report.Components.StiText Text113;
        public Stimulsoft.Report.Components.StiText Text114;
        public Stimulsoft.Report.Components.StiText Text115;
        public Stimulsoft.Report.Components.StiText Text116;
        public Stimulsoft.Report.Components.StiText Text117;
        public Stimulsoft.Report.Components.StiText Text118;
        public Stimulsoft.Report.Components.StiText Text119;
        public Stimulsoft.Report.Components.StiText Text120;
        public Stimulsoft.Report.Components.StiText Text121;
        public Stimulsoft.Report.Components.StiText Text122;
        public Stimulsoft.Report.Components.StiText Text123;
        public Stimulsoft.Report.Components.StiText Text124;
        public Stimulsoft.Report.Components.StiText Text125;
        public Stimulsoft.Report.Components.StiText Text126;
        public Stimulsoft.Report.Components.StiText Text127;
        public Stimulsoft.Report.Components.StiText Text128;
        public Stimulsoft.Report.Components.StiText Text134;
        public Stimulsoft.Report.Components.StiText Text135;
        public Stimulsoft.Report.Components.StiWatermark Page1_Watermark;
        public Stimulsoft.Report.Print.StiPrinterSettings 吉林省医疗机构住院收费专用票据_PrinterSettings;
        
        public virtual double 放射费
        {
            get
            {
                return 0d;
            }
        }
        
        public void Text1__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "吉 林 省 医 疗 机 构 住 院 收 费 专 用 票 据";
        }
        
        public void Text2__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "项目";
        }
        
        public void Text3__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "床 位 费";
        }
        
        public void Text4__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "取 暖 费";
        }
        
        public void Text5__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "西 药 费";
        }
        
        public void Text6__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "中 成 药";
        }
        
        public void Text7__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "中 草 药";
        }
        
        public void Text8__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "检 查 费";
        }
        
        public void Text9__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "治 疗 费";
        }
        
        public void Text10__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "手 术 费";
        }
        
        public void Text11__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "护 理 费";
        }
        
        public void Text12__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "1、  2、  3、  4、  ";
        }
        
        public void Text13__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "金额";
        }
        
        public void Text15__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 取暖费, true);
        }
        
        public void Text16__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 西药费, true);
        }
        
        public void Text17__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 中成药, true);
        }
        
        public void Text18__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 中草药, true);
        }
        
        public void Text19__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 检查费, true);
        }
        
        public void Text20__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 治疗费, true);
        }
        
        public void Text21__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 手术费, true);
        }
        
        public void Text14__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 床位费, true);
        }
        
        public void Text22__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 护理费, true);
        }
        
        public void Text23__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "个 人 账 户 支 付";
        }
        
        public void Text24__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 个人账户支付, true);
        }
        
        public void Text25__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "项目";
        }
        
        public void Text26__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "化 验 费";
        }
        
        public void Text27__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "放 射 费";
        }
        
        public void Text28__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "处 置 费";
        }
        
        public void Text29__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "输 氧 费";
        }
        
        public void Text30__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "输 血 费";
        }
        
        public void Text31__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "诊 查 费";
        }
        
        public void Text32__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "接 生 费";
        }
        
        public void Text33__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "麻 醉 费";
        }
        
        public void Text35__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "统 筹 基 金 支 付";
        }
        
        public void Text36__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 统筹基金支付, true);
        }
        
        public void Text37__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "金额";
        }
        
        public void Text38__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 化验费, true);
        }
        
        public void Text39__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 放射费, true);
        }
        
        public void Text40__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 处置费, true);
        }
        
        public void Text41__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 输氧费, true);
        }
        
        public void Text42__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 输血费, true);
        }
        
        public void Text43__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 诊查费, true);
        }
        
        public void Text44__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 接生费, true);
        }
        
        public void Text45__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 麻醉费, true);
        }
        
        public void Text47__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "公务员补基本医疗部分";
        }
        
        public void Text48__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 公务员补基本医疗部分, true);
        }
        
        public void Text49__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "  现金支付总额(大写):";
        }
        
        public void Text50__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "公务员补助支付";
        }
        
        public void Text51__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 现金支付总额(大写), true);
        }
        
        public void Text52__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "金额（小写）：" + ToString(sender, 金额(小写), true);
        }
        
        public void Text53__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "保健对象补助支付";
        }
        
        public void Text54__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 公务员补助支付, true);
        }
        
        public void Text55__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 保健对象补助支付, true);
        }
        
        public void Text56__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "入 院 时 间";
        }
        
        public void Text57__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 年, true);
        }
        
        public void Text69__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "住 院\r\n天 数";
        }
        
        public void Text70__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 住院天数, true);
        }
        
        public void Text71__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "预交住院押金";
        }
        
        public void Text72__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "退补住院押金";
        }
        
        public void Text73__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 预交住院押金, true);
        }
        
        public void Text74__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 退补住院押金, true);
        }
        
        public void Text75__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "出 院 时 间";
        }
        
        public void Text76__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 年, true);
        }
        
        public void Text86__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "大 额 保 险 支 付";
        }
        
        public void Text87__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "离 休 统 筹 支 付";
        }
        
        public void Text88__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 大额保险支付, true);
        }
        
        public void Text89__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 离休统筹支付, true);
        }
        
        public void Text90__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "起付线";
        }
        
        public void Text91__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 起付线, true);
        }
        
        public void Text92__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "自付比例";
        }
        
        public void Text93__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 自付比例, true);
        }
        
        public void Text94__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "自 理 费 用";
        }
        
        public void Text95__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 自理费用, true);
        }
        
        public void Text96__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "自 费 合 计";
        }
        
        public void Text97__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 自费合计, true);
        }
        
        public void Text98__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "工 伤 保 险 支 付";
        }
        
        public void Text99__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 工伤保险支付, true);
        }
        
        public void Text100__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "住院费用总额";
        }
        
        public void Text101__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 住院费用总额, true);
        }
        
        public void Text102__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "原个人账户余额";
        }
        
        public void Text103__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 原个人账户余额, true);
        }
        
        public void Text104__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "生育保险支付";
        }
        
        public void Text105__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 生育保险支付, true);
        }
        
        public void Text106__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "个人账户余额";
        }
        
        public void Text107__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 个人账户余额, true);
        }
        
        public void Text108__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "制单:";
        }
        
        public void Text109__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 制单, true);
        }
        
        public void Text110__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "收款:";
        }
        
        public void Text111__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 收款, true);
        }
        
        public void Text112__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "复核:";
        }
        
        public void Text113__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 复核, true);
        }
        
        public void Text114__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "结算:";
        }
        
        public void Text115__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 结算, true);
        }
        
        public void Text116__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "医院名称:";
        }
        
        public void Text117__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 医院名称, true);
        }
        
        public void Text118__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "姓名:";
        }
        
        public void Text119__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 姓名, true);
        }
        
        public void Text120__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "医疗保单号:";
        }
        
        public void Text121__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 医疗保单号, true);
        }
        
        public void Text122__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "社会保险号:";
        }
        
        public void Text123__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 社会保险号, true);
        }
        
        public void Text124__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "住院病志号:";
        }
        
        public void Text125__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 住院病志号, true);
        }
        
        public void Text126__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "科别:";
        }
        
        public void Text127__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 科别, true);
        }
        
        public void Text128__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, 年, true);
        }
        
        public void Text134__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = "NO";
        }
        
        public void Text135__GetValue(object sender, Stimulsoft.Report.Events.StiGetValueEventArgs e)
        {
            e.Value = ToString(sender, NO, true);
        }
        
        private void InitializeComponent()
        {
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "床位费", "床位费", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "取暖费", "取暖费", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "西药费", "西药费", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "中成药", "中成药", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "中草药", "中草药", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "治疗费", "治疗费", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "手术费", "手术费", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "护理费", "护理费", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "个人账户支付", "个人账户支付", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "统筹基金支付", "统筹基金支付", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "化验费", "化验费", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "放射费", "放射费", "", typeof(double), "", true, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "处置费", "处置费", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "输氧费", "输氧费", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "输血费", "输血费", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "检查费", "检查费", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "接生费", "接生费", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "麻醉费", "麻醉费", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "公务员补基本医疗部分", "公务员补基本医疗部分", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "现金支付总额(大写)", "现金支付总额(大写)", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "公务员补助支付", "公务员补助支付", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "保健对象补助支付", "保健对象补助支付", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "住院天数", "住院天数", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "预交住院押金", "预交住院押金", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "大额保险支付", "大额保险支付", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "退补住院押金", "退补住院押金", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "离休统筹支付", "离休统筹支付", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "起付线", "起付线", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "自付比例", "自付比例", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "自理费用", "自理费用", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "自费合计", "自费合计", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "工伤保险支付", "工伤保险支付", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "住院总额", "住院总额", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "原个人账户余额", "原个人账户余额", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "个人账户余额", "个人账户余额", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "生育保险支付", "生育保险支付", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "制单", "制单", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "收款", "收款", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "复核", "复核", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "结算", "结算", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "医院名称", "医院名称", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "姓名", "姓名", "", typeof(string), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "医疗账单号", "医疗账单号", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "社会保险号", "社会保险号", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "住院病志号", "住院病志号", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "科别", "科别", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.Dictionary.Variables.Add(new Stimulsoft.Report.Dictionary.StiVariable("", "NO", "NO", "", typeof(double), "", false, Stimulsoft.Report.Dictionary.StiVariableInitBy.Value, false));
            this.NeedsCompiling = false;
            // 
            // Variables init
            // 
            this.床位费 = 0d;
            this.取暖费 = 0d;
            this.西药费 = 0d;
            this.中成药 = 0d;
            this.中草药 = 0d;
            this.治疗费 = 0d;
            this.手术费 = 0d;
            this.护理费 = 0d;
            this.个人账户支付 = 0d;
            this.统筹基金支付 = 0d;
            this.化验费 = 0d;
            this.处置费 = 0d;
            this.输氧费 = 0d;
            this.输血费 = 0d;
            this.检查费 = 0d;
            this.接生费 = 0d;
            this.麻醉费 = 0d;
            this.公务员补基本医疗部分 = 0d;
            this.现金支付总额(大写) = 0d;
            this.公务员补助支付 = 0d;
            this.保健对象补助支付 = 0d;
            this.住院天数 = 0d;
            this.预交住院押金 = 0d;
            this.大额保险支付 = 0d;
            this.退补住院押金 = 0d;
            this.离休统筹支付 = "";
            this.起付线 = 0d;
            this.自付比例 = 0d;
            this.自理费用 = 0d;
            this.自费合计 = 0d;
            this.工伤保险支付 = 0d;
            this.住院总额 = 0d;
            this.原个人账户余额 = 0d;
            this.个人账户余额 = 0d;
            this.生育保险支付 = 0d;
            this.制单 = 0d;
            this.收款 = 0d;
            this.复核 = 0d;
            this.结算 = 0d;
            this.医院名称 = "";
            this.姓名 = "";
            this.医疗账单号 = 0d;
            this.社会保险号 = 0d;
            this.住院病志号 = 0d;
            this.科别 = 0d;
            this.NO = 0d;
            this.EngineVersion = Stimulsoft.Report.Engine.StiEngineVersion.EngineV2;
            this.ReferencedAssemblies = new System.String[] {
                    "System.Dll",
                    "System.Drawing.Dll",
                    "System.Windows.Forms.Dll",
                    "System.Data.Dll",
                    "System.Xml.Dll",
                    "Stimulsoft.Controls.Dll",
                    "Stimulsoft.Base.Dll",
                    "Stimulsoft.Report.Dll"};
            this.ReportAlias = "吉林省医疗机构住院收费专用票据";
            // 
            // ReportChanged
            // 
            this.ReportChanged = new DateTime(2012, 6, 18, 11, 26, 24, 0);
            // 
            // ReportCreated
            // 
            this.ReportCreated = new DateTime(2012, 5, 28, 9, 12, 3, 0);
            this.ReportFile = ".\\Rpt\\吉林省医疗机构住院收费专用票据.mrt";
            this.ReportGuid = "789f2d06144c4f2f9a61919774d14e93";
            this.ReportName = "吉林省医疗机构住院收费专用票据";
            this.ReportUnit = Stimulsoft.Report.StiReportUnitType.Centimeters;
            this.ReportVersion = "2011.2.1026";
            this.ScriptLanguage = Stimulsoft.Report.StiReportLanguageType.CSharp;
            // 
            // Page1
            // 
            this.Page1 = new Stimulsoft.Report.Components.StiPage();
            this.Page1.Guid = "e6d631df77314b11bc4573b4a814d428";
            this.Page1.Name = "Page1";
            this.Page1.PageHeight = 12.6;
            this.Page1.PageWidth = 26.2;
            this.Page1.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 2, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Page1.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            // 
            // HeaderBand1
            // 
            this.HeaderBand1 = new Stimulsoft.Report.Components.StiHeaderBand();
            this.HeaderBand1.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(0, 0.4, 24.2, 10.2);
            this.HeaderBand1.Name = "HeaderBand1";
            this.HeaderBand1.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.HeaderBand1.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            // 
            // Text1
            // 
            this.Text1 = new Stimulsoft.Report.Components.StiText();
            this.Text1.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(4.4, 0.2, 11.8, 0.6);
            this.Text1.Enabled = false;
            this.Text1.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text1.Name = "Text1";
            this.Text1.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text1__GetValue);
            this.Text1.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text1.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text1.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text1.Font = new System.Drawing.Font("Arial", 15F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
            this.Text1.Guid = null;
            this.Text1.Indicator = null;
            this.Text1.Interaction = null;
            this.Text1.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text1.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text1.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text2
            // 
            this.Text2 = new Stimulsoft.Report.Components.StiText();
            this.Text2.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(1.6, 1.8, 0.9, 0.55);
            this.Text2.Enabled = false;
            this.Text2.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text2.Name = "Text2";
            this.Text2.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text2__GetValue);
            this.Text2.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text2.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text2.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text2.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text2.Font = new System.Drawing.Font("Arial", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text2.Guid = null;
            this.Text2.Indicator = null;
            this.Text2.Interaction = null;
            this.Text2.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text2.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text2.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text3
            // 
            this.Text3 = new Stimulsoft.Report.Components.StiText();
            this.Text3.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(2.5, 1.8, 1.7, 0.55);
            this.Text3.Enabled = false;
            this.Text3.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text3.Name = "Text3";
            this.Text3.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text3__GetValue);
            this.Text3.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text3.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text3.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text3.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text3.Font = new System.Drawing.Font("Arial", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text3.Guid = null;
            this.Text3.Indicator = null;
            this.Text3.Interaction = null;
            this.Text3.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text3.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text3.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text4
            // 
            this.Text4 = new Stimulsoft.Report.Components.StiText();
            this.Text4.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(4.2, 1.8, 1.7, 0.55);
            this.Text4.Enabled = false;
            this.Text4.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text4.Name = "Text4";
            this.Text4.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text4__GetValue);
            this.Text4.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text4.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text4.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text4.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text4.Font = new System.Drawing.Font("Arial", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text4.Guid = null;
            this.Text4.Indicator = null;
            this.Text4.Interaction = null;
            this.Text4.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text4.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text4.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text5
            // 
            this.Text5 = new Stimulsoft.Report.Components.StiText();
            this.Text5.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(5.9, 1.8, 1.75, 0.55);
            this.Text5.Enabled = false;
            this.Text5.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text5.Name = "Text5";
            this.Text5.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text5__GetValue);
            this.Text5.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text5.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text5.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text5.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text5.Font = new System.Drawing.Font("Arial", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text5.Guid = null;
            this.Text5.Indicator = null;
            this.Text5.Interaction = null;
            this.Text5.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text5.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text5.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text6
            // 
            this.Text6 = new Stimulsoft.Report.Components.StiText();
            this.Text6.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(7.65, 1.8, 1.75, 0.55);
            this.Text6.Enabled = false;
            this.Text6.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text6.Name = "Text6";
            this.Text6.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text6__GetValue);
            this.Text6.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text6.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text6.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text6.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text6.Font = new System.Drawing.Font("Arial", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text6.Guid = null;
            this.Text6.Indicator = null;
            this.Text6.Interaction = null;
            this.Text6.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text6.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text6.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text7
            // 
            this.Text7 = new Stimulsoft.Report.Components.StiText();
            this.Text7.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(9.4, 1.8, 1.75, 0.55);
            this.Text7.Enabled = false;
            this.Text7.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text7.Name = "Text7";
            this.Text7.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text7__GetValue);
            this.Text7.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text7.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text7.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text7.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text7.Font = new System.Drawing.Font("Arial", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text7.Guid = null;
            this.Text7.Indicator = null;
            this.Text7.Interaction = null;
            this.Text7.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text7.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text7.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text8
            // 
            this.Text8 = new Stimulsoft.Report.Components.StiText();
            this.Text8.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(11.15, 1.8, 1.75, 0.55);
            this.Text8.Enabled = false;
            this.Text8.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text8.Name = "Text8";
            this.Text8.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text8__GetValue);
            this.Text8.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text8.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text8.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text8.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text8.Font = new System.Drawing.Font("Arial", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text8.Guid = null;
            this.Text8.Indicator = null;
            this.Text8.Interaction = null;
            this.Text8.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text8.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text8.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text9
            // 
            this.Text9 = new Stimulsoft.Report.Components.StiText();
            this.Text9.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(12.9, 1.8, 1.8, 0.55);
            this.Text9.Enabled = false;
            this.Text9.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text9.Name = "Text9";
            this.Text9.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text9__GetValue);
            this.Text9.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text9.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text9.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text9.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text9.Font = new System.Drawing.Font("Arial", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text9.Guid = null;
            this.Text9.Indicator = null;
            this.Text9.Interaction = null;
            this.Text9.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text9.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text9.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text10
            // 
            this.Text10 = new Stimulsoft.Report.Components.StiText();
            this.Text10.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(14.7, 1.8, 1.7, 0.55);
            this.Text10.Enabled = false;
            this.Text10.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text10.Name = "Text10";
            this.Text10.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text10__GetValue);
            this.Text10.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text10.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text10.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text10.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text10.Font = new System.Drawing.Font("Arial", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text10.Guid = null;
            this.Text10.Indicator = null;
            this.Text10.Interaction = null;
            this.Text10.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text10.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text10.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text11
            // 
            this.Text11 = new Stimulsoft.Report.Components.StiText();
            this.Text11.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(16.4, 1.8, 1.6, 0.55);
            this.Text11.Enabled = false;
            this.Text11.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text11.Name = "Text11";
            this.Text11.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text11__GetValue);
            this.Text11.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text11.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text11.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text11.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text11.Font = new System.Drawing.Font("Arial", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text11.Guid = null;
            this.Text11.Indicator = null;
            this.Text11.Interaction = null;
            this.Text11.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text11.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text11.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text12
            // 
            this.Text12 = new Stimulsoft.Report.Components.StiText();
            this.Text12.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(18, 1.8, 4.5, 0.55);
            this.Text12.Enabled = false;
            this.Text12.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text12.Name = "Text12";
            this.Text12.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text12__GetValue);
            this.Text12.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text12.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text12.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text12.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text12.Font = new System.Drawing.Font("Arial", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text12.Guid = null;
            this.Text12.Indicator = null;
            this.Text12.Interaction = null;
            this.Text12.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text12.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text12.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text13
            // 
            this.Text13 = new Stimulsoft.Report.Components.StiText();
            this.Text13.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(1.6, 2.35, 0.9, 0.8);
            this.Text13.Enabled = false;
            this.Text13.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text13.Name = "Text13";
            this.Text13.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text13__GetValue);
            this.Text13.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text13.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text13.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text13.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text13.Font = new System.Drawing.Font("Arial", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text13.Guid = null;
            this.Text13.Indicator = null;
            this.Text13.Interaction = null;
            this.Text13.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text13.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text13.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text15
            // 
            this.Text15 = new Stimulsoft.Report.Components.StiText();
            this.Text15.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(4.2, 2.35, 1.7, 0.8);
            this.Text15.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text15.Name = "Text15";
            this.Text15.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text15__GetValue);
            this.Text15.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text15.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text15.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text15.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text15.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text15.Guid = null;
            this.Text15.Indicator = null;
            this.Text15.Interaction = null;
            this.Text15.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text15.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text15.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text16
            // 
            this.Text16 = new Stimulsoft.Report.Components.StiText();
            this.Text16.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(5.9, 2.35, 1.75, 0.8);
            this.Text16.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text16.Name = "Text16";
            this.Text16.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text16__GetValue);
            this.Text16.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text16.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text16.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text16.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text16.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text16.Guid = null;
            this.Text16.Indicator = null;
            this.Text16.Interaction = null;
            this.Text16.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text16.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text16.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text17
            // 
            this.Text17 = new Stimulsoft.Report.Components.StiText();
            this.Text17.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(7.65, 2.35, 1.75, 0.8);
            this.Text17.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text17.Name = "Text17";
            this.Text17.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text17__GetValue);
            this.Text17.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text17.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text17.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text17.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text17.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text17.Guid = null;
            this.Text17.Indicator = null;
            this.Text17.Interaction = null;
            this.Text17.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text17.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text17.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text18
            // 
            this.Text18 = new Stimulsoft.Report.Components.StiText();
            this.Text18.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(9.4, 2.35, 1.75, 0.8);
            this.Text18.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text18.Name = "Text18";
            this.Text18.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text18__GetValue);
            this.Text18.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text18.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text18.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text18.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text18.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text18.Guid = null;
            this.Text18.Indicator = null;
            this.Text18.Interaction = null;
            this.Text18.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text18.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text18.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text19
            // 
            this.Text19 = new Stimulsoft.Report.Components.StiText();
            this.Text19.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(11.15, 2.35, 1.75, 0.8);
            this.Text19.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text19.Name = "Text19";
            this.Text19.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text19__GetValue);
            this.Text19.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text19.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text19.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text19.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text19.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text19.Guid = null;
            this.Text19.Indicator = null;
            this.Text19.Interaction = null;
            this.Text19.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text19.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text19.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text20
            // 
            this.Text20 = new Stimulsoft.Report.Components.StiText();
            this.Text20.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(12.9, 2.35, 1.8, 0.8);
            this.Text20.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text20.Name = "Text20";
            this.Text20.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text20__GetValue);
            this.Text20.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text20.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text20.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text20.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text20.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text20.Guid = null;
            this.Text20.Indicator = null;
            this.Text20.Interaction = null;
            this.Text20.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text20.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text20.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text21
            // 
            this.Text21 = new Stimulsoft.Report.Components.StiText();
            this.Text21.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(14.7, 2.35, 1.7, 0.8);
            this.Text21.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text21.Name = "Text21";
            this.Text21.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text21__GetValue);
            this.Text21.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text21.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text21.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text21.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text21.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text21.Guid = null;
            this.Text21.Indicator = null;
            this.Text21.Interaction = null;
            this.Text21.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text21.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text21.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text14
            // 
            this.Text14 = new Stimulsoft.Report.Components.StiText();
            this.Text14.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(2.5, 2.35, 1.7, 0.8);
            this.Text14.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text14.Name = "Text14";
            this.Text14.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text14__GetValue);
            this.Text14.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text14.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text14.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text14.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text14.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text14.Guid = null;
            this.Text14.Indicator = null;
            this.Text14.Interaction = null;
            this.Text14.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text14.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text14.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text22
            // 
            this.Text22 = new Stimulsoft.Report.Components.StiText();
            this.Text22.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(16.4, 2.35, 1.6, 0.8);
            this.Text22.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text22.Name = "Text22";
            this.Text22.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text22__GetValue);
            this.Text22.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text22.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text22.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text22.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text22.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text22.Guid = null;
            this.Text22.Indicator = null;
            this.Text22.Interaction = null;
            this.Text22.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text22.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text22.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text23
            // 
            this.Text23 = new Stimulsoft.Report.Components.StiText();
            this.Text23.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(18, 2.35, 3.1, 0.8);
            this.Text23.Enabled = false;
            this.Text23.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text23.Name = "Text23";
            this.Text23.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text23__GetValue);
            this.Text23.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text23.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text23.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text23.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text23.Font = new System.Drawing.Font("Arial", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text23.Guid = null;
            this.Text23.Indicator = null;
            this.Text23.Interaction = null;
            this.Text23.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text23.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text23.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text24
            // 
            this.Text24 = new Stimulsoft.Report.Components.StiText();
            this.Text24.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(21.1, 2.35, 1.4, 0.8);
            this.Text24.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text24.Name = "Text24";
            this.Text24.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text24__GetValue);
            this.Text24.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text24.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text24.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text24.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text24.Font = new System.Drawing.Font("Arial", 8F);
            this.Text24.Guid = null;
            this.Text24.Indicator = null;
            this.Text24.Interaction = null;
            this.Text24.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text24.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text24.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text25
            // 
            this.Text25 = new Stimulsoft.Report.Components.StiText();
            this.Text25.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(1.6, 3.15, 0.9, 0.8);
            this.Text25.Enabled = false;
            this.Text25.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text25.Name = "Text25";
            this.Text25.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text25__GetValue);
            this.Text25.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text25.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text25.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text25.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text25.Font = new System.Drawing.Font("Arial", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text25.Guid = null;
            this.Text25.Indicator = null;
            this.Text25.Interaction = null;
            this.Text25.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text25.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text25.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text26
            // 
            this.Text26 = new Stimulsoft.Report.Components.StiText();
            this.Text26.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(2.5, 3.15, 1.7, 0.8);
            this.Text26.Enabled = false;
            this.Text26.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text26.Name = "Text26";
            this.Text26.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text26__GetValue);
            this.Text26.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text26.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text26.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text26.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text26.Font = new System.Drawing.Font("Arial", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text26.Guid = null;
            this.Text26.Indicator = null;
            this.Text26.Interaction = null;
            this.Text26.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text26.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text26.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text27
            // 
            this.Text27 = new Stimulsoft.Report.Components.StiText();
            this.Text27.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(4.2, 3.15, 1.7, 0.8);
            this.Text27.Enabled = false;
            this.Text27.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text27.Name = "Text27";
            this.Text27.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text27__GetValue);
            this.Text27.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text27.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text27.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text27.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text27.Font = new System.Drawing.Font("Arial", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text27.Guid = null;
            this.Text27.Indicator = null;
            this.Text27.Interaction = null;
            this.Text27.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text27.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text27.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text28
            // 
            this.Text28 = new Stimulsoft.Report.Components.StiText();
            this.Text28.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(5.9, 3.15, 1.75, 0.8);
            this.Text28.Enabled = false;
            this.Text28.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text28.Name = "Text28";
            this.Text28.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text28__GetValue);
            this.Text28.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text28.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text28.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text28.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text28.Font = new System.Drawing.Font("Arial", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text28.Guid = null;
            this.Text28.Indicator = null;
            this.Text28.Interaction = null;
            this.Text28.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text28.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text28.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text29
            // 
            this.Text29 = new Stimulsoft.Report.Components.StiText();
            this.Text29.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(7.65, 3.15, 1.75, 0.8);
            this.Text29.Enabled = false;
            this.Text29.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text29.Name = "Text29";
            this.Text29.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text29__GetValue);
            this.Text29.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text29.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text29.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text29.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text29.Font = new System.Drawing.Font("Arial", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text29.Guid = null;
            this.Text29.Indicator = null;
            this.Text29.Interaction = null;
            this.Text29.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text29.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text29.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text30
            // 
            this.Text30 = new Stimulsoft.Report.Components.StiText();
            this.Text30.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(9.4, 3.15, 1.75, 0.8);
            this.Text30.Enabled = false;
            this.Text30.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text30.Name = "Text30";
            this.Text30.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text30__GetValue);
            this.Text30.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text30.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text30.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text30.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text30.Font = new System.Drawing.Font("Arial", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text30.Guid = null;
            this.Text30.Indicator = null;
            this.Text30.Interaction = null;
            this.Text30.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text30.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text30.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text31
            // 
            this.Text31 = new Stimulsoft.Report.Components.StiText();
            this.Text31.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(11.15, 3.15, 1.75, 0.8);
            this.Text31.Enabled = false;
            this.Text31.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text31.Name = "Text31";
            this.Text31.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text31__GetValue);
            this.Text31.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text31.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text31.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text31.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text31.Font = new System.Drawing.Font("Arial", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text31.Guid = null;
            this.Text31.Indicator = null;
            this.Text31.Interaction = null;
            this.Text31.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text31.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text31.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text32
            // 
            this.Text32 = new Stimulsoft.Report.Components.StiText();
            this.Text32.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(12.9, 3.15, 1.8, 0.8);
            this.Text32.Enabled = false;
            this.Text32.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text32.Name = "Text32";
            this.Text32.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text32__GetValue);
            this.Text32.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text32.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text32.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text32.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text32.Font = new System.Drawing.Font("Arial", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text32.Guid = null;
            this.Text32.Indicator = null;
            this.Text32.Interaction = null;
            this.Text32.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text32.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text32.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text33
            // 
            this.Text33 = new Stimulsoft.Report.Components.StiText();
            this.Text33.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(14.7, 3.15, 1.7, 0.8);
            this.Text33.Enabled = false;
            this.Text33.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text33.Name = "Text33";
            this.Text33.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text33__GetValue);
            this.Text33.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text33.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text33.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text33.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text33.Font = new System.Drawing.Font("Arial", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text33.Guid = null;
            this.Text33.Indicator = null;
            this.Text33.Interaction = null;
            this.Text33.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text33.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text33.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text34
            // 
            this.Text34 = new Stimulsoft.Report.Components.StiText();
            this.Text34.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(16.4, 3.15, 1.6, 0.8);
            this.Text34.Name = "Text34";
            this.Text34.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text34.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text34.Font = new System.Drawing.Font("Arial", 8F);
            this.Text34.Guid = null;
            this.Text34.Indicator = null;
            this.Text34.Interaction = null;
            this.Text34.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text34.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text34.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text35
            // 
            this.Text35 = new Stimulsoft.Report.Components.StiText();
            this.Text35.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(18, 3.15, 3.1, 0.8);
            this.Text35.Enabled = false;
            this.Text35.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text35.Name = "Text35";
            this.Text35.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text35__GetValue);
            this.Text35.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text35.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text35.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text35.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text35.Font = new System.Drawing.Font("Arial", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text35.Guid = null;
            this.Text35.Indicator = null;
            this.Text35.Interaction = null;
            this.Text35.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text35.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text35.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text36
            // 
            this.Text36 = new Stimulsoft.Report.Components.StiText();
            this.Text36.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(21.1, 3.15, 1.4, 0.8);
            this.Text36.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text36.Name = "Text36";
            this.Text36.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text36__GetValue);
            this.Text36.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text36.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text36.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text36.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text36.Font = new System.Drawing.Font("Arial", 8F);
            this.Text36.Guid = null;
            this.Text36.Indicator = null;
            this.Text36.Interaction = null;
            this.Text36.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text36.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text36.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text37
            // 
            this.Text37 = new Stimulsoft.Report.Components.StiText();
            this.Text37.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(1.6, 3.95, 0.9, 0.8);
            this.Text37.Enabled = false;
            this.Text37.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text37.Name = "Text37";
            this.Text37.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text37__GetValue);
            this.Text37.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text37.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text37.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text37.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text37.Font = new System.Drawing.Font("Arial", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text37.Guid = null;
            this.Text37.Indicator = null;
            this.Text37.Interaction = null;
            this.Text37.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text37.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text37.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text38
            // 
            this.Text38 = new Stimulsoft.Report.Components.StiText();
            this.Text38.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(2.5, 3.95, 1.7, 0.8);
            this.Text38.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text38.Name = "Text38";
            this.Text38.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text38__GetValue);
            this.Text38.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text38.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text38.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text38.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text38.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text38.Guid = null;
            this.Text38.Indicator = null;
            this.Text38.Interaction = null;
            this.Text38.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text38.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text38.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text39
            // 
            this.Text39 = new Stimulsoft.Report.Components.StiText();
            this.Text39.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(4.2, 3.95, 1.7, 0.8);
            this.Text39.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text39.Name = "Text39";
            this.Text39.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text39__GetValue);
            this.Text39.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text39.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text39.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text39.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text39.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text39.Guid = null;
            this.Text39.Indicator = null;
            this.Text39.Interaction = null;
            this.Text39.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text39.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text39.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text40
            // 
            this.Text40 = new Stimulsoft.Report.Components.StiText();
            this.Text40.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(5.9, 3.95, 1.75, 0.8);
            this.Text40.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text40.Name = "Text40";
            this.Text40.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text40__GetValue);
            this.Text40.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text40.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text40.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text40.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text40.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text40.Guid = null;
            this.Text40.Indicator = null;
            this.Text40.Interaction = null;
            this.Text40.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text40.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text40.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text41
            // 
            this.Text41 = new Stimulsoft.Report.Components.StiText();
            this.Text41.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(7.65, 3.95, 1.75, 0.8);
            this.Text41.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text41.Name = "Text41";
            this.Text41.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text41__GetValue);
            this.Text41.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text41.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text41.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text41.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text41.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text41.Guid = null;
            this.Text41.Indicator = null;
            this.Text41.Interaction = null;
            this.Text41.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text41.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text41.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text42
            // 
            this.Text42 = new Stimulsoft.Report.Components.StiText();
            this.Text42.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(9.4, 3.95, 1.75, 0.8);
            this.Text42.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text42.Name = "Text42";
            this.Text42.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text42__GetValue);
            this.Text42.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text42.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text42.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text42.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text42.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text42.Guid = null;
            this.Text42.Indicator = null;
            this.Text42.Interaction = null;
            this.Text42.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text42.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text42.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text43
            // 
            this.Text43 = new Stimulsoft.Report.Components.StiText();
            this.Text43.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(11.15, 3.95, 1.75, 0.8);
            this.Text43.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text43.Name = "Text43";
            this.Text43.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text43__GetValue);
            this.Text43.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text43.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text43.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text43.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text43.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text43.Guid = null;
            this.Text43.Indicator = null;
            this.Text43.Interaction = null;
            this.Text43.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text43.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text43.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text44
            // 
            this.Text44 = new Stimulsoft.Report.Components.StiText();
            this.Text44.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(12.9, 3.95, 1.8, 0.8);
            this.Text44.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text44.Name = "Text44";
            this.Text44.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text44__GetValue);
            this.Text44.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text44.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text44.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text44.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text44.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text44.Guid = null;
            this.Text44.Indicator = null;
            this.Text44.Interaction = null;
            this.Text44.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text44.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text44.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text45
            // 
            this.Text45 = new Stimulsoft.Report.Components.StiText();
            this.Text45.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(14.7, 3.95, 1.7, 0.8);
            this.Text45.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text45.Name = "Text45";
            this.Text45.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text45__GetValue);
            this.Text45.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text45.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text45.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text45.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.InitializeComponent2();
        }
        
        public void InitializeComponent2()
        {
            this.Text45.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text45.Guid = null;
            this.Text45.Indicator = null;
            this.Text45.Interaction = null;
            this.Text45.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text45.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text45.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text46
            // 
            this.Text46 = new Stimulsoft.Report.Components.StiText();
            this.Text46.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(16.4, 3.95, 1.6, 0.8);
            this.Text46.Name = "Text46";
            this.Text46.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text46.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text46.Font = new System.Drawing.Font("Arial", 8F);
            this.Text46.Guid = null;
            this.Text46.Indicator = null;
            this.Text46.Interaction = null;
            this.Text46.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text46.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text46.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text47
            // 
            this.Text47 = new Stimulsoft.Report.Components.StiText();
            this.Text47.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(18, 3.95, 3.1, 0.8);
            this.Text47.Enabled = false;
            this.Text47.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text47.Name = "Text47";
            this.Text47.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text47__GetValue);
            this.Text47.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text47.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text47.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text47.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text47.Font = new System.Drawing.Font("Arial", 7.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text47.Guid = null;
            this.Text47.Indicator = null;
            this.Text47.Interaction = null;
            this.Text47.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text47.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text47.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text48
            // 
            this.Text48 = new Stimulsoft.Report.Components.StiText();
            this.Text48.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(21.1, 3.95, 1.4, 0.8);
            this.Text48.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text48.Name = "Text48";
            this.Text48.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text48__GetValue);
            this.Text48.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text48.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text48.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text48.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text48.Font = new System.Drawing.Font("Arial", 8F);
            this.Text48.Guid = null;
            this.Text48.Indicator = null;
            this.Text48.Interaction = null;
            this.Text48.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text48.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text48.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text49
            // 
            this.Text49 = new Stimulsoft.Report.Components.StiText();
            this.Text49.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(1.6, 4.75, 4.3, 1.2);
            this.Text49.Enabled = false;
            this.Text49.Name = "Text49";
            this.Text49.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text49__GetValue);
            this.Text49.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text49.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Bottom;
            this.Text49.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text49.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text49.Font = new System.Drawing.Font("Arial", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text49.Guid = null;
            this.Text49.Indicator = null;
            this.Text49.Interaction = null;
            this.Text49.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text49.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text49.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text50
            // 
            this.Text50 = new Stimulsoft.Report.Components.StiText();
            this.Text50.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(18, 4.75, 3.1, 0.6);
            this.Text50.Enabled = false;
            this.Text50.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text50.Name = "Text50";
            this.Text50.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text50__GetValue);
            this.Text50.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text50.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text50.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text50.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text50.Font = new System.Drawing.Font("Arial", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text50.Guid = null;
            this.Text50.Indicator = null;
            this.Text50.Interaction = null;
            this.Text50.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text50.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text50.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text51
            // 
            this.Text51 = new Stimulsoft.Report.Components.StiText();
            this.Text51.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(5.9, 4.75, 7, 1.2);
            this.Text51.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text51.Name = "Text51";
            this.Text51.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text51__GetValue);
            this.Text51.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text51.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text51.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text51.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text51.Font = new System.Drawing.Font("Arial", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text51.Guid = null;
            this.Text51.Indicator = null;
            this.Text51.Interaction = null;
            this.Text51.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text51.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text51.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text52
            // 
            this.Text52 = new Stimulsoft.Report.Components.StiText();
            this.Text52.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(12.9, 4.75, 5.1, 1.2);
            this.Text52.Name = "Text52";
            this.Text52.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text52__GetValue);
            this.Text52.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text52.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Bottom;
            this.Text52.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text52.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text52.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text52.Guid = null;
            this.Text52.Indicator = null;
            this.Text52.Interaction = null;
            this.Text52.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text52.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text52.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text53
            // 
            this.Text53 = new Stimulsoft.Report.Components.StiText();
            this.Text53.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(18, 5.35, 3.1, 0.6);
            this.Text53.Enabled = false;
            this.Text53.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text53.Name = "Text53";
            this.Text53.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text53__GetValue);
            this.Text53.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text53.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text53.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text53.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text53.Font = new System.Drawing.Font("Arial", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text53.Guid = null;
            this.Text53.Indicator = null;
            this.Text53.Interaction = null;
            this.Text53.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text53.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text53.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text54
            // 
            this.Text54 = new Stimulsoft.Report.Components.StiText();
            this.Text54.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(21.1, 4.75, 1.4, 0.6);
            this.Text54.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text54.Name = "Text54";
            this.Text54.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text54__GetValue);
            this.Text54.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text54.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text54.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text54.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text54.Font = new System.Drawing.Font("Arial", 8F);
            this.Text54.Guid = null;
            this.Text54.Indicator = null;
            this.Text54.Interaction = null;
            this.Text54.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text54.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text54.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text55
            // 
            this.Text55 = new Stimulsoft.Report.Components.StiText();
            this.Text55.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(21.1, 5.35, 1.4, 0.6);
            this.Text55.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text55.Name = "Text55";
            this.Text55.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text55__GetValue);
            this.Text55.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text55.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text55.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text55.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text55.Font = new System.Drawing.Font("Arial", 8F);
            this.Text55.Guid = null;
            this.Text55.Indicator = null;
            this.Text55.Interaction = null;
            this.Text55.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text55.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text55.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text56
            // 
            this.Text56 = new Stimulsoft.Report.Components.StiText();
            this.Text56.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(1.6, 5.95, 2.4, 0.65);
            this.Text56.Enabled = false;
            this.Text56.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text56.Name = "Text56";
            this.Text56.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text56__GetValue);
            this.Text56.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text56.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text56.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text56.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text56.Font = new System.Drawing.Font("Arial", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text56.Guid = null;
            this.Text56.Indicator = null;
            this.Text56.Interaction = null;
            this.Text56.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text56.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text56.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text57
            // 
            this.Text57 = new Stimulsoft.Report.Components.StiText();
            this.Text57.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(4, 5.95, 5.4, 0.65);
            this.Text57.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text57.Name = "Text57";
            this.Text57.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text57__GetValue);
            this.Text57.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text57.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text57.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text57.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text57.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text57.Guid = null;
            this.Text57.Indicator = null;
            this.Text57.Interaction = null;
            this.Text57.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text57.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text57.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text69
            // 
            this.Text69 = new Stimulsoft.Report.Components.StiText();
            this.Text69.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(9.4, 5.95, 1.8, 1.3);
            this.Text69.Enabled = false;
            this.Text69.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text69.Name = "Text69";
            this.Text69.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text69__GetValue);
            this.Text69.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text69.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text69.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text69.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text69.Font = new System.Drawing.Font("Arial", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text69.Guid = null;
            this.Text69.Indicator = null;
            this.Text69.Interaction = null;
            this.Text69.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text69.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text69.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text70
            // 
            this.Text70 = new Stimulsoft.Report.Components.StiText();
            this.Text70.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(11.2, 5.95, 1.7, 1.3);
            this.Text70.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text70.Name = "Text70";
            this.Text70.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text70__GetValue);
            this.Text70.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text70.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text70.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text70.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text70.Font = new System.Drawing.Font("Arial", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text70.Guid = null;
            this.Text70.Indicator = null;
            this.Text70.Interaction = null;
            this.Text70.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text70.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text70.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text71
            // 
            this.Text71 = new Stimulsoft.Report.Components.StiText();
            this.Text71.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(12.9, 5.95, 2.6, 0.65);
            this.Text71.Enabled = false;
            this.Text71.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text71.Name = "Text71";
            this.Text71.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text71__GetValue);
            this.Text71.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text71.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text71.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text71.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text71.Font = new System.Drawing.Font("Arial", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text71.Guid = null;
            this.Text71.Indicator = null;
            this.Text71.Interaction = null;
            this.Text71.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text71.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text71.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text72
            // 
            this.Text72 = new Stimulsoft.Report.Components.StiText();
            this.Text72.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(12.9, 6.6, 2.6, 0.65);
            this.Text72.Enabled = false;
            this.Text72.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text72.Name = "Text72";
            this.Text72.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text72__GetValue);
            this.Text72.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text72.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text72.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text72.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text72.Font = new System.Drawing.Font("Arial", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text72.Guid = null;
            this.Text72.Indicator = null;
            this.Text72.Interaction = null;
            this.Text72.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text72.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text72.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text73
            // 
            this.Text73 = new Stimulsoft.Report.Components.StiText();
            this.Text73.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(15.5, 5.95, 2.5, 0.65);
            this.Text73.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text73.Name = "Text73";
            this.Text73.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text73__GetValue);
            this.Text73.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text73.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text73.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text73.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text73.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text73.Guid = null;
            this.Text73.Indicator = null;
            this.Text73.Interaction = null;
            this.Text73.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text73.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text73.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text74
            // 
            this.Text74 = new Stimulsoft.Report.Components.StiText();
            this.Text74.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(15.5, 6.6, 2.5, 0.65);
            this.Text74.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text74.Name = "Text74";
            this.Text74.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text74__GetValue);
            this.Text74.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text74.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text74.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text74.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text74.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text74.Guid = null;
            this.Text74.Indicator = null;
            this.Text74.Interaction = null;
            this.Text74.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text74.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text74.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text75
            // 
            this.Text75 = new Stimulsoft.Report.Components.StiText();
            this.Text75.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(1.6, 6.6, 2.4, 0.65);
            this.Text75.Enabled = false;
            this.Text75.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text75.Name = "Text75";
            this.Text75.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text75__GetValue);
            this.Text75.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text75.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text75.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text75.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text75.Font = new System.Drawing.Font("Arial", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text75.Guid = null;
            this.Text75.Indicator = null;
            this.Text75.Interaction = null;
            this.Text75.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text75.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text75.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text76
            // 
            this.Text76 = new Stimulsoft.Report.Components.StiText();
            this.Text76.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(4, 6.6, 5.4, 0.65);
            this.Text76.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text76.Name = "Text76";
            this.Text76.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text76__GetValue);
            this.Text76.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text76.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text76.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text76.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text76.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text76.Guid = null;
            this.Text76.Indicator = null;
            this.Text76.Interaction = null;
            this.Text76.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text76.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text76.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text86
            // 
            this.Text86 = new Stimulsoft.Report.Components.StiText();
            this.Text86.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(18, 5.95, 3.1, 0.65);
            this.Text86.Enabled = false;
            this.Text86.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text86.Name = "Text86";
            this.Text86.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text86__GetValue);
            this.Text86.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text86.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text86.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text86.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text86.Font = new System.Drawing.Font("Arial", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text86.Guid = null;
            this.Text86.Indicator = null;
            this.Text86.Interaction = null;
            this.Text86.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text86.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text86.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text87
            // 
            this.Text87 = new Stimulsoft.Report.Components.StiText();
            this.Text87.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(18, 6.6, 3.1, 0.65);
            this.Text87.Enabled = false;
            this.Text87.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text87.Name = "Text87";
            this.Text87.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text87__GetValue);
            this.Text87.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text87.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text87.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text87.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text87.Font = new System.Drawing.Font("Arial", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text87.Guid = null;
            this.Text87.Indicator = null;
            this.Text87.Interaction = null;
            this.Text87.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text87.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text87.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text88
            // 
            this.Text88 = new Stimulsoft.Report.Components.StiText();
            this.Text88.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(21.1, 5.95, 1.4, 0.65);
            this.Text88.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text88.Name = "Text88";
            this.Text88.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text88__GetValue);
            this.Text88.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text88.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text88.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text88.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text88.Font = new System.Drawing.Font("Arial", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text88.Guid = null;
            this.Text88.Indicator = null;
            this.Text88.Interaction = null;
            this.Text88.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text88.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text88.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text89
            // 
            this.Text89 = new Stimulsoft.Report.Components.StiText();
            this.Text89.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(21.1, 6.6, 1.4, 0.65);
            this.Text89.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text89.Name = "Text89";
            this.Text89.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text89__GetValue);
            this.Text89.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text89.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text89.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text89.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text89.Font = new System.Drawing.Font("Arial", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text89.Guid = null;
            this.Text89.Indicator = null;
            this.Text89.Interaction = null;
            this.Text89.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text89.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text89.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text90
            // 
            this.Text90 = new Stimulsoft.Report.Components.StiText();
            this.Text90.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(1.6, 7.25, 1.2, 0.8);
            this.Text90.Enabled = false;
            this.Text90.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text90.Name = "Text90";
            this.Text90.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text90__GetValue);
            this.Text90.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text90.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text90.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text90.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text90.Font = new System.Drawing.Font("Arial", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text90.Guid = null;
            this.Text90.Indicator = null;
            this.Text90.Interaction = null;
            this.Text90.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text90.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text90.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text91
            // 
            this.Text91 = new Stimulsoft.Report.Components.StiText();
            this.Text91.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(2.8, 7.25, 1.2, 0.8);
            this.Text91.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text91.Name = "Text91";
            this.Text91.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text91__GetValue);
            this.Text91.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text91.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text91.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text91.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text91.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text91.Guid = null;
            this.Text91.Indicator = null;
            this.Text91.Interaction = null;
            this.Text91.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text91.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text91.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text92
            // 
            this.Text92 = new Stimulsoft.Report.Components.StiText();
            this.Text92.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(4, 7.25, 1.6, 0.8);
            this.Text92.Enabled = false;
            this.Text92.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text92.Name = "Text92";
            this.Text92.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text92__GetValue);
            this.Text92.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text92.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text92.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text92.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text92.Font = new System.Drawing.Font("Arial", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text92.Guid = null;
            this.Text92.Indicator = null;
            this.Text92.Interaction = null;
            this.Text92.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text92.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text92.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text93
            // 
            this.Text93 = new Stimulsoft.Report.Components.StiText();
            this.Text93.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(5.6, 7.25, 1.6, 0.8);
            this.Text93.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text93.Name = "Text93";
            this.Text93.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text93__GetValue);
            this.Text93.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text93.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text93.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text93.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text93.Font = new System.Drawing.Font("Arial", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text93.Guid = null;
            this.Text93.Indicator = null;
            this.Text93.Interaction = null;
            this.Text93.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text93.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text93.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text94
            // 
            this.Text94 = new Stimulsoft.Report.Components.StiText();
            this.Text94.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(7.1, 7.25, 2.3, 0.8);
            this.Text94.Enabled = false;
            this.Text94.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text94.Name = "Text94";
            this.Text94.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text94__GetValue);
            this.Text94.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text94.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text94.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text94.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text94.Font = new System.Drawing.Font("Arial", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text94.Guid = null;
            this.Text94.Indicator = null;
            this.Text94.Interaction = null;
            this.Text94.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text94.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text94.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text95
            // 
            this.Text95 = new Stimulsoft.Report.Components.StiText();
            this.Text95.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(9.4, 7.25, 3.5, 0.8);
            this.Text95.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text95.Name = "Text95";
            this.Text95.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text95__GetValue);
            this.Text95.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text95.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text95.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text95.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text95.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text95.Guid = null;
            this.Text95.Indicator = null;
            this.Text95.Interaction = null;
            this.Text95.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text95.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text95.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text96
            // 
            this.Text96 = new Stimulsoft.Report.Components.StiText();
            this.Text96.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(12.9, 7.25, 2.6, 0.8);
            this.Text96.Enabled = false;
            this.Text96.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text96.Name = "Text96";
            this.Text96.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text96__GetValue);
            this.Text96.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text96.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text96.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text96.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text96.Font = new System.Drawing.Font("Arial", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text96.Guid = null;
            this.Text96.Indicator = null;
            this.Text96.Interaction = null;
            this.Text96.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text96.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text96.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text97
            // 
            this.Text97 = new Stimulsoft.Report.Components.StiText();
            this.Text97.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(15.5, 7.25, 2.5, 0.8);
            this.Text97.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text97.Name = "Text97";
            this.Text97.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text97__GetValue);
            this.Text97.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text97.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text97.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text97.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text97.Font = new System.Drawing.Font("Arial", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text97.Guid = null;
            this.Text97.Indicator = null;
            this.Text97.Interaction = null;
            this.Text97.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text97.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text97.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text98
            // 
            this.Text98 = new Stimulsoft.Report.Components.StiText();
            this.Text98.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(18, 7.25, 3.1, 0.8);
            this.Text98.Enabled = false;
            this.Text98.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text98.Name = "Text98";
            this.Text98.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text98__GetValue);
            this.Text98.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text98.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text98.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text98.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text98.Font = new System.Drawing.Font("Arial", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text98.Guid = null;
            this.Text98.Indicator = null;
            this.Text98.Interaction = null;
            this.Text98.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text98.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text98.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text99
            // 
            this.Text99 = new Stimulsoft.Report.Components.StiText();
            this.Text99.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(21.1, 7.25, 1.4, 0.8);
            this.Text99.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text99.Name = "Text99";
            this.Text99.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text99__GetValue);
            this.Text99.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text99.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text99.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text99.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text99.Font = new System.Drawing.Font("Arial", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text99.Guid = null;
            this.Text99.Indicator = null;
            this.Text99.Interaction = null;
            this.Text99.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text99.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text99.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text100
            // 
            this.Text100 = new Stimulsoft.Report.Components.StiText();
            this.Text100.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(1.6, 8.05, 2.4, 0.8);
            this.Text100.Enabled = false;
            this.Text100.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text100.Name = "Text100";
            this.Text100.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text100__GetValue);
            this.Text100.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text100.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text100.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text100.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text100.Font = new System.Drawing.Font("Arial", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text100.Guid = null;
            this.Text100.Indicator = null;
            this.Text100.Interaction = null;
            this.Text100.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text100.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text100.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text101
            // 
            this.Text101 = new Stimulsoft.Report.Components.StiText();
            this.Text101.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(4, 8.05, 3.1, 0.8);
            this.Text101.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text101.Name = "Text101";
            this.Text101.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text101__GetValue);
            this.Text101.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text101.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text101.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text101.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text101.Font = new System.Drawing.Font("Arial", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text101.Guid = null;
            this.Text101.Indicator = null;
            this.Text101.Interaction = null;
            this.Text101.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text101.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text101.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text102
            // 
            this.Text102 = new Stimulsoft.Report.Components.StiText();
            this.Text102.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(7.1, 8.05, 2.3, 0.8);
            this.Text102.Enabled = false;
            this.Text102.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text102.Name = "Text102";
            this.Text102.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text102__GetValue);
            this.Text102.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text102.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text102.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text102.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text102.Font = new System.Drawing.Font("Arial", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text102.Guid = null;
            this.Text102.Indicator = null;
            this.Text102.Interaction = null;
            this.Text102.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text102.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text102.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text103
            // 
            this.Text103 = new Stimulsoft.Report.Components.StiText();
            this.Text103.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(9.4, 8.05, 3.5, 0.8);
            this.Text103.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text103.Name = "Text103";
            this.Text103.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text103__GetValue);
            this.Text103.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text103.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text103.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text103.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text103.Font = new System.Drawing.Font("Arial", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text103.Guid = null;
            this.Text103.Indicator = null;
            this.Text103.Interaction = null;
            this.Text103.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text103.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text103.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text104
            // 
            this.Text104 = new Stimulsoft.Report.Components.StiText();
            this.Text104.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(18, 8.05, 3.1, 0.8);
            this.Text104.Enabled = false;
            this.Text104.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text104.Name = "Text104";
            this.Text104.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text104__GetValue);
            this.Text104.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text104.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text104.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text104.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text104.Font = new System.Drawing.Font("Arial", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text104.Guid = null;
            this.Text104.Indicator = null;
            this.Text104.Interaction = null;
            this.Text104.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text104.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text104.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text105
            // 
            this.Text105 = new Stimulsoft.Report.Components.StiText();
            this.Text105.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(21.1, 8.05, 1.4, 0.8);
            this.Text105.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text105.Name = "Text105";
            this.Text105.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text105__GetValue);
            this.Text105.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text105.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text105.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text105.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text105.Font = new System.Drawing.Font("Arial", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text105.Guid = null;
            this.Text105.Indicator = null;
            this.Text105.Interaction = null;
            this.Text105.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text105.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text105.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text106
            // 
            this.Text106 = new Stimulsoft.Report.Components.StiText();
            this.Text106.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(12.9, 8.05, 2.6, 0.8);
            this.Text106.Enabled = false;
            this.Text106.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text106.Name = "Text106";
            this.Text106.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text106__GetValue);
            this.Text106.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text106.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text106.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text106.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text106.Font = new System.Drawing.Font("Arial", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text106.Guid = null;
            this.Text106.Indicator = null;
            this.Text106.Interaction = null;
            this.Text106.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text106.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text106.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text107
            // 
            this.Text107 = new Stimulsoft.Report.Components.StiText();
            this.Text107.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(15.5, 8.05, 2.5, 0.8);
            this.Text107.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text107.Name = "Text107";
            this.Text107.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text107__GetValue);
            this.Text107.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text107.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text107.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text107.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text107.Font = new System.Drawing.Font("Arial", 8F);
            this.Text107.Guid = null;
            this.Text107.Indicator = null;
            this.Text107.Interaction = null;
            this.Text107.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text107.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text107.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text108
            // 
            this.Text108 = new Stimulsoft.Report.Components.StiText();
            this.Text108.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(1.6, 9, 1, 0.6);
            this.Text108.Enabled = false;
            this.Text108.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text108.Name = "Text108";
            this.Text108.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text108__GetValue);
            this.Text108.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text108.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text108.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text108.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text108.Font = new System.Drawing.Font("Arial", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text108.Guid = null;
            this.Text108.Indicator = null;
            this.Text108.Interaction = null;
            this.Text108.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text108.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text108.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text109
            // 
            this.Text109 = new Stimulsoft.Report.Components.StiText();
            this.Text109.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(2.6, 9, 1.6, 0.6);
            this.Text109.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text109.Name = "Text109";
            this.Text109.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text109__GetValue);
            this.Text109.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text109.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text109.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text109.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text109.Font = new System.Drawing.Font("Arial", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text109.Guid = null;
            this.Text109.Indicator = null;
            this.Text109.Interaction = null;
            this.Text109.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text109.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text109.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text110
            // 
            this.Text110 = new Stimulsoft.Report.Components.StiText();
            this.Text110.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(5.4, 9, 1, 0.6);
            this.Text110.Enabled = false;
            this.Text110.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text110.Name = "Text110";
            this.Text110.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text110__GetValue);
            this.Text110.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text110.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text110.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text110.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text110.Font = new System.Drawing.Font("Arial", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text110.Guid = null;
            this.Text110.Indicator = null;
            this.Text110.Interaction = null;
            this.Text110.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text110.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text110.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text111
            // 
            this.Text111 = new Stimulsoft.Report.Components.StiText();
            this.Text111.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(6.4, 9, 1.6, 0.6);
            this.Text111.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text111.Name = "Text111";
            this.Text111.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text111__GetValue);
            this.Text111.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text111.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text111.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text111.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text111.Font = new System.Drawing.Font("Arial", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text111.Guid = null;
            this.Text111.Indicator = null;
            this.Text111.Interaction = null;
            this.Text111.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text111.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text111.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text112
            // 
            this.Text112 = new Stimulsoft.Report.Components.StiText();
            this.Text112.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(9.2, 9, 1, 0.6);
            this.Text112.Enabled = false;
            this.Text112.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text112.Name = "Text112";
            this.Text112.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text112__GetValue);
            this.Text112.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text112.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text112.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text112.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text112.Font = new System.Drawing.Font("Arial", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text112.Guid = null;
            this.Text112.Indicator = null;
            this.Text112.Interaction = null;
            this.Text112.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text112.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text112.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text113
            // 
            this.Text113 = new Stimulsoft.Report.Components.StiText();
            this.Text113.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(10.2, 9, 1.6, 0.6);
            this.Text113.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text113.Name = "Text113";
            this.Text113.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text113__GetValue);
            this.Text113.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text113.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text113.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text113.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text113.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text113.Guid = null;
            this.Text113.Indicator = null;
            this.Text113.Interaction = null;
            this.Text113.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text113.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text113.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text114
            // 
            this.Text114 = new Stimulsoft.Report.Components.StiText();
            this.Text114.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(13, 9, 1, 0.6);
            this.Text114.Enabled = false;
            this.Text114.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text114.Name = "Text114";
            this.Text114.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text114__GetValue);
            this.Text114.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text114.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text114.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text114.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text114.Font = new System.Drawing.Font("Arial", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text114.Guid = null;
            this.Text114.Indicator = null;
            this.Text114.Interaction = null;
            this.Text114.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text114.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text114.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text115
            // 
            this.Text115 = new Stimulsoft.Report.Components.StiText();
            this.Text115.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(14, 9, 1.6, 0.6);
            this.Text115.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text115.Name = "Text115";
            this.Text115.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text115__GetValue);
            this.Text115.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text115.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text115.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text115.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text115.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text115.Guid = null;
            this.Text115.Indicator = null;
            this.Text115.Interaction = null;
            this.Text115.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text115.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text115.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text116
            // 
            this.Text116 = new Stimulsoft.Report.Components.StiText();
            this.Text116.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(16.8, 9, 2, 0.6);
            this.Text116.Enabled = false;
            this.Text116.Name = "Text116";
            this.Text116.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text116__GetValue);
            this.Text116.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text116.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text116.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text116.Font = new System.Drawing.Font("Arial", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text116.Guid = null;
            this.Text116.Indicator = null;
            this.Text116.Interaction = null;
            this.Text116.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text116.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text116.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text117
            // 
            this.Text117 = new Stimulsoft.Report.Components.StiText();
            this.Text117.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(18.8, 9, 3.7, 0.6);
            this.Text117.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text117.Name = "Text117";
            this.InitializeComponent3();
        }
        
        public void InitializeComponent3()
        {
            this.Text117.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text117__GetValue);
            this.Text117.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text117.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text117.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text117.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text117.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text117.Guid = null;
            this.Text117.Indicator = null;
            this.Text117.Interaction = null;
            this.Text117.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text117.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text117.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text118
            // 
            this.Text118 = new Stimulsoft.Report.Components.StiText();
            this.Text118.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(1.6, 1.2, 0.8, 0.6);
            this.Text118.Enabled = false;
            this.Text118.Name = "Text118";
            this.Text118.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text118__GetValue);
            this.Text118.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text118.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text118.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text118.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text118.Font = new System.Drawing.Font("Arial", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text118.Guid = null;
            this.Text118.Indicator = null;
            this.Text118.Interaction = null;
            this.Text118.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text118.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text118.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text119
            // 
            this.Text119 = new Stimulsoft.Report.Components.StiText();
            this.Text119.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(2.4, 1.2, 1.8, 0.6);
            this.Text119.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text119.Name = "Text119";
            this.Text119.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text119__GetValue);
            this.Text119.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text119.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text119.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text119.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text119.Font = new System.Drawing.Font("Arial", 8F);
            this.Text119.Guid = null;
            this.Text119.Indicator = null;
            this.Text119.Interaction = null;
            this.Text119.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text119.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text119.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text120
            // 
            this.Text120 = new Stimulsoft.Report.Components.StiText();
            this.Text120.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(4.2, 1.2, 1.8, 0.6);
            this.Text120.Enabled = false;
            this.Text120.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text120.Name = "Text120";
            this.Text120.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text120__GetValue);
            this.Text120.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text120.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text120.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text120.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text120.Font = new System.Drawing.Font("Arial", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text120.Guid = null;
            this.Text120.Indicator = null;
            this.Text120.Interaction = null;
            this.Text120.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text120.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text120.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text121
            // 
            this.Text121 = new Stimulsoft.Report.Components.StiText();
            this.Text121.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(6, 1.2, 1.65, 0.6);
            this.Text121.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text121.Name = "Text121";
            this.Text121.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text121__GetValue);
            this.Text121.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text121.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text121.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text121.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text121.Font = new System.Drawing.Font("Arial", 8F);
            this.Text121.Guid = null;
            this.Text121.Indicator = null;
            this.Text121.Interaction = null;
            this.Text121.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text121.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text121.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text122
            // 
            this.Text122 = new Stimulsoft.Report.Components.StiText();
            this.Text122.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(7.65, 1.2, 1.8, 0.6);
            this.Text122.Enabled = false;
            this.Text122.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text122.Name = "Text122";
            this.Text122.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text122__GetValue);
            this.Text122.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text122.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text122.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text122.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text122.Font = new System.Drawing.Font("Arial", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text122.Guid = null;
            this.Text122.Indicator = null;
            this.Text122.Interaction = null;
            this.Text122.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text122.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text122.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text123
            // 
            this.Text123 = new Stimulsoft.Report.Components.StiText();
            this.Text123.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(9.4, 1.2, 3, 0.6);
            this.Text123.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text123.Name = "Text123";
            this.Text123.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text123__GetValue);
            this.Text123.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text123.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text123.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text123.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text123.Font = new System.Drawing.Font("Arial", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text123.Guid = null;
            this.Text123.Indicator = null;
            this.Text123.Interaction = null;
            this.Text123.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text123.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text123.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text124
            // 
            this.Text124 = new Stimulsoft.Report.Components.StiText();
            this.Text124.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(12.4, 1.2, 2.2, 0.6);
            this.Text124.Enabled = false;
            this.Text124.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text124.Name = "Text124";
            this.Text124.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text124__GetValue);
            this.Text124.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text124.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text124.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text124.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text124.Font = new System.Drawing.Font("Arial", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text124.Guid = null;
            this.Text124.Indicator = null;
            this.Text124.Interaction = null;
            this.Text124.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text124.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text124.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text125
            // 
            this.Text125 = new Stimulsoft.Report.Components.StiText();
            this.Text125.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(14.6, 1.2, 2.2, 0.6);
            this.Text125.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text125.Name = "Text125";
            this.Text125.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text125__GetValue);
            this.Text125.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text125.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text125.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text125.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text125.Font = new System.Drawing.Font("Arial", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text125.Guid = null;
            this.Text125.Indicator = null;
            this.Text125.Interaction = null;
            this.Text125.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text125.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text125.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text126
            // 
            this.Text126 = new Stimulsoft.Report.Components.StiText();
            this.Text126.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(16.8, 1.2, 1, 0.6);
            this.Text126.Enabled = false;
            this.Text126.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text126.Name = "Text126";
            this.Text126.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text126__GetValue);
            this.Text126.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text126.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text126.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text126.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text126.Font = new System.Drawing.Font("Arial", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text126.Guid = null;
            this.Text126.Indicator = null;
            this.Text126.Interaction = null;
            this.Text126.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text126.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text126.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text127
            // 
            this.Text127 = new Stimulsoft.Report.Components.StiText();
            this.Text127.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(17.8, 1.2, 1.6, 0.6);
            this.Text127.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text127.Name = "Text127";
            this.Text127.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text127__GetValue);
            this.Text127.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text127.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text127.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text127.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text127.Font = new System.Drawing.Font("Arial", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text127.Guid = null;
            this.Text127.Indicator = null;
            this.Text127.Interaction = null;
            this.Text127.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text127.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text127.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text128
            // 
            this.Text128 = new Stimulsoft.Report.Components.StiText();
            this.Text128.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(19.4, 1.2, 3.1, 0.6);
            this.Text128.Guid = "b44cf5b4ae964ebbba514749f7988574";
            this.Text128.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text128.Name = "Text128";
            this.Text128.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text128__GetValue);
            this.Text128.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text128.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text128.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text128.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text128.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text128.Indicator = null;
            this.Text128.Interaction = null;
            this.Text128.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text128.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text128.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text134
            // 
            this.Text134 = new Stimulsoft.Report.Components.StiText();
            this.Text134.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(17.2, 0.4, 1, 0.4);
            this.Text134.Enabled = false;
            this.Text134.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text134.Name = "Text134";
            this.Text134.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text134__GetValue);
            this.Text134.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text134.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text134.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text134.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text134.Font = new System.Drawing.Font("Arial", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text134.Guid = null;
            this.Text134.Indicator = null;
            this.Text134.Interaction = null;
            this.Text134.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text134.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text134.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            // 
            // Text135
            // 
            this.Text135 = new Stimulsoft.Report.Components.StiText();
            this.Text135.ClientRectangle = new Stimulsoft.Base.Drawing.RectangleD(18.4, 0.4, 4, 0.4);
            this.Text135.HorAlignment = Stimulsoft.Base.Drawing.StiTextHorAlignment.Center;
            this.Text135.Name = "Text135";
            this.Text135.GetValue += new Stimulsoft.Report.Events.StiGetValueEventHandler(this.Text135__GetValue);
            this.Text135.Type = Stimulsoft.Report.Components.StiSystemTextType.Expression;
            this.Text135.VertAlignment = Stimulsoft.Base.Drawing.StiVertAlignment.Center;
            this.Text135.Border = new Stimulsoft.Base.Drawing.StiBorder(Stimulsoft.Base.Drawing.StiBorderSides.None, System.Drawing.Color.Black, 1, Stimulsoft.Base.Drawing.StiPenStyle.Solid, false, 4, new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black), false);
            this.Text135.Brush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Transparent);
            this.Text135.Font = new System.Drawing.Font("Arial", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
            this.Text135.Guid = null;
            this.Text135.Indicator = null;
            this.Text135.Interaction = null;
            this.Text135.Margins = new Stimulsoft.Report.Components.StiMargins(0, 0, 0, 0);
            this.Text135.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.Black);
            this.Text135.TextOptions = new Stimulsoft.Base.Drawing.StiTextOptions(false, false, false, 0F, System.Drawing.Text.HotkeyPrefix.None, System.Drawing.StringTrimming.None);
            this.HeaderBand1.Guid = null;
            this.HeaderBand1.Interaction = null;
            this.Page1.ExcelSheetValue = null;
            this.Page1.Interaction = null;
            this.Page1.Margins = new Stimulsoft.Report.Components.StiMargins(1, 1, 0, 0);
            this.Page1_Watermark = new Stimulsoft.Report.Components.StiWatermark();
            this.Page1_Watermark.Font = new System.Drawing.Font("Arial", 100F);
            this.Page1_Watermark.Image = null;
            this.Page1_Watermark.TextBrush = new Stimulsoft.Base.Drawing.StiSolidBrush(System.Drawing.Color.FromArgb(50, 0, 0, 0));
            this.吉林省医疗机构住院收费专用票据_PrinterSettings = new Stimulsoft.Report.Print.StiPrinterSettings();
            this.PrinterSettings = this.吉林省医疗机构住院收费专用票据_PrinterSettings;
            this.Page1.Report = this;
            this.Page1.Watermark = this.Page1_Watermark;
            this.HeaderBand1.Page = this.Page1;
            this.HeaderBand1.Parent = this.Page1;
            this.Text1.Page = this.Page1;
            this.Text1.Parent = this.HeaderBand1;
            this.Text2.Page = this.Page1;
            this.Text2.Parent = this.HeaderBand1;
            this.Text3.Page = this.Page1;
            this.Text3.Parent = this.HeaderBand1;
            this.Text4.Page = this.Page1;
            this.Text4.Parent = this.HeaderBand1;
            this.Text5.Page = this.Page1;
            this.Text5.Parent = this.HeaderBand1;
            this.Text6.Page = this.Page1;
            this.Text6.Parent = this.HeaderBand1;
            this.Text7.Page = this.Page1;
            this.Text7.Parent = this.HeaderBand1;
            this.Text8.Page = this.Page1;
            this.Text8.Parent = this.HeaderBand1;
            this.Text9.Page = this.Page1;
            this.Text9.Parent = this.HeaderBand1;
            this.Text10.Page = this.Page1;
            this.Text10.Parent = this.HeaderBand1;
            this.Text11.Page = this.Page1;
            this.Text11.Parent = this.HeaderBand1;
            this.Text12.Page = this.Page1;
            this.Text12.Parent = this.HeaderBand1;
            this.Text13.Page = this.Page1;
            this.Text13.Parent = this.HeaderBand1;
            this.Text15.Page = this.Page1;
            this.Text15.Parent = this.HeaderBand1;
            this.Text16.Page = this.Page1;
            this.Text16.Parent = this.HeaderBand1;
            this.Text17.Page = this.Page1;
            this.Text17.Parent = this.HeaderBand1;
            this.Text18.Page = this.Page1;
            this.Text18.Parent = this.HeaderBand1;
            this.Text19.Page = this.Page1;
            this.Text19.Parent = this.HeaderBand1;
            this.Text20.Page = this.Page1;
            this.Text20.Parent = this.HeaderBand1;
            this.Text21.Page = this.Page1;
            this.Text21.Parent = this.HeaderBand1;
            this.Text14.Page = this.Page1;
            this.Text14.Parent = this.HeaderBand1;
            this.Text22.Page = this.Page1;
            this.Text22.Parent = this.HeaderBand1;
            this.Text23.Page = this.Page1;
            this.Text23.Parent = this.HeaderBand1;
            this.Text24.Page = this.Page1;
            this.Text24.Parent = this.HeaderBand1;
            this.Text25.Page = this.Page1;
            this.Text25.Parent = this.HeaderBand1;
            this.Text26.Page = this.Page1;
            this.Text26.Parent = this.HeaderBand1;
            this.Text27.Page = this.Page1;
            this.Text27.Parent = this.HeaderBand1;
            this.Text28.Page = this.Page1;
            this.Text28.Parent = this.HeaderBand1;
            this.Text29.Page = this.Page1;
            this.Text29.Parent = this.HeaderBand1;
            this.Text30.Page = this.Page1;
            this.Text30.Parent = this.HeaderBand1;
            this.Text31.Page = this.Page1;
            this.Text31.Parent = this.HeaderBand1;
            this.Text32.Page = this.Page1;
            this.Text32.Parent = this.HeaderBand1;
            this.Text33.Page = this.Page1;
            this.Text33.Parent = this.HeaderBand1;
            this.Text34.Page = this.Page1;
            this.Text34.Parent = this.HeaderBand1;
            this.Text35.Page = this.Page1;
            this.Text35.Parent = this.HeaderBand1;
            this.Text36.Page = this.Page1;
            this.Text36.Parent = this.HeaderBand1;
            this.Text37.Page = this.Page1;
            this.Text37.Parent = this.HeaderBand1;
            this.Text38.Page = this.Page1;
            this.Text38.Parent = this.HeaderBand1;
            this.Text39.Page = this.Page1;
            this.Text39.Parent = this.HeaderBand1;
            this.Text40.Page = this.Page1;
            this.Text40.Parent = this.HeaderBand1;
            this.Text41.Page = this.Page1;
            this.Text41.Parent = this.HeaderBand1;
            this.Text42.Page = this.Page1;
            this.Text42.Parent = this.HeaderBand1;
            this.Text43.Page = this.Page1;
            this.Text43.Parent = this.HeaderBand1;
            this.Text44.Page = this.Page1;
            this.Text44.Parent = this.HeaderBand1;
            this.Text45.Page = this.Page1;
            this.Text45.Parent = this.HeaderBand1;
            this.Text46.Page = this.Page1;
            this.Text46.Parent = this.HeaderBand1;
            this.Text47.Page = this.Page1;
            this.Text47.Parent = this.HeaderBand1;
            this.Text48.Page = this.Page1;
            this.Text48.Parent = this.HeaderBand1;
            this.Text49.Page = this.Page1;
            this.Text49.Parent = this.HeaderBand1;
            this.Text50.Page = this.Page1;
            this.Text50.Parent = this.HeaderBand1;
            this.Text51.Page = this.Page1;
            this.Text51.Parent = this.HeaderBand1;
            this.Text52.Page = this.Page1;
            this.Text52.Parent = this.HeaderBand1;
            this.Text53.Page = this.Page1;
            this.Text53.Parent = this.HeaderBand1;
            this.Text54.Page = this.Page1;
            this.Text54.Parent = this.HeaderBand1;
            this.Text55.Page = this.Page1;
            this.Text55.Parent = this.HeaderBand1;
            this.Text56.Page = this.Page1;
            this.Text56.Parent = this.HeaderBand1;
            this.Text57.Page = this.Page1;
            this.Text57.Parent = this.HeaderBand1;
            this.Text69.Page = this.Page1;
            this.Text69.Parent = this.HeaderBand1;
            this.Text70.Page = this.Page1;
            this.Text70.Parent = this.HeaderBand1;
            this.Text71.Page = this.Page1;
            this.Text71.Parent = this.HeaderBand1;
            this.Text72.Page = this.Page1;
            this.Text72.Parent = this.HeaderBand1;
            this.Text73.Page = this.Page1;
            this.Text73.Parent = this.HeaderBand1;
            this.Text74.Page = this.Page1;
            this.Text74.Parent = this.HeaderBand1;
            this.Text75.Page = this.Page1;
            this.Text75.Parent = this.HeaderBand1;
            this.Text76.Page = this.Page1;
            this.Text76.Parent = this.HeaderBand1;
            this.Text86.Page = this.Page1;
            this.Text86.Parent = this.HeaderBand1;
            this.Text87.Page = this.Page1;
            this.Text87.Parent = this.HeaderBand1;
            this.Text88.Page = this.Page1;
            this.Text88.Parent = this.HeaderBand1;
            this.Text89.Page = this.Page1;
            this.Text89.Parent = this.HeaderBand1;
            this.Text90.Page = this.Page1;
            this.Text90.Parent = this.HeaderBand1;
            this.Text91.Page = this.Page1;
            this.Text91.Parent = this.HeaderBand1;
            this.Text92.Page = this.Page1;
            this.Text92.Parent = this.HeaderBand1;
            this.Text93.Page = this.Page1;
            this.Text93.Parent = this.HeaderBand1;
            this.Text94.Page = this.Page1;
            this.Text94.Parent = this.HeaderBand1;
            this.Text95.Page = this.Page1;
            this.Text95.Parent = this.HeaderBand1;
            this.Text96.Page = this.Page1;
            this.Text96.Parent = this.HeaderBand1;
            this.Text97.Page = this.Page1;
            this.Text97.Parent = this.HeaderBand1;
            this.Text98.Page = this.Page1;
            this.Text98.Parent = this.HeaderBand1;
            this.Text99.Page = this.Page1;
            this.Text99.Parent = this.HeaderBand1;
            this.Text100.Page = this.Page1;
            this.Text100.Parent = this.HeaderBand1;
            this.Text101.Page = this.Page1;
            this.Text101.Parent = this.HeaderBand1;
            this.Text102.Page = this.Page1;
            this.Text102.Parent = this.HeaderBand1;
            this.Text103.Page = this.Page1;
            this.Text103.Parent = this.HeaderBand1;
            this.Text104.Page = this.Page1;
            this.Text104.Parent = this.HeaderBand1;
            this.Text105.Page = this.Page1;
            this.Text105.Parent = this.HeaderBand1;
            this.Text106.Page = this.Page1;
            this.Text106.Parent = this.HeaderBand1;
            this.Text107.Page = this.Page1;
            this.Text107.Parent = this.HeaderBand1;
            this.Text108.Page = this.Page1;
            this.Text108.Parent = this.HeaderBand1;
            this.Text109.Page = this.Page1;
            this.Text109.Parent = this.HeaderBand1;
            this.Text110.Page = this.Page1;
            this.Text110.Parent = this.HeaderBand1;
            this.Text111.Page = this.Page1;
            this.Text111.Parent = this.HeaderBand1;
            this.Text112.Page = this.Page1;
            this.Text112.Parent = this.HeaderBand1;
            this.Text113.Page = this.Page1;
            this.Text113.Parent = this.HeaderBand1;
            this.Text114.Page = this.Page1;
            this.Text114.Parent = this.HeaderBand1;
            this.Text115.Page = this.Page1;
            this.Text115.Parent = this.HeaderBand1;
            this.Text116.Page = this.Page1;
            this.Text116.Parent = this.HeaderBand1;
            this.Text117.Page = this.Page1;
            this.Text117.Parent = this.HeaderBand1;
            this.Text118.Page = this.Page1;
            this.Text118.Parent = this.HeaderBand1;
            this.Text119.Page = this.Page1;
            this.Text119.Parent = this.HeaderBand1;
            this.Text120.Page = this.Page1;
            this.Text120.Parent = this.HeaderBand1;
            this.Text121.Page = this.Page1;
            this.Text121.Parent = this.HeaderBand1;
            this.Text122.Page = this.Page1;
            this.Text122.Parent = this.HeaderBand1;
            this.Text123.Page = this.Page1;
            this.Text123.Parent = this.HeaderBand1;
            this.Text124.Page = this.Page1;
            this.Text124.Parent = this.HeaderBand1;
            this.Text125.Page = this.Page1;
            this.Text125.Parent = this.HeaderBand1;
            this.Text126.Page = this.Page1;
            this.Text126.Parent = this.HeaderBand1;
            this.Text127.Page = this.Page1;
            this.Text127.Parent = this.HeaderBand1;
            this.Text128.Page = this.Page1;
            this.Text128.Parent = this.HeaderBand1;
            this.Text134.Page = this.Page1;
            this.Text134.Parent = this.HeaderBand1;
            this.Text135.Page = this.Page1;
            this.Text135.Parent = this.HeaderBand1;
            // 
            // Add to HeaderBand1.Components
            // 
            this.HeaderBand1.Components.Clear();
            this.HeaderBand1.Components.AddRange(new Stimulsoft.Report.Components.StiComponent[] {
                        this.Text1,
                        this.Text2,
                        this.Text3,
                        this.Text4,
                        this.Text5,
                        this.Text6,
                        this.Text7,
                        this.Text8,
                        this.Text9,
                        this.Text10,
                        this.Text11,
                        this.Text12,
                        this.Text13,
                        this.Text15,
                        this.Text16,
                        this.Text17,
                        this.Text18,
                        this.Text19,
                        this.Text20,
                        this.Text21,
                        this.Text14,
                        this.Text22,
                        this.Text23,
                        this.Text24,
                        this.Text25,
                        this.Text26,
                        this.Text27,
                        this.Text28,
                        this.Text29,
                        this.Text30,
                        this.Text31,
                        this.Text32,
                        this.Text33,
                        this.Text34,
                        this.Text35,
                        this.Text36,
                        this.Text37,
                        this.Text38,
                        this.Text39,
                        this.Text40,
                        this.Text41,
                        this.Text42,
                        this.Text43,
                        this.Text44,
                        this.Text45,
                        this.Text46,
                        this.Text47,
                        this.Text48,
                        this.Text49,
                        this.Text50,
                        this.Text51,
                        this.Text52,
                        this.Text53,
                        this.Text54,
                        this.Text55,
                        this.Text56,
                        this.Text57,
                        this.Text69,
                        this.Text70,
                        this.Text71,
                        this.Text72,
                        this.Text73,
                        this.Text74,
                        this.Text75,
                        this.Text76,
                        this.Text86,
                        this.Text87,
                        this.Text88,
                        this.Text89,
                        this.Text90,
                        this.Text91,
                        this.Text92,
                        this.Text93,
                        this.Text94,
                        this.Text95,
                        this.Text96,
                        this.Text97,
                        this.Text98,
                        this.Text99,
                        this.Text100,
                        this.Text101,
                        this.Text102,
                        this.Text103,
                        this.Text104,
                        this.Text105,
                        this.Text106,
                        this.Text107,
                        this.Text108,
                        this.Text109,
                        this.Text110,
                        this.Text111,
                        this.Text112,
                        this.Text113,
                        this.Text114,
                        this.Text115,
                        this.Text116,
                        this.Text117,
                        this.Text118,
                        this.Text119,
                        this.Text120,
                        this.Text121,
                        this.Text122,
                        this.Text123,
                        this.Text124,
                        this.Text125,
                        this.Text126,
                        this.Text127,
                        this.Text128,
                        this.Text134,
                        this.Text135});
            // 
            // Add to Page1.Components
            // 
            this.Page1.Components.Clear();
            this.Page1.Components.AddRange(new Stimulsoft.Report.Components.StiComponent[] {
                        this.HeaderBand1});
            // 
            // Add to Pages
            // 
            this.Pages.Clear();
            this.Pages.AddRange(new Stimulsoft.Report.Components.StiPage[] {
                        this.Page1});
        }
        #endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>