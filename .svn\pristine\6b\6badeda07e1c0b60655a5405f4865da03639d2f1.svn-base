﻿Imports System.Data.SqlClient
Imports Stimulsoft.Report
Imports HisControl
Imports Stimulsoft.Report.Components

Public Class Xs_Mz_Js
    Dim My_DataSet As New DataSet
    Dim My_Adapter As New SqlDataAdapter
    Dim Money_Dx As New BaseClass.ChineseNum
   
#Region "传参"
    Dim Rrow As DataRow
    Dim Rds As DataSet

#End Region

    Public Sub New(ByVal trow As DataRow, ByVal tds As DataSet)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        'Rdate = tdate
        Rrow = trow
        Rds = tds
        ' 在 InitializeComponent() 调用之后添加任何初始化。
    End Sub

    Protected Overrides Sub OnKeyDown(ByVal e As System.Windows.Forms.KeyEventArgs)
        MyBase.OnKeyDown(e)
        If e.Control = True And e.KeyCode = Keys.P Then
            C1Button2.Select()
            Call C1Button2_Click(C1Button2, Nothing)
        ElseIf e.Control = True And e.KeyCode = Keys.O Then
            C1Button1.Select()
            Call C1Button2_Click(C1Button1, Nothing)
        End If
    End Sub


    Private Sub Xs_Mz_Js_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Label1.Text = "患者姓名：" & Rrow("Ry_Name")
        C1NumericEdit1.Value = Rrow("Mz_Money")
        C1NumericEdit2.Select()

        If HisPara.PublicConfig.XqName.Contains("丰润") Then
            C1Button3.Enabled = False
            C1Button2.Enabled = False
        End If

        If Rrow.Item("Mz_Jffs") = "健康卡" Then
           
        Else
            JKKye.Visible = False
            C1NumericEdit2.Value = 0
            C1NumericEdit3.Value = C1NumericEdit2.Value - C1NumericEdit1.Value
        End If

        Dim Name As String = iniOperate.iniopreate.GetINI("回车键焦点", "门诊录入缴费", "", HisVar.HisVar.Parapath & "\Config.Ini")
        If Name = "确认不打印" Then
            C1Button3.TabIndex = 1
            C1Button1.TabIndex = 2
            C1Button2.TabIndex = 3

        ElseIf Name = "快速打印" Or Name = "" Then
            C1Button3.TabIndex = 2
            C1Button1.TabIndex = 1
            C1Button2.TabIndex = 3
        ElseIf Name = "打印预览" Then
            C1Button3.TabIndex = 3
            C1Button1.TabIndex = 2
            C1Button2.TabIndex = 1
        End If
        C1NumericEdit2.Select()
    End Sub


    Private Sub C1NumericEdit2_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles C1NumericEdit2.KeyPress
        If e.KeyChar = Chr(Keys.Return) Then
            e.Handled = True
            System.Windows.Forms.SendKeys.Send("{Tab}")
        End If
    End Sub

    Private Sub C1NumericEdit2_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1NumericEdit2.Validated
        Me.C1NumericEdit3.Value = Me.C1NumericEdit2.Value - Me.C1NumericEdit1.Value
        If Me.C1NumericEdit3.Value < 0 Then
            Me.C1NumericEdit3.ForeColor = Color.Red
        Else
            Me.C1NumericEdit3.ForeColor = Color.Black
        End If
    End Sub

    Private Sub C1Button2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Button2.Click, C1Button1.Click, C1Button3.Click

        If C1NumericEdit2.Value < C1NumericEdit1.Value Then
            MsgBox("实收金额小于应收金额，请检查！", vbInformation + vbOKOnly + vbDefaultButton1, "提示:")
            C1NumericEdit2.Select()
            Exit Sub
        End If

        iniOperate.iniopreate.WriteINI("回车键焦点", "门诊录入缴费", sender.text, HisVar.HisVar.Parapath & "\Config.Ini")
        C1Button1.Enabled = False
        C1Button2.Enabled = False
        C1Button3.Enabled = False

      

        If Rrow.Item("Mz_Jffs") = "健康卡" Then
         
        Else

            If HisPara.PublicConfig.MzFpStyle = 0 Then  '老板发票
                If HisPara.PublicConfig.XqName.Contains("曲周") Then
                    Call Qz_Fp(sender.text)
                ElseIf HisPara.PublicConfig.XqName.Contains("长春") Or HisPara.PublicConfig.XqName.Contains("辽源") Or HisPara.PublicConfig.XqName.Contains("长春市") Or HisPara.PublicConfig.XqName.Contains("辽源市区") Or HisPara.PublicConfig.XqName.Contains("龙山区") Or HisPara.PublicConfig.XqName.Contains("西安区") Or HisPara.PublicConfig.XqName.Contains("东丰县") Or HisPara.PublicConfig.XqName.Contains("东辽县") Then
                    Call Cc_Fp(sender.text)
                ElseIf HisPara.PublicConfig.XqName.Contains("通辽") Then
                    Call Tl_Fp(sender.text)
                ElseIf HisPara.PublicConfig.XqName.Contains("连山区") Or HisPara.PublicConfig.XqName.Contains("龙港区") Or HisPara.PublicConfig.XqName.Contains("南票区") Or HisPara.PublicConfig.XqName.Contains("杨家杖子经济开发区") Or HisPara.PublicConfig.XqName.Contains("建昌县") Or HisPara.PublicConfig.XqName.Contains("兴城市") Then
                    Call Hld_Fp_Lb(sender.text)
                Else
                    Call Ts_Fp(sender.text)
                End If

            ElseIf HisPara.PublicConfig.MzFpStyle = 1 Then '2013新发票
                If HisPara.PublicConfig.XqName.Contains("连山区") Or HisPara.PublicConfig.XqName.Contains("龙港区") Or HisPara.PublicConfig.XqName.Contains("南票区") Or HisPara.PublicConfig.XqName.Contains("杨家杖子经济开发区") Or HisPara.PublicConfig.XqName.Contains("建昌县") Or HisPara.PublicConfig.XqName.Contains("兴城市") Then
                    Call Hld_Fp(sender.text)
                Else
                    Call Ts_Fp2013(sender.text)
                End If
            End If
        End If


        C1Button1.Enabled = True
        C1Button2.Enabled = True
        C1Button3.Enabled = True

    End Sub

    Private Sub Ts_Fp(ByVal Text As String)
        Dim V_Max As String
        Dim V_Length As Integer
        Dim i As Integer
        V_Max = 0
        V_Length = 2

      

        HisVar.HisVar.Sqldal.ExecuteSql("Update Mz_Yp Set Mz_Dl=Case isnull(a.Lb_Name,'') when '' then Mz_Lb else a.Lb_Name end  From Mz_Yp left join (select Lb_Name,substring(Fp_Code,2,2)AS m_Code,Fp_Name from Zd_MzFpHb1,Zd_MzFpHb2 where Zd_MzFpHb1.Lb_Code=Zd_MzFpHb2.Lb_Code And substring(Fp_Code,1,1)='Y')a on  (a.m_Code=substring(Mz_Yp.Xx_Code,1,2) or a.Fp_Name=Mz_Lb) where Mz_Code='" & Rrow.Item("Mz_Code") & "'")
        HisVar.HisVar.Sqldal.ExecuteSql("Update Mz_Xm Set Mz_Dl=Case isnull(a.Lb_Name,'') when '' then Mz_Lb else a.Lb_Name end From Mz_Xm,Zd_Ml_Xm3,Zd_Ml_Xm1 left join (select Lb_Name,substring(Fp_Code,2,2)AS m_Code,Fp_Name from Zd_MzFpHb1,Zd_MzFpHb2 where Zd_MzFpHb1.Lb_Code=Zd_MzFpHb2.Lb_Code And substring(Fp_Code,1,1)='X')a on a.m_Code=Zd_Ml_Xm1.Xmlb_Code where Zd_ML_Xm1.Xmlb_Code=Zd_ML_Xm3.Xmlb_Code and Zd_Ml_Xm3.Xm_Code=Mz_Xm.Xm_Code and Mz_Code='" & Rrow.Item("Mz_Code") & "'")

        HisVar.HisVar.Sqldal.ExecuteSql("Update Mz Set Mz_SsMoney=" & Me.C1NumericEdit2.Value & ",Mz_ThMoney=" & Me.C1NumericEdit3.Value & " Where Mz_Code='" & Rrow.Item("Mz_Code") & "'")
        HisVar.HisVar.Sqldal.QueryDt(My_DataSet, "Select Distinct Mz_Dl From Mz_Yp Where Mz_Code='" & Rrow.Item("Mz_Code") & "' Order by Mz_Dl", "药品", True)

        For Each temp_row In My_DataSet.Tables("药品").Rows
            V_Max = V_Max + 1
            For i = 1 To V_Length - Len(V_Max)
                V_Max = "0" & V_Max
            Next
            HisVar.HisVar.Sqldal.ExecuteSql("Update Mz_Yp Set Mz_Ph='" & Rrow.Item("Mz_Code") & V_Max & "' Where Mz_Code='" & Rrow.Item("Mz_Code") & "' And Mz_Dl='" & temp_row("Mz_Dl") & "'")
        Next


        HisVar.HisVar.Sqldal.QueryDt(My_DataSet, "Select Distinct Mz_Dl From Mz_Xm Where Mz_Code='" & Rrow.Item("Mz_Code") & "' Order by Mz_Dl", "项目", True)

        For Each temp_row In My_DataSet.Tables("项目").Rows
            V_Max = V_Max + 1
            For i = 1 To V_Length - Len(V_Max)
                V_Max = "0" & V_Max
            Next
            HisVar.HisVar.Sqldal.ExecuteSql("Update Mz_Xm Set Mz_Ph='" & Rrow.Item("Mz_Code") & V_Max & "' Where Mz_Code='" & Rrow.Item("Mz_Code") & "' And Mz_Dl='" & temp_row("Mz_Dl") & "'")
        Next
        Select Case Text
            Case "确认不打印"
                HisVar.HisVar.Sqldal.ExecuteSql("Update Mz Set Mz_Print='1' Where Mz_Code='" & Rrow.Item("Mz_Code") & "'")

                If HisVar.HisVar.Sqldal.ExecuteSql("Update Mz Set Mz_FyQr='1',Mz_FyQr_Date=getdate(),Fy_JsrCode='" & HisVar.HisVar.JsrCode & "'  where Mz_Code In (Select Mz_Code From Mz_Xm) And Mz_Code not In (Select Mz_Code From Mz_Yp) And Mz_Code='" & Rrow.Item("Mz_Code") & "'") = 0 Then

                Else
                    Rrow("Mz_FyQr") = True
                    Rrow("Mz_FyQr1") = "是"
                End If

                Rrow("Mz_Print") = True
                Rrow("Mz_Print1") = "是"
                Rrow.Item("Mz_SsMoney") = Me.C1NumericEdit2.Value
                Rrow.Item("Mz_ThMoney") = Me.C1NumericEdit3.Value


                Call BdInser_Ybzl()

                Me.Close()
                Exit Sub
        End Select

        'Dim StiRpt As New StiReport
        'Sqldal.QueryDt(My_DataSet, "Select Mz.Mz_Code,Ry_Name,Ry_Address,Ry_YlCode,Mz_Ph,Ks_Name,Mz_Dl As Mz_Lb,Sum(Mz_Yp.Mz_Money) As Mz_Money From Mz_Yp,Mz,Zd_YyKs Where Mz_Yp.Mz_Code=Mz.Mz_Code And Mz.Ks_Code=Zd_YyKs.Ks_Code And Mz.Mz_Code='" & Rcode & "' Group by Mz.Mz_Code,Ry_Name,Ry_Address,Ry_YlCode,Ks_Name,Mz_Ph,Mz_Dl Union All Select Mz.Mz_Code,Ry_Name,Ry_Address,Ry_YlCode,Mz_Ph,Ks_Name,Mz_Dl AS Mz_Lb,Sum(Mz_Xm.Mz_Money) As Mz_Money From Mz_Xm,Mz,Zd_YyKs Where Mz_Xm.Mz_Code=Mz.Mz_Code And Mz.Ks_Code=Zd_YyKs.Ks_Code And Mz.Mz_Code='" & Rcode & "' Group by Mz.Mz_Code,Ry_Name,Ry_Address,Ks_Name,Mz_Ph,Ry_YlCode,Mz_Dl", "门诊发票", True)

        'Dim mz_top As Double = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\中软智通", "门诊上边距", Nothing)
        'Dim mz_bottom As Double = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\中软智通", "门诊下边距", Nothing)
        'Dim mz_left As Double = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\中软智通", "门诊左边距", Nothing)

        'StiRpt.Load(".\Rpt\唐山门诊发票.mrt")
        'StiRpt.ReportName = "唐山门诊发票"

        'StiRpt.Pages(0).Margins.Left = mz_left + 1.1
        'StiRpt.Pages(0).Margins.Top = mz_top
        'StiRpt.Pages(0).Margins.Bottom = mz_bottom

        'StiRpt.Pages(0).PageHeight = mz_top + mz_bottom + 9.9
        'StiRpt.Pages(0).PageWidth = mz_left + 1.2 + 1.2 + 21
        'StiRpt.RegData(My_DataSet.Tables("门诊发票"))

        'StiRpt.Compile()

        'StiRpt("医院名称") = HisVar.HisVar.WsyName
        'StiRpt("开票员") = HisVar.HisVar.JsrName
        'StiRpt("日期") = Format(Rrow("Mz_Date"), "yyyy-MM-dd")

        ''StiRpt.Show()

        'Dim vForm As New PublicForm.PrintForm(Me.Name, "门诊录入打印", Nothing, Rrow)
        'StiRpt.Render()
        'vForm.StiViewerControl1.Report = StiRpt

        Dim mz_top As Double = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\中软智通", "门诊上边距", Nothing)
        Dim mz_bottom As Double = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\中软智通", "门诊下边距", Nothing)
        Dim mz_left As Double = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\中软智通", "门诊左边距", Nothing)


        HisVar.HisVar.Sqldal.QueryDt(My_DataSet, "Select '0' as Fp_Id,Mz.Mz_Code,Ry_Name,Ry_Address,Ry_YlCode,Mz_Ph,Ks_Name,Mz_Dl As Mz_Lb,Sum(Mz_Yp.Mz_Money) As Mz_Money From Mz_Yp,Mz,Zd_YyKs Where Mz_Yp.Mz_Code=Mz.Mz_Code And Mz.Ks_Code=Zd_YyKs.Ks_Code And Mz.Mz_Code='" & Rrow.Item("Mz_Code") & "' Group by Mz.Mz_Code,Ry_Name,Ry_Address,Ry_YlCode,Ks_Name,Mz_Ph,Mz_Dl Union All Select '0' as Fp_Id ,Mz.Mz_Code,Ry_Name,Ry_Address,Ry_YlCode,Mz_Ph,Ks_Name,Mz_Dl AS Mz_Lb,Sum(Mz_Xm.Mz_Money) As Mz_Money From Mz_Xm,Mz,Zd_YyKs Where Mz_Xm.Mz_Code=Mz.Mz_Code And Mz.Ks_Code=Zd_YyKs.Ks_Code And Mz.Mz_Code='" & Rrow.Item("Mz_Code") & "' Group by Mz.Mz_Code,Ry_Name,Ry_Address,Ks_Name,Mz_Ph,Ry_YlCode,Mz_Dl", "收据明细", True)
        Dim vform As New PublicForm.Pr_Cfj(Me.Name, "门诊录入打印", Nothing, Rrow)

        If IIf(iniOperate.iniopreate.GetINI("打印选项", "收据打印方式", "", HisVar.HisVar.Parapath & "\Config.ini") & "" = "", "每一大类打印一张收据", iniOperate.iniopreate.GetINI("打印选项", "收据打印方式", "", HisVar.HisVar.Parapath & "\Config.ini")) = "每一大类打印一张收据" Then
            Dim Rpt As New Ar_Mz_Print

            With Rpt
                .PageSettings.PaperKind = Printing.PaperKind.Custom

                .PageSettings.Margins.Left = mz_left
                .PageSettings.Margins.Right = 0
                .PageSettings.Margins.Top = mz_top
                .PageSettings.Margins.Bottom = mz_bottom
                .PageSettings.PaperHeight = 2.646 + mz_top + mz_bottom + 0.01
                '.PageSettings.PaperHeight = Rpt.Detail.Height + mz_top + 0.01 + mz_bottom
                .PageSettings.PaperWidth = Rpt.PrintWidth + mz_left + 0.1
                .A1.Text = HisVar.HisVar.WsyName
                .A10.Text = HisVar.HisVar.JsrName
                .A11.Text = Format(Rrow("Mz_Date"), "yyyy-MM-dd")
                .B1.Text = HisVar.HisVar.WsyName
                .B10.Text = HisVar.HisVar.JsrName
                .B11.Text = Format(Rrow("Mz_Date"), "yyyy-MM-dd")
                .C1.Text = HisVar.HisVar.WsyName
                .C10.Text = HisVar.HisVar.JsrName
                .C11.Text = Format(Rrow("Mz_Date"), "yyyy-MM-dd")
            End With

            '获取指定打印机
            Rpt.Document.Printer.PrinterName = IIf(iniOperate.iniopreate.GetINI("打印机设置", "门诊打印机", "", HisVar.HisVar.Parapath & "\Config.ini") & "" = "", Rpt.Document.Printer.PrinterName, iniOperate.iniopreate.GetINI("打印机设置", "门诊打印机", "", HisVar.HisVar.Parapath & "\Config.ini"))



            Rpt.DataSource = My_DataSet.Tables("收据明细")

            vform.Viewer1.Document = Rpt.Document
            Rpt.Run()
        Else

            Dim Rpt As New Ar_Mz_Print_Hz
            If My_DataSet.Tables("收据明细").Rows.Count Mod 5 <> 0 Then
                Dim NewRow As DataRow
                Dim K As Integer
                For K = 1 To 5 - My_DataSet.Tables("收据明细").Rows.Count Mod 5
                    NewRow = My_DataSet.Tables("收据明细").NewRow
                    NewRow.Item("Mz_Code") = Rrow.Item("Mz_Code")
                    NewRow.Item("Ry_Name") = ""
                    NewRow.Item("Ry_Address") = ""
                    NewRow.Item("Ry_YlCode") = ""
                    NewRow.Item("Mz_Ph") = ""
                    NewRow.Item("Ks_Name") = ""
                    NewRow.Item("Mz_Lb") = ""
                    NewRow.Item("Mz_Money") = DBNull.Value
                    My_DataSet.Tables("收据明细").Rows.Add(NewRow)
                Next

            End If
            Dim V_ColId As Integer
            My_DataSet.Tables("收据明细").Columns("Fp_Id").ReadOnly = False
            V_ColId = BaseFunc.BaseFunc.Edit_Col0(My_DataSet.Tables("收据明细"), 0, 5)

            With Rpt
                .PageSettings.PaperKind = Printing.PaperKind.Custom

                .PageSettings.Margins.Left = mz_left
                .PageSettings.Margins.Right = 0
                .PageSettings.Margins.Top = mz_top
                .PageSettings.Margins.Bottom = mz_bottom
                .PageSettings.PaperHeight = 2.646 + mz_top + mz_bottom + 0.01
                '.PageSettings.PaperHeight = Rpt.Detail.Height + mz_top + 0.01 + mz_bottom
                .PageSettings.PaperWidth = Rpt.PrintWidth + mz_left + 0.1
                .A1.Text = HisVar.HisVar.WsyName
                .A10.Text = HisVar.HisVar.JsrName
                .A11.Text = Format(Rrow("Mz_Date"), "yyyy-MM-dd")
                .B1.Text = HisVar.HisVar.WsyName
                .B10.Text = HisVar.HisVar.JsrName
                .B11.Text = Format(Rrow("Mz_Date"), "yyyy-MM-dd")
                .C1.Text = HisVar.HisVar.WsyName
                .C10.Text = HisVar.HisVar.JsrName
                .C11.Text = Format(Rrow("Mz_Date"), "yyyy-MM-dd")
            End With

            '获取指定打印机
            Rpt.Document.Printer.PrinterName = IIf(iniOperate.iniopreate.GetINI("打印机设置", "门诊打印机", "", HisVar.HisVar.Parapath & "\Config.ini") & "" = "", Rpt.Document.Printer.PrinterName, iniOperate.iniopreate.GetINI("打印机设置", "门诊打印机", "", HisVar.HisVar.Parapath & "\Config.ini"))


            Rpt.DataSource = My_DataSet.Tables("收据明细")

            vform.Viewer1.Document = Rpt.Document
            Rpt.Run()
        End If




        Select Case Text
            Case "打印预览" '详单打印
                vform.ShowDialog()
            Case "快速打印"
                vform.Viewer1.Document.Print(False, False)
                '  vForm.StiViewerControl1.Report.Print(False, False)
                HisVar.HisVar.Sqldal.ExecuteSql("Update Mz Set Mz_Print='1' Where Mz_Code='" & Rrow.Item("Mz_Code") & "'")
                If HisVar.HisVar.Sqldal.ExecuteSql("Update Mz Set Mz_FyQr='1',Mz_FyQr_Date=getdate(),Fy_JsrCode='" & HisVar.HisVar.JsrCode & "'  where Mz_Code In (Select Mz_Code From Mz_Xm) And Mz_Code not In (Select Mz_Code From Mz_Yp) And Mz_Code='" & Rrow.Item("Mz_Code") & "'") = 0 Then

                Else
                    Rrow("Mz_FyQr") = True
                    Rrow("Mz_FyQr1") = "是"
                End If
                Rrow("Mz_Print") = True
                Rrow("Mz_Print1") = "是"

        End Select
        Rrow.Item("Mz_SsMoney") = Me.C1NumericEdit2.Value
        Rrow.Item("Mz_ThMoney") = Me.C1NumericEdit3.Value
        Call BdInser_Ybzl()
        Me.Close()
    End Sub

    Private Sub Qz_Fp(ByVal Text As String)


        HisVar.HisVar.Sqldal.ExecuteSql("Update Mz Set Mz_SsMoney=" & Me.C1NumericEdit2.Value & ",Mz_ThMoney=" & Me.C1NumericEdit3.Value & " Where Mz_Code='" & Rrow.Item("Mz_Code") & "'")

        HisVar.HisVar.Sqldal.ExecuteSql("Update Mz_Yp Set Mz_Ph='" & Rrow.Item("Mz_Code") & "' Where Mz_Code='" & Rrow.Item("Mz_Code") & "' ")

        HisVar.HisVar.Sqldal.ExecuteSql("Update Mz_Xm Set Mz_Ph='" & Rrow.Item("Mz_Code") & "' Where Mz_Code='" & Rrow.Item("Mz_Code") & "' ")

        Select Case Text
            Case "确认不打印"
                HisVar.HisVar.Sqldal.ExecuteSql("Update Mz Set Mz_Print='1' Where Mz_Code='" & Rrow.Item("Mz_Code") & "'")
                If HisVar.HisVar.Sqldal.ExecuteSql("Update Mz Set Mz_FyQr='1',Mz_FyQr_Date=getdate(),Fy_JsrCode='" & HisVar.HisVar.JsrCode & "'  where Mz_Code In (Select Mz_Code From Mz_Xm) And Mz_Code not In (Select Mz_Code From Mz_Yp) And Mz_Code='" & Rrow.Item("Mz_Code") & "'") = 0 Then

                Else
                    Rrow("Mz_FyQr") = True
                    Rrow("Mz_FyQr1") = "是"
                End If
                Rrow("Mz_Print") = True
                Rrow("Mz_Print1") = "是"
                Rrow.Item("Mz_SsMoney") = Me.C1NumericEdit2.Value
                Rrow.Item("Mz_ThMoney") = Me.C1NumericEdit3.Value
                Me.Close()
                Exit Sub
        End Select



        Dim StiRpt As New StiReport
        Dim Fp_Row As DataRow
        Dim Qt_Money As Double = 0.0

        Dim mz_top As Double = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\中软智通", "门诊上边距", Nothing)
        Dim mz_bottom As Double = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\中软智通", "门诊下边距", Nothing)
        Dim mz_left As Double = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\中软智通", "门诊左边距", Nothing)

        StiRpt.Load(".\Rpt\邯郸曲周门诊票据.mrt")
        StiRpt.ReportName = "邯郸曲周门诊票据"

        StiRpt.Pages(0).Margins.Left = mz_left + 2.7
        StiRpt.Pages(0).Margins.Top = mz_top
        StiRpt.Pages(0).Margins.Bottom = mz_bottom

        StiRpt.Pages(0).PageHeight = mz_top + mz_bottom + 14.5
        StiRpt.Pages(0).PageWidth = mz_left + 2.7 + 2.1 + 14.5
        '获取指定打印机
        StiRpt.PrinterSettings.PrinterName = IIf(iniOperate.iniopreate.GetINI("打印机设置", "门诊打印机", "", HisVar.HisVar.Parapath & "\Config.ini") & "" = "", StiRpt.PrinterSettings.PrinterName, iniOperate.iniopreate.GetINI("打印机设置", "门诊打印机", "", HisVar.HisVar.Parapath & "\Config.ini"))

        StiRpt.Compile()
        StiRpt("医院名称") = HisVar.HisVar.WsyName
        StiRpt("科别") = Rrow("Ks_Name")
        StiRpt("医生") = Rrow("Ys_Name")
        StiRpt("姓名") = Rrow("Ry_Name")
        StiRpt("经手人") = "经手人:" & HisVar.HisVar.JsrCode
        HisVar.HisVar.Sqldal.QueryDt(My_DataSet, "Select Sum(Mz_Money)Mz_Money,Mz_lb from Mz_Yp where  Mz_Code='" & Rrow.Item("Mz_Code") & "' group by Mz_lb  ", "邯郸曲周发票药品卫材", True)
        For Each Fp_Row In My_DataSet.Tables("邯郸曲周发票药品卫材").Rows
            Select Case Fp_Row.Item("Mz_lb")
                Case "西药"
                    StiRpt("西药") = Format(Fp_Row.Item("Mz_Money"), "0.00")
                Case "中成药"
                    StiRpt("中成") = Format(Fp_Row.Item("Mz_Money"), "0.00")
                Case "中草药"
                    StiRpt("中草") = Format(Fp_Row.Item("Mz_Money"), "0.00")
                Case "卫生材料"
                    Qt_Money = Format(Fp_Row.Item("Mz_Money"), "0.00")
            End Select

        Next
        HisVar.HisVar.Sqldal.QueryDt(My_DataSet, "Select Sum(Mz_Money)Mz_Money,Lb_Name from Mz_Xm,Zd_Jkfl1,Zd_Jkfl2 where Zd_Jkfl1.Lb_Code=Zd_Jkfl2.Lb_Code and Mz_Xm.Xm_Code=Zd_Jkfl2.Mx_Code and  Mz_Code='" & Rrow.Item("Mz_Code") & "' group by Lb_Name", "邯郸曲周发票诊疗项目", True)
        For Each Fp_Row In My_DataSet.Tables("邯郸曲周发票诊疗项目").Rows
            Select Case Fp_Row.Item("Lb_Name")
                Case "检查"
                    StiRpt("检查") = Format(Fp_Row.Item("Mz_Money"), "0.00")
                Case "治疗"
                    StiRpt("治疗") = Format(Fp_Row.Item("Mz_Money"), "0.00")
                Case "放射"
                    StiRpt("X光") = Format(Fp_Row.Item("Mz_Money"), "0.00")
                Case "手术"
                    StiRpt("手术") = Format(Fp_Row.Item("Mz_Money"), "0.00")
                Case "化验"
                    StiRpt("化验") = Format(Fp_Row.Item("Mz_Money"), "0.00")
                Case "血费"
                    StiRpt("输血") = Format(Fp_Row.Item("Mz_Money"), "0.00")
                Case "氧气"
                    StiRpt("输氧") = Format(Fp_Row.Item("Mz_Money"), "0.00")
                Case Else
                    Qt_Money = Qt_Money + Format(Fp_Row.Item("Mz_Money"), "0.00")
            End Select

        Next
        StiRpt("其它") = Format(Qt_Money, "0.00")
        StiRpt("门诊编码") = "编码:" & Rrow.Item("Mz_Code")
        StiRpt("打印时间") = Format(Now, "yyyy-MM-dd HH:mm:ss")


        Dim H_Money As Double = HisVar.HisVar.Sqldal.GetSingle("Select Sum(Mz_Money) From Mz where Mz_Code='" & Rrow.Item("Mz_Code") & "'")
        StiRpt("合计") = Format(H_Money, "0.00")

        Dim Money_Dx As New BaseClass.ChineseNum
        If H_Money >= 0 Then
            Money_Dx.InputString = H_Money
        Else
            Money_Dx.InputString = -H_Money
        End If

        If Money_Dx.Valiad = True Then
            If H_Money >= 0 Then
                StiRpt("大写") = Money_Dx.OutString

            Else
                StiRpt("大写") = "负" & Money_Dx.OutString

            End If
        Else
            MsgBox("输入金额有误,请检查后重新打印", MsgBoxStyle.Critical, "提示")
            Exit Sub
        End If



        'Dim vform As New MzSf_Print(Me, Rrow, Rcode, "打印")
        Dim vForm As New PublicForm.PrintForm(Me.Name, "门诊录入打印", Nothing, Rrow)
        StiRpt.Render()
        vForm.StiViewerControl1.Report = StiRpt
        'StiRpt.Design()

        Select Case Text
            Case "打印预览" '详单打印
                vForm.ShowDialog()
                Me.Close()
            Case "快速打印"
                vForm.StiViewerControl1.Report.Print(False, False)
                HisVar.HisVar.Sqldal.ExecuteSql("Update Mz Set Mz_Print='1' Where Mz_Code='" & Rrow.Item("Mz_Code") & "'")
                If HisVar.HisVar.Sqldal.ExecuteSql("Update Mz Set Mz_FyQr='1',Mz_FyQr_Date=getdate(),Fy_JsrCode='" & HisVar.HisVar.JsrCode & "'  where Mz_Code In (Select Mz_Code From Mz_Xm) And Mz_Code not In (Select Mz_Code From Mz_Yp) And Mz_Code='" & Rrow.Item("Mz_Code") & "'") = 0 Then

                Else
                    Rrow("Mz_FyQr") = True
                    Rrow("Mz_FyQr1") = "是"
                End If
                Rrow("Mz_Print") = True
                Rrow("Mz_Print1") = "是"
                Me.Close()

        End Select
        Rrow.Item("Mz_SsMoney") = Me.C1NumericEdit2.Value
        Rrow.Item("Mz_ThMoney") = Me.C1NumericEdit3.Value

    End Sub

    Private Sub Tl_Fp(ByVal Text As String)

        HisVar.HisVar.Sqldal.ExecuteSql("Update Mz Set Mz_SsMoney=" & Me.C1NumericEdit2.Value & ",Mz_ThMoney=" & Me.C1NumericEdit3.Value & " Where Mz_Code='" & Rrow.Item("Mz_Code") & "'")

        HisVar.HisVar.Sqldal.ExecuteSql("Update Mz_Yp Set Mz_Ph='" & Rrow.Item("Mz_Code") & "' Where Mz_Code='" & Rrow.Item("Mz_Code") & "' ")

        HisVar.HisVar.Sqldal.ExecuteSql("Update Mz_Xm Set Mz_Ph='" & Rrow.Item("Mz_Code") & "' Where Mz_Code='" & Rrow.Item("Mz_Code") & "' ")

        Select Case Text
            Case "确认不打印"
                HisVar.HisVar.Sqldal.ExecuteSql("Update Mz Set Mz_Print='1' Where Mz_Code='" & Rrow.Item("Mz_Code") & "'")
                If HisVar.HisVar.Sqldal.ExecuteSql("Update Mz Set Mz_FyQr='1',Mz_FyQr_Date=getdate(),Fy_JsrCode='" & HisVar.HisVar.JsrCode & "'  where Mz_Code In (Select Mz_Code From Mz_Xm) And Mz_Code not In (Select Mz_Code From Mz_Yp) And Mz_Code='" & Rrow.Item("Mz_Code") & "'") = 0 Then

                Else
                    Rrow("Mz_FyQr") = True
                    Rrow("Mz_FyQr1") = "是"
                End If
                Rrow("Mz_Print") = True
                Rrow("Mz_Print1") = "是"
                Rrow.Item("Mz_SsMoney") = Me.C1NumericEdit2.Value
                Rrow.Item("Mz_ThMoney") = Me.C1NumericEdit3.Value
                Me.Close()
                Exit Sub
        End Select

        Dim StiRpt As New StiReport
        'Dim Fp_Row As DataRow
        'Dim Qt_Money As Double = 0.0

        Dim mz_top As Double = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\中软智通", "门诊上边距", Nothing)
        Dim mz_bottom As Double = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\中软智通", "门诊下边距", Nothing)
        Dim mz_left As Double = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\中软智通", "门诊左边距", Nothing)




        StiRpt.Load(".\Rpt\内蒙古通辽门诊票据.mrt")
        StiRpt.ReportName = "内蒙古通辽门诊票据"

        StiRpt.Pages(0).Margins.Left = mz_left + 1
        StiRpt.Pages(0).Margins.Top = mz_top
        StiRpt.Pages(0).Margins.Bottom = mz_bottom

        'StiRpt.Pages(0).PageHeight = mz_top + mz_bottom + 14.5
        'StiRpt.Pages(0).PageWidth = mz_left + 1.5 + 1.5 + 14.5
        '获取指定打印机
        StiRpt.PrinterSettings.PrinterName = IIf(iniOperate.iniopreate.GetINI("打印机设置", "门诊打印机", "", HisVar.HisVar.Parapath & "\Config.ini") & "" = "", StiRpt.PrinterSettings.PrinterName, iniOperate.iniopreate.GetINI("打印机设置", "门诊打印机", "", HisVar.HisVar.Parapath & "\Config.ini"))

        StiRpt.Compile()
        StiRpt("医院名称") = HisVar.HisVar.WsyName
        StiRpt("科别") = Rrow("Ks_Name")
        StiRpt("医生") = Rrow("Ys_Name")
        StiRpt("姓名") = Rrow("Ry_Name")
        StiRpt("经手人") = HisVar.HisVar.JsrName
        StiRpt("门诊编码") = Rrow.Item("Mz_Code")
        StiRpt("打印时间") = Format(Now, "yyyy-MM-dd HH:mm:ss")
        StiRpt("应收") = C1NumericEdit1.Text
        StiRpt("实收") = C1NumericEdit2.Text
        StiRpt("退找") = C1NumericEdit3.Text


        HisVar.HisVar.Sqldal.QueryDt(My_DataSet, "Select Sum(Mz_Money)Mz_Money,Mz_lb from Mz_Yp where  Mz_Code='" & Rrow.Item("Mz_Code") & "' group by Mz_lb  Union all Select Sum(Mz_Money)Mz_Money,Lb_Name from Mz_Xm,Zd_Jkfl1,Zd_Jkfl2 where Zd_Jkfl1.Lb_Code=Zd_Jkfl2.Lb_Code and Mz_Xm.Xm_Code=Zd_Jkfl2.Mx_Code and  Mz_Code='" & Rrow.Item("Mz_Code") & "' group by Lb_Name ", "内蒙古通辽门诊票据", True)

        '将门诊票据打印明细设定为固定个数
        Dim V_Newrow As DataRow

        Dim Tbrowcount As Integer = My_DataSet.Tables("内蒙古通辽门诊票据").Rows.Count
        If Tbrowcount Mod 8 <> 0 Then
            For V_TbRowCount = 1 To 8 - (Tbrowcount Mod 8)
                V_Newrow = My_DataSet.Tables("内蒙古通辽门诊票据").NewRow
                With V_Newrow
                    .Item(0) = DBNull.Value
                    .Item(1) = DBNull.Value
                End With
                My_DataSet.Tables("内蒙古通辽门诊票据").Rows.Add(V_Newrow)
                V_Newrow.AcceptChanges()
            Next
        End If

        StiRpt.RegData(My_DataSet.Tables("内蒙古通辽门诊票据"))

        Dim Money_Dx As New BaseClass.ChineseNum
        If C1NumericEdit1.Text >= 0 Then
            Money_Dx.InputString = C1NumericEdit1.Text
        Else
            Money_Dx.InputString = -C1NumericEdit1.Text
        End If

        If Money_Dx.Valiad = True Then
            If C1NumericEdit1.Text >= 0 Then
                StiRpt("大写") = Money_Dx.OutString

            Else
                StiRpt("大写") = "负" & Money_Dx.OutString

            End If
        Else
            MsgBox("输入金额有误,请检查后重新打印", MsgBoxStyle.Critical, "提示")
            Exit Sub
        End If


        'Dim vform As New MzSf_Print(Me, Rrow, Rcode, "打印")
        Dim vForm As New PublicForm.PrintForm(Me.Name, "门诊录入打印", Nothing, Rrow)
        StiRpt.Render()
        vForm.StiViewerControl1.Report = StiRpt
        Select Case Text
            Case "打印预览" '详单打印
                'StiRpt.Design()
                vForm.ShowDialog()
                Me.Close()
            Case "快速打印"
                vForm.StiViewerControl1.Report.Print(False, False)
                HisVar.HisVar.Sqldal.ExecuteSql("Update Mz Set Mz_Print='1' Where Mz_Code='" & Rrow.Item("Mz_Code") & "'")
                If HisVar.HisVar.Sqldal.ExecuteSql("Update Mz Set Mz_FyQr='1',Mz_FyQr_Date=getdate(),Fy_JsrCode='" & HisVar.HisVar.JsrCode & "'  where Mz_Code In (Select Mz_Code From Mz_Xm) And Mz_Code not In (Select Mz_Code From Mz_Yp) And Mz_Code='" & Rrow.Item("Mz_Code") & "'") = 0 Then

                Else
                    Rrow("Mz_FyQr") = True
                    Rrow("Mz_FyQr1") = "是"
                End If
                Rrow("Mz_Print") = True
                Rrow("Mz_Print1") = "是"
                Me.Close()

        End Select
        Rrow.Item("Mz_SsMoney") = Me.C1NumericEdit2.Value
        Rrow.Item("Mz_ThMoney") = Me.C1NumericEdit3.Value


    End Sub

    Private Sub Cc_Fp(ByVal Text As String)

        HisVar.HisVar.Sqldal.ExecuteSql("Update Mz Set Mz_SsMoney=" & Me.C1NumericEdit2.Value & ",Mz_ThMoney=" & Me.C1NumericEdit3.Value & " Where Mz_Code='" & Rrow.Item("Mz_Code") & "'")

        Select Case Text
            Case "确认不打印"
                HisVar.HisVar.Sqldal.ExecuteSql("Update Mz Set Mz_Print='1' Where Mz_Code='" & Rrow.Item("Mz_Code") & "'")
                If HisVar.HisVar.Sqldal.ExecuteSql("Update Mz Set Mz_FyQr='1',Mz_FyQr_Date=getdate(),Fy_JsrCode='" & HisVar.HisVar.JsrCode & "'  where Mz_Code In (Select Mz_Code From Mz_Xm) And Mz_Code not In (Select Mz_Code From Mz_Yp) And Mz_Code='" & Rrow.Item("Mz_Code") & "'") = 0 Then

                Else
                    Rrow("Mz_FyQr") = True
                    Rrow("Mz_FyQr1") = "是"
                End If
                Rrow("Mz_Print") = True
                Rrow("Mz_Print1") = "是"
                Rrow.Item("Mz_SsMoney") = Me.C1NumericEdit2.Value
                Rrow.Item("Mz_ThMoney") = Me.C1NumericEdit3.Value
                Me.Close()
                Exit Sub
        End Select

        Dim StiRpt As New StiReport              '打所有门诊数据

        Dim mz_top As Double = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\中软智通", "门诊上边距", Nothing)
        Dim mz_bottom As Double = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\中软智通", "门诊下边距", Nothing)
        Dim mz_left As Double = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\中软智通", "门诊左边距", Nothing)
        Dim DaXie As String

        StiRpt.Load(".\Rpt\吉林省医疗机构门诊收费专用票据.mrt")
        StiRpt.ReportName = "吉林省医疗机构门诊收费专用票据"

        StiRpt.Pages(0).Margins.Left = mz_left
        StiRpt.Pages(0).Margins.Top = mz_top
        StiRpt.Pages(0).Margins.Bottom = mz_bottom
        '获取指定打印机
        StiRpt.PrinterSettings.PrinterName = IIf(iniOperate.iniopreate.GetINI("打印机设置", "门诊打印机", "", HisVar.HisVar.Parapath & "\Config.ini") & "" = "", StiRpt.PrinterSettings.PrinterName, iniOperate.iniopreate.GetINI("打印机设置", "门诊打印机", "", HisVar.HisVar.Parapath & "\Config.ini"))
        'StiRpt.Design()
        StiRpt.Compile()
        StiRpt("医院名称") = HisVar.HisVar.WsyName
        StiRpt("科别") = Rrow("Ks_Name")
        StiRpt("医生") = Rrow("Ys_Name")
        StiRpt("患者类别") = Rrow("Bxlb_Name")

        StiRpt("患者姓名") = Rrow("Ry_Name")
        StiRpt("患者姓名1") = Rrow("Ry_Name")
        StiRpt("患者姓名2") = Rrow("Ry_Name")
        StiRpt("患者姓名3") = Rrow("Ry_Name")

        StiRpt("打印编号") = Rrow.Item("Mz_Code")
        StiRpt("打印编号1") = Rrow.Item("Mz_Code")
        StiRpt("打印编号2") = Rrow.Item("Mz_Code")
        StiRpt("打印编号3") = Rrow.Item("Mz_Code")

        StiRpt("打印时间") = Now
        StiRpt("打印时间1") = Now
        StiRpt("打印时间2") = Now
        StiRpt("打印时间3") = Now

        StiRpt("收款员") = HisVar.HisVar.JsrName
        StiRpt("应收金额") = CDbl(C1NumericEdit1.Text)

        '转化成大写
        If C1NumericEdit1.Text >= 0 Then
            Money_Dx.InputString = C1NumericEdit1.Text
        Else
            Money_Dx.InputString = -C1NumericEdit1.Text
        End If

        If Money_Dx.Valiad = True Then
            If C1NumericEdit1.Text >= 0 Then
                DaXie = Money_Dx.OutString
            Else
                DaXie = "负" & Money_Dx.OutString
            End If
        Else
            MsgBox("输入金额有误,请检查后重新打印", MsgBoxStyle.Critical, "提示")
            Exit Sub
        End If

        StiRpt("应收金额大写") = DaXie




        HisVar.HisVar.Sqldal.QueryDt(My_DataSet, "Select  Mz_Lb,Sum(Mz_Money) AS Mz_Money From Mz_Yp  Where  Mz_Code='" & Rrow.Item("Mz_Code") & "' Group By Mz_Lb", "药品类别", True)
        HisVar.HisVar.Sqldal.QueryDt(My_DataSet, "Select  Lb_Name,Sum(Mz_Money) AS Mz_Money From Mz_Xm,Zd_MzFp1,Zd_MzFp2 Where Zd_MzFp2.Yy_Code=Mz_Xm.Yy_Code And Mz_Xm.Xm_Code=Zd_MzFp2.Xm_Code And Zd_MzFp1.Lb_Code=Zd_MzFp2.Lb_Code And Mz_Code='" & Rrow.Item("Mz_Code") & "' Group By Lb_Name", "服务类别", True)
        HisVar.HisVar.Sqldal.QueryDt(My_DataSet, "Select Fl_Code,Fl_Name  from Zd_Mzfp3 where Yy_Code='" & HisVar.HisVar.WsyCode & "' ", "科室类别", True)

        Dim XM As Double = 0
        Dim WM As Double = 0
        Dim CzM As Double = 0
        Dim H_Money As Double = 0

        For Each my_row In My_DataSet.Tables("药品类别").Rows
            Select Case my_row("Mz_Lb")
                Case "西药"
                    StiRpt("西药费") = CDbl(StiRpt("西药费") + my_row.Item("Mz_Money"))
                    XM = CDbl(XM + my_row("Mz_Money"))
                Case "中成药"
                    StiRpt("中成药") = CDbl(my_row.Item("Mz_Money"))
                Case "中草药"
                    StiRpt("中草药") = CDbl(my_row.Item("Mz_Money"))
                Case "卫生材料"
                    StiRpt("西药费") = CDbl(StiRpt("西药费") + my_row.Item("Mz_Money"))
                    WM = CDbl(WM + my_row("Mz_Money"))
            End Select
            'H_Money = CDbl(H_Money + my_row("Mz_Money"))
        Next

        For Each my_row In My_DataSet.Tables("服务类别").Rows
            Select Case my_row("Lb_Name")
                Case "化验费"
                    StiRpt("化验费") = CDbl(my_row.Item("Mz_Money"))
                Case "X光费"
                    StiRpt("X光费") = CDbl(my_row.Item("Mz_Money"))
                Case "电诊费"
                    StiRpt("电诊费") = CDbl(my_row.Item("Mz_Money"))
                Case "CT费"
                    StiRpt("CT费") = CDbl(my_row.Item("Mz_Money"))
                Case "磁共振"
                    StiRpt("磁共振") = CDbl(my_row.Item("Mz_Money"))
                Case "检查费"
                    StiRpt("检查费") = CDbl(my_row.Item("Mz_Money"))
                Case "手术费"
                    StiRpt("手术费") = CDbl(my_row.Item("Mz_Money"))
                Case "输氧费"
                    StiRpt("输氧费") = CDbl(my_row.Item("Mz_Money"))
                Case "输血费"
                    StiRpt("输血费") = CDbl(my_row.Item("Mz_Money"))
                Case "处置费"
                    StiRpt("处置费") = CDbl(my_row.Item("Mz_Money"))
                    CzM = CDbl(CzM + my_row("Mz_Money"))
                Case "注射费"
                    StiRpt("注射费") = CDbl(my_row.Item("Mz_Money"))
                Case "治疗费"
                    StiRpt("治疗费") = CDbl(my_row.Item("Mz_Money"))
            End Select
            'H_Money = CDbl(H_Money + my_row("Mz_Money"))
        Next

        Dim Row_I As Integer
        Dim Row_H As Integer
        For Row_I = 0 To My_DataSet.Tables("科室类别").Rows.Count - 1
            If My_DataSet.Tables("类别") IsNot Nothing Then My_DataSet.Tables("类别").Clear()
            With My_Adapter
                .SelectCommand = New SqlCommand("Select  Fl_Name,Mc_name,Mc_Code From Zd_MzFp3,Zd_MzFp31 Where Zd_MzFp31.Fl_Code=Zd_MzFp3.Fl_Code and Zd_MzFp31.Fl_Code='" & My_DataSet.Tables("科室类别").Rows(Row_I).Item("Fl_Code") & "' and Zd_MzFp31.Yy_Code='" & HisVar.HisVar.WsyCode & "'    ", My_Cn)
                .Fill(My_DataSet, "类别")
            End With

            Select Case Row_I
                Case 0
                    StiRpt("科室1") = My_DataSet.Tables("科室类别").Rows(0).Item("Fl_Name")

                    For Row_H = 0 To My_DataSet.Tables("类别").Rows.Count - 1
                        If My_DataSet.Tables("科室类别明细") IsNot Nothing Then My_DataSet.Tables("科室类别明细").Clear()
                        With My_Adapter
                            .SelectCommand = New SqlCommand("Select  Mc_Name,Lb_name From Zd_MzFp31,Zd_MzFp4,Zd_MzFp1 Where Zd_MzFp31.Mc_Code='" & My_DataSet.Tables("类别").Rows(Row_H).Item("Mc_Code") & "' And Zd_MzFp31.Mc_Code=Zd_MzFp4.Mc_Code And Zd_MzFp1.Lb_Code=Zd_MzFp4.Lb_Code and Zd_MzFp31.Yy_Code=Zd_MzFp4.Yy_Code and Zd_MzFp31.Yy_Code='" & HisVar.HisVar.WsyCode & "'  union all Select  Mc_Name,Dl_name From Zd_MzFp31,Zd_MzFp4,Zd_Ml_Yp1 Where Zd_MzFp31.Mc_Code='" & My_DataSet.Tables("类别").Rows(Row_H).Item("Mc_Code") & "' And Zd_MzFp31.Mc_Code=Zd_MzFp4.Mc_Code And 'Y'+Zd_Ml_Yp1.Dl_Code=Zd_MzFp4.Lb_Code and Zd_MzFp31.Yy_Code=Zd_MzFp4.Yy_Code and Zd_MzFp31.Yy_Code='" & HisVar.HisVar.WsyCode & "'  ", My_Cn)
                            .Fill(My_DataSet, "科室类别明细")
                        End With
                        If My_DataSet.Tables("科室类别明细").Rows.Count <> 0 Then
                            Select Case Row_H
                                Case 0
                                    StiRpt("类别1") = My_DataSet.Tables("类别").Rows(0).Item("Mc_Name")
                                    For Each my_row In My_DataSet.Tables("科室类别明细").Rows
                                        Select Case my_row("Lb_name")
                                            Case "西药"
                                                StiRpt("类别金额1") = Val(StiRpt("类别金额1")) + XM
                                            Case "中成药"
                                                StiRpt("类别金额1") = Val(StiRpt("类别金额1")) + Val(StiRpt("中成药"))
                                            Case "中草药"
                                                StiRpt("类别金额1") = Val(StiRpt("类别金额1")) + Val(StiRpt("中草药"))
                                            Case "卫生材料"
                                                StiRpt("类别金额1") = Val(StiRpt("类别金额1")) + WM
                                            Case "化验费"
                                                StiRpt("类别金额1") = Val(StiRpt("类别金额1")) + Val(StiRpt("化验费"))
                                            Case "X光费"
                                                StiRpt("类别金额1") = Val(StiRpt("类别金额1")) + Val(StiRpt("X光费"))
                                            Case "电诊费"
                                                StiRpt("类别金额1") = Val(StiRpt("类别金额1")) + Val(StiRpt("电诊费"))
                                            Case "CT费"
                                                StiRpt("类别金额1") = Val(StiRpt("类别金额1")) + Val(StiRpt("CT费"))
                                            Case "磁共振"
                                                StiRpt("类别金额1") = Val(StiRpt("类别金额1")) + Val(StiRpt("磁共振"))
                                            Case "检查费"
                                                StiRpt("类别金额1") = Val(StiRpt("类别金额1")) + Val(StiRpt("检查费"))
                                            Case "手术费"
                                                StiRpt("类别金额1") = Val(StiRpt("类别金额1")) + Val(StiRpt("手术费"))
                                            Case "输氧费"
                                                StiRpt("类别金额1") = Val(StiRpt("类别金额1")) + Val(StiRpt("输氧费"))
                                            Case "输血费"
                                                StiRpt("类别金额1") = Val(StiRpt("类别金额1")) + Val(StiRpt("输血费"))
                                            Case "处置费"
                                                StiRpt("类别金额1") = Val(StiRpt("类别金额1")) + CzM
                                            Case "注射费"
                                                StiRpt("类别金额1") = Val(StiRpt("类别金额1")) + Val(StiRpt("注射费"))
                                            Case "治疗费"
                                                StiRpt("类别金额1") = Val(StiRpt("类别金额1")) + Val(StiRpt("治疗费"))
                                        End Select
                                    Next
                                Case 1
                                    StiRpt("类别2") = My_DataSet.Tables("类别").Rows(1).Item("Mc_Name")
                                    For Each my_row In My_DataSet.Tables("科室类别明细").Rows
                                        Select Case my_row("Lb_name")
                                            Case "西药"
                                                StiRpt("类别金额2") = Val(StiRpt("类别金额2")) + XM
                                            Case "中成药"
                                                StiRpt("类别金额2") = Val(StiRpt("类别金额2")) + Val(StiRpt("中成药"))
                                            Case "中草药"
                                                StiRpt("类别金额2") = Val(StiRpt("类别金额2")) + Val(StiRpt("中草药"))
                                            Case "卫生材料"
                                                StiRpt("类别金额2") = Val(StiRpt("类别金额2")) + WM
                                            Case "化验费"
                                                StiRpt("类别金额2") = Val(StiRpt("类别金额2")) + Val(StiRpt("化验费"))
                                            Case "X光费"
                                                StiRpt("类别金额2") = Val(StiRpt("类别金额2")) + Val(StiRpt("X光费"))
                                            Case "电诊费"
                                                StiRpt("类别金额2") = Val(StiRpt("类别金额2")) + Val(StiRpt("电诊费"))
                                            Case "CT费"
                                                StiRpt("类别金额2") = Val(StiRpt("类别金额2")) + Val(StiRpt("CT费"))
                                            Case "磁共振"
                                                StiRpt("类别金额2") = Val(StiRpt("类别金额2")) + Val(StiRpt("磁共振"))
                                            Case "检查费"
                                                StiRpt("类别金额2") = Val(StiRpt("类别金额2")) + Val(StiRpt("检查费"))
                                            Case "手术费"
                                                StiRpt("类别金额2") = Val(StiRpt("类别金额2")) + Val(StiRpt("手术费"))
                                            Case "输氧费"
                                                StiRpt("类别金额2") = Val(StiRpt("类别金额2")) + Val(StiRpt("输氧费"))
                                            Case "输血费"
                                                StiRpt("类别金额2") = Val(StiRpt("类别金额2")) + Val(StiRpt("输血费"))
                                            Case "处置费"
                                                StiRpt("类别金额2") = Val(StiRpt("类别金额2")) + CzM
                                            Case "注射费"
                                                StiRpt("类别金额2") = Val(StiRpt("类别金额2")) + Val(StiRpt("注射费"))
                                            Case "治疗费"
                                                StiRpt("类别金额2") = Val(StiRpt("类别金额2")) + Val(StiRpt("治疗费"))
                                        End Select
                                    Next
                            End Select
                            StiRpt("合计1") = StiRpt("类别金额1") + StiRpt("类别金额2")
                        End If
                    Next
                Case 1
                    StiRpt("科室2") = My_DataSet.Tables("科室类别").Rows(1).Item("Fl_Name")

                    For Row_H = 0 To My_DataSet.Tables("类别").Rows.Count - 1
                        If My_DataSet.Tables("科室类别明细") IsNot Nothing Then My_DataSet.Tables("科室类别明细").Clear()
                        With My_Adapter
                            .SelectCommand = New SqlCommand("Select  Mc_Name,Lb_name From Zd_MzFp31,Zd_MzFp4,Zd_MzFp1 Where Zd_MzFp31.Mc_Code='" & My_DataSet.Tables("类别").Rows(Row_H).Item("Mc_Code") & "' And Zd_MzFp31.Mc_Code=Zd_MzFp4.Mc_Code And Zd_MzFp1.Lb_Code=Zd_MzFp4.Lb_Code and Zd_MzFp31.Yy_Code=Zd_MzFp4.Yy_Code and Zd_MzFp31.Yy_Code='" & HisVar.HisVar.WsyCode & "'  union all Select  Mc_Name,Dl_name From Zd_MzFp31,Zd_MzFp4,Zd_Ml_Yp1 Where Zd_MzFp31.Mc_Code='" & My_DataSet.Tables("类别").Rows(Row_H).Item("Mc_Code") & "' And Zd_MzFp31.Mc_Code=Zd_MzFp4.Mc_Code And 'Y'+Zd_Ml_Yp1.Dl_Code=Zd_MzFp4.Lb_Code and Zd_MzFp31.Yy_Code=Zd_MzFp4.Yy_Code and Zd_MzFp31.Yy_Code='" & HisVar.HisVar.WsyCode & "'  ", My_Cn)
                            .Fill(My_DataSet, "科室类别明细")
                        End With
                        If My_DataSet.Tables("科室类别明细").Rows.Count <> 0 Then
                            Select Case Row_H
                                Case 0
                                    StiRpt("类别3") = My_DataSet.Tables("类别").Rows(0).Item("Mc_Name")
                                    For Each my_row In My_DataSet.Tables("科室类别明细").Rows
                                        Select Case my_row("Lb_name")
                                            Case "西药"
                                                StiRpt("类别金额3") = Val(StiRpt("类别金额3")) + XM
                                            Case "中成药"
                                                StiRpt("类别金额3") = Val(StiRpt("类别金额3")) + Val(StiRpt("中成药"))
                                            Case "中草药"
                                                StiRpt("类别金额3") = Val(StiRpt("类别金额3")) + Val(StiRpt("中草药"))
                                            Case "卫生材料"
                                                StiRpt("类别金额3") = Val(StiRpt("类别金额3")) + WM
                                            Case "化验费"
                                                StiRpt("类别金额3") = Val(StiRpt("类别金额3")) + Val(StiRpt("化验费"))
                                            Case "X光费"
                                                StiRpt("类别金额3") = Val(StiRpt("类别金额3")) + Val(StiRpt("X光费"))
                                            Case "电诊费"
                                                StiRpt("类别金额3") = Val(StiRpt("类别金额3")) + Val(StiRpt("电诊费"))
                                            Case "CT费"
                                                StiRpt("类别金额3") = Val(StiRpt("类别金额3")) + Val(StiRpt("CT费"))
                                            Case "磁共振"
                                                StiRpt("类别金额3") = Val(StiRpt("类别金额3")) + Val(StiRpt("磁共振"))
                                            Case "检查费"
                                                StiRpt("类别金额3") = Val(StiRpt("类别金额3")) + Val(StiRpt("检查费"))
                                            Case "手术费"
                                                StiRpt("类别金额3") = Val(StiRpt("类别金额3")) + Val(StiRpt("手术费"))
                                            Case "输氧费"
                                                StiRpt("类别金额3") = Val(StiRpt("类别金额3")) + Val(StiRpt("输氧费"))
                                            Case "输血费"
                                                StiRpt("类别金额3") = Val(StiRpt("类别金额3")) + Val(StiRpt("输血费"))
                                            Case "处置费"
                                                StiRpt("类别金额3") = Val(StiRpt("类别金额3")) + CzM
                                            Case "注射费"
                                                StiRpt("类别金额3") = Val(StiRpt("类别金额3")) + Val(StiRpt("注射费"))
                                            Case "治疗费"
                                                StiRpt("类别金额3") = Val(StiRpt("类别金额3")) + Val(StiRpt("治疗费"))
                                        End Select
                                    Next
                                Case 1
                                    StiRpt("类别4") = My_DataSet.Tables("类别").Rows(1).Item("Mc_Name")
                                    For Each my_row In My_DataSet.Tables("科室类别明细").Rows
                                        Select Case my_row("Lb_name")
                                            Case "西药"
                                                StiRpt("类别金额4") = Val(StiRpt("类别金额4")) + XM
                                            Case "中成药"
                                                StiRpt("类别金额4") = Val(StiRpt("类别金额4")) + Val(StiRpt("中成药"))
                                            Case "中草药"
                                                StiRpt("类别金额4") = Val(StiRpt("类别金额4")) + Val(StiRpt("中草药"))
                                            Case "卫生材料"
                                                StiRpt("类别金额4") = Val(StiRpt("类别金额4")) + WM
                                            Case "化验费"
                                                StiRpt("类别金额4") = Val(StiRpt("类别金额4")) + Val(StiRpt("化验费"))
                                            Case "X光费"
                                                StiRpt("类别金额4") = Val(StiRpt("类别金额4")) + Val(StiRpt("X光费"))
                                            Case "电诊费"
                                                StiRpt("类别金额4") = Val(StiRpt("类别金额4")) + Val(StiRpt("电诊费"))
                                            Case "CT费"
                                                StiRpt("类别金额4") = Val(StiRpt("类别金额4")) + Val(StiRpt("CT费"))
                                            Case "磁共振"
                                                StiRpt("类别金额4") = Val(StiRpt("类别金额4")) + Val(StiRpt("磁共振"))
                                            Case "检查费"
                                                StiRpt("类别金额4") = Val(StiRpt("类别金额4")) + Val(StiRpt("检查费"))
                                            Case "手术费"
                                                StiRpt("类别金额4") = Val(StiRpt("类别金额4")) + Val(StiRpt("手术费"))
                                            Case "输氧费"
                                                StiRpt("类别金额4") = Val(StiRpt("类别金额4")) + Val(StiRpt("输氧费"))
                                            Case "输血费"
                                                StiRpt("类别金额4") = Val(StiRpt("类别金额4")) + Val(StiRpt("输血费"))
                                            Case "处置费"
                                                StiRpt("类别金额4") = Val(StiRpt("类别金额4")) + CzM
                                            Case "注射费"
                                                StiRpt("类别金额4") = Val(StiRpt("类别金额4")) + Val(StiRpt("注射费"))
                                            Case "治疗费"
                                                StiRpt("类别金额4") = Val(StiRpt("类别金额4")) + Val(StiRpt("治疗费"))
                                        End Select
                                    Next
                            End Select
                            StiRpt("合计2") = StiRpt("类别金额3") + StiRpt("类别金额4")
                        End If
                    Next
                Case 2
                    StiRpt("科室3") = My_DataSet.Tables("科室类别").Rows(2).Item("Fl_Name")

                    For Row_H = 0 To My_DataSet.Tables("类别").Rows.Count - 1
                        If My_DataSet.Tables("科室类别明细") IsNot Nothing Then My_DataSet.Tables("科室类别明细").Clear()
                        With My_Adapter
                            .SelectCommand = New SqlCommand("Select  Mc_Name,Lb_name From Zd_MzFp31,Zd_MzFp4,Zd_MzFp1 Where Zd_MzFp31.Mc_Code='" & My_DataSet.Tables("类别").Rows(Row_H).Item("Mc_Code") & "' And Zd_MzFp31.Mc_Code=Zd_MzFp4.Mc_Code And Zd_MzFp1.Lb_Code=Zd_MzFp4.Lb_Code and Zd_MzFp31.Yy_Code=Zd_MzFp4.Yy_Code and Zd_MzFp31.Yy_Code='" & HisVar.HisVar.WsyCode & "'  union all Select  Mc_Name,Dl_name From Zd_MzFp31,Zd_MzFp4,Zd_Ml_Yp1 Where Zd_MzFp31.Mc_Code='" & My_DataSet.Tables("类别").Rows(Row_H).Item("Mc_Code") & "' And Zd_MzFp31.Mc_Code=Zd_MzFp4.Mc_Code And 'Y'+Zd_Ml_Yp1.Dl_Code=Zd_MzFp4.Lb_Code and Zd_MzFp31.Yy_Code=Zd_MzFp4.Yy_Code and Zd_MzFp31.Yy_Code='" & HisVar.HisVar.WsyCode & "'  ", My_Cn)
                            .Fill(My_DataSet, "科室类别明细")
                        End With
                        If My_DataSet.Tables("科室类别明细").Rows.Count <> 0 Then
                            Select Case Row_H
                                Case 0
                                    StiRpt("类别5") = My_DataSet.Tables("类别").Rows(0).Item("Mc_Name")
                                    For Each my_row In My_DataSet.Tables("科室类别明细").Rows
                                        Select Case my_row("Lb_name")
                                            Case "西药"
                                                StiRpt("类别金额5") = Val(StiRpt("类别金额5")) + XM
                                            Case "中成药"
                                                StiRpt("类别金额5") = Val(StiRpt("类别金额5")) + Val(StiRpt("中成药"))
                                            Case "中草药"
                                                StiRpt("类别金额5") = Val(StiRpt("类别金额5")) + Val(StiRpt("中草药"))
                                            Case "卫生材料"
                                                StiRpt("类别金额5") = Val(StiRpt("类别金额5")) + WM
                                            Case "化验费"
                                                StiRpt("类别金额5") = Val(StiRpt("类别金额5")) + Val(StiRpt("化验费"))
                                            Case "X光费"
                                                StiRpt("类别金额5") = Val(StiRpt("类别金额5")) + Val(StiRpt("X光费"))
                                            Case "电诊费"
                                                StiRpt("类别金额5") = Val(StiRpt("类别金额5")) + Val(StiRpt("电诊费"))
                                            Case "CT费"
                                                StiRpt("类别金额5") = Val(StiRpt("类别金额5")) + Val(StiRpt("CT费"))
                                            Case "磁共振"
                                                StiRpt("类别金额5") = Val(StiRpt("类别金额5")) + Val(StiRpt("磁共振"))
                                            Case "检查费"
                                                StiRpt("类别金额5") = Val(StiRpt("类别金额5")) + Val(StiRpt("检查费"))
                                            Case "手术费"
                                                StiRpt("类别金额5") = Val(StiRpt("类别金额5")) + Val(StiRpt("手术费"))
                                            Case "输氧费"
                                                StiRpt("类别金额5") = Val(StiRpt("类别金额5")) + Val(StiRpt("输氧费"))
                                            Case "输血费"
                                                StiRpt("类别金额5") = Val(StiRpt("类别金额5")) + Val(StiRpt("输血费"))
                                            Case "处置费"
                                                StiRpt("类别金额5") = Val(StiRpt("类别金额5")) + CzM
                                            Case "注射费"
                                                StiRpt("类别金额5") = Val(StiRpt("类别金额5")) + Val(StiRpt("注射费"))
                                            Case "治疗费"
                                                StiRpt("类别金额5") = Val(StiRpt("类别金额5")) + Val(StiRpt("治疗费"))
                                        End Select
                                    Next
                                Case 1
                                    StiRpt("类别6") = My_DataSet.Tables("类别").Rows(1).Item("Mc_Name")
                                    For Each my_row In My_DataSet.Tables("科室类别明细").Rows
                                        Select Case my_row("Lb_name")
                                            Case "西药"
                                                StiRpt("类别金额6") = Val(StiRpt("类别金额6")) + XM
                                            Case "中成药"
                                                StiRpt("类别金额6") = Val(StiRpt("类别金额6")) + Val(StiRpt("中成药"))
                                            Case "中草药"
                                                StiRpt("类别金额6") = Val(StiRpt("类别金额6")) + Val(StiRpt("中草药"))
                                            Case "卫生材料"
                                                StiRpt("类别金额6") = Val(StiRpt("类别金额6")) + WM
                                            Case "化验费"
                                                StiRpt("类别金额6") = Val(StiRpt("类别金额6")) + Val(StiRpt("化验费"))
                                            Case "X光费"
                                                StiRpt("类别金额6") = Val(StiRpt("类别金额6")) + Val(StiRpt("X光费"))
                                            Case "电诊费"
                                                StiRpt("类别金额6") = Val(StiRpt("类别金额6")) + Val(StiRpt("电诊费"))
                                            Case "CT费"
                                                StiRpt("类别金额6") = Val(StiRpt("类别金额6")) + Val(StiRpt("CT费"))
                                            Case "磁共振"
                                                StiRpt("类别金额6") = Val(StiRpt("类别金额6")) + Val(StiRpt("磁共振"))
                                            Case "检查费"
                                                StiRpt("类别金额6") = Val(StiRpt("类别金额6")) + Val(StiRpt("检查费"))
                                            Case "手术费"
                                                StiRpt("类别金额6") = Val(StiRpt("类别金额6")) + Val(StiRpt("手术费"))
                                            Case "输氧费"
                                                StiRpt("类别金额6") = Val(StiRpt("类别金额6")) + Val(StiRpt("输氧费"))
                                            Case "输血费"
                                                StiRpt("类别金额6") = Val(StiRpt("类别金额6")) + Val(StiRpt("输血费"))
                                            Case "处置费"
                                                StiRpt("类别金额6") = Val(StiRpt("类别金额6")) + CzM
                                            Case "注射费"
                                                StiRpt("类别金额6") = Val(StiRpt("类别金额6")) + Val(StiRpt("注射费"))
                                            Case "治疗费"
                                                StiRpt("类别金额6") = Val(StiRpt("类别金额6")) + Val(StiRpt("治疗费"))
                                        End Select
                                    Next
                            End Select
                            StiRpt("合计3") = StiRpt("类别金额5") + StiRpt("类别金额6")
                        End If
                    Next
            End Select
        Next

        '--------------------------------------------------------
        If StiRpt("科室1") = "" Or (StiRpt("类别1") = "" And StiRpt("类别2") = "") Or (Val(StiRpt("类别金额1")) = 0 And Val(StiRpt("类别金额2")) = 0) Then
            StiRpt("打印编号1") = ""
            StiRpt("患者姓名1") = ""
            StiRpt("科室1") = ""
            StiRpt("类别1") = ""
            StiRpt("类别2") = ""
            StiRpt("作废1") = "作   废"

        Else
            StiRpt("作废1") = ""
            If Val(StiRpt("类别金额1")) = 0 And Val(StiRpt("类别金额2")) <> 0 Then
                StiRpt("类别1") = StiRpt("类别2")
                StiRpt("类别金额1") = Val(StiRpt("类别金额2"))
                StiRpt("类别2") = ""
                StiRpt("类别金额2") = 0
            End If

            If Val(StiRpt("类别金额2")) = 0 Then
                StiRpt("类别2") = ""
            End If

        End If
        '--------------------------------------------------------
        If StiRpt("科室2") = "" Or (StiRpt("类别3") = "" And StiRpt("类别4") = "") Or (Val(StiRpt("类别金额3")) = 0 And Val(StiRpt("类别金额4")) = 0) Then
            StiRpt("打印编号2") = ""
            StiRpt("患者姓名2") = ""
            StiRpt("科室2") = ""
            StiRpt("类别3") = ""
            StiRpt("类别4") = ""
            StiRpt("作废2") = "作   废"

        Else
            StiRpt("作废2") = ""
            If Val(StiRpt("类别金额3")) = 0 And Val(StiRpt("类别金额4")) <> 0 Then
                StiRpt("类别3") = StiRpt("类别4")
                StiRpt("类别金额3") = Val(StiRpt("类别金额4"))
                StiRpt("类别4") = ""
                StiRpt("类别金额4") = 0
            End If

            If Val(StiRpt("类别金额4")) = 0 Then
                StiRpt("类别4") = ""
            End If

        End If
        '--------------------------------------------------------
        If StiRpt("科室3") = "" Or (StiRpt("类别5") = "" And StiRpt("类别6") = "") Or (Val(StiRpt("类别金额5")) = 0 And Val(StiRpt("类别金额6")) = 0) Then
            StiRpt("打印编号3") = ""
            StiRpt("患者姓名3") = ""
            StiRpt("科室3") = ""
            StiRpt("类别5") = ""
            StiRpt("类别6") = ""
            StiRpt("作废3") = "作   废"

        Else
            StiRpt("作废3") = ""
            If Val(StiRpt("类别金额5")) = 0 And Val(StiRpt("类别金额6")) <> 0 Then
                StiRpt("类别5") = StiRpt("类别6")
                StiRpt("类别金额5") = Val(StiRpt("类别金额6"))
                StiRpt("类别6") = ""
                StiRpt("类别金额6") = 0
            End If

            If Val(StiRpt("类别金额6")) = 0 Then
                StiRpt("类别6") = ""
            End If

        End If
        '--------------------------------------------------------

        HisVar.HisVar.Sqldal.ExecuteSql("Update Mz_Yp Set Mz_Ph='" & Rrow.Item("Mz_Code") & "' Where Mz_Code='" & Rrow.Item("Mz_Code") & "'")
        HisVar.HisVar.Sqldal.ExecuteSql("Update Mz_Xm Set Mz_Ph='" & Rrow.Item("Mz_Code") & "' Where Mz_Code='" & Rrow.Item("Mz_Code") & "'")
        Dim vForm As New PublicForm.PrintForm(Me.Name, "门诊录入打印", Nothing, Rrow)
        StiRpt.Render()
        vForm.StiViewerControl1.Report = StiRpt

        Select Case Text
            Case "打印预览" '详单打印
                vForm.ShowDialog()
                Me.Close()
            Case "快速打印"
                vForm.StiViewerControl1.Report.Print(False, False)
                HisVar.HisVar.Sqldal.ExecuteSql("Update Mz Set Mz_Print='1' Where Mz_Code='" & Rrow.Item("Mz_Code") & "'")
                If HisVar.HisVar.Sqldal.ExecuteSql("Update Mz Set Mz_FyQr='1',Mz_FyQr_Date=getdate(),Fy_JsrCode='" & HisVar.HisVar.JsrCode & "'  where Mz_Code In (Select Mz_Code From Mz_Xm) And Mz_Code not In (Select Mz_Code From Mz_Yp) And Mz_Code='" & Rrow.Item("Mz_Code") & "'") = 0 Then

                Else
                    Rrow("Mz_FyQr") = True
                    Rrow("Mz_FyQr1") = "是"
                End If
                Rrow("Mz_Print") = True
                Rrow("Mz_Print1") = "是"
                Me.Close()
        End Select
        Rrow.Item("Mz_SsMoney") = Me.C1NumericEdit2.Value
        Rrow.Item("Mz_ThMoney") = Me.C1NumericEdit3.Value

    End Sub

    Private Sub Ts_Fp2013(ByVal Text As String)

        HisVar.HisVar.Sqldal.ExecuteSql("Update Mz Set Mz_SsMoney=" & Me.C1NumericEdit2.Value & ",Mz_ThMoney=" & Me.C1NumericEdit3.Value & " Where Mz_Code='" & Rrow.Item("Mz_Code") & "'")

        HisVar.HisVar.Sqldal.ExecuteSql("Update Mz_Yp Set Mz_Ph='" & Rrow.Item("Mz_Code") & "' Where Mz_Code='" & Rrow.Item("Mz_Code") & "' ")

        HisVar.HisVar.Sqldal.ExecuteSql("Update Mz_Xm Set Mz_Ph='" & Rrow.Item("Mz_Code") & "' Where Mz_Code='" & Rrow.Item("Mz_Code") & "' ")

        Select Case Text
            Case "确认不打印"
                HisVar.HisVar.Sqldal.ExecuteSql("Update Mz Set Mz_Print='1' Where Mz_Code='" & Rrow.Item("Mz_Code") & "'")
                If HisVar.HisVar.Sqldal.ExecuteSql("Update Mz Set Mz_FyQr='1',Mz_FyQr_Date=getdate(),Fy_JsrCode='" & HisVar.HisVar.JsrCode & "'  where Mz_Code In (Select Mz_Code From Mz_Xm) And Mz_Code not In (Select Mz_Code From Mz_Yp) And Mz_Code='" & Rrow.Item("Mz_Code") & "'") = 0 Then

                Else
                    Rrow("Mz_FyQr") = True
                    Rrow("Mz_FyQr1") = "是"
                End If
                Rrow("Mz_Print") = True
                Rrow("Mz_Print1") = "是"
                Rrow.Item("Mz_SsMoney") = Me.C1NumericEdit2.Value
                Rrow.Item("Mz_ThMoney") = Me.C1NumericEdit3.Value
                Call BdInser_Ybzl()
                Me.Close()
                Exit Sub
        End Select

        Dim StiRpt As New StiReport

        Dim mz_top As Double = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\中软智通", "门诊上边距", Nothing)
        Dim mz_bottom As Double = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\中软智通", "门诊下边距", Nothing)
        Dim mz_left As Double = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\中软智通", "门诊左边距", Nothing)


        StiRpt.Load(".\Rpt\2013年河北省门诊收费票据.mrt")
        StiRpt.ReportName = "2013年河北省门诊收费票据"
        StiRpt.Pages(0).Margins.Left = mz_left + 2
        StiRpt.Pages(0).Margins.Top = mz_top
        StiRpt.Pages(0).Margins.Bottom = mz_bottom
        '获取指定打印机
        StiRpt.PrinterSettings.PrinterName = IIf(iniOperate.iniopreate.GetINI("打印机设置", "门诊打印机", "", HisVar.HisVar.Parapath & "\Config.ini") & "" = "", StiRpt.PrinterSettings.PrinterName, iniOperate.iniopreate.GetINI("打印机设置", "门诊打印机", "", HisVar.HisVar.Parapath & "\Config.ini"))



        StiRpt.Compile()
        StiRpt("医疗机构") = HisVar.HisVar.WsyName
        StiRpt("科室") = Rrow.Item("Ks_Name")
        StiRpt("业务流水号") = Rrow.Item("Mz_Code")
        StiRpt("姓名") = Rrow.Item("Ry_Name")
        StiRpt("性别") = Rrow.Item("Ry_Sex")
        StiRpt("医保类型") = Rrow.Item("Bxlb_Name") & ""
        StiRpt("社保号码") = Rrow.Item("Ry_YlCode") & ""
        StiRpt("收款人") = HisVar.HisVar.JsrName
        StiRpt("打印时间") = Format(Now, "yyyy-MM-dd HH:mm:ss")

        If (HisPara.PublicConfig.XqName.Contains("迁安") And HisVar.HisVar.WsyName <> "迁安钢城医院") Or HisPara.PublicConfig.XqName.Contains("迁西") Or HisPara.PublicConfig.XqName.Contains("丰润") Or HisPara.PublicConfig.XqName.Contains("曲周") Then
            HisVar.HisVar.Sqldal.QueryDt(My_DataSet, "Select '0' as V_Group,Yp_Name as Mz_Lb,Mz_Sl,Mz_Money  from Mz_Yp,V_YpKc where Mz_Yp.Xx_Code=V_YpKc.Xx_Code and  Mz_Code='" & Rrow.Item("Mz_Code") & "'  Union all Select Row_Number() over(order by Xm_Name) as V_Group,Xm_Name,Mz_Sl,Mz_Money from Mz_Xm,Zd_Ml_Xm3 where Mz_Xm.Xm_Code=Zd_Ml_Xm3.Xm_Code and  Mz_Code='" & Rrow.Item("Mz_Code") & "' ", "收费明细", True)
            'Case Dl_Code when '03' then '中草药' else  Yp_Name end as Yp_Name 
        ElseIf HisPara.PublicConfig.XqName.Contains("博野") Then
            HisVar.HisVar.Sqldal.QueryDt(My_DataSet, "Select '0' as V_Group,Yp_Name as Mz_Lb,Mz_Sl,Mz_Money  from Mz_Yp,V_YpKc where Mz_Yp.Xx_Code=V_YpKc.Xx_Code and  Mz_Code='" & Rrow.Item("Mz_Code") & "'  Union all Select Zd_Jkfl1.Lb_Code as V_Group, Xm_Name as Mz_Lb,Null as Mz_Sl,Sum(Mz_Money)Mz_Money from Mz_Xm,Zd_Ml_Xm3 left join Zd_Jkfl2 On Zd_Ml_Xm3.Xm_Code=Zd_Jkfl2.Mx_Code left join Zd_Jkfl1 on Zd_Jkfl1.Lb_Code=Zd_Jkfl2.Lb_Code where   Mz_Xm.Xm_Code=Zd_Ml_Xm3.Xm_Code and  Mz_Code='" & Rrow.Item("Mz_Code") & "'  group by Zd_JkFl1.Lb_Code, Xm_Name ", "收费明细", True)
            If My_DataSet.Tables("收费明细").Select("V_Group is null").Length > 0 Then
                MsgBox("存在未维护类别的收费项目，请先维护！", MsgBoxStyle.Information, "提示：")
                Exit Sub
            End If
        Else
            HisVar.HisVar.Sqldal.QueryDt(My_DataSet, "Select '0' as V_Group,Case Mz_Lb when '西药' then '西药费' else Mz_lb end as Mz_Lb,Null as Mz_Sl,Sum(Mz_Money)Mz_Money  from Mz_Yp where  Mz_Code='" & Rrow.Item("Mz_Code") & "' group by Mz_lb  Union all Select  Row_Number() over(order by Lb_Name) as V_Group,Lb_Name,Null as Mz_Sl,Sum(Mz_Money)Mz_Money from Mz_Xm,Zd_Jkfl1,Zd_Jkfl2 where Zd_Jkfl1.Lb_Code=Zd_Jkfl2.Lb_Code and Mz_Xm.Xm_Code=Zd_Jkfl2.Mx_Code and  Mz_Code='" & Rrow.Item("Mz_Code") & "'  group by Lb_Name ", "收费明细", True)
        End If

        '将门诊打印明细设定为固定个数
        Dim V_Newrow As DataRow
        If HisPara.PublicConfig.XqName.Contains("博野") Then

            Dim Yprowcount As Integer = My_DataSet.Tables("收费明细").Select("V_Group=0").Length
            If Yprowcount <> 0 Then
                If Yprowcount Mod 22 <> 0 Then
                    For V_TbRowCount = 1 To 22 - (Yprowcount Mod 22)
                        V_Newrow = My_DataSet.Tables("收费明细").NewRow
                        With V_Newrow
                            .Item(0) = 0
                            .Item(1) = DBNull.Value
                            .Item(2) = DBNull.Value
                            .Item(3) = DBNull.Value
                        End With
                        My_DataSet.Tables("收费明细").Rows.Add(V_Newrow)
                        V_Newrow.AcceptChanges()
                    Next
                End If
            End If

            Dim Row As DataRow
            HisVar.HisVar.Sqldal.QueryDt(My_DataSet, "Select Lb_Code from Zd_Jkfl1", "发票类别编码", True)
            For Each Row In My_DataSet.Tables("发票类别编码").Rows
                Dim Xmrowcount As Integer = My_DataSet.Tables("收费明细").Select("V_Group='" & Row.Item("Lb_Code") & "'").Length
                If Xmrowcount <> 0 Then
                    If Xmrowcount Mod 22 <> 0 Then
                        For V_TbRowCount = 1 To 22 - (Xmrowcount Mod 22)
                            V_Newrow = My_DataSet.Tables("收费明细").NewRow
                            With V_Newrow
                                .Item(0) = Row.Item("Lb_Code")
                                .Item(1) = DBNull.Value
                                .Item(2) = DBNull.Value
                                .Item(3) = DBNull.Value
                            End With
                            My_DataSet.Tables("收费明细").Rows.Add(V_Newrow)
                            V_Newrow.AcceptChanges()
                        Next
                    End If
                End If
            Next
        Else
            Dim Tbrowcount As Integer = My_DataSet.Tables("收费明细").Rows.Count
            If Tbrowcount Mod 22 <> 0 Then
                For V_TbRowCount = 1 To 22 - (Tbrowcount Mod 22)
                    V_Newrow = My_DataSet.Tables("收费明细").NewRow
                    With V_Newrow
                        .Item(0) = DBNull.Value
                        .Item(1) = DBNull.Value
                        .Item(2) = DBNull.Value
                        .Item(3) = DBNull.Value
                    End With
                    My_DataSet.Tables("收费明细").Rows.Add(V_Newrow)
                    V_Newrow.AcceptChanges()
                Next
              
            End If
            My_DataSet.Tables("收费明细").Columns("V_Group").ReadOnly = False
            BaseFunc.BaseFunc.Edit_Col0(My_DataSet.Tables("收费明细"), 0, 22)
        End If

        StiRpt.RegData(My_DataSet.Tables("收费明细"))

        Dim vForm As New PublicForm.PrintForm(Me.Name, "门诊录入打印", Nothing, Rrow)
        StiRpt.Render()

        vForm.StiViewerControl1.Report = StiRpt
        Select Case Text
            Case "打印预览" '详单打印
                'StiRpt.Design()
                vForm.ShowDialog()
                Me.Close()
            Case "快速打印"
                vForm.StiViewerControl1.Report.Print(False, False)
                HisVar.HisVar.Sqldal.ExecuteSql("Update Mz Set Mz_Print='1' Where Mz_Code='" & Rrow.Item("Mz_Code") & "'")
                If HisVar.HisVar.Sqldal.ExecuteSql("Update Mz Set Mz_FyQr='1',Mz_FyQr_Date=getdate(),Fy_JsrCode='" & HisVar.HisVar.JsrCode & "'  where Mz_Code In (Select Mz_Code From Mz_Xm) And Mz_Code not In (Select Mz_Code From Mz_Yp) And Mz_Code='" & Rrow.Item("Mz_Code") & "'") = 0 Then

                Else
                    Rrow("Mz_FyQr") = True
                    Rrow("Mz_FyQr1") = "是"
                End If
                Rrow("Mz_Print") = True
                Rrow("Mz_Print1") = "是"
                Me.Close()

        End Select
        Rrow.Item("Mz_SsMoney") = Me.C1NumericEdit2.Value
        Rrow.Item("Mz_ThMoney") = Me.C1NumericEdit3.Value
        Call BdInser_Ybzl()
        Me.Close()

    End Sub

    Private Sub Hld_Fp(ByVal Text As String)

        If C1NumericEdit2.Value < C1NumericEdit1.Value Then
            MsgBox("实收金额小于应收金额，请检查！", vbInformation + vbOKOnly + vbDefaultButton1, "提示:")
            C1NumericEdit2.Select()
            Exit Sub
        End If

        HisVar.HisVar.Sqldal.ExecuteSql("Update Mz Set Mz_SsMoney=" & Me.C1NumericEdit2.Value & ",Mz_ThMoney=" & Me.C1NumericEdit3.Value & " Where Mz_Code='" & Rrow.Item("Mz_Code") & "'")

        HisVar.HisVar.Sqldal.ExecuteSql("Update Mz_Yp Set Mz_Ph='" & Rrow.Item("Mz_Code") & "' Where Mz_Code='" & Rrow.Item("Mz_Code") & "' ")

        HisVar.HisVar.Sqldal.ExecuteSql("Update Mz_Xm Set Mz_Ph='" & Rrow.Item("Mz_Code") & "' Where Mz_Code='" & Rrow.Item("Mz_Code") & "' ")

        Select Case Text
            Case "确认不打印"
                HisVar.HisVar.Sqldal.ExecuteSql("Update Mz Set Mz_Print='1' Where Mz_Code='" & Rrow.Item("Mz_Code") & "'")
                If HisVar.HisVar.Sqldal.ExecuteSql("Update Mz Set Mz_FyQr='1',Mz_FyQr_Date=getdate(),Fy_JsrCode='" & HisVar.HisVar.JsrCode & "'  where Mz_Code In (Select Mz_Code From Mz_Xm) And Mz_Code not In (Select Mz_Code From Mz_Yp) And Mz_Code='" & Rrow.Item("Mz_Code") & "'") = 0 Then

                Else
                    Rrow("Mz_FyQr") = True
                    Rrow("Mz_FyQr1") = "是"
                End If
                Rrow("Mz_Print") = True
                Rrow("Mz_Print1") = "是"
                Rrow.Item("Mz_SsMoney") = Me.C1NumericEdit2.Value
                Rrow.Item("Mz_ThMoney") = Me.C1NumericEdit3.Value
                Call BdInser_Ybzl()
                Me.Close()
                Exit Sub
        End Select

        Dim StiRpt As New StiReport

        Dim mz_top As Double = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\中软智通", "门诊上边距", Nothing)
        Dim mz_bottom As Double = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\中软智通", "门诊下边距", Nothing)
        Dim mz_left As Double = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\中软智通", "门诊左边距", Nothing)


        StiRpt.Load(".\Rpt\辽宁省门诊收费票据.mrt")
        StiRpt.ReportName = "辽宁省门诊收费票据"
        StiRpt.Pages(0).Margins.Left = mz_left + 2
        StiRpt.Pages(0).Margins.Top = mz_top
        StiRpt.Pages(0).Margins.Bottom = mz_bottom
        '获取指定打印机
        StiRpt.PrinterSettings.PrinterName = IIf(iniOperate.iniopreate.GetINI("打印机设置", "门诊打印机", "", HisVar.HisVar.Parapath & "\Config.ini") & "" = "", StiRpt.PrinterSettings.PrinterName, iniOperate.iniopreate.GetINI("打印机设置", "门诊打印机", "", HisVar.HisVar.Parapath & "\Config.ini"))


        StiRpt.Compile()
        StiRpt("医疗机构") = HisVar.HisVar.WsyName
        StiRpt("科室") = Rrow.Item("Ks_Name")
        StiRpt("业务流水号") = Rrow.Item("Mz_Code")
        StiRpt("姓名") = Rrow.Item("Ry_Name")
        StiRpt("性别") = Rrow.Item("Ry_Sex")
        StiRpt("医保类型") = Rrow.Item("BxLb_Name") & ""
        StiRpt("社保号码") = Rrow.Item("Ry_YlCode") & ""
        StiRpt("收款人") = HisVar.HisVar.JsrName
        StiRpt("打印时间") = Format(Now, "yyyy-MM-dd HH:mm:ss")

     
        HisVar.HisVar.Sqldal.QueryDt(My_DataSet, "Select '0' as V_Group,Case Mz_Lb when '西药' then '西药费' else Mz_lb end as Mz_Lb,Null as Mz_Sl,Sum(Mz_Money)Mz_Money  from Mz_Yp where  Mz_Code='" & Rrow.Item("Mz_Code") & "' group by Mz_lb  Union all Select  Row_Number() over(order by Lb_Name) as V_Group,Lb_Name,Null as Mz_Sl,Sum(Mz_Money)Mz_Money from Mz_Xm,Zd_Jkfl1,Zd_Jkfl2 where Zd_Jkfl1.Lb_Code=Zd_Jkfl2.Lb_Code and Mz_Xm.Xm_Code=Zd_Jkfl2.Mx_Code and  Mz_Code='" & Rrow.Item("Mz_Code") & "' and Zd_Jkfl2.Yy_Code='" & HisVar.HisVar.WsyCode & "' group by Lb_Name ", "收费明细", True)
      

        '将门诊打印明细设定为固定个数
        Dim V_Newrow As DataRow

        Dim Tbrowcount As Integer = My_DataSet.Tables("收费明细").Rows.Count
        If Tbrowcount Mod 22 <> 0 Then
            For V_TbRowCount = 1 To 22 - (Tbrowcount Mod 22)
                V_Newrow = My_DataSet.Tables("收费明细").NewRow
                With V_Newrow
                    .Item(0) = DBNull.Value
                    .Item(1) = DBNull.Value
                    .Item(2) = DBNull.Value
                    .Item(3) = DBNull.Value
                End With
                My_DataSet.Tables("收费明细").Rows.Add(V_Newrow)
                V_Newrow.AcceptChanges()
            Next
            My_DataSet.Tables("收费明细").Columns("V_Group").ReadOnly = False
            BaseFunc.BaseFunc.Edit_Col0(My_DataSet.Tables("收费明细"), 0, 22)
        End If


        StiRpt.RegData(My_DataSet.Tables("收费明细"))

        Dim vForm As New PublicForm.PrintForm(Me.Name, "门诊录入打印", Nothing, Rrow)
        StiRpt.Render()

        vForm.StiViewerControl1.Report = StiRpt
        Select Case Text
            Case "打印预览" '详单打印
                'StiRpt.Design()
                vForm.ShowDialog()
                Me.Close()
            Case "快速打印"
                vForm.StiViewerControl1.Report.Print(False, False)
                HisVar.HisVar.Sqldal.ExecuteSql("Update Mz Set Mz_Print='1' Where Mz_Code='" & Rrow.Item("Mz_Code") & "'")
                If HisVar.HisVar.Sqldal.ExecuteSql("Update Mz Set Mz_FyQr='1',Mz_FyQr_Date=getdate(),Fy_JsrCode='" & HisVar.HisVar.JsrCode & "'  where Mz_Code In (Select Mz_Code From Mz_Xm) And Mz_Code not In (Select Mz_Code From Mz_Yp) And Mz_Code='" & Rrow.Item("Mz_Code") & "'") = 0 Then

                Else
                    Rrow("Mz_FyQr") = True
                    Rrow("Mz_FyQr1") = "是"
                End If
                Rrow("Mz_Print") = True
                Rrow("Mz_Print1") = "是"
                Me.Close()

        End Select
        Rrow.Item("Mz_SsMoney") = Me.C1NumericEdit2.Value
        Rrow.Item("Mz_ThMoney") = Me.C1NumericEdit3.Value
        Call BdInser_Ybzl()
        Me.Close()

    End Sub

    Private Sub Hld_Fp_Lb(ByVal Text As String)

        If C1NumericEdit2.Value < C1NumericEdit1.Value Then
            MsgBox("实收金额小于应收金额，请检查！", vbInformation + vbOKOnly + vbDefaultButton1, "提示:")
            C1NumericEdit2.Select()
            Exit Sub
        End If

        HisVar.HisVar.Sqldal.ExecuteSql("Update Mz Set Mz_SsMoney=" & Me.C1NumericEdit2.Value & ",Mz_ThMoney=" & Me.C1NumericEdit3.Value & " Where Mz_Code='" & Rrow.Item("Mz_Code") & "'")

        HisVar.HisVar.Sqldal.ExecuteSql("Update Mz_Yp Set Mz_Ph='" & Rrow.Item("Mz_Code") & "' Where Mz_Code='" & Rrow.Item("Mz_Code") & "' ")

        HisVar.HisVar.Sqldal.ExecuteSql("Update Mz_Xm Set Mz_Ph='" & Rrow.Item("Mz_Code") & "' Where Mz_Code='" & Rrow.Item("Mz_Code") & "' ")

        Select Case Text
            Case "确认不打印"
                HisVar.HisVar.Sqldal.ExecuteSql("Update Mz Set Mz_Print='1' Where Mz_Code='" & Rrow.Item("Mz_Code") & "'")
                If HisVar.HisVar.Sqldal.ExecuteSql("Update Mz Set Mz_FyQr='1',Mz_FyQr_Date=getdate(),Fy_JsrCode='" & HisVar.HisVar.JsrCode & "'  where Mz_Code In (Select Mz_Code From Mz_Xm) And Mz_Code not In (Select Mz_Code From Mz_Yp) And Mz_Code='" & Rrow.Item("Mz_Code") & "'") = 0 Then

                Else
                    Rrow("Mz_FyQr") = True
                    Rrow("Mz_FyQr1") = "是"
                End If
                Rrow("Mz_Print") = True
                Rrow("Mz_Print1") = "是"
                Rrow.Item("Mz_SsMoney") = Me.C1NumericEdit2.Value
                Rrow.Item("Mz_ThMoney") = Me.C1NumericEdit3.Value
                Call BdInser_Ybzl()
                Me.Close()
                Exit Sub
        End Select

        Dim StiRpt As New StiReport

        Dim mz_top As Double = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\中软智通", "门诊上边距", Nothing)
        Dim mz_bottom As Double = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\中软智通", "门诊下边距", Nothing)
        Dim mz_left As Double = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\中软智通", "门诊左边距", Nothing)


        StiRpt.Load(".\Rpt\辽宁省门诊收费票据老版.mrt")
        StiRpt.ReportName = "辽宁省门诊收费票据"
        StiRpt.Pages(0).Margins.Left = mz_left + 2
        StiRpt.Pages(0).Margins.Top = mz_top
        StiRpt.Pages(0).Margins.Bottom = mz_bottom
        '获取指定打印机
        StiRpt.PrinterSettings.PrinterName = IIf(iniOperate.iniopreate.GetINI("打印机设置", "门诊打印机", "", HisVar.HisVar.Parapath & "\Config.ini") & "" = "", StiRpt.PrinterSettings.PrinterName, iniOperate.iniopreate.GetINI("打印机设置", "门诊打印机", "", HisVar.HisVar.Parapath & "\Config.ini"))


        StiRpt.Compile()
        StiRpt("医疗机构") = HisVar.HisVar.WsyName
        StiRpt("科室") = Rrow.Item("Ks_Name")
        StiRpt("业务流水号") = Rrow.Item("Mz_Code")
        StiRpt("姓名") = Rrow.Item("Ry_Name")
        StiRpt("性别") = Rrow.Item("Ry_Sex")
        StiRpt("医保类型") = Rrow.Item("Bxlb_Name") & ""
        StiRpt("社保号码") = Rrow.Item("Ry_YlCode") & ""
        StiRpt("收款人") = HisVar.HisVar.JsrName
        StiRpt("打印时间") = Format(Now, "yyyy-MM-dd HH:mm:ss")


        HisVar.HisVar.Sqldal.QueryDt(My_DataSet, "Select '0' as V_Group,Case Mz_Lb when '西药' then '西药费' else Mz_lb end as Mz_Lb,Null as Mz_Sl,Sum(Mz_Money)Mz_Money  from Mz_Yp where  Mz_Code='" & Rrow.Item("Mz_Code") & "' group by Mz_lb  Union all Select  Row_Number() over(order by Lb_Name) as V_Group,Lb_Name,Null as Mz_Sl,Sum(Mz_Money)Mz_Money from Mz_Xm,Zd_Jkfl1,Zd_Jkfl2 where Zd_Jkfl1.Lb_Code=Zd_Jkfl2.Lb_Code and Mz_Xm.Xm_Code=Zd_Jkfl2.Mx_Code and  Mz_Code='" & Rrow.Item("Mz_Code") & "' and Zd_Jkfl2.Yy_Code='" & HisVar.HisVar.WsyCode & "' group by Lb_Name ", "收费明细", True)


        '将门诊打印明细设定为固定个数
        Dim V_Newrow As DataRow

        Dim Tbrowcount As Integer = My_DataSet.Tables("收费明细").Rows.Count
        If Tbrowcount Mod 14 <> 0 Then
            For V_TbRowCount = 1 To 14 - (Tbrowcount Mod 14)
                V_Newrow = My_DataSet.Tables("收费明细").NewRow
                With V_Newrow
                    .Item(0) = DBNull.Value
                    .Item(1) = DBNull.Value
                    .Item(2) = DBNull.Value
                    .Item(3) = DBNull.Value
                End With
                My_DataSet.Tables("收费明细").Rows.Add(V_Newrow)
                V_Newrow.AcceptChanges()
            Next
            My_DataSet.Tables("收费明细").Columns("V_Group").ReadOnly = False
            BaseFunc.BaseFunc.Edit_Col0(My_DataSet.Tables("收费明细"), 0, 14)
        End If


        StiRpt.RegData(My_DataSet.Tables("收费明细"))

        Dim vForm As New PublicForm.PrintForm(Me.Name, "门诊录入打印", Nothing, Rrow)
        StiRpt.Render()

        vForm.StiViewerControl1.Report = StiRpt
        Select Case Text
            Case "打印预览" '详单打印
                'StiRpt.Design()
                vForm.ShowDialog()
                Me.Close()
            Case "快速打印"
                vForm.StiViewerControl1.Report.Print(False, False)
                HisVar.HisVar.Sqldal.ExecuteSql("Update Mz Set Mz_Print='1' Where Mz_Code='" & Rrow.Item("Mz_Code") & "'")
                If HisVar.HisVar.Sqldal.ExecuteSql("Update Mz Set Mz_FyQr='1',Mz_FyQr_Date=getdate(),Fy_JsrCode='" & HisVar.HisVar.JsrCode & "'  where Mz_Code In (Select Mz_Code From Mz_Xm) And Mz_Code not In (Select Mz_Code From Mz_Yp) And Mz_Code='" & Rrow.Item("Mz_Code") & "'") = 0 Then

                Else
                    Rrow("Mz_FyQr") = True
                    Rrow("Mz_FyQr1") = "是"
                End If
                Rrow("Mz_Print") = True
                Rrow("Mz_Print1") = "是"
                Me.Close()

        End Select
        Rrow.Item("Mz_SsMoney") = Me.C1NumericEdit2.Value
        Rrow.Item("Mz_ThMoney") = Me.C1NumericEdit3.Value
        Call BdInser_Ybzl()
        Me.Close()

    End Sub

    Private Sub C1NumericEdit3_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles C1NumericEdit3.KeyPress
        If e.KeyChar = Chr(Keys.Return) Then
            Me.C1Button2.Select()
        End If
    End Sub

    Private Sub BdInser_Ybzl()
        '触发一般诊疗费条件

        If HisVar.HisVar.IsYbzl = "是" And (Rrow.Item("Bxlb_Name") = "合作医疗" Or Rrow.Item("Bxlb_Name") = "城乡居民") And Rrow.Item("Ry_YlCode") <> "" Then

            Dim Ary As New ArrayList

            Dim Cb_Row As DataRow
            Dim V_In As Boolean = False

            If Rrow("Mz_Print") = True Then
                Dim YbzlXm_Str As String = "门诊输液诊查费,普通门诊诊查费,住院诊查费,门诊输液,静脉输液自第,门诊留观诊查费,门急诊留观诊查费,注射费,门急诊观诊留观诊查费,挂号,挂号费,肌肉（皮下）,肌肉注射,肌肉注射（皮试）,肌肉注射（新生儿）,肌肉注射费,急诊门留观诊查费一般输液,急诊诊查费,急诊诊察费,静脉输液,静脉输液(使用微量泵),静脉输液(使用微量泵或输液泵加收）,静脉输液(自第二瓶起),静脉输液（自第二瓶起加收）,静脉输液（自第二瓶起每瓶收）,静脉输液（自第二瓶起每瓶收）1,静脉输液（自第二瓶起每瓶收）2,静脉注射（小儿静脉采血）,静脉注射费,门急诊留观诊查费一般输液,门诊挂号费,门诊观察费,门诊急诊费,门诊输液费,门诊诊查费,皮内注射,皮试,皮下输液,皮下注射,普通诊查费,小儿静脉输液,小儿静脉输液（自第二瓶起）,小儿头皮静脉输液,小儿头皮输液,小儿头皮输液费,诊查费,诊察费1,诊费,诊费1,诊费2,诊费3,诊费4,诊费5,注射费1,专家挂号费,专家门诊诊查费,专家诊费,肌肉注射（皮下）,肌肉注射（皮内）,静脉注射,静脉注射（小儿）,静脉注射（静脉采血）,皮下输液（自第二瓶起每瓶收）,静脉输液（使用微量泵或输液泵按小时加收）,静脉输液（输血）,小儿头皮静脉输液（自第二瓶起每瓶收）,小儿头皮静脉输液（使用微量泵或输液泵按小时加收）"

                For Each Cb_Row In Rds.Tables("诊疗项目").Rows

                    If YbzlXm_Str.Contains(Cb_Row.Item("Xm_Name")) And Cb_Row.Item("Mz_Dj") = 0 And Cb_Row.Item("Mz_Sl") > 0 Then
                        V_In = True
                        Exit For
                    End If
                Next
                If V_In = False Then Exit Sub

                If C1NumericEdit1.Value = 0 Then
                    MsgBox("未发生费用，不能记录为一般诊疗费，该记录不能上传！", MsgBoxStyle.Information, "提示")
                    Exit Sub
                End If

                '一般诊疗费政策限制条件

                If HisPara.PublicConfig.XqName.Contains("滦县") And HisVar.HisVar.Sqldal.GetSingle("Select isnull(Sum(Mz_Sl),0) from Ybzl_Ry where Ry_YlCode='" & Rrow.Item("Ry_YlCode") & "' and DATEDIFF(DAY,substring(Mz_Code,5,2)+'-'+substring(Mz_Code,7,2)+'-'+substring(Mz_Code,9,2),getdate())>=-2 and DATEDIFF(DAY,substring(Mz_Code,5,2)+'-'+substring(Mz_Code,7,2)+'-'+substring(Mz_Code,9,2), getdate())<=2") > 0 Then
                    MsgBox("此患者三天内重复使用一般诊疗费项目，将不累计！")
                    Exit Sub
                End If

                If (HisPara.PublicConfig.XqName.Contains("迁安") Or HisPara.PublicConfig.XqName.Contains("玉田")) And HisVar.HisVar.Sqldal.GetSingle("Select isnull(Sum(Mz_Sl),0) from Ybzl_Ry where  Ry_YlCode='" & Rrow.Item("Ry_YlCode") & "' and DATEDIFF(DAY,substring(Mz_Code,5,2)+'-'+substring(Mz_Code,7,2)+'-'+substring(Mz_Code,9,2),getdate())>=-2 and DATEDIFF(DAY,substring(Mz_Code,5,2)+'-'+substring(Mz_Code,7,2)+'-'+substring(Mz_Code,9,2), getdate())<=2 and  Ks_Name='" & Rrow.Item("Ks_Name") & "' ") > 0 Then
                    MsgBox("此患者三天内在同一科室重复使用一般诊疗费项目，将不累计！")
                    Exit Sub
                End If

                If (HisPara.PublicConfig.XqName.Contains("迁安") Or HisPara.PublicConfig.XqName.Contains("芦台") Or HisPara.PublicConfig.XqName.Contains("汉沽")) And HisVar.HisVar.Sqldal.GetSingle("Select isnull(Sum(Mz_Sl),0) from Ybzl_Ry where  Ry_YlCode='" & Rrow.Item("Ry_YlCode") & "' and DATEDIFF(DAY,substring(Mz_Code,5,2)+'-'+substring(Mz_Code,7,2)+'-'+substring(Mz_Code,9,2),getdate())=0 ") > 0 Then
                    MsgBox("此患者同一天内重复使用一般诊疗费项目，将不累计！")
                    Exit Sub
                End If

                Dim Ybzl_BcMoney As Double
                Dim Bc_Rc As Integer
                If V_Ybzl_FdLx = "年封顶" Then
                    Bc_Rc = HisVar.HisVar.Sqldal.GetSingle("Select Isnull(Sum(Mz_Sl),0) from Ybzl_Ry where year(mz_date)=year(Getdate())")
                ElseIf V_Ybzl_FdLx = "月封顶" Then
                    Bc_Rc = HisVar.HisVar.Sqldal.GetSingle("Select Isnull(Sum(Mz_Sl),0) from Ybzl_Ry where Month(mz_date)=Month(Getdate())")
                End If
                '当不存在封顶时下边第一个判断成立，始终补偿单价金额
                If V_Ybzl_FdJe - V_Ybzl_BcDj * Bc_Rc >= V_Ybzl_BcDj Or V_Ybzl_FdLx = "无" Then
                    Ybzl_BcMoney = V_Ybzl_BcDj
                ElseIf V_Ybzl_FdJe - V_Ybzl_BcDj * Bc_Rc < V_Ybzl_BcDj And V_Ybzl_FdJe - V_Ybzl_BcDj * Bc_Rc >= 0 Then
                    Ybzl_BcMoney = V_Ybzl_FdJe - V_Ybzl_BcDj * Bc_Rc
                ElseIf V_Ybzl_FdJe - V_Ybzl_BcDj * Bc_Rc < 0 Then
                    Ybzl_BcMoney = 0
                End If


                Ary.Add("Delete from YbZl_RyMx where Mz_Code='" & Rrow.Item("Mz_Code") & "'")
                Ary.Add("Delete from YbZl_Ry where Mz_Code='" & Rrow.Item("Mz_Code") & "'")

                If HisPara.PublicConfig.XqName.Contains("玉田") Or HisPara.PublicConfig.XqName.Contains("滦县") Or HisPara.PublicConfig.XqName.Contains("迁安") Or HisPara.PublicConfig.XqName.Contains("芦台") Or HisPara.PublicConfig.XqName.Contains("汉沽") Or HisPara.PublicConfig.XqName.Contains("迁西") Or HisPara.PublicConfig.XqName.Contains("高新区") Then
                    Ary.Add("Insert Into YbZl_Ry(Mz_Code,Ry_YlCode,Ry_Name,Ry_Sex,Ks_Name,Ys_Name,Mz_Date,Mz_Sl,Jf_Lb,Qt_XmMoney,Yp_Money,Wc_Money,Ry_Sfzh,Ry_Jb,Jsr_Name,Ry_Age,Ry_Address,Ry_Memo,Mz_Jffs,Ybzl_BcJe) Values('" & Rrow.Item("Mz_Code") & "','" & Rrow.Item("Ry_YlCode") & "','" & Rrow.Item("Ry_Name") & "','" & Rrow.Item("Ry_Sex") & "','" & Rrow.Item("Ks_Name") & "','" & Rrow.Item("Ys_Name") & "','" & Rrow.Item("Mz_Date") & " " & Rrow.Item("Mz_Time") & "',1,'收费'," & Rrow.Item("Mz_XmMoney") & "," & IIf(Rds.Tables("药品卫材").Compute("Sum(Mz_Money)", "Mz_Lb<>'卫生材料'") Is DBNull.Value, 0, Rds.Tables("药品卫材").Compute("Sum(Mz_Money)", "Mz_Lb<>'卫生材料'")) & "," & IIf(Rds.Tables("药品卫材").Compute("Sum(Mz_Money)", "Mz_Lb='卫生材料'") Is DBNull.Value, 0, Rds.Tables("药品卫材").Compute("Sum(Mz_Money)", "Mz_Lb='卫生材料'")) & ",'" & Rrow.Item("Ry_Sfzh") & "','" & Rrow.Item("Jb_Name") & "','" & HisVar.HisVar.JsrName & "'," & IIf(Rrow.Item("Ry_Age") Is DBNull.Value, "Null", Rrow.Item("Ry_Age")) & ",'" & Rrow.Item("Ry_Address") & "','" & Rrow.Item("Ry_Memo") & "','" & Rrow.Item("Mz_Jffs") & "'," & Ybzl_BcMoney & ")")
                Else
                    Ary.Add("Insert Into YbZl_Ry(Mz_Code,Ry_YlCode,Ry_Name,Ry_Sex,Ks_Name,Ys_Name,Mz_Date,Mz_Sl,Jf_Lb,Qt_XmMoney,Yp_Money,Wc_Money,Ry_Sfzh,Ry_Jb,Jsr_Name,Ry_Age,Ry_Address,Ry_Memo,Mz_Jffs) Values('" & Rrow.Item("Mz_Code") & "','" & Rrow.Item("Ry_YlCode") & "','" & Rrow.Item("Ry_Name") & "','" & Rrow.Item("Ry_Sex") & "','" & Rrow.Item("Ks_Name") & "','" & Rrow.Item("Ys_Name") & "','" & Rrow.Item("Mz_Date") & " " & Rrow.Item("Mz_Time") & "',1,'收费'," & Rrow.Item("Mz_XmMoney") & "," & IIf(Rds.Tables("药品卫材").Compute("Sum(Mz_Money)", "Mz_Lb<>'卫生材料'") Is DBNull.Value, 0, Rds.Tables("药品卫材").Compute("Sum(Mz_Money)", "Mz_Lb<>'卫生材料'")) & "," & IIf(Rds.Tables("药品卫材").Compute("Sum(Mz_Money)", "Mz_Lb='卫生材料'") Is DBNull.Value, 0, Rds.Tables("药品卫材").Compute("Sum(Mz_Money)", "Mz_Lb='卫生材料'")) & ",'" & Rrow.Item("Ry_Sfzh") & "','" & Rrow.Item("Jb_Name") & "','" & HisVar.HisVar.JsrName & "'," & IIf(Rrow.Item("Ry_Age") Is DBNull.Value, "Null", Rrow.Item("Ry_Age")) & ",'" & Rrow.Item("Ry_Address") & "','" & Rrow.Item("Ry_Memo") & "','" & Rrow.Item("Mz_Jffs") & "'  )")
                End If


                Ary.Add("Insert Into YbZl_RyMx (Mz_Code,Mx_Name,Mz_Sl,Mz_Dj,Mz_Money,Mx_Gg,Mx_Cd,Mx_Dw)  Select Mz_Code,Yp_Name,Mz_Sl,Mz_Dj,Mz_Money,Mx_Gg,Mx_Cd,Mx_XsDw from Mz_Yp,V_YpKc where Mz_Yp.Xx_Code=V_YpKc.Xx_Code and Mz_Code='" & Rrow.Item("Mz_Code") & "' order by Mz_Id ")
                Ary.Add("Insert Into YbZl_RyMx (Mz_Code,Mx_Name,Mz_Sl,Mz_Dj,Mz_Money,Mx_Gg,Mx_Cd,Mx_Dw)  Select Mz_Code,Xm_Name,Mz_Sl,Mz_Dj,Mz_Money,'','',Xm_Dw      from Mz_Xm,Zd_Ml_Xm3 where Mz_Xm.Xm_Code=Zd_Ml_Xm3.Xm_Code and Mz_Code='" & Rrow.Item("Mz_Code") & "' order by Mz_Id ")

              


                HisVar.HisVar.Sqldal.ExecuteSqlTran(Ary)

                If HisPara.PublicConfig.XqName.Contains("玉田") Then Exit Sub
                Dim Ybzl_Dataset As New DataSet
                HisVar.HisVar.Sqldal.QueryDt(Ybzl_Dataset, "Select * from Ybzl_Ry where  Is_Up='否' order by Mz_Code", "一般诊疗主表", True)
                HisVar.HisVar.Sqldal.QueryDt(Ybzl_Dataset, "Select * from Ybzl_RyMx where  Exists (Select Mz_Code from Ybzl_Ry where  Is_Up='否' and Ybzl_Ry.Mz_Code=Ybzl_RyMx.Mz_Code )  order by Mz_Code,Mz_Id", "一般诊疗从表", True)

'                If ZtXnh.UpLoad_Ybzl(Ybzl_Dataset) = True Then
'                    HisVar.HisVar.Sqldal.ExecuteSql("Update Ybzl_Ry set Is_Up='是'")
'                Else
'                    MsgBox("农合网络可能存在问题，数据不能正常上传，请检查！")
'                End If

            End If

        End If


    End Sub

End Class