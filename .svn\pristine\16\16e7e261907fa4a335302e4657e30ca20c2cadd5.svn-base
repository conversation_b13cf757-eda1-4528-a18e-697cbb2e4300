﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.21022</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{EF778791-6E30-4A07-83B9-2D73FD08810A}</ProjectGuid>
    <OutputType>Library</OutputType>
    <StartupObject>
    </StartupObject>
    <RootNamespace>HisControl</RootNamespace>
    <AssemblyName>HisControl</AssemblyName>
    <FileAlignment>512</FileAlignment>
    <MyType>Windows</MyType>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <OptionExplicit>On</OptionExplicit>
    <OptionCompare>Binary</OptionCompare>
    <OptionStrict>Off</OptionStrict>
    <OptionInfer>On</OptionInfer>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <OldToolsVersion>3.5</OldToolsVersion>
    <PublishUrl>http://localhost/HisControl/</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Web</InstallFrom>
    <UpdateEnabled>true</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <IsWebBootstrapper>true</IsWebBootstrapper>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <OutputPath>..\output\</OutputPath>
    <DocumentationFile>HisControl.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022,42353,42354,42355</NoWarn>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
    <PlatformTarget>x86</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>None</DebugType>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DocumentationFile>HisControl.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022,42353,42354,42355</NoWarn>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="C1.Win.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=944ae1ea0e47ca04, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.C1Command.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=e808566f358766d8, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.C1Ribbon.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.C1TrueDBGrid.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=75ae3fb0e2b1e0da, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
  </ItemGroup>
  <ItemGroup>
    <Import Include="Microsoft.VisualBasic" />
    <Import Include="System" />
    <Import Include="System.Collections" />
    <Import Include="System.Collections.Generic" />
    <Import Include="System.Drawing" />
    <Import Include="System.Diagnostics" />
    <Import Include="System.Windows.Forms" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="base\Base.Designer.vb">
      <DependentUpon>Base.vb</DependentUpon>
    </Compile>
    <Compile Include="base\Base.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="base\BaseChild.Designer.vb">
      <DependentUpon>BaseChild.vb</DependentUpon>
    </Compile>
    <Compile Include="base\BaseChild.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="base\BaseForm.Designer.vb">
      <DependentUpon>BaseForm.vb</DependentUpon>
    </Compile>
    <Compile Include="base\BaseForm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Module1.vb" />
    <Compile Include="My Project\Resources.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="提示框\MessageNotify.Designer.vb">
      <DependentUpon>MessageNotify.vb</DependentUpon>
    </Compile>
    <Compile Include="提示框\MessageNotify.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="My Project\AssemblyInfo.vb" />
    <Compile Include="My Project\Application.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
    </Compile>
    <Compile Include="My Project\Settings.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <Compile Include="提示框\MsgNotify.Designer.vb">
      <DependentUpon>MsgNotify.vb</DependentUpon>
    </Compile>
    <Compile Include="提示框\MsgNotify.vb">
      <SubType>Form</SubType>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="base\Base.resx">
      <DependentUpon>Base.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="base\BaseChild.resx">
      <DependentUpon>BaseChild.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="base\BaseForm.resx">
      <DependentUpon>BaseForm.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="My Project\Resources.resx">
      <Generator>VbMyResourcesResXFileCodeGenerator</Generator>
      <CustomToolNamespace>My.Resources</CustomToolNamespace>
      <SubType>Designer</SubType>
      <LastGenOutput>Resources.Designer.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="提示框\MsgNotify.resx">
      <DependentUpon>MsgNotify.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
    <None Include="My Project\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <CustomToolNamespace>My</CustomToolNamespace>
      <LastGenOutput>Settings.Designer.vb</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Service Include="{94E38DFF-614B-4CBD-B67C-F211BB35CE8B}" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Resources\bgheadtools.png" />
    <Content Include="Resources\bodyspin.png" />
    <Content Include="Resources\bold.gif" />
    <Content Include="Resources\bottombg.png" />
    <Content Include="Resources\btn1.png" />
    <Content Include="Resources\btn2.png" />
    <Content Include="Resources\btn3.png" />
    <Content Include="Resources\bullets.gif" />
    <Content Include="Resources\bullets1.gif" />
    <Content Include="Resources\button_cancel.png" />
    <Content Include="Resources\button_ok.png" />
    <Content Include="Resources\card.gif" />
    <Content Include="Resources\changqiyizhu_jcy.png" />
    <Content Include="Resources\chongwubg.png" />
    <Content Include="Resources\close1.png" />
    <Content Include="Resources\close2.png" />
    <Content Include="Resources\close3.png" />
    <Content Include="Resources\close4.png" />
    <Content Include="Resources\code.gif" />
    <Content Include="Resources\colorpen.gif" />
    <Content Include="Resources\copy.gif" />
    <Content Include="Resources\cut.gif" />
    <Content Include="Resources\delete.gif" />
    <Content Include="Resources\error.png" />
    <Content Include="Resources\exit.gif" />
    <Content Include="Resources\fenshu.gif" />
    <Content Include="Resources\fontcolor.gif" />
    <Content Include="Resources\fullscreen.gif" />
    <Content Include="Resources\heilongjiang - 副本 %282%29.png" />
    <Content Include="Resources\heilongjiang - 副本.png" />
    <Content Include="Resources\heilongjiang.png" />
    <Content Include="Resources\heilongjiang_bak.png" />
    <Content Include="Resources\indent.gif" />
    <Content Include="Resources\insertimage.gif" />
    <Content Include="Resources\insertrule.gif" />
    <Content Include="Resources\italic.gif" />
    <Content Include="Resources\js.htm" />
    <Content Include="Resources\jsprintpage.txt" />
    <Content Include="Resources\justifycenter.gif" />
    <Content Include="Resources\justifycenter1.gif" />
    <Content Include="Resources\justifyfull.gif" />
    <Content Include="Resources\JustifyLeft.gif" />
    <Content Include="Resources\justifyright.gif" />
    <Content Include="Resources\k1.png" />
    <Content Include="Resources\k2.png" />
    <Content Include="Resources\k3.png" />
    <Content Include="Resources\Lcase.gif" />
    <Content Include="Resources\leftspin.png" />
    <Content Include="Resources\lefttitle.png" />
    <Content Include="Resources\linshiyizhu_jcy.png" />
    <Content Include="Resources\login.gif" />
    <Content Include="Resources\logo.gif" />
    <Content Include="Resources\max1.png" />
    <Content Include="Resources\max2.png" />
    <Content Include="Resources\max3.png" />
    <Content Include="Resources\max4.png" />
    <Content Include="Resources\menubarbg.png" />
    <Content Include="Resources\menubg.png" />
    <Content Include="Resources\Menuon1.png" />
    <Content Include="Resources\Menuon2.png" />
    <Content Include="Resources\menutoolsbg.png" />
    <Content Include="Resources\menutopbg.png" />
    <Content Include="Resources\mima.gif" />
    <Content Include="Resources\min1.png" />
    <Content Include="Resources\min2.png" />
    <Content Include="Resources\min3.png" />
    <Content Include="Resources\min4.png" />
    <Content Include="Resources\mode_design.gif" />
    <Content Include="Resources\mode_html.gif" />
    <Content Include="Resources\mode_view.gif" />
    <Content Include="Resources\mohe_hljld - 副本 - 副本.png" />
    <Content Include="Resources\mohe_hljld.png" />
    <Content Include="Resources\mohe_wzhz - 副本.png" />
    <Content Include="Resources\mohe_wzhz.png" />
    <Content Include="Resources\mohe_xsehljl - 副本.png" />
    <Content Include="Resources\mohe_xsehljl.png" />
    <Content Include="Resources\msgtitlebg.png" />
    <Content Include="Resources\no.png" />
    <Content Include="Resources\numberedlist.gif" />
    <Content Include="Resources\numberedlist1.gif" />
    <Content Include="Resources\nv_body_topbg.gif" />
    <Content Include="Resources\nv_choice.png" />
    <Content Include="Resources\nv_directory.ico" />
    <Content Include="Resources\nv_directory.png" />
    <Content Include="Resources\nv_left_topbg.gif" />
    <Content Include="Resources\nv_user.ico" />
    <Content Include="Resources\nv_user.png" />
    <Content Include="Resources\outdent.gif" />
    <Content Include="Resources\paste.gif" />
    <Content Include="Resources\print.gif" />
    <Content Include="Resources\redo.gif" />
    <Content Include="Resources\removeformat.gif" />
    <Content Include="Resources\replace.gif" />
    <Content Include="Resources\save.gif" />
    <Content Include="Resources\SelectAll.gif" />
    <Content Include="Resources\size_status.png" />
    <Content Include="Resources\specialchar.gif" />
    <Content Include="Resources\splitterbg.png" />
    <Content Include="Resources\strikethrough.gif" />
    <Content Include="Resources\style.gif" />
    <Content Include="Resources\subscript.gif" />
    <Content Include="Resources\superscript.gif" />
    <Content Include="Resources\table.gif" />
    <Content Include="Resources\tablebg.gif" />
    <Content Include="Resources\time.gif" />
    <Content Include="Resources\titlebg.png" />
    <Content Include="Resources\today.gif" />
    <Content Include="Resources\toolsbg.png" />
    <Content Include="Resources\tool_backward.png" />
    <Content Include="Resources\tool_forward.png" />
    <Content Include="Resources\tool_go.png" />
    <Content Include="Resources\tool_home.png" />
    <Content Include="Resources\tool_refresh.png" />
    <Content Include="Resources\tool_restore.png" />
    <Content Include="Resources\tool_search.png" />
    <Content Include="Resources\tool_sidebar.png" />
    <Content Include="Resources\tool_stop.png" />
    <Content Include="Resources\toptitle.gif" />
    <Content Include="Resources\UCase.gif" />
    <Content Include="Resources\UCase1.gif" />
    <Content Include="Resources\underline.gif" />
    <Content Include="Resources\undo.gif" />
    <Content Include="Resources\visable1.png" />
    <Content Include="Resources\visable2.png" />
    <Content Include="Resources\wnl.png" />
    <Content Include="Resources\zhanghaobg.png" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\CustomControl\CustomControl.csproj">
      <Project>{12BF4168-D60E-4A6C-85BF-926130EE6A6D}</Project>
      <Name>CustomControl</Name>
    </ProjectReference>
    <ProjectReference Include="..\HisVar\HisVar.vbproj">
      <Project>{4DAD5E14-6224-46B2-886D-6D22CE3886BC}</Project>
      <Name>HisVar</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include="Microsoft.Net.Client.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1 Client Profile</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.2.0">
      <Visible>False</Visible>
      <ProductName>.NET Framework 2.0 %28x86%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.0">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.0 %28x86%29</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.VisualBasic.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>