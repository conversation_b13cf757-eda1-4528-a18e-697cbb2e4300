﻿/**  版本信息模板在安装目录下，可自行修改。
* D_KC22.cs
*
* 功 能： N/A
* 类 名： D_KC22
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/8/24 08:44:38   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
namespace DAL
{
	/// <summary>
	/// 数据访问类:D_KC22
	/// </summary>
	public partial class D_KC22
	{
		public D_KC22()
		{}
		#region  BasicMethod

        ///// <summary>
        ///// 是否存在该记录
        ///// </summary>
        //public bool Exists(string AKC190,string AKC220,DateTime AKC221,string AKC515)
        //{
        //    StringBuilder strSql=new StringBuilder();
        //    strSql.Append("select count(1) from KC22");
        //    strSql.Append(" where AKC190=@AKC190 and AKC220=@AKC220 and AKC221=@AKC221 and AKC515=@AKC515 ");
        //    SqlParameter[] parameters = {
        //            new SqlParameter("@AKC190", SqlDbType.VarChar,18),
        //            new SqlParameter("@AKC220", SqlDbType.VarChar,20),
        //            new SqlParameter("@AKC221", SqlDbType.DateTime),
        //            new SqlParameter("@AKC515", SqlDbType.VarChar,20)			};
        //    parameters[0].Value = AKC190;
        //    parameters[1].Value = AKC220;
        //    parameters[2].Value = AKC221;
        //    parameters[3].Value = AKC515;

        //    return  HisVar.HisVar.Sqldal.Exists(strSql.ToString(),parameters);
        //}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(Model.M_KC22 model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into KC22(");
			strSql.Append("AKC190,AKC220,AKC221,AKC515,AKC223,AKC224,AKC225,AKC226,AKC227,AKA070,AKA071,AKA076,AKA072,AKA073,AKC229,ZKA100,CKC126)");
			strSql.Append(" values (");
			strSql.Append("@AKC190,@AKC220,@AKC221,@AKC515,@AKC223,@AKC224,@AKC225,@AKC226,@AKC227,@AKA070,@AKA071,@AKA076,@AKA072,@AKA073,@AKC229,@ZKA100,@CKC126)");
			SqlParameter[] parameters = {
					new SqlParameter("@AKC190", SqlDbType.VarChar,18),
					new SqlParameter("@AKC220", SqlDbType.VarChar,20),
					new SqlParameter("@AKC221", SqlDbType.DateTime),
					new SqlParameter("@AKC515", SqlDbType.VarChar,20),
					new SqlParameter("@AKC223", SqlDbType.VarChar,100),
					new SqlParameter("@AKC224", SqlDbType.VarChar,3),
					new SqlParameter("@AKC225", SqlDbType.Decimal,9),
					new SqlParameter("@AKC226", SqlDbType.Decimal,5),
					new SqlParameter("@AKC227", SqlDbType.Decimal,5),
					new SqlParameter("@AKA070", SqlDbType.VarChar,100),
					new SqlParameter("@AKA071", SqlDbType.Decimal,5),
					new SqlParameter("@AKA076", SqlDbType.VarChar,10),
					new SqlParameter("@AKA072", SqlDbType.VarChar,20),
					new SqlParameter("@AKA073", SqlDbType.VarChar,50),
					new SqlParameter("@AKC229", SqlDbType.Decimal,5),
					new SqlParameter("@ZKA100", SqlDbType.VarChar,50),
					new SqlParameter("@CKC126", SqlDbType.Decimal,5)};
			parameters[0].Value = model.AKC190;
			parameters[1].Value = model.AKC220;
			parameters[2].Value = model.AKC221;
			parameters[3].Value = model.AKC515;
			parameters[4].Value = model.AKC223;
			parameters[5].Value = model.AKC224;
			parameters[6].Value = model.AKC225;
			parameters[7].Value = model.AKC226;
			parameters[8].Value = model.AKC227;
			parameters[9].Value = model.AKA070;
			parameters[10].Value = model.AKA071;
			parameters[11].Value = model.AKA076;
			parameters[12].Value = model.AKA072;
			parameters[13].Value = model.AKA073;
			parameters[14].Value = model.AKC229;
			parameters[15].Value = model.ZKA100;
			parameters[16].Value = model.CKC126;

			int rows= HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
        /// <summary>
		/// 按条件更新数据
		/// </summary>
        public bool Update(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("UPDATE dbo.KC22 SET CKC126 = '1',EditState = '1' ");

            if (!string.IsNullOrEmpty(strWhere))
            {
                strSql.Append(" where " + strWhere);
            }

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 将记录标记为不可用  IsEnable 0 不可用，1 可用
        /// </summary>
        /// <param name="strWhere"></param>
        /// <returns></returns>
        public bool UpdateIsEnable(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("UPDATE KC22 SET IsEnable = '0' ");

            if (!string.IsNullOrEmpty(strWhere))
            {
                strSql.Append(" where " + strWhere);
            }

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.M_KC22 model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update KC22 set ");
			strSql.Append("AKC223=@AKC223,");
			strSql.Append("AKC224=@AKC224,");
			strSql.Append("AKC225=@AKC225,");
			strSql.Append("AKC226=@AKC226,");
			strSql.Append("AKC227=@AKC227,");
			strSql.Append("AKA070=@AKA070,");
			strSql.Append("AKA071=@AKA071,");
			strSql.Append("AKA076=@AKA076,");
			strSql.Append("AKA072=@AKA072,");
			strSql.Append("AKA073=@AKA073,");
			strSql.Append("AKC229=@AKC229,");
			strSql.Append("ZKA100=@ZKA100,");
			strSql.Append("CKC126=@CKC126");
			strSql.Append(" where AKC190=@AKC190 and AKC220=@AKC220 and AKC221=@AKC221 and AKC515=@AKC515 ");
			SqlParameter[] parameters = {
					new SqlParameter("@AKC223", SqlDbType.VarChar,100),
					new SqlParameter("@AKC224", SqlDbType.VarChar,3),
					new SqlParameter("@AKC225", SqlDbType.Decimal,9),
					new SqlParameter("@AKC226", SqlDbType.Decimal,5),
					new SqlParameter("@AKC227", SqlDbType.Decimal,5),
					new SqlParameter("@AKA070", SqlDbType.VarChar,100),
					new SqlParameter("@AKA071", SqlDbType.Decimal,5),
					new SqlParameter("@AKA076", SqlDbType.VarChar,10),
					new SqlParameter("@AKA072", SqlDbType.VarChar,20),
					new SqlParameter("@AKA073", SqlDbType.VarChar,50),
					new SqlParameter("@AKC229", SqlDbType.Decimal,5),
					new SqlParameter("@ZKA100", SqlDbType.VarChar,50),
					new SqlParameter("@CKC126", SqlDbType.Decimal,5),
					new SqlParameter("@AKC190", SqlDbType.VarChar,18),
					new SqlParameter("@AKC220", SqlDbType.VarChar,20),
					new SqlParameter("@AKC221", SqlDbType.DateTime),
					new SqlParameter("@AKC515", SqlDbType.VarChar,20)};
            if (model.AKC223 == null)
            {
                parameters[0].Value = DBNull.Value;
            }
            else
            {
                parameters[0].Value = model.AKC223;
            }
			
			parameters[1].Value = model.AKC224;
			parameters[2].Value = model.AKC225;
			parameters[3].Value = model.AKC226;
			parameters[4].Value = model.AKC227;
            if (model.AKA070 == null)
            {
                parameters[5].Value = DBNull.Value;
            }
            else
            {
                parameters[5].Value = model.AKA070;
            }
            if (model.AKA071 == null)
            {
                parameters[6].Value = DBNull.Value;
            }
            else
            {
                parameters[6].Value = model.AKA071;
            }
            if (model.AKA076 == null)
            {
                parameters[7].Value = DBNull.Value;
            }
            else
            {
                parameters[7].Value = model.AKA076;
            }
            if (model.AKA072 == null)
            {
                parameters[8].Value = DBNull.Value;
            }
            else
            {
                parameters[8].Value = model.AKA072;
            }
            if (model.AKA073 == null)
            {
                parameters[9].Value = DBNull.Value;
            }
            else
            {
                parameters[9].Value = model.AKA073;
            }
            if (model.AKC229 == null)
            {
                parameters[10].Value = DBNull.Value;
            }
            else
            {
                parameters[10].Value = model.AKC229;
            }
            if (model.ZKA100 == null)
            {
                parameters[11].Value = DBNull.Value;
            }
            else
            {
                parameters[11].Value = model.ZKA100;
            }
	        //todo:新增字段没写
			parameters[12].Value = model.CKC126;
			parameters[13].Value = model.AKC190;
			parameters[14].Value = model.AKC220;
			parameters[15].Value = model.AKC221;
			parameters[16].Value = model.AKC515;

			int rows= HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string AKC190,string AKC220,DateTime AKC221,string AKC515)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from KC22 ");
			strSql.Append(" where AKC190=@AKC190 and AKC220=@AKC220 and AKC221=@AKC221 and AKC515=@AKC515 ");
			SqlParameter[] parameters = {
					new SqlParameter("@AKC190", SqlDbType.VarChar,18),
					new SqlParameter("@AKC220", SqlDbType.VarChar,20),
					new SqlParameter("@AKC221", SqlDbType.DateTime),
					new SqlParameter("@AKC515", SqlDbType.VarChar,20)			};
			parameters[0].Value = AKC190;
			parameters[1].Value = AKC220;
			parameters[2].Value = AKC221;
			parameters[3].Value = AKC515;

			int rows= HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

        /// <summary>
        /// 删除多条数据
        /// </summary>
        public bool Delete(string AKC190)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from KC22 ");
            strSql.Append(" where AKC190=@AKC190 ");
            SqlParameter[] parameters = {
					new SqlParameter("@AKC190", SqlDbType.VarChar,18)			};
            parameters[0].Value = AKC190;

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.M_KC22 GetModel(string AKC190, string AKC220, DateTime AKC221, string AKC515)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 AKC190,AKC220,AKC221,AKC515,AKC223,AKC224,AKC225,AKC226,AKC227,AKA070,AKA071,AKA076,AKA072,AKA073,AKC229,ZKA100,CKC126 from KC22 ");
            strSql.Append(" where AKC190=@AKC190 and AKC220=@AKC220 and AKC221=@AKC221 and AKC515=@AKC515 ");
            SqlParameter[] parameters = {
                    new SqlParameter("@AKC190", SqlDbType.VarChar,18),
                    new SqlParameter("@AKC220", SqlDbType.VarChar,20),
                    new SqlParameter("@AKC221", SqlDbType.DateTime),
                    new SqlParameter("@AKC515", SqlDbType.VarChar,20)			};
            parameters[0].Value = AKC190;
            parameters[1].Value = AKC220;
            parameters[2].Value = AKC221;
            parameters[3].Value = AKC515;

            Model.M_KC22 model = new Model.M_KC22();
            DataSet ds = HisVar.HisVar.Sqldal.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.M_KC22 DataRowToModel(DataRow row)
		{
			Model.M_KC22 model=new Model.M_KC22();
			if (row != null)
			{
				if(row["AKC190"]!=null)
				{
					model.AKC190=row["AKC190"].ToString();
				}
				if(row["AKC220"]!=null)
				{
					model.AKC220=row["AKC220"].ToString();
				}
				if(row["AKC221"]!=null && row["AKC221"].ToString()!="")
				{
					model.AKC221=DateTime.Parse(row["AKC221"].ToString());
				}
				if(row["AKC515"]!=null)
				{
					model.AKC515=row["AKC515"].ToString();
				}
				if(row["AKC223"]!=null)
				{
					model.AKC223=row["AKC223"].ToString();
				}
				if(row["AKC224"]!=null)
				{
					model.AKC224=row["AKC224"].ToString();
				}
				if(row["AKC225"]!=null && row["AKC225"].ToString()!="")
				{
					model.AKC225=decimal.Parse(row["AKC225"].ToString());
				}
				if(row["AKC226"]!=null && row["AKC226"].ToString()!="")
				{
					model.AKC226=decimal.Parse(row["AKC226"].ToString());
				}
				if(row["AKC227"]!=null && row["AKC227"].ToString()!="")
				{
					model.AKC227=decimal.Parse(row["AKC227"].ToString());
				}
				if(row["AKA070"]!=null)
				{
					model.AKA070=row["AKA070"].ToString();
				}
				if(row["AKA071"]!=null && row["AKA071"].ToString()!="")
				{
					model.AKA071=decimal.Parse(row["AKA071"].ToString());
				}
				if(row["AKA076"]!=null)
				{
					model.AKA076=row["AKA076"].ToString();
				}
				if(row["AKA072"]!=null)
				{
					model.AKA072=row["AKA072"].ToString();
				}
				if(row["AKA073"]!=null)
				{
					model.AKA073=row["AKA073"].ToString();
				}
				if(row["AKC229"]!=null && row["AKC229"].ToString()!="")
				{
					model.AKC229=decimal.Parse(row["AKC229"].ToString());
				}
				if(row["ZKA100"]!=null)
				{
					model.ZKA100=row["ZKA100"].ToString();
				}
				if(row["CKC126"]!=null && row["CKC126"].ToString()!="")
				{
					model.CKC126=decimal.Parse(row["CKC126"].ToString());
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append(" SELECT  KC22.AKC190 ,KC22.AKC220 ,KC22.AKC221 ,KC22.AKC515 , KC22.AKC223 ,KC22.AKC224 ,KC22.AKC225 ,KC22.AKC226 ,KC22.AKC227 ,KC22.AKA070 ,KC22.AKA071 ,KC22.AKA076 ,KC22.AKA072 ,KC22.AKA073 ,KC22.AKC229 ,KC22.ZKA100 ,KC22.CKC126 , ");
            strSql.Append(" KC22.EditState ,KC22.old_AKC221,KC22.IsYearEnd,KC22.IsEnable,HisCount,");
            strSql.Append(" CASE KC21.Ry_Bxlb WHEN '城镇职工' THEN ISNULL(Yb_InfoDy.Zg_FyDj,'')  WHEN '城镇居民' THEN ISNULL(Yb_InfoDy.Jm_FyDj,'') ELSE ISNULL(Yb_InfoDy.Gs_FyDj,'') END AS FyDj ,ZKC258,KC22.BKF050,AKC008,KC22.BKF040,AKC025 ");
            strSql.Append(" FROM KC22 LEFT JOIN Yb_InfoDy ON KC22.AKC515 = Yb_InfoDy.His_Code,KC21 ");
            strSql.Append(" WHERE   KC22.AKC190 = KC21.AKC190  ");
			if(strWhere.Trim()!="")
			{
                strSql.Append(" AND " + strWhere);
			}
            strSql.Append(" ORDER BY KC22.IsYearEnd ASC, KC22.AKC220,KC22.AKC221 ");
			return  HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" AKC190,AKC220,AKC221,AKC515,AKC223,AKC224,AKC225,AKC226,AKC227,AKA070,AKA071,AKA076,AKA072,AKA073,AKC229,ZKA100,CKC126 ");
			strSql.Append(" FROM KC22 ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return  HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM KC22 ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj =  HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.AKC515 desc");
			}
			strSql.Append(")AS Row, T.*  from KC22 T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return  HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "KC22";
			parameters[1].Value = "AKC515";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return  HisVar.HisVar.Sqldal.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

        public double SumUploadMoney(string akc190)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(" SELECT  SUM(AKC227) FROM dbo.KC22 WHERE  CKC126 = 1 AND EditState = 1 AND IsEnable = 1 ");
            if (akc190.Trim() != "")
            {
                strSql.Append(" AND SUBSTRING(AKC190,1,16) = 'ZY" + akc190 + "'");
            }
            object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToDouble(obj);
            }
        }


		#endregion  ExtensionMethod
	}
}

