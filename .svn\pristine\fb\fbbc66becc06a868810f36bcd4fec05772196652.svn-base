﻿/**  版本信息模板在安装目录下，可自行修改。
* D_LIS_Test1.cs
*
* 功 能： N/A
* 类 名： D_LIS_Test1
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/10/21 15:19:57   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;

namespace DAL
{
	/// <summary>
	/// 数据访问类:D_LIS_Test1
	/// </summary>
	public partial class D_LIS_Test1
	{
		public D_LIS_Test1()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string Test_Code,string Test_Lb,string His_Code)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from LIS_Test1");
			strSql.Append(" where Test_Code=@Test_Code and Test_Lb=@Test_Lb and His_Code=@His_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Test_Code", SqlDbType.Char,11),
					new SqlParameter("@Test_Lb", SqlDbType.Char,4),
					new SqlParameter("@His_Code", SqlDbType.Char,14)			};
			parameters[0].Value = Test_Code;
			parameters[1].Value = Test_Lb;
			parameters[2].Value = His_Code;

			return HisVar.HisVar .Sqldal .Exists(strSql.ToString(),parameters);
		}

        public string MaxCode()
        {
          string max = DateTime.Now.ToString("yyMMdd") + (string)(HisVar.HisVar.Sqldal.F_MaxCode("select max(substring(Test_Code,7,5)) from LIS_Test1 where substring(Test_Code,1,6)='" + DateTime.Now.ToString("yyMMdd") + "'", 5));
         return max;
        }

		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ModelOld.M_LIS_Test1 model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into LIS_Test1(");
			strSql.Append("Test_Code,Test_Lb,His_Code,Ry_Name,Ry_Sfzh,Ry_Sex,JKkNo,Ry_Age,Ry_Age_Month,Ry_Age_Week,Bxlb_Code,Ks_Code,Bc_Code,TestXm_Code,TestSample,TestSample_BarCode,SendJsr,SendDoctor,SendTime,GetJsr,GetDoctor,GetTime,TestJsr,TestTime,CheckJsr,CheckTime,LisPrintTimes,SelfPrintTimes,Diagnose,Memo,TestState)");
			strSql.Append(" values (");
			strSql.Append("@Test_Code,@Test_Lb,@His_Code,@Ry_Name,@Ry_Sfzh,@Ry_Sex,@JKkNo,@Ry_Age,@Ry_Age_Month,@Ry_Age_Week,@Bxlb_Code,@Ks_Code,@Bc_Code,@TestXm_Code,@TestSample,@TestSample_BarCode,@SendJsr,@SendDoctor,@SendTime,@GetJsr,@GetDoctor,@GetTime,@TestJsr,@TestTime,@CheckJsr,@CheckTime,@LisPrintTimes,@SelfPrintTimes,@Diagnose,@Memo,@TestState)");
			SqlParameter[] parameters = {
					new SqlParameter("@Test_Code", SqlDbType.Char,11),
					new SqlParameter("@Test_Lb", SqlDbType.Char,4),
					new SqlParameter("@His_Code", SqlDbType.Char,14),
					new SqlParameter("@Ry_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Ry_Sfzh", SqlDbType.VarChar,18),
					new SqlParameter("@Ry_Sex", SqlDbType.Char,2),
					new SqlParameter("@JKkNo", SqlDbType.VarChar,19),
					new SqlParameter("@Ry_Age", SqlDbType.Int,4),
					new SqlParameter("@Ry_Age_Month", SqlDbType.Int,4),
					new SqlParameter("@Ry_Age_Week", SqlDbType.Int,4),
					new SqlParameter("@Bxlb_Code", SqlDbType.Char,6),
					new SqlParameter("@Ks_Code", SqlDbType.Char,6),
					new SqlParameter("@Bc_Code", SqlDbType.Char,7),
					new SqlParameter("@TestXm_Code", SqlDbType.Char,5),
					new SqlParameter("@TestSample", SqlDbType.VarChar,50),
					new SqlParameter("@TestSample_BarCode", SqlDbType.VarChar,50),
					new SqlParameter("@SendJsr", SqlDbType.Char,7),
					new SqlParameter("@SendDoctor", SqlDbType.VarChar,50),
					new SqlParameter("@SendTime", SqlDbType.SmallDateTime),
					new SqlParameter("@GetJsr", SqlDbType.Char,7),
					new SqlParameter("@GetDoctor", SqlDbType.VarChar,50),
					new SqlParameter("@GetTime", SqlDbType.SmallDateTime),
					new SqlParameter("@TestJsr", SqlDbType.Char,7),
					new SqlParameter("@TestTime", SqlDbType.SmallDateTime),
					new SqlParameter("@CheckJsr", SqlDbType.Char,7),
					new SqlParameter("@CheckTime", SqlDbType.SmallDateTime),
					new SqlParameter("@LisPrintTimes", SqlDbType.Int,4),
					new SqlParameter("@SelfPrintTimes", SqlDbType.Int,4),
					new SqlParameter("@Diagnose", SqlDbType.VarChar,50),
					new SqlParameter("@Memo", SqlDbType.VarChar,50),
					new SqlParameter("@TestState", SqlDbType.VarChar,4)};
			parameters[0].Value = model.Test_Code;
			parameters[1].Value = model.Test_Lb;
			parameters[2].Value = model.His_Code;
			parameters[3].Value = model.Ry_Name;
			parameters[4].Value = model.Ry_Sfzh;
			parameters[5].Value = model.Ry_Sex;
			parameters[6].Value = model.JKkNo;
			parameters[7].Value = model.Ry_Age;
			parameters[8].Value = model.Ry_Age_Month;
			parameters[9].Value = model.Ry_Age_Week;
			parameters[10].Value = model.Bxlb_Code;
			parameters[11].Value = model.Ks_Code;
			parameters[12].Value = model.Bc_Code;
			parameters[13].Value = model.TestXm_Code;
			parameters[14].Value = model.TestSample;
			parameters[15].Value = model.TestSample_BarCode;
			parameters[16].Value = model.SendJsr;
			parameters[17].Value = model.SendDoctor;
			parameters[18].Value = model.SendTime;
			parameters[19].Value = model.GetJsr;
			parameters[20].Value = model.GetDoctor;
			parameters[21].Value = model.GetTime;
			parameters[22].Value = model.TestJsr;
			parameters[23].Value = model.TestTime;
			parameters[24].Value = model.CheckJsr;
			parameters[25].Value = model.CheckTime;
			parameters[26].Value = model.LisPrintTimes;
			parameters[27].Value = model.SelfPrintTimes;
			parameters[28].Value = model.Diagnose;
			parameters[29].Value = model.Memo;
			parameters[30].Value = model.TestState;

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ModelOld.M_LIS_Test1 model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update LIS_Test1 set ");
			strSql.Append("Ry_Name=@Ry_Name,");
			strSql.Append("Ry_Sfzh=@Ry_Sfzh,");
			strSql.Append("Ry_Sex=@Ry_Sex,");
			strSql.Append("JKkNo=@JKkNo,");
			strSql.Append("Ry_Age=@Ry_Age,");
			strSql.Append("Ry_Age_Month=@Ry_Age_Month,");
			strSql.Append("Ry_Age_Week=@Ry_Age_Week,");
			strSql.Append("Bxlb_Code=@Bxlb_Code,");
			strSql.Append("Ks_Code=@Ks_Code,");
			strSql.Append("Bc_Code=@Bc_Code,");
			strSql.Append("TestXm_Code=@TestXm_Code,");
			strSql.Append("TestSample=@TestSample,");
			strSql.Append("TestSample_BarCode=@TestSample_BarCode,");
			strSql.Append("SendJsr=@SendJsr,");
			strSql.Append("SendDoctor=@SendDoctor,");
			strSql.Append("SendTime=@SendTime,");
			strSql.Append("GetJsr=@GetJsr,");
			strSql.Append("GetDoctor=@GetDoctor,");
			strSql.Append("GetTime=@GetTime,");
			strSql.Append("TestJsr=@TestJsr,");
			strSql.Append("TestTime=@TestTime,");
			strSql.Append("CheckJsr=@CheckJsr,");
			strSql.Append("CheckTime=@CheckTime,");
			strSql.Append("LisPrintTimes=@LisPrintTimes,");
			strSql.Append("SelfPrintTimes=@SelfPrintTimes,");
			strSql.Append("Diagnose=@Diagnose,");
			strSql.Append("Memo=@Memo,");
			strSql.Append("TestState=@TestState");
			strSql.Append(" where Test_Code=@Test_Code and Test_Lb=@Test_Lb and His_Code=@His_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Ry_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Ry_Sfzh", SqlDbType.VarChar,18),
					new SqlParameter("@Ry_Sex", SqlDbType.Char,2),
					new SqlParameter("@JKkNo", SqlDbType.VarChar,19),
					new SqlParameter("@Ry_Age", SqlDbType.Int,4),
					new SqlParameter("@Ry_Age_Month", SqlDbType.Int,4),
					new SqlParameter("@Ry_Age_Week", SqlDbType.Int,4),
					new SqlParameter("@Bxlb_Code", SqlDbType.Char,6),
					new SqlParameter("@Ks_Code", SqlDbType.Char,6),
					new SqlParameter("@Bc_Code", SqlDbType.Char,7),
					new SqlParameter("@TestXm_Code", SqlDbType.Char,5),
					new SqlParameter("@TestSample", SqlDbType.VarChar,50),
					new SqlParameter("@TestSample_BarCode", SqlDbType.VarChar,50),
					new SqlParameter("@SendJsr", SqlDbType.Char,7),
					new SqlParameter("@SendDoctor", SqlDbType.VarChar,50),
					new SqlParameter("@SendTime", SqlDbType.SmallDateTime),
					new SqlParameter("@GetJsr", SqlDbType.Char,7),
					new SqlParameter("@GetDoctor", SqlDbType.VarChar,50),
					new SqlParameter("@GetTime", SqlDbType.SmallDateTime),
					new SqlParameter("@TestJsr", SqlDbType.Char,7),
					new SqlParameter("@TestTime", SqlDbType.SmallDateTime),
					new SqlParameter("@CheckJsr", SqlDbType.Char,7),
					new SqlParameter("@CheckTime", SqlDbType.SmallDateTime),
					new SqlParameter("@LisPrintTimes", SqlDbType.Int,4),
					new SqlParameter("@SelfPrintTimes", SqlDbType.Int,4),
					new SqlParameter("@Diagnose", SqlDbType.VarChar,50),
					new SqlParameter("@Memo", SqlDbType.VarChar,50),
					new SqlParameter("@TestState", SqlDbType.VarChar,4),
					new SqlParameter("@Test_Code", SqlDbType.Char,11),
					new SqlParameter("@Test_Lb", SqlDbType.Char,4),
					new SqlParameter("@His_Code", SqlDbType.Char,14)};
			parameters[0].Value = model.Ry_Name;
			parameters[1].Value = model.Ry_Sfzh;
			parameters[2].Value = model.Ry_Sex;
			parameters[3].Value = model.JKkNo;
			parameters[4].Value = model.Ry_Age;
			parameters[5].Value = model.Ry_Age_Month;
			parameters[6].Value = model.Ry_Age_Week;
			parameters[7].Value = model.Bxlb_Code;
			parameters[8].Value = model.Ks_Code;
			parameters[9].Value = model.Bc_Code;
			parameters[10].Value = model.TestXm_Code;
			parameters[11].Value = model.TestSample;
			parameters[12].Value = model.TestSample_BarCode;
			parameters[13].Value = model.SendJsr;
			parameters[14].Value = model.SendDoctor;
			parameters[15].Value = model.SendTime;
			parameters[16].Value = model.GetJsr;
			parameters[17].Value = model.GetDoctor;
			parameters[18].Value = model.GetTime;
			parameters[19].Value = model.TestJsr;
			parameters[20].Value = model.TestTime;
			parameters[21].Value = model.CheckJsr;
			parameters[22].Value = model.CheckTime;
			parameters[23].Value = model.LisPrintTimes;
			parameters[24].Value = model.SelfPrintTimes;
			parameters[25].Value = model.Diagnose;
			parameters[26].Value = model.Memo;
			parameters[27].Value = model.TestState;
			parameters[28].Value = model.Test_Code;
			parameters[29].Value = model.Test_Lb;
			parameters[30].Value = model.His_Code;

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string Test_Code,string Test_Lb,string His_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from LIS_Test1 ");
			strSql.Append(" where Test_Code=@Test_Code and Test_Lb=@Test_Lb and His_Code=@His_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Test_Code", SqlDbType.Char,11),
					new SqlParameter("@Test_Lb", SqlDbType.Char,4),
					new SqlParameter("@His_Code", SqlDbType.Char,14)			};
			parameters[0].Value = Test_Code;
			parameters[1].Value = Test_Lb;
			parameters[2].Value = His_Code;

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_LIS_Test1 GetModel(string Test_Code,string Test_Lb,string His_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 Test_Code,Test_Lb,His_Code,Ry_Name,Ry_Sfzh,Ry_Sex,JKkNo,Ry_Age,Ry_Age_Month,Ry_Age_Week,Bxlb_Code,Ks_Code,Bc_Code,TestXm_Code,TestSample,TestSample_BarCode,SendJsr,SendDoctor,SendTime,GetJsr,GetDoctor,GetTime,TestJsr,TestTime,CheckJsr,CheckTime,LisPrintTimes,SelfPrintTimes,Diagnose,Memo,TestItem1,TestState from LIS_Test1 ");
			strSql.Append(" where Test_Code=@Test_Code and Test_Lb=@Test_Lb and His_Code=@His_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Test_Code", SqlDbType.Char,11),
					new SqlParameter("@Test_Lb", SqlDbType.Char,4),
					new SqlParameter("@His_Code", SqlDbType.Char,14)			};
			parameters[0].Value = Test_Code;
			parameters[1].Value = Test_Lb;
			parameters[2].Value = His_Code;

			ModelOld.M_LIS_Test1 model=new ModelOld.M_LIS_Test1();
            DataSet ds = HisVar.HisVar.Sqldal.Query(strSql.ToString(), parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_LIS_Test1 DataRowToModel(DataRow row)
		{
			ModelOld.M_LIS_Test1 model=new ModelOld.M_LIS_Test1();
			if (row != null)
			{
				if(row["Test_Code"]!=null)
				{
					model.Test_Code=row["Test_Code"].ToString();
				}
				if(row["Test_Lb"]!=null)
				{
					model.Test_Lb=row["Test_Lb"].ToString();
				}
				if(row["His_Code"]!=null)
				{
					model.His_Code=row["His_Code"].ToString();
				}
				if(row["Ry_Name"]!=null)
				{
					model.Ry_Name=row["Ry_Name"].ToString();
				}
				if(row["Ry_Sfzh"]!=null)
				{
					model.Ry_Sfzh=row["Ry_Sfzh"].ToString();
				}
				if(row["Ry_Sex"]!=null)
				{
					model.Ry_Sex=row["Ry_Sex"].ToString();
				}
				if(row["JKkNo"]!=null)
				{
					model.JKkNo=row["JKkNo"].ToString();
				}
				if(row["Ry_Age"]!=null && row["Ry_Age"].ToString()!="")
				{
					model.Ry_Age=int.Parse(row["Ry_Age"].ToString());
				}
				if(row["Ry_Age_Month"]!=null && row["Ry_Age_Month"].ToString()!="")
				{
					model.Ry_Age_Month=int.Parse(row["Ry_Age_Month"].ToString());
				}
				if(row["Ry_Age_Week"]!=null && row["Ry_Age_Week"].ToString()!="")
				{
					model.Ry_Age_Week=int.Parse(row["Ry_Age_Week"].ToString());
				}
				if(row["Bxlb_Code"]!=null)
				{
					model.Bxlb_Code=row["Bxlb_Code"].ToString();
				}
				if(row["Ks_Code"]!=null)
				{
					model.Ks_Code=row["Ks_Code"].ToString();
				}
				if(row["Bc_Code"]!=null)
				{
					model.Bc_Code=row["Bc_Code"].ToString();
				}
				if(row["TestXm_Code"]!=null)
				{
					model.TestXm_Code=row["TestXm_Code"].ToString();
				}
				if(row["TestSample"]!=null)
				{
					model.TestSample=row["TestSample"].ToString();
				}
				if(row["TestSample_BarCode"]!=null)
				{
					model.TestSample_BarCode=row["TestSample_BarCode"].ToString();
				}
				if(row["SendJsr"]!=null)
				{
					model.SendJsr=row["SendJsr"].ToString();
				}
				if(row["SendDoctor"]!=null)
				{
					model.SendDoctor=row["SendDoctor"].ToString();
				}
				if(row["SendTime"]!=null && row["SendTime"].ToString()!="")
				{
					model.SendTime=DateTime.Parse(row["SendTime"].ToString());
				}
				if(row["GetJsr"]!=null)
				{
					model.GetJsr=row["GetJsr"].ToString();
				}
				if(row["GetDoctor"]!=null)
				{
					model.GetDoctor=row["GetDoctor"].ToString();
				}
				if(row["GetTime"]!=null && row["GetTime"].ToString()!="")
				{
					model.GetTime=DateTime.Parse(row["GetTime"].ToString());
				}
				if(row["TestJsr"]!=null)
				{
					model.TestJsr=row["TestJsr"].ToString();
				}
				if(row["TestTime"]!=null && row["TestTime"].ToString()!="")
				{
					model.TestTime=DateTime.Parse(row["TestTime"].ToString());
				}
				if(row["CheckJsr"]!=null)
				{
					model.CheckJsr=row["CheckJsr"].ToString();
				}
				if(row["CheckTime"]!=null && row["CheckTime"].ToString()!="")
				{
					model.CheckTime=DateTime.Parse(row["CheckTime"].ToString());
				}
				if(row["LisPrintTimes"]!=null && row["LisPrintTimes"].ToString()!="")
				{
					model.LisPrintTimes=int.Parse(row["LisPrintTimes"].ToString());
				}
				if(row["SelfPrintTimes"]!=null && row["SelfPrintTimes"].ToString()!="")
				{
					model.SelfPrintTimes=int.Parse(row["SelfPrintTimes"].ToString());
				}
				if(row["Diagnose"]!=null)
				{
					model.Diagnose=row["Diagnose"].ToString();
				}
				if(row["Memo"]!=null)
				{
					model.Memo=row["Memo"].ToString();
				}
				if(row["TestItem1"]!=null && row["TestItem1"].ToString()!="")
				{
					model.TestItem1=(byte[])row["TestItem1"];
				}
				if(row["TestState"]!=null)
				{
					model.TestState=row["TestState"].ToString();
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select Test_Code,Test_Lb,His_Code,Ry_Name,Ry_Sfzh,Ry_Sex,JKkNo,Ry_Age,Ry_Age_Month,Ry_Age_Week,Bxlb_Code,Ks_Code,Bc_Code,TestXm_Code,TestSample,TestSample_BarCode,SendJsr,SendDoctor,SendTime,GetJsr,GetDoctor,GetTime,TestJsr,TestTime,CheckJsr,CheckTime,LisPrintTimes,SelfPrintTimes,Diagnose,Memo,TestItem1,TestState ");
			strSql.Append(" FROM LIS_Test1 ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return HisVar.HisVar .Sqldal .Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" Test_Code,Test_Lb,His_Code,Ry_Name,Ry_Sfzh,Ry_Sex,JKkNo,Ry_Age,Ry_Age_Month,Ry_Age_Week,Bxlb_Code,Ks_Code,Bc_Code,TestXm_Code,TestSample,TestSample_BarCode,SendJsr,SendDoctor,SendTime,GetJsr,GetDoctor,GetTime,TestJsr,TestTime,CheckJsr,CheckTime,LisPrintTimes,SelfPrintTimes,Diagnose,Memo,TestItem1,TestState ");
			strSql.Append(" FROM LIS_Test1 ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return HisVar.HisVar .Sqldal .Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM LIS_Test1 ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = HisVar.HisVar .Sqldal .GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.His_Code desc");
			}
			strSql.Append(")AS Row, T.*  from LIS_Test1 T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return HisVar.HisVar .Sqldal .Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "LIS_Test1";
			parameters[1].Value = "His_Code";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return HisVar.HisVar .Sqldal .RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

