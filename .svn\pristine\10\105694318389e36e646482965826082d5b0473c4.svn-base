﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using YBBLL;
using YBModel;

namespace ERX
{
    public partial class Erx_Config : Common.BaseForm.BaseChild
    {
        private BllERx_Config _bllERxConfig = new BllERx_Config();
        private MdlERx_Config _mdlERxConfig = new MdlERx_Config();
        public Erx_Config()
        {
            InitializeComponent();
        }

        private void Erx_Config_Load(object sender, EventArgs e)
        {
            DataInit();
        }
        private void myButton1_Click(object sender, EventArgs e)
        {

            if (!DataSave())
            {
                return;
            }
            MessageBox.Show("更新成功", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            ERXConfig.Erx_Init();
        }


        #region 初始化
        private void DataInit()
        {

            _mdlERxConfig = _bllERxConfig.GetModel(1);
            if (_mdlERxConfig != null)
            {
                TxtAPIUrl.Text = _mdlERxConfig.ERX_Url ?? "";
                TxtappId.Text = _mdlERxConfig.ERX_Id ?? "";
                Txtappkey.Text = _mdlERxConfig.ERX_Key ?? "";
                TxtSecretKey.Text = _mdlERxConfig.ERX_PriKey ?? "";

            }
            else
            {
                TxtAPIUrl.Text = "";
                TxtappId.Text = "";
                Txtappkey.Text = "";
                TxtSecretKey.Text = "";
            }
        }
        #endregion

        #region 数据操作

        private bool DataCheck()
        {
            if (CustomControl.Func.NotAllowEmpty(TxtAPIUrl)) return false;
            if (CustomControl.Func.NotAllowEmpty(TxtappId)) return false;
            if (CustomControl.Func.NotAllowEmpty(Txtappkey)) return false;
            if (CustomControl.Func.NotAllowEmpty(TxtSecretKey)) return false;
            return true;
        }

        private bool DataSave()
        {
            if (!DataCheck()) return false;
            if (_mdlERxConfig == null)
            {
                _mdlERxConfig = new MdlERx_Config();
                _mdlERxConfig.id = 1;
            }

            _mdlERxConfig.ERX_Url = TxtAPIUrl.Text;
            _mdlERxConfig.ERX_Id = TxtappId.Text;
            _mdlERxConfig.ERX_Key = Txtappkey.Text;
            _mdlERxConfig.ERX_PriKey = TxtSecretKey.Text;

            if (_bllERxConfig.Exists(1))
            {
                return _bllERxConfig.Update(_mdlERxConfig);
            }
            else
            {
                return _bllERxConfig.Add(_mdlERxConfig);
            }
        }
        #endregion

    }
}
