﻿<?xml version='1.0' encoding='utf-8'?>
<SettingsFile xmlns="http://schemas.microsoft.com/VisualStudio/2004/01/settings" CurrentProfile="(Default)" GeneratedClassNamespace="My" GeneratedClassName="MySettings" UseMySettingsClassName="true">
  <Profiles />
  <Settings>
    <Setting Name="HisVar_WebReference_DotNetService" Type="(Web Service URL)" Scope="Application">
      <Value Profile="(Default)">http://127.0.0.1/His_DataBase/DotNetService.asmx</Value>
    </Setting>
    <Setting Name="HisVar_MztcXyDb_OracleService" Type="(Web Service URL)" Scope="Application">
      <Value Profile="(Default)">http://localhost/Qx_Database/OracleService.asmx</Value>
    </Setting>
    <Setting Name="HisVar_DzblFileSend_DzblFileSend" Type="(Web Service URL)" Scope="Application">
      <Value Profile="(Default)">http://localhost/His_Database/DzblFileSend.asmx</Value>
    </Setting>
    <Setting Name="HisVar_JkkService_WebService1" Type="(Web Service URL)" Scope="Application">
      <Value Profile="(Default)">http://localhost:8312/WebService1.asmx</Value>
    </Setting>
    <Setting Name="HisVar_YpNetCg_WebService" Type="(Web Service URL)" Scope="Application">
      <Value Profile="(Default)">http://************/Ds_Service_temp/WebService.asmx</Value>
    </Setting>
  </Settings>
</SettingsFile>