ALTER TABLE Bl ADD YbJsMoney NUMERIC(10, 2);
ALTER TABLE Bl ADD Th_Pay_Way_Code CHAR(2);
ALTER TABLE Bl ADD Ry_CyJsDate DATETIME;
ALTER TABLE Bl_Zh ADD YbJsMoney NUMERIC(10, 2);
ALTER TABLE Bl_Zh ADD Th_Pay_Way_Code CHAR(2);
ALTER TABLE Bl_Zh ADD CreateDate DATETIME DEFAULT GETDATE();
ALTER TABLE Bl_Cf ADD CyJz_Code CHAR(12);
GO
UPDATE dbo.Bl SET Ry_CyJsDate=Ry_CyDate WHERE Ry_CyJsDate IS NULL;
UPDATE Bl Set Th_Pay_Way_Code='01' where Th_Pay_Way_Code is NULL
UPDATE Bl_Zh Set Th_Pay_Way_Code='01' where Th_Pay_Way_Code is NULL