﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class Yp_Xx
    Inherits HisControl.Base

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(Yp_Xx))
        Me.ToolTip1 = New System.Windows.Forms.ToolTip(Me.components)
        Me.C1Button1 = New C1.Win.C1Input.C1Button()
        Me.C1Button2 = New C1.Win.C1Input.C1Button()
        Me.Label16 = New System.Windows.Forms.Label()
        Me.Label07 = New System.Windows.Forms.Label()
        Me.C1TextBox4 = New C1.Win.C1Input.C1TextBox()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.C1TextBox1 = New C1.Win.C1Input.C1TextBox()
        Me.DateTimePicker2 = New System.Windows.Forms.DateTimePicker()
        CType(Me.C1TextBox4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1TextBox1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'C1Button1
        '
        Me.C1Button1.Location = New System.Drawing.Point(54, 147)
        Me.C1Button1.Name = "C1Button1"
        Me.C1Button1.Size = New System.Drawing.Size(65, 30)
        Me.C1Button1.TabIndex = 69
        Me.C1Button1.Text = "确定"
        Me.C1Button1.UseVisualStyleBackColor = True
        Me.C1Button1.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.C1Button1.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'C1Button2
        '
        Me.C1Button2.Location = New System.Drawing.Point(136, 147)
        Me.C1Button2.Name = "C1Button2"
        Me.C1Button2.Size = New System.Drawing.Size(65, 30)
        Me.C1Button2.TabIndex = 70
        Me.C1Button2.Text = "取消"
        Me.C1Button2.UseVisualStyleBackColor = True
        Me.C1Button2.VisualStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        Me.C1Button2.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'Label16
        '
        Me.Label16.AutoSize = True
        Me.Label16.Location = New System.Drawing.Point(41, 102)
        Me.Label16.Name = "Label16"
        Me.Label16.Size = New System.Drawing.Size(53, 12)
        Me.Label16.TabIndex = 227
        Me.Label16.Text = "有 效 期"
        '
        'Label07
        '
        Me.Label07.AutoSize = True
        Me.Label07.Location = New System.Drawing.Point(41, 56)
        Me.Label07.Name = "Label07"
        Me.Label07.Size = New System.Drawing.Size(53, 12)
        Me.Label07.TabIndex = 231
        Me.Label07.Text = "生产批号"
        '
        'C1TextBox4
        '
        Me.C1TextBox4.AutoSize = False
        Me.C1TextBox4.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1TextBox4.Location = New System.Drawing.Point(98, 54)
        Me.C1TextBox4.Name = "C1TextBox4"
        Me.C1TextBox4.Size = New System.Drawing.Size(122, 16)
        Me.C1TextBox4.TabIndex = 230
        Me.C1TextBox4.Tag = "说明"
        Me.C1TextBox4.TextDetached = True
        Me.C1TextBox4.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.Location = New System.Drawing.Point(41, 20)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(53, 12)
        Me.Label1.TabIndex = 233
        Me.Label1.Text = "药品名称"
        '
        'C1TextBox1
        '
        Me.C1TextBox1.AutoSize = False
        Me.C1TextBox1.BackColor = System.Drawing.SystemColors.Info
        Me.C1TextBox1.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1TextBox1.Location = New System.Drawing.Point(98, 18)
        Me.C1TextBox1.Name = "C1TextBox1"
        Me.C1TextBox1.ReadOnly = True
        Me.C1TextBox1.Size = New System.Drawing.Size(122, 16)
        Me.C1TextBox1.TabIndex = 232
        Me.C1TextBox1.Tag = "说明"
        Me.C1TextBox1.TextDetached = True
        Me.C1TextBox1.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        '
        'DateTimePicker2
        '
        Me.DateTimePicker2.CustomFormat = "yyyy-MM-dd"
        Me.DateTimePicker2.Format = System.Windows.Forms.DateTimePickerFormat.Custom
        Me.DateTimePicker2.Location = New System.Drawing.Point(98, 98)
        Me.DateTimePicker2.MaxDate = New Date(2079, 1, 1, 0, 0, 0, 0)
        Me.DateTimePicker2.Name = "DateTimePicker2"
        Me.DateTimePicker2.Size = New System.Drawing.Size(122, 21)
        Me.DateTimePicker2.TabIndex = 235
        '
        'Yp_Xx
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(254, 193)
        Me.Controls.Add(Me.DateTimePicker2)
        Me.Controls.Add(Me.Label1)
        Me.Controls.Add(Me.C1TextBox1)
        Me.Controls.Add(Me.Label07)
        Me.Controls.Add(Me.C1TextBox4)
        Me.Controls.Add(Me.Label16)
        Me.Controls.Add(Me.C1Button2)
        Me.Controls.Add(Me.C1Button1)
        Me.Icon = CType(resources.GetObject("$this.Icon"), System.Drawing.Icon)
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.Name = "Yp_Xx"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "药品入库参数修改"
        CType(Me.C1TextBox4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1TextBox1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents ToolTip1 As System.Windows.Forms.ToolTip
    Friend WithEvents C1Button2 As C1.Win.C1Input.C1Button
    Friend WithEvents C1Button1 As C1.Win.C1Input.C1Button
    Friend WithEvents Label16 As System.Windows.Forms.Label
    Friend WithEvents Label07 As System.Windows.Forms.Label
    Friend WithEvents C1TextBox4 As C1.Win.C1Input.C1TextBox
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents C1TextBox1 As C1.Win.C1Input.C1TextBox
    Friend WithEvents DateTimePicker2 As System.Windows.Forms.DateTimePicker
End Class
