﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{42C23254-BA1F-4222-895B-276FC06BF59C}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>BLLOld</RootNamespace>
    <AssemblyName>BLLOld</AssemblyName>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>x86</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="B_Bl.cs" />
    <Compile Include="B_Bl_Jf.cs" />
    <Compile Include="B_Bl_TWD1.cs" />
    <Compile Include="B_Bl_TWD2.cs" />
    <Compile Include="B_Emr_BasicElementList.cs" />
    <Compile Include="B_Emr_BasicElementTree.cs" />
    <Compile Include="B_Emr_BasicElementValue.cs" />
    <Compile Include="B_Emr_Bl.cs" />
    <Compile Include="B_Emr_BlZk1.cs" />
    <Compile Include="B_Emr_BlZk2.cs" />
    <Compile Include="B_Emr_DataField.cs" />
    <Compile Include="B_Emr_Mb.cs" />
    <Compile Include="B_Emr_Mblb.cs" />
    <Compile Include="B_Emr_SiKong.cs" />
    <Compile Include="B_Emr_ZhiKong1.cs" />
    <Compile Include="B_Emr_ZhiKong2.cs" />
    <Compile Include="B_Emr_ZkDj.cs" />
    <Compile Include="B_Jkk_Consume.cs" />
    <Compile Include="B_Jkk_Cz.cs" />
    <Compile Include="B_Materials_Check1.cs" />
    <Compile Include="B_Materials_Check2.cs" />
    <Compile Include="B_Materials_Dict.cs" />
    <Compile Include="B_Materials_Move1.cs" />
    <Compile Include="B_Materials_Move2.cs" />
    <Compile Include="B_Materials_Return1.cs" />
    <Compile Include="B_Materials_Return2.cs" />
    <Compile Include="B_Materials_Stock.cs" />
    <Compile Include="B_Materials_Sup_Dict.cs" />
    <Compile Include="B_Materials_Buy_In1.cs" />
    <Compile Include="B_Materials_Buy_In2.cs" />
    <Compile Include="B_Materials_Class_Dict.cs" />
    <Compile Include="B_Materials_InOut_Class_Dict.cs" />
    <Compile Include="B_Materials_Manufacturer_Dict.cs" />
    <Compile Include="B_Materials_Other_In1.cs" />
    <Compile Include="B_Materials_Other_In2.cs" />
    <Compile Include="B_Materials_Other_Out1.cs" />
    <Compile Include="B_Materials_Other_Out2.cs" />
    <Compile Include="B_Materials_Use_Out1.cs" />
    <Compile Include="B_Materials_Use_Out2.cs" />
    <Compile Include="B_Materials_Warehouse_Dict.cs" />
    <Compile Include="B_Zd_Msg2.cs" />
    <Compile Include="B_Zd_YyBq1.cs" />
    <Compile Include="B_Zd_YyJsr.cs" />
    <Compile Include="B_Zd_YyKs.cs" />
    <Compile Include="B_Zd_YyYs.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Common\Common.csproj">
      <Project>{92E350A0-3691-4B8D-A07E-EBB0F10E6997}</Project>
      <Name>Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\DAL\DAL.csproj">
      <Project>{C0DAB999-F761-4901-BE5B-C542365756A6}</Project>
      <Name>DAL</Name>
    </ProjectReference>
    <ProjectReference Include="..\ModelOld\ModelOld.csproj">
      <Project>{1FAF80FE-D42E-4FEB-853A-B9846C1862AE}</Project>
      <Name>ModelOld</Name>
    </ProjectReference>
    <ProjectReference Include="..\Model\Model.csproj">
      <Project>{3fb6ea13-2c32-4d08-a426-c22224f72121}</Project>
      <Name>Model</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>