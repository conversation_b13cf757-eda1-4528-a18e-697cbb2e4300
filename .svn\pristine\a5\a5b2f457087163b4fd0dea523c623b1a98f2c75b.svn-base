﻿/**  版本信息模板在安装目录下，可自行修改。
* D_Emr_ZhiKong2.cs
*
* 功 能： N/A
* 类 名： D_Emr_ZhiKong2
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/8/20 12:18:11   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
namespace DAL
{
	/// <summary>
	/// 数据访问类:D_Emr_ZhiKong2
	/// </summary>
	public partial class D_Emr_ZhiKong2
	{
		public D_Emr_ZhiKong2()
		{}
		#region  BasicMethod

		/// <summary>
		/// 得到最大ID
		/// </summary>
		public int GetMaxId()
		{
		return HisVar.HisVar.Sqldal.GetMaxID("id", "Emr_ZhiKong2"); 
		}

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(int id)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from Emr_ZhiKong2");
			strSql.Append(" where id=@id ");
			SqlParameter[] parameters = {
					new SqlParameter("@id", SqlDbType.Int,4)			};
			parameters[0].Value = id;

			return HisVar.HisVar.Sqldal.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ModelOld.M_Emr_ZhiKong2 model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into Emr_ZhiKong2(");
			strSql.Append("id,Zk_Code,Mx_Name,Kf_Pz,Kf_PzNr,Zk_Kf,Memo)");
			strSql.Append(" values (");
			strSql.Append("@id,@Zk_Code,@Mx_Name,@Kf_Pz,@Kf_PzNr,@Zk_Kf,@Memo)");
			SqlParameter[] parameters = {
					new SqlParameter("@id", SqlDbType.Int,4),
					new SqlParameter("@Zk_Code", SqlDbType.Char,10),
					new SqlParameter("@Mx_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Kf_Pz", SqlDbType.Char,1),
					new SqlParameter("@Kf_PzNr", SqlDbType.VarChar,50),
					new SqlParameter("@Zk_Kf", SqlDbType.Decimal,9),
					new SqlParameter("@Memo", SqlDbType.VarChar,50)};
           // string id=HisVar.HisVar.Sqldal.GetSingle("select max(id) from Emr_ZhiKong2").ToString();
            //if ( id== "")
            //{
            //    parameters[0].Value = 0;
            //}
            //else
            //{
            //    parameters[0].Value = int.Parse(id)+1;
            //}
            parameters[0].Value = model.id;
			parameters[1].Value = model.Zk_Code;
			parameters[2].Value = model.Mx_Name;
			parameters[3].Value = model.Kf_Pz;
			parameters[4].Value = model.Kf_PzNr;
            parameters[5].Value = Common.Tools.IsValueNull(model.Zk_Kf);
			parameters[6].Value = model.Memo;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ModelOld.M_Emr_ZhiKong2 model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update Emr_ZhiKong2 set ");
			strSql.Append("Zk_Code=@Zk_Code,");
			strSql.Append("Mx_Name=@Mx_Name,");
			strSql.Append("Kf_Pz=@Kf_Pz,");
			strSql.Append("Kf_PzNr=@Kf_PzNr,");
			strSql.Append("Zk_Kf=@Zk_Kf,");
			strSql.Append("Memo=@Memo");
			strSql.Append(" where id=@id ");
			SqlParameter[] parameters = {
					new SqlParameter("@Zk_Code", SqlDbType.Char,10),
					new SqlParameter("@Mx_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Kf_Pz", SqlDbType.Char,1),
					new SqlParameter("@Kf_PzNr", SqlDbType.VarChar,50),
					new SqlParameter("@Zk_Kf", SqlDbType.Decimal,9),
					new SqlParameter("@Memo", SqlDbType.VarChar,50),
					new SqlParameter("@id", SqlDbType.Int,4)};
			parameters[0].Value = model.Zk_Code;
			parameters[1].Value = model.Mx_Name;
			parameters[2].Value = model.Kf_Pz;
			parameters[3].Value = model.Kf_PzNr;
			parameters[4].Value = Common.Tools.IsValueNull (model.Zk_Kf);
			parameters[5].Value = model.Memo;
			parameters[6].Value = model.id;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(int id)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Emr_ZhiKong2 ");
			strSql.Append(" where id=@id ");
			SqlParameter[] parameters = {
					new SqlParameter("@id", SqlDbType.Int,4)			};
			parameters[0].Value = id;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(string Zk_Code)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from Emr_ZhiKong2 ");
            strSql.Append(" where Zk_Code=@Zk_Code ");
            SqlParameter[] parameters = {
					new SqlParameter("@Zk_Code", SqlDbType.Char ,10)			};
            parameters[0].Value = Zk_Code;

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string idlist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Emr_ZhiKong2 ");
			strSql.Append(" where id in ("+idlist + ")  ");
			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Emr_ZhiKong2 GetModel(int id)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 id,Zk_Code,Mx_Name,Kf_Pz,Kf_PzNr,Zk_Kf,Memo from Emr_ZhiKong2 ");
			strSql.Append(" where id=@id ");
			SqlParameter[] parameters = {
					new SqlParameter("@id", SqlDbType.Int,4)			};
			parameters[0].Value = id;

			ModelOld.M_Emr_ZhiKong2 model=new ModelOld.M_Emr_ZhiKong2();
			DataSet ds=HisVar.HisVar.Sqldal.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Emr_ZhiKong2 DataRowToModel(DataRow row)
		{
			ModelOld.M_Emr_ZhiKong2 model=new ModelOld.M_Emr_ZhiKong2();
			if (row != null)
			{
				if(row["id"]!=null && row["id"].ToString()!="")
				{
					model.id=int.Parse(row["id"].ToString());
				}
				if(row["Zk_Code"]!=null)
				{
					model.Zk_Code=row["Zk_Code"].ToString();
				}
				if(row["Mx_Name"]!=null)
				{
					model.Mx_Name=row["Mx_Name"].ToString();
				}
				if(row["Kf_Pz"]!=null)
				{
					model.Kf_Pz=row["Kf_Pz"].ToString();
				}
				if(row["Kf_PzNr"]!=null)
				{
					model.Kf_PzNr=row["Kf_PzNr"].ToString();
				}
				if(row["Zk_Kf"]!=null && row["Zk_Kf"].ToString()!="")
				{
					model.Zk_Kf=decimal.Parse(row["Zk_Kf"].ToString());
				}
				if(row["Memo"]!=null)
				{
					model.Memo=row["Memo"].ToString();
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
            strSql.Append("select id,Zk_Code,Mx_Name,Kf_Pz,Kf_PzNr,Zk_Kf,Memo,ZkDj_Code ");
            strSql.Append(" FROM Emr_ZhiKong2 left join Emr_ZkDj on Kf_PzNr=ZkDj_Name ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" id,Zk_Code,Mx_Name,Kf_Pz,Kf_PzNr,Zk_Kf,Memo ");
			strSql.Append(" FROM Emr_ZhiKong2 ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM Emr_ZhiKong2 ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
            object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.id desc");
			}
			strSql.Append(")AS Row, T.*  from Emr_ZhiKong2 T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Emr_ZhiKong2";
			parameters[1].Value = "id";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return HisVar.HisVar.Sqldal.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

