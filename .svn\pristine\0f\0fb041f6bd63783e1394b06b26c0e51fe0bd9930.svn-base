﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{1FAF80FE-D42E-4FEB-853A-B9846C1862AE}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>ModelOld</RootNamespace>
    <AssemblyName>ModelOld</AssemblyName>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="M_Bl.cs" />
    <Compile Include="M_Bl_Jf.cs" />
    <Compile Include="M_Bl_TWD1.cs" />
    <Compile Include="M_Bl_TWD2.cs" />
    <Compile Include="M_Emr_BasicElementList.cs" />
    <Compile Include="M_Emr_BasicElementTree.cs" />
    <Compile Include="M_Emr_BasicElementValue.cs" />
    <Compile Include="M_Emr_Bl.cs" />
    <Compile Include="M_Emr_BlZk1.cs" />
    <Compile Include="M_Emr_BlZk2.cs" />
    <Compile Include="M_Emr_DataField.cs" />
    <Compile Include="M_Emr_GDlog.cs" />
    <Compile Include="M_Emr_Mb.cs" />
    <Compile Include="M_Emr_Mblb.cs" />
    <Compile Include="M_Emr_SiKong.cs" />
    <Compile Include="M_Emr_ZhiKong1.cs" />
    <Compile Include="M_Emr_ZhiKong2.cs" />
    <Compile Include="M_Emr_ZkDj.cs" />
    <Compile Include="M_Jkk_Consume.cs" />
    <Compile Include="M_Jkk_Cz.cs" />
    <Compile Include="M_LIS_DevPara.cs" />
    <Compile Include="M_LIS_DictDev.cs" />
    <Compile Include="M_LIS_Element1.cs" />
    <Compile Include="M_LIS_Element2.cs" />
    <Compile Include="M_LIS_Test1.cs" />
    <Compile Include="M_LIS_TestItem.cs" />
    <Compile Include="M_LIS_TestXm.cs" />
    <Compile Include="M_LIS_TestXmDy.cs" />
    <Compile Include="M_Materials_Buy_In1.cs" />
    <Compile Include="M_Materials_Buy_In2.cs" />
    <Compile Include="M_Materials_Buy_In2WriteOff.cs" />
    <Compile Include="M_Materials_Check1.cs" />
    <Compile Include="M_Materials_Check2.cs" />
    <Compile Include="M_Materials_Class_Dict.cs" />
    <Compile Include="M_Materials_Dict.cs" />
    <Compile Include="M_Materials_InOut_Class_Dict.cs" />
    <Compile Include="M_Materials_Manufacturer_Dict.cs" />
    <Compile Include="M_Materials_Move1.cs" />
    <Compile Include="M_Materials_Move2.cs" />
    <Compile Include="M_Materials_Other_In1.cs" />
    <Compile Include="M_Materials_Other_In2.cs" />
    <Compile Include="M_Materials_Other_Out1.cs" />
    <Compile Include="M_Materials_Other_Out2.cs" />
    <Compile Include="M_Materials_Other_Out2WriteOff.cs" />
    <Compile Include="M_Materials_Return1.cs" />
    <Compile Include="M_Materials_Return2.cs" />
    <Compile Include="M_Materials_Stock.cs" />
    <Compile Include="M_Materials_StockCheck.cs" />
    <Compile Include="M_Materials_Sup_Dict.cs" />
    <Compile Include="M_Materials_Use_Out1.cs" />
    <Compile Include="M_Materials_Use_Out2.cs" />
    <Compile Include="M_Materials_Warehouse_Dict.cs" />
    <Compile Include="M_PatientInfo.cs" />
    <Compile Include="M_Zd_Msg2.cs" />
    <Compile Include="M_Zd_YyBq1.cs" />
    <Compile Include="M_Zd_YyJsr.cs" />
    <Compile Include="M_Zd_YyKs.cs" />
    <Compile Include="M_Zd_YyYs.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="M_Materials_Return2WriteOff.cs" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>