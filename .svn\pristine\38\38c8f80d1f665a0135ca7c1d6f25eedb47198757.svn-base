<?xml version="1.0" encoding="utf-8"?>
<DiscoveryClientResultsFile xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <Results>
    <DiscoveryClientResult referenceType="System.Web.Services.Discovery.DiscoveryDocumentReference" url="http://127.0.0.1/His_DataBase/DotNetService.asmx?disco" filename="DotNetService.disco" />
    <DiscoveryClientResult referenceType="System.Web.Services.Discovery.ContractReference" url="http://127.0.0.1/His_DataBase/DotNetService.asmx?wsdl" filename="DotNetService.wsdl" />
  </Results>
</DiscoveryClientResultsFile>