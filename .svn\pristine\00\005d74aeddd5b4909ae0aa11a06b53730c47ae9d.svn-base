﻿'------------------------------------------------------------------------------
' <auto-generated>
'     此代码由工具生成。
'     运行时版本:4.0.30319.42000
'
'     对此文件的更改可能会导致不正确的行为，并且如果
'     重新生成代码，这些更改将会丢失。
' </auto-generated>
'------------------------------------------------------------------------------

Option Strict Off
Option Explicit On

Imports System
Imports System.ComponentModel
Imports System.Data
Imports System.Diagnostics
Imports System.Web.Services
Imports System.Web.Services.Protocols
Imports System.Xml.Serialization

'
'此源代码是由 Microsoft.VSDesigner 4.0.30319.42000 版自动生成。
'
Namespace YpNetCg
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code"),  _
     System.Web.Services.WebServiceBindingAttribute(Name:="WebServiceSoap", [Namespace]:="http://tempuri.org/")>  _
    Partial Public Class WebService
        Inherits System.Web.Services.Protocols.SoapHttpClientProtocol
        
        Private To_CgdOperationCompleted As System.Threading.SendOrPostCallback
        
        Private To_PsdOperationCompleted As System.Threading.SendOrPostCallback
        
        Private To_RkdOperationCompleted As System.Threading.SendOrPostCallback
        
        Private GetCg_ZCgjOperationCompleted As System.Threading.SendOrPostCallback
        
        Private DB_Cg_NameOperationCompleted As System.Threading.SendOrPostCallback
        
        Private GetCgdNameOperationCompleted As System.Threading.SendOrPostCallback
        
        Private getPs_DateOperationCompleted As System.Threading.SendOrPostCallback
        
        Private getCg_DateOperationCompleted As System.Threading.SendOrPostCallback
        
        Private getRk_DateOperationCompleted As System.Threading.SendOrPostCallback
        
        Private GetHISRk_DateOperationCompleted As System.Threading.SendOrPostCallback
        
        Private HIs_BackOperationCompleted As System.Threading.SendOrPostCallback
        
        Private useDefaultCredentialsSetExplicitly As Boolean
        
        '''<remarks/>
        Public Sub New()
            MyBase.New
            Me.Url = Global.HisVar.My.MySettings.Default.HisVar_YpNetCg_WebService
            If (Me.IsLocalFileSystemWebService(Me.Url) = true) Then
                Me.UseDefaultCredentials = true
                Me.useDefaultCredentialsSetExplicitly = false
            Else
                Me.useDefaultCredentialsSetExplicitly = true
            End If
        End Sub
        
        Public Shadows Property Url() As String
            Get
                Return MyBase.Url
            End Get
            Set
                If (((Me.IsLocalFileSystemWebService(MyBase.Url) = true)  _
                            AndAlso (Me.useDefaultCredentialsSetExplicitly = false))  _
                            AndAlso (Me.IsLocalFileSystemWebService(value) = false)) Then
                    MyBase.UseDefaultCredentials = false
                End If
                MyBase.Url = value
            End Set
        End Property
        
        Public Shadows Property UseDefaultCredentials() As Boolean
            Get
                Return MyBase.UseDefaultCredentials
            End Get
            Set
                MyBase.UseDefaultCredentials = value
                Me.useDefaultCredentialsSetExplicitly = true
            End Set
        End Property
        
        '''<remarks/>
        Public Event To_CgdCompleted As To_CgdCompletedEventHandler
        
        '''<remarks/>
        Public Event To_PsdCompleted As To_PsdCompletedEventHandler
        
        '''<remarks/>
        Public Event To_RkdCompleted As To_RkdCompletedEventHandler
        
        '''<remarks/>
        Public Event GetCg_ZCgjCompleted As GetCg_ZCgjCompletedEventHandler
        
        '''<remarks/>
        Public Event DB_Cg_NameCompleted As DB_Cg_NameCompletedEventHandler
        
        '''<remarks/>
        Public Event GetCgdNameCompleted As GetCgdNameCompletedEventHandler
        
        '''<remarks/>
        Public Event getPs_DateCompleted As getPs_DateCompletedEventHandler
        
        '''<remarks/>
        Public Event getCg_DateCompleted As getCg_DateCompletedEventHandler
        
        '''<remarks/>
        Public Event getRk_DateCompleted As getRk_DateCompletedEventHandler
        
        '''<remarks/>
        Public Event GetHISRk_DateCompleted As GetHISRk_DateCompletedEventHandler
        
        '''<remarks/>
        Public Event HIs_BackCompleted As HIs_BackCompletedEventHandler
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/To_Cgd", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function To_Cgd(ByVal Cg_Name As String, ByVal Lb As String) As String
            Dim results() As Object = Me.Invoke("To_Cgd", New Object() {Cg_Name, Lb})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub To_CgdAsync(ByVal Cg_Name As String, ByVal Lb As String)
            Me.To_CgdAsync(Cg_Name, Lb, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub To_CgdAsync(ByVal Cg_Name As String, ByVal Lb As String, ByVal userState As Object)
            If (Me.To_CgdOperationCompleted Is Nothing) Then
                Me.To_CgdOperationCompleted = AddressOf Me.OnTo_CgdOperationCompleted
            End If
            Me.InvokeAsync("To_Cgd", New Object() {Cg_Name, Lb}, Me.To_CgdOperationCompleted, userState)
        End Sub
        
        Private Sub OnTo_CgdOperationCompleted(ByVal arg As Object)
            If (Not (Me.To_CgdCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent To_CgdCompleted(Me, New To_CgdCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/To_Psd", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function To_Psd(ByVal Cg_Name As String, ByVal Lsh As String, ByVal Cg_Ph As String) As String
            Dim results() As Object = Me.Invoke("To_Psd", New Object() {Cg_Name, Lsh, Cg_Ph})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub To_PsdAsync(ByVal Cg_Name As String, ByVal Lsh As String, ByVal Cg_Ph As String)
            Me.To_PsdAsync(Cg_Name, Lsh, Cg_Ph, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub To_PsdAsync(ByVal Cg_Name As String, ByVal Lsh As String, ByVal Cg_Ph As String, ByVal userState As Object)
            If (Me.To_PsdOperationCompleted Is Nothing) Then
                Me.To_PsdOperationCompleted = AddressOf Me.OnTo_PsdOperationCompleted
            End If
            Me.InvokeAsync("To_Psd", New Object() {Cg_Name, Lsh, Cg_Ph}, Me.To_PsdOperationCompleted, userState)
        End Sub
        
        Private Sub OnTo_PsdOperationCompleted(ByVal arg As Object)
            If (Not (Me.To_PsdCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent To_PsdCompleted(Me, New To_PsdCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/To_Rkd", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function To_Rkd(ByVal Cg_Name As String, ByVal Lsh As String, ByVal Cg_Ph As String, ByVal Cg_Dhsl As String) As String
            Dim results() As Object = Me.Invoke("To_Rkd", New Object() {Cg_Name, Lsh, Cg_Ph, Cg_Dhsl})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub To_RkdAsync(ByVal Cg_Name As String, ByVal Lsh As String, ByVal Cg_Ph As String, ByVal Cg_Dhsl As String)
            Me.To_RkdAsync(Cg_Name, Lsh, Cg_Ph, Cg_Dhsl, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub To_RkdAsync(ByVal Cg_Name As String, ByVal Lsh As String, ByVal Cg_Ph As String, ByVal Cg_Dhsl As String, ByVal userState As Object)
            If (Me.To_RkdOperationCompleted Is Nothing) Then
                Me.To_RkdOperationCompleted = AddressOf Me.OnTo_RkdOperationCompleted
            End If
            Me.InvokeAsync("To_Rkd", New Object() {Cg_Name, Lsh, Cg_Ph, Cg_Dhsl}, Me.To_RkdOperationCompleted, userState)
        End Sub
        
        Private Sub OnTo_RkdOperationCompleted(ByVal arg As Object)
            If (Not (Me.To_RkdCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent To_RkdCompleted(Me, New To_RkdCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/GetCg_ZCgj", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function GetCg_ZCgj(ByVal Cg_Name As String, ByVal Lb As String) As String
            Dim results() As Object = Me.Invoke("GetCg_ZCgj", New Object() {Cg_Name, Lb})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub GetCg_ZCgjAsync(ByVal Cg_Name As String, ByVal Lb As String)
            Me.GetCg_ZCgjAsync(Cg_Name, Lb, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub GetCg_ZCgjAsync(ByVal Cg_Name As String, ByVal Lb As String, ByVal userState As Object)
            If (Me.GetCg_ZCgjOperationCompleted Is Nothing) Then
                Me.GetCg_ZCgjOperationCompleted = AddressOf Me.OnGetCg_ZCgjOperationCompleted
            End If
            Me.InvokeAsync("GetCg_ZCgj", New Object() {Cg_Name, Lb}, Me.GetCg_ZCgjOperationCompleted, userState)
        End Sub
        
        Private Sub OnGetCg_ZCgjOperationCompleted(ByVal arg As Object)
            If (Not (Me.GetCg_ZCgjCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent GetCg_ZCgjCompleted(Me, New GetCg_ZCgjCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/DB_Cg_Name", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function DB_Cg_Name(ByVal Sw_Cg_Name As String, ByVal Ts_Cg_Name As String, ByVal Lb As String) As String
            Dim results() As Object = Me.Invoke("DB_Cg_Name", New Object() {Sw_Cg_Name, Ts_Cg_Name, Lb})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub DB_Cg_NameAsync(ByVal Sw_Cg_Name As String, ByVal Ts_Cg_Name As String, ByVal Lb As String)
            Me.DB_Cg_NameAsync(Sw_Cg_Name, Ts_Cg_Name, Lb, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub DB_Cg_NameAsync(ByVal Sw_Cg_Name As String, ByVal Ts_Cg_Name As String, ByVal Lb As String, ByVal userState As Object)
            If (Me.DB_Cg_NameOperationCompleted Is Nothing) Then
                Me.DB_Cg_NameOperationCompleted = AddressOf Me.OnDB_Cg_NameOperationCompleted
            End If
            Me.InvokeAsync("DB_Cg_Name", New Object() {Sw_Cg_Name, Ts_Cg_Name, Lb}, Me.DB_Cg_NameOperationCompleted, userState)
        End Sub
        
        Private Sub OnDB_Cg_NameOperationCompleted(ByVal arg As Object)
            If (Not (Me.DB_Cg_NameCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent DB_Cg_NameCompleted(Me, New DB_Cg_NameCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/GetCgdName", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function GetCgdName(ByVal Cg_Name As String, ByVal lb As String) As String
            Dim results() As Object = Me.Invoke("GetCgdName", New Object() {Cg_Name, lb})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub GetCgdNameAsync(ByVal Cg_Name As String, ByVal lb As String)
            Me.GetCgdNameAsync(Cg_Name, lb, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub GetCgdNameAsync(ByVal Cg_Name As String, ByVal lb As String, ByVal userState As Object)
            If (Me.GetCgdNameOperationCompleted Is Nothing) Then
                Me.GetCgdNameOperationCompleted = AddressOf Me.OnGetCgdNameOperationCompleted
            End If
            Me.InvokeAsync("GetCgdName", New Object() {Cg_Name, lb}, Me.GetCgdNameOperationCompleted, userState)
        End Sub
        
        Private Sub OnGetCgdNameOperationCompleted(ByVal arg As Object)
            If (Not (Me.GetCgdNameCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent GetCgdNameCompleted(Me, New GetCgdNameCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/getPs_Date", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function getPs_Date(ByVal Qy_Name As String, ByVal date1 As Date, ByVal date2 As Date, ByVal Lb As String) As System.Data.DataTable
            Dim results() As Object = Me.Invoke("getPs_Date", New Object() {Qy_Name, date1, date2, Lb})
            Return CType(results(0),System.Data.DataTable)
        End Function
        
        '''<remarks/>
        Public Overloads Sub getPs_DateAsync(ByVal Qy_Name As String, ByVal date1 As Date, ByVal date2 As Date, ByVal Lb As String)
            Me.getPs_DateAsync(Qy_Name, date1, date2, Lb, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub getPs_DateAsync(ByVal Qy_Name As String, ByVal date1 As Date, ByVal date2 As Date, ByVal Lb As String, ByVal userState As Object)
            If (Me.getPs_DateOperationCompleted Is Nothing) Then
                Me.getPs_DateOperationCompleted = AddressOf Me.OngetPs_DateOperationCompleted
            End If
            Me.InvokeAsync("getPs_Date", New Object() {Qy_Name, date1, date2, Lb}, Me.getPs_DateOperationCompleted, userState)
        End Sub
        
        Private Sub OngetPs_DateOperationCompleted(ByVal arg As Object)
            If (Not (Me.getPs_DateCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent getPs_DateCompleted(Me, New getPs_DateCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/getCg_Date", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function getCg_Date(ByVal Wsj_Name As String, ByVal date1 As Date, ByVal date2 As Date, ByVal Lb As String) As System.Data.DataTable
            Dim results() As Object = Me.Invoke("getCg_Date", New Object() {Wsj_Name, date1, date2, Lb})
            Return CType(results(0),System.Data.DataTable)
        End Function
        
        '''<remarks/>
        Public Overloads Sub getCg_DateAsync(ByVal Wsj_Name As String, ByVal date1 As Date, ByVal date2 As Date, ByVal Lb As String)
            Me.getCg_DateAsync(Wsj_Name, date1, date2, Lb, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub getCg_DateAsync(ByVal Wsj_Name As String, ByVal date1 As Date, ByVal date2 As Date, ByVal Lb As String, ByVal userState As Object)
            If (Me.getCg_DateOperationCompleted Is Nothing) Then
                Me.getCg_DateOperationCompleted = AddressOf Me.OngetCg_DateOperationCompleted
            End If
            Me.InvokeAsync("getCg_Date", New Object() {Wsj_Name, date1, date2, Lb}, Me.getCg_DateOperationCompleted, userState)
        End Sub
        
        Private Sub OngetCg_DateOperationCompleted(ByVal arg As Object)
            If (Not (Me.getCg_DateCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent getCg_DateCompleted(Me, New getCg_DateCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/getRk_Date", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function getRk_Date(ByVal Wsj_Name As String, ByVal date1 As Date, ByVal date2 As Date, ByVal Lb As String) As System.Data.DataTable
            Dim results() As Object = Me.Invoke("getRk_Date", New Object() {Wsj_Name, date1, date2, Lb})
            Return CType(results(0),System.Data.DataTable)
        End Function
        
        '''<remarks/>
        Public Overloads Sub getRk_DateAsync(ByVal Wsj_Name As String, ByVal date1 As Date, ByVal date2 As Date, ByVal Lb As String)
            Me.getRk_DateAsync(Wsj_Name, date1, date2, Lb, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub getRk_DateAsync(ByVal Wsj_Name As String, ByVal date1 As Date, ByVal date2 As Date, ByVal Lb As String, ByVal userState As Object)
            If (Me.getRk_DateOperationCompleted Is Nothing) Then
                Me.getRk_DateOperationCompleted = AddressOf Me.OngetRk_DateOperationCompleted
            End If
            Me.InvokeAsync("getRk_Date", New Object() {Wsj_Name, date1, date2, Lb}, Me.getRk_DateOperationCompleted, userState)
        End Sub
        
        Private Sub OngetRk_DateOperationCompleted(ByVal arg As Object)
            If (Not (Me.getRk_DateCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent getRk_DateCompleted(Me, New getRk_DateCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/GetHISRk_Date", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function GetHISRk_Date(ByVal Nh_Bm As String) As System.Data.DataSet
            Dim results() As Object = Me.Invoke("GetHISRk_Date", New Object() {Nh_Bm})
            Return CType(results(0),System.Data.DataSet)
        End Function
        
        '''<remarks/>
        Public Overloads Sub GetHISRk_DateAsync(ByVal Nh_Bm As String)
            Me.GetHISRk_DateAsync(Nh_Bm, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub GetHISRk_DateAsync(ByVal Nh_Bm As String, ByVal userState As Object)
            If (Me.GetHISRk_DateOperationCompleted Is Nothing) Then
                Me.GetHISRk_DateOperationCompleted = AddressOf Me.OnGetHISRk_DateOperationCompleted
            End If
            Me.InvokeAsync("GetHISRk_Date", New Object() {Nh_Bm}, Me.GetHISRk_DateOperationCompleted, userState)
        End Sub
        
        Private Sub OnGetHISRk_DateOperationCompleted(ByVal arg As Object)
            If (Not (Me.GetHISRk_DateCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent GetHISRk_DateCompleted(Me, New GetHISRk_DateCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/HIs_Back", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function HIs_Back(ByVal Cg_MxCode As String) As String
            Dim results() As Object = Me.Invoke("HIs_Back", New Object() {Cg_MxCode})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub HIs_BackAsync(ByVal Cg_MxCode As String)
            Me.HIs_BackAsync(Cg_MxCode, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub HIs_BackAsync(ByVal Cg_MxCode As String, ByVal userState As Object)
            If (Me.HIs_BackOperationCompleted Is Nothing) Then
                Me.HIs_BackOperationCompleted = AddressOf Me.OnHIs_BackOperationCompleted
            End If
            Me.InvokeAsync("HIs_Back", New Object() {Cg_MxCode}, Me.HIs_BackOperationCompleted, userState)
        End Sub
        
        Private Sub OnHIs_BackOperationCompleted(ByVal arg As Object)
            If (Not (Me.HIs_BackCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent HIs_BackCompleted(Me, New HIs_BackCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        Public Shadows Sub CancelAsync(ByVal userState As Object)
            MyBase.CancelAsync(userState)
        End Sub
        
        Private Function IsLocalFileSystemWebService(ByVal url As String) As Boolean
            If ((url Is Nothing)  _
                        OrElse (url Is String.Empty)) Then
                Return false
            End If
            Dim wsUri As System.Uri = New System.Uri(url)
            If ((wsUri.Port >= 1024)  _
                        AndAlso (String.Compare(wsUri.Host, "localHost", System.StringComparison.OrdinalIgnoreCase) = 0)) Then
                Return true
            End If
            Return false
        End Function
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub To_CgdCompletedEventHandler(ByVal sender As Object, ByVal e As To_CgdCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class To_CgdCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub To_PsdCompletedEventHandler(ByVal sender As Object, ByVal e As To_PsdCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class To_PsdCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub To_RkdCompletedEventHandler(ByVal sender As Object, ByVal e As To_RkdCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class To_RkdCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub GetCg_ZCgjCompletedEventHandler(ByVal sender As Object, ByVal e As GetCg_ZCgjCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class GetCg_ZCgjCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub DB_Cg_NameCompletedEventHandler(ByVal sender As Object, ByVal e As DB_Cg_NameCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class DB_Cg_NameCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub GetCgdNameCompletedEventHandler(ByVal sender As Object, ByVal e As GetCgdNameCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class GetCgdNameCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub getPs_DateCompletedEventHandler(ByVal sender As Object, ByVal e As getPs_DateCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class getPs_DateCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As System.Data.DataTable
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),System.Data.DataTable)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub getCg_DateCompletedEventHandler(ByVal sender As Object, ByVal e As getCg_DateCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class getCg_DateCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As System.Data.DataTable
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),System.Data.DataTable)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub getRk_DateCompletedEventHandler(ByVal sender As Object, ByVal e As getRk_DateCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class getRk_DateCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As System.Data.DataTable
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),System.Data.DataTable)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub GetHISRk_DateCompletedEventHandler(ByVal sender As Object, ByVal e As GetHISRk_DateCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class GetHISRk_DateCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As System.Data.DataSet
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),System.Data.DataSet)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub HIs_BackCompletedEventHandler(ByVal sender As Object, ByVal e As HIs_BackCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class HIs_BackCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
End Namespace
