﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class Cq_Cf34
    Inherits HisControl.BaseChild

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(Cq_Cf34))
        Me.Control2_Link = New C1.Win.C1Command.C1CommandLink
        Me.Control2 = New C1.Win.C1Command.C1CommandControl
        Me.T_Label2 = New C1.Win.C1Input.C1Label
        Me.C1CommandHolder1 = New C1.Win.C1Command.C1CommandHolder
        Me.Move3 = New C1.Win.C1Command.C1Command
        Me.Move4 = New C1.Win.C1Command.C1Command
        Me.Move5 = New C1.Win.C1Command.C1Command
        Me.Move2 = New C1.Win.C1Command.C1Command
        Me.Move1 = New C1.Win.C1Command.C1Command
        Me.Control1 = New C1.Win.C1Command.C1CommandControl
        Me.T_Label1 = New C1.Win.C1Input.C1Label
        Me.Lr1 = New C1.Win.C1Command.C1Command
        Me.Lr2 = New C1.Win.C1Command.C1Command
        Me.Lr3 = New C1.Win.C1Command.C1Command
        Me.Lr_Control = New C1.Win.C1Command.C1CommandControl
        Me.Move_Link3 = New C1.Win.C1Command.C1CommandLink
        Me.Move_Link4 = New C1.Win.C1Command.C1CommandLink
        Me.Move5_Link = New C1.Win.C1Command.C1CommandLink
        Me.C1Combo1 = New C1.Win.C1List.C1Combo
        Me.Move_Link2 = New C1.Win.C1Command.C1CommandLink
        Me.Move_Link1 = New C1.Win.C1Command.C1CommandLink
        Me.Label01 = New System.Windows.Forms.Label
        Me.Control1_Link = New C1.Win.C1Command.C1CommandLink
        Me.T_Line6 = New System.Windows.Forms.Label
        Me.Comm1 = New System.Windows.Forms.Button
        Me.C1Numeric4 = New C1.Win.C1Input.C1NumericEdit
        Me.Label11 = New System.Windows.Forms.Label
        Me.ErrorProvider1 = New System.Windows.Forms.ErrorProvider(Me.components)
        Me.Label10 = New System.Windows.Forms.Label
        Me.C1Numeric1 = New C1.Win.C1Input.C1NumericEdit
        Me.C1Numeric2 = New C1.Win.C1Input.C1NumericEdit
        Me.Label13 = New System.Windows.Forms.Label
        Me.ToolTip1 = New System.Windows.Forms.ToolTip(Me.components)
        Me.Comm2 = New System.Windows.Forms.Button
        Me.Panel4 = New System.Windows.Forms.Panel
        Me.C1ToolBar2 = New C1.Win.C1Command.C1ToolBar
        Me.T_Line4 = New System.Windows.Forms.Label
        Me.Label1 = New System.Windows.Forms.Label
        Me.Label2 = New System.Windows.Forms.Label
        CType(Me.T_Label2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1CommandHolder1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T_Label1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1Combo1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1Numeric4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ErrorProvider1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1Numeric1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1Numeric2, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Panel4.SuspendLayout()
        Me.C1ToolBar2.SuspendLayout()
        Me.SuspendLayout()
        '
        'Control2_Link
        '
        Me.Control2_Link.Command = Me.Control2
        Me.Control2_Link.SortOrder = 3
        '
        'Control2
        '
        Me.Control2.Control = Me.T_Label2
        Me.Control2.HotFrame = C1.Win.C1Command.HotFrameEnum.None
        Me.Control2.Name = "Control2"
        Me.Control2.ShowShortcut = False
        Me.Control2.ShowTextAsToolTip = False
        '
        'T_Label2
        '
        Me.T_Label2.AutoSize = True
        Me.T_Label2.BackColor = System.Drawing.SystemColors.Control
        Me.T_Label2.ForeColor = System.Drawing.Color.Maroon
        Me.T_Label2.Location = New System.Drawing.Point(80, 5)
        Me.T_Label2.Name = "T_Label2"
        Me.T_Label2.Size = New System.Drawing.Size(23, 12)
        Me.T_Label2.TabIndex = 139
        Me.T_Label2.Tag = Nothing
        Me.T_Label2.Text = "(1)"
        Me.T_Label2.TextAlign = System.Drawing.ContentAlignment.BottomCenter
        Me.T_Label2.TextDetached = True
        Me.T_Label2.TrimStart = True
        '
        'C1CommandHolder1
        '
        Me.C1CommandHolder1.Commands.Add(Me.Control2)
        Me.C1CommandHolder1.Commands.Add(Me.Move3)
        Me.C1CommandHolder1.Commands.Add(Me.Move4)
        Me.C1CommandHolder1.Commands.Add(Me.Move5)
        Me.C1CommandHolder1.Commands.Add(Me.Move2)
        Me.C1CommandHolder1.Commands.Add(Me.Move1)
        Me.C1CommandHolder1.Commands.Add(Me.Control1)
        Me.C1CommandHolder1.Commands.Add(Me.Lr1)
        Me.C1CommandHolder1.Commands.Add(Me.Lr2)
        Me.C1CommandHolder1.Commands.Add(Me.Lr3)
        Me.C1CommandHolder1.Commands.Add(Me.Lr_Control)
        Me.C1CommandHolder1.Owner = Me
        '
        'Move3
        '
        Me.Move3.Image = CType(resources.GetObject("Move3.Image"), System.Drawing.Image)
        Me.Move3.Name = "Move3"
        Me.Move3.Shortcut = System.Windows.Forms.Shortcut.F4
        Me.Move3.Text = "下移"
        Me.Move3.ToolTipText = "下移记录"
        '
        'Move4
        '
        Me.Move4.Image = CType(resources.GetObject("Move4.Image"), System.Drawing.Image)
        Me.Move4.Name = "Move4"
        Me.Move4.Shortcut = System.Windows.Forms.Shortcut.F6
        '
        'Move5
        '
        Me.Move5.Image = CType(resources.GetObject("Move5.Image"), System.Drawing.Image)
        Me.Move5.Name = "Move5"
        Me.Move5.Shortcut = System.Windows.Forms.Shortcut.F6
        Me.Move5.Text = "新增"
        '
        'Move2
        '
        Me.Move2.Image = CType(resources.GetObject("Move2.Image"), System.Drawing.Image)
        Me.Move2.Name = "Move2"
        Me.Move2.Shortcut = System.Windows.Forms.Shortcut.F3
        Me.Move2.ShowShortcut = False
        Me.Move2.Text = "上移"
        Me.Move2.ToolTipText = "上移记录"
        '
        'Move1
        '
        Me.Move1.Image = CType(resources.GetObject("Move1.Image"), System.Drawing.Image)
        Me.Move1.Name = "Move1"
        Me.Move1.Shortcut = System.Windows.Forms.Shortcut.F2
        '
        'Control1
        '
        Me.Control1.Control = Me.T_Label1
        Me.Control1.HotFrame = C1.Win.C1Command.HotFrameEnum.None
        Me.Control1.Name = "Control1"
        Me.Control1.ShowShortcut = False
        Me.Control1.ShowTextAsToolTip = False
        Me.Control1.Text = "记录"
        '
        'T_Label1
        '
        Me.T_Label1.AutoSize = True
        Me.T_Label1.BackColor = System.Drawing.SystemColors.Control
        Me.T_Label1.ForeColor = System.Drawing.Color.Maroon
        Me.T_Label1.Location = New System.Drawing.Point(1, 5)
        Me.T_Label1.Name = "T_Label1"
        Me.T_Label1.Size = New System.Drawing.Size(29, 12)
        Me.T_Label1.TabIndex = 138
        Me.T_Label1.Tag = Nothing
        Me.T_Label1.Text = "记录"
        Me.T_Label1.TextAlign = System.Drawing.ContentAlignment.BottomLeft
        Me.T_Label1.TextDetached = True
        '
        'Lr1
        '
        Me.Lr1.Name = "Lr1"
        Me.Lr1.Pressed = True
        Me.Lr1.Shortcut = System.Windows.Forms.Shortcut.F9
        Me.Lr1.Text = "简称"
        '
        'Lr2
        '
        Me.Lr2.Name = "Lr2"
        Me.Lr2.Shortcut = System.Windows.Forms.Shortcut.F10
        Me.Lr2.Text = "全称"
        '
        'Lr3
        '
        Me.Lr3.Name = "Lr3"
        Me.Lr3.Shortcut = System.Windows.Forms.Shortcut.F11
        Me.Lr3.Text = "编码"
        '
        'Lr_Control
        '
        Me.Lr_Control.HotFrame = C1.Win.C1Command.HotFrameEnum.None
        Me.Lr_Control.Name = "Lr_Control"
        Me.Lr_Control.ShowShortcut = False
        Me.Lr_Control.ShowTextAsToolTip = False
        Me.Lr_Control.Text = "提示"
        '
        'Move_Link3
        '
        Me.Move_Link3.Command = Me.Move3
        Me.Move_Link3.SortOrder = 4
        '
        'Move_Link4
        '
        Me.Move_Link4.Command = Me.Move4
        Me.Move_Link4.SortOrder = 5
        Me.Move_Link4.Text = "New Command"
        '
        'Move5_Link
        '
        Me.Move5_Link.Command = Me.Move5
        Me.Move5_Link.Delimiter = True
        Me.Move5_Link.SortOrder = 6
        Me.Move5_Link.Text = "新增"
        Me.Move5_Link.ToolTipText = "新增记录"
        '
        'C1Combo1
        '
        Me.C1Combo1.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.C1Combo1.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1Combo1.Caption = ""
        Me.C1Combo1.CaptionHeight = 17
        Me.C1Combo1.CharacterCasing = System.Windows.Forms.CharacterCasing.Normal
        Me.C1Combo1.ColumnCaptionHeight = 18
        Me.C1Combo1.ColumnFooterHeight = 18
        Me.C1Combo1.ContentHeight = 16
        Me.C1Combo1.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.C1Combo1.EditorBackColor = System.Drawing.SystemColors.Window
        Me.C1Combo1.EditorFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.C1Combo1.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.C1Combo1.EditorHeight = 16
        Me.C1Combo1.Images.Add(CType(resources.GetObject("C1Combo1.Images"), System.Drawing.Image))
        Me.C1Combo1.ItemHeight = 15
        Me.C1Combo1.Location = New System.Drawing.Point(72, 71)
        Me.C1Combo1.MatchEntryTimeout = CType(2000, Long)
        Me.C1Combo1.MaxDropDownItems = CType(5, Short)
        Me.C1Combo1.MaxLength = 32767
        Me.C1Combo1.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.C1Combo1.Name = "C1Combo1"
        Me.C1Combo1.RowDivider.Color = System.Drawing.Color.DarkGray
        Me.C1Combo1.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.C1Combo1.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.C1Combo1.ScrollTips = True
        Me.C1Combo1.Size = New System.Drawing.Size(333, 16)
        Me.C1Combo1.SuperBack = True
        Me.C1Combo1.TabIndex = 218
        Me.C1Combo1.PropBag = resources.GetString("C1Combo1.PropBag")
        '
        'Move_Link2
        '
        Me.Move_Link2.Command = Me.Move2
        Me.Move_Link2.SortOrder = 2
        '
        'Move_Link1
        '
        Me.Move_Link1.Command = Me.Move1
        Me.Move_Link1.Delimiter = True
        Me.Move_Link1.SortOrder = 1
        Me.Move_Link1.Text = "New Command"
        '
        'Label01
        '
        Me.Label01.AutoSize = True
        Me.Label01.ForeColor = System.Drawing.Color.DarkRed
        Me.Label01.Location = New System.Drawing.Point(16, 73)
        Me.Label01.Name = "Label01"
        Me.Label01.Size = New System.Drawing.Size(53, 12)
        Me.Label01.TabIndex = 228
        Me.Label01.Text = "项目简称"
        '
        'Control1_Link
        '
        Me.Control1_Link.Command = Me.Control1
        '
        'T_Line6
        '
        Me.T_Line6.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.T_Line6.Location = New System.Drawing.Point(190, 3)
        Me.T_Line6.Name = "T_Line6"
        Me.T_Line6.Size = New System.Drawing.Size(2, 30)
        Me.T_Line6.TabIndex = 0
        '
        'Comm1
        '
        Me.Comm1.Location = New System.Drawing.Point(207, 4)
        Me.Comm1.Name = "Comm1"
        Me.Comm1.Size = New System.Drawing.Size(50, 24)
        Me.Comm1.TabIndex = 0
        Me.Comm1.Tag = "保存"
        Me.Comm1.Text = "确认"
        Me.Comm1.UseVisualStyleBackColor = False
        '
        'C1Numeric4
        '
        Me.C1Numeric4.AutoSize = False
        Me.C1Numeric4.BackColor = System.Drawing.SystemColors.Info
        Me.C1Numeric4.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1Numeric4.Location = New System.Drawing.Point(71, 155)
        Me.C1Numeric4.Name = "C1Numeric4"
        Me.C1Numeric4.ReadOnly = True
        Me.C1Numeric4.Size = New System.Drawing.Size(106, 16)
        Me.C1Numeric4.TabIndex = 221
        Me.C1Numeric4.TabStop = False
        Me.C1Numeric4.Tag = Nothing
        Me.C1Numeric4.TextAlign = System.Windows.Forms.HorizontalAlignment.Right
        Me.C1Numeric4.Value = New Decimal(New Integer() {0, 0, 0, 0})
        Me.C1Numeric4.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.C1Numeric4.VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.None
        '
        'Label11
        '
        Me.Label11.AutoSize = True
        Me.Label11.Location = New System.Drawing.Point(16, 131)
        Me.Label11.Name = "Label11"
        Me.Label11.Size = New System.Drawing.Size(53, 12)
        Me.Label11.TabIndex = 226
        Me.Label11.Text = "项目单价"
        '
        'ErrorProvider1
        '
        Me.ErrorProvider1.ContainerControl = Me
        '
        'Label10
        '
        Me.Label10.AutoSize = True
        Me.Label10.Location = New System.Drawing.Point(15, 103)
        Me.Label10.Name = "Label10"
        Me.Label10.Size = New System.Drawing.Size(53, 12)
        Me.Label10.TabIndex = 225
        Me.Label10.Text = "使用数量"
        '
        'C1Numeric1
        '
        Me.C1Numeric1.AutoSize = False
        Me.C1Numeric1.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1Numeric1.Location = New System.Drawing.Point(71, 101)
        Me.C1Numeric1.Name = "C1Numeric1"
        Me.C1Numeric1.Size = New System.Drawing.Size(106, 16)
        Me.C1Numeric1.TabIndex = 219
        Me.C1Numeric1.Tag = "数量"
        Me.C1Numeric1.TextAlign = System.Windows.Forms.HorizontalAlignment.Right
        Me.C1Numeric1.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.C1Numeric1.VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.None
        '
        'C1Numeric2
        '
        Me.C1Numeric2.AutoSize = False
        Me.C1Numeric2.BackColor = System.Drawing.SystemColors.Info
        Me.C1Numeric2.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1Numeric2.Location = New System.Drawing.Point(71, 129)
        Me.C1Numeric2.Name = "C1Numeric2"
        Me.C1Numeric2.ReadOnly = True
        Me.C1Numeric2.Size = New System.Drawing.Size(106, 16)
        Me.C1Numeric2.TabIndex = 220
        Me.C1Numeric2.TabStop = False
        Me.C1Numeric2.Tag = "单价"
        Me.C1Numeric2.TextAlign = System.Windows.Forms.HorizontalAlignment.Right
        Me.C1Numeric2.Value = New Decimal(New Integer() {0, 0, 0, 0})
        Me.C1Numeric2.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.C1Numeric2.VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.None
        '
        'Label13
        '
        Me.Label13.AutoSize = True
        Me.Label13.Location = New System.Drawing.Point(15, 157)
        Me.Label13.Name = "Label13"
        Me.Label13.Size = New System.Drawing.Size(53, 12)
        Me.Label13.TabIndex = 227
        Me.Label13.Text = "金    额"
        '
        'Comm2
        '
        Me.Comm2.DialogResult = DialogResult.Cancel
        Me.Comm2.Location = New System.Drawing.Point(259, 4)
        Me.Comm2.Name = "Comm2"
        Me.Comm2.Size = New System.Drawing.Size(50, 24)
        Me.Comm2.TabIndex = 1
        Me.Comm2.Tag = "取消"
        Me.Comm2.Text = "取消"
        Me.ToolTip1.SetToolTip(Me.Comm2, "取消存盘并退出(&C)")
        Me.Comm2.UseVisualStyleBackColor = False
        '
        'Panel4
        '
        Me.Panel4.Controls.Add(Me.Comm2)
        Me.Panel4.Controls.Add(Me.C1ToolBar2)
        Me.Panel4.Controls.Add(Me.T_Line6)
        Me.Panel4.Controls.Add(Me.Comm1)
        Me.Panel4.Controls.Add(Me.T_Line4)
        Me.Panel4.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.Panel4.Location = New System.Drawing.Point(0, 228)
        Me.Panel4.Name = "Panel4"
        Me.Panel4.Size = New System.Drawing.Size(421, 30)
        Me.Panel4.TabIndex = 223
        '
        'C1ToolBar2
        '
        Me.C1ToolBar2.AccessibleName = "Tool Bar"
        Me.C1ToolBar2.CommandHolder = Nothing
        Me.C1ToolBar2.CommandLinks.AddRange(New C1.Win.C1Command.C1CommandLink() {Me.Control1_Link, Me.Move_Link1, Me.Move_Link2, Me.Control2_Link, Me.Move_Link3, Me.Move_Link4, Me.Move5_Link})
        Me.C1ToolBar2.Controls.Add(Me.T_Label2)
        Me.C1ToolBar2.Controls.Add(Me.T_Label1)
        Me.C1ToolBar2.Location = New System.Drawing.Point(4, 6)
        Me.C1ToolBar2.MinButtonSize = 22
        Me.C1ToolBar2.Movable = False
        Me.C1ToolBar2.Name = "C1ToolBar2"
        Me.C1ToolBar2.Size = New System.Drawing.Size(174, 22)
        Me.C1ToolBar2.Text = "C1ToolBar2"
        Me.C1ToolBar2.VisualStyle = C1.Win.C1Command.VisualStyle.Classic
        Me.C1ToolBar2.VisualStyleBase = C1.Win.C1Command.VisualStyle.Classic
        '
        'T_Line4
        '
        Me.T_Line4.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.T_Line4.Dock = System.Windows.Forms.DockStyle.Top
        Me.T_Line4.Location = New System.Drawing.Point(0, 0)
        Me.T_Line4.Name = "T_Line4"
        Me.T_Line4.Size = New System.Drawing.Size(421, 2)
        Me.T_Line4.TabIndex = 134
        Me.T_Line4.Text = "Label1"
        '
        'Label1
        '
        Me.Label1.BackColor = System.Drawing.SystemColors.Info
        Me.Label1.Location = New System.Drawing.Point(247, 102)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(158, 16)
        Me.Label1.TabIndex = 232
        Me.Label1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.Location = New System.Drawing.Point(188, 104)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(53, 12)
        Me.Label2.TabIndex = 233
        Me.Label2.Text = "类别名称"
        '
        'Cq_Cf34
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(421, 258)
        Me.Controls.Add(Me.Label2)
        Me.Controls.Add(Me.Label1)
        Me.Controls.Add(Me.C1Combo1)
        Me.Controls.Add(Me.Label01)
        Me.Controls.Add(Me.C1Numeric4)
        Me.Controls.Add(Me.Label11)
        Me.Controls.Add(Me.Label10)
        Me.Controls.Add(Me.C1Numeric1)
        Me.Controls.Add(Me.C1Numeric2)
        Me.Controls.Add(Me.Label13)
        Me.Controls.Add(Me.Panel4)
        Me.Icon = CType(resources.GetObject("$this.Icon"), System.Drawing.Icon)
        Me.Name = "Cq_Cf34"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "长期处方明细"
        CType(Me.T_Label2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1CommandHolder1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T_Label1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1Combo1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1Numeric4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ErrorProvider1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1Numeric1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1Numeric2, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Panel4.ResumeLayout(False)
        Me.C1ToolBar2.ResumeLayout(False)
        Me.C1ToolBar2.PerformLayout()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents Control2_Link As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Control2 As C1.Win.C1Command.C1CommandControl
    Friend WithEvents T_Label2 As C1.Win.C1Input.C1Label
    Friend WithEvents C1CommandHolder1 As C1.Win.C1Command.C1CommandHolder
    Friend WithEvents Move3 As C1.Win.C1Command.C1Command
    Friend WithEvents Move4 As C1.Win.C1Command.C1Command
    Friend WithEvents Move5 As C1.Win.C1Command.C1Command
    Friend WithEvents Move2 As C1.Win.C1Command.C1Command
    Friend WithEvents Move1 As C1.Win.C1Command.C1Command
    Friend WithEvents Control1 As C1.Win.C1Command.C1CommandControl
    Friend WithEvents T_Label1 As C1.Win.C1Input.C1Label
    Friend WithEvents Lr1 As C1.Win.C1Command.C1Command
    Friend WithEvents Lr2 As C1.Win.C1Command.C1Command
    Friend WithEvents Lr3 As C1.Win.C1Command.C1Command
    Friend WithEvents Lr_Control As C1.Win.C1Command.C1CommandControl
    Friend WithEvents C1Combo1 As C1.Win.C1List.C1Combo
    Friend WithEvents Label01 As System.Windows.Forms.Label
    Friend WithEvents C1Numeric4 As C1.Win.C1Input.C1NumericEdit
    Friend WithEvents Label11 As System.Windows.Forms.Label
    Friend WithEvents Label10 As System.Windows.Forms.Label
    Friend WithEvents C1Numeric1 As C1.Win.C1Input.C1NumericEdit
    Friend WithEvents C1Numeric2 As C1.Win.C1Input.C1NumericEdit
    Friend WithEvents Label13 As System.Windows.Forms.Label
    Friend WithEvents Panel4 As System.Windows.Forms.Panel
    Friend WithEvents Comm2 As System.Windows.Forms.Button
    Friend WithEvents ToolTip1 As System.Windows.Forms.ToolTip
    Friend WithEvents C1ToolBar2 As C1.Win.C1Command.C1ToolBar
    Friend WithEvents Control1_Link As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Move_Link1 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Move_Link2 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Move_Link3 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Move_Link4 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Move5_Link As C1.Win.C1Command.C1CommandLink
    Friend WithEvents T_Line6 As System.Windows.Forms.Label
    Friend WithEvents Comm1 As System.Windows.Forms.Button
    Friend WithEvents T_Line4 As System.Windows.Forms.Label
    Friend WithEvents ErrorProvider1 As System.Windows.Forms.ErrorProvider
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents Label2 As System.Windows.Forms.Label
End Class
