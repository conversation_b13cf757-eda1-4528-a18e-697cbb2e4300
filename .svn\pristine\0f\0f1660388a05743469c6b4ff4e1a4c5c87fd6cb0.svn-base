﻿Imports System.Data.SqlClient


Public Class Yf_Js1

#Region "定义__变量"
    Public My_Adapter As New SqlDataAdapter             '从表一适配器
    Dim My_Table As New DataTable                       '从表一
    Dim My_Date As String                               '选择日期
    Public Zb_Cm As CurrencyManager                     '同步指针
    Public Zb_Row As DataRow                            '选 择 行

    Public V_Key As String
    Public My_DataSet As New DataSet
    Dim V_Ck_Code1 As String
    Dim My_Adapter1 As New SqlDataAdapter
#End Region

    Private Sub Yf_Js1_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Call Form_Init()
        Call Show_Data(DateTimePicker1.Value)
        Me.Focus()                              '窗体焦点
        Me.DateTimePicker1.Focus()
        CheckBox1.Checked = False
    End Sub

#Region "窗体事件"

    Private Sub Form_Init()

        Panel1.BorderStyle = BorderStyle.None
        Panel1.Height = 24
        'T_Combo.Width = 72
        T_Combo.Location = New Point(C1ToolBar1.Right + 3, 4)
        T_Textbox.Location = New Point(T_Combo.Left + T_Combo.Width, 5)
        T_Line2.Location = New Point(T_Textbox.Left + T_Textbox.Width + 3, 0)
        T_Label.Location = New Point(T_Line2.Left + 2, 2)
        T_Label2.Location = New Point(T_Label.Left + T_Label.Width, 6)
        DateTimePicker1.Location = New Point(T_Label2.Left + T_Label2.Width, 2)
        CheckBox1.Location = New Point(DateTimePicker1.Left + DateTimePicker1.Width + 10, 4)

        '初始化TDBGrid
        Dim My_Grid As New BaseClass.C_Grid(Me.C1TrueDBGrid1)
        With My_Grid
            .Init_Grid()
            .P_Dock(BaseClass.C_Grid.Dock.Fill)
            .AllAddNew(False)
            .AllDelete(False)
            .AllUpdate(False)
            .AllSort(True)
            .P_MultiSelect(BaseClass.C_Grid.MultiSelect.Extended)
            .P_MarqueeEnum(BaseClass.C_Grid.MarqueeEnum.HighlightRow)
            .Init_Column("调拨编码", "Ck_Code", 105, "中", "")
            .Init_Column("调拨日期", "Ck_Date", 80, "中", "yyyy-MM-dd")
            .Init_Column("调拨金额", "Ck_Money", 95, "右", "###,##0.00")
            .Init_Column("票据状态", "Ck_Qr1", 70, "中", "")
            .Init_Column("经手人", "Jsr_Name", 90, "左", "")
            .Init_Column("备注", "Ck_Memo", 60, "左", "")
        End With

        Dim My_Combo As New BaseClass.C_Combo1(Me.T_Combo)
        With My_Combo
            .Init_TDBCombo()
            .AddItem("调拨编码")
            .AddItem("经 手 人")
            .SelectedIndex(0)
            T_Combo.Width = 72
            T_Combo.DropDownWidth = 72
        End With
        DateTimePicker1.Value = Format(Date.Today, "yyyy-MM-dd")
    End Sub

#End Region

#Region "数据编辑"


    Private Sub Show_Data(ByVal My_Date As String)
        If My_DataSet.Tables("主表") IsNot Nothing Then My_DataSet.Tables("主表").Clear()
        Dim Str_Select As String
        If CheckBox1.Checked = False Then
            Str_Select = "Select Ck_Code,Zd_YyJsr.Jsr_Name,Ck_Qr,Ck_Date,Ck_Money,Ck_Memo,CASE Ck_Qr WHEN '1' THEN '已接收' WHEN '0' THEN '未接收' END AS Ck_Qr1 from Yk_Yf1,Zd_YyJsr where Yk_Yf1.Jsr_Code=Zd_YyJsr.Jsr_Code And Ck_Ok=1 And Yk_Yf1.Yy_Code='" & HisVar.HisVar.WsyCode & "' And Yk_Yf1.Yf_Code='" & HisVar.HisVar.YfCode & "' And CONVERT(varchar(10),Ck_Date,120)='" & Format(DateTimePicker1.Value, "yyyy-MM-dd") & "' order by Ck_Code"
            DateTimePicker1.Enabled = True
          
        Else
            Str_Select = "Select Ck_Code,Zd_YyJsr.Jsr_Name,Ck_Qr,Ck_Date,Ck_Money,Ck_Memo,CASE Ck_Qr WHEN '1' THEN '已接收' WHEN '0' THEN '未接收' END AS Ck_Qr1 from Yk_Yf1,Zd_YyJsr where Yk_Yf1.Jsr_Code=Zd_YyJsr.Jsr_Code And Ck_Qr='0' And Ck_Ok=1 And Yk_Yf1.Yy_Code='" & HisVar.HisVar.WsyCode & "' And Yk_Yf1.Yf_Code='" & HisVar.HisVar.YfCode & "' order by Ck_Code"
            DateTimePicker1.Enabled = False
        
        End If

        With My_Adapter
            .SelectCommand = New SqlCommand(Str_Select, My_Cn)
            .Fill(My_DataSet, "主表")
            .AcceptChangesDuringFill = True
            .MissingSchemaAction = MissingSchemaAction.AddWithKey
        End With
        My_Table = My_DataSet.Tables("主表")
        My_Table.PrimaryKey = New DataColumn() {My_Table.Columns("Ck_Code")}
        My_Table.Columns("Ck_Qr1").ReadOnly = False
        'TDBGrid初始化
        C1TrueDBGrid1.SetDataBinding(My_DataSet, "主表", True)
        Zb_Cm = CType(BindingContext(C1TrueDBGrid1.DataSource, C1TrueDBGrid1.DataMember), CurrencyManager)
        T_Label.Text = "∑=" + C1TrueDBGrid1.Splits(0).Rows.Count.ToString
        T_Textbox.Text = ""
    End Sub

#End Region

#Region "控件动作"

    Private Sub T_Combo_Close(ByVal sender As Object, ByVal e As System.EventArgs) Handles T_Combo.Close
        T_Textbox.Select()
        Select Case Me.T_Combo.Text
            Case "经 手 人"
                InputLanguage.CurrentInputLanguage = HisVar.HisVar.InputCode
            Case "调拨编码"
                InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages(0)
        End Select
    End Sub

    Private Sub T_Textbox_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles T_Textbox.TextChanged
        Dim My_View As New DataView
        My_View = Zb_Cm.List

        Dim V_Sort As String = ""
        Dim V_RowFilter As String = ""

        If Trim(T_Textbox.Text & "") = "" Then
            V_Sort = "Ck_Code Asc"
            V_RowFilter = ""
        Else
            Select Case T_Combo.Text
                Case "调拨编码"
                    V_Sort = "Ck_Code Asc"
                    V_RowFilter = "Ck_Code Like '*" & Trim(T_Textbox.Text) & "*'"
                Case "经 手 人"
                    V_Sort = "Jsr_Name Asc"
                    V_RowFilter = "Jsr_Name Like '*" & Trim(T_Textbox.Text) & "*'"
            End Select
        End If

        My_View.Sort = V_Sort
        My_View.RowFilter = V_RowFilter

    End Sub

    Private Sub DateTimePicker1_CloseUp(ByVal sender As Object, ByVal e As System.EventArgs) Handles DateTimePicker1.CloseUp
        C1TrueDBGrid1.Select()
    End Sub

    Private Sub DateTimePicker1_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles DateTimePicker1.KeyPress
        If e.KeyChar = Chr(Keys.Enter) Then
            e.Handled = True
            C1TrueDBGrid1.Select()
        End If
    End Sub

    Private Sub DateTimePicker1_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles DateTimePicker1.Validated
        Dim V_New_Date As String = Format(DateTimePicker1.Value, "yyyy-MM-dd")
        If My_Date <> V_New_Date Then
            My_Date = V_New_Date
            Call Show_Data(V_New_Date)
        End If
    End Sub

    Private Sub CheckBox1_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox1.CheckedChanged
        Call Show_Data(DateTimePicker1.Value)
    End Sub

    Private Sub C1TrueDBGrid1_MouseUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles C1TrueDBGrid1.MouseUp
        If C1TrueDBGrid1.RowCount = 0 Then Exit Sub
        If e.Button = Windows.Forms.MouseButtons.Right Then Call P_Show_Mx("DBGrid")
    End Sub


#End Region

    Private Sub P_Show_Mx(ByVal V_Lb As String)         '显示明细表
        Zb_Row = Zb_Cm.List(C1TrueDBGrid1.Row).Row
        Dim vform As New Yf_Js2(Me, DateTimePicker1.Value, Zb_Row, My_Table, C1TrueDBGrid1, T_Label, My_Adapter, "主窗体传参", CheckBox1.Checked)
        vform.Tag = "Yf_Js1"
        BaseFunc.BaseFunc.addTabControl(vform, "药房接收明细-" & Zb_Row("Ck_Code"))
    End Sub

    Private Sub Comm2_Click(ByVal sender As System.Object, ByVal e As C1.Win.C1Command.ClickEventArgs) Handles Comm2.Click
        Call Show_Data(DateTimePicker1.Value)
    End Sub


End Class