﻿Imports System.Data.SqlClient
Imports System.Data.OleDb
Imports BaseClass

Public Class Zd_MzFp13
    Dim My_Cc As New BaseClass.C_Cc()         '取最大编码及简称的类
    Dim My_Dataset As New Data.DataSet
    Dim My_Adapter As New SqlDataAdapter

    Private Sub Zd_MzFp13_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        With Zd_MzFp14
            Call Form_Init()
            If .V_Insert = True Then Call Data_Clear()
        End With
    End Sub

    Protected Overrides Sub OnKeyDown(ByVal e As System.Windows.Forms.KeyEventArgs)
        MyBase.OnKeyDown(e)
        If e.Control = True And e.KeyCode = Keys.S Then
            Comm1.Select()
            Call Comm_Click(Comm1, Nothing)
        End If
    End Sub

#Region "窗体__事件"

    Private Sub Form_Init()
        '按扭初始化
        Call P_Comm(Me.Comm1)
        Call P_Comm(Me.Comm2)
        If My_Dataset.Tables("项目字典") IsNot Nothing Then My_Dataset.Tables("项目字典").Clear()
        With My_Adapter
            '.SelectCommand = New SqlCommand("Select Lb_Code,Lb_Jc,Lb_Name From Zd_MzFp1 where Lb_Code not in (select Lb_Code from Zd_MzFp4 where Yy_Code='" & V_WsyCode & "') union all Select 'Y'+Dl_Code Lb_Code,Dl_Jc Lb_Jc,Dl_Name Lb_Name From Zd_Ml_Yp1 where 'Y'+Dl_Code not in (select Lb_Code from Zd_MzFp4 where Yy_Code='" & V_WsyCode & "') and  Dl_Name<>'卫生材料' Order By Lb_Jc", My_Cn)
            .SelectCommand = New SqlCommand("Select Lb_Code,Lb_Jc,Lb_Name From Zd_MzFp1 where Lb_Code not in (select Lb_Code from Zd_MzFp4 where Yy_Code='" & HisVar.HisVar.WsyCode & "') union all Select 'Y'+Dl_Code Lb_Code,Dl_Jc Lb_Jc,Dl_Name Lb_Name From Zd_Ml_Yp1 where 'Y'+Dl_Code not in (select Lb_Code from Zd_MzFp4 where Yy_Code='" & HisVar.HisVar.WsyCode & "')  Order By Lb_Jc", My_Cn)

            .Fill(My_Dataset, "项目字典")
        End With
        My_Dataset.Tables("项目字典").PrimaryKey = New DataColumn() {My_Dataset.Tables("项目字典").Columns("Lb_Code")}
        Dim My_Combo As C_Combo2 = New C_Combo2(C1Combo1, My_Dataset.Tables("项目字典").DefaultView, "Lb_Jc", "Lb_Code", 300)
        With My_Combo
            .Init_TDBCombo()
            .Init_Colum("Lb_Jc", "简称", 100, "左")
            .Init_Colum("Lb_Name", "名称", 150, "左")
            .Init_Colum("Lb_Code", "", 0, "中")
            .MaxDropDownItems(15)
            .SelectedIndex(-1)
        End With
    End Sub

#End Region

#Region "数据__编辑"

    Private Sub Data_Add()

        Dim My_NewRow As DataRow = Zd_MzFp14.My_Table.NewRow
        If Trim(C1Combo1.Text) = "" Then
            MsgBox("请选择名称!", MsgBoxStyle.Critical, "提示")
            C1Combo1.Focus()
            Exit Sub
        End If

        With My_NewRow
            .Item("Yy_Code") = HisVar.HisVar.WsyCode
            .Item("Mc_Code") = Zd_MzFp11.C1TrueDBGrid1.Columns("Mc_Code").Value
            .Item("Lb_Code") = C1Combo1.SelectedValue                                '最大编码
            .Item("Lb_Name") = C1Combo1.Columns("Lb_Name").Value
            .Item("Lb_Jc") = C1Combo1.Columns("Lb_Jc").Value
        End With
        MsgBox("数据添加成功!", MsgBoxStyle.Information, "提示:")
        '数据保存
        Try
            With Zd_MzFp14
                .My_Table.Rows.Add(My_NewRow)
                .C1TrueDBGrid1.MoveLast()

            End With
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            Exit Sub
        Finally
            C1Combo1.Select()
        End Try


        '更新数据
        With Zd_MzFp14.My_Adapter.InsertCommand
            Try
                Dim I As Integer = 0
                For Each My_Para As SqlParameter In .Parameters
                    My_Para.Value = My_NewRow.Item(I)
                    I = I + 1
                Next
                Call P_Conn(True)
                .ExecuteNonQuery()
                My_NewRow.AcceptChanges()
            Catch ex As Exception
                Beep()
                MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
                Exit Sub
            Finally
                C1Combo1.Select()
                Call P_Conn(False)
            End Try
        End With

        '清空记录--继续增加记录
        Call Data_Clear()
    End Sub





#End Region

#Region "数据__操作"

    Private Sub Data_Clear()
        Zd_MzFp14.V_Insert = True
        C1Combo1.Text = ""
        Label12.Text = ""
        If My_Dataset.Tables("项目字典") IsNot Nothing Then My_Dataset.Tables("项目字典").Clear()
        With My_Adapter
            '.SelectCommand = New SqlCommand("Select Lb_Code,Lb_Jc,Lb_Name From Zd_Mzfp1 where Lb_Code not in (select Lb_Code from Zd_MzFp4 where Yy_Code='" & V_WsyCode & "')  union all Select 'Y'+Dl_Code Lb_Code,Dl_Jc Lb_Jc,Dl_Name Lb_Name From Zd_Ml_Yp1 where 'Y'+Dl_Code not in (select Lb_Code from Zd_MzFp4 where Yy_Code='" & V_WsyCode & "') and Dl_Name<>'卫生材料' Order By Lb_Jc", My_Cn)
            .SelectCommand = New SqlCommand("Select Lb_Code,Lb_Jc,Lb_Name From Zd_Mzfp1 where Lb_Code not in (select Lb_Code from Zd_MzFp4 where Yy_Code='" & HisVar.HisVar.WsyCode & "')  union all Select 'Y'+Dl_Code Lb_Code,Dl_Jc Lb_Jc,Dl_Name Lb_Name From Zd_Ml_Yp1 where 'Y'+Dl_Code not in (select Lb_Code from Zd_MzFp4 where Yy_Code='" & HisVar.HisVar.WsyCode & "')  Order By Lb_Jc", My_Cn)

            .Fill(My_Dataset, "项目字典")
        End With
        My_Dataset.Tables("项目字典").PrimaryKey = New DataColumn() {My_Dataset.Tables("项目字典").Columns("Lb_Code")}
        Dim My_Combo As C_Combo2 = New C_Combo2(C1Combo1, My_Dataset.Tables("项目字典").DefaultView, "Lb_Jc", "Lb_Code", 300)
        With My_Combo
            .Init_TDBCombo()
            .Init_Colum("Lb_Jc", "简称", 100, "左")
            .Init_Colum("Lb_Name", "名称", 150, "左")
            .Init_Colum("Lb_Code", "", 0, "中")
            .MaxDropDownItems(15)
            .SelectedIndex(-1)
        End With
    End Sub



#End Region

#Region "控件__动作"

    Private Sub Comm_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.Click, Comm2.Click
        Select Case sender.tag
            Case "保存"
                If Zd_MzFp14.V_Insert = True Then
                    Call Data_Add()

                Else

                End If
            Case "取消"
                Me.Close()
        End Select
    End Sub




#End Region

#Region "自定义按扭"

    Private Sub P_Comm(ByVal Bt As Button)
        With Bt
            Select Case .Tag
                Case "保存"
                    .Top = 3
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")
                    .Text = "            &B"

                Case "取消"
                    .Location = New Point(Comm1.Left + Comm1.Width + 4, Comm1.Top)
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
                    .Width = Me.Comm1.Width
                    .Text = "            &C"
            End Select
            .FlatStyle = FlatStyle.Flat
            .FlatAppearance.BorderSize = 0
            .BackgroundImageLayout = ImageLayout.Center
        End With

    End Sub

    Private Sub Comm_MouseEnter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.MouseEnter, Comm2.MouseEnter
        Select Case sender.tag
            Case "保存"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存2")
            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消2")
        End Select

    End Sub

    Private Sub Comm_MouseLeave(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.MouseLeave, Comm2.MouseLeave
        Select Case sender.tag
            Case "保存"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")
            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
        End Select
    End Sub

    Private Sub Comm_MouseDown(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm1.MouseDown, Comm2.MouseDown
        Select Case sender.tag
            Case "保存"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存3")

            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消3")
        End Select
    End Sub

    Private Sub Comm_MouseUp(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm1.MouseUp, Comm2.MouseUp
        Select Case sender.tag
            Case "保存"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")
            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
        End Select
    End Sub

#End Region

#Region "输入法设置"
    '中文
    Private Sub C1TextBox1_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs)
        InputLanguage.CurrentInputLanguage = HisVar.HisVar.InputCode
    End Sub

    '英文
    Private Sub C1DateEdit1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages(0)
    End Sub
#End Region

    Private Sub C1Combo1_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles C1Combo1.KeyPress, Comm1.KeyPress
        If e.KeyChar <> Chr(Keys.Return) Then Exit Sub
        e.Handled = True
        System.Windows.Forms.SendKeys.Send("{Tab}")
    End Sub
    Private Sub C1Combo1_RowChange(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1Combo1.RowChange
        If C1Combo1.Text = "" Then
            Label12.Text = ""
        Else
            Label12.Text = C1Combo1.Columns("Lb_Name").Value
        End If
    End Sub

    Private Sub C1Combo1_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1Combo1.TextChanged
        If C1Combo1.Text = "" Then
            Label12.Text = ""
        Else
            Label12.Text = C1Combo1.Columns("Lb_Name").Value
        End If
    End Sub




End Class