﻿Public Class Sfzh15to18
    Public Shared Function getCheckCode(ByVal SFZH As String) As String
        Dim strJiaoYan() As Char = {"1", "0", "X", "9", "8", "7", "6", "5", "4", "3", "2"}
        Dim intQuan() As Integer = {7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2, 1}
        Dim strTemp As String
        Dim intTemp As String = Nothing
        Dim i As Integer

        strTemp = SFZH.Substring(0, 6) & "19" & SFZH.Substring(6)
        For i = 0 To strTemp.Length - 1
            intTemp = intTemp + Convert.ToInt32(strTemp.Substring(i, 1)) * intQuan(i)
        Next
        intTemp = intTemp Mod 11

        Return strTemp & strJiaoYan(intTemp)
    End Function

End Class
