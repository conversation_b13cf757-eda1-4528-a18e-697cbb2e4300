﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class Test_Cj
    Inherits HisControl.BaseForm

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel()
        Me.GroupBox1 = New System.Windows.Forms.GroupBox()
        Me.BtnFz = New CustomControl.MyButton()
        Me.TestCode = New CustomControl.MyTextBox()
        Me.BtnOk = New CustomControl.MyButton()
        Me.TestLb = New CustomControl.MySingleComobo()
        Me.BtnPrint = New CustomControl.MyButton()
        Me.BtnBar = New CustomControl.MyButton()
        Me.BarCode = New CustomControl.MyTextBox()
        Me.GetTime = New CustomControl.MyDateEdit()
        Me.GetJsr = New CustomControl.MyDtComobo()
        Me.TestSample = New CustomControl.MyDtComobo()
        Me.BtnDk = New CustomControl.MyButton()
        Me.RySex = New CustomControl.MySingleComobo()
        Me.RyName = New CustomControl.MyTextBox()
        Me.MyGrid1 = New CustomControl.MyGrid()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.BtnQuery = New CustomControl.MyButton()
        Me.MyDateEdit2 = New CustomControl.MyDateEdit()
        Me.MyDateEdit1 = New CustomControl.MyDateEdit()
        Me.TableLayoutPanel1.SuspendLayout()
        Me.GroupBox1.SuspendLayout()
        Me.Panel1.SuspendLayout()
        Me.SuspendLayout()
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.ColumnCount = 3
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 8.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 403.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 573.0!))
        Me.TableLayoutPanel1.Controls.Add(Me.GroupBox1, 1, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.MyGrid1, 2, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.Panel1, 2, 0)
        Me.TableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(0, 0)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.RowCount = 2
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 43.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 46.0!))
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(1170, 535)
        Me.TableLayoutPanel1.TabIndex = 0
        '
        'GroupBox1
        '
        Me.GroupBox1.Controls.Add(Me.BtnFz)
        Me.GroupBox1.Controls.Add(Me.TestCode)
        Me.GroupBox1.Controls.Add(Me.BtnOk)
        Me.GroupBox1.Controls.Add(Me.TestLb)
        Me.GroupBox1.Controls.Add(Me.BtnPrint)
        Me.GroupBox1.Controls.Add(Me.BtnBar)
        Me.GroupBox1.Controls.Add(Me.BarCode)
        Me.GroupBox1.Controls.Add(Me.GetTime)
        Me.GroupBox1.Controls.Add(Me.GetJsr)
        Me.GroupBox1.Controls.Add(Me.TestSample)
        Me.GroupBox1.Controls.Add(Me.BtnDk)
        Me.GroupBox1.Controls.Add(Me.RySex)
        Me.GroupBox1.Controls.Add(Me.RyName)
        Me.GroupBox1.Location = New System.Drawing.Point(11, 46)
        Me.GroupBox1.Name = "GroupBox1"
        Me.GroupBox1.Size = New System.Drawing.Size(389, 307)
        Me.GroupBox1.TabIndex = 1
        Me.GroupBox1.TabStop = False
        Me.GroupBox1.Text = "采集标本信息"
        '
        'BtnFz
        '
        Me.BtnFz.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.BtnFz.DialogResult = System.Windows.Forms.DialogResult.None
        Me.BtnFz.Location = New System.Drawing.Point(302, 241)
        Me.BtnFz.Name = "BtnFz"
        Me.BtnFz.Size = New System.Drawing.Size(70, 34)
        Me.BtnFz.TabIndex = 34
        Me.BtnFz.Tag = "复制"
        Me.BtnFz.Text = "复制"
        '
        'TestCode
        '
        Me.TestCode.Captain = "检验编码"
        Me.TestCode.CaptainBackColor = System.Drawing.Color.Transparent
        Me.TestCode.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.TestCode.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.TestCode.CaptainWidth = 60.0!
        Me.TestCode.ContentForeColor = System.Drawing.Color.Black
        Me.TestCode.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.TestCode.Location = New System.Drawing.Point(8, 42)
        Me.TestCode.Multiline = False
        Me.TestCode.Name = "TestCode"
        Me.TestCode.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.TestCode.ReadOnly = False
        Me.TestCode.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.TestCode.SelectionStart = 0
        Me.TestCode.SelectStart = 0
        Me.TestCode.Size = New System.Drawing.Size(180, 22)
        Me.TestCode.TabIndex = 33
        Me.TestCode.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.TestCode.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        '
        'BtnOk
        '
        Me.BtnOk.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.BtnOk.DialogResult = System.Windows.Forms.DialogResult.None
        Me.BtnOk.Location = New System.Drawing.Point(123, 241)
        Me.BtnOk.Name = "BtnOk"
        Me.BtnOk.Size = New System.Drawing.Size(70, 34)
        Me.BtnOk.TabIndex = 32
        Me.BtnOk.Tag = "确定"
        Me.BtnOk.Text = "确定"
        '
        'TestLb
        '
        Me.TestLb.Captain = "标本来源"
        Me.TestLb.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.TestLb.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.TestLb.CaptainWidth = 60.0!
        Me.TestLb.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        Me.TestLb.ItemHeight = 16
        Me.TestLb.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.TestLb.Location = New System.Drawing.Point(8, 121)
        Me.TestLb.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.TestLb.MinimumSize = New System.Drawing.Size(0, 20)
        Me.TestLb.Name = "TestLb"
        Me.TestLb.ReadOnly = False
        Me.TestLb.Size = New System.Drawing.Size(180, 20)
        Me.TestLb.TabIndex = 31
        Me.TestLb.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'BtnPrint
        '
        Me.BtnPrint.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.BtnPrint.DialogResult = System.Windows.Forms.DialogResult.None
        Me.BtnPrint.Location = New System.Drawing.Point(213, 241)
        Me.BtnPrint.Name = "BtnPrint"
        Me.BtnPrint.Size = New System.Drawing.Size(71, 34)
        Me.BtnPrint.TabIndex = 30
        Me.BtnPrint.Tag = "打印"
        Me.BtnPrint.Text = "打印"
        '
        'BtnBar
        '
        Me.BtnBar.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.BtnBar.DialogResult = System.Windows.Forms.DialogResult.None
        Me.BtnBar.Location = New System.Drawing.Point(213, 190)
        Me.BtnBar.Name = "BtnBar"
        Me.BtnBar.Size = New System.Drawing.Size(70, 27)
        Me.BtnBar.TabIndex = 29
        Me.BtnBar.Tag = "生成条码"
        Me.BtnBar.Text = "生成条码"
        '
        'BarCode
        '
        Me.BarCode.Captain = "条 形 码"
        Me.BarCode.CaptainBackColor = System.Drawing.Color.Transparent
        Me.BarCode.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.BarCode.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.BarCode.CaptainWidth = 60.0!
        Me.BarCode.ContentForeColor = System.Drawing.Color.Black
        Me.BarCode.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.BarCode.Location = New System.Drawing.Point(8, 192)
        Me.BarCode.Multiline = False
        Me.BarCode.Name = "BarCode"
        Me.BarCode.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.BarCode.ReadOnly = False
        Me.BarCode.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.BarCode.SelectionStart = 0
        Me.BarCode.SelectStart = 0
        Me.BarCode.Size = New System.Drawing.Size(180, 22)
        Me.BarCode.TabIndex = 28
        Me.BarCode.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.BarCode.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        '
        'GetTime
        '
        Me.GetTime.Captain = "采样时间"
        Me.GetTime.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.GetTime.CaptainWidth = 60.0!
        Me.GetTime.InitialSelection = C1.Win.C1Input.InitialSelectionEnum.CaretAtEnd
        Me.GetTime.Location = New System.Drawing.Point(192, 157)
        Me.GetTime.MaximumSize = New System.Drawing.Size(100000000, 20)
        Me.GetTime.MinimumSize = New System.Drawing.Size(0, 20)
        Me.GetTime.Name = "GetTime"
        Me.GetTime.Size = New System.Drawing.Size(180, 20)
        Me.GetTime.TabIndex = 27
        Me.GetTime.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.GetTime.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.GetTime.VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.DropDown
        '
        'GetJsr
        '
        Me.GetJsr.Captain = "采样医师"
        Me.GetJsr.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.GetJsr.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.GetJsr.CaptainWidth = 60.0!
        Me.GetJsr.DataSource = Nothing
        Me.GetJsr.ItemHeight = 18
        Me.GetJsr.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.GetJsr.Location = New System.Drawing.Point(8, 157)
        Me.GetJsr.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.GetJsr.MinimumSize = New System.Drawing.Size(0, 20)
        Me.GetJsr.Name = "GetJsr"
        Me.GetJsr.ReadOnly = False
        Me.GetJsr.Size = New System.Drawing.Size(178, 20)
        Me.GetJsr.TabIndex = 26
        Me.GetJsr.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'TestSample
        '
        Me.TestSample.Captain = "标本类型"
        Me.TestSample.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.TestSample.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.TestSample.CaptainWidth = 60.0!
        Me.TestSample.DataSource = Nothing
        Me.TestSample.ItemHeight = 18
        Me.TestSample.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.TestSample.Location = New System.Drawing.Point(192, 121)
        Me.TestSample.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.TestSample.MinimumSize = New System.Drawing.Size(0, 20)
        Me.TestSample.Name = "TestSample"
        Me.TestSample.ReadOnly = False
        Me.TestSample.Size = New System.Drawing.Size(180, 20)
        Me.TestSample.TabIndex = 22
        Me.TestSample.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'BtnDk
        '
        Me.BtnDk.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.BtnDk.DialogResult = System.Windows.Forms.DialogResult.None
        Me.BtnDk.Location = New System.Drawing.Point(213, 37)
        Me.BtnDk.Name = "BtnDk"
        Me.BtnDk.Size = New System.Drawing.Size(60, 27)
        Me.BtnDk.TabIndex = 25
        Me.BtnDk.Text = "读  卡"
        '
        'RySex
        '
        Me.RySex.Captain = "性    别"
        Me.RySex.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.RySex.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.RySex.CaptainWidth = 60.0!
        Me.RySex.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        Me.RySex.ItemHeight = 16
        Me.RySex.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.RySex.Location = New System.Drawing.Point(194, 85)
        Me.RySex.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.RySex.MinimumSize = New System.Drawing.Size(0, 20)
        Me.RySex.Name = "RySex"
        Me.RySex.ReadOnly = False
        Me.RySex.Size = New System.Drawing.Size(180, 20)
        Me.RySex.TabIndex = 24
        Me.RySex.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'RyName
        '
        Me.RyName.Captain = "姓    名"
        Me.RyName.CaptainBackColor = System.Drawing.Color.Transparent
        Me.RyName.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.RyName.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.RyName.CaptainWidth = 60.0!
        Me.RyName.ContentForeColor = System.Drawing.Color.Black
        Me.RyName.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.RyName.Location = New System.Drawing.Point(8, 83)
        Me.RyName.Multiline = False
        Me.RyName.Name = "RyName"
        Me.RyName.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.RyName.ReadOnly = False
        Me.RyName.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.RyName.SelectionStart = 0
        Me.RyName.SelectStart = 0
        Me.RyName.Size = New System.Drawing.Size(180, 22)
        Me.RyName.TabIndex = 23
        Me.RyName.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.RyName.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        '
        'MyGrid1
        '
        Me.MyGrid1.CanCustomCol = False
        Me.MyGrid1.Col = 0
        Me.MyGrid1.ColumnFooters = False
        Me.MyGrid1.DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveRight
        Me.MyGrid1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.MyGrid1.FetchRowStyles = False
        Me.MyGrid1.Location = New System.Drawing.Point(411, 43)
        Me.MyGrid1.Margin = New System.Windows.Forms.Padding(0)
        Me.MyGrid1.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.MyGrid1.Name = "MyGrid1"
        Me.MyGrid1.Size = New System.Drawing.Size(759, 492)
        Me.MyGrid1.TabAction = C1.Win.C1TrueDBGrid.TabActionEnum.ControlNavigation
        Me.MyGrid1.TabIndex = 0
        Me.MyGrid1.Xmlpath = Nothing
        '
        'Panel1
        '
        Me.Panel1.Controls.Add(Me.BtnQuery)
        Me.Panel1.Controls.Add(Me.MyDateEdit2)
        Me.Panel1.Controls.Add(Me.MyDateEdit1)
        Me.Panel1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel1.Location = New System.Drawing.Point(414, 3)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(753, 37)
        Me.Panel1.TabIndex = 1
        '
        'BtnQuery
        '
        Me.BtnQuery.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.BtnQuery.DialogResult = System.Windows.Forms.DialogResult.None
        Me.BtnQuery.Location = New System.Drawing.Point(335, 6)
        Me.BtnQuery.Name = "BtnQuery"
        Me.BtnQuery.Size = New System.Drawing.Size(60, 27)
        Me.BtnQuery.TabIndex = 2
        Me.BtnQuery.Tag = "查询"
        Me.BtnQuery.Text = "查询"
        '
        'MyDateEdit2
        '
        Me.MyDateEdit2.Captain = "至"
        Me.MyDateEdit2.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MyDateEdit2.CaptainWidth = 60.0!
        Me.MyDateEdit2.InitialSelection = C1.Win.C1Input.InitialSelectionEnum.CaretAtEnd
        Me.MyDateEdit2.Location = New System.Drawing.Point(173, 9)
        Me.MyDateEdit2.MaximumSize = New System.Drawing.Size(100000000, 20)
        Me.MyDateEdit2.MinimumSize = New System.Drawing.Size(0, 20)
        Me.MyDateEdit2.Name = "MyDateEdit2"
        Me.MyDateEdit2.Size = New System.Drawing.Size(145, 20)
        Me.MyDateEdit2.TabIndex = 1
        Me.MyDateEdit2.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MyDateEdit2.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.MyDateEdit2.VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.DropDown
        '
        'MyDateEdit1
        '
        Me.MyDateEdit1.Captain = "申请时间"
        Me.MyDateEdit1.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MyDateEdit1.CaptainWidth = 60.0!
        Me.MyDateEdit1.InitialSelection = C1.Win.C1Input.InitialSelectionEnum.CaretAtEnd
        Me.MyDateEdit1.Location = New System.Drawing.Point(22, 9)
        Me.MyDateEdit1.MaximumSize = New System.Drawing.Size(100000000, 20)
        Me.MyDateEdit1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.MyDateEdit1.Name = "MyDateEdit1"
        Me.MyDateEdit1.Size = New System.Drawing.Size(145, 20)
        Me.MyDateEdit1.TabIndex = 0
        Me.MyDateEdit1.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MyDateEdit1.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.MyDateEdit1.VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.DropDown
        '
        'Test_Cj
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1170, 535)
        Me.Controls.Add(Me.TableLayoutPanel1)
        Me.Name = "Test_Cj"
        Me.Text = "采集工作站"
        Me.TableLayoutPanel1.ResumeLayout(False)
        Me.GroupBox1.ResumeLayout(False)
        Me.Panel1.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents TableLayoutPanel1 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents MyGrid1 As CustomControl.MyGrid
    Friend WithEvents GroupBox1 As System.Windows.Forms.GroupBox
    Friend WithEvents BtnPrint As CustomControl.MyButton
    Friend WithEvents BtnBar As CustomControl.MyButton
    Friend WithEvents BarCode As CustomControl.MyTextBox
    Friend WithEvents GetTime As CustomControl.MyDateEdit
    Friend WithEvents GetJsr As CustomControl.MyDtComobo
    Friend WithEvents TestSample As CustomControl.MyDtComobo
    Friend WithEvents BtnDk As CustomControl.MyButton
    Friend WithEvents RySex As CustomControl.MySingleComobo
    Friend WithEvents RyName As CustomControl.MyTextBox
    Friend WithEvents TestLb As CustomControl.MySingleComobo
    Friend WithEvents BtnOk As CustomControl.MyButton
    Friend WithEvents TestCode As CustomControl.MyTextBox
    Friend WithEvents BtnFz As CustomControl.MyButton
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents BtnQuery As CustomControl.MyButton
    Friend WithEvents MyDateEdit2 As CustomControl.MyDateEdit
    Friend WithEvents MyDateEdit1 As CustomControl.MyDateEdit
End Class
