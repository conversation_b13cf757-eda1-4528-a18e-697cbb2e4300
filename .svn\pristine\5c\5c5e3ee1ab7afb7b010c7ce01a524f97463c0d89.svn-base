﻿/**  版本信息模板在安装目录下，可自行修改。
* D_LIS_DevPara.cs
*
* 功 能： N/A
* 类 名： D_LIS_DevPara
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/10/17 星期一 下午 1:38:51   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;

namespace DAL
{
	/// <summary>
	/// 数据访问类:D_LIS_DevPara
	/// </summary>
	public partial class D_LIS_DevPara
	{
		public D_LIS_DevPara()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string DevPara_Code)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from LIS_DevPara");
			strSql.Append(" where DevPara_Code=@DevPara_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@DevPara_Code", SqlDbType.Char,10)			};
			parameters[0].Value = DevPara_Code;

			return HisVar.HisVar.Sqldal.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ModelOld.M_LIS_DevPara model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into LIS_DevPara(");
			strSql.Append("DevPara_Code,Dev_Name,Dev_Jc,Dev_Model,Dev_Manufacturer,ParaItem)");
			strSql.Append(" values (");
			strSql.Append("@DevPara_Code,@Dev_Name,@Dev_Jc,@Dev_Model,@Dev_Manufacturer,@ParaItem)");
			SqlParameter[] parameters = {
					new SqlParameter("@DevPara_Code", SqlDbType.Char,10),
					new SqlParameter("@Dev_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Dev_Jc", SqlDbType.VarChar,50),
					new SqlParameter("@Dev_Model", SqlDbType.VarChar,50),
					new SqlParameter("@Dev_Manufacturer", SqlDbType.VarChar,50),
					new SqlParameter("@ParaItem", SqlDbType.VarChar,50)};
			parameters[0].Value = model.DevPara_Code;
			parameters[1].Value = model.Dev_Name;
			parameters[2].Value = model.Dev_Jc;
			parameters[3].Value = model.Dev_Model;
			parameters[4].Value = model.Dev_Manufacturer;
			parameters[5].Value = model.ParaItem;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ModelOld.M_LIS_DevPara model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update LIS_DevPara set ");
			strSql.Append("Dev_Name=@Dev_Name,");
			strSql.Append("Dev_Jc=@Dev_Jc,");
			strSql.Append("Dev_Model=@Dev_Model,");
			strSql.Append("Dev_Manufacturer=@Dev_Manufacturer,");
			strSql.Append("ParaItem=@ParaItem");
			strSql.Append(" where DevPara_Code=@DevPara_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Dev_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Dev_Jc", SqlDbType.VarChar,50),
					new SqlParameter("@Dev_Model", SqlDbType.VarChar,50),
					new SqlParameter("@Dev_Manufacturer", SqlDbType.VarChar,50),
					new SqlParameter("@ParaItem", SqlDbType.VarChar,50),
					new SqlParameter("@DevPara_Code", SqlDbType.Char,10)};
			parameters[0].Value = model.Dev_Name;
			parameters[1].Value = model.Dev_Jc;
			parameters[2].Value = model.Dev_Model;
			parameters[3].Value = model.Dev_Manufacturer;
			parameters[4].Value = model.ParaItem;
			parameters[5].Value = model.DevPara_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string DevPara_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from LIS_DevPara ");
			strSql.Append(" where DevPara_Code=@DevPara_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@DevPara_Code", SqlDbType.Char,10)			};
			parameters[0].Value = DevPara_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string DevPara_Codelist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from LIS_DevPara ");
			strSql.Append(" where DevPara_Code in ("+DevPara_Codelist + ")  ");
			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_LIS_DevPara GetModel(string DevPara_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 DevPara_Code,Dev_Name,Dev_Jc,Dev_Model,Dev_Manufacturer,ParaItem from LIS_DevPara ");
			strSql.Append(" where DevPara_Code=@DevPara_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@DevPara_Code", SqlDbType.Char,10)			};
			parameters[0].Value = DevPara_Code;

			ModelOld.M_LIS_DevPara model=new ModelOld.M_LIS_DevPara();
			DataSet ds=HisVar.HisVar.Sqldal.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_LIS_DevPara DataRowToModel(DataRow row)
		{
			ModelOld.M_LIS_DevPara model=new ModelOld.M_LIS_DevPara();
			if (row != null)
			{
				if(row["DevPara_Code"]!=null)
				{
					model.DevPara_Code=row["DevPara_Code"].ToString();
				}
				if(row["Dev_Name"]!=null)
				{
					model.Dev_Name=row["Dev_Name"].ToString();
				}
				if(row["Dev_Jc"]!=null)
				{
					model.Dev_Jc=row["Dev_Jc"].ToString();
				}
				if(row["Dev_Model"]!=null)
				{
					model.Dev_Model=row["Dev_Model"].ToString();
				}
				if(row["Dev_Manufacturer"]!=null)
				{
					model.Dev_Manufacturer=row["Dev_Manufacturer"].ToString();
				}
				if(row["ParaItem"]!=null)
				{
					model.ParaItem=row["ParaItem"].ToString();
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select DevPara_Code,Dev_Name,Dev_Jc,Dev_Model,Dev_Manufacturer,ParaItem ");
			strSql.Append(" FROM LIS_DevPara ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" DevPara_Code,Dev_Name,Dev_Jc,Dev_Model,Dev_Manufacturer,ParaItem ");
			strSql.Append(" FROM LIS_DevPara ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM LIS_DevPara ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.DevPara_Code desc");
			}
			strSql.Append(")AS Row, T.*  from LIS_DevPara T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "LIS_DevPara";
			parameters[1].Value = "DevPara_Code";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return HisVar.HisVar.Sqldal.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

