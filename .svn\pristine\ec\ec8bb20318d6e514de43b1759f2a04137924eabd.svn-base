﻿/**  版本信息模板在安装目录下，可自行修改。
* D_Zd_Bxlb.cs
*
* 功 能： N/A
* 类 名： D_Zd_Bxlb
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/10/21 15:32:50   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;

namespace DAL
{
	/// <summary>
	/// 数据访问类:D_Zd_Bxlb
	/// </summary>
	public partial class D_Zd_Bxlb
	{
		public D_Zd_Bxlb()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string Bxlb_Code)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from Zd_Bxlb");
			strSql.Append(" where Bxlb_Code=@Bxlb_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Bxlb_Code", SqlDbType.Char,6)			};
			parameters[0].Value = Bxlb_Code;

			return HisVar.HisVar.Sqldal.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ModelOld.M_Zd_Bxlb model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into Zd_Bxlb(");
			strSql.Append("Yy_Code,Bxlb_Code,Bxlb_Jc,Bxlb_Name,Bxlb_Memo)");
			strSql.Append(" values (");
			strSql.Append("@Yy_Code,@Bxlb_Code,@Bxlb_Jc,@Bxlb_Name,@Bxlb_Memo)");
			SqlParameter[] parameters = {
					new SqlParameter("@Yy_Code", SqlDbType.Char,4),
					new SqlParameter("@Bxlb_Code", SqlDbType.Char,6),
					new SqlParameter("@Bxlb_Jc", SqlDbType.VarChar,50),
					new SqlParameter("@Bxlb_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Bxlb_Memo", SqlDbType.VarChar,200)};
			parameters[0].Value = model.Yy_Code;
			parameters[1].Value = model.Bxlb_Code;
			parameters[2].Value = model.Bxlb_Jc;
			parameters[3].Value = model.Bxlb_Name;
			parameters[4].Value = model.Bxlb_Memo;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ModelOld.M_Zd_Bxlb model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update Zd_Bxlb set ");
			strSql.Append("Yy_Code=@Yy_Code,");
			strSql.Append("Bxlb_Jc=@Bxlb_Jc,");
			strSql.Append("Bxlb_Name=@Bxlb_Name,");
			strSql.Append("Bxlb_Memo=@Bxlb_Memo");
			strSql.Append(" where Bxlb_Code=@Bxlb_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Yy_Code", SqlDbType.Char,4),
					new SqlParameter("@Bxlb_Jc", SqlDbType.VarChar,50),
					new SqlParameter("@Bxlb_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Bxlb_Memo", SqlDbType.VarChar,200),
					new SqlParameter("@Bxlb_Code", SqlDbType.Char,6)};
			parameters[0].Value = model.Yy_Code;
			parameters[1].Value = model.Bxlb_Jc;
			parameters[2].Value = model.Bxlb_Name;
			parameters[3].Value = model.Bxlb_Memo;
			parameters[4].Value = model.Bxlb_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string Bxlb_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Zd_Bxlb ");
			strSql.Append(" where Bxlb_Code=@Bxlb_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Bxlb_Code", SqlDbType.Char,6)			};
			parameters[0].Value = Bxlb_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string Bxlb_Codelist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Zd_Bxlb ");
			strSql.Append(" where Bxlb_Code in ("+Bxlb_Codelist + ")  ");
			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Zd_Bxlb GetModel(string Bxlb_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 Yy_Code,Bxlb_Code,Bxlb_Jc,Bxlb_Name,Bxlb_Memo from Zd_Bxlb ");
			strSql.Append(" where Bxlb_Code=@Bxlb_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Bxlb_Code", SqlDbType.Char,6)			};
			parameters[0].Value = Bxlb_Code;

			ModelOld.M_Zd_Bxlb model=new ModelOld.M_Zd_Bxlb();
			DataSet ds=HisVar.HisVar.Sqldal.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Zd_Bxlb DataRowToModel(DataRow row)
		{
			ModelOld.M_Zd_Bxlb model=new ModelOld.M_Zd_Bxlb();
			if (row != null)
			{
				if(row["Yy_Code"]!=null)
				{
					model.Yy_Code=row["Yy_Code"].ToString();
				}
				if(row["Bxlb_Code"]!=null)
				{
					model.Bxlb_Code=row["Bxlb_Code"].ToString();
				}
				if(row["Bxlb_Jc"]!=null)
				{
					model.Bxlb_Jc=row["Bxlb_Jc"].ToString();
				}
				if(row["Bxlb_Name"]!=null)
				{
					model.Bxlb_Name=row["Bxlb_Name"].ToString();
				}
				if(row["Bxlb_Memo"]!=null)
				{
					model.Bxlb_Memo=row["Bxlb_Memo"].ToString();
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select Yy_Code,Bxlb_Code,Bxlb_Jc,Bxlb_Name,Bxlb_Memo ");
			strSql.Append(" FROM Zd_Bxlb ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" Yy_Code,Bxlb_Code,Bxlb_Jc,Bxlb_Name,Bxlb_Memo ");
			strSql.Append(" FROM Zd_Bxlb ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM Zd_Bxlb ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.Bxlb_Code desc");
			}
			strSql.Append(")AS Row, T.*  from Zd_Bxlb T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Zd_Bxlb";
			parameters[1].Value = "Bxlb_Code";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return HisVar.HisVar.Sqldal.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

