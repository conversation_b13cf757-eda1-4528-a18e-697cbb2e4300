﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="0" />
    <DataSources isList="true" count="2">
      <收入汇总表 Ref="2" type="DataTableSource" isKey="true">
        <Alias>收入汇总表</Alias>
        <Columns isList="true" count="9">
          <value>Yy_Name,System.String</value>
          <value>lb,System.String</value>
          <value>lb_Order,System.Int32</value>
          <value>lb2,System.String</value>
          <value>sl,System.Decimal</value>
          <value>S_Money,System.Decimal</value>
          <value>S_lb,System.String</value>
          <value>N_Order,System.Int32</value>
          <value>S_money,System.Decimal</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>收入汇总表</Name>
        <NameInSource>收入汇总表</NameInSource>
      </收入汇总表>
      <支出汇总表 Ref="3" type="DataTableSource" isKey="true">
        <Alias>支出汇总表</Alias>
        <Columns isList="true" count="8">
          <value>Yy_name,System.String</value>
          <value>lb,System.String</value>
          <value>lb_Order,System.Int32</value>
          <value>lb2,System.String</value>
          <value>sl,System.Int32</value>
          <value>S_money,System.Decimal</value>
          <value>S_Lb,System.String</value>
          <value>N_Order,System.Int32</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>支出汇总表</Name>
        <NameInSource>支出汇总表</NameInSource>
      </支出汇总表>
    </DataSources>
    <Relations isList="true" count="0" />
    <Report isRef="0" />
    <Variables isList="true" count="1">
      <value>,统计时间,统计时间,System.String,,False,False</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="4" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="1">
        <ReportTitleBand1 Ref="5" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,20,1091,62.99</ClientRectangle>
          <Components isList="true" count="4">
            <Text1 Ref="6" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,1086.61,39.37</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>黑体,15.75,Regular,Point,False,134</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text1</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <Text>全 县 基 层 卫 生 院 支  出 情 况 月 汇 总 表</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text1>
            <Text2 Ref="7" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>39.37,39.37,606.3,23.62</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,0</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text2</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <Text>{统计时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text2>
            <Text3 Ref="8" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>645.67,39.37,338.58,23.62</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,0</Font>
              <Guid>1d6956058038454ea03d0ecdd3b2b1c9</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text3</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <Text>单位：元</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text3>
            <CrossTab1 Ref="9" type="Stimulsoft.Report.CrossTab.StiCrossTab" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,60,1091,152.99</ClientRectangle>
              <Components isList="true" count="10">
                <CrossTab1_RowTotal1 Ref="10" type="CrossRowTotal" isKey="true">
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>[255:255:255]</Brush>
                  <ClientRectangle>0,98.43,70.87,0</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,8</Font>
                  <Guid>2716cfed07d3473c9a0147843c75868c</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_RowTotal1</Name>
                  <Page isRef="4" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <Text>Total</Text>
                  <TextBrush>Black</TextBrush>
                </CrossTab1_RowTotal1>
                <CrossTab1_Row1_Title Ref="11" type="CrossTitle" isKey="true">
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>White</Brush>
                  <ClientRectangle>0,22,80,40</ClientRectangle>
                  <Font>宋体,10.5,Regular,Point,False,0</Font>
                  <Guid>0a9ce7ca9ad84243a63c7502ed15c690</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_Row1_Title</Name>
                  <Page isRef="4" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <Text>医院名称</Text>
                  <TextBrush>[105:105:105]</TextBrush>
                  <Type>Expression</Type>
                  <TypeOfComponent>Row:CrossTab1_Row1</TypeOfComponent>
                </CrossTab1_Row1_Title>
                <CrossTab1_ColTotal1 Ref="12" type="CrossColumnTotal" isKey="true">
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>[255:255:255]</Brush>
                  <ClientRectangle>112,22,40,40</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>宋体,10.5,Regular,Point,False,0</Font>
                  <Guid>110689232ff44a0680c3db5527928323</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_ColTotal1</Name>
                  <Page isRef="4" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <Text>合计</Text>
                  <TextBrush>Black</TextBrush>
                  <Type>Expression</Type>
                </CrossTab1_ColTotal1>
                <CrossTab1_LeftTitle Ref="13" type="CrossTitle" isKey="true">
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>White</Brush>
                  <ClientRectangle>0,0,80,20</ClientRectangle>
                  <Enabled>False</Enabled>
                  <Font>宋体,10.5,Regular,Point,False,0</Font>
                  <Guid>e9880b9e747147a5b27cc692a284b921</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_LeftTitle</Name>
                  <Page isRef="4" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <Text>收入汇总表</Text>
                  <TextBrush>[105:105:105]</TextBrush>
                  <TypeOfComponent>LeftTitle</TypeOfComponent>
                </CrossTab1_LeftTitle>
                <CrossTab1_ColTotal2 Ref="14" type="CrossColumnTotal" isKey="true">
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>[255:255:255]</Brush>
                  <ClientRectangle>112,42,0,0</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>Arial,8</Font>
                  <Guid>f80420068cf84c9281af7b365c53b3b0</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_ColTotal2</Name>
                  <Page isRef="4" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <Text>Total</Text>
                  <TextBrush>Black</TextBrush>
                </CrossTab1_ColTotal2>
                <CrossTab1_Row1 Ref="15" type="CrossRow" isKey="true">
                  <Alias>Yy_Name</Alias>
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>White</Brush>
                  <ClientRectangle>0,64,80,20</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <DisplayValue>{收入汇总表.Yy_Name}</DisplayValue>
                  <Font>宋体,10.5,Regular,Point,False,0</Font>
                  <Guid>0c4e238957cc4af59a5e4e9437950f70</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_Row1</Name>
                  <Page isRef="4" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <ShowTotal>False</ShowTotal>
                  <Text>Yy_Name</Text>
                  <TextBrush>[105:105:105]</TextBrush>
                  <TotalGuid>2716cfed07d3473c9a0147843c75868c</TotalGuid>
                  <Value>{收入汇总表.Yy_Name}</Value>
                </CrossTab1_Row1>
                <CrossTab1_Column1 Ref="16" type="CrossColumn" isKey="true">
                  <Alias>lb</Alias>
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>White</Brush>
                  <ClientRectangle>82,22,30,20</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <DisplayValue>{收入汇总表.lb}</DisplayValue>
                  <Font>宋体,10.5,Regular,Point,False,0</Font>
                  <Guid>57fbd18932b84fb787afe4d93b9fad0e</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_Column1</Name>
                  <Page isRef="4" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <Text>lb</Text>
                  <TextBrush>[105:105:105]</TextBrush>
                  <TotalGuid>110689232ff44a0680c3db5527928323</TotalGuid>
                  <Value>{收入汇总表.lb}</Value>
                </CrossTab1_Column1>
                <CrossTab1_Column2 Ref="17" type="CrossColumn" isKey="true">
                  <Alias>S_lb</Alias>
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>White</Brush>
                  <ClientRectangle>82,42,30,20</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <DisplayValue>{收入汇总表.S_lb}</DisplayValue>
                  <Font>Arial,8</Font>
                  <Guid>c969b55ff95a42a8900594462cde32e9</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_Column2</Name>
                  <Page isRef="4" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <ShowTotal>False</ShowTotal>
                  <Text>S_lb</Text>
                  <TextBrush>[105:105:105]</TextBrush>
                  <TotalGuid>f80420068cf84c9281af7b365c53b3b0</TotalGuid>
                  <Value>{收入汇总表.S_lb}</Value>
                </CrossTab1_Column2>
                <CrossTab1_Sum1 Ref="18" type="CrossSummary" isKey="true">
                  <Alias>S_Money</Alias>
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>[255:255:255]</Brush>
                  <ClientRectangle>82,64,30,20</ClientRectangle>
                  <Conditions isList="true" count="0" />
                  <Font>宋体,10.5,Regular,Point,False,0</Font>
                  <Guid>54932a167d454107ad55ff6e166cd57a</Guid>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_Sum1</Name>
                  <Page isRef="4" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <Text>0</Text>
                  <TextBrush>Black</TextBrush>
                  <TextFormat Ref="19" type="CustomFormat" isKey="true">
                    <StringFormat>0.00</StringFormat>
                  </TextFormat>
                  <Value>{收入汇总表.S_Money}</Value>
                </CrossTab1_Sum1>
                <CrossTab1_RightTitle Ref="20" type="CrossTitle" isKey="true">
                  <Border>All;[155:155:155];1;Solid;False;4;Black</Border>
                  <Brush>White</Brush>
                  <ClientRectangle>82,0,70,20</ClientRectangle>
                  <Font>Arial,8</Font>
                  <Guid>1c9c403e90bf41aea587fc8c645c6d16</Guid>
                  <HorAlignment>Center</HorAlignment>
                  <Margins>0,0,0,0</Margins>
                  <Name>CrossTab1_RightTitle</Name>
                  <Page isRef="4" />
                  <Parent isRef="9" />
                  <Restrictions>AllowMove, AllowResize, AllowSelect, AllowChange</Restrictions>
                  <Text>lb, S_lb</Text>
                  <TextBrush>[105:105:105]</TextBrush>
                  <TypeOfComponent>RightTitle</TypeOfComponent>
                </CrossTab1_RightTitle>
              </Components>
              <Conditions isList="true" count="0" />
              <DataRelationName />
              <DataSourceName>收入汇总表</DataSourceName>
              <EmptyValue />
              <Filters isList="true" count="0" />
              <Name>CrossTab1</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <Sort isList="true" count="0" />
            </CrossTab1>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>ReportTitleBand1</Name>
          <Page isRef="4" />
          <Parent isRef="4" />
        </ReportTitleBand1>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>140e719a1fad4b49bcde72b3c70dd461</Guid>
      <Margins>39,39,39,39</Margins>
      <Name>Page1</Name>
      <Orientation>Landscape</Orientation>
      <PageHeight>827</PageHeight>
      <PageWidth>1169</PageWidth>
      <Report isRef="0" />
      <Watermark Ref="21" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page1>
  </Pages>
  <PrinterSettings Ref="22" type="Stimulsoft.Report.Print.StiPrinterSettings" isKey="true" />
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>支出汇总表</ReportAlias>
  <ReportChanged>8/21/2012 2:27:01 PM</ReportChanged>
  <ReportCreated>7/13/2012 3:39:50 PM</ReportCreated>
  <ReportFile>Rpt\支出汇总表.mrt</ReportFile>
  <ReportGuid>6789abf47b4c4a35b685a19995439d5e</ReportGuid>
  <ReportName>支出汇总表</ReportName>
  <ReportUnit>HundredthsOfInch</ReportUnit>
  <ReportVersion>2011.2.1026</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class Report : Stimulsoft.Report.StiReport
    {
        public Report()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
        #endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>