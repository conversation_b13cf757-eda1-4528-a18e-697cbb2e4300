﻿Public Class Zy_Jz_Zf

    Private Sub C1Button2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Button2.Click
        If MessageBox.Show("是否确认作废上次日结？", "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Exclamation) = DialogResult.Cancel Then
            Exit Sub
        End If
        Dim m_Jz_Code As String = HisVar.HisVar.Sqldal.GetSingle("Select Max(Jz_Code) From Bl_Jz Where Jsr_Code='" & HisVar.HisVar.JsrCode & "'")
        If m_Jz_Code & "" = "" Then
            MessageBox.Show("没有日结信息,无法作废", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
            Exit Sub
        End If
        Dim arr As New ArrayList
        arr.Add("Update Bl_Jf Set Jz_Code=Null Where Isnull(Jz_Code,'')='" & m_Jz_Code & "'")
        arr.Add("Update Bl    Set Jz_Code=Null Where Isnull(Jz_Code,'')='" & m_Jz_Code & "'")
        arr.Add("Update Bl_Cf       Set Jz_Code=Null Where Isnull(Jz_Code,'')='" & m_Jz_Code & "'")
        arr.Add("Update Bl_Zh    Set Jz_Code=Null Where Isnull(Jz_Code,'')='" & m_Jz_Code & "'")
        arr.Add("Delete From Bl_Jz Where Isnull(Jz_Code,'')='" & m_Jz_Code & "'")
        HisVar.HisVar.Sqldal.ExecuteSqlTran(arr)
        MessageBox.Show("日结作废成功!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub Zy_Jz_Zf_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Me.Select()
    End Sub
End Class