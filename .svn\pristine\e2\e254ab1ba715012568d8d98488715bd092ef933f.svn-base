﻿/**  版本信息模板在安装目录下，可自行修改。
* D_Zd_YyBq1.cs
*
* 功 能： N/A
* 类 名： D_Zd_YyBq1
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/8/29 1:45:27   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;

namespace DAL
{
	/// <summary>
	/// 数据访问类:D_Zd_YyBq1
	/// </summary>
	public partial class D_Zd_YyBq1
	{
		public D_Zd_YyBq1()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string Bq_Code)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from Zd_YyBq1");
			strSql.Append(" where Bq_Code=@Bq_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Bq_Code", SqlDbType.Char,7)			};
			parameters[0].Value = Bq_Code;

			return HisVar.HisVar.Sqldal.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ModelOld.M_Zd_YyBq1 model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into Zd_YyBq1(");
			strSql.Append("Yy_Code,Bq_Code,Bq_Name,Bq_Jc,Bq_Memo)");
			strSql.Append(" values (");
			strSql.Append("@Yy_Code,@Bq_Code,@Bq_Name,@Bq_Jc,@Bq_Memo)");
			SqlParameter[] parameters = {
					new SqlParameter("@Yy_Code", SqlDbType.Char,4),
					new SqlParameter("@Bq_Code", SqlDbType.Char,7),
					new SqlParameter("@Bq_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Bq_Jc", SqlDbType.VarChar,50),
					new SqlParameter("@Bq_Memo", SqlDbType.VarChar,50)};
			parameters[0].Value = model.Yy_Code;
			parameters[1].Value = model.Bq_Code;
			parameters[2].Value = model.Bq_Name;
			parameters[3].Value = model.Bq_Jc;
			parameters[4].Value = model.Bq_Memo;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ModelOld.M_Zd_YyBq1 model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update Zd_YyBq1 set ");
			strSql.Append("Yy_Code=@Yy_Code,");
			strSql.Append("Bq_Name=@Bq_Name,");
			strSql.Append("Bq_Jc=@Bq_Jc,");
			strSql.Append("Bq_Memo=@Bq_Memo");
			strSql.Append(" where Bq_Code=@Bq_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Yy_Code", SqlDbType.Char,4),
					new SqlParameter("@Bq_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Bq_Jc", SqlDbType.VarChar,50),
					new SqlParameter("@Bq_Memo", SqlDbType.VarChar,50),
					new SqlParameter("@Bq_Code", SqlDbType.Char,7)};
			parameters[0].Value = model.Yy_Code;
			parameters[1].Value = model.Bq_Name;
			parameters[2].Value = model.Bq_Jc;
			parameters[3].Value = model.Bq_Memo;
			parameters[4].Value = model.Bq_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string Bq_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Zd_YyBq1 ");
			strSql.Append(" where Bq_Code=@Bq_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Bq_Code", SqlDbType.Char,7)			};
			parameters[0].Value = Bq_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string Bq_Codelist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Zd_YyBq1 ");
			strSql.Append(" where Bq_Code in ("+Bq_Codelist + ")  ");
			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Zd_YyBq1 GetModel(string Bq_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 Yy_Code,Bq_Code,Bq_Name,Bq_Jc,Bq_Memo from Zd_YyBq1 ");
			strSql.Append(" where Bq_Code=@Bq_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Bq_Code", SqlDbType.Char,7)			};
			parameters[0].Value = Bq_Code;

			ModelOld.M_Zd_YyBq1 model=new ModelOld.M_Zd_YyBq1();
			DataSet ds=HisVar.HisVar.Sqldal.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Zd_YyBq1 DataRowToModel(DataRow row)
		{
			ModelOld.M_Zd_YyBq1 model=new ModelOld.M_Zd_YyBq1();
			if (row != null)
			{
				if(row["Yy_Code"]!=null)
				{
					model.Yy_Code=row["Yy_Code"].ToString();
				}
				if(row["Bq_Code"]!=null)
				{
					model.Bq_Code=row["Bq_Code"].ToString();
				}
				if(row["Bq_Name"]!=null)
				{
					model.Bq_Name=row["Bq_Name"].ToString();
				}
				if(row["Bq_Jc"]!=null)
				{
					model.Bq_Jc=row["Bq_Jc"].ToString();
				}
				if(row["Bq_Memo"]!=null)
				{
					model.Bq_Memo=row["Bq_Memo"].ToString();
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select Yy_Code,Bq_Code,Bq_Name,Bq_Jc,Bq_Memo ");
			strSql.Append(" FROM Zd_YyBq1 ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" Yy_Code,Bq_Code,Bq_Name,Bq_Jc,Bq_Memo ");
			strSql.Append(" FROM Zd_YyBq1 ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM Zd_YyBq1 ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.Bq_Code desc");
			}
			strSql.Append(")AS Row, T.*  from Zd_YyBq1 T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Zd_YyBq1";
			parameters[1].Value = "Bq_Code";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return HisVar.HisVar.Sqldal.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

