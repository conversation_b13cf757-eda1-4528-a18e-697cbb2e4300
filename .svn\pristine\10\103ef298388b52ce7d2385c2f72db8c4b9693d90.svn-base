﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class Return_Search
    Inherits HisControl.BaseForm

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.C1Holder1 = New C1.Win.C1Command.C1CommandHolder()
        Me.Control1 = New C1.Win.C1Command.C1CommandControl()
        Me.Control2 = New C1.Win.C1Command.C1CommandControl()
        Me.button = New C1.Win.C1Input.C1Button()
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel()
        Me.SuplierDtComobo1 = New CustomControl.MyDtComobo()
        Me.Label6 = New System.Windows.Forms.Label()
        Me.MyDateEdit1 = New CustomControl.MyDateEdit()
        Me.MyDateEdit2 = New CustomControl.MyDateEdit()
        Me.cb_Czy = New CustomControl.MyDtComobo()
        Me.DhText = New CustomControl.MyTextBox()
        Me.StateSingleComobo1 = New CustomControl.MySingleComobo()
        Me.WhCombobox = New CustomControl.MyDtComobo()
        Me.MyGrid1 = New CustomControl.MyGrid()
        Me.WriteOffStatusSingleComobo1 = New CustomControl.MySingleComobo()
        CType(Me.C1Holder1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.TableLayoutPanel1.SuspendLayout()
        Me.SuspendLayout()
        '
        'C1Holder1
        '
        Me.C1Holder1.Commands.Add(Me.Control1)
        Me.C1Holder1.Commands.Add(Me.Control2)
        Me.C1Holder1.Owner = Me
        Me.C1Holder1.VisualStyle = C1.Win.C1Command.VisualStyle.Office2010Blue
        '
        'Control1
        '
        Me.Control1.HotFrame = C1.Win.C1Command.HotFrameEnum.None
        Me.Control1.Name = "Control1"
        Me.Control1.ShortcutText = ""
        Me.Control1.ShowShortcut = False
        Me.Control1.ShowTextAsToolTip = False
        Me.Control1.Text = "New Command"
        '
        'Control2
        '
        Me.Control2.HotFrame = C1.Win.C1Command.HotFrameEnum.None
        Me.Control2.Name = "Control2"
        Me.Control2.ShortcutText = ""
        Me.Control2.ShowShortcut = False
        Me.Control2.ShowTextAsToolTip = False
        Me.Control2.Text = "New Command"
        '
        'button
        '
        Me.button.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.button.Image = Global.ZtHis.Materials.My.Resources.Resources.miBarSearch_Glyph
        Me.button.Location = New System.Drawing.Point(788, 29)
        Me.button.Name = "button"
        Me.button.Size = New System.Drawing.Size(86, 20)
        Me.button.TabIndex = 8
        Me.button.Tag = "查询"
        Me.button.Text = "查询"
        Me.button.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText
        Me.button.UseVisualStyleBackColor = True
        Me.button.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2010Blue
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.ColumnCount = 11
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 45.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 130.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 20.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 130.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 60.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 100.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 60.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 100.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 60.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 80.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanel1.Controls.Add(Me.SuplierDtComobo1, 4, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.Label6, 0, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.MyDateEdit1, 0, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.MyDateEdit2, 2, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.cb_Czy, 10, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.DhText, 0, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.StateSingleComobo1, 4, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.button, 10, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.WhCombobox, 7, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.WriteOffStatusSingleComobo1, 7, 0)
        Me.TableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Top
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(0, 0)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.RowCount = 3
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 26.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 26.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 4.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 20.0!))
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(1139, 63)
        Me.TableLayoutPanel1.TabIndex = 0
        '
        'SuplierDtComobo1
        '
        Me.SuplierDtComobo1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SuplierDtComobo1.Captain = "供 应 商"
        Me.SuplierDtComobo1.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.SuplierDtComobo1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.SuplierDtComobo1.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.SuplierDtComobo1, 3)
        Me.SuplierDtComobo1.DataSource = Nothing
        Me.SuplierDtComobo1.ItemHeight = 18
        Me.SuplierDtComobo1.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.SuplierDtComobo1.Location = New System.Drawing.Point(328, 29)
        Me.SuplierDtComobo1.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.SuplierDtComobo1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.SuplierDtComobo1.Name = "SuplierDtComobo1"
        Me.SuplierDtComobo1.ReadOnly = False
        Me.SuplierDtComobo1.Size = New System.Drawing.Size(214, 20)
        Me.SuplierDtComobo1.TabIndex = 6
        Me.SuplierDtComobo1.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'Label6
        '
        Me.Label6.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label6.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.TableLayoutPanel1.SetColumnSpan(Me.Label6, 11)
        Me.Label6.ImageAlign = System.Drawing.ContentAlignment.BottomCenter
        Me.Label6.Location = New System.Drawing.Point(3, 56)
        Me.Label6.Name = "Label6"
        Me.Label6.Size = New System.Drawing.Size(1133, 2)
        Me.Label6.TabIndex = 127
        Me.Label6.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'MyDateEdit1
        '
        Me.MyDateEdit1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.MyDateEdit1.Captain = "日期"
        Me.MyDateEdit1.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MyDateEdit1.CaptainWidth = 40.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.MyDateEdit1, 2)
        Me.MyDateEdit1.InitialSelection = C1.Win.C1Input.InitialSelectionEnum.CaretAtEnd
        Me.MyDateEdit1.Location = New System.Drawing.Point(3, 29)
        Me.MyDateEdit1.MaximumSize = New System.Drawing.Size(100000000, 20)
        Me.MyDateEdit1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.MyDateEdit1.Name = "MyDateEdit1"
        Me.MyDateEdit1.Size = New System.Drawing.Size(169, 20)
        Me.MyDateEdit1.TabIndex = 4
        Me.MyDateEdit1.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MyDateEdit1.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.MyDateEdit1.VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.DropDown
        '
        'MyDateEdit2
        '
        Me.MyDateEdit2.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.MyDateEdit2.Captain = "至"
        Me.MyDateEdit2.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MyDateEdit2.CaptainWidth = 20.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.MyDateEdit2, 2)
        Me.MyDateEdit2.InitialSelection = C1.Win.C1Input.InitialSelectionEnum.CaretAtEnd
        Me.MyDateEdit2.Location = New System.Drawing.Point(178, 29)
        Me.MyDateEdit2.MaximumSize = New System.Drawing.Size(100000000, 20)
        Me.MyDateEdit2.MinimumSize = New System.Drawing.Size(0, 20)
        Me.MyDateEdit2.Name = "MyDateEdit2"
        Me.MyDateEdit2.Size = New System.Drawing.Size(144, 20)
        Me.MyDateEdit2.TabIndex = 5
        Me.MyDateEdit2.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MyDateEdit2.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.MyDateEdit2.VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.DropDown
        '
        'cb_Czy
        '
        Me.cb_Czy.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.cb_Czy.Captain = "操 作 员"
        Me.cb_Czy.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.cb_Czy.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.cb_Czy.CaptainWidth = 60.0!
        Me.cb_Czy.DataSource = Nothing
        Me.cb_Czy.ItemHeight = 18
        Me.cb_Czy.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.cb_Czy.Location = New System.Drawing.Point(788, 3)
        Me.cb_Czy.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.cb_Czy.MinimumSize = New System.Drawing.Size(0, 20)
        Me.cb_Czy.Name = "cb_Czy"
        Me.cb_Czy.ReadOnly = False
        Me.cb_Czy.Size = New System.Drawing.Size(247, 20)
        Me.cb_Czy.TabIndex = 3
        Me.cb_Czy.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'DhText
        '
        Me.DhText.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.DhText.Captain = "单号"
        Me.DhText.CaptainBackColor = System.Drawing.Color.Transparent
        Me.DhText.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.DhText.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.DhText.CaptainWidth = 40.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.DhText, 4)
        Me.DhText.ContentForeColor = System.Drawing.Color.Black
        Me.DhText.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.DhText.Location = New System.Drawing.Point(3, 3)
        Me.DhText.Multiline = False
        Me.DhText.Name = "DhText"
        Me.DhText.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.DhText.ReadOnly = False
        Me.DhText.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.DhText.SelectionStart = 0
        Me.DhText.SelectStart = 0
        Me.DhText.Size = New System.Drawing.Size(319, 20)
        Me.DhText.TabIndex = 0
        Me.DhText.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.DhText.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        '
        'StateSingleComobo1
        '
        Me.StateSingleComobo1.Captain = "录入状态"
        Me.StateSingleComobo1.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.StateSingleComobo1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.StateSingleComobo1.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.StateSingleComobo1, 3)
        Me.StateSingleComobo1.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        Me.StateSingleComobo1.ItemHeight = 16
        Me.StateSingleComobo1.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.StateSingleComobo1.Location = New System.Drawing.Point(328, 3)
        Me.StateSingleComobo1.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.StateSingleComobo1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.StateSingleComobo1.Name = "StateSingleComobo1"
        Me.StateSingleComobo1.ReadOnly = False
        Me.StateSingleComobo1.Size = New System.Drawing.Size(214, 20)
        Me.StateSingleComobo1.TabIndex = 1
        Me.StateSingleComobo1.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'WhCombobox
        '
        Me.WhCombobox.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.WhCombobox.Captain = "仓库名称"
        Me.WhCombobox.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.WhCombobox.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.WhCombobox.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.WhCombobox, 3)
        Me.WhCombobox.DataSource = Nothing
        Me.WhCombobox.ItemHeight = 18
        Me.WhCombobox.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.WhCombobox.Location = New System.Drawing.Point(548, 29)
        Me.WhCombobox.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.WhCombobox.MinimumSize = New System.Drawing.Size(0, 20)
        Me.WhCombobox.Name = "WhCombobox"
        Me.WhCombobox.ReadOnly = False
        Me.WhCombobox.Size = New System.Drawing.Size(234, 20)
        Me.WhCombobox.TabIndex = 7
        Me.WhCombobox.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'MyGrid1
        '
        Me.MyGrid1.CanCustomCol = False
        Me.MyGrid1.Col = 0
        Me.MyGrid1.ColumnFooters = False
        Me.MyGrid1.DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveRight
        Me.MyGrid1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.MyGrid1.FetchRowStyles = False
        Me.MyGrid1.Location = New System.Drawing.Point(0, 63)
        Me.MyGrid1.Margin = New System.Windows.Forms.Padding(0)
        Me.MyGrid1.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.MyGrid1.Name = "MyGrid1"
        Me.MyGrid1.Size = New System.Drawing.Size(1139, 613)
        Me.MyGrid1.TabAction = C1.Win.C1TrueDBGrid.TabActionEnum.ControlNavigation
        Me.MyGrid1.TabIndex = 1
        Me.MyGrid1.Xmlpath = Nothing
        '
        'WriteOffStatusSingleComobo1
        '
        Me.WriteOffStatusSingleComobo1.Captain = "冲销状态"
        Me.WriteOffStatusSingleComobo1.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.WriteOffStatusSingleComobo1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.WriteOffStatusSingleComobo1.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.WriteOffStatusSingleComobo1, 3)
        Me.WriteOffStatusSingleComobo1.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        Me.WriteOffStatusSingleComobo1.ItemHeight = 16
        Me.WriteOffStatusSingleComobo1.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.WriteOffStatusSingleComobo1.Location = New System.Drawing.Point(548, 3)
        Me.WriteOffStatusSingleComobo1.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.WriteOffStatusSingleComobo1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.WriteOffStatusSingleComobo1.Name = "WriteOffStatusSingleComobo1"
        Me.WriteOffStatusSingleComobo1.ReadOnly = False
        Me.WriteOffStatusSingleComobo1.Size = New System.Drawing.Size(234, 20)
        Me.WriteOffStatusSingleComobo1.TabIndex = 2
        Me.WriteOffStatusSingleComobo1.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'Return_Search
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1139, 676)
        Me.Controls.Add(Me.MyGrid1)
        Me.Controls.Add(Me.TableLayoutPanel1)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle
        Me.Margin = New System.Windows.Forms.Padding(5)
        Me.MinimizeBox = False
        Me.Name = "Return_Search"
        Me.ShowInTaskbar = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "盘点返单"
        CType(Me.C1Holder1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.TableLayoutPanel1.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents C1Holder1 As C1.Win.C1Command.C1CommandHolder
    Friend WithEvents Control1 As C1.Win.C1Command.C1CommandControl
    Friend WithEvents Control2 As C1.Win.C1Command.C1CommandControl
    Friend WithEvents TableLayoutPanel1 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents button As C1.Win.C1Input.C1Button
    Friend WithEvents Label6 As System.Windows.Forms.Label
    Friend WithEvents MyDateEdit1 As CustomControl.MyDateEdit
    Friend WithEvents MyDateEdit2 As CustomControl.MyDateEdit
    Friend WithEvents cb_Czy As CustomControl.MyDtComobo
    Friend WithEvents WhCombobox As CustomControl.MyDtComobo
    Friend WithEvents DhText As CustomControl.MyTextBox
    Friend WithEvents MyGrid1 As CustomControl.MyGrid
    Friend WithEvents StateSingleComobo1 As CustomControl.MySingleComobo
    Friend WithEvents SuplierDtComobo1 As CustomControl.MyDtComobo
    Friend WithEvents WriteOffStatusSingleComobo1 As CustomControl.MySingleComobo

End Class
