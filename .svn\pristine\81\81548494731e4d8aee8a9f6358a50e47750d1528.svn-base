﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="0" />
    <DataSources isList="true" count="3">
      <view发药明细 Ref="2" type="Stimulsoft.Report.Dictionary.StiDataViewSource" isKey="true">
        <Alias>view发药明细</Alias>
        <Columns isList="true" count="21">
          <value>V_Lb,System.String</value>
          <value>Ry_Name,System.String</value>
          <value>Ry_BlCode,System.String</value>
          <value>Ks_Name,System.String</value>
          <value>Ys_Name,System.String</value>
          <value>Cf_Date,System.DateTime</value>
          <value>Yp_Name,System.String</value>
          <value>Mx_Gg,System.String</value>
          <value>Mx_Cd,System.String</value>
          <value>Yp_Yxq,System.DateTime</value>
          <value>Yp_Ph,System.String</value>
          <value>Mx_Xsdw,System.String</value>
          <value>Cf_Sl,System.Decimal</value>
          <value>Cf_Dj,System.Decimal</value>
          <value>Cf_Money,System.Decimal</value>
          <value>Jsr_Name,System.String</value>
          <value>Dl_Name,System.String</value>
          <value>IsJb,System.String</value>
          <value>Special,System.String</value>
          <value>Yf_Code,System.String</value>
          <value>Qr_Date,System.DateTime</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>view发药明细</Name>
        <NameInSource>view发药明细</NameInSource>
      </view发药明细>
      <ds Ref="3" type="DataTableSource" isKey="true">
        <Alias>ds</Alias>
        <Columns isList="true" count="21">
          <value>V_Lb,System.String</value>
          <value>Ry_Name,System.String</value>
          <value>Ry_BlCode,System.String</value>
          <value>Ks_Name,System.String</value>
          <value>Ys_Name,System.String</value>
          <value>Cf_Date,System.DateTime</value>
          <value>Yp_Name,System.String</value>
          <value>Mx_Gg,System.String</value>
          <value>Mx_Cd,System.String</value>
          <value>Yp_Yxq,System.DateTime</value>
          <value>Yp_Ph,System.String</value>
          <value>Mx_Xsdw,System.String</value>
          <value>Cf_Sl,System.Decimal</value>
          <value>Cf_Dj,System.Decimal</value>
          <value>Cf_Money,System.Decimal</value>
          <value>Jsr_Name,System.String</value>
          <value>Dl_Name,System.String</value>
          <value>IsJb,System.String</value>
          <value>Special,System.String</value>
          <value>Yf_Code,System.String</value>
          <value>Qr_Date,System.DateTime</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>ds</Name>
        <NameInSource>发药明细</NameInSource>
      </ds>
      <ds2 Ref="4" type="DataTableSource" isKey="true">
        <Alias>ds</Alias>
        <Columns isList="true" count="13">
          <value>V_Lb,System.String</value>
          <value>Cf_Code,System.String</value>
          <value>Ry_Name,System.String</value>
          <value>Ks_Name,System.String</value>
          <value>Ys_Name,System.String</value>
          <value>Cf_Date,System.DateTime</value>
          <value>Xm_Name,System.String</value>
          <value>Xm_Dw,System.String</value>
          <value>Cf_Sl,System.Decimal</value>
          <value>Cf_Dj,System.Decimal</value>
          <value>Cf_Money,System.Decimal</value>
          <value>Xmlb_Name,System.String</value>
          <value>Qr_Date,System.DateTime</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>ds2</Name>
        <NameInSource>项目明细</NameInSource>
      </ds2>
    </DataSources>
    <Relations isList="true" count="0" />
    <Report isRef="0" />
    <Variables isList="true" count="3">
      <value>,打印时间,打印时间,System.String,,False,False</value>
      <value>,标题,标题,System.String,,False,False</value>
      <value>,查询时间,查询时间,System.String,,False,False</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="5" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="8">
        <ReportTitleBand1 Ref="6" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,19,1.5</ClientRectangle>
          <Components isList="true" count="3">
            <Text1 Ref="7" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,19,0.9</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>黑体,15,Regular,Point,False,134</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text1</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>{标题}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text1>
            <Text2 Ref="8" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>10,0.9,9,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text2</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>打印时间:{打印时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text2>
            <Text3 Ref="9" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.9,9,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <Guid>04ac4c72595c4c0a97835740f47d48ab</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text3</Name>
              <Page isRef="5" />
              <Parent isRef="6" />
              <Text>{查询时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text3>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>ReportTitleBand1</Name>
          <Page isRef="5" />
          <Parent isRef="5" />
        </ReportTitleBand1>
        <HeaderBand1 Ref="10" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,2.7,19,0.5</ClientRectangle>
          <Components isList="true" count="7">
            <Text5 Ref="11" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text5</Name>
              <Page isRef="5" />
              <Parent isRef="10" />
              <Text>项目名称</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text5>
            <Text11 Ref="12" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>12.2,0,1.2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text11</Name>
              <Page isRef="5" />
              <Parent isRef="10" />
              <Text>单位</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text11>
            <Text17 Ref="13" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>10.9,0,1.3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text17</Name>
              <Page isRef="5" />
              <Parent isRef="10" />
              <Text>数量</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text17>
            <Text19 Ref="14" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>15.5,0,3.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text19</Name>
              <Page isRef="5" />
              <Parent isRef="10" />
              <Text>销售金额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text19>
            <Text6 Ref="15" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>4,0,3.4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>310f71df2f2e4be997cf22e5a98db41c</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text6</Name>
              <Page isRef="5" />
              <Parent isRef="10" />
              <Text>患者名称</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text6>
            <Text8 Ref="16" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>7.4,0,3.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>a3fce7c808854cdfbe0b9725119d128f</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text8</Name>
              <Page isRef="5" />
              <Parent isRef="10" />
              <Text>打印时间</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text8>
            <Text21 Ref="17" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>13.4,0,2.1,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>78616270d11d43f5b4cd990080e5c4b7</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text21</Name>
              <Page isRef="5" />
              <Parent isRef="10" />
              <Text>单价</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text21>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>HeaderBand1</Name>
          <Page isRef="5" />
          <Parent isRef="5" />
        </HeaderBand1>
        <GroupHeaderBand1 Ref="18" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,4,19,0.5</ClientRectangle>
          <Components isList="true" count="1">
            <Text20 Ref="19" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,19,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>d957366811614a2099146b42fd607f2e</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text20</Name>
              <Page isRef="5" />
              <Parent isRef="18" />
              <Text>{ds2.Xmlb_Name}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text20>
          </Components>
          <Condition>{ds2.Xmlb_Name}</Condition>
          <Conditions isList="true" count="0" />
          <Name>GroupHeaderBand1</Name>
          <Page isRef="5" />
          <Parent isRef="5" />
        </GroupHeaderBand1>
        <GroupHeaderBand2 Ref="20" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,5.3,19,0</ClientRectangle>
          <Components isList="true" count="0" />
          <Condition>{ds2.Xm_Name}</Condition>
          <Conditions isList="true" count="0" />
          <Name>GroupHeaderBand2</Name>
          <Page isRef="5" />
          <Parent isRef="5" />
        </GroupHeaderBand2>
        <DataBand1 Ref="21" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,6.1,19,0</ClientRectangle>
          <Components isList="true" count="0" />
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>ds2</DataSourceName>
          <Filters isList="true" count="0" />
          <Name>DataBand1</Name>
          <Page isRef="5" />
          <Parent isRef="5" />
          <Sort isList="true" count="0" />
        </DataBand1>
        <GroupFooterBand2 Ref="22" type="GroupFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,6.9,19,0.5</ClientRectangle>
          <Components isList="true" count="7">
            <Text4 Ref="23" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>0,0,0,0</Margins>
              <Name>Text4</Name>
              <Page isRef="5" />
              <Parent isRef="22" />
              <Text>{ds2.Xm_Name}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text4>
            <Text10 Ref="24" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>10.9,0,1.3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>36d1782af2bd4bc5964e189dab6c86a0</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text10</Name>
              <Page isRef="5" />
              <Parent isRef="22" />
              <Text>{Sum(GroupHeaderBand2,ds2.Cf_Sl)} </Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="25" type="CustomFormat" isKey="true">
                <StringFormat>0.####</StringFormat>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text10>
            <Text12 Ref="26" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>12.2,0,1.2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>eb420e8c35b04a73b6b483b1fdfcb0e6</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text12</Name>
              <Page isRef="5" />
              <Parent isRef="22" />
              <Text>{ds2.Xm_Dw}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text12>
            <Text13 Ref="27" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>15.5,0,3.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>e0414a3ab2d544398ea56bd5846b1f46</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text13</Name>
              <Page isRef="5" />
              <Parent isRef="22" />
              <Text>{Sum(GroupHeaderBand2,ds2.Cf_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text13>
            <Text7 Ref="28" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4,0,3.4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text7</Name>
              <Page isRef="5" />
              <Parent isRef="22" />
              <Text>{ds2.Ry_Name}</Text>
              <TextBrush>Black</TextBrush>
            </Text7>
            <Text9 Ref="29" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.4,0,3.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text9</Name>
              <Page isRef="5" />
              <Parent isRef="22" />
              <Text>{ds2.Cf_Date}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="30" type="CustomFormat" isKey="true">
                <StringFormat>yyyy-MM-dd HH:mm:ss</StringFormat>
              </TextFormat>
            </Text9>
            <Text22 Ref="31" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13.4,0,2.1,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text22</Name>
              <Page isRef="5" />
              <Parent isRef="22" />
              <Text>{ds2.Cf_Dj}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text22>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>GroupFooterBand2</Name>
          <Page isRef="5" />
          <Parent isRef="5" />
        </GroupFooterBand2>
        <GroupFooterBand1 Ref="32" type="GroupFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,8.2,19,0.5</ClientRectangle>
          <Components isList="true" count="2">
            <Text14 Ref="33" type="Text" isKey="true">
              <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>46ce84020d6040aebdc07b222eda39b4</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text14</Name>
              <Page isRef="5" />
              <Parent isRef="32" />
              <Text>合计</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text14>
            <Text15 Ref="34" type="Text" isKey="true">
              <Border>Top, Right, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>6,0,13,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>04b6e5b994e84c388a6f170bc96f1bf1</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text15</Name>
              <Page isRef="5" />
              <Parent isRef="32" />
              <Text>{Sum(GroupHeaderBand1,ds2.Cf_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text15>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>GroupFooterBand1</Name>
          <Page isRef="5" />
          <Parent isRef="5" />
        </GroupFooterBand1>
        <FooterBand1 Ref="35" type="FooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,9.5,19,0.6</ClientRectangle>
          <Components isList="true" count="2">
            <Text16 Ref="36" type="Text" isKey="true">
              <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>43e45d7c5ca54b8c9f546da3c8798359</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text16</Name>
              <Page isRef="5" />
              <Parent isRef="35" />
              <Text>总计</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text16>
            <Text18 Ref="37" type="Text" isKey="true">
              <Border>Top, Right, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>6,0,13,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>012ee16f331e4e14b2461696a84aa506</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text18</Name>
              <Page isRef="5" />
              <Parent isRef="35" />
              <Text>{Sum(DataBand1,ds2.Cf_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text18>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>FooterBand1</Name>
          <Page isRef="5" />
          <Parent isRef="5" />
        </FooterBand1>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>bcd794663d99417081ed58d02ec4725e</Guid>
      <Margins>1,1,1,1</Margins>
      <Name>Page1</Name>
      <PageHeight>29.7</PageHeight>
      <PageWidth>21</PageWidth>
      <PaperSize>A4</PaperSize>
      <Report isRef="0" />
      <Watermark Ref="38" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page1>
  </Pages>
  <PrinterSettings Ref="39" type="Stimulsoft.Report.Print.StiPrinterSettings" isKey="true" />
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>药房项目表</ReportAlias>
  <ReportChanged>12/27/2018 10:27:53 AM</ReportChanged>
  <ReportCreated>1/10/2012 4:54:46 PM</ReportCreated>
  <ReportFile>E:\ZTHis5\ZTHis5\Rpt\药房项目表(带明细).mrt</ReportFile>
  <ReportGuid>2d6911ef24ae4102ac5a8b0bd542f159</ReportGuid>
  <ReportName>药房项目表</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2011.2.1026</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class Report : Stimulsoft.Report.StiReport
    {
        public Report()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
        #endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>