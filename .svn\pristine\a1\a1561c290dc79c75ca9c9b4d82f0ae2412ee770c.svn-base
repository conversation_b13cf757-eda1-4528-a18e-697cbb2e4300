﻿Imports System.Resources
Imports System.Drawing

Public Class C_Resources
    Private Shared rm As ResourceManager
    Private Shared myAssembly As System.Reflection.Assembly

    Public Shared Function getimage(ByVal str As String) As Image
        myAssembly = System.Reflection.Assembly.Load("MyResources")
        rm = New ResourceManager("MyResources.Resource1", myAssembly)
        getimage = rm.GetObject(str)
    End Function

End Class
