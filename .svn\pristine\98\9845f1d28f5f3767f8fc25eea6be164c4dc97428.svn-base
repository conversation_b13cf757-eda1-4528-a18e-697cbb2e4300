﻿Imports System.Data.SqlClient

Public Class Zd_Yp15_Database

#Region "变量定义"
    Dim My_But<PERSON> As C_Button                                                                                       '按扭初始化
    Dim V_Jx_Code As String = ""                                                                                    '剂型编码    
    Dim My_Cc As New BaseClass.C_Cc()
    Dim V_Mx_Code As String

    Dim _Dt As DataTable
#End Region

#Region "传参"
    Dim <PERSON>ert As Boolean
    Dim Rrow As DataRow
    Dim Rrow1 As DataRow
    Dim RZbtb As DataTable
    Dim Rtdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Dim Rlb As C1.Win.C1Input.C1Label
    Dim Rds As DataSet
#End Region

    Public Sub New(ByVal tinsert As Boolean, ByVal trow As DataRow, ByVal trow1 As DataRow, ByVal tZbtb As DataTable, ByVal ttdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid, ByVal tlb As C1.Win.C1Input.C1Label, ByVal tds As DataSet)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        Rinsert = tinsert
        Rrow = trow
        Rrow1 = trow1
        RZbtb = tZbtb
        Rtdbgrid = ttdbgrid
        Rlb = tlb

        Rds = tds
        ' 在 InitializeComponent() 调用之后添加任何初始化。
    End Sub

    Private Sub Zd_Yp15_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load

        Call Form_Init()
        Call Combo_Init()
        L_Yp_Name.Text = Rrow("Yp_Name")
        If Rinsert = True Then Call Data_Clear() Else Call Data_Show()

    End Sub

#Region "窗体__事件"

    Private Sub Form_Init()
        Panel1.Height = 29

        Call TDBCommBo_Init()
        Call Load_Dict()
        Call Load_Ypgg()

        '按扭初始化
        My_Button = New C_Button(Comm1, Comm2)
        My_Button.Init_Button(Panel1.Left + Panel1.Width / 2, 4)
    End Sub

    Private Sub TDBCommBo_Init()
        With C1Combo2
            .ClearItems()
            .AllowSort = False

            .AutoDropDown = True

            .AutoCompletion = True
            .AutoSelect = True
            .AutoSize = False

            .ColumnHeaders = False
            .SuperBack = True
            .DropDownWidth = 200
            .ItemHeight = 16
            .MaxDropDownItems = 10
            .DataMode = C1.Win.C1List.DataModeEnum.AddItem
            .ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownCombo
            .Style.VerticalAlignment = C1.Win.C1List.AlignVertEnum.Bottom
            .DrawMode = DrawMode.OwnerDrawFixed

            .AddItemCols = 1
            .AddItemTitles("列名")
            .ExtendRightColumn = True
            With .Splits(0)
                .ColumnCaptionHeight = 20
                .DisplayColumns(0).Width = 10
                .Style.VerticalAlignment = C1.Win.C1List.AlignVertEnum.Bottom
                .SelectedStyle.BackColor = System.Drawing.Color.Teal
            End With

            .RowDivider.Style = C1.Win.C1List.LineStyleEnum.Single
            .DropdownPosition = C1.Win.C1List.DropdownPositionEnum.LeftUp

            .DisplayMember = "列名"
            .ValueMember = "列名"
            .Text = ""
            .MatchCol = C1.Win.C1List.MatchColEnum.DisplayMember
            .MatchEntry = C1.Win.C1List.MatchEntryEnum.Standard
        End With
    End Sub

    Private Sub Combo_Init()
        Dim My_Combo As New BaseClass.C_Combo2(C1Combo3, HisVar.HisVar.HisDBservice.GetMlYp3(Rrow("Yp_Code")).Tables("药品明细").DefaultView, "Mx_Gyzz", "Mx_Code", 720)
        With My_Combo
            .Init_TDBCombo()
            .Init_Colum("Mx_Gyzz", "批准文号", 92, "左")
            .Init_Colum("Mx_Cd", "生产厂家", 200, "左")
            .Init_Colum("Jx_Code", "剂型编码", 0, "左")
            .Init_Colum("Mx_Gg", "规格", 80, "左")
            .Init_Colum("Mx_Cgdw", "采购单位", 80, "左")
            .Init_Colum("Mx_Xsdw", "销售单位", 80, "左")
            .Init_Colum("Mx_Cfbl", "拆分比例", 80, "左")
            .Init_Colum("Mx_Code", "明细编码", 80, "左")
            .Init_Colum("Yp_Code", "药品编码", 0, "左")
            .Init_Colum("Mx_Memo", "", 0, "左")
            .Init_Colum("HzylMz_Code", "", 0, "左")
            .Init_Colum("BarCode", "", 0, "左")
            .Init_Colum("Yy_Mx_Code", "调整编码", 0, "左")
            .MaxDropDownItems(12)
            .SelectedIndex(-1)
        End With
        C1Combo3.DropdownPosition = C1.Win.C1List.DropdownPositionEnum.LeftUp
    End Sub

#End Region

#Region "数据初始化"

    Private Sub Load_Dict()     '药品剂型字典
        Dim My_Combo As BaseClass.C_Combo2

        If HisPara.PublicConfig.JbYp = "是" Then
            _Dt = HisVar.HisVar.HisDBservice.GetMlJbYpJx(Rrow("Yp_Code")).Tables("剂型")
            If _Dt.Rows.Count > 0 Then
                My_Combo = New BaseClass.C_Combo2(C1Combo1, HisVar.HisVar.HisDBservice.GetMlJbYpJx(Rrow("Yp_Code")).Tables("剂型").DefaultView, "Jx_Name", "Jx_Code", 386)
            Else
                My_Combo = New BaseClass.C_Combo2(C1Combo1, Rds.Tables("剂型").DefaultView, "Jx_Name", "Jx_Code", 386)
            End If
        Else
            My_Combo = New BaseClass.C_Combo2(C1Combo1, Rds.Tables("剂型").DefaultView, "Jx_Name", "Jx_Code", 386)
        End If

        With My_Combo
            .Init_TDBCombo()
            .Init_Colum("Jx_Jc", "简称", 92, "左")
            .Init_Colum("Jx_Name", "剂型名称", 200, "左")
            .Init_Colum("Jx_Code", "编码", 0, "左")
            .Init_Colum("Jx_Memo", "编码", 0, "左")
            .Init_Colum("Yy_Jx_Code", "编码", 0, "左")
            .MaxDropDownItems(12)
            .SelectedIndex(-1)
        End With
        With C1Combo1
            .AutoCompletion = False
            .AutoSelect = False
        End With
    End Sub

    Private Sub Load_Ypgg()     '药品规格
        Dim My_Reader As SqlDataReader = HisVar.HisVar.Sqldal.ExecuteReader("Select Mx_Gg From Zd_Ml_Yp3 Where Yp_Code='" & Rrow("Yp_Code") & "' Order By Mx_Gg")
        If My_Reader.HasRows = True Then
            While My_Reader.Read()
                C1Combo2.AddItem(My_Reader.Item(0).ToString)
            End While
        Else
            C1Combo2.Text = ""
        End If
        My_Reader.Close()

    End Sub

#End Region

#Region "数据__操作"

    Private Sub Data_Clear()
        V_Jx_Code = ""
        V_Mx_Code = ""
        C1Combo3.SelectedIndex = -1
        C1Combo3.Text = ""

        C1TextBox2.Text = ""
        C1TextBox3.Text = ""
        C1TextBox4.Text = ""
        C1TextBox5.Text = ""
        C1TextBox6.Text = ""
        C1Combo1.SelectedIndex = -1
        C1Combo2.Text = ""
        C1NumericEdit1.Value = 1

        With C1Combo1
            .ReadOnly = False
            .ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownCombo
            .TextAlign = HorizontalAlignment.Left
        End With
        With C1Combo2
            .ReadOnly = False
            .ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownCombo
            .TextAlign = HorizontalAlignment.Left
        End With

    End Sub

    Private Sub Data_Show()
        'Dim My_Row As DataRow = Zd_Yp11.Zd_Yp14.My_Cm.List(Zd_Yp11.Zd_Yp14.C1TrueDBGrid1.Row).Row
        With Rrow1
            V_Jx_Code = .Item("Jx_Code") & ""
            C1Combo3.Text = .Item("Mx_Gyzz") & ""

            V_Mx_Code = .Item("Mx_Code") & ""

            C1TextBox2.Text = .Item("Mx_Cd") & ""
            C1TextBox3.Text = .Item("Mx_Cgdw") & ""
            C1TextBox4.Text = .Item("Mx_Xsdw") & ""
            C1TextBox5.Text = .Item("BarCode") & ""
            C1TextBox6.Text = .Item("Mx_Memo") & ""
            C1Combo1.SelectedValue = V_Jx_Code
            C1Combo2.Text = .Item("Mx_Gg") & ""
            C1NumericEdit1.Value = Format(.Item("Mx_Cfbl"), "###,###,###.##")
        End With


        With C1Combo1
            .ReadOnly = False
            .ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownCombo
            .TextAlign = HorizontalAlignment.Left
        End With
        With C1Combo2
            .ReadOnly = False
            .ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownCombo
            .TextAlign = HorizontalAlignment.Left
        End With

    End Sub


#End Region

#Region "控件__动作"

    Private Sub Comm_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.Click, Comm2.Click
        Select Case sender.tag

            Case "保存"
                If C1NumericEdit1.Text & "" = "" Then
                    MsgBox("拆分比例不能为空!", MsgBoxStyle.Critical, "提示")
                    C1NumericEdit1.Select()
                    Exit Sub
                End If

                If C1NumericEdit1.Text & "" = 0 Then
                    MsgBox("拆分比例不能为0!", MsgBoxStyle.Critical, "提示")
                    C1NumericEdit1.Select()
                    Exit Sub
                End If

                If C1TextBox3.Text = C1TextBox4.Text And C1NumericEdit1.Text <> 1 Then
                    MsgBox("拆分比例与单位换算不符，请检查!", MsgBoxStyle.Critical, "提示")
                    C1NumericEdit1.Select()
                End If



                If Rinsert = True Then
                    Call Data_Add()
                Else
                    Call Data_Edit()
                End If

            Case "取消"
                Me.Close()

        End Select
    End Sub

    Private Sub Move_Click(ByVal sender As Object, ByVal e As C1.Win.C1Command.ClickEventArgs)
        If sender.text = "新增" Then                            '增加状态
            Call Data_Clear()
        Else
            With Rtdbgrid
                If .RowCount = 0 Then Exit Sub
                Select Case sender.text
                    Case "最前"
                        .MoveFirst()
                    Case "上移"
                        .MovePrevious()
                    Case "下移"
                        .MoveNext()
                    Case "最后"
                        .MoveLast()
                End Select
                Call Data_Show()
            End With
        End If
    End Sub

    Private Sub TextBox_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles C1TextBox2.KeyPress, C1TextBox3.KeyPress, C1TextBox4.KeyPress, C1TextBox5.KeyPress, C1TextBox6.KeyPress, C1NumericEdit1.KeyPress, C1Combo2.KeyPress
        If e.KeyChar <> Chr(Keys.Return) Then Exit Sub
        e.Handled = True
        System.Windows.Forms.SendKeys.Send("{Tab}")
    End Sub

#Region "Combo1动作"

    Private Sub C1Combo1_GotFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1Combo1.GotFocus
        InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages(0)
        Me.CancelButton = Nothing

        'If Zd_Yp14.V_Insert = False Then Exit Sub
        'Dim My_Reader As SqlDataReader = Sqldal.ExecuteReader("Select Jx_Code From Zd_Ml_Yp3 Where Yp_Code='09010001' Order By Jx_Code")   '剂型编码
        'If My_Reader.HasRows = True Then
        '    My_Reader.Read()
        '    C1Combo1.SelectedValue = My_Reader.Item(0)
        'Else
        '    C1Combo1.SelectedValue = -1
        'End If
        'My_Reader.Close()

    End Sub

    Private Sub C1Combo1_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1Combo1.Validated
        Me.CancelButton = Comm2
    End Sub

    Private Sub C1Combo1_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles C1Combo1.KeyUp

        If (e.KeyValue > 47 And e.KeyValue < 58) Or (e.KeyValue > 96 And e.KeyValue < 123) Or (e.KeyValue > 64 And e.KeyValue < 91) Or (e.KeyValue = 8) Or (e.KeyValue = 189) Or (e.KeyValue = 190) Then
            Dim s As String = C1Combo1.Text
            If C1Combo1.Text = "" Then
                C1Combo1.DataSource.RowFilter = ""
            Else
                C1Combo1.DataSource.RowFilter = "Jx_Jc like '*" & C1Combo1.Text.Replace("*", "[*]") & "*'"
            End If
            If (e.KeyValue = 8) Then
                C1Combo1.DroppedDown = False
                C1Combo1.DroppedDown = True
            End If

            C1Combo1.Text = s
            C1Combo1.SelectionStart = C1Combo1.Text.Length
            C1Combo1.SelectionLength = 0
        End If

    End Sub

    Private Sub C1Combo1_KeyDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles C1Combo1.KeyDown
        If e.KeyValue = 13 Then
            If C1Combo1.WillChangeToIndex < 1 Then
                If (CType(C1Combo1.DataSource, DataView).Count) = 0 Then
                    MsgBox("剂型: '" + Me.C1Combo1.Text + "' 不存在！", MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
                    System.Windows.Forms.SendKeys.Send("^{A}")
                    Exit Sub
                End If
                C1Combo1.SelectedIndex = -1
                C1Combo1.SelectedIndex = 0
            Else
                Dim index As Integer
                index = C1Combo1.WillChangeToIndex
                C1Combo1.SelectedIndex = -1
                C1Combo1.SelectedIndex = index
            End If
            e.Handled = True
            System.Windows.Forms.SendKeys.Send("{Tab}")
        End If
    End Sub

#End Region

#End Region

#Region "数据__编辑"

    Private Sub Data_Add()
        If HisPara.PublicConfig.JbYp = "是" Then
            If _Dt.Rows.Count > 0 Then
                If C1Combo1.Text = "" Then
                    MsgBox("剂型不能为空！", MsgBoxStyle.Information, "提示")
                    Exit Sub
                End If
            End If
        End If

        If C1Combo2.Text.Contains("|") Then
            MsgBox("请输入正确的规格形式！", MsgBoxStyle.Information, "提示")
            Exit Sub

        End If


        Dim My_NewRow As DataRow = RZbtb.NewRow
        Dim V_CenerCode As String = HisVar.HisVar.HisDBservice.UpDate_Yp3(Rrow("Yp_Code"), V_Mx_Code, C1Combo1.SelectedValue, C1TextBox2.Text & "", Trim(C1Combo2.Text) & "", Trim(C1TextBox3.Text) & "", Trim(C1TextBox4.Text) & "", C1NumericEdit1.Text, C1Combo3.Text & "", C1TextBox6.Text & "")

        If Microsoft.VisualBasic.Right(V_CenerCode, Len(V_CenerCode) - InStr(V_CenerCode, "New") - 2) = "" Then
            MsgBox("未能正确保存，请从新单击保存按钮进行保存！", MsgBoxStyle.Information, "提示")
            Exit Sub
        End If

        With My_NewRow
            .Item("Yp_Code") = Rrow("Yp_Code")
            .Item("Mx_Code") = Microsoft.VisualBasic.Right(V_CenerCode, Len(V_CenerCode) - InStr(V_CenerCode, "New") - 2)

            If C1Combo1.SelectedValue <> "" Then
                .Item("Jx_Code") = Trim(C1Combo1.SelectedValue & "")
                .Item("Jx_Name") = Trim(C1Combo1.Text & "")
            Else
                .Item("Jx_Code") = DBNull.Value
                .Item("Jx_Name") = ""
            End If
            .Item("Mx_Gg") = Trim(C1Combo2.Text & "")
            .Item("Mx_Gyzz") = C1Combo3.Text & ""
            .Item("Mx_Cd") = C1TextBox2.Text & ""
            .Item("Mx_Cgdw") = C1TextBox3.Text & ""
            .Item("Mx_Xsdw") = C1TextBox4.Text & ""
            .Item("BarCode") = C1TextBox5.Text & ""
            .Item("Mx_Memo") = C1TextBox6.Text & ""
            .Item("Mx_Cfbl") = C1NumericEdit1.Text & ""
        End With

        '数据保存

        Try
            RZbtb.Rows.Add(My_NewRow)
            Rtdbgrid.MoveLast()
            Rlb.Text = "∑=" + Rtdbgrid.Splits(0).Rows.Count.ToString
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            Exit Sub
        Finally
            C1Combo3.Select()
        End Try
        'End With

        '数据更新
        Try


            Dim Para(10) As SqlClient.SqlParameter
            Para(0) = New SqlParameter("@Yp_Code", SqlDbType.Char)
            Para(1) = New SqlParameter("@Mx_Code", SqlDbType.VarChar)
            Para(2) = New SqlParameter("@Jx_Code", SqlDbType.VarChar)
            Para(3) = New SqlParameter("@Mx_Gyzz", SqlDbType.VarChar)
            Para(4) = New SqlParameter("@Mx_Cd", SqlDbType.VarChar)
            Para(5) = New SqlParameter("@Mx_Gg", SqlDbType.VarChar)
            Para(6) = New SqlParameter("@Mx_Cgdw", SqlDbType.VarChar)
            Para(7) = New SqlParameter("@Mx_Xsdw", SqlDbType.VarChar)
            Para(8) = New SqlParameter("@Mx_Cfbl", SqlDbType.Decimal)
            Para(9) = New SqlParameter("@Mx_Memo", SqlDbType.VarChar)
            Para(10) = New SqlParameter("@BarCode", SqlDbType.VarChar)

            Para(0).Value = My_NewRow.Item("Yp_Code")
            Para(1).Value = My_NewRow.Item("Mx_Code")
            Para(2).Value = My_NewRow.Item("Jx_Code")
            Para(3).Value = My_NewRow.Item("Mx_Gyzz")
            Para(4).Value = My_NewRow.Item("Mx_Cd")
            Para(5).Value = My_NewRow.Item("Mx_Gg")
            Para(6).Value = My_NewRow.Item("Mx_Cgdw")
            Para(7).Value = My_NewRow.Item("Mx_Xsdw")
            Para(8).Value = My_NewRow.Item("Mx_Cfbl")
            Para(9).Value = My_NewRow.Item("Mx_Memo")
            Para(10).Value = My_NewRow.Item("BarCode")

            HisVar.HisVar.Sqldal.ExecuteSql("Insert Into Zd_Ml_Yp3 (Yp_Code,Mx_Code,Jx_Code,Mx_Gyzz,Mx_Cd,Mx_Gg,Mx_Cgdw,Mx_Xsdw,Mx_Cfbl,Mx_Memo,BarCode)Values(@Yp_Code,@Mx_Code,@Jx_Code,@Mx_Gyzz,@Mx_Cd,@Mx_Gg,@Mx_Cgdw,@Mx_Xsdw,@Mx_Cfbl,@Mx_Memo,@BarCode)", Para)

            My_NewRow.AcceptChanges()
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            C1Combo3.Select()
            Exit Sub
        Finally

        End Try

        '数据清空
        Call Data_Clear()

    End Sub

    Private Sub Data_Edit()

        Dim V_CenerCode As String = HisVar.HisVar.HisDBservice.UpDate_Yp3(Rrow("Yp_Code"), V_Mx_Code, C1Combo1.SelectedValue, C1TextBox2.Text & "", Trim(C1Combo2.Text) & "", Trim(C1TextBox3.Text) & "", Trim(C1TextBox4.Text) & "", C1NumericEdit1.Text, C1Combo3.Text & "", C1TextBox6.Text & "")
        Try
            With Rrow1
                .BeginEdit()

                If C1Combo1.SelectedValue <> "" Then
                    .Item("Jx_Code") = Trim(C1Combo1.SelectedValue & "")
                    .Item("Jx_Name") = Trim(C1Combo1.Text & "")
                Else
                    .Item("Jx_Code") = DBNull.Value
                    .Item("Jx_Name") = ""
                End If
                .Item("Mx_Gyzz") = C1Combo3.Text & ""
                .Item("Mx_Cd") = C1TextBox2.Text & ""
                .Item("Mx_Gg") = Trim(C1Combo2.Text & "")
                .Item("Mx_Cgdw") = C1TextBox3.Text & ""
                .Item("Mx_Xsdw") = C1TextBox4.Text & ""
                .Item("Mx_Cfbl") = C1NumericEdit1.Value & ""
                .Item("Mx_Memo") = C1TextBox6.Text & ""
                .Item("BarCode") = C1TextBox5.Text & ""

                .EndEdit()
            End With
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Critical, "提示:")
            Rrow1.CancelEdit()
            Exit Sub
        Finally
            C1Combo3.Select()
        End Try

        '数据更新

        Try

            Dim Para(9) As SqlClient.SqlParameter


            Para(0) = New SqlParameter("@Jx_Code", SqlDbType.VarChar)
            Para(1) = New SqlParameter("@Mx_Gyzz", SqlDbType.VarChar)
            Para(2) = New SqlParameter("@Mx_Cd", SqlDbType.VarChar)
            Para(3) = New SqlParameter("@Mx_Gg", SqlDbType.VarChar)
            Para(4) = New SqlParameter("@Mx_Cgdw", SqlDbType.VarChar)
            Para(5) = New SqlParameter("@Mx_Xsdw", SqlDbType.VarChar)
            Para(6) = New SqlParameter("@Mx_Cfbl", SqlDbType.Decimal)
            Para(7) = New SqlParameter("@Mx_Memo", SqlDbType.VarChar)
            Para(8) = New SqlParameter("@BarCode", SqlDbType.VarChar)
            Para(9) = New SqlParameter("@Old_Mx_Code", SqlDbType.VarChar)



            Para(0).Value = Rrow1.Item("Jx_Code")
            Para(1).Value = Rrow1.Item("Mx_Gyzz")
            Para(2).Value = Rrow1.Item("Mx_Cd")
            Para(3).Value = Rrow1.Item("Mx_Gg")
            Para(4).Value = Rrow1.Item("Mx_Cgdw")
            Para(5).Value = Rrow1.Item("Mx_Xsdw")
            Para(6).Value = Rrow1.Item("Mx_Cfbl")
            Para(7).Value = Rrow1.Item("Mx_Memo")
            Para(8).Value = Rrow1.Item("BarCode")
            Para(9).Value = Rrow1.Item("Mx_Code", DataRowVersion.Original)

            HisVar.HisVar.Sqldal.ExecuteSql("Update Zd_Ml_Yp3 Set Jx_Code=@Jx_Code,Mx_Gyzz=@Mx_Gyzz,Mx_Cd=@Mx_Cd,Mx_Gg=@Mx_Gg,Mx_Cgdw=@Mx_Cgdw,Mx_Xsdw=@Mx_Xsdw,Mx_Cfbl=@Mx_Cfbl,Mx_Memo=@Mx_Memo,BarCode=@BarCode Where Mx_Code=@Old_Mx_Code", Para)


            Rrow1.AcceptChanges()
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
        Finally

            C1Combo3.Select()
        End Try

    End Sub

#End Region

#Region "自定义按扭"

    Private Sub Comm_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles Comm1.KeyDown, Comm2.KeyDown
        If e.KeyCode = Keys.Enter Or e.KeyCode = Keys.Space Then My_Button.KeyDown(sender.tag)
    End Sub

    Private Sub Comm_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles Comm1.KeyUp, Comm2.KeyUp
        If e.KeyCode = Keys.Enter Or e.KeyCode = Keys.Space Then My_Button.KeyUp(sender.tag)
    End Sub

    Private Sub Comm_MouseEnter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.MouseEnter, Comm2.MouseEnter
        My_Button.MouseEnter(sender.tag)
    End Sub

    Private Sub Comm_MouseLeave(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.MouseLeave, Comm2.MouseLeave
        My_Button.MouseLeave(sender.tag)
    End Sub

    Private Sub Comm_MouseDown(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm1.MouseDown, Comm2.MouseDown
        My_Button.MouseDown(sender.tag)
    End Sub

    Private Sub Comm_MouseUp(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm1.MouseUp, Comm2.MouseUp
        My_Button.MouseUp(sender.tag)
    End Sub

#End Region

#Region "输入法设置"
    '中文
    Private Sub C1TextBox1_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1TextBox2.GotFocus, C1TextBox3.GotFocus, C1Combo2.GotFocus, C1TextBox4.GotFocus, C1TextBox6.GotFocus
        InputLanguage.CurrentInputLanguage = HisVar.HisVar.InputCode
    End Sub
    '英文
    Private Sub C1DateEdit1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1NumericEdit1.GotFocus, C1TextBox5.GotFocus, C1Combo3.GotFocus
        InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages(0)
    End Sub
#End Region

#Region "Combo3动作"

    Private Sub C1Combo3_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1Combo3.Validated
        Me.CancelButton = Comm2
    End Sub

    Private Sub C1Combo3_RowChange(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1Combo3.RowChange
        If C1Combo3.WillChangeToValue = "" Then
            C1TextBox2.Text = ""
            C1TextBox3.Text = ""
            C1TextBox4.Text = ""
            C1TextBox5.Text = ""
            C1TextBox6.Text = ""
            C1Combo1.SelectedIndex = -1
            C1Combo2.Text = ""
            C1NumericEdit1.Value = 1
        Else
            V_Mx_Code = Trim(C1Combo3.Columns("Mx_Code").Text)
            C1TextBox2.Text = Trim(C1Combo3.Columns("Mx_Cd").Text)
            C1TextBox3.Text = Trim(C1Combo3.Columns("Mx_Cgdw").Text)
            C1TextBox4.Text = Trim(C1Combo3.Columns("Mx_Xsdw").Text)
            C1Combo1.SelectedValue = Trim(C1Combo3.Columns("Jx_Code").Text)
            C1Combo2.Text = Trim(C1Combo3.Columns("Mx_Gg").Text)
            C1NumericEdit1.Value = Trim(C1Combo3.Columns("Mx_Cfbl").Text)
        End If
    End Sub

    Private Sub C1Combo3_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1Combo3.TextChanged
        If C1Combo3.Text = "" Then
            C1TextBox2.Text = ""
            C1TextBox3.Text = ""
            C1TextBox4.Text = ""
            C1TextBox5.Text = ""
            C1TextBox6.Text = ""
            C1Combo1.SelectedIndex = -1
            C1Combo2.Text = ""
            C1NumericEdit1.Value = 1
        End If
    End Sub

    Private Sub C1Combo3_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles C1Combo3.KeyPress
        Select Case e.KeyChar
            Case Chr(Keys.Enter)
                e.Handled = True
                C1TextBox2.Select()

            Case Chr(Keys.Escape)
                If C1Combo3.WillChangeToValue = "" Then                                 '客户简称不存在
                    If V_Mx_Code = "" Then
                        If C1Combo3.Text <> "" Then
                            C1Combo3.Text = ""
                            C1TextBox2.Text = ""
                            C1TextBox3.Text = ""
                            C1TextBox4.Text = ""
                            C1TextBox5.Text = ""
                            C1TextBox6.Text = ""
                            C1Combo1.SelectedIndex = -1
                            C1Combo2.Text = ""
                            C1NumericEdit1.Value = 1
                        Else
                            Call Comm_Click(Comm2, Nothing)         '调用取消按键
                        End If
                    Else
                        C1Combo3.SelectedValue = V_Mx_Code          '恢复到原来的状态
                    End If
                Else                                                '客户简称存在
                    If C1Combo3.WillChangeToValue = V_Mx_Code Then                      '客户简称没有发生变化    
                        Call Comm_Click(Comm2, Nothing)             '调用取消按键
                    Else
                        C1Combo3.SelectedValue = V_Mx_Code          '恢复到原来的状态
                    End If
                End If
        End Select
    End Sub

    Private Sub C1Combo3_Validating(ByVal sender As System.Object, ByVal e As System.ComponentModel.CancelEventArgs) Handles C1Combo3.Validating

        If C1Combo3.WillChangeToValue = "" Then
            If Rinsert = True Then
                V_Mx_Code = ""
            End If
        End If
    End Sub


#End Region

End Class