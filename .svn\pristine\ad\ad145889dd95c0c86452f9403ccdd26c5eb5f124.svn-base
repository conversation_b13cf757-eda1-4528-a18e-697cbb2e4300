﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="0" />
    <DataSources isList="true" count="1">
      <明细表 Ref="2" type="DataTableSource" isKey="true">
        <Alias>明细表</Alias>
        <Columns isList="true" count="4">
          <value>Cf_Sl,System.Decimal</value>
          <value>Cf_Money,System.Decimal</value>
          <value>Cf_Lb,System.String</value>
          <value>Bl_Code,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>明细表</Name>
        <NameInSource>明细表</NameInSource>
      </明细表>
    </DataSources>
    <Relations isList="true" count="0" />
    <Report isRef="0" />
    <Variables isList="true" count="44">
      <value>,姓名,姓名,System.String,,False,False</value>
      <value>,性别,性别,System.String,,False,False</value>
      <value>,付款方式,付款方式,System.String,,False,False</value>
      <value>,社保号码,社保号码,System.String,,False,False</value>
      <value>,业务流水号,业务流水号,System.String,,False,False</value>
      <value>,缴费日期,缴费日期,System.String,,False,False</value>
      <value>,合计大写,合计大写,System.String,,False,False</value>
      <value>,合计,合计,System.String,,False,False</value>
      <value>,医保统筹支付,医保统筹支付,System.String,,False,False</value>
      <value>,个人账户支付,个人账户支付,System.String,,False,False</value>
      <value>,其他医保支付,其他医保支付,System.String,,False,False</value>
      <value>,自费,自费,System.String,,False,False</value>
      <value>,医保外自费,医保外自费,System.String,,False,False</value>
      <value>,报销类别,报销类别,System.String,,False,False</value>
      <value>,个人自付,个人自付,System.String,,False,False</value>
      <value>,个人账户余额,个人账户余额,System.String,,False,False</value>
      <value>,统筹自付金额,统筹自付金额,System.String,,False,False</value>
      <value>,统筹累计支付,统筹累计支付,System.String,,False,False</value>
      <value>,医疗机构,医疗机构,System.String,,False,False</value>
      <value>,类别,类别,System.String,,False,False</value>
      <value>,科室,科室,System.String,,False,False</value>
      <value>,流水号,流水号,System.String,,False,False</value>
      <value>,住院号,住院号,System.String,,False,False</value>
      <value>,医保类型,医保类型,System.String,,False,False</value>
      <value>,预缴金额,预缴金额,System.String,,False,False</value>
      <value>,补缴金额,补缴金额,System.String,,False,False</value>
      <value>,退费金额,退费金额,System.String,,False,False</value>
      <value>,医院垫支,医院垫支,System.String,,False,False</value>
      <value>,住院天数,住院天数,System.String,,False,False</value>
      <value>,收款人,收款人,System.String,,False,False</value>
      <value>,打印时间,打印时间,System.String,,False,False</value>
      <value>,封顶线上自付,封顶线上自付,System.String,,False,False</value>
      <value>,乙类药品自付,乙类药品自付,System.String,,False,False</value>
      <value>,特检特治自付,特检特治自付,System.String,,False,False</value>
      <value>,起付额支付,起付额支付,System.String,,False,False</value>
      <value>,入院年,入院年,System.String,,False,False</value>
      <value>,入院月,入院月,System.String,,False,False</value>
      <value>,入院日,入院日,System.String,,False,False</value>
      <value>,出院年,出院年,System.String,,False,False</value>
      <value>,出院月,出院月,System.String,,False,False</value>
      <value>,出院日,出院日,System.String,,False,False</value>
      <value>,本次公务员补助支付,本次公务员补助支付,System.String,,False,False</value>
      <value>,本次大病支付,本次大病支付,System.String,,False,False</value>
      <value>,大病保险累计支付,大病保险累计支付,System.String,,False,False</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="3" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="4">
        <Text27 Ref="4" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>6.8,24.7,1.3,0.5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Microsoft Sans Serif,8.25,Regular,Point,False,134</Font>
          <Guid>f6b2f24201c9450bb6e8dd54b4181fda</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text27</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Text>金额</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Bottom</VertAlignment>
        </Text27>
        <ReportTitleBand1 Ref="5" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,16.5,2.6</ClientRectangle>
          <Components isList="true" count="34">
            <Text120 Ref="6" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.8,0.2,11.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text120</Name>
              <Page isRef="3" />
              <Parent isRef="5" />
              <TextBrush>Black</TextBrush>
            </Text120>
            <Text37 Ref="7" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.8,4.8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>3f6776d1eeae42ad8f7c82500526e3c0</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text37</Name>
              <Page isRef="3" />
              <Parent isRef="5" />
              <Text>{医疗机构}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text37>
            <Text57 Ref="8" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.8,0.8,0.8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>9e3a4bae60d0405da391ab89e9b97fd5</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text57</Name>
              <Page isRef="3" />
              <Parent isRef="5" />
              <Text>类型：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text57>
            <Text28 Ref="9" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>5.6,0.8,1.2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>8fe4039247ca4e10baa03faeb04f88e3</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text28</Name>
              <Page isRef="3" />
              <Parent isRef="5" />
              <Text>{类别}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text28>
            <Text42 Ref="10" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>6.8,0.8,0.8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>1e2c34dbda124fb781e4af94a0fc65fd</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text42</Name>
              <Page isRef="3" />
              <Parent isRef="5" />
              <Text>科室：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text42>
            <Text45 Ref="11" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7.5,0.8,2.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>157e4e4ffaf547c5879dce9605bc06c4</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text45</Name>
              <Page isRef="3" />
              <Parent isRef="5" />
              <Text>{科室}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text45>
            <Text47 Ref="12" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>10.1,0.8,2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>c7024decc8c743ec9e48cb0267be3cd1</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text47</Name>
              <Page isRef="3" />
              <Parent isRef="5" />
              <Text>流水号：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text47>
            <Text48 Ref="13" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>12.1,0.8,4.4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>c4df1a87ff2a4431849721a320e0d39f</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text48</Name>
              <Page isRef="3" />
              <Parent isRef="5" />
              <Text>{流水号}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text48>
            <Text76 Ref="14" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>14.1,1.3,2.4,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>bc478af00cf8454a9b4b972045e0fc0c</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text76</Name>
              <Page isRef="3" />
              <Parent isRef="5" />
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text76>
            <Text74 Ref="15" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.3,1.2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text74</Name>
              <Page isRef="3" />
              <Parent isRef="5" />
              <Text>住院号:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text74>
            <Text75 Ref="16" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.2,1.3,1.9,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>e2320836202b4b498417d13785bccb49</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text75</Name>
              <Page isRef="3" />
              <Parent isRef="5" />
              <Text>{住院号}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text75>
            <Text77 Ref="17" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>3.1,1.3,2.1,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>f1d9dbcc7f9e4f909b9a817a69d4d1a1</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text77</Name>
              <Page isRef="3" />
              <Parent isRef="5" />
              <Text>住院时间:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text77>
            <Text90 Ref="18" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>10.6,1.3,1.5,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>ece9c4d035bc42f481ea1e0c0ae9b41f</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text90</Name>
              <Page isRef="3" />
              <Parent isRef="5" />
              <Text>住院天数:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text90>
            <Text91 Ref="19" type="Text" isKey="true">
              <Border>None;Black;1;Solid;False;4;Transparent</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>11.7,1.3,1.6,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>81085eea8f3142acb9aeae50f7951d36</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text91</Name>
              <Page isRef="3" />
              <Parent isRef="5" />
              <Text>{住院天数}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text91>
            <Text8 Ref="20" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.7,0.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>829031bdf8e14ea1bae2cbc7f5fe7db6</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text8</Name>
              <Page isRef="3" />
              <Parent isRef="5" />
              <Text>姓名：
</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text8>
            <Text9 Ref="21" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0.7,1.7,3.3,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>75cc88a2e1a3443fa51013c4c49727c6</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text9</Name>
              <Page isRef="3" />
              <Parent isRef="5" />
              <Text>{姓名}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text9>
            <Text10 Ref="22" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4,1.7,1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>cb8db3c9d6be4f28845744d69da1b317</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text10</Name>
              <Page isRef="3" />
              <Parent isRef="5" />
              <Text>性别：
</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text10>
            <Text13 Ref="23" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>5,1.7,1.5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>4a15957ed78c4ca2b01dbd0d381f8396</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text13</Name>
              <Page isRef="3" />
              <Parent isRef="5" />
              <Text>{性别}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text13>
            <Text12 Ref="24" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>6.5,1.7,1.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>aa0393ef457341f1bd37a2adb91e31ab</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text12</Name>
              <Page isRef="3" />
              <Parent isRef="5" />
              <Text>医保类型：
</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text12>
            <Text16 Ref="25" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>10.3,1.7,2.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>410b909064bb40189e019e35b7a0e67f</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text16</Name>
              <Page isRef="3" />
              <Parent isRef="5" />
              <Text>社会保障号码</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text16>
            <Text17 Ref="26" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>12.9,1.7,3.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>a5304f06bfa64e7791b12d1843e1feea</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text17</Name>
              <Page isRef="3" />
              <Parent isRef="5" />
              <Text>{社保号码}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text17>
            <Text11 Ref="27" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>8.2,1.7,2.1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>1ffb683024fb459f99d35cb67832aa97</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text11</Name>
              <Page isRef="3" />
              <Parent isRef="5" />
              <Text>{医保类型}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text11>
            <Text19 Ref="28" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,2.3,5.4,0.2</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>b316bd4e58cc49ac8528ff5ce8f2b62f</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text19</Name>
              <Page isRef="3" />
              <Parent isRef="5" />
              <Text>       项目/规格</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text19>
            <Text21 Ref="29" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>5.4,2.3,1.3,0.2</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>450d5a41ef1a41d58e61ccae1deb9b16</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text21</Name>
              <Page isRef="3" />
              <Parent isRef="5" />
              <Text>金额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text21>
            <Text22 Ref="30" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>6.7,2.3,1.3,0.2</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>a54e892529434c7fb01b2c11beb89664</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text22</Name>
              <Page isRef="3" />
              <Parent isRef="5" />
              <Text>支付类型</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text22>
            <Text14 Ref="31" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>8,2.3,5.8,0.2</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>e20cb7f86a6749589e2e700542049f85</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text14</Name>
              <Page isRef="3" />
              <Parent isRef="5" />
              <Text>       项目/规格</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text14>
            <Text18 Ref="32" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>13.8,2.3,1.4,0.2</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>6a6810c1758e4c4aae051dbab27f9176</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text18</Name>
              <Page isRef="3" />
              <Parent isRef="5" />
              <Text>金额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text18>
            <Text94 Ref="33" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>15.2,2.3,1.3,0.2</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>def1c6d604614ed4b404e4fb4ef582ea</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text94</Name>
              <Page isRef="3" />
              <Parent isRef="5" />
              <Text>支付类型</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text94>
            <Text1 Ref="34" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>5,1.3,0.8,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text1</Name>
              <Page isRef="3" />
              <Parent isRef="5" />
              <Text>{入院年}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text1>
            <Text2 Ref="35" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>6,1.3,0.6,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text2</Name>
              <Page isRef="3" />
              <Parent isRef="5" />
              <Text>{入院月}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text2>
            <Text53 Ref="36" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>6.8,1.3,0.6,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text53</Name>
              <Page isRef="3" />
              <Parent isRef="5" />
              <Text>{入院日}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text53>
            <Text56 Ref="37" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>8,1.3,0.8,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text56</Name>
              <Page isRef="3" />
              <Parent isRef="5" />
              <Text>{出院年}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text56>
            <Text58 Ref="38" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>9,1.3,0.6,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text58</Name>
              <Page isRef="3" />
              <Parent isRef="5" />
              <Text>{出院月}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text58>
            <Text70 Ref="39" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>9.8,1.3,0.6,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text70</Name>
              <Page isRef="3" />
              <Parent isRef="5" />
              <Text>{出院日}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text70>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>ReportTitleBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </ReportTitleBand1>
        <DataBand1 Ref="40" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,3.8,16.5,0.5</ClientRectangle>
          <ColumnDirection>DownThenAcross</ColumnDirection>
          <Columns>2</Columns>
          <Components isList="true" count="3">
            <Text29 Ref="41" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>5.9,0,1.35,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>3b3a939e7b59422592930d597aecdf53</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text29</Name>
              <NullValue>   </NullValue>
              <Page isRef="3" />
              <Parent isRef="40" />
              <Text>{明细表.Cf_Money}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="42" type="CustomFormat" isKey="true">
                <StringFormat>0.00##</StringFormat>
              </TextFormat>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text29>
            <Text30 Ref="43" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7.25,0,0.95,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>47a3e0c9e6ff4fdf84a2b5a4a22b4792</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text30</Name>
              <Page isRef="3" />
              <Parent isRef="40" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text30>
            <Text5 Ref="44" type="Text" isKey="true">
              <Border>None;Black;1;Solid;False;4;Transparent</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,5.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>073ce9d0318c4441a9e00a1be3eee141</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text5</Name>
              <Page isRef="3" />
              <Parent isRef="40" />
              <Text>{明细表.Cf_Lb}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text5>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>明细表</DataSourceName>
          <Filters isList="true" count="0" />
          <Name>DataBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Sort isList="true" count="0" />
        </DataBand1>
        <ReportSummaryBand1 Ref="45" type="ReportSummaryBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,5.1,16.5,3.1</ClientRectangle>
          <Components isList="true" count="46">
            <Text51 Ref="46" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.6,16.5,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>125acb89e9fa4323ad98716f53a0cc80</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text51</Name>
              <Page isRef="3" />
              <Parent isRef="45" />
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text51>
            <Text41 Ref="47" type="Text" isKey="true">
              <Border>None;Black;1;Solid;False;4;Transparent</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,2.7,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>ca9ff5c8e609486d8c65e1bb9c0bdc65</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text41</Name>
              <Page isRef="3" />
              <Parent isRef="45" />
              <Text>合计（大写）：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text41>
            <Text43 Ref="48" type="Text" isKey="true">
              <Border>None;Black;1;Solid;False;4;Transparent</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.7,0,5.7,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>df7e1c21ecee46189f8b831a19e94f1a</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text43</Name>
              <Page isRef="3" />
              <Parent isRef="45" />
              <Text>{MoneyCn(Sum(明细表.Cf_Money))}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text43>
            <Text44 Ref="49" type="Text" isKey="true">
              <Border>None;Black;1;Solid;False;4;Transparent</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>8.4,0,0.6,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>ed8958f943cb4257811635fcd566c997</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text44</Name>
              <Page isRef="3" />
              <Parent isRef="45" />
              <Text>￥</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text44>
            <Text49 Ref="50" type="Text" isKey="true">
              <Border>None;Black;1;Solid;False;4;Transparent</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9,0,7.5,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>70b2dfa01af44df192242a5e6e3e6542</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text49</Name>
              <Page isRef="3" />
              <Parent isRef="45" />
              <Text>{Sum(明细表.Cf_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text49>
            <Text3 Ref="51" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0.4,0.4,1.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text3</Name>
              <Page isRef="3" />
              <Parent isRef="45" />
              <Text>  预缴金额：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text3>
            <Text4 Ref="52" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.6,0.4,1.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>4f3a2f20e6c14ffb8df4a9ac7c3ec167</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text4</Name>
              <Page isRef="3" />
              <Parent isRef="45" />
              <Text>补缴金额：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text4>
            <Text23 Ref="53" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.9,0.4,2.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text23</Name>
              <Page isRef="3" />
              <Parent isRef="45" />
              <Text>{预缴金额}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text23>
            <Text31 Ref="54" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>8.2,0.4,2.2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>add52a44b2d54ba49aa1101a92cf934c</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text31</Name>
              <Page isRef="3" />
              <Parent isRef="45" />
              <Text>退费金额：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text31>
            <Text32 Ref="55" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>6,0.4,2.2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>9c8c6a1cb0b2441b9738e01770b97352</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text32</Name>
              <Page isRef="3" />
              <Parent isRef="45" />
              <Text>{补缴金额}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text32>
            <Text34 Ref="56" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>10.2,0.4,2.2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>2b4fdcdffaf24e4d81a720d1bc0f8d0e</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text34</Name>
              <Page isRef="3" />
              <Parent isRef="45" />
              <Text>{退费金额}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text34>
            <Text35 Ref="57" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>12.4,0.4,2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>6209031a22b946df9968fbca4e17399b</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text35</Name>
              <Page isRef="3" />
              <Parent isRef="45" />
              <Text>医院垫支：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text35>
            <Text36 Ref="58" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>14.4,0.4,1.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>8ce052ab9895451090a0cdfd146eb360</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text36</Name>
              <Page isRef="3" />
              <Parent isRef="45" />
              <Text>{医院垫支}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text36>
            <Text24 Ref="59" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0.4,0.9,2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>9cce41c293854487bedd5ad104cc7309</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text24</Name>
              <Page isRef="3" />
              <Parent isRef="45" />
              <Text>医保统筹支付:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text24>
            <Text25 Ref="60" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.2,0.9,2.2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>20e04a2757d04b9ab559f672c3b892c5</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text25</Name>
              <Page isRef="3" />
              <Parent isRef="45" />
              <Text>{医保统筹支付}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text25>
            <Text46 Ref="61" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.4,0.9,2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>12cc6d71e1584120abcdaf9e22f553f6</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text46</Name>
              <Page isRef="3" />
              <Parent isRef="45" />
              <Text>个人账户支付:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text46>
            <Text59 Ref="62" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>6.2,0.9,2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>6f5fad546b5a4d5886b97fe319883e4d</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text59</Name>
              <Page isRef="3" />
              <Parent isRef="45" />
              <Text>{个人账户支付}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text59>
            <Text60 Ref="63" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>8.2,0.9,2.2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>5b319ab402ca4572b0bd0758a05ee89b</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text60</Name>
              <Page isRef="3" />
              <Parent isRef="45" />
              <Text>个人自付:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text60>
            <Text61 Ref="64" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>10.2,0.9,2.2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>730c8d1289544f29978e8f60b87b0bd7</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text61</Name>
              <Page isRef="3" />
              <Parent isRef="45" />
              <Text>{个人自付}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text61>
            <Text62 Ref="65" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>12.4,0.9,2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>73ae1c43918b464ab8d99b1ef249be53</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text62</Name>
              <Page isRef="3" />
              <Parent isRef="45" />
              <Text>个人自费:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text62>
            <Text63 Ref="66" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>14.4,0.9,1.9,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>b668f1c0dd6e49d8bbbf72afc08202be</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text63</Name>
              <Page isRef="3" />
              <Parent isRef="45" />
              <Text>{自费}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text63>
            <Text64 Ref="67" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0.4,1.3,2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>2af7983359734df296019202388d80a8</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text64</Name>
              <Page isRef="3" />
              <Parent isRef="45" />
              <Text>个人账户余额:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text64>
            <Text65 Ref="68" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.2,1.3,2.2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>268a7d7ff5eb4ac2861c1c6f6a95506b</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text65</Name>
              <Page isRef="3" />
              <Parent isRef="45" />
              <Text>{个人账户余额}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text65>
            <Text66 Ref="69" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0.4,1.7,2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>c0d7a009f059430cb11a8e0671c57bb3</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text66</Name>
              <Page isRef="3" />
              <Parent isRef="45" />
              <Text>统筹自付金额:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text66>
            <Text67 Ref="70" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.2,1.7,2.2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>efd323c1742948e8b25c14517eaa976d</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text67</Name>
              <Page isRef="3" />
              <Parent isRef="45" />
              <Text>{统筹自付金额}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text67>
            <Text68 Ref="71" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.4,1.3,2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>05c973ef52034d3c812087573ef667fe</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text68</Name>
              <Page isRef="3" />
              <Parent isRef="45" />
              <Text>统筹累计支付:
</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text68>
            <Text69 Ref="72" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>6.2,1.3,2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>dbac17d48bde4c89aa982e09dff859a7</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text69</Name>
              <Page isRef="3" />
              <Parent isRef="45" />
              <Text>{统筹累计支付}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text69>
            <Text38 Ref="73" type="Text" isKey="true">
              <Border>None;Black;1;Solid;False;4;Transparent</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,2.7,2.4,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>ae4ad689852648a78b314b07c5820bf9</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text38</Name>
              <Page isRef="3" />
              <Parent isRef="45" />
              <Text>收款单位（章）：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text38>
            <Text39 Ref="74" type="Text" isKey="true">
              <Border>None;Black;1;Solid;False;4;Transparent</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.4,2.7,4.8,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>93c166a4b51548c986dd9538fcd59036</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text39</Name>
              <Page isRef="3" />
              <Parent isRef="45" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text39>
            <Text40 Ref="75" type="Text" isKey="true">
              <Border>None;Black;1;Solid;False;4;Transparent</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.2,2.7,1.6,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>3773f711cce94575ac089318660e8939</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text40</Name>
              <Page isRef="3" />
              <Parent isRef="45" />
              <Text>收款人:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text40>
            <Text50 Ref="76" type="Text" isKey="true">
              <Border>None;Black;1;Solid;False;4;Transparent</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>8.8,2.7,3,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>f642494222454374a841fff12eb01fe3</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text50</Name>
              <Page isRef="3" />
              <Parent isRef="45" />
              <Text>{收款人}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text50>
            <Text52 Ref="77" type="Text" isKey="true">
              <Border>None;Black;1;Solid;False;4;Transparent</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>11.8,2.7,4.1,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>cf785cd2ef2c4b9d9a39437a1248dce7</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text52</Name>
              <Page isRef="3" />
              <Parent isRef="45" />
              <Text>{打印时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text52>
            <Text6 Ref="78" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>8.2,1.7,2.2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>25ee75d4943d453abc529a1daad4ff74</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text6</Name>
              <Page isRef="3" />
              <Parent isRef="45" />
              <Text>封顶线上自付:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text6>
            <Text7 Ref="79" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>10.2,1.7,2.2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text7</Name>
              <Page isRef="3" />
              <Parent isRef="45" />
              <Text>{封顶线上自付}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text7>
            <Text15 Ref="80" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.4,1.7,2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>e48e3f470aa840bf896a1c908aa03389</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text15</Name>
              <Page isRef="3" />
              <Parent isRef="45" />
              <Text>乙类药品自付:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text15>
            <Text20 Ref="81" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>6.2,1.7,2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text20</Name>
              <Page isRef="3" />
              <Parent isRef="45" />
              <Text>{乙类药品自付}</Text>
              <TextBrush>Black</TextBrush>
            </Text20>
            <Text26 Ref="82" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>8.2,1.3,2.2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>e864d8064d8149f2b3c2bb43244a94f9</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text26</Name>
              <Page isRef="3" />
              <Parent isRef="45" />
              <Text>特检特治自付:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text26>
            <Text33 Ref="83" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>10.2,1.3,2.2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text33</Name>
              <Page isRef="3" />
              <Parent isRef="45" />
              <Text>{特检特治自付}</Text>
              <TextBrush>Black</TextBrush>
            </Text33>
            <Text54 Ref="84" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>12.2,1.3,2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text54</Name>
              <Page isRef="3" />
              <Parent isRef="45" />
              <Text>起付额支付:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text54>
            <Text55 Ref="85" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>14.3,1.3,2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text55</Name>
              <Page isRef="3" />
              <Parent isRef="45" />
              <Text>{起付额支付}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text55>
            <Text71 Ref="86" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0.4,2.1,2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>a4c6e957a449496c8dc49ced4e747f39</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text71</Name>
              <Page isRef="3" />
              <Parent isRef="45" />
              <Text>公务员补助:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text71>
            <Text72 Ref="87" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.4,2.1,2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>6a6825e6f0db49b7a4dd07773122ac74</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text72</Name>
              <Page isRef="3" />
              <Parent isRef="45" />
              <Text>职工补充支付:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text72>
            <Text73 Ref="88" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>8.2,2.1,2.6,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>962f22ea13cb472aa623ed3b344d3847</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text73</Name>
              <Page isRef="3" />
              <Parent isRef="45" />
              <Text>职工补充支付累计:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text73>
            <Text78 Ref="89" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.4,2.1,2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>46f6f99da5704a71a3e41ce37b328c92</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text78</Name>
              <Page isRef="3" />
              <Parent isRef="45" />
              <Text>{本次公务员补助支付}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text78>
            <Text79 Ref="90" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>6.4,2.1,1.8,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9</Font>
              <Guid>904a64fce43b461598711f2a5575cbd6</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text79</Name>
              <Page isRef="3" />
              <Parent isRef="45" />
              <Text>{本次大病支付}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text79>
            <Text80 Ref="91" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>10.2,2.1,2.2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9</Font>
              <Guid>5abda1ae2ada4ae2afe4ee74bdf4aa18</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text80</Name>
              <Page isRef="3" />
              <Parent isRef="45" />
              <Text>{大病保险累计支付}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text80>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>ReportSummaryBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </ReportSummaryBand1>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>20c1288e9da6491e858185f416cfb06d</Guid>
      <Margins>1.3,1.2,0,0</Margins>
      <Name>Page1</Name>
      <PageHeight>10.13</PageHeight>
      <PageWidth>19</PageWidth>
      <Report isRef="0" />
      <Watermark Ref="92" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page1>
  </Pages>
  <PrinterSettings Ref="93" type="Stimulsoft.Report.Print.StiPrinterSettings" isKey="true" />
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>2013年河北省住院收费票据</ReportAlias>
  <ReportChanged>1/5/2018 2:00:22 PM</ReportChanged>
  <ReportCreated>12/21/2012 9:16:47 AM</ReportCreated>
  <ReportFile>C:\Users\<USER>\Desktop\His\发票修改\2013年河北省医保住院收费票据.mrt</ReportFile>
  <ReportGuid>70682d4590d149af8111cb4801068f1b</ReportGuid>
  <ReportName>2013年河北省住院收费票据</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2011.2.1026</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class Report : Stimulsoft.Report.StiReport
    {
        public Report()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
        #endregion StiReport Designer generated code - do not modify
		
		
		public	string NumberCn(decimal ANumber) 

		{

			const string cPointCn = "点十百千万十百千亿十百千";

			const string cNumberCn = "零一二三四五六七八九";

			string S = ANumber.ToString();

			if (S == "0") return "" + cPointCn[0];

			if (!S.Contains(".")) S += ".";

			int P = S.IndexOf(".");

			string Result = "";

    

			for (int i = 0; i &lt; S.Length; i++)

			{

				if (P == i)

				{

					Result = Result.Replace("零十零", "零");

					Result = Result.Replace("零百零", "零");

					Result = Result.Replace("零千零", "零");

					Result = Result.Replace("零十", "零");

					Result = Result.Replace("零百", "零");

					Result = Result.Replace("零千", "零");

					Result = Result.Replace("零万", "万");

					Result = Result.Replace("零亿", "亿");

					Result = Result.Replace("亿万", "亿");

					Result = Result.Replace("零点", "点");

				}

				else

				{

					if (P &gt; i)

						Result += "" + cNumberCn[S[i] - '0'] + cPointCn[P - i - 1];

					else Result += "" + cNumberCn[S[i] - '0'];

				}

			}

			if (Result.Substring(Result.Length - 1, 1) == "" + cPointCn[0])

				Result = Result.Remove(Result.Length - 1); // 一点-&gt; 一

    

			if (Result[0] == cPointCn[0])

				Result = cNumberCn[0] + Result; // 点三-&gt; 零点三

 

			if ((Result.Length &gt; 1) &amp;&amp; (Result[1] == cPointCn[1]) &amp;&amp; 

				(Result[0] == cNumberCn[1]))

				Result = Result.Remove(0, 1); // 一十三-&gt; 十三

			return Result;

		}

 

		public	string MoneyCn(decimal ANumber)

		{

			string V_Fs="";
			if (ANumber &lt; 0)
			{
				ANumber = -ANumber;
				V_Fs="负";
			}
			else
			{
				V_Fs = "";
			}

			
			if (ANumber == 0) return "零";

			string Result = NumberCn(Math.Truncate(ANumber * 100) / 100);

			Result = Result.Replace("一", "壹");

			Result = Result.Replace("二", "贰");

			Result = Result.Replace("三", "叁");

			Result = Result.Replace("四", "肆");

			Result = Result.Replace("五", "伍");

			Result = Result.Replace("六", "陆");

			Result = Result.Replace("七", "柒");

			Result = Result.Replace("八", "捌");

			Result = Result.Replace("九", "玖");

			Result = Result.Replace("九", "玖");

			Result = Result.Replace("十", "拾");

			Result = Result.Replace("百", "佰");

			Result = Result.Replace("千", "仟");

			if (Result.Contains("点"))

			{

				int P = Result.IndexOf("点");
				if (P + 3 &gt; Result.Length)
				{
					Result = Result+ "分";
				}
				else

				{
					Result = Result.Insert(P + 3, "分");
				}
				//	Result = Result.Insert(P + 3, "分");

				Result = Result.Insert(P + 2, "角");

				Result = Result.Replace("点", "圆");

				Result = Result.Replace("角分", "角");

				Result = Result.Replace("零分", "");

				Result = Result.Replace("零角", "");

				Result = Result.Replace("分角", "");

				if (Result.Substring(0, 2) == "零圆")

					Result = Result.Replace("零圆", "");

			} else Result += "圆整";

			Result =  V_Fs+Result;

			return Result;

		}
		
		
		
		
	}
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>