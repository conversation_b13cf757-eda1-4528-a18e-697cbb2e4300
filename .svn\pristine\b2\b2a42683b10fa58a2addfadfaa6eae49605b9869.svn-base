﻿Public Class Cx_Cqyz

    Private mBl_Code As String
    Public My_Dataset As New DataSet            '数据库
    Dim My_Table As New DataTable               '主表
    Public Zb_Cm As CurrencyManager             '同步指针

    Public Sub New(ByVal Bl_Code As String)

        ' 此调用是设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        mBl_Code = Bl_Code
    End Sub


    Private Sub Cx_Cqyz_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Form_Init()
        Show_Data()
    End Sub

    Private Sub Cx_Cqyz_FormClosed(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosedEventArgs) Handles Me.FormClosed
        Me.Dispose()
    End Sub

    Private Sub Form_Init()


        '初始化TDBGrid
        Dim My_Grid As New BaseClass.C_Grid(Me.C1TrueDBGrid1)
        With My_Grid
            .Init_Grid()
            .P_Dock(BaseClass.C_Grid.Dock.Fill)
            .Init_Column("选择", "IsSelect", 50, "中", "Check")
            .Init_Column("长期医嘱编码", "AutoCf_Code", 100, "左", "")
            .Init_Column("患者姓名", "Ry_Name", 70, "左", "")
            .Init_Column("患者类别", "Bxlb_Name", 65, "左", "")
            .Init_Column("处方科室", "Ks_Name", 70, "左", "")
            .Init_Column("处方医生", "Ys_Name", 70, "左", "")
            .Init_Column("处方金额", "Cf_Money", 60, "右", "###,##0.00")
            .Init_Column("开始时间", "Start_Date", 110, "中", "yyyy-MM-dd HH:mm")
            .Init_Column("结束时间", "End_Date", 110, "中", "yyyy-MM-dd HH:mm")
            .Init_Column("首次执行时间", "First_Execut_Time", 85, "中", "")
            .Init_Column("执行频率", "Everyday_Times", 60, "中", "0.##")
            .Init_Column("执行间隔", "Remind_interval", 60, "中", "0.##")
            .Init_Column("经手人", "Jsr_Name", 60, "中", "")
            .Init_Column("医嘱状态", "Auto_Zt", 60, "中", "")


            .AllDelete(False)
            .AllSort(False)
        End With





    End Sub

    Private Sub Show_Data()
        '主表数据
        Dim Str_Select As String = "select 'False' as IsSelect,Auto_Cf.Yy_Code,AutoCf_Code,Auto_Cf.Yf_Code,Auto_Cf.Bl_Code,Auto_Cf.Ks_Code,Auto_Cf.Ys_Code,Auto_Cf.Jsr_Code,Cf_Date,Cf_Time,Cf_Memo,Cf_Money,Cf_YpMoney,Start_Date,End_Date,Auto_Zt,Everyday_Times,Execut_Border_Time,Remind_interval,First_Execut_Time,Next_Execut_Time,Last_Execut_Time,Execute_Tomorrow,Ry_Name,Ry_Jc,Ks_Name,Ys_Name,Ry_Sex,Bc_Name,Jsr_Name,Bxlb_Name From Bl,Zd_Bxlb,Auto_Cf,Zd_Yyks,Zd_Yyys,Zd_YyBc,Zd_YyJsr Where Bl.Bxlb_Code=Zd_Bxlb.Bxlb_Code and Auto_Cf.Jsr_Code=Zd_YyJsr.Jsr_Code and Auto_Cf.ks_code=Zd_Yyks.Ks_Code and Auto_Cf.Ys_Code=Zd_Yyys.Ys_Code and Bl.Bl_Code=Auto_Cf.Bl_Code and Bl.Bc_Code=Zd_Yybc.Bc_Code and Ry_CyDate is null AND	Auto_Cf.Bl_Code  = '" & mBl_Code & "' "

        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, Str_Select, "主表", True)

        My_Table = My_Dataset.Tables("主表")
        My_Table.PrimaryKey = New DataColumn() {My_Table.Columns("AutoCf_Code")}
        My_Table.Columns("IsSelect").ReadOnly = False
        'TDBGrid初始化
        Zb_Cm = CType(BindingContext(My_Dataset, "主表"), CurrencyManager)
        C1TrueDBGrid1.SetDataBinding(My_Dataset, "主表", True)

    End Sub

    Private Sub Cancle_Btn_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Cancle_Btn.Click
        Me.Close()
    End Sub

    Private Sub Sure_Btn_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Sure_Btn.Click

        Dim Sql_where As String = ""

        For Each _row As DataRow In My_Table.Rows
            If _row("IsSelect") = True Then
                Sql_where = Sql_where & "'" & _row("AutoCf_Code") & "',"
            End If
        Next

        If Sql_where.Trim <> "" Then
            Sql_where = Sql_where.Substring(0, Sql_where.Length - 1)
        End If


        'Zy_Dzbl1.str_sql = Sql_where

        DialogResult = DialogResult.OK
    End Sub

    Private Sub c1Command1_Click(ByVal sender As System.Object, ByVal e As C1.Win.C1Command.ClickEventArgs) Handles c1Command1.Click
        For Each My_Row As DataRow In My_Table.Rows
            My_Row.Item("IsSelect") = True
        Next
    End Sub

    Private Sub c1Command2_Click(ByVal sender As System.Object, ByVal e As C1.Win.C1Command.ClickEventArgs) Handles c1Command2.Click
        For Each My_Row As DataRow In My_Table.Rows
            My_Row.Item("IsSelect") = False
        Next
    End Sub

    Private Sub C1TrueDBGrid1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1TrueDBGrid1.DoubleClick
        If C1TrueDBGrid1.RowCount = 0 Then Exit Sub
        If C1TrueDBGrid1.Col.ToString = 0 Then

            Dim _row As DataRow = Zb_Cm.List(C1TrueDBGrid1.Row).row

            If _row("IsSelect") = False Then
                _row("IsSelect") = True
            Else
                _row("IsSelect") = False
            End If

            Dim s As String = C1TrueDBGrid1.Columns(0).Caption

        End If
    End Sub
End Class
