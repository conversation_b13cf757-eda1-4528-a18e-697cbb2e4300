﻿/**  版本信息模板在安装目录下，可自行修改。
* D_Materials_Use_Out2.cs
*
* 功 能： N/A
* 类 名： D_Materials_Use_Out2
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2017/1/4 16:37:29   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
namespace DAL
{
    /// <summary>
    /// 数据访问类:D_Materials_Use_Out2
    /// </summary>
    public partial class D_Materials_Use_Out2
    {
        public D_Materials_Use_Out2()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(string M_Use_Detail_Code)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from Materials_Use_Out2");
            strSql.Append(" where M_Use_Detail_Code=@M_Use_Detail_Code ");
            SqlParameter[] parameters = {
                    new SqlParameter("@M_Use_Detail_Code", SqlDbType.Char,15)           };
            parameters[0].Value = M_Use_Detail_Code;

            return HisVar.HisVar.Sqldal.Exists(strSql.ToString(), parameters);
        }
        /// <summary>
        /// 获取当前时间 最大编码  yyMMdd + 5位流水号
        /// </summary>
        /// <returns></returns>

        public string MaxCode(string M_Use_Code)
        {

            string max = M_Use_Code + HisVar.HisVar.Sqldal.F_MaxCode("SELECT MAX(SUBSTRING(M_Use_Detail_Code,12,4)) FROM Materials_Use_Out2 WHERE M_Use_Code = '" + M_Use_Code + "'", 4);
            return max;
        }

        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ModelOld.M_Materials_Use_Out2 model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into Materials_Use_Out2(");
            strSql.Append("M_Use_Code,Materials_Code,MaterialsStock_Code,M_Use_Detail_Code,MaterialsLot,MaterialsExpiryDate,M_Use_Num,M_Use_WriteoffNo,M_Use_RealNo,M_Use_Price,M_Use_Money,M_Use_RealMoney,M_UseDetail_Memo)");
            strSql.Append(" values (");
            strSql.Append("@M_Use_Code,@Materials_Code,@MaterialsStock_Code,@M_Use_Detail_Code,@MaterialsLot,@MaterialsExpiryDate,@M_Use_Num,@M_Use_WriteoffNo,@M_Use_RealNo,@M_Use_Price,@M_Use_Money,@M_Use_RealMoney,@M_UseDetail_Memo)");
            SqlParameter[] parameters = {
                    new SqlParameter("@M_Use_Code", SqlDbType.Char,11),
                    new SqlParameter("@Materials_Code", SqlDbType.Char,10),
                    new SqlParameter("@MaterialsStock_Code", SqlDbType.Char,16),
                    new SqlParameter("@M_Use_Detail_Code", SqlDbType.Char,15),
                    new SqlParameter("@MaterialsLot", SqlDbType.VarChar,50),
                    new SqlParameter("@MaterialsExpiryDate", SqlDbType.SmallDateTime),
                    new SqlParameter("@M_Use_Num", SqlDbType.Decimal,5),
                    new SqlParameter("@M_Use_WriteoffNo", SqlDbType.Decimal,5),
                    new SqlParameter("@M_Use_RealNo", SqlDbType.Decimal,5),
                    new SqlParameter("@M_Use_Price", SqlDbType.Decimal,5),
                    new SqlParameter("@M_Use_Money", SqlDbType.Decimal,5),
                    new SqlParameter("@M_Use_RealMoney", SqlDbType.Decimal,5),
                    new SqlParameter("@M_UseDetail_Memo", SqlDbType.VarChar,200)};
            parameters[0].Value = model.M_Use_Code;
            parameters[1].Value = model.Materials_Code;
            parameters[2].Value = model.MaterialsStock_Code;
            parameters[3].Value = model.M_Use_Detail_Code;
            parameters[4].Value = model.MaterialsLot;
            parameters[5].Value = model.MaterialsExpiryDate;
            parameters[6].Value = model.M_Use_Num;
            parameters[7].Value = model.M_Use_WriteoffNo;
            parameters[8].Value = model.M_Use_RealNo;
            parameters[9].Value = model.M_Use_Price;
            parameters[10].Value = model.M_Use_Money;
            parameters[11].Value = model.M_Use_RealMoney;
            parameters[12].Value = model.M_UseDetail_Memo;

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ModelOld.M_Materials_Use_Out2 model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update Materials_Use_Out2 set ");
            strSql.Append("M_Use_Code=@M_Use_Code,");
            strSql.Append("Materials_Code=@Materials_Code,");
            strSql.Append("MaterialsStock_Code=@MaterialsStock_Code,");
            strSql.Append("MaterialsLot=@MaterialsLot,");
            strSql.Append("MaterialsExpiryDate=@MaterialsExpiryDate,");
            strSql.Append("M_Use_Num=@M_Use_Num,");
            strSql.Append("M_Use_WriteoffNo=@M_Use_WriteoffNo,");
            strSql.Append("M_Use_RealNo=@M_Use_RealNo,");
            strSql.Append("M_Use_Price=@M_Use_Price,");
            strSql.Append("M_Use_Money=@M_Use_Money,");
            strSql.Append("M_Use_RealMoney=@M_Use_RealMoney,");
            strSql.Append("M_UseDetail_Memo=@M_UseDetail_Memo");
            strSql.Append(" where M_Use_Detail_Code=@M_Use_Detail_Code ");
            SqlParameter[] parameters = {
                    new SqlParameter("@M_Use_Code", SqlDbType.Char,11),
                    new SqlParameter("@Materials_Code", SqlDbType.Char,10),
                    new SqlParameter("@MaterialsStock_Code", SqlDbType.Char,16),
                    new SqlParameter("@MaterialsLot", SqlDbType.VarChar,50),
                    new SqlParameter("@MaterialsExpiryDate", SqlDbType.SmallDateTime),
                    new SqlParameter("@M_Use_Num", SqlDbType.Decimal,5),
                    new SqlParameter("@M_Use_WriteoffNo", SqlDbType.Decimal,5),
                    new SqlParameter("@M_Use_RealNo", SqlDbType.Decimal,5),
                    new SqlParameter("@M_Use_Price", SqlDbType.Decimal,5),
                    new SqlParameter("@M_Use_Money", SqlDbType.Decimal,5),
                    new SqlParameter("@M_Use_RealMoney", SqlDbType.Decimal,5),
                    new SqlParameter("@M_UseDetail_Memo", SqlDbType.VarChar,200),
                    new SqlParameter("@M_Use_Detail_Code", SqlDbType.Char,15)};
            parameters[0].Value = model.M_Use_Code;
            parameters[1].Value = model.Materials_Code;
            parameters[2].Value = model.MaterialsStock_Code;
            parameters[3].Value = model.MaterialsLot;
            parameters[4].Value = model.MaterialsExpiryDate;
            parameters[5].Value = model.M_Use_Num;
            parameters[6].Value = model.M_Use_WriteoffNo;
            parameters[7].Value = model.M_Use_RealNo;
            parameters[8].Value = model.M_Use_Price;
            parameters[9].Value = model.M_Use_Money;
            parameters[10].Value = model.M_Use_RealMoney;
            parameters[11].Value = model.M_UseDetail_Memo;
            parameters[12].Value = model.M_Use_Detail_Code;

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(string M_Use_Detail_Code)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from Materials_Use_Out2 ");
            strSql.Append(" where M_Use_Detail_Code=@M_Use_Detail_Code ");
            SqlParameter[] parameters = {
                    new SqlParameter("@M_Use_Detail_Code", SqlDbType.Char,15)           };
            parameters[0].Value = M_Use_Detail_Code;

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string M_Use_Detail_Codelist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from Materials_Use_Out2 ");
            strSql.Append(" where M_Use_Detail_Code in (" + M_Use_Detail_Codelist + ")  ");
            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ModelOld.M_Materials_Use_Out2 GetModel(string M_Use_Detail_Code)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 M_Use_Code,Materials_Code,MaterialsStock_Code,M_Use_Detail_Code,MaterialsLot,MaterialsExpiryDate,M_Use_Num,M_Use_WriteoffNo,M_Use_RealNo,M_Use_Price,M_Use_Money,M_Use_RealMoney,M_UseDetail_Memo from Materials_Use_Out2 ");
            strSql.Append(" where M_Use_Detail_Code=@M_Use_Detail_Code ");
            SqlParameter[] parameters = {
                    new SqlParameter("@M_Use_Detail_Code", SqlDbType.Char,15)           };
            parameters[0].Value = M_Use_Detail_Code;

            ModelOld.M_Materials_Use_Out2 model = new ModelOld.M_Materials_Use_Out2();
            DataSet ds = HisVar.HisVar.Sqldal.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }
        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ModelOld.M_Materials_Use_Out2 GetModelByCondition(string strWhere)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 M_Use_Code,Materials_Code,MaterialsStock_Code,M_Use_Detail_Code,MaterialsLot,MaterialsExpiryDate,M_Use_Num,M_Use_WriteoffNo,M_Use_RealNo,M_Use_Price,M_Use_Money,M_Use_RealMoney,M_UseDetail_Memo from Materials_Use_Out2 ");

            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            ModelOld.M_Materials_Use_Out2 model = new ModelOld.M_Materials_Use_Out2();
            DataSet ds = HisVar.HisVar.Sqldal.Query(strSql.ToString());
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ModelOld.M_Materials_Use_Out2 DataRowToModel(DataRow row)
        {
            ModelOld.M_Materials_Use_Out2 model = new ModelOld.M_Materials_Use_Out2();
            if (row != null)
            {
                if (row["M_Use_Code"] != null)
                {
                    model.M_Use_Code = row["M_Use_Code"].ToString();
                }
                if (row["Materials_Code"] != null)
                {
                    model.Materials_Code = row["Materials_Code"].ToString();
                }
                if (row["MaterialsStock_Code"] != null)
                {
                    model.MaterialsStock_Code = row["MaterialsStock_Code"].ToString();
                }
                if (row["M_Use_Detail_Code"] != null)
                {
                    model.M_Use_Detail_Code = row["M_Use_Detail_Code"].ToString();
                }
                if (row["MaterialsLot"] != null)
                {
                    model.MaterialsLot = row["MaterialsLot"].ToString();
                }
                if (row["MaterialsExpiryDate"] != null && row["MaterialsExpiryDate"].ToString() != "")
                {
                    model.MaterialsExpiryDate = DateTime.Parse(row["MaterialsExpiryDate"].ToString());
                }
                if (row["M_Use_Num"] != null && row["M_Use_Num"].ToString() != "")
                {
                    model.M_Use_Num = decimal.Parse(row["M_Use_Num"].ToString());
                }
                if (row["M_Use_WriteoffNo"] != null && row["M_Use_WriteoffNo"].ToString() != "")
                {
                    model.M_Use_WriteoffNo = decimal.Parse(row["M_Use_WriteoffNo"].ToString());
                }
                if (row["M_Use_RealNo"] != null && row["M_Use_RealNo"].ToString() != "")
                {
                    model.M_Use_RealNo = decimal.Parse(row["M_Use_RealNo"].ToString());
                }
                if (row["M_Use_Price"] != null && row["M_Use_Price"].ToString() != "")
                {
                    model.M_Use_Price = decimal.Parse(row["M_Use_Price"].ToString());
                }
                if (row["M_Use_Money"] != null && row["M_Use_Money"].ToString() != "")
                {
                    model.M_Use_Money = decimal.Parse(row["M_Use_Money"].ToString());
                }
                if (row["M_Use_RealMoney"] != null && row["M_Use_RealMoney"].ToString() != "")
                {
                    model.M_Use_RealMoney = decimal.Parse(row["M_Use_RealMoney"].ToString());
                }
                if (row["M_UseDetail_Memo"] != null)
                {
                    model.M_UseDetail_Memo = row["M_UseDetail_Memo"].ToString();
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(" SELECT  M_Use_Code , ");
            strSql.Append("        Materials_Use_Out2.Materials_Code , ");
            strSql.Append("        Materials_Use_Out2.MaterialsStock_Code , ");
            strSql.Append("        M_Use_Detail_Code ,");
            strSql.Append("        Materials_Use_Out2.MaterialsLot , ");
            strSql.Append("        Materials_Use_Out2.MaterialsExpiryDate ,");
            strSql.Append("        M_Use_Num , ");
            strSql.Append("        Materials_Dict.Materials_Spec , ");
            strSql.Append("        M_Use_WriteoffNo , ");
            strSql.Append("        M_Use_RealNo , ");
            strSql.Append("        M_Use_Price , ");
            strSql.Append("        M_Use_Money , ");
            strSql.Append("        M_Use_RealMoney , ");
            strSql.Append("        M_UseDetail_Memo,");
            strSql.Append("        Materials_Dict.Materials_Name, ");
            strSql.Append("        Materials_Stock.MaterialsStore_Num ");
            strSql.Append(" FROM   Materials_Use_Out2");
            strSql.Append("        JOIN  Materials_Dict ON Materials_Dict.Materials_Code = Materials_Use_Out2.Materials_Code ");
            strSql.Append("        JOIN Materials_Stock ON Materials_Stock.MaterialsStock_Code = Materials_Use_Out2.MaterialsStock_Code ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

        public DataSet GetWriteOffList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(" SELECT  Materials_Use_Out2.M_Use_Code , ");
            strSql.Append("         Materials_Use_Out2.Materials_Code , ");
            strSql.Append("         Materials_Use_Out2.MaterialsStock_Code ,");
            strSql.Append("         Materials_Use_Out2.M_Use_Detail_Code , ");
            strSql.Append("         Materials_Use_Out2.MaterialsLot , ");
            strSql.Append("         Materials_Use_Out2.MaterialsExpiryDate , ");
            strSql.Append("         Materials_Use_Out2.M_Use_Num ,");
            strSql.Append("         Materials_Use_Out2.M_Use_WriteoffNo , ");
            strSql.Append("         Materials_Use_Out2.M_Use_RealNo , ");
            strSql.Append("         Materials_Use_Out2.M_Use_Price , ");
            strSql.Append("         Materials_Use_Out2.M_Use_Money , ");
            strSql.Append("         Materials_Use_Out2.M_Use_RealMoney , ");
            strSql.Append("         Materials_Use_Out2.M_UseDetail_Memo,");
            strSql.Append("         Materials_Dict.Materials_Spec,");
            strSql.Append("         Materials_Dict.Materials_Name,");
            strSql.Append("         b.M_Use_RealNo AS Can_RealWriteOffNo,");
            strSql.Append("         Materials_Stock.MaterialsStore_Num ");

            strSql.Append(" FROM    Materials_Use_Out2 ");
            strSql.Append("         JOIN  Materials_Dict ON Materials_Dict.Materials_Code = Materials_Use_Out2.Materials_Code JOIN Materials_Stock ON Materials_Stock.MaterialsStock_Code = Materials_Use_Out2.MaterialsStock_Code,");
            strSql.Append("		    Materials_Use_Out1,Materials_Use_Out2 b");
            strSql.Append(" WHERE Materials_Use_Out1.M_Use_Code = Materials_Use_Out2.M_Use_Code ");
            strSql.Append(" AND b.M_Use_Code = Materials_Use_Out1.WriteOff_Code ");
            strSql.Append(" AND Materials_Use_Out2.MaterialsStock_Code = b.MaterialsStock_Code  ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" AND " + strWhere);
            }
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" M_Use_Code,Materials_Code,MaterialsStock_Code,M_Use_Detail_Code,MaterialsLot,MaterialsExpiryDate,M_Use_Num,M_Use_WriteoffNo,M_Use_RealNo,M_Use_Price,M_Use_Money,M_Use_RealMoney,M_UseDetail_Memo ");
            strSql.Append(" FROM Materials_Use_Out2 ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM Materials_Use_Out2 ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.M_Use_Detail_Code desc");
            }
            strSql.Append(")AS Row, T.*  from Materials_Use_Out2 T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Materials_Use_Out2";
			parameters[1].Value = "M_Use_Detail_Code";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return HisVar.HisVar.Sqldal.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod
        #region  ExtensionMethod

        #endregion  ExtensionMethod
    }
}

