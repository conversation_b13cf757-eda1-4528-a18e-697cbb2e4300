﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="0" />
    <DataSources isList="true" count="1">
      <简化版 Ref="2" type="DataTableSource" isKey="true">
        <Alias>简化版</Alias>
        <Columns isList="true" count="16">
          <value>V_Order,System.String</value>
          <value>Ry_Name,System.String</value>
          <value>Ry_CyDate,System.String</value>
          <value>Ry_CyJsr,System.String</value>
          <value>Bl_Code,System.String</value>
          <value>Xx_Code,System.String</value>
          <value>Yp_Name,System.String</value>
          <value>Cf_Sl,System.Decimal</value>
          <value>Cf_Dj,System.Decimal</value>
          <value>Cf_Money,System.Decimal</value>
          <value>Cf_Lb,System.String</value>
          <value>Ks_Name,System.String</value>
          <value>Bc_Name,System.String</value>
          <value>Ry_BlCode,System.String</value>
          <value>Ry_ZyTs,System.Int32</value>
          <value>Bxlb_Name,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>简化版</Name>
        <NameInSource>简化版</NameInSource>
      </简化版>
    </DataSources>
    <Relations isList="true" count="0" />
    <Report isRef="0" />
    <Variables isList="true" count="3">
      <value>,查询时间,查询时间,System.String,,False,False</value>
      <value>,打印时间,打印时间,System.String,,False,False</value>
      <value>,经手人,经手人,System.String,,False,False</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="3" type="Page" isKey="true">
      <Border>None;Black;0;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="3">
        <GroupHeaderBand1 Ref="4" type="GroupHeaderBand" isKey="true">
          <Border>None;Black;0;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,7.35,3.8</ClientRectangle>
          <Components isList="true" count="13">
            <Text2 Ref="5" type="Text" isKey="true">
              <Border>Top, Left, Bottom;Black;0;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.8,1.8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>13003485beac474d9dc6dc2ca5debb8b</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text2</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>姓名:{简化版.Ry_Name}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text2>
            <Text9 Ref="6" type="Text" isKey="true">
              <Border>Top, Bottom;Black;0;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,2.3,2.2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>747786303c4943ffa1ecf27741302ecd</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text9</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>科别:{简化版.Ks_Name}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text9>
            <Text10 Ref="7" type="Text" isKey="true">
              <Border>Top, Bottom;Black;0;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.2,2.3,2.2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>141a6851ecd74dfcb6ef3b2554b2b2cd</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text10</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>床位号:{简化版.Bc_Name}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text10>
            <Text11 Ref="8" type="Text" isKey="true">
              <Border>Top, Right, Bottom;Black;0;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.1,2.8,4.2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>d4c63c686b094aafb2b6c30d1ee99ace</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text11</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>流水号:{简化版.Bl_Code}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text11>
            <Text5 Ref="9" type="Text" isKey="true">
              <Border>Top, Bottom;Black;0;Dash;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,3.3,4.1,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.5,Bold,Point,False,134</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text5</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>药品及项目名称</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text5>
            <Text18 Ref="10" type="Text" isKey="true">
              <Border>Top, Bottom;Black;0;Dash;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.1,3.3,0.8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.5,Bold,Point,False,134</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text18</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>数量</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Bottom</VertAlignment>
            </Text18>
            <Text20 Ref="11" type="Text" isKey="true">
              <Border>Top, Bottom;Black;0;Dash;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.9,3.3,1.1,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.5,Bold,Point,False,134</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text20</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>单价</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Bottom</VertAlignment>
            </Text20>
            <Text1 Ref="12" type="Text" isKey="true">
              <Border>None;Black;0;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1,7.3,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>黑体,15,Regular,Point,False,134</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text1</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>病人住院用药统计一览表</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text1>
            <Text7 Ref="13" type="Text" isKey="true">
              <Border>None;Black;0;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.8,1.8,5.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.5</Font>
              <Guid>7f0afcec41d149ab969d805da9d5f4ea</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text7</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>{查询时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text7>
            <Text29 Ref="14" type="Text" isKey="true">
              <Border>None;Black;0;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,7.3,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>e1204aea7a7c45c2879971a1c7adbc1d</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text29</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text29>
            <Text32 Ref="15" type="Text" isKey="true">
              <Border>Top, Left, Bottom;Black;1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,2.8,3.1,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>e190e15e68ef4443af0d8e10ce0928b3</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text32</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>患者类别:{简化版.Bxlb_Name}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text32>
            <Text33 Ref="16" type="Text" isKey="true">
              <Border>Top, Bottom;Black;1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.4,2.3,2.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,134</Font>
              <Guid>6b6fccc98f71490cb6ca48580c8e9f5f</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text33</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>住院号:{简化版.Ry_BlCode}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text33>
            <Text22 Ref="17" type="Text" isKey="true">
              <Border>Top, Bottom;Black;0;Dash;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6,3.3,1.3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.5,Bold,Point,False,134</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text22</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>金额</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Bottom</VertAlignment>
            </Text22>
          </Components>
          <Condition>{简化版.Bl_Code}</Condition>
          <Conditions isList="true" count="0" />
          <Name>GroupHeaderBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </GroupHeaderBand1>
        <DataBand1 Ref="18" type="DataBand" isKey="true">
          <Border>None;Black;0;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,5,7.35,0.6</ClientRectangle>
          <Columns>1</Columns>
          <Components isList="true" count="4">
            <Text4 Ref="19" type="Text" isKey="true">
              <Border>None;Black;0;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,4.1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.5</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>be530bd5b815479ba1817fa7ecabeaa2</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text4</Name>
              <NullValue>-</NullValue>
              <Page isRef="3" />
              <Parent isRef="18" />
              <Text>{简化版.Yp_Name}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text4>
            <Text17 Ref="20" type="Text" isKey="true">
              <Border>None;Black;0;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.1,0,0.8,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.5</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>c6950196dd284a74adbd6ff8028702d7</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text17</Name>
              <NullValue>-</NullValue>
              <Page isRef="3" />
              <Parent isRef="18" />
              <Text>{简化版.Cf_Sl}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="21" type="CustomFormat" isKey="true">
                <StringFormat>0.####</StringFormat>
              </TextFormat>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text17>
            <Text19 Ref="22" type="Text" isKey="true">
              <Border>None;Black;0;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.9,0,1.1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.5</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>e93948579c264a60b556da5114324b9f</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text19</Name>
              <NullValue>-</NullValue>
              <Page isRef="3" />
              <Parent isRef="18" />
              <Text>{简化版.Cf_Dj}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="23" type="CustomFormat" isKey="true">
                <StringFormat>0.00##</StringFormat>
              </TextFormat>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text19>
            <Text21 Ref="24" type="Text" isKey="true">
              <Border>None;Black;0;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6,0,1.3,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.5</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>86dfced89c5c47ae823c058f1af74977</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text21</Name>
              <NullValue>-</NullValue>
              <Page isRef="3" />
              <Parent isRef="18" />
              <Text>{简化版.Cf_Money}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="25" type="CustomFormat" isKey="true">
                <StringFormat>0.00##</StringFormat>
              </TextFormat>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text21>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>简化版</DataSourceName>
          <Filters isList="true" count="0" />
          <Name>DataBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Sort isList="true" count="4">
            <value>ASC</value>
            <value>V_Order</value>
            <value>DESC</value>
            <value>Yp_Name</value>
          </Sort>
        </DataBand1>
        <GroupFooterBand1 Ref="26" type="GroupFooterBand" isKey="true">
          <Border>None;Black;0;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>0,6.4,7.35,0.5</ClientRectangle>
          <Components isList="true" count="2">
            <Text25 Ref="27" type="Text" isKey="true">
              <Border>Top;Black;0;Dash;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>5.7,0,1.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.5</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text25</Name>
              <Page isRef="3" />
              <Parent isRef="26" />
              <Text>{Sum(DataBand1,简化版.Cf_Money)}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text25>
            <Text23 Ref="28" type="Text" isKey="true">
              <Border>Top;Black;0;Dash;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,5.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.5</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text23</Name>
              <Page isRef="3" />
              <Parent isRef="26" />
              <Text>合计:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text23>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>GroupFooterBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </GroupFooterBand1>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>e67f634a845f412dbae15ab107312b84</Guid>
      <Margins>0.2,0.2,0,0</Margins>
      <Name>Page1</Name>
      <PageHeight>8</PageHeight>
      <PageWidth>7.75</PageWidth>
      <Report isRef="0" />
      <Watermark Ref="29" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page1>
  </Pages>
  <PrinterSettings Ref="30" type="Stimulsoft.Report.Print.StiPrinterSettings" isKey="true" />
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>患者用药清单热敏打印版</ReportAlias>
  <ReportChanged>2/26/2015 4:59:45 PM</ReportChanged>
  <ReportCreated>1/9/2012 9:49:05 AM</ReportCreated>
  <ReportFile>E:\正在进行时\唐山\正在修改版\his2010\his2010\Rpt\患者用药清单热敏打印.mrt</ReportFile>
  <ReportGuid>fd553cf7b62643c6a449e2979deee619</ReportGuid>
  <ReportName>患者用药清单热敏打印版</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2011.2.1026</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class 患者用药清单标准版 : Stimulsoft.Report.StiReport
    {
        public 患者用药清单标准版()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
        #endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>