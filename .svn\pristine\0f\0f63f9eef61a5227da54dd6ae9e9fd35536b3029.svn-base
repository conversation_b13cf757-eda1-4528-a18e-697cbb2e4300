﻿Imports System.Data.SqlClient

Public Class Zd_MzFpHb1

#Region "变量初始化"
    Dim My_View As New DataView                 '数据视图

    Public My_Dataset As New DataSet
    Public My_Adapter As New SqlDataAdapter
    Public My_Table As New DataTable            '药品字典
    Public My_Cm As CurrencyManager             '同步指针
    Public My_Row As DataRow                    '当 前 行
    Public V_Insert As Boolean                  '增加记录
    Public V_FirstLoad As Boolean               '首次调入明细表
    Public V_Lb_Code As String                  '病区编码 

#End Region

    Private Sub Zd_Cw1_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Call Form_Init()
        Call Init_Data()
    End Sub

#Region "窗体初始化"

    Private Sub Form_Init()
        Panel1.BorderStyle = BorderStyle.None
        Panel1.Height = 25
        ToolBar1.Location = New Point(1, 1)
        T_Line1.Location = New Point(ToolBar1.Width + 2, 0)
  

        '初始化TDBGrid
        Dim My_Grid As New BaseClass.C_Grid(Me.C1TrueDBGrid1)
        With My_Grid
            .Init_Grid()
            .P_Dock(BaseClass.C_Grid.Dock.Fill)
            .Init_Column("编码", "Lb_Code", 65, "中", "")
            .Init_Column("名称", "Lb_Name", 110, "左", "")
            .Init_Column("简称", "Lb_Jc", 90, "左", "")
            .Init_Column("备注", "Lb_Memo", 220, "左", "")
            .Init_Column("发票类别合并", "", 1, "中", "")
        End With

        With C1TrueDBGrid1.Splits(0).DisplayColumns(4)
            .ButtonAlways = True
            .Button = True
            .ButtonText = True
        End With

        Dim V_Fs As String = ""
        With ComboBox1
            .Items.Add("每一大类打印一张收据")
            .Items.Add("每五大类打印一张收据")

            V_Fs = IIf(iniOperate.iniopreate.GetINI("打印选项", "收据打印方式", "", HisVar.HisVar.Parapath & "\Config.ini") & "" = "", "每一大类打印一张收据", iniOperate.iniopreate.GetINI("打印选项", "收据打印方式", "", HisVar.HisVar.Parapath & "\Config.ini"))
            If V_Fs = "每一大类打印一张收据" Then
                .SelectedIndex = 0
            Else
                .SelectedIndex = 1
            End If

        End With


     

    End Sub

    Private Sub Init_Data()
        Dim Str_Select As String = "select Lb_Code,Lb_Name,Lb_Jc,Lb_Memo,Yy_Code from Zd_MzFpHb1 where Yy_Code = '" & HisVar.HisVar.WsyCode & "' Order By Lb_Code"
        Dim Str_Update As String = "Update Zd_MzFpHb1 Set Lb_Code=@Lb_Code,Lb_Name=@Lb_Name,Lb_Jc=@Lb_Jc,Lb_Memo=@Lb_Memo Where Lb_Code=@Old_Lb_Code"
        Dim Str_Insert As String = "Insert Into Zd_MzFpHb1(Lb_Code,Lb_Name,Lb_Jc,Lb_Memo,Yy_Code)Values(@Lb_Code,@Lb_Name,@Lb_Jc,@Lb_Memo,@Yy_Code)"
        Dim Str_Delete As String = "Delete From Zd_MzFpHb1 WHERE Lb_Code=@Lb_Code"

        With My_Adapter
            .UpdateCommand = New SqlCommand(Str_Update, My_Cn)
            With .UpdateCommand.Parameters
                .Add("@Lb_Code", SqlDbType.Char, 7, "Lb_Code")
                .Add("@Lb_Name", SqlDbType.VarChar, 50, "Lb_Name")
                .Add("@Lb_Jc", SqlDbType.VarChar, 50, "Lb_Jc")
                .Add("@Lb_Memo", SqlDbType.VarChar, 50, "Lb_Memo")
                .Add("@Old_Lb_Code", SqlDbType.Char, 7, "Lb_Code")
            End With

            .InsertCommand = New SqlCommand(Str_Insert, My_Cn)
            With .InsertCommand.Parameters
                .Add("@Lb_Code", SqlDbType.Char, 7, "Lb_Code")
                .Add("@Lb_Name", SqlDbType.VarChar, 50, "Lb_Name")
                .Add("@Lb_Jc", SqlDbType.VarChar, 50, "Lb_Jc")
                .Add("@Lb_Memo", SqlDbType.VarChar, 50, "Lb_Memo")
                .Add("@Yy_Code", SqlDbType.VarChar, 50, "Yy_Code")

            End With

            .DeleteCommand = New SqlCommand(Str_Delete, My_Cn)
            With .DeleteCommand.Parameters
                .Add("@Lb_Code", SqlDbType.Char, 7, "Lb_Code")
            End With

            .SelectCommand = New SqlCommand(Str_Select, My_Cn)
            .Fill(My_Dataset, "类别名称")

            .AcceptChangesDuringFill = True
            .MissingSchemaAction = MissingSchemaAction.AddWithKey

        End With

        My_Table = My_Dataset.Tables("类别名称")
        My_Table.PrimaryKey = New DataColumn() {My_Table.Columns("Lb_Code")}

        With Me.C1TrueDBGrid1
            My_Cm = CType(BindingContext(My_Dataset, "类别名称"), CurrencyManager)
            .SetDataBinding(My_Dataset, "类别名称", True)


        End With

        My_View = My_Cm.List



    End Sub

    Private Sub Zd_Cw1_FormClosing(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosingEventArgs) Handles Me.FormClosing
       My_Dataset.Dispose()
    End Sub

#End Region

#Region "控件__动作"

    Private Sub Comm_Click(ByVal sender As Object, ByVal e As C1.Win.C1Command.ClickEventArgs) Handles Comm1.Click, Comm2.Click, Comm3.Click
        Select Case sender.text
            Case "增加"
                Call P_ShowData("增加")

            Case "删除"
                Beep()
                If C1TrueDBGrid1.Splits(0).Rows.Count = 0 Then Exit Sub
                Call P_Del_Data()

            Case "更新"
                My_Table.AcceptChanges()
                My_Adapter.Fill(My_Dataset, "类别名称")
                C1TrueDBGrid1.Select()
                C1TrueDBGrid1.MoveFirst()
        End Select

    End Sub
    Private Sub C1TrueDBGrid1_MouseUp(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles C1TrueDBGrid1.MouseUp
        If e.Button = MouseButtons.Right Then Call P_ShowData("DBGrid")
    End Sub

    Private Sub C1TrueDBGrid1_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles C1TrueDBGrid1.KeyDown
        Select Case e.KeyCode
            Case Keys.Return
                Call P_ShowData("DBGrid")
            Case Keys.Delete
                If Me.C1TrueDBGrid1.RowCount > 0 Then Call P_Del_Data()
            Case Keys.Insert
                Call P_ShowData("增加")
        End Select
    End Sub

    Private Sub C1TrueDBGrid1_ButtonClick(ByVal sender As System.Object, ByVal e As C1.Win.C1TrueDBGrid.ColEventArgs) Handles C1TrueDBGrid1.ButtonClick
        V_Lb_Code = C1TrueDBGrid1.Columns("Lb_Code").Text

        If Zd_MzFpHb3 Is Nothing Then                      '窗体没有调入
            V_FirstLoad = True
            Zd_MzFpHb3.Owner = Me
        End If
        Zd_MzFpHb3.ShowDialog()
    End Sub

    Dim m_Rc As New BaseClass.C_RowChange

    Private Sub C1TrueDBGrid1_RowColChange(ByVal sender As System.Object, ByVal e As C1.Win.C1TrueDBGrid.RowColChangeEventArgs) Handles C1TrueDBGrid1.RowColChange
        If (C1TrueDBGrid1.Row + 1) > C1TrueDBGrid1.RowCount Then

        Else
            My_Row = My_Cm.List(C1TrueDBGrid1.Row).Row
            m_Rc.ChangeRow(My_Row)
        End If
    End Sub

#End Region

#Region "自定义函数"

    Private Sub P_ShowData(ByVal V_Lb As String)

        If V_Lb = "增加" Then
            V_Insert = True
        Else
            If Me.C1TrueDBGrid1.RowCount > 0 Then V_Insert = False Else V_Insert = True
        End If

        If V_Insert = False Then
            My_Row = My_Cm.List(C1TrueDBGrid1.Row).Row
        End If

        Dim vform As New Zd_MzFpHb2(V_Insert, My_Row, My_Table, C1TrueDBGrid1, My_Adapter, m_Rc)
        If BaseFunc.BaseFunc.CheckOwnForm(Me, vform) = False Then
            vform.Owner = Me
            vform.Show()
        End If

    End Sub

    Private Sub P_Del_Data()
        Beep()
        If MsgBox("是否删除:类别=" + C1TrueDBGrid1.Columns("Lb_Name").Value + " ", MsgBoxStyle.Information + MsgBoxStyle.OkCancel + MsgBoxStyle.DefaultButton1, "提示:") = MsgBoxResult.Cancel Then Exit Sub
        Dim V_Flag As Integer
        My_Row = My_Cm.List(C1TrueDBGrid1.Row).Row
        V_Flag = HisVar.HisVar.Sqldal.GetSingle("Select Count(Lb_Code) From Zd_MzFpHb2 Where Lb_Code='" & My_Row.Item(0) & "'")
        If V_Flag > 0 Then
            MsgBox("该发票类别包含数据，不能删除！若删除，请先清空已合并内容！", MsgBoxStyle.Information, "提示")
            Exit Sub
        End If

        My_Adapter.DeleteCommand.Parameters(0).Value = My_Row.Item(0)
        Try
            Call P_Conn(True)
            My_Adapter.DeleteCommand.ExecuteNonQuery()
            C1TrueDBGrid1.Delete()
            My_Row.AcceptChanges()

        Catch ex As Exception
            Beep()
            MsgBox("该发票类别包含数据，不能删除！若删除，请先清空已合并内容！", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
        Finally
            Call P_Conn(False)
            C1TrueDBGrid1.Select()
        End Try
    End Sub

#End Region

    Private Sub ComboBox1_SelectedValueChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles ComboBox1.SelectedValueChanged
        iniOperate.iniopreate.WriteINI("打印选项", "收据打印方式", ComboBox1.Text, HisVar.HisVar.Parapath & "\Config.ini")
    End Sub
End Class
