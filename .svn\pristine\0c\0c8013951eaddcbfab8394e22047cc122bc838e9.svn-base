﻿/**  版本信息模板在安装目录下，可自行修改。
* M_Bl.cs
*
* 功 能： N/A
* 类 名： M_Bl
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/6/23 15:47:21   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace ModelOld
{
    /// <summary>
    /// M_Bl:实体类(属性说明自动提取数据库字段的描述信息)
    /// </summary>
    [Serializable]
    public partial class M_Bl
    {
        public M_Bl()
        { }
        #region Model
        private string _yy_code;
        private string _bl_code;
        private string _ks_code;
        private string _ys_code;
        private string _bc_code;
        private string _jsr_code;
        private string _jb_code;
        private string _bxlb_code;
        private string _ry_ylcode;
        private string _ry_name;
        private string _ry_jc;
        private string _ry_sex;
        private string _ry_sfzh;
        private DateTime? _ry_csdate;
        private string _ry_address;
        private string _ry_tele;
        private string _ry_memo;
        private string _ry_blcode;
        private DateTime? _ry_rydate;
        private DateTime? _ry_cydate;
        private int? _ry_zyts;
        private string _ry_cyjsr;
        private string _bl_ph;
        private decimal? _bl_m1 = 0M;
        private decimal? _bl_m2 = 0M;
        private decimal? _bl_m3 = 0M;
        private decimal? _bl_m4 = 0M;
        private decimal? _bl_m5 = 0M;
        private decimal? _bl_m6 = 0M;
        private decimal? _bl_m7 = 0M;
        private decimal? _bl_m8 = 0M;
        private decimal? _bl_m9 = 0M;
        private decimal? _bl_m10 = 0M;
        private decimal? _bl_m11 = 0M;
        private decimal? _bl_m12 = 0M;
        private decimal? _bl_m13 = 0M;
        private decimal? _bl_m14 = 0M;
        private decimal? _bl_m15 = 0M;
        private decimal? _bl_m16 = 0M;
        private decimal? _bl_m17 = 0M;
        private decimal? _bl_m_yj = 0M;
        private decimal? _bl_m_sk = 0M;
        private decimal? _bl_m_th = 0M;
        private bool _bl_dc;
        private string _jz_code;
        private string _cy_qr = "否";
        private string _jb_name;
        /// <summary>
        /// 
        /// </summary>
        public string Yy_code
        {
            set { _yy_code = value; }
            get { return _yy_code; }
        }
        /// <summary>
        /// 病历编码
        /// </summary>
        public string Bl_Code
        {
            set { _bl_code = value; }
            get { return _bl_code; }
        }
        /// <summary>
        /// 科室编码
        /// </summary>
        public string Ks_Code
        {
            set { _ks_code = value; }
            get { return _ks_code; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string Ys_Code
        {
            set { _ys_code = value; }
            get { return _ys_code; }
        }
        /// <summary>
        /// 床位编码
        /// </summary>
        public string Bc_Code
        {
            set { _bc_code = value; }
            get { return _bc_code; }
        }
        /// <summary>
        /// 经手人
        /// </summary>
        public string Jsr_Code
        {
            set { _jsr_code = value; }
            get { return _jsr_code; }
        }
        /// <summary>
        /// 疾病编码
        /// </summary>
        public string Jb_Code
        {
            set { _jb_code = value; }
            get { return _jb_code; }
        }
        /// <summary>
        /// 报销类别_编码
        /// </summary>
        public string Bxlb_Code
        {
            set { _bxlb_code = value; }
            get { return _bxlb_code; }
        }
        /// <summary>
        /// 医疗证号
        /// </summary>
        public string Ry_YlCode
        {
            set { _ry_ylcode = value; }
            get { return _ry_ylcode; }
        }
        /// <summary>
        /// 病人姓名
        /// </summary>
        public string Ry_Name
        {
            set { _ry_name = value; }
            get { return _ry_name; }
        }
        /// <summary>
        /// 病人简称
        /// </summary>
        public string Ry_Jc
        {
            set { _ry_jc = value; }
            get { return _ry_jc; }
        }
        /// <summary>
        /// 病人性别
        /// </summary>
        public string Ry_Sex
        {
            set { _ry_sex = value; }
            get { return _ry_sex; }
        }
        /// <summary>
        /// 病人身份证号
        /// </summary>
        public string Ry_Sfzh
        {
            set { _ry_sfzh = value; }
            get { return _ry_sfzh; }
        }
        /// <summary>
        /// 病人出生日期
        /// </summary>
        public DateTime? Ry_Csdate
        {
            set { _ry_csdate = value; }
            get { return _ry_csdate; }
        }
        /// <summary>
        /// 病人地址
        /// </summary>
        public string Ry_Address
        {
            set { _ry_address = value; }
            get { return _ry_address; }
        }
        /// <summary>
        /// 病人电话
        /// </summary>
        public string Ry_Tele
        {
            set { _ry_tele = value; }
            get { return _ry_tele; }
        }
        /// <summary>
        /// 备注
        /// </summary>
        public string Ry_Memo
        {
            set { _ry_memo = value; }
            get { return _ry_memo; }
        }
        /// <summary>
        /// 病例号
        /// </summary>
        public string Ry_BlCode
        {
            set { _ry_blcode = value; }
            get { return _ry_blcode; }
        }
        /// <summary>
        /// 入院日期
        /// </summary>
        public DateTime? Ry_RyDate
        {
            set { _ry_rydate = value; }
            get { return _ry_rydate; }
        }
        /// <summary>
        /// 出院日期
        /// </summary>
        public DateTime? Ry_CyDate
        {
            set { _ry_cydate = value; }
            get { return _ry_cydate; }
        }
        /// <summary>
        /// 住院天数
        /// </summary>
        public int? Ry_ZyTs
        {
            set { _ry_zyts = value; }
            get { return _ry_zyts; }
        }
        /// <summary>
        /// 出院经手人
        /// </summary>
        public string Ry_CyJsr
        {
            set { _ry_cyjsr = value; }
            get { return _ry_cyjsr; }
        }
        /// <summary>
        /// 病历票号
        /// </summary>
        public string Bl_Ph
        {
            set { _bl_ph = value; }
            get { return _bl_ph; }
        }
        /// <summary>
        /// 住院
        /// </summary>
        public decimal? Bl_M1
        {
            set { _bl_m1 = value; }
            get { return _bl_m1; }
        }
        /// <summary>
        /// 接生
        /// </summary>
        public decimal? Bl_M2
        {
            set { _bl_m2 = value; }
            get { return _bl_m2; }
        }
        /// <summary>
        /// 手术
        /// </summary>
        public decimal? Bl_M3
        {
            set { _bl_m3 = value; }
            get { return _bl_m3; }
        }
        /// <summary>
        /// 冷暖
        /// </summary>
        public decimal? Bl_M4
        {
            set { _bl_m4 = value; }
            get { return _bl_m4; }
        }
        /// <summary>
        /// 治疗
        /// </summary>
        public decimal? Bl_M5
        {
            set { _bl_m5 = value; }
            get { return _bl_m5; }
        }
        /// <summary>
        /// 婴儿
        /// </summary>
        public decimal? Bl_M6
        {
            set { _bl_m6 = value; }
            get { return _bl_m6; }
        }
        /// <summary>
        /// 血费
        /// </summary>
        public decimal? Bl_M7
        {
            set { _bl_m7 = value; }
            get { return _bl_m7; }
        }
        /// <summary>
        /// 检查
        /// </summary>
        public decimal? Bl_M8
        {
            set { _bl_m8 = value; }
            get { return _bl_m8; }
        }
        /// <summary>
        /// 医疗
        /// </summary>
        public decimal? Bl_M9
        {
            set { _bl_m9 = value; }
            get { return _bl_m9; }
        }
        /// <summary>
        /// 氧气
        /// </summary>
        public decimal? Bl_M10
        {
            set { _bl_m10 = value; }
            get { return _bl_m10; }
        }
        /// <summary>
        /// 化验
        /// </summary>
        public decimal? Bl_M11
        {
            set { _bl_m11 = value; }
            get { return _bl_m11; }
        }
        /// <summary>
        /// 放射
        /// </summary>
        public decimal? Bl_M12
        {
            set { _bl_m12 = value; }
            get { return _bl_m12; }
        }
        /// <summary>
        /// 监护
        /// </summary>
        public decimal? Bl_M13
        {
            set { _bl_m13 = value; }
            get { return _bl_m13; }
        }
        /// <summary>
        /// 中成药
        /// </summary>
        public decimal? Bl_M14
        {
            set { _bl_m14 = value; }
            get { return _bl_m14; }
        }
        /// <summary>
        /// 中草药
        /// </summary>
        public decimal? Bl_M15
        {
            set { _bl_m15 = value; }
            get { return _bl_m15; }
        }
        /// <summary>
        /// 西药
        /// </summary>
        public decimal? Bl_M16
        {
            set { _bl_m16 = value; }
            get { return _bl_m16; }
        }
        /// <summary>
        /// 卫材
        /// </summary>
        public decimal? Bl_M17
        {
            set { _bl_m17 = value; }
            get { return _bl_m17; }
        }
        /// <summary>
        /// 押金总额
        /// </summary>
        public decimal? Bl_M_Yj
        {
            set { _bl_m_yj = value; }
            get { return _bl_m_yj; }
        }
        /// <summary>
        /// 实收金额
        /// </summary>
        public decimal? Bl_M_Sk
        {
            set { _bl_m_sk = value; }
            get { return _bl_m_sk; }
        }
        /// <summary>
        /// 退回金额
        /// </summary>
        public decimal? Bl_M_Th
        {
            set { _bl_m_th = value; }
            get { return _bl_m_th; }
        }
        /// <summary>
        /// 入院上报导出标志位
        /// </summary>
        public bool Bl_Dc
        {
            set { _bl_dc = value; }
            get { return _bl_dc; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string Jz_Code
        {
            set { _jz_code = value; }
            get { return _jz_code; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string Cy_Qr
        {
            set { _cy_qr = value; }
            get { return _cy_qr; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string Jb_Name
        {
            set { _jb_name = value; }
            get { return _jb_name; }
        }
        #endregion Model

    }
}

