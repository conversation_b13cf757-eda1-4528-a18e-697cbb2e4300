﻿Imports System.Data.SqlClient
Imports Stimulsoft.Report

Public Class Xs_Zy_Jz
    Dim V_Date As Date
    Dim V_Time As Date
    Public V_Jz_Code As String
    Dim My_Reader As SqlClient.SqlDataReader
    Dim My_Reader1 As SqlClient.SqlDataReader
    Dim My_Reader2 As SqlClient.SqlDataReader
    Dim My_Dataset As New DataSet
    Dim Zh_Min_date As Object
    Dim Zh_Max_date As Object
    Dim V_Str As String

    Dim V_Sq As Decimal = 0 '收取金额
    Dim V_Th As Decimal = 0 '退回金额
    Dim V_Sj As Decimal = 0 '上缴金额
    Dim V_Sz As Decimal = 0 '上账金额
    Dim V_Yp As Decimal = 0 '药品金额
    Dim V_Xm As Decimal = 0 '项目金额
    Dim V_Rs As Decimal = 0 '出院人数
    Dim V_Cy As Decimal = 0 '出院总额
    Dim V_Zh As Decimal = 0 '病人召回产生的金额
    Dim Ch_Th As Decimal = 0

    Private Sub V_Js()


        'V_Jz_Code = "100112072001"
        V_Date = Format(Now, "yyyy-MM-dd")
        V_Time = Format(Now, "HH:mm:ss")
        V_Jz_Code = F_MaxCode(Format(Now.Date, "yyMMdd"))
        Dim arr As New ArrayList
        arr.Add("Insert Into Bl_Jz(Yy_Code,Jz_Code,Jz_Date,Jz_Time,Jsr_Code) Values('" & HisVar.HisVar.WsyCode & "','" & V_Jz_Code & "','" & V_Date & "','" & V_Time & "','" & HisVar.HisVar.JsrCode & "')")
        arr.Add("Update Bl_Jf Set Jz_Code='" & V_Jz_Code & "' Where  Jf_Date<='" & V_Date + " " + V_Time & "' and Isnull(Jz_Code,'')='' And Jsr_Code='" & HisVar.HisVar.JsrCode & "'")
        arr.Add("Update Bl Set Jz_Code='" & V_Jz_Code & "' Where  Isnull(Jz_Code,'')='' And Ry_CyJsr='" & HisVar.HisVar.JsrCode & "' And Ry_CyDate<='" & V_Date + " " + V_Time & "' And Ry_CyDate Is Not null")

        If ZTHisPara.PublicConfig.EnableZyCfHz = True Then
            arr.Add("Update Bl_Cf Set Jz_Code='" & V_Jz_Code & "' Where  Cf_Date+Cf_Time<='" & V_Date + " " + V_Time & "' and Isnull(Jz_Code,'')=''and Cf_Qr='是'")
        Else
            arr.Add("Update Bl_Cf Set Jz_Code='" & V_Jz_Code & "' Where  Cf_Date+Cf_Time<='" & V_Date + " " + V_Time & "' and Isnull(Jz_Code,'')=''and Cf_Qr='是'And Jsr_Code='" & HisVar.HisVar.JsrCode & "'")
        End If
        arr.Add("Update Bl_Zh Set Jz_Code='" & V_Jz_Code & "' Where  Cy_Date+Cy_Time<='" & V_Date + " " + V_Time & "' and Isnull(Jz_Code,'')=''And Jsr_Code='" & HisVar.HisVar.JsrCode & "'")
        HisVar.HisVar.Sqldal.ExecuteSqlTran(arr)

        '药品金额
        My_Reader = HisVar.HisVar.Sqldal.ExecuteReader("Select Isnull(Sum(Bl_CfYp.Cf_Money),0) As Cf_Money From Bl_CfYp,Bl_Cf Where  Bl_CfYp.Cf_Code=Bl_Cf.Cf_Code And Jz_Code='" & V_Jz_Code & "'")
        My_Reader.Read()
        V_Yp = My_Reader.Item(0)
        Call P_Conn(False)
        My_Reader.Close()
        '项目金额
        My_Reader = HisVar.HisVar.Sqldal.ExecuteReader("Select Isnull(Sum(Bl_CfXm.Cf_Money),0) As Cf_Money From Bl_CfXm,Bl_Cf Where  Bl_CfXm.Cf_Code=Bl_Cf.Cf_Code And Jz_Code='" & V_Jz_Code & "'")
        My_Reader.Read()
        V_Xm = My_Reader.Item(0)
        Call P_Conn(False)
        My_Reader.Close()
        '上账金额=药品金额+项目金额
        V_Sz = V_Yp + V_Xm

        '出院药品金额
        V_Cy = 0
        My_Reader = HisVar.HisVar.Sqldal.ExecuteReader("Select Isnull(Sum(Bl_CfYp.Cf_Money),0) As Cf_Money From Bl_CfYp,Bl_Cf,Bl Where Bl_Cf.Bl_Code=Bl.Bl_Code  and Bl_CfYp.Cf_Code=Bl_Cf.Cf_Code And Bl.Jz_Code='" & V_Jz_Code & "'")
        My_Reader.Read()
        V_Cy = My_Reader.Item(0)
        Call P_Conn(False)
        My_Reader.Close()
        '出院项目金额
        My_Reader = HisVar.HisVar.Sqldal.ExecuteReader("Select Isnull(Sum(Bl_CfXm.Cf_Money),0) As Cf_Money From Bl_CfXm,Bl_Cf,Bl Where Bl_Cf.Bl_Code=Bl.Bl_Code  and Bl_CfXm.Cf_Code=Bl_Cf.Cf_Code And Bl.Jz_Code='" & V_Jz_Code & "'")
        My_Reader.Read()
        V_Cy = V_Cy + My_Reader.Item(0)
        Call P_Conn(False)
        My_Reader.Close()

        ''发生过召回，且患者为不同操作员先后出院
        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "select distinct bl_zh.Bl_Code from bl_zh,bl where bl_zh.bl_code=Bl.bl_Code and Bl_Zh.Jz_Code<>Bl.Jz_Code and Bl_zh. Jz_code='" & V_Jz_Code & "' and Cy_Lb='出院'", "召回结账", True)
        V_Str = ""
        Dim Zh_Row As DataRow
        For Each Zh_Row In My_Dataset.Tables("召回结账").Rows

            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "select top 1 Cy_Id,cy_date+cy_time as Zh_Cy_Date,Cy_Lb from Bl_Zh where bl_Code='" & Zh_Row.Item("Bl_Code") & "' and  Jz_code='" & V_Jz_Code & "' order by cy_id asc", "最小时间", True)
            If My_Dataset.Tables("最小时间").Rows(0).Item("cy_lb") = "召回" Then
                Zh_Min_date = My_Dataset.Tables("最小时间").Rows(0).Item("Zh_Cy_Date")
            Else
                Zh_Min_date = HisVar.HisVar.Sqldal.GetSingle("select top 1 cy_date+cy_time as Zh_Cy_Date from bl_zh where bl_code='" & Zh_Row.Item("Bl_Code") & "' and Cy_Lb='召回' and cy_id<'" & My_Dataset.Tables("最小时间").Rows(0).Item("Cy_Id") & "' order by cy_id desc")
            End If
            Zh_Max_date = HisVar.HisVar.Sqldal.GetSingle("select Top 1 cy_date+cy_time as Zh_Cy_Date from Bl_Zh where bl_Code='" & Zh_Row.Item("Bl_Code") & "' and  Jz_code='" & V_Jz_Code & "' and Cy_Lb='出院' order by Cy_Id desc")

            V_Str = V_Str & " or (Bl_Cf.Bl_Code ='" & Zh_Row.Item("Bl_Code") & "' and convert(varchar(10),Cf_Date,126)+' '+Cf_Time<='" & Format(Zh_Max_date, "yyyy-MM-dd HH:mm:ss") & "' And convert(varchar(10),Cf_Date,126)+' '+Cf_Time>='" & Format(Zh_Min_date, "yyyy-MM-dd HH:mm:ss") & "' and Cf_Qr='是' )"

            '出院药品金额
            My_Reader = HisVar.HisVar.Sqldal.ExecuteReader("Select Isnull(Sum(Bl_CfYp.Cf_Money),0) As Cf_Money From Bl_CfYp,Bl_Cf,Bl Where Bl_Cf.Bl_Code=Bl.Bl_Code  and Bl_CfYp.Cf_Code=Bl_Cf.Cf_Code And Bl.Bl_Code='" & Zh_Row.Item("Bl_Code") & "' and convert(varchar(10),Cf_Date,126)+' '+Cf_Time<='" & Format(Zh_Max_date, "yyyy-MM-dd HH:mm:ss") & "' And convert(varchar(10),Cf_Date,126)+' '+Cf_Time>='" & Format(Zh_Min_date, "yyyy-MM-dd HH:mm:ss") & "'  and Cf_Qr='是'")
            My_Reader.Read()
            V_Cy = V_Cy + My_Reader.Item(0)
            Call P_Conn(False)
            My_Reader.Close()
            '出院项目金额
            My_Reader = HisVar.HisVar.Sqldal.ExecuteReader("Select Isnull(Sum(Bl_CfXm.Cf_Money),0) As Cf_Money From Bl_CfXm,Bl_Cf,Bl Where Bl_Cf.Bl_Code=Bl.Bl_Code  and Bl_CfXm.Cf_Code=Bl_Cf.Cf_Code And Bl.Bl_Code='" & Zh_Row.Item("Bl_Code") & "' and convert(varchar(10),Cf_Date,126)+' '+Cf_Time<='" & Format(Zh_Max_date, "yyyy-MM-dd HH:mm:ss") & "' And convert(varchar(10),Cf_Date,126)+' '+Cf_Time>='" & Format(Zh_Min_date, "yyyy-MM-dd HH:mm:ss") & "' and Cf_Qr='是'")
            My_Reader.Read()
            V_Cy = V_Cy + My_Reader.Item(0)
            Call P_Conn(False)
            My_Reader.Close()
        Next


        '医院收取押金
        My_Reader = HisVar.HisVar.Sqldal.ExecuteReader("Select Isnull(Sum(Jf_Money),0) Zy_Yj From Bl_Jf Where  Jz_Code='" & V_Jz_Code & "' ")
        My_Reader.Read()
        V_Sq = My_Reader.Item(0)
        Call P_Conn(False)
        My_Reader.Close()

        '改  退回金额=实际退给病人押金+(召回后再次出院所退押金-上次出院所退押金)
        My_Reader = HisVar.HisVar.Sqldal.ExecuteReader("Select Isnull(Sum(Bl_M_Th),0) Zy_Th From Bl Where  Jz_Code='" & V_Jz_Code & "' ")
        My_Reader.Read()
        V_Th = My_Reader.Item(0)
        Call P_Conn(False)
        My_Reader.Close()


        Dim T_Jz_Code1 As String = "1"
        Dim T_Jz_Code2 As String = "2"
        Dim T_Bl_Code As String = "3"

        Dim My_Row As DataRow
        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "select Bl_Zh.Jz_Code AS Jz_Code1,Isnull(Bl.Jz_Code,'空') AS Jz_Code2,Bl_Zh.Bl_Code from Bl_Zh,Bl Where Bl_Zh.Bl_Code=Bl.Bl_Code And Cy_Id IN (select Max(Cy_Id) From Bl_Zh Where Cy_Lb='出院' And Jz_Code='" & V_Jz_Code & "' Group By Bl_Code)", "召回出院", True)
        For Each My_Row In My_Dataset.Tables("召回出院").Rows

            T_Jz_Code1 = My_Row.Item("Jz_Code1")
            T_Jz_Code2 = My_Row.Item("Jz_Code2")
            T_Bl_Code = My_Row.Item("Bl_Code")
            If T_Jz_Code1 = T_Jz_Code2 Or T_Jz_Code2 = "空" Then
            Else
                '因为召回再次出院的退回的押金
                My_Reader2 = HisVar.HisVar.Sqldal.ExecuteReader("Select Isnull(Sum(Bl_M_Th),0) Bl_M_Th From Bl_Zh Where   Cy_Lb='出院' And Jz_Code='" & V_Jz_Code & "' And Bl_Code='" & T_Bl_Code & "'")
                My_Reader2.Read()
                If My_Reader2.HasRows = True Then
                    Ch_Th = My_Reader2.Item("Bl_M_Th")
                    V_Th = V_Th - Ch_Th
                End If
                My_Reader2.Close()
                Call P_Conn(False)
            End If
        Next

        '上缴金额=收取的金额-退回的金额
        V_Sj = V_Sq - V_Th
        '出院人数
        My_Reader = HisVar.HisVar.Sqldal.ExecuteReader("Select Count(Bl_Code) Cy_Rs From Bl Where   Jz_Code='" & V_Jz_Code & "' union all Select Count(Bl_Zh.Bl_Code) Cy_Rs From Bl_Zh,Bl Where Bl_Zh.Bl_Code=Bl.Bl_Code and    Bl_Zh.Jz_Code='" & V_Jz_Code & "'and Cy_Lb='出院' and isnull(Bl_Zh.Jz_Code,'')<>isnull(Bl.Jz_Code,'')")
        V_Rs = 0
        While My_Reader.Read()
            V_Rs = V_Rs + My_Reader.Item(0)
        End While
        Call P_Conn(False)
        My_Reader.Close()

        HisVar.HisVar.Sqldal.ExecuteSql("Update Bl_Jz Set Jz_Sq_M=" & V_Sq & ",Jz_Th_M=" & V_Th & ",Jz_Sj_M=" & V_Sj & ",Jz_Sz_M=" & V_Sz & ",Jz_Cy_Rs=" & V_Rs & ",Jz_Cy_M=" & V_Cy & " Where  Jz_Code='" & V_Jz_Code & "'")

        MsgBox("数据结账完成，点击确定打印日结单。", vbInformation + vbOKOnly + vbDefaultButton1, "提示:")
    End Sub

    Private Function F_MaxCode(ByVal V_MaxCode As String)   '出库编码
        Dim My_Cc As New BaseClass.C_Cc()
        Dim V_Code As String = V_MaxCode
        My_Cc.Get_MaxCode("Bl_Jz", "Jz_Code", 12, "Left(Jz_Code,10)", HisVar.HisVar.WsyCode & V_MaxCode)
        F_MaxCode = My_Cc.编码
        If Mid(F_MaxCode, 1, 10) = "0000000000" Then F_MaxCode = V_MaxCode + Mid(My_Cc.编码, 7)
        Return F_MaxCode
    End Function

    Private Sub C1Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Button1.Click


        If ZTHisPara.PublicConfig.EnableZyCfHz = False Then

            If HisVar.HisVar.Sqldal.GetSingle("Select Count(Cf_Code) From Bl_Cf Where Cf_Qr='否' And Jsr_Code='" & HisVar.HisVar.JsrCode & "'") > 0 Then
                MsgBox("存在未发药品或未请求发药患者，请检查！", vbCritical + vbOKOnly, "提示:")
                Exit Sub
            End If
        Else
            If HisVar.HisVar.Sqldal.GetSingle("Select Count(*) from Bl_Cf Where Cf_Date+Cf_Time<=GetDate() and Cf_Print='是' and Cf_Qr='否' ") > 0 Then
                MsgBox("存在早于当前时间的未发药品或未进行汇总领药的处方，请检查！", vbCritical + vbOKOnly, "提示:")
                Exit Sub
            End If
        End If

        Label7.Text = Format(Now, "yyyy-M-dd HH:mm:ss")

        If MsgBox("是否确认进行住院数据日结？", vbQuestion + vbYesNo + vbDefaultButton1, "提示:") = vbYes Then

            Call V_Js()


            Dim Str As String = " select V_Lb,Bxlb_Name,Cf_Lb,V_Order,isnull(Cf_Money,0) Cf_Money from  (Select '上账费用'V_Lb,Bxlb_Name,Cf_Lb,'X'+Cf_lb as V_Order,Sum(Bl_CfYp.Cf_Money) As Cf_Money " &
                           " From Bl,Zd_Bxlb,Bl_Cf,Bl_CfYp Where Bl.Bxlb_Code=Zd_Bxlb.Bxlb_Code and Bl.Bl_Code=Bl_Cf.Bl_Code and Bl_Cf.Cf_Code=Bl_CfYp.Cf_Code and Bl_Cf.jz_code='" & V_Jz_Code & "' Group By Bxlb_Name,Cf_Lb " &
                           "Union all Select '出院费用'V_Lb,Bxlb_Name,Cf_Lb,'X'+Cf_lb as V_Order,Sum(Bl_CfYp.Cf_Money) As Cy_Money" &
                           " From Bl,Zd_Bxlb,Bl_Cf,Bl_CfYp Where Bl.Bxlb_Code=Zd_Bxlb.Bxlb_Code and Bl.Bl_Code=Bl_Cf.Bl_Code and Bl_Cf.Cf_Code=Bl_CfYp.Cf_Code  and (bl.Jz_code='" & V_Jz_Code & "' " & V_Str & ") Group By Bxlb_Name,Cf_Lb" &
                           " UNION all Select '上账费用'V_Lb,Bxlb_Name,Cf_Lb,'Y'+Cf_lb as V_Order,Sum(Bl_CfXm.Cf_Money) As Cf_Money " &
                           " From Bl,Zd_Bxlb,Bl_Cf,Bl_CfXm Where Bl.Bxlb_Code=Zd_Bxlb.Bxlb_Code and Bl.Bl_Code=Bl_Cf.Bl_Code and Bl_Cf.Cf_Code=Bl_CfXm.Cf_Code and Bl_Cf.jz_code='" & V_Jz_Code & "' Group By Bxlb_Name,Cf_Lb " &
                           "Union all Select '出院费用'V_Lb,Bxlb_Name,Cf_Lb,'Y'+Cf_lb as V_Order,Sum(Bl_CfXm.Cf_Money) As Cy_Money " &
                            " From Bl,Zd_Bxlb,Bl_Cf,Bl_CfXm Where Bl.Bxlb_Code=Zd_Bxlb.Bxlb_Code and Bl.Bl_Code=Bl_Cf.Bl_Code and Bl_Cf.Cf_Code=Bl_CfXm.Cf_Code and  (bl.Jz_code='" & V_Jz_Code & "' " & V_Str & ") Group By Bxlb_Name, Cf_Lb" &
                           ")a  Order By V_Order"

            Dim Str1 As String = "select Sum(Isnull(Jf_XjMoney,0))Jf_XjMoney,Sum(Isnull(Jf_JkkMoney,0))Jf_JkkMoney,Sum(Bl_M_Th) Bl_M_Th, Sum(Isnull(Jf_XjMoney,0))+Sum(Isnull(Jf_JkkMoney,0))-Sum(Bl_M_Th) as Sk_Money,sum(Cy_Rc)Cy_Rc,Bxlb_Name from " &
                    "(select Case When Isnull(Bl_Jffs,'现金')='现金' then Jf_Money end as Jf_XjMoney,Case When Isnull(Bl_Jffs,'现金')='健康卡' then Jf_Money end as Jf_JkkMoney,0 as Bl_M_Th,Bxlb_Name,0 as Cy_Rc from bl_jf,Bl,Zd_Bxlb where Bl_Jf.Bl_Code=Bl.Bl_Code and Bl.bxlb_Code=Zd_Bxlb.Bxlb_Code and bl_jf.jz_code='" & V_Jz_Code & "' " &
                    "union all  select 0 as Jf_XjMoney,0 as Jf_JkkMoney,Sum(Bl_M_Th)Bl_M_Th,Bxlb_Name,Count(*)Cy_Rc from bl,Zd_Bxlb where Bl.Bxlb_Code=Zd_Bxlb.Bxlb_Code and jz_code='" & V_Jz_Code & "' group by Bxlb_Name " &
                    "union all  select 0 as Jf_XjMoney,0 as Jf_JkkMoney,-Sum(bl_zh.Bl_M_Th)  Bl_M_Th,Bxlb_Name,Count(*) as Cy_Rc from bl_zh,Bl,Zd_Bxlb where Bl_Zh.Bl_Code=Bl.Bl_Code and Bl.Bxlb_Code=Zd_Bxlb.Bxlb_Code and Cy_Lb='出院'  and Bl.Jz_Code<>Bl_zh.Jz_Code and Bl_Zh.jz_Code='" & V_Jz_Code & "' group by Bxlb_Name " &
                    "union all  select 0 as Jf_XjMoney,0 as Jf_JkkMoney,bl_zh.Bl_M_Th Bl_M_Th,Bxlb_Name,0 as Cy_Rc from bl_zh,Bl,Zd_Bxlb  where Bl_Zh.Bl_Code=Bl.Bl_Code and Bl.Bxlb_Code=Zd_Bxlb.Bxlb_Code and Cy_Lb='出院'  and Bl.Jz_Code<>Bl_zh.Jz_Code and Bl.jZ_Code='" & V_Jz_Code & "' and Bl_Zh.Jz_Code>Bl.Jz_Code)A  Group by Bxlb_Name"



            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, Str, "住院日结单", True)
            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, Str1, "住院分类汇总", True)



            Dim Stirpt As New StiReport
            Stirpt.RegData(My_Dataset.Tables("住院日结单"))
            Stirpt.RegData(My_Dataset.Tables("住院分类汇总"))
            Stirpt.Load(".\Rpt\住院日结.mrt")
            Stirpt.ReportName = "住院日结"
            Stirpt.Compile()
            Stirpt("标题") = "住 院 日 结 单(" & HisVar.HisVar.JsrName & ")"
            Stirpt("打印时间") = "打印时间:" & Format(Now, "yyyy-MM-dd HH:mm:ss")



            If Label6.Text = "无" Then
                Stirpt("汇总时间段") = "结帐时间段: 无" & " 至: " & V_Date + " " + V_Time
            Else
                Stirpt("汇总时间段") = "结帐时间段:" & Label6.Text & " 至: " & V_Date + " " + V_Time
            End If


            Stirpt.Render()


            ZyRj_Print.StiViewerControl1.Report = Stirpt
            ZyRj_Print.ShowDialog()

        End If
    End Sub

    Private Sub Xs_Zy_Jz_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load

        My_Reader = HisVar.HisVar.Sqldal.ExecuteReader("select top 1 Jsr_Name,Jz_Date,Jz_Time from Bl_jz,Zd_YyJsr where Bl_Jz.Jsr_code=Zd_YyJsr.Jsr_code and Bl_Jz.Jsr_Code='" & HisVar.HisVar.JsrCode & "' order by Jz_Date desc,Jz_Time desc")
        My_Reader.Read()
        If My_Reader.HasRows = False Then
            Label6.Text = "无"  '上次日结时间
            Label8.Text = "上次日结操作员:无"
        Else
            Label6.Text = My_Reader.Item(1) & " " & My_Reader.Item(2) '上次日结时间
            Label8.Text = "上次日结操作员:" & My_Reader.Item(0)
        End If
        Label7.Text = Format(Now, "yyyy-M-dd HH:mm:ss") '本次次日结时间
        Label3.Text = "本次日结操作员:" & HisVar.HisVar.JsrName
        My_Reader.Close()


        Dim m_str As String

        If ZTHisPara.PublicConfig.EnableZyCfHz = True Then
            m_str = "select top 1 Jz_Code from Bl_Jf where isnull(jz_code,'')=''   And Jsr_Code='" & HisVar.HisVar.JsrCode & "' " &
             " union all select top 1 Jz_Code from Bl where isnull(jz_code,'')='' And Ry_CyJsr='" & HisVar.HisVar.JsrCode & "' And Ry_CyDate Is Not Null " &
             " union all select top 1 Jz_Code from Bl_Cf where Cf_Qr='是' and isnull(jz_code,'')='' and Cf_Date+Cf_Time<=Getdate()  " &
             " union all select top 1 Jz_Code from Bl_Zh where isnull(jz_code,'')='' And Jsr_Code='" & HisVar.HisVar.JsrCode & "' and Cy_Lb='出院'"
        Else
            m_str = "select top 1 Jz_Code from Bl_Jf where isnull(jz_code,'')=''   And Jsr_Code='" & HisVar.HisVar.JsrCode & "' " &
                " union all select top 1 Jz_Code from Bl where isnull(jz_code,'')='' And Ry_CyJsr='" & HisVar.HisVar.JsrCode & "' And Ry_CyDate Is Not Null " &
                " union all select top 1 Jz_Code from Bl_Cf where Cf_Qr='是' and isnull(jz_code,'')='' and Cf_Date+Cf_Time<=Getdate() And Jsr_Code='" & HisVar.HisVar.JsrCode & "' " &
                " union all select top 1 Jz_Code from Bl_Zh where isnull(jz_code,'')='' And Jsr_Code='" & HisVar.HisVar.JsrCode & "' and Cy_Lb='出院'"
        End If

        My_Reader = HisVar.HisVar.Sqldal.ExecuteReader(m_str)
        My_Reader.Read()
        If My_Reader.HasRows = False Then
            C1Button1.Enabled = False
        Else
            C1Button1.Enabled = True
        End If
        My_Reader.Close()

    End Sub



End Class