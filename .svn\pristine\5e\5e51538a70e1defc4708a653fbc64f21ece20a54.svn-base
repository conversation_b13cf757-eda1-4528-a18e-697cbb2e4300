﻿Imports System.Data.SqlClient
Imports BaseClass

Public Class Zd_MzFp12

    Dim My_Cc As New BaseClass.C_Cc()                         '取最大编码及简称的类

#Region "传参"
    Di<PERSON> As Boolean
    Dim Rrow As DataRow
    Dim RZbtb As DataTable
    Dim Rtdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    'Dim Rlb As C1.Win.C1Input.C1Label
    Dim Rzbadt As SqlDataAdapter
    Dim Rrc As C_RowChange
    Dim Rtree As TreeView
#End Region

    Public Sub New(ByVal tinsert As Boolean, ByVal trow As DataRow, ByVal tZbtb As DataTable, ByVal ttdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid, ByVal tzbadt As SqlDataAdapter, ByRef trc As C_RowChange, ByRef ttree As TreeView)

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        Rinsert = tinsert
        'Rdate = tdate
        Rrow = trow
        RZbtb = tZbtb
        Rtdbgrid = ttdbgrid
        'Rlb = tlb
        Rzbadt = tzbadt
        Rrc = trc
        Rtree = ttree
        AddHandler Rrc.RowChanged, New BaseClass.RowChangedHandler(AddressOf Data_Show)
        ' 在 InitializeComponent() 调用之后添加任何初始化。
    End Sub

    Protected Overrides Sub OnKeyDown(ByVal e As System.Windows.Forms.KeyEventArgs)
        MyBase.OnKeyDown(e)
        If e.Control = True And e.KeyCode = Keys.S Then
            Comm1.Select()
            Call Comm_Click(Comm1, Nothing)
        End If
    End Sub

    Private Sub Zd_MzFp12_FormClosed(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosedEventArgs) Handles Me.FormClosed
        RemoveHandler Rrc.RowChanged, New BaseClass.RowChangedHandler(AddressOf Data_Show)
        Me.Dispose()
    End Sub

    Private Sub Zd_MzFp12_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Call Form_Init()
        If Rinsert = True Then Call Data_Clear() Else Call Data_Show(Rrow)
    End Sub


#Region "窗体__事件"

    Private Sub Form_Init()
        Panel1.Height = 28
        ToolBar1.Location = New Point(2, 4)

        'Escape键具有跳出功能
        C1TextBox1.AcceptsEscape = False
        C1TextBox2.AcceptsEscape = False

        '按扭初始化
        Call P_Comm(Me.Comm1)
        Call P_Comm(Me.Comm2)
    End Sub

#End Region

#Region "数据__操作"

    Private Sub Data_Clear()
        If Rtree.SelectedNode.Tag = "0" Then
            Dim My_Reader As SqlDataReader
            My_Reader = HisVar.HisVar.Sqldal.ExecuteReader("select  count(*)from Zd_MzFp3  Where Yy_Code='" & HisVar.HisVar.WsyCode & "' ")
            While My_Reader.Read
                If My_Reader.Item(0) = 3 Then
                    MsgBox("最多填写三个类别", MsgBoxStyle.Information + MsgBoxStyle.OkOnly, "提示:")
                    My_Reader.Close()
                    Call P_Conn(False)
                    Me.Close()
                    Exit Sub
                End If
            End While
            My_Reader.Close()
            Call P_Conn(False)
        End If
        Rinsert = True

        '最大编码
        My_Cc.Get_MaxCode("Zd_MzFp3", "Fl_Code", 6, "Yy_Code", HisVar.HisVar.WsyCode)

        L_Dl_Code.Text = My_Cc.编码

        C1TextBox1.Text = ""
        C1TextBox2.Text = ""
        L_Dl_Jc.Text = ""

        Call Show_Label()
    End Sub

    Private Sub Data_Show(ByVal tmp_Row As DataRow)
        Rinsert = False
        Rrow = tmp_Row
        With Rrow
            L_Dl_Code.Text = .Item("Fl_Code") & ""
            C1TextBox1.Text = .Item("Fl_Name") & ""
            L_Dl_Jc.Text = .Item("Fl_Jc") & ""
            C1TextBox2.Text = .Item("Fl_Memo") & ""
        End With
        Call Show_Label()

    End Sub

    Private Sub Show_Label()
        If Rinsert = True Then
            If Move5.Enabled = True Then Move5.Enabled = False
            T_Label2.Text = "新增"
        Else
            If Move5.Enabled = False Then Move5.Enabled = True
            T_Label2.Text = IIf(Rtdbgrid.Splits(0).Rows.Count() = 0, "0", CStr((Rtdbgrid.Row) + 1))
        End If
        T_Label3.Text = "∑=" + Rtdbgrid.Splits(0).Rows.Count.ToString

        C1TextBox1.SelectAll()
        C1TextBox1.Select()
    End Sub

#End Region

#Region "控件__动作"

    Private Sub Comm_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.Click, Comm2.Click
        Select Case sender.tag
            Case "保存"
                If Rinsert = True Then Call Data_Add() Else Call Data_Edit()
            Case "取消"
                Me.Close()
        End Select
    End Sub

    Private Sub Move_Click(ByVal sender As Object, ByVal e As C1.Win.C1Command.ClickEventArgs) Handles Move1.Click, Move2.Click, Move3.Click, Move4.Click, Move5.Click
        If sender.text = "新增" Then                            '增加状态

            Call Data_Clear()
        Else
            With Rtdbgrid
                If .RowCount = 0 Then Exit Sub
                Select Case sender.text
                    Case "最前"
                        .MoveFirst()
                    Case "上移"
                        .MovePrevious()
                    Case "下移"
                        .MoveNext()
                    Case "最后"
                        .MoveLast()
                End Select
            End With
            'Call Data_Show()
        End If

    End Sub

    Private Sub TextBox_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles C1TextBox1.KeyPress, C1TextBox2.KeyPress
        If e.KeyChar <> Chr(Keys.Return) Then Exit Sub
        e.Handled = True
        System.Windows.Forms.SendKeys.Send("{Tab}")
    End Sub

    Private Sub C1TextBox2_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1TextBox1.Validated
        My_Cc.Get_Py(Me.C1TextBox1.Text & "")
    End Sub

#End Region

#Region "数据__编辑"

    Private Sub Data_Add()
        Call Tree_Edit(L_Dl_Code.Text, Trim(C1TextBox1.Text) & "(0)")
        My_Cc.Get_MaxCode("Zd_MzFp3", "Fl_Code", 6, "Yy_Code", HisVar.HisVar.WsyCode)
        Dim My_NewRow As DataRow = RZbtb.NewRow
        If Trim(C1TextBox1.Text) = "" Then
            MsgBox("请填写类别名称!", MsgBoxStyle.Critical, "提示")
            C1TextBox1.Focus()
            Exit Sub
        End If

        With My_NewRow
            .Item("Yy_Code") = HisVar.HisVar.WsyCode
            .Item("Fl_Code") = My_Cc.编码.ToString
            .Item("Fl_Name") = Trim(C1TextBox1.Text & "")
            .Item("Fl_Jc") = Trim(L_Dl_Jc.Text & "")
            .Item("Fl_Memo") = Trim(C1TextBox2.Text & "")
        End With
        MsgBox("数据添加成功!", MsgBoxStyle.Information, "提示:")
        '数据保存

        Try
            RZbtb.Rows.Add(My_NewRow)
            Rtdbgrid.MoveLast()
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            Exit Sub
        Finally
            Me.C1TextBox1.Select()
        End Try


        '数据更新
        Try
            With Rzbadt.InsertCommand
                Dim I As Integer = 0
                For Each My_Para As SqlParameter In .Parameters
                    My_Para.Value = My_NewRow.Item(I)
                    I = I + 1
                Next
                Call P_Conn(True)
                .ExecuteNonQuery()
            End With
            My_NewRow.AcceptChanges()
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            Exit Sub
        Finally
            C1TextBox1.Select()
            Call P_Conn(False)
        End Try

        '数据清空
        Call Data_Clear()

    End Sub

    Private Sub Data_Edit()
        Call Tree_Edit(L_Dl_Code.Text, Trim(C1TextBox1.Text) & "(" & F_Count(L_Dl_Code.Text) & ")")

        Dim My_Row As DataRow = Rrow
        If Trim(C1TextBox1.Text) = "" Then
            MsgBox("请填写类别名称!", MsgBoxStyle.Critical, "提示")
            C1TextBox1.Focus()
            Exit Sub
        End If
        Try
            MsgBox("数据修改成功!", MsgBoxStyle.Information, "提示:")
            With My_Row
                .BeginEdit()
                .Item("Yy_Code") = HisVar.HisVar.WsyCode
                .Item("Fl_Name") = Trim(C1TextBox1.Text & "")
                .Item("Fl_Jc") = Trim(L_Dl_Jc.Text & "")
                .Item("Fl_Memo") = Trim(C1TextBox2.Text & "")
                .EndEdit()
            End With
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Critical, "提示:")
            My_Row.CancelEdit()
            Exit Sub
        Finally
            C1TextBox1.Select()
        End Try

        '数据更新
        Call P_Conn(True)
        With Rzbadt.UpdateCommand
            Dim I As Integer = 0
            For Each My_Para As SqlParameter In .Parameters
                If I = .Parameters.Count - 1 Then
                    My_Para.Value = My_Row.Item("Fl_Code", DataRowVersion.Original)
                Else
                    My_Para.Value = My_Row.Item(I)
                    I = I + 1
                End If
            Next
            Try
                .ExecuteNonQuery()
                My_Row.AcceptChanges()
            Catch ex As Exception
                Beep()
                MsgBox(ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
            Finally
                Call P_Conn(False)
                C1TextBox1.SelectAll()
                C1TextBox1.Select()
            End Try
        End With
    End Sub

    Private Function F_Count(ByVal V_Lb_Code As String) As Integer

        F_Count = HisVar.HisVar.Sqldal.GetSingle("select count(Mc_Code) as Mc_Count From Zd_MzFp31 Where Fl_Code='" & V_Lb_Code & "'")


    End Function

    Private Sub C1TextBox3_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1TextBox1.Validated
        My_Cc.Get_Py(Me.C1TextBox1.Text & "")
        L_Dl_Jc.Text = My_Cc.简拚.ToString
    End Sub

#End Region

#Region "自定义按扭"

    Private Sub P_Comm(ByVal Bt As Button)
        With Bt
            Select Case .Tag
                Case "保存"
                    .Top = 3
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")
                    .Size = New Size(52, 24)
                    .Text = "            &B"
                Case "取消"
                    .Location = New Point(Comm1.Left + Comm1.Width, Comm1.Top)
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
                    .Size = New Size(52, 24)
                    .Text = "            &C"
            End Select
            .FlatStyle = FlatStyle.Flat
            .FlatAppearance.BorderSize = 0
            .BackgroundImageLayout = ImageLayout.Center
        End With

    End Sub

    Private Sub Comm_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles Comm1.KeyDown, Comm2.KeyDown
        If e.KeyCode = Keys.Space Then
            Select Case sender.tag
                Case "保存"
                    Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存3")

                Case "取消"
                    Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消3")
            End Select
        End If
    End Sub

    Private Sub Comm_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles Comm1.KeyUp, Comm2.KeyUp
        If e.KeyCode = Keys.Enter Or e.KeyCode = Keys.Space Then
            Select Case sender.tag
                Case "保存"
                    Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")
                Case "取消"
                    Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
            End Select
        End If

    End Sub

    Private Sub Comm_MouseEnter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.MouseEnter, Comm2.MouseEnter
        Select Case sender.tag
            Case "保存"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存2")
            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消2")
        End Select
    End Sub

    Private Sub Comm_MouseLeave(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.MouseLeave, Comm2.MouseLeave
        Select Case sender.tag
            Case "保存"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")
            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
        End Select
    End Sub

    Private Sub Comm_MouseDown(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm1.MouseDown, Comm2.MouseDown
        Select Case sender.tag
            Case "保存"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存3")

            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消3")
        End Select
    End Sub

    Private Sub Comm_MouseUp(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm1.MouseUp, Comm2.MouseUp
        Select Case sender.tag
            Case "保存"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")
            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
        End Select
    End Sub

#End Region

#Region "自定义函数"

    Private Sub Tree_Edit(ByVal V_Key As String, ByVal V_Text As String)
        Rtree.SelectedNode = Rtree.TopNode()

        Dim My_Node As New TreeNode
        If Rinsert = True Then
            My_Node.Tag = L_Dl_Code.Text & ""
            My_Node.Text = V_Text
            My_Node.ImageIndex = 1
            My_Node.SelectedImageIndex = 2
            Rtree.SelectedNode.Nodes.Add(My_Node)
            If Rtree.SelectedNode.IsExpanded = False Then
                Rtree.SelectedNode.Expand()
            End If

        Else
            For Each My_Node In Rtree.SelectedNode.Nodes
                If My_Node.Tag = V_Key Then
                    My_Node.Text = V_Text
                    Exit For
                End If
            Next
        End If
    End Sub

#End Region

#Region "输入法设置"
    '中文
    Private Sub C1TextBox1_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1TextBox2.GotFocus, C1TextBox1.GotFocus
        InputLanguage.CurrentInputLanguage = HisVar.HisVar.InputCode
    End Sub
#End Region

End Class