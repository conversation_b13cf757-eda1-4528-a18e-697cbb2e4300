﻿/**  版本信息模板在安装目录下，可自行修改。
* B_Materials_Stock.cs
*
* 功 能： N/A
* 类 名： B_Materials_Stock
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016-11-26 11:32:28   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Collections.Generic;
using Common;
using ModelOld;
namespace BLLOld
{
    /// <summary>
    /// 物资库存
    /// </summary>
    public partial class B_Materials_Stock
    {
        private readonly DAL.D_Materials_Stock dal = new DAL.D_Materials_Stock();
        public B_Materials_Stock()
        { }
        #region  BasicMethod
        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(string MaterialsStock_Code)
        {
            return dal.Exists(MaterialsStock_Code);
        }
        public bool Exists(string Materials_Code, string MaterialsWh_Code, string MaterialsLot, DateTime MaterialsExpiryDate, decimal MaterialsStore_Price)
        {
            return dal.Exists(Materials_Code, MaterialsWh_Code, MaterialsLot, MaterialsExpiryDate, MaterialsStore_Price);
        }
        public string MaterialsStock_Code(string Materials_Code, string MaterialsWh_Code, string MaterialsLot, DateTime MaterialsExpiryDate, decimal MaterialsStore_Price)
        {
            return dal.MaterialsStock_Code(Materials_Code, MaterialsWh_Code, MaterialsLot, MaterialsExpiryDate, MaterialsStore_Price);
        }
        public string MaxCode(string MaterialsStock_Code)
        {
            return dal.MaxCode(MaterialsStock_Code);
        }
        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ModelOld.M_Materials_Stock model)
        {
            return dal.Add(model);
        }

        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ModelOld.M_Materials_Stock model)
        {
            return dal.Update(model);
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(string MaterialsStock_Code)
        {

            return dal.Delete(MaterialsStock_Code);
        }
        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool DeleteList(string MaterialsStock_Codelist)
        {
            return dal.DeleteList(MaterialsStock_Codelist);
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ModelOld.M_Materials_Stock GetModel(string MaterialsStock_Code)
        {

            return dal.GetModel(MaterialsStock_Code);
        }

        /// <summary>
        /// 得到一个对象实体，从缓存中
        /// </summary>
        public ModelOld.M_Materials_Stock GetModelByCache(string MaterialsStock_Code)
        {

            string CacheKey = "M_Materials_StockModel-" + MaterialsStock_Code;
            object objModel = Common.DataCache.GetCache(CacheKey);
            if (objModel == null)
            {
                try
                {
                    objModel = dal.GetModel(MaterialsStock_Code);
                    if (objModel != null)
                    {
                        int ModelCache = Common.ConfigHelper.GetConfigInt("ModelCache");
                        Common.DataCache.SetCache(CacheKey, objModel, DateTime.Now.AddMinutes(ModelCache), TimeSpan.Zero);
                    }
                }
                catch { }
            }
            return (ModelOld.M_Materials_Stock)objModel;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            return dal.GetList(strWhere);
        }

        public DataSet GetHzList(string strWhere)
        {
            return dal.GetHzList(strWhere);

        }
        public DataSet GetListDc(string strWhere)
        {
            return dal.GetListDc(strWhere);

        }

        public DataSet GetHzListMaterials(string strWhere)
        {
            return dal.GetHzListMaterials(strWhere);
        }

        public DataSet GetHzListMaterialsClass(string strWhere)
        {
            return dal.GetHzListMaterialsClass(strWhere);
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            return dal.GetList(Top, strWhere, filedOrder);
        }
        /// <summary>
        /// 获得数据列表
        /// </summary>
        public List<ModelOld.M_Materials_Stock> GetModelList(string strWhere)
        {
            DataSet ds = dal.GetList(strWhere);
            return DataTableToList(ds.Tables[0]);
        }
        /// <summary>
        /// 获得数据列表
        /// </summary>
        public List<ModelOld.M_Materials_Stock> DataTableToList(DataTable dt)
        {
            List<ModelOld.M_Materials_Stock> modelList = new List<ModelOld.M_Materials_Stock>();
            int rowsCount = dt.Rows.Count;
            if (rowsCount > 0)
            {
                ModelOld.M_Materials_Stock model;
                for (int n = 0; n < rowsCount; n++)
                {
                    model = dal.DataRowToModel(dt.Rows[n]);
                    if (model != null)
                    {
                        modelList.Add(model);
                    }
                }
            }
            return modelList;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetAllList()
        {
            return GetList("");
        }
        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string Materials_Code, string MaterialsWh_Code, string MaterialsLot, string MaterialsExpiryDate, decimal MaterialsStore_Price)
        {
            return dal.GetRecordCount(Materials_Code, MaterialsWh_Code, MaterialsLot, MaterialsExpiryDate, MaterialsStore_Price);
        }
        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string str)
        {
            return dal.GetRecordCount(str);
        }

        /// <summary>
        /// 获得同一种物资的 所有批号（同一种物资 同一批号 单价 和有效期相同）
        /// </summary>
        public DataSet GetMaterialsLot(string strWhere)
        {
            return dal.GetMaterialsLot(strWhere);
        }

         /// <summary>
        /// 获取库存编码
        /// </summary>
        public string GetMaterialsStockCode(string Materials_Code, string MaterialsWh_Code, string MaterialsLot, DateTime MaterialsExpiryDate, decimal MaterialsStore_Price)
        {
            return dal.GetMaterialsStockCode(Materials_Code, MaterialsWh_Code, MaterialsLot, MaterialsExpiryDate, MaterialsStore_Price);
        }
        /// <summary>
        /// 增加一条新编码数据
        /// </summary>
        public string AddNew(string Materials_Code, string MaterialsWh_Code, string MaterialsLot, string MaterialsExpiryDate, decimal MaterialsStore_Price)
        {
            return dal.AddNew(Materials_Code, MaterialsWh_Code, MaterialsLot, MaterialsExpiryDate, MaterialsStore_Price);
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            return dal.GetListByPage(strWhere, orderby, startIndex, endIndex);
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        //public DataSet GetList(int PageSize,int PageIndex,string strWhere)
        //{
        //return dal.GetList(PageSize,PageIndex,strWhere);
        //}

        #endregion  BasicMethod
        #region  ExtensionMethod

        #endregion  ExtensionMethod
    }
}
