﻿Imports System.Data.SqlClient

Public Class MbDesign
    Dim m_Row As DataRow
    Dim BllEmrMb As New BLLOld.B_Emr_Mb



    Public Sub New(ByVal row As DataRow)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        m_Row = row
        ' 在 InitializeComponent() 调用之后添加任何初始化。
    End Sub

    Private Sub ZyDzbl_Mb2_FormClosed(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosedEventArgs) Handles Me.FormClosed
        nsoff.RemoveAllListener()
        nsoff.Close()
    End Sub

    Private Sub Zd_Gx2_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        nsoff.Visible = False
        Call Data_Show()
        nsoff.Visible = True
    End Sub

#Region "窗体__事件"

    Private Sub Form_Init()
        nsoff.DesignTemplet(True, True) '切换为设计模式
        nsoff.SetMenuBarVisible(True) '显示和隐藏菜单栏
        nsoff.InsertExternalSubMenuItem("PickList", "MenuAddIn1", "保存", AppDomain.CurrentDomain.BaseDirectory & "Resources\Icon_1469.png")
        nsoff.InsertExternalSubMenuItem("InsertMenu", "MenuAddIn2", "基本元素", AppDomain.CurrentDomain.BaseDirectory & "Resources\insert.png")
        nsoff.RemoveUserRootMenuItem("HenJi")
        nsoff.RemoveExternalSubMenuItem("PickList", "MenuAddIn3")
        nsoff.RemoveExternalSubMenuItem("PickList", "MenuAddIn8")
        nsoff.RemoveExternalSubMenuItem("PickList", "MenuAddIn9")
        nsoff.SetRulersVisible(True, True) '显示标尺
        nsoff.SetStatusBarVisible(True) '显示状态栏
        nsoff.SetToolBarsVisible("standard", True) '显示工具栏
        nsoff.EnableCopyFromExternal(True) '设置是否可以从外部(比如IE，记事本，VS等等)拷贝到odt文档
        nsoff.SetCanCopyWithStruct(True)  '置控件内拷贝是否可以带结构信息
        nsoff.AddFileListener()     '开启文件监听，可在office崩溃后自动恢复当前编辑的文档
        nsoff.AddGlobalDocumentListener() '开启全局监听器，监听的事件非常多
        nsoff.SetGlobalDocumentListener(True, True, True, True) '设置在开启全局监听时，哪些对象会产生相应的事件
        nsoff.SetUserUIListener(False, True, False) '对用户添加做监听
        nsoff.AddKeyListener(0) '键盘监听
        nsoff.RemoveUserRootMenuItem("HelpMenu")
        MenuVisible(False)
    End Sub

    Private Sub MenuVisible(ByVal flag As Boolean)
        If flag = True Then
            nsoff.ShowMenuItem("newDoc") '显示“新建”菜单项
            nsoff.ShowMenuItem("Open")   '显示“打开”菜单项
            nsoff.ShowMenuItem("Save") '保存
            nsoff.ShowMenuItem("SaveAs") '另存为
            nsoff.ShowMenuItem("Quit") '退出
            nsoff.ShowMenuItem("About") '关于
            nsoff.SetToolbarItemVisible("standardbar", "newDoc", False)
            nsoff.SetToolbarItemVisible("standardbar", "Open", False)
            nsoff.SetToolbarItemVisible("standardbar", "Save", False)
            nsoff.SetToolbarItemVisible("standardbar", "SaveAs", False)
            nsoff.SetToolbarItemVisible("standardbar", "Quit", False)
            nsoff.SetToolbarItemVisible("standardbar", "DragLinePrint", False)
            nsoff.SetToolbarItemVisible("standardbar", "DragLinePrint_Print", False)
        Else
            nsoff.HideMenuItem("newDoc")
            nsoff.HideMenuItem("Open")
            nsoff.HideMenuItem("Save")
            nsoff.HideMenuItem("SaveAs")
            nsoff.HideMenuItem("Quit")
            nsoff.HideMenuItem("About")
            nsoff.SetToolbarItemVisible("standardbar", "newDoc", True)
            nsoff.SetToolbarItemVisible("standardbar", "Open", True)
            nsoff.SetToolbarItemVisible("standardbar", "Save", True)
            nsoff.SetToolbarItemVisible("standardbar", "SaveAs", True)
            nsoff.SetToolbarItemVisible("standardbar", "Quit", True)
            nsoff.SetToolbarItemVisible("standardbar", "DragLinePrint", True)
            nsoff.SetToolbarItemVisible("standardbar", "DragLinePrint_Print", True)
        End If
    End Sub

#End Region

#Region "数据__操作"

    Private Sub Data_Show()
        With m_Row
            If IsDBNull(m_Row("Mb_Nr")) = True Then
                nsoff.CreateNew("swriter")
            Else
                nsoff.OpenDocumentWithStream(m_Row("Mb_Nr"), 2)
            End If
            Call Form_Init()
        End With
    End Sub

#End Region

#Region "控件__动作"

    Private Sub nsoff_NsoUserMenuItemEvent(sender As Object, e As AxNsoOfficeLib._INsoControlEvents_NsoUserMenuItemEventEvent) Handles nsoff.NsoUserMenuItemEvent
        If e.nID = 1 Then
            Call Data_Edit()
        ElseIf e.nID = 2 Then
            Dim frm As New MbElement()
            frm.Owner = Me
            frm.StartPosition = Windows.Forms.FormStartPosition.CenterScreen
            If frm.ShowDialog() = Windows.Forms.DialogResult.OK Then

                Select Case frm.strControlType
                    Case "1"
                        If frm.objReserve = True Then
                            nsoff.InsertNewControlAtCurrentCursor("MultiCombox" &
           nsoff.GetNewControlCountByType(EnumNewControl.MultiCombox), frm.strName, EnumNewControl.MultiCombox)

                            For Each row In frm.My_Table.Rows
                                nsoff.SetCompoundBoxCodeAndValue("MultiCombox" &
          (nsoff.GetNewControlCountByType(EnumNewControl.MultiCombox) - 1),
           row("ItemText"), frm.My_Table.Rows.IndexOf(row), 4)
                            Next
                        Else
                            nsoff.InsertNewControlAtCurrentCursor("Combox" &
          nsoff.GetNewControlCountByType(EnumNewControl.Combox), frm.strName, EnumNewControl.Combox)

                            Dim s = nsoff.GetAllControlNameByCurrentDoc
                            For Each row In frm.My_Table.Rows
                                nsoff.SetCompoundBoxCodeAndValue("Combox" &
           (nsoff.GetNewControlCountByType(EnumNewControl.Combox) - 1),
           row("ItemText"), frm.My_Table.Rows.IndexOf(row), 1)
                            Next
                        End If
                    Case "2"
                        nsoff.InsertNewControlAtCurrentCursor("DefaultTextBox" &
   nsoff.GetNewControlCountByType(EnumNewControl.TextBox), frm.strName, EnumNewControl.TextBox)

                        nsoff.SetNewControlProp("DefaultTextBox" &
  nsoff.GetNewControlCountByType(EnumNewControl.TextBox) - 1, "Reserve", frm.objReserve)
                    Case "3"
                        nsoff.InsertNewControlAtCurrentCursor("DataFieldTextBox" &
nsoff.GetNewControlCountByType(EnumNewControl.TextBox), frm.strName, EnumNewControl.TextBox)

                        nsoff.SetNewControlProp("DataFieldTextBox" &
nsoff.GetNewControlCountByType(EnumNewControl.TextBox) - 1, "Reserve", frm.objReserve)
                End Select
            End If
        End If

    End Sub
    Private Sub nsOff_NsoKeyPressedEvent(ByVal sender As System.Object, ByVal e As AxNsoOfficeLib._INsoControlEvents_NsoKeyPressedEventEvent) Handles nsoff.NsoKeyPressedEvent
        '处理鼠标和键盘事件的时候，请尽量不用使用同步方式来显示其他窗体，如果在这些窗体中再打开其他的病历文档，可能会导致文档无法显示出来。因为控制权被这些窗体所掌握，无法回到office进程中，会导致office无法刷新界面。
        If e.nKeyCode = 530 And e.nModifiers = 2 Then
            Call Data_Edit()
        End If
    End Sub

#End Region

#Region "自定义函数"

#End Region

#Region "数据__编辑"

    Private Sub Data_Edit()

        '先保存到临时文件中，然后读出文件内容，再存入数据库
        Dim lbytContents() As Byte
        Dim lstrFilename As String

        lstrFilename = System.Windows.Forms.Application.StartupPath & "\" & Format(Now, "yyyymmddhhmmss") & Format(Rnd() * 100, "000") & ".odt"
        nsoff.SaveAs2(lstrFilename, True)      '将当前文件另存为一个临时文件
        nsoff.SetDocModified2(False)    '将文件的修改标志设为“未修改”

        lbytContents = My.Computer.FileSystem.ReadAllBytes(lstrFilename)
        My.Computer.FileSystem.DeleteFile(lstrFilename)     '删除该临时文件

        Try
            With m_Row
                .BeginEdit()
                .Item("Mb_Nr") = lbytContents
                .EndEdit()
            End With
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Critical, "提示:")
            m_Row.CancelEdit()
            Exit Sub
        Finally

        End Try

        '数据更新
        Dim ModelEmrMb As ModelOld.M_Emr_Mb = BllEmrMb.GetModel(m_Row("Mb_Code") & "")
        Try
            ModelEmrMb.Mb_Nr = m_Row.Item("Mb_Nr")
            BllEmrMb.Update(ModelEmrMb)
            m_Row.AcceptChanges()
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
        Finally

        End Try
        HisControl.msg.Show("模板修改成功!", "提示")
    End Sub

#End Region

    Enum EnumNewControl
        Combox = 1
        ListBox = 2
        TextBox = 3
        CheckBox = 4
        NumberBox = 5
        MultiListBox = 6
        MultiCombox = 7
        DateTimeBox = 8
        RadioButton = 9
        MultiCheckbox = 10
    End Enum
End Class