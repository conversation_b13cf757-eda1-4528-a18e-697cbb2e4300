﻿
Imports System.Windows.Forms

Public Class MblbChange


#Region "变量初始化"
    Private My_Table As New DataTable            '药品字典
    Private My_Cm As CurrencyManager             '同步指针
    'Private My_Row As DataRow                    '当 前 行
    'Private V_Insert As Boolean                  '增加记录

    Private bllEmrMblb As New BLLOld.B_Emr_Mblb

#End Region


    Private Sub BedAllocation_Load(sender As System.Object, e As System.EventArgs) Handles MyBase.Load
        Call Form_Init()
        Call Init_Data()
    End Sub


#Region "窗体初始化"

    Private Sub Form_Init()

        '初始化MyGrid1

        With MyGrid1
            .Init_Column("模板编码", "Mblb_Code", 0, "左", "", False)
            .Init_Column("模板名称", "Mblb_Name", 90, "中", "", False)
            .Init_Column("模板简称", "Mblb_Jc", 90, "中", "", False)

        End With
        NameMyTextBox1.Focus()
    End Sub

    Private Sub Init_Data()

        My_Table = bllEmrMblb.GetEndNodes().Tables(0)
        With My_Table
            .PrimaryKey = New DataColumn() {.Columns("Mblb_Code")}
        End With
        With Me.MyGrid1
            My_Cm = CType(BindingContext(My_Table, ""), CurrencyManager)
            .DataTable = My_Table
        End With

    End Sub


#End Region


#Region "控件动作"
    Private Sub NameMyTextBox1_TextChanged(sender As Object, e As System.EventArgs) Handles NameMyTextBox1.TextChanged
        Dim view As DataView = My_Cm.List
        view.RowFilter = "Mblb_jc like '%" & NameMyTextBox1.Text & "%' or Mblb_name like '%" & NameMyTextBox1.Text & "%'"
    End Sub

    Private Sub MyGrid1_DoubleClick(sender As Object, e As System.EventArgs) Handles MyGrid1.DoubleClick
        If MyGrid1.RowCount > 0 Then
            If MsgBox("您确定要把模板换到" & MyGrid1.Columns("Mblb_Name").Value & "吗？", MsgBoxStyle.OkCancel + MsgBoxStyle.DefaultButton1, "提示：") = MsgBoxResult.Ok Then
                EmrMb1.vSelectedNodeTag = MyGrid1.Columns("Mblb_Code").CellValue(MyGrid1.Row)
                Me.DialogResult = Windows.Forms.DialogResult.OK
            End If
            Me.Close()
        End If
    End Sub
#End Region




End Class