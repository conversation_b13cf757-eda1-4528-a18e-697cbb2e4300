﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="C1CommandHolder1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>70, 20</value>
  </metadata>
  <metadata name="Edit_Materials_CMS.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>446, 19</value>
  </metadata>
  <metadata name="CMenuStrip1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>326, 19</value>
  </metadata>
  <metadata name="Image1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>220, 20</value>
  </metadata>
  <data name="Image1.ImageStream" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>
        AAEAAAD/////AQAAAAAAAAAMAgAAAFdTeXN0ZW0uV2luZG93cy5Gb3JtcywgVmVyc2lvbj0yLjAuMC4w
        LCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAACZTeXN0
        ZW0uV2luZG93cy5Gb3Jtcy5JbWFnZUxpc3RTdHJlYW1lcgEAAAAERGF0YQcCAgAAAAkDAAAADwMAAABa
        DAAAAk1TRnQBSQFMAgEBBQEAARwBAQEcAQEBEAEAARABAAT/ARkBAAj/AUIBTQE2BwABNgMAASgDAAFA
        AwABIAMAAQEBAAEYBgABGB4AAR0BRwFdASwBYwGKAU4BiwG9AYoBtgHQAfYB+AH5qAAB4AHLAb8B1AGz
        AaABwQGJAWYBLQFlAYIBlAHHAfkBkQHJAfkBQQGFAckBIwFlAaYBkwFyAWMBqAFvAU4BpwFuAU0BpQFw
        AU8BrAF8AV4BwwGiAY2WAAHJAZkBeAYAAUMBiQGqAeAB8gH/AVQBmgHYARoBegG+AUkBmAHFAUkBjwHH
        AdwB6gH3CQABrgF+AWOWAAHBAYEBWgMAAa8BZwEsAXUBZgFXAXoBtgHVAZABtwHRAVUByQHkAVsB3wH1
        AXgB0AHtAVABmwHaAdcB2QHdAfAB5QHeAwABpgFxAVCWAAHEAYYBWgMAAa8BZwEsAc0BnwF0AX0BbgFY
        AXYBugHXAcIB9gH9AWMB3wH3AV0B4gH4AXkB0wHwAUkBmAHcAdkB2gHdAwABqAFxAU+WAAHFAYYBWwMA
        Aa8BZwEsAc0BoAF1Aa0BZAErAbEB1gHnAXcBywHnAccB9wH9AV4B3AH1AVoB4QH3AXsB1AHxAUsBmQHc
        AdYB6AH3AawBcgFQlgABxwGIAVoDAAG0AW4BMwHQAaUBfgGyAWwBLgMAAbUB1gHdAXkB0wHuAccB9wH9
        AV8B3AH1AVsB4gH3AXoB1gHyAVEBogHiAZ0BegFolgAByAGKAVsDAAG3AXYBQAHRAaYBgwG2AXMBOAMA
        AfAB5QHeAbsB3wHpAX0B1AHuAcQB9gH9AWwB3QH2AW0BygHtAWMBowHXAWQBmAHJAfYB9wH4kwABygGM
        AVwDAAG8AX8BVAG6AX0BTAG6AXwBSgMAAfAB5QHeAfAB5QHeAasB1QHfAYEB1QHuAbIB4wH5AYsBwAHn
        Aa4B0wH2AcQB4AH8AW8BowHTkwABygGNAV8YAAGxAeYB9QF3Ab4B5wG0AdIB8AHlAfMB/wGsAdIB7wFk
        AZsBzJMAAcwBkgFiAwAB7QHEAZsB7QHEAZsB7QHEAZsB7QHEAZsB7QHEAZsB7QHEAZsB7QHEAZsB7QHE
        AZsBsAG9AbEBWAGlAdgBhQGxAdsBRgGdAdAB2AHmAe6TAAHQAZoBbQMAAe0BxAGcAfQB2gHBAfQB2gHB
        AfQB2wHCAfQB2wHCAfQB2wHCAfQB2wHCAfQB2wHCAfQB2wHCAe0BxAGbAwABwAGDAVqWAAHVAawBhgMA
        Ae0BxAGcAe0BxAGcAe0BxAGcAe0BxAGbAe0BxAGbAe0BxAGbAe0BxAGbAe0BxAGbAe0BxAGbAe0BxAGb
        AwAByQGQAWeWAAHiAc0BuiQAAdYBqgGJlgAB6gHfAdQB5AHRAcEB2QGzAZEBzgGTAV8BzAGSAWEBzQGS
        AV8BzAGQAV8BzQGSAWEBzQGTAWMBywGRAWEBzQGYAW8B1gGyAZYB2AGuAYsB1wGsAYv/AP8AFQAqgGkA
        AeABywG/AdQBswGgAcEBiQFmAboBeQFTAbYBdwFRAbQBdgFRAbEBdAFQAa0BcgFPAaoBcQFPAagBbwFO
        AacBbgFNAaUBcAFPAawBfAFeAcMBogGNAwADgCQAA4BpAAHJAZkBeCQAAa4BfgFjAwADgAPADwADwA8A
        BoADACqADAAkgAkAAcEBgQFaAwABTgGbAVMBTAGYAVABSgGXAU8DAAHwAeUB3gHwAeUB3gHwAeUB3gHw
        AeUB3gHwAeUB3gHwAeUB3gMAAaYBcQFQBgADgAPAA4AJAAPAAQACgAEAAv8BAAL/AQAC/wEAAv8DAAOA
        AwADgAQAAv8DwAEAAv8DwAEAAv8DwAEAAv8DwAEAAv8DwAEAAv8DgAwAA4AEAAL/A8ABAAL/A8ABAAL/
        A8ABAAL/A8ABAAL/A4AJAAHEAYYBWgMAAVEBnwFWAXQBywF+AU0BmgFRAwAB8AHlAd4B9gHvAesB9gHv
        AesB9gHvAesB9gHvAesB8AHlAd4DAAGoAXEBTwYAA4ADwAOADAADwAEAAoABAAL/AQACgAEAAoADAAPA
        AwADgAMAA8ABAAL/A8ABAAL/A8ABAAL/A8ABAAL/A8ABAAL/A8ADgAkAA4AEAAL/A8ABAAL/A8ABAAL/
        A8ABAAL/A8ABAAL/A8ADAAOABgABxQGGAVsDAAFVAaMBWgF0AcsBfgFQAZ4BVQMAAfAB5QHeAfYB7wHr
        AfUB7AHmAfUB7AHmAfYB7wHrAfAB5QHeAwABrAFyAVAJAAOAA8ADgAkAA8ABAAKAAQACgAzAAwADgAQA
        Av8DwAEAAv8DwAEAAv8DwAEAAv8DwAEAAv8DwAEAAv8DgAkAA4ADAAPAAQAC/wPAAQAC/wPAAQAC/wPA
        AQAC/wPAA4ADAAOABgABxwGIAVoDAAFaAakBYAF0AcsBfgFVAaQBWgMAAfAB5QHeAfYB7wHrAfUB7AHm
        AfUB7AHmAfYB7wHrAfAB5QHeAwABsgF2AVEJACGAAwADgAMAA4ADAAPAAQAC/wPAAQAC/wPAAQAC/wPA
        AQAC/wPAAQAC/wPAA4AGAAOAAwADwAEAAv8DwAEAAv8DwAEAAv8DwAEAAv8DwAEAAv8DAAaABgAByAGK
        AVsDAAFeAa8BZQF0AcsBfgFbAasBYQMAAfAB5QHeAfYB7wHrAfYB7wHrAfYB7wHrAfYB7wHrAfAB5QHe
        AwABtgF4AVMzAAOABAAC/wPAAQAC/wPAAQAC/wPAAQAC/wPAAQAC/wPAAQAC/wOABgADgB4AA4ADAAPA
        A4AGAAHKAYwBXAMAAWQBtQFrAWEBswFpAWABsgFnAwAB8AHlAd4B8AHlAd4B8AHlAd4B8AHlAd4B8AHl
        Ad4B8AHlAd4DAAG5AXwBVDMAA4ADAAPAAQAC/wPAAQAC/wPAAQAC/wPAAQAC/wPAAQAC/wPAA4AGACeA
        AQAC/wOABgABygGNAV8kAAG8AX4BVTMAA4AEAAL/A8ABAAL/A8ABAAL/A8ABAAL/A8ABAAL/A8ABAAL/
        A4AJAAOAAwADwAEAAv8DwAEAAv8DwAEAAv8DwAEAAv8DwAEAAv8DwAOABgABzAGSAWIDAAHtAcQBmwHt
        AcQBmwHtAcQBmwHtAcQBmwHtAcQBmwHtAcQBmwHtAcQBmwHtAcQBmwHtAcQBmwHtAcQBmwMAAb4BgQFX
        MwADgCQAA4AJAAOABAAC/wPAAQAC/wPAAQAC/wPADwADgAYAAdABmgFtAwAB7QHEAZwB9AHaAcEB9AHa
        AcEB9AHbAcIB9AHbAcIB9AHbAcIB9AHbAcIB9AHbAcIB9AHbAcIB7QHEAZsDAAHAAYMBWhUAAoABAAL/
        AQACgAEAAoATAAOAA8ABAAL/A8ABAAL/A8ABAAL/A8ASgAkAA4ADAAPAAQAC/wPAAQAC/wPAAwASgAYA
        AdUBrAGGAwAB7QHEAZwB7QHEAZwB7QHEAZwB7QHEAZsB7QHEAZsB7QHEAZsB7QHEAZsB7QHEAZsB7QHE
        AZsB7QHEAZsDAAHJAZABZxgAAoABAAKAGQADgAPAAQAC/wPAAQAC/wPAA4AeAAOADwADgBgAAeIBzQG6
        JAAB1gGqAYkYAAKAAQACgBwAD4AkAA+AGwAB6gHfAdQB5AHRAcEB2QGzAZEBzgGTAV8BzAGSAWEBzQGS
        AV8BzAGQAV8BzQGSAWEBzQGTAWMBywGRAWEBzQGYAW8B1gGyAZYB2AGuAYsB1wGsAYsYAAKAqQABQgFN
        AT4HAAE+AwABKAMAAUADAAEgAwABAQEAAQEGAAEBFgAD/wEAAfABfwYAAYABAQYAAbABHQYAAaABBQYA
        AaABBQYAAaABAQYAAaIBAQYAAaIHAAGiBwABvwHABgABoAcAAaABBQYAAaABBQYAAb8B/QYAAYABAQYA
        Av8HAAEDBv8BAAEBBP8BgAEBAX8B+QGAAQEB4AEAAb8B/QMAAQEBwAEAAaIBBQGMAQABQAEBAdABAAGi
        AQUBjgEAAUABAQGgAQABogEFAcYBAAFAAQEBoAEAAaIBBQHAAQABQAEBAUABAAGiAQUB/wH7AUABAQF/
        AeABogEFAf8B/QFAAQECAAG/Af0B/wH9AUABAQGgAQABoAEFAfgBDgF/AfkBoAF8AaABBQH8ARkBAAED
        AaABgQGgAQUB/gEnAYAB/wHfAX8BvwH9Af4BHwHBAf8B4AH/AYABAQH+AX8G/ws=
</value>
  </data>
</root>