﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class Zy_Fy1
    Inherits HisControl.BaseForm

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(Zy_Fy1))
        Me.C1CommandHolder1 = New C1.Win.C1Command.C1CommandHolder()
        Me.Comm2 = New C1.Win.C1Command.C1Command()
        Me.Comm3 = New C1.Win.C1Command.C1Command()
        Me.C1TrueDBGrid1 = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.T_Textbox = New CustomControl.MyTextBox()
        Me.C1Combo1 = New C1.Win.C1List.C1Combo()
        Me.CheckBox1 = New System.Windows.Forms.CheckBox()
        Me.T_Line2 = New System.Windows.Forms.Label()
        Me.T_Label = New C1.Win.C1Input.C1Label()
        Me.C1ToolBar1 = New C1.Win.C1Command.C1ToolBar()
        Me.C1CommandLink2 = New C1.Win.C1Command.C1CommandLink()
        Me.C1CommandLink3 = New C1.Win.C1Command.C1CommandLink()
        Me.Panel2 = New System.Windows.Forms.Panel()
        Me.T_Label3 = New System.Windows.Forms.Label()
        Me.T_Label4 = New System.Windows.Forms.Label()
        Me.C1TrueDBGrid2 = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.C1TrueDBGrid3 = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.C1TrueDBGrid4 = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.C1TrueDBGrid5 = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.C1TrueDBGrid6 = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        CType(Me.C1CommandHolder1,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.C1TrueDBGrid1,System.ComponentModel.ISupportInitialize).BeginInit
        Me.Panel1.SuspendLayout
        CType(Me.C1Combo1,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.T_Label,System.ComponentModel.ISupportInitialize).BeginInit
        Me.Panel2.SuspendLayout
        CType(Me.C1TrueDBGrid2,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.C1TrueDBGrid3,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.C1TrueDBGrid4,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.C1TrueDBGrid5,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.C1TrueDBGrid6,System.ComponentModel.ISupportInitialize).BeginInit
        Me.SuspendLayout
        '
        'C1CommandHolder1
        '
        Me.C1CommandHolder1.Commands.Add(Me.Comm2)
        Me.C1CommandHolder1.Commands.Add(Me.Comm3)
        Me.C1CommandHolder1.Owner = Me
        '
        'Comm2
        '
        Me.Comm2.Image = CType(resources.GetObject("Comm2.Image"),System.Drawing.Image)
        Me.Comm2.Name = "Comm2"
        Me.Comm2.ShortcutText = ""
        Me.Comm2.Text = "更新"
        '
        'Comm3
        '
        Me.Comm3.Image = CType(resources.GetObject("Comm3.Image"),System.Drawing.Image)
        Me.Comm3.Name = "Comm3"
        Me.Comm3.ShortcutText = ""
        Me.Comm3.Text = "退回"
        '
        'C1TrueDBGrid1
        '
        Me.C1TrueDBGrid1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.C1TrueDBGrid1.GroupByCaption = "Drag a column header here to group by that column"
        Me.C1TrueDBGrid1.Images.Add(CType(resources.GetObject("C1TrueDBGrid1.Images"),System.Drawing.Image))
        Me.C1TrueDBGrid1.Location = New System.Drawing.Point(0, 24)
        Me.C1TrueDBGrid1.Name = "C1TrueDBGrid1"
        Me.C1TrueDBGrid1.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.C1TrueDBGrid1.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.C1TrueDBGrid1.PreviewInfo.ZoomFactor = 75R
        Me.C1TrueDBGrid1.PrintInfo.MeasurementDevice = C1.Win.C1TrueDBGrid.PrintInfo.MeasurementDeviceEnum.Screen
        Me.C1TrueDBGrid1.PrintInfo.MeasurementPrinterName = Nothing
        Me.C1TrueDBGrid1.Size = New System.Drawing.Size(953, 392)
        Me.C1TrueDBGrid1.TabIndex = 5
        Me.C1TrueDBGrid1.Text = "C1TrueDBGrid1"
        Me.C1TrueDBGrid1.PropBag = resources.GetString("C1TrueDBGrid1.PropBag")
        '
        'Panel1
        '
        Me.Panel1.Controls.Add(Me.T_Textbox)
        Me.Panel1.Controls.Add(Me.C1Combo1)
        Me.Panel1.Controls.Add(Me.CheckBox1)
        Me.Panel1.Controls.Add(Me.T_Line2)
        Me.Panel1.Controls.Add(Me.T_Label)
        Me.Panel1.Controls.Add(Me.C1ToolBar1)
        Me.Panel1.Dock = System.Windows.Forms.DockStyle.Top
        Me.Panel1.Location = New System.Drawing.Point(0, 0)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(953, 24)
        Me.Panel1.TabIndex = 4
        '
        'T_Textbox
        '
        Me.T_Textbox.Captain = "过滤框"
        Me.T_Textbox.CaptainBackColor = System.Drawing.Color.Transparent
        Me.T_Textbox.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.T_Textbox.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.T_Textbox.CaptainWidth = 60!
        Me.T_Textbox.ContentForeColor = System.Drawing.Color.Black
        Me.T_Textbox.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer))
        Me.T_Textbox.EditMask = Nothing
        Me.T_Textbox.Location = New System.Drawing.Point(139, 3)
        Me.T_Textbox.Multiline = false
        Me.T_Textbox.Name = "T_Textbox"
        Me.T_Textbox.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.T_Textbox.ReadOnly = false
        Me.T_Textbox.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.T_Textbox.SelectionStart = 0
        Me.T_Textbox.SelectStart = 0
        Me.T_Textbox.Size = New System.Drawing.Size(218, 20)
        Me.T_Textbox.TabIndex = 16
        Me.T_Textbox.TextAlign = System.Windows.Forms.HorizontalAlignment.Left
        Me.T_Textbox.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.T_Textbox.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.T_Textbox.Watermark = Nothing
        '
        'C1Combo1
        '
        Me.C1Combo1.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1Combo1.Caption = ""
        Me.C1Combo1.CaptionHeight = 17
        Me.C1Combo1.CharacterCasing = System.Windows.Forms.CharacterCasing.Normal
        Me.C1Combo1.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.C1Combo1.Images.Add(CType(resources.GetObject("C1Combo1.Images"),System.Drawing.Image))
        Me.C1Combo1.ItemHeight = 15
        Me.C1Combo1.Location = New System.Drawing.Point(494, 5)
        Me.C1Combo1.MatchEntryTimeout = CType(2000,Long)
        Me.C1Combo1.MaxDropDownItems = CType(5,Short)
        Me.C1Combo1.MaxLength = 32767
        Me.C1Combo1.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.C1Combo1.Name = "C1Combo1"
        Me.C1Combo1.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.C1Combo1.Size = New System.Drawing.Size(151, 16)
        Me.C1Combo1.TabIndex = 12
        Me.C1Combo1.TabStop = false
        Me.C1Combo1.PropBag = resources.GetString("C1Combo1.PropBag")
        '
        'CheckBox1
        '
        Me.CheckBox1.AutoSize = true
        Me.CheckBox1.Location = New System.Drawing.Point(473, 7)
        Me.CheckBox1.Name = "CheckBox1"
        Me.CheckBox1.Size = New System.Drawing.Size(15, 14)
        Me.CheckBox1.TabIndex = 10
        Me.CheckBox1.UseVisualStyleBackColor = true
        '
        'T_Line2
        '
        Me.T_Line2.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.T_Line2.Location = New System.Drawing.Point(401, -1)
        Me.T_Line2.Name = "T_Line2"
        Me.T_Line2.Size = New System.Drawing.Size(2, 25)
        Me.T_Line2.TabIndex = 8
        Me.T_Line2.Text = "Label2"
        '
        'T_Label
        '
        Me.T_Label.AutoSize = true
        Me.T_Label.BackColor = System.Drawing.SystemColors.Control
        Me.T_Label.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.T_Label.ForeColor = System.Drawing.Color.Maroon
        Me.T_Label.Location = New System.Drawing.Point(409, 7)
        Me.T_Label.Name = "T_Label"
        Me.T_Label.Size = New System.Drawing.Size(47, 12)
        Me.T_Label.TabIndex = 7
        Me.T_Label.Tag = Nothing
        Me.T_Label.Text = "T_Label"
        Me.T_Label.TextAlign = System.Drawing.ContentAlignment.BottomLeft
        Me.T_Label.TextDetached = true
        Me.T_Label.TrimStart = true
        '
        'C1ToolBar1
        '
        Me.C1ToolBar1.AccessibleName = "Tool Bar"
        Me.C1ToolBar1.CommandHolder = Me.C1CommandHolder1
        Me.C1ToolBar1.CommandLinks.AddRange(New C1.Win.C1Command.C1CommandLink() {Me.C1CommandLink2, Me.C1CommandLink3})
        Me.C1ToolBar1.Location = New System.Drawing.Point(1, 1)
        Me.C1ToolBar1.Name = "C1ToolBar1"
        Me.C1ToolBar1.Size = New System.Drawing.Size(118, 24)
        Me.C1ToolBar1.Text = "C1ToolBar1"
        Me.C1ToolBar1.VisualStyle = C1.Win.C1Command.VisualStyle.Classic
        Me.C1ToolBar1.VisualStyleBase = C1.Win.C1Command.VisualStyle.Classic
        '
        'C1CommandLink2
        '
        Me.C1CommandLink2.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image),C1.Win.C1Command.ButtonLookFlags)
        Me.C1CommandLink2.Command = Me.Comm2
        '
        'C1CommandLink3
        '
        Me.C1CommandLink3.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image),C1.Win.C1Command.ButtonLookFlags)
        Me.C1CommandLink3.Command = Me.Comm3
        Me.C1CommandLink3.SortOrder = 1
        '
        'Panel2
        '
        Me.Panel2.BackColor = System.Drawing.Color.Transparent
        Me.Panel2.Controls.Add(Me.T_Label3)
        Me.Panel2.Controls.Add(Me.T_Label4)
        Me.Panel2.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.Panel2.Location = New System.Drawing.Point(0, 416)
        Me.Panel2.Name = "Panel2"
        Me.Panel2.Size = New System.Drawing.Size(953, 21)
        Me.Panel2.TabIndex = 8
        '
        'T_Label3
        '
        Me.T_Label3.AutoSize = true
        Me.T_Label3.Location = New System.Drawing.Point(394, 4)
        Me.T_Label3.Name = "T_Label3"
        Me.T_Label3.Size = New System.Drawing.Size(47, 12)
        Me.T_Label3.TabIndex = 6
        Me.T_Label3.Text = "∑本日="
        Me.T_Label3.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'T_Label4
        '
        Me.T_Label4.AutoSize = true
        Me.T_Label4.BackColor = System.Drawing.SystemColors.ButtonFace
        Me.T_Label4.ForeColor = System.Drawing.Color.DarkRed
        Me.T_Label4.Location = New System.Drawing.Point(472, 4)
        Me.T_Label4.Name = "T_Label4"
        Me.T_Label4.Size = New System.Drawing.Size(53, 12)
        Me.T_Label4.TabIndex = 5
        Me.T_Label4.Text = "T_Label4"
        Me.T_Label4.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'C1TrueDBGrid2
        '
        Me.C1TrueDBGrid2.Dock = System.Windows.Forms.DockStyle.Fill
        Me.C1TrueDBGrid2.GroupByCaption = "Drag a column header here to group by that column"
        Me.C1TrueDBGrid2.Images.Add(CType(resources.GetObject("C1TrueDBGrid2.Images"),System.Drawing.Image))
        Me.C1TrueDBGrid2.Location = New System.Drawing.Point(0, 24)
        Me.C1TrueDBGrid2.Name = "C1TrueDBGrid2"
        Me.C1TrueDBGrid2.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.C1TrueDBGrid2.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.C1TrueDBGrid2.PreviewInfo.ZoomFactor = 75R
        Me.C1TrueDBGrid2.PrintInfo.MeasurementDevice = C1.Win.C1TrueDBGrid.PrintInfo.MeasurementDeviceEnum.Screen
        Me.C1TrueDBGrid2.PrintInfo.MeasurementPrinterName = Nothing
        Me.C1TrueDBGrid2.Size = New System.Drawing.Size(953, 392)
        Me.C1TrueDBGrid2.TabIndex = 9
        Me.C1TrueDBGrid2.Text = "3"
        Me.C1TrueDBGrid2.PropBag = resources.GetString("C1TrueDBGrid2.PropBag")
        '
        'C1TrueDBGrid3
        '
        Me.C1TrueDBGrid3.Dock = System.Windows.Forms.DockStyle.Fill
        Me.C1TrueDBGrid3.GroupByCaption = "Drag a column header here to group by that column"
        Me.C1TrueDBGrid3.Images.Add(CType(resources.GetObject("C1TrueDBGrid3.Images"),System.Drawing.Image))
        Me.C1TrueDBGrid3.Location = New System.Drawing.Point(0, 24)
        Me.C1TrueDBGrid3.Name = "C1TrueDBGrid3"
        Me.C1TrueDBGrid3.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.C1TrueDBGrid3.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.C1TrueDBGrid3.PreviewInfo.ZoomFactor = 75R
        Me.C1TrueDBGrid3.PrintInfo.MeasurementDevice = C1.Win.C1TrueDBGrid.PrintInfo.MeasurementDeviceEnum.Screen
        Me.C1TrueDBGrid3.PrintInfo.MeasurementPrinterName = Nothing
        Me.C1TrueDBGrid3.Size = New System.Drawing.Size(953, 392)
        Me.C1TrueDBGrid3.TabIndex = 10
        Me.C1TrueDBGrid3.Text = "3"
        Me.C1TrueDBGrid3.PropBag = resources.GetString("C1TrueDBGrid3.PropBag")
        '
        'C1TrueDBGrid4
        '
        Me.C1TrueDBGrid4.Dock = System.Windows.Forms.DockStyle.Fill
        Me.C1TrueDBGrid4.GroupByCaption = "Drag a column header here to group by that column"
        Me.C1TrueDBGrid4.Images.Add(CType(resources.GetObject("C1TrueDBGrid4.Images"),System.Drawing.Image))
        Me.C1TrueDBGrid4.Location = New System.Drawing.Point(0, 24)
        Me.C1TrueDBGrid4.Name = "C1TrueDBGrid4"
        Me.C1TrueDBGrid4.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.C1TrueDBGrid4.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.C1TrueDBGrid4.PreviewInfo.ZoomFactor = 75R
        Me.C1TrueDBGrid4.PrintInfo.MeasurementDevice = C1.Win.C1TrueDBGrid.PrintInfo.MeasurementDeviceEnum.Screen
        Me.C1TrueDBGrid4.PrintInfo.MeasurementPrinterName = Nothing
        Me.C1TrueDBGrid4.Size = New System.Drawing.Size(953, 392)
        Me.C1TrueDBGrid4.TabIndex = 11
        Me.C1TrueDBGrid4.Text = "3"
        Me.C1TrueDBGrid4.PropBag = resources.GetString("C1TrueDBGrid4.PropBag")
        '
        'C1TrueDBGrid5
        '
        Me.C1TrueDBGrid5.Dock = System.Windows.Forms.DockStyle.Fill
        Me.C1TrueDBGrid5.GroupByCaption = "Drag a column header here to group by that column"
        Me.C1TrueDBGrid5.Images.Add(CType(resources.GetObject("C1TrueDBGrid5.Images"),System.Drawing.Image))
        Me.C1TrueDBGrid5.Location = New System.Drawing.Point(0, 24)
        Me.C1TrueDBGrid5.Name = "C1TrueDBGrid5"
        Me.C1TrueDBGrid5.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.C1TrueDBGrid5.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.C1TrueDBGrid5.PreviewInfo.ZoomFactor = 75R
        Me.C1TrueDBGrid5.PrintInfo.MeasurementDevice = C1.Win.C1TrueDBGrid.PrintInfo.MeasurementDeviceEnum.Screen
        Me.C1TrueDBGrid5.PrintInfo.MeasurementPrinterName = Nothing
        Me.C1TrueDBGrid5.Size = New System.Drawing.Size(953, 392)
        Me.C1TrueDBGrid5.TabIndex = 12
        Me.C1TrueDBGrid5.Text = "3"
        Me.C1TrueDBGrid5.PropBag = resources.GetString("C1TrueDBGrid5.PropBag")
        '
        'C1TrueDBGrid6
        '
        Me.C1TrueDBGrid6.Dock = System.Windows.Forms.DockStyle.Fill
        Me.C1TrueDBGrid6.GroupByCaption = "Drag a column header here to group by that column"
        Me.C1TrueDBGrid6.Images.Add(CType(resources.GetObject("C1TrueDBGrid6.Images"),System.Drawing.Image))
        Me.C1TrueDBGrid6.Location = New System.Drawing.Point(0, 24)
        Me.C1TrueDBGrid6.Name = "C1TrueDBGrid6"
        Me.C1TrueDBGrid6.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.C1TrueDBGrid6.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.C1TrueDBGrid6.PreviewInfo.ZoomFactor = 75R
        Me.C1TrueDBGrid6.PrintInfo.MeasurementDevice = C1.Win.C1TrueDBGrid.PrintInfo.MeasurementDeviceEnum.Screen
        Me.C1TrueDBGrid6.PrintInfo.MeasurementPrinterName = Nothing
        Me.C1TrueDBGrid6.Size = New System.Drawing.Size(953, 392)
        Me.C1TrueDBGrid6.TabIndex = 13
        Me.C1TrueDBGrid6.Text = "3"
        Me.C1TrueDBGrid6.PropBag = resources.GetString("C1TrueDBGrid6.PropBag")
        '
        'Zy_Fy1
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6!, 12!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(953, 437)
        Me.Controls.Add(Me.C1TrueDBGrid6)
        Me.Controls.Add(Me.C1TrueDBGrid5)
        Me.Controls.Add(Me.C1TrueDBGrid4)
        Me.Controls.Add(Me.C1TrueDBGrid3)
        Me.Controls.Add(Me.C1TrueDBGrid2)
        Me.Controls.Add(Me.C1TrueDBGrid1)
        Me.Controls.Add(Me.Panel1)
        Me.Controls.Add(Me.Panel2)
        Me.Icon = CType(resources.GetObject("$this.Icon"),System.Drawing.Icon)
        Me.Name = "Zy_Fy1"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "住院发药"
        CType(Me.C1CommandHolder1,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.C1TrueDBGrid1,System.ComponentModel.ISupportInitialize).EndInit
        Me.Panel1.ResumeLayout(false)
        Me.Panel1.PerformLayout
        CType(Me.C1Combo1,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.T_Label,System.ComponentModel.ISupportInitialize).EndInit
        Me.Panel2.ResumeLayout(false)
        Me.Panel2.PerformLayout
        CType(Me.C1TrueDBGrid2,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.C1TrueDBGrid3,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.C1TrueDBGrid4,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.C1TrueDBGrid5,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.C1TrueDBGrid6,System.ComponentModel.ISupportInitialize).EndInit
        Me.ResumeLayout(false)

End Sub
    Friend WithEvents C1CommandHolder1 As C1.Win.C1Command.C1CommandHolder
    Friend WithEvents C1TrueDBGrid1 As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents C1ToolBar1 As C1.Win.C1Command.C1ToolBar
    Friend WithEvents Comm2 As C1.Win.C1Command.C1Command
    Friend WithEvents C1CommandLink2 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents T_Line2 As System.Windows.Forms.Label
    Friend WithEvents T_Label As C1.Win.C1Input.C1Label
    Friend WithEvents Panel2 As System.Windows.Forms.Panel
    Friend WithEvents T_Label3 As System.Windows.Forms.Label
    Friend WithEvents T_Label4 As System.Windows.Forms.Label
    Friend WithEvents C1TrueDBGrid2 As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents CheckBox1 As System.Windows.Forms.CheckBox
    Friend WithEvents C1Combo1 As C1.Win.C1List.C1Combo
    Friend WithEvents C1TrueDBGrid4 As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents C1TrueDBGrid3 As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents Comm3 As C1.Win.C1Command.C1Command
    Friend WithEvents C1CommandLink3 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents C1TrueDBGrid5 As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents C1TrueDBGrid6 As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents T_Textbox As CustomControl.MyTextBox
End Class
