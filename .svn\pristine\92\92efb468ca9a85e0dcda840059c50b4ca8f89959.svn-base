﻿Imports C1.Win.C1TrueDBGrid
Imports System.Windows.Forms


Public Class mMenuStrip
    Inherits System.Windows.Forms.ContextMenuStrip
    Dim Lb As String
    Dim TrueDBGrid As C1TrueDBGrid
    Protected Overrides Sub OnPaint(ByVal e As System.Windows.Forms.PaintEventArgs)
        MyBase.OnPaint(e)

        '在此处添加自定义绘制代码
    End Sub

    Public Property MyLb() As String
        Get
            Return Lb
        End Get
        Set(ByVal value As String)
            Lb = value
        End Set
    End Property

    Public Property MyC1TrueDBGrid() As C1TrueDBGrid
        Get
            Return TrueDBGrid
        End Get
        Set(ByVal value As C1TrueDBGrid)
            TrueDBGrid = value
        End Set
    End Property


    Private Sub mMenuStrip_ItemClicked(ByVal sender As System.Object, ByVal e As System.Windows.Forms.ToolStripItemClickedEventArgs) Handles MyBase.ItemClicked
        If e.ClickedItem.Text <> "展开所有节点" And e.ClickedItem.Text <> "收缩所有节点" Then
            TrueDBGrid.Splits(0).DisplayColumns(e.ClickedItem.Tag).Visible = Not DirectCast(e.ClickedItem, ToolStripMenuItem).Checked
            WriteINI(Lb, e.ClickedItem.Tag, Not DirectCast(e.ClickedItem, ToolStripMenuItem).Checked, HisVar.HisVar.Parapath & "\System.ini")
        End If
    End Sub

    Private Sub init()
        Dim mitem As New ToolStripMenuItem
        mitem.Text = "展开所有节点"
        AddHandler mitem.Click, AddressOf Expand
        Me.Items.Insert(0, mitem)
        mitem = New ToolStripMenuItem
        mitem.Text = "收缩所有节点"
        AddHandler mitem.Click, AddressOf Collapse
        Me.Items.Insert(1, mitem)
        Dim m As New ToolStripSeparator
        Me.Items.Insert(2, m)
    End Sub

    Private Sub Expand()
        Me.TrueDBGrid.ExpandGroupRow(-1, True)
    End Sub
    Dim gr As C1.Win.C1TrueDBGrid.GroupRow = Nothing
    Private Sub Collapse()
        Me.TrueDBGrid.CollapseGroupRow(-1)
    End Sub

    Public Sub New()

        ' 此调用是设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        init()
    End Sub
End Class
