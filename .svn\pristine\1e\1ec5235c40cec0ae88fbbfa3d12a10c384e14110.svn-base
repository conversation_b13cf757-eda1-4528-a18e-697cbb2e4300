﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="0" />
    <DataSources isList="true" count="2">
      <标准版 Ref="2" type="DataTableSource" isKey="true">
        <Alias>标准版</Alias>
        <Columns isList="true" count="22">
          <value>V_Order,System.String</value>
          <value>Ry_Name,System.String</value>
          <value>Bl_Code,System.String</value>
          <value>Xx_Code,System.String</value>
          <value>Yp_Name,System.String</value>
          <value>Mx_Gg,System.String</value>
          <value>Mx_Cd,System.String</value>
          <value>Mx_XsDw,System.String</value>
          <value>Cf_Date,System.DateTime</value>
          <value>Cf_Sl,System.Decimal</value>
          <value>Cf_Dj,System.Decimal</value>
          <value>Cf_Money,System.Decimal</value>
          <value>Ks_Name,System.String</value>
          <value>Bc_Name,System.String</value>
          <value>Ry_CyDate,System.String</value>
          <value>Ry_CyJsr,System.String</value>
          <value>Ry_BlCode,System.String</value>
          <value>Ry_ZyTs,System.Int32</value>
          <value>Bxlb_Name,System.String</value>
          <value>Cf_Lb,System.String</value>
          <value>Dl_Code,System.String</value>
          <value>Dl_Name,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>标准版</Name>
        <NameInSource>标准版</NameInSource>
      </标准版>
      <押金及余额 Ref="3" type="DataTableSource" isKey="true">
        <Alias>押金及余额</Alias>
        <Columns isList="true" count="4">
          <value>New_Money,System.Decimal</value>
          <value>Jf_Money,System.Decimal</value>
          <value>Xf_Money,System.Decimal</value>
          <value>Bl_Code,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>押金及余额</Name>
        <NameInSource>押金及余额</NameInSource>
      </押金及余额>
    </DataSources>
    <Relations isList="true" count="1">
      <名称 Ref="4" type="DataRelation" isKey="true">
        <Alias>名称</Alias>
        <ChildColumns isList="true" count="1">
          <value>Bl_Code</value>
        </ChildColumns>
        <ChildSource isRef="2" />
        <Dictionary isRef="1" />
        <Name>名称</Name>
        <NameInSource>关系</NameInSource>
        <ParentColumns isList="true" count="1">
          <value>Bl_Code</value>
        </ParentColumns>
        <ParentSource isRef="3" />
      </名称>
    </Relations>
    <Report isRef="0" />
    <Variables isList="true" count="9">
      <value>,医院名称,医院名称,System.String,,False,False</value>
      <value>,患者姓名,患者姓名,System.String,,False,False</value>
      <value>,科室,科室,System.String,,False,False</value>
      <value>,项目,项目,System.String,,False,False</value>
      <value>,应收金额,应收金额,System.String,,False,False</value>
      <value>,实收金额,实收金额,System.String,,False,False</value>
      <value>,金额大写,金额大写,System.String,,False,False</value>
      <value>,收费员,收费员,System.String,,False,False</value>
      <value>,日期,日期,System.String,,False,False</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="5" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="3">
        <Text29 Ref="6" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>-0.1,0.3,22,5</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>宋体,9,Regular,Point,False,134</Font>
          <Guid>e1204aea7a7c45c2879971a1c7adbc1d</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text29</Name>
          <Page isRef="5" />
          <Parent isRef="5" />
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text29>
        <GroupHeaderBand1 Ref="7" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,22,5.5</ClientRectangle>
          <Components isList="true" count="52">
            <Text1 Ref="8" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,11,2</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,14.25,Regular,Point,False,0</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text1</Name>
              <Page isRef="5" />
              <Parent isRef="7" />
              <Text>河北省门诊统一收费收据</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text1>
            <Text2 Ref="9" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>11,0,11,2</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,14.25,Regular,Point,False,0</Font>
              <Guid>78c96ae8c80e40ce9c306e9ca1c27f52</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text2</Name>
              <Page isRef="5" />
              <Parent isRef="7" />
              <Text>河北省门诊统一收费收据</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text2>
            <Text3 Ref="10" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,2,11,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text3</Name>
              <Page isRef="5" />
              <Parent isRef="7" />
              <TextBrush>Black</TextBrush>
            </Text3>
            <Text4 Ref="11" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>11,2,11,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>51f9368abe414c59883e5dec4158bc52</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text4</Name>
              <Page isRef="5" />
              <Parent isRef="7" />
              <TextBrush>Black</TextBrush>
            </Text4>
            <Text5 Ref="12" type="Text" isKey="true">
              <Brush>EmptyBrush</Brush>
              <ClientRectangle>0,2,1.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text5</Name>
              <Page isRef="5" />
              <Parent isRef="7" />
              <TextBrush>Black</TextBrush>
            </Text5>
            <Text6 Ref="13" type="Text" isKey="true">
              <Brush>EmptyBrush</Brush>
              <ClientRectangle>1.5,2,1.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,8</Font>
              <Guid>9bdfd285c40147b1be5bc72f5e456e42</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text6</Name>
              <Page isRef="5" />
              <Parent isRef="7" />
              <Text>医院名称</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text6>
            <Text7 Ref="14" type="Text" isKey="true">
              <Brush>EmptyBrush</Brush>
              <ClientRectangle>3,2,3.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>1a66d92532004aec8641201dbb9750c1</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text7</Name>
              <Page isRef="5" />
              <Parent isRef="7" />
              <Text>{医院名称}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text7>
            <Text8 Ref="15" type="Text" isKey="true">
              <Brush>EmptyBrush</Brush>
              <ClientRectangle>6.5,2,4.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>a0258df2185a4f7398bff1a7de0a1d41</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text8</Name>
              <Page isRef="5" />
              <Parent isRef="7" />
              <TextBrush>Black</TextBrush>
            </Text8>
            <Text9 Ref="16" type="Text" isKey="true">
              <Brush>EmptyBrush</Brush>
              <ClientRectangle>11,2,1.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>bdd0bb22cf4742e9bf78720664281f40</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text9</Name>
              <Page isRef="5" />
              <Parent isRef="7" />
              <TextBrush>Black</TextBrush>
            </Text9>
            <Text10 Ref="17" type="Text" isKey="true">
              <Brush>EmptyBrush</Brush>
              <ClientRectangle>12.5,2,1.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,8</Font>
              <Guid>c7e99d81abc94537aa95e9ce533c1221</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text10</Name>
              <Page isRef="5" />
              <Parent isRef="7" />
              <Text>医院名称</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text10>
            <Text11 Ref="18" type="Text" isKey="true">
              <Brush>EmptyBrush</Brush>
              <ClientRectangle>14,2,3.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>50cd6e967bd540059a7b14b45a34f88b</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text11</Name>
              <Page isRef="5" />
              <Parent isRef="7" />
              <Text>{医院名称}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text11>
            <Text12 Ref="19" type="Text" isKey="true">
              <Brush>EmptyBrush</Brush>
              <ClientRectangle>17.5,2,4.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>c2ecf5dcb312402b94d3eadea3f90622</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text12</Name>
              <Page isRef="5" />
              <Parent isRef="7" />
              <TextBrush>Black</TextBrush>
            </Text12>
            <Text13 Ref="20" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,2.5,1.4,2.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text13</Name>
              <Page isRef="5" />
              <Parent isRef="7" />
              <TextBrush>Black</TextBrush>
            </Text13>
            <Text14 Ref="21" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.4,2.5,8.4,2.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>c70dfe041b4040109a6a9ec970d12452</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text14</Name>
              <Page isRef="5" />
              <Parent isRef="7" />
              <TextBrush>Black</TextBrush>
            </Text14>
            <Text15 Ref="22" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>9.8,2.5,1.2,2.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>53b980e478e34d5d8feb6afa63fdd338</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text15</Name>
              <Page isRef="5" />
              <Parent isRef="7" />
              <TextBrush>Black</TextBrush>
            </Text15>
            <Text16 Ref="23" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>11,2.5,1.4,2.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>6b6611bb3c9048e68624d3334abf8e58</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text16</Name>
              <Page isRef="5" />
              <Parent isRef="7" />
              <TextBrush>Black</TextBrush>
            </Text16>
            <Text17 Ref="24" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>12.4,2.5,8.4,2.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>eb926136aaad49afb9f6f3d9959e2d9d</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text17</Name>
              <Page isRef="5" />
              <Parent isRef="7" />
              <TextBrush>Black</TextBrush>
            </Text17>
            <Text18 Ref="25" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>20.8,2.5,1.2,2.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>8e40c66bf10a42dd8d9d886df5549d99</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text18</Name>
              <Page isRef="5" />
              <Parent isRef="7" />
              <TextBrush>Black</TextBrush>
            </Text18>
            <Text19 Ref="26" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>EmptyBrush</Brush>
              <ClientRectangle>1.4,2.5,1.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,8</Font>
              <Guid>bf507bf0c24a42ce8d1ffeb58b1bec33</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text19</Name>
              <Page isRef="5" />
              <Parent isRef="7" />
              <Text>姓名</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text19>
            <Text20 Ref="27" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>EmptyBrush</Brush>
              <ClientRectangle>2.6,2.5,1.3,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>bcb87483a9474546be52547c6f7edc64</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text20</Name>
              <Page isRef="5" />
              <Parent isRef="7" />
              <Text>{患者姓名}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text20>
            <Text21 Ref="28" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>EmptyBrush</Brush>
              <ClientRectangle>3.9,2.5,1.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,8</Font>
              <Guid>d5d1e2501a7f40df82ed4eaff5d8cdf3</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text21</Name>
              <Page isRef="5" />
              <Parent isRef="7" />
              <Text>科室</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text21>
            <Text22 Ref="29" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>EmptyBrush</Brush>
              <ClientRectangle>5.1,2.5,1.5,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>bffde4c5f14c4e699d36a54b0b51247e</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text22</Name>
              <Page isRef="5" />
              <Parent isRef="7" />
              <Text>{科室}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text22>
            <Text23 Ref="30" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>EmptyBrush</Brush>
              <ClientRectangle>6.6,2.5,1.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,8</Font>
              <Guid>6d534ed57d1f43418b12f065295fd9f0</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text23</Name>
              <Page isRef="5" />
              <Parent isRef="7" />
              <Text>项目</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text23>
            <Text24 Ref="31" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>EmptyBrush</Brush>
              <ClientRectangle>7.8,2.5,2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>94285e2b366742c085a4ce902962a3f8</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text24</Name>
              <Page isRef="5" />
              <Parent isRef="7" />
              <Text>{项目}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text24>
            <Text25 Ref="32" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>EmptyBrush</Brush>
              <ClientRectangle>1.4,3.3,1.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,8</Font>
              <Guid>32fccdfc0f79417d8a653144bf5e9f3d</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text25</Name>
              <Page isRef="5" />
              <Parent isRef="7" />
              <Text>应收金额</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text25>
            <Text26 Ref="33" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>EmptyBrush</Brush>
              <ClientRectangle>6.4,3.3,1.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,8</Font>
              <Guid>6e8851289bf043dda43d3de39911f064</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text26</Name>
              <Page isRef="5" />
              <Parent isRef="7" />
              <Text>实收金额</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text26>
            <Text27 Ref="34" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>EmptyBrush</Brush>
              <ClientRectangle>3,3.3,3.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>cc5741e384aa496f8ec3d3ba1666efb6</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text27</Name>
              <Page isRef="5" />
              <Parent isRef="7" />
              <Text>{应收金额}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text27>
            <Text28 Ref="35" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>EmptyBrush</Brush>
              <ClientRectangle>8,3.3,1.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>9cdfc414ce124b3bbc769137f252cdd9</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text28</Name>
              <Page isRef="5" />
              <Parent isRef="7" />
              <Text>{实收金额}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text28>
            <Text30 Ref="36" type="Text" isKey="true">
              <Brush>EmptyBrush</Brush>
              <ClientRectangle>1.4,4.1,1.2,0.9</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>66d2ab1c5d9f49caab7d413d9051847b</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text30</Name>
              <Page isRef="5" />
              <Parent isRef="7" />
              <Text>大写</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text30>
            <Text31 Ref="37" type="Text" isKey="true">
              <Brush>EmptyBrush</Brush>
              <ClientRectangle>2.6,4.1,7.2,0.9</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>d506e4aa5f514fddb915b7ae06d799c7</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text31</Name>
              <Page isRef="5" />
              <Parent isRef="7" />
              <Text>{金额大写}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text31>
            <Text32 Ref="38" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>EmptyBrush</Brush>
              <ClientRectangle>12.4,2.5,1.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,8</Font>
              <Guid>02e1db0e210643f2a68548350c0f2709</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text32</Name>
              <Page isRef="5" />
              <Parent isRef="7" />
              <Text>姓名</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text32>
            <Text33 Ref="39" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>EmptyBrush</Brush>
              <ClientRectangle>13.6,2.5,1.3,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>9726e20375d941e0bbee092a46bbf8ea</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text33</Name>
              <Page isRef="5" />
              <Parent isRef="7" />
              <Text>{患者姓名}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text33>
            <Text34 Ref="40" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>EmptyBrush</Brush>
              <ClientRectangle>14.9,2.5,1.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,8</Font>
              <Guid>899f7d20f1d344c9ad3986069d44dd6f</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text34</Name>
              <Page isRef="5" />
              <Parent isRef="7" />
              <Text>科室</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text34>
            <Text35 Ref="41" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>EmptyBrush</Brush>
              <ClientRectangle>16.1,2.5,1.5,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>987765921c2b43a1ae83a3d5adee794a</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text35</Name>
              <Page isRef="5" />
              <Parent isRef="7" />
              <Text>{科室}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text35>
            <Text36 Ref="42" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>EmptyBrush</Brush>
              <ClientRectangle>17.6,2.5,1.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,8</Font>
              <Guid>3d83a2ff31bd4b95b37e6cb8df9a50d6</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text36</Name>
              <Page isRef="5" />
              <Parent isRef="7" />
              <Text>项目</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text36>
            <Text37 Ref="43" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>EmptyBrush</Brush>
              <ClientRectangle>18.8,2.5,2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>f5064998364c473b873b803892e70386</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text37</Name>
              <Page isRef="5" />
              <Parent isRef="7" />
              <Text>{项目}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text37>
            <Text38 Ref="44" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>EmptyBrush</Brush>
              <ClientRectangle>12.4,3.3,1.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,8</Font>
              <Guid>aa8b3e3e0dcb4ec28f56eed93c7f631d</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text38</Name>
              <Page isRef="5" />
              <Parent isRef="7" />
              <Text>应收金额</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text38>
            <Text39 Ref="45" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>EmptyBrush</Brush>
              <ClientRectangle>14,3.3,3.4,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>aab94fac420446b1bb1b0e14679d5c15</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text39</Name>
              <Page isRef="5" />
              <Parent isRef="7" />
              <Text>{应收金额}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text39>
            <Text40 Ref="46" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>EmptyBrush</Brush>
              <ClientRectangle>17.4,3.3,1.6,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,8</Font>
              <Guid>7f5768222cd14d7ea4d36a8c550b96bd</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text40</Name>
              <Page isRef="5" />
              <Parent isRef="7" />
              <Text>实收金额</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text40>
            <Text41 Ref="47" type="Text" isKey="true">
              <Border>Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>EmptyBrush</Brush>
              <ClientRectangle>19,3.3,1.8,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>0631872df46442789497169f2c4a53f2</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text41</Name>
              <Page isRef="5" />
              <Parent isRef="7" />
              <Text>{实收金额}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text41>
            <Text42 Ref="48" type="Text" isKey="true">
              <Brush>EmptyBrush</Brush>
              <ClientRectangle>12.4,4.1,1.2,0.9</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>592298b4c49f42358a0a58ceb43e3fd9</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text42</Name>
              <Page isRef="5" />
              <Parent isRef="7" />
              <Text>大写</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text42>
            <Text43 Ref="49" type="Text" isKey="true">
              <Brush>EmptyBrush</Brush>
              <ClientRectangle>13.6,4.1,7.2,0.9</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>c909191e04cf4dfcbd46debf2ff84278</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text43</Name>
              <Page isRef="5" />
              <Parent isRef="7" />
              <Text>{金额大写}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text43>
            <Text44 Ref="50" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,5,1.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text44</Name>
              <Page isRef="5" />
              <Parent isRef="7" />
              <TextBrush>Black</TextBrush>
            </Text44>
            <Text45 Ref="51" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.5,5,1.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,8</Font>
              <Guid>3909550106b44726962a1ce12cacc90e</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text45</Name>
              <Page isRef="5" />
              <Parent isRef="7" />
              <Text>收费员</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text45>
            <Text46 Ref="52" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>3,5,4.2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>0bbc8bbcfe4f430e88392b1c121e8475</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text46</Name>
              <Page isRef="5" />
              <Parent isRef="7" />
              <Text>{收费员}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text46>
            <Text47 Ref="53" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7.2,5,0.8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,8</Font>
              <Guid>476c44b17d77409f85eaec04c513efb8</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text47</Name>
              <Page isRef="5" />
              <Parent isRef="7" />
              <Text>日期</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text47>
            <Text48 Ref="54" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>8,5,3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>762eca59f605426b80ffab5ca0f5471a</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text48</Name>
              <Page isRef="5" />
              <Parent isRef="7" />
              <Text>{日期}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text48>
            <Text49 Ref="55" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>11,5,1.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>62a149450a714060b2b25021f0c3bfc3</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text49</Name>
              <Page isRef="5" />
              <Parent isRef="7" />
              <TextBrush>Black</TextBrush>
            </Text49>
            <Text50 Ref="56" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>12.5,5,1.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,8</Font>
              <Guid>c6f80353961d487cb60384ce19d97a59</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text50</Name>
              <Page isRef="5" />
              <Parent isRef="7" />
              <Text>收费员</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text50>
            <Text51 Ref="57" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>14,5,4.2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>58d2a51a1c104d498ff60ecab7b537c4</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text51</Name>
              <Page isRef="5" />
              <Parent isRef="7" />
              <Text>{收费员}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text51>
            <Text52 Ref="58" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>18.2,5,0.8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,8</Font>
              <Guid>48b2c0295459404aad0ef925976a4793</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text52</Name>
              <Page isRef="5" />
              <Parent isRef="7" />
              <Text>日期</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text52>
            <Text53 Ref="59" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>19,5,3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>d95dba42c73d4c87be43876865e1b6ec</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text53</Name>
              <Page isRef="5" />
              <Parent isRef="7" />
              <Text>{日期}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text53>
          </Components>
          <Condition>{标准版.Bl_Code}</Condition>
          <Conditions isList="true" count="0" />
          <Name>GroupHeaderBand1</Name>
          <Page isRef="5" />
          <Parent isRef="5" />
        </GroupHeaderBand1>
        <DataBand1 Ref="60" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,6.7,22,5</ClientRectangle>
          <Components isList="true" count="6">
            <Text54 Ref="61" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.5,0,3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,8</Font>
              <Guid>7c4e00a850cf472ab0e93acffb17aef7</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text54</Name>
              <Page isRef="5" />
              <Parent isRef="60" />
              <Text>{标准版.Yp_Name}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text54>
            <Text55 Ref="62" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,1.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>8f21475f5fa44e168ca9ee3648904a63</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text55</Name>
              <Page isRef="5" />
              <Parent isRef="60" />
              <TextBrush>Black</TextBrush>
            </Text55>
            <Text57 Ref="63" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.5,0,2.1,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,8</Font>
              <Guid>a957569daa3549929148a43377ac3e2a</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text57</Name>
              <Page isRef="5" />
              <Parent isRef="60" />
              <Text>{标准版.Cf_Sl}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text57>
            <Text56 Ref="64" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>6.6,0,1.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,8</Font>
              <Guid>bfc523abb00e4a779df475016accbe48</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text56</Name>
              <Page isRef="5" />
              <Parent isRef="60" />
              <Text>{标准版.Cf_Dj}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text56>
            <Text58 Ref="65" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>8.5,0,1.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,8</Font>
              <Guid>dae8391cb38142f9a138a5b0354217d3</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text58</Name>
              <Page isRef="5" />
              <Parent isRef="60" />
              <Text>{标准版.Cf_Money}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text58>
            <Text59 Ref="66" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>10.4,0,0.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>1ae7bbbcb3f34454886eac85ff7e300a</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text59</Name>
              <Page isRef="5" />
              <Parent isRef="60" />
              <TextBrush>Black</TextBrush>
            </Text59>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName>关系</DataRelationName>
          <DataSourceName>标准版</DataSourceName>
          <Filters isList="true" count="0" />
          <Name>DataBand1</Name>
          <Page isRef="5" />
          <Parent isRef="5" />
          <Sort isList="true" count="0" />
        </DataBand1>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>e67f634a845f412dbae15ab107312b84</Guid>
      <Margins>0,0,0,0</Margins>
      <Name>Page1</Name>
      <PageHeight>12</PageHeight>
      <PageWidth>22</PageWidth>
      <PaperSize>A4</PaperSize>
      <Report isRef="0" />
      <Watermark Ref="67" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page1>
  </Pages>
  <PrinterSettings Ref="68" type="Stimulsoft.Report.Print.StiPrinterSettings" isKey="true" />
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>患者用药清单标准版</ReportAlias>
  <ReportChanged>6/15/2013 4:00:52 PM</ReportChanged>
  <ReportCreated>1/9/2012 9:49:05 AM</ReportCreated>
  <ReportFile>C:\Documents and Settings\Administrator\桌面\河北省统一门诊收据.mrt</ReportFile>
  <ReportGuid>8ca381d6df444692b36d3c09cf8ecb2f</ReportGuid>
  <ReportName>患者用药清单标准版</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2011.2.1026</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class 患者用药清单标准版 : Stimulsoft.Report.StiReport
    {
        public 患者用药清单标准版()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
        #endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>