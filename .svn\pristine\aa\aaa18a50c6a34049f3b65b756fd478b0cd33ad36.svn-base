﻿/**  版本信息模板在安装目录下，可自行修改。
* M_DRYB_ZYJS.cs
*
* 功 能： N/A
* 类 名： M_DRYB_ZYJS
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2017/12/15 10:05:04   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace ModelOld
{
	/// <summary>
	/// M_DRYB_ZYJS:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class M_DRYB_ZYJS
	{
		public M_DRYB_ZYJS()
		{}
		#region Model
		private int _id;
		private string _akc190;
		private string _aka130;
		private string _dj_code;
		private string _zy_jsfs;
		private decimal _jf_money;
		private decimal? _accountmoney;
		private DateTime _lrdate= DateTime.Now;
		/// <summary>
		/// 住院结算记录表
		/// </summary>
		public int Id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 住院号
		/// </summary>
		public string AKC190
		{
			set{ _akc190=value;}
			get{return _akc190;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string AKA130
		{
			set{ _aka130=value;}
			get{return _aka130;}
		}
		/// <summary>
		/// 单据号即对于一次结算生成的收据号---不同于住院（门诊号）的唯一表示号码
		/// </summary>
		public string Dj_Code
		{
			set{ _dj_code=value;}
			get{return _dj_code;}
		}
		/// <summary>
		/// 住院结算方式：1-普通住院结算；2-中途结算，3-年终结算
		/// </summary>
		public string ZY_JSFS
		{
			set{ _zy_jsfs=value;}
			get{return _zy_jsfs;}
		}
		/// <summary>
		/// 费用总额为KC22中SUM（AKC227），格式为0.00
		/// </summary>
		public decimal JF_MONEY
		{
			set{ _jf_money=value;}
			get{return _jf_money;}
		}
		/// <summary>
		/// 本次结算使用账户金额：若传0，则本次结算不使用账户支付。传入的金额不能大于患者当前账户余额
		/// </summary>
		public decimal? AccountMoney
		{
			set{ _accountmoney=value;}
			get{return _accountmoney;}
		}
		/// <summary>
		/// 
		/// </summary>
		public DateTime LrDate
		{
			set{ _lrdate=value;}
			get{return _lrdate;}
		}
		#endregion Model

	}
}

