<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="Image1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <data name="Image1.ImageStream" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>
        AAEAAAD/////AQAAAAAAAAAMAgAAAFdTeXN0ZW0uV2luZG93cy5Gb3JtcywgVmVyc2lvbj00LjAuMC4w
        LCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAACZTeXN0
        ZW0uV2luZG93cy5Gb3Jtcy5JbWFnZUxpc3RTdHJlYW1lcgEAAAAERGF0YQcCAgAAAAkDAAAADwMAAAAM
        BwAAAk1TRnQBSQFMAgEBAgEAASQBAAEkAQABEAEAARABAAT/ARkBAAj/AUIBTQE2BwABNgMAASgDAAFA
        AwABEAMAAQEBAAEYBgABDBgAAegB6gHtAbkBvwHTAdAB0wHcHuwD9gYAAegB6gHtAbkBvwHTAdAB0wHc
        D+wBDwGCAS4BAwF7AR4B3AHiAd0G7AP2YAAD/gP4ASwBSwGlATUBXQGvASEBQgGrA9wD2APUA9ID0QPT
        A9EDywPNA8cD7AP+A/gBLAFLAaUBNQFdAa8BIQFCAasD3APYA9QD0gPRAQkBhgEyAUMBoQFfARUBgwEx
        Aa4BwAGxA8cD7GAAA/cDzQHEAcYBygHAAcYBzAEhAUMBrAP9A/sD+AP0A+0D1QPpCQAD7AP3A80BxAHG
        AcoBwAHGAcwBIQFDAawD/QEiAZcBUgEcAZEBSgEWAY8BRAEQAYsBPAE6AZ8BXgGAAcEBlgFGAaMBYgEY
        AYkBNAHVAekB2QPsYAAD/gPqAWMBeQGzAWEBjwG/ASIBRgGuBvoD9gPzA+4D8wPKA+YD+wP8A+wD/gPq
        AWMBeQGzAWEBjwG/ASIBRgGuA/oBKQGbAVsBkAHKAakBjQHIAaUBigHGAaEBiAHFAZ4BagG2AYUBggHC
        AZcBSAGlAWYBFQGHATMB1AHcAdZgAAP3A80BxAHGAcoBwAHGAcwBIgFJAa4G+gP4A/QD8QP7A/IDygPm
        A/wD7AP3A80BxAHGAcoBwAHGAcwBIgFJAa4D+gExAZ8BYwGUAc0BrQFvAboBjgFrAbgBiQFmAbYBhQFh
        AbMBgAFnAbUBggGDAcIBmAE8AaABXAEEAYABKWAAA/4D6gFkAXwBswFjAZIBwQEjAUsBrwn5A/YD8wb6
        A/IDyQPoA+wD/gPqAWQBfAGzAWMBkgHBASMBSwGvA/kBNwGjAWsBlgHOAbABlAHNAa0BkQHLAaoBkAHL
        AagBdAG8AZABigHHAaEBRgGlAWgBCQGIATYB5QHnAeZgAAP3A80BxAHGAcoBwAHGAc0BIwFOAbED+gP5
        A/gD9wP2CfoD8gPRA+sD9wPNAcQBxgHKAcABxgHNASMBTgGxA/oBPQGlAW8BOQGkAW4BNQGiAWgBMQGe
        AWIBVQGvAXwBkQHLAaoBTwGrAXQBGQGQAUYByAHOAcoD62AAA/4D6gFlAX8BtQFkAZQBwgEkAVEBsgP6
        A/gB4AGfAXMB3QGdAXEB3AGaAW4B2gGZAWsB2QGYAWoB1AGTAWoD6gbsA/4D6gFlAX8BtQFkAZQBwgEk
        AVEBsgP6A/gB4AGfAXMB3QGdAXEB3AGaAW4BOAGfAWMBWgGzAYEBKAGYAVcB4wHnAeUG7GAAA/cDzQHE
        AcYBygHAAccBzQElAVIBsgP7A/oJ+AP3A/MD8gPwBu4D9wPNAcQBxgHKAcABxwHNASUBUgGyA/sD+gn4
        AT8BpgFwATEBnwFlAesB7wHtA/AG7mAAA/4D6gFmAYIBtQFlAZYBwwElAVUBtAb6AeABogF2AeABoAF2
        AeABoAF0Ad8BngFzAdwBnAFyAdwBmwFvA/ID9QPsA/4D6gFmAYIBtQFlAZYBwwElAVUBtAb6AeABogF2
        AeABoAF2AeABoAF0Ad8BngFzAdwBnAFyAdwBmwFvA/ID9QPsYAAD9wPNAcQBxgHKAcABxwHNASYBVgG1
        BvkB4QGjAXgB6gHAAaMB6gHAAaIB6gG/AaEB6gG+AaAB3wGeAXED9AP3A+wD9wPNAcQBxgHKAcABxwHN
        ASYBVgG1BvkB4QGjAXgB6gHAAaMB6gHAAaIB6gG/AaEB6gG+AaAB3wGeAXED9AP3A+xgAAP+A+oBZwGD
        AbcBZgGZAcQBJgFZAbcG+AHhAaUBegHhAaMBeAHhAaMBdwHgAaIBdgHgAaABdgHgAaABdAP0A/oD7AP+
        A+oBZwGDAbcBZgGZAcQBJgFZAbcG+AHhAaUBegHhAaMBeAHhAaMBdwHgAaIBdgHgAaABdgHgAaABdAP0
        A/oD7GAAA/cDzQHEAccBygHAAccBzQEmAVsBtw/3CfUD8wP6A+wD9wPNAcQBxwHKAcABxwHNASYBWwG3
        D/cJ9QPzA/oD7GAAA/4D6gFnAYcBuQFnAZoBxQEnAV4BuAz3A/UJ9APyA/sD7AP+A+oBZwGHAbkBZwGa
        AcUBJwFeAbgM9wP1CfQD8gP7A+xgAAP3A80BxAHHAcoBwQHHAc0BJwFfAbge/APsA/cDzQHEAccBygHB
        AccBzQEnAV8BuB78A+xjAAPyAWQBiwHCAUkBeAG+ASgBYQG6HuwD9gMAA/IBZAGLAcIBSQF4Ab4BKAFh
        Aboe7AP2YAABQgFNAT4HAAE+AwABKAMAAUADAAEQAwABAQEAAQEFAAGAFwAD/wEAAcABAAHADgABDmYA
        AYABAAGABQAL
</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="C1TrueDBGrid1.Images" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAAkAAAAJCAYAAADgkQYQAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAA0SURBVChTdYkBCgAgDAL9/6eLIsd0eSCKhw/r9aCLtC88
        vAdHMEIXKUIUhMK76EfagglgA6CqHOQpL6GyAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="C1TrueDBGrid1.PropBag" xml:space="preserve">
    <value>&lt;?xml version="1.0"?&gt;&lt;Blob&gt;&lt;Styles type="C1.Win.C1TrueDBGrid.Design.ContextWrapper"&gt;&lt;Data&gt;Caption{AlignHorz:Center;}ColumnSelector{}Editor{}EvenRow{BackColor:Aqua;}FilterBar{}FilterWatermark{BackColor:Info;ForeColor:InfoText;}Footer{}Group{BackColor:ControlDark;Border:None,,0, 0, 0, 0;AlignVert:Center;}Heading{BackColor:Control;ForeColor:ControlText;Border:Flat,ControlDark,0, 1, 0, 1;AlignVert:Center;WrapText:WrapWithOverflow;}HighlightRow{BackColor:Highlight;ForeColor:HighlightText;}Inactive{BackColor:InactiveCaption;ForeColor:InactiveCaptionText;}Normal{}OddRow{}RecordSelector{AlignImage:Center;}RowSelector{}Selected{BackColor:Highlight;ForeColor:HighlightText;}Style1{}Style10{AlignHorz:Near;}Style11{}Style12{}Style13{}Style14{}Style15{}Style16{}Style17{}Style18{}Style2{}Style3{}Style4{}Style5{}Style6{}Style7{}Style8{}Style9{}&lt;/Data&gt;&lt;/Styles&gt;&lt;Splits&gt;&lt;C1.Win.C1TrueDBGrid.MergeView Name="" CaptionHeight="18" ColumnCaptionHeight="18" ColumnFooterHeight="18" MarqueeStyle="DottedCellBorder" RecordSelectorWidth="17" DefRecSelWidth="17" VerticalScrollGroup="1" HorizontalScrollGroup="1"&gt;&lt;CaptionStyle parent="Style2" me="Style10" /&gt;&lt;EditorStyle parent="Editor" me="Style5" /&gt;&lt;EvenRowStyle parent="EvenRow" me="Style8" /&gt;&lt;FilterBarStyle parent="FilterBar" me="Style13" /&gt;&lt;FilterWatermarkStyle parent="FilterWatermark" me="Style14" /&gt;&lt;FooterStyle parent="Footer" me="Style3" /&gt;&lt;GroupStyle parent="Group" me="Style12" /&gt;&lt;HeadingStyle parent="Heading" me="Style2" /&gt;&lt;HighLightRowStyle parent="HighlightRow" me="Style7" /&gt;&lt;InactiveStyle parent="Inactive" me="Style4" /&gt;&lt;OddRowStyle parent="OddRow" me="Style9" /&gt;&lt;RecordSelectorStyle parent="RecordSelector" me="Style11" /&gt;&lt;RowSelectorStyle parent="RowSelector" me="Style17" /&gt;&lt;ColumnSelectorStyle parent="ColumnSelector" me="Style18" /&gt;&lt;SelectedStyle parent="Selected" me="Style6" /&gt;&lt;Style parent="Normal" me="Style1" /&gt;&lt;ClientRect&gt;0, 0, 847, 462&lt;/ClientRect&gt;&lt;BorderSide&gt;0&lt;/BorderSide&gt;&lt;/C1.Win.C1TrueDBGrid.MergeView&gt;&lt;/Splits&gt;&lt;NamedStyles&gt;&lt;Style parent="" me="Normal" /&gt;&lt;Style parent="Normal" me="Heading" /&gt;&lt;Style parent="Heading" me="Footer" /&gt;&lt;Style parent="Heading" me="Caption" /&gt;&lt;Style parent="Heading" me="Inactive" /&gt;&lt;Style parent="Normal" me="Selected" /&gt;&lt;Style parent="Normal" me="Editor" /&gt;&lt;Style parent="Normal" me="HighlightRow" /&gt;&lt;Style parent="Normal" me="EvenRow" /&gt;&lt;Style parent="Normal" me="OddRow" /&gt;&lt;Style parent="Heading" me="RecordSelector" /&gt;&lt;Style parent="Normal" me="FilterBar" /&gt;&lt;Style parent="FilterBar" me="FilterWatermark" /&gt;&lt;Style parent="Caption" me="Group" /&gt;&lt;Style parent="RecordSelector" me="RowSelector" /&gt;&lt;Style parent="Heading" me="ColumnSelector" /&gt;&lt;/NamedStyles&gt;&lt;vertSplits&gt;1&lt;/vertSplits&gt;&lt;horzSplits&gt;1&lt;/horzSplits&gt;&lt;Layout&gt;None&lt;/Layout&gt;&lt;DefaultRecSelWidth&gt;17&lt;/DefaultRecSelWidth&gt;&lt;ClientArea&gt;0, 0, 847, 462&lt;/ClientArea&gt;&lt;PrintPageHeaderStyle parent="" me="Style15" /&gt;&lt;PrintPageFooterStyle parent="" me="Style16" /&gt;&lt;/Blob&gt;</value>
  </data>
</root>