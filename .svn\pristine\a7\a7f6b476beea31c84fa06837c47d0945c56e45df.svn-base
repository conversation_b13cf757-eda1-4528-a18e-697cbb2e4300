﻿Imports BaseClass
Imports System.Windows.Forms
Imports System.Drawing

Public Class LIS_TestItem

#Region "变量定义"
    Dim V_Name As String                                            '简称是否发生变化
    Dim V_LbCount As Integer
    Dim Spell As New Chs2Spell
    Dim LIS_TestItemModel As New ModelOld.M_LIS_TestItem
    Dim LIS_TestItemBll As New BLLOld.B_LIS_TestItem

#End Region

#Region "传参"
    Dim Rinsert As Boolean
    Dim Rrow As DataRow
    Dim RZbtb As DataTable
    Dim Rrc As C_RowChange
    Dim RTestXm_Code As String
#End Region
    Public Sub New(ByVal tinsert As Boolean, ByVal trow As DataRow, ByVal tZbtb As DataTable, ByRef trc As C_RowChange, ByVal tZk As String)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        Rinsert = tinsert
        Rrow = trow
        RZbtb = tZbtb
        Rrc = trc
        RTestXm_Code = tZk

        ' 在 InitializeComponent() 调用之后添加任何初始化。
    End Sub

    Private Sub ZkDj2_FormClosed(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosedEventArgs) Handles Me.FormClosed
        RemoveHandler Rrc.RowChanged, AddressOf Data_Show
    End Sub

    Private Sub ZkDj2_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Me.Load
        AddHandler Rrc.RowChanged, AddressOf Data_Show
        Call Form_Init()
        If Rinsert = True Then Call Data_Clear() Else Call Data_Show(Rrow)


    End Sub

#Region "窗体__事件"

    Private Sub Form_Init()
        ItemOrderMyTextBox1.Enabled = False
        Code_TextBox.Enabled = False
        JcMyTextBox3.Enabled = False
        MaleMaxNumericEdit1.ValueIsDbNull = True
        MaleMinNumericEdit1.ValueIsDbNull = True
        FemaleMinNumericEdit2.ValueIsDbNull = True
        FemaleMaxNumericEdit3.ValueIsDbNull = True
        MaleMinNumericEdit1.Value = Nothing
        MaleMaxNumericEdit1.Value = Nothing
        FemaleMaxNumericEdit3.Value = Nothing
        FemaleMinNumericEdit2.Value = Nothing
        Panel1.Height = 27
        ToolBar1.Location = New Point(2, 4)

        '按扭初始化
        Comm1.Top = 2
        Comm2.Location = New Point(Comm1.Left + Comm1.Width + 2, Comm1.Top)
        Code_TextBox.Enabled = False

    End Sub

#End Region

#Region "数据__操作"

    Private Sub Data_Clear()
        Rinsert = True
        Code_TextBox.Text = LIS_TestItemBll.MaxCode
        Name_TextBox.Text = ""
        JcMyTextBox3.Text = ""
        DwMyTextBox1.Text = ""
        isDefaultCheckBox1.Checked = False
        MaleMinNumericEdit1.Value = Nothing
        MaleMaxNumericEdit1.Value = Nothing
        FemaleMaxNumericEdit3.Value = Nothing
        FemaleMinNumericEdit2.Value = Nothing
        ItemGroupMyTextBox2.Text = ""
        ItemOrderMyTextBox1.Value = LIS_TestItemBll.MaxOrder(RTestXm_Code)
        Memo_TextBox.Text = ""
        Call P_Show_Label()
    End Sub

    Private Sub Data_Show(ByVal tmp_Row As DataRow)
        Rinsert = False
        Rrow = tmp_Row
        With Rrow
            'TestXm_Code, TestItem_Code, Item_Name, Item_Jc, Item_Dw, MaleMin, MaleMax, FemaleMin, FemaleMax, isDefault, ItemGroup, ItemOrder, Memo
            Code_TextBox.Text = .Item("TestItem_Code") & ""
            Name_TextBox.Text = .Item("Item_Name") & ""
            JcMyTextBox3.Text = .Item("Item_Jc") & ""
            DwMyTextBox1.Text = .Item("Item_Dw") & ""
            isDefaultCheckBox1.Checked = .Item("isDefault")
            MaleMinNumericEdit1.Value = .Item("MaleMin")
            MaleMaxNumericEdit1.Value = .Item("MaleMax")
            FemaleMinNumericEdit2.Value = .Item("FemaleMin")
            FemaleMaxNumericEdit3.Value = .Item("FemaleMax")
            ItemGroupMyTextBox2.Text = .Item("ItemGroup") & ""
            ItemOrderMyTextBox1.Text = .Item("ItemOrder")
            Memo_TextBox.Text = .Item("Memo") & ""
            V_Name = Name_TextBox.Text
            CheckIsDefault()
        End With
        Call P_Show_Label()
    End Sub

    Private Sub CheckIsDefault()
        If isDefaultCheckBox1.Checked = True Then
            Name_TextBox.Enabled = False
            DwMyTextBox1.Enabled = False
            ItemGroupMyTextBox2.Enabled = False
            ItemOrderMyTextBox1.Enabled = False
            Memo_TextBox.Enabled = False
        Else
            Name_TextBox.Enabled = True
            DwMyTextBox1.Enabled = True
            ItemGroupMyTextBox2.Enabled = True
            Memo_TextBox.Enabled = True
        End If
    End Sub

    Private Sub P_Show_Label()

        If Rinsert = True Then
            Move5.Enabled = False                                           '新增记录
            T_Label2.Text = "新增"
        Else
            Move5.Enabled = True                                            '新增记录
            T_Label2.Text = IIf(RZbtb.Rows.Count() = 0, "0", CStr((RZbtb.Rows.IndexOf(Rrow)) + 1))
        End If
        T_Label3.Text = "∑=" & RZbtb.Rows.Count
        Name_TextBox.Select()
    End Sub

#End Region

#Region "控件__动作"

    Private Sub NameMyTextBox2_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles Name_TextBox.Validated
        JcMyTextBox3.Text = Spell.GetPy(Name_TextBox.Text & "")
    End Sub

    Private Sub isDefaultCheckBox1_CheckedChanged(sender As Object, e As System.EventArgs) Handles isDefaultCheckBox1.CheckedChanged
        If Rinsert = False Then
            CheckIsDefault()
        End If
    End Sub
    Private Sub isDefaultCheckBox1_KeyPress(sender As Object, e As System.Windows.Forms.KeyPressEventArgs) Handles isDefaultCheckBox1.KeyPress
        If e.KeyChar = Chr(Keys.Return) Then
            e.Handled = True
            System.Windows.Forms.SendKeys.Send("{Tab}")
        End If
    End Sub

    Private Sub Comm_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.Click, Comm2.Click
        Select Case sender.tag
            Case "保存"
                If Name_TextBox.Text.Trim = "" Then
                    MsgBox("请输入小项名称！", MsgBoxStyle.Exclamation, "提示")
                    Name_TextBox.Select()
                    Exit Sub
                End If
                V_LbCount = LIS_TestItemBll.GetRecordCount("Item_Name = '" & Trim(Name_TextBox.Text) & "'")
                If Rinsert = True Then
                    If V_LbCount > 0 Then
                        MsgBox("该小项名称已经存在！", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示")
                        Name_TextBox.Select()
                        Exit Sub
                    End If
                    Call Data_Add()
                Else
                    If V_LbCount > 0 And Name_TextBox.Text.Trim <> V_Name Then
                        MsgBox("该小项名称已经存在！", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示")
                        Name_TextBox.Select()
                        Exit Sub
                    End If
                    Call Data_Edit()
                End If
            Case "取消"
                Me.Close()
        End Select
    End Sub

    Private Sub Move_Click(ByVal sender As Object, ByVal e As C1.Win.C1Command.ClickEventArgs) Handles Move1.Click, Move2.Click, Move3.Click, Move4.Click, Move5.Click
        If sender.text = "新增" Then                            '增加状态
            Call Data_Clear()
        Else
            Rrc.GridMove(sender.text)
        End If
    End Sub




#End Region

#Region "数据__编辑"

    Private Sub Data_Add()
        Dim My_NewRow As DataRow = RZbtb.NewRow
        'TestXm_Code, TestItem_Code, Item_Name, Item_Jc, Item_Dw, MaleMin, MaleMax, FemaleMin, FemaleMax, isDefault, ItemGroup, ItemOrder, Memo
        With My_NewRow
            .Item("TestXm_Code") = RTestXm_Code
            .Item("TestItem_Code") = LIS_TestItemBll.MaxCode
            .Item("Item_Name") = Trim(Name_TextBox.Text & "")
            .Item("Item_Jc") = Trim(JcMyTextBox3.Text & "")
            .Item("Item_Dw") = Trim(DwMyTextBox1.Text & "")
            .Item("MaleMin") = MaleMinNumericEdit1.Value
            .Item("MaleMax") = MaleMaxNumericEdit1.Value
            .Item("FemaleMin") = FemaleMinNumericEdit2.Value
            .Item("FemaleMax") = FemaleMaxNumericEdit3.Value
            .Item("isDefault") = isDefaultCheckBox1.Checked
            .Item("ItemGroup") = Trim(ItemGroupMyTextBox2.Text & "")
            .Item("ItemOrder") = ItemOrderMyTextBox1.Value
            .Item("Memo") = Trim(Memo_TextBox.Text & "")

        End With

        '数据保存
        Try
            RZbtb.Rows.Add(My_NewRow)
            Rrc.GridMove("最后")
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            Exit Sub
        Finally
            Name_TextBox.Select()
        End Try

        '数据更新
        Try
            With LIS_TestItemModel
                .TestXm_Code = My_NewRow.Item("TestXm_Code")
                .TestItem_Code = My_NewRow.Item("TestItem_Code")
                .Item_Name = My_NewRow.Item("Item_Name")
                .Item_Jc = My_NewRow.Item("Item_Jc")
                .Item_Dw = My_NewRow.Item("Item_Dw")
                If IsDBNull(My_NewRow.Item("MaleMin")) = True Then
                Else
                    .MaleMin = My_NewRow.Item("MaleMin")
                End If
                If IsDBNull(My_NewRow.Item("MaleMax")) = True Then
                Else
                    .MaleMax = My_NewRow.Item("MaleMax")
                End If
                If IsDBNull(My_NewRow.Item("FemaleMin")) = True Then
                Else
                    .FemaleMin = My_NewRow.Item("FemaleMin")
                End If
                If IsDBNull(My_NewRow.Item("FemaleMax")) = True Then
                Else
                    .FemaleMax = My_NewRow.Item("FemaleMax")
                End If
                .isDefault = My_NewRow.Item("isDefault")
                .ItemGroup = My_NewRow.Item("ItemGroup")
                .ItemOrder = My_NewRow.Item("ItemOrder")
                .Memo = My_NewRow.Item("Memo")
            End With
            LIS_TestItemBll.Add(LIS_TestItemModel)
            My_NewRow.AcceptChanges()
            RZbtb.AcceptChanges()
            ' MsgBox("添加成功！", MsgBoxStyle.Exclamation, "提示:")
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            Name_TextBox.Select()
            Exit Sub
        Finally
        End Try

        '数据清空
        Call Data_Clear()

    End Sub



    Private Sub Data_Edit()

        Dim My_Row As DataRow = Rrow

        Try
            With My_Row
                .BeginEdit()
                .Item("Item_Name") = Trim(Name_TextBox.Text & "")
                .Item("Item_Jc") = Trim(JcMyTextBox3.Text & "")
                .Item("Item_Dw") = Trim(DwMyTextBox1.Text & "")
                .Item("MaleMin") = MaleMinNumericEdit1.Value
                .Item("MaleMax") = MaleMaxNumericEdit1.Value
                .Item("FemaleMin") = FemaleMinNumericEdit2.Value
                .Item("FemaleMax") = FemaleMaxNumericEdit3.Value
                .Item("isDefault") = isDefaultCheckBox1.Checked
                .Item("ItemGroup") = Trim(ItemGroupMyTextBox2.Text & "")
                .Item("ItemOrder") = ItemOrderMyTextBox1.Value
                .Item("Memo") = Trim(Memo_TextBox.Text & "")
                .EndEdit()
            End With

        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Critical, "提示:")
            My_Row.CancelEdit()
            Exit Sub
        Finally
            Name_TextBox.Select()
        End Try

        '数据更新
        Try
            With LIS_TestItemModel
                .TestXm_Code = My_Row.Item("TestXm_Code")
                .TestItem_Code = My_Row.Item("TestItem_Code")
                .Item_Name = My_Row.Item("Item_Name")
                .Item_Jc = My_Row.Item("Item_Jc")
                .Item_Dw = My_Row.Item("Item_Dw")
                If IsDBNull(My_Row.Item("MaleMin")) = True Then
                Else
                    .MaleMin = My_Row.Item("MaleMin")
                End If
                If IsDBNull(My_Row.Item("MaleMax")) = True Then
                Else
                    .MaleMax = My_Row.Item("MaleMax")
                End If
                If IsDBNull(My_Row.Item("FemaleMin")) = True Then
                Else
                    .FemaleMin = My_Row.Item("FemaleMin")
                End If
                If IsDBNull(My_Row.Item("FemaleMax")) = True Then
                Else
                    .FemaleMax = My_Row.Item("FemaleMax")
                End If
                .isDefault = My_Row.Item("isDefault")
                .ItemGroup = My_Row.Item("ItemGroup")
                .ItemOrder = My_Row.Item("ItemOrder")
                .Memo = My_Row.Item("Memo")
            End With
            LIS_TestItemBll.Update(LIS_TestItemModel)
            My_Row.AcceptChanges()
            V_Name = My_Row.Item("Item_Name")
            MsgBox("更新成功！", MsgBoxStyle.Exclamation, "提示:")

        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Information + MsgBoxStyle.OkOnly, "提示:")
        Finally
            'MinValue_TextBox.Select()
            Name_TextBox.Select()
        End Try
    End Sub

#End Region


    Private Sub ZhongWen_TextBox_GotFocus(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Name_TextBox.GotFocus, Memo_TextBox.GotFocus, ItemGroupMyTextBox2.GotFocus, JcMyTextBox3.GotFocus
        InputLanguage.CurrentInputLanguage = HisVar.HisVar.InputCode
    End Sub

    Private Sub DwMyTextBox1_TextBox_GotFocus(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles DwMyTextBox1.GotFocus
        InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages(0)
    End Sub


End Class