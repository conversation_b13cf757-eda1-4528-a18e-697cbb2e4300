﻿Imports Stimulsoft.Report

Public Class JcKs_Money_Tj
    Dim My_Dataset As New DataSet

    Private Sub JcKs_Money_Tj_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        BaseFunc.BaseFunc.Init_Datetimepicker(DateTimePicker1)
        BaseFunc.BaseFunc.Init_Datetimepicker(DateTimePicker2)
        DateTimePicker1.Value = Format(Now, "yyyy-MM-01 00:00:00")
        DateTimePicker2.Value = Format(Now, "yyyy-MM-dd 23:59:59")

        Dim My_Combo1 As New BaseClass.C_Combo1(Me.C1Combo1)
        With My_Combo1
            .Init_TDBCombo()
            .AddItem("全部")
            .AddItem("住院")
            .AddItem("门诊")

            .SelectedIndex(0)
        End With
        With C1Combo1
            .BorderStyle = BorderStyle.Fixed3D
            .Height = 22
            .Width = 144
            .DropDownWidth = 144
        End With

        Dim My_Combo2 As New BaseClass.C_Combo1(Me.C1Combo2)
        With My_Combo2
            .Init_TDBCombo()
            .AddItem("按日清统计")
            .AddItem("按出院统计")
            .SelectedIndex(0)
        End With
        With C1Combo2
            .BorderStyle = BorderStyle.Fixed3D
            .Height = 22
            .Width = 144
            .DropDownWidth = 144
        End With
    End Sub

    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click
        Me.Cursor = Cursors.WaitCursor
        Dim V_Str As String = ""
        Dim V_Str1 As String = ""
        Dim V_Str2 As String = ""


        If C1Combo1.Text = "全部" Or C1Combo1.Text = "门诊" Then
            V_Str1 = "select Xm_Name,Xm_Dw,Mz_Dj as Xm_Dj,Mz_Sl as Xm_Sl,Mz_Xm_Sum.Mz_Money as Xm_Money,Jc_KsTab.Ks_Name as Jc_KsName, Cf_KsTab.Ks_Name as Cf_KsName,Ys_Name as Cf_YsName   " & _
                     " from Mz_Sum, Mz_Xm_Sum, Zd_YyKs as Jc_KsTab,Zd_YyKs as Cf_KsTab ,Zd_YyYs as Cf_YsTab, zd_yyks_xm, zd_ml_xm3, Mz_Jz" & _
                      " where Mz_Jz.Jz_Code = Mz_Sum.Jz_Code And Mz_Sum.Mz_Code = Mz_Xm_Sum.Mz_Code And Mz_Xm_Sum.Xm_Code = Zd_Ml_Xm3.Xm_Code and not exists (select Mz_Ph+Mz_Code from Mz_Ty_Sum where Mz_Ty_Sum.mz_ph=Mz_Xm_Sum.Mz_Ph and Mz_Ty_Sum.Mz_code=Mz_Xm_Sum.Mz_Code) and Jc_KsTab.Ks_Code=Zd_YyKs_Xm.Ks_Code and Mz_Sum.Ks_Code=Cf_KsTab.Ks_Code and Mz_Sum.Ys_Code=Cf_YsTab.Ys_Code" & _
                      " and Jc_KsTab.Ks_Code=Zd_YyKs_Xm.Ks_Code and zd_yyks_xm.xm_code=zd_ml_xm3.xm_code and Jz_Date+Jz_Time>='" & DateTimePicker1.Value & "' and Jz_Date+Jz_Time<='" & DateTimePicker2.Value & "'" & _
                     "    "


        Else
            V_Str1 = ""
        End If

        If C1Combo1.Text = "全部" Or C1Combo1.Text = "住院" Then
            If C1Combo1.Text = "全部" Then V_Str1 = V_Str1 & " UNION ALL "

            If C1Combo2.Text = "按日清统计" Then
                V_Str2 = "select Xm_Name,Xm_Dw,Cf_Dj as Xm_Dj,Cf_Sl as Xm_Sl,Bl_CfXm.Cf_Money as Xm_Money,Jc_KsTab.Ks_Name as Jc_KsName, Cf_KsTab.Ks_Name as Cf_KsName,Ys_Name as Cf_YsName " & _
             "  from  Bl_Cf, Bl_CfXm, Zd_YyKs as Jc_KsTab,Zd_YyKs as Cf_KsTab ,Zd_YyYs as Cf_YsTab, zd_yyks_xm, zd_ml_xm3, Bl_Jz" & _
             "  where Bl_Jz.Jz_Code = Bl_Cf.Jz_Code  And Bl_Cf.Cf_Code = Bl_CfXm.Cf_Code And Bl_CfXm.Xm_Code = Zd_Ml_Xm3.Xm_Code " & _
             "  and Jc_KsTab.Ks_Code=Zd_YyKs_Xm.Ks_Code and Bl_Cf.Ks_Code=Cf_KsTab.Ks_Code and Bl_Cf.Ys_Code=Cf_YsTab.Ys_Code  and zd_yyks_xm.xm_code=zd_ml_xm3.xm_code and Jz_Date+Jz_Time>='" & DateTimePicker1.Value & "' and Jz_Date+Jz_Time<='" & DateTimePicker2.Value & "' " & _
             " "

            ElseIf C1Combo2.Text = "按出院统计" Then
                V_Str2 = "select Xm_Name,Xm_Dw,Cf_Dj as Xm_Dj,Cf_Sl as Xm_Sl,Bl_CfXm.Cf_Money as Xm_Money,Jc_KsTab.Ks_Name as Jc_KsName, Cf_KsTab.Ks_Name as Cf_KsName,Ys_Name as Cf_YsName " & _
              "  from Bl, Bl_Cf, Bl_CfXm, Zd_YyKs as Jc_KsTab,Zd_YyKs as Cf_KsTab ,Zd_YyYs as Cf_YsTab, zd_yyks_xm, zd_ml_xm3, Bl_Jz " & _
              "  where Bl_Jz.Jz_Code = Bl.Jz_Code And Bl.Bl_Code = Bl_Cf.Bl_Code And Bl_Cf.Cf_Code = Bl_CfXm.Cf_Code And Bl_CfXm.Xm_Code = Zd_Ml_Xm3.Xm_Code " & _
              "  and Jc_KsTab.Ks_Code=Zd_YyKs_Xm.Ks_Code and Bl_Cf.Ks_Code=Cf_KsTab.Ks_Code and Bl_Cf.Ys_Code=Cf_YsTab.Ys_Code and zd_yyks_xm.xm_code=zd_ml_xm3.xm_code and Jz_Date+Jz_Time>='" & DateTimePicker1.Value & "' and Jz_Date+Jz_Time<='" & DateTimePicker2.Value & "' " & _
              "  "

            End If
        Else
            V_Str2 = " "
        End If

        V_Str = V_Str1 & V_Str2 & " Order by Xm_Name"

        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, V_Str, "医技科室收入统计", True)

        Dim Stirpt As New StiReport
        Stirpt.Load(".\Rpt\医技科室收入统计.mrt")
        Stirpt.ReportName = "医技科室收入统计"
        Stirpt.RegData(My_Dataset.Tables("医技科室收入统计"))

        Stirpt.Compile()
        Stirpt("标题") = HisVar.HisVar.WsyName & "医技科室收入统计"
        Stirpt("查询时间") = Format(DateTimePicker1.Value, "yyyy-MM-dd HH:mm:ss") & "至" & Format(DateTimePicker2.Value, "yyyy-MM-dd HH:mm:ss")
        Stirpt("打印时间") = Format(Now, "yyyy-MM-dd HH:mm:ss")
        ' Stirpt.Design()
        Stirpt.Show()
        Me.Cursor = Cursors.Default
    End Sub
End Class