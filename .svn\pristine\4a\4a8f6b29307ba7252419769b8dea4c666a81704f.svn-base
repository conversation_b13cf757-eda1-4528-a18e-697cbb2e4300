﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class MMSCondition
    Inherits HisControl.BaseChild

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.MyButton1 = New CustomControl.MyButton()
        Me.dhCheckBox1 = New System.Windows.Forms.CheckBox()
        Me.QXButton = New CustomControl.MyButton()
        Me.lrCheckBox3 = New System.Windows.Forms.CheckBox()
        Me.wcCheckBox4 = New System.Windows.Forms.CheckBox()
        Me.dqCheckBox5 = New System.Windows.Forms.CheckBox()
        Me.YKDoubleDateEdit = New CustomControl.DoubleDateEdit()
        Me.LrDoubleDateEdit = New CustomControl.DoubleDateEdit()
        Me.WCDoubleDateEdit = New CustomControl.DoubleDateEdit()
        Me.DQDoubleDateEdit = New CustomControl.DoubleDateEdit()
        Me.WZLBDtComobo1 = New CustomControl.MyDtComobo()
        Me.YCKFDtComobo = New CustomControl.MyDtComobo()
        Me.WZMCDtComobo = New CustomControl.MyDtComobo()
        Me.DJZTSingleComobo = New CustomControl.MySingleComobo()
        Me.WZPHTextBox1 = New CustomControl.MyTextBox()
        Me.JSRDtComobo = New CustomControl.MyDtComobo()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.QDButton = New CustomControl.MyButton()
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel()
        Me.YRKFDtComobo = New CustomControl.MyDtComobo()
        Me.GGTextBox1 = New CustomControl.MyTextBox()
        Me.SCCJTextBox1 = New CustomControl.MyTextBox()
        Me.Panel1.SuspendLayout
        Me.TableLayoutPanel1.SuspendLayout
        Me.SuspendLayout
        '
        'MyButton1
        '
        Me.MyButton1.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.MyButton1.DialogResult = System.Windows.Forms.DialogResult.None
        Me.MyButton1.Location = New System.Drawing.Point(285, 3)
        Me.MyButton1.Name = "MyButton1"
        Me.MyButton1.Size = New System.Drawing.Size(60, 27)
        Me.MyButton1.TabIndex = 1
        Me.MyButton1.Text = "清空"
        '
        'dhCheckBox1
        '
        Me.dhCheckBox1.AutoSize = True
        Me.dhCheckBox1.Location = New System.Drawing.Point(11, 8)
        Me.dhCheckBox1.Name = "dhCheckBox1"
        Me.dhCheckBox1.Size = New System.Drawing.Size(14, 14)
        Me.dhCheckBox1.TabIndex = 7
        Me.dhCheckBox1.UseVisualStyleBackColor = True
        '
        'QXButton
        '
        Me.QXButton.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.QXButton.DialogResult = System.Windows.Forms.DialogResult.Cancel
        Me.QXButton.Location = New System.Drawing.Point(196, 3)
        Me.QXButton.Name = "QXButton"
        Me.QXButton.Size = New System.Drawing.Size(60, 27)
        Me.QXButton.TabIndex = 0
        Me.QXButton.Text = "取消"
        '
        'lrCheckBox3
        '
        Me.lrCheckBox3.AutoSize = True
        Me.lrCheckBox3.Location = New System.Drawing.Point(11, 36)
        Me.lrCheckBox3.Name = "lrCheckBox3"
        Me.lrCheckBox3.Size = New System.Drawing.Size(14, 14)
        Me.lrCheckBox3.TabIndex = 7
        Me.lrCheckBox3.UseVisualStyleBackColor = True
        '
        'wcCheckBox4
        '
        Me.wcCheckBox4.AutoSize = True
        Me.wcCheckBox4.Location = New System.Drawing.Point(11, 64)
        Me.wcCheckBox4.Name = "wcCheckBox4"
        Me.wcCheckBox4.Size = New System.Drawing.Size(14, 14)
        Me.wcCheckBox4.TabIndex = 7
        Me.wcCheckBox4.UseVisualStyleBackColor = True
        '
        'dqCheckBox5
        '
        Me.dqCheckBox5.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.dqCheckBox5.AutoSize = True
        Me.dqCheckBox5.Location = New System.Drawing.Point(11, 96)
        Me.dqCheckBox5.Name = "dqCheckBox5"
        Me.dqCheckBox5.Size = New System.Drawing.Size(14, 14)
        Me.dqCheckBox5.TabIndex = 7
        Me.dqCheckBox5.UseVisualStyleBackColor = True
        '
        'YKDoubleDateEdit
        '
        Me.YKDoubleDateEdit.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.YKDoubleDateEdit.Captain = "移库日期"
        Me.YKDoubleDateEdit.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.YKDoubleDateEdit, 3)
        Me.YKDoubleDateEdit.Location = New System.Drawing.Point(28, 8)
        Me.YKDoubleDateEdit.Margin = New System.Windows.Forms.Padding(0)
        Me.YKDoubleDateEdit.MaximumSize = New System.Drawing.Size(1000000000, 22)
        Me.YKDoubleDateEdit.MinimumSize = New System.Drawing.Size(0, 22)
        Me.YKDoubleDateEdit.Name = "YKDoubleDateEdit"
        Me.YKDoubleDateEdit.Size = New System.Drawing.Size(474, 22)
        Me.YKDoubleDateEdit.TabIndex = 8
        '
        'LrDoubleDateEdit
        '
        Me.LrDoubleDateEdit.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LrDoubleDateEdit.Captain = "录入时间"
        Me.LrDoubleDateEdit.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.LrDoubleDateEdit, 3)
        Me.LrDoubleDateEdit.Location = New System.Drawing.Point(28, 36)
        Me.LrDoubleDateEdit.Margin = New System.Windows.Forms.Padding(0)
        Me.LrDoubleDateEdit.MaximumSize = New System.Drawing.Size(1000000000, 22)
        Me.LrDoubleDateEdit.MinimumSize = New System.Drawing.Size(0, 22)
        Me.LrDoubleDateEdit.Name = "LrDoubleDateEdit"
        Me.LrDoubleDateEdit.Size = New System.Drawing.Size(474, 22)
        Me.LrDoubleDateEdit.TabIndex = 8
        '
        'WCDoubleDateEdit
        '
        Me.WCDoubleDateEdit.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.WCDoubleDateEdit.Captain = "完成时间"
        Me.WCDoubleDateEdit.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.WCDoubleDateEdit, 3)
        Me.WCDoubleDateEdit.Location = New System.Drawing.Point(28, 64)
        Me.WCDoubleDateEdit.Margin = New System.Windows.Forms.Padding(0)
        Me.WCDoubleDateEdit.MaximumSize = New System.Drawing.Size(1000000000, 22)
        Me.WCDoubleDateEdit.MinimumSize = New System.Drawing.Size(0, 22)
        Me.WCDoubleDateEdit.Name = "WCDoubleDateEdit"
        Me.WCDoubleDateEdit.Size = New System.Drawing.Size(474, 22)
        Me.WCDoubleDateEdit.TabIndex = 8
        '
        'DQDoubleDateEdit
        '
        Me.DQDoubleDateEdit.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.DQDoubleDateEdit.Captain = "药品有效期"
        Me.DQDoubleDateEdit.CaptainWidth = 80.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.DQDoubleDateEdit, 3)
        Me.DQDoubleDateEdit.Location = New System.Drawing.Point(28, 92)
        Me.DQDoubleDateEdit.Margin = New System.Windows.Forms.Padding(0)
        Me.DQDoubleDateEdit.MaximumSize = New System.Drawing.Size(1000000000, 22)
        Me.DQDoubleDateEdit.MinimumSize = New System.Drawing.Size(0, 22)
        Me.DQDoubleDateEdit.Name = "DQDoubleDateEdit"
        Me.DQDoubleDateEdit.Size = New System.Drawing.Size(474, 22)
        Me.DQDoubleDateEdit.TabIndex = 8
        '
        'WZLBDtComobo1
        '
        Me.WZLBDtComobo1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.WZLBDtComobo1.Captain = "物资类别"
        Me.WZLBDtComobo1.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.WZLBDtComobo1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.WZLBDtComobo1.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.WZLBDtComobo1, 2)
        Me.WZLBDtComobo1.DataSource = Nothing
        Me.WZLBDtComobo1.ItemHeight = 18
        Me.WZLBDtComobo1.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.WZLBDtComobo1.Location = New System.Drawing.Point(258, 177)
        Me.WZLBDtComobo1.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.WZLBDtComobo1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.WZLBDtComobo1.Name = "WZLBDtComobo1"
        Me.WZLBDtComobo1.ReadOnly = False
        Me.WZLBDtComobo1.Size = New System.Drawing.Size(241, 20)
        Me.WZLBDtComobo1.TabIndex = 0
        Me.WZLBDtComobo1.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'YCKFDtComobo
        '
        Me.YCKFDtComobo.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.YCKFDtComobo.Captain = "移除库房"
        Me.YCKFDtComobo.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.YCKFDtComobo.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.YCKFDtComobo.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.YCKFDtComobo, 2)
        Me.YCKFDtComobo.DataSource = Nothing
        Me.YCKFDtComobo.ItemHeight = 18
        Me.YCKFDtComobo.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.YCKFDtComobo.Location = New System.Drawing.Point(11, 121)
        Me.YCKFDtComobo.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.YCKFDtComobo.MinimumSize = New System.Drawing.Size(0, 20)
        Me.YCKFDtComobo.Name = "YCKFDtComobo"
        Me.YCKFDtComobo.ReadOnly = False
        Me.YCKFDtComobo.Size = New System.Drawing.Size(241, 20)
        Me.YCKFDtComobo.TabIndex = 1
        Me.YCKFDtComobo.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'WZMCDtComobo
        '
        Me.WZMCDtComobo.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.WZMCDtComobo.Captain = "物资名称"
        Me.WZMCDtComobo.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.WZMCDtComobo.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.WZMCDtComobo.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.WZMCDtComobo, 2)
        Me.WZMCDtComobo.DataSource = Nothing
        Me.WZMCDtComobo.ItemHeight = 18
        Me.WZMCDtComobo.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.WZMCDtComobo.Location = New System.Drawing.Point(11, 149)
        Me.WZMCDtComobo.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.WZMCDtComobo.MinimumSize = New System.Drawing.Size(0, 20)
        Me.WZMCDtComobo.Name = "WZMCDtComobo"
        Me.WZMCDtComobo.ReadOnly = False
        Me.WZMCDtComobo.Size = New System.Drawing.Size(241, 20)
        Me.WZMCDtComobo.TabIndex = 1
        Me.WZMCDtComobo.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'DJZTSingleComobo
        '
        Me.DJZTSingleComobo.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.DJZTSingleComobo.Captain = "单据状态"
        Me.DJZTSingleComobo.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.DJZTSingleComobo.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.DJZTSingleComobo.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.DJZTSingleComobo, 2)
        Me.DJZTSingleComobo.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        Me.DJZTSingleComobo.ItemHeight = 16
        Me.DJZTSingleComobo.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.DJZTSingleComobo.Location = New System.Drawing.Point(11, 177)
        Me.DJZTSingleComobo.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.DJZTSingleComobo.MinimumSize = New System.Drawing.Size(0, 20)
        Me.DJZTSingleComobo.Name = "DJZTSingleComobo"
        Me.DJZTSingleComobo.ReadOnly = False
        Me.DJZTSingleComobo.Size = New System.Drawing.Size(241, 20)
        Me.DJZTSingleComobo.TabIndex = 1
        Me.DJZTSingleComobo.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'WZPHTextBox1
        '
        Me.WZPHTextBox1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.WZPHTextBox1.Captain = "物资批号"
        Me.WZPHTextBox1.CaptainBackColor = System.Drawing.Color.Transparent
        Me.WZPHTextBox1.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.WZPHTextBox1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.WZPHTextBox1.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.WZPHTextBox1, 2)
        Me.WZPHTextBox1.ContentForeColor = System.Drawing.Color.Black
        Me.WZPHTextBox1.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.WZPHTextBox1.Location = New System.Drawing.Point(11, 205)
        Me.WZPHTextBox1.Multiline = False
        Me.WZPHTextBox1.Name = "WZPHTextBox1"
        Me.WZPHTextBox1.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.WZPHTextBox1.ReadOnly = False
        Me.WZPHTextBox1.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.WZPHTextBox1.SelectionStart = 0
        Me.WZPHTextBox1.SelectStart = 0
        Me.WZPHTextBox1.Size = New System.Drawing.Size(241, 20)
        Me.WZPHTextBox1.TabIndex = 4
        Me.WZPHTextBox1.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.WZPHTextBox1.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.WZPHTextBox1.Watermark = Nothing
        '
        'JSRDtComobo
        '
        Me.JSRDtComobo.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.JSRDtComobo.Captain = "经 手 人"
        Me.JSRDtComobo.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.JSRDtComobo.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.JSRDtComobo.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.JSRDtComobo, 2)
        Me.JSRDtComobo.DataSource = Nothing
        Me.JSRDtComobo.ItemHeight = 18
        Me.JSRDtComobo.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.JSRDtComobo.Location = New System.Drawing.Point(258, 149)
        Me.JSRDtComobo.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.JSRDtComobo.MinimumSize = New System.Drawing.Size(0, 20)
        Me.JSRDtComobo.Name = "JSRDtComobo"
        Me.JSRDtComobo.ReadOnly = False
        Me.JSRDtComobo.Size = New System.Drawing.Size(241, 20)
        Me.JSRDtComobo.TabIndex = 0
        Me.JSRDtComobo.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'Panel1
        '
        Me.TableLayoutPanel1.SetColumnSpan(Me.Panel1, 2)
        Me.Panel1.Controls.Add(Me.MyButton1)
        Me.Panel1.Controls.Add(Me.QXButton)
        Me.Panel1.Controls.Add(Me.QDButton)
        Me.Panel1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel1.Location = New System.Drawing.Point(31, 260)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(448, 34)
        Me.Panel1.TabIndex = 6
        '
        'QDButton
        '
        Me.QDButton.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.QDButton.DialogResult = System.Windows.Forms.DialogResult.OK
        Me.QDButton.Location = New System.Drawing.Point(106, 3)
        Me.QDButton.Name = "QDButton"
        Me.QDButton.Size = New System.Drawing.Size(60, 27)
        Me.QDButton.TabIndex = 0
        Me.QDButton.Text = "确定"
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.ColumnCount = 6
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 8.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 20.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 50.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 50.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 20.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 10.0!))
        Me.TableLayoutPanel1.Controls.Add(Me.JSRDtComobo, 3, 6)
        Me.TableLayoutPanel1.Controls.Add(Me.Panel1, 2, 10)
        Me.TableLayoutPanel1.Controls.Add(Me.YCKFDtComobo, 1, 5)
        Me.TableLayoutPanel1.Controls.Add(Me.WZMCDtComobo, 1, 6)
        Me.TableLayoutPanel1.Controls.Add(Me.DJZTSingleComobo, 1, 7)
        Me.TableLayoutPanel1.Controls.Add(Me.WZPHTextBox1, 1, 8)
        Me.TableLayoutPanel1.Controls.Add(Me.dhCheckBox1, 1, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.lrCheckBox3, 1, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.wcCheckBox4, 1, 3)
        Me.TableLayoutPanel1.Controls.Add(Me.dqCheckBox5, 1, 4)
        Me.TableLayoutPanel1.Controls.Add(Me.YKDoubleDateEdit, 2, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.LrDoubleDateEdit, 2, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.WCDoubleDateEdit, 2, 3)
        Me.TableLayoutPanel1.Controls.Add(Me.DQDoubleDateEdit, 2, 4)
        Me.TableLayoutPanel1.Controls.Add(Me.YRKFDtComobo, 3, 5)
        Me.TableLayoutPanel1.Controls.Add(Me.GGTextBox1, 1, 9)
        Me.TableLayoutPanel1.Controls.Add(Me.WZLBDtComobo1, 3, 7)
        Me.TableLayoutPanel1.Controls.Add(Me.SCCJTextBox1, 3, 8)
        Me.TableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(0, 0)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.RowCount = 12
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 5.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 40.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 5.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 20.0!))
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(512, 301)
        Me.TableLayoutPanel1.TabIndex = 3
        '
        'YRKFDtComobo
        '
        Me.YRKFDtComobo.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.YRKFDtComobo.Captain = "移入库房"
        Me.YRKFDtComobo.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.YRKFDtComobo.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.YRKFDtComobo.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.YRKFDtComobo, 2)
        Me.YRKFDtComobo.DataSource = Nothing
        Me.YRKFDtComobo.ItemHeight = 18
        Me.YRKFDtComobo.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.YRKFDtComobo.Location = New System.Drawing.Point(258, 121)
        Me.YRKFDtComobo.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.YRKFDtComobo.MinimumSize = New System.Drawing.Size(0, 20)
        Me.YRKFDtComobo.Name = "YRKFDtComobo"
        Me.YRKFDtComobo.ReadOnly = False
        Me.YRKFDtComobo.Size = New System.Drawing.Size(241, 20)
        Me.YRKFDtComobo.TabIndex = 1
        Me.YRKFDtComobo.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'GGTextBox1
        '
        Me.GGTextBox1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GGTextBox1.Captain = "规    格"
        Me.GGTextBox1.CaptainBackColor = System.Drawing.Color.Transparent
        Me.GGTextBox1.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.GGTextBox1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.GGTextBox1.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.GGTextBox1, 2)
        Me.GGTextBox1.ContentForeColor = System.Drawing.Color.Black
        Me.GGTextBox1.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.GGTextBox1.Location = New System.Drawing.Point(11, 233)
        Me.GGTextBox1.Multiline = False
        Me.GGTextBox1.Name = "GGTextBox1"
        Me.GGTextBox1.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.GGTextBox1.ReadOnly = False
        Me.GGTextBox1.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.GGTextBox1.SelectionStart = 0
        Me.GGTextBox1.SelectStart = 0
        Me.GGTextBox1.Size = New System.Drawing.Size(241, 20)
        Me.GGTextBox1.TabIndex = 4
        Me.GGTextBox1.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.GGTextBox1.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.GGTextBox1.Watermark = Nothing
        '
        'SCCJTextBox1
        '
        Me.SCCJTextBox1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SCCJTextBox1.Captain = "生产厂家"
        Me.SCCJTextBox1.CaptainBackColor = System.Drawing.Color.Transparent
        Me.SCCJTextBox1.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.SCCJTextBox1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.SCCJTextBox1.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.SCCJTextBox1, 2)
        Me.SCCJTextBox1.ContentForeColor = System.Drawing.Color.Black
        Me.SCCJTextBox1.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.SCCJTextBox1.Location = New System.Drawing.Point(258, 205)
        Me.SCCJTextBox1.Multiline = False
        Me.SCCJTextBox1.Name = "SCCJTextBox1"
        Me.SCCJTextBox1.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.SCCJTextBox1.ReadOnly = False
        Me.SCCJTextBox1.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.SCCJTextBox1.SelectionStart = 0
        Me.SCCJTextBox1.SelectStart = 0
        Me.SCCJTextBox1.Size = New System.Drawing.Size(241, 20)
        Me.SCCJTextBox1.TabIndex = 4
        Me.SCCJTextBox1.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.SCCJTextBox1.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.SCCJTextBox1.Watermark = Nothing
        '
        'MMSCondition
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(512, 301)
        Me.Controls.Add(Me.TableLayoutPanel1)
        Me.Name = "MMSCondition"
        Me.Text = "物资移库查询条件"
        Me.Panel1.ResumeLayout(false)
        Me.TableLayoutPanel1.ResumeLayout(false)
        Me.TableLayoutPanel1.PerformLayout
        Me.ResumeLayout(false)

End Sub
    Friend WithEvents MyButton1 As CustomControl.MyButton
    Friend WithEvents TableLayoutPanel1 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents JSRDtComobo As CustomControl.MyDtComobo
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents QXButton As CustomControl.MyButton
    Friend WithEvents QDButton As CustomControl.MyButton
    Friend WithEvents YCKFDtComobo As CustomControl.MyDtComobo
    Friend WithEvents WZMCDtComobo As CustomControl.MyDtComobo
    Friend WithEvents DJZTSingleComobo As CustomControl.MySingleComobo
    Friend WithEvents WZPHTextBox1 As CustomControl.MyTextBox
    Friend WithEvents dhCheckBox1 As System.Windows.Forms.CheckBox
    Friend WithEvents lrCheckBox3 As System.Windows.Forms.CheckBox
    Friend WithEvents wcCheckBox4 As System.Windows.Forms.CheckBox
    Friend WithEvents dqCheckBox5 As System.Windows.Forms.CheckBox
    Friend WithEvents YKDoubleDateEdit As CustomControl.DoubleDateEdit
    Friend WithEvents LrDoubleDateEdit As CustomControl.DoubleDateEdit
    Friend WithEvents WCDoubleDateEdit As CustomControl.DoubleDateEdit
    Friend WithEvents DQDoubleDateEdit As CustomControl.DoubleDateEdit
    Friend WithEvents WZLBDtComobo1 As CustomControl.MyDtComobo
    Friend WithEvents YRKFDtComobo As CustomControl.MyDtComobo
    Friend WithEvents GGTextBox1 As CustomControl.MyTextBox
    Friend WithEvents SCCJTextBox1 As CustomControl.MyTextBox
End Class
