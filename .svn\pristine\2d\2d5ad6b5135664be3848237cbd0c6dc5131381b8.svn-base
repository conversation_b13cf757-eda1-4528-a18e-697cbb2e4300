﻿Imports System.Drawing
Imports System.Windows.Forms

Public Class C_Combo2
    Private My_Combo As C1.Win.C1List.C1Combo
    Private My_View As DataView
    Private My_DisplayMember As String
    Private My_ValueMember As String
    Private My_DropDownWidth As Integer

    Public Sub New(ByVal TDBCombo As C1.Win.C1List.C1Combo, ByVal V_View As DataView, ByVal V_DisplayMember As String, ByVal V_ValueMember As String, ByVal V_DropDownWidth As Integer) ' 定义一个公用构造函数来初始化C1Combo
        My_Combo = TDBCombo
        My_View = V_View
        My_DisplayMember = V_DisplayMember
        My_ValueMember = V_ValueMember
        My_DropDownWidth = V_DropDownWidth
    End Sub


    Public Sub Init_TDBCombo()
        With My_Combo
            .AutoSize = False
            .AllowColMove = False
            .AllowSort = True
            .AutoDropDown = True
            .AutoCompletion = True

            .AutoSelect = True
            .AlternatingRows = False
            .BorderStyle = BorderStyle.None

            .LimitToList = False
            .RowTracking = True

            .CausesValidation = True
            .SuperBack = True
            .CausesValidation = True
            .ColumnHeaders = True
            .ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownCombo
            .FlatStyle = C1.Win.C1List.FlatModeEnum.System
            .ItemHeight = 16
            .MaxDropDownItems = 18
            .Height = 16
            .DropDownWidth = 500

            With .Splits(0)
                .AllowFocus = True
                .ExtendRightColumn = True
                .ColumnCaptionHeight = 18
                With .HighLightRowStyle '选中行
                    .ForeColor = Color.FromArgb(255, 255, 255)
                    .BackColor = Color.FromArgb(205, 92, 92) 'Color.SlateGray
                    .VerticalAlignment = C1.Win.C1List.AlignVertEnum.Bottom
                End With

                With .SelectedStyle
                    .ForeColor = Color.FromArgb(255, 255, 255)
                    .BackColor = Color.FromArgb(205, 92, 92) 'Color.SlateGray
                    .VerticalAlignment = C1.Win.C1List.AlignVertEnum.Bottom
                End With
            End With

            .DataMode = C1.Win.C1List.DataModeEnum.Normal
            .RowDivider.Style = C1.Win.C1List.LineStyleEnum.Single
            .DropdownPosition = C1.Win.C1List.DropdownPositionEnum.LeftDown
            .MatchCol = C1.Win.C1List.MatchColEnum.DisplayMember

            .DataSource = My_View
            .DisplayMember = My_DisplayMember
            .ValueMember = My_ValueMember
            .DropDownWidth = My_DropDownWidth
        End With
    End Sub

    Public Sub Init_Colum(ByVal V_字段 As String, ByVal V_标题 As String, ByVal V_长度 As Integer, ByVal V_水平 As String)
        With My_Combo

            .Columns(V_字段).Caption = V_标题

            With .Splits(0).DisplayColumns(V_字段)
                .Width = V_长度

                If V_长度 = 0 Then .Visible = False

                '标题修饰
                With .HeadingStyle
                    .BackColor = Color.FromArgb(0, 78, 152) ' System.Drawing.Color.FromKnownColor(KnownColor.Desktop)
                    .ForeColor = Color.FromArgb(255, 255, 255) 'System.Drawing.Color.FromKnownColor(KnownColor.White)
                    .HorizontalAlignment = C1.Win.C1List.AlignHorzEnum.Center
                    .VerticalAlignment = C1.Win.C1List.AlignVertEnum.Bottom
                End With

                '内容修饰
                Select Case V_水平
                    Case "左"
                        .Style.HorizontalAlignment = C1.Win.C1List.AlignHorzEnum.Near
                    Case "中"
                        .Style.HorizontalAlignment = C1.Win.C1List.AlignHorzEnum.Center
                    Case "右"
                        .Style.HorizontalAlignment = C1.Win.C1List.AlignHorzEnum.Far
                End Select
                .Style.VerticalAlignment = C1.Win.C1List.AlignVertEnum.Bottom

            End With

        End With

    End Sub

    '重写方法
    Public Overridable Sub SelectedIndex(ByVal V_SelectIndex As Integer)
        Me.My_Combo.SelectedIndex = V_SelectIndex
    End Sub

    '重写方法
    Public Overridable Sub MaxDropDownItems(ByVal V_Max As Integer)
        Me.My_Combo.MaxDropDownItems = V_Max
    End Sub


End Class
