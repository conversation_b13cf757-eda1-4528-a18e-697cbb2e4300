﻿/**  版本信息模板在安装目录下，可自行修改。
* D_Emr_GDlog.cs
*
* 功 能： N/A
* 类 名： D_Emr_GDlog
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/8/29 11:28:59   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
namespace DAL
{
	/// <summary>
	/// 数据访问类:D_Emr_GDlog
	/// </summary>
	public partial class D_Emr_GDlog
	{
		public D_Emr_GDlog()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(long id)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from Emr_GDlog");
			strSql.Append(" where id=@id ");
			SqlParameter[] parameters = {
					new SqlParameter("@id", SqlDbType.BigInt,8)			};
			parameters[0].Value = id;

			return HisVar.HisVar.Sqldal.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ModelOld.M_Emr_GDlog model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into Emr_GDlog(");
			strSql.Append("id,Bl_Code,Jsr_Code,GuiDang_State,Log_Date)");
			strSql.Append(" values (");
			strSql.Append("@id,@Bl_Code,@Jsr_Code,@GuiDang_State,@Log_Date)");
			SqlParameter[] parameters = {
					new SqlParameter("@id", SqlDbType.BigInt,8),
					new SqlParameter("@Bl_Code", SqlDbType.Char,10),
					new SqlParameter("@Jsr_Code", SqlDbType.Char,7),
					new SqlParameter("@GuiDang_State", SqlDbType.VarChar,4),
					new SqlParameter("@Log_Date", SqlDbType.SmallDateTime)};
			parameters[0].Value = model.id;
			parameters[1].Value = model.Bl_Code;
			parameters[2].Value = model.Jsr_Code;
			parameters[3].Value = model.GuiDang_State;
			parameters[4].Value = model.Log_Date;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ModelOld.M_Emr_GDlog model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update Emr_GDlog set ");
			strSql.Append("Bl_Code=@Bl_Code,");
			strSql.Append("Jsr_Code=@Jsr_Code,");
			strSql.Append("GuiDang_State=@GuiDang_State,");
			strSql.Append("Log_Date=@Log_Date");
			strSql.Append(" where id=@id ");
			SqlParameter[] parameters = {
					new SqlParameter("@Bl_Code", SqlDbType.Char,10),
					new SqlParameter("@Jsr_Code", SqlDbType.Char,7),
					new SqlParameter("@GuiDang_State", SqlDbType.VarChar,4),
					new SqlParameter("@Log_Date", SqlDbType.SmallDateTime),
					new SqlParameter("@id", SqlDbType.BigInt,8)};
			parameters[0].Value = model.Bl_Code;
			parameters[1].Value = model.Jsr_Code;
			parameters[2].Value = model.GuiDang_State;
			parameters[3].Value = model.Log_Date;
			parameters[4].Value = model.id;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(long id)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Emr_GDlog ");
			strSql.Append(" where id=@id ");
			SqlParameter[] parameters = {
					new SqlParameter("@id", SqlDbType.BigInt,8)			};
			parameters[0].Value = id;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string idlist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Emr_GDlog ");
			strSql.Append(" where id in ("+idlist + ")  ");
			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Emr_GDlog GetModel(long id)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 id,Bl_Code,Jsr_Code,GuiDang_State,Log_Date from Emr_GDlog ");
			strSql.Append(" where id=@id ");
			SqlParameter[] parameters = {
					new SqlParameter("@id", SqlDbType.BigInt,8)			};
			parameters[0].Value = id;

			ModelOld.M_Emr_GDlog model=new ModelOld.M_Emr_GDlog();
			DataSet ds=HisVar.HisVar.Sqldal.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Emr_GDlog DataRowToModel(DataRow row)
		{
			ModelOld.M_Emr_GDlog model=new ModelOld.M_Emr_GDlog();
			if (row != null)
			{
				if(row["id"]!=null && row["id"].ToString()!="")
				{
					model.id=long.Parse(row["id"].ToString());
				}
				if(row["Bl_Code"]!=null)
				{
					model.Bl_Code=row["Bl_Code"].ToString();
				}
				if(row["Jsr_Code"]!=null)
				{
					model.Jsr_Code=row["Jsr_Code"].ToString();
				}
				if(row["GuiDang_State"]!=null)
				{
					model.GuiDang_State=row["GuiDang_State"].ToString();
				}
				if(row["Log_Date"]!=null && row["Log_Date"].ToString()!="")
				{
					model.Log_Date=DateTime.Parse(row["Log_Date"].ToString());
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select id,Bl_Code,Jsr_Code,GuiDang_State,Log_Date ");
			strSql.Append(" FROM Emr_GDlog ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" id,Bl_Code,Jsr_Code,GuiDang_State,Log_Date ");
			strSql.Append(" FROM Emr_GDlog ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM Emr_GDlog ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
            object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.id desc");
			}
			strSql.Append(")AS Row, T.*  from Emr_GDlog T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Emr_GDlog";
			parameters[1].Value = "id";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return HisVar.HisVar.Sqldal.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

