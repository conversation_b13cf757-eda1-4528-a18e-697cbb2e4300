﻿/**  版本信息模板在安装目录下，可自行修改。
* M_Zd_YyKs.cs
*
* 功 能： N/A
* 类 名： M_Zd_YyKs
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/8/11 9:17:02   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace ModelOld
{
	/// <summary>
	/// M_Zd_YyKs:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class M_Zd_YyKs
	{
		public M_Zd_YyKs()
		{}
		#region Model
		private string _yy_code;
		private string _ks_code;
		private string _ks_name;
		private string _ks_jc;
		private string _ks_fzr;
		private string _ks_tel;
		private string _ks_memo;
		/// <summary>
		/// 
		/// </summary>
		public string Yy_Code
		{
			set{ _yy_code=value;}
			get{return _yy_code;}
		}
		/// <summary>
		/// 科室编码
		/// </summary>
		public string Ks_Code
		{
			set{ _ks_code=value;}
			get{return _ks_code;}
		}
		/// <summary>
		/// 科室名称
		/// </summary>
		public string Ks_Name
		{
			set{ _ks_name=value;}
			get{return _ks_name;}
		}
		/// <summary>
		/// 简称
		/// </summary>
		public string Ks_Jc
		{
			set{ _ks_jc=value;}
			get{return _ks_jc;}
		}
		/// <summary>
		/// 负责人
		/// </summary>
		public string Ks_Fzr
		{
			set{ _ks_fzr=value;}
			get{return _ks_fzr;}
		}
		/// <summary>
		/// 电话
		/// </summary>
		public string Ks_Tel
		{
			set{ _ks_tel=value;}
			get{return _ks_tel;}
		}
		/// <summary>
		/// 备注
		/// </summary>
		public string Ks_Memo
		{
			set{ _ks_memo=value;}
			get{return _ks_memo;}
		}
		#endregion Model

	}
}

