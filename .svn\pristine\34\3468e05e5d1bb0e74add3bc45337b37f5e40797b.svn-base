﻿Imports System
Imports System.Data
Imports System.Data.SqlClient
Imports C1.Win.C1FlexGrid
Imports System.Math
Imports Stimulsoft.Report
Imports Stimulsoft.Report.Components
Imports ZTHisPara

Public Class Hsz_Hzly

#Region "变量初始化"
    Public My_Dataset As New DataSet
    Public Q As Object
#End Region

    Private Sub Hsz_Hzly_FormClosing(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosingEventArgs) Handles Me.FormClosing
        HisVar.HisVar.Sqldal.ExecuteSql("Delete from  Bl_CfLyd where Jsr_Code='" & HisVar.HisVar.JsrCode & "' and Ly_Qr='否'")
    End Sub

    Private Sub Hsz_Hzly_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load

        HisVar.HisVar.Sqldal.ExecuteSql("Delete from  Bl_CfLyd where Jsr_Code='" & HisVar.HisVar.JsrCode & "' and Ly_Qr='否'")

        If HisPara.PublicConfig.ZyCfKsXz = "是" Then
            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "select 'false'  as isCheck,Cf_Code,Ry_Name,Ry_Sex,datediff(yyyy,Ry_Csdate,Cf_Date)as Ry_Age,Cf_Date+Cf_Time as Cf_Date,Ks_Name,Ys_Name,Cf_YpMoney,CASE len(AutoCf_Code) WHEN 14 THEN '长期医嘱' ELSE AutoCf_Code END AS AutoCf_Code1  From Bl,Bl_Cf,Zd_YyKs,Zd_YyYs Where Bl.Bl_Code=Bl_Cf.Bl_Code and Bl_Cf.Ks_Code=Zd_YyKs.Ks_Code and Bl_Cf.Ys_Code=Zd_YyYs.Ys_Code  and Ry_CyDate Is Null  And Cf_Print='是' and isnull(Ly_Wc,'否')='否' and Cf_Qr='否'  and Exists (select Xx_Code from Bl_Cfyp where Bl_Cfyp.Cf_Code=Bl_Cf.Cf_Code) And not exists (Select Cf_Code from Bl_Cflyd where Bl_Cflyd.Cf_Code=Bl_Cf.Cf_Code) and Bl.Ks_Code like '%" & HisVar.HisVar.XmKs & "%'  Order by Cf_Code ", "领药患者", True)
        Else
            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "select 'false'  as isCheck,Cf_Code,Ry_Name,Ry_Sex,datediff(yyyy,Ry_Csdate,Cf_Date)as Ry_Age,Cf_Date+Cf_Time as Cf_Date,Ks_Name,Ys_Name,Cf_YpMoney,CASE len(AutoCf_Code) WHEN 14 THEN '长期医嘱' ELSE AutoCf_Code END AS AutoCf_Code1  From Bl,Bl_Cf,Zd_YyKs,Zd_YyYs Where Bl.Bl_Code=Bl_Cf.Bl_Code and Bl_Cf.Ks_Code=Zd_YyKs.Ks_Code and Bl_Cf.Ys_Code=Zd_YyYs.Ys_Code  and Ry_CyDate Is Null  And Cf_Print='是' and isnull(Ly_Wc,'否')='否' and Cf_Qr='否'  and Exists (select Xx_Code from Bl_Cfyp where Bl_Cfyp.Cf_Code=Bl_Cf.Cf_Code) And not exists (Select Cf_Code from Bl_Cflyd where Bl_Cflyd.Cf_Code=Bl_Cf.Cf_Code)  Order by Cf_Code ", "领药患者", True)
        End If



        Dim My_Grid1 As New BaseClass.C_Grid(Me.C1TrueDBGrid1)
        With My_Grid1
            .Init_Grid()
            .P_Dock(BaseClass.C_Grid.Dock.Fill)
            .Init_Column("库存编码", "Xx_Code", 120, "左", "")
            .Init_Column("名称", "Yp_Name", 150, "左", "")
            .Init_Column("规格", "Mx_Gg", 120, "左", "")
            .Init_Column("产地", "Mx_Cd", 230, "左", "")
            .Init_Column("有效期", "Yp_Yxq", 80, "中", "yyyy-MM-dd")
            .Init_Column("数量", "Cf_Sl", 60, "右", "###,###,##0.00##")
            .Init_Column("单位", "Mx_XsDw", 45, "左", "")
            .Init_Column("单价", "Cf_Dj", 70, "右", "###,###,##0.00####")
            .Init_Column("金额", "Cf_Money", 80, "右", "###,###,##0.00##")
            .Init_Column("用法用量", "Yp_Yfyl", 200, "左", "")
        End With
        C1TrueDBGrid1.VScrollBar.Style = C1.Win.C1TrueDBGrid.ScrollBarStyleEnum.Automatic

        Flex_Init(C1FlexGrid1)

        Dim My_Combo6 As New BaseClass.C_Combo1(Me.C1Combo6)
        With My_Combo6
            .Init_TDBCombo()
            .AddItem("不使用汇总打印")
            .AddItem("按药品汇总打印")
            .AddItem("按患者汇总打印")

            .SelectedIndex(0)
        End With
        With C1Combo6
            .DropDownWidth = 136
            .Width = 136
            .ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
            .Height = 22
        End With

        C1Combo6.Text = IIf(iniOperate.iniopreate.GetINI("汇总领药", "汇总打印方式", "", HisVar.HisVar.Parapath & "\Config.ini") & "" = "", "按药品汇总打印", iniOperate.iniopreate.GetINI("汇总领药", "汇总打印方式", "", HisVar.HisVar.Parapath & "\Config.ini"))



        Dim s As C1.Win.C1FlexGrid.CellStyle = C1FlexGrid1.Styles.Add("Critical")
        s.BackColor = Color.SeaGreen
        s.ForeColor = Color.White
        s.Font = New Font(C1FlexGrid1.Font, FontStyle.Bold)

    End Sub

    Private Sub Flex_Init(ByVal C1FlexG As C1.Win.C1FlexGrid.C1FlexGrid)
        With C1FlexG
            .Clear()

            .AllowDelete = False
            .AllowEditing = True
            .AutoResize = True
            .AllowSorting = AllowSortingEnum.None
            .AllowDragging = AllowDraggingEnum.Columns
            .AllowResizing = C1.Win.C1FlexGrid.AllowResizingEnum.Both
            .AllowMerging = C1.Win.C1FlexGrid.AllowMergingEnum.None
            .BorderStyle = C1.Win.C1FlexGrid.Util.BaseControls.BorderStyleEnum.Fixed3D

            .ExtendLastCol = True
            .FocusRect = FocusRectEnum.None
            .SelectionMode = SelectionModeEnum.Row
            With .Tree
                .Column = 0
                .Indent = 20
                .Style = TreeStyleFlags.CompleteLeaf
                .LineColor = Color.DarkRed
                .LineStyle = Drawing2D.DashStyle.Solid
            End With

            '类型()
            With .Styles.Fixed
                .WordWrap = False
                .Border.Style = C1.Win.C1FlexGrid.BorderStyleEnum.Raised
                .TextAlign = TextAlignEnum.CenterCenter
                .Margins.Top = 1
                .Margins.Bottom = 0
                .BackColor = Color.FromArgb(0, 78, 152)
                .ForeColor = Color.FromArgb(255, 255, 255)
            End With

            With .Styles.Highlight
                .ForeColor = Color.FromArgb(255, 255, 255)
                .BackColor = Color.FromArgb(49, 106, 197)
            End With


            '.Rows.Count = 5
            .Cols.Count = 10
            '.Rows.Fixed = 1
            .Cols.Fixed = 0
            .Rows(0).Height = 20

            C1FlexGrid1.DataSource = My_Dataset.Tables("领药患者")
            My_Dataset.Tables("领药患者").Columns("IsCheck").ReadOnly = False
            With .Cols(0)
                .Caption = "选择"
                .Width = 40
                .Visible = True
                .AllowMerging = False
                .AllowDragging = False
                .AllowEditing = True
                .TextAlign = C1.Win.C1FlexGrid.TextAlignEnum.RightCenter
                .Visible = True
                .Style.WordWrap = False
                .DataType = GetType(System.Boolean)
            End With

            With .Cols(1)
                .Caption = "医嘱编码"
                .Width = 100
                .AllowMerging = True
                .AllowDragging = True
                .AllowEditing = False
                .TextAlign = C1.Win.C1FlexGrid.TextAlignEnum.CenterCenter
                .Style.WordWrap = True
                .Sort = C1.Win.C1FlexGrid.SortFlags.UseColSort
                .Visible = True
            End With

            With .Cols(2)
                .Caption = "患者姓名"
                .Width = 150
                .AllowMerging = True
                .AllowDragging = True
                .AllowEditing = False
                .TextAlign = C1.Win.C1FlexGrid.TextAlignEnum.LeftCenter
                .Style.WordWrap = True
                .Sort = C1.Win.C1FlexGrid.SortFlags.UseColSort
                .Visible = True
            End With

            With .Cols(3)
                .Caption = "性别"
                .Width = 60
                .AllowMerging = True
                .AllowDragging = True
                .AllowEditing = False
                .TextAlign = C1.Win.C1FlexGrid.TextAlignEnum.CenterCenter
                .Style.WordWrap = True
                .Sort = C1.Win.C1FlexGrid.SortFlags.UseColSort
                .Visible = True
            End With

            With .Cols(4)
                .Caption = "年龄"
                .Width = 60
                .AllowMerging = True
                .AllowDragging = True
                .AllowEditing = False
                .TextAlign = C1.Win.C1FlexGrid.TextAlignEnum.RightCenter
                .Style.WordWrap = True
                .Sort = C1.Win.C1FlexGrid.SortFlags.UseColSort
                .Visible = True
            End With

            With .Cols(5)
                .Caption = "处方时间"
                .Width = 120
                .AllowMerging = True
                .AllowDragging = True
                .AllowEditing = False
                .TextAlign = C1.Win.C1FlexGrid.TextAlignEnum.LeftCenter
                .Style.WordWrap = True
                .Sort = C1.Win.C1FlexGrid.SortFlags.UseColSort
                .Visible = True
                .Format = "yyyy-MM-dd HH:mm"
            End With

            With .Cols(6)
                .Caption = "科室"
                .Width = 130
                .AllowMerging = True
                .AllowDragging = True
                .AllowEditing = False
                .TextAlign = C1.Win.C1FlexGrid.TextAlignEnum.LeftCenter
                .Style.WordWrap = True
                .Sort = C1.Win.C1FlexGrid.SortFlags.UseColSort
                .Visible = True
            End With
            With .Cols(7)
                .Caption = "医生"
                .Width = 100
                .AllowMerging = True
                .AllowDragging = True
                .AllowEditing = False
                .TextAlign = C1.Win.C1FlexGrid.TextAlignEnum.LeftCenter
                .Style.WordWrap = True
                .Sort = C1.Win.C1FlexGrid.SortFlags.UseColSort
                .Visible = True
            End With
            With .Cols(8)
                .Caption = "金额"
                .Width = 80
                .AllowMerging = True
                .AllowDragging = True
                .AllowEditing = False
                .TextAlign = C1.Win.C1FlexGrid.TextAlignEnum.RightCenter
                .Style.WordWrap = True
                .Sort = C1.Win.C1FlexGrid.SortFlags.UseColSort
                .Visible = True
                .Format = "0.00###"
            End With

            With .Cols(9)
                .Caption = "类型"
                .Width = 70
                .AllowMerging = True
                .AllowDragging = True
                .AllowEditing = False
                .TextAlign = C1.Win.C1FlexGrid.TextAlignEnum.CenterCenter
                .Style.WordWrap = True
                .Sort = C1.Win.C1FlexGrid.SortFlags.UseColSort
                .Visible = True
            End With

            .Redraw = True
        End With

    End Sub

    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click
        Dim check1 As String = "''"
        For Each Ro In My_Dataset.Tables("领药患者").Select("IsCheck=True")
            'If HisVar.HisVar.Sqldal.GetSingle("select count(*) from bl_czd where Cf_Code='" & Ro.Item("Cf_Code") & "' ") = 0 Then
            '    Continue For
            'End If
            check1 = check1 & ",'" & Ro.Item("Cf_Code") & "'"
        Next
        If HisVar.HisVar.Sqldal.GetSingle("select count(*) from bl_czd where cf_code in (" & check1 & ")  ") > 0 Then
            If MsgBox("存在输液处置卡！如已经打印,请点击确定进行领药，否则，请取消后先打印输液卡！", MsgBoxStyle.Information + MsgBoxStyle.OkCancel, "提示:") = MsgBoxResult.Cancel Then
                Exit Sub
            End If
        End If

        Dim bllBl_Cfyp As New BLL.BllBl_Cfyp
        Dim table As DataTable = bllBl_Cfyp.StockCheck(check1, "")
        Dim str As String = ""

        If table.Rows.Count <> 0 Then
            For Each row As DataRow In table.Rows
                str += "药品名称: " & row("Yp_Name").ToString() & vbCrLf
            Next
            If MsgBox(str & "大于库存数量，确定继续领药吗！", MsgBoxStyle.Information + MsgBoxStyle.OkCancel, "提示：") = MsgBoxResult.Cancel Then Exit Sub
        End If


        iniOperate.iniopreate.WriteINI("汇总领药", "汇总打印方式", C1Combo6.Text, HisVar.HisVar.Parapath & "\Config.ini")
        If My_Dataset.Tables("领药患者").Select("IsCheck=True").Length = 0 Then MsgBox("请选择要汇总领药的处方！", MsgBoxStyle.Information, "提示") : Exit Sub
        C1Combo6.Enabled = False

        C1FlexGrid1.Redraw = False
        Dim arr As New ArrayList
        Dim R As DataRow
        Dim V_LydCode As String = Format(Now, "yyyyMMddHHmmssfff")
        For Each R In My_Dataset.Tables("领药患者").Select("IsCheck=True")
            arr.Add("Insert into Bl_Cflyd(Cf_Code,Ry_Name,Ks_Name,Ys_Name,Cf_YpMoney,Jsr_Code,Lyd_Code,Ly_Qr)Values('" & R.Item("Cf_Code") & "','" & R.Item("Ry_Name") & "','" & R.Item("Ks_Name") & "','" & R.Item("Ys_Name") & "','" & R.Item("Cf_YpMoney") & "','" & HisVar.HisVar.JsrCode & "','" & V_LydCode & "','是')")
            arr.Add("Update Bl_Cf Set Ly_WC='是'  Where Cf_Code='" & R.Item("Cf_Code") & "'")
            My_Dataset.Tables("领药患者").Rows.Remove(R)
        Next
        If My_Dataset.Tables("领药患者").Rows.Count > 0 Then C1FlexGrid1.Row = 1

        Try
            HisVar.HisVar.Sqldal.ExecuteSqlTran(arr)
            C1FlexGrid1.Redraw = True
            If C1Combo6.Text = "不使用汇总打印" Then
                MsgBox("领药完成，请到药房拿药！", MsgBoxStyle.Information, "提示")
            Else
                MsgBox("领药完成，点击确定打印领药单！", MsgBoxStyle.Information, "提示")
                Dim print As New ZTHisNurse.Print
                print.PrintHzlyd(V_LydCode, C1Combo6.Text, Nothing)

                'Dim StiRpt As New StiReport
                'Dim Str_Select As String = "Select Bl_CfLyd.Cf_Code,Bl.Bl_Code,Ry_BlCode,Bl_Cflyd.Ry_Name,Cf_Sl,Bl_Cfyp.Cf_Money,Cf_Dj,Mx_Gg,Mx_Cd,Yp_Name,Mx_XsDw,Yp_Ph,Yp_Yxq,Bc_Name,Ks_Name from Bl,Zd_YyBc,Bl_Cflyd,Bl_Cf,Bl_Cfyp,V_YpKc  Where Bl.Bl_Code=Bl_Cf.Bl_Code and Bl.Bc_Code=Zd_YyBc.Bc_Code and Bl_CfLyd.Cf_Code=Bl_Cf.Cf_Code and Bl_Cf.Cf_Code=Bl_CfYp.Cf_Code and Bl_CfYp.Xx_Code=V_YpKc.Xx_Code  And lyd_Code='" & V_LydCode & "' order by Cf_Id"
                'HisVar.HisVar.Sqldal.QueryDt(My_Dataset, Str_Select, "汇总领药", True)
                'StiRpt.RegData(My_Dataset.Tables("汇总领药"))
                'StiRpt.Load(".\Rpt\护士站汇总领药.mrt")
                'StiRpt.ReportName = "护士站汇总领药"



                'If C1Combo6.Text = "按药品汇总打印" Then
                '    Dim groupBand As New StiGroupHeaderBand
                '    groupBand = StiRpt.GetComponents("GroupHeaderBand1")
                '    groupBand.Condition.Value = ""
                '    groupBand.Height = 0

                '    TryCast(StiRpt.Pages(0).GetComponents.Item("Text21"), StiText).Text = ""
                '    TryCast(StiRpt.Pages(0).GetComponents.Item("Text20"), StiText).Text = ""
                '    TryCast(StiRpt.Pages(0).GetComponents.Item("Text22"), StiText).Text = ""
                '    TryCast(StiRpt.Pages(0).GetComponents.Item("Text23"), StiText).Text = ""
                '    StiRpt.GetComponents("Text21").Enabled = False
                '    StiRpt.GetComponents("Text20").Enabled = False
                '    StiRpt.GetComponents("Text22").Enabled = False
                '    StiRpt.GetComponents("Text23").Enabled = False
                'End If


                'StiRpt.Compile()
                'StiRpt("标题") = "汇 总 领 药 单"
                'StiRpt("操作员") = "操作员:" & HisVar.HisVar.JsrName
                'StiRpt("医嘱数量") = "医嘱数量:" & HisVar.HisVar.Sqldal.GetSingle("Select Count(*) from Bl_CfLyd where Lyd_Code='" & V_LydCode & "'")
                'StiRpt("领药日期") = "领药日期:" & Format(Now, "yyyy年M月d日")
                '' StiRpt.Design()
                'StiRpt.Show()
            End If

        Catch ex As Exception
            C1FlexGrid1.Redraw = True
            MsgBox(ex.Message.ToString, MsgBoxStyle.Information, "提示：")
        End Try
        C1Combo6.Enabled = True
    End Sub

    Private Sub C1FlexGrid1_OwnerDrawCell(ByVal sender As System.Object, ByVal e As C1.Win.C1FlexGrid.OwnerDrawCellEventArgs) Handles C1FlexGrid1.OwnerDrawCell
        ' ignore fixed cells
        If e.Row < C1FlexGrid1.Rows.Fixed OrElse e.Col < C1FlexGrid1.Cols.Fixed Then
            Exit Sub
        End If

        ' apply custom style if reorder level is critical
        If C1FlexGrid1.Cols(e.Col).Caption = "类型" Then
            If C1FlexGrid1(e.Row, e.Col) & "" = "长期医嘱" Then
                e.Style = C1FlexGrid1.Styles("Critical")
            End If
        End If
    End Sub

    Private Sub CheckBox1_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox1.CheckedChanged
        Dim K As Integer = 0
        Dim V_KsName As String = HisVar.HisVar.Sqldal.GetSingle("Select ks_Name from Zd_YyKs where ks_Code='" & HisVar.HisVar.XmKs & "'")
        If V_KsName & "" = "" Then MsgBox("该操作员未指定所属科室，请管理员指定所属科室后使用该功能！", MsgBoxStyle.Information, "提示") : Exit Sub
        If Me.CheckBox1.Checked = True Then
            For K = 1 To Me.C1FlexGrid1.Rows.Count - 1

                If C1FlexGrid1.Item(K, 6) = V_KsName Then Me.C1FlexGrid1.Item(K, 0) = True
            Next
        Else
            For K = 1 To Me.C1FlexGrid1.Rows.Count - 1
                If C1FlexGrid1.Item(K, 6) = V_KsName Then Me.C1FlexGrid1.Item(K, 0) = False
            Next
        End If
    End Sub

    Private Sub Button2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button2.Click
        If My_Dataset.Tables("领药患者").Select("IsCheck=True").Length = 0 Then MsgBox("请选择要拒领的处方！", MsgBoxStyle.Information, "提示") : Exit Sub
        If MsgBox("是否确认将已选择处方退回至医嘱录入？", MsgBoxStyle.DefaultButton1 + MsgBoxStyle.OkCancel, "提示") = MsgBoxResult.Cancel Then Exit Sub
        Dim My_Row As DataRow
        Dim Arr As New ArrayList
        For Each My_Row In My_Dataset.Tables("领药患者").Select("IsCheck=True")
            Arr.Add("Update Bl_Cf Set Cf_Print='否' where Cf_Code='" & My_Row.Item("Cf_Code") & "'")
            My_Dataset.Tables("领药患者").Rows.Remove(My_Row)

        Next
        If My_Dataset.Tables("领药患者").Rows.Count > 0 Then C1FlexGrid1.Row = 1
        Try
            HisVar.HisVar.Sqldal.ExecuteSqlTran(Arr)
            C1FlexGrid1.Redraw = True
            MsgBox("处方成功退回至医嘱录入窗口，请根据日期进行查找修改！", MsgBoxStyle.Information, "提示")
        Catch ex As Exception
            C1FlexGrid1.Redraw = True
            MsgBox(ex.Message.ToString, MsgBoxStyle.Information, "提示：")
        End Try

    End Sub
    Private Sub Button6_Click(sender As Object, e As EventArgs) Handles Button6.Click
        If My_Dataset.Tables("领药患者").Select("IsCheck=True").Length = 0 Then
            MsgBox("请选择要打印处置单的患者！")
            Exit Sub
        End If
        Dim check1 As String = "''"
        For Each Ro In My_Dataset.Tables("领药患者").Select("IsCheck=True")
            If HisVar.HisVar.Sqldal.GetSingle("select count(*) from bl_czd where Cf_Code='" & Ro.Item("Cf_Code") & "' ") = 0 Then
                Continue For
            End If
            check1 = check1 & ",'" & Ro.Item("Cf_Code") & "'"
        Next
        Dim print As New ZTHisNurse.Print
        print.PrintSypq(check1, "住院", Nothing)

    End Sub
    Private Sub Button3_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button3.Click
        If My_Dataset.Tables("领药患者").Select("IsCheck=True").Length = 0 Then
            MsgBox("请选择要打印处置单的患者！")
            Exit Sub
        End If
        Dim check1 As String = "''"
        For Each Ro In My_Dataset.Tables("领药患者").Select("IsCheck=True")
            If HisVar.HisVar.Sqldal.GetSingle("select count(*) from bl_czd where Cf_Code='" & Ro.Item("Cf_Code") & "' ") = 0 Then
                Continue For
            End If
            check1 = check1 & ",'" & Ro.Item("Cf_Code") & "'"
        Next
        Dim print As ZTHisPublicFunction.ISyk
        print = ZTHisPublicFunction.SykFactory.CreateSykObject()
        print.Print(check1, "住院", Nothing)
        'Dim Str As String = "SELECT Czd_Id,Cf_Code,Z_Name,Xx_Code,Use_Num,Czd_Memo,Yp_Name,Mx_Gg,Mx_Cd,Yp_Yxq,Ys_Name,Ry_Name,Bc_Name,Bl_Code ,Ry_Blcode,Mx_Dw  FROM Bl_Czd where  cf_code in (" & check1 & ")    order by Cf_Code,z_name"

        'HisVar.HisVar.Sqldal.QueryDt(My_Dataset, Str, "分组1", True)

        'Dim StiRpt As New StiReport
        ''StiRpt.Load("Rpt\输液卡（连打）.mrt")
        ''
        'If HisPara.PublicConfig.XqName.Contains("丰南") Then
        '    StiRpt.Load("Rpt\输液卡（丰南连打）.mrt")  '丰南要求每个患者的每个组单独打印
        'Else
        '    StiRpt.Load("Rpt\输液卡（连打）.mrt")
        'End If
        'StiRpt.ReportName = "输液卡"
        ''Dim I As Integer
        ''For Each Ro In My_Dataset.Tables("领药患者").Select("IsCheck=True")
        ''    Dim Strr As String = "SELECT Czd_Id,Cf_Code,Z_Name,Xx_Code,Use_Num,Czd_Memo,Yp_Name,Mx_Gg,Mx_Cd,Yp_Yxq,Ys_Name,Ry_Name,Bc_Name,Bl_Code ,Ry_Blcode,Mx_Dw  FROM Bl_Czd where  cf_code='" & Ro.Item("Cf_Code") & "'    order by Cf_Code,z_name"

        ''    HisVar.HisVar.Sqldal.QueryDt(My_Dataset, Strr, "分组2", True)
        ''    Dim M As Integer = My_Dataset.Tables("分组2").Rows.Count - 1
        ''    Dim K As Integer = 0
        ''    Dim Str1 As String
        ''    Dim Row1 As DataRow
        ''    Dim Row2 As DataRow
        ''    Dim V_NewRow As DataRow

        ''    For I = 0 To M
        ''        Row1 = My_Dataset.Tables("分组2").Rows.Item(I)
        ''        If I = M Then
        ''            Str1 = True
        ''        Else
        ''            Row2 = My_Dataset.Tables("分组2").Rows.Item(I + 1)
        ''            Str1 = Row1.Item("z_name") <> Row2.Item("z_name")
        ''        End If
        ''        If Str1 Then
        ''            '
        ''            If My_Dataset.Tables("分组2").Select("z_name='" & Row1.Item("z_name") & "' and cf_code='" & Row1.Item("cf_code") & "'").Length Mod 10 <> 0 Then

        ''                For V_TbRowCount = 1 To 10 - (My_Dataset.Tables("分组2").Select("z_name='" & Row1.Item("z_name") & "' and cf_code='" & Row1.Item("cf_code") & "'").Length Mod 10)

        ''                    V_NewRow = My_Dataset.Tables("分组1").NewRow
        ''                    With V_NewRow

        ''                        .Item("Cf_Code") = Row1.Item("Cf_Code")
        ''                        .Item("Z_Name") = Row1.Item("Z_Name")
        ''                        .Item("Bc_Name") = Row1.Item("Bc_Name")
        ''                        .Item("Bl_Code") = Row1.Item("Bl_Code")
        ''                        .Item("Ry_Blcode") = Row1.Item("Ry_Blcode")
        ''                        .Item("Ys_Name") = DBNull.Value
        ''                        .Item("Ry_Name") = DBNull.Value
        ''                        .Item("Xx_Code") = DBNull.Value
        ''                        .Item("Use_Num") = DBNull.Value
        ''                        .Item("Czd_Memo") = DBNull.Value
        ''                        .Item("Yp_Name") = DBNull.Value
        ''                        .Item("Mx_Gg") = DBNull.Value
        ''                        .Item("Yp_Yxq") = DBNull.Value
        ''                        .Item("Mx_Dw") = DBNull.Value
        ''                    End With
        ''                    My_Dataset.Tables("分组1").Rows.Add(V_NewRow)
        ''                    V_NewRow.AcceptChanges()
        ''                Next
        ''            End If
        ''        End If
        ''    Next
        ''    My_Dataset.Tables("分组1").DefaultView.Sort = "z_name"

        ''Next
        'StiRpt.RegData(My_Dataset.Tables("分组1"))
        'StiRpt.Pages(0).PaperSize = Printing.PaperKind.Custom
        'StiRpt.Pages(0).PageHeight = 27.9
        'StiRpt.Compile()
        'StiRpt("打印时间") = Format(Now, "yyyy年MM月dd日")

        ''StiRpt.Design()
        'StiRpt.Show()
    End Sub

    Private Sub C1FlexGrid1_RowColChange(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1FlexGrid1.RowColChange
        Dim V_Code As String = ""
        If C1FlexGrid1.Row >= 0 Then
            V_Code = C1FlexGrid1.Item(C1FlexGrid1.Row, 1)
        Else
            V_Code = ""
        End If


        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "Select V_YpKc.Xx_Code,Cf_Id,Cf_Dj,Cf_Sl,Cf_Money,Mx_Gyzz,Mx_Gg,Mx_Cd ,Mx_XsDw,Yp_Name,Cf_Lb,V_YpKc.Yp_Ph,V_YpKc.Yp_Yxq,Yp_Yfyl From Bl_Cfyp,V_YpKc Where Bl_Cfyp.Xx_Code=V_YpKc.Xx_Code And Cf_Code='" & V_Code & "' Order By Cf_Id", "用药明细", True)

        With Me.C1TrueDBGrid1
            .SetDataBinding(My_Dataset, "用药明细", True)
            .MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRowRaiseCell
        End With
    End Sub

    Private Sub Button4_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button4.Click
        Dim frm As New Hsz_Cqcf_Dr(My_Dataset.Tables("领药患者"))
        frm.ShowDialog()
    End Sub

    Private Function F_MaxCode(ByVal V_MaxCode As String)   '出库编码
        Dim My_Cc As New BaseClass.C_Cc()
        Dim V_Code As String = V_MaxCode
        My_Cc.Get_MaxCode("Bl_Cf", "Cf_Code", 14, "Left(Cf_Code,10)", HisVar.HisVar.WsyCode & V_MaxCode)
        F_MaxCode = My_Cc.编码
        Return F_MaxCode
    End Function

    Private Sub Button5_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button5.Click
        If HisPara.PublicConfig.ZyCfKsXz = "是" Then
            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "select 'false'  as isCheck,Cf_Code,Ry_Name,Ry_Sex,datediff(yyyy,Ry_Csdate,Cf_Date)as Ry_Age,Cf_Date+Cf_Time as Cf_Date,Ks_Name,Ys_Name,Cf_YpMoney,CASE len(AutoCf_Code) WHEN 14 THEN '长期医嘱' ELSE AutoCf_Code END AS AutoCf_Code1  From Bl,Bl_Cf,Zd_YyKs,Zd_YyYs Where Bl.Bl_Code=Bl_Cf.Bl_Code and Bl_Cf.Ks_Code=Zd_YyKs.Ks_Code and Bl_Cf.Ys_Code=Zd_YyYs.Ys_Code  and Ry_CyDate Is Null  And Cf_Print='是' and isnull(Ly_Wc,'否')='否' and Cf_Qr='否'  and Exists (select Xx_Code from Bl_Cfyp where Bl_Cfyp.Cf_Code=Bl_Cf.Cf_Code) And not exists (Select Cf_Code from Bl_Cflyd where Bl_Cflyd.Cf_Code=Bl_Cf.Cf_Code) and Bl.Ks_Code like '%" & HisVar.HisVar.XmKs & "%'  Order by Cf_Code ", "领药患者", True)
        Else
            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "select 'false'  as isCheck,Cf_Code,Ry_Name,Ry_Sex,datediff(yyyy,Ry_Csdate,Cf_Date)as Ry_Age,Cf_Date+Cf_Time as Cf_Date,Ks_Name,Ys_Name,Cf_YpMoney,CASE len(AutoCf_Code) WHEN 14 THEN '长期医嘱' ELSE AutoCf_Code END AS AutoCf_Code1  From Bl,Bl_Cf,Zd_YyKs,Zd_YyYs Where Bl.Bl_Code=Bl_Cf.Bl_Code and Bl_Cf.Ks_Code=Zd_YyKs.Ks_Code and Bl_Cf.Ys_Code=Zd_YyYs.Ys_Code  and Ry_CyDate Is Null  And Cf_Print='是' and isnull(Ly_Wc,'否')='否' and Cf_Qr='否'  and Exists (select Xx_Code from Bl_Cfyp where Bl_Cfyp.Cf_Code=Bl_Cf.Cf_Code) And not exists (Select Cf_Code from Bl_Cflyd where Bl_Cflyd.Cf_Code=Bl_Cf.Cf_Code)  Order by Cf_Code ", "领药患者", True)
        End If

        Flex_Init(C1FlexGrid1)
    End Sub



End Class
