﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="0" />
    <DataSources isList="true" count="0" />
    <Relations isList="true" count="0" />
    <Report isRef="0" />
    <Variables isList="true" count="21">
      <value>,打印时间,打印时间,System.String,,False,False</value>
      <value>,标题,标题,System.String,,False,False</value>
      <value>,日结时间,日结时间,System.String,,False,False</value>
      <value>,日结单位,日结单位,System.String,,False,False</value>
      <value>,大写,大写,System.String,,False,False</value>
      <value>,交款人,交款人,System.String,,False,False</value>
      <value>,日补偿人次,日补偿人次,System.String,,False,False</value>
      <value>,日作废人次,日作废人次,System.String,,False,False</value>
      <value>,人次小计,人次小计,System.String,,False,False</value>
      <value>,门诊费用,门诊费用,System.String,,False,False</value>
      <value>,门诊作废费用,门诊作废费用,System.String,,False,False</value>
      <value>,门诊费用小计,门诊费用小计,System.String,,False,False</value>
      <value>,日补偿金额,日补偿金额,System.String,,False,False</value>
      <value>,日作废金额,日作废金额,System.String,,False,False</value>
      <value>,日补偿费用小计,日补偿费用小计,System.String,,False,False</value>
      <value>,家庭账户支付金额,家庭账户支付金额,System.String,,False,False</value>
      <value>,家庭账户作废支付金额,家庭账户作废支付金额,System.String,,False,False</value>
      <value>,家庭账户支付小计,家庭账户支付小计,System.String,,False,False</value>
      <value>,自付金额,自付金额,System.String,,False,False</value>
      <value>,作废自付金额,作废自付金额,System.String,,False,False</value>
      <value>,自付金额小计,自付金额小计,System.String,,False,False</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="2" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="1">
        <ReportTitleBand1 Ref="3" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,19,7.5</ClientRectangle>
          <Components isList="true" count="40">
            <Text1 Ref="4" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,19,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>黑体,15,Regular,Point,False,134</Font>
              <Guid>0a40c337a7e448f084c142dd687eb708</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text1</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{标题}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text1>
            <Text2 Ref="5" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>12.4,1,6.6,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,0</Font>
              <Guid>045ea49074734b868ffd5dcd4931e4cc</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text2</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{打印时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text2>
            <Text48 Ref="6" type="Text" isKey="true">
              <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,3,1.9,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75</Font>
              <Guid>01abb3de0c9f45b4a190c8897dd383ed</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text48</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>日作废人次</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text48>
            <Text49 Ref="7" type="Text" isKey="true">
              <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.9,3,1.7,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75</Font>
              <Guid>969757ce77594c1986a07b066ace070c</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text49</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{日作废人次}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text49>
            <Text50 Ref="8" type="Text" isKey="true">
              <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.6,3,1.5,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75</Font>
              <Guid>cad80eb873da4bf3b5b1907ea79bc6ee</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text50</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>门诊作废</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text50>
            <Text51 Ref="9" type="Text" isKey="true">
              <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>5.1,3,1.9,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75</Font>
              <Guid>f6882cd08c314923b1b47b67938f5f20</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text51</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{门诊作废费用}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="10" type="CustomFormat" isKey="true">
                <StringFormat>0.00##</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text51>
            <Text52 Ref="11" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>17.1,2,1.9,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75</Font>
              <Guid>4709876a33a9420c86c5fb7bda97fe7b</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text52</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{自付金额}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="12" type="CustomFormat" isKey="true">
                <StringFormat>0.00##</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text52>
            <Text55 Ref="13" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15.6,3,1.5,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75</Font>
              <Guid>5d253e904aaf476ab3d5ef66368cb0ef</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text55</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>作废自付</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text55>
            <Text56 Ref="14" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>17.1,3,1.9,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75</Font>
              <Guid>274fa5d8f6d94201869a3fc5faaab23e</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text56</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{作废自付金额}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="15" type="CustomFormat" isKey="true">
                <StringFormat>0.00##</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text56>
            <Text58 Ref="16" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13.7,3,1.9,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75</Font>
              <Guid>46517d8e3d4241158614925803526aff</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text58</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{家庭账户作废支付金额}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="17" type="CustomFormat" isKey="true">
                <StringFormat>0.00##</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text58>
            <Text59 Ref="18" type="Text" isKey="true">
              <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,2,1.9,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75</Font>
              <Guid>88a742fcb46f46b182b5b32078dcb693</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text59</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>日补偿人次</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text59>
            <Text60 Ref="19" type="Text" isKey="true">
              <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.9,2,1.7,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75</Font>
              <Guid>1a5593fb972d4b099872b54769565e37</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text60</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{日补偿人次}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text60>
            <Text61 Ref="20" type="Text" isKey="true">
              <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.6,2,1.5,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75</Font>
              <Guid>d4cfe5ba12034516b7695c237a2795cc</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text61</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>门诊费用</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text61>
            <Text62 Ref="21" type="Text" isKey="true">
              <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7,2,1.9,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75</Font>
              <Guid>56edb39036b84c48b6c56d1da3fdeb29</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text62</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>日补偿金额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text62>
            <Text63 Ref="22" type="Text" isKey="true">
              <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>8.9,2,1.9,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75</Font>
              <Guid>797372dd0382460da1b1ec1e9a6deb03</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text63</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{日补偿金额}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="23" type="CustomFormat" isKey="true">
                <StringFormat>0.00##</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text63>
            <Text64 Ref="24" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15.6,2,1.5,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75</Font>
              <Guid>0f4f55132b7d46549e977fa0093f4693</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text64</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>自费金额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text64>
            <Text65 Ref="25" type="Text" isKey="true">
              <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>5.1,2,1.9,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75</Font>
              <Guid>3d5bc73987e940348b0f533573221008</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text65</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{门诊费用}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="26" type="CustomFormat" isKey="true">
                <StringFormat>0.00##</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text65>
            <Text66 Ref="27" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>10.8,2,2.9,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75</Font>
              <Guid>d2eaf94d7de14ee0888de6407432501e</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text66</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>家庭账户支付金额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text66>
            <Text67 Ref="28" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13.7,2,1.9,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75</Font>
              <Guid>bbc0509fea224771a1855573870ad853</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text67</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{家庭账户支付金额}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="29" type="CustomFormat" isKey="true">
                <StringFormat>0.00##</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text67>
            <Text3 Ref="30" type="Text" isKey="true">
              <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7,3,1.9,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75</Font>
              <Guid>ec322a61a2464537b67cedc8e6b45827</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text3</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>日作废金额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text3>
            <Text4 Ref="31" type="Text" isKey="true">
              <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>8.9,3,1.9,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75</Font>
              <Guid>5a5a1fd836d84d2eafe1a216ae2528ea</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text4</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{日作废金额}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="32" type="CustomFormat" isKey="true">
                <StringFormat>0.00##</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text4>
            <Text5 Ref="33" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>10.8,3,2.9,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75</Font>
              <Guid>c5995f8815d645429b1b5b101df8dac4</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text5</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>家庭账户作废支付</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text5>
            <Text6 Ref="34" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>6.5,1,5.9,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,0</Font>
              <Guid>1c6289da534b4fdca3f58f3ee5fcfedf</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text6</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{日结时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text6>
            <Text7 Ref="35" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1,6.5,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,0</Font>
              <Guid>c4f832be359d401db03c39b76abef1d1</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text7</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{日结单位}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text7>
            <Text8 Ref="36" type="Text" isKey="true">
              <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,4,1.9,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75</Font>
              <Guid>30e88a0b8e314daabd200bc83ffb2c65</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text8</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>小计</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text8>
            <Text10 Ref="37" type="Text" isKey="true">
              <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.9,4,1.7,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75</Font>
              <Guid>28fd3e2e6ba44435880066e954364eed</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text10</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{人次小计}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text10>
            <Text11 Ref="38" type="Text" isKey="true">
              <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.6,4,1.5,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75</Font>
              <Guid>3aef9a2e9d8c4e9e986404144fadc0d0</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text11</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text11>
            <Text12 Ref="39" type="Text" isKey="true">
              <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>5.1,4,1.9,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75</Font>
              <Guid>439cce4afcc448ecb1dd9017bc3a06dd</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text12</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{门诊费用小计}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="40" type="CustomFormat" isKey="true">
                <StringFormat>0.00##</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text12>
            <Text13 Ref="41" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15.6,4,1.5,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75</Font>
              <Guid>1cf09a3e9b564d20911f07cb7dbc1e3c</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text13</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text13>
            <Text14 Ref="42" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>17.1,4,1.9,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75</Font>
              <Guid>2a048b4de7b9478a9d5879ba9cfa3086</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text14</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{自付金额小计}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="43" type="CustomFormat" isKey="true">
                <StringFormat>0.00##</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text14>
            <Text15 Ref="44" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13.7,4,1.9,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75</Font>
              <Guid>8109bfcf48c4485c815625fced5f6a4b</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text15</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{家庭账户支付小计}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="45" type="CustomFormat" isKey="true">
                <StringFormat>0.00##</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text15>
            <Text16 Ref="46" type="Text" isKey="true">
              <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7,4,1.9,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75</Font>
              <Guid>eac0bb03e2c34f3483d70ecb68e005dd</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text16</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text16>
            <Text17 Ref="47" type="Text" isKey="true">
              <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>8.9,4,1.9,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75</Font>
              <Guid>c78cfa6b76184d05a02bc45803138700</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text17</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{日补偿费用小计}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="48" type="CustomFormat" isKey="true">
                <StringFormat>0.00##</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text17>
            <Text18 Ref="49" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>10.8,4,2.9,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75</Font>
              <Guid>9f3df12368ab4cbb8cd40e7f8f488716</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text18</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text18>
            <Text9 Ref="50" type="Text" isKey="true">
              <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,5,5.1,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75</Font>
              <Guid>264602ea79b7414abdca00f49ce3e98f</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text9</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>门诊统筹支付金额（大写）</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text9>
            <Text19 Ref="51" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>5.1,5,13.9,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75</Font>
              <Guid>82fde5d1bca741f792f374681aa56e9f</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text19</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{大写}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text19>
            <Text20 Ref="52" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>1.8,6,1.9,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75</Font>
              <Guid>d93ad8b02838460fa77002c43b317cb0</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text20</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>出纳员</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text20>
            <Text21 Ref="53" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>6.4,6,1.9,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75</Font>
              <Guid>f21e28c23ae14411b9035f034e3388e5</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text21</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>交款人</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text21>
            <Text22 Ref="54" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>11.4,6,1.9,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75</Font>
              <Guid>50f0e0b3b2284621b4b80c672b43806e</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text22</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>单位负责人</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text22>
            <Text23 Ref="55" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>8.3,6,3.1,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75</Font>
              <Guid>6f78ded045044f9ab1c7bb70af7d5018</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text23</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{交款人}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text23>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>ReportTitleBand1</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
        </ReportTitleBand1>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>2fb177867e564de28323a8c8ccfdad91</Guid>
      <Margins>1,1,1,1</Margins>
      <Name>Page1</Name>
      <PageHeight>9.9</PageHeight>
      <PageWidth>21</PageWidth>
      <PaperSize>A4</PaperSize>
      <Report isRef="0" />
      <Watermark Ref="56" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page1>
  </Pages>
  <PrinterSettings Ref="57" type="Stimulsoft.Report.Print.StiPrinterSettings" isKey="true" />
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>门诊统筹日结</ReportAlias>
  <ReportChanged>11/27/2012 2:45:09 PM</ReportChanged>
  <ReportCreated>10/13/2012 10:39:58 AM</ReportCreated>
  <ReportFile>D:\正在进行时\唐山\正在修改版\his2010\His2010\Rpt\门诊统筹日结.mrt</ReportFile>
  <ReportGuid>493aee0c37a04befa930fb5c00005ab0</ReportGuid>
  <ReportName>门诊统筹日结</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2011.2.1026</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class Report : Stimulsoft.Report.StiReport
    {
        public Report()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
        #endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>