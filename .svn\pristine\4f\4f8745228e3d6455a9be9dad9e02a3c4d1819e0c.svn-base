﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{D79BB10E-64C3-4865-BEE0-2D72454F47DF}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>ZTHisInpatient</RootNamespace>
    <AssemblyName>ZTHisInpatient</AssemblyName>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\output\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>x86</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="C1.Win.4, Version=4.0.20192.375, Culture=neutral, PublicKeyToken=944ae1ea0e47ca04, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.C1Command.4, Version=4.0.20192.375, Culture=neutral, PublicKeyToken=e808566f358766d8, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.C1FlexGrid.4, Version=4.0.20192.375, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.C1Input.4, Version=4.0.20192.375, Culture=neutral, PublicKeyToken=7e7ff60f0c214f9a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.C1List.4, Version=4.0.20192.375, Culture=neutral, PublicKeyToken=6b24f8f981dbd7bc, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.C1Ribbon.4, Version=4.0.20192.375, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.C1SplitContainer.4, Version=4.0.20222.566, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL" />
    <Reference Include="C1.Win.C1TrueDBGrid.4, Version=4.0.20192.375, Culture=neutral, PublicKeyToken=75ae3fb0e2b1e0da, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.Input.MultiSelect.4, Version=4.0.20192.375, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.TreeView.4, Version=4.0.20192.375, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="Microsoft.VisualBasic" />
    <Reference Include="Stimulsoft.Base, Version=2011.2.1026.0, Culture=neutral, PublicKeyToken=ebe6666cba19647a, processorArchitecture=MSIL" />
    <Reference Include="Stimulsoft.Report, Version=2011.2.1026.0, Culture=neutral, PublicKeyToken=ebe6666cba19647a, processorArchitecture=MSIL" />
    <Reference Include="Stimulsoft.Report.Win, Version=2011.2.1026.0, Culture=neutral, PublicKeyToken=ebe6666cba19647a, processorArchitecture=MSIL" />
    <Reference Include="SunnyUI, Version=3.1.9.0, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb, processorArchitecture=MSIL">
      <HintPath>..\packages\SunnyUI.3.1.9\lib\net40\SunnyUI.dll</HintPath>
    </Reference>
    <Reference Include="SunnyUI.Common, Version=3.1.2.0, Culture=neutral, PublicKeyToken=5a271fb7ba597231, processorArchitecture=MSIL">
      <HintPath>..\packages\SunnyUI.Common.3.1.2\lib\net40\SunnyUI.Common.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Design" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="ZTHisInpatient\Class\Print.cs" />
    <Compile Include="ZTHisInpatient\住院日结\Zy_Jz.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ZTHisInpatient\住院日结\Zy_Jz.Designer.cs">
      <DependentUpon>Zy_Jz.cs</DependentUpon>
    </Compile>
    <Compile Include="ZTHisInpatient\住院日结\Zy_Jz_Zf.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ZTHisInpatient\住院日结\Zy_Jz_Zf.Designer.cs">
      <DependentUpon>Zy_Jz_Zf.cs</DependentUpon>
    </Compile>
    <Compile Include="ZTHisInpatient\住院查询\01在院患者费用\Cx_ZyCfMx.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ZTHisInpatient\住院查询\01在院患者费用\Cx_ZyCfMx.Designer.cs">
      <DependentUpon>Cx_ZyCfMx.cs</DependentUpon>
    </Compile>
    <Compile Include="ZTHisInpatient\出院召回\BlEdit.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ZTHisInpatient\出院召回\BlEdit.Designer.cs">
      <DependentUpon>BlEdit.cs</DependentUpon>
    </Compile>
    <Compile Include="ZTHisInpatient\住院查询\01在院患者费用\Cx_ZyYjMx.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ZTHisInpatient\住院查询\01在院患者费用\Cx_ZyYjMx.Designer.cs">
      <DependentUpon>Cx_ZyYjMx.cs</DependentUpon>
    </Compile>
    <Compile Include="ZTHisInpatient\住院查询\04住院未发药查询\Cx_WZyHz1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ZTHisInpatient\住院查询\04住院未发药查询\Cx_WZyHz1.Designer.cs">
      <DependentUpon>Cx_WZyHz1.cs</DependentUpon>
    </Compile>
    <Compile Include="ZTHisInpatient\住院查询\06缴纳押金查询\Cx_Yj.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ZTHisInpatient\住院查询\06缴纳押金查询\Cx_Yj.Designer.cs">
      <DependentUpon>Cx_Yj.cs</DependentUpon>
    </Compile>
    <Compile Include="ZTHisInpatient\住院查询\07患者诊疗项目查询\ZyXmCx.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ZTHisInpatient\住院查询\07患者诊疗项目查询\ZyXmCx.Designer.cs">
      <DependentUpon>ZyXmCx.cs</DependentUpon>
    </Compile>
    <Compile Include="ZTHisInpatient\住院查询\08国家版病案首页导出\BASYExport.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ZTHisInpatient\住院查询\08国家版病案首页导出\BASYExport.Designer.cs">
      <DependentUpon>BASYExport.cs</DependentUpon>
    </Compile>
    <Compile Include="ZTHisInpatient\住院查询\08国家版病案首页导出\TWNewTable.cs" />
    <Compile Include="ZTHisInpatient\入院登记\Ry_Dj.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ZTHisInpatient\入院登记\Ry_Dj.Designer.cs">
      <DependentUpon>Ry_Dj.cs</DependentUpon>
    </Compile>
    <Compile Include="ZTHisInpatient\出院办理\CyBl.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ZTHisInpatient\出院办理\CyBl.Designer.cs">
      <DependentUpon>CyBl.cs</DependentUpon>
    </Compile>
    <Compile Include="ZTHisInpatient\出院办理\CySearch.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ZTHisInpatient\出院办理\CySearch.Designer.cs">
      <DependentUpon>CySearch.cs</DependentUpon>
    </Compile>
    <Compile Include="ZTHisInpatient\医嘱录入\Bl_Cf3.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ZTHisInpatient\医嘱录入\Bl_Cf3.Designer.cs">
      <DependentUpon>Bl_Cf3.cs</DependentUpon>
    </Compile>
    <Compile Include="ZTHisInpatient\医嘱录入\Bl_CfXm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ZTHisInpatient\医嘱录入\Bl_CfXm.Designer.cs">
      <DependentUpon>Bl_CfXm.cs</DependentUpon>
    </Compile>
    <Compile Include="ZTHisInpatient\医嘱录入\Bl_CfYp.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ZTHisInpatient\医嘱录入\Bl_CfYp.Designer.cs">
      <DependentUpon>Bl_CfYp.cs</DependentUpon>
    </Compile>
    <Compile Include="ZTHisInpatient\医嘱录入\Bl_CfYpTf.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ZTHisInpatient\医嘱录入\Bl_CfYpTf.Designer.cs">
      <DependentUpon>Bl_CfYpTf.cs</DependentUpon>
    </Compile>
    <Compile Include="ZTHisInpatient\病例录入\Bl_AutoXm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ZTHisInpatient\病例录入\Bl_AutoXm.Designer.cs">
      <DependentUpon>Bl_AutoXm.cs</DependentUpon>
    </Compile>
    <Compile Include="ZTHisInpatient\医嘱录入\Bl_Cf6.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ZTHisInpatient\医嘱录入\Bl_Cf6.Designer.cs">
      <DependentUpon>Bl_Cf6.cs</DependentUpon>
    </Compile>
    <Compile Include="ZTHisInpatient\病例录入\Zd_Bl11.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ZTHisInpatient\病例录入\Zd_Bl11.Designer.cs">
      <DependentUpon>Zd_Bl11.cs</DependentUpon>
    </Compile>
    <Compile Include="ZTHisInpatient\病例录入\Zd_Bl12.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ZTHisInpatient\病例录入\Zd_Bl12.Designer.cs">
      <DependentUpon>Zd_Bl12.cs</DependentUpon>
    </Compile>
    <Compile Include="ZTHisInpatient\病例录入\Zd_Bl13.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ZTHisInpatient\病例录入\Zd_Bl13.Designer.cs">
      <DependentUpon>Zd_Bl13.cs</DependentUpon>
    </Compile>
    <Compile Include="ZTHisInpatient\病案首页\Bl_FirstBlood.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ZTHisInpatient\病案首页\Bl_FirstBlood.Designer.cs">
      <DependentUpon>Bl_FirstBlood.cs</DependentUpon>
    </Compile>
    <Compile Include="ZTHisInpatient\病案首页\Bl_FirstICU.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ZTHisInpatient\病案首页\Bl_FirstICU.Designer.cs">
      <DependentUpon>Bl_FirstICU.cs</DependentUpon>
    </Compile>
    <Compile Include="ZTHisInpatient\病案首页\Bl_Firstoper.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ZTHisInpatient\病案首页\Bl_Firstoper.Designer.cs">
      <DependentUpon>Bl_Firstoper.cs</DependentUpon>
    </Compile>
    <Compile Include="ZTHisInpatient\病案首页\Bl_FirstOutDiag.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ZTHisInpatient\病案首页\Bl_FirstOutDiag.Designer.cs">
      <DependentUpon>Bl_FirstOutDiag.cs</DependentUpon>
    </Compile>
    <Compile Include="ZTHisInpatient\病案首页\XyBl_Basy.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ZTHisInpatient\病案首页\XyBl_Basy.Designer.cs">
      <DependentUpon>XyBl_Basy.cs</DependentUpon>
    </Compile>
    <Compile Include="ZTHisInpatient\病案首页\ZyBl_Basy.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ZTHisInpatient\病案首页\ZyBl_Basy.Designer.cs">
      <DependentUpon>ZyBl_Basy.cs</DependentUpon>
    </Compile>
    <Compile Include="ZTHisInpatient\长期医嘱\Cq_CfXm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ZTHisInpatient\长期医嘱\Cq_CfXm.Designer.cs">
      <DependentUpon>Cq_CfXm.cs</DependentUpon>
    </Compile>
    <Compile Include="ZTHisInpatient\长期医嘱\Cq_CfYp.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ZTHisInpatient\长期医嘱\Cq_CfYp.Designer.cs">
      <DependentUpon>Cq_CfYp.cs</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Properties\licenses.licx" />
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="ZTHisInpatient\住院日结\Zy_Jz.resx">
      <DependentUpon>Zy_Jz.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ZTHisInpatient\住院日结\Zy_Jz_Zf.resx">
      <DependentUpon>Zy_Jz_Zf.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ZTHisInpatient\住院查询\01在院患者费用\Cx_ZyCfMx.resx">
      <DependentUpon>Cx_ZyCfMx.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ZTHisInpatient\出院召回\BlEdit.resx">
      <DependentUpon>BlEdit.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ZTHisInpatient\住院查询\01在院患者费用\Cx_ZyYjMx.resx">
      <DependentUpon>Cx_ZyYjMx.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ZTHisInpatient\住院查询\04住院未发药查询\Cx_WZyHz1.resx">
      <DependentUpon>Cx_WZyHz1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ZTHisInpatient\住院查询\06缴纳押金查询\Cx_Yj.resx">
      <DependentUpon>Cx_Yj.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ZTHisInpatient\住院查询\07患者诊疗项目查询\ZyXmCx.resx">
      <DependentUpon>ZyXmCx.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ZTHisInpatient\住院查询\08国家版病案首页导出\BASYExport.resx">
      <DependentUpon>BASYExport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ZTHisInpatient\入院登记\Ry_Dj.resx">
      <DependentUpon>Ry_Dj.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ZTHisInpatient\出院办理\CyBl.resx">
      <DependentUpon>CyBl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ZTHisInpatient\出院办理\CySearch.resx">
      <DependentUpon>CySearch.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ZTHisInpatient\医嘱录入\Bl_Cf3.resx">
      <DependentUpon>Bl_Cf3.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ZTHisInpatient\医嘱录入\Bl_CfXm.resx">
      <DependentUpon>Bl_CfXm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ZTHisInpatient\医嘱录入\Bl_CfYp.resx">
      <DependentUpon>Bl_CfYp.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ZTHisInpatient\医嘱录入\Bl_CfYpTf.resx">
      <DependentUpon>Bl_CfYpTf.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ZTHisInpatient\病例录入\Bl_AutoXm.resx">
      <DependentUpon>Bl_AutoXm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ZTHisInpatient\医嘱录入\Bl_Cf6.resx">
      <DependentUpon>Bl_Cf6.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ZTHisInpatient\病例录入\Zd_Bl11.resx">
      <DependentUpon>Zd_Bl11.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ZTHisInpatient\病例录入\Zd_Bl12.resx">
      <DependentUpon>Zd_Bl12.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ZTHisInpatient\病例录入\Zd_Bl13.resx">
      <DependentUpon>Zd_Bl13.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ZTHisInpatient\病案首页\Bl_FirstBlood.resx">
      <DependentUpon>Bl_FirstBlood.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ZTHisInpatient\病案首页\Bl_FirstICU.resx">
      <DependentUpon>Bl_FirstICU.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ZTHisInpatient\病案首页\Bl_Firstoper.resx">
      <DependentUpon>Bl_Firstoper.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ZTHisInpatient\病案首页\Bl_FirstOutDiag.resx">
      <DependentUpon>Bl_FirstOutDiag.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ZTHisInpatient\病案首页\XyBl_Basy.resx">
      <DependentUpon>XyBl_Basy.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ZTHisInpatient\病案首页\ZyBl_Basy.resx">
      <DependentUpon>ZyBl_Basy.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ZTHisInpatient\长期医嘱\Cq_CfXm.resx">
      <DependentUpon>Cq_CfXm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ZTHisInpatient\长期医嘱\Cq_CfYp.resx">
      <DependentUpon>Cq_CfYp.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\BLL\BLL.csproj">
      <Project>{46b795c2-6efa-41e6-948e-66f92e591b6a}</Project>
      <Name>BLL</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common.BaseForm\Common.BaseForm.csproj">
      <Project>{1dd7020c-8603-438a-8015-34702dabc229}</Project>
      <Name>Common.BaseForm</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common.Delegate\Common.Delegate.csproj">
      <Project>{943ED6DC-C1FB-42FA-B543-9AFAA67BA7C3}</Project>
      <Name>Common.Delegate</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common.Enum\Common.Enum.csproj">
      <Project>{eca72bf5-a6c2-4ecb-a80a-9723ef1098a1}</Project>
      <Name>Common.Enum</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common.WinFormVar\Common.WinFormVar.csproj">
      <Project>{e267bdd2-634a-405b-bdbf-55354adbc027}</Project>
      <Name>Common.WinFormVar</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common\Common.csproj">
      <Project>{92e350a0-3691-4b8d-a07e-ebb0f10e6997}</Project>
      <Name>Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\CustomControl\CustomControl.csproj">
      <Project>{12BF4168-D60E-4A6C-85BF-926130EE6A6D}</Project>
      <Name>CustomControl</Name>
    </ProjectReference>
    <ProjectReference Include="..\CustomSunnyUI\CustomSunnyUI.csproj">
      <Project>{e12e3d1c-d546-447d-9cd7-1ac63e8d7f18}</Project>
      <Name>CustomSunnyUI</Name>
    </ProjectReference>
    <ProjectReference Include="..\DTO\DTO.csproj">
      <Project>{cca47e9e-8991-4e5e-b341-1e313499e38e}</Project>
      <Name>DTO</Name>
    </ProjectReference>
    <ProjectReference Include="..\FastDBF\SocialExplorer.FastDBF.csproj">
      <Project>{9cf5ed11-6d2b-4fef-8a0a-b2e12de59867}</Project>
      <Name>SocialExplorer.FastDBF</Name>
    </ProjectReference>
    <ProjectReference Include="..\HisControl\HisControl.vbproj">
      <Project>{EF778791-6E30-4A07-83B9-2D73FD08810A}</Project>
      <Name>HisControl</Name>
    </ProjectReference>
    <ProjectReference Include="..\HisVar\HisVar.vbproj">
      <Project>{4dad5e14-6224-46b2-886d-6d22ce3886bc}</Project>
      <Name>HisVar</Name>
    </ProjectReference>
    <ProjectReference Include="..\Model\Model.csproj">
      <Project>{3fb6ea13-2c32-4d08-a426-c22224f72121}</Project>
      <Name>Model</Name>
    </ProjectReference>
    <ProjectReference Include="..\Utilities\Utilities.csproj">
      <Project>{ddef90d7-bcf0-4e30-9fce-3bb2d493565d}</Project>
      <Name>Utilities</Name>
    </ProjectReference>
    <ProjectReference Include="..\YBBLL\YBBLL.csproj">
      <Project>{41e5b5ba-abbd-445c-90b1-ce381afbc5ce}</Project>
      <Name>YBBLL</Name>
    </ProjectReference>
    <ProjectReference Include="..\YBModel\YBModel.csproj">
      <Project>{3cb4dc25-92a0-49f4-a946-d9988b409c7c}</Project>
      <Name>YBModel</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisControl\ZTHisControl.csproj">
      <Project>{3CC56B11-C172-4753-89C8-EEA972402626}</Project>
      <Name>ZTHisControl</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisEnum\ZTHisEnum.csproj">
      <Project>{940cdbcc-e9a4-4771-be47-343404a40123}</Project>
      <Name>ZTHisEnum</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisInsurance_Enum\ZTHisInsurance_Enum.csproj">
      <Project>{731ec1b9-0bdd-413b-9ea5-441ec8b96031}</Project>
      <Name>ZTHisInsurance_Enum</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisLis\ZTHisLis.csproj">
      <Project>{e800477a-4ffe-4308-a238-44b853fb32fb}</Project>
      <Name>ZTHisLis</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisPara\ZTHisPara.csproj">
      <Project>{9ca37597-119c-4f39-8063-effc91c2b20d}</Project>
      <Name>ZTHisPara</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisPublicForm\ZTHisPublicForm.csproj">
      <Project>{cdf3f32b-e89e-4761-ac07-991bcc2dd00b}</Project>
      <Name>ZTHisPublicForm</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisPublicFunction\ZTHisPublicFunction.csproj">
      <Project>{484f5b0c-f19f-448d-b819-e183bfc2fd96}</Project>
      <Name>ZTHisPublicFunction</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisResources\ZTHisResources.csproj">
      <Project>{e7e57f38-534a-4aea-841e-9a869e3738ff}</Project>
      <Name>ZTHisResources</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisVar\ZTHisVar.csproj">
      <Project>{26bf0cc0-ae2a-459a-b41b-73f691f5299d}</Project>
      <Name>ZTHisVar</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
    <None Include="Resources\导出DBF.png" />
    <EmbeddedResource Include="ZTHisInpatient\Rpt\西医病案首页.mrt" />
    <EmbeddedResource Include="ZTHisInpatient\Rpt\入院通知单.mrt" />
    <EmbeddedResource Include="ZTHisInpatient\Rpt\住院腕带.mrt" />
    <EmbeddedResource Include="ZTHisInpatient\Rpt\中医病案首页.mrt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\反选.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\全选.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\导出CSV.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\刷新.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\导出Excel.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\导出.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\通过.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\退费.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\打印.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\增加.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\删除.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\还原.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\召回.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\修改.png" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>