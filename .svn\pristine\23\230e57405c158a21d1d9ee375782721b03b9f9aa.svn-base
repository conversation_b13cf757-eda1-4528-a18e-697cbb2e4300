﻿
<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class Zd_ZhiKong3
    Inherits HisControl.BaseChild

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(Zd_ZhiKong3))
        Me.C1Holder1 = New C1.Win.C1Command.C1CommandHolder()
        Me.Move1 = New C1.Win.C1Command.C1Command()
        Me.Move2 = New C1.Win.C1Command.C1Command()
        Me.Move3 = New C1.Win.C1Command.C1Command()
        Me.Move4 = New C1.Win.C1Command.C1Command()
        Me.Move5 = New C1.Win.C1Command.C1Command()
        Me.Control1 = New C1.Win.C1Command.C1CommandControl()
        Me.T_Label1 = New C1.Win.C1Input.C1Label()
        Me.Control2 = New C1.Win.C1Command.C1CommandControl()
        Me.T_Label2 = New C1.Win.C1Input.C1Label()
        Me.Control3 = New C1.Win.C1Command.C1CommandControl()
        Me.T_Label3 = New C1.Win.C1Input.C1Label()
        Me.Control4 = New C1.Win.C1Command.C1Command()
        Me.ToolBar1 = New C1.Win.C1Command.C1ToolBar()
        Me.Link6 = New C1.Win.C1Command.C1CommandLink()
        Me.Link1 = New C1.Win.C1Command.C1CommandLink()
        Me.Link2 = New C1.Win.C1Command.C1CommandLink()
        Me.Link7 = New C1.Win.C1Command.C1CommandLink()
        Me.Link3 = New C1.Win.C1Command.C1CommandLink()
        Me.Link4 = New C1.Win.C1Command.C1CommandLink()
        Me.Link5 = New C1.Win.C1Command.C1CommandLink()
        Me.Link8 = New C1.Win.C1Command.C1CommandLink()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.Comm2 = New CustomControl.MyButton()
        Me.Comm1 = New CustomControl.MyButton()
        Me.T_Line3 = New System.Windows.Forms.Label()
        Me.ToolTip1 = New System.Windows.Forms.ToolTip()
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel()
        Me.Code_TextBox = New CustomControl.MyTextBox()
        Me.Name_TextBox = New CustomControl.MyTextBox()
        Me.Memo_TextBox = New CustomControl.MyTextBox()
        Me.KfStyleMySingleComobo1 = New CustomControl.MySingleComobo()
        Me.SplitContainer1 = New System.Windows.Forms.SplitContainer()
        Me.GradeMyDtComobo1 = New CustomControl.MyDtComobo()
        Me.NrMyTextBox1 = New CustomControl.MyTextBox()
        Me.KfNumericEdit1 = New CustomControl.MyNumericEdit()
        Me.T_Line1 = New System.Windows.Forms.Label()
        CType(Me.C1Holder1,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.T_Label1,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.T_Label2,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.T_Label3,System.ComponentModel.ISupportInitialize).BeginInit
        Me.ToolBar1.SuspendLayout
        Me.Panel1.SuspendLayout
        Me.TableLayoutPanel1.SuspendLayout
        CType(Me.SplitContainer1,System.ComponentModel.ISupportInitialize).BeginInit
        Me.SplitContainer1.Panel1.SuspendLayout
        Me.SplitContainer1.Panel2.SuspendLayout
        Me.SplitContainer1.SuspendLayout
        Me.SuspendLayout
        '
        'C1Holder1
        '
        Me.C1Holder1.Commands.Add(Me.Move1)
        Me.C1Holder1.Commands.Add(Me.Move2)
        Me.C1Holder1.Commands.Add(Me.Move3)
        Me.C1Holder1.Commands.Add(Me.Move4)
        Me.C1Holder1.Commands.Add(Me.Move5)
        Me.C1Holder1.Commands.Add(Me.Control1)
        Me.C1Holder1.Commands.Add(Me.Control2)
        Me.C1Holder1.Commands.Add(Me.Control3)
        Me.C1Holder1.Commands.Add(Me.Control4)
        Me.C1Holder1.Owner = Me
        Me.C1Holder1.VisualStyle = C1.Win.C1Command.VisualStyle.Office2010Blue
        '
        'Move1
        '
        Me.Move1.Image = CType(resources.GetObject("Move1.Image"),System.Drawing.Image)
        Me.Move1.Name = "Move1"
        Me.Move1.Shortcut = System.Windows.Forms.Shortcut.F2
        Me.Move1.ShortcutText = ""
        Me.Move1.Text = "最前"
        Me.Move1.ToolTipText = "移到最前记录"
        '
        'Move2
        '
        Me.Move2.Image = CType(resources.GetObject("Move2.Image"),System.Drawing.Image)
        Me.Move2.Name = "Move2"
        Me.Move2.Shortcut = System.Windows.Forms.Shortcut.F3
        Me.Move2.ShortcutText = ""
        Me.Move2.Text = "上移"
        Me.Move2.ToolTipText = "上移记录"
        '
        'Move3
        '
        Me.Move3.Image = CType(resources.GetObject("Move3.Image"),System.Drawing.Image)
        Me.Move3.Name = "Move3"
        Me.Move3.Shortcut = System.Windows.Forms.Shortcut.F4
        Me.Move3.ShortcutText = ""
        Me.Move3.Text = "下移"
        Me.Move3.ToolTipText = "下移记录"
        '
        'Move4
        '
        Me.Move4.Image = CType(resources.GetObject("Move4.Image"),System.Drawing.Image)
        Me.Move4.Name = "Move4"
        Me.Move4.Shortcut = System.Windows.Forms.Shortcut.F5
        Me.Move4.ShortcutText = ""
        Me.Move4.Text = "最后"
        Me.Move4.ToolTipText = "移至最后记录"
        '
        'Move5
        '
        Me.Move5.Image = CType(resources.GetObject("Move5.Image"),System.Drawing.Image)
        Me.Move5.Name = "Move5"
        Me.Move5.Shortcut = System.Windows.Forms.Shortcut.F6
        Me.Move5.ShortcutText = ""
        Me.Move5.Text = "新增"
        '
        'Control1
        '
        Me.Control1.Control = Me.T_Label1
        Me.Control1.HotFrame = C1.Win.C1Command.HotFrameEnum.None
        Me.Control1.Name = "Control1"
        Me.Control1.ShortcutText = ""
        Me.Control1.ShowShortcut = false
        Me.Control1.ShowTextAsToolTip = false
        '
        'T_Label1
        '
        Me.T_Label1.AutoSize = true
        Me.T_Label1.BackColor = System.Drawing.Color.Transparent
        Me.T_Label1.BorderColor = System.Drawing.Color.Empty
        Me.T_Label1.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.T_Label1.ForeColor = System.Drawing.SystemColors.ControlText
        Me.T_Label1.Location = New System.Drawing.Point(1, 5)
        Me.T_Label1.Name = "T_Label1"
        Me.T_Label1.Size = New System.Drawing.Size(29, 12)
        Me.T_Label1.TabIndex = 36
        Me.T_Label1.Tag = Nothing
        Me.T_Label1.Text = "记录"
        Me.T_Label1.TextAlign = System.Drawing.ContentAlignment.BottomLeft
        Me.T_Label1.TextDetached = true
        '
        'Control2
        '
        Me.Control2.Control = Me.T_Label2
        Me.Control2.HotFrame = C1.Win.C1Command.HotFrameEnum.None
        Me.Control2.Name = "Control2"
        Me.Control2.ShortcutText = ""
        Me.Control2.ShowShortcut = false
        Me.Control2.ShowTextAsToolTip = false
        '
        'T_Label2
        '
        Me.T_Label2.AutoSize = true
        Me.T_Label2.BackColor = System.Drawing.Color.Transparent
        Me.T_Label2.BorderColor = System.Drawing.Color.Empty
        Me.T_Label2.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.T_Label2.ForeColor = System.Drawing.SystemColors.ControlText
        Me.T_Label2.Location = New System.Drawing.Point(80, 5)
        Me.T_Label2.Name = "T_Label2"
        Me.T_Label2.Size = New System.Drawing.Size(23, 12)
        Me.T_Label2.TabIndex = 37
        Me.T_Label2.Tag = Nothing
        Me.T_Label2.Text = "(1)"
        Me.T_Label2.TextAlign = System.Drawing.ContentAlignment.BottomCenter
        Me.T_Label2.TextDetached = true
        Me.T_Label2.TrimStart = true
        '
        'Control3
        '
        Me.Control3.Control = Me.T_Label3
        Me.Control3.HotFrame = C1.Win.C1Command.HotFrameEnum.None
        Me.Control3.Name = "Control3"
        Me.Control3.ShortcutText = ""
        Me.Control3.ShowShortcut = false
        Me.Control3.ShowTextAsToolTip = false
        '
        'T_Label3
        '
        Me.T_Label3.BackColor = System.Drawing.Color.Transparent
        Me.T_Label3.BorderColor = System.Drawing.Color.Empty
        Me.T_Label3.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.T_Label3.ForeColor = System.Drawing.SystemColors.ControlText
        Me.T_Label3.Location = New System.Drawing.Point(212, 3)
        Me.T_Label3.Name = "T_Label3"
        Me.T_Label3.Size = New System.Drawing.Size(35, 15)
        Me.T_Label3.TabIndex = 35
        Me.T_Label3.Tag = Nothing
        Me.T_Label3.Text = "Σ=1 "
        Me.T_Label3.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.T_Label3.TextDetached = true
        '
        'Control4
        '
        Me.Control4.Name = "Control4"
        Me.Control4.ShortcutText = ""
        Me.Control4.Text = "New Command"
        '
        'ToolBar1
        '
        Me.ToolBar1.AccessibleName = "Tool Bar"
        Me.ToolBar1.BackColor = System.Drawing.Color.Transparent
        Me.ToolBar1.CommandHolder = Me.C1Holder1
        Me.ToolBar1.CommandLinks.AddRange(New C1.Win.C1Command.C1CommandLink() {Me.Link6, Me.Link1, Me.Link2, Me.Link7, Me.Link3, Me.Link4, Me.Link5, Me.Link8})
        Me.ToolBar1.Controls.Add(Me.T_Label1)
        Me.ToolBar1.Controls.Add(Me.T_Label3)
        Me.ToolBar1.Controls.Add(Me.T_Label2)
        Me.ToolBar1.Location = New System.Drawing.Point(1, 3)
        Me.ToolBar1.MinButtonSize = 22
        Me.ToolBar1.Movable = false
        Me.ToolBar1.Name = "ToolBar1"
        Me.ToolBar1.Size = New System.Drawing.Size(248, 22)
        Me.ToolBar1.Text = "C1ToolBar2"
        Me.ToolBar1.VisualStyle = C1.Win.C1Command.VisualStyle.Custom
        Me.ToolBar1.VisualStyleBase = C1.Win.C1Command.VisualStyle.Classic
        '
        'Link6
        '
        Me.Link6.Command = Me.Control1
        '
        'Link1
        '
        Me.Link1.Command = Me.Move1
        Me.Link1.Delimiter = true
        Me.Link1.SortOrder = 1
        '
        'Link2
        '
        Me.Link2.Command = Me.Move2
        Me.Link2.SortOrder = 2
        '
        'Link7
        '
        Me.Link7.Command = Me.Control2
        Me.Link7.SortOrder = 3
        '
        'Link3
        '
        Me.Link3.Command = Me.Move3
        Me.Link3.SortOrder = 4
        '
        'Link4
        '
        Me.Link4.Command = Me.Move4
        Me.Link4.SortOrder = 5
        '
        'Link5
        '
        Me.Link5.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image),C1.Win.C1Command.ButtonLookFlags)
        Me.Link5.Command = Me.Move5
        Me.Link5.Delimiter = true
        Me.Link5.SortOrder = 6
        Me.Link5.Text = "新增"
        Me.Link5.ToolTipText = "新增记录"
        '
        'Link8
        '
        Me.Link8.Command = Me.Control3
        Me.Link8.Delimiter = true
        Me.Link8.SortOrder = 7
        Me.Link8.Text = "New Command"
        '
        'Panel1
        '
        Me.Panel1.Controls.Add(Me.Comm2)
        Me.Panel1.Controls.Add(Me.Comm1)
        Me.Panel1.Controls.Add(Me.T_Line3)
        Me.Panel1.Controls.Add(Me.ToolBar1)
        Me.Panel1.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.Panel1.Location = New System.Drawing.Point(0, 196)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(484, 27)
        Me.Panel1.TabIndex = 1
        '
        'Comm2
        '
        Me.Comm2.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.Comm2.DialogResult = System.Windows.Forms.DialogResult.None
        Me.Comm2.Font = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.Comm2.Location = New System.Drawing.Point(360, 3)
        Me.Comm2.Name = "Comm2"
        Me.Comm2.Size = New System.Drawing.Size(60, 25)
        Me.Comm2.TabIndex = 1
        Me.Comm2.Tag = "取消"
        Me.Comm2.Text = "取消"
        '
        'Comm1
        '
        Me.Comm1.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.Comm1.DialogResult = System.Windows.Forms.DialogResult.None
        Me.Comm1.Font = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.Comm1.Location = New System.Drawing.Point(294, 3)
        Me.Comm1.Name = "Comm1"
        Me.Comm1.Size = New System.Drawing.Size(60, 25)
        Me.Comm1.TabIndex = 0
        Me.Comm1.Tag = "保存"
        Me.Comm1.Text = "保存"
        '
        'T_Line3
        '
        Me.T_Line3.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.T_Line3.Dock = System.Windows.Forms.DockStyle.Top
        Me.T_Line3.Location = New System.Drawing.Point(0, 0)
        Me.T_Line3.Name = "T_Line3"
        Me.T_Line3.Size = New System.Drawing.Size(484, 2)
        Me.T_Line3.TabIndex = 101
        Me.T_Line3.Text = "Label2"
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.ColumnCount = 2
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 249!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 256!))
        Me.TableLayoutPanel1.Controls.Add(Me.Code_TextBox, 0, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.Name_TextBox, 0, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.Memo_TextBox, 0, 4)
        Me.TableLayoutPanel1.Controls.Add(Me.KfStyleMySingleComobo1, 1, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.SplitContainer1, 0, 3)
        Me.TableLayoutPanel1.Controls.Add(Me.KfNumericEdit1, 1, 3)
        Me.TableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(0, 0)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.RowCount = 5
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 4!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 27!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 27!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 27!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 20!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 20!))
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(484, 196)
        Me.TableLayoutPanel1.TabIndex = 0
        '
        'Code_TextBox
        '
        Me.Code_TextBox.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.Code_TextBox.Captain = "质控编码"
        Me.Code_TextBox.CaptainBackColor = System.Drawing.Color.Transparent
        Me.Code_TextBox.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.Code_TextBox.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.Code_TextBox.CaptainWidth = 60!
        Me.Code_TextBox.ContentForeColor = System.Drawing.Color.Black
        Me.Code_TextBox.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer))
        Me.Code_TextBox.EditMask = Nothing
        Me.Code_TextBox.Location = New System.Drawing.Point(3, 7)
        Me.Code_TextBox.Multiline = false
        Me.Code_TextBox.Name = "Code_TextBox"
        Me.Code_TextBox.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.Code_TextBox.ReadOnly = false
        Me.Code_TextBox.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.Code_TextBox.SelectionStart = 0
        Me.Code_TextBox.SelectStart = 0
        Me.Code_TextBox.Size = New System.Drawing.Size(243, 20)
        Me.Code_TextBox.TabIndex = 0
        Me.Code_TextBox.TextAlign = System.Windows.Forms.HorizontalAlignment.Left
        Me.Code_TextBox.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.Code_TextBox.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.Code_TextBox.Watermark = Nothing
        '
        'Name_TextBox
        '
        Me.Name_TextBox.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.Name_TextBox.Captain = "质控内容"
        Me.Name_TextBox.CaptainBackColor = System.Drawing.Color.Transparent
        Me.Name_TextBox.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.Name_TextBox.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.Name_TextBox.CaptainWidth = 60!
        Me.Name_TextBox.ContentForeColor = System.Drawing.Color.Black
        Me.Name_TextBox.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer))
        Me.Name_TextBox.EditMask = Nothing
        Me.Name_TextBox.Location = New System.Drawing.Point(3, 34)
        Me.Name_TextBox.Multiline = false
        Me.Name_TextBox.Name = "Name_TextBox"
        Me.Name_TextBox.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.Name_TextBox.ReadOnly = false
        Me.Name_TextBox.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.Name_TextBox.SelectionStart = 0
        Me.Name_TextBox.SelectStart = 0
        Me.Name_TextBox.Size = New System.Drawing.Size(243, 20)
        Me.Name_TextBox.TabIndex = 1
        Me.Name_TextBox.TextAlign = System.Windows.Forms.HorizontalAlignment.Left
        Me.Name_TextBox.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.Name_TextBox.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.Name_TextBox.Watermark = Nothing
        '
        'Memo_TextBox
        '
        Me.Memo_TextBox.Captain = "备注"
        Me.Memo_TextBox.CaptainBackColor = System.Drawing.Color.Transparent
        Me.Memo_TextBox.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.Memo_TextBox.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.Memo_TextBox.CaptainWidth = 60!
        Me.TableLayoutPanel1.SetColumnSpan(Me.Memo_TextBox, 2)
        Me.Memo_TextBox.ContentForeColor = System.Drawing.Color.Black
        Me.Memo_TextBox.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer))
        Me.Memo_TextBox.Dock = System.Windows.Forms.DockStyle.Left
        Me.Memo_TextBox.EditMask = Nothing
        Me.Memo_TextBox.Location = New System.Drawing.Point(3, 88)
        Me.Memo_TextBox.Multiline = true
        Me.Memo_TextBox.Name = "Memo_TextBox"
        Me.Memo_TextBox.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.Memo_TextBox.ReadOnly = false
        Me.Memo_TextBox.ScrollBars = System.Windows.Forms.ScrollBars.Vertical
        Me.Memo_TextBox.SelectionStart = 0
        Me.Memo_TextBox.SelectStart = 0
        Me.Memo_TextBox.Size = New System.Drawing.Size(476, 105)
        Me.Memo_TextBox.TabIndex = 5
        Me.Memo_TextBox.TextAlign = System.Windows.Forms.HorizontalAlignment.Left
        Me.Memo_TextBox.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.Memo_TextBox.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Top
        Me.Memo_TextBox.Watermark = Nothing
        '
        'KfStyleMySingleComobo1
        '
        Me.KfStyleMySingleComobo1.Captain = "评分方式"
        Me.KfStyleMySingleComobo1.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.KfStyleMySingleComobo1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.KfStyleMySingleComobo1.CaptainWidth = 59!
        Me.KfStyleMySingleComobo1.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        Me.KfStyleMySingleComobo1.ItemHeight = 16
        Me.KfStyleMySingleComobo1.ItemTextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.KfStyleMySingleComobo1.Location = New System.Drawing.Point(252, 34)
        Me.KfStyleMySingleComobo1.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.KfStyleMySingleComobo1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.KfStyleMySingleComobo1.Name = "KfStyleMySingleComobo1"
        Me.KfStyleMySingleComobo1.ReadOnly = false
        Me.KfStyleMySingleComobo1.Size = New System.Drawing.Size(227, 20)
        Me.KfStyleMySingleComobo1.TabIndex = 2
        Me.KfStyleMySingleComobo1.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        '
        'SplitContainer1
        '
        Me.SplitContainer1.Location = New System.Drawing.Point(3, 61)
        Me.SplitContainer1.Name = "SplitContainer1"
        '
        'SplitContainer1.Panel1
        '
        Me.SplitContainer1.Panel1.Controls.Add(Me.GradeMyDtComobo1)
        '
        'SplitContainer1.Panel2
        '
        Me.SplitContainer1.Panel2.Controls.Add(Me.NrMyTextBox1)
        Me.SplitContainer1.Size = New System.Drawing.Size(243, 21)
        Me.SplitContainer1.SplitterDistance = 81
        Me.SplitContainer1.TabIndex = 3
        '
        'GradeMyDtComobo1
        '
        Me.GradeMyDtComobo1.Bookmark = -1
        Me.GradeMyDtComobo1.Captain = "扣分等级"
        Me.GradeMyDtComobo1.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.GradeMyDtComobo1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.GradeMyDtComobo1.CaptainWidth = 60!
        Me.GradeMyDtComobo1.ColumnCaptionHeight = 18
        Me.GradeMyDtComobo1.DataSource = Nothing
        Me.GradeMyDtComobo1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.GradeMyDtComobo1.ItemHeight = 16
        Me.GradeMyDtComobo1.ItemTextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.GradeMyDtComobo1.Location = New System.Drawing.Point(0, 0)
        Me.GradeMyDtComobo1.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.GradeMyDtComobo1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.GradeMyDtComobo1.Name = "GradeMyDtComobo1"
        Me.GradeMyDtComobo1.ReadOnly = false
        Me.GradeMyDtComobo1.Row = 0
        Me.GradeMyDtComobo1.Size = New System.Drawing.Size(81, 20)
        Me.GradeMyDtComobo1.TabIndex = 0
        Me.GradeMyDtComobo1.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        '
        'NrMyTextBox1
        '
        Me.NrMyTextBox1.Captain = "扣分项"
        Me.NrMyTextBox1.CaptainBackColor = System.Drawing.Color.Transparent
        Me.NrMyTextBox1.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.NrMyTextBox1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.NrMyTextBox1.CaptainWidth = 60!
        Me.NrMyTextBox1.ContentForeColor = System.Drawing.Color.Black
        Me.NrMyTextBox1.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer))
        Me.NrMyTextBox1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.NrMyTextBox1.EditMask = Nothing
        Me.NrMyTextBox1.Location = New System.Drawing.Point(0, 0)
        Me.NrMyTextBox1.Multiline = false
        Me.NrMyTextBox1.Name = "NrMyTextBox1"
        Me.NrMyTextBox1.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.NrMyTextBox1.ReadOnly = false
        Me.NrMyTextBox1.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.NrMyTextBox1.SelectionStart = 0
        Me.NrMyTextBox1.SelectStart = 0
        Me.NrMyTextBox1.Size = New System.Drawing.Size(158, 21)
        Me.NrMyTextBox1.TabIndex = 0
        Me.NrMyTextBox1.TextAlign = System.Windows.Forms.HorizontalAlignment.Left
        Me.NrMyTextBox1.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.NrMyTextBox1.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.NrMyTextBox1.Watermark = Nothing
        '
        'KfNumericEdit1
        '
        Me.KfNumericEdit1.Captain = "扣除分数"
        Me.KfNumericEdit1.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.KfNumericEdit1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.KfNumericEdit1.CaptainWidth = 60!
        Me.KfNumericEdit1.Location = New System.Drawing.Point(252, 61)
        Me.KfNumericEdit1.MaximumSize = New System.Drawing.Size(100000, 80)
        Me.KfNumericEdit1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.KfNumericEdit1.Name = "KfNumericEdit1"
        Me.KfNumericEdit1.NumericInputKeys = CType(((((C1.Win.C1Input.NumericInputKeyFlags.F9 Or C1.Win.C1Input.NumericInputKeyFlags.Minus)  _
            Or C1.Win.C1Input.NumericInputKeyFlags.Plus)  _
            Or C1.Win.C1Input.NumericInputKeyFlags.[Decimal])  _
            Or C1.Win.C1Input.NumericInputKeyFlags.X),C1.Win.C1Input.NumericInputKeyFlags)
        Me.KfNumericEdit1.NumFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.KfNumericEdit1.ReadOnly = false
        Me.KfNumericEdit1.Size = New System.Drawing.Size(227, 20)
        Me.KfNumericEdit1.TabIndex = 4
        Me.KfNumericEdit1.ValueIsDbNull = false
        '
        'T_Line1
        '
        Me.T_Line1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.T_Line1.BackColor = System.Drawing.SystemColors.Control
        Me.T_Line1.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.T_Line1.Location = New System.Drawing.Point(3, 33)
        Me.T_Line1.Name = "T_Line1"
        Me.T_Line1.Size = New System.Drawing.Size(499, 2)
        Me.T_Line1.TabIndex = 131
        Me.T_Line1.Text = "Label1"
        '
        'Zd_ZhiKong3
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6!, 12!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(484, 223)
        Me.Controls.Add(Me.TableLayoutPanel1)
        Me.Controls.Add(Me.Panel1)
        Me.MinimizeBox = false
        Me.Name = "Zd_ZhiKong3"
        Me.Text = "质控标准"
        Me.TopMost = true
        CType(Me.C1Holder1,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.T_Label1,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.T_Label2,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.T_Label3,System.ComponentModel.ISupportInitialize).EndInit
        Me.ToolBar1.ResumeLayout(false)
        Me.ToolBar1.PerformLayout
        Me.Panel1.ResumeLayout(false)
        Me.TableLayoutPanel1.ResumeLayout(false)
        Me.SplitContainer1.Panel1.ResumeLayout(false)
        Me.SplitContainer1.Panel2.ResumeLayout(false)
        CType(Me.SplitContainer1,System.ComponentModel.ISupportInitialize).EndInit
        Me.SplitContainer1.ResumeLayout(false)
        Me.ResumeLayout(false)

End Sub
    Friend WithEvents C1Holder1 As C1.Win.C1Command.C1CommandHolder
    Friend WithEvents Move1 As C1.Win.C1Command.C1Command
    Friend WithEvents Move2 As C1.Win.C1Command.C1Command
    Friend WithEvents Move3 As C1.Win.C1Command.C1Command
    Friend WithEvents Move4 As C1.Win.C1Command.C1Command
    Friend WithEvents Move5 As C1.Win.C1Command.C1Command
    Friend WithEvents Control1 As C1.Win.C1Command.C1CommandControl
    Friend WithEvents Control2 As C1.Win.C1Command.C1CommandControl
    Friend WithEvents Control3 As C1.Win.C1Command.C1CommandControl
    Friend WithEvents T_Label3 As C1.Win.C1Input.C1Label
    Friend WithEvents T_Label2 As C1.Win.C1Input.C1Label
    Friend WithEvents T_Label1 As C1.Win.C1Input.C1Label
    Friend WithEvents ToolBar1 As C1.Win.C1Command.C1ToolBar
    Friend WithEvents Link1 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link2 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link7 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link3 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link4 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link5 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link8 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents T_Line3 As System.Windows.Forms.Label
    Friend WithEvents Link6 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents ToolTip1 As System.Windows.Forms.ToolTip
    Friend WithEvents TableLayoutPanel1 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents Control4 As C1.Win.C1Command.C1Command
    Friend WithEvents Comm1 As CustomControl.MyButton
    Friend WithEvents Comm2 As CustomControl.MyButton
    Friend WithEvents Code_TextBox As CustomControl.MyTextBox
    ' Friend WithEvents Province_Comobo As CustomControl.MyDtComobo
    'Friend WithEvents City_Comobo As CustomControl.MyDtComobo
    Friend WithEvents T_Line1 As System.Windows.Forms.Label
    'Friend WithEvents Xq_Comobo As CustomControl.MyDtComobo
    Friend WithEvents Name_TextBox As CustomControl.MyTextBox
    'Friend WithEvents Add_TextBox As CustomControl.MyTextBox
    'Friend WithEvents FzrTell_TextBox As CustomControl.MyTextBox
    ' Friend WithEvents OrgCode_TextBox As CustomControl.MyTextBox
    'Friend WithEvents isUse_CheckBox As System.Windows.Forms.CheckBox
    'Friend WithEvents SuperCode_TextBox As CustomControl.MyTextBox
    'Friend WithEvents SuperName_TextBox As CustomControl.MyTextBox
    Friend WithEvents Memo_TextBox As CustomControl.MyTextBox
    Friend WithEvents KfStyleMySingleComobo1 As CustomControl.MySingleComobo
    Friend WithEvents SplitContainer1 As System.Windows.Forms.SplitContainer
    Friend WithEvents NrMyTextBox1 As CustomControl.MyTextBox
    Friend WithEvents GradeMyDtComobo1 As CustomControl.MyDtComobo
    Friend WithEvents KfNumericEdit1 As CustomControl.MyNumericEdit
    'Friend WithEvents Level_Comobo As CustomControl.MyDtComobo
End Class
