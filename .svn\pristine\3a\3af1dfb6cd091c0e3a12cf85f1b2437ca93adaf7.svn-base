﻿Imports BaseClass

Imports Stimulsoft.Report
Imports Stimulsoft.Report.Components
Imports System.Drawing
Imports System.Windows.Forms
Imports ModelOld

Public Class Materials_Other_Out

#Region "定义__变量"
    Dim My_Table As New DataTable                       '从表
    Dim V_TotalMoney As Double
    Dim Cb_Cm As CurrencyManager                     '同步指针
    Dim flag_Error As <PERSON><PERSON>an
    Dim R<PERSON>ert As Boolean = True
    Dim Materials_Other_Out1Bll As New BLLOld.B_Materials_Other_Out1
    Dim Materials_Other_Out2Bll As New BLLOld.B_Materials_Other_Out2
    Dim Materials_Other_Out1Model As New ModelOld.M_Materials_Other_Out1
    Dim Materials_Other_Out2Model As New ModelOld.M_Materials_Other_Out2
    Dim MaterialsStockBll As New BLLOld.B_Materials_Stock
#End Region

    Public Sub New(ByVal model As M_Materials_Other_Out1)
        ' 此调用是设计器所必需的。
        InitializeComponent()
        Materials_Other_Out1Model = model
        ' 在 InitializeComponent() 调用之后添加任何初始化。
    End Sub

    Private Sub Materials_Other_Out_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Me.Load
        Call Form_Init()                '窗体初始化
        If Materials_Other_Out1Model Is Nothing Then
            Call Zb_Clear()
        Else
            Call Zb_Show()
        End If
        Call statisticsDataShow()
    End Sub

#Region "窗体__事件"

    Private Sub Form_Init()
        Panel2.Height = 35
        Comm_Del.Location = New Point(100, 1)
        Comm_DelAll.Location = New Point(Comm_Del.Left + Comm_Del.Width + 2, 1)
        Comm_New.Location = New Point(Comm_DelAll.Left + Comm_DelAll.Width + 2, 1)
        Comm_Save.Location = New Point(Comm_New.Left + Comm_New.Width + 2, 1)
        Comm_Complete.Location = New Point(Comm_Save.Left + Comm_Save.Width + 2, 1)
        Comm_Print.Location = New Point(Comm_Complete.Left + Comm_Complete.Width + 2, 1)
        Comm_Search.Location = New Point(Comm_Print.Left + Comm_Print.Width + 2, 1)
        Comm_WriteOffAll.Location = New Point(Comm_Search.Left + Comm_Search.Width + 2, 1)
        Comm_WriteOffPart.Location = New Point(Comm_WriteOffAll.Left + Comm_WriteOffAll.Width + 2, 1)
        Comm_Close.Location = New Point(Comm_WriteOffPart.Left + Comm_WriteOffPart.Width + 2, 1)
        '当前日期
        With Form_Date
            .CustomFormat = "yyyy-MM-dd HH:mm"
            .EditFormat = "yyyy-MM-dd"
            .DisplayFormat = "yyyy-MM-dd"
        End With

        '物资仓库字典
        Dim MaterialsWhBll As New BLLOld.B_Materials_Warehouse_Dict
        With WareHouse_DtCom
            .DataView = MaterialsWhBll.GetList("isuse=1").Tables(0).DefaultView
            .Init_Colum("MaterialsWh_Py", "物资仓库简称", 100, "左")
            .Init_Colum("MaterialsWh_Name", "物资仓库名称", 210, "左")
            .Init_Colum("MaterialsWh_Code", "编码", 0, "左")
            .DisplayMember = "MaterialsWh_Name"
            .ValueMember = "MaterialsWh_Code"
            .DroupDownWidth = 310
            .MaxDropDownItems = 15
            .SelectedIndex = -1
            .RowFilterTextNull = ""
            .RowFilterNotTextNull = "MaterialsWh_Py"
        End With

        '出库类别
        Dim Materials_InOutBll As New BLLOld.B_Materials_InOut_Class_Dict
        With InOutClass_DtCom
            .DataView = Materials_InOutBll.GetList("isuse=1 and InOutType='出库' ").Tables(0).DefaultView
            .Init_Colum("MaterialsInOut_Py", "类别简称", 0, "左")
            .Init_Colum("MaterialsInOut_Name", "类别名称", 100, "左")
            .Init_Colum("MaterialsInOut_Code", "编码", 0, "左")
            .DisplayMember = "MaterialsInOut_Name"
            .ValueMember = "MaterialsInOut_Code"
            .DroupDownWidth = .Width - .CaptainWidth
            .MaxDropDownItems = 15
            .SelectedIndex = 0
            .RowFilterTextNull = ""
            .RowFilterNotTextNull = "MaterialsInOut_Py"
        End With


        Code_Text.Enabled = False
        Jsr_Text.Enabled = False

    End Sub

    Private Sub BtnState()
        MyGrid_Init()
        '录入或者新单按钮状态
        If Materials_Other_Out1Model.OrdersStatus Is Nothing OrElse Materials_Other_Out1Model.OrdersStatus = "录入" Then
            Comm_Del.Enabled = True
            Comm_DelAll.Enabled = True
            Comm_New.Enabled = True
            Comm_Save.Enabled = True
            Comm_Complete.Enabled = True
            Comm_Print.Enabled = False
            Comm_Search.Enabled = True
            Comm_Close.Enabled = True
            Comm_WriteOffAll.Enabled = False
            Comm_WriteOffPart.Enabled = False

            MyGrid1.Tag = True
            If Materials_Other_Out1Model.WriteOffStatus = "冲销" Then
                WareHouse_DtCom.Enabled = False
                InOutClass_DtCom.Enabled = False
            Else
                WareHouse_DtCom.Enabled = True
                InOutClass_DtCom.Enabled = True
            End If
            Form_Date.Enabled = True
            Memo_Text.Enabled = True
            Exit Sub
        End If

        '非本人的单据不可以修改
        If Materials_Other_Out1Model.Jsr_Code <> HisVar.HisVar.JsrCode Then
            Comm_Del.Enabled = False
            Comm_DelAll.Enabled = False
            Comm_New.Enabled = True
            Comm_Save.Enabled = False
            Comm_Complete.Enabled = False
            Comm_Print.Enabled = False
            Comm_Search.Enabled = True
            Comm_Close.Enabled = True
            Comm_WriteOffAll.Enabled = False
            Comm_WriteOffPart.Enabled = False

            MyGrid1.Tag = False
            WareHouse_DtCom.Enabled = False
            InOutClass_DtCom.Enabled = False
            Form_Date.Enabled = False
            Memo_Text.Enabled = False
            Exit Sub
        End If

        '完成的只有冲销可以使用，被冲销的一键冲销不能使用
        If Materials_Other_Out1Model.OrdersStatus = "完成" Then
            Comm_Del.Enabled = False
            Comm_DelAll.Enabled = False
            Comm_New.Enabled = True
            Comm_Save.Enabled = False
            Comm_Complete.Enabled = False
            Comm_Print.Enabled = True
            Comm_Search.Enabled = True
            Comm_Close.Enabled = True

            MyGrid1.Tag = False
            WareHouse_DtCom.Enabled = False
            InOutClass_DtCom.Enabled = False
            Form_Date.Enabled = False
            Memo_Text.Enabled = False

            Select Case Materials_Other_Out1Model.WriteOffStatus & ""
                Case "冲销"      '冲销单不能被冲销
                    Comm_WriteOffAll.Enabled = False
                    Comm_WriteOffPart.Enabled = False
                Case "被冲销"    '被冲销单不能一键冲销
                    Comm_WriteOffAll.Enabled = False
                    Comm_WriteOffPart.Enabled = False
                Case "全部被冲销" '全部被冲销单不能被冲销
                    Comm_WriteOffAll.Enabled = False
                    Comm_WriteOffPart.Enabled = False
                Case ""
                    Comm_WriteOffAll.Enabled = True
                    Comm_WriteOffPart.Enabled = True
            End Select
            Exit Sub
        End If
    End Sub

    Private Sub MyGrid_Init()
        Dim ColEdit As Boolean
        If Materials_Other_Out1Model.OrdersStatus = "完成" Then
            ColEdit = False
        Else
            ColEdit = True
        End If
        With MyGrid1
            .Init_Grid()
            .Init_Column("出库编码", "M_OtherOut_Code", "0", "左", "", False) '0-
            .Init_Column("明细编码", "M_OtherOut_Detail_Code", "0", "左", "", False) '0
            .Init_Column("物资编码", "Materials_Code", "0", "左", "", False) '0
            .Init_Column("库存编码", "MaterialsStock_Code", "0", "左", "", False) '2-

            If Materials_Other_Out1Model.WriteOffStatus = "冲销" Then
                .Init_Column("物资名称", "Materials_Name", "150", "左", "", False)
            Else
                .Init_Column("物资名称", "Materials_Name", "150", "左", "", ColEdit)
            End If

            .Init_Column("物资批号", "MaterialsLot", "100", "左", "", False) '3-
            .Init_Column("物资有效期", "MaterialsExpiryDate", "110", "左", "yyyyMMdd", False) '1-

            If Materials_Other_Out1Model.WriteOffStatus = "冲销" Then
                .Init_Column("可冲数量", "Can_RealWriteOffNo", "100", "右", "####0.####", False)
                .Init_Column("数量", "M_OtherOut_Num", "80", "右", "####0.####", ColEdit)
                .Init_Column("库存数量", "MaterialsStore_Num", "100", "右", "####0.####", False)
                .Init_Column("单价", "M_OtherOut_Price", "70", "右", "####0.00##", False)
                .Init_Column("冲销金额", "M_OtherOut_Money", "100", "右", "##,##0.00##", False)
            Else
                If Materials_Other_Out1Model.OrdersStatus = "完成" Then
                    .Init_Column("数量", "M_OtherOut_Num", "80", "右", "####0.####", ColEdit)
                    .Init_Column("冲销数量", "M_OtherOut_WriteoffNo", "80", "右", "####0.####", False)
                    .Init_Column("实际出库数量", "M_OtherOut_RealNo", "100", "右", "####0.####", False)
                    .Init_Column("单价", "M_OtherOut_Price", "70", "右", "####0.00##", False)
                    .Init_Column("出库金额", "M_OtherOut_Money", "100", "右", "##,##0.00##", False)
                    .Init_Column("实际出库金额", "M_OtherOut_RealMoney", "120", "右", "##,##0.00##", False)
                Else
                    .Init_Column("当前库存", "MaterialsStore_Num", "70", "右", "####0.####", False)
                    .Init_Column("数量", "M_OtherOut_Num", "80", "右", "####0.####", ColEdit)
                    .Init_Column("单价", "M_OtherOut_Price", "70", "右", "####0.00##", False)
                    .Init_Column("出库金额", "M_OtherOut_Money", "100", "右", "##,##0.00##", False)
                End If
            End If
            .Init_Column("备注", "M_OtherOutDetail_Memo", "200", "左", "", ColEdit) '0

            .MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightCell
            If Materials_Other_Out1Model.WriteOffStatus = "冲销" Then
                .AllowAddNew = False '冲销单不允许增加新行
            Else
                .AllowAddNew = ColEdit
            End If
            .ColumnFooters = True
            MyGrid1.Splits(0).DisplayColumns("Materials_Name").Locked = True
        End With
    End Sub

    Private Sub Yk_Materials_Other_Out2_FormClosing(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosingEventArgs) Handles Me.FormClosing
        If e.CloseReason = CloseReason.UserClosing Then
            Try
                MyGrid1.UpdateData()
                If My_Table.DataSet.HasChanges = True Then
                    If Materials_Other_Out1Bll.GetRecordCount("  M_OtherOut_Code='" & Code_Text.Text.Trim & "' and OrdersStatus in ('完成')") = 0 Then
                        If MessageBox.Show("数据尚未保存,是否保存数据?", "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1) = Windows.Forms.DialogResult.OK Then
                            Call Data_Save("保存")
                        End If
                    End If
                End If
            Catch ex As Exception
                MsgBox("当前编辑行数据因未填写完整，将不能保存！", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示：")
                MyGrid1.Delete()
            End Try
        End If

    End Sub

#End Region

#Region "清空__显示"

    Private Sub Zb_Clear()
        Rinsert = True
        Materials_Other_Out1Model = New ModelOld.M_Materials_Other_Out1
        Materials_Other_Out1Model.M_OtherOut_Code = Materials_Other_Out1Bll.MaxCode(Format(Now, "yyMMdd"))

        Code_Text.Text = Materials_Other_Out1Model.M_OtherOut_Code
        Jsr_Text.Text = HisVar.HisVar.JsrName

        WareHouse_DtCom.SelectedIndex = -1
        InOutClass_DtCom.SelectedIndex = 0
        Form_Date.Value = Now
        Memo_Text.Text = ""

        V_TotalMoney = 0

        Call BtnState()
        Call P_Data_Show()

        WareHouse_DtCom.Select()
    End Sub

    Private Sub Zb_Show()   '显示记录
        Rinsert = False
        With Materials_Other_Out1Model
            Code_Text.Text = .M_OtherOut_Code
            Jsr_Text.Text = .Jsr_Name
            WareHouse_DtCom.DataSource.RowFilter = ""
            WareHouse_DtCom.SelectedValue = .MaterialsWh_Code
            InOutClass_DtCom.SelectedValue = .MaterialsInOut_Code
            Form_Date.Value = .OtherOut_Date
            Memo_Text.Text = .M_OtherOut_Memo
        End With
        Call BtnState()
        Call P_Data_Show()
    End Sub

    Private Sub P_Data_Show()   '从表数据
        If Materials_Other_Out1Model.WriteOffStatus = "冲销" Then
            My_Table = Materials_Other_Out2Bll.GetWriteOffList("Materials_Other_Out2.M_OtherOut_Code='" & Materials_Other_Out1Model.M_OtherOut_Code & "'").Tables(0)
        Else
            My_Table = Materials_Other_Out2Bll.GetList("Materials_Other_Out2.M_OtherOut_Code='" & Materials_Other_Out1Model.M_OtherOut_Code & "'").Tables(0)
        End If
        My_Table.Columns("M_OtherOut_Code").AllowDBNull = True
        My_Table.Columns("M_OtherOut_Detail_Code").AllowDBNull = True
        My_Table.Columns("M_OtherOut_Num").AllowDBNull = True
        My_Table.Columns("M_OtherOut_WriteoffNo").DefaultValue = 0
        My_Table.Constraints.Clear()
        MyGrid1.DataTable = My_Table
        Cb_Cm = CType(BindingContext(My_Table, ""), CurrencyManager)
        Call F_Sum()
        MyGrid1.Focus()
    End Sub

    Private Sub StatisticsDataShow()
        Dim str As String = "Finish_Date BETWEEN '" & Format(Now, "yyyy-MM-dd 00:00:00") & "' AND '" & Format(Now, "yyyy-MM-dd 23:59:59") & "'"
        TodayFormsNo_Lbl.Text = "今日出库数量：" & Materials_Other_Out1Bll.GetRecordCount(str)
        TodayMoney_Lbl.Text = "今日出库总金额：" & Materials_Other_Out1Bll.GetSumMoeny(str)
        str += "AND Jsr_Code='" & HisVar.HisVar.JsrCode & "'"
        BrTodayFormsNo_Lbl.Text = "本人出库数量：" & Materials_Other_Out1Bll.GetRecordCount(str)
        BrTodayMoney_Lbl.Text = "本人出库总金额：" & Materials_Other_Out1Bll.GetSumMoeny(str)
    End Sub
#End Region

#Region "控件__动作"

#Region "按钮"

    Private Sub Comm_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm_Del.Click, Comm_DelAll.Click, Comm_Save.Click,
        Comm_Complete.Click, Comm_New.Click, Comm_Print.Click, Comm_Close.Click, Comm_Search.Click, Comm_WriteOffPart.Click, Comm_WriteOffAll.Click
        Select Case sender.tag
            Case "删除行"
                If Materials_Other_Out1Bll.GetRecordCount("  M_OtherOut_Code='" & Code_Text.Text.Trim & "' and OrdersStatus ='完成'") = 0 Then
                    MsgBox("已完成单据不能删除！", MsgBoxStyle.Exclamation, "提示")
                    Exit Sub
                End If
                Data_Delete()
            Case "删除单"
                If Materials_Other_Out1Model.Input_Date Is Nothing Then
                    MsgBox("数据尚未保存,无法删除!", MsgBoxStyle.Exclamation, "提示")
                    Exit Sub
                End If
                If Materials_Other_Out1Bll.GetRecordCount("  M_OtherOut_Code='" & Code_Text.Text.Trim & "' and OrdersStatus ='完成'") = 0 Then
                    MsgBox("已完成单据不能删除！", MsgBoxStyle.Exclamation, "提示")
                    Exit Sub
                End If
                Call Data_DeleteAll()
            Case "新单"
                Data_New()
            Case "保存", "完成"
                Call Data_Save(sender.tag)
            Case "打印"
                If Materials_Other_Out1Model.Input_Date Is Nothing Then
                    MsgBox("数据尚未保存,无法打印!", MsgBoxStyle.Exclamation, "提示")
                    Exit Sub
                End If

                If Materials_Other_Out1Model.OrdersStatus = "录入" Then
                    MsgBox("未完成单据不能打印", MsgBoxStyle.Exclamation, "提示")
                    Exit Sub
                End If
                Call Data_Print()
            Case "速查"
                Dim t_Rc As New BaseClass.C_RowChange
                AddHandler t_Rc.ModelChangeEvent, AddressOf ChangeModel
                Dim f As New Other_Search("出库速查", t_Rc)
                f.Owner = Me
                f.ShowDialog()
            Case "退出"
                Me.Close()
            Case "冲销", "一键冲销"
                If Materials_Other_Out1Bll.GetRecordCount("M_OtherOut_Code='" & Code_Text.Text & "' and OrdersStatus='完成'") = 0 Then
                    MsgBox("未完成单据不能冲销", MsgBoxStyle.Exclamation, "提示")
                    Exit Sub
                End If
                If Materials_Other_Out2Bll.GetRecordCount("M_OtherOut_Code='" & Code_Text.Text & "' and M_OtherOut_RealNo>0") = 0 Then
                    MsgBox("该单据已全部被冲销，不能冲销", MsgBoxStyle.Exclamation, "提示")
                    Exit Sub
                End If


                If MsgBox("是否" + sender.Text + "仓库" & WareHouse_DtCom.Text & "，出库金额" & V_TotalMoney.ToString("#####0.00##") & "，单号" & Code_Text.Text & "的物资出库单？", MsgBoxStyle.Question + MsgBoxStyle.YesNo + MsgBoxStyle.DefaultButton1, "提示") = DialogResult.Yes Then
                    Dim Other_Out1Model As New ModelOld.M_Materials_Other_Out1
                    With Other_Out1Model
                        .M_OtherOut_Code = Materials_Other_Out1Bll.MaxCode(Format(Now, "yyMMdd"))
                        .MaterialsInOut_Code = Materials_Other_Out1Model.MaterialsInOut_Code
                        .MaterialsWh_Code = Materials_Other_Out1Model.MaterialsWh_Code
                        .OtherOut_Date = Now
                        .Input_Date = Now
                        .Jsr_Code = HisVar.HisVar.JsrCode
                        .Jsr_Name = HisVar.HisVar.JsrName
                        .TotalMoney = 0
                        .M_OtherOut_Memo = ""
                        .OrdersStatus = "录入"
                        .WriteOff_Code = Materials_Other_Out1Model.M_OtherOut_Code
                        .WriteOffStatus = "冲销"
                    End With

                    If Materials_Other_Out1Bll.AddWriteOff(Other_Out1Model) = True Then
                        If sender.Text = "一键冲销" Then
                            Other_Out1Model.OrdersStatus = "完成"
                            Other_Out1Model.Finish_Date = Now
                            If Not Materials_Other_Out1Bll.Complete(Other_Out1Model) Then
                                Other_Out1Model.OrdersStatus = "录入"
                                Other_Out1Model.Finish_Date = Nothing
                                MsgBox("完成操作失败!", MsgBoxStyle.Exclamation, "提示")
                                Exit Sub
                            End If
                        End If
                        Dim ReturnForm As New Materials.Materials_Other_Out(Other_Out1Model)
                        ReturnForm.Name = ReturnForm.Name & Other_Out1Model.M_OtherOut_Code
                        BaseFunc.BaseFunc.addTabControl(ReturnForm, "出库冲销-" & Code_Text.Text)
                    End If
                End If
        End Select

    End Sub

    Private Sub Memo_Text_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles Memo_Text.Validated
        MyGrid1.Focus()
    End Sub


#End Region

#Region "DBGrid动作"

    Private Sub MyGrid1_AfterColUpdate(ByVal sender As System.Object, ByVal e As C1.Win.C1TrueDBGrid.ColEventArgs) Handles MyGrid1.AfterColUpdate
        Dim up_Row As DataRow                    '当 前 行
        up_Row = Cb_Cm.List(MyGrid1.Row).Row
        Select Case MyGrid1.Splits(0).DisplayColumns(e.ColIndex).Name
            Case "数量"
                If up_Row("M_OtherOut_Num") IsNot DBNull.Value Then
                    up_Row("M_OtherOut_Money") = up_Row("M_OtherOut_Num") * up_Row("M_OtherOut_Price")
                    up_Row("M_OtherOut_RealNo") = up_Row("M_OtherOut_Num") - up_Row("M_OtherOut_WriteoffNo")
                    up_Row("M_OtherOut_RealMoney") = up_Row("M_OtherOut_RealNo") * up_Row("M_OtherOut_Price")
                End If
        End Select
    End Sub

    Private Sub MyGrid1_AfterUpdate(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyGrid1.AfterUpdate
        Call F_Sum()
    End Sub

    Private Sub MyGrid1_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles MyGrid1.KeyDown
        Select Case e.KeyCode
            Case Keys.Escape, Keys.F2, Keys.F3, Keys.F4, Keys.F5, Keys.F6, Keys.Up, Keys.Down, Keys.Left, Keys.Right
            Case Keys.Delete
                Data_Delete()
            Case Else

                If Materials_Other_Out1Model.WriteOffStatus <> "冲销" And Materials_Other_Out1Bll.GetRecordCount("M_OtherOut_Code='" & Code_Text.Text & "' and OrdersStatus='完成'") = 0 Then
                    If Zb_Check() = False Then Exit Sub
                    If Materials_Other_Out1Bll.GetRecordCount("M_OtherOut_Code='" & Code_Text.Text & "' And OrdersStatus='完成'") > 0 Then Exit Sub
                    If MyGrid1.Columns(MyGrid1.Col).DataField = "Materials_Name" Then '物资输入
                        Call Input("物资")
                        Exit Sub
                    End If
                End If

                If e.KeyCode = Keys.Enter Then
                    If MyGrid1.Columns(MyGrid1.Col).DataField = "M_OtherOut_Num" Then
                        Me.MyGrid1.DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveNone
                        Me.MyGrid1.Col = MyGrid1.Splits(0).DisplayColumns.IndexOf(MyGrid1.Columns("M_OtherOutDetail_Memo"))
                        Exit Sub
                    End If

                    If MyGrid1.Columns(MyGrid1.Col).DataField = "M_OtherOutDetail_Memo" Then
                        Me.MyGrid1.DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveNone
                        Me.MyGrid1.Row = Me.MyGrid1.Row + 1
                        If flag_Error = True Then
                            flag_Error = False
                            Exit Sub
                        End If
                        If Materials_Other_Out1Model.WriteOffStatus = "冲销" Then
                            Me.MyGrid1.Col = MyGrid1.Splits(0).DisplayColumns.IndexOf(MyGrid1.Columns("M_OtherOut_Num"))
                        Else
                            Me.MyGrid1.Col = MyGrid1.Splits(0).DisplayColumns.IndexOf(MyGrid1.Columns("Materials_Name"))
                        End If
                    Else
                        Me.MyGrid1.DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveRight
                    End If
                End If
        End Select
    End Sub

    Private Sub MyGrid1_MouseDoubleClick(ByVal sender As System.Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles MyGrid1.MouseDoubleClick
        If Materials_Other_Out1Model.WriteOffStatus <> "冲销" And Materials_Other_Out1Bll.GetRecordCount("M_OtherOut_Code='" & Code_Text.Text & "' and OrdersStatus='完成'") = 0 Then
            If Zb_Check() = False Then Exit Sub
            If MyGrid1.Columns(MyGrid1.Col).DataField = "Materials_Name" Then '物资输入
                Call Input("物资")
                Exit Sub
            End If
        End If
    End Sub

    Private Sub MyGrid1_GotFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles MyGrid1.GotFocus
        Me.CancelButton = Nothing
    End Sub

    Private Sub MyGrid1_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles MyGrid1.Validated
        Me.CancelButton = Comm_Close
    End Sub

    Private Sub MyGrid1_BeforeColUpdate(ByVal sender As System.Object, ByVal e As C1.Win.C1TrueDBGrid.BeforeColUpdateEventArgs) Handles MyGrid1.BeforeColUpdate
        If Zb_Check() = False Then Exit Sub
        If e.Column.Name = "物资名称" Then
            If VerifyMaterialsName(MyGrid1.Columns("Materials_Name").Text) = False Then
                e.Cancel = True
            End If
        End If

        If e.Column.Name = "数量" Then
            If VerifyNum(MyGrid1.Columns("M_OtherOut_Num").Value, MyGrid1.Row) = False Then
                e.Cancel = True
            End If
        End If
    End Sub

    Private Sub MyGrid1_Error(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.ErrorEventArgs) Handles MyGrid1.Error
        e.Handled = True
        flag_Error = True
        If StrConv(e.Exception.Message, VbStrConv.Narrow) = "列""Materials_Name""不允许空值?" Then
            MessageBox.Show("物资不能为空!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
            MyGrid1.SetActiveCell(MyGrid1.Row, MyGrid1.Splits(0).DisplayColumns.IndexOf(MyGrid1.Columns("Materials_Name")))
        End If

        If StrConv(e.Exception.Message, VbStrConv.Narrow) = "列""M_OtherOut_RealNo""不允许空值?" Then
            MessageBox.Show("数量不能为空!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
            MyGrid1.SetActiveCell(MyGrid1.Row, MyGrid1.Splits(0).DisplayColumns.IndexOf(MyGrid1.Columns("M_OtherOut_RealNo")))
        End If
    End Sub

#End Region

#Region "快捷键"
    Private Sub PjCl_Materials_Other_Out2_KeyDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles MyBase.KeyDown
        If e.Control = True And e.KeyCode = Keys.S And Comm_Save.Enabled = True Then
            Call Data_Save("保存")
        End If

        If e.KeyData = Keys.F2 And Comm_New.Enabled = True Then
            MyGrid1.Focus()
            sender.tag = "新单"
            Call Comm_Click(sender, Nothing)
        End If

        If e.KeyData = Keys.F3 And Comm_Save.Enabled = True Then
            MyGrid1.Focus()
            sender.tag = "保存"
            Call Comm_Click(sender, Nothing)
        End If

        If e.KeyData = Keys.F4 And Comm_Complete.Enabled = True Then
            MyGrid1.Focus()
            sender.tag = "完成"
            Call Comm_Click(sender, Nothing)
        End If

        If e.KeyData = Keys.F5 And Comm_Print.Enabled = True Then
            MyGrid1.Focus()
            sender.tag = "打印"
            Call Comm_Click(sender, Nothing)
        End If
        If e.KeyData = Keys.F6 And Comm_Search.Enabled = True Then
            MyGrid1.Focus()
            sender.tag = "速查"
            Call Comm_Click(sender, Nothing)
        End If
        If e.KeyData = Keys.F9 And Comm_Save.Enabled = True Then
            MyGrid1.Focus()
            sender.tag = "退出"
            Call Comm_Click(sender, Nothing)
        End If

    End Sub
#End Region

#End Region

#Region "自定义函数"

#Region "验证函数"
    Private Function Zb_Check() As Boolean
        If InOutClass_DtCom.SelectedIndex = -1 Then
            InOutClass_DtCom.Select()
            MessageBox.Show("请选择出库类别!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
            Return False

        ElseIf WareHouse_DtCom.SelectedIndex = -1 Then
            WareHouse_DtCom.Select()
            MessageBox.Show("请选择仓库!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
            Return False
        Else
            Return True
        End If
    End Function

    Private Function Zb_CheckWc() As Boolean
        If Materials_Other_Out1Bll.GetRecordCount("M_OtherOut_Code='" & Code_Text.Text & "' And OrdersStatus='完成'") > 0 Then
            MsgBox("此次出库已经完成!", MsgBoxStyle.Exclamation, "提示")
            Return False
        End If
        Return True
    End Function

    Private Function Cb_Check() As Boolean
        MyGrid1.UpdateData()
        Dim _Row As DataRow
        Dim i As Integer = 0
        For Each _Row In My_Table.Rows
            If _Row.RowState = DataRowState.Deleted Then
                Continue For
            End If
            If VerifyNum(_Row("M_OtherOut_Num"), i) = False Then
                MyGrid1.Focus()
                MyGrid1.SetActiveCell(i, MyGrid1.Splits(0).DisplayColumns.IndexOf(MyGrid1.Columns("M_OtherOut_Num")))
                Return False
            End If

            If Materials_Other_Out1Model.WriteOffStatus = "冲销" Then
                _Row("M_OtherOut_RealNo") = 0
                _Row("M_OtherOut_WriteoffNo") = 0
                _Row("M_OtherOut_Money") = _Row("M_OtherOut_Num") * _Row("M_OtherOut_Price")
                _Row("M_OtherOut_RealMoney") = 0
            Else
                _Row("M_OtherOut_RealNo") = _Row("M_OtherOut_Num") + _Row("M_OtherOut_WriteoffNo")
                _Row("M_OtherOut_Money") = CDec(_Row("M_OtherOut_Num")) * CDec(_Row("M_OtherOut_Price"))
                _Row("M_OtherOut_RealMoney") = CDec(_Row("M_OtherOut_RealNo")) * CDec(_Row("M_OtherOut_Price"))
            End If
            i = i + 1
        Next
        Return True
    End Function

    Private Function Cb_CheckRowCounts() As Boolean
        If MyGrid1.RowCount = 0 Then
            MsgBox("尚未录入数据", MsgBoxStyle.Exclamation, "提示")
            Return False
        End If
        Return True
    End Function

    '验证出库数量是否正确
    Private Function VerifyNum(ByVal Num As Object, ByVal Index As Integer) As Boolean

        If IsDBNull(Num) Then
            HisControl.msg.Show("请输入正确数字!", "提示")
            Return False
        End If

        If  Common.Tools.IsNumber(Num) = False Then
            HisControl.msg.Show("请输入正确数字!", "提示")
            Return False
        End If
        If Materials_Other_Out1Model.WriteOffStatus & "" <> "冲销" Then
            If CDbl(Num) <= 0 Then
                HisControl.msg.Show("出库数量必须大于0!", "提示")
                Return False
            End If
        Else
            If CDbl(Num) >= 0 Then
                HisControl.msg.Show("冲销数量必须小于0!", "提示")
                Return False
            End If
        End If

        Dim row As DataRow = Cb_Cm.List(Index).row
        If Materials_Other_Out1Model.WriteOffStatus & "" <> "冲销" Then '正常单据减少库存，要和当前库存比较
            If CDbl(Num) > MaterialsStockBll.GetModel(row("MaterialsStock_Code")).MaterialsStore_Num Then
                HisControl.msg.Show("出库数量不能大于库存数量!", "提示")
                Return False
            End If
        Else '冲销单据不能超过可冲数量
            If -CDbl(Num) > Materials_Other_Out2Bll.GetModelByCondition("M_OtherOut_Code='" & Materials_Other_Out1Model.WriteOff_Code & "' and MaterialsStock_Code='" & row("MaterialsStock_Code") & "'").M_OtherOut_RealNo Then
                HisControl.msg.Show("冲销数量不能大于可冲数量!", "提示")
                Return False
            End If
        End If
        Return True
    End Function
    '验证出库物资
    Private Function VerifyMaterialsName(ByVal Materials_Name As String) As Boolean
        If Materials_Name = "" Then
            HisControl.msg.Show("物资不能为空!", "提示")
            Return False
        End If
        Return True
    End Function
#End Region

#Region "按钮函数"

    Private Sub Data_Delete()
        If Materials_Other_Out1Bll.GetRecordCount("  M_OtherOut_Code='" & Code_Text.Text.Trim & "' and OrdersStatus='完成'") > 0 Then
            MsgBox("此次出库已经完成，不能删除!", MsgBoxStyle.Exclamation, "提示")
            Exit Sub
        End If
        If MessageBox.Show("确认是否删除当前记录,如果删除该记录将不可恢复!", "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Error, MessageBoxDefaultButton.Button2) = Windows.Forms.DialogResult.OK Then
            Try
                MyGrid1.Delete()
                Call F_Sum()
            Catch ex As Exception
                If ex.Message.ToString = "索引 -1 不是为负数，就是大于行数。" Then
                    MsgBox("未选中任何行！", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
                End If
            End Try

        End If
    End Sub

    Private Sub Data_DeleteAll()
        If Materials_Other_Out1Bll.GetRecordCount("  M_OtherOut_Code='" & Code_Text.Text.Trim & "' and OrdersStatus='完成'") > 0 Then
            MsgBox("此次出库已经完成，不能删除!", MsgBoxStyle.Exclamation, "提示")
            Exit Sub
        End If
        If MessageBox.Show("确认是否删除当前单据,如果删除该单据将不可恢复!", "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Error, MessageBoxDefaultButton.Button2) = Windows.Forms.DialogResult.OK Then
            Materials_Other_Out1Bll.Delete(Materials_Other_Out1Model.M_OtherOut_Code)
            Zb_Clear()
        End If
    End Sub

    Private Sub Data_New()
        MyGrid1.UpdateData()
        If My_Table.DataSet.HasChanges = True Then
            If Materials_Other_Out1Bll.GetRecordCount("  M_OtherOut_Code='" & Code_Text.Text.Trim & "' and OrdersStatus='录入'") = 1 Then
                If MessageBox.Show("数据尚未保存,是否保存数据?", "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1) = Windows.Forms.DialogResult.OK Then
                    Call Data_Save("保存")
                End If
            End If
        End If
        Call Zb_Clear()
    End Sub

    Private Sub Data_Save(ByVal arg As String)
        If Not Zb_Check() Then Exit Sub
        If Not Zb_CheckWc() Then Exit Sub
        If Not Cb_CheckRowCounts() Then Exit Sub
        If Not Cb_Check() Then Exit Sub

        If arg = "保存" Then
            Call Zb_Save()
        ElseIf arg = "完成" Then
            Call Zb_Save()
            Call Data_Complete()
        End If
    End Sub

    Private Sub Data_Complete()
        If MsgBox("是否完成此次出库？", MsgBoxStyle.Information + MsgBoxStyle.OkCancel + MsgBoxStyle.DefaultButton1, "提示:") = MsgBoxResult.Cancel Then Exit Sub

        If Not Zb_CheckWc() Then Exit Sub

        Try
            Materials_Other_Out1Model.OrdersStatus = "完成"
            Materials_Other_Out1Model.Finish_Date = Now
            If Not Materials_Other_Out1Bll.Complete(Materials_Other_Out1Model) Then
                With Materials_Other_Out1Model
                    .Finish_Date = Nothing
                    .OrdersStatus = "录入"
                End With
            End If
            StatisticsDataShow()
            HisControl.msg.Show("退库完成!", "提示")
            Call BtnState()
            Call P_Data_Show()
            Focus()
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            WareHouse_DtCom.Select()
            Exit Sub
        End Try

    End Sub


    Private Sub Data_Print()
        Dim StiRpt As New StiReport
        StiRpt.Load(".\Rpt\物资出库单.mrt")

        StiRpt.RegData(My_Table)

        StiRpt.Pages(0).PaperSize = Printing.PaperKind.A4
        StiRpt.Pages(0).Margins.Top = 1
        StiRpt.Pages(0).Margins.Bottom = 1
        StiRpt.Pages(0).Margins.Left = 1
        StiRpt.Pages(0).Margins.Right = 1
        StiRpt.Compile()

        StiRpt("仓库") = WareHouse_DtCom.Text
        StiRpt("单号") = Code_Text.Text
        StiRpt("出库日期") = Format(Form_Date.Value, "yyyy年MM月dd日")
        StiRpt("打印时间") = Format(Now, "yyyy-MM-dd HH:mm:ss")
        StiRpt("操作员") = HisVar.HisVar.JsrName

        StiRpt.Render()
        'StiRpt.Design()
        StiRpt.Show()
    End Sub

#End Region

#Region "数据库更改"

#Region "主表__编辑"

    Private Sub Zb_Save()                       '主表保存
        If Not Zb_CheckWc() Then Exit Sub
        If Rinsert = True Then     '增加记录
            Call Zb_Add()
        Else                                '编辑记录
            Call Zb_Edit()
        End If
        Call Cb_Update()
        HisControl.msg.Show("数据保存成功!", "提示")
        MyGrid1.Focus()
    End Sub

    Private Sub Zb_Add()    '增加记录
        Try
            With Materials_Other_Out1Model
                .M_OtherOut_Code = Materials_Other_Out1Bll.MaxCode(Format(Now, "yyMMdd"))
                .MaterialsWh_Code = WareHouse_DtCom.SelectedValue
                .MaterialsInOut_Code = InOutClass_DtCom.SelectedValue
                .OtherOut_Date = Format(Form_Date.Value, "yyyy-MM-dd HH:mm:ss")
                .Input_Date = Now
                .Jsr_Code = HisVar.HisVar.JsrCode
                .TotalMoney = V_TotalMoney
                .M_OtherOut_Memo = Memo_Text.Text
                .OrdersStatus = "录入"
                Code_Text.Text = .M_OtherOut_Code
            End With
            Materials_Other_Out1Bll.Add(Materials_Other_Out1Model)
            Rinsert = False
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            Exit Sub
        End Try
    End Sub

    Private Sub Zb_Edit()   '编辑记录
        Try
            With Materials_Other_Out1Model
                .OtherOut_Date = Format(Form_Date.Value, "yyyy-MM-dd HH:mm:ss")
                .MaterialsWh_Code = WareHouse_DtCom.SelectedValue
                .MaterialsInOut_Code = InOutClass_DtCom.SelectedValue
                .Jsr_Code = HisVar.HisVar.JsrCode
                .M_OtherOut_Memo = Memo_Text.Text
                .TotalMoney = V_TotalMoney ' .TotalMoney
            End With
            Materials_Other_Out1Bll.Update(Materials_Other_Out1Model)
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
            Exit Sub
        End Try
    End Sub
#End Region

#Region "从表__编辑"

    Private Sub Cb_Update()   '从表更新
        For Each _row As DataRow In My_Table.Rows
            If _row.RowState = DataRowState.Added Then
                CbData_Insert(_row)
            End If
            If _row.RowState = DataRowState.Modified Then
                CbData_Update(_row)
            End If
            If _row.RowState = DataRowState.Deleted Then
                CbData_Delete(_row)
            End If
        Next
        My_Table.AcceptChanges()
    End Sub

    Private Sub CbData_Update(ByVal Cb_Row As DataRow)   '从表更新
        Materials_Other_Out2Model = Materials_Other_Out2Bll.GetModel(Cb_Row("M_OtherOut_Detail_Code"))
        With Materials_Other_Out2Model
            .Materials_Code = Cb_Row("Materials_Code") & ""
            .MaterialsStock_Code = Cb_Row("MaterialsStock_Code") & ""
            .MaterialsLot = Cb_Row("MaterialsLot") & ""
            .MaterialsExpiryDate = Cb_Row("MaterialsExpiryDate")
            .M_OtherOut_Num = Cb_Row("M_OtherOut_Num")
            .M_OtherOut_WriteoffNo = Cb_Row("M_OtherOut_WriteoffNo")
            .M_OtherOut_RealNo = Cb_Row("M_OtherOut_RealNo")
            .M_OtherOut_Price = Cb_Row("M_OtherOut_Price")
            .M_OtherOut_Money = Cb_Row("M_OtherOut_Money")
            .M_OtherOut_RealMoney = Cb_Row("M_OtherOut_RealMoney")
            .M_OtherOutDetail_Memo = Cb_Row("M_OtherOutDetail_Memo") & ""
        End With
        Materials_Other_Out2Bll.Update(Materials_Other_Out2Model)
    End Sub

    Private Sub CbData_Insert(ByVal Cb_Row As DataRow)   '从表增加 
        Cb_Row("M_OtherOut_Code") = Materials_Other_Out1Model.M_OtherOut_Code
        Cb_Row("M_OtherOut_Detail_Code") = Materials_Other_Out2Bll.MaxCode(Materials_Other_Out1Model.M_OtherOut_Code)
        With Materials_Other_Out2Model
            .M_OtherOut_Code = Cb_Row("M_OtherOut_Code")
            .M_OtherOut_Detail_Code = Cb_Row("M_OtherOut_Detail_Code")
            .Materials_Code = Cb_Row("Materials_Code") & ""
            .MaterialsStock_Code = Cb_Row("MaterialsStock_Code") & ""
            .MaterialsLot = Cb_Row("MaterialsLot") & ""
            .MaterialsExpiryDate = Cb_Row("MaterialsExpiryDate")
            .M_OtherOut_Num = Cb_Row("M_OtherOut_Num")
            .M_OtherOut_WriteoffNo = Cb_Row("M_OtherOut_WriteoffNo")
            .M_OtherOut_RealNo = Cb_Row("M_OtherOut_RealNo")
            .M_OtherOut_Price = Cb_Row("M_OtherOut_Price")
            .M_OtherOut_Money = Cb_Row("M_OtherOut_Money")
            .M_OtherOut_RealMoney = Cb_Row("M_OtherOut_RealMoney")
            .M_OtherOutDetail_Memo = Cb_Row("M_OtherOutDetail_Memo") & ""
        End With
        Materials_Other_Out2Bll.Add(Materials_Other_Out2Model)
    End Sub

    Private Sub CbData_Delete(ByVal Cb_Row As DataRow)
        Materials_Other_Out2Bll.Delete(Cb_Row.Item("M_OtherOut_Detail_Code", DataRowVersion.Original))
    End Sub

#End Region

#End Region

    Private Sub ChangeModel(ByVal model As ModelOld.M_Materials_Other_Out1)
        Materials_Other_Out1Model = model
        If Materials_Other_Out1Model Is Nothing Then Exit Sub
        Call Zb_Show()
    End Sub

    Public Overrides Sub F_Sum()
        If MyGrid1.RowCount = 0 Then
            WareHouse_DtCom.Enabled = True
        Else
            WareHouse_DtCom.Enabled = False
        End If
        Dim Total_Return_Money As Double
        Total_Return_Money = IIf(My_Table.Compute("Sum(M_OtherOut_Money)", "") Is DBNull.Value, 0, My_Table.Compute("Sum(M_OtherOut_Money)", ""))
        V_TotalMoney = IIf(My_Table.Compute("Sum(M_OtherOut_RealMoney)", "") Is DBNull.Value, 0, My_Table.Compute("Sum(M_OtherOut_RealMoney)", ""))
        MyGrid1.Columns("M_OtherOut_Money").FooterText = Total_Return_Money.ToString("0.00##")
        If Materials_Other_Out1Model.OrdersStatus = "完成" And Materials_Other_Out1Model.WriteOffStatus <> "冲销" Then MyGrid1.Columns("M_OtherOut_RealMoney").FooterText = V_TotalMoney.ToString("0.00##")
    End Sub

    Private Sub Input(ByVal V_Lb As String)
        Dim strwhere As String = ""
        For Each _row In My_Table.Rows
            If _row.RowState = DataRowState.Deleted Then
                Continue For
            End If
            strwhere = strwhere & "'" & _row("MaterialsStock_Code") & "',"
        Next
        Dim f As New MaterialsStockSelect(WareHouse_DtCom.SelectedValue, strwhere)
        f.Owner = Me
        If f.ShowDialog = Windows.Forms.DialogResult.OK Then
            If My_Table.Select("MaterialsStock_Code='" & f.ModelMaterials_Stock.MaterialsStock_Code & "'").Length > 0 Then
                MsgBox("已经存在此条出库记录!", MsgBoxStyle.Exclamation, "提示")
                Exit Sub
            End If
            MyGrid1.Columns("M_OtherOut_Code").Value = Materials_Other_Out1Model.M_OtherOut_Code
            Dim up_Row As DataRow
            up_Row = Cb_Cm.List(MyGrid1.Row).Row
            Select Case V_Lb
                Case "物资"
                    With f.ModelMaterials_Stock
                        up_Row("Materials_Code") = .Materials_Code
                        up_Row("Materials_Name") = .Materials_Name
                        up_Row("MaterialsStock_Code") = .MaterialsStock_Code
                        up_Row("MaterialsLot") = .MaterialsLot
                        up_Row("MaterialsExpiryDate") = .MaterialsExpiryDate
                        up_Row("MaterialsStore_Num") = .MaterialsStore_Num
                        up_Row("M_OtherOut_Price") = .MaterialsStore_Price
                    End With
                    MyGrid1.SetActiveCell(MyGrid1.Row, MyGrid1.Splits(0).DisplayColumns.IndexOf(MyGrid1.Columns("M_OtherOut_Num")))
            End Select
        End If
    End Sub

#End Region

#Region "输入法设置"
    '中文
    Private Sub CodeTextBox1_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Memo_Text.GotFocus
        InputLanguage.CurrentInputLanguage = HisVar.HisVar.InputCode
    End Sub
    '英文
    Private Sub yw_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles WareHouse_DtCom.GotFocus, InOutClass_DtCom.GotFocus
        InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages(0)
    End Sub
#End Region

End Class