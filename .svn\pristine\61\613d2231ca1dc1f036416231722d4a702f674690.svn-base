﻿/**  版本信息模板在安装目录下，可自行修改。
* D_DRYB_MzSf_Ht.cs
*
* 功 能： N/A
* 类 名： D_DRYB_MzSf_Ht
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2017/3/8 11:36:04   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
namespace DAL
{
	/// <summary>
	/// 数据访问类:D_DRYB_MzSf_Ht
	/// </summary>
	public partial class D_DRYB_MzSf_Ht
	{
		public D_DRYB_MzSf_Ht()
		{}
		#region  BasicMethod



		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(Model.M_DRYB_MzSf_Ht model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into DRYB_MzSf_Ht(");
            strSql.Append("MzSf_Id,BcGrzhZfJe,BcTcZfJe,BcXjZfJe,MzZy_Code,Ry_Name,Lb,YbJz_Code,Jsr_Code)");
			strSql.Append(" values (");
            strSql.Append("@MzSf_Id,@BcGrzhZfJe,@BcTcZfJe,@BcXjZfJe,@MzZy_Code,@Ry_Name,@Lb,@YbJz_Code,@Jsr_Code)");
			SqlParameter[] parameters = {
					
					new SqlParameter("@MzSf_Id", SqlDbType.Int,4),
					
					new SqlParameter("@BcGrzhZfJe", SqlDbType.Decimal,9),
				
					new SqlParameter("@BcTcZfJe", SqlDbType.Decimal,9),
					new SqlParameter("@BcXjZfJe", SqlDbType.Decimal,9),
				
					new SqlParameter("@MzZy_Code", SqlDbType.Char,18),
					new SqlParameter("@Ry_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Lb", SqlDbType.VarChar,4),
					new SqlParameter("@YbJz_Code", SqlDbType.VarChar,12),
					new SqlParameter("@Jsr_Code", SqlDbType.Char,7),
					
                    };
            parameters[0].Value = model.MzSf_Id;
            parameters[1].Value = model.BcGrzhZfJe;
            parameters[2].Value = model.BcTcZfJe;
            parameters[3].Value = model.BcXjZfJe;
            parameters[4].Value = model.MzZy_Code;
            parameters[5].Value = model.Ry_Name;
            parameters[6].Value = model.Lb;
            parameters[7].Value = model.YbJz_Code;
            parameters[8].Value = model.Jsr_Code;
			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.M_DRYB_MzSf_Ht model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update DRYB_MzSf_Ht set ");
			strSql.Append("Id=@Id,");
			strSql.Append("MzSf_Id=@MzSf_Id,");
			strSql.Append("BcFhJbYlbxFyJe=@BcFhJbYlbxFyJe,");
			strSql.Append("BcFhJbYlbxWZfJe=@BcFhJbYlbxWZfJe,");
			strSql.Append("BcGrzhZfJe=@BcGrzhZfJe,");
			strSql.Append("BcGwyBzZfJe=@BcGwyBzZfJe,");
			strSql.Append("BcJrDbBf=@BcJrDbBf,");
			strSql.Append("BcTcZfJe=@BcTcZfJe,");
			strSql.Append("BcXjZfJe=@BcXjZfJe,");
			strSql.Append("JshICYe=@JshICYe,");
			strSql.Append("YlFyZe=@YlFyZe,");
			strSql.Append("BnZyCs=@BnZyCs,");
			strSql.Append("BcQfxBz=@BcQfxBz,");
			strSql.Append("MzZy_Code=@MzZy_Code,");
			strSql.Append("Ry_Name=@Ry_Name,");
			strSql.Append("Lb=@Lb,");
			strSql.Append("YbJz_Code=@YbJz_Code,");
			strSql.Append("Jsr_Code=@Jsr_Code,");
			strSql.Append("Js_Zt=@Js_Zt");
			strSql.Append(" where ");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.Int,4),
					new SqlParameter("@MzSf_Id", SqlDbType.Int,4),
					new SqlParameter("@BcFhJbYlbxFyJe", SqlDbType.Decimal,9),
					new SqlParameter("@BcFhJbYlbxWZfJe", SqlDbType.Decimal,9),
					new SqlParameter("@BcGrzhZfJe", SqlDbType.Decimal,9),
					new SqlParameter("@BcGwyBzZfJe", SqlDbType.Decimal,9),
					new SqlParameter("@BcJrDbBf", SqlDbType.Decimal,9),
					new SqlParameter("@BcTcZfJe", SqlDbType.Decimal,9),
					new SqlParameter("@BcXjZfJe", SqlDbType.Decimal,9),
					new SqlParameter("@JshICYe", SqlDbType.Decimal,9),
					new SqlParameter("@YlFyZe", SqlDbType.Decimal,9),
					new SqlParameter("@BnZyCs", SqlDbType.Decimal,9),
					new SqlParameter("@BcQfxBz", SqlDbType.Decimal,9),
					new SqlParameter("@MzZy_Code", SqlDbType.Char,18),
					new SqlParameter("@Ry_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Lb", SqlDbType.VarChar,4),
					new SqlParameter("@YbJz_Code", SqlDbType.VarChar,12),
					new SqlParameter("@Jsr_Code", SqlDbType.Char,7),
					new SqlParameter("@Js_Zt", SqlDbType.Bit,1)};
			parameters[0].Value = model.Id;
			parameters[1].Value = model.MzSf_Id;
			parameters[2].Value = model.BcFhJbYlbxFyJe;
			parameters[3].Value = model.BcFhJbYlbxWZfJe;
			parameters[4].Value = model.BcGrzhZfJe;
			parameters[5].Value = model.BcGwyBzZfJe;
			parameters[6].Value = model.BcJrDbBf;
			parameters[7].Value = model.BcTcZfJe;
			parameters[8].Value = model.BcXjZfJe;
			parameters[9].Value = model.JshICYe;
			parameters[10].Value = model.YlFyZe;
			parameters[11].Value = model.BnZyCs;
			parameters[12].Value = model.BcQfxBz;
			parameters[13].Value = model.MzZy_Code;
			parameters[14].Value = model.Ry_Name;
			parameters[15].Value = model.Lb;
			parameters[16].Value = model.YbJz_Code;
			parameters[17].Value = model.Jsr_Code;
			parameters[18].Value = model.Js_Zt;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
        
		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete()
		{
			//该表无主键信息，请自定义主键/条件字段
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from DRYB_MzSf_Ht ");
			strSql.Append(" where ");
			SqlParameter[] parameters = {
			};

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.M_DRYB_MzSf_Ht GetModel()
		{
			//该表无主键信息，请自定义主键/条件字段
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 Id,MzSf_Id,BcFhJbYlbxFyJe,BcFhJbYlbxWZfJe,BcGrzhZfJe,BcGwyBzZfJe,BcJrDbBf,BcTcZfJe,BcXjZfJe,JshICYe,YlFyZe,BnZyCs,BcQfxBz,MzZy_Code,Ry_Name,Lb,YbJz_Code,Jsr_Code,Js_Zt from DRYB_MzSf_Ht ");
			strSql.Append(" where ");
			SqlParameter[] parameters = {
			};

			Model.M_DRYB_MzSf_Ht model=new Model.M_DRYB_MzSf_Ht();
			DataSet ds=HisVar.HisVar.Sqldal.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.M_DRYB_MzSf_Ht DataRowToModel(DataRow row)
		{
			Model.M_DRYB_MzSf_Ht model=new Model.M_DRYB_MzSf_Ht();
			if (row != null)
			{
				if(row["Id"]!=null && row["Id"].ToString()!="")
				{
					model.Id=int.Parse(row["Id"].ToString());
				}
				if(row["MzSf_Id"]!=null && row["MzSf_Id"].ToString()!="")
				{
					model.MzSf_Id=int.Parse(row["MzSf_Id"].ToString());
				}
				if(row["BcFhJbYlbxFyJe"]!=null && row["BcFhJbYlbxFyJe"].ToString()!="")
				{
					model.BcFhJbYlbxFyJe=decimal.Parse(row["BcFhJbYlbxFyJe"].ToString());
				}
				if(row["BcFhJbYlbxWZfJe"]!=null && row["BcFhJbYlbxWZfJe"].ToString()!="")
				{
					model.BcFhJbYlbxWZfJe=decimal.Parse(row["BcFhJbYlbxWZfJe"].ToString());
				}
				if(row["BcGrzhZfJe"]!=null && row["BcGrzhZfJe"].ToString()!="")
				{
					model.BcGrzhZfJe=decimal.Parse(row["BcGrzhZfJe"].ToString());
				}
				if(row["BcGwyBzZfJe"]!=null && row["BcGwyBzZfJe"].ToString()!="")
				{
					model.BcGwyBzZfJe=decimal.Parse(row["BcGwyBzZfJe"].ToString());
				}
				if(row["BcJrDbBf"]!=null && row["BcJrDbBf"].ToString()!="")
				{
					model.BcJrDbBf=decimal.Parse(row["BcJrDbBf"].ToString());
				}
				if(row["BcTcZfJe"]!=null && row["BcTcZfJe"].ToString()!="")
				{
					model.BcTcZfJe=decimal.Parse(row["BcTcZfJe"].ToString());
				}
				if(row["BcXjZfJe"]!=null && row["BcXjZfJe"].ToString()!="")
				{
					model.BcXjZfJe=decimal.Parse(row["BcXjZfJe"].ToString());
				}
				if(row["JshICYe"]!=null && row["JshICYe"].ToString()!="")
				{
					model.JshICYe=decimal.Parse(row["JshICYe"].ToString());
				}
				if(row["YlFyZe"]!=null && row["YlFyZe"].ToString()!="")
				{
					model.YlFyZe=decimal.Parse(row["YlFyZe"].ToString());
				}
				if(row["BnZyCs"]!=null && row["BnZyCs"].ToString()!="")
				{
					model.BnZyCs=decimal.Parse(row["BnZyCs"].ToString());
				}
				if(row["BcQfxBz"]!=null && row["BcQfxBz"].ToString()!="")
				{
					model.BcQfxBz=decimal.Parse(row["BcQfxBz"].ToString());
				}
				if(row["MzZy_Code"]!=null)
				{
					model.MzZy_Code=row["MzZy_Code"].ToString();
				}
				if(row["Ry_Name"]!=null)
				{
					model.Ry_Name=row["Ry_Name"].ToString();
				}
				if(row["Lb"]!=null)
				{
					model.Lb=row["Lb"].ToString();
				}
				if(row["YbJz_Code"]!=null)
				{
					model.YbJz_Code=row["YbJz_Code"].ToString();
				}
				if(row["Jsr_Code"]!=null)
				{
					model.Jsr_Code=row["Jsr_Code"].ToString();
				}
				if(row["Js_Zt"]!=null && row["Js_Zt"].ToString()!="")
				{
					if((row["Js_Zt"].ToString()=="1")||(row["Js_Zt"].ToString().ToLower()=="true"))
					{
						model.Js_Zt=true;
					}
					else
					{
						model.Js_Zt=false;
					}
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select Id,MzSf_Id,BcFhJbYlbxFyJe,BcFhJbYlbxWZfJe,BcGrzhZfJe,BcGwyBzZfJe,BcJrDbBf,BcTcZfJe,BcXjZfJe,JshICYe,YlFyZe,BnZyCs,BcQfxBz,MzZy_Code,Ry_Name,Lb,YbJz_Code,Jsr_Code,Js_Zt ");
			strSql.Append(" FROM DRYB_MzSf_Ht ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" Id,MzSf_Id,BcFhJbYlbxFyJe,BcFhJbYlbxWZfJe,BcGrzhZfJe,BcGwyBzZfJe,BcJrDbBf,BcTcZfJe,BcXjZfJe,JshICYe,YlFyZe,BnZyCs,BcQfxBz,MzZy_Code,Ry_Name,Lb,YbJz_Code,Jsr_Code,Js_Zt ");
			strSql.Append(" FROM DRYB_MzSf_Ht ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM DRYB_MzSf_Ht ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}

        /// <summary>
        /// 回退笔数
        /// </summary>
        /// <param name="YbJz_Code"></param>
        /// <returns></returns>
        public int GetHuiTuiCount(string YbJz_Code)
        {
            if (string.IsNullOrEmpty(YbJz_Code))
            {
                return 0;
            }
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT COUNT(1) FROM DRYB_MzSf_Ht WHERE YbJz_Code = '" + YbJz_Code + "' ");

            object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }

        /// <summary>
        /// 回退金额
        /// </summary>
        /// <returns></returns>
        public Decimal GetHuiTuiMoney(string YbJz_Code)
        {
            if (string.IsNullOrEmpty(YbJz_Code))
            {
                return 0;
            }
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT SUM(BcTcZfJe) FROM DRYB_MzSf_Ht WHERE YbJz_Code = '" + YbJz_Code + "'");

            object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToDecimal(obj);
            }
        }

		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T. desc");
			}
			strSql.Append(")AS Row, T.*  from DRYB_MzSf_Ht T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "DRYB_MzSf_Ht";
			parameters[1].Value = "";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return HisVar.HisVar.Sqldal.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

