﻿using BLL;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Text;
using System.Windows.Forms;
using C1.Win.C1List;
using ERX.Form;
using ERX.Model;
using Newtonsoft.Json;
using Stimulsoft.Report;
using YBBLL;
using YBModel;
using ZTHisInsurance;
using ZTHisInsuranceAPI;
using Stimulsoft.Report.Export;
using ZTHisInsurance_Enum;

namespace ERX
{
    public partial class ERxUpload : Common.BaseForm.BaseFather
    {
        YBBLL.BllERx_Upload _bllExr = new YBBLL.BllERx_Upload();
        private YBBLL.BllCountry_YBMLDZ_YZRY _bllYzry = new BllCountry_YBMLDZ_YZRY();
        private BllCountry_YB_MZGH _bllCountryYbMzgh = new BllCountry_YB_MZGH();
        private bool _init;
        private DataTable _myTable;
        private CurrencyManager _myCm;
        private DataTable _subDataTable = new DataTable();
        private DataTable _diagTable;
        private ERXApi _api = new ERXApi();
        private YB_API _ybApi = new YB_API(ZTHisVar.Var.JsrCode, ZTHisVar.Var.JsrName);
        private MdlERx_Upload _mdlUpload = new MdlERx_Upload();
        private YBModel.MdlDataBaseInfoOut mdlDataBaseInfoOut = new MdlDataBaseInfoOut();
        public ERxUpload()
        {
            InitializeComponent();
        }

        private void ERxUpload_Load(object sender, EventArgs e)
        {
            Form_Init();
            DataClear();
        }
        #region 自定义方法

        private void Form_Init()
        {
            //主表
            myGrid1.Init_Grid();
            myGrid1.Init_Column("医保就诊ID", "mdtrt_id", 100, "中", "", false);
            myGrid1.Init_Column("患者姓名", "psn_name", 112, "左", "", false);
            myGrid1.Init_Column("年龄", "age", 42, "右", "", false);
            myGrid1.Init_Column("性别", "Ry_Sex", 42, "中", "", false);
            myGrid1.Init_Column("身份证号", "certno", 152, "中", "", false);
            myGrid1.Init_Column("家庭住址", "Ry_Address", 0, "左", "", false);
            myGrid1.Init_Column("电话号码", "Ry_Tell", 0, "左", "", false);
            myGrid1.Init_Column("门诊编号", "Mz_Code", 123, "中", "", false);
            myGrid1.Init_Column("患者科室", "Ks_Name", 82, "左", "", false);
            myGrid1.Init_Column("处方医生", "Ys_Name", 82, "中", "", false);
            myGrid1.Init_Column("临床诊断", "Jb_Name", 161, "左", "", false);
            myGrid1.Init_Column("经手人", "Jsr_Name", 0, "左", "", false);
            myGrid1.Init_Column("报销类别", "Bxlb_Name", 98, "左", "", false);
            myGrid1.Init_Column("发药日期", "Mz_FyQr_Date", 0, "中", "yyyy-MM-dd HH:mm:ss", false);
            myGrid1.Init_Column("门诊日期", "Mz_Date", 102, "中", "yyyy-MM-dd", false);
            myGrid1.Init_Column("门诊时间", "Mz_Time", 102, "中", "", false);

            myGrid1.Init_Column("处方追溯码", "rxTraceCode", 102, "中", "", false);
            myGrid1.Init_Column("医保处方编号", "hiRxno", 102, "中", "", false);
            myGrid1.Init_Column("处方状态编码", "rxStasCodg", 0, "中", "", false);
            myGrid1.Init_Column("医保处方状态", "rxStasName", 120, "中", "", false);
            myGrid1.Init_Column("医保状态", "Status", 100, "中", "", false);

            myGrid1.CanGroup = true;
            myGrid1.AllowSort = true;
            myGrid1.Columns["Mz_Code"].Aggregate = C1.Win.C1TrueDBGrid.AggregateEnum.Count;

            //从表
            myGrid2.Init_Grid();
            myGrid2.Init_Column("医保编码", "医疗目录编码", 195, "中", "", false);
            myGrid2.Init_Column("名称", "Xm_Name", 195, "左", "", false);
            myGrid2.Init_Column("规格", "Mx_Gg", 131, "左", "", false);
            myGrid2.Init_Column("剂型", "Jx_Name", 146, "左", "", false);
            myGrid2.Init_Column("产地", "Mx_Cd", 169, "左", "", false);
            myGrid2.Init_Column("有效期", "Yp_Yxq", 127, "中", "yyyy-MM-dd", false);
            myGrid2.Init_Column("用药途径代码", "Administration_Code", 120, "左", "", false);
            myGrid2.Init_Column("用药途径描述", "Administration_Name", 172, "左", "", false);
            myGrid2.Init_Column("用药天数", "Use_Days", 116, "左", "", false);
            myGrid2.Init_Column("频率", "Freq_Code", 140, "左", "", false);
            myGrid2.Init_Column("数量", "Mz_Sl", 78, "右", "##0", false);
            myGrid2.Init_Column("单位", "Xm_Dw", 93, "左", "", false);
            myGrid2.Init_Column("单价", "Mz_Dj", 176, "右", "###,###,##0.00##", false);

            comboYs1.Init();
            single_rxStasCodg1.Init();
            comboKs1.Init("Zd_YyKs.OI_Code in ('02','04')");

            //初始时间
            DateEditStart.CustomFormat = "yyyy-MM-dd HH:mm:ss";
            DateEditStart.EditFormat = "yyyy-MM-dd HH:mm:ss";
            DateEditStart.DisplayFormat = "yyyy-MM-dd HH:mm:ss";
            DateEditStart.Value = DateTime.Today.AddDays(-7).Date;
            //至
            DateEditEnd.CustomFormat = "yyyy-MM-dd HH:mm:ss";
            DateEditEnd.EditFormat = "yyyy-MM-dd HH:mm:ss";
            DateEditEnd.DisplayFormat = "yyyy-MM-dd HH:mm:ss";
            DateEditEnd.Value = DateTime.Now.Date.AddDays(1).AddSeconds(-1);

        }

        private void DataClear()
        {
            comboKs1.SelectedIndex = -1;
            comboYs1.SelectedIndex = -1;
            single_rxStasCodg1.SelectedIndex = 1;
            TxtCode.Text = "";    //门诊编号
            TxtHz.Text = "";
            DateEditStart.Value = DateTime.Today.AddDays(-7);
            DateEditEnd.Value = DateTime.Now;
            if (_myTable != null) _myTable.Clear();
        }

        private void DataInit()
        {
            //init = false;
            StringBuilder strSql = new StringBuilder();
            strSql.Append($" MzCf_Ok='true' and MzCfZxType in ('{ZTHisEnum.MzCfZxType.外配处方}')");
            switch (single_rxStasCodg1.SelectedValue + "")
            {
                case "5":
                    strSql.Append("");
                    break;
                case "6":
                    strSql.Append(" and rxStasCodg is null");
                    break;
                default:
                    strSql.Append($" and rxStasCodg={single_rxStasCodg1.SelectedValue.ToString()}");
                    break;
            }

            if (TxtHz.Text.Trim() != "")
            {
                strSql.Append(" and Mz.Ry_Name Like '%" + TxtHz.Text.Trim() + "%'");

            }
            if (comboKs1.Text.Trim() != "")
            {
                strSql.Append(" and Ks_Name Like '%" + comboKs1.Text.Trim() + "%'");

            }
            if (comboYs1.Text.Trim() != "")
            {
                strSql.Append(" and Ys_Name Like '%" + comboYs1.Text.Trim() + "%'");

            }
            if (TxtCode.Text.Trim() != "")
            {
                strSql.Append(" and Mz.Mz_Code Like '%" + TxtCode.Text.Trim() + "%'");

            }
            if (DateEditStart.Text != "" && DateEditEnd.Text != "")
            {
                strSql.Append(" and  CONVERT(VARCHAR(10),Mz_Date,120) + ' ' + Mz_Time Between '" + ((DateTime)DateEditStart.Value).ToString("yyyy-MM-dd 00:00:00") + "' And '" + ((DateTime)DateEditEnd.Value).ToString("yyyy-MM-dd 23:59:59") + "'  ");
            }


            _myTable = _bllExr.GetMzList(strSql.ToString()).Tables[0];
            _myTable.PrimaryKey = new DataColumn[] { _myTable.Columns["Mz.Mz_Code"] };
            _myTable.Columns["mdtrt_id"].ReadOnly = false;
            _myTable.Columns["psn_no"].ReadOnly = false;
            _myTable.Columns["psn_name"].ReadOnly = false;
            _myTable.Columns["certno"].ReadOnly = false;
            _myTable.Columns["age"].ReadOnly = false;
            _myCm = (CurrencyManager)BindingContext[_myTable, ""];
            this.myGrid1.DataTable = _myTable;

        }

        private void Cb_Show()
        {
            if (myGrid1.RowCount == 0)
            {
                if (_subDataTable != null) _subDataTable.Clear();
                return;
            }
            DataRow _myRow = ((DataRowView)_myCm.List[myGrid1.Row]).Row;
            bool Jz_State = false;
            if (_myRow["Jz_Code"] + "" != "")
            {
                Jz_State = true;
            }
            _subDataTable = _bllExr.GetCbYpList(_myRow["Mz_Code"].ToString(), Jz_State).Tables[0];
            myGrid2.DataTable = _subDataTable;
        }
        #endregion

        #region 控件动作
        private void BtnSearch_Click(object sender, EventArgs e)
        {
            DataInit();

        }
        private void myGrid1_RowColChange(object sender, C1.Win.C1TrueDBGrid.RowColChangeEventArgs e)
        {
            Cb_Show();
        }
        private void BtnClear_Click(object sender, EventArgs e)
        {
            DataClear();
        }

        private void CmduploadChk_Click(object sender, C1.Win.C1Command.ClickEventArgs e) //上传预核验
        {
            if (myGrid1.RowCount == 0) return;
            if (!ERXConfig.Erx_Init())
            {
                MessageBox.Show("请先配置电子处方参数!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }

            if (_subDataTable.Rows.Count == 0)
            {
                MessageBox.Show("电子处方必须包含药品!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            if (_subDataTable.AsEnumerable().Where<DataRow>(p => string.IsNullOrEmpty(p.Field<string>("医疗目录编码"))).Count() > 0)
            {
                MessageBox.Show("存在未对照的药品，请先对照", "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }
            DataRow _myRow = ((DataRowView)_myCm.List[myGrid1.Row]).Row;
            _diagTable = _bllExr.GetMzDiagList(_myRow["Mz_Code"] + "").Tables[0];
            if (_diagTable.Rows.Count == 0)
            {
                MessageBox.Show("该处方未填写诊断", "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }
            //判断诊断是否全部对照
            if (_diagTable.AsEnumerable().Where(p => p.Field<string>("YbJb_Code") + "" == "").Count() > 0)
            {
                MessageBox.Show("存在未对照的诊断，请先对照", "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            if (_myRow["rxStasCodg"] + "" == "1")
            {
                MessageBox.Show("该电子处方已经上传", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }
            int result = -1;
            string msg = "";

            MdluploadChkIn mdlInput = new MdluploadChkIn();

            ZTHisInsurance.YbReadCard frm = new YbReadCard(false, "");
            if (frm.ShowDialog() != DialogResult.OK) return;
            mdlInput.mdtrtCertType = frm.mdtrt_cert_type;
            mdlInput.mdtrtCertNo = frm.mdtrt_cert_no;
            mdlInput.ecToken = "";
            mdlDataBaseInfoOut = frm.mdl;
            switch (mdlInput.mdtrtCertType)
            {
                case "01":
                    mdlInput.ecToken = frm.mdtrt_cert_no;
                    break;
                case "03":
                    mdlInput.cardSn = frm.Card_SN;
                    break;
            }

            _mdlUpload = _bllExr.GetModel(_myRow["Mz_Code"] + "") ?? new MdlERx_Upload();
            _mdlUpload.psn_name = mdlDataBaseInfoOut.baseinfo.psn_name;
            _mdlUpload.certno = mdlDataBaseInfoOut.baseinfo.certno;
            _mdlUpload.age = decimal.Parse(mdlDataBaseInfoOut.baseinfo.age);
            _myRow["psn_name"] = _mdlUpload.psn_name;
            _myRow["certno"] = _mdlUpload.certno;
            _myRow["age"] = _mdlUpload.age;
            string insutype = mdlDataBaseInfoOut.insuinfo.Where(o => o.insutype == "310" || o.insutype == "390" || o.insutype == "340").FirstOrDefault().insutype + "";
            string insuplc_admdvs = mdlDataBaseInfoOut.insuinfo.Where(p => p.insutype == insutype).First()?.insuplc_admdvs;

            if (_myRow["mdtrt_id"] + "" == "" && string.IsNullOrEmpty(_myRow["Status"] + ""))
            {
                #region 调用挂号信息

                //不启用挂号，需要进行挂号
                YBModel.MdlCountry_YB_MZGH mdlHbYbMzgh = new YBModel.MdlCountry_YB_MZGH();
                mdlHbYbMzgh.psn_no = mdlDataBaseInfoOut.baseinfo.psn_no;
                mdlHbYbMzgh.insutype = insutype;
                mdlHbYbMzgh.begntime = DateTime.Parse(_myRow["Mz_DTime"].ToString());
                mdlHbYbMzgh.mdtrt_cert_type = mdlInput.mdtrtCertType;
                if (YB_Config.Province == YbProvince.吉林省 && mdlHbYbMzgh.mdtrt_cert_type == "03")
                {
                    mdlHbYbMzgh.mdtrt_cert_no = frm.CardString;
                }
                else
                {
                    mdlHbYbMzgh.mdtrt_cert_no = mdlInput.mdtrtCertNo;
                }
                mdlHbYbMzgh.ipt_otp_no = _myRow["Mz_Code"] + "";
                mdlHbYbMzgh.atddr_no = _myRow["prac_psn_code"].ToString();
                mdlHbYbMzgh.dr_name = _myRow["Ys_Name"].ToString();
                mdlHbYbMzgh.dept_code = _myRow["Ks_Code"].ToString();//开方科室编码
                mdlHbYbMzgh.dept_name = _myRow["Ks_Name"].ToString();//开方科室名称
                mdlHbYbMzgh.caty = _myRow["caty"].ToString();
                YBModel.MdlDataMzGhOut mdlDataMzGhOut = new MdlDataMzGhOut();
                result = _ybApi.MzGh(mdlHbYbMzgh, insuplc_admdvs, ref msg, ref mdlDataMzGhOut);
                if (result != 0)
                {
                    MessageBox.Show("医保门诊挂号失败:" + msg, "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }
                _mdlUpload.mdtrt_id = mdlDataMzGhOut.data.mdtrt_id;
                _mdlUpload.psn_no = mdlHbYbMzgh.psn_no;
                _mdlUpload.Status = ZTHisInsurance_Enum.YbMzStatus.已挂号.ToString();
                _mdlUpload.insuplc_admdvs = insuplc_admdvs;
                _mdlUpload.Mz_Code = _myRow["Mz_Code"] + "";
                if (_bllExr.GetRecordCount($"Mz_Code='{_mdlUpload.Mz_Code}'") > 0)
                {
                    if (_bllExr.Update(_mdlUpload))
                    {
                        _myRow["mdtrt_id"] = _mdlUpload.mdtrt_id;
                        _myRow["psn_no"] = _mdlUpload.psn_no;
                        _myRow["Status"] = _mdlUpload.Status;
                        _myRow["insuplc_admdvs"] = _mdlUpload.insuplc_admdvs;

                    }
                }
                else
                {
                    if (_bllExr.Add(_mdlUpload))
                    {
                        _myRow["mdtrt_id"] = _mdlUpload.mdtrt_id;
                        _myRow["psn_no"] = _mdlUpload.psn_no;
                        _myRow["Status"] = _mdlUpload.Status;
                        _myRow["insuplc_admdvs"] = _mdlUpload.insuplc_admdvs;
                    }
                }

                #endregion
            }

            if (_myRow["mdtrt_id"] + "" != "" && _myRow["Status"] + "" == YbMzStatus.已挂号.ToString())
            {
                #region 调用就诊上传信息

                MdlMzJzInfoIn mdlMzJzInfoIn = new MdlMzJzInfoIn();
                mdlMzJzInfoIn.psn_no = mdlDataBaseInfoOut.baseinfo.psn_no;
                mdlMzJzInfoIn.begntime = DateTime.Parse(_myRow["Mz_DTime"].ToString()).ToString("yyyy-MM-dd HH:mm:ss");
                if (YB_Config.Province == YbProvince.吉林省)//吉林没开通12门诊挂号
                {
                    mdlMzJzInfoIn.med_type = "11";
                }
                else
                {
                    mdlMzJzInfoIn.med_type = "12";
                }
                mdlMzJzInfoIn.main_cond_dscr = "";
                mdlMzJzInfoIn.mdtrt_id = _myRow["mdtrt_id"] + "";
                mdlMzJzInfoIn.birctrl_type = "";
                mdlMzJzInfoIn.birctrl_matn_date = "";
                mdlMzJzInfoIn.matn_type = "";
                mdlMzJzInfoIn.geso_val = 0;


                List<MdlMzJzInfoIn_Diseinfo> list = new List<MdlMzJzInfoIn_Diseinfo>();
                if (_diagTable.Rows.Count > 0)
                {
                    foreach (DataRow row in _diagTable.Rows)
                    {
                        MdlMzJzInfoIn_Diseinfo mdlMzJzInfoInDiseinfo = new MdlMzJzInfoIn_Diseinfo();
                        mdlMzJzInfoInDiseinfo.diag_type = row["Diag_Type"].ToString();//诊断类别
                        mdlMzJzInfoInDiseinfo.diag_srt_no = row["Diag_Srt_No"] + "";//诊断排序号
                        if (mdlMzJzInfoInDiseinfo.diag_srt_no == "1")
                        {
                            mdlMzJzInfoIn.dise_codg = row["YbJb_Code"].ToString();//主诊断代码
                            mdlMzJzInfoIn.dise_name = row["YbJb_Name"].ToString();//主诊断名称
                        }
                        mdlMzJzInfoInDiseinfo.diag_code = row["YbJb_Code"].ToString();//主诊断代码
                        mdlMzJzInfoInDiseinfo.diag_name = row["YbJb_Name"].ToString();//主诊断名称
                        mdlMzJzInfoInDiseinfo.diag_dept = row["Ks_Name"].ToString();
                        mdlMzJzInfoInDiseinfo.dise_dor_no = row["prac_psn_code"].ToString();
                        mdlMzJzInfoInDiseinfo.dise_dor_name = row["Ys_Name"].ToString();
                        mdlMzJzInfoInDiseinfo.diag_time = DateTime.Parse(row["Diag_Time"].ToString()).ToString("yyyy-MM-dd HH:mm:ss");
                        mdlMzJzInfoInDiseinfo.vali_flag = "1";
                        list.Add(mdlMzJzInfoInDiseinfo);
                    }
                }


                YBModel.MdlDataMzJzInfoOut mdlDataMzJzInfoOut = new MdlDataMzJzInfoOut();
                if (YB_Config.Province == YbProvince.吉林省)//吉林没开通12门诊挂号
                {
                    result = _ybApi.MzJzInfo(mdlMzJzInfoIn, list, insuplc_admdvs, ref msg, ref mdlDataMzJzInfoOut);
                }
                else
                {
                    result = _ybApi.MzJzInfoA(mdlMzJzInfoIn, list, insuplc_admdvs, ref msg, ref mdlDataMzJzInfoOut);
                }

                if (result != 0)
                {
                    MessageBox.Show("医保门诊就诊上传失败:" + msg + "\r\n请在医保接口的门诊报销进行撤销操作!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }
                _mdlUpload.Mz_Code = _myRow["Mz_Code"] + "";
                _mdlUpload.Status = ZTHisInsurance_Enum.YbMzStatus.就诊上传.ToString();
                _myRow["Status"] = _mdlUpload.Status;
                _bllExr.Update(_mdlUpload);

                #endregion
            }

            mdlInput.authNo = "";  //--------
            mdlInput.bizTypeCode = "01"; //01-定点医疗机构就诊  02-互联网医院问诊

            mdlInput.insuPlcNo = insuplc_admdvs; //参保地编号 异地参保必传---------

            mdlInput.hospRxno = _myRow["Mz_Code"] + "";//定点医药机构处方编码
            mdlInput.rxDrugCnt = _subDataTable.Rows.Count.ToString();
            if (myGrid2.RowCount > 0)
            {
                foreach (DataRow row in _subDataTable.Rows)
                {
                    switch (row["Dl_Code"].ToString())
                    {
                        case "01":
                            mdlInput.rxTypeCode = "1";//处方类别代码 (rx_type_code) 字典 
                            break;
                        case "02":
                            mdlInput.rxTypeCode = "10";//处方类别代码 (rx_type_code) 字典 
                            mdlInput.rxDrugCnt = row["Mz_Fs"].ToString();//药品类目数
                            break;
                        case "03":
                            mdlInput.rxTypeCode = "2";//处方类别代码 (rx_type_code) 字典 
                            mdlInput.rxUsedWayCodg = _subDataTable.AsEnumerable().Where(p => p.Field<string>("Dl_Code") == "03" && !string.IsNullOrEmpty(p.Field<string>("Administration_Code"))).FirstOrDefault()?["Administration_Code"]?.ToString() ?? "";
                            mdlInput.rxUsedWayName = _subDataTable.AsEnumerable().Where(p => p.Field<string>("Dl_Code") == "03" && !string.IsNullOrEmpty(p.Field<string>("Administration_Name"))).FirstOrDefault()?["Administration_Name"]?.ToString() ?? "";
                            mdlInput.rxFrquCodg = _subDataTable.AsEnumerable().Where(p => p.Field<string>("Dl_Code") == "03" && !string.IsNullOrEmpty(p.Field<string>("Freq_Code"))).FirstOrDefault()?["Freq_Code"]?.ToString() ?? "";
                            mdlInput.rxFrquName = _subDataTable.AsEnumerable().Where(p => p.Field<string>("Dl_Code") == "03" && !string.IsNullOrEmpty(p.Field<string>("Frequency"))).FirstOrDefault()?["Frequency"]?.ToString() ?? "";
                            mdlInput.rxDosunt = _subDataTable.AsEnumerable().Where(p => p.Field<string>("Dl_Code") == "03" && !string.IsNullOrEmpty(p.Field<string>("Xm_Dw"))).FirstOrDefault()?["Xm_Dw"]?.ToString() ?? "";
                            mdlInput.rxDoscnt = decimal.Parse(_subDataTable.AsEnumerable().Where(p => p.Field<string>("Dl_Code") == "03").FirstOrDefault()?["Dosage"]?.ToString() ?? "1").ToString("0.##");

                            break;
                        default:
                            mdlInput.rxTypeCode = "1";//处方类别代码 (rx_type_code) 字典 
                            break;
                    }

                    if (mdlInput.rxTypeCode != "")
                    {
                        break;
                    }
                }
            }

            mdlInput.prscTime = DateTime.Parse(_myRow["Mz_DTime"].ToString()).ToString("yyyy-MM-dd HH:mm:ss");//开方时间
            mdlInput.valiDays = "3";//处方有效天数
            mdlInput.valiEndTime = DateTime.Parse(_myRow["Mz_DTime"].ToString()).AddDays(int.Parse(mdlInput.valiDays)).ToString("yyyy-MM-dd HH:mm:ss"); //开方时间+处方有效天数
            mdlInput.rxCotnFlag = "0";//0-否 1-是

            List<MdlrxdrugdetailIn> mdlrxdrugdetailist = new List<MdlrxdrugdetailIn>();
            foreach (DataRow row in _subDataTable.Rows)
            {
                MdlrxdrugdetailIn mdlrxdrugdetail = new MdlrxdrugdetailIn();
                mdlrxdrugdetail.medListCodg = row["医疗目录编码"].ToString();//医保药品编码
                switch (row["Dl_Code"].ToString())
                {
                    case "01":
                        mdlrxdrugdetail.rxItemTypeCode = "11";//处方项目分类代码 (rx_item_type_code)
                        break;
                    case "02":
                        mdlrxdrugdetail.rxItemTypeCode = "12";//处方项目分类代码 (rx_item_type_code)
                        break;
                    case "03":
                        mdlrxdrugdetail.rxItemTypeCode = "13";//处方项目分类代码 (rx_item_type_code)
                        mdlrxdrugdetail.tcmdrugTypeCode = "3";
                        mdlrxdrugdetail.tcmdrugTypeName = "中药饮片";
                        break;
                    default:
                        mdlrxdrugdetail.rxItemTypeCode = "11";//处方项目分类代码 (rx_item_type_code)
                        break;
                }
                mdlrxdrugdetail.drugGenname = row["Xm_Name"].ToString();//药品通用名
                mdlrxdrugdetail.drugDosform = row["Jx_Name"].ToString();//药品剂型
                mdlrxdrugdetail.drugSpec = row["Mx_Gg"].ToString();//药品规格
                mdlrxdrugdetail.medcWayCodg = row["Administration_Code"].ToString();//用药途径代码
                mdlrxdrugdetail.medcWayDscr = row["Administration_Name"].ToString();//用药途径描述
                mdlrxdrugdetail.medcDays = row["Use_Days"] == DBNull.Value ? "1" : row["Use_Days"] + "";//用药天数   ---
                mdlrxdrugdetail.medcBegntime = DateTime.Parse(_myRow["Mz_DTime"].ToString()).ToString("yyyy-MM-dd HH:mm:ss");//用药开始时间    --------
                mdlrxdrugdetail.medcEndtime = DateTime.Parse(_myRow["Mz_DTime"].ToString()).AddDays(int.Parse(mdlrxdrugdetail.medcDays)).ToString("yyyy-MM-dd HH:mm:ss");//用药结束时间    
                mdlrxdrugdetail.drugCnt = row["Mz_Sl"].ToString();//药品发药总量---
                mdlrxdrugdetail.drugPric = row["Mz_Dj"].ToString();//药品单价  非必填
                mdlrxdrugdetail.drugDosunt = row["Xm_Dw"].ToString();//药品发药单位
                mdlrxdrugdetail.drugTotlcnt = row["Mz_Sl"].ToString();//用药总量---
                mdlrxdrugdetail.drugTotlcntEmp = row["Xm_Dw"].ToString();//用药总量单位
                mdlrxdrugdetail.sinDoscnt = row["Dosage"] == DBNull.Value ? "1" : row["Dosage"] + "";//单次用量
                mdlrxdrugdetail.sinDosunt = row["Xm_Dw"].ToString();//单次计量单位
                mdlrxdrugdetail.usedFrquCodg = row["Freq_Code"].ToString();//使用频次编码
                mdlrxdrugdetail.usedFrquName = row["Frequency"].ToString();//使用频次名称
                mdlrxdrugdetail.hospApprFlag = "1";//医院审批标志

                mdlrxdrugdetailist.Add(mdlrxdrugdetail);
            }

            mdlInput.rxdrugdetail = mdlrxdrugdetailist;
            string gend = "";
            //判断医生国家编码、医生职称不能为空
            if (_myRow["Manage_Title_Code"].ToString() + "" == "")
            {
                MessageBox.Show("请在【医护字典】里为医生分配职务" + "\r\n", "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }
            switch (_myRow["Ry_Sex"].ToString())
            {
                case "男":
                    gend = "1";
                    break;
                case "女":
                    gend = "2";
                    break;
                default:
                    gend = "9";
                    break;
            }
            MdlmdtrtinfoIn mdlmdtrtinfo = new MdlmdtrtinfoIn();

            mdlmdtrtinfo.fixmedinsCode = YB_Config.fixmedins_code;//定点医疗机构名称
            mdlmdtrtinfo.fixmedinsName = YB_Config.fixmedins_name;//定点医疗机构编号
            mdlmdtrtinfo.mdtrtId = _myRow["mdtrt_id"].ToString();//医保就诊ID  医保门诊挂号返回
            mdlmdtrtinfo.medType = frm.med_type;//医疗类别 (med_type)
            mdlmdtrtinfo.iptOtpNo = _myRow["Mz_Code"].ToString();//门诊号
            mdlmdtrtinfo.psnNo = _myRow["psn_no"].ToString();//医保人员编号
            mdlmdtrtinfo.patnName = mdlDataBaseInfoOut.baseinfo.psn_name;// _myRow["Ry_Name"].ToString();//患者姓名
            mdlmdtrtinfo.psnCertType = "01";//人员证件类型
            mdlmdtrtinfo.certno = mdlDataBaseInfoOut.baseinfo.certno;// _myRow["Ry_Sfzh"].ToString();//证件编码
            mdlmdtrtinfo.patnAge = mdlDataBaseInfoOut.baseinfo.age;// _myRow["Ry_Age"] + "";//年龄
            mdlmdtrtinfo.gend = gend; //性别
            mdlmdtrtinfo.prscDeptName = _myRow["Ks_Name"].ToString();//开方科室名称
            mdlmdtrtinfo.prscDeptCode = _myRow["Ks_Code"].ToString();//开方科室编码
            mdlmdtrtinfo.prscDrName = _myRow["Ys_Name"].ToString();//开方医师姓名
            mdlmdtrtinfo.drProfttlCodg = _myRow["Manage_Title_Code"].ToString();//医生职称编码
            mdlmdtrtinfo.drProfttlName = _myRow["Manage_Title_Name"].ToString();//医生职称名称
            mdlmdtrtinfo.drDeptCode = _myRow["Ks_Code"].ToString();//医生科室编码
            mdlmdtrtinfo.drDeptName = _myRow["Ks_Name"].ToString();//医生科室名称
            mdlmdtrtinfo.mdtrtTime = DateTime.Parse(_myRow["Mz_DTime"].ToString()).ToString("yyyy-MM-dd HH:mm:ss");//就诊时间
            mdlmdtrtinfo.spDiseFlag = "0";//特殊病种标志  0-否 1-是
            mdlInput.mdtrtinfo = mdlmdtrtinfo;

            List<MdldiseinfoIn> mdlsideList = new List<MdldiseinfoIn>();

            if (_diagTable.Rows.Count > 0)
            {
                foreach (DataRow row in _diagTable.Rows)
                {
                    MdldiseinfoIn mdlside = new MdldiseinfoIn();
                    mdlside.diagType = row["Diag_Type"].ToString();//诊断类别
                    mdlside.maindiagFlag = "0";//主诊断标志
                    mdlside.diagSrtNo = row["Diag_Srt_No"] + "";//诊断排序号
                    if (mdlside.diagSrtNo == "1")
                    {
                        mdlside.maindiagFlag = "1";//主诊断标志
                        mdlmdtrtinfo.maindiagCode = row["YbJb_Code"].ToString();//主诊断代码
                        mdlmdtrtinfo.maindiagName = row["YbJb_Name"].ToString();//主诊断名称
                    }

                    mdlside.diagCode = row["YbJb_Code"].ToString();//诊断代码
                    mdlside.diagName = row["YbJb_Name"].ToString();//诊断名称
                    if (mdlside.diagType == "2")
                    {
                        mdlside.tcmDiseCode = row["YbJb_Code"].ToString();//诊断代码
                        mdlside.tcmDiseName = row["YbJb_Name"].ToString();//诊断名称
                    }
                    if (mdlside.diagType == "3")
                    {
                        mdlside.tcmsympCode = row["YbJb_Code"].ToString();//诊断代码
                        mdlside.tcmsymp = row["YbJb_Name"].ToString();//诊断名称
                    }
                    mdlside.diagDept = row["Ks_Name"].ToString();//诊断科室
                    mdlside.diagDrNo = row["prac_psn_code"].ToString();//国家医保医师代码
                    mdlside.diagDrName = row["Ys_Name"].ToString();//诊断医生姓名
                    mdlmdtrtinfo.drCode = row["prac_psn_code"].ToString();//国家医保医师编码
                    mdlside.diagTime = DateTime.Parse(row["Diag_Time"].ToString()).ToString("yyyy-MM-dd HH:mm:ss");//诊断时间

                    mdlsideList.Add(mdlside);
                }
            }
            mdlInput.diseinfo = mdlsideList;
            MdluploadChkOut uploadChOut = new MdluploadChkOut();
            string type = "上传预核验";
            result = _api.uploadChk(mdlInput, ref msg, ref uploadChOut);
            if (result != 0)
            {
                MessageBox.Show(type + "失败!" + "\r\n" + msg, "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }
            _mdlUpload.hiRxno = uploadChOut.hiRxno;
            _mdlUpload.rxTraceCode = uploadChOut.rxTraceCode;
            _mdlUpload.Mz_Code = mdlmdtrtinfo.iptOtpNo;

            if (_bllExr.GetRecordCount($"Mz_Code='{mdlmdtrtinfo.iptOtpNo}'") > 0)
            {

                if (_bllExr.Update(_mdlUpload))
                {
                    _myRow["hiRxno"] = _mdlUpload.hiRxno;
                    _myRow["rxTraceCode"] = _mdlUpload.rxTraceCode;

                }
            }
            else
            {
                if (_bllExr.Add(_mdlUpload))
                {
                    _myRow["hiRxno"] = _mdlUpload.hiRxno;
                    _myRow["rxTraceCode"] = _mdlUpload.rxTraceCode;
                }
            }
            _myTable.AcceptChanges();
            CustomSunnyUI.MyShowNotifier.ShowSuccessNotifier("电子处方预核验上传成功");
        }
        private void CmdrxFixmedinsSignAndUpld_Click(object sender, C1.Win.C1Command.ClickEventArgs e) //医保电子签名和上传
        {
            if (myGrid1.RowCount == 0) return;
            if (!ERXConfig.Erx_Init())
            {
                MessageBox.Show("请先配置电子处方参数!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            DataRow _myRow = ((DataRowView)_myCm.List[myGrid1.Row]).Row;
            if (_myRow["rxStasCodg"] + "" == "1")
            {
                MessageBox.Show("该电子处方已经上传", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            #region 生成处方文件base64
            ZTHisPublicFunction.prescriptionPara prescriptionPara = new ZTHisPublicFunction.prescriptionPara();
            prescriptionPara.Mz_Code = _myRow["Mz_Code"].ToString();
            prescriptionPara.Bxlb_Name = _myRow["Bxlb_Name"].ToString();
            prescriptionPara.Jb_Name = _myRow["Jb_Name"].ToString();
            prescriptionPara.Jsr_Name = _myRow["Jsr_Name"].ToString();
            prescriptionPara.Ks_Name = _myRow["Ks_Name"].ToString();
            prescriptionPara.Mz_Money = double.Parse(_myRow["Mz_Money"].ToString());
            prescriptionPara.Ry_Age = _myRow["age"].ToString();
            prescriptionPara.Ry_Name = _myRow["psn_name"].ToString();
            prescriptionPara.Ry_Sex = _myRow["Ry_Sex"].ToString();
            prescriptionPara.Ys_Name = _myRow["Ys_Name"].ToString();
            prescriptionPara.Jz_Code = _myRow["Jz_Code"].ToString();
            prescriptionPara.Ys_Code = _myRow["Ys_Code"].ToString();
            prescriptionPara.Mz_Date = _myRow["Mz_Date"].ToString();
            prescriptionPara.Mz_Time = _myRow["Mz_Time"].ToString();
            ZTHisPublicFunction.OutpatientPrescription.IOutpatientPrescription mzcf;
            mzcf = ZTHisPublicFunction.OutpatientPrescription.OutpatientPrescriptionFactory.CreateOutpatientPrescriptionObject();
            StiReport StiRpt = mzcf.Print(prescriptionPara);
            StiRpt.Render();
            string path = Application.StartupPath + $"\\ErxUpload.pdf";
            if (File.Exists(path)) File.Delete(path);
            StiRpt.ExportDocument(StiExportFormat.Pdf, path);
            string originalRxFile = "";
            using (FileStream fsForRead = new FileStream(path, FileMode.Open))
            {
                fsForRead.Seek(0, SeekOrigin.Begin);
                byte[] bs = new byte[fsForRead.Length];
                int log = Convert.ToInt32(fsForRead.Length);
                fsForRead.Read(bs, 0, log);
                originalRxFile = Convert.ToBase64String(bs);
            }
            #endregion

            //电子处方上传

            MdlrxFileUpldIn mdlrxFileUpldIn = new MdlrxFileUpldIn();
            SelectYs frm = new SelectYs(_myRow["prac_psn_code"].ToString());
            if (frm.ShowDialog() != DialogResult.OK)
            {
                return;
            }
            mdlrxFileUpldIn.rxTraceCode = _myRow["rxTraceCode"].ToString();//处方追溯码  有效时间和处方有效时间保持一致，上传时每张处方只能使用一次
            mdlrxFileUpldIn.hiRxno = _myRow["hiRxno"].ToString();//医保处方编号
            mdlrxFileUpldIn.mdtrtId = _myRow["mdtrt_id"].ToString();//医保就诊ID
            mdlrxFileUpldIn.patnName = _myRow["psn_name"].ToString();//患者姓名
            mdlrxFileUpldIn.psnCertType = "01";//人员证件类型
            mdlrxFileUpldIn.certno = _myRow["certno"].ToString();//证件号码
            mdlrxFileUpldIn.fixmedinsName = YB_Config.fixmedins_name;//定点医药机构名称
            mdlrxFileUpldIn.fixmedinsCode = YB_Config.fixmedins_code;//定点医药机构编号
            mdlrxFileUpldIn.drCode = _myRow["prac_psn_code"].ToString();//开方医保医师代码
            mdlrxFileUpldIn.prscDrName = _myRow["Ys_Name"].ToString();//开方医师姓名
            mdlrxFileUpldIn.pharDeptName = _myRow["Ks_Name"].ToString();//审方药师科室名称
            mdlrxFileUpldIn.pharDeptCode = _myRow["Ks_Code"].ToString();//审方药师科室编号
            mdlrxFileUpldIn.pharProfttlCodg = _myRow["Manage_Title_Code"].ToString();//审方药师职称编码  N
            mdlrxFileUpldIn.pharProfttlName = _myRow["Manage_Title_Name"].ToString();//审方药师职称名称  N
            mdlrxFileUpldIn.pharCode = frm.YbYs_Code;//审方医保药师代码  国家  -----------暂时固定

            mdlrxFileUpldIn.pharCertType = "01";//审方药师证件类型 N
            mdlrxFileUpldIn.pharCertno = "";//审方药师证件号码 N
            mdlrxFileUpldIn.pharName = frm.Ys_Name;//审方药师姓名    ------------暂时固定
            mdlrxFileUpldIn.pharPracCertNo = "";//审方药师执业资格证号  N
            mdlrxFileUpldIn.pharChkTime = DateTime.Parse(_myRow["Mz_DTime"].ToString()).ToString("yyyy-MM-dd HH:mm:ss");//医疗机构药师审方时间  yyyy-MM-dd HH:mm:ss

            Dictionary<string, string> cfdic = new Dictionary<string, string>();
            cfdic.Add("rxTraceCode", mdlrxFileUpldIn.rxTraceCode);
            cfdic.Add("hiRxno", mdlrxFileUpldIn.hiRxno);
            cfdic.Add("mdtrtId", mdlrxFileUpldIn.mdtrtId);
            cfdic.Add("patnName", mdlrxFileUpldIn.patnName);
            cfdic.Add("psnCertType", mdlrxFileUpldIn.psnCertType);
            cfdic.Add("certno", mdlrxFileUpldIn.certno);
            cfdic.Add("fixmedinsName", mdlrxFileUpldIn.fixmedinsName);
            cfdic.Add("fixmedinsCode", mdlrxFileUpldIn.fixmedinsCode);
            cfdic.Add("drCode", mdlrxFileUpldIn.drCode);
            cfdic.Add("prscDrName", mdlrxFileUpldIn.prscDrName);
            cfdic.Add("pharDeptName", mdlrxFileUpldIn.pharDeptName);
            cfdic.Add("pharDeptCode", mdlrxFileUpldIn.pharDeptCode);
            cfdic.Add("pharProfttlCodg", mdlrxFileUpldIn.pharProfttlCodg);
            cfdic.Add("pharProfttlName", mdlrxFileUpldIn.pharProfttlName);
            cfdic.Add("pharCode", mdlrxFileUpldIn.pharCode);
            cfdic.Add("pharCertType", mdlrxFileUpldIn.pharCertType);
            cfdic.Add("pharCertno", mdlrxFileUpldIn.pharCertno);
            cfdic.Add("pharName", mdlrxFileUpldIn.pharName);
            cfdic.Add("pharPracCertNo", mdlrxFileUpldIn.pharPracCertNo);
            cfdic.Add("pharChkTime", mdlrxFileUpldIn.pharChkTime);

            MdlrxFixmedinsSignIn mdlrxFixmedinsSignIn = new MdlrxFixmedinsSignIn();
            mdlrxFixmedinsSignIn.fixmedinsCode = YB_Config.fixmedins_code;//定点机构代码
            mdlrxFixmedinsSignIn.originalValue = Convert.ToBase64String(Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(cfdic)));//原始待签名处方信息---------------
            mdlrxFixmedinsSignIn.originalRxFile = originalRxFile;//原始待签名处方文件

            MdlrxFixmedinsSignOut mdlrxFixmedinsSignOut = new MdlrxFixmedinsSignOut();
            string msg = "";
            int result = _api.rxFixmedinsSign(mdlrxFixmedinsSignIn, ref msg, ref mdlrxFixmedinsSignOut);
            if (result != 0)
            {
                MessageBox.Show("电子处方医保电子签名失败!" + "\r\n" + msg, "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }
            mdlrxFileUpldIn.rxFile = mdlrxFixmedinsSignOut.rxFile;//处方原件
            mdlrxFileUpldIn.signDigest = mdlrxFixmedinsSignOut.signDigest;//处方信息签名值
            MdlrxFileUpldOut mdlrxFileUpldOut = new MdlrxFileUpldOut();

            result = _api.rxFileUpld(mdlrxFileUpldIn, ref msg, ref mdlrxFileUpldOut);
            if (result != 0)
            {
                MessageBox.Show("电子处方上传失败!" + "\r\n" + msg, "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            _mdlUpload = _bllExr.GetModel(_myRow["Mz_Code"].ToString());
            _mdlUpload.rxStasCodg = mdlrxFileUpldOut.rxStasCodg;
            _mdlUpload.rxStasName = mdlrxFileUpldOut.rxStasName;
            _mdlUpload.Mz_Code = _myRow["Mz_Code"].ToString();

            _myRow["rxStasCodg"] = mdlrxFileUpldOut.rxStasCodg;
            _myRow["rxStasName"] = mdlrxFileUpldOut.rxStasName;

            try
            {
                _bllExr.Update(_mdlUpload);
                _myTable.AcceptChanges();
            }
            catch (Exception exception)
            {
                MessageBox.Show(exception.Message, "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            finally
            {
                CustomSunnyUI.MyShowNotifier.ShowSuccessNotifier("电子处方上传成功");
            }

        }

        private void CmdrxUndo_Click(object sender, C1.Win.C1Command.ClickEventArgs e)//电子处方撤销
        {
            if (myGrid1.RowCount == 0) return;
            DataRow _myRow = ((DataRowView)_myCm.List[myGrid1.Row]).Row;
            if (_myRow["rxStasCodg"] + "" != "1" && _myRow["Status"] + "" != YbMzStatus.就诊上传.ToString())
            {
                MessageBox.Show("该电子处方不是有效状态，无法撤销", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (_myRow["rxStasCodg"] + "" == "1")
            {
                MdlrxUndoIn mdlrxUndoIn = new MdlrxUndoIn();
                MdlCountry_YBMLDZ_YZRY mdlCountryYbmldzYzry = new MdlCountry_YBMLDZ_YZRY();
                mdlrxUndoIn.hiRxno = _myRow["hiRxno"].ToString();//医保处方编号
                mdlrxUndoIn.fixmedinsCode = YB_Config.fixmedins_code;//定点医疗机构编号
                mdlrxUndoIn.drCode = "-";//撤销医师的医保医师代码
                mdlrxUndoIn.undoDrName = ZTHisVar.Var.YsName;//撤销医师姓名
                mdlrxUndoIn.undoDrCertType = "01";//撤销医师证件类型
                mdlrxUndoIn.undoDrCertno = "-";//撤销医师证件号码
                if (ZTHisVar.Var.YsCode + "" == "")
                {
                    MessageBox.Show("请先关联医生再进行此操作!" + "\r\n", "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }
                if (_bllYzry.GetModel(ZTHisVar.Var.YsCode) != null)
                {
                    mdlCountryYbmldzYzry = _bllYzry.GetModel(ZTHisVar.Var.YsCode);
                    mdlrxUndoIn.undoDrCertType = mdlCountryYbmldzYzry.psn_cert_type;//撤销医师证件类型
                    mdlrxUndoIn.undoDrCertno = mdlCountryYbmldzYzry.certno;//撤销医师证件类型
                    mdlrxUndoIn.drCode = mdlCountryYbmldzYzry.prac_psn_code;//撤销医师的医保医师代码
                    mdlrxUndoIn.undoDrName = mdlCountryYbmldzYzry.prac_psn_name;//撤销医师姓名
                }

                mdlrxUndoIn.undoRea = "撤回";//撤销原因描述
                mdlrxUndoIn.undoTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                MdlrxUndoOut mdlrxUndoOut = new MdlrxUndoOut();
                string type = "电子处方撤销", msg = "";
                int result = _api.rxUndo(mdlrxUndoIn, ref msg, ref mdlrxUndoOut);
                if (result != 0)
                {
                    MessageBox.Show(type + "失败!" + "\r\n" + msg, "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                _mdlUpload = _bllExr.GetModel(_myRow["Mz_Code"].ToString());
                _mdlUpload.Mz_Code = _myRow["Mz_Code"].ToString();
                _mdlUpload.rxStasCodg = mdlrxUndoOut.rxStasCodg;
                _mdlUpload.rxStasName = mdlrxUndoOut.rxStasName;
                _mdlUpload.hiRxno = mdlrxUndoOut.hiRxno;
                _myRow["rxStasCodg"] = mdlrxUndoOut.rxStasCodg;
                _myRow["rxStasName"] = mdlrxUndoOut.rxStasName;
                _myRow["hiRxno"] = mdlrxUndoOut.hiRxno;
                try
                {
                    _bllExr.Update(_mdlUpload);
                    CustomSunnyUI.MyShowNotifier.ShowSuccessNotifier("电子处方撤销成功");
                }
                catch (Exception exception)
                {
                    MessageBox.Show(exception.Message, "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }
            }
            if (_myRow["Status"] + "" == YbMzStatus.就诊上传.ToString())
            {
                try
                {
                    //非挂号记录，撤销挂号
                    if (_bllCountryYbMzgh.GetRecordCount($"Mz_Code='{_mdlUpload.Mz_Code}'") == 0)
                    {
                        string msg = "";
                        MdlCountry_YB_MZGH _mdlHbYbMzgh = new MdlCountry_YB_MZGH();
                        _mdlUpload = _bllExr.GetModel(_myRow["Mz_Code"].ToString());
                        _mdlHbYbMzgh.psn_no = _mdlUpload.psn_no;
                        _mdlHbYbMzgh.mdtrt_id = _mdlUpload.mdtrt_id;
                        _mdlHbYbMzgh.ipt_otp_no = _mdlUpload.Mz_Code;
                        YBModel.MdlDataMzGhCancelOut mdlDataMzGhCancelOut = new MdlDataMzGhCancelOut();
                        int result = _ybApi.MzGhCancel(_mdlHbYbMzgh, _mdlUpload.insuplc_admdvs + "", ref msg, ref mdlDataMzGhCancelOut);
                        if (result != 0)
                        {
                            MessageBox.Show("医保门诊挂号撤销失败!" + "\r\n" + msg, "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            return;
                        }
                        _mdlUpload.psn_no = "";
                        _mdlUpload.mdtrt_id = "";
                        _mdlUpload.psn_name = "";
                        _mdlUpload.certno = "";
                        _mdlUpload.age = 0;
                        _mdlUpload.Status = "";
                        _mdlUpload.insuplc_admdvs = "";

                        _myRow["psn_no"] = _mdlUpload.psn_no;
                        _myRow["mdtrt_id"] = _mdlUpload.mdtrt_id;
                        _myRow["psn_name"] = _mdlUpload.psn_name;
                        _myRow["certno"] = _mdlUpload.certno;
                        _myRow["age"] = _mdlUpload.age;
                        _myRow["Status"] = _mdlUpload.Status;
                        _myRow["insuplc_admdvs"] = _mdlUpload.insuplc_admdvs;
                        _bllExr.Update(_mdlUpload);

                        CustomSunnyUI.MyShowNotifier.ShowSuccessNotifier("医保门诊挂号撤销成功");
                    }
                    _myTable.AcceptChanges();

                }
                catch (Exception exception)
                {
                    MessageBox.Show(exception.Message, "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }
            }
        }
        private void CmdhospRxDetlQuery_Click(object sender, C1.Win.C1Command.ClickEventArgs e)//电子处方信息查询
        {
            if (myGrid1.RowCount == 0) return;
            DataRow _myRow = ((DataRowView)_myCm.List[myGrid1.Row]).Row;
            MdlhospRxDetlQueryIn mdlhospRxDetlQueryIn = new MdlhospRxDetlQueryIn();
            mdlhospRxDetlQueryIn.fixmedinsCode = YB_Config.fixmedins_code;//定点医疗机构编号
            mdlhospRxDetlQueryIn.hiRxno = _myRow["hiRxno"].ToString();//医保处方号
            mdlhospRxDetlQueryIn.mdtrtId = _myRow["mdtrt_Id"].ToString();//医保就诊ID
            mdlhospRxDetlQueryIn.psnName = _myRow["Ry_Name"].ToString();//人员名称
            mdlhospRxDetlQueryIn.psnCertType = "01";//人员证件类型
            mdlhospRxDetlQueryIn.certno = _myRow["Ry_Sfzh"].ToString();//证件号码
            string type = "电子处方信息查询", msg = "";
            MdlhospRxDetlQueryOut mdlhospRxDetlQueryOut = new MdlhospRxDetlQueryOut();
            int result = -1;
            result = _api.hospRxDetlQuery(mdlhospRxDetlQueryIn, ref msg, ref mdlhospRxDetlQueryOut);

            if (result != 0)
            {
                MessageBox.Show(type + "失败!" + "\r\n" + msg, "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }
            ErxQuery frm = new ErxQuery(mdlhospRxDetlQueryOut);
            this.AddTabControl(frm, "电子处方信息查询", ZTHisResources.C_Resources.GetImage16(""));
        }

        private void CmdrxChkInfoQuery_Click(object sender, C1.Win.C1Command.ClickEventArgs e)//审核结果查询
        {
            if (myGrid1.RowCount == 0) return;
            DataRow _myRow = ((DataRowView)_myCm.List[myGrid1.Row]).Row;
            MdlrxChkInfoQueryIn mdlrxChkInfoQueryIn = new MdlrxChkInfoQueryIn();
            mdlrxChkInfoQueryIn.hiRxno = _myRow["hiRxno"].ToString();//医保处方编号
            mdlrxChkInfoQueryIn.fixmedinsCode = YB_Config.fixmedins_code;//定点医疗机构编号
            mdlrxChkInfoQueryIn.mdtrtId = _myRow["mdtrt_Id"].ToString();//医保就诊ID
            mdlrxChkInfoQueryIn.psnName = _myRow["Ry_Name"].ToString();//人员名称
            mdlrxChkInfoQueryIn.psnCertType = "01";//人员证件类型
            mdlrxChkInfoQueryIn.certno = _myRow["Ry_Sfzh"].ToString();//证件号码

            string type = "电子处方审核结果查询", msg = "";
            MdlrxChkInfoQueryOut mdlrxChkInfoQueryOut = new MdlrxChkInfoQueryOut();
            int result = -1;
            result = _api.rxChkInfoQuery(mdlrxChkInfoQueryIn, ref msg, ref mdlrxChkInfoQueryOut);
            if (result != 0)
            {
                MessageBox.Show(type + "失败!" + "\r\n" + msg, "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }
            MessageBox.Show("审核意见：" + mdlrxChkInfoQueryOut.rxChkStasCodg + "\r\n" + "处方审核时间：" + mdlrxChkInfoQueryOut.rxChkTime, "反馈结果", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void CmdrxSetlInfoQuery_Click(object sender, C1.Win.C1Command.ClickEventArgs e)//取药结果查询
        {
            if (myGrid1.RowCount == 0) return;
            DataRow _myRow = ((DataRowView)_myCm.List[myGrid1.Row]).Row;

            MdlrxSetlInfoQueryIn mdlrxSetlInfoQueryIn = new MdlrxSetlInfoQueryIn();
            mdlrxSetlInfoQueryIn.hiRxno = _myRow["hiRxno"].ToString();//医保处方编号
            mdlrxSetlInfoQueryIn.fixmedinsCode = YB_Config.fixmedins_code;
            mdlrxSetlInfoQueryIn.mdtrtId = _myRow["mdtrt_Id"].ToString();//医保就诊ID
            mdlrxSetlInfoQueryIn.psnName = _myRow["Ry_Name"].ToString();//人员名称
            mdlrxSetlInfoQueryIn.psnCertType = "01";//人员证件类型
            mdlrxSetlInfoQueryIn.certno = _myRow["Ry_Sfzh"].ToString();//证件号码
            string type = "电子处方取药结果查询", msg = "";
            MdlrxSetlInfoQueryOut mdlrxSetlInfoQueryOut = new MdlrxSetlInfoQueryOut();
            int result = -1;
            result = _api.rxSetlInfoQuery(mdlrxSetlInfoQueryIn, ref msg, ref mdlrxSetlInfoQueryOut);

            if (result != 0)
            {
                MessageBox.Show(type + "失败!" + "\r\n" + msg, "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            ERxQyJgQuery frm = new ERxQyJgQuery(mdlrxSetlInfoQueryOut);
            this.AddTabControl(frm, "电子处方取药结果查询", ZTHisResources.C_Resources.GetImage16(""));

        }
        private void Cmdcfypmucx_Click(object sender, C1.Win.C1Command.ClickEventArgs e)//电子处方药品目录查询
        {
            MdlcircDrugQueryIn mdlcircDrugQueryIn = new MdlcircDrugQueryIn();
            MdlcircDrugQueryOut mdlcircDrugQueryOut = new MdlcircDrugQueryOut();
            mdlcircDrugQueryIn.fixmedinsCode = YB_Config.fixmedins_code;
            mdlcircDrugQueryIn.pageNum = "1";
            mdlcircDrugQueryIn.pageSize = "100";
            string type = "电子处方药品目录查询", msg = "";
            int result = -1;
            result = _api.circDrugQuery(mdlcircDrugQueryIn, ref msg, ref mdlcircDrugQueryOut);
            if (result != 0)
            {
                MessageBox.Show(type + "失败!" + "\r\n" + msg, "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }
            ErxypmlQuery frm = new ErxypmlQuery(mdlcircDrugQueryIn, mdlcircDrugQueryOut);
            this.AddTabControl(frm, "电子处方药品目录查询", ZTHisResources.C_Resources.GetImage16(""));
        }
        #endregion


    }
}
