﻿Imports HisVar.JkkService
Imports YBBLL
Imports YBModel
Imports ZTHisInsurance
Imports ZTHisNurse
Imports ZTHisOutpatient
Imports ZTHisSysManage

Public Class Test
    Private Sub Button2_Click(sender As Object, e As EventArgs) Handles Button2.Click
        Dim frm As New ZTHisInsurance.BxMx_Query
        BaseFunc.BaseFunc.addTabControl(frm, "出院办理")
    End Sub

    Private Sub Button6_Click(sender As Object, e As EventArgs) Handles Button6.Click
        Dim frm As New Mz1
        BaseFunc.BaseFunc.addTabControl(frm, "新医生开处方")
    End Sub
    Private Sub Test_Load(sender As Object, e As EventArgs) Handles MyBase.Load

    End Sub

    Private Sub Button5_Click(sender As Object, e As EventArgs) Handles Button5.Click
        Dim frm As New ZTHisPharmacy.Yf_TYk1
        BaseFunc.BaseFunc.addTabControl(frm, "药房退药库")
    End Sub

    Private Sub Button4_Click(sender As Object, e As EventArgs) Handles Button4.Click
        Dim frm As New ZTHisFinance.Xm_Tj1
        BaseFunc.BaseFunc.addTabControl(frm, "项目售价查询")
    End Sub

    Private Sub Button1_Click(sender As Object, e As EventArgs) Handles Button1.Click
        Dim frm As New ZTHisFinance.JcKs_Money_Tj
        BaseFunc.BaseFunc.addTabControl(frm, "医技科室统计")
    End Sub
End Class