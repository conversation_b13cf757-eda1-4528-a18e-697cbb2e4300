﻿
Imports System.Text
'Imports DCSoft.WinForms.Controls
Imports System.ComponentModel
Imports Stimulsoft.Report
Imports System.Windows.Forms

Public Class EmrAddBl
    Dim V_Finish As Boolean = False             '初始化完成
    Dim My_Table As New DataTable            '药品字典
    Dim My_Cm As CurrencyManager             '同步指针
    Dim m_Rc As New BaseClass.C_RowChange
    'Public showFlag As String
    Dim My_Row As DataRow                    '当 前 行
    Dim V_Insert As Boolean                  '增加记录
    Dim My_View As New DataView                 '视图
    Dim completeFlag As Boolean
    Public Shared vSelectedNodeTag As String
    Dim vSelectedNodeText As String
    Dim datesetcy As New DataSet
    Dim sqr As String = ""
    Dim boysql As String = ""
    Dim girlsql As String = ""
    Dim Tysql As String = ""
    Dim mbnamesql As String = ""

    Dim Emr_bl As New BLLOld.B_Emr_Bl
    Dim BllEmr_Mblb As New BLLOld.B_Emr_Mblb
    Dim ModelEmr_Mblb As New ModelOld.M_Emr_Mblb
    Dim BllEmr_Mb As New BLLOld.B_Emr_Mb

#Region "传参"
    Dim blmode As New ModelOld.M_PatientInfo
#End Region

    Public Sub New(ByVal modelinfer As ModelOld.M_PatientInfo)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        blmode = modelinfer
        ' 在 InitializeComponent() 调用之后添加任何初始化。
    End Sub

    Private Sub EmrAddBl_Load(sender As System.Object, e As System.EventArgs) Handles MyBase.Load

        Form_Init()
        sqr = sqr & " ( isStandard=1 OR (isStandard=0 and Emr_Mb.Ks_Code='" & blmode.Ks_code & "' AND ISNULL(Emr_Mb.Ys_Code,'') IN ('','" & HisVar.HisVar.JsrYsCode & "'))) and  AgeLimit>='" & blmode.Age & "' "
        Init_Tree()
    End Sub

#Region "窗体加载"
    Private Sub Form_Init()
        Panel1.BorderStyle = BorderStyle.None

        With MyGrid1
            .Init_Column("模板类别编码", "Mblb_Code", "120 ", "中", "", False)
            .Init_Column("所属类别", "Mblb_Name", "120 ", "左", "", False)
            .Init_Column("模板编码", "Mb_Code", "120", "中", "", False)
            .Init_Column("模板名称", "Mb_Name", "160", "左", "", False)
            .Init_Column("适应性别", "Mb_Sex", "120 ", "中", "Combobox", False)
            .Init_Column("是否必需", "isMust", "80 ", "中", "Combobox", False)
            .Init_Column("是否多份", "isMulti", "80 ", "中", "Combobox", False)
            .Init_Column("年龄限制", "AgeLimit", "100 ", "中", "", False)
            .Init_Column("科室名称", "ks_name", "120", "中", "", False)
            .Init_Column("医生名称", "ys_Name", "120", "中", "", False)

            .Columns("Mb_Sex").ValueItems.Values.Add(New C1.Win.C1TrueDBGrid.ValueItem("0", "通用"))
            .Columns("Mb_Sex").ValueItems.Values.Add(New C1.Win.C1TrueDBGrid.ValueItem("1", "男性"))
            .Columns("Mb_Sex").ValueItems.Values.Add(New C1.Win.C1TrueDBGrid.ValueItem("2", "女性"))

            .Columns("isMust").ValueItems.Values.Add(New C1.Win.C1TrueDBGrid.ValueItem(True, "必需"))
            .Columns("isMust").ValueItems.Values.Add(New C1.Win.C1TrueDBGrid.ValueItem(False, "无需"))
            .Columns("isMulti").ValueItems.Values.Add(New C1.Win.C1TrueDBGrid.ValueItem(True, "多份"))
            .Columns("isMulti").ValueItems.Values.Add(New C1.Win.C1TrueDBGrid.ValueItem(False, "一份"))
            .AllowUpdate = True
        End With

        TyCheckBox.Checked = True
        If blmode.Sex = 0 Then
            GCheckBox.Checked = True
            BCheckBox.Enabled = False
            BCheckBox.Checked = False
        ElseIf blmode.Sex = 1 Then
            GCheckBox.Checked = False
            GCheckBox.Enabled = False
            BCheckBox.Checked = True
        End If
    End Sub
#End Region


#Region "自定义函数"
    Private Sub GridMove(ByVal s As String)
        With MyGrid1
            If .RowCount = 0 Then Exit Sub
            Select Case s
                Case "最前"
                    .MoveFirst()
                Case "上移"
                    .MovePrevious()
                Case "下移"
                    .MoveNext()
                Case "最后"
                    .MoveLast()
                    T_Label.Text = "∑=" + .Splits(0).Rows.Count.ToString
            End Select
        End With
    End Sub

    Private Sub P_Init_Data1(ByVal V_Mblb_Code As String)
        Dim sql As String
        sql = Tysql + boysql + girlsql
        If sql <> "" Then
            sql = "and ( " & sql.Substring(2, sql.Length - 2) & ") " & mbnamesql
        Else
            sql = mbnamesql
        End If
        sql = sqr & sql
        If V_Mblb_Code = "00000" Then
            My_Table = BllEmr_Mb.GetList(sql).Tables(0)
        Else

            My_Table = BllEmr_Mb.GetList(" Emr_Mb.Mblb_Code='" & V_Mblb_Code & "' and " & sql).Tables(0)
        End If

        My_Table.PrimaryKey = New DataColumn() {My_Table.Columns("Mb_Code")}
        With MyGrid1
            My_Cm = CType(BindingContext(My_Table, ""), CurrencyManager)
            My_View = My_Cm.List
            .DataTable = My_Table
            T_Label.Text = "∑=" + .Splits(0).Rows.Count.ToString
        End With
    End Sub
#End Region

#Region "Tree"
    Private Sub Init_Tree()
        V_Finish = False
        '根节点
        Dim sql As String
        sql = Tysql + boysql + girlsql
        If sql <> "" Then
            sql = "and ( " & sql.Substring(2, sql.Length - 2) & ") " & mbnamesql
        Else
            sql = mbnamesql
        End If
        sql = sqr & sql
        TreeView1.Nodes.Clear()
        Dim My_Node As New TreeNode
        With My_Node
            .Tag = "00000"
            .Text = "模板类别" & "(" & BllEmr_Mb.GetRecordCount(sql) & "" & ")"
            .ImageIndex = 0
            .SelectedImageIndex = 0
        End With
        TreeView1.Nodes.Add(My_Node)
        Product_Node(My_Node)
        '一级数据
        V_Finish = True
        With Me.TreeView1
            .SelectedNode = TreeView1.TopNode
            .SelectedNode.Expand()
            .Select()
        End With
    End Sub
    'Mblb_Code, Mblb_Name, Father_Code
    'Mblb_Code, Mb_Code, Mb_Name

    Private Sub Product_Node(ByVal FatherNode As TreeNode)
        Dim dt As DataTable
        Dim sql As String
        sql = Tysql + boysql + girlsql
        If sql <> "" Then
            sql = "and ( " & sql.Substring(2, sql.Length - 2) & ") " & mbnamesql
        Else
            sql = mbnamesql
        End If
        sql = sqr & sql

        'Dim sqls As String = ""
        'If sqr <> "" Then
        '    sqls = " and " & sqr
        'End If
        dt = BllEmr_Mblb.GetList("Emr_Mblb.Father_Code='" & FatherNode.Tag & "' ").Tables(0)

        If dt.Rows.Count > 0 Then
            For Each row As DataRow In dt.Rows
                Dim My_Node As New TreeNode
                With My_Node
                    .Tag = row("Mblb_Code").ToString
                    .Text = row("Mblb_Name").ToString & "(" & BllEmr_Mb.GetRecordCount("Mblb_Code='" & row("Mblb_Code").ToString.Trim & "' and " & sql & "") & ")"
                    .ImageIndex = 1
                    .SelectedImageIndex = 2
                End With
                FatherNode.Nodes.Add(My_Node)
                Product_Node(My_Node)
            Next
        End If
    End Sub

    Private Sub Tree_Edit(ByVal V_Key As String, ByVal V_Text As String, Optional ByVal InsertFlag As String = "")
        If InsertFlag = "" Then
            Dim My_Node As New TreeNode
            If V_Insert = True Then
                My_Node.Tag = V_Key
                My_Node.Text = V_Text
                My_Node.ImageIndex = 1
                My_Node.SelectedImageIndex = 2
                TreeView1.SelectedNode.Nodes.Add(My_Node)
                If TreeView1.SelectedNode.IsExpanded = False Then
                    TreeView1.SelectedNode.Expand()
                End If
                P_Count()

            Else
                TreeView1.SelectedNode.Text = V_Text
                If My_Table.Rows.Count > 0 Then
                    For Each row In My_Table.Rows
                        row("Mblb_name") = Mid(V_Text, 1, InStr(V_Text, "(") - 1)
                    Next
                End If
            End If
        ElseIf InsertFlag = "update" Then
            completeFlag = False
            Find_Node(V_Key, TreeView1.TopNode)

            P_Count()

        Else
            completeFlag = False
            Find_Node(InsertFlag, TreeView1.TopNode)
            Dim My_Node As New TreeNode
            My_Node.Tag = V_Key
            My_Node.Text = V_Text
            My_Node.ImageIndex = 1
            My_Node.SelectedImageIndex = 2
            TreeView1.SelectedNode.Nodes.Add(My_Node)
            TreeView1.SelectedNode = My_Node
            P_Count()
        End If

    End Sub

    Private Sub Find_Node(ByVal V_Key As String, ByVal node As TreeNode)

        If node.Tag = V_Key Then
            TreeView1.SelectedNode = node
            completeFlag = True
            Exit Sub
        End If

        If node.Nodes.Count > 0 Then
            For Each My_Node As TreeNode In node.Nodes
                If completeFlag = True Then
                    Exit Sub
                End If

                If My_Node.Tag = V_Key Then
                    If TreeView1.SelectedNode.Tag = My_Node.Tag Then
                        TreeView1_AfterSelect(Nothing, Nothing)
                    Else
                        TreeView1.SelectedNode = My_Node
                    End If
                    completeFlag = True
                    Exit Sub
                End If

                Find_Node(V_Key, My_Node)
            Next
        End If
    End Sub

    Private Sub CheckMbExist(ByVal node As TreeNode)
        If node.Nodes.Count = 0 Then
            If BllEmr_Mb.GetRecordCount("Mblb_Code='" & node.Tag & "'") > 0 Then
                completeFlag = True
            End If
        Else
            For Each childNode In node.Nodes
                Call CheckMbExist(childNode)
                If completeFlag = True Then Exit Sub
            Next
        End If
    End Sub


    Private Sub P_Delete_Node(ByVal node As TreeNode)
        If node.Tag = TreeView1.TopNode.Tag Then
            MsgBox("无法删除根节点！", MsgBoxStyle.Critical, "提示")
        Else

            Dim par_Node As TreeNode = node.Parent
            Delete_node(par_Node, node)
            TreeView1.SelectedNode = par_Node
            P_Count()
        End If

    End Sub

    Private Sub Delete_node(ByVal Parent_Node As TreeNode, ByVal Child_Node As TreeNode)
        If Child_Node.Nodes.Count = 0 Then
            Parent_Node.Nodes.Remove(Child_Node)
            BllEmr_Mblb.Delete(Child_Node.Tag)
        Else
            Delete_node(Child_Node, Child_Node.FirstNode)
            Delete_node(Parent_Node, Child_Node)
        End If

    End Sub

    Private Sub P_Count()
        TreeView1.TopNode.Text = "模板类别" & "(" & BllEmr_Mb.GetRecordCount("") & ")" '修改Treeview节点
        completeFlag = False
        Find_Node(vSelectedNodeTag, TreeView1.TopNode)
        If TreeView1.SelectedNode.Tag <> "00000" Then

            TreeView1.SelectedNode.Text = Mid(TreeView1.SelectedNode.Text, 1, InStr(TreeView1.SelectedNode.Text, "(") - 1) & "(" & My_Table.Rows.Count & ")"
        End If
    End Sub
#End Region

#Region "控件动作"

    Private Sub TreeView1_AfterSelect(ByVal sender As System.Object, ByVal e As System.Windows.Forms.TreeViewEventArgs) Handles TreeView1.AfterSelect
        If V_Finish = False Then Exit Sub '     '初始化完成
        Call P_Init_Data1(Me.TreeView1.SelectedNode.Tag)
        vSelectedNodeTag = TreeView1.SelectedNode.Tag
        'TreeView1.SelectedNode.Expand()
        My_Table.AcceptChanges() 'afterSelect更新My_table
    End Sub

    Private Sub MyButton1_Click(sender As System.Object, e As System.EventArgs) Handles MyButton1.Click, MyGrid1.DoubleClick
        datesetcy = Emr_bl.GetListblandmb(" Bl_Code='" & blmode.Bl_Code & "' AND dbo.Emr_Bl.Mb_Code='" & My_Row.Item("Mb_Code") & "'")
        If datesetcy.Tables(0).Rows.Count > 0 AndAlso datesetcy.Tables(0).Rows(0).Item("isMulti") = False Then
            MsgBox("您已经插入此病例", MsgBoxStyle.Information + MsgBoxStyle.OkOnly, "提示:")
            Exit Sub
        Else
            If My_Row.Item("Mb_Nr") Is DBNull.Value Then
                MsgBox("模版尚未维护，请勿添加！", MsgBoxStyle.Information + MsgBoxStyle.OkOnly, "提示:")
                Exit Sub
            End If
           
            Dim emr_bl_mode As New ModelOld.M_Emr_Bl
            emr_bl_mode.Bl_Code = blmode.Bl_Code
            emr_bl_mode.Mb_Code = My_Row.Item("Mb_Code")
            emr_bl_mode.Mb_Name = My_Row.Item("Mb_name")

            emr_bl_mode.Mb_Nr = My_Row.Item("Mb_Nr")
            emr_bl_mode.Jsr_Code = HisVar.HisVar.JsrCode & ""
            emr_bl_mode.Ys_Code = HisVar.HisVar.JsrYsCode & ""
            emr_bl_mode.Ks_Code = HisVar.HisVar.XmKs & ""
            'emr_bl_mode.Print_Page = My_Row.Item("Print_Page")
            'emr_bl_mode.Print_Row = My_Row.Item("Print_Row")
            Dim V_Form As New ZtHis.Emr.EmrEditBl(emr_bl_mode, True)
            BaseFunc.BaseFunc.addTabControl(V_Form, blmode.Name & "-" & My_Row.Item("Mb_name"))
        End If
    End Sub

    Private Sub C1TrueDBGrid1_RowColChange(ByVal sender As System.Object, ByVal e As C1.Win.C1TrueDBGrid.RowColChangeEventArgs) Handles MyGrid1.RowColChange

        If (MyGrid1.Row + 1) > MyGrid1.RowCount Then
        Else
            My_Row = My_Cm.List(MyGrid1.Row).Row
            m_Rc.ChangeRow(My_Row)
        End If
    End Sub

    Private Sub MyButton2_Click(sender As System.Object, e As System.EventArgs) Handles MyButton2.Click
        Me.Close()
    End Sub

    Private Sub TyCheckBox_CheckedChanged(sender As Object, e As System.EventArgs) Handles TyCheckBox.CheckedChanged
        If TyCheckBox.Checked = True Then
            Tysql = "or mb_sex = '0' "
        Else
            Tysql = ""
        End If
        TreeView1_AfterSelect(Nothing, Nothing)
    End Sub


    Private Sub MbcxTextBox_TextChanged(sender As Object, e As System.EventArgs) Handles MbcxTextBox.TextChanged
        If MbcxTextBox.Text <> "" Then
            mbnamesql = " and  mb_name like '%" & MbcxTextBox.Text & "%'"
        Else
            mbnamesql = ""
        End If
        TreeView1_AfterSelect(Nothing, Nothing)
    End Sub

    Private Sub BCheckBox_CheckedChanged(sender As Object, e As System.EventArgs) Handles BCheckBox.CheckedChanged
        If BCheckBox.Checked = True Then
            boysql = "or mb_sex = '1' "
        Else
            boysql = ""
        End If
        TreeView1_AfterSelect(Nothing, Nothing)
    End Sub

    Private Sub GCheckBox_CheckedChanged(sender As Object, e As System.EventArgs) Handles GCheckBox.CheckedChanged
        If GCheckBox.Checked = True Then
            girlsql = "or mb_sex = '2' "
        Else
            girlsql = ""
        End If
        TreeView1_AfterSelect(Nothing, Nothing)
    End Sub

#End Region

   
  
End Class