﻿Public Class BedExchange


#Region "变量初始化"
    Private My_Table As New DataTable            '药品字典
    Private My_Cm As CurrencyManager             '同步指针
    'Private My_Row As DataRow                    '当 前 行
    'Private V_Insert As Boolean                  '增加记录

    Dim modelPatientInfo As ModelOld.M_PatientInfo
#End Region

    Public Sub New(ByVal patient As ModelOld.M_PatientInfo)

        ' 此调用是设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        modelPatientInfo = patient
    End Sub
    Private Sub BedAllocation_Load(sender As System.Object, e As System.EventArgs) Handles MyBase.Load
        Call Form_Init()
        Call Init_Data()
    End Sub


#Region "窗体初始化"

    Private Sub Form_Init()

        '初始化MyGrid1

        With MyGrid1
            .Init_Column("病区编码", "Bq_Code", 0, "左", "", False)
            .Init_Column("病区名称", "Bq_Name", 90, "中", "", False)
            .Init_Column("病床编码", "Bc_Code", 0, "中", "", False)
            .Init_Column("未使用病床", "Bc_Name", 85, "中", "", False)

        End With
        NameMyTextBox1.Focus()
    End Sub

    Private Sub Init_Data()
        Dim strSql As String = "select Bc_Code,Bc_Jc,Bq_Name,Bc_Name,Bc_Memo,Bq_Code from V_YyBc where " &
            "  Not exists (Select distinct Bc_Code From Bl Where V_YyBc.Bc_code=Bl.Bc_Code And " &
            " Ry_CyJsr is  null and isnull(Bc_Code,'')<>'' ) "
        My_Table = HisVar.HisVar.Sqldal.Query(strSql).Tables(0)
        With My_Table
            .PrimaryKey = New DataColumn() {.Columns("Bc_Code")}
        End With
        With Me.MyGrid1
            My_Cm = CType(BindingContext(My_Table, ""), CurrencyManager)
            .DataTable = My_Table
        End With

    End Sub


#End Region


#Region "控件动作"
    Private Sub NameMyTextBox1_TextChanged(sender As Object, e As System.EventArgs) Handles NameMyTextBox1.TextChanged
        Dim view As DataView = My_Cm.List
        view.RowFilter = "bc_jc like '%" & NameMyTextBox1.Text & "%' or bc_name like '%" & NameMyTextBox1.Text & "%'"
    End Sub

    Private Sub MyGrid1_DoubleClick(sender As Object, e As System.EventArgs) Handles MyGrid1.DoubleClick
        If MyGrid1.RowCount > 0 Then
            If MsgBox("您确定要把" & modelPatientInfo.Name & "换到" & MyGrid1.Columns("Bc_Name").Value & "号床吗？", MsgBoxStyle.OkCancel + MsgBoxStyle.DefaultButton1, "提示：") = MsgBoxResult.Ok Then
                HisVar.HisVar.Sqldal.ExecuteSql("update bl set bc_code='" &
            MyGrid1.Columns("bc_code").Value & "' where bl_code='" & modelPatientInfo.Bl_Code & "'")
                MyGrid1.Delete()
                Me.DialogResult = Windows.Forms.DialogResult.OK
            End If
           
            Me.Close()
        End If
    End Sub
#End Region




End Class