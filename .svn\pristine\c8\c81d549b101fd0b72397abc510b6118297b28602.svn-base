﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="ContextMenuStrip1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <metadata name="Image1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>607, 17</value>
  </metadata>
  <data name="Image1.ImageStream" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>
        ********************************************************************************
        LCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAACZTeXN0
        ZW0uV2luZG93cy5Gb3Jtcy5JbWFnZUxpc3RTdHJlYW1lcgEAAAAERGF0YQcCAgAAAAkDAAAADwMAAADO
        CAAAAk1TRnQBSQFMAgEBBAEAAdQBAAHUAQABEAEAARABAAT/ARkBAAj/AUIBTQE2BwABNgMAASgDAAFA
        AwABIAMAAQEBAAEYBgABGP8A/wD/AP8A/wD/AP8A/wD/AP8A/wD/AN4AKoCWAAOAJAADgGkAAWABSAEw
        AWABSAEwAWABSAEwAWABSAEwAWABSAEwAWABSAEwAWABSAEwAWABSAEwAWABSAEwAWABSAEwAWABSAEw
        AWABSAEwAWABSAEwBgADgAPADwADwA8ABoADACqADAAkgAYAAcABqAGgAf8C8AH/AfAB4AH/AfAB4AH/
        AegB4AH/AegB4AH/AegB4AH/AegB4AH/AegB0AH/AeAB0AH/AeAB0AH/AeAB0AH/AeAB0AH/AeAB0AFg
        AUgBMAYAA4ADwAOACQADwAEAAoABAAL/AQAC/wEAAv8BAAL/AwADgAMAA4AEAAL/A8ABAAL/A8ABAAL/
        A8ABAAL/A8ABAAL/A8ABAAL/A4AMAAOABAAC/wPAAQAC/wPAAQAC/wPAAQAC/wPAAQAC/wOABgABwAGo
        AaAB/wLwAdABmAGAAeABoAGAAfABoAGAAfABoAFwAfABmAFwAeABkAFgAeABiAFgAeABgAFQAeABgAFA
        AeABeAFAAeABgAFQAf8B4AHQAWABSAEwBgADgAPAA4AMAAPAAQACgAEAAv8BAAKAAQACgAMAA8ADAAOA
        AwADwAEAAv8DwAEAAv8DwAEAAv8DwAEAAv8DwAEAAv8DwAOACQADgAQAAv8DwAEAAv8DwAEAAv8DwAEA
        Av8DwAEAAv8DwAMAA4ADAAHAAbABoAH/AfgB8AHAAZABcAH/AcABoAHwAbABkAHwAagBgAHwAaABcAHw
        AZgBcAHgAZABYAHgAYABYAHQAXgBUAHQAXABUAHgAXABQAH/AeAB0AFgAUgBMAkAA4ADwAOACQADwAEA
        AoABAAKADMADAAOABAAC/wPAAQAC/wPAAQAC/wPAAQAC/wPAAQAC/wPAAQAC/wOACQADgAMAA8ABAAL/
        A8ABAAL/A8ABAAL/A8ABAAL/A8ADgAMAA4ADAAHAAbABoAH/AfgB8AHAAZABcAH/AcgBsAH/AegB4AH/
        AegB0AH/AeAB0AH/AeAB0AH/AdgB0AH/AdgBwAH/AdABwAHQAXgBUAHgAXABQAH/AeAB0AFgAUgBMAkA
        IYADAAOAAwADgAMAA8ABAAL/A8ABAAL/A8ABAAL/A8ABAAL/A8ABAAL/A8ADgAYAA4ADAAPAAQAC/wPA
        AQAC/wPAAQAC/wPAAQAC/wPAAQAC/wMABoADAAHAAbABoAH/AfgB8AHAAZABcAH/AdABsAH/AcgBoAH/
        AcABoAH/AbABkAHwAagBgAHwAaABgAHwAZgBcAHgAZABYAHgAYABYAHgAXABQAH/AeAB0AFgAUgBMDMA
        A4AEAAL/A8ABAAL/A8ABAAL/A8ABAAL/A8ABAAL/A8ABAAL/A4AGAAOAHgADgAMAA8ADgAMAAcABsAGg
        Af8B+AHwAcABkAFwAf8B0AHAAf8B8AHgAf8B6AHgAf8B6AHgAf8B6AHQAf8B4AHQAf8B4AHQAf8B2AHQ
        AeABkAFgAeABcAFAAf8B6AHQAWABSAEwMwADgAMAA8ABAAL/A8ABAAL/A8ABAAL/A8ABAAL/A8ABAAL/
        A8ADgAYAJ4ABAAL/A4ADAAHAAbABoAH/AfgB8AHAAZABcAH/AdgBwAH/AdABwAH/AcgBsAH/AcgBoAH/
        AcABoAH/AbgBkAHwAagBgAHwAaABcAHwAZgBcAHgAXABQAH/AegB4AFgAUgBMDMAA4AEAAL/A8ABAAL/
        A8ABAAL/A8ABAAL/A8ABAAL/A8ABAAL/A4AJAAOAAwADwAEAAv8DwAEAAv8DwAEAAv8DwAEAAv8DwAEA
        Av8DwAOAAwAB0AGwAaADAAHAAZABcAH/AeAB0AH/AvAB/wHwAeAB/wHwAeAB/wHAAaAB/wHAAaAB/wG4
        AZAB/wGwAZAB8AGgAXAB4AF4AUAB/wHoAeABYAFIATAzAAOAJAADgAkAA4AEAAL/A8ABAAL/A8ABAAL/
        A8APAAOAAwAB0AGwAaADAAHAAZABcAH/AeAB0AH/AdgB0AH/AdgBwAH/AdABsAH/AcgBsAH/AcABoAH/
        AcABoAH/AbgBkAH/AbABgAHgAXgBQAH/AegB4AFgAUgBMBUAAoABAAL/AQACgAEAAoATAAOAA8ABAAL/
        A8ABAAL/A8ABAAL/A8ASgAkAA4ADAAPAAQAC/wPAAQAC/wPAAwASgAMAAdABuAGgAwAB0AGgAYAB0AGQ
        AYABwAGQAXAB0AGIAXAB0AGIAWABwAGAAWABwAF4AVABwAFwAUABwAFoAUABwAFoAUAB0AF4AUAB/wHo
        AeABYAFIATAYAAKAAQACgBkAA4ADwAEAAv8DwAEAAv8DwAOAHgADgA8AA4AVAAHQAbgBoAwAAf8B+AHw
        Af8B+AHwAf8B+AHwAf8B+AHwAf8B+AHwAf8C8AH/AvAB/wHwAeAB/wHwAeABYAFIATAYAAKAAQACgBwA
        D4AkAA+AGwAB0AG4AaAB0AG4AaAB0AGwAaAB0AGwAaABwAGwAaABwAGwAaABwAGwAaABwAGwAaABwAGw
        AaABwAGoAaABwAGoAaABwAGoAaABwAGoAaAbAAKAqQABQgFNAT4HAAE+AwABKAMAAUADAAEgAwABAQEA
        AQEGAAEBFgAD/4IAAQMG/wEAAQEG/wF/AfkBgAEBAeABAAGAAQMDAAEBAcACAAEBAYwBAAFAAQEB0AIA
        AQEBjgEAAUABAQGgAgABAQHGAQABQAEBAaACAAEBAcABAAFAAQEBQAIAAQEB/wH7AUABAQF/AeABAAEB
        Af8B/QFAAQEDAAEBAf8B/QFAAQEBoAEAAUABAQH4AQ4BfwH5AaABfAFAAQEB/AEZAQABAwGgAYEBQAEB
        Af4BJwGAAf8B3wF/AXgBAQH+AR8BwQH/AeAB/wGAAQMB/gF/Bv8L
</value>
  </data>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>63</value>
  </metadata>
</root>