{"Version": 1, "WorkspaceRootPath": "D:\\开发\\his2010v3(长春)\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{CC45DF1D-6686-41A1-A2FC-F4462EF5E005}|AIMedicalAssistant\\AIMedicalAssistant.csproj|d:\\开发\\his2010v3(长春)\\aimedicalassistant\\forms\\apiconfigform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form", "RelativeMoniker": "D:0:0:{CC45DF1D-6686-41A1-A2FC-F4462EF5E005}|AIMedicalAssistant\\AIMedicalAssistant.csproj|solutionrelative:aimedicalassistant\\forms\\apiconfigform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form"}, {"AbsoluteMoniker": "D:0:0:{472BFECE-5FDB-4EB3-8973-F9734FAF896B}|ERX\\ERX.csproj|d:\\开发\\his2010v3(长春)\\erx\\form\\erxquery.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{472BFECE-5FDB-4EB3-8973-F9734FAF896B}|ERX\\ERX.csproj|solutionrelative:erx\\form\\erxquery.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{472BFECE-5FDB-4EB3-8973-F9734FAF896B}|ERX\\ERX.csproj|D:\\开发\\his2010v3(长春)\\erx\\form\\erxqyjgquery.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{472BFECE-5FDB-4EB3-8973-F9734FAF896B}|ERX\\ERX.csproj|solutionrelative:erx\\form\\erxqyjgquery.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 1, "Children": [{"$type": "Bookmark", "Name": "ST:128:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "ApiConfigForm.cs [设计]", "DocumentMoniker": "D:\\开发\\his2010v3(长春)\\AIMedicalAssistant\\Forms\\ApiConfigForm.cs", "RelativeDocumentMoniker": "AIMedicalAssistant\\Forms\\ApiConfigForm.cs", "ToolTip": "D:\\开发\\his2010v3(长春)\\AIMedicalAssistant\\Forms\\ApiConfigForm.cs [设计]", "RelativeToolTip": "AIMedicalAssistant\\Forms\\ApiConfigForm.cs [设计]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-17T12:59:11.987Z", "EditorCaption": " [设计]"}, {"$type": "Document", "DocumentIndex": 1, "Title": "ErxQuery.cs", "DocumentMoniker": "D:\\开发\\his2010v3(长春)\\ERX\\Form\\ErxQuery.cs", "RelativeDocumentMoniker": "ERX\\Form\\ErxQuery.cs", "ToolTip": "D:\\开发\\his2010v3(长春)\\ERX\\Form\\ErxQuery.cs", "RelativeToolTip": "ERX\\Form\\ErxQuery.cs", "ViewState": "AgIAAA8AAAAAAAAAAAAuwBEAAAAsAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-12T09:01:17.129Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "ERxQyJgQuery.cs", "DocumentMoniker": "D:\\开发\\his2010v3(长春)\\ERX\\Form\\ERxQyJgQuery.cs", "RelativeDocumentMoniker": "ERX\\Form\\ERxQyJgQuery.cs", "ToolTip": "D:\\开发\\his2010v3(长春)\\ERX\\Form\\ERxQyJgQuery.cs", "RelativeToolTip": "ERX\\Form\\ERxQyJgQuery.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-12T09:01:04.111Z", "EditorCaption": ""}]}]}]}