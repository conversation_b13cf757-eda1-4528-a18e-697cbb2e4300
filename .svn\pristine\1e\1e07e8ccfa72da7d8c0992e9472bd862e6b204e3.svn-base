﻿/**  版本信息模板在安装目录下，可自行修改。
* D_Bl_TWD1.cs
*
* 功 能： N/A
* 类 名： D_Bl_TWD1
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/6/23 15:47:22   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
namespace DAL
{
	/// <summary>
	/// 数据访问类:D_Bl_TWD1
	/// </summary>
	public partial class D_Bl_TWD1
	{
		public D_Bl_TWD1()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string CL_Code)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from Bl_TWD1");
			strSql.Append(" where CL_Code=@CL_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@CL_Code", SqlDbType.Char,10)			};
			parameters[0].Value = CL_Code;

			return HisVar.HisVar.Sqldal.Exists(strSql.ToString(),parameters);
		}

        public string MaxCode()
        {
            string Max = HisVar.HisVar.Sqldal.F_MaxCode("SELECT MAX(CL_Code) FROM dbo.Bl_TWD1", 10);
            return Max;
        }
		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ModelOld.M_Bl_TWD1 Model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into Bl_TWD1(");
			strSql.Append("Bl_Code,CL_Code,CL_Date,Height,Weight,ZRL,ZCL,YLL,DBCS,XBCS,XBL,GMYW1,PSJG,XY1,XY2,IsPregnant,Jsr_Code,Lr_Date)");
			strSql.Append(" values (");
			strSql.Append("@Bl_Code,@CL_Code,@CL_Date,@Height,@Weight,@ZRL,@ZCL,@YLL,@DBCS,@XBCS,@XBL,@GMYW1,@PSJG,@XY1,@XY2,@IsPregnant,@Jsr_Code,@Lr_Date)");
			SqlParameter[] parameters = {
					new SqlParameter("@Bl_Code", SqlDbType.Char,14),
					new SqlParameter("@CL_Code", SqlDbType.Char,10),
					new SqlParameter("@CL_Date", SqlDbType.SmallDateTime),
					new SqlParameter("@Height", SqlDbType.Int,4),
					new SqlParameter("@Weight", SqlDbType.Decimal,9),
					new SqlParameter("@ZRL", SqlDbType.Decimal,9),
					new SqlParameter("@ZCL", SqlDbType.Decimal,9),
					new SqlParameter("@YLL", SqlDbType.Decimal,9),
					new SqlParameter("@DBCS", SqlDbType.VarChar,50),
					new SqlParameter("@XBCS", SqlDbType.Int,4),
					new SqlParameter("@XBL", SqlDbType.Decimal,9),
					new SqlParameter("@GMYW1", SqlDbType.VarChar,50),
					new SqlParameter("@PSJG", SqlDbType.VarChar,50),
					new SqlParameter("@XY1", SqlDbType.VarChar,50),
					new SqlParameter("@XY2", SqlDbType.VarChar,50),
					new SqlParameter("@IsPregnant", SqlDbType.Bit,1),
					new SqlParameter("@Jsr_Code", SqlDbType.Char,7),
					new SqlParameter("@Lr_Date", SqlDbType.SmallDateTime)};
			parameters[0].Value = Model.Bl_Code;
			parameters[1].Value = Model.CL_Code;
			parameters[2].Value = Model.CL_Date;
            parameters[3].Value = Common.Tools.IsValueNull(Model.Height);
			parameters[4].Value = Model.Weight;
			parameters[5].Value = Model.ZRL;
			parameters[6].Value = Model.ZCL;
            if (Model.YLL==null)
            {
                parameters[7].Value = DBNull.Value;
            }
            else
            {
                parameters[7].Value = Model.YLL;
            }

			parameters[8].Value = Model.DBCS;
            if (Model.XBCS == null)
            {
                parameters[9].Value = DBNull.Value;
            }
            else
            {
                parameters[9].Value = Model.XBCS ;
            }
			parameters[10].Value = Model.XBL;
			parameters[11].Value = Model.GMYW1;
			parameters[12].Value = Model.PSJG;
			parameters[13].Value = Model.XY1;

            if (Model.XY2 == null)
            {
                parameters[14].Value = DBNull.Value;
            }
            else
            {
                parameters[14].Value = Model.XY2;
            }


			parameters[15].Value = Model.IsPregnant;
			parameters[16].Value = Model.Jsr_Code;
			parameters[17].Value = Model.Lr_Date;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ModelOld.M_Bl_TWD1 Model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update Bl_TWD1 set ");
			strSql.Append("Bl_Code=@Bl_Code,");
			strSql.Append("CL_Date=@CL_Date,");
			strSql.Append("Height=@Height,");
			strSql.Append("Weight=@Weight,");
			strSql.Append("ZRL=@ZRL,");
			strSql.Append("ZCL=@ZCL,");
			strSql.Append("YLL=@YLL,");
			strSql.Append("DBCS=@DBCS,");
			strSql.Append("XBCS=@XBCS,");
			strSql.Append("XBL=@XBL,");
			strSql.Append("GMYW1=@GMYW1,");
			strSql.Append("PSJG=@PSJG,");
			strSql.Append("XY1=@XY1,");
			strSql.Append("XY2=@XY2,");
			strSql.Append("IsPregnant=@IsPregnant,");
			strSql.Append("Jsr_Code=@Jsr_Code,");
			strSql.Append("Lr_Date=@Lr_Date");
			strSql.Append(" where CL_Code=@CL_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Bl_Code", SqlDbType.Char,14),
					new SqlParameter("@CL_Date", SqlDbType.SmallDateTime),
					new SqlParameter("@Height", SqlDbType.Int,4),
					new SqlParameter("@Weight", SqlDbType.Decimal,9),
					new SqlParameter("@ZRL", SqlDbType.Decimal,9),
					new SqlParameter("@ZCL", SqlDbType.Decimal,9),
					new SqlParameter("@YLL", SqlDbType.Decimal,9),
					new SqlParameter("@DBCS", SqlDbType.VarChar,50),
					new SqlParameter("@XBCS", SqlDbType.Int,4),
					new SqlParameter("@XBL", SqlDbType.Decimal,9),
					new SqlParameter("@GMYW1", SqlDbType.VarChar,50),
					new SqlParameter("@PSJG", SqlDbType.VarChar,50),
					new SqlParameter("@XY1", SqlDbType.VarChar,50),
					new SqlParameter("@XY2", SqlDbType.VarChar,50),
					new SqlParameter("@IsPregnant", SqlDbType.Bit,1),
					new SqlParameter("@Jsr_Code", SqlDbType.Char,7),
					new SqlParameter("@Lr_Date", SqlDbType.SmallDateTime),
					new SqlParameter("@CL_Code", SqlDbType.Char,10)};
			parameters[0].Value = Model.Bl_Code;
			parameters[1].Value = Model.CL_Date;
			parameters[2].Value = Model.Height;
			parameters[3].Value = Model.Weight;
			parameters[4].Value = Model.ZRL;
			parameters[5].Value = Model.ZCL;
            if (Model.YLL == null)
            {
                parameters[6].Value = DBNull.Value;
            }
            else
            {
                parameters[6].Value = Model.YLL;
            }

            parameters[7].Value = Model.DBCS;
            if (Model.XBCS == null)
            {
                parameters[8].Value = DBNull.Value;
            }
            else
            {
                parameters[8].Value = Model.XBCS;
            }
            parameters[9].Value = Model.XBL;
            parameters[10].Value = Model.GMYW1;
            parameters[11].Value = Model.PSJG;
            parameters[12].Value = Model.XY1;

            if (Model.XY2 == null)
            {
                parameters[13].Value = DBNull.Value;
            }
            else
            {
                parameters[13].Value = Model.XY2;
            }
			parameters[14].Value = Model.IsPregnant;
			parameters[15].Value = Model.Jsr_Code;
			parameters[16].Value = Model.Lr_Date;
			parameters[17].Value = Model.CL_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string CL_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Bl_TWD1 ");
			strSql.Append(" where CL_Code=@CL_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@CL_Code", SqlDbType.Char,10)			};
			parameters[0].Value = CL_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string CL_Codelist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Bl_TWD1 ");
			strSql.Append(" where CL_Code in ("+CL_Codelist + ")  ");
			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Bl_TWD1 GetModel(string CL_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 Bl_Code,CL_Code,CL_Date,Height,Weight,ZRL,ZCL,YLL,DBCS,XBCS,XBL,GMYW1,PSJG,XY1,XY2,IsPregnant,Jsr_Code,Lr_Date from Bl_TWD1 ");
			strSql.Append(" where CL_Code=@CL_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@CL_Code", SqlDbType.Char,10)			};
			parameters[0].Value = CL_Code;

			ModelOld.M_Bl_TWD1 Model=new ModelOld.M_Bl_TWD1();
			DataSet ds=HisVar.HisVar.Sqldal.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Bl_TWD1 DataRowToModel(DataRow row)
		{
			ModelOld.M_Bl_TWD1 Model=new ModelOld.M_Bl_TWD1();
			if (row != null)
			{
				if(row["Bl_Code"]!=null)
				{
					Model.Bl_Code=row["Bl_Code"].ToString();
				}
				if(row["CL_Code"]!=null)
				{
					Model.CL_Code=row["CL_Code"].ToString();
				}
				if(row["CL_Date"]!=null && row["CL_Date"].ToString()!="")
				{
					Model.CL_Date=DateTime.Parse(row["CL_Date"].ToString());
				}
				if(row["Height"]!=null && row["Height"].ToString()!="")
				{
					Model.Height=int.Parse(row["Height"].ToString());
				}
				if(row["Weight"]!=null && row["Weight"].ToString()!="")
				{
					Model.Weight=decimal.Parse(row["Weight"].ToString());
				}
				if(row["ZRL"]!=null && row["ZRL"].ToString()!="")
				{
					Model.ZRL=decimal.Parse(row["ZRL"].ToString());
				}
				if(row["ZCL"]!=null && row["ZCL"].ToString()!="")
				{
					Model.ZCL=decimal.Parse(row["ZCL"].ToString());
				}
				if(row["YLL"]!=null && row["YLL"].ToString()!="")
				{
					Model.YLL=decimal.Parse(row["YLL"].ToString());
				}
				if(row["DBCS"]!=null)
				{
					Model.DBCS=row["DBCS"].ToString();
				}
				if(row["XBCS"]!=null && row["XBCS"].ToString()!="")
				{
					Model.XBCS=int.Parse(row["XBCS"].ToString());
				}
				if(row["XBL"]!=null && row["XBL"].ToString()!="")
				{
					Model.XBL=decimal.Parse(row["XBL"].ToString());
				}
				if(row["GMYW1"]!=null)
				{
					Model.GMYW1=row["GMYW1"].ToString();
				}
				if(row["PSJG"]!=null)
				{
					Model.PSJG=row["PSJG"].ToString();
				}
				if(row["XY1"]!=null)
				{
					Model.XY1=row["XY1"].ToString();
				}
				if(row["XY2"]!=null)
				{
					Model.XY2=row["XY2"].ToString();
				}
				if(row["IsPregnant"]!=null && row["IsPregnant"].ToString()!="")
				{
					if((row["IsPregnant"].ToString()=="1")||(row["IsPregnant"].ToString().ToLower()=="true"))
					{
						Model.IsPregnant=true;
					}
					else
					{
						Model.IsPregnant=false;
					}
				}
				if(row["Jsr_Code"]!=null)
				{
					Model.Jsr_Code=row["Jsr_Code"].ToString();
				}
				if(row["Lr_Date"]!=null && row["Lr_Date"].ToString()!="")
				{
					Model.Lr_Date=DateTime.Parse(row["Lr_Date"].ToString());
				}
			}
			return Model;
		}

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("   select Bl_Code, CL_Code, CL_Date, Height, Weight, ZRL, ZCL, YLL, DBCS, XBCS, XBL, GMYW1,");
            strSql.Append("    PSJG, XY1, XY2, IsPregnant, Jsr_Code, Lr_Date  FROM dbo.Bl_TWD1");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }



        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetListOfPersonalInformation(string bl,string clDate)
        {
            StringBuilder strSql = new StringBuilder();

            strSql.Append(" SELECT bl.Bl_Code, Ry_Name ,Ry_Jc,Ry_Sex,Ry_Sfzh,Ry_Csdate,CASE WHEN ISNULL(Ry_Csdate,'')<>''  ");
            strSql.Append("    THEN DATEDIFF(YEAR,Ry_Csdate,GETDATE()) ELSE '' END AGE,Bl.Ks_Code, ");
            strSql.Append("   Ks_Name ,Ry_RyDate ,operationTime ,Bl.Bc_Code , Bc_Name,Bq_Code,Bq_Name  ");
            strSql.Append("    FROM dbo.Bl LEFT join V_YyBc on   Bl.Bc_Code=V_YyBc.Bc_Code JOIN dbo.Zd_YyKs ON Zd_YyKs.Ks_Code = Bl.Ks_Code  ");
            strSql.Append("   LEFT JOIN  (SELECT Bl_Code,MAX(EventTime) operationTime FROM  ");
            strSql.Append("    dbo.Bl_TWD1 LEFT JOIN dbo.Bl_TWD2 ON Bl_TWD2.CL_Code = Bl_TWD1.CL_Code ");
            strSql.Append("  WHERE Event='手术' AND CL_Date<='"+clDate +"'");
            strSql.Append("   GROUP BY Bl_Code) tw ON  tw.Bl_Code = Bl.Bl_Code  WHERE ISNULL(Ry_CyDate,'')=''  ");
            strSql.Append(" and Bl.Bl_Code='"+bl+"'");
        
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }
        

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" Bl_Code,CL_Code,CL_Date,Height,Weight,ZRL,ZCL,YLL,DBCS,XBCS,XBL,GMYW1,PSJG,XY1,XY2,IsPregnant,Jsr_Code,Lr_Date ");
			strSql.Append(" FROM Bl_TWD1 ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM Bl_TWD1 ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = HisVar.HisVar.Sqldal .GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.CL_Code desc");
			}
			strSql.Append(")AS Row, T.*  from Bl_TWD1 T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Bl_TWD1";
			parameters[1].Value = "CL_Code";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return HisVar.HisVar.Sqldal.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

