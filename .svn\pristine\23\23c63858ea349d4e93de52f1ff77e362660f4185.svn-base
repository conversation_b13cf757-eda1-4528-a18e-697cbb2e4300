﻿Imports System.Data.SqlClient
Imports HisControl
Imports BaseClass

Public Class Cq_Cf34

#Region "定义变量"

    Dim V_Mx_Code As String = ""                '药品__明细编码
    Dim V_Yp_Code As String                     '药品编码
    Dim My_DataSet As New DataSet
#End Region

#Region "传参"
    Dim Rform As BaseForm
    Dim Rinsert As Boolean
    'Dim Rdate As Date
    Dim Rrow As DataRow
    Dim RZbtb As DataTable
    Dim Rtdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Dim Rlb As Label
    Dim Rzbadt As SqlDataAdapter
    Dim Rds As DataSet
    Dim Rcode As String
    Dim Rrc As C_RowChange
    Dim RBl_Code As String

#End Region

    Public Sub New(ByVal tform As BaseForm, ByVal tinsert As Boolean, ByVal trow As DataRow, ByVal tZbtb As DataTable, ByVal tlb As Label, ByVal ttdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid, ByVal tzbadt As SqlDataAdapter, ByVal tds As DataSet, ByVal tcode As String, ByVal tBl_Code As String)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        Rform = tform
        Rinsert = tinsert
        'Rdate = tdate
        Rrow = trow
        RZbtb = tZbtb
        Rtdbgrid = ttdbgrid
        Rlb = tlb
        Rzbadt = tzbadt
        Rds = tds
        Rcode = tcode

        ' 在 InitializeComponent() 调用之后添加任何初始化。

        RBl_Code = tBl_Code

    End Sub

    Protected Overrides Sub OnKeyDown(ByVal e As System.Windows.Forms.KeyEventArgs)
        MyBase.OnKeyDown(e)
        If e.Control = True And e.KeyCode = Keys.S Then
            Comm1.Select()
            Call Comm_Click(Comm1, Nothing)
        End If
    End Sub

    Private Sub Cq_Cf31_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        With Rform
            Call Form_Init()
            Me.Location = New Point(.Left + (.Width - Me.Width) / 2, .Top + (.Height - Me.Height))
            If Rinsert = True Then Call Data_Clear() Else Call Data_Show(Rrow)
        End With
    End Sub

#Region "窗体__事件"

    Private Sub Form_Init()

        '按扭初始化
        Call P_Comm(Me.Comm1)
        Call P_Comm(Me.Comm2)


        With Me.C1Numeric1
            .Value = 1
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .CustomFormat = "###,###,###.####"
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .ShowContextMenu = False
            .EmptyAsNull = False
        End With

        With Me.C1Numeric2
            .Value = 0
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .CustomFormat = "###,###,##0.00##"
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .ShowContextMenu = False
            .EmptyAsNull = False
        End With

        With Me.C1Numeric4
            .Value = 0
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .CustomFormat = "###,###,##0.00##"
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .ShowContextMenu = False
            .EmptyAsNull = False
        End With

        If Rinsert = True Then
            HisVar.HisVar.Sqldal.QueryDt(My_DataSet, "SELECT Xm_Jc,Xm_Name,Xm_Code,Xm_Dj,Xmlb_Name,Xm_Dw,'诊疗字典'as Lb  FROM Zd_Ml_Xm1,Zd_Ml_Xm3 where Zd_Ml_Xm1.Xmlb_Code=Zd_Ml_Xm3.Xmlb_Code And Xm_Dj<>0  union all SELECT Templet_Jc Xm_Jc,Templet_Name Xm_Name,Zd_Templet1.Templet_Code Xm_Code,sum(Xm_Dj*mx_sl)  Xm_Dj,''as Xmlb_Name,'' as Xm_Dw,'诊疗模板'as Lb  FROM Zd_Templet1,Zd_Templet2,Zd_Ml_Xm3 where  Zd_Templet1.Templet_Code=Zd_Templet2.Templet_Code and Zd_Templet2.Mx_Code=Zd_ML_Xm3.Xm_Code  group by Zd_Templet1.Templet_Code, Templet_Jc,Templet_name order by Xm_Jc", "总项目", True)
        Else
            HisVar.HisVar.Sqldal.QueryDt(My_DataSet, "SELECT Xm_Jc,Xm_Name,Xm_Code,Xm_Dj,Xmlb_Name,Xm_Dw,'诊疗字典'as Lb  FROM Zd_Ml_Xm1,Zd_Ml_Xm3 where Zd_Ml_Xm1.Xmlb_Code=Zd_Ml_Xm3.Xmlb_Code And Xm_Dj<>0   order by Xm_Jc", "总项目", True)
        End If

        Dim My_Combo As BaseClass.C_Combo2 = New BaseClass.C_Combo2(C1Combo1, My_DataSet.Tables("总项目").DefaultView, "Xm_Name", "Xm_Code", 400)
        With My_Combo
            .Init_TDBCombo()
            .Init_Colum("Xm_Jc", "简称", 100, "左")
            .Init_Colum("Xm_Name", "项目名称", 150, "左")
            .Init_Colum("Xm_Dj", "单价", 50, "中")
            .Init_Colum("Xm_Dw", "单位", 50, "中")
            .Init_Colum("Xm_Code", "", 0, "中")
            .Init_Colum("Xmlb_Name", "", 0, "中")
            .Init_Colum("Lb", "类别", 0, "中")
            .MaxDropDownItems(15)
            .SelectedIndex(-1)
        End With
        C1Combo1.AutoCompletion = False
        C1Combo1.AutoSelect = False
        '草药字典视图
    End Sub

#End Region

#Region "其它项目"


    Private Sub C1Numeric_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles C1Numeric1.KeyPress, C1Numeric2.KeyPress
        If e.KeyChar <> Chr(Keys.Return) Then Exit Sub
        e.Handled = True
        System.Windows.Forms.SendKeys.Send("{Tab}")
    End Sub

    Private Sub C1Numeric1_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1Numeric1.Validated, C1Numeric2.Validated
        C1Numeric4.Value = C1Numeric1.Value * C1Numeric2.Value
    End Sub

#End Region

#Region "控件__动作"

    Private Sub Comm_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.Click, Comm2.Click
        Select Case sender.tag
            Case "保存"

                If C1Combo1.SelectedValue = "" Then
                    Beep()
                    MsgBox("项目编码不能为空,按任意键返回!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    C1Combo1.Select()
                    Exit Sub
                End If
                If C1Numeric1.Value <= 0 Then
                    Beep()
                    MsgBox("使用数量应大于零！", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示")
                    C1Numeric1.Select()
                    Exit Sub
                End If

                If Rinsert = True Then      '增加记录
                    Call Save_Add()
                Else                                '编辑记录
                    Call Save_Edit()
                End If

            Case "取消"
                C1Combo1.SelectedValue = -1
                C1Combo1.Text = ""
                Rtdbgrid.Focus()
                Me.Close()
        End Select
    End Sub

    Private Sub C1Move_Click(ByVal sender As Object, ByVal e As C1.Win.C1Command.ClickEventArgs) Handles Move2.Click, Move3.Click, Move5.Click
        If sender.text = "新增" Then
            Rinsert = True
            Call Data_Clear()
        Else
            Rinsert = False
            Select Case sender.text
                Case "上移"
                    Rtdbgrid.MovePrevious()
                Case "下移"
                    Rtdbgrid.MoveNext()
            End Select
        End If

    End Sub

#Region "C1Combo1"

    Private Sub C1Combo1_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1Combo1.Validated
        Me.CancelButton = Comm2
    End Sub

    Private Sub C1Combo1_RowChange(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1Combo1.RowChange

        If C1Combo1.WillChangeToValue = "" Then
            Label1.Text = ""
            C1Numeric2.Value = 0
            V_Mx_Code = ""
        Else
            If HisPara.PublicConfig.ZyXmDj_Change = "是" And C1Combo1.Columns("lb").Value = "诊疗字典" Then
                C1Numeric2.ReadOnly = False
                C1Numeric2.BackColor = Color.White
                C1Numeric2.TabStop = True
            Else
                C1Numeric2.ReadOnly = True
                C1Numeric2.BackColor = SystemColors.Info
                C1Numeric2.TabStop = False
            End If

            V_Mx_Code = C1Combo1.Columns("Xm_Code").Value
            Label1.Text = C1Combo1.Columns("Xmlb_Name").Value
            C1Numeric2.Value = C1Combo1.Columns("Xm_Dj").Value
        End If
    End Sub

    Private Sub C1Combo1_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles C1Combo1.KeyUp

        If (e.KeyValue > 47 And e.KeyValue < 58) Or (e.KeyValue > 96 And e.KeyValue < 123) Or (e.KeyValue > 64 And e.KeyValue < 91) Or (e.KeyValue = 8) Or (e.KeyValue = 189) Or (e.KeyValue = 190) Then
            Dim s As String = C1Combo1.Text
            If C1Combo1.Text = "" Then
                C1Combo1.DataSource.RowFilter = "1=1"
            Else
                C1Combo1.DataSource.RowFilter = "Xm_Jc like '*" & C1Combo1.Text.Replace("*", "[*]") & "*'"
            End If

            If (e.KeyValue = 8) Then
                C1Combo1.DroppedDown = False
                C1Combo1.DroppedDown = True
            End If

            C1Combo1.Text = s
            C1Combo1.SelectionStart = C1Combo1.Text.Length
            C1Combo1.SelectionLength = 0
        End If

    End Sub

    Private Sub C1Combo1_KeyDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles C1Combo1.KeyDown
        If e.KeyValue = 13 Then
            If C1Combo1.WillChangeToIndex < 1 Then
                If (CType(C1Combo1.DataSource, DataView).Count) = 0 Then
                    MsgBox("项目: '" + Me.C1Combo1.Text + "' 不存在！", MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
                    System.Windows.Forms.SendKeys.Send("^{A}")
                    Exit Sub
                End If
                C1Combo1.SelectedIndex = -1
                C1Combo1.SelectedIndex = 0
            Else
                Dim index As Integer
                index = C1Combo1.WillChangeToIndex
                C1Combo1.SelectedIndex = -1
                C1Combo1.SelectedIndex = index
            End If
            e.Handled = True
            System.Windows.Forms.SendKeys.Send("{Tab}")
        End If
    End Sub

#End Region

#End Region

#Region "数据__编辑"

    Private Sub Data_Clear()
        Rinsert = True
        If Move5.Enabled = True Then Move5.Enabled = False
        V_Mx_Code = ""                                          '药品编码

        C1Combo1.SelectedValue = -1
        Label1.Text = ""
        C1Combo1.Enabled = True
        C1Combo1.Text = ""                                      '药品明细编码
        C1Numeric1.Value = 1                                    '采购数量
        C1Numeric2.Value = 0                                    '采购单价
        C1Numeric4.Value = 0                                    '采购金额

        C1Combo1.Select()

    End Sub

    Private Sub Data_Show(ByVal tmp_Row As DataRow)
        Rinsert = False
        C1Combo1.DataSource.RowFilter = ""
        If Move5.Enabled = False Then Move5.Enabled = True
        T_Label2.Text = CStr((Rtdbgrid.Row) + 1)
        Rrow = tmp_Row
        With Rrow
            V_Mx_Code = .Item("Xm_Code") & ""
            '药品名称
            C1Combo1.SelectedValue = V_Mx_Code
            C1Numeric1.Value = .Item("Cf_Sl")                   '采购数量
            C1Numeric2.Value = .Item("Cf_Dj") & ""
            C1Numeric4.Value = .Item("Cf_Money")                '采购金额
        End With

    End Sub

    Private Sub Save_Add()

        If C1Combo1.Columns("lb").Value = "诊疗字典" Then
            Dim My_NewRow As DataRow = RZbtb.NewRow
            With My_NewRow
                .BeginEdit()
                .Item("AutoCf_Code") = Rcode
                .Item("Xm_Code") = V_Mx_Code                        '药品明细编码
                .Item("Xm_Dw") = C1Combo1.Columns("Xm_Dw").Value
                .Item("Cf_Sl") = C1Numeric1.Value                   '采购单价
                .Item("Cf_Dj") = C1Numeric2.Value                   '采购单价
                .Item("Cf_Money") = C1Numeric1.Value * C1Numeric2.Value                '采购金额
                .Item("Cf_Lb") = Me.Label1.Text
                .Item("Xm_Name") = Trim(C1Combo1.Text & "")
                .EndEdit()
            End With

            With Rzbadt.InsertCommand
                Try
                    .Parameters(0).Value = HisVar.HisVar.WsyCode
                    .Parameters(1).Value = Rcode
                    .Parameters(2).Value = My_NewRow.Item("Xm_Code") & ""
                    .Parameters(3).Value = My_NewRow.Item("Cf_Sl")
                    .Parameters(4).Value = My_NewRow.Item("Cf_Dj")
                    .Parameters(5).Value = My_NewRow.Item("Cf_Money")
                    .Parameters(6).Value = My_NewRow.Item("Cf_Lb")

                    Call P_Conn(True)
                    .ExecuteNonQuery()
                    Call P_Conn(False)

                    RZbtb.Rows.Add(My_NewRow)
                    My_NewRow.AcceptChanges()
                Catch ex As Exception
                    Beep()
                    MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
                End Try
            End With
        Else
            Dim My_Reader As SqlDataReader = HisVar.HisVar.Sqldal.ExecuteReader("Select Xm_Code,Xm_Dw,Mx_Sl,Xm_Name,Xm_Dj,Xmlb_Name From Zd_Templet2,Zd_Ml_Xm3,Zd_Ml_Xm1   where Zd_Templet2.Mx_Code=Zd_Ml_Xm3.Xm_Code and Zd_Ml_Xm1.Xmlb_Code=Zd_Ml_Xm3.Xmlb_Code and Templet_Code='" & C1Combo1.SelectedValue & "' ")
            While My_Reader.Read
                Dim My_NewRow As DataRow = RZbtb.NewRow
                With My_NewRow
                    .BeginEdit()
                    .Item("AutoCf_Code") = Rcode
                    .Item("Xm_Code") = My_Reader.Item("Xm_Code")                       '药品明细编码
                    .Item("Xm_Dw") = My_Reader.Item("Xm_Dw")
                    .Item("Cf_Sl") = My_Reader.Item("Mx_Sl") * C1Numeric1.Value                   '采购单价
                    .Item("Cf_Dj") = My_Reader.Item("Xm_Dj")                   '采购单价
                    .Item("Cf_Money") = My_Reader.Item("Mx_Sl") * C1Numeric1.Value * My_Reader.Item("Xm_Dj")                 '采购金额
                    .Item("Cf_Lb") = My_Reader.Item("Xmlb_Name")
                    .Item("Xm_Name") = Trim(My_Reader.Item("Xm_Name") & "")
                    .EndEdit()
                End With

                With Rzbadt.InsertCommand
                    Try

                        .Parameters(0).Value = HisVar.HisVar.WsyCode
                        .Parameters(1).Value = Rcode
                        .Parameters(2).Value = My_NewRow.Item("Xm_Code") & ""
                        .Parameters(3).Value = My_NewRow.Item("Cf_Sl")
                        .Parameters(4).Value = My_NewRow.Item("Cf_Dj")
                        .Parameters(5).Value = My_NewRow.Item("Cf_Money")
                        .Parameters(6).Value = My_NewRow.Item("Cf_Lb")
                        Call P_Conn(True)
                        .ExecuteNonQuery()
                        Call P_Conn(False)

                        RZbtb.Rows.Add(My_NewRow)
                        My_NewRow.AcceptChanges()
                    Catch ex As Exception
                        Beep()
                        MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
                    End Try
                End With

            End While

        End If

        Rtdbgrid.MoveLast()
        Call Data_Clear()

    End Sub

    Private Sub Save_Edit()
       
        With Rrow
            Try
                .BeginEdit()
                .Item("AutoCf_Code") = Rcode
                .Item("Xm_Code") = V_Mx_Code                        '药品明细编码
                .Item("Xm_Dw") = C1Combo1.Columns("Xm_Dw").Value
                .Item("Cf_Sl") = C1Numeric1.Value                   '采购单价
                .Item("Cf_Dj") = C1Numeric2.Value                   '采购单价
                .Item("Cf_Money") = C1Numeric1.Value * C1Numeric2.Value                '采购金额
                .Item("Cf_Lb") = Me.Label1.Text
                .Item("Xm_Name") = Trim(C1Combo1.Text & "")
                .EndEdit()
            Catch ex As Exception
                Beep()
                MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            End Try
        End With

        With Rzbadt.UpdateCommand

            .Parameters(0).Value = Rrow.Item("Xm_Code")
            .Parameters(1).Value = Rrow.Item("Cf_Sl")
            .Parameters(2).Value = Rrow.Item("Cf_Dj")
            .Parameters(3).Value = Rrow.Item("Cf_Money")
            .Parameters(4).Value = Rrow.Item("Cf_Lb")
            .Parameters(5).Value = Rrow.Item("Cf_Id", DataRowVersion.Original)


            Try
                Call P_Conn(True)
                .ExecuteNonQuery()
                Call P_Conn(False)
                Rrow.AcceptChanges()
            Catch ex As Exception
                Beep()
                MsgBox(ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
            End Try
        End With

    End Sub

#End Region

#Region "鼠标__外观"

    Private Sub P_Comm(ByVal Bt As Button)

        With Bt
            Select Case .Tag
                Case "保存"
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")

                Case "取消"
                    .Left = Me.Comm1.Left + Me.Comm1.Width + 1
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
                    .Width = Me.Comm1.Width
            End Select

            .Text = ""
            .FlatStyle = FlatStyle.Flat
            .FlatAppearance.BorderSize = 0
            .BackgroundImageLayout = ImageLayout.Center
        End With

    End Sub

    Private Sub Comm_MouseEnter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.MouseEnter, Comm2.MouseEnter
        Select Case sender.tag
            Case "保存"
                Me.Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存2")
                Me.Comm1.Cursor = Cursors.Hand

            Case "取消"
                Me.Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消2")
                Me.Comm2.Cursor = Cursors.Hand

        End Select

    End Sub

    Private Sub Comm_MouseLeave(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.MouseLeave, Comm2.MouseLeave
        Select Case sender.tag

            Case "保存"
                Me.Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")
                Me.Comm1.Cursor = Cursors.Default

            Case "取消"
                Me.Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
                Me.Comm2.Cursor = Cursors.Default

        End Select

    End Sub

    Private Sub Comm_MouseDown(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm1.MouseDown, Comm2.MouseDown
        Select Case sender.tag
            Case "保存"
                Me.Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存3")

            Case "取消"
                Me.Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消3")
        End Select
    End Sub

    Private Sub Comm_MouseUp(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm1.MouseUp, Comm2.MouseUp
        Select Case sender.tag
            Case "保存"
                Me.Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存2")

            Case "取消"
                Me.Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消2")
        End Select
    End Sub


#End Region

#Region "输入法设置"

    Private Sub C1Numeric1_KeyDown(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Combo1.GotFocus, C1Numeric1.GotFocus
        InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages(0)
    End Sub

#End Region

End Class