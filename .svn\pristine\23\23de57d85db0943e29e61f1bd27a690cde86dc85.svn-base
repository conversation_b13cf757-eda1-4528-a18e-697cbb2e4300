﻿Imports System.Data.SqlClient
Imports HisControl
Imports BaseClass
Imports BLL

Public Class Zy_Cf31

#Region "定义变量"

    Dim V_Xx_Code As String = ""                '药品__明细编码

    Dim V_Yp_Code As String                     '药品编码
    Dim My_DataSet As New DataSet
    Dim V_Ypcsl As Double '修改药品时的差额
    Dim Xm_Sum As Double = 0

    Dim V_Yf_Code As String

    Dim YpDt As DataTable
    ''' <summary>
    ''' 是否负记录
    ''' </summary>
    Dim _fcf As Boolean = False
    Dim listTfCfid As String
    Dim count As Integer = 4
#End Region

#Region "传参"
    Dim Rform As BaseForm
    Dim Rinsert As Boolean
    Dim Rrow As DataRow
    Dim RZbtb As DataTable
    Dim Rtdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Dim Rlb As Label
    Dim Rzbadt As SqlDataAdapter
    Dim Rds As DataSet
    Dim Rcode As String
    Dim RBl_Code As String
    Dim RSyje As Double '剩余金额
#End Region

    Public Sub New(ByVal tform As BaseForm, ByVal tinsert As Boolean, ByVal trow As DataRow, ByVal tZbtb As DataTable, ByVal tlb As Label, ByVal ttdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid, ByVal tzbadt As SqlDataAdapter, ByVal tds As DataSet, ByVal tcode As String, ByVal tyfcode As String, ByVal tBl_Code As String, ByVal tSyJe As Double)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        Rform = tform
        Rinsert = tinsert
        'Rdate = tdate
        Rrow = trow
        RZbtb = tZbtb
        Rtdbgrid = ttdbgrid
        Rlb = tlb
        Rzbadt = tzbadt
        Rds = tds
        Rcode = tcode

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        V_Yf_Code = tyfcode

        RBl_Code = tBl_Code
        RSyje = tSyJe
    End Sub

    Protected Overrides Sub OnKeyDown(ByVal e As System.Windows.Forms.KeyEventArgs)
        MyBase.OnKeyDown(e)
        If e.Control = True And e.KeyCode = Keys.S Then
            Comm1.Select()
            Call Comm_Click(Comm1, Nothing)
        End If
    End Sub

    Private Sub Yf_Ck31_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        If HisPara.PublicConfig.ZyYsz <> "是" Then
            C1Combo_Yfyl1.TabStop = False
            C1Combo_Yfyl2.TabStop = False
            C1Combo_Yfyl3.TabStop = False
            C1Combo_Yfyl4.TabStop = False
        Else
            C1Combo_Yfyl1.TabStop = True
            C1Combo_Yfyl2.TabStop = True
            C1Combo_Yfyl3.TabStop = True
            C1Combo_Yfyl4.TabStop = True
        End If

        If Rds.Tables("诊疗项目") IsNot Nothing Then
            Xm_Sum = IIf(Rds.Tables("诊疗项目").Compute("Sum(Cf_Money)", "") Is DBNull.Value, 0, Rds.Tables("诊疗项目").Compute("Sum(Cf_Money)", ""))
        End If
        With Rform
            Call Data_Init()
            Call Form_Init()
            Call Combo_Init()    '调取用法用量
            CheckBox1.Checked = False
            Me.Location = New Point(.Left + (.Width - Me.Width) / 2, .Top + (.Height - Me.Height))
            If Rinsert = True Then Call Data_Clear() Else Call Data_Show(Rrow)
        End With

    End Sub

#Region "窗体__事件"

    Private Sub Data_Init()
        ComboMzZyYp1.Init(V_Yf_Code)
        YpDt = ComboMzZyYp1.DataView.Table
        YpDt.PrimaryKey = New DataColumn() {YpDt.Columns("Xx_Code")}
        YpDt.Columns("Yf_Sl").ReadOnly = False
        ComboMzZyYp1.RowFilterTextNull = "Yf_Sl>0"
    End Sub

    Private Sub Form_Init()

        '按扭初始化
        Call P_Comm(Me.Comm1)
        Call P_Comm(Me.Comm2)


        With Me.C1Numeric1
            .Value = 0
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .CustomFormat = "###,###,###.####"
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .ShowContextMenu = False
            .EmptyAsNull = False
        End With

        With Me.C1Numeric2
            .Value = 0
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .CustomFormat = "###,###,##0.00####"
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .ShowContextMenu = False
            .EmptyAsNull = False
        End With

        With Me.C1Numeric4
            .Value = 0
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .CustomFormat = "###,###,##0.00##"
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .ShowContextMenu = False
            .EmptyAsNull = False
        End With

    End Sub

#End Region

#Region "其它项目"
    Private Sub C1Combo_Yfyl3_GotFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1Combo_Yfyl3.GotFocus
        C1Combo_Yfyl3.OpenCombo()
    End Sub

    Private Sub C1Combo_Yfyl4_GotFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1Combo_Yfyl4.GotFocus
        C1Combo_Yfyl4.OpenCombo()
    End Sub

    Private Sub C1Combo_Yfyl2_GotFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1Combo_Yfyl2.GotFocus
        C1Combo_Yfyl2.OpenCombo()
    End Sub

    Private Sub C1Combo_Yfyl1_GotFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1Combo_Yfyl1.GotFocus
        C1Combo_Yfyl1.OpenCombo()
    End Sub

    Private Sub C1Numeric_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles C1Numeric1.KeyPress
        If e.KeyChar <> Chr(Keys.Return) Then Exit Sub
        If HisPara.PublicConfig.ZyYsz = "是" And ComboMzZyYp1.Columns("Dl_Name").Value <> "卫生材料" Then
            C1Combo_Yfyl3.Select()
        Else
            Comm1.Select()
        End If

    End Sub

    Private Sub C1Combo2_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles C1Combo_Yfyl3.KeyPress, C1Combo_Yfyl2.KeyPress, C1Combo_Yfyl1.KeyPress, C1Combo_Yfyl4.KeyPress, C1TextBox1.KeyPress
        If e.KeyChar <> Chr(Keys.Return) Then Exit Sub
        e.Handled = True
        System.Windows.Forms.SendKeys.Send("{Tab}")
    End Sub


    Private Sub C1Numeric1_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1Numeric1.Validated
        If _fcf = False Then
            C1Numeric4.Value = C1Numeric1.Value * C1Numeric2.Value
        End If

    End Sub

#End Region

#Region "控件__动作"

    Private Sub Comm_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.Click, Comm2.Click
        Select Case sender.tag
            Case "保存"
                If ComboMzZyYp1.SelectedValue = "" Then
                    Beep()
                    MsgBox("药品编码不能为空,按任意键返回!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    ComboMzZyYp1.Select()
                    Exit Sub
                End If
                If C1Numeric1.Value = 0 Then
                    Beep()
                    MsgBox("使用数量不能为零！", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示")
                    C1Numeric1.Select()
                    Exit Sub
                End If

                If C1Numeric1.Value > 0 And C1Numeric1.Value > IIf(IsDBNull(ComboMzZyYp1.Columns("Yf_Sl").Value), 0, ComboMzZyYp1.Columns("Yf_Sl").Value) Then
                    MsgBox("销售数量不能大于库存数量,请修改!", MsgBoxStyle.Critical, "提示")
                    C1Numeric1.Select()
                    Exit Sub
                End If

                Dim Yp_Sum As Double = 0
                Yp_Sum = IIf(Rds.Tables("药品卫材").Compute("Sum(Cf_Money)", "") Is DBNull.Value, 0, Rds.Tables("药品卫材").Compute("Sum(Cf_Money)", ""))


                If Yp_Sum + Xm_Sum + C1Numeric1.Value * C1Numeric2.Value > RSyje - Val(HisPara.PublicConfig.Qfed) Then
                    MsgBox("所录处方总金额将大于押金底线该条药品记录不能保存！", MsgBoxStyle.Information, "提示：")
                    C1Numeric1.Select()
                    Exit Sub
                End If

                If HisVar.HisVar.Sqldal.GetSingle("select count(1) from bl_cfyp where cf_code ='" & Rcode & "'  and SUBSTRING(XX_CODE, 1, 11) = SUBSTRING('" & ComboMzZyYp1.SelectedValue & "', 1, 11) and SUBSTRING(XX_CODE, 12, 5) <> SUBSTRING('" & ComboMzZyYp1.SelectedValue & "', 12, 5)  ") > 0 Then  '为医保接口做的限定
                    MsgBox("药品名称相同，批号不同,不能开在同一处方！", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示")
                    C1Numeric1.Select()
                    Exit Sub
                End If

                If count > 0 And (_fcf = False And C1Numeric1.Value <= 0) Then
                    MsgBox("退费请使用退费录入功能！", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示")
                    C1Numeric1.Select()
                    count = count - 1
                    Exit Sub
                End If

                If Rinsert = True Then      '增加记录
                    Call Save_Add()
                    Rtdbgrid.Refresh()
                Else                                '编辑记录
                    Call Save_Edit()
                End If

            Case "取消"
                Rtdbgrid.Focus()
                Me.Close()
        End Select
    End Sub


#Region "C1Combo1"

    Private Sub C1Combo1_RowChange(ByVal sender As Object, ByVal e As System.EventArgs) Handles ComboMzZyYp1.RowChange

        If ComboMzZyYp1.WillChangeToValue = "" Then
            Label4.Text = ""
            Label3.Text = ""
            Label1.Text = ""
            Label5.Text = ""
            Label12.Text = ""
            Label8.Text = ""
            C1Numeric2.Value = 0
            V_Xx_Code = ""
        Else
            V_Xx_Code = ComboMzZyYp1.Columns("Xx_Code").Value & ""
            Label4.Text = ComboMzZyYp1.Columns("Mx_Gyzz").Value & ""
            Label3.Text = ComboMzZyYp1.Columns("Mx_Cd").Value & ""
            Label1.Text = ComboMzZyYp1.Columns("Mx_Gg").Value & ""
            Label5.Text = ComboMzZyYp1.Columns("Jx_Name").Value & ""
            Label12.Text = Format(ComboMzZyYp1.Columns("Mx_Cfbl").Value, "0.####")
            Label8.Text = Format(ComboMzZyYp1.Columns("Yf_Sl").Value, "###,##0.####") & " " & ComboMzZyYp1.Columns("Mx_Xsdw").Value & ""
            C1Numeric2.Value = ComboMzZyYp1.Columns("Yf_Lsj").Value & ""
        End If
    End Sub

#End Region

    Private Sub CheckBox1_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox1.CheckedChanged
        Dim V_Str As String = ""
        If CheckBox1.Checked = True Then
            V_Str = "1=1" & V_Str
        Else
            V_Str = "Yf_Sl>0" & V_Str
        End If
        ComboMzZyYp1.RowFilterTextNull = V_Str
        If Trim(ComboMzZyYp1.Text & "") <> "" Then
            V_Str = V_Str & " And (Yp_Jc Like '*" & Trim(ComboMzZyYp1.Text.Replace("*", "[*]")) & "*')"
        End If
        ComboMzZyYp1.DataView.RowFilter = V_Str
        ComboMzZyYp1.SelectedIndex = -1
        ComboMzZyYp1.Select()
    End Sub

#Region "用法用量"

    Private Sub Combo_Init()   '初始化用法用量

        HisVar.HisVar.Sqldal.QueryDt(My_DataSet, "Select Yfyl_Nr,Lb_Name from Zd_Yfyl", "用法用量", True)
        Dim Row As DataRow

        Dim My_Combo1 As BaseClass.C_Combo1 = New BaseClass.C_Combo1(C1Combo_Yfyl1)
        Dim My_Combo2 As BaseClass.C_Combo1 = New BaseClass.C_Combo1(C1Combo_Yfyl2)
        Dim My_Combo3 As BaseClass.C_Combo1 = New BaseClass.C_Combo1(C1Combo_Yfyl3)
        Dim My_Combo4 As BaseClass.C_Combo1 = New BaseClass.C_Combo1(C1Combo_Yfyl4)
        My_Combo1.Init_TDBCombo()
        My_Combo2.Init_TDBCombo()
        My_Combo3.Init_TDBCombo()
        My_Combo4.Init_TDBCombo()
        For Each Row In My_DataSet.Tables("用法用量").Rows
            Select Case Row.Item("Lb_Name")
                Case "用法"
                    My_Combo1.AddItem(Row.Item("Yfyl_Nr"))
                Case "频度"
                    My_Combo2.AddItem(Row.Item("Yfyl_Nr"))
                Case "用量"
                    My_Combo3.AddItem(Row.Item("Yfyl_Nr"))
                Case "单位"
                    My_Combo4.AddItem(Row.Item("Yfyl_Nr"))
            End Select
        Next

        My_Combo1.SelectedIndex(-1)
        My_Combo2.SelectedIndex(-1)
        My_Combo3.SelectedIndex(-1)
        My_Combo4.SelectedIndex(-1)



    End Sub

    Private Sub C1Combo_Yfyl_RowChange(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Combo_Yfyl1.RowChange, C1Combo_Yfyl2.RowChange, C1Combo_Yfyl3.RowChange, C1Combo_Yfyl4.RowChange
        C1TextBox1.Text = C1Combo_Yfyl3.Text + C1Combo_Yfyl4.Text + " " + C1Combo_Yfyl2.Text + " " + C1Combo_Yfyl1.Text
    End Sub

#End Region

#End Region

#Region "数据__编辑"

    Private Sub Data_Clear()
        Rinsert = True
        _fcf = False
        listTfCfid = ""
        V_Xx_Code = ""                                          '药品编码
        C1Numeric1.Enabled = True
        C1Numeric1.BackColor = SystemColors.Window
        ComboMzZyYp1.Enabled = True
        ComboMzZyYp1.SelectedIndex = -1
        CheckBox1.Enabled = True
        CheckBox1.Checked = False
        '备注
        C1Numeric1.Value = 1                                    '采购数量
        C1Numeric2.Value = 0                                    '采购单价
        C1Numeric4.Value = 0                                    '采购金额
        MyTextBox1.Text = ""
        Label8.Text = ""
        C1TextBox1.Text = ""
        C1Combo_Yfyl1.SelectedIndex = -1
        C1Combo_Yfyl2.SelectedIndex = -1
        C1Combo_Yfyl3.SelectedIndex = -1
        C1Combo_Yfyl4.SelectedIndex = -1
        ComboMzZyYp1.Select()

    End Sub

    Private Sub Data_Show(ByVal tmp_Row As DataRow)
        Rinsert = False
        CheckBox1.Enabled = False
        CmdTf.Enabled = False
        Rrow = tmp_Row
        With Rrow
            ComboMzZyYp1.DataView.RowFilter = "Yf_Sl>0 or Xx_Code='" & .Item("Xx_Code") & "" & "' "
            V_Xx_Code = .Item("Xx_Code") & ""
            '药品名称
            Dim Yf_Row As DataRow
            Yf_Row = YpDt.Rows.Find(V_Xx_Code)
            With Yf_Row
                .BeginEdit()
                .Item("Yf_Sl") = .Item("Yf_Sl") + Rrow.Item("Cf_Sl")
                .EndEdit()
            End With
            YpDt.AcceptChanges()

            ComboMzZyYp1.SelectedValue = V_Xx_Code
            C1Numeric1.Value = .Item("Cf_Sl")                   '采购数量
            C1Numeric2.Value = .Item("Cf_Dj") & ""
            C1Numeric4.Value = .Item("Cf_Money")                '采购金额
            C1TextBox1.Text = .Item("Yp_Yfyl") & ""
            C1Combo_Yfyl1.SelectedIndex = -1
            C1Combo_Yfyl2.SelectedIndex = -1
            C1Combo_Yfyl3.SelectedIndex = -1
            C1Combo_Yfyl4.SelectedIndex = -1
        End With
        ComboMzZyYp1.Select()
        If C1Numeric1.Value < 0 Then
            ComboMzZyYp1.Enabled = False
            C1Numeric1.Enabled = False
            C1Numeric1.BackColor = SystemColors.Info
            C1Numeric1.ForeColor = SystemColors.WindowText
            MyTextBox1.Enabled = False
        End If

    End Sub

    Private Sub Save_Add()
        Dim Sl1 As Object = HisVar.HisVar.Sqldal.GetSingle("select Sum(Cf_Sl) as Cf_Sl from Bl_Cfyp,Bl_Cf where Bl_Cfyp.Cf_Code=Bl_Cf.Cf_Code and Bl_Code='" & RBl_Code & "' and Xx_Code='" & ComboMzZyYp1.SelectedValue & "'")
        If C1Numeric1.Value + Sl1 < 0 Then
            Beep()
            MsgBox("退药数量不能大于使用数量！", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示")
            C1Numeric1.Select()
            Exit Sub
        End If
        Dim My_Tb As DataTable = RZbtb
        Dim My_NewRow As DataRow = My_Tb.NewRow
        With My_NewRow
            .BeginEdit()
            .Item("Cf_Code") = Rcode
            .Item("Xx_Code") = V_Xx_Code                        '药品明细编码
            .Item("Cf_Sl") = C1Numeric1.Value                   '采购单价
            .Item("Cf_Dj") = C1Numeric2.Value                   '采购单价
            If _fcf = True Then
                .Item("Cf_Money") = C1Numeric4.Value
            Else
                .Item("Cf_Money") = C1Numeric1.Value * C1Numeric2.Value                '采购金额
            End If
            .Item("Cf_Lb") = ComboMzZyYp1.Columns("Dl_Name").Value
            .Item("Yp_Yxq") = ComboMzZyYp1.Columns("Yp_Yxq").Value
            .Item("Mx_Gg") = Trim(Label1.Text & "")
            .Item("Mx_Cd") = Trim(Label3.Text & "")
            .Item("Yp_Name") = Trim(ComboMzZyYp1.Text & "")
            .Item("Yp_Yfyl") = Trim(C1TextBox1.Text & "")
            .EndEdit()
        End With

        Try
            My_Tb.Rows.Add(My_NewRow)

            Rtdbgrid.UpdateData()
            Rtdbgrid.MoveLast()

            Rlb.Text = Format(Val(Rlb.Text) + My_NewRow.Item("Cf_Money"), "###,###,###.00") & "元"

            Dim Yf_Row As DataRow
            Yf_Row = YpDt.Rows.Find(V_Xx_Code)
            With Yf_Row
                .BeginEdit()
                .Item("Yf_Sl") = .Item("Yf_Sl") - My_NewRow.Item("Cf_Sl")
                .EndEdit()
            End With
            Yf_Row.AcceptChanges()

            With Rzbadt.InsertCommand
                Try
                    .Parameters(0).Value = HisVar.HisVar.WsyCode
                    .Parameters(1).Value = Rcode
                    .Parameters(2).Value = My_NewRow.Item("Xx_Code") & ""
                    .Parameters(3).Value = My_NewRow.Item("Cf_Sl")
                    .Parameters(4).Value = My_NewRow.Item("Cf_Dj")
                    .Parameters(5).Value = My_NewRow.Item("Cf_Money")
                    .Parameters(6).Value = My_NewRow.Item("Cf_Lb")
                    .Parameters(7).Value = My_NewRow.Item("Yp_Yfyl")
                    .Parameters(8).Direction = ParameterDirection.Output
                    Call P_Conn(True)
                    .ExecuteNonQuery()
                    Dim cf_id As Integer
                    cf_id = Rzbadt.InsertCommand.Parameters(8).Value
                    If _fcf = True Then
                        Dim bll As New BllBl_Cfyp
                        bll.UpdateTfCfId(cf_id, listTfCfid)
                    End If
                    Call P_Conn(False)

                    My_NewRow.AcceptChanges()
                Catch ex As Exception
                    Beep()
                    MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
                End Try
            End With

            Call Data_Clear()                                   '清空记录
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            ComboMzZyYp1.Select()
        End Try
    End Sub

    Private Sub Save_Edit()

        Dim Sl1 As Object = HisVar.HisVar.Sqldal.GetSingle("select Sum(Cf_Sl) as Cf_Sl from Bl_Cfyp,Bl_Cf where Bl_Cfyp.Cf_Code=Bl_Cf.Cf_Code and Bl_Code='" & RBl_Code & "' and Xx_Code='" & ComboMzZyYp1.SelectedValue & "' and Cf_Id<>'" & Rrow.Item("Cf_Id", DataRowVersion.Original) & "'")
        If C1Numeric1.Value + Sl1 < 0 Then
            Beep()
            MsgBox("退药数量不能大于使用数量！", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示")
            C1Numeric1.Select()
            Exit Sub
        End If

        With Rrow
            Try
                .BeginEdit()
                .Item("Cf_Code") = Rcode
                .Item("Xx_Code") = V_Xx_Code                        '药品明细编码
                .Item("Cf_Sl") = C1Numeric1.Value                   '采购单价
                .Item("Cf_Dj") = C1Numeric2.Value                   '采购单价
                .Item("Cf_Money") = C1Numeric1.Value * C1Numeric2.Value                '采购金额
                .Item("Cf_Lb") = ComboMzZyYp1.Columns("Dl_Name").Value
                .Item("Yp_Yxq") = ComboMzZyYp1.Columns("Yp_Yxq").Value
                .Item("Mx_Gg") = Trim(Label1.Text & "")
                .Item("Mx_Cd") = Trim(Label3.Text & "")
                .Item("Yp_Name") = Trim(ComboMzZyYp1.Text & "")
                .Item("Yp_yfyl") = Trim(C1TextBox1.Text & "")
                .EndEdit()
            Catch ex As Exception
                Beep()
                MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            End Try
        End With



        Rlb.Text = Format(Val(Rlb.Text) + Rrow.Item("Cf_Money", DataRowVersion.Current) - Rrow.Item("Cf_Money", DataRowVersion.Original), "###,###,###.00") & "元"
        Dim Yf_Row As DataRow
        Yf_Row = YpDt.Rows.Find(V_Xx_Code)
        With Yf_Row
            .BeginEdit()
            .Item("Yf_Sl") = .Item("Yf_Sl") + Rrow.Item("Cf_Sl", DataRowVersion.Original)
            .Item("Yf_Sl") = .Item("Yf_Sl") - Rrow.Item("Cf_Sl")
            .EndEdit()
        End With
        Yf_Row.AcceptChanges()

        With Rzbadt.UpdateCommand
            .Parameters(0).Value = Rrow.Item("Xx_Code")
            .Parameters(1).Value = Rrow.Item("Cf_Sl")
            .Parameters(2).Value = Rrow.Item("Cf_Dj")
            .Parameters(3).Value = Rrow.Item("Cf_Money")
            .Parameters(4).Value = Rrow.Item("Cf_Lb")
            .Parameters(5).Value = Rrow.Item("Yp_Yfyl")
            .Parameters(6).Value = Rrow.Item("Cf_Id", DataRowVersion.Original)

            Try
                Call P_Conn(True)
                .ExecuteNonQuery()
                Call P_Conn(False)
                Rrow.AcceptChanges()
            Catch ex As Exception
                Beep()
                MsgBox(ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
            End Try
        End With
        MsgBox("修改完成！", MsgBoxStyle.Information, "提示：")
        Me.Close()

    End Sub

#End Region

#Region "鼠标__外观"

    Private Sub P_Comm(ByVal Bt As Button)

        With Bt
            Select Case .Tag
                Case "保存"
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")

                Case "取消"
                    .Left = Me.Comm1.Left + Me.Comm1.Width + 1
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
                    .Width = Me.Comm1.Width
            End Select

            .Text = ""
            .FlatStyle = FlatStyle.Flat
            .FlatAppearance.BorderSize = 0
            .BackgroundImageLayout = ImageLayout.Center
        End With

    End Sub

    Private Sub Comm_MouseEnter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.MouseEnter, Comm2.MouseEnter
        Select Case sender.tag
            Case "保存"
                Me.Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存2")
                Me.Comm1.Cursor = Cursors.Hand

            Case "取消"
                Me.Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消2")
                Me.Comm2.Cursor = Cursors.Hand

        End Select

    End Sub

    Private Sub Comm_MouseLeave(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.MouseLeave, Comm2.MouseLeave
        Select Case sender.tag

            Case "保存"
                Me.Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")
                Me.Comm1.Cursor = Cursors.Default

            Case "取消"
                Me.Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
                Me.Comm2.Cursor = Cursors.Default

        End Select

    End Sub

    Private Sub Comm_MouseDown(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm1.MouseDown, Comm2.MouseDown
        Select Case sender.tag
            Case "保存"
                Me.Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存3")

            Case "取消"
                Me.Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消3")
        End Select
    End Sub

    Private Sub Comm_MouseUp(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm1.MouseUp, Comm2.MouseUp
        Select Case sender.tag
            Case "保存"
                Me.Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存2")

            Case "取消"
                Me.Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消2")
        End Select
    End Sub


#End Region

#Region "输入法设置"

    Private Sub C1Numeric1_KeyDown(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ComboMzZyYp1.GotFocus, C1Numeric1.GotFocus
        InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages(0)
    End Sub

    '中文
    Private Sub C1TextBox1_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1TextBox1.GotFocus
        InputLanguage.CurrentInputLanguage = HisVar.HisVar.InputCode
    End Sub

#End Region

    Private Sub MyTextBox1_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyTextBox1.TextChanged
        ComboMzZyYp1.Text = ""
        If CheckBox1.Checked = True Then
            ComboMzZyYp1.DataSource.RowFilter = "1=1 and Xx_Code like '%" & MyTextBox1.Text & "%'"
        Else
            ComboMzZyYp1.DataSource.RowFilter = "Yf_Sl>0 and Xx_Code like '%" & MyTextBox1.Text & "%'"
        End If
    End Sub

    Private Sub CmdTf_Click(sender As Object, e As C1.Win.C1Command.ClickEventArgs) Handles CmdTf.Click
        Dim frm As New ZTHisInpatient.Bl_CfYpTf("药品", RBl_Code)
        If frm.ShowDialog() = DialogResult.OK Then
            CheckBox1.Checked = True
            YpDt.DefaultView.RowFilter = ""
            ComboMzZyYp1.SelectedValue = frm.MdlBlCfyp.Xx_Code
            C1Numeric1.Value = frm.MdlBlCfyp.Cf_Sl
            C1Numeric2.Value = frm.MdlBlCfyp.Cf_Dj
            _fcf = True
            C1Numeric1.Enabled = False
            C1Numeric1.BackColor = SystemColors.Info
            C1Numeric1.ForeColor = SystemColors.WindowText
            C1Numeric4.Value = frm.MdlBlCfyp.Cf_Money
            listTfCfid = frm.cfidstr
        End If
    End Sub
End Class