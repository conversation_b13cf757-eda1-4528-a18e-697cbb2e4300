﻿Imports System.Data.SqlClient
Imports HisControl
Imports BaseClass

Public Class Xs_Mz31

#Region "定义变量"

    Dim V_Xx_Code As String = ""                '药品__明细编码
    Dim V_Yp_Code As String                     '药品编码


    Dim My_DataSet As New DataSet
    Dim Dt_Init As Boolean '用来判断Comobo的数据源是否初始化完成

    Dim V_Yf_Code As String

    Dim YpDt As DataTable
#End Region

#Region "传参"
    Dim Rform As BaseForm
    Dim Rinsert As Boolean
    Dim Rrow As DataRow
    Dim RZbtb As DataTable
    Dim Rtdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Dim Rcode As String
    Public Hzlb As String = ""
#End Region

    Public Sub New(ByVal tform As BaseForm, ByVal tinsert As Boolean, ByVal trow As DataRow, ByVal tZbtb As DataTable, ByVal ttdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid, ByVal tcode As String, ByVal tyfcode As String)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        Rform = tform
        Rinsert = tinsert
        Rrow = trow
        RZbtb = tZbtb
        Rtdbgrid = ttdbgrid
        Rcode = tcode

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        V_Yf_Code = tyfcode
    End Sub

    Protected Overrides Sub OnKeyDown(ByVal e As System.Windows.Forms.KeyEventArgs)
        MyBase.OnKeyDown(e)
        If e.Control = True And e.KeyCode = Keys.S Then
            Comm1.Select()
            Call Comm_Click(Comm1, Nothing)
        End If
    End Sub

    Private Sub Yf_Ck31_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        With Rform

            Dt_Init = False
            Call Form_Init()
            Dt_Init = True
            CheckBox1.Checked = False
            Me.Location = New Point(.Left + (.Width - Me.Width) / 2, .Top + (.Height - Me.Height))
            If Rinsert = True Then Call Data_Clear() Else Call Data_Show()
        End With
    End Sub

#Region "窗体__事件"


    Private Sub Form_Init()

        '按扭初始化
        Call P_Comm(Me.Comm1)
        Call P_Comm(Me.Comm2)


        With Me.C1Numeric1
            .Value = 0
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .CustomFormat = "########0.#####"
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .ShowContextMenu = False
            .EmptyAsNull = True
        End With

        With Me.C1Numeric2
            .Value = 0
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .CustomFormat = "###,###,##0.00####"
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .ShowContextMenu = False
            .EmptyAsNull = True
        End With

        With Me.C1Numeric4
            .Value = 0
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .CustomFormat = "###,###,##0.00##"
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .ShowContextMenu = False
            .EmptyAsNull = True
        End With

        With Me.C1NumericEdit1
            .Value = 0
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .CustomFormat = "########0.####"
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .ShowContextMenu = False
            .EmptyAsNull = True
        End With
        ComboMzZyYp1.Init(V_Yf_Code)
        YpDt = ComboMzZyYp1.DataView.Table
        YpDt.PrimaryKey = New DataColumn() {YpDt.Columns("Xx_Code")}
        YpDt.Columns("Yf_Sl").ReadOnly = False
        ComboMzZyYp1.RowFilterTextNull = "Yf_Sl>0"

        HisVar.HisVar.Sqldal.QueryDt(My_DataSet, "Select Yfyl_Nr,Lb_Name from Zd_Yfyl", "用法用量", True)
        Dim Row As DataRow

        Dim My_Combo1 As BaseClass.C_Combo1 = New BaseClass.C_Combo1(C1Combo2)
        Dim My_Combo2 As BaseClass.C_Combo1 = New BaseClass.C_Combo1(C1Combo3)
        Dim My_Combo3 As BaseClass.C_Combo1 = New BaseClass.C_Combo1(C1Combo4)
        Dim My_Combo4 As BaseClass.C_Combo1 = New BaseClass.C_Combo1(C1Combo5)
        My_Combo1.Init_TDBCombo()
        My_Combo2.Init_TDBCombo()
        My_Combo3.Init_TDBCombo()
        My_Combo4.Init_TDBCombo()
        For Each Row In My_DataSet.Tables("用法用量").Rows
            Select Case Row.Item("Lb_Name")
                Case "用法"
                    My_Combo1.AddItem(Row.Item("Yfyl_Nr"))
                Case "频度"
                    My_Combo2.AddItem(Row.Item("Yfyl_Nr"))
                Case "用量"
                    My_Combo3.AddItem(Row.Item("Yfyl_Nr"))
                Case "单位"
                    My_Combo4.AddItem(Row.Item("Yfyl_Nr"))
            End Select
        Next

        My_Combo1.SelectedIndex(-1)
        My_Combo2.SelectedIndex(-1)
        My_Combo3.SelectedIndex(-1)
        My_Combo4.SelectedIndex(-1)


    End Sub

#End Region

#Region "其它项目"

    Private Sub C1Combo2_GotFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1Combo2.GotFocus
        C1Combo2.OpenCombo()
    End Sub

    Private Sub C1Combo3_GotFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1Combo3.GotFocus
        C1Combo3.OpenCombo()
    End Sub

    Private Sub C1Combo4_GotFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1Combo4.GotFocus
        C1Combo4.OpenCombo()
    End Sub

    Private Sub C1Combo5_GotFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1Combo5.GotFocus
        C1Combo5.OpenCombo()
    End Sub

    Private Sub C1Numeric_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles C1Numeric1.KeyPress
        If e.KeyChar <> Chr(Keys.Return) Then Exit Sub
        If HisPara.PublicConfig.MzYsz = "是" And ComboMzZyYp1.Columns("Dl_Name").Value <> "卫生材料" Then
            C1Combo4.Select()
        Else
            Comm1.Select()
        End If

    End Sub

    Private Sub C1Combo2_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles C1Combo2.KeyPress, C1Combo3.KeyPress, C1Combo4.KeyPress, C1Combo5.KeyPress, C1TextBox1.KeyPress
        If e.KeyChar <> Chr(Keys.Return) Then Exit Sub
        e.Handled = True
        System.Windows.Forms.SendKeys.Send("{Tab}")
    End Sub

    Private Sub C1Combo2_RowChange(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1Combo2.RowChange, C1Combo3.RowChange, C1Combo4.RowChange, C1Combo5.RowChange
        C1TextBox1.Text = Trim(C1Combo4.Text & C1Combo5.Text & " " & C1Combo3.Text & " " & C1Combo2.Text)

    End Sub

    Private Sub C1Numeric1_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1Numeric1.Validated
        C1Numeric4.Value = C1Numeric1.Value * C1Numeric2.Value
    End Sub

#End Region

#Region "控件__动作"

    Private Sub Comm_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.Click, Comm2.Click
        Select Case sender.tag
            Case "保存"

                If ComboMzZyYp1.SelectedValue = "" Then
                    Beep()
                    MsgBox("药品卫材不能为空!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    ComboMzZyYp1.Select()
                    Exit Sub
                End If
                If C1Numeric1.Value = 0 Then
                    Beep()
                    MsgBox("使用数量不能为零！", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示")
                    C1Numeric1.Select()
                    Exit Sub
                End If

                If C1Numeric1.Value > C1NumericEdit1.Value Then
                    Beep()
                    MsgBox("使用数量不能大于库存数量！", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示")
                    C1Numeric1.Select()
                    Exit Sub
                End If

                If C1Numeric1.Value > IIf(IsDBNull(ComboMzZyYp1.Columns("Yf_Sl").Value), 0, ComboMzZyYp1.Columns("Yf_Sl").Value) Then
                    MsgBox("销售数量不能大于库存数量!", MsgBoxStyle.Critical, "提示")
                    C1Numeric1.Value = 0
                    C1Numeric1.Select()
                    Exit Sub
                End If

                'If V_Dl_Name <> "卫生材料" And Trim(C1TextBox1.Text) = "" Then
                '    Beep()
                '    MsgBox("请填写用法用量！", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示")
                '    C1TextBox1.Select()
                '    Exit Sub
                'End If

                Call Save_Add()

            Case "取消"
                ComboMzZyYp1.SelectedIndex = -1
                ComboMzZyYp1.Text = ""
                Rtdbgrid.Focus()
                Me.Close()
        End Select
    End Sub

#Region "C1Combo1"

    Private Sub C1Combo1_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles ComboMzZyYp1.Validated
        Me.CancelButton = Comm2
    End Sub

    Private Sub C1Combo1_RowChange(ByVal sender As Object, ByVal e As System.EventArgs) Handles ComboMzZyYp1.RowChange
        If Dt_Init = False Then Exit Sub
        If ComboMzZyYp1.WillChangeToValue = "" Then
            Label4.Text = ""
            Label16.Text = ""
            Label9.Text = ""
            Label3.Text = ""
            Label1.Text = ""
            Label5.Text = ""
            C1Numeric2.Value = 0
            V_Xx_Code = ""
            C1TextBox1.Text = ""
            C1NumericEdit1.Value = 0
        Else
            V_Xx_Code = ComboMzZyYp1.Columns("Xx_Code").Value & ""
            Label4.Text = ComboMzZyYp1.Columns("Mx_XsDw").Value & ""
            Label16.Text = Format(ComboMzZyYp1.Columns("Mx_Cfbl").Value, "0.####")
            Label9.Text = ComboMzZyYp1.Columns("Mx_Gyzz").Value & ""
            Label3.Text = ComboMzZyYp1.Columns("Mx_Cd").Value & ""
            Label1.Text = ComboMzZyYp1.Columns("Mx_Gg").Value & ""
            Label5.Text = ComboMzZyYp1.Columns("Jx_Name").Value & ""
            C1Numeric2.Value = ComboMzZyYp1.Columns("Yf_Lsj").Value & ""
            C1NumericEdit1.Value = ComboMzZyYp1.Columns("Yf_Sl").Value & ""


        End If
    End Sub

#End Region

    Private Sub CheckBox1_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox1.CheckedChanged
        Dim V_Str As String = ""
        If CheckBox1.Checked = True Then
            V_Str = "1=1" & V_Str
        Else
            V_Str = "Yf_Sl>0" & V_Str
        End If
        ComboMzZyYp1.RowFilterTextNull = V_Str
        If Trim(ComboMzZyYp1.Text & "") <> "" Then
            V_Str = V_Str & " And (Yp_Jc Like '*" & Trim(ComboMzZyYp1.Text.Replace("*", "[*]")) & "*')"
        End If
        ComboMzZyYp1.DataView.RowFilter = V_Str
        ComboMzZyYp1.SelectedIndex = -1
        ComboMzZyYp1.Select()
    End Sub

#End Region

#Region "数据__编辑"

    Private Sub Data_Clear()
        Rinsert = True
        V_Xx_Code = ""                                          '药品编码

        ComboMzZyYp1.Enabled = True
        ComboMzZyYp1.SelectedValue = -1
        ComboMzZyYp1.Text = ""                                      '药品明细编码
        CheckBox1.Enabled = True
        '备注
        C1Numeric1.Value = 1                                    '采购数量
        C1Numeric2.Value = 0                                    '采购单价
        C1Numeric4.Value = 0                                    '采购金额
        C1Combo2.SelectedValue = -1
        C1Combo2.Text = ""
        C1Combo3.SelectedValue = -1
        C1Combo3.Text = ""
        C1Combo4.SelectedValue = -1
        C1Combo4.Text = ""
        C1Combo5.SelectedValue = -1
        C1Combo5.Text = ""

        Label4.Text = ""
        Label16.Text = ""
        C1TextBox1.Text = ""
        Label9.Text = ""
        Label3.Text = ""
        Label1.Text = ""
        Label5.Text = ""
        C1NumericEdit1.Value = 0

        ComboMzZyYp1.Select()


    End Sub

    Private Sub Data_Show()
        Rinsert = False
        CheckBox1.Enabled = False
        With Rrow
            ComboMzZyYp1.DataView.RowFilter = "Yf_Sl>0 or Xx_Code='" & .Item("Xx_Code") & "" & "' "
            V_Xx_Code = .Item("Xx_Code") & ""
            '药品名称
            Dim Yf_Row As DataRow
            Yf_Row = YpDt.Rows.Find(V_Xx_Code)
            With Yf_Row
                .BeginEdit()
                .Item("Yf_Sl") = .Item("Yf_Sl") + Rrow.Item("Mz_Sl")
                .EndEdit()
            End With
            YpDt.AcceptChanges()

            ComboMzZyYp1.SelectedValue = V_Xx_Code
            C1Numeric1.Value = .Item("Mz_Sl")                   '采购数量
            C1Numeric2.Value = .Item("Mz_Dj") & ""
            C1Numeric4.Value = .Item("Mz_Money") & ""              '采购金额
            C1TextBox1.Text = .Item("Yp_Yfyl") & ""
        End With
        ComboMzZyYp1.Select()

    End Sub

    Private Sub Save_Add()
        Dim My_NewRow As DataRow
        If Rinsert = True Then
            My_NewRow = RZbtb.NewRow
        Else
            My_NewRow = Rrow
        End If

        With My_NewRow
            .BeginEdit()
            .Item("Yy_Code") = HisVar.HisVar.WsyCode
            .Item("Mz_Code") = Rcode
            .Item("Xx_Code") = V_Xx_Code                        '药品明细编码
            .Item("Mz_Sl") = C1Numeric1.Value                   '采购单价
            .Item("Mz_Dj") = C1Numeric2.Value                   '采购单价
            .Item("Mz_Money") = C1Numeric1.Value * C1Numeric2.Value                '采购金额
            .Item("Mz_Lb") = ComboMzZyYp1.Columns("Dl_Name").Value
            .Item("Yp_Yxq") = ComboMzZyYp1.Columns("Yp_Yxq").Value
            .Item("Mx_Gg") = Trim(Label1.Text & "")
            .Item("Mx_Cd") = Trim(Label3.Text & "")
            .Item("Yp_Name") = Trim(ComboMzZyYp1.Text & "")
            .Item("Yp_Yfyl") = Trim(C1TextBox1.Text & "")
            .Item("Yp_Discount") = 1
            .Item("Yp_Original_Money") = C1Numeric1.Value * C1Numeric2.Value
            .Item("Mz_DfSl") = C1Numeric1.Value
            .Item("Mz_Fs") = 1
            .EndEdit()
        End With

        Zb_Update(My_NewRow)
    End Sub

    Private Sub Zb_Update(ByVal V_Row As DataRow)      '更新主表
        Dim Insert_String As String = ""
        Dim Update_String As String = ""
        Dim para() As SqlParameter = Nothing
        Dim ilist As List(Of SqlParameter) = New List(Of SqlParameter)()

        ilist.Add(New SqlParameter("@Yy_Code", SqlDbType.Char))
        ilist.Add(New SqlParameter("@Xx_Code", SqlDbType.VarChar))
        ilist.Add(New SqlParameter("@Mz_Sl", SqlDbType.Decimal))
        ilist.Add(New SqlParameter("@Mz_Dj", SqlDbType.Decimal))
        ilist.Add(New SqlParameter("@Mz_Money", SqlDbType.Decimal))
        ilist.Add(New SqlParameter("@Mz_Lb", SqlDbType.VarChar))
        ilist.Add(New SqlParameter("@Yp_Yfyl", SqlDbType.VarChar))
        ilist.Add(New SqlParameter("@Yp_Discount", SqlDbType.VarChar))
        ilist.Add(New SqlParameter("@Yp_Original_Money", SqlDbType.Decimal))
        ilist.Add(New SqlParameter("@Mz_DfSl", SqlDbType.Decimal))
        ilist.Add(New SqlParameter("@Mz_Fs", SqlDbType.VarChar))
        ilist.Add(New SqlParameter("@Mz_Code", SqlDbType.VarChar))
        If Rinsert = False Then
            ilist.Add(New SqlParameter("@Mz_Id", SqlDbType.VarChar))
        End If



        para = ilist.ToArray()

        Insert_String = "Insert Into Mz_Yp(Yy_Code,Xx_Code,Mz_Sl,Mz_Dj,Mz_Money,Mz_Lb,Yp_Yfyl,Yp_Discount,Yp_Original_Money,Mz_DfSl,Mz_Fs,Mz_Code)Values(@Yy_Code,@Xx_Code,@Mz_Sl,@Mz_Dj,@Mz_Money,@Mz_Lb,@Yp_Yfyl,@Yp_Discount,@Yp_Original_Money,@Mz_DfSl,@Mz_Fs,@Mz_Code)  "
        Update_String = "Update Mz_Yp Set Yy_Code=@Yy_Code,Xx_Code=@Xx_Code,Mz_Sl=@Mz_Sl,Mz_Dj=@Mz_Dj,Mz_Money=@Mz_Money,Mz_Lb=@Mz_Lb,Yp_Yfyl=@Yp_Yfyl,Yp_Discount=@Yp_Discount,Yp_Original_Money=@Yp_Original_Money,Mz_DfSl=@Mz_DfSl Where Mz_Code=@Mz_Code and Mz_Id=@Mz_Id   "

        For I = 0 To para.Length - 1
            If Mid(para(I).ParameterName, 2) = "Mz_Id" Then
                para(I).Value = V_Row.Item(Mid(para(I).ParameterName, 2), DataRowVersion.Original)
            Else
                para(I).Value = V_Row.Item(Mid(para(I).ParameterName, 2))

            End If

        Next

        Try
            If Rinsert = True Then
                HisVar.HisVar.Sqldal.ExecuteSql(Insert_String, para)
                RZbtb.Rows.Add(V_Row)
                V_Row.AcceptChanges()
                Rtdbgrid.MoveLast()
                Dim Yf_Row As DataRow
                Yf_Row =YpDt.Rows.Find(V_Xx_Code)
                With Yf_Row
                    .BeginEdit()
                    .Item("Yf_Sl") = .Item("Yf_Sl") - V_Row.Item("Mz_Sl")
                    .EndEdit()
                End With
                Call Data_Clear()
            Else
                HisVar.HisVar.Sqldal.ExecuteSql(Update_String, para)
                V_Row.AcceptChanges()
                Dim Yf_Row As DataRow
                Yf_Row = YpDt.Rows.Find(V_Xx_Code)
                With Yf_Row
                    .BeginEdit()
                    .Item("Yf_Sl") = .Item("Yf_Sl") + Rrow.Item("Mz_Sl", DataRowVersion.Original)
                    .Item("Yf_Sl") = .Item("Yf_Sl") - Rrow.Item("Mz_Sl")
                    .EndEdit()
                End With
                Yf_Row.AcceptChanges()
                MsgBox("修改成功！", MsgBoxStyle.Information, "提示")
                Me.Close()
            End If

        Catch ex As Exception
            MsgBox(ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
        End Try
        Rform.F_Sum()

    End Sub



#End Region

#Region "鼠标__外观"

    Private Sub P_Comm(ByVal Bt As Button)

        With Bt
            Select Case .Tag
                Case "保存"
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")

                Case "取消"
                    .Left = Me.Comm1.Left + Me.Comm1.Width + 1
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
                    .Width = Me.Comm1.Width
            End Select

            .Text = ""
            .FlatStyle = FlatStyle.Flat
            .FlatAppearance.BorderSize = 0
            .BackgroundImageLayout = ImageLayout.Center
        End With

    End Sub

    Private Sub Comm_MouseEnter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.MouseEnter, Comm2.MouseEnter
        Select Case sender.tag
            Case "保存"
                Me.Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存2")
                Me.Comm1.Cursor = Cursors.Hand

            Case "取消"
                Me.Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消2")
                Me.Comm2.Cursor = Cursors.Hand

        End Select

    End Sub

    Private Sub Comm_MouseLeave(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.MouseLeave, Comm2.MouseLeave
        Select Case sender.tag

            Case "保存"
                Me.Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")
                Me.Comm1.Cursor = Cursors.Default

            Case "取消"
                Me.Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
                Me.Comm2.Cursor = Cursors.Default

        End Select

    End Sub

    Private Sub Comm_MouseDown(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm1.MouseDown, Comm2.MouseDown
        Select Case sender.tag
            Case "保存"
                Me.Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存3")

            Case "取消"
                Me.Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消3")
        End Select
    End Sub

    Private Sub Comm_MouseUp(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm1.MouseUp, Comm2.MouseUp
        Select Case sender.tag
            Case "保存"
                Me.Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存2")

            Case "取消"
                Me.Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消2")
        End Select
    End Sub


#End Region

#Region "输入法设置"

    Private Sub C1Numeric1_KeyDown(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Numeric1.GotFocus,ComboMzZyYp1.GotFocus
        InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages(0)
    End Sub

    Private Sub C1TextBox1_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs)
        InputLanguage.CurrentInputLanguage = HisVar.HisVar.InputCode
    End Sub
#End Region






End Class