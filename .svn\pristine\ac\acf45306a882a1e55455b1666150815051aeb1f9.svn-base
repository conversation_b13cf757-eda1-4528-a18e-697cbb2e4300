﻿/**  版本信息模板在安装目录下，可自行修改。
* D_Materials_Use_Out1.cs
*
* 功 能： N/A
* 类 名： D_Materials_Use_Out1
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2017/1/4 16:37:29   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using System.Collections;
using System.Collections.Generic;
namespace DAL
{
	/// <summary>
	/// 数据访问类:D_Materials_Use_Out1
	/// </summary>
	public partial class D_Materials_Use_Out1
	{
		public D_Materials_Use_Out1()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string M_Use_Code)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from Materials_Use_Out1");
			strSql.Append(" where M_Use_Code=@M_Use_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@M_Use_Code", SqlDbType.Char,11)			};
			parameters[0].Value = M_Use_Code;

			return HisVar.HisVar.Sqldal.Exists(strSql.ToString(),parameters);
		}


        /// <summary>
        /// 获取当前时间 最大编码  yyMMdd + 5位流水号
        /// </summary>
        /// <returns></returns>

        public string MaxCode()
        {
            string prefix = (string)HisVar.HisVar.Sqldal.GetSingle("SELECT CONVERT(VARCHAR(6),GETDATE(),12)");
            string max = (string)(prefix + HisVar.HisVar.Sqldal.F_MaxCode("SELECT MAX(SUBSTRING(M_Use_Code,7,5)) FROM Materials_Use_Out1 WHERE SUBSTRING(M_Use_Code,1,6) = CONVERT(VARCHAR(6),GETDATE(),12)", 5));
            return max;
        }

		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ModelOld.M_Materials_Use_Out1 model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into Materials_Use_Out1(");
			strSql.Append("M_Use_Code,Ks_Code,Use_Staff,MaterialsWh_Code,M_Use_Date,Input_Date,Finish_Date,Jsr_Code,TotalMoney,M_Use_Memo,OrdersStatus,WriteOff_Code,WriteOffStatus)");
			strSql.Append(" values (");
			strSql.Append("@M_Use_Code,@Ks_Code,@Use_Staff,@MaterialsWh_Code,@M_Use_Date,@Input_Date,@Finish_Date,@Jsr_Code,@TotalMoney,@M_Use_Memo,@OrdersStatus,@WriteOff_Code,@WriteOffStatus)");
			SqlParameter[] parameters = {
					new SqlParameter("@M_Use_Code", SqlDbType.Char,11),
					new SqlParameter("@Ks_Code", SqlDbType.Char,6),
					new SqlParameter("@Use_Staff", SqlDbType.VarChar,50),
					new SqlParameter("@MaterialsWh_Code", SqlDbType.Char,2),
					new SqlParameter("@M_Use_Date", SqlDbType.SmallDateTime),
					new SqlParameter("@Input_Date", SqlDbType.SmallDateTime),
					new SqlParameter("@Finish_Date", SqlDbType.SmallDateTime),
					new SqlParameter("@Jsr_Code", SqlDbType.Char,7),
					new SqlParameter("@TotalMoney", SqlDbType.Decimal,5),
					new SqlParameter("@M_Use_Memo", SqlDbType.VarChar,50),
					new SqlParameter("@OrdersStatus", SqlDbType.VarChar,10),
					new SqlParameter("@WriteOff_Code", SqlDbType.Char,11),
					new SqlParameter("@WriteOffStatus", SqlDbType.VarChar,10)};
			parameters[0].Value = model.M_Use_Code;
			parameters[1].Value = model.Ks_Code;
			parameters[2].Value = model.Use_Staff;
			parameters[3].Value = model.MaterialsWh_Code;
			parameters[4].Value = model.M_Use_Date;
			parameters[5].Value = model.Input_Date;
			parameters[6].Value = Common.Tools.IsValueNull(model.Finish_Date);
			parameters[7].Value = model.Jsr_Code;
			parameters[8].Value = model.TotalMoney;
			parameters[9].Value = model.M_Use_Memo;
			parameters[10].Value = model.OrdersStatus;
			parameters[11].Value = Common.Tools.IsValueNull(model.WriteOff_Code);
			parameters[12].Value = Common.Tools.IsValueNull(model.WriteOffStatus);

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

        /// <summary>
        /// 生成冲销数据
        /// </summary>
        public bool AddWriteOff(ModelOld.M_Materials_Use_Out1 model)
        {
            List<string> sqlList = new List<string>();
            List<SqlParameter[]> parametersList = new List<SqlParameter[]>();

            StringBuilder strSql = new StringBuilder();
            strSql.Append(" INSERT INTO Materials_Use_Out1 ");
            strSql.Append(" ( M_Use_Code,Ks_Code,Use_Staff,MaterialsWh_Code,M_Use_Date,Input_Date,Finish_Date,Jsr_Code,TotalMoney,M_Use_Memo,OrdersStatus,WriteOff_Code,WriteOffStatus) ");
            strSql.Append(" VALUES  (");
            strSql.Append(" @M_Use_Code,@Ks_Code,@Use_Staff,@MaterialsWh_Code,@M_Use_Date,@Input_Date,@Finish_Date,@Jsr_Code,@TotalMoney,@M_Use_Memo,@OrdersStatus,@WriteOff_Code,@WriteOffStatus)");
            SqlParameter[] parameters = {
					new SqlParameter("@M_Use_Code", SqlDbType.Char,11),
					new SqlParameter("@Ks_Code", SqlDbType.Char,6),
					new SqlParameter("@Use_Staff", SqlDbType.VarChar,50),
					new SqlParameter("@MaterialsWh_Code", SqlDbType.Char,2),
					new SqlParameter("@M_Use_Date", SqlDbType.SmallDateTime),
					new SqlParameter("@Input_Date", SqlDbType.SmallDateTime),
					new SqlParameter("@Finish_Date", SqlDbType.SmallDateTime),
					new SqlParameter("@Jsr_Code", SqlDbType.Char,7),
					new SqlParameter("@TotalMoney", SqlDbType.Decimal,5),
					new SqlParameter("@M_Use_Memo", SqlDbType.VarChar,50),
					new SqlParameter("@OrdersStatus", SqlDbType.VarChar,10),
					new SqlParameter("@WriteOff_Code", SqlDbType.Char,11),
					new SqlParameter("@WriteOffStatus", SqlDbType.VarChar,10)};
            parameters[0].Value = model.M_Use_Code;
            parameters[1].Value = model.Ks_Code;
            parameters[2].Value = model.Use_Staff;
            parameters[3].Value = model.MaterialsWh_Code;
            parameters[4].Value = model.M_Use_Date;
            parameters[5].Value = model.Input_Date;
            parameters[6].Value = Common.Tools.IsValueNull(model.Finish_Date);
            parameters[7].Value = model.Jsr_Code;
            parameters[8].Value = model.TotalMoney;
            parameters[9].Value = model.M_Use_Memo;
            parameters[10].Value = model.OrdersStatus;
            parameters[11].Value = model.WriteOff_Code;
            parameters[12].Value = model.WriteOffStatus;

            sqlList.Add(strSql.ToString());
            parametersList.Add(parameters);

            StringBuilder strSql2 = new StringBuilder();
            strSql2.Append("INSERT INTO Materials_Use_Out2 ");
            strSql2.Append(" SELECT  @M_Use_Code, ");
            strSql2.Append(" Materials_Code,MaterialsStock_Code,");
            strSql2.Append(" @M_Use_Code + SUBSTRING(M_Use_Detail_Code, 12, 4) , ");
            strSql2.Append(" MaterialsLot,MaterialsExpiryDate,-M_Use_RealNo,0,0,M_Use_Price,-M_Use_RealMoney,0,'' ");
            strSql2.Append(" FROM Materials_Use_Out2 ");
            strSql2.Append(" WHERE M_Use_Code = @WriteOff_Code AND M_Use_RealNo > 0");
            SqlParameter[] parameters2 =
            {
               new SqlParameter("@M_Use_Code", SqlDbType.Char,11),
                new SqlParameter("@WriteOff_Code", SqlDbType.Char, 11)
            };
            parameters2[0].Value = model.M_Use_Code;
            parameters2[1].Value = model.WriteOff_Code;
            sqlList.Add(strSql2.ToString());
            parametersList.Add(parameters2);

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(sqlList, parametersList);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }

        }
        /// <summary>
        /// 表单完成操作，并修改库存
        /// </summary>
        public bool Complete(ModelOld.M_Materials_Use_Out1 model)
        {
            List<string> sqlList = new List<string>();
            List<SqlParameter[]> parametersList = new List<SqlParameter[]>();

            //单据状态改成完成,填写完成时间
            StringBuilder strSql1 = new StringBuilder();
            strSql1.Append("update Materials_Use_Out1 set ");
            strSql1.Append("OrdersStatus=@OrdersStatus,");
            strSql1.Append("Finish_Date=@Finish_Date");
            strSql1.Append(" where M_Use_Code=@M_Use_Code ");
            SqlParameter[] parameters1 = {
					new SqlParameter("@OrdersStatus", SqlDbType.VarChar,10),
                    new SqlParameter("@Finish_Date", SqlDbType.SmallDateTime),
					new SqlParameter("@M_Use_Code", SqlDbType.Char,11)};
            parameters1[0].Value = model.OrdersStatus;
            parameters1[1].Value = model.Finish_Date;
            parameters1[2].Value = model.M_Use_Code;

            sqlList.Add(strSql1.ToString());
            parametersList.Add(parameters1);

            if (model.WriteOffStatus == "冲销")
            {
                //更改原单据的冲销数量，实际数量，实际金额
                StringBuilder strSql2 = new StringBuilder();
                strSql2.Append(" UPDATE  Materials_Use_Out2 SET   ");
                strSql2.Append(" M_Use_WriteoffNo = M_Use_WriteoffNo + a.M_Use_Num , ");
                strSql2.Append(" M_Use_RealNo = M_Use_RealNo + a.M_Use_Num , ");
                strSql2.Append(" M_Use_RealMoney = ( M_Use_RealNo + a.M_Use_Num )* M_Use_Price  ");
                strSql2.Append(" FROM ( SELECT M_Use_Num ,WriteOff_Code ,MaterialsStock_Code FROM Materials_Use_Out2 ,Materials_Use_Out1  ");
                strSql2.Append(" WHERE  ");
                strSql2.Append(" Materials_Use_Out1.M_Use_Code = Materials_Use_Out2.M_Use_Code  ");
                strSql2.Append(" AND Materials_Use_Out1.M_Use_Code = @M_Use_Code)a ");
                strSql2.Append(" WHERE  ");
                strSql2.Append(" M_Use_Code = a.WriteOff_Code AND Materials_Use_Out2.MaterialsStock_Code = a.MaterialsStock_Code");
                SqlParameter[] parameters2 = {
					new SqlParameter("@M_Use_Code", SqlDbType.Char,11)};
                parameters2[0].Value = model.M_Use_Code;
                sqlList.Add(strSql2.ToString());
                parametersList.Add(parameters2);

                //冲销单完成，原单据改成被冲销
                StringBuilder strSql3 = new StringBuilder();
                //strSql3.Append(" update Materials_Use_Out1 set ");
                //strSql3.Append(" WriteOffStatus=@WriteOffStatus ");
                //strSql3.Append(" where M_Use_Code=@M_Use_Code ");

                strSql3.Append("update Materials_Use_Out1 set ");
                strSql3.Append("WriteOffStatus=@WriteOffStatus,TotalMoney=a.TotalMoney ");
                strSql3.Append("From (select Sum(M_Use_RealMoney) TotalMoney,M_Use_Code from Materials_Use_Out2 group by M_Use_Code)a ");
                strSql3.Append(" where Materials_Use_Out1.M_Use_Code=@M_Use_Code and a.M_Use_Code=Materials_Use_Out1.M_Use_Code ");
                SqlParameter[] parameters3 = {
					new SqlParameter("@WriteOffStatus", SqlDbType.VarChar,10),
					new SqlParameter("@M_Use_Code", SqlDbType.Char,11)};
                parameters3[0].Value = "被冲销";
                parameters3[1].Value = model.WriteOff_Code;

                sqlList.Add(strSql3.ToString());
                parametersList.Add(parameters3);
            }

            //库存减掉退库数量（因为冲销是负数，减负等于加正）
            StringBuilder strSqlend = new StringBuilder();
            strSqlend.Append("UPDATE Materials_Stock SET MaterialsStore_Num=MaterialsStore_Num-M_Use_Num,MaterialsStore_Money=MaterialsStore_Money-M_Use_Num*MaterialsStore_Price   ");
            strSqlend.Append("FROM Materials_Use_Out2 ");
            strSqlend.Append("WHERE Materials_Stock.MaterialsStock_Code=Materials_Use_Out2.MaterialsStock_Code ");
            strSqlend.Append("AND M_Use_Code=@M_Use_Code ");
            SqlParameter[] parametersend = {
                    new SqlParameter("@M_Use_Code", SqlDbType.Char,11)};
            parametersend[0].Value = model.M_Use_Code;

            sqlList.Add(strSqlend.ToString());
            parametersList.Add(parametersend);

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(sqlList, parametersList);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }

        }
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ModelOld.M_Materials_Use_Out1 model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update Materials_Use_Out1 set ");
			strSql.Append("Ks_Code=@Ks_Code,");
			strSql.Append("Use_Staff=@Use_Staff,");
			strSql.Append("MaterialsWh_Code=@MaterialsWh_Code,");
			strSql.Append("M_Use_Date=@M_Use_Date,");
			strSql.Append("Input_Date=@Input_Date,");
			strSql.Append("Finish_Date=@Finish_Date,");
			strSql.Append("Jsr_Code=@Jsr_Code,");
			strSql.Append("TotalMoney=@TotalMoney,");
			strSql.Append("M_Use_Memo=@M_Use_Memo,");
			strSql.Append("OrdersStatus=@OrdersStatus,");
			strSql.Append("WriteOff_Code=@WriteOff_Code,");
			strSql.Append("WriteOffStatus=@WriteOffStatus");
			strSql.Append(" where M_Use_Code=@M_Use_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Ks_Code", SqlDbType.Char,6),
					new SqlParameter("@Use_Staff", SqlDbType.VarChar,50),
					new SqlParameter("@MaterialsWh_Code", SqlDbType.Char,2),
					new SqlParameter("@M_Use_Date", SqlDbType.SmallDateTime),
					new SqlParameter("@Input_Date", SqlDbType.SmallDateTime),
					new SqlParameter("@Finish_Date", SqlDbType.SmallDateTime),
					new SqlParameter("@Jsr_Code", SqlDbType.Char,7),
					new SqlParameter("@TotalMoney", SqlDbType.Decimal,5),
					new SqlParameter("@M_Use_Memo", SqlDbType.VarChar,50),
					new SqlParameter("@OrdersStatus", SqlDbType.VarChar,10),
					new SqlParameter("@WriteOff_Code", SqlDbType.Char,11),
					new SqlParameter("@WriteOffStatus", SqlDbType.VarChar,10),
					new SqlParameter("@M_Use_Code", SqlDbType.Char,11)};
			parameters[0].Value = model.Ks_Code;
			parameters[1].Value = model.Use_Staff;
			parameters[2].Value = model.MaterialsWh_Code;
			parameters[3].Value = model.M_Use_Date;
			parameters[4].Value = model.Input_Date;
			parameters[5].Value = Common.Tools.IsValueNull(model.Finish_Date);
			parameters[6].Value = model.Jsr_Code;
			parameters[7].Value = model.TotalMoney;
			parameters[8].Value = model.M_Use_Memo;
			parameters[9].Value = model.OrdersStatus;
			parameters[10].Value = Common.Tools.IsValueNull(model.WriteOff_Code);
			parameters[11].Value = Common.Tools.IsValueNull(model.WriteOffStatus);
			parameters[12].Value = model.M_Use_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string M_Use_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Materials_Use_Out1 ");
			strSql.Append(" where M_Use_Code=@M_Use_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@M_Use_Code", SqlDbType.Char,11)			};
			parameters[0].Value = M_Use_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

        /// <summary>
        /// 删除主表和明细表数据
        /// </summary>
        public bool DeleteAll(string M_Use_Code)
        {
            try
            {
                ArrayList arry = new ArrayList();

                StringBuilder sb = new StringBuilder();
                sb.Append("DELETE Materials_Use_Out2 ");
                sb.Append(" WHERE M_Use_Code = '" + M_Use_Code + "' ");
                arry.Add(sb);

                sb = new StringBuilder();
                sb.Append("DELETE Materials_Use_Out1 ");
                sb.Append(" WHERE M_Use_Code = '" + M_Use_Code + "' ");
                arry.Add(sb);
                HisVar.HisVar.Sqldal.ExecuteSqlTran(arry);
                return true;
            }
            catch (Exception e)
            {
                return false;
            }
        }

		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string M_Use_Codelist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Materials_Use_Out1 ");
			strSql.Append(" where M_Use_Code in ("+M_Use_Codelist + ")  ");
			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
        /// <summary>
        ///支领总金额
        /// </summary>
        public double GetSumMoney(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT isnull(SUM(TotalMoney),0)  FROM Materials_Use_Out1  where OrdersStatus='完成'");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" and " + strWhere);
            }
            try
            {
                double douSum = Double.Parse(HisVar.HisVar.Sqldal.GetSingle(strSql.ToString()).ToString());
                return douSum;
            }
            catch (Exception)
            {
                return 0;
            }

        }

		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Materials_Use_Out1 GetModel(string M_Use_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
            strSql.Append("select  top 1 M_Use_Code,Ks_Code,Use_Staff,MaterialsWh_Code,M_Use_Date,Input_Date,Finish_Date,Materials_Use_Out1.Jsr_Code,Jsr_name,TotalMoney,M_Use_Memo,OrdersStatus,WriteOff_Code,WriteOffStatus from Materials_Use_Out1 JOIN Zd_YyJsr ON Materials_Use_Out1.Jsr_Code = Zd_YyJsr.Jsr_Code ");
			strSql.Append(" where M_Use_Code=@M_Use_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@M_Use_Code", SqlDbType.Char,11)			};
			parameters[0].Value = M_Use_Code;

			ModelOld.M_Materials_Use_Out1 model=new ModelOld.M_Materials_Use_Out1();
			DataSet ds=HisVar.HisVar.Sqldal.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Materials_Use_Out1 DataRowToModel(DataRow row)
		{
			ModelOld.M_Materials_Use_Out1 model=new ModelOld.M_Materials_Use_Out1();
			if (row != null)
			{
				if(row["M_Use_Code"]!=null)
				{
					model.M_Use_Code=row["M_Use_Code"].ToString();
				}
				if(row["Ks_Code"]!=null)
				{
					model.Ks_Code=row["Ks_Code"].ToString();
				}
				if(row["Use_Staff"]!=null)
				{
					model.Use_Staff=row["Use_Staff"].ToString();
				}
				if(row["MaterialsWh_Code"]!=null)
				{
					model.MaterialsWh_Code=row["MaterialsWh_Code"].ToString();
				}
				if(row["M_Use_Date"]!=null && row["M_Use_Date"].ToString()!="")
				{
					model.M_Use_Date=DateTime.Parse(row["M_Use_Date"].ToString());
				}
				if(row["Input_Date"]!=null && row["Input_Date"].ToString()!="")
				{
					model.Input_Date=DateTime.Parse(row["Input_Date"].ToString());
				}
				if(row["Finish_Date"]!=null && row["Finish_Date"].ToString()!="")
				{
					model.Finish_Date=DateTime.Parse(row["Finish_Date"].ToString());
				}
				if(row["Jsr_Code"]!=null)
				{
					model.Jsr_Code=row["Jsr_Code"].ToString();
				}
                if (row["Jsr_Name"] != null)
                {
                    model.Jsr_Name = row["Jsr_Name"].ToString();
                }
				if(row["TotalMoney"]!=null && row["TotalMoney"].ToString()!="")
				{
					model.TotalMoney=decimal.Parse(row["TotalMoney"].ToString());
				}
				if(row["M_Use_Memo"]!=null)
				{
					model.M_Use_Memo=row["M_Use_Memo"].ToString();
				}
				if(row["OrdersStatus"]!=null)
				{
					model.OrdersStatus=row["OrdersStatus"].ToString();
				}
				if(row["WriteOff_Code"]!=null)
				{
					model.WriteOff_Code=row["WriteOff_Code"].ToString();
				}
				if(row["WriteOffStatus"]!=null)
				{
					model.WriteOffStatus=row["WriteOffStatus"].ToString();
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select M_Use_Code,Materials_Use_Out1.Ks_Code,Use_Staff,Materials_Use_Out1.MaterialsWh_Code,M_Use_Date,Input_Date,Finish_Date,Materials_Use_Out1.Jsr_Code,TotalMoney,M_Use_Memo,OrdersStatus,WriteOff_Code,WriteOffStatus ");
            strSql.Append(" ,MaterialsWh_Name,Ks_Name,Jsr_Name ");
            strSql.Append(" FROM Materials_Use_Out1 ,Materials_Warehouse_Dict,Zd_YyKs,Zd_YyJsr ");
            strSql.Append(" WHERE Materials_Use_Out1.MaterialsWh_Code = Materials_Warehouse_Dict.MaterialsWh_Code ");
            strSql.Append(" AND Materials_Use_Out1.Ks_Code = Zd_YyKs.Ks_Code ");
            strSql.Append(" AND Materials_Use_Out1.Jsr_Code = Zd_YyJsr.Jsr_Code ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" AND " + strWhere);
            }
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" M_Use_Code,Ks_Code,Use_Staff,MaterialsWh_Code,M_Use_Date,Input_Date,Finish_Date,Jsr_Code,TotalMoney,M_Use_Memo,OrdersStatus,WriteOff_Code,WriteOffStatus ");
			strSql.Append(" FROM Materials_Use_Out1 ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM Materials_Use_Out1 ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.M_Use_Code desc");
			}
			strSql.Append(")AS Row, T.*  from Materials_Use_Out1 T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

        public DataSet GetHzList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM Materials_Use_Out1 ,Materials_Use_Out2 ,Zd_YyKs ,Materials_Dict ,Materials_Stock ,Materials_Warehouse_Dict ,Zd_YyJsr ,Materials_Class_Dict ");
            strSql.Append(" WHERE Materials_Use_Out2.M_Use_Code = Materials_Use_Out1.M_Use_Code ");
            strSql.Append(" AND Materials_Use_Out1.Ks_Code = Zd_YyKs.Ks_Code ");
            strSql.Append(" AND Materials_Dict.Materials_Code = Materials_Use_Out2.Materials_Code ");
            strSql.Append(" AND Materials_Use_Out2.MaterialsStock_Code = Materials_Stock.MaterialsStock_Code ");

            strSql.Append(" AND Materials_Use_Out1.Jsr_Code = Zd_YyJsr.Jsr_Code ");
            strSql.Append(" AND Materials_Use_Out1.MaterialsWh_Code = Materials_Warehouse_Dict.MaterialsWh_Code ");
            strSql.Append(" AND Materials_Class_Dict.Class_Code = Materials_Dict.Class_Code ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(strWhere);
            }
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }
		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Materials_Use_Out1";
			parameters[1].Value = "M_Use_Code";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return HisVar.HisVar.Sqldal.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

