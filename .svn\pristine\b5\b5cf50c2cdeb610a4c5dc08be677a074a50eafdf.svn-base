﻿Imports System.Data.SqlClient
Imports Stimulsoft.Report
Imports Stimulsoft.Report.Components

Public Class Work_Tj
    Private Sub Work_Tj_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        BaseFunc.BaseFunc.Init_Datetimepicker(DateTimePicker1)
        BaseFunc.BaseFunc.Init_Datetimepicker(DateTimePicker2)
        DateTimePicker1.Value = Format(Now, "yyyy-MM-01 00:00:00")
        DateTimePicker2.Value = Format(Now, "yyyy-MM-dd 23:59:59")
        ComboBox1.SelectedIndex = 0
    End Sub

    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click
        Try

            If ComboBox1.Text <> "入院患者统计" Then
                Dim v_ds As New DataSet
                Dim paralist(2) As SqlParameter
                paralist(0) = New SqlParameter("@Date1", SqlDbType.SmallDateTime)
                paralist(1) = New SqlParameter("@Date2", SqlDbType.SmallDateTime)
                paralist(2) = New SqlParameter("@lx", SqlDbType.VarChar)

                paralist(0).Value = Format(DateTimePicker1.Value, "yyyy-MM-dd 00:00:00")
                paralist(1).Value = Format(DateTimePicker2.Value, "yyyy-MM-dd 23:59:59")
                If ComboBox1.Text = "医生工作量统计" Then
                    paralist(2).Value = "医生"
                End If
                If ComboBox1.Text = "科室工作量统计" Then
                    paralist(2).Value = "科室"
                End If
                v_ds = HisVar.HisVar.Sqldal.RunProcedure("P_WorkTj", paralist, "全院工作量统计")

                Dim Stirpt As New StiReport
                Stirpt.Load(".\Rpt\医院工作量统计.mrt")
                Stirpt.Dictionary.Databases.Clear()
                Stirpt.RegData(v_ds)
                Stirpt.Dictionary.Synchronize()
                TryCast(Stirpt.Pages(0).GetComponents.Item("Text20"), StiText).Text = Format((v_ds.Tables("全院工作量统计").Compute("Sum(JbYp_Money)", "") / v_ds.Tables("全院工作量统计").Compute("Sum(Yp_Money)", "")) * 100, "0.00")

                If ComboBox1.Text = "医生工作量统计" Then

                    Stirpt.ReportName = "全院医生工作量统计表"
                    TryCast(Stirpt.Pages(0).GetComponents.Item("Text3"), StiText).Text = "医生姓名"
                    Stirpt.Compile()
                    Stirpt("统计名称") = "医生"
                    Stirpt("时间") = "统计时间：" & Format(DateTimePicker1.Value, "yyyy-MM-dd HH:mm:ss") & "  至  " & Format(DateTimePicker2.Value, "yyyy-MM-dd HH:mm:ss")
                    ' Stirpt.Design()
                    Stirpt.Show()
                End If
                If ComboBox1.Text = "科室工作量统计" Then
                    Stirpt.ReportName = "全院科室工作量统计表"
                    TryCast(Stirpt.Pages(0).GetComponents.Item("Text3"), StiText).Text = "科室名称"
                    Stirpt.Compile()
                    Stirpt("统计名称") = "科室"
                    Stirpt("时间") = "统计时间：" & Format(DateTimePicker1.Value, "yyyy-MM-dd HH:mm:ss") & "  至  " & Format(DateTimePicker2.Value, "yyyy-MM-dd HH:mm:ss")
                    ' Stirpt.Design()
                    Stirpt.Show()
                End If
            End If
            
            If ComboBox1.Text = "入院患者统计" Then
                Dim My_Dataset As New DataSet
                Dim My_Adapter As New SqlDataAdapter
                Dim Str_Select As String = "SELECT Bl.Bl_Code,Ry_YlCode,Bxlb_Name,Bq_Name,Bc_Name,Ry_Name,Ry_Jc,Ry_Sex,Ry_Sfzh,Ry_Csdate,Ry_Address,Ys_Name,Ks_Name,Ry_RyDate,Ry_CyDate,Jsr_Code,Ry_Memo,Bl.Bxlb_Code,Isnull(Xf_YpMoney,0)Xf_YpMoney,Isnull(Xf_Money,0)-Isnull(Xf_YpMoney,0) Xf_XmMoney,Isnull(Xf_Money,0)Xf_Money,Jb_Code,Datediff(day,Ry_Rydate,Isnull(Ry_CyDate,getdate())) as Ts FROM Zd_YyKs,Zd_YyYs,Zd_Bxlb,Bl Left Join V_YyBc on V_YyBc.Bc_Code=Bl.Bc_Code left join (Select Sum(Cf_Money) AS Xf_Money,Sum(Cf_YpMoney) as Xf_YpMoney,Bl_Code From Bl_Cf where Cf_Qr='是' Group By Bl_Code) a on Bl.Bl_Code=a.Bl_Code Where Ry_RyDate between '" & Format(DateTimePicker1.Value, "yyyy-MM-dd") & "' and '" & Format(DateTimePicker2.Value, "yyyy-MM-dd") & "' and Zd_Bxlb.Bxlb_Code=Bl.Bxlb_Code And Zd_YyKs.Ks_Code=Bl.Ks_Code And Zd_YyYs.Ys_Code=Bl.Ys_Code And Bl.Yy_Code='" & HisVar.HisVar.WsyCode & "'"
                If My_Dataset.Tables("入院患者统计") IsNot Nothing Then My_Dataset.Tables("入院患者统计").Clear()
            
                HisVar.HisVar.Sqldal.QueryDt(My_Dataset, Str_Select, "入院患者统计", True)
                Dim StiRpt1 As New StiReport
                StiRpt1.Load(".\Rpt\入院患者统计.mrt")
                StiRpt1.ReportName = "入院患者统计"
                StiRpt1.RegData(My_Dataset.Tables("入院患者统计"))
                StiRpt1.Compile()
                StiRpt1("统计名称") = "入院患者统计"
                StiRpt1("时间") = "统计时间：" & Format(DateTimePicker1.Value, "yyyy-MM-dd HH:mm:ss") & "  至  " & Format(DateTimePicker2.Value, "yyyy-MM-dd HH:mm:ss")
                'StiRpt1.Design()
                StiRpt1.Show()
            End If

        Catch ex As Exception
            MsgBox(ex.Message, MsgBoxStyle.Information, "提示")
        Finally

        End Try

    End Sub

    Private Sub Button2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button2.Click
        DateTimePicker1.Value = Format(Now, "yyyy-MM-01 00:00:00")
        DateTimePicker2.Value = Format(Now, "yyyy-MM-dd 23:59:59")
        ComboBox1.SelectedIndex = 0
    End Sub
End Class