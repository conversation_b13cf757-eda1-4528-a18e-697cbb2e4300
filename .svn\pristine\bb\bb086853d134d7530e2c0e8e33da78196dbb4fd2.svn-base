﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class Zd_ZhiKong1
    Inherits HisControl.BaseForm

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.C1Holder1 = New C1.Win.C1Command.C1CommandHolder()
        Me.Comm1 = New C1.Win.C1Command.C1Command()
        Me.Comm2 = New C1.Win.C1Command.C1Command()
        Me.Comm3 = New C1.Win.C1Command.C1Command()
        Me.T_Textbox = New C1.Win.C1Input.C1TextBox()
        Me.T_Label = New C1.Win.C1Input.C1Label()
        Me.ToolBar1 = New C1.Win.C1Command.C1ToolBar()
        Me.Link1 = New C1.Win.C1Command.C1CommandLink()
        Me.Link2 = New C1.Win.C1Command.C1CommandLink()
        Me.Link3 = New C1.Win.C1Command.C1CommandLink()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.MySingleComobo1 = New CustomControl.MySingleComobo()
        Me.T_Line1 = New System.Windows.Forms.Label()
        Me.MyGrid1 = New CustomControl.MyGrid()
        CType(Me.C1Holder1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T_Textbox, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T_Label, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Panel1.SuspendLayout()
        Me.SuspendLayout()
        '
        'C1Holder1
        '
        Me.C1Holder1.Commands.Add(Me.Comm1)
        Me.C1Holder1.Commands.Add(Me.Comm2)
        Me.C1Holder1.Commands.Add(Me.Comm3)
        Me.C1Holder1.Owner = Me
        Me.C1Holder1.VisualStyle = C1.Win.C1Command.VisualStyle.Office2010Blue
        '
        'Comm1
        '
        Me.Comm1.Image = Global.ZtHis.Emr.My.Resources.Resources.增加
        Me.Comm1.Name = "Comm1"
        Me.Comm1.Shortcut = System.Windows.Forms.Shortcut.F2
        Me.Comm1.ShortcutText = ""
        Me.Comm1.Text = "增加"
        Me.Comm1.ToolTipText = "增加记录"
        '
        'Comm2
        '
        Me.Comm2.Image = Global.ZtHis.Emr.My.Resources.Resources.删除
        Me.Comm2.Name = "Comm2"
        Me.Comm2.Shortcut = System.Windows.Forms.Shortcut.F3
        Me.Comm2.ShortcutText = ""
        Me.Comm2.Text = "删除"
        Me.Comm2.ToolTipText = "删除记录"
        '
        'Comm3
        '
        Me.Comm3.Image = Global.ZtHis.Emr.My.Resources.Resources.刷新
        Me.Comm3.Name = "Comm3"
        Me.Comm3.Shortcut = System.Windows.Forms.Shortcut.F4
        Me.Comm3.ShortcutText = ""
        Me.Comm3.Text = "更新"
        Me.Comm3.ToolTipText = "更新记录"
        '
        'T_Textbox
        '
        Me.T_Textbox.AutoSize = False
        Me.T_Textbox.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.T_Textbox.Location = New System.Drawing.Point(300, 3)
        Me.T_Textbox.Name = "T_Textbox"
        Me.T_Textbox.Size = New System.Drawing.Size(70, 20)
        Me.T_Textbox.TabIndex = 3
        Me.T_Textbox.TabStop = False
        Me.T_Textbox.Tag = Nothing
        Me.T_Textbox.TextDetached = True
        Me.T_Textbox.TrimStart = True
        Me.T_Textbox.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.T_Textbox.VisualStyle = C1.Win.C1Input.VisualStyle.Office2010Blue
        Me.T_Textbox.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2010Blue
        '
        'T_Label
        '
        Me.T_Label.BackColor = System.Drawing.Color.Transparent
        Me.T_Label.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.T_Label.ForeColor = System.Drawing.Color.Black
        Me.T_Label.Location = New System.Drawing.Point(376, 5)
        Me.T_Label.Name = "T_Label"
        Me.T_Label.Size = New System.Drawing.Size(47, 16)
        Me.T_Label.TabIndex = 2
        Me.T_Label.Tag = Nothing
        Me.T_Label.Text = "T_Label"
        Me.T_Label.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.T_Label.TextDetached = True
        Me.T_Label.TrimStart = True
        Me.T_Label.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'ToolBar1
        '
        Me.ToolBar1.AccessibleName = "Tool Bar"
        Me.ToolBar1.BackColor = System.Drawing.Color.Transparent
        Me.ToolBar1.CommandHolder = Me.C1Holder1
        Me.ToolBar1.CommandLinks.AddRange(New C1.Win.C1Command.C1CommandLink() {Me.Link1, Me.Link2, Me.Link3})
        Me.ToolBar1.Location = New System.Drawing.Point(3, 3)
        Me.ToolBar1.MinButtonSize = 22
        Me.ToolBar1.Movable = False
        Me.ToolBar1.Name = "ToolBar1"
        Me.ToolBar1.Size = New System.Drawing.Size(165, 22)
        Me.ToolBar1.Text = "C1ToolBar1"
        Me.ToolBar1.VisualStyle = C1.Win.C1Command.VisualStyle.Custom
        Me.ToolBar1.VisualStyleBase = C1.Win.C1Command.VisualStyle.Classic
        '
        'Link1
        '
        Me.Link1.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image), C1.Win.C1Command.ButtonLookFlags)
        Me.Link1.Command = Me.Comm1
        '
        'Link2
        '
        Me.Link2.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image), C1.Win.C1Command.ButtonLookFlags)
        Me.Link2.Command = Me.Comm2
        Me.Link2.SortOrder = 1
        Me.Link2.Text = "删除"
        '
        'Link3
        '
        Me.Link3.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image), C1.Win.C1Command.ButtonLookFlags)
        Me.Link3.Command = Me.Comm3
        Me.Link3.SortOrder = 2
        '
        'Panel1
        '
        Me.Panel1.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Panel1.Controls.Add(Me.MySingleComobo1)
        Me.Panel1.Controls.Add(Me.T_Line1)
        Me.Panel1.Controls.Add(Me.T_Textbox)
        Me.Panel1.Controls.Add(Me.ToolBar1)
        Me.Panel1.Controls.Add(Me.T_Label)
        Me.Panel1.Dock = System.Windows.Forms.DockStyle.Top
        Me.Panel1.Location = New System.Drawing.Point(0, 0)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(654, 28)
        Me.Panel1.TabIndex = 2
        '
        'MySingleComobo1
        '
        Me.MySingleComobo1.Captain = ""
        Me.MySingleComobo1.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MySingleComobo1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.MySingleComobo1.CaptainWidth = 0.0!
        Me.MySingleComobo1.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        Me.MySingleComobo1.ItemHeight = 16
        Me.MySingleComobo1.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MySingleComobo1.Location = New System.Drawing.Point(184, 2)
        Me.MySingleComobo1.MaximumSize = New System.Drawing.Size(10000, 80)
        Me.MySingleComobo1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.MySingleComobo1.Name = "MySingleComobo1"
        Me.MySingleComobo1.ReadOnly = False
        Me.MySingleComobo1.Size = New System.Drawing.Size(100, 22)
        Me.MySingleComobo1.TabIndex = 122
        Me.MySingleComobo1.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'T_Line1
        '
        Me.T_Line1.BackColor = System.Drawing.SystemColors.Control
        Me.T_Line1.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.T_Line1.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.T_Line1.Location = New System.Drawing.Point(0, 24)
        Me.T_Line1.Name = "T_Line1"
        Me.T_Line1.Size = New System.Drawing.Size(652, 2)
        Me.T_Line1.TabIndex = 120
        Me.T_Line1.Text = "Label1"
        '
        'MyGrid1
        '
        Me.MyGrid1.CanCustomCol = False
        Me.MyGrid1.Col = 0
        Me.MyGrid1.ColumnFooters = False
        Me.MyGrid1.DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveRight
        Me.MyGrid1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.MyGrid1.FetchRowStyles = False
        Me.MyGrid1.Location = New System.Drawing.Point(0, 28)
        Me.MyGrid1.Margin = New System.Windows.Forms.Padding(0)
        Me.MyGrid1.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.MyGrid1.Name = "MyGrid1"
        Me.MyGrid1.Size = New System.Drawing.Size(654, 371)
        Me.MyGrid1.TabAction = C1.Win.C1TrueDBGrid.TabActionEnum.ControlNavigation
        Me.MyGrid1.TabIndex = 3
        Me.MyGrid1.Xmlpath = Nothing
        '
        'Zd_ZhiKong1
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(654, 399)
        Me.Controls.Add(Me.MyGrid1)
        Me.Controls.Add(Me.Panel1)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle
        Me.MinimizeBox = False
        Me.Name = "Zd_ZhiKong1"
        Me.ShowInTaskbar = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "质控内容"
        CType(Me.C1Holder1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T_Textbox, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T_Label, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Panel1.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents C1Holder1 As C1.Win.C1Command.C1CommandHolder
    Friend WithEvents Comm1 As C1.Win.C1Command.C1Command
    Friend WithEvents Comm2 As C1.Win.C1Command.C1Command
    Friend WithEvents Comm3 As C1.Win.C1Command.C1Command
    Friend WithEvents ToolBar1 As C1.Win.C1Command.C1ToolBar
    Friend WithEvents Link1 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link2 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents T_Textbox As C1.Win.C1Input.C1TextBox
    Friend WithEvents T_Label As C1.Win.C1Input.C1Label
    Friend WithEvents Link3 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents T_Line1 As System.Windows.Forms.Label
    Friend WithEvents Hook1 As CustomControl.Hook
    Friend WithEvents MyGrid1 As CustomControl.MyGrid
    Friend WithEvents MySingleComobo1 As CustomControl.MySingleComobo

End Class
