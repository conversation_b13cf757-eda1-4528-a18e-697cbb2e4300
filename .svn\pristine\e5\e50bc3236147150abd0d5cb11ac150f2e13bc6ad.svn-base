<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="TextBox5.Height" type="System.Single, mscorlib">
    <value>0.55</value>
  </data>
  <data name="TextBox5.Left" type="System.Single, mscorlib">
    <value>0.0625</value>
  </data>
  <data name="TextBox5.Style" xml:space="preserve">
    <value>font-family: 宋体; font-size: 9pt; text-align: left; vertical-align: middle; ddo-char-set: 134</value>
  </data>
  <data name="TextBox5.Text" xml:space="preserve">
    <value>TextBox1</value>
  </data>
  <data name="TextBox5.Top" type="System.Single, mscorlib">
    <value>0</value>
  </data>
  <data name="TextBox5.Width" type="System.Single, mscorlib">
    <value>1.375</value>
  </data>
  <data name="TextBox6.Height" type="System.Single, mscorlib">
    <value>0.55</value>
  </data>
  <data name="TextBox6.Left" type="System.Single, mscorlib">
    <value>2.125</value>
  </data>
  <data name="TextBox6.OutputFormat" xml:space="preserve">
    <value>0.00####</value>
  </data>
  <data name="TextBox6.Style" xml:space="preserve">
    <value>font-family: 宋体; font-size: 9pt; text-align: center; vertical-align: middle; ddo-char-set: 134</value>
  </data>
  <data name="TextBox6.Text" xml:space="preserve">
    <value>TextBox1</value>
  </data>
  <data name="TextBox6.Top" type="System.Single, mscorlib">
    <value>0</value>
  </data>
  <data name="TextBox6.Width" type="System.Single, mscorlib">
    <value>0.4375</value>
  </data>
  <data name="TextBox22.Height" type="System.Single, mscorlib">
    <value>0.55</value>
  </data>
  <data name="TextBox22.Left" type="System.Single, mscorlib">
    <value>2.5625</value>
  </data>
  <data name="TextBox22.OutputFormat" xml:space="preserve">
    <value>0.##</value>
  </data>
  <data name="TextBox22.Style" xml:space="preserve">
    <value>font-family: 宋体; font-size: 9pt; text-align: center; vertical-align: middle; ddo-char-set: 134</value>
  </data>
  <data name="TextBox22.Text" xml:space="preserve">
    <value>1000.00</value>
  </data>
  <data name="TextBox22.Top" type="System.Single, mscorlib">
    <value>0</value>
  </data>
  <data name="TextBox22.Width" type="System.Single, mscorlib">
    <value>0.5625</value>
  </data>
  <data name="TextBox23.Height" type="System.Single, mscorlib">
    <value>0.55</value>
  </data>
  <data name="TextBox23.Left" type="System.Single, mscorlib">
    <value>3.125</value>
  </data>
  <data name="TextBox23.OutputFormat" xml:space="preserve">
    <value>0.00####</value>
  </data>
  <data name="TextBox23.Style" xml:space="preserve">
    <value>font-family: 宋体; font-size: 9pt; text-align: center; vertical-align: middle; ddo-char-set: 134</value>
  </data>
  <data name="TextBox23.Text" xml:space="preserve">
    <value>TextBox1</value>
  </data>
  <data name="TextBox23.Top" type="System.Single, mscorlib">
    <value>0</value>
  </data>
  <data name="TextBox23.Width" type="System.Single, mscorlib">
    <value>0.75</value>
  </data>
  <data name="TextBox11.Height" type="System.Single, mscorlib">
    <value>0.55</value>
  </data>
  <data name="TextBox11.Left" type="System.Single, mscorlib">
    <value>1.4375</value>
  </data>
  <data name="TextBox11.Style" xml:space="preserve">
    <value>font-family: 宋体; font-size: 9pt; text-align: center; vertical-align: middle; ddo-char-set: 134</value>
  </data>
  <data name="TextBox11.Text" xml:space="preserve">
    <value>TextBox1</value>
  </data>
  <data name="TextBox11.Top" type="System.Single, mscorlib">
    <value>0</value>
  </data>
  <data name="TextBox11.Width" type="System.Single, mscorlib">
    <value>0.6875</value>
  </data>
  <data name="TextBox2.Height" type="System.Single, mscorlib">
    <value>0.25</value>
  </data>
  <data name="TextBox2.Left" type="System.Single, mscorlib">
    <value>3.125</value>
  </data>
  <data name="TextBox2.OutputFormat" xml:space="preserve">
    <value>0.00####</value>
  </data>
  <data name="TextBox2.Style" xml:space="preserve">
    <value>font-family: 宋体; font-size: 9.75pt; text-align: center; vertical-align: middle; ddo-char-set: 0</value>
  </data>
  <data name="TextBox2.SummaryGroup" xml:space="preserve">
    <value>GroupHeader2</value>
  </data>
  <data name="TextBox2.Text" xml:space="preserve">
    <value>合计</value>
  </data>
  <data name="TextBox2.Top" type="System.Single, mscorlib">
    <value>0</value>
  </data>
  <data name="TextBox2.Width" type="System.Single, mscorlib">
    <value>0.75</value>
  </data>
  <data name="TextBox8.Height" type="System.Single, mscorlib">
    <value>0.25</value>
  </data>
  <data name="TextBox8.Left" type="System.Single, mscorlib">
    <value>0.0625</value>
  </data>
  <data name="TextBox8.Style" xml:space="preserve">
    <value>font-family: 宋体; font-size: 9.75pt; text-align: center; vertical-align: middle; ddo-char-set: 0</value>
  </data>
  <data name="TextBox8.Text" xml:space="preserve">
    <value>合计</value>
  </data>
  <data name="TextBox8.Top" type="System.Single, mscorlib">
    <value>0</value>
  </data>
  <data name="TextBox8.Width" type="System.Single, mscorlib">
    <value>1.375</value>
  </data>
  <data name="TextBox9.Height" type="System.Single, mscorlib">
    <value>0.25</value>
  </data>
  <data name="TextBox9.Left" type="System.Single, mscorlib">
    <value>1.4375</value>
  </data>
  <data name="TextBox9.Style" xml:space="preserve">
    <value>font-family: 宋体; font-size: 9.75pt; text-align: center; vertical-align: middle; ddo-char-set: 0</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="TextBox9.Text" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="TextBox9.Top" type="System.Single, mscorlib">
    <value>0</value>
  </data>
  <data name="TextBox9.Width" type="System.Single, mscorlib">
    <value>1.6875</value>
  </data>
  <data name="TextBox1.Height" type="System.Single, mscorlib">
    <value>0.25</value>
  </data>
  <data name="TextBox1.Left" type="System.Single, mscorlib">
    <value>0.0625</value>
  </data>
  <data name="TextBox1.Style" xml:space="preserve">
    <value>text-align: center; vertical-align: middle</value>
  </data>
  <data name="TextBox1.Text" xml:space="preserve">
    <value>药品名称</value>
  </data>
  <data name="TextBox1.Top" type="System.Single, mscorlib">
    <value>0.6875</value>
  </data>
  <data name="TextBox1.Width" type="System.Single, mscorlib">
    <value>1.375</value>
  </data>
  <data name="TextBox3.Height" type="System.Single, mscorlib">
    <value>0.25</value>
  </data>
  <data name="TextBox3.Left" type="System.Single, mscorlib">
    <value>1.4375</value>
  </data>
  <data name="TextBox3.Style" xml:space="preserve">
    <value>text-align: center; vertical-align: middle</value>
  </data>
  <data name="TextBox3.Text" xml:space="preserve">
    <value>基本药物</value>
  </data>
  <data name="TextBox3.Top" type="System.Single, mscorlib">
    <value>0.6875</value>
  </data>
  <data name="TextBox3.Width" type="System.Single, mscorlib">
    <value>0.6875</value>
  </data>
  <data name="TextBox4.Height" type="System.Single, mscorlib">
    <value>0.25</value>
  </data>
  <data name="TextBox4.Left" type="System.Single, mscorlib">
    <value>2.125</value>
  </data>
  <data name="TextBox4.Style" xml:space="preserve">
    <value>text-align: center; vertical-align: middle</value>
  </data>
  <data name="TextBox4.Text" xml:space="preserve">
    <value>单价</value>
  </data>
  <data name="TextBox4.Top" type="System.Single, mscorlib">
    <value>0.6875</value>
  </data>
  <data name="TextBox4.Width" type="System.Single, mscorlib">
    <value>0.4375</value>
  </data>
  <data name="TextBox7.Height" type="System.Single, mscorlib">
    <value>0.25</value>
  </data>
  <data name="TextBox7.Left" type="System.Single, mscorlib">
    <value>2.5625</value>
  </data>
  <data name="TextBox7.Style" xml:space="preserve">
    <value>text-align: center; vertical-align: middle</value>
  </data>
  <data name="TextBox7.Text" xml:space="preserve">
    <value>数量</value>
  </data>
  <data name="TextBox7.Top" type="System.Single, mscorlib">
    <value>0.6875</value>
  </data>
  <data name="TextBox7.Width" type="System.Single, mscorlib">
    <value>0.5625</value>
  </data>
  <data name="TextBox10.Height" type="System.Single, mscorlib">
    <value>0.25</value>
  </data>
  <data name="TextBox10.Left" type="System.Single, mscorlib">
    <value>3.125</value>
  </data>
  <data name="TextBox10.Style" xml:space="preserve">
    <value>text-align: center; vertical-align: middle</value>
  </data>
  <data name="TextBox10.Text" xml:space="preserve">
    <value>发药总额</value>
  </data>
  <data name="TextBox10.Top" type="System.Single, mscorlib">
    <value>0.6875</value>
  </data>
  <data name="TextBox10.Width" type="System.Single, mscorlib">
    <value>0.75</value>
  </data>
  <data name="Label1.Height" type="System.Single, mscorlib">
    <value>0.2083333</value>
  </data>
  <data name="Label1.HyperLink" xml:space="preserve">
    <value />
  </data>
  <data name="Label1.Left" type="System.Single, mscorlib">
    <value>1.8125</value>
  </data>
  <data name="Label1.Style" xml:space="preserve">
    <value>font-family: 宋体; font-size: 9pt; text-align: right; vertical-align: bottom; ddo-char-set: 0</value>
  </data>
  <data name="Label1.Text" xml:space="preserve">
    <value>打印时间:</value>
  </data>
  <data name="Label1.Top" type="System.Single, mscorlib">
    <value>0.3125</value>
  </data>
  <data name="Label1.Width" type="System.Single, mscorlib">
    <value>2.041667</value>
  </data>
  <data name="TextBox12.Height" type="System.Single, mscorlib">
    <value>0.1875</value>
  </data>
  <data name="TextBox12.Left" type="System.Single, mscorlib">
    <value>0.0625</value>
  </data>
  <data name="TextBox12.Style" xml:space="preserve">
    <value>font-family: 宋体; font-size: 9.75pt; text-align: center; vertical-align: middle; ddo-char-set: 0</value>
  </data>
  <data name="TextBox12.Text" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="TextBox12.Top" type="System.Single, mscorlib">
    <value>0.5</value>
  </data>
  <data name="TextBox12.Width" type="System.Single, mscorlib">
    <value>3.8125</value>
  </data>
  <data name="Label2.Height" type="System.Single, mscorlib">
    <value>0.2916667</value>
  </data>
  <data name="Label2.HyperLink" xml:space="preserve">
    <value />
  </data>
  <data name="Label2.Left" type="System.Single, mscorlib">
    <value>1.25</value>
  </data>
  <data name="Label2.Style" xml:space="preserve">
    <value>font-family: 黑体; font-size: 15pt; font-weight: normal; text-align: center; text-decoration: none; vertical-align: middle; ddo-char-set: 134</value>
  </data>
  <data name="Label2.Text" xml:space="preserve">
    <value>处   方   皮</value>
  </data>
  <data name="Label2.Top" type="System.Single, mscorlib">
    <value>0</value>
  </data>
  <data name="Label2.Width" type="System.Single, mscorlib">
    <value>1.5</value>
  </data>
  <data name="TextBox13.Height" type="System.Single, mscorlib">
    <value>0.25</value>
  </data>
  <data name="TextBox13.Left" type="System.Single, mscorlib">
    <value>3.125</value>
  </data>
  <data name="TextBox13.OutputFormat" xml:space="preserve">
    <value>0.00####</value>
  </data>
  <data name="TextBox13.Style" xml:space="preserve">
    <value>font-family: 宋体; font-size: 9.75pt; text-align: center; vertical-align: middle; ddo-char-set: 0</value>
  </data>
  <data name="TextBox13.SummaryGroup" xml:space="preserve">
    <value>GroupHeader1</value>
  </data>
  <data name="TextBox13.Text" xml:space="preserve">
    <value>合计</value>
  </data>
  <data name="TextBox13.Top" type="System.Single, mscorlib">
    <value>0</value>
  </data>
  <data name="TextBox13.Width" type="System.Single, mscorlib">
    <value>0.75</value>
  </data>
  <data name="TextBox14.Height" type="System.Single, mscorlib">
    <value>0.25</value>
  </data>
  <data name="TextBox14.Left" type="System.Single, mscorlib">
    <value>0.0625</value>
  </data>
  <data name="TextBox14.Style" xml:space="preserve">
    <value>font-family: 宋体; font-size: 9.75pt; text-align: center; vertical-align: middle; ddo-char-set: 0</value>
  </data>
  <data name="TextBox14.Text" xml:space="preserve">
    <value>总计</value>
  </data>
  <data name="TextBox14.Top" type="System.Single, mscorlib">
    <value>0</value>
  </data>
  <data name="TextBox14.Width" type="System.Single, mscorlib">
    <value>1.375</value>
  </data>
  <data name="TextBox15.Height" type="System.Single, mscorlib">
    <value>0.25</value>
  </data>
  <data name="TextBox15.Left" type="System.Single, mscorlib">
    <value>1.4375</value>
  </data>
  <data name="TextBox15.Style" xml:space="preserve">
    <value>font-family: 宋体; font-size: 9.75pt; text-align: center; vertical-align: middle; ddo-char-set: 0</value>
  </data>
  <data name="TextBox15.Text" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="TextBox15.Top" type="System.Single, mscorlib">
    <value>0</value>
  </data>
  <data name="TextBox15.Width" type="System.Single, mscorlib">
    <value>1.6875</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="$this.ScriptEditorPositionForUndo" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>0, 0</value>
  </metadata>
  <metadata name="$this.ScriptEditorPositionForRedo" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>0, 0</value>
  </metadata>
  <metadata name="$this.TrayLargeIcon" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
</root>