﻿Imports System.Collections.Generic
Imports System.ComponentModel
Imports System.Data
Imports System.Drawing
Imports System.Text
Imports System.Windows.Forms
Imports System.Deployment.Application



Public Class pupdate

    Private Sub pupdate_Load(ByVal sender As Object, ByVal e As EventArgs) Handles MyBase.Load
        downloadStatus.Text = ""
        BeginUpdate()
    End Sub
    Private sizeOfUpdate As Long = 0



    Private Function GetProgressString(ByVal state As System.Deployment.Application.DeploymentProgressState) As String
        If state = System.Deployment.Application.DeploymentProgressState.DownloadingApplicationFiles Then
            Return "正在下载组成应该应用程序的DLL与数据文件"
        ElseIf state = DeploymentProgressState.DownloadingApplicationInformation Then
            Return "正在下载应该程序清单"
        Else
            Return "配置程序清单"
        End If
    End Function

    '开始更新
    Private Sub BeginUpdate()
        Dim ad As ApplicationDeployment = ApplicationDeployment.CurrentDeployment
        AddHandler ad.UpdateCompleted, New AsyncCompletedEventHandler(AddressOf ad_UpdateCompleted)
        AddHandler ad.UpdateProgressChanged, New DeploymentProgressChangedEventHandler(AddressOf ad_UpdateProgressChanged)
        ad.UpdateAsync()
    End Sub

    Private Sub ad_UpdateProgressChanged(ByVal sender As Object, ByVal e As DeploymentProgressChangedEventArgs)
        If GetProgressString(e.State) = "正在下载组成应该应用程序的DLL与数据文件" Then
            If e.BytesCompleted = 0 Then
                downloadStatus.Text = "更新程序准备中,请稍等"
                Exit Sub
            End If
            Dim progressText As [String] = [String].Format("已完成下载{1:#.###}K 中的{0:#.###}K -  已完成：{2:#.###}%", e.BytesCompleted / 1024, e.BytesTotal / 1024, e.ProgressPercentage)
            downloadStatus.Text = progressText
            ProgressStatus.Value = e.ProgressPercentage
            If e.ProgressPercentage = 100 Then
                downloadStatus.Text = "更新程序下载完毕,系统正在更新中,请稍等"
                ProgressStatus.Value = e.ProgressPercentage
            End If
        End If
    End Sub

    Private Sub ad_UpdateCompleted(ByVal sender As Object, ByVal e As AsyncCompletedEventArgs)
        Dim dr As DialogResult = DialogResult.None
        If e.Cancelled Then
            dr = MessageBox.Show("应用程序更新已被取消！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
            If DialogResult.OK = dr Then
                Me.Close()

            End If
        ElseIf e.[Error] IsNot Nothing Then
            dr = MessageBox.Show("错误：对不起，不能更新当前版本！原因：" & vbLf & e.[Error].Message & vbLf & "请与软件发布者联系！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
            If DialogResult.OK = dr Then
                Me.Close()
            End If
        End If

        If MessageBox.Show("更新成功！是否现在重启应用程序？(如有未保存数据请先保存然后自行重启程序)", "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Information) = Windows.Forms.DialogResult.OK Then
            Application.Restart()
        Else
            Me.Close()
        End If
    End Sub



End Class

