﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class JySfDy
    Inherits HisControl.BaseForm

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel()
        Me.TestXmCb = New CustomControl.MyDtComobo()
        Me.SfXmCb = New CustomControl.MyDtComobo()
        Me.TjDyBt = New CustomControl.MyButton()
        Me.DelDyBt = New CustomControl.MyButton()
        Me.MyGrid1 = New CustomControl.MyGrid()
        Me.TableLayoutPanel1.SuspendLayout()
        Me.SuspendLayout()
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.ColumnCount = 6
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 28.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 400.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 400.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 100.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 100.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 28.0!))
        Me.TableLayoutPanel1.Controls.Add(Me.TestXmCb, 1, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.SfXmCb, 2, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.TjDyBt, 3, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.DelDyBt, 4, 1)
        Me.TableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Top
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(0, 0)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.RowCount = 4
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 8.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 41.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 47.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 20.0!))
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(1148, 61)
        Me.TableLayoutPanel1.TabIndex = 0
        '
        'TestXmCb
        '
        Me.TestXmCb.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TestXmCb.Captain = "检验项目"
        Me.TestXmCb.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.TestXmCb.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.TestXmCb.CaptainWidth = 60.0!
        Me.TestXmCb.DataSource = Nothing
        Me.TestXmCb.ItemHeight = 18
        Me.TestXmCb.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.TestXmCb.Location = New System.Drawing.Point(31, 18)
        Me.TestXmCb.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.TestXmCb.MinimumSize = New System.Drawing.Size(0, 20)
        Me.TestXmCb.Name = "TestXmCb"
        Me.TestXmCb.ReadOnly = False
        Me.TestXmCb.Size = New System.Drawing.Size(394, 20)
        Me.TestXmCb.TabIndex = 0
        Me.TestXmCb.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'SfXmCb
        '
        Me.SfXmCb.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.SfXmCb.Captain = "收费项目"
        Me.SfXmCb.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.SfXmCb.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.SfXmCb.CaptainWidth = 60.0!
        Me.SfXmCb.DataSource = Nothing
        Me.SfXmCb.ItemHeight = 18
        Me.SfXmCb.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.SfXmCb.Location = New System.Drawing.Point(431, 18)
        Me.SfXmCb.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.SfXmCb.MinimumSize = New System.Drawing.Size(0, 20)
        Me.SfXmCb.Name = "SfXmCb"
        Me.SfXmCb.ReadOnly = False
        Me.SfXmCb.Size = New System.Drawing.Size(394, 20)
        Me.SfXmCb.TabIndex = 1
        Me.SfXmCb.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'TjDyBt
        '
        Me.TjDyBt.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TjDyBt.ButtonImageSize = CustomControl.MyButton.imageSize.large
        Me.TjDyBt.DialogResult = System.Windows.Forms.DialogResult.None
        Me.TjDyBt.Location = New System.Drawing.Point(831, 11)
        Me.TjDyBt.Name = "TjDyBt"
        Me.TjDyBt.Size = New System.Drawing.Size(94, 35)
        Me.TjDyBt.TabIndex = 2
        Me.TjDyBt.Tag = "添加"
        Me.TjDyBt.Text = "添加"
        '
        'DelDyBt
        '
        Me.DelDyBt.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.DelDyBt.ButtonImageSize = CustomControl.MyButton.imageSize.large
        Me.DelDyBt.DialogResult = System.Windows.Forms.DialogResult.None
        Me.DelDyBt.Location = New System.Drawing.Point(931, 11)
        Me.DelDyBt.Name = "DelDyBt"
        Me.DelDyBt.Size = New System.Drawing.Size(94, 35)
        Me.DelDyBt.TabIndex = 3
        Me.DelDyBt.Tag = "删除"
        Me.DelDyBt.Text = "删除"
        '
        'MyGrid1
        '
        Me.MyGrid1.CanCustomCol = False
        Me.MyGrid1.Col = 0
        Me.MyGrid1.ColumnFooters = False
        Me.MyGrid1.DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveRight
        Me.MyGrid1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.MyGrid1.FetchRowStyles = False
        Me.MyGrid1.Location = New System.Drawing.Point(0, 61)
        Me.MyGrid1.Margin = New System.Windows.Forms.Padding(0)
        Me.MyGrid1.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.MyGrid1.Name = "MyGrid1"
        Me.MyGrid1.Size = New System.Drawing.Size(1148, 437)
        Me.MyGrid1.TabAction = C1.Win.C1TrueDBGrid.TabActionEnum.ControlNavigation
        Me.MyGrid1.TabIndex = 1
        Me.MyGrid1.Xmlpath = Nothing
        '
        'JySfDy
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1148, 498)
        Me.Controls.Add(Me.MyGrid1)
        Me.Controls.Add(Me.TableLayoutPanel1)
        Me.Name = "JySfDy"
        Me.Text = "检验项目与收费对应关系"
        Me.TableLayoutPanel1.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents TableLayoutPanel1 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents TestXmCb As CustomControl.MyDtComobo
    Friend WithEvents SfXmCb As CustomControl.MyDtComobo
    Friend WithEvents TjDyBt As CustomControl.MyButton
    Friend WithEvents DelDyBt As CustomControl.MyButton
    Friend WithEvents MyGrid1 As CustomControl.MyGrid
End Class
