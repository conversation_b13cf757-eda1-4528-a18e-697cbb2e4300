﻿/**  版本信息模板在安装目录下，可自行修改。
* M_Zd_Qx1.cs
*
* 功 能： N/A
* 类 名： M_Zd_Qx1
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016-08-15 09:56:32   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace ModelOld
{
	/// <summary>
	/// M_Zd_Qx1:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class M_Zd_Qx1
	{
		public M_Zd_Qx1()
		{}
		#region Model
		private string _yy_code;
		private string _glz_code;
		private string _glz_name;
		private string _glz_jc;
		private string _glz_memo;
		/// <summary>
		/// 
		/// </summary>
		public string Yy_Code
		{
			set{ _yy_code=value;}
			get{return _yy_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Glz_Code
		{
			set{ _glz_code=value;}
			get{return _glz_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Glz_Name
		{
			set{ _glz_name=value;}
			get{return _glz_name;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Glz_Jc
		{
			set{ _glz_jc=value;}
			get{return _glz_jc;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Glz_Memo
		{
			set{ _glz_memo=value;}
			get{return _glz_memo;}
		}
		#endregion Model

	}
}

