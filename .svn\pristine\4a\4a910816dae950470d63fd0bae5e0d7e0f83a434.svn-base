﻿Imports BaseClass
Imports System.Windows.Forms
Imports System.Drawing
Imports CustomControl
Imports Microsoft.VisualBasic.Strings
Imports C1.Win.C1Command    '待定

Public Class Zd_ZhiKong2

#Region "变量定义"
    Dim My_Table As New DataTable
    Dim My_Row As DataRow                                           '当 前 行
    Dim My_Cm As CurrencyManager                                    '同步指针
    Dim V_Insert, vResult As Boolean                                         '是否新增人员
    'Dim V_Jt_Insert As Boolean = False                              '是否新增家庭

    Dim vMbCode, vMblbCode As String
    Dim V_Zk_code As String

    Dim BllEmr_Mb As New BLLOld.B_Emr_Mb
    Dim BllEmr_Mblb As New BLLOld.B_Emr_Mblb
    Dim ModelEmr_ZhiKong1 As New ModelOld.M_Emr_ZhiKong1
    Dim BllEmr_ZhiKong1 As New BLLOld.B_Emr_ZhiKong1
    Dim BllEmr_ZhiKong2 As New BLLOld.B_Emr_ZhiKong2
#End Region

#Region "传参"
    Dim Rinsert As Boolean
    Dim Rrow As DataRow
    Dim RZbtb As DataTable
    Dim Rrc As C_RowChange
#End Region

    Public Sub New(ByVal tinsert As Boolean, ByRef trow As DataRow, ByRef tZbtb As DataTable, ByRef trc As C_RowChange)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        Rinsert = tinsert
        Rrow = trow
        RZbtb = tZbtb
        Rrc = trc

        ' 在 InitializeComponent() 调用之后添加任何初始化。
    End Sub

    Private Sub Zd_Ry4_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        If Rrc IsNot Nothing Then
            AddHandler Rrc.RowChanged, AddressOf Data_Show
        End If
        Call Form_Init()
        If Rinsert = True Then
            V_Zk_code = BllEmr_ZhiKong1.MaxCode()
            Call Data_Clear()
        Else
            V_Zk_code = Trim(Rrow.Item("Zk_code") & "")
            Call Data_Show(Rrow)
        End If
        Call Init_Data()
        AddHandler m_Rc.GridMoveEvent, AddressOf GridMove
    End Sub

    Private Sub Zd_Ry4_FormClosed(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosedEventArgs) Handles Me.FormClosed
        If Rrc IsNot Nothing Then
            RemoveHandler Rrc.RowChanged, AddressOf Data_Show
        End If
        If m_Rc IsNot Nothing Then
            RemoveHandler m_Rc.GridMoveEvent, AddressOf GridMove
        End If
    End Sub

#Region "窗体__事件"

    Private Sub Form_Init()
        CodeTextBox.Enabled = False
        With MblbMyDtComobo1
            .DataView = BllEmr_Mblb.GetList("").Tables(0).DefaultView
            .Init_Colum("Mblb_Name", "模板类别名称", 120, "左")
            .Init_Colum("Mblb_Jc", "模板类别简称", 0, "左")
            .Init_Colum("Mblb_Code", "模板类别编码", 0, "中")
            .DisplayMember = "Mblb_Name"
            .ValueMember = "Mblb_Code"
            .RowFilterNotTextNull = "Mblb_Jc"
            .RowFilterTextNull = ""
            .DroupDownWidth = .Width - .CaptainWidth
            .MaxDropDownItems = 15
            .SelectedValue = -1
        End With


        '按扭初始化
        Button1.Top = 2
        Button2.Location = New Point(Button1.Left + Button1.Width + 2, Button1.Top)

        'id, Zk_Code, Mx_Name, Kf_Pz, Kf_PzNr, Zk_Kf, Memo
        'MyGrid1初始化
        With MyGrid1
            .Clear()
            .Init_Column("编码", "id", "0 ", "中", "", False)
            .Init_Column("质控编码", "Zk_Code", "0 ", "中", "", False)
            .Init_Column("质控内容", "Mx_Name", "200 ", "中", "", False)
            .Init_Column("评分方式", "Kf_Pz", "150 ", "中", "Combobox", False)
            .Columns(3).ValueItems.Values.Add(New C1.Win.C1TrueDBGrid.ValueItem("1", "按等级扣分"))
            .Columns(3).ValueItems.Values.Add(New C1.Win.C1TrueDBGrid.ValueItem("2", "单项扣分"))
            .Columns(3).ValueItems.Values.Add(New C1.Win.C1TrueDBGrid.ValueItem("3", "多项扣分"))
            .Init_Column("质控等级编码", "ZkDj_Code", "0", "中", "", False)
            .Init_Column("扣分项", "Kf_PzNr", "150", "中", "", False)
            .Init_Column("扣除分数", "Zk_Kf", "100", "中", "", False)
            .Init_Column("备注", "Memo", "90", "中", "", False)
        End With

    End Sub

    Private Sub Init_Data()

        '下面为必须
        My_Table = BllEmr_ZhiKong2.GetList(" Zk_code = '" & V_Zk_code & "'").Tables(0)  '待定
        My_Cm = CType(BindingContext(My_Table, ""), CurrencyManager)
        MyGrid1.DataTable = My_Table
        'MyGrid1.Select()
    End Sub

#End Region

#Region "动作__函数"

    Private Sub GridMove(ByVal s As String)
        With MyGrid1
            If MyGrid1.RowCount = 0 Then Exit Sub
            Select Case s
                Case "最前"
                    .MoveFirst()
                Case "上移"
                    .MovePrevious()
                Case "下移"
                    .MoveNext()
                Case "最后"
                    .MoveLast()

            End Select
        End With
    End Sub
#End Region

#Region "控件__动作"
    Private Sub Button_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click, Button2.Click
        Select Case sender.tag
            Case "保存"
                If MblbMyDtComobo1.SelectedValue & "" = "" Then
                    MsgBox("模板类别必须选择！", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
                    MblbMyDtComobo1.Select()
                    Exit Sub
                End If

                Dim vCount As Integer = BllEmr_ZhiKong1.GetRecordCount("Mblb_Code='" & MblbMyDtComobo1.SelectedValue &
                IIf(MbMyDtComobo2.SelectedValue Is Nothing, "' AND Mb_Code is null ", "' AND Mb_Code='" & MbMyDtComobo2.SelectedValue & "'"))
                If Rinsert = True Then                    '增加记录
                    If vCount > 0 Then
                        MsgBox("该质控信息已经存在！", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示")
                        MblbMyDtComobo1.Select()
                        Exit Sub
                    End If
                    Call Data_Add()
                    Call Data_Clear()
                Else                    '编辑记录
                    If MbMyDtComobo2.SelectedValue <> vMbCode Or MblbMyDtComobo1.SelectedValue <> vMblbCode Then
                        If vCount > 0 Then
                            MsgBox("该质控信息已经存在！", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示")
                            MblbMyDtComobo1.Select()
                            Exit Sub
                        End If
                    End If
                    Call Data_Edit()
                End If
            Case "取消"
                Me.Close()
        End Select
    End Sub

    Private Sub Comm_Click(ByVal sender As Object, ByVal e As C1.Win.C1Command.ClickEventArgs) Handles Comm1.Click, Comm2.Click, Comm3.Click
        Select Case sender.text
            Case "增加", "保存"
                vResult = False
                If MblbMyDtComobo1.SelectedValue & "" = "" Then
                    MsgBox("模板类别必须选择！", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
                    MblbMyDtComobo1.Select()
                    Exit Sub
                End If
                Dim vCount As Integer = BllEmr_ZhiKong1.GetRecordCount("Mblb_Code='" & MblbMyDtComobo1.SelectedValue &
                              IIf(MbMyDtComobo2.SelectedValue Is Nothing, "' AND Mb_Code is null ", "' AND Mb_Code='" & MbMyDtComobo2.SelectedValue & "'"))

                If Rinsert = True Then                    '增加记录
                    If vCount > 0 Then
                        MsgBox("该质控信息已经存在！", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示")
                        MblbMyDtComobo1.Select()
                        Exit Sub
                    End If
                    Call Data_Add()
                    Rinsert = False
                Else                    '编辑记录
                    If MbMyDtComobo2.SelectedValue & "" <> vMbCode Or MblbMyDtComobo1.SelectedValue & "" <> vMblbCode Then
                        If vCount > 0 Then
                            MsgBox("该质控信息已经存在！", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示")
                            MblbMyDtComobo1.Select()
                            Exit Sub
                        End If
                    End If
                    Call Data_Edit()
                End If
                vResult = True
                If sender.Text = "增加" Then
                    Call P_ShowData("增加")
                End If

            Case "删除"
                Beep()
                If MyGrid1.Splits(0).Rows.Count = 0 Then Exit Sub
                Call P_Del_Data()
            Case "刷新"
                '初始化
                Call Init_Data()
                MyGrid1.Select()
                MyGrid1.MoveFirst()
        End Select

    End Sub

    Private Sub MblbMyDtComobo1_RowChange(sender As Object, e As System.EventArgs) Handles MblbMyDtComobo1.RowChange
        If MblbMyDtComobo1.SelectedValue & "" <> "" Then
            With MbMyDtComobo2
                .DataView = BllEmr_Mb.GetList("Emr_Mb.Mblb_Code='" & MblbMyDtComobo1.SelectedValue & "'").Tables(0).DefaultView
                .Init_Colum("Mb_Name", "模板名称", 120, "左")
                .Init_Colum("Mb_Jc", "模板简称", 0, "左")
                .Init_Colum("Mb_Code", "模板编码", 0, "中")
                .DisplayMember = "Mb_Name"
                .ValueMember = "Mb_Code"
                .RowFilterNotTextNull = "Mb_Jc"
                .RowFilterTextNull = ""
                .DroupDownWidth = .Width - .CaptainWidth
                .MaxDropDownItems = 15
                .SelectedValue = -1
                .Text = ""
            End With
        End If
    End Sub

    Private Sub Move_Click(ByVal sender As Object, ByVal e As C1.Win.C1Command.ClickEventArgs) Handles Move1.Click, Move2.Click, Move3.Click, Move4.Click, Move5.Click
        If sender.text = "新增" Then                            '增加状态
            Call Data_Clear()
            My_Table.Clear() '待定
        Else
            Rrc.GridMove(sender.text)
            Init_Data()
        End If
    End Sub


    Private Sub MyGrid1_MouseUp(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles MyGrid1.MouseUp
        If e.Button = Windows.Forms.MouseButtons.Right Then Call Comm_Click(Button1, Nothing) : If vResult = True Then Call P_ShowData("DBGrid")
    End Sub

    Private Sub MyGrid1_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles MyGrid1.KeyDown
        Select Case e.KeyCode
            Case Keys.Return
                Call Comm_Click(Button1, Nothing)
                If vResult = True Then Call P_ShowData("DBGrid")
            Case Keys.Insert
                Call Comm_Click(Button1, Nothing)
                If vResult = True Then Call P_ShowData("增加")
            Case Keys.Delete
                Call Comm_Click(Comm2, Nothing)
        End Select
    End Sub

    Dim m_Rc As New BaseClass.C_RowChange
    Private Sub MyGrid1_RowColChange(ByVal sender As System.Object, ByVal e As C1.Win.C1TrueDBGrid.RowColChangeEventArgs) Handles MyGrid1.RowColChange
        If (MyGrid1.Row + 1) > MyGrid1.RowCount Then
        Else
            My_Row = My_Cm.List(MyGrid1.Row).Row
            m_Rc.ChangeRow(My_Row)
        End If
    End Sub


#End Region

#Region "数据__操作"
    '待定
    Private Sub Data_Add()
        'id, Zk_Code, Mx_Name, Kf_Pz, Kf_PzNr, Zk_Kf, Memo
        Dim My_NewRow As DataRow = RZbtb.NewRow
        With My_NewRow
            .Item("Zk_code") = Trim(CodeTextBox.Text & "")
            .Item("Mblb_code") = MblbMyDtComobo1.SelectedValue
            .Item("Mblb_Name") = Trim(MblbMyDtComobo1.Text & "")
            .Item("Mb_code") = MbMyDtComobo2.SelectedValue
            .Item("Mb_Name") = Trim(MbMyDtComobo2.Text & "")
        End With
        V_Zk_code = Trim(CodeTextBox.Text & "")
        '数据保存
        Try
            RZbtb.Rows.Add(My_NewRow)
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            Exit Sub
        Finally
        End Try

        '数据更新
        Try
            With ModelEmr_ZhiKong1
                .Zk_Code = My_NewRow.Item("Zk_code")
                .Mblb_Code = My_NewRow.Item("Mblb_code")
                .Mb_Code = IIf(IsDBNull(My_NewRow.Item("Mb_code")) = True, Nothing, My_NewRow.Item("Mb_code"))
            End With
            BllEmr_ZhiKong1.Add(ModelEmr_ZhiKong1)
            My_NewRow.AcceptChanges()
            RZbtb.AcceptChanges()
            Rrc.GridMove("最后")
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            MblbMyDtComobo1.Select()
            Exit Sub
        Finally
        End Try

        '数据清空

        'Call Data_Clear()
    End Sub

    Private Sub Data_Edit()
        Dim My_Row As DataRow = Rrow
        Try
            With My_Row
                .BeginEdit()
                .Item("Zk_code") = Trim(CodeTextBox.Text & "")
                .Item("Mblb_code") = MblbMyDtComobo1.SelectedValue
                .Item("Mblb_Name") = Trim(MblbMyDtComobo1.Text & "")
                .Item("Mb_code") = MbMyDtComobo2.SelectedValue
                .Item("Mb_Name") = Trim(MbMyDtComobo2.Text & "")


                vMbCode = .Item("Mb_code") & ""
                vMblbCode = .Item("Mblb_code") & ""
                .EndEdit()
            End With
            V_Zk_code = Trim(CodeTextBox.Text & "")
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Critical, "提示:")
            My_Row.CancelEdit()
            Exit Sub
        Finally
            Me.MblbMyDtComobo1.Select()
        End Try

        '数据更新
        Try
            With ModelEmr_ZhiKong1
                .Zk_Code = My_Row.Item("Zk_code")
                .Mblb_Code = My_Row.Item("Mblb_code")
                .Mb_Code = IIf(IsDBNull(My_Row.Item("Mb_code")) = True, Nothing, My_Row.Item("Mb_code"))


            End With
            BllEmr_ZhiKong1.Update(ModelEmr_ZhiKong1)
            My_Row.AcceptChanges()
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
        Finally
            Me.MblbMyDtComobo1.Select()
        End Try
    End Sub

    Private Sub Data_Clear()
        Rinsert = True
        Move5.Enabled = False                                   '新增记录
        CodeTextBox.Text = BllEmr_ZhiKong1.MaxCode()
        MblbMyDtComobo1.SelectedIndex = -1
        MbMyDtComobo2.SelectedIndex = -1
        Call Show_Label()
    End Sub

    Private Sub Data_Show(ByVal tmp_Row As DataRow)
        Rinsert = False
        Move5.Enabled = True
        Rrow = tmp_Row                                   '更新Rrow
        With tmp_Row
            CodeTextBox.Text = .Item("Zk_code") & ""
            V_Zk_code = CodeTextBox.Text
            MblbMyDtComobo1.SelectedValue = .Item("Mblb_code") & ""
            If IsDBNull(.Item("Mb_code")) = True Then
                MbMyDtComobo2.SelectedIndex = -1
            Else
                MbMyDtComobo2.SelectedValue = .Item("Mb_code")
            End If

            vMbCode = .Item("Mb_code") & ""
            vMblbCode = .Item("Mblb_code") & ""
        End With
        Call Show_Label()
    End Sub

    Private Sub Show_Label()
        If Rinsert = True Then
            T_Label2.Text = "新增"
        Else
            T_Label2.Text = IIf(RZbtb.Rows.Count() = 0, "0", CStr((RZbtb.Rows.IndexOf(Rrow)) + 1))

        End If
        T_Label3.Text = "∑=" + RZbtb.Rows.Count.ToString
        Me.MblbMyDtComobo1.Select()
    End Sub

    Private Sub P_Del_Data()
        Beep()
        My_Row = My_Cm.List(MyGrid1.Row).Row
        Dim Code As Integer
        Try
            If MsgBox("是否删除：标准 = " + Me.MyGrid1.Columns("Mx_Name").Value + " ", MsgBoxStyle.Information + MsgBoxStyle.OkCancel, "提示:") = MsgBoxResult.Cancel Then Exit Sub
            Code = My_Row.Item("id")
            BllEmr_ZhiKong2.Delete(Code)
            MyGrid1.Delete()
            My_Row.AcceptChanges()
            T_Label.Text = "∑=" + MyGrid1.Splits(0).Rows.Count.ToString
        Catch ex As Exception
            Beep()
            MsgBox("错误:" + ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
        Finally
            MyGrid1.Select()
        End Try
    End Sub

#End Region

#Region "自定义函数"

    Private Sub P_ShowData(ByVal V_Lb As String)
        If V_Lb = "增加" Then
            V_Insert = True
        Else
            If MyGrid1.RowCount > 0 Then V_Insert = False Else V_Insert = True
        End If

        If MblbMyDtComobo1.SelectedValue = "" Then
            Exit Sub
        ElseIf V_Insert = False Then
            My_Row = My_Cm.List(MyGrid1.Row).Row
        End If

        Dim vform As Form = New Zd_ZhiKong3(V_Insert, My_Row, My_Table, m_Rc, V_Zk_code)
        vform.Owner = Me
        vform.StartPosition = FormStartPosition.CenterScreen
        vform.ShowDialog()
        MyGrid1.Select()
    End Sub

#End Region


End Class
