﻿/**  版本信息模板在安装目录下，可自行修改。
* M_Materials_Move1.cs
*
* 功 能： N/A
* 类 名： M_Materials_Move1
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016-12-05 13:54:39   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace ModelOld
{
	/// <summary>
	/// 物资移库主表
	/// </summary>
	[Serializable]
	public partial class M_Materials_Move1
	{
		public M_Materials_Move1()
		{}
		#region Model
		private string _m_move_code;
		private string _old_materialswh_code;
		private string _new_materialswh_code;
		private DateTime? _move_date;
		private DateTime? _input_date;
		private DateTime? _finish_date;
		private string _jsr_code;
        private string _jsr_name;
		private string _m_move_memo;
		private string _ordersstatus;
		private decimal? _totalmoney;
		/// <summary>
		/// yyMMdd+5位流水号
		/// </summary>
		public string M_Move_Code
		{
			set{ _m_move_code=value;}
			get{return _m_move_code;}
		}
		/// <summary>
		/// 原库房编码
		/// </summary>
		public string Old_MaterialsWh_Code
		{
			set{ _old_materialswh_code=value;}
			get{return _old_materialswh_code;}
		}
		/// <summary>
		/// 新库房编码
		/// </summary>
		public string New_MaterialsWh_Code
		{
			set{ _new_materialswh_code=value;}
			get{return _new_materialswh_code;}
		}
		/// <summary>
		/// 移库日期
		/// </summary>
		public DateTime? Move_Date
		{
			set{ _move_date=value;}
			get{return _move_date;}
		}
		/// <summary>
		/// 录入时间
		/// </summary>
		public DateTime? Input_Date
		{
			set{ _input_date=value;}
			get{return _input_date;}
		}
		/// <summary>
		/// 完成时间
		/// </summary>
		public DateTime? Finish_Date
		{
			set{ _finish_date=value;}
			get{return _finish_date;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Jsr_Code
		{
			set{ _jsr_code=value;}
			get{return _jsr_code;}
		}
        /// <summary>
        /// 
        /// </summary>
        public string Jsr_Name
        {
            set { _jsr_name = value; }
            get { return _jsr_name; }
        }
		/// <summary>
		/// 备注
		/// </summary>
		public string M_Move_Memo
		{
			set{ _m_move_memo=value;}
			get{return _m_move_memo;}
		}
		/// <summary>
		/// 单据状态
		/// </summary>
		public string OrdersStatus
		{
			set{ _ordersstatus=value;}
			get{return _ordersstatus;}
		}
		/// <summary>
		/// 
		/// </summary>
		public decimal? TotalMoney
		{
			set{ _totalmoney=value;}
			get{return _totalmoney;}
		}
		#endregion Model

	}
}

