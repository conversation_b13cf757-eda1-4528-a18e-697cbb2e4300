﻿
<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class LIS_TestItem
    Inherits HisControl.BaseChild

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(LIS_TestItem))
        Me.C1Holder1 = New C1.Win.C1Command.C1CommandHolder()
        Me.Move1 = New C1.Win.C1Command.C1Command()
        Me.Move2 = New C1.Win.C1Command.C1Command()
        Me.Move3 = New C1.Win.C1Command.C1Command()
        Me.Move4 = New C1.Win.C1Command.C1Command()
        Me.Move5 = New C1.Win.C1Command.C1Command()
        Me.Control1 = New C1.Win.C1Command.C1CommandControl()
        Me.T_Label1 = New C1.Win.C1Input.C1Label()
        Me.Control2 = New C1.Win.C1Command.C1CommandControl()
        Me.T_Label2 = New C1.Win.C1Input.C1Label()
        Me.Control3 = New C1.Win.C1Command.C1CommandControl()
        Me.T_Label3 = New C1.Win.C1Input.C1Label()
        Me.Control4 = New C1.Win.C1Command.C1Command()
        Me.ToolBar1 = New C1.Win.C1Command.C1ToolBar()
        Me.Link6 = New C1.Win.C1Command.C1CommandLink()
        Me.Link1 = New C1.Win.C1Command.C1CommandLink()
        Me.Link2 = New C1.Win.C1Command.C1CommandLink()
        Me.Link7 = New C1.Win.C1Command.C1CommandLink()
        Me.Link3 = New C1.Win.C1Command.C1CommandLink()
        Me.Link4 = New C1.Win.C1Command.C1CommandLink()
        Me.Link5 = New C1.Win.C1Command.C1CommandLink()
        Me.Link8 = New C1.Win.C1Command.C1CommandLink()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.Comm2 = New CustomControl.MyButton()
        Me.Comm1 = New CustomControl.MyButton()
        Me.T_Line3 = New System.Windows.Forms.Label()
        Me.ToolTip1 = New System.Windows.Forms.ToolTip(Me.components)
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel()
        Me.Code_TextBox = New CustomControl.MyTextBox()
        Me.Name_TextBox = New CustomControl.MyTextBox()
        Me.Memo_TextBox = New CustomControl.MyTextBox()
        Me.MaleMinNumericEdit1 = New CustomControl.MyNumericEdit()
        Me.MaleMaxNumericEdit1 = New CustomControl.MyNumericEdit()
        Me.FemaleMinNumericEdit2 = New CustomControl.MyNumericEdit()
        Me.FemaleMaxNumericEdit3 = New CustomControl.MyNumericEdit()
        Me.DwMyTextBox1 = New CustomControl.MyTextBox()
        Me.isDefaultCheckBox1 = New System.Windows.Forms.CheckBox()
        Me.ItemGroupMyTextBox2 = New CustomControl.MyTextBox()
        Me.JcMyTextBox3 = New CustomControl.MyTextBox()
        Me.ItemOrderMyTextBox1 = New CustomControl.MyNumericEdit()
        Me.T_Line1 = New System.Windows.Forms.Label()
        CType(Me.C1Holder1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T_Label1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T_Label2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T_Label3, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.ToolBar1.SuspendLayout()
        Me.Panel1.SuspendLayout()
        Me.TableLayoutPanel1.SuspendLayout()
        Me.SuspendLayout()
        '
        'C1Holder1
        '
        Me.C1Holder1.Commands.Add(Me.Move1)
        Me.C1Holder1.Commands.Add(Me.Move2)
        Me.C1Holder1.Commands.Add(Me.Move3)
        Me.C1Holder1.Commands.Add(Me.Move4)
        Me.C1Holder1.Commands.Add(Me.Move5)
        Me.C1Holder1.Commands.Add(Me.Control1)
        Me.C1Holder1.Commands.Add(Me.Control2)
        Me.C1Holder1.Commands.Add(Me.Control3)
        Me.C1Holder1.Commands.Add(Me.Control4)
        Me.C1Holder1.Owner = Me
        Me.C1Holder1.VisualStyle = C1.Win.C1Command.VisualStyle.Office2010Blue
        '
        'Move1
        '
        Me.Move1.Image = CType(resources.GetObject("Move1.Image"), System.Drawing.Image)
        Me.Move1.Name = "Move1"
        Me.Move1.Shortcut = System.Windows.Forms.Shortcut.F2
        Me.Move1.ShortcutText = ""
        Me.Move1.Text = "最前"
        Me.Move1.ToolTipText = "移到最前记录"
        '
        'Move2
        '
        Me.Move2.Image = CType(resources.GetObject("Move2.Image"), System.Drawing.Image)
        Me.Move2.Name = "Move2"
        Me.Move2.Shortcut = System.Windows.Forms.Shortcut.F3
        Me.Move2.ShortcutText = ""
        Me.Move2.Text = "上移"
        Me.Move2.ToolTipText = "上移记录"
        '
        'Move3
        '
        Me.Move3.Image = CType(resources.GetObject("Move3.Image"), System.Drawing.Image)
        Me.Move3.Name = "Move3"
        Me.Move3.Shortcut = System.Windows.Forms.Shortcut.F4
        Me.Move3.ShortcutText = ""
        Me.Move3.Text = "下移"
        Me.Move3.ToolTipText = "下移记录"
        '
        'Move4
        '
        Me.Move4.Image = CType(resources.GetObject("Move4.Image"), System.Drawing.Image)
        Me.Move4.Name = "Move4"
        Me.Move4.Shortcut = System.Windows.Forms.Shortcut.F5
        Me.Move4.ShortcutText = ""
        Me.Move4.Text = "最后"
        Me.Move4.ToolTipText = "移至最后记录"
        '
        'Move5
        '
        Me.Move5.Image = CType(resources.GetObject("Move5.Image"), System.Drawing.Image)
        Me.Move5.Name = "Move5"
        Me.Move5.Shortcut = System.Windows.Forms.Shortcut.F6
        Me.Move5.ShortcutText = ""
        Me.Move5.Text = "新增"
        '
        'Control1
        '
        Me.Control1.Control = Me.T_Label1
        Me.Control1.HotFrame = C1.Win.C1Command.HotFrameEnum.None
        Me.Control1.Name = "Control1"
        Me.Control1.ShortcutText = ""
        Me.Control1.ShowShortcut = False
        Me.Control1.ShowTextAsToolTip = False
        '
        'T_Label1
        '
        Me.T_Label1.AutoSize = True
        Me.T_Label1.BackColor = System.Drawing.Color.Transparent
        Me.T_Label1.BorderColor = System.Drawing.Color.Empty
        Me.T_Label1.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.T_Label1.ForeColor = System.Drawing.SystemColors.ControlText
        Me.T_Label1.Location = New System.Drawing.Point(1, 5)
        Me.T_Label1.Name = "T_Label1"
        Me.T_Label1.Size = New System.Drawing.Size(29, 12)
        Me.T_Label1.TabIndex = 36
        Me.T_Label1.Tag = Nothing
        Me.T_Label1.Text = "记录"
        Me.T_Label1.TextAlign = System.Drawing.ContentAlignment.BottomLeft
        Me.T_Label1.TextDetached = True
        '
        'Control2
        '
        Me.Control2.Control = Me.T_Label2
        Me.Control2.HotFrame = C1.Win.C1Command.HotFrameEnum.None
        Me.Control2.Name = "Control2"
        Me.Control2.ShortcutText = ""
        Me.Control2.ShowShortcut = False
        Me.Control2.ShowTextAsToolTip = False
        '
        'T_Label2
        '
        Me.T_Label2.AutoSize = True
        Me.T_Label2.BackColor = System.Drawing.Color.Transparent
        Me.T_Label2.BorderColor = System.Drawing.Color.Empty
        Me.T_Label2.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.T_Label2.ForeColor = System.Drawing.SystemColors.ControlText
        Me.T_Label2.Location = New System.Drawing.Point(80, 5)
        Me.T_Label2.Name = "T_Label2"
        Me.T_Label2.Size = New System.Drawing.Size(23, 12)
        Me.T_Label2.TabIndex = 37
        Me.T_Label2.Tag = Nothing
        Me.T_Label2.Text = "(1)"
        Me.T_Label2.TextAlign = System.Drawing.ContentAlignment.BottomCenter
        Me.T_Label2.TextDetached = True
        Me.T_Label2.TrimStart = True
        '
        'Control3
        '
        Me.Control3.Control = Me.T_Label3
        Me.Control3.HotFrame = C1.Win.C1Command.HotFrameEnum.None
        Me.Control3.Name = "Control3"
        Me.Control3.ShortcutText = ""
        Me.Control3.ShowShortcut = False
        Me.Control3.ShowTextAsToolTip = False
        '
        'T_Label3
        '
        Me.T_Label3.BackColor = System.Drawing.Color.Transparent
        Me.T_Label3.BorderColor = System.Drawing.Color.Empty
        Me.T_Label3.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.T_Label3.ForeColor = System.Drawing.SystemColors.ControlText
        Me.T_Label3.Location = New System.Drawing.Point(212, 3)
        Me.T_Label3.Name = "T_Label3"
        Me.T_Label3.Size = New System.Drawing.Size(35, 15)
        Me.T_Label3.TabIndex = 35
        Me.T_Label3.Tag = Nothing
        Me.T_Label3.Text = "Σ=1 "
        Me.T_Label3.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.T_Label3.TextDetached = True
        '
        'Control4
        '
        Me.Control4.Name = "Control4"
        Me.Control4.ShortcutText = ""
        Me.Control4.Text = "New Command"
        '
        'ToolBar1
        '
        Me.ToolBar1.AccessibleName = "Tool Bar"
        Me.ToolBar1.BackColor = System.Drawing.Color.Transparent
        Me.ToolBar1.CommandHolder = Me.C1Holder1
        Me.ToolBar1.CommandLinks.AddRange(New C1.Win.C1Command.C1CommandLink() {Me.Link6, Me.Link1, Me.Link2, Me.Link7, Me.Link3, Me.Link4, Me.Link5, Me.Link8})
        Me.ToolBar1.Controls.Add(Me.T_Label1)
        Me.ToolBar1.Controls.Add(Me.T_Label3)
        Me.ToolBar1.Controls.Add(Me.T_Label2)
        Me.ToolBar1.Location = New System.Drawing.Point(1, 3)
        Me.ToolBar1.MinButtonSize = 22
        Me.ToolBar1.Movable = False
        Me.ToolBar1.Name = "ToolBar1"
        Me.ToolBar1.Size = New System.Drawing.Size(248, 22)
        Me.ToolBar1.Text = "C1ToolBar2"
        Me.ToolBar1.VisualStyle = C1.Win.C1Command.VisualStyle.Custom
        Me.ToolBar1.VisualStyleBase = C1.Win.C1Command.VisualStyle.Classic
        '
        'Link6
        '
        Me.Link6.Command = Me.Control1
        '
        'Link1
        '
        Me.Link1.Command = Me.Move1
        Me.Link1.Delimiter = True
        Me.Link1.SortOrder = 1
        '
        'Link2
        '
        Me.Link2.Command = Me.Move2
        Me.Link2.SortOrder = 2
        '
        'Link7
        '
        Me.Link7.Command = Me.Control2
        Me.Link7.SortOrder = 3
        '
        'Link3
        '
        Me.Link3.Command = Me.Move3
        Me.Link3.SortOrder = 4
        '
        'Link4
        '
        Me.Link4.Command = Me.Move4
        Me.Link4.SortOrder = 5
        '
        'Link5
        '
        Me.Link5.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image), C1.Win.C1Command.ButtonLookFlags)
        Me.Link5.Command = Me.Move5
        Me.Link5.Delimiter = True
        Me.Link5.SortOrder = 6
        Me.Link5.Text = "新增"
        Me.Link5.ToolTipText = "新增记录"
        '
        'Link8
        '
        Me.Link8.Command = Me.Control3
        Me.Link8.Delimiter = True
        Me.Link8.SortOrder = 7
        Me.Link8.Text = "New Command"
        '
        'Panel1
        '
        Me.Panel1.Controls.Add(Me.Comm2)
        Me.Panel1.Controls.Add(Me.Comm1)
        Me.Panel1.Controls.Add(Me.T_Line3)
        Me.Panel1.Controls.Add(Me.ToolBar1)
        Me.Panel1.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.Panel1.Location = New System.Drawing.Point(0, 280)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(483, 27)
        Me.Panel1.TabIndex = 1
        '
        'Comm2
        '
        Me.Comm2.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.Comm2.DialogResult = System.Windows.Forms.DialogResult.None
        Me.Comm2.Location = New System.Drawing.Point(360, 3)
        Me.Comm2.Name = "Comm2"
        Me.Comm2.Size = New System.Drawing.Size(60, 25)
        Me.Comm2.TabIndex = 1
        Me.Comm2.Tag = "取消"
        Me.Comm2.Text = "取消"
        '
        'Comm1
        '
        Me.Comm1.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.Comm1.DialogResult = System.Windows.Forms.DialogResult.None
        Me.Comm1.Location = New System.Drawing.Point(294, 3)
        Me.Comm1.Name = "Comm1"
        Me.Comm1.Size = New System.Drawing.Size(60, 25)
        Me.Comm1.TabIndex = 0
        Me.Comm1.Tag = "保存"
        Me.Comm1.Text = "保存"
        '
        'T_Line3
        '
        Me.T_Line3.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.T_Line3.Dock = System.Windows.Forms.DockStyle.Top
        Me.T_Line3.Location = New System.Drawing.Point(0, 0)
        Me.T_Line3.Name = "T_Line3"
        Me.T_Line3.Size = New System.Drawing.Size(483, 2)
        Me.T_Line3.TabIndex = 1
        Me.T_Line3.Text = "Label2"
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.ColumnCount = 2
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 249.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 308.0!))
        Me.TableLayoutPanel1.Controls.Add(Me.Code_TextBox, 0, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.Name_TextBox, 0, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.Memo_TextBox, 0, 7)
        Me.TableLayoutPanel1.Controls.Add(Me.MaleMinNumericEdit1, 0, 4)
        Me.TableLayoutPanel1.Controls.Add(Me.MaleMaxNumericEdit1, 1, 4)
        Me.TableLayoutPanel1.Controls.Add(Me.FemaleMinNumericEdit2, 0, 5)
        Me.TableLayoutPanel1.Controls.Add(Me.FemaleMaxNumericEdit3, 1, 5)
        Me.TableLayoutPanel1.Controls.Add(Me.DwMyTextBox1, 0, 3)
        Me.TableLayoutPanel1.Controls.Add(Me.isDefaultCheckBox1, 1, 3)
        Me.TableLayoutPanel1.Controls.Add(Me.ItemGroupMyTextBox2, 0, 6)
        Me.TableLayoutPanel1.Controls.Add(Me.JcMyTextBox3, 1, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.ItemOrderMyTextBox1, 1, 6)
        Me.TableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(0, 0)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.RowCount = 9
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 4.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 27.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 27.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 27.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 27.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 27.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 27.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 27.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 20.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 20.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 20.0!))
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(483, 280)
        Me.TableLayoutPanel1.TabIndex = 0
        '
        'Code_TextBox
        '
        Me.Code_TextBox.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Code_TextBox.Captain = "小项编码"
        Me.Code_TextBox.CaptainBackColor = System.Drawing.Color.Transparent
        Me.Code_TextBox.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Code_TextBox.CaptainForeColor = System.Drawing.Color.Maroon
        Me.Code_TextBox.CaptainWidth = 75.0!
        Me.Code_TextBox.ContentForeColor = System.Drawing.Color.Black
        Me.Code_TextBox.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.Code_TextBox.Location = New System.Drawing.Point(3, 7)
        Me.Code_TextBox.Multiline = False
        Me.Code_TextBox.Name = "Code_TextBox"
        Me.Code_TextBox.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.Code_TextBox.ReadOnly = False
        Me.Code_TextBox.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.Code_TextBox.SelectionStart = 0
        Me.Code_TextBox.SelectStart = 0
        Me.Code_TextBox.Size = New System.Drawing.Size(243, 20)
        Me.Code_TextBox.TabIndex = 0
        Me.Code_TextBox.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Code_TextBox.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        '
        'Name_TextBox
        '
        Me.Name_TextBox.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Name_TextBox.Captain = "小项名称"
        Me.Name_TextBox.CaptainBackColor = System.Drawing.Color.Transparent
        Me.Name_TextBox.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Name_TextBox.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.Name_TextBox.CaptainWidth = 75.0!
        Me.Name_TextBox.ContentForeColor = System.Drawing.Color.Black
        Me.Name_TextBox.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.Name_TextBox.Location = New System.Drawing.Point(3, 34)
        Me.Name_TextBox.Multiline = False
        Me.Name_TextBox.Name = "Name_TextBox"
        Me.Name_TextBox.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.Name_TextBox.ReadOnly = False
        Me.Name_TextBox.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.Name_TextBox.SelectionStart = 0
        Me.Name_TextBox.SelectStart = 0
        Me.Name_TextBox.Size = New System.Drawing.Size(243, 20)
        Me.Name_TextBox.TabIndex = 1
        Me.Name_TextBox.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Name_TextBox.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        '
        'Memo_TextBox
        '
        Me.Memo_TextBox.Captain = "备注"
        Me.Memo_TextBox.CaptainBackColor = System.Drawing.Color.Transparent
        Me.Memo_TextBox.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Memo_TextBox.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.Memo_TextBox.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.Memo_TextBox, 2)
        Me.Memo_TextBox.ContentForeColor = System.Drawing.Color.Black
        Me.Memo_TextBox.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.Memo_TextBox.Dock = System.Windows.Forms.DockStyle.Left
        Me.Memo_TextBox.Location = New System.Drawing.Point(3, 169)
        Me.Memo_TextBox.Multiline = True
        Me.Memo_TextBox.Name = "Memo_TextBox"
        Me.Memo_TextBox.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.Memo_TextBox.ReadOnly = False
        Me.TableLayoutPanel1.SetRowSpan(Me.Memo_TextBox, 2)
        Me.Memo_TextBox.ScrollBars = System.Windows.Forms.ScrollBars.Vertical
        Me.Memo_TextBox.SelectionStart = 0
        Me.Memo_TextBox.SelectStart = 0
        Me.Memo_TextBox.Size = New System.Drawing.Size(476, 108)
        Me.Memo_TextBox.TabIndex = 11
        Me.Memo_TextBox.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Memo_TextBox.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Top
        '
        'MaleMinNumericEdit1
        '
        Me.MaleMinNumericEdit1.Captain = "男性下限"
        Me.MaleMinNumericEdit1.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MaleMinNumericEdit1.CaptainWidth = 75.0!
        Me.MaleMinNumericEdit1.Location = New System.Drawing.Point(3, 88)
        Me.MaleMinNumericEdit1.MaximumSize = New System.Drawing.Size(100000, 80)
        Me.MaleMinNumericEdit1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.MaleMinNumericEdit1.Name = "MaleMinNumericEdit1"
        Me.MaleMinNumericEdit1.NumFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MaleMinNumericEdit1.ReadOnly = False
        Me.MaleMinNumericEdit1.Size = New System.Drawing.Size(243, 20)
        Me.MaleMinNumericEdit1.TabIndex = 5
        Me.MaleMinNumericEdit1.ValueIsDbNull = False
        '
        'MaleMaxNumericEdit1
        '
        Me.MaleMaxNumericEdit1.Captain = "男性上限"
        Me.MaleMaxNumericEdit1.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MaleMaxNumericEdit1.CaptainWidth = 60.0!
        Me.MaleMaxNumericEdit1.Location = New System.Drawing.Point(252, 88)
        Me.MaleMaxNumericEdit1.MaximumSize = New System.Drawing.Size(100000, 80)
        Me.MaleMaxNumericEdit1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.MaleMaxNumericEdit1.Name = "MaleMaxNumericEdit1"
        Me.MaleMaxNumericEdit1.NumFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MaleMaxNumericEdit1.ReadOnly = False
        Me.MaleMaxNumericEdit1.Size = New System.Drawing.Size(227, 20)
        Me.MaleMaxNumericEdit1.TabIndex = 6
        Me.MaleMaxNumericEdit1.ValueIsDbNull = False
        '
        'FemaleMinNumericEdit2
        '
        Me.FemaleMinNumericEdit2.Captain = "女性下限"
        Me.FemaleMinNumericEdit2.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.FemaleMinNumericEdit2.CaptainWidth = 75.0!
        Me.FemaleMinNumericEdit2.Location = New System.Drawing.Point(3, 115)
        Me.FemaleMinNumericEdit2.MaximumSize = New System.Drawing.Size(100000, 80)
        Me.FemaleMinNumericEdit2.MinimumSize = New System.Drawing.Size(0, 20)
        Me.FemaleMinNumericEdit2.Name = "FemaleMinNumericEdit2"
        Me.FemaleMinNumericEdit2.NumFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.FemaleMinNumericEdit2.ReadOnly = False
        Me.FemaleMinNumericEdit2.Size = New System.Drawing.Size(243, 20)
        Me.FemaleMinNumericEdit2.TabIndex = 7
        Me.FemaleMinNumericEdit2.ValueIsDbNull = False
        '
        'FemaleMaxNumericEdit3
        '
        Me.FemaleMaxNumericEdit3.Captain = "女性上限"
        Me.FemaleMaxNumericEdit3.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.FemaleMaxNumericEdit3.CaptainWidth = 60.0!
        Me.FemaleMaxNumericEdit3.Location = New System.Drawing.Point(252, 115)
        Me.FemaleMaxNumericEdit3.MaximumSize = New System.Drawing.Size(100000, 80)
        Me.FemaleMaxNumericEdit3.MinimumSize = New System.Drawing.Size(0, 20)
        Me.FemaleMaxNumericEdit3.Name = "FemaleMaxNumericEdit3"
        Me.FemaleMaxNumericEdit3.NumFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.FemaleMaxNumericEdit3.ReadOnly = False
        Me.FemaleMaxNumericEdit3.Size = New System.Drawing.Size(227, 20)
        Me.FemaleMaxNumericEdit3.TabIndex = 8
        Me.FemaleMaxNumericEdit3.ValueIsDbNull = False
        '
        'DwMyTextBox1
        '
        Me.DwMyTextBox1.Captain = "小项单位"
        Me.DwMyTextBox1.CaptainBackColor = System.Drawing.Color.Transparent
        Me.DwMyTextBox1.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.DwMyTextBox1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.DwMyTextBox1.CaptainWidth = 75.0!
        Me.DwMyTextBox1.ContentForeColor = System.Drawing.Color.Black
        Me.DwMyTextBox1.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.DwMyTextBox1.Location = New System.Drawing.Point(3, 61)
        Me.DwMyTextBox1.Multiline = False
        Me.DwMyTextBox1.Name = "DwMyTextBox1"
        Me.DwMyTextBox1.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.DwMyTextBox1.ReadOnly = False
        Me.DwMyTextBox1.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.DwMyTextBox1.SelectionStart = 0
        Me.DwMyTextBox1.SelectStart = 0
        Me.DwMyTextBox1.Size = New System.Drawing.Size(243, 20)
        Me.DwMyTextBox1.TabIndex = 3
        Me.DwMyTextBox1.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.DwMyTextBox1.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        '
        'isDefaultCheckBox1
        '
        Me.isDefaultCheckBox1.AutoSize = True
        Me.isDefaultCheckBox1.Location = New System.Drawing.Point(252, 61)
        Me.isDefaultCheckBox1.Name = "isDefaultCheckBox1"
        Me.isDefaultCheckBox1.Size = New System.Drawing.Size(72, 16)
        Me.isDefaultCheckBox1.TabIndex = 4
        Me.isDefaultCheckBox1.Text = "是否默认"
        Me.isDefaultCheckBox1.UseVisualStyleBackColor = True
        '
        'ItemGroupMyTextBox2
        '
        Me.ItemGroupMyTextBox2.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.ItemGroupMyTextBox2.Captain = "项目组合名"
        Me.ItemGroupMyTextBox2.CaptainBackColor = System.Drawing.Color.Transparent
        Me.ItemGroupMyTextBox2.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.ItemGroupMyTextBox2.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.ItemGroupMyTextBox2.CaptainWidth = 75.0!
        Me.ItemGroupMyTextBox2.ContentForeColor = System.Drawing.Color.Black
        Me.ItemGroupMyTextBox2.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.ItemGroupMyTextBox2.Location = New System.Drawing.Point(3, 142)
        Me.ItemGroupMyTextBox2.Multiline = False
        Me.ItemGroupMyTextBox2.Name = "ItemGroupMyTextBox2"
        Me.ItemGroupMyTextBox2.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.ItemGroupMyTextBox2.ReadOnly = False
        Me.ItemGroupMyTextBox2.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.ItemGroupMyTextBox2.SelectionStart = 0
        Me.ItemGroupMyTextBox2.SelectStart = 0
        Me.ItemGroupMyTextBox2.Size = New System.Drawing.Size(243, 20)
        Me.ItemGroupMyTextBox2.TabIndex = 9
        Me.ItemGroupMyTextBox2.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.ItemGroupMyTextBox2.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        '
        'JcMyTextBox3
        '
        Me.JcMyTextBox3.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.JcMyTextBox3.Captain = "小项简称"
        Me.JcMyTextBox3.CaptainBackColor = System.Drawing.Color.Transparent
        Me.JcMyTextBox3.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.JcMyTextBox3.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.JcMyTextBox3.CaptainWidth = 60.0!
        Me.JcMyTextBox3.ContentForeColor = System.Drawing.Color.Black
        Me.JcMyTextBox3.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.JcMyTextBox3.Enabled = False
        Me.JcMyTextBox3.Location = New System.Drawing.Point(252, 34)
        Me.JcMyTextBox3.Multiline = False
        Me.JcMyTextBox3.Name = "JcMyTextBox3"
        Me.JcMyTextBox3.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.JcMyTextBox3.ReadOnly = False
        Me.JcMyTextBox3.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.JcMyTextBox3.SelectionStart = 0
        Me.JcMyTextBox3.SelectStart = 0
        Me.JcMyTextBox3.Size = New System.Drawing.Size(227, 20)
        Me.JcMyTextBox3.TabIndex = 2
        Me.JcMyTextBox3.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.JcMyTextBox3.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        '
        'ItemOrderMyTextBox1
        '
        Me.ItemOrderMyTextBox1.Captain = "项目顺序"
        Me.ItemOrderMyTextBox1.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.ItemOrderMyTextBox1.CaptainWidth = 60.0!
        Me.ItemOrderMyTextBox1.Location = New System.Drawing.Point(252, 142)
        Me.ItemOrderMyTextBox1.MaximumSize = New System.Drawing.Size(100000, 80)
        Me.ItemOrderMyTextBox1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.ItemOrderMyTextBox1.Name = "ItemOrderMyTextBox1"
        Me.ItemOrderMyTextBox1.NumFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.ItemOrderMyTextBox1.ReadOnly = False
        Me.ItemOrderMyTextBox1.Size = New System.Drawing.Size(227, 20)
        Me.ItemOrderMyTextBox1.TabIndex = 10
        Me.ItemOrderMyTextBox1.ValueIsDbNull = False
        '
        'T_Line1
        '
        Me.T_Line1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.T_Line1.BackColor = System.Drawing.SystemColors.Control
        Me.T_Line1.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.T_Line1.Location = New System.Drawing.Point(3, 33)
        Me.T_Line1.Name = "T_Line1"
        Me.T_Line1.Size = New System.Drawing.Size(499, 2)
        Me.T_Line1.TabIndex = 131
        Me.T_Line1.Text = "Label1"
        '
        'LIS_TestItem
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(483, 307)
        Me.Controls.Add(Me.TableLayoutPanel1)
        Me.Controls.Add(Me.Panel1)
        Me.MinimizeBox = False
        Me.Name = "LIS_TestItem"
        Me.Text = "详细信息"
        Me.TopMost = True
        CType(Me.C1Holder1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T_Label1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T_Label2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T_Label3, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ToolBar1.ResumeLayout(False)
        Me.ToolBar1.PerformLayout()
        Me.Panel1.ResumeLayout(False)
        Me.TableLayoutPanel1.ResumeLayout(False)
        Me.TableLayoutPanel1.PerformLayout()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents C1Holder1 As C1.Win.C1Command.C1CommandHolder
    Friend WithEvents Move1 As C1.Win.C1Command.C1Command
    Friend WithEvents Move2 As C1.Win.C1Command.C1Command
    Friend WithEvents Move3 As C1.Win.C1Command.C1Command
    Friend WithEvents Move4 As C1.Win.C1Command.C1Command
    Friend WithEvents Move5 As C1.Win.C1Command.C1Command
    Friend WithEvents Control1 As C1.Win.C1Command.C1CommandControl
    Friend WithEvents Control2 As C1.Win.C1Command.C1CommandControl
    Friend WithEvents Control3 As C1.Win.C1Command.C1CommandControl
    Friend WithEvents T_Label3 As C1.Win.C1Input.C1Label
    Friend WithEvents T_Label2 As C1.Win.C1Input.C1Label
    Friend WithEvents T_Label1 As C1.Win.C1Input.C1Label
    Friend WithEvents ToolBar1 As C1.Win.C1Command.C1ToolBar
    Friend WithEvents Link1 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link2 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link7 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link3 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link4 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link5 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link8 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents T_Line3 As System.Windows.Forms.Label
    Friend WithEvents Link6 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents ToolTip1 As System.Windows.Forms.ToolTip
    Friend WithEvents TableLayoutPanel1 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents Control4 As C1.Win.C1Command.C1Command
    Friend WithEvents Comm1 As CustomControl.MyButton
    Friend WithEvents Comm2 As CustomControl.MyButton
    Friend WithEvents Code_TextBox As CustomControl.MyTextBox
    ' Friend WithEvents Province_Comobo As CustomControl.MyDtComobo
    'Friend WithEvents City_Comobo As CustomControl.MyDtComobo
    Friend WithEvents T_Line1 As System.Windows.Forms.Label
    'Friend WithEvents Xq_Comobo As CustomControl.MyDtComobo
    Friend WithEvents Name_TextBox As CustomControl.MyTextBox
    'Friend WithEvents Add_TextBox As CustomControl.MyTextBox
    'Friend WithEvents FzrTell_TextBox As CustomControl.MyTextBox
    ' Friend WithEvents OrgCode_TextBox As CustomControl.MyTextBox
    'Friend WithEvents isUse_CheckBox As System.Windows.Forms.CheckBox
    'Friend WithEvents SuperCode_TextBox As CustomControl.MyTextBox
    'Friend WithEvents SuperName_TextBox As CustomControl.MyTextBox
    Friend WithEvents Memo_TextBox As CustomControl.MyTextBox
    Friend WithEvents MaleMinNumericEdit1 As CustomControl.MyNumericEdit
    Friend WithEvents MaleMaxNumericEdit1 As CustomControl.MyNumericEdit
    Friend WithEvents FemaleMinNumericEdit2 As CustomControl.MyNumericEdit
    Friend WithEvents FemaleMaxNumericEdit3 As CustomControl.MyNumericEdit
    Friend WithEvents DwMyTextBox1 As CustomControl.MyTextBox
    Friend WithEvents isDefaultCheckBox1 As System.Windows.Forms.CheckBox
    Friend WithEvents ItemGroupMyTextBox2 As CustomControl.MyTextBox
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents JcMyTextBox3 As CustomControl.MyTextBox
    Friend WithEvents ItemOrderMyTextBox1 As CustomControl.MyNumericEdit
    'Friend WithEvents Level_Comobo As CustomControl.MyDtComobo
End Class
