﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ERX.Model
{
    /// <summary>
    /// 电子处方医保电子签名
    /// </summary>
    public class MdlrxFixmedinsSignIn
    {
        public string fixmedinsCode { get; set; }   // 定点机构代码
        public string originalValue { get; set; }   // 原始待签名处方信息
        public string originalRxFile { get; set; }   // 原始待签名处方文件 
        public string extras { get; set; }   // 扩展字段


    }
    public class MdlrxFixmedinsSignOut
    {
        public string rxFile { get; set; }   //处方文件 
        public string signDigest { get; set; }   //签名/章摘要值 
        public string signCertSn { get; set; }   //签名机构证书SN
        public string signCertDn { get; set; }   //签名机构证书DN
    }
}
