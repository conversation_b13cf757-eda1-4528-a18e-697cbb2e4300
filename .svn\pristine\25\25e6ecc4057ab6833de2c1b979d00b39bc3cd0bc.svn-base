﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using Common;
using ERX.Model;

namespace ERX
{
    public partial class ERxQyJgQuery : Common.BaseForm.BaseFather
    {
        private MdlrxSetlInfoQueryOut mdlrxSetlInfoQueryOut = new MdlrxSetlInfoQueryOut();
        public ERxQyJgQuery(MdlrxSetlInfoQueryOut mdlrxSetlInfoQueryOut )
        {
            InitializeComponent();
            this.mdlrxSetlInfoQueryOut = mdlrxSetlInfoQueryOut;
        }

        private void ERxQyJgQuery_Load(object sender, EventArgs e)
        {
            myGrid1.Init_Grid();
            myGrid1.Init_Column("医药机构药品编号", "medinsListCodg", 222, "左", "", false);
            myGrid1.Init_Column("通用名", "drugGenname", 112, "左", "", false);
            myGrid1.Init_Column("药品商品名", "drugProdname", 200, "左", "", false);
            myGrid1.Init_Column("药品剂型", "drugDosform", 112, "左", "", false);
            myGrid1.Init_Column("药品规格", "drugSpec", 112, "左", "", false);
            myGrid1.Init_Column("数量", "ent", 112, "左", "", false);
            myGrid1.Init_Column("批准文号", "aprvno", 112, "左", "", false);
            myGrid1.Init_Column("批次号", "bchno", 112, "左", "", false);
            myGrid1.Init_Column("生产批号", "manuLotnum", 112, "左", "", false);
            myGrid1.Init_Column("生厂厂家", "prdrName", 112, "左", "", false);

            DataTable dt = DataTableToList.ToDataTable(mdlrxSetlInfoQueryOut.seltdelts);
            myGrid1.DataTable = dt;
        }


    }
}
