﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class Yf_Js1
    Inherits HisControl.BaseForm
    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(Yf_Js1))
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.CheckBox1 = New System.Windows.Forms.CheckBox()
        Me.DateTimePicker1 = New System.Windows.Forms.DateTimePicker()
        Me.T_Label2 = New C1.Win.C1Input.C1Label()
        Me.T_Line2 = New System.Windows.Forms.Label()
        Me.T_Label = New C1.Win.C1Input.C1Label()
        Me.C1ToolBar1 = New C1.Win.C1Command.C1ToolBar()
        Me.C1CommandHolder1 = New C1.Win.C1Command.C1CommandHolder()
        Me.Comm2 = New C1.Win.C1Command.C1Command()
        Me.C1CommandLink2 = New C1.Win.C1Command.C1CommandLink()
        Me.T_Combo = New C1.Win.C1List.C1Combo()
        Me.T_Textbox = New C1.Win.C1Input.C1TextBox()
        Me.C1TrueDBGrid1 = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.Panel1.SuspendLayout
        CType(Me.T_Label2,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.T_Label,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.C1CommandHolder1,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.T_Combo,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.T_Textbox,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.C1TrueDBGrid1,System.ComponentModel.ISupportInitialize).BeginInit
        Me.SuspendLayout
        '
        'Panel1
        '
        Me.Panel1.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Panel1.Controls.Add(Me.CheckBox1)
        Me.Panel1.Controls.Add(Me.DateTimePicker1)
        Me.Panel1.Controls.Add(Me.T_Label2)
        Me.Panel1.Controls.Add(Me.T_Line2)
        Me.Panel1.Controls.Add(Me.T_Label)
        Me.Panel1.Controls.Add(Me.C1ToolBar1)
        Me.Panel1.Controls.Add(Me.T_Combo)
        Me.Panel1.Controls.Add(Me.T_Textbox)
        Me.Panel1.Dock = System.Windows.Forms.DockStyle.Top
        Me.Panel1.Location = New System.Drawing.Point(0, 0)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(708, 26)
        Me.Panel1.TabIndex = 0
        '
        'CheckBox1
        '
        Me.CheckBox1.AutoSize = true
        Me.CheckBox1.Checked = true
        Me.CheckBox1.CheckState = System.Windows.Forms.CheckState.Checked
        Me.CheckBox1.Location = New System.Drawing.Point(578, 5)
        Me.CheckBox1.Name = "CheckBox1"
        Me.CheckBox1.Size = New System.Drawing.Size(132, 16)
        Me.CheckBox1.TabIndex = 14
        Me.CheckBox1.Text = "显示所有未接受单据"
        Me.CheckBox1.UseVisualStyleBackColor = true
        '
        'DateTimePicker1
        '
        Me.DateTimePicker1.CustomFormat = "yyyy-MM-dd"
        Me.DateTimePicker1.Format = System.Windows.Forms.DateTimePickerFormat.Custom
        Me.DateTimePicker1.Location = New System.Drawing.Point(481, 2)
        Me.DateTimePicker1.Name = "DateTimePicker1"
        Me.DateTimePicker1.Size = New System.Drawing.Size(84, 21)
        Me.DateTimePicker1.TabIndex = 12
        Me.DateTimePicker1.Value = New Date(2012, 9, 20, 0, 0, 0, 0)
        '
        'T_Label2
        '
        Me.T_Label2.AutoSize = true
        Me.T_Label2.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.T_Label2.ForeColor = System.Drawing.Color.DarkRed
        Me.T_Label2.Location = New System.Drawing.Point(430, 6)
        Me.T_Label2.Name = "T_Label2"
        Me.T_Label2.Size = New System.Drawing.Size(53, 12)
        Me.T_Label2.TabIndex = 11
        Me.T_Label2.Tag = Nothing
        Me.T_Label2.Text = "调拨日期"
        Me.T_Label2.TextDetached = true
        '
        'T_Line2
        '
        Me.T_Line2.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.T_Line2.Location = New System.Drawing.Point(378, -6)
        Me.T_Line2.Name = "T_Line2"
        Me.T_Line2.Size = New System.Drawing.Size(2, 25)
        Me.T_Line2.TabIndex = 8
        Me.T_Line2.Text = "Label2"
        '
        'T_Label
        '
        Me.T_Label.AutoSize = true
        Me.T_Label.BackColor = System.Drawing.SystemColors.Control
        Me.T_Label.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.T_Label.ForeColor = System.Drawing.Color.Maroon
        Me.T_Label.Location = New System.Drawing.Point(382, 1)
        Me.T_Label.Name = "T_Label"
        Me.T_Label.Size = New System.Drawing.Size(47, 12)
        Me.T_Label.TabIndex = 7
        Me.T_Label.Tag = Nothing
        Me.T_Label.Text = "T_Label"
        Me.T_Label.TextAlign = System.Drawing.ContentAlignment.BottomLeft
        Me.T_Label.TextDetached = true
        Me.T_Label.TrimStart = true
        '
        'C1ToolBar1
        '
        Me.C1ToolBar1.AccessibleName = "Tool Bar"
        Me.C1ToolBar1.CommandHolder = Me.C1CommandHolder1
        Me.C1ToolBar1.CommandLinks.AddRange(New C1.Win.C1Command.C1CommandLink() {Me.C1CommandLink2})
        Me.C1ToolBar1.Location = New System.Drawing.Point(1, 1)
        Me.C1ToolBar1.Name = "C1ToolBar1"
        Me.C1ToolBar1.Size = New System.Drawing.Size(59, 24)
        Me.C1ToolBar1.Text = "C1ToolBar1"
        Me.C1ToolBar1.VisualStyle = C1.Win.C1Command.VisualStyle.Classic
        Me.C1ToolBar1.VisualStyleBase = C1.Win.C1Command.VisualStyle.Classic
        '
        'C1CommandHolder1
        '
        Me.C1CommandHolder1.Commands.Add(Me.Comm2)
        Me.C1CommandHolder1.Owner = Me
        '
        'Comm2
        '
        Me.Comm2.Image = CType(resources.GetObject("Comm2.Image"),System.Drawing.Image)
        Me.Comm2.Name = "Comm2"
        Me.Comm2.ShortcutText = ""
        Me.Comm2.Text = "更新"
        '
        'C1CommandLink2
        '
        Me.C1CommandLink2.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image),C1.Win.C1Command.ButtonLookFlags)
        Me.C1CommandLink2.Command = Me.Comm2
        '
        'T_Combo
        '
        Me.T_Combo.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.T_Combo.Caption = ""
        Me.T_Combo.CaptionHeight = 17
        Me.T_Combo.CharacterCasing = System.Windows.Forms.CharacterCasing.Normal
        Me.T_Combo.ColumnCaptionHeight = 18
        Me.T_Combo.ColumnFooterHeight = 18
        Me.T_Combo.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.T_Combo.Images.Add(CType(resources.GetObject("T_Combo.Images"),System.Drawing.Image))
        Me.T_Combo.ItemHeight = 15
        Me.T_Combo.Location = New System.Drawing.Point(223, 3)
        Me.T_Combo.MatchEntryTimeout = CType(2000,Long)
        Me.T_Combo.MaxDropDownItems = CType(5,Short)
        Me.T_Combo.MaxLength = 32767
        Me.T_Combo.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.T_Combo.Name = "T_Combo"
        Me.T_Combo.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.T_Combo.Size = New System.Drawing.Size(76, 16)
        Me.T_Combo.TabIndex = 5
        Me.T_Combo.TabStop = false
        Me.T_Combo.PropBag = resources.GetString("T_Combo.PropBag")
        '
        'T_Textbox
        '
        Me.T_Textbox.AutoSize = false
        Me.T_Textbox.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.T_Textbox.Location = New System.Drawing.Point(300, 4)
        Me.T_Textbox.Name = "T_Textbox"
        Me.T_Textbox.Size = New System.Drawing.Size(72, 14)
        Me.T_Textbox.TabIndex = 6
        Me.T_Textbox.TabStop = false
        Me.T_Textbox.Tag = Nothing
        Me.T_Textbox.TextDetached = true
        Me.T_Textbox.TrimStart = true
        Me.T_Textbox.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        '
        'C1TrueDBGrid1
        '
        Me.C1TrueDBGrid1.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.C1TrueDBGrid1.GroupByCaption = "Drag a column header here to group by that column"
        Me.C1TrueDBGrid1.Images.Add(CType(resources.GetObject("C1TrueDBGrid1.Images"),System.Drawing.Image))
        Me.C1TrueDBGrid1.Location = New System.Drawing.Point(40, 81)
        Me.C1TrueDBGrid1.Name = "C1TrueDBGrid1"
        Me.C1TrueDBGrid1.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.C1TrueDBGrid1.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.C1TrueDBGrid1.PreviewInfo.ZoomFactor = 75R
        Me.C1TrueDBGrid1.PrintInfo.MeasurementDevice = C1.Win.C1TrueDBGrid.PrintInfo.MeasurementDeviceEnum.Screen
        Me.C1TrueDBGrid1.PrintInfo.MeasurementPrinterName = Nothing
        Me.C1TrueDBGrid1.Size = New System.Drawing.Size(405, 183)
        Me.C1TrueDBGrid1.TabIndex = 3
        Me.C1TrueDBGrid1.Text = "C1TrueDBGrid1"
        Me.C1TrueDBGrid1.UseCompatibleTextRendering = false
        Me.C1TrueDBGrid1.PropBag = resources.GetString("C1TrueDBGrid1.PropBag")
        '
        'Yf_Js1
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6!, 12!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(708, 448)
        Me.Controls.Add(Me.C1TrueDBGrid1)
        Me.Controls.Add(Me.Panel1)
        Me.Icon = CType(resources.GetObject("$this.Icon"),System.Drawing.Icon)
        Me.Name = "Yf_Js1"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "药房接收药库调拨"
        Me.Panel1.ResumeLayout(false)
        Me.Panel1.PerformLayout
        CType(Me.T_Label2,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.T_Label,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.C1CommandHolder1,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.T_Combo,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.T_Textbox,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.C1TrueDBGrid1,System.ComponentModel.ISupportInitialize).EndInit
        Me.ResumeLayout(false)

End Sub
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents C1TrueDBGrid1 As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents T_Combo As C1.Win.C1List.C1Combo
    Friend WithEvents T_Textbox As C1.Win.C1Input.C1TextBox
    Friend WithEvents C1ToolBar1 As C1.Win.C1Command.C1ToolBar
    Friend WithEvents C1CommandHolder1 As C1.Win.C1Command.C1CommandHolder
    Friend WithEvents T_Line2 As System.Windows.Forms.Label
    Friend WithEvents T_Label As C1.Win.C1Input.C1Label
    Friend WithEvents Comm2 As C1.Win.C1Command.C1Command
    Friend WithEvents C1CommandLink2 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents T_Label2 As C1.Win.C1Input.C1Label
    Friend WithEvents DateTimePicker1 As System.Windows.Forms.DateTimePicker
    Friend WithEvents CheckBox1 As System.Windows.Forms.CheckBox
End Class
