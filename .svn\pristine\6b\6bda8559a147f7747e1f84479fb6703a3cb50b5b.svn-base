﻿Public Class Hsz_Cqcf_Dr
    Dim My_Dataset As New DataSet
    Dim Ly_Table As DataTable
    Dim My_Cm As CurrencyManager
    Dim row As DataRow
    Public Sub New(ByVal T_Table As DataTable)

        ' 此调用是设计器所必需的。
        InitializeComponent()
        Ly_Table = T_Table
        ' 在 InitializeComponent() 调用之后添加任何初始化。

    End Sub

    Private Sub Hsz_Cqcf_Dr_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Dim Str As String
        Dim SysDate As Date = HisVar.HisVar.Sqldal.GetSingle("Select Getdate()")
        With DateComobo
            .Enabled = False
            .Additem = Format(SysDate, "yyyy-MM-dd") & ",0"
            .Additem = Format(DateAdd("D", 1, SysDate), "yyyy-MM-dd") & ",0"
            .DisplayColumns(1).Visible = False
            .SelectedIndex = 0

        End With

        With CfTimeDateEdit
            .Enabled = False
            .Value = Format(Now, "HH:mm:ss")
            .DisplayFormat = "HH:mm:ss"
            .EditFormat = "HH:mm:ss"
        End With


        Call DataInit()

        Dim My_Flex1 As New BaseClass.C_Flex(C1FlexGrid1, My_Dataset.Tables("长期医嘱").DefaultView)
        With My_Flex1
            .Init_Flex()
            .Init_Column("选择", "IsCheck", 60, "中", "")
            .Init_Column("入院编码", "Bl_Code", 0, "中", "")
            .Init_Column("长期医嘱编码", "AutoCf_Code", 100, "中", "")
            .Init_Column("患者姓名", "Ry_Name", 120, "中", "")
            .Init_Column("性别", "Ry_Sex", 60, "中", "")
            .Init_Column("病历编码", "Ry_BlCode", 100, "中", "")
            .Init_Column("科室", "Ks_Name", 140, "中", "")
            .Init_Column("医生", "Ys_Name", 100, "中", "")
            .Init_Column("操作员", "Jsr_Name", 80, "中", "")
            .Init_Column("医院编码", "Yy_Code", 0, "中", "")
            .Init_Column("药房编码", "Yf_Code", 0, "中", "")
            .Init_Column("入院编码", "Bl_Code", 0, "中", "")
            .Init_Column("科室编码", "Ks_Code", 0, "中", "")
            .Init_Column("医生编码", "Ys_Code", 0, "中", "")
            .Init_Column("处方金额", "Cf_Money", 0, "中", "")
            .Init_Column("药品金额", "Cf_YpMoney", 0, "中", "")
            .Init_Column("操作员编码", "Jsr_Code", 0, "中", "")
            .Init_Column("每天执行次数", "Everyday_Times", 0, "中", "")
            .Init_Column("执行间隔", "Remind_interval", 0, "中", "")
            .Init_Column("首次执行时间", "First_Execut_Time", 0, "中", "")
            .Init_Column("上次执行时间", "Last_Execut_Time", 0, "中", "")
            .Init_Column("下次执行时间", "Next_Execut_Time", 0, "中", "")

        End With
        C1FlexGrid1.Cols(1).DataType = GetType(System.Boolean)
        C1FlexGrid1.Select()

        Dim My_Flex2 As New BaseClass.C_Flex(C1FlexGrid2, My_Dataset.Tables("医嘱明细").DefaultView)
        With My_Flex2
            .Init_Flex()
            .Init_Column("选择", "IsCheck", 60, "中", "")
            .Init_Column("长期医嘱编码", "AutoCf_Code", 100, "中", "")
            .Init_Column("编码", "Xm_Code", 0, "中", "")
            .Init_Column("名称", "Xm_Name", 120, "中", "")
            .Init_Column("数量", "Cf_Sl", 100, "中", "")
            .Init_Column("单价", "Cf_Dj", 140, "中", "")
            .Init_Column("金额", "Cf_Money", 100, "中", "")
            .Init_Column("类别", "Cf_Lb", 100, "中", "")
        End With
        C1FlexGrid2.Cols(1).DataType = GetType(System.Boolean)
        ChkAllCheck.Checked = True


    End Sub

    Private Sub DataInit()
        Dim Str As String
        If RadioButton2.Checked = True Then
            If HisPara.PublicConfig.ZyCfKsXz = "是" Then
                Str = "Select 'true'  as IsCheck,AutoCf_Code,Ry_Name,Ry_Sex,Ry_BlCode,Ks_Name,Ys_Name,Jsr_Name,Auto_Cf.Yy_Code,Auto_Cf.Yf_Code,Auto_Cf.Bl_Code,Auto_Cf.Ks_Code,Auto_Cf.Ys_Code,Auto_Cf.Cf_Money,Auto_Cf.Cf_YpMoney,Auto_Cf.Jsr_Code,Everyday_Times,Remind_interval,First_Execut_Time,Last_Execut_Time,Next_Execut_Time from Auto_Cf,Bl,Zd_YyKs,Zd_YyYs,Zd_YyJsr where Auto_Cf.Bl_Code=Bl.Bl_Code and Auto_Cf.Ks_Code=Zd_YyKs.Ks_Code and Auto_Cf.Ys_Code=Zd_YyYs.Ys_Code and Auto_Cf.Jsr_Code=Zd_YyJsr.Jsr_Code and   Ry_CyDate is null And Convert(Varchar(10),Start_Date,126)<=Convert(Varchar(10),dateadd(day,1,getdate()),126)  And End_Date>=getdate() and Auto_Zt='已开始'  and Convert(varchar(10),getdate(),126)>=Convert(varchar(10),Execute_Tomorrow,126) and Bl.Ks_Code like '%" & HisVar.HisVar.XmKs & "%'"
            Else
                Str = "Select 'true'  as IsCheck,AutoCf_Code,Ry_Name,Ry_Sex,Ry_BlCode,Ks_Name,Ys_Name,Jsr_Name,Auto_Cf.Yy_Code,Auto_Cf.Yf_Code,Auto_Cf.Bl_Code,Auto_Cf.Ks_Code,Auto_Cf.Ys_Code,Auto_Cf.Cf_Money,Auto_Cf.Cf_YpMoney,Auto_Cf.Jsr_Code,Everyday_Times,Remind_interval,First_Execut_Time,Last_Execut_Time,Next_Execut_Time from Auto_Cf,Bl,Zd_YyKs,Zd_YyYs,Zd_YyJsr where Auto_Cf.Bl_Code=Bl.Bl_Code and Auto_Cf.Ks_Code=Zd_YyKs.Ks_Code and Auto_Cf.Ys_Code=Zd_YyYs.Ys_Code and Auto_Cf.Jsr_Code=Zd_YyJsr.Jsr_Code and   Ry_CyDate is null And Convert(Varchar(10),Start_Date,126)<=Convert(Varchar(10),dateadd(day,1,getdate()),126)  And End_Date>=getdate() and Auto_Zt='已开始'  and Convert(varchar(10),getdate(),126)>=Convert(varchar(10),Execute_Tomorrow,126)"
            End If

            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, Str, "长期医嘱", True)

            If HisPara.PublicConfig.ZyCfKsXz = "是" Then
                Str = "Select AutoCf_Code from Auto_Cf,Bl,Zd_YyKs,Zd_YyYs,Zd_YyJsr where Auto_Cf.Bl_Code=Bl.Bl_Code and Auto_Cf.Ks_Code=Zd_YyKs.Ks_Code and Auto_Cf.Ys_Code=Zd_YyYs.Ys_Code and Auto_Cf.Jsr_Code=Zd_YyJsr.Jsr_Code and   Ry_CyDate is null And Convert(Varchar(10),Start_Date,126)<=Convert(Varchar(10),dateadd(day,1,getdate()),126)  And End_Date>=getdate() and Auto_Zt='已开始'  and Convert(varchar(10),getdate(),126)>=Convert(varchar(10),Execute_Tomorrow,126) and Bl.Ks_Code like '%" & HisVar.HisVar.XmKs & "%'"
            Else
                Str = "Select AutoCf_Code from Auto_Cf,Bl,Zd_YyKs,Zd_YyYs,Zd_YyJsr where Auto_Cf.Bl_Code=Bl.Bl_Code and Auto_Cf.Ks_Code=Zd_YyKs.Ks_Code and Auto_Cf.Ys_Code=Zd_YyYs.Ys_Code and Auto_Cf.Jsr_Code=Zd_YyJsr.Jsr_Code and   Ry_CyDate is null And Convert(Varchar(10),Start_Date,126)<=Convert(Varchar(10),dateadd(day,1,getdate()),126)  And End_Date>=getdate() and Auto_Zt='已开始'  and Convert(varchar(10),getdate(),126)>=Convert(varchar(10),Execute_Tomorrow,126)"
            End If

            Str = "SELECT 'true'  as IsCheck,Yy_Code,AutoCf_Code, Auto_Cfxm.Xm_Code,Xm_Name ,Cf_Sl, Cf_Dj, Cf_Money, Cf_Lb,'Xm' flag FROM dbo.Auto_Cfxm LEFT JOIN zd_ml_xm3 ON dbo.Auto_Cfxm.Xm_Code = dbo.Zd_Ml_Xm3.Xm_Code WHERE AutoCf_Code IN (" & Str & ")" &
            "UNION  ALL SELECT 'true'  as IsCheck,Auto_Cfyp.Yy_Code, AutoCf_Code, Auto_Cfyp.Xx_Code,Yp_Name, Cf_Sl, Cf_Dj, Cf_Money, Cf_Lb,'Yp' flag FROM Auto_Cfyp LEFT JOIN dbo.V_Ypkc ON dbo.Auto_Cfyp.Xx_Code = V_Ypkc.Xx_Code WHERE AutoCf_Code IN (" & Str & ") "

            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, Str, "医嘱明细", True)


            DateComobo.Enabled = True
            DateComobo.SelectedIndex = -1

        ElseIf RadioButton1.Checked = True Then
            If HisPara.PublicConfig.ZyCfKsXz = "是" Then
                Str = "Select 'true'  as IsCheck,AutoCf_Code,Ry_Name,Ry_Sex,Ry_BlCode,Ks_Name,Ys_Name,Jsr_Name,Auto_Cf.Yy_Code,Auto_Cf.Yf_Code,Auto_Cf.Bl_Code,Auto_Cf.Ks_Code,Auto_Cf.Ys_Code,Auto_Cf.Cf_Money,Auto_Cf.Cf_YpMoney,Auto_Cf.Jsr_Code,Everyday_Times,Remind_interval,First_Execut_Time,Last_Execut_Time,Next_Execut_Time from Auto_Cf,Bl,Zd_YyKs,Zd_YyYs,Zd_YyJsr where Auto_Cf.Bl_Code=Bl.Bl_Code and Auto_Cf.Ks_Code=Zd_YyKs.Ks_Code and Auto_Cf.Ys_Code=Zd_YyYs.Ys_Code and Auto_Cf.Jsr_Code=Zd_YyJsr.Jsr_Code and   Ry_CyDate is null And Start_Date<=getdate() And End_Date>=getdate() and Auto_Zt='已开始' AND GETDATE() >=Execut_Border_Time  and Convert(varchar(10),getdate(),126)<>Convert(varchar(10),Execute_Tomorrow,126) and Bl.Ks_Code like '%" & HisVar.HisVar.XmKs & "%' "
            Else
                Str = "Select 'true'  as IsCheck,AutoCf_Code,Ry_Name,Ry_Sex,Ry_BlCode,Ks_Name,Ys_Name,Jsr_Name,Auto_Cf.Yy_Code,Auto_Cf.Yf_Code,Auto_Cf.Bl_Code,Auto_Cf.Ks_Code,Auto_Cf.Ys_Code,Auto_Cf.Cf_Money,Auto_Cf.Cf_YpMoney,Auto_Cf.Jsr_Code,Everyday_Times,Remind_interval,First_Execut_Time,Last_Execut_Time,Next_Execut_Time from Auto_Cf,Bl,Zd_YyKs,Zd_YyYs,Zd_YyJsr where Auto_Cf.Bl_Code=Bl.Bl_Code and Auto_Cf.Ks_Code=Zd_YyKs.Ks_Code and Auto_Cf.Ys_Code=Zd_YyYs.Ys_Code and Auto_Cf.Jsr_Code=Zd_YyJsr.Jsr_Code and   Ry_CyDate is null And Start_Date<=getdate() And End_Date>=getdate() and Auto_Zt='已开始' AND GETDATE() >=Execut_Border_Time  and Convert(varchar(10),getdate(),126)<>Convert(varchar(10),Execute_Tomorrow,126)"
            End If

            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, Str, "长期医嘱", True)

            If HisPara.PublicConfig.ZyCfKsXz = "是" Then
                Str = "Select AutoCf_Code from Auto_Cf,Bl,Zd_YyKs,Zd_YyYs,Zd_YyJsr where Auto_Cf.Bl_Code=Bl.Bl_Code and Auto_Cf.Ks_Code=Zd_YyKs.Ks_Code and Auto_Cf.Ys_Code=Zd_YyYs.Ys_Code and Auto_Cf.Jsr_Code=Zd_YyJsr.Jsr_Code and   Ry_CyDate is null And Start_Date<=getdate() And End_Date>=getdate() and Auto_Zt='已开始' AND GETDATE() >=Execut_Border_Time  and Convert(varchar(10),getdate(),126)<>Convert(varchar(10),Execute_Tomorrow,126) and Bl.Ks_Code like '%" & HisVar.HisVar.XmKs & "%' "
            Else
                Str = "Select AutoCf_Code from Auto_Cf,Bl,Zd_YyKs,Zd_YyYs,Zd_YyJsr where Auto_Cf.Bl_Code=Bl.Bl_Code and Auto_Cf.Ks_Code=Zd_YyKs.Ks_Code and Auto_Cf.Ys_Code=Zd_YyYs.Ys_Code and Auto_Cf.Jsr_Code=Zd_YyJsr.Jsr_Code and   Ry_CyDate is null And Start_Date<=getdate() And End_Date>=getdate() and Auto_Zt='已开始' AND GETDATE() >=Execut_Border_Time  and Convert(varchar(10),getdate(),126)<>Convert(varchar(10),Execute_Tomorrow,126)"
            End If


            Str = "SELECT 'true'  as IsCheck,Yy_Code,AutoCf_Code, Auto_Cfxm.Xm_Code,Xm_Name ,Cf_Sl, Cf_Dj, Cf_Money, Cf_Lb,Null Yp_Yfyl,Null Combo_Id,Null IsCombo,Null Dosage,Null Administration_Name,Null Group_Id,Null Frequency,Null Mx_Dw,Null Administration_Code,Null Freq_Code,'Xm' flag,Templet_Code FROM dbo.Auto_Cfxm LEFT JOIN zd_ml_xm3 ON dbo.Auto_Cfxm.Xm_Code = dbo.Zd_Ml_Xm3.Xm_Code WHERE AutoCf_Code IN (" & Str & ") AND XmEnd_Date IS NULL " &
            "UNION  ALL SELECT 'true'  as IsCheck,Auto_Cfyp.Yy_Code, AutoCf_Code, Auto_Cfyp.Xx_Code,Yp_Name, Cf_Sl, Cf_Dj, Cf_Money, Cf_Lb,Yp_Yfyl,Combo_Id,IsCombo,Dosage,Auto_Cfyp.Administration_Name,Group_Id,Auto_Cfyp.Frequency,Mx_Dw,Auto_Cfyp.Administration_Code,Auto_Cfyp.Freq_Code,'Yp' flag,null FROM Auto_Cfyp LEFT JOIN dbo.V_Ypkc ON dbo.Auto_Cfyp.Xx_Code = V_Ypkc.Xx_Code WHERE AutoCf_Code IN (" & Str & ")  AND YpEnd_Date IS NULL "

            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, Str, "医嘱明细", True)

            DateComobo.Enabled = False
            DateComobo.SelectedIndex = 0


        End If



        My_Dataset.Tables("长期医嘱").Columns("IsCheck").ReadOnly = False
        My_Dataset.Tables("长期医嘱").Columns("IsCheck").MaxLength = 5
        My_Dataset.Tables("医嘱明细").Columns("IsCheck").ReadOnly = False
        My_Dataset.Tables("医嘱明细").Columns("IsCheck").MaxLength = 5

    End Sub

    Private Sub ItemShow()
        If C1FlexGrid1.Row > -1 Then
            My_Dataset.Tables("医嘱明细").DefaultView.RowFilter = "AutoCf_Code='" & C1FlexGrid1.Item(C1FlexGrid1.Row, 2) & "'"
        End If

    End Sub

    Private Sub C1FlexGrid1_RowColChange(sender As System.Object, e As System.EventArgs) Handles C1FlexGrid1.RowColChange
        ItemShow()
    End Sub


    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click
        If My_Dataset.Tables("长期医嘱").Select("IsCheck='true'").Length = 0 Then
            MsgBox("请选择需要汇总领药的长期处方！", MsgBoxStyle.Information, "提示")
            Exit Sub
        End If

        If DateComobo.Text = "" Then
            MsgBox("请选择要生成的日期！", MsgBoxStyle.Information, "提示")
            Exit Sub
        End If

        If MsgBox("是否确认导入全部所选处方至【" & DateComobo.Text & " " & Format(CfTimeDateEdit.Value, "HH:mm:ss") & "】？", MsgBoxStyle.DefaultButton1 + MsgBoxStyle.OkCancel, "提示：") = DialogResult.Cancel Then Exit Sub

        Dim QfName As String = ""

        Dim Row As DataRow
        For Each Row In My_Dataset.Tables("长期医嘱").Select("IsCheck='true'")

            If QfName.Contains(Row.Item("Ry_Name") & "（入院编码：" & Row.Item("Bl_Code") & "）") Then Continue For

            Dim YeMoney As Decimal = HisVar.HisVar.Sqldal.GetSingle("Select (Select Isnull(Sum(Jf_Money),0) From Bl_Jf Where Bl_Code=Bl.Bl_Code)-(Select Isnull(Sum(Bl_Cfyp.Cf_Money),0) From Bl_Cfyp,Bl_Cf Where Cf_Print='是' and Bl_Cfyp.Cf_Code=Bl_Cf.Cf_Code And Bl_Code=Bl.Bl_Code)-(Select Isnull(Sum(Bl_Cfxm.Cf_Money),0) From Bl_Cfxm,Bl_Cf Where Cf_Print='是' and  Bl_Cfxm.Cf_Code=Bl_Cf.Cf_Code And Bl_Code=Bl.Bl_Code) New_Money FROM Bl where Bl_Code='" & Row.Item("Bl_Code") & "'")
            If YeMoney - Row.Item("Cf_Money") * Row.Item("Everyday_Times") < Val(HisPara.PublicConfig.Qfed) Then
                QfName = QfName & Row.Item("Ry_Name") & "（入院编码：" & Row.Item("Bl_Code") & "）" & vbCrLf
                Continue For
            End If



            Dim Have_Yp As Integer = My_Dataset.Tables("医嘱明细").Select("IsCheck='true' and flag='Yp' and AUTOCF_CODE='" & Row.Item("AUTOCF_CODE") & "'").Length

            Dim ypMoney As Decimal = 0
            Dim cfMoney As Decimal = 0
            For Each dr In My_Dataset.Tables("医嘱明细").Select("IsCheck='true' and AUTOCF_CODE='" & Row.Item("AUTOCF_CODE") & "'")
                If dr.Item("flag") = "Yp" Then ypMoney += dr.Item("cf_money")
                cfMoney += dr.Item("cf_money")
            Next

            If RadioButton1.Checked = True Then
                Dim V_Cf_Code As String = F_MaxCode(Format(CDate(DateComobo.Text), "yyMMdd"))
                Dim Arr As New ArrayList


                If Have_Yp = 0 Then
                    Arr.Add("Insert into Bl_Cf(Yy_Code,Cf_Code,Yf_Code,Bl_Code,Ks_Code,Ys_Code,Cf_Date,Cf_Time,Cf_Money,Cf_YpMoney,Jsr_Code,Cf_Print,AutoCf_Code,Cf_Qr,Cf_Qr_Date,Jsr_Code_Qr,Ly_Wc) Values('" & Row.Item("Yy_Code") & "','" & V_Cf_Code & "','" & Row.Item("Yf_Code") & "','" & Row.Item("Bl_Code") & "','" & Row.Item("Ks_Code") & "','" & Row.Item("Ys_Code") & "','" & CDate(DateComobo.Text) & "','" & Format(CfTimeDateEdit.Value, "HH:mm:ss") & "','" & cfMoney & "','" & ypMoney & "','" & Row.Item("Jsr_Code") & "','是','" & Row.Item("AutoCf_Code") & "','是',getdate(),'" & Row.Item("Jsr_Code") & "','是')")
                Else
                    Arr.Add("Insert Into Bl_Cf(Yy_Code,Cf_Code,Yf_Code,Bl_Code,Ks_Code,Ys_Code,Cf_Date,Cf_Time,Cf_Money,Cf_YpMoney,Jsr_Code,Cf_Print,AutoCf_Code)Values ('" & Row.Item("Yy_Code") & "','" & V_Cf_Code & "','" & Row.Item("Yf_Code") & "','" & Row.Item("Bl_Code") & "','" & Row.Item("Ks_Code") & "','" & Row.Item("Ys_Code") & "','" & CDate(DateComobo.Text) & "','" & Format(CfTimeDateEdit.Value, "HH:mm:ss") & "','" & cfMoney & "','" & ypMoney & "','" & Row.Item("Jsr_Code") & "','是','" & Row.Item("AutoCf_Code") & "') ")
                End If

                For Each tmpRow In My_Dataset.Tables("医嘱明细").Select("AutoCf_Code='" & Row.Item("AutoCf_Code") & "' and IsCheck='true'")
                    If tmpRow.Item("flag") = "Yp" Then
                        Arr.Add("Insert Into Bl_CfYp(Yy_Code,Cf_Code,Xx_Code,Cf_Sl,Cf_Dj,Cf_Money,Cf_Lb,Yp_Yfyl,Combo_Id,IsCombo,Dosage,Administration_Name,Group_Id,Frequency,Mx_Dw,Administration_Code,Freq_Code)values ('" & tmpRow.Item("Yy_Code") & "','" & V_Cf_Code & "','" & tmpRow.Item("Xm_Code") & "','" & tmpRow.Item("Cf_Sl") & "','" & tmpRow.Item("Cf_Dj") & "','" & tmpRow.Item("Cf_Money") & "','" & tmpRow.Item("Cf_Lb") & "','" & tmpRow.Item("Yp_Yfyl") & "'," & IIf(tmpRow.Item("Combo_Id") Is DBNull.Value, "Null", tmpRow.Item("Combo_Id")) & ",'" & tmpRow.Item("IsCombo") & "'," & IIf(tmpRow.Item("Dosage") Is DBNull.Value, "Null", tmpRow.Item("Dosage")) & ",'" & tmpRow.Item("Administration_Name") & "','" & tmpRow.Item("Group_Id") & "','" & tmpRow.Item("Frequency") & "','" & tmpRow.Item("Mx_Dw") & "','" & tmpRow.Item("Administration_Code") & "','" & tmpRow.Item("Freq_Code") & "')")
                    Else
                        Arr.Add("Insert Into Bl_Cfxm(Yy_Code,Cf_Code,Xm_Code,Cf_Sl,Cf_Dj,Cf_Money,Cf_Lb,Templet_Code)values ('" & tmpRow.Item("Yy_Code") & "','" & V_Cf_Code & "','" & tmpRow.Item("Xm_Code") & "','" & tmpRow.Item("Cf_Sl") & "','" & tmpRow.Item("Cf_Dj") & "','" & tmpRow.Item("Cf_Money") & "','" & tmpRow.Item("Cf_Lb") & "','" & tmpRow.Item("Templet_Code") & "')")
                    End If
                    My_Dataset.Tables("医嘱明细").Rows.Remove(tmpRow)

                Next



                For Each tmpRow In My_Dataset.Tables("医嘱明细").Select("AutoCf_Code='" & Row.Item("AutoCf_Code") & "' and IsCheck='false'")
                    My_Dataset.Tables("医嘱明细").Rows.Remove(tmpRow)
                Next

                Arr.Add("Insert Into Bl_Czd(Cf_Code,Bl_Code,Ry_BlCode,Ry_Name,Bc_Name,Ys_Name,Z_Name,Xx_Code,Use_Num,Czd_Memo,Yp_Name,Mx_Gg,Mx_Cd,Yp_Yxq,Mx_Dw)   SELECT '" & V_Cf_Code & "',Bl_Code,Ry_BlCode,Ry_Name,Bc_Name,Ys_Name,Z_Name,Xx_Code,Use_Num,Czd_Memo,Yp_Name,Mx_Gg,Mx_Cd,Yp_Yxq,Mx_Dw  FROM Auto_Czd where AutoCf_Code='" & Row.Item("AutoCf_Code") & "'")

                If Row.Item("Everyday_Times") > 1 Then
                    Dim V_Next_Execut_Time As String
                    Dim V_Last_Execut_Time As String
                    Dim V_Execut_Border_Time As String
                    If DateAdd(DateInterval.Hour, Row.Item("Remind_interval"), Row.Item("Next_Execut_Time")) <= DateAdd(DateInterval.Hour, (Row.Item("Everyday_Times") - 1) * Row.Item("Remind_interval"), Row.Item("First_Execut_Time")) Then
                        V_Next_Execut_Time = Format(DateAdd(DateInterval.Hour, Row.Item("Remind_interval"), Row.Item("Next_Execut_Time")), "HH:mm")
                        V_Last_Execut_Time = Row.Item("Next_Execut_Time")   '如果未执行完则执行时间依次后移
                        V_Execut_Border_Time = V_Last_Execut_Time
                    Else
                        V_Next_Execut_Time = Row.Item("First_Execut_Time") & ""
                        V_Last_Execut_Time = "00:00" '取到首次执行的最长时间段
                        V_Execut_Border_Time = "23:59:59"
                    End If
                    Arr.Add("Update Auto_Cf Set Last_Execut_Time='" & V_Last_Execut_Time & "',Next_Execut_Time='" & V_Next_Execut_Time & "',Execut_Border_Time=Convert(Varchar(10),getdate(),126)+' '+'" & V_Execut_Border_Time & "'  where  AutoCf_Code='" & Row.Item("AutoCf_Code") & "'")
                Else
                    Arr.Add("Update Auto_Cf Set Execut_Border_Time=Convert(Varchar(10),getdate(),126)+' '+'23:59:59'  where  AutoCf_Code='" & Row.Item("AutoCf_Code") & "'")
                End If
                Try
                    HisVar.HisVar.Sqldal.ExecuteSqlTran(Arr)
                    If Have_Yp > 0 Then
                        Dim Ly_Row As DataRow = Ly_Table.NewRow
                        Ly_Row.Item("isCheck") = False
                        Ly_Row.Item("Cf_Code") = V_Cf_Code
                        Ly_Row.Item("Ry_Name") = Row.Item("Ry_Name")
                        Ly_Row.Item("Cf_Date") = CDate(DateComobo.Text & " " & Format(CfTimeDateEdit.Value, "HH:mm"))
                        Ly_Row.Item("Ks_Name") = Row.Item("Ks_Name")
                        Ly_Row.Item("Ys_Name") = Row.Item("Ys_Name")
                        Ly_Row.Item("Cf_YpMoney") = ypMoney
                        Ly_Row.Item("AutoCf_Code1") = "长期医嘱"
                        Ly_Table.Rows.Add(Ly_Row)
                        Ly_Row.AcceptChanges()
                    End If
                    My_Dataset.Tables("长期医嘱").Rows.Remove(Row)
                Catch ex As Exception
                    MsgBox(ex.Message.ToString, MsgBoxStyle.Information, "提示")
                    Exit Sub
                End Try
            ElseIf RadioButton2.Checked = True Then
                Dim I As Integer
                For I = 1 To Row.Item("Everyday_Times")   '根据执行次数全部导入
                    Dim V_Cf_Code As String = F_MaxCode(Format(CDate(DateComobo.Text), "yyMMdd"))
                    Dim Arr As New ArrayList

                    If Have_Yp = 0 Then
                        Arr.Add("Insert into Bl_Cf (Yy_Code,Cf_Code,Yf_Code,Bl_Code,Ks_Code,Ys_Code,Cf_Date,Cf_Time,Cf_Money,Cf_YpMoney,Jsr_Code,Cf_Print,AutoCf_Code,Cf_Qr,Cf_Qr_Date,Jsr_Code_Qr,Ly_Wc) Values('" & Row.Item("Yy_Code") & "','" & V_Cf_Code & "','" & Row.Item("Yf_Code") & "','" & Row.Item("Bl_Code") & "','" & Row.Item("Ks_Code") & "','" & Row.Item("Ys_Code") & "','" & CDate(DateComobo.Text) & "','" & Format(CfTimeDateEdit.Value, "HH:mm:ss") & "','" & cfMoney & "','" & ypMoney & "','" & Row.Item("Jsr_Code") & "','是','" & Row.Item("AutoCf_Code") & "','是',getdate(),'" & Row.Item("Jsr_Code") & "','是')")
                    Else
                        Arr.Add("Insert Into Bl_Cf(Yy_Code,Cf_Code,Yf_Code,Bl_Code,Ks_Code,Ys_Code,Cf_Date,Cf_Time,Cf_Money,Cf_YpMoney,Jsr_Code,Cf_Print,AutoCf_Code)Values ('" & Row.Item("Yy_Code") & "','" & V_Cf_Code & "','" & Row.Item("Yf_Code") & "','" & Row.Item("Bl_Code") & "','" & Row.Item("Ks_Code") & "','" & Row.Item("Ys_Code") & "','" & CDate(DateComobo.Text) & "','" & Format(CfTimeDateEdit.Value, "HH:mm:ss") & "','" & cfMoney & "','" & ypMoney & "','" & Row.Item("Jsr_Code") & "','是','" & Row.Item("AutoCf_Code") & "') ")
                    End If

                    For Each tmpRow In My_Dataset.Tables("医嘱明细").Select("AutoCf_Code='" & Row.Item("AutoCf_Code") & "' and IsCheck='true'")
                        If tmpRow.Item("flag") = "Yp" Then
                            Arr.Add("Insert Into Bl_CfYp(Yy_Code,Cf_Code,Xx_Code,Cf_Sl,Cf_Dj,Cf_Money,Cf_Lb,Yp_Yfyl,Combo_Id,IsCombo,Dosage,Administration_Name,Group_Id,Frequency,Mx_Dw,Administration_Code,Freq_Code)values ('" & tmpRow.Item("Yy_Code") & "','" & V_Cf_Code & "','" & tmpRow.Item("Xm_Code") & "','" & tmpRow.Item("Cf_Sl") & "','" & tmpRow.Item("Cf_Dj") & "','" & tmpRow.Item("Cf_Money") & "','" & tmpRow.Item("Cf_Lb") & "','" & tmpRow.Item("Yp_Yfyl") & "'," & IIf(tmpRow.Item("Combo_Id") Is DBNull.Value, "Null", tmpRow.Item("Combo_Id")) & ",'" & tmpRow.Item("IsCombo") & "'," & IIf(tmpRow.Item("Dosage") Is DBNull.Value, "Null", tmpRow.Item("Dosage")) & ",'" & tmpRow.Item("Administration_Name") & "','" & tmpRow.Item("Group_Id") & "','" & tmpRow.Item("Frequency") & "','" & tmpRow.Item("Mx_Dw") & "','" & tmpRow.Item("Administration_Code") & "','" & tmpRow.Item("Freq_Code") & "')")
                        Else
                            Arr.Add("Insert Into Bl_Cfxm(Yy_Code,Cf_Code,Xm_Code,Cf_Sl,Cf_Dj,Cf_Money,Cf_Lb,Templet_Code)values('" & tmpRow.Item("Yy_Code") & "','" & V_Cf_Code & "','" & tmpRow.Item("Xm_Code") & "','" & tmpRow.Item("Cf_Sl") & "','" & tmpRow.Item("Cf_Dj") & "','" & tmpRow.Item("Cf_Money") & "','" & tmpRow.Item("Cf_Lb") & "','" & tmpRow.Item("Templet_Code") & "')")
                        End If
                        My_Dataset.Tables("医嘱明细").Rows.Remove(tmpRow)
                    Next



                    For Each tmpRow In My_Dataset.Tables("医嘱明细").Select("AutoCf_Code='" & Row.Item("AutoCf_Code") & "' and IsCheck='false'")
                        My_Dataset.Tables("医嘱明细").Rows.Remove(tmpRow)
                    Next


                    Arr.Add("Insert Into Bl_Czd(Cf_Code,Bl_Code,Ry_BlCode,Ry_Name,Bc_Name,Ys_Name,Z_Name,Xx_Code,Use_Num,Czd_Memo,Yp_Name,Mx_Gg,Mx_Cd,Yp_Yxq,Mx_Dw)   SELECT '" & V_Cf_Code & "',Bl_Code,Ry_BlCode,Ry_Name,Bc_Name,Ys_Name,Z_Name,Xx_Code,Use_Num,Czd_Memo,Yp_Name,Mx_Gg,Mx_Cd,Yp_Yxq,Mx_Dw  FROM Auto_Czd where AutoCf_Code='" & Row.Item("AutoCf_Code") & "'")
                    Try
                        HisVar.HisVar.Sqldal.ExecuteSqlTran(Arr)
                        If Have_Yp > 0 Then
                            Dim Ly_Row As DataRow = Ly_Table.NewRow
                            Ly_Row.Item("isCheck") = False
                            Ly_Row.Item("Cf_Code") = V_Cf_Code
                            Ly_Row.Item("Ry_Name") = Row.Item("Ry_Name")
                            Ly_Row.Item("Cf_Date") = CDate(DateComobo.Text & " " & Format(CfTimeDateEdit.Value, "HH:mm"))
                            Ly_Row.Item("Ks_Name") = Row.Item("Ks_Name")
                            Ly_Row.Item("Ys_Name") = Row.Item("Ys_Name")
                            Ly_Row.Item("Cf_YpMoney") = ypMoney
                            Ly_Row.Item("AutoCf_Code1") = "长期医嘱"
                            Ly_Table.Rows.Add(Ly_Row)
                            Ly_Row.AcceptChanges()
                        End If


                    Catch ex As Exception
                        MsgBox(ex.Message.ToString, MsgBoxStyle.Information, "提示")
                        Exit Sub
                    End Try
                Next
                HisVar.HisVar.Sqldal.ExecuteSql("Update Auto_Cf Set Execut_Border_Time=(Case When Execut_Border_Time>Getdate() then Execut_Border_Time else Convert(Varchar(10),Execute_Tomorrow,126)+' '+'23:59:58' end),Execute_Tomorrow=Dateadd(Day,1,Getdate()) where AutoCf_Code='" & Row.Item("AutoCf_Code") & "'")
                My_Dataset.Tables("长期医嘱").Rows.Remove(Row)
            End If

        Next


        If QfName <> "" Then
            MsgBox("下列患者所选处方可能导致达到押金警戒线，未能完成导入！" & vbCrLf & QfName)
        End If

    End Sub

    Private Function F_MaxCode(ByVal V_MaxCode As String)   '出库编码
        Dim My_Cc As New BaseClass.C_Cc()
        Dim V_Code As String = V_MaxCode
        My_Cc.Get_MaxCode("Bl_Cf", "Cf_Code", 14, "Left(Cf_Code,10)", HisVar.HisVar.WsyCode & V_MaxCode)
        F_MaxCode = My_Cc.编码
        Return F_MaxCode
    End Function

    Private Sub RadioButton2_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles RadioButton2.CheckedChanged
        Call DataInit()
    End Sub


    Private Sub C1FlexGrid1_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1FlexGrid1.Click

        If C1FlexGrid1.Col = 1 And C1FlexGrid1.Rows.Count > 1 Then
            If C1FlexGrid1.Item(C1FlexGrid1.Row, 1) = False Then
                C1FlexGrid1.Item(C1FlexGrid1.Row, 1) = True
                For Each tmpRow In My_Dataset.Tables("医嘱明细").Select("AutoCf_Code='" & C1FlexGrid1.Item(C1FlexGrid1.Row, 2) & "'")
                    tmpRow.Item("IsCheck") = True
                Next
            Else
                C1FlexGrid1.Item(C1FlexGrid1.Row, 1) = False
                For Each tmpRow In My_Dataset.Tables("医嘱明细").Select("AutoCf_Code='" & C1FlexGrid1.Item(C1FlexGrid1.Row, 2) & "'")
                    tmpRow.Item("IsCheck") = False

                Next
            End If

        End If
    End Sub

    Private Sub C1FlexGrid2_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1FlexGrid2.Click

        If C1FlexGrid2.Col = 1 And C1FlexGrid2.Rows.Count > 1 Then
            If C1FlexGrid2.Item(C1FlexGrid2.Row, 1) = False Then
                C1FlexGrid2.Item(C1FlexGrid2.Row, 1) = True
            Else
                C1FlexGrid2.Item(C1FlexGrid2.Row, 1) = False
            End If

        End If
    End Sub

    Private Sub CheckBox2_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ChkKs.CheckedChanged

        Dim K As Integer = 0
        Dim V_KsName As String = HisVar.HisVar.Sqldal.GetSingle("Select ks_Name from Zd_YyKs where ks_Code='" & HisVar.HisVar.XmKs & "'")
        If V_KsName & "" = "" Then
            MsgBox("该操作员未指定所属科室，请管理员指定所属科室后使用该功能！", MsgBoxStyle.Information, "提示")
            Exit Sub
        End If


        For K = 1 To Me.C1FlexGrid1.Rows.Count - 1
            If C1FlexGrid1.Item(K, 6) = V_KsName Then
                Me.C1FlexGrid1.Item(K, 1) = ChkKs.Checked
                For Each tmpRow In My_Dataset.Tables("医嘱明细").Select("AutoCf_Code='" & C1FlexGrid1.Item(K, 2) & "'")
                    tmpRow.Item("IsCheck") = ChkKs.Checked
                Next
            Else
                Me.C1FlexGrid1.Item(K, 1) = False
                For Each tmpRow In My_Dataset.Tables("医嘱明细").Select("AutoCf_Code='" & C1FlexGrid1.Item(K, 2) & "'")
                    tmpRow.Item("IsCheck") = False
                Next
            End If
        Next


        'If Me.ChkKs.Checked = True Then
        '    For K = 1 To Me.C1FlexGrid1.Rows.Count - 1
        '        If C1FlexGrid1.Item(K, 6) = V_KsName Then Me.C1FlexGrid1.Item(K, 1) = True
        '    Next
        'Else
        '    For K = 1 To Me.C1FlexGrid1.Rows.Count - 1
        '        If C1FlexGrid1.Item(K, 6) = V_KsName Then Me.C1FlexGrid1.Item(K, 1) = False
        '    Next
        'End If
    End Sub

    Private Sub ChkAllCheck_CheckedChanged(sender As Object, e As EventArgs) Handles ChkAllCheck.CheckedChanged
        For K = 1 To Me.C1FlexGrid1.Rows.Count - 1
            Me.C1FlexGrid1.Item(K, 1) = ChkAllCheck.Checked
            For Each tmpRow In My_Dataset.Tables("医嘱明细").Select("AutoCf_Code='" & C1FlexGrid1.Item(K, 2) & "'")
                tmpRow.Item("IsCheck") = ChkAllCheck.Checked
            Next
        Next
    End Sub
    Private Sub DateComobo_RowChange(ByVal sender As Object, ByVal e As System.EventArgs) Handles DateComobo.RowChange
        If DateComobo.Visible = False Then Exit Sub
        If DateComobo.SelectedIndex = 0 Then
            CfTimeDateEdit.Enabled = False
            CfTimeDateEdit.Value = Format(Now, "HH:mm:ss")
        ElseIf DateComobo.SelectedIndex = 1 Then
            CfTimeDateEdit.Enabled = True
            CfTimeDateEdit.Value = "06:00:00"
        End If
    End Sub


End Class