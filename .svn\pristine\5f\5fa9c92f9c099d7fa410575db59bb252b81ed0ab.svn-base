﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class Cx_YkYf_Pd
    Inherits HisControl.BaseForm

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(Cx_YkYf_Pd))
        Me.C1Holder1 = New C1.Win.C1Command.C1CommandHolder()
        Me.Comm2 = New C1.Win.C1Command.C1Command()
        Me.Comm1 = New C1.Win.C1Command.C1Command()
        Me.T_Textbox = New C1.Win.C1Input.C1TextBox()
        Me.T_Label = New C1.Win.C1Input.C1Label()
        Me.ToolBar1 = New C1.Win.C1Command.C1ToolBar()
        Me.C1CommandLink1 = New C1.Win.C1Command.C1CommandLink()
        Me.C1CommandLink2 = New C1.Win.C1Command.C1CommandLink()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.T_Line1 = New System.Windows.Forms.Label()
        Me.C1TrueDBGrid1 = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.Image1 = New System.Windows.Forms.ImageList(Me.components)
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel()
        Me.C1TrueDBGrid2 = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        CType(Me.C1Holder1,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.T_Textbox,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.T_Label,System.ComponentModel.ISupportInitialize).BeginInit
        Me.Panel1.SuspendLayout
        CType(Me.C1TrueDBGrid1,System.ComponentModel.ISupportInitialize).BeginInit
        Me.TableLayoutPanel1.SuspendLayout
        CType(Me.C1TrueDBGrid2,System.ComponentModel.ISupportInitialize).BeginInit
        Me.SuspendLayout
        '
        'C1Holder1
        '
        Me.C1Holder1.Commands.Add(Me.Comm2)
        Me.C1Holder1.Commands.Add(Me.Comm1)
        Me.C1Holder1.Owner = Me
        Me.C1Holder1.VisualStyle = C1.Win.C1Command.VisualStyle.Classic
        '
        'Comm2
        '
        Me.Comm2.Image = CType(resources.GetObject("Comm2.Image"),System.Drawing.Image)
        Me.Comm2.Name = "Comm2"
        Me.Comm2.Shortcut = System.Windows.Forms.Shortcut.F3
        Me.Comm2.ShortcutText = ""
        Me.Comm2.Text = "盈亏表"
        Me.Comm2.ToolTipText = "盈亏表"
        '
        'Comm1
        '
        Me.Comm1.Image = CType(resources.GetObject("Comm1.Image"),System.Drawing.Image)
        Me.Comm1.Name = "Comm1"
        Me.Comm1.ShortcutText = ""
        Me.Comm1.Text = "盘点表"
        '
        'T_Textbox
        '
        Me.T_Textbox.BackColor = System.Drawing.Color.White
        Me.T_Textbox.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.T_Textbox.Location = New System.Drawing.Point(180, 5)
        Me.T_Textbox.MaxLength = 10
        Me.T_Textbox.Name = "T_Textbox"
        Me.T_Textbox.Size = New System.Drawing.Size(72, 14)
        Me.T_Textbox.TabIndex = 3
        Me.T_Textbox.TabStop = false
        Me.T_Textbox.Tag = Nothing
        Me.T_Textbox.TextDetached = true
        Me.T_Textbox.TrimStart = true
        Me.T_Textbox.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        '
        'T_Label
        '
        Me.T_Label.AutoSize = true
        Me.T_Label.BackColor = System.Drawing.SystemColors.Control
        Me.T_Label.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.T_Label.ForeColor = System.Drawing.Color.Maroon
        Me.T_Label.Location = New System.Drawing.Point(276, 6)
        Me.T_Label.Name = "T_Label"
        Me.T_Label.Size = New System.Drawing.Size(47, 12)
        Me.T_Label.TabIndex = 2
        Me.T_Label.Tag = Nothing
        Me.T_Label.Text = "T_Label"
        Me.T_Label.TextAlign = System.Drawing.ContentAlignment.BottomLeft
        Me.T_Label.TextDetached = true
        Me.T_Label.TrimStart = true
        '
        'ToolBar1
        '
        Me.ToolBar1.AccessibleName = "Tool Bar"
        Me.ToolBar1.CommandHolder = Me.C1Holder1
        Me.ToolBar1.CommandLinks.AddRange(New C1.Win.C1Command.C1CommandLink() {Me.C1CommandLink1, Me.C1CommandLink2})
        Me.ToolBar1.Location = New System.Drawing.Point(1, 0)
        Me.ToolBar1.MinButtonSize = 22
        Me.ToolBar1.Movable = false
        Me.ToolBar1.Name = "ToolBar1"
        Me.ToolBar1.Size = New System.Drawing.Size(134, 22)
        Me.ToolBar1.Text = "C1ToolBar1"
        Me.ToolBar1.VisualStyle = C1.Win.C1Command.VisualStyle.Classic
        Me.ToolBar1.VisualStyleBase = C1.Win.C1Command.VisualStyle.Classic
        '
        'C1CommandLink1
        '
        Me.C1CommandLink1.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image),C1.Win.C1Command.ButtonLookFlags)
        Me.C1CommandLink1.Command = Me.Comm1
        '
        'C1CommandLink2
        '
        Me.C1CommandLink2.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image),C1.Win.C1Command.ButtonLookFlags)
        Me.C1CommandLink2.Command = Me.Comm2
        Me.C1CommandLink2.SortOrder = 1
        Me.C1CommandLink2.Text = "盈亏表"
        Me.C1CommandLink2.ToolTipText = "盈亏表"
        '
        'Panel1
        '
        Me.Panel1.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Panel1.Controls.Add(Me.ToolBar1)
        Me.Panel1.Controls.Add(Me.T_Line1)
        Me.Panel1.Controls.Add(Me.T_Textbox)
        Me.Panel1.Controls.Add(Me.T_Label)
        Me.Panel1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel1.Location = New System.Drawing.Point(3, 173)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(845, 26)
        Me.Panel1.TabIndex = 1
        '
        'T_Line1
        '
        Me.T_Line1.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.T_Line1.Location = New System.Drawing.Point(172, 0)
        Me.T_Line1.Name = "T_Line1"
        Me.T_Line1.Size = New System.Drawing.Size(2, 27)
        Me.T_Line1.TabIndex = 4
        Me.T_Line1.Text = "Label1"
        '
        'C1TrueDBGrid1
        '
        Me.C1TrueDBGrid1.GroupByCaption = "Drag a column header here to group by that column"
        Me.C1TrueDBGrid1.Images.Add(CType(resources.GetObject("C1TrueDBGrid1.Images"),System.Drawing.Image))
        Me.C1TrueDBGrid1.Location = New System.Drawing.Point(3, 205)
        Me.C1TrueDBGrid1.Name = "C1TrueDBGrid1"
        Me.C1TrueDBGrid1.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.C1TrueDBGrid1.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.C1TrueDBGrid1.PreviewInfo.ZoomFactor = 75R
        Me.C1TrueDBGrid1.PrintInfo.MeasurementDevice = C1.Win.C1TrueDBGrid.PrintInfo.MeasurementDeviceEnum.Screen
        Me.C1TrueDBGrid1.PrintInfo.MeasurementPrinterName = Nothing
        Me.C1TrueDBGrid1.Size = New System.Drawing.Size(324, 243)
        Me.C1TrueDBGrid1.TabIndex = 0
        Me.C1TrueDBGrid1.Text = "C1TrueDBGrid1"
        Me.C1TrueDBGrid1.UseCompatibleTextRendering = false
        Me.C1TrueDBGrid1.PropBag = resources.GetString("C1TrueDBGrid1.PropBag")
        '
        'Image1
        '
        Me.Image1.ImageStream = CType(resources.GetObject("Image1.ImageStream"),System.Windows.Forms.ImageListStreamer)
        Me.Image1.TransparentColor = System.Drawing.Color.White
        Me.Image1.Images.SetKeyName(0, "")
        Me.Image1.Images.SetKeyName(1, "")
        Me.Image1.Images.SetKeyName(2, "")
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.ColumnCount = 1
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100!))
        Me.TableLayoutPanel1.Controls.Add(Me.C1TrueDBGrid2, 0, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.C1TrueDBGrid1, 0, 3)
        Me.TableLayoutPanel1.Controls.Add(Me.Panel1, 0, 2)
        Me.TableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(0, 0)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.RowCount = 4
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 20!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 150!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 32!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 154!))
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(851, 528)
        Me.TableLayoutPanel1.TabIndex = 2
        '
        'C1TrueDBGrid2
        '
        Me.C1TrueDBGrid2.GroupByCaption = "Drag a column header here to group by that column"
        Me.C1TrueDBGrid2.Images.Add(CType(resources.GetObject("C1TrueDBGrid2.Images"),System.Drawing.Image))
        Me.C1TrueDBGrid2.Location = New System.Drawing.Point(3, 23)
        Me.C1TrueDBGrid2.Name = "C1TrueDBGrid2"
        Me.C1TrueDBGrid2.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.C1TrueDBGrid2.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.C1TrueDBGrid2.PreviewInfo.ZoomFactor = 75R
        Me.C1TrueDBGrid2.PrintInfo.MeasurementDevice = C1.Win.C1TrueDBGrid.PrintInfo.MeasurementDeviceEnum.Screen
        Me.C1TrueDBGrid2.PrintInfo.MeasurementPrinterName = Nothing
        Me.C1TrueDBGrid2.Size = New System.Drawing.Size(240, 144)
        Me.C1TrueDBGrid2.TabIndex = 0
        Me.C1TrueDBGrid2.Text = "C1TrueDBGrid2"
        Me.C1TrueDBGrid2.UseCompatibleTextRendering = false
        Me.C1TrueDBGrid2.PropBag = resources.GetString("C1TrueDBGrid2.PropBag")
        '
        'Cx_YkYf_Pd
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6!, 12!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(851, 528)
        Me.Controls.Add(Me.TableLayoutPanel1)
        Me.Icon = CType(resources.GetObject("$this.Icon"),System.Drawing.Icon)
        Me.Name = "Cx_YkYf_Pd"
        Me.ShowInTaskbar = false
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Tag = "大类"
        Me.Text = "药房库存盘点明细"
        CType(Me.C1Holder1,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.T_Textbox,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.T_Label,System.ComponentModel.ISupportInitialize).EndInit
        Me.Panel1.ResumeLayout(false)
        Me.Panel1.PerformLayout
        CType(Me.C1TrueDBGrid1,System.ComponentModel.ISupportInitialize).EndInit
        Me.TableLayoutPanel1.ResumeLayout(false)
        CType(Me.C1TrueDBGrid2,System.ComponentModel.ISupportInitialize).EndInit
        Me.ResumeLayout(false)

End Sub
    Friend WithEvents C1Holder1 As C1.Win.C1Command.C1CommandHolder
    Friend WithEvents Comm2 As C1.Win.C1Command.C1Command
    Friend WithEvents ToolBar1 As C1.Win.C1Command.C1ToolBar
    Friend WithEvents T_Textbox As C1.Win.C1Input.C1TextBox
    Friend WithEvents T_Label As C1.Win.C1Input.C1Label
    Friend WithEvents C1TrueDBGrid1 As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents Image1 As System.Windows.Forms.ImageList
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents T_Line1 As System.Windows.Forms.Label
    Friend WithEvents Comm1 As C1.Win.C1Command.C1Command
    Friend WithEvents C1CommandLink1 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents C1CommandLink2 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents TableLayoutPanel1 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents C1TrueDBGrid2 As C1.Win.C1TrueDBGrid.C1TrueDBGrid

End Class
