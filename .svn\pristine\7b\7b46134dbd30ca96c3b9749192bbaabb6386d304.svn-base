﻿'------------------------------------------------------------------------------
' <auto-generated>
'     此代码由工具生成。
'     运行时版本:4.0.30319.42000
'
'     对此文件的更改可能会导致不正确的行为，并且如果
'     重新生成代码，这些更改将会丢失。
' </auto-generated>
'------------------------------------------------------------------------------

Option Strict Off
Option Explicit On

Imports System
Imports System.ComponentModel
Imports System.Data
Imports System.Diagnostics
Imports System.Web.Services
Imports System.Web.Services.Protocols
Imports System.Xml.Serialization

'
'此源代码是由 Microsoft.VSDesigner 4.0.30319.42000 版自动生成。
'
Namespace CenterBase
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code"),  _
     System.Web.Services.WebServiceBindingAttribute(Name:="DotNetServiceSoap", [Namespace]:="http://tempuri.org/")>  _
    Partial Public Class DotNetService
        Inherits System.Web.Services.Protocols.SoapHttpClientProtocol
        
        Private HelloWorldOperationCompleted As System.Threading.SendOrPostCallback
        
        Private GetMlYpGxOperationCompleted As System.Threading.SendOrPostCallback
        
        Private GetMlYpjxOperationCompleted As System.Threading.SendOrPostCallback
        
        Private GetMlJb1OperationCompleted As System.Threading.SendOrPostCallback
        
        Private GetMlJb3OperationCompleted As System.Threading.SendOrPostCallback
        
        Private GetMlXm1OperationCompleted As System.Threading.SendOrPostCallback
        
        Private GetMlXm3OperationCompleted As System.Threading.SendOrPostCallback
        
        Private GetMlYp2OperationCompleted As System.Threading.SendOrPostCallback
        
        Private GetMlYp3OperationCompleted As System.Threading.SendOrPostCallback
        
        Private GetMlJbYpJxOperationCompleted As System.Threading.SendOrPostCallback
        
        Private UpDate_JblbOperationCompleted As System.Threading.SendOrPostCallback
        
        Private UpDate_JbOperationCompleted As System.Threading.SendOrPostCallback
        
        Private UpDate_XmlbOperationCompleted As System.Threading.SendOrPostCallback
        
        Private UpDate_XmOperationCompleted As System.Threading.SendOrPostCallback
        
        Private UpDate_Yp2OperationCompleted As System.Threading.SendOrPostCallback
        
        Private UpDate_Yp3OperationCompleted As System.Threading.SendOrPostCallback
        
        Private UpDate_YpjxOperationCompleted As System.Threading.SendOrPostCallback
        
        Private UpDate_YpGxOperationCompleted As System.Threading.SendOrPostCallback
        
        Private Ckeck_YbzlOperationCompleted As System.Threading.SendOrPostCallback
        
        Private GetYbZL_XmOperationCompleted As System.Threading.SendOrPostCallback
        
        Private F_GetJkdaRyXxOperationCompleted As System.Threading.SendOrPostCallback
        
        Private F_GetJkdaJsrOperationCompleted As System.Threading.SendOrPostCallback
        
        Private F_GetSupJsrOperationCompleted As System.Threading.SendOrPostCallback
        
        Private F_InserGxySfOperationCompleted As System.Threading.SendOrPostCallback
        
        Private F_InserTnbSfOperationCompleted As System.Threading.SendOrPostCallback
        
        Private useDefaultCredentialsSetExplicitly As Boolean
        
        '''<remarks/>
        Public Sub New()
            MyBase.New
            Me.Url = Global.HisVar.My.MySettings.Default.HisVar_WebReference_DotNetService
            If (Me.IsLocalFileSystemWebService(Me.Url) = true) Then
                Me.UseDefaultCredentials = true
                Me.useDefaultCredentialsSetExplicitly = false
            Else
                Me.useDefaultCredentialsSetExplicitly = true
            End If
        End Sub
        
        Public Shadows Property Url() As String
            Get
                Return MyBase.Url
            End Get
            Set
                If (((Me.IsLocalFileSystemWebService(MyBase.Url) = true)  _
                            AndAlso (Me.useDefaultCredentialsSetExplicitly = false))  _
                            AndAlso (Me.IsLocalFileSystemWebService(value) = false)) Then
                    MyBase.UseDefaultCredentials = false
                End If
                MyBase.Url = value
            End Set
        End Property
        
        Public Shadows Property UseDefaultCredentials() As Boolean
            Get
                Return MyBase.UseDefaultCredentials
            End Get
            Set
                MyBase.UseDefaultCredentials = value
                Me.useDefaultCredentialsSetExplicitly = true
            End Set
        End Property
        
        '''<remarks/>
        Public Event HelloWorldCompleted As HelloWorldCompletedEventHandler
        
        '''<remarks/>
        Public Event GetMlYpGxCompleted As GetMlYpGxCompletedEventHandler
        
        '''<remarks/>
        Public Event GetMlYpjxCompleted As GetMlYpjxCompletedEventHandler
        
        '''<remarks/>
        Public Event GetMlJb1Completed As GetMlJb1CompletedEventHandler
        
        '''<remarks/>
        Public Event GetMlJb3Completed As GetMlJb3CompletedEventHandler
        
        '''<remarks/>
        Public Event GetMlXm1Completed As GetMlXm1CompletedEventHandler
        
        '''<remarks/>
        Public Event GetMlXm3Completed As GetMlXm3CompletedEventHandler
        
        '''<remarks/>
        Public Event GetMlYp2Completed As GetMlYp2CompletedEventHandler
        
        '''<remarks/>
        Public Event GetMlYp3Completed As GetMlYp3CompletedEventHandler
        
        '''<remarks/>
        Public Event GetMlJbYpJxCompleted As GetMlJbYpJxCompletedEventHandler
        
        '''<remarks/>
        Public Event UpDate_JblbCompleted As UpDate_JblbCompletedEventHandler
        
        '''<remarks/>
        Public Event UpDate_JbCompleted As UpDate_JbCompletedEventHandler
        
        '''<remarks/>
        Public Event UpDate_XmlbCompleted As UpDate_XmlbCompletedEventHandler
        
        '''<remarks/>
        Public Event UpDate_XmCompleted As UpDate_XmCompletedEventHandler
        
        '''<remarks/>
        Public Event UpDate_Yp2Completed As UpDate_Yp2CompletedEventHandler
        
        '''<remarks/>
        Public Event UpDate_Yp3Completed As UpDate_Yp3CompletedEventHandler
        
        '''<remarks/>
        Public Event UpDate_YpjxCompleted As UpDate_YpjxCompletedEventHandler
        
        '''<remarks/>
        Public Event UpDate_YpGxCompleted As UpDate_YpGxCompletedEventHandler
        
        '''<remarks/>
        Public Event Ckeck_YbzlCompleted As Ckeck_YbzlCompletedEventHandler
        
        '''<remarks/>
        Public Event GetYbZL_XmCompleted As GetYbZL_XmCompletedEventHandler
        
        '''<remarks/>
        Public Event F_GetJkdaRyXxCompleted As F_GetJkdaRyXxCompletedEventHandler
        
        '''<remarks/>
        Public Event F_GetJkdaJsrCompleted As F_GetJkdaJsrCompletedEventHandler
        
        '''<remarks/>
        Public Event F_GetSupJsrCompleted As F_GetSupJsrCompletedEventHandler
        
        '''<remarks/>
        Public Event F_InserGxySfCompleted As F_InserGxySfCompletedEventHandler
        
        '''<remarks/>
        Public Event F_InserTnbSfCompleted As F_InserTnbSfCompletedEventHandler
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/HelloWorld", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function HelloWorld() As String
            Dim results() As Object = Me.Invoke("HelloWorld", New Object(-1) {})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub HelloWorldAsync()
            Me.HelloWorldAsync(Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub HelloWorldAsync(ByVal userState As Object)
            If (Me.HelloWorldOperationCompleted Is Nothing) Then
                Me.HelloWorldOperationCompleted = AddressOf Me.OnHelloWorldOperationCompleted
            End If
            Me.InvokeAsync("HelloWorld", New Object(-1) {}, Me.HelloWorldOperationCompleted, userState)
        End Sub
        
        Private Sub OnHelloWorldOperationCompleted(ByVal arg As Object)
            If (Not (Me.HelloWorldCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent HelloWorldCompleted(Me, New HelloWorldCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/GetMlYpGx", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function GetMlYpGx(ByVal V_YpGx_Code As String) As System.Data.DataSet
            Dim results() As Object = Me.Invoke("GetMlYpGx", New Object() {V_YpGx_Code})
            Return CType(results(0),System.Data.DataSet)
        End Function
        
        '''<remarks/>
        Public Overloads Sub GetMlYpGxAsync(ByVal V_YpGx_Code As String)
            Me.GetMlYpGxAsync(V_YpGx_Code, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub GetMlYpGxAsync(ByVal V_YpGx_Code As String, ByVal userState As Object)
            If (Me.GetMlYpGxOperationCompleted Is Nothing) Then
                Me.GetMlYpGxOperationCompleted = AddressOf Me.OnGetMlYpGxOperationCompleted
            End If
            Me.InvokeAsync("GetMlYpGx", New Object() {V_YpGx_Code}, Me.GetMlYpGxOperationCompleted, userState)
        End Sub
        
        Private Sub OnGetMlYpGxOperationCompleted(ByVal arg As Object)
            If (Not (Me.GetMlYpGxCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent GetMlYpGxCompleted(Me, New GetMlYpGxCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/GetMlYpjx", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function GetMlYpjx(ByVal V_Jx_Code As String) As System.Data.DataSet
            Dim results() As Object = Me.Invoke("GetMlYpjx", New Object() {V_Jx_Code})
            Return CType(results(0),System.Data.DataSet)
        End Function
        
        '''<remarks/>
        Public Overloads Sub GetMlYpjxAsync(ByVal V_Jx_Code As String)
            Me.GetMlYpjxAsync(V_Jx_Code, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub GetMlYpjxAsync(ByVal V_Jx_Code As String, ByVal userState As Object)
            If (Me.GetMlYpjxOperationCompleted Is Nothing) Then
                Me.GetMlYpjxOperationCompleted = AddressOf Me.OnGetMlYpjxOperationCompleted
            End If
            Me.InvokeAsync("GetMlYpjx", New Object() {V_Jx_Code}, Me.GetMlYpjxOperationCompleted, userState)
        End Sub
        
        Private Sub OnGetMlYpjxOperationCompleted(ByVal arg As Object)
            If (Not (Me.GetMlYpjxCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent GetMlYpjxCompleted(Me, New GetMlYpjxCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/GetMlJb1", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function GetMlJb1(ByVal V_Jb1_Code As String) As System.Data.DataSet
            Dim results() As Object = Me.Invoke("GetMlJb1", New Object() {V_Jb1_Code})
            Return CType(results(0),System.Data.DataSet)
        End Function
        
        '''<remarks/>
        Public Overloads Sub GetMlJb1Async(ByVal V_Jb1_Code As String)
            Me.GetMlJb1Async(V_Jb1_Code, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub GetMlJb1Async(ByVal V_Jb1_Code As String, ByVal userState As Object)
            If (Me.GetMlJb1OperationCompleted Is Nothing) Then
                Me.GetMlJb1OperationCompleted = AddressOf Me.OnGetMlJb1OperationCompleted
            End If
            Me.InvokeAsync("GetMlJb1", New Object() {V_Jb1_Code}, Me.GetMlJb1OperationCompleted, userState)
        End Sub
        
        Private Sub OnGetMlJb1OperationCompleted(ByVal arg As Object)
            If (Not (Me.GetMlJb1CompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent GetMlJb1Completed(Me, New GetMlJb1CompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/GetMlJb3", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function GetMlJb3(ByVal V_Jblb_Code As String) As System.Data.DataSet
            Dim results() As Object = Me.Invoke("GetMlJb3", New Object() {V_Jblb_Code})
            Return CType(results(0),System.Data.DataSet)
        End Function
        
        '''<remarks/>
        Public Overloads Sub GetMlJb3Async(ByVal V_Jblb_Code As String)
            Me.GetMlJb3Async(V_Jblb_Code, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub GetMlJb3Async(ByVal V_Jblb_Code As String, ByVal userState As Object)
            If (Me.GetMlJb3OperationCompleted Is Nothing) Then
                Me.GetMlJb3OperationCompleted = AddressOf Me.OnGetMlJb3OperationCompleted
            End If
            Me.InvokeAsync("GetMlJb3", New Object() {V_Jblb_Code}, Me.GetMlJb3OperationCompleted, userState)
        End Sub
        
        Private Sub OnGetMlJb3OperationCompleted(ByVal arg As Object)
            If (Not (Me.GetMlJb3CompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent GetMlJb3Completed(Me, New GetMlJb3CompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/GetMlXm1", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function GetMlXm1(ByVal V_Xm1_Code As String) As System.Data.DataSet
            Dim results() As Object = Me.Invoke("GetMlXm1", New Object() {V_Xm1_Code})
            Return CType(results(0),System.Data.DataSet)
        End Function
        
        '''<remarks/>
        Public Overloads Sub GetMlXm1Async(ByVal V_Xm1_Code As String)
            Me.GetMlXm1Async(V_Xm1_Code, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub GetMlXm1Async(ByVal V_Xm1_Code As String, ByVal userState As Object)
            If (Me.GetMlXm1OperationCompleted Is Nothing) Then
                Me.GetMlXm1OperationCompleted = AddressOf Me.OnGetMlXm1OperationCompleted
            End If
            Me.InvokeAsync("GetMlXm1", New Object() {V_Xm1_Code}, Me.GetMlXm1OperationCompleted, userState)
        End Sub
        
        Private Sub OnGetMlXm1OperationCompleted(ByVal arg As Object)
            If (Not (Me.GetMlXm1CompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent GetMlXm1Completed(Me, New GetMlXm1CompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/GetMlXm3", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function GetMlXm3(ByVal V_Xmlb_Code As String) As System.Data.DataSet
            Dim results() As Object = Me.Invoke("GetMlXm3", New Object() {V_Xmlb_Code})
            Return CType(results(0),System.Data.DataSet)
        End Function
        
        '''<remarks/>
        Public Overloads Sub GetMlXm3Async(ByVal V_Xmlb_Code As String)
            Me.GetMlXm3Async(V_Xmlb_Code, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub GetMlXm3Async(ByVal V_Xmlb_Code As String, ByVal userState As Object)
            If (Me.GetMlXm3OperationCompleted Is Nothing) Then
                Me.GetMlXm3OperationCompleted = AddressOf Me.OnGetMlXm3OperationCompleted
            End If
            Me.InvokeAsync("GetMlXm3", New Object() {V_Xmlb_Code}, Me.GetMlXm3OperationCompleted, userState)
        End Sub
        
        Private Sub OnGetMlXm3OperationCompleted(ByVal arg As Object)
            If (Not (Me.GetMlXm3CompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent GetMlXm3Completed(Me, New GetMlXm3CompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/GetMlYp2", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function GetMlYp2(ByVal V_Dl_Code As String) As System.Data.DataSet
            Dim results() As Object = Me.Invoke("GetMlYp2", New Object() {V_Dl_Code})
            Return CType(results(0),System.Data.DataSet)
        End Function
        
        '''<remarks/>
        Public Overloads Sub GetMlYp2Async(ByVal V_Dl_Code As String)
            Me.GetMlYp2Async(V_Dl_Code, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub GetMlYp2Async(ByVal V_Dl_Code As String, ByVal userState As Object)
            If (Me.GetMlYp2OperationCompleted Is Nothing) Then
                Me.GetMlYp2OperationCompleted = AddressOf Me.OnGetMlYp2OperationCompleted
            End If
            Me.InvokeAsync("GetMlYp2", New Object() {V_Dl_Code}, Me.GetMlYp2OperationCompleted, userState)
        End Sub
        
        Private Sub OnGetMlYp2OperationCompleted(ByVal arg As Object)
            If (Not (Me.GetMlYp2CompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent GetMlYp2Completed(Me, New GetMlYp2CompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/GetMlYp3", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function GetMlYp3(ByVal V_Yp_Code As String) As System.Data.DataSet
            Dim results() As Object = Me.Invoke("GetMlYp3", New Object() {V_Yp_Code})
            Return CType(results(0),System.Data.DataSet)
        End Function
        
        '''<remarks/>
        Public Overloads Sub GetMlYp3Async(ByVal V_Yp_Code As String)
            Me.GetMlYp3Async(V_Yp_Code, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub GetMlYp3Async(ByVal V_Yp_Code As String, ByVal userState As Object)
            If (Me.GetMlYp3OperationCompleted Is Nothing) Then
                Me.GetMlYp3OperationCompleted = AddressOf Me.OnGetMlYp3OperationCompleted
            End If
            Me.InvokeAsync("GetMlYp3", New Object() {V_Yp_Code}, Me.GetMlYp3OperationCompleted, userState)
        End Sub
        
        Private Sub OnGetMlYp3OperationCompleted(ByVal arg As Object)
            If (Not (Me.GetMlYp3CompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent GetMlYp3Completed(Me, New GetMlYp3CompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/GetMlJbYpJx", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function GetMlJbYpJx(ByVal V_Yp_Code As String) As System.Data.DataSet
            Dim results() As Object = Me.Invoke("GetMlJbYpJx", New Object() {V_Yp_Code})
            Return CType(results(0),System.Data.DataSet)
        End Function
        
        '''<remarks/>
        Public Overloads Sub GetMlJbYpJxAsync(ByVal V_Yp_Code As String)
            Me.GetMlJbYpJxAsync(V_Yp_Code, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub GetMlJbYpJxAsync(ByVal V_Yp_Code As String, ByVal userState As Object)
            If (Me.GetMlJbYpJxOperationCompleted Is Nothing) Then
                Me.GetMlJbYpJxOperationCompleted = AddressOf Me.OnGetMlJbYpJxOperationCompleted
            End If
            Me.InvokeAsync("GetMlJbYpJx", New Object() {V_Yp_Code}, Me.GetMlJbYpJxOperationCompleted, userState)
        End Sub
        
        Private Sub OnGetMlJbYpJxOperationCompleted(ByVal arg As Object)
            If (Not (Me.GetMlJbYpJxCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent GetMlJbYpJxCompleted(Me, New GetMlJbYpJxCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/UpDate_Jblb", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function UpDate_Jblb(ByVal V_Jblb_Code As String, ByVal V_Jblb_Name As String, ByVal V_Jblb_Jc As String, ByVal V_Jblb_Memo As String) As String
            Dim results() As Object = Me.Invoke("UpDate_Jblb", New Object() {V_Jblb_Code, V_Jblb_Name, V_Jblb_Jc, V_Jblb_Memo})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub UpDate_JblbAsync(ByVal V_Jblb_Code As String, ByVal V_Jblb_Name As String, ByVal V_Jblb_Jc As String, ByVal V_Jblb_Memo As String)
            Me.UpDate_JblbAsync(V_Jblb_Code, V_Jblb_Name, V_Jblb_Jc, V_Jblb_Memo, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub UpDate_JblbAsync(ByVal V_Jblb_Code As String, ByVal V_Jblb_Name As String, ByVal V_Jblb_Jc As String, ByVal V_Jblb_Memo As String, ByVal userState As Object)
            If (Me.UpDate_JblbOperationCompleted Is Nothing) Then
                Me.UpDate_JblbOperationCompleted = AddressOf Me.OnUpDate_JblbOperationCompleted
            End If
            Me.InvokeAsync("UpDate_Jblb", New Object() {V_Jblb_Code, V_Jblb_Name, V_Jblb_Jc, V_Jblb_Memo}, Me.UpDate_JblbOperationCompleted, userState)
        End Sub
        
        Private Sub OnUpDate_JblbOperationCompleted(ByVal arg As Object)
            If (Not (Me.UpDate_JblbCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent UpDate_JblbCompleted(Me, New UpDate_JblbCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/UpDate_Jb", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function UpDate_Jb(ByVal V_Jblb_Code As String, ByVal V_Jb_Code As String, ByVal V_Jb_Name As String, ByVal V_Jb_Jc As String, ByVal V_Jb_Memo As String) As String
            Dim results() As Object = Me.Invoke("UpDate_Jb", New Object() {V_Jblb_Code, V_Jb_Code, V_Jb_Name, V_Jb_Jc, V_Jb_Memo})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub UpDate_JbAsync(ByVal V_Jblb_Code As String, ByVal V_Jb_Code As String, ByVal V_Jb_Name As String, ByVal V_Jb_Jc As String, ByVal V_Jb_Memo As String)
            Me.UpDate_JbAsync(V_Jblb_Code, V_Jb_Code, V_Jb_Name, V_Jb_Jc, V_Jb_Memo, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub UpDate_JbAsync(ByVal V_Jblb_Code As String, ByVal V_Jb_Code As String, ByVal V_Jb_Name As String, ByVal V_Jb_Jc As String, ByVal V_Jb_Memo As String, ByVal userState As Object)
            If (Me.UpDate_JbOperationCompleted Is Nothing) Then
                Me.UpDate_JbOperationCompleted = AddressOf Me.OnUpDate_JbOperationCompleted
            End If
            Me.InvokeAsync("UpDate_Jb", New Object() {V_Jblb_Code, V_Jb_Code, V_Jb_Name, V_Jb_Jc, V_Jb_Memo}, Me.UpDate_JbOperationCompleted, userState)
        End Sub
        
        Private Sub OnUpDate_JbOperationCompleted(ByVal arg As Object)
            If (Not (Me.UpDate_JbCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent UpDate_JbCompleted(Me, New UpDate_JbCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/UpDate_Xmlb", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function UpDate_Xmlb(ByVal V_Xmlb_Code As String, ByVal V_Xmlb_Name As String, ByVal V_Xmlb_Jc As String, ByVal V_Xmlb_Memo As String) As String
            Dim results() As Object = Me.Invoke("UpDate_Xmlb", New Object() {V_Xmlb_Code, V_Xmlb_Name, V_Xmlb_Jc, V_Xmlb_Memo})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub UpDate_XmlbAsync(ByVal V_Xmlb_Code As String, ByVal V_Xmlb_Name As String, ByVal V_Xmlb_Jc As String, ByVal V_Xmlb_Memo As String)
            Me.UpDate_XmlbAsync(V_Xmlb_Code, V_Xmlb_Name, V_Xmlb_Jc, V_Xmlb_Memo, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub UpDate_XmlbAsync(ByVal V_Xmlb_Code As String, ByVal V_Xmlb_Name As String, ByVal V_Xmlb_Jc As String, ByVal V_Xmlb_Memo As String, ByVal userState As Object)
            If (Me.UpDate_XmlbOperationCompleted Is Nothing) Then
                Me.UpDate_XmlbOperationCompleted = AddressOf Me.OnUpDate_XmlbOperationCompleted
            End If
            Me.InvokeAsync("UpDate_Xmlb", New Object() {V_Xmlb_Code, V_Xmlb_Name, V_Xmlb_Jc, V_Xmlb_Memo}, Me.UpDate_XmlbOperationCompleted, userState)
        End Sub
        
        Private Sub OnUpDate_XmlbOperationCompleted(ByVal arg As Object)
            If (Not (Me.UpDate_XmlbCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent UpDate_XmlbCompleted(Me, New UpDate_XmlbCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/UpDate_Xm", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function UpDate_Xm(ByVal V_Xmlb_Code As String, ByVal V_Xm_Code As String, ByVal V_Xm_Name As String, ByVal V_Xm_Jc As String, ByVal V_Xm_Dw As String, ByVal V_Xm_Memo As String) As String
            Dim results() As Object = Me.Invoke("UpDate_Xm", New Object() {V_Xmlb_Code, V_Xm_Code, V_Xm_Name, V_Xm_Jc, V_Xm_Dw, V_Xm_Memo})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub UpDate_XmAsync(ByVal V_Xmlb_Code As String, ByVal V_Xm_Code As String, ByVal V_Xm_Name As String, ByVal V_Xm_Jc As String, ByVal V_Xm_Dw As String, ByVal V_Xm_Memo As String)
            Me.UpDate_XmAsync(V_Xmlb_Code, V_Xm_Code, V_Xm_Name, V_Xm_Jc, V_Xm_Dw, V_Xm_Memo, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub UpDate_XmAsync(ByVal V_Xmlb_Code As String, ByVal V_Xm_Code As String, ByVal V_Xm_Name As String, ByVal V_Xm_Jc As String, ByVal V_Xm_Dw As String, ByVal V_Xm_Memo As String, ByVal userState As Object)
            If (Me.UpDate_XmOperationCompleted Is Nothing) Then
                Me.UpDate_XmOperationCompleted = AddressOf Me.OnUpDate_XmOperationCompleted
            End If
            Me.InvokeAsync("UpDate_Xm", New Object() {V_Xmlb_Code, V_Xm_Code, V_Xm_Name, V_Xm_Jc, V_Xm_Dw, V_Xm_Memo}, Me.UpDate_XmOperationCompleted, userState)
        End Sub
        
        Private Sub OnUpDate_XmOperationCompleted(ByVal arg As Object)
            If (Not (Me.UpDate_XmCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent UpDate_XmCompleted(Me, New UpDate_XmCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/UpDate_Yp2", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function UpDate_Yp2(ByVal V_Dl_Code As String, ByVal V_Yp_Code As String, ByVal V_Yp_Name As String, ByVal V_Yp_Jc As String, ByVal V_Yp_Memo As String, ByVal V_Gx_Code As String, ByVal V_IsJb As String) As String
            Dim results() As Object = Me.Invoke("UpDate_Yp2", New Object() {V_Dl_Code, V_Yp_Code, V_Yp_Name, V_Yp_Jc, V_Yp_Memo, V_Gx_Code, V_IsJb})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub UpDate_Yp2Async(ByVal V_Dl_Code As String, ByVal V_Yp_Code As String, ByVal V_Yp_Name As String, ByVal V_Yp_Jc As String, ByVal V_Yp_Memo As String, ByVal V_Gx_Code As String, ByVal V_IsJb As String)
            Me.UpDate_Yp2Async(V_Dl_Code, V_Yp_Code, V_Yp_Name, V_Yp_Jc, V_Yp_Memo, V_Gx_Code, V_IsJb, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub UpDate_Yp2Async(ByVal V_Dl_Code As String, ByVal V_Yp_Code As String, ByVal V_Yp_Name As String, ByVal V_Yp_Jc As String, ByVal V_Yp_Memo As String, ByVal V_Gx_Code As String, ByVal V_IsJb As String, ByVal userState As Object)
            If (Me.UpDate_Yp2OperationCompleted Is Nothing) Then
                Me.UpDate_Yp2OperationCompleted = AddressOf Me.OnUpDate_Yp2OperationCompleted
            End If
            Me.InvokeAsync("UpDate_Yp2", New Object() {V_Dl_Code, V_Yp_Code, V_Yp_Name, V_Yp_Jc, V_Yp_Memo, V_Gx_Code, V_IsJb}, Me.UpDate_Yp2OperationCompleted, userState)
        End Sub
        
        Private Sub OnUpDate_Yp2OperationCompleted(ByVal arg As Object)
            If (Not (Me.UpDate_Yp2CompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent UpDate_Yp2Completed(Me, New UpDate_Yp2CompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/UpDate_Yp3", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function UpDate_Yp3(ByVal V_Yp_Code As String, ByVal V_Mx_Code As String, ByVal V_Jx_Code As String, ByVal V_Mx_Cd As String, ByVal V_Mx_Gg As String, ByVal V_Mx_CgDw As String, ByVal V_Mx_XsDw As String, ByVal V_Mx_Cfbl As Double, ByVal V_Mx_Gyzz As String, ByVal V_Mx_Memo As String) As String
            Dim results() As Object = Me.Invoke("UpDate_Yp3", New Object() {V_Yp_Code, V_Mx_Code, V_Jx_Code, V_Mx_Cd, V_Mx_Gg, V_Mx_CgDw, V_Mx_XsDw, V_Mx_Cfbl, V_Mx_Gyzz, V_Mx_Memo})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub UpDate_Yp3Async(ByVal V_Yp_Code As String, ByVal V_Mx_Code As String, ByVal V_Jx_Code As String, ByVal V_Mx_Cd As String, ByVal V_Mx_Gg As String, ByVal V_Mx_CgDw As String, ByVal V_Mx_XsDw As String, ByVal V_Mx_Cfbl As Double, ByVal V_Mx_Gyzz As String, ByVal V_Mx_Memo As String)
            Me.UpDate_Yp3Async(V_Yp_Code, V_Mx_Code, V_Jx_Code, V_Mx_Cd, V_Mx_Gg, V_Mx_CgDw, V_Mx_XsDw, V_Mx_Cfbl, V_Mx_Gyzz, V_Mx_Memo, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub UpDate_Yp3Async(ByVal V_Yp_Code As String, ByVal V_Mx_Code As String, ByVal V_Jx_Code As String, ByVal V_Mx_Cd As String, ByVal V_Mx_Gg As String, ByVal V_Mx_CgDw As String, ByVal V_Mx_XsDw As String, ByVal V_Mx_Cfbl As Double, ByVal V_Mx_Gyzz As String, ByVal V_Mx_Memo As String, ByVal userState As Object)
            If (Me.UpDate_Yp3OperationCompleted Is Nothing) Then
                Me.UpDate_Yp3OperationCompleted = AddressOf Me.OnUpDate_Yp3OperationCompleted
            End If
            Me.InvokeAsync("UpDate_Yp3", New Object() {V_Yp_Code, V_Mx_Code, V_Jx_Code, V_Mx_Cd, V_Mx_Gg, V_Mx_CgDw, V_Mx_XsDw, V_Mx_Cfbl, V_Mx_Gyzz, V_Mx_Memo}, Me.UpDate_Yp3OperationCompleted, userState)
        End Sub
        
        Private Sub OnUpDate_Yp3OperationCompleted(ByVal arg As Object)
            If (Not (Me.UpDate_Yp3CompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent UpDate_Yp3Completed(Me, New UpDate_Yp3CompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/UpDate_Ypjx", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function UpDate_Ypjx(ByVal V_Jx_Code As String, ByVal V_Jx_Name As String, ByVal V_Jx_Jc As String, ByVal V_Jx_Memo As String) As String
            Dim results() As Object = Me.Invoke("UpDate_Ypjx", New Object() {V_Jx_Code, V_Jx_Name, V_Jx_Jc, V_Jx_Memo})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub UpDate_YpjxAsync(ByVal V_Jx_Code As String, ByVal V_Jx_Name As String, ByVal V_Jx_Jc As String, ByVal V_Jx_Memo As String)
            Me.UpDate_YpjxAsync(V_Jx_Code, V_Jx_Name, V_Jx_Jc, V_Jx_Memo, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub UpDate_YpjxAsync(ByVal V_Jx_Code As String, ByVal V_Jx_Name As String, ByVal V_Jx_Jc As String, ByVal V_Jx_Memo As String, ByVal userState As Object)
            If (Me.UpDate_YpjxOperationCompleted Is Nothing) Then
                Me.UpDate_YpjxOperationCompleted = AddressOf Me.OnUpDate_YpjxOperationCompleted
            End If
            Me.InvokeAsync("UpDate_Ypjx", New Object() {V_Jx_Code, V_Jx_Name, V_Jx_Jc, V_Jx_Memo}, Me.UpDate_YpjxOperationCompleted, userState)
        End Sub
        
        Private Sub OnUpDate_YpjxOperationCompleted(ByVal arg As Object)
            If (Not (Me.UpDate_YpjxCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent UpDate_YpjxCompleted(Me, New UpDate_YpjxCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/UpDate_YpGx", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function UpDate_YpGx(ByVal V_Gx_Code As String, ByVal V_Gx_Name As String, ByVal V_Gx_Jc As String, ByVal V_Gx_Memo As String) As String
            Dim results() As Object = Me.Invoke("UpDate_YpGx", New Object() {V_Gx_Code, V_Gx_Name, V_Gx_Jc, V_Gx_Memo})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub UpDate_YpGxAsync(ByVal V_Gx_Code As String, ByVal V_Gx_Name As String, ByVal V_Gx_Jc As String, ByVal V_Gx_Memo As String)
            Me.UpDate_YpGxAsync(V_Gx_Code, V_Gx_Name, V_Gx_Jc, V_Gx_Memo, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub UpDate_YpGxAsync(ByVal V_Gx_Code As String, ByVal V_Gx_Name As String, ByVal V_Gx_Jc As String, ByVal V_Gx_Memo As String, ByVal userState As Object)
            If (Me.UpDate_YpGxOperationCompleted Is Nothing) Then
                Me.UpDate_YpGxOperationCompleted = AddressOf Me.OnUpDate_YpGxOperationCompleted
            End If
            Me.InvokeAsync("UpDate_YpGx", New Object() {V_Gx_Code, V_Gx_Name, V_Gx_Jc, V_Gx_Memo}, Me.UpDate_YpGxOperationCompleted, userState)
        End Sub
        
        Private Sub OnUpDate_YpGxOperationCompleted(ByVal arg As Object)
            If (Not (Me.UpDate_YpGxCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent UpDate_YpGxCompleted(Me, New UpDate_YpGxCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/Ckeck_Ybzl", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function Ckeck_Ybzl(ByVal V_Xm_Name As String) As Boolean
            Dim results() As Object = Me.Invoke("Ckeck_Ybzl", New Object() {V_Xm_Name})
            Return CType(results(0),Boolean)
        End Function
        
        '''<remarks/>
        Public Overloads Sub Ckeck_YbzlAsync(ByVal V_Xm_Name As String)
            Me.Ckeck_YbzlAsync(V_Xm_Name, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub Ckeck_YbzlAsync(ByVal V_Xm_Name As String, ByVal userState As Object)
            If (Me.Ckeck_YbzlOperationCompleted Is Nothing) Then
                Me.Ckeck_YbzlOperationCompleted = AddressOf Me.OnCkeck_YbzlOperationCompleted
            End If
            Me.InvokeAsync("Ckeck_Ybzl", New Object() {V_Xm_Name}, Me.Ckeck_YbzlOperationCompleted, userState)
        End Sub
        
        Private Sub OnCkeck_YbzlOperationCompleted(ByVal arg As Object)
            If (Not (Me.Ckeck_YbzlCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent Ckeck_YbzlCompleted(Me, New Ckeck_YbzlCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/GetYbZL_Xm", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function GetYbZL_Xm() As System.Data.DataTable
            Dim results() As Object = Me.Invoke("GetYbZL_Xm", New Object(-1) {})
            Return CType(results(0),System.Data.DataTable)
        End Function
        
        '''<remarks/>
        Public Overloads Sub GetYbZL_XmAsync()
            Me.GetYbZL_XmAsync(Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub GetYbZL_XmAsync(ByVal userState As Object)
            If (Me.GetYbZL_XmOperationCompleted Is Nothing) Then
                Me.GetYbZL_XmOperationCompleted = AddressOf Me.OnGetYbZL_XmOperationCompleted
            End If
            Me.InvokeAsync("GetYbZL_Xm", New Object(-1) {}, Me.GetYbZL_XmOperationCompleted, userState)
        End Sub
        
        Private Sub OnGetYbZL_XmOperationCompleted(ByVal arg As Object)
            If (Not (Me.GetYbZL_XmCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent GetYbZL_XmCompleted(Me, New GetYbZL_XmCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/F_GetJkdaRyXx", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function F_GetJkdaRyXx(ByVal V_Tj As String) As System.Data.DataSet
            Dim results() As Object = Me.Invoke("F_GetJkdaRyXx", New Object() {V_Tj})
            Return CType(results(0),System.Data.DataSet)
        End Function
        
        '''<remarks/>
        Public Overloads Sub F_GetJkdaRyXxAsync(ByVal V_Tj As String)
            Me.F_GetJkdaRyXxAsync(V_Tj, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub F_GetJkdaRyXxAsync(ByVal V_Tj As String, ByVal userState As Object)
            If (Me.F_GetJkdaRyXxOperationCompleted Is Nothing) Then
                Me.F_GetJkdaRyXxOperationCompleted = AddressOf Me.OnF_GetJkdaRyXxOperationCompleted
            End If
            Me.InvokeAsync("F_GetJkdaRyXx", New Object() {V_Tj}, Me.F_GetJkdaRyXxOperationCompleted, userState)
        End Sub
        
        Private Sub OnF_GetJkdaRyXxOperationCompleted(ByVal arg As Object)
            If (Not (Me.F_GetJkdaRyXxCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent F_GetJkdaRyXxCompleted(Me, New F_GetJkdaRyXxCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/F_GetJkdaJsr", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function F_GetJkdaJsr(ByVal V_UserName As String, ByVal V_UserPass As String) As System.Data.DataTable
            Dim results() As Object = Me.Invoke("F_GetJkdaJsr", New Object() {V_UserName, V_UserPass})
            Return CType(results(0),System.Data.DataTable)
        End Function
        
        '''<remarks/>
        Public Overloads Sub F_GetJkdaJsrAsync(ByVal V_UserName As String, ByVal V_UserPass As String)
            Me.F_GetJkdaJsrAsync(V_UserName, V_UserPass, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub F_GetJkdaJsrAsync(ByVal V_UserName As String, ByVal V_UserPass As String, ByVal userState As Object)
            If (Me.F_GetJkdaJsrOperationCompleted Is Nothing) Then
                Me.F_GetJkdaJsrOperationCompleted = AddressOf Me.OnF_GetJkdaJsrOperationCompleted
            End If
            Me.InvokeAsync("F_GetJkdaJsr", New Object() {V_UserName, V_UserPass}, Me.F_GetJkdaJsrOperationCompleted, userState)
        End Sub
        
        Private Sub OnF_GetJkdaJsrOperationCompleted(ByVal arg As Object)
            If (Not (Me.F_GetJkdaJsrCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent F_GetJkdaJsrCompleted(Me, New F_GetJkdaJsrCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/F_GetSupJsr", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function F_GetSupJsr(ByVal V_Tj As String) As System.Data.DataTable
            Dim results() As Object = Me.Invoke("F_GetSupJsr", New Object() {V_Tj})
            Return CType(results(0),System.Data.DataTable)
        End Function
        
        '''<remarks/>
        Public Overloads Sub F_GetSupJsrAsync(ByVal V_Tj As String)
            Me.F_GetSupJsrAsync(V_Tj, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub F_GetSupJsrAsync(ByVal V_Tj As String, ByVal userState As Object)
            If (Me.F_GetSupJsrOperationCompleted Is Nothing) Then
                Me.F_GetSupJsrOperationCompleted = AddressOf Me.OnF_GetSupJsrOperationCompleted
            End If
            Me.InvokeAsync("F_GetSupJsr", New Object() {V_Tj}, Me.F_GetSupJsrOperationCompleted, userState)
        End Sub
        
        Private Sub OnF_GetSupJsrOperationCompleted(ByVal arg As Object)
            If (Not (Me.F_GetSupJsrCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent F_GetSupJsrCompleted(Me, New F_GetSupJsrCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/F_InserGxySf", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function F_InserGxySf( _
                    ByVal V_Ry_Code As String,  _
                    ByVal V_Gxy_Sffs As String,  _
                    ByVal V_Gxy_SfDate As Date,  _
                    ByVal V_Gxy_Xy As String,  _
                    ByVal V_Gxy_Tz As String,  _
                    ByVal V_Gxy_Tzzs As String,  _
                    ByVal V_Gxy_Xl As String,  _
                    ByVal V_Gxy_Qt As String,  _
                    ByVal V_Gxy_YwName1 As String,  _
                    ByVal V_Gxy_YfMr1 As String,  _
                    ByVal V_Gxy_YfMrMg1 As String,  _
                    ByVal V_Gxy_YwName2 As String,  _
                    ByVal V_Gxy_YfMr2 As String,  _
                    ByVal V_Gxy_YfMrMg2 As String,  _
                    ByVal V_Gxy_YwName3 As String,  _
                    ByVal V_Gxy_YfMr3 As String,  _
                    ByVal V_Gxy_YfMrMg3 As String,  _
                    ByVal V_Gxy_QtYw As String,  _
                    ByVal V_Gxy_YfMr4 As String,  _
                    ByVal V_Gxy_YfMrMg4 As String,  _
                    ByVal V_Gxy_SfYs As String,  _
                    ByVal V_Gxy_XcSfDate As String,  _
                    ByVal V_Jsr_Code As String,  _
                    ByVal V_Jsr_Name As String,  _
                    ByVal V_Gxy_GaoYa As String,  _
                    ByVal V_Gxy_DiYa As String,  _
                    ByVal V_Gxy_QwTz As String,  _
                    ByVal V_Gxy_QwTzzs As String) As String
            Dim results() As Object = Me.Invoke("F_InserGxySf", New Object() {V_Ry_Code, V_Gxy_Sffs, V_Gxy_SfDate, V_Gxy_Xy, V_Gxy_Tz, V_Gxy_Tzzs, V_Gxy_Xl, V_Gxy_Qt, V_Gxy_YwName1, V_Gxy_YfMr1, V_Gxy_YfMrMg1, V_Gxy_YwName2, V_Gxy_YfMr2, V_Gxy_YfMrMg2, V_Gxy_YwName3, V_Gxy_YfMr3, V_Gxy_YfMrMg3, V_Gxy_QtYw, V_Gxy_YfMr4, V_Gxy_YfMrMg4, V_Gxy_SfYs, V_Gxy_XcSfDate, V_Jsr_Code, V_Jsr_Name, V_Gxy_GaoYa, V_Gxy_DiYa, V_Gxy_QwTz, V_Gxy_QwTzzs})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub F_InserGxySfAsync( _
                    ByVal V_Ry_Code As String,  _
                    ByVal V_Gxy_Sffs As String,  _
                    ByVal V_Gxy_SfDate As Date,  _
                    ByVal V_Gxy_Xy As String,  _
                    ByVal V_Gxy_Tz As String,  _
                    ByVal V_Gxy_Tzzs As String,  _
                    ByVal V_Gxy_Xl As String,  _
                    ByVal V_Gxy_Qt As String,  _
                    ByVal V_Gxy_YwName1 As String,  _
                    ByVal V_Gxy_YfMr1 As String,  _
                    ByVal V_Gxy_YfMrMg1 As String,  _
                    ByVal V_Gxy_YwName2 As String,  _
                    ByVal V_Gxy_YfMr2 As String,  _
                    ByVal V_Gxy_YfMrMg2 As String,  _
                    ByVal V_Gxy_YwName3 As String,  _
                    ByVal V_Gxy_YfMr3 As String,  _
                    ByVal V_Gxy_YfMrMg3 As String,  _
                    ByVal V_Gxy_QtYw As String,  _
                    ByVal V_Gxy_YfMr4 As String,  _
                    ByVal V_Gxy_YfMrMg4 As String,  _
                    ByVal V_Gxy_SfYs As String,  _
                    ByVal V_Gxy_XcSfDate As String,  _
                    ByVal V_Jsr_Code As String,  _
                    ByVal V_Jsr_Name As String,  _
                    ByVal V_Gxy_GaoYa As String,  _
                    ByVal V_Gxy_DiYa As String,  _
                    ByVal V_Gxy_QwTz As String,  _
                    ByVal V_Gxy_QwTzzs As String)
            Me.F_InserGxySfAsync(V_Ry_Code, V_Gxy_Sffs, V_Gxy_SfDate, V_Gxy_Xy, V_Gxy_Tz, V_Gxy_Tzzs, V_Gxy_Xl, V_Gxy_Qt, V_Gxy_YwName1, V_Gxy_YfMr1, V_Gxy_YfMrMg1, V_Gxy_YwName2, V_Gxy_YfMr2, V_Gxy_YfMrMg2, V_Gxy_YwName3, V_Gxy_YfMr3, V_Gxy_YfMrMg3, V_Gxy_QtYw, V_Gxy_YfMr4, V_Gxy_YfMrMg4, V_Gxy_SfYs, V_Gxy_XcSfDate, V_Jsr_Code, V_Jsr_Name, V_Gxy_GaoYa, V_Gxy_DiYa, V_Gxy_QwTz, V_Gxy_QwTzzs, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub F_InserGxySfAsync( _
                    ByVal V_Ry_Code As String,  _
                    ByVal V_Gxy_Sffs As String,  _
                    ByVal V_Gxy_SfDate As Date,  _
                    ByVal V_Gxy_Xy As String,  _
                    ByVal V_Gxy_Tz As String,  _
                    ByVal V_Gxy_Tzzs As String,  _
                    ByVal V_Gxy_Xl As String,  _
                    ByVal V_Gxy_Qt As String,  _
                    ByVal V_Gxy_YwName1 As String,  _
                    ByVal V_Gxy_YfMr1 As String,  _
                    ByVal V_Gxy_YfMrMg1 As String,  _
                    ByVal V_Gxy_YwName2 As String,  _
                    ByVal V_Gxy_YfMr2 As String,  _
                    ByVal V_Gxy_YfMrMg2 As String,  _
                    ByVal V_Gxy_YwName3 As String,  _
                    ByVal V_Gxy_YfMr3 As String,  _
                    ByVal V_Gxy_YfMrMg3 As String,  _
                    ByVal V_Gxy_QtYw As String,  _
                    ByVal V_Gxy_YfMr4 As String,  _
                    ByVal V_Gxy_YfMrMg4 As String,  _
                    ByVal V_Gxy_SfYs As String,  _
                    ByVal V_Gxy_XcSfDate As String,  _
                    ByVal V_Jsr_Code As String,  _
                    ByVal V_Jsr_Name As String,  _
                    ByVal V_Gxy_GaoYa As String,  _
                    ByVal V_Gxy_DiYa As String,  _
                    ByVal V_Gxy_QwTz As String,  _
                    ByVal V_Gxy_QwTzzs As String,  _
                    ByVal userState As Object)
            If (Me.F_InserGxySfOperationCompleted Is Nothing) Then
                Me.F_InserGxySfOperationCompleted = AddressOf Me.OnF_InserGxySfOperationCompleted
            End If
            Me.InvokeAsync("F_InserGxySf", New Object() {V_Ry_Code, V_Gxy_Sffs, V_Gxy_SfDate, V_Gxy_Xy, V_Gxy_Tz, V_Gxy_Tzzs, V_Gxy_Xl, V_Gxy_Qt, V_Gxy_YwName1, V_Gxy_YfMr1, V_Gxy_YfMrMg1, V_Gxy_YwName2, V_Gxy_YfMr2, V_Gxy_YfMrMg2, V_Gxy_YwName3, V_Gxy_YfMr3, V_Gxy_YfMrMg3, V_Gxy_QtYw, V_Gxy_YfMr4, V_Gxy_YfMrMg4, V_Gxy_SfYs, V_Gxy_XcSfDate, V_Jsr_Code, V_Jsr_Name, V_Gxy_GaoYa, V_Gxy_DiYa, V_Gxy_QwTz, V_Gxy_QwTzzs}, Me.F_InserGxySfOperationCompleted, userState)
        End Sub
        
        Private Sub OnF_InserGxySfOperationCompleted(ByVal arg As Object)
            If (Not (Me.F_InserGxySfCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent F_InserGxySfCompleted(Me, New F_InserGxySfCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/F_InserTnbSf", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function F_InserTnbSf( _
                    ByVal V_Ry_Code As String,  _
                    ByVal V_Tnb_Sffs As String,  _
                    ByVal V_Tnb_SfDate As Date,  _
                    ByVal V_Tnb_Xy As String,  _
                    ByVal V_Tnb_Tz As String,  _
                    ByVal V_Tnb_Tzzs As String,  _
                    ByVal V_Tnb_Zbdmbd As String,  _
                    ByVal V_Tnb_Xl As String,  _
                    ByVal V_Tnb_Qt As String,  _
                    ByVal V_Tnb_YwName1 As String,  _
                    ByVal V_Tnb_YfMr1 As String,  _
                    ByVal V_Tnb_YfMrMg1 As String,  _
                    ByVal V_Tnb_YwName2 As String,  _
                    ByVal V_Tnb_YfMr2 As String,  _
                    ByVal V_Tnb_YfMrMg2 As String,  _
                    ByVal V_Tnb_YwName3 As String,  _
                    ByVal V_Tnb_YfMr3 As String,  _
                    ByVal V_Tnb_YfMrMg3 As String,  _
                    ByVal V_Tnb_Yds As String,  _
                    ByVal V_Tnb_SfYs As String,  _
                    ByVal V_Tnb_XcSfDate As String,  _
                    ByVal V_Jsr_Code As String,  _
                    ByVal V_Jsr_Name As String,  _
                    ByVal V_Tnb_QwTz As String,  _
                    ByVal V_Tnb_QwTzzs As String) As String
            Dim results() As Object = Me.Invoke("F_InserTnbSf", New Object() {V_Ry_Code, V_Tnb_Sffs, V_Tnb_SfDate, V_Tnb_Xy, V_Tnb_Tz, V_Tnb_Tzzs, V_Tnb_Zbdmbd, V_Tnb_Xl, V_Tnb_Qt, V_Tnb_YwName1, V_Tnb_YfMr1, V_Tnb_YfMrMg1, V_Tnb_YwName2, V_Tnb_YfMr2, V_Tnb_YfMrMg2, V_Tnb_YwName3, V_Tnb_YfMr3, V_Tnb_YfMrMg3, V_Tnb_Yds, V_Tnb_SfYs, V_Tnb_XcSfDate, V_Jsr_Code, V_Jsr_Name, V_Tnb_QwTz, V_Tnb_QwTzzs})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub F_InserTnbSfAsync( _
                    ByVal V_Ry_Code As String,  _
                    ByVal V_Tnb_Sffs As String,  _
                    ByVal V_Tnb_SfDate As Date,  _
                    ByVal V_Tnb_Xy As String,  _
                    ByVal V_Tnb_Tz As String,  _
                    ByVal V_Tnb_Tzzs As String,  _
                    ByVal V_Tnb_Zbdmbd As String,  _
                    ByVal V_Tnb_Xl As String,  _
                    ByVal V_Tnb_Qt As String,  _
                    ByVal V_Tnb_YwName1 As String,  _
                    ByVal V_Tnb_YfMr1 As String,  _
                    ByVal V_Tnb_YfMrMg1 As String,  _
                    ByVal V_Tnb_YwName2 As String,  _
                    ByVal V_Tnb_YfMr2 As String,  _
                    ByVal V_Tnb_YfMrMg2 As String,  _
                    ByVal V_Tnb_YwName3 As String,  _
                    ByVal V_Tnb_YfMr3 As String,  _
                    ByVal V_Tnb_YfMrMg3 As String,  _
                    ByVal V_Tnb_Yds As String,  _
                    ByVal V_Tnb_SfYs As String,  _
                    ByVal V_Tnb_XcSfDate As String,  _
                    ByVal V_Jsr_Code As String,  _
                    ByVal V_Jsr_Name As String,  _
                    ByVal V_Tnb_QwTz As String,  _
                    ByVal V_Tnb_QwTzzs As String)
            Me.F_InserTnbSfAsync(V_Ry_Code, V_Tnb_Sffs, V_Tnb_SfDate, V_Tnb_Xy, V_Tnb_Tz, V_Tnb_Tzzs, V_Tnb_Zbdmbd, V_Tnb_Xl, V_Tnb_Qt, V_Tnb_YwName1, V_Tnb_YfMr1, V_Tnb_YfMrMg1, V_Tnb_YwName2, V_Tnb_YfMr2, V_Tnb_YfMrMg2, V_Tnb_YwName3, V_Tnb_YfMr3, V_Tnb_YfMrMg3, V_Tnb_Yds, V_Tnb_SfYs, V_Tnb_XcSfDate, V_Jsr_Code, V_Jsr_Name, V_Tnb_QwTz, V_Tnb_QwTzzs, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub F_InserTnbSfAsync( _
                    ByVal V_Ry_Code As String,  _
                    ByVal V_Tnb_Sffs As String,  _
                    ByVal V_Tnb_SfDate As Date,  _
                    ByVal V_Tnb_Xy As String,  _
                    ByVal V_Tnb_Tz As String,  _
                    ByVal V_Tnb_Tzzs As String,  _
                    ByVal V_Tnb_Zbdmbd As String,  _
                    ByVal V_Tnb_Xl As String,  _
                    ByVal V_Tnb_Qt As String,  _
                    ByVal V_Tnb_YwName1 As String,  _
                    ByVal V_Tnb_YfMr1 As String,  _
                    ByVal V_Tnb_YfMrMg1 As String,  _
                    ByVal V_Tnb_YwName2 As String,  _
                    ByVal V_Tnb_YfMr2 As String,  _
                    ByVal V_Tnb_YfMrMg2 As String,  _
                    ByVal V_Tnb_YwName3 As String,  _
                    ByVal V_Tnb_YfMr3 As String,  _
                    ByVal V_Tnb_YfMrMg3 As String,  _
                    ByVal V_Tnb_Yds As String,  _
                    ByVal V_Tnb_SfYs As String,  _
                    ByVal V_Tnb_XcSfDate As String,  _
                    ByVal V_Jsr_Code As String,  _
                    ByVal V_Jsr_Name As String,  _
                    ByVal V_Tnb_QwTz As String,  _
                    ByVal V_Tnb_QwTzzs As String,  _
                    ByVal userState As Object)
            If (Me.F_InserTnbSfOperationCompleted Is Nothing) Then
                Me.F_InserTnbSfOperationCompleted = AddressOf Me.OnF_InserTnbSfOperationCompleted
            End If
            Me.InvokeAsync("F_InserTnbSf", New Object() {V_Ry_Code, V_Tnb_Sffs, V_Tnb_SfDate, V_Tnb_Xy, V_Tnb_Tz, V_Tnb_Tzzs, V_Tnb_Zbdmbd, V_Tnb_Xl, V_Tnb_Qt, V_Tnb_YwName1, V_Tnb_YfMr1, V_Tnb_YfMrMg1, V_Tnb_YwName2, V_Tnb_YfMr2, V_Tnb_YfMrMg2, V_Tnb_YwName3, V_Tnb_YfMr3, V_Tnb_YfMrMg3, V_Tnb_Yds, V_Tnb_SfYs, V_Tnb_XcSfDate, V_Jsr_Code, V_Jsr_Name, V_Tnb_QwTz, V_Tnb_QwTzzs}, Me.F_InserTnbSfOperationCompleted, userState)
        End Sub
        
        Private Sub OnF_InserTnbSfOperationCompleted(ByVal arg As Object)
            If (Not (Me.F_InserTnbSfCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent F_InserTnbSfCompleted(Me, New F_InserTnbSfCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        Public Shadows Sub CancelAsync(ByVal userState As Object)
            MyBase.CancelAsync(userState)
        End Sub
        
        Private Function IsLocalFileSystemWebService(ByVal url As String) As Boolean
            If ((url Is Nothing)  _
                        OrElse (url Is String.Empty)) Then
                Return false
            End If
            Dim wsUri As System.Uri = New System.Uri(url)
            If ((wsUri.Port >= 1024)  _
                        AndAlso (String.Compare(wsUri.Host, "localHost", System.StringComparison.OrdinalIgnoreCase) = 0)) Then
                Return true
            End If
            Return false
        End Function
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub HelloWorldCompletedEventHandler(ByVal sender As Object, ByVal e As HelloWorldCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class HelloWorldCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub GetMlYpGxCompletedEventHandler(ByVal sender As Object, ByVal e As GetMlYpGxCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class GetMlYpGxCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As System.Data.DataSet
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),System.Data.DataSet)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub GetMlYpjxCompletedEventHandler(ByVal sender As Object, ByVal e As GetMlYpjxCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class GetMlYpjxCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As System.Data.DataSet
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),System.Data.DataSet)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub GetMlJb1CompletedEventHandler(ByVal sender As Object, ByVal e As GetMlJb1CompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class GetMlJb1CompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As System.Data.DataSet
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),System.Data.DataSet)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub GetMlJb3CompletedEventHandler(ByVal sender As Object, ByVal e As GetMlJb3CompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class GetMlJb3CompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As System.Data.DataSet
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),System.Data.DataSet)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub GetMlXm1CompletedEventHandler(ByVal sender As Object, ByVal e As GetMlXm1CompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class GetMlXm1CompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As System.Data.DataSet
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),System.Data.DataSet)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub GetMlXm3CompletedEventHandler(ByVal sender As Object, ByVal e As GetMlXm3CompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class GetMlXm3CompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As System.Data.DataSet
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),System.Data.DataSet)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub GetMlYp2CompletedEventHandler(ByVal sender As Object, ByVal e As GetMlYp2CompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class GetMlYp2CompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As System.Data.DataSet
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),System.Data.DataSet)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub GetMlYp3CompletedEventHandler(ByVal sender As Object, ByVal e As GetMlYp3CompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class GetMlYp3CompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As System.Data.DataSet
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),System.Data.DataSet)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub GetMlJbYpJxCompletedEventHandler(ByVal sender As Object, ByVal e As GetMlJbYpJxCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class GetMlJbYpJxCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As System.Data.DataSet
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),System.Data.DataSet)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub UpDate_JblbCompletedEventHandler(ByVal sender As Object, ByVal e As UpDate_JblbCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class UpDate_JblbCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub UpDate_JbCompletedEventHandler(ByVal sender As Object, ByVal e As UpDate_JbCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class UpDate_JbCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub UpDate_XmlbCompletedEventHandler(ByVal sender As Object, ByVal e As UpDate_XmlbCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class UpDate_XmlbCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub UpDate_XmCompletedEventHandler(ByVal sender As Object, ByVal e As UpDate_XmCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class UpDate_XmCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub UpDate_Yp2CompletedEventHandler(ByVal sender As Object, ByVal e As UpDate_Yp2CompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class UpDate_Yp2CompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub UpDate_Yp3CompletedEventHandler(ByVal sender As Object, ByVal e As UpDate_Yp3CompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class UpDate_Yp3CompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub UpDate_YpjxCompletedEventHandler(ByVal sender As Object, ByVal e As UpDate_YpjxCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class UpDate_YpjxCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub UpDate_YpGxCompletedEventHandler(ByVal sender As Object, ByVal e As UpDate_YpGxCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class UpDate_YpGxCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub Ckeck_YbzlCompletedEventHandler(ByVal sender As Object, ByVal e As Ckeck_YbzlCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class Ckeck_YbzlCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As Boolean
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),Boolean)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub GetYbZL_XmCompletedEventHandler(ByVal sender As Object, ByVal e As GetYbZL_XmCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class GetYbZL_XmCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As System.Data.DataTable
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),System.Data.DataTable)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub F_GetJkdaRyXxCompletedEventHandler(ByVal sender As Object, ByVal e As F_GetJkdaRyXxCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class F_GetJkdaRyXxCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As System.Data.DataSet
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),System.Data.DataSet)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub F_GetJkdaJsrCompletedEventHandler(ByVal sender As Object, ByVal e As F_GetJkdaJsrCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class F_GetJkdaJsrCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As System.Data.DataTable
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),System.Data.DataTable)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub F_GetSupJsrCompletedEventHandler(ByVal sender As Object, ByVal e As F_GetSupJsrCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class F_GetSupJsrCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As System.Data.DataTable
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),System.Data.DataTable)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub F_InserGxySfCompletedEventHandler(ByVal sender As Object, ByVal e As F_InserGxySfCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class F_InserGxySfCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub F_InserTnbSfCompletedEventHandler(ByVal sender As Object, ByVal e As F_InserTnbSfCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class F_InserTnbSfCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
End Namespace
