<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:tns="http://tempuri.org/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tm="http://microsoft.com/wsdl/mime/textMatching/" xmlns:s="http://www.w3.org/2001/XMLSchema" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <s:schema elementFormDefault="qualified" targetNamespace="http://tempuri.org/">
      <s:element name="getRy_Zj">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sfzh" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getRy_ZjResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="getRy_ZjResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getMzDataNew">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sfzh" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getMzDataNewResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="getMzDataNewResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getYyJsr">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Name" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Pwd" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getYyJsrResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="getYyJsrResult">
              <s:complexType>
                <s:sequence>
                  <s:any minOccurs="0" maxOccurs="unbounded" namespace="http://www.w3.org/2001/XMLSchema" processContents="lax" />
                  <s:any minOccurs="1" namespace="urn:schemas-microsoft-com:xml-diffgram-v1" processContents="lax" />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="changePwd">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Jsr_Code" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="oldPwd" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="newPwd" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="changePwdResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="changePwdResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getCity">
        <s:complexType />
      </s:element>
      <s:element name="getCityResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="getCityResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getYyName">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Yy_Code" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getYyNameResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="getYyNameResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getSex">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="code" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getSexResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="getSexResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getMZ">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="code" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getMZResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="getMZResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getWHCD">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="code" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getWHCDResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="getWHCDResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getHYZK">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="code" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getHYZKResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="getHYZKResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getZY">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="code" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getZYResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="getZYResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getZJLB">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="code" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getZJLBResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="getZJLBResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getABXX">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="code" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getABXXResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="getABXXResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getRHXX">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="code" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getRHXXResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="getRHXXResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getZyData">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sfzh" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getZyDataResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="getZyDataResult">
              <s:complexType>
                <s:sequence>
                  <s:any minOccurs="0" maxOccurs="unbounded" namespace="http://www.w3.org/2001/XMLSchema" processContents="lax" />
                  <s:any minOccurs="1" namespace="urn:schemas-microsoft-com:xml-diffgram-v1" processContents="lax" />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getZyDataXml">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sfzh" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getZyDataXmlResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="getZyDataXmlResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getZymx">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Post_Code" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getZymxResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="getZymxResult">
              <s:complexType>
                <s:sequence>
                  <s:any minOccurs="0" maxOccurs="unbounded" namespace="http://www.w3.org/2001/XMLSchema" processContents="lax" />
                  <s:any minOccurs="1" namespace="urn:schemas-microsoft-com:xml-diffgram-v1" processContents="lax" />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getZymxXml">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Post_Code" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getZymxXmlResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="getZymxXmlResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getMzData">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sfzh" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getMzDataResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="getMzDataResult">
              <s:complexType>
                <s:sequence>
                  <s:any minOccurs="0" maxOccurs="unbounded" namespace="http://www.w3.org/2001/XMLSchema" processContents="lax" />
                  <s:any minOccurs="1" namespace="urn:schemas-microsoft-com:xml-diffgram-v1" processContents="lax" />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getMzDataXml">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sfzh" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getMzDataXmlResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="getMzDataXmlResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getMzmx">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Post_Code" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getMzmxResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="getMzmxResult">
              <s:complexType>
                <s:sequence>
                  <s:any minOccurs="0" maxOccurs="unbounded" namespace="http://www.w3.org/2001/XMLSchema" processContents="lax" />
                  <s:any minOccurs="1" namespace="urn:schemas-microsoft-com:xml-diffgram-v1" processContents="lax" />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getMzmxXml">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Post_Code" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getMzmxXmlResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="getMzmxXmlResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getMzLsJl_Date">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="cfys" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="date1" type="s:dateTime" />
            <s:element minOccurs="1" maxOccurs="1" name="date2" type="s:dateTime" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getMzLsJl_DateResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="getMzLsJl_DateResult">
              <s:complexType>
                <s:sequence>
                  <s:any minOccurs="0" maxOccurs="unbounded" namespace="http://www.w3.org/2001/XMLSchema" processContents="lax" />
                  <s:any minOccurs="1" namespace="urn:schemas-microsoft-com:xml-diffgram-v1" processContents="lax" />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CheckATR">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sfzh" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="JKKBankNo" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Atr" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CheckATRResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="CheckATRResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getAccount">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sfzh" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="JKKBankNo" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getAccountResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="getAccountResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="addConsumeJsr">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sfzh" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="JKKBankNo" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="money" type="s:decimal" />
            <s:element minOccurs="0" maxOccurs="1" name="yycode" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="SAM_Terminal" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="DeviceCSN" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="YyJsr_Code" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="BankNoYxq" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="addConsumeJsrResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="addConsumeJsrResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CancelConsumeJsr">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sfzh" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="JKKBankNo" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="id" type="s:int" />
            <s:element minOccurs="0" maxOccurs="1" name="yycode" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="SAM_Terminal" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="DeviceCSN" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="YyJsr_Code" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CancelConsumeJsrResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="CancelConsumeJsrResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="addPOSRecharge">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sfzh" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="JKKBankNo" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="money" type="s:decimal" />
            <s:element minOccurs="0" maxOccurs="1" name="yycode" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="bankno" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="SAM_Terminal" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="DeviceCSN" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="POSCode" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="YyJsr_Code" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="addPOSRechargeResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="addPOSRechargeResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="addCashRecharge">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sfzh" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="JKKBankNo" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="money" type="s:decimal" />
            <s:element minOccurs="0" maxOccurs="1" name="yycode" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="SAM_Terminal" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="DeviceCSN" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="YyJsr_Code" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="addCashRechargeResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="addCashRechargeResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="addAppRecharge">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sfzh" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="JKKBankNo" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="money" type="s:decimal" />
            <s:element minOccurs="0" maxOccurs="1" name="yycode" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="bankno" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="SAM_Terminal" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="DeviceCSN" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="POSCode" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="YyJsr_Code" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="addAppRechargeResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="addAppRechargeResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="addAlipayRecharge">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sfzh" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="JKKBankNo" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="money" type="s:decimal" />
            <s:element minOccurs="0" maxOccurs="1" name="yycode" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="bankno" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="SAM_Terminal" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="DeviceCSN" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="POSCode" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="YyJsr_Code" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="addAlipayRechargeResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="addAlipayRechargeResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="AccToCash">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sfzh" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="JKKBankNo" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="money" type="s:decimal" />
            <s:element minOccurs="0" maxOccurs="1" name="yycode" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="SAM_Terminal" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="DeviceCSN" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="YyJsr_Code" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="BankNoYxq" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="AccToCashResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="AccToCashResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="AccToBank">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sfzh" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="JKKBankNo" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="money" type="s:decimal" />
            <s:element minOccurs="0" maxOccurs="1" name="yycode" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="SAM_Terminal" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="DeviceCSN" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="YyJsr_Code" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="BankNoYxq" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="AccToBankResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="AccToBankResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="JKKCzJz">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="jz_date" type="s:dateTime" />
            <s:element minOccurs="0" maxOccurs="1" name="yycode" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="SAM_Terminal" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="DeviceCSN" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="YyJsr_Code" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="JKKCzJzResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="JKKCzJzResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="JKKXFJz">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="jz_date" type="s:dateTime" />
            <s:element minOccurs="0" maxOccurs="1" name="yycode" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="SAM_Terminal" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="DeviceCSN" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="YyJsr_Code" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="JKKXFJzResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="JKKXFJzResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ConsumeList">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sfzh" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="yy_code" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="date1" type="s:dateTime" />
            <s:element minOccurs="1" maxOccurs="1" name="date2" type="s:dateTime" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ConsumeListResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ConsumeListResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getJzTime">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="yycode" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="YyJsr_Code" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getJzTimeResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="getJzTimeResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getJzDataNew">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="jz_date" type="s:dateTime" />
            <s:element minOccurs="0" maxOccurs="1" name="yycode" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="SAM_Terminal" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="DeviceCSN" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="YyJsr_Code" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getJzDataNewResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="getJzDataNewResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getJzData">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="jz_date" type="s:dateTime" />
            <s:element minOccurs="0" maxOccurs="1" name="yycode" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="YyJsr_Code" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getJzDataResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="getJzDataResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CxJzData">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="jz_date1" type="s:dateTime" />
            <s:element minOccurs="1" maxOccurs="1" name="jz_date2" type="s:dateTime" />
            <s:element minOccurs="0" maxOccurs="1" name="yycode" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="YyJsr_Code" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CxJzDataResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="CxJzDataResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CxMxData_Date">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="jz_date1" type="s:dateTime" />
            <s:element minOccurs="1" maxOccurs="1" name="jz_date2" type="s:dateTime" />
            <s:element minOccurs="0" maxOccurs="1" name="yycode" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="YyJsr_Code" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CxMxData_DateResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="CxMxData_DateResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CxMxData_JzCode">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Jz_Code" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="yycode" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CxMxData_JzCodeResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="CxMxData_JzCodeResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="addBk">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sfzh" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="JKKBankNo" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="money" type="s:decimal" />
            <s:element minOccurs="0" maxOccurs="1" name="yycode" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="YyJsr_Code" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="NhLb" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="NhCode" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="addBkResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="addBkResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="RemoveBk">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sfzh" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="JKKBankNo" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="id" type="s:int" />
            <s:element minOccurs="0" maxOccurs="1" name="yycode" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="YyJsr_Code" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="RemoveBkResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="RemoveBkResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CheckBkState">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="id" type="s:int" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CheckBkStateResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="CheckBkStateResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="TempGetAccount">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sfzh" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Ry_Name" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Temp_Code" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="TempGetAccountResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="TempGetAccountResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getTempTellAdd">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sfzh" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Temp_Code" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getTempTellAddResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="getTempTellAddResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="TempNewCard">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Ry_Name" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Ry_Sex" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Ry_Birth" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sfzh" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Ry_Tell" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Ry_Add" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="yycode" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="YyJsr_Code" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="SAM_Terminal" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="DeviceCSN" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Temp_Code" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Original_Code" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="TempNewCardResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="TempNewCardResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="TempAddPOSRecharge">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sfzh" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Ry_Name" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Temp_Code" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="money" type="s:decimal" />
            <s:element minOccurs="0" maxOccurs="1" name="yycode" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="bankno" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="SAM_Terminal" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="DeviceCSN" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="POSCode" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="YyJsr_Code" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="TempAddPOSRechargeResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="TempAddPOSRechargeResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="TempAddCashRecharge">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sfzh" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Ry_Name" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Temp_Code" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="money" type="s:decimal" />
            <s:element minOccurs="0" maxOccurs="1" name="yycode" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="SAM_Terminal" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="DeviceCSN" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="YyJsr_Code" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="TempAddCashRechargeResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="TempAddCashRechargeResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="TempAddConsumeJsr">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sfzh" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Ry_Name" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Temp_Code" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="money" type="s:decimal" />
            <s:element minOccurs="0" maxOccurs="1" name="yycode" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="SAM_Terminal" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="DeviceCSN" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="YyJsr_Code" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="TempAddConsumeJsrResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="TempAddConsumeJsrResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="TempCancelConsumeJsr">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sfzh" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Ry_Name" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Temp_Code" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="id" type="s:int" />
            <s:element minOccurs="0" maxOccurs="1" name="yycode" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="SAM_Terminal" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="DeviceCSN" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="YyJsr_Code" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="TempCancelConsumeJsrResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="TempCancelConsumeJsrResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="TempAccToCash">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sfzh" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Ry_Name" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Temp_Code" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Ry_Tell" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="money" type="s:decimal" />
            <s:element minOccurs="0" maxOccurs="1" name="yycode" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="SAM_Terminal" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="DeviceCSN" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="YyJsr_Code" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="TempAccToCashResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="TempAccToCashResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="TempConsumeList">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Temp_Code" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="yycode" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="date1" type="s:dateTime" />
            <s:element minOccurs="1" maxOccurs="1" name="date2" type="s:dateTime" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="TempConsumeListResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="TempConsumeListResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="TempCancelCard">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sfzh" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Ry_Name" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Temp_Code" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Ry_Tell" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="yycode" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="SAM_Terminal" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="DeviceCSN" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="YyJsr_Code" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="TempCancelCardResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="TempCancelCardResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="TempCheckCancel">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Temp_Code" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="TempCheckCancelResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="TempCheckCancelResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="TempCheckState">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Temp_Code" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="TempCheckStateResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="TempCheckStateResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getRy_Base">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sfzh" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getRy_BaseResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="getRy_BaseResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getRy_GuoMin">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sfzh" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getRy_GuoMinResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="getRy_GuoMinResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getRy_Health">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sfzh" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getRy_HealthResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="getRy_HealthResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getRy_Info">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sfzh" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getRy_InfoResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="getRy_InfoResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getRy_Lxfs">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sfzh" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getRy_LxfsResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="getRy_LxfsResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getRy_MianYi">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sfzh" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getRy_MianYiResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="getRy_MianYiResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="string" nillable="true" type="s:string" />
      <s:element name="DataTable" nillable="true">
        <s:complexType>
          <s:sequence>
            <s:any minOccurs="0" maxOccurs="unbounded" namespace="http://www.w3.org/2001/XMLSchema" processContents="lax" />
            <s:any minOccurs="1" namespace="urn:schemas-microsoft-com:xml-diffgram-v1" processContents="lax" />
          </s:sequence>
        </s:complexType>
      </s:element>
    </s:schema>
  </wsdl:types>
  <wsdl:message name="getRy_ZjSoapIn">
    <wsdl:part name="parameters" element="tns:getRy_Zj" />
  </wsdl:message>
  <wsdl:message name="getRy_ZjSoapOut">
    <wsdl:part name="parameters" element="tns:getRy_ZjResponse" />
  </wsdl:message>
  <wsdl:message name="getMzDataNewSoapIn">
    <wsdl:part name="parameters" element="tns:getMzDataNew" />
  </wsdl:message>
  <wsdl:message name="getMzDataNewSoapOut">
    <wsdl:part name="parameters" element="tns:getMzDataNewResponse" />
  </wsdl:message>
  <wsdl:message name="getYyJsrSoapIn">
    <wsdl:part name="parameters" element="tns:getYyJsr" />
  </wsdl:message>
  <wsdl:message name="getYyJsrSoapOut">
    <wsdl:part name="parameters" element="tns:getYyJsrResponse" />
  </wsdl:message>
  <wsdl:message name="changePwdSoapIn">
    <wsdl:part name="parameters" element="tns:changePwd" />
  </wsdl:message>
  <wsdl:message name="changePwdSoapOut">
    <wsdl:part name="parameters" element="tns:changePwdResponse" />
  </wsdl:message>
  <wsdl:message name="getCitySoapIn">
    <wsdl:part name="parameters" element="tns:getCity" />
  </wsdl:message>
  <wsdl:message name="getCitySoapOut">
    <wsdl:part name="parameters" element="tns:getCityResponse" />
  </wsdl:message>
  <wsdl:message name="getYyNameSoapIn">
    <wsdl:part name="parameters" element="tns:getYyName" />
  </wsdl:message>
  <wsdl:message name="getYyNameSoapOut">
    <wsdl:part name="parameters" element="tns:getYyNameResponse" />
  </wsdl:message>
  <wsdl:message name="getSexSoapIn">
    <wsdl:part name="parameters" element="tns:getSex" />
  </wsdl:message>
  <wsdl:message name="getSexSoapOut">
    <wsdl:part name="parameters" element="tns:getSexResponse" />
  </wsdl:message>
  <wsdl:message name="getMZSoapIn">
    <wsdl:part name="parameters" element="tns:getMZ" />
  </wsdl:message>
  <wsdl:message name="getMZSoapOut">
    <wsdl:part name="parameters" element="tns:getMZResponse" />
  </wsdl:message>
  <wsdl:message name="getWHCDSoapIn">
    <wsdl:part name="parameters" element="tns:getWHCD" />
  </wsdl:message>
  <wsdl:message name="getWHCDSoapOut">
    <wsdl:part name="parameters" element="tns:getWHCDResponse" />
  </wsdl:message>
  <wsdl:message name="getHYZKSoapIn">
    <wsdl:part name="parameters" element="tns:getHYZK" />
  </wsdl:message>
  <wsdl:message name="getHYZKSoapOut">
    <wsdl:part name="parameters" element="tns:getHYZKResponse" />
  </wsdl:message>
  <wsdl:message name="getZYSoapIn">
    <wsdl:part name="parameters" element="tns:getZY" />
  </wsdl:message>
  <wsdl:message name="getZYSoapOut">
    <wsdl:part name="parameters" element="tns:getZYResponse" />
  </wsdl:message>
  <wsdl:message name="getZJLBSoapIn">
    <wsdl:part name="parameters" element="tns:getZJLB" />
  </wsdl:message>
  <wsdl:message name="getZJLBSoapOut">
    <wsdl:part name="parameters" element="tns:getZJLBResponse" />
  </wsdl:message>
  <wsdl:message name="getABXXSoapIn">
    <wsdl:part name="parameters" element="tns:getABXX" />
  </wsdl:message>
  <wsdl:message name="getABXXSoapOut">
    <wsdl:part name="parameters" element="tns:getABXXResponse" />
  </wsdl:message>
  <wsdl:message name="getRHXXSoapIn">
    <wsdl:part name="parameters" element="tns:getRHXX" />
  </wsdl:message>
  <wsdl:message name="getRHXXSoapOut">
    <wsdl:part name="parameters" element="tns:getRHXXResponse" />
  </wsdl:message>
  <wsdl:message name="getZyDataSoapIn">
    <wsdl:part name="parameters" element="tns:getZyData" />
  </wsdl:message>
  <wsdl:message name="getZyDataSoapOut">
    <wsdl:part name="parameters" element="tns:getZyDataResponse" />
  </wsdl:message>
  <wsdl:message name="getZyDataXmlSoapIn">
    <wsdl:part name="parameters" element="tns:getZyDataXml" />
  </wsdl:message>
  <wsdl:message name="getZyDataXmlSoapOut">
    <wsdl:part name="parameters" element="tns:getZyDataXmlResponse" />
  </wsdl:message>
  <wsdl:message name="getZymxSoapIn">
    <wsdl:part name="parameters" element="tns:getZymx" />
  </wsdl:message>
  <wsdl:message name="getZymxSoapOut">
    <wsdl:part name="parameters" element="tns:getZymxResponse" />
  </wsdl:message>
  <wsdl:message name="getZymxXmlSoapIn">
    <wsdl:part name="parameters" element="tns:getZymxXml" />
  </wsdl:message>
  <wsdl:message name="getZymxXmlSoapOut">
    <wsdl:part name="parameters" element="tns:getZymxXmlResponse" />
  </wsdl:message>
  <wsdl:message name="getMzDataSoapIn">
    <wsdl:part name="parameters" element="tns:getMzData" />
  </wsdl:message>
  <wsdl:message name="getMzDataSoapOut">
    <wsdl:part name="parameters" element="tns:getMzDataResponse" />
  </wsdl:message>
  <wsdl:message name="getMzDataXmlSoapIn">
    <wsdl:part name="parameters" element="tns:getMzDataXml" />
  </wsdl:message>
  <wsdl:message name="getMzDataXmlSoapOut">
    <wsdl:part name="parameters" element="tns:getMzDataXmlResponse" />
  </wsdl:message>
  <wsdl:message name="getMzmxSoapIn">
    <wsdl:part name="parameters" element="tns:getMzmx" />
  </wsdl:message>
  <wsdl:message name="getMzmxSoapOut">
    <wsdl:part name="parameters" element="tns:getMzmxResponse" />
  </wsdl:message>
  <wsdl:message name="getMzmxXmlSoapIn">
    <wsdl:part name="parameters" element="tns:getMzmxXml" />
  </wsdl:message>
  <wsdl:message name="getMzmxXmlSoapOut">
    <wsdl:part name="parameters" element="tns:getMzmxXmlResponse" />
  </wsdl:message>
  <wsdl:message name="getMzLsJl_DateSoapIn">
    <wsdl:part name="parameters" element="tns:getMzLsJl_Date" />
  </wsdl:message>
  <wsdl:message name="getMzLsJl_DateSoapOut">
    <wsdl:part name="parameters" element="tns:getMzLsJl_DateResponse" />
  </wsdl:message>
  <wsdl:message name="CheckATRSoapIn">
    <wsdl:part name="parameters" element="tns:CheckATR" />
  </wsdl:message>
  <wsdl:message name="CheckATRSoapOut">
    <wsdl:part name="parameters" element="tns:CheckATRResponse" />
  </wsdl:message>
  <wsdl:message name="getAccountSoapIn">
    <wsdl:part name="parameters" element="tns:getAccount" />
  </wsdl:message>
  <wsdl:message name="getAccountSoapOut">
    <wsdl:part name="parameters" element="tns:getAccountResponse" />
  </wsdl:message>
  <wsdl:message name="addConsumeJsrSoapIn">
    <wsdl:part name="parameters" element="tns:addConsumeJsr" />
  </wsdl:message>
  <wsdl:message name="addConsumeJsrSoapOut">
    <wsdl:part name="parameters" element="tns:addConsumeJsrResponse" />
  </wsdl:message>
  <wsdl:message name="CancelConsumeJsrSoapIn">
    <wsdl:part name="parameters" element="tns:CancelConsumeJsr" />
  </wsdl:message>
  <wsdl:message name="CancelConsumeJsrSoapOut">
    <wsdl:part name="parameters" element="tns:CancelConsumeJsrResponse" />
  </wsdl:message>
  <wsdl:message name="addPOSRechargeSoapIn">
    <wsdl:part name="parameters" element="tns:addPOSRecharge" />
  </wsdl:message>
  <wsdl:message name="addPOSRechargeSoapOut">
    <wsdl:part name="parameters" element="tns:addPOSRechargeResponse" />
  </wsdl:message>
  <wsdl:message name="addCashRechargeSoapIn">
    <wsdl:part name="parameters" element="tns:addCashRecharge" />
  </wsdl:message>
  <wsdl:message name="addCashRechargeSoapOut">
    <wsdl:part name="parameters" element="tns:addCashRechargeResponse" />
  </wsdl:message>
  <wsdl:message name="addAppRechargeSoapIn">
    <wsdl:part name="parameters" element="tns:addAppRecharge" />
  </wsdl:message>
  <wsdl:message name="addAppRechargeSoapOut">
    <wsdl:part name="parameters" element="tns:addAppRechargeResponse" />
  </wsdl:message>
  <wsdl:message name="addAlipayRechargeSoapIn">
    <wsdl:part name="parameters" element="tns:addAlipayRecharge" />
  </wsdl:message>
  <wsdl:message name="addAlipayRechargeSoapOut">
    <wsdl:part name="parameters" element="tns:addAlipayRechargeResponse" />
  </wsdl:message>
  <wsdl:message name="AccToCashSoapIn">
    <wsdl:part name="parameters" element="tns:AccToCash" />
  </wsdl:message>
  <wsdl:message name="AccToCashSoapOut">
    <wsdl:part name="parameters" element="tns:AccToCashResponse" />
  </wsdl:message>
  <wsdl:message name="AccToBankSoapIn">
    <wsdl:part name="parameters" element="tns:AccToBank" />
  </wsdl:message>
  <wsdl:message name="AccToBankSoapOut">
    <wsdl:part name="parameters" element="tns:AccToBankResponse" />
  </wsdl:message>
  <wsdl:message name="JKKCzJzSoapIn">
    <wsdl:part name="parameters" element="tns:JKKCzJz" />
  </wsdl:message>
  <wsdl:message name="JKKCzJzSoapOut">
    <wsdl:part name="parameters" element="tns:JKKCzJzResponse" />
  </wsdl:message>
  <wsdl:message name="JKKXFJzSoapIn">
    <wsdl:part name="parameters" element="tns:JKKXFJz" />
  </wsdl:message>
  <wsdl:message name="JKKXFJzSoapOut">
    <wsdl:part name="parameters" element="tns:JKKXFJzResponse" />
  </wsdl:message>
  <wsdl:message name="ConsumeListSoapIn">
    <wsdl:part name="parameters" element="tns:ConsumeList" />
  </wsdl:message>
  <wsdl:message name="ConsumeListSoapOut">
    <wsdl:part name="parameters" element="tns:ConsumeListResponse" />
  </wsdl:message>
  <wsdl:message name="getJzTimeSoapIn">
    <wsdl:part name="parameters" element="tns:getJzTime" />
  </wsdl:message>
  <wsdl:message name="getJzTimeSoapOut">
    <wsdl:part name="parameters" element="tns:getJzTimeResponse" />
  </wsdl:message>
  <wsdl:message name="getJzDataNewSoapIn">
    <wsdl:part name="parameters" element="tns:getJzDataNew" />
  </wsdl:message>
  <wsdl:message name="getJzDataNewSoapOut">
    <wsdl:part name="parameters" element="tns:getJzDataNewResponse" />
  </wsdl:message>
  <wsdl:message name="getJzDataSoapIn">
    <wsdl:part name="parameters" element="tns:getJzData" />
  </wsdl:message>
  <wsdl:message name="getJzDataSoapOut">
    <wsdl:part name="parameters" element="tns:getJzDataResponse" />
  </wsdl:message>
  <wsdl:message name="CxJzDataSoapIn">
    <wsdl:part name="parameters" element="tns:CxJzData" />
  </wsdl:message>
  <wsdl:message name="CxJzDataSoapOut">
    <wsdl:part name="parameters" element="tns:CxJzDataResponse" />
  </wsdl:message>
  <wsdl:message name="CxMxData_DateSoapIn">
    <wsdl:part name="parameters" element="tns:CxMxData_Date" />
  </wsdl:message>
  <wsdl:message name="CxMxData_DateSoapOut">
    <wsdl:part name="parameters" element="tns:CxMxData_DateResponse" />
  </wsdl:message>
  <wsdl:message name="CxMxData_JzCodeSoapIn">
    <wsdl:part name="parameters" element="tns:CxMxData_JzCode" />
  </wsdl:message>
  <wsdl:message name="CxMxData_JzCodeSoapOut">
    <wsdl:part name="parameters" element="tns:CxMxData_JzCodeResponse" />
  </wsdl:message>
  <wsdl:message name="addBkSoapIn">
    <wsdl:part name="parameters" element="tns:addBk" />
  </wsdl:message>
  <wsdl:message name="addBkSoapOut">
    <wsdl:part name="parameters" element="tns:addBkResponse" />
  </wsdl:message>
  <wsdl:message name="RemoveBkSoapIn">
    <wsdl:part name="parameters" element="tns:RemoveBk" />
  </wsdl:message>
  <wsdl:message name="RemoveBkSoapOut">
    <wsdl:part name="parameters" element="tns:RemoveBkResponse" />
  </wsdl:message>
  <wsdl:message name="CheckBkStateSoapIn">
    <wsdl:part name="parameters" element="tns:CheckBkState" />
  </wsdl:message>
  <wsdl:message name="CheckBkStateSoapOut">
    <wsdl:part name="parameters" element="tns:CheckBkStateResponse" />
  </wsdl:message>
  <wsdl:message name="TempGetAccountSoapIn">
    <wsdl:part name="parameters" element="tns:TempGetAccount" />
  </wsdl:message>
  <wsdl:message name="TempGetAccountSoapOut">
    <wsdl:part name="parameters" element="tns:TempGetAccountResponse" />
  </wsdl:message>
  <wsdl:message name="getTempTellAddSoapIn">
    <wsdl:part name="parameters" element="tns:getTempTellAdd" />
  </wsdl:message>
  <wsdl:message name="getTempTellAddSoapOut">
    <wsdl:part name="parameters" element="tns:getTempTellAddResponse" />
  </wsdl:message>
  <wsdl:message name="TempNewCardSoapIn">
    <wsdl:part name="parameters" element="tns:TempNewCard" />
  </wsdl:message>
  <wsdl:message name="TempNewCardSoapOut">
    <wsdl:part name="parameters" element="tns:TempNewCardResponse" />
  </wsdl:message>
  <wsdl:message name="TempAddPOSRechargeSoapIn">
    <wsdl:part name="parameters" element="tns:TempAddPOSRecharge" />
  </wsdl:message>
  <wsdl:message name="TempAddPOSRechargeSoapOut">
    <wsdl:part name="parameters" element="tns:TempAddPOSRechargeResponse" />
  </wsdl:message>
  <wsdl:message name="TempAddCashRechargeSoapIn">
    <wsdl:part name="parameters" element="tns:TempAddCashRecharge" />
  </wsdl:message>
  <wsdl:message name="TempAddCashRechargeSoapOut">
    <wsdl:part name="parameters" element="tns:TempAddCashRechargeResponse" />
  </wsdl:message>
  <wsdl:message name="TempAddConsumeJsrSoapIn">
    <wsdl:part name="parameters" element="tns:TempAddConsumeJsr" />
  </wsdl:message>
  <wsdl:message name="TempAddConsumeJsrSoapOut">
    <wsdl:part name="parameters" element="tns:TempAddConsumeJsrResponse" />
  </wsdl:message>
  <wsdl:message name="TempCancelConsumeJsrSoapIn">
    <wsdl:part name="parameters" element="tns:TempCancelConsumeJsr" />
  </wsdl:message>
  <wsdl:message name="TempCancelConsumeJsrSoapOut">
    <wsdl:part name="parameters" element="tns:TempCancelConsumeJsrResponse" />
  </wsdl:message>
  <wsdl:message name="TempAccToCashSoapIn">
    <wsdl:part name="parameters" element="tns:TempAccToCash" />
  </wsdl:message>
  <wsdl:message name="TempAccToCashSoapOut">
    <wsdl:part name="parameters" element="tns:TempAccToCashResponse" />
  </wsdl:message>
  <wsdl:message name="TempConsumeListSoapIn">
    <wsdl:part name="parameters" element="tns:TempConsumeList" />
  </wsdl:message>
  <wsdl:message name="TempConsumeListSoapOut">
    <wsdl:part name="parameters" element="tns:TempConsumeListResponse" />
  </wsdl:message>
  <wsdl:message name="TempCancelCardSoapIn">
    <wsdl:part name="parameters" element="tns:TempCancelCard" />
  </wsdl:message>
  <wsdl:message name="TempCancelCardSoapOut">
    <wsdl:part name="parameters" element="tns:TempCancelCardResponse" />
  </wsdl:message>
  <wsdl:message name="TempCheckCancelSoapIn">
    <wsdl:part name="parameters" element="tns:TempCheckCancel" />
  </wsdl:message>
  <wsdl:message name="TempCheckCancelSoapOut">
    <wsdl:part name="parameters" element="tns:TempCheckCancelResponse" />
  </wsdl:message>
  <wsdl:message name="TempCheckStateSoapIn">
    <wsdl:part name="parameters" element="tns:TempCheckState" />
  </wsdl:message>
  <wsdl:message name="TempCheckStateSoapOut">
    <wsdl:part name="parameters" element="tns:TempCheckStateResponse" />
  </wsdl:message>
  <wsdl:message name="getRy_BaseSoapIn">
    <wsdl:part name="parameters" element="tns:getRy_Base" />
  </wsdl:message>
  <wsdl:message name="getRy_BaseSoapOut">
    <wsdl:part name="parameters" element="tns:getRy_BaseResponse" />
  </wsdl:message>
  <wsdl:message name="getRy_GuoMinSoapIn">
    <wsdl:part name="parameters" element="tns:getRy_GuoMin" />
  </wsdl:message>
  <wsdl:message name="getRy_GuoMinSoapOut">
    <wsdl:part name="parameters" element="tns:getRy_GuoMinResponse" />
  </wsdl:message>
  <wsdl:message name="getRy_HealthSoapIn">
    <wsdl:part name="parameters" element="tns:getRy_Health" />
  </wsdl:message>
  <wsdl:message name="getRy_HealthSoapOut">
    <wsdl:part name="parameters" element="tns:getRy_HealthResponse" />
  </wsdl:message>
  <wsdl:message name="getRy_InfoSoapIn">
    <wsdl:part name="parameters" element="tns:getRy_Info" />
  </wsdl:message>
  <wsdl:message name="getRy_InfoSoapOut">
    <wsdl:part name="parameters" element="tns:getRy_InfoResponse" />
  </wsdl:message>
  <wsdl:message name="getRy_LxfsSoapIn">
    <wsdl:part name="parameters" element="tns:getRy_Lxfs" />
  </wsdl:message>
  <wsdl:message name="getRy_LxfsSoapOut">
    <wsdl:part name="parameters" element="tns:getRy_LxfsResponse" />
  </wsdl:message>
  <wsdl:message name="getRy_MianYiSoapIn">
    <wsdl:part name="parameters" element="tns:getRy_MianYi" />
  </wsdl:message>
  <wsdl:message name="getRy_MianYiSoapOut">
    <wsdl:part name="parameters" element="tns:getRy_MianYiResponse" />
  </wsdl:message>
  <wsdl:message name="getRy_ZjHttpGetIn">
    <wsdl:part name="sfzh" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getRy_ZjHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getMzDataNewHttpGetIn">
    <wsdl:part name="sfzh" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getMzDataNewHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getYyJsrHttpGetIn">
    <wsdl:part name="Name" type="s:string" />
    <wsdl:part name="Pwd" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getYyJsrHttpGetOut">
    <wsdl:part name="Body" element="tns:DataTable" />
  </wsdl:message>
  <wsdl:message name="changePwdHttpGetIn">
    <wsdl:part name="Jsr_Code" type="s:string" />
    <wsdl:part name="oldPwd" type="s:string" />
    <wsdl:part name="newPwd" type="s:string" />
  </wsdl:message>
  <wsdl:message name="changePwdHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getCityHttpGetIn" />
  <wsdl:message name="getCityHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getYyNameHttpGetIn">
    <wsdl:part name="Yy_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getYyNameHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getSexHttpGetIn">
    <wsdl:part name="code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getSexHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getMZHttpGetIn">
    <wsdl:part name="code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getMZHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getWHCDHttpGetIn">
    <wsdl:part name="code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getWHCDHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getHYZKHttpGetIn">
    <wsdl:part name="code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getHYZKHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getZYHttpGetIn">
    <wsdl:part name="code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getZYHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getZJLBHttpGetIn">
    <wsdl:part name="code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getZJLBHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getABXXHttpGetIn">
    <wsdl:part name="code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getABXXHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getRHXXHttpGetIn">
    <wsdl:part name="code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getRHXXHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getZyDataHttpGetIn">
    <wsdl:part name="sfzh" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getZyDataHttpGetOut">
    <wsdl:part name="Body" element="tns:DataTable" />
  </wsdl:message>
  <wsdl:message name="getZyDataXmlHttpGetIn">
    <wsdl:part name="sfzh" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getZyDataXmlHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getZymxHttpGetIn">
    <wsdl:part name="Post_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getZymxHttpGetOut">
    <wsdl:part name="Body" element="tns:DataTable" />
  </wsdl:message>
  <wsdl:message name="getZymxXmlHttpGetIn">
    <wsdl:part name="Post_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getZymxXmlHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getMzDataHttpGetIn">
    <wsdl:part name="sfzh" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getMzDataHttpGetOut">
    <wsdl:part name="Body" element="tns:DataTable" />
  </wsdl:message>
  <wsdl:message name="getMzDataXmlHttpGetIn">
    <wsdl:part name="sfzh" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getMzDataXmlHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getMzmxHttpGetIn">
    <wsdl:part name="Post_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getMzmxHttpGetOut">
    <wsdl:part name="Body" element="tns:DataTable" />
  </wsdl:message>
  <wsdl:message name="getMzmxXmlHttpGetIn">
    <wsdl:part name="Post_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getMzmxXmlHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getMzLsJl_DateHttpGetIn">
    <wsdl:part name="cfys" type="s:string" />
    <wsdl:part name="date1" type="s:string" />
    <wsdl:part name="date2" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getMzLsJl_DateHttpGetOut">
    <wsdl:part name="Body" element="tns:DataTable" />
  </wsdl:message>
  <wsdl:message name="CheckATRHttpGetIn">
    <wsdl:part name="sfzh" type="s:string" />
    <wsdl:part name="JKKBankNo" type="s:string" />
    <wsdl:part name="Atr" type="s:string" />
  </wsdl:message>
  <wsdl:message name="CheckATRHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getAccountHttpGetIn">
    <wsdl:part name="sfzh" type="s:string" />
    <wsdl:part name="JKKBankNo" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getAccountHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="addConsumeJsrHttpGetIn">
    <wsdl:part name="sfzh" type="s:string" />
    <wsdl:part name="JKKBankNo" type="s:string" />
    <wsdl:part name="money" type="s:string" />
    <wsdl:part name="yycode" type="s:string" />
    <wsdl:part name="SAM_Terminal" type="s:string" />
    <wsdl:part name="DeviceCSN" type="s:string" />
    <wsdl:part name="YyJsr_Code" type="s:string" />
    <wsdl:part name="BankNoYxq" type="s:string" />
  </wsdl:message>
  <wsdl:message name="addConsumeJsrHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="CancelConsumeJsrHttpGetIn">
    <wsdl:part name="sfzh" type="s:string" />
    <wsdl:part name="JKKBankNo" type="s:string" />
    <wsdl:part name="id" type="s:string" />
    <wsdl:part name="yycode" type="s:string" />
    <wsdl:part name="SAM_Terminal" type="s:string" />
    <wsdl:part name="DeviceCSN" type="s:string" />
    <wsdl:part name="YyJsr_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="CancelConsumeJsrHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="addPOSRechargeHttpGetIn">
    <wsdl:part name="sfzh" type="s:string" />
    <wsdl:part name="JKKBankNo" type="s:string" />
    <wsdl:part name="money" type="s:string" />
    <wsdl:part name="yycode" type="s:string" />
    <wsdl:part name="bankno" type="s:string" />
    <wsdl:part name="SAM_Terminal" type="s:string" />
    <wsdl:part name="DeviceCSN" type="s:string" />
    <wsdl:part name="POSCode" type="s:string" />
    <wsdl:part name="YyJsr_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="addPOSRechargeHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="addCashRechargeHttpGetIn">
    <wsdl:part name="sfzh" type="s:string" />
    <wsdl:part name="JKKBankNo" type="s:string" />
    <wsdl:part name="money" type="s:string" />
    <wsdl:part name="yycode" type="s:string" />
    <wsdl:part name="SAM_Terminal" type="s:string" />
    <wsdl:part name="DeviceCSN" type="s:string" />
    <wsdl:part name="YyJsr_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="addCashRechargeHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="addAppRechargeHttpGetIn">
    <wsdl:part name="sfzh" type="s:string" />
    <wsdl:part name="JKKBankNo" type="s:string" />
    <wsdl:part name="money" type="s:string" />
    <wsdl:part name="yycode" type="s:string" />
    <wsdl:part name="bankno" type="s:string" />
    <wsdl:part name="SAM_Terminal" type="s:string" />
    <wsdl:part name="DeviceCSN" type="s:string" />
    <wsdl:part name="POSCode" type="s:string" />
    <wsdl:part name="YyJsr_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="addAppRechargeHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="addAlipayRechargeHttpGetIn">
    <wsdl:part name="sfzh" type="s:string" />
    <wsdl:part name="JKKBankNo" type="s:string" />
    <wsdl:part name="money" type="s:string" />
    <wsdl:part name="yycode" type="s:string" />
    <wsdl:part name="bankno" type="s:string" />
    <wsdl:part name="SAM_Terminal" type="s:string" />
    <wsdl:part name="DeviceCSN" type="s:string" />
    <wsdl:part name="POSCode" type="s:string" />
    <wsdl:part name="YyJsr_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="addAlipayRechargeHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="AccToCashHttpGetIn">
    <wsdl:part name="sfzh" type="s:string" />
    <wsdl:part name="JKKBankNo" type="s:string" />
    <wsdl:part name="money" type="s:string" />
    <wsdl:part name="yycode" type="s:string" />
    <wsdl:part name="SAM_Terminal" type="s:string" />
    <wsdl:part name="DeviceCSN" type="s:string" />
    <wsdl:part name="YyJsr_Code" type="s:string" />
    <wsdl:part name="BankNoYxq" type="s:string" />
  </wsdl:message>
  <wsdl:message name="AccToCashHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="AccToBankHttpGetIn">
    <wsdl:part name="sfzh" type="s:string" />
    <wsdl:part name="JKKBankNo" type="s:string" />
    <wsdl:part name="money" type="s:string" />
    <wsdl:part name="yycode" type="s:string" />
    <wsdl:part name="SAM_Terminal" type="s:string" />
    <wsdl:part name="DeviceCSN" type="s:string" />
    <wsdl:part name="YyJsr_Code" type="s:string" />
    <wsdl:part name="BankNoYxq" type="s:string" />
  </wsdl:message>
  <wsdl:message name="AccToBankHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="JKKCzJzHttpGetIn">
    <wsdl:part name="jz_date" type="s:string" />
    <wsdl:part name="yycode" type="s:string" />
    <wsdl:part name="SAM_Terminal" type="s:string" />
    <wsdl:part name="DeviceCSN" type="s:string" />
    <wsdl:part name="YyJsr_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="JKKCzJzHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="JKKXFJzHttpGetIn">
    <wsdl:part name="jz_date" type="s:string" />
    <wsdl:part name="yycode" type="s:string" />
    <wsdl:part name="SAM_Terminal" type="s:string" />
    <wsdl:part name="DeviceCSN" type="s:string" />
    <wsdl:part name="YyJsr_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="JKKXFJzHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="ConsumeListHttpGetIn">
    <wsdl:part name="sfzh" type="s:string" />
    <wsdl:part name="yy_code" type="s:string" />
    <wsdl:part name="date1" type="s:string" />
    <wsdl:part name="date2" type="s:string" />
  </wsdl:message>
  <wsdl:message name="ConsumeListHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getJzTimeHttpGetIn">
    <wsdl:part name="yycode" type="s:string" />
    <wsdl:part name="YyJsr_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getJzTimeHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getJzDataNewHttpGetIn">
    <wsdl:part name="jz_date" type="s:string" />
    <wsdl:part name="yycode" type="s:string" />
    <wsdl:part name="SAM_Terminal" type="s:string" />
    <wsdl:part name="DeviceCSN" type="s:string" />
    <wsdl:part name="YyJsr_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getJzDataNewHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getJzDataHttpGetIn">
    <wsdl:part name="jz_date" type="s:string" />
    <wsdl:part name="yycode" type="s:string" />
    <wsdl:part name="YyJsr_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getJzDataHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="CxJzDataHttpGetIn">
    <wsdl:part name="jz_date1" type="s:string" />
    <wsdl:part name="jz_date2" type="s:string" />
    <wsdl:part name="yycode" type="s:string" />
    <wsdl:part name="YyJsr_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="CxJzDataHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="CxMxData_DateHttpGetIn">
    <wsdl:part name="jz_date1" type="s:string" />
    <wsdl:part name="jz_date2" type="s:string" />
    <wsdl:part name="yycode" type="s:string" />
    <wsdl:part name="YyJsr_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="CxMxData_DateHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="CxMxData_JzCodeHttpGetIn">
    <wsdl:part name="Jz_Code" type="s:string" />
    <wsdl:part name="yycode" type="s:string" />
  </wsdl:message>
  <wsdl:message name="CxMxData_JzCodeHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="addBkHttpGetIn">
    <wsdl:part name="sfzh" type="s:string" />
    <wsdl:part name="JKKBankNo" type="s:string" />
    <wsdl:part name="money" type="s:string" />
    <wsdl:part name="yycode" type="s:string" />
    <wsdl:part name="YyJsr_Code" type="s:string" />
    <wsdl:part name="NhLb" type="s:string" />
    <wsdl:part name="NhCode" type="s:string" />
  </wsdl:message>
  <wsdl:message name="addBkHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="RemoveBkHttpGetIn">
    <wsdl:part name="sfzh" type="s:string" />
    <wsdl:part name="JKKBankNo" type="s:string" />
    <wsdl:part name="id" type="s:string" />
    <wsdl:part name="yycode" type="s:string" />
    <wsdl:part name="YyJsr_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="RemoveBkHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="CheckBkStateHttpGetIn">
    <wsdl:part name="id" type="s:string" />
  </wsdl:message>
  <wsdl:message name="CheckBkStateHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="TempGetAccountHttpGetIn">
    <wsdl:part name="sfzh" type="s:string" />
    <wsdl:part name="Ry_Name" type="s:string" />
    <wsdl:part name="Temp_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="TempGetAccountHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getTempTellAddHttpGetIn">
    <wsdl:part name="sfzh" type="s:string" />
    <wsdl:part name="Temp_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getTempTellAddHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="TempNewCardHttpGetIn">
    <wsdl:part name="Ry_Name" type="s:string" />
    <wsdl:part name="Ry_Sex" type="s:string" />
    <wsdl:part name="Ry_Birth" type="s:string" />
    <wsdl:part name="sfzh" type="s:string" />
    <wsdl:part name="Ry_Tell" type="s:string" />
    <wsdl:part name="Ry_Add" type="s:string" />
    <wsdl:part name="yycode" type="s:string" />
    <wsdl:part name="YyJsr_Code" type="s:string" />
    <wsdl:part name="SAM_Terminal" type="s:string" />
    <wsdl:part name="DeviceCSN" type="s:string" />
    <wsdl:part name="Temp_Code" type="s:string" />
    <wsdl:part name="Original_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="TempNewCardHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="TempAddPOSRechargeHttpGetIn">
    <wsdl:part name="sfzh" type="s:string" />
    <wsdl:part name="Ry_Name" type="s:string" />
    <wsdl:part name="Temp_Code" type="s:string" />
    <wsdl:part name="money" type="s:string" />
    <wsdl:part name="yycode" type="s:string" />
    <wsdl:part name="bankno" type="s:string" />
    <wsdl:part name="SAM_Terminal" type="s:string" />
    <wsdl:part name="DeviceCSN" type="s:string" />
    <wsdl:part name="POSCode" type="s:string" />
    <wsdl:part name="YyJsr_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="TempAddPOSRechargeHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="TempAddCashRechargeHttpGetIn">
    <wsdl:part name="sfzh" type="s:string" />
    <wsdl:part name="Ry_Name" type="s:string" />
    <wsdl:part name="Temp_Code" type="s:string" />
    <wsdl:part name="money" type="s:string" />
    <wsdl:part name="yycode" type="s:string" />
    <wsdl:part name="SAM_Terminal" type="s:string" />
    <wsdl:part name="DeviceCSN" type="s:string" />
    <wsdl:part name="YyJsr_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="TempAddCashRechargeHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="TempAddConsumeJsrHttpGetIn">
    <wsdl:part name="sfzh" type="s:string" />
    <wsdl:part name="Ry_Name" type="s:string" />
    <wsdl:part name="Temp_Code" type="s:string" />
    <wsdl:part name="money" type="s:string" />
    <wsdl:part name="yycode" type="s:string" />
    <wsdl:part name="SAM_Terminal" type="s:string" />
    <wsdl:part name="DeviceCSN" type="s:string" />
    <wsdl:part name="YyJsr_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="TempAddConsumeJsrHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="TempCancelConsumeJsrHttpGetIn">
    <wsdl:part name="sfzh" type="s:string" />
    <wsdl:part name="Ry_Name" type="s:string" />
    <wsdl:part name="Temp_Code" type="s:string" />
    <wsdl:part name="id" type="s:string" />
    <wsdl:part name="yycode" type="s:string" />
    <wsdl:part name="SAM_Terminal" type="s:string" />
    <wsdl:part name="DeviceCSN" type="s:string" />
    <wsdl:part name="YyJsr_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="TempCancelConsumeJsrHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="TempAccToCashHttpGetIn">
    <wsdl:part name="sfzh" type="s:string" />
    <wsdl:part name="Ry_Name" type="s:string" />
    <wsdl:part name="Temp_Code" type="s:string" />
    <wsdl:part name="Ry_Tell" type="s:string" />
    <wsdl:part name="money" type="s:string" />
    <wsdl:part name="yycode" type="s:string" />
    <wsdl:part name="SAM_Terminal" type="s:string" />
    <wsdl:part name="DeviceCSN" type="s:string" />
    <wsdl:part name="YyJsr_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="TempAccToCashHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="TempConsumeListHttpGetIn">
    <wsdl:part name="Temp_Code" type="s:string" />
    <wsdl:part name="yycode" type="s:string" />
    <wsdl:part name="date1" type="s:string" />
    <wsdl:part name="date2" type="s:string" />
  </wsdl:message>
  <wsdl:message name="TempConsumeListHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="TempCancelCardHttpGetIn">
    <wsdl:part name="sfzh" type="s:string" />
    <wsdl:part name="Ry_Name" type="s:string" />
    <wsdl:part name="Temp_Code" type="s:string" />
    <wsdl:part name="Ry_Tell" type="s:string" />
    <wsdl:part name="yycode" type="s:string" />
    <wsdl:part name="SAM_Terminal" type="s:string" />
    <wsdl:part name="DeviceCSN" type="s:string" />
    <wsdl:part name="YyJsr_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="TempCancelCardHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="TempCheckCancelHttpGetIn">
    <wsdl:part name="Temp_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="TempCheckCancelHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="TempCheckStateHttpGetIn">
    <wsdl:part name="Temp_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="TempCheckStateHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getRy_BaseHttpGetIn">
    <wsdl:part name="sfzh" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getRy_BaseHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getRy_GuoMinHttpGetIn">
    <wsdl:part name="sfzh" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getRy_GuoMinHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getRy_HealthHttpGetIn">
    <wsdl:part name="sfzh" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getRy_HealthHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getRy_InfoHttpGetIn">
    <wsdl:part name="sfzh" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getRy_InfoHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getRy_LxfsHttpGetIn">
    <wsdl:part name="sfzh" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getRy_LxfsHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getRy_MianYiHttpGetIn">
    <wsdl:part name="sfzh" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getRy_MianYiHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getRy_ZjHttpPostIn">
    <wsdl:part name="sfzh" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getRy_ZjHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getMzDataNewHttpPostIn">
    <wsdl:part name="sfzh" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getMzDataNewHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getYyJsrHttpPostIn">
    <wsdl:part name="Name" type="s:string" />
    <wsdl:part name="Pwd" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getYyJsrHttpPostOut">
    <wsdl:part name="Body" element="tns:DataTable" />
  </wsdl:message>
  <wsdl:message name="changePwdHttpPostIn">
    <wsdl:part name="Jsr_Code" type="s:string" />
    <wsdl:part name="oldPwd" type="s:string" />
    <wsdl:part name="newPwd" type="s:string" />
  </wsdl:message>
  <wsdl:message name="changePwdHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getCityHttpPostIn" />
  <wsdl:message name="getCityHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getYyNameHttpPostIn">
    <wsdl:part name="Yy_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getYyNameHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getSexHttpPostIn">
    <wsdl:part name="code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getSexHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getMZHttpPostIn">
    <wsdl:part name="code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getMZHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getWHCDHttpPostIn">
    <wsdl:part name="code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getWHCDHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getHYZKHttpPostIn">
    <wsdl:part name="code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getHYZKHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getZYHttpPostIn">
    <wsdl:part name="code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getZYHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getZJLBHttpPostIn">
    <wsdl:part name="code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getZJLBHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getABXXHttpPostIn">
    <wsdl:part name="code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getABXXHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getRHXXHttpPostIn">
    <wsdl:part name="code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getRHXXHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getZyDataHttpPostIn">
    <wsdl:part name="sfzh" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getZyDataHttpPostOut">
    <wsdl:part name="Body" element="tns:DataTable" />
  </wsdl:message>
  <wsdl:message name="getZyDataXmlHttpPostIn">
    <wsdl:part name="sfzh" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getZyDataXmlHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getZymxHttpPostIn">
    <wsdl:part name="Post_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getZymxHttpPostOut">
    <wsdl:part name="Body" element="tns:DataTable" />
  </wsdl:message>
  <wsdl:message name="getZymxXmlHttpPostIn">
    <wsdl:part name="Post_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getZymxXmlHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getMzDataHttpPostIn">
    <wsdl:part name="sfzh" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getMzDataHttpPostOut">
    <wsdl:part name="Body" element="tns:DataTable" />
  </wsdl:message>
  <wsdl:message name="getMzDataXmlHttpPostIn">
    <wsdl:part name="sfzh" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getMzDataXmlHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getMzmxHttpPostIn">
    <wsdl:part name="Post_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getMzmxHttpPostOut">
    <wsdl:part name="Body" element="tns:DataTable" />
  </wsdl:message>
  <wsdl:message name="getMzmxXmlHttpPostIn">
    <wsdl:part name="Post_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getMzmxXmlHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getMzLsJl_DateHttpPostIn">
    <wsdl:part name="cfys" type="s:string" />
    <wsdl:part name="date1" type="s:string" />
    <wsdl:part name="date2" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getMzLsJl_DateHttpPostOut">
    <wsdl:part name="Body" element="tns:DataTable" />
  </wsdl:message>
  <wsdl:message name="CheckATRHttpPostIn">
    <wsdl:part name="sfzh" type="s:string" />
    <wsdl:part name="JKKBankNo" type="s:string" />
    <wsdl:part name="Atr" type="s:string" />
  </wsdl:message>
  <wsdl:message name="CheckATRHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getAccountHttpPostIn">
    <wsdl:part name="sfzh" type="s:string" />
    <wsdl:part name="JKKBankNo" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getAccountHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="addConsumeJsrHttpPostIn">
    <wsdl:part name="sfzh" type="s:string" />
    <wsdl:part name="JKKBankNo" type="s:string" />
    <wsdl:part name="money" type="s:string" />
    <wsdl:part name="yycode" type="s:string" />
    <wsdl:part name="SAM_Terminal" type="s:string" />
    <wsdl:part name="DeviceCSN" type="s:string" />
    <wsdl:part name="YyJsr_Code" type="s:string" />
    <wsdl:part name="BankNoYxq" type="s:string" />
  </wsdl:message>
  <wsdl:message name="addConsumeJsrHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="CancelConsumeJsrHttpPostIn">
    <wsdl:part name="sfzh" type="s:string" />
    <wsdl:part name="JKKBankNo" type="s:string" />
    <wsdl:part name="id" type="s:string" />
    <wsdl:part name="yycode" type="s:string" />
    <wsdl:part name="SAM_Terminal" type="s:string" />
    <wsdl:part name="DeviceCSN" type="s:string" />
    <wsdl:part name="YyJsr_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="CancelConsumeJsrHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="addPOSRechargeHttpPostIn">
    <wsdl:part name="sfzh" type="s:string" />
    <wsdl:part name="JKKBankNo" type="s:string" />
    <wsdl:part name="money" type="s:string" />
    <wsdl:part name="yycode" type="s:string" />
    <wsdl:part name="bankno" type="s:string" />
    <wsdl:part name="SAM_Terminal" type="s:string" />
    <wsdl:part name="DeviceCSN" type="s:string" />
    <wsdl:part name="POSCode" type="s:string" />
    <wsdl:part name="YyJsr_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="addPOSRechargeHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="addCashRechargeHttpPostIn">
    <wsdl:part name="sfzh" type="s:string" />
    <wsdl:part name="JKKBankNo" type="s:string" />
    <wsdl:part name="money" type="s:string" />
    <wsdl:part name="yycode" type="s:string" />
    <wsdl:part name="SAM_Terminal" type="s:string" />
    <wsdl:part name="DeviceCSN" type="s:string" />
    <wsdl:part name="YyJsr_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="addCashRechargeHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="addAppRechargeHttpPostIn">
    <wsdl:part name="sfzh" type="s:string" />
    <wsdl:part name="JKKBankNo" type="s:string" />
    <wsdl:part name="money" type="s:string" />
    <wsdl:part name="yycode" type="s:string" />
    <wsdl:part name="bankno" type="s:string" />
    <wsdl:part name="SAM_Terminal" type="s:string" />
    <wsdl:part name="DeviceCSN" type="s:string" />
    <wsdl:part name="POSCode" type="s:string" />
    <wsdl:part name="YyJsr_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="addAppRechargeHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="addAlipayRechargeHttpPostIn">
    <wsdl:part name="sfzh" type="s:string" />
    <wsdl:part name="JKKBankNo" type="s:string" />
    <wsdl:part name="money" type="s:string" />
    <wsdl:part name="yycode" type="s:string" />
    <wsdl:part name="bankno" type="s:string" />
    <wsdl:part name="SAM_Terminal" type="s:string" />
    <wsdl:part name="DeviceCSN" type="s:string" />
    <wsdl:part name="POSCode" type="s:string" />
    <wsdl:part name="YyJsr_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="addAlipayRechargeHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="AccToCashHttpPostIn">
    <wsdl:part name="sfzh" type="s:string" />
    <wsdl:part name="JKKBankNo" type="s:string" />
    <wsdl:part name="money" type="s:string" />
    <wsdl:part name="yycode" type="s:string" />
    <wsdl:part name="SAM_Terminal" type="s:string" />
    <wsdl:part name="DeviceCSN" type="s:string" />
    <wsdl:part name="YyJsr_Code" type="s:string" />
    <wsdl:part name="BankNoYxq" type="s:string" />
  </wsdl:message>
  <wsdl:message name="AccToCashHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="AccToBankHttpPostIn">
    <wsdl:part name="sfzh" type="s:string" />
    <wsdl:part name="JKKBankNo" type="s:string" />
    <wsdl:part name="money" type="s:string" />
    <wsdl:part name="yycode" type="s:string" />
    <wsdl:part name="SAM_Terminal" type="s:string" />
    <wsdl:part name="DeviceCSN" type="s:string" />
    <wsdl:part name="YyJsr_Code" type="s:string" />
    <wsdl:part name="BankNoYxq" type="s:string" />
  </wsdl:message>
  <wsdl:message name="AccToBankHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="JKKCzJzHttpPostIn">
    <wsdl:part name="jz_date" type="s:string" />
    <wsdl:part name="yycode" type="s:string" />
    <wsdl:part name="SAM_Terminal" type="s:string" />
    <wsdl:part name="DeviceCSN" type="s:string" />
    <wsdl:part name="YyJsr_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="JKKCzJzHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="JKKXFJzHttpPostIn">
    <wsdl:part name="jz_date" type="s:string" />
    <wsdl:part name="yycode" type="s:string" />
    <wsdl:part name="SAM_Terminal" type="s:string" />
    <wsdl:part name="DeviceCSN" type="s:string" />
    <wsdl:part name="YyJsr_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="JKKXFJzHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="ConsumeListHttpPostIn">
    <wsdl:part name="sfzh" type="s:string" />
    <wsdl:part name="yy_code" type="s:string" />
    <wsdl:part name="date1" type="s:string" />
    <wsdl:part name="date2" type="s:string" />
  </wsdl:message>
  <wsdl:message name="ConsumeListHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getJzTimeHttpPostIn">
    <wsdl:part name="yycode" type="s:string" />
    <wsdl:part name="YyJsr_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getJzTimeHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getJzDataNewHttpPostIn">
    <wsdl:part name="jz_date" type="s:string" />
    <wsdl:part name="yycode" type="s:string" />
    <wsdl:part name="SAM_Terminal" type="s:string" />
    <wsdl:part name="DeviceCSN" type="s:string" />
    <wsdl:part name="YyJsr_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getJzDataNewHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getJzDataHttpPostIn">
    <wsdl:part name="jz_date" type="s:string" />
    <wsdl:part name="yycode" type="s:string" />
    <wsdl:part name="YyJsr_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getJzDataHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="CxJzDataHttpPostIn">
    <wsdl:part name="jz_date1" type="s:string" />
    <wsdl:part name="jz_date2" type="s:string" />
    <wsdl:part name="yycode" type="s:string" />
    <wsdl:part name="YyJsr_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="CxJzDataHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="CxMxData_DateHttpPostIn">
    <wsdl:part name="jz_date1" type="s:string" />
    <wsdl:part name="jz_date2" type="s:string" />
    <wsdl:part name="yycode" type="s:string" />
    <wsdl:part name="YyJsr_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="CxMxData_DateHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="CxMxData_JzCodeHttpPostIn">
    <wsdl:part name="Jz_Code" type="s:string" />
    <wsdl:part name="yycode" type="s:string" />
  </wsdl:message>
  <wsdl:message name="CxMxData_JzCodeHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="addBkHttpPostIn">
    <wsdl:part name="sfzh" type="s:string" />
    <wsdl:part name="JKKBankNo" type="s:string" />
    <wsdl:part name="money" type="s:string" />
    <wsdl:part name="yycode" type="s:string" />
    <wsdl:part name="YyJsr_Code" type="s:string" />
    <wsdl:part name="NhLb" type="s:string" />
    <wsdl:part name="NhCode" type="s:string" />
  </wsdl:message>
  <wsdl:message name="addBkHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="RemoveBkHttpPostIn">
    <wsdl:part name="sfzh" type="s:string" />
    <wsdl:part name="JKKBankNo" type="s:string" />
    <wsdl:part name="id" type="s:string" />
    <wsdl:part name="yycode" type="s:string" />
    <wsdl:part name="YyJsr_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="RemoveBkHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="CheckBkStateHttpPostIn">
    <wsdl:part name="id" type="s:string" />
  </wsdl:message>
  <wsdl:message name="CheckBkStateHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="TempGetAccountHttpPostIn">
    <wsdl:part name="sfzh" type="s:string" />
    <wsdl:part name="Ry_Name" type="s:string" />
    <wsdl:part name="Temp_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="TempGetAccountHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getTempTellAddHttpPostIn">
    <wsdl:part name="sfzh" type="s:string" />
    <wsdl:part name="Temp_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getTempTellAddHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="TempNewCardHttpPostIn">
    <wsdl:part name="Ry_Name" type="s:string" />
    <wsdl:part name="Ry_Sex" type="s:string" />
    <wsdl:part name="Ry_Birth" type="s:string" />
    <wsdl:part name="sfzh" type="s:string" />
    <wsdl:part name="Ry_Tell" type="s:string" />
    <wsdl:part name="Ry_Add" type="s:string" />
    <wsdl:part name="yycode" type="s:string" />
    <wsdl:part name="YyJsr_Code" type="s:string" />
    <wsdl:part name="SAM_Terminal" type="s:string" />
    <wsdl:part name="DeviceCSN" type="s:string" />
    <wsdl:part name="Temp_Code" type="s:string" />
    <wsdl:part name="Original_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="TempNewCardHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="TempAddPOSRechargeHttpPostIn">
    <wsdl:part name="sfzh" type="s:string" />
    <wsdl:part name="Ry_Name" type="s:string" />
    <wsdl:part name="Temp_Code" type="s:string" />
    <wsdl:part name="money" type="s:string" />
    <wsdl:part name="yycode" type="s:string" />
    <wsdl:part name="bankno" type="s:string" />
    <wsdl:part name="SAM_Terminal" type="s:string" />
    <wsdl:part name="DeviceCSN" type="s:string" />
    <wsdl:part name="POSCode" type="s:string" />
    <wsdl:part name="YyJsr_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="TempAddPOSRechargeHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="TempAddCashRechargeHttpPostIn">
    <wsdl:part name="sfzh" type="s:string" />
    <wsdl:part name="Ry_Name" type="s:string" />
    <wsdl:part name="Temp_Code" type="s:string" />
    <wsdl:part name="money" type="s:string" />
    <wsdl:part name="yycode" type="s:string" />
    <wsdl:part name="SAM_Terminal" type="s:string" />
    <wsdl:part name="DeviceCSN" type="s:string" />
    <wsdl:part name="YyJsr_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="TempAddCashRechargeHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="TempAddConsumeJsrHttpPostIn">
    <wsdl:part name="sfzh" type="s:string" />
    <wsdl:part name="Ry_Name" type="s:string" />
    <wsdl:part name="Temp_Code" type="s:string" />
    <wsdl:part name="money" type="s:string" />
    <wsdl:part name="yycode" type="s:string" />
    <wsdl:part name="SAM_Terminal" type="s:string" />
    <wsdl:part name="DeviceCSN" type="s:string" />
    <wsdl:part name="YyJsr_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="TempAddConsumeJsrHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="TempCancelConsumeJsrHttpPostIn">
    <wsdl:part name="sfzh" type="s:string" />
    <wsdl:part name="Ry_Name" type="s:string" />
    <wsdl:part name="Temp_Code" type="s:string" />
    <wsdl:part name="id" type="s:string" />
    <wsdl:part name="yycode" type="s:string" />
    <wsdl:part name="SAM_Terminal" type="s:string" />
    <wsdl:part name="DeviceCSN" type="s:string" />
    <wsdl:part name="YyJsr_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="TempCancelConsumeJsrHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="TempAccToCashHttpPostIn">
    <wsdl:part name="sfzh" type="s:string" />
    <wsdl:part name="Ry_Name" type="s:string" />
    <wsdl:part name="Temp_Code" type="s:string" />
    <wsdl:part name="Ry_Tell" type="s:string" />
    <wsdl:part name="money" type="s:string" />
    <wsdl:part name="yycode" type="s:string" />
    <wsdl:part name="SAM_Terminal" type="s:string" />
    <wsdl:part name="DeviceCSN" type="s:string" />
    <wsdl:part name="YyJsr_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="TempAccToCashHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="TempConsumeListHttpPostIn">
    <wsdl:part name="Temp_Code" type="s:string" />
    <wsdl:part name="yycode" type="s:string" />
    <wsdl:part name="date1" type="s:string" />
    <wsdl:part name="date2" type="s:string" />
  </wsdl:message>
  <wsdl:message name="TempConsumeListHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="TempCancelCardHttpPostIn">
    <wsdl:part name="sfzh" type="s:string" />
    <wsdl:part name="Ry_Name" type="s:string" />
    <wsdl:part name="Temp_Code" type="s:string" />
    <wsdl:part name="Ry_Tell" type="s:string" />
    <wsdl:part name="yycode" type="s:string" />
    <wsdl:part name="SAM_Terminal" type="s:string" />
    <wsdl:part name="DeviceCSN" type="s:string" />
    <wsdl:part name="YyJsr_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="TempCancelCardHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="TempCheckCancelHttpPostIn">
    <wsdl:part name="Temp_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="TempCheckCancelHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="TempCheckStateHttpPostIn">
    <wsdl:part name="Temp_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="TempCheckStateHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getRy_BaseHttpPostIn">
    <wsdl:part name="sfzh" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getRy_BaseHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getRy_GuoMinHttpPostIn">
    <wsdl:part name="sfzh" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getRy_GuoMinHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getRy_HealthHttpPostIn">
    <wsdl:part name="sfzh" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getRy_HealthHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getRy_InfoHttpPostIn">
    <wsdl:part name="sfzh" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getRy_InfoHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getRy_LxfsHttpPostIn">
    <wsdl:part name="sfzh" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getRy_LxfsHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="getRy_MianYiHttpPostIn">
    <wsdl:part name="sfzh" type="s:string" />
  </wsdl:message>
  <wsdl:message name="getRy_MianYiHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:portType name="WebService1Soap">
    <wsdl:operation name="getRy_Zj">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">人员信息_证件信息</wsdl:documentation>
      <wsdl:input message="tns:getRy_ZjSoapIn" />
      <wsdl:output message="tns:getRy_ZjSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="getMzDataNew">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">人员信息_门诊信息</wsdl:documentation>
      <wsdl:input message="tns:getMzDataNewSoapIn" />
      <wsdl:output message="tns:getMzDataNewSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="getYyJsr">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">登录用户</wsdl:documentation>
      <wsdl:input message="tns:getYyJsrSoapIn" />
      <wsdl:output message="tns:getYyJsrSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="changePwd">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">修改密码</wsdl:documentation>
      <wsdl:input message="tns:changePwdSoapIn" />
      <wsdl:output message="tns:changePwdSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="getCity">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">获取当前省份市</wsdl:documentation>
      <wsdl:input message="tns:getCitySoapIn" />
      <wsdl:output message="tns:getCitySoapOut" />
    </wsdl:operation>
    <wsdl:operation name="getYyName">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">获取医院名称</wsdl:documentation>
      <wsdl:input message="tns:getYyNameSoapIn" />
      <wsdl:output message="tns:getYyNameSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="getSex">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">获取性别</wsdl:documentation>
      <wsdl:input message="tns:getSexSoapIn" />
      <wsdl:output message="tns:getSexSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="getMZ">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">获取民族</wsdl:documentation>
      <wsdl:input message="tns:getMZSoapIn" />
      <wsdl:output message="tns:getMZSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="getWHCD">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">文化程度</wsdl:documentation>
      <wsdl:input message="tns:getWHCDSoapIn" />
      <wsdl:output message="tns:getWHCDSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="getHYZK">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">婚姻状况</wsdl:documentation>
      <wsdl:input message="tns:getHYZKSoapIn" />
      <wsdl:output message="tns:getHYZKSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="getZY">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">职    业</wsdl:documentation>
      <wsdl:input message="tns:getZYSoapIn" />
      <wsdl:output message="tns:getZYSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="getZJLB">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">证件类别</wsdl:documentation>
      <wsdl:input message="tns:getZJLBSoapIn" />
      <wsdl:output message="tns:getZJLBSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="getABXX">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">AB血型</wsdl:documentation>
      <wsdl:input message="tns:getABXXSoapIn" />
      <wsdl:output message="tns:getABXXSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="getRHXX">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">RH血型</wsdl:documentation>
      <wsdl:input message="tns:getRHXXSoapIn" />
      <wsdl:output message="tns:getRHXXSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="getZyData">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">住院主表</wsdl:documentation>
      <wsdl:input message="tns:getZyDataSoapIn" />
      <wsdl:output message="tns:getZyDataSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="getZyDataXml">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">住院主表XML</wsdl:documentation>
      <wsdl:input message="tns:getZyDataXmlSoapIn" />
      <wsdl:output message="tns:getZyDataXmlSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="getZymx">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">住院明细</wsdl:documentation>
      <wsdl:input message="tns:getZymxSoapIn" />
      <wsdl:output message="tns:getZymxSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="getZymxXml">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">住院明细XML</wsdl:documentation>
      <wsdl:input message="tns:getZymxXmlSoapIn" />
      <wsdl:output message="tns:getZymxXmlSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="getMzData">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">门诊主表</wsdl:documentation>
      <wsdl:input message="tns:getMzDataSoapIn" />
      <wsdl:output message="tns:getMzDataSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="getMzDataXml">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">门诊主表XML</wsdl:documentation>
      <wsdl:input message="tns:getMzDataXmlSoapIn" />
      <wsdl:output message="tns:getMzDataXmlSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="getMzmx">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">门诊明细</wsdl:documentation>
      <wsdl:input message="tns:getMzmxSoapIn" />
      <wsdl:output message="tns:getMzmxSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="getMzmxXml">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">门诊明细XML</wsdl:documentation>
      <wsdl:input message="tns:getMzmxXmlSoapIn" />
      <wsdl:output message="tns:getMzmxXmlSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="getMzLsJl_Date">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">门诊历史记录</wsdl:documentation>
      <wsdl:input message="tns:getMzLsJl_DateSoapIn" />
      <wsdl:output message="tns:getMzLsJl_DateSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="CheckATR">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">验证ATR</wsdl:documentation>
      <wsdl:input message="tns:CheckATRSoapIn" />
      <wsdl:output message="tns:CheckATRSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="getAccount">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">获取人员账户余额</wsdl:documentation>
      <wsdl:input message="tns:getAccountSoapIn" />
      <wsdl:output message="tns:getAccountSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="addConsumeJsr">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">增加消费记录,带经手人</wsdl:documentation>
      <wsdl:input message="tns:addConsumeJsrSoapIn" />
      <wsdl:output message="tns:addConsumeJsrSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="CancelConsumeJsr">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">消费撤销，消费出错时使用</wsdl:documentation>
      <wsdl:input message="tns:CancelConsumeJsrSoapIn" />
      <wsdl:output message="tns:CancelConsumeJsrSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="addPOSRecharge">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">增加POS充值记录</wsdl:documentation>
      <wsdl:input message="tns:addPOSRechargeSoapIn" />
      <wsdl:output message="tns:addPOSRechargeSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="addCashRecharge">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">增加现金充值记录</wsdl:documentation>
      <wsdl:input message="tns:addCashRechargeSoapIn" />
      <wsdl:output message="tns:addCashRechargeSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="addAppRecharge">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">增加手机充值记录</wsdl:documentation>
      <wsdl:input message="tns:addAppRechargeSoapIn" />
      <wsdl:output message="tns:addAppRechargeSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="addAlipayRecharge">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">增加支付宝充值记录</wsdl:documentation>
      <wsdl:input message="tns:addAlipayRechargeSoapIn" />
      <wsdl:output message="tns:addAlipayRechargeSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="AccToCash">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">健康账户退回现金</wsdl:documentation>
      <wsdl:input message="tns:AccToCashSoapIn" />
      <wsdl:output message="tns:AccToCashSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="AccToBank">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">健康账户退回银行账户</wsdl:documentation>
      <wsdl:input message="tns:AccToBankSoapIn" />
      <wsdl:output message="tns:AccToBankSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="JKKCzJz">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">健康卡充值结账</wsdl:documentation>
      <wsdl:input message="tns:JKKCzJzSoapIn" />
      <wsdl:output message="tns:JKKCzJzSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="JKKXFJz">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">健康卡消费结账</wsdl:documentation>
      <wsdl:input message="tns:JKKXFJzSoapIn" />
      <wsdl:output message="tns:JKKXFJzSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="ConsumeList">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">健康卡消费清单</wsdl:documentation>
      <wsdl:input message="tns:ConsumeListSoapIn" />
      <wsdl:output message="tns:ConsumeListSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="getJzTime">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">获取结账时间</wsdl:documentation>
      <wsdl:input message="tns:getJzTimeSoapIn" />
      <wsdl:output message="tns:getJzTimeSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="getJzDataNew">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">健康卡按设备充值消费结账数据</wsdl:documentation>
      <wsdl:input message="tns:getJzDataNewSoapIn" />
      <wsdl:output message="tns:getJzDataNewSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="getJzData">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">健康卡充值消费结账数据</wsdl:documentation>
      <wsdl:input message="tns:getJzDataSoapIn" />
      <wsdl:output message="tns:getJzDataSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="CxJzData">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">健康卡充值消费结账历史数据</wsdl:documentation>
      <wsdl:input message="tns:CxJzDataSoapIn" />
      <wsdl:output message="tns:CxJzDataSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="CxMxData_Date">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">健康卡充值消费结账明细数据，根据时间段查询</wsdl:documentation>
      <wsdl:input message="tns:CxMxData_DateSoapIn" />
      <wsdl:output message="tns:CxMxData_DateSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="CxMxData_JzCode">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">健康卡充值消费结账明细数据，根据结账编码查询</wsdl:documentation>
      <wsdl:input message="tns:CxMxData_JzCodeSoapIn" />
      <wsdl:output message="tns:CxMxData_JzCodeSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="addBk">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">拨款接口</wsdl:documentation>
      <wsdl:input message="tns:addBkSoapIn" />
      <wsdl:output message="tns:addBkSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="RemoveBk">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">退款接口</wsdl:documentation>
      <wsdl:input message="tns:RemoveBkSoapIn" />
      <wsdl:output message="tns:RemoveBkSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="CheckBkState">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">查询拨款状态</wsdl:documentation>
      <wsdl:input message="tns:CheckBkStateSoapIn" />
      <wsdl:output message="tns:CheckBkStateSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="TempGetAccount">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">临时卡_获取人员账户余额</wsdl:documentation>
      <wsdl:input message="tns:TempGetAccountSoapIn" />
      <wsdl:output message="tns:TempGetAccountSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="getTempTellAdd">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">临时卡_查询电话和地址</wsdl:documentation>
      <wsdl:input message="tns:getTempTellAddSoapIn" />
      <wsdl:output message="tns:getTempTellAddSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="TempNewCard">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">临时卡_办卡</wsdl:documentation>
      <wsdl:input message="tns:TempNewCardSoapIn" />
      <wsdl:output message="tns:TempNewCardSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="TempAddPOSRecharge">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">临时卡_增加POS充值记录</wsdl:documentation>
      <wsdl:input message="tns:TempAddPOSRechargeSoapIn" />
      <wsdl:output message="tns:TempAddPOSRechargeSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="TempAddCashRecharge">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">临时卡_增加现金充值记录</wsdl:documentation>
      <wsdl:input message="tns:TempAddCashRechargeSoapIn" />
      <wsdl:output message="tns:TempAddCashRechargeSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="TempAddConsumeJsr">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">临时卡_增加消费记录,带经手人</wsdl:documentation>
      <wsdl:input message="tns:TempAddConsumeJsrSoapIn" />
      <wsdl:output message="tns:TempAddConsumeJsrSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="TempCancelConsumeJsr">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">临时卡_消费撤销，消费出错时使用,带经手人</wsdl:documentation>
      <wsdl:input message="tns:TempCancelConsumeJsrSoapIn" />
      <wsdl:output message="tns:TempCancelConsumeJsrSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="TempAccToCash">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">临时卡_账户退回现金</wsdl:documentation>
      <wsdl:input message="tns:TempAccToCashSoapIn" />
      <wsdl:output message="tns:TempAccToCashSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="TempConsumeList">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">临时卡_消费清单</wsdl:documentation>
      <wsdl:input message="tns:TempConsumeListSoapIn" />
      <wsdl:output message="tns:TempConsumeListSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="TempCancelCard">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">临时卡_销卡</wsdl:documentation>
      <wsdl:input message="tns:TempCancelCardSoapIn" />
      <wsdl:output message="tns:TempCancelCardSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="TempCheckCancel">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">验证临时卡是否注销</wsdl:documentation>
      <wsdl:input message="tns:TempCheckCancelSoapIn" />
      <wsdl:output message="tns:TempCheckCancelSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="TempCheckState">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">判断临时卡状态</wsdl:documentation>
      <wsdl:input message="tns:TempCheckStateSoapIn" />
      <wsdl:output message="tns:TempCheckStateSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="getRy_Base">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">人员信息_基础信息</wsdl:documentation>
      <wsdl:input message="tns:getRy_BaseSoapIn" />
      <wsdl:output message="tns:getRy_BaseSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="getRy_GuoMin">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">人员信息_过敏信息</wsdl:documentation>
      <wsdl:input message="tns:getRy_GuoMinSoapIn" />
      <wsdl:output message="tns:getRy_GuoMinSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="getRy_Health">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">人员信息_临床数据</wsdl:documentation>
      <wsdl:input message="tns:getRy_HealthSoapIn" />
      <wsdl:output message="tns:getRy_HealthSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="getRy_Info">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">人员信息_职业婚姻</wsdl:documentation>
      <wsdl:input message="tns:getRy_InfoSoapIn" />
      <wsdl:output message="tns:getRy_InfoSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="getRy_Lxfs">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">人员信息_联系人</wsdl:documentation>
      <wsdl:input message="tns:getRy_LxfsSoapIn" />
      <wsdl:output message="tns:getRy_LxfsSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="getRy_MianYi">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">人员信息_免疫</wsdl:documentation>
      <wsdl:input message="tns:getRy_MianYiSoapIn" />
      <wsdl:output message="tns:getRy_MianYiSoapOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:portType name="WebService1HttpGet">
    <wsdl:operation name="getRy_Zj">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">人员信息_证件信息</wsdl:documentation>
      <wsdl:input message="tns:getRy_ZjHttpGetIn" />
      <wsdl:output message="tns:getRy_ZjHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="getMzDataNew">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">人员信息_门诊信息</wsdl:documentation>
      <wsdl:input message="tns:getMzDataNewHttpGetIn" />
      <wsdl:output message="tns:getMzDataNewHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="getYyJsr">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">登录用户</wsdl:documentation>
      <wsdl:input message="tns:getYyJsrHttpGetIn" />
      <wsdl:output message="tns:getYyJsrHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="changePwd">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">修改密码</wsdl:documentation>
      <wsdl:input message="tns:changePwdHttpGetIn" />
      <wsdl:output message="tns:changePwdHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="getCity">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">获取当前省份市</wsdl:documentation>
      <wsdl:input message="tns:getCityHttpGetIn" />
      <wsdl:output message="tns:getCityHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="getYyName">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">获取医院名称</wsdl:documentation>
      <wsdl:input message="tns:getYyNameHttpGetIn" />
      <wsdl:output message="tns:getYyNameHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="getSex">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">获取性别</wsdl:documentation>
      <wsdl:input message="tns:getSexHttpGetIn" />
      <wsdl:output message="tns:getSexHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="getMZ">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">获取民族</wsdl:documentation>
      <wsdl:input message="tns:getMZHttpGetIn" />
      <wsdl:output message="tns:getMZHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="getWHCD">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">文化程度</wsdl:documentation>
      <wsdl:input message="tns:getWHCDHttpGetIn" />
      <wsdl:output message="tns:getWHCDHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="getHYZK">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">婚姻状况</wsdl:documentation>
      <wsdl:input message="tns:getHYZKHttpGetIn" />
      <wsdl:output message="tns:getHYZKHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="getZY">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">职    业</wsdl:documentation>
      <wsdl:input message="tns:getZYHttpGetIn" />
      <wsdl:output message="tns:getZYHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="getZJLB">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">证件类别</wsdl:documentation>
      <wsdl:input message="tns:getZJLBHttpGetIn" />
      <wsdl:output message="tns:getZJLBHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="getABXX">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">AB血型</wsdl:documentation>
      <wsdl:input message="tns:getABXXHttpGetIn" />
      <wsdl:output message="tns:getABXXHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="getRHXX">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">RH血型</wsdl:documentation>
      <wsdl:input message="tns:getRHXXHttpGetIn" />
      <wsdl:output message="tns:getRHXXHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="getZyData">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">住院主表</wsdl:documentation>
      <wsdl:input message="tns:getZyDataHttpGetIn" />
      <wsdl:output message="tns:getZyDataHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="getZyDataXml">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">住院主表XML</wsdl:documentation>
      <wsdl:input message="tns:getZyDataXmlHttpGetIn" />
      <wsdl:output message="tns:getZyDataXmlHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="getZymx">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">住院明细</wsdl:documentation>
      <wsdl:input message="tns:getZymxHttpGetIn" />
      <wsdl:output message="tns:getZymxHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="getZymxXml">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">住院明细XML</wsdl:documentation>
      <wsdl:input message="tns:getZymxXmlHttpGetIn" />
      <wsdl:output message="tns:getZymxXmlHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="getMzData">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">门诊主表</wsdl:documentation>
      <wsdl:input message="tns:getMzDataHttpGetIn" />
      <wsdl:output message="tns:getMzDataHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="getMzDataXml">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">门诊主表XML</wsdl:documentation>
      <wsdl:input message="tns:getMzDataXmlHttpGetIn" />
      <wsdl:output message="tns:getMzDataXmlHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="getMzmx">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">门诊明细</wsdl:documentation>
      <wsdl:input message="tns:getMzmxHttpGetIn" />
      <wsdl:output message="tns:getMzmxHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="getMzmxXml">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">门诊明细XML</wsdl:documentation>
      <wsdl:input message="tns:getMzmxXmlHttpGetIn" />
      <wsdl:output message="tns:getMzmxXmlHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="getMzLsJl_Date">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">门诊历史记录</wsdl:documentation>
      <wsdl:input message="tns:getMzLsJl_DateHttpGetIn" />
      <wsdl:output message="tns:getMzLsJl_DateHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="CheckATR">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">验证ATR</wsdl:documentation>
      <wsdl:input message="tns:CheckATRHttpGetIn" />
      <wsdl:output message="tns:CheckATRHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="getAccount">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">获取人员账户余额</wsdl:documentation>
      <wsdl:input message="tns:getAccountHttpGetIn" />
      <wsdl:output message="tns:getAccountHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="addConsumeJsr">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">增加消费记录,带经手人</wsdl:documentation>
      <wsdl:input message="tns:addConsumeJsrHttpGetIn" />
      <wsdl:output message="tns:addConsumeJsrHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="CancelConsumeJsr">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">消费撤销，消费出错时使用</wsdl:documentation>
      <wsdl:input message="tns:CancelConsumeJsrHttpGetIn" />
      <wsdl:output message="tns:CancelConsumeJsrHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="addPOSRecharge">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">增加POS充值记录</wsdl:documentation>
      <wsdl:input message="tns:addPOSRechargeHttpGetIn" />
      <wsdl:output message="tns:addPOSRechargeHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="addCashRecharge">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">增加现金充值记录</wsdl:documentation>
      <wsdl:input message="tns:addCashRechargeHttpGetIn" />
      <wsdl:output message="tns:addCashRechargeHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="addAppRecharge">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">增加手机充值记录</wsdl:documentation>
      <wsdl:input message="tns:addAppRechargeHttpGetIn" />
      <wsdl:output message="tns:addAppRechargeHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="addAlipayRecharge">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">增加支付宝充值记录</wsdl:documentation>
      <wsdl:input message="tns:addAlipayRechargeHttpGetIn" />
      <wsdl:output message="tns:addAlipayRechargeHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="AccToCash">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">健康账户退回现金</wsdl:documentation>
      <wsdl:input message="tns:AccToCashHttpGetIn" />
      <wsdl:output message="tns:AccToCashHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="AccToBank">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">健康账户退回银行账户</wsdl:documentation>
      <wsdl:input message="tns:AccToBankHttpGetIn" />
      <wsdl:output message="tns:AccToBankHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="JKKCzJz">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">健康卡充值结账</wsdl:documentation>
      <wsdl:input message="tns:JKKCzJzHttpGetIn" />
      <wsdl:output message="tns:JKKCzJzHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="JKKXFJz">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">健康卡消费结账</wsdl:documentation>
      <wsdl:input message="tns:JKKXFJzHttpGetIn" />
      <wsdl:output message="tns:JKKXFJzHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="ConsumeList">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">健康卡消费清单</wsdl:documentation>
      <wsdl:input message="tns:ConsumeListHttpGetIn" />
      <wsdl:output message="tns:ConsumeListHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="getJzTime">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">获取结账时间</wsdl:documentation>
      <wsdl:input message="tns:getJzTimeHttpGetIn" />
      <wsdl:output message="tns:getJzTimeHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="getJzDataNew">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">健康卡按设备充值消费结账数据</wsdl:documentation>
      <wsdl:input message="tns:getJzDataNewHttpGetIn" />
      <wsdl:output message="tns:getJzDataNewHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="getJzData">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">健康卡充值消费结账数据</wsdl:documentation>
      <wsdl:input message="tns:getJzDataHttpGetIn" />
      <wsdl:output message="tns:getJzDataHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="CxJzData">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">健康卡充值消费结账历史数据</wsdl:documentation>
      <wsdl:input message="tns:CxJzDataHttpGetIn" />
      <wsdl:output message="tns:CxJzDataHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="CxMxData_Date">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">健康卡充值消费结账明细数据，根据时间段查询</wsdl:documentation>
      <wsdl:input message="tns:CxMxData_DateHttpGetIn" />
      <wsdl:output message="tns:CxMxData_DateHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="CxMxData_JzCode">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">健康卡充值消费结账明细数据，根据结账编码查询</wsdl:documentation>
      <wsdl:input message="tns:CxMxData_JzCodeHttpGetIn" />
      <wsdl:output message="tns:CxMxData_JzCodeHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="addBk">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">拨款接口</wsdl:documentation>
      <wsdl:input message="tns:addBkHttpGetIn" />
      <wsdl:output message="tns:addBkHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="RemoveBk">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">退款接口</wsdl:documentation>
      <wsdl:input message="tns:RemoveBkHttpGetIn" />
      <wsdl:output message="tns:RemoveBkHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="CheckBkState">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">查询拨款状态</wsdl:documentation>
      <wsdl:input message="tns:CheckBkStateHttpGetIn" />
      <wsdl:output message="tns:CheckBkStateHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="TempGetAccount">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">临时卡_获取人员账户余额</wsdl:documentation>
      <wsdl:input message="tns:TempGetAccountHttpGetIn" />
      <wsdl:output message="tns:TempGetAccountHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="getTempTellAdd">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">临时卡_查询电话和地址</wsdl:documentation>
      <wsdl:input message="tns:getTempTellAddHttpGetIn" />
      <wsdl:output message="tns:getTempTellAddHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="TempNewCard">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">临时卡_办卡</wsdl:documentation>
      <wsdl:input message="tns:TempNewCardHttpGetIn" />
      <wsdl:output message="tns:TempNewCardHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="TempAddPOSRecharge">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">临时卡_增加POS充值记录</wsdl:documentation>
      <wsdl:input message="tns:TempAddPOSRechargeHttpGetIn" />
      <wsdl:output message="tns:TempAddPOSRechargeHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="TempAddCashRecharge">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">临时卡_增加现金充值记录</wsdl:documentation>
      <wsdl:input message="tns:TempAddCashRechargeHttpGetIn" />
      <wsdl:output message="tns:TempAddCashRechargeHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="TempAddConsumeJsr">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">临时卡_增加消费记录,带经手人</wsdl:documentation>
      <wsdl:input message="tns:TempAddConsumeJsrHttpGetIn" />
      <wsdl:output message="tns:TempAddConsumeJsrHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="TempCancelConsumeJsr">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">临时卡_消费撤销，消费出错时使用,带经手人</wsdl:documentation>
      <wsdl:input message="tns:TempCancelConsumeJsrHttpGetIn" />
      <wsdl:output message="tns:TempCancelConsumeJsrHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="TempAccToCash">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">临时卡_账户退回现金</wsdl:documentation>
      <wsdl:input message="tns:TempAccToCashHttpGetIn" />
      <wsdl:output message="tns:TempAccToCashHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="TempConsumeList">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">临时卡_消费清单</wsdl:documentation>
      <wsdl:input message="tns:TempConsumeListHttpGetIn" />
      <wsdl:output message="tns:TempConsumeListHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="TempCancelCard">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">临时卡_销卡</wsdl:documentation>
      <wsdl:input message="tns:TempCancelCardHttpGetIn" />
      <wsdl:output message="tns:TempCancelCardHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="TempCheckCancel">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">验证临时卡是否注销</wsdl:documentation>
      <wsdl:input message="tns:TempCheckCancelHttpGetIn" />
      <wsdl:output message="tns:TempCheckCancelHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="TempCheckState">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">判断临时卡状态</wsdl:documentation>
      <wsdl:input message="tns:TempCheckStateHttpGetIn" />
      <wsdl:output message="tns:TempCheckStateHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="getRy_Base">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">人员信息_基础信息</wsdl:documentation>
      <wsdl:input message="tns:getRy_BaseHttpGetIn" />
      <wsdl:output message="tns:getRy_BaseHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="getRy_GuoMin">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">人员信息_过敏信息</wsdl:documentation>
      <wsdl:input message="tns:getRy_GuoMinHttpGetIn" />
      <wsdl:output message="tns:getRy_GuoMinHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="getRy_Health">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">人员信息_临床数据</wsdl:documentation>
      <wsdl:input message="tns:getRy_HealthHttpGetIn" />
      <wsdl:output message="tns:getRy_HealthHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="getRy_Info">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">人员信息_职业婚姻</wsdl:documentation>
      <wsdl:input message="tns:getRy_InfoHttpGetIn" />
      <wsdl:output message="tns:getRy_InfoHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="getRy_Lxfs">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">人员信息_联系人</wsdl:documentation>
      <wsdl:input message="tns:getRy_LxfsHttpGetIn" />
      <wsdl:output message="tns:getRy_LxfsHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="getRy_MianYi">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">人员信息_免疫</wsdl:documentation>
      <wsdl:input message="tns:getRy_MianYiHttpGetIn" />
      <wsdl:output message="tns:getRy_MianYiHttpGetOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:portType name="WebService1HttpPost">
    <wsdl:operation name="getRy_Zj">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">人员信息_证件信息</wsdl:documentation>
      <wsdl:input message="tns:getRy_ZjHttpPostIn" />
      <wsdl:output message="tns:getRy_ZjHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="getMzDataNew">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">人员信息_门诊信息</wsdl:documentation>
      <wsdl:input message="tns:getMzDataNewHttpPostIn" />
      <wsdl:output message="tns:getMzDataNewHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="getYyJsr">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">登录用户</wsdl:documentation>
      <wsdl:input message="tns:getYyJsrHttpPostIn" />
      <wsdl:output message="tns:getYyJsrHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="changePwd">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">修改密码</wsdl:documentation>
      <wsdl:input message="tns:changePwdHttpPostIn" />
      <wsdl:output message="tns:changePwdHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="getCity">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">获取当前省份市</wsdl:documentation>
      <wsdl:input message="tns:getCityHttpPostIn" />
      <wsdl:output message="tns:getCityHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="getYyName">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">获取医院名称</wsdl:documentation>
      <wsdl:input message="tns:getYyNameHttpPostIn" />
      <wsdl:output message="tns:getYyNameHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="getSex">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">获取性别</wsdl:documentation>
      <wsdl:input message="tns:getSexHttpPostIn" />
      <wsdl:output message="tns:getSexHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="getMZ">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">获取民族</wsdl:documentation>
      <wsdl:input message="tns:getMZHttpPostIn" />
      <wsdl:output message="tns:getMZHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="getWHCD">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">文化程度</wsdl:documentation>
      <wsdl:input message="tns:getWHCDHttpPostIn" />
      <wsdl:output message="tns:getWHCDHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="getHYZK">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">婚姻状况</wsdl:documentation>
      <wsdl:input message="tns:getHYZKHttpPostIn" />
      <wsdl:output message="tns:getHYZKHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="getZY">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">职    业</wsdl:documentation>
      <wsdl:input message="tns:getZYHttpPostIn" />
      <wsdl:output message="tns:getZYHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="getZJLB">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">证件类别</wsdl:documentation>
      <wsdl:input message="tns:getZJLBHttpPostIn" />
      <wsdl:output message="tns:getZJLBHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="getABXX">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">AB血型</wsdl:documentation>
      <wsdl:input message="tns:getABXXHttpPostIn" />
      <wsdl:output message="tns:getABXXHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="getRHXX">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">RH血型</wsdl:documentation>
      <wsdl:input message="tns:getRHXXHttpPostIn" />
      <wsdl:output message="tns:getRHXXHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="getZyData">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">住院主表</wsdl:documentation>
      <wsdl:input message="tns:getZyDataHttpPostIn" />
      <wsdl:output message="tns:getZyDataHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="getZyDataXml">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">住院主表XML</wsdl:documentation>
      <wsdl:input message="tns:getZyDataXmlHttpPostIn" />
      <wsdl:output message="tns:getZyDataXmlHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="getZymx">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">住院明细</wsdl:documentation>
      <wsdl:input message="tns:getZymxHttpPostIn" />
      <wsdl:output message="tns:getZymxHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="getZymxXml">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">住院明细XML</wsdl:documentation>
      <wsdl:input message="tns:getZymxXmlHttpPostIn" />
      <wsdl:output message="tns:getZymxXmlHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="getMzData">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">门诊主表</wsdl:documentation>
      <wsdl:input message="tns:getMzDataHttpPostIn" />
      <wsdl:output message="tns:getMzDataHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="getMzDataXml">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">门诊主表XML</wsdl:documentation>
      <wsdl:input message="tns:getMzDataXmlHttpPostIn" />
      <wsdl:output message="tns:getMzDataXmlHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="getMzmx">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">门诊明细</wsdl:documentation>
      <wsdl:input message="tns:getMzmxHttpPostIn" />
      <wsdl:output message="tns:getMzmxHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="getMzmxXml">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">门诊明细XML</wsdl:documentation>
      <wsdl:input message="tns:getMzmxXmlHttpPostIn" />
      <wsdl:output message="tns:getMzmxXmlHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="getMzLsJl_Date">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">门诊历史记录</wsdl:documentation>
      <wsdl:input message="tns:getMzLsJl_DateHttpPostIn" />
      <wsdl:output message="tns:getMzLsJl_DateHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="CheckATR">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">验证ATR</wsdl:documentation>
      <wsdl:input message="tns:CheckATRHttpPostIn" />
      <wsdl:output message="tns:CheckATRHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="getAccount">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">获取人员账户余额</wsdl:documentation>
      <wsdl:input message="tns:getAccountHttpPostIn" />
      <wsdl:output message="tns:getAccountHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="addConsumeJsr">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">增加消费记录,带经手人</wsdl:documentation>
      <wsdl:input message="tns:addConsumeJsrHttpPostIn" />
      <wsdl:output message="tns:addConsumeJsrHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="CancelConsumeJsr">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">消费撤销，消费出错时使用</wsdl:documentation>
      <wsdl:input message="tns:CancelConsumeJsrHttpPostIn" />
      <wsdl:output message="tns:CancelConsumeJsrHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="addPOSRecharge">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">增加POS充值记录</wsdl:documentation>
      <wsdl:input message="tns:addPOSRechargeHttpPostIn" />
      <wsdl:output message="tns:addPOSRechargeHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="addCashRecharge">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">增加现金充值记录</wsdl:documentation>
      <wsdl:input message="tns:addCashRechargeHttpPostIn" />
      <wsdl:output message="tns:addCashRechargeHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="addAppRecharge">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">增加手机充值记录</wsdl:documentation>
      <wsdl:input message="tns:addAppRechargeHttpPostIn" />
      <wsdl:output message="tns:addAppRechargeHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="addAlipayRecharge">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">增加支付宝充值记录</wsdl:documentation>
      <wsdl:input message="tns:addAlipayRechargeHttpPostIn" />
      <wsdl:output message="tns:addAlipayRechargeHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="AccToCash">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">健康账户退回现金</wsdl:documentation>
      <wsdl:input message="tns:AccToCashHttpPostIn" />
      <wsdl:output message="tns:AccToCashHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="AccToBank">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">健康账户退回银行账户</wsdl:documentation>
      <wsdl:input message="tns:AccToBankHttpPostIn" />
      <wsdl:output message="tns:AccToBankHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="JKKCzJz">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">健康卡充值结账</wsdl:documentation>
      <wsdl:input message="tns:JKKCzJzHttpPostIn" />
      <wsdl:output message="tns:JKKCzJzHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="JKKXFJz">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">健康卡消费结账</wsdl:documentation>
      <wsdl:input message="tns:JKKXFJzHttpPostIn" />
      <wsdl:output message="tns:JKKXFJzHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="ConsumeList">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">健康卡消费清单</wsdl:documentation>
      <wsdl:input message="tns:ConsumeListHttpPostIn" />
      <wsdl:output message="tns:ConsumeListHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="getJzTime">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">获取结账时间</wsdl:documentation>
      <wsdl:input message="tns:getJzTimeHttpPostIn" />
      <wsdl:output message="tns:getJzTimeHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="getJzDataNew">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">健康卡按设备充值消费结账数据</wsdl:documentation>
      <wsdl:input message="tns:getJzDataNewHttpPostIn" />
      <wsdl:output message="tns:getJzDataNewHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="getJzData">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">健康卡充值消费结账数据</wsdl:documentation>
      <wsdl:input message="tns:getJzDataHttpPostIn" />
      <wsdl:output message="tns:getJzDataHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="CxJzData">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">健康卡充值消费结账历史数据</wsdl:documentation>
      <wsdl:input message="tns:CxJzDataHttpPostIn" />
      <wsdl:output message="tns:CxJzDataHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="CxMxData_Date">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">健康卡充值消费结账明细数据，根据时间段查询</wsdl:documentation>
      <wsdl:input message="tns:CxMxData_DateHttpPostIn" />
      <wsdl:output message="tns:CxMxData_DateHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="CxMxData_JzCode">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">健康卡充值消费结账明细数据，根据结账编码查询</wsdl:documentation>
      <wsdl:input message="tns:CxMxData_JzCodeHttpPostIn" />
      <wsdl:output message="tns:CxMxData_JzCodeHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="addBk">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">拨款接口</wsdl:documentation>
      <wsdl:input message="tns:addBkHttpPostIn" />
      <wsdl:output message="tns:addBkHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="RemoveBk">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">退款接口</wsdl:documentation>
      <wsdl:input message="tns:RemoveBkHttpPostIn" />
      <wsdl:output message="tns:RemoveBkHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="CheckBkState">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">查询拨款状态</wsdl:documentation>
      <wsdl:input message="tns:CheckBkStateHttpPostIn" />
      <wsdl:output message="tns:CheckBkStateHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="TempGetAccount">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">临时卡_获取人员账户余额</wsdl:documentation>
      <wsdl:input message="tns:TempGetAccountHttpPostIn" />
      <wsdl:output message="tns:TempGetAccountHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="getTempTellAdd">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">临时卡_查询电话和地址</wsdl:documentation>
      <wsdl:input message="tns:getTempTellAddHttpPostIn" />
      <wsdl:output message="tns:getTempTellAddHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="TempNewCard">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">临时卡_办卡</wsdl:documentation>
      <wsdl:input message="tns:TempNewCardHttpPostIn" />
      <wsdl:output message="tns:TempNewCardHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="TempAddPOSRecharge">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">临时卡_增加POS充值记录</wsdl:documentation>
      <wsdl:input message="tns:TempAddPOSRechargeHttpPostIn" />
      <wsdl:output message="tns:TempAddPOSRechargeHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="TempAddCashRecharge">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">临时卡_增加现金充值记录</wsdl:documentation>
      <wsdl:input message="tns:TempAddCashRechargeHttpPostIn" />
      <wsdl:output message="tns:TempAddCashRechargeHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="TempAddConsumeJsr">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">临时卡_增加消费记录,带经手人</wsdl:documentation>
      <wsdl:input message="tns:TempAddConsumeJsrHttpPostIn" />
      <wsdl:output message="tns:TempAddConsumeJsrHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="TempCancelConsumeJsr">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">临时卡_消费撤销，消费出错时使用,带经手人</wsdl:documentation>
      <wsdl:input message="tns:TempCancelConsumeJsrHttpPostIn" />
      <wsdl:output message="tns:TempCancelConsumeJsrHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="TempAccToCash">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">临时卡_账户退回现金</wsdl:documentation>
      <wsdl:input message="tns:TempAccToCashHttpPostIn" />
      <wsdl:output message="tns:TempAccToCashHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="TempConsumeList">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">临时卡_消费清单</wsdl:documentation>
      <wsdl:input message="tns:TempConsumeListHttpPostIn" />
      <wsdl:output message="tns:TempConsumeListHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="TempCancelCard">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">临时卡_销卡</wsdl:documentation>
      <wsdl:input message="tns:TempCancelCardHttpPostIn" />
      <wsdl:output message="tns:TempCancelCardHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="TempCheckCancel">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">验证临时卡是否注销</wsdl:documentation>
      <wsdl:input message="tns:TempCheckCancelHttpPostIn" />
      <wsdl:output message="tns:TempCheckCancelHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="TempCheckState">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">判断临时卡状态</wsdl:documentation>
      <wsdl:input message="tns:TempCheckStateHttpPostIn" />
      <wsdl:output message="tns:TempCheckStateHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="getRy_Base">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">人员信息_基础信息</wsdl:documentation>
      <wsdl:input message="tns:getRy_BaseHttpPostIn" />
      <wsdl:output message="tns:getRy_BaseHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="getRy_GuoMin">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">人员信息_过敏信息</wsdl:documentation>
      <wsdl:input message="tns:getRy_GuoMinHttpPostIn" />
      <wsdl:output message="tns:getRy_GuoMinHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="getRy_Health">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">人员信息_临床数据</wsdl:documentation>
      <wsdl:input message="tns:getRy_HealthHttpPostIn" />
      <wsdl:output message="tns:getRy_HealthHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="getRy_Info">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">人员信息_职业婚姻</wsdl:documentation>
      <wsdl:input message="tns:getRy_InfoHttpPostIn" />
      <wsdl:output message="tns:getRy_InfoHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="getRy_Lxfs">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">人员信息_联系人</wsdl:documentation>
      <wsdl:input message="tns:getRy_LxfsHttpPostIn" />
      <wsdl:output message="tns:getRy_LxfsHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="getRy_MianYi">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">人员信息_免疫</wsdl:documentation>
      <wsdl:input message="tns:getRy_MianYiHttpPostIn" />
      <wsdl:output message="tns:getRy_MianYiHttpPostOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="WebService1Soap" type="tns:WebService1Soap">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="getRy_Zj">
      <soap:operation soapAction="http://tempuri.org/getRy_Zj" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getMzDataNew">
      <soap:operation soapAction="http://tempuri.org/getMzDataNew" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getYyJsr">
      <soap:operation soapAction="http://tempuri.org/getYyJsr" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="changePwd">
      <soap:operation soapAction="http://tempuri.org/changePwd" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getCity">
      <soap:operation soapAction="http://tempuri.org/getCity" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getYyName">
      <soap:operation soapAction="http://tempuri.org/getYyName" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getSex">
      <soap:operation soapAction="http://tempuri.org/getSex" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getMZ">
      <soap:operation soapAction="http://tempuri.org/getMZ" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getWHCD">
      <soap:operation soapAction="http://tempuri.org/getWHCD" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getHYZK">
      <soap:operation soapAction="http://tempuri.org/getHYZK" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getZY">
      <soap:operation soapAction="http://tempuri.org/getZY" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getZJLB">
      <soap:operation soapAction="http://tempuri.org/getZJLB" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getABXX">
      <soap:operation soapAction="http://tempuri.org/getABXX" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getRHXX">
      <soap:operation soapAction="http://tempuri.org/getRHXX" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getZyData">
      <soap:operation soapAction="http://tempuri.org/getZyData" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getZyDataXml">
      <soap:operation soapAction="http://tempuri.org/getZyDataXml" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getZymx">
      <soap:operation soapAction="http://tempuri.org/getZymx" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getZymxXml">
      <soap:operation soapAction="http://tempuri.org/getZymxXml" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getMzData">
      <soap:operation soapAction="http://tempuri.org/getMzData" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getMzDataXml">
      <soap:operation soapAction="http://tempuri.org/getMzDataXml" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getMzmx">
      <soap:operation soapAction="http://tempuri.org/getMzmx" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getMzmxXml">
      <soap:operation soapAction="http://tempuri.org/getMzmxXml" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getMzLsJl_Date">
      <soap:operation soapAction="http://tempuri.org/getMzLsJl_Date" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CheckATR">
      <soap:operation soapAction="http://tempuri.org/CheckATR" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getAccount">
      <soap:operation soapAction="http://tempuri.org/getAccount" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="addConsumeJsr">
      <soap:operation soapAction="http://tempuri.org/addConsumeJsr" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CancelConsumeJsr">
      <soap:operation soapAction="http://tempuri.org/CancelConsumeJsr" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="addPOSRecharge">
      <soap:operation soapAction="http://tempuri.org/addPOSRecharge" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="addCashRecharge">
      <soap:operation soapAction="http://tempuri.org/addCashRecharge" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="addAppRecharge">
      <soap:operation soapAction="http://tempuri.org/addAppRecharge" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="addAlipayRecharge">
      <soap:operation soapAction="http://tempuri.org/addAlipayRecharge" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AccToCash">
      <soap:operation soapAction="http://tempuri.org/AccToCash" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AccToBank">
      <soap:operation soapAction="http://tempuri.org/AccToBank" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="JKKCzJz">
      <soap:operation soapAction="http://tempuri.org/JKKCzJz" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="JKKXFJz">
      <soap:operation soapAction="http://tempuri.org/JKKXFJz" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ConsumeList">
      <soap:operation soapAction="http://tempuri.org/ConsumeList" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getJzTime">
      <soap:operation soapAction="http://tempuri.org/getJzTime" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getJzDataNew">
      <soap:operation soapAction="http://tempuri.org/getJzDataNew" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getJzData">
      <soap:operation soapAction="http://tempuri.org/getJzData" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CxJzData">
      <soap:operation soapAction="http://tempuri.org/CxJzData" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CxMxData_Date">
      <soap:operation soapAction="http://tempuri.org/CxMxData_Date" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CxMxData_JzCode">
      <soap:operation soapAction="http://tempuri.org/CxMxData_JzCode" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="addBk">
      <soap:operation soapAction="http://tempuri.org/addBk" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="RemoveBk">
      <soap:operation soapAction="http://tempuri.org/RemoveBk" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CheckBkState">
      <soap:operation soapAction="http://tempuri.org/CheckBkState" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TempGetAccount">
      <soap:operation soapAction="http://tempuri.org/TempGetAccount" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getTempTellAdd">
      <soap:operation soapAction="http://tempuri.org/getTempTellAdd" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TempNewCard">
      <soap:operation soapAction="http://tempuri.org/TempNewCard" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TempAddPOSRecharge">
      <soap:operation soapAction="http://tempuri.org/TempAddPOSRecharge" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TempAddCashRecharge">
      <soap:operation soapAction="http://tempuri.org/TempAddCashRecharge" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TempAddConsumeJsr">
      <soap:operation soapAction="http://tempuri.org/TempAddConsumeJsr" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TempCancelConsumeJsr">
      <soap:operation soapAction="http://tempuri.org/TempCancelConsumeJsr" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TempAccToCash">
      <soap:operation soapAction="http://tempuri.org/TempAccToCash" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TempConsumeList">
      <soap:operation soapAction="http://tempuri.org/TempConsumeList" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TempCancelCard">
      <soap:operation soapAction="http://tempuri.org/TempCancelCard" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TempCheckCancel">
      <soap:operation soapAction="http://tempuri.org/TempCheckCancel" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TempCheckState">
      <soap:operation soapAction="http://tempuri.org/TempCheckState" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getRy_Base">
      <soap:operation soapAction="http://tempuri.org/getRy_Base" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getRy_GuoMin">
      <soap:operation soapAction="http://tempuri.org/getRy_GuoMin" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getRy_Health">
      <soap:operation soapAction="http://tempuri.org/getRy_Health" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getRy_Info">
      <soap:operation soapAction="http://tempuri.org/getRy_Info" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getRy_Lxfs">
      <soap:operation soapAction="http://tempuri.org/getRy_Lxfs" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getRy_MianYi">
      <soap:operation soapAction="http://tempuri.org/getRy_MianYi" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="WebService1Soap12" type="tns:WebService1Soap">
    <soap12:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="getRy_Zj">
      <soap12:operation soapAction="http://tempuri.org/getRy_Zj" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getMzDataNew">
      <soap12:operation soapAction="http://tempuri.org/getMzDataNew" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getYyJsr">
      <soap12:operation soapAction="http://tempuri.org/getYyJsr" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="changePwd">
      <soap12:operation soapAction="http://tempuri.org/changePwd" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getCity">
      <soap12:operation soapAction="http://tempuri.org/getCity" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getYyName">
      <soap12:operation soapAction="http://tempuri.org/getYyName" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getSex">
      <soap12:operation soapAction="http://tempuri.org/getSex" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getMZ">
      <soap12:operation soapAction="http://tempuri.org/getMZ" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getWHCD">
      <soap12:operation soapAction="http://tempuri.org/getWHCD" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getHYZK">
      <soap12:operation soapAction="http://tempuri.org/getHYZK" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getZY">
      <soap12:operation soapAction="http://tempuri.org/getZY" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getZJLB">
      <soap12:operation soapAction="http://tempuri.org/getZJLB" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getABXX">
      <soap12:operation soapAction="http://tempuri.org/getABXX" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getRHXX">
      <soap12:operation soapAction="http://tempuri.org/getRHXX" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getZyData">
      <soap12:operation soapAction="http://tempuri.org/getZyData" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getZyDataXml">
      <soap12:operation soapAction="http://tempuri.org/getZyDataXml" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getZymx">
      <soap12:operation soapAction="http://tempuri.org/getZymx" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getZymxXml">
      <soap12:operation soapAction="http://tempuri.org/getZymxXml" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getMzData">
      <soap12:operation soapAction="http://tempuri.org/getMzData" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getMzDataXml">
      <soap12:operation soapAction="http://tempuri.org/getMzDataXml" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getMzmx">
      <soap12:operation soapAction="http://tempuri.org/getMzmx" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getMzmxXml">
      <soap12:operation soapAction="http://tempuri.org/getMzmxXml" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getMzLsJl_Date">
      <soap12:operation soapAction="http://tempuri.org/getMzLsJl_Date" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CheckATR">
      <soap12:operation soapAction="http://tempuri.org/CheckATR" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getAccount">
      <soap12:operation soapAction="http://tempuri.org/getAccount" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="addConsumeJsr">
      <soap12:operation soapAction="http://tempuri.org/addConsumeJsr" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CancelConsumeJsr">
      <soap12:operation soapAction="http://tempuri.org/CancelConsumeJsr" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="addPOSRecharge">
      <soap12:operation soapAction="http://tempuri.org/addPOSRecharge" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="addCashRecharge">
      <soap12:operation soapAction="http://tempuri.org/addCashRecharge" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="addAppRecharge">
      <soap12:operation soapAction="http://tempuri.org/addAppRecharge" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="addAlipayRecharge">
      <soap12:operation soapAction="http://tempuri.org/addAlipayRecharge" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AccToCash">
      <soap12:operation soapAction="http://tempuri.org/AccToCash" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AccToBank">
      <soap12:operation soapAction="http://tempuri.org/AccToBank" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="JKKCzJz">
      <soap12:operation soapAction="http://tempuri.org/JKKCzJz" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="JKKXFJz">
      <soap12:operation soapAction="http://tempuri.org/JKKXFJz" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ConsumeList">
      <soap12:operation soapAction="http://tempuri.org/ConsumeList" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getJzTime">
      <soap12:operation soapAction="http://tempuri.org/getJzTime" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getJzDataNew">
      <soap12:operation soapAction="http://tempuri.org/getJzDataNew" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getJzData">
      <soap12:operation soapAction="http://tempuri.org/getJzData" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CxJzData">
      <soap12:operation soapAction="http://tempuri.org/CxJzData" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CxMxData_Date">
      <soap12:operation soapAction="http://tempuri.org/CxMxData_Date" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CxMxData_JzCode">
      <soap12:operation soapAction="http://tempuri.org/CxMxData_JzCode" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="addBk">
      <soap12:operation soapAction="http://tempuri.org/addBk" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="RemoveBk">
      <soap12:operation soapAction="http://tempuri.org/RemoveBk" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CheckBkState">
      <soap12:operation soapAction="http://tempuri.org/CheckBkState" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TempGetAccount">
      <soap12:operation soapAction="http://tempuri.org/TempGetAccount" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getTempTellAdd">
      <soap12:operation soapAction="http://tempuri.org/getTempTellAdd" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TempNewCard">
      <soap12:operation soapAction="http://tempuri.org/TempNewCard" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TempAddPOSRecharge">
      <soap12:operation soapAction="http://tempuri.org/TempAddPOSRecharge" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TempAddCashRecharge">
      <soap12:operation soapAction="http://tempuri.org/TempAddCashRecharge" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TempAddConsumeJsr">
      <soap12:operation soapAction="http://tempuri.org/TempAddConsumeJsr" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TempCancelConsumeJsr">
      <soap12:operation soapAction="http://tempuri.org/TempCancelConsumeJsr" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TempAccToCash">
      <soap12:operation soapAction="http://tempuri.org/TempAccToCash" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TempConsumeList">
      <soap12:operation soapAction="http://tempuri.org/TempConsumeList" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TempCancelCard">
      <soap12:operation soapAction="http://tempuri.org/TempCancelCard" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TempCheckCancel">
      <soap12:operation soapAction="http://tempuri.org/TempCheckCancel" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TempCheckState">
      <soap12:operation soapAction="http://tempuri.org/TempCheckState" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getRy_Base">
      <soap12:operation soapAction="http://tempuri.org/getRy_Base" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getRy_GuoMin">
      <soap12:operation soapAction="http://tempuri.org/getRy_GuoMin" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getRy_Health">
      <soap12:operation soapAction="http://tempuri.org/getRy_Health" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getRy_Info">
      <soap12:operation soapAction="http://tempuri.org/getRy_Info" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getRy_Lxfs">
      <soap12:operation soapAction="http://tempuri.org/getRy_Lxfs" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getRy_MianYi">
      <soap12:operation soapAction="http://tempuri.org/getRy_MianYi" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="WebService1HttpGet" type="tns:WebService1HttpGet">
    <http:binding verb="GET" />
    <wsdl:operation name="getRy_Zj">
      <http:operation location="/getRy_Zj" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getMzDataNew">
      <http:operation location="/getMzDataNew" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getYyJsr">
      <http:operation location="/getYyJsr" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="changePwd">
      <http:operation location="/changePwd" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getCity">
      <http:operation location="/getCity" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getYyName">
      <http:operation location="/getYyName" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getSex">
      <http:operation location="/getSex" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getMZ">
      <http:operation location="/getMZ" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getWHCD">
      <http:operation location="/getWHCD" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getHYZK">
      <http:operation location="/getHYZK" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getZY">
      <http:operation location="/getZY" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getZJLB">
      <http:operation location="/getZJLB" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getABXX">
      <http:operation location="/getABXX" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getRHXX">
      <http:operation location="/getRHXX" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getZyData">
      <http:operation location="/getZyData" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getZyDataXml">
      <http:operation location="/getZyDataXml" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getZymx">
      <http:operation location="/getZymx" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getZymxXml">
      <http:operation location="/getZymxXml" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getMzData">
      <http:operation location="/getMzData" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getMzDataXml">
      <http:operation location="/getMzDataXml" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getMzmx">
      <http:operation location="/getMzmx" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getMzmxXml">
      <http:operation location="/getMzmxXml" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getMzLsJl_Date">
      <http:operation location="/getMzLsJl_Date" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CheckATR">
      <http:operation location="/CheckATR" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getAccount">
      <http:operation location="/getAccount" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="addConsumeJsr">
      <http:operation location="/addConsumeJsr" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CancelConsumeJsr">
      <http:operation location="/CancelConsumeJsr" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="addPOSRecharge">
      <http:operation location="/addPOSRecharge" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="addCashRecharge">
      <http:operation location="/addCashRecharge" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="addAppRecharge">
      <http:operation location="/addAppRecharge" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="addAlipayRecharge">
      <http:operation location="/addAlipayRecharge" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AccToCash">
      <http:operation location="/AccToCash" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AccToBank">
      <http:operation location="/AccToBank" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="JKKCzJz">
      <http:operation location="/JKKCzJz" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="JKKXFJz">
      <http:operation location="/JKKXFJz" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ConsumeList">
      <http:operation location="/ConsumeList" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getJzTime">
      <http:operation location="/getJzTime" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getJzDataNew">
      <http:operation location="/getJzDataNew" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getJzData">
      <http:operation location="/getJzData" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CxJzData">
      <http:operation location="/CxJzData" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CxMxData_Date">
      <http:operation location="/CxMxData_Date" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CxMxData_JzCode">
      <http:operation location="/CxMxData_JzCode" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="addBk">
      <http:operation location="/addBk" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="RemoveBk">
      <http:operation location="/RemoveBk" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CheckBkState">
      <http:operation location="/CheckBkState" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TempGetAccount">
      <http:operation location="/TempGetAccount" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getTempTellAdd">
      <http:operation location="/getTempTellAdd" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TempNewCard">
      <http:operation location="/TempNewCard" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TempAddPOSRecharge">
      <http:operation location="/TempAddPOSRecharge" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TempAddCashRecharge">
      <http:operation location="/TempAddCashRecharge" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TempAddConsumeJsr">
      <http:operation location="/TempAddConsumeJsr" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TempCancelConsumeJsr">
      <http:operation location="/TempCancelConsumeJsr" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TempAccToCash">
      <http:operation location="/TempAccToCash" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TempConsumeList">
      <http:operation location="/TempConsumeList" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TempCancelCard">
      <http:operation location="/TempCancelCard" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TempCheckCancel">
      <http:operation location="/TempCheckCancel" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TempCheckState">
      <http:operation location="/TempCheckState" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getRy_Base">
      <http:operation location="/getRy_Base" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getRy_GuoMin">
      <http:operation location="/getRy_GuoMin" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getRy_Health">
      <http:operation location="/getRy_Health" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getRy_Info">
      <http:operation location="/getRy_Info" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getRy_Lxfs">
      <http:operation location="/getRy_Lxfs" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getRy_MianYi">
      <http:operation location="/getRy_MianYi" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="WebService1HttpPost" type="tns:WebService1HttpPost">
    <http:binding verb="POST" />
    <wsdl:operation name="getRy_Zj">
      <http:operation location="/getRy_Zj" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getMzDataNew">
      <http:operation location="/getMzDataNew" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getYyJsr">
      <http:operation location="/getYyJsr" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="changePwd">
      <http:operation location="/changePwd" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getCity">
      <http:operation location="/getCity" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getYyName">
      <http:operation location="/getYyName" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getSex">
      <http:operation location="/getSex" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getMZ">
      <http:operation location="/getMZ" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getWHCD">
      <http:operation location="/getWHCD" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getHYZK">
      <http:operation location="/getHYZK" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getZY">
      <http:operation location="/getZY" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getZJLB">
      <http:operation location="/getZJLB" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getABXX">
      <http:operation location="/getABXX" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getRHXX">
      <http:operation location="/getRHXX" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getZyData">
      <http:operation location="/getZyData" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getZyDataXml">
      <http:operation location="/getZyDataXml" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getZymx">
      <http:operation location="/getZymx" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getZymxXml">
      <http:operation location="/getZymxXml" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getMzData">
      <http:operation location="/getMzData" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getMzDataXml">
      <http:operation location="/getMzDataXml" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getMzmx">
      <http:operation location="/getMzmx" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getMzmxXml">
      <http:operation location="/getMzmxXml" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getMzLsJl_Date">
      <http:operation location="/getMzLsJl_Date" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CheckATR">
      <http:operation location="/CheckATR" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getAccount">
      <http:operation location="/getAccount" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="addConsumeJsr">
      <http:operation location="/addConsumeJsr" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CancelConsumeJsr">
      <http:operation location="/CancelConsumeJsr" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="addPOSRecharge">
      <http:operation location="/addPOSRecharge" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="addCashRecharge">
      <http:operation location="/addCashRecharge" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="addAppRecharge">
      <http:operation location="/addAppRecharge" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="addAlipayRecharge">
      <http:operation location="/addAlipayRecharge" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AccToCash">
      <http:operation location="/AccToCash" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AccToBank">
      <http:operation location="/AccToBank" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="JKKCzJz">
      <http:operation location="/JKKCzJz" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="JKKXFJz">
      <http:operation location="/JKKXFJz" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ConsumeList">
      <http:operation location="/ConsumeList" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getJzTime">
      <http:operation location="/getJzTime" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getJzDataNew">
      <http:operation location="/getJzDataNew" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getJzData">
      <http:operation location="/getJzData" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CxJzData">
      <http:operation location="/CxJzData" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CxMxData_Date">
      <http:operation location="/CxMxData_Date" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CxMxData_JzCode">
      <http:operation location="/CxMxData_JzCode" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="addBk">
      <http:operation location="/addBk" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="RemoveBk">
      <http:operation location="/RemoveBk" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CheckBkState">
      <http:operation location="/CheckBkState" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TempGetAccount">
      <http:operation location="/TempGetAccount" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getTempTellAdd">
      <http:operation location="/getTempTellAdd" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TempNewCard">
      <http:operation location="/TempNewCard" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TempAddPOSRecharge">
      <http:operation location="/TempAddPOSRecharge" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TempAddCashRecharge">
      <http:operation location="/TempAddCashRecharge" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TempAddConsumeJsr">
      <http:operation location="/TempAddConsumeJsr" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TempCancelConsumeJsr">
      <http:operation location="/TempCancelConsumeJsr" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TempAccToCash">
      <http:operation location="/TempAccToCash" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TempConsumeList">
      <http:operation location="/TempConsumeList" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TempCancelCard">
      <http:operation location="/TempCancelCard" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TempCheckCancel">
      <http:operation location="/TempCheckCancel" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TempCheckState">
      <http:operation location="/TempCheckState" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getRy_Base">
      <http:operation location="/getRy_Base" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getRy_GuoMin">
      <http:operation location="/getRy_GuoMin" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getRy_Health">
      <http:operation location="/getRy_Health" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getRy_Info">
      <http:operation location="/getRy_Info" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getRy_Lxfs">
      <http:operation location="/getRy_Lxfs" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getRy_MianYi">
      <http:operation location="/getRy_MianYi" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="WebService1">
    <wsdl:port name="WebService1Soap" binding="tns:WebService1Soap">
      <soap:address location="http://localhost:8312/WebService1.asmx" />
    </wsdl:port>
    <wsdl:port name="WebService1Soap12" binding="tns:WebService1Soap12">
      <soap12:address location="http://localhost:8312/WebService1.asmx" />
    </wsdl:port>
    <wsdl:port name="WebService1HttpGet" binding="tns:WebService1HttpGet">
      <http:address location="http://localhost:8312/WebService1.asmx" />
    </wsdl:port>
    <wsdl:port name="WebService1HttpPost" binding="tns:WebService1HttpPost">
      <http:address location="http://localhost:8312/WebService1.asmx" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>