﻿Imports System.Data.SqlClient
Imports BaseClass

Public Class Zd_Dict2

    Dim My_Cc As New BaseClass.C_Cc()                         '取最大编码及简称的类

#Region "传参"
    Dim Rinsert As Boolean
    Dim Rrow As DataRow
    Dim RZbtb As DataTable
    Dim Rtdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Dim Rlb As C1.Win.C1Input.C1Label
    Dim Rrc As C_RowChange
    Dim Form_Lb As String
#End Region

    Public Sub New(ByVal tinsert As Boolean, ByVal trow As DataRow, ByVal tZbtb As DataTable, ByVal ttdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid, ByVal tlb As C1.Win.C1Input.C1Label, ByRef trc As C_RowChange, ByVal tform_lb As String)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        Rinsert = tinsert
        Rrow = trow
        RZbtb = tZbtb
        Rtdbgrid = ttdbgrid
        Rlb = tlb
        Rrc = trc
        Form_Lb = tform_lb
        ' 在 InitializeComponent() 调用之后添加任何初始化。
    End Sub

    Protected Overrides Sub OnKeyDown(ByVal e As System.Windows.Forms.KeyEventArgs)
        MyBase.OnKeyDown(e)
        If e.Control = True And e.KeyCode = Keys.S Then
            Comm1.Select()
            Call Comm_Click(Comm1, Nothing)
        End If
    End Sub

    Private Sub Zd_Dict2_FormClosed(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosedEventArgs) Handles Me.FormClosed
        RemoveHandler Rrc.RowChanged, New BaseClass.RowChangedHandler(AddressOf Data_Show)
        Me.Dispose()
    End Sub

    Private Sub Zd_Dict2_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        AddHandler Rrc.RowChanged, New BaseClass.RowChangedHandler(AddressOf Data_Show)
        Panel1.Height = 28
        Me.Text = Form_Lb & "明细录入"
        '按扭初始化
        Call P_Comm(Me.Comm1)
        Call P_Comm(Me.Comm2)
        If Rinsert = True Then Call Data_Clear() Else Call Data_Show(Rrow)
    End Sub


#Region "数据__操作"

    Private Sub Data_Clear()
        Rinsert = True
        C1TextBox1.Text = F_MaxCode(HisVar.HisVar.WsyCode)
        C1TextBox2.Text = ""
        C1TextBox3.Text = ""
        L_Dl_Jc.Text = ""
        C1TextBox2.Enabled = True
    End Sub

    Private Sub Data_Show(ByVal tmp_Row As DataRow)
        Rinsert = False
        Rrow = tmp_Row
        With Rrow
            If Form_Lb = "病例类别字典" Then
                C1TextBox1.Text = .Item("Bxlb_Code") & ""
                C1TextBox2.Text = .Item("Bxlb_Name") & ""
                C1TextBox3.Text = .Item("Bxlb_Memo") & ""
                L_Dl_Jc.Text = .Item("Bxlb_Jc") & ""
                If Mid(Rrow("Bxlb_Code"), 5, 2) = "01" Or Mid(Rrow("Bxlb_Code"), 5, 2) = "02" Then
                    C1TextBox2.Enabled = False
                Else
                    C1TextBox2.Enabled = True
                End If
            ElseIf Form_Lb = "病区字典" Then
                C1TextBox1.Text = .Item("Bq_Code") & ""
                C1TextBox2.Text = .Item("Bq_Name") & ""
                C1TextBox3.Text = .Item("Bq_Memo") & ""
                L_Dl_Jc.Text = .Item("Bq_Jc") & ""
            ElseIf Form_Lb = "床位字典" Then
                C1TextBox1.Text = .Item("Bc_Code") & ""
                C1TextBox2.Text = .Item("Bc_Name") & ""
                C1TextBox3.Text = .Item("Bc_Memo") & ""
                L_Dl_Jc.Text = .Item("Bc_Jc") & ""
            ElseIf Form_Lb = "药房字典" Then
                C1TextBox1.Text = .Item("Yf_Code") & ""
                C1TextBox2.Text = .Item("Yf_Name") & ""
                L_Dl_Jc.Text = .Item("Yf_Jc") & ""
                C1TextBox3.Text = .Item("Yf_Memo") & ""
            ElseIf Form_Lb = "功效字典" Then
                If HisPara.PublicConfig.IsCenterDb = "是" Then
                    C1TextBox2.BackColor = SystemColors.Info
                    C1TextBox2.Enabled = False
                End If
                C1TextBox1.Text = .Item("Gx_Code") & ""
                C1TextBox2.Text = .Item("Gx_Name") & ""
                L_Dl_Jc.Text = .Item("Gx_Jc") & ""
                C1TextBox3.Text = .Item("Gx_Memo") & ""
            ElseIf Form_Lb = "剂型字典" Then
                If HisPara.PublicConfig.IsCenterDb = "是" Then
                    C1TextBox2.BackColor = SystemColors.Info
                    C1TextBox2.Enabled = False
                End If
                C1TextBox1.Text = .Item("Jx_Code") & ""
                C1TextBox2.Text = .Item("Jx_Name") & ""
                L_Dl_Jc.Text = .Item("Jx_Jc") & ""
                C1TextBox3.Text = .Item("Jx_Memo") & ""
            ElseIf Form_Lb = "诊疗模板" Then
                C1TextBox1.Text = .Item("Templet_Code") & ""
                C1TextBox2.Text = .Item("Templet_Name") & ""
                L_Dl_Jc.Text = .Item("Templet_Jc") & ""
                C1TextBox3.Text = .Item("Templet_Memo") & ""
            ElseIf Form_Lb = "住院票据分类" Then
                C1TextBox1.Text = .Item("Lb_Code") & ""
                C1TextBox2.Text = .Item("Lb_Name") & ""
                C1TextBox3.Text = .Item("Lb_Memo") & ""
                L_Dl_Jc.Text = .Item("Lb_Jc") & ""
                '----------------------------------
            ElseIf Form_Lb = "门诊票据分类" Then
                C1TextBox1.Text = .Item("Lb_Code") & ""
                C1TextBox2.Text = .Item("Lb_Name") & ""
                C1TextBox3.Text = .Item("Lb_Memo") & ""
                L_Dl_Jc.Text = .Item("Lb_Jc") & ""
            ElseIf Form_Lb = "挂号媒体字典" Then
                C1TextBox1.Text = .Item("Ghmt_Code") & ""
                C1TextBox2.Text = .Item("Ghmt_Name") & ""
                C1TextBox3.Text = .Item("Ghmt_Memo") & ""
                L_Dl_Jc.Text = .Item("Ghmt_Jc") & ""

            End If

        End With
    End Sub


#End Region

#Region "控件__动作"

    Private Sub Comm_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.Click, Comm2.Click
        Select Case sender.tag
            Case "保存"
                Call Data_Save()
            Case "取消"
                Me.Close()
        End Select
    End Sub

    Private Sub TextBox_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles C1TextBox2.KeyPress, C1TextBox3.KeyPress, C1TextBox1.KeyPress
        If e.KeyChar <> Chr(Keys.Return) Then Exit Sub
        e.Handled = True
        System.Windows.Forms.SendKeys.Send("{Tab}")
    End Sub

    Private Sub C1TextBox1_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1TextBox2.Validated
        My_Cc.Get_Py(Me.C1TextBox2.Text & "")
        L_Dl_Jc.Text = My_Cc.简拚.ToString
    End Sub

#End Region

#Region "数据__编辑"

    Private Sub Data_Save()
        Dim My_NewRow As DataRow
        If Rinsert = True Then
            My_NewRow = RZbtb.NewRow
        Else
            My_NewRow = Rrow
        End If

        If C1TextBox2.Text = "" Then
            MsgBox("必须填写名称!", MsgBoxStyle.Critical, "提示")
            C1TextBox2.Focus()
            Exit Sub
        End If
        If Form_Lb = "病例类别字典" Then
            With My_NewRow
                If Rinsert = True Then
                    .Item("Bxlb_Code") = F_MaxCode(HisVar.HisVar.WsyCode)
                Else
                    .Item("Bxlb_Code") = My_NewRow.Item("Bxlb_Code")
                End If
                .Item("Bxlb_Name") = Trim(C1TextBox2.Text & "")
                .Item("Bxlb_Jc") = L_Dl_Jc.Text & ""
                .Item("Bxlb_Memo") = Trim(C1TextBox3.Text & "")
                .Item("Yy_Code") = HisVar.HisVar.WsyCode
            End With
        ElseIf Form_Lb = "病区字典" Then
            With My_NewRow
                If Rinsert = True Then
                    .Item("Bq_Code") = F_MaxCode(HisVar.HisVar.WsyCode)
                Else
                    .Item("Bq_Code") = My_NewRow.Item("Bq_Code")
                End If
                .Item("Bq_Name") = Trim(C1TextBox2.Text & "")
                .Item("Bq_Memo") = Trim(C1TextBox3.Text & "")
                .Item("Bq_Jc") = L_Dl_Jc.Text & ""
                .Item("Yy_Code") = HisVar.HisVar.WsyCode
            End With
        ElseIf Form_Lb = "床位字典" Then
            With My_NewRow
                If Rinsert = True Then
                    .Item("Bc_Code") = F_MaxCode(HisVar.HisVar.WsyCode)
                Else
                    .Item("Bc_Code") = My_NewRow.Item("Bc_Code")
                End If
                .Item("Bc_Name") = Trim(C1TextBox2.Text & "")
                .Item("Bc_Memo") = Trim(C1TextBox3.Text & "")
                .Item("Bc_Jc") = L_Dl_Jc.Text & ""
                .Item("Yy_Code") = HisVar.HisVar.WsyCode
            End With
        ElseIf Form_Lb = "药房字典" Then
            With My_NewRow
                If Rinsert = True Then
                    .Item("Yf_Code") = F_MaxCode(HisVar.HisVar.WsyCode)
                Else
                    .Item("Yf_Code") = My_NewRow.Item("Yf_Code")
                End If
                .Item("Yf_Name") = Trim(C1TextBox2.Text & "")
                .Item("Yf_Jc") = L_Dl_Jc.Text & ""
                .Item("Yf_Memo") = Trim(C1TextBox3.Text & "")
                .Item("Yy_Code") = HisVar.HisVar.WsyCode
            End With
        ElseIf Form_Lb = "功效字典" Then
            With My_NewRow
                If Rinsert = True Then
                    .Item("Gx_Code") = F_MaxCode(HisVar.HisVar.WsyCode)
                Else
                    .Item("Gx_Code") = My_NewRow.Item("Gx_Code")
                End If
                .Item("Gx_Name") = Trim(C1TextBox2.Text & "")
                .Item("Gx_Jc") = L_Dl_Jc.Text & ""
                .Item("Gx_Memo") = Trim(C1TextBox3.Text & "")
            End With
        ElseIf Form_Lb = "剂型字典" Then
            With My_NewRow
                If Rinsert = True Then
                    .Item("Jx_Code") = F_MaxCode(HisVar.HisVar.WsyCode)
                Else
                    .Item("Jx_Code") = My_NewRow.Item("Jx_Code")
                End If
                .Item("Jx_Name") = Trim(C1TextBox2.Text & "")
                .Item("Jx_Jc") = L_Dl_Jc.Text & ""
                .Item("Jx_Memo") = Trim(C1TextBox3.Text & "")
            End With
        ElseIf Form_Lb = "诊疗模板" Then
            With My_NewRow
                If Rinsert = True Then
                    .Item("Templet_Code") = F_MaxCode(HisVar.HisVar.WsyCode)
                Else
                    .Item("Templet_Code") = My_NewRow.Item("Templet_Code")
                End If
                .Item("Templet_Name") = Trim(C1TextBox2.Text & "")
                .Item("Templet_Jc") = L_Dl_Jc.Text & ""
                .Item("Templet_Memo") = Trim(C1TextBox3.Text & "")
                .Item("Yy_Code") = HisVar.HisVar.WsyCode
            End With
        ElseIf Form_Lb = "住院票据分类" Then
            With My_NewRow
                If Rinsert = True Then
                    .Item("Lb_Code") = F_MaxCode(HisVar.HisVar.WsyCode)
                Else
                    .Item("Lb_Code") = My_NewRow.Item("Lb_Code")
                End If
                .Item("Lb_Name") = Trim(C1TextBox2.Text & "")
                .Item("Lb_Memo") = Trim(C1TextBox3.Text & "")
                .Item("Lb_Jc") = L_Dl_Jc.Text & ""
                .Item("Yy_Code") = HisVar.HisVar.WsyCode
            End With
            '-----------------------------------------
        ElseIf Form_Lb = "门诊票据分类" Then
            With My_NewRow
                If Rinsert = True Then
                    .Item("Lb_Code") = F_MaxCode(HisVar.HisVar.WsyCode)
                Else
                    .Item("Lb_Code") = My_NewRow.Item("Lb_Code")
                End If
                .Item("Lb_Name") = Trim(C1TextBox2.Text & "")
                .Item("Lb_Memo") = Trim(C1TextBox3.Text & "")
                .Item("Lb_Jc") = L_Dl_Jc.Text & ""

            End With
        ElseIf Form_Lb = "挂号媒体字典" Then
            With My_NewRow
                If Rinsert = True Then
                    .Item("Ghmt_Code") = F_MaxCode(HisVar.HisVar.WsyCode)
                Else
                    .Item("Ghmt_Code") = My_NewRow.Item("Ghmt_Code")
                End If
                .Item("Ghmt_Name") = Trim(C1TextBox2.Text & "")
                .Item("Ghmt_Jc") = L_Dl_Jc.Text & ""
                .Item("Ghmt_Memo") = Trim(C1TextBox3.Text & "")
                .Item("Yy_Code") = HisVar.HisVar.WsyCode
            End With

        End If
        '数据保存
        Try
            Call Zb_Para_Int(My_NewRow)
            If Rinsert = True Then
                RZbtb.Rows.Add(My_NewRow)
                Rrow = My_NewRow
                Rtdbgrid.MoveLast()
                Call Data_Clear()
            End If
            Rlb.Text = "∑=" + Rtdbgrid.Splits(0).Rows.Count.ToString
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            Exit Sub
        Finally
            Me.C1TextBox2.Select()
        End Try
    End Sub

    Private Sub Zb_Para_Int(ByVal Row As DataRow)
        Dim Insert_String As String = ""
        Dim Update_String As String = ""
        Dim para() As SqlParameter = Nothing
        Dim ilist As List(Of SqlParameter) = New List(Of SqlParameter)()
        If Form_Lb = "病例类别字典" Then
            ilist.Add(New SqlParameter("@Yy_Code", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Bxlb_Name", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Bxlb_Jc", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Bxlb_Memo", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Bxlb_Code", SqlDbType.Char))
            Insert_String = "Insert Into Zd_Bxlb(Yy_Code,Bxlb_Name,Bxlb_Jc,Bxlb_Memo,Bxlb_Code)Values(@Yy_Code,@Bxlb_Name,@Bxlb_Jc,@Bxlb_Memo,@Bxlb_Code)"
            Update_String = "Update Zd_Bxlb Set Yy_Code=@Yy_Code,Bxlb_Name=@Bxlb_Name,Bxlb_Jc=@Bxlb_Jc,Bxlb_Memo=@Bxlb_Memo Where Bxlb_Code=@Bxlb_Code"
        ElseIf Form_Lb = "病区字典" Then
            ilist.Add(New SqlParameter("@Yy_Code", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Bq_Name", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Bq_Jc", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Bq_Memo", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Bq_Code", SqlDbType.Char))
            Insert_String = "Insert Into Zd_YyBq1(Yy_Code,Bq_Name,Bq_Jc,Bq_Memo,Bq_Code)Values(@Yy_Code,@Bq_Name,@Bq_Jc,@Bq_Memo,@Bq_Code)"
            Update_String = "Update Zd_YyBq1 Set Yy_Code=@Yy_Code,Bq_Name=@Bq_Name,Bq_Jc=@Bq_Jc,Bq_Memo=@Bq_Memo Where Bq_Code=@Bq_Code"
        ElseIf Form_Lb = "床位字典" Then
            ilist.Add(New SqlParameter("@Yy_Code", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Bc_Name", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Bc_Jc", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Bc_Memo", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Bc_Code", SqlDbType.Char))
            Insert_String = "Insert Into Zd_YyBc(Yy_Code,Bc_Name,Bc_Jc,Bc_Memo,Bc_Code)Values(@Yy_Code,@Bc_Name,@Bc_Jc,@Bc_Memo,@Bc_Code)"
            Update_String = "Update Zd_YyBc Set Yy_Code=@Yy_Code,Bc_Name=@Bc_Name,Bc_Jc=@Bc_Jc,Bc_Memo=@Bc_Memo Where Bc_Code=@Bc_Code"
        ElseIf Form_Lb = "药房字典" Then
            ilist.Add(New SqlParameter("@Yy_Code", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Yf_Name", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Yf_Jc", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Yf_Memo", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Yf_Use", SqlDbType.Char))
            ilist.Add(New SqlParameter("@Yf_Code", SqlDbType.Char))
            Insert_String = "Insert Into Zd_YyYf (Yy_Code,Yf_Name,Yf_Jc,Yf_Memo,Yf_Use,Yf_Code)values(@Yy_Code,@Yf_Name,@Yf_Jc,@Yf_Memo,@Yf_Use,@Yf_Code)"
            Update_String = "Update Zd_YyYf Set Yy_Code=@Yy_Code,Yf_Name=@Yf_Name,Yf_Jc=@Yf_Jc,Yf_Memo=@Yf_Memo,Yf_Use=@Yf_Use where Yf_Code=@Yf_Code"
        ElseIf Form_Lb = "功效字典" Then
            ilist.Add(New SqlParameter("@Gx_Name", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Gx_Jc", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Gx_Memo", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Gx_Code", SqlDbType.Char))
            Insert_String = "Insert Into Zd_Ml_YpGx(Gx_Name,Gx_Jc,Gx_Memo,Gx_Code)Values(@Gx_Name,@Gx_Jc,@Gx_Memo,@Gx_Code)"
            Update_String = "Update Zd_Ml_YpGx Set Gx_Name=@Gx_Name,Gx_Jc=@Gx_Jc,Gx_Memo=@Gx_Memo where Gx_Code=@Gx_Code"
        ElseIf Form_Lb = "剂型字典" Then
            ilist.Add(New SqlParameter("@Jx_Name", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Jx_Jc", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Jx_Memo", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Jx_Code", SqlDbType.Char))
            Insert_String = "Insert Into Zd_Ml_Ypjx(Jx_Name,Jx_Jc,Jx_Memo,Jx_Code)Values(@Jx_Name,@Jx_Jc,@Jx_Memo,@Jx_Code)"
            Update_String = "Update Zd_Ml_Ypjx Set Jx_Name=@Jx_Name,Jx_Jc=@Jx_Jc,Jx_Memo=@Jx_Memo where Jx_Code=@Jx_Code"
        ElseIf Form_Lb = "诊疗模板" Then
            ilist.Add(New SqlParameter("@Yy_Code", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Templet_Name", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Templet_Jc", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Templet_Memo", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Templet_Code", SqlDbType.Char))
            Insert_String = "Insert Into Zd_Templet1(Yy_Code,Templet_Name,Templet_Jc,Templet_Memo,Templet_Code)Values(@Yy_Code,@Templet_Name,@Templet_Jc,@Templet_Memo,@Templet_Code)"
            Update_String = "Update Zd_Templet1 Set Yy_Code=@Yy_Code,Templet_Name=@Templet_Name,Templet_Jc=@Templet_Jc,Templet_Memo=@Templet_Memo Where Templet_Code=@Templet_Code"
        ElseIf Form_Lb = "住院票据分类" Then
            ilist.Add(New SqlParameter("@Yy_Code", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Lb_Name", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Lb_Jc", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Lb_Memo", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Lb_Code", SqlDbType.Char))
            Insert_String = "Insert Into Zd_JkFl1(Yy_Code,Lb_Name,Lb_Jc,Lb_Memo,Lb_Code)Values(@Yy_Code,@Lb_Name,@Lb_Jc,@Lb_Memo,@Lb_Code)"
            Update_String = "Update Zd_JkFl1 Set Yy_Code=@Yy_Code,Lb_Name=@Lb_Name,Lb_Jc=@Lb_Jc,Lb_Memo=@Lb_Memo Where Lb_Code=@Lb_Code"
            '’-------------------------------------------

        ElseIf Form_Lb = "门诊票据分类" Then
            ilist.Add(New SqlParameter("@Lb_Name", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Lb_Jc", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Lb_Memo", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Lb_Code", SqlDbType.Char))
            Insert_String = "Insert Into Zd_MzFp1(Lb_Name,Lb_Jc,Lb_Memo,Lb_Code)Values(@Lb_Name,@Lb_Jc,@Lb_Memo,@Lb_Code)"
            Update_String = "Update Zd_MzFp1 Set Lb_Name=@Lb_Name,Lb_Jc=@Lb_Jc,Lb_Memo=@Lb_Memo Where Lb_Code=@Lb_Code"

        ElseIf Form_Lb = "挂号媒体字典" Then
            ilist.Add(New SqlParameter("@Yy_Code", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Ghmt_Name", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Ghmt_Jc", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Ghmt_Memo", SqlDbType.VarChar))
            ilist.Add(New SqlParameter("@Ghmt_Code", SqlDbType.Char))
            Insert_String = "Insert Into Zd_GhMt(Yy_Code,Ghmt_Code,Ghmt_Name,Ghmt_Jc,Ghmt_Memo)Values(@Yy_Code,@Ghmt_Code,@Ghmt_Name,@Ghmt_Jc,@Ghmt_Memo)"
            Update_String = "Update Zd_GhMt Set Yy_Code=@Yy_Code,Ghmt_Name=@Ghmt_Name,Ghmt_Jc=@Ghmt_Jc,Ghmt_Memo=@Ghmt_Memo Where Ghmt_Code=@Ghmt_Code"
        End If

        para = ilist.ToArray()
        If Rinsert = True Then
            Zb_Para_Excute(para, Row, Insert_String)
            ' Rinsert = False
        Else
            Zb_Para_Excute(para, Row, Update_String)
        End If
    End Sub

    Private Sub Zb_Para_Excute(ByVal V_Paras() As SqlClient.SqlParameter, ByVal V_Row As DataRow, ByVal V_Command_Str As String)
        For I = 0 To V_Paras.Length - 1
            V_Paras(I).Value = V_Row.Item(Mid(V_Paras(I).ParameterName, 2))
        Next
        Try
            HisVar.HisVar.Sqldal.ExecuteSql(V_Command_Str, V_Paras)
        Catch ex As Exception
            MsgBox(ex.Message.ToString, MsgBoxStyle.Information, "提示")
        End Try
    End Sub

    Private Function F_MaxCode(ByVal V_MaxCode As String)   '入库编码
        Dim V_Code As String = V_MaxCode
        If Form_Lb = "病例类别字典" Then
            My_Cc.Get_MaxCode("Zd_Bxlb", "Bxlb_Code", 6, "Yy_Code", V_MaxCode)
        ElseIf Form_Lb = "病区字典" Then
            My_Cc.Get_MaxCode("Zd_YyBq1", "Bq_Code", 7, "Yy_Code", V_MaxCode)
        ElseIf Form_Lb = "床位字典" Then
            My_Cc.Get_MaxCode("Zd_YyBc", "Bc_Code", 7, "Yy_Code", V_MaxCode)
        ElseIf Form_Lb = "药房字典" Then
            My_Cc.Get_MaxCode("Zd_YyYf", "Yf_Code", 6, "Yy_Code", V_MaxCode)
        ElseIf Form_Lb = "功效字典" Then
            My_Cc.Get_MaxCode("Zd_Ml_YpGx", "Gx_Code", 3, "", "")
        ElseIf Form_Lb = "剂型字典" Then
            My_Cc.Get_MaxCode("Zd_Ml_Ypjx", "Jx_Code", 2, "", "")
        ElseIf Form_Lb = "诊疗模板" Then
            My_Cc.Get_MaxCode("Zd_Templet1", "Templet_Code", 8, "Yy_Code", V_MaxCode)
        ElseIf Form_Lb = "住院票据分类" Then
            My_Cc.Get_MaxCode("Zd_JkFl1", "Lb_Code", 8, "Yy_Code", V_MaxCode)
            '’-----------------------------------------------------
        ElseIf Form_Lb = "门诊票据分类" Then
            My_Cc.Get_MaxCode("Zd_MzFp1", "Lb_Code", 2, "", "")
        ElseIf Form_Lb = "挂号媒体字典" Then
            My_Cc.Get_MaxCode("Zd_GhMt", "Ghmt_Code", 7, "Yy_Code", V_MaxCode)
        End If
        F_MaxCode = My_Cc.编码
        If Mid(F_MaxCode, 1, 6) = "000000" Then F_MaxCode = V_MaxCode + Mid(My_Cc.编码, 7)
        Return F_MaxCode
    End Function

  

#End Region

#Region "自定义按扭"

    Private Sub P_Comm(ByVal Bt As Button)
        With Bt
            Select Case .Tag
                Case "保存"
                    .Top = 3
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")
                    .Size = New Size(52, 24)
                    .Text = "            &B"
                Case "取消"
                    .Location = New Point(Comm1.Left + Comm1.Width, Comm1.Top)
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
                    .Size = New Size(52, 24)
                    .Text = "            &C"
            End Select
            .FlatStyle = FlatStyle.Flat
            .FlatAppearance.BorderSize = 0
            .BackgroundImageLayout = ImageLayout.Center
        End With

    End Sub

    Private Sub Comm_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles Comm1.KeyDown, Comm2.KeyDown
        If e.KeyCode = Keys.Space Then
            Select Case sender.tag
                Case "保存"
                    Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存3")

                Case "取消"
                    Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消3")
            End Select
        End If
    End Sub

    Private Sub Comm_KeyUp(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles Comm1.KeyUp, Comm2.KeyUp
        If e.KeyCode = Keys.Enter Or e.KeyCode = Keys.Space Then
            Select Case sender.tag
                Case "保存"
                    Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")
                Case "取消"
                    Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
            End Select
        End If

    End Sub

    Private Sub Comm_MouseEnter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.MouseEnter, Comm2.MouseEnter
        Select Case sender.tag
            Case "保存"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存2")
            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消2")
        End Select
    End Sub

    Private Sub Comm_MouseLeave(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.MouseLeave, Comm2.MouseLeave
        Select Case sender.tag
            Case "保存"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")
            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
        End Select
    End Sub

    Private Sub Comm_MouseDown(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm1.MouseDown, Comm2.MouseDown
        Select Case sender.tag
            Case "保存"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存3")

            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消3")
        End Select
    End Sub

    Private Sub Comm_MouseUp(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm1.MouseUp, Comm2.MouseUp
        Select Case sender.tag
            Case "保存"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")
            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
        End Select
    End Sub

#End Region

#Region "输入法设置"
    '中文
    Private Sub C1TextBox1_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1TextBox2.GotFocus, C1TextBox3.GotFocus
        InputLanguage.CurrentInputLanguage = HisVar.HisVar.InputCode
    End Sub
#End Region

End Class
