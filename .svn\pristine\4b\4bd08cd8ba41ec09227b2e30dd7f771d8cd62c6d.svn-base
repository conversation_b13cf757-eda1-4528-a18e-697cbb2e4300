﻿Imports System.Data.SqlClient


Public Class Zy_Fy1


#Region "定义__变量"
    Dim My_Table As New DataTable                       '从表一
    Dim V_Sum As Double                                 '金额小计

    Public Zb_Cm As CurrencyManager             '同步指针
    Public Zb_Row As DataRow                    '选 择 行

    Public My_DataSet As New DataSet
    Dim Cf_Code_Set As String
#End Region

    Private Sub Zy_Fy1_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Call Form_Init()
        Call Show_Data()       '显示数据
    End Sub

#Region "窗体事件"

    Private Sub Form_Init()
        Panel1.BorderStyle = BorderStyle.None
        Panel1.Height = 24

        '初始化TDBGrid
        Dim My_Grid As New BaseClass.C_Grid(Me.C1TrueDBGrid1)
        With My_Grid
            .Init_Grid()
            .P_Dock(BaseClass.C_Grid.Dock.Fill)
            .AllAddNew(False)
            .AllDelete(False)
            .AllUpdate(False)
            .AllSort(True)
            .P_MultiSelect(BaseClass.C_Grid.MultiSelect.Extended)
            .P_MarqueeEnum(BaseClass.C_Grid.MarqueeEnum.HighlightRow)
            .Init_Column("医嘱编码", "Cf_Code", 110, "中", "")
            .Init_Column("病人姓名", "Ry_Name", 100, "左", "")
            .Init_Column("处方日期", "Cf_Date", 75, "中", "yyyy-MM-dd")
            .Init_Column("处方时间", "Cf_Time", 75, "中", "")
            .Init_Column("科室名称", "Ks_Name", 130, "左", "")
            .Init_Column("病床名称", "Bc_Name", 130, "左", "")
            .Init_Column("医生姓名", "Ys_Name", 80, "左", "")
            .Init_Column("金额", "Cf_Money", 80, "右", "###,##0.00")
            .Init_Column("医嘱类型", "Cf_Lx", 80, "中", "")
            '  .Init_Column("药品发送状态", "Mz_Qr1", 10, "中", "")
        End With
        '初始化TDBGrid
        Dim My_Grid1 As New BaseClass.C_Grid(Me.C1TrueDBGrid2)
        With My_Grid1
            .Init_Grid()
            .P_Dock(BaseClass.C_Grid.Dock.Fill)
            .Init_Column("经手人", "Jsr_Name", 80, "左", "")
            .Init_Column("处方数量", "Cf_Sl", 100, "右", "")
            .Init_Column("药品总价", "Cf_Money", 100, "右", "###,##0.00")
            .AllDelete(False)
        End With
        C1TrueDBGrid2.Visible = False

        Dim My_Grid2 As New BaseClass.C_Grid(Me.C1TrueDBGrid3)
        With My_Grid2
            .Init_Grid()
            .P_Dock(BaseClass.C_Grid.Dock.Fill)
            .Init_Column("医生", "Ys_Name", 80, "左", "")
            .Init_Column("处方数量", "Cf_Sl", 100, "右", "")
            .Init_Column("药品总价", "Cf_Money", 100, "右", "###,##0.00")
            .AllDelete(False)
        End With
        C1TrueDBGrid3.Visible = False

        Dim My_Grid3 As New BaseClass.C_Grid(Me.C1TrueDBGrid4)
        With My_Grid3
            .Init_Grid()
            .P_Dock(BaseClass.C_Grid.Dock.Fill)
            .Init_Column("科室", "Ks_Name", 80, "左", "")
            .Init_Column("处方数量", "Cf_Sl", 100, "右", "")
            .Init_Column("药品总价", "Cf_Money", 100, "右", "###,##0.00")
            .AllDelete(False)
        End With
        C1TrueDBGrid4.Visible = False

        Dim My_Grid4 As New BaseClass.C_Grid(Me.C1TrueDBGrid5)
        With My_Grid4
            .Init_Grid()
            .P_Dock(BaseClass.C_Grid.Dock.Fill)
            .Init_Column("处方类型", "Cf_Lx", 80, "中", "")
            .Init_Column("处方数量", "Cf_Sl", 100, "右", "")
            .Init_Column("药品总价", "Cf_Money", 100, "右", "###,##0.00")
            .AllDelete(False)
        End With
        C1TrueDBGrid5.Visible = False

        Dim My_Grid5 As New BaseClass.C_Grid(Me.C1TrueDBGrid6)
        With My_Grid5
            .Init_Grid()
            .P_Dock(BaseClass.C_Grid.Dock.Fill)
            .Init_Column("汇总方式", "Cf", 100, "中", "")
            .Init_Column("处方数量", "Cf_Sl", 100, "右", "")
            .Init_Column("药品总价", "Cf_Money", 100, "右", "###,##0.00")
            .AllDelete(False)
        End With
        C1TrueDBGrid6.Visible = False

        Dim My_Combo1 As New BaseClass.C_Combo1(Me.C1Combo1)
        With My_Combo1
            .Init_TDBCombo()
            .AddItem("按经手人汇总发药")
            .AddItem("按医生汇总发药")
            .AddItem("按科室汇总发药")
            .AddItem("按长期/临时医嘱汇总发药")
            .AddItem("全部待发药汇总")
            .SelectedIndex(0)
        End With
        C1Combo1.Width = 165
        C1Combo1.DropDownWidth = 165
    End Sub

#End Region

#Region "数据编辑"

    Private Sub Show_Data()
        Dim Str_Select As String
        If HisPara.PublicConfig.ZyHsz = "是" Then
            Str_Select = "Select Bl_Cf.Jsr_Code,Cf_Code,Bl_Cf.Ks_Code,Ks_Name,Bl_Cf.Ys_Code,Ys_Name,Bl_Cf.Bl_Code,Ry_Name,Cf_Date,Cf_Time,Cf_YpMoney As Cf_Money,Ry_Sex,Ry_Address,Cf_Qr,datediff(yyyy,Ry_Csdate,getdate())as Ry_Age,Jb_Name,Ry_BlCode,Bxlb_Name,Bc_Name,Case  When Isnull(AutoCf_Code,'')<>'' then '长期医嘱' end as Cf_Lx From Bl_Cf,Zd_YyYs,Zd_YyKs,Zd_Bxlb,Bl left join Zd_YyBc on Bl.Bc_Code=Zd_YyBc.Bc_Code Where Bl.Bxlb_Code=Zd_Bxlb.Bxlb_Code and Bl_Cf.Ks_Code=Zd_YyKs.Ks_Code And Bl_Cf.Ys_Code=Zd_YyYs.Ys_Code And Bl_Cf.Bl_Code=Bl.Bl_Code And Cf_Qr='否' And Cf_Print='是' and Ly_Wc='是' And Bl_Cf.Yy_Code='" & HisVar.HisVar.WsyCode & "' And Bl_Cf.Yf_Code='" & HisVar.HisVar.YfCode & "' Order By Cf_Code"
        Else
            Str_Select = "Select Bl_Cf.Jsr_Code,Cf_Code,Bl_Cf.Ks_Code,Ks_Name,Bl_Cf.Ys_Code,Ys_Name,Bl_Cf.Bl_Code,Ry_Name,Cf_Date,Cf_Time,Cf_YpMoney As Cf_Money,Ry_Sex,Ry_Address,Cf_Qr,datediff(yyyy,Ry_Csdate,getdate())as Ry_Age,Jb_Name,Ry_BlCode,Bxlb_Name,Bc_Name,Case  When Isnull(AutoCf_Code,'')<>'' then '长期医嘱' end as Cf_Lx From Bl_Cf,Zd_YyYs,Zd_YyKs,Zd_Bxlb,Bl left join Zd_YyBc on Bl.Bc_Code=Zd_YyBc.Bc_Code Where Bl.Bxlb_Code=Zd_Bxlb.Bxlb_Code and Bl_Cf.Ks_Code=Zd_YyKs.Ks_Code And Bl_Cf.Ys_Code=Zd_YyYs.Ys_Code And Bl_Cf.Bl_Code=Bl.Bl_Code And Cf_Qr='否' And Cf_Print='是'  And Bl_Cf.Yy_Code='" & HisVar.HisVar.WsyCode & "' And Bl_Cf.Yf_Code='" & HisVar.HisVar.YfCode & "' Order By Cf_Code"
        End If

        HisVar.HisVar.Sqldal.QueryDt(My_DataSet, Str_Select, "主表", True)

        My_Table = My_DataSet.Tables("主表")
        My_Table.PrimaryKey = New DataColumn() {My_Table.Columns("Cf_Code")}

        'TDBGrid初始化
        C1TrueDBGrid1.SetDataBinding(My_DataSet, "主表", True)
        Zb_Cm = CType(BindingContext(C1TrueDBGrid1.DataSource, C1TrueDBGrid1.DataMember), CurrencyManager)

        T_Textbox.Text = ""
        Call F_Sum()
    End Sub

    Private Sub Tmp_ShowData()
        Dim Str_Select As String
        If HisPara.PublicConfig.ZyHsz = "是" Then
            Str_Select = "Select Bl_Cf.Jsr_Code,Cf_Code,Bl_Cf.Ks_Code,Ks_Name,Bl_Cf.Ys_Code,Ys_Name,Bl_Cf.Bl_Code,Ry_Name,Cf_Date,Cf_Time,Cf_YpMoney As Cf_Money,Ry_Sex,Ry_Address,Cf_Qr,Case when Isnull(AutoCf_Code,'')<>'' then '长期医嘱' else '临时医嘱' end as Cf_Lx From Bl,Bl_Cf,Zd_YyYs,Zd_YyKs Where Bl_Cf.Ks_Code=Zd_YyKs.Ks_Code And Bl_Cf.Ys_Code=Zd_YyYs.Ys_Code And Bl_Cf.Bl_Code=Bl.Bl_Code And Cf_Qr='否' And Cf_Print='是' and Ly_Wc='是' And Bl_Cf.Yy_Code='" & HisVar.HisVar.WsyCode & "' And Bl_Cf.Yf_Code='" & HisVar.HisVar.YfCode & "' Order By Cf_Code"
        Else
            Str_Select = "Select Bl_Cf.Jsr_Code,Cf_Code,Bl_Cf.Ks_Code,Ks_Name,Bl_Cf.Ys_Code,Ys_Name,Bl_Cf.Bl_Code,Ry_Name,Cf_Date,Cf_Time,Cf_YpMoney As Cf_Money,Ry_Sex,Ry_Address,Cf_Qr,Case when Isnull(AutoCf_Code,'')<>'' then '长期医嘱' else '临时医嘱' end as Cf_Lx From Bl,Bl_Cf,Zd_YyYs,Zd_YyKs Where Bl_Cf.Ks_Code=Zd_YyKs.Ks_Code And Bl_Cf.Ys_Code=Zd_YyYs.Ys_Code And Bl_Cf.Bl_Code=Bl.Bl_Code And Cf_Qr='否' And Cf_Print='是'  And Bl_Cf.Yy_Code='" & HisVar.HisVar.WsyCode & "' And Bl_Cf.Yf_Code='" & HisVar.HisVar.YfCode & "' Order By Cf_Code"
        End If

        HisVar.HisVar.Sqldal.QueryDt(My_DataSet, Str_Select, "主表", True)

        My_Table = My_DataSet.Tables("主表")
        My_Table.PrimaryKey = New DataColumn() {My_Table.Columns("Cf_Code")}
    End Sub

    Private Sub Show_Data1()
        Call Tmp_ShowData()
        Dim Str_Select As String
        If HisPara.PublicConfig.ZyHsz = "是" Then
            Str_Select = "select Sum(Cf_YpMoney) as Cf_Money,Count(Cf_Code) as Cf_Sl,Jsr_Name,Bl_Cf.Jsr_Code from Bl_Cf,Zd_YyJsr where Bl_Cf.Jsr_Code=Zd_YyJsr.Jsr_Code And Cf_Qr='否' And Cf_Print='是'  and Ly_Wc='是' And Bl_Cf.Yy_Code='" & HisVar.HisVar.WsyCode & "' And Bl_Cf.Yf_Code='" & HisVar.HisVar.YfCode & "' group by Bl_Cf.Jsr_Code,Jsr_Name"
        Else
            Str_Select = "select Sum(Cf_YpMoney) as Cf_Money,Count(Cf_Code) as Cf_Sl,Jsr_Name,Bl_Cf.Jsr_Code from Bl_Cf,Zd_YyJsr where Bl_Cf.Jsr_Code=Zd_YyJsr.Jsr_Code And Cf_Qr='否' And Cf_Print='是'   And Bl_Cf.Yy_Code='" & HisVar.HisVar.WsyCode & "' And Bl_Cf.Yf_Code='" & HisVar.HisVar.YfCode & "' group by Bl_Cf.Jsr_Code,Jsr_Name"
        End If

        HisVar.HisVar.Sqldal.QueryDt(My_DataSet, Str_Select, "主表1", True)
        My_Table = My_DataSet.Tables("主表1")


        'TDBGrid初始化
        C1TrueDBGrid2.SetDataBinding(My_DataSet, "主表1", True)
        Zb_Cm = CType(BindingContext(C1TrueDBGrid2.DataSource, C1TrueDBGrid2.DataMember), CurrencyManager)

        T_Textbox.Text = ""
        Call F_Sum()
    End Sub

    Private Sub Show_Data2()
        Call Tmp_ShowData()
        Dim Str_Select As String
        If HisPara.PublicConfig.ZyHsz = "是" Then
            Str_Select = "select Sum(Cf_YpMoney) as Cf_Money,Count(Cf_Code) as Cf_Sl,Ys_Name,Bl_Cf.Ys_Code from Bl_Cf,Zd_YyYs where Bl_Cf.Ys_Code=Zd_YyYs.Ys_Code And Cf_Qr='否' And Cf_Print='是'  and Ly_Wc='是' And Bl_Cf.Yy_Code='" & HisVar.HisVar.WsyCode & "' And Bl_Cf.Yf_Code='" & HisVar.HisVar.YfCode & "' group by Bl_Cf.Ys_Code,Ys_Name"
        Else
            Str_Select = "select Sum(Cf_YpMoney) as Cf_Money,Count(Cf_Code) as Cf_Sl,Ys_Name,Bl_Cf.Ys_Code from Bl_Cf,Zd_YyYs where Bl_Cf.Ys_Code=Zd_YyYs.Ys_Code And Cf_Qr='否' And Cf_Print='是'   And Bl_Cf.Yy_Code='" & HisVar.HisVar.WsyCode & "' And Bl_Cf.Yf_Code='" & HisVar.HisVar.YfCode & "' group by Bl_Cf.Ys_Code,Ys_Name"
        End If

        HisVar.HisVar.Sqldal.QueryDt(My_DataSet, Str_Select, "主表2", True)
        My_Table = My_DataSet.Tables("主表2")

        'TDBGrid初始化
        C1TrueDBGrid3.SetDataBinding(My_DataSet, "主表2", True)
        Zb_Cm = CType(BindingContext(C1TrueDBGrid3.DataSource, C1TrueDBGrid3.DataMember), CurrencyManager)

        T_Textbox.Text = ""
        Call F_Sum()
    End Sub

    Private Sub Show_Data3()
        Call Tmp_ShowData()
        Dim Str_Select As String
        If HisPara.PublicConfig.ZyHsz = "是" Then
            Str_Select = "select Sum(Cf_YpMoney) as Cf_Money,Count(Cf_Code) as Cf_Sl,Ks_Name,Bl_Cf.Ks_Code from Bl_Cf,Zd_YyKs where Bl_Cf.Ks_Code=Zd_YyKs.Ks_Code And Cf_Qr='否' And Cf_Print='是' and Ly_Wc='是' And Bl_Cf.Yy_Code='" & HisVar.HisVar.WsyCode & "' And Bl_Cf.Yf_Code='" & HisVar.HisVar.YfCode & "' group by Bl_Cf.Ks_Code,Ks_Name"
        Else
            Str_Select = "select Sum(Cf_YpMoney) as Cf_Money,Count(Cf_Code) as Cf_Sl,Ks_Name,Bl_Cf.Ks_Code from Bl_Cf,Zd_YyKs where Bl_Cf.Ks_Code=Zd_YyKs.Ks_Code And Cf_Qr='否' And Cf_Print='是'  And Bl_Cf.Yy_Code='" & HisVar.HisVar.WsyCode & "' And Bl_Cf.Yf_Code='" & HisVar.HisVar.YfCode & "' group by Bl_Cf.Ks_Code,Ks_Name"
        End If

        HisVar.HisVar.Sqldal.QueryDt(My_DataSet, Str_Select, "主表3", True)
        My_Table = My_DataSet.Tables("主表3")


        'TDBGrid初始化
        C1TrueDBGrid4.SetDataBinding(My_DataSet, "主表3", True)
        Zb_Cm = CType(BindingContext(C1TrueDBGrid4.DataSource, C1TrueDBGrid4.DataMember), CurrencyManager)

        T_Textbox.Text = ""
        Call F_Sum()
    End Sub

    '临时 and 长期医嘱
    Private Sub Show_Data4()
        Call Tmp_ShowData()
        Dim Str_Select As String
        '20200113 日 霍林河 药房发药是需要显示0元的费用
        'If HisPara.PublicConfig.ZyHsz = "是" Then
        '    Str_Select = "select * from( select Sum(Cf_YpMoney) as Cf_Money,Count(Cf_Code) as Cf_Sl,'长期医嘱' as Cf_Lx from Bl_Cf where Isnull(AutoCf_Code,'')<>'' And Cf_Qr='否' And Cf_Print='是' and Ly_Wc='是' And Bl_Cf.Yy_Code='" & HisVar.HisVar.WsyCode & "' And Bl_Cf.Yf_Code='" & HisVar.HisVar.YfCode & "' union all " &
        '                                        "select Sum(Cf_YpMoney) as Cf_Money,Count(Cf_Code) as Cf_Sl,'临时医嘱' as Cf_Lx from Bl_Cf where Isnull(AutoCf_Code,'')='' And Cf_Qr='否' And Cf_Print='是' and Ly_Wc='是' And Bl_Cf.Yy_Code='" & HisVar.HisVar.WsyCode & "' And Bl_Cf.Yf_Code='" & HisVar.HisVar.YfCode & "') A where Isnull(Cf_Money,0)<>0 "
        'Else
        '    Str_Select = "select * from( select Sum(Cf_YpMoney) as Cf_Money,Count(Cf_Code) as Cf_Sl,'长期医嘱' as Cf_Lx from Bl_Cf where Isnull(AutoCf_Code,'')<>'' And Cf_Qr='否' And Cf_Print='是'  And Bl_Cf.Yy_Code='" & HisVar.HisVar.WsyCode & "' And Bl_Cf.Yf_Code='" & HisVar.HisVar.YfCode & "' union all " &
        '                                        "select Sum(Cf_YpMoney) as Cf_Money,Count(Cf_Code) as Cf_Sl,'临时医嘱' as Cf_Lx from Bl_Cf where Isnull(AutoCf_Code,'')='' And Cf_Qr='否' And Cf_Print='是'  And Bl_Cf.Yy_Code='" & HisVar.HisVar.WsyCode & "' And Bl_Cf.Yf_Code='" & HisVar.HisVar.YfCode & "') A where Isnull(Cf_Money,0)<>0 "
        'End If
        If HisPara.PublicConfig.ZyHsz = "是" Then
            Str_Select = "select * from( select Sum(Cf_YpMoney) as Cf_Money,Count(Cf_Code) as Cf_Sl,'长期医嘱' as Cf_Lx from Bl_Cf where Isnull(AutoCf_Code,'')<>'' And Cf_Qr='否' And Cf_Print='是' and Ly_Wc='是' And Bl_Cf.Yy_Code='" & HisVar.HisVar.WsyCode & "' And Bl_Cf.Yf_Code='" & HisVar.HisVar.YfCode & "' union all " &
                                                "select Sum(Cf_YpMoney) as Cf_Money,Count(Cf_Code) as Cf_Sl,'临时医嘱' as Cf_Lx from Bl_Cf where Isnull(AutoCf_Code,'')='' And Cf_Qr='否' And Cf_Print='是' and Ly_Wc='是' And Bl_Cf.Yy_Code='" & HisVar.HisVar.WsyCode & "' And Bl_Cf.Yf_Code='" & HisVar.HisVar.YfCode & "') A "
        Else
            Str_Select = "select * from( select Sum(Cf_YpMoney) as Cf_Money,Count(Cf_Code) as Cf_Sl,'长期医嘱' as Cf_Lx from Bl_Cf where Isnull(AutoCf_Code,'')<>'' And Cf_Qr='否' And Cf_Print='是'  And Bl_Cf.Yy_Code='" & HisVar.HisVar.WsyCode & "' And Bl_Cf.Yf_Code='" & HisVar.HisVar.YfCode & "' union all " &
                                                "select Sum(Cf_YpMoney) as Cf_Money,Count(Cf_Code) as Cf_Sl,'临时医嘱' as Cf_Lx from Bl_Cf where Isnull(AutoCf_Code,'')='' And Cf_Qr='否' And Cf_Print='是'  And Bl_Cf.Yy_Code='" & HisVar.HisVar.WsyCode & "' And Bl_Cf.Yf_Code='" & HisVar.HisVar.YfCode & "') A "
        End If

        HisVar.HisVar.Sqldal.QueryDt(My_DataSet, Str_Select, "主表4", True)
        My_Table = My_DataSet.Tables("主表4")


        'TDBGrid初始化
        C1TrueDBGrid5.SetDataBinding(My_DataSet, "主表4", True)
        Zb_Cm = CType(BindingContext(C1TrueDBGrid5.DataSource, C1TrueDBGrid5.DataMember), CurrencyManager)

        T_Textbox.Text = ""
        Call F_Sum()
    End Sub


    Private Sub Show_Data5()
        Call Tmp_ShowData()
        Dim Str_Select As String
        If HisPara.PublicConfig.ZyHsz = "是" Then
            Str_Select = "select Sum(Cf_YpMoney) as Cf_Money,Count(Cf_Code) as Cf_Sl,'全部待发药品' cf from Bl_Cf,Zd_YyKs where Bl_Cf.Ks_Code=Zd_YyKs.Ks_Code And Cf_Qr='否' And Cf_Print='是' and Ly_Wc='是' And Bl_Cf.Yy_Code='" & HisVar.HisVar.WsyCode & "' And Bl_Cf.Yf_Code='" & HisVar.HisVar.YfCode & "'"
        Else
            Str_Select = "select Sum(Cf_YpMoney) as Cf_Money,Count(Cf_Code) as Cf_Sl,'全部待发药品' cf from Bl_Cf,Zd_YyKs where Bl_Cf.Ks_Code=Zd_YyKs.Ks_Code And Cf_Qr='否' And Cf_Print='是'                And Bl_Cf.Yy_Code='" & HisVar.HisVar.WsyCode & "' And Bl_Cf.Yf_Code='" & HisVar.HisVar.YfCode & "'"
        End If

        HisVar.HisVar.Sqldal.QueryDt(My_DataSet, Str_Select, "主表5", True)
        My_Table = My_DataSet.Tables("主表5")


        'TDBGrid初始化
        C1TrueDBGrid6.SetDataBinding(My_DataSet, "主表5", True)
        Zb_Cm = CType(BindingContext(C1TrueDBGrid6.DataSource, C1TrueDBGrid6.DataMember), CurrencyManager)

        T_Textbox.Text = ""
        Call F_Sum()
    End Sub


#End Region

#Region "控件动作"


    Private Sub T_Textbox_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles T_Textbox.TextChanged
        Dim My_View As New DataView
        My_View = Zb_Cm.List
        My_View.Sort = "Cf_Code"
        My_View.RowFilter = "Ry_Name Like '*" & Trim(T_Textbox.Text) & "*'"
    End Sub

    Private Sub C1TrueDBGrid1_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles C1TrueDBGrid1.KeyDown
        If e.KeyCode = Keys.Return Then Call P_Show_Mx("DBGrid")
    End Sub

    Private Sub C1TrueDBGrid1_MouseUp(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles C1TrueDBGrid1.MouseUp
        If e.Button = MouseButtons.Right Then Call P_Show_Mx("DBGrid")
    End Sub

#End Region

    Public Overrides Sub F_Sum()
        If My_Table.Rows.Count = 0 Then
            T_Label4.Text = "0元"
        Else
            Dim V_Sum As Double = 0
            For Each Me.Zb_Row In My_Table.Rows
                If Zb_Row.RowState <> DataRowState.Deleted Then
                    If Zb_Row.Item("Cf_Money") IsNot DBNull.Value Then
                        V_Sum = V_Sum + Zb_Row.Item("Cf_Money")
                    End If
                End If
            Next
            T_Label4.Text = Trim(Format(V_Sum, "###,###,###.00") + "元")
        End If
        T_Label.Text = "∑=" + C1TrueDBGrid1.Splits(0).Rows.Count.ToString
        T_Label4.Location = New System.Drawing.Point(Me.Width - T_Label4.Width - 7, 3)
        T_Label3.Location = New System.Drawing.Point(Me.Width - T_Label4.Width - T_Label3.Width - 7, 3)
        C1TrueDBGrid1.Select()
    End Sub

    Private Sub P_Show_Mx(ByVal V_Lb As String)
        Cf_Code_Set = ""
        '显示明细表
        If CheckBox1.Checked = True Then
            Dim FormName As String
            If C1TrueDBGrid1.RowCount = 0 Then Exit Sub

            Select Case C1Combo1.Text
                Case "按经手人汇总发药"
                    Zb_Row = Zb_Cm.List(C1TrueDBGrid2.Row).Row
                    For Each tmp_Row In My_DataSet.Tables("主表").Select("Jsr_Code='" & Zb_Row("Jsr_Code") & "'")
                        Cf_Code_Set = Cf_Code_Set & tmp_Row("Cf_Code") & ","
                    Next
                    FormName = C1TrueDBGrid2.Columns(0).Value
                Case "按医生汇总发药"
                    Zb_Row = Zb_Cm.List(C1TrueDBGrid3.Row).Row
                    For Each tmp_Row In My_DataSet.Tables("主表").Select("Ys_Code='" & Zb_Row("Ys_Code") & "'")
                        Cf_Code_Set = Cf_Code_Set & tmp_Row("Cf_Code") & ","
                    Next
                    FormName = C1TrueDBGrid3.Columns(0).Value
                Case "按科室汇总发药"
                    Zb_Row = Zb_Cm.List(C1TrueDBGrid4.Row).Row
                    For Each tmp_Row In My_DataSet.Tables("主表").Select("Ks_Code='" & Zb_Row("Ks_Code") & "'")
                        Cf_Code_Set = Cf_Code_Set & tmp_Row("Cf_Code") & ","
                    Next
                    FormName = C1TrueDBGrid4.Columns(0).Value
                Case "按长期/临时医嘱汇总发药"
                    Zb_Row = Zb_Cm.List(C1TrueDBGrid5.Row).Row
                    For Each tmp_Row In My_DataSet.Tables("主表").Select("Cf_Lx='" & Zb_Row("Cf_Lx") & "'")
                        Cf_Code_Set = Cf_Code_Set & tmp_Row("Cf_Code") & ","
                    Next
                    FormName = C1TrueDBGrid5.Columns(0).Value
                Case "全部待发药汇总"
                    Zb_Row = Zb_Cm.List(C1TrueDBGrid6.Row).Row
                    For Each tmp_Row In My_DataSet.Tables("主表").Rows
                        Cf_Code_Set = Cf_Code_Set & tmp_Row("Cf_Code") & ","
                    Next
                    FormName = C1TrueDBGrid6.Columns(0).Value
            End Select
            Cf_Code_Set = "(" & Mid(Cf_Code_Set, 1, Cf_Code_Set.Length - 1) & ")"


            Dim vform As New Zy_Fy3(Me, Zb_Row, C1Combo1.Text)
            vform.Cf_Code = Cf_Code_Set
            vform.Name = vform.Name & FormName
            BaseFunc.BaseFunc.addTabControl(vform, C1Combo1.Text & "-" & FormName)
        Else
            If C1TrueDBGrid1.RowCount = 0 Then Exit Sub
            Zb_Row = Zb_Cm.List(C1TrueDBGrid1.Row).Row
            Dim vform As New Zy_Fy2(Me, Zb_Row, My_Table, T_Label, "主窗体传参")
            vform.Name = vform.Name & Zb_Row("Cf_Code")
            BaseFunc.BaseFunc.addTabControl(vform, "住院发药明细-" & Zb_Row("Ry_Name"))
        End If
    End Sub

    Private Sub Comm2_Click(ByVal sender As System.Object, ByVal e As C1.Win.C1Command.ClickEventArgs) Handles Comm2.Click
        If CheckBox1.Checked = True Then
            T_Textbox.Text = ""
            T_Textbox.Enabled = False

            Select Case C1Combo1.Text
                Case "按经手人汇总发药"
                    C1TrueDBGrid2.Visible = True
                    C1TrueDBGrid1.Visible = False
                    C1TrueDBGrid3.Visible = False
                    C1TrueDBGrid4.Visible = False
                    C1TrueDBGrid5.Visible = False
                    C1TrueDBGrid6.Visible = False
                    Call Show_Data1()
                Case "按医生汇总发药"
                    C1TrueDBGrid3.Visible = True
                    C1TrueDBGrid1.Visible = False
                    C1TrueDBGrid2.Visible = False
                    C1TrueDBGrid4.Visible = False
                    C1TrueDBGrid5.Visible = False
                    C1TrueDBGrid6.Visible = False
                    Call Show_Data2()
                Case "按科室汇总发药"
                    C1TrueDBGrid4.Visible = True
                    C1TrueDBGrid1.Visible = False
                    C1TrueDBGrid2.Visible = False
                    C1TrueDBGrid3.Visible = False
                    C1TrueDBGrid5.Visible = False
                    C1TrueDBGrid6.Visible = False
                    Call Show_Data3()
                Case "按长期/临时医嘱汇总发药"
                    C1TrueDBGrid5.Visible = True
                    C1TrueDBGrid1.Visible = False
                    C1TrueDBGrid2.Visible = False
                    C1TrueDBGrid3.Visible = False
                    C1TrueDBGrid4.Visible = False
                    C1TrueDBGrid6.Visible = False
                    Call Show_Data4()
                Case "全部待发药汇总"
                    C1TrueDBGrid6.Visible = True
                    C1TrueDBGrid1.Visible = False
                    C1TrueDBGrid2.Visible = False
                    C1TrueDBGrid3.Visible = False
                    C1TrueDBGrid4.Visible = False
                    C1TrueDBGrid5.Visible = False
                    Call Show_Data5()
            End Select

        Else
            T_Textbox.Text = ""
            T_Textbox.Enabled = True
            C1TrueDBGrid1.Visible = True
            C1TrueDBGrid2.Visible = False
            C1TrueDBGrid3.Visible = False
            C1TrueDBGrid4.Visible = False
            C1TrueDBGrid5.Visible = False
            C1TrueDBGrid6.Visible = False
            Call Show_Data()
        End If
    End Sub

    Private Sub Comm3_Click(ByVal sender As System.Object, ByVal e As C1.Win.C1Command.ClickEventArgs) Handles Comm3.Click
        If C1TrueDBGrid1.Visible = False Then Exit Sub
        If C1TrueDBGrid1.RowCount = 0 Then Exit Sub
        If MsgBox("是否确认退回该处方？", MsgBoxStyle.OkCancel + MsgBoxStyle.DefaultButton1, "提示") = MsgBoxResult.Cancel Then Exit Sub

        If HisVar.HisVar.Sqldal.GetSingle("Select Count(*) from Bl_Cf where Cf_Qr_Date is not null and Cf_Code='" & C1TrueDBGrid1.Columns("Cf_Code").Value & "'") > 0 Then
            MsgBox("该处方已经发药,请更新状态！", MsgBoxStyle.Information, "提示")
            Exit Sub
        End If

        Dim arr As New ArrayList
        arr.Add("Update Bl_Cf Set Cf_Print='否', Ly_Wc='否' Where Cf_Code='" & C1TrueDBGrid1.Columns("Cf_Code").Value & "'")
        arr.Add("Delete from  Bl_CfLyd where Cf_Code='" & C1TrueDBGrid1.Columns("Cf_Code").Value & "'")
        HisVar.HisVar.Sqldal.ExecuteSqlTran(arr)

        Call Show_Data()
    End Sub

    Private Sub CheckBox1_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox1.CheckedChanged
        Comm2.PerformClick()
    End Sub

    Private Sub C1Combo1_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Combo1.TextChanged
        Comm2.PerformClick()
    End Sub

    Private Sub C1TrueDBGrid2_MouseUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles C1TrueDBGrid2.MouseUp
        If e.Button = MouseButtons.Right Then Call P_Show_Mx("DBGrid")
    End Sub

    Private Sub C1TrueDBGrid3_MouseUp(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles C1TrueDBGrid3.MouseUp
        If e.Button = MouseButtons.Right Then Call P_Show_Mx("DBGrid")
    End Sub

    Private Sub C1TrueDBGrid4_MouseUp(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles C1TrueDBGrid4.MouseUp
        If e.Button = MouseButtons.Right Then Call P_Show_Mx("DBGrid")
    End Sub

    Private Sub C1TrueDBGrid5_MouseUp(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles C1TrueDBGrid5.MouseUp
        If e.Button = MouseButtons.Right Then Call P_Show_Mx("DBGrid")
    End Sub

    Private Sub C1TrueDBGrid6_MouseUp(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles C1TrueDBGrid6.MouseUp
        If e.Button = MouseButtons.Right Then Call P_Show_Mx("DBGrid")
    End Sub
End Class