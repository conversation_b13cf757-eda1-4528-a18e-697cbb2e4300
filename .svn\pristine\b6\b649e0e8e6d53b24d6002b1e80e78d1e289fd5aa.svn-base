﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class Zd_MzFp15
    Inherits HisControl.BaseChild

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(Zd_MzFp15))
        Me.Label4 = New System.Windows.Forms.Label
        Me.Label2 = New System.Windows.Forms.Label
        Me.Label1 = New System.Windows.Forms.Label
        Me.L_Dl_Code = New System.Windows.Forms.Label
        Me.C1TextBox2 = New C1.Win.C1Input.C1TextBox
        Me.C1TextBox1 = New C1.Win.C1Input.C1TextBox
        Me.T_Line1 = New System.Windows.Forms.Label
        Me.C1Holder1 = New C1.Win.C1Command.C1CommandHolder
        Me.Move1 = New C1.Win.C1Command.C1Command
        Me.Move2 = New C1.Win.C1Command.C1Command
        Me.Move3 = New C1.Win.C1Command.C1Command
        Me.Move4 = New C1.Win.C1Command.C1Command
        Me.Move5 = New C1.Win.C1Command.C1Command
        Me.Control1 = New C1.Win.C1Command.C1CommandControl
        Me.T_Label1 = New C1.Win.C1Input.C1Label
        Me.Control2 = New C1.Win.C1Command.C1CommandControl
        Me.T_Label2 = New C1.Win.C1Input.C1Label
        Me.Control3 = New C1.Win.C1Command.C1CommandControl
        Me.T_Label3 = New C1.Win.C1Input.C1Label
        Me.T_Line2 = New System.Windows.Forms.Label
        Me.T_Line3 = New System.Windows.Forms.Label
        Me.Panel1 = New System.Windows.Forms.Panel
        Me.ToolBar1 = New C1.Win.C1Command.C1ToolBar
        Me.Link6 = New C1.Win.C1Command.C1CommandLink
        Me.Link1 = New C1.Win.C1Command.C1CommandLink
        Me.Link2 = New C1.Win.C1Command.C1CommandLink
        Me.Link7 = New C1.Win.C1Command.C1CommandLink
        Me.Link3 = New C1.Win.C1Command.C1CommandLink
        Me.Link4 = New C1.Win.C1Command.C1CommandLink
        Me.Link5 = New C1.Win.C1Command.C1CommandLink
        Me.Link8 = New C1.Win.C1Command.C1CommandLink
        Me.Comm2 = New System.Windows.Forms.Button
        Me.Comm1 = New System.Windows.Forms.Button
        Me.ToolTip1 = New System.Windows.Forms.ToolTip(Me.components)
        Me.Label3 = New System.Windows.Forms.Label
        Me.L_Dl_Jc = New System.Windows.Forms.Label
        CType(Me.C1TextBox2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1TextBox1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1Holder1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T_Label1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T_Label2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T_Label3, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Panel1.SuspendLayout()
        Me.ToolBar1.SuspendLayout()
        Me.SuspendLayout()
        '
        'Label4
        '
        Me.Label4.AutoSize = True
        Me.Label4.Location = New System.Drawing.Point(6, 134)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(53, 12)
        Me.Label4.TabIndex = 139
        Me.Label4.Text = "备    注"
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.Location = New System.Drawing.Point(6, 53)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(53, 12)
        Me.Label2.TabIndex = 137
        Me.Label2.Text = "类别名称"
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.ForeColor = System.Drawing.Color.DarkRed
        Me.Label1.Location = New System.Drawing.Point(6, 12)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(53, 12)
        Me.Label1.TabIndex = 136
        Me.Label1.Text = "类别编码"
        '
        'L_Dl_Code
        '
        Me.L_Dl_Code.BackColor = System.Drawing.SystemColors.Info
        Me.L_Dl_Code.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.L_Dl_Code.Location = New System.Drawing.Point(62, 10)
        Me.L_Dl_Code.Name = "L_Dl_Code"
        Me.L_Dl_Code.Size = New System.Drawing.Size(141, 17)
        Me.L_Dl_Code.TabIndex = 135
        Me.L_Dl_Code.TextAlign = System.Drawing.ContentAlignment.BottomCenter
        '
        'C1TextBox2
        '
        Me.C1TextBox2.Location = New System.Drawing.Point(62, 81)
        Me.C1TextBox2.Multiline = True
        Me.C1TextBox2.Name = "C1TextBox2"
        Me.C1TextBox2.ScrollBars = System.Windows.Forms.ScrollBars.Vertical
        Me.C1TextBox2.Size = New System.Drawing.Size(310, 128)
        Me.C1TextBox2.TabIndex = 2
        Me.C1TextBox2.Tag = Nothing
        Me.C1TextBox2.TextDetached = True
        '
        'C1TextBox1
        '
        Me.C1TextBox1.Location = New System.Drawing.Point(62, 49)
        Me.C1TextBox1.Name = "C1TextBox1"
        Me.C1TextBox1.Size = New System.Drawing.Size(141, 21)
        Me.C1TextBox1.TabIndex = 0
        Me.C1TextBox1.Tag = Nothing
        Me.C1TextBox1.TextDetached = True
        Me.C1TextBox1.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        '
        'T_Line1
        '
        Me.T_Line1.BackColor = System.Drawing.SystemColors.Control
        Me.T_Line1.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.T_Line1.Location = New System.Drawing.Point(-13, 37)
        Me.T_Line1.Name = "T_Line1"
        Me.T_Line1.Size = New System.Drawing.Size(450, 2)
        Me.T_Line1.TabIndex = 133
        Me.T_Line1.Text = "Label1"
        '
        'C1Holder1
        '
        Me.C1Holder1.Commands.Add(Me.Move1)
        Me.C1Holder1.Commands.Add(Me.Move2)
        Me.C1Holder1.Commands.Add(Me.Move3)
        Me.C1Holder1.Commands.Add(Me.Move4)
        Me.C1Holder1.Commands.Add(Me.Move5)
        Me.C1Holder1.Commands.Add(Me.Control1)
        Me.C1Holder1.Commands.Add(Me.Control2)
        Me.C1Holder1.Commands.Add(Me.Control3)
        Me.C1Holder1.Owner = Me
        '
        'Move1
        '
        Me.Move1.Image = CType(resources.GetObject("Move1.Image"), System.Drawing.Image)
        Me.Move1.Name = "Move1"
        Me.Move1.Shortcut = System.Windows.Forms.Shortcut.F2
        Me.Move1.Text = "最前"
        Me.Move1.ToolTipText = "移到最前记录"
        '
        'Move2
        '
        Me.Move2.Image = CType(resources.GetObject("Move2.Image"), System.Drawing.Image)
        Me.Move2.Name = "Move2"
        Me.Move2.Shortcut = System.Windows.Forms.Shortcut.F3
        Me.Move2.Text = "上移"
        Me.Move2.ToolTipText = "上移记录"
        '
        'Move3
        '
        Me.Move3.Image = CType(resources.GetObject("Move3.Image"), System.Drawing.Image)
        Me.Move3.Name = "Move3"
        Me.Move3.Shortcut = System.Windows.Forms.Shortcut.F4
        Me.Move3.Text = "下移"
        Me.Move3.ToolTipText = "下移记录"
        '
        'Move4
        '
        Me.Move4.Image = CType(resources.GetObject("Move4.Image"), System.Drawing.Image)
        Me.Move4.Name = "Move4"
        Me.Move4.Shortcut = System.Windows.Forms.Shortcut.F5
        Me.Move4.Text = "最后"
        Me.Move4.ToolTipText = "移至最后记录"
        '
        'Move5
        '
        Me.Move5.Image = CType(resources.GetObject("Move5.Image"), System.Drawing.Image)
        Me.Move5.Name = "Move5"
        Me.Move5.Shortcut = System.Windows.Forms.Shortcut.F6
        Me.Move5.Text = "新增"
        '
        'Control1
        '
        Me.Control1.Control = Me.T_Label1
        Me.Control1.HotFrame = C1.Win.C1Command.HotFrameEnum.None
        Me.Control1.Name = "Control1"
        Me.Control1.ShowShortcut = False
        Me.Control1.ShowTextAsToolTip = False
        '
        'T_Label1
        '
        Me.T_Label1.AutoSize = True
        Me.T_Label1.BackColor = System.Drawing.SystemColors.Control
        Me.T_Label1.ForeColor = System.Drawing.Color.Maroon
        Me.T_Label1.Location = New System.Drawing.Point(1, 5)
        Me.T_Label1.Name = "T_Label1"
        Me.T_Label1.Size = New System.Drawing.Size(29, 12)
        Me.T_Label1.TabIndex = 42
        Me.T_Label1.Tag = Nothing
        Me.T_Label1.Text = "记录"
        Me.T_Label1.TextAlign = System.Drawing.ContentAlignment.BottomLeft
        Me.T_Label1.TextDetached = True
        '
        'Control2
        '
        Me.Control2.Control = Me.T_Label2
        Me.Control2.HotFrame = C1.Win.C1Command.HotFrameEnum.None
        Me.Control2.Name = "Control2"
        Me.Control2.ShowShortcut = False
        Me.Control2.ShowTextAsToolTip = False
        '
        'T_Label2
        '
        Me.T_Label2.AutoSize = True
        Me.T_Label2.BackColor = System.Drawing.SystemColors.Control
        Me.T_Label2.ForeColor = System.Drawing.Color.Maroon
        Me.T_Label2.Location = New System.Drawing.Point(80, 5)
        Me.T_Label2.Name = "T_Label2"
        Me.T_Label2.Size = New System.Drawing.Size(23, 12)
        Me.T_Label2.TabIndex = 43
        Me.T_Label2.Tag = Nothing
        Me.T_Label2.Text = "(1)"
        Me.T_Label2.TextAlign = System.Drawing.ContentAlignment.BottomCenter
        Me.T_Label2.TextDetached = True
        Me.T_Label2.TrimStart = True
        '
        'Control3
        '
        Me.Control3.Control = Me.T_Label3
        Me.Control3.HotFrame = C1.Win.C1Command.HotFrameEnum.None
        Me.Control3.Name = "Control3"
        Me.Control3.ShowShortcut = False
        Me.Control3.ShowTextAsToolTip = False
        '
        'T_Label3
        '
        Me.T_Label3.AutoSize = True
        Me.T_Label3.BackColor = System.Drawing.SystemColors.Control
        Me.T_Label3.ForeColor = System.Drawing.Color.Maroon
        Me.T_Label3.Location = New System.Drawing.Point(179, 5)
        Me.T_Label3.Name = "T_Label3"
        Me.T_Label3.Size = New System.Drawing.Size(35, 12)
        Me.T_Label3.TabIndex = 41
        Me.T_Label3.Tag = Nothing
        Me.T_Label3.Text = "Σ=1 "
        Me.T_Label3.TextAlign = System.Drawing.ContentAlignment.BottomLeft
        Me.T_Label3.TextDetached = True
        '
        'T_Line2
        '
        Me.T_Line2.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.T_Line2.Location = New System.Drawing.Point(251, 1)
        Me.T_Line2.Name = "T_Line2"
        Me.T_Line2.Size = New System.Drawing.Size(2, 28)
        Me.T_Line2.TabIndex = 0
        Me.T_Line2.Text = "Label1"
        '
        'T_Line3
        '
        Me.T_Line3.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.T_Line3.Dock = System.Windows.Forms.DockStyle.Top
        Me.T_Line3.Location = New System.Drawing.Point(0, 0)
        Me.T_Line3.Name = "T_Line3"
        Me.T_Line3.Size = New System.Drawing.Size(387, 2)
        Me.T_Line3.TabIndex = 101
        Me.T_Line3.Text = "Label2"
        '
        'Panel1
        '
        Me.Panel1.Controls.Add(Me.T_Line2)
        Me.Panel1.Controls.Add(Me.T_Line3)
        Me.Panel1.Controls.Add(Me.ToolBar1)
        Me.Panel1.Controls.Add(Me.Comm2)
        Me.Panel1.Controls.Add(Me.Comm1)
        Me.Panel1.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.Panel1.Location = New System.Drawing.Point(0, 226)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(387, 27)
        Me.Panel1.TabIndex = 140
        '
        'ToolBar1
        '
        Me.ToolBar1.AccessibleName = "Tool Bar"
        Me.ToolBar1.CommandHolder = Me.C1Holder1
        Me.ToolBar1.CommandLinks.AddRange(New C1.Win.C1Command.C1CommandLink() {Me.Link6, Me.Link1, Me.Link2, Me.Link7, Me.Link3, Me.Link4, Me.Link5, Me.Link8})
        Me.ToolBar1.Controls.Add(Me.T_Label2)
        Me.ToolBar1.Controls.Add(Me.T_Label3)
        Me.ToolBar1.Controls.Add(Me.T_Label1)
        Me.ToolBar1.Location = New System.Drawing.Point(8, 3)
        Me.ToolBar1.MinButtonSize = 22
        Me.ToolBar1.Movable = False
        Me.ToolBar1.Name = "ToolBar1"
        Me.ToolBar1.Size = New System.Drawing.Size(215, 22)
        Me.ToolBar1.Text = "C1ToolBar2"
        Me.ToolBar1.VisualStyle = C1.Win.C1Command.VisualStyle.Classic
        Me.ToolBar1.VisualStyleBase = C1.Win.C1Command.VisualStyle.Classic
        '
        'Link6
        '
        Me.Link6.Command = Me.Control1
        '
        'Link1
        '
        Me.Link1.Command = Me.Move1
        Me.Link1.Delimiter = True
        Me.Link1.SortOrder = 1
        '
        'Link2
        '
        Me.Link2.Command = Me.Move2
        Me.Link2.SortOrder = 2
        '
        'Link7
        '
        Me.Link7.Command = Me.Control2
        Me.Link7.SortOrder = 3
        '
        'Link3
        '
        Me.Link3.Command = Me.Move3
        Me.Link3.SortOrder = 4
        '
        'Link4
        '
        Me.Link4.Command = Me.Move4
        Me.Link4.SortOrder = 5
        '
        'Link5
        '
        Me.Link5.Command = Me.Move5
        Me.Link5.Delimiter = True
        Me.Link5.SortOrder = 6
        Me.Link5.Text = "新增"
        Me.Link5.ToolTipText = "新增记录"
        '
        'Link8
        '
        Me.Link8.Command = Me.Control3
        Me.Link8.Delimiter = True
        Me.Link8.SortOrder = 7
        Me.Link8.Text = "New Command"
        '
        'Comm2
        '
        Me.Comm2.DialogResult = DialogResult.Cancel
        Me.Comm2.Location = New System.Drawing.Point(322, 3)
        Me.Comm2.Name = "Comm2"
        Me.Comm2.Size = New System.Drawing.Size(50, 24)
        Me.Comm2.TabIndex = 3
        Me.Comm2.Tag = "取消"
        Me.Comm2.Text = "取消             &C"
        Me.ToolTip1.SetToolTip(Me.Comm2, "取消存盘并退出(&C)")
        Me.Comm2.UseVisualStyleBackColor = False
        '
        'Comm1
        '
        Me.Comm1.Location = New System.Drawing.Point(270, 3)
        Me.Comm1.Name = "Comm1"
        Me.Comm1.Size = New System.Drawing.Size(50, 24)
        Me.Comm1.TabIndex = 1
        Me.Comm1.Tag = "保存"
        Me.Comm1.Text = "保存           &S"
        Me.ToolTip1.SetToolTip(Me.Comm1, "数据存盘(&S)")
        Me.Comm1.UseVisualStyleBackColor = False
        '
        'Label3
        '
        Me.Label3.AutoSize = True
        Me.Label3.Location = New System.Drawing.Point(209, 53)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(53, 12)
        Me.Label3.TabIndex = 141
        Me.Label3.Text = "类别简称"
        '
        'L_Dl_Jc
        '
        Me.L_Dl_Jc.BackColor = System.Drawing.SystemColors.Info
        Me.L_Dl_Jc.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.L_Dl_Jc.Location = New System.Drawing.Point(263, 50)
        Me.L_Dl_Jc.Name = "L_Dl_Jc"
        Me.L_Dl_Jc.Size = New System.Drawing.Size(109, 18)
        Me.L_Dl_Jc.TabIndex = 142
        Me.L_Dl_Jc.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'Zd_MzFp15
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.CancelButton = Me.Comm2
        Me.ClientSize = New System.Drawing.Size(387, 253)
        Me.Controls.Add(Me.L_Dl_Jc)
        Me.Controls.Add(Me.Label3)
        Me.Controls.Add(Me.Panel1)
        Me.Controls.Add(Me.Label4)
        Me.Controls.Add(Me.Label2)
        Me.Controls.Add(Me.Label1)
        Me.Controls.Add(Me.L_Dl_Code)
        Me.Controls.Add(Me.C1TextBox2)
        Me.Controls.Add(Me.C1TextBox1)
        Me.Controls.Add(Me.T_Line1)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle
        Me.Icon = CType(resources.GetObject("$this.Icon"), System.Drawing.Icon)
        Me.KeyPreview = True
        Me.MaximizeBox = False
        Me.Name = "Zd_MzFp15"
        Me.ShowInTaskbar = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "项目类别明细"
        CType(Me.C1TextBox2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1TextBox1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1Holder1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T_Label1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T_Label2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T_Label3, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Panel1.ResumeLayout(False)
        Me.ToolBar1.ResumeLayout(False)
        Me.ToolBar1.PerformLayout()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents Label4 As System.Windows.Forms.Label
    Friend WithEvents Label2 As System.Windows.Forms.Label
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents L_Dl_Code As System.Windows.Forms.Label
    Friend WithEvents C1TextBox2 As C1.Win.C1Input.C1TextBox
    Friend WithEvents C1TextBox1 As C1.Win.C1Input.C1TextBox
    Friend WithEvents T_Line1 As System.Windows.Forms.Label
    Friend WithEvents C1Holder1 As C1.Win.C1Command.C1CommandHolder
    Friend WithEvents Move1 As C1.Win.C1Command.C1Command
    Friend WithEvents Move2 As C1.Win.C1Command.C1Command
    Friend WithEvents Move3 As C1.Win.C1Command.C1Command
    Friend WithEvents Move4 As C1.Win.C1Command.C1Command
    Friend WithEvents Move5 As C1.Win.C1Command.C1Command
    Friend WithEvents Control1 As C1.Win.C1Command.C1CommandControl
    Friend WithEvents T_Label1 As C1.Win.C1Input.C1Label
    Friend WithEvents Control2 As C1.Win.C1Command.C1CommandControl
    Friend WithEvents T_Label2 As C1.Win.C1Input.C1Label
    Friend WithEvents Control3 As C1.Win.C1Command.C1CommandControl
    Friend WithEvents T_Label3 As C1.Win.C1Input.C1Label
    Friend WithEvents T_Line2 As System.Windows.Forms.Label
    Friend WithEvents T_Line3 As System.Windows.Forms.Label
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents ToolBar1 As C1.Win.C1Command.C1ToolBar
    Friend WithEvents Link6 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link1 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link2 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link7 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link3 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link4 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link5 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link8 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Comm2 As System.Windows.Forms.Button
    Friend WithEvents ToolTip1 As System.Windows.Forms.ToolTip
    Friend WithEvents Comm1 As System.Windows.Forms.Button
    Friend WithEvents Label3 As System.Windows.Forms.Label
    Friend WithEvents L_Dl_Jc As System.Windows.Forms.Label
End Class
