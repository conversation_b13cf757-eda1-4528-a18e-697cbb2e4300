﻿/**  版本信息模板在安装目录下，可自行修改。
* M_V_YyBc.cs
*
* 功 能： N/A
* 类 名： M_V_YyBc
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/8/29 1:40:24   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace ModelOld
{
	/// <summary>
	/// M_V_YyBc:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class M_V_YyBc
	{
		public M_V_YyBc()
		{}
		#region Model
		private string _bc_code;
		private string _yy_code;
		private string _bc_name;
		private string _bc_jc;
		private string _bc_memo;
		private bool _bc_use;
		private string _bq_name;
		private string _bq_code;
		/// <summary>
		/// 
		/// </summary>
		public string Bc_Code
		{
			set{ _bc_code=value;}
			get{return _bc_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Yy_Code
		{
			set{ _yy_code=value;}
			get{return _yy_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Bc_Name
		{
			set{ _bc_name=value;}
			get{return _bc_name;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Bc_Jc
		{
			set{ _bc_jc=value;}
			get{return _bc_jc;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Bc_Memo
		{
			set{ _bc_memo=value;}
			get{return _bc_memo;}
		}
		/// <summary>
		/// 
		/// </summary>
		public bool Bc_Use
		{
			set{ _bc_use=value;}
			get{return _bc_use;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Bq_Name
		{
			set{ _bq_name=value;}
			get{return _bq_name;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Bq_Code
		{
			set{ _bq_code=value;}
			get{return _bq_code;}
		}
		#endregion Model

	}
}

