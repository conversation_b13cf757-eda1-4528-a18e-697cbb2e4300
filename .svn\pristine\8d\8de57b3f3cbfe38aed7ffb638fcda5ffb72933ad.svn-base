﻿/**  版本信息模板在安装目录下，可自行修改。
* M_Materials_Dict.cs
*
* 功 能： N/A
* 类 名： M_Materials_Dict
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016-12-03 15:06:33   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace ModelOld
{
	/// <summary>
	/// 物资字典
	/// </summary>
	[Serializable]
	public partial class M_Materials_Dict
	{
		public M_Materials_Dict()
		{}
		#region Model
		private string _materials_code;
		private string _materials_name;
		private string _materials_py;
		private string _materials_wb;
		private string _materials_spec;
		private string _pack_unit;
		private int? _convert_ratio;
		private string _bulk_unit;
		private string _matemanu_code;
		private string _matemanu_name;
		private string _class_code;
		private bool _isuse;
		private string _materials_memo;
		/// <summary>
		/// 物资编码
		/// </summary>
		public string Materials_Code
		{
			set{ _materials_code=value;}
			get{return _materials_code;}
		}
		/// <summary>
		/// 名称
		/// </summary>
		public string Materials_Name
		{
			set{ _materials_name=value;}
			get{return _materials_name;}
		}
		/// <summary>
		/// 拼音
		/// </summary>
		public string Materials_Py
		{
			set{ _materials_py=value;}
			get{return _materials_py;}
		}
		/// <summary>
		/// 五笔
		/// </summary>
		public string Materials_Wb
		{
			set{ _materials_wb=value;}
			get{return _materials_wb;}
		}
		/// <summary>
		/// 规格
		/// </summary>
		public string Materials_Spec
		{
			set{ _materials_spec=value;}
			get{return _materials_spec;}
		}
		/// <summary>
		/// 包装单位
		/// </summary>
		public string Pack_Unit
		{
			set{ _pack_unit=value;}
			get{return _pack_unit;}
		}
		/// <summary>
		/// 拆分比例
		/// </summary>
		public int? Convert_Ratio
		{
			set{ _convert_ratio=value;}
			get{return _convert_ratio;}
		}
		/// <summary>
		/// 散装单位
		/// </summary>
		public string Bulk_Unit
		{
			set{ _bulk_unit=value;}
			get{return _bulk_unit;}
		}
		/// <summary>
		/// 生产厂家编码
		/// </summary>
		public string MateManu_Code
		{
			set{ _matemanu_code=value;}
			get{return _matemanu_code;}
		}
		/// <summary>
		/// 名称
		/// </summary>
		public string MateManu_Name
		{
			set{ _matemanu_name=value;}
			get{return _matemanu_name;}
		}
		/// <summary>
		/// 物资类别编码
		/// </summary>
		public string Class_Code
		{
			set{ _class_code=value;}
			get{return _class_code;}
		}
		/// <summary>
		/// 真为启用，假为停用，默认值为真
		/// </summary>
		public bool IsUse
		{
			set{ _isuse=value;}
			get{return _isuse;}
		}
		/// <summary>
		/// 备注
		/// </summary>
		public string Materials_Memo
		{
			set{ _materials_memo=value;}
			get{return _materials_memo;}
		}
		#endregion Model

	}
}

