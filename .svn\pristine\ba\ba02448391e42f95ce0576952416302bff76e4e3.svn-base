﻿/**  版本信息模板在安装目录下，可自行修改。
* M_Materials_Buy_In2.cs
*
* 功 能： N/A
* 类 名： M_Materials_Buy_In2
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/11/29 13:55:41   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace ModelOld
{
	/// <summary>
	/// 物资采购入库从表
	/// </summary>
	[Serializable]
	public partial class M_Materials_Buy_In2
	{
		public M_Materials_Buy_In2()
		{}
		#region Model
		private string _m_buy_code;
		private string _materials_code;
        private string _materials_name;
		private string _materialsstock_code;
		private string _m_buy_detail_code;
		private string _materialslot;
		private DateTime? _materialsexpirydate;
		private decimal? _m_buy_num;
		private decimal? _m_buy_writeoffno;
		private decimal? _m_buy_realno;
		private decimal? _m_buyin_num;
		private decimal? _m_buyin_writeoffno;
		private decimal? _m_buyin_realno;
		private decimal? _m_buy_price;
        private decimal? _m_buyIn_price;
		private decimal? _m_buy_money;
		private decimal? _m_buy_realmoney;
		private string _pack_unit;
		private int? _convert_ratio;
		private string _bulk_unit;
		private string _m_buydetail_memo;
		/// <summary>
		/// yyMMdd+5位流水号
		/// </summary>
		public string M_Buy_Code
		{
			set{ _m_buy_code=value;}
			get{return _m_buy_code;}
		}
		/// <summary>
		/// 物资编码
		/// </summary>
		public string Materials_Code
		{
			set{ _materials_code=value;}
			get{return _materials_code;}
		}
        /// <summary>
        /// 物资名称
        /// </summary>
        public string Materials_Name
        {
            set { _materials_name = value; }
            get { return _materials_name; }
        }
		/// <summary>
		/// 物资编码+6位流水号
		/// </summary>
		public string MaterialsStock_Code
		{
			set{ _materialsstock_code=value;}
			get{return _materialsstock_code;}
		}
		/// <summary>
		/// 入库明细编码
		/// </summary>
		public string M_Buy_Detail_Code
		{
			set{ _m_buy_detail_code=value;}
			get{return _m_buy_detail_code;}
		}
		/// <summary>
		/// 物资批号
		/// </summary>
		public string MaterialsLot
		{
			set{ _materialslot=value;}
			get{return _materialslot;}
		}
		/// <summary>
		/// 物资有效期
		/// </summary>
		public DateTime? MaterialsExpiryDate
		{
			set{ _materialsexpirydate=value;}
			get{return _materialsexpirydate;}
		}
		/// <summary>
		/// 包装数量
		/// </summary>
		public decimal? M_Buy_Num
		{
			set{ _m_buy_num=value;}
			get{return _m_buy_num;}
		}
		/// <summary>
		/// 购买冲销数量
		/// </summary>
		public decimal? M_Buy_WriteoffNo
		{
			set{ _m_buy_writeoffno=value;}
			get{return _m_buy_writeoffno;}
		}
		/// <summary>
		/// 购买实际数量
		/// </summary>
		public decimal? M_Buy_RealNo
		{
			set{ _m_buy_realno=value;}
			get{return _m_buy_realno;}
		}
		/// <summary>
		/// 入库数量=包装数量*拆分比例
		/// </summary>
		public decimal? M_BuyIn_Num
		{
			set{ _m_buyin_num=value;}
			get{return _m_buyin_num;}
		}
		/// <summary>
		/// 入库冲销
		/// </summary>
		public decimal? M_BuyIn_WriteoffNo
		{
			set{ _m_buyin_writeoffno=value;}
			get{return _m_buyin_writeoffno;}
		}
		/// <summary>
		/// 入库实际数量
		/// </summary>
		public decimal? M_BuyIn_RealNo
		{
			set{ _m_buyin_realno=value;}
			get{return _m_buyin_realno;}
		}
		/// <summary>
		/// 采购单价
		/// </summary>
		public decimal? M_Buy_Price
		{
			set{ _m_buy_price=value;}
			get{return _m_buy_price;}
		}
        /// <summary>
        /// 入库单价
        /// </summary>
        public decimal? M_BuyIn_Price
        {
            set { _m_buyIn_price = value; }
            get { return _m_buyIn_price; }
        }
		/// <summary>
		/// 采购金额
		/// </summary>
		public decimal? M_Buy_Money
		{
			set{ _m_buy_money=value;}
			get{return _m_buy_money;}
		}
		/// <summary>
		/// 实际采购金额
		/// </summary>
		public decimal? M_Buy_RealMoney
		{
			set{ _m_buy_realmoney=value;}
			get{return _m_buy_realmoney;}
		}
		/// <summary>
		/// 包装单位
		/// </summary>
		public string Pack_Unit
		{
			set{ _pack_unit=value;}
			get{return _pack_unit;}
		}
		/// <summary>
		/// 拆分比例
		/// </summary>
		public int? Convert_Ratio
		{
			set{ _convert_ratio=value;}
			get{return _convert_ratio;}
		}
		/// <summary>
		/// 散装单位
		/// </summary>
		public string Bulk_Unit
		{
			set{ _bulk_unit=value;}
			get{return _bulk_unit;}
		}
		/// <summary>
		/// 采购备注
		/// </summary>
		public string M_BuyDetail_Memo
		{
			set{ _m_buydetail_memo=value;}
			get{return _m_buydetail_memo;}
		}
		#endregion Model

	}
}

