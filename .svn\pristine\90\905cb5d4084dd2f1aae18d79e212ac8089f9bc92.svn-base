﻿Imports System.Data.SqlClient
Imports System.Text.RegularExpressions
Imports System.Runtime.InteropServices
Imports System
Imports System.Data
Imports System.Drawing
Imports System.Windows.Forms
Imports Common
Imports DbProviderFactory
Imports ZTHisInsuranceAPI
Imports ZTHisSysManage


Public Class F_Login

    '    Public My_Dataset As New DataSet
    '    Dim V_DatabaseService As String '中心库连接
    '    Dim V_DzblPrintService As String '电子病历打印服务
    '    Dim V_JkkService As String '健康卡service
    Dim V_InputLanguage As String
    Public B_InputLanguage As Boolean
    Dim thread As Threading.Thread
    Dim V_HKey As String = "HKEY_CURRENT_USER\中软智通"             '添加注册表用户

    '    Dim V_NhIp As String
    '    Dim V_NhSid As String
    '    Dim V_NhUid As String
    '    Dim V_NhPwd As String
    '
    '    Dim Xy_NhIp As String
    '    Dim Xy_NhSid As String
    '    Dim Xy_NhUid As String
    '    Dim Xy_NhPwd As String
    '    Dim V_Middle_DbName As String



    Private Sub F_Login_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        C1TextBox1.ReadOnly = True
        C1TextBox2.ReadOnly = True
        C1Button1.Enabled = False
        CheckBox1.Enabled = False
        CheckBox2.Enabled = False

        If My.Settings.DebugLb <> "正式" And My.Settings.DebugLb <> "调试" Then
            C1Label3.Visible = False
            C1Label4.Visible = False
            Me.BackgroundImage = MyResources.C_Resources.getimage("_3")
        Else
            C1Label3.Visible = True
            C1Label4.Visible = True
            Me.BackgroundImage = MyResources.C_Resources.getimage("_2")
        End If

        thread = New Threading.Thread(AddressOf check)
        thread.IsBackground = True
        thread.Start()


        thread = New Threading.Thread(AddressOf GetMachineCode)
        thread.IsBackground = True
        thread.Start()
    End Sub


    Delegate Sub SetValueCallback(ByVal str As String)
    Delegate Sub MyInvoke()


    Public Sub SetText(ByVal str As String)
        If C1Label6.InvokeRequired = True Then
            Dim del As New SetValueCallback(AddressOf SetText)
            C1Label6.Invoke(del, str)
        Else
            Application.DoEvents()
            Me.C1Label6.Text = str
            Me.C1Label6.Refresh()
        End If
    End Sub


    Private Sub check()
        If Me.InvokeRequired = True Then
            Dim del As New MyInvoke(AddressOf check)
            Me.Invoke(del)
        Else

            Try
                SetText("正在初始化界面...")
                Dim V_Str As String = Application.StartupPath

                SetText("正在初始化医院参数...")

                If My.Application.IsNetworkDeployed Then
                    HisVar.HisVar.ConfPath = HisVar.HisVar.Parapath
                    If System.IO.File.Exists(HisVar.HisVar.ConfPath & "\Conf.dat") Then

                    Else
                        System.IO.File.Copy(Application.StartupPath & "\Conf\Conf.dat", HisVar.HisVar.ConfPath & "\Conf.dat")
                    End If
                Else
                    HisVar.HisVar.ConfPath = Application.StartupPath & "\Conf\"
                End If

                Common.WinFormVar.Var.ParaPath = HisVar.HisVar.Parapath
                Common.WinFormVar.Var.IniFileHelper = New Common.INIFileHelper(Common.WinFormVar.Var.ParaPath + "\\Config.ini")

                HisVar.HisVar.Sqllite.ConnectionString = "Data Source=" + HisVar.HisVar.ConfPath + "\conf.dat;Version=3"
                HisVar.HisVar.SqlliteCon.ConnectionString = "Data Source=" + Application.StartupPath & "\Conf\conn.dat;Version=3"

                HisVar.HisVar.YyOledb.ConnectionString = "Provider=Microsoft.Jet.OLEDB.4.0; Data Source=.\Yy_Db.mdb; Jet OLEDB:Database Password=dzh"
                ZTHisVar.Var.XqCode = My.Computer.Registry.GetValue(V_HKey, "县区编码", Nothing)
                ZTHisVar.Var.DebugLb = My.Settings.DebugLb

                If My.Settings.DebugLb = "正式" Then
                    Dim _ds As New DataSet
                    If My.Computer.Registry.GetValue(V_HKey, "医院编码", Nothing) & "" = "" Then
                        If Zd_Cssz1.ShowDialog() = DialogResult.Cancel Then Exit Sub
                    End If
                    _ds = HisVar.HisVar.YyOledb.Query("Select Yy_Ip,Yy_Db,Yy_Pass,Db_Id,Is_Ybzl,DataBaseService,DzblPrintService,JkkService,V_iP,V_Sid,V_Uid,V_Pwd,Xy_iP,Xy_Sid,Xy_Uid,Xy_Pwd,Ybzl_FdLx,Ybzl_BcDj,Ybzl_FdJe,Middle_DbName,Is_Yb_Ydjy,JkdaService From Zd_Yy,Zd_Xq  Where Zd_Yy.Xq_Code=Zd_Xq.Xq_Code and Yy_Code='" & My.Computer.Registry.GetValue(V_HKey, "医院编码", Nothing) & "" & "' And Zd_Yy.Xq_Code='" & My.Computer.Registry.GetValue(V_HKey, "县区编码", Nothing) & "'")

                    My.Settings.DB_Ip = _ds.Tables(0).Rows(0).Item("Yy_Ip") & ""
                    My.Settings.DB_Name = _ds.Tables(0).Rows(0).Item("Yy_Db") & ""
                    My.Settings.DB_Pwd = _ds.Tables(0).Rows(0).Item("Yy_Pass") & ""
                    '                    My.Settings.Is_Ybzl = _ds.Tables(0).Rows(0).Item("Is_Ybzl").ToString & ""
                    My.Settings.DB_Id = _ds.Tables(0).Rows(0).Item("Db_Id") & ""
                    '                    My.Settings.Is_Yb_Ydjy = _ds.Tables(0).Rows(0).Item("Is_Yb_Ydjy").ToString & ""

                    '                    V_DatabaseService = _ds.Tables(0).Rows(0).Item("DataBaseService") & ""
                    '                    V_DzblPrintService = _ds.Tables(0).Rows(0).Item("DzblPrintService") & ""
                    '                    V_JkkService = _ds.Tables(0).Rows(0).Item("JkkService") & ""

                    '                    V_NhIp = _ds.Tables(0).Rows(0).Item("V_iP").ToString & ""
                    '                    V_NhSid = _ds.Tables(0).Rows(0).Item("V_Sid").ToString & ""
                    '                    V_NhUid = _ds.Tables(0).Rows(0).Item("V_Uid").ToString & ""
                    '                    V_NhPwd = _ds.Tables(0).Rows(0).Item("V_Pwd").ToString & ""

                    '                    Xy_NhIp = _ds.Tables(0).Rows(0).Item("Xy_iP").ToString & ""
                    '                    Xy_NhSid = _ds.Tables(0).Rows(0).Item("Xy_Sid").ToString & ""
                    '                    Xy_NhUid = _ds.Tables(0).Rows(0).Item("Xy_Uid").ToString & ""
                    '                    Xy_NhPwd = _ds.Tables(0).Rows(0).Item("Xy_Pwd").ToString & ""

                    '                    V_Middle_DbName = _ds.Tables(0).Rows(0).Item("Middle_DbName").ToString & ""

                    '                    V_Ybzl_FdLx = _ds.Tables(0).Rows(0).Item("Ybzl_FdLx")
                    '                    V_Ybzl_BcDj = _ds.Tables(0).Rows(0).Item("Ybzl_BcDj")
                    '                    V_Ybzl_FdJe = _ds.Tables(0).Rows(0).Item("Ybzl_FdJe")
                ElseIf My.Settings.DebugLb = "云诊所" Then
                    Dim localIni = New Common.INIFileHelper(Application.StartupPath + "\\localpathpara.ini")
                    Dim OrgCode As String = localIni.IniReadValue("CloudPara", "OrgCode") + ""
                    If OrgCode.IsNullOrEmpty() Then
                        Dim frm As New ZTHisPublicForm.CloudReg
                        If frm.ShowDialog() = DialogResult.Cancel Then
                            SetText("初始化医院参数失败。")
                            Exit Sub
                        End If
                    End If
                    ZTHisVar.Var.ClientCode = localIni.IniReadValue("CloudPara", "OrgCode") + ""
                    Dim cloud As New ZTHisSysManage.Cloud(Me)

                    If Not cloud.GetDBInfo(ZTHisVar.Var.ClientCode, My.Application.Info.Version.ToString(), My.Settings.DB_Ip, My.Settings.DB_Name, My.Settings.DB_Id, My.Settings.DB_Pwd) Then
                        SetText("初始化医院参数失败。")
                        Exit Sub
                    End If
                End If
                If My.Settings.DebugLb = "单机诊所" Or My.Settings.DebugLb = "云诊所" Then
                    SetText("正在检测更新...")
                    Dim cloud As New ZTHisSysManage.Cloud(Me)
                    cloud.Update(My.Application.Info.Version.ToString())
                    cloud.GetWhiteList()
                    cloud.GetRegisterCode()
                End If



                '                If V_Middle_DbName & "" = "" Then
                '                    V_Middle_DbName = "Hzyl_His"
                '                End If
                SetText("正在检测数据库连接...")
                Call con_init()
                If HisVar.HisVar.Sqldal.GetSingle("Select GetDate()") IsNot Nothing Then
                    DataBackUp.Getdata()
                    If ZTHisPublicFunction.DBUpdate.DBNeedUpdate() = True Then
                        SetText("正在更新数据库结构..")
                        ZTHisPublicFunction.DBUpdate.DbUpdate(My.Settings.DB_Pwd, My.Settings.DB_Ip, My.Settings.DB_Name, My.Settings.DB_Id)
                    End If
                End If
                If My.Settings.DebugLb = "单机诊所" Or My.Settings.DebugLb = "云诊所" Then
                    Dim cloud As New ZTHisSysManage.Cloud(Me)
                    cloud.GetErxConfig()
                End If
                ZTHisInsuranceAPI.Yb_Info.GetYbInfo(ZTHisVar.Var.YsCode)
                SetText("系统准备就绪.请登陆.")
                Me.BeginInvoke(New Action(Of String)(Function(p)
                                                         lblfixmedins_code.Text = Yb_Info.fixmedins_code
                                                         lblfixmedins_name.Text = Yb_Info.fixmedins_name
                                                     End Function), "")
                C1TextBox1.ReadOnly = False
                C1TextBox2.ReadOnly = False
                C1Button1.Enabled = True
                CheckBox1.Enabled = True
                CheckBox2.Enabled = True
                If iniOperate.iniopreate.GetINI("参数", "记住用户", "", HisVar.HisVar.Parapath & "\Config.ini") & "" = "" Or iniOperate.iniopreate.GetINI("参数", "记住密码", "", HisVar.HisVar.Parapath & "\Config.ini") & "" = "" Then Exit Sub

                If iniOperate.iniopreate.GetINI("参数", "记住用户", "", HisVar.HisVar.Parapath & "\Config.ini") Then
                    C1TextBox1.Text = iniOperate.iniopreate.GetINI("参数", "登录用户", "", HisVar.HisVar.Parapath & "\Config.ini")
                    CheckBox1.Checked = iniOperate.iniopreate.GetINI("参数", "记住用户", "", HisVar.HisVar.Parapath & "\Config.ini")
                End If
                If iniOperate.iniopreate.GetINI("参数", "记住密码", "", HisVar.HisVar.Parapath & "\Config.ini") Then
                    C1TextBox2.Text = BaseFunc.BaseFunc.Look_Pwd(iniOperate.iniopreate.GetINI("参数", "登录密码", "", HisVar.HisVar.Parapath & "\Config.ini"))
                    CheckBox2.Checked = iniOperate.iniopreate.GetINI("参数", "记住密码", "", HisVar.HisVar.Parapath & "\Config.ini")
                End If
                If C1TextBox1.Text = "" Then
                    C1TextBox1.Select()
                Else
                    C1TextBox2.Select()
                End If

            Catch ex As Exception
                MsgBox(ex.Message.ToString, MsgBoxStyle.Information, "提示")
                SetText("系统数据库连接失败.")
                If My.Settings.DebugLb <> "正式" Then
                    If Connect_Edit.ShowDialog = Windows.Forms.DialogResult.OK Then
                        MsgBox("连接修改成功程序将重新启动！", MsgBoxStyle.Information, "提示")
                        Application.Restart()
                    Else
                        MsgBox("无法正常连接数据库程序将退出！", MsgBoxStyle.Information, "提示")
                        Application.Exit()
                    End If
                End If

            Finally

            End Try

        End If
    End Sub


    Private Sub C1Button2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Button2.Click
        Me.DialogResult = Windows.Forms.DialogResult.Cancel
        End
    End Sub

    Private Sub TextBox_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles C1TextBox1.KeyPress, C1TextBox2.KeyPress
        If e.KeyChar = Chr(13) Then
            e.Handled = True
            System.Windows.Forms.SendKeys.Send("{Tab}")
        End If
    End Sub

    Private Sub C1Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Button1.Click
        Try

            Dim V_date As Date = HisVar.HisVar.Sqldal.GetSingle("Select getdate()")

            If Format(V_date, "yyyy-MM-dd HH:mm") <> Format(Now, "yyyy-MM-dd HH:mm") Then
                If MsgBox("服务器时间为:" & vbCrLf & Format(V_date, "yyyy-MM-dd HH:mm") & vbCrLf & "本地时间为:" & vbCrLf & Format(Now, "yyyy-MM-dd HH:mm") & vbCrLf & "本地时间与服务器时间不符,请手动调整!", MsgBoxStyle.Critical, "警告") = MsgBoxResult.Ok Then
                End If
            End If
            Dim R As Regex
            R = New Regex("^\w*$")
            If R.IsMatch(C1TextBox1.Text) = False Then
                MsgBox("请输入有效字符！", MsgBoxStyle.Information + MsgBoxStyle.OkOnly, "提示")
                C1TextBox1.SelectAll()
                C1TextBox1.Focus()
                Exit Sub
            End If

            V_LoginCode = Trim(Me.C1TextBox1.Text)
            V_PassWord = encode.F_Encode(Trim(Me.C1TextBox2.Text))
            V_PassWord = Replace(V_PassWord, "'", "''")
            Dim BllJsr As New BLL.BllZd_YyJsr
            Dim dt As DataTable = BllJsr.GetList("Jsr_PassWord='" & V_PassWord & "'and login_Code='" & V_LoginCode & "'").Tables(0)
            If dt.Rows.Count > 0 Then
                HisVar.HisVar.JsrName = dt.Rows(0).Item("Jsr_Name")
                HisVar.HisVar.JsrCode = dt.Rows(0).Item("Jsr_Code")

                V_LoginCode = dt.Rows(0).Item("Login_Code")
                V_PassWord = dt.Rows(0).Item("Jsr_PassWord")
                '                V_GlzCode = dt.Rows(0).Item("Glz_Code")
                V_InputLanguage = dt.Rows(0).Item("Jsr_Py") & ""
                HisVar.HisVar.WsyCode = dt.Rows(0).Item("Yy_Code") & ""
                HisVar.HisVar.WsyName = dt.Rows(0).Item("Yy_Name") & ""
                HisVar.HisVar.YfCode = dt.Rows(0).Item("Yf_Code") & ""
                HisVar.HisVar.YfName = dt.Rows(0).Item("Yf_Name") & ""
                HisVar.HisVar.JsrYsCode = dt.Rows(0).Item("Ys_Code") & ""
                HisVar.HisVar.JsrYsName = dt.Rows(0).Item("Ys_Name") & ""
                HisVar.HisVar.GlzCode = dt.Rows(0).Item("Glz_Code") & ""
                HisVar.HisVar.XmKs = dt.Rows(0).Item("Xm_Ks") & ""
                '                HisVar.HisVar.Ks_Name = dt.Rows(0).Item("Ks_Name") & ""
                ZTHisVar.Var.KsName = dt.Rows(0).Item("Ks_Name") & ""

                ZTHisVar.Var.Manage_Title_Level = IIf(IsDBNull(dt.Rows(0).Item("Manage_Title_Level")), 0, dt.Rows(0).Item("Manage_Title_Level"))

                If dt.Rows(0).Item("Medical_Type") & "" = "" Then
                    ZTHisVar.Var.Medical_Type = Nothing
                Else
                    ZTHisVar.Var.Medical_Type = [Enum].Parse(GetType(ZTHisEnum.Medical_Type), dt.Rows(0).Item("Medical_Type") & "")
                End If
                ZTHisVar.Var.JsrCode = HisVar.HisVar.JsrCode
                ZTHisVar.Var.JsrName = HisVar.HisVar.JsrName
                ZTHisVar.Var.HosCode = HisVar.HisVar.WsyCode
                ZTHisVar.Var.HosName = HisVar.HisVar.WsyName
                ZTHisVar.Var.YfCode = HisVar.HisVar.YfCode
                ZTHisVar.Var.YfName = HisVar.HisVar.YfName
                ZTHisVar.Var.YsCode = HisVar.HisVar.JsrYsCode
                ZTHisVar.Var.YsName = HisVar.HisVar.JsrYsName
                ZTHisVar.Var.GlzCode = HisVar.HisVar.GlzCode
                ZTHisVar.Var.KsCode = HisVar.HisVar.XmKs

                If dt.Rows(0).Item("EmrColor").ToString() & "" <> "" Then
                    Dim mycolor As String()
                    mycolor = dt.Rows(0).Item("EmrColor").ToString().Split(",")
                    HisVar.HisVar.JsrColor = Color.FromArgb(mycolor(1), mycolor(2), mycolor(3))
                End If

                If dt.Rows(0).Item("Ys_Use").ToString <> "是" Then
                    HisVar.HisVar.JsrYsCode = ""
                End If
            End If

            Dim m_inputcode As InputLanguage
            For Each m_inputcode In InputLanguage.InstalledInputLanguages
                If m_inputcode.LayoutName = V_InputLanguage Then
                    HisVar.HisVar.InputCode = m_inputcode
                    Common.WinFormVar.Var.InputMethod = m_inputcode
                    B_InputLanguage = True
                    Exit For
                End If
                B_InputLanguage = False
            Next

            If HisVar.HisVar.JsrName = "" Then
                MsgBox("用户名或密码无效,请重新登陆!", MsgBoxStyle.Information, "提示")
                Me.C1TextBox2.Text = ""
                Me.C1TextBox2.Focus()
            Else

                HisPara.PublicConfig.ReadConfig()
                ZTHisPara.PublicConfig.ReadConfig()
                ZTHisPara.ApiConfig.ReadConfig()
                ZTHisInsuranceAPI.Yb_Info.GetYbInfo(ZTHisVar.Var.YsCode)

                If CheckBox1.Checked = True Then
                    iniOperate.iniopreate.WriteINI("参数", "记住用户", CheckBox1.Checked, HisVar.HisVar.Parapath & "\Config.ini")
                    iniOperate.iniopreate.WriteINI("参数", "登录用户", C1TextBox1.Text, HisVar.HisVar.Parapath & "\Config.ini")
                End If

                If CheckBox2.Checked = True Then
                    iniOperate.iniopreate.WriteINI("参数", "记住密码", CheckBox1.Checked, HisVar.HisVar.Parapath & "\Config.ini")
                    iniOperate.iniopreate.WriteINI("参数", "登录密码", encode.F_Encode(C1TextBox2.Text), HisVar.HisVar.Parapath & "\Config.ini")
                End If

                If B_InputLanguage = False Then
                    MsgBox("未找到软件默认输入法, 请先设置您习惯的中文输入法", vbInformation + vbOKOnly + vbDefaultButton1, "提示:")
                    Person_Config.TabControl1.SelectedTab = Person_Config.TabPage2

                    Person_Config.ShowDialog()
                End If

                Dim files As String() = IO.Directory.GetFiles(".\HuaDaJkkDLL")
                For Each V_File In files   '复制动态链接库
                    Dim NewInfo As New System.IO.FileInfo(V_File)
                    Dim OldInfo As New System.IO.FileInfo(".\" & IO.Path.GetFileName(V_File))

                    If IO.File.Exists(".\" & IO.Path.GetFileName(V_File)) = False Then
                        FileCopy(V_File, ".\" & IO.Path.GetFileName(V_File))
                    ElseIf NewInfo.LastWriteTime > OldInfo.LastWriteTime Then
                        FileCopy(V_File, ".\" & IO.Path.GetFileName(V_File))
                    End If
                Next

                If My.Settings.DebugLb = "调试" Then
                    Me.DialogResult = Windows.Forms.DialogResult.OK
                ElseIf CheckJsrIsLogin() = False Then
                    Me.DialogResult = Windows.Forms.DialogResult.OK
                End If
            End If

        Catch ex As Exception
            MsgBox(ex.ToString, MsgBoxStyle.Information, "提示")
        End Try
    End Sub

    Public Function CheckJsrIsLogin() As Boolean
        Try
            If Login_Cn.State = ConnectionState.Closed Then
                Login_Cn.ConnectionString = My_Cn.ConnectionString
                Login_Cn.Open()
            End If


            Dim JsrCmd As SqlCommand
            JsrCmd = New SqlCommand("If OBJECT_ID ('tempdb..##Login" & HisVar.HisVar.JsrCode & "') is not null Select Jsr_Code,ComputerName,Login_DateTime from ##Login" & HisVar.HisVar.JsrCode, Login_Cn)
            Dim LogReader As SqlDataReader = JsrCmd.ExecuteReader()
            LogReader.Read()
            If LogReader.HasRows = True Then
                MsgBox("该用户【" & LogReader.Item("Login_DateTime") & "】已经在名为【" & LogReader.Item("ComputerName") & "】的计算机上登陆,请检查！", MsgBoxStyle.Information, "提示")
                LogReader.Close()
                Return True
            Else
                LogReader.Close()
                JsrCmd = New SqlCommand("If OBJECT_ID ('tempdb..##Login" & HisVar.HisVar.JsrCode & "') is null Select '" & HisVar.HisVar.JsrCode & "' as Jsr_Code,'" & My.Computer.Name & "' as ComputerName,Getdate() as Login_DateTime Into ##Login" & HisVar.HisVar.JsrCode, Login_Cn)
                JsrCmd.ExecuteNonQuery()
                Return False
            End If
        Catch ex As Exception
            MsgBox(ex.Message.ToString, MsgBoxStyle.Information, "提示")
            Return True

        End Try
    End Function

    Private Sub con_init()
        My_Cn.ConnectionString = "Data Source=" & My.Settings.DB_Ip & ";Initial Catalog=" & My.Settings.DB_Name & ";Persist Security Info=True;User Id=" & My.Settings.DB_Id & ";Pwd=" & My.Settings.DB_Pwd & ""
        HisVar.HisVar.Sqldal.ConnectionString = My_Cn.ConnectionString

        Dim pp As New PersistenceProperty()
        pp.DatabaseType = DatabaseType.MSSQLServer
        pp.ConnectionString = My_Cn.ConnectionString
        Common.WinFormVar.Var.DbHelper = DataAccessFactory.CreateDataAccess(pp)
        Common.WinFormVar.Var.DbHelper.TimeOut = 2 * 60 * 1000
        Common.WinFormVar.Var.DALPath = "SQLServerDAL"

        '        If My.Settings.DebugLb = "正式" Then
        '            If My.Settings.DB_Name = "His2013_New" And My.Settings.DB_Pwd = "0703" Then 'His2013_New这个数据库名有两个医院，只有王官营不允许连接正式中心库，所以加限制
        '            Else
        '            End If
        '            HisVar.HisVar.DzblPrintservice.Url = V_DzblPrintService
        '        Else
        '            HisVar.HisVar.DzblPrintservice.Url = iniOperate.iniopreate.GetINI("His_DataBase", "DzblPrintService", "", ".\System.Ini")
        '        End If


        '        If My.Settings.DebugLb <> "调试" Then
        '
        '
        '
        '
        '
        '        Else
        '
        '
        '
        '        End If

        '        HisVar.HisVar.HdDy_Dal.ConnectionString = "Data Source=" & My.Settings.DB_Ip & ";Initial Catalog=" & V_Middle_DbName & ";Persist Security Info=True;User Id=sa;Pwd=" & My.Settings.DB_Pwd & ";"
        BaseClass.C_Cc.ConnectionString = My_Cn.ConnectionString
    End Sub

    Private Sub GetMachineCode()
        ZTHisVar.Var.MachineCode = Common.RegisterCode.RegisterCodeCheck.GetMachineCode()
    End Sub
#Region "控件光标"
    Private Sub C1Button_MouseEnter(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1Button1.MouseEnter, C1Button2.MouseEnter
        Me.Cursor = Cursors.Hand
    End Sub

    Private Sub C1Button_MouseLeave(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1Button1.MouseLeave, C1Button2.MouseLeave
        Me.Cursor = Cursors.Default
    End Sub
#End Region

    Private Sub C1TextBox1_Validated(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1TextBox1.Validated
        If C1TextBox1.Text.Trim & "" = "" Then Exit Sub
        Dim ss As Object
        ss = HisVar.HisVar.Sqldal.GetSingle("Select Jsr_Name From Zd_Yyjsr Where Login_Code='" & C1TextBox1.Text & "'")
        If ss Is Nothing Then
            Label1.Text = ""
        Else
            Label1.Text = ss.ToString
        End If

    End Sub

    Private Sub LinkLabel2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles LinkLabel2.Click
        If ZTHisVar.Var.DebugLb = "单机诊所" Then
            Dim frm As New ZTHisVar.RegisterForm()
            frm.ShowDialog()
        ElseIf ZTHisVar.Var.DebugLb = "云诊所" Then
            Dim frm As New ZTHisPublicForm.CloudReg
            If frm.ShowDialog() = DialogResult.OK Then
                Application.Restart()
            End If
        Else
            If Zd_Cssz1.ShowDialog() = DialogResult.Cancel Then

            Else
                Application.Restart()
            End If
        End If


    End Sub

    Private Sub CheckBox1_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox1.CheckedChanged
        If CheckBox1.Checked = True Then
            iniOperate.iniopreate.WriteINI("参数", "记住用户", CheckBox1.Checked, HisVar.HisVar.Parapath & "\Config.ini")
            iniOperate.iniopreate.WriteINI("参数", "登录用户", C1TextBox1.Text, HisVar.HisVar.Parapath & "\Config.ini")
        Else
            iniOperate.iniopreate.WriteINI("参数", "记住用户", CheckBox1.Checked, HisVar.HisVar.Parapath & "\Config.ini")
            iniOperate.iniopreate.WriteINI("参数", "登录用户", "", HisVar.HisVar.Parapath & "\Config.ini")
        End If

    End Sub

    Private Sub CheckBox2_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox2.CheckedChanged
        If CheckBox2.Checked = True Then
            iniOperate.iniopreate.WriteINI("参数", "记住密码", CheckBox2.Checked, HisVar.HisVar.Parapath & "\Config.ini")
            iniOperate.iniopreate.WriteINI("参数", "登录密码", encode.F_Encode(C1TextBox2.Text), HisVar.HisVar.Parapath & "\Config.ini")
        Else
            iniOperate.iniopreate.WriteINI("参数", "记住密码", CheckBox2.Checked, HisVar.HisVar.Parapath & "\Config.ini")
            iniOperate.iniopreate.WriteINI("参数", "登录密码", "", HisVar.HisVar.Parapath & "\Config.ini")
        End If

    End Sub

#Region "实现窗体可以移动"
    <DllImport("user32.dll")>
    Public Shared Function ReleaseCapture() As Boolean
    End Function
    <DllImport("user32.dll")>
    Public Shared Function SendMessage(ByVal hwnd As IntPtr, ByVal wMsg As Integer, ByVal wParam As Integer, ByVal IParam As Integer) As Boolean
    End Function
    Public Const WM_SYSCOMMAND As Integer = &H112
    Public Const SC_MOVE As Integer = &HF010
    Public Const HTCAPTION As Integer = &H2

    ''' <summary>
    ''' 鼠标按住时候能移动窗体
    ''' </summary>
    ''' <param name="e"></param>
    Protected Overloads Overrides Sub OnMouseDown(ByVal e As MouseEventArgs)
        MyBase.OnMouseDown(e)
        ReleaseCapture()
        SendMessage(Me.Handle, WM_SYSCOMMAND, SC_MOVE + HTCAPTION, 0)
    End Sub
#End Region


End Class
