﻿Public Class ZyXmCx
    Dim My_DataSet As New DataSet

    Private Sub ZyXmCx_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        MyDateEdit1.Value = Format(Now, "yyyy-MM-dd")
        MyDateEdit1.CustomFormat = "yyyy-MM-dd"
        MyDateEdit1.DisplayFormat = "yyyy-MM-dd"
        MyDateEdit1.EditFormat = "yyyy-MM-dd"
        With MyGrid1
            .Init_Grid()
            .Init_Column("病历号", "Bl_Code", 0, "中", "", False)
            .Init_Column("姓名", "Ry_Name", 100, "中", "", False)
            .Init_Column("科室", "ks_name", 100, "中", "", False)
            .Init_Column("病床", "Bc_Name", 100, "中", "", False)
            .Init_Column("日期", "Cf_Date", 100, "中", "yyyy-MM-dd", False)
            .Init_Column("时间", "Cf_Time", 100, "中", "HH:mm:ss", False)
            .Init_Column("项目名称", "Xm_Name", 400, "左", "", False)
            .Init_Column("数量", "cf_sl", 100, "中", "##.##", False)
            .Init_Column("是否完成", "Xm_Wc", 60, "中", "", False)
        End With
        MyGrid1.Splits(0).DisplayColumns("Bl_Code").Merge = C1.Win.C1TrueDBGrid.ColumnMergeEnum.Free
        MyGrid1.Splits(0).DisplayColumns("Ry_Name").Merge = C1.Win.C1TrueDBGrid.ColumnMergeEnum.Free
        MyGrid1.Splits(0).DisplayColumns("ks_name").Merge = C1.Win.C1TrueDBGrid.ColumnMergeEnum.Free
        MyGrid1.Splits(0).DisplayColumns("Bc_Name").Merge = C1.Win.C1TrueDBGrid.ColumnMergeEnum.Free
        MyGrid1.Splits(0).DisplayColumns("Bl_Code").Style.VerticalAlignment = C1.Win.C1TrueDBGrid.AlignVertEnum.Center
        MyGrid1.Splits(0).DisplayColumns("Ry_Name").Style.VerticalAlignment = C1.Win.C1TrueDBGrid.AlignVertEnum.Center
        MyGrid1.Splits(0).DisplayColumns("ks_name").Style.VerticalAlignment = C1.Win.C1TrueDBGrid.AlignVertEnum.Center
        MyGrid1.Splits(0).DisplayColumns("Bc_Name").Style.VerticalAlignment = C1.Win.C1TrueDBGrid.AlignVertEnum.Center

        MyGrid1.Splits(0).DisplayColumns("Xm_Wc").OwnerDraw = True
        MyGrid1.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightCell

        Dim str As String = "select Bl.Bl_Code ,bl_cf.cf_code,Ry_Name,ks_name,Bc_Name, CONVERT(VARCHAR(10),Cf_Date,126) AS Cf_Date,Cf_Time,Xm_Name,cf_sl,Xm_Wc from bl,zd_yyks,zd_yybc,bl_cf,Bl_Cfxm,zd_ml_xm3 where bl.bl_code=bl_cf.bl_code and bl_cf.Cf_Code=bl_cfxm.Cf_Code and bl_cfxm.Xm_Code =zd_ml_xm3.Xm_Code  " & _
                                   "and bl.Ks_Code =zd_yyks.Ks_Code and bl.Bc_Code =zd_yybc.Bc_Code   and Ry_CyDate is null and Cf_Date ='" & MyDateEdit1.Value & "' and Cf_Qr='是' ORDER BY Cf_Date"

        Call Init_Data(str)

    End Sub
    Private Sub Init_Data(ByVal sql As String)
     
        HisVar.HisVar.Sqldal.QueryDt(My_DataSet, sql, "查询结果", True)
        MyGrid1.DataTable = My_DataSet.Tables("查询结果")

    End Sub

    Private Sub MyDateEdit1_ValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyDateEdit1.ValueChanged
        Dim str As String = "select Bl.Bl_Code ,bl_cf.cf_code,Ry_Name,ks_name,Bc_Name, CONVERT(VARCHAR(10),Cf_Date,126) AS Cf_Date,Cf_Time,Xm_Name,cf_sl,Xm_Wc from bl,zd_yyks,zd_yybc,bl_cf,Bl_Cfxm,zd_ml_xm3 where bl.bl_code=bl_cf.bl_code and bl_cf.Cf_Code=bl_cfxm.Cf_Code and bl_cfxm.Xm_Code =zd_ml_xm3.Xm_Code  " & _
                                     "and bl.Ks_Code =zd_yyks.Ks_Code and bl.Bc_Code =zd_yybc.Bc_Code   and Ry_CyDate is null and Cf_Date ='" & MyDateEdit1.Value & "' and Cf_Qr='是' ORDER BY Cf_Date"

        Call Init_Data(str)
    End Sub

    Private Sub MyButton1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyButton1.Click
        Dim str As String = "select Bl.Bl_Code ,bl_cf.cf_code,Ry_Name,ks_name,Bc_Name, CONVERT(VARCHAR(10),Cf_Date,126) AS Cf_Date,Cf_Time,Xm_Name,cf_sl,Xm_Wc from bl,zd_yyks,zd_yybc,bl_cf,Bl_Cfxm,zd_ml_xm3 where bl.bl_code=bl_cf.bl_code and bl_cf.Cf_Code=bl_cfxm.Cf_Code and bl_cfxm.Xm_Code =zd_ml_xm3.Xm_Code  " & _
                                    "and bl.Ks_Code =zd_yyks.Ks_Code and bl.Bc_Code =zd_yybc.Bc_Code   and Ry_CyDate is null and Cf_Date ='" & MyDateEdit1.Value & "' and Cf_Qr='是' ORDER BY Cf_Date"

        Call Init_Data(str)
    End Sub

    Private Sub MyGrid1_OwnerDrawCell(ByVal sender As System.Object, ByVal e As C1.Win.C1TrueDBGrid.OwnerDrawCellEventArgs) Handles MyGrid1.OwnerDrawCell
        If e.Column.Name = "是否完成" Then
            If e.Text = "否" Then
                e.Style.ForeColor = Color.Red
            End If
        End If

    End Sub

    Private Sub BlCx_Btn_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BlCx_Btn.Click
        Dim str As String = "select Bl.Bl_Code ,bl_cf.cf_code,Ry_Name,ks_name,Bc_Name, CONVERT(VARCHAR(10),Cf_Date,126) AS Cf_Date,Cf_Time,Xm_Name,cf_sl,Xm_Wc from bl,zd_yyks,zd_yybc,bl_cf,Bl_Cfxm,zd_ml_xm3 where bl.bl_code=bl_cf.bl_code and bl_cf.Cf_Code=bl_cfxm.Cf_Code and bl_cfxm.Xm_Code =zd_ml_xm3.Xm_Code  " & _
                               "and bl.Ks_Code =zd_yyks.Ks_Code and bl.Bc_Code =zd_yybc.Bc_Code   and Ry_CyDate is null and Bl.Bl_Code ='" & Bl_TextBox.Text.Trim & "' and Cf_Qr='是' ORDER BY Cf_Date"

        Call Init_Data(str)
    End Sub
End Class