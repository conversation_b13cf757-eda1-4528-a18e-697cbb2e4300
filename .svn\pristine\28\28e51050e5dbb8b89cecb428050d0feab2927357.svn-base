﻿Imports C1.Win.C1Input
Imports System.Data.SqlClient
Imports BaseClass
Imports System.Drawing

Public Class EmrBasic2
#Region "变量定义"
    Dim Emr_BasicElementTreeBLL As New BLLOld.B_Emr_BasicElementTree
    Dim Emr_BasicElementTreeModel As New ModelOld.M_Emr_BasicElementTree
    ' Dim Emr_BasicElementListBLL As New BLLOld.B_Emr_BasicElementList
    ' Dim Emr_BasicElementValueBLL As New BLLOld.B_Emr_BasicElementValue
    'Dim Emr_DataFieldBLL As New BLLOld.B_Emr_DataField

    ' Dim Emr_BasicElementListModel As New ModelOld.M_Emr_BasicElementList
    ' Dim cha As New BaseClass.Chs2Spell
#End Region

#Region "传参"
    Dim Rrc As C_RowChange
    Dim Rinsert As Boolean
    Dim V_LbCount As Integer
    Dim Rtext, Rtag, RType, rFTxt, rFTag As String
#End Region
    Public Sub New(ByVal tRc As C_RowChange, ByVal tinsert As Boolean, ByVal tTag As String, ByVal tText As String,
                   ByVal tType As String, ByVal fTag As String, ByVal fText As String)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        Rinsert = tinsert
        Rrc = tRc
        Rtag = tTag
        Rtext = tText
        rtype = tType
        rFTag = fTag
        rFTxt = fText
        ' 在 InitializeComponent() 调用之后添加任何初始化。
    End Sub

    Private Sub EmrBasic2_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Call Form_Init()
        If Rinsert = True Then Call Data_Clear() Else Call Data_Show()
    End Sub

#Region "窗体__事件"

    Private Sub Form_Init()
        Panel1.Height = 32
        ToolBar1.Location = New Point(2, 4)
        CodeTextBox1.Enabled = False
        FatherNameTextBox2.Enabled = False
        '按扭初始化
        MyButton1.Top = 4
        MyButton2.Location = New Point(MyButton1.Left + MyButton1.Width + 2, MyButton1.Top)
    End Sub

#End Region

#Region "数据__操作"

    Private Sub Data_Clear()
        Rinsert = True
        CodeTextBox1.Text = Emr_BasicElementTreeBLL.MaxCode()
        NameMyTextBox1.Text = ""
        FatherNameTextBox2.Text = rFTxt
        NameMyTextBox1.Focus()
    End Sub
    Private Sub Data_Show()
        Rinsert = False
        CodeTextBox1.Text = Emr_BasicElementTreeBLL.MaxCode()
        NameMyTextBox1.Text = Rtext
        FatherNameTextBox2.Text = rFTxt
        NameMyTextBox1.Select()
    End Sub

#End Region

#Region "控件__动作"

    Private Sub Comm_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyButton1.Click, MyButton2.Click
        Select Case sender.tag
            Case "保存"
                If NameMyTextBox1.Text.Trim = "" Then
                    MsgBox("元素名称不能为空", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示")
                    If Rinsert = True Then
                        NameMyTextBox1.Focus()
                    End If
                End If

                If Rinsert = True Then
                    V_LbCount = Emr_BasicElementTreeBLL.GetRecordCount(" Ele_Name='" & Trim(NameMyTextBox1.Text) & "' ")
                    If V_LbCount > 0 Then
                        MsgBox("该元素已经存在", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示")
                        NameMyTextBox1.Text = ""
                        NameMyTextBox1.Focus()
                        Exit Sub
                    Else
                        Call Data_Add()
                        Me.Close()
                    End If
                Else

                    V_LbCount = Emr_BasicElementTreeBLL.GetRecordCount(" Ele_Name='" & Trim(NameMyTextBox1.Text) & "' and Ele_Code<>'" & Rtag & "' ")
                    If V_LbCount > 0 Then
                        MsgBox("该元素已经存在", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示")
                        NameMyTextBox1.Select()
                        Exit Sub
                    Else
                        Call Data_Edit()
                        Me.Close()
                    End If
                End If
            Case "取消"
                Me.Close()
        End Select
    End Sub

#End Region

#Region "数据__编辑"

    Private Sub Data_Add()
        Try
            'Ele_Code, Ele_Name, isDir, EleType, isMultiSelect, DataField, Father_Code
            With Emr_BasicElementTreeModel
                .Ele_Code = Trim(CodeTextBox1.Text)
                .Ele_Name = Trim(NameMyTextBox1.Text)
                .Father_Code = rFTag
                Select Case RType
                    Case "1"
                        .isDir = False
                        .isMultiSelect = False
                        .EleType = "1"
                    Case "2"
                        .isDir = False
                        .isMultiSelect = False
                        .EleType = "2"
                    Case "3"
                        .isDir = False
                        .isMultiSelect = False
                        .EleType = "3"
                    Case Else
                        .isDir = True
                        .isMultiSelect = False
                        .EleType = "0"
                End Select
            End With
            Emr_BasicElementTreeBLL.Add(Emr_BasicElementTreeModel)
            ' EmrMb1.V_Insert = Rinsert
            Rrc.TreeAdd(CodeTextBox1.Text & RType, Trim(NameMyTextBox1.Text))
            '数据保存
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            NameMyTextBox1.Select()
            Exit Sub
        Finally

        End Try
        '数据清空
        Call Data_Clear()

    End Sub

    Private Sub Data_Edit()
        '数据更新
        'Ele_Code, Ele_Name, isDir, EleType, isMultiSelect, DataField, Father_Code

        Emr_BasicElementTreeModel = Emr_BasicElementTreeBLL.GetModel(Rtag)
        With Emr_BasicElementTreeModel
            .Ele_Name = Trim(NameMyTextBox1.Text)
        End With
        Try
            Emr_BasicElementTreeBLL.Update(Emr_BasicElementTreeModel)
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
        Finally
            NameMyTextBox1.Select()
        End Try
        ' EmrMb1.V_Insert = Rinsert
        Rrc.TreeAdd(CodeTextBox1.Text & RType, Trim(NameMyTextBox1.Text))
    End Sub

#End Region

End Class