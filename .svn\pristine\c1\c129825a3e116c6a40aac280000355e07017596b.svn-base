﻿/**  版本信息模板在安装目录下，可自行修改。
* D_Bl.cs
*
* 功 能： N/A
* 类 名： D_Bl
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/6/23 15:47:21   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using System.Collections.Generic;
namespace DAL
{
    /// <summary>
    /// 数据访问类:D_Bl
    /// </summary>
    public partial class D_Bl
    {
        public D_Bl()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(string Yy_code, string Bl_Code)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from Bl");
            strSql.Append(" where Yy_code=@Yy_code and Bl_Code=@Bl_Code ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Yy_code", SqlDbType.Char,4),
                    new SqlParameter("@Bl_Code", SqlDbType.Char,14)         };
            parameters[0].Value = Yy_code;
            parameters[1].Value = Bl_Code;

            return HisVar.HisVar.Sqldal.Exists(strSql.ToString(), parameters);
        }


        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(Model.M_Bl Model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into Bl(");
            strSql.Append("Yy_code,Bl_Code,Ks_Code,Ys_Code,Bc_Code,Jsr_Code,Jb_Code,Bxlb_Code,Ry_YlCode,Ry_Name,Ry_Jc,Ry_Sex,Ry_Sfzh,Ry_Csdate,Ry_Address,Ry_Tele,Ry_Memo,Ry_BlCode,Ry_RyDate,Ry_CyDate,Ry_ZyTs,Ry_CyJsr,Bl_Ph,Bl_M1,Bl_M2,Bl_M3,Bl_M4,Bl_M5,Bl_M6,Bl_M7,Bl_M8,Bl_M9,Bl_M10,Bl_M11,Bl_M12,Bl_M13,Bl_M14,Bl_M15,Bl_M16,Bl_M17,Bl_M_Yj,Bl_M_Sk,Bl_M_Th,Bl_Dc,Jz_Code,Cy_Qr,Jb_Name)");
            strSql.Append(" values (");
            strSql.Append("@Yy_code,@Bl_Code,@Ks_Code,@Ys_Code,@Bc_Code,@Jsr_Code,@Jb_Code,@Bxlb_Code,@Ry_YlCode,@Ry_Name,@Ry_Jc,@Ry_Sex,@Ry_Sfzh,@Ry_Csdate,@Ry_Address,@Ry_Tele,@Ry_Memo,@Ry_BlCode,@Ry_RyDate,@Ry_CyDate,@Ry_ZyTs,@Ry_CyJsr,@Bl_Ph,@Bl_M1,@Bl_M2,@Bl_M3,@Bl_M4,@Bl_M5,@Bl_M6,@Bl_M7,@Bl_M8,@Bl_M9,@Bl_M10,@Bl_M11,@Bl_M12,@Bl_M13,@Bl_M14,@Bl_M15,@Bl_M16,@Bl_M17,@Bl_M_Yj,@Bl_M_Sk,@Bl_M_Th,@Bl_Dc,@Jz_Code,@Cy_Qr,@Jb_Name)");
            SqlParameter[] parameters = {
                    new SqlParameter("@Yy_code", SqlDbType.Char,4),
                    new SqlParameter("@Bl_Code", SqlDbType.Char,14),
                    new SqlParameter("@Ks_Code", SqlDbType.Char,6),
                    new SqlParameter("@Ys_Code", SqlDbType.Char,7),
                    new SqlParameter("@Bc_Code", SqlDbType.Char,7),
                    new SqlParameter("@Jsr_Code", SqlDbType.Char,7),
                    new SqlParameter("@Jb_Code", SqlDbType.VarChar,20),
                    new SqlParameter("@Bxlb_Code", SqlDbType.Char,6),
                    new SqlParameter("@Ry_YlCode", SqlDbType.VarChar,50),
                    new SqlParameter("@Ry_Name", SqlDbType.VarChar,100),
                    new SqlParameter("@Ry_Jc", SqlDbType.VarChar,50),
                    new SqlParameter("@Ry_Sex", SqlDbType.Char,2),
                    new SqlParameter("@Ry_Sfzh", SqlDbType.VarChar,18),
                    new SqlParameter("@Ry_Csdate", SqlDbType.SmallDateTime),
                    new SqlParameter("@Ry_Address", SqlDbType.VarChar,100),
                    new SqlParameter("@Ry_Tele", SqlDbType.VarChar,50),
                    new SqlParameter("@Ry_Memo", SqlDbType.VarChar,200),
                    new SqlParameter("@Ry_BlCode", SqlDbType.VarChar,50),
                    new SqlParameter("@Ry_RyDate", SqlDbType.DateTime),
                    new SqlParameter("@Ry_CyDate", SqlDbType.DateTime),
                    new SqlParameter("@Ry_ZyTs", SqlDbType.Int,4),
                    new SqlParameter("@Ry_CyJsr", SqlDbType.VarChar,10),
                    new SqlParameter("@Bl_Ph", SqlDbType.VarChar,50),
                    new SqlParameter("@Bl_M1", SqlDbType.Decimal,9),
                    new SqlParameter("@Bl_M2", SqlDbType.Decimal,5),
                    new SqlParameter("@Bl_M3", SqlDbType.Decimal,5),
                    new SqlParameter("@Bl_M4", SqlDbType.Decimal,5),
                    new SqlParameter("@Bl_M5", SqlDbType.Decimal,5),
                    new SqlParameter("@Bl_M6", SqlDbType.Decimal,5),
                    new SqlParameter("@Bl_M7", SqlDbType.Decimal,5),
                    new SqlParameter("@Bl_M8", SqlDbType.Decimal,5),
                    new SqlParameter("@Bl_M9", SqlDbType.Decimal,5),
                    new SqlParameter("@Bl_M10", SqlDbType.Decimal,5),
                    new SqlParameter("@Bl_M11", SqlDbType.Decimal,5),
                    new SqlParameter("@Bl_M12", SqlDbType.Decimal,5),
                    new SqlParameter("@Bl_M13", SqlDbType.Decimal,5),
                    new SqlParameter("@Bl_M14", SqlDbType.Decimal,5),
                    new SqlParameter("@Bl_M15", SqlDbType.Decimal,5),
                    new SqlParameter("@Bl_M16", SqlDbType.Decimal,5),
                    new SqlParameter("@Bl_M17", SqlDbType.Decimal,5),
                    new SqlParameter("@Bl_M_Yj", SqlDbType.Decimal,9),
                    new SqlParameter("@Bl_M_Sk", SqlDbType.Decimal,9),
                    new SqlParameter("@Bl_M_Th", SqlDbType.Decimal,9),
                    new SqlParameter("@Bl_Dc", SqlDbType.Bit,1),
                    new SqlParameter("@Jz_Code", SqlDbType.VarChar,12),
                    new SqlParameter("@Cy_Qr", SqlDbType.Char,2),
                    new SqlParameter("@Jb_Name", SqlDbType.VarChar,200)};
            parameters[0].Value = Model.Yy_code;
            parameters[1].Value = Model.Bl_Code;
            parameters[2].Value = Model.Ks_Code;
            parameters[3].Value = Model.Ys_Code;
            parameters[4].Value = Model.Bc_Code;
            parameters[5].Value = Model.Jsr_Code;
            parameters[6].Value = Model.Jb_Code;
            parameters[7].Value = Model.Bxlb_Code;
            parameters[8].Value = Model.Ry_YlCode;
            parameters[9].Value = Model.Ry_Name;
            parameters[10].Value = Model.Ry_Jc;
            parameters[11].Value = Model.Ry_Sex;
            parameters[12].Value = Model.Ry_Sfzh;
            parameters[13].Value = Model.Ry_Csdate;
            parameters[14].Value = Model.Ry_Address;
            parameters[15].Value = Model.Ry_Tele;
            parameters[16].Value = Model.Ry_Memo;
            parameters[17].Value = Model.Ry_BlCode;
            parameters[18].Value = Model.Ry_RyDate;
            parameters[19].Value = Model.Ry_CyDate;
            parameters[20].Value = Model.Ry_ZyTs;
            parameters[21].Value = Model.Ry_CyJsr;
            parameters[22].Value = Model.Bl_Ph;
            parameters[23].Value = Model.Bl_M1;
            parameters[24].Value = Model.Bl_M2;
            parameters[25].Value = Model.Bl_M3;
            parameters[26].Value = Model.Bl_M4;
            parameters[27].Value = Model.Bl_M5;
            parameters[28].Value = Model.Bl_M6;
            parameters[29].Value = Model.Bl_M7;
            parameters[30].Value = Model.Bl_M8;
            parameters[31].Value = Model.Bl_M9;
            parameters[32].Value = Model.Bl_M10;
            parameters[33].Value = Model.Bl_M11;
            parameters[34].Value = Model.Bl_M12;
            parameters[35].Value = Model.Bl_M13;
            parameters[36].Value = Model.Bl_M14;
            parameters[37].Value = Model.Bl_M15;
            parameters[38].Value = Model.Bl_M16;
            parameters[39].Value = Model.Bl_M17;
            parameters[40].Value = Model.Bl_M_Yj;
            parameters[41].Value = Model.Bl_M_Sk;
            parameters[42].Value = Model.Bl_M_Th;
            parameters[43].Value = Model.Bl_Dc;
            parameters[44].Value = Model.Jz_Code;
            parameters[45].Value = Model.Cy_Qr;
            parameters[46].Value = Model.Jb_Name;

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(Model.M_Bl Model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update Bl set ");
            strSql.Append("Ks_Code=@Ks_Code,");
            strSql.Append("Ys_Code=@Ys_Code,");
            strSql.Append("Bc_Code=@Bc_Code,");
            strSql.Append("Jsr_Code=@Jsr_Code,");
            strSql.Append("Jb_Code=@Jb_Code,");
            strSql.Append("Bxlb_Code=@Bxlb_Code,");
            strSql.Append("Ry_YlCode=@Ry_YlCode,");
            strSql.Append("Ry_Name=@Ry_Name,");
            strSql.Append("Ry_Jc=@Ry_Jc,");
            strSql.Append("Ry_Sex=@Ry_Sex,");
            strSql.Append("Ry_Sfzh=@Ry_Sfzh,");
            strSql.Append("Ry_Csdate=@Ry_Csdate,");
            strSql.Append("Ry_Address=@Ry_Address,");
            strSql.Append("Ry_Tele=@Ry_Tele,");
            strSql.Append("Ry_Memo=@Ry_Memo,");
            strSql.Append("Ry_BlCode=@Ry_BlCode,");
            strSql.Append("Ry_RyDate=@Ry_RyDate,");
            strSql.Append("Ry_CyDate=@Ry_CyDate,");
            strSql.Append("Ry_ZyTs=@Ry_ZyTs,");
            strSql.Append("Ry_CyJsr=@Ry_CyJsr,");
            strSql.Append("Bl_Ph=@Bl_Ph,");
            strSql.Append("Bl_M1=@Bl_M1,");
            strSql.Append("Bl_M2=@Bl_M2,");
            strSql.Append("Bl_M3=@Bl_M3,");
            strSql.Append("Bl_M4=@Bl_M4,");
            strSql.Append("Bl_M5=@Bl_M5,");
            strSql.Append("Bl_M6=@Bl_M6,");
            strSql.Append("Bl_M7=@Bl_M7,");
            strSql.Append("Bl_M8=@Bl_M8,");
            strSql.Append("Bl_M9=@Bl_M9,");
            strSql.Append("Bl_M10=@Bl_M10,");
            strSql.Append("Bl_M11=@Bl_M11,");
            strSql.Append("Bl_M12=@Bl_M12,");
            strSql.Append("Bl_M13=@Bl_M13,");
            strSql.Append("Bl_M14=@Bl_M14,");
            strSql.Append("Bl_M15=@Bl_M15,");
            strSql.Append("Bl_M16=@Bl_M16,");
            strSql.Append("Bl_M17=@Bl_M17,");
            strSql.Append("Bl_M_Yj=@Bl_M_Yj,");
            strSql.Append("Bl_M_Sk=@Bl_M_Sk,");
            strSql.Append("Bl_M_Th=@Bl_M_Th,");
            strSql.Append("Bl_Dc=@Bl_Dc,");
            strSql.Append("Jz_Code=@Jz_Code,");
            strSql.Append("Cy_Qr=@Cy_Qr,");
            strSql.Append("Jb_Name=@Jb_Name");
            strSql.Append(" where Yy_code=@Yy_code and Bl_Code=@Bl_Code ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Ks_Code", SqlDbType.Char,6),
                    new SqlParameter("@Ys_Code", SqlDbType.Char,7),
                    new SqlParameter("@Bc_Code", SqlDbType.Char,7),
                    new SqlParameter("@Jsr_Code", SqlDbType.Char,7),
                    new SqlParameter("@Jb_Code", SqlDbType.VarChar,20),
                    new SqlParameter("@Bxlb_Code", SqlDbType.Char,6),
                    new SqlParameter("@Ry_YlCode", SqlDbType.VarChar,50),
                    new SqlParameter("@Ry_Name", SqlDbType.VarChar,100),
                    new SqlParameter("@Ry_Jc", SqlDbType.VarChar,50),
                    new SqlParameter("@Ry_Sex", SqlDbType.Char,2),
                    new SqlParameter("@Ry_Sfzh", SqlDbType.VarChar,18),
                    new SqlParameter("@Ry_Csdate", SqlDbType.SmallDateTime),
                    new SqlParameter("@Ry_Address", SqlDbType.VarChar,100),
                    new SqlParameter("@Ry_Tele", SqlDbType.VarChar,50),
                    new SqlParameter("@Ry_Memo", SqlDbType.VarChar,200),
                    new SqlParameter("@Ry_BlCode", SqlDbType.VarChar,50),
                    new SqlParameter("@Ry_RyDate", SqlDbType.DateTime),
                    new SqlParameter("@Ry_CyDate", SqlDbType.DateTime),
                    new SqlParameter("@Ry_ZyTs", SqlDbType.Int,4),
                    new SqlParameter("@Ry_CyJsr", SqlDbType.VarChar,10),
                    new SqlParameter("@Bl_Ph", SqlDbType.VarChar,50),
                    new SqlParameter("@Bl_M1", SqlDbType.Decimal,9),
                    new SqlParameter("@Bl_M2", SqlDbType.Decimal,5),
                    new SqlParameter("@Bl_M3", SqlDbType.Decimal,5),
                    new SqlParameter("@Bl_M4", SqlDbType.Decimal,5),
                    new SqlParameter("@Bl_M5", SqlDbType.Decimal,5),
                    new SqlParameter("@Bl_M6", SqlDbType.Decimal,5),
                    new SqlParameter("@Bl_M7", SqlDbType.Decimal,5),
                    new SqlParameter("@Bl_M8", SqlDbType.Decimal,5),
                    new SqlParameter("@Bl_M9", SqlDbType.Decimal,5),
                    new SqlParameter("@Bl_M10", SqlDbType.Decimal,5),
                    new SqlParameter("@Bl_M11", SqlDbType.Decimal,5),
                    new SqlParameter("@Bl_M12", SqlDbType.Decimal,5),
                    new SqlParameter("@Bl_M13", SqlDbType.Decimal,5),
                    new SqlParameter("@Bl_M14", SqlDbType.Decimal,5),
                    new SqlParameter("@Bl_M15", SqlDbType.Decimal,5),
                    new SqlParameter("@Bl_M16", SqlDbType.Decimal,5),
                    new SqlParameter("@Bl_M17", SqlDbType.Decimal,5),
                    new SqlParameter("@Bl_M_Yj", SqlDbType.Decimal,9),
                    new SqlParameter("@Bl_M_Sk", SqlDbType.Decimal,9),
                    new SqlParameter("@Bl_M_Th", SqlDbType.Decimal,9),
                    new SqlParameter("@Bl_Dc", SqlDbType.Bit,1),
                    new SqlParameter("@Jz_Code", SqlDbType.VarChar,12),
                    new SqlParameter("@Cy_Qr", SqlDbType.Char,2),
                    new SqlParameter("@Jb_Name", SqlDbType.VarChar,200),
                    new SqlParameter("@Yy_code", SqlDbType.Char,4),
                    new SqlParameter("@Bl_Code", SqlDbType.Char,14)};
            parameters[0].Value = Model.Ks_Code;
            parameters[1].Value = Model.Ys_Code;
            if (Model.Bc_Code == null)
            {
                parameters[2].Value = DBNull.Value;
            }
            else
            {
                parameters[2].Value = Model.Bc_Code;
            }
            parameters[3].Value = Model.Jsr_Code;
            parameters[4].Value = Model.Jb_Code;
            parameters[5].Value = Model.Bxlb_Code;
            parameters[6].Value = Model.Ry_YlCode;
            parameters[7].Value = Model.Ry_Name;
            parameters[8].Value = Model.Ry_Jc;
            parameters[9].Value = Model.Ry_Sex;
            parameters[10].Value = Model.Ry_Sfzh;
            parameters[11].Value = Model.Ry_Csdate;
            parameters[12].Value = Model.Ry_Address;
            parameters[13].Value = Model.Ry_Tele;
            parameters[14].Value = Model.Ry_Memo;
            parameters[15].Value = Model.Ry_BlCode;
            parameters[16].Value = Model.Ry_RyDate;
            if (Model.Ry_CyDate == null)
            {
                parameters[17].Value = DBNull.Value;
            }
            else
            {
                parameters[17].Value = Model.Ry_CyDate;
            }
            if (Model.Ry_ZyTs == null)
            {
                parameters[18].Value = DBNull.Value;
            }
            else
            {
                parameters[18].Value = Model.Ry_ZyTs;
            }

            parameters[19].Value = Model.Ry_CyJsr;
            parameters[20].Value = Model.Bl_Ph;
            parameters[21].Value = Model.Bl_M1;
            parameters[22].Value = Model.Bl_M2;
            parameters[23].Value = Model.Bl_M3;
            parameters[24].Value = Model.Bl_M4;
            parameters[25].Value = Model.Bl_M5;
            parameters[26].Value = Model.Bl_M6;
            parameters[27].Value = Model.Bl_M7;
            parameters[28].Value = Model.Bl_M8;
            parameters[29].Value = Model.Bl_M9;
            parameters[30].Value = Model.Bl_M10;
            parameters[31].Value = Model.Bl_M11;
            parameters[32].Value = Model.Bl_M12;
            parameters[33].Value = Model.Bl_M13;
            parameters[34].Value = Model.Bl_M14;
            parameters[35].Value = Model.Bl_M15;
            parameters[36].Value = Model.Bl_M16;
            parameters[37].Value = Model.Bl_M17;
            parameters[38].Value = Model.Bl_M_Yj;
            parameters[39].Value = Model.Bl_M_Sk;
            parameters[40].Value = Model.Bl_M_Th;
            parameters[41].Value = Model.Bl_Dc;
            parameters[42].Value = Model.Jz_Code;
            parameters[43].Value = Model.Cy_Qr;
            parameters[44].Value = Model.Jb_Name;
            parameters[45].Value = Model.Yy_code;
            parameters[46].Value = Model.Bl_Code;

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 更新一组数据
        /// </summary>
        public bool Update(Boolean Emr_GuiDang, string code)
        {
            try
            {
                List<string> sqlList = new List<string>();
                List<SqlParameter[]> sqlParaList = new List<SqlParameter[]>();

                StringBuilder strSql = new StringBuilder();
                strSql.Append("update Bl set Emr_GuiDang=@Emr_GuiDang  ");
                strSql.Append(" where Bl_Code =@Bl_Code");

                SqlParameter[] sqlPara ={
            new SqlParameter("@Emr_GuiDang",SqlDbType .Bit ),
            new SqlParameter("@Bl_Code",SqlDbType.VarChar )};
                sqlPara[0].Value = Emr_GuiDang;
                sqlPara[1].Value = code;

                sqlList.Add(strSql.ToString());
                sqlParaList.Add(sqlPara);

                strSql = new StringBuilder();
                strSql.Append("INSERT INTO dbo.Emr_GDlog  ( id,Bl_Code, Jsr_Code, GuiDang_State, Log_Date)");
                strSql.Append("VALUES  (@id,@Bl_Code, @Jsr_Code, @GuiDang_State,@Log_Date )");


                sqlPara = new SqlParameter[5]{
                    new SqlParameter("@id",SqlDbType.Int   ),
            new SqlParameter("@Bl_Code",SqlDbType.Char  ),
            new SqlParameter("@Jsr_Code",SqlDbType.Char ),
            new SqlParameter("@GuiDang_State",SqlDbType.Char,4 ),
            new SqlParameter("@Log_Date",SqlDbType.DateTime  )};

                sqlPara[0].Value = HisVar.HisVar.Sqldal.GetMaxID("id", "Emr_GDlog");
                sqlPara[1].Value = code;
                sqlPara[2].Value = HisVar.HisVar.JsrCode;
                sqlPara[3].Value = Emr_GuiDang == false ? "解档" : "归档";
                sqlPara[4].Value = DateTime.Now;

                sqlList.Add(strSql.ToString());
                sqlParaList.Add(sqlPara);

                int rows = HisVar.HisVar.Sqldal.ExecuteSql(sqlList, sqlParaList);
                if (rows > 0)
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
            catch (Exception ex)
            {
                return false;

            }

        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(string Yy_code, string Bl_Code)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from Bl ");
            strSql.Append(" where Yy_code=@Yy_code and Bl_Code=@Bl_Code ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Yy_code", SqlDbType.Char,4),
                    new SqlParameter("@Bl_Code", SqlDbType.Char,14)         };
            parameters[0].Value = Yy_code;
            parameters[1].Value = Bl_Code;

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.M_Bl GetModel(string Yy_code, string Bl_Code)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Yy_code,Bl_Code,Ks_Code,Ys_Code,Bc_Code,Jsr_Code,Jb_Code,Bxlb_Code,Ry_YlCode,Ry_Name,Ry_Jc,Ry_Sex,Ry_Sfzh,Ry_Csdate,Ry_Address,Ry_Tele,Ry_Memo,Ry_BlCode,Ry_RyDate,Ry_CyDate,Ry_ZyTs,Ry_CyJsr,Bl_Ph,Bl_M1,Bl_M2,Bl_M3,Bl_M4,Bl_M5,Bl_M6,Bl_M7,Bl_M8,Bl_M9,Bl_M10,Bl_M11,Bl_M12,Bl_M13,Bl_M14,Bl_M15,Bl_M16,Bl_M17,Bl_M_Yj,Bl_M_Sk,Bl_M_Th,Bl_Dc,Jz_Code,Cy_Qr,Jb_Name from Bl ");
            strSql.Append(" where Yy_code=@Yy_code and Bl_Code=@Bl_Code ");
            SqlParameter[] parameters = {
                    new SqlParameter("@Yy_code", SqlDbType.Char,4),
                    new SqlParameter("@Bl_Code", SqlDbType.Char,14)         };
            parameters[0].Value = Yy_code;
            parameters[1].Value = Bl_Code;

            Model.M_Bl Model = new Model.M_Bl();
            DataSet ds = HisVar.HisVar.Sqldal.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.M_Bl DataRowToModel(DataRow row)
        {
            Model.M_Bl Model = new Model.M_Bl();
            if (row != null)
            {
                if (row["Yy_code"] != null)
                {
                    Model.Yy_code = row["Yy_code"].ToString();
                }
                if (row["Bl_Code"] != null)
                {
                    Model.Bl_Code = row["Bl_Code"].ToString();
                }
                if (row["Ks_Code"] != null)
                {
                    Model.Ks_Code = row["Ks_Code"].ToString();
                }
                if (row["Ys_Code"] != null)
                {
                    Model.Ys_Code = row["Ys_Code"].ToString();
                }
                if (row["Bc_Code"] != null)
                {
                    Model.Bc_Code = row["Bc_Code"].ToString();
                }
                if (row["Jsr_Code"] != null)
                {
                    Model.Jsr_Code = row["Jsr_Code"].ToString();
                }
                if (row["Jb_Code"] != null)
                {
                    Model.Jb_Code = row["Jb_Code"].ToString();
                }
                if (row["Bxlb_Code"] != null)
                {
                    Model.Bxlb_Code = row["Bxlb_Code"].ToString();
                }
                if (row["Ry_YlCode"] != null)
                {
                    Model.Ry_YlCode = row["Ry_YlCode"].ToString();
                }
                if (row["Ry_Name"] != null)
                {
                    Model.Ry_Name = row["Ry_Name"].ToString();
                }
                if (row["Ry_Jc"] != null)
                {
                    Model.Ry_Jc = row["Ry_Jc"].ToString();
                }
                if (row["Ry_Sex"] != null)
                {
                    Model.Ry_Sex = row["Ry_Sex"].ToString();
                }
                if (row["Ry_Sfzh"] != null)
                {
                    Model.Ry_Sfzh = row["Ry_Sfzh"].ToString();
                }
                if (row["Ry_Csdate"] != null && row["Ry_Csdate"].ToString() != "")
                {
                    Model.Ry_Csdate = DateTime.Parse(row["Ry_Csdate"].ToString());
                }
                if (row["Ry_Address"] != null)
                {
                    Model.Ry_Address = row["Ry_Address"].ToString();
                }
                if (row["Ry_Tele"] != null)
                {
                    Model.Ry_Tele = row["Ry_Tele"].ToString();
                }
                if (row["Ry_Memo"] != null)
                {
                    Model.Ry_Memo = row["Ry_Memo"].ToString();
                }
                if (row["Ry_BlCode"] != null)
                {
                    Model.Ry_BlCode = row["Ry_BlCode"].ToString();
                }
                if (row["Ry_RyDate"] != null && row["Ry_RyDate"].ToString() != "")
                {
                    Model.Ry_RyDate = DateTime.Parse(row["Ry_RyDate"].ToString());
                }
                if (row["Ry_CyDate"] != null && row["Ry_CyDate"].ToString() != "")
                {
                    Model.Ry_CyDate = DateTime.Parse(row["Ry_CyDate"].ToString());
                }
                if (row["Ry_ZyTs"] != null && row["Ry_ZyTs"].ToString() != "")
                {
                    Model.Ry_ZyTs = int.Parse(row["Ry_ZyTs"].ToString());
                }
                if (row["Ry_CyJsr"] != null)
                {
                    Model.Ry_CyJsr = row["Ry_CyJsr"].ToString();
                }
                if (row["Bl_Ph"] != null)
                {
                    Model.Bl_Ph = row["Bl_Ph"].ToString();
                }
                if (row["Bl_M1"] != null && row["Bl_M1"].ToString() != "")
                {
                    Model.Bl_M1 = decimal.Parse(row["Bl_M1"].ToString());
                }
                if (row["Bl_M2"] != null && row["Bl_M2"].ToString() != "")
                {
                    Model.Bl_M2 = decimal.Parse(row["Bl_M2"].ToString());
                }
                if (row["Bl_M3"] != null && row["Bl_M3"].ToString() != "")
                {
                    Model.Bl_M3 = decimal.Parse(row["Bl_M3"].ToString());
                }
                if (row["Bl_M4"] != null && row["Bl_M4"].ToString() != "")
                {
                    Model.Bl_M4 = decimal.Parse(row["Bl_M4"].ToString());
                }
                if (row["Bl_M5"] != null && row["Bl_M5"].ToString() != "")
                {
                    Model.Bl_M5 = decimal.Parse(row["Bl_M5"].ToString());
                }
                if (row["Bl_M6"] != null && row["Bl_M6"].ToString() != "")
                {
                    Model.Bl_M6 = decimal.Parse(row["Bl_M6"].ToString());
                }
                if (row["Bl_M7"] != null && row["Bl_M7"].ToString() != "")
                {
                    Model.Bl_M7 = decimal.Parse(row["Bl_M7"].ToString());
                }
                if (row["Bl_M8"] != null && row["Bl_M8"].ToString() != "")
                {
                    Model.Bl_M8 = decimal.Parse(row["Bl_M8"].ToString());
                }
                if (row["Bl_M9"] != null && row["Bl_M9"].ToString() != "")
                {
                    Model.Bl_M9 = decimal.Parse(row["Bl_M9"].ToString());
                }
                if (row["Bl_M10"] != null && row["Bl_M10"].ToString() != "")
                {
                    Model.Bl_M10 = decimal.Parse(row["Bl_M10"].ToString());
                }
                if (row["Bl_M11"] != null && row["Bl_M11"].ToString() != "")
                {
                    Model.Bl_M11 = decimal.Parse(row["Bl_M11"].ToString());
                }
                if (row["Bl_M12"] != null && row["Bl_M12"].ToString() != "")
                {
                    Model.Bl_M12 = decimal.Parse(row["Bl_M12"].ToString());
                }
                if (row["Bl_M13"] != null && row["Bl_M13"].ToString() != "")
                {
                    Model.Bl_M13 = decimal.Parse(row["Bl_M13"].ToString());
                }
                if (row["Bl_M14"] != null && row["Bl_M14"].ToString() != "")
                {
                    Model.Bl_M14 = decimal.Parse(row["Bl_M14"].ToString());
                }
                if (row["Bl_M15"] != null && row["Bl_M15"].ToString() != "")
                {
                    Model.Bl_M15 = decimal.Parse(row["Bl_M15"].ToString());
                }
                if (row["Bl_M16"] != null && row["Bl_M16"].ToString() != "")
                {
                    Model.Bl_M16 = decimal.Parse(row["Bl_M16"].ToString());
                }
                if (row["Bl_M17"] != null && row["Bl_M17"].ToString() != "")
                {
                    Model.Bl_M17 = decimal.Parse(row["Bl_M17"].ToString());
                }
                if (row["Bl_M_Yj"] != null && row["Bl_M_Yj"].ToString() != "")
                {
                    Model.Bl_M_Yj = decimal.Parse(row["Bl_M_Yj"].ToString());
                }
                if (row["Bl_M_Sk"] != null && row["Bl_M_Sk"].ToString() != "")
                {
                    Model.Bl_M_Sk = decimal.Parse(row["Bl_M_Sk"].ToString());
                }
                if (row["Bl_M_Th"] != null && row["Bl_M_Th"].ToString() != "")
                {
                    Model.Bl_M_Th = decimal.Parse(row["Bl_M_Th"].ToString());
                }
                if (row["Bl_Dc"] != null && row["Bl_Dc"].ToString() != "")
                {
                    if ((row["Bl_Dc"].ToString() == "1") || (row["Bl_Dc"].ToString().ToLower() == "true"))
                    {
                        Model.Bl_Dc = true;
                    }
                    else
                    {
                        Model.Bl_Dc = false;
                    }
                }
                if (row["Jz_Code"] != null)
                {
                    Model.Jz_Code = row["Jz_Code"].ToString();
                }
                if (row["Cy_Qr"] != null)
                {
                    Model.Cy_Qr = row["Cy_Qr"].ToString();
                }
                if (row["Jb_Name"] != null)
                {
                    Model.Jb_Name = row["Jb_Name"].ToString();
                }
            }
            return Model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT bl.Yy_code,Bl.Bl_Code, bl.Ks_Code,Ks_Name, bl.Ys_Code,Ys_Name, bl.Bc_Code,Bq_Name,Bc_Name ,");
            strSql.Append("bl.Jsr_Code,Jsr_Name, Jb_Code, Bxlb_Code, Ry_YlCode, Ry_Name, Ry_Jc, Ry_Sex, Ry_Sfzh, Ry_Csdate, Ry_Address, ");
            strSql.Append("Ry_Tele, Ry_Memo, Ry_BlCode, Ry_RyDate, Ry_CyDate, Ry_ZyTs, Ry_CyJsr,");
            strSql.Append(" Bl_Ph, Bl_M1, Bl_M2, Bl_M3, Bl_M4, Bl_M5, Bl_M6, Bl_M7, Bl_M8, Bl_M9, Bl_M10, Bl_M11,");
            strSql.Append(" Bl_M12, Bl_M13, Bl_M14, Bl_M15, Bl_M16, Bl_M17, Bl_M_Yj, Bl_M_Sk, Bl_M_Th, Bl_Dc, Jz_Code,");
            strSql.Append(" Cy_Qr, Jb_Name, Emr_GuiDang ,'false' isSelected ,Zk_Pf,ZkDj_name FROM dbo.Bl LEFT JOIN dbo.V_YyBc ON V_YyBc.Bc_Code = Bl.Bc_Code");
            strSql.Append(" LEFT JOIN dbo.Zd_YyYs ON Zd_YyYs.Ys_Code = Bl.Ys_Code LEFT JOIN dbo.Zd_YyKs ON Zd_YyKs.Ks_Code = Bl.Ks_Code");
            strSql.Append(" LEFT JOIN dbo.Zd_YyJsr ON Zd_YyJsr.Jsr_Code = Bl.Jsr_Code");
            strSql.Append(" LEFT JOIN dbo.emr_blzk1 ON emr_blzk1.Bl_Code = Bl.Bl_Code");
            strSql.Append(" LEFT JOIN dbo.Emr_ZkDj ON Emr_ZkDj.ZkDj_Code = emr_blzk1.ZkDj_Code ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }


        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetListRy()
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(" SELECT bl.Bl_Code,Ry_Name,Ry_Sfzh,emr_bl.Mblb_Code,emr_bl.Mblb_Name,emr_bl.Mb_Code,emr_bl.Mb_Name,");
            strSql.Append(" emr_bl.EmrTimeInterval,bl.blTimeInterval FROM ");
            strSql.Append(" (SELECT bl.Bl_Code,Ry_Name,Ry_Sfzh,DATEDIFF(HOUR,MAX(Ry_RyDate),GETDATE()) blTimeInterval");
            strSql.Append(" FROM bl WHERE Ry_CyJsr IS  NULL GROUP BY  bl.Bl_Code,Ry_Name,Ry_Sfzh) bl");
            strSql.Append("  JOIN (SELECT Bl_Code,dbo.Emr_Bl.Mb_Code,Emr_Bl.Mb_Name,dbo.Emr_Mb.Mblb_Code,Mblb_Name,");
            strSql.Append(" DATEDIFF(HOUR,MAX(AddDate),GETDATE()) EmrTimeInterval");
            strSql.Append(" FROM dbo.Emr_Bl JOIN dbo.Emr_Mb ON Emr_Mb.Mb_Code = Emr_Bl.Mb_Code");
            strSql.Append(" JOIN dbo.Emr_Mblb ON Emr_Mblb.Mblb_Code = Emr_Mb.Mblb_Code ");
            strSql.Append(" GROUP BY Bl_Code,dbo.Emr_Bl.Mb_Code,Emr_Bl.Mb_Name,dbo.Emr_Mb.Mblb_Code,Mblb_Name");
            strSql.Append(" ) emr_bl ON emr_bl.Bl_Code = bl.Bl_Code");

            strSql.Append(" union all SELECT bl.Bl_Code,Ry_Name,Ry_Sfzh,null,null,null,null,null, ");
            strSql.Append(" bl.blTimeInterval FROM  ");
            strSql.Append(" (SELECT bl.Bl_Code,Ry_Name,Ry_Sfzh,DATEDIFF(HOUR,MAX(Ry_RyDate),GETDATE()) blTimeInterval");
            strSql.Append(" FROM bl WHERE Ry_CyJsr IS  NULL GROUP BY  bl.Bl_Code,Ry_Name,Ry_Sfzh) bl");
            strSql.Append(" where not exists (select 1 from ");
            strSql.Append("   (SELECT distinct Bl_Code");
            strSql.Append(" FROM dbo.Emr_Bl JOIN dbo.Emr_Mb ON Emr_Mb.Mb_Code = Emr_Bl.Mb_Code");
            strSql.Append(" JOIN dbo.Emr_Mblb ON Emr_Mblb.Mblb_Code = Emr_Mb.Mblb_Code ");
            strSql.Append(" GROUP BY Bl_Code,dbo.Emr_Bl.Mb_Code,Emr_Bl.Mb_Name,dbo.Emr_Mb.Mblb_Code,Mblb_Name");
            strSql.Append(" ) emr_bl where emr_bl.bl_code=bl.bl_code)");
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }
        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetListCy()
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT bl.Bl_Code,Ry_Name,Ry_Sfzh,emr_bl.Mblb_Code,emr_bl.Mblb_Name,emr_bl.Mb_Code,emr_bl.Mb_Name, ");
            strSql.Append(" emr_bl.EmrTimeInterval,bl.blTimeInterval FROM  ");
            strSql.Append(" (SELECT bl.Bl_Code,Ry_Name,Ry_Sfzh,DATEDIFF(HOUR,MAX(Ry_CyDate),GETDATE()) blTimeInterval");
            strSql.Append(" FROM bl WHERE Ry_CyJsr IS NOT NULL GROUP BY  bl.Bl_Code,Ry_Name,Ry_Sfzh) bl");
            strSql.Append("  JOIN (SELECT Bl_Code,dbo.Emr_Bl.Mb_Code,Emr_Bl.Mb_Name,dbo.Emr_Mb.Mblb_Code,Mblb_Name,");
            strSql.Append(" DATEDIFF(HOUR,MAX(AddDate),GETDATE()) EmrTimeInterval");
            strSql.Append(" FROM dbo.Emr_Bl JOIN dbo.Emr_Mb ON Emr_Mb.Mb_Code = Emr_Bl.Mb_Code");
            strSql.Append(" JOIN dbo.Emr_Mblb ON Emr_Mblb.Mblb_Code = Emr_Mb.Mblb_Code ");
            strSql.Append(" GROUP BY Bl_Code,dbo.Emr_Bl.Mb_Code,Emr_Bl.Mb_Name,dbo.Emr_Mb.Mblb_Code,Mblb_Name");
            strSql.Append(" ) emr_bl ON emr_bl.Bl_Code = bl.Bl_Code");

            strSql.Append(" union all SELECT bl.Bl_Code,Ry_Name,Ry_Sfzh,null,null,null,null,null, ");
            strSql.Append(" bl.blTimeInterval FROM  ");
            strSql.Append(" (SELECT bl.Bl_Code,Ry_Name,Ry_Sfzh,DATEDIFF(HOUR,MAX(Ry_CyDate),GETDATE()) blTimeInterval");
            strSql.Append(" FROM bl WHERE Ry_CyJsr IS NOT NULL GROUP BY  bl.Bl_Code,Ry_Name,Ry_Sfzh) bl");
            strSql.Append(" where not exists (select 1 from ");
            strSql.Append("   (SELECT distinct Bl_Code");
            strSql.Append(" FROM dbo.Emr_Bl JOIN dbo.Emr_Mb ON Emr_Mb.Mb_Code = Emr_Bl.Mb_Code");
            strSql.Append(" JOIN dbo.Emr_Mblb ON Emr_Mblb.Mblb_Code = Emr_Mb.Mblb_Code ");
            strSql.Append(" GROUP BY Bl_Code,dbo.Emr_Bl.Mb_Code,Emr_Bl.Mb_Name,dbo.Emr_Mb.Mblb_Code,Mblb_Name");
            strSql.Append(" ) emr_bl where emr_bl.bl_code=bl.bl_code)");

            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" Yy_code,Bl_Code,Ks_Code,Ys_Code,Bc_Code,Jsr_Code,Jb_Code,Bxlb_Code,Ry_YlCode,Ry_Name,Ry_Jc,Ry_Sex,Ry_Sfzh,Ry_Csdate,Ry_Address,Ry_Tele,Ry_Memo,Ry_BlCode,Ry_RyDate,Ry_CyDate,Ry_ZyTs,Ry_CyJsr,Bl_Ph,Bl_M1,Bl_M2,Bl_M3,Bl_M4,Bl_M5,Bl_M6,Bl_M7,Bl_M8,Bl_M9,Bl_M10,Bl_M11,Bl_M12,Bl_M13,Bl_M14,Bl_M15,Bl_M16,Bl_M17,Bl_M_Yj,Bl_M_Sk,Bl_M_Th,Bl_Dc,Jz_Code,Cy_Qr,Jb_Name ");
            strSql.Append(" FROM Bl ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM Bl ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.Bl_Code desc");
            }
            strSql.Append(")AS Row, T.*  from Bl T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

        /*
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetList(int PageSize,int PageIndex,string strWhere)
        {
            SqlParameter[] parameters = {
                    new SqlParameter("@tblName", SqlDbType.VarChar, 255),
                    new SqlParameter("@fldName", SqlDbType.VarChar, 255),
                    new SqlParameter("@PageSize", SqlDbType.Int),
                    new SqlParameter("@PageIndex", SqlDbType.Int),
                    new SqlParameter("@IsReCount", SqlDbType.Bit),
                    new SqlParameter("@OrderType", SqlDbType.Bit),
                    new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
                    };
            parameters[0].Value = "Bl";
            parameters[1].Value = "Bl_Code";
            parameters[2].Value = PageSize;
            parameters[3].Value = PageIndex;
            parameters[4].Value = 0;
            parameters[5].Value = 0;
            parameters[6].Value = strWhere;	
            return HisVar.HisVar.Sqldal.RunProcedure("UP_GetRecordByPage",parameters,"ds");
        }*/

        #endregion  BasicMethod
        #region  ExtensionMethod

        public string GetBxBlCodeMaxCode(string bl_Code)
        {
            string prefix = bl_Code;
            string max = (string)(prefix + HisVar.HisVar.Sqldal.F_MaxCode("SELECT  MAX(SUBSTRING(Bx_BlCode,15,2)) FROM BL WHERE BL_CODE = '" + bl_Code + "'", 2));
            return max;
        }

        public bool UpdateBxMoney(string bl_Code, decimal bl_BxMoney)
        {
            int rows = HisVar.HisVar.Sqldal.ExecuteSql("UPDATE BL SET Bl_BxMoney = " + bl_BxMoney + " WHERE BL_CODE = '" + bl_Code + "'");
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        #endregion  ExtensionMethod
    }
}

