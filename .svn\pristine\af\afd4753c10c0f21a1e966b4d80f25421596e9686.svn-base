﻿Imports System.Data.SqlClient
Imports HisControl
Imports Stimulsoft.Report
Imports ZTHisPublicFunction

Public Class Zy_Cf3

#Region "定义__变量"
    Dim My_Dataset As New DataSet
#End Region

#Region "传参"
    'Dim Rform As BaseForm
    Dim Rrow As DataRow
#End Region

    Public Sub New(ByVal tform As BaseForm, ByVal trow As DataRow)

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        'Rform = tform
        Rrow = trow

        ' 在 InitializeComponent() 调用之后添加任何初始化。

    End Sub

    Private Sub Zy_Cf3_FormClosed(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosedEventArgs) Handles Me.FormClosed
        'Rform.Visible = True
    End Sub

    Private Sub Zy_Cf3_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        'Rform.Visible = False
        Call Form_Init()
        Call Zb_Show()

    End Sub

#Region "窗体__事件"

    Private Sub Form_Init()
        Dim My_Grid As New BaseClass.C_Grid(Me.C1TrueDBGrid1)
        With My_Grid
            .Init_Grid()
            .P_Dock(BaseClass.C_Grid.Dock.Fill)
            .Init_Column("名称", "Xm_Name", 160, "左", "")
            .Init_Column("规格", "Mx_Gg", 100, "左", "")
            .Init_Column("产地", "Mx_Cd", 200, "左", "")
            .Init_Column("有效期", "Yp_Yxq", 75, "中", "yyyy-MM-dd")
            .Init_Column("数量", "Cf_Sl", 60, "右", "###,###,##0.00##")
            .Init_Column("单位", "Xm_Dw", 45, "左", "")
            .Init_Column("单价", "Cf_Dj", 70, "右", "###,###,##0.00####")
            .Init_Column("金额", "Cf_Money", 80, "右", "###,###,##0.00##")
            .Init_Column("用法用量", "Yp_Yfyl", 200, "左", "")
            .Init_Column("类别", "Cf_Lb", 70, "中", "")
            .Init_Column("是否检查", "Xm_Wc", 70, "中", "")
        End With
        C1TrueDBGrid1.VScrollBar.Style = C1.Win.C1TrueDBGrid.ScrollBarStyleEnum.Automatic
    End Sub

#End Region

#Region "清空__显示"

    Private Sub Zb_Show()   '显示记录
        With Rrow
            C1TextBox1.Text = .Item("Cf_Code") & ""                                   '出库编码
            C1TextBox2.Text = .Item("Ry_Name") & ""                                   '客户编码
            C1TextBox3.Text = .Item("Ks_Name") & ""
            C1TextBox4.Text = .Item("Ry_Sex") & ""
            C1TextBox5.Text = .Item("Ys_Name") & ""
            C1TextBox6.Text = .Item("Bc_Name") & ""
            C1TextBox8.Text = .Item("Bxlb_Name") & ""
            C1TextBox7.Text = .Item("Yf_Name") & ""
            C1TextBox1.ReadOnly = True
            C1TextBox1.BackColor = SystemColors.Info
            C1TextBox2.ReadOnly = True
            C1TextBox2.BackColor = SystemColors.Info
            C1TextBox3.ReadOnly = True
            C1TextBox3.BackColor = SystemColors.Info
            C1TextBox4.ReadOnly = True
            C1TextBox4.BackColor = SystemColors.Info
            C1TextBox5.ReadOnly = True
            C1TextBox5.BackColor = SystemColors.Info
            C1TextBox6.ReadOnly = True
            C1TextBox6.BackColor = SystemColors.Info
            C1TextBox8.ReadOnly = True
            C1TextBox8.BackColor = SystemColors.Info
            C1TextBox7.ReadOnly = True
            C1TextBox7.BackColor = SystemColors.Info
        End With

        Call P_Data_Show()

    End Sub


    Private Sub P_Data_Show()   '从表数据
        '主表记录
        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "Select Cf_Id,Cf_Dj,Cf_Sl,Cf_Money,''Mx_Gyzz,'' As Mx_Gg,''Mx_Cd,Xm_Dw,Xm_Name,Cf_Lb,''Yp_Ph,Null Yp_Yxq,Xmlb_Code as Dl_Code,Xm_Wc,''Yp_Yfyl,'项目' as V_Lb From Bl_Cfxm,Zd_Ml_Xm3 Where  Bl_Cfxm.Xm_Code=Zd_Ml_Xm3.Xm_Code And Cf_Code='" & Rrow.Item("Cf_Code") & "' Union All" & " Select Cf_Id,Cf_Dj,Cf_Sl,Cf_Money,Mx_Gyzz,Mx_Gg,Mx_Cd ,Mx_XsDw,Yp_Name,Cf_Lb,V_YpKc.Yp_Ph,V_YpKc.Yp_Yxq,Dl_Code,'' as Xm_Wc,Yp_Yfyl,'药品' as V_lb From Bl_Cfyp,V_YpKc Where Bl_Cfyp.Xx_Code=V_YpKc.Xx_Code And Cf_Code='" & Rrow.Item("Cf_Code") & "' Order By Cf_Id", "明细", True)
        C1TrueDBGrid1.SetDataBinding(My_Dataset, "明细", True)
    End Sub


#End Region

    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click
        Call Print_Zy_Cfj()
    End Sub

    Private Sub Print_Zy_Cfj()
        Dim rpt As New StiReport
        Dim zycf As ZTHisPublicFunction.InpatientPrescription.IInpatientPrescription
        zycf = ZTHisPublicFunction.InpatientPrescription.InpatientPrescriptionFactory.CreateInpatientPrescriptionObject()
        Dim prescriptionPara As New prescriptionPara()
        prescriptionPara.Mz_Code = Rrow("Cf_Code")
        prescriptionPara.Ry_Name = Rrow("Ry_Name")
        prescriptionPara.Ry_Sex = Rrow("Ry_Sex")
        prescriptionPara.Ks_Name = Rrow("Ks_Name")
        prescriptionPara.Bc_Name = Rrow.Item("Bc_Name") & ""
        prescriptionPara.Ys_Name = Rrow("Ys_Name")
        prescriptionPara.Mz_Money = CDbl(Rrow("Cf_Money"))

        Dim rd As SqlDataReader = HisVar.HisVar.Sqldal.ExecuteReader("SELECT Jb_Name,Ry_BlCode,DATEDIFF(yyyy,Ry_Csdate,getdate()) AS Ry_Age FROM Bl Where Bl_Code='" & Rrow("Bl_Code") & "' ")
        While rd.Read
            prescriptionPara.Ry_Age = rd.Item("Ry_Age") & "" '年龄
            prescriptionPara.Jb_Name = rd.Item("Jb_Name") & "" '疾病
            prescriptionPara.Ry_BlCode = rd.Item("Ry_BlCode") & "" '病历号
        End While
        rd.Close()
        prescriptionPara.Bxlb_Name = Rrow.Item("Bxlb_Name") & "" '类别
        rpt = zycf.Print(prescriptionPara)
        rpt.Show()
        Exit Sub

        Dim V_ColId As Integer
        Dim V_TbRowCount As Integer
        Dim V_Newrow As DataRow
        If My_Dataset.Tables("处方明细1") IsNot Nothing Then My_Dataset.Tables("处方明细1").Clear()

        Dim Str As String = "select Mx_Code,Cf_Id,Mx_Gg+'   ×' as Mx_Gg,Cf_Sl,Mx_XsDw,'用法： '+isnull(Yp_Yfyl,'') as Yp_Yfyl,Cf_Money,IsJb,Yp_Name,Jx_Name from Bl_CfYp,V_Ypkc where Bl_CfYp.Xx_Code=V_Ypkc.Xx_Code And Cf_Code='" & C1TextBox1.Text & "'  and Dl_Code='01' Order By Cf_Id"

        Dim My_Adapter As SqlDataAdapter = New SqlDataAdapter(Str, My_Cn)
        My_Adapter.Fill(My_Dataset, "处方明细1")



        If My_Dataset.Tables("处方明细1").Rows.Count Mod 5 <> 0 Then
            For V_TbRowCount = 1 To 5 - (My_Dataset.Tables("处方明细1").Rows.Count Mod 5)
                V_Newrow = My_Dataset.Tables("处方明细1").NewRow
                With V_Newrow

                    .Item("Mx_Code") = DBNull.Value
                    .Item("Cf_Id") = 2147483647
                    .Item("Yp_Name") = DBNull.Value
                    .Item("Mx_Gg") = DBNull.Value
                    .Item("Cf_Sl") = DBNull.Value
                    .Item("Mx_XsDw") = DBNull.Value
                    .Item("Yp_Yfyl") = DBNull.Value
                    .Item("Cf_Money") = DBNull.Value
                    .Item("Jx_Name") = DBNull.Value
                    '.Item("IsJb1") = DBNull.Value
                End With
                My_Dataset.Tables("处方明细1").Rows.Add(V_Newrow)
                V_Newrow.AcceptChanges()
            Next
        End If
        V_ColId = BaseFunc.BaseFunc.Edit_Col0(My_Dataset.Tables("处方明细1"), 0, 5)




        'Str = "select Mx_Code,Mx_Gg+'   ×' as Mx_Gg,sum(Cf_Sl) as Cf_Sl,Mx_XsDw,'用法： '+isnull(Yp_Yfyl,'') as Yp_Yfyl,Sum(Cf_Money) as Cf_Money,IsJb,Yp_Name from Bl_CfYp,V_Ypkc where Bl_CfYp.Xx_Code=V_Ypkc.Xx_Code And Cf_Code='" & C1TextBox1.Text & "'  and Dl_Code='02' Group by Mx_Code,Yp_Name,Mx_Gg,Mx_XsDw,isjb,Yp_Yfyl"
        Str = "select Mx_Code,Cf_Id,Mx_Gg+'   ×' as Mx_Gg,Cf_Sl,Mx_XsDw,'用法： '+isnull(Yp_Yfyl,'') as Yp_Yfyl,Cf_Money,IsJb,Yp_Name,Jx_Name from Bl_CfYp,V_Ypkc where Bl_CfYp.Xx_Code=V_Ypkc.Xx_Code And Cf_Code='" & C1TextBox1.Text & "'  and Dl_Code='02' Order by Cf_Id"

        My_Adapter = New SqlDataAdapter(Str, My_Cn)
        My_Adapter.Fill(My_Dataset, "处方明细1")



        If My_Dataset.Tables("处方明细1").Rows.Count Mod 5 <> 0 Then
            For V_TbRowCount = 1 To 5 - (My_Dataset.Tables("处方明细1").Rows.Count Mod 5)
                V_Newrow = My_Dataset.Tables("处方明细1").NewRow
                With V_Newrow

                    .Item("Mx_Code") = DBNull.Value
                    .Item("Cf_Id") = 2147483647
                    .Item("Yp_Name") = DBNull.Value
                    .Item("Mx_Gg") = DBNull.Value
                    .Item("Cf_Sl") = DBNull.Value
                    .Item("Mx_XsDw") = DBNull.Value
                    .Item("Yp_Yfyl") = DBNull.Value
                    .Item("Cf_Money") = DBNull.Value
                    .Item("Jx_Name") = DBNull.Value
                    '.Item("IsJb1") = DBNull.Value
                End With
                My_Dataset.Tables("处方明细1").Rows.Add(V_Newrow)
                V_Newrow.AcceptChanges()
            Next
        End If
        V_ColId = BaseFunc.BaseFunc.Edit_Col0(My_Dataset.Tables("处方明细1"), V_ColId + 1, 5)


        Str = "select Mx_Code,Cf_Id,Mx_Gg+'   ×' as Mx_Gg,Cf_Sl,Mx_XsDw,'用法： '+isnull(Yp_Yfyl,'') as Yp_Yfyl,Cf_Money,IsJb,Yp_Name,Jx_Name from Bl_CfYp,V_Ypkc where Bl_CfYp.Xx_Code=V_Ypkc.Xx_Code And Cf_Code='" & C1TextBox1.Text & "'  and Dl_Code='03' Order By Cf_Id"

        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, Str, "中药处方", True)

        '中药处方单张方上药品数量无限制，尽可能多
        If My_Dataset.Tables("中药处方").Rows.Count Mod 40 <> 0 Then
            For V_TbRowCount = 1 To 40 - (My_Dataset.Tables("中药处方").Rows.Count Mod 40)
                V_Newrow = My_Dataset.Tables("中药处方").NewRow
                With V_Newrow
                    .Item("Mx_Code") = V_TbRowCount
                    .Item("Cf_Id") = 2147483500 + V_TbRowCount
                    .Item("Yp_Name") = DBNull.Value
                    .Item("Mx_Gg") = DBNull.Value
                    .Item("Cf_Sl") = DBNull.Value
                    .Item("Mx_XsDw") = DBNull.Value
                    .Item("Yp_Yfyl") = DBNull.Value
                    .Item("Cf_Money") = DBNull.Value
                    .Item("Jx_Name") = DBNull.Value

                End With
                My_Dataset.Tables("中药处方").Rows.Add(V_Newrow)
                V_Newrow.AcceptChanges()
            Next
        End If

        V_ColId = BaseFunc.BaseFunc.Edit_Col0(My_Dataset.Tables("中药处方"), 0, 40)



        Str = "select Mx_Code,Cf_Id,Mx_Gg+'   ×' as Mx_Gg,Cf_Sl,Mx_XsDw,'用法： '+isnull(Yp_Yfyl,'') as Yp_Yfyl,Cf_Money,IsJb,Yp_Name,''Jx_Name from Bl_CfYp,V_Ypkc where Bl_CfYp.Xx_Code=V_Ypkc.Xx_Code And Cf_Code='" & C1TextBox1.Text & "'  and Dl_Code='04' Order By Cf_Id"

        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, Str, "卫材处方", True)
        If My_Dataset.Tables("卫材处方").Rows.Count Mod 20 <> 0 Then
            For V_TbRowCount = 1 To 20 - (My_Dataset.Tables("卫材处方").Rows.Count Mod 20)
                V_Newrow = My_Dataset.Tables("卫材处方").NewRow
                With V_Newrow
                    .Item("Mx_Code") = V_TbRowCount
                    .Item("Cf_Id") = 2147483500 + V_TbRowCount
                    .Item("Yp_Name") = DBNull.Value
                    .Item("Mx_Gg") = DBNull.Value
                    .Item("Cf_Sl") = DBNull.Value
                    .Item("Mx_XsDw") = DBNull.Value
                    .Item("Yp_Yfyl") = DBNull.Value
                    .Item("Cf_Money") = DBNull.Value
                    .Item("Jx_Name") = DBNull.Value

                End With
                My_Dataset.Tables("卫材处方").Rows.Add(V_Newrow)
                V_Newrow.AcceptChanges()
            Next
        End If
        V_ColId = BaseFunc.BaseFunc.Edit_Col0(My_Dataset.Tables("卫材处方"), 0, 20)



        Str = "select Bl_CfXm.Xm_Code as Mx_Code,Cf_Id,'' as Mx_Gg,Cf_Sl,Xm_Dw as Mx_XsDw,'' as Yp_Yfyl,Cf_Money,'' as IsJb,Xm_Name as Yp_Name,''Jx_Name from Bl_CfXm,Zd_Ml_Xm3 where Bl_CfXm.Xm_Code=Zd_Ml_Xm3.Xm_Code And Cf_Code='" & C1TextBox1.Text & "'  Order By Cf_Id"

        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, Str, "诊疗处方", True)

        If My_Dataset.Tables("诊疗处方").Rows.Count Mod 20 <> 0 Then
            For V_TbRowCount = 1 To 20 - (My_Dataset.Tables("诊疗处方").Rows.Count Mod 20)
                V_Newrow = My_Dataset.Tables("诊疗处方").NewRow
                With V_Newrow

                    .Item("Mx_Code") = V_TbRowCount
                    .Item("Cf_Id") = 2147483500 + V_TbRowCount
                    .Item("Yp_Name") = DBNull.Value
                    .Item("Mx_Gg") = DBNull.Value
                    .Item("Cf_Sl") = DBNull.Value
                    .Item("Mx_XsDw") = DBNull.Value
                    .Item("Yp_Yfyl") = DBNull.Value
                    .Item("Cf_Money") = DBNull.Value
                    .Item("Jx_Name") = DBNull.Value

                End With
                My_Dataset.Tables("诊疗处方").Rows.Add(V_Newrow)
                V_Newrow.AcceptChanges()
            Next
        End If
        V_ColId = BaseFunc.BaseFunc.Edit_Col0(My_Dataset.Tables("诊疗处方"), 0, 20)



        Dim StiRpt As New StiReport
        StiRpt.Load(".\Rpt\住院处方表(二分之一A4).mrt")
        ' StiRpt.Load(".\Rpt\住院处方表.mrt")
        StiRpt.ReportName = "住院处方表"
        StiRpt.RegData(My_Dataset.Tables("处方明细1"))
        StiRpt.RegData(My_Dataset.Tables("中药处方"))
        StiRpt.RegData(My_Dataset.Tables("卫材处方"))
        StiRpt.RegData(My_Dataset.Tables("诊疗处方"))

        If My_Dataset.Tables("处方明细1").Rows.Count > 0 Then
            StiRpt.Pages(0).Enabled = True
        Else
            StiRpt.Pages(0).Enabled = False
        End If

        If My_Dataset.Tables("中药处方").Rows.Count > 0 Then
            StiRpt.Pages(1).Enabled = True
        Else
            StiRpt.Pages(1).Enabled = False
        End If

        If My_Dataset.Tables("卫材处方").Rows.Count > 0 Then
            StiRpt.Pages(2).Enabled = True
        Else
            StiRpt.Pages(2).Enabled = False
        End If

        If My_Dataset.Tables("诊疗处方").Rows.Count > 0 Then
            StiRpt.Pages(3).Enabled = True
        Else
            StiRpt.Pages(3).Enabled = False
        End If


        StiRpt.Compile()
        StiRpt("标题") = HisVar.HisVar.WsyName & "住院处方笺"
        StiRpt("处方编码") = C1TextBox1.Text '门诊编号
        StiRpt("打印时间") = Format(Now, "yyyy年MM月dd日")
        StiRpt("患者姓名") = C1TextBox2.Text '患者姓名
        StiRpt("患者性别") = C1TextBox4.Text '患者性别
        StiRpt("科室") = C1TextBox3.Text '科室
        StiRpt("床位号") = C1TextBox6.Text '床位号
        StiRpt("医生姓名") = C1TextBox5.Text
        StiRpt("金额") = Format(CDbl(Rrow("Cf_Money")), "#0.00")
        StiRpt("经手人") = HisVar.HisVar.JsrName
        StiRpt("类别") = Rrow.Item("Bxlb_Name") & "" '类别

        Dim My_Reader As SqlDataReader = HisVar.HisVar.Sqldal.ExecuteReader("SELECT Jb_Name,Ry_BlCode,DATEDIFF(yyyy,Ry_Csdate,getdate()) AS Ry_Age FROM Bl Where Bl_Code='" & Rrow("Bl_Code") & "' ")
        While My_Reader.Read
            StiRpt("年龄") = My_Reader.Item("Ry_Age") & "" '年龄
            StiRpt("疾病") = My_Reader.Item("Jb_Name") & "" '疾病
            StiRpt("病历号") = My_Reader.Item("Ry_BlCode") & "" '病历号
        End While
        My_Reader.Close()

        ' StiRpt.Design()
        StiRpt.Show()

    End Sub

    Private Sub Button2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button2.Click
        Dim str As String = "Select Cf_Id,Ks_Name as Xm_KsName,Xm_Name,Xm_Dw,Cf_Sl,Cf_Money from Zd_Ml_Xm3,Bl_CfXm,Zd_Yyks_Xm,Zd_YyKs  where Zd_Yyks_xm.Ks_Code=Zd_YyKs.Ks_Code and Bl_CfXm.Xm_Code=Zd_Yyks_xm.Xm_Code and Bl_CfXm.Xm_Code=Zd_Ml_Xm3.Xm_Code and Cf_Code='" & C1TextBox1.Text & "'"
        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, str, "诊疗卡", True)

        Dim StiRpt As New StiReport
        StiRpt.Load(".\Rpt\诊疗卡.mrt")
        StiRpt.ReportName = "诊疗卡"
        StiRpt.RegData(My_Dataset.Tables("诊疗卡"))
        StiRpt.Compile()
        StiRpt("患者姓名") = Rrow.Item("Ry_Name")
        StiRpt("性别") = Rrow.Item("Ry_Sex")
        Dim My_Reader As SqlDataReader = HisVar.HisVar.Sqldal.ExecuteReader("SELECT Jb_Name,Ry_BlCode,DATEDIFF(yyyy,Ry_Csdate,getdate()) AS Ry_Age FROM Bl Where Bl_Code='" & Rrow("Bl_Code") & "' ")
        While My_Reader.Read
            StiRpt("年龄") = My_Reader.Item("Ry_Age") & "" '年龄
            StiRpt("疾病") = My_Reader.Item("Jb_Name") & "" '疾病
        End While
        My_Reader.Close()
        StiRpt("床位") = Rrow.Item("Bc_Name")
        StiRpt("处方科室") = Rrow.Item("Ks_Name")
        StiRpt("处方医生") = Rrow.Item("Ys_Name") & ""
        StiRpt("打印时间") = Format(Now, "yyyy-MM-dd")
        ' StiRpt.Design()
        StiRpt.Show()
    End Sub
End Class