﻿/**  版本信息模板在安装目录下，可自行修改。
* M_Emr_GDlog.cs
*
* 功 能： N/A
* 类 名： M_Emr_GDlog
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/8/29 11:28:58   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace ModelOld
{
	/// <summary>
	/// M_Emr_GDlog:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class M_Emr_GDlog
	{
		public M_Emr_GDlog()
		{}
		#region Model
		private long _id;
		private string _bl_code;
		private string _jsr_code;
		private string _guidang_state;
		private DateTime? _log_date= DateTime.Now;
		/// <summary>
		/// 
		/// </summary>
		public long id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Bl_Code
		{
			set{ _bl_code=value;}
			get{return _bl_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Jsr_Code
		{
			set{ _jsr_code=value;}
			get{return _jsr_code;}
		}
		/// <summary>
		/// 归档、反归档
		/// </summary>
		public string GuiDang_State
		{
			set{ _guidang_state=value;}
			get{return _guidang_state;}
		}
		/// <summary>
		/// 
		/// </summary>
		public DateTime? Log_Date
		{
			set{ _log_date=value;}
			get{return _log_date;}
		}
		#endregion Model

	}
}

