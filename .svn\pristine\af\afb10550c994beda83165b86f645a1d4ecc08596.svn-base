﻿/**  版本信息模板在安装目录下，可自行修改。
* D_Materials_Stock.cs
*
* 功 能： N/A
* 类 名： D_Materials_Stock
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016-11-26 15:05:16   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using System.Collections.Generic;

namespace DAL
{
    /// <summary>
    /// 数据访问类:D_Materials_Stock
    /// </summary>
    public partial class D_Materials_Stock
    {
        public D_Materials_Stock()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(string MaterialsStock_Code)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from Materials_Stock");
            strSql.Append(" where MaterialsStock_Code=@MaterialsStock_Code ");
            SqlParameter[] parameters = {
					new SqlParameter("@MaterialsStock_Code", SqlDbType.Char,16)			};
            parameters[0].Value = MaterialsStock_Code;

            return HisVar.HisVar.Sqldal.Exists(strSql.ToString(), parameters);
        }
        public bool Exists(string Materials_Code, string MaterialsWh_Code, string MaterialsLot, DateTime MaterialsExpiryDate, decimal MaterialsStore_Price)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from Materials_Stock");
            strSql.Append(" where Materials_Code=@Materials_Code ");
            strSql.Append(" and MaterialsWh_Code=@MaterialsWh_Code ");
            strSql.Append(" and MaterialsLot=@MaterialsLot ");
            strSql.Append(" and MaterialsExpiryDate=@MaterialsExpiryDate ");
            strSql.Append(" and MaterialsStore_Price=@MaterialsStore_Price ");
            SqlParameter[] parameters = {
                                            new SqlParameter("@Materials_Code", SqlDbType.Char,10)	,
                                            new SqlParameter("@MaterialsWh_Code", SqlDbType.Char,2)	,
                                            new SqlParameter("@MaterialsLot", SqlDbType.VarChar ,50)	,
                                            new SqlParameter("@MaterialsExpiryDate", SqlDbType.SmallDateTime)	,	
					                        new SqlParameter("@MaterialsStore_Price", SqlDbType.Decimal,5)};
            parameters[0].Value = Materials_Code;
            parameters[1].Value = MaterialsWh_Code;
            parameters[2].Value = MaterialsLot;
            parameters[3].Value = MaterialsExpiryDate;
            parameters[4].Value = MaterialsStore_Price;
            return HisVar.HisVar.Sqldal.Exists(strSql.ToString(), parameters);
        }
        /// <summary>
        /// 获取库存编码
        /// </summary>
        public string MaterialsStock_Code(string Materials_Code, string MaterialsWh_Code, string MaterialsLot, DateTime MaterialsExpiryDate, decimal MaterialsStore_Price)
        {
            string maxid;
            maxid = (string)HisVar.HisVar.Sqldal.GetSingle("select MaterialsStock_Code from Materials_Stock where Materials_Code='" + Materials_Code + "' and MaterialsWh_Code='" + MaterialsWh_Code + "' and  MaterialsLot='" + MaterialsLot + "' and MaterialsExpiryDate='" + MaterialsExpiryDate + "' and MaterialsStore_Price='" + MaterialsStore_Price + "'");
            return maxid;
        }
        /// <summary>
        /// 最大编码
        /// </summary>
        public string MaxCode(string Materials_Code)
        {
            string max = Materials_Code + (string)(HisVar.HisVar.Sqldal.F_MaxCode("select max(substring(MaterialsStock_Code,11,6)) from Materials_Stock where Materials_Code='" + Materials_Code + "'", 6));
            return max;
        }
        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ModelOld.M_Materials_Stock model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into Materials_Stock(");
            strSql.Append("Materials_Code,MaterialsWh_Code,MaterialsStock_Code,MaterialsLot,MaterialsExpiryDate,MaterialsStore_Num,MaterialsStore_Price,MaterialsStore_Money)");
            strSql.Append(" values (");
            strSql.Append("@Materials_Code,@MaterialsWh_Code,@MaterialsStock_Code,@MaterialsLot,@MaterialsExpiryDate,@MaterialsStore_Num,@MaterialsStore_Price,@MaterialsStore_Money)");
            SqlParameter[] parameters = {
					new SqlParameter("@Materials_Code", SqlDbType.Char,10),
					new SqlParameter("@MaterialsWh_Code", SqlDbType.Char,2),
					new SqlParameter("@MaterialsStock_Code", SqlDbType.Char,16),
					new SqlParameter("@MaterialsLot", SqlDbType.VarChar,50),
					new SqlParameter("@MaterialsExpiryDate", SqlDbType.SmallDateTime),
					new SqlParameter("@MaterialsStore_Num", SqlDbType.Decimal,5),
					new SqlParameter("@MaterialsStore_Price", SqlDbType.Decimal,5),
					new SqlParameter("@MaterialsStore_Money", SqlDbType.Decimal,5)};
            parameters[0].Value = model.Materials_Code;
            parameters[1].Value = model.MaterialsWh_Code;
            parameters[2].Value = model.MaterialsStock_Code;
            parameters[3].Value = model.MaterialsLot;
            parameters[4].Value = model.MaterialsExpiryDate;
            parameters[5].Value = model.MaterialsStore_Num;
            parameters[6].Value = model.MaterialsStore_Price;
            parameters[7].Value = model.MaterialsStore_Money;

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ModelOld.M_Materials_Stock model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update Materials_Stock set ");
            strSql.Append("Materials_Code=@Materials_Code,");
            strSql.Append("MaterialsWh_Code=@MaterialsWh_Code,");
            strSql.Append("MaterialsLot=@MaterialsLot,");
            strSql.Append("MaterialsExpiryDate=@MaterialsExpiryDate,");
            strSql.Append("MaterialsStore_Num=@MaterialsStore_Num,");
            strSql.Append("MaterialsStore_Price=@MaterialsStore_Price,");
            strSql.Append("MaterialsStore_Money=@MaterialsStore_Money");
            strSql.Append(" where MaterialsStock_Code=@MaterialsStock_Code ");
            SqlParameter[] parameters = {
					new SqlParameter("@Materials_Code", SqlDbType.Char,10),
					new SqlParameter("@MaterialsWh_Code", SqlDbType.Char,2),
					new SqlParameter("@MaterialsLot", SqlDbType.VarChar,50),
					new SqlParameter("@MaterialsExpiryDate", SqlDbType.SmallDateTime),
					new SqlParameter("@MaterialsStore_Num", SqlDbType.Decimal,5),
					new SqlParameter("@MaterialsStore_Price", SqlDbType.Decimal,5),
					new SqlParameter("@MaterialsStore_Money", SqlDbType.Decimal,5),
					new SqlParameter("@MaterialsStock_Code", SqlDbType.Char,16)};
            parameters[0].Value = model.Materials_Code;
            parameters[1].Value = model.MaterialsWh_Code;
            parameters[2].Value = model.MaterialsLot;
            parameters[3].Value = model.MaterialsExpiryDate;
            parameters[4].Value = model.MaterialsStore_Num;
            parameters[5].Value = model.MaterialsStore_Price;
            parameters[6].Value = model.MaterialsStore_Money;
            parameters[7].Value = model.MaterialsStock_Code;

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(string MaterialsStock_Code)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from Materials_Stock ");
            strSql.Append(" where MaterialsStock_Code=@MaterialsStock_Code ");
            SqlParameter[] parameters = {
					new SqlParameter("@MaterialsStock_Code", SqlDbType.Char,16)			};
            parameters[0].Value = MaterialsStock_Code;

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string MaterialsStock_Codelist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from Materials_Stock ");
            strSql.Append(" where MaterialsStock_Code in (" + MaterialsStock_Codelist + ")  ");
            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ModelOld.M_Materials_Stock GetModel(string MaterialsStock_Code)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 Materials_Code,MaterialsWh_Code,MaterialsStock_Code,MaterialsLot,MaterialsExpiryDate,MaterialsStore_Num,MaterialsStore_Price,MaterialsStore_Money from Materials_Stock ");
            strSql.Append(" where MaterialsStock_Code=@MaterialsStock_Code ");
            SqlParameter[] parameters = {
					new SqlParameter("@MaterialsStock_Code", SqlDbType.Char,16)			};
            parameters[0].Value = MaterialsStock_Code;

            ModelOld.M_Materials_Stock model = new ModelOld.M_Materials_Stock();
            DataSet ds = HisVar.HisVar.Sqldal.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ModelOld.M_Materials_Stock DataRowToModel(DataRow row)
        {
            ModelOld.M_Materials_Stock model = new ModelOld.M_Materials_Stock();
            if (row != null)
            {
                if (row["Materials_Code"] != null)
                {
                    model.Materials_Code = row["Materials_Code"].ToString();
                }
                if (row["MaterialsWh_Code"] != null)
                {
                    model.MaterialsWh_Code = row["MaterialsWh_Code"].ToString();
                }
                if (row["MaterialsStock_Code"] != null)
                {
                    model.MaterialsStock_Code = row["MaterialsStock_Code"].ToString();
                }
                if (row["MaterialsLot"] != null)
                {
                    model.MaterialsLot = row["MaterialsLot"].ToString();
                }
                if (row["MaterialsExpiryDate"] != null && row["MaterialsExpiryDate"].ToString() != "")
                {
                    model.MaterialsExpiryDate = DateTime.Parse(row["MaterialsExpiryDate"].ToString());
                }
                if (row["MaterialsStore_Num"] != null && row["MaterialsStore_Num"].ToString() != "")
                {
                    model.MaterialsStore_Num = decimal.Parse(row["MaterialsStore_Num"].ToString());
                }
                if (row["MaterialsStore_Price"] != null && row["MaterialsStore_Price"].ToString() != "")
                {
                    model.MaterialsStore_Price = decimal.Parse(row["MaterialsStore_Price"].ToString());
                }
                if (row["MaterialsStore_Money"] != null && row["MaterialsStore_Money"].ToString() != "")
                {
                    model.MaterialsStore_Money = decimal.Parse(row["MaterialsStore_Money"].ToString());
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Materials_Code,MaterialsWh_Code,MaterialsStock_Code,MaterialsLot,MaterialsExpiryDate,MaterialsStore_Num,MaterialsStore_Price,MaterialsStore_Money ");
            strSql.Append(" FROM Materials_Stock ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }
        public DataSet GetHzList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT  * FROM dbo.Materials_Stock ,Materials_Dict ,Materials_Warehouse_Dict,Materials_Class_Dict ");
            strSql.Append(" WHERE   dbo.Materials_Stock.Materials_Code = Materials_Dict.Materials_Code AND dbo.Materials_Stock.MaterialsWh_Code = Materials_Warehouse_Dict.MaterialsWh_Code AND  dbo.Materials_Dict.Class_Code = Materials_Class_Dict.Class_Code ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" and " + strWhere);
            }
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());

        }

        public DataSet GetListDc(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT Materials_Name,dbo.Materials_Stock.Materials_Code,dbo.Materials_Stock. MaterialsStock_Code,dbo.Materials_Stock.MaterialsWh_Code,MaterialsWh_Name,");
            strSql.Append("  dbo.Materials_Stock.MaterialsLot,dbo.Materials_Stock. MaterialsExpiryDate,dbo.Materials_Dict. Materials_Spec,");
            strSql.Append(" MaterialsStore_Num M_Paper_Num,0 M_Real_Num,MaterialsStore_Price M_Check_Price,'' M_CheckDetail_Memo");
            strSql.Append(" FROM dbo.Materials_Class_Dict JOIN dbo.Materials_Dict ON dbo.Materials_Class_Dict.Class_Code = dbo.Materials_Dict.Class_Code");
            strSql.Append(" JOIN dbo.Materials_Stock ON dbo.Materials_Dict.Materials_Code = dbo.Materials_Stock.Materials_Code");
            strSql.Append(" JOIN dbo.Materials_Warehouse_Dict ON dbo.Materials_Stock.MaterialsWh_Code = dbo.Materials_Warehouse_Dict.MaterialsWh_Code");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());

        }

        public DataSet GetHzListMaterials(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT   distinct Materials_Dict.Materials_Code code, Materials_Name name, Materials_Py py, Materials_Wb, Materials_Spec, Pack_Unit, Convert_Ratio, Bulk_Unit, MateManu_Code, MateManu_Name, Materials_Dict.Class_Code, Materials_Dict.IsUse, Materials_Memo FROM dbo.Materials_Stock ,Materials_Dict ,Materials_Warehouse_Dict,Materials_Class_Dict ");
            strSql.Append(" WHERE   dbo.Materials_Stock.Materials_Code = Materials_Dict.Materials_Code AND dbo.Materials_Stock.MaterialsWh_Code = Materials_Warehouse_Dict.MaterialsWh_Code AND  dbo.Materials_Dict.Class_Code = Materials_Class_Dict.Class_Code ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" and " + strWhere);
            }
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());

        }

        public DataSet GetHzListMaterialsClass(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT  distinct Materials_Class_Dict.Class_Code code, Class_Name name, Class_Py py, Class_Wb, Class_Father, HaveChild, Materials_Class_Dict.Serial_No FROM dbo.Materials_Stock ,Materials_Dict ,Materials_Warehouse_Dict,Materials_Class_Dict ");
            strSql.Append(" WHERE   dbo.Materials_Stock.Materials_Code = Materials_Dict.Materials_Code AND dbo.Materials_Stock.MaterialsWh_Code = Materials_Warehouse_Dict.MaterialsWh_Code AND  dbo.Materials_Dict.Class_Code = Materials_Class_Dict.Class_Code ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" and " + strWhere);
            }
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());

        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" Materials_Code,MaterialsWh_Code,MaterialsStock_Code,MaterialsLot,MaterialsExpiryDate,MaterialsStore_Num,MaterialsStore_Price,MaterialsStore_Money ");
            strSql.Append(" FROM Materials_Stock ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM Materials_Stock ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.MaterialsStock_Code desc");
            }
            strSql.Append(")AS Row, T.*  from Materials_Stock T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }
        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string Materials_Code, string MaterialsWh_Code, string MaterialsLot, string MaterialsExpiryDate, decimal MaterialsStore_Price)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM Materials_Stock ");

            strSql.Append(" where Materials_Code= '" + Materials_Code + "' and MaterialsWh_Code= '" + MaterialsWh_Code + "' and MaterialsLot= '" + MaterialsLot + "' and MaterialsExpiryDate= '" + MaterialsExpiryDate + "' and MaterialsStore_Price= '" + MaterialsStore_Price + "'");

            object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }


        /// <summary>
        /// 获得同一种物资的 所有批号（同一种物资 同一批号 单价 和有效期相同）
        /// </summary>
        public DataSet GetMaterialsLot(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT Materials_Code,MaterialsLot,MaterialsExpiryDate,MaterialsStore_Price FROM Materials_Stock  ");
            if (!string.IsNullOrEmpty(strWhere))
            {
                strSql.Append(" where Materials_Code ='" + strWhere + "' ");
            }
            strSql.Append(" GROUP BY Materials_Code,MaterialsLot,MaterialsExpiryDate,MaterialsStore_Price ");
            strSql.Append(" ORDER BY MaterialsLot ");

            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取库存编码
        /// </summary>
        public string GetMaterialsStockCode(string Materials_Code, string MaterialsWh_Code, string MaterialsLot, DateTime MaterialsExpiryDate, decimal MaterialsStore_Price)
        {
            string MaterialsStock_Code;
            MaterialsStock_Code = (string)HisVar.HisVar.Sqldal.GetSingle("select MaterialsStock_Code from Materials_Stock where Materials_Code='" + Materials_Code + "' and MaterialsWh_Code='" + MaterialsWh_Code + "' and  MaterialsLot='" + MaterialsLot + "' and CONVERT(VARCHAR(10),MaterialsExpiryDate,126)='" + MaterialsExpiryDate.ToString("yyyy-MM-dd") + "' and MaterialsStore_Price='" + MaterialsStore_Price + "'");
            return MaterialsStock_Code;
        }

        /// <summary>
        /// 增加一条新编码数据
        /// </summary>
        public string AddNew(string Materials_Code, string MaterialsWh_Code, string MaterialsLot, string MaterialsExpiryDate, decimal MaterialsStore_Price)
        {
            int count = GetRecordCount(Materials_Code, MaterialsWh_Code, MaterialsLot, MaterialsExpiryDate, MaterialsStore_Price);
            string maxcode;
            if (count == 0)
            {
                maxcode = MaxCode(Materials_Code);
                StringBuilder strSql = new StringBuilder();
                strSql.Append("insert into Materials_Stock(");
                strSql.Append("Materials_Code,MaterialsWh_Code,MaterialsStock_Code,MaterialsLot,MaterialsExpiryDate,MaterialsStore_Price)");
                strSql.Append(" values (");
                strSql.Append("'" + Materials_Code + "','" + MaterialsWh_Code + "','" + maxcode + "','" + MaterialsLot + "','" + MaterialsExpiryDate + "','" + MaterialsStore_Price + "')");

                HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString());
            }
            StringBuilder strSql1 = new StringBuilder();
            strSql1.Append("select top 1 MaterialsStock_Code  FROM Materials_Stock ");
            strSql1.Append(" where Materials_Code= '" + Materials_Code + "' and MaterialsLot= '" + MaterialsLot + "' and MaterialsExpiryDate= '" + MaterialsExpiryDate + "' and MaterialsStore_Price= '" + MaterialsStore_Price + "'");


            string code = (string)HisVar.HisVar.Sqldal.GetSingle(strSql1.ToString());

            return code;

        }
        /*
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetList(int PageSize,int PageIndex,string strWhere)
        {
            SqlParameter[] parameters = {
                    new SqlParameter("@tblName", SqlDbType.VarChar, 255),
                    new SqlParameter("@fldName", SqlDbType.VarChar, 255),
                    new SqlParameter("@PageSize", SqlDbType.Int),
                    new SqlParameter("@PageIndex", SqlDbType.Int),
                    new SqlParameter("@IsReCount", SqlDbType.Bit),
                    new SqlParameter("@OrderType", SqlDbType.Bit),
                    new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
                    };
            parameters[0].Value = "Materials_Stock";
            parameters[1].Value = "MaterialsStock_Code";
            parameters[2].Value = PageSize;
            parameters[3].Value = PageIndex;
            parameters[4].Value = 0;
            parameters[5].Value = 0;
            parameters[6].Value = strWhere;	
            return HisVar.HisVar.Sqldal.RunProcedure("UP_GetRecordByPage",parameters,"ds");
        }*/

        #endregion  BasicMethod
        #region  ExtensionMethod

        #endregion  ExtensionMethod
    }
}

