﻿/**  版本信息模板在安装目录下，可自行修改。
* D_DRYB_Jz.cs
*
* 功 能： N/A
* 类 名： D_DRYB_Jz
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/12/20 16:07:50   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using System.Collections;

namespace DAL
{
	/// <summary>
	/// 数据访问类:D_DRYB_Jz
	/// </summary>
	public partial class D_DRYB_Jz
	{
		public D_DRYB_Jz()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string Jz_Code)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from DRYB_Jz");
			strSql.Append(" where Jz_Code=@Jz_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Jz_Code", SqlDbType.VarChar,12)			};
			parameters[0].Value = Jz_Code;

			return HisVar.HisVar.Sqldal.Exists(strSql.ToString(),parameters);
		}
        /// <summary>
        /// 获取最新结账时间 
        /// </summary>
        public DateTime  Exists2(string Jsr_Code)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select top 1 Jz_Time from DRYB_Jz");
            strSql.Append(" where Jsr_Code=@Jsr_Code ");
            strSql.Append(" Order By Jz_Time Desc ");
            SqlParameter[] parameters = {new SqlParameter("@Jsr_Code", SqlDbType.Char,7)};
            parameters[0].Value = Jsr_Code;
            object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString(), parameters);
            if (obj == null)
            {
                return Convert.ToDateTime("1753-01-01 12:00:00");
            }
            else
            {
                return Convert.ToDateTime(obj);
            }
           
            
        }
        public string MaxCode()
        {
            string max =  DateTime.Now.ToString("yyMMdd") + (string)(HisVar.HisVar.Sqldal.F_MaxCode("select max(substring(Jz_Code,7,6)) from DRYB_Jz where substring(Jz_Code,1,6)='" + DateTime.Now.ToString("yyMMdd") + "'", 6));
            return max;
        }
		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(Model.M_DRYB_Jz model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into DRYB_Jz(");
            strSql.Append("Jz_Code,Jz_Time,BcGrzhZfJe,BcTcZfJe,BcXjZfJe,Jsr_Code,LastJz_Time)");
			strSql.Append(" values (");
            strSql.Append("@Jz_Code,@Jz_Time,@BcGrzhZfJe,@BcTcZfJe,@BcXjZfJe,@Jsr_Code,@LastJz_Time)");
			SqlParameter[] parameters = {
					new SqlParameter("@Jz_Code", SqlDbType.VarChar,12),
					new SqlParameter("@Jz_Time", SqlDbType.DateTime),
					new SqlParameter("@BcGrzhZfJe", SqlDbType.Decimal,9),
					new SqlParameter("@BcTcZfJe", SqlDbType.Decimal,9),
					new SqlParameter("@BcXjZfJe", SqlDbType.Decimal,9),
					new SqlParameter("@Jsr_Code", SqlDbType.Char,7),
                    new SqlParameter("@LastJz_Time", SqlDbType.DateTime)};
			parameters[0].Value = model.Jz_Code;
			parameters[1].Value = model.Jz_Time;
			parameters[2].Value = model.BcGrzhZfJe;
			parameters[3].Value = model.BcTcZfJe;
			parameters[4].Value = model.BcXjZfJe;
			parameters[5].Value = model.Jsr_Code;
            parameters[6].Value = model.LastJz_Time;
			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.M_DRYB_Jz model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update DRYB_Jz set ");
			strSql.Append("Jz_Time=@Jz_Time,");
			strSql.Append("BcGrzhZfJe=@BcGrzhZfJe,");
			strSql.Append("BcTcZfJe=@BcTcZfJe,");
			strSql.Append("BcXjZfJe=@BcXjZfJe,");
			strSql.Append("Jsr_Code=@Jsr_Code");
			strSql.Append(" where Jz_Code=@Jz_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Jz_Time", SqlDbType.DateTime),
					new SqlParameter("@BcGrzhZfJe", SqlDbType.Decimal,9),
					new SqlParameter("@BcTcZfJe", SqlDbType.Decimal,9),
					new SqlParameter("@BcXjZfJe", SqlDbType.Decimal,9),
					new SqlParameter("@Jsr_Code", SqlDbType.Char,7),
					new SqlParameter("@Jz_Code", SqlDbType.VarChar,12)};
			parameters[0].Value = model.Jz_Time;
			parameters[1].Value = model.BcGrzhZfJe;
			parameters[2].Value = model.BcTcZfJe;
			parameters[3].Value = model.BcXjZfJe;
			parameters[4].Value = model.Jsr_Code;
			parameters[5].Value = model.Jz_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string Jz_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from DRYB_Jz ");
			strSql.Append(" where Jz_Code=@Jz_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Jz_Code", SqlDbType.VarChar,12)			};
			parameters[0].Value = Jz_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string Jz_Codelist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from DRYB_Jz ");
			strSql.Append(" where Jz_Code in ("+Jz_Codelist + ")  ");
			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.M_DRYB_Jz GetModel(string Jz_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
            strSql.Append("select  top 1 Jz_Code,Jz_Time,BcGrzhZfJe,BcTcZfJe,BcXjZfJe,Jsr_Code,LastJz_Time from DRYB_Jz ");
			strSql.Append(" where Jz_Code=@Jz_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Jz_Code", SqlDbType.VarChar,12)			};
			parameters[0].Value = Jz_Code;

			Model.M_DRYB_Jz model=new Model.M_DRYB_Jz();
			DataSet ds=HisVar.HisVar.Sqldal.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.M_DRYB_Jz DataRowToModel(DataRow row)
		{
			Model.M_DRYB_Jz model=new Model.M_DRYB_Jz();
			if (row != null)
			{
				if(row["Jz_Code"]!=null)
				{
					model.Jz_Code=row["Jz_Code"].ToString();
				}
				if(row["Jz_Time"]!=null && row["Jz_Time"].ToString()!="")
				{
					model.Jz_Time=DateTime.Parse(row["Jz_Time"].ToString());
				}
				if(row["BcGrzhZfJe"]!=null && row["BcGrzhZfJe"].ToString()!="")
				{
					model.BcGrzhZfJe=decimal.Parse(row["BcGrzhZfJe"].ToString());
				}
				if(row["BcTcZfJe"]!=null && row["BcTcZfJe"].ToString()!="")
				{
					model.BcTcZfJe=decimal.Parse(row["BcTcZfJe"].ToString());
				}
				if(row["BcXjZfJe"]!=null && row["BcXjZfJe"].ToString()!="")
				{
					model.BcXjZfJe=decimal.Parse(row["BcXjZfJe"].ToString());
				}
				if(row["Jsr_Code"]!=null)
				{
					model.Jsr_Code=row["Jsr_Code"].ToString();
				}
                if (row["LastJz_Time"] != null && row["LastJz_Time"].ToString() != "")
                {
                    model.LastJz_Time = DateTime.Parse(row["LastJz_Time"].ToString());
                }
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
            strSql.Append("select Jz_Code,Jz_Time,BcGrzhZfJe,BcTcZfJe,BcXjZfJe,DRYB_Jz.Jsr_Code,LastJz_Time,Jsr_Name,IsZf ");
            strSql.Append(" FROM DRYB_Jz,zd_yyjsr  where DRYB_Jz.Jsr_Code=zd_yyjsr.Jsr_Code ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" and  "+strWhere);
                strSql.Append(" order by Jz_Code  ");
			}
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" Jz_Code,Jz_Time,BcGrzhZfJe,BcTcZfJe,BcXjZfJe,Jsr_Code ");
			strSql.Append(" FROM DRYB_Jz ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM DRYB_Jz ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.Jz_Code desc");
			}
			strSql.Append(")AS Row, T.*  from DRYB_Jz T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}
        /// <summary>
        /// 医保日结作废
        /// </summary>
        /// <param name="JzCode"></param>
        /// <returns></returns>
        public bool WriteOffByJzCode(string JzCode)
        {
            ArrayList arry = new ArrayList();

            StringBuilder sb = new StringBuilder();
            sb.Append(" INSERT INTO dbo.DRYB_MzSf_Zf( Zf_Id ,BcGrzhZfJe ,BcTcZfJe ,BcXjZfJe ,MzZy_Code ,Ry_Name ,Lb ,YbJz_Code ,Jsr_Code ,Zf_Lr_Date ,Tb_Lb,Lr_Date ) ");
            sb.Append(" SELECT * from( ");
            sb.Append(" SELECT id, BcGrzhZfJe,BcTcZfJe,BcXjZfJe ,MzZy_Code,Ry_Name,Lb,YbJz_Code,Jsr_Code ,Lr_Date AS Zf_Lr_Date, 'Js_Tb' AS Tb_Lb,GETDATE() AS Lr_Date from DRYB_MzSf WHERE YbJz_Code = '" + JzCode + "' ");
            sb.Append(" union all ");
            sb.Append(" SELECT id, -BcGrzhZfJe,-BcTcZfJe,-BcXjZfJe,MzZy_Code,Ry_Name,Lb,YbJz_Code,Jsr_Code ,Lr_Date AS Zf_Lr_Date,'Ht_Tb' AS Tb_Lb,GETDATE()AS Lr_Date FROM DRYB_MzSf_Ht WHERE YbJz_Code = '" + JzCode + "' ");
            sb.Append("  ) AS T");
            arry.Add(sb.ToString());

            sb = new StringBuilder();
            sb.Append(" UPDATE DRYB_MzSf SET YbJz_Code = NULL WHERE YbJz_Code = '" + JzCode + "'");
            arry.Add(sb.ToString());

            sb = new StringBuilder();
            sb.Append(" UPDATE DRYB_MzSf_Ht SET YbJz_Code = NULL WHERE YbJz_Code = '" + JzCode + "'");
            arry.Add(sb.ToString());

            sb = new StringBuilder();
            sb.Append("UPDATE DRYB_Jz SET IsZf = '是' WHERE Jz_Code = '" + JzCode + "'");
            arry.Add(sb.ToString());

            try
            {
                HisVar.HisVar.Sqldal.ExecuteSqlTran(arry);
                return true;
            }
            catch (Exception e)
            {
                return false;
            }
        }


		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "DRYB_Jz";
			parameters[1].Value = "Jz_Code";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return HisVar.HisVar.Sqldal.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

