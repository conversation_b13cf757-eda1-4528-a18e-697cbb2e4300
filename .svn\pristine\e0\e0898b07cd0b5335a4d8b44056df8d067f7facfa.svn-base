﻿/**  版本信息模板在安装目录下，可自行修改。
* D_Jkk_Cz.cs
*
* 功 能： N/A
* 类 名： D_Jkk_Cz
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/7/2 10:28:15   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;

namespace  DAL
{
	/// <summary>
	/// 数据访问类:D_Jkk_Cz
	/// </summary>
	public partial class D_Jkk_Cz
	{
		public D_Jkk_Cz()
		{}
		#region  BasicMethod

		/// <summary>
		/// 得到最大ID
		/// </summary>
		public int GetMaxId()
		{
		return HisVar.HisVar.Sqldal.GetMaxID("Id", "Jkk_Cz"); 
		}

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(int Id)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from Jkk_Cz");
			strSql.Append(" where Id=@Id");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.Int,4)
			};
			parameters[0].Value = Id;

			return HisVar.HisVar.Sqldal.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public int Add( ModelOld.M_Jkk_Cz model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into Jkk_Cz(");
			strSql.Append("Ry_Name,Ry_sfzh,Jsr_Code,Jkk_Code,CzId,Cz_Money,Cz_Date,Lb,JkkLb,Jz_Code,CzBankNo,Jz_CwCode,Cz_Lb,MzZy_Code)");
			strSql.Append(" values (");
			strSql.Append("@Ry_Name,@Ry_sfzh,@Jsr_Code,@Jkk_Code,@CzId,@Cz_Money,@Cz_Date,@Lb,@JkkLb,@Jz_Code,@CzBankNo,@Jz_CwCode,@Cz_Lb,@MzZy_Code)");
			strSql.Append(";select @@IDENTITY");
			SqlParameter[] parameters = {
					new SqlParameter("@Ry_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Ry_sfzh", SqlDbType.VarChar,18),
					new SqlParameter("@Jsr_Code", SqlDbType.Char,7),
					new SqlParameter("@Jkk_Code", SqlDbType.VarChar,19),
					new SqlParameter("@CzId", SqlDbType.Int,4),
					new SqlParameter("@Cz_Money", SqlDbType.Decimal,9),
					new SqlParameter("@Cz_Date", SqlDbType.DateTime),
					new SqlParameter("@Lb", SqlDbType.VarChar,50),
					new SqlParameter("@JkkLb", SqlDbType.VarChar,50),
					new SqlParameter("@Jz_Code", SqlDbType.Char,12),
					new SqlParameter("@CzBankNo", SqlDbType.VarChar,50),
					new SqlParameter("@Jz_CwCode", SqlDbType.VarChar,20),
					new SqlParameter("@Cz_Lb", SqlDbType.VarChar,20),
					new SqlParameter("@MzZy_Code", SqlDbType.VarChar,50)};
			parameters[0].Value = model.Ry_Name;
			parameters[1].Value = model.Ry_sfzh;
			parameters[2].Value = model.Jsr_Code;
			parameters[3].Value = model.Jkk_Code;
			parameters[4].Value = model.CzId;
			parameters[5].Value = model.Cz_Money;
			parameters[6].Value = model.Cz_Date;
			parameters[7].Value = model.Lb;
			parameters[8].Value = model.JkkLb;
			parameters[9].Value = model.Jz_Code;
			parameters[10].Value = model.CzBankNo;
			parameters[11].Value = model.Jz_CwCode;
			parameters[12].Value = model.Cz_Lb;
			parameters[13].Value = model.MzZy_Code;

			object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString(),parameters);
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}

        //增加退费记录
        public int AddTf(ModelOld.M_Jkk_Cz model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into Jkk_Cz(");
            strSql.Append("Ry_Name,Ry_sfzh,Jsr_Code,Jkk_Code,CzId,Cz_Money,Cz_Date,Lb,JkkLb,CzBankNo,Cz_Lb,MzZy_Code)");
            strSql.Append(" values (");
            strSql.Append("@Ry_Name,@Ry_sfzh,@Jsr_Code,@Jkk_Code,@CzId,@Cz_Money,@Cz_Date,@Lb,@JkkLb,@CzBankNo,@Cz_Lb,@MzZy_Code)");
            strSql.Append(";select @@IDENTITY");
            SqlParameter[] parameters = {
					new SqlParameter("@Ry_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Ry_sfzh", SqlDbType.VarChar,18),
					new SqlParameter("@Jsr_Code", SqlDbType.Char,7),
					new SqlParameter("@Jkk_Code", SqlDbType.VarChar,19),
					new SqlParameter("@CzId", SqlDbType.Int,4),
					new SqlParameter("@Cz_Money", SqlDbType.Decimal,9),
					new SqlParameter("@Cz_Date", SqlDbType.DateTime),
					new SqlParameter("@Lb", SqlDbType.VarChar,50),
					new SqlParameter("@JkkLb", SqlDbType.VarChar,50),
					new SqlParameter("@CzBankNo", SqlDbType.VarChar,50),
					new SqlParameter("@Cz_Lb", SqlDbType.VarChar,20),
					new SqlParameter("@MzZy_Code", SqlDbType.VarChar,50)};
            parameters[0].Value = model.Ry_Name;
            parameters[1].Value = model.Ry_sfzh;
            parameters[2].Value = model.Jsr_Code;
            parameters[3].Value = model.Jkk_Code;
            parameters[4].Value = model.CzId;
            parameters[5].Value = model.Cz_Money;
            parameters[6].Value = model.Cz_Date;
            parameters[7].Value = model.Lb;
            parameters[8].Value = model.JkkLb;
            parameters[9].Value = model.CzBankNo;
            parameters[10].Value = model.Cz_Lb;
            parameters[11].Value = model.MzZy_Code;

            object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString(), parameters);
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }

		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update( ModelOld.M_Jkk_Cz model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update Jkk_Cz set ");
			strSql.Append("Ry_Name=@Ry_Name,");
			strSql.Append("Ry_sfzh=@Ry_sfzh,");
			strSql.Append("Jsr_Code=@Jsr_Code,");
			strSql.Append("Jkk_Code=@Jkk_Code,");
			strSql.Append("CzId=@CzId,");
			strSql.Append("Cz_Money=@Cz_Money,");
			strSql.Append("Cz_Date=@Cz_Date,");
			strSql.Append("Lb=@Lb,");
			strSql.Append("JkkLb=@JkkLb,");
			strSql.Append("Jz_Code=@Jz_Code,");
			strSql.Append("CzBankNo=@CzBankNo,");
			strSql.Append("Jz_CwCode=@Jz_CwCode,");
			strSql.Append("Cz_Lb=@Cz_Lb,");
			strSql.Append("MzZy_Code=@MzZy_Code");
			strSql.Append(" where Id=@Id");
			SqlParameter[] parameters = {
					new SqlParameter("@Ry_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Ry_sfzh", SqlDbType.VarChar,18),
					new SqlParameter("@Jsr_Code", SqlDbType.Char,7),
					new SqlParameter("@Jkk_Code", SqlDbType.VarChar,19),
					new SqlParameter("@CzId", SqlDbType.Int,4),
					new SqlParameter("@Cz_Money", SqlDbType.Decimal,9),
					new SqlParameter("@Cz_Date", SqlDbType.DateTime),
					new SqlParameter("@Lb", SqlDbType.VarChar,50),
					new SqlParameter("@JkkLb", SqlDbType.VarChar,50),
					new SqlParameter("@Jz_Code", SqlDbType.Char,12),
					new SqlParameter("@CzBankNo", SqlDbType.VarChar,50),
					new SqlParameter("@Jz_CwCode", SqlDbType.VarChar,20),
					new SqlParameter("@Cz_Lb", SqlDbType.VarChar,20),
					new SqlParameter("@MzZy_Code", SqlDbType.VarChar,50),
					new SqlParameter("@Id", SqlDbType.Int,4)};
			parameters[0].Value = model.Ry_Name;
			parameters[1].Value = model.Ry_sfzh;
			parameters[2].Value = model.Jsr_Code;
			parameters[3].Value = model.Jkk_Code;
			parameters[4].Value = model.CzId;
			parameters[5].Value = model.Cz_Money;
			parameters[6].Value = model.Cz_Date;
			parameters[7].Value = model.Lb;
			parameters[8].Value = model.JkkLb;
			parameters[9].Value = model.Jz_Code;
			parameters[10].Value = model.CzBankNo;
			parameters[11].Value = model.Jz_CwCode;
			parameters[12].Value = model.Cz_Lb;
			parameters[13].Value = model.MzZy_Code;
			parameters[14].Value = model.Id;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(int Id)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Jkk_Cz ");
			strSql.Append(" where Id=@Id");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.Int,4)
			};
			parameters[0].Value = Id;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string Idlist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Jkk_Cz ");
			strSql.Append(" where Id in ("+Idlist + ")  ");
			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public  ModelOld.M_Jkk_Cz GetModel(int Id)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 Id,Ry_Name,Ry_sfzh,Jsr_Code,Jkk_Code,CzId,Cz_Money,Cz_Date,Lb,JkkLb,Jz_Code,CzBankNo,Jz_CwCode,Cz_Lb,MzZy_Code from Jkk_Cz ");
			strSql.Append(" where Id=@Id");
			SqlParameter[] parameters = {
					new SqlParameter("@Id", SqlDbType.Int,4)
			};
			parameters[0].Value = Id;

			 ModelOld.M_Jkk_Cz model=new  ModelOld.M_Jkk_Cz();
			DataSet ds=HisVar.HisVar.Sqldal.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public  ModelOld.M_Jkk_Cz DataRowToModel(DataRow row)
		{
			 ModelOld.M_Jkk_Cz model=new  ModelOld.M_Jkk_Cz();
			if (row != null)
			{
				if(row["Id"]!=null && row["Id"].ToString()!="")
				{
					model.Id=int.Parse(row["Id"].ToString());
				}
				if(row["Ry_Name"]!=null)
				{
					model.Ry_Name=row["Ry_Name"].ToString();
				}
				if(row["Ry_sfzh"]!=null)
				{
					model.Ry_sfzh=row["Ry_sfzh"].ToString();
				}
				if(row["Jsr_Code"]!=null)
				{
					model.Jsr_Code=row["Jsr_Code"].ToString();
				}
				if(row["Jkk_Code"]!=null)
				{
					model.Jkk_Code=row["Jkk_Code"].ToString();
				}
				if(row["CzId"]!=null && row["CzId"].ToString()!="")
				{
					model.CzId=int.Parse(row["CzId"].ToString());
				}
				if(row["Cz_Money"]!=null && row["Cz_Money"].ToString()!="")
				{
					model.Cz_Money=decimal.Parse(row["Cz_Money"].ToString());
				}
				if(row["Cz_Date"]!=null && row["Cz_Date"].ToString()!="")
				{
					model.Cz_Date=DateTime.Parse(row["Cz_Date"].ToString());
				}
				if(row["Lb"]!=null)
				{
					model.Lb=row["Lb"].ToString();
				}
				if(row["JkkLb"]!=null)
				{
					model.JkkLb=row["JkkLb"].ToString();
				}
				if(row["Jz_Code"]!=null)
				{
					model.Jz_Code=row["Jz_Code"].ToString();
				}
				if(row["CzBankNo"]!=null)
				{
					model.CzBankNo=row["CzBankNo"].ToString();
				}
				if(row["Jz_CwCode"]!=null)
				{
					model.Jz_CwCode=row["Jz_CwCode"].ToString();
				}
				if(row["Cz_Lb"]!=null)
				{
					model.Cz_Lb=row["Cz_Lb"].ToString();
				}
				if(row["MzZy_Code"]!=null)
				{
					model.MzZy_Code=row["MzZy_Code"].ToString();
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select Id,Ry_Name,Ry_sfzh,Jsr_Code,Jkk_Code,CzId,Cz_Money,Cz_Date,Lb,JkkLb,Jz_Code,CzBankNo,Jz_CwCode,Cz_Lb,MzZy_Code ");
			strSql.Append(" FROM Jkk_Cz ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" Id,Ry_Name,Ry_sfzh,Jsr_Code,Jkk_Code,CzId,Cz_Money,Cz_Date,Lb,JkkLb,Jz_Code,CzBankNo,Jz_CwCode,Cz_Lb,MzZy_Code ");
			strSql.Append(" FROM Jkk_Cz ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM Jkk_Cz ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.Id desc");
			}
			strSql.Append(")AS Row, T.*  from Jkk_Cz T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Jkk_Cz";
			parameters[1].Value = "Id";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return HisVar.HisVar.Sqldal.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

