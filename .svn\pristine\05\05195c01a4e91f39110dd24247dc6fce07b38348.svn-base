﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="0" />
    <DataSources isList="true" count="2">
      <view明细 Ref="2" type="Stimulsoft.Report.Dictionary.StiDataViewSource" isKey="true">
        <Alias>view明细</Alias>
        <Columns isList="true" count="20">
          <value>Dl_Code,System.String</value>
          <value>Dl_Name,System.String</value>
          <value>Xx_Code,System.String</value>
          <value>Yp_Name,System.String</value>
          <value>Yp_Jc,System.String</value>
          <value>Mx_Gg,System.String</value>
          <value>Mx_Cd,System.String</value>
          <value>Yp_Ph,System.String</value>
          <value>Yp_Yxq,System.DateTime</value>
          <value>Yp_Scrq,System.DateTime</value>
          <value>Alter_Ts,System.Int32</value>
          <value>Yp_Dw,System.String</value>
          <value>Yp_Cgj,System.Decimal</value>
          <value>Yp_Pfj,System.Decimal</value>
          <value>Yp_Xsj,System.Decimal</value>
          <value>Yp_Sl,System.Decimal</value>
          <value>Cg_Money,System.Decimal</value>
          <value>Pf_Money,System.Decimal</value>
          <value>Xs_Money,System.Decimal</value>
          <value>IsJb,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>view明细</Name>
        <NameInSource>view明细</NameInSource>
      </view明细>
      <ds Ref="3" type="DataTableSource" isKey="true">
        <Alias>ds</Alias>
        <Columns isList="true" count="52">
          <value>Yf_Sl,System.Decimal</value>
          <value>Yf_Sl2,System.Decimal</value>
          <value>Yf_Sl3,System.Decimal</value>
          <value>Yf_Sl4,System.Decimal</value>
          <value>Yf_Sl5,System.Decimal</value>
          <value>Yf_Sl6,System.Decimal</value>
          <value>Yf_Sl7,System.Decimal</value>
          <value>Yf_Sl8,System.Decimal</value>
          <value>Yf_Sl9,System.Decimal</value>
          <value>Yf_Cgj,System.Decimal</value>
          <value>Yf_Xsj,System.Decimal</value>
          <value>Yf_Pfj,System.Decimal</value>
          <value>Yp_Code,System.String</value>
          <value>Yp_Name,System.String</value>
          <value>Yp_Jc,System.String</value>
          <value>Mx_Code,System.String</value>
          <value>Mx_Gg,System.String</value>
          <value>Yp_Dw,System.String</value>
          <value>Mx_CgDw,System.String</value>
          <value>Jx_Code,System.String</value>
          <value>Mx_Cd,System.String</value>
          <value>Mx_XsDw,System.String</value>
          <value>Mx_Cfbl,System.Decimal</value>
          <value>Mx_Gyzz,System.String</value>
          <value>Jx_Name,System.String</value>
          <value>Yp_Memo,System.String</value>
          <value>Mx_Memo,System.String</value>
          <value>Yk_Sl,System.Decimal</value>
          <value>Yk_Cgj,System.Decimal</value>
          <value>Yk_Pfj,System.Decimal</value>
          <value>Yk_Xsj,System.Decimal</value>
          <value>Yf_Lsj1,System.Decimal</value>
          <value>Yf_Lsj2,System.Decimal</value>
          <value>Yf_Lsj3,System.Decimal</value>
          <value>Yf_Lsj4,System.Decimal</value>
          <value>Yf_Lsj5,System.Decimal</value>
          <value>Yy_Code,System.String</value>
          <value>Dl_Code,System.String</value>
          <value>Dl_Name,System.String</value>
          <value>Xx_Code,System.String</value>
          <value>Yp_Ph,System.String</value>
          <value>Yp_Yxq,System.DateTime</value>
          <value>Yp_Scrq,System.DateTime</value>
          <value>IsJb,System.String</value>
          <value>Special,System.String</value>
          <value>Yf_Lsj6,System.Decimal</value>
          <value>Yf_Lsj7,System.Decimal</value>
          <value>Yf_Lsj8,System.Decimal</value>
          <value>Yf_Lsj9,System.Decimal</value>
          <value>Xs_Money,System.Double</value>
          <value>Yp_Xsj,System.Double</value>
          <value>Yp_Sl,System.Double</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>ds</Name>
        <NameInSource>ds</NameInSource>
      </ds>
    </DataSources>
    <Relations isList="true" count="0" />
    <Report isRef="0" />
    <Variables isList="true" count="2">
      <value>,打印时间,打印时间,System.String,,False,False</value>
      <value>,标题,标题,System.String,,False,False</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="4" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="7">
        <ReportTitleBand1 Ref="5" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,19,1.5</ClientRectangle>
          <Components isList="true" count="3">
            <Text1 Ref="6" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,19,0.9</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>黑体,15,Regular,Point,False,134</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text1</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <Text>{标题}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text1>
            <Text2 Ref="7" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.9,9,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text2</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <Text>打印时间:{打印时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text2>
            <Text3 Ref="8" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>13,0.9,6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text3</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <Text>操作员(手写)：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text3>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>ReportTitleBand1</Name>
          <Page isRef="4" />
          <Parent isRef="4" />
        </ReportTitleBand1>
        <HeaderBand1 Ref="9" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,2.7,19,0.5</ClientRectangle>
          <Components isList="true" count="8">
            <Text5 Ref="10" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,3.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text5</Name>
              <Page isRef="4" />
              <Parent isRef="9" />
              <Text>药品名称</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text5>
            <Text7 Ref="11" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>3.6,0,2.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text7</Name>
              <Page isRef="4" />
              <Parent isRef="9" />
              <Text>规格</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text7>
            <Text9 Ref="12" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>6.5,0,2.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text9</Name>
              <Page isRef="4" />
              <Parent isRef="9" />
              <Text>产地</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text9>
            <Text11 Ref="13" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>9.4,0,1.2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text11</Name>
              <Page isRef="4" />
              <Parent isRef="9" />
              <Text>单位</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text11>
            <Text13 Ref="14" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>10.6,0,2.4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text13</Name>
              <Page isRef="4" />
              <Parent isRef="9" />
              <Text>产品批号</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text13>
            <Text15 Ref="15" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>13,0,2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text15</Name>
              <Page isRef="4" />
              <Parent isRef="9" />
              <Text>有效期</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text15>
            <Text17 Ref="16" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>15,0,2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text17</Name>
              <Page isRef="4" />
              <Parent isRef="9" />
              <Text>库存数量</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text17>
            <Text19 Ref="17" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>17,0,2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text19</Name>
              <Page isRef="4" />
              <Parent isRef="9" />
              <Text>盘点数量</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text19>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>HeaderBand1</Name>
          <Page isRef="4" />
          <Parent isRef="4" />
        </HeaderBand1>
        <GroupHeaderBand1 Ref="18" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,4,19,0.5</ClientRectangle>
          <Components isList="true" count="1">
            <Text20 Ref="19" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,19,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>d957366811614a2099146b42fd607f2e</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text20</Name>
              <Page isRef="4" />
              <Parent isRef="18" />
              <Text>{ds.IsJb}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text20>
          </Components>
          <Condition>{ds.IsJb}</Condition>
          <Conditions isList="true" count="0" />
          <Name>GroupHeaderBand1</Name>
          <Page isRef="4" />
          <Parent isRef="4" />
        </GroupHeaderBand1>
        <GroupHeaderBand2 Ref="20" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,5.3,19,0.5</ClientRectangle>
          <Components isList="true" count="1">
            <Text21 Ref="21" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,19,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>14968a3c975f498883aec4955318012d</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text21</Name>
              <Page isRef="4" />
              <Parent isRef="20" />
              <Text>{ds.Dl_Name}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text21>
          </Components>
          <Condition>{ds.Dl_Name}</Condition>
          <Conditions isList="true" count="0" />
          <Name>GroupHeaderBand2</Name>
          <Page isRef="4" />
          <Parent isRef="4" />
        </GroupHeaderBand2>
        <DataBand1 Ref="22" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,6.6,19,0.5</ClientRectangle>
          <Components isList="true" count="8">
            <Text4 Ref="23" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,3.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>0,0,0,0</Margins>
              <Name>Text4</Name>
              <Page isRef="4" />
              <Parent isRef="22" />
              <Text>{ds.Yp_Name}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text4>
            <Text6 Ref="24" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>3.6,0,2.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>0,0,0,0</Margins>
              <Name>Text6</Name>
              <Page isRef="4" />
              <Parent isRef="22" />
              <Text>{ds.Mx_Gg}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text6>
            <Text8 Ref="25" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>6.5,0,2.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>0,0,0,0</Margins>
              <Name>Text8</Name>
              <Page isRef="4" />
              <Parent isRef="22" />
              <Text>{ds.Mx_Cd}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text8>
            <Text10 Ref="26" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>9.4,0,1.2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>0,0,0,0</Margins>
              <Name>Text10</Name>
              <Page isRef="4" />
              <Parent isRef="22" />
              <Text>{ds.Yp_Dw}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text10>
            <Text12 Ref="27" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>10.6,0,2.4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Margins>0,0,0,0</Margins>
              <Name>Text12</Name>
              <Page isRef="4" />
              <Parent isRef="22" />
              <Text>{ds.Yp_Ph}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text12>
            <Text14 Ref="28" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>13,0,2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text14</Name>
              <Page isRef="4" />
              <Parent isRef="22" />
              <Text>{ds.Yp_Yxq}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="29" type="CustomFormat" isKey="true">
                <StringFormat>yyyy-MM-dd</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text14>
            <Text16 Ref="30" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>15,0,2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text16</Name>
              <Page isRef="4" />
              <Parent isRef="22" />
              <Text>{ds.Yp_Sl}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="31" type="CustomFormat" isKey="true">
                <StringFormat>0.####</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text16>
            <Text18 Ref="32" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>17,0,2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Regular,Point,False,0</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text18</Name>
              <Page isRef="4" />
              <Parent isRef="22" />
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="33" type="CustomFormat" isKey="true">
                <StringFormat>0.####</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text18>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>ds</DataSourceName>
          <Filters isList="true" count="0" />
          <Name>DataBand1</Name>
          <Page isRef="4" />
          <Parent isRef="4" />
          <Sort isList="true" count="0" />
        </DataBand1>
        <GroupFooterBand2 Ref="34" type="GroupFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,7.9,19,0</ClientRectangle>
          <Components isList="true" count="0" />
          <Conditions isList="true" count="0" />
          <Name>GroupFooterBand2</Name>
          <Page isRef="4" />
          <Parent isRef="4" />
        </GroupFooterBand2>
        <GroupFooterBand1 Ref="35" type="GroupFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,8.7,19,0</ClientRectangle>
          <Components isList="true" count="0" />
          <Conditions isList="true" count="0" />
          <Name>GroupFooterBand1</Name>
          <Page isRef="4" />
          <Parent isRef="4" />
        </GroupFooterBand1>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>bcd794663d99417081ed58d02ec4725e</Guid>
      <Margins>1,1,1,1</Margins>
      <Name>Page1</Name>
      <PageHeight>29.7</PageHeight>
      <PageWidth>21</PageWidth>
      <PaperSize>A4</PaperSize>
      <Report isRef="0" />
      <Watermark Ref="36" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page1>
  </Pages>
  <PrinterSettings Ref="37" type="Stimulsoft.Report.Print.StiPrinterSettings" isKey="true" />
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>药房盘点手抄单</ReportAlias>
  <ReportChanged>11/7/2018 1:51:00 PM</ReportChanged>
  <ReportCreated>1/10/2012 4:54:46 PM</ReportCreated>
  <ReportFile>.\Rpt\盘点手抄单.mrt</ReportFile>
  <ReportGuid>f9c57fcb6dd84d4d83edaf258800b34d</ReportGuid>
  <ReportName>药房盘点手抄单</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2011.2.1026</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class Report : Stimulsoft.Report.StiReport
    {
        public Report()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
        #endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>