﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="C1CommandHolder1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <assembly alias="System.Drawing" name="System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="Move1.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAgY0hSTQAAeiYAAICEAAD6AAAAgOgAAHUwAADqYAAAOpgAABdwnLpRPAAAAF9JREFUOE/NjIEJ
        wDAIBJ3NZdx/grYXtISkJGih9ODBxPfkf5jZQfw5sdo1KKjqY2knb3hhKoW4JIjja8wL+mOSEozHJC3w
        +U5KAKMkLYBeUhJA/JcFwO6VALaCjxE5Adwhb9ybKreeAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="Move2.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAgY0hSTQAAeiYAAICEAAD6AAAAgOgAAHUwAADqYAAAOpgAABdwnLpRPAAAAFBJREFUOE/VzMEJ
        ACAMBMHUlmbSfwXqgoL4uxMfLoj53MQ/VVWbpx5jG2CYmR6wxuPUgX3Mk4BzzJOB8fkAnYgM0I5YAC3E
        BojxFUDXwKMiOp6JR30VPd5TAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="Move3.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAgY0hSTQAAeiYAAICEAAD6AAAAgOgAAHUwAADqYAAAOpgAABdwnLpRPAAAAE1JREFUOE/NzMEJ
        ADAIBEFrsxn7ryBhIYLk5wkhB4KfHftzEbHOqw1ghBC7u46cUEcS4CSkAlwbuQFuBLRiVoF2zBKQYkYo
        x2wUP57ZBqb3R30HKjhoAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="Move4.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAgY0hSTQAAeiYAAICEAAD6AAAAgOgAAHUwAADqYAAAOpgAABdwnLpRPAAAAFZJREFUOE/NztEJ
        ACAIBFBncxn3n6Ay+jK1kyA6kAKvR/RfRKSt6xbdZfuZrMTMGJAUMWAcEYIDOg5SA3QMUgd0rgDzoAY4
        ZRwIihiQlOAfRDkDj0PUAaHob9yrL+48AAAAAElFTkSuQmCC
</value>
  </data>
  <data name="Move5.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAgY0hSTQAAeiYAAICEAAD6AAAAgOgAAHUwAADqYAAAOpgAABdwnLpRPAAAAHdJREFUOE/NkEEO
        wCAIBH2bn/G5PomyBQmlaJp6aCdZQ8WdQ8s/aK2Rju+AYEuCcq31Jum90wh/WtydoMVU4iBerAVIIrEd
        HzZrBC9AguQsxuBe1kwUIF4wwofNGiEKXNljguU/mJSBvZkKFuULqeBpOWWr/DGlHJkCnc+Lfq2fAAAA
        AElFTkSuQmCC
</value>
  </data>
  <data name="Comm4.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAA6CSURBVFhHhZhrrGV3WcbXvpx9LjDtTJkypdoIFKZBa1oQ
        GlIgIVGRwAcSISUQsIKJF2xiSIylJjhgagIOqF9MjPETMfpBJJqYAkKHDClaLkPnXPb9fll7r30/Z5+z
        75fH3/vfZ0jtB3bT/+y91tpnrWc97/s87/v+vbmkBWu1WklLlh2w7OvMnT/TSmM+F5rz4zbrB6y/Ph3p
        E82q3lo61YP5ji5maopkiormK7qn2tLV4EyPtqb6dDfQ9eFQN7lnwP1GrAkPmHPPhc404HjsnsM/3He1
        WPJce6K4KnmGZw1saV/4zXppwTn71WSNOcf6j+FCX6i09dFkSe9I5fVgrqAryZruTle1lSvLK1UUqtS0
        Wwt0EZBXym096Nf0jmpNT3DuC92Bvj5bKcW9wCANWbBg5KxB89wVa7l0l+3x3pIDB25hXE44OeNfvnPK
        XveMrzd5zWf8vh4HxJXDgrb2c/L28/KOCvLifCb5NIAVX149kOcHAK3LK1TlAdTL+4pmKrq34OtdtaY+
        1zzRC9xztn6kA2vPnDu0nLBQgcuY9DQ3fgzNxIXTcC84PuVbfb7Sl/oj/TZhe81L+4BKyEsBIt+VlwFI
        qiYvW1ovx2BN4WpDIQB6tQarrp3ysUKlPuCPOe7wt2VdzmT1Eb+l66OVfB5tz3IYFjAy48hAwqSFG4AW
        +qVjbrXiB/Ole6nneb1rnbruSx9pO3UIoLRjwss3FUrXFSLfIkVWrurYsbWVrSpWrCsGS1v1liKNlqKV
        qsLFhiJVQDbP5AU9eeWydss5vb5R0bVeQ9+DJCNT0zVIY9KFm5OeJZgdGMXGnIH7IRc+02jqvsOfAAzW
        shnCVVY4ByOwFrOQZlOwElc0VVUkCTg+tzO+dvMBrLW0VQFgpSmvTQrUSrDYADggG6wqDBdL7gXvT+/r
        qU5XtwybgTzHMuXThONZMjpBnFP93enMgfvFpOVZUtGisUYo00XyraDteEW7iMLLE9Z8TuFUhWvnK1dz
        QAxYuArTFYB02oAysC2FS3wHvFfmGgLaseNMQQ8Uyw6kMenCDRwTjhOJo9ZoI7qWc59v1XTl4McOnFcc
        IQQeTIi8GjcmfKFETdtHsJFsK5SynOIFCLOX43cmChOHD3MNrtfbipXH2i0NtVM85R4n8lqszoDf9BUq
        EO4OQgCkMWnhtpx0eNCLWRAMTh17J/D75d5MFwqELpdWNAeopK/tw562EEQ4D4NZWIWpaB4QZUJXuq27
        kyT/UV7R/SM9XmjoPbzIXokUIPd2TBw10qNaJxcRlina50UaCMqEVAEoqt/FurxCXhf9op6FqA7AnM+I
        l3FQAfj9E6FWPCwZB0TWhcvLYRcpbkwYQ3hejLBGCHH4yFfEwpxFOIfGZFbvJem/NpzqX1DeB/qB9uoo
        2hgKWgqZQPJ9QtxFOKZwsyRSwYfVVpdwE3Ii8OpCUR+qd/W8+aMDiKi0mqkNe5+vdXXP7duKHvLQNAxk
        8touEaocYUyT5LC3zU22koG24n1tJToKJ2DjpYLeDvB/bNeIxZD7jvSv455+s93WdpYo+LBQJVVKI0VK
        A21jL9Eqf1fmJQEYbhMB8nw339ZuFoMn3M+g9raFGvK8OQL51slE78kCav8AYBXFsAsvkXFv5RV4Y3zQ
        w7sc0By5kz1z+ReL+3oTLP4tLDSO2+RxEasoazwZ6J9Px3q3hbNI+ADm1XhYfQS4FnmJNZmSLezBOo/v
        Kp0gJBgln83MvzlamlfjAlD550FTlxPkXgZl8mZRjDdmFQJAFupoEcAZ8s8AlwlXsqHwQU5vKFT0N72q
        ilOyetTXeJTRcpwH5AnZI33tLNDVZtv5o1c0MIQMdUeqvmKNOznZ0k6hiS/CZADTCOkS4f9cr6eeAXyB
        fz5QQgCHJDM0WzWwHHx1ijzB14w9q7NhqxZlQmaM3jrSL9/+qT7Tqagz6mo2O9Zs1NJkHmi+apI/PeCd
        ajTr6NriTO8qFvBLXrAKWz55aSDrfCffPL+rPc47S/JhujFx+fm+Zk0vGsDrZ2P9mjF3wMoC0EqWVQ0+
        o0nyjNIUJiejdCteHoAAf0s8qb+s5nRrVKfg9zQfNnV8Uqa2NsnBriZLA4kW5x1VMIu/P27qnSWqDEx5
        1WMYPGcPG3LW07TzZtx2DMv46FtJj787A/Anm4EeoRJ4B4QBAVxwhku4sZW9Q/INxw/RuUTSAEz42jso
        6knK2P6Muj0JNBsfI7aO5v2qpqMqnk9TtSIfCfKp+oS+pgLJ/tRgqMs51IqytwDplWCMXNumXfNgy/ML
        umjWVjZRtfRwraZPN8HyK3iRtUyxg/Ocw4yd8cKUWUkkSc5lCUuqhHIP9WS5qP0hfj9ra3Zym7xLAszX
        +MTXcuJjWS2WhbgDrAwAswAeKDtb6lpjoDdaJIxJf6QtH5O2AgBYqzQu7LRlZvivLXX0SICwHoDWu/G5
        qLVOBtCqgSU1TIYyAExQtuI5Xc0k9WfNum4NaWDnuOkCEMuGprNAq3HXAZxOajQhMOgAnlBbzXJJAwv3
        YqjUfKxnT7t6G4YcK5kXWkjxSXwwSgmM+nYMwHJNd3H8BvPJvSy2ArAwDDmAWctBALJCWWprijeLJ/Te
        UkLfGPY1oG/snvaVOukqMR0qNySUw4EmJw1NZrV1iBfkIZW0BHO3CHN2HKg/bKgB6K8D+sO9gi6VSCMa
        ECuJYdiLlc6bCwNIm2Y2ZCA9L426MMdQhh8bQJYxZ+BcNcmTqBw/VC7o99uBvtJq6dliRZ/FnP+Egv8P
        9boOBwDCUhYz3zG4WHaVWgz0T71Af9hv67NBTdeCsv5i4Ot3Rg090qtrF6vxCjAWUIlq1GwD6PKS1SAX
        KY+hIs/fDJAQWxtFPt6Nsu9DMK/fT+sXDlK6mEpRHjN6boDFTBHGtAF7AZWzq+/Mu3qintclfPUyPmrd
        9D3Y1B5h3MJaIpUT/JUQbgK4KcReNqsdmoEdsyErbcwjkUMElcCKigk9VijoG3jgctGiESa8eKEJ5D/J
        u8cD/JUZxQmPpHcKLRKRXJc63dGeVZJNId4kEo8SGE5SlxMlbSeteyYv3XlWvqpfp//79rCLqgME09J0
        gQeqpe8iove1YIc0cD0gZhypU8MRRoiux0zf2rGNIrljM1FjxQAarS+zGetCPN7OwESoBjZaegV+k25r
        76W+PkoJ+x/yTGNfZ8uOhuaB5OKLo44+znB0IT3QdhGbshmlTgRQsGu56rBl5W6TzZhRP3zEH5lRp1t6
        FSF0Rk2Xu3vQW+fjK9bPOmhe4jcqh/rOWVnLeU39VVnHqsJgoBuLht7fpnyWeaCFzpZVj1esndKYlsvC
        WtalnLFNJWE0eJjh6/dapIaVukcJoSt11lpZzaXUWYJaqdsE8IO8yM0+Pjfq0bmZN1JZ5gO9MB7oQ9b7
        bQAYstKGULwqEXKlDsCkwdvqPqWOa9Ys/BazhWsWSFzPOheahbsckHW+/VyA+ZZu9KksQ4DRB2pCeaMM
        3hxPaT4RxQaAHnm3x+e6WaAEumbBJ3+r+l/6VNduPe3X1+2WKZhuxtqt3XNVbwTIC/037AmBLKm7mlA5
        1NON5RkMAmAjQGYc+sHdMuQEiIJu5xIN7dO91rrdsk2F5xjOXcN6eMSDy+uGFUFYw7oJ4CdyWf0AcG7n
        ZUb+LajHfH+RbPxdE8MmgAHX6ZguFFG5jQa5kh6nUXhuNNFigUhsKg6Ym6zlv3xwoAgG7Fr+HPWyyEyy
        AeAnU0X912mgFk1CZ9ZVe97DBXv6Jp+fKtlw9fMBhtvmEsxCFISdbJ2WP6dnWgNZZG28+39D00cw1O34
        IQ+nvbdR04amDQB/NdXUHzTq+nKrrK92O3TYXV3v+/qjXluP2nbHJgaZScwrwzmfibJC3vZfNjTRYb98
        7Lx+vNCrcraTkMK7+GPGzk0AvcKp7iFEb0rFdRWvfAiPvJpP616fF/SnmwHSVe9VYJF7XPSr+isaJWzf
        YXJTnU3vRqINyzUGqGsM7q+7zeD+EoN7nkksjpLN+d12BQU+TULT4ewkeOs45mqNbBKgTlQGmN+cA7IK
        sVUbKkYx2LJZxlr+JqvNqIkpx6xZIJYe5n9/+oDBPVgP7gbqzuDudhZsf5D/7cTz05n+uNHULyWwHkCa
        wlzTYGNBsuj2YbaNSZLZy+Xc3syd7Q+r3VbwY9W2m97Cxl7HWAooZcwhNsaanWDCNpfY/o2Be4DK8VSn
        r+/NVuutD0Davozb+rAD26gZIxbbRCLg+hHoDeTrDn5CU4CybfOImSLEww1Q6MhGUBiuJB24cKKsULzk
        rm0zt8QA4gq/DVlNqhTzRhjBxWz+aKxbetvs9Molx5yBs80jI8vjv8k5DtvL9Gy77ZXbb3bxxmyuL3bq
        uj95qF2Wh524rsTadcJsNXm7YCxa9wOIFMqHZatAtkd4Z3/QbWRaLa/icS1UaV5XYrYpFPVglT6RsBpz
        LpL24Lntbdn2m303gDPUYVBJwjsbmAbYwm05+ZXeWB/GDy/99DaDfRw2DaTVaIDGEQzKswR33bHtsMK8
        1wQQs7ZHMxstA6hMi2VDeQ1VkzL3UQCeYLL7KvG8s4FpzBk4zZDwihC6DUzO2y6m2wK2bVfXqM8cRCdz
        fj/l600s6E9J8MeyRd0LU1vWmu3bIk8TfBp7lpPW+1n9NZA0nFY2I1SIULahLcT0Wqzr3QzqTzdP3bby
        yB5picbjLc3WW8AstwW81q4B99z+oIEE9ToP+W7CMcqJuiUss5n+fbTA0Nt6ArU+Rmf9ZqrPFVi8QK8Y
        cSCLKLeqXQbviwjkXob8h7COd1Yq+hiAv9g91r/xxnHuBTk/U6vd30XXzlk0Dcr5ZQfQ/lvv9J+vc/Rg
        468s2ITfgHMp4M43uXj99Eyfavp6rDzUQ3TIr4Eh68x38cErsP2WxlBvb8705EldXxqf6AZ/S6V297T9
        07Vej10qGRCXZRY47m/PtnNnkv4PFMuTxmuzsiIAAAAASUVORK5CYII=
</value>
  </data>
  <data name="Comm5.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAA6FSURBVFhHhZl7zKRnWcbfOXzzHehht2zZFSWCLa1oTQtC
        Q1pIiFE0kNhESAkEqGDiAZsYEmOpCS4YSMAV5R8TY/yLRP1DJJqYokLXrIJWYel+pzmf552Zd87fzDfn
        0+XvfmZtyvLHbPbZd953nnnf673u+77u63nWsz8TSXPGesk/C44rya4NGNKZ1jrn8kxjznzG9bX0pUlf
        z/RrentjrrfUR7pcaWs/7yuWKuq1KV8PZzt6vDTSJxpVXTsf6wYPCLj/jN+uxQM05VkDd097trtkz18y
        YcW4/ceBc6c2AWTg46drre2i/ZIJ9rtTxt/PFvpcu6cPV2p6Z7msh6sV3Z8PdKEUaL9cV6hUkZctKJLM
        6+54UZdPK3pzJq/Hkzk9nSrps35L/zBeKsO93P0N3YwBoBkAFgbC2Fna2ID0XkG/MOrmTJoDkAt2DXAT
        0N2Ayuca53pXtanXFeragaFQpq5IqS2vACgAe/UGI5BX9gFZlAdIL844zDLy2jnJ6/5EUY9nCvp92L7R
        B9uGDcfQhCdPHCPGLF8Acs3wXFgNNeA0H/Gl/UIEVapy+St8eLrS0RVC5+Wq8vwzQJzLKw0ULXGsAS4A
        XKO5AVlmTr7EXEa+LA8WQ2mu57vyMsw9PNXFH9zSB7Jl/Wl3Ih+mLJUce0Bcr4ccwWCY5kt5FlN7EWPO
        gYNyO//3+VpXu4EeqPg6yBfkFXlwAKDmFMY4FpsKlXmgb6OucIUQF2AvfZu9NL/JlrSbLypin1MGHKAF
        5mcz2k8c6/WMz7druj63DDcQsLYeOyYdYIB4BtS+dGHlj4X+Jh+fJddenzqCKR5YhImKMdRXzO8rTJi9
        Mg9tMEqEutxQrNjQbq6uaBLGTosKx0sKJ/ldOQG4hEInmc13RdKgSFpk0qTAiX7s6Pv6XZj/X/gxHAbM
        wm05aX83RWL5yImF1ZgzcG/IcxPefKcMsApvDkNeOVAEINES5zUe1G4qTIFEAR+rtPguIDdhMVV24AyQ
        l80qRE7upiuKJACc4KWM5azPC8Hqywn9ZDzrQL44m2+Uw+FZu0h67oLlJkgt5yysjjkLS3utWL6lUI1i
        aHVgjCMJvlPqKVYeaMcfEWYrjNsDmdmEmN9SEJFUReHTpvbiLe0neVELc4FRYS5RiJwyPz92IK8cfk9X
        m77LSVc4YDLyPImkBFiHky9S9heQDkvwg3IPlgBlob1zEFI3YM+rznR/ta6Hcin9NA9/KFfRg8lT3eeK
        hFxNcoRRGyFYvHN4iap2C9wzm9JrsnFdO1uqb4ULJhMfAIIK0C9SwE/Verqbygtnq2haC9ao2C0AHyuf
        6Xe6LV3rVfVn3Y6+0mnry82Sfqte088lmbMNYHbDppfKaPf0WB+kiP4DCTKARiMAp2oB8PnmQBfSWe0h
        Bfs5blz0FW7B4haAnyjW9M1FV2111bLjvKPmqql/Pg/0MQv1FoCxAgARdy+VV+QoqUtHRwh6R8GmrOUt
        l3O9MJ7qCX8jsJEKLBbINQtRwI+3APz1alkv0Q5pZEhAlZeuuM/fnTf1UeRkG8AQeugls4plYJNu4x2f
        6N10nxd6Y9dQvC7/PNdt6mKFiVXEFK3bp0O4CqsCegvAp+pNXV+ZuHYJRk2rMd0acP827ur91mW2hZgG
        sJ8sKpoxOeN5mZIuxZN6rlpzkfX+m4R8b5OJ1gHqiHD1zFXmgQGpkod3grPxKoC/WuvrxoR4zAE57VF6
        AB2d6XrvXO/PMXcrQF/3uCNs2wvlid5xWr+cy+o7xuBXh0O9rQa4MmD8iSIFA8XEAHnxhz8K7g6AT9F/
        vzNBrHAmWuF85gCEvRu9NgwCbgvAaKJ9uwOlYM/0EYBHRT2WyOnacCLvN5o1PWIi7FMQpaEuZg0oN20G
        2isyYQvAX2mldX1ZJ8Q1MrGi3rqk1aKsbw1L+sXy8VaA+0eklb1INqnXoJ1eivsfVfTISUEfa1ADjwZz
        va4IauscPp0CUY5Y97CW5HOso4U1wFAMXpVqq3Gk9+4Wuro7NdBHGn29NG4T4qpG65aGKz5PqvqvXksf
        KvR08DIvnuKlEXHryREKwkMrrZd7uZ4Tc/cSWY7Wp8nJ2FFOlxO+fpZm4L2peq57SkwuQTMAo+RdlPOQ
        tTMAhmrcHCkJ5bFZpEEErbRu4rk5LfK3pW8vCauami0bmlEgmgf611FHv0CfdmCoUGMrmqa9JSraxSuG
        CaFHtdp1x7BVsZEEwCjW7F5a5Rss3Qxc1FqPcyUwSOhivF3YWKy36CgdhS1xcchegTcq9QHMub0t3u+J
        oKB/WsMaSqhFoPU00GrZ1DfGLT2ex9UU4piClCLHALIQxsvaI8f2TgxgZtO7DaQNKtgAhqlqc+YHGYC7
        BHWG0/LQzEDgAIZwv1Yo0cI5oPvaqXZ0ALv3wcr9+aouUXEXS1U9XcvpW4sOwk+zXKKFs7qms4ZeGDT1
        a7m0LiST+nEE+I2HKV3BWd+LBdu3Hp2mMK0hYBoM5IZJy8EinwGa5uVSpMM2gB5mYb9S1aPdmj4+ruuP
        BlVdDUr6dODrt8mzv8ZcJJcDRLWDxwy0JBenw0DHg47+EjP7e6TBp3NFfaGAQW029ZutQA+XzC8CKEdE
        tgHcFmJzxxeLSX2gm9fXCWNdffVGdWUmgW6qp+J6QLO0hUsHLxloOvc17dd1PhooO+orPhsp2e+oc97T
        YLnQN0Y9vacYx8nEFcLhbA3xtiIxfdxDBt5GBX8RVpILA4OzcHlXY0FlR7r7qusYnE19TfpVrScdCoaQ
        r5Agimi9GOvmaKg/aNT0UDqBDcsqEm9sL5JtMrNTXSMveDbs+k9RhVfrA2Xm5oUQ5nEGv5HeFAgAtW5o
        Na06gLNxVeNJQvP+Laq6pcPRuZ4pYYDjpo2EMtPFwN4pM5BkACmoV2Tmk42yHjGjYAJMhV7MAaxK/Jv4
        tCJ+zodJwO5UaIEsfC5lG3p2MFLeHCV995wwOwbRQAvxbFzRoodhGLY1n2AiqOpD2uAztabuOkJH41VA
        kXMUTKhAGI+RrRzXEeq7jUmzaAj1o7hvJ9RfHQ70VhNh6wy0tk2r4w0bdo6kmA4CPsLKzgNkDCbfyZri
        L84aKptBX8Ae4Z6uGtjKjuaLhs76JS1GDXpyVzfHNf1xJau3nLI2MaZs3UIkwtirMI7JWp0trlyrc0eI
        QoZ+HmfuWt1LROu9DRhk4b0xCwOXfwcmxkiLuWoLu1uXmNvhepRu8GQhr6vLocZzy0FWM+uuFoR4ihbO
        x03N52dqjzv6VLusn7n1A3k3T0gTGEGmwvTcHQOTgxjk5i7XDjEL5mZyEIJZeF+xsDELZrc+0+3qogE0
        xoIxdutMe7ZEZKFu7MVodxGkxoEMMBCFjnZI6ocaLX0NSTEDrFlfq0lOkzE5Oe6pMJvisCt6kzn0I+Qi
        wf1Zy3jm/zDGUXMuSIzlXOw0t7Fb6Kqtacxu/SFrbWe3VgD85nilJy3XSNQIi/J7igA1PxjAHJ3EZChW
        olVZntYoGB8rXiSBSYd3kR5/M0RopoR7VgJcQfWzlv682dGDmRQPZ/mQxLFkhphT68sAScOWsQejzrDG
        0xvDmubz4ZEzrP/Sn2rBAgrLP1cLkM83hlj+vPYzAZYfmlkAhVmTRJAhe7MohnYXRiMGrAjICqPa1y5L
        hF9qtfR3+MA165uZRvqrlq+3I87ey+QZrW0nzkrwtKedRKBdAIUyMGpAsVa7LGO9NG2PnIwep3TfrVvO
        8rfcjodbk2A0X1k0dXRXvuDeyukgLDiAFn6YiuD9wkXyEhcSYmngBQg6lX1Q8/W+XqC/xaN/bTTTe0qs
        hZOwdMyLwqJJSeS0xigrBvBQFpVA50JJ7mWLJhdq2204pT36dy6aTCKckukLbtlJyedz2jcDa4tz06Iq
        xzqJbFaLMEcJu2uPfhrPyOdyRQfFvN6Nbj6B64keUhCYgXsTrAqLt4gAOZZjvhNk2IOEnTRsHlN0LDuj
        WQjIIDP5pL7cnf/wspPMMaBusbxZuNdZuB9CO29Jlpr2eVXYagO0SW5SSHsYiP3iyBlat6i3YV3H7JUt
        3k10YSWcxaaRf7FES7sndYXifGcuyObaUvMEwAVS5TChy0ff02dZuNcWsGXbcuBxOwubrQ8u8tdtfSyW
        erbdoc2YJuUBgfW3NbKFnBYY5tx1GhZLXpvvWL+EqW7r4aEiD3Xh4sEMtzeThzHCag5mlxB7p2YCiBKh
        jNouA+B+IpHVp+oNfXs2dxhceCkQIMkzbLZRQ824b+zfzeZRxzEZKXAzFN+2K8KY1RhLAwfE51o754DZ
        /s0e4PfpQjF0bSdJxd8eXvnUhS+W4GWTG1bd9pxtHqXjunL8fQfuf3iw7Xgsb2Nxu1uE2rMtLztx229L
        uJ1tQBqTFu431svoIiyUuGlAuKl2WzuHi+QOuReByR3aWMzHBRE+08cocmEjYrLFC4aw+qEUwMz/mXzR
        NXaTx7qSOtHVdk0vzhcO3I9uv7kNTD64DUymzCHYQPKlUW05eW281geRl0tOuwDpmzbSl4s9WKMIbu8P
        higk2yN03cC6hAmvDVjzKAgvRzElLaRxvfblQ6q1rC+xOLecs2cZcz+0gekALm0LmBibWrstYCN3Tsjt
        82YeL6f/pNA/w+LoSYCYmzZ2HBMm3Lggt5Fple6qnusGkJB6dAgP6+Qd5rRzmNXl46ye4LvnKTrbVh5a
        qDZ8uLBO/79a8Y22rbAC16s20ZkFk0at5aQrHJuPPtqEJIevz9f6XGegp7Fl76j4eqDq6zIFdAGg+1wL
        2f50EWEGxL0UhVmmB9C8d+BcPsR65PNI1z+OlspyL1MSR5QBoCA2aWYsMiyqq7Wb86r/hqBq7AuXB5vf
        mQQt+XfOa075ZC+LBdUNfnltNNInO4Eea870IP35PtNHwmZLywu4lQdybb0Vu/aRRkV/Mhzqu5DSYrhA
        rZc8bQKUoePA/a+DobG9QU6MHDtdSPo/Cv+fzVGjYPUAAAAASUVORK5CYII=
</value>
  </data>
</root>