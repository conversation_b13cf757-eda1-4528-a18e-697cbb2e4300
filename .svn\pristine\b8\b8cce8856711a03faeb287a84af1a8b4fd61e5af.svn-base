﻿'------------------------------------------------------------------------------
' <auto-generated>
'     此代码由工具生成。
'     运行时版本:4.0.30319.42000
'
'     对此文件的更改可能会导致不正确的行为，并且如果
'     重新生成代码，这些更改将会丢失。
' </auto-generated>
'------------------------------------------------------------------------------

Option Strict Off
Option Explicit On

Imports System
Imports System.ComponentModel
Imports System.Data
Imports System.Diagnostics
Imports System.Web.Services
Imports System.Web.Services.Protocols
Imports System.Xml.Serialization

'
'此源代码是由 Microsoft.VSDesigner 4.0.30319.42000 版自动生成。
'
Namespace JkkService
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code"),  _
     System.Web.Services.WebServiceBindingAttribute(Name:="WebService1Soap", [Namespace]:="http://tempuri.org/")>  _
    Partial Public Class WebService1
        Inherits System.Web.Services.Protocols.SoapHttpClientProtocol
        
        Private getRy_ZjOperationCompleted As System.Threading.SendOrPostCallback
        
        Private getMzDataNewOperationCompleted As System.Threading.SendOrPostCallback
        
        Private getYyJsrOperationCompleted As System.Threading.SendOrPostCallback
        
        Private changePwdOperationCompleted As System.Threading.SendOrPostCallback
        
        Private getCityOperationCompleted As System.Threading.SendOrPostCallback
        
        Private getYyNameOperationCompleted As System.Threading.SendOrPostCallback
        
        Private getSexOperationCompleted As System.Threading.SendOrPostCallback
        
        Private getMZOperationCompleted As System.Threading.SendOrPostCallback
        
        Private getWHCDOperationCompleted As System.Threading.SendOrPostCallback
        
        Private getHYZKOperationCompleted As System.Threading.SendOrPostCallback
        
        Private getZYOperationCompleted As System.Threading.SendOrPostCallback
        
        Private getZJLBOperationCompleted As System.Threading.SendOrPostCallback
        
        Private getABXXOperationCompleted As System.Threading.SendOrPostCallback
        
        Private getRHXXOperationCompleted As System.Threading.SendOrPostCallback
        
        Private getZyDataOperationCompleted As System.Threading.SendOrPostCallback
        
        Private getZyDataXmlOperationCompleted As System.Threading.SendOrPostCallback
        
        Private getZymxOperationCompleted As System.Threading.SendOrPostCallback
        
        Private getZymxXmlOperationCompleted As System.Threading.SendOrPostCallback
        
        Private getMzDataOperationCompleted As System.Threading.SendOrPostCallback
        
        Private getMzDataXmlOperationCompleted As System.Threading.SendOrPostCallback
        
        Private getMzmxOperationCompleted As System.Threading.SendOrPostCallback
        
        Private getMzmxXmlOperationCompleted As System.Threading.SendOrPostCallback
        
        Private getMzLsJl_DateOperationCompleted As System.Threading.SendOrPostCallback
        
        Private CheckATROperationCompleted As System.Threading.SendOrPostCallback
        
        Private getAccountOperationCompleted As System.Threading.SendOrPostCallback
        
        Private addConsumeJsrOperationCompleted As System.Threading.SendOrPostCallback
        
        Private CancelConsumeJsrOperationCompleted As System.Threading.SendOrPostCallback
        
        Private addPOSRechargeOperationCompleted As System.Threading.SendOrPostCallback
        
        Private addCashRechargeOperationCompleted As System.Threading.SendOrPostCallback
        
        Private addAppRechargeOperationCompleted As System.Threading.SendOrPostCallback
        
        Private addAlipayRechargeOperationCompleted As System.Threading.SendOrPostCallback
        
        Private AccToCashOperationCompleted As System.Threading.SendOrPostCallback
        
        Private AccToBankOperationCompleted As System.Threading.SendOrPostCallback
        
        Private JKKCzJzOperationCompleted As System.Threading.SendOrPostCallback
        
        Private JKKXFJzOperationCompleted As System.Threading.SendOrPostCallback
        
        Private ConsumeListOperationCompleted As System.Threading.SendOrPostCallback
        
        Private getJzTimeOperationCompleted As System.Threading.SendOrPostCallback
        
        Private getJzDataNewOperationCompleted As System.Threading.SendOrPostCallback
        
        Private getJzDataOperationCompleted As System.Threading.SendOrPostCallback
        
        Private CxJzDataOperationCompleted As System.Threading.SendOrPostCallback
        
        Private CxMxData_DateOperationCompleted As System.Threading.SendOrPostCallback
        
        Private CxMxData_JzCodeOperationCompleted As System.Threading.SendOrPostCallback
        
        Private addBkOperationCompleted As System.Threading.SendOrPostCallback
        
        Private RemoveBkOperationCompleted As System.Threading.SendOrPostCallback
        
        Private CheckBkStateOperationCompleted As System.Threading.SendOrPostCallback
        
        Private TempGetAccountOperationCompleted As System.Threading.SendOrPostCallback
        
        Private getTempTellAddOperationCompleted As System.Threading.SendOrPostCallback
        
        Private TempNewCardOperationCompleted As System.Threading.SendOrPostCallback
        
        Private TempAddPOSRechargeOperationCompleted As System.Threading.SendOrPostCallback
        
        Private TempAddCashRechargeOperationCompleted As System.Threading.SendOrPostCallback
        
        Private TempAddConsumeJsrOperationCompleted As System.Threading.SendOrPostCallback
        
        Private TempCancelConsumeJsrOperationCompleted As System.Threading.SendOrPostCallback
        
        Private TempAccToCashOperationCompleted As System.Threading.SendOrPostCallback
        
        Private TempConsumeListOperationCompleted As System.Threading.SendOrPostCallback
        
        Private TempCancelCardOperationCompleted As System.Threading.SendOrPostCallback
        
        Private TempCheckCancelOperationCompleted As System.Threading.SendOrPostCallback
        
        Private TempCheckStateOperationCompleted As System.Threading.SendOrPostCallback
        
        Private getRy_BaseOperationCompleted As System.Threading.SendOrPostCallback
        
        Private getRy_GuoMinOperationCompleted As System.Threading.SendOrPostCallback
        
        Private getRy_HealthOperationCompleted As System.Threading.SendOrPostCallback
        
        Private getRy_InfoOperationCompleted As System.Threading.SendOrPostCallback
        
        Private getRy_LxfsOperationCompleted As System.Threading.SendOrPostCallback
        
        Private getRy_MianYiOperationCompleted As System.Threading.SendOrPostCallback
        
        Private useDefaultCredentialsSetExplicitly As Boolean
        
        '''<remarks/>
        Public Sub New()
            MyBase.New
            Me.Url = Global.HisVar.My.MySettings.Default.HisVar_JkkService_WebService1
            If (Me.IsLocalFileSystemWebService(Me.Url) = true) Then
                Me.UseDefaultCredentials = true
                Me.useDefaultCredentialsSetExplicitly = false
            Else
                Me.useDefaultCredentialsSetExplicitly = true
            End If
        End Sub
        
        Public Shadows Property Url() As String
            Get
                Return MyBase.Url
            End Get
            Set
                If (((Me.IsLocalFileSystemWebService(MyBase.Url) = true)  _
                            AndAlso (Me.useDefaultCredentialsSetExplicitly = false))  _
                            AndAlso (Me.IsLocalFileSystemWebService(value) = false)) Then
                    MyBase.UseDefaultCredentials = false
                End If
                MyBase.Url = value
            End Set
        End Property
        
        Public Shadows Property UseDefaultCredentials() As Boolean
            Get
                Return MyBase.UseDefaultCredentials
            End Get
            Set
                MyBase.UseDefaultCredentials = value
                Me.useDefaultCredentialsSetExplicitly = true
            End Set
        End Property
        
        '''<remarks/>
        Public Event getRy_ZjCompleted As getRy_ZjCompletedEventHandler
        
        '''<remarks/>
        Public Event getMzDataNewCompleted As getMzDataNewCompletedEventHandler
        
        '''<remarks/>
        Public Event getYyJsrCompleted As getYyJsrCompletedEventHandler
        
        '''<remarks/>
        Public Event changePwdCompleted As changePwdCompletedEventHandler
        
        '''<remarks/>
        Public Event getCityCompleted As getCityCompletedEventHandler
        
        '''<remarks/>
        Public Event getYyNameCompleted As getYyNameCompletedEventHandler
        
        '''<remarks/>
        Public Event getSexCompleted As getSexCompletedEventHandler
        
        '''<remarks/>
        Public Event getMZCompleted As getMZCompletedEventHandler
        
        '''<remarks/>
        Public Event getWHCDCompleted As getWHCDCompletedEventHandler
        
        '''<remarks/>
        Public Event getHYZKCompleted As getHYZKCompletedEventHandler
        
        '''<remarks/>
        Public Event getZYCompleted As getZYCompletedEventHandler
        
        '''<remarks/>
        Public Event getZJLBCompleted As getZJLBCompletedEventHandler
        
        '''<remarks/>
        Public Event getABXXCompleted As getABXXCompletedEventHandler
        
        '''<remarks/>
        Public Event getRHXXCompleted As getRHXXCompletedEventHandler
        
        '''<remarks/>
        Public Event getZyDataCompleted As getZyDataCompletedEventHandler
        
        '''<remarks/>
        Public Event getZyDataXmlCompleted As getZyDataXmlCompletedEventHandler
        
        '''<remarks/>
        Public Event getZymxCompleted As getZymxCompletedEventHandler
        
        '''<remarks/>
        Public Event getZymxXmlCompleted As getZymxXmlCompletedEventHandler
        
        '''<remarks/>
        Public Event getMzDataCompleted As getMzDataCompletedEventHandler
        
        '''<remarks/>
        Public Event getMzDataXmlCompleted As getMzDataXmlCompletedEventHandler
        
        '''<remarks/>
        Public Event getMzmxCompleted As getMzmxCompletedEventHandler
        
        '''<remarks/>
        Public Event getMzmxXmlCompleted As getMzmxXmlCompletedEventHandler
        
        '''<remarks/>
        Public Event getMzLsJl_DateCompleted As getMzLsJl_DateCompletedEventHandler
        
        '''<remarks/>
        Public Event CheckATRCompleted As CheckATRCompletedEventHandler
        
        '''<remarks/>
        Public Event getAccountCompleted As getAccountCompletedEventHandler
        
        '''<remarks/>
        Public Event addConsumeJsrCompleted As addConsumeJsrCompletedEventHandler
        
        '''<remarks/>
        Public Event CancelConsumeJsrCompleted As CancelConsumeJsrCompletedEventHandler
        
        '''<remarks/>
        Public Event addPOSRechargeCompleted As addPOSRechargeCompletedEventHandler
        
        '''<remarks/>
        Public Event addCashRechargeCompleted As addCashRechargeCompletedEventHandler
        
        '''<remarks/>
        Public Event addAppRechargeCompleted As addAppRechargeCompletedEventHandler
        
        '''<remarks/>
        Public Event addAlipayRechargeCompleted As addAlipayRechargeCompletedEventHandler
        
        '''<remarks/>
        Public Event AccToCashCompleted As AccToCashCompletedEventHandler
        
        '''<remarks/>
        Public Event AccToBankCompleted As AccToBankCompletedEventHandler
        
        '''<remarks/>
        Public Event JKKCzJzCompleted As JKKCzJzCompletedEventHandler
        
        '''<remarks/>
        Public Event JKKXFJzCompleted As JKKXFJzCompletedEventHandler
        
        '''<remarks/>
        Public Event ConsumeListCompleted As ConsumeListCompletedEventHandler
        
        '''<remarks/>
        Public Event getJzTimeCompleted As getJzTimeCompletedEventHandler
        
        '''<remarks/>
        Public Event getJzDataNewCompleted As getJzDataNewCompletedEventHandler
        
        '''<remarks/>
        Public Event getJzDataCompleted As getJzDataCompletedEventHandler
        
        '''<remarks/>
        Public Event CxJzDataCompleted As CxJzDataCompletedEventHandler
        
        '''<remarks/>
        Public Event CxMxData_DateCompleted As CxMxData_DateCompletedEventHandler
        
        '''<remarks/>
        Public Event CxMxData_JzCodeCompleted As CxMxData_JzCodeCompletedEventHandler
        
        '''<remarks/>
        Public Event addBkCompleted As addBkCompletedEventHandler
        
        '''<remarks/>
        Public Event RemoveBkCompleted As RemoveBkCompletedEventHandler
        
        '''<remarks/>
        Public Event CheckBkStateCompleted As CheckBkStateCompletedEventHandler
        
        '''<remarks/>
        Public Event TempGetAccountCompleted As TempGetAccountCompletedEventHandler
        
        '''<remarks/>
        Public Event getTempTellAddCompleted As getTempTellAddCompletedEventHandler
        
        '''<remarks/>
        Public Event TempNewCardCompleted As TempNewCardCompletedEventHandler
        
        '''<remarks/>
        Public Event TempAddPOSRechargeCompleted As TempAddPOSRechargeCompletedEventHandler
        
        '''<remarks/>
        Public Event TempAddCashRechargeCompleted As TempAddCashRechargeCompletedEventHandler
        
        '''<remarks/>
        Public Event TempAddConsumeJsrCompleted As TempAddConsumeJsrCompletedEventHandler
        
        '''<remarks/>
        Public Event TempCancelConsumeJsrCompleted As TempCancelConsumeJsrCompletedEventHandler
        
        '''<remarks/>
        Public Event TempAccToCashCompleted As TempAccToCashCompletedEventHandler
        
        '''<remarks/>
        Public Event TempConsumeListCompleted As TempConsumeListCompletedEventHandler
        
        '''<remarks/>
        Public Event TempCancelCardCompleted As TempCancelCardCompletedEventHandler
        
        '''<remarks/>
        Public Event TempCheckCancelCompleted As TempCheckCancelCompletedEventHandler
        
        '''<remarks/>
        Public Event TempCheckStateCompleted As TempCheckStateCompletedEventHandler
        
        '''<remarks/>
        Public Event getRy_BaseCompleted As getRy_BaseCompletedEventHandler
        
        '''<remarks/>
        Public Event getRy_GuoMinCompleted As getRy_GuoMinCompletedEventHandler
        
        '''<remarks/>
        Public Event getRy_HealthCompleted As getRy_HealthCompletedEventHandler
        
        '''<remarks/>
        Public Event getRy_InfoCompleted As getRy_InfoCompletedEventHandler
        
        '''<remarks/>
        Public Event getRy_LxfsCompleted As getRy_LxfsCompletedEventHandler
        
        '''<remarks/>
        Public Event getRy_MianYiCompleted As getRy_MianYiCompletedEventHandler
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/getRy_Zj", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function getRy_Zj(ByVal sfzh As String) As String
            Dim results() As Object = Me.Invoke("getRy_Zj", New Object() {sfzh})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub getRy_ZjAsync(ByVal sfzh As String)
            Me.getRy_ZjAsync(sfzh, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub getRy_ZjAsync(ByVal sfzh As String, ByVal userState As Object)
            If (Me.getRy_ZjOperationCompleted Is Nothing) Then
                Me.getRy_ZjOperationCompleted = AddressOf Me.OngetRy_ZjOperationCompleted
            End If
            Me.InvokeAsync("getRy_Zj", New Object() {sfzh}, Me.getRy_ZjOperationCompleted, userState)
        End Sub
        
        Private Sub OngetRy_ZjOperationCompleted(ByVal arg As Object)
            If (Not (Me.getRy_ZjCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent getRy_ZjCompleted(Me, New getRy_ZjCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/getMzDataNew", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function getMzDataNew(ByVal sfzh As String) As String
            Dim results() As Object = Me.Invoke("getMzDataNew", New Object() {sfzh})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub getMzDataNewAsync(ByVal sfzh As String)
            Me.getMzDataNewAsync(sfzh, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub getMzDataNewAsync(ByVal sfzh As String, ByVal userState As Object)
            If (Me.getMzDataNewOperationCompleted Is Nothing) Then
                Me.getMzDataNewOperationCompleted = AddressOf Me.OngetMzDataNewOperationCompleted
            End If
            Me.InvokeAsync("getMzDataNew", New Object() {sfzh}, Me.getMzDataNewOperationCompleted, userState)
        End Sub
        
        Private Sub OngetMzDataNewOperationCompleted(ByVal arg As Object)
            If (Not (Me.getMzDataNewCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent getMzDataNewCompleted(Me, New getMzDataNewCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/getYyJsr", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function getYyJsr(ByVal Name As String, ByVal Pwd As String) As System.Data.DataTable
            Dim results() As Object = Me.Invoke("getYyJsr", New Object() {Name, Pwd})
            Return CType(results(0),System.Data.DataTable)
        End Function
        
        '''<remarks/>
        Public Overloads Sub getYyJsrAsync(ByVal Name As String, ByVal Pwd As String)
            Me.getYyJsrAsync(Name, Pwd, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub getYyJsrAsync(ByVal Name As String, ByVal Pwd As String, ByVal userState As Object)
            If (Me.getYyJsrOperationCompleted Is Nothing) Then
                Me.getYyJsrOperationCompleted = AddressOf Me.OngetYyJsrOperationCompleted
            End If
            Me.InvokeAsync("getYyJsr", New Object() {Name, Pwd}, Me.getYyJsrOperationCompleted, userState)
        End Sub
        
        Private Sub OngetYyJsrOperationCompleted(ByVal arg As Object)
            If (Not (Me.getYyJsrCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent getYyJsrCompleted(Me, New getYyJsrCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/changePwd", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function changePwd(ByVal Jsr_Code As String, ByVal oldPwd As String, ByVal newPwd As String) As String
            Dim results() As Object = Me.Invoke("changePwd", New Object() {Jsr_Code, oldPwd, newPwd})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub changePwdAsync(ByVal Jsr_Code As String, ByVal oldPwd As String, ByVal newPwd As String)
            Me.changePwdAsync(Jsr_Code, oldPwd, newPwd, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub changePwdAsync(ByVal Jsr_Code As String, ByVal oldPwd As String, ByVal newPwd As String, ByVal userState As Object)
            If (Me.changePwdOperationCompleted Is Nothing) Then
                Me.changePwdOperationCompleted = AddressOf Me.OnchangePwdOperationCompleted
            End If
            Me.InvokeAsync("changePwd", New Object() {Jsr_Code, oldPwd, newPwd}, Me.changePwdOperationCompleted, userState)
        End Sub
        
        Private Sub OnchangePwdOperationCompleted(ByVal arg As Object)
            If (Not (Me.changePwdCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent changePwdCompleted(Me, New changePwdCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/getCity", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function getCity() As String
            Dim results() As Object = Me.Invoke("getCity", New Object(-1) {})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub getCityAsync()
            Me.getCityAsync(Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub getCityAsync(ByVal userState As Object)
            If (Me.getCityOperationCompleted Is Nothing) Then
                Me.getCityOperationCompleted = AddressOf Me.OngetCityOperationCompleted
            End If
            Me.InvokeAsync("getCity", New Object(-1) {}, Me.getCityOperationCompleted, userState)
        End Sub
        
        Private Sub OngetCityOperationCompleted(ByVal arg As Object)
            If (Not (Me.getCityCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent getCityCompleted(Me, New getCityCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/getYyName", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function getYyName(ByVal Yy_Code As String) As String
            Dim results() As Object = Me.Invoke("getYyName", New Object() {Yy_Code})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub getYyNameAsync(ByVal Yy_Code As String)
            Me.getYyNameAsync(Yy_Code, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub getYyNameAsync(ByVal Yy_Code As String, ByVal userState As Object)
            If (Me.getYyNameOperationCompleted Is Nothing) Then
                Me.getYyNameOperationCompleted = AddressOf Me.OngetYyNameOperationCompleted
            End If
            Me.InvokeAsync("getYyName", New Object() {Yy_Code}, Me.getYyNameOperationCompleted, userState)
        End Sub
        
        Private Sub OngetYyNameOperationCompleted(ByVal arg As Object)
            If (Not (Me.getYyNameCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent getYyNameCompleted(Me, New getYyNameCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/getSex", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function getSex(ByVal code As String) As String
            Dim results() As Object = Me.Invoke("getSex", New Object() {code})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub getSexAsync(ByVal code As String)
            Me.getSexAsync(code, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub getSexAsync(ByVal code As String, ByVal userState As Object)
            If (Me.getSexOperationCompleted Is Nothing) Then
                Me.getSexOperationCompleted = AddressOf Me.OngetSexOperationCompleted
            End If
            Me.InvokeAsync("getSex", New Object() {code}, Me.getSexOperationCompleted, userState)
        End Sub
        
        Private Sub OngetSexOperationCompleted(ByVal arg As Object)
            If (Not (Me.getSexCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent getSexCompleted(Me, New getSexCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/getMZ", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function getMZ(ByVal code As String) As String
            Dim results() As Object = Me.Invoke("getMZ", New Object() {code})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub getMZAsync(ByVal code As String)
            Me.getMZAsync(code, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub getMZAsync(ByVal code As String, ByVal userState As Object)
            If (Me.getMZOperationCompleted Is Nothing) Then
                Me.getMZOperationCompleted = AddressOf Me.OngetMZOperationCompleted
            End If
            Me.InvokeAsync("getMZ", New Object() {code}, Me.getMZOperationCompleted, userState)
        End Sub
        
        Private Sub OngetMZOperationCompleted(ByVal arg As Object)
            If (Not (Me.getMZCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent getMZCompleted(Me, New getMZCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/getWHCD", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function getWHCD(ByVal code As String) As String
            Dim results() As Object = Me.Invoke("getWHCD", New Object() {code})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub getWHCDAsync(ByVal code As String)
            Me.getWHCDAsync(code, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub getWHCDAsync(ByVal code As String, ByVal userState As Object)
            If (Me.getWHCDOperationCompleted Is Nothing) Then
                Me.getWHCDOperationCompleted = AddressOf Me.OngetWHCDOperationCompleted
            End If
            Me.InvokeAsync("getWHCD", New Object() {code}, Me.getWHCDOperationCompleted, userState)
        End Sub
        
        Private Sub OngetWHCDOperationCompleted(ByVal arg As Object)
            If (Not (Me.getWHCDCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent getWHCDCompleted(Me, New getWHCDCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/getHYZK", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function getHYZK(ByVal code As String) As String
            Dim results() As Object = Me.Invoke("getHYZK", New Object() {code})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub getHYZKAsync(ByVal code As String)
            Me.getHYZKAsync(code, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub getHYZKAsync(ByVal code As String, ByVal userState As Object)
            If (Me.getHYZKOperationCompleted Is Nothing) Then
                Me.getHYZKOperationCompleted = AddressOf Me.OngetHYZKOperationCompleted
            End If
            Me.InvokeAsync("getHYZK", New Object() {code}, Me.getHYZKOperationCompleted, userState)
        End Sub
        
        Private Sub OngetHYZKOperationCompleted(ByVal arg As Object)
            If (Not (Me.getHYZKCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent getHYZKCompleted(Me, New getHYZKCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/getZY", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function getZY(ByVal code As String) As String
            Dim results() As Object = Me.Invoke("getZY", New Object() {code})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub getZYAsync(ByVal code As String)
            Me.getZYAsync(code, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub getZYAsync(ByVal code As String, ByVal userState As Object)
            If (Me.getZYOperationCompleted Is Nothing) Then
                Me.getZYOperationCompleted = AddressOf Me.OngetZYOperationCompleted
            End If
            Me.InvokeAsync("getZY", New Object() {code}, Me.getZYOperationCompleted, userState)
        End Sub
        
        Private Sub OngetZYOperationCompleted(ByVal arg As Object)
            If (Not (Me.getZYCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent getZYCompleted(Me, New getZYCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/getZJLB", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function getZJLB(ByVal code As String) As String
            Dim results() As Object = Me.Invoke("getZJLB", New Object() {code})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub getZJLBAsync(ByVal code As String)
            Me.getZJLBAsync(code, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub getZJLBAsync(ByVal code As String, ByVal userState As Object)
            If (Me.getZJLBOperationCompleted Is Nothing) Then
                Me.getZJLBOperationCompleted = AddressOf Me.OngetZJLBOperationCompleted
            End If
            Me.InvokeAsync("getZJLB", New Object() {code}, Me.getZJLBOperationCompleted, userState)
        End Sub
        
        Private Sub OngetZJLBOperationCompleted(ByVal arg As Object)
            If (Not (Me.getZJLBCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent getZJLBCompleted(Me, New getZJLBCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/getABXX", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function getABXX(ByVal code As String) As String
            Dim results() As Object = Me.Invoke("getABXX", New Object() {code})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub getABXXAsync(ByVal code As String)
            Me.getABXXAsync(code, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub getABXXAsync(ByVal code As String, ByVal userState As Object)
            If (Me.getABXXOperationCompleted Is Nothing) Then
                Me.getABXXOperationCompleted = AddressOf Me.OngetABXXOperationCompleted
            End If
            Me.InvokeAsync("getABXX", New Object() {code}, Me.getABXXOperationCompleted, userState)
        End Sub
        
        Private Sub OngetABXXOperationCompleted(ByVal arg As Object)
            If (Not (Me.getABXXCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent getABXXCompleted(Me, New getABXXCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/getRHXX", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function getRHXX(ByVal code As String) As String
            Dim results() As Object = Me.Invoke("getRHXX", New Object() {code})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub getRHXXAsync(ByVal code As String)
            Me.getRHXXAsync(code, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub getRHXXAsync(ByVal code As String, ByVal userState As Object)
            If (Me.getRHXXOperationCompleted Is Nothing) Then
                Me.getRHXXOperationCompleted = AddressOf Me.OngetRHXXOperationCompleted
            End If
            Me.InvokeAsync("getRHXX", New Object() {code}, Me.getRHXXOperationCompleted, userState)
        End Sub
        
        Private Sub OngetRHXXOperationCompleted(ByVal arg As Object)
            If (Not (Me.getRHXXCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent getRHXXCompleted(Me, New getRHXXCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/getZyData", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function getZyData(ByVal sfzh As String) As System.Data.DataTable
            Dim results() As Object = Me.Invoke("getZyData", New Object() {sfzh})
            Return CType(results(0),System.Data.DataTable)
        End Function
        
        '''<remarks/>
        Public Overloads Sub getZyDataAsync(ByVal sfzh As String)
            Me.getZyDataAsync(sfzh, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub getZyDataAsync(ByVal sfzh As String, ByVal userState As Object)
            If (Me.getZyDataOperationCompleted Is Nothing) Then
                Me.getZyDataOperationCompleted = AddressOf Me.OngetZyDataOperationCompleted
            End If
            Me.InvokeAsync("getZyData", New Object() {sfzh}, Me.getZyDataOperationCompleted, userState)
        End Sub
        
        Private Sub OngetZyDataOperationCompleted(ByVal arg As Object)
            If (Not (Me.getZyDataCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent getZyDataCompleted(Me, New getZyDataCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/getZyDataXml", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function getZyDataXml(ByVal sfzh As String) As String
            Dim results() As Object = Me.Invoke("getZyDataXml", New Object() {sfzh})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub getZyDataXmlAsync(ByVal sfzh As String)
            Me.getZyDataXmlAsync(sfzh, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub getZyDataXmlAsync(ByVal sfzh As String, ByVal userState As Object)
            If (Me.getZyDataXmlOperationCompleted Is Nothing) Then
                Me.getZyDataXmlOperationCompleted = AddressOf Me.OngetZyDataXmlOperationCompleted
            End If
            Me.InvokeAsync("getZyDataXml", New Object() {sfzh}, Me.getZyDataXmlOperationCompleted, userState)
        End Sub
        
        Private Sub OngetZyDataXmlOperationCompleted(ByVal arg As Object)
            If (Not (Me.getZyDataXmlCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent getZyDataXmlCompleted(Me, New getZyDataXmlCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/getZymx", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function getZymx(ByVal Post_Code As String) As System.Data.DataTable
            Dim results() As Object = Me.Invoke("getZymx", New Object() {Post_Code})
            Return CType(results(0),System.Data.DataTable)
        End Function
        
        '''<remarks/>
        Public Overloads Sub getZymxAsync(ByVal Post_Code As String)
            Me.getZymxAsync(Post_Code, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub getZymxAsync(ByVal Post_Code As String, ByVal userState As Object)
            If (Me.getZymxOperationCompleted Is Nothing) Then
                Me.getZymxOperationCompleted = AddressOf Me.OngetZymxOperationCompleted
            End If
            Me.InvokeAsync("getZymx", New Object() {Post_Code}, Me.getZymxOperationCompleted, userState)
        End Sub
        
        Private Sub OngetZymxOperationCompleted(ByVal arg As Object)
            If (Not (Me.getZymxCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent getZymxCompleted(Me, New getZymxCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/getZymxXml", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function getZymxXml(ByVal Post_Code As String) As String
            Dim results() As Object = Me.Invoke("getZymxXml", New Object() {Post_Code})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub getZymxXmlAsync(ByVal Post_Code As String)
            Me.getZymxXmlAsync(Post_Code, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub getZymxXmlAsync(ByVal Post_Code As String, ByVal userState As Object)
            If (Me.getZymxXmlOperationCompleted Is Nothing) Then
                Me.getZymxXmlOperationCompleted = AddressOf Me.OngetZymxXmlOperationCompleted
            End If
            Me.InvokeAsync("getZymxXml", New Object() {Post_Code}, Me.getZymxXmlOperationCompleted, userState)
        End Sub
        
        Private Sub OngetZymxXmlOperationCompleted(ByVal arg As Object)
            If (Not (Me.getZymxXmlCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent getZymxXmlCompleted(Me, New getZymxXmlCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/getMzData", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function getMzData(ByVal sfzh As String) As System.Data.DataTable
            Dim results() As Object = Me.Invoke("getMzData", New Object() {sfzh})
            Return CType(results(0),System.Data.DataTable)
        End Function
        
        '''<remarks/>
        Public Overloads Sub getMzDataAsync(ByVal sfzh As String)
            Me.getMzDataAsync(sfzh, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub getMzDataAsync(ByVal sfzh As String, ByVal userState As Object)
            If (Me.getMzDataOperationCompleted Is Nothing) Then
                Me.getMzDataOperationCompleted = AddressOf Me.OngetMzDataOperationCompleted
            End If
            Me.InvokeAsync("getMzData", New Object() {sfzh}, Me.getMzDataOperationCompleted, userState)
        End Sub
        
        Private Sub OngetMzDataOperationCompleted(ByVal arg As Object)
            If (Not (Me.getMzDataCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent getMzDataCompleted(Me, New getMzDataCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/getMzDataXml", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function getMzDataXml(ByVal sfzh As String) As String
            Dim results() As Object = Me.Invoke("getMzDataXml", New Object() {sfzh})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub getMzDataXmlAsync(ByVal sfzh As String)
            Me.getMzDataXmlAsync(sfzh, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub getMzDataXmlAsync(ByVal sfzh As String, ByVal userState As Object)
            If (Me.getMzDataXmlOperationCompleted Is Nothing) Then
                Me.getMzDataXmlOperationCompleted = AddressOf Me.OngetMzDataXmlOperationCompleted
            End If
            Me.InvokeAsync("getMzDataXml", New Object() {sfzh}, Me.getMzDataXmlOperationCompleted, userState)
        End Sub
        
        Private Sub OngetMzDataXmlOperationCompleted(ByVal arg As Object)
            If (Not (Me.getMzDataXmlCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent getMzDataXmlCompleted(Me, New getMzDataXmlCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/getMzmx", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function getMzmx(ByVal Post_Code As String) As System.Data.DataTable
            Dim results() As Object = Me.Invoke("getMzmx", New Object() {Post_Code})
            Return CType(results(0),System.Data.DataTable)
        End Function
        
        '''<remarks/>
        Public Overloads Sub getMzmxAsync(ByVal Post_Code As String)
            Me.getMzmxAsync(Post_Code, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub getMzmxAsync(ByVal Post_Code As String, ByVal userState As Object)
            If (Me.getMzmxOperationCompleted Is Nothing) Then
                Me.getMzmxOperationCompleted = AddressOf Me.OngetMzmxOperationCompleted
            End If
            Me.InvokeAsync("getMzmx", New Object() {Post_Code}, Me.getMzmxOperationCompleted, userState)
        End Sub
        
        Private Sub OngetMzmxOperationCompleted(ByVal arg As Object)
            If (Not (Me.getMzmxCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent getMzmxCompleted(Me, New getMzmxCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/getMzmxXml", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function getMzmxXml(ByVal Post_Code As String) As String
            Dim results() As Object = Me.Invoke("getMzmxXml", New Object() {Post_Code})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub getMzmxXmlAsync(ByVal Post_Code As String)
            Me.getMzmxXmlAsync(Post_Code, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub getMzmxXmlAsync(ByVal Post_Code As String, ByVal userState As Object)
            If (Me.getMzmxXmlOperationCompleted Is Nothing) Then
                Me.getMzmxXmlOperationCompleted = AddressOf Me.OngetMzmxXmlOperationCompleted
            End If
            Me.InvokeAsync("getMzmxXml", New Object() {Post_Code}, Me.getMzmxXmlOperationCompleted, userState)
        End Sub
        
        Private Sub OngetMzmxXmlOperationCompleted(ByVal arg As Object)
            If (Not (Me.getMzmxXmlCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent getMzmxXmlCompleted(Me, New getMzmxXmlCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/getMzLsJl_Date", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function getMzLsJl_Date(ByVal cfys As String, ByVal date1 As Date, ByVal date2 As Date) As System.Data.DataTable
            Dim results() As Object = Me.Invoke("getMzLsJl_Date", New Object() {cfys, date1, date2})
            Return CType(results(0),System.Data.DataTable)
        End Function
        
        '''<remarks/>
        Public Overloads Sub getMzLsJl_DateAsync(ByVal cfys As String, ByVal date1 As Date, ByVal date2 As Date)
            Me.getMzLsJl_DateAsync(cfys, date1, date2, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub getMzLsJl_DateAsync(ByVal cfys As String, ByVal date1 As Date, ByVal date2 As Date, ByVal userState As Object)
            If (Me.getMzLsJl_DateOperationCompleted Is Nothing) Then
                Me.getMzLsJl_DateOperationCompleted = AddressOf Me.OngetMzLsJl_DateOperationCompleted
            End If
            Me.InvokeAsync("getMzLsJl_Date", New Object() {cfys, date1, date2}, Me.getMzLsJl_DateOperationCompleted, userState)
        End Sub
        
        Private Sub OngetMzLsJl_DateOperationCompleted(ByVal arg As Object)
            If (Not (Me.getMzLsJl_DateCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent getMzLsJl_DateCompleted(Me, New getMzLsJl_DateCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/CheckATR", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function CheckATR(ByVal sfzh As String, ByVal JKKBankNo As String, ByVal Atr As String) As String
            Dim results() As Object = Me.Invoke("CheckATR", New Object() {sfzh, JKKBankNo, Atr})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub CheckATRAsync(ByVal sfzh As String, ByVal JKKBankNo As String, ByVal Atr As String)
            Me.CheckATRAsync(sfzh, JKKBankNo, Atr, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub CheckATRAsync(ByVal sfzh As String, ByVal JKKBankNo As String, ByVal Atr As String, ByVal userState As Object)
            If (Me.CheckATROperationCompleted Is Nothing) Then
                Me.CheckATROperationCompleted = AddressOf Me.OnCheckATROperationCompleted
            End If
            Me.InvokeAsync("CheckATR", New Object() {sfzh, JKKBankNo, Atr}, Me.CheckATROperationCompleted, userState)
        End Sub
        
        Private Sub OnCheckATROperationCompleted(ByVal arg As Object)
            If (Not (Me.CheckATRCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent CheckATRCompleted(Me, New CheckATRCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/getAccount", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function getAccount(ByVal sfzh As String, ByVal JKKBankNo As String) As String
            Dim results() As Object = Me.Invoke("getAccount", New Object() {sfzh, JKKBankNo})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub getAccountAsync(ByVal sfzh As String, ByVal JKKBankNo As String)
            Me.getAccountAsync(sfzh, JKKBankNo, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub getAccountAsync(ByVal sfzh As String, ByVal JKKBankNo As String, ByVal userState As Object)
            If (Me.getAccountOperationCompleted Is Nothing) Then
                Me.getAccountOperationCompleted = AddressOf Me.OngetAccountOperationCompleted
            End If
            Me.InvokeAsync("getAccount", New Object() {sfzh, JKKBankNo}, Me.getAccountOperationCompleted, userState)
        End Sub
        
        Private Sub OngetAccountOperationCompleted(ByVal arg As Object)
            If (Not (Me.getAccountCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent getAccountCompleted(Me, New getAccountCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/addConsumeJsr", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function addConsumeJsr(ByVal sfzh As String, ByVal JKKBankNo As String, ByVal money As Decimal, ByVal yycode As String, ByVal SAM_Terminal As String, ByVal DeviceCSN As String, ByVal YyJsr_Code As String, ByVal BankNoYxq As String) As String
            Dim results() As Object = Me.Invoke("addConsumeJsr", New Object() {sfzh, JKKBankNo, money, yycode, SAM_Terminal, DeviceCSN, YyJsr_Code, BankNoYxq})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub addConsumeJsrAsync(ByVal sfzh As String, ByVal JKKBankNo As String, ByVal money As Decimal, ByVal yycode As String, ByVal SAM_Terminal As String, ByVal DeviceCSN As String, ByVal YyJsr_Code As String, ByVal BankNoYxq As String)
            Me.addConsumeJsrAsync(sfzh, JKKBankNo, money, yycode, SAM_Terminal, DeviceCSN, YyJsr_Code, BankNoYxq, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub addConsumeJsrAsync(ByVal sfzh As String, ByVal JKKBankNo As String, ByVal money As Decimal, ByVal yycode As String, ByVal SAM_Terminal As String, ByVal DeviceCSN As String, ByVal YyJsr_Code As String, ByVal BankNoYxq As String, ByVal userState As Object)
            If (Me.addConsumeJsrOperationCompleted Is Nothing) Then
                Me.addConsumeJsrOperationCompleted = AddressOf Me.OnaddConsumeJsrOperationCompleted
            End If
            Me.InvokeAsync("addConsumeJsr", New Object() {sfzh, JKKBankNo, money, yycode, SAM_Terminal, DeviceCSN, YyJsr_Code, BankNoYxq}, Me.addConsumeJsrOperationCompleted, userState)
        End Sub
        
        Private Sub OnaddConsumeJsrOperationCompleted(ByVal arg As Object)
            If (Not (Me.addConsumeJsrCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent addConsumeJsrCompleted(Me, New addConsumeJsrCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/CancelConsumeJsr", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function CancelConsumeJsr(ByVal sfzh As String, ByVal JKKBankNo As String, ByVal id As Integer, ByVal yycode As String, ByVal SAM_Terminal As String, ByVal DeviceCSN As String, ByVal YyJsr_Code As String) As String
            Dim results() As Object = Me.Invoke("CancelConsumeJsr", New Object() {sfzh, JKKBankNo, id, yycode, SAM_Terminal, DeviceCSN, YyJsr_Code})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub CancelConsumeJsrAsync(ByVal sfzh As String, ByVal JKKBankNo As String, ByVal id As Integer, ByVal yycode As String, ByVal SAM_Terminal As String, ByVal DeviceCSN As String, ByVal YyJsr_Code As String)
            Me.CancelConsumeJsrAsync(sfzh, JKKBankNo, id, yycode, SAM_Terminal, DeviceCSN, YyJsr_Code, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub CancelConsumeJsrAsync(ByVal sfzh As String, ByVal JKKBankNo As String, ByVal id As Integer, ByVal yycode As String, ByVal SAM_Terminal As String, ByVal DeviceCSN As String, ByVal YyJsr_Code As String, ByVal userState As Object)
            If (Me.CancelConsumeJsrOperationCompleted Is Nothing) Then
                Me.CancelConsumeJsrOperationCompleted = AddressOf Me.OnCancelConsumeJsrOperationCompleted
            End If
            Me.InvokeAsync("CancelConsumeJsr", New Object() {sfzh, JKKBankNo, id, yycode, SAM_Terminal, DeviceCSN, YyJsr_Code}, Me.CancelConsumeJsrOperationCompleted, userState)
        End Sub
        
        Private Sub OnCancelConsumeJsrOperationCompleted(ByVal arg As Object)
            If (Not (Me.CancelConsumeJsrCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent CancelConsumeJsrCompleted(Me, New CancelConsumeJsrCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/addPOSRecharge", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function addPOSRecharge(ByVal sfzh As String, ByVal JKKBankNo As String, ByVal money As Decimal, ByVal yycode As String, ByVal bankno As String, ByVal SAM_Terminal As String, ByVal DeviceCSN As String, ByVal POSCode As String, ByVal YyJsr_Code As String) As String
            Dim results() As Object = Me.Invoke("addPOSRecharge", New Object() {sfzh, JKKBankNo, money, yycode, bankno, SAM_Terminal, DeviceCSN, POSCode, YyJsr_Code})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub addPOSRechargeAsync(ByVal sfzh As String, ByVal JKKBankNo As String, ByVal money As Decimal, ByVal yycode As String, ByVal bankno As String, ByVal SAM_Terminal As String, ByVal DeviceCSN As String, ByVal POSCode As String, ByVal YyJsr_Code As String)
            Me.addPOSRechargeAsync(sfzh, JKKBankNo, money, yycode, bankno, SAM_Terminal, DeviceCSN, POSCode, YyJsr_Code, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub addPOSRechargeAsync(ByVal sfzh As String, ByVal JKKBankNo As String, ByVal money As Decimal, ByVal yycode As String, ByVal bankno As String, ByVal SAM_Terminal As String, ByVal DeviceCSN As String, ByVal POSCode As String, ByVal YyJsr_Code As String, ByVal userState As Object)
            If (Me.addPOSRechargeOperationCompleted Is Nothing) Then
                Me.addPOSRechargeOperationCompleted = AddressOf Me.OnaddPOSRechargeOperationCompleted
            End If
            Me.InvokeAsync("addPOSRecharge", New Object() {sfzh, JKKBankNo, money, yycode, bankno, SAM_Terminal, DeviceCSN, POSCode, YyJsr_Code}, Me.addPOSRechargeOperationCompleted, userState)
        End Sub
        
        Private Sub OnaddPOSRechargeOperationCompleted(ByVal arg As Object)
            If (Not (Me.addPOSRechargeCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent addPOSRechargeCompleted(Me, New addPOSRechargeCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/addCashRecharge", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function addCashRecharge(ByVal sfzh As String, ByVal JKKBankNo As String, ByVal money As Decimal, ByVal yycode As String, ByVal SAM_Terminal As String, ByVal DeviceCSN As String, ByVal YyJsr_Code As String) As String
            Dim results() As Object = Me.Invoke("addCashRecharge", New Object() {sfzh, JKKBankNo, money, yycode, SAM_Terminal, DeviceCSN, YyJsr_Code})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub addCashRechargeAsync(ByVal sfzh As String, ByVal JKKBankNo As String, ByVal money As Decimal, ByVal yycode As String, ByVal SAM_Terminal As String, ByVal DeviceCSN As String, ByVal YyJsr_Code As String)
            Me.addCashRechargeAsync(sfzh, JKKBankNo, money, yycode, SAM_Terminal, DeviceCSN, YyJsr_Code, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub addCashRechargeAsync(ByVal sfzh As String, ByVal JKKBankNo As String, ByVal money As Decimal, ByVal yycode As String, ByVal SAM_Terminal As String, ByVal DeviceCSN As String, ByVal YyJsr_Code As String, ByVal userState As Object)
            If (Me.addCashRechargeOperationCompleted Is Nothing) Then
                Me.addCashRechargeOperationCompleted = AddressOf Me.OnaddCashRechargeOperationCompleted
            End If
            Me.InvokeAsync("addCashRecharge", New Object() {sfzh, JKKBankNo, money, yycode, SAM_Terminal, DeviceCSN, YyJsr_Code}, Me.addCashRechargeOperationCompleted, userState)
        End Sub
        
        Private Sub OnaddCashRechargeOperationCompleted(ByVal arg As Object)
            If (Not (Me.addCashRechargeCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent addCashRechargeCompleted(Me, New addCashRechargeCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/addAppRecharge", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function addAppRecharge(ByVal sfzh As String, ByVal JKKBankNo As String, ByVal money As Decimal, ByVal yycode As String, ByVal bankno As String, ByVal SAM_Terminal As String, ByVal DeviceCSN As String, ByVal POSCode As String, ByVal YyJsr_Code As String) As String
            Dim results() As Object = Me.Invoke("addAppRecharge", New Object() {sfzh, JKKBankNo, money, yycode, bankno, SAM_Terminal, DeviceCSN, POSCode, YyJsr_Code})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub addAppRechargeAsync(ByVal sfzh As String, ByVal JKKBankNo As String, ByVal money As Decimal, ByVal yycode As String, ByVal bankno As String, ByVal SAM_Terminal As String, ByVal DeviceCSN As String, ByVal POSCode As String, ByVal YyJsr_Code As String)
            Me.addAppRechargeAsync(sfzh, JKKBankNo, money, yycode, bankno, SAM_Terminal, DeviceCSN, POSCode, YyJsr_Code, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub addAppRechargeAsync(ByVal sfzh As String, ByVal JKKBankNo As String, ByVal money As Decimal, ByVal yycode As String, ByVal bankno As String, ByVal SAM_Terminal As String, ByVal DeviceCSN As String, ByVal POSCode As String, ByVal YyJsr_Code As String, ByVal userState As Object)
            If (Me.addAppRechargeOperationCompleted Is Nothing) Then
                Me.addAppRechargeOperationCompleted = AddressOf Me.OnaddAppRechargeOperationCompleted
            End If
            Me.InvokeAsync("addAppRecharge", New Object() {sfzh, JKKBankNo, money, yycode, bankno, SAM_Terminal, DeviceCSN, POSCode, YyJsr_Code}, Me.addAppRechargeOperationCompleted, userState)
        End Sub
        
        Private Sub OnaddAppRechargeOperationCompleted(ByVal arg As Object)
            If (Not (Me.addAppRechargeCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent addAppRechargeCompleted(Me, New addAppRechargeCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/addAlipayRecharge", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function addAlipayRecharge(ByVal sfzh As String, ByVal JKKBankNo As String, ByVal money As Decimal, ByVal yycode As String, ByVal bankno As String, ByVal SAM_Terminal As String, ByVal DeviceCSN As String, ByVal POSCode As String, ByVal YyJsr_Code As String) As String
            Dim results() As Object = Me.Invoke("addAlipayRecharge", New Object() {sfzh, JKKBankNo, money, yycode, bankno, SAM_Terminal, DeviceCSN, POSCode, YyJsr_Code})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub addAlipayRechargeAsync(ByVal sfzh As String, ByVal JKKBankNo As String, ByVal money As Decimal, ByVal yycode As String, ByVal bankno As String, ByVal SAM_Terminal As String, ByVal DeviceCSN As String, ByVal POSCode As String, ByVal YyJsr_Code As String)
            Me.addAlipayRechargeAsync(sfzh, JKKBankNo, money, yycode, bankno, SAM_Terminal, DeviceCSN, POSCode, YyJsr_Code, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub addAlipayRechargeAsync(ByVal sfzh As String, ByVal JKKBankNo As String, ByVal money As Decimal, ByVal yycode As String, ByVal bankno As String, ByVal SAM_Terminal As String, ByVal DeviceCSN As String, ByVal POSCode As String, ByVal YyJsr_Code As String, ByVal userState As Object)
            If (Me.addAlipayRechargeOperationCompleted Is Nothing) Then
                Me.addAlipayRechargeOperationCompleted = AddressOf Me.OnaddAlipayRechargeOperationCompleted
            End If
            Me.InvokeAsync("addAlipayRecharge", New Object() {sfzh, JKKBankNo, money, yycode, bankno, SAM_Terminal, DeviceCSN, POSCode, YyJsr_Code}, Me.addAlipayRechargeOperationCompleted, userState)
        End Sub
        
        Private Sub OnaddAlipayRechargeOperationCompleted(ByVal arg As Object)
            If (Not (Me.addAlipayRechargeCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent addAlipayRechargeCompleted(Me, New addAlipayRechargeCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/AccToCash", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function AccToCash(ByVal sfzh As String, ByVal JKKBankNo As String, ByVal money As Decimal, ByVal yycode As String, ByVal SAM_Terminal As String, ByVal DeviceCSN As String, ByVal YyJsr_Code As String, ByVal BankNoYxq As String) As String
            Dim results() As Object = Me.Invoke("AccToCash", New Object() {sfzh, JKKBankNo, money, yycode, SAM_Terminal, DeviceCSN, YyJsr_Code, BankNoYxq})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub AccToCashAsync(ByVal sfzh As String, ByVal JKKBankNo As String, ByVal money As Decimal, ByVal yycode As String, ByVal SAM_Terminal As String, ByVal DeviceCSN As String, ByVal YyJsr_Code As String, ByVal BankNoYxq As String)
            Me.AccToCashAsync(sfzh, JKKBankNo, money, yycode, SAM_Terminal, DeviceCSN, YyJsr_Code, BankNoYxq, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub AccToCashAsync(ByVal sfzh As String, ByVal JKKBankNo As String, ByVal money As Decimal, ByVal yycode As String, ByVal SAM_Terminal As String, ByVal DeviceCSN As String, ByVal YyJsr_Code As String, ByVal BankNoYxq As String, ByVal userState As Object)
            If (Me.AccToCashOperationCompleted Is Nothing) Then
                Me.AccToCashOperationCompleted = AddressOf Me.OnAccToCashOperationCompleted
            End If
            Me.InvokeAsync("AccToCash", New Object() {sfzh, JKKBankNo, money, yycode, SAM_Terminal, DeviceCSN, YyJsr_Code, BankNoYxq}, Me.AccToCashOperationCompleted, userState)
        End Sub
        
        Private Sub OnAccToCashOperationCompleted(ByVal arg As Object)
            If (Not (Me.AccToCashCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent AccToCashCompleted(Me, New AccToCashCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/AccToBank", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function AccToBank(ByVal sfzh As String, ByVal JKKBankNo As String, ByVal money As Decimal, ByVal yycode As String, ByVal SAM_Terminal As String, ByVal DeviceCSN As String, ByVal YyJsr_Code As String, ByVal BankNoYxq As String) As String
            Dim results() As Object = Me.Invoke("AccToBank", New Object() {sfzh, JKKBankNo, money, yycode, SAM_Terminal, DeviceCSN, YyJsr_Code, BankNoYxq})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub AccToBankAsync(ByVal sfzh As String, ByVal JKKBankNo As String, ByVal money As Decimal, ByVal yycode As String, ByVal SAM_Terminal As String, ByVal DeviceCSN As String, ByVal YyJsr_Code As String, ByVal BankNoYxq As String)
            Me.AccToBankAsync(sfzh, JKKBankNo, money, yycode, SAM_Terminal, DeviceCSN, YyJsr_Code, BankNoYxq, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub AccToBankAsync(ByVal sfzh As String, ByVal JKKBankNo As String, ByVal money As Decimal, ByVal yycode As String, ByVal SAM_Terminal As String, ByVal DeviceCSN As String, ByVal YyJsr_Code As String, ByVal BankNoYxq As String, ByVal userState As Object)
            If (Me.AccToBankOperationCompleted Is Nothing) Then
                Me.AccToBankOperationCompleted = AddressOf Me.OnAccToBankOperationCompleted
            End If
            Me.InvokeAsync("AccToBank", New Object() {sfzh, JKKBankNo, money, yycode, SAM_Terminal, DeviceCSN, YyJsr_Code, BankNoYxq}, Me.AccToBankOperationCompleted, userState)
        End Sub
        
        Private Sub OnAccToBankOperationCompleted(ByVal arg As Object)
            If (Not (Me.AccToBankCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent AccToBankCompleted(Me, New AccToBankCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/JKKCzJz", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function JKKCzJz(ByVal jz_date As Date, ByVal yycode As String, ByVal SAM_Terminal As String, ByVal DeviceCSN As String, ByVal YyJsr_Code As String) As String
            Dim results() As Object = Me.Invoke("JKKCzJz", New Object() {jz_date, yycode, SAM_Terminal, DeviceCSN, YyJsr_Code})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub JKKCzJzAsync(ByVal jz_date As Date, ByVal yycode As String, ByVal SAM_Terminal As String, ByVal DeviceCSN As String, ByVal YyJsr_Code As String)
            Me.JKKCzJzAsync(jz_date, yycode, SAM_Terminal, DeviceCSN, YyJsr_Code, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub JKKCzJzAsync(ByVal jz_date As Date, ByVal yycode As String, ByVal SAM_Terminal As String, ByVal DeviceCSN As String, ByVal YyJsr_Code As String, ByVal userState As Object)
            If (Me.JKKCzJzOperationCompleted Is Nothing) Then
                Me.JKKCzJzOperationCompleted = AddressOf Me.OnJKKCzJzOperationCompleted
            End If
            Me.InvokeAsync("JKKCzJz", New Object() {jz_date, yycode, SAM_Terminal, DeviceCSN, YyJsr_Code}, Me.JKKCzJzOperationCompleted, userState)
        End Sub
        
        Private Sub OnJKKCzJzOperationCompleted(ByVal arg As Object)
            If (Not (Me.JKKCzJzCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent JKKCzJzCompleted(Me, New JKKCzJzCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/JKKXFJz", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function JKKXFJz(ByVal jz_date As Date, ByVal yycode As String, ByVal SAM_Terminal As String, ByVal DeviceCSN As String, ByVal YyJsr_Code As String) As String
            Dim results() As Object = Me.Invoke("JKKXFJz", New Object() {jz_date, yycode, SAM_Terminal, DeviceCSN, YyJsr_Code})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub JKKXFJzAsync(ByVal jz_date As Date, ByVal yycode As String, ByVal SAM_Terminal As String, ByVal DeviceCSN As String, ByVal YyJsr_Code As String)
            Me.JKKXFJzAsync(jz_date, yycode, SAM_Terminal, DeviceCSN, YyJsr_Code, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub JKKXFJzAsync(ByVal jz_date As Date, ByVal yycode As String, ByVal SAM_Terminal As String, ByVal DeviceCSN As String, ByVal YyJsr_Code As String, ByVal userState As Object)
            If (Me.JKKXFJzOperationCompleted Is Nothing) Then
                Me.JKKXFJzOperationCompleted = AddressOf Me.OnJKKXFJzOperationCompleted
            End If
            Me.InvokeAsync("JKKXFJz", New Object() {jz_date, yycode, SAM_Terminal, DeviceCSN, YyJsr_Code}, Me.JKKXFJzOperationCompleted, userState)
        End Sub
        
        Private Sub OnJKKXFJzOperationCompleted(ByVal arg As Object)
            If (Not (Me.JKKXFJzCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent JKKXFJzCompleted(Me, New JKKXFJzCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/ConsumeList", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function ConsumeList(ByVal sfzh As String, ByVal yy_code As String, ByVal date1 As Date, ByVal date2 As Date) As String
            Dim results() As Object = Me.Invoke("ConsumeList", New Object() {sfzh, yy_code, date1, date2})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub ConsumeListAsync(ByVal sfzh As String, ByVal yy_code As String, ByVal date1 As Date, ByVal date2 As Date)
            Me.ConsumeListAsync(sfzh, yy_code, date1, date2, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub ConsumeListAsync(ByVal sfzh As String, ByVal yy_code As String, ByVal date1 As Date, ByVal date2 As Date, ByVal userState As Object)
            If (Me.ConsumeListOperationCompleted Is Nothing) Then
                Me.ConsumeListOperationCompleted = AddressOf Me.OnConsumeListOperationCompleted
            End If
            Me.InvokeAsync("ConsumeList", New Object() {sfzh, yy_code, date1, date2}, Me.ConsumeListOperationCompleted, userState)
        End Sub
        
        Private Sub OnConsumeListOperationCompleted(ByVal arg As Object)
            If (Not (Me.ConsumeListCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent ConsumeListCompleted(Me, New ConsumeListCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/getJzTime", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function getJzTime(ByVal yycode As String, ByVal YyJsr_Code As String) As String
            Dim results() As Object = Me.Invoke("getJzTime", New Object() {yycode, YyJsr_Code})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub getJzTimeAsync(ByVal yycode As String, ByVal YyJsr_Code As String)
            Me.getJzTimeAsync(yycode, YyJsr_Code, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub getJzTimeAsync(ByVal yycode As String, ByVal YyJsr_Code As String, ByVal userState As Object)
            If (Me.getJzTimeOperationCompleted Is Nothing) Then
                Me.getJzTimeOperationCompleted = AddressOf Me.OngetJzTimeOperationCompleted
            End If
            Me.InvokeAsync("getJzTime", New Object() {yycode, YyJsr_Code}, Me.getJzTimeOperationCompleted, userState)
        End Sub
        
        Private Sub OngetJzTimeOperationCompleted(ByVal arg As Object)
            If (Not (Me.getJzTimeCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent getJzTimeCompleted(Me, New getJzTimeCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/getJzDataNew", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function getJzDataNew(ByVal jz_date As Date, ByVal yycode As String, ByVal SAM_Terminal As String, ByVal DeviceCSN As String, ByVal YyJsr_Code As String) As String
            Dim results() As Object = Me.Invoke("getJzDataNew", New Object() {jz_date, yycode, SAM_Terminal, DeviceCSN, YyJsr_Code})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub getJzDataNewAsync(ByVal jz_date As Date, ByVal yycode As String, ByVal SAM_Terminal As String, ByVal DeviceCSN As String, ByVal YyJsr_Code As String)
            Me.getJzDataNewAsync(jz_date, yycode, SAM_Terminal, DeviceCSN, YyJsr_Code, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub getJzDataNewAsync(ByVal jz_date As Date, ByVal yycode As String, ByVal SAM_Terminal As String, ByVal DeviceCSN As String, ByVal YyJsr_Code As String, ByVal userState As Object)
            If (Me.getJzDataNewOperationCompleted Is Nothing) Then
                Me.getJzDataNewOperationCompleted = AddressOf Me.OngetJzDataNewOperationCompleted
            End If
            Me.InvokeAsync("getJzDataNew", New Object() {jz_date, yycode, SAM_Terminal, DeviceCSN, YyJsr_Code}, Me.getJzDataNewOperationCompleted, userState)
        End Sub
        
        Private Sub OngetJzDataNewOperationCompleted(ByVal arg As Object)
            If (Not (Me.getJzDataNewCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent getJzDataNewCompleted(Me, New getJzDataNewCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/getJzData", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function getJzData(ByVal jz_date As Date, ByVal yycode As String, ByVal YyJsr_Code As String) As String
            Dim results() As Object = Me.Invoke("getJzData", New Object() {jz_date, yycode, YyJsr_Code})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub getJzDataAsync(ByVal jz_date As Date, ByVal yycode As String, ByVal YyJsr_Code As String)
            Me.getJzDataAsync(jz_date, yycode, YyJsr_Code, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub getJzDataAsync(ByVal jz_date As Date, ByVal yycode As String, ByVal YyJsr_Code As String, ByVal userState As Object)
            If (Me.getJzDataOperationCompleted Is Nothing) Then
                Me.getJzDataOperationCompleted = AddressOf Me.OngetJzDataOperationCompleted
            End If
            Me.InvokeAsync("getJzData", New Object() {jz_date, yycode, YyJsr_Code}, Me.getJzDataOperationCompleted, userState)
        End Sub
        
        Private Sub OngetJzDataOperationCompleted(ByVal arg As Object)
            If (Not (Me.getJzDataCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent getJzDataCompleted(Me, New getJzDataCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/CxJzData", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function CxJzData(ByVal jz_date1 As Date, ByVal jz_date2 As Date, ByVal yycode As String, ByVal YyJsr_Code As String) As String
            Dim results() As Object = Me.Invoke("CxJzData", New Object() {jz_date1, jz_date2, yycode, YyJsr_Code})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub CxJzDataAsync(ByVal jz_date1 As Date, ByVal jz_date2 As Date, ByVal yycode As String, ByVal YyJsr_Code As String)
            Me.CxJzDataAsync(jz_date1, jz_date2, yycode, YyJsr_Code, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub CxJzDataAsync(ByVal jz_date1 As Date, ByVal jz_date2 As Date, ByVal yycode As String, ByVal YyJsr_Code As String, ByVal userState As Object)
            If (Me.CxJzDataOperationCompleted Is Nothing) Then
                Me.CxJzDataOperationCompleted = AddressOf Me.OnCxJzDataOperationCompleted
            End If
            Me.InvokeAsync("CxJzData", New Object() {jz_date1, jz_date2, yycode, YyJsr_Code}, Me.CxJzDataOperationCompleted, userState)
        End Sub
        
        Private Sub OnCxJzDataOperationCompleted(ByVal arg As Object)
            If (Not (Me.CxJzDataCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent CxJzDataCompleted(Me, New CxJzDataCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/CxMxData_Date", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function CxMxData_Date(ByVal jz_date1 As Date, ByVal jz_date2 As Date, ByVal yycode As String, ByVal YyJsr_Code As String) As String
            Dim results() As Object = Me.Invoke("CxMxData_Date", New Object() {jz_date1, jz_date2, yycode, YyJsr_Code})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub CxMxData_DateAsync(ByVal jz_date1 As Date, ByVal jz_date2 As Date, ByVal yycode As String, ByVal YyJsr_Code As String)
            Me.CxMxData_DateAsync(jz_date1, jz_date2, yycode, YyJsr_Code, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub CxMxData_DateAsync(ByVal jz_date1 As Date, ByVal jz_date2 As Date, ByVal yycode As String, ByVal YyJsr_Code As String, ByVal userState As Object)
            If (Me.CxMxData_DateOperationCompleted Is Nothing) Then
                Me.CxMxData_DateOperationCompleted = AddressOf Me.OnCxMxData_DateOperationCompleted
            End If
            Me.InvokeAsync("CxMxData_Date", New Object() {jz_date1, jz_date2, yycode, YyJsr_Code}, Me.CxMxData_DateOperationCompleted, userState)
        End Sub
        
        Private Sub OnCxMxData_DateOperationCompleted(ByVal arg As Object)
            If (Not (Me.CxMxData_DateCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent CxMxData_DateCompleted(Me, New CxMxData_DateCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/CxMxData_JzCode", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function CxMxData_JzCode(ByVal Jz_Code As String, ByVal yycode As String) As String
            Dim results() As Object = Me.Invoke("CxMxData_JzCode", New Object() {Jz_Code, yycode})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub CxMxData_JzCodeAsync(ByVal Jz_Code As String, ByVal yycode As String)
            Me.CxMxData_JzCodeAsync(Jz_Code, yycode, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub CxMxData_JzCodeAsync(ByVal Jz_Code As String, ByVal yycode As String, ByVal userState As Object)
            If (Me.CxMxData_JzCodeOperationCompleted Is Nothing) Then
                Me.CxMxData_JzCodeOperationCompleted = AddressOf Me.OnCxMxData_JzCodeOperationCompleted
            End If
            Me.InvokeAsync("CxMxData_JzCode", New Object() {Jz_Code, yycode}, Me.CxMxData_JzCodeOperationCompleted, userState)
        End Sub
        
        Private Sub OnCxMxData_JzCodeOperationCompleted(ByVal arg As Object)
            If (Not (Me.CxMxData_JzCodeCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent CxMxData_JzCodeCompleted(Me, New CxMxData_JzCodeCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/addBk", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function addBk(ByVal sfzh As String, ByVal JKKBankNo As String, ByVal money As Decimal, ByVal yycode As String, ByVal YyJsr_Code As String, ByVal NhLb As String, ByVal NhCode As String) As String
            Dim results() As Object = Me.Invoke("addBk", New Object() {sfzh, JKKBankNo, money, yycode, YyJsr_Code, NhLb, NhCode})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub addBkAsync(ByVal sfzh As String, ByVal JKKBankNo As String, ByVal money As Decimal, ByVal yycode As String, ByVal YyJsr_Code As String, ByVal NhLb As String, ByVal NhCode As String)
            Me.addBkAsync(sfzh, JKKBankNo, money, yycode, YyJsr_Code, NhLb, NhCode, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub addBkAsync(ByVal sfzh As String, ByVal JKKBankNo As String, ByVal money As Decimal, ByVal yycode As String, ByVal YyJsr_Code As String, ByVal NhLb As String, ByVal NhCode As String, ByVal userState As Object)
            If (Me.addBkOperationCompleted Is Nothing) Then
                Me.addBkOperationCompleted = AddressOf Me.OnaddBkOperationCompleted
            End If
            Me.InvokeAsync("addBk", New Object() {sfzh, JKKBankNo, money, yycode, YyJsr_Code, NhLb, NhCode}, Me.addBkOperationCompleted, userState)
        End Sub
        
        Private Sub OnaddBkOperationCompleted(ByVal arg As Object)
            If (Not (Me.addBkCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent addBkCompleted(Me, New addBkCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/RemoveBk", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function RemoveBk(ByVal sfzh As String, ByVal JKKBankNo As String, ByVal id As Integer, ByVal yycode As String, ByVal YyJsr_Code As String) As String
            Dim results() As Object = Me.Invoke("RemoveBk", New Object() {sfzh, JKKBankNo, id, yycode, YyJsr_Code})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub RemoveBkAsync(ByVal sfzh As String, ByVal JKKBankNo As String, ByVal id As Integer, ByVal yycode As String, ByVal YyJsr_Code As String)
            Me.RemoveBkAsync(sfzh, JKKBankNo, id, yycode, YyJsr_Code, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub RemoveBkAsync(ByVal sfzh As String, ByVal JKKBankNo As String, ByVal id As Integer, ByVal yycode As String, ByVal YyJsr_Code As String, ByVal userState As Object)
            If (Me.RemoveBkOperationCompleted Is Nothing) Then
                Me.RemoveBkOperationCompleted = AddressOf Me.OnRemoveBkOperationCompleted
            End If
            Me.InvokeAsync("RemoveBk", New Object() {sfzh, JKKBankNo, id, yycode, YyJsr_Code}, Me.RemoveBkOperationCompleted, userState)
        End Sub
        
        Private Sub OnRemoveBkOperationCompleted(ByVal arg As Object)
            If (Not (Me.RemoveBkCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent RemoveBkCompleted(Me, New RemoveBkCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/CheckBkState", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function CheckBkState(ByVal id As Integer) As String
            Dim results() As Object = Me.Invoke("CheckBkState", New Object() {id})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub CheckBkStateAsync(ByVal id As Integer)
            Me.CheckBkStateAsync(id, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub CheckBkStateAsync(ByVal id As Integer, ByVal userState As Object)
            If (Me.CheckBkStateOperationCompleted Is Nothing) Then
                Me.CheckBkStateOperationCompleted = AddressOf Me.OnCheckBkStateOperationCompleted
            End If
            Me.InvokeAsync("CheckBkState", New Object() {id}, Me.CheckBkStateOperationCompleted, userState)
        End Sub
        
        Private Sub OnCheckBkStateOperationCompleted(ByVal arg As Object)
            If (Not (Me.CheckBkStateCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent CheckBkStateCompleted(Me, New CheckBkStateCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/TempGetAccount", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function TempGetAccount(ByVal sfzh As String, ByVal Ry_Name As String, ByVal Temp_Code As String) As String
            Dim results() As Object = Me.Invoke("TempGetAccount", New Object() {sfzh, Ry_Name, Temp_Code})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub TempGetAccountAsync(ByVal sfzh As String, ByVal Ry_Name As String, ByVal Temp_Code As String)
            Me.TempGetAccountAsync(sfzh, Ry_Name, Temp_Code, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub TempGetAccountAsync(ByVal sfzh As String, ByVal Ry_Name As String, ByVal Temp_Code As String, ByVal userState As Object)
            If (Me.TempGetAccountOperationCompleted Is Nothing) Then
                Me.TempGetAccountOperationCompleted = AddressOf Me.OnTempGetAccountOperationCompleted
            End If
            Me.InvokeAsync("TempGetAccount", New Object() {sfzh, Ry_Name, Temp_Code}, Me.TempGetAccountOperationCompleted, userState)
        End Sub
        
        Private Sub OnTempGetAccountOperationCompleted(ByVal arg As Object)
            If (Not (Me.TempGetAccountCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent TempGetAccountCompleted(Me, New TempGetAccountCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/getTempTellAdd", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function getTempTellAdd(ByVal sfzh As String, ByVal Temp_Code As String) As String
            Dim results() As Object = Me.Invoke("getTempTellAdd", New Object() {sfzh, Temp_Code})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub getTempTellAddAsync(ByVal sfzh As String, ByVal Temp_Code As String)
            Me.getTempTellAddAsync(sfzh, Temp_Code, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub getTempTellAddAsync(ByVal sfzh As String, ByVal Temp_Code As String, ByVal userState As Object)
            If (Me.getTempTellAddOperationCompleted Is Nothing) Then
                Me.getTempTellAddOperationCompleted = AddressOf Me.OngetTempTellAddOperationCompleted
            End If
            Me.InvokeAsync("getTempTellAdd", New Object() {sfzh, Temp_Code}, Me.getTempTellAddOperationCompleted, userState)
        End Sub
        
        Private Sub OngetTempTellAddOperationCompleted(ByVal arg As Object)
            If (Not (Me.getTempTellAddCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent getTempTellAddCompleted(Me, New getTempTellAddCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/TempNewCard", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function TempNewCard(ByVal Ry_Name As String, ByVal Ry_Sex As String, ByVal Ry_Birth As String, ByVal sfzh As String, ByVal Ry_Tell As String, ByVal Ry_Add As String, ByVal yycode As String, ByVal YyJsr_Code As String, ByVal SAM_Terminal As String, ByVal DeviceCSN As String, ByVal Temp_Code As String, ByVal Original_Code As String) As String
            Dim results() As Object = Me.Invoke("TempNewCard", New Object() {Ry_Name, Ry_Sex, Ry_Birth, sfzh, Ry_Tell, Ry_Add, yycode, YyJsr_Code, SAM_Terminal, DeviceCSN, Temp_Code, Original_Code})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub TempNewCardAsync(ByVal Ry_Name As String, ByVal Ry_Sex As String, ByVal Ry_Birth As String, ByVal sfzh As String, ByVal Ry_Tell As String, ByVal Ry_Add As String, ByVal yycode As String, ByVal YyJsr_Code As String, ByVal SAM_Terminal As String, ByVal DeviceCSN As String, ByVal Temp_Code As String, ByVal Original_Code As String)
            Me.TempNewCardAsync(Ry_Name, Ry_Sex, Ry_Birth, sfzh, Ry_Tell, Ry_Add, yycode, YyJsr_Code, SAM_Terminal, DeviceCSN, Temp_Code, Original_Code, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub TempNewCardAsync(ByVal Ry_Name As String, ByVal Ry_Sex As String, ByVal Ry_Birth As String, ByVal sfzh As String, ByVal Ry_Tell As String, ByVal Ry_Add As String, ByVal yycode As String, ByVal YyJsr_Code As String, ByVal SAM_Terminal As String, ByVal DeviceCSN As String, ByVal Temp_Code As String, ByVal Original_Code As String, ByVal userState As Object)
            If (Me.TempNewCardOperationCompleted Is Nothing) Then
                Me.TempNewCardOperationCompleted = AddressOf Me.OnTempNewCardOperationCompleted
            End If
            Me.InvokeAsync("TempNewCard", New Object() {Ry_Name, Ry_Sex, Ry_Birth, sfzh, Ry_Tell, Ry_Add, yycode, YyJsr_Code, SAM_Terminal, DeviceCSN, Temp_Code, Original_Code}, Me.TempNewCardOperationCompleted, userState)
        End Sub
        
        Private Sub OnTempNewCardOperationCompleted(ByVal arg As Object)
            If (Not (Me.TempNewCardCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent TempNewCardCompleted(Me, New TempNewCardCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/TempAddPOSRecharge", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function TempAddPOSRecharge(ByVal sfzh As String, ByVal Ry_Name As String, ByVal Temp_Code As String, ByVal money As Decimal, ByVal yycode As String, ByVal bankno As String, ByVal SAM_Terminal As String, ByVal DeviceCSN As String, ByVal POSCode As String, ByVal YyJsr_Code As String) As String
            Dim results() As Object = Me.Invoke("TempAddPOSRecharge", New Object() {sfzh, Ry_Name, Temp_Code, money, yycode, bankno, SAM_Terminal, DeviceCSN, POSCode, YyJsr_Code})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub TempAddPOSRechargeAsync(ByVal sfzh As String, ByVal Ry_Name As String, ByVal Temp_Code As String, ByVal money As Decimal, ByVal yycode As String, ByVal bankno As String, ByVal SAM_Terminal As String, ByVal DeviceCSN As String, ByVal POSCode As String, ByVal YyJsr_Code As String)
            Me.TempAddPOSRechargeAsync(sfzh, Ry_Name, Temp_Code, money, yycode, bankno, SAM_Terminal, DeviceCSN, POSCode, YyJsr_Code, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub TempAddPOSRechargeAsync(ByVal sfzh As String, ByVal Ry_Name As String, ByVal Temp_Code As String, ByVal money As Decimal, ByVal yycode As String, ByVal bankno As String, ByVal SAM_Terminal As String, ByVal DeviceCSN As String, ByVal POSCode As String, ByVal YyJsr_Code As String, ByVal userState As Object)
            If (Me.TempAddPOSRechargeOperationCompleted Is Nothing) Then
                Me.TempAddPOSRechargeOperationCompleted = AddressOf Me.OnTempAddPOSRechargeOperationCompleted
            End If
            Me.InvokeAsync("TempAddPOSRecharge", New Object() {sfzh, Ry_Name, Temp_Code, money, yycode, bankno, SAM_Terminal, DeviceCSN, POSCode, YyJsr_Code}, Me.TempAddPOSRechargeOperationCompleted, userState)
        End Sub
        
        Private Sub OnTempAddPOSRechargeOperationCompleted(ByVal arg As Object)
            If (Not (Me.TempAddPOSRechargeCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent TempAddPOSRechargeCompleted(Me, New TempAddPOSRechargeCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/TempAddCashRecharge", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function TempAddCashRecharge(ByVal sfzh As String, ByVal Ry_Name As String, ByVal Temp_Code As String, ByVal money As Decimal, ByVal yycode As String, ByVal SAM_Terminal As String, ByVal DeviceCSN As String, ByVal YyJsr_Code As String) As String
            Dim results() As Object = Me.Invoke("TempAddCashRecharge", New Object() {sfzh, Ry_Name, Temp_Code, money, yycode, SAM_Terminal, DeviceCSN, YyJsr_Code})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub TempAddCashRechargeAsync(ByVal sfzh As String, ByVal Ry_Name As String, ByVal Temp_Code As String, ByVal money As Decimal, ByVal yycode As String, ByVal SAM_Terminal As String, ByVal DeviceCSN As String, ByVal YyJsr_Code As String)
            Me.TempAddCashRechargeAsync(sfzh, Ry_Name, Temp_Code, money, yycode, SAM_Terminal, DeviceCSN, YyJsr_Code, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub TempAddCashRechargeAsync(ByVal sfzh As String, ByVal Ry_Name As String, ByVal Temp_Code As String, ByVal money As Decimal, ByVal yycode As String, ByVal SAM_Terminal As String, ByVal DeviceCSN As String, ByVal YyJsr_Code As String, ByVal userState As Object)
            If (Me.TempAddCashRechargeOperationCompleted Is Nothing) Then
                Me.TempAddCashRechargeOperationCompleted = AddressOf Me.OnTempAddCashRechargeOperationCompleted
            End If
            Me.InvokeAsync("TempAddCashRecharge", New Object() {sfzh, Ry_Name, Temp_Code, money, yycode, SAM_Terminal, DeviceCSN, YyJsr_Code}, Me.TempAddCashRechargeOperationCompleted, userState)
        End Sub
        
        Private Sub OnTempAddCashRechargeOperationCompleted(ByVal arg As Object)
            If (Not (Me.TempAddCashRechargeCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent TempAddCashRechargeCompleted(Me, New TempAddCashRechargeCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/TempAddConsumeJsr", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function TempAddConsumeJsr(ByVal sfzh As String, ByVal Ry_Name As String, ByVal Temp_Code As String, ByVal money As Decimal, ByVal yycode As String, ByVal SAM_Terminal As String, ByVal DeviceCSN As String, ByVal YyJsr_Code As String) As String
            Dim results() As Object = Me.Invoke("TempAddConsumeJsr", New Object() {sfzh, Ry_Name, Temp_Code, money, yycode, SAM_Terminal, DeviceCSN, YyJsr_Code})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub TempAddConsumeJsrAsync(ByVal sfzh As String, ByVal Ry_Name As String, ByVal Temp_Code As String, ByVal money As Decimal, ByVal yycode As String, ByVal SAM_Terminal As String, ByVal DeviceCSN As String, ByVal YyJsr_Code As String)
            Me.TempAddConsumeJsrAsync(sfzh, Ry_Name, Temp_Code, money, yycode, SAM_Terminal, DeviceCSN, YyJsr_Code, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub TempAddConsumeJsrAsync(ByVal sfzh As String, ByVal Ry_Name As String, ByVal Temp_Code As String, ByVal money As Decimal, ByVal yycode As String, ByVal SAM_Terminal As String, ByVal DeviceCSN As String, ByVal YyJsr_Code As String, ByVal userState As Object)
            If (Me.TempAddConsumeJsrOperationCompleted Is Nothing) Then
                Me.TempAddConsumeJsrOperationCompleted = AddressOf Me.OnTempAddConsumeJsrOperationCompleted
            End If
            Me.InvokeAsync("TempAddConsumeJsr", New Object() {sfzh, Ry_Name, Temp_Code, money, yycode, SAM_Terminal, DeviceCSN, YyJsr_Code}, Me.TempAddConsumeJsrOperationCompleted, userState)
        End Sub
        
        Private Sub OnTempAddConsumeJsrOperationCompleted(ByVal arg As Object)
            If (Not (Me.TempAddConsumeJsrCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent TempAddConsumeJsrCompleted(Me, New TempAddConsumeJsrCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/TempCancelConsumeJsr", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function TempCancelConsumeJsr(ByVal sfzh As String, ByVal Ry_Name As String, ByVal Temp_Code As String, ByVal id As Integer, ByVal yycode As String, ByVal SAM_Terminal As String, ByVal DeviceCSN As String, ByVal YyJsr_Code As String) As String
            Dim results() As Object = Me.Invoke("TempCancelConsumeJsr", New Object() {sfzh, Ry_Name, Temp_Code, id, yycode, SAM_Terminal, DeviceCSN, YyJsr_Code})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub TempCancelConsumeJsrAsync(ByVal sfzh As String, ByVal Ry_Name As String, ByVal Temp_Code As String, ByVal id As Integer, ByVal yycode As String, ByVal SAM_Terminal As String, ByVal DeviceCSN As String, ByVal YyJsr_Code As String)
            Me.TempCancelConsumeJsrAsync(sfzh, Ry_Name, Temp_Code, id, yycode, SAM_Terminal, DeviceCSN, YyJsr_Code, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub TempCancelConsumeJsrAsync(ByVal sfzh As String, ByVal Ry_Name As String, ByVal Temp_Code As String, ByVal id As Integer, ByVal yycode As String, ByVal SAM_Terminal As String, ByVal DeviceCSN As String, ByVal YyJsr_Code As String, ByVal userState As Object)
            If (Me.TempCancelConsumeJsrOperationCompleted Is Nothing) Then
                Me.TempCancelConsumeJsrOperationCompleted = AddressOf Me.OnTempCancelConsumeJsrOperationCompleted
            End If
            Me.InvokeAsync("TempCancelConsumeJsr", New Object() {sfzh, Ry_Name, Temp_Code, id, yycode, SAM_Terminal, DeviceCSN, YyJsr_Code}, Me.TempCancelConsumeJsrOperationCompleted, userState)
        End Sub
        
        Private Sub OnTempCancelConsumeJsrOperationCompleted(ByVal arg As Object)
            If (Not (Me.TempCancelConsumeJsrCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent TempCancelConsumeJsrCompleted(Me, New TempCancelConsumeJsrCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/TempAccToCash", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function TempAccToCash(ByVal sfzh As String, ByVal Ry_Name As String, ByVal Temp_Code As String, ByVal Ry_Tell As String, ByVal money As Decimal, ByVal yycode As String, ByVal SAM_Terminal As String, ByVal DeviceCSN As String, ByVal YyJsr_Code As String) As String
            Dim results() As Object = Me.Invoke("TempAccToCash", New Object() {sfzh, Ry_Name, Temp_Code, Ry_Tell, money, yycode, SAM_Terminal, DeviceCSN, YyJsr_Code})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub TempAccToCashAsync(ByVal sfzh As String, ByVal Ry_Name As String, ByVal Temp_Code As String, ByVal Ry_Tell As String, ByVal money As Decimal, ByVal yycode As String, ByVal SAM_Terminal As String, ByVal DeviceCSN As String, ByVal YyJsr_Code As String)
            Me.TempAccToCashAsync(sfzh, Ry_Name, Temp_Code, Ry_Tell, money, yycode, SAM_Terminal, DeviceCSN, YyJsr_Code, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub TempAccToCashAsync(ByVal sfzh As String, ByVal Ry_Name As String, ByVal Temp_Code As String, ByVal Ry_Tell As String, ByVal money As Decimal, ByVal yycode As String, ByVal SAM_Terminal As String, ByVal DeviceCSN As String, ByVal YyJsr_Code As String, ByVal userState As Object)
            If (Me.TempAccToCashOperationCompleted Is Nothing) Then
                Me.TempAccToCashOperationCompleted = AddressOf Me.OnTempAccToCashOperationCompleted
            End If
            Me.InvokeAsync("TempAccToCash", New Object() {sfzh, Ry_Name, Temp_Code, Ry_Tell, money, yycode, SAM_Terminal, DeviceCSN, YyJsr_Code}, Me.TempAccToCashOperationCompleted, userState)
        End Sub
        
        Private Sub OnTempAccToCashOperationCompleted(ByVal arg As Object)
            If (Not (Me.TempAccToCashCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent TempAccToCashCompleted(Me, New TempAccToCashCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/TempConsumeList", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function TempConsumeList(ByVal Temp_Code As String, ByVal yycode As String, ByVal date1 As Date, ByVal date2 As Date) As String
            Dim results() As Object = Me.Invoke("TempConsumeList", New Object() {Temp_Code, yycode, date1, date2})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub TempConsumeListAsync(ByVal Temp_Code As String, ByVal yycode As String, ByVal date1 As Date, ByVal date2 As Date)
            Me.TempConsumeListAsync(Temp_Code, yycode, date1, date2, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub TempConsumeListAsync(ByVal Temp_Code As String, ByVal yycode As String, ByVal date1 As Date, ByVal date2 As Date, ByVal userState As Object)
            If (Me.TempConsumeListOperationCompleted Is Nothing) Then
                Me.TempConsumeListOperationCompleted = AddressOf Me.OnTempConsumeListOperationCompleted
            End If
            Me.InvokeAsync("TempConsumeList", New Object() {Temp_Code, yycode, date1, date2}, Me.TempConsumeListOperationCompleted, userState)
        End Sub
        
        Private Sub OnTempConsumeListOperationCompleted(ByVal arg As Object)
            If (Not (Me.TempConsumeListCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent TempConsumeListCompleted(Me, New TempConsumeListCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/TempCancelCard", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function TempCancelCard(ByVal sfzh As String, ByVal Ry_Name As String, ByVal Temp_Code As String, ByVal Ry_Tell As String, ByVal yycode As String, ByVal SAM_Terminal As String, ByVal DeviceCSN As String, ByVal YyJsr_Code As String) As String
            Dim results() As Object = Me.Invoke("TempCancelCard", New Object() {sfzh, Ry_Name, Temp_Code, Ry_Tell, yycode, SAM_Terminal, DeviceCSN, YyJsr_Code})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub TempCancelCardAsync(ByVal sfzh As String, ByVal Ry_Name As String, ByVal Temp_Code As String, ByVal Ry_Tell As String, ByVal yycode As String, ByVal SAM_Terminal As String, ByVal DeviceCSN As String, ByVal YyJsr_Code As String)
            Me.TempCancelCardAsync(sfzh, Ry_Name, Temp_Code, Ry_Tell, yycode, SAM_Terminal, DeviceCSN, YyJsr_Code, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub TempCancelCardAsync(ByVal sfzh As String, ByVal Ry_Name As String, ByVal Temp_Code As String, ByVal Ry_Tell As String, ByVal yycode As String, ByVal SAM_Terminal As String, ByVal DeviceCSN As String, ByVal YyJsr_Code As String, ByVal userState As Object)
            If (Me.TempCancelCardOperationCompleted Is Nothing) Then
                Me.TempCancelCardOperationCompleted = AddressOf Me.OnTempCancelCardOperationCompleted
            End If
            Me.InvokeAsync("TempCancelCard", New Object() {sfzh, Ry_Name, Temp_Code, Ry_Tell, yycode, SAM_Terminal, DeviceCSN, YyJsr_Code}, Me.TempCancelCardOperationCompleted, userState)
        End Sub
        
        Private Sub OnTempCancelCardOperationCompleted(ByVal arg As Object)
            If (Not (Me.TempCancelCardCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent TempCancelCardCompleted(Me, New TempCancelCardCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/TempCheckCancel", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function TempCheckCancel(ByVal Temp_Code As String) As String
            Dim results() As Object = Me.Invoke("TempCheckCancel", New Object() {Temp_Code})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub TempCheckCancelAsync(ByVal Temp_Code As String)
            Me.TempCheckCancelAsync(Temp_Code, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub TempCheckCancelAsync(ByVal Temp_Code As String, ByVal userState As Object)
            If (Me.TempCheckCancelOperationCompleted Is Nothing) Then
                Me.TempCheckCancelOperationCompleted = AddressOf Me.OnTempCheckCancelOperationCompleted
            End If
            Me.InvokeAsync("TempCheckCancel", New Object() {Temp_Code}, Me.TempCheckCancelOperationCompleted, userState)
        End Sub
        
        Private Sub OnTempCheckCancelOperationCompleted(ByVal arg As Object)
            If (Not (Me.TempCheckCancelCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent TempCheckCancelCompleted(Me, New TempCheckCancelCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/TempCheckState", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function TempCheckState(ByVal Temp_Code As String) As String
            Dim results() As Object = Me.Invoke("TempCheckState", New Object() {Temp_Code})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub TempCheckStateAsync(ByVal Temp_Code As String)
            Me.TempCheckStateAsync(Temp_Code, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub TempCheckStateAsync(ByVal Temp_Code As String, ByVal userState As Object)
            If (Me.TempCheckStateOperationCompleted Is Nothing) Then
                Me.TempCheckStateOperationCompleted = AddressOf Me.OnTempCheckStateOperationCompleted
            End If
            Me.InvokeAsync("TempCheckState", New Object() {Temp_Code}, Me.TempCheckStateOperationCompleted, userState)
        End Sub
        
        Private Sub OnTempCheckStateOperationCompleted(ByVal arg As Object)
            If (Not (Me.TempCheckStateCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent TempCheckStateCompleted(Me, New TempCheckStateCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/getRy_Base", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function getRy_Base(ByVal sfzh As String) As String
            Dim results() As Object = Me.Invoke("getRy_Base", New Object() {sfzh})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub getRy_BaseAsync(ByVal sfzh As String)
            Me.getRy_BaseAsync(sfzh, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub getRy_BaseAsync(ByVal sfzh As String, ByVal userState As Object)
            If (Me.getRy_BaseOperationCompleted Is Nothing) Then
                Me.getRy_BaseOperationCompleted = AddressOf Me.OngetRy_BaseOperationCompleted
            End If
            Me.InvokeAsync("getRy_Base", New Object() {sfzh}, Me.getRy_BaseOperationCompleted, userState)
        End Sub
        
        Private Sub OngetRy_BaseOperationCompleted(ByVal arg As Object)
            If (Not (Me.getRy_BaseCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent getRy_BaseCompleted(Me, New getRy_BaseCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/getRy_GuoMin", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function getRy_GuoMin(ByVal sfzh As String) As String
            Dim results() As Object = Me.Invoke("getRy_GuoMin", New Object() {sfzh})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub getRy_GuoMinAsync(ByVal sfzh As String)
            Me.getRy_GuoMinAsync(sfzh, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub getRy_GuoMinAsync(ByVal sfzh As String, ByVal userState As Object)
            If (Me.getRy_GuoMinOperationCompleted Is Nothing) Then
                Me.getRy_GuoMinOperationCompleted = AddressOf Me.OngetRy_GuoMinOperationCompleted
            End If
            Me.InvokeAsync("getRy_GuoMin", New Object() {sfzh}, Me.getRy_GuoMinOperationCompleted, userState)
        End Sub
        
        Private Sub OngetRy_GuoMinOperationCompleted(ByVal arg As Object)
            If (Not (Me.getRy_GuoMinCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent getRy_GuoMinCompleted(Me, New getRy_GuoMinCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/getRy_Health", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function getRy_Health(ByVal sfzh As String) As String
            Dim results() As Object = Me.Invoke("getRy_Health", New Object() {sfzh})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub getRy_HealthAsync(ByVal sfzh As String)
            Me.getRy_HealthAsync(sfzh, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub getRy_HealthAsync(ByVal sfzh As String, ByVal userState As Object)
            If (Me.getRy_HealthOperationCompleted Is Nothing) Then
                Me.getRy_HealthOperationCompleted = AddressOf Me.OngetRy_HealthOperationCompleted
            End If
            Me.InvokeAsync("getRy_Health", New Object() {sfzh}, Me.getRy_HealthOperationCompleted, userState)
        End Sub
        
        Private Sub OngetRy_HealthOperationCompleted(ByVal arg As Object)
            If (Not (Me.getRy_HealthCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent getRy_HealthCompleted(Me, New getRy_HealthCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/getRy_Info", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function getRy_Info(ByVal sfzh As String) As String
            Dim results() As Object = Me.Invoke("getRy_Info", New Object() {sfzh})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub getRy_InfoAsync(ByVal sfzh As String)
            Me.getRy_InfoAsync(sfzh, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub getRy_InfoAsync(ByVal sfzh As String, ByVal userState As Object)
            If (Me.getRy_InfoOperationCompleted Is Nothing) Then
                Me.getRy_InfoOperationCompleted = AddressOf Me.OngetRy_InfoOperationCompleted
            End If
            Me.InvokeAsync("getRy_Info", New Object() {sfzh}, Me.getRy_InfoOperationCompleted, userState)
        End Sub
        
        Private Sub OngetRy_InfoOperationCompleted(ByVal arg As Object)
            If (Not (Me.getRy_InfoCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent getRy_InfoCompleted(Me, New getRy_InfoCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/getRy_Lxfs", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function getRy_Lxfs(ByVal sfzh As String) As String
            Dim results() As Object = Me.Invoke("getRy_Lxfs", New Object() {sfzh})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub getRy_LxfsAsync(ByVal sfzh As String)
            Me.getRy_LxfsAsync(sfzh, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub getRy_LxfsAsync(ByVal sfzh As String, ByVal userState As Object)
            If (Me.getRy_LxfsOperationCompleted Is Nothing) Then
                Me.getRy_LxfsOperationCompleted = AddressOf Me.OngetRy_LxfsOperationCompleted
            End If
            Me.InvokeAsync("getRy_Lxfs", New Object() {sfzh}, Me.getRy_LxfsOperationCompleted, userState)
        End Sub
        
        Private Sub OngetRy_LxfsOperationCompleted(ByVal arg As Object)
            If (Not (Me.getRy_LxfsCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent getRy_LxfsCompleted(Me, New getRy_LxfsCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        <System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/getRy_MianYi", RequestNamespace:="http://tempuri.org/", ResponseNamespace:="http://tempuri.org/", Use:=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle:=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)>  _
        Public Function getRy_MianYi(ByVal sfzh As String) As String
            Dim results() As Object = Me.Invoke("getRy_MianYi", New Object() {sfzh})
            Return CType(results(0),String)
        End Function
        
        '''<remarks/>
        Public Overloads Sub getRy_MianYiAsync(ByVal sfzh As String)
            Me.getRy_MianYiAsync(sfzh, Nothing)
        End Sub
        
        '''<remarks/>
        Public Overloads Sub getRy_MianYiAsync(ByVal sfzh As String, ByVal userState As Object)
            If (Me.getRy_MianYiOperationCompleted Is Nothing) Then
                Me.getRy_MianYiOperationCompleted = AddressOf Me.OngetRy_MianYiOperationCompleted
            End If
            Me.InvokeAsync("getRy_MianYi", New Object() {sfzh}, Me.getRy_MianYiOperationCompleted, userState)
        End Sub
        
        Private Sub OngetRy_MianYiOperationCompleted(ByVal arg As Object)
            If (Not (Me.getRy_MianYiCompletedEvent) Is Nothing) Then
                Dim invokeArgs As System.Web.Services.Protocols.InvokeCompletedEventArgs = CType(arg,System.Web.Services.Protocols.InvokeCompletedEventArgs)
                RaiseEvent getRy_MianYiCompleted(Me, New getRy_MianYiCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState))
            End If
        End Sub
        
        '''<remarks/>
        Public Shadows Sub CancelAsync(ByVal userState As Object)
            MyBase.CancelAsync(userState)
        End Sub
        
        Private Function IsLocalFileSystemWebService(ByVal url As String) As Boolean
            If ((url Is Nothing)  _
                        OrElse (url Is String.Empty)) Then
                Return false
            End If
            Dim wsUri As System.Uri = New System.Uri(url)
            If ((wsUri.Port >= 1024)  _
                        AndAlso (String.Compare(wsUri.Host, "localHost", System.StringComparison.OrdinalIgnoreCase) = 0)) Then
                Return true
            End If
            Return false
        End Function
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub getRy_ZjCompletedEventHandler(ByVal sender As Object, ByVal e As getRy_ZjCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class getRy_ZjCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub getMzDataNewCompletedEventHandler(ByVal sender As Object, ByVal e As getMzDataNewCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class getMzDataNewCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub getYyJsrCompletedEventHandler(ByVal sender As Object, ByVal e As getYyJsrCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class getYyJsrCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As System.Data.DataTable
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),System.Data.DataTable)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub changePwdCompletedEventHandler(ByVal sender As Object, ByVal e As changePwdCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class changePwdCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub getCityCompletedEventHandler(ByVal sender As Object, ByVal e As getCityCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class getCityCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub getYyNameCompletedEventHandler(ByVal sender As Object, ByVal e As getYyNameCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class getYyNameCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub getSexCompletedEventHandler(ByVal sender As Object, ByVal e As getSexCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class getSexCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub getMZCompletedEventHandler(ByVal sender As Object, ByVal e As getMZCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class getMZCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub getWHCDCompletedEventHandler(ByVal sender As Object, ByVal e As getWHCDCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class getWHCDCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub getHYZKCompletedEventHandler(ByVal sender As Object, ByVal e As getHYZKCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class getHYZKCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub getZYCompletedEventHandler(ByVal sender As Object, ByVal e As getZYCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class getZYCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub getZJLBCompletedEventHandler(ByVal sender As Object, ByVal e As getZJLBCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class getZJLBCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub getABXXCompletedEventHandler(ByVal sender As Object, ByVal e As getABXXCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class getABXXCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub getRHXXCompletedEventHandler(ByVal sender As Object, ByVal e As getRHXXCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class getRHXXCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub getZyDataCompletedEventHandler(ByVal sender As Object, ByVal e As getZyDataCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class getZyDataCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As System.Data.DataTable
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),System.Data.DataTable)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub getZyDataXmlCompletedEventHandler(ByVal sender As Object, ByVal e As getZyDataXmlCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class getZyDataXmlCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub getZymxCompletedEventHandler(ByVal sender As Object, ByVal e As getZymxCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class getZymxCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As System.Data.DataTable
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),System.Data.DataTable)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub getZymxXmlCompletedEventHandler(ByVal sender As Object, ByVal e As getZymxXmlCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class getZymxXmlCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub getMzDataCompletedEventHandler(ByVal sender As Object, ByVal e As getMzDataCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class getMzDataCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As System.Data.DataTable
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),System.Data.DataTable)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub getMzDataXmlCompletedEventHandler(ByVal sender As Object, ByVal e As getMzDataXmlCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class getMzDataXmlCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub getMzmxCompletedEventHandler(ByVal sender As Object, ByVal e As getMzmxCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class getMzmxCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As System.Data.DataTable
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),System.Data.DataTable)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub getMzmxXmlCompletedEventHandler(ByVal sender As Object, ByVal e As getMzmxXmlCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class getMzmxXmlCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub getMzLsJl_DateCompletedEventHandler(ByVal sender As Object, ByVal e As getMzLsJl_DateCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class getMzLsJl_DateCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As System.Data.DataTable
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),System.Data.DataTable)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub CheckATRCompletedEventHandler(ByVal sender As Object, ByVal e As CheckATRCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class CheckATRCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub getAccountCompletedEventHandler(ByVal sender As Object, ByVal e As getAccountCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class getAccountCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub addConsumeJsrCompletedEventHandler(ByVal sender As Object, ByVal e As addConsumeJsrCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class addConsumeJsrCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub CancelConsumeJsrCompletedEventHandler(ByVal sender As Object, ByVal e As CancelConsumeJsrCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class CancelConsumeJsrCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub addPOSRechargeCompletedEventHandler(ByVal sender As Object, ByVal e As addPOSRechargeCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class addPOSRechargeCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub addCashRechargeCompletedEventHandler(ByVal sender As Object, ByVal e As addCashRechargeCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class addCashRechargeCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub addAppRechargeCompletedEventHandler(ByVal sender As Object, ByVal e As addAppRechargeCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class addAppRechargeCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub addAlipayRechargeCompletedEventHandler(ByVal sender As Object, ByVal e As addAlipayRechargeCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class addAlipayRechargeCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub AccToCashCompletedEventHandler(ByVal sender As Object, ByVal e As AccToCashCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class AccToCashCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub AccToBankCompletedEventHandler(ByVal sender As Object, ByVal e As AccToBankCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class AccToBankCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub JKKCzJzCompletedEventHandler(ByVal sender As Object, ByVal e As JKKCzJzCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class JKKCzJzCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub JKKXFJzCompletedEventHandler(ByVal sender As Object, ByVal e As JKKXFJzCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class JKKXFJzCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub ConsumeListCompletedEventHandler(ByVal sender As Object, ByVal e As ConsumeListCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class ConsumeListCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub getJzTimeCompletedEventHandler(ByVal sender As Object, ByVal e As getJzTimeCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class getJzTimeCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub getJzDataNewCompletedEventHandler(ByVal sender As Object, ByVal e As getJzDataNewCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class getJzDataNewCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub getJzDataCompletedEventHandler(ByVal sender As Object, ByVal e As getJzDataCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class getJzDataCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub CxJzDataCompletedEventHandler(ByVal sender As Object, ByVal e As CxJzDataCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class CxJzDataCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub CxMxData_DateCompletedEventHandler(ByVal sender As Object, ByVal e As CxMxData_DateCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class CxMxData_DateCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub CxMxData_JzCodeCompletedEventHandler(ByVal sender As Object, ByVal e As CxMxData_JzCodeCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class CxMxData_JzCodeCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub addBkCompletedEventHandler(ByVal sender As Object, ByVal e As addBkCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class addBkCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub RemoveBkCompletedEventHandler(ByVal sender As Object, ByVal e As RemoveBkCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class RemoveBkCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub CheckBkStateCompletedEventHandler(ByVal sender As Object, ByVal e As CheckBkStateCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class CheckBkStateCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub TempGetAccountCompletedEventHandler(ByVal sender As Object, ByVal e As TempGetAccountCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class TempGetAccountCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub getTempTellAddCompletedEventHandler(ByVal sender As Object, ByVal e As getTempTellAddCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class getTempTellAddCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub TempNewCardCompletedEventHandler(ByVal sender As Object, ByVal e As TempNewCardCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class TempNewCardCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub TempAddPOSRechargeCompletedEventHandler(ByVal sender As Object, ByVal e As TempAddPOSRechargeCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class TempAddPOSRechargeCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub TempAddCashRechargeCompletedEventHandler(ByVal sender As Object, ByVal e As TempAddCashRechargeCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class TempAddCashRechargeCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub TempAddConsumeJsrCompletedEventHandler(ByVal sender As Object, ByVal e As TempAddConsumeJsrCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class TempAddConsumeJsrCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub TempCancelConsumeJsrCompletedEventHandler(ByVal sender As Object, ByVal e As TempCancelConsumeJsrCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class TempCancelConsumeJsrCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub TempAccToCashCompletedEventHandler(ByVal sender As Object, ByVal e As TempAccToCashCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class TempAccToCashCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub TempConsumeListCompletedEventHandler(ByVal sender As Object, ByVal e As TempConsumeListCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class TempConsumeListCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub TempCancelCardCompletedEventHandler(ByVal sender As Object, ByVal e As TempCancelCardCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class TempCancelCardCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub TempCheckCancelCompletedEventHandler(ByVal sender As Object, ByVal e As TempCheckCancelCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class TempCheckCancelCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub TempCheckStateCompletedEventHandler(ByVal sender As Object, ByVal e As TempCheckStateCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class TempCheckStateCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub getRy_BaseCompletedEventHandler(ByVal sender As Object, ByVal e As getRy_BaseCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class getRy_BaseCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub getRy_GuoMinCompletedEventHandler(ByVal sender As Object, ByVal e As getRy_GuoMinCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class getRy_GuoMinCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub getRy_HealthCompletedEventHandler(ByVal sender As Object, ByVal e As getRy_HealthCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class getRy_HealthCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub getRy_InfoCompletedEventHandler(ByVal sender As Object, ByVal e As getRy_InfoCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class getRy_InfoCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub getRy_LxfsCompletedEventHandler(ByVal sender As Object, ByVal e As getRy_LxfsCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class getRy_LxfsCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0")>  _
    Public Delegate Sub getRy_MianYiCompletedEventHandler(ByVal sender As Object, ByVal e As getRy_MianYiCompletedEventArgs)
    
    '''<remarks/>
    <System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.7.3056.0"),  _
     System.Diagnostics.DebuggerStepThroughAttribute(),  _
     System.ComponentModel.DesignerCategoryAttribute("code")>  _
    Partial Public Class getRy_MianYiCompletedEventArgs
        Inherits System.ComponentModel.AsyncCompletedEventArgs
        
        Private results() As Object
        
        Friend Sub New(ByVal results() As Object, ByVal exception As System.Exception, ByVal cancelled As Boolean, ByVal userState As Object)
            MyBase.New(exception, cancelled, userState)
            Me.results = results
        End Sub
        
        '''<remarks/>
        Public ReadOnly Property Result() As String
            Get
                Me.RaiseExceptionIfNecessary
                Return CType(Me.results(0),String)
            End Get
        End Property
    End Class
End Namespace
