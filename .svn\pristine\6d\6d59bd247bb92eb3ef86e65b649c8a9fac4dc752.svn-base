﻿Imports System.Data.SqlClient
Imports HisControl

Public Class Yf_Js2

#Region "定义__变量"
    Dim My_Adapter As New SqlDataAdapter                '从表适配器
    Dim My_Table As New DataTable                       '从表

    Dim V_GridCol As Integer                            '当前TDBGrid所在的列

    Public Cb_Cm As CurrencyManager                     '同步指针

    Public V_Ck_Code As String                          '出库编码
    Public V_Insert As Boolean                          '增加记录

    Public Ck3_FirstLoad As Boolean                     '第一次调入明细表

    Dim Str_Select As String
    Public Yp_Lb As String
    Dim My_Dataset As New DataSet
    'Dim Ck_Sl1 As Integer
#End Region

#Region "传参"
 
    Dim Rdate As Date
    Dim Rrow As DataRow
    Dim RZbtb As DataTable
    Dim Rtdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Dim Rlb As C1.Win.C1Input.C1Label
    Dim Rzbadt As SqlDataAdapter
    Dim Rlx As String
    Dim R_Flag As Boolean
#End Region

    Public Sub New(ByVal tform As BaseForm, ByVal tdate As Date, ByVal trow As DataRow, ByVal tZbtb As DataTable, ByVal ttdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid, ByVal tlb As C1.Win.C1Input.C1Label, ByVal tzbadt As SqlDataAdapter, ByVal tlx As String, ByVal t_Flag As Boolean)

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        Rdate = tdate
        Rrow = trow
        RZbtb = tZbtb
        Rtdbgrid = ttdbgrid
        Rlb = tlb
        Rzbadt = tzbadt
        Rlx = tlx
        R_Flag = t_Flag
        ' 在 InitializeComponent() 调用之后添加任何初始化。

    End Sub

    Private Sub Yf_Js2_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Call Form_Init()                '窗体初始化
        Call Zb_Show()                  '显示数据

        If Rrow("Ck_Qr1") = "已接收" Then
            Comm1.Enabled = False
            Comm2.Enabled = False
            Comm3.Enabled = False
            C1TextBox1.Enabled = False
        Else
            Comm1.Enabled = True
            Comm2.Enabled = True
            Comm3.Enabled = True
            C1TextBox1.Enabled = True
        End If


    End Sub

#Region "窗体__事件"

    Private Sub Form_Init()
        Panel2.Height = 30
        '按扭初始化
        Call P_Comm(Me.Comm1)
        Call P_Comm(Me.Comm2)
        Call P_Comm(Me.Comm3)


        Dim My_Grid As New BaseClass.C_Grid(Me.C1TrueDBGrid1)
        With My_Grid
            .Init_Grid()
            .AllSort(False)
            .P_Dock(BaseClass.C_Grid.Dock.Fill)
            .Init_Column("名称", "Yp_Name", 185, "左", "")
            .Init_Column("批准文号", "Mx_Gyzz", 75, "左", "")
            .Init_Column("规格", "Mx_Gg", 100, "左", "")
            .Init_Column("产地", "Mx_Cd", 100, "左", "")
            .Init_Column("销售单位", "Mx_XsDw", 58, "左", "")
            .Init_Column("接收数量", "Yf_JsSl", 60, "右", "##0.0###")
            .Init_Column("零售价", "Yf_JsLsj", 60, "右", "##0.00####")
            .Init_Column("金额", "Ck_Money", 60, "右", "##0.00##")

        End With
    End Sub

    Private Sub P_Data_Show()   '从表数据

        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "Select V_YpKC.Xx_Code,Yp_Name,Mx_Gyzz,Mx_Gg,Mx_Cd,Yk_Yf2.Mx_XsDw,Ck_Code,Ck_Sl,Ck_Sl*Yk_Yf2.Mx_Cfbl as Yf_JsSl,Yf_Lsj1 as Yf_JsLsj,Ck_Money from Yk_Yf2,V_YpKC WHERE Yk_Yf2.Xx_Code=V_YpKc.Xx_Code and Ck_Code='" & V_Ck_Code & "' Order By Ck_Id", "从表", True)
        My_Table = My_Dataset.Tables("从表")

        '主表记录
        Cb_Cm = CType(BindingContext(My_Dataset, "从表"), CurrencyManager)
        C1TrueDBGrid1.SetDataBinding(My_Dataset, "从表", True)

        Call P_Sum()
        C1TextBox1.Select()

    End Sub
#End Region

#Region "清空__显示"

    Private Sub Zb_Show()   '显示记录
        If Rtdbgrid.RowCount = 0 Then Exit Sub
        With Rrow
            V_Ck_Code = .Item("Ck_Code") & ""
            Me.C1TextBox1.Text = .Item("Ck_Memo") & ""
            Me.Label1.Text = .Item("Jsr_Name") & ""
        End With
        Label12.Text = V_Ck_Code                                                '出库编码
        Call P_Data_Show()
    End Sub


#End Region

#Region "控件__动作"

#Region "其它__控件"

    

    Private Sub Comm_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.Click

        Dim Pd As Object = HisVar.HisVar.Sqldal.GetSingle("select distinct Pd_Wc from Zd_YkPd where Pd_Wc='否'")
        If Pd Is Nothing Then
        Else
            MsgBox("药库正在进行盘点，请等待盘点完成后在进行接收！", MsgBoxStyle.Information, "提示：")
            Exit Sub
        End If

        Pd = HisVar.HisVar.Sqldal.GetSingle("select distinct Pd_Wc from Zd_YfPd where Pd_Wc='否' and Yf_Code='" & HisVar.HisVar.YfCode & "'")
        If Pd Is Nothing Then
        Else
            MsgBox("药房正在进行盘点，请等待盘点完成后在进行接收！", MsgBoxStyle.Information, "提示：")
            Exit Sub
        End If

        Dim V_Js As Boolean
        V_Js = HisVar.HisVar.Sqldal.GetSingle("Select Ck_Qr From Yk_Yf1 Where Ck_Code='" & Rrow.Item("Ck_Code") & "'")
        If V_Js = True Then
            MsgBox("该笔调拨记录已经接收过,请退出窗体重新刷新数据", MsgBoxStyle.Exclamation, "提示")
            Exit Sub
        End If


        If MsgBox("是否接收:调拨单号=" + Rtdbgrid.Columns("Ck_Code").Value + " ", MsgBoxStyle.Information + MsgBoxStyle.OkCancel, "提示:") = MsgBoxResult.Cancel Then Exit Sub

        Dim tmp_Ds As New DataSet
        Dim tmp_str As String = ""
        tmp_Ds = HisVar.HisVar.Sqldal.Query("select Yp_Name,Mx_Gg,Yp_Ph from V_YpKc,Yk_Yf2 where  Yk_Yf2.Xx_Code=V_YpKc.Xx_Code   and Yk_Sl<Ck_Sl and Ck_Code='" & V_Ck_Code & "'")
        For Each tmprow In tmp_Ds.Tables(0).Rows
            tmp_str = tmprow("Yp_Name") & tmprow("Mx_Gg") & ":" & tmprow("Yp_Ph") & vbCrLf
        Next
        If tmp_str.Length > 0 Then
            MsgBox("下列药品实际库存不足" & vbCrLf & tmp_str, MsgBoxStyle.Information, "提示")
            Exit Sub
        End If

        Call Data_Update()
        Call Zb_Edit()
        Me.Close()
    End Sub

#End Region


    Private Sub Comm2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm2.Click
        Me.Close()
    End Sub
#End Region

#Region "自定义函数"
    Private Sub Data_Update()


        Dim Cb_Row As DataRow
        Dim Yf_Sl As String

        Yf_Sl = "Yf_Sl" & CDbl(Mid(HisVar.HisVar.YfCode, 5, 2))
        Dim arr As New ArrayList
        For Each Cb_Row In My_Dataset.Tables("从表").Rows
            arr.Add("Update Zd_Ml_Yp4 Set Yk_Sl=Yk_Sl-(" & Cb_Row.Item("Ck_Sl") & ")," & Yf_Sl & "=Isnull(" & Yf_Sl & ",0)+(" & Cb_Row.Item("Yf_JsSl") & ") Where Xx_code='" & Cb_Row.Item("Xx_Code") & "' ")
        Next
        arr.Add("Update Yk_Yf1 Set Ck_Qr=1 Where  Ck_Code='" & Rrow.Item("Ck_Code") & "' And Ck_Ok='1' And Ck_Qr='0'")
        HisVar.HisVar.Sqldal.ExecuteSqlTran(arr)
    End Sub

    Private Sub P_Sum()
        If My_Table.Rows.Count = 0 Then
            T_Label5.Text = "0条"
        Else
            T_Label5.Text = Trim(My_Table.Rows.Count & "条")
        End If
        T_Label5.Location = New Point(Me.Width - T_Label5.Width - 7, 8)
        T_Label4.Location = New Point(Me.Width - T_Label5.Width - T_Label4.Width - 7, 8)
    End Sub

    Private Sub Zb_Edit()   '编辑记录
        Try
            With Rrow
                .BeginEdit()
                .Item("Ck_Qr") = 1
                .Item("Ck_Qr1") = "已接收"
                .EndEdit()
               
            End With
            Rrow.AcceptChanges()
            If R_Flag = True Then
                Rrow.Delete()
            End If
        Catch ex As Exception
            Beep()
            Rrow.CancelEdit()
            MsgBox(ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
            Exit Sub
        End Try

    End Sub

#End Region

#Region "鼠标__外观"
    Private Sub P_Comm(ByVal Bt As Button)
        With Bt
            Select Case .Tag
                Case "接收"
                    .Location = New Point(T_Line1.Left + 10, 4)
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_接收1")
                    .Text = "            &B"

                Case "取消"
                    .Location = New Point(Comm1.Left + Comm1.Width + 4, 4)
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
                    .Width = Me.Comm1.Width
                    .Text = "            &C"
                    'T_Line2.Location = New Point(Me.Comm2.Left + Me.Comm2.Width + 8, 0)
                Case "拒收"
                    .Location = New Point(Comm2.Left + Comm2.Width + 4, 4)
                    .BackgroundImage = MyResources.C_Resources.getimage("拒收1")
                    .Width = Me.Comm1.Width
                    .Text = "            &T"
            End Select
            .FlatStyle = FlatStyle.Flat
            .FlatAppearance.BorderSize = 0
            .BackgroundImageLayout = ImageLayout.Center
        End With
    End Sub

    Private Sub Comm_MouseEnter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm2.MouseEnter, Comm1.MouseEnter, Comm3.MouseEnter
        Select Case sender.tag
            Case "接收"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_接收2")
                Comm1.Cursor = Cursors.Hand
            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消2")
                Comm2.Cursor = Cursors.Hand
            Case "拒收"
                Comm3.BackgroundImage = MyResources.C_Resources.getimage("拒收2")
                Comm3.Cursor = Cursors.Hand
        End Select

    End Sub

    Private Sub Comm_MouseLeave(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm2.MouseLeave, Comm1.MouseLeave, Comm3.MouseLeave
        Select Case sender.tag
            Case "接收"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_接收1")
                Comm1.Cursor = Cursors.Default
            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
                Comm2.Cursor = Cursors.Default
            Case "拒收"
                Comm3.BackgroundImage = MyResources.C_Resources.getimage("拒收1")
                Comm3.Cursor = Cursors.Default
        End Select
    End Sub

    Private Sub Comm_MouseDown(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm2.MouseDown, Comm1.MouseDown, Comm3.MouseDown
        Select Case sender.tag
            Case "接收"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_接收3")
                Comm1.Cursor = Cursors.Default

            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消3")
                Comm2.Cursor = Cursors.Default
            Case "拒收"
                Comm3.BackgroundImage = MyResources.C_Resources.getimage("拒收3")
                Comm3.Cursor = Cursors.Default
        End Select
    End Sub

    Private Sub Comm_MouseUp(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm2.MouseUp, Comm1.MouseUp, Comm3.MouseUp
        Select Case sender.tag
            Case "接收"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_接收1")
                Comm1.Cursor = Cursors.Hand
            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
                Comm2.Cursor = Cursors.Hand
            Case "拒收"
                Comm3.BackgroundImage = MyResources.C_Resources.getimage("拒收1")
                Comm3.Cursor = Cursors.Hand
        End Select
    End Sub
#End Region


    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm3.Click
        If MsgBox("是否确定拒收该单据？ ", MsgBoxStyle.Information + MsgBoxStyle.OkCancel, "提示:") = MsgBoxResult.Cancel Then Exit Sub
        Try
            HisVar.HisVar.Sqldal.ExecuteSql("Update Yk_Yf1 Set Ck_Ok='0'where  Ck_Code='" & Label12.Text & "' and Ck_OK=1 And Ck_Qr=0")
            Rtdbgrid.Delete()
            Rrow.AcceptChanges()
        Catch ex As Exception
            Beep()
            Rrow.CancelEdit()
            MsgBox(ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
            Exit Sub
        End Try

        '更新主表

        Me.Close()
    End Sub
End Class