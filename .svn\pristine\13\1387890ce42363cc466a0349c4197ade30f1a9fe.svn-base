﻿/**  版本信息模板在安装目录下，可自行修改。
* D_Emr_BasicElementValue.cs
*
* 功 能： N/A
* 类 名： D_Emr_BasicElementValue
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/8/16 星期二 下午 1:38:16   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using System.Collections.Generic;
//
namespace DAL
{
    /// <summary>
    /// 数据访问类:D_Emr_BasicElementValue
    /// </summary>
    public partial class D_Emr_BasicElementValue
    {
        public D_Emr_BasicElementValue()
        { }
        #region  BasicMethod

        /// <summary>
        /// 得到最大ID
        /// </summary>
        public int GetMaxId()
        {
            return HisVar.HisVar.Sqldal.GetMaxID("id", "Emr_BasicElementValue");
        }

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(int id)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from Emr_BasicElementValue");
            strSql.Append(" where id=@id ");
            SqlParameter[] parameters = {
					new SqlParameter("@id", SqlDbType.Int,4)			};
            parameters[0].Value = id;

            return HisVar.HisVar.Sqldal.Exists(strSql.ToString(), parameters);
        }


        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ModelOld.M_Emr_BasicElementValue model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into Emr_BasicElementValue(");
            strSql.Append("id,Ele_Code,DefaultValue)");
            strSql.Append(" values (");
            strSql.Append("@id,@Ele_Code,@DefaultValue)");
            SqlParameter[] parameters = {
					new SqlParameter("@id", SqlDbType.Int,4),
					new SqlParameter("@Ele_Code", SqlDbType.Char,10),
					new SqlParameter("@DefaultValue", SqlDbType.VarChar,50)};
            parameters[0].Value = model.id;
            parameters[1].Value = model.Ele_Code;
            parameters[2].Value = model.DefaultValue;

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ModelOld.M_Emr_BasicElementValue model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update Emr_BasicElementValue set ");
            strSql.Append("Ele_Code=@Ele_Code,");
            strSql.Append("DefaultValue=@DefaultValue");
            strSql.Append(" where id=@id ");
            SqlParameter[] parameters = {
					new SqlParameter("@Ele_Code", SqlDbType.Char,10),
					new SqlParameter("@DefaultValue", SqlDbType.VarChar,50),
					new SqlParameter("@id", SqlDbType.Int,4)};
            parameters[0].Value = model.Ele_Code;
            parameters[1].Value = model.DefaultValue;
            parameters[2].Value = model.id;

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        public bool Update(string Ele_Code, string content)
        {
            try
            {
                List<string> SqlList = new List<string>();
                List<SqlParameter[]> sqlPara = new List<SqlParameter[]>();
                int id = 0;
                StringBuilder strSql = new StringBuilder();
                strSql.Append("delete from Emr_BasicElementValue ");
                strSql.Append(" where Ele_Code=@Ele_Code ");
                SqlParameter[] parameters = {
					new SqlParameter("@Ele_Code", SqlDbType.Int,10)			};
                parameters[0].Value = Ele_Code;

                SqlList.Add(strSql.ToString());
                sqlPara.Add(parameters);

                if (HisVar.HisVar.Sqldal.GetMaxID("id", "Emr_BasicElementList").ToString() == "")
                {
                    id = 1;
                }
                else
                {
                    id = int.Parse(HisVar.HisVar.Sqldal.GetMaxID("id", "Emr_BasicElementList").ToString()) + 1;
                }


                strSql = new StringBuilder();
                strSql.Append("insert into Emr_BasicElementValue(");
                strSql.Append("id,Ele_Code,DefaultValue)");
                strSql.Append(" values (");
                strSql.Append("@id,@Ele_Code,@DefaultValue)");
                parameters = new SqlParameter[]{
					new SqlParameter("@id", SqlDbType.Int,4),
					new SqlParameter("@Ele_Code", SqlDbType.Char,10),
					new SqlParameter("@DefaultValue", SqlDbType.VarChar,50)};
                parameters[0].Value = id;
                parameters[1].Value = Ele_Code;
                parameters[2].Value = content;

                SqlList.Add(strSql.ToString());
                sqlPara.Add(parameters);

                HisVar.HisVar.Sqldal.ExecuteSql(SqlList, sqlPara);
                return true;
            }
            catch (Exception e)
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(int id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from Emr_BasicElementValue ");
            strSql.Append(" where id=@id ");
            SqlParameter[] parameters = {
					new SqlParameter("@id", SqlDbType.Int,4)			};
            parameters[0].Value = id;

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteCode(string code)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from Emr_BasicElementList ");
            strSql.Append(" where ele_code='" + code + "'  ");
            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ModelOld.M_Emr_BasicElementValue GetModel(int id)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 id,Ele_Code,DefaultValue from Emr_BasicElementValue ");
            strSql.Append(" where id=@id ");
            SqlParameter[] parameters = {
					new SqlParameter("@id", SqlDbType.Int,4)			};
            parameters[0].Value = id;

            ModelOld.M_Emr_BasicElementValue model = new ModelOld.M_Emr_BasicElementValue();
            DataSet ds = HisVar.HisVar.Sqldal.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ModelOld.M_Emr_BasicElementValue DataRowToModel(DataRow row)
        {
            ModelOld.M_Emr_BasicElementValue model = new ModelOld.M_Emr_BasicElementValue();
            if (row != null)
            {
                if (row["id"] != null && row["id"].ToString() != "")
                {
                    model.id = int.Parse(row["id"].ToString());
                }
                if (row["Ele_Code"] != null)
                {
                    model.Ele_Code = row["Ele_Code"].ToString();
                }
                if (row["DefaultValue"] != null)
                {
                    model.DefaultValue = row["DefaultValue"].ToString();
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select id,Ele_Code,DefaultValue ");
            strSql.Append(" FROM Emr_BasicElementValue ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" id,Ele_Code,DefaultValue ");
            strSql.Append(" FROM Emr_BasicElementValue ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM Emr_BasicElementValue ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.id desc");
            }
            strSql.Append(")AS Row, T.*  from Emr_BasicElementValue T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

        /*
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetList(int PageSize,int PageIndex,string strWhere)
        {
            SqlParameter[] parameters = {
                    new SqlParameter("@tblName", SqlDbType.VarChar, 255),
                    new SqlParameter("@fldName", SqlDbType.VarChar, 255),
                    new SqlParameter("@PageSize", SqlDbType.Int),
                    new SqlParameter("@PageIndex", SqlDbType.Int),
                    new SqlParameter("@IsReCount", SqlDbType.Bit),
                    new SqlParameter("@OrderType", SqlDbType.Bit),
                    new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
                    };
            parameters[0].Value = "Emr_BasicElementValue";
            parameters[1].Value = "id";
            parameters[2].Value = PageSize;
            parameters[3].Value = PageIndex;
            parameters[4].Value = 0;
            parameters[5].Value = 0;
            parameters[6].Value = strWhere;	
            return HisVar.HisVar.Sqldal.RunProcedure("UP_GetRecordByPage",parameters,"ds");
        }*/

        #endregion  BasicMethod
        #region  ExtensionMethod

        #endregion  ExtensionMethod
    }
}

