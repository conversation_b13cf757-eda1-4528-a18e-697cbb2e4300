﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Data;
namespace DAL
{
    public static class DAL_Dict
    {
        #region 字典

        #region 科室字典
        /// <summary>
        ///科室字典
        /// </summary>
        public static DataTable GetDepartmentDict()
        {
            return HisVar.HisVar.Sqldal.Query("SELECT Ks_Jc,Ks_Name,Ks_Code FROM Zd_YyKs Where Yy_Code='" + HisVar.HisVar.WsyCode + "' Order By Ks_Code").Tables[0];
        }
        #endregion

        #region 医生字典
        /// <summary>
        ///医生字典
        /// </summary>
        public static DataTable GetDoctorDict()
        {
            return HisVar.HisVar.Sqldal.Query("SELECT Ys_Jc,Ys_Name,Ys_Code,Zd_YyYs.Ks_Code,Ks_Name FROM Zd_YyYs,Zd_Yyks Where Zd_YyYs.Ks_Code=Zd_Yyks.Ks_Code and Zd_YyYs.Yy_Code='" + HisVar.HisVar.WsyCode + "' And Ys_Use='是' Order By Ys_Jc").Tables[0];
        }
        #endregion

        #region 供应商字典
        /// <summary>
        ///供应商字典
        /// </summary>
        public static DataTable GetCgkhDict()
        {
            return HisVar.HisVar.Sqldal.Query("SELECT Kh_Jc,Kh_Name,Kh_Code,Kh_Fzr,Kh_Tel,Kh_Fax,Kh_Address FROM Zd_Kh_Rk Where Kh_Use='是' And Yy_Code='" + HisVar.HisVar.WsyCode + "'").Tables[0];
        }
        #endregion

        #region 批发客户字典
        /// <summary>
        ///批发客户字典
        /// </summary>
        public static DataTable GetPfsDict()
        {
            return HisVar.HisVar.Sqldal.Query("SELECT Kh_Jc,Kh_Name,Kh_Code,Kh_Fzr,Kh_Tel, Hzyl_Zs_Code FROM Zd_Kh_Xs Where Kh_Use='是' And Yy_Code='" + HisVar.HisVar.WsyCode + "'").Tables[0];
        }
        #endregion
  

        /// <summary>
        ///药房字典
        /// </summary>
        public static DataTable GetYfDict()
        {
            return HisVar.HisVar.Sqldal.Query("SELECT Yf_Code,Yf_Jc,Yf_Name FROM Zd_YyYf Where Yy_Code='" + HisVar.HisVar.WsyCode + "' And Yf_Use=1 Order By Yf_Jc").Tables[0];
        }

        ///// <summary>
        /////疾病字典
        ///// </summary>
        //public static DataTable GetJbDict()
        //{
        //    return HisVar.HisVar.Sqldal.Query("SELECT Jb_Code,Jb_Jc,Jb_Name FROM Zd_Ml_Jb3 Order By Jb_Jc").Tables[0];
        //}

        // /// <summary>
        // ///药品字典
        // /// </summary>
        // public static DataTable GetYpDict()
        // {
        //     string str;
        //     if (HisPara.PublicConfig.JbYp == "是")
        //     {
        //         str = "Select Yp_Jc,Yp_Name,Jx_Name,Mx_Gyzz,Mx_Gg,Mx_Cd,Jx_Code,Mx_Cgdw,Mx_Xsdw,Mx_Cfbl,Mx_Code,Dl_Code,Yp_Code,IsJb,Dl_Name From V_Yp  Where (IsUse=1 or Dl_Code='03' or Dl_Code='04') Order by Yp_Jc";
        //     }
        //     else
        //     {
        //         str = "Select Yp_Jc,Yp_Name,Jx_Name,Mx_Gyzz,Mx_Gg,Mx_Cd,Jx_Code,Mx_Cgdw,Mx_Xsdw,Mx_Cfbl,Mx_Code,Dl_Code,Yp_Code,IsJb,Dl_Name From V_Yp Order by Yp_Jc";
        //     }
        //     return HisVar.HisVar.Sqldal.Query(str).Tables[0];
        //
        // }

        #endregion
    }
}
