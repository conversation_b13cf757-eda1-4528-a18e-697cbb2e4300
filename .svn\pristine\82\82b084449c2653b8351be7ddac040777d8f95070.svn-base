﻿/**  版本信息模板在安装目录下，可自行修改。
* M_Zd_Bxlb.cs
*
* 功 能： N/A
* 类 名： M_Zd_Bxlb
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/10/21 15:32:50   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace ModelOld
{
	/// <summary>
	/// M_Zd_Bxlb:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class M_Zd_Bxlb
	{
		public M_Zd_Bxlb()
		{}
		#region Model
		private string _yy_code;
		private string _bxlb_code;
		private string _bxlb_jc;
		private string _bxlb_name;
		private string _bxlb_memo;
		/// <summary>
		/// 
		/// </summary>
		public string Yy_Code
		{
			set{ _yy_code=value;}
			get{return _yy_code;}
		}
		/// <summary>
		/// 报销类别类别_编码
		/// </summary>
		public string Bxlb_Code
		{
			set{ _bxlb_code=value;}
			get{return _bxlb_code;}
		}
		/// <summary>
		/// 报销类别_简称
		/// </summary>
		public string Bxlb_Jc
		{
			set{ _bxlb_jc=value;}
			get{return _bxlb_jc;}
		}
		/// <summary>
		/// 报销类别_名称
		/// </summary>
		public string Bxlb_Name
		{
			set{ _bxlb_name=value;}
			get{return _bxlb_name;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Bxlb_Memo
		{
			set{ _bxlb_memo=value;}
			get{return _bxlb_memo;}
		}
		#endregion Model

	}
}

