﻿/**  版本信息模板在安装目录下，可自行修改。
* M_LIS_TestXm.cs
*
* 功 能： N/A
* 类 名： M_LIS_TestXm
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/10/17 星期一 下午 1:38:53   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace ModelOld
{
	/// <summary>
	/// M_LIS_TestXm:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class M_LIS_TestXm
	{
		public M_LIS_TestXm()
		{}
		#region Model
		private string _testxm_code;
		private string _testxm_name;
		private string _testxm_jc;
		private string _dev_code;
		private string _testsample;
		private string _channelcode;
		private int? _accuracy;
		private decimal? _addnum;
		private decimal? _conversionnum;
		private byte[] _testxmrpt;
		private string _memo;
		/// <summary>
		/// 
		/// </summary>
		public string TestXm_Code
		{
			set{ _testxm_code=value;}
			get{return _testxm_code;}
		}
		/// <summary>
		/// 检验项目
		/// </summary>
		public string TestXm_Name
		{
			set{ _testxm_name=value;}
			get{return _testxm_name;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string TestXm_Jc
		{
			set{ _testxm_jc=value;}
			get{return _testxm_jc;}
		}
		/// <summary>
		/// 检验设备
		/// </summary>
		public string Dev_Code
		{
			set{ _dev_code=value;}
			get{return _dev_code;}
		}
		/// <summary>
		/// 检验标本
		/// </summary>
		public string TestSample
		{
			set{ _testsample=value;}
			get{return _testsample;}
		}
		/// <summary>
		/// 通道码
		/// </summary>
		public string ChannelCode
		{
			set{ _channelcode=value;}
			get{return _channelcode;}
		}
		/// <summary>
		/// 精度：就是小数位数，如果不填，默认为 2 位小数
		/// </summary>
		public int? Accuracy
		{
			set{ _accuracy=value;}
			get{return _accuracy;}
		}
		/// <summary>
		/// 加算值：在原始结果上加上一个固定数字。
		/// </summary>
		public decimal? AddNum
		{
			set{ _addnum=value;}
			get{return _addnum;}
		}
		/// <summary>
		/// 换算比：在原始结果上乘以一个固定数字。
		/// </summary>
		public decimal? ConversionNum
		{
			set{ _conversionnum=value;}
			get{return _conversionnum;}
		}
		/// <summary>
		/// 项目报告单
		/// </summary>
		public byte[] TestXmRpt
		{
			set{ _testxmrpt=value;}
			get{return _testxmrpt;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Memo
		{
			set{ _memo=value;}
			get{return _memo;}
		}
		#endregion Model

	}
}

