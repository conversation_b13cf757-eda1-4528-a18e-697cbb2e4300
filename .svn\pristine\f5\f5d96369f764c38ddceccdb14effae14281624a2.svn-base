﻿/**  版本信息模板在安装目录下，可自行修改。
* B_KC22.cs
*
* 功 能： N/A
* 类 名： B_KC22
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/8/24 08:44:38   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Collections.Generic;
using ModelOld;
namespace BLLOld
{
	/// <summary>
	/// B_KC22
	/// </summary>
	public partial class B_KC22
	{
		private readonly DAL.D_KC22 dal=new DAL.D_KC22();
		public B_KC22()
		{}
		#region  BasicMethod
        ///// <summary>
        ///// 是否存在该记录
        ///// </summary>
        //public bool Exists(string AKC190,string AKC220,DateTime AKC221,string AKC515)
        //{
        //    return dal.Exists(AKC190,AKC220,AKC221,AKC515);
        //}

		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ModelOld.M_KC22 model)
		{
			return dal.Add(model);
		}

        /// <summary>
		/// 按条件更新数据
		/// </summary>
        public bool Update(string strWhere)
        {
            return dal.Update(strWhere);
        }
        /// <summary>
        /// 将记录标记为不可用  IsEnable 0 不可用，1 可用
        /// </summary>
        /// <param name="strWhere"></param>
        /// <returns></returns>
        public bool UpdateIsEnable(string strWhere)
        {
            return dal.UpdateIsEnable(strWhere);
        }

        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ModelOld.M_KC22 model)
		{
			return dal.Update(model);
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string AKC190,string AKC220,DateTime AKC221,string AKC515)
		{
			
			return dal.Delete(AKC190,AKC220,AKC221,AKC515);
		}
        /// <summary>
        /// 删除多条数据
        /// </summary>
        public bool Delete(string AKC190)
        {
            return dal.Delete(AKC190);
        }
        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ModelOld.M_KC22 GetModel(string AKC190, string AKC220, DateTime AKC221, string AKC515)
        {

            return dal.GetModel(AKC190, AKC220, AKC221, AKC515);
        }

        ///// <summary>
        ///// 得到一个对象实体，从缓存中
        ///// </summary>
        //public ModelOld.M_KC22 GetModelByCache(string AKC190,string AKC220,DateTime AKC221,string AKC515)
        //{
			
        //    string CacheKey = "M_KC22Model-" + AKC190+AKC220+AKC221+AKC515;
        //    object objModel = Common.DataCache.GetCache(CacheKey);
        //    if (objModel == null)
        //    {
        //        try
        //        {
        //            objModel = dal.GetModel(AKC190,AKC220,AKC221,AKC515);
        //            if (objModel != null)
        //            {
        //                int ModelCache = Common.ConfigHelper.GetConfigInt("ModelCache");
        //                Common.DataCache.SetCache(CacheKey, objModel, DateTime.Now.AddMinutes(ModelCache), TimeSpan.Zero);
        //            }
        //        }
        //        catch{}
        //    }
        //    return (ModelOld.M_KC22)objModel;
        //}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			return dal.GetList(strWhere);
		}
		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			return dal.GetList(Top,strWhere,filedOrder);
		}
		/// <summary>
		/// 获得数据列表
		/// </summary>
		public List<ModelOld.M_KC22> GetModelList(string strWhere)
		{
			DataSet ds = dal.GetList(strWhere);
			return DataTableToList(ds.Tables[0]);
		}
		/// <summary>
		/// 获得数据列表
		/// </summary>
		public List<ModelOld.M_KC22> DataTableToList(DataTable dt)
		{
			List<ModelOld.M_KC22> modelList = new List<ModelOld.M_KC22>();
			int rowsCount = dt.Rows.Count;
			if (rowsCount > 0)
			{
				ModelOld.M_KC22 model;
				for (int n = 0; n < rowsCount; n++)
				{
					model = dal.DataRowToModel(dt.Rows[n]);
					if (model != null)
					{
						modelList.Add(model);
					}
				}
			}
			return modelList;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetAllList()
		{
			return GetList("");
		}

		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			return dal.GetRecordCount(strWhere);
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			return dal.GetListByPage( strWhere,  orderby,  startIndex,  endIndex);
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		//public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		//{
			//return dal.GetList(PageSize,PageIndex,strWhere);
		//}

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

