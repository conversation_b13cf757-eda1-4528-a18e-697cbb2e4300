﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="0" />
    <DataSources isList="true" count="0" />
    <Relations isList="true" count="0" />
    <Report isRef="0" />
    <Variables isList="true" count="55">
      <value>,名称,名称,名称,System.String,,False,False,False</value>
      <value>,个人编号,个人编号,System.String,,False,False</value>
      <value>,姓名,姓名,System.String,,False,False</value>
      <value>,性别,性别,System.String,,False,False</value>
      <value>,年龄,年龄,System.String,,False,False</value>
      <value>,人员类别,人员类别,System.String,,False,False</value>
      <value>,工作单位,工作单位,System.String,,False,False</value>
      <value>,账户余额,账户余额,System.String,,False,False</value>
      <value>,待遇类别,待遇类别,System.String,,False,False</value>
      <value>,公务员类别,公务员类别,System.String,,False,False</value>
      <value>,医疗类别,医疗类别,System.String,,False,False</value>
      <value>,就诊医院,就诊医院,System.String,,False,False</value>
      <value>,疾病诊断,疾病诊断,System.String,,False,False</value>
      <value>,截止上次本年医疗费总额,截止上次本年医疗费总额,System.String,,False,False</value>
      <value>,截止上次本年住院符合基本医疗费用累计,截止上次本年住院符合基本医疗费用累计,System.String,,False,False</value>
      <value>,截止上次本年统筹基金支付累计,截止上次本年统筹基金支付累计,System.String,,False,False</value>
      <value>,本次医疗费用总额,本次医疗费用总额,System.String,,False,False</value>
      <value>,本次符合基本医疗费用合计,本次符合基本医疗费用合计,System.String,,False,False</value>
      <value>,起付标准,起付标准,System.String,,False,False</value>
      <value>,转诊先自付,转诊先自付,System.String,,False,False</value>
      <value>,本次不符合基本医疗费用合计,本次不符合基本医疗费用合计,System.String,,False,False</value>
      <value>,乙类自理,乙类自理,System.String,,False,False</value>
      <value>,丙类自费,丙类自费,System.String,,False,False</value>
      <value>,超限价自费,超限价自费,System.String,,False,False</value>
      <value>,统筹基金支付,统筹基金支付,System.String,,False,False</value>
      <value>,大额基金支付,大额基金支付,System.String,,False,False</value>
      <value>,公务员补助支付,公务员补助支付,System.String,,False,False</value>
      <value>,个人账户支付,个人账户支付,System.String,,False,False</value>
      <value>,自费费用,自费费用,System.String,,False,False</value>
      <value>,个人自付,个人自付,System.String,,False,False</value>
      <value>,本次报销总额,本次报销总额,System.String,,False,False</value>
      <value>,现金支付,现金支付,System.String,,False,False</value>
      <value>,本次报销总额大写,本次报销总额大写,System.String,,False,False</value>
      <value>,合计,合计,System.String,,False,False</value>
      <value>,年度报销累计,年度报销累计,System.String,,False,False</value>
      <value>,统筹基金支付累计,统筹基金支付累计,System.String,,False,False</value>
      <value>,大额基金支付累计,大额基金支付累计,System.String,,False,False</value>
      <value>,超大额封顶线公务员补助累计,超大额封顶线公务员补助累计,System.String,,False,False</value>
      <value>,单据号,单据号,System.String,,False,False</value>
      <value>,定点医疗机构名称,定点医疗机构名称,System.String,,False,False</value>
      <value>,就诊流水号,就诊流水号,System.String,,False,False</value>
      <value>,截止上次本年大额基金支付累计,截止上次本年大额基金支付累计,System.String,,False,False</value>
      <value>,截止上次本年门诊慢性病起付标准累计,截止上次本年门诊慢性病起付标准累计,System.String,,False,False</value>
      <value>,截止上次本年门诊慢性病基金支付累计,截止上次本年门诊慢性病基金支付累计,System.String,,False,False</value>
      <value>,药费,药费,System.String,,False,False</value>
      <value>,治疗费,治疗费,System.String,,False,False</value>
      <value>,检查费化验费,检查费化验费,System.String,,False,False</value>
      <value>,手术费,手术费,System.String,,False,False</value>
      <value>,材料费,材料费,System.String,,False,False</value>
      <value>,其他费,其他费,System.String,,False,False</value>
      <value>,操作员,操作员,System.String,,False,False</value>
      <value>,打印日期,打印日期,System.String,,False,False</value>
      <value>,救助类型,救助类型,System.String,,False,False</value>
      <value>,医疗救助支付,医疗救助支付,System.String,,False,False</value>
      <value>,截至上次本年医疗救助支付累计,截至上次本年医疗救助支付累计,System.String,,False,False</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="2" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="241">
        <Text2 Ref="3" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0.05,2.2,1.63,0.76</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text2</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>个人编号</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text2>
        <Text3 Ref="4" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>5.31,2.2,0.76,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text3</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>姓名</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text3>
        <Text4 Ref="5" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>9.46,2.2,1.1,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text4</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>性别</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text4>
        <Text5 Ref="6" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>12.16,2.2,0.65,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text5</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>年龄</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text5>
        <Text6 Ref="7" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>14.42,2.2,1.52,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text6</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>人员类别</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text6>
        <Text7 Ref="8" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0.2,3.2,1.5,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text7</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>工作单位</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text7>
        <Text10 Ref="9" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>9.53,3.2,2.09,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text10</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>医疗类别</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text10>
        <Text12 Ref="10" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>14.31,3.2,1.52,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text12</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>疾病诊断</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text12>
        <Text19 Ref="11" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0.21,3.85,6.6,0.7</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text19</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>截止上次本年统筹基金支付累计		
</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text19>
        <Text20 Ref="12" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0.3,6.4,2.27,0.73</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text20</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>本次医疗费用
总额	
</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
        </Text20>
        <Text21 Ref="13" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>5.98,6.4,4.58,0.71</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text21</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>本次符合基本医疗费用合计				
</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text21>
        <Text22 Ref="14" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>12.57,6.4,2.18,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text22</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>起付标准自付</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text22>
        <Text24 Ref="15" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0.32,7.2,2.23,0.66</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text24</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>本次不符合基本
医疗费用合计	
</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
        </Text24>
        <Text25 Ref="16" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>5.97,7.25,1.52,0.4</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text25</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>其中:</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
        </Text25>
        <Text26 Ref="17" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>7.7,7.25,1.52,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text26</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>乙类自理:</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text26>
        <Text27 Ref="18" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>11.11,7.25,1.52,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text27</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>丙类自费:</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text27>
        <Text28 Ref="19" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>14.57,7.25,1.52,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text28</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>超限价自费</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text28>
        <Text39 Ref="20" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0.15,8.2,2.65,1.49</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text39</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>本次基金支付</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text39>
        <Text40 Ref="21" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>3.39,8.2,2.37,0.63</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text40</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>统筹基本支付</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text40>
        <Text41 Ref="22" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>3.57,9,1.98,0.65</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text41</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>大额基金支付</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text41>
        <Text42 Ref="23" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>8.63,8.2,2.15,0.54</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text42</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>公务员补助支付</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text42>
        <Text43 Ref="24" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>8.64,9.2,2.13,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text43</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>个人账户支付</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text43>
        <Text44 Ref="25" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0.09,10.8,2.25,0.53</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text44</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>本次报销总额</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text44>
        <Text45 Ref="26" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>9.14,10.8,1.49,0.41</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text45</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>现金自付</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text45>
        <Text46 Ref="27" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0.16,11.6,2.88,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text46</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>本次报销总额(大写)</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text46>
        <Text47 Ref="28" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>13.89,8,0.52,3.19</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text47</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>个
人
负
担
部
分</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text47>
        <Text48 Ref="29" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>14.72,8.2,1.52,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text48</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>乙类自理</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text48>
        <Text49 Ref="30" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>14.71,9,1.52,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text49</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>自费费用</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text49>
        <Text50 Ref="31" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>14.86,10,1.52,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text50</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>个人自付</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text50>
        <Text51 Ref="32" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>14.81,10.8,1.52,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text51</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>合计:</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text51>
        <Text57 Ref="33" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0.05,1.4,2.71,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text57</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>定点医疗机构名称:</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text57>
        <Text58 Ref="34" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>6.34,1.4,1.52,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text58</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>单据号:</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text58>
        <Text59 Ref="35" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>17.24,1.6,1.52,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text59</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>单位:元</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
        </Text59>
        <Text67 Ref="36" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>1.87,2.2,3.24,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text67</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{个人编号}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text67>
        <Text68 Ref="37" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>6.49,2.2,2.59,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text68</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{姓名}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text68>
        <Text69 Ref="38" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>10.96,2.2,0.88,0.44</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text69</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{性别}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text69>
        <Text70 Ref="39" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>13.06,2.2,0.91,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text70</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{年龄}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text70>
        <Text71 Ref="40" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>16.27,2.2,2.51,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text71</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{人员类别}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text71>
        <Text72 Ref="41" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>1.84,3.2,3.39,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text72</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{工作单位}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text72>
        <Text76 Ref="42" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>12.04,3.2,1.99,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text76</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{医疗类别}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text76>
        <Text78 Ref="43" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>16.26,3.2,2.53,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,7</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text78</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{疾病诊断}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text78>
        <Text85 Ref="44" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>6.99,4,2.22,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text85</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{截止上次本年统筹基金支付累计}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text85>
        <Text86 Ref="45" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>2.74,6.4,2.85,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text86</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{本次医疗费用总额}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text86>
        <Text87 Ref="46" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>10.86,6.4,1.5,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text87</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{本次符合基本医疗费用合计}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text87>
        <Text88 Ref="47" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>15.37,6.4,3.28,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text88</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{起付标准}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text88>
        <Text90 Ref="48" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>2.79,7.25,2.74,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text90</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{本次不符合基本医疗费用合计}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text90>
        <Text91 Ref="49" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>9.26,7.25,1.73,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text91</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{乙类自理}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text91>
        <Text92 Ref="50" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>12.66,7.25,1.86,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text92</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{丙类自费}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text92>
        <Text93 Ref="51" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>16.17,7.25,2.54,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text93</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{超限价自费}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text93>
        <Text115 Ref="52" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>6.14,8.2,2.08,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text115</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{统筹基金支付}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text115>
        <Text116 Ref="53" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>6.16,9,2.11,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text116</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{大额基金支付}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text116>
        <Text117 Ref="54" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>11.31,8.2,2.26,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text117</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{公务员补助支付}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text117>
        <Text118 Ref="55" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>11.3,9,2.26,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text118</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{个人账户支付}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text118>
        <Text119 Ref="56" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>16.6,8.2,1.98,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text119</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{乙类自理}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text119>
        <Text120 Ref="57" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>16.65,9,2.11,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text120</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{自费费用}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text120>
        <Text121 Ref="58" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>16.64,10,2.11,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text121</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{个人自付}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text121>
        <Text122 Ref="59" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>3.34,10.8,4.93,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text122</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{本次报销总额}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text122>
        <Text123 Ref="60" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>11.35,10.8,2.25,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text123</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{现金支付}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text123>
        <Text124 Ref="61" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>3.37,11.6,15.38,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text124</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{本次报销总额大写}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text124>
        <Text125 Ref="62" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>16.61,10.8,2.01,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text125</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{合计}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text125>
        <Text130 Ref="63" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>7.76,1.4,4.22,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text130</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{单据号}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text130>
        <Text131 Ref="64" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>2.82,1.4,3.08,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text131</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{定点医疗机构名称}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text131>
        <Text66 Ref="65" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>14.01,1.4,3.11,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text66</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{就诊流水号}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text66>
        <Text132 Ref="66" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>12.02,1.4,1.91,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text132</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>就诊流水号:</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text132>
        <Text8 Ref="67" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>9.3,4,6.54,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text8</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>截止上次本年大额基金支付累计</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text8>
        <Text9 Ref="68" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0.16,4.8,6.65,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text9</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>截止上次本年门诊慢性病起付标准累计</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text9>
        <Text11 Ref="69" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>9.32,4.8,6.55,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text11</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>截止上次本年门诊慢性病基金支付累计</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text11>
        <Text13 Ref="70" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0.07,5.6,0.83,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text13</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>药费</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text13>
        <Text14 Ref="71" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>2.75,5.6,1.07,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text14</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>治疗费</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text14>
        <Text15 Ref="72" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>5.66,5.6,2.57,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text15</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>检查费化验费</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text15>
        <Text16 Ref="73" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>12.45,5.6,1.52,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text16</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>材料费</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text16>
        <Text17 Ref="74" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>15.87,5.6,1.23,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text17</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>其他费</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text17>
        <Text18 Ref="75" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0.25,12.4,1.52,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text18</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>初审人签字</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text18>
        <Text23 Ref="76" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>12.17,12.4,2.24,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text23</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>领款人签字</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text23>
        <Text30 Ref="77" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>9.71,5.6,1.07,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text30</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>手术费</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text30>
        <Text31 Ref="78" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>16.06,4,2.69,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text31</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{截止上次本年大额基金支付累计}</Text>
          <TextBrush>Black</TextBrush>
        </Text31>
        <Text32 Ref="79" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>7.06,4.8,2.08,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text32</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{截止上次本年门诊慢性病起付标准累计}</Text>
          <TextBrush>Black</TextBrush>
        </Text32>
        <Text33 Ref="80" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>16.1,4.8,2.62,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text33</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{截止上次本年门诊慢性病基金支付累计}</Text>
          <TextBrush>Black</TextBrush>
        </Text33>
        <Text34 Ref="81" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>1.02,5.6,1.52,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text34</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{药费}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text34>
        <Text35 Ref="82" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>4.04,5.6,1.52,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text35</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{治疗费}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text35>
        <Text36 Ref="83" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>8.33,5.6,1.34,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text36</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{检查费化验费}</Text>
          <TextBrush>Black</TextBrush>
        </Text36>
        <Text37 Ref="84" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>11.07,5.6,1.23,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text37</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{手术费}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text37>
        <Text38 Ref="85" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>14.14,5.6,1.52,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text38</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{材料费}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text38>
        <Text52 Ref="86" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>17.27,5.6,1.38,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text52</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{其他费}</Text>
          <TextBrush>Black</TextBrush>
          <VertAlignment>Center</VertAlignment>
        </Text52>
        <Text55 Ref="87" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>5.2,3,1.1,0.71</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>8c5fcf9e51f9408f93134d4eb57ef851</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text55</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>救助
类型</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text55>
        <Text56 Ref="88" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,9.8,3.05,0.73</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>a6cb610d74e447f1871936197d3fe6cd</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text56</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>医疗救助支付</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text56>
        <Text60 Ref="89" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>6,9.8,4.05,0.73</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>8206e49b28aa413bb9cd19b9badab528</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text60</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>截至上次本年医疗救助支付累计</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text60>
        <Text61 Ref="90" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>6.2,12.4,2.04,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>ce544d5a3ccc4414a0f6787adb30cf6b</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text61</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>复审人签字</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text61>
        <Text53 Ref="91" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>8.4,13.2,1.52,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>bc2524ff2cc141deb780bcb1299a9ac8</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text53</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>年    月    日</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text53>
        <Text54 Ref="92" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>14.6,13.2,1.52,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>753c0855b5ea47d4ab4ee095020658ce</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text54</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>年    月    日</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text54>
        <Text62 Ref="93" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>6.4,3,2.7,0.71</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>a29e61ceebdc483e87152c3b8c525ad7</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text62</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{救助类型}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text62>
        <Text63 Ref="94" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>3.2,9.8,2.65,0.73</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>b81345ae857f4e3d9a8a17c0b996dee9</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text63</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{医疗救助支付}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text63>
        <Text64 Ref="95" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>10.2,9.8,3.25,0.73</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>06c164836f084915a9956fb78b9970ac</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text64</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{截至上次本年医疗救助支付累计}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text64>
        <Text65 Ref="96" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>1.8,12.4,1.52,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>cc8c46f51ec64e41bf0b909a400af783</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text65</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{操作员}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text65>
        <Text29 Ref="97" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>1.8,13.2,1.52,0.51</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,8</Font>
          <Guid>8a333026678e4f04adf70b679202d07b</Guid>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text29</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <Text>{打印日期}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text29>
        <ReportTitleBand1 Ref="98" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,18.8,0.81</ClientRectangle>
          <Components isList="true" count="1">
            <Text1 Ref="99" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>6.82,0,5.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text1</Name>
              <Page isRef="2" />
              <Parent isRef="98" />
              <Text>{名称}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text1>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>ReportTitleBand1</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
        </ReportTitleBand1>
        <HorizontalLinePrimitive1 Ref="100" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>0,2,18.8,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="101" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Name>HorizontalLinePrimitive1</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="102" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive1>
        <VerticalLinePrimitive1 Ref="103" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>0,2,0.0254,10.15</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="104" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>168bd8311aae458bbb9560ebf77ba1ab</Guid>
          <Name>VerticalLinePrimitive1</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="105" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive1>
        <StartPointPrimitive1 Ref="106" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>0,2,0,0</ClientRectangle>
          <Name>StartPointPrimitive1</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>168bd8311aae458bbb9560ebf77ba1ab</ReferenceToGuid>
        </StartPointPrimitive1>
        <EndPointPrimitive1 Ref="107" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>0,12.15,0,0</ClientRectangle>
          <Name>EndPointPrimitive1</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>168bd8311aae458bbb9560ebf77ba1ab</ReferenceToGuid>
        </EndPointPrimitive1>
        <HorizontalLinePrimitive2 Ref="108" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>0,3,18.8,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="109" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Name>HorizontalLinePrimitive2</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="110" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive2>
        <VerticalLinePrimitive2 Ref="111" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>18.8,2,0.0254,10.2</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="112" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>7015fe4a909241b9b0a758edad0cdb8a</Guid>
          <Name>VerticalLinePrimitive2</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="113" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive2>
        <StartPointPrimitive2 Ref="114" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>18.8,2,0,0</ClientRectangle>
          <Name>StartPointPrimitive2</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>7015fe4a909241b9b0a758edad0cdb8a</ReferenceToGuid>
        </StartPointPrimitive2>
        <EndPointPrimitive2 Ref="115" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>18.8,12.2,0,0</ClientRectangle>
          <Name>EndPointPrimitive2</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>7015fe4a909241b9b0a758edad0cdb8a</ReferenceToGuid>
        </EndPointPrimitive2>
        <VerticalLinePrimitive3 Ref="116" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>1.85,2,0.0254,1.75</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="117" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>b19207f24b2f401bae839152c398722d</Guid>
          <Name>VerticalLinePrimitive3</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="118" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive3>
        <StartPointPrimitive3 Ref="119" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>1.85,2,0,0</ClientRectangle>
          <Name>StartPointPrimitive3</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>b19207f24b2f401bae839152c398722d</ReferenceToGuid>
        </StartPointPrimitive3>
        <EndPointPrimitive3 Ref="120" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>1.85,3.75,0,0</ClientRectangle>
          <Name>EndPointPrimitive3</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>b19207f24b2f401bae839152c398722d</ReferenceToGuid>
        </EndPointPrimitive3>
        <VerticalLinePrimitive4 Ref="121" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>5.25,2,0.0254,1.8</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="122" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>449ffca869bf4b919207a70849c66624</Guid>
          <Name>VerticalLinePrimitive4</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="123" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive4>
        <StartPointPrimitive4 Ref="124" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>5.25,2,0,0</ClientRectangle>
          <Name>StartPointPrimitive4</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>449ffca869bf4b919207a70849c66624</ReferenceToGuid>
        </StartPointPrimitive4>
        <EndPointPrimitive4 Ref="125" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>5.25,3.8,0,0</ClientRectangle>
          <Name>EndPointPrimitive4</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>449ffca869bf4b919207a70849c66624</ReferenceToGuid>
        </EndPointPrimitive4>
        <VerticalLinePrimitive5 Ref="126" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>6.4,2,0.0254,1.8</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="127" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>ed90a0714dd94688947e0918f876ecd9</Guid>
          <Name>VerticalLinePrimitive5</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="128" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive5>
        <StartPointPrimitive5 Ref="129" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>6.4,2,0,0</ClientRectangle>
          <Name>StartPointPrimitive5</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>ed90a0714dd94688947e0918f876ecd9</ReferenceToGuid>
        </StartPointPrimitive5>
        <EndPointPrimitive5 Ref="130" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>6.4,3.8,0,0</ClientRectangle>
          <Name>EndPointPrimitive5</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>ed90a0714dd94688947e0918f876ecd9</ReferenceToGuid>
        </EndPointPrimitive5>
        <VerticalLinePrimitive6 Ref="131" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>9.19,2,0.0254,3.4</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="132" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>ae3d668dd285457faf191066f6ad61d6</Guid>
          <Name>VerticalLinePrimitive6</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="133" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive6>
        <StartPointPrimitive6 Ref="134" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>9.19,2,0,0</ClientRectangle>
          <Name>StartPointPrimitive6</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>ae3d668dd285457faf191066f6ad61d6</ReferenceToGuid>
        </StartPointPrimitive6>
        <EndPointPrimitive6 Ref="135" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>9.19,5.4,0,0</ClientRectangle>
          <Name>EndPointPrimitive6</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>ae3d668dd285457faf191066f6ad61d6</ReferenceToGuid>
        </EndPointPrimitive6>
        <VerticalLinePrimitive7 Ref="136" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>10.85,2,0.0254,1</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="137" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>1505fe22d3004949a335cd33818201d1</Guid>
          <Name>VerticalLinePrimitive7</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="138" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive7>
        <StartPointPrimitive7 Ref="139" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>10.85,2,0,0</ClientRectangle>
          <Name>StartPointPrimitive7</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>1505fe22d3004949a335cd33818201d1</ReferenceToGuid>
        </StartPointPrimitive7>
        <EndPointPrimitive7 Ref="140" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>10.85,3,0,0</ClientRectangle>
          <Name>EndPointPrimitive7</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>1505fe22d3004949a335cd33818201d1</ReferenceToGuid>
        </EndPointPrimitive7>
        <VerticalLinePrimitive8 Ref="141" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>12,2,0.0254,1.75</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="142" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>0fb939c850154397ad17f6ae4ca7a17a</Guid>
          <Name>VerticalLinePrimitive8</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="143" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive8>
        <StartPointPrimitive8 Ref="144" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>12,2,0,0</ClientRectangle>
          <Name>StartPointPrimitive8</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>0fb939c850154397ad17f6ae4ca7a17a</ReferenceToGuid>
        </StartPointPrimitive8>
        <EndPointPrimitive8 Ref="145" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>12,3.75,0,0</ClientRectangle>
          <Name>EndPointPrimitive8</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>0fb939c850154397ad17f6ae4ca7a17a</ReferenceToGuid>
        </EndPointPrimitive8>
        <VerticalLinePrimitive9 Ref="146" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>13,2,0.0254,1</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="147" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>b07a7db015d84f8296ff70d4029279e3</Guid>
          <Name>VerticalLinePrimitive9</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="148" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive9>
        <StartPointPrimitive9 Ref="149" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>13,2,0,0</ClientRectangle>
          <Name>StartPointPrimitive9</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>b07a7db015d84f8296ff70d4029279e3</ReferenceToGuid>
        </StartPointPrimitive9>
        <EndPointPrimitive9 Ref="150" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>13,3,0,0</ClientRectangle>
          <Name>EndPointPrimitive9</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>b07a7db015d84f8296ff70d4029279e3</ReferenceToGuid>
        </EndPointPrimitive9>
        <VerticalLinePrimitive10 Ref="151" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>14.15,2,0.0254,1.75</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="152" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>e2cd15a85ff44e9f967f6a8c67967d2a</Guid>
          <Name>VerticalLinePrimitive10</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="153" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive10>
        <StartPointPrimitive10 Ref="154" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>14.15,2,0,0</ClientRectangle>
          <Name>StartPointPrimitive10</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>e2cd15a85ff44e9f967f6a8c67967d2a</ReferenceToGuid>
        </StartPointPrimitive10>
        <EndPointPrimitive10 Ref="155" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>14.15,3.75,0,0</ClientRectangle>
          <Name>EndPointPrimitive10</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>e2cd15a85ff44e9f967f6a8c67967d2a</ReferenceToGuid>
        </EndPointPrimitive10>
        <VerticalLinePrimitive11 Ref="156" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>16.2,2,0.0254,1.75</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="157" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>0e73fea7a5b74f6baf66bfa65241b28b</Guid>
          <Name>VerticalLinePrimitive11</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="158" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive11>
        <StartPointPrimitive11 Ref="159" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>16.2,2,0,0</ClientRectangle>
          <Name>StartPointPrimitive11</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>0e73fea7a5b74f6baf66bfa65241b28b</ReferenceToGuid>
        </StartPointPrimitive11>
        <EndPointPrimitive11 Ref="160" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>16.2,3.75,0,0</ClientRectangle>
          <Name>EndPointPrimitive11</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>0e73fea7a5b74f6baf66bfa65241b28b</ReferenceToGuid>
        </EndPointPrimitive11>
        <HorizontalLinePrimitive3 Ref="161" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>0,3.8,18.8,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="162" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Name>HorizontalLinePrimitive3</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="163" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive3>
        <HorizontalLinePrimitive4 Ref="164" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>0,4.6,18.8,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="165" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Name>HorizontalLinePrimitive4</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="166" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive4>
        <HorizontalLinePrimitive5 Ref="167" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>0,5.4,18.8,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="168" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Name>HorizontalLinePrimitive5</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="169" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive5>
        <VerticalLinePrimitive13 Ref="170" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>7,3.8,0.0254,1.6</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="171" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>c4407cb76e0640e4b3d9431d9b63a75f</Guid>
          <Name>VerticalLinePrimitive13</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="172" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive13>
        <StartPointPrimitive13 Ref="173" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>7,3.8,0,0</ClientRectangle>
          <Name>StartPointPrimitive13</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>c4407cb76e0640e4b3d9431d9b63a75f</ReferenceToGuid>
        </StartPointPrimitive13>
        <EndPointPrimitive13 Ref="174" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>7,5.4,0,0</ClientRectangle>
          <Name>EndPointPrimitive13</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>c4407cb76e0640e4b3d9431d9b63a75f</ReferenceToGuid>
        </EndPointPrimitive13>
        <VerticalLinePrimitive15 Ref="175" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>16,3.8,0.0254,1.6</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="176" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>d3b7e8751da647e8996b87e69c6984bc</Guid>
          <Name>VerticalLinePrimitive15</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="177" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive15>
        <StartPointPrimitive15 Ref="178" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>16,3.8,0,0</ClientRectangle>
          <Name>StartPointPrimitive15</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>d3b7e8751da647e8996b87e69c6984bc</ReferenceToGuid>
        </StartPointPrimitive15>
        <EndPointPrimitive15 Ref="179" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>16,5.4,0,0</ClientRectangle>
          <Name>EndPointPrimitive15</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>d3b7e8751da647e8996b87e69c6984bc</ReferenceToGuid>
        </EndPointPrimitive15>
        <VerticalLinePrimitive18 Ref="180" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>2.7,6.2,0.0254,1.8</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="181" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>9cf8dd166a254d97882a3d33b415aa79</Guid>
          <Name>VerticalLinePrimitive18</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="182" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive18>
        <StartPointPrimitive18 Ref="183" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>2.7,6.2,0,0</ClientRectangle>
          <Name>StartPointPrimitive18</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>9cf8dd166a254d97882a3d33b415aa79</ReferenceToGuid>
        </StartPointPrimitive18>
        <EndPointPrimitive18 Ref="184" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>2.75,8,0,0</ClientRectangle>
          <Name>EndPointPrimitive18</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>9cf8dd166a254d97882a3d33b415aa79</ReferenceToGuid>
        </EndPointPrimitive18>
        <HorizontalLinePrimitive6 Ref="185" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>0,6.2,18.8,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="186" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Name>HorizontalLinePrimitive6</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="187" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive6>
        <HorizontalLinePrimitive7 Ref="188" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>0,7,18.8,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="189" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Name>HorizontalLinePrimitive7</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="190" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive7>
        <VerticalLinePrimitive19 Ref="191" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>8.32,5.4,0.0254,0.8</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="192" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>f13723d5c75f4377a57d4702c1911f6c</Guid>
          <Name>VerticalLinePrimitive19</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="193" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive19>
        <StartPointPrimitive19 Ref="194" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>8.32,5.4,0,0</ClientRectangle>
          <Name>StartPointPrimitive19</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>f13723d5c75f4377a57d4702c1911f6c</ReferenceToGuid>
        </StartPointPrimitive19>
        <EndPointPrimitive19 Ref="195" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>8.32,6.2,0,0</ClientRectangle>
          <Name>EndPointPrimitive19</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>f13723d5c75f4377a57d4702c1911f6c</ReferenceToGuid>
        </EndPointPrimitive19>
        <VerticalLinePrimitive20 Ref="196" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>15.8,5.4,0.0254,0.8</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="197" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>e7865635d5c54bd0a74af05d21f963dc</Guid>
          <Name>VerticalLinePrimitive20</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="198" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive20>
        <StartPointPrimitive20 Ref="199" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>15.8,5.4,0,0</ClientRectangle>
          <Name>StartPointPrimitive20</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>e7865635d5c54bd0a74af05d21f963dc</ReferenceToGuid>
        </StartPointPrimitive20>
        <EndPointPrimitive20 Ref="200" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>15.8,6.2,0,0</ClientRectangle>
          <Name>EndPointPrimitive20</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>e7865635d5c54bd0a74af05d21f963dc</ReferenceToGuid>
        </EndPointPrimitive20>
        <VerticalLinePrimitive22 Ref="201" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>15.1,6.2,0.0254,0.8</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="202" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>3299b54df8bd4ba7b1e8f5c74f58f1e3</Guid>
          <Name>VerticalLinePrimitive22</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="203" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive22>
        <StartPointPrimitive22 Ref="204" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>15.1,6.2,0,0</ClientRectangle>
          <Name>StartPointPrimitive22</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>3299b54df8bd4ba7b1e8f5c74f58f1e3</ReferenceToGuid>
        </StartPointPrimitive22>
        <EndPointPrimitive22 Ref="205" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>15.1,7,0,0</ClientRectangle>
          <Name>EndPointPrimitive22</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>3299b54df8bd4ba7b1e8f5c74f58f1e3</ReferenceToGuid>
        </EndPointPrimitive22>
        <VerticalLinePrimitive28 Ref="206" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>13.65,8,0.0254,3.4</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="207" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>3e3702fbcd254abf88d0b96f69f919a7</Guid>
          <Name>VerticalLinePrimitive28</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="208" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive28>
        <StartPointPrimitive28 Ref="209" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>13.65,8,0,0</ClientRectangle>
          <Name>StartPointPrimitive28</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>3e3702fbcd254abf88d0b96f69f919a7</ReferenceToGuid>
        </StartPointPrimitive28>
        <EndPointPrimitive28 Ref="210" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>13.65,11.4,0,0</ClientRectangle>
          <Name>EndPointPrimitive28</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>3e3702fbcd254abf88d0b96f69f919a7</ReferenceToGuid>
        </EndPointPrimitive28>
        <VerticalLinePrimitive29 Ref="211" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>16.5,8,0.0254,3.4</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="212" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>381a8e32752d4768a5c118b8977b50b0</Guid>
          <Name>VerticalLinePrimitive29</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="213" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive29>
        <StartPointPrimitive29 Ref="214" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>16.5,8,0,0</ClientRectangle>
          <Name>StartPointPrimitive29</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>381a8e32752d4768a5c118b8977b50b0</ReferenceToGuid>
        </StartPointPrimitive29>
        <EndPointPrimitive29 Ref="215" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>16.5,11.4,0,0</ClientRectangle>
          <Name>EndPointPrimitive29</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>381a8e32752d4768a5c118b8977b50b0</ReferenceToGuid>
        </EndPointPrimitive29>
        <HorizontalLinePrimitive13 Ref="216" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>0,8,18.8,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="217" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Name>HorizontalLinePrimitive13</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="218" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive13>
        <VerticalLinePrimitive30 Ref="219" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>3.15,8,0.0254,4.2</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="220" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>b32f914d85b14e6c85c4a840d3ea7181</Guid>
          <Name>VerticalLinePrimitive30</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="221" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive30>
        <StartPointPrimitive30 Ref="222" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>3.15,8,0,0</ClientRectangle>
          <Name>StartPointPrimitive30</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>b32f914d85b14e6c85c4a840d3ea7181</ReferenceToGuid>
        </StartPointPrimitive30>
        <EndPointPrimitive30 Ref="223" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>3.15,12.2,0,0</ClientRectangle>
          <Name>EndPointPrimitive30</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>b32f914d85b14e6c85c4a840d3ea7181</ReferenceToGuid>
        </EndPointPrimitive30>
        <VerticalLinePrimitive31 Ref="224" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>6,8,0.0254,2.6</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="225" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>0d32260479254158972e7997b567dcf1</Guid>
          <Name>VerticalLinePrimitive31</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="226" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive31>
        <StartPointPrimitive31 Ref="227" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>6,8,0,0</ClientRectangle>
          <Name>StartPointPrimitive31</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>0d32260479254158972e7997b567dcf1</ReferenceToGuid>
        </StartPointPrimitive31>
        <EndPointPrimitive31 Ref="228" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>6,10.6,0,0</ClientRectangle>
          <Name>EndPointPrimitive31</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>0d32260479254158972e7997b567dcf1</ReferenceToGuid>
        </EndPointPrimitive31>
        <HorizontalLinePrimitive14 Ref="229" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>0,9.8,13.65,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="230" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Name>HorizontalLinePrimitive14</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="231" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive14>
        <HorizontalLinePrimitive15 Ref="232" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>3.15,9,10.5,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="233" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Name>HorizontalLinePrimitive15</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="234" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive15>
        <VerticalLinePrimitive32 Ref="235" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>8.4,8,0.0254,1.8</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="236" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>27435a4824ac42a3b817c15a7e767974</Guid>
          <Name>VerticalLinePrimitive32</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="237" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive32>
        <StartPointPrimitive32 Ref="238" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>8.4,8,0,0</ClientRectangle>
          <Name>StartPointPrimitive32</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>27435a4824ac42a3b817c15a7e767974</ReferenceToGuid>
        </StartPointPrimitive32>
        <EndPointPrimitive32 Ref="239" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>8.4,9.8,0,0</ClientRectangle>
          <Name>EndPointPrimitive32</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>27435a4824ac42a3b817c15a7e767974</ReferenceToGuid>
        </EndPointPrimitive32>
        <VerticalLinePrimitive33 Ref="240" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>11.25,8,0.0254,1.8</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="241" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>87c839aa4fa545548f09aa6aa912d5b4</Guid>
          <Name>VerticalLinePrimitive33</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="242" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive33>
        <StartPointPrimitive33 Ref="243" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>11.25,8,0,0</ClientRectangle>
          <Name>StartPointPrimitive33</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>87c839aa4fa545548f09aa6aa912d5b4</ReferenceToGuid>
        </StartPointPrimitive33>
        <EndPointPrimitive33 Ref="244" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>11.25,9.8,0,0</ClientRectangle>
          <Name>EndPointPrimitive33</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>87c839aa4fa545548f09aa6aa912d5b4</ReferenceToGuid>
        </EndPointPrimitive33>
        <HorizontalLinePrimitive16 Ref="245" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>0,10.6,13.65,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="246" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Name>HorizontalLinePrimitive16</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="247" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive16>
        <HorizontalLinePrimitive17 Ref="248" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>0,11.4,18.8,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="249" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Name>HorizontalLinePrimitive17</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="250" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive17>
        <VerticalLinePrimitive34 Ref="251" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>14.55,8,0.0254,3.4</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="252" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>218086df90ba4af98cd0269004dded90</Guid>
          <Name>VerticalLinePrimitive34</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="253" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive34>
        <StartPointPrimitive34 Ref="254" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>14.55,8,0,0</ClientRectangle>
          <Name>StartPointPrimitive34</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>218086df90ba4af98cd0269004dded90</ReferenceToGuid>
        </StartPointPrimitive34>
        <EndPointPrimitive34 Ref="255" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>14.55,11.4,0,0</ClientRectangle>
          <Name>EndPointPrimitive34</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>218086df90ba4af98cd0269004dded90</ReferenceToGuid>
        </EndPointPrimitive34>
        <HorizontalLinePrimitive18 Ref="256" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>14.55,9,4.25,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="257" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Name>HorizontalLinePrimitive18</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="258" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive18>
        <HorizontalLinePrimitive19 Ref="259" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>14.55,9.8,4.25,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="260" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>406b448fd36e4cf88321b700ccb79ae8</Guid>
          <Name>HorizontalLinePrimitive19</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="261" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive19>
        <HorizontalLinePrimitive20 Ref="262" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>14.55,10.6,4.25,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="263" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>7d76bfc7faa5495d8f809173b39d7e49</Guid>
          <Name>HorizontalLinePrimitive20</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="264" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive20>
        <VerticalLinePrimitive35 Ref="265" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>2.5,13.6,0.0254,0</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="266" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>4c653c5dd66d4aa19afafdce7f84c4b9</Guid>
          <Name>VerticalLinePrimitive35</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="267" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive35>
        <StartPointPrimitive35 Ref="268" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>2.5,13.6,0,0</ClientRectangle>
          <Name>StartPointPrimitive35</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>4c653c5dd66d4aa19afafdce7f84c4b9</ReferenceToGuid>
        </StartPointPrimitive35>
        <EndPointPrimitive35 Ref="269" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>2.5,13.6,0,0</ClientRectangle>
          <Name>EndPointPrimitive35</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>4c653c5dd66d4aa19afafdce7f84c4b9</ReferenceToGuid>
        </EndPointPrimitive35>
        <VerticalLinePrimitive36 Ref="270" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>4.8,13.6,0.0254,0</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="271" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>7e2e45b4062d4ed78a812db150a75014</Guid>
          <Name>VerticalLinePrimitive36</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="272" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive36>
        <StartPointPrimitive36 Ref="273" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>4.8,13.6,0,0</ClientRectangle>
          <Name>StartPointPrimitive36</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>7e2e45b4062d4ed78a812db150a75014</ReferenceToGuid>
        </StartPointPrimitive36>
        <EndPointPrimitive36 Ref="274" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>4.8,13.6,0,0</ClientRectangle>
          <Name>EndPointPrimitive36</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>7e2e45b4062d4ed78a812db150a75014</ReferenceToGuid>
        </EndPointPrimitive36>
        <VerticalLinePrimitive37 Ref="275" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>7.7,13.6,0.0254,0</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="276" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>a61bcef5d216463aab3237e85920e9be</Guid>
          <Name>VerticalLinePrimitive37</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="277" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive37>
        <StartPointPrimitive37 Ref="278" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>7.7,13.6,0,0</ClientRectangle>
          <Name>StartPointPrimitive37</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>a61bcef5d216463aab3237e85920e9be</ReferenceToGuid>
        </StartPointPrimitive37>
        <EndPointPrimitive37 Ref="279" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>7.7,13.6,0,0</ClientRectangle>
          <Name>EndPointPrimitive37</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>a61bcef5d216463aab3237e85920e9be</ReferenceToGuid>
        </EndPointPrimitive37>
        <VerticalLinePrimitive38 Ref="280" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>11.7,13.6,0.0254,0</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="281" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>baef33ad8bea452e81336926b0ef14b6</Guid>
          <Name>VerticalLinePrimitive38</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="282" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive38>
        <StartPointPrimitive38 Ref="283" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>11.7,13.6,0,0</ClientRectangle>
          <Name>StartPointPrimitive38</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>baef33ad8bea452e81336926b0ef14b6</ReferenceToGuid>
        </StartPointPrimitive38>
        <EndPointPrimitive38 Ref="284" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>11.7,13.6,0,0</ClientRectangle>
          <Name>EndPointPrimitive38</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>baef33ad8bea452e81336926b0ef14b6</ReferenceToGuid>
        </EndPointPrimitive38>
        <VerticalLinePrimitive39 Ref="285" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>13.7,13.6,0.0254,0</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="286" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>989a24d07e3a44858c0f86a173df90a7</Guid>
          <Name>VerticalLinePrimitive39</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="287" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive39>
        <StartPointPrimitive39 Ref="288" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>13.7,13.6,0,0</ClientRectangle>
          <Name>StartPointPrimitive39</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>989a24d07e3a44858c0f86a173df90a7</ReferenceToGuid>
        </StartPointPrimitive39>
        <EndPointPrimitive39 Ref="289" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>13.7,13.6,0,0</ClientRectangle>
          <Name>EndPointPrimitive39</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>989a24d07e3a44858c0f86a173df90a7</ReferenceToGuid>
        </EndPointPrimitive39>
        <VerticalLinePrimitive12 Ref="290" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>5.8,6.2,0.0254,1.8</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="291" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>f6a989cb5c6242e187f902f3525c3d09</Guid>
          <Name>VerticalLinePrimitive12</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="292" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive12>
        <StartPointPrimitive12 Ref="293" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>5.8,6.2,0,0</ClientRectangle>
          <Name>StartPointPrimitive12</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>f6a989cb5c6242e187f902f3525c3d09</ReferenceToGuid>
        </StartPointPrimitive12>
        <EndPointPrimitive12 Ref="294" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>5.8,8,0,0</ClientRectangle>
          <Name>EndPointPrimitive12</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>f6a989cb5c6242e187f902f3525c3d09</ReferenceToGuid>
        </EndPointPrimitive12>
        <VerticalLinePrimitive14 Ref="295" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>1,5.4,0.0254,0.8</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="296" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>c4ea73ee0e5649c4bb844b80ee77fb12</Guid>
          <Name>VerticalLinePrimitive14</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="297" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive14>
        <StartPointPrimitive14 Ref="298" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>1,5.4,0,0</ClientRectangle>
          <Name>StartPointPrimitive14</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>c4ea73ee0e5649c4bb844b80ee77fb12</ReferenceToGuid>
        </StartPointPrimitive14>
        <EndPointPrimitive14 Ref="299" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>1,6.2,0,0</ClientRectangle>
          <Name>EndPointPrimitive14</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>c4ea73ee0e5649c4bb844b80ee77fb12</ReferenceToGuid>
        </EndPointPrimitive14>
        <VerticalLinePrimitive16 Ref="300" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>2.7,5.4,0.0254,0.8</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="301" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>d01a834546d74326b4d456ea3d53eed9</Guid>
          <Name>VerticalLinePrimitive16</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="302" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive16>
        <StartPointPrimitive16 Ref="303" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>2.7,5.4,0,0</ClientRectangle>
          <Name>StartPointPrimitive16</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>d01a834546d74326b4d456ea3d53eed9</ReferenceToGuid>
        </StartPointPrimitive16>
        <EndPointPrimitive16 Ref="304" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>2.7,6.2,0,0</ClientRectangle>
          <Name>EndPointPrimitive16</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>d01a834546d74326b4d456ea3d53eed9</ReferenceToGuid>
        </EndPointPrimitive16>
        <VerticalLinePrimitive17 Ref="305" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>3.9,5.4,0.0254,0.8</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="306" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>9edc945347504bcaa32ff715b896c74d</Guid>
          <Name>VerticalLinePrimitive17</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="307" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive17>
        <StartPointPrimitive17 Ref="308" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>3.9,5.4,0,0</ClientRectangle>
          <Name>StartPointPrimitive17</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>9edc945347504bcaa32ff715b896c74d</ReferenceToGuid>
        </StartPointPrimitive17>
        <EndPointPrimitive17 Ref="309" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>3.9,6.2,0,0</ClientRectangle>
          <Name>EndPointPrimitive17</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>9edc945347504bcaa32ff715b896c74d</ReferenceToGuid>
        </EndPointPrimitive17>
        <VerticalLinePrimitive23 Ref="310" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>5.6,5.4,0.0254,0.8</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="311" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>b72598e273c34ed6b81986864c82de49</Guid>
          <Name>VerticalLinePrimitive23</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="312" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive23>
        <StartPointPrimitive23 Ref="313" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>5.6,5.4,0,0</ClientRectangle>
          <Name>StartPointPrimitive23</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>b72598e273c34ed6b81986864c82de49</ReferenceToGuid>
        </StartPointPrimitive23>
        <EndPointPrimitive23 Ref="314" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>5.6,6.2,0,0</ClientRectangle>
          <Name>EndPointPrimitive23</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>b72598e273c34ed6b81986864c82de49</ReferenceToGuid>
        </EndPointPrimitive23>
        <VerticalLinePrimitive24 Ref="315" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>9.7,5.4,0.0254,0.8</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="316" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>4a2b83b5267f4f7c9efef2a8490d8723</Guid>
          <Name>VerticalLinePrimitive24</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="317" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive24>
        <StartPointPrimitive24 Ref="318" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>9.7,5.4,0,0</ClientRectangle>
          <Name>StartPointPrimitive24</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>4a2b83b5267f4f7c9efef2a8490d8723</ReferenceToGuid>
        </StartPointPrimitive24>
        <EndPointPrimitive24 Ref="319" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>9.7,6.2,0,0</ClientRectangle>
          <Name>EndPointPrimitive24</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>4a2b83b5267f4f7c9efef2a8490d8723</ReferenceToGuid>
        </EndPointPrimitive24>
        <VerticalLinePrimitive25 Ref="320" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>12.4,5.4,0.0254,1.55</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="321" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>a0ea9dee88514c20b84ea51453eede6b</Guid>
          <Name>VerticalLinePrimitive25</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="322" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive25>
        <StartPointPrimitive25 Ref="323" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>12.4,5.4,0,0</ClientRectangle>
          <Name>StartPointPrimitive25</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>a0ea9dee88514c20b84ea51453eede6b</ReferenceToGuid>
        </StartPointPrimitive25>
        <EndPointPrimitive25 Ref="324" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>12.4,6.95,0,0</ClientRectangle>
          <Name>EndPointPrimitive25</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>a0ea9dee88514c20b84ea51453eede6b</ReferenceToGuid>
        </EndPointPrimitive25>
        <VerticalLinePrimitive26 Ref="325" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>14.05,5.4,0.0254,0.8</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="326" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>a119e7c0fc1646b3b5541f4d86e80293</Guid>
          <Name>VerticalLinePrimitive26</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="327" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive26>
        <StartPointPrimitive26 Ref="328" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>14.05,5.4,0,0</ClientRectangle>
          <Name>StartPointPrimitive26</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>a119e7c0fc1646b3b5541f4d86e80293</ReferenceToGuid>
        </StartPointPrimitive26>
        <EndPointPrimitive26 Ref="329" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>14.05,6.2,0,0</ClientRectangle>
          <Name>EndPointPrimitive26</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>a119e7c0fc1646b3b5541f4d86e80293</ReferenceToGuid>
        </EndPointPrimitive26>
        <VerticalLinePrimitive40 Ref="330" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>17.2,5.4,0.0254,0.8</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="331" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>17851a4d810d49f7abb94283fed73d14</Guid>
          <Name>VerticalLinePrimitive40</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="332" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive40>
        <StartPointPrimitive40 Ref="333" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>17.2,5.4,0,0</ClientRectangle>
          <Name>StartPointPrimitive40</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>17851a4d810d49f7abb94283fed73d14</ReferenceToGuid>
        </StartPointPrimitive40>
        <EndPointPrimitive40 Ref="334" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>17.2,6.2,0,0</ClientRectangle>
          <Name>EndPointPrimitive40</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>17851a4d810d49f7abb94283fed73d14</ReferenceToGuid>
        </EndPointPrimitive40>
        <VerticalLinePrimitive41 Ref="335" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>10.8,6.2,0.0254,0.8</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="336" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>df6ef1394a894348bfdd1018928cae07</Guid>
          <Name>VerticalLinePrimitive41</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="337" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive41>
        <StartPointPrimitive41 Ref="338" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>10.8,6.2,0,0</ClientRectangle>
          <Name>StartPointPrimitive41</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>df6ef1394a894348bfdd1018928cae07</ReferenceToGuid>
        </StartPointPrimitive41>
        <EndPointPrimitive41 Ref="339" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>10.8,7,0,0</ClientRectangle>
          <Name>EndPointPrimitive41</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>df6ef1394a894348bfdd1018928cae07</ReferenceToGuid>
        </EndPointPrimitive41>
        <VerticalLinePrimitive21 Ref="340" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>11.01,5.4,0.0254,0.8</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="341" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>2383e41f42de41db84c3c91aeb3bb97a</Guid>
          <Name>VerticalLinePrimitive21</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="342" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive21>
        <StartPointPrimitive21 Ref="343" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>11.01,5.4,0,0</ClientRectangle>
          <Name>StartPointPrimitive21</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>2383e41f42de41db84c3c91aeb3bb97a</ReferenceToGuid>
        </StartPointPrimitive21>
        <EndPointPrimitive21 Ref="344" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>11.01,6.2,0,0</ClientRectangle>
          <Name>EndPointPrimitive21</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>2383e41f42de41db84c3c91aeb3bb97a</ReferenceToGuid>
        </EndPointPrimitive21>
        <HorizontalLinePrimitive8 Ref="345" type="HorizontalLinePrimitive" isKey="true">
          <ClientRectangle>0,12.2,18.8,0.0254</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="346" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>c186fea575a441698584db993b801def</Guid>
          <Name>HorizontalLinePrimitive8</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="347" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </HorizontalLinePrimitive8>
        <VerticalLinePrimitive27 Ref="348" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>11.2,10.6,0.0254,0.8</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="349" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>cf0bcb6dacd04a6198a387eaf3a5d848</Guid>
          <Name>VerticalLinePrimitive27</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="350" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive27>
        <StartPointPrimitive27 Ref="351" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>11.2,10.6,0,0</ClientRectangle>
          <Name>StartPointPrimitive27</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>cf0bcb6dacd04a6198a387eaf3a5d848</ReferenceToGuid>
        </StartPointPrimitive27>
        <EndPointPrimitive27 Ref="352" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>11.2,11.4,0,0</ClientRectangle>
          <Name>EndPointPrimitive27</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>cf0bcb6dacd04a6198a387eaf3a5d848</ReferenceToGuid>
        </EndPointPrimitive27>
        <VerticalLinePrimitive42 Ref="353" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>8.4,10.6,0.0254,0.8</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="354" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>c41cbda60aa94002be12f541c6c8a23d</Guid>
          <Name>VerticalLinePrimitive42</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="355" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive42>
        <StartPointPrimitive42 Ref="356" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>8.4,10.6,0,0</ClientRectangle>
          <Name>StartPointPrimitive42</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>c41cbda60aa94002be12f541c6c8a23d</ReferenceToGuid>
        </StartPointPrimitive42>
        <EndPointPrimitive42 Ref="357" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>8.4,11.4,0,0</ClientRectangle>
          <Name>EndPointPrimitive42</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>c41cbda60aa94002be12f541c6c8a23d</ReferenceToGuid>
        </EndPointPrimitive42>
        <VerticalLinePrimitive43 Ref="358" type="VerticalLinePrimitive" isKey="true">
          <ClientRectangle>10.2,9.8,0.0254,0.8</ClientRectangle>
          <Color>Black</Color>
          <EndCap Ref="359" type="Cap" isKey="true">
            <Color>Black</Color>
          </EndCap>
          <Guid>fa6f5bc9a4194cba8c9afc19675275b5</Guid>
          <Name>VerticalLinePrimitive43</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <StartCap Ref="360" type="Cap" isKey="true">
            <Color>Black</Color>
          </StartCap>
        </VerticalLinePrimitive43>
        <StartPointPrimitive43 Ref="361" type="Stimulsoft.Report.Components.StiStartPointPrimitive" isKey="true">
          <ClientRectangle>10.2,9.8,0,0</ClientRectangle>
          <Name>StartPointPrimitive43</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>fa6f5bc9a4194cba8c9afc19675275b5</ReferenceToGuid>
        </StartPointPrimitive43>
        <EndPointPrimitive43 Ref="362" type="Stimulsoft.Report.Components.StiEndPointPrimitive" isKey="true">
          <ClientRectangle>10.2,10.6,0,0</ClientRectangle>
          <Name>EndPointPrimitive43</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
          <ReferenceToGuid>fa6f5bc9a4194cba8c9afc19675275b5</ReferenceToGuid>
        </EndPointPrimitive43>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>a602b0ce0a8b4f73b56e7924e9f50042</Guid>
      <Margins>1.1,1.1,1,1</Margins>
      <Name>Page1</Name>
      <PageHeight>29.7</PageHeight>
      <PageWidth>21</PageWidth>
      <Report isRef="0" />
      <Watermark Ref="363" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page1>
  </Pages>
  <PrinterSettings Ref="364" type="Stimulsoft.Report.Print.StiPrinterSettings" isKey="true" />
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>Report</ReportAlias>
  <ReportChanged>12/31/2020 9:45:27 AM</ReportChanged>
  <ReportCreated>5/22/2019 2:37:22 PM</ReportCreated>
  <ReportFile>C:\Users\<USER>\Desktop\通辽市医疗保险门诊费用结算单.mrt</ReportFile>
  <ReportGuid>ff5386be996f48848ac525823baf891c</ReportGuid>
  <ReportName>Report</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2011.2.1026</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class Report : Stimulsoft.Report.StiReport
    {
        public Report()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
        #endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>