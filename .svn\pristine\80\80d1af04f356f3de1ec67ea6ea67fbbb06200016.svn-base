﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="0" />
    <DataSources isList="true" count="1">
      <催款单 Ref="2" type="DataTableSource" isKey="true">
        <Alias>催款单</Alias>
        <Columns isList="true" count="27">
          <value>Bl_Code,System.String</value>
          <value>Ry_YlCode,System.String</value>
          <value>Bxlb_Name,System.String</value>
          <value>Bq_Name,System.String</value>
          <value>Bc_Name,System.String</value>
          <value>Ry_Name,System.String</value>
          <value>Ry_Jc,System.String</value>
          <value>Ry_Sex,System.String</value>
          <value>Ry_Sfzh,System.String</value>
          <value>Ry_BlCode,System.String</value>
          <value>Ry_Csdate,System.DateTime</value>
          <value>Ry_Address,System.String</value>
          <value>Ks_Name,System.String</value>
          <value>Ry_RyDate,System.DateTime</value>
          <value>Jsr_Code,System.String</value>
          <value>Ry_Memo,System.String</value>
          <value>Bxlb_Code,System.String</value>
          <value>Jb_Name,System.String</value>
          <value>Ry_Age,System.Int32</value>
          <value>Ry_BlCode1,System.String</value>
          <value>Jf_Money,System.Decimal</value>
          <value>Xf_YpMoney,System.Decimal</value>
          <value>Xf_XmMoney,System.Decimal</value>
          <value>Xf_Money,System.Decimal</value>
          <value>New_Money,System.Decimal</value>
          <value>Jb_Code,System.String</value>
          <value>Ts,System.Int32</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>催款单</Name>
        <NameInSource>催款单</NameInSource>
      </催款单>
    </DataSources>
    <Relations isList="true" count="0" />
    <Report isRef="0" />
    <Variables isList="true" count="2">
      <value>,打单人,打单人,System.String,,False,False</value>
      <value>,打印时间,打印时间,System.String,,False,False</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="3" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="32">
        <Text16 Ref="4" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>8.6,6.6,6.4,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,12,Regular,Point,False,0</Font>
          <Guid>914dbee3313b4122a304210ba34ea268</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text16</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Text>打印时间：{打印时间}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
        </Text16>
        <Text2 Ref="5" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>3.1,6.6,5.5,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,12,Regular,Point,False,0</Font>
          <Margins>0,0,0,0</Margins>
          <Name>Text2</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Text>打单人：{打单人}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
        </Text2>
        <Text3 Ref="6" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>3.2,4.9,11.6,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,12,Regular,Point,False,0</Font>
          <Guid>6ed02308b61143fa8debf1917c8d8615</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text3</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Text>为了您的治疗能及时有效的进行，请速来缴费。谢谢！</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
        </Text3>
        <Text33 Ref="7" type="Text" isKey="true">
          <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>2,3.2,2.3,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,12,Regular,Point,False,0</Font>
          <Guid>a69e5a362570490e907e701ffaacb6c2</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text33</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Text>住院金额：</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text33>
        <Text34 Ref="8" type="Text" isKey="true">
          <Border>Top, Bottom;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>4.3,3.2,4.2,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,12,Regular,Point,False,0</Font>
          <Guid>0cda5ba78f824275a61a83addf0de709</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text34</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Text>{催款单.Xf_Money}</Text>
          <TextBrush>Black</TextBrush>
          <TextFormat Ref="9" type="CustomFormat" isKey="true">
            <StringFormat>0.00####</StringFormat>
          </TextFormat>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text34>
        <Text35 Ref="10" type="Text" isKey="true">
          <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>9.1,3.2,2.3,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,12,Regular,Point,False,0</Font>
          <Guid>8800185ec4ec46739921983048ed675f</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text35</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Text>已交押金：</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text35>
        <Text36 Ref="11" type="Text" isKey="true">
          <Border>Top, Bottom;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>11.4,3.2,4.8,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,12,Regular,Point,False,0</Font>
          <Guid>8f47b3cce93b44788097cbe5ff69fce9</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text36</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Text>{催款单.Jf_Money}</Text>
          <TextBrush>Black</TextBrush>
          <TextFormat Ref="12" type="CustomFormat" isKey="true">
            <StringFormat>0.00####</StringFormat>
          </TextFormat>
          <Type>DataColumn</Type>
          <VertAlignment>Center</VertAlignment>
        </Text36>
        <Text37 Ref="13" type="Text" isKey="true">
          <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>2,3.8,2.3,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,12,Regular,Point,False,0</Font>
          <Guid>d8d3a2dda28749169d4839f920f3c696</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text37</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Text>剩余押金：</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text37>
        <Text38 Ref="14" type="Text" isKey="true">
          <Border>Top, Bottom;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>4.3,3.8,4.2,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,12,Regular,Point,False,0</Font>
          <Guid>eb9f2a10b76b45b2937205618c2f88a1</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text38</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Text>{催款单.New_Money}</Text>
          <TextBrush>Black</TextBrush>
          <TextFormat Ref="15" type="CustomFormat" isKey="true">
            <StringFormat>0.00####</StringFormat>
          </TextFormat>
          <Type>DataColumn</Type>
          <VertAlignment>Center</VertAlignment>
        </Text38>
        <Text39 Ref="16" type="Text" isKey="true">
          <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>9.1,3.8,2.3,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,12,Regular,Point,False,0</Font>
          <Guid>4bb6908480564bf8bca23a20039456a9</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text39</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Text>需要补交：</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text39>
        <Text40 Ref="17" type="Text" isKey="true">
          <Border>Top, Bottom;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>11.4,3.8,4.8,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,12,Regular,Point,False,0</Font>
          <Guid>d56cdd9851bd4edb85a86cd191251593</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text40</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <TextBrush>Red</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text40>
        <Text41 Ref="18" type="Text" isKey="true">
          <Border>Top, Right, Bottom;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>16.2,3.8,0.7,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,12,Regular,Point,False,0</Font>
          <Guid>2d50d61b7bc44a4c913366634353ac4b</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text41</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Text>元</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text41>
        <Text42 Ref="19" type="Text" isKey="true">
          <Border>Top, Right, Bottom;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>8.5,3.8,0.6,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,12,Regular,Point,False,0</Font>
          <Guid>9445904a26fd4599a72b840d916320e5</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text42</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Text>元</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text42>
        <Text17 Ref="20" type="Text" isKey="true">
          <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>2,0.8,2.3,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,12,Regular,Point,False,0</Font>
          <Guid>2b4a8be1d2a84bf7a90861857f7d6a34</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text17</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Text>患者姓名：</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text17>
        <Text18 Ref="21" type="Text" isKey="true">
          <Border>Top, Right, Bottom;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>4.3,0.8,4.8,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,12,Regular,Point,False,0</Font>
          <Guid>932c2d08674b4f7e8cc8128b97a9442a</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text18</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Text>{催款单.Ry_Name}</Text>
          <TextBrush>Black</TextBrush>
          <Type>DataColumn</Type>
          <VertAlignment>Center</VertAlignment>
        </Text18>
        <Text19 Ref="22" type="Text" isKey="true">
          <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>9.1,0.8,2.3,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,12,Regular,Point,False,0</Font>
          <Guid>1df73cef134f4bf5a35cf0025410deed</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text19</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Text>性        别：</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text19>
        <Text20 Ref="23" type="Text" isKey="true">
          <Border>Top, Right, Bottom;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>11.4,0.8,5.5,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,12,Regular,Point,False,0</Font>
          <Guid>4516054e903744c0ba16ed7a7576442c</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text20</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Text>{催款单.Ry_Sex}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text20>
        <Text21 Ref="24" type="Text" isKey="true">
          <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>9.1,1.4,2.3,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,12,Regular,Point,False,0</Font>
          <Guid>d7119efed8784a2eb4196836beeec3ba</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text21</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Text>入院时间：</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text21>
        <Text22 Ref="25" type="Text" isKey="true">
          <Border>Top, Right, Bottom;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>11.4,1.4,5.5,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,12,Regular,Point,False,0</Font>
          <Guid>da2b5d223f3d41baa0e6612ea5732bd0</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text22</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Text>{催款单.Ry_RyDate}</Text>
          <TextBrush>Black</TextBrush>
          <Type>DataColumn</Type>
          <VertAlignment>Center</VertAlignment>
        </Text22>
        <Text23 Ref="26" type="Text" isKey="true">
          <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>2,1.4,2.3,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,12,Regular,Point,False,0</Font>
          <Guid>f6d6acbdddab43aab24f1eb2c7cb6350</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text23</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Text>病  历  号：</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text23>
        <Text24 Ref="27" type="Text" isKey="true">
          <Border>Top, Right, Bottom;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>4.3,1.4,4.8,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,12,Regular,Point,False,0</Font>
          <Guid>d0be7212a48540268a988222558b6724</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text24</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Text>{催款单.Ry_BlCode}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text24>
        <Text25 Ref="28" type="Text" isKey="true">
          <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>9.1,2,2.3,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,12,Regular,Point,False,0</Font>
          <Guid>791a3e47f3734fcc9f2c9e81e3c51512</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text25</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Text>科        室：</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text25>
        <Text27 Ref="29" type="Text" isKey="true">
          <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>2,2,2.3,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,12,Regular,Point,False,0</Font>
          <Guid>33e64d5521614de196c4569e75b43a71</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text27</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Text>病人类别：</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text27>
        <Text28 Ref="30" type="Text" isKey="true">
          <Border>Top, Right, Bottom;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>4.3,2,4.8,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,12,Regular,Point,False,0</Font>
          <Guid>d6235fba7ef042eda9ca188e8fbddd9a</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text28</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Text>{催款单.Bxlb_Name}</Text>
          <TextBrush>Black</TextBrush>
          <Type>DataColumn</Type>
          <VertAlignment>Center</VertAlignment>
        </Text28>
        <Text8 Ref="31" type="Text" isKey="true">
          <Border>Top, Right, Bottom;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>11.4,2,5.5,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,12,Regular,Point,False,0</Font>
          <Guid>9bf1e1b5e17f4edeafafb6903a55ae91</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text8</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Text>{催款单.Ks_Name}</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text8>
        <Text1 Ref="32" type="Text" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0,19,0.7</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,15,Bold,Point,False,0</Font>
          <HorAlignment>Center</HorAlignment>
          <Margins>0,0,0,0</Margins>
          <Name>Text1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Text>催 款 单</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text1>
        <Text4 Ref="33" type="Text" isKey="true">
          <Border>Top, Right, Bottom;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>8.5,3.2,0.6,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,12,Regular,Point,False,0</Font>
          <Guid>7e538fc012294581840764065c5cb3b5</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text4</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Text>元</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text4>
        <Text5 Ref="34" type="Text" isKey="true">
          <Border>Top, Right, Bottom;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>16.2,3.2,0.7,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,12,Regular,Point,False,0</Font>
          <Guid>107ac480312e4e4da45395ac5fd4d6c7</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text5</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Text>元</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text5>
        <Text6 Ref="35" type="Text" isKey="true">
          <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>9.1,2.6,2.3,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,12,Regular,Point,False,0</Font>
          <Guid>5dc1a057260f4e938d109db643dcbe26</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text6</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Text>病        床：</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text6>
        <Text7 Ref="36" type="Text" isKey="true">
          <Border>Top, Right, Bottom;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>11.4,2.6,5.5,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,12,Regular,Point,False,0</Font>
          <Guid>ceea35903d8d4542becfb2a18e84a4a2</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text7</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Text>{催款单.Bc_Name}</Text>
          <TextBrush>Black</TextBrush>
          <Type>DataColumn</Type>
          <VertAlignment>Center</VertAlignment>
        </Text7>
        <Text9 Ref="37" type="Text" isKey="true">
          <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>2,2.6,2.3,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,12,Regular,Point,False,0</Font>
          <Guid>6981c627163744c4ae2f64d1918785e3</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text9</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Text>病       区：</Text>
          <TextBrush>Black</TextBrush>
          <Type>Expression</Type>
          <VertAlignment>Center</VertAlignment>
        </Text9>
        <Text10 Ref="38" type="Text" isKey="true">
          <Border>Top, Right, Bottom;Black;1;Solid;False;4;Black</Border>
          <Brush>Transparent</Brush>
          <ClientRectangle>4.3,2.6,4.8,0.6</ClientRectangle>
          <Conditions isList="true" count="0" />
          <Font>Arial,12,Regular,Point,False,0</Font>
          <Guid>b81ae852547f4a02b8e025b37bd01929</Guid>
          <Margins>0,0,0,0</Margins>
          <Name>Text10</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Text>{催款单.Bq_Name}</Text>
          <TextBrush>Black</TextBrush>
          <Type>DataColumn</Type>
          <VertAlignment>Center</VertAlignment>
        </Text10>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>77c73957ba6d40769b606b8c85438f05</Guid>
      <Margins>1,1,1,1</Margins>
      <Name>Page1</Name>
      <PageHeight>9.3</PageHeight>
      <PageWidth>21</PageWidth>
      <Report isRef="0" />
      <Watermark Ref="39" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page1>
  </Pages>
  <PrinterSettings Ref="40" type="Stimulsoft.Report.Print.StiPrinterSettings" isKey="true" />
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>催款单</ReportAlias>
  <ReportChanged>6/18/2014 9:28:47 AM</ReportChanged>
  <ReportCreated>8/1/2012 11:17:25 AM</ReportCreated>
  <ReportFile>E:\正在进行时\唐山\正在修改版\his2010\his2010\Rpt\催款单.mrt</ReportFile>
  <ReportGuid>fb4b4172356e4b44ac62f77f7aad500c</ReportGuid>
  <ReportName>催款单</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2011.2.1026</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class Report : Stimulsoft.Report.StiReport
    {
        public Report()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
        #endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>