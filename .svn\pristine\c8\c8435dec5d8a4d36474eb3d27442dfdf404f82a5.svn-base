﻿/**  版本信息模板在安装目录下，可自行修改。
* M_Materials_Class_Dict.cs
*
* 功 能： N/A
* 类 名： M_Materials_Class_Dict
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016-11-26 11:32:26   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace ModelOld
{
	/// <summary>
	/// 物资类别
	/// </summary>
	[Serializable]
	public partial class M_Materials_Class_Dict
	{
		public M_Materials_Class_Dict()
		{}
		#region Model
		private string _class_code;
		private string _class_name;
		private string _class_py;
		private string _class_wb;
		private string _class_father;
		private bool _havechild;
		private int? _serial_no;
		/// <summary>
		/// 编码
		/// </summary>
		public string Class_Code
		{
			set{ _class_code=value;}
			get{return _class_code;}
		}
		/// <summary>
		/// 名称
		/// </summary>
		public string Class_Name
		{
			set{ _class_name=value;}
			get{return _class_name;}
		}
		/// <summary>
		/// 拼音
		/// </summary>
		public string Class_Py
		{
			set{ _class_py=value;}
			get{return _class_py;}
		}
		/// <summary>
		/// 五笔
		/// </summary>
		public string Class_Wb
		{
			set{ _class_wb=value;}
			get{return _class_wb;}
		}
		/// <summary>
		/// 顶级为00000
		/// </summary>
		public string Class_Father
		{
			set{ _class_father=value;}
			get{return _class_father;}
		}
		/// <summary>
		/// 是否拥有下级
		/// </summary>
		public bool HaveChild
		{
			set{ _havechild=value;}
			get{return _havechild;}
		}
		/// <summary>
		/// 排列顺序
		/// </summary>
		public int? Serial_No
		{
			set{ _serial_no=value;}
			get{return _serial_no;}
		}

		#endregion Model

	}
}

