﻿Imports C1.Win.C1TrueDBGrid
Imports System.Drawing
Imports System.Windows.Forms

Public Class C_Grid


    Private My_Grid As C1.Win.C1TrueDBGrid.C1TrueDBGrid

    Public Sub New(ByVal TDBGrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid) ' 定义一个公用构造函数来初始化C1TrueDBGrid
        My_Grid = TDBGrid
    End Sub

    Public Sub Init_Grid()
        With My_Grid

            '清空
            .Columns.Clear()
            .VisualStyle = VisualStyle.Custom
            '属性
            .AllowUpdate = False
            .AllowDelete = False
            .AllowAddNew = False
            .AllowSort = True

            .AllowColSelect = False
            .AllowColMove = False
            .ScrollTips = True
            .ExtendRightColumn = True

            .BorderStyle = BorderStyle.Fixed3D
            .FlatStyle = C1.Win.C1TrueDBGrid.FlatModeEnum.System

            '滚动条
            .HScrollBar.Style = C1.Win.C1TrueDBGrid.ScrollBarStyleEnum.Automatic
            .VScrollBar.Style = C1.Win.C1TrueDBGrid.ScrollBarStyleEnum.Automatic

            '选择行
            .MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
            .MultiSelect = C1.Win.C1TrueDBGrid.MultiSelectEnum.None

            .DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveNone

            '行状态
            .RowHeight = 18
            .RowDivider.Style = C1.Win.C1TrueDBGrid.LineStyleEnum.Single

            With .Splits(0)
                .ColumnCaptionHeight = 20
                With .HighLightRowStyle
                    .ForeColor = Color.FromArgb(0, 0, 0)
                    .BackColor = Color.FromArgb(175, 238, 238)
                    .VerticalAlignment = C1.Win.C1TrueDBGrid.AlignVertEnum.Bottom
                End With

                With .EditorStyle
                    .ForeColor = Color.FromArgb(0, 0, 0)
                    .BackColor = Color.FromArgb(175, 238, 238)
                    .VerticalAlignment = C1.Win.C1TrueDBGrid.AlignVertEnum.Bottom
                End With

                With .SelectedStyle
                    .ForeColor = Color.FromArgb(255, 255, 255)
                    .BackColor = Color.FromArgb(49, 106, 197)
                    .VerticalAlignment = C1.Win.C1TrueDBGrid.AlignVertEnum.Bottom
                End With

            End With

            'Record
            .RecordSelectorWidth = 17
            With .RecordSelectorStyle
                '.BackColor = SystemColors.Desktop
                .BackColor = Color.FromArgb(0, 78, 152)
                .ForeColor = Color.FromArgb(255, 255, 255)
                .VerticalAlignment = C1.Win.C1TrueDBGrid.AlignVertEnum.Bottom
                .HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                .Borders.BorderType = C1.Win.C1TrueDBGrid.BorderTypeEnum.Raised
            End With

            With .FooterStyle
                .BackColor = Color.FromArgb(0, 78, 152)
                .ForeColor = Color.FromArgb(255, 255, 255)
                .Borders.BorderType = C1.Win.C1TrueDBGrid.BorderTypeEnum.Raised
                .Font = New Font("宋体", 9.5, FontStyle.Regular)
                .VerticalAlignment = C1.Win.C1TrueDBGrid.AlignVertEnum.Bottom
                .HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Far
                .WrapText = False
            End With

        End With
    End Sub

    Public Sub Init_Column(ByVal V_标题 As String, ByVal V_字段 As String, ByVal V_长度 As Integer, ByVal V_水平 As String, ByVal V_格式 As String)
        '字段付值
        Dim My_C As New C1.Win.C1TrueDBGrid.C1DataColumn

        If V_格式 = "Check" Then
            With My_C.ValueItems
                .Translate = True
                .CycleOnClick = True
                .Validate = True
                .Presentation = PresentationEnum.CheckBox
                .Values.Clear()
                .Values.Add(New C1.Win.C1TrueDBGrid.ValueItem("False", False))
                .Values.Add(New C1.Win.C1TrueDBGrid.ValueItem("True", True))

            End With
        Else
            If V_格式 <> "" Then My_C.NumberFormat = V_格式
        End If

        With My_C
            .Caption = V_标题
            .DataField = V_字段
        End With
        My_Grid.Columns.Add(My_C)



        '字段显示
        Dim My_D As C1.Win.C1TrueDBGrid.C1DisplayColumn
        My_D = My_Grid.Splits(0).DisplayColumns.Item(V_标题)
        With My_D
            .Visible = True
            .Width = V_长度
            With .Style
                .VerticalAlignment = C1.Win.C1TrueDBGrid.AlignVertEnum.Bottom
                .HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.General
                .Trimming = StringTrimming.EllipsisPath
                .WrapText = False
                .Font = New Font("宋体", 9.5, FontStyle.Regular)
            End With

            Select Case V_水平
                Case "左"
                    .Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Near
                Case "中"
                    .Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                Case "右"
                    .Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Far
            End Select

            'Header
            With .HeadingStyle
                '.BackColor = SystemColors.Desktop
                .BackColor = Color.FromArgb(0, 78, 152)
                .ForeColor = Color.FromArgb(255, 255, 255)
                .Borders.BorderType = C1.Win.C1TrueDBGrid.BorderTypeEnum.Raised
                .Font = New Font("宋体", 9.5, FontStyle.Regular)
                .VerticalAlignment = C1.Win.C1TrueDBGrid.AlignVertEnum.Bottom
                .HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Center
                .WrapText = False
            End With

            If V_长度 = 0 Then
                .Visible = False
            End If

        End With



    End Sub



    Public Overridable Sub AllAddNew(ByVal V_Boolean As Boolean)
        Me.My_Grid.AllowAddNew = V_Boolean
    End Sub

    Public Overridable Sub AllDelete(ByVal V_Boolean As Boolean)
        Me.My_Grid.AllowDelete = V_Boolean
    End Sub

    Public Overridable Sub AllUpdate(ByVal V_Boolean As Boolean)
        Me.My_Grid.AllowUpdate = V_Boolean
    End Sub

    Public Overridable Sub AllSort(ByVal V_Boolean As Boolean)
        Me.My_Grid.AllowSort = V_Boolean
    End Sub

    Public Enum MultiSelect         '行选择---定义枚举类型
        Null
        Simple
        Extended
    End Enum

    Public Sub P_MultiSelect(ByVal Res As MultiSelect)
        Select Case Res
            Case MultiSelect.Null
                Me.My_Grid.MultiSelect = C1.Win.C1TrueDBGrid.MultiSelectEnum.None
            Case MultiSelect.Simple
                Me.My_Grid.MultiSelect = C1.Win.C1TrueDBGrid.MultiSelectEnum.Simple
            Case MultiSelect.Extended
                Me.My_Grid.MultiSelect = C1.Win.C1TrueDBGrid.MultiSelectEnum.Extended
        End Select
    End Sub

    Public Enum MarqueeEnum         '行选择---定义枚举类型
        FloatingEditor
        HighlightRow
    End Enum

    Public Sub P_MarqueeEnum(ByVal Res As MarqueeEnum)
        Select Case Res
            Case MarqueeEnum.FloatingEditor
                Me.My_Grid.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.FloatingEditor

            Case MarqueeEnum.HighlightRow
                Me.My_Grid.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        End Select
    End Sub

    Public Enum Dock    '行选择---定义枚举类型
        None
        Fill
        Top
        Right
        Left
        Bottom
    End Enum

    Public Sub P_Dock(ByVal Res As Dock)
        Select Case Res
            Case Dock.None
                Me.My_Grid.Dock = DockStyle.None
            Case Dock.Fill
                Me.My_Grid.Dock = DockStyle.Fill
            Case Dock.Top
                Me.My_Grid.Dock = DockStyle.Top
            Case Dock.Right
                Me.My_Grid.Dock = DockStyle.Right
            Case Dock.Left
                Me.My_Grid.Dock = DockStyle.Left
            Case Dock.Bottom
                Me.My_Grid.Dock = DockStyle.Bottom
        End Select
    End Sub

End Class

