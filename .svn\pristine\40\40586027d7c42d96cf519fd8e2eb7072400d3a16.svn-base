﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class MainForm
    Inherits C1.Win.C1Ribbon.C1RibbonForm


    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(MainForm))
        Me.C1StatusBar1 = New C1.Win.C1Ribbon.C1StatusBar()
        Me.RibbonLabel1 = New C1.Win.C1Ribbon.RibbonLabel()
        Me.RibbonSeparator1 = New C1.Win.C1Ribbon.RibbonSeparator()
        Me.RibbonLabel2 = New C1.Win.C1Ribbon.RibbonLabel()
        Me.RibbonSeparator2 = New C1.Win.C1Ribbon.RibbonSeparator()
        Me.RibbonLabel4 = New C1.Win.C1Ribbon.RibbonLabel()
        Me.RibbonSeparator6 = New C1.Win.C1Ribbon.RibbonSeparator()
        Me.RibbonLabel3 = New C1.Win.C1Ribbon.RibbonLabel()
        Me.RibbonSeparator3 = New C1.Win.C1Ribbon.RibbonSeparator()
        Me.barSysTime = New C1.Win.C1Ribbon.RibbonLabel()
        Me.ImageList1 = New System.Windows.Forms.ImageList(Me.components)
        Me.C1NavBarSectionHeader1 = New C1.Win.C1Command.C1NavBarSectionHeader()
        Me.C1Ribbon1 = New C1.Win.C1Ribbon.C1Ribbon()
        Me.RibbonApplicationMenu1 = New C1.Win.C1Ribbon.RibbonApplicationMenu()
        Me.fixBtnPerson = New C1.Win.C1Ribbon.RibbonButton()
        Me.RibbonSeparator4 = New C1.Win.C1Ribbon.RibbonSeparator()
        Me.fixBtnHelp = New C1.Win.C1Ribbon.RibbonButton()
        Me.fixBtnUpdateMemo = New C1.Win.C1Ribbon.RibbonButton()
        Me.fixBtnExit = New C1.Win.C1Ribbon.RibbonButton()
        Me.fixBtnlist = New C1.Win.C1Ribbon.RibbonListItem()
        Me.fixlbl = New C1.Win.C1Ribbon.RibbonLabel()
        Me.RibbonListItem1 = New C1.Win.C1Ribbon.RibbonListItem()
        Me.RibbonSeparator5 = New C1.Win.C1Ribbon.RibbonSeparator()
        Me.RibbonBottomToolBar1 = New C1.Win.C1Ribbon.RibbonBottomToolBar()
        Me.RibbonConfigToolBar1 = New C1.Win.C1Ribbon.RibbonConfigToolBar()
        Me.fixBtnUpdate = New C1.Win.C1Ribbon.RibbonButton()
        Me.RibbonQat1 = New C1.Win.C1Ribbon.RibbonQat()
        Me.RibbonTopToolBar1 = New C1.Win.C1Ribbon.RibbonTopToolBar()
        Me.Timer1 = New System.Windows.Forms.Timer(Me.components)
        Me.Timer2 = New System.Windows.Forms.Timer(Me.components)
        Me.Timer3 = New System.Windows.Forms.Timer(Me.components)
        Me.Timer4 = New System.Windows.Forms.Timer(Me.components)
        Me.C1DockingTab1 = New C1.Win.C1Command.C1DockingTab()
        Me.ImageList4 = New System.Windows.Forms.ImageList(Me.components)
        Me.ContextMenuStrip1 = New System.Windows.Forms.ContextMenuStrip(Me.components)
        Me.ToolStripMenuItem1 = New System.Windows.Forms.ToolStripMenuItem()
        Me.ToolStripMenuItem2 = New System.Windows.Forms.ToolStripMenuItem()
        Me.ToolStripMenuItem3 = New System.Windows.Forms.ToolStripMenuItem()
        Me.ToolStripMenuItem4 = New System.Windows.Forms.ToolStripMenuItem()
        Me.ToolStripMenuItem5 = New System.Windows.Forms.ToolStripMenuItem()
        Me.NotifyIcon1 = New System.Windows.Forms.NotifyIcon(Me.components)
        Me.ScrollPanel1 = New CustomControl.ScrollPanel()
        CType(Me.C1StatusBar1,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.C1Ribbon1,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.C1DockingTab1,System.ComponentModel.ISupportInitialize).BeginInit
        Me.ContextMenuStrip1.SuspendLayout
        Me.SuspendLayout
        '
        'C1StatusBar1
        '
        Me.C1StatusBar1.LeftPaneItems.Add(Me.RibbonLabel1)
        Me.C1StatusBar1.LeftPaneItems.Add(Me.RibbonSeparator1)
        Me.C1StatusBar1.LeftPaneItems.Add(Me.RibbonLabel2)
        Me.C1StatusBar1.LeftPaneItems.Add(Me.RibbonSeparator2)
        Me.C1StatusBar1.LeftPaneItems.Add(Me.RibbonLabel4)
        Me.C1StatusBar1.LeftPaneItems.Add(Me.RibbonSeparator6)
        Me.C1StatusBar1.LeftPaneItems.Add(Me.RibbonLabel3)
        Me.C1StatusBar1.LeftPaneItems.Add(Me.RibbonSeparator3)
        Me.C1StatusBar1.Location = New System.Drawing.Point(0, 758)
        Me.C1StatusBar1.Name = "C1StatusBar1"
        Me.C1StatusBar1.RightPaneItems.Add(Me.barSysTime)
        Me.C1StatusBar1.Size = New System.Drawing.Size(1242, 23)
        Me.C1StatusBar1.SmallImageList = Me.ImageList1
        '
        'RibbonLabel1
        '
        Me.RibbonLabel1.Name = "RibbonLabel1"
        Me.RibbonLabel1.SmallImageIndex = 1
        Me.RibbonLabel1.Text = "Label"
        '
        'RibbonSeparator1
        '
        Me.RibbonSeparator1.Name = "RibbonSeparator1"
        '
        'RibbonLabel2
        '
        Me.RibbonLabel2.Name = "RibbonLabel2"
        Me.RibbonLabel2.SmallImageIndex = 2
        Me.RibbonLabel2.Text = "Label"
        '
        'RibbonSeparator2
        '
        Me.RibbonSeparator2.Name = "RibbonSeparator2"
        '
        'RibbonLabel4
        '
        Me.RibbonLabel4.Name = "RibbonLabel4"
        Me.RibbonLabel4.SmallImageIndex = 2
        Me.RibbonLabel4.Text = "标签"
        '
        'RibbonSeparator6
        '
        Me.RibbonSeparator6.Name = "RibbonSeparator6"
        '
        'RibbonLabel3
        '
        Me.RibbonLabel3.Name = "RibbonLabel3"
        Me.RibbonLabel3.SmallImageIndex = 0
        Me.RibbonLabel3.Text = "Label"
        '
        'RibbonSeparator3
        '
        Me.RibbonSeparator3.Name = "RibbonSeparator3"
        '
        'barSysTime
        '
        Me.barSysTime.Name = "barSysTime"
        Me.barSysTime.SmallImageIndex = 3
        Me.barSysTime.Text = "Label"
        '
        'ImageList1
        '
        Me.ImageList1.ImageStream = CType(resources.GetObject("ImageList1.ImageStream"),System.Windows.Forms.ImageListStreamer)
        Me.ImageList1.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList1.Images.SetKeyName(0, "20120524090529318_easyicon_cn_24.png")
        Me.ImageList1.Images.SetKeyName(1, "201205240910231000_easyicon_cn_24.png")
        Me.ImageList1.Images.SetKeyName(2, "20120524091244177_easyicon_cn_24.png")
        Me.ImageList1.Images.SetKeyName(3, "20120524092219989_easyicon_cn_24.png")
        Me.ImageList1.Images.SetKeyName(4, "20120530033542892_easyicon_cn_24.png")
        '
        'C1NavBarSectionHeader1
        '
        Me.C1NavBarSectionHeader1.Dock = System.Windows.Forms.DockStyle.Top
        Me.C1NavBarSectionHeader1.Location = New System.Drawing.Point(0, 0)
        Me.C1NavBarSectionHeader1.Name = "C1NavBarSectionHeader1"
        Me.C1NavBarSectionHeader1.Size = New System.Drawing.Size(0, 19)
        Me.C1NavBarSectionHeader1.Text = "C1NavBarSectionHeader1"
        '
        'C1Ribbon1
        '
        Me.C1Ribbon1.ApplicationMenuHolder = Me.RibbonApplicationMenu1
        Me.C1Ribbon1.BottomToolBarHolder = Me.RibbonBottomToolBar1
        Me.C1Ribbon1.ConfigToolBarHolder = Me.RibbonConfigToolBar1
        Me.C1Ribbon1.Location = New System.Drawing.Point(0, 0)
        Me.C1Ribbon1.Name = "C1Ribbon1"
        Me.C1Ribbon1.QatHolder = Me.RibbonQat1
        Me.C1Ribbon1.Size = New System.Drawing.Size(1242, 43)
        Me.C1Ribbon1.TopToolBarHolder = Me.RibbonTopToolBar1
        '
        'RibbonApplicationMenu1
        '
        Me.RibbonApplicationMenu1.Description = "快捷菜单"
        Me.RibbonApplicationMenu1.DropDownWidth = 300
        Me.RibbonApplicationMenu1.LeftPaneItems.Add(Me.fixBtnPerson)
        Me.RibbonApplicationMenu1.LeftPaneItems.Add(Me.RibbonSeparator4)
        Me.RibbonApplicationMenu1.LeftPaneItems.Add(Me.fixBtnHelp)
        Me.RibbonApplicationMenu1.LeftPaneItems.Add(Me.fixBtnUpdateMemo)
        Me.RibbonApplicationMenu1.LeftPaneItems.Add(Me.fixBtnExit)
        Me.RibbonApplicationMenu1.Name = "RibbonApplicationMenu1"
        Me.RibbonApplicationMenu1.RightPaneItems.Add(Me.fixBtnlist)
        Me.RibbonApplicationMenu1.RightPaneItems.Add(Me.RibbonListItem1)
        Me.RibbonApplicationMenu1.SmallImage = CType(resources.GetObject("RibbonApplicationMenu1.SmallImage"),System.Drawing.Image)
        Me.RibbonApplicationMenu1.Text = "快捷菜单"
        Me.RibbonApplicationMenu1.ToolTip = "快捷菜单"
        '
        'fixBtnPerson
        '
        Me.fixBtnPerson.LargeImage = Global.His2010.My.Resources.Resources.个人设置32
        Me.fixBtnPerson.Name = "fixBtnPerson"
        Me.fixBtnPerson.Text = "个人设置"
        '
        'RibbonSeparator4
        '
        Me.RibbonSeparator4.Name = "RibbonSeparator4"
        '
        'fixBtnHelp
        '
        Me.fixBtnHelp.LargeImage = Global.His2010.My.Resources.Resources.帮助32
        Me.fixBtnHelp.Name = "fixBtnHelp"
        Me.fixBtnHelp.Text = "帮助文档"
        '
        'fixBtnUpdateMemo
        '
        Me.fixBtnUpdateMemo.LargeImage = Global.His2010.My.Resources.Resources.说明32
        Me.fixBtnUpdateMemo.Name = "fixBtnUpdateMemo"
        Me.fixBtnUpdateMemo.Text = "更新说明"
        '
        'fixBtnExit
        '
        Me.fixBtnExit.LargeImage = Global.His2010.My.Resources.Resources.退出32
        Me.fixBtnExit.Name = "fixBtnExit"
        Me.fixBtnExit.Text = "退出程序"
        '
        'fixBtnlist
        '
        Me.fixBtnlist.AllowSelection = false
        Me.fixBtnlist.Items.Add(Me.fixlbl)
        Me.fixBtnlist.Name = "fixBtnlist"
        '
        'fixlbl
        '
        Me.fixlbl.Font = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.fixlbl.Name = "fixlbl"
        Me.fixlbl.Text = "最常使用模块"
        '
        'RibbonListItem1
        '
        Me.RibbonListItem1.AllowSelection = false
        Me.RibbonListItem1.Items.Add(Me.RibbonSeparator5)
        Me.RibbonListItem1.Name = "RibbonListItem1"
        '
        'RibbonSeparator5
        '
        Me.RibbonSeparator5.Name = "RibbonSeparator5"
        '
        'RibbonBottomToolBar1
        '
        Me.RibbonBottomToolBar1.Name = "RibbonBottomToolBar1"
        '
        'RibbonConfigToolBar1
        '
        Me.RibbonConfigToolBar1.Items.Add(Me.fixBtnUpdate)
        Me.RibbonConfigToolBar1.Name = "RibbonConfigToolBar1"
        '
        'fixBtnUpdate
        '
        Me.fixBtnUpdate.Name = "fixBtnUpdate"
        Me.fixBtnUpdate.SmallImage = CType(resources.GetObject("fixBtnUpdate.SmallImage"),System.Drawing.Image)
        Me.fixBtnUpdate.Text = "检查更新"
        '
        'RibbonQat1
        '
        Me.RibbonQat1.Name = "RibbonQat1"
        Me.RibbonQat1.Visible = false
        '
        'RibbonTopToolBar1
        '
        Me.RibbonTopToolBar1.Name = "RibbonTopToolBar1"
        '
        'Timer2
        '
        '
        'Timer3
        '
        '
        'Timer4
        '
        Me.Timer4.Interval = 1000
        '
        'C1DockingTab1
        '
        Me.C1DockingTab1.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1DockingTab1.CanCloseTabs = true
        Me.C1DockingTab1.CanMoveTabs = true
        Me.C1DockingTab1.CloseBox = C1.Win.C1Command.CloseBoxPositionEnum.AllPages
        Me.C1DockingTab1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.C1DockingTab1.HotTrack = true
        Me.C1DockingTab1.ImageList = Me.ImageList4
        Me.C1DockingTab1.Location = New System.Drawing.Point(0, 61)
        Me.C1DockingTab1.Name = "C1DockingTab1"
        Me.C1DockingTab1.SelectedTabBold = true
        Me.C1DockingTab1.ShowTabList = true
        Me.C1DockingTab1.Size = New System.Drawing.Size(1242, 697)
        Me.C1DockingTab1.TabIndex = 20
        Me.C1DockingTab1.VisualStyle = C1.Win.C1Command.VisualStyle.Office2010Blue
        Me.C1DockingTab1.VisualStyleBase = C1.Win.C1Command.VisualStyle.Office2010Blue
        '
        'ImageList4
        '
        Me.ImageList4.ImageStream = CType(resources.GetObject("ImageList4.ImageStream"),System.Windows.Forms.ImageListStreamer)
        Me.ImageList4.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList4.Images.SetKeyName(0, "doctor_health_medical_24px_3793_easyicon.net.png")
        '
        'ContextMenuStrip1
        '
        Me.ContextMenuStrip1.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.ToolStripMenuItem1, Me.ToolStripMenuItem2, Me.ToolStripMenuItem3, Me.ToolStripMenuItem4, Me.ToolStripMenuItem5})
        Me.ContextMenuStrip1.Name = "ContextMenuStrip1"
        Me.ContextMenuStrip1.Size = New System.Drawing.Size(185, 114)
        '
        'ToolStripMenuItem1
        '
        Me.ToolStripMenuItem1.Image = CType(resources.GetObject("ToolStripMenuItem1.Image"),System.Drawing.Image)
        Me.ToolStripMenuItem1.Name = "ToolStripMenuItem1"
        Me.ToolStripMenuItem1.Size = New System.Drawing.Size(184, 22)
        Me.ToolStripMenuItem1.Text = "关闭"
        '
        'ToolStripMenuItem2
        '
        Me.ToolStripMenuItem2.Name = "ToolStripMenuItem2"
        Me.ToolStripMenuItem2.Size = New System.Drawing.Size(184, 22)
        Me.ToolStripMenuItem2.Text = "关闭其他窗体"
        '
        'ToolStripMenuItem3
        '
        Me.ToolStripMenuItem3.Name = "ToolStripMenuItem3"
        Me.ToolStripMenuItem3.Size = New System.Drawing.Size(184, 22)
        Me.ToolStripMenuItem3.Text = "关闭左边的所有窗体"
        '
        'ToolStripMenuItem4
        '
        Me.ToolStripMenuItem4.Name = "ToolStripMenuItem4"
        Me.ToolStripMenuItem4.Size = New System.Drawing.Size(184, 22)
        Me.ToolStripMenuItem4.Text = "关闭右边的所有窗体"
        '
        'ToolStripMenuItem5
        '
        Me.ToolStripMenuItem5.Image = CType(resources.GetObject("ToolStripMenuItem5.Image"),System.Drawing.Image)
        Me.ToolStripMenuItem5.Name = "ToolStripMenuItem5"
        Me.ToolStripMenuItem5.Size = New System.Drawing.Size(184, 22)
        Me.ToolStripMenuItem5.Text = "关闭所有窗体"
        '
        'NotifyIcon1
        '
        Me.NotifyIcon1.Text = "NotifyIcon1"
        Me.NotifyIcon1.Visible = true
        '
        'ScrollPanel1
        '
        Me.ScrollPanel1.Dock = System.Windows.Forms.DockStyle.Top
        Me.ScrollPanel1.Font = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.ScrollPanel1.LabelHeight = 14
        Me.ScrollPanel1.Location = New System.Drawing.Point(0, 43)
        Me.ScrollPanel1.Margin = New System.Windows.Forms.Padding(0)
        Me.ScrollPanel1.Name = "ScrollPanel1"
        Me.ScrollPanel1.Size = New System.Drawing.Size(1242, 18)
        Me.ScrollPanel1.TabIndex = 24
        Me.ScrollPanel1.Text = "ScrollPanel1"
        '
        'MainForm
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6!, 12!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Stretch
        Me.ClientSize = New System.Drawing.Size(1242, 781)
        Me.Controls.Add(Me.C1DockingTab1)
        Me.Controls.Add(Me.ScrollPanel1)
        Me.Controls.Add(Me.C1Ribbon1)
        Me.Controls.Add(Me.C1StatusBar1)
        Me.DoubleBuffered = true
        Me.Icon = CType(resources.GetObject("$this.Icon"),System.Drawing.Icon)
        Me.IsMdiContainer = true
        Me.KeyPreview = true
        Me.Name = "MainForm"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.Manual
        Me.Text = "居民健康卡综合管理信息系统"
        Me.VisualStyleHolder = C1.Win.C1Ribbon.VisualStyle.Windows7
        Me.WindowState = System.Windows.Forms.FormWindowState.Maximized
        CType(Me.C1StatusBar1,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.C1Ribbon1,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.C1DockingTab1,System.ComponentModel.ISupportInitialize).EndInit
        Me.ContextMenuStrip1.ResumeLayout(false)
        Me.ResumeLayout(false)
        Me.PerformLayout

End Sub
    Friend WithEvents C1StatusBar1 As C1.Win.C1Ribbon.C1StatusBar
    Friend WithEvents C1NavBarSectionHeader1 As C1.Win.C1Command.C1NavBarSectionHeader
    Friend WithEvents C1CommandLink19 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents C1CommandLink20 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents C1CommandLink21 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents C1CommandLink23 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents C1CommandLink28 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents C1CommandLink29 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents C1CommandLink30 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents RibbonLabel1 As C1.Win.C1Ribbon.RibbonLabel
    Friend WithEvents RibbonSeparator1 As C1.Win.C1Ribbon.RibbonSeparator
    Friend WithEvents RibbonLabel2 As C1.Win.C1Ribbon.RibbonLabel
    Friend WithEvents RibbonSeparator2 As C1.Win.C1Ribbon.RibbonSeparator
    Friend WithEvents RibbonLabel3 As C1.Win.C1Ribbon.RibbonLabel
    Friend WithEvents ImageList1 As System.Windows.Forms.ImageList
    Friend WithEvents barSysTime As C1.Win.C1Ribbon.RibbonLabel
    Friend WithEvents RibbonSeparator3 As C1.Win.C1Ribbon.RibbonSeparator
    Friend WithEvents C1Ribbon1 As C1.Win.C1Ribbon.C1Ribbon
    Friend WithEvents RibbonApplicationMenu1 As C1.Win.C1Ribbon.RibbonApplicationMenu
    Friend WithEvents fixBtnPerson As C1.Win.C1Ribbon.RibbonButton
    Friend WithEvents RibbonSeparator4 As C1.Win.C1Ribbon.RibbonSeparator
    Friend WithEvents fixBtnHelp As C1.Win.C1Ribbon.RibbonButton
    Friend WithEvents fixBtnUpdateMemo As C1.Win.C1Ribbon.RibbonButton
    Friend WithEvents fixBtnExit As C1.Win.C1Ribbon.RibbonButton
    Friend WithEvents fixBtnlist As C1.Win.C1Ribbon.RibbonListItem
    Friend WithEvents fixlbl As C1.Win.C1Ribbon.RibbonLabel
    Friend WithEvents RibbonListItem1 As C1.Win.C1Ribbon.RibbonListItem
    Friend WithEvents RibbonSeparator5 As C1.Win.C1Ribbon.RibbonSeparator
    Friend WithEvents RibbonConfigToolBar1 As C1.Win.C1Ribbon.RibbonConfigToolBar
    Friend WithEvents fixBtnUpdate As C1.Win.C1Ribbon.RibbonButton
    Friend WithEvents RibbonQat1 As C1.Win.C1Ribbon.RibbonQat
    Friend WithEvents Timer1 As System.Windows.Forms.Timer
    Friend WithEvents Timer2 As System.Windows.Forms.Timer
    Friend WithEvents Timer3 As System.Windows.Forms.Timer
    Friend WithEvents Timer4 As System.Windows.Forms.Timer
    Friend WithEvents C1DockingTab1 As C1.Win.C1Command.C1DockingTab
    Friend WithEvents ContextMenuStrip1 As System.Windows.Forms.ContextMenuStrip
    Friend WithEvents ToolStripMenuItem1 As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents ToolStripMenuItem2 As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents ToolStripMenuItem3 As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents ToolStripMenuItem4 As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents ToolStripMenuItem5 As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents ImageList4 As System.Windows.Forms.ImageList
    Friend WithEvents ScrollPanel1 As CustomControl.ScrollPanel
    Friend WithEvents RibbonLabel4 As C1.Win.C1Ribbon.RibbonLabel
    Friend WithEvents RibbonSeparator6 As C1.Win.C1Ribbon.RibbonSeparator
    Friend WithEvents RibbonBottomToolBar1 As C1.Win.C1Ribbon.RibbonBottomToolBar
    Friend WithEvents RibbonTopToolBar1 As C1.Win.C1Ribbon.RibbonTopToolBar
    Friend WithEvents NotifyIcon1 As NotifyIcon
End Class
