﻿/**  版本信息模板在安装目录下，可自行修改。
* D_LIS_DictDev.cs
*
* 功 能： N/A
* 类 名： D_LIS_DictDev
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016-10-19 16:06:34   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
namespace DAL
{
	/// <summary>
	/// 数据访问类:D_LIS_DictDev
	/// </summary>
	public partial class D_LIS_DictDev
	{
		public D_LIS_DictDev()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string Dev_Code)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from LIS_DictDev");
			strSql.Append(" where Dev_Code=@Dev_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Dev_Code", SqlDbType.Char,10)			};
			parameters[0].Value = Dev_Code;

			return HisVar.HisVar.Sqldal.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ModelOld.M_LIS_DictDev model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into LIS_DictDev(");
			strSql.Append("Dev_Code,Dev_Name,Dev_Jc,Dev_Model,Dev_Manufacturer,Dev_Lx,Dev_Lb,Ks_Code,InterfaceType,ComPort,BaudRate,DataBits,StopBits,CheckBits,IP,Port,FilePath,InterfacePrograme,Memo)");
			strSql.Append(" values (");
			strSql.Append("@Dev_Code,@Dev_Name,@Dev_Jc,@Dev_Model,@Dev_Manufacturer,@Dev_Lx,@Dev_Lb,@Ks_Code,@InterfaceType,@ComPort,@BaudRate,@DataBits,@StopBits,@CheckBits,@IP,@Port,@FilePath,@InterfacePrograme,@Memo)");
			SqlParameter[] parameters = {
					new SqlParameter("@Dev_Code", SqlDbType.Char,10),
					new SqlParameter("@Dev_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Dev_Jc", SqlDbType.VarChar,50),
					new SqlParameter("@Dev_Model", SqlDbType.VarChar,50),
					new SqlParameter("@Dev_Manufacturer", SqlDbType.VarChar,50),
					new SqlParameter("@Dev_Lx", SqlDbType.VarChar,50),
					new SqlParameter("@Dev_Lb", SqlDbType.VarChar,50),
					new SqlParameter("@Ks_Code", SqlDbType.Char,6),
					new SqlParameter("@InterfaceType", SqlDbType.VarChar,10),
					new SqlParameter("@ComPort", SqlDbType.VarChar,50),
					new SqlParameter("@BaudRate", SqlDbType.VarChar,50),
					new SqlParameter("@DataBits", SqlDbType.VarChar,50),
					new SqlParameter("@StopBits", SqlDbType.VarChar,50),
					new SqlParameter("@CheckBits", SqlDbType.VarChar,50),
					new SqlParameter("@IP", SqlDbType.VarChar,50),
					new SqlParameter("@Port", SqlDbType.VarChar,50),
					new SqlParameter("@FilePath", SqlDbType.VarChar,100),
					new SqlParameter("@InterfacePrograme", SqlDbType.VarChar,50),
					new SqlParameter("@Memo", SqlDbType.VarChar,500)};
			parameters[0].Value = model.Dev_Code;
			parameters[1].Value = model.Dev_Name;
			parameters[2].Value = model.Dev_Jc;
			parameters[3].Value = model.Dev_Model;
			parameters[4].Value = model.Dev_Manufacturer;
			parameters[5].Value = model.Dev_Lx;
			parameters[6].Value = model.Dev_Lb;
			parameters[7].Value = model.Ks_Code;
			parameters[8].Value = model.InterfaceType;
			parameters[9].Value = model.ComPort;
			parameters[10].Value = model.BaudRate;
			parameters[11].Value = model.DataBits;
			parameters[12].Value = model.StopBits;
			parameters[13].Value = model.CheckBits;
			parameters[14].Value = model.IP;
			parameters[15].Value = model.Port;
			parameters[16].Value = model.FilePath;
			parameters[17].Value = model.InterfacePrograme;
			parameters[18].Value = model.Memo;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ModelOld.M_LIS_DictDev model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update LIS_DictDev set ");
			strSql.Append("Dev_Name=@Dev_Name,");
			strSql.Append("Dev_Jc=@Dev_Jc,");
			strSql.Append("Dev_Model=@Dev_Model,");
			strSql.Append("Dev_Manufacturer=@Dev_Manufacturer,");
			strSql.Append("Dev_Lx=@Dev_Lx,");
			strSql.Append("Dev_Lb=@Dev_Lb,");
			strSql.Append("Ks_Code=@Ks_Code,");
			strSql.Append("InterfaceType=@InterfaceType,");
			strSql.Append("ComPort=@ComPort,");
			strSql.Append("BaudRate=@BaudRate,");
			strSql.Append("DataBits=@DataBits,");
			strSql.Append("StopBits=@StopBits,");
			strSql.Append("CheckBits=@CheckBits,");
			strSql.Append("IP=@IP,");
			strSql.Append("Port=@Port,");
			strSql.Append("FilePath=@FilePath,");
			strSql.Append("InterfacePrograme=@InterfacePrograme,");
			strSql.Append("Memo=@Memo");
			strSql.Append(" where Dev_Code=@Dev_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Dev_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Dev_Jc", SqlDbType.VarChar,50),
					new SqlParameter("@Dev_Model", SqlDbType.VarChar,50),
					new SqlParameter("@Dev_Manufacturer", SqlDbType.VarChar,50),
					new SqlParameter("@Dev_Lx", SqlDbType.VarChar,50),
					new SqlParameter("@Dev_Lb", SqlDbType.VarChar,50),
					new SqlParameter("@Ks_Code", SqlDbType.Char,6),
					new SqlParameter("@InterfaceType", SqlDbType.VarChar,10),
					new SqlParameter("@ComPort", SqlDbType.VarChar,50),
					new SqlParameter("@BaudRate", SqlDbType.VarChar,50),
					new SqlParameter("@DataBits", SqlDbType.VarChar,50),
					new SqlParameter("@StopBits", SqlDbType.VarChar,50),
					new SqlParameter("@CheckBits", SqlDbType.VarChar,50),
					new SqlParameter("@IP", SqlDbType.VarChar,50),
					new SqlParameter("@Port", SqlDbType.VarChar,50),
					new SqlParameter("@FilePath", SqlDbType.VarChar,100),
					new SqlParameter("@InterfacePrograme", SqlDbType.VarChar,50),
					new SqlParameter("@Memo", SqlDbType.VarChar,500),
					new SqlParameter("@Dev_Code", SqlDbType.Char,10)};
			parameters[0].Value = model.Dev_Name;
			parameters[1].Value = model.Dev_Jc;
			parameters[2].Value = model.Dev_Model;
			parameters[3].Value = model.Dev_Manufacturer;
			parameters[4].Value = model.Dev_Lx;
			parameters[5].Value = model.Dev_Lb;
			parameters[6].Value = model.Ks_Code;
			parameters[7].Value = model.InterfaceType;
			parameters[8].Value = model.ComPort;
			parameters[9].Value = model.BaudRate;
			parameters[10].Value = model.DataBits;
			parameters[11].Value = model.StopBits;
			parameters[12].Value = model.CheckBits;
			parameters[13].Value = model.IP;
			parameters[14].Value = model.Port;
			parameters[15].Value = model.FilePath;
			parameters[16].Value = model.InterfacePrograme;
			parameters[17].Value = model.Memo;
			parameters[18].Value = model.Dev_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string Dev_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from LIS_DictDev ");
			strSql.Append(" where Dev_Code=@Dev_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Dev_Code", SqlDbType.Char,10)			};
			parameters[0].Value = Dev_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string Dev_Codelist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from LIS_DictDev ");
			strSql.Append(" where Dev_Code in ("+Dev_Codelist + ")  ");
			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public  ModelOld.M_LIS_DictDev GetModel(string Dev_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 Dev_Code,Dev_Name,Dev_Jc,Dev_Model,Dev_Manufacturer,Dev_Lx,Dev_Lb,Ks_Code,InterfaceType,ComPort,BaudRate,DataBits,StopBits,CheckBits,IP,Port,FilePath,InterfacePrograme,Memo from LIS_DictDev ");
			strSql.Append(" where Dev_Code=@Dev_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Dev_Code", SqlDbType.Char,10)			};
			parameters[0].Value = Dev_Code;

			ModelOld.M_LIS_DictDev model=new  ModelOld.M_LIS_DictDev();
			DataSet ds=HisVar.HisVar.Sqldal.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public  ModelOld.M_LIS_DictDev DataRowToModel(DataRow row)
		{
			ModelOld.M_LIS_DictDev model=new ModelOld.M_LIS_DictDev();
			if (row != null)
			{
				if(row["Dev_Code"]!=null)
				{
					model.Dev_Code=row["Dev_Code"].ToString();
				}
				if(row["Dev_Name"]!=null)
				{
					model.Dev_Name=row["Dev_Name"].ToString();
				}
				if(row["Dev_Jc"]!=null)
				{
					model.Dev_Jc=row["Dev_Jc"].ToString();
				}
				if(row["Dev_Model"]!=null)
				{
					model.Dev_Model=row["Dev_Model"].ToString();
				}
				if(row["Dev_Manufacturer"]!=null)
				{
					model.Dev_Manufacturer=row["Dev_Manufacturer"].ToString();
				}
				if(row["Dev_Lx"]!=null)
				{
					model.Dev_Lx=row["Dev_Lx"].ToString();
				}
				if(row["Dev_Lb"]!=null)
				{
					model.Dev_Lb=row["Dev_Lb"].ToString();
				}
				if(row["Ks_Code"]!=null)
				{
					model.Ks_Code=row["Ks_Code"].ToString();
				}
				if(row["InterfaceType"]!=null)
				{
					model.InterfaceType=row["InterfaceType"].ToString();
				}
				if(row["ComPort"]!=null)
				{
					model.ComPort=row["ComPort"].ToString();
				}
				if(row["BaudRate"]!=null)
				{
					model.BaudRate=row["BaudRate"].ToString();
				}
				if(row["DataBits"]!=null)
				{
					model.DataBits=row["DataBits"].ToString();
				}
				if(row["StopBits"]!=null)
				{
					model.StopBits=row["StopBits"].ToString();
				}
				if(row["CheckBits"]!=null)
				{
					model.CheckBits=row["CheckBits"].ToString();
				}
				if(row["IP"]!=null)
				{
					model.IP=row["IP"].ToString();
				}
				if(row["Port"]!=null)
				{
					model.Port=row["Port"].ToString();
				}
				if(row["FilePath"]!=null)
				{
					model.FilePath=row["FilePath"].ToString();
				}
				if(row["InterfacePrograme"]!=null)
				{
					model.InterfacePrograme=row["InterfacePrograme"].ToString();
				}
				if(row["Memo"]!=null)
				{
					model.Memo=row["Memo"].ToString();
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
            strSql.Append(" SELECT Dev_Code,Dev_Name,Dev_Jc,Dev_Model,Dev_Manufacturer,Dev_Lx,Dev_Lb,InterfaceType,ComPort,BaudRate,DataBits,StopBits,CheckBits,IP,Port,FilePath,InterfacePrograme,Memo,LIS_DictDev.Ks_Code,Ks_Name ");
            strSql.Append(" FROM LIS_DictDev ,Zd_YyKs");
            strSql.Append(" WHERE Zd_YyKs.Ks_Code=LIS_DictDev.Ks_Code ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" and "+strWhere);
			}
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
            strSql.Append(" Dev_Code,Dev_Name,Dev_Jc,Dev_Model,Dev_Manufacturer,Dev_Lx,Dev_Lb,InterfaceType,ComPort,BaudRate,DataBits,StopBits,CheckBits,IP,Port,FilePath,InterfacePrograme,Memo,LIS_DictDev.Ks_Code,Ks_Name ");
            strSql.Append(" FROM LIS_DictDev ,Zd_YyKs");
            strSql.Append(" WHERE Zd_YyKs.Ks_Code=LIS_DictDev.Ks_Code ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" and"+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM LIS_DictDev ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.Dev_Code desc");
			}
			strSql.Append(")AS Row, T.*  from LIS_DictDev T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "LIS_DictDev";
			parameters[1].Value = "Dev_Code";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return HisVar.HisVar.Sqldal.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

