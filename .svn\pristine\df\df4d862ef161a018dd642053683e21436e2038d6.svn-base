﻿Imports BaseClass
Imports System.Windows.Forms
Imports System.Drawing

Public Class Materials_Buy_In2
#Region "传参"
    Di<PERSON> As Boolean
    Dim Rrow As DataRow
    Dim RZbtb As New DataTable
    Dim Rcode As String
    Dim Rrc As C_RowChange


    Dim Materials_Code As String
    Dim MaterialsLot As String
#End Region

    Public Sub New(ByVal tinsert As Boolean, ByVal trow As DataRow, ByVal tZbtb As DataTable, ByVal ttdbgrid As CustomControl.MyGrid, ByVal tcode As String, ByRef trc As C_RowChange)
        '此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        Rinsert = tinsert
        Rrow = trow
        RZbtb = tZbtb
        Rcode = tcode
        Rrc = trc
        '在 InitializeComponent() 调用之后添加任何初始化。
    End Sub

    Private Sub Materials_Buy_In2_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Call Form_Init()
        If Rinsert = True Then
            Call Data_Clear()
        Else
            Call Data_Show()
        End If
    End Sub
    Private Sub Form_Init()
        '物资comobo
        Dim MaterialsBll As New BLLOld.B_Materials_Dict
        With Materials_Comobo
            .DataView = MaterialsBll.GetList("IsUse='1'").Tables(0).DefaultView
            .Init_Colum("Materials_Py", "物资简称", 80, "左")
            .Init_Colum("Materials_Name", "物资名称", 150, "左")
            .Init_Colum("Materials_Code", "编码", 0, "左")
            .Init_Colum("Materials_Wb", "--", 0, "左")
            .Init_Colum("Materials_Spec", "规格", 80, "左")
            .Init_Colum("MateManu_Name", "生产厂家", 100, "左")
            .Init_Colum("Pack_Unit", "包装单位", 0, "左")
            .Init_Colum("Bulk_Unit", "散装单位", 0, "左")
            .Init_Colum("Convert_Ratio", "拆分比例", 0, "左")
            .Init_Colum("IsUse", "--", 0, "左")
            .DisplayMember = "Materials_Name"
            .ValueMember = "Materials_Code"
            .DroupDownWidth = 510
            .MaxDropDownItems = 15
            .SelectedIndex = -1
            .RowFilterTextNull = ""
            .RowFilterNotTextNull = "Materials_Py+Materials_Name+Materials_Spec"
        End With
        '有效期
        With YXQ_MyDateEdit1
            .CustomFormat = "yyyy-MM-dd"
            .EditFormat = "yyyy-MM-dd"
            .DisplayFormat = "yyyy-MM-dd"
            .Value = Format(Now, "yyyy-MM-dd")
        End With

        CGDJ_MyNumericEdit2.CustomFormat = "######0.00##"
        CGJE_MyNumericEdit3.CustomFormat = "######0.00##"
        RKDJMyNumericEdit1.CustomFormat = "######0.00##"
        RKJE_MyNumericEdit2.CustomFormat = "######0.00##"
        GG_MyTextBox1.Enabled = False
        SCCJ_MyTextBox2.Enabled = False
        BZDW_MyTextBox3.Enabled = False
        SZDW_MyTextBox4.Enabled = False
        CFBL_MyNumericEdit1.Enabled = False
        CGJE_MyNumericEdit3.Enabled = False
        RKSL_MyNumericEdit1.Enabled = False
        RKDJMyNumericEdit1.Enabled = False
        RKJE_MyNumericEdit2.Enabled = False

    End Sub
#Region "数据编辑"
    Private Sub Data_Clear()
        Rinsert = True
        Materials_Comobo.SelectedIndex = -1
        GG_MyTextBox1.Text = ""
        SCCJ_MyTextBox2.Text = ""
        BZDW_MyTextBox3.Text = ""
        SZDW_MyTextBox4.Text = ""
        CFBL_MyNumericEdit1.Value = ""
        Ph_MyDtComobo1.Text = ""
        Ph_MyDtComobo1.SelectedIndex = -1
        Ph_MyDtComobo1.ReadOnly = False
        YXQ_MyDateEdit1.Value = Format(Now, "yyyy-MM-dd")
        BZSL_MyNumericEdit1.Value = 0
        RKSL_MyNumericEdit1.Value = 0
        CGDJ_MyNumericEdit2.Value = 0
        CGJE_MyNumericEdit3.Value = 0
        RKDJMyNumericEdit1.Value = 0
        RKJE_MyNumericEdit2.Value = 0
        Memo.Text = ""

        Materials_Code = ""
        MaterialsLot = ""

        Materials_Comobo.Select()
        Materials_Comobo.Focus()
    End Sub

    Private Sub Data_Show()
        Rinsert = False
        With Rrow
            Materials_Comobo.SelectedValue = .Item("Materials_Code")
            Ph_MyDtComobo1.Text = .Item("MaterialsLot")
            YXQ_MyDateEdit1.Value = .Item("MaterialsExpiryDate")

            BZSL_MyNumericEdit1.Value = .Item("M_Buy_Num")
            CGDJ_MyNumericEdit2.Value = .Item("M_Buy_Price")
            CGJE_MyNumericEdit3.Value = .Item("M_Buy_Money")
            RKSL_MyNumericEdit1.Value = .Item("M_BuyIn_Num")

            RKDJMyNumericEdit1.Value = .Item("M_BuyIn_Price")
            RKJE_MyNumericEdit2.Value = .Item("M_Buy_RealMoney")
            Memo.Text = .Item("M_BuyDetail_Memo")

            Materials_Code = .Item("Materials_Code")
            MaterialsLot = .Item("MaterialsLot")
        End With
        Materials_Comobo.Select()
    End Sub


    Private Sub Save_Add()
        Dim My_NewRow As DataRow
        If Rinsert = True Then
            My_NewRow = RZbtb.NewRow
        Else
            My_NewRow = Rrow
        End If
        With My_NewRow
            .BeginEdit()
            .Item("M_Buy_Code") = Rcode
            .Item("Materials_Code") = Materials_Comobo.SelectedValue
            .Item("Materials_Name") = Materials_Comobo.Text
            .Item("MaterialsLot") = Ph_MyDtComobo1.Text
            .Item("MaterialsExpiryDate") = Format(YXQ_MyDateEdit1.Value, "yyyy-MM-dd")
            .Item("M_Buy_Num") = BZSL_MyNumericEdit1.Value
            .Item("M_Buy_WriteoffNo") = 0
            .Item("M_Buy_RealNo") = BZSL_MyNumericEdit1.Value
            .Item("M_BuyIn_Num") = RKSL_MyNumericEdit1.Value
            .Item("M_BuyIn_WriteoffNo") = 0
            .Item("M_BuyIn_RealNo") = RKSL_MyNumericEdit1.Value
            .Item("M_Buy_Price") = CGDJ_MyNumericEdit2.Value
            .Item("M_BuyIn_Price") = RKDJMyNumericEdit1.Value
            .Item("M_Buy_Money") = BZSL_MyNumericEdit1.Value * CGDJ_MyNumericEdit2.Value
            .Item("M_Buy_RealMoney") = RKDJMyNumericEdit1.Value * RKSL_MyNumericEdit1.Value
            .Item("Pack_Unit") = BZDW_MyTextBox3.Text
            .Item("Convert_Ratio") = CFBL_MyNumericEdit1.Value
            .Item("Bulk_Unit") = SZDW_MyTextBox4.Text
            .Item("M_BuyDetail_Memo") = Memo.Text
            .EndEdit()
        End With
        Try
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            Exit Sub
        Finally
        End Try

        Try
            If Rinsert = True Then
                RZbtb.Rows.Add(My_NewRow)
                Rrc.GridMove("最后")
                Call Data_Clear()
            End If
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            Materials_Comobo.Select()
            Exit Sub
        Finally
        End Try

    End Sub

#End Region

#Region "按钮动作"
    Private Sub Save_MyButton1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Save_MyButton1.Click, Cancle_MyButton1.Click
        Select Case sender.tag
            Case "保存"
                If Materials_Comobo.SelectedValue = "" Then
                    Beep()
                    MsgBox("物资不能为空!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    Materials_Comobo.Select()
                    Exit Sub
                End If

                If Trim(Ph_MyDtComobo1.Text & "") = "" Then
                    Beep()
                    MsgBox("批号不能为空!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    Ph_MyDtComobo1.Select()
                    Exit Sub
                End If

                If Trim(YXQ_MyDateEdit1.Value & "") = "" Or YXQ_MyDateEdit1.Value < Now Then
                    Beep()
                    MsgBox("有效期输入有误!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    YXQ_MyDateEdit1.Select()
                    Exit Sub
                End If

                If BZSL_MyNumericEdit1.Value <= 0 Then
                    Beep()
                    MsgBox("包装数量必须大于0!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    BZSL_MyNumericEdit1.Select()
                    Exit Sub
                End If

                If CGDJ_MyNumericEdit2.Text = "" Or CGDJ_MyNumericEdit2.Value < 0 Then
                    Beep()
                    MsgBox("采购单价有误!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    CGDJ_MyNumericEdit2.Select()
                    Exit Sub
                End If

                If Materials_Code <> Materials_Comobo.SelectedValue And MaterialsLot <> Ph_MyDtComobo1.Text.Trim And RZbtb.Select("Materials_Code = '" & Materials_Comobo.SelectedValue & "' and MaterialsLot = '" & Ph_MyDtComobo1.Text.Trim & "'").Length > 0 Then
                    Beep()
                    MsgBox("物资:" & Materials_Comobo.Text.Trim & " 批号:" & Ph_MyDtComobo1.Text.Trim & "不能重复录入！", MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
                    Materials_Comobo.Select()
                    Exit Sub
                End If

                Call Save_Add()
            Case "取消"
                Me.Close()
        End Select
    End Sub
#End Region

    Private Sub BZSL_MyNumericEdit1_Validated(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles BZSL_MyNumericEdit1.Validated
        If Materials_Comobo.SelectedValue = 0 Then Exit Sub
        RKSL_MyNumericEdit1.Value = Format(BZSL_MyNumericEdit1.Value * Materials_Comobo.Columns("Convert_Ratio").Value, "0.######")
        CGJE_MyNumericEdit3.Value = Format(CGDJ_MyNumericEdit2.Value * BZSL_MyNumericEdit1.Value, "0.00####")
        RKJE_MyNumericEdit2.Value = Format(RKDJMyNumericEdit1.Value * RKSL_MyNumericEdit1.Value, "0.00####")
    End Sub
    Private Sub CGDJ_MyNumericEdit2_Validated(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CGDJ_MyNumericEdit2.Validated
        If Materials_Comobo.SelectedValue = 0 Then Exit Sub
        RKDJMyNumericEdit1.Value = Format(CGDJ_MyNumericEdit2.Value / Materials_Comobo.Columns("Convert_Ratio").Value, "0.######")
        CGJE_MyNumericEdit3.Value = Format(CGDJ_MyNumericEdit2.Value * BZSL_MyNumericEdit1.Value, "0.00####")
        RKJE_MyNumericEdit2.Value = Format(RKDJMyNumericEdit1.Value * RKSL_MyNumericEdit1.Value, "0.00####")
    End Sub

    Private Sub Materials_Comobo_RowChange(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Materials_Comobo.RowChange
        GG_MyTextBox1.Text = Materials_Comobo.Columns("Materials_Spec").Value
        SCCJ_MyTextBox2.Text = Materials_Comobo.Columns("MateManu_Name").Value
        BZDW_MyTextBox3.Text = Materials_Comobo.Columns("Pack_Unit").Value
        SZDW_MyTextBox4.Text = Materials_Comobo.Columns("Bulk_Unit").Value
        CFBL_MyNumericEdit1.Text = Materials_Comobo.Columns("Convert_Ratio").Value

        Dim PhBll As New BLLOld.B_Materials_Stock
        With Ph_MyDtComobo1
            .DataView = PhBll.GetList("Materials_Code='" & Materials_Comobo.SelectedValue & "'").Tables(0).DefaultView
            .Init_Colum("MaterialsLot", "批号", 80, "左")
            .Init_Colum("MaterialsExpiryDate", "有效期", 90, "左")
            .Init_Colum("Materials_Code", "--", 0, "左")
            .Init_Colum("MaterialsWh_Code", "--", 0, "左")
            .Init_Colum("MaterialsStock_Code", "--", 0, "左")
            .Init_Colum("MaterialsStore_Num", "--", 0, "左")
            .Init_Colum("MaterialsStore_Price", "--", 0, "左")
            .Init_Colum("MaterialsStore_Money", "--", 0, "左")
            .DisplayMember = "MaterialsLot"
            .ValueMember = "MaterialsStock_Code"
            .DroupDownWidth = 200
            .MaxDropDownItems = 15
            .SelectedIndex = -1
            .RowFilterTextNull = ""
            .RowFilterNotTextNull = "MaterialsLot"
            .AllowNew = True
        End With

    End Sub


    Private Sub Ph_MyDtComobo1_RowChange(ByVal sender As Object, ByVal e As System.EventArgs) Handles Ph_MyDtComobo1.RowChange

        If Ph_MyDtComobo1.WillChangeToValue = "" Then
            YXQ_MyDateEdit1.Value = ""
            CGDJ_MyNumericEdit2.Value = 0
            RKDJMyNumericEdit1.Value = 0
        Else
            YXQ_MyDateEdit1.Value = Ph_MyDtComobo1.Columns("MaterialsExpiryDate").Value & ""
            CGDJ_MyNumericEdit2.Value = Ph_MyDtComobo1.Columns("MaterialsStore_Price").Value * CFBL_MyNumericEdit1.Value
            RKDJMyNumericEdit1.Value = Ph_MyDtComobo1.Columns("MaterialsStore_Price").Value
        End If
    End Sub


    Private Sub Ph_MyDtComobo1_Validating(ByVal sender As Object, ByVal e As System.ComponentModel.CancelEventArgs) Handles Ph_MyDtComobo1.Validating
        Dim V_Code As String = Me.Ph_MyDtComobo1.WillChangeToValue
        If V_Code & "" = "" Then                  '编码不存在
            YXQ_MyDateEdit1.Enabled = True
            CGDJ_MyNumericEdit2.Enabled = True
            YXQ_MyDateEdit1.Select()
        Else                                                            '编码存在
            YXQ_MyDateEdit1.Enabled = False
            CGDJ_MyNumericEdit2.Enabled = False
            BZSL_MyNumericEdit1.Select()
        End If
    End Sub


End Class