﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class Xs_Mz2
    Inherits HisControl.BaseForm

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(Xs_Mz2))
        Me.Comm2 = New System.Windows.Forms.Button()
        Me.C1TrueDBGrid1 = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel()
        Me.AddressTextBox = New CustomControl.MyTextBox()
        Me.SfzhTextBox = New CustomControl.MyTextBox()
        Me.DateRyBirthday = New CustomControl.MyDateEdit()
        Me.Label11 = New System.Windows.Forms.Label()
        Me.lblMz_Code = New System.Windows.Forms.Label()
        Me.GroupBox1 = New System.Windows.Forms.GroupBox()
        Me.C1ToolBar1 = New C1.Win.C1Command.C1ToolBar()
        Me.C1Holder1 = New C1.Win.C1Command.C1CommandHolder()
        Me.Control1 = New C1.Win.C1Command.C1CommandControl()
        Me.Control2 = New C1.Win.C1Command.C1CommandControl()
        Me.Control3 = New C1.Win.C1Command.C1CommandControl()
        Me.C1Command1 = New C1.Win.C1Command.C1Command()
        Me.C1Command2 = New C1.Win.C1Command.C1Command()
        Me.C1CommandLink1 = New C1.Win.C1Command.C1CommandLink()
        Me.C1CommandLink2 = New C1.Win.C1Command.C1CommandLink()
        Me.C1NumericEdit1 = New C1.Win.C1Input.C1NumericEdit()
        Me.Label8 = New System.Windows.Forms.Label()
        Me.RadioButton4 = New System.Windows.Forms.RadioButton()
        Me.RadioButton1 = New System.Windows.Forms.RadioButton()
        Me.ComboYf1 = New ZTHisControl.ComboYf()
        Me.NlC1NumericEdit = New CustomControl.MyNumericEdit()
        Me.ComboJb1 = New ZTHisControl.ComboJb()
        Me.ComboYs1 = New ZTHisControl.ComboYs()
        Me.ComboKs1 = New ZTHisControl.ComboKs()
        Me.SingleSex1 = New ZTHisControl.SingleSex()
        Me.XmTextBox = New CustomControl.MyTextBox()
        Me.YlCodeTextBox = New CustomControl.MyTextBox()
        Me.ComboBxlb1 = New ZTHisControl.ComboBxlb()
        Me.TelText = New CustomControl.MyTextBox()
        Me.KMoneyLabel = New System.Windows.Forms.Label()
        Me.C1Combo8 = New C1.Win.C1List.C1Combo()
        Me.Panel2 = New System.Windows.Forms.Panel()
        Me.BtnSaveTemplate = New System.Windows.Forms.Button()
        Me.T_Label5 = New System.Windows.Forms.Label()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.Comm3 = New System.Windows.Forms.Button()
        Me.Comm1 = New System.Windows.Forms.Button()
        Me.Label16 = New System.Windows.Forms.Label()
        Me.T_Line2 = New System.Windows.Forms.Label()
        Me.C1CommandDock1 = New C1.Win.C1Command.C1CommandDock()
        Me.C1DockingTab1 = New C1.Win.C1Command.C1DockingTab()
        Me.C1DockingTabPage1 = New C1.Win.C1Command.C1DockingTabPage()
        Me.MzData1 = New ZTHisOutpatient.MzData()
        Me.ToolBarReadCard1 = New ZTHisPublicForm.ToolBarReadCard()
        CType(Me.C1TrueDBGrid1,System.ComponentModel.ISupportInitialize).BeginInit
        Me.Panel1.SuspendLayout
        Me.TableLayoutPanel1.SuspendLayout
        Me.GroupBox1.SuspendLayout
        CType(Me.C1Holder1,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.C1NumericEdit1,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.C1Combo8,System.ComponentModel.ISupportInitialize).BeginInit
        Me.Panel2.SuspendLayout
        CType(Me.C1CommandDock1,System.ComponentModel.ISupportInitialize).BeginInit
        Me.C1CommandDock1.SuspendLayout
        CType(Me.C1DockingTab1,System.ComponentModel.ISupportInitialize).BeginInit
        Me.C1DockingTab1.SuspendLayout
        Me.C1DockingTabPage1.SuspendLayout
        Me.SuspendLayout
        '
        'Comm2
        '
        Me.Comm2.DialogResult = System.Windows.Forms.DialogResult.Cancel
        Me.Comm2.Location = New System.Drawing.Point(347, 3)
        Me.Comm2.Name = "Comm2"
        Me.Comm2.Size = New System.Drawing.Size(50, 24)
        Me.Comm2.TabIndex = 46
        Me.Comm2.Tag = "取消"
        Me.Comm2.Text = "取消"
        Me.Comm2.UseVisualStyleBackColor = false
        '
        'C1TrueDBGrid1
        '
        Me.C1TrueDBGrid1.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.C1TrueDBGrid1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.C1TrueDBGrid1.GroupByCaption = "Drag a column header here to group by that column"
        Me.C1TrueDBGrid1.Images.Add(CType(resources.GetObject("C1TrueDBGrid1.Images"),System.Drawing.Image))
        Me.C1TrueDBGrid1.Location = New System.Drawing.Point(0, 143)
        Me.C1TrueDBGrid1.Name = "C1TrueDBGrid1"
        Me.C1TrueDBGrid1.PreviewInfo.Caption = "PrintPreview窗口"
        Me.C1TrueDBGrid1.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.C1TrueDBGrid1.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.C1TrueDBGrid1.PreviewInfo.ZoomFactor = 75R
        Me.C1TrueDBGrid1.PrintInfo.MeasurementDevice = C1.Win.C1TrueDBGrid.PrintInfo.MeasurementDeviceEnum.Screen
        Me.C1TrueDBGrid1.PrintInfo.MeasurementPrinterName = Nothing
        Me.C1TrueDBGrid1.Size = New System.Drawing.Size(634, 471)
        Me.C1TrueDBGrid1.TabIndex = 0
        Me.C1TrueDBGrid1.Text = "C1TrueDBGrid1"
        Me.C1TrueDBGrid1.UseCompatibleTextRendering = false
        Me.C1TrueDBGrid1.PropBag = resources.GetString("C1TrueDBGrid1.PropBag")
        '
        'Panel1
        '
        Me.Panel1.Controls.Add(Me.TableLayoutPanel1)
        Me.Panel1.Dock = System.Windows.Forms.DockStyle.Top
        Me.Panel1.Location = New System.Drawing.Point(0, 28)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(1129, 115)
        Me.Panel1.TabIndex = 0
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.ColumnCount = 11
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 60!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 180!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 60!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 60!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 60!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 60!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 60!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 120!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 60!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 202!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 207!))
        Me.TableLayoutPanel1.Controls.Add(Me.AddressTextBox, 6, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.SfzhTextBox, 6, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.DateRyBirthday, 0, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.Label11, 0, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.lblMz_Code, 1, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.GroupBox1, 9, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.NlC1NumericEdit, 4, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.ComboJb1, 6, 3)
        Me.TableLayoutPanel1.Controls.Add(Me.ComboYs1, 0, 3)
        Me.TableLayoutPanel1.Controls.Add(Me.ComboKs1, 2, 3)
        Me.TableLayoutPanel1.Controls.Add(Me.SingleSex1, 2, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.XmTextBox, 2, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.YlCodeTextBox, 6, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.ComboBxlb1, 2, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.TelText, 0, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.KMoneyLabel, 10, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.C1Combo8, 10, 0)
        Me.TableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(0, 0)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.RowCount = 6
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 26!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 26!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 26!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 26!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 26!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 20!))
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(1129, 115)
        Me.TableLayoutPanel1.TabIndex = 180
        '
        'AddressTextBox
        '
        Me.AddressTextBox.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.AddressTextBox.Captain = "家庭住址"
        Me.AddressTextBox.CaptainBackColor = System.Drawing.Color.Transparent
        Me.AddressTextBox.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.AddressTextBox.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.AddressTextBox.CaptainWidth = 70!
        Me.TableLayoutPanel1.SetColumnSpan(Me.AddressTextBox, 3)
        Me.AddressTextBox.ContentForeColor = System.Drawing.Color.Black
        Me.AddressTextBox.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer))
        Me.AddressTextBox.EditMask = Nothing
        Me.AddressTextBox.Location = New System.Drawing.Point(483, 29)
        Me.AddressTextBox.Multiline = false
        Me.AddressTextBox.Name = "AddressTextBox"
        Me.AddressTextBox.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.AddressTextBox.ReadOnly = false
        Me.AddressTextBox.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.AddressTextBox.SelectionStart = 0
        Me.AddressTextBox.SelectStart = 0
        Me.AddressTextBox.Size = New System.Drawing.Size(234, 20)
        Me.AddressTextBox.TabIndex = 5
        Me.AddressTextBox.TextAlign = System.Windows.Forms.HorizontalAlignment.Left
        Me.AddressTextBox.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.AddressTextBox.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.AddressTextBox.Watermark = Nothing
        '
        'SfzhTextBox
        '
        Me.SfzhTextBox.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.SfzhTextBox.Captain = "身份证号"
        Me.SfzhTextBox.CaptainBackColor = System.Drawing.Color.Transparent
        Me.SfzhTextBox.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.SfzhTextBox.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.SfzhTextBox.CaptainWidth = 70!
        Me.TableLayoutPanel1.SetColumnSpan(Me.SfzhTextBox, 3)
        Me.SfzhTextBox.ContentForeColor = System.Drawing.Color.Black
        Me.SfzhTextBox.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer))
        Me.SfzhTextBox.EditMask = Nothing
        Me.SfzhTextBox.Location = New System.Drawing.Point(483, 3)
        Me.SfzhTextBox.Multiline = false
        Me.SfzhTextBox.Name = "SfzhTextBox"
        Me.SfzhTextBox.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.SfzhTextBox.ReadOnly = false
        Me.SfzhTextBox.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.SfzhTextBox.SelectionStart = 0
        Me.SfzhTextBox.SelectStart = 0
        Me.SfzhTextBox.Size = New System.Drawing.Size(234, 20)
        Me.SfzhTextBox.TabIndex = 1
        Me.SfzhTextBox.TextAlign = System.Windows.Forms.HorizontalAlignment.Left
        Me.SfzhTextBox.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.SfzhTextBox.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.SfzhTextBox.Watermark = Nothing
        '
        'DateRyBirthday
        '
        Me.DateRyBirthday.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.DateRyBirthday.Captain = "出生日期"
        Me.DateRyBirthday.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.DateRyBirthday.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.DateRyBirthday.CaptainWidth = 70!
        Me.TableLayoutPanel1.SetColumnSpan(Me.DateRyBirthday, 2)
        Me.DateRyBirthday.InitialSelection = C1.Win.C1Input.InitialSelectionEnum.CaretAtStart
        Me.DateRyBirthday.Location = New System.Drawing.Point(3, 29)
        Me.DateRyBirthday.MaximumSize = New System.Drawing.Size(100000000, 23)
        Me.DateRyBirthday.MinimumSize = New System.Drawing.Size(0, 20)
        Me.DateRyBirthday.Name = "DateRyBirthday"
        Me.DateRyBirthday.ReadOnly = false
        Me.DateRyBirthday.Size = New System.Drawing.Size(234, 20)
        Me.DateRyBirthday.TabIndex = 2
        Me.DateRyBirthday.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.DateRyBirthday.ValueIsDbNull = false
        Me.DateRyBirthday.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.DateRyBirthday.VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.DropDown
        '
        'Label11
        '
        Me.Label11.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom)  _
            Or System.Windows.Forms.AnchorStyles.Left)  _
            Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.Label11.AutoSize = true
        Me.Label11.ForeColor = System.Drawing.SystemColors.ControlText
        Me.Label11.Location = New System.Drawing.Point(3, 0)
        Me.Label11.Name = "Label11"
        Me.Label11.Size = New System.Drawing.Size(54, 26)
        Me.Label11.TabIndex = 158
        Me.Label11.Text = "门诊编码"
        Me.Label11.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'lblMz_Code
        '
        Me.lblMz_Code.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.lblMz_Code.BackColor = System.Drawing.SystemColors.Info
        Me.lblMz_Code.Location = New System.Drawing.Point(63, 5)
        Me.lblMz_Code.Name = "lblMz_Code"
        Me.lblMz_Code.Size = New System.Drawing.Size(174, 16)
        Me.lblMz_Code.TabIndex = 159
        Me.lblMz_Code.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'GroupBox1
        '
        Me.GroupBox1.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left)  _
            Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.GroupBox1.Controls.Add(Me.C1ToolBar1)
        Me.GroupBox1.Controls.Add(Me.C1NumericEdit1)
        Me.GroupBox1.Controls.Add(Me.Label8)
        Me.GroupBox1.Controls.Add(Me.RadioButton4)
        Me.GroupBox1.Controls.Add(Me.RadioButton1)
        Me.GroupBox1.Controls.Add(Me.ComboYf1)
        Me.GroupBox1.Location = New System.Drawing.Point(723, 3)
        Me.GroupBox1.Name = "GroupBox1"
        Me.TableLayoutPanel1.SetRowSpan(Me.GroupBox1, 5)
        Me.GroupBox1.Size = New System.Drawing.Size(196, 110)
        Me.GroupBox1.TabIndex = 69
        Me.GroupBox1.TabStop = false
        Me.GroupBox1.Text = "药材类别"
        '
        'C1ToolBar1
        '
        Me.C1ToolBar1.AccessibleName = "Tool Bar"
        Me.C1ToolBar1.BackColor = System.Drawing.Color.FromArgb(CType(CType(239,Byte),Integer), CType(CType(239,Byte),Integer), CType(CType(239,Byte),Integer))
        Me.C1ToolBar1.Border.Style = C1.Win.C1Command.BorderStyleEnum.Ridge
        Me.C1ToolBar1.CommandHolder = Me.C1Holder1
        Me.C1ToolBar1.CommandLinks.AddRange(New C1.Win.C1Command.C1CommandLink() {Me.C1CommandLink1, Me.C1CommandLink2})
        Me.C1ToolBar1.Location = New System.Drawing.Point(138, 78)
        Me.C1ToolBar1.Name = "C1ToolBar1"
        Me.C1ToolBar1.Size = New System.Drawing.Size(52, 28)
        Me.C1ToolBar1.Text = "C1ToolBar1"
        Me.C1ToolBar1.VisualStyle = C1.Win.C1Command.VisualStyle.Custom
        Me.C1ToolBar1.VisualStyleBase = C1.Win.C1Command.VisualStyle.Classic
        '
        'C1Holder1
        '
        Me.C1Holder1.Commands.Add(Me.Control1)
        Me.C1Holder1.Commands.Add(Me.Control2)
        Me.C1Holder1.Commands.Add(Me.Control3)
        Me.C1Holder1.Commands.Add(Me.C1Command1)
        Me.C1Holder1.Commands.Add(Me.C1Command2)
        Me.C1Holder1.Owner = Me
        '
        'Control1
        '
        Me.Control1.HotFrame = C1.Win.C1Command.HotFrameEnum.None
        Me.Control1.Name = "Control1"
        Me.Control1.ShortcutText = ""
        Me.Control1.ShowShortcut = false
        Me.Control1.ShowTextAsToolTip = false
        '
        'Control2
        '
        Me.Control2.HotFrame = C1.Win.C1Command.HotFrameEnum.None
        Me.Control2.Name = "Control2"
        Me.Control2.ShortcutText = ""
        Me.Control2.ShowShortcut = false
        Me.Control2.ShowTextAsToolTip = false
        '
        'Control3
        '
        Me.Control3.HotFrame = C1.Win.C1Command.HotFrameEnum.None
        Me.Control3.Name = "Control3"
        Me.Control3.ShortcutText = ""
        Me.Control3.ShowShortcut = false
        Me.Control3.ShowTextAsToolTip = false
        '
        'C1Command1
        '
        Me.C1Command1.Name = "C1Command1"
        Me.C1Command1.ShortcutText = ""
        Me.C1Command1.Text = "乘"
        '
        'C1Command2
        '
        Me.C1Command2.Name = "C1Command2"
        Me.C1Command2.ShortcutText = ""
        Me.C1Command2.Text = "除"
        '
        'C1CommandLink1
        '
        Me.C1CommandLink1.ButtonLook = C1.Win.C1Command.ButtonLookFlags.Text
        Me.C1CommandLink1.Command = Me.C1Command1
        '
        'C1CommandLink2
        '
        Me.C1CommandLink2.ButtonLook = C1.Win.C1Command.ButtonLookFlags.Text
        Me.C1CommandLink2.Command = Me.C1Command2
        Me.C1CommandLink2.SortOrder = 1
        '
        'C1NumericEdit1
        '
        Me.C1NumericEdit1.AutoSize = false
        Me.C1NumericEdit1.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1NumericEdit1.ImagePadding = New System.Windows.Forms.Padding(0)
        Me.C1NumericEdit1.Location = New System.Drawing.Point(68, 83)
        Me.C1NumericEdit1.Name = "C1NumericEdit1"
        Me.C1NumericEdit1.Size = New System.Drawing.Size(64, 16)
        Me.C1NumericEdit1.TabIndex = 70
        Me.C1NumericEdit1.Tag = Nothing
        Me.C1NumericEdit1.TextAlign = System.Windows.Forms.HorizontalAlignment.Right
        Me.C1NumericEdit1.VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.None
        '
        'Label8
        '
        Me.Label8.AutoSize = true
        Me.Label8.Location = New System.Drawing.Point(10, 84)
        Me.Label8.Name = "Label8"
        Me.Label8.Size = New System.Drawing.Size(53, 12)
        Me.Label8.TabIndex = 69
        Me.Label8.Text = "草药副数"
        '
        'RadioButton4
        '
        Me.RadioButton4.AutoSize = true
        Me.RadioButton4.Location = New System.Drawing.Point(12, 59)
        Me.RadioButton4.Name = "RadioButton4"
        Me.RadioButton4.Size = New System.Drawing.Size(83, 16)
        Me.RadioButton4.TabIndex = 62
        Me.RadioButton4.Text = "&1.诊疗项目"
        Me.RadioButton4.UseVisualStyleBackColor = true
        '
        'RadioButton1
        '
        Me.RadioButton1.AutoSize = true
        Me.RadioButton1.Checked = true
        Me.RadioButton1.Location = New System.Drawing.Point(104, 59)
        Me.RadioButton1.Name = "RadioButton1"
        Me.RadioButton1.Size = New System.Drawing.Size(83, 16)
        Me.RadioButton1.TabIndex = 62
        Me.RadioButton1.TabStop = true
        Me.RadioButton1.Text = "&2.药品卫材"
        Me.RadioButton1.UseVisualStyleBackColor = true
        '
        'ComboYf1
        '
        Me.ComboYf1.Bookmark = -1
        Me.ComboYf1.Captain = "药    房"
        Me.ComboYf1.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.ComboYf1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.ComboYf1.CaptainWidth = 70!
        Me.ComboYf1.ColumnCaptionHeight = 18
        Me.ComboYf1.DataSource = Nothing
        Me.ComboYf1.DataView = Nothing
        Me.ComboYf1.ItemHeight = 16
        Me.ComboYf1.ItemTextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.ComboYf1.Location = New System.Drawing.Point(6, 20)
        Me.ComboYf1.MaximumSize = New System.Drawing.Size(10000, 23)
        Me.ComboYf1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.ComboYf1.Name = "ComboYf1"
        Me.ComboYf1.ReadOnly = false
        Me.ComboYf1.Row = 0
        Me.ComboYf1.Size = New System.Drawing.Size(184, 23)
        Me.ComboYf1.TabIndex = 180
        Me.ComboYf1.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        '
        'NlC1NumericEdit
        '
        Me.NlC1NumericEdit.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.NlC1NumericEdit.Captain = "年    龄"
        Me.NlC1NumericEdit.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.NlC1NumericEdit.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.NlC1NumericEdit.CaptainWidth = 40!
        Me.TableLayoutPanel1.SetColumnSpan(Me.NlC1NumericEdit, 2)
        Me.NlC1NumericEdit.Location = New System.Drawing.Point(363, 29)
        Me.NlC1NumericEdit.MaximumSize = New System.Drawing.Size(100000, 80)
        Me.NlC1NumericEdit.MinimumSize = New System.Drawing.Size(0, 20)
        Me.NlC1NumericEdit.Name = "NlC1NumericEdit"
        Me.NlC1NumericEdit.NumericInputKeys = CType(((((C1.Win.C1Input.NumericInputKeyFlags.F9 Or C1.Win.C1Input.NumericInputKeyFlags.Minus)  _
            Or C1.Win.C1Input.NumericInputKeyFlags.Plus)  _
            Or C1.Win.C1Input.NumericInputKeyFlags.[Decimal])  _
            Or C1.Win.C1Input.NumericInputKeyFlags.X),C1.Win.C1Input.NumericInputKeyFlags)
        Me.NlC1NumericEdit.NumFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.NlC1NumericEdit.ReadOnly = false
        Me.NlC1NumericEdit.Size = New System.Drawing.Size(114, 20)
        Me.NlC1NumericEdit.TabIndex = 4
        Me.NlC1NumericEdit.ValueIsDbNull = true
        '
        'ComboJb1
        '
        Me.ComboJb1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.ComboJb1.Bookmark = -1
        Me.ComboJb1.Captain = "疾病诊断"
        Me.ComboJb1.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.ComboJb1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.ComboJb1.CaptainWidth = 70!
        Me.ComboJb1.ColumnCaptionHeight = 18
        Me.TableLayoutPanel1.SetColumnSpan(Me.ComboJb1, 3)
        Me.ComboJb1.DataSource = Nothing
        Me.ComboJb1.DataView = Nothing
        Me.ComboJb1.ItemHeight = 16
        Me.ComboJb1.ItemTextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.ComboJb1.Location = New System.Drawing.Point(483, 81)
        Me.ComboJb1.MaximumSize = New System.Drawing.Size(10000, 23)
        Me.ComboJb1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.ComboJb1.Name = "ComboJb1"
        Me.ComboJb1.ReadOnly = false
        Me.ComboJb1.Row = 0
        Me.ComboJb1.Size = New System.Drawing.Size(234, 20)
        Me.ComboJb1.TabIndex = 12
        Me.ComboJb1.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        '
        'ComboYs1
        '
        Me.ComboYs1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.ComboYs1.Bookmark = -1
        Me.ComboYs1.Captain = "医    生"
        Me.ComboYs1.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.ComboYs1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.ComboYs1.CaptainWidth = 70!
        Me.ComboYs1.ColumnCaptionHeight = 18
        Me.TableLayoutPanel1.SetColumnSpan(Me.ComboYs1, 2)
        Me.ComboYs1.DataSource = Nothing
        Me.ComboYs1.DataView = Nothing
        Me.ComboYs1.ItemHeight = 16
        Me.ComboYs1.ItemTextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.ComboYs1.Location = New System.Drawing.Point(3, 81)
        Me.ComboYs1.MaximumSize = New System.Drawing.Size(10000, 23)
        Me.ComboYs1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.ComboYs1.Name = "ComboYs1"
        Me.ComboYs1.ReadOnly = false
        Me.ComboYs1.Row = 0
        Me.ComboYs1.Size = New System.Drawing.Size(234, 20)
        Me.ComboYs1.TabIndex = 10
        Me.ComboYs1.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        '
        'ComboKs1
        '
        Me.ComboKs1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.ComboKs1.Bookmark = -1
        Me.ComboKs1.Captain = "科    室"
        Me.ComboKs1.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.ComboKs1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.ComboKs1.CaptainWidth = 70!
        Me.ComboKs1.ColumnCaptionHeight = 18
        Me.TableLayoutPanel1.SetColumnSpan(Me.ComboKs1, 4)
        Me.ComboKs1.DataSource = Nothing
        Me.ComboKs1.DataView = Nothing
        Me.ComboKs1.ItemHeight = 16
        Me.ComboKs1.ItemTextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.ComboKs1.Location = New System.Drawing.Point(243, 81)
        Me.ComboKs1.MaximumSize = New System.Drawing.Size(10000, 23)
        Me.ComboKs1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.ComboKs1.Name = "ComboKs1"
        Me.ComboKs1.ReadOnly = false
        Me.ComboKs1.Row = 0
        Me.ComboKs1.Size = New System.Drawing.Size(234, 20)
        Me.ComboKs1.TabIndex = 11
        Me.ComboKs1.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        '
        'SingleSex1
        '
        Me.SingleSex1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.SingleSex1.Captain = "性    别"
        Me.SingleSex1.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.SingleSex1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.SingleSex1.CaptainWidth = 59!
        Me.TableLayoutPanel1.SetColumnSpan(Me.SingleSex1, 2)
        Me.SingleSex1.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        Me.SingleSex1.ItemHeight = 16
        Me.SingleSex1.ItemTextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.SingleSex1.Location = New System.Drawing.Point(243, 29)
        Me.SingleSex1.MaximumSize = New System.Drawing.Size(10000, 23)
        Me.SingleSex1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.SingleSex1.Name = "SingleSex1"
        Me.SingleSex1.ReadOnly = false
        Me.SingleSex1.Size = New System.Drawing.Size(114, 20)
        Me.SingleSex1.TabIndex = 3
        Me.SingleSex1.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        '
        'XmTextBox
        '
        Me.XmTextBox.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.XmTextBox.Captain = "病人姓名"
        Me.XmTextBox.CaptainBackColor = System.Drawing.Color.Transparent
        Me.XmTextBox.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.XmTextBox.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.XmTextBox.CaptainWidth = 70!
        Me.TableLayoutPanel1.SetColumnSpan(Me.XmTextBox, 4)
        Me.XmTextBox.ContentForeColor = System.Drawing.Color.Black
        Me.XmTextBox.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer))
        Me.XmTextBox.EditMask = Nothing
        Me.XmTextBox.Location = New System.Drawing.Point(243, 3)
        Me.XmTextBox.Multiline = false
        Me.XmTextBox.Name = "XmTextBox"
        Me.XmTextBox.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.XmTextBox.ReadOnly = false
        Me.XmTextBox.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.XmTextBox.SelectionStart = 0
        Me.XmTextBox.SelectStart = 0
        Me.XmTextBox.Size = New System.Drawing.Size(234, 20)
        Me.XmTextBox.TabIndex = 0
        Me.XmTextBox.TextAlign = System.Windows.Forms.HorizontalAlignment.Left
        Me.XmTextBox.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.XmTextBox.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.XmTextBox.Watermark = Nothing
        '
        'YlCodeTextBox
        '
        Me.YlCodeTextBox.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.YlCodeTextBox.Captain = "医保卡号"
        Me.YlCodeTextBox.CaptainBackColor = System.Drawing.Color.Transparent
        Me.YlCodeTextBox.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.YlCodeTextBox.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.YlCodeTextBox.CaptainWidth = 70!
        Me.TableLayoutPanel1.SetColumnSpan(Me.YlCodeTextBox, 3)
        Me.YlCodeTextBox.ContentForeColor = System.Drawing.Color.Black
        Me.YlCodeTextBox.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer))
        Me.YlCodeTextBox.EditMask = Nothing
        Me.YlCodeTextBox.Location = New System.Drawing.Point(483, 55)
        Me.YlCodeTextBox.Multiline = false
        Me.YlCodeTextBox.Name = "YlCodeTextBox"
        Me.YlCodeTextBox.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.YlCodeTextBox.ReadOnly = false
        Me.YlCodeTextBox.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.YlCodeTextBox.SelectionStart = 0
        Me.YlCodeTextBox.SelectStart = 0
        Me.YlCodeTextBox.Size = New System.Drawing.Size(234, 20)
        Me.YlCodeTextBox.TabIndex = 8
        Me.YlCodeTextBox.TextAlign = System.Windows.Forms.HorizontalAlignment.Left
        Me.YlCodeTextBox.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.YlCodeTextBox.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.YlCodeTextBox.Watermark = Nothing
        '
        'ComboBxlb1
        '
        Me.ComboBxlb1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.ComboBxlb1.Bookmark = -1
        Me.ComboBxlb1.Captain = "患者类别"
        Me.ComboBxlb1.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.ComboBxlb1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.ComboBxlb1.CaptainWidth = 70!
        Me.ComboBxlb1.ColumnCaptionHeight = 18
        Me.TableLayoutPanel1.SetColumnSpan(Me.ComboBxlb1, 4)
        Me.ComboBxlb1.DataSource = Nothing
        Me.ComboBxlb1.DataView = Nothing
        Me.ComboBxlb1.ItemHeight = 16
        Me.ComboBxlb1.ItemTextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.ComboBxlb1.Location = New System.Drawing.Point(243, 55)
        Me.ComboBxlb1.MaximumSize = New System.Drawing.Size(10000, 23)
        Me.ComboBxlb1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.ComboBxlb1.Name = "ComboBxlb1"
        Me.ComboBxlb1.ReadOnly = false
        Me.ComboBxlb1.Row = 0
        Me.ComboBxlb1.Size = New System.Drawing.Size(234, 20)
        Me.ComboBxlb1.TabIndex = 7
        Me.ComboBxlb1.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        '
        'TelText
        '
        Me.TelText.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.TelText.Captain = "联系电话"
        Me.TelText.CaptainBackColor = System.Drawing.Color.Transparent
        Me.TelText.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.TelText.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.TelText.CaptainWidth = 70!
        Me.TableLayoutPanel1.SetColumnSpan(Me.TelText, 2)
        Me.TelText.ContentForeColor = System.Drawing.Color.Black
        Me.TelText.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer), CType(CType(183,Byte),Integer))
        Me.TelText.EditMask = Nothing
        Me.TelText.Location = New System.Drawing.Point(3, 55)
        Me.TelText.Multiline = false
        Me.TelText.Name = "TelText"
        Me.TelText.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.TelText.ReadOnly = false
        Me.TelText.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.TelText.SelectionStart = 0
        Me.TelText.SelectStart = 0
        Me.TelText.Size = New System.Drawing.Size(234, 20)
        Me.TelText.TabIndex = 6
        Me.TelText.TextAlign = System.Windows.Forms.HorizontalAlignment.Left
        Me.TelText.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.TelText.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.TelText.Watermark = Nothing
        '
        'KMoneyLabel
        '
        Me.KMoneyLabel.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left),System.Windows.Forms.AnchorStyles)
        Me.KMoneyLabel.AutoSize = true
        Me.KMoneyLabel.Font = New System.Drawing.Font("宋体", 12!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.KMoneyLabel.ForeColor = System.Drawing.Color.FromArgb(CType(CType(128,Byte),Integer), CType(CType(64,Byte),Integer), CType(CType(0,Byte),Integer))
        Me.KMoneyLabel.Location = New System.Drawing.Point(925, 36)
        Me.KMoneyLabel.Name = "KMoneyLabel"
        Me.KMoneyLabel.Size = New System.Drawing.Size(59, 16)
        Me.KMoneyLabel.TabIndex = 178
        Me.KMoneyLabel.Text = "卡余额"
        Me.KMoneyLabel.Visible = false
        '
        'C1Combo8
        '
        Me.C1Combo8.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.C1Combo8.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1Combo8.Caption = ""
        Me.C1Combo8.CaptionHeight = 17
        Me.C1Combo8.CharacterCasing = System.Windows.Forms.CharacterCasing.Normal
        Me.C1Combo8.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.C1Combo8.Images.Add(CType(resources.GetObject("C1Combo8.Images"),System.Drawing.Image))
        Me.C1Combo8.ItemHeight = 15
        Me.C1Combo8.Location = New System.Drawing.Point(925, 5)
        Me.C1Combo8.MatchEntryTimeout = CType(2000,Long)
        Me.C1Combo8.MaxDropDownItems = CType(5,Short)
        Me.C1Combo8.MaxLength = 32767
        Me.C1Combo8.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.C1Combo8.Name = "C1Combo8"
        Me.C1Combo8.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.C1Combo8.Size = New System.Drawing.Size(201, 16)
        Me.C1Combo8.TabIndex = 0
        Me.C1Combo8.PropBag = resources.GetString("C1Combo8.PropBag")
        '
        'Panel2
        '
        Me.Panel2.Controls.Add(Me.BtnSaveTemplate)
        Me.Panel2.Controls.Add(Me.T_Label5)
        Me.Panel2.Controls.Add(Me.Label1)
        Me.Panel2.Controls.Add(Me.Comm3)
        Me.Panel2.Controls.Add(Me.Comm2)
        Me.Panel2.Controls.Add(Me.Comm1)
        Me.Panel2.Controls.Add(Me.Label16)
        Me.Panel2.Controls.Add(Me.T_Line2)
        Me.Panel2.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.Panel2.Location = New System.Drawing.Point(0, 614)
        Me.Panel2.Name = "Panel2"
        Me.Panel2.Size = New System.Drawing.Size(1129, 30)
        Me.Panel2.TabIndex = 40
        '
        'BtnSaveTemplate
        '
        Me.BtnSaveTemplate.Location = New System.Drawing.Point(476, 3)
        Me.BtnSaveTemplate.Name = "BtnSaveTemplate"
        Me.BtnSaveTemplate.Size = New System.Drawing.Size(75, 24)
        Me.BtnSaveTemplate.TabIndex = 61
        Me.BtnSaveTemplate.Tag = "保存成模版"
        Me.BtnSaveTemplate.Text = "保存成模版"
        Me.BtnSaveTemplate.UseVisualStyleBackColor = false
        '
        'T_Label5
        '
        Me.T_Label5.AutoSize = true
        Me.T_Label5.BackColor = System.Drawing.SystemColors.ButtonFace
        Me.T_Label5.ForeColor = System.Drawing.Color.DarkRed
        Me.T_Label5.Location = New System.Drawing.Point(584, 10)
        Me.T_Label5.Name = "T_Label5"
        Me.T_Label5.Size = New System.Drawing.Size(53, 12)
        Me.T_Label5.TabIndex = 52
        Me.T_Label5.Text = "T_Label5"
        Me.T_Label5.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Label1
        '
        Me.Label1.AutoSize = true
        Me.Label1.BackColor = System.Drawing.SystemColors.ButtonFace
        Me.Label1.ForeColor = System.Drawing.Color.DarkRed
        Me.Label1.Location = New System.Drawing.Point(10, 10)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(197, 12)
        Me.Label1.TabIndex = 50
        Me.Label1.Text = "诊疗快捷键F1、药品F2、数据结算F3"
        Me.Label1.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Comm3
        '
        Me.Comm3.Location = New System.Drawing.Point(397, 3)
        Me.Comm3.Name = "Comm3"
        Me.Comm3.Size = New System.Drawing.Size(79, 24)
        Me.Comm3.TabIndex = 49
        Me.Comm3.Tag = "数据结算"
        Me.Comm3.Text = "数据结算"
        Me.Comm3.UseVisualStyleBackColor = false
        '
        'Comm1
        '
        Me.Comm1.Location = New System.Drawing.Point(297, 3)
        Me.Comm1.Name = "Comm1"
        Me.Comm1.Size = New System.Drawing.Size(50, 24)
        Me.Comm1.TabIndex = 45
        Me.Comm1.Tag = "保存"
        Me.Comm1.Text = "保存"
        Me.Comm1.UseVisualStyleBackColor = false
        '
        'Label16
        '
        Me.Label16.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.Label16.Location = New System.Drawing.Point(287, -4)
        Me.Label16.Name = "Label16"
        Me.Label16.Size = New System.Drawing.Size(2, 40)
        Me.Label16.TabIndex = 38
        '
        'T_Line2
        '
        Me.T_Line2.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.T_Line2.Location = New System.Drawing.Point(560, -5)
        Me.T_Line2.Name = "T_Line2"
        Me.T_Line2.Size = New System.Drawing.Size(2, 40)
        Me.T_Line2.TabIndex = 29
        '
        'C1CommandDock1
        '
        Me.C1CommandDock1.Controls.Add(Me.C1DockingTab1)
        Me.C1CommandDock1.Dock = System.Windows.Forms.DockStyle.Right
        Me.C1CommandDock1.Id = 1
        Me.C1CommandDock1.Location = New System.Drawing.Point(634, 143)
        Me.C1CommandDock1.Name = "C1CommandDock1"
        Me.C1CommandDock1.Size = New System.Drawing.Size(495, 471)
        '
        'C1DockingTab1
        '
        Me.C1DockingTab1.Alignment = System.Windows.Forms.TabAlignment.Right
        Me.C1DockingTab1.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1DockingTab1.CanAutoHide = true
        Me.C1DockingTab1.CanMoveTabs = true
        Me.C1DockingTab1.Controls.Add(Me.C1DockingTabPage1)
        Me.C1DockingTab1.Location = New System.Drawing.Point(0, 0)
        Me.C1DockingTab1.Name = "C1DockingTab1"
        Me.C1DockingTab1.ShowCaption = true
        Me.C1DockingTab1.Size = New System.Drawing.Size(495, 471)
        Me.C1DockingTab1.TabIndex = 0
        Me.C1DockingTab1.TabSizeMode = C1.Win.C1Command.TabSizeModeEnum.Fit
        Me.C1DockingTab1.TabsSpacing = 5
        Me.C1DockingTab1.VisualStyle = C1.Win.C1Command.VisualStyle.Office2010Blue
        Me.C1DockingTab1.VisualStyleBase = C1.Win.C1Command.VisualStyle.Office2010Blue
        '
        'C1DockingTabPage1
        '
        Me.C1DockingTabPage1.CaptionVisible = true
        Me.C1DockingTabPage1.Controls.Add(Me.MzData1)
        Me.C1DockingTabPage1.Font = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.C1DockingTabPage1.Location = New System.Drawing.Point(3, 0)
        Me.C1DockingTabPage1.Name = "C1DockingTabPage1"
        Me.C1DockingTabPage1.Size = New System.Drawing.Size(467, 471)
        Me.C1DockingTabPage1.TabIndex = 0
        Me.C1DockingTabPage1.Text = "门诊辅助"
        '
        'MzData1
        '
        Me.MzData1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.MzData1.Location = New System.Drawing.Point(0, 23)
        Me.MzData1.Margin = New System.Windows.Forms.Padding(0)
        Me.MzData1.MzCfZxType = ZTHisEnum.MzCfZxType.正常
        Me.MzData1.Name = "MzData1"
        Me.MzData1.Size = New System.Drawing.Size(467, 448)
        Me.MzData1.TabIndex = 0
        '
        'ToolBarReadCard1
        '
        Me.ToolBarReadCard1.AutoSize = true
        Me.ToolBarReadCard1.Dock = System.Windows.Forms.DockStyle.Top
        Me.ToolBarReadCard1.Font = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.ToolBarReadCard1.Location = New System.Drawing.Point(0, 0)
        Me.ToolBarReadCard1.Name = "ToolBarReadCard1"
        Me.ToolBarReadCard1.Size = New System.Drawing.Size(1129, 28)
        Me.ToolBarReadCard1.TabIndex = 41
        '
        'Xs_Mz2
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6!, 12!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.CancelButton = Me.Comm2
        Me.ClientSize = New System.Drawing.Size(1129, 644)
        Me.Controls.Add(Me.C1TrueDBGrid1)
        Me.Controls.Add(Me.C1CommandDock1)
        Me.Controls.Add(Me.Panel2)
        Me.Controls.Add(Me.Panel1)
        Me.Controls.Add(Me.ToolBarReadCard1)
        Me.Icon = CType(resources.GetObject("$this.Icon"),System.Drawing.Icon)
        Me.Name = "Xs_Mz2"
        Me.ShowInTaskbar = false
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "门诊录入"
        CType(Me.C1TrueDBGrid1,System.ComponentModel.ISupportInitialize).EndInit
        Me.Panel1.ResumeLayout(false)
        Me.TableLayoutPanel1.ResumeLayout(false)
        Me.TableLayoutPanel1.PerformLayout
        Me.GroupBox1.ResumeLayout(false)
        Me.GroupBox1.PerformLayout
        CType(Me.C1Holder1,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.C1NumericEdit1,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.C1Combo8,System.ComponentModel.ISupportInitialize).EndInit
        Me.Panel2.ResumeLayout(false)
        Me.Panel2.PerformLayout
        CType(Me.C1CommandDock1,System.ComponentModel.ISupportInitialize).EndInit
        Me.C1CommandDock1.ResumeLayout(false)
        CType(Me.C1DockingTab1,System.ComponentModel.ISupportInitialize).EndInit
        Me.C1DockingTab1.ResumeLayout(false)
        Me.C1DockingTabPage1.ResumeLayout(false)
        Me.ResumeLayout(false)
        Me.PerformLayout

End Sub
    Friend WithEvents C1Holder1 As C1.Win.C1Command.C1CommandHolder
    Friend WithEvents Control1 As C1.Win.C1Command.C1CommandControl
    Friend WithEvents Control2 As C1.Win.C1Command.C1CommandControl
    Friend WithEvents Control3 As C1.Win.C1Command.C1CommandControl
    Friend WithEvents Panel2 As System.Windows.Forms.Panel
    Friend WithEvents T_Line2 As System.Windows.Forms.Label
    Friend WithEvents C1Command1 As C1.Win.C1Command.C1Command
    Friend WithEvents C1Command2 As C1.Win.C1Command.C1Command
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents GroupBox1 As System.Windows.Forms.GroupBox
    Friend WithEvents RadioButton4 As System.Windows.Forms.RadioButton
    Friend WithEvents RadioButton1 As System.Windows.Forms.RadioButton
    Friend WithEvents lblMz_Code As System.Windows.Forms.Label
    Friend WithEvents Label11 As System.Windows.Forms.Label
    Friend WithEvents C1TrueDBGrid1 As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents Label16 As System.Windows.Forms.Label
    Friend WithEvents C1ToolBar1 As C1.Win.C1Command.C1ToolBar
    Friend WithEvents C1CommandLink1 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents C1CommandLink2 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents C1NumericEdit1 As C1.Win.C1Input.C1NumericEdit
    Friend WithEvents Label8 As System.Windows.Forms.Label
    Friend WithEvents Comm2 As System.Windows.Forms.Button
    Friend WithEvents Comm1 As System.Windows.Forms.Button
    Friend WithEvents Comm3 As System.Windows.Forms.Button
    Friend WithEvents C1Combo8 As C1.Win.C1List.C1Combo
    Friend WithEvents TableLayoutPanel1 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents T_Label5 As System.Windows.Forms.Label
    Friend WithEvents KMoneyLabel As System.Windows.Forms.Label
    Friend WithEvents NlC1NumericEdit As CustomControl.MyNumericEdit
    Friend WithEvents ToolBarReadCard1 As ZTHisPublicForm.ToolBarReadCard
    Friend WithEvents DateRyBirthday As CustomControl.MyDateEdit
    Friend WithEvents TelText As CustomControl.MyTextBox
    Friend WithEvents ComboJb1 As ZTHisControl.ComboJb
    Friend WithEvents ComboYs1 As ZTHisControl.ComboYs
    Friend WithEvents ComboKs1 As ZTHisControl.ComboKs
    Friend WithEvents ComboBxlb1 As ZTHisControl.ComboBxlb
    Friend WithEvents SingleSex1 As ZTHisControl.SingleSex
    Friend WithEvents ComboYf1 As ZTHisControl.ComboYf
    Friend WithEvents SfzhTextBox As CustomControl.MyTextBox
    Friend WithEvents AddressTextBox As CustomControl.MyTextBox
    Friend WithEvents XmTextBox As CustomControl.MyTextBox
    Friend WithEvents YlCodeTextBox As CustomControl.MyTextBox
    Friend WithEvents C1CommandDock1 As C1.Win.C1Command.C1CommandDock
    Friend WithEvents C1DockingTab1 As C1.Win.C1Command.C1DockingTab
    Friend WithEvents C1DockingTabPage1 As C1.Win.C1Command.C1DockingTabPage
    Friend WithEvents MzData1 As ZTHisOutpatient.MzData
    Friend WithEvents BtnSaveTemplate As Button
End Class
