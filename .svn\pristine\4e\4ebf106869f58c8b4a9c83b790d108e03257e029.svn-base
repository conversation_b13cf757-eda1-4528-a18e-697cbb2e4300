﻿Imports System.Data.SqlClient
Imports System.Windows.Forms
Imports System.Drawing
Public Class JySfDy
#Region "初始化"
    Dim My_Dataset As New DataSet
    Dim My_View As New DataView
    Public My_Table As New DataTable
    Public My_Cm As CurrencyManager
    Public My_Row As DataRow
    Public V_FirstLoad As Boolean

    Dim BllXmDy As New BLLOld.B_LIS_TestXmDy
    Dim BllTestXm As New BLLOld.B_LIS_TestXm
    Dim BllXm As New BLLOld.B_Zd_Ml_Xm3

    Dim ModelXmDy As New ModelOld.M_LIS_TestXmDy

    Dim m_Rc As New BaseClass.C_RowChange


#End Region

    Private Sub JySfDy_load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Call Form_Init()
        Call Init_Data()
    End Sub

#Region "窗口初始化"
    Private Sub Form_Init()

        With MyGrid1
            .Init_Column("检验项目", "TestXm_Name", 500, "左", "", False)
            .Init_Column("收费项目", "Xm_Name", 500, "左", "", False)
        End With

        With Me.MyGrid1
            '.Splits(0).DisplayColumns(0).Merge = C1.Win.C1TrueDBGrid.ColumnMergeEnum.Free
            '.Splits(0).DisplayColumns(1).Merge = C1.Win.C1TrueDBGrid.ColumnMergeEnum.Free
            .Splits(0).DisplayColumns(0).Style.VerticalAlignment = C1.Win.C1TrueDBGrid.AlignVertEnum.Center
            .Splits(0).DisplayColumns(0).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Near
            .Splits(0).DisplayColumns(1).Style.VerticalAlignment = C1.Win.C1TrueDBGrid.AlignVertEnum.Center
            .Splits(0).DisplayColumns(1).Style.HorizontalAlignment = C1.Win.C1TrueDBGrid.AlignHorzEnum.Near
        End With

        With SfXmCb
            .DataView = BllXm.GetAllList().Tables(0).DefaultView
            .Init_Colum("Xm_Code", "编码", 0, "左")
            .Init_Colum("Xm_Name", "收费项目", 120, "左")
            .RowFilterNotTextNull = "Xm_Jc"
            .DisplayMember = "Xm_Name"
            .DroupDownWidth = 500
            .MaxDropDownItems = 15
            .SelectedValue = -1
        End With

        With TestXmCb
            .DataView = BllTestXm.GetAllList().Tables(0).DefaultView
            .Init_Colum("TestXm_Code", "编码", 0, "左")
            .Init_Colum("TestXm_Name", "检验项目", 120, "左")
            .RowFilterNotTextNull = "TestXm_Jc"
            .DisplayMember = "TestXm_Name"
            .DroupDownWidth = 500
            .MaxDropDownItems = 15
            .SelectedValue = -1
        End With

    End Sub

    Private Sub Init_Data()
        My_Dataset = BllXmDy.GetAllList()
        My_Table = My_Dataset.Tables(0)
        My_Table.PrimaryKey = New DataColumn() {My_Table.Columns("TestXm_Code"), My_Table.Columns("Xm_Code")}

        With Me.MyGrid1
            My_Cm = CType(BindingContext(My_Table, ""), CurrencyManager)
            .DataTable = My_Table
        End With
        My_View = My_Cm.List
        My_View.Sort = "TestXm_Code"
    End Sub

    Private Sub JYYQGLDic11_FormClosing(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosingEventArgs) Handles Me.FormClosing
        My_Dataset.Dispose()
    End Sub

#End Region

#Region "控件__动作"

    Private Sub C1TrueDBGrid1_RowColChange_1(ByVal sender As System.Object, ByVal e As C1.Win.C1TrueDBGrid.RowColChangeEventArgs) Handles MyGrid1.RowColChange
        If (MyGrid1.Row + 1) > MyGrid1.RowCount Then
        Else
            My_Row = My_Cm.List(MyGrid1.Row).Row
            m_Rc.ChangeRow(My_Row)
        End If
    End Sub

    Private Sub MyButton1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles TjDyBt.Click, DelDyBt.Click
        Select Case sender.tag
            Case "添加"
                If TestXmCb.Text = "" Then
                    Beep()
                    MsgBox("请选择检验项目", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示")
                    TestXmCb.Select()
                ElseIf SfXmCb.Text = "" Then
                    Beep()
                    MsgBox("请选择收费项目", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示")
                    SfXmCb.Select()
                Else
                    Dim IfhaveDy As Int32
                    IfhaveDy = BllXmDy.GetList(" Xm_Name='" & SfXmCb.Text & "' AND TestXm_Name='" & TestXmCb.Text & "'").Tables(0).Rows.Count()

                    If IfhaveDy = 0 Then
                        Data_Add()
                    ElseIf IfhaveDy <> 0 Then
                        Beep()
                        MsgBox("该对应关系已存在", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示")
                        SfXmCb.Select()
                    End If
                End If
            Case "删除"
                Data_Del()
        End Select
        Init_Data()
    End Sub

#End Region

#Region "自定义函数"

    Private Sub Data_Add()
        Dim My_NewRow As DataRow = My_Table.NewRow
        With My_NewRow
            .Item("Xm_Code") = BllXm.GetList("Xm_Name='" & Trim(SfXmCb.Text & "") & "'").Tables(0).Rows(0).Item("Xm_Code").ToString()
            .Item("TestXm_Code") = BllTestXm.GetList("TestXm_Name='" & Trim(TestXmCb.Text & "") & "'").Tables(0).Rows(0).Item("TestXm_Code").ToString()
        End With

        Try
            My_Table.Rows.Add(My_NewRow)
            m_Rc.GridMove("最后")
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            Exit Sub
        Finally
            SfXmCb.Select()
        End Try

        Try
            With ModelXmDy
                .Xm_Code = My_NewRow("Xm_Code")
                .TestXm_Code = My_NewRow("TestXm_Code")
            End With
            BllXmDy.Add(ModelXmDy)
            My_NewRow.AcceptChanges()
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            Exit Sub
        Finally
            SfXmCb.Select()
        End Try
    End Sub



    Private Sub Data_Del()
        Beep()
        If MsgBox("是否删除-" & Me.MyGrid1.Columns("TestXm_Name").Value & "-与-" & Me.MyGrid1.Columns("Xm_Name").Value & "-的对应关系", MsgBoxStyle.Information + MsgBoxStyle.OkCancel + MsgBoxStyle.DefaultButton1, "提示:") = MsgBoxResult.Cancel Then Exit Sub
        My_Row = My_Cm.List(MyGrid1.Row).Row

        Try
            BllXmDy.Delete(My_Row("Xm_Code"), My_Row("TestXm_Code"))
            MyGrid1.Delete()
            My_Row.AcceptChanges()
        Catch ex As Exception
            Beep()
            MsgBox("错误:" + ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
        Finally
            MyGrid1.Select()
        End Try
    End Sub

#End Region

#Region "输入法设置"
    '中文
    Private Sub C1TextBox1_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles SfXmCb.GotFocus, TestXmCb.GotFocus
        InputLanguage.CurrentInputLanguage = HisVar.HisVar.InputCode
    End Sub

    '英文
    Private Sub C1TextBox2_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs)
        InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages(0)
    End Sub
#End Region


End Class