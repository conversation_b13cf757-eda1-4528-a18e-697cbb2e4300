﻿Imports System.Data.SqlClient
Imports HisControl

Public Class Xs_Mz34

#Region "定义变量"

    Dim My_DataSet As New DataSet
    Dim YbzlXm_Str As String = "门诊输液诊查费,普通门诊诊查费,住院诊查费,门诊输液,静脉输液自第,门诊留观诊查费,门急诊留观诊查费,注射费,门急诊观诊留观诊查费,挂号,挂号费,肌肉（皮下）,肌肉注射,肌肉注射（皮试）,肌肉注射（新生儿）,肌肉注射费,急诊门留观诊查费一般输液,急诊诊查费,急诊诊察费,静脉输液,静脉输液(使用微量泵),静脉输液(使用微量泵或输液泵加收）,静脉输液(自第二瓶起),静脉输液（自第二瓶起加收）,静脉输液（自第二瓶起每瓶收）,静脉输液（自第二瓶起每瓶收）1,静脉输液（自第二瓶起每瓶收）2,静脉注射（小儿静脉采血）,静脉注射费,门急诊留观诊查费一般输液,门诊挂号费,门诊观察费,门诊急诊费,门诊输液费,门诊诊查费,皮内注射,皮试,皮下输液,皮下注射,普通诊查费,小儿静脉输液,小儿静脉输液（自第二瓶起）,小儿头皮静脉输液,小儿头皮输液,小儿头皮输液费,诊查费,诊察费1,诊费,诊费1,诊费2,诊费3,诊费4,诊费5,注射费1,专家挂号费,专家门诊诊查费,专家诊费,肌肉注射（皮下）,肌肉注射（皮内）,静脉注射,静脉注射（小儿）,静脉注射（静脉采血）,皮下输液（自第二瓶起每瓶收）,静脉输液（使用微量泵或输液泵按小时加收）,静脉输液（输血）,小儿头皮静脉输液（自第二瓶起每瓶收）,小儿头皮静脉输液（使用微量泵或输液泵按小时加收）"

#End Region

#Region "传参"
    Dim Rform As BaseForm
    Dim Rinsert As Boolean
    Dim ZbRow As DataRow
    Dim Cbrow As DataRow
    Dim RZbtb As DataTable
    Dim Rtdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Dim Rzbadt As SqlDataAdapter

#End Region

    Public Sub New(ByVal tform As BaseForm, ByVal tinsert As Boolean, ByVal t_ZbRow As DataRow, ByVal t_Cbrow As DataRow, ByVal tZbtb As DataTable, ByVal ttdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid, ByVal tzbadt As SqlDataAdapter)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        Rform = tform
        Rinsert = tinsert
        ZbRow = t_ZbRow
        Cbrow = t_Cbrow
        RZbtb = tZbtb
        Rtdbgrid = ttdbgrid
        Rzbadt = tzbadt
        ' 在 InitializeComponent() 调用之后添加任何初始化。

    End Sub

    Protected Overrides Sub OnKeyDown(ByVal e As System.Windows.Forms.KeyEventArgs)
        MyBase.OnKeyDown(e)
        If e.Control = True And e.KeyCode = Keys.S Then
            Comm1.Select()
            Call Comm_Click(Comm1, Nothing)
        End If
    End Sub

    Private Sub Yf_Ck31_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load

        Call Form_Init()

        If Rinsert = True Then
            Call Data_Clear()
        Else
            Call Data_Show()
        End If

    End Sub

#Region "窗体__事件"

    Private Sub Form_Init()

        '按扭初始化
        Call P_Comm(Me.Comm1)
        Call P_Comm(Me.Comm2)


        With Me.C1Numeric1
            .Value = 1
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .CustomFormat = "###,###,###.####"
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .ShowContextMenu = False
            .EmptyAsNull = False
        End With

        With Me.C1Numeric2
            .Value = 0
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .CustomFormat = "###,###,##0.00##"
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .ShowContextMenu = False
            .EmptyAsNull = False
        End With



        With Me.C1Numeric4
            .Value = 0
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .CustomFormat = "###,###,##0.00##"
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .ShowContextMenu = False
            .EmptyAsNull = False
        End With

        If Rinsert = True Then
            HisVar.HisVar.Sqldal.QueryDt(My_DataSet, "SELECT Xm_Jc,Xm_Name,Zd_Ml_Xm3.Xm_Code,Xm_Dj,Xmlb_Name,Xm_Dw,'诊疗字典'as Lb,医疗目录编码  FROM Zd_Ml_Xm1,Zd_Ml_Xm3 Left Join Country_YBMLDZ_XM on Xm_Code=Mx_Code where Zd_Ml_Xm1.Xmlb_Code=Zd_Ml_Xm3.Xmlb_Code And IsEnable=1 union all SELECT Templet_Jc Xm_Jc,Templet_Name Xm_Name,Zd_Templet1.Templet_Code Xm_Code,isnull(Templet_NewDj,sum(Xm_Dj*mx_sl)) Xm_Dj,''as Xmlb_Name,'' as Xm_Dw,'诊疗模板'as Lb,''  FROM Zd_Templet1,Zd_Templet2,Zd_Ml_Xm3 where  Zd_Templet1.Templet_Code=Zd_Templet2.Templet_Code and Zd_Templet2.Mx_Code=Zd_ML_Xm3.Xm_Code  AND SUBSTRING(Templet_Name,LEN(Templet_Name)-2,LEN(Templet_Name)) != 'Lis'  group by Zd_Templet1.Templet_Code, Templet_Jc,Templet_name,Templet_NewDj order by Xm_Jc", "总项目", True)
        Else
            HisVar.HisVar.Sqldal.QueryDt(My_DataSet, "SELECT Xm_Jc,Xm_Name,Zd_Ml_Xm3.Xm_Code,Xm_Dj,Xmlb_Name,Xm_Dw,'诊疗字典'as Lb,医疗目录编码  FROM Zd_Ml_Xm1,Zd_Ml_Xm3 Left Join Country_YBMLDZ_XM on Xm_Code=Mx_Code where Zd_Ml_Xm1.Xmlb_Code=Zd_Ml_Xm3.Xmlb_Code And IsEnable=1 order by Xm_Jc", "总项目", True)
        End If


        Dim My_Combo As BaseClass.C_Combo2 = New BaseClass.C_Combo2(C1Combo1, My_DataSet.Tables("总项目").DefaultView, "Xm_Name", "Xm_Code", 500)
        With My_Combo
            .Init_TDBCombo()
            .Init_Colum("Xm_Jc", "简称", 100, "左")
            .Init_Colum("Xm_Name", "项目名称", 150, "左")
            .Init_Colum("Xm_Code", "编码", 0, "左")
            .Init_Colum("Xm_Dj", "单价", 50, "中")
            .Init_Colum("Xmlb_Name", "", 0, "中")
            .Init_Colum("Xm_Dw", "单位", 50, "中")
            .Init_Colum("Lb", "类别", 0, "中")

            .Init_Colum("医疗目录编码", "国家编码", 100, "左")
            .MaxDropDownItems(15)
            .SelectedIndex(-1)
        End With

        With C1Combo1
            .AutoCompletion = False
            .AutoSelect = False
        End With

        My_DataSet.Tables("总项目").DefaultView.Sort = "Xm_Jc Asc "
        Call Data_Clear()
    End Sub

#End Region

#Region "其它项目"

    Private Sub C1Numeric_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles C1Numeric1.KeyPress, C1Numeric2.KeyPress

        If e.KeyChar <> Chr(Keys.Return) Then Exit Sub
        e.Handled = True
        System.Windows.Forms.SendKeys.Send("{Tab}")
    End Sub

    Private Sub C1Numeric1_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1Numeric1.Validated, C1Numeric2.Validated
        C1Numeric4.Value = C1Numeric1.Value * C1Numeric2.Value
    End Sub

#End Region

#Region "控件__动作"

    Private Sub Comm_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.Click, Comm2.Click
        Select Case sender.tag
            Case "保存"

                If C1Combo1.SelectedValue = "" Then
                    Beep()
                    MsgBox("项目编码不能为空,按任意键返回!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    C1Combo1.Select()
                    Exit Sub
                End If
                If C1Numeric1.Value = 0 Then
                    Beep()
                    MsgBox("使用数量不能为零！", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示")
                    C1Numeric1.Select()
                    Exit Sub
                End If

                If HisPara.PublicConfig.XqName.Contains("海港") And HisVar.HisVar.Sqldal.GetSingle("Select isnull(Sum(Mz_Sl),0) from (Select  Mz_Sl from Zd_Ml_Xm3,Mz_Xm,Mz where Zd_Ml_Xm3.Xm_Code=Mz_Xm.Xm_Code and  Mz_Xm.Mz_Code=Mz.Mz_Code and Xm_Name Like '%一般诊疗%' and Ry_YlCode='" & ZbRow.Item("Ry_YlCode") & "' and  Not Exists(Select Mz_Code from Mz_Ty where Mz_Ty.Mz_Code=Mz.Mz_Code) and DATEDIFF(DAY,Mz_Date,Getdate())>=-2 and DATEDIFF(DAY,Mz_Date, Getdate())<=2 " &
                                                                                                                      "    Union All Select  Mz_Sl from Zd_Ml_Xm3,Mz_Xm_Sum,Mz_Sum where Zd_Ml_Xm3.Xm_Code=Mz_Xm_Sum.Xm_Code and  Mz_Xm_Sum.Mz_Code=Mz_Sum.Mz_Code and Xm_Name Like '%一般诊疗%' and Ry_YlCode='" & ZbRow.Item("Ry_YlCode") & "' and  Not Exists(Select Mz_Code from Mz_Ty_Sum where Mz_Ty_Sum.Mz_Code=Mz_Sum.Mz_Code) and DATEDIFF(DAY,Mz_Date,Getdate())>=-2 and DATEDIFF(DAY,Mz_Date, Getdate())<=2 ) A") > 0 And C1Combo1.Text.Contains("一般诊疗") And C1Numeric1.Value > 0 Then
                    MsgBox("该患者三天内使用过一般诊疗，不能再次使用！", MsgBoxStyle.Information, "提示:")
                    Exit Sub
                End If
                If HisPara.PublicConfig.XqName.Contains("丰润") And ZbRow.Item("Ry_YlCode") <> "" And HisVar.HisVar.Sqldal.GetSingle("Select isnull(Sum(Mz_Sl),0) from (Select  Mz_Sl from Zd_Ml_Xm3,Mz_Xm,Mz where Zd_Ml_Xm3.Xm_Code=Mz_Xm.Xm_Code and  Mz_Xm.Mz_Code=Mz.Mz_Code and Xm_Name Like '%一般诊疗%' and Ry_YlCode='" & ZbRow.Item("Ry_YlCode") & "' and  Not Exists(Select Mz_Code from Mz_Ty where Mz_Ty.Mz_Code=Mz.Mz_Code) and Mz_Date=CONVERT(varchar(10), Getdate(), 23) " &
                                                                                                                  "    Union All Select  Mz_Sl from Zd_Ml_Xm3,Mz_Xm_Sum,Mz_Sum where Zd_Ml_Xm3.Xm_Code=Mz_Xm_Sum.Xm_Code and  Mz_Xm_Sum.Mz_Code=Mz_Sum.Mz_Code and Xm_Name Like '%一般诊疗%' and Ry_YlCode='" & ZbRow.Item("Ry_YlCode") & "' and  Not Exists(Select Mz_Code from Mz_Ty_Sum where Mz_Ty_Sum.Mz_Code=Mz_Sum.Mz_Code) and Mz_Date=CONVERT(varchar(10), Getdate(), 23) ) A") > 0 And C1Combo1.Text.Contains("一般诊疗") And C1Numeric1.Value > 0 Then
                    MsgBox("该患者今天内使用过一般诊疗，不能再次使用！", MsgBoxStyle.Information, "提示:")
                    Exit Sub
                End If
                If Rinsert = True Then      '增加记录
                    Call Save_Add()
                Else                                '编辑记录
                    Call Save_Edit()
                    MsgBox("修改完成！", MsgBoxStyle.Information, "提示：")
                    Me.Close()
                End If

            Case "取消"
                C1Combo1.SelectedValue = -1
                C1Combo1.Text = ""
                Rtdbgrid.Focus()
                Me.Close()
        End Select
    End Sub

#Region "C1Combo1"

    Private Sub C1Combo1_RowChange(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1Combo1.RowChange

        If C1Combo1.WillChangeToValue = "" Then
            Label1.Text = ""
            C1Numeric2.Value = 0
        Else
            If HisPara.PublicConfig.MzXmDj_Change = "是" And C1Combo1.Columns("lb").Value = "诊疗字典" Then
                C1Numeric2.ReadOnly = False
                C1Numeric2.BackColor = Color.White
                C1Numeric2.TabStop = True
            Else
                C1Numeric2.ReadOnly = True
                C1Numeric2.BackColor = SystemColors.Info
                C1Numeric2.TabStop = False
            End If


            Label1.Text = C1Combo1.Columns("Xmlb_Name").Value


            If HisVar.HisVar.IsYbzl = "是" And (ZbRow.Item("Bxlb_Name") = "合作医疗" Or ZbRow.Item("Bxlb_Name") = "城乡居民") And ZbRow.Item("Ry_YlCode") <> "" And C1Combo1.Columns("lb").Value = "诊疗字典" Then
                If YbzlXm_Str.Contains(C1Combo1.Columns("Xm_Name").Value) Then
                    C1Numeric2.Value = 0
                Else
                    C1Numeric2.Value = C1Combo1.Columns("Xm_Dj").Value
                End If
            ElseIf HisPara.PublicConfig.XqName.Contains("乐亭") And (ZbRow.Item("Bxlb_Name") = "合作医疗" Or ZbRow.Item("Bxlb_Name") = "城乡居民") And ZbRow.Item("Ry_YlCode") <> "" And C1Combo1.Columns("lb").Value = "诊疗字典" Then
                If C1Combo1.Columns("Xm_Name").Value.ToString.Contains("一般诊疗") Then
                    C1Numeric2.Value = 0
                Else
                    C1Numeric2.Value = C1Combo1.Columns("Xm_Dj").Value
                End If
            Else
                C1Numeric2.Value = C1Combo1.Columns("Xm_Dj").Value
            End If
            C1Numeric4.Value = C1Numeric1.Value * C1Numeric2.Value
        End If

    End Sub

    Private Sub C1Combo1_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles C1Combo1.KeyUp

        If (e.KeyValue > 47 And e.KeyValue < 58) Or (e.KeyValue > 96 And e.KeyValue < 123) Or (e.KeyValue > 64 And e.KeyValue < 91) Or (e.KeyValue = 8) Or (e.KeyValue = 189) Or (e.KeyValue = 190) Then
            Dim s As String = C1Combo1.Text
            If C1Combo1.Text = "" Then
                C1Combo1.DataSource.RowFilter = "1=1"
            Else
                C1Combo1.DataSource.RowFilter = "Xm_Jc like '*" & C1Combo1.Text.Replace("*", "[*]") & "*'"
            End If

            If (e.KeyValue = 8) Then
                C1Combo1.DroppedDown = False
                C1Combo1.DroppedDown = True
            End If

            C1Combo1.Text = s
            C1Combo1.SelectionStart = C1Combo1.Text.Length
            C1Combo1.SelectionLength = 0
        End If

    End Sub

    Private Sub C1Combo1_KeyDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles C1Combo1.KeyDown
        If e.KeyValue = 13 Then
            If C1Combo1.WillChangeToIndex < 1 Then
                If (CType(C1Combo1.DataSource, DataView).Count) = 0 Then
                    MsgBox("项目: '" + Me.C1Combo1.Text + "' 不存在！", MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
                    System.Windows.Forms.SendKeys.Send("^{A}")
                    Exit Sub
                End If
                C1Combo1.SelectedIndex = -1
                C1Combo1.SelectedIndex = 0
            Else
                Dim index As Integer
                index = C1Combo1.WillChangeToIndex
                C1Combo1.SelectedIndex = -1
                C1Combo1.SelectedIndex = index
            End If
            e.Handled = True
            System.Windows.Forms.SendKeys.Send("{Tab}")
        End If
    End Sub

#End Region



#End Region

#Region "数据__编辑"

    Private Sub Data_Clear()

        C1Combo1.Enabled = True
        C1Combo1.SelectedValue = -1
        C1Combo1.Text = ""                                      '药品明细编码

        C1Numeric1.Value = 1                                    '采购数量
        C1Numeric2.Value = 0                                    '采购单价
        C1Numeric4.Value = 0                                    '采购金额

        Label1.Text = ""
        C1Combo1.Select()

    End Sub

    Private Sub Data_Show()
        C1Combo1.DataSource.RowFilter = ""
        With Cbrow
            '药品名称
            C1Combo1.SelectedValue = .Item("Xm_Code") & ""
            C1Numeric1.Value = .Item("Mz_Sl")                   '采购数量
            C1Numeric2.Value = .Item("Mz_Dj") & ""
            C1Numeric4.Value = .Item("Mz_Money")                '采购金额
        End With

    End Sub

    Private Sub Save_Add()
        Dim My_Tb As DataTable = RZbtb

        If C1Combo1.Columns("lb").Value = "诊疗字典" Then
            Dim My_NewRow As DataRow = My_Tb.NewRow
            With My_NewRow
                .BeginEdit()
                .Item("Mz_Code") = ZbRow.Item("Mz_Code")
                .Item("Xm_Code") = C1Combo1.Columns("Xm_Code").Value                        '药品明细编码
                .Item("Xm_Dw") = C1Combo1.Columns("Xm_Dw").Value
                .Item("Mz_Sl") = C1Numeric1.Value                   '采购单价
                .Item("Mz_Dj") = C1Numeric2.Value                   '采购单价
                .Item("Mz_Money") = C1Numeric1.Value * C1Numeric2.Value                '采购金额
                .Item("Mz_Lb") = Me.Label1.Text
                .Item("Xm_Name") = Trim(C1Combo1.Text & "")
                .Item("Xm_Discount") = 1
                .Item("Xm_Original_Money") = C1Numeric1.Value * C1Numeric2.Value
                .Item("Templet_Code") = ""
                .EndEdit()
            End With

            '数据保存
            Try

                My_Tb.Rows.Add(My_NewRow)
            Catch ex As Exception
                Beep()
                MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
                C1Combo1.Select()
            End Try


            With Rzbadt.InsertCommand
                Try

                    .Parameters(0).Value = HisVar.HisVar.WsyCode
                    .Parameters(1).Value = ZbRow.Item("Mz_Code")
                    .Parameters(2).Value = My_NewRow.Item("Xm_Code") & ""
                    .Parameters(3).Value = My_NewRow.Item("Mz_Sl")
                    .Parameters(4).Value = My_NewRow.Item("Mz_Dj")
                    .Parameters(5).Value = My_NewRow.Item("Mz_Money")
                    .Parameters(6).Value = My_NewRow.Item("Xm_Discount")
                    .Parameters(7).Value = My_NewRow.Item("Xm_Original_Money")
                    .Parameters(8).Value = My_NewRow.Item("Mz_Lb")
                    .Parameters(9).Value = My_NewRow.Item("Templet_Code")
                    Call P_Conn(True)
                    .ExecuteNonQuery()
                    Call P_Conn(False)

                    My_NewRow.AcceptChanges()
                Catch ex As Exception
                    Beep()
                    MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
                End Try
            End With


        Else


            Dim My_Reader As SqlDataReader = HisVar.HisVar.Sqldal.ExecuteReader("Select Xm_Code,Xm_Dw,Mx_Sl,Xm_Name,isnull(Xm_NewDj,Xm_Dj)Xm_Dj,Xmlb_Name,Templet_Code From Zd_Templet2,Zd_Ml_Xm3,Zd_Ml_Xm1   where Zd_Templet2.Mx_Code=Zd_Ml_Xm3.Xm_Code and Zd_Ml_Xm1.Xmlb_Code=Zd_Ml_Xm3.Xmlb_Code and Templet_Code='" & C1Combo1.SelectedValue & "' ")
            While My_Reader.Read
                Dim My_NewRow As DataRow = My_Tb.NewRow
                With My_NewRow
                    .BeginEdit()
                    .Item("Mz_Code") = ZbRow.Item("Mz_Code")
                    .Item("Xm_Code") = My_Reader.Item("Xm_Code")                       '药品明细编码
                    .Item("Xm_Dw") = My_Reader.Item("Xm_Dw")
                    .Item("Mz_Sl") = My_Reader.Item("Mx_Sl") * C1Numeric1.Value
                    .Item("Xm_Name") = Trim(My_Reader.Item("Xm_Name") & "")

                    If HisVar.HisVar.IsYbzl = "是" And (ZbRow.Item("Bxlb_Name") = "合作医疗" Or ZbRow.Item("Bxlb_Name") = "城乡居民") And ZbRow.Item("Ry_YlCode") <> "" Then
                        If YbzlXm_Str.Contains(C1Combo1.Columns("Xm_Name").Value) Then
                            .Item("Mz_Dj") = 0
                            .Item("Mz_Money") = 0
                        Else
                            .Item("Mz_Dj") = My_Reader.Item("Xm_Dj")
                            .Item("Mz_Money") = My_Reader.Item("Mx_Sl") * C1Numeric1.Value * My_Reader.Item("Xm_Dj")                 '采购金额
                        End If
                    Else
                        .Item("Mz_Dj") = My_Reader.Item("Xm_Dj")
                        .Item("Mz_Money") = My_Reader.Item("Mx_Sl") * C1Numeric1.Value * My_Reader.Item("Xm_Dj")                 '采购金额
                    End If
                    .Item("Mz_Lb") = My_Reader.Item("Xmlb_Name")
                    .Item("Xm_Discount") = 1
                    .Item("Xm_Original_Money") =  My_Reader.Item("Mx_Sl") * C1Numeric1.Value * My_Reader.Item("Xm_Dj") 
                    .Item("Templet_Code") = C1Combo1.SelectedValue.Trim()
                    .EndEdit()
                End With

                '数据保存
                Try

                    My_Tb.Rows.Add(My_NewRow)
                Catch ex As Exception
                    Beep()
                    MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
                    C1Combo1.Select()
                End Try

                With Rzbadt.InsertCommand
                    Try
                        .Parameters(0).Value = HisVar.HisVar.WsyCode
                        .Parameters(1).Value = ZbRow.Item("Mz_Code")
                        .Parameters(2).Value = My_NewRow.Item("Xm_Code") & ""
                        .Parameters(3).Value = My_NewRow.Item("Mz_Sl")
                        .Parameters(4).Value = My_NewRow.Item("Mz_Dj")
                        .Parameters(5).Value = My_NewRow.Item("Mz_Money")
                        .Parameters(6).Value = My_NewRow.Item("Xm_Discount")
                        .Parameters(7).Value = My_NewRow.Item("Xm_Original_Money")
                        .Parameters(8).Value = My_NewRow.Item("Mz_Lb")
                        .Parameters(9).Value = My_NewRow.Item("Templet_Code")
                        Call P_Conn(True)
                        .ExecuteNonQuery()
                        Call P_Conn(False)

                        My_NewRow.AcceptChanges()
                    Catch ex As Exception
                        Beep()
                        MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
                    End Try
                End With

            End While
        End If

        Rtdbgrid.UpdateData()
        Rtdbgrid.MoveLast()
        Rform.F_Sum()

        Call Data_Clear()

    End Sub

    Private Sub Save_Edit()

        With Cbrow
            Try
                .BeginEdit()
                .Item("Mz_Code") = ZbRow.Item("Mz_Code")
                .Item("Xm_Code") = C1Combo1.Columns("Xm_Code").Value                        '药品明细编码
                .Item("Xm_Dw") = C1Combo1.Columns("Xm_Dw").Value
                .Item("Mz_Sl") = C1Numeric1.Value                   '采购单价
                .Item("Mz_Dj") = C1Numeric2.Value                   '采购单价
                .Item("Mz_Money") = C1Numeric1.Value * C1Numeric2.Value                '采购金额
                .Item("Mz_Lb") = Me.Label1.Text
                .Item("Xm_Discount") = 1
                .Item("Xm_Original_Money") = C1Numeric1.Value * C1Numeric2.Value
                .Item("Xm_Name") = Trim(C1Combo1.Text & "")
                .EndEdit()
            Catch ex As Exception
                Beep()
                MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            End Try
        End With

        With Rzbadt.UpdateCommand

            .Parameters(0).Value = Cbrow.Item("Xm_Code")
            .Parameters(1).Value = Cbrow.Item("Mz_Sl")
            .Parameters(2).Value = Cbrow.Item("Mz_Dj")
            .Parameters(3).Value = Cbrow.Item("Mz_Money")
            .Parameters(4).Value = Cbrow.Item("Xm_Discount")
            .Parameters(5).Value = Cbrow.Item("Xm_Original_Money")
            .Parameters(6).Value = Cbrow.Item("Mz_Lb")
            .Parameters(7).Value = Cbrow.Item("Mz_Id", DataRowVersion.Original)


            Try
                Call P_Conn(True)
                .ExecuteNonQuery()
                Call P_Conn(False)
                Cbrow.AcceptChanges()
                Rform.F_Sum()
            Catch ex As Exception
                Beep()
                MsgBox(ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
            End Try
        End With


    End Sub

#End Region

#Region "鼠标__外观"

    Private Sub P_Comm(ByVal Bt As Button)

        With Bt
            Select Case .Tag
                Case "保存"
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")

                Case "取消"
                    .Left = Me.Comm1.Left + Me.Comm1.Width + 1
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
                    .Width = Me.Comm1.Width
            End Select

            .Text = ""
            .FlatStyle = FlatStyle.Flat
            .FlatAppearance.BorderSize = 0
            .BackgroundImageLayout = ImageLayout.Center
        End With

    End Sub

    Private Sub Comm_MouseEnter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.MouseEnter, Comm2.MouseEnter
        Select Case sender.tag
            Case "保存"
                Me.Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存2")
                Me.Comm1.Cursor = Cursors.Hand

            Case "取消"
                Me.Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消2")
                Me.Comm2.Cursor = Cursors.Hand

        End Select

    End Sub

    Private Sub Comm_MouseLeave(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.MouseLeave, Comm2.MouseLeave
        Select Case sender.tag

            Case "保存"
                Me.Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")
                Me.Comm1.Cursor = Cursors.Default

            Case "取消"
                Me.Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
                Me.Comm2.Cursor = Cursors.Default

        End Select

    End Sub

    Private Sub Comm_MouseDown(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm1.MouseDown, Comm2.MouseDown
        Select Case sender.tag
            Case "保存"
                Me.Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存3")

            Case "取消"
                Me.Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消3")
        End Select
    End Sub

    Private Sub Comm_MouseUp(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm1.MouseUp, Comm2.MouseUp
        Select Case sender.tag
            Case "保存"
                Me.Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存2")

            Case "取消"
                Me.Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消2")
        End Select
    End Sub


#End Region

#Region "输入法设置"

    Private Sub C1Numeric1_KeyDown(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Combo1.GotFocus, C1Numeric1.GotFocus
        InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages(0)
    End Sub

#End Region





End Class