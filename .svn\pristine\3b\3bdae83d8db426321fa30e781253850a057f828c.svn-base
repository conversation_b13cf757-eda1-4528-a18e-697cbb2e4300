<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:tm="http://microsoft.com/wsdl/mime/textMatching/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:tns="http://tempuri.org/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:s="http://www.w3.org/2001/XMLSchema" xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <s:schema elementFormDefault="qualified" targetNamespace="http://tempuri.org/">
      <s:element name="HelloWorld">
        <s:complexType />
      </s:element>
      <s:element name="HelloWorldResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="HelloWorldResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetMlYpGx">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="V_YpGx_Code" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetMlYpGxResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetMlYpGxResult">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetMlYpjx">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="V_Jx_Code" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetMlYpjxResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetMlYpjxResult">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetMlJb1">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="V_Jb1_Code" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetMlJb1Response">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetMlJb1Result">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetMlJb3">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="V_Jblb_Code" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetMlJb3Response">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetMlJb3Result">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetMlXm1">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="V_Xm1_Code" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetMlXm1Response">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetMlXm1Result">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetMlXm3">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="V_Xmlb_Code" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetMlXm3Response">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetMlXm3Result">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetMlYp2">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="V_Dl_Code" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetMlYp2Response">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetMlYp2Result">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetMlYp3">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="V_Yp_Code" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetMlYp3Response">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetMlYp3Result">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetMlJbYpJx">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="V_Yp_Code" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetMlJbYpJxResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetMlJbYpJxResult">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UpDate_Jblb">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="V_Jblb_Code" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Jblb_Name" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Jblb_Jc" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Jblb_Memo" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UpDate_JblbResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="UpDate_JblbResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UpDate_Jb">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="V_Jblb_Code" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Jb_Code" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Jb_Name" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Jb_Jc" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Jb_Memo" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UpDate_JbResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="UpDate_JbResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UpDate_Xmlb">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="V_Xmlb_Code" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Xmlb_Name" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Xmlb_Jc" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Xmlb_Memo" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UpDate_XmlbResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="UpDate_XmlbResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UpDate_Xm">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="V_Xmlb_Code" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Xm_Code" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Xm_Name" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Xm_Jc" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Xm_Dw" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Xm_Memo" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UpDate_XmResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="UpDate_XmResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UpDate_Yp2">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="V_Dl_Code" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Yp_Code" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Yp_Name" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Yp_Jc" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Yp_Memo" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Gx_Code" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_IsJb" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UpDate_Yp2Response">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="UpDate_Yp2Result" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UpDate_Yp3">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="V_Yp_Code" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Mx_Code" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Jx_Code" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Mx_Cd" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Mx_Gg" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Mx_CgDw" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Mx_XsDw" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="V_Mx_Cfbl" type="s:double" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Mx_Gyzz" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Mx_Memo" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UpDate_Yp3Response">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="UpDate_Yp3Result" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UpDate_Ypjx">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="V_Jx_Code" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Jx_Name" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Jx_Jc" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Jx_Memo" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UpDate_YpjxResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="UpDate_YpjxResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UpDate_YpGx">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="V_Gx_Code" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Gx_Name" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Gx_Jc" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Gx_Memo" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UpDate_YpGxResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="UpDate_YpGxResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="Ckeck_Ybzl">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="V_Xm_Name" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="Ckeck_YbzlResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="Ckeck_YbzlResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetYbZL_Xm">
        <s:complexType />
      </s:element>
      <s:element name="GetYbZL_XmResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetYbZL_XmResult">
              <s:complexType>
                <s:sequence>
                  <s:any minOccurs="0" maxOccurs="unbounded" namespace="http://www.w3.org/2001/XMLSchema" processContents="lax" />
                  <s:any minOccurs="1" namespace="urn:schemas-microsoft-com:xml-diffgram-v1" processContents="lax" />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="F_GetJkdaRyXx">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="V_Tj" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="F_GetJkdaRyXxResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="F_GetJkdaRyXxResult">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="F_GetJkdaJsr">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="V_UserName" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_UserPass" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="F_GetJkdaJsrResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="F_GetJkdaJsrResult">
              <s:complexType>
                <s:sequence>
                  <s:any minOccurs="0" maxOccurs="unbounded" namespace="http://www.w3.org/2001/XMLSchema" processContents="lax" />
                  <s:any minOccurs="1" namespace="urn:schemas-microsoft-com:xml-diffgram-v1" processContents="lax" />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="F_GetSupJsr">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="V_Tj" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="F_GetSupJsrResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="F_GetSupJsrResult">
              <s:complexType>
                <s:sequence>
                  <s:any minOccurs="0" maxOccurs="unbounded" namespace="http://www.w3.org/2001/XMLSchema" processContents="lax" />
                  <s:any minOccurs="1" namespace="urn:schemas-microsoft-com:xml-diffgram-v1" processContents="lax" />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="F_InserGxySf">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="V_Ry_Code" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Gxy_Sffs" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="V_Gxy_SfDate" type="s:dateTime" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Gxy_Xy" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Gxy_Tz" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Gxy_Tzzs" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Gxy_Xl" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Gxy_Qt" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Gxy_YwName1" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Gxy_YfMr1" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Gxy_YfMrMg1" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Gxy_YwName2" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Gxy_YfMr2" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Gxy_YfMrMg2" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Gxy_YwName3" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Gxy_YfMr3" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Gxy_YfMrMg3" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Gxy_QtYw" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Gxy_YfMr4" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Gxy_YfMrMg4" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Gxy_SfYs" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Gxy_XcSfDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Jsr_Code" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Jsr_Name" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Gxy_GaoYa" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Gxy_DiYa" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Gxy_QwTz" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Gxy_QwTzzs" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="F_InserGxySfResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="F_InserGxySfResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="F_InserTnbSf">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="V_Ry_Code" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Tnb_Sffs" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="V_Tnb_SfDate" type="s:dateTime" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Tnb_Xy" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Tnb_Tz" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Tnb_Tzzs" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Tnb_Zbdmbd" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Tnb_Xl" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Tnb_Qt" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Tnb_YwName1" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Tnb_YfMr1" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Tnb_YfMrMg1" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Tnb_YwName2" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Tnb_YfMr2" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Tnb_YfMrMg2" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Tnb_YwName3" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Tnb_YfMr3" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Tnb_YfMrMg3" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Tnb_Yds" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Tnb_SfYs" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Tnb_XcSfDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Jsr_Code" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Jsr_Name" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Tnb_QwTz" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="V_Tnb_QwTzzs" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="F_InserTnbSfResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="F_InserTnbSfResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="string" nillable="true" type="s:string" />
      <s:element name="DataSet" nillable="true">
        <s:complexType>
          <s:sequence>
            <s:element ref="s:schema" />
            <s:any />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="boolean" type="s:boolean" />
      <s:element name="DataTable" nillable="true">
        <s:complexType>
          <s:sequence>
            <s:any minOccurs="0" maxOccurs="unbounded" namespace="http://www.w3.org/2001/XMLSchema" processContents="lax" />
            <s:any minOccurs="1" namespace="urn:schemas-microsoft-com:xml-diffgram-v1" processContents="lax" />
          </s:sequence>
        </s:complexType>
      </s:element>
    </s:schema>
  </wsdl:types>
  <wsdl:message name="HelloWorldSoapIn">
    <wsdl:part name="parameters" element="tns:HelloWorld" />
  </wsdl:message>
  <wsdl:message name="HelloWorldSoapOut">
    <wsdl:part name="parameters" element="tns:HelloWorldResponse" />
  </wsdl:message>
  <wsdl:message name="GetMlYpGxSoapIn">
    <wsdl:part name="parameters" element="tns:GetMlYpGx" />
  </wsdl:message>
  <wsdl:message name="GetMlYpGxSoapOut">
    <wsdl:part name="parameters" element="tns:GetMlYpGxResponse" />
  </wsdl:message>
  <wsdl:message name="GetMlYpjxSoapIn">
    <wsdl:part name="parameters" element="tns:GetMlYpjx" />
  </wsdl:message>
  <wsdl:message name="GetMlYpjxSoapOut">
    <wsdl:part name="parameters" element="tns:GetMlYpjxResponse" />
  </wsdl:message>
  <wsdl:message name="GetMlJb1SoapIn">
    <wsdl:part name="parameters" element="tns:GetMlJb1" />
  </wsdl:message>
  <wsdl:message name="GetMlJb1SoapOut">
    <wsdl:part name="parameters" element="tns:GetMlJb1Response" />
  </wsdl:message>
  <wsdl:message name="GetMlJb3SoapIn">
    <wsdl:part name="parameters" element="tns:GetMlJb3" />
  </wsdl:message>
  <wsdl:message name="GetMlJb3SoapOut">
    <wsdl:part name="parameters" element="tns:GetMlJb3Response" />
  </wsdl:message>
  <wsdl:message name="GetMlXm1SoapIn">
    <wsdl:part name="parameters" element="tns:GetMlXm1" />
  </wsdl:message>
  <wsdl:message name="GetMlXm1SoapOut">
    <wsdl:part name="parameters" element="tns:GetMlXm1Response" />
  </wsdl:message>
  <wsdl:message name="GetMlXm3SoapIn">
    <wsdl:part name="parameters" element="tns:GetMlXm3" />
  </wsdl:message>
  <wsdl:message name="GetMlXm3SoapOut">
    <wsdl:part name="parameters" element="tns:GetMlXm3Response" />
  </wsdl:message>
  <wsdl:message name="GetMlYp2SoapIn">
    <wsdl:part name="parameters" element="tns:GetMlYp2" />
  </wsdl:message>
  <wsdl:message name="GetMlYp2SoapOut">
    <wsdl:part name="parameters" element="tns:GetMlYp2Response" />
  </wsdl:message>
  <wsdl:message name="GetMlYp3SoapIn">
    <wsdl:part name="parameters" element="tns:GetMlYp3" />
  </wsdl:message>
  <wsdl:message name="GetMlYp3SoapOut">
    <wsdl:part name="parameters" element="tns:GetMlYp3Response" />
  </wsdl:message>
  <wsdl:message name="GetMlJbYpJxSoapIn">
    <wsdl:part name="parameters" element="tns:GetMlJbYpJx" />
  </wsdl:message>
  <wsdl:message name="GetMlJbYpJxSoapOut">
    <wsdl:part name="parameters" element="tns:GetMlJbYpJxResponse" />
  </wsdl:message>
  <wsdl:message name="UpDate_JblbSoapIn">
    <wsdl:part name="parameters" element="tns:UpDate_Jblb" />
  </wsdl:message>
  <wsdl:message name="UpDate_JblbSoapOut">
    <wsdl:part name="parameters" element="tns:UpDate_JblbResponse" />
  </wsdl:message>
  <wsdl:message name="UpDate_JbSoapIn">
    <wsdl:part name="parameters" element="tns:UpDate_Jb" />
  </wsdl:message>
  <wsdl:message name="UpDate_JbSoapOut">
    <wsdl:part name="parameters" element="tns:UpDate_JbResponse" />
  </wsdl:message>
  <wsdl:message name="UpDate_XmlbSoapIn">
    <wsdl:part name="parameters" element="tns:UpDate_Xmlb" />
  </wsdl:message>
  <wsdl:message name="UpDate_XmlbSoapOut">
    <wsdl:part name="parameters" element="tns:UpDate_XmlbResponse" />
  </wsdl:message>
  <wsdl:message name="UpDate_XmSoapIn">
    <wsdl:part name="parameters" element="tns:UpDate_Xm" />
  </wsdl:message>
  <wsdl:message name="UpDate_XmSoapOut">
    <wsdl:part name="parameters" element="tns:UpDate_XmResponse" />
  </wsdl:message>
  <wsdl:message name="UpDate_Yp2SoapIn">
    <wsdl:part name="parameters" element="tns:UpDate_Yp2" />
  </wsdl:message>
  <wsdl:message name="UpDate_Yp2SoapOut">
    <wsdl:part name="parameters" element="tns:UpDate_Yp2Response" />
  </wsdl:message>
  <wsdl:message name="UpDate_Yp3SoapIn">
    <wsdl:part name="parameters" element="tns:UpDate_Yp3" />
  </wsdl:message>
  <wsdl:message name="UpDate_Yp3SoapOut">
    <wsdl:part name="parameters" element="tns:UpDate_Yp3Response" />
  </wsdl:message>
  <wsdl:message name="UpDate_YpjxSoapIn">
    <wsdl:part name="parameters" element="tns:UpDate_Ypjx" />
  </wsdl:message>
  <wsdl:message name="UpDate_YpjxSoapOut">
    <wsdl:part name="parameters" element="tns:UpDate_YpjxResponse" />
  </wsdl:message>
  <wsdl:message name="UpDate_YpGxSoapIn">
    <wsdl:part name="parameters" element="tns:UpDate_YpGx" />
  </wsdl:message>
  <wsdl:message name="UpDate_YpGxSoapOut">
    <wsdl:part name="parameters" element="tns:UpDate_YpGxResponse" />
  </wsdl:message>
  <wsdl:message name="Ckeck_YbzlSoapIn">
    <wsdl:part name="parameters" element="tns:Ckeck_Ybzl" />
  </wsdl:message>
  <wsdl:message name="Ckeck_YbzlSoapOut">
    <wsdl:part name="parameters" element="tns:Ckeck_YbzlResponse" />
  </wsdl:message>
  <wsdl:message name="GetYbZL_XmSoapIn">
    <wsdl:part name="parameters" element="tns:GetYbZL_Xm" />
  </wsdl:message>
  <wsdl:message name="GetYbZL_XmSoapOut">
    <wsdl:part name="parameters" element="tns:GetYbZL_XmResponse" />
  </wsdl:message>
  <wsdl:message name="F_GetJkdaRyXxSoapIn">
    <wsdl:part name="parameters" element="tns:F_GetJkdaRyXx" />
  </wsdl:message>
  <wsdl:message name="F_GetJkdaRyXxSoapOut">
    <wsdl:part name="parameters" element="tns:F_GetJkdaRyXxResponse" />
  </wsdl:message>
  <wsdl:message name="F_GetJkdaJsrSoapIn">
    <wsdl:part name="parameters" element="tns:F_GetJkdaJsr" />
  </wsdl:message>
  <wsdl:message name="F_GetJkdaJsrSoapOut">
    <wsdl:part name="parameters" element="tns:F_GetJkdaJsrResponse" />
  </wsdl:message>
  <wsdl:message name="F_GetSupJsrSoapIn">
    <wsdl:part name="parameters" element="tns:F_GetSupJsr" />
  </wsdl:message>
  <wsdl:message name="F_GetSupJsrSoapOut">
    <wsdl:part name="parameters" element="tns:F_GetSupJsrResponse" />
  </wsdl:message>
  <wsdl:message name="F_InserGxySfSoapIn">
    <wsdl:part name="parameters" element="tns:F_InserGxySf" />
  </wsdl:message>
  <wsdl:message name="F_InserGxySfSoapOut">
    <wsdl:part name="parameters" element="tns:F_InserGxySfResponse" />
  </wsdl:message>
  <wsdl:message name="F_InserTnbSfSoapIn">
    <wsdl:part name="parameters" element="tns:F_InserTnbSf" />
  </wsdl:message>
  <wsdl:message name="F_InserTnbSfSoapOut">
    <wsdl:part name="parameters" element="tns:F_InserTnbSfResponse" />
  </wsdl:message>
  <wsdl:message name="HelloWorldHttpGetIn" />
  <wsdl:message name="HelloWorldHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="GetMlYpGxHttpGetIn">
    <wsdl:part name="V_YpGx_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="GetMlYpGxHttpGetOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="GetMlYpjxHttpGetIn">
    <wsdl:part name="V_Jx_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="GetMlYpjxHttpGetOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="GetMlJb1HttpGetIn">
    <wsdl:part name="V_Jb1_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="GetMlJb1HttpGetOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="GetMlJb3HttpGetIn">
    <wsdl:part name="V_Jblb_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="GetMlJb3HttpGetOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="GetMlXm1HttpGetIn">
    <wsdl:part name="V_Xm1_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="GetMlXm1HttpGetOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="GetMlXm3HttpGetIn">
    <wsdl:part name="V_Xmlb_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="GetMlXm3HttpGetOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="GetMlYp2HttpGetIn">
    <wsdl:part name="V_Dl_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="GetMlYp2HttpGetOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="GetMlYp3HttpGetIn">
    <wsdl:part name="V_Yp_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="GetMlYp3HttpGetOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="GetMlJbYpJxHttpGetIn">
    <wsdl:part name="V_Yp_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="GetMlJbYpJxHttpGetOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="UpDate_JblbHttpGetIn">
    <wsdl:part name="V_Jblb_Code" type="s:string" />
    <wsdl:part name="V_Jblb_Name" type="s:string" />
    <wsdl:part name="V_Jblb_Jc" type="s:string" />
    <wsdl:part name="V_Jblb_Memo" type="s:string" />
  </wsdl:message>
  <wsdl:message name="UpDate_JblbHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="UpDate_JbHttpGetIn">
    <wsdl:part name="V_Jblb_Code" type="s:string" />
    <wsdl:part name="V_Jb_Code" type="s:string" />
    <wsdl:part name="V_Jb_Name" type="s:string" />
    <wsdl:part name="V_Jb_Jc" type="s:string" />
    <wsdl:part name="V_Jb_Memo" type="s:string" />
  </wsdl:message>
  <wsdl:message name="UpDate_JbHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="UpDate_XmlbHttpGetIn">
    <wsdl:part name="V_Xmlb_Code" type="s:string" />
    <wsdl:part name="V_Xmlb_Name" type="s:string" />
    <wsdl:part name="V_Xmlb_Jc" type="s:string" />
    <wsdl:part name="V_Xmlb_Memo" type="s:string" />
  </wsdl:message>
  <wsdl:message name="UpDate_XmlbHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="UpDate_XmHttpGetIn">
    <wsdl:part name="V_Xmlb_Code" type="s:string" />
    <wsdl:part name="V_Xm_Code" type="s:string" />
    <wsdl:part name="V_Xm_Name" type="s:string" />
    <wsdl:part name="V_Xm_Jc" type="s:string" />
    <wsdl:part name="V_Xm_Dw" type="s:string" />
    <wsdl:part name="V_Xm_Memo" type="s:string" />
  </wsdl:message>
  <wsdl:message name="UpDate_XmHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="UpDate_Yp2HttpGetIn">
    <wsdl:part name="V_Dl_Code" type="s:string" />
    <wsdl:part name="V_Yp_Code" type="s:string" />
    <wsdl:part name="V_Yp_Name" type="s:string" />
    <wsdl:part name="V_Yp_Jc" type="s:string" />
    <wsdl:part name="V_Yp_Memo" type="s:string" />
    <wsdl:part name="V_Gx_Code" type="s:string" />
    <wsdl:part name="V_IsJb" type="s:string" />
  </wsdl:message>
  <wsdl:message name="UpDate_Yp2HttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="UpDate_Yp3HttpGetIn">
    <wsdl:part name="V_Yp_Code" type="s:string" />
    <wsdl:part name="V_Mx_Code" type="s:string" />
    <wsdl:part name="V_Jx_Code" type="s:string" />
    <wsdl:part name="V_Mx_Cd" type="s:string" />
    <wsdl:part name="V_Mx_Gg" type="s:string" />
    <wsdl:part name="V_Mx_CgDw" type="s:string" />
    <wsdl:part name="V_Mx_XsDw" type="s:string" />
    <wsdl:part name="V_Mx_Cfbl" type="s:string" />
    <wsdl:part name="V_Mx_Gyzz" type="s:string" />
    <wsdl:part name="V_Mx_Memo" type="s:string" />
  </wsdl:message>
  <wsdl:message name="UpDate_Yp3HttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="UpDate_YpjxHttpGetIn">
    <wsdl:part name="V_Jx_Code" type="s:string" />
    <wsdl:part name="V_Jx_Name" type="s:string" />
    <wsdl:part name="V_Jx_Jc" type="s:string" />
    <wsdl:part name="V_Jx_Memo" type="s:string" />
  </wsdl:message>
  <wsdl:message name="UpDate_YpjxHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="UpDate_YpGxHttpGetIn">
    <wsdl:part name="V_Gx_Code" type="s:string" />
    <wsdl:part name="V_Gx_Name" type="s:string" />
    <wsdl:part name="V_Gx_Jc" type="s:string" />
    <wsdl:part name="V_Gx_Memo" type="s:string" />
  </wsdl:message>
  <wsdl:message name="UpDate_YpGxHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="Ckeck_YbzlHttpGetIn">
    <wsdl:part name="V_Xm_Name" type="s:string" />
  </wsdl:message>
  <wsdl:message name="Ckeck_YbzlHttpGetOut">
    <wsdl:part name="Body" element="tns:boolean" />
  </wsdl:message>
  <wsdl:message name="GetYbZL_XmHttpGetIn" />
  <wsdl:message name="GetYbZL_XmHttpGetOut">
    <wsdl:part name="Body" element="tns:DataTable" />
  </wsdl:message>
  <wsdl:message name="F_GetJkdaRyXxHttpGetIn">
    <wsdl:part name="V_Tj" type="s:string" />
  </wsdl:message>
  <wsdl:message name="F_GetJkdaRyXxHttpGetOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="F_GetJkdaJsrHttpGetIn">
    <wsdl:part name="V_UserName" type="s:string" />
    <wsdl:part name="V_UserPass" type="s:string" />
  </wsdl:message>
  <wsdl:message name="F_GetJkdaJsrHttpGetOut">
    <wsdl:part name="Body" element="tns:DataTable" />
  </wsdl:message>
  <wsdl:message name="F_GetSupJsrHttpGetIn">
    <wsdl:part name="V_Tj" type="s:string" />
  </wsdl:message>
  <wsdl:message name="F_GetSupJsrHttpGetOut">
    <wsdl:part name="Body" element="tns:DataTable" />
  </wsdl:message>
  <wsdl:message name="F_InserGxySfHttpGetIn">
    <wsdl:part name="V_Ry_Code" type="s:string" />
    <wsdl:part name="V_Gxy_Sffs" type="s:string" />
    <wsdl:part name="V_Gxy_SfDate" type="s:string" />
    <wsdl:part name="V_Gxy_Xy" type="s:string" />
    <wsdl:part name="V_Gxy_Tz" type="s:string" />
    <wsdl:part name="V_Gxy_Tzzs" type="s:string" />
    <wsdl:part name="V_Gxy_Xl" type="s:string" />
    <wsdl:part name="V_Gxy_Qt" type="s:string" />
    <wsdl:part name="V_Gxy_YwName1" type="s:string" />
    <wsdl:part name="V_Gxy_YfMr1" type="s:string" />
    <wsdl:part name="V_Gxy_YfMrMg1" type="s:string" />
    <wsdl:part name="V_Gxy_YwName2" type="s:string" />
    <wsdl:part name="V_Gxy_YfMr2" type="s:string" />
    <wsdl:part name="V_Gxy_YfMrMg2" type="s:string" />
    <wsdl:part name="V_Gxy_YwName3" type="s:string" />
    <wsdl:part name="V_Gxy_YfMr3" type="s:string" />
    <wsdl:part name="V_Gxy_YfMrMg3" type="s:string" />
    <wsdl:part name="V_Gxy_QtYw" type="s:string" />
    <wsdl:part name="V_Gxy_YfMr4" type="s:string" />
    <wsdl:part name="V_Gxy_YfMrMg4" type="s:string" />
    <wsdl:part name="V_Gxy_SfYs" type="s:string" />
    <wsdl:part name="V_Gxy_XcSfDate" type="s:string" />
    <wsdl:part name="V_Jsr_Code" type="s:string" />
    <wsdl:part name="V_Jsr_Name" type="s:string" />
    <wsdl:part name="V_Gxy_GaoYa" type="s:string" />
    <wsdl:part name="V_Gxy_DiYa" type="s:string" />
    <wsdl:part name="V_Gxy_QwTz" type="s:string" />
    <wsdl:part name="V_Gxy_QwTzzs" type="s:string" />
  </wsdl:message>
  <wsdl:message name="F_InserGxySfHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="F_InserTnbSfHttpGetIn">
    <wsdl:part name="V_Ry_Code" type="s:string" />
    <wsdl:part name="V_Tnb_Sffs" type="s:string" />
    <wsdl:part name="V_Tnb_SfDate" type="s:string" />
    <wsdl:part name="V_Tnb_Xy" type="s:string" />
    <wsdl:part name="V_Tnb_Tz" type="s:string" />
    <wsdl:part name="V_Tnb_Tzzs" type="s:string" />
    <wsdl:part name="V_Tnb_Zbdmbd" type="s:string" />
    <wsdl:part name="V_Tnb_Xl" type="s:string" />
    <wsdl:part name="V_Tnb_Qt" type="s:string" />
    <wsdl:part name="V_Tnb_YwName1" type="s:string" />
    <wsdl:part name="V_Tnb_YfMr1" type="s:string" />
    <wsdl:part name="V_Tnb_YfMrMg1" type="s:string" />
    <wsdl:part name="V_Tnb_YwName2" type="s:string" />
    <wsdl:part name="V_Tnb_YfMr2" type="s:string" />
    <wsdl:part name="V_Tnb_YfMrMg2" type="s:string" />
    <wsdl:part name="V_Tnb_YwName3" type="s:string" />
    <wsdl:part name="V_Tnb_YfMr3" type="s:string" />
    <wsdl:part name="V_Tnb_YfMrMg3" type="s:string" />
    <wsdl:part name="V_Tnb_Yds" type="s:string" />
    <wsdl:part name="V_Tnb_SfYs" type="s:string" />
    <wsdl:part name="V_Tnb_XcSfDate" type="s:string" />
    <wsdl:part name="V_Jsr_Code" type="s:string" />
    <wsdl:part name="V_Jsr_Name" type="s:string" />
    <wsdl:part name="V_Tnb_QwTz" type="s:string" />
    <wsdl:part name="V_Tnb_QwTzzs" type="s:string" />
  </wsdl:message>
  <wsdl:message name="F_InserTnbSfHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="HelloWorldHttpPostIn" />
  <wsdl:message name="HelloWorldHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="GetMlYpGxHttpPostIn">
    <wsdl:part name="V_YpGx_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="GetMlYpGxHttpPostOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="GetMlYpjxHttpPostIn">
    <wsdl:part name="V_Jx_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="GetMlYpjxHttpPostOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="GetMlJb1HttpPostIn">
    <wsdl:part name="V_Jb1_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="GetMlJb1HttpPostOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="GetMlJb3HttpPostIn">
    <wsdl:part name="V_Jblb_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="GetMlJb3HttpPostOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="GetMlXm1HttpPostIn">
    <wsdl:part name="V_Xm1_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="GetMlXm1HttpPostOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="GetMlXm3HttpPostIn">
    <wsdl:part name="V_Xmlb_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="GetMlXm3HttpPostOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="GetMlYp2HttpPostIn">
    <wsdl:part name="V_Dl_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="GetMlYp2HttpPostOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="GetMlYp3HttpPostIn">
    <wsdl:part name="V_Yp_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="GetMlYp3HttpPostOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="GetMlJbYpJxHttpPostIn">
    <wsdl:part name="V_Yp_Code" type="s:string" />
  </wsdl:message>
  <wsdl:message name="GetMlJbYpJxHttpPostOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="UpDate_JblbHttpPostIn">
    <wsdl:part name="V_Jblb_Code" type="s:string" />
    <wsdl:part name="V_Jblb_Name" type="s:string" />
    <wsdl:part name="V_Jblb_Jc" type="s:string" />
    <wsdl:part name="V_Jblb_Memo" type="s:string" />
  </wsdl:message>
  <wsdl:message name="UpDate_JblbHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="UpDate_JbHttpPostIn">
    <wsdl:part name="V_Jblb_Code" type="s:string" />
    <wsdl:part name="V_Jb_Code" type="s:string" />
    <wsdl:part name="V_Jb_Name" type="s:string" />
    <wsdl:part name="V_Jb_Jc" type="s:string" />
    <wsdl:part name="V_Jb_Memo" type="s:string" />
  </wsdl:message>
  <wsdl:message name="UpDate_JbHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="UpDate_XmlbHttpPostIn">
    <wsdl:part name="V_Xmlb_Code" type="s:string" />
    <wsdl:part name="V_Xmlb_Name" type="s:string" />
    <wsdl:part name="V_Xmlb_Jc" type="s:string" />
    <wsdl:part name="V_Xmlb_Memo" type="s:string" />
  </wsdl:message>
  <wsdl:message name="UpDate_XmlbHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="UpDate_XmHttpPostIn">
    <wsdl:part name="V_Xmlb_Code" type="s:string" />
    <wsdl:part name="V_Xm_Code" type="s:string" />
    <wsdl:part name="V_Xm_Name" type="s:string" />
    <wsdl:part name="V_Xm_Jc" type="s:string" />
    <wsdl:part name="V_Xm_Dw" type="s:string" />
    <wsdl:part name="V_Xm_Memo" type="s:string" />
  </wsdl:message>
  <wsdl:message name="UpDate_XmHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="UpDate_Yp2HttpPostIn">
    <wsdl:part name="V_Dl_Code" type="s:string" />
    <wsdl:part name="V_Yp_Code" type="s:string" />
    <wsdl:part name="V_Yp_Name" type="s:string" />
    <wsdl:part name="V_Yp_Jc" type="s:string" />
    <wsdl:part name="V_Yp_Memo" type="s:string" />
    <wsdl:part name="V_Gx_Code" type="s:string" />
    <wsdl:part name="V_IsJb" type="s:string" />
  </wsdl:message>
  <wsdl:message name="UpDate_Yp2HttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="UpDate_Yp3HttpPostIn">
    <wsdl:part name="V_Yp_Code" type="s:string" />
    <wsdl:part name="V_Mx_Code" type="s:string" />
    <wsdl:part name="V_Jx_Code" type="s:string" />
    <wsdl:part name="V_Mx_Cd" type="s:string" />
    <wsdl:part name="V_Mx_Gg" type="s:string" />
    <wsdl:part name="V_Mx_CgDw" type="s:string" />
    <wsdl:part name="V_Mx_XsDw" type="s:string" />
    <wsdl:part name="V_Mx_Cfbl" type="s:string" />
    <wsdl:part name="V_Mx_Gyzz" type="s:string" />
    <wsdl:part name="V_Mx_Memo" type="s:string" />
  </wsdl:message>
  <wsdl:message name="UpDate_Yp3HttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="UpDate_YpjxHttpPostIn">
    <wsdl:part name="V_Jx_Code" type="s:string" />
    <wsdl:part name="V_Jx_Name" type="s:string" />
    <wsdl:part name="V_Jx_Jc" type="s:string" />
    <wsdl:part name="V_Jx_Memo" type="s:string" />
  </wsdl:message>
  <wsdl:message name="UpDate_YpjxHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="UpDate_YpGxHttpPostIn">
    <wsdl:part name="V_Gx_Code" type="s:string" />
    <wsdl:part name="V_Gx_Name" type="s:string" />
    <wsdl:part name="V_Gx_Jc" type="s:string" />
    <wsdl:part name="V_Gx_Memo" type="s:string" />
  </wsdl:message>
  <wsdl:message name="UpDate_YpGxHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="Ckeck_YbzlHttpPostIn">
    <wsdl:part name="V_Xm_Name" type="s:string" />
  </wsdl:message>
  <wsdl:message name="Ckeck_YbzlHttpPostOut">
    <wsdl:part name="Body" element="tns:boolean" />
  </wsdl:message>
  <wsdl:message name="GetYbZL_XmHttpPostIn" />
  <wsdl:message name="GetYbZL_XmHttpPostOut">
    <wsdl:part name="Body" element="tns:DataTable" />
  </wsdl:message>
  <wsdl:message name="F_GetJkdaRyXxHttpPostIn">
    <wsdl:part name="V_Tj" type="s:string" />
  </wsdl:message>
  <wsdl:message name="F_GetJkdaRyXxHttpPostOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="F_GetJkdaJsrHttpPostIn">
    <wsdl:part name="V_UserName" type="s:string" />
    <wsdl:part name="V_UserPass" type="s:string" />
  </wsdl:message>
  <wsdl:message name="F_GetJkdaJsrHttpPostOut">
    <wsdl:part name="Body" element="tns:DataTable" />
  </wsdl:message>
  <wsdl:message name="F_GetSupJsrHttpPostIn">
    <wsdl:part name="V_Tj" type="s:string" />
  </wsdl:message>
  <wsdl:message name="F_GetSupJsrHttpPostOut">
    <wsdl:part name="Body" element="tns:DataTable" />
  </wsdl:message>
  <wsdl:message name="F_InserGxySfHttpPostIn">
    <wsdl:part name="V_Ry_Code" type="s:string" />
    <wsdl:part name="V_Gxy_Sffs" type="s:string" />
    <wsdl:part name="V_Gxy_SfDate" type="s:string" />
    <wsdl:part name="V_Gxy_Xy" type="s:string" />
    <wsdl:part name="V_Gxy_Tz" type="s:string" />
    <wsdl:part name="V_Gxy_Tzzs" type="s:string" />
    <wsdl:part name="V_Gxy_Xl" type="s:string" />
    <wsdl:part name="V_Gxy_Qt" type="s:string" />
    <wsdl:part name="V_Gxy_YwName1" type="s:string" />
    <wsdl:part name="V_Gxy_YfMr1" type="s:string" />
    <wsdl:part name="V_Gxy_YfMrMg1" type="s:string" />
    <wsdl:part name="V_Gxy_YwName2" type="s:string" />
    <wsdl:part name="V_Gxy_YfMr2" type="s:string" />
    <wsdl:part name="V_Gxy_YfMrMg2" type="s:string" />
    <wsdl:part name="V_Gxy_YwName3" type="s:string" />
    <wsdl:part name="V_Gxy_YfMr3" type="s:string" />
    <wsdl:part name="V_Gxy_YfMrMg3" type="s:string" />
    <wsdl:part name="V_Gxy_QtYw" type="s:string" />
    <wsdl:part name="V_Gxy_YfMr4" type="s:string" />
    <wsdl:part name="V_Gxy_YfMrMg4" type="s:string" />
    <wsdl:part name="V_Gxy_SfYs" type="s:string" />
    <wsdl:part name="V_Gxy_XcSfDate" type="s:string" />
    <wsdl:part name="V_Jsr_Code" type="s:string" />
    <wsdl:part name="V_Jsr_Name" type="s:string" />
    <wsdl:part name="V_Gxy_GaoYa" type="s:string" />
    <wsdl:part name="V_Gxy_DiYa" type="s:string" />
    <wsdl:part name="V_Gxy_QwTz" type="s:string" />
    <wsdl:part name="V_Gxy_QwTzzs" type="s:string" />
  </wsdl:message>
  <wsdl:message name="F_InserGxySfHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="F_InserTnbSfHttpPostIn">
    <wsdl:part name="V_Ry_Code" type="s:string" />
    <wsdl:part name="V_Tnb_Sffs" type="s:string" />
    <wsdl:part name="V_Tnb_SfDate" type="s:string" />
    <wsdl:part name="V_Tnb_Xy" type="s:string" />
    <wsdl:part name="V_Tnb_Tz" type="s:string" />
    <wsdl:part name="V_Tnb_Tzzs" type="s:string" />
    <wsdl:part name="V_Tnb_Zbdmbd" type="s:string" />
    <wsdl:part name="V_Tnb_Xl" type="s:string" />
    <wsdl:part name="V_Tnb_Qt" type="s:string" />
    <wsdl:part name="V_Tnb_YwName1" type="s:string" />
    <wsdl:part name="V_Tnb_YfMr1" type="s:string" />
    <wsdl:part name="V_Tnb_YfMrMg1" type="s:string" />
    <wsdl:part name="V_Tnb_YwName2" type="s:string" />
    <wsdl:part name="V_Tnb_YfMr2" type="s:string" />
    <wsdl:part name="V_Tnb_YfMrMg2" type="s:string" />
    <wsdl:part name="V_Tnb_YwName3" type="s:string" />
    <wsdl:part name="V_Tnb_YfMr3" type="s:string" />
    <wsdl:part name="V_Tnb_YfMrMg3" type="s:string" />
    <wsdl:part name="V_Tnb_Yds" type="s:string" />
    <wsdl:part name="V_Tnb_SfYs" type="s:string" />
    <wsdl:part name="V_Tnb_XcSfDate" type="s:string" />
    <wsdl:part name="V_Jsr_Code" type="s:string" />
    <wsdl:part name="V_Jsr_Name" type="s:string" />
    <wsdl:part name="V_Tnb_QwTz" type="s:string" />
    <wsdl:part name="V_Tnb_QwTzzs" type="s:string" />
  </wsdl:message>
  <wsdl:message name="F_InserTnbSfHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:portType name="DotNetServiceSoap">
    <wsdl:operation name="HelloWorld">
      <wsdl:input message="tns:HelloWorldSoapIn" />
      <wsdl:output message="tns:HelloWorldSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetMlYpGx">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;返回Zd_Ml_YpGx</wsdl:documentation>
      <wsdl:input message="tns:GetMlYpGxSoapIn" />
      <wsdl:output message="tns:GetMlYpGxSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetMlYpjx">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;返回Zd_Ml_Ypjx</wsdl:documentation>
      <wsdl:input message="tns:GetMlYpjxSoapIn" />
      <wsdl:output message="tns:GetMlYpjxSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetMlJb1">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;返回Zd_Ml_Jb1</wsdl:documentation>
      <wsdl:input message="tns:GetMlJb1SoapIn" />
      <wsdl:output message="tns:GetMlJb1SoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetMlJb3">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;返回Zd_Ml_Jb3</wsdl:documentation>
      <wsdl:input message="tns:GetMlJb3SoapIn" />
      <wsdl:output message="tns:GetMlJb3SoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetMlXm1">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;返回Zd_Ml_Xm1</wsdl:documentation>
      <wsdl:input message="tns:GetMlXm1SoapIn" />
      <wsdl:output message="tns:GetMlXm1SoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetMlXm3">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;返回Zd_Ml_Xm3</wsdl:documentation>
      <wsdl:input message="tns:GetMlXm3SoapIn" />
      <wsdl:output message="tns:GetMlXm3SoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetMlYp2">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;返回Zd_Ml_Yp2</wsdl:documentation>
      <wsdl:input message="tns:GetMlYp2SoapIn" />
      <wsdl:output message="tns:GetMlYp2SoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetMlYp3">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;返回Zd_Ml_Yp3</wsdl:documentation>
      <wsdl:input message="tns:GetMlYp3SoapIn" />
      <wsdl:output message="tns:GetMlYp3SoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetMlJbYpJx">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;返回基本药品目录剂型</wsdl:documentation>
      <wsdl:input message="tns:GetMlJbYpJxSoapIn" />
      <wsdl:output message="tns:GetMlJbYpJxSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="UpDate_Jblb">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;插入更新Zd_Ml_Jb1</wsdl:documentation>
      <wsdl:input message="tns:UpDate_JblbSoapIn" />
      <wsdl:output message="tns:UpDate_JblbSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="UpDate_Jb">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;插入更新Zd_Ml_Jb3</wsdl:documentation>
      <wsdl:input message="tns:UpDate_JbSoapIn" />
      <wsdl:output message="tns:UpDate_JbSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="UpDate_Xmlb">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;插入更新Zd_Ml_Xm1</wsdl:documentation>
      <wsdl:input message="tns:UpDate_XmlbSoapIn" />
      <wsdl:output message="tns:UpDate_XmlbSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="UpDate_Xm">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;插入更新Zd_Ml_Xm3</wsdl:documentation>
      <wsdl:input message="tns:UpDate_XmSoapIn" />
      <wsdl:output message="tns:UpDate_XmSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="UpDate_Yp2">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;插入更新Zd_Ml_Yp2</wsdl:documentation>
      <wsdl:input message="tns:UpDate_Yp2SoapIn" />
      <wsdl:output message="tns:UpDate_Yp2SoapOut" />
    </wsdl:operation>
    <wsdl:operation name="UpDate_Yp3">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;插入更新Zd_Ml_Yp3</wsdl:documentation>
      <wsdl:input message="tns:UpDate_Yp3SoapIn" />
      <wsdl:output message="tns:UpDate_Yp3SoapOut" />
    </wsdl:operation>
    <wsdl:operation name="UpDate_Ypjx">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;插入更新Zd_Ml_Ypjx</wsdl:documentation>
      <wsdl:input message="tns:UpDate_YpjxSoapIn" />
      <wsdl:output message="tns:UpDate_YpjxSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="UpDate_YpGx">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;插入更新Zd_Ml_YpGx</wsdl:documentation>
      <wsdl:input message="tns:UpDate_YpGxSoapIn" />
      <wsdl:output message="tns:UpDate_YpGxSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="Ckeck_Ybzl">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;判断一般诊疗费</wsdl:documentation>
      <wsdl:input message="tns:Ckeck_YbzlSoapIn" />
      <wsdl:output message="tns:Ckeck_YbzlSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetYbZL_Xm">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;返回一般诊疗费所有项目</wsdl:documentation>
      <wsdl:input message="tns:GetYbZL_XmSoapIn" />
      <wsdl:output message="tns:GetYbZL_XmSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="F_GetJkdaRyXx">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">人员基本信息数据返回</wsdl:documentation>
      <wsdl:input message="tns:F_GetJkdaRyXxSoapIn" />
      <wsdl:output message="tns:F_GetJkdaRyXxSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="F_GetJkdaJsr">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">经手人信息返回</wsdl:documentation>
      <wsdl:input message="tns:F_GetJkdaJsrSoapIn" />
      <wsdl:output message="tns:F_GetJkdaJsrSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="F_GetSupJsr">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">超管信息返回</wsdl:documentation>
      <wsdl:input message="tns:F_GetSupJsrSoapIn" />
      <wsdl:output message="tns:F_GetSupJsrSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="F_InserGxySf">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">插入高血压随访数据</wsdl:documentation>
      <wsdl:input message="tns:F_InserGxySfSoapIn" />
      <wsdl:output message="tns:F_InserGxySfSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="F_InserTnbSf">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">插入糖尿病随访数据</wsdl:documentation>
      <wsdl:input message="tns:F_InserTnbSfSoapIn" />
      <wsdl:output message="tns:F_InserTnbSfSoapOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:portType name="DotNetServiceHttpGet">
    <wsdl:operation name="HelloWorld">
      <wsdl:input message="tns:HelloWorldHttpGetIn" />
      <wsdl:output message="tns:HelloWorldHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="GetMlYpGx">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;返回Zd_Ml_YpGx</wsdl:documentation>
      <wsdl:input message="tns:GetMlYpGxHttpGetIn" />
      <wsdl:output message="tns:GetMlYpGxHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="GetMlYpjx">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;返回Zd_Ml_Ypjx</wsdl:documentation>
      <wsdl:input message="tns:GetMlYpjxHttpGetIn" />
      <wsdl:output message="tns:GetMlYpjxHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="GetMlJb1">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;返回Zd_Ml_Jb1</wsdl:documentation>
      <wsdl:input message="tns:GetMlJb1HttpGetIn" />
      <wsdl:output message="tns:GetMlJb1HttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="GetMlJb3">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;返回Zd_Ml_Jb3</wsdl:documentation>
      <wsdl:input message="tns:GetMlJb3HttpGetIn" />
      <wsdl:output message="tns:GetMlJb3HttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="GetMlXm1">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;返回Zd_Ml_Xm1</wsdl:documentation>
      <wsdl:input message="tns:GetMlXm1HttpGetIn" />
      <wsdl:output message="tns:GetMlXm1HttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="GetMlXm3">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;返回Zd_Ml_Xm3</wsdl:documentation>
      <wsdl:input message="tns:GetMlXm3HttpGetIn" />
      <wsdl:output message="tns:GetMlXm3HttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="GetMlYp2">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;返回Zd_Ml_Yp2</wsdl:documentation>
      <wsdl:input message="tns:GetMlYp2HttpGetIn" />
      <wsdl:output message="tns:GetMlYp2HttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="GetMlYp3">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;返回Zd_Ml_Yp3</wsdl:documentation>
      <wsdl:input message="tns:GetMlYp3HttpGetIn" />
      <wsdl:output message="tns:GetMlYp3HttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="GetMlJbYpJx">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;返回基本药品目录剂型</wsdl:documentation>
      <wsdl:input message="tns:GetMlJbYpJxHttpGetIn" />
      <wsdl:output message="tns:GetMlJbYpJxHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="UpDate_Jblb">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;插入更新Zd_Ml_Jb1</wsdl:documentation>
      <wsdl:input message="tns:UpDate_JblbHttpGetIn" />
      <wsdl:output message="tns:UpDate_JblbHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="UpDate_Jb">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;插入更新Zd_Ml_Jb3</wsdl:documentation>
      <wsdl:input message="tns:UpDate_JbHttpGetIn" />
      <wsdl:output message="tns:UpDate_JbHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="UpDate_Xmlb">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;插入更新Zd_Ml_Xm1</wsdl:documentation>
      <wsdl:input message="tns:UpDate_XmlbHttpGetIn" />
      <wsdl:output message="tns:UpDate_XmlbHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="UpDate_Xm">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;插入更新Zd_Ml_Xm3</wsdl:documentation>
      <wsdl:input message="tns:UpDate_XmHttpGetIn" />
      <wsdl:output message="tns:UpDate_XmHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="UpDate_Yp2">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;插入更新Zd_Ml_Yp2</wsdl:documentation>
      <wsdl:input message="tns:UpDate_Yp2HttpGetIn" />
      <wsdl:output message="tns:UpDate_Yp2HttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="UpDate_Yp3">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;插入更新Zd_Ml_Yp3</wsdl:documentation>
      <wsdl:input message="tns:UpDate_Yp3HttpGetIn" />
      <wsdl:output message="tns:UpDate_Yp3HttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="UpDate_Ypjx">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;插入更新Zd_Ml_Ypjx</wsdl:documentation>
      <wsdl:input message="tns:UpDate_YpjxHttpGetIn" />
      <wsdl:output message="tns:UpDate_YpjxHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="UpDate_YpGx">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;插入更新Zd_Ml_YpGx</wsdl:documentation>
      <wsdl:input message="tns:UpDate_YpGxHttpGetIn" />
      <wsdl:output message="tns:UpDate_YpGxHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="Ckeck_Ybzl">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;判断一般诊疗费</wsdl:documentation>
      <wsdl:input message="tns:Ckeck_YbzlHttpGetIn" />
      <wsdl:output message="tns:Ckeck_YbzlHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="GetYbZL_Xm">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;返回一般诊疗费所有项目</wsdl:documentation>
      <wsdl:input message="tns:GetYbZL_XmHttpGetIn" />
      <wsdl:output message="tns:GetYbZL_XmHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="F_GetJkdaRyXx">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">人员基本信息数据返回</wsdl:documentation>
      <wsdl:input message="tns:F_GetJkdaRyXxHttpGetIn" />
      <wsdl:output message="tns:F_GetJkdaRyXxHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="F_GetJkdaJsr">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">经手人信息返回</wsdl:documentation>
      <wsdl:input message="tns:F_GetJkdaJsrHttpGetIn" />
      <wsdl:output message="tns:F_GetJkdaJsrHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="F_GetSupJsr">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">超管信息返回</wsdl:documentation>
      <wsdl:input message="tns:F_GetSupJsrHttpGetIn" />
      <wsdl:output message="tns:F_GetSupJsrHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="F_InserGxySf">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">插入高血压随访数据</wsdl:documentation>
      <wsdl:input message="tns:F_InserGxySfHttpGetIn" />
      <wsdl:output message="tns:F_InserGxySfHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="F_InserTnbSf">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">插入糖尿病随访数据</wsdl:documentation>
      <wsdl:input message="tns:F_InserTnbSfHttpGetIn" />
      <wsdl:output message="tns:F_InserTnbSfHttpGetOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:portType name="DotNetServiceHttpPost">
    <wsdl:operation name="HelloWorld">
      <wsdl:input message="tns:HelloWorldHttpPostIn" />
      <wsdl:output message="tns:HelloWorldHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="GetMlYpGx">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;返回Zd_Ml_YpGx</wsdl:documentation>
      <wsdl:input message="tns:GetMlYpGxHttpPostIn" />
      <wsdl:output message="tns:GetMlYpGxHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="GetMlYpjx">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;返回Zd_Ml_Ypjx</wsdl:documentation>
      <wsdl:input message="tns:GetMlYpjxHttpPostIn" />
      <wsdl:output message="tns:GetMlYpjxHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="GetMlJb1">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;返回Zd_Ml_Jb1</wsdl:documentation>
      <wsdl:input message="tns:GetMlJb1HttpPostIn" />
      <wsdl:output message="tns:GetMlJb1HttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="GetMlJb3">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;返回Zd_Ml_Jb3</wsdl:documentation>
      <wsdl:input message="tns:GetMlJb3HttpPostIn" />
      <wsdl:output message="tns:GetMlJb3HttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="GetMlXm1">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;返回Zd_Ml_Xm1</wsdl:documentation>
      <wsdl:input message="tns:GetMlXm1HttpPostIn" />
      <wsdl:output message="tns:GetMlXm1HttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="GetMlXm3">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;返回Zd_Ml_Xm3</wsdl:documentation>
      <wsdl:input message="tns:GetMlXm3HttpPostIn" />
      <wsdl:output message="tns:GetMlXm3HttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="GetMlYp2">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;返回Zd_Ml_Yp2</wsdl:documentation>
      <wsdl:input message="tns:GetMlYp2HttpPostIn" />
      <wsdl:output message="tns:GetMlYp2HttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="GetMlYp3">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;返回Zd_Ml_Yp3</wsdl:documentation>
      <wsdl:input message="tns:GetMlYp3HttpPostIn" />
      <wsdl:output message="tns:GetMlYp3HttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="GetMlJbYpJx">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;返回基本药品目录剂型</wsdl:documentation>
      <wsdl:input message="tns:GetMlJbYpJxHttpPostIn" />
      <wsdl:output message="tns:GetMlJbYpJxHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="UpDate_Jblb">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;插入更新Zd_Ml_Jb1</wsdl:documentation>
      <wsdl:input message="tns:UpDate_JblbHttpPostIn" />
      <wsdl:output message="tns:UpDate_JblbHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="UpDate_Jb">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;插入更新Zd_Ml_Jb3</wsdl:documentation>
      <wsdl:input message="tns:UpDate_JbHttpPostIn" />
      <wsdl:output message="tns:UpDate_JbHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="UpDate_Xmlb">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;插入更新Zd_Ml_Xm1</wsdl:documentation>
      <wsdl:input message="tns:UpDate_XmlbHttpPostIn" />
      <wsdl:output message="tns:UpDate_XmlbHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="UpDate_Xm">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;插入更新Zd_Ml_Xm3</wsdl:documentation>
      <wsdl:input message="tns:UpDate_XmHttpPostIn" />
      <wsdl:output message="tns:UpDate_XmHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="UpDate_Yp2">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;插入更新Zd_Ml_Yp2</wsdl:documentation>
      <wsdl:input message="tns:UpDate_Yp2HttpPostIn" />
      <wsdl:output message="tns:UpDate_Yp2HttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="UpDate_Yp3">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;插入更新Zd_Ml_Yp3</wsdl:documentation>
      <wsdl:input message="tns:UpDate_Yp3HttpPostIn" />
      <wsdl:output message="tns:UpDate_Yp3HttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="UpDate_Ypjx">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;插入更新Zd_Ml_Ypjx</wsdl:documentation>
      <wsdl:input message="tns:UpDate_YpjxHttpPostIn" />
      <wsdl:output message="tns:UpDate_YpjxHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="UpDate_YpGx">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;插入更新Zd_Ml_YpGx</wsdl:documentation>
      <wsdl:input message="tns:UpDate_YpGxHttpPostIn" />
      <wsdl:output message="tns:UpDate_YpGxHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="Ckeck_Ybzl">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;判断一般诊疗费</wsdl:documentation>
      <wsdl:input message="tns:Ckeck_YbzlHttpPostIn" />
      <wsdl:output message="tns:Ckeck_YbzlHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="GetYbZL_Xm">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">&lt;内部使用&gt;返回一般诊疗费所有项目</wsdl:documentation>
      <wsdl:input message="tns:GetYbZL_XmHttpPostIn" />
      <wsdl:output message="tns:GetYbZL_XmHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="F_GetJkdaRyXx">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">人员基本信息数据返回</wsdl:documentation>
      <wsdl:input message="tns:F_GetJkdaRyXxHttpPostIn" />
      <wsdl:output message="tns:F_GetJkdaRyXxHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="F_GetJkdaJsr">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">经手人信息返回</wsdl:documentation>
      <wsdl:input message="tns:F_GetJkdaJsrHttpPostIn" />
      <wsdl:output message="tns:F_GetJkdaJsrHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="F_GetSupJsr">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">超管信息返回</wsdl:documentation>
      <wsdl:input message="tns:F_GetSupJsrHttpPostIn" />
      <wsdl:output message="tns:F_GetSupJsrHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="F_InserGxySf">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">插入高血压随访数据</wsdl:documentation>
      <wsdl:input message="tns:F_InserGxySfHttpPostIn" />
      <wsdl:output message="tns:F_InserGxySfHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="F_InserTnbSf">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">插入糖尿病随访数据</wsdl:documentation>
      <wsdl:input message="tns:F_InserTnbSfHttpPostIn" />
      <wsdl:output message="tns:F_InserTnbSfHttpPostOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="DotNetServiceSoap" type="tns:DotNetServiceSoap">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="HelloWorld">
      <soap:operation soapAction="http://tempuri.org/HelloWorld" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetMlYpGx">
      <soap:operation soapAction="http://tempuri.org/GetMlYpGx" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetMlYpjx">
      <soap:operation soapAction="http://tempuri.org/GetMlYpjx" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetMlJb1">
      <soap:operation soapAction="http://tempuri.org/GetMlJb1" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetMlJb3">
      <soap:operation soapAction="http://tempuri.org/GetMlJb3" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetMlXm1">
      <soap:operation soapAction="http://tempuri.org/GetMlXm1" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetMlXm3">
      <soap:operation soapAction="http://tempuri.org/GetMlXm3" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetMlYp2">
      <soap:operation soapAction="http://tempuri.org/GetMlYp2" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetMlYp3">
      <soap:operation soapAction="http://tempuri.org/GetMlYp3" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetMlJbYpJx">
      <soap:operation soapAction="http://tempuri.org/GetMlJbYpJx" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpDate_Jblb">
      <soap:operation soapAction="http://tempuri.org/UpDate_Jblb" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpDate_Jb">
      <soap:operation soapAction="http://tempuri.org/UpDate_Jb" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpDate_Xmlb">
      <soap:operation soapAction="http://tempuri.org/UpDate_Xmlb" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpDate_Xm">
      <soap:operation soapAction="http://tempuri.org/UpDate_Xm" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpDate_Yp2">
      <soap:operation soapAction="http://tempuri.org/UpDate_Yp2" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpDate_Yp3">
      <soap:operation soapAction="http://tempuri.org/UpDate_Yp3" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpDate_Ypjx">
      <soap:operation soapAction="http://tempuri.org/UpDate_Ypjx" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpDate_YpGx">
      <soap:operation soapAction="http://tempuri.org/UpDate_YpGx" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Ckeck_Ybzl">
      <soap:operation soapAction="http://tempuri.org/Ckeck_Ybzl" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetYbZL_Xm">
      <soap:operation soapAction="http://tempuri.org/GetYbZL_Xm" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="F_GetJkdaRyXx">
      <soap:operation soapAction="http://tempuri.org/F_GetJkdaRyXx" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="F_GetJkdaJsr">
      <soap:operation soapAction="http://tempuri.org/F_GetJkdaJsr" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="F_GetSupJsr">
      <soap:operation soapAction="http://tempuri.org/F_GetSupJsr" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="F_InserGxySf">
      <soap:operation soapAction="http://tempuri.org/F_InserGxySf" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="F_InserTnbSf">
      <soap:operation soapAction="http://tempuri.org/F_InserTnbSf" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="DotNetServiceSoap12" type="tns:DotNetServiceSoap">
    <soap12:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="HelloWorld">
      <soap12:operation soapAction="http://tempuri.org/HelloWorld" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetMlYpGx">
      <soap12:operation soapAction="http://tempuri.org/GetMlYpGx" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetMlYpjx">
      <soap12:operation soapAction="http://tempuri.org/GetMlYpjx" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetMlJb1">
      <soap12:operation soapAction="http://tempuri.org/GetMlJb1" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetMlJb3">
      <soap12:operation soapAction="http://tempuri.org/GetMlJb3" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetMlXm1">
      <soap12:operation soapAction="http://tempuri.org/GetMlXm1" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetMlXm3">
      <soap12:operation soapAction="http://tempuri.org/GetMlXm3" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetMlYp2">
      <soap12:operation soapAction="http://tempuri.org/GetMlYp2" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetMlYp3">
      <soap12:operation soapAction="http://tempuri.org/GetMlYp3" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetMlJbYpJx">
      <soap12:operation soapAction="http://tempuri.org/GetMlJbYpJx" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpDate_Jblb">
      <soap12:operation soapAction="http://tempuri.org/UpDate_Jblb" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpDate_Jb">
      <soap12:operation soapAction="http://tempuri.org/UpDate_Jb" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpDate_Xmlb">
      <soap12:operation soapAction="http://tempuri.org/UpDate_Xmlb" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpDate_Xm">
      <soap12:operation soapAction="http://tempuri.org/UpDate_Xm" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpDate_Yp2">
      <soap12:operation soapAction="http://tempuri.org/UpDate_Yp2" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpDate_Yp3">
      <soap12:operation soapAction="http://tempuri.org/UpDate_Yp3" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpDate_Ypjx">
      <soap12:operation soapAction="http://tempuri.org/UpDate_Ypjx" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpDate_YpGx">
      <soap12:operation soapAction="http://tempuri.org/UpDate_YpGx" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Ckeck_Ybzl">
      <soap12:operation soapAction="http://tempuri.org/Ckeck_Ybzl" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetYbZL_Xm">
      <soap12:operation soapAction="http://tempuri.org/GetYbZL_Xm" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="F_GetJkdaRyXx">
      <soap12:operation soapAction="http://tempuri.org/F_GetJkdaRyXx" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="F_GetJkdaJsr">
      <soap12:operation soapAction="http://tempuri.org/F_GetJkdaJsr" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="F_GetSupJsr">
      <soap12:operation soapAction="http://tempuri.org/F_GetSupJsr" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="F_InserGxySf">
      <soap12:operation soapAction="http://tempuri.org/F_InserGxySf" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="F_InserTnbSf">
      <soap12:operation soapAction="http://tempuri.org/F_InserTnbSf" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="DotNetServiceHttpGet" type="tns:DotNetServiceHttpGet">
    <http:binding verb="GET" />
    <wsdl:operation name="HelloWorld">
      <http:operation location="/HelloWorld" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetMlYpGx">
      <http:operation location="/GetMlYpGx" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetMlYpjx">
      <http:operation location="/GetMlYpjx" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetMlJb1">
      <http:operation location="/GetMlJb1" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetMlJb3">
      <http:operation location="/GetMlJb3" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetMlXm1">
      <http:operation location="/GetMlXm1" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetMlXm3">
      <http:operation location="/GetMlXm3" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetMlYp2">
      <http:operation location="/GetMlYp2" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetMlYp3">
      <http:operation location="/GetMlYp3" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetMlJbYpJx">
      <http:operation location="/GetMlJbYpJx" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpDate_Jblb">
      <http:operation location="/UpDate_Jblb" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpDate_Jb">
      <http:operation location="/UpDate_Jb" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpDate_Xmlb">
      <http:operation location="/UpDate_Xmlb" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpDate_Xm">
      <http:operation location="/UpDate_Xm" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpDate_Yp2">
      <http:operation location="/UpDate_Yp2" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpDate_Yp3">
      <http:operation location="/UpDate_Yp3" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpDate_Ypjx">
      <http:operation location="/UpDate_Ypjx" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpDate_YpGx">
      <http:operation location="/UpDate_YpGx" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Ckeck_Ybzl">
      <http:operation location="/Ckeck_Ybzl" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetYbZL_Xm">
      <http:operation location="/GetYbZL_Xm" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="F_GetJkdaRyXx">
      <http:operation location="/F_GetJkdaRyXx" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="F_GetJkdaJsr">
      <http:operation location="/F_GetJkdaJsr" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="F_GetSupJsr">
      <http:operation location="/F_GetSupJsr" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="F_InserGxySf">
      <http:operation location="/F_InserGxySf" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="F_InserTnbSf">
      <http:operation location="/F_InserTnbSf" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="DotNetServiceHttpPost" type="tns:DotNetServiceHttpPost">
    <http:binding verb="POST" />
    <wsdl:operation name="HelloWorld">
      <http:operation location="/HelloWorld" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetMlYpGx">
      <http:operation location="/GetMlYpGx" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetMlYpjx">
      <http:operation location="/GetMlYpjx" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetMlJb1">
      <http:operation location="/GetMlJb1" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetMlJb3">
      <http:operation location="/GetMlJb3" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetMlXm1">
      <http:operation location="/GetMlXm1" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetMlXm3">
      <http:operation location="/GetMlXm3" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetMlYp2">
      <http:operation location="/GetMlYp2" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetMlYp3">
      <http:operation location="/GetMlYp3" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetMlJbYpJx">
      <http:operation location="/GetMlJbYpJx" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpDate_Jblb">
      <http:operation location="/UpDate_Jblb" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpDate_Jb">
      <http:operation location="/UpDate_Jb" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpDate_Xmlb">
      <http:operation location="/UpDate_Xmlb" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpDate_Xm">
      <http:operation location="/UpDate_Xm" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpDate_Yp2">
      <http:operation location="/UpDate_Yp2" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpDate_Yp3">
      <http:operation location="/UpDate_Yp3" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpDate_Ypjx">
      <http:operation location="/UpDate_Ypjx" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpDate_YpGx">
      <http:operation location="/UpDate_YpGx" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Ckeck_Ybzl">
      <http:operation location="/Ckeck_Ybzl" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetYbZL_Xm">
      <http:operation location="/GetYbZL_Xm" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="F_GetJkdaRyXx">
      <http:operation location="/F_GetJkdaRyXx" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="F_GetJkdaJsr">
      <http:operation location="/F_GetJkdaJsr" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="F_GetSupJsr">
      <http:operation location="/F_GetSupJsr" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="F_InserGxySf">
      <http:operation location="/F_InserGxySf" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="F_InserTnbSf">
      <http:operation location="/F_InserTnbSf" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="DotNetService">
    <wsdl:port name="DotNetServiceSoap" binding="tns:DotNetServiceSoap">
      <soap:address location="http://127.0.0.1/His_DataBase/DotNetService.asmx" />
    </wsdl:port>
    <wsdl:port name="DotNetServiceSoap12" binding="tns:DotNetServiceSoap12">
      <soap12:address location="http://127.0.0.1/His_DataBase/DotNetService.asmx" />
    </wsdl:port>
    <wsdl:port name="DotNetServiceHttpGet" binding="tns:DotNetServiceHttpGet">
      <http:address location="http://127.0.0.1/His_DataBase/DotNetService.asmx" />
    </wsdl:port>
    <wsdl:port name="DotNetServiceHttpPost" binding="tns:DotNetServiceHttpPost">
      <http:address location="http://127.0.0.1/His_DataBase/DotNetService.asmx" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>