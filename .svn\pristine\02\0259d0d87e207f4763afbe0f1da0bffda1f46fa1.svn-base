﻿/**  版本信息模板在安装目录下，可自行修改。
* D_Materials_Return1.cs
*
* 功 能： N/A
* 类 名： D_Materials_Return1
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016-11-30 17:16:47   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using System.Collections.Generic;

namespace DAL
{
    /// <summary>
    /// 数据访问类:D_Materials_Return1
    /// </summary>
    public partial class D_Materials_Return1
    {
        public D_Materials_Return1()
        { }
        #region  BasicMethod

        public string MaxCode(string date)
        {
            string max = date + (string)(HisVar.HisVar.Sqldal.F_MaxCode("select right(max(M_Return_Code),5) from Materials_Return1 where left(M_Return_Code,6)='" + date + "'", 5));
            return max;
        }
        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(string M_Return_Code)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from Materials_Return1");
            strSql.Append(" where M_Return_Code=@M_Return_Code ");
            SqlParameter[] parameters = {
					new SqlParameter("@M_Return_Code", SqlDbType.Char,11)			};
            parameters[0].Value = M_Return_Code;

            return HisVar.HisVar.Sqldal.Exists(strSql.ToString(), parameters);
        }

        public DataSet GetHzList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM Materials_Return1,Materials_Return2,dbo.Materials_Warehouse_Dict ,dbo.Zd_YyJsr,dbo.Materials_Dict,dbo.Materials_Stock,dbo.Materials_Sup_Dict ");
            strSql.Append(" WHERE Materials_Return1.M_Return_Code=Materials_Return2.M_Return_Code AND Materials_Return1.MaterialsWh_Code=Materials_Warehouse_Dict.MaterialsWh_Code ");
            strSql.Append("  AND Materials_Return1.Jsr_Code=Zd_YyJsr.Jsr_Code AND Materials_Return2.Materials_Code=Materials_Dict.Materials_Code  ");
            strSql.Append(" AND Materials_Return2.MaterialsStock_Code=Materials_Stock.MaterialsStock_Code AND Materials_Return1.MaterialsSup_Code=Materials_Sup_Dict.MaterialsSup_Code ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(strWhere);
            }
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }
        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ModelOld.M_Materials_Return1 model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into Materials_Return1(");
            strSql.Append("M_Return_Code,MaterialsSup_Code,MaterialsWh_Code,Return_Date,Input_Date,Finish_Date,Jsr_Code,TotalMoney,M_Return_Memo,OrdersStatus,WriteOff_Code,WriteOffStatus)");
            strSql.Append(" values (");
            strSql.Append("@M_Return_Code,@MaterialsSup_Code,@MaterialsWh_Code,@Return_Date,@Input_Date,@Finish_Date,@Jsr_Code,@TotalMoney,@M_Return_Memo,@OrdersStatus,@WriteOff_Code,@WriteOffStatus)");
            SqlParameter[] parameters = {
					new SqlParameter("@M_Return_Code", SqlDbType.Char,11),
					new SqlParameter("@MaterialsSup_Code", SqlDbType.Char,5),
					new SqlParameter("@MaterialsWh_Code", SqlDbType.Char,2),
					new SqlParameter("@Return_Date", SqlDbType.SmallDateTime),
					new SqlParameter("@Input_Date", SqlDbType.SmallDateTime),
					new SqlParameter("@Finish_Date", SqlDbType.SmallDateTime),
					new SqlParameter("@Jsr_Code", SqlDbType.Char,7),
					new SqlParameter("@TotalMoney", SqlDbType.Decimal,5),
					new SqlParameter("@M_Return_Memo", SqlDbType.VarChar,50),
					new SqlParameter("@OrdersStatus", SqlDbType.VarChar,10),
					new SqlParameter("@WriteOff_Code", SqlDbType.Char,11),
					new SqlParameter("@WriteOffStatus", SqlDbType.VarChar,10)};
            parameters[0].Value = model.M_Return_Code;
            parameters[1].Value = model.MaterialsSup_Code;
            parameters[2].Value = model.MaterialsWh_Code;
            parameters[3].Value = Common.Tools.IsValueNull(model.Return_Date);
            parameters[4].Value = model.Input_Date;
            parameters[5].Value = Common.Tools.IsValueNull(model.Finish_Date);
            parameters[6].Value = model.Jsr_Code;
            parameters[7].Value = model.TotalMoney;
            parameters[8].Value = model.M_Return_Memo;
            parameters[9].Value = model.OrdersStatus;
            parameters[10].Value = Common.Tools.IsValueNull(model.WriteOff_Code);
            parameters[11].Value = Common.Tools.IsValueNull(model.WriteOffStatus);

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ModelOld.M_Materials_Return1 model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update Materials_Return1 set ");
            strSql.Append("MaterialsSup_Code=@MaterialsSup_Code,");
            strSql.Append("MaterialsWh_Code=@MaterialsWh_Code,");
            strSql.Append("Return_Date=@Return_Date,");
            strSql.Append("Input_Date=@Input_Date,");
            strSql.Append("Finish_Date=@Finish_Date,");
            strSql.Append("Jsr_Code=@Jsr_Code,");
            strSql.Append("TotalMoney=@TotalMoney,");
            strSql.Append("M_Return_Memo=@M_Return_Memo,");
            strSql.Append("OrdersStatus=@OrdersStatus,");
            strSql.Append("WriteOff_Code=@WriteOff_Code,");
            strSql.Append("WriteOffStatus=@WriteOffStatus");
            strSql.Append(" where M_Return_Code=@M_Return_Code ");
            SqlParameter[] parameters = {
					new SqlParameter("@MaterialsSup_Code", SqlDbType.Char,5),
					new SqlParameter("@MaterialsWh_Code", SqlDbType.Char,2),
					new SqlParameter("@Return_Date", SqlDbType.SmallDateTime),
					new SqlParameter("@Input_Date", SqlDbType.SmallDateTime),
					new SqlParameter("@Finish_Date", SqlDbType.SmallDateTime),
					new SqlParameter("@Jsr_Code", SqlDbType.Char,7),
					new SqlParameter("@TotalMoney", SqlDbType.Decimal,5),
					new SqlParameter("@M_Return_Memo", SqlDbType.VarChar,50),
					new SqlParameter("@OrdersStatus", SqlDbType.VarChar,10),
					new SqlParameter("@WriteOff_Code", SqlDbType.Char,11),
					new SqlParameter("@WriteOffStatus", SqlDbType.VarChar,10),
					new SqlParameter("@M_Return_Code", SqlDbType.Char,11)};
            parameters[0].Value = model.MaterialsSup_Code;
            parameters[1].Value = model.MaterialsWh_Code;
            parameters[2].Value = model.Return_Date;
            parameters[3].Value = model.Input_Date;
            parameters[4].Value = Common.Tools.IsValueNull(model.Finish_Date);
            parameters[5].Value = model.Jsr_Code;
            parameters[6].Value = model.TotalMoney;
            parameters[7].Value = model.M_Return_Memo;
            parameters[8].Value = model.OrdersStatus;
            parameters[9].Value = Common.Tools.IsValueNull(model.WriteOff_Code);
            parameters[10].Value = Common.Tools.IsValueNull(model.WriteOffStatus);
            parameters[11].Value = model.M_Return_Code;

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 单据完成,WriteOffStatus为被冲销和全部被冲销
        /// </summary>
        public bool Complete(ModelOld.M_Materials_Return1 model)
        {
            List<string> sqlList = new List<string>();
            List<SqlParameter[]> parametersList = new List<SqlParameter[]>();

            //单据状态改成完成,填写完成时间
            StringBuilder strSql1 = new StringBuilder();
            strSql1.Append("update Materials_Return1 set ");
            strSql1.Append("OrdersStatus=@OrdersStatus,");
            strSql1.Append("Finish_Date=@Finish_Date");
            strSql1.Append(" where M_Return_Code=@M_Return_Code ");
            SqlParameter[] parameters1 = {
					new SqlParameter("@OrdersStatus", SqlDbType.VarChar,10),
                    new SqlParameter("@Finish_Date", SqlDbType.SmallDateTime),
					new SqlParameter("@M_Return_Code", SqlDbType.Char,11)};
            parameters1[0].Value = model.OrdersStatus;
            parameters1[1].Value = model.Finish_Date;
            parameters1[2].Value = model.M_Return_Code;

            sqlList.Add(strSql1.ToString());
            parametersList.Add(parameters1);

            if (model.WriteOffStatus == "冲销")
            {
                //更改原单据的冲销数量，实际数量，实际金额
                StringBuilder strSql2 = new StringBuilder();
                strSql2.Append("UPDATE Materials_Return2 SET  ");
                strSql2.Append("M_Return_WriteoffNo=M_Return_WriteoffNo+a.M_Return_Num, ");
                strSql2.Append("M_Return_RealNo=M_Return_RealNo+a.M_Return_Num, ");
                strSql2.Append("M_Return_RealMoney=(M_Return_RealNo+a.M_Return_Num)*M_Return_Price  ");
                strSql2.Append("FROM  (SELECT M_Return_Num,WriteOff_Code,MaterialsStock_Code FROM Materials_Return2,Materials_Return1  ");
                strSql2.Append("WHERE  ");
                strSql2.Append("Materials_Return1.M_Return_Code=Materials_Return2.M_Return_Code  ");
                strSql2.Append("AND Materials_Return1.M_Return_Code=@M_Return_Code)a ");
                strSql2.Append("WHERE  ");
                strSql2.Append("M_Return_Code=a.WriteOff_Code And Materials_Return2.MaterialsStock_Code=a.MaterialsStock_Code");
                SqlParameter[] parameters2 = {
					new SqlParameter("@M_Return_Code", SqlDbType.Char,11)};
                parameters2[0].Value = model.M_Return_Code;
                sqlList.Add(strSql2.ToString());
                parametersList.Add(parameters2);

                //冲销单完成，原单据改成被冲销
                StringBuilder strSql3 = new StringBuilder();
                strSql3.Append("update Materials_Return1 set ");
                strSql3.Append("WriteOffStatus=@WriteOffStatus,TotalMoney=a.TotalMoney ");
                strSql3.Append("From (select Sum(M_Return_RealMoney) TotalMoney,M_Return_Code from Materials_Return2 group by M_Return_Code)a ");
                strSql3.Append(" where Materials_Return1.M_Return_Code=@M_Return_Code and a.M_Return_Code=Materials_Return1.M_Return_Code ");
                SqlParameter[] parameters3 = {
					new SqlParameter("@WriteOffStatus", SqlDbType.VarChar,10),
					new SqlParameter("@M_Return_Code", SqlDbType.Char,11)};
                parameters3[0].Value = "被冲销";
                parameters3[1].Value = model.WriteOff_Code;

                sqlList.Add(strSql3.ToString());
                parametersList.Add(parameters3);
            }

            //库存减掉退库数量（因为冲销是负数，减负等于加正）
            StringBuilder strSqlend = new StringBuilder();
            strSqlend.Append("UPDATE Materials_Stock SET MaterialsStore_Num=MaterialsStore_Num-M_Return_Num   ");
            strSqlend.Append("FROM Materials_Return2 ");
            strSqlend.Append("WHERE Materials_Stock.MaterialsStock_Code=Materials_Return2.MaterialsStock_Code ");
            strSqlend.Append("AND M_Return_Code=@M_Return_Code ");
            SqlParameter[] parametersend = {
					new SqlParameter("@M_Return_Code", SqlDbType.Char,11)};
            parametersend[0].Value = model.M_Return_Code;

            sqlList.Add(strSqlend.ToString());
            parametersList.Add(parametersend);

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(sqlList, parametersList);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 生成冲销数据
        /// </summary>
        public bool AddWriteOff(ModelOld.M_Materials_Return1 model)
        {
            List<string> sqlList = new List<string>();
            List<SqlParameter[]> parametersList = new List<SqlParameter[]>();

            StringBuilder strSql1 = new StringBuilder();
            strSql1.Append("insert into Materials_Return1(");
            strSql1.Append("M_Return_Code,MaterialsSup_Code,MaterialsWh_Code,Return_Date,Input_Date,Finish_Date,Jsr_Code,TotalMoney,M_Return_Memo,OrdersStatus,WriteOff_Code,WriteOffStatus)");
            strSql1.Append(" values (");
            strSql1.Append("@M_Return_Code,@MaterialsSup_Code,@MaterialsWh_Code,@Return_Date,@Input_Date,@Finish_Date,@Jsr_Code,@TotalMoney,@M_Return_Memo,@OrdersStatus,@WriteOff_Code,@WriteOffStatus)");
            SqlParameter[] parameters1 = {
					new SqlParameter("@M_Return_Code", SqlDbType.Char,11),
					new SqlParameter("@MaterialsSup_Code", SqlDbType.Char,5),
					new SqlParameter("@MaterialsWh_Code", SqlDbType.Char,2),
					new SqlParameter("@Return_Date", SqlDbType.SmallDateTime),
					new SqlParameter("@Input_Date", SqlDbType.SmallDateTime),
					new SqlParameter("@Finish_Date", SqlDbType.SmallDateTime),
					new SqlParameter("@Jsr_Code", SqlDbType.Char,7),
					new SqlParameter("@TotalMoney", SqlDbType.Decimal,5),
					new SqlParameter("@M_Return_Memo", SqlDbType.VarChar,50),
					new SqlParameter("@OrdersStatus", SqlDbType.VarChar,10),
					new SqlParameter("@WriteOff_Code", SqlDbType.Char,11),
					new SqlParameter("@WriteOffStatus", SqlDbType.VarChar,10)};
            parameters1[0].Value = model.M_Return_Code;
            parameters1[1].Value = model.MaterialsSup_Code;
            parameters1[2].Value = model.MaterialsWh_Code;
            parameters1[3].Value = Common.Tools.IsValueNull(model.Return_Date);
            parameters1[4].Value = model.Input_Date;
            parameters1[5].Value = Common.Tools.IsValueNull(model.Finish_Date);
            parameters1[6].Value = model.Jsr_Code;
            parameters1[7].Value = model.TotalMoney;
            parameters1[8].Value = model.M_Return_Memo;
            parameters1[9].Value = model.OrdersStatus;
            parameters1[10].Value = Common.Tools.IsValueNull(model.WriteOff_Code);
            parameters1[11].Value = Common.Tools.IsValueNull(model.WriteOffStatus);

            sqlList.Add(strSql1.ToString());
            parametersList.Add(parameters1);

            StringBuilder strSql2 = new StringBuilder();
            strSql2.Append("INSERT INTO Materials_Return2 ");
            strSql2.Append("SELECT @M_Return_Code,Materials_Code,MaterialsStock_Code, ");
            strSql2.Append("@M_Return_Code+SUBSTRING(M_Return_Detail_Code,12,4),MaterialsLot,MaterialsExpiryDate, ");
            strSql2.Append("-M_Return_RealNo,0,0,M_Return_Price,-M_Return_RealMoney,0, ");
            strSql2.Append("M_ReturnDetail_Memo ");
            strSql2.Append("FROM Materials_Return2 where M_Return_Code=@WriteOff_Code AND M_Return_RealNo > 0");
            SqlParameter[] parameters2 =
            {
                new SqlParameter("@M_Return_Code", SqlDbType.Char, 11),
                new SqlParameter("@WriteOff_Code", SqlDbType.Char, 11)
            };
            parameters2[0].Value = model.M_Return_Code;
            parameters2[1].Value = model.WriteOff_Code;
            sqlList.Add(strSql2.ToString());
            parametersList.Add(parameters2);

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(sqlList, parametersList);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }

        }
        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(string M_Return_Code)
        {
            List<string> sqlList = new List<string>();
            List<SqlParameter[]> parametersList = new List<SqlParameter[]>();

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from Materials_Return2 ");
            strSql.Append(" where M_Return_Code=@M_Return_Code ");
            SqlParameter[] parameters = {
					new SqlParameter("@M_Return_Code", SqlDbType.Char,15)			};
            parameters[0].Value = M_Return_Code;

            sqlList.Add(strSql.ToString());
            parametersList.Add(parameters);

            strSql = new StringBuilder();
            strSql.Append("delete from Materials_Return1 ");
            strSql.Append(" where M_Return_Code=@M_Return_Code ");
            parameters = new SqlParameter[] {
					new SqlParameter("@M_Return_Code", SqlDbType.Char,11)			};
            parameters[0].Value = M_Return_Code;


            sqlList.Add(strSql.ToString());
            parametersList.Add(parameters);

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(sqlList, parametersList);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string M_Return_Codelist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from Materials_Return1 ");
            strSql.Append(" where M_Return_Code in (" + M_Return_Codelist + ")  ");
            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ModelOld.M_Materials_Return1 GetModel(string M_Return_Code)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 M_Return_Code,MaterialsSup_Code,MaterialsWh_Code,Return_Date,Input_Date,Finish_Date,Materials_Return1.Jsr_Code,Jsr_name,TotalMoney,M_Return_Memo,OrdersStatus,WriteOff_Code,WriteOffStatus from Materials_Return1 JOIN dbo.Zd_YyJsr ON dbo.Materials_Return1.Jsr_Code = dbo.Zd_YyJsr.Jsr_Code");
            strSql.Append(" where M_Return_Code=@M_Return_Code ");
            SqlParameter[] parameters = {
					new SqlParameter("@M_Return_Code", SqlDbType.Char,11)			};
            parameters[0].Value = M_Return_Code;

            ModelOld.M_Materials_Return1 model = new ModelOld.M_Materials_Return1();
            DataSet ds = HisVar.HisVar.Sqldal.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ModelOld.M_Materials_Return1 DataRowToModel(DataRow row)
        {
            ModelOld.M_Materials_Return1 model = new ModelOld.M_Materials_Return1();
            if (row != null)
            {
                if (row["M_Return_Code"] != null)
                {
                    model.M_Return_Code = row["M_Return_Code"].ToString();
                }
                if (row["MaterialsSup_Code"] != null)
                {
                    model.MaterialsSup_Code = row["MaterialsSup_Code"].ToString();
                }
                if (row["MaterialsWh_Code"] != null)
                {
                    model.MaterialsWh_Code = row["MaterialsWh_Code"].ToString();
                }
                if (row["Return_Date"] != null && row["Return_Date"].ToString() != "")
                {
                    model.Return_Date = DateTime.Parse(row["Return_Date"].ToString());
                }
                if (row["Input_Date"] != null && row["Input_Date"].ToString() != "")
                {
                    model.Input_Date = DateTime.Parse(row["Input_Date"].ToString());
                }
                if (row["Finish_Date"] != null && row["Finish_Date"].ToString() != "")
                {
                    model.Finish_Date = DateTime.Parse(row["Finish_Date"].ToString());
                }
                if (row["Jsr_Code"] != null)
                {
                    model.Jsr_Code = row["Jsr_Code"].ToString();
                }
                if (row["Jsr_Name"] != null)
                {
                    model.Jsr_Name = row["Jsr_Name"].ToString();
                }
                if (row["TotalMoney"] != null && row["TotalMoney"].ToString() != "")
                {
                    model.TotalMoney = decimal.Parse(row["TotalMoney"].ToString());
                }
                if (row["M_Return_Memo"] != null)
                {
                    model.M_Return_Memo = row["M_Return_Memo"].ToString();
                }
                if (row["OrdersStatus"] != null)
                {
                    model.OrdersStatus = row["OrdersStatus"].ToString();
                }
                if (row["WriteOff_Code"] != null)
                {
                    model.WriteOff_Code = row["WriteOff_Code"].ToString();
                }
                if (row["WriteOffStatus"] != null)
                {
                    model.WriteOffStatus = row["WriteOffStatus"].ToString();
                }
            }
            return model;
        }

        /// <summary>
        ///总金额
        /// </summary>
        public double GetSumMoney(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT SUM(TotalMoney)  FROM dbo.Materials_Return1  where OrdersStatus='完成'");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" and " + strWhere);
            }
            try
            {
                double douSum = Double.Parse(HisVar.HisVar.Sqldal.GetSingle(strSql.ToString()).ToString());
                return douSum;
            }
            catch (Exception)
            {
                return 0;
            }

        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT M_Return_Code,Materials_Return1. MaterialsSup_Code,MaterialsSup_Name,Materials_Return1. MaterialsWh_Code,MaterialsWh_Name, Return_Date, Input_Date, Finish_Date, Materials_Return1.Jsr_Code,Jsr_Name, TotalMoney, M_Return_Memo, OrdersStatus, WriteOff_Code, WriteOffStatus ");
            strSql.Append(" FROM dbo.Zd_YyJsr JOIN  dbo.Materials_Return1 ON dbo.Zd_YyJsr.Jsr_Code = dbo.Materials_Return1.Jsr_Code");
            strSql.Append("  JOIN dbo.Materials_Sup_Dict ON dbo.Materials_Return1.MaterialsSup_Code = dbo.Materials_Sup_Dict.MaterialsSup_Code");
            strSql.Append("  JOIN dbo.Materials_Warehouse_Dict ON dbo.Materials_Return1.MaterialsWh_Code=dbo.Materials_Warehouse_Dict.MaterialsWh_Code");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" M_Return_Code,MaterialsSup_Code,MaterialsWh_Code,Return_Date,Input_Date,Finish_Date,Jsr_Code,TotalMoney,M_Return_Memo,OrdersStatus,WriteOff_Code,WriteOffStatus ");
            strSql.Append(" FROM Materials_Return1 ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM Materials_Return1 ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.M_Return_Code desc");
            }
            strSql.Append(")AS Row, T.*  from Materials_Return1 T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

        /*
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetList(int PageSize,int PageIndex,string strWhere)
        {
            SqlParameter[] parameters = {
                    new SqlParameter("@tblName", SqlDbType.VarChar, 255),
                    new SqlParameter("@fldName", SqlDbType.VarChar, 255),
                    new SqlParameter("@PageSize", SqlDbType.Int),
                    new SqlParameter("@PageIndex", SqlDbType.Int),
                    new SqlParameter("@IsReCount", SqlDbType.Bit),
                    new SqlParameter("@OrderType", SqlDbType.Bit),
                    new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
                    };
            parameters[0].Value = "Materials_Return1";
            parameters[1].Value = "M_Return_Code";
            parameters[2].Value = PageSize;
            parameters[3].Value = PageIndex;
            parameters[4].Value = 0;
            parameters[5].Value = 0;
            parameters[6].Value = strWhere;	
            return HisVar.HisVar.Sqldal.RunProcedure("UP_GetRecordByPage",parameters,"ds");
        }*/

        #endregion  BasicMethod
        #region  ExtensionMethod

        #endregion  ExtensionMethod
    }
}

