﻿Imports System
Imports System.Reflection
Imports System.Runtime.InteropServices

' 有关程序集的常规信息通过下列属性集
' 控制。更改这些属性值可修改
' 与程序集关联的信息。

' 查看程序集属性的值

<Assembly: AssemblyTitle("HisPara")> 
<Assembly: AssemblyDescription("")> 
<Assembly: AssemblyCompany("China")> 
<Assembly: AssemblyProduct("HisPara")> 
<Assembly: AssemblyCopyright("Copyright © China 2011")> 
<Assembly: AssemblyTrademark("")> 

<Assembly: ComVisible(False)>

'如果此项目向 COM 公开，则下列 GUID 用于类型库的 ID
<Assembly: Guid("00120a1e-01b4-415c-9ee5-41cccd2e2929")> 

' 程序集的版本信息由下面四个值组成:
'
'      主版本
'      次版本
'      内部版本号
'      修订号
'
' 可以指定所有这些值，也可以使用“内部版本号”和“修订号”的默认值，
' 方法是按如下所示使用“*”:
' <Assembly: AssemblyVersion("1.0.*")> 

<Assembly: AssemblyVersion("*******")> 
<Assembly: AssemblyFileVersion("*******")> 
