﻿Imports System.Data.SqlClient
Imports System.Text
Imports BaseClass
Imports ZTHisPublicFunction


Public Class Xs_Zy_Sqcy

#Region "定义__变量"
    Dim V_Sum As Double                                 '金额小计
    Dim Zb_Sum As Double
    Dim Cb_Row As DataRow                            '当前选择行
    Dim V_Bl_Code As String
    Public My_Dataset As New DataSet
    Dim modelPatientInfo As Modelold.M_PatientInfo
    Dim nurseDel As BaseClass.DelegateList.MyDelegateOne
#End Region

    Public Sub New(ByRef patient As Modelold.M_PatientInfo, ByVal _nurseDel As BaseClass.DelegateList.MyDelegateOne)
        ' 此调用是设计器所必需的。
        InitializeComponent()
        ' 在 InitializeComponent() 调用之后添加任何初始化。
        modelPatientInfo = patient
        nurseDel = _nurseDel
    End Sub
    Private Sub Xs_Zy_Cy1_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Me.DateTimePicker1.Value = Now
        Call Data_Init()
        Call Form_Init()
        If modelPatientInfo IsNot Nothing Then
            C1Combo3.SelectedValue = modelPatientInfo.Bl_Code
        End If
        C1Combo3.Select()
    End Sub

#Region "窗体__事件"

    Private Sub Data_Init()
        '病人出院
        Dim strSql As New StringBuilder
        strSql.Append("Select Bl.Bl_Code,Ry_Jc,Ry_Name,Ry_Sex,Ry_Sfzh,Ry_RyDate,Ks_Name,Ry_BlCode,Isnull(Jf_Money,0)Jf_Money FROM Zd_YyKs,Bl Left Join (Select Sum(Jf_Money) AS Jf_Money,Bl_Code From Bl_Jf Group By Bl_Code) B On Bl.Bl_Code = B.Bl_Code Where Bl.Ks_Code=Zd_YyKs.Ks_Code And Ry_CyDate is null And isnull(Cy_Qr,'否')<>'是'")
        If HisPara.PublicConfig.ZyCfKsXz = "是" Then
            strSql.Append(" and Bl.Ks_Code = '" & HisVar.HisVar.XmKs & "' ")
        End If
        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, strSql.ToString(), "病人出院", True)
        My_Dataset.Tables("病人出院").PrimaryKey = New DataColumn() {My_Dataset.Tables("病人出院").Columns("Bl_Code")}
    End Sub

    Private Sub Form_Init()

        T_Label5.Location = New Point(Me.Width - T_Label5.Width - 7, 8)
        T_Label4.Location = New Point(Me.Width - T_Label5.Width - T_Label4.Width - 7, 8)

        '按扭初始化
        Call P_Comm(Me.Comm3)

        Dim My_Combo3 As New BaseClass.C_Combo2(Me.C1Combo3, My_Dataset.Tables("病人出院").DefaultView, "Ry_Name", "Bl_Code", 420)
        With My_Combo3
            .Init_TDBCombo()
            .Init_Colum("Ry_Jc", "简称", 55, "左")
            .Init_Colum("Ry_Name", "姓名", 55, "左")
            .Init_Colum("Jf_Money", "0", 0, "右")
            .Init_Colum("Ry_Sex", "性别", 55, "左")
            .Init_Colum("Ry_Sfzh", "身份证号", 100, "中")
            .Init_Colum("Bl_Code", "", 0, "左")
            .Init_Colum("Ry_RyDate", "入院日期", 70, "中")
            .Init_Colum("Ks_Name", "科室", 60, "中")
            .Init_Colum("Ry_BlCode", "住院号", 0, "中")
            .MaxDropDownItems(17)
            .SelectedIndex(-1)
        End With
        C1Combo3.AutoCompletion = False
        C1Combo3.AutoSelect = False
        C1Combo3.Columns("Ry_RyDate").NumberFormat = "yyyy-MM-dd"

        Dim My_Grid As New BaseClass.C_Grid(Me.C1TrueDBGrid1)
        With My_Grid
            .Init_Grid()
            .AllSort(False)
            .AllDelete(False)
            .P_Dock(BaseClass.C_Grid.Dock.Fill)
            .Init_Column("类别", "Cf_Lb", 180, "左", "")
            .Init_Column("数量", "Cf_Sl", 120, "右", "###,###,##0")
            .Init_Column("金额", "Cf_Money", 70, "右", "###,###,##0.00")

        End With
        C1TrueDBGrid1.HScrollBar.Style = C1.Win.C1TrueDBGrid.ScrollBarStyleEnum.Automatic
    End Sub

#End Region

#Region "用药__显示"

    Private Sub P_Data_Show(ByVal Bl_Code As String)   '从表数据


        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "SELECT '1' AS V_Order,Sum(Cf_Sl) AS Cf_Sl,Sum( Bl_Cfxm.Cf_Money) as Cf_Money,Zd_JkFl1.Lb_Name AS Cf_Lb,Bl_Cf.Bl_Code from Zd_JkFl2,Zd_JkFl1,Bl_Cf,Bl_Cfxm Where Zd_JkFl2.Lb_Code = Zd_JkFl1.Lb_Code and Bl_Cf.Cf_Code = Bl_Cfxm.Cf_Code and Zd_JkFl2.Mx_Code = Bl_Cfxm.Xm_Code AND  Zd_JkFl2.Yy_Code = Bl_Cfxm.Yy_Code and Bl_Code='" & Bl_Code & "' And Cf_Qr='是' Group By Zd_JkFl1.Lb_Name,Bl_Cf.Bl_Code Union all SELECT '2' AS V_Order,Sum(Cf_Sl) AS Cf_Sl,Sum(Bl_Cfyp.Cf_Money) as Cf_Money,Cf_Lb,Bl_Code FROM Bl_Cf INNER JOIN Bl_Cfyp ON Bl_Cf.Cf_Code = dbo.Bl_Cfyp.Cf_Code Where Bl_Code='" & Bl_Code & "' And Cf_Qr='是' Group By Cf_Lb,Bl_Code", "明细表", True)
        C1TrueDBGrid1.SetDataBinding(My_Dataset, "明细表", True)

        Call P_SumMoney()

        C1TextBox18.Text = Format(V_Sum, "0.00")
        C1TextBox19.Text = Format(IIf(Label10.Text = "", 0, Label10.Text) - C1TextBox18.Text, "0.00")
        If C1TextBox19.Text < 0 Then
            C1TextBox19.ForeColor = Color.Red
        Else
            C1TextBox19.ForeColor = Color.Black
        End If

    End Sub

    Private Function P_Yz(ByVal Bl_Code As String) As String


        Dim My_Reader As SqlDataReader


        My_Reader = HisVar.HisVar.Sqldal.ExecuteReader("Select *,Ry_RyDate,Isnull((Select Sum(Jf_Money) From Bl_Jf Where Bl_Code=Bl.Bl_Code),0) Jf_Money From Bl Where Bl_Code='" & Bl_Code & "'")

        If My_Reader.HasRows = True Then
            P_Yz = "正确"
        Else
            P_Yz = "不存在"
        End If

    End Function

#End Region

#Region "控件__动作"

#Region "其它__控件"
    Private Sub Comm_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm3.Click
        Select Case sender.tag
            Case "申请出院"


                If C1Combo3.SelectedValue = "" Then
                    Beep()
                    MsgBox("出院病人不能为空!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    C1Combo3.Select()
                    Exit Sub
                End If

                If Format(DateTimePicker1.Value, "yyyy-MM-dd") > Format(Now, "yyyy-MM-dd") Then
                    MsgBox("出院日期不能大于当前日期", MsgBoxStyle.Information, "提示")
                    Exit Sub
                End If

                If HisVar.HisVar.Sqldal.GetSingle("Select Bc_Code from Bl where Bl_Code='" & C1Combo3.Columns("Bl_Code").Value & "'") & "" = "" Then
                    MsgBox("该患者未正常配床，不能申请出院！", MsgBoxStyle.Information, "提示")
                    Exit Sub
                End If

                If HisVar.HisVar.Sqldal.GetSingle("select Count(*) from Bl_Cf where Cf_Date+Cf_Time>'" & Format(DateTimePicker1.Value, "yyyy-MM-dd HH:mm:ss") & "' and Bl_Code='" & C1Combo3.Columns("Bl_Code").Value & "'") > 0 Then
                    MsgBox("该患者存在晚于当前时间的处方，不能办理出院！", MsgBoxStyle.Information, "提示")
                    Exit Sub
                End If

                If ZTHisPara.PublicConfig.CyXmMustComplete = True Then
                    '验证诊疗项目是否 有 未完成
                    If HisVar.HisVar.Sqldal.Exists("SELECT COUNT(1) FROM dbo.Bl_Cfxm WHERE EXISTS(SELECT 1 FROM dbo.Bl_Cf WHERE dbo.Bl_Cf.Cf_Code = Bl_Cfxm.Cf_Code AND Bl_Code = '" & C1Combo3.Columns("Bl_Code").Value & "') AND Xm_Wc = '否'") Then
                        MsgBox("有未完成诊疗项目，不能申请出院！", MsgBoxStyle.Information, "提示")
                        Exit Sub
                    End If

                End If
                If ZTHisPara.PublicConfig.CheckBedFee = True Then
                    If InpatFunc.ChekBedFee(Label11.Text, C1Combo3.Columns("Bl_Code").Value) = False Then
                        Exit Sub
                    End If
                End If
                Dim m_Str As String = ""

                HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "select distinct Bl_Cfxm.Xm_Code,Xm_Name From Bl_Cfxm,Bl_Cf,Zd_Ml_Xm3 where Bl_Cfxm.Xm_Code=Zd_Ml_Xm3.Xm_Code And Bl_Cfxm.Cf_Code=Bl_Cf.Cf_Code And not exists (Select Mx_Code From zd_Jkfl2 where zd_Jkfl2.Mx_Code=Bl_Cfxm.Xm_Code) and Bl_Code='" & C1Combo3.Columns("Bl_Code").Value & "'", "未维护项目", True)

                For Each m_row In My_Dataset.Tables("未维护项目").Rows
                    m_Str = m_Str & vbCrLf & m_row("Xm_Name")
                Next

                If m_Str <> "" Then
                    m_Str = m_Str & vbCrLf
                    MsgBox("诊疗项目:" & m_Str & "未维护到住院发票类别,请先维护才能办理出院!", MsgBoxStyle.Critical, "提示")
                    Exit Sub
                End If

                HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "Select Cf_Date  from Bl_Cf where Bl_Code='" & C1Combo3.Columns("Bl_Code").Value & "' and (Cf_Qr='否'  or Cf_Print='否')", "未完成处方", True)
                For Each m_row In My_Dataset.Tables("未完成处方").Rows
                    m_Str = m_Str & vbCrLf & Format(m_row("Cf_Date"), "yyyy-MM-dd")
                Next

                If m_Str <> "" Then
                    m_Str = m_Str & vbCrLf
                    MsgBox("存在未完成处方:" & m_Str, MsgBoxStyle.Critical, "提示")
                    Exit Sub
                End If



                Dim V_Jg As String
                V_Jg = P_Yz(C1Combo3.Columns("Bl_Code").Value)

                V_Bl_Code = C1Combo3.Columns("Bl_Code").Value

                If V_Jg = "正确" Then
                    If MsgBox("是否确认此患者出院？", vbQuestion + vbYesNo, "提示:") = vbNo Then Exit Sub
                    HisVar.HisVar.Sqldal.ExecuteSql("Update Bl Set Cy_Qr='是',Ry_CyDate='" & Format(DateTimePicker1.Value, "yyyy-MM-dd HH:mm:ss") & "',Ry_ZyTs='" & Label11.Text & "' where Bl.Bl_Code='" & V_Bl_Code & "'")
                    MsgBox("申请出院成功", MsgBoxStyle.Information + MsgBoxStyle.OkOnly, "提示")
                    If modelPatientInfo IsNot Nothing Then
                        modelPatientInfo.Cy_Qr = "是"
                        modelPatientInfo.ShenqingCy = MyResources.C_Resources.getimage("申请出院")
                        nurseDel("申请出院")
                    End If
                    Me.Close()
                Else
                    If V_Jg = "未请求" Then
                        MsgBox("病人存在尚未请求发药处方,不能结算!", vbCritical + vbOKOnly, "提示:")
                        Exit Sub
                    End If

                    If V_Jg = "未发药" Then
                        MsgBox("病人存在药房未发出的药品,不能结算!", vbCritical + vbOKOnly, "提示:")
                        Exit Sub
                    End If

                    If V_Jg = "不存在" Then
                        MsgBox("病人不存在，或已结算出院!", vbCritical + vbOKOnly, "提示:")
                        Exit Sub
                    End If
                End If

            Case "取消"
                Me.Close()
        End Select

    End Sub
#End Region

#Region "Combo3动作"

    Private Sub C1Combo3_RowChange(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1Combo3.RowChange
        If Me.Visible = False Then
            Label1.Text = ""
            Label3.Text = ""
            Label10.Text = "0"
            Label11.Text = ""
            Exit Sub
        End If

        If C1Combo3.WillChangeToValue = "" Then
            Label1.Text = ""
            Label3.Text = ""
            Label10.Text = "0"
            Label11.Text = ""
        Else
            Label10.Text = Trim(C1Combo3.Columns("Jf_Money").Value)
            Label1.Text = Trim(C1Combo3.Columns("Ry_Sex").Value)
            Label3.Text = Trim(C1Combo3.Columns("Ry_Sfzh").Value)

            Label11.Text = ZTHisPublicFunction.InpatFunc.CalculateHospitalDays(Convert.ToDateTime(C1Combo3.Columns("Ry_RyDate").Value), Convert.ToDateTime(DateTimePicker1.Value))

            P_Data_Show(C1Combo3.Columns("Bl_Code").Value)
        End If
    End Sub

    Private Sub C1Combo1_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles C1Combo3.KeyUp

        If (e.KeyValue > 47 And e.KeyValue < 58) Or (e.KeyValue > 96 And e.KeyValue < 123) Or (e.KeyValue > 64 And e.KeyValue < 91) Or (e.KeyValue = 8) Or (e.KeyValue = 189) Or (e.KeyValue = 190) Then
            Dim s As String = C1Combo3.Text
            If C1Combo3.Text = "" Then
                C1Combo3.DataSource.RowFilter = "1=1"
            Else
                C1Combo3.DataSource.RowFilter = "Ry_Jc like '*" & C1Combo3.Text.Replace("*", "[*]") & "*'"
            End If


            If (e.KeyValue = 8) Then
                C1Combo3.DroppedDown = False
                C1Combo3.DroppedDown = True
            End If

            C1Combo3.Text = s
            C1Combo3.SelectionStart = C1Combo3.Text.Length
            C1Combo3.SelectionLength = 0
        End If

    End Sub

    Private Sub C1Combo3_KeyDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles C1Combo3.KeyDown
        If e.KeyValue = 13 Then
            If C1Combo3.WillChangeToIndex < 1 Then
                If (CType(C1Combo3.DataSource, DataView).Count) = 0 Then
                    MsgBox("输入有误！", MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
                    System.Windows.Forms.SendKeys.Send("^{A}")
                    Exit Sub
                End If
                C1Combo3.SelectedIndex = -1
                C1Combo3.SelectedIndex = 0
            Else
                Dim index As Integer
                index = C1Combo3.WillChangeToIndex
                C1Combo3.SelectedIndex = -1
                C1Combo3.SelectedIndex = index
            End If
            e.Handled = True
            System.Windows.Forms.SendKeys.Send("{Tab}")
        End If
    End Sub

#End Region

#End Region

#Region "自定义函数"
    Private Sub P_SumMoney()
        If My_Dataset.Tables("明细表").Rows.Count = 0 Then
            C1TrueDBGrid1.Columns("Cf_Money").FooterText = "0元"
            V_Sum = 0
        Else
            V_Sum = 0
            For Each Me.Cb_Row In My_Dataset.Tables("明细表").Rows
                If Cb_Row.RowState <> DataRowState.Deleted Then
                    If Cb_Row.Item("Cf_Money") IsNot DBNull.Value Then
                        V_Sum = V_Sum + Cb_Row.Item("Cf_Money")
                    End If
                End If
            Next
            T_Label5.Text = My_Dataset.Tables("明细表").Rows.Count
        End If
        T_Label5.Location = New Point(Me.Width - T_Label5.Width - 7, 8)
        T_Label4.Location = New Point(Me.Width - T_Label5.Width - T_Label4.Width - 7, 8)
    End Sub
#End Region

#Region "鼠标__外观"
    Private Sub P_Comm(ByVal Bt As Button)
        With Bt
            Select Case .Tag
                Case "申请出院"

                    .BackgroundImage = MyResources.C_Resources.getimage("命令_申请出院1")
                    .Text = "                     &K"
            End Select
            .FlatStyle = FlatStyle.Flat
            .FlatAppearance.BorderSize = 0
            .BackgroundImageLayout = ImageLayout.Center
        End With
    End Sub

    Private Sub Comm_MouseEnter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm3.MouseEnter
        Select Case sender.tag
            Case "申请出院"
                Comm3.BackgroundImage = MyResources.C_Resources.getimage("命令_申请出院2")
                Comm3.Cursor = Cursors.Hand
        End Select

    End Sub

    Private Sub Comm_MouseLeave(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm3.MouseLeave
        Select Case sender.tag
            Case "申请出院"
                Comm3.BackgroundImage = MyResources.C_Resources.getimage("命令_申请出院1")
                Comm3.Cursor = Cursors.Default
        End Select
    End Sub

    Private Sub Comm_MouseDown(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm3.MouseDown
        Select Case sender.tag
            Case "申请出院"
                Comm3.BackgroundImage = MyResources.C_Resources.getimage("命令_申请出院3")
                Comm3.Cursor = Cursors.Default
        End Select
    End Sub

    Private Sub Comm_MouseUp(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm3.MouseUp
        Select Case sender.tag
            Case "申请出院"
                Comm3.BackgroundImage = MyResources.C_Resources.getimage("命令_申请出院1")
                Comm3.Cursor = Cursors.Hand
        End Select
    End Sub
#End Region

    '英文
    Private Sub C1Combo1_Open(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Combo3.GotFocus
        InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages(0)
    End Sub


    Private Sub DateTimePicker1_ValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles DateTimePicker1.ValueChanged
        If C1Combo3.SelectedValue = "" Then Exit Sub
        Label11.Text = ZTHisPublicFunction.InpatFunc.CalculateHospitalDays(Convert.ToDateTime(C1Combo3.Columns("Ry_RyDate").Value), Convert.ToDateTime(DateTimePicker1.Value))
    End Sub
End Class