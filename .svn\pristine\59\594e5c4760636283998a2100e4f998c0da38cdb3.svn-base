﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{D79BB10E-64C3-4865-BEE0-2D72454F47DF}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>ZTHisInpatient</RootNamespace>
    <AssemblyName>ZTHisInpatient</AssemblyName>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\output\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>x86</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="C1.Win.4, Version=4.0.20192.375, Culture=neutral, PublicKeyToken=944ae1ea0e47ca04, processorArchitecture=MSIL" />
    <Reference Include="C1.Win.C1Command.4, Version=4.0.20192.375, Culture=neutral, PublicKeyToken=e808566f358766d8, processorArchitecture=MSIL" />
    <Reference Include="C1.Win.C1FlexGrid.4, Version=4.0.20192.375, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL" />
    <Reference Include="C1.Win.C1Input.4, Version=4.0.20192.375, Culture=neutral, PublicKeyToken=7e7ff60f0c214f9a, processorArchitecture=MSIL" />
    <Reference Include="C1.Win.C1List.4, Version=4.0.20192.375, Culture=neutral, PublicKeyToken=6b24f8f981dbd7bc, processorArchitecture=MSIL" />
    <Reference Include="C1.Win.C1Ribbon.4, Version=4.0.20192.375, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL" />
    <Reference Include="C1.Win.C1TrueDBGrid.4, Version=4.0.20192.375, Culture=neutral, PublicKeyToken=75ae3fb0e2b1e0da, processorArchitecture=MSIL" />
    <Reference Include="C1.Win.Input.MultiSelect.4, Version=4.0.20192.375, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL" />
    <Reference Include="C1.Win.TreeView.4, Version=4.0.20192.375, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL" />
    <Reference Include="Microsoft.VisualBasic" />
    <Reference Include="Stimulsoft.Report, Version=2011.2.1026.0, Culture=neutral, PublicKeyToken=ebe6666cba19647a, processorArchitecture=MSIL" />
    <Reference Include="Stimulsoft.Report.Win, Version=2011.2.1026.0, Culture=neutral, PublicKeyToken=ebe6666cba19647a, processorArchitecture=MSIL" />
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Design" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="ZTHisInpatient\病案首页\Bl_Firstoper.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ZTHisInpatient\病案首页\Bl_Firstoper.Designer.cs">
      <DependentUpon>Bl_Firstoper.cs</DependentUpon>
    </Compile>
    <Compile Include="ZTHisInpatient\病案首页\Bl_FirstOutDiag.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ZTHisInpatient\病案首页\Bl_FirstOutDiag.Designer.cs">
      <DependentUpon>Bl_FirstOutDiag.cs</DependentUpon>
    </Compile>
    <Compile Include="ZTHisInpatient\病案首页\XyBl_Basy.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ZTHisInpatient\病案首页\XyBl_Basy.Designer.cs">
      <DependentUpon>XyBl_Basy.cs</DependentUpon>
    </Compile>
    <Compile Include="ZTHisInpatient\病案首页\ZyBl_Basy.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ZTHisInpatient\病案首页\ZyBl_Basy.Designer.cs">
      <DependentUpon>ZyBl_Basy.cs</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Properties\licenses.licx" />
    <EmbeddedResource Include="ZTHisInpatient\病案首页\Bl_Firstoper.resx">
      <DependentUpon>Bl_Firstoper.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ZTHisInpatient\病案首页\Bl_FirstOutDiag.resx">
      <DependentUpon>Bl_FirstOutDiag.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ZTHisInpatient\病案首页\XyBl_Basy.resx">
      <DependentUpon>XyBl_Basy.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ZTHisInpatient\病案首页\ZyBl_Basy.resx">
      <DependentUpon>ZyBl_Basy.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\BLL\BLL.csproj">
      <Project>{46b795c2-6efa-41e6-948e-66f92e591b6a}</Project>
      <Name>BLL</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common.BaseForm\Common.BaseForm.csproj">
      <Project>{1dd7020c-8603-438a-8015-34702dabc229}</Project>
      <Name>Common.BaseForm</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common.Enum\Common.Enum.csproj">
      <Project>{eca72bf5-a6c2-4ecb-a80a-9723ef1098a1}</Project>
      <Name>Common.Enum</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common.WinFormVar\Common.WinFormVar.csproj">
      <Project>{e267bdd2-634a-405b-bdbf-55354adbc027}</Project>
      <Name>Common.WinFormVar</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common\Common.csproj">
      <Project>{92e350a0-3691-4b8d-a07e-ebb0f10e6997}</Project>
      <Name>Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\CustomControl\CustomControl.csproj">
      <Project>{12BF4168-D60E-4A6C-85BF-926130EE6A6D}</Project>
      <Name>CustomControl</Name>
    </ProjectReference>
    <ProjectReference Include="..\HisVar\HisVar.vbproj">
      <Project>{4dad5e14-6224-46b2-886d-6d22ce3886bc}</Project>
      <Name>HisVar</Name>
    </ProjectReference>
    <ProjectReference Include="..\Model\Model.csproj">
      <Project>{3fb6ea13-2c32-4d08-a426-c22224f72121}</Project>
      <Name>Model</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisVar\ZTHisVar.csproj">
      <Project>{26bf0cc0-ae2a-459a-b41b-73f691f5299d}</Project>
      <Name>ZTHisVar</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>