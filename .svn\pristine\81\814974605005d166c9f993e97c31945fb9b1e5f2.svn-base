﻿Imports System.Drawing
Imports System.Windows.Forms
Imports ModelOld
Imports Stimulsoft.Report

Public Class MaterialsOtherIn

#Region "定义__变量"
    Dim My_Table As New DataTable                       '从表 普通录入表
    Dim V_TotalMoney As Double
    Dim Cb_Cm As CurrencyManager                     '同步指针
    Dim Cb_Row As DataRow                            '当前选择行
    Dim <PERSON>ert As Boolean
    Dim M_Materials_Other_In1 As New ModelOld.M_Materials_Other_In1
    Dim B_Materials_Other_In1 As New BLLOld.B_Materials_Other_In1
    Dim B_Materials_Other_In2 As New BLLOld.B_Materials_Other_In2
    Dim B_Materials_Stock As New BLLOld.B_Materials_Stock
    Dim m_Rc As New BaseClass.C_RowChange
#End Region

    Public Sub New(ByVal model As ModelOld.M_Materials_Other_In1)
        ' 此调用是设计器所必需的。
        InitializeComponent()
        M_Materials_Other_In1 = model
        ' 在 InitializeComponent() 调用之后添加任何初始化。
    End Sub

    Private Sub MaterialsOtherIn_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Call Form_Init()                '窗体初始化
        If M_Materials_Other_In1 Is Nothing Then
            Call Zb_Clear()
        Else
            Call Zb_Show()
        End If
        Call StatisticsDataShow()
    End Sub

#Region "窗体__事件"
    '公共窗体
    Private Sub Form_Init()
        Panel2.Height = 35
        Comm_Del.Location = New Point(100, 1)
        Comm_DelAll.Location = New Point(Comm_Del.Left + Comm_Del.Width + 2, 1)
        Comm_New.Location = New Point(Comm_DelAll.Left + Comm_DelAll.Width + 2, 1)
        Comm_Save.Location = New Point(Comm_New.Left + Comm_New.Width + 2, 1)
        Comm_Complete.Location = New Point(Comm_Save.Left + Comm_Save.Width + 2, 1)
        Comm_Print.Location = New Point(Comm_Complete.Left + Comm_Complete.Width + 2, 1)
        Comm_Search.Location = New Point(Comm_Print.Left + Comm_Print.Width + 2, 1)
        Comm_WriteOffAll.Location = New Point(Comm_Search.Left + Comm_Search.Width + 2, 1)
        Comm_WriteOffPart.Location = New Point(Comm_WriteOffAll.Left + Comm_WriteOffAll.Width + 2, 1)
        Comm_Close.Location = New Point(Comm_WriteOffPart.Left + Comm_WriteOffPart.Width + 2, 1)

        Jsr_Text.Enabled = False
        Code_Text.Enabled = False

        '库房
        Dim B_Materials_Warehouse_Dict As New BLLOld.B_Materials_Warehouse_Dict
        With WareHouse_DtCom
            .DataView = B_Materials_Warehouse_Dict.GetList("IsUse='1' ").Tables(0).DefaultView
            .Init_Colum("MaterialsWh_Code", "库房编码", 0, "左")
            .Init_Colum("MaterialsWh_Name", "库房名称", 120, "左")
            .Init_Colum("MaterialsWh_Py", "库房简称", 60, "左")
            .DisplayMember = "MaterialsWh_Name"
            .ValueMember = "MaterialsWh_Code"
            .DroupDownWidth = .Width
            .MaxDropDownItems = 15
            .SelectedIndex = -1
            .RowFilterTextNull = ""
            .RowFilterNotTextNull = "MaterialsWh_Py"
        End With

        '出入库类别
        Dim B_Materials_InOut_Class_Dict As New BLLOld.B_Materials_InOut_Class_Dict
        With InOutClass_DtCom
            .DataView = B_Materials_InOut_Class_Dict.GetList(" isuse=1 and InOutType = '入库'").Tables(0).DefaultView
            .Init_Colum("MaterialsInOut_Code", "类别编码", 0, "左")
            .Init_Colum("MaterialsInOut_Name", "类别名称", 120, "左")
            .Init_Colum("MaterialsInOut_Py", "类别简称", 60, "左")
            .DisplayMember = "MaterialsInOut_Name"
            .ValueMember = "MaterialsInOut_Code"
            .DroupDownWidth = .Width
            .MaxDropDownItems = 15
            .SelectedIndex = -1
            .RowFilterTextNull = ""
            .RowFilterNotTextNull = "MaterialsInOut_Py"
        End With

        '入库日期
        With Form_Date
            .CustomFormat = "yyyy-MM-dd HH:mm"
            .EditFormat = "yyyy-MM-dd HH:mm"
            .DisplayFormat = "yyyy-MM-dd HH:mm"
            .Value = Format(Now, "yyyy-MM-dd HH:mm")
        End With
        Jsr_Text.Text = HisVar.HisVar.JsrName
    End Sub

    Private Sub BtnState()
        MyGrid_Init()
        If M_Materials_Other_In1.OrdersStatus Is Nothing OrElse M_Materials_Other_In1.OrdersStatus = "录入" Then
            Comm_Del.Enabled = True
            Comm_DelAll.Enabled = True
            Comm_New.Enabled = True
            Comm_Save.Enabled = True
            Comm_Complete.Enabled = True
            Comm_Print.Enabled = False
            Comm_Search.Enabled = True
            Comm_Close.Enabled = True
            Comm_WriteOffAll.Enabled = False
            Comm_WriteOffPart.Enabled = False

            MyGrid1.Tag = True
            If M_Materials_Other_In1.WriteOffStatus = "冲销" Then
                WareHouse_DtCom.Enabled = False
                InOutClass_DtCom.Enabled = False
                Form_Date.Enabled = False
            Else
                WareHouse_DtCom.Enabled = True
                InOutClass_DtCom.Enabled = True
                Form_Date.Enabled = True
            End If
            Memo_Text.Enabled = True
            Exit Sub
        End If

        '非本人的单据不可以修改
        If M_Materials_Other_In1.Jsr_Code <> HisVar.HisVar.JsrCode Then
            Comm_Del.Enabled = False
            Comm_DelAll.Enabled = False
            Comm_New.Enabled = True
            Comm_Save.Enabled = False
            Comm_Complete.Enabled = False
            Comm_Print.Enabled = False
            Comm_Search.Enabled = True
            Comm_Close.Enabled = True
            Comm_WriteOffAll.Enabled = False
            Comm_WriteOffPart.Enabled = False

            MyGrid1.Tag = False
            WareHouse_DtCom.Enabled = False
            InOutClass_DtCom.Enabled = False
            Form_Date.Enabled = False
            Memo_Text.Enabled = False
            Exit Sub
        End If

        '完成的只有冲销可以使用，被冲销的一键冲销不能使用
        If M_Materials_Other_In1.OrdersStatus = "完成" Then
            Comm_Del.Enabled = False
            Comm_DelAll.Enabled = False
            Comm_New.Enabled = True
            Comm_Save.Enabled = False
            Comm_Complete.Enabled = False
            Comm_Print.Enabled = True
            Comm_Search.Enabled = True
            Comm_Close.Enabled = True

            MyGrid1.Tag = False
            WareHouse_DtCom.Enabled = False
            InOutClass_DtCom.Enabled = False
            Form_Date.Enabled = False
            Memo_Text.Enabled = False

            Select Case M_Materials_Other_In1.WriteOffStatus & ""
                Case "冲销"      '冲销单不能被冲销
                    Comm_WriteOffAll.Enabled = False
                    Comm_WriteOffPart.Enabled = False
                Case "被冲销"    '被冲销单不能一键冲销
                    Comm_WriteOffAll.Enabled = False
                    Comm_WriteOffPart.Enabled = True
                Case "全部被冲销" '全部被冲销单不能被冲销
                    Comm_WriteOffAll.Enabled = False
                    Comm_WriteOffPart.Enabled = False
                Case ""
                    Comm_WriteOffAll.Enabled = True
                    Comm_WriteOffPart.Enabled = True
            End Select
            Exit Sub
        End If
    End Sub

    Private Sub MyGrid_Init()
        Dim ColEdit As Boolean
        If M_Materials_Other_In1.OrdersStatus = "完成" Then
            ColEdit = False
        Else
            ColEdit = True
        End If

        With MyGrid1
            .Init_Grid()
            .Init_Column("入库编码", "M_OtherIn_Code", "0", "左", "", False)
            .Init_Column("入库明细编码", "M_OtherIn_Detail_Code", "0", "左", "", False)
            .Init_Column("物资库存编码", "MaterialsStock_Code", "0", "左", "", False)
            .Init_Column("物资编码", "Materials_Code", "0", "左", "", False)

            .Init_Column("物资名称", "Materials_Name", "100", "左", "", False)
            .Init_Column("物资批号", "MaterialsLot", "120", "左", "", False)
            .Init_Column("物资有效期", "MaterialsExpiryDate", "100", "中", "yyyy-MM-dd", False)
            If M_Materials_Other_In1.WriteOffStatus = "冲销" Then
                .Init_Column("可冲数量", "Can_RealWriteOffNo", "100", "右", "####0.####", False)
                .Init_Column("数量", "M_OtherIn_Num", "80", "右", "####0.####", ColEdit)
                .Init_Column("库存数量", "MaterialsStore_Num", "100", "右", "####0.####", False)
                .Init_Column("单价", "M_OtherIn_Price", "90 ", "左", "####0.00##", False)
                .Init_Column("冲销金额", "M_OtherIn_Money", "100", "右", "##,##0.00##", False)
            Else
                If M_Materials_Other_In1.OrdersStatus = "完成" Then
                    .Init_Column("数量", "M_OtherIn_Num", "80", "右", "####0.####", ColEdit)
                    .Init_Column("冲销数量", "M_OtherIn_WriteoffNo", "80", "右", "####0.####", False)
                    .Init_Column("实际数量", "M_OtherIn_RealNo", "100", "右", "####0.####", False)
                    .Init_Column("库存数量", "MaterialsStore_Num", "100", "右", "####0.####", False)
                    .Init_Column("单价", "M_OtherIn_Price", "70", "右", "####0.00##", False)
                    .Init_Column("入库金额", "M_OtherIn_Money", "100", "右", "##,##0.00##", False)
                    .Init_Column("实际金额", "M_OtherIn_RealMoney", "120", "右", "##,##0.00##", False)
                Else
                    .Init_Column("数量", "M_OtherIn_Num", "80", "右", "####0.####", ColEdit)
                    .Init_Column("单价", "M_OtherIn_Price", "70", "右", "####0.00##", False)
                    .Init_Column("入库金额", "M_OtherIn_Money", "100", "右", "##,##0.00##", False)
                End If
            End If
            .Init_Column("备注", "M_OtherInDetail_Memo", "55", "左", "", ColEdit) '0
            .MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightCell
            If M_Materials_Other_In1.WriteOffStatus = "冲销" Then
                .AllowAddNew = False '冲销单不允许增加新行
            Else
                .AllowAddNew = ColEdit
            End If
            .ColumnFooters = True
        End With
    End Sub

    Private Sub MaterialsOtherIn_FormClosing(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosingEventArgs) Handles Me.FormClosing
        If e.CloseReason = CloseReason.UserClosing Then
            Try
                MyGrid1.UpdateData()
                If My_Table.DataSet.HasChanges = True Then '只有录入状态的单据才能保存
                    If B_Materials_Other_In1.GetRecordCount("M_OtherIn_Code='" & Code_Text.Text.Trim & "' and OrdersStatus='录入'") = 1 Then
                        If MessageBox.Show("数据尚未保存,是否保存数据?", "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1) = Windows.Forms.DialogResult.OK Then
                            Call Data_Save("保存")
                        End If
                    End If
                End If
            Catch ex As Exception
                MsgBox("当前编辑行数据因未填写完整，将不能保存！", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示：")
                MyGrid1.Delete()
            End Try
        End If
    End Sub
#End Region

#Region "清空__显示"
    '窗体_清空数据
    Private Sub Zb_Clear()
        Rinsert = True
        M_Materials_Other_In1 = New ModelOld.M_Materials_Other_In1
        M_Materials_Other_In1.M_OtherIn_Code = B_Materials_Other_In1.MaxCode
        Code_Text.Text = M_Materials_Other_In1.M_OtherIn_Code
        Jsr_Text.Text = HisVar.HisVar.JsrName
        WareHouse_DtCom.SelectedIndex = -1
        InOutClass_DtCom.SelectedIndex = -1
        Form_Date.Value = Format(Now, "yyyy-MM-dd HH:mm")
        Memo_Text.Text = ""
        V_TotalMoney = 0
        BtnState()
        P_Data_Show()
        WareHouse_DtCom.Select()
    End Sub

    '窗体_展示数据
    Private Sub Zb_Show()
        With M_Materials_Other_In1
            Code_Text.Text = .M_OtherIn_Code
            Jsr_Text.Text = .Jsr_Name
            WareHouse_DtCom.SelectedValue = .MaterialsWh_Code
            InOutClass_DtCom.SelectedValue = .MaterialsInOut_Code
            Form_Date.Value = .OtherIn_Date
            Memo_Text.Text = .M_OtherIn_Memo
        End With
        Rinsert = False
        Call BtnState()
        Call P_Data_Show()
    End Sub
    '显示 从表数据
    Private Sub P_Data_Show()
        If M_Materials_Other_In1.WriteOffStatus = "冲销" Then
            My_Table = B_Materials_Other_In2.GetWriteOffList(" Materials_Other_In2.M_OtherIn_Code ='" & M_Materials_Other_In1.M_OtherIn_Code & "'").Tables(0)
        Else
            My_Table = B_Materials_Other_In2.GetList(" Materials_Other_In2.M_OtherIn_Code = '" & M_Materials_Other_In1.M_OtherIn_Code & "'").Tables(0)
        End If
        My_Table.Columns("M_OtherIn_Code").AllowDBNull = True
        My_Table.Columns("M_OtherIn_Detail_Code").AllowDBNull = True
        My_Table.Columns("MaterialsStock_Code").AllowDBNull = True
        My_Table.Columns("M_OtherIn_WriteoffNo").DefaultValue = 0
        My_Table.Constraints.Clear()
        MyGrid1.DataTable = My_Table
        Cb_Cm = CType(BindingContext(My_Table.DataSet, My_Table.TableName), CurrencyManager)
        Call F_Sum()
        MyGrid1.Focus()
    End Sub

    '表单 统计
    Private Sub StatisticsDataShow()
        Dim str As String = "Finish_Date BETWEEN '" & Format(Now, "yyyy-MM-dd 00:00:00") & "' AND '" & Format(Now, "yyyy-MM-dd 23:59:59") & "'"
        TodayFormsNo_Lbl.Text = "今日入库单数量：" & B_Materials_Other_In1.GetRecordCount(str)
        TodayMoney_Lbl.Text = "今日入库总金额：" & B_Materials_Other_In1.GetSumMoney(str)
        str += "AND Jsr_Code='" & HisVar.HisVar.JsrCode & "'"
        BrTodayFormsNo_Lbl.Text = "本人入库单数量：" & B_Materials_Other_In1.GetRecordCount(str)
        BrTodayMoney_Lbl.Text = "本人入库总金额：" & B_Materials_Other_In1.GetSumMoney(str)
    End Sub


#End Region


#Region "控件__动作"

#Region "按钮"
    Private Sub Comm_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm_Del.Click, Comm_DelAll.Click, Comm_Save.Click,
      Comm_Complete.Click, Comm_New.Click, Comm_Print.Click, Comm_Close.Click, Comm_Search.Click, Comm_WriteOffPart.Click, Comm_WriteOffAll.Click
        Select Case sender.tag
            Case "删除行"


                If MyGrid1.Row >= MyGrid1.RowCount Then
                    MsgBox("未选中行！", MsgBoxStyle.Exclamation, "提示")
                    Exit Sub
                End If


                If M_Materials_Other_In1.Input_Date IsNot Nothing AndAlso B_Materials_Other_In1.GetRecordCount(" M_OtherIn_Code='" & Code_Text.Text.Trim & "' and OrdersStatus ='录入'") = 0 Then
                    MsgBox("已完成单据不能删除！", MsgBoxStyle.Exclamation, "提示")
                    Exit Sub
                End If
                Data_Delete()
            Case "删除单"
                If M_Materials_Other_In1.OtherIn_Date Is Nothing Then
                    MsgBox("数据尚未保存,无法删除!", MsgBoxStyle.Exclamation, "提示")
                    Exit Sub
                End If
                If B_Materials_Other_In1.GetRecordCount(" M_OtherIn_Code='" & Code_Text.Text.Trim & "' and OrdersStatus ='录入'") = 0 Then
                    MsgBox("已完成单据不能删除！", MsgBoxStyle.Exclamation, "提示")
                    Exit Sub
                End If
                Call Data_DeleteAll()
            Case "新单"
                Data_New()
            Case "保存", "完成"
                Call Data_Save(sender.tag)
            Case "打印"
                If M_Materials_Other_In1.Finish_Date Is Nothing Then
                    MsgBox("数据尚未保存,无法打印!", MsgBoxStyle.Exclamation, "提示")
                    Exit Sub
                End If
                Call Data_Print()
            Case "速查"
                Dim t_Rc As New BaseClass.C_RowChange
                AddHandler t_Rc.ModelChangeEvent, AddressOf ChangeModel
                Dim f As New MaterialsOtherInQuery(t_Rc)
                f.Owner = Me
                f.ShowDialog()
            Case "退出"
                Me.Close()
            Case "冲销", "一键冲销"
                If B_Materials_Other_In1.GetRecordCount("M_OtherIn_Code='" & Code_Text.Text & "' and OrdersStatus='完成'") = 0 Then
                    MsgBox("未完成单据不能冲销", MsgBoxStyle.Exclamation, "提示")
                    Exit Sub
                End If
                If B_Materials_Other_In2.GetRecordCount("M_OtherIn_Code='" & Code_Text.Text & "' and M_OtherIn_RealNo>0") = 0 Then
                    MsgBox("该单据已全部被冲销，不能冲销", MsgBoxStyle.Exclamation, "提示")
                    Exit Sub
                End If

                If MsgBox("是否" + sender.tag + " 入库类型：" & InOutClass_DtCom.Text & "，入库金额：" & M_Materials_Other_In1.TotalMoney & "，单号：" & Code_Text.Text & "的其他入库单？", MsgBoxStyle.Question + MsgBoxStyle.YesNo + MsgBoxStyle.DefaultButton1, "提示") = DialogResult.Yes Then
                    Dim model As New ModelOld.M_Materials_Other_In1
                    With model
                        .M_OtherIn_Code = B_Materials_Other_In1.MaxCode
                        .MaterialsInOut_Code = M_Materials_Other_In1.MaterialsInOut_Code
                        .MaterialsWh_Code = M_Materials_Other_In1.MaterialsWh_Code

                        .OtherIn_Date = M_Materials_Other_In1.OtherIn_Date
                        .Input_Date = Format(Now, "yyyy-MM-dd HH:mm")
                        .Finish_Date = Nothing
                        .Jsr_Code = HisVar.HisVar.JsrCode
                        .TotalMoney = 0
                        .M_OtherIn_Memo = ""
                        .OrdersStatus = "录入"
                        .WriteOff_Code = M_Materials_Other_In1.M_OtherIn_Code
                        .WriteOffStatus = "冲销"
                    End With
                    If B_Materials_Other_In1.AddWriteOff(model) = True Then
                        If sender.Text = "一键冲销" Then
                            model.OrdersStatus = "完成"
                            model.Finish_Date = Format(Now, "yyyy-MM-dd HH:mm")
                            If Not B_Materials_Other_In1.Complete(model) Then
                                model.OrdersStatus = "录入"
                                model.Finish_Date = Nothing
                                MsgBox("完成操作失败!", MsgBoxStyle.Exclamation, "提示")
                                Exit Sub
                            End If
                        End If
                        Dim V_Form As New Materials.MaterialsOtherIn(model)
                        V_Form.Name = V_Form.Name & model.M_OtherIn_Code
                        BaseFunc.BaseFunc.addTabControl(V_Form, "入库冲销-" & Code_Text.Text)
                    End If
                End If
        End Select

    End Sub

    Private Sub Memo_Text_Validated(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Memo_Text.Validated
        MyGrid1.Focus()
    End Sub
#End Region

#Region "DBGrid动作"

    'Private Sub MyGrid1_RowColChange(ByVal sender As System.Object, ByVal e As C1.Win.C1TrueDBGrid.RowColChangeEventArgs) Handles MyGrid1.RowColChange
    '    If (MyGrid1.Row + 1) > MyGrid1.RowCount Then
    '    Else
    '        Cb_Row = Cb_Cm.List(MyGrid1.Row).Row
    '        m_Rc.ChangeRow(Cb_Row)
    '    End If
    'End Sub

    Private Sub MyGrid1_AfterColUpdate(ByVal sender As System.Object, ByVal e As C1.Win.C1TrueDBGrid.ColEventArgs) Handles MyGrid1.AfterColUpdate
        Dim row As DataRow                    '当 前 行
        row = Cb_Cm.List(MyGrid1.Row).Row
        Select Case MyGrid1.Splits(0).DisplayColumns(e.ColIndex).Name
            Case "入库数量", "单价"
                If row("M_OtherIn_Price") IsNot DBNull.Value Then
                    row("M_OtherIn_Money") = row("M_OtherIn_Num") * row("M_OtherIn_Price")
                End If
        End Select
    End Sub

    Private Sub MyGrid1_AfterUpdate(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyGrid1.AfterUpdate
        Call F_Sum()
    End Sub

    Private Sub MyGrid1_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles MyGrid1.KeyDown
        Select Case e.KeyCode
            Case Keys.Escape, Keys.F2, Keys.F3, Keys.F4, Keys.F5, Keys.F6, Keys.Up, Keys.Down, Keys.Left, Keys.Right
            Case Keys.Delete
                Data_Delete()
            Case Keys.Enter
                If Zb_Check() = False Then Exit Sub
                If MyGrid1.Columns(MyGrid1.Col).DataField <> "M_OtherIn_Num" And MyGrid1.Columns(MyGrid1.Col).DataField <> "M_OtherIn_Price" Then
                    Cb_Edit()
                End If
        End Select
    End Sub

    Private Sub MyGrid1_MouseDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles MyGrid1.MouseDown
        If e.Button = Windows.Forms.MouseButtons.Right Then
            If Zb_Check() = False Then Exit Sub
            Cb_Edit()
        End If
    End Sub

    Private Sub MyGrid1_GotFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles MyGrid1.GotFocus
        Me.CancelButton = Nothing
    End Sub

    Private Sub MyGrid1_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles MyGrid1.Validated
        Me.CancelButton = Comm_Close
    End Sub

    Private Sub MyGrid1_BeforeColUpdate(ByVal sender As System.Object, ByVal e As C1.Win.C1TrueDBGrid.BeforeColUpdateEventArgs) Handles MyGrid1.BeforeColUpdate
        If Zb_Check() = False Then Exit Sub
        If e.Column.Name = "数量" Then
            If VerifyNum(MyGrid1.Columns("M_OtherIn_Num").Value, MyGrid1.Row) = False Then
                e.Cancel = True
            End If
        End If
        If e.Column.Name = "单价" Then
            If VerifyPrice(MyGrid1.Columns("M_OtherIn_Price").Value) = False Then
                e.Cancel = True
            End If
        End If
    End Sub

    'Private Sub MyGrid1_Error(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.ErrorEventArgs) Handles MyGrid1.Error
    '    e.Handled = True

    '    If StrConv(e.Exception.Message, VbStrConv.Narrow) = "列""M_Buy_Num""不允许空值?" Then
    '        MessageBox.Show("入库数量不能为空!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
    '        MyGrid1.SetActiveCell(MyGrid1.Row, MyGrid1.Splits(0).DisplayColumns.IndexOf(MyGrid1.Columns("M_Buy_Num")))
    '    End If

    '    If StrConv(e.Exception.Message, VbStrConv.Narrow) = "列""M_OtherIn_Price""不允许空值?" Then
    '        MessageBox.Show("单价不能为空!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
    '        MyGrid1.SetActiveCell(MyGrid1.Row, MyGrid1.Splits(0).DisplayColumns.IndexOf(MyGrid1.Columns("M_Buy_Price")))
    '    End If
    'End Sub
#End Region

#Region "快捷键"
    Private Sub ShortCutKey(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles MyBase.KeyDown
        If e.Control = True And e.KeyCode = Keys.S And Comm_Save.Enabled = True Then
            Call Data_Save("保存")
        End If

        If e.KeyData = Keys.F2 And Comm_New.Enabled = True Then
            MyGrid1.Focus()
            sender.tag = "新单"
            Call Comm_Click(sender, Nothing)
        End If

        If e.KeyData = Keys.F3 And Comm_Save.Enabled = True Then
            MyGrid1.Focus()
            sender.tag = "保存"
            Call Comm_Click(sender, Nothing)
        End If

        If e.KeyData = Keys.F4 And Comm_Complete.Enabled = True Then
            MyGrid1.Focus()
            sender.tag = "完成"
            Call Comm_Click(sender, Nothing)
        End If

        If e.KeyData = Keys.F5 And Comm_Print.Enabled = True Then
            MyGrid1.Focus()
            sender.tag = "打印"
            Call Comm_Click(sender, Nothing)
        End If
        If e.KeyData = Keys.F6 And Comm_Search.Enabled = True Then
            MyGrid1.Focus()
            sender.tag = "速查"
            Call Comm_Click(sender, Nothing)
        End If
    End Sub
#End Region

#End Region

#Region "自定义函数"

#Region "验证函数"

    Private Function Zb_Check() As Boolean
        If WareHouse_DtCom.SelectedValue Is Nothing Then
            MessageBox.Show("请选择库房!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
            WareHouse_DtCom.Select()
            Return False
        End If
        If InOutClass_DtCom.SelectedValue Is Nothing Then
            MessageBox.Show("请选择入库类别!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
            InOutClass_DtCom.Select()
            Return False
        End If
        Return True
    End Function

    Private Function Zb_CheckWc() As Boolean
        If B_Materials_Other_In1.GetRecordCount("M_OtherIn_Code='" & Code_Text.Text.Trim & "' And OrdersStatus='完成'") > 0 Then
            MsgBox("此次入库已经完成!", MsgBoxStyle.Exclamation, "提示")
            Return False
        End If
        Return True
    End Function

    Private Function Cb_CheckRowCounts() As Boolean
        If MyGrid1.RowCount = 0 Then
            MsgBox("尚未录入数据", MsgBoxStyle.Exclamation, "提示")
            Return False
        End If
        Return True
    End Function

    '对从表每行数据验证批号
    Private Function Cb_CheckMaterialsLot(ByVal _row As DataRow) As Boolean

        '判断库存中是否有 指定物资，指定批号的 物资
        If B_Materials_Stock.GetRecordCount("Materials_Code = '" & _row("Materials_Code") & "' AND MaterialsLot = '" & _row("MaterialsLot") & "'") > 0 Then
            '同一种物资，如果批号相同，那么单价 与 有效期 也相同
            If B_Materials_Stock.GetRecordCount("Materials_Code = '" & _row("Materials_Code") & "' AND MaterialsLot = '" & _row("MaterialsLot") & "' AND CONVERT(varchar(10),MaterialsExpiryDate,126) = '" & Format(_row("MaterialsExpiryDate"), "yyyy-MM-dd") & "' AND MaterialsStore_Price =  " & _row("M_OtherIn_Price")) = 0 Then
                MsgBox("请修改物资：" & _row("Materials_Name") & "批号：" & _row("MaterialsLot") & "的有效期或单价,或更改批号！", MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
                'MyGrid1.SetActiveCell(My_Table.Rows.IndexOf(_row), 0)
                'MyGrid1.Row = CStr((My_Table.Rows.IndexOf(_row)))
                Return False
            End If
        End If

        Return True
    End Function

    Private Function Cb_Check() As Boolean
        MyGrid1.UpdateData()
        Dim i As Integer = 0
        For Each _Row In My_Table.Rows
            If _Row.RowState = DataRowState.Deleted Then
                Continue For
            End If
            '入库数量验证
            If VerifyNum(_Row("M_OtherIn_Num"), i) = False Then
                MyGrid1.Focus()
                MyGrid1.SetActiveCell(i, MyGrid1.Splits(0).DisplayColumns.IndexOf(MyGrid1.Columns("M_OtherIn_Num")))
                Return False
            End If
            '物资批号验证
            If Not Cb_CheckMaterialsLot(_Row) Then
                MyGrid1.Focus()
                MyGrid1.SetActiveCell(i, MyGrid1.Splits(0).DisplayColumns.IndexOf(MyGrid1.Columns("MaterialsLot")))
                Return False
            End If



            If M_Materials_Other_In1.WriteOffStatus = "冲销" Then
                _Row("M_OtherIn_RealNo") = 0
                _Row("M_OtherIn_WriteoffNo") = 0
                _Row("M_OtherIn_Money") = _Row("M_OtherIn_Num") * _Row("M_OtherIn_Price")
                _Row("M_OtherIn_RealMoney") = 0
            Else
                Dim MaterialsStock_Code As String = GetMaterialsStockCode(_Row)
                If MaterialsStock_Code Is Nothing Then
                    HisControl.msg.Show("生成库存编码失败!", "提示")
                    MyGrid1.Focus()
                    MyGrid1.SetActiveCell(i, MyGrid1.Splits(0).DisplayColumns.IndexOf(MyGrid1.Columns("MaterialsLot")))
                    Return False
                End If
                _Row("MaterialsStock_Code") = MaterialsStock_Code
                _Row("M_OtherIn_RealNo") = _Row("M_OtherIn_Num") + _Row("M_OtherIn_WriteoffNo")
                _Row("M_OtherIn_Money") = CDec(_Row("M_OtherIn_Num")) * CDec(_Row("M_OtherIn_Price"))
                _Row("M_OtherIn_RealMoney") = CDec(_Row("M_OtherIn_RealNo")) * CDec(_Row("M_OtherIn_Price"))
            End If
            i = i + 1
        Next
        Return True
    End Function


    '验证入库数量是否正确
    Private Function VerifyNum(ByVal Num As String, ByVal Index As Integer) As Boolean
        If  Common.Tools.IsNumber(Num) = False Then
            'HisControl.msg.Show("请输入正确数字!", "提示")
            MessageBox.Show("请输入正确数字!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
            Return False
        End If
        If M_Materials_Other_In1.WriteOffStatus & "" <> "冲销" Then
            If CDbl(Num) <= 0 Then
                'HisControl.msg.Show("入库数量必须大于0!", "提示")
                MessageBox.Show("入库数量必须大于0!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
                Return False
            End If
        Else
            If CDbl(Num) >= 0 Then
                'HisControl.msg.Show("冲销数量必须小于0!", "提示")
                MessageBox.Show("冲销数量必须小于0!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
                Return False
            End If
        End If

        If M_Materials_Other_In1.WriteOffStatus & "" = "冲销" Then
            Dim row As DataRow = Cb_Cm.List(Index).row
            If -CDbl(Num) > B_Materials_Other_In2.GetModelByCondition("M_OtherIn_Code='" & M_Materials_Other_In1.WriteOff_Code & "' AND Materials_Code = '" & row("Materials_Code") & "' AND MaterialsLot = '" & row("MaterialsLot") & "' and MaterialsStock_Code='" & row("MaterialsStock_Code") & "'").M_OtherIn_RealNo Then
                'HisControl.msg.Show("冲销数量不能大于可冲数量!", "提示")
                MessageBox.Show("冲销数量不能大于可冲数量!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
                Return False
            End If


            If -CDbl(Num) > B_Materials_Stock.GetModel(row("MaterialsStock_Code")).MaterialsStore_Num Then
                'HisControl.msg.Show("冲销数量不能大于库存数量!", "提示")
                MessageBox.Show("冲销数量不能大于可冲数量!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
                Return False
            End If
        End If
        Return True
    End Function

    '验证价格是否正确
    Private Function VerifyPrice(ByVal Num As String) As Boolean
        If  Common.Tools.IsNumber(Num) = False Then
            'HisControl.msg.Show("请输入正确数字!", "提示")
            MessageBox.Show("请输入正确数字!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
            Return False
        End If
        If CDbl(Num) <= 0 Then
            'HisControl.msg.Show("单价必须大于0!", "提示")
            MessageBox.Show("单价必须大于0!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
            Return False
        End If
        Return True
    End Function

#End Region

#Region "按钮函数"
    Private Sub Data_Delete()
        If MessageBox.Show("确认是否删除当前记录,如果删除该记录将不可恢复!", "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Error, MessageBoxDefaultButton.Button1) = Windows.Forms.DialogResult.OK Then
            Try
                MyGrid1.Delete()
                Call F_Sum()
            Catch ex As Exception
                If ex.Message.ToString = "索引 -1 不是为负数，就是大于行数。" Then
                    MsgBox("未选中任何行！", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
                End If
            End Try
        End If
    End Sub

    Private Sub Data_DeleteAll()
        If MessageBox.Show("确认是否删除当前单据,如果删除该单据将不可恢复!", "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Error, MessageBoxDefaultButton.Button1) = Windows.Forms.DialogResult.OK Then
            B_Materials_Other_In1.DeleteAll(M_Materials_Other_In1.M_OtherIn_Code)
            Zb_Clear()
        End If
    End Sub

    Private Sub Data_New()
        MyGrid1.UpdateData()
        If My_Table.DataSet.HasChanges = True Then
            If B_Materials_Other_In1.GetRecordCount("  M_OtherIn_Code='" & Code_Text.Text.Trim & "' and OrdersStatus='录入'") = 1 Then
                If MessageBox.Show("数据尚未保存,是否保存数据?", "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1) = Windows.Forms.DialogResult.OK Then
                    Call Data_Save("保存")
                End If
            End If
        End If
        Call Zb_Clear()
    End Sub

    Private Sub Data_Save(ByVal arg As String)

        If Not Zb_CheckWc() Then Exit Sub
        If Not Zb_Check() Then Exit Sub
        If Not Cb_CheckRowCounts() Then Exit Sub
        If Not Cb_Check() Then Exit Sub

        If arg = "保存" Then
            Call Zb_Save()
        ElseIf arg = "完成" Then
            Call Zb_Save()
            Call Data_Complete()
        End If
    End Sub

    Private Sub Data_Complete()
        If MsgBox("是否完成此次入库？", MsgBoxStyle.Information + MsgBoxStyle.OkCancel + MsgBoxStyle.DefaultButton1, "提示:") = MsgBoxResult.Cancel Then Exit Sub
        Try
            With M_Materials_Other_In1
                .Finish_Date = Format(Now, "yyyy-MM-dd HH:mm")
                .OrdersStatus = "完成"
            End With
            B_Materials_Other_In1.Complete(M_Materials_Other_In1)
            StatisticsDataShow()
            HisControl.msg.Show("入库完成!", "提示")
            BtnState()
            P_Data_Show()
            Focus()
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            WareHouse_DtCom.Select()
            Exit Sub
        End Try
    End Sub

    Private Sub Data_Print()
        Dim StiRpt As New StiReport
        StiRpt.Load(".\Rpt\物资其他入库单.mrt")

        StiRpt.ReportName = "物资其他入库单"
        StiRpt.RegData(My_Table)
        StiRpt.Pages(0).PaperSize = Printing.PaperKind.A4
        StiRpt.Pages(0).Margins.Top = 1
        StiRpt.Pages(0).Margins.Bottom = 1
        StiRpt.Pages(0).Margins.Left = 1
        StiRpt.Pages(0).Margins.Right = 1
        StiRpt.Compile()

        StiRpt("入库编码") = Code_Text.Text.Trim
        StiRpt("经手人") = Jsr_Text.Text.Trim
        StiRpt("物资仓库") = WareHouse_DtCom.Text.Trim
        StiRpt("入库类别") = InOutClass_DtCom.Text.Trim
        StiRpt("入库时间") = Format(Form_Date.Value, "yyyy-MM-dd")
        StiRpt("打印时间") = Format(Now, "yyyy-MM-dd HH:mm:ss")

        StiRpt.Render()
        'StiRpt.Design()
        StiRpt.Show()
    End Sub

#End Region

#Region "数据库更改"

#Region "主表__编辑"
    Private Sub Zb_Save()                       '主表保存
        If Rinsert = True Then     '增加记录
            Call Zb_Add()
        Else                       '编辑记录
            Call Zb_Edit()
        End If
        Call Cb_Update()
        HisControl.msg.Show("数据保存成功!", "提示")
        MyGrid1.Focus()
    End Sub

    Private Sub Zb_Add()    '增加记录
        Try

            With M_Materials_Other_In1
                .M_OtherIn_Code = B_Materials_Other_In1.MaxCode
                Code_Text.Text = .M_OtherIn_Code
                .MaterialsInOut_Code = InOutClass_DtCom.SelectedValue
                .MaterialsWh_Code = WareHouse_DtCom.SelectedValue
                .OtherIn_Date = Format(Form_Date.Value, "yyyy-MM-dd HH:mm")
                .Input_Date = Format(Now, "yyyy-MM-dd HH:mm")
                .Finish_Date = Nothing
                .Jsr_Code = HisVar.HisVar.JsrCode
                .TotalMoney = V_TotalMoney
                .M_OtherIn_Memo = Memo_Text.Text.Trim
                .OrdersStatus = "录入"
                .WriteOff_Code = Nothing
                .WriteOffStatus = Nothing
            End With
            B_Materials_Other_In1.Add(M_Materials_Other_In1)
            Rinsert = False
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            Exit Sub
        End Try
    End Sub

    Private Sub Zb_Edit()   '编辑记录
        Try
            With M_Materials_Other_In1
                .MaterialsInOut_Code = InOutClass_DtCom.SelectedValue
                .MaterialsWh_Code = WareHouse_DtCom.SelectedValue
                .OtherIn_Date = Format(Form_Date.Value, "yyyy-MM-dd HH:mm")
                .Jsr_Code = HisVar.HisVar.JsrCode
                .M_OtherIn_Memo = Memo_Text.Text.Trim
                .TotalMoney = V_TotalMoney
            End With
            B_Materials_Other_In1.Update(M_Materials_Other_In1)
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
            Exit Sub
        End Try
    End Sub
#End Region

#Region "从表__编辑"
    Private Sub Cb_Edit()

        Dim Cb_Insert As Boolean

        If (MyGrid1.Row + 1) >= MyGrid1.RowCount Then '添加明细
            Cb_Insert = True
        Else '修改明细
            Cb_Insert = False
            Cb_Row = Cb_Cm.List(MyGrid1.Row).Row
        End If

        Dim vform As New MaterialsOtherInMx(Cb_Insert, Cb_Row, My_Table, m_Rc)
        If BaseFunc.BaseFunc.CheckOwnForm(Me, vform) = False Then
            AddHandler m_Rc.AddChangeEvent, AddressOf AddChange
            vform.ShowDialog()
            RemoveHandler m_Rc.AddChangeEvent, AddressOf AddChange
        End If
    End Sub

    Private Sub Cb_Update()   '从表更新
        For Each _row As DataRow In My_Table.Rows
            If _row.RowState = DataRowState.Added Then
                CbData_Insert(_row)
            End If
            If _row.RowState = DataRowState.Modified Then
                CbData_Update(_row)
            End If
            If _row.RowState = DataRowState.Deleted Then
                CbData_Delete(_row)
            End If
        Next
        My_Table.AcceptChanges()
    End Sub

    Private Sub CbData_Update(ByVal Cb_Row As DataRow)   '从表更新
        Dim model As ModelOld.M_Materials_Other_In2 = B_Materials_Other_In2.GetModel(Cb_Row("M_OtherIn_Detail_Code"))
        With model
            .Materials_Code = Cb_Row("Materials_Code")
            .MaterialsStock_Code = Cb_Row("MaterialsStock_Code")
            .MaterialsLot = Cb_Row("MaterialsLot")
            .MaterialsExpiryDate = Cb_Row("MaterialsExpiryDate")
            .M_OtherIn_Num = Cb_Row("M_OtherIn_Num")
            .M_OtherIn_WriteoffNo = Cb_Row("M_OtherIn_WriteoffNo")
            .M_OtherIn_RealNo = Cb_Row("M_OtherIn_RealNo")
            .M_OtherIn_Price = Cb_Row("M_OtherIn_Price")
            .M_OtherIn_Money = Cb_Row("M_OtherIn_Money")
            .M_OtherIn_RealMoney = Cb_Row("M_OtherIn_RealMoney")
            .M_OtherInDetail_Memo = Cb_Row("M_OtherInDetail_Memo")
        End With
        B_Materials_Other_In2.Update(model)
    End Sub

    Private Sub CbData_Insert(ByVal Cb_Row As DataRow)   '从表增加 
        Cb_Row("M_OtherIn_Code") = M_Materials_Other_In1.M_OtherIn_Code
        Cb_Row("M_OtherIn_Detail_Code") = B_Materials_Other_In2.MaxCode(M_Materials_Other_In1.M_OtherIn_Code)

        Dim model As New ModelOld.M_Materials_Other_In2
        With model
            .M_OtherIn_Code = Cb_Row("M_OtherIn_Code")
            .Materials_Code = Cb_Row("Materials_Code")
            .MaterialsStock_Code = Cb_Row("MaterialsStock_Code")
            .M_OtherIn_Detail_Code = Cb_Row("M_OtherIn_Detail_Code")
            .MaterialsLot = Cb_Row("MaterialsLot")
            .MaterialsExpiryDate = Cb_Row("MaterialsExpiryDate")
            .M_OtherIn_Num = Cb_Row("M_OtherIn_Num")
            .M_OtherIn_WriteoffNo = Cb_Row("M_OtherIn_WriteoffNo")
            .M_OtherIn_RealNo = Cb_Row("M_OtherIn_RealNo")
            .M_OtherIn_Price = Cb_Row("M_OtherIn_Price")
            .M_OtherIn_Money = Cb_Row("M_OtherIn_Money")
            .M_OtherIn_RealMoney = Cb_Row("M_OtherIn_RealMoney")
            .M_OtherInDetail_Memo = Cb_Row("M_OtherInDetail_Memo")
        End With
        B_Materials_Other_In2.Add(model)
    End Sub

    Private Sub CbData_Delete(ByVal Cb_Row As DataRow)
        B_Materials_Other_In2.Delete(Cb_Row("M_OtherIn_Detail_Code", DataRowVersion.Original))
    End Sub

#End Region

#End Region

    '生成 指定库房，指定物资，指定批号的 库存编码（如果在Materials_Stock 表中 已经存在指定库房，指定物资，指定批号 的库存编码 则使用该库存编码MaterialsStock_Code，否则生成一个新的库存编码，并将信息插入Materials_Stock表中）
    Private Function GetMaterialsStockCode(ByVal _row As DataRow) As String

        Try
            Dim MaterialsStock_Code As String = B_Materials_Stock.GetMaterialsStockCode(_row("Materials_Code"), WareHouse_DtCom.SelectedValue, _row("MaterialsLot"), _row("MaterialsExpiryDate"), _row("M_OtherIn_Price"))

            If String.IsNullOrEmpty(MaterialsStock_Code) Then
                MaterialsStock_Code = B_Materials_Stock.MaxCode(_row("Materials_Code"))

                Dim model As New ModelOld.M_Materials_Stock
                With model
                    .Materials_Code = _row("Materials_Code")
                    .MaterialsWh_Code = WareHouse_DtCom.SelectedValue
                    .MaterialsStock_Code = MaterialsStock_Code
                    .MaterialsLot = _row("MaterialsLot")
                    .MaterialsExpiryDate = Format(_row("MaterialsExpiryDate"), "yyyy-MM-dd")
                    .MaterialsStore_Num = 0
                    .MaterialsStore_Price = _row("M_OtherIn_Price")
                    .MaterialsStore_Money = 0
                End With

                If Not B_Materials_Stock.Add(model) Then Return Nothing

            End If
            Return MaterialsStock_Code
        Catch ex As Exception
            MsgBox(ex.ToString, MsgBoxStyle.Exclamation, "提示")
            Return Nothing
        End Try

    End Function

    '快速查询 窗体中选择行 发生改变时，更新主表窗体显示数据
    Private Sub ChangeModel(ByVal model As M_Materials_Other_In1)
        M_Materials_Other_In1 = model
        If M_Materials_Other_In1 Is Nothing Then Exit Sub
        Call Zb_Show()
    End Sub

    Public Overrides Sub F_Sum()
        If MyGrid1.RowCount = 0 Then
            WareHouse_DtCom.Enabled = True
            InOutClass_DtCom.Enabled = True
        Else
            WareHouse_DtCom.Enabled = False
            InOutClass_DtCom.Enabled = False
        End If

        Dim Total_OtherIn_Num As Double = IIf(My_Table.Compute("Sum(M_OtherIn_Num)", "") Is DBNull.Value, 0, My_Table.Compute("Sum(M_OtherIn_Num)", ""))
        MyGrid1.Columns("M_OtherIn_Num").FooterText = Total_OtherIn_Num.ToString("0.00##")

        Dim Total_OtherIn_Money As Double = IIf(My_Table.Compute("Sum(M_OtherIn_Money)", "") Is DBNull.Value, 0, My_Table.Compute("Sum(M_OtherIn_Money)", ""))
        MyGrid1.Columns("M_OtherIn_Money").FooterText = Total_OtherIn_Money.ToString("0.00##")

        If M_Materials_Other_In1.OrdersStatus = "完成" And M_Materials_Other_In1.WriteOffStatus <> "冲销" Then
            Dim Total_OtherIn_WriteoffNo As Double = IIf(My_Table.Compute("Sum(M_OtherIn_WriteoffNo)", "") Is DBNull.Value, 0, My_Table.Compute("Sum(M_OtherIn_WriteoffNo)", ""))
            MyGrid1.Columns("M_OtherIn_WriteoffNo").FooterText = Total_OtherIn_WriteoffNo.ToString("0.00##")

            Dim Total_OtherIn_RealNo As Double = IIf(My_Table.Compute("Sum(M_OtherIn_RealNo)", "") Is DBNull.Value, 0, My_Table.Compute("Sum(M_OtherIn_RealNo)", ""))
            MyGrid1.Columns("M_OtherIn_RealNo").FooterText = Total_OtherIn_RealNo.ToString("0.00##")

            Dim Total_Real_Money As Double = IIf(My_Table.Compute("Sum(M_OtherIn_RealMoney)", "") Is DBNull.Value, 0, My_Table.Compute("Sum(M_OtherIn_RealMoney)", ""))
            MyGrid1.Columns("M_OtherIn_RealMoney").FooterText = Total_Real_Money.ToString("0.00##")
        End If

        V_TotalMoney = Total_OtherIn_Money
    End Sub

    Private Sub AddChange(ByVal value As String)
        Call F_Sum()
    End Sub

#End Region

#Region "输入法设置"
    '中文
    Private Sub Chs_Input(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Memo_Text.GotFocus
        InputLanguage.CurrentInputLanguage = HisVar.HisVar.InputCode
    End Sub
    '英文
    Private Sub Eng_Input(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles WareHouse_DtCom.GotFocus, InOutClass_DtCom.GotFocus
        InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages(0)
    End Sub
#End Region


End Class
