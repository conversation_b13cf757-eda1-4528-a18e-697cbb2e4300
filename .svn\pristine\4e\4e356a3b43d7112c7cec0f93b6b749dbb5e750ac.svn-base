﻿/**  版本信息模板在安装目录下，可自行修改。
* D_LIS_TestItem.cs
*
* 功 能： N/A
* 类 名： D_LIS_TestItem
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/10/17 星期一 下午 1:38:53   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using System.Collections.Generic;
namespace DAL
{
    /// <summary>
    /// 数据访问类:D_LIS_TestItem
    /// </summary>
    public partial class D_LIS_TestItem
    {
        public D_LIS_TestItem()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(string TestItem_Code)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from LIS_TestItem");
            strSql.Append(" where TestItem_Code=@TestItem_Code ");
            SqlParameter[] parameters = {
					new SqlParameter("@TestItem_Code", SqlDbType.Char,10)			};
            parameters[0].Value = TestItem_Code;

            return HisVar.HisVar.Sqldal.Exists(strSql.ToString(), parameters);
        }

        public int MaxOrder(string TestXm_Code)
        {
            object max = HisVar.HisVar.Sqldal.GetSingle("SELECT MAX(ItemOrder)  FROM dbo.LIS_TestItem WHERE TestXm_Code='" + TestXm_Code
                + "'");
            if (max == null)
            {
                return 1;
            }
            else
            {
                return int.Parse(max.ToString()) + 1;
            }

        }
        public string MaxCode()
        {
            try
            {
                string max = (string)(HisVar.HisVar.Sqldal.F_MaxCode("select max(TestItem_Code) from LIS_TestItem", 10));
                return max;
            }
            catch (Exception)
            { throw; }

        }

        public bool ExchangeOrder(ModelOld.M_LIS_TestItem model1, ModelOld.M_LIS_TestItem model2)
        {
            List<string> sqlList = new List<string>();
            List<SqlParameter[]> sqlParaList = new List<SqlParameter[]>();
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update LIS_TestItem set ");
            strSql.Append("ItemOrder=@ItemOrder");
            strSql.Append(" where TestItem_Code=@TestItem_Code ");

            SqlParameter[] parameters = {
					new SqlParameter("@ItemOrder", SqlDbType.Int,4),		
					new SqlParameter("@TestItem_Code", SqlDbType.Char,10)};

            parameters[0].Value = model2.ItemOrder;
            parameters[1].Value = model1.TestItem_Code;
            sqlList.Add(strSql.ToString());
            sqlParaList.Add(parameters);

            parameters = new SqlParameter[]{
					new SqlParameter("@ItemOrder", SqlDbType.Int,4),		
					new SqlParameter("@TestItem_Code", SqlDbType.Char,10)};
            parameters[0].Value = model1.ItemOrder;
            parameters[1].Value = model2.TestItem_Code;
            sqlList.Add(strSql.ToString());
            sqlParaList.Add(parameters);

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(sqlList, sqlParaList);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }

        }
        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ModelOld.M_LIS_TestItem model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into LIS_TestItem(");
            strSql.Append("TestXm_Code,TestItem_Code,Item_Name,Item_Jc,Item_Dw,MaleMin,MaleMax,FemaleMin,FemaleMax,isDefault,ItemGroup,ItemOrder,Memo)");
            strSql.Append(" values (");
            strSql.Append("@TestXm_Code,@TestItem_Code,@Item_Name,@Item_Jc,@Item_Dw,@MaleMin,@MaleMax,@FemaleMin,@FemaleMax,@isDefault,@ItemGroup,@ItemOrder,@Memo)");
            SqlParameter[] parameters = {
					new SqlParameter("@TestXm_Code", SqlDbType.Char,5),
					new SqlParameter("@TestItem_Code", SqlDbType.Char,10),
					new SqlParameter("@Item_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Item_Jc", SqlDbType.VarChar,50),
					new SqlParameter("@Item_Dw", SqlDbType.VarChar,50),
					new SqlParameter("@MaleMin", SqlDbType.Decimal,9),
					new SqlParameter("@MaleMax", SqlDbType.Decimal,9),
					new SqlParameter("@FemaleMin", SqlDbType.Decimal,9),
					new SqlParameter("@FemaleMax", SqlDbType.Decimal,9),
					new SqlParameter("@isDefault", SqlDbType.Bit,1),
					new SqlParameter("@ItemGroup", SqlDbType.VarChar,50),
					new SqlParameter("@ItemOrder", SqlDbType.Int,4),
					new SqlParameter("@Memo", SqlDbType.VarChar,200)};
            parameters[0].Value = model.TestXm_Code;
            parameters[1].Value = model.TestItem_Code;
            parameters[2].Value = model.Item_Name;
            parameters[3].Value = model.Item_Jc;
            parameters[4].Value = model.Item_Dw;
            parameters[5].Value = Common.Tools.IsValueNull(model.MaleMin);
            parameters[6].Value = Common.Tools.IsValueNull(model.MaleMax);
            parameters[7].Value = Common.Tools.IsValueNull(model.FemaleMin);
            parameters[8].Value = Common.Tools.IsValueNull(model.FemaleMax);
            parameters[9].Value = model.isDefault;
            parameters[10].Value = model.ItemGroup;
            parameters[11].Value = model.ItemOrder;
            parameters[12].Value = model.Memo;

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ModelOld.M_LIS_TestItem model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update LIS_TestItem set ");
            strSql.Append("TestXm_Code=@TestXm_Code,");
            strSql.Append("Item_Name=@Item_Name,");
            strSql.Append("Item_Jc=@Item_Jc,");
            strSql.Append("Item_Dw=@Item_Dw,");
            strSql.Append("MaleMin=@MaleMin,");
            strSql.Append("MaleMax=@MaleMax,");
            strSql.Append("FemaleMin=@FemaleMin,");
            strSql.Append("FemaleMax=@FemaleMax,");
            strSql.Append("isDefault=@isDefault,");
            strSql.Append("ItemGroup=@ItemGroup,");
            strSql.Append("ItemOrder=@ItemOrder,");
            strSql.Append("Memo=@Memo");
            strSql.Append(" where TestItem_Code=@TestItem_Code ");
            SqlParameter[] parameters = {
					new SqlParameter("@TestXm_Code", SqlDbType.Char,5),
					new SqlParameter("@Item_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Item_Jc", SqlDbType.VarChar,50),
					new SqlParameter("@Item_Dw", SqlDbType.VarChar,50),
					new SqlParameter("@MaleMin", SqlDbType.Decimal,9),
					new SqlParameter("@MaleMax", SqlDbType.Decimal,9),
					new SqlParameter("@FemaleMin", SqlDbType.Decimal,9),
					new SqlParameter("@FemaleMax", SqlDbType.Decimal,9),
					new SqlParameter("@isDefault", SqlDbType.Bit,1),
					new SqlParameter("@ItemGroup", SqlDbType.VarChar,50),
					new SqlParameter("@ItemOrder", SqlDbType.Int,4),
					new SqlParameter("@Memo", SqlDbType.VarChar,200),
					new SqlParameter("@TestItem_Code", SqlDbType.Char,10)};
            parameters[0].Value = model.TestXm_Code;
            parameters[1].Value = model.Item_Name;
            parameters[2].Value = model.Item_Jc;
            parameters[3].Value = model.Item_Dw;
            parameters[4].Value = Common.Tools.IsValueNull(model.MaleMin);
            parameters[5].Value = Common.Tools.IsValueNull(model.MaleMax);
            parameters[6].Value = Common.Tools.IsValueNull(model.FemaleMin);
            parameters[7].Value = Common.Tools.IsValueNull(model.FemaleMax);
            parameters[8].Value = model.isDefault;
            parameters[9].Value = model.ItemGroup;
            parameters[10].Value = model.ItemOrder;
            parameters[11].Value = model.Memo;
            parameters[12].Value = model.TestItem_Code;

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(string TestItem_Code)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from LIS_TestItem ");
            strSql.Append(" where TestItem_Code=@TestItem_Code ");
            SqlParameter[] parameters = {
					new SqlParameter("@TestItem_Code", SqlDbType.Char,10)			};
            parameters[0].Value = TestItem_Code;

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string TestItem_Codelist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from LIS_TestItem ");
            strSql.Append(" where TestItem_Code in (" + TestItem_Codelist + ")  ");
            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ModelOld.M_LIS_TestItem GetModel(string TestItem_Code)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 TestXm_Code,TestItem_Code,Item_Name,Item_Jc,Item_Dw,MaleMin,MaleMax,FemaleMin,FemaleMax,isDefault,ItemGroup,ItemOrder,Memo from LIS_TestItem ");
            strSql.Append(" where TestItem_Code=@TestItem_Code ");
            SqlParameter[] parameters = {
					new SqlParameter("@TestItem_Code", SqlDbType.Char,10)			};
            parameters[0].Value = TestItem_Code;

            ModelOld.M_LIS_TestItem model = new ModelOld.M_LIS_TestItem();
            DataSet ds = HisVar.HisVar.Sqldal.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ModelOld.M_LIS_TestItem DataRowToModel(DataRow row)
        {
            ModelOld.M_LIS_TestItem model = new ModelOld.M_LIS_TestItem();
            if (row != null)
            {
                if (row["TestXm_Code"] != null)
                {
                    model.TestXm_Code = row["TestXm_Code"].ToString();
                }
                if (row["TestItem_Code"] != null)
                {
                    model.TestItem_Code = row["TestItem_Code"].ToString();
                }
                if (row["Item_Name"] != null)
                {
                    model.Item_Name = row["Item_Name"].ToString();
                }
                if (row["Item_Jc"] != null)
                {
                    model.Item_Jc = row["Item_Jc"].ToString();
                }
                if (row["Item_Dw"] != null)
                {
                    model.Item_Dw = row["Item_Dw"].ToString();
                }
                if (row["MaleMin"] != null && row["MaleMin"].ToString() != "")
                {
                    model.MaleMin = decimal.Parse(row["MaleMin"].ToString());
                }
                if (row["MaleMax"] != null && row["MaleMax"].ToString() != "")
                {
                    model.MaleMax = decimal.Parse(row["MaleMax"].ToString());
                }
                if (row["FemaleMin"] != null && row["FemaleMin"].ToString() != "")
                {
                    model.FemaleMin = decimal.Parse(row["FemaleMin"].ToString());
                }
                if (row["FemaleMax"] != null && row["FemaleMax"].ToString() != "")
                {
                    model.FemaleMax = decimal.Parse(row["FemaleMax"].ToString());
                }
                if (row["isDefault"] != null && row["isDefault"].ToString() != "")
                {
                    if ((row["isDefault"].ToString() == "1") || (row["isDefault"].ToString().ToLower() == "true"))
                    {
                        model.isDefault = true;
                    }
                    else
                    {
                        model.isDefault = false;
                    }
                }
                if (row["ItemGroup"] != null)
                {
                    model.ItemGroup = row["ItemGroup"].ToString();
                }
                if (row["ItemOrder"] != null && row["ItemOrder"].ToString() != "")
                {
                    model.ItemOrder = int.Parse(row["ItemOrder"].ToString());
                }
                if (row["Memo"] != null)
                {
                    model.Memo = row["Memo"].ToString();
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select TestXm_Code,TestItem_Code,Item_Name,Item_Jc,Item_Dw,MaleMin,MaleMax,FemaleMin,FemaleMax,isDefault,ItemGroup,ItemOrder,Memo ");
            strSql.Append(" FROM LIS_TestItem ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" TestXm_Code,TestItem_Code,Item_Name,Item_Jc,Item_Dw,MaleMin,MaleMax,FemaleMin,FemaleMax,isDefault,ItemGroup,ItemOrder,Memo ");
            strSql.Append(" FROM LIS_TestItem ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM LIS_TestItem ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.TestItem_Code desc");
            }
            strSql.Append(")AS Row, T.*  from LIS_TestItem T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

        /*
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetList(int PageSize,int PageIndex,string strWhere)
        {
            SqlParameter[] parameters = {
                    new SqlParameter("@tblName", SqlDbType.VarChar, 255),
                    new SqlParameter("@fldName", SqlDbType.VarChar, 255),
                    new SqlParameter("@PageSize", SqlDbType.Int),
                    new SqlParameter("@PageIndex", SqlDbType.Int),
                    new SqlParameter("@IsReCount", SqlDbType.Bit),
                    new SqlParameter("@OrderType", SqlDbType.Bit),
                    new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
                    };
            parameters[0].Value = "LIS_TestItem";
            parameters[1].Value = "TestItem_Code";
            parameters[2].Value = PageSize;
            parameters[3].Value = PageIndex;
            parameters[4].Value = 0;
            parameters[5].Value = 0;
            parameters[6].Value = strWhere;	
            return HisVar.HisVar.Sqldal.RunProcedure("UP_GetRecordByPage",parameters,"ds");
        }*/

        #endregion  BasicMethod
        #region  ExtensionMethod

        #endregion  ExtensionMethod
    }
}

