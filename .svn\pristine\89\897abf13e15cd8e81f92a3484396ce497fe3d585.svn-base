﻿Imports System.Runtime.InteropServices
Imports System.IO
Imports System.Windows.Forms

Public Class XzxDkq116D
    <StructLayout(LayoutKind.Sequential, CharSet:=CharSet.Ansi, Pack:=1)> _
Private Structure IDCardData
        '结构中使用的字串，[]类似限定说明 说明此公共结构传输到非托管代码时封装定义
        <MarshalAs(UnmanagedType.ByValTStr, SizeConst:=32)> _
        Public Name As String
        '姓名   
        <MarshalAs(UnmanagedType.ByValTStr, SizeConst:=6)> _
        Public Sex As String
        '性别
        <MarshalAs(UnmanagedType.ByValTStr, SizeConst:=20)> _
        Public Nation As String
        '名族
        <MarshalAs(UnmanagedType.ByValTStr, SizeConst:=18)> _
        Public Born As String
        '出生日期
        <MarshalAs(UnmanagedType.ByValTStr, SizeConst:=72)> _
        Public Address As String
        '住址
        <MarshalAs(UnmanagedType.ByValTStr, SizeConst:=38)> _
        Public IDCardNo As String
        ' 身份证号
        <MarshalAs(UnmanagedType.ByValTStr, SizeConst:=32)> _
        Public GrantDept As String
        '发证机关
        <MarshalAs(UnmanagedType.ByValTStr, SizeConst:=18)> _
        Public UserLifeBegin As String
        ' 有效开始日期
        <MarshalAs(UnmanagedType.ByValTStr, SizeConst:=18)> _
        Public UserLifeEnd As String
        ' 有效截止日期
        <MarshalAs(UnmanagedType.ByValTStr, SizeConst:=38)> _
        Public reserved As String
        ' 保留
        <MarshalAs(UnmanagedType.ByValTStr, SizeConst:=255)> _
        Public PhotoFileName As String
        '照片路径
    End Structure
    <DllImport(".\XzxDkq116D\SynIDCardAPI.dll", EntryPoint:="Syn_SetMaxRFByte", CharSet:=CharSet.Ansi)> _
    Private Shared Function Syn_SetMaxRFByte(ByVal iPort As Integer, ByVal ucByte As Byte, ByVal iIfOpen As Integer) As Integer
    End Function

    <DllImport(".\XzxDkq116D\SynIDCardAPI.dll", EntryPoint:="Syn_OpenPort", CharSet:=CharSet.Ansi)> _
    Private Shared Function Syn_OpenPort(ByVal iPort As Integer) As Integer
    End Function
    <DllImport(".\XzxDkq116D\SynIDCardAPI.dll", EntryPoint:="Syn_StartFindIDCard", CharSet:=CharSet.Ansi)> _
    Private Shared Function Syn_StartFindIDCard(ByVal iPort As Integer, ByRef pucIIN As Byte, ByVal iIfOpen As Integer) As Integer
    End Function
    <DllImport(".\XzxDkq116D\SynIDCardAPI.dll", EntryPoint:="Syn_SelectIDCard", CharSet:=CharSet.Ansi)> _
    Private Shared Function Syn_SelectIDCard(ByVal iPort As Integer, ByRef pucSN As Byte, ByVal iIfOpen As Integer) As Integer
    End Function
    <DllImport(".\XzxDkq116D\SynIDCardAPI.dll", EntryPoint:="Syn_ReadMsg", CharSet:=CharSet.Ansi)> _
    Private Shared Function Syn_ReadMsg(ByVal iPortID As Integer, ByVal iIfOpen As Integer, ByRef pIDCardData As IDCardData) As Integer
    End Function

    <DllImport(".\XzxDkq116D\SynIDCardAPI.dll", EntryPoint:="Syn_FindReader", CharSet:=CharSet.Ansi)> _
    Private Shared Function Syn_FindReader() As Integer
    End Function

    <DllImport(".\XzxDkq116D\SynIDCardAPI.dll", EntryPoint:="Syn_SetPhotoPath", CharSet:=CharSet.Ansi)> _
Private Shared Function Syn_SetPhotoPath(ByVal iOption As Integer, ByRef cPhotoPath As Byte) As Integer
    End Function


    Public Shared Function Dkq_RyXx()
        FileCopy(Application.StartupPath & "\XzxDkq116D\License.dat", "C:\License.dat")



        Dim CardMsg As New IDCardData()
        Dim nRet As Integer, nPort As Integer
        Dim pucIIN As Byte() = New Byte(3) {}
        Dim pucSN As Byte() = New Byte(7) {}
        Dim Ryxx(4) As String
        nPort = Convert.ToInt32(Convert.ToString(Syn_FindReader()))
        If Syn_OpenPort(nPort) = 0 Then
            If Syn_SetMaxRFByte(nPort, 80, 0) = 0 Then
                nRet = Syn_StartFindIDCard(nPort, pucIIN(0), 0)
                nRet = Syn_SelectIDCard(nPort, pucSN(0), 0)

                Dim cPath As Byte() = New Byte(255) {}
                nRet = Syn_SetPhotoPath(1, cPath(0))

                nRet = Syn_ReadMsg(nPort, 0, CardMsg)
                File.Delete(".\tmp.bmp")

                If nRet = 0 Then

                    Ryxx(0) = Trim(CardMsg.Name)
                    If Trim(CardMsg.Sex) = "1" Then
                        Ryxx(1) = "男"
                    Else
                        Ryxx(1) = "女"
                    End If

                    Select Case Trim(CardMsg.Nation)
                        Case "01"
                            Ryxx(2) = "汉族"
                        Case "02"
                            Ryxx(2) = "蒙古族"
                        Case "03"
                            Ryxx(2) = "回族"
                        Case "04"
                            Ryxx(2) = "藏族"
                        Case "05"
                            Ryxx(2) = "维吾尔族"
                        Case "06"
                            Ryxx(2) = "苗族"
                        Case "07"
                            Ryxx(2) = "彝族"
                        Case "08"
                            Ryxx(2) = "壮族"
                        Case "09"
                            Ryxx(2) = "布依族"
                        Case "10"
                            Ryxx(2) = "朝鲜族"
                        Case "11"
                            Ryxx(2) = "满族"
                        Case "12"
                            Ryxx(2) = "侗族"
                        Case "13"
                            Ryxx(2) = "瑶族"
                        Case "14"
                            Ryxx(2) = "白族"
                        Case "15"
                            Ryxx(2) = "土家族"
                        Case "16"
                            Ryxx(2) = "哈尼族"
                        Case "17"
                            Ryxx(2) = "哈萨克族"
                        Case "18"
                            Ryxx(2) = "傣族"
                        Case "19"
                            Ryxx(2) = "黎族"
                        Case "20"
                            Ryxx(2) = "僳僳族"
                        Case "21"
                            Ryxx(2) = "佤族"
                        Case "22"
                            Ryxx(2) = "畲族"
                        Case "23"
                            Ryxx(2) = "高山族"
                        Case "24"
                            Ryxx(2) = "拉祜族"
                        Case "25"
                            Ryxx(2) = "水族"
                        Case "26"
                            Ryxx(2) = "东乡族"
                        Case "27"
                            Ryxx(2) = "纳西族"
                        Case "28"
                            Ryxx(2) = "景颇族"
                        Case "29"
                            Ryxx(2) = "柯尔克孜族"
                        Case "30"
                            Ryxx(2) = "土族"
                        Case "31"
                            Ryxx(2) = "达斡尔族"
                        Case "32"
                            Ryxx(2) = "仫佬族"
                        Case "33"
                            Ryxx(2) = "羌族"
                        Case "34"
                            Ryxx(2) = "布朗族"
                        Case "35"
                            Ryxx(2) = "撤拉族"
                        Case "36"
                            Ryxx(2) = "毛难族"
                        Case "37"
                            Ryxx(2) = "仡佬族"
                        Case "38"
                            Ryxx(2) = "锡伯族"
                        Case "39"
                            Ryxx(2) = "阿昌族"
                        Case "40"
                            Ryxx(2) = "普米族"
                        Case "41"
                            Ryxx(2) = "塔吉克族"
                        Case "42"
                            Ryxx(2) = "怒族"
                        Case "43"
                            Ryxx(2) = "乌孜别克族"
                        Case "44"
                            Ryxx(2) = "俄罗斯族"
                        Case "45"
                            Ryxx(2) = "鄂温克族"
                        Case "46"
                            Ryxx(2) = "崩龙族"
                        Case "47"
                            Ryxx(2) = "保安族"
                        Case "48"
                            Ryxx(2) = "裕固族"
                        Case "49"
                            Ryxx(2) = "京族"
                        Case "50"
                            Ryxx(2) = "塔塔尔族"
                        Case "51"
                            Ryxx(2) = "独龙族"
                        Case "52"
                            Ryxx(2) = "鄂伦春族"
                        Case "53"
                            Ryxx(2) = "赫哲族"
                        Case "54"
                            Ryxx(2) = "门巴族"
                        Case "55"
                            Ryxx(2) = "珞巴族"
                        Case "56"
                            Ryxx(2) = "基诺族"

                    End Select


                    Ryxx(3) = Trim(CardMsg.Address)
                    Ryxx(4) = Trim(CardMsg.IDCardNo)

                Else
                    Ryxx(0) = "读取身份证信息失败"

                End If
            Else
                Ryxx(0) = "读取身份证信息失败"

            End If
        Else
            Ryxx(0) = "打开端口失败"

        End If
        Return Ryxx

    End Function

End Class
