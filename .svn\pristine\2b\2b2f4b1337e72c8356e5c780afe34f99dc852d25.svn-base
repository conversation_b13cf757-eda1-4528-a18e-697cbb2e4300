﻿Imports System.Data.SqlClient
Imports Stimulsoft.Report
Imports Stimulsoft.Report.Components

Public Class Zy_Rj_Cx
    Dim My_Date1 As String
    Dim My_Date2 As String
    Dim My_Dataset As New DataSet
    Dim My_Adapter As New SqlDataAdapter


    Dim V_Jz_Code As String

    Public My_Cm As CurrencyManager             '同步指针
    Dim My_View As New DataView                 '数据视图



    Private Sub Zy_Rj_Cx_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Panel1.BorderStyle = BorderStyle.None
        'Panel1.Height = 25
        BaseFunc.BaseFunc.Init_Datetimepicker(DateTimePicker1)
        BaseFunc.BaseFunc.Init_Datetimepicker(DateTimePicker2)
        CheckBox3.Checked = True
        CheckBox1.Checked = True
        CheckBox2.Checked = False
        RadioButton1.Checked = True

        DateTimePicker1.Value = Format(Now, "yyyy-MM-dd 00:00:00")
        DateTimePicker2.Value = Format(Now, "yyyy-MM-dd 23:59:59")
        Call Grid_Init()
        Call Init_Data()
    End Sub


    Private Sub Grid_Init()

        Dim My_Grid As New BaseClass.C_Grid(Me.C1TrueDBGrid1)
        With My_Grid
            .Init_Grid()
            .P_Dock(BaseClass.C_Grid.Dock.Fill)
            .Init_Column("结账编码", "Jz_Code", 0, "中", "")
            .Init_Column("结账日期", "Jz_Date", 80, "中", "yyyy-MM-dd")
            .Init_Column("结账时间", "Jz_Time", 80, "中", "")
            .Init_Column("出院人数", "Jz_Cy_Rs", 90, "中", "")
            .Init_Column("实收金额", "Jz_Sj_M", 80, "右", "")
            .Init_Column("上账金额", "Jz_Sz_M", 70, "右", "")
            .Init_Column("经手人", "Jsr_Name", 70, "中", "")
            .Init_Column("经手人编码", "Jsr_Code", 0, "中", "")
        End With
        C1TrueDBGrid1.Splits(0).DisplayColumns(7).Visible = False
        C1TrueDBGrid1.Splits(0).DisplayColumns(0).Visible = False



    End Sub

    Private Sub Init_Data()

        Dim V_Select As String = "Select Jz_Code,Jz_Date,Jz_Time,Jz_Cy_Rs,Jz_Sj_M,Jz_Sz_M,Bl_Jz.Jsr_Code,Jsr_Name From Bl_Jz,Zd_YyJsr Where Bl_Jz.Jsr_Code=Zd_YyJsr.Jsr_Code and Jz_Date+Jz_Time between'" & DateTimePicker1.Value & "' and '" & DateTimePicker2.Value & "'  And Bl_Jz.Yy_Code='" & HisVar.HisVar.WsyCode & "' Order By Jz_Code Asc"
        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, V_Select, "住院日结", True)

        Me.C1TrueDBGrid1.SetDataBinding(My_Dataset, "住院日结", True)
        My_Cm = CType(BindingContext(C1TrueDBGrid1.DataSource, C1TrueDBGrid1.DataMember), CurrencyManager)
        My_View = My_Cm.List

        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "Select Distinct Jsr_Name from bl_Jz,Zd_Yyjsr where Bl_Jz.Jsr_Code=Zd_Yyjsr.Jsr_Code and Jz_Date+Jz_Time between'" & DateTimePicker1.Value & "' and '" & DateTimePicker2.Value & "'  And Bl_Jz.Yy_Code='" & HisVar.HisVar.WsyCode & "'", "日结经手人", True)
        Dim My_Combo As New BaseClass.C_Combo1(Me.C1Combo1)
        My_Combo.Init_TDBCombo()
        With C1Combo1
            Dim Row As DataRow
            If My_Dataset.Tables("日结经手人").Rows.Count <> 0 Then
                .AddItem("所有人员")
                .SelectedIndex = 0
            Else
                .SelectedIndex = -1
            End If

            For Each Row In My_Dataset.Tables("日结经手人").Rows
                .AddItem(Row.Item("Jsr_Name"))
            Next


            .DropDownWidth = 100
            .Width = 87
        End With
        If My_Dataset.Tables("住院日结").Rows.Count = 0 Then

        Else
            C1Command3.Enabled = True
        End If

    End Sub

    Private Sub C1Command2_Click(ByVal sender As System.Object, ByVal e As C1.Win.C1Command.ClickEventArgs) Handles C1Command2.Click
        If C1TrueDBGrid1.RowCount = 0 Then Exit Sub

        V_Jz_Code = C1TrueDBGrid1.Columns("Jz_Code").Value
        '1 召回表中存在出院   2召回表中不存在出院
        ''1 发生过召回，且患者为不同操作员先后出院
        Dim Zh_Min_date As New Object
        Dim Zh_Max_date As New Object

        'and Bl_Zh.Jz_Code<>Bl.Jz_Code
        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "select distinct bl_zh.Bl_Code from bl_zh,bl where bl_zh.bl_code=Bl.bl_Code  and Bl_zh. Jz_code='" & V_Jz_Code & "' and Cy_Lb='出院'", "召回结账", True)

        Dim Zh_Row As DataRow
        Dim V_Str As String = ""
        For Each Zh_Row In My_Dataset.Tables("召回结账").Rows

            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "select top 1 Cy_Id,cy_date+cy_time as Zh_Cy_Date,Cy_Lb from Bl_Zh where bl_Code='" & Zh_Row.Item("Bl_Code") & "' and  Jz_code='" & V_Jz_Code & "' order by cy_id asc", "最小时间", True)
            If My_Dataset.Tables("最小时间").Rows(0).Item("cy_lb") = "召回" Then
                Zh_Min_date = My_Dataset.Tables("最小时间").Rows(0).Item("Zh_Cy_Date")
            Else
                Zh_Min_date = HisVar.HisVar.Sqldal.GetSingle("select top 1 cy_date+cy_time as Zh_Cy_Date from bl_zh where bl_code='" & Zh_Row.Item("Bl_Code") & "' and Cy_Lb='召回' and cy_id<'" & My_Dataset.Tables("最小时间").Rows(0).Item("Cy_Id") & "' order by cy_id desc")
            End If
            Zh_Max_date = HisVar.HisVar.Sqldal.GetSingle("select Top 1 cy_date+cy_time as Zh_Cy_Date from Bl_Zh where bl_Code='" & Zh_Row.Item("Bl_Code") & "' and  Jz_code='" & V_Jz_Code & "' and Cy_Lb='出院' order by Cy_Id desc")



            V_Str = V_Str & " or (Bl_Cf.Bl_Code='" & Zh_Row.Item("Bl_Code") & "' and convert(varchar(10),Cf_Date,126)+' '+Cf_Time<='" & Format(Zh_Max_date, "yyyy-MM-dd HH:mm:ss") & "' And convert(varchar(10),Cf_Date,126)+' '+Cf_Time>='" & Format(Zh_Min_date, "yyyy-MM-dd HH:mm:ss") & "') "
        Next


        '2 发生过召回，不管出没出过院,最小时间为第一个处方时间，故只需找出最大时间，即第一次非本次结账的召回时的出院时间

        Dim Zh_Max_date2 As New Object
        Dim Zh_Bl_Code2 As String = ""
        'and isnull(Bl_Zh.Jz_Code,1)<>Bl.Jz_Code
        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "select distinct bl_zh.Bl_Code from bl_zh,bl where bl_zh.bl_code=Bl.bl_Code  and Bl. Jz_code='" & V_Jz_Code & "'  ", "召回结账2", True)
        Dim Zh_Row2 As DataRow
        Dim V_Str2 As String = ""
        For Each Zh_Row2 In My_Dataset.Tables("召回结账2").Rows
            Zh_Bl_Code2 = Zh_Bl_Code2 + "," + "'" + Zh_Row2.Item("Bl_Code") + "'"
            Zh_Max_date2 = HisVar.HisVar.Sqldal.GetSingle("select top 1 Cy_Date+Cy_Time as Zh_Cy_Date from   Bl_Zh where bl_code= '" & Zh_Row2.Item("Bl_Code") & "' and Cy_Lb='召回'")

            V_Str2 = V_Str2 + " or (Bl_Cf.Bl_Code= '" & Zh_Row2.Item("Bl_Code") & "' and convert(varchar(10),Cf_Date,126)+' '+Cf_Time<='" & Format(Zh_Max_date2, "yyyy-MM-dd HH:mm:ss") & "' ) "
        Next

        Zh_Bl_Code2 = "(" + Mid(Zh_Bl_Code2, 2) + ")"
        Dim V_str3 As String = ""
        If V_Str2 = "" Then
            V_str3 = ")" & V_Str & " ) "
        Else
            V_str3 = " and Bl.Bl_Code not in " & Zh_Bl_Code2 & ")  " & V_Str2 & V_Str & " )"
        End If

        Dim Str As String = " select V_Lb,Bxlb_Name,Cf_Lb,V_Order,isnull(Cf_Money,0) Cf_Money from  (Select '上账费用'V_Lb,Bxlb_Name,Cf_Lb,'X'+Cf_lb as V_Order,Sum(Bl_CfYp.Cf_Money) As Cf_Money " & _
                            " From Bl,Zd_Bxlb,Bl_Cf,Bl_CfYp Where Bl.Bxlb_Code=Zd_Bxlb.Bxlb_Code and Bl.Bl_Code=Bl_Cf.Bl_Code and Bl_Cf.Cf_Code=Bl_CfYp.Cf_Code and Bl_Cf.jz_code='" & V_Jz_Code & "' Group By Bxlb_Name,Cf_Lb " & _
                            "Union all Select '出院费用'V_Lb,Bxlb_Name,Cf_Lb,'X'+Cf_lb as V_Order,Sum(Bl_CfYp.Cf_Money) As Cy_Money" & _
                            " From Bl,Zd_Bxlb,Bl_Cf,Bl_CfYp Where Bl.Bxlb_Code=Zd_Bxlb.Bxlb_Code and Bl.Bl_Code=Bl_Cf.Bl_Code and Bl_Cf.Cf_Code=Bl_CfYp.Cf_Code  and (( bl.Jz_code='" & V_Jz_Code & "' " & V_str3 & " Group By Bxlb_Name,Cf_Lb" & _
                            " UNION all Select '上账费用'V_Lb,Bxlb_Name,Cf_Lb,'Y'+Cf_lb as V_Order,Sum(Bl_CfXm.Cf_Money) As Cf_Money " & _
                            " From Bl,Zd_Bxlb,Bl_Cf,Bl_CfXm Where Bl.Bxlb_Code=Zd_Bxlb.Bxlb_Code and Bl.Bl_Code=Bl_Cf.Bl_Code and Bl_Cf.Cf_Code=Bl_CfXm.Cf_Code and Bl_Cf.jz_code='" & V_Jz_Code & "' Group By Bxlb_Name,Cf_Lb " & _
                            "Union all Select '出院费用'V_Lb,Bxlb_Name,Cf_Lb,'Y'+Cf_lb as V_Order,Sum(Bl_CfXm.Cf_Money) As Cy_Money " & _
                             " From Bl,Zd_Bxlb,Bl_Cf,Bl_CfXm Where Bl.Bxlb_Code=Zd_Bxlb.Bxlb_Code and Bl.Bl_Code=Bl_Cf.Bl_Code and Bl_Cf.Cf_Code=Bl_CfXm.Cf_Code and  ((bl.Jz_code='" & V_Jz_Code & "' " & V_str3 & " Group By Bxlb_Name, Cf_Lb" & _
                            ")a  Order By V_Order"



        Dim Str1 As String = "select Sum(Isnull(Jf_XjMoney,0))Jf_XjMoney,Sum(Isnull(Jf_JkkMoney,0))Jf_JkkMoney,Sum(Bl_M_Th) Bl_M_Th, Sum(Isnull(Jf_XjMoney,0))+Sum(Isnull(Jf_JkkMoney,0))-Sum(Bl_M_Th) as Sk_Money,sum(Cy_Rc)Cy_Rc,Bxlb_Name from " & _
           "(select Case When Isnull(Bl_Jffs,'现金')='现金' then Jf_Money end as Jf_XjMoney,Case When Isnull(Bl_Jffs,'现金')='健康卡' then Jf_Money end as Jf_JkkMoney,0 as Bl_M_Th,Bxlb_Name,0 as Cy_Rc from bl_jf,Bl,Zd_Bxlb where Bl_Jf.Bl_Code=Bl.Bl_Code and Bl.bxlb_Code=Zd_Bxlb.Bxlb_Code and bl_jf.jz_code='" & V_Jz_Code & "' " & _
           "union all  select 0 as Jf_XjMoney,0 as Jf_JkkMoney,Sum(Bl_M_Th)Bl_M_Th,Bxlb_Name,Count(*)Cy_Rc from bl,Zd_Bxlb where Bl.Bxlb_Code=Zd_Bxlb.Bxlb_Code and jz_code='" & V_Jz_Code & "' group by Bxlb_Name " & _
           "union all  select 0 as Jf_XjMoney,0 as Jf_JkkMoney,-Sum(bl_zh.Bl_M_Th)  Bl_M_Th,Bxlb_Name,Count(*) as Cy_Rc from bl_zh,Bl,Zd_Bxlb where Bl_Zh.Bl_Code=Bl.Bl_Code and Bl.Bxlb_Code=Zd_Bxlb.Bxlb_Code and Cy_Lb='出院'  and Bl.Jz_Code<>Bl_zh.Jz_Code and Bl_Zh.jz_Code='" & V_Jz_Code & "' group by Bxlb_Name " & _
           "union all  select 0 as Jf_XjMoney,0 as Jf_JkkMoney,bl_zh.Bl_M_Th Bl_M_Th,Bxlb_Name,0 as Cy_Rc from bl_zh,Bl,Zd_Bxlb  where Bl_Zh.Bl_Code=Bl.Bl_Code and Bl.Bxlb_Code=Zd_Bxlb.Bxlb_Code and Cy_Lb='出院'  and Bl.Jz_Code<>Bl_zh.Jz_Code and Bl.jZ_Code='" & V_Jz_Code & "' and Bl_Zh.Jz_Code>Bl.Jz_Code)A  Group by Bxlb_Name"


        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, Str, "住院日结单", True)
        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, Str1, "住院分类汇总", True)

        Dim Stirpt As New StiReport
        Stirpt.RegData(My_Dataset.Tables("住院日结单"))
        Stirpt.RegData(My_Dataset.Tables("住院分类汇总"))
        Stirpt.Load(".\Rpt\住院日结.mrt")
        Stirpt.ReportName = "住院日结"
        Stirpt.Compile()
        Stirpt("标题") = "住 院 日 结 单(重打)(" & HisVar.HisVar.JsrName & ")"
        Stirpt("打印时间") = "打印时间:" & Format(Now, "yyyy-MM-dd HH:mm:ss")

        Dim My_Reader As SqlDataReader
        My_Reader = HisVar.HisVar.Sqldal.ExecuteReader("Select Top 1 * From Bl_Jz Where Jz_Code<'" & C1TrueDBGrid1.Columns("Jz_Code").Value & "' And Bl_Jz.Jsr_Code='" & C1TrueDBGrid1.Columns("Jsr_Code").Value & "' Order By Jz_Code desc")
        My_Reader.Read()

        If My_Reader.HasRows = True Then
            Stirpt("汇总时间段") = "结算时间段：" + Format(My_Reader.Item("Jz_Date"), "yyyy-MM-dd") + " " + My_Reader.Item("Jz_Time") + " 至" + Format(C1TrueDBGrid1.Columns("Jz_Date").Value, "yyyy-MM-dd") + " " + C1TrueDBGrid1.Columns("Jz_Time").Value
        Else
            Stirpt("汇总时间段") = "结算时间段：无" + " 至" + Format(C1TrueDBGrid1.Columns("Jz_Date").Value, "yyyy-MM-dd") + " " + C1TrueDBGrid1.Columns("Jz_Time").Value
        End If
        My_Reader.Close()


        ' Stirpt.Design()
        Stirpt.Show()

    End Sub


    Private Sub C1Command1_Click(ByVal sender As System.Object, ByVal e As C1.Win.C1Command.ClickEventArgs) Handles C1Command1.Click

        C1TrueDBGrid1.Select()
        Call Init_Data()
        Call F_Sum()

    End Sub


    Private Sub C1Command3_Click(ByVal sender As System.Object, ByVal e As C1.Win.C1Command.ClickEventArgs) Handles C1Command3.Click
        'If C1TrueDBGrid1.RowCount = 0 Then Exit Sub
        Dim Code As Object = HisVar.HisVar.Sqldal.GetSingle("Select Jsr_Code from Zd_Yyjsr where Jsr_Name='" & C1Combo1.Text & "'")
        If Code Is Nothing Then Code = ""

        Dim Str1 As String = "Select Jz_Code,Jz_Date+Jz_Time as Jz_Date,Jsr_Name,Jz_Sz_M,Jz_Sq_M,Jz_Th_M,Jz_Sj_M,Jz_Cy_Rs,isnull(Jz_Cy_M,0)Jz_Cy_M From Bl_Jz,Zd_YyJsr Where Bl_Jz.Jsr_Code=Zd_YyJsr.Jsr_Code and Jz_Date+Jz_Time between'" & DateTimePicker1.Value & "' and '" & DateTimePicker2.Value & "'  And Bl_Jz.Yy_Code='" & HisVar.HisVar.WsyCode & "' and  Bl_Jz.Jsr_Code like '%" & Code & "%' order by  Bl_Jz.Jsr_Code"
        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, Str1, "日结", True)

        Dim Code_Row As DataRow
        Dim Zh_Min_date As New Object
        Dim Zh_Max_date As New Object
        Dim Zh_Row As DataRow
        Dim V_Str As String = ""
        Dim Zh_Max_date2 As New Object
        Dim Zh_Bl_Code2 As String = ""
        Dim Zh_Row2 As DataRow
        Dim V_Str2 As String = ""
        For Each Code_Row In My_Dataset.Tables("日结").Rows

            '1 召回表中存在出院   2召回表中不存在出院
            ''1 发生过召回，且患者为不同操作员先后出院
            ' and Bl_Zh.Jz_Code<>Bl.Jz_Code
            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "select distinct bl_zh.Bl_Code from bl_zh,bl where bl_zh.bl_code=Bl.bl_Code  and Bl_zh. Jz_code='" & Code_Row.Item("Jz_Code") & "' and Cy_Lb='出院'", "召回结账", True)


            For Each Zh_Row In My_Dataset.Tables("召回结账").Rows

                HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "select top 1 Cy_Id,cy_date+cy_time as Zh_Cy_Date,Cy_Lb from Bl_Zh where bl_Code='" & Zh_Row.Item("Bl_Code") & "' and  Jz_code='" & Code_Row.Item("Jz_Code") & "' order by cy_id asc", "最小时间", True)
                If My_Dataset.Tables("最小时间").Rows(0).Item("cy_lb") = "召回" Then
                    Zh_Min_date = My_Dataset.Tables("最小时间").Rows(0).Item("Zh_Cy_Date")
                Else
                    Zh_Min_date = HisVar.HisVar.Sqldal.GetSingle("select top 1 cy_date+cy_time as Zh_Cy_Date from bl_zh where bl_code='" & Zh_Row.Item("Bl_Code") & "' and Cy_Lb='召回' and cy_id<'" & My_Dataset.Tables("最小时间").Rows(0).Item("Cy_Id") & "' order by cy_id desc")
                End If
                Zh_Max_date = HisVar.HisVar.Sqldal.GetSingle("select Top 1 cy_date+cy_time as Zh_Cy_Date from Bl_Zh where bl_Code='" & Zh_Row.Item("Bl_Code") & "' and  Jz_code='" & Code_Row.Item("Jz_Code") & "' and Cy_Lb='出院' order by Cy_Id desc")



                V_Str = V_Str & " or (Bl_Cf.Bl_Code='" & Zh_Row.Item("Bl_Code") & "' and convert(varchar(10),Cf_Date,126)+' '+Cf_Time<='" & Format(Zh_Max_date, "yyyy-MM-dd HH:mm:ss") & "' And convert(varchar(10),Cf_Date,126)+' '+Cf_Time>='" & Format(Zh_Min_date, "yyyy-MM-dd HH:mm:ss") & "') "
            Next


            '2 发生过召回，不管出没出过院,最小时间为第一个处方时间，故只需找出最大时间，即第一次非本次结账的召回时的出院时间

            'and isnull(Bl_Zh.Jz_Code,1)<>Bl.Jz_Code

            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "select distinct bl_zh.Bl_Code from bl_zh,bl where bl_zh.bl_code=Bl.bl_Code  and Bl. Jz_code='" & Code_Row.Item("Jz_Code") & "' ", "召回结账2", True)

            For Each Zh_Row2 In My_Dataset.Tables("召回结账2").Rows
                Zh_Bl_Code2 = Zh_Bl_Code2 + "," + "'" + Zh_Row2.Item("Bl_Code") + "'"
                Zh_Max_date2 = HisVar.HisVar.Sqldal.GetSingle("select top 1 Cy_Date+Cy_Time as Zh_Cy_Date from   Bl_Zh where bl_code= '" & Zh_Row2.Item("Bl_Code") & "' and Cy_Lb='召回'")

                V_Str2 = V_Str2 + " or (Bl_Cf.Bl_Code= '" & Zh_Row2.Item("Bl_Code") & "' and convert(varchar(10),Cf_Date,126)+' '+Cf_Time<='" & Format(Zh_Max_date2, "yyyy-MM-dd HH:mm:ss") & "' ) "
            Next

        Next




        Zh_Bl_Code2 = "(" + Mid(Zh_Bl_Code2, 2) + ")"
        Dim V_str3 As String = ""
        If V_Str2 = "" Then
            V_str3 = ")" & V_Str & " ) "
        Else
            V_str3 = " and Bl.Bl_Code not in " & Zh_Bl_Code2 & ")  " & V_Str2 & V_Str & " )"
        End If


        Dim Str As String = " select V_Lb,Bxlb_Name,Cf_Lb,V_Order,isnull(Cf_Money,0) Cf_Money from  (Select '上账费用'V_Lb,Bxlb_Name,Cf_Lb,'X'+Cf_lb as V_Order,Sum(Bl_CfYp.Cf_Money) As Cf_Money " & _
                           " From Bl,Zd_Bxlb,Bl_Cf,Bl_CfYp Where Bl.Bxlb_Code=Zd_Bxlb.Bxlb_Code and Bl.Bl_Code=Bl_Cf.Bl_Code and Bl_Cf.Cf_Code=Bl_CfYp.Cf_Code and Bl_Cf.jz_code in (select Jz_Code from Bl_Jz where Jz_Date+Jz_Time between '" & DateTimePicker1.Value & "' and '" & DateTimePicker2.Value & "' and Yy_Code='" & HisVar.HisVar.WsyCode & "' and Jsr_Code like '%" & Code & "%') Group By Bxlb_Name,Cf_Lb " & _
                           "Union all Select '出院费用'V_Lb,Bxlb_Name,Cf_Lb,'X'+Cf_lb as V_Order,Sum(Bl_CfYp.Cf_Money) As Cy_Money" & _
                           " From Bl,Zd_Bxlb,Bl_Cf,Bl_CfYp Where Bl.Bxlb_Code=Zd_Bxlb.Bxlb_Code and Bl.Bl_Code=Bl_Cf.Bl_Code and Bl_Cf.Cf_Code=Bl_CfYp.Cf_Code  and (( bl.Jz_code in (select Jz_Code from Bl_Jz where Jz_Date+Jz_Time between '" & DateTimePicker1.Value & "' and '" & DateTimePicker2.Value & "' and Yy_Code='" & HisVar.HisVar.WsyCode & "' and Jsr_Code like '%" & Code & "%') " & V_str3 & " Group By Bxlb_Name,Cf_Lb" & _
                           " UNION all Select '上账费用'V_Lb,Bxlb_Name,Cf_Lb,'Y'+Cf_lb as V_Order,Sum(Bl_CfXm.Cf_Money) As Cf_Money " & _
                           " From Bl,Zd_Bxlb,Bl_Cf,Bl_CfXm Where Bl.Bxlb_Code=Zd_Bxlb.Bxlb_Code and Bl.Bl_Code=Bl_Cf.Bl_Code and Bl_Cf.Cf_Code=Bl_CfXm.Cf_Code and Bl_Cf.jz_code in (select Jz_Code from Bl_Jz where Jz_Date+Jz_Time between '" & DateTimePicker1.Value & "' and '" & DateTimePicker2.Value & "' and Yy_Code='" & HisVar.HisVar.WsyCode & "' and Jsr_Code like '%" & Code & "%') Group By Bxlb_Name,Cf_Lb " & _
                           "Union all Select '出院费用'V_Lb,Bxlb_Name,Cf_Lb,'Y'+Cf_lb as V_Order,Sum(Bl_CfXm.Cf_Money) As Cy_Money " & _
                            " From Bl,Zd_Bxlb,Bl_Cf,Bl_CfXm Where Bl.Bxlb_Code=Zd_Bxlb.Bxlb_Code and Bl.Bl_Code=Bl_Cf.Bl_Code and Bl_Cf.Cf_Code=Bl_CfXm.Cf_Code and  ((bl.Jz_code in (select Jz_Code from Bl_Jz where Jz_Date+Jz_Time between '" & DateTimePicker1.Value & "' and '" & DateTimePicker2.Value & "' and Yy_Code='" & HisVar.HisVar.WsyCode & "' and Jsr_Code like '%" & Code & "%') " & V_str3 & " Group By Bxlb_Name, Cf_Lb" & _
                           ")a  Order By Cf_Lb"

        'Dim Str2 As String = "select sum(Jf_Money)Jf_Money,Sum(Bl_M_Th) Bl_M_Th, sum(Jf_Money)-Sum(Bl_M_Th) as Sk_Money,sum(Cy_Rc)Cy_Rc,Bxlb_Name from " & _
        '     "(select Jf_Money,0 as Bl_M_Th,Bxlb_Name,0 as Cy_Rc from bl_jf,Bl,Zd_Bxlb where Bl_Jf.Bl_Code=Bl.Bl_Code and Bl.bxlb_Code=Zd_Bxlb.Bxlb_Code and bl_jf.jz_code in (select Jz_Code from Bl_Jz where Jz_Date+Jz_Time between '" & DateTimePicker1.Value & "' and '" & DateTimePicker2.Value & "' and Yy_Code='" & HisVar.HisVar.WsyCode & "' and Jsr_Code like '%" & Code & "%') " & _
        '     "union all  select 0 as Jf_Money,Sum(Bl_M_Th)Bl_M_Th,Bxlb_Name,Count(*)Cy_Rc from bl,Zd_Bxlb where Bl.Bxlb_Code=Zd_Bxlb.Bxlb_Code and jz_code in (select Jz_Code from Bl_Jz where Jz_Date+Jz_Time between '" & DateTimePicker1.Value & "' and '" & DateTimePicker2.Value & "' and Yy_Code='" & HisVar.HisVar.WsyCode & "' and Jsr_Code like '%" & Code & "%') group by Bxlb_Name " & _
        '     "union all  select 0 as Jf_Money,-Sum(bl_zh.Bl_M_Th)  Bl_M_Th,Bxlb_Name,Count(*) as Cy_Rc from bl_zh,Bl,Zd_Bxlb where Bl_Zh.Bl_Code=Bl.Bl_Code and Bl.Bxlb_Code=Zd_Bxlb.Bxlb_Code and Cy_Lb='出院'  and Bl.Jz_Code<>Bl_zh.Jz_Code and Bl_Zh.jz_Code in (select Jz_Code from Bl_Jz where Jz_Date+Jz_Time between '" & DateTimePicker1.Value & "' and '" & DateTimePicker2.Value & "' and Yy_Code='" & HisVar.HisVar.WsyCode & "' and Jsr_Code like '%" & Code & "%') group by Bxlb_Name " & _
        '     "union all  select 0 as Jf_Money,bl_zh.Bl_M_Th Bl_M_Th,Bxlb_Name,0 as Cy_Rc from bl_zh,Bl,Zd_Bxlb  where Bl_Zh.Bl_Code=Bl.Bl_Code and Bl.Bxlb_Code=Zd_Bxlb.Bxlb_Code and Cy_Lb='出院'  and Bl.Jz_Code<>Bl_zh.Jz_Code and Bl.jZ_Code in (select Jz_Code from Bl_Jz where Jz_Date+Jz_Time between '" & DateTimePicker1.Value & "' and '" & DateTimePicker2.Value & "' and Yy_Code='" & HisVar.HisVar.WsyCode & "' and Jsr_Code like '%" & Code & "%') and Bl_Zh.Jz_Code>Bl.Jz_Code)A  Group by Bxlb_Name"
        Dim Str2 As String = "select Sum(Isnull(Jf_XjMoney,0))Jf_XjMoney,Sum(Isnull(Jf_JkkMoney,0))Jf_JkkMoney,Sum(Bl_M_Th) Bl_M_Th, Sum(Isnull(Jf_XjMoney,0))+Sum(Isnull(Jf_JkkMoney,0))-Sum(Bl_M_Th) as Sk_Money,sum(Cy_Rc)Cy_Rc,Bxlb_Name from " & _
           "(select Case When Isnull(Bl_Jffs,'现金')='现金' then Jf_Money end as Jf_XjMoney,Case When Isnull(Bl_Jffs,'现金')='健康卡' then Jf_Money end as Jf_JkkMoney,0 as Bl_M_Th,Bxlb_Name,0 as Cy_Rc from bl_jf,Bl,Zd_Bxlb where Bl_Jf.Bl_Code=Bl.Bl_Code and Bl.bxlb_Code=Zd_Bxlb.Bxlb_Code and bl_jf.jz_code in (select Jz_Code from Bl_Jz where Jz_Date+Jz_Time between '" & DateTimePicker1.Value & "' and '" & DateTimePicker2.Value & "' and Yy_Code='" & HisVar.HisVar.WsyCode & "' and Jsr_Code like '%" & Code & "%')  " & _
           "union all  select 0 as Jf_XjMoney,0 as Jf_JkkMoney,Sum(Bl_M_Th)Bl_M_Th,Bxlb_Name,Count(*)Cy_Rc from bl,Zd_Bxlb where Bl.Bxlb_Code=Zd_Bxlb.Bxlb_Code and jz_code in (select Jz_Code from Bl_Jz where Jz_Date+Jz_Time between '" & DateTimePicker1.Value & "' and '" & DateTimePicker2.Value & "' and Yy_Code='" & HisVar.HisVar.WsyCode & "' and Jsr_Code like '%" & Code & "%') group by Bxlb_Name " & _
           "union all  select 0 as Jf_XjMoney,0 as Jf_JkkMoney,-Sum(bl_zh.Bl_M_Th)  Bl_M_Th,Bxlb_Name,Count(*) as Cy_Rc from bl_zh,Bl,Zd_Bxlb where Bl_Zh.Bl_Code=Bl.Bl_Code and Bl.Bxlb_Code=Zd_Bxlb.Bxlb_Code and Cy_Lb='出院'  and Bl.Jz_Code<>Bl_zh.Jz_Code and Bl_Zh.jz_Code in (select Jz_Code from Bl_Jz where Jz_Date+Jz_Time between '" & DateTimePicker1.Value & "' and '" & DateTimePicker2.Value & "' and Yy_Code='" & HisVar.HisVar.WsyCode & "' and Jsr_Code like '%" & Code & "%') group by Bxlb_Name " & _
           "union all  select 0 as Jf_XjMoney,0 as Jf_JkkMoney,bl_zh.Bl_M_Th Bl_M_Th,Bxlb_Name,0 as Cy_Rc from bl_zh,Bl,Zd_Bxlb  where Bl_Zh.Bl_Code=Bl.Bl_Code and Bl.Bxlb_Code=Zd_Bxlb.Bxlb_Code and Cy_Lb='出院'  and Bl.Jz_Code<>Bl_zh.Jz_Code and Bl.jZ_Code in (select Jz_Code from Bl_Jz where Jz_Date+Jz_Time between '" & DateTimePicker1.Value & "' and '" & DateTimePicker2.Value & "' and Yy_Code='" & HisVar.HisVar.WsyCode & "' and Jsr_Code like '%" & Code & "%') and Bl_Zh.Jz_Code>Bl.Jz_Code)A  Group by Bxlb_Name"

        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, Str, "住院日结单", True)
        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, Str2, "住院分类汇总", True)


        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "select Ry_Name,Ry_Sex,Ks_Name,Jf_Date,Jf_Money,Jsr_Name from Bl,Bl_Jf,Zd_YyKs,Zd_YyJsr,Bl_Jz where Bl_Jf.Jsr_Code=Zd_YyJsr.Jsr_Code and Bl.Bl_Code=Bl_Jf.Bl_Code and Bl.Ks_Code=Zd_YyKs.Ks_Code  and Bl_Jf.Jz_code=Bl_Jz.Jz_Code and  Jz_Date+Jz_Time between '" & DateTimePicker1.Value & "' and '" & DateTimePicker2.Value & "'  and Bl_Jf.Jsr_Code like '%" & Code & "%' ", "结账患者押金", True)

        Dim V_Newrow As DataRow

        If My_Dataset.Tables("结账患者押金").Rows.Count Mod 2 <> 0 Then
            V_Newrow = My_Dataset.Tables("结账患者押金").NewRow
            V_Newrow.Item("Ry_Name") = DBNull.Value
            V_Newrow.Item("Ry_Sex") = DBNull.Value
            V_Newrow.Item("Ks_Name") = DBNull.Value
            V_Newrow.Item("Jf_Date") = DBNull.Value
            V_Newrow.Item("Jf_Money") = DBNull.Value
            V_Newrow.Item("Jsr_Name") = DBNull.Value
            My_Dataset.Tables("结账患者押金").Rows.Add(V_Newrow)
            V_Newrow.AcceptChanges()
        End If
        Call load_SubRpt()

    End Sub

    Private Sub load_SubRpt()
        Try
            Dim WithSubReport As Boolean = True

            Dim report As StiReport = New StiReport
            report.Load(".\Rpt\子报表母表.mrt")

            If CheckBox5.Checked = True Then
                '横打设置
                report.Pages(0).Orientation = StiPageOrientation.Landscape
            End If


            Dim Sub1_H As Integer = 0
            Dim Sub2_H As Integer = 0
            Dim Sub3_H As Integer = 0
            'Creat Subreport 
            If WithSubReport Then
                'SubReport1
                If CheckBox3.Checked = True Then
                    Dim I As Integer
                    Dim M As Integer = My_Dataset.Tables("住院日结单").Rows.Count - 1
                    Dim Q As Integer = 0
                    Dim Str1 As String
                    Dim Row1 As DataRow
                    Dim Row2 As DataRow
                    For I = 0 To M
                        Row1 = My_Dataset.Tables("住院日结单").Rows(I)
                        If I = M Then
                            Str1 = True
                        Else
                            Row2 = My_Dataset.Tables("住院日结单").Rows(I + 1)
                            Str1 = Row1.Item("Cf_Lb") <> Row2.Item("Cf_Lb")
                        End If
                        If Str1 Then
                            Q = Q + 1 '行数
                        End If
                    Next
                    Sub1_H = 3.1 + 2 + Q * 0.5 + 0.6 * My_Dataset.Tables("住院分类汇总").Rows.Count + 2.3
                    Dim SubReport1 As Stimulsoft.Report.Components.StiSubReport = New Stimulsoft.Report.Components.StiSubReport()
                    SubReport1.ClientRectangle = New Stimulsoft.Base.Drawing.RectangleD(0, 0, report.Pages(0).Width, Sub1_H + 2)
                    SubReport1.Name = "SubReport1"
                    SubReport1.UseExternalReport = True
                    report.Pages(0).Components.AddRange(New Stimulsoft.Report.Components.StiComponent() {SubReport1})
                End If

                'SubReport2
                If CheckBox1.Checked = True Then
                    Dim Sub2_Dt As DataTable = My_Dataset.Tables("日结").DefaultView.ToTable(True, "Jsr_Name")
                    Sub2_H = 2.4 + 0.5 + 0.5 * IIf(Sub2_Dt.Compute("Count(Jsr_Name)", "") Is DBNull.Value, 0, Sub2_Dt.Compute("Count(Jsr_Name)", ""))
                    Dim SubReport2 As Stimulsoft.Report.Components.StiSubReport = New Stimulsoft.Report.Components.StiSubReport()
                    SubReport2.ClientRectangle = New Stimulsoft.Base.Drawing.RectangleD(1, Sub1_H + 2, 19, Sub2_H + 0.5)
                    SubReport2.Name = "SubReport2"
                    SubReport2.UseExternalReport = True
                    report.Pages(0).Components.AddRange(New Stimulsoft.Report.Components.StiComponent() {SubReport2})
                End If

                'SubReport3
                If CheckBox2.Checked = True Then
                    Sub3_H = 2.8 + My_Dataset.Tables("结账患者押金").Rows.Count / 2 * 0.8
                    Dim SubReport3 As Stimulsoft.Report.Components.StiSubReport = New Stimulsoft.Report.Components.StiSubReport()
                    SubReport3.ClientRectangle = New Stimulsoft.Base.Drawing.RectangleD(1, Sub1_H + Sub2_H + 2, 19, Sub3_H + 1)
                    SubReport3.Name = "SubReport3"
                    SubReport3.UseExternalReport = True
                    report.Pages(0).Components.AddRange(New Stimulsoft.Report.Components.StiComponent() {SubReport3})
                End If

                AddHandler report.GetSubReport, AddressOf report_GetSubReport
            End If

            If RadioButton1.Checked = True Then
                report.Pages(0).UnlimitedHeight = True
                report.Pages(0).UnlimitedBreakable = False
            Else
                Dim P_H As Integer = Sub1_H + Sub2_H + 2 + Sub3_H + 3
                Dim prt As New System.Drawing.Printing.PrinterSettings
                If prt.DefaultPageSettings.PrinterSettings.PrinterName.Contains("Epson LQ-1600K") Then
                    If P_H > 231 Then
                        MsgBox("数据量过大，请从新选择打印范围", MsgBoxStyle.Information, "提示：")
                        Exit Sub

                    End If
                End If
                report.Pages(0).PaperSize = Printing.PaperKind.Custom
                report.Pages(0).PageHeight = P_H
            End If

            report.Render()
            report.Show()
        Catch ex As Exception
            MessageBox.Show(ex.Message)
        End Try
    End Sub

    Private Sub report_GetSubReport(ByVal sender As Object, ByVal e As StiGetSubReportEventArgs)
        Dim subReport As StiReport = New StiReport()

        If e.SubReportName = "SubReport1" Then
            If CheckBox5.Checked = True Then
                subReport.Load(".\Rpt\住院日结横打.mrt")
            Else
                subReport.Load(".\Rpt\住院日结.mrt")
            End If
            subReport.RegData(My_Dataset.Tables("住院日结单"))
            subReport.RegData(My_Dataset.Tables("住院分类汇总"))
            subReport.Pages(0).Orientation = CType(sender, StiReport).Pages(0).Orientation
            subReport.Compile()
            subReport.Pages(0).GetComponents.Item("CrossTab2").Width = subReport.Pages(0).Width
            subReport("标题") = "住 院 日 结 单(汇总)(" & C1Combo1.Text & ")"
            subReport("打印时间") = "打印时间:" & Format(Now, "yyyy-MM-dd HH:mm:ss")
            subReport("汇总时间段") = "汇总时间段：" + DateTimePicker1.Value + " 至" + DateTimePicker2.Value
        End If

        If e.SubReportName = "SubReport2" Then
            subReport.Load(".\Rpt\住院日结操作员现金统计.mrt")
            subReport.RegData(My_Dataset.Tables("日结"))
            subReport.Compile()
            subReport("标题") = "住院日结操作员现金统计"
            subReport("制单人") = "制单人:" & HisVar.HisVar.JsrName
            subReport("打印时间") = "打印时间:" & Format(Now, "yyyy-MM-dd HH:mm:ss")
            subReport("汇总时间段") = "汇总时间段：" + DateTimePicker1.Value + " 至" + DateTimePicker2.Value
        End If

        If e.SubReportName = "SubReport3" Then
            subReport.Load(".\Rpt\住院日结患者押金.mrt")
            subReport.RegData(My_Dataset.Tables("结账患者押金"))
            subReport.Pages(0).UnlimitedHeight = True
            subReport.Pages(0).UnlimitedBreakable = False
            subReport.Compile()
            subReport("标题") = HisVar.HisVar.WsyName & "结账押金统计"
            subReport("制单人") = "制单人:" & HisVar.HisVar.JsrName
            subReport("打印时间") = "打印时间:" & Format(Now, "yyyy-MM-dd HH:mm:ss")
            subReport("押金合计") = "押金合计：" & IIf(My_Dataset.Tables("结账患者押金").Compute("Sum(Jf_Money)", "") Is DBNull.Value, 0, My_Dataset.Tables("结账患者押金").Compute("Sum(Jf_Money)", ""))
            subReport("汇总时间段") = "汇总时间段：" + DateTimePicker1.Value + " 至" + DateTimePicker2.Value
        End If

        e.Report = subReport
    End Sub



    Private Sub C1Combo1_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1Combo1.TextChanged
        If C1Combo1.Text = "" Or C1Combo1.Text = "所有人员" Then
            My_View.RowFilter = ""

        Else
            My_View.RowFilter = "Jsr_Name='" & C1Combo1.Text & "'"
        End If
        Call F_Sum()
    End Sub

    Public Overrides Sub F_Sum()
        Dim Sum1 As Double = 0
        Dim Sum2 As Double = 0
        Dim Sum3 As Double = 0
        Sum1 = IIf(My_View.Table.Compute("Sum(Jz_Cy_Rs)", My_View.RowFilter) Is DBNull.Value, 0, My_View.Table.Compute("Sum(Jz_Cy_Rs)", My_View.RowFilter))
        Sum2 = IIf(My_View.Table.Compute("Sum(Jz_Sj_M)", My_View.RowFilter) Is DBNull.Value, 0, My_View.Table.Compute("Sum(Jz_Sj_M)", My_View.RowFilter))
        Sum3 = IIf(My_View.Table.Compute("Sum(Jz_Sz_M)", My_View.RowFilter) Is DBNull.Value, 0, My_View.Table.Compute("Sum(Jz_Sz_M)", My_View.RowFilter))
        With C1TrueDBGrid1
            .ColumnFooters = True
            .Columns(3).FooterText = Format(Sum1, "###0")
            .Columns(4).FooterText = Format(Sum2, "###0.00")
            .Columns(5).FooterText = Format(Sum3, "###0.00")
        End With



    End Sub

    Private Sub DateTimePicker1_ValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles DateTimePicker1.ValueChanged, DateTimePicker2.ValueChanged
        C1Command3.Enabled = False
    End Sub
End Class