﻿/**  版本信息模板在安装目录下，可自行修改。
* M_Zd_YyBq1.cs
*
* 功 能： N/A
* 类 名： M_Zd_YyBq1
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/8/29 1:45:27   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace ModelOld
{
	/// <summary>
	/// M_Zd_YyBq1:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class M_Zd_YyBq1
	{
		public M_Zd_YyBq1()
		{}
		#region Model
		private string _yy_code;
		private string _bq_code;
		private string _bq_name;
		private string _bq_jc;
		private string _bq_memo;
		/// <summary>
		/// 
		/// </summary>
		public string Yy_Code
		{
			set{ _yy_code=value;}
			get{return _yy_code;}
		}
		/// <summary>
		/// 病区编码
		/// </summary>
		public string Bq_Code
		{
			set{ _bq_code=value;}
			get{return _bq_code;}
		}
		/// <summary>
		/// 病区名称
		/// </summary>
		public string Bq_Name
		{
			set{ _bq_name=value;}
			get{return _bq_name;}
		}
		/// <summary>
		/// 简称
		/// </summary>
		public string Bq_Jc
		{
			set{ _bq_jc=value;}
			get{return _bq_jc;}
		}
		/// <summary>
		/// 备注
		/// </summary>
		public string Bq_Memo
		{
			set{ _bq_memo=value;}
			get{return _bq_memo;}
		}
		#endregion Model

	}
}

