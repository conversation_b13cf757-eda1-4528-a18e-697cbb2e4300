﻿Imports System.Data.SqlClient
Imports HisControl

Public Class Yk_Tj2

#Region "定义__变量"
    Dim My_Adapter As New SqlDataAdapter                '从表适配器
    Dim My_Table As New DataTable                       '从表

    Dim V_GridCol As Integer                            '当前TDBGrid所在的列

    Public Cb_Cm As CurrencyManager                     '同步指针
    Public Cb_Row As DataRow                            '当前选择行
    Public V_Tj_Code As String                          '出库编码
    Public V_Insert As Boolean                          '增加记录

    Public Ck3_FirstLoad As Boolean                     '第一次调入明细表

    Dim Str_Select As String
    Public Yp_Lb As String
    Dim My_Dataset As New DataSet
#End Region

#Region "传参"
    Dim Rinsert As Boolean
    Dim Rdate As Date
    Dim Rrow As DataRow
    Dim RZbtb As DataTable
    Dim Rtdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Dim Rlb As C1.Win.C1Input.C1Label
    Dim Rzbadt As SqlDataAdapter
    Dim Rlx As String
#End Region

    Public Sub New(ByVal tform As BaseForm, ByVal tinsert As Boolean, ByVal tdate As Date, ByVal trow As DataRow, ByVal tZbtb As DataTable, ByVal ttdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid, ByVal tlb As C1.Win.C1Input.C1Label, ByVal tzbadt As SqlDataAdapter, ByVal tlx As String)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        Rinsert = tinsert
        Rdate = tdate
        Rrow = trow
        RZbtb = tZbtb
        Rtdbgrid = ttdbgrid
        Rlb = tlb
        Rzbadt = tzbadt
        Rlx = tlx
        ' 在 InitializeComponent() 调用之后添加任何初始化。

    End Sub

    Protected Overrides Sub OnKeyDown(ByVal e As System.Windows.Forms.KeyEventArgs)
        MyBase.OnKeyDown(e)
        If e.Control = True And e.KeyCode = Keys.S Then
            Comm1.Select()
            Call Comm_Click(Comm1, Nothing)
        End If
    End Sub

    Private Sub Yk_Tj2_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Panel2.Height = 30

        '按扭初始化
        Call P_Comm(Me.Comm1)
        Call P_Comm(Me.Comm2)


        C1Command1.Enabled = False
        C1Command2.Enabled = False
        Call Init_Data()                '数据初始化


        If Rinsert = True Then           '主表
            Call Zb_Clear()                 '清空数据
        Else
            Call Zb_Show()                  '显示数据
        End If

    End Sub

#Region "窗体__事件"


    Private Sub Yk_Tj2_FormClosing(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosingEventArgs) Handles Me.FormClosing
        If e.CloseReason = CloseReason.UserClosing Then
            If Rinsert = False Then Call Zb_Save() '更新主表(主表已经保存过,取消操作)

        End If
    End Sub

#End Region

#Region "清空__显示"

    Private Sub Zb_Clear()

        If Move5.Enabled = True Then Move5.Enabled = False '                    '新增记录
        V_Tj_Code = F_MaxCode(Format(Rdate, "yyMMdd"))       '出库编码
        C1TextBox7.Enabled = True
        Comm1.Enabled = True
        Me.Text = "调价录入"
        Label12.Text = V_Tj_Code                                                '客户编码


        C1TrueDBGrid_Init_Zxy()

        Call P_Data_Show()
    End Sub

    Private Sub Zb_Show()   '显示记录

        If Rtdbgrid.RowCount = 0 Then Exit Sub
        If Move5.Enabled = False Then Move5.Enabled = True '                    '新增记录

        V_Tj_Code = Rrow.Item("Tj_Code") & ""                                   '出库编码



        C1TrueDBGrid_Init_Zxy()

        Label12.Text = V_Tj_Code

        If Rrow.Item("Qp_Date") IsNot DBNull.Value Then
            Comm1.Enabled = False
            C1TextBox7.Enabled = False
            Me.Text = "调价生效"
        Else
            C1TextBox7.Enabled = True
            Comm1.Enabled = True
            Me.Text = "调价录入"
        End If

        Call P_Data_Show()
    End Sub

    Private Sub P_Data_Show()   '从表数据


        Str_Select = "Select Yk_Tj2.*,Mx_Gg,Mx_Cd,Yp_Name,Mx_Cfbl,Yp_Yxq  From Yk_Tj2,V_Ypkc Where Yk_Tj2.Xx_Code=V_Ypkc.Xx_Code  And Tj_Code='" & V_Tj_Code & "' And Yk_Tj2.Yy_code='" & HisVar.HisVar.WsyCode & "' Order By Tj_Id"


        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, Str_Select, "调价明细", True)
        My_Table = My_Dataset.Tables("调价明细")
        '主表记录
        Cb_Cm = CType(BindingContext(My_Dataset, My_Table.TableName), CurrencyManager)
        C1TrueDBGrid1.SetDataBinding(My_Dataset, My_Table.TableName, True)

    

        Call P_Sum()
        C1TextBox7.Select()

    End Sub

#End Region

#Region "主表__编辑"

    Private Sub Zb_Save()                       '主表保存

        If Rinsert = True Then     '增加记录
            Call Zb_Add()
        Else                                '编辑记录
            Call Zb_Edit()
        End If

    End Sub

    Private Sub Zb_Add()    '增加记录

        Dim My_Tb As DataTable = RZbtb
        Dim My_NewRow As DataRow = My_Tb.NewRow
        With My_NewRow
            .Item("Tj_Date") = Format(Rdate, "yyyy-MM-dd")               '入库日期
            V_Tj_Code = F_MaxCode(Format(Rdate, "yyMMdd"))       '出库编码
            Label12.Text = V_Tj_Code
            .Item("Tj_Code") = V_Tj_Code
            .Item("Jsr_Code") = HisVar.HisVar.JsrCode
            .Item("Tj_Memo") = Trim(C1TextBox7.Text) & ""
        End With
        Try
            My_Tb.Rows.Add(My_NewRow)
            Rrow = My_NewRow
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            C1TextBox7.Select()
            Exit Sub
        End Try

        '显示增加后的状态
        Rtdbgrid.MoveLast()
        Rlb.Text = "∑=" + Rtdbgrid.Splits(0).Rows.Count.ToString
      
        
        '更新主表
        Call Zb_Update("增加", My_NewRow)

    End Sub

    Private Sub Zb_Edit()   '编辑记录

        Try
            With Rrow
                .BeginEdit()
                .Item("Tj_Date") = Format(Rdate, "yyyy-MM-dd")               '入库日期
                .Item("Jsr_Code") = HisVar.HisVar.JsrCode
                .Item("Tj_Memo") = Trim(C1TextBox7.Text) & ""
                .EndEdit()
            End With

        Catch ex As Exception
            Beep()
            Rrow.CancelEdit()
            MsgBox(ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
            Exit Sub
        End Try

        '更新主表
        Call Zb_Update("保存", Rrow)

    End Sub

    Private Sub Zb_Update(ByVal V_Lb As String, ByVal V_Row As DataRow)     '更新主表
        Call P_Conn(True)

        If V_Lb = "增加" Then                                               '增加主表
            With Rzbadt.InsertCommand
                .Parameters(0).Value = HisVar.HisVar.WsyCode
                .Parameters(1).Value = V_Row.Item("Tj_Code")
                .Parameters(2).Value = V_Row.Item("Tj_Date")
                .Parameters(3).Value = V_Row.Item("Jsr_Code")
                .Parameters(4).Value = V_Row.Item("Tj_Memo")
                Try
                    .ExecuteNonQuery()
                    Rrow.AcceptChanges()
                    Rinsert = False                                '不充许增加
                    Me.Move5.Enabled = True
                Catch ex As Exception
                    Beep()
                    MsgBox(ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
                Finally
                    Call P_Conn(False)
                End Try
            End With
        Else                                                                '编辑主表
            With Rzbadt.UpdateCommand
                .Parameters(0).Value = V_Row.Item("Tj_Date")
                .Parameters(1).Value = V_Row.Item("Jsr_Code")
                .Parameters(2).Value = V_Row.Item("Tj_Memo")
                .Parameters(3).Value = V_Row.Item("Tj_Code", DataRowVersion.Original)
                Try
                    .ExecuteNonQuery()
                    Rrow.AcceptChanges()
                Catch ex As Exception
                    Beep()
                    MsgBox(ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
                Finally
                    Call P_Conn(False)
                End Try
            End With
        End If
        C1TrueDBGrid1.Select()
    End Sub

#End Region

#Region "控件__动作"

#Region "其它__控件"


    Private Sub Comm_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.Click, Comm2.Click
        Select Case sender.tag
            Case "保存"
                Call Zb_Save()                          '主表存盘
                '更新从表
                C1TextBox7.Select()
            Case "取消"

                Me.Close()
        End Select

    End Sub
#End Region

#Region "DBGrid动作"

    Private Sub C1TrueDBGrid1_AfterDelete(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1TrueDBGrid1.AfterDelete
        Call P_Sum()
    End Sub

    Dim m_Rc As New BaseClass.C_RowChange
    Private Sub C1TrueDBGrid1_RowColChange(ByVal sender As System.Object, ByVal e As C1.Win.C1TrueDBGrid.RowColChangeEventArgs) Handles C1TrueDBGrid1.RowColChange
        V_GridCol = Me.C1TrueDBGrid1.Col
        If (C1TrueDBGrid1.Row + 1) > C1TrueDBGrid1.RowCount Then

        Else
            Cb_Row = Cb_Cm.List(C1TrueDBGrid1.Row).Row
            m_Rc.ChangeRow(Cb_Row)
        End If

    End Sub

    Private Sub C1TrueDBGrid1_MouseDown(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles C1TrueDBGrid1.MouseDown
        If e.Button = Windows.Forms.MouseButtons.Right Then
            Call Cb_Edit()
        End If
    End Sub

    Private Sub C1TrueDBGrid1_BeforeInsert(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.CancelEventArgs) Handles C1TrueDBGrid1.BeforeInsert
        e.Cancel = True
        Call Cb_Edit()
    End Sub

    Private Sub C1TrueDBGrid1_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles C1TrueDBGrid1.KeyDown
        If Rinsert = False Then
            If Rrow.Item("Qp_Date") IsNot DBNull.Value Then
                Exit Sub
            End If
        End If
      
        Select Case e.KeyCode

            Case Keys.Delete
                If Me.C1TrueDBGrid1.SelectedRows.Count = 0 Then
                    If MsgBox("是否删除:药品名称=" + Me.C1TrueDBGrid1.Columns("Yp_Name").Value + " ", MsgBoxStyle.Information + MsgBoxStyle.OkCancel, "提示:") = MsgBoxResult.Cancel Then Exit Sub
                    If Me.C1TrueDBGrid1.RowCount > 0 Then C1TrueDBGrid1.Delete()
                Else

                    Dim Max As Integer = C1TrueDBGrid1.SelectedRows.Count - 1
                    For I As Integer = Max To 0 Step -1
                        C1TrueDBGrid1.Bookmark = C1TrueDBGrid1.SelectedRows(I)
                        C1TrueDBGrid1.Delete()
                    Next
                    C1TrueDBGrid1.SelectedRows.Clear()
                End If
                Call Data_Delete()
                Call P_Sum()
        End Select

    End Sub


#End Region

#End Region

#Region "从表__编辑"

    Private Sub Init_Data()     '从表数据
        Dim Str_Insert As String = "Insert Into Yk_Tj2(Yy_Code,Tj_Code,Xx_Code,Tj_Lsj_Old,Tj_Pfj_Old,Tj_Lsj_New,Tj_Pfj_New,Tj_Yk_Sl,Tj_Yf_Sl1,Tj_Yf_Sl2,Tj_Yf_Sl3,Tj_Yf_Sl4,Tj_Yf_Sl5,Tj_Yf_Sl6,Tj_Yf_Sl7,Tj_Yf_Sl8,Tj_Yf_Sl9)Values(@Yy_Code,@Tj_Code,@Xx_Code,@Tj_Lsj_Old,@Tj_Pfj_Old,@Tj_Lsj_New,@Tj_Pfj_New,@Tj_Yk_Sl,@Tj_Yf_Sl1,@Tj_Yf_Sl2,@Tj_Yf_Sl3,@Tj_Yf_Sl4,@Tj_Yf_Sl5,@Tj_Yf_Sl6,@Tj_Yf_Sl7,@Tj_Yf_Sl8,@Tj_Yf_Sl9)"
        Dim Str_Update As String = "Update Yk_Tj2 Set Tj_Code=@Tj_Code,Xx_Code=@Xx_Code,Tj_Lsj_Old=@Tj_Lsj_Old,Tj_Pfj_Old=@Tj_Pfj_Old,Tj_Lsj_New=@Tj_Lsj_New,Tj_Pfj_New=@Tj_Pfj_New,Tj_Yk_Sl=@Tj_Yk_Sl,Tj_Yf_Sl1=@Tj_Yf_Sl1,Tj_Yf_Sl2=@Tj_Yf_Sl2,Tj_Yf_Sl3=@Tj_Yf_Sl3,Tj_Yf_Sl4=@Tj_Yf_Sl4,Tj_Yf_Sl5=@Tj_Yf_Sl5,Tj_Yf_Sl6=@Tj_Yf_Sl6,Tj_Yf_Sl7=@Tj_Yf_Sl7,Tj_Yf_Sl8=@Tj_Yf_Sl8,Tj_Yf_Sl9=@Tj_Yf_Sl9 Where Tj_Id=@Old_Tj_Id "
        Dim Str_Delete As String = "Delete From Yk_Tj2 Where Tj_Code=@Tj_Code And Xx_Code=@Xx_Code"
        With My_Adapter

            .InsertCommand = New SqlCommand(Str_Insert, My_Cn)
            With .InsertCommand.Parameters
                .Add("@Yy_Code", SqlDbType.Char, 4)
                .Add("@Tj_Code", SqlDbType.Char, 12)
                .Add("@Xx_Code", SqlDbType.VarChar, 16)
                .Add("@Tj_Lsj_Old", SqlDbType.Decimal)
                .Add("@Tj_Pfj_Old", SqlDbType.Decimal)
                .Add("@Tj_Lsj_New", SqlDbType.Decimal)
                .Add("@Tj_Pfj_New", SqlDbType.Decimal)
                .Add("@Tj_Yk_Sl", SqlDbType.Decimal)
                .Add("@Tj_Yf_Sl1", SqlDbType.Decimal)
                .Add("@Tj_Yf_Sl2", SqlDbType.Decimal)
                .Add("@Tj_Yf_Sl3", SqlDbType.Decimal)
                .Add("@Tj_Yf_Sl4", SqlDbType.Decimal)
                .Add("@Tj_Yf_Sl5", SqlDbType.Decimal)
                .Add("@Tj_Yf_Sl6", SqlDbType.Decimal)
                .Add("@Tj_Yf_Sl7", SqlDbType.Decimal)
                .Add("@Tj_Yf_Sl8", SqlDbType.Decimal)
                .Add("@Tj_Yf_Sl9", SqlDbType.Decimal)
            End With

            .UpdateCommand = New SqlCommand(Str_Update, My_Cn)
            With .UpdateCommand.Parameters
                .Add("@Tj_Code", SqlDbType.Char, 12)
                .Add("@Xx_Code", SqlDbType.VarChar, 16)
                .Add("@Tj_Lsj_Old", SqlDbType.Decimal)
                .Add("@Tj_Pfj_Old", SqlDbType.Decimal)
                .Add("@Tj_Lsj_New", SqlDbType.Decimal)
                .Add("@Tj_Pfj_New", SqlDbType.Decimal)
                .Add("@Tj_Yk_Sl", SqlDbType.Decimal)
                .Add("@Tj_Yf_Sl1", SqlDbType.Decimal)
                .Add("@Tj_Yf_Sl2", SqlDbType.Decimal)
                .Add("@Tj_Yf_Sl3", SqlDbType.Decimal)
                .Add("@Tj_Yf_Sl4", SqlDbType.Decimal)
                .Add("@Tj_Yf_Sl5", SqlDbType.Decimal)
                .Add("@Tj_Yf_Sl6", SqlDbType.Decimal)
                .Add("@Tj_Yf_Sl7", SqlDbType.Decimal)
                .Add("@Tj_Yf_Sl8", SqlDbType.Decimal)
                .Add("@Tj_Yf_Sl9", SqlDbType.Decimal)
                .Add("@Old_Tj_Id", SqlDbType.Int, 4, "Tj_Id")
            End With

            .DeleteCommand = New SqlCommand(Str_Delete, My_Cn)
            With .DeleteCommand.Parameters
                .Add("@Tj_Code", SqlDbType.Char, 12)                 '出库编码
                .Add("@Xx_Code", SqlDbType.VarChar, 16)                 '类别编码
            End With


        End With

    End Sub

    Private Sub Data_Delete()
        Dim My_ChangeTable As DataTable = My_Table.GetChanges(DataRowState.Deleted)
        If My_ChangeTable Is Nothing Then Exit Sub
        For Each Me.Cb_Row In My_ChangeTable.Rows
            With My_Adapter.DeleteCommand
                .Parameters(0).Value = Cb_Row.Item("Tj_Code", DataRowVersion.Original)
                .Parameters(1).Value = Cb_Row.Item("Xx_Code", DataRowVersion.Original)
                Try
                    Call P_Conn(True)
                    .ExecuteNonQuery()
                    Call P_Conn(False)
                Catch ex As Exception
                    Beep()
                    MsgBox(ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
                End Try
            End With
        Next
    End Sub

#End Region

#Region "自定义函数"

    Private Sub Cb_Edit()
        If Rinsert = False Then
            If Rrow.Item("Qp_Date") IsNot DBNull.Value Then
                Exit Sub
            End If
        End If

        '判断主表是否存在
        If Rinsert = True Then Call Zb_Add()
        If (C1TrueDBGrid1.Row + 1) > C1TrueDBGrid1.RowCount Then V_Insert = True Else V_Insert = False

        If (C1TrueDBGrid1.Row + 1) > C1TrueDBGrid1.RowCount Then
            V_Insert = True
        Else
            V_Insert = False
            Cb_Row = Cb_Cm.List(C1TrueDBGrid1.Row).Row
        End If

        Dim vform As New Yk_Tj31(Me, V_Insert, Cb_Row, My_Dataset.Tables("调价明细"), C1TrueDBGrid1, My_Adapter, My_Dataset, V_Tj_Code, m_Rc)
        If BaseFunc.BaseFunc.CheckOwnForm(Me, vform) = False Then
            vform.Owner = Me
            vform.Show()
        End If


    End Sub

    Private Sub P_Sum()
        If My_Table.Rows.Count = 0 Then
            T_Label5.Text = "0条"
        Else
            T_Label5.Text = Trim(My_Table.Rows.Count & "条")
        End If

        T_Label5.Location = New Point(Me.Width - T_Label5.Width - 7, 8)
        T_Label4.Location = New Point(Me.Width - T_Label5.Width - T_Label4.Width - 7, 8)

    End Sub

    Private Function F_MaxCode(ByVal V_MaxCode As String)   '出库编码
        Dim My_Cc As New BaseClass.C_Cc()
        Dim V_Code As String = V_MaxCode
        My_Cc.Get_MaxCode("Yk_Tj1", "Tj_Code", 12, "Left(Tj_Code,10)", HisVar.HisVar.WsyCode & V_MaxCode)
        F_MaxCode = My_Cc.编码
        If Mid(F_MaxCode, 1, 10) = "0000000000" Then F_MaxCode = V_MaxCode + Mid(My_Cc.编码, 7)
        Return F_MaxCode
    End Function

#End Region

#Region "鼠标__外观"
    Private Sub P_Comm(ByVal Bt As Button)
        With Bt
            Select Case .Tag
                Case "保存"
                    .Location = New Point(T_Line1.Left + 10, 4)
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")
                    .Text = "            &B"
                Case "取消"
                    .Location = New Point(Comm1.Left + Comm1.Width + 4, 4)
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
                    .Width = Me.Comm1.Width
                    .Text = "            &C"
                    T_Line2.Location = New Point(Me.Comm2.Left + Me.Comm2.Width + 8, 0)
            End Select
            .FlatStyle = FlatStyle.Flat
            .FlatAppearance.BorderSize = 0
            .BackgroundImageLayout = ImageLayout.Center
        End With
    End Sub

    Private Sub Comm_MouseEnter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.MouseEnter, Comm2.MouseEnter
        Select Case sender.tag
            Case "保存"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存2")
                Comm1.Cursor = Cursors.Hand
            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消2")
                Comm2.Cursor = Cursors.Hand
        End Select

    End Sub

    Private Sub Comm_MouseLeave(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.MouseLeave, Comm2.MouseLeave
        Select Case sender.tag
            Case "保存"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")
                Comm1.Cursor = Cursors.Default
            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
                Comm2.Cursor = Cursors.Default
        End Select
    End Sub

    Private Sub Comm_MouseDown(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm1.MouseDown, Comm2.MouseDown
        Select Case sender.tag
            Case "保存"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存3")
                Comm1.Cursor = Cursors.Default

            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消3")
                Comm2.Cursor = Cursors.Default
        End Select
    End Sub

    Private Sub Comm_MouseUp(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm1.MouseUp, Comm2.MouseUp
        Select Case sender.tag
            Case "保存"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")
                Comm1.Cursor = Cursors.Hand
            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
                Comm2.Cursor = Cursors.Hand
        End Select
    End Sub
#End Region



#Region "药材C1Truedbgrid初始化"

    Private Sub C1TrueDBGrid_Init_Zxy()
        '初始化TDBGrid
        Dim My_Grid As New BaseClass.C_Grid(Me.C1TrueDBGrid1)
        With My_Grid
            .Init_Grid()

            .AllAddNew(true)
            .AllDelete(False)
            .AllUpdate(False)
            .AllSort(False)
            .P_Dock(BaseClass.C_Grid.Dock.Fill)
            .Init_Column("药品名称", "Yp_Name", 230, "左", "")
            .Init_Column("产地", "Mx_Cd", 100, "左", "")
            .Init_Column("规格", "Mx_Gg", 100, "左", "")
            .Init_Column("有效期", "Yp_Yxq", 80, "中", "yyyy-MM-dd")
            .Init_Column("原药房售价", "Tj_Lsj_Old", 70, "右", "###,###,##0.00####")
            .Init_Column("原批发价", "Tj_Pfj_Old", 70, "右", "###,###,##0.00####")
            .Init_Column("新药房售价", "Tj_Lsj_New", 70, "右", "###,###,##0.00####")
            .Init_Column("新批发价", "Tj_Pfj_New", 70, "右", "###,###,##0.00####")
            C1TrueDBGrid1.Splits(0).DisplayColumns("有效期").Locked = True
            C1TrueDBGrid1.Splits(0).DisplayColumns("药品名称").Locked = True
            C1TrueDBGrid1.Splits(0).DisplayColumns("产地").Locked = True
            C1TrueDBGrid1.Splits(0).DisplayColumns("规格").Locked = True
            C1TrueDBGrid1.Splits(0).DisplayColumns("原药房售价").Locked = True
            C1TrueDBGrid1.Splits(0).DisplayColumns("原批发价").Locked = True
            C1TrueDBGrid1.Splits(0).DisplayColumns("新药房售价").Locked = True
            C1TrueDBGrid1.Splits(0).DisplayColumns("新批发价").Locked = True

        End With
    End Sub


#End Region

#Region "输入法设置"
    '中文
    Private Sub C1TextBox1_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1TextBox7.GotFocus
        InputLanguage.CurrentInputLanguage = HisVar.HisVar.InputCode
    End Sub
#End Region

End Class