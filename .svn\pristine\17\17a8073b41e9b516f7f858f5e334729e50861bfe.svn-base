﻿Imports C1.Win.C1Input
Imports System.Data.SqlClient
Imports BaseClass
Imports System.Windows.Forms

Public Class EmrMb3

#Region "变量定义"
    Dim V_Name As String                                            '简称是否发生变化
    Dim BllEmr_Mb As New BLLOld.B_Emr_Mb
    Dim BllEmr_Mblb As New BLLOld.B_Emr_Mblb
    Dim ModelEmr_Mb As New ModelOld.M_Emr_Mb

    Private BllKs As New BLLOld.B_Zd_YyKs
    Private BllYs As New BLLOld.B_Zd_YyYs
    Private cha As New BaseClass.Chs2Spell
#End Region

#Region "传参"
    Dim Rinsert As Boolean
    Dim Rrow As DataRow
    Dim RZbtb As DataTable
    Dim Rrc As C_RowChange
    Dim Rtext, Rtag As String
    Dim RNodeList As List(Of TreeNode)
#End Region

    Public Sub New(ByVal tinsert As Boolean, ByVal trow As DataRow, ByVal tZbtb As DataTable, ByVal trc As C_RowChange,
                   ByVal tTag As String, ByVal tText As String)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        Rinsert = tinsert
        Rrow = trow
        RZbtb = tZbtb
        Rrc = trc
        Rtag = tTag
        Rtext = tText
        ' 在 InitializeComponent() 调用之后添加任何初始化。
    End Sub

    Private Sub Zd_Cl_Kq2_FormClosed(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosedEventArgs) Handles Me.FormClosed
        RemoveHandler Rrc.RowChanged, AddressOf Data_Show
    End Sub

    Private Sub Zd_Cl_Kq2_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        ' BaseFunc.BaseFunc.SetNumsytle(Me)
        Form_Init()
        AddHandler Rrc.RowChanged, AddressOf Data_Show
        If Rinsert = True Then Call Data_Clear() Else Call Data_Show(Rrow)
    End Sub

#Region "窗体__事件"

    Private Sub Form_Init()

        With DoctorDtComobo1
            .DataView = BllYs.GetList("yy_code='" & HisVar.HisVar.WsyCode & "'").Tables(0).DefaultView
            .Init_Colum("Ys_Name", "医生名称", 120, "左")
            .Init_Colum("Ys_Jc", "医生简称", 0, "左")
            .Init_Colum("Ys_Code", "医生编码", 0, "中")
            .DisplayMember = "Ys_Name"
            .ValueMember = "Ys_Code"
            .RowFilterNotTextNull = "Ys_Jc"
            .RowFilterTextNull = ""
            .DroupDownWidth = .Width - .CaptainWidth
            .MaxDropDownItems = 15
            .SelectedValue = -1
        End With
        With ks_Combo
            .DataView = BllKs.GetList("yy_code='" & HisVar.HisVar.WsyCode & "'").Tables(0).DefaultView
            .Init_Colum("ks_Name", "科室名称", 120, "左")
            .Init_Colum("ks_Jc", "科室简称", 0, "左")
            .Init_Colum("ks_Code", "科室编码", 0, "中")
            .DisplayMember = "ks_Name"
            .ValueMember = "ks_Code"
            .RowFilterNotTextNull = "ks_Jc"
            .RowFilterTextNull = ""
            .DroupDownWidth = .Width - .CaptainWidth
            .MaxDropDownItems = 15
            .SelectedValue = -1
        End With

        With SexDtComobo1
            .Additem = "通用,0"
            .Additem = "男性,1"
            .Additem = "女性,2"
            .SelectedIndex = 0
            .DisplayColumns(1).Visible = False
        End With
        Dim dt As DataTable = BllEmr_Mblb.GetEndNodes().Tables(0)
        With MblbSingleComobo1
            .DataView = BllEmr_Mblb.GetEndNodes().Tables(0).DefaultView
            .Init_Colum("Mblb_Name", "类别名称", 120, "左")
            .Init_Colum("Mblb_Code", "编码", 0, "左")
            .Init_Colum("Mblb_jc", "简称", 0, "左")
            .DisplayMember = "Mblb_Name"
            .ValueMember = "Mblb_Code"
            .RowFilterNotTextNull = "Mblb_jc"
            .RowFilterTextNull = ""
            .DroupDownWidth = .Width - .CaptainWidth
            .MaxDropDownItems = 15
            .SelectedValue = Rtag
        End With
        RadioButton标准.Checked = True



        CodeTextBox.Enabled = False
        JcTextBox.Enabled = False

    End Sub

#End Region

#Region "数据__操作"

    Private Sub Data_Clear()

        Rinsert = True

        CodeTextBox.Text = BllEmr_Mb.MaxCode()
        MblbSingleComobo1.SelectedValue = Rtag
        NameTextBox.Text = ""
        JcTextBox.Text = ""
        SexDtComobo1.SelectedIndex = 0
        AgeNumericEdit1.Value = 200
        RadioButton标准.Checked = True
        ks_Combo.SelectedIndex = -1
        DoctorDtComobo1.SelectedIndex = -1
        CheckBoxMulti.Checked = False
        CheckBoxMust.Checked = False
        MemoTextBox1.Text = ""
        Call P_Show_Label()
    End Sub
    'Mb_Code, Mblb_Code, Mb_Name, Mb_Jc, Mb_Nr, Mb_Sex,
    ' isMust, isMulti, AgeLimit, isStandard, Ks_Code, Ys_Code, Mb_Memo
    Private Sub Data_Show(ByVal tmp_Row As DataRow)
        Rinsert = False
        Rrow = tmp_Row
        With Rrow
            CodeTextBox.Text = .Item("Mb_Code") & ""
            NameTextBox.Text = .Item("Mb_Name") & ""
            MblbSingleComobo1.SelectedValue = .Item("Mblb_Code") & ""
            JcTextBox.Text = .Item("Mb_Jc") & ""
            SexDtComobo1.SelectedValue = .Item("Mb_Sex")
            AgeNumericEdit1.Value = .Item("AgeLimit")
            If .Item("isStandard") = True Then
                RadioButton标准.Checked = True
            Else
                RadioButton专科.Checked = True
            End If
            ks_Combo.SelectedValue = .Item("Ks_Code") & ""
            DoctorDtComobo1.SelectedValue = .Item("Ys_Code") & ""
            CheckBoxMulti.Checked = .Item("isMulti")
            CheckBoxMust.Checked = .Item("isMust")
            MemoTextBox1.Text = .Item("Mb_Memo") & ""
        End With
        V_Name = MblbSingleComobo1.SelectedValue
        Call P_Show_Label()
    End Sub

    Private Sub P_Show_Label()
        If Rinsert = True Then
            Move5.Enabled = False                                           '新增记录
            T_Label2.Text = "新增"
        Else
            Move5.Enabled = True                                            '新增记录
            T_Label2.Text = IIf(RZbtb.Rows.Count() = 0, "0", CStr((RZbtb.Rows.IndexOf(Rrow)) + 1))
        End If
        T_Label3.Text = "∑=" & RZbtb.Rows.Count
        NameTextBox.Select()
    End Sub

#End Region

#Region "控件__动作"

    Private Sub Comm_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyButton1.Click, MyButton2.Click
        Select Case sender.tag
            Case "保存"
                If MblbSingleComobo1.SelectedValue = "" Then
                    MsgBox("模板类别不能为空", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示")
                    MblbSingleComobo1.SelectedIndex = 0
                    MblbSingleComobo1.Select()
                    Exit Sub

                End If
                If NameTextBox.Text = "" Then
                    MsgBox("模板名称不能为空", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示")
                    If Rinsert = True Then
                        NameTextBox.Focus()
                        Exit Sub
                    Else
                        Me.Close()
                        Exit Sub
                    End If
                End If
                If checkZd(CodeTextBox.Text) = False Then
                    MsgBox("模板名称已存在!", MsgBoxStyle.Information + MsgBoxStyle.OkOnly, "提示")
                    NameTextBox.Focus()
                    Exit Sub
                End If

                If RadioButton专科.Checked = True And ks_Combo.SelectedIndex = -1 Then
                    MsgBox("专科模板必须选择科室!", MsgBoxStyle.Information + MsgBoxStyle.OkOnly, "提示")
                    ks_Combo.Focus()
                    Exit Sub
                End If

                If Rinsert = True Then
                    Call Data_Add()
                Else
                    Call Data_Edit()
                End If

            Case "取消"
                Me.Close()

        End Select
    End Sub

    Private Sub Move_Click(ByVal sender As Object, ByVal e As C1.Win.C1Command.ClickEventArgs) Handles Move1.Click, Move2.Click, Move3.Click, Move4.Click, Move5.Click
        If sender.text = "新增" Then
            If Rtag = "00000" Then
                MsgBox("选择根节点不能增加模板名称！", MsgBoxStyle.Exclamation, "提示")
                NameTextBox.Focus()
                Exit Sub
            End If '增加状态
            Call Data_Clear()
        Else
            Rrc.GridMove(sender.text)
        End If
    End Sub

    Private Sub RadioButton_Validating(sender As Object, e As System.EventArgs) Handles RadioButton标准.CheckedChanged, RadioButton专科.CheckedChanged
        If RadioButton标准.Checked = True Then
            ks_Combo.Enabled = False
            DoctorDtComobo1.Enabled = False
            ks_Combo.SelectedIndex = -1
            DoctorDtComobo1.SelectedIndex = -1
        Else
            ks_Combo.Enabled = True
            DoctorDtComobo1.Enabled = True
        End If
    End Sub

    Private Sub NameTextBox_TextChanged(sender As Object, e As System.EventArgs) Handles NameTextBox.TextChanged
        If NameTextBox.Text.Trim <> "" Then JcTextBox.Text = cha.GetPy(Trim(NameTextBox.Text))
    End Sub


#End Region

#Region "数据__编辑"

    Private Sub Data_Add()
        'Mb_Code, Mblb_Code, Mb_Name, Mb_Jc, Mb_Nr, Mb_Sex,
        ' isMust, isMulti, AgeLimit, isStandard, Ks_Code, Ys_Code, Mb_Memo

        Dim My_NewRow As DataRow = RZbtb.NewRow

        With My_NewRow
            .Item("Mb_Code") = BllEmr_Mb.MaxCode()
            .Item("Mb_Name") = Trim(NameTextBox.Text & "")
            .Item("Mblb_Code") = MblbSingleComobo1.SelectedValue
            .Item("Mblb_Name") = Trim(MblbSingleComobo1.Text & "")
            .Item("Mb_Jc") = Trim(JcTextBox.Text & "")

            .Item("Mb_Nr") = Nothing

            .Item("Mb_Sex") = SexDtComobo1.SelectedValue
            .Item("isMust") = CheckBoxMust.Checked
            .Item("isMulti") = CheckBoxMulti.Checked
            .Item("AgeLimit") = AgeNumericEdit1.Value
            If RadioButton标准.Checked = True Then
                .Item("isStandard") = True
            Else
                .Item("isStandard") = False
            End If

            .Item("Ks_Code") = ks_Combo.SelectedValue
            .Item("Ys_Code") = DoctorDtComobo1.SelectedValue

            .Item("Ks_name") = ks_Combo.Text
            .Item("Ys_name") = DoctorDtComobo1.Text
            .Item("Mb_Memo") = MemoTextBox1.Text

        End With


        '数据保存
        Try
            With ModelEmr_Mb
                .Mblb_Code = My_NewRow.Item("Mblb_Code")
                .Mb_Code = My_NewRow.Item("Mb_Code")
                .Mb_Name = My_NewRow.Item("Mb_Name")
                .Mb_Jc = My_NewRow.Item("Mb_Jc")
                ' .Mb_Nr = My_NewRow.Item("Mb_Nr")
                .Mb_Sex = SexDtComobo1.SelectedValue
                .isMust = My_NewRow.Item("isMust")
                .isMulti = My_NewRow.Item("isMulti")
                .AgeLimit = My_NewRow.Item("AgeLimit")
                .isStandard = My_NewRow.Item("isStandard")
                If IsDBNull(My_NewRow.Item("Ks_Code")) = True Then
                Else
                    .Ks_Code = My_NewRow.Item("Ks_Code")
                End If
                If IsDBNull(My_NewRow.Item("Ys_Code")) = False Then
                    .Ys_Code = My_NewRow.Item("Ys_Code")
                End If

                .Mb_Memo = My_NewRow.Item("Mb_Memo")

            End With
            RZbtb.Rows.Add(My_NewRow)
            BllEmr_Mb.Add(ModelEmr_Mb)
            My_NewRow.AcceptChanges()
            EmrMb1.vSelectedNodeTag = MblbSingleComobo1.SelectedValue
            Rrc.NodeAdd()

            Rrc.GridMove("最后")
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            Exit Sub
        Finally
            NameTextBox.Focus()
        End Try
        '数据清空
        Call Data_Clear()

    End Sub

    Private Sub Data_Edit()
        Dim My_Row As DataRow = Rrow
        With My_Row
            .BeginEdit()

            .Item("Mb_Name") = Trim(NameTextBox.Text & "")
            .Item("Mblb_Code") = MblbSingleComobo1.SelectedValue
            .Item("Mblb_Name") = Trim(MblbSingleComobo1.Text & "")
            .Item("Mb_Jc") = Trim(JcTextBox.Text & "")
            .Item("Mb_Nr") = Nothing
            .Item("Mb_Sex") = SexDtComobo1.SelectedValue
            .Item("isMust") = CheckBoxMust.Checked
            .Item("isMulti") = CheckBoxMulti.Checked
            .Item("AgeLimit") = AgeNumericEdit1.Value
            If RadioButton标准.Checked = True Then
                .Item("isStandard") = True
            Else
                .Item("isStandard") = False
            End If
            .Item("Ks_Code") = ks_Combo.SelectedValue
            .Item("Ys_Code") = DoctorDtComobo1.SelectedValue
            .Item("Ks_name") = ks_Combo.Text
            .Item("Ys_name") = DoctorDtComobo1.Text
            .Item("Mb_Memo") = MemoTextBox1.Text

        End With
        '数据保存

        With ModelEmr_Mb
            .Mblb_Code = My_Row.Item("Mblb_Code")
            .Mb_Code = My_Row.Item("Mb_Code")
            .Mb_Name = My_Row.Item("Mb_Name")
            .Mb_Jc = My_Row.Item("Mb_Jc")
            ' .Mb_Nr = My_Row.Item("Mb_Nr")
            .Mb_Sex = SexDtComobo1.SelectedValue
            .isMust = My_Row.Item("isMust")
            .isMulti = My_Row.Item("isMulti")
            .AgeLimit = My_Row.Item("AgeLimit")
            .isStandard = My_Row.Item("isStandard")
            If IsDBNull(My_Row.Item("Ks_Code")) = True Then
            Else
                .Ks_Code = My_Row.Item("Ks_Code")
            End If
            If IsDBNull(My_Row.Item("Ys_Code")) = False Then
                .Ys_Code = My_Row.Item("Ys_Code")
            End If
            .Mb_Memo = My_Row.Item("Mb_Memo")

        End With
        Try
            My_Row.AcceptChanges()
            BllEmr_Mb.Update(ModelEmr_Mb)


            If V_Name <> MblbSingleComobo1.SelectedValue Then

                EmrMb1.vSelectedNodeTag = V_Name
                Rrc.NodeAdd()


                EmrMb1.vSelectedNodeTag = MblbSingleComobo1.SelectedValue
                Rrc.NodeAdd()
                Rrc.GridMove("最后")
            End If


        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
        Finally
            NameTextBox.Focus()
        End Try
    End Sub

#End Region

#Region "检查当前数据库是否有重复值"
    Private Function checkZd(ByVal Mb_Code As String) As Boolean
        If BllEmr_Mb.GetList("Emr_mb.mblb_code='" & MblbSingleComobo1.SelectedValue & "' and Mb_Name='" & NameTextBox.Text & "'  and Mb_Code<>'" & Mb_Code & "'").Tables(0).Rows.Count > 0 Then
            Return False
        Else
            Return True
        End If

    End Function

#End Region


End Class