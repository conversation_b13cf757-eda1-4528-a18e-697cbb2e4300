﻿
Public Delegate Sub RowChangedHandler(ByVal s As DataRow)
Public Delegate Sub AddChangeChange(ByVal lb As String)
Public Delegate Sub GridRowChange(ByVal lb As String)
Public Delegate Sub DeleTreeAdd(ByVal V_Key As String, ByVal V_Text As String)
Public Delegate Sub DeleDrDc(ByVal V_Key As String, ByVal V_Text As String, ByVal vFlag As String)
Public Delegate Sub DeleNodeAdd()
Public Delegate Sub ModelChange(ByVal model As Object)
Public Class C_RowChange
    Public Event RowChanged As RowChangedHandler
    Public Event AddChangeEvent As AddChangeChange
    Public Event GridMoveEvent As GridRowChange
    Public Event TreeAddEvent As DeleTreeAdd
    Public Event NodeAddEvent As DeleNodeAdd
    Public Event DrDcEvent As DeleDrDc
    Public Event ModelChangeEvent As ModelChange
    Public Sub ChangeRow(ByVal s As DataRow)
        RaiseEvent RowChanged(s)
    End Sub
    Public Sub ChangeModel(ByVal model As Object)
        RaiseEvent ModelChangeEvent(model)
    End Sub
    Public Sub AddChange(ByVal s As String)
        RaiseEvent AddChangeEvent(s)
    End Sub

    Public Sub GridMove(ByVal s As String)
        RaiseEvent GridMoveEvent(s)
    End Sub
    Public Sub TreeAdd(ByVal V_Key As String, ByVal V_Text As String)
        RaiseEvent TreeAddEvent(V_Key, V_Text)
    End Sub

    Public Sub NodeAdd()
        RaiseEvent NodeAddEvent()
    End Sub


    Public Sub DrDc(ByVal V_Key As String, ByVal V_Text As String, ByVal vFlag As String)
        RaiseEvent DrDcEvent(V_Key, V_Text, vFlag)
    End Sub

End Class
