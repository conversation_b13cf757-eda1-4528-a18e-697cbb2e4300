﻿/**  版本信息模板在安装目录下，可自行修改。
* M_Materials_Stock.cs
*
* 功 能： N/A
* 类 名： M_Materials_Stock
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016-11-26 15:05:16   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace ModelOld
{
    /// <summary>
    /// 物资库存
    /// </summary>
    [Serializable]
    public partial class M_Materials_Stock
    {
        public M_Materials_Stock()
        { }
        #region Model
        private string _materials_code;
        private string _materialswh_code;
        private string _materialsstock_code;
        private string _materialslot;
        private DateTime? _materialsexpirydate;
        private decimal? _materialsstore_num;
        private decimal? _materialsstore_price;
        private decimal? _materialsstore_money;

        private string _materials_name;
        private string _materialswh_Name;
        private string _materials_py;
        private string _materials_spec;
        //Materials_Stock.Materials_Code, MaterialsWh_Code, MaterialsStock_Code, MaterialsLot, MaterialsExpiryDate, MaterialsStore_Num,
        //MaterialsStore_Price, MaterialsStore_Money, Materials_Name, Materials_Py
        public string Materials_Name
        {
            set { _materials_name = value; }
            get { return _materials_name; }
        }
        /// <summary>
        /// 库房编码
        /// </summary>
        public string MaterialsWh_Name
        {
            set { _materialswh_Name = value; }
            get { return _materialswh_Name; }
        }
        /// <summary>
        /// 物资编码+6位流水号
        /// </summary>
        public string Materials_Py
        {
            set { _materials_py = value; }
            get { return _materials_py; }
        }

        public string Materials_Spec
        {
            set { _materials_spec = value; }
            get { return _materials_spec; }
        }

        /// <summary>
        /// 物资编码
        /// </summary>
        public string Materials_Code
        {
            set { _materials_code = value; }
            get { return _materials_code; }
        }
        /// <summary>
        /// 库房编码
        /// </summary>
        public string MaterialsWh_Code
        {
            set { _materialswh_code = value; }
            get { return _materialswh_code; }
        }
        /// <summary>
        /// 物资编码+6位流水号
        /// </summary>
        public string MaterialsStock_Code
        {
            set { _materialsstock_code = value; }
            get { return _materialsstock_code; }
        }
        /// <summary>
        /// 物资批号
        /// </summary>
        public string MaterialsLot
        {
            set { _materialslot = value; }
            get { return _materialslot; }
        }
        /// <summary>
        /// 物资有效期
        /// </summary>
        public DateTime? MaterialsExpiryDate
        {
            set { _materialsexpirydate = value; }
            get { return _materialsexpirydate; }
        }
        /// <summary>
        /// 库存数量
        /// </summary>
        public decimal? MaterialsStore_Num
        {
            set { _materialsstore_num = value; }
            get { return _materialsstore_num; }
        }
        /// <summary>
        /// 库存单价
        /// </summary>
        public decimal? MaterialsStore_Price
        {
            set { _materialsstore_price = value; }
            get { return _materialsstore_price; }
        }
        /// <summary>
        /// 库存金额
        /// </summary>
        public decimal? MaterialsStore_Money
        {
            set { _materialsstore_money = value; }
            get { return _materialsstore_money; }
        }
        #endregion Model

    }
}

