﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="0" />
    <DataSources isList="true" count="1">
      <门诊发票 Ref="2" type="DataTableSource" isKey="true">
        <Alias>门诊发票</Alias>
        <Columns isList="true" count="9">
          <value>Fp_Id,System.String</value>
          <value>Mz_Code,System.String</value>
          <value>Ry_Name,System.String</value>
          <value>Ry_Address,System.String</value>
          <value>Ry_YlCode,System.String</value>
          <value>Mz_Ph,System.String</value>
          <value>Ks_Name,System.String</value>
          <value>Mz_Lb,System.String</value>
          <value>Mz_Money,System.Decimal</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>门诊发票</Name>
        <NameInSource>门诊发票</NameInSource>
      </门诊发票>
    </DataSources>
    <Relations isList="true" count="0" />
    <Report isRef="0" />
    <Variables isList="true" count="9">
      <value>,医院名称,医院名称,System.String,,False,False</value>
      <value>,患者姓名,患者姓名,System.String,,False,False</value>
      <value>,患者单位,患者单位,System.String,,False,False</value>
      <value>,编号,编号,System.String,,False,False</value>
      <value>,科别,科别,System.String,,False,False</value>
      <value>,医疗号,医疗号,System.String,,False,False</value>
      <value>,大写,大写,System.String,,False,False</value>
      <value>,开票员,开票员,System.String,,False,False</value>
      <value>,日期,日期,System.String,,False,False</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="3" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="3">
        <GroupHeaderBand1 Ref="4" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,21.1,4.7</ClientRectangle>
          <Components isList="true" count="30">
            <Text1 Ref="5" type="Text" isKey="true">
              <Border>All;[155:155:155];1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0.8,1.2,5.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,8</Font>
              <Guid>785d945ac07d4af982a3fcd0c8fb8b81</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text1</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>河北省门诊收费收据</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text1>
            <Text6 Ref="6" type="Text" isKey="true">
              <Border>All;[155:155:155];1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0.5,2.8,5.8,0.3</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>9d0f27064e414ee5b49198da3918292a</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text6</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>{医院名称}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text6>
            <Text7 Ref="7" type="Text" isKey="true">
              <Border>All;[155:155:155];1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0.5,3.1,3.2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>2fdc5f6556634fc68eb33bea4d7d2fdf</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text7</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>{门诊发票.Ry_Name}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text7>
            <Text8 Ref="8" type="Text" isKey="true">
              <Border>All;[155:155:155];1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0.5,3.5,3.2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>b704b1e6adaf46cf8f3196dc113371a3</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text8</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>{患者单位}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text8>
            <Text11 Ref="9" type="Text" isKey="true">
              <Border>All;[155:155:155];1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0.5,3.9,2.9,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,8</Font>
              <Guid>4fa18a6e093444daa109f5533a2ac7f6</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text11</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>项目名称</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text11>
            <Text12 Ref="10" type="Text" isKey="true">
              <Border>All;[155:155:155];1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0.5,2.4,5.8,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>a2994af70a404031ba091215b6119015</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text12</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>{门诊发票.Mz_Ph}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text12>
            <Text13 Ref="11" type="Text" isKey="true">
              <Border>All;[155:155:155];1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.7,3.1,3.1,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>a3a5e42a67b343cd8f93e64d450ea824</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text13</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>{门诊发票.Ks_Name}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text13>
            <Text14 Ref="12" type="Text" isKey="true">
              <Border>All;[155:155:155];1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.7,3.5,3.1,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>34c5b7b1db8740a6ad65d5047d2d2a0d</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text14</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>{门诊发票.Ry_YlCode}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text14>
            <Text15 Ref="13" type="Text" isKey="true">
              <Border>All;[155:155:155];1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.4,3.9,2.9,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,8</Font>
              <Guid>fdece025516c482fb73d07fad111a8f3</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text15</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>项目金额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text15>
            <Text2 Ref="14" type="Text" isKey="true">
              <Border>All;[155:155:155];1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.8,1.2,5.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,8</Font>
              <Guid>4dda599d8aa841f78de7aa03d5ee5f43</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text2</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>河北省门诊收费收据</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text2>
            <Text3 Ref="15" type="Text" isKey="true">
              <Border>All;[155:155:155];1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.5,2.8,5.8,0.3</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>b466106abcc2458b92af6262340aefca</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text3</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>{医院名称}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text3>
            <Text4 Ref="16" type="Text" isKey="true">
              <Border>All;[155:155:155];1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.5,3.1,3.2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>c88165d683724f57b5321be5ce21068f</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text4</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>{门诊发票.Ry_Name}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text4>
            <Text5 Ref="17" type="Text" isKey="true">
              <Border>All;[155:155:155];1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.5,3.5,3.2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>ae42d72566984751ac49c7abc500e897</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text5</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>{患者单位}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text5>
            <Text9 Ref="18" type="Text" isKey="true">
              <Border>All;[155:155:155];1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.5,3.9,2.9,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,8</Font>
              <Guid>dbd726f111754bb6aa4a13895a2db4d6</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text9</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>项目名称</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text9>
            <Text10 Ref="19" type="Text" isKey="true">
              <Border>All;[155:155:155];1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.5,2.4,5.8,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>10836084b57c4d17a679025f8963fc9d</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text10</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>{门诊发票.Mz_Ph}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text10>
            <Text25 Ref="20" type="Text" isKey="true">
              <Border>All;[155:155:155];1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>10.7,3.1,3.1,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>0856819227ad4f1c8538858e1eea4ed4</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text25</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>{门诊发票.Ks_Name}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text25>
            <Text26 Ref="21" type="Text" isKey="true">
              <Border>All;[155:155:155];1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>10.7,3.5,3.1,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>d66831b3e17341978935ff4237a4a67b</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text26</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>{门诊发票.Ry_YlCode}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text26>
            <Text27 Ref="22" type="Text" isKey="true">
              <Border>All;[155:155:155];1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>10.4,3.9,2.9,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,8</Font>
              <Guid>d627156847784ef48f721585d340735c</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text27</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>项目金额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text27>
            <Text34 Ref="23" type="Text" isKey="true">
              <Border>All;[155:155:155];1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6.9,2.8,0.6,1.9</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>88cffa1c1ece4d7392b8dc6c4d7740f5</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text34</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <TextBrush>Black</TextBrush>
            </Text34>
            <Text37 Ref="24" type="Text" isKey="true">
              <Border>All;[155:155:155];1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>14.7,1.2,5.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,8</Font>
              <Guid>905feb3269ad45a98962c4bc99f78e5b</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text37</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>河北省门诊收费收据</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text37>
            <Text38 Ref="25" type="Text" isKey="true">
              <Border>All;[155:155:155];1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>14.4,2.7,5.8,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>c7449a438b4b4c65bf62a97b1b513024</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text38</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>{医院名称}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text38>
            <Text39 Ref="26" type="Text" isKey="true">
              <Border>All;[155:155:155];1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>14.4,3.1,3.2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>0e5006d874bb42019f5747b774843ce6</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text39</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>{门诊发票.Ry_Name}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text39>
            <Text40 Ref="27" type="Text" isKey="true">
              <Border>All;[155:155:155];1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>14.4,3.5,3.2,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>149cb39ad4fa4f638766b70164e1e1e0</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text40</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>{患者单位}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text40>
            <Text41 Ref="28" type="Text" isKey="true">
              <Border>All;[155:155:155];1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>14.4,3.9,2.9,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,8</Font>
              <Guid>79a7a0b8f65a4966bc4406e1a8e422e9</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text41</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>项目名称</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text41>
            <Text42 Ref="29" type="Text" isKey="true">
              <Border>All;[155:155:155];1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>14.4,2.4,5.8,0.3</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>461ceb11fcaf4365a9e7946f1d54a358</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text42</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>{门诊发票.Mz_Ph}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text42>
            <Text43 Ref="30" type="Text" isKey="true">
              <Border>All;[155:155:155];1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>17.6,3.1,3.1,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>a1dd134d4912431fb02a7ff3a4eed6f8</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text43</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>{门诊发票.Ks_Name}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text43>
            <Text44 Ref="31" type="Text" isKey="true">
              <Border>All;[155:155:155];1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>17.6,3.5,3.1,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>d1afec6e46814293addb7bec9f7ef63a</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text44</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>{门诊发票.Ry_YlCode}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text44>
            <Text45 Ref="32" type="Text" isKey="true">
              <Border>All;[155:155:155];1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>17.3,3.9,2.9,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,8</Font>
              <Guid>167966ce351b4575869afeab31a4fdc8</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text45</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>项目金额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text45>
            <Text52 Ref="33" type="Text" isKey="true">
              <Border>All;[155:155:155];1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13.8,2.7,0.6,2</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>eee51970c38a4cd9b7a93070fb705ab7</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text52</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <TextBrush>Black</TextBrush>
            </Text52>
            <Text22 Ref="34" type="Text" isKey="true">
              <Border>All;[155:155:155];1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,2.6,0.6,2.1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>561be705378f41b6bbcae66752510da5</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text22</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <TextBrush>Black</TextBrush>
            </Text22>
          </Components>
          <Condition>{门诊发票.Fp_Id}</Condition>
          <Conditions isList="true" count="0" />
          <Name>GroupHeaderBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </GroupHeaderBand1>
        <DataBand1 Ref="35" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,5.9,21.1,0.4</ClientRectangle>
          <Components isList="true" count="6">
            <Text16 Ref="36" type="Text" isKey="true">
              <Border>All;[155:155:155];1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0.6,0,2.9,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>fafbbc7aa29a49f2b3bd3a32a5aa6ae9</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text16</Name>
              <Page isRef="3" />
              <Parent isRef="35" />
              <Text>{门诊发票.Mz_Lb}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text16>
            <Text17 Ref="37" type="Text" isKey="true">
              <Border>All;[155:155:155];1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.5,0,2.9,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>e8e080ca8aa247429560995d5979ad64</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text17</Name>
              <NullValue>  </NullValue>
              <Page isRef="3" />
              <Parent isRef="35" />
              <Text>{门诊发票.Mz_Money}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text17>
            <Text28 Ref="38" type="Text" isKey="true">
              <Border>All;[155:155:155];1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.6,0,2.9,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>2a2fc45bc12246e8bb607dc3406e3b68</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text28</Name>
              <Page isRef="3" />
              <Parent isRef="35" />
              <Text>{门诊发票.Mz_Lb}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text28>
            <Text29 Ref="39" type="Text" isKey="true">
              <Border>All;[155:155:155];1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>10.5,0,2.9,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>0df1753e0c25437097a34dd4f3903196</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text29</Name>
              <NullValue>  </NullValue>
              <Page isRef="3" />
              <Parent isRef="35" />
              <Text>{门诊发票.Mz_Money}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text29>
            <Text46 Ref="40" type="Text" isKey="true">
              <Border>All;[155:155:155];1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>14.5,0,2.9,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>4e91a404e9224073a566d45514aec08f</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text46</Name>
              <Page isRef="3" />
              <Parent isRef="35" />
              <Text>{门诊发票.Mz_Lb}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text46>
            <Text47 Ref="41" type="Text" isKey="true">
              <Border>All;[155:155:155];1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>17.4,0,2.9,0.4</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>a6e66e47a2e34eee987677c52501db27</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text47</Name>
              <NullValue> </NullValue>
              <Page isRef="3" />
              <Parent isRef="35" />
              <Text>{门诊发票.Mz_Money}</Text>
              <TextBrush>Black</TextBrush>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text47>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>门诊发票</DataSourceName>
          <Filters isList="true" count="0" />
          <Name>DataBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Sort isList="true" count="2">
            <value>DESC</value>
            <value>Mz_Lb</value>
          </Sort>
        </DataBand1>
        <GroupFooterBand1 Ref="42" type="GroupFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,7.1,21.1,3.2</ClientRectangle>
          <Components isList="true" count="18">
            <Text18 Ref="43" type="Text" isKey="true">
              <Border>All;[155:155:155];1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0.5,0,1.3,1.1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,8</Font>
              <Guid>256f411d2e734407811d8c25d297c58a</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text18</Name>
              <Page isRef="3" />
              <Parent isRef="42" />
              <Text>大写</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text18>
            <Text19 Ref="44" type="Text" isKey="true">
              <Border>All;[155:155:155];1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.8,0,4.5,1.1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9,Regular,Point,False,0</Font>
              <Guid>b58fa8a86b494ce49610c8313745ff7e</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text19</Name>
              <Page isRef="3" />
              <Parent isRef="42" />
              <Text>{MoneyCn(Sum(GroupHeaderBand1,门诊发票.Mz_Money))}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text19>
            <Text20 Ref="45" type="Text" isKey="true">
              <Border>All;[155:155:155];1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0.5,1.1,1.3,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,8</Font>
              <Guid>d1f1f79460c54182acbc702ddbbdf2bf</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text20</Name>
              <Page isRef="3" />
              <Parent isRef="42" />
              <Text>开票员</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text20>
            <Text21 Ref="46" type="Text" isKey="true">
              <Border>All;[155:155:155];1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0.5,1.8,1.3,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,8</Font>
              <Guid>3fe75e2dad70499d96b162002e0793d2</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text21</Name>
              <Page isRef="3" />
              <Parent isRef="42" />
              <Text>日期</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text21>
            <Text23 Ref="47" type="Text" isKey="true">
              <Border>All;[155:155:155];1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.8,1.1,4.5,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>06a72858a3074997944b42456636d04d</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text23</Name>
              <Page isRef="3" />
              <Parent isRef="42" />
              <Text>{开票员}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text23>
            <Text24 Ref="48" type="Text" isKey="true">
              <Border>All;[155:155:155];1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>1.8,1.8,4.5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>bc899a64c0d84fdfb3241d1ea8c1bf05</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text24</Name>
              <Page isRef="3" />
              <Parent isRef="42" />
              <Text>{日期}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text24>
            <Text30 Ref="49" type="Text" isKey="true">
              <Border>All;[155:155:155];1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.5,0,1.3,1.1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,8</Font>
              <Guid>9ec2e1ef4576433ba82b36cc8f881099</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text30</Name>
              <Page isRef="3" />
              <Parent isRef="42" />
              <Text>大写</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text30>
            <Text31 Ref="50" type="Text" isKey="true">
              <Border>All;[155:155:155];1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>8.8,0,4.5,1.1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9,Regular,Point,False,0</Font>
              <Guid>91a71bd5f3b344d49ad3e89e3fca627e</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text31</Name>
              <Page isRef="3" />
              <Parent isRef="42" />
              <Text>{MoneyCn(Sum(GroupHeaderBand1,门诊发票.Mz_Money))}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text31>
            <Text32 Ref="51" type="Text" isKey="true">
              <Border>All;[155:155:155];1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.5,1.1,1.3,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,8</Font>
              <Guid>c81d7459f22b4a1094dd7408c9e01e9d</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text32</Name>
              <Page isRef="3" />
              <Parent isRef="42" />
              <Text>开票员</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text32>
            <Text33 Ref="52" type="Text" isKey="true">
              <Border>All;[155:155:155];1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.5,1.8,1.3,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,8</Font>
              <Guid>a6203141de4a4ff09259d4d6165160d6</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text33</Name>
              <Page isRef="3" />
              <Parent isRef="42" />
              <Text>日期</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text33>
            <Text35 Ref="53" type="Text" isKey="true">
              <Border>All;[155:155:155];1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>8.8,1.1,4.5,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>b55bc761534a48eaa51316dfbff87ce7</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text35</Name>
              <Page isRef="3" />
              <Parent isRef="42" />
              <Text>{开票员}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text35>
            <Text36 Ref="54" type="Text" isKey="true">
              <Border>All;[155:155:155];1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>8.8,1.8,4.5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>76560edf4f7349198225f1c36bf3ce41</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text36</Name>
              <Page isRef="3" />
              <Parent isRef="42" />
              <Text>{日期}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text36>
            <Text48 Ref="55" type="Text" isKey="true">
              <Border>All;[155:155:155];1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>14.4,0,1.3,1.1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,8</Font>
              <Guid>10d1b12bbc904e4ba4e819c3527662f9</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text48</Name>
              <Page isRef="3" />
              <Parent isRef="42" />
              <Text>大写</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text48>
            <Text49 Ref="56" type="Text" isKey="true">
              <Border>All;[155:155:155];1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15.7,0,4.5,1.1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,9,Regular,Point,False,0</Font>
              <Guid>579fb2cc00a74e3989537b1c568c0173</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text49</Name>
              <Page isRef="3" />
              <Parent isRef="42" />
              <Text>{MoneyCn(Sum(GroupHeaderBand1,门诊发票.Mz_Money))}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text49>
            <Text50 Ref="57" type="Text" isKey="true">
              <Border>All;[155:155:155];1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>14.4,1.1,1.3,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,8</Font>
              <Guid>fe6490e0ddc8492e96acf7057b5cd914</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text50</Name>
              <Page isRef="3" />
              <Parent isRef="42" />
              <Text>开票员</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text50>
            <Text51 Ref="58" type="Text" isKey="true">
              <Border>All;[155:155:155];1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>14.4,1.8,1.3,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>Arial,8</Font>
              <Guid>9453ec38fce3423a8a689cb28ec7fd02</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text51</Name>
              <Page isRef="3" />
              <Parent isRef="42" />
              <Text>日期</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text51>
            <Text53 Ref="59" type="Text" isKey="true">
              <Border>All;[155:155:155];1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15.7,1.1,4.5,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>e0142dd7e01a43fab2ebbb2c736881da</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text53</Name>
              <Page isRef="3" />
              <Parent isRef="42" />
              <Text>{开票员}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text53>
            <Text54 Ref="60" type="Text" isKey="true">
              <Border>All;[155:155:155];1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15.7,1.8,4.5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,8</Font>
              <Guid>67fe37eb7aa8424eb7d558c7845153c3</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text54</Name>
              <Page isRef="3" />
              <Parent isRef="42" />
              <Text>{日期}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text54>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>GroupFooterBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </GroupFooterBand1>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>0f5b31ea18054d43acd798586c22c0ef</Guid>
      <Margins>1.1,1.2,0,0</Margins>
      <Name>Page1</Name>
      <PageHeight>9.9</PageHeight>
      <PageWidth>23.4</PageWidth>
      <Report isRef="0" />
      <Watermark Ref="61" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page1>
  </Pages>
  <PrinterSettings Ref="62" type="Stimulsoft.Report.Print.StiPrinterSettings" isKey="true" />
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>唐山门诊发票</ReportAlias>
  <ReportChanged>6/18/2013 9:00:43 AM</ReportChanged>
  <ReportCreated>2/24/2012 10:02:45 AM</ReportCreated>
  <ReportFile>.\Rpt\唐山门诊发票(汇总).mrt</ReportFile>
  <ReportGuid>b05c364af2be424fb67e888a0b8a2e55</ReportGuid>
  <ReportName>唐山门诊发票</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2011.2.1026</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class Report : Stimulsoft.Report.StiReport
    {
        public Report()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify#endregion StiReport Designer generated code - do not modify
		
		public	string NumberCn(decimal ANumber) 

		{

			const string cPointCn = "点十百千万十百千亿十百千";

			const string cNumberCn = "零一二三四五六七八九";

			string S = ANumber.ToString();

			if (S == "0") return "" + cPointCn[0];

			if (!S.Contains(".")) S += ".";

			int P = S.IndexOf(".");

			string Result = "";

    

			for (int i = 0; i &lt; S.Length; i++)

			{

				if (P == i)

				{

					Result = Result.Replace("零十零", "零");

					Result = Result.Replace("零百零", "零");

					Result = Result.Replace("零千零", "零");

					Result = Result.Replace("零十", "零");

					Result = Result.Replace("零百", "零");

					Result = Result.Replace("零千", "零");

					Result = Result.Replace("零万", "万");

					Result = Result.Replace("零亿", "亿");

					Result = Result.Replace("亿万", "亿");

					Result = Result.Replace("零点", "点");

				}

				else

				{

					if (P &gt; i)

						Result += "" + cNumberCn[S[i] - '0'] + cPointCn[P - i - 1];

					else Result += "" + cNumberCn[S[i] - '0'];

				}

			}

			if (Result.Substring(Result.Length - 1, 1) == "" + cPointCn[0])

				Result = Result.Remove(Result.Length - 1); // 一点-&gt; 一

    

			if (Result[0] == cPointCn[0])

				Result = cNumberCn[0] + Result; // 点三-&gt; 零点三

 

			if ((Result.Length &gt; 1) &amp;&amp; (Result[1] == cPointCn[1]) &amp;&amp; 

				(Result[0] == cNumberCn[1]))

				Result = Result.Remove(0, 1); // 一十三-&gt; 十三

			return Result;

		}

 

		public	string MoneyCn(decimal ANumber)

		{

			string V_Fs="";
			if (ANumber &lt; 0)
			{
				ANumber = -ANumber;
				V_Fs="负";
			}
			else
			{
				V_Fs = "";
			}

			
			if (ANumber == 0) return "零";

			string Result = NumberCn(Math.Truncate(ANumber * 100) / 100);

			Result = Result.Replace("一", "壹");

			Result = Result.Replace("二", "贰");

			Result = Result.Replace("三", "叁");

			Result = Result.Replace("四", "肆");

			Result = Result.Replace("五", "伍");

			Result = Result.Replace("六", "陆");

			Result = Result.Replace("七", "柒");

			Result = Result.Replace("八", "捌");

			Result = Result.Replace("九", "玖");

			Result = Result.Replace("九", "玖");

			Result = Result.Replace("十", "拾");

			Result = Result.Replace("百", "佰");

			Result = Result.Replace("千", "仟");

			if (Result.Contains("点"))

			{

				int P = Result.IndexOf("点");
				if (P + 3 &gt; Result.Length)
				{
					Result = Result+ "分";
				}
				else

				{
					Result = Result.Insert(P + 3, "分");
				}
			//	Result = Result.Insert(P + 3, "分");

				Result = Result.Insert(P + 2, "角");

				Result = Result.Replace("点", "圆");

				Result = Result.Replace("角分", "角");

				Result = Result.Replace("零分", "");

				Result = Result.Replace("零角", "");

				Result = Result.Replace("分角", "");

				if (Result.Substring(0, 2) == "零圆")

					Result = Result.Replace("零圆", "");

			} else Result += "圆整";

			Result =  V_Fs+Result;

			return Result;

		}
	}
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>