﻿Imports System.Data.SqlClient
Imports System.Windows.Forms
Imports ZtHis.Emr.MbDesign
Imports System.Text

Public Class EmrEditBl

    Dim m_Row As DataRow
    Dim Emr_bl As New BLLOld.B_Emr_Bl
    ' Dim V_Finish As Boolean = False             '初始化完成
    ' Dim My_table As DataTable
    Public Shared vSelectedNodeTag As String

    Dim emr_bl_mode As New ModelOld.M_Emr_Bl
    Dim my_row As DataRow
    Dim bllEmr_datafield As New BLLOld.B_Emr_DataField
    Dim lastPageNo As Long  '上次打印内容最后位置的页码，从数据库中或文档属性中取出
    Dim lastRowNo As Long  '上次打印内容最后位置的行号，从数据库中或文档属性中取出


#Region "传参"
    Dim insertflage As Boolean
#End Region

    Public Sub New(ByVal modelinfer As ModelOld.M_Emr_Bl, ByVal flage As Boolean)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        emr_bl_mode = modelinfer
        insertflage = flage
        ' 在 InitializeComponent() 调用之后添加任何初始化。
    End Sub
    Private Sub EmrEditBl_Load(sender As System.Object, e As System.EventArgs) Handles MyBase.Load
        'Init_Tree()
        AxNsoControl1.Visible = False
        Form_Init()
        AxNsoControl1.Visible = True
    End Sub

#Region "窗体__事件"

    Private Sub Form_Init()
        ' AxNsoControl1.RemoveInternalSubMenuItem '移除自定义菜单栏内部子菜单项


        AxNsoControl1.OpenDocumentWithStream(emr_bl_mode.Mb_Nr, 2)
        AxNsoControl1.GetTrackRevisions()


        AxNsoControl1.SetMenuBarVisible(True) '显示和隐藏菜单栏

        Call Data_Query()

        If my_row("Emr_GuiDang") Is DBNull.Value OrElse my_row("Emr_GuiDang") = False Then
            If insertflage = True Then

                Dim lstrName As String, lstrID() As String, lstrValue As String
                Dim i As Integer

                lstrName = AxNsoControl1.GetAllControlNameByCurrentDoc()
                If lstrName <> "" Then
                    lstrID = Split(lstrName, ",")

                    For i = LBound(lstrID) To UBound(lstrID) - 1
                        '获取指定的病历元素信息
                        lstrValue = AxNsoControl1.GetNewControlProp(lstrID(i), "Reserve")
                        If bllEmr_datafield.Exists(lstrValue) Then
                            AxNsoControl1.SetNewControlText(lstrID(i), my_row.Item(lstrValue)&"")
                        End If
                    Next
                End If
            Else

                If HisVar.HisVar.JsrYsCode = emr_bl_mode.Ys_Code And HisPara.PublicConfig.EmrMark = "否" Then '自己修改自己的
                    AxNsoControl1.BrowseTemplet(1, 0)
                Else
                    AxNsoControl1.ShowRecension(1)
                    AxNsoControl1.BrowseTemplet(10, 0)
                    AxNsoControl1.SetRecensionInfo(HisVar.HisVar.JsrYsCode, HisVar.HisVar.JsrYsName, "", 1, RGB(HisVar.HisVar.JsrColor.R, HisVar.HisVar.JsrColor.G, HisVar.HisVar.JsrColor.B))
                    AxNsoControl1.SwitchRecension(True)
                    AxNsoControl1.EnableRedlineReview(False)
                End If
            End If

        Else
            AxNsoControl1.RemoveExternalSubMenuItem("HenJi", "MenuAddIn4")  '切换为设计模式
            AxNsoControl1.RemoveExternalSubMenuItem("HenJi", "MenuAddIn5")  '切换为设计模式

            AxNsoControl1.BrowseTemplet(10, 1)

        End If



        AxNsoControl1.InsertExternalSubMenuItem("PickList", "MenuAddIn1", "保存", AppDomain.CurrentDomain.BaseDirectory & "Resources\Icon_1469.png")
        AxNsoControl1.InsertExternalSubMenuItem("InsertMenu", "MenuAddIn2", "基本元素", AppDomain.CurrentDomain.BaseDirectory & "Resources\insert.png")
        AxNsoControl1.InsertExternalSubMenuItem("PickList", "MenuAddIn3", "打印", AppDomain.CurrentDomain.BaseDirectory & "Resources\打印.bmp")
        AxNsoControl1.InsertExternalSubMenuItem("PickList", "MenuAddIn8", "选择续打", AppDomain.CurrentDomain.BaseDirectory & "Resources\打印.bmp")
        AxNsoControl1.InsertExternalSubMenuItem("PickList", "MenuAddIn9", "自动续打", AppDomain.CurrentDomain.BaseDirectory & "Resources\打印.bmp")
        AxNsoControl1.CreateUserRootMenuItem("HenJi", "痕迹") '添加菜单项
        AxNsoControl1.InsertExternalSubMenuItem("HenJi", "MenuAddIn4", "关闭痕迹", AppDomain.CurrentDomain.BaseDirectory & "img\关闭痕迹.png")
        AxNsoControl1.InsertExternalSubMenuItem("HenJi", "MenuAddIn5", "显示痕迹", AppDomain.CurrentDomain.BaseDirectory & "img\开启痕迹.png")
        AxNsoControl1.InsertExternalSubMenuItem("HenJi", "MenuAddIn6", "开启审阅窗格", AppDomain.CurrentDomain.BaseDirectory & "img\开启审阅.png")
        AxNsoControl1.InsertExternalSubMenuItem("HenJi", "MenuAddIn7", "关闭审阅窗格", AppDomain.CurrentDomain.BaseDirectory & "img\关闭审阅.png")
        If HisPara.PublicConfig.EmrPrintType = 1 Then
            'AxNsoControl1.RemoveExternalSubMenuItem("PickList", "MenuAddIn8")
            AxNsoControl1.RemoveExternalSubMenuItem("PickList", "MenuAddIn9")
        End If
        AxNsoControl1.RemoveExternalSubMenuItem("standardbar", " DragLinePrint") '不显示拉线打印
        AxNsoControl1.RemoveExternalSubMenuItem("standardbar", " DragLinePrint_Print") '不显示打印
        AxNsoControl1.InsertLineNumber(False, 1, False, False) '在文档中插入行号
        AxNsoControl1.SetRulersVisible(True, True) '显示标尺
        AxNsoControl1.SetStatusBarVisible(True) '显示状态栏
        AxNsoControl1.SetToolBarsVisible("standard", True) '显示工具栏
        AxNsoControl1.SetToolBarsVisible("textobjectbar", True) '显示工具栏
        AxNsoControl1.EnableCopyFromExternal(True) '设置是否可以从外部(比如IE，记事本，VS等等)拷贝到odt文档
        AxNsoControl1.SetCanCopyWithStruct(True)  '置控件内拷贝是否可以带结构信息
        AxNsoControl1.AddFileListener()     '开启文件监听，可在office崩溃后自动恢复当前编辑的文档
        AxNsoControl1.AddGlobalDocumentListener() '开启全局监听器，监听的事件非常多
        AxNsoControl1.SetGlobalDocumentListener(True, True, True, True) '设置在开启全局监听时，哪些对象会产生相应的事件
        AxNsoControl1.SetUserUIListener(False, True, False) '对用户添加做监听
        AxNsoControl1.AddKeyListener(0) '键盘监听
        MenuVisible(False)

    End Sub

    Private Sub Data_Query()
        Dim strSql As New StringBuilder
        strSql.Append("SELECT yy_name, bl.Yy_code,bl.bl_code,bl.Ks_Code,Ks_Name Blks_Name,V_YyBc.Bc_Code,Bc_Name Cw_Name,Bc_Jc,Bq_Code,Bq_Name,jb_name,Ry_BlCode,isnull(Ry_CyDate,'')")
        strSql.Append("Jb_Code,Ry_Name,Ry_Jc,Ry_Sex,Ry_Sfzh,Ry_Address,Jb_Code,Jb_Name ,bl.Ys_Code,Ys_Name Blys_Name,Bl.Bxlb_Code,Bxlb_Name,Ry_RyDate,Ry_CyDate,")
        strSql.Append("isnull(Jf_Money,0)Jf_Money,Isnull(Xf_YpMoney,0)Xf_YpMoney,Isnull(Xf_Money,0)-Isnull(Xf_YpMoney,0) Xf_XmMoney,Ry_ZyTs,Emr_GuiDang, ")
        strSql.Append("ISNULL(Xf_Money,0)Xf_Money,isnull(Jf_Money,0)-isnull(Xf_Money,0) AS New_Money,DATEDIFF(DAY,Ry_RyDate,GETDATE()) HospitalDay,Cy_Qr,")
        strSql.Append(" CASE ISNULL(Ry_Sfzh,'') WHEN '' then ''  ELSE year(getdate())-substring(ry_sfzh,7,4) END Ry_Age,Ry_MinZu,Ry_Tele ")
        strSql.Append(" FROM (SELECT * FROM dbo.Bl WHERE bl_code='" & emr_bl_mode.Bl_Code & "' ")
        strSql.Append(" ) Bl LEFT JOIN dbo.Zd_YyKs ON  Zd_YyKs.Ks_Code = Bl.Ks_Code")
        strSql.Append(" LEFT JOIN dbo.Zd_YyYs ON Zd_YyYs.Ys_Code = Bl.Ys_Code ")
        strSql.Append(" LEFT JOIN dbo.Zd_Bxlb ON  Zd_Bxlb.Bxlb_Code = Bl.Bxlb_Code")
        strSql.Append(" left join  V_YyBc ON  Bl.Bc_Code=V_YyBc.Bc_Code ")
        strSql.Append(" LEFT join (Select Sum(Cf_Money) AS Xf_Money,Sum(Cf_YpMoney) as Xf_YpMoney,Bl_Code From Bl_Cf where Cf_Qr='是' Group By Bl_Code) a on Bl.Bl_Code=a.Bl_Code ")
        strSql.Append(" left join(Select Sum(Jf_Money) AS Jf_Money,Bl_Code From Bl_Jf Group By Bl_Code) b on Bl.Bl_Code = b.Bl_Code ")

        strSql.Append(" left join  dbo.zd_yy on zd_yy.yy_code = Bl.yy_code ")
        my_row = HisVar.HisVar.Sqldal.Query(strSql.ToString()).Tables(0).Rows(0)
    End Sub
    Private Sub MenuVisible(ByVal flag As Boolean)
        If flag = True Then
            AxNsoControl1.ShowMenuItem("newDoc") '显示“新建”菜单项
            AxNsoControl1.ShowMenuItem("Open")   '显示“打开”菜单项
            AxNsoControl1.ShowMenuItem("Save") '保存
            AxNsoControl1.ShowMenuItem("SaveAs") '另存为
            AxNsoControl1.ShowMenuItem("Quit") '退出
            AxNsoControl1.ShowMenuItem("About") '关于
            AxNsoControl1.ShowMenuItem("Print")
            'AxNsoControl1.ShowMenuItem("PrintPreview")
            AxNsoControl1.ShowMenuItem("PickList")
        Else
            AxNsoControl1.HideMenuItem("newDoc")
            AxNsoControl1.HideMenuItem("Open")
            AxNsoControl1.HideMenuItem("Save")
            AxNsoControl1.HideMenuItem("SaveAs")
            AxNsoControl1.HideMenuItem("Quit")
            AxNsoControl1.HideMenuItem("About")
            AxNsoControl1.HideMenuItem("Print")
            'AxNsoControl1.HideMenuItem("PrintPreview")
            AxNsoControl1.HideMenuItem("PickList")
        End If
        AxNsoControl1.SetToolbarItemVisible("standardbar", "Print", flag)
        'AxNsoControl1.SetToolbarItemVisible("standardbar", "PrintPreview", flag)
        AxNsoControl1.SetToolbarItemVisible("standardbar", "ExportDirectToPDF", flag)
        AxNsoControl1.SetToolbarItemVisible("standardbar", "PrintDefault", flag)
        AxNsoControl1.SetToolbarItemVisible("standardbar", "DragLinePrint", flag)
        AxNsoControl1.SetToolbarItemVisible("standardbar", "DragLinePrint_Print", flag)
    End Sub


#End Region


#Region "自定义函数"
    Private Sub Data_Add()
        '先保存到临时文件中，然后读出文件内容，再存入数据库
        Dim lbytContents() As Byte
        Dim lstrFilename As String

        lstrFilename = System.Windows.Forms.Application.StartupPath & "\" & Format(Now, "yyyymmddhhmmss") & Format(Rnd() * 100, "000") & ".odt"
        AxNsoControl1.SaveAs2(lstrFilename, True)      '将当前文件另存为一个临时文件
        AxNsoControl1.SetDocModified2(False)    '将文件的修改标志设为“未修改”

        lbytContents = My.Computer.FileSystem.ReadAllBytes(lstrFilename)
        My.Computer.FileSystem.DeleteFile(lstrFilename)     '删除该临时文件


        '数据更新
        Try

            With emr_bl_mode

                .Mb_Nr = lbytContents
                .AddDate = Now()
            End With
            If Emr_bl.Add(emr_bl_mode) Then
                HisControl.msg.Show("病例增加成功!", "提示")
            Else
                HisControl.msg.Show("病例增加失败!", "提示")
                Exit Sub
            End If

        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
        Finally

        End Try

        '数据清空
        insertflage = False

    End Sub

    Private Sub Data_Edit()
        '先保存到临时文件中，然后读出文件内容，再存入数据库
        Dim lbytContents() As Byte
        Dim lstrFilename As String

        lstrFilename = System.Windows.Forms.Application.StartupPath & "\" & Format(Now, "yyyymmddhhmmss") & Format(Rnd() * 100, "000") & ".odt"
        AxNsoControl1.SaveAs2(lstrFilename, True)      '将当前文件另存为一个临时文件
        AxNsoControl1.SetDocModified2(False)    '将文件的修改标志设为“未修改”

        lbytContents = My.Computer.FileSystem.ReadAllBytes(lstrFilename)
        My.Computer.FileSystem.DeleteFile(lstrFilename)     '删除该临时文件

        '数据更新

        Try
            With emr_bl_mode
                .Mb_Nr = lbytContents
            End With

            If Emr_bl.Update(emr_bl_mode) Then
                HisControl.msg.Show("病例修改成功!", "提示")
            Else
                HisControl.msg.Show("病例修改失败!", "提示")
            End If
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
        End Try
    End Sub

    Private Sub editflage()
        If my_row("Emr_GuiDang") Is DBNull.Value OrElse my_row("Emr_GuiDang") = False Then
        Else
            MsgBox("病人已经归档不可更改病人电子病例", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
            Exit Sub

        End If


        If insertflage = True Then
            Data_Add()
        Else
            Data_Edit()
        End If
    End Sub
    Private Sub print()



        If HisPara.PublicConfig.EmrPrintType = 1 Then
            Call editflage()
            Dim startdate As Date = Now
            Dim Downdata() As Byte = Nothing
            Try
                HisVar.HisVar.DzblPrintservice.UploadFile(emr_bl_mode.Mb_Nr, HisVar.HisVar.WsyName, emr_bl_mode.Bl_Code & "(" & emr_bl_mode.id & ").odt")
                HisVar.HisVar.DzblPrintservice.pdfDel(HisVar.HisVar.WsyName, "" & emr_bl_mode.Bl_Code & "(" & emr_bl_mode.id & ").pdf")

                While Downdata Is Nothing
                    Downdata = HisVar.HisVar.DzblPrintservice.DownLoadPdf(HisVar.HisVar.WsyName, emr_bl_mode.Bl_Code & "(" & emr_bl_mode.id & ").pdf")
                    If DateDiff(DateInterval.Minute, startdate, Now) = 1 Then
                        MsgBox("文件请求超时，请稍后重试！", MsgBoxStyle.Information, "提示")
                        Exit Sub
                    End If
                End While
            Catch ex As Exception
                MsgBox(ex.Message.ToString, MsgBoxStyle.Information, "提示：")
                Exit Sub
            End Try
            System.IO.File.WriteAllBytes(System.Windows.Forms.Application.StartupPath & "\" & emr_bl_mode.Bl_Code & "(" & emr_bl_mode.id & ").pdf", Downdata)
            Dim startInfo As New ProcessStartInfo()
            startInfo.UseShellExecute = True
            startInfo.Verb = "Print"
            startInfo.CreateNoWindow = True
            startInfo.WindowStyle = ProcessWindowStyle.Hidden
            startInfo.FileName = System.Windows.Forms.Application.StartupPath & "\" & emr_bl_mode.Bl_Code & "(" & emr_bl_mode.id & ").pdf"
            Process.Start(startInfo)
            'AxNsoControl1.RemoveExternalSubMenuItem("PickList", "MenuAddIn8")

        Else

            AxNsoControl1.JumpToFileEnd() '将光标跳转到打印内容的最后位置
            emr_bl_mode.Print_Page = AxNsoControl1.GetCurrentCursorPage()
            emr_bl_mode.Print_Row = AxNsoControl1.GetCurrentRowIndex()

            If AxNsoControl1.GetSurplusLinesOfOnePage(emr_bl_mode.Print_Page) = 0 Then
                emr_bl_mode.Print_Page = emr_bl_mode.Print_Page + 1       '当前页满页了，下次要从新的页打印
                emr_bl_mode.Print_Row = 1
            End If
            Call editflage()
            AxNsoControl1.PrintDoc(False)
            ' AxNsoControl1.Print("", 1, "1-3")


        End If

    End Sub

    Private Sub XPrint()

        Dim printHeader As Boolean '首页是否打印页眉
        lastPageNo = emr_bl_mode.Print_Page
        lastRowNo = emr_bl_mode.Print_Row
        AxNsoControl1.JumpToFileEnd() '将光标跳转到打印内容的最后位置
        emr_bl_mode.Print_Page = AxNsoControl1.GetCurrentCursorPage()
        emr_bl_mode.Print_Row = AxNsoControl1.GetCurrentRowIndex()
        If AxNsoControl1.GetSurplusLinesOfOnePage(emr_bl_mode.Print_Page) = 0 Then
            emr_bl_mode.Print_Page = emr_bl_mode.Print_Page + 1       '当前页满页了，下次要从新的页打印
            emr_bl_mode.Print_Row = 1
        End If
        Call editflage()
        If lastRowNo = 1 Then
            printHeader = True '从新的一页开始打印，要打印页眉
        Else
            printHeader = True '接续上页开始打印，不要打印页眉
        End If

        'AxNsoControl1.PrintDocByLine(True, printHeader, 1, lastPageNo, lastRowNo, emr_bl_mode.Print_Page, emr_bl_mode.Print_Row)
        AxNsoControl1.SelectOneArea(lastPageNo, lastRowNo, emr_bl_mode.Print_Page, emr_bl_mode.Print_Row)
        AxNsoControl1.EnableFloatbar(True)

        AxNsoControl1.PreviewDocBySelect()
        'AxNsoControl1.PrintDocBySelect(printHeader)

    End Sub
#End Region
#Region "控件动作"
    Private Sub AxNsoControl1_NsoUserMenuItemEvent(sender As Object, e As AxNsoOfficeLib._INsoControlEvents_NsoUserMenuItemEventEvent) Handles AxNsoControl1.NsoUserMenuItemEvent
        If e.nID = 1 Then
            Call editflage()
        ElseIf e.nID = 2 Then
            Dim frm As New MbElement()
            frm.Owner = Me
            frm.StartPosition = Windows.Forms.FormStartPosition.CenterScreen
            If frm.ShowDialog() = Windows.Forms.DialogResult.OK Then

                Select Case frm.strControlType
                    Case "1"
                        If frm.objReserve = True Then
                            AxNsoControl1.InsertNewControlAtCurrentCursor("MultiCombox" &
           AxNsoControl1.GetNewControlCountByType(EnumNewControl.MultiCombox), frm.strName, EnumNewControl.MultiCombox)

                            For Each row In frm.My_Table.Rows
                                AxNsoControl1.SetCompoundBoxCodeAndValue("MultiCombox" &
          (AxNsoControl1.GetNewControlCountByType(EnumNewControl.MultiCombox) - 1),
           row("ItemText"), frm.My_Table.Rows.IndexOf(row), 4)
                            Next
                        Else
                            AxNsoControl1.InsertNewControlAtCurrentCursor("Combox" &
          AxNsoControl1.GetNewControlCountByType(EnumNewControl.Combox), frm.strName, EnumNewControl.Combox)

                            Dim s = AxNsoControl1.GetAllControlNameByCurrentDoc
                            For Each row In frm.My_Table.Rows
                                AxNsoControl1.SetCompoundBoxCodeAndValue("Combox" &
           (AxNsoControl1.GetNewControlCountByType(EnumNewControl.Combox) - 1),
           row("ItemText"), frm.My_Table.Rows.IndexOf(row), 1)
                            Next
                        End If
                    Case "2"
                        AxNsoControl1.InsertNewControlAtCurrentCursor("DefaultTextBox" &
   AxNsoControl1.GetNewControlCountByType(EnumNewControl.TextBox), frm.strName, EnumNewControl.TextBox)

                        AxNsoControl1.SetNewControlProp("DefaultTextBox" &
  AxNsoControl1.GetNewControlCountByType(EnumNewControl.TextBox) - 1, "Reserve", frm.objReserve)
                    Case "3"
                        AxNsoControl1.InsertNewControlAtCurrentCursor("DataFieldTextBox" &
AxNsoControl1.GetNewControlCountByType(EnumNewControl.TextBox), frm.strName, EnumNewControl.TextBox)

                        AxNsoControl1.SetNewControlProp("DataFieldTextBox" &
AxNsoControl1.GetNewControlCountByType(EnumNewControl.TextBox) - 1, "Reserve", frm.objReserve)
                End Select
            End If
        ElseIf e.nID = 3 Then
            Call print()
        ElseIf e.nID = 4 Then
            AxNsoControl1.BrowseTemplet(10, 0)
        ElseIf e.nID = 5 Then
            AxNsoControl1.BrowseTemplet(2, 0)
            AxNsoControl1.InsertLineNumber(False, 1, False, False)
        ElseIf e.nID = 6 Then
            AxNsoControl1.EnableRedlineReview(True)
        ElseIf e.nID = 7 Then
            AxNsoControl1.EnableRedlineReview(False)

        ElseIf e.nID = 9 Then
            XPrint()
        ElseIf e.nID = 8 Then
            'AxNsoControl1.EnableFloatbar(True) '进入预览状态显示预览工具栏

            'AxNsoControl1.PreviewDocBySelect()  '打印预览
            ' AxNsoControl1.PrintDocBySelect(True)  '拉线打
            ' 印
            AxNsoControl1.EnableFloatbar(True)
            '进入预览状态显示预览工具栏
            AxNsoControl1.PreviewDocBySelect()
            If (MessageBox.Show("是否打印边框？", "提示", MessageBoxButtons.YesNo) = DialogResult.No) Then
                AxNsoControl1.SetPrintWithTableBorder(False)
            Else
                AxNsoControl1.SetPrintWithTableBorder(True)
            End If
            AxNsoControl1.PrintDocBySelect(False)
            '打印预览
            AxNsoControl1.Refresh()
        End If


    End Sub
    Private Sub AxNsoControl1_NsoKeyPressedEvent(ByVal sender As Object, ByVal e As AxNsoOfficeLib._INsoControlEvents_NsoKeyPressedEventEvent) Handles AxNsoControl1.NsoKeyPressedEvent
        If e.nKeyCode = 530 And e.nModifiers = 2 Then
            Call editflage()
        ElseIf e.nKeyCode = 527 And e.nModifiers = 2 Then
            Call print()
        End If
    End Sub
#End Region

End Class