﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class Cx_Cqyz
    Inherits HisControl.BaseChild

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(Cx_Cqyz))
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.C1ToolBar1 = New C1.Win.C1Command.C1ToolBar()
        Me.C1CommandHolder1 = New C1.Win.C1Command.C1CommandHolder()
        Me.c1Command1 = New C1.Win.C1Command.C1Command()
        Me.c1Command2 = New C1.Win.C1Command.C1Command()
        Me.C1CommandLink2 = New C1.Win.C1Command.C1CommandLink()
        Me.C1CommandLink1 = New C1.Win.C1Command.C1CommandLink()
        Me.C1TrueDBGrid1 = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.Panel2 = New System.Windows.Forms.Panel()
        Me.Cancle_Btn = New C1.Win.C1Input.C1Button()
        Me.Sure_Btn = New C1.Win.C1Input.C1Button()
        Me.C1ToolBar2 = New C1.Win.C1Command.C1ToolBar()
        Me.Panel1.SuspendLayout
        CType(Me.C1CommandHolder1,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.C1TrueDBGrid1,System.ComponentModel.ISupportInitialize).BeginInit
        Me.Panel2.SuspendLayout
        CType(Me.Cancle_Btn,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.Sure_Btn,System.ComponentModel.ISupportInitialize).BeginInit
        Me.SuspendLayout
        '
        'Panel1
        '
        Me.Panel1.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Panel1.Controls.Add(Me.C1ToolBar1)
        Me.Panel1.Dock = System.Windows.Forms.DockStyle.Top
        Me.Panel1.Location = New System.Drawing.Point(0, 0)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(1092, 27)
        Me.Panel1.TabIndex = 4
        '
        'C1ToolBar1
        '
        Me.C1ToolBar1.AccessibleName = "Tool Bar"
        Me.C1ToolBar1.CommandHolder = Me.C1CommandHolder1
        Me.C1ToolBar1.CommandLinks.AddRange(New C1.Win.C1Command.C1CommandLink() {Me.C1CommandLink2, Me.C1CommandLink1})
        Me.C1ToolBar1.Location = New System.Drawing.Point(3, 0)
        Me.C1ToolBar1.Name = "C1ToolBar1"
        Me.C1ToolBar1.Size = New System.Drawing.Size(118, 24)
        Me.C1ToolBar1.Text = "C1ToolBar1"
        Me.C1ToolBar1.VisualStyle = C1.Win.C1Command.VisualStyle.Classic
        Me.C1ToolBar1.VisualStyleBase = C1.Win.C1Command.VisualStyle.Classic
        '
        'C1CommandHolder1
        '
        Me.C1CommandHolder1.Commands.Add(Me.c1Command1)
        Me.C1CommandHolder1.Commands.Add(Me.c1Command2)
        Me.C1CommandHolder1.Owner = Me
        '
        'c1Command1
        '
        Me.c1Command1.Image = CType(resources.GetObject("c1Command1.Image"),System.Drawing.Image)
        Me.c1Command1.Name = "c1Command1"
        Me.c1Command1.ShortcutText = ""
        Me.c1Command1.Text = "全选"
        '
        'c1Command2
        '
        Me.c1Command2.Image = CType(resources.GetObject("c1Command2.Image"),System.Drawing.Image)
        Me.c1Command2.Name = "c1Command2"
        Me.c1Command2.ShortcutText = ""
        Me.c1Command2.Text = "撤销（&U）"
        '
        'C1CommandLink2
        '
        Me.C1CommandLink2.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image),C1.Win.C1Command.ButtonLookFlags)
        Me.C1CommandLink2.Command = Me.c1Command2
        Me.C1CommandLink2.Text = "撤销"
        '
        'C1CommandLink1
        '
        Me.C1CommandLink1.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image),C1.Win.C1Command.ButtonLookFlags)
        Me.C1CommandLink1.Command = Me.c1Command1
        Me.C1CommandLink1.SortOrder = 1
        '
        'C1TrueDBGrid1
        '
        Me.C1TrueDBGrid1.GroupByCaption = "将列头拖拽到这里以便按照该列进行分组"
        Me.C1TrueDBGrid1.Images.Add(CType(resources.GetObject("C1TrueDBGrid1.Images"),System.Drawing.Image))
        Me.C1TrueDBGrid1.Location = New System.Drawing.Point(411, 160)
        Me.C1TrueDBGrid1.Name = "C1TrueDBGrid1"
        Me.C1TrueDBGrid1.PreviewInfo.Caption = "PrintPreview窗口"
        Me.C1TrueDBGrid1.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.C1TrueDBGrid1.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.C1TrueDBGrid1.PreviewInfo.ZoomFactor = 75R
        Me.C1TrueDBGrid1.PrintInfo.MeasurementDevice = C1.Win.C1TrueDBGrid.PrintInfo.MeasurementDeviceEnum.Screen
        Me.C1TrueDBGrid1.PrintInfo.MeasurementPrinterName = Nothing
        Me.C1TrueDBGrid1.PrintInfo.PageSettings = CType(resources.GetObject("C1TrueDBGrid1.PrintInfo.PageSettings"),System.Drawing.Printing.PageSettings)
        Me.C1TrueDBGrid1.Size = New System.Drawing.Size(240, 150)
        Me.C1TrueDBGrid1.TabIndex = 10
        Me.C1TrueDBGrid1.Text = "C1TrueDBGrid1"
        Me.C1TrueDBGrid1.UseCompatibleTextRendering = false
        Me.C1TrueDBGrid1.PropBag = resources.GetString("C1TrueDBGrid1.PropBag")
        '
        'Panel2
        '
        Me.Panel2.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Panel2.Controls.Add(Me.Cancle_Btn)
        Me.Panel2.Controls.Add(Me.Sure_Btn)
        Me.Panel2.Controls.Add(Me.C1ToolBar2)
        Me.Panel2.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.Panel2.Location = New System.Drawing.Point(0, 647)
        Me.Panel2.Name = "Panel2"
        Me.Panel2.Size = New System.Drawing.Size(1092, 27)
        Me.Panel2.TabIndex = 11
        '
        'Cancle_Btn
        '
        Me.Cancle_Btn.Location = New System.Drawing.Point(561, 1)
        Me.Cancle_Btn.Name = "Cancle_Btn"
        Me.Cancle_Btn.Size = New System.Drawing.Size(75, 23)
        Me.Cancle_Btn.TabIndex = 2
        Me.Cancle_Btn.Text = "取消"
        Me.Cancle_Btn.UseVisualStyleBackColor = true
        '
        'Sure_Btn
        '
        Me.Sure_Btn.Location = New System.Drawing.Point(454, 1)
        Me.Sure_Btn.Name = "Sure_Btn"
        Me.Sure_Btn.Size = New System.Drawing.Size(75, 23)
        Me.Sure_Btn.TabIndex = 1
        Me.Sure_Btn.Text = "确定"
        Me.Sure_Btn.UseVisualStyleBackColor = true
        '
        'C1ToolBar2
        '
        Me.C1ToolBar2.AccessibleName = "Tool Bar"
        Me.C1ToolBar2.CommandHolder = Me.C1CommandHolder1
        Me.C1ToolBar2.Location = New System.Drawing.Point(3, 0)
        Me.C1ToolBar2.Name = "C1ToolBar2"
        Me.C1ToolBar2.Size = New System.Drawing.Size(24, 24)
        Me.C1ToolBar2.Text = "C1ToolBar2"
        Me.C1ToolBar2.VisualStyle = C1.Win.C1Command.VisualStyle.Classic
        Me.C1ToolBar2.VisualStyleBase = C1.Win.C1Command.VisualStyle.Classic
        '
        'Cx_Cqyz
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6!, 12!)
        Me.ClientSize = New System.Drawing.Size(1092, 674)
        Me.Controls.Add(Me.Panel2)
        Me.Controls.Add(Me.C1TrueDBGrid1)
        Me.Controls.Add(Me.Panel1)
        Me.Name = "Cx_Cqyz"
        Me.Text = "长期医嘱"
        Me.Panel1.ResumeLayout(false)
        CType(Me.C1CommandHolder1,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.C1TrueDBGrid1,System.ComponentModel.ISupportInitialize).EndInit
        Me.Panel2.ResumeLayout(false)
        CType(Me.Cancle_Btn,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.Sure_Btn,System.ComponentModel.ISupportInitialize).EndInit
        Me.ResumeLayout(false)

End Sub
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents C1TrueDBGrid1 As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents C1ToolBar1 As C1.Win.C1Command.C1ToolBar
    Friend WithEvents C1CommandHolder1 As C1.Win.C1Command.C1CommandHolder
    Friend WithEvents C1CommandLink1 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents c1Command1 As C1.Win.C1Command.C1Command
    Friend WithEvents c1Command2 As C1.Win.C1Command.C1Command
    Friend WithEvents C1CommandLink2 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Panel2 As System.Windows.Forms.Panel
    Friend WithEvents Cancle_Btn As C1.Win.C1Input.C1Button
    Friend WithEvents Sure_Btn As C1.Win.C1Input.C1Button
    Friend WithEvents C1ToolBar2 As C1.Win.C1Command.C1ToolBar

End Class
