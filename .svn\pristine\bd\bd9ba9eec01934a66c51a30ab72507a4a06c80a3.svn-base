﻿Public Class Zd_Bcfp
    Dim My_Dataset As New DataSet
    Dim My_View As New DataView
    Dim My_Cm As CurrencyManager
    Dim Cw_Cm As CurrencyManager
    Dim Cw_View As New DataView
    Dim Load_Ok As Boolean
    Private Sub Zy_Pc_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Load_Ok = False
        '初始化TDBGrid
        Dim My_Grid1 As New BaseClass.C_Grid(Me.C1TrueDBGrid1)
        With My_Grid1
            .Init_Grid()
            .P_Dock(BaseClass.C_Grid.Dock.Fill)
            .Init_Column("病例编码", "Bl_Code", 0, "左", "")
            .Init_Column("病人简称", "Ry_Jc", 0, "左", "")
            .Init_Column("病人姓名", "Ry_Name", 70, "左", "")
            .Init_Column("性别", "Ry_Sex", 35, "中", "")
            .Init_Column("入院日期", "Ry_RyDate", 130, "中", "yyyy-MM-dd HH:mm")
            .Init_Column("病人类别", "BxLb_Name", 60, "左", "")
            .Init_Column("入院科室", "Ks_Name", 80, "左", "")
            .Init_Column("主治医师", "Ys_Name", 60, "左", "")
            .Init_Column("病区", "Bq_Name", 100, "左", "")
            .Init_Column("床位", "Bc_Name", 100, "左", "")
            .Init_Column("退床", "", 45, "中", "Check")
        End With
        If HisPara.PublicConfig.ZyCfKsXz = "是" Then
            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "SELECT Bl_Code,Ry_YlCode,BxLb_Name,Ry_Name,Ry_Jc,Ry_Sex,Ry_Sfzh,Ry_Csdate,Ry_Address,Ry_RyDate,Ry_BlCode,Ry_Tele,Jb_Name,Ys_Name,Ks_Name,V_YyBc.* FROM Zd_BxLb,Zd_YyYs,Zd_Yyks,Bl left join V_YyBc on  Bl.Bc_Code=V_YyBc.Bc_Code Where Bl.Ys_code=Zd_YyYs.Ys_code and Bl.Ks_Code=Zd_YyKs.Ks_Code  and Zd_BxLB.BxLb_Code=BL.BxLb_Code And Ry_CyDate is null and Bl.Ks_Code like '%" & HisVar.HisVar.XmKs & "%' ", "配床信息", True)
        Else
            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "SELECT Bl_Code,Ry_YlCode,BxLb_Name,Ry_Name,Ry_Jc,Ry_Sex,Ry_Sfzh,Ry_Csdate,Ry_Address,Ry_RyDate,Ry_BlCode,Ry_Tele,Jb_Name,Ys_Name,Ks_Name,V_YyBc.* FROM Zd_BxLb,Zd_YyYs,Zd_Yyks,Bl left join V_YyBc on  Bl.Bc_Code=V_YyBc.Bc_Code Where Bl.Ys_code=Zd_YyYs.Ys_code and Bl.Ks_Code=Zd_YyKs.Ks_Code  and Zd_BxLB.BxLb_Code=BL.BxLb_Code And Ry_CyDate is null ", "配床信息", True)
        End If

        With Me.C1TrueDBGrid1
            My_Cm = CType(BindingContext(My_Dataset, "配床信息"), CurrencyManager)
            .SetDataBinding(My_Dataset, "配床信息", True)

            .HScrollBar.Style = C1.Win.C1TrueDBGrid.ScrollBarStyleEnum.Automatic
            .Splits(0).DisplayColumns(0).Visible = False
            .Splits(0).DisplayColumns(1).Visible = False
            If RadioButton1.Checked = True Then

                .Splits(0).DisplayColumns(10).Visible = False
            End If
        End With
        My_View = My_Cm.List
        If RadioButton1.Checked = True Then
            My_View.RowFilter = "(Ry_Jc like '%" & C1TextBox1.Text & "%' OR Ry_Name like '%" & C1TextBox1.Text & "%') and Isnull(Bc_Name,'')=''"
        Else
            My_View.RowFilter = "(Ry_Jc like '%" & C1TextBox1.Text & "%' OR Ry_Name like '%" & C1TextBox1.Text & "%') and Isnull(Bc_Name,'')<>''"
        End If

        Dim My_Grid2 As New BaseClass.C_Grid(Me.C1TrueDBGrid2)
        With My_Grid2
            .Init_Grid()
            .P_Dock(BaseClass.C_Grid.Dock.Fill)
            .Init_Column("病区编码", "Bq_Code", 0, "左", "")
            .Init_Column("病区名称", "Bq_Name", 90, "左", "")
            .Init_Column("病床编码", "Bc_Code", 0, "中", "")
            .Init_Column("未使用病床", "Bc_Name", 85, "中", "")
            .Init_Column("配床", "", 50, "中", "Check")
        End With

        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "select Bc_Code,Bc_Jc,Bq_Name,Bc_Name,Bc_Memo,Bq_Code from V_YyBc where Yy_Code='" & HisVar.HisVar.WsyCode & "'  And  Not exists (Select distinct Bc_Code From Bl Where V_YyBc.Bc_code=Bl.Bc_Code And Ry_Cydate is  null and isnull(Bc_Code,'')<>'' ) Order By Bc_Code", "床位字典", True)
        With Me.C1TrueDBGrid2
            .SetDataBinding(My_Dataset, "床位字典", True)
            Cw_Cm = CType(BindingContext(.DataSource, .DataMember), CurrencyManager)
            .HScrollBar.Style = C1.Win.C1TrueDBGrid.ScrollBarStyleEnum.Automatic
            .Splits(0).DisplayColumns(0).Visible = False
            .Splits(0).DisplayColumns(0).Visible = False
            .Splits(0).DisplayColumns(2).Visible = False
            .ExtendRightColumn = False
        End With
        Cw_View = Cw_Cm.List
        Cw_View.Sort="Bc_Code"


        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "Select Bq_Code,Bq_Name from Zd_YyBq1", "病区字典", True)
        If My_Dataset.Tables("病区字典").Rows.Count = 0 Then
            C1Combo1.Visible = False
        Else
            Dim My_Combo As New BaseClass.C_Combo1(Me.C1Combo1)
            My_Combo.Init_TDBCombo()
            With C1Combo1
                Dim Bq_Row As DataRow
                .AddItem("所有病区")
                For Each Bq_Row In My_Dataset.Tables("病区字典").Rows
                    .AddItem(Bq_Row.Item("Bq_Name"))
                Next
                .SelectedIndex = 0
                .DropDownWidth = 113
                .Width = 123
                .Left = C1TrueDBGrid2.Left
            End With
        End If

        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "Select Ks_Code,Ks_Name from Zd_YyKs", "科室字典", True)

        Dim My_Combo2 As New BaseClass.C_Combo1(Me.C1Combo2)
        My_Combo2.Init_TDBCombo()
        With C1Combo2
            Dim Ks_Row As DataRow
            .AddItem("所有科室")
            For Each Ks_Row In My_Dataset.Tables("科室字典").Rows
                .AddItem(Ks_Row.Item("Ks_Name"))
            Next
            .SelectedIndex = 0
            .DropDownWidth = 113
            .Width = 123

        End With


        Load_Ok = True
    End Sub



    Private Sub RadioButton1_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles RadioButton1.CheckedChanged
        If Load_Ok = False Then Exit Sub
        If RadioButton1.Checked = True Then

            C1TrueDBGrid1.Splits(0).DisplayColumns(10).Visible = False
            My_View.RowFilter = "(Ry_Jc like '%" & C1TextBox1.Text & "%' OR Ry_Name like '%" & C1TextBox1.Text & "%') and Isnull(Bc_Name,'')=''"
        Else
            C1TrueDBGrid1.Splits(0).DisplayColumns(10).Visible = True
            My_View.RowFilter = "(Ry_Jc like '%" & C1TextBox1.Text & "%' OR Ry_Name like '%" & C1TextBox1.Text & "%') and Isnull(Bc_Name,'')<>''"

        End If

    End Sub


    Private Sub C1TrueDBGrid1_DoubleClick(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1TrueDBGrid1.DoubleClick
        If C1TrueDBGrid1.Col.ToString = 10 Then
            If MsgBox("确认要为【" & C1TrueDBGrid1.Columns("Ry_Name").Value & "】退床吗？", MsgBoxStyle.OkCancel + MsgBoxStyle.DefaultButton1, "提示：") = MsgBoxResult.Cancel Then Exit Sub
            HisVar.HisVar.Sqldal.ExecuteSql("Update Bl Set Bc_Code=NULL where Bl_Code='" & C1TrueDBGrid1.Columns("Bl_Code").Value & "'")
            Dim Tc_Row As DataRow = My_Cm.List(C1TrueDBGrid1.Row).Row
            Dim row As DataRow = My_Dataset.Tables("床位字典").NewRow()
            'Bc_Code,Bc_Jc,Bq_Name,Bc_Name,Bc_Memo,Bq_Code
            row("Bc_Code")=Tc_Row("Bc_Code")
            row("Bc_Jc")=Tc_Row("Bc_Jc")
            row("Bq_Name")=Tc_Row("Bq_Name")
            row("Bc_Name")=Tc_Row("Bc_Name")
            row("Bc_Memo")=Tc_Row("Bc_Memo")
            row("Bq_Code")=Tc_Row("Bq_Code")
            My_Dataset.Tables("床位字典").Rows.Add(row)
            With Tc_Row
                .BeginEdit()
                .Item("Bc_Name") = DBNull.Value
                .Item("Bq_Name") = DBNull.Value
                .EndEdit()
            End With
        End If
    End Sub

    Private Sub C1TrueDBGrid2_DoubleClick(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1TrueDBGrid2.DoubleClick
        If C1TrueDBGrid1.RowCount = 0 Then Exit Sub
        If C1TrueDBGrid2.Col.ToString = 4 Then
            If RadioButton2.Checked = True Then
                MsgBox(C1TrueDBGrid1.Columns("Ry_Name").Value & "已经存在床位，请先退床在进行分配！", MsgBoxStyle.Information, "提示：")
                Exit Sub
            End If

            If MsgBox("是否确认给患者【" & C1TrueDBGrid1.Columns("Ry_Name").Value & "】分配" & C1TrueDBGrid2.Columns("Bq_Name").Value & C1TrueDBGrid2.Columns("Bc_Name").Value & "?", MsgBoxStyle.OkCancel + MsgBoxStyle.DefaultButton1, "提示：") = MsgBoxResult.Cancel Then Exit Sub

            HisVar.HisVar.Sqldal.ExecuteSql("Update Bl Set Bc_Code='" & C1TrueDBGrid2.Columns("Bc_Code").Value & "' where Bl_Code='" & C1TrueDBGrid1.Columns("Bl_Code").Value & "'")
            Dim Tc_Row As DataRow = My_Cm.List(C1TrueDBGrid1.Row).Row
            Dim row As DataRow = Cw_Cm.List(C1TrueDBGrid2.Row).Row

            Tc_Row("Bc_Code")=row("Bc_Code")
            Tc_Row("Bc_Jc")=row("Bc_Jc")
            Tc_Row("Bq_Name")=row("Bq_Name")
            Tc_Row("Bc_Name")=row("Bc_Name")
            Tc_Row("Bc_Memo")=row("Bc_Memo")
            Tc_Row("Bq_Code")=row("Bq_Code")

            C1TrueDBGrid2.Delete()
        End If
    End Sub

    Private Sub C1TextBox1_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1TextBox1.TextChanged
        If C1Combo2.Text = "所有科室" Then
            If RadioButton1.Checked = True Then
                My_View.RowFilter = "(Ry_Jc like '%" & C1TextBox1.Text & "%' OR Ry_Name like '%" & C1TextBox1.Text & "%') and Isnull(Bc_Name,'')=''"
            Else
                My_View.RowFilter = "(Ry_Jc like '%" & C1TextBox1.Text & "%' OR Ry_Name like '%" & C1TextBox1.Text & "%') and Isnull(Bc_Name,'')<>'' "
            End If

        Else
            If RadioButton1.Checked = True Then
                My_View.RowFilter = "(Ry_Jc like '%" & C1TextBox1.Text & "%' OR Ry_Name like '%" & C1TextBox1.Text & "%') and Isnull(Bc_Name,'')='' and Ks_Name='" & C1Combo2.Text & "'"
            Else
                My_View.RowFilter = "(Ry_Jc like '%" & C1TextBox1.Text & "%' OR Ry_Name like '%" & C1TextBox1.Text & "%') and Isnull(Bc_Name,'')<>'' and Ks_Name='" & C1Combo2.Text & "'"
            End If

        End If
    End Sub

    Private Sub C1Combo1_RowChange(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1Combo1.RowChange
        If Load_Ok = False Then Exit Sub
        If C1Combo1.Text = "所有病区" Then
            Cw_View.RowFilter = ""
        Else
            Cw_View.RowFilter = "Bq_Name='" & C1Combo1.Text & "'"
        End If
    End Sub

    Private Sub C1Combo2_RowChange(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1Combo2.RowChange
        If Load_Ok = False Then Exit Sub
        If C1Combo2.Text = "所有科室" Then
            If RadioButton1.Checked = True Then
                My_View.RowFilter = "(Ry_Jc like '%" & C1TextBox1.Text & "%' OR Ry_Name like '%" & C1TextBox1.Text & "%') and Isnull(Bc_Name,'')=''"
            Else
                My_View.RowFilter = "(Ry_Jc like '%" & C1TextBox1.Text & "%' OR Ry_Name like '%" & C1TextBox1.Text & "%') and Isnull(Bc_Name,'')<>'' "
            End If

        Else
            If RadioButton1.Checked = True Then
                My_View.RowFilter = "(Ry_Jc like '%" & C1TextBox1.Text & "%' OR Ry_Name like '%" & C1TextBox1.Text & "%') and Isnull(Bc_Name,'')='' and Ks_Name='" & C1Combo2.Text & "'"
            Else
                My_View.RowFilter = "(Ry_Jc like '%" & C1TextBox1.Text & "%' OR Ry_Name like '%" & C1TextBox1.Text & "%') and Isnull(Bc_Name,'')<>'' and Ks_Name='" & C1Combo2.Text & "'"
            End If

        End If
    End Sub

    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click
        If HisPara.PublicConfig.ZyCfKsXz = "是" Then
            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "SELECT Bl_Code,Ry_YlCode,BxLb_Name,Ry_Name,Ry_Jc,Ry_Sex,Ry_Sfzh,Ry_Csdate,Ry_Address,Ry_RyDate,Ry_BlCode,Ry_Tele,Jb_Name,Ys_Name,Ks_Name,Bq_Name,Bc_Name FROM Zd_BxLb,Zd_YyYs,Zd_Yyks,Bl left join V_YyBc on  Bl.Bc_Code=V_YyBc.Bc_Code Where Bl.Ys_code=Zd_YyYs.Ys_code and Bl.Ks_Code=Zd_YyKs.Ks_Code  and Zd_BxLB.BxLb_Code=BL.BxLb_Code And Ry_CyDate is null and Bl.Ks_Code like '%" & HisVar.HisVar.XmKs & "%' ", "配床信息", True)
        Else
            HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "SELECT Bl_Code,Ry_YlCode,BxLb_Name,Ry_Name,Ry_Jc,Ry_Sex,Ry_Sfzh,Ry_Csdate,Ry_Address,Ry_RyDate,Ry_BlCode,Ry_Tele,Jb_Name,Ys_Name,Ks_Name,Bq_Name,Bc_Name FROM Zd_BxLb,Zd_YyYs,Zd_Yyks,Bl left join V_YyBc on  Bl.Bc_Code=V_YyBc.Bc_Code Where Bl.Ys_code=Zd_YyYs.Ys_code and Bl.Ks_Code=Zd_YyKs.Ks_Code  and Zd_BxLB.BxLb_Code=BL.BxLb_Code And Ry_CyDate is null ", "配床信息", True)
        End If

        If RadioButton1.Checked = True Then
            My_View.RowFilter = "(Ry_Jc like '%" & C1TextBox1.Text & "%' OR Ry_Name like '%" & C1TextBox1.Text & "%') and Isnull(Bc_Name,'')=''"
        Else
            My_View.RowFilter = "(Ry_Jc like '%" & C1TextBox1.Text & "%' OR Ry_Name like '%" & C1TextBox1.Text & "%') and Isnull(Bc_Name,'')<>''"
        End If

    End Sub
End Class