﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class Marterials_Check
    Inherits HisControl.BaseForm

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(Marterials_Check))
        Me.C1Holder1 = New C1.Win.C1Command.C1CommandHolder()
        Me.Control1 = New C1.Win.C1Command.C1CommandControl()
        Me.Panel2 = New System.Windows.Forms.Panel()
        Me.Comm_Close = New CustomControl.MyButton()
        Me.Comm_Dc = New CustomControl.MyButton()
        Me.Comm_Dr = New CustomControl.MyButton()
        Me.Comm_Search = New CustomControl.MyButton()
        Me.Comm_Print = New CustomControl.MyButton()
        Me.Comm_Complete = New CustomControl.MyButton()
        Me.Comm_New = New CustomControl.MyButton()
        Me.Comm_Save = New CustomControl.MyButton()
        Me.Comm_DelAll = New CustomControl.MyButton()
        Me.Comm_Del = New CustomControl.MyButton()
        Me.ImageList1 = New System.Windows.Forms.ImageList(Me.components)
        Me.T_Line2 = New System.Windows.Forms.Label()
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel()
        Me.Label6 = New System.Windows.Forms.Label()
        Me.WareHouse_DtCom = New CustomControl.MyDtComobo()
        Me.Form_Date = New CustomControl.MyDateEdit()
        Me.Code_Text = New CustomControl.MyTextBox()
        Me.Memo_Text = New CustomControl.MyTextBox()
        Me.Jsr_Text = New CustomControl.MyTextBox()
        Me.TodayTimesLabel1 = New System.Windows.Forms.Label()
        Me.TodayTotalMoneyLabel1 = New System.Windows.Forms.Label()
        Me.JsrTimesLabel2 = New System.Windows.Forms.Label()
        Me.JsrTotalMoneyLabel3 = New System.Windows.Forms.Label()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.ToolTip1 = New System.Windows.Forms.ToolTip(Me.components)
        Me.MyGrid1 = New CustomControl.MyGrid()
        Me.OpenFileDialog1 = New System.Windows.Forms.OpenFileDialog()
        Me.SaveFileDialog1 = New System.Windows.Forms.SaveFileDialog()
        CType(Me.C1Holder1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Panel2.SuspendLayout()
        Me.TableLayoutPanel1.SuspendLayout()
        Me.SuspendLayout()
        '
        'C1Holder1
        '
        Me.C1Holder1.Commands.Add(Me.Control1)
        Me.C1Holder1.Owner = Me
        Me.C1Holder1.VisualStyle = C1.Win.C1Command.VisualStyle.Office2010Blue
        '
        'Control1
        '
        Me.Control1.HotFrame = C1.Win.C1Command.HotFrameEnum.None
        Me.Control1.Name = "Control1"
        Me.Control1.ShortcutText = ""
        Me.Control1.ShowShortcut = False
        Me.Control1.ShowTextAsToolTip = False
        '
        'Panel2
        '
        Me.Panel2.Controls.Add(Me.Comm_Close)
        Me.Panel2.Controls.Add(Me.Comm_Dc)
        Me.Panel2.Controls.Add(Me.Comm_Dr)
        Me.Panel2.Controls.Add(Me.Comm_Search)
        Me.Panel2.Controls.Add(Me.Comm_Print)
        Me.Panel2.Controls.Add(Me.Comm_Complete)
        Me.Panel2.Controls.Add(Me.Comm_New)
        Me.Panel2.Controls.Add(Me.Comm_Save)
        Me.Panel2.Controls.Add(Me.Comm_DelAll)
        Me.Panel2.Controls.Add(Me.Comm_Del)
        Me.Panel2.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.Panel2.Location = New System.Drawing.Point(0, 485)
        Me.Panel2.Name = "Panel2"
        Me.Panel2.Size = New System.Drawing.Size(993, 35)
        Me.Panel2.TabIndex = 2
        '
        'Comm_Close
        '
        Me.Comm_Close.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.Comm_Close.DialogResult = System.Windows.Forms.DialogResult.None
        Me.Comm_Close.Font = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Comm_Close.Location = New System.Drawing.Point(877, 2)
        Me.Comm_Close.Name = "Comm_Close"
        Me.Comm_Close.Size = New System.Drawing.Size(75, 30)
        Me.Comm_Close.TabIndex = 30
        Me.Comm_Close.Tag = "退出"
        Me.Comm_Close.Text = "退出F9"
        '
        'Comm_Dc
        '
        Me.Comm_Dc.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.Comm_Dc.DialogResult = System.Windows.Forms.DialogResult.None
        Me.Comm_Dc.Font = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Comm_Dc.Location = New System.Drawing.Point(784, 2)
        Me.Comm_Dc.Name = "Comm_Dc"
        Me.Comm_Dc.Size = New System.Drawing.Size(75, 30)
        Me.Comm_Dc.TabIndex = 30
        Me.Comm_Dc.Tag = "导出"
        Me.Comm_Dc.Text = "导出F8"
        '
        'Comm_Dr
        '
        Me.Comm_Dr.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.Comm_Dr.DialogResult = System.Windows.Forms.DialogResult.None
        Me.Comm_Dr.Font = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Comm_Dr.Location = New System.Drawing.Point(691, 2)
        Me.Comm_Dr.Name = "Comm_Dr"
        Me.Comm_Dr.Size = New System.Drawing.Size(75, 30)
        Me.Comm_Dr.TabIndex = 30
        Me.Comm_Dr.Tag = "导入"
        Me.Comm_Dr.Text = "导入F7"
        '
        'Comm_Search
        '
        Me.Comm_Search.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.Comm_Search.DialogResult = System.Windows.Forms.DialogResult.None
        Me.Comm_Search.Font = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Comm_Search.Location = New System.Drawing.Point(598, 2)
        Me.Comm_Search.Name = "Comm_Search"
        Me.Comm_Search.Size = New System.Drawing.Size(75, 30)
        Me.Comm_Search.TabIndex = 30
        Me.Comm_Search.Tag = "速查"
        Me.Comm_Search.Text = "速查F6"
        '
        'Comm_Print
        '
        Me.Comm_Print.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.Comm_Print.DialogResult = System.Windows.Forms.DialogResult.None
        Me.Comm_Print.Font = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Comm_Print.Location = New System.Drawing.Point(505, 2)
        Me.Comm_Print.Name = "Comm_Print"
        Me.Comm_Print.Size = New System.Drawing.Size(75, 30)
        Me.Comm_Print.TabIndex = 30
        Me.Comm_Print.Tag = "打印"
        Me.Comm_Print.Text = "打印F5"
        '
        'Comm_Complete
        '
        Me.Comm_Complete.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.Comm_Complete.DialogResult = System.Windows.Forms.DialogResult.None
        Me.Comm_Complete.Font = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Comm_Complete.Location = New System.Drawing.Point(412, 2)
        Me.Comm_Complete.Name = "Comm_Complete"
        Me.Comm_Complete.Size = New System.Drawing.Size(75, 30)
        Me.Comm_Complete.TabIndex = 30
        Me.Comm_Complete.Tag = "完成"
        Me.Comm_Complete.Text = "完成F4"
        '
        'Comm_New
        '
        Me.Comm_New.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.Comm_New.DialogResult = System.Windows.Forms.DialogResult.None
        Me.Comm_New.Font = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Comm_New.Location = New System.Drawing.Point(319, 2)
        Me.Comm_New.Name = "Comm_New"
        Me.Comm_New.Size = New System.Drawing.Size(75, 30)
        Me.Comm_New.TabIndex = 30
        Me.Comm_New.Tag = "新单"
        Me.Comm_New.Text = "新单F2"
        '
        'Comm_Save
        '
        Me.Comm_Save.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.Comm_Save.DialogResult = System.Windows.Forms.DialogResult.None
        Me.Comm_Save.Font = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Comm_Save.Location = New System.Drawing.Point(226, 2)
        Me.Comm_Save.Name = "Comm_Save"
        Me.Comm_Save.Size = New System.Drawing.Size(75, 30)
        Me.Comm_Save.TabIndex = 0
        Me.Comm_Save.Tag = "保存"
        Me.Comm_Save.Text = "保存F3"
        '
        'Comm_DelAll
        '
        Me.Comm_DelAll.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.Comm_DelAll.DialogResult = System.Windows.Forms.DialogResult.None
        Me.Comm_DelAll.Font = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Comm_DelAll.Location = New System.Drawing.Point(133, 2)
        Me.Comm_DelAll.Name = "Comm_DelAll"
        Me.Comm_DelAll.Size = New System.Drawing.Size(75, 30)
        Me.Comm_DelAll.TabIndex = 30
        Me.Comm_DelAll.Tag = "删除单"
        Me.Comm_DelAll.Text = "删除单"
        '
        'Comm_Del
        '
        Me.Comm_Del.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.Comm_Del.DialogResult = System.Windows.Forms.DialogResult.None
        Me.Comm_Del.Font = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Comm_Del.Location = New System.Drawing.Point(40, 2)
        Me.Comm_Del.Name = "Comm_Del"
        Me.Comm_Del.Size = New System.Drawing.Size(75, 30)
        Me.Comm_Del.TabIndex = 30
        Me.Comm_Del.Tag = "删除行"
        Me.Comm_Del.Text = "删除行"
        '
        'ImageList1
        '
        Me.ImageList1.ImageStream = CType(resources.GetObject("ImageList1.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList1.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList1.Images.SetKeyName(0, "导出.png")
        Me.ImageList1.Images.SetKeyName(1, "导入.png")
        '
        'T_Line2
        '
        Me.T_Line2.BackColor = System.Drawing.SystemColors.Control
        Me.T_Line2.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.T_Line2.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.T_Line2.Location = New System.Drawing.Point(0, 483)
        Me.T_Line2.Name = "T_Line2"
        Me.T_Line2.Size = New System.Drawing.Size(993, 2)
        Me.T_Line2.TabIndex = 2
        Me.T_Line2.Text = "Label2"
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.ColumnCount = 9
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 60.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 160.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 60.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 160.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 60.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 160.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 60.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 160.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 113.0!))
        Me.TableLayoutPanel1.Controls.Add(Me.Label6, 0, 4)
        Me.TableLayoutPanel1.Controls.Add(Me.WareHouse_DtCom, 0, 3)
        Me.TableLayoutPanel1.Controls.Add(Me.Form_Date, 2, 3)
        Me.TableLayoutPanel1.Controls.Add(Me.Code_Text, 0, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.Memo_Text, 0, 5)
        Me.TableLayoutPanel1.Controls.Add(Me.Jsr_Text, 2, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.TodayTimesLabel1, 0, 7)
        Me.TableLayoutPanel1.Controls.Add(Me.TodayTotalMoneyLabel1, 2, 7)
        Me.TableLayoutPanel1.Controls.Add(Me.JsrTimesLabel2, 4, 7)
        Me.TableLayoutPanel1.Controls.Add(Me.JsrTotalMoneyLabel3, 6, 7)
        Me.TableLayoutPanel1.Controls.Add(Me.Label1, 0, 6)
        Me.TableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Top
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(0, 0)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.RowCount = 8
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 3.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 26.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 8.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 26.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 8.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 26.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 2.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 26.0!))
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(993, 99)
        Me.TableLayoutPanel1.TabIndex = 0
        '
        'Label6
        '
        Me.Label6.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label6.BackColor = System.Drawing.SystemColors.Control
        Me.Label6.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.TableLayoutPanel1.SetColumnSpan(Me.Label6, 9)
        Me.Label6.Location = New System.Drawing.Point(3, 66)
        Me.Label6.Name = "Label6"
        Me.Label6.Size = New System.Drawing.Size(987, 2)
        Me.Label6.TabIndex = 3
        Me.Label6.Text = "Label1"
        '
        'WareHouse_DtCom
        '
        Me.WareHouse_DtCom.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.WareHouse_DtCom.Captain = "物资仓库"
        Me.WareHouse_DtCom.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.WareHouse_DtCom.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.WareHouse_DtCom.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.WareHouse_DtCom, 2)
        Me.WareHouse_DtCom.DataSource = Nothing
        Me.WareHouse_DtCom.ItemHeight = 18
        Me.WareHouse_DtCom.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.WareHouse_DtCom.Location = New System.Drawing.Point(3, 40)
        Me.WareHouse_DtCom.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.WareHouse_DtCom.MinimumSize = New System.Drawing.Size(0, 20)
        Me.WareHouse_DtCom.Name = "WareHouse_DtCom"
        Me.WareHouse_DtCom.ReadOnly = False
        Me.WareHouse_DtCom.Size = New System.Drawing.Size(214, 20)
        Me.WareHouse_DtCom.TabIndex = 2
        Me.WareHouse_DtCom.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'Form_Date
        '
        Me.Form_Date.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Form_Date.Captain = "盘点时间"
        Me.Form_Date.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Form_Date.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.Form_Date, 2)
        Me.Form_Date.InitialSelection = C1.Win.C1Input.InitialSelectionEnum.CaretAtEnd
        Me.Form_Date.Location = New System.Drawing.Point(223, 40)
        Me.Form_Date.MaximumSize = New System.Drawing.Size(100000000, 20)
        Me.Form_Date.MinimumSize = New System.Drawing.Size(0, 20)
        Me.Form_Date.Name = "Form_Date"
        Me.Form_Date.Size = New System.Drawing.Size(214, 20)
        Me.Form_Date.TabIndex = 3
        Me.Form_Date.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Form_Date.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.Form_Date.VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.DropDown
        '
        'Code_Text
        '
        Me.Code_Text.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Code_Text.Captain = "盘点编码"
        Me.Code_Text.CaptainBackColor = System.Drawing.Color.Transparent
        Me.Code_Text.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Code_Text.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.Code_Text.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.Code_Text, 2)
        Me.Code_Text.ContentForeColor = System.Drawing.Color.Black
        Me.Code_Text.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.Code_Text.EditMask = Nothing
        Me.Code_Text.Location = New System.Drawing.Point(3, 6)
        Me.Code_Text.Multiline = False
        Me.Code_Text.Name = "Code_Text"
        Me.Code_Text.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.Code_Text.ReadOnly = False
        Me.Code_Text.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.Code_Text.SelectionStart = 0
        Me.Code_Text.SelectStart = 0
        Me.Code_Text.Size = New System.Drawing.Size(214, 20)
        Me.Code_Text.TabIndex = 0
        Me.Code_Text.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Code_Text.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.Code_Text.Watermark = Nothing
        '
        'Memo_Text
        '
        Me.Memo_Text.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Memo_Text.Captain = "备    注"
        Me.Memo_Text.CaptainBackColor = System.Drawing.Color.Transparent
        Me.Memo_Text.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Memo_Text.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.Memo_Text.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.Memo_Text, 6)
        Me.Memo_Text.ContentForeColor = System.Drawing.Color.Black
        Me.Memo_Text.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.Memo_Text.EditMask = Nothing
        Me.Memo_Text.Location = New System.Drawing.Point(3, 74)
        Me.Memo_Text.Multiline = False
        Me.Memo_Text.Name = "Memo_Text"
        Me.Memo_Text.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.Memo_Text.ReadOnly = False
        Me.Memo_Text.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.Memo_Text.SelectionStart = 0
        Me.Memo_Text.SelectStart = 0
        Me.Memo_Text.Size = New System.Drawing.Size(654, 20)
        Me.Memo_Text.TabIndex = 4
        Me.Memo_Text.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Memo_Text.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.Memo_Text.Watermark = Nothing
        '
        'Jsr_Text
        '
        Me.Jsr_Text.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Jsr_Text.Captain = "操作员"
        Me.Jsr_Text.CaptainBackColor = System.Drawing.Color.Transparent
        Me.Jsr_Text.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Jsr_Text.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.Jsr_Text.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.Jsr_Text, 2)
        Me.Jsr_Text.ContentForeColor = System.Drawing.Color.Black
        Me.Jsr_Text.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.Jsr_Text.EditMask = Nothing
        Me.Jsr_Text.Location = New System.Drawing.Point(223, 6)
        Me.Jsr_Text.Multiline = False
        Me.Jsr_Text.Name = "Jsr_Text"
        Me.Jsr_Text.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.Jsr_Text.ReadOnly = False
        Me.Jsr_Text.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.Jsr_Text.SelectionStart = 0
        Me.Jsr_Text.SelectStart = 0
        Me.Jsr_Text.Size = New System.Drawing.Size(214, 20)
        Me.Jsr_Text.TabIndex = 1
        Me.Jsr_Text.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Jsr_Text.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.Jsr_Text.Watermark = Nothing
        '
        'TodayTimesLabel1
        '
        Me.TodayTimesLabel1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TodayTimesLabel1.AutoSize = True
        Me.TableLayoutPanel1.SetColumnSpan(Me.TodayTimesLabel1, 2)
        Me.TodayTimesLabel1.Font = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.TodayTimesLabel1.ForeColor = System.Drawing.Color.Maroon
        Me.TodayTimesLabel1.Location = New System.Drawing.Point(3, 105)
        Me.TodayTimesLabel1.Name = "TodayTimesLabel1"
        Me.TodayTimesLabel1.Size = New System.Drawing.Size(214, 14)
        Me.TodayTimesLabel1.TabIndex = 5
        Me.TodayTimesLabel1.Text = "今日盘点数量："
        '
        'TodayTotalMoneyLabel1
        '
        Me.TodayTotalMoneyLabel1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TodayTotalMoneyLabel1.AutoSize = True
        Me.TableLayoutPanel1.SetColumnSpan(Me.TodayTotalMoneyLabel1, 2)
        Me.TodayTotalMoneyLabel1.Font = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.TodayTotalMoneyLabel1.ForeColor = System.Drawing.Color.Maroon
        Me.TodayTotalMoneyLabel1.Location = New System.Drawing.Point(223, 105)
        Me.TodayTotalMoneyLabel1.Name = "TodayTotalMoneyLabel1"
        Me.TodayTotalMoneyLabel1.Size = New System.Drawing.Size(214, 14)
        Me.TodayTotalMoneyLabel1.TabIndex = 5
        Me.TodayTotalMoneyLabel1.Text = "今日盘点总金额："
        '
        'JsrTimesLabel2
        '
        Me.JsrTimesLabel2.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.JsrTimesLabel2.AutoSize = True
        Me.TableLayoutPanel1.SetColumnSpan(Me.JsrTimesLabel2, 2)
        Me.JsrTimesLabel2.Font = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.JsrTimesLabel2.ForeColor = System.Drawing.Color.Maroon
        Me.JsrTimesLabel2.Location = New System.Drawing.Point(443, 105)
        Me.JsrTimesLabel2.Name = "JsrTimesLabel2"
        Me.JsrTimesLabel2.Size = New System.Drawing.Size(214, 14)
        Me.JsrTimesLabel2.TabIndex = 5
        Me.JsrTimesLabel2.Text = "本人盘点数量："
        '
        'JsrTotalMoneyLabel3
        '
        Me.JsrTotalMoneyLabel3.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.JsrTotalMoneyLabel3.AutoSize = True
        Me.TableLayoutPanel1.SetColumnSpan(Me.JsrTotalMoneyLabel3, 2)
        Me.JsrTotalMoneyLabel3.Font = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.JsrTotalMoneyLabel3.ForeColor = System.Drawing.Color.Maroon
        Me.JsrTotalMoneyLabel3.Location = New System.Drawing.Point(663, 105)
        Me.JsrTotalMoneyLabel3.Name = "JsrTotalMoneyLabel3"
        Me.JsrTotalMoneyLabel3.Size = New System.Drawing.Size(214, 14)
        Me.JsrTotalMoneyLabel3.TabIndex = 5
        Me.JsrTotalMoneyLabel3.Text = "本人盘点总金额："
        '
        'Label1
        '
        Me.Label1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label1.BackColor = System.Drawing.SystemColors.Control
        Me.Label1.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.TableLayoutPanel1.SetColumnSpan(Me.Label1, 9)
        Me.Label1.Location = New System.Drawing.Point(3, 97)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(987, 2)
        Me.Label1.TabIndex = 3
        Me.Label1.Text = "Label1"
        '
        'MyGrid1
        '
        Me.MyGrid1.CanCustomCol = False
        Me.MyGrid1.Col = 0
        Me.MyGrid1.ColumnFooters = False
        Me.MyGrid1.DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveRight
        Me.MyGrid1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.MyGrid1.FetchRowStyles = False
        Me.MyGrid1.GroupByAreaVisible = False
        Me.MyGrid1.Location = New System.Drawing.Point(0, 99)
        Me.MyGrid1.Margin = New System.Windows.Forms.Padding(0)
        Me.MyGrid1.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.MyGrid1.Name = "MyGrid1"
        Me.MyGrid1.Size = New System.Drawing.Size(993, 384)
        Me.MyGrid1.TabAction = C1.Win.C1TrueDBGrid.TabActionEnum.ControlNavigation
        Me.MyGrid1.TabIndex = 1
        Me.MyGrid1.Xmlpath = Nothing
        '
        'OpenFileDialog1
        '
        Me.OpenFileDialog1.FileName = "OpenFileDialog1"
        '
        'Marterials_Check
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(993, 520)
        Me.Controls.Add(Me.MyGrid1)
        Me.Controls.Add(Me.TableLayoutPanel1)
        Me.Controls.Add(Me.T_Line2)
        Me.Controls.Add(Me.Panel2)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle
        Me.Name = "Marterials_Check"
        Me.ShowInTaskbar = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "物资盘点"
        CType(Me.C1Holder1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Panel2.ResumeLayout(False)
        Me.TableLayoutPanel1.ResumeLayout(False)
        Me.TableLayoutPanel1.PerformLayout()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents C1Holder1 As C1.Win.C1Command.C1CommandHolder
    Friend WithEvents Control1 As C1.Win.C1Command.C1CommandControl
    Friend WithEvents Panel2 As System.Windows.Forms.Panel
    Friend WithEvents T_Line2 As System.Windows.Forms.Label
    Friend WithEvents TableLayoutPanel1 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents Label6 As System.Windows.Forms.Label
    Friend WithEvents ToolTip1 As System.Windows.Forms.ToolTip
    Friend WithEvents WareHouse_DtCom As CustomControl.MyDtComobo
    Friend WithEvents MyGrid1 As CustomControl.MyGrid
    Friend WithEvents Form_Date As CustomControl.MyDateEdit
    Friend WithEvents Code_Text As CustomControl.MyTextBox
    Friend WithEvents Memo_Text As CustomControl.MyTextBox
    Friend WithEvents Jsr_Text As CustomControl.MyTextBox
    Friend WithEvents ImageList1 As System.Windows.Forms.ImageList
    Friend WithEvents OpenFileDialog1 As System.Windows.Forms.OpenFileDialog
    Friend WithEvents SaveFileDialog1 As System.Windows.Forms.SaveFileDialog
    Friend WithEvents TodayTimesLabel1 As System.Windows.Forms.Label
    Friend WithEvents TodayTotalMoneyLabel1 As System.Windows.Forms.Label
    Friend WithEvents JsrTimesLabel2 As System.Windows.Forms.Label
    Friend WithEvents JsrTotalMoneyLabel3 As System.Windows.Forms.Label
    Friend WithEvents Comm_Save As CustomControl.MyButton
    Friend WithEvents Comm_DelAll As CustomControl.MyButton
    Friend WithEvents Comm_Del As CustomControl.MyButton
    Friend WithEvents Comm_Close As CustomControl.MyButton
    Friend WithEvents Comm_Dc As CustomControl.MyButton
    Friend WithEvents Comm_Dr As CustomControl.MyButton
    Friend WithEvents Comm_Search As CustomControl.MyButton
    Friend WithEvents Comm_Print As CustomControl.MyButton
    Friend WithEvents Comm_Complete As CustomControl.MyButton
    Friend WithEvents Comm_New As CustomControl.MyButton
    Friend WithEvents Label1 As System.Windows.Forms.Label
End Class
