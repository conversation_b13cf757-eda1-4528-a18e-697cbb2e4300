﻿/**  版本信息模板在安装目录下，可自行修改。
* D_Emr_ZkDj.cs
*
* 功 能： N/A
* 类 名： D_Emr_ZkDj
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/8/11 星期四 上午 10:29:58   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
//
namespace DAL
{
	/// <summary>
	/// 数据访问类:D_Emr_ZkDj
	/// </summary>
	public partial class D_Emr_ZkDj
	{
		public D_Emr_ZkDj()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string ZkDj_Code)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from Emr_ZkDj");
			strSql.Append(" where ZkDj_Code=@ZkDj_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@ZkDj_Code", SqlDbType.Char,2)			};
			parameters[0].Value = ZkDj_Code;

			return HisVar.HisVar.Sqldal.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ModelOld.M_Emr_ZkDj model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into Emr_ZkDj(");
			strSql.Append("ZkDj_Code,ZkDj_Name,ZkDj_Jc,ZkDj_MaxValue,ZkDj_MinValue,ZkDj_Kf,ZkDj_Memo)");
			strSql.Append(" values (");
			strSql.Append("@ZkDj_Code,@ZkDj_Name,@ZkDj_Jc,@ZkDj_MaxValue,@ZkDj_MinValue,@ZkDj_Kf,@ZkDj_Memo)");
			SqlParameter[] parameters = {
					new SqlParameter("@ZkDj_Code", SqlDbType.Char,2),
					new SqlParameter("@ZkDj_Name", SqlDbType.VarChar,50),
					new SqlParameter("@ZkDj_Jc", SqlDbType.VarChar,50),
					new SqlParameter("@ZkDj_MaxValue", SqlDbType.Decimal,9),
					new SqlParameter("@ZkDj_MinValue", SqlDbType.Decimal,9),
					new SqlParameter("@ZkDj_Kf", SqlDbType.Decimal,9),
					new SqlParameter("@ZkDj_Memo", SqlDbType.VarChar,50)};
			parameters[0].Value = model.ZkDj_Code;
			parameters[1].Value = model.ZkDj_Name;
			parameters[2].Value = model.ZkDj_Jc;
			parameters[3].Value = model.ZkDj_MaxValue;
			parameters[4].Value = model.ZkDj_MinValue;
			parameters[5].Value = model.ZkDj_Kf;
			parameters[6].Value = model.ZkDj_Memo;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ModelOld.M_Emr_ZkDj model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update Emr_ZkDj set ");
			strSql.Append("ZkDj_Name=@ZkDj_Name,");
			strSql.Append("ZkDj_Jc=@ZkDj_Jc,");
			strSql.Append("ZkDj_MaxValue=@ZkDj_MaxValue,");
			strSql.Append("ZkDj_MinValue=@ZkDj_MinValue,");
			strSql.Append("ZkDj_Kf=@ZkDj_Kf,");
			strSql.Append("ZkDj_Memo=@ZkDj_Memo");
			strSql.Append(" where ZkDj_Code=@ZkDj_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@ZkDj_Name", SqlDbType.VarChar,50),
					new SqlParameter("@ZkDj_Jc", SqlDbType.VarChar,50),
					new SqlParameter("@ZkDj_MaxValue", SqlDbType.Decimal,9),
					new SqlParameter("@ZkDj_MinValue", SqlDbType.Decimal,9),
					new SqlParameter("@ZkDj_Kf", SqlDbType.Decimal,9),
					new SqlParameter("@ZkDj_Memo", SqlDbType.VarChar,50),
					new SqlParameter("@ZkDj_Code", SqlDbType.Char,2)};
			parameters[0].Value = model.ZkDj_Name;
			parameters[1].Value = model.ZkDj_Jc;
			parameters[2].Value = model.ZkDj_MaxValue;
			parameters[3].Value = model.ZkDj_MinValue;
			parameters[4].Value = model.ZkDj_Kf;
			parameters[5].Value = model.ZkDj_Memo;
			parameters[6].Value = model.ZkDj_Code;

            string Code = parameters[6].Value.ToString();

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

        public bool Update1(ModelOld.M_Emr_ZkDj model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update Emr_ZkDj set ");
            strSql.Append("ZkDj_Name=@ZkDj_Name,");
            strSql.Append("ZkDj_Jc=@ZkDj_Jc,");
            strSql.Append("ZkDj_MaxValue=@ZkDj_MaxValue,");
            strSql.Append("ZkDj_MinValue=@ZkDj_MinValue,");
            strSql.Append("ZkDj_Kf=@ZkDj_Kf,");
            strSql.Append("ZkDj_Memo=@ZkDj_Memo");
            strSql.Append(" where ZkDj_Code=@ZkDj_Code ");
            SqlParameter[] parameters = {
					new SqlParameter("@ZkDj_Name", SqlDbType.VarChar,50),
					new SqlParameter("@ZkDj_Jc", SqlDbType.VarChar,50),
					new SqlParameter("@ZkDj_MaxValue", SqlDbType.Decimal,9),
					new SqlParameter("@ZkDj_MinValue", SqlDbType.Decimal,9),
					new SqlParameter("@ZkDj_Kf", SqlDbType.Decimal,9),
					new SqlParameter("@ZkDj_Memo", SqlDbType.VarChar,50),
					new SqlParameter("@ZkDj_Code", SqlDbType.Char,2)};
            parameters[0].Value = model.ZkDj_Name;
            parameters[1].Value = model.ZkDj_Jc;
            parameters[2].Value = model.ZkDj_MaxValue;
            parameters[3].Value = model.ZkDj_MinValue;
            parameters[4].Value = model.ZkDj_Kf;
            parameters[5].Value = model.ZkDj_Memo;
            parameters[6].Value = model.ZkDj_Code;

            string rowCode = HisVar.HisVar.Sqldal.F_MaxCode("SELECT MAX(ZkDj_Code) FROM dbo.Emr_ZkDj", 2);

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        public string rowCode()
        {
            string rowCode = HisVar.HisVar.Sqldal.F_MaxCode("SELECT MAX(ZkDj_Code) FROM dbo.Emr_ZkDj", 2);
            return rowCode;
        }
        public bool nextCode(string ZkDj_Code)           
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT ZkDj_Code FROM dbo.Emr_ZkDj ");
            strSql.Append(" where ZkDj_Code='" + ZkDj_Code + "' ");            
            string Max = HisVar.HisVar.Sqldal.F_MaxCode(strSql.ToString(), 2);
            StringBuilder strSql1 = new StringBuilder();
            strSql1.Append("SELECT count(1) FROM dbo.Emr_ZkDj ");
            strSql1.Append(" where ZkDj_Code=@Max ");
            SqlParameter[] parameters1 = {
					    new SqlParameter("@Max", SqlDbType.Char,2)			};
            parameters1[0].Value = Max;
            return HisVar.HisVar.Sqldal.Exists(strSql1.ToString(), parameters1);
           
        }

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string ZkDj_Code)
		{
            bool b = nextCode(ZkDj_Code);

            if (nextCode(ZkDj_Code))
                return false;
            else
            {
                 StringBuilder strSql=new StringBuilder();
			    strSql.Append("delete from Emr_ZkDj ");
			    strSql.Append(" where ZkDj_Code=@ZkDj_Code ");
			    SqlParameter[] parameters = {
					    new SqlParameter("@ZkDj_Code", SqlDbType.Char,2)			};
			    parameters[0].Value = ZkDj_Code;

			    int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			    if (rows > 0)
			    {
				    return true;
			    }
			    else
			    {
				    return false;
			    }
            }
			
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string ZkDj_Codelist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Emr_ZkDj ");
			strSql.Append(" where ZkDj_Code in ("+ZkDj_Codelist + ")  ");
			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

        public string GetDeductedScore(string grade)
        {
            return HisVar.HisVar.Sqldal.GetSingle("SELECT ZkDj_Kf FROM dbo.Emr_ZkDj WHERE ZkDj_Name='"+grade+"'").ToString();
        }
		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Emr_ZkDj GetModel(string ZkDj_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 ZkDj_Code,ZkDj_Name,ZkDj_Jc,ZkDj_MaxValue,ZkDj_MinValue,ZkDj_Kf,ZkDj_Memo from Emr_ZkDj ");
			strSql.Append(" where ZkDj_Code=@ZkDj_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@ZkDj_Code", SqlDbType.Char,2)			};
			parameters[0].Value = ZkDj_Code;

			ModelOld.M_Emr_ZkDj model=new ModelOld.M_Emr_ZkDj();
			DataSet ds=HisVar.HisVar.Sqldal.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Emr_ZkDj DataRowToModel(DataRow row)
		{
			ModelOld.M_Emr_ZkDj model=new ModelOld.M_Emr_ZkDj();
			if (row != null)
			{
				if(row["ZkDj_Code"]!=null)
				{
					model.ZkDj_Code=row["ZkDj_Code"].ToString();
				}
				if(row["ZkDj_Name"]!=null)
				{
					model.ZkDj_Name=row["ZkDj_Name"].ToString();
				}
				if(row["ZkDj_Jc"]!=null)
				{
					model.ZkDj_Jc=row["ZkDj_Jc"].ToString();
				}
				if(row["ZkDj_MaxValue"]!=null && row["ZkDj_MaxValue"].ToString()!="")
				{
					model.ZkDj_MaxValue=decimal.Parse(row["ZkDj_MaxValue"].ToString());
				}
				if(row["ZkDj_MinValue"]!=null && row["ZkDj_MinValue"].ToString()!="")
				{
					model.ZkDj_MinValue=decimal.Parse(row["ZkDj_MinValue"].ToString());
				}
				if(row["ZkDj_Kf"]!=null && row["ZkDj_Kf"].ToString()!="")
				{
					model.ZkDj_Kf=decimal.Parse(row["ZkDj_Kf"].ToString());
				}
				if(row["ZkDj_Memo"]!=null)
				{
					model.ZkDj_Memo=row["ZkDj_Memo"].ToString();
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ZkDj_Code,ZkDj_Name,ZkDj_Jc,ZkDj_MaxValue,ZkDj_MinValue,ZkDj_Kf,ZkDj_Memo ");
            strSql.Append(" from Emr_ZkDj ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

       

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" ZkDj_Code,ZkDj_Name,ZkDj_Jc,ZkDj_MaxValue,ZkDj_MinValue,ZkDj_Kf,ZkDj_Memo ");
			strSql.Append(" FROM Emr_ZkDj ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM Emr_ZkDj ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.ZkDj_Code desc");
			}
			strSql.Append(")AS Row, T.*  from Emr_ZkDj T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Emr_ZkDj";
			parameters[1].Value = "ZkDj_Code";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return HisVar.HisVar.Sqldal.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

