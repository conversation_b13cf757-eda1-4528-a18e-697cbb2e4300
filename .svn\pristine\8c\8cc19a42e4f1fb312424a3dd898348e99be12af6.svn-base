﻿/**  版本信息模板在安装目录下，可自行修改。
* D_Emr_Mblb.cs
*
* 功 能： N/A
* 类 名： D_Emr_Mblb
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/8/10 11:31:34   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
namespace DAL
{
	/// <summary>
	/// 数据访问类:D_Emr_Mblb
	/// </summary>
	public partial class D_Emr_Mblb
	{
		public D_Emr_Mblb()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string Mblb_Code)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from Emr_Mblb");
			strSql.Append(" where Mblb_Code=@Mblb_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Mblb_Code", SqlDbType.Char,5)			};
			parameters[0].Value = Mblb_Code;

			return HisVar.HisVar.Sqldal.Exists(strSql.ToString(),parameters);
		}
        public bool Existsyz(string Mblb_Code)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from Emr_Mblb");
            strSql.Append(" where Mblb_Code=@Mblb_Code ");
            SqlParameter[] parameters = {
					new SqlParameter("@Mblb_Code", SqlDbType.Char,5)			};
            parameters[0].Value = Mblb_Code;

            return HisVar.HisVar.Sqldal.Exists(strSql.ToString(), parameters);
        }

        public string MaxCode()
        {
            string Max = HisVar.HisVar.Sqldal.F_MaxCode("SELECT MAX(Mblb_Code) FROM dbo.Emr_Mblb", 5);
            return Max;
        }

		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ModelOld.M_Emr_Mblb model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into Emr_Mblb(");
			strSql.Append("Mblb_Code,Mblb_Name,Mblb_Jc,Father_Code)");
			strSql.Append(" values (");
			strSql.Append("@Mblb_Code,@Mblb_Name,@Mblb_Jc,@Father_Code)");
			SqlParameter[] parameters = {
					new SqlParameter("@Mblb_Code", SqlDbType.Char,5),
					new SqlParameter("@Mblb_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Mblb_Jc", SqlDbType.VarChar,50),
					new SqlParameter("@Father_Code", SqlDbType.Char,5)};
			parameters[0].Value = model.Mblb_Code;
			parameters[1].Value = model.Mblb_Name;
			parameters[2].Value = model.Mblb_Jc;
			parameters[3].Value = model.Father_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ModelOld.M_Emr_Mblb model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update Emr_Mblb set ");
			strSql.Append("Mblb_Name=@Mblb_Name,");
			strSql.Append("Mblb_Jc=@Mblb_Jc,");
			strSql.Append("Father_Code=@Father_Code");
			strSql.Append(" where Mblb_Code=@Mblb_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Mblb_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Mblb_Jc", SqlDbType.VarChar,50),
					new SqlParameter("@Father_Code", SqlDbType.Char,5),
					new SqlParameter("@Mblb_Code", SqlDbType.Char,5)};
			parameters[0].Value = model.Mblb_Name;
			parameters[1].Value = model.Mblb_Jc;
			parameters[2].Value = model.Father_Code;
			parameters[3].Value = model.Mblb_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string Mblb_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Emr_Mblb ");
			strSql.Append(" where Mblb_Code=@Mblb_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Mblb_Code", SqlDbType.Char,5)			};
			parameters[0].Value = Mblb_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


        public bool DeleteAll()
        {

            StringBuilder strSql1 = new StringBuilder();
            strSql1.Append("delete from emr_mb ");

            StringBuilder strSql2 = new StringBuilder();
            strSql2.Append("delete from emr_mblb ");


            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql1.ToString());
            rows += HisVar.HisVar.Sqldal.ExecuteSql(strSql2.ToString());
            if (rows > 0)
            {
                return true; 
            }
            else
            {
                return false;
            }
        }
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string Mblb_Codelist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Emr_Mblb ");
			strSql.Append(" where Mblb_Code in ("+Mblb_Codelist + ")  ");
			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Emr_Mblb GetModel(string Mblb_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 Mblb_Code,Mblb_Name,Mblb_Jc,Father_Code from Emr_Mblb ");
			strSql.Append(" where Mblb_Code=@Mblb_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Mblb_Code", SqlDbType.Char,5)			};
			parameters[0].Value = Mblb_Code;

			ModelOld.M_Emr_Mblb model=new ModelOld.M_Emr_Mblb();
			DataSet ds=HisVar.HisVar.Sqldal.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Emr_Mblb DataRowToModel(DataRow row)
		{
			ModelOld.M_Emr_Mblb model=new ModelOld.M_Emr_Mblb();
			if (row != null)
			{
				if(row["Mblb_Code"]!=null)
				{
					model.Mblb_Code=row["Mblb_Code"].ToString();
				}
				if(row["Mblb_Name"]!=null)
				{
					model.Mblb_Name=row["Mblb_Name"].ToString();
				}
				if(row["Mblb_Jc"]!=null)
				{
					model.Mblb_Jc=row["Mblb_Jc"].ToString();
				}
				if(row["Father_Code"]!=null)
				{
					model.Father_Code=row["Father_Code"].ToString();
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select Mblb_Code,Mblb_Name,Mblb_Jc,Father_Code ");
			strSql.Append(" FROM Emr_Mblb ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}
        public DataSet GetListmbname(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select distinct Emr_Mblb.Mblb_Code,Mblb_Name,Mblb_Jc,Father_Code ");
            strSql.Append(" FROM dbo.Emr_Mblb ,dbo.Emr_Mb,dbo.Emr_Bl ");
            strSql.Append(" where Emr_Mb.Mblb_Code=Emr_Mblb.Mblb_Code AND Emr_Bl.Mb_Code=Emr_Mb.Mb_Code ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" and " + strWhere);
            }
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

        public DataSet GetAllMb(string Mblb_Code,string Yl_Event)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT Emr_Mblb.Mblb_Code, Mblb_Name, Mblb_Jc, Father_Code,Mb_Code, Mb_Name,");
            strSql.Append("Mb_Jc, Mb_Nr, Mb_Sex, isMust, isMulti, AgeLimit, isStandard, Ks_Code, Ys_Code, Mb_Memo");
            strSql.Append(" FROM dbo.Emr_Mb  JOIN dbo.Emr_Mblb ON Emr_Mblb.Mblb_Code = Emr_Mb.Mblb_Code");
            strSql.Append(" WHERE NOT EXISTS (SELECT 1 FROM (SELECT Emr_SiKong.Mb_Code,Mb_Name,Emr_Mb.Mblb_Code,Mblb_Name ");
            strSql.Append(" FROM dbo.Emr_SiKong LEFT JOIN dbo.Emr_Mb ON Emr_Mb.Mb_Code = Emr_SiKong.Mb_Code JOIN dbo.Emr_Mblb");
            strSql.Append(" ON Emr_Mblb.Mblb_Code = Emr_Mb.Mblb_Code where Yl_Event='" + Yl_Event +
                "') t WHERE t.Mb_Code=emr_mb.Mb_Code AND t.Mblb_Code=dbo.Emr_Mblb.Mblb_Code)");
            strSql.Append(" and Emr_Mblb.Mblb_Code='" + Mblb_Code + "'");
               return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }
        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetEndNodes()
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Mblb_Code,Mblb_Name,Mblb_Jc,Father_Code ");
            strSql.Append(" FROM Emr_Mblb ");
            strSql.Append("  WHERE not EXISTS(SELECT 1 FROM dbo.Emr_Mblb a WHERE a.Father_Code=dbo.Emr_Mblb.Mblb_Code) ");
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" Mblb_Code,Mblb_Name,Mblb_Jc,Father_Code ");
			strSql.Append(" FROM Emr_Mblb ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}
        public DataSet getyzlis() {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM Emr_Mblb WHERE NOT EXISTS (SELECT 1 FROM Emr_Mblb a WHERE a.Father_Code=dbo.Emr_Mblb.Mblb_Code) ");
            
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());

        }

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM Emr_Mblb ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.Mblb_Code desc");
			}
			strSql.Append(")AS Row, T.*  from Emr_Mblb T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Emr_Mblb";
			parameters[1].Value = "Mblb_Code";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return HisVar.HisVar.Sqldal.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

