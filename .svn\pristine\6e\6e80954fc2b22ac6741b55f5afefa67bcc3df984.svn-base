﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class Cq_Cf1
    Inherits HisControl.BaseForm

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(Cq_Cf1))
        Me.Panel2 = New System.Windows.Forms.Panel()
        Me.T_Label3 = New System.Windows.Forms.Label()
        Me.T_Label4 = New System.Windows.Forms.Label()
        Me.C1Holder1 = New C1.Win.C1Command.C1CommandHolder()
        Me.Comm1 = New C1.Win.C1Command.C1Command()
        Me.Comm2 = New C1.Win.C1Command.C1Command()
        Me.Comm3 = New C1.Win.C1Command.C1Command()
        Me.Comm4 = New C1.Win.C1Command.C1Command()
        Me.Comm5 = New C1.Win.C1Command.C1Command()
        Me.Comm6 = New C1.Win.C1Command.C1Command()
        Me.Comm8 = New C1.Win.C1Command.C1Command()
        Me.C1TrueDBGrid1 = New C1.Win.C1TrueDBGrid.C1TrueDBGrid()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.C1Label1 = New C1.Win.C1Input.C1Label()
        Me.T_Line3 = New System.Windows.Forms.Label()
        Me.ToolBar1 = New C1.Win.C1Command.C1ToolBar()
        Me.Link1 = New C1.Win.C1Command.C1CommandLink()
        Me.Link2 = New C1.Win.C1Command.C1CommandLink()
        Me.Link3 = New C1.Win.C1Command.C1CommandLink()
        Me.C1CommandLink1 = New C1.Win.C1Command.C1CommandLink()
        Me.C1CommandLink2 = New C1.Win.C1Command.C1CommandLink()
        Me.C1CommandLink3 = New C1.Win.C1Command.C1CommandLink()
        Me.C1CommandLink5 = New C1.Win.C1Command.C1CommandLink()
        Me.T_Line1 = New System.Windows.Forms.Label()
        Me.T_Line2 = New System.Windows.Forms.Label()
        Me.T_Combo = New C1.Win.C1List.C1Combo()
        Me.T_Textbox = New C1.Win.C1Input.C1TextBox()
        Me.T_Label = New C1.Win.C1Input.C1Label()
        Me.Panel2.SuspendLayout
        CType(Me.C1Holder1,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.C1TrueDBGrid1,System.ComponentModel.ISupportInitialize).BeginInit
        Me.Panel1.SuspendLayout
        CType(Me.C1Label1,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.T_Combo,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.T_Textbox,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.T_Label,System.ComponentModel.ISupportInitialize).BeginInit
        Me.SuspendLayout
        '
        'Panel2
        '
        Me.Panel2.BackColor = System.Drawing.Color.Transparent
        Me.Panel2.Controls.Add(Me.T_Label3)
        Me.Panel2.Controls.Add(Me.T_Label4)
        Me.Panel2.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.Panel2.Location = New System.Drawing.Point(0, 581)
        Me.Panel2.Name = "Panel2"
        Me.Panel2.Size = New System.Drawing.Size(975, 21)
        Me.Panel2.TabIndex = 7
        '
        'T_Label3
        '
        Me.T_Label3.AutoSize = true
        Me.T_Label3.Location = New System.Drawing.Point(394, 4)
        Me.T_Label3.Name = "T_Label3"
        Me.T_Label3.Size = New System.Drawing.Size(47, 12)
        Me.T_Label3.TabIndex = 6
        Me.T_Label3.Text = "∑本日="
        Me.T_Label3.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'T_Label4
        '
        Me.T_Label4.AutoSize = true
        Me.T_Label4.BackColor = System.Drawing.SystemColors.ButtonFace
        Me.T_Label4.ForeColor = System.Drawing.Color.DarkRed
        Me.T_Label4.Location = New System.Drawing.Point(472, 4)
        Me.T_Label4.Name = "T_Label4"
        Me.T_Label4.Size = New System.Drawing.Size(53, 12)
        Me.T_Label4.TabIndex = 5
        Me.T_Label4.Text = "T_Label4"
        Me.T_Label4.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'C1Holder1
        '
        Me.C1Holder1.Commands.Add(Me.Comm1)
        Me.C1Holder1.Commands.Add(Me.Comm2)
        Me.C1Holder1.Commands.Add(Me.Comm3)
        Me.C1Holder1.Commands.Add(Me.Comm4)
        Me.C1Holder1.Commands.Add(Me.Comm5)
        Me.C1Holder1.Commands.Add(Me.Comm6)
        Me.C1Holder1.Commands.Add(Me.Comm8)
        Me.C1Holder1.Owner = Me
        Me.C1Holder1.VisualStyle = C1.Win.C1Command.VisualStyle.Classic
        '
        'Comm1
        '
        Me.Comm1.Image = CType(resources.GetObject("Comm1.Image"),System.Drawing.Image)
        Me.Comm1.Name = "Comm1"
        Me.Comm1.Shortcut = System.Windows.Forms.Shortcut.CtrlZ
        Me.Comm1.ShortcutText = ""
        Me.Comm1.Text = "增加"
        Me.Comm1.ToolTipText = "增加记录"
        '
        'Comm2
        '
        Me.Comm2.Image = CType(resources.GetObject("Comm2.Image"),System.Drawing.Image)
        Me.Comm2.Name = "Comm2"
        Me.Comm2.Shortcut = System.Windows.Forms.Shortcut.CtrlS
        Me.Comm2.ShortcutText = ""
        Me.Comm2.Text = "删除"
        Me.Comm2.ToolTipText = "删除记录"
        '
        'Comm3
        '
        Me.Comm3.Image = CType(resources.GetObject("Comm3.Image"),System.Drawing.Image)
        Me.Comm3.Name = "Comm3"
        Me.Comm3.Shortcut = System.Windows.Forms.Shortcut.CtrlG
        Me.Comm3.ShortcutText = ""
        Me.Comm3.Text = "更新"
        Me.Comm3.ToolTipText = "更新记录"
        '
        'Comm4
        '
        Me.Comm4.Image = CType(resources.GetObject("Comm4.Image"),System.Drawing.Image)
        Me.Comm4.Name = "Comm4"
        Me.Comm4.ShortcutText = ""
        Me.Comm4.Text = "停止"
        '
        'Comm5
        '
        Me.Comm5.Image = CType(resources.GetObject("Comm5.Image"),System.Drawing.Image)
        Me.Comm5.Name = "Comm5"
        Me.Comm5.ShortcutText = ""
        Me.Comm5.Text = "开始"
        '
        'Comm6
        '
        Me.Comm6.Image = CType(resources.GetObject("Comm6.Image"),System.Drawing.Image)
        Me.Comm6.Name = "Comm6"
        Me.Comm6.ShortcutText = ""
        Me.Comm6.Text = "复制"
        '
        'Comm8
        '
        Me.Comm8.Image = CType(resources.GetObject("Comm8.Image"),System.Drawing.Image)
        Me.Comm8.Name = "Comm8"
        Me.Comm8.ShortcutText = ""
        Me.Comm8.Text = "延后结束时间"
        Me.Comm8.ToolTipText = "将医嘱结束时间延后一周，可以手动停止"
        '
        'C1TrueDBGrid1
        '
        Me.C1TrueDBGrid1.GroupByCaption = "Drag a column header here to group by that column"
        Me.C1TrueDBGrid1.Images.Add(CType(resources.GetObject("C1TrueDBGrid1.Images"),System.Drawing.Image))
        Me.C1TrueDBGrid1.Location = New System.Drawing.Point(104, 61)
        Me.C1TrueDBGrid1.Name = "C1TrueDBGrid1"
        Me.C1TrueDBGrid1.PreviewInfo.Caption = "PrintPreview窗口"
        Me.C1TrueDBGrid1.PreviewInfo.Location = New System.Drawing.Point(0, 0)
        Me.C1TrueDBGrid1.PreviewInfo.Size = New System.Drawing.Size(0, 0)
        Me.C1TrueDBGrid1.PreviewInfo.ZoomFactor = 75R
        Me.C1TrueDBGrid1.PrintInfo.MeasurementDevice = C1.Win.C1TrueDBGrid.PrintInfo.MeasurementDeviceEnum.Screen
        Me.C1TrueDBGrid1.PrintInfo.MeasurementPrinterName = Nothing
        Me.C1TrueDBGrid1.Size = New System.Drawing.Size(319, 182)
        Me.C1TrueDBGrid1.TabIndex = 10
        Me.C1TrueDBGrid1.Text = "C1TrueDBGrid1"
        Me.C1TrueDBGrid1.UseCompatibleTextRendering = false
        Me.C1TrueDBGrid1.PropBag = resources.GetString("C1TrueDBGrid1.PropBag")
        '
        'Panel1
        '
        Me.Panel1.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Panel1.Controls.Add(Me.C1Label1)
        Me.Panel1.Controls.Add(Me.T_Line3)
        Me.Panel1.Controls.Add(Me.ToolBar1)
        Me.Panel1.Controls.Add(Me.T_Line1)
        Me.Panel1.Controls.Add(Me.T_Line2)
        Me.Panel1.Controls.Add(Me.T_Combo)
        Me.Panel1.Controls.Add(Me.T_Textbox)
        Me.Panel1.Controls.Add(Me.T_Label)
        Me.Panel1.Dock = System.Windows.Forms.DockStyle.Top
        Me.Panel1.Location = New System.Drawing.Point(0, 0)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(975, 24)
        Me.Panel1.TabIndex = 0
        '
        'C1Label1
        '
        Me.C1Label1.AutoSize = true
        Me.C1Label1.BackColor = System.Drawing.SystemColors.Control
        Me.C1Label1.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1Label1.ForeColor = System.Drawing.Color.Maroon
        Me.C1Label1.Location = New System.Drawing.Point(582, 5)
        Me.C1Label1.Name = "C1Label1"
        Me.C1Label1.Size = New System.Drawing.Size(29, 12)
        Me.C1Label1.TabIndex = 9
        Me.C1Label1.Tag = Nothing
        Me.C1Label1.Text = "过滤"
        Me.C1Label1.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.C1Label1.TextDetached = true
        Me.C1Label1.TrimStart = true
        '
        'T_Line3
        '
        Me.T_Line3.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.T_Line3.Location = New System.Drawing.Point(802, -1)
        Me.T_Line3.Name = "T_Line3"
        Me.T_Line3.Size = New System.Drawing.Size(2, 25)
        Me.T_Line3.TabIndex = 7
        Me.T_Line3.Text = "Label2"
        '
        'ToolBar1
        '
        Me.ToolBar1.AccessibleName = "Tool Bar"
        Me.ToolBar1.CommandHolder = Me.C1Holder1
        Me.ToolBar1.CommandLinks.AddRange(New C1.Win.C1Command.C1CommandLink() {Me.Link1, Me.Link2, Me.Link3, Me.C1CommandLink1, Me.C1CommandLink2, Me.C1CommandLink3, Me.C1CommandLink5})
        Me.ToolBar1.Location = New System.Drawing.Point(1, 0)
        Me.ToolBar1.MinButtonSize = 22
        Me.ToolBar1.Movable = false
        Me.ToolBar1.Name = "ToolBar1"
        Me.ToolBar1.Size = New System.Drawing.Size(434, 22)
        Me.ToolBar1.Text = "C1ToolBar1"
        Me.ToolBar1.VisualStyle = C1.Win.C1Command.VisualStyle.Classic
        Me.ToolBar1.VisualStyleBase = C1.Win.C1Command.VisualStyle.Classic
        '
        'Link1
        '
        Me.Link1.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image),C1.Win.C1Command.ButtonLookFlags)
        Me.Link1.Command = Me.Comm1
        '
        'Link2
        '
        Me.Link2.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image),C1.Win.C1Command.ButtonLookFlags)
        Me.Link2.Command = Me.Comm2
        Me.Link2.SortOrder = 1
        Me.Link2.Text = "删除"
        '
        'Link3
        '
        Me.Link3.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image),C1.Win.C1Command.ButtonLookFlags)
        Me.Link3.Command = Me.Comm3
        Me.Link3.SortOrder = 2
        '
        'C1CommandLink1
        '
        Me.C1CommandLink1.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image),C1.Win.C1Command.ButtonLookFlags)
        Me.C1CommandLink1.Command = Me.Comm4
        Me.C1CommandLink1.SortOrder = 3
        '
        'C1CommandLink2
        '
        Me.C1CommandLink2.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image),C1.Win.C1Command.ButtonLookFlags)
        Me.C1CommandLink2.Command = Me.Comm5
        Me.C1CommandLink2.SortOrder = 4
        '
        'C1CommandLink3
        '
        Me.C1CommandLink3.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image),C1.Win.C1Command.ButtonLookFlags)
        Me.C1CommandLink3.Command = Me.Comm6
        Me.C1CommandLink3.SortOrder = 5
        '
        'C1CommandLink5
        '
        Me.C1CommandLink5.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image),C1.Win.C1Command.ButtonLookFlags)
        Me.C1CommandLink5.Command = Me.Comm8
        Me.C1CommandLink5.SortOrder = 6
        '
        'T_Line1
        '
        Me.T_Line1.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.T_Line1.Location = New System.Drawing.Point(574, -1)
        Me.T_Line1.Name = "T_Line1"
        Me.T_Line1.Size = New System.Drawing.Size(2, 25)
        Me.T_Line1.TabIndex = 4
        Me.T_Line1.Text = "Label1"
        '
        'T_Line2
        '
        Me.T_Line2.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.T_Line2.Location = New System.Drawing.Point(701, -1)
        Me.T_Line2.Name = "T_Line2"
        Me.T_Line2.Size = New System.Drawing.Size(2, 25)
        Me.T_Line2.TabIndex = 6
        Me.T_Line2.Text = "Label2"
        '
        'T_Combo
        '
        Me.T_Combo.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.T_Combo.Caption = ""
        Me.T_Combo.CaptionHeight = 17
        Me.T_Combo.CharacterCasing = System.Windows.Forms.CharacterCasing.Normal
        Me.T_Combo.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.T_Combo.Images.Add(CType(resources.GetObject("T_Combo.Images"),System.Drawing.Image))
        Me.T_Combo.ItemHeight = 15
        Me.T_Combo.Location = New System.Drawing.Point(810, 3)
        Me.T_Combo.MatchEntryTimeout = CType(2000,Long)
        Me.T_Combo.MaxDropDownItems = CType(5,Short)
        Me.T_Combo.MaxLength = 32767
        Me.T_Combo.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.T_Combo.Name = "T_Combo"
        Me.T_Combo.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.T_Combo.Size = New System.Drawing.Size(139, 16)
        Me.T_Combo.TabIndex = 2
        Me.T_Combo.TabStop = false
        Me.T_Combo.PropBag = resources.GetString("T_Combo.PropBag")
        '
        'T_Textbox
        '
        Me.T_Textbox.AutoSize = false
        Me.T_Textbox.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.T_Textbox.Location = New System.Drawing.Point(615, 4)
        Me.T_Textbox.Name = "T_Textbox"
        Me.T_Textbox.Size = New System.Drawing.Size(81, 14)
        Me.T_Textbox.TabIndex = 3
        Me.T_Textbox.TabStop = false
        Me.T_Textbox.Tag = Nothing
        Me.T_Textbox.TextDetached = true
        Me.T_Textbox.TrimStart = true
        Me.T_Textbox.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        '
        'T_Label
        '
        Me.T_Label.AutoSize = true
        Me.T_Label.BackColor = System.Drawing.SystemColors.Control
        Me.T_Label.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.T_Label.ForeColor = System.Drawing.Color.Maroon
        Me.T_Label.Location = New System.Drawing.Point(709, 5)
        Me.T_Label.Name = "T_Label"
        Me.T_Label.Size = New System.Drawing.Size(47, 12)
        Me.T_Label.TabIndex = 2
        Me.T_Label.Tag = Nothing
        Me.T_Label.Text = "T_Label"
        Me.T_Label.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.T_Label.TextDetached = true
        Me.T_Label.TrimStart = true
        '
        'Cq_Cf1
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6!, 12!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(975, 602)
        Me.Controls.Add(Me.C1TrueDBGrid1)
        Me.Controls.Add(Me.Panel1)
        Me.Controls.Add(Me.Panel2)
        Me.Icon = CType(resources.GetObject("$this.Icon"),System.Drawing.Icon)
        Me.Name = "Cq_Cf1"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "长期处方"
        Me.Panel2.ResumeLayout(false)
        Me.Panel2.PerformLayout
        CType(Me.C1Holder1,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.C1TrueDBGrid1,System.ComponentModel.ISupportInitialize).EndInit
        Me.Panel1.ResumeLayout(false)
        Me.Panel1.PerformLayout
        CType(Me.C1Label1,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.T_Combo,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.T_Textbox,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.T_Label,System.ComponentModel.ISupportInitialize).EndInit
        Me.ResumeLayout(false)

End Sub
    Friend WithEvents Panel2 As System.Windows.Forms.Panel
    Friend WithEvents T_Label3 As System.Windows.Forms.Label
    Friend WithEvents T_Label4 As System.Windows.Forms.Label
    Friend WithEvents C1Holder1 As C1.Win.C1Command.C1CommandHolder
    Friend WithEvents Comm1 As C1.Win.C1Command.C1Command
    Friend WithEvents Comm2 As C1.Win.C1Command.C1Command
    Friend WithEvents Comm3 As C1.Win.C1Command.C1Command
    Friend WithEvents C1TrueDBGrid1 As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents ToolBar1 As C1.Win.C1Command.C1ToolBar
    Friend WithEvents Link1 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link2 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link3 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents T_Line1 As System.Windows.Forms.Label
    Friend WithEvents T_Line2 As System.Windows.Forms.Label
    Friend WithEvents T_Label As C1.Win.C1Input.C1Label
    Friend WithEvents T_Line3 As System.Windows.Forms.Label
    Friend WithEvents C1CommandLink1 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Comm4 As C1.Win.C1Command.C1Command
    Friend WithEvents Comm5 As C1.Win.C1Command.C1Command
    Friend WithEvents C1CommandLink2 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Comm6 As C1.Win.C1Command.C1Command
    Friend WithEvents C1CommandLink3 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents C1Label1 As C1.Win.C1Input.C1Label
    Friend WithEvents T_Combo As C1.Win.C1List.C1Combo
    Friend WithEvents T_Textbox As C1.Win.C1Input.C1TextBox
    Friend WithEvents Comm8 As C1.Win.C1Command.C1Command
    Friend WithEvents C1CommandLink5 As C1.Win.C1Command.C1CommandLink

End Class
