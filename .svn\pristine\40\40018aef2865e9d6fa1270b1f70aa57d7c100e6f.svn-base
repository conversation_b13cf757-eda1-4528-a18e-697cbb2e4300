﻿Imports System.Data.SqlClient
Imports Stimulsoft.Report

Public Class Lis_TestXmRpt

    Dim BllLIS_TestXm As New BLLOld.B_LIS_TestXm
#Region "传入参数"
    Dim rRow As DataRow
#End Region
    Public Sub New(ByVal tRow As DataRow)

        ' 此调用是设计器所必需的。
        InitializeComponent()
        rRow = tRow
        AddHandler Stimulsoft.Report.StiOptions.Engine.GlobalEvents.SavingReportInDesigner, AddressOf GlobalEvents_SavingReportInDesigner
        AddHandler Stimulsoft.Report.StiOptions.Engine.GlobalEvents.LoadingReportInDesigner, AddressOf GlobalEvents_LoadingReportInDesigner
        ' 在 InitializeComponent() 调用之后添加任何初始化。

    End Sub

    Private Sub Ylbsy_Load(sender As System.Object, e As System.EventArgs) Handles MyBase.Load

        Rpt_Init()
    End Sub

    Private Sub Rpt_Init()
        Dim ylbByte As Byte()
        If IsDBNull(rRow.Item("TestXmRpt")) = True Then
            ylbByte = System.IO.File.ReadAllBytes(".\项目报告单.mrt")
        Else
            ylbByte = rRow.Item("TestXmRpt")
        End If

        Dim StiRpt As New StiReport
        StiRpt.Load(ylbByte)
        StiRpt.Render()
        StiDesignerControl1.Report = StiRpt
    End Sub


    Private Sub GlobalEvents_LoadingReportInDesigner(ByVal sender As Object, ByVal e As Stimulsoft.Report.Design.StiLoadingObjectEventArgs)
    End Sub

    Private Sub GlobalEvents_SavingReportInDesigner(ByVal sender As Object, ByVal e As Stimulsoft.Report.Design.StiSavingObjectEventArgs)
        Try
            Dim lbytContents() As Byte
            If StiDesignerControl1.Report Is Nothing Then
                Exit Sub
            End If
            lbytContents = StiDesignerControl1.Report.SaveToByteArray
            Dim ModelLIS_TestXm As ModelOld.M_LIS_TestXm = BllLIS_TestXm.GetModel(rRow("TestXm_Code"))
            ModelLIS_TestXm.TestXmRpt = lbytContents
            BllLIS_TestXm.Update(ModelLIS_TestXm)
            rRow("TestXmRpt") = lbytContents
            rRow.AcceptChanges()
            HisControl.msg.Show("保存成功！")
        Catch ex As Exception
            ' MsgBox(ex.ToString, MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation + MsgBoxStyle.DefaultButton1, "警告")
        End Try

    End Sub

    Private Sub Ylbsy_FormClosing(sender As System.Object, e As System.Windows.Forms.FormClosingEventArgs) Handles MyBase.FormClosing
        Me.Dispose()
    End Sub
End Class
