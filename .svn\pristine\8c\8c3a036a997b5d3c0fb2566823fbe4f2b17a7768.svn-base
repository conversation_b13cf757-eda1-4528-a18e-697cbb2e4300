﻿Imports System.Data.SqlClient
Imports Stimulsoft.Report
Public Class Money_Tj1
    Dim My_Cm As CurrencyManager
    Dim My_View As DataView
    Public My_Dataset As New DataSet

    Dim Order_Str As String = ""


    Private Sub Money_Tj2_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Call Init_Data()

        Label7.Text = "无"
    End Sub

    Private Sub Init_Data()
        BaseFunc.BaseFunc.Init_Datetimepicker(DateTimePicker1)
        BaseFunc.BaseFunc.Init_Datetimepicker(DateTimePicker2)
        DateTimePicker1.Value = Format(Now, "yyyy-MM-01 00:00:00")
        DateTimePicker2.Value = Format(Now, "yyyy-MM-dd 23:59:59")

        Dim My_Grid As New BaseClass.C_Grid(Me.C1TrueDBGrid1)
        With My_Grid
            .Init_Grid()
            .Init_Column("查询类别", "V_Lb", 70, "中", "")
            .Init_Column("科室名称", "Ks_Name", 80, "左", "")
            .Init_Column("医生姓名", "Ys_Name", 80, "左", "")
            .Init_Column("费用类别", "Dl_Name", 80, "左", "")
            .Init_Column("患者类别", "Bxlb_Name", 80, "左", "")
            .Init_Column("人员姓名", "Ry_Name", 60, "左", "")
            .Init_Column("处方时间", "Mz_Date", 130, "中", "yyyy-MM-dd HH:mm:ss")
            .Init_Column("项目名称", "Yp_Name", 180, "左", "")
            .Init_Column("规格", "Mx_Gg", 80, "左", "")
            '.Init_Column("国药准字", "Mx_GyZz", 80, "左", "")
            '.Init_Column("生产批号", "Yp_Ph", 80, "左", "")
            '.Init_Column("有效期", "Yp_Yxq", 75, "中", "yyyy-MM-dd")
            .Init_Column("单位", "Mx_XsDw", 50, "中", "")
            .Init_Column("单价", "Mz_Dj", 80, "右", "0.00##")
            .Init_Column("数量", "Mz_Sl", 60, "右", "0.###")
            .Init_Column("金额", "Mz_Money", 60, "右", "0.00##")
        End With

        C1TrueDBGrid1.Splits(0).DisplayColumns("V_Lb").Visible = False
        C1TrueDBGrid1.Splits(0).DisplayColumns("Ks_Name").Visible = False
        C1TrueDBGrid1.Splits(0).DisplayColumns("Ys_Name").Visible = False
        C1TrueDBGrid1.Splits(0).DisplayColumns("Dl_Name").Visible = False
        C1TrueDBGrid1.Splits(0).DisplayColumns("Bxlb_Name").Visible = False

        C1TrueDBGrid1.HScrollBar.Style = C1.Win.C1TrueDBGrid.ScrollBarStyleEnum.Automatic
        C1TrueDBGrid1.DataView = C1.Win.C1TrueDBGrid.DataViewEnum.GroupBy
        C1TrueDBGrid1.GroupByAreaVisible = False
        C1TrueDBGrid1.Columns("Mz_Money").Aggregate = C1.Win.C1TrueDBGrid.AggregateEnum.Sum

        Dim My_Combo As New BaseClass.C_Combo1(Me.C1Combo1)
        With My_Combo
            .Init_TDBCombo()
            .AddItem("全部")
            .AddItem("住院")
            .AddItem("门诊")

            .SelectedIndex(0)
        End With
        With C1Combo1
            .BorderStyle = BorderStyle.Fixed3D
            .Height = 22
            .Width = 60
            .DropDownWidth = 60
        End With

        Dim My_Combo1 As New BaseClass.C_Combo1(Me.C1Combo2)
        With My_Combo1
            .Init_TDBCombo()
            .AddItem("按日清统计")
            .AddItem("按出院统计")
            .SelectedIndex(0)
        End With
        With C1Combo2
            .BorderStyle = BorderStyle.Fixed3D
            .Height = 22
            .Width = 93
            .DropDownWidth = 93
        End With
    End Sub

    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click
        Me.Cursor = Cursors.WaitCursor
        Dim V_Str As String = ""
        Dim V_Str1 As String = ""
        Dim V_Str2 As String = ""
        Dim Rs_Str As String = "" '人数汇总
        Dim Rs_Str1 As String = "" '人数汇总
        Dim Rs_Str2 As String = "" '人数汇总


        If C1Combo1.Text = "全部" Or C1Combo1.Text = "门诊" Then
            V_Str1 = "select '门诊' as V_Lb,Ks_Name,Zd_YyKs.Ks_Code,Ys_Name,Zd_YyYs.Ys_Code,'X'+isnull(Xl_Code,Dl_Code) as Dl_Code,CASE  WHEN Xl_Name is NULL then Dl_Name ELSE  Dl_Name+'-'+Xl_Name END Dl_Name,Ry_Name,Mz_Date+Mz_Time as Mz_Date,Yp_Name,Mx_Gg,Mx_XsDw,Mz_Dj,Mz_Sl,Mz_YP_Sum.Mz_Money,Bxlb_Name from Zd_Bxlb,Mz_Sum,Mz_YP_Sum,Zd_Yyks,V_Ypkc,Zd_YyYs,Mz_Jz where Mz_Sum.Bxlb_Code=Zd_Bxlb.Bxlb_Code  and Mz_Sum.Jz_Code=Mz_Jz.Jz_Code and Not exists (select Mz_Ph+Mz_Code from Mz_Ty_Sum where Mz_Ty_Sum.mz_ph=Mz_YP_Sum.Mz_Ph and Mz_Ty_Sum.Mz_code=Mz_YP_Sum.Mz_Code) And Mz_Sum.Mz_Code=Mz_YP_Sum.Mz_Code and Mz_Sum.Ks_Code=Zd_Yyks.Ks_Code and Zd_Yyys.Ys_Code=Mz_Sum.Ys_Code and Mz_Yp_Sum.Xx_Code=V_YpKc.Xx_Code and CONVERT(VARCHAR(10),Jz_Date,120) + ' ' +Jz_Time>='" & DateTimePicker1.Value.ToString("yyyy-MM-dd HH:mm:ss") & "' and CONVERT(VARCHAR(10),Jz_Date,120) + ' ' +Jz_Time<='" & DateTimePicker2.Value.ToString("yyyy-MM-dd HH:mm:ss")  & "'  " & _
                 "  Union all select '门诊' as V_Lb,Ks_Name,Zd_YyKs.Ks_Code,Ys_Name,Zd_YyYs.Ys_Code,'Y'+Zd_Ml_Xm3.Xmlb_Code as Dl_Code,Xmlb_Name as Dl_Name,Ry_Name,Mz_Date+Mz_Time as Mz_Date,Xm_Name as Yp_Name,''Mx_Gg,Xm_Dw as Mx_XsDw,Mz_Dj,Mz_Sl,Mz_Xm_Sum.Mz_Money,Bxlb_Name from Zd_Bxlb, Mz_Sum,Mz_Xm_Sum,Zd_Yyks,Zd_ML_Xm3,Zd_Ml_Xm1,Zd_YyYs,Mz_Jz where Mz_Sum.Bxlb_Code=Zd_Bxlb.Bxlb_Code  and Mz_Sum.Jz_Code=Mz_Jz.Jz_Code and not exists (select Mz_Ph+Mz_Code from Mz_Ty_Sum where Mz_Ty_Sum.mz_ph=Mz_Xm_Sum.Mz_Ph and Mz_Ty_Sum.Mz_code=Mz_Xm_Sum.Mz_Code) And Mz_Sum.Mz_Code=Mz_Xm_Sum.Mz_Code and Mz_Sum.Ks_Code=Zd_Yyks.Ks_Code and Zd_Yyys.Ys_Code=Mz_Sum.Ys_Code and Mz_Xm_Sum.Xm_Code=Zd_Ml_Xm3.Xm_Code and Zd_Ml_Xm1.Xmlb_Code=Zd_ML_Xm3.Xmlb_Code and CONVERT(VARCHAR(10),Jz_Date,120) + ' ' +Jz_Time>='" & DateTimePicker1.Value.ToString("yyyy-MM-dd HH:mm:ss")  & "' and CONVERT(VARCHAR(10),Jz_Date,120) + ' ' +Jz_Time<='" & DateTimePicker2.Value.ToString("yyyy-MM-dd HH:mm:ss")  & "'  "

            Rs_Str1 = "select  '门诊'+Mz_Sum.Mz_Code as Mz_Code,Ys_Code,Ks_Code from Mz_Yp_Sum,Mz_Sum,Mz_Jz where Not Exists (Select Mz_Code from Mz_Ty_Sum where Mz_Ty_Sum.Mz_Code=Mz_Yp_Sum.Mz_Code and Mz_Ty_Sum.Mz_Ph=Mz_Yp_Sum.Mz_Ph) and Mz_Sum.Mz_Code=Mz_Yp_Sum.Mz_Code and Mz_Sum.Jz_Code=Mz_Jz.Jz_Code  and CONVERT(VARCHAR(10),Jz_Date,120) + ' ' +Jz_Time>='" & DateTimePicker1.Value.ToString("yyyy-MM-dd HH:mm:ss")  & "' and CONVERT(VARCHAR(10),Jz_Date,120) + ' ' +Jz_Time<='" & DateTimePicker2.Value.ToString("yyyy-MM-dd HH:mm:ss")  & "' Union all select  '门诊'+Mz_Sum.Mz_Code as Mz_Code,Ys_Code,Ks_Code from Mz_Xm_Sum,Mz_Sum,Mz_Jz where Not Exists (Select Mz_Code from Mz_Ty_Sum where Mz_Ty_Sum.Mz_Code=Mz_Xm_Sum.Mz_Code and Mz_Ty_Sum.Mz_Ph=Mz_Xm_Sum.Mz_Ph) and Mz_Sum.Mz_Code=Mz_Xm_Sum.Mz_Code and Mz_Sum.Jz_Code=Mz_Jz.Jz_Code  and CONVERT(VARCHAR(10),Jz_Date,120) + ' ' +Jz_Time>='" & DateTimePicker1.Value.ToString("yyyy-MM-dd HH:mm:ss")  & "' and CONVERT(VARCHAR(10),Jz_Date,120) + ' ' +Jz_Time<='" & DateTimePicker2.Value.ToString("yyyy-MM-dd HH:mm:ss")  & "' "

        Else
            V_Str1 = ""
            Rs_Str1 = ""
        End If

        If C1Combo1.Text = "全部" Or C1Combo1.Text = "住院" Then
            If C1Combo1.Text = "全部" Then
                V_Str1 = V_Str1 & " UNION ALL "
                Rs_Str1 = Rs_Str1 & "  UNION ALL  "
            End If


            If C1Combo2.Text = "按日清统计" Then
                V_Str2 = " select '住院' as V_Lb,Ks_Name,Zd_YyKs.Ks_Code,Ys_Name,Zd_YyYs.Ys_Code,'X'+isnull(Xl_Code,Dl_Code) as Dl_Code,CASE  WHEN Xl_Name is NULL then Dl_Name ELSE  Dl_Name+'-'+Xl_Name END Dl_Name,Ry_Name,Cf_Date+Cf_Time as Mz_Date,Yp_Name,Mx_Gg,Mx_XsDw,Cf_Dj as Mz_Dj,Cf_Sl as Mz_Sl,Bl_CfYP.Cf_Money as Mz_Money ,Bxlb_Name from Zd_Bxlb, Bl,Bl_Cf,Bl_CfYp,Zd_Yyks,V_Ypkc,Zd_YyYs,Bl_Jz where Zd_Bxlb.Bxlb_Code=Bl.Bxlb_Code and Bl.Bl_Code=Bl_Cf.Bl_Code  and Bl_Cf.Jz_Code=Bl_Jz.Jz_Code  And Bl_Cf.Cf_Code=Bl_CfYp.Cf_Code and Bl_Cf.Ks_Code=Zd_Yyks.Ks_Code and Zd_Yyys.Ys_Code=Bl_Cf.Ys_Code and Bl_CfYp.Xx_Code=V_YpKc.Xx_Code and CONVERT(VARCHAR(10),Jz_Date,120) + ' ' +Jz_Time>='" & DateTimePicker1.Value.ToString("yyyy-MM-dd HH:mm:ss") & "' and CONVERT(VARCHAR(10),Jz_Date,120) + ' ' +Jz_Time<='" & DateTimePicker2.Value.ToString("yyyy-MM-dd HH:mm:ss")  & "'  " & _
                "  Union all select '住院' as V_Lb,Ks_Name,Zd_YyKs.Ks_Code,Ys_Name,Zd_YyYs.Ys_Code,'Y'+Zd_Ml_Xm3.Xmlb_Code as Dl_Code,Xmlb_Name as Dl_Name,Ry_Name,Cf_Date+Cf_Time as Mz_Date,Xm_Name as Yp_Name,''Mx_Gg,Xm_Dw as Mx_XsDw,Cf_Dj as Mz_Dj,Cf_Sl as Mz_Sl,Bl_CfXm.Cf_Money as Mz_Money ,Bxlb_Name from Zd_Bxlb, Bl,Bl_Cf,Bl_CfXm,Zd_Yyks,Zd_ML_Xm3,Zd_Ml_Xm1,Zd_YyYs,Bl_Jz where Zd_Bxlb.Bxlb_Code=Bl.Bxlb_Code and Bl.Bl_Code=Bl_Cf.Bl_Code  and Bl_Cf.Jz_Code=Bl_Jz.Jz_Code  And Bl_Cf.Cf_Code=Bl_CfXm.Cf_Code and Bl_Cf.Ks_Code=Zd_Yyks.Ks_Code and Zd_Yyys.Ys_Code=Bl_Cf.Ys_Code and Bl_CfXm.Xm_Code=Zd_Ml_Xm3.Xm_Code and Zd_Ml_Xm1.Xmlb_Code=Zd_ML_Xm3.Xmlb_Code and CONVERT(VARCHAR(10),Jz_Date,120) + ' ' +Jz_Time>='" & DateTimePicker1.Value.ToString("yyyy-MM-dd HH:mm:ss")  & "' and CONVERT(VARCHAR(10),Jz_Date,120) + ' ' +Jz_Time<='" & DateTimePicker2.Value.ToString("yyyy-MM-dd HH:mm:ss")  & "'  "

                Rs_Str2 = " Select  '住院'+Bl_Code as Mz_Code,Ys_Code,Ks_Code from Bl_Cf,Bl_Jz where Bl_Cf.Jz_Code=Bl_Jz.Jz_Code and CONVERT(VARCHAR(10),Jz_Date,120) + ' ' +Jz_Time>='" & DateTimePicker1.Value.ToString("yyyy-MM-dd HH:mm:ss") & "' and CONVERT(VARCHAR(10),Jz_Date,120) + ' ' +Jz_Time<='" & DateTimePicker2.Value.ToString("yyyy-MM-dd HH:mm:ss") & "' "

            ElseIf C1Combo2.Text = "按出院统计" Then

                V_Str2 = " select '住院' as V_Lb,Ks_Name,Zd_YyKs.Ks_Code,Ys_Name,Zd_YyYs.Ys_Code,'X'+isnull(Xl_Code,Dl_Code) as Dl_Code,CASE  WHEN Xl_Name is NULL then Dl_Name ELSE  Dl_Name+'-'+Xl_Name END Dl_Name,Ry_Name,Cf_Date+Cf_Time as Mz_Date,Yp_Name,Mx_Gg,Mx_XsDw,Cf_Dj as Mz_Dj,Cf_Sl as Mz_Sl,Bl_CfYP.Cf_Money as Mz_Money ,Bxlb_Name from Zd_Bxlb, Bl,Bl_Cf,Bl_CfYp,Zd_Yyks,V_Ypkc,Zd_YyYs where Zd_Bxlb.Bxlb_Code=Bl.Bxlb_Code and Bl.Bl_Code=Bl_Cf.Bl_Code  And Bl_Cf.Cf_Code=Bl_CfYp.Cf_Code and Bl_Cf.Ks_Code=Zd_Yyks.Ks_Code and Zd_Yyys.Ys_Code=Bl_Cf.Ys_Code and Bl_CfYp.Xx_Code=V_YpKc.Xx_Code and Ry_CyDate>='" & DateTimePicker1.Value.ToString("yyyy-MM-dd HH:mm:ss") & "' and Ry_CyDate<='" & DateTimePicker2.Value.ToString("yyyy-MM-dd HH:mm:ss") & "'  " & _
                         "  Union all select '住院' as V_Lb,Ks_Name,Zd_YyKs.Ks_Code,Ys_Name,Zd_YyYs.Ys_Code,'Y'+Zd_Ml_Xm3.Xmlb_Code as Dl_Code,Xmlb_Name as Dl_Name,Ry_Name,Cf_Date+Cf_Time as Mz_Date,Xm_Name as Yp_Name,''Mx_Gg,Xm_Dw as Mx_XsDw,Cf_Dj as Mz_Dj,Cf_Sl as Mz_Sl,Bl_CfXm.Cf_Money as Mz_Money ,Bxlb_Name from Zd_Bxlb, Bl,Bl_Cf,Bl_CfXm,Zd_Yyks,Zd_ML_Xm3,Zd_Ml_Xm1,Zd_YyYs where Zd_Bxlb.Bxlb_Code=Bl.Bxlb_Code and Bl.Bl_Code=Bl_Cf.Bl_Code And Bl_Cf.Cf_Code=Bl_CfXm.Cf_Code and Bl_Cf.Ks_Code=Zd_Yyks.Ks_Code and Zd_Yyys.Ys_Code=Bl_Cf.Ys_Code and Bl_CfXm.Xm_Code=Zd_Ml_Xm3.Xm_Code and Zd_Ml_Xm1.Xmlb_Code=Zd_ML_Xm3.Xmlb_Code and Ry_CyDate>='" & DateTimePicker1.Value.ToString("yyyy-MM-dd HH:mm:ss")  & "' and Ry_CyDate<='" & DateTimePicker2.Value.ToString("yyyy-MM-dd HH:mm:ss")  & "'  "

                Rs_Str2 = " select  '住院'+Bl_Code as Mz_Code,Ys_Code,Ks_Code from Bl where Ry_CyDate>='" & DateTimePicker1.Value.ToString("yyyy-MM-dd HH:mm:ss") & "' and Ry_CyDate<='" & DateTimePicker2.Value.ToString("yyyy-MM-dd HH:mm:ss") & "' "


            End If
        Else
            V_Str2 = " "
            Rs_Str2 = ""
        End If

        V_Str = V_Str1 & V_Str2 & "Order by Dl_Code"

        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, V_Str, "收入统计", True)

        Rs_Str = "Select Count(Distinct Mz_Code) as Rs,Ks_Code,Ys_Code from ( " & Rs_Str1 & Rs_Str2 & ") Rs_Tab Group by Ks_Code,Ys_Code"
        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, Rs_Str, "人数", True)



        With Me.C1TrueDBGrid1
            My_Cm = CType(BindingContext(My_Dataset, "收入统计"), CurrencyManager)
            .SetDataBinding(My_Dataset, "收入统计", True)

            My_View = My_Cm.List

            Try
                If Order_Str <> "" Then
                    My_View.Sort = Mid(Order_Str, 2)
                End If
            Catch ex As Exception

            End Try


            Dim Sum_Money As Double
            Sum_Money = IIf(My_View.Table.Compute("Sum(Mz_Money)", "") Is DBNull.Value, 0, My_View.Table.Compute("Sum(Mz_Money)", ""))

            .ColumnFooters = True
            .Columns(12).FooterText = Format(Sum_Money, "###0.00")


        End With
        Me.Cursor = Cursors.Default
    End Sub


    Private Sub CheckBox1_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox1.CheckedChanged
        If CheckBox1.Checked = True Then
            C1TrueDBGrid1.GroupedColumns.Add(Me.C1TrueDBGrid1.Columns("V_Lb"))
            Order_Str = Order_Str & ",V_Lb"
            Label7.Text = Label7.Text & ",查询类别"
        Else
            C1TrueDBGrid1.GroupedColumns.RemoveAt(C1TrueDBGrid1.GroupedColumns.IndexOf(Me.C1TrueDBGrid1.Columns("V_Lb")))
            Order_Str = Order_Str.Replace(",V_Lb", "")
            Label7.Text = Label7.Text.Replace(",查询类别", "")
            Label7.Text = Label7.Text.Replace("查询类别", "无")
        End If

        Label7.Text = Label7.Text.Replace("无,", "")
        Try
            If Order_Str <> "" Then
                My_View.Sort = Mid(Order_Str, 2)

            End If

        Catch ex As Exception

        End Try

    End Sub

    Private Sub CheckBox2_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox2.CheckedChanged
        If CheckBox2.Checked = True Then
            C1TrueDBGrid1.GroupedColumns.Add(Me.C1TrueDBGrid1.Columns("Ks_Name"))
            Order_Str = Order_Str & ",Ks_Name"
            Label7.Text = Label7.Text & ",科室名称"
            If HisPara.PublicConfig.XqName.Contains("丰南") Then
                CheckBox6.Enabled = False
            End If

        Else
            C1TrueDBGrid1.GroupedColumns.RemoveAt(C1TrueDBGrid1.GroupedColumns.IndexOf(Me.C1TrueDBGrid1.Columns("Ks_Name")))
            Order_Str = Order_Str.Replace(",Ks_Name", "")
            Label7.Text = Label7.Text.Replace(",科室名称", "")
            Label7.Text = Label7.Text.Replace("科室名称", "无")
            If CheckBox3.Checked = False And HisPara.PublicConfig.XqName.Contains("丰南") Then
                CheckBox6.Enabled = True
            End If
        End If
        Label7.Text = Label7.Text.Replace("无,", "")
        Try
            If Order_Str <> "" Then
                My_View.Sort = Mid(Order_Str, 2)
            End If

        Catch ex As Exception

        End Try
    End Sub

    Private Sub CheckBox3_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox3.CheckedChanged
        If CheckBox3.Checked = True Then
            C1TrueDBGrid1.GroupedColumns.Add(Me.C1TrueDBGrid1.Columns("Ys_Name"))
            Order_Str = Order_Str & ",Ys_Name"
            Label7.Text = Label7.Text & ",医生名称"
            If HisPara.PublicConfig.XqName.Contains("丰南") Then
                CheckBox6.Enabled = False
            End If

        Else
            C1TrueDBGrid1.GroupedColumns.RemoveAt(C1TrueDBGrid1.GroupedColumns.IndexOf(Me.C1TrueDBGrid1.Columns("Ys_Name")))
            Order_Str = Order_Str.Replace(",Ys_Name", "")
            Label7.Text = Label7.Text.Replace(",医生名称", "")
            Label7.Text = Label7.Text.Replace("医生名称", "无")
            If CheckBox2.Checked = False And HisPara.PublicConfig.XqName.Contains("丰南") Then
                CheckBox6.Enabled = True
            End If
        End If
        Label7.Text = Label7.Text.Replace("无,", "")
        Try
            If Order_Str <> "" Then
                My_View.Sort = Mid(Order_Str, 2)
            End If

        Catch ex As Exception

        End Try
    End Sub

    Private Sub CheckBox4_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox4.CheckedChanged
        If CheckBox4.Checked = True Then
            C1TrueDBGrid1.GroupedColumns.Add(Me.C1TrueDBGrid1.Columns("Dl_Name"))
            C1TrueDBGrid1.Columns("Mz_Sl").Aggregate = C1.Win.C1TrueDBGrid.AggregateEnum.Sum


            Order_Str = Order_Str & ",Dl_Code"
            Label7.Text = Label7.Text & ",费用类别"

        Else
            C1TrueDBGrid1.GroupedColumns.RemoveAt(C1TrueDBGrid1.GroupedColumns.IndexOf(Me.C1TrueDBGrid1.Columns("Dl_Name")))
            If CheckBox6.Checked = False Then
                C1TrueDBGrid1.Columns("Mz_Sl").Aggregate = C1.Win.C1TrueDBGrid.AggregateEnum.None
            End If

            Order_Str = Order_Str.Replace(",Dl_Code", "")
            Label7.Text = Label7.Text.Replace(",费用类别", "")
            Label7.Text = Label7.Text.Replace("费用类别", "无")
        End If
        Label7.Text = Label7.Text.Replace("无,", "")
        Try
            If Order_Str <> "" Then
                My_View.Sort = Mid(Order_Str, 2)
            End If

        Catch ex As Exception

        End Try

    End Sub

    Private Sub CheckBox5_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox5.CheckedChanged
        If CheckBox5.Checked = True Then
            C1TrueDBGrid1.GroupedColumns.Add(Me.C1TrueDBGrid1.Columns("Bxlb_Name"))

            Order_Str = Order_Str & ",Bxlb_Name"
            Label7.Text = Label7.Text & ",患者类别"

        Else
            C1TrueDBGrid1.GroupedColumns.RemoveAt(C1TrueDBGrid1.GroupedColumns.IndexOf(Me.C1TrueDBGrid1.Columns("Bxlb_Name")))

            Order_Str = Order_Str.Replace(",Bxlb_Name", "")
            Label7.Text = Label7.Text.Replace(",患者类别", "")
            Label7.Text = Label7.Text.Replace("患者类别", "无")
        End If
        Label7.Text = Label7.Text.Replace("无,", "")
        Try
            If Order_Str <> "" Then
                My_View.Sort = Mid(Order_Str, 2)
            End If

        Catch ex As Exception

        End Try
    End Sub

    Private Sub CheckBox6_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckBox6.CheckedChanged
        If CheckBox6.Checked = True Then
            C1TrueDBGrid1.GroupedColumns.Add(Me.C1TrueDBGrid1.Columns("Yp_Name"))
            C1TrueDBGrid1.Columns("Mz_Sl").Aggregate = C1.Win.C1TrueDBGrid.AggregateEnum.Sum
            Order_Str = Order_Str & ",Yp_Name"
            Label7.Text = Label7.Text & ",项目名称"
            If HisPara.PublicConfig.XqName.Contains("丰南") Then
                CheckBox2.Enabled = False
                CheckBox3.Enabled = False
            End If
           
        Else
            C1TrueDBGrid1.GroupedColumns.RemoveAt(C1TrueDBGrid1.GroupedColumns.IndexOf(Me.C1TrueDBGrid1.Columns("Yp_Name")))
            If CheckBox4.Checked = False Then
                C1TrueDBGrid1.Columns("Mz_Sl").Aggregate = C1.Win.C1TrueDBGrid.AggregateEnum.None
            End If

            Order_Str = Order_Str.Replace(",Yp_Name", "")
            Label7.Text = Label7.Text.Replace(",项目名称", "")
            Label7.Text = Label7.Text.Replace("项目名称", "无")
            If HisPara.PublicConfig.XqName.Contains("丰南") Then
                CheckBox2.Enabled = True
                CheckBox3.Enabled = True
            End If
          
        End If
        Label7.Text = Label7.Text.Replace("无,", "")
        Try
            If Order_Str <> "" Then
                My_View.Sort = Mid(Order_Str, 2)
            End If

        Catch ex As Exception

        End Try
    End Sub

    Private Sub Button2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button2.Click
        If C1TrueDBGrid1.RowCount = 0 Then Exit Sub
        Dim Stirpt As New StiReport
        Stirpt.Load(".\Rpt\科室收入统计表.mrt")
        Stirpt.ReportName = "科室收入统计"
        Stirpt.RegData(My_Dataset.Tables("收入统计"))
        Stirpt.RegData(My_Dataset.Tables("人数"))

        Stirpt.Compile()
        Stirpt("标题") = HisVar.HisVar.WsyName & Format(DateTimePicker1.Value, "yyyy-MM-dd HH:mm:ss") & "至" & Format(DateTimePicker2.Value, "yyyy-MM-dd HH:mm:ss") & "科室收入统计(" & Format(Now, "yyyy-MM-dd HH:mm:ss") & ")"

        '   Stirpt.Design()
        Stirpt.Show()
    End Sub

    Private Sub C1Combo1_RowChange(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1Combo1.RowChange
        If C1Combo1.WillChangeToValue = "" Then

        Else
            If C1Combo1.SelectedText = "门诊" Then
                C1Combo2.Enabled = False
            Else
                C1Combo2.Enabled = True
            End If

        End If
    End Sub


    Private Sub Button3_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button3.Click
        If C1TrueDBGrid1.RowCount = 0 Then Exit Sub
        Dim Stirpt As New StiReport

        Stirpt.Load(".\Rpt\患者类别统计表.mrt")
        Stirpt.ReportName = "患者类别统计"
        Stirpt.RegData(My_Dataset.Tables("收入统计"))
        Stirpt.Compile()
        Stirpt("标题") = HisVar.HisVar.WsyName & Format(DateTimePicker1.Value, "yyyy-MM-dd HH:mm:ss") & "至" & Format(DateTimePicker2.Value, "yyyy-MM-dd HH:mm:ss") & "患者类别统计(" & Format(Now, "yyyy-MM-dd HH:mm:ss") & ")"

        ' Stirpt.Design()
        Stirpt.Show()
    End Sub


    Private Sub Button4_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button4.Click
        Me.Cursor = Cursors.WaitCursor
        Dim V_Str As String = ""
        Dim V_Str1 As String = ""
        Dim V_Str2 As String = ""
        Dim Rs_Str As String = "" '人数汇总
        Dim Rs_Str1 As String = "" '人数汇总
        Dim Rs_Str2 As String = "" '人数汇总

        If C1Combo1.Text = "全部" Or C1Combo1.Text = "门诊" Then
            V_Str1 = "select '门诊' as V_Lb,Ks_Name,Zd_YyKs.Ks_Code,Ys_Name,Zd_YyYs.Ys_Code,'X'+Dl_Code as Dl_Code, Dl_Name ,Ry_Name,Mz_Date+Mz_Time as Mz_Date,Yp_Name,Mx_Gg,Mx_XsDw,Mz_Dj,Mz_Sl,Mz_YP_Sum.Mz_Money,Bxlb_Name from Zd_Bxlb,Mz_Sum,Mz_YP_Sum,Zd_Yyks,V_Ypkc,Zd_YyYs,Mz_Jz where Mz_Sum.Bxlb_Code=Zd_Bxlb.Bxlb_Code and Mz_Sum.Yy_Code='" & HisVar.HisVar.WsyCode & "' and Mz_Sum.Jz_Code=Mz_Jz.Jz_Code and Not exists (select Mz_Ph+Mz_Code from Mz_Ty_Sum where Mz_Ty_Sum.mz_ph=Mz_YP_Sum.Mz_Ph and Mz_Ty_Sum.Mz_code=Mz_YP_Sum.Mz_Code) And Mz_Sum.Mz_Code=Mz_YP_Sum.Mz_Code and Mz_Sum.Ks_Code=Zd_Yyks.Ks_Code and Zd_Yyys.Ys_Code=Mz_Sum.Ys_Code and Mz_Yp_Sum.Xx_Code=V_YpKc.Xx_Code and CONVERT(VARCHAR(10),Jz_Date,120) + ' ' +Jz_Time>='" & DateTimePicker1.Value.ToString("yyyy-MM-dd HH:mm:ss") & "' and CONVERT(VARCHAR(10),Jz_Date,120) + ' ' +Jz_Time<='" & DateTimePicker2.Value.ToString("yyyy-MM-dd HH:mm:ss") & "'  " & _
                 "  Union all select '门诊' as V_Lb,Ks_Name,Zd_YyKs.Ks_Code,Ys_Name,Zd_YyYs.Ys_Code,Case Lb_Name when '西药' then 'X01'+Zd_JkFl1.Lb_Code when '西药费' then 'X01'+Zd_JkFl1.Lb_Code When '中成药' then 'X02'+Zd_JkFl1.Lb_Code when '中草药' then 'X03'+Zd_JkFl1.Lb_Code  when '卫生材料' then 'X04'+Zd_JkFl1.Lb_Code else 'Y'+Zd_JkFl1.Lb_Code end as Dl_Code,Case Lb_Name when '西药费' then '西药' else Lb_Name end as Dl_Name,Ry_Name,Mz_Date+Mz_Time as Mz_Date,Xm_Name as Yp_Name,''Mx_Gg,Xm_Dw as Mx_XsDw,Mz_Dj,Mz_Sl,Mz_Xm_Sum.Mz_Money,Bxlb_Name from Zd_Bxlb, Mz_Sum,Mz_Xm_Sum,Zd_Yyks,Zd_ML_Xm3,Zd_JkFl1,Zd_JkFl2,Zd_YyYs,Mz_Jz where Mz_Sum.Bxlb_Code=Zd_Bxlb.Bxlb_Code and Mz_Sum.Yy_Code='" & HisVar.HisVar.WsyCode & "' and Mz_Sum.Jz_Code=Mz_Jz.Jz_Code and not exists (select Mz_Ph+Mz_Code from Mz_Ty_Sum where Mz_Ty_Sum.mz_ph=Mz_Xm_Sum.Mz_Ph and Mz_Ty_Sum.Mz_code=Mz_Xm_Sum.Mz_Code)  And Mz_Sum.Mz_Code=Mz_Xm_Sum.Mz_Code and Mz_Sum.Ks_Code=Zd_Yyks.Ks_Code and Zd_Yyys.Ys_Code=Mz_Sum.Ys_Code and Mz_Xm_Sum.Xm_Code=Zd_Ml_Xm3.Xm_Code and Zd_JkFl1.Lb_Code=Zd_JkFl2.Lb_Code and Zd_JkFl2.Mx_Code=Zd_Ml_Xm3.Xm_Code and CONVERT(VARCHAR(10),Jz_Date,120) + ' ' +Jz_Time>='" & DateTimePicker1.Value.ToString("yyyy-MM-dd HH:mm:ss") & "' and CONVERT(VARCHAR(10),Jz_Date,120) + ' ' +Jz_Time<='" & DateTimePicker2.Value.ToString("yyyy-MM-dd HH:mm:ss") & "'  "

            Rs_Str1 = "select  '门诊'+Mz_Sum.Mz_Code as Mz_Code,Ys_Code,Ks_Code from Mz_Yp_Sum,Mz_Sum,Mz_Jz where Not Exists (Select Mz_Code from Mz_Ty_Sum where Mz_Ty_Sum.Mz_Code=Mz_Yp_Sum.Mz_Code and Mz_Ty_Sum.Mz_Ph=Mz_Yp_Sum.Mz_Ph) and Mz_Sum.Mz_Code=Mz_Yp_Sum.Mz_Code and Mz_Sum.Jz_Code=Mz_Jz.Jz_Code  and CONVERT(VARCHAR(10),Jz_Date,120) + ' ' +Jz_Time>='" & DateTimePicker1.Value.ToString("yyyy-MM-dd HH:mm:ss") & "' and CONVERT(VARCHAR(10),Jz_Date,120) + ' ' +Jz_Time<='" & DateTimePicker2.Value.ToString("yyyy-MM-dd HH:mm:ss") & "' Union all select  '门诊'+Mz_Sum.Mz_Code as Mz_Code,Ys_Code,Ks_Code from Mz_Xm_Sum,Mz_Sum,Mz_Jz where Not Exists (Select Mz_Code from Mz_Ty_Sum where Mz_Ty_Sum.Mz_Code=Mz_Xm_Sum.Mz_Code and Mz_Ty_Sum.Mz_Ph=Mz_Xm_Sum.Mz_Ph) and Mz_Sum.Mz_Code=Mz_Xm_Sum.Mz_Code and Mz_Sum.Jz_Code=Mz_Jz.Jz_Code  and CONVERT(VARCHAR(10),Jz_Date,120) + ' ' +Jz_Time>='" & DateTimePicker1.Value.ToString("yyyy-MM-dd HH:mm:ss") & "' and CONVERT(VARCHAR(10),Jz_Date,120) + ' ' +Jz_Time<='" & DateTimePicker2.Value.ToString("yyyy-MM-dd HH:mm:ss") & "' "
        Else
            V_Str1 = ""
            Rs_Str1 = ""
        End If

        If C1Combo1.Text = "全部" Or C1Combo1.Text = "住院" Then
            If C1Combo1.Text = "全部" Then
                V_Str1 = V_Str1 & " UNION ALL "
                Rs_Str1 = Rs_Str1 & "  UNION ALL  "
            End If

            If C1Combo2.Text = "按日清统计" Then
                V_Str2 = "select '住院' as V_Lb,Ks_Name,Zd_YyKs.Ks_Code,Ys_Name,Zd_YyYs.Ys_Code,'X'+Dl_Code as Dl_Code, Dl_Name ,Ry_Name,Cf_Date+Cf_Time as Mz_Date,Yp_Name,Mx_Gg,Mx_XsDw,Cf_Dj as Mz_Dj,Cf_Sl as Mz_Sl,Bl_CfYP.Cf_Money as Mz_Money ,Bxlb_Name from Zd_Bxlb, Bl,Bl_Cf,Bl_CfYp,Zd_Yyks,V_Ypkc,Zd_YyYs,Bl_Jz where Zd_Bxlb.Bxlb_Code=Bl.Bxlb_Code and Bl.Bl_Code=Bl_Cf.Bl_Code and Bl.Yy_Code='" & HisVar.HisVar.WsyCode & "' and Bl_Cf.Jz_Code=Bl_Jz.Jz_Code  And Bl_Cf.Cf_Code=Bl_CfYp.Cf_Code and Bl_Cf.Ks_Code=Zd_Yyks.Ks_Code and Zd_Yyys.Ys_Code=Bl_Cf.Ys_Code and Bl_CfYp.Xx_Code=V_YpKc.Xx_Code and CONVERT(VARCHAR(10),Jz_Date,120) + ' ' +Jz_Time>='" & DateTimePicker1.Value.ToString("yyyy-MM-dd HH:mm:ss") & "' and CONVERT(VARCHAR(10),Jz_Date,120) + ' ' +Jz_Time<='" & DateTimePicker2.Value.ToString("yyyy-MM-dd HH:mm:ss") & "'  " & _
                "  Union all select '住院' as V_Lb,Ks_Name,Zd_YyKs.Ks_Code,Ys_Name,Zd_YyYs.Ys_Code,Case Lb_Name when '西药' then 'X01'+Zd_JkFl1.Lb_Code when '西药费' then 'X01'+Zd_JkFl1.Lb_Code When '中成药' then 'X02'+Zd_JkFl1.Lb_Code when '中草药' then 'X03'+Zd_JkFl1.Lb_Code  when '卫生材料' then 'X04'+Zd_JkFl1.Lb_Code else 'Y'+Zd_JkFl1.Lb_Code end as Dl_Code,Case Lb_Name when '西药费' then '西药' else Lb_Name end as Dl_Name,Ry_Name,Cf_Date+Cf_Time as Mz_Date,Xm_Name as Yp_Name,''Mx_Gg,Xm_Dw as Mx_XsDw,Cf_Dj as Mz_Dj,Cf_Sl as Mz_Sl,Bl_CfXm.Cf_Money as Mz_Money ,Bxlb_Name from Zd_Bxlb, Bl,Bl_Cf,Bl_CfXm,Zd_Yyks,Zd_ML_Xm3,Zd_JkFl1,Zd_JkFl2,Zd_YyYs,Bl_Jz where Zd_Bxlb.Bxlb_Code=Bl.Bxlb_Code and Bl.Bl_Code=Bl_Cf.Bl_Code and Bl.Yy_Code='" & HisVar.HisVar.WsyCode & "' and Bl_Cf.Jz_Code=Bl_Jz.Jz_Code  And Bl_Cf.Cf_Code=Bl_CfXm.Cf_Code and Bl_Cf.Ks_Code=Zd_Yyks.Ks_Code and Zd_Yyys.Ys_Code=Bl_Cf.Ys_Code and Bl_CfXm.Xm_Code=Zd_Ml_Xm3.Xm_Code and Zd_JkFl1.Lb_Code=Zd_JkFl2.Lb_Code and Zd_JkFl2.Mx_Code=Zd_Ml_Xm3.Xm_Code and CONVERT(VARCHAR(10),Jz_Date,120) + ' ' +Jz_Time>='" & DateTimePicker1.Value.ToString("yyyy-MM-dd HH:mm:ss") & "' and CONVERT(VARCHAR(10),Jz_Date,120) + ' ' +Jz_Time<='" & DateTimePicker2.Value.ToString("yyyy-MM-dd HH:mm:ss") & "'  "

                Rs_Str2 = " Select '住院'+Bl_Code as Mz_Code,Ys_Code,Ks_Code from Bl_Cf,Bl_Jz where Bl_Cf.Jz_Code=Bl_Jz.Jz_Code and CONVERT(VARCHAR(10),Jz_Date,120) + ' ' +Jz_Time>='" & DateTimePicker1.Value.ToString("yyyy-MM-dd HH:mm:ss") & "' and CONVERT(VARCHAR(10),Jz_Date,120) + ' ' +Jz_Time<='" & DateTimePicker2.Value.ToString("yyyy-MM-dd HH:mm:ss") & "' "

            ElseIf C1Combo2.Text = "按出院统计" Then
                V_Str2 = "select '住院' as V_Lb,Ks_Name,Zd_YyKs.Ks_Code,Ys_Name,Zd_YyYs.Ys_Code,'X'+Dl_Code as Dl_Code, Dl_Name ,Ry_Name,Cf_Date+Cf_Time as Mz_Date,Yp_Name,Mx_Gg,Mx_XsDw,Cf_Dj as Mz_Dj,Cf_Sl as Mz_Sl,Bl_CfYP.Cf_Money as Mz_Money ,Bxlb_Name from Zd_Bxlb, Bl,Bl_Cf,Bl_CfYp,Zd_Yyks,V_Ypkc,Zd_YyYs,Bl_Jz where Zd_Bxlb.Bxlb_Code=Bl.Bxlb_Code and Bl.Bl_Code=Bl_Cf.Bl_Code and Bl.Yy_Code='" & HisVar.HisVar.WsyCode & "' and Bl.Jz_Code=Bl_Jz.Jz_Code  And Bl_Cf.Cf_Code=Bl_CfYp.Cf_Code and Bl_Cf.Ks_Code=Zd_Yyks.Ks_Code and Zd_Yyys.Ys_Code=Bl_Cf.Ys_Code and Bl_CfYp.Xx_Code=V_YpKc.Xx_Code and CONVERT(VARCHAR(10),Jz_Date,120) + ' ' +Jz_Time>='" & DateTimePicker1.Value.ToString("yyyy-MM-dd HH:mm:ss") & "' and CONVERT(VARCHAR(10),Jz_Date,120) + ' ' +Jz_Time<='" & DateTimePicker2.Value.ToString("yyyy-MM-dd HH:mm:ss") & "'  " & _
               "  Union all select '住院' as V_Lb,Ks_Name,Zd_YyKs.Ks_Code,Ys_Name,Zd_YyYs.Ys_Code, Case Lb_Name when '西药' then 'X01'+Zd_JkFl1.Lb_Code when '西药费' then 'X01'+Zd_JkFl1.Lb_Code When '中成药' then 'X02'+Zd_JkFl1.Lb_Code when '中草药' then 'X03'+Zd_JkFl1.Lb_Code  when '卫生材料' then 'X04'+Zd_JkFl1.Lb_Code else 'Y'+Zd_JkFl1.Lb_Code end as Dl_Code,Case Lb_Name when '西药费' then '西药' else Lb_Name end as Dl_Name,Ry_Name,Cf_Date+Cf_Time as Mz_Date,Xm_Name as Yp_Name,''Mx_Gg,Xm_Dw as Mx_XsDw,Cf_Dj as Mz_Dj,Cf_Sl as Mz_Sl,Bl_CfXm.Cf_Money as Mz_Money ,Bxlb_Name from Zd_Bxlb, Bl,Bl_Cf,Bl_CfXm,Zd_Yyks,Zd_ML_Xm3,Zd_JkFl1,Zd_JkFl2,Zd_YyYs,Bl_Jz where Zd_Bxlb.Bxlb_Code=Bl.Bxlb_Code and Bl.Bl_Code=Bl_Cf.Bl_Code and Bl.Yy_Code='" & HisVar.HisVar.WsyCode & "' and Bl.Jz_Code=Bl_Jz.Jz_Code  And Bl_Cf.Cf_Code=Bl_CfXm.Cf_Code and Bl_Cf.Ks_Code=Zd_Yyks.Ks_Code and Zd_Yyys.Ys_Code=Bl_Cf.Ys_Code and Bl_CfXm.Xm_Code=Zd_Ml_Xm3.Xm_Code and Zd_JkFl1.Lb_Code=Zd_JkFl2.Lb_Code and Zd_JkFl2.Mx_Code=Zd_Ml_Xm3.Xm_Code and CONVERT(VARCHAR(10),Jz_Date,120) + ' ' +Jz_Time>='" & DateTimePicker1.Value.ToString("yyyy-MM-dd HH:mm:ss") & "' and CONVERT(VARCHAR(10),Jz_Date,120) + ' ' +Jz_Time<='" & DateTimePicker2.Value.ToString("yyyy-MM-dd HH:mm:ss") & "'  "

                Rs_Str2 = " select '住院'+ Bl_Code as Mz_Code,Ys_Code,Ks_Code from Bl,Bl_Jz where Bl.Jz_Code=Bl_Jz.Jz_Code and CONVERT(VARCHAR(10),Jz_Date,120) + ' ' +Jz_Time>='" & DateTimePicker1.Value.ToString("yyyy-MM-dd HH:mm:ss") & "' and CONVERT(VARCHAR(10),Jz_Date,120) + ' ' +Jz_Time<='" & DateTimePicker2.Value.ToString("yyyy-MM-dd HH:mm:ss") & "' "
            End If
        Else
            V_Str2 = " "
            Rs_Str2 = ""
        End If

        V_Str = V_Str1 & V_Str2 & "Order by Dl_Code"
        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, V_Str, "收入统计", True)

        Rs_Str = "Select Count(Distinct Mz_Code) as Rs,Ks_Code,Ys_Code from ( " & Rs_Str1 & Rs_Str2 & ") Rs_Tab Group by Ks_Code,Ys_Code"
        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, Rs_Str, "人数", True)

        With Me.C1TrueDBGrid1
            My_Cm = CType(BindingContext(My_Dataset, "收入统计"), CurrencyManager)
            .SetDataBinding(My_Dataset, "收入统计", True)
            My_View = My_Cm.List

            Try
                If Order_Str <> "" Then
                    My_View.Sort = Mid(Order_Str, 2)
                End If
            Catch ex As Exception

            End Try


            Dim Sum_Money As Double
            Sum_Money = IIf(My_View.Table.Compute("Sum(Mz_Money)", "") Is DBNull.Value, 0, My_View.Table.Compute("Sum(Mz_Money)", ""))

            .ColumnFooters = True
            .Columns(12).FooterText = Format(Sum_Money, "###0.00")


        End With
        Me.Cursor = Cursors.Default
    End Sub

   
    Private Sub TextBox1_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles TextBox1.TextChanged
        My_View.RowFilter = " Ry_Name like '%" & Trim(TextBox1.Text) & "%' or Yp_Name like '%" & Trim(TextBox1.Text) & "%' "
    End Sub
End Class