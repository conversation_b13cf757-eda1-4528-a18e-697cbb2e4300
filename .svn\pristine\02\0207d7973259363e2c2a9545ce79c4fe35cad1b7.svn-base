﻿/**  版本信息模板在安装目录下，可自行修改。
* D_LIS_TestXm.cs
*
* 功 能： N/A
* 类 名： D_LIS_TestXm
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/10/17 星期一 下午 1:38:53   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;

namespace DAL
{
    /// <summary>
    /// 数据访问类:D_LIS_TestXm
    /// </summary>
    public partial class D_LIS_TestXm
    {
        public D_LIS_TestXm()
        { }
        #region  BasicMethod

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(string TestXm_Code)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from LIS_TestXm");
            strSql.Append(" where TestXm_Code=@TestXm_Code ");
            SqlParameter[] parameters = {
					new SqlParameter("@TestXm_Code", SqlDbType.Char,5)			};
            parameters[0].Value = TestXm_Code;

            return HisVar.HisVar.Sqldal.Exists(strSql.ToString(), parameters);
        }

        public string MaxCode()
        {
            string max = (string)(HisVar.HisVar.Sqldal.F_MaxCode("select max(TestXm_Code) from LIS_TestXm", 5));
            return max;
        }
        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ModelOld.M_LIS_TestXm model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into LIS_TestXm(");
            strSql.Append("TestXm_Code,TestXm_Name,TestXm_Jc,Dev_Code,TestSample,ChannelCode,Accuracy,AddNum,ConversionNum,TestXmRpt,Memo)");
            strSql.Append(" values (");
            strSql.Append("@TestXm_Code,@TestXm_Name,@TestXm_Jc,@Dev_Code,@TestSample,@ChannelCode,@Accuracy,@AddNum,@ConversionNum,@TestXmRpt,@Memo)");
            SqlParameter[] parameters = {
					new SqlParameter("@TestXm_Code", SqlDbType.Char,5),
					new SqlParameter("@TestXm_Name", SqlDbType.VarChar,50),
					new SqlParameter("@TestXm_Jc", SqlDbType.VarChar,50),
					new SqlParameter("@Dev_Code", SqlDbType.Char,10),
					new SqlParameter("@TestSample", SqlDbType.VarChar,50),
					new SqlParameter("@ChannelCode", SqlDbType.VarChar,50),
					new SqlParameter("@Accuracy", SqlDbType.Int,4),
					new SqlParameter("@AddNum", SqlDbType.Decimal,9),
					new SqlParameter("@ConversionNum", SqlDbType.Decimal,9),
					new SqlParameter("@TestXmRpt", SqlDbType.Image),
					new SqlParameter("@Memo", SqlDbType.VarChar,200)};
            parameters[0].Value = model.TestXm_Code;
            parameters[1].Value = model.TestXm_Name;
            parameters[2].Value = model.TestXm_Jc;
            parameters[3].Value = model.Dev_Code;
            parameters[4].Value = model.TestSample;
            parameters[5].Value = model.ChannelCode;
            parameters[6].Value = model.Accuracy;
            parameters[7].Value = model.AddNum;
            parameters[8].Value = model.ConversionNum;
            parameters[9].Value = Common.Tools.IsValueNull(model.TestXmRpt);
            parameters[10].Value = model.Memo;

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ModelOld.M_LIS_TestXm model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update LIS_TestXm set ");
            strSql.Append("TestXm_Name=@TestXm_Name,");
            strSql.Append("TestXm_Jc=@TestXm_Jc,");
            strSql.Append("Dev_Code=@Dev_Code,");
            strSql.Append("TestSample=@TestSample,");
            strSql.Append("ChannelCode=@ChannelCode,");
            strSql.Append("Accuracy=@Accuracy,");
            strSql.Append("AddNum=@AddNum,");
            strSql.Append("ConversionNum=@ConversionNum,");
            strSql.Append("TestXmRpt=@TestXmRpt,");
            strSql.Append("Memo=@Memo");
            strSql.Append(" where TestXm_Code=@TestXm_Code ");
            SqlParameter[] parameters = {
					new SqlParameter("@TestXm_Name", SqlDbType.VarChar,50),
					new SqlParameter("@TestXm_Jc", SqlDbType.VarChar,50),
					new SqlParameter("@Dev_Code", SqlDbType.Char,10),
					new SqlParameter("@TestSample", SqlDbType.VarChar,50),
					new SqlParameter("@ChannelCode", SqlDbType.VarChar,50),
					new SqlParameter("@Accuracy", SqlDbType.Int,4),
					new SqlParameter("@AddNum", SqlDbType.Decimal,9),
					new SqlParameter("@ConversionNum", SqlDbType.Decimal,9),
					new SqlParameter("@TestXmRpt", SqlDbType.Image),
					new SqlParameter("@Memo", SqlDbType.VarChar,200),
					new SqlParameter("@TestXm_Code", SqlDbType.Char,5)};
            parameters[0].Value = model.TestXm_Name;
            parameters[1].Value = model.TestXm_Jc;
            parameters[2].Value = model.Dev_Code;
            parameters[3].Value = model.TestSample;
            parameters[4].Value = model.ChannelCode;
            parameters[5].Value = model.Accuracy;
            parameters[6].Value = model.AddNum;
            parameters[7].Value = model.ConversionNum;
            parameters[8].Value = Common.Tools.IsValueNull(model.TestXmRpt);
            parameters[9].Value = model.Memo;
            parameters[10].Value = model.TestXm_Code;

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(string TestXm_Code)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from LIS_TestXm ");
            strSql.Append(" where TestXm_Code=@TestXm_Code ");
            SqlParameter[] parameters = {
					new SqlParameter("@TestXm_Code", SqlDbType.Char,5)			};
            parameters[0].Value = TestXm_Code;

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string TestXm_Codelist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from LIS_TestXm ");
            strSql.Append(" where TestXm_Code in (" + TestXm_Codelist + ")  ");
            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ModelOld.M_LIS_TestXm GetModel(string TestXm_Code)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 TestXm_Code,TestXm_Name,TestXm_Jc,Dev_Code,TestSample,ChannelCode,Accuracy,AddNum,ConversionNum,TestXmRpt,Memo from LIS_TestXm ");
            strSql.Append(" where TestXm_Code=@TestXm_Code ");
            SqlParameter[] parameters = {
					new SqlParameter("@TestXm_Code", SqlDbType.Char,5)			};
            parameters[0].Value = TestXm_Code;

            ModelOld.M_LIS_TestXm model = new ModelOld.M_LIS_TestXm();
            DataSet ds = HisVar.HisVar.Sqldal.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ModelOld.M_LIS_TestXm DataRowToModel(DataRow row)
        {
            ModelOld.M_LIS_TestXm model = new ModelOld.M_LIS_TestXm();
            if (row != null)
            {
                if (row["TestXm_Code"] != null)
                {
                    model.TestXm_Code = row["TestXm_Code"].ToString();
                }
                if (row["TestXm_Name"] != null)
                {
                    model.TestXm_Name = row["TestXm_Name"].ToString();
                }
                if (row["TestXm_Jc"] != null)
                {
                    model.TestXm_Jc = row["TestXm_Jc"].ToString();
                }
                if (row["Dev_Code"] != null)
                {
                    model.Dev_Code = row["Dev_Code"].ToString();
                }
                if (row["TestSample"] != null)
                {
                    model.TestSample = row["TestSample"].ToString();
                }
                if (row["ChannelCode"] != null)
                {
                    model.ChannelCode = row["ChannelCode"].ToString();
                }
                if (row["Accuracy"] != null && row["Accuracy"].ToString() != "")
                {
                    model.Accuracy = int.Parse(row["Accuracy"].ToString());
                }
                if (row["AddNum"] != null && row["AddNum"].ToString() != "")
                {
                    model.AddNum = decimal.Parse(row["AddNum"].ToString());
                }
                if (row["ConversionNum"] != null && row["ConversionNum"].ToString() != "")
                {
                    model.ConversionNum = decimal.Parse(row["ConversionNum"].ToString());
                }
                if (row["TestXmRpt"] != null && row["TestXmRpt"].ToString() != "")
                {
                    model.TestXmRpt = (byte[])row["TestXmRpt"];
                }
                if (row["Memo"] != null)
                {
                    model.Memo = row["Memo"].ToString();
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(" SELECT TestXm_Code, TestXm_Name, TestXm_Jc, lis_testxm.Dev_Code,Dev_Name, TestSample,");
            strSql.Append(" ChannelCode, Accuracy, AddNum, ConversionNum, TestXmRpt,lis_testxm. Memo");
            strSql.Append(" FROM lis_testxm JOIN LIS_DictDev ON dbo.LIS_TestXm.Dev_Code = dbo.LIS_DictDev.Dev_Code");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" TestXm_Code,TestXm_Name,TestXm_Jc,Dev_Code,TestSample,ChannelCode,Accuracy,AddNum,ConversionNum,TestXmRpt,Memo ");
            strSql.Append(" FROM LIS_TestXm ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM LIS_TestXm ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.TestXm_Code desc");
            }
            strSql.Append(")AS Row, T.*  from LIS_TestXm T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

        /*
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetList(int PageSize,int PageIndex,string strWhere)
        {
            SqlParameter[] parameters = {
                    new SqlParameter("@tblName", SqlDbType.VarChar, 255),
                    new SqlParameter("@fldName", SqlDbType.VarChar, 255),
                    new SqlParameter("@PageSize", SqlDbType.Int),
                    new SqlParameter("@PageIndex", SqlDbType.Int),
                    new SqlParameter("@IsReCount", SqlDbType.Bit),
                    new SqlParameter("@OrderType", SqlDbType.Bit),
                    new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
                    };
            parameters[0].Value = "LIS_TestXm";
            parameters[1].Value = "TestXm_Code";
            parameters[2].Value = PageSize;
            parameters[3].Value = PageIndex;
            parameters[4].Value = 0;
            parameters[5].Value = 0;
            parameters[6].Value = strWhere;	
            return HisVar.HisVar.Sqldal.RunProcedure("UP_GetRecordByPage",parameters,"ds");
        }*/

        #endregion  BasicMethod
        #region  ExtensionMethod

        #endregion  ExtensionMethod
    }
}

