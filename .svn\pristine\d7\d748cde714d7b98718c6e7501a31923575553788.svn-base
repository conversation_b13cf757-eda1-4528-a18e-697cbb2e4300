﻿/**  版本信息模板在安装目录下，可自行修改。
* M_Materials_Manufacturer_Dict.cs
*
* 功 能： N/A
* 类 名： M_Materials_Manufacturer_Dict
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016-11-26 11:32:26   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace ModelOld
{
	/// <summary>
	/// 物资生产厂家
	/// </summary>
	[Serializable]
	public partial class M_Materials_Manufacturer_Dict
	{
		public M_Materials_Manufacturer_Dict()
		{}
		#region Model
		private string _matemanu_code;
		private string _matemanu_name;
		private string _matemanu_py;
		private string _matemanu_wb;
		private string _matemanu_memo;
		private int? _serial_no;
		private bool _isuse;
		/// <summary>
		/// 生产厂家编码
		/// </summary>
		public string MateManu_Code
		{
			set{ _matemanu_code=value;}
			get{return _matemanu_code;}
		}
		/// <summary>
		/// 名称
		/// </summary>
		public string MateManu_Name
		{
			set{ _matemanu_name=value;}
			get{return _matemanu_name;}
		}
		/// <summary>
		/// 拼音
		/// </summary>
		public string MateManu_Py
		{
			set{ _matemanu_py=value;}
			get{return _matemanu_py;}
		}
		/// <summary>
		/// 五笔
		/// </summary>
		public string MateManu_Wb
		{
			set{ _matemanu_wb=value;}
			get{return _matemanu_wb;}
		}
		/// <summary>
		/// 备注
		/// </summary>
		public string MateManu_Memo
		{
			set{ _matemanu_memo=value;}
			get{return _matemanu_memo;}
		}
		/// <summary>
		/// 排列顺序
		/// </summary>
		public int? Serial_No
		{
			set{ _serial_no=value;}
			get{return _serial_no;}
		}
		/// <summary>
		/// 真为启用，假为停用，默认值为真
		/// </summary>
		public bool IsUse
		{
			set{ _isuse=value;}
			get{return _isuse;}
		}
		#endregion Model

	}
}

