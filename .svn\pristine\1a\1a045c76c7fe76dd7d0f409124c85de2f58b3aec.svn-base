﻿Imports System.Data.SqlClient
Imports Stimulsoft.Report
Imports Stimulsoft.Report.Components

Public Class Yk_Tj_Cx1
    Dim My_Cm As CurrencyManager             '同步指针
    Dim My_View As New DataView
    Public My_Adapter As New SqlDataAdapter
    Dim My_Dataset As New DataSet

    Private Sub Yk_Tj_Cx2_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        BaseFunc.BaseFunc.Init_Datetimepicker(DateTimePicker1)
        BaseFunc.BaseFunc.Init_Datetimepicker(DateTimePicker2)
        DateTimePicker1.Value = Format(Now, "yyyy-MM-01 00:00:00")
        DateTimePicker2.Value = Format(Now, "yyyy-MM-dd 23:59:59")
        C1TextBox1.Text = ""
        Dim My_Grid As New BaseClass.C_Grid(Me.C1TrueDBGrid1)
        With My_Grid
            .Init_Grid()
            .Init_Column("调价编码", "Tj_Code", 0, "中", "")
            .Init_Column("调价日期", "Tj_Date", 130, "中", "yyyy-MM-dd HH:mm:ss")
            .Init_Column("签字领导", "Ld_Name", 55, "中", "")
            .Init_Column("药品名称", "Yp_Name", 150, "左", "")
            .Init_Column("规格", "Mx_Gg", 95, "左", "")
            .Init_Column("生产厂家", "Mx_Cd", 100, "左", "")
            .Init_Column("比例", "Mx_Cfbl", 40, "右", "0.0###")
            .Init_Column("国药准字", "Mx_Gyzz", 65, "左", "")
            .Init_Column("药品批号", "Yp_Ph", 75, "左", "")
            .Init_Column("有效期", "Yp_Yxq", 75, "中", "yyyy-MM-dd")
            .Init_Column("原售价", "Tj_Lsj_Old", 50, "右", "######0.00##")
            .Init_Column("新售价", "Tj_Lsj_New", 50, "右", "######0.00##")
            .Init_Column("售价结果", "Tj_Lsj_Ty1", 55, "中", "")
            .Init_Column("原批发价", "Tj_Pfj_Old", 55, "右", "######0.00##")
            .Init_Column("新批发价", "Tj_Pfj_New", 55, "右", "######0.00##")
            .Init_Column("批发价结果", "Tj_Pfj_Ty1", 70, "中", "")

        End With
        C1TrueDBGrid1.Splits(0).HScrollBar.Style = C1.Win.C1TrueDBGrid.ScrollBarStyleEnum.Automatic
        C1TrueDBGrid1.RecordSelectors = False
        Dim My_Combo As New BaseClass.C_Combo1(Me.C1Combo1)
        With My_Combo
            .Init_TDBCombo()
            .AddItem("药品简称")
            .AddItem("药品名称")
            .AddItem("领导名称")
            .AddItem("调价编码")
        End With
        C1Combo1.Width = 80
        C1Combo1.DropDownWidth = 80

        My_Adapter = New SqlDataAdapter("Select Yf_Name,Yf_Code from Zd_YyYf where Yy_Code='" & HisVar.HisVar.WsyCode & "' and Yf_Use = 1 Order by Yf_Code", My_Cn)
        My_Adapter.Fill(My_Dataset, "药房")
        My_Dataset.Tables("药房").PrimaryKey = New DataColumn() {My_Dataset.Tables("药房").Columns("Yf_Code")}

        Call Combo2_Init()
        C1TextBox1.Enabled = False
    End Sub

    Private Sub Combo2_Init()
        Dim Yf_Combo As New BaseClass.C_Combo2(C1Combo2, Me.My_Dataset.Tables("药房").DefaultView, "Yf_Name", "Yf_Code", 90)
        With Yf_Combo
            .Init_TDBCombo()
            .Init_Colum("Yf_Name", "药房", 84, "左")
            .Init_Colum("Yf_Code", "编码", 40, "中")
            .MaxDropDownItems(15)
            .SelectedIndex(-1)
        End With
        With Me.C1Combo2
            .Splits(0).DisplayColumns("Yf_Code").Visible = False
            .DropDownWidth = 90
            .Height = 22
            .BorderStyle = BorderStyle.Fixed3D
            .ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        End With
    End Sub
    Private Sub C1TextBox1_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1TextBox1.TextChanged

        My_View = My_Cm.List

        Dim V_Sort As String = ""
        Dim V_RowFilter As String = ""

        If Trim(Me.C1TextBox1.Text & "") = "" Then
            V_Sort = "Tj_Code Asc"
            V_RowFilter = ""
        Else
            Select Case Me.C1Combo1.Text
                Case "药品简称"
                    V_Sort = "Yp_Jc Asc"
                    V_RowFilter = "Yp_Jc like '*" & Trim(C1TextBox1.Text) & "*'"
                Case "药品名称"
                    V_Sort = "Yp_Name Asc"
                    V_RowFilter = "Yp_Name Like '*" & Trim(C1TextBox1.Text) & "*'"
                Case "领导名称"
                    V_Sort = "Ld_Name Asc"
                    V_RowFilter = "Ld_Name Like '*" & Trim(C1TextBox1.Text) & "*'"
                Case "调价编码"
                    V_Sort = "Tj_Code"
                    V_RowFilter = "Tj_Code like '" & Trim(C1TextBox1.Text) & "*'"
            End Select
        End If

        My_View.Sort = V_Sort
        My_View.RowFilter = V_RowFilter
        'T_Label.Text = "∑=" + C1TrueDBGrid1.Splits(0).Rows.Count.ToString
    End Sub


    Private Sub C1Command1_Click(ByVal sender As System.Object, ByVal e As C1.Win.C1Command.ClickEventArgs) Handles C1Command1.Click
        Dim Str As String = "select Yk_Tj2.Tj_Code,Tj_Date,Ld_Name,Yp_Name,Yp_Jc,Mx_Gg,Mx_Cd,Mx_CgDw,Mx_XsDw,Mx_Cfbl,Mx_Gyzz,Yp_Ph,Yp_Yxq,Tj_Lsj_Old,Tj_Lsj_New,Tj_Lsj_Old * Mx_Cfbl as Tj_Yk_Lsj_Old,Tj_Lsj_New * Mx_Cfbl as Tj_Yk_Lsj_New,Tj_Pfj_Old,Tj_Pfj_New,(Tj_Pfj_New-Tj_Pfj_Old)*Tj_Yk_Sl as Tj_Yk_PfCe,(Tj_Lsj_New * Mx_Cfbl-Tj_Lsj_Old * Mx_Cfbl)*Tj_Yk_Sl as Tj_Yk_LsCe," & _
                         "case Tj_Lsj_Ty when 1 then '同意' else '否决' end Tj_Lsj_Ty1,case Tj_Pfj_Ty when 1 then '同意' else '否决' end Tj_Pfj_Ty1,Tj_Yk_Sl,Tj_Yf_Sl1,Tj_Yf_Sl2,Tj_Yf_Sl3,Tj_Yf_Sl4,Tj_Yf_Sl5,Tj_Yf_Sl6,Tj_Yf_Sl7,Tj_Yf_Sl8,Tj_Yf_Sl9,(Tj_Lsj_New-Tj_Lsj_Old)*Tj_Yf_Sl1 as Tj_Yf_LsCe1,(Tj_Lsj_New-Tj_Lsj_Old)*Tj_Yf_Sl2 as Tj_Yf_LsCe2,(Tj_Lsj_New-Tj_Lsj_Old)*Tj_Yf_Sl3 as Tj_Yf_LsCe3,(Tj_Lsj_New-Tj_Lsj_Old)*Tj_Yf_Sl4 as Tj_Yf_LsCe4,(Tj_Lsj_New-Tj_Lsj_Old)*Tj_Yf_Sl5 as Tj_Yf_LsCe5,(Tj_Lsj_New-Tj_Lsj_Old)*Tj_Yf_Sl6 as Tj_Yf_LsCe6,(Tj_Lsj_New-Tj_Lsj_Old)*Tj_Yf_Sl7 as Tj_Yf_LsCe7,(Tj_Lsj_New-Tj_Lsj_Old)*Tj_Yf_Sl8 as Tj_Yf_LsCe8,(Tj_Lsj_New-Tj_Lsj_Old)*Tj_Yf_Sl9 as Tj_Yf_LsCe9,(Tj_Pfj_New-Tj_Pfj_Old)*Tj_Yf_Sl1/Mx_Cfbl as Tj_Yf_PfCe1,(Tj_Pfj_New-Tj_Pfj_Old)*Tj_Yf_Sl2/Mx_Cfbl as Tj_Yf_PfCe2,(Tj_Pfj_New-Tj_Pfj_Old)*Tj_Yf_Sl3/Mx_Cfbl as Tj_Yf_PfCe3,(Tj_Pfj_New-Tj_Pfj_Old)*Tj_Yf_Sl4/Mx_Cfbl as Tj_Yf_PfCe4,(Tj_Pfj_New-Tj_Pfj_Old)*Tj_Yf_Sl5/Mx_Cfbl as Tj_Yf_PfCe5,(Tj_Pfj_New-Tj_Pfj_Old)*Tj_Yf_Sl6/Mx_Cfbl as Tj_Yf_PfCe6,(Tj_Pfj_New-Tj_Pfj_Old)*Tj_Yf_Sl7/Mx_Cfbl as Tj_Yf_PfCe7,(Tj_Pfj_New-Tj_Pfj_Old)*Tj_Yf_Sl8/Mx_Cfbl as Tj_Yf_PfCe8,(Tj_Pfj_New-Tj_Pfj_Old)*Tj_Yf_Sl9/Mx_Cfbl as Tj_Yf_PfCe9 from Yk_Tj1,Yk_Tj2,V_Ypkc,Zd_YyLd where Yk_Tj1.Ld_Code=Zd_YyLd.Ld_Code And Yk_Tj1.Tj_Code=Yk_Tj2.Tj_Code and Yk_Tj2.Xx_Code=V_Ypkc.Xx_Code and Tj_Date between '" & DateTimePicker1.Value & "'and '" & DateTimePicker2.Value & "' And Yk_Tj1.Yy_Code='" & HisVar.HisVar.WsyCode & "'"



        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, Str, "调价信息", True)
        If My_Dataset.Tables("调价信息").Rows.Count = 0 Then
            MsgBox("没有符合条件的记录", MsgBoxStyle.Information, "提示")
            Exit Sub
        End If

        With C1TrueDBGrid1
            My_Cm = CType(BindingContext(My_Dataset, "调价信息"), CurrencyManager)
            .SetDataBinding(My_Dataset, "调价信息", True)

            My_View = My_Cm.List
            My_View.Sort = "Tj_Code Asc"
        End With
        C1TextBox1.Enabled = True
    End Sub

    Private Sub C1Command2_Click(ByVal sender As System.Object, ByVal e As C1.Win.C1Command.ClickEventArgs) Handles Lr2.Click, Lr3.Click, Lr4.Click

        If C1TrueDBGrid1.RowCount = 0 Then
            Exit Sub
        End If



        Select Case sender.text
            Case "药库批发价打印"

                My_View.RowFilter = "Tj_Pfj_Ty1='同意' and Tj_Yk_Sl>0"


                Dim Stirpt As New StiReport
                Stirpt.Load(".\Rpt\药库调价查询.mrt")
                Stirpt.ReportName = "药库调价查询批发价"

                Stirpt.RegData(My_View)

                Stirpt.Compile()
                Stirpt("标题") = HisVar.HisVar.WsyName & "药库批发价调价打印"
                Stirpt("打印时间") = "打印日期:" & Format(Now, "yyyy-MM-dd HH:mm:ss")
                'Stirpt.Design()
                Stirpt.Show()

            Case "药库售价打印"

                My_View.RowFilter = "Tj_Lsj_Ty1='同意'and Tj_Yk_Sl>0"


                Dim Stirpt As New StiReport
                Stirpt.Load(".\Rpt\药库调价查询.mrt")
                Stirpt.ReportName = "药库调价查询药库销售价"
                Stirpt.Dictionary.Databases.Clear()
                Stirpt.RegData(My_View)
                Stirpt.Dictionary.Synchronize()
                TryCast(Stirpt.Pages(0).GetComponents.Item("Text17"), StiText).Text = "原销售价"
                TryCast(Stirpt.Pages(0).GetComponents.Item("Text19"), StiText).Text = "新销售价"
                TryCast(Stirpt.Pages(0).GetComponents.Item("Text28"), StiText).Text = "销售差额"
                TryCast(Stirpt.Pages(0).GetComponents.Item("Text12"), StiText).Text = "{view调价信息.Tj_Yk_Sl}"
                TryCast(Stirpt.Pages(0).GetComponents.Item("Text14"), StiText).Text = "{view调价信息.Mx_CgDw}"
                TryCast(Stirpt.Pages(0).GetComponents.Item("Text16"), StiText).Text = "{view调价信息.Tj_Yk_Lsj_Old}"
                TryCast(Stirpt.Pages(0).GetComponents.Item("Text18"), StiText).Text = "{view调价信息.Tj_Yk_Lsj_New}"
                TryCast(Stirpt.Pages(0).GetComponents.Item("Text29"), StiText).Text = "{view调价信息.Tj_Yk_LsCe}"
                TryCast(Stirpt.Pages(0).GetComponents.Item("Text26"), StiText).Text = "{Sum(GroupHeaderBand1,view调价信息.Tj_Yk_LsCe)}"
                TryCast(Stirpt.Pages(0).GetComponents.Item("Text23"), StiText).Text = "{Sum(DataBand1,view调价信息.Tj_Yk_LsCe)}"
                Stirpt.Compile()
                Stirpt("标题") = HisVar.HisVar.WsyName & "药库销售价调价打印"
                Stirpt("打印时间") = "打印日期:" & Format(Now, "yyyy-MM-dd HH:mm:ss")
                'Stirpt.Design()
                Stirpt.Show()

            Case "药房售价打印"

                If C1Combo2.SelectedValue = "" Then
                    Beep()
                    MsgBox("请先选择打印药房!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    C1Combo2.Select()
                    Exit Sub
                End If


                My_View.RowFilter = "Tj_Lsj_Ty1='同意' and Tj_Yf_Sl" & Mid(C1Combo2.SelectedValue, 6) & " >0"


                Dim Stirpt As New StiReport
                Stirpt.Load(".\Rpt\药库调价查询.mrt")
                Stirpt.ReportName = "药库调价查询药房销售价"
                Stirpt.Dictionary.Databases.Clear()
                Stirpt.RegData(My_View)
                Stirpt.Dictionary.Synchronize()
                TryCast(Stirpt.Pages(0).GetComponents.Item("Text17"), StiText).Text = "原零售价"
                TryCast(Stirpt.Pages(0).GetComponents.Item("Text19"), StiText).Text = "新零售价"
                TryCast(Stirpt.Pages(0).GetComponents.Item("Text28"), StiText).Text = "零售差额"
                TryCast(Stirpt.Pages(0).GetComponents.Item("Text12"), StiText).Text = "{view调价信息.Tj_Yf_Sl" & Mid(C1Combo2.SelectedValue, 6) & "}"
                TryCast(Stirpt.Pages(0).GetComponents.Item("Text14"), StiText).Text = "{view调价信息.Mx_XsDw}"
                TryCast(Stirpt.Pages(0).GetComponents.Item("Text16"), StiText).Text = "{view调价信息.Tj_Lsj_Old}"
                TryCast(Stirpt.Pages(0).GetComponents.Item("Text18"), StiText).Text = "{view调价信息.Tj_Lsj_New}"
                TryCast(Stirpt.Pages(0).GetComponents.Item("Text29"), StiText).Text = "{view调价信息.Tj_Yf_LsCe" & Mid(C1Combo2.SelectedValue, 6) & "}"
                TryCast(Stirpt.Pages(0).GetComponents.Item("Text26"), StiText).Text = "{Sum(GroupHeaderBand1,view调价信息.Tj_Yf_LsCe" & Mid(C1Combo2.SelectedValue, 6) & ")}"
                TryCast(Stirpt.Pages(0).GetComponents.Item("Text23"), StiText).Text = "{Sum(DataBand1,view调价信息.Tj_Yf_LsCe" & Mid(C1Combo2.SelectedValue, 6) & ")}"
                Stirpt.Compile()
                Stirpt("标题") = HisVar.HisVar.WsyName & C1Combo2.SelectedText & "零售价调价打印"
                Stirpt("打印时间") = "打印日期:" & Format(Now, "yyyy-MM-dd HH:mm:ss")
                'Stirpt.Design()
                Stirpt.Show()


        End Select

        My_View.RowFilter = ""


    End Sub
End Class