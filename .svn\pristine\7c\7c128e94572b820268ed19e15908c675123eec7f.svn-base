﻿Imports System.Data.SqlClient
Imports HisControl
Imports Stimulsoft.Report
Imports ZTHisPublicFunction

Public Class Zy_Fy2

#Region "定义__变量"
    Dim My_Adapter As New SqlDataAdapter                '从表适配器
    Dim My_Table As New DataTable                       '从表

    Dim V_GridCol As Integer                            '当前TDBGrid所在的列

    Public Cb_Cm As CurrencyManager                     '同步指针

    Public V_Mz_Code As String                          '出库编码
    Public V_Insert As Boolean                          '增加记录

    Public Ck3_FirstLoad As Boolean                     '第一次调入明细表

    Dim Str_Select As String
    Public Yp_Lb As String
    Dim My_Dataset As New DataSet
    Dim V_Age As String
    Dim V_JbName As String
    Dim V_BlCode As String
    Dim V_Bxlb As String
    Dim V_BcName As String
#End Region


#Region "传参"
    Dim Rform As BaseForm
    Dim Rrow As DataRow
    Dim RZbtb As DataTable
    Dim Rlb As C1.Win.C1Input.C1Label
    Dim Rlx As String

#End Region

    Public Sub New(ByVal tform As BaseForm, ByVal trow As DataRow, ByVal tZbtb As DataTable, ByVal tlb As C1.Win.C1Input.C1Label, ByVal tlx As String)

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        Rform = tform
        Rrow = trow
        RZbtb = tZbtb
        Rlb = tlb
        Rlx = tlx

        ' 在 InitializeComponent() 调用之后添加任何初始化。

    End Sub

    Private Sub Zy_Fy2_FormClosed(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosedEventArgs) Handles Me.FormClosed
        Rform.F_Sum()
    End Sub

    Private Sub Zy_Fy2_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Call Form_Init()                '窗体初始化
        Call Zb_Show()                  '显示数据
    End Sub

#Region "窗体__事件"

    Private Sub Form_Init()
        Panel2.Height = 30
        '按扭初始化
        Call P_Comm(Me.Comm1)
        Call P_Comm(Me.Comm2)
        Call P_Comm(Me.Comm3)


        Dim My_Grid As New BaseClass.C_Grid(Me.C1TrueDBGrid1)
        With My_Grid
            .Init_Grid()
            .AllSort(False)
            '.P_MultiSelect(BaseClass.C_Grid.MultiSelect.Extended)
            '.P_MarqueeEnum(BaseClass.C_Grid.MarqueeEnum.FloatingEditor)
            .P_Dock(BaseClass.C_Grid.Dock.Fill)
            .Init_Column("药品名称", "Yp_Name", 200, "左", "")
            .Init_Column("规格", "Mx_Gg", 100, "左", "")
            .Init_Column("产地", "Mx_Cd", 200, "左", "")
            .Init_Column("生产批号", "Yp_Ph", 80, "左", "")
            .Init_Column("有效期", "Yp_Yxq", 80, "中", "yyyy-MM-dd")
            .Init_Column("数量", "Cf_Sl", 60, "右", "###,###,##0.####")
            If ZTHisPara.PublicConfig.PharmacyShowKc = True Then
                .Init_Column("库存数量", "Yf_Sl", 70, "右", "###,###,##0.####")
            End If
            .Init_Column("单位", "Mx_XsDw", 45, "左", "")
            .Init_Column("单价", "Cf_Dj", 70, "右", "###,###,##0.00####")
            .Init_Column("金额", "Cf_Money", 80, "右", "###,###,##0.00##")
            .Init_Column("用法用量", "Yp_Yfyl", 200, "左", "")
        End With
    End Sub
    Private Sub P_Data_Show()   '从表数据
        If My_Dataset.Tables("发药") IsNot Nothing Then
            My_Dataset.Tables("发药").Clear()
        End If
        With My_Adapter

            Dim Yf_Sl As String
            Yf_Sl = "Yf_Sl" & CDbl(Mid(HisVar.HisVar.YfCode, 5, 2))

            Str_Select = "SELECT  dbo.Bl_CfYp.*,Yp_Name,Mx_Gg,Mx_Cd,Mx_Gyzz,Mx_XsDw,V_YpKc.Yp_Ph,V_YpKc.Yp_Yxq,Dl_Code," & Yf_Sl & " as Yf_Sl FROM Bl_CfYp INNER JOIN V_YpKc ON Bl_CfYp.Xx_Code = dbo.V_YpKc.Xx_Code where Cf_Code='" & Label12.Text & "' order by Cf_Id"
            .SelectCommand = New SqlCommand(Str_Select, My_Cn)
            .Fill(My_Dataset, "发药")

        End With
        My_Table = My_Dataset.Tables("发药")



        '主表记录
        Cb_Cm = CType(BindingContext(My_Dataset, "发药"), CurrencyManager)
        C1TrueDBGrid1.SetDataBinding(My_Dataset, "发药", True)

        Dim Sum1 As Double = IIf(My_Dataset.Tables("发药").Compute("Sum(Cf_Money)", "") Is DBNull.Value, 0, My_Dataset.Tables("发药").Compute("Sum(Cf_Money)", ""))
        With C1TrueDBGrid1
            .ColumnFooters = True
            .Columns("Cf_Money").FooterText = Format(Sum1, "###0.00")
        End With

        Call P_Sum()

    End Sub
#End Region

#Region "清空__显示"

    Private Sub Zb_Show()   '显示记录

        With Rrow
            V_Mz_Code = .Item("Cf_Code") & ""
            Label15.Text = .Item("Ks_Name") & ""
            Label4.Text = .Item("Ys_Name") & ""
            Label7.Text = .Item("Ry_Name") & ""
            Label1.Text = .Item("Ry_Sex") & ""
            Label5.Text = .Item("Ry_Address") & ""
            V_Age = .Item("Ry_Age") & ""
            V_JbName = .Item("Jb_Name") & ""
            V_BlCode = .Item("Ry_BlCode") & ""
            V_Bxlb = .Item("Bxlb_Name") & ""
            V_BcName = .Item("Bc_Name") & ""
        End With
        Label12.Text = V_Mz_Code                                                '出库编码
        Call P_Data_Show()
    End Sub



#End Region

#Region "控件__动作"

#Region "其它__控件"

    Private Sub Comm_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.Click
        If MsgBox("是否发送:住院处方编码=" + Rrow("Cf_Code") + " ", MsgBoxStyle.Information + MsgBoxStyle.OkCancel, "提示:") = MsgBoxResult.Cancel Then Exit Sub

        Dim Pd As Object = HisVar.HisVar.Sqldal.GetSingle("select distinct Pd_Wc from Zd_YfPd where Pd_Wc='否' and Yf_Code='" & HisVar.HisVar.YfCode & "'")
        If Pd Is Nothing Then
        Else
            MsgBox("正在进行盘点，请等待盘点完成后在进行发药！", MsgBoxStyle.Information, "提示：")
            Exit Sub
        End If

        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "select Yp_Name from V_YpKc,(Select Sum(Cf_Sl) as Cf_Sl,Xx_Code from Bl_CfYp where  Cf_Code='" & Label12.Text & "' group by Xx_Code)A where A.Xx_Code=V_YpKc.Xx_Code And Cf_Sl>Yf_Sl" & CDbl(Mid(HisVar.HisVar.YfCode, 5, 2)) & " ", "导致负库存", True)
        If My_Dataset.Tables("导致负库存").Rows.Count > 0 Then
            Dim Str As String = ""
            Dim Yp_Row As DataRow
            For Each Yp_Row In My_Dataset.Tables("导致负库存").Rows
                Str = Str & Yp_Row.Item("Yp_Name") & vbCr
            Next
            MsgBox(Str & "大于库存数量，请点击【退回】进行修改处方！", MsgBoxStyle.Information, "提示：")
            Exit Sub
        End If

        If HisVar.HisVar.Sqldal.GetSingle("Select Count(*) from Bl_Cf where Cf_Qr_Date is not null and Cf_Code='" & Label12.Text & "'") > 0 Then
            MsgBox("该处方已经发药,请更新状态！", MsgBoxStyle.Information, "提示")
            Exit Sub
        End If

        If HisVar.HisVar.Sqldal.GetSingle("Select Count(*) from Bl_Cf where Cf_Print = '否' and Cf_Qr = '否' And Cf_Code='" & Label12.Text & "'") > 0 Then
            MsgBox("该处方已经退回,请更新状态！", MsgBoxStyle.Information, "提示")
            Exit Sub
        End If

        Me.Cursor = Cursors.WaitCursor
        Comm1.Enabled = False


        Dim Yf_Sl As String = "Yf_Sl" & CDbl(Mid(HisVar.HisVar.YfCode, 5, 2))
        Dim Arr As New ArrayList

        Arr.Add("Update Zd_Ml_Yp4 set " & Yf_Sl & "=isnull(" & Yf_Sl & ",0)-Cf_Sl from (Select Sum(Cf_Sl) Cf_Sl,Xx_Code from Bl_CfYp where Cf_Code='" & Rrow("Cf_Code") & "' Group by xx_code)A where A.Xx_Code=Zd_Ml_Yp4.Xx_Code")
        Arr.Add("Update Bl_Cf Set Cf_Qr='是',Cf_Qr_Date=Getdate(),Jsr_Code_Qr='" & HisVar.HisVar.JsrCode & "',Ly_Wc='是' Where Cf_Code='" & Label12.Text & "'")

        Try
            HisVar.HisVar.Sqldal.ExecuteSqlTran(Arr)
            Rrow.Delete()
            Rrow.AcceptChanges()
            Me.Cursor = Cursors.Default
            Comm1.Enabled = True
        Catch ex As Exception
            Me.Cursor = Cursors.Default
            MsgBox(ex.Message.ToString, MsgBoxStyle.Information, "提示：")

        End Try

        Me.Close()
    End Sub
#End Region

    Private Sub Comm2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm2.Click
        Me.Close()
    End Sub
#End Region



#Region "自定义函数"

    Private Sub P_Sum()
        If My_Table.Rows.Count = 0 Then
            T_Label5.Text = "0条"
        Else
            T_Label5.Text = Trim(My_Table.Rows.Count & "条")
        End If
        T_Label5.Location = New Point(Me.Width - T_Label5.Width - 7, 8)
        T_Label4.Location = New Point(Me.Width - T_Label5.Width - T_Label4.Width - 7, 8)
    End Sub

#End Region


#Region "鼠标__外观"
    Private Sub P_Comm(ByVal Bt As Button)
        With Bt
            Select Case .Tag
                Case "发药"
                    .Location = New Point(T_Line1.Left + 10, 4)
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_发药1")
                    .Text = "            &B"
                Case "取消"
                    .Location = New Point(Comm1.Left + Comm1.Width + 4, 4)
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
                    .Width = Me.Comm1.Width
                    .Text = "            &C"
                Case "打印处方"
                    .Location = New Point(Comm2.Left + Comm2.Width + 4, 4)
                    .BackgroundImage = MyResources.C_Resources.getimage("打印处方1")
                    .Text = "            &P"

                    T_Line2.Location = New Point(Me.Comm3.Left + Me.Comm3.Width + 8, 0)
            End Select
            .FlatStyle = FlatStyle.Flat
            .FlatAppearance.BorderSize = 0
            .BackgroundImageLayout = ImageLayout.Center
        End With
    End Sub

    Private Sub Comm_MouseEnter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm2.MouseEnter, Comm1.MouseEnter, Comm3.MouseEnter
        Select Case sender.tag
            Case "打印处方"
                Comm3.BackgroundImage = MyResources.C_Resources.getimage("打印处方2")
                Comm3.Cursor = Cursors.Hand
            Case "发药"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_发药2")
                Comm1.Cursor = Cursors.Hand
            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消2")
                Comm2.Cursor = Cursors.Hand
        End Select

    End Sub

    Private Sub Comm_MouseLeave(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm2.MouseLeave, Comm1.MouseLeave, Comm3.MouseLeave
        Select Case sender.tag
            Case "打印处方"
                Comm3.BackgroundImage = MyResources.C_Resources.getimage("打印处方1")
                Comm3.Cursor = Cursors.Default
            Case "发药"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_发药1")
                Comm1.Cursor = Cursors.Default
            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
                Comm2.Cursor = Cursors.Default
        End Select
    End Sub

    Private Sub Comm_MouseDown(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm2.MouseDown, Comm1.MouseDown, Comm3.MouseDown
        Select Case sender.tag
            Case "打印处方"
                Comm3.BackgroundImage = MyResources.C_Resources.getimage("打印处方3")
                Comm3.Cursor = Cursors.Default
            Case "发药"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_发药3")
                Comm1.Cursor = Cursors.Default

            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消3")
                Comm2.Cursor = Cursors.Default
        End Select
    End Sub

    Private Sub Comm_MouseUp(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm2.MouseUp, Comm1.MouseUp, Comm3.MouseUp
        Select Case sender.tag
            Case "打印处方"
                Comm3.BackgroundImage = MyResources.C_Resources.getimage("打印处方1")
                Comm3.Cursor = Cursors.Hand
            Case "发药"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_发药1")
                Comm1.Cursor = Cursors.Hand
            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
                Comm2.Cursor = Cursors.Hand
        End Select
    End Sub
#End Region

    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm3.Click
        Call Print_Zy_Cfj()
    End Sub

    Private Sub Print_Zy_Cfj()

        Dim rpt As New StiReport
        Dim zycf As ZTHisPublicFunction.InpatientPrescription.IInpatientPrescription
        zycf = ZTHisPublicFunction.InpatientPrescription.InpatientPrescriptionFactory.CreateInpatientPrescriptionObject()
        Dim prescriptionPara As New prescriptionPara()
        prescriptionPara.Mz_Code = Label12.Text
        prescriptionPara.Ry_Name = Label7.Text
        prescriptionPara.Ry_Sex = Label1.Text
        prescriptionPara.Ks_Name = Label15.Text
        prescriptionPara.Bc_Name = V_BcName
        prescriptionPara.Ys_Name = Label4.Text
        prescriptionPara.Mz_Money = CDbl(Rrow("Cf_Money"))

        prescriptionPara.Jb_Name = V_JbName
        prescriptionPara.Ry_Age = V_Age
        prescriptionPara.Bxlb_Name = V_Bxlb
        prescriptionPara.Ry_BlCode = V_BlCode
        rpt = zycf.Print(prescriptionPara)
        rpt.Show()
        Exit Sub

        Dim V_ColId As Integer
        Dim V_TbRowCount As Integer
        Dim V_Newrow As DataRow
        If My_Dataset.Tables("处方明细1") IsNot Nothing Then My_Dataset.Tables("处方明细1").Clear()

        'Dim Str As String = "select Mx_Code,Mx_Gg+'   ×' as Mx_Gg,sum(Cf_Sl) as Cf_Sl,Mx_XsDw,'用法： '+isnull(Yp_Yfyl,'') as Yp_Yfyl,Sum(Cf_Money) as Cf_Money,IsJb,Yp_Name from Bl_CfYp,V_Ypkc where Bl_CfYp.Xx_Code=V_Ypkc.Xx_Code And Cf_Code='" & Label12.Text & "' and Dl_Code='01'   Group by Mx_Code,Yp_Name,Mx_Gg,Mx_XsDw,isjb,Yp_Yfyl"
        Dim Str As String = "select Mx_Code,Cf_Id,Mx_Gg+'   ×' as Mx_Gg,Cf_Sl,Mx_XsDw,'用法： '+isnull(Yp_Yfyl,'') as Yp_Yfyl,Cf_Money,IsJb,Yp_Name,Jx_Name from Bl_CfYp,V_Ypkc where Bl_CfYp.Xx_Code=V_Ypkc.Xx_Code And Cf_Code='" & Label12.Text & "' and Dl_Code='01'   Order By Cf_Id"

        Dim My_Adapter As SqlDataAdapter = New SqlDataAdapter(Str, My_Cn)
        My_Adapter.Fill(My_Dataset, "处方明细1")

        If My_Dataset.Tables("处方明细1").Rows.Count Mod 5 <> 0 Then
            For V_TbRowCount = 1 To 5 - (My_Dataset.Tables("处方明细1").Rows.Count Mod 5)
                V_Newrow = My_Dataset.Tables("处方明细1").NewRow
                With V_Newrow

                    .Item("Mx_Code") = DBNull.Value
                    .Item("Cf_Id") = 2147483647
                    .Item("Yp_Name") = DBNull.Value
                    .Item("Mx_Gg") = DBNull.Value
                    .Item("Cf_Sl") = DBNull.Value
                    .Item("Mx_XsDw") = DBNull.Value
                    .Item("Yp_Yfyl") = DBNull.Value
                    .Item("Cf_Money") = DBNull.Value
                    .Item("Jx_Name") = DBNull.Value
                    '.Item("IsJb1") = DBNull.Value
                End With
                My_Dataset.Tables("处方明细1").Rows.Add(V_Newrow)
                V_Newrow.AcceptChanges()
            Next
        End If
        V_ColId = BaseFunc.BaseFunc.Edit_Col0(My_Dataset.Tables("处方明细1"), 0, 5)

        'Str = "select Mx_Code,Mx_Gg+'   ×' as Mx_Gg,sum(Cf_Sl) as Cf_Sl,Mx_XsDw,'用法： '+isnull(Yp_Yfyl,'') as Yp_Yfyl,Sum(Cf_Money) as Cf_Money,IsJb,Yp_Name from Bl_CfYp,V_Ypkc where Bl_CfYp.Xx_Code=V_Ypkc.Xx_Code And Cf_Code='" & Label12.Text & "' and Dl_Code='02'   Group by Mx_Code,Yp_Name,Mx_Gg,Mx_XsDw,isjb,Yp_Yfyl"
        Str = "select Mx_Code,Cf_Id,Mx_Gg+'   ×' as Mx_Gg,Cf_Sl,Mx_XsDw,'用法： '+isnull(Yp_Yfyl,'') as Yp_Yfyl,Cf_Money,IsJb,Yp_Name,Jx_Name from Bl_CfYp,V_Ypkc where Bl_CfYp.Xx_Code=V_Ypkc.Xx_Code And Cf_Code='" & Label12.Text & "' and Dl_Code='02'  Order by cf_id"

        My_Adapter = New SqlDataAdapter(Str, My_Cn)
        My_Adapter.Fill(My_Dataset, "处方明细1")



        If My_Dataset.Tables("处方明细1").Rows.Count Mod 5 <> 0 Then
            For V_TbRowCount = 1 To 5 - (My_Dataset.Tables("处方明细1").Rows.Count Mod 5)
                V_Newrow = My_Dataset.Tables("处方明细1").NewRow
                With V_Newrow

                    .Item("Mx_Code") = DBNull.Value
                    .Item("Cf_Id") = 2147483647
                    .Item("Yp_Name") = DBNull.Value
                    .Item("Mx_Gg") = DBNull.Value
                    .Item("Cf_Sl") = DBNull.Value
                    .Item("Mx_XsDw") = DBNull.Value
                    .Item("Yp_Yfyl") = DBNull.Value
                    .Item("Cf_Money") = DBNull.Value
                    .Item("Jx_Name") = DBNull.Value
                    '.Item("IsJb1") = DBNull.Value
                End With
                My_Dataset.Tables("处方明细1").Rows.Add(V_Newrow)
                V_Newrow.AcceptChanges()
            Next
        End If
        V_ColId = BaseFunc.BaseFunc.Edit_Col0(My_Dataset.Tables("处方明细1"), V_ColId + 1, 5)


        Str = "select Mx_Code,Cf_id,Mx_Gg+'   ×' as Mx_Gg,Cf_Sl,Mx_XsDw,'用法： '+isnull(Yp_Yfyl,'') as Yp_Yfyl,Cf_Money,IsJb,Yp_Name,Jx_Name from Bl_CfYp,V_Ypkc where Bl_CfYp.Xx_Code=V_Ypkc.Xx_Code And Cf_Code='" & Label12.Text & "' and Dl_Code='03'   Order by Cf_id"

        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, Str, "中药处方", True)

        '中药处方单张方上药品数量无限制，尽可能多
        If My_Dataset.Tables("中药处方").Rows.Count Mod 40 <> 0 Then
            For V_TbRowCount = 1 To 40 - (My_Dataset.Tables("中药处方").Rows.Count Mod 40)
                V_Newrow = My_Dataset.Tables("中药处方").NewRow
                With V_Newrow
                    .Item("Mx_Code") = V_TbRowCount
                    .Item("Cf_Id") = 2147483500 + V_TbRowCount
                    .Item("Yp_Name") = DBNull.Value
                    .Item("Mx_Gg") = DBNull.Value
                    .Item("Cf_Sl") = DBNull.Value
                    .Item("Mx_XsDw") = DBNull.Value
                    .Item("Yp_Yfyl") = DBNull.Value
                    .Item("Cf_Money") = DBNull.Value
                    .Item("Jx_Name") = DBNull.Value

                End With
                My_Dataset.Tables("中药处方").Rows.Add(V_Newrow)
                V_Newrow.AcceptChanges()
            Next
        End If

        V_ColId = BaseFunc.BaseFunc.Edit_Col0(My_Dataset.Tables("中药处方"), 0, 40)



        Str = "select Mx_Code,Cf_Id,Mx_Gg+'   ×' as Mx_Gg,Cf_Sl,Mx_XsDw,'用法： '+isnull(Yp_Yfyl,'') as Yp_Yfyl,Cf_Money,IsJb,Yp_Name,''Jx_Name from Bl_CfYp,V_Ypkc where Bl_CfYp.Xx_Code=V_Ypkc.Xx_Code And Cf_Code='" & Label12.Text & "'  and Dl_Code='04' Order By Cf_Id"
        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, Str, "卫材处方", True)
        If My_Dataset.Tables("卫材处方").Rows.Count Mod 20 <> 0 Then
            For V_TbRowCount = 1 To 20 - (My_Dataset.Tables("卫材处方").Rows.Count Mod 20)
                V_Newrow = My_Dataset.Tables("卫材处方").NewRow
                With V_Newrow
                    .Item("Mx_Code") = V_TbRowCount
                    .Item("Cf_Id") = 2147483500 + V_TbRowCount
                    .Item("Yp_Name") = DBNull.Value
                    .Item("Mx_Gg") = DBNull.Value
                    .Item("Cf_Sl") = DBNull.Value
                    .Item("Mx_XsDw") = DBNull.Value
                    .Item("Yp_Yfyl") = DBNull.Value
                    .Item("Cf_Money") = DBNull.Value
                    .Item("Jx_Name") = DBNull.Value

                End With
                My_Dataset.Tables("卫材处方").Rows.Add(V_Newrow)
                V_Newrow.AcceptChanges()
            Next
        End If
        V_ColId = BaseFunc.BaseFunc.Edit_Col0(My_Dataset.Tables("卫材处方"), 0, 20)


        Str = "select Bl_CfXm.Xm_Code as Mx_Code,Cf_Id,'' as Mx_Gg,Cf_Sl,Xm_Dw as Mx_XsDw,'' as Yp_Yfyl,Cf_Money,'' as IsJb,Xm_Name as Yp_Name,''Jx_Name from Bl_CfXm,Zd_Ml_Xm3 where Bl_CfXm.Xm_Code=Zd_Ml_Xm3.Xm_Code And Cf_Code='" & Label12.Text & "'   Order By Cf_Id"
        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, Str, "诊疗处方", True)

        If My_Dataset.Tables("诊疗处方").Rows.Count Mod 20 <> 0 Then
            For V_TbRowCount = 1 To 20 - (My_Dataset.Tables("诊疗处方").Rows.Count Mod 20)
                V_Newrow = My_Dataset.Tables("诊疗处方").NewRow
                With V_Newrow

                    .Item("Mx_Code") = V_TbRowCount
                    .Item("Cf_Id") = 2147483500 + V_TbRowCount
                    .Item("Yp_Name") = DBNull.Value
                    .Item("Mx_Gg") = DBNull.Value
                    .Item("Cf_Sl") = DBNull.Value
                    .Item("Mx_XsDw") = DBNull.Value
                    .Item("Yp_Yfyl") = DBNull.Value
                    .Item("Cf_Money") = DBNull.Value
                    .Item("Jx_Name") = DBNull.Value

                End With
                My_Dataset.Tables("诊疗处方").Rows.Add(V_Newrow)
                V_Newrow.AcceptChanges()
            Next
        End If
        V_ColId = BaseFunc.BaseFunc.Edit_Col0(My_Dataset.Tables("诊疗处方"), 0, 20)

        Dim StiRpt As New StiReport
        StiRpt.Load(".\Rpt\住院处方表(二分之一A4).mrt")
        ' StiRpt.Load(".\Rpt\住院处方表.mrt")
        StiRpt.ReportName = "住院处方表"
        StiRpt.RegData(My_Dataset.Tables("处方明细1"))
        StiRpt.RegData(My_Dataset.Tables("中药处方"))
        StiRpt.RegData(My_Dataset.Tables("卫材处方"))
        StiRpt.RegData(My_Dataset.Tables("诊疗处方"))

        If My_Dataset.Tables("处方明细1").Rows.Count > 0 Then
            StiRpt.Pages(0).Enabled = True
        Else
            StiRpt.Pages(0).Enabled = False
        End If

        If My_Dataset.Tables("中药处方").Rows.Count > 0 Then
            StiRpt.Pages(1).Enabled = True
        Else
            StiRpt.Pages(1).Enabled = False
        End If

        If My_Dataset.Tables("卫材处方").Rows.Count > 0 Then
            StiRpt.Pages(2).Enabled = True
        Else
            StiRpt.Pages(2).Enabled = False
        End If

        If My_Dataset.Tables("诊疗处方").Rows.Count > 0 Then
            StiRpt.Pages(3).Enabled = True
        Else
            StiRpt.Pages(3).Enabled = False
        End If




        StiRpt.Compile()
        StiRpt("标题") = HisVar.HisVar.WsyName & "住院处方笺"
        StiRpt("处方编码") = Label12.Text '门诊编号
        StiRpt("打印时间") = Format(Now, "yyyy年MM月dd日")
        StiRpt("患者姓名") = Label7.Text '患者姓名
        StiRpt("患者性别") = Label1.Text '患者性别
        StiRpt("科室") = Label15.Text '科室
        StiRpt("床位号") = V_BcName '床位号
        StiRpt("医生姓名") = Label4.Text
        StiRpt("金额") = Format(CDbl(Rrow("Cf_Money")), "#0.00")

        StiRpt("经手人") = HisVar.HisVar.JsrName

        Dim My_Reader As SqlDataReader = HisVar.HisVar.Sqldal.ExecuteReader("SELECT Bl_Code,Ry_Jc,Ry_Name,Ry_Sex,Jb_Name,Bc_Name,Ry_BlCode,Ys_Code,BL.Ks_Code,Bxlb_Name,DATEDIFF(yyyy,Ry_Csdate,getdate()) AS Ry_Age FROM Bl,Zd_YyBc,Zd_Bxlb Where Bl.Bxlb_Code=Zd_Bxlb.Bxlb_Code and Bl.Bc_Code=Zd_YyBc.Bc_Code  And Isnull(Ry_CyDate,'1900-01-01')='1900-01-01' And Bl.Yy_Code='" & HisVar.HisVar.WsyCode & "' And Bl_Code='" & Rrow("Bl_Code") & "'Order By Ry_Jc")
        While My_Reader.Read
            StiRpt("年龄") = My_Reader.Item("Ry_Age") & "" '年龄
            StiRpt("类别") = My_Reader.Item("Bxlb_Name") & "" '类别
            StiRpt("疾病") = My_Reader.Item("Jb_Name") & "" '疾病
            StiRpt("病历号") = My_Reader.Item("Ry_BlCode") & "" '病历号
        End While
        My_Reader.Close()
        Call P_Conn(False)

        'StiRpt.Design()
        StiRpt.Show()


    End Sub
End Class