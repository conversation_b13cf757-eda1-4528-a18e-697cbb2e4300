﻿Imports System.Windows.Forms
Imports System.Text

Public Class PublicForm
    Dim m_lb As String
    Dim m_code As String
    Dim My_DataSet As New DataSet
    Public str_where As String
    Public str_text As String
    Public My_Cm As CurrencyManager             '同步指针
    Dim My_View As DataView
    Dim key As String
    Dim Materials_StockBll As New BLLOld.B_Materials_Stock
    Dim RQuery As String
    Dim My_Table As DataTable
    Public Sub New(ByVal Lb As String, ByVal tQuery As String)

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        m_lb = Lb
        RQuery = tQuery
        ' 在 InitializeComponent() 调用之后添加任何初始化。
    End Sub

    Private Sub F_Select_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Me.Text = m_lb & "选择"

        FilterTextBox.Captain = m_lb

        Select Case m_lb
            Case "物资类别"
                With MyGrid1
                    .Init_Grid()
                    .Init_Column("编码", "Code", "0", "左", "", False) '0
                    .Init_Column("简称", "Py", "100", "左", "", False)
                    .Init_Column("名称", "Name", "200", "左", "", False)
                    .Init_Column("选择", "IsSelect", "20", "中", "Check", True)

                End With
            Case "物资"
                With MyGrid1
                    .Init_Grid()
                    .Init_Column("编码", "Code", "0", "左", "", False) '0
                    .Init_Column("简称", "Py", "100", "左", "", False)
                    .Init_Column("物资名称", "Name", "150", "左", "", True) '4-
                    .Init_Column("物资批号", "MaterialsLot", "80", "左", "", False) '3-
                    .Init_Column("物资有效期", "MaterialsExpiryDate", "100", "左", "yyyyMMdd", False) '1-
                    .Init_Column("物资规格", "Materials_Spec", "80", "左", "yyyyMMdd", False) '1-
                    .Init_Column("选择", "IsSelect", "20", "中", "Check", True)
                End With
        End Select

        Select Case m_lb
            Case "物资类别"
                My_Table = Materials_StockBll.GetHzListMaterialsClass(RQuery).Tables(0)
            Case "物资"
                My_Table = Materials_StockBll.GetHzListMaterials(RQuery).Tables(0)
        End Select


        Dim col As New DataColumn
        col.ColumnName = "IsSelect"
        col.DataType = GetType(Boolean)
        col.DefaultValue = False
        My_Table.Columns.Add(col)
        With MyGrid1
            .DataTable = My_Table
            My_Cm = CType(BindingContext(.DataSource, .DataMember), CurrencyManager)
        End With
        Me.MyGrid1.MultiSelect = C1.Win.C1TrueDBGrid.MultiSelectEnum.Extended

        My_View = My_Cm.List
    End Sub

    Private Sub C1TextBox1_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles FilterTextBox.TextChanged
        My_View.RowFilter = "Py like '*" & FilterTextBox.Text & "*'"
    End Sub

    Private Sub MyButton1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles QXButton.Click, FXButton.Click
        If sender.text = "全选" Then
            For Each row In My_Table.Rows
                row("IsSelect") = True
            Next
        End If

        If sender.text = "反选" Then
            For Each row In My_Table.Rows
                row("IsSelect") = False
            Next
        End If
    End Sub

    Private Sub button_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles EnsureButton.Click
        str_where = ""
        str_text = ""
        For Each row In My_View.ToTable.Rows ' My_DataSet.Tables("选择字典").Rows
            If row("IsSelect") = True Then
                str_where = str_where & "'" & row("Code") & "',"
                str_text = str_text & "," & row("Name")
            End If
        Next
        If str_where <> "" Then
            str_where = "(" & Mid(str_where, 1, str_where.Length - 1) & ")"
            str_text = Mid(str_text, 2)
        Else

        End If

    End Sub

    Private Sub C1TextBox1_KeyDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles FilterTextBox.KeyDown
        If e.KeyValue = 40 Then
            MyGrid1.Focus()
        End If
    End Sub


    Private Sub MyGrid1_DoubleClick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyGrid1.DoubleClick
        Dim up_Row As DataRow                    '当 前 行
        up_Row = My_Cm.List(MyGrid1.Row).Row
        If up_Row("IsSelect") = False Then
            up_Row("IsSelect") = True
        Else
            up_Row("IsSelect") = False
        End If

    End Sub





   
End Class
