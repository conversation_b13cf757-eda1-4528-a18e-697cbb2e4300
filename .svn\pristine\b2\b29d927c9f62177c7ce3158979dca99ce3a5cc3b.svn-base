﻿/**  版本信息模板在安装目录下，可自行修改。
* D_Materials_Move2.cs
*
* 功 能： N/A
* 类 名： D_Materials_Move2
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016-12-05 13:54:39   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Collections.Generic;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using ModelOld;

namespace DAL
{
    /// <summary>
    /// 数据访问类:D_Materials_Move2
    /// </summary>
    public partial class D_Materials_Move2
    {
        public D_Materials_Move2()
        { }
        #region  BasicMethod
        public string MaxCode(string M_Move_Code)
        {
            string max = M_Move_Code + (string)(HisVar.HisVar.Sqldal.F_MaxCode("select right(max(M_Move_Detail_Code),4) from Materials_Move2 where left(M_Move_Detail_Code,11)='" + M_Move_Code + "'", 4));
            return max;
        }

        public string MaxNewStockCode(string Materials_Code)
        {
            string max = Materials_Code + (string)(HisVar.HisVar.Sqldal.F_MaxCode("select max(substring(New_MaterialsStock_Code,11,6)) from Materials_Move2 where Materials_Code='" + Materials_Code + "'", 6));
            return max;
        }

        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool Exists(string M_Move_Detail_Code)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from Materials_Move2");
            strSql.Append(" where M_Move_Detail_Code=@M_Move_Detail_Code ");
            SqlParameter[] parameters = {
                    new SqlParameter("@M_Move_Detail_Code", SqlDbType.Char,15)          };
            parameters[0].Value = M_Move_Detail_Code;

            return HisVar.HisVar.Sqldal.Exists(strSql.ToString(), parameters);
        }


        /// <summary>
        /// 增加一条数据
        /// </summary>
        public bool Add(ModelOld.M_Materials_Move2 model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into Materials_Move2(");
            strSql.Append("M_Move_Code,Materials_Code,New_MaterialsStock_Code,Old_MaterialsStock_Code,M_Move_Detail_Code,MaterialsLot,MaterialsExpiryDate,M_Move_Num,M_Move_Price,M_Move_Money,M_MoveDetail_Memo,Materials_Spec)");
            strSql.Append(" values (");
            strSql.Append("@M_Move_Code,@Materials_Code,@New_MaterialsStock_Code,@Old_MaterialsStock_Code,@M_Move_Detail_Code,@MaterialsLot,@MaterialsExpiryDate,@M_Move_Num,@M_Move_Price,@M_Move_Money,@M_MoveDetail_Memo,@Materials_Spec)");
            SqlParameter[] parameters = {
                    new SqlParameter("@M_Move_Code", SqlDbType.Char,11),
                    new SqlParameter("@Materials_Code", SqlDbType.Char,10),
                    new SqlParameter("@New_MaterialsStock_Code", SqlDbType.Char,16),
                    new SqlParameter("@Old_MaterialsStock_Code", SqlDbType.Char,16),
                    new SqlParameter("@M_Move_Detail_Code", SqlDbType.Char,15),
                    new SqlParameter("@MaterialsLot", SqlDbType.VarChar,50),
                    new SqlParameter("@MaterialsExpiryDate", SqlDbType.SmallDateTime),
                    new SqlParameter("@M_Move_Num", SqlDbType.Decimal,5),
                    new SqlParameter("@M_Move_Price", SqlDbType.Decimal,5),
                    new SqlParameter("@M_Move_Money", SqlDbType.Decimal,5),
                    new SqlParameter("@M_MoveDetail_Memo", SqlDbType.VarChar,200),
                    new SqlParameter("@Materials_Spec", SqlDbType.VarChar,50)};
            parameters[0].Value = model.M_Move_Code;
            parameters[1].Value = model.Materials_Code;
            parameters[2].Value = Common.Tools.IsValueNull(model.New_MaterialsStock_Code);
            parameters[3].Value = model.Old_MaterialsStock_Code;
            parameters[4].Value = Common.Tools.IsValueNull(model.M_Move_Detail_Code);
            parameters[5].Value = model.MaterialsLot;
            parameters[6].Value = model.MaterialsExpiryDate;
            parameters[7].Value = model.M_Move_Num;
            parameters[8].Value = model.M_Move_Price;
            parameters[9].Value = model.M_Move_Money;
            parameters[10].Value = model.M_MoveDetail_Memo;
            parameters[11].Value = model.Materials_Spec;
            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 更新一条数据
        /// </summary>
        public bool Update(ModelOld.M_Materials_Move2 model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update Materials_Move2 set ");
            strSql.Append("M_Move_Code=@M_Move_Code,");
            strSql.Append("Materials_Code=@Materials_Code,");
            strSql.Append("New_MaterialsStock_Code=@New_MaterialsStock_Code,");
            strSql.Append("Old_MaterialsStock_Code=@Old_MaterialsStock_Code,");
            strSql.Append("MaterialsLot=@MaterialsLot,");
            strSql.Append("MaterialsExpiryDate=@MaterialsExpiryDate,");
            strSql.Append("M_Move_Num=@M_Move_Num,");
            strSql.Append("M_Move_Price=@M_Move_Price,");
            strSql.Append("M_Move_Money=@M_Move_Money,");
            strSql.Append("M_MoveDetail_Memo=@M_MoveDetail_Memo,");
            strSql.Append("Materials_Spec=@Materials_Spec");
            strSql.Append(" where M_Move_Detail_Code=@M_Move_Detail_Code ");
            SqlParameter[] parameters = {
                    new SqlParameter("@M_Move_Code", SqlDbType.Char,11),
                    new SqlParameter("@Materials_Code", SqlDbType.Char,10),
                    new SqlParameter("@New_MaterialsStock_Code", SqlDbType.Char,16),
                    new SqlParameter("@Old_MaterialsStock_Code", SqlDbType.Char,16),
                    new SqlParameter("@MaterialsLot", SqlDbType.VarChar,50),
                    new SqlParameter("@MaterialsExpiryDate", SqlDbType.SmallDateTime),
                    new SqlParameter("@M_Move_Num", SqlDbType.Decimal,5),
                    new SqlParameter("@M_Move_Price", SqlDbType.Decimal,5),
                    new SqlParameter("@M_Move_Money", SqlDbType.Decimal,5),
                    new SqlParameter("@M_MoveDetail_Memo", SqlDbType.VarChar,200),
                    new SqlParameter("@Materials_Spec", SqlDbType.VarChar,50),
                    new SqlParameter("@M_Move_Detail_Code", SqlDbType.Char,15)};
            parameters[0].Value = model.M_Move_Code;
            parameters[1].Value = model.Materials_Code;
            parameters[2].Value = Common.Tools.IsValueNull(model.New_MaterialsStock_Code);
            parameters[3].Value = model.Old_MaterialsStock_Code;
            parameters[4].Value = model.MaterialsLot;
            parameters[5].Value = model.MaterialsExpiryDate;
            parameters[6].Value = model.M_Move_Num;
            parameters[7].Value = model.M_Move_Price;
            parameters[8].Value = model.M_Move_Money;
            parameters[9].Value = model.M_MoveDetail_Memo;
            parameters[10].Value = model.Materials_Spec;
            parameters[11].Value = model.M_Move_Detail_Code;

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public bool Delete(string M_Move_Detail_Code)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from Materials_Move2 ");
            strSql.Append(" where M_Move_Detail_Code=@M_Move_Detail_Code ");
            SqlParameter[] parameters = {
                    new SqlParameter("@M_Move_Detail_Code", SqlDbType.Char,15)          };
            parameters[0].Value = M_Move_Detail_Code;

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        /// <summary>
        /// 批量删除数据
        /// </summary>
        public bool DeleteList(string M_Move_Detail_Codelist)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from Materials_Move2 ");
            strSql.Append(" where M_Move_Detail_Code in (" + M_Move_Detail_Codelist + ")  ");
            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString());
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }


        /// <summary>
        /// 得到一个对象实体集
        /// </summary>
        public List<ModelOld.M_Materials_Move2> GetModelList(string M_Move_Code)
        {
            List<ModelOld.M_Materials_Move2> modelList = new List<M_Materials_Move2>();
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  M_Move_Code,Materials_Code,New_MaterialsStock_Code,Old_MaterialsStock_Code,M_Move_Detail_Code,MaterialsLot,MaterialsExpiryDate,Materials_Spec,M_Move_Num,M_Move_Price,M_Move_Money,M_MoveDetail_Memo from Materials_Move2 ");
            strSql.Append(" where M_Move_Code=@M_Move_Code ");
            SqlParameter[] parameters = {
                    new SqlParameter("@M_Move_Code", SqlDbType.Char,11)         };
            parameters[0].Value = M_Move_Code;

            ModelOld.M_Materials_Move2 model = new ModelOld.M_Materials_Move2();
            DataSet ds = HisVar.HisVar.Sqldal.Query(strSql.ToString(), parameters);

            if (ds.Tables[0].Rows.Count > 0)
            {
                foreach (DataRow _row in ds.Tables[0].Rows)
                {
                    modelList.Add(DataRowToModel(_row));
                }
                return modelList;
            }
            else
            {
                return null;
            }
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ModelOld.M_Materials_Move2 GetModel(string M_Move_Detail_Code)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 M_Move_Code,Materials_Code,New_MaterialsStock_Code,Old_MaterialsStock_Code,M_Move_Detail_Code,MaterialsLot,MaterialsExpiryDate,Materials_Spec,M_Move_Num,M_Move_Price,M_Move_Money,M_MoveDetail_Memo from Materials_Move2 ");
            strSql.Append(" where M_Move_Detail_Code=@M_Move_Detail_Code ");
            SqlParameter[] parameters = {
                    new SqlParameter("@M_Move_Detail_Code", SqlDbType.Char,15)          };
            parameters[0].Value = M_Move_Detail_Code;

            ModelOld.M_Materials_Move2 model = new ModelOld.M_Materials_Move2();
            DataSet ds = HisVar.HisVar.Sqldal.Query(strSql.ToString(), parameters);
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }

        public ModelOld.M_Materials_Move2 GetModelByCondition(string strWhere)
        {

            StringBuilder strSql = new StringBuilder();
            strSql.Append("select  top 1 M_Move_Code, Materials_Code, New_MaterialsStock_Code, Old_MaterialsStock_Code, M_Move_Detail_Code, MaterialsLot, MaterialsExpiryDate,Materials_Spec, M_Move_Num, M_Move_Price, M_Move_Money, M_MoveDetail_Memo FROM dbo.Materials_Move2  ");

            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            ModelOld.M_Materials_Return2 model = new ModelOld.M_Materials_Return2();
            DataSet ds = HisVar.HisVar.Sqldal.Query(strSql.ToString());
            if (ds.Tables[0].Rows.Count > 0)
            {
                return DataRowToModel(ds.Tables[0].Rows[0]);
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public ModelOld.M_Materials_Move2 DataRowToModel(DataRow row)
        {
            ModelOld.M_Materials_Move2 model = new ModelOld.M_Materials_Move2();
            if (row != null)
            {
                if (row["M_Move_Code"] != null)
                {
                    model.M_Move_Code = row["M_Move_Code"].ToString();
                }
                if (row["Materials_Code"] != null)
                {
                    model.Materials_Code = row["Materials_Code"].ToString();
                }
                if (row["New_MaterialsStock_Code"] != null)
                {
                    model.New_MaterialsStock_Code = row["New_MaterialsStock_Code"].ToString();
                }
                if (row["Old_MaterialsStock_Code"] != null)
                {
                    model.Old_MaterialsStock_Code = row["Old_MaterialsStock_Code"].ToString();
                }
                if (row["M_Move_Detail_Code"] != null)
                {
                    model.M_Move_Detail_Code = row["M_Move_Detail_Code"].ToString();
                }
                if (row["MaterialsLot"] != null)
                {
                    model.MaterialsLot = row["MaterialsLot"].ToString();
                }
                if (row["Materials_Spec"] != null)
                {
                    model.Materials_Spec = row["Materials_Spec"].ToString();
                }
                if (row["MaterialsExpiryDate"] != null && row["MaterialsExpiryDate"].ToString() != "")
                {
                    model.MaterialsExpiryDate = DateTime.Parse(row["MaterialsExpiryDate"].ToString());
                }
                if (row["M_Move_Num"] != null && row["M_Move_Num"].ToString() != "")
                {
                    model.M_Move_Num = decimal.Parse(row["M_Move_Num"].ToString());
                }
                if (row["M_Move_Price"] != null && row["M_Move_Price"].ToString() != "")
                {
                    model.M_Move_Price = decimal.Parse(row["M_Move_Price"].ToString());
                }
                if (row["M_Move_Money"] != null && row["M_Move_Money"].ToString() != "")
                {
                    model.M_Move_Money = decimal.Parse(row["M_Move_Money"].ToString());
                }
                if (row["M_MoveDetail_Memo"] != null)
                {
                    model.M_MoveDetail_Memo = row["M_MoveDetail_Memo"].ToString();
                }
            }
            return model;
        }

        /// <summary>
        /// 获得数据列表
        /// </summary>
        public DataSet GetList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            //strSql.Append("   SELECT Materials_Move2.M_Move_Code, Materials_Move2.Materials_Code,Materials_Name, New_MaterialsStock_Code, Old_MaterialsStock_Code, M_Move_Detail_Code,");
            //strSql.Append(" Materials_Move2.MaterialsLot,Materials_Move2. MaterialsExpiryDate,Materials_Move2.Materials_Spec,Materials_Stock.MaterialsStore_Num Old_Num, M_Move_Num,ISNULL(b.MaterialsStore_Num,0) New_Num, M_Move_Price, M_Move_Money, M_MoveDetail_Memo");
            //strSql.Append("  FROM dbo.Materials_Move1 JOIN dbo.Materials_Move2 ON dbo.Materials_Move1.M_Move_Code = dbo.Materials_Move2.M_Move_Code");
            //strSql.Append("  JOIN dbo.Materials_Dict ON dbo.Materials_Move2.Materials_Code = dbo.Materials_Dict.Materials_Code");
            //strSql.Append("    JOIN dbo.Materials_Stock ON MaterialsStock_Code=Old_MaterialsStock_Code ");
            //strSql.Append("    LEFT JOIN dbo.Materials_Stock b ON b.MaterialsStock_Code=New_MaterialsStock_Code");
            strSql.Append("SELECT M_Move_Code, Materials_Move2.Materials_Code,Materials_Name, New_MaterialsStock_Code, Old_MaterialsStock_Code, M_Move_Detail_Code, Materials_Move2.MaterialsLot,Materials_Move2. MaterialsExpiryDate, M_Move_Num, M_Move_Price, M_Move_Money, M_MoveDetail_Memo,Materials_Move2. Materials_Spec,MaterialsStore_Num");
            strSql.Append(" FROM dbo.Materials_Move2 JOIN  dbo.Materials_Dict ON dbo.Materials_Move2.Materials_Code = dbo.Materials_Dict.Materials_Code");
            strSql.Append("        JOIN Materials_Stock ON Materials_Move2.Old_MaterialsStock_Code = Materials_Stock.MaterialsStock_Code ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

        /// <summary>
        /// 获得前几行数据
        /// </summary>
        public DataSet GetList(int Top, string strWhere, string filedOrder)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select ");
            if (Top > 0)
            {
                strSql.Append(" top " + Top.ToString());
            }
            strSql.Append(" M_Move_Code,Materials_Code,New_MaterialsStock_Code,Old_MaterialsStock_Code,M_Move_Detail_Code,MaterialsLot,MaterialsExpiryDate,Materials_Spec,M_Move_Num,M_Move_Price,M_Move_Money,M_MoveDetail_Memo ");
            strSql.Append(" FROM Materials_Move2 ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            strSql.Append(" order by " + filedOrder);
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

        /// <summary>
        /// 获取记录总数
        /// </summary>
        public int GetRecordCount(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) FROM Materials_Move2 ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" where " + strWhere);
            }
            object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return Convert.ToInt32(obj);
            }
        }
        /// <summary>
        /// 分页获取数据列表
        /// </summary>
        public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT * FROM ( ");
            strSql.Append(" SELECT ROW_NUMBER() OVER (");
            if (!string.IsNullOrEmpty(orderby.Trim()))
            {
                strSql.Append("order by T." + orderby);
            }
            else
            {
                strSql.Append("order by T.M_Move_Detail_Code desc");
            }
            strSql.Append(")AS Row, T.*  from Materials_Move2 T ");
            if (!string.IsNullOrEmpty(strWhere.Trim()))
            {
                strSql.Append(" WHERE " + strWhere);
            }
            strSql.Append(" ) TT");
            strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

        /*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Materials_Move2";
			parameters[1].Value = "M_Move_Detail_Code";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return HisVar.HisVar.Sqldal.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

        #endregion  BasicMethod
        #region  ExtensionMethod

        #endregion  ExtensionMethod
    }
}

