﻿/**  版本信息模板在安装目录下，可自行修改。
* M_Bl_TWD2.cs
*
* 功 能： N/A
* 类 名： M_Bl_TWD2
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/6/23 15:47:22   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace ModelOld
{
	/// <summary>
	/// M_Bl_TWD2:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class M_Bl_TWD2
	{
		public M_Bl_TWD2()
		{}
		#region Model
		private string _cl_code;
		private string _cl_time;
		private string _twbw;
		private decimal? _temperature;
		private int? _pulse;
        private int? _HeartRate;
		private int? _breath;
		private decimal? _wljw;
		private string _event;
		private string _eventtime;
		private string _specialevent;
		private bool _isfh;
		private bool _ishxj;
		private bool _isxzqbq;
		private string _jsr_code;
		private DateTime? _lr_date= DateTime.Now;
		/// <summary>
		/// 
		/// </summary>
		public string CL_Code
		{
			set{ _cl_code=value;}
			get{return _cl_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string  CL_Time
		{
			set{ _cl_time=value;}
			get{return _cl_time;}
		}
		/// <summary>
		/// 体温部位
		/// </summary>
		public string TWBW
		{
			set{ _twbw=value;}
			get{return _twbw;}
		}
		/// <summary>
		/// 
		/// </summary>
		public decimal? Temperature
		{
			set{ _temperature=value;}
			get{return _temperature;}
		}
		/// <summary>
		/// 
		/// </summary>
		public int? Pulse
		{
			set{ _pulse=value;}
			get{return _pulse;}
		}
        /// <summary>
        /// 
        /// </summary>
        public int? HeartRate
        {
            set { _HeartRate = value; }
            get { return _HeartRate; }
        }

		/// <summary>
		/// 
		/// </summary>
		public int? Breath
		{
			set{ _breath=value;}
			get{return _breath;}
		}
		/// <summary>
		/// 物理降温
		/// </summary>
		public decimal? WLJW
		{
			set{ _wljw=value;}
			get{return _wljw;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Event
		{
			set{ _event=value;}
			get{return _event;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string EventTime
		{
			set{ _eventtime=value;}
			get{return _eventtime;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string SpecialEvent
		{
			set{ _specialevent=value;}
			get{return _specialevent;}
		}
		/// <summary>
		/// 
		/// </summary>
		public bool isFH
		{
			set{ _isfh=value;}
			get{return _isfh;}
		}
		/// <summary>
		/// 
		/// </summary>
		public bool isHXJ
		{
			set{ _ishxj=value;}
			get{return _ishxj;}
		}
		/// <summary>
		/// 
		/// </summary>
		public bool isXZQBQ
		{
			set{ _isxzqbq=value;}
			get{return _isxzqbq;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Jsr_Code
		{
			set{ _jsr_code=value;}
			get{return _jsr_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public DateTime? Lr_Date
		{
			set{ _lr_date=value;}
			get{return _lr_date;}
		}
		#endregion Model

	}
}

