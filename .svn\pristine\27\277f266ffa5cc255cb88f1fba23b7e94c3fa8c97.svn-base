﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class EmrBasic1
    Inherits HisControl.BaseForm

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(EmrBasic1))
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.MyButton3 = New CustomControl.MyButton()
        Me.MyButton2 = New CustomControl.MyButton()
        Me.MyButton1 = New CustomControl.MyButton()
        Me.AddMyButton1 = New CustomControl.MyButton()
        Me.MyButton5 = New CustomControl.MyButton()
        Me.MyButton4 = New CustomControl.MyButton()
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel()
        Me.SplitContainer1 = New System.Windows.Forms.SplitContainer()
        Me.TableLayoutPanel2 = New System.Windows.Forms.TableLayoutPanel()
        Me.Panel2 = New System.Windows.Forms.Panel()
        Me.DoubleRadioButton2 = New System.Windows.Forms.RadioButton()
        Me.SingleRadioButton1 = New System.Windows.Forms.RadioButton()
        Me.ListTextBox1 = New C1.Win.C1Input.C1TextBox()
        Me.SplitContainer2 = New System.Windows.Forms.SplitContainer()
        Me.DefaultTextBox1 = New CustomControl.MyTextBox()
        Me.DataFieldComobo1 = New CustomControl.MyDtComobo()
        Me.Panel5 = New System.Windows.Forms.Panel()
        Me.TreeView1 = New System.Windows.Forms.TreeView()
        Me.Image1 = New System.Windows.Forms.ImageList(Me.components)
        Me.AddNode = New System.Windows.Forms.ToolStripMenuItem()
        Me.DeleNode = New System.Windows.Forms.ToolStripMenuItem()
        Me.UpdateNode = New System.Windows.Forms.ToolStripMenuItem()
        Me.InputButton = New System.Windows.Forms.ToolStripMenuItem()
        Me.DataFieldButton = New System.Windows.Forms.ToolStripMenuItem()
        Me.SelectButton = New System.Windows.Forms.ToolStripMenuItem()
        Me.ContextMenuStrip1 = New System.Windows.Forms.ContextMenuStrip(Me.components)
        Me.Panel1.SuspendLayout()
        Me.TableLayoutPanel1.SuspendLayout()
        Me.SplitContainer1.Panel1.SuspendLayout()
        Me.SplitContainer1.Panel2.SuspendLayout()
        Me.SplitContainer1.SuspendLayout()
        Me.TableLayoutPanel2.SuspendLayout()
        Me.Panel2.SuspendLayout()
        CType(Me.ListTextBox1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SplitContainer2.Panel1.SuspendLayout()
        Me.SplitContainer2.Panel2.SuspendLayout()
        Me.SplitContainer2.SuspendLayout()
        Me.Panel5.SuspendLayout()
        Me.ContextMenuStrip1.SuspendLayout()
        Me.SuspendLayout()
        '
        'Panel1
        '
        Me.Panel1.Controls.Add(Me.MyButton3)
        Me.Panel1.Controls.Add(Me.MyButton2)
        Me.Panel1.Controls.Add(Me.MyButton1)
        Me.Panel1.Controls.Add(Me.AddMyButton1)
        Me.Panel1.Dock = System.Windows.Forms.DockStyle.Top
        Me.Panel1.Location = New System.Drawing.Point(0, 0)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(992, 47)
        Me.Panel1.TabIndex = 1
        '
        'MyButton3
        '
        Me.MyButton3.ButtonImageSize = CustomControl.MyButton.imageSize.large
        Me.MyButton3.DialogResult = System.Windows.Forms.DialogResult.None
        Me.MyButton3.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MyButton3.Location = New System.Drawing.Point(244, 5)
        Me.MyButton3.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.MyButton3.Name = "MyButton3"
        Me.MyButton3.Size = New System.Drawing.Size(80, 37)
        Me.MyButton3.TabIndex = 3
        Me.MyButton3.Text = "查询"
        '
        'MyButton2
        '
        Me.MyButton2.ButtonImageSize = CustomControl.MyButton.imageSize.large
        Me.MyButton2.DialogResult = System.Windows.Forms.DialogResult.None
        Me.MyButton2.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MyButton2.Location = New System.Drawing.Point(164, 5)
        Me.MyButton2.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.MyButton2.Name = "MyButton2"
        Me.MyButton2.Size = New System.Drawing.Size(80, 37)
        Me.MyButton2.TabIndex = 2
        Me.MyButton2.Text = "修改"
        '
        'MyButton1
        '
        Me.MyButton1.ButtonImageSize = CustomControl.MyButton.imageSize.large
        Me.MyButton1.DialogResult = System.Windows.Forms.DialogResult.None
        Me.MyButton1.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MyButton1.Location = New System.Drawing.Point(84, 5)
        Me.MyButton1.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.MyButton1.Name = "MyButton1"
        Me.MyButton1.Size = New System.Drawing.Size(80, 37)
        Me.MyButton1.TabIndex = 1
        Me.MyButton1.Text = "删除"
        '
        'AddMyButton1
        '
        Me.AddMyButton1.ButtonImageSize = CustomControl.MyButton.imageSize.large
        Me.AddMyButton1.DialogResult = System.Windows.Forms.DialogResult.None
        Me.AddMyButton1.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.AddMyButton1.Location = New System.Drawing.Point(4, 5)
        Me.AddMyButton1.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.AddMyButton1.Name = "AddMyButton1"
        Me.AddMyButton1.Size = New System.Drawing.Size(80, 37)
        Me.AddMyButton1.TabIndex = 0
        Me.AddMyButton1.Text = "增加"
        '
        'MyButton5
        '
        Me.MyButton5.ButtonImageSize = CustomControl.MyButton.imageSize.large
        Me.MyButton5.DialogResult = System.Windows.Forms.DialogResult.None
        Me.MyButton5.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MyButton5.Location = New System.Drawing.Point(630, 7)
        Me.MyButton5.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.MyButton5.Name = "MyButton5"
        Me.MyButton5.Size = New System.Drawing.Size(80, 37)
        Me.MyButton5.TabIndex = 5
        Me.MyButton5.Text = "取消"
        '
        'MyButton4
        '
        Me.MyButton4.ButtonImageSize = CustomControl.MyButton.imageSize.large
        Me.MyButton4.DialogResult = System.Windows.Forms.DialogResult.None
        Me.MyButton4.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MyButton4.Location = New System.Drawing.Point(542, 7)
        Me.MyButton4.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.MyButton4.Name = "MyButton4"
        Me.MyButton4.Size = New System.Drawing.Size(80, 37)
        Me.MyButton4.TabIndex = 4
        Me.MyButton4.Text = "保存"
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.CellBorderStyle = System.Windows.Forms.TableLayoutPanelCellBorderStyle.[Single]
        Me.TableLayoutPanel1.ColumnCount = 2
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 250.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanel1.Controls.Add(Me.SplitContainer1, 1, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.Panel5, 1, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.TreeView1, 0, 0)
        Me.TableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(0, 47)
        Me.TableLayoutPanel1.Margin = New System.Windows.Forms.Padding(0)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.RowCount = 2
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 50.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 20.0!))
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(992, 455)
        Me.TableLayoutPanel1.TabIndex = 7
        '
        'SplitContainer1
        '
        Me.SplitContainer1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.SplitContainer1.Location = New System.Drawing.Point(255, 4)
        Me.SplitContainer1.Name = "SplitContainer1"
        '
        'SplitContainer1.Panel1
        '
        Me.SplitContainer1.Panel1.Controls.Add(Me.TableLayoutPanel2)
        '
        'SplitContainer1.Panel2
        '
        Me.SplitContainer1.Panel2.Controls.Add(Me.SplitContainer2)
        Me.SplitContainer1.Size = New System.Drawing.Size(733, 396)
        Me.SplitContainer1.SplitterDistance = 238
        Me.SplitContainer1.TabIndex = 13
        '
        'TableLayoutPanel2
        '
        Me.TableLayoutPanel2.ColumnCount = 1
        Me.TableLayoutPanel2.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanel2.Controls.Add(Me.Panel2, 0, 0)
        Me.TableLayoutPanel2.Controls.Add(Me.ListTextBox1, 0, 1)
        Me.TableLayoutPanel2.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TableLayoutPanel2.Location = New System.Drawing.Point(0, 0)
        Me.TableLayoutPanel2.Name = "TableLayoutPanel2"
        Me.TableLayoutPanel2.RowCount = 2
        Me.TableLayoutPanel2.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 50.0!))
        Me.TableLayoutPanel2.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanel2.Size = New System.Drawing.Size(238, 396)
        Me.TableLayoutPanel2.TabIndex = 8
        '
        'Panel2
        '
        Me.Panel2.Controls.Add(Me.DoubleRadioButton2)
        Me.Panel2.Controls.Add(Me.SingleRadioButton1)
        Me.Panel2.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel2.Location = New System.Drawing.Point(3, 3)
        Me.Panel2.Name = "Panel2"
        Me.Panel2.Size = New System.Drawing.Size(232, 44)
        Me.Panel2.TabIndex = 0
        '
        'DoubleRadioButton2
        '
        Me.DoubleRadioButton2.AutoSize = True
        Me.DoubleRadioButton2.Font = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.DoubleRadioButton2.Location = New System.Drawing.Point(137, 16)
        Me.DoubleRadioButton2.Name = "DoubleRadioButton2"
        Me.DoubleRadioButton2.Size = New System.Drawing.Size(81, 18)
        Me.DoubleRadioButton2.TabIndex = 1
        Me.DoubleRadioButton2.TabStop = True
        Me.DoubleRadioButton2.Text = "多选类型"
        Me.DoubleRadioButton2.UseVisualStyleBackColor = True
        '
        'SingleRadioButton1
        '
        Me.SingleRadioButton1.AutoSize = True
        Me.SingleRadioButton1.Font = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.SingleRadioButton1.Location = New System.Drawing.Point(18, 16)
        Me.SingleRadioButton1.Name = "SingleRadioButton1"
        Me.SingleRadioButton1.Size = New System.Drawing.Size(81, 18)
        Me.SingleRadioButton1.TabIndex = 0
        Me.SingleRadioButton1.TabStop = True
        Me.SingleRadioButton1.Text = "单选类型"
        Me.SingleRadioButton1.UseVisualStyleBackColor = True
        '
        'ListTextBox1
        '
        Me.ListTextBox1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.ListTextBox1.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.ListTextBox1.Location = New System.Drawing.Point(3, 53)
        Me.ListTextBox1.Multiline = True
        Me.ListTextBox1.Name = "ListTextBox1"
        Me.ListTextBox1.ScrollBars = System.Windows.Forms.ScrollBars.Both
        Me.ListTextBox1.Size = New System.Drawing.Size(232, 340)
        Me.ListTextBox1.TabIndex = 1
        Me.ListTextBox1.Tag = Nothing
        Me.ListTextBox1.TextDetached = True
        Me.ListTextBox1.Value = ""
        '
        'SplitContainer2
        '
        Me.SplitContainer2.Dock = System.Windows.Forms.DockStyle.Fill
        Me.SplitContainer2.Location = New System.Drawing.Point(0, 0)
        Me.SplitContainer2.Name = "SplitContainer2"
        '
        'SplitContainer2.Panel1
        '
        Me.SplitContainer2.Panel1.Controls.Add(Me.DefaultTextBox1)
        '
        'SplitContainer2.Panel2
        '
        Me.SplitContainer2.Panel2.Controls.Add(Me.DataFieldComobo1)
        Me.SplitContainer2.Size = New System.Drawing.Size(491, 396)
        Me.SplitContainer2.SplitterDistance = 183
        Me.SplitContainer2.TabIndex = 0
        '
        'DefaultTextBox1
        '
        Me.DefaultTextBox1.Captain = "默 认 值"
        Me.DefaultTextBox1.CaptainBackColor = System.Drawing.Color.Transparent
        Me.DefaultTextBox1.CaptainFont = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.DefaultTextBox1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.DefaultTextBox1.CaptainWidth = 70.0!
        Me.DefaultTextBox1.ContentForeColor = System.Drawing.Color.Black
        Me.DefaultTextBox1.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.DefaultTextBox1.Location = New System.Drawing.Point(3, 17)
        Me.DefaultTextBox1.Multiline = False
        Me.DefaultTextBox1.Name = "DefaultTextBox1"
        Me.DefaultTextBox1.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.DefaultTextBox1.ReadOnly = False
        Me.DefaultTextBox1.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.DefaultTextBox1.SelectionStart = 0
        Me.DefaultTextBox1.SelectStart = 0
        Me.DefaultTextBox1.Size = New System.Drawing.Size(300, 22)
        Me.DefaultTextBox1.TabIndex = 1
        Me.DefaultTextBox1.TextFont = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.DefaultTextBox1.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        '
        'DataFieldComobo1
        '
        Me.DataFieldComobo1.AutoSize = True
        Me.DataFieldComobo1.Captain = "数据字段"
        Me.DataFieldComobo1.CaptainFont = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.DataFieldComobo1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.DataFieldComobo1.CaptainWidth = 70.0!
        Me.DataFieldComobo1.DataSource = Nothing
        Me.DataFieldComobo1.ItemHeight = 16
        Me.DataFieldComobo1.ItemTextFont = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.DataFieldComobo1.Location = New System.Drawing.Point(8, 19)
        Me.DataFieldComobo1.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.DataFieldComobo1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.DataFieldComobo1.Name = "DataFieldComobo1"
        Me.DataFieldComobo1.ReadOnly = False
        Me.DataFieldComobo1.Size = New System.Drawing.Size(261, 20)
        Me.DataFieldComobo1.TabIndex = 1
        Me.DataFieldComobo1.TextFont = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'Panel5
        '
        Me.Panel5.Controls.Add(Me.MyButton5)
        Me.Panel5.Controls.Add(Me.MyButton4)
        Me.Panel5.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel5.Location = New System.Drawing.Point(252, 404)
        Me.Panel5.Margin = New System.Windows.Forms.Padding(0)
        Me.Panel5.Name = "Panel5"
        Me.Panel5.Size = New System.Drawing.Size(739, 50)
        Me.Panel5.TabIndex = 14
        '
        'TreeView1
        '
        Me.TreeView1.ContextMenuStrip = Me.ContextMenuStrip1
        Me.TreeView1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TreeView1.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.TreeView1.FullRowSelect = True
        Me.TreeView1.HotTracking = True
        Me.TreeView1.ImageIndex = 0
        Me.TreeView1.ImageList = Me.Image1
        Me.TreeView1.Location = New System.Drawing.Point(1, 1)
        Me.TreeView1.Margin = New System.Windows.Forms.Padding(0)
        Me.TreeView1.Name = "TreeView1"
        Me.TableLayoutPanel1.SetRowSpan(Me.TreeView1, 2)
        Me.TreeView1.SelectedImageIndex = 0
        Me.TreeView1.Size = New System.Drawing.Size(250, 453)
        Me.TreeView1.TabIndex = 5
        '
        'Image1
        '
        Me.Image1.ImageStream = CType(resources.GetObject("Image1.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.Image1.TransparentColor = System.Drawing.Color.White
        Me.Image1.Images.SetKeyName(0, "")
        Me.Image1.Images.SetKeyName(1, "")
        Me.Image1.Images.SetKeyName(2, "")
        Me.Image1.Images.SetKeyName(3, "Icon_1361.png")
        '
        'AddNode
        '
        Me.AddNode.Image = Global.ZtHis.Emr.My.Resources.Resources.增加
        Me.AddNode.Name = "AddNode"
        Me.AddNode.Size = New System.Drawing.Size(160, 22)
        Me.AddNode.Text = "增加目录"
        '
        'DeleNode
        '
        Me.DeleNode.AutoSize = False
        Me.DeleNode.Image = Global.ZtHis.Emr.My.Resources.Resources.删除
        Me.DeleNode.Name = "DeleNode"
        Me.DeleNode.Size = New System.Drawing.Size(160, 22)
        Me.DeleNode.Text = "删除"
        '
        'UpdateNode
        '
        Me.UpdateNode.AutoSize = False
        Me.UpdateNode.Image = Global.ZtHis.Emr.My.Resources.Resources.刷新
        Me.UpdateNode.Name = "UpdateNode"
        Me.UpdateNode.Size = New System.Drawing.Size(160, 22)
        Me.UpdateNode.Text = "修改"
        '
        'InputButton
        '
        Me.InputButton.Name = "InputButton"
        Me.InputButton.Size = New System.Drawing.Size(160, 22)
        Me.InputButton.Text = "增加输入类元素"
        '
        'DataFieldButton
        '
        Me.DataFieldButton.Name = "DataFieldButton"
        Me.DataFieldButton.Size = New System.Drawing.Size(160, 22)
        Me.DataFieldButton.Text = "增加数据类元素"
        '
        'SelectButton
        '
        Me.SelectButton.Name = "SelectButton"
        Me.SelectButton.Size = New System.Drawing.Size(160, 22)
        Me.SelectButton.Text = "增加选择类元素"
        '
        'ContextMenuStrip1
        '
        Me.ContextMenuStrip1.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.AddNode, Me.DeleNode, Me.UpdateNode, Me.InputButton, Me.DataFieldButton, Me.SelectButton})
        Me.ContextMenuStrip1.Name = "ContextMenuStrip1"
        Me.ContextMenuStrip1.Size = New System.Drawing.Size(161, 158)
        '
        'EmrBasic1
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(992, 502)
        Me.Controls.Add(Me.TableLayoutPanel1)
        Me.Controls.Add(Me.Panel1)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle
        Me.MinimizeBox = False
        Me.Name = "EmrBasic1"
        Me.ShowInTaskbar = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "基础设置"
        Me.Panel1.ResumeLayout(False)
        Me.TableLayoutPanel1.ResumeLayout(False)
        Me.SplitContainer1.Panel1.ResumeLayout(False)
        Me.SplitContainer1.Panel2.ResumeLayout(False)
        Me.SplitContainer1.ResumeLayout(False)
        Me.TableLayoutPanel2.ResumeLayout(False)
        Me.Panel2.ResumeLayout(False)
        Me.Panel2.PerformLayout()
        CType(Me.ListTextBox1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.SplitContainer2.Panel1.ResumeLayout(False)
        Me.SplitContainer2.Panel2.ResumeLayout(False)
        Me.SplitContainer2.Panel2.PerformLayout()
        Me.SplitContainer2.ResumeLayout(False)
        Me.Panel5.ResumeLayout(False)
        Me.ContextMenuStrip1.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    'Friend WithEvents C1Holder1 As C1.Win.C1Command.C1CommandHolder
    Friend WithEvents T_Label As C1.Win.C1Input.C1Label
    ' Friend WithEvents MyGrid1 As CustomControl.MyGrid
    Friend WithEvents ListBox2 As System.Windows.Forms.ListBox
    Friend WithEvents ComboBox2 As System.Windows.Forms.ComboBox
    Friend WithEvents AddMyButton1 As CustomControl.MyButton
    Friend WithEvents MyButton5 As CustomControl.MyButton
    Friend WithEvents MyButton4 As CustomControl.MyButton
    Friend WithEvents MyButton3 As CustomControl.MyButton
    Friend WithEvents MyButton2 As CustomControl.MyButton
    Friend WithEvents MyButton1 As CustomControl.MyButton
    Friend WithEvents TableLayoutPanel1 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents TreeView1 As System.Windows.Forms.TreeView
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents SplitContainer1 As System.Windows.Forms.SplitContainer
    Friend WithEvents TableLayoutPanel2 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents Panel2 As System.Windows.Forms.Panel
    Friend WithEvents DoubleRadioButton2 As System.Windows.Forms.RadioButton
    Friend WithEvents SingleRadioButton1 As System.Windows.Forms.RadioButton
    Friend WithEvents ListTextBox1 As C1.Win.C1Input.C1TextBox
    Friend WithEvents SplitContainer2 As System.Windows.Forms.SplitContainer
    Friend WithEvents DefaultTextBox1 As CustomControl.MyTextBox
    Friend WithEvents DataFieldComobo1 As CustomControl.MyDtComobo
    Friend WithEvents Panel5 As System.Windows.Forms.Panel
    Friend WithEvents ContextMenuStrip1 As System.Windows.Forms.ContextMenuStrip
    Friend WithEvents AddNode As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents DeleNode As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents UpdateNode As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents InputButton As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents DataFieldButton As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents SelectButton As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents Image1 As System.Windows.Forms.ImageList
End Class
