﻿Imports Stimulsoft.Report
Imports Stimulsoft.Report.Components
Public Class Yf_Cr_Tz
    Dim My_Date As String
    Dim My_Dataset As New DataSet

    Public Zb_Cm As CurrencyManager

    Dim V_Fiter As String
    Dim V_Yf_Code As String
    Dim V_Yf_Name As String
    Dim Yf_Sl As String

    Public Sub New(ByVal Yf_Code As String, ByVal Yf_Name As String)

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        V_Yf_Code = Yf_Code
        V_Yf_Name = Yf_Name
        ' 在 InitializeComponent() 调用之后添加任何初始化。

    End Sub

    Private Sub Yp_Cr_Tz_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Me.Text = V_Yf_Name & "出入库台账"
        Yf_Sl = "Yf_Sl" & Microsoft.VisualBasic.Right(V_Yf_Code, 1)

        V_Fiter = " and isnull(" & Yf_Sl & ",0)<>0"
        Call Init_Form()
        Call Init_Grid()
        Call Init_Data()
        Call F_Sum()

    End Sub

    Private Sub Init_Form()
        Dim My_Combo As New BaseClass.C_Combo1(Me.C1Combo1)
        With My_Combo
            .Init_TDBCombo()
            .AddItem("药品简称")
            .AddItem("药品名称")
            .SelectedIndex(0)
        End With
        C1Combo1.Width = 100
        Me.Panel2.Location = New Point(0, 0)
        My_Date = Date.Today
        With C1DateEdit1
            .Value = My_Date
            .DateTimeInput = True
            .AutoChangePosition = False
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .InitialSelection = C1.Win.C1Input.InitialSelectionEnum.CaretAtEnd
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .EditFormat.CustomFormat = "yyyy-MM-dd"
            .DisplayFormat.CustomFormat = "yyyy-MM-dd"
            .MaskInfo.AutoTabWhenFilled = True
            .AcceptsTab = True
            .EmptyAsNull = True
        End With
        With C1DateEdit2
            .Value = My_Date
            .DateTimeInput = True
            .AutoChangePosition = False
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .InitialSelection = C1.Win.C1Input.InitialSelectionEnum.CaretAtEnd
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .EditFormat.CustomFormat = "yyyy-MM-dd"
            .DisplayFormat.CustomFormat = "yyyy-MM-dd"
            .MaskInfo.AutoTabWhenFilled = True
            .AcceptsTab = True
            .EmptyAsNull = True
        End With

        Dim My_Combo2 As New BaseClass.C_Combo1(Me.C1Combo2)
        With My_Combo2
            .Init_TDBCombo()
            .AddItem("合计金额")
            .AddItem("药品级别")
            .AddItem("厂家级别")
            .SelectedIndex(0)
        End With
        C1Combo2.Width = 90
        RadioButton1.Checked = True
    End Sub

    Private Sub Init_Grid()
        Dim My_Grid1 As New BaseClass.C_Grid(Me.C1TrueDBGrid1)
        Dim My_Grid2 As New BaseClass.C_Grid(Me.C1TrueDBGrid2)
        With My_Grid1
            .Init_Grid()
            .Init_Column("", "Xx_Code", 0, "", "")
            .Init_Column("药品名称", "Yp_Name", 200, "左", "")
            .Init_Column("药品规格", "Mx_Gg", 100, "左", "")
            .Init_Column("单位", "Mx_XsDw", 45, "中", "")
            .Init_Column("产地", "Mx_Cd", 105, "左", "")
            .Init_Column("有效期", "Yp_Yxq", 70, "中", "yyyy-MM-dd")
        End With
        C1TrueDBGrid1.HScrollBar.Style = C1.Win.C1TrueDBGrid.ScrollBarStyleEnum.Automatic
        With My_Grid2
            .Init_Grid()
            .Init_Column("类别", "Lb", 85, "中", "")
            .Init_Column("日期", "Mx_Date", 70, "中", "yyyy-MM-dd")
            .Init_Column("数量", "Mx_Sl", 60, "右", "###0.####")
            .Init_Column("金额", "Mx_CgMoney", 65, "右", "###.00")
            .Init_Column("经手人", "Jsr_Name", 45, "左", "")

        End With
        C1TrueDBGrid2.HScrollBar.Style = C1.Win.C1TrueDBGrid.ScrollBarStyleEnum.Automatic


        C1TrueDBGrid1.Splits(0).HScrollBar.Style = C1.Win.C1TrueDBGrid.ScrollBarStyleEnum.Automatic
        C1TrueDBGrid2.Splits(0).HScrollBar.Style = C1.Win.C1TrueDBGrid.ScrollBarStyleEnum.Automatic
        C1TrueDBGrid2.ColumnFooters = True
        C1TrueDBGrid1.RecordSelectors = False
        C1TrueDBGrid2.RecordSelectors = False
    End Sub

    Public Overrides Sub F_Sum()
        If C1TrueDBGrid2.RowCount = 0 Then
            C1TrueDBGrid2.Columns(2).FooterText = 0

        Else
            C1TrueDBGrid2.Columns(2).FooterText = Format(My_Dataset.Tables("药品详情").Compute("Sum(Mx_Sl)", ""), "###0.####")
        End If
        C1TrueDBGrid1.Select()
    End Sub

    Private Sub Init_Data()

        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "select * from V_YpKc where Yy_Code='" & HisVar.HisVar.WsyCode & "'" & V_Fiter & "", "药品信息", True)

        Me.C1TrueDBGrid1.SetDataBinding(My_Dataset, "药品信息", True)
        Zb_Cm = CType(BindingContext(C1TrueDBGrid1.DataSource, C1TrueDBGrid1.DataMember), CurrencyManager)
    End Sub

    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click


        Dim Cx_Date1 As String
        Dim Cx_Date2 As String

        Cx_Date1 = Format(C1DateEdit1.Value, "yyyy-MM-dd 00:00:00")
        Cx_Date2 = Format(C1DateEdit2.Value, "yyyy-MM-dd 23:59:59")
        Dim V_Str As String

        V_Str = "select '1' as V_Order,'药库拨药'as Lb,Ck_Date Mx_Date,YK_Yf1.Ck_Code Mx_Ph,CK_SL*Yk_Yf2.Mx_Cfbl as  Mx_Sl,Ck_Xsj Mx_Cgj,Ck_Xsj*Ck_Sl Mx_CgMoney,Jsr_Name from Zd_YyJsr,V_YpKc,Yk_Yf1,Yk_Yf2 where Zd_YyJsr.Jsr_Code=Yk_Yf1.Jsr_Code and  Yk_Yf2.Xx_Code=V_YpKc.Xx_Code and Yk_Yf1.Ck_Code=Yk_Yf2.Ck_Code and V_YpKc.Xx_code='" & C1TrueDBGrid1.Columns("Xx_Code").Value & "' and Ck_Date between'" & Cx_Date1 & "' And '" & Cx_Date2 & "'  and Yk_Yf1.Yf_Code='" & V_Yf_Code & "' And Ck_Qr=1" &
      " union all" &
       " select '2' as V_Order,'药房采购'  ,Rk_Date,Yf_Rk1.Rk_Code,Rk_Sl,Rk_Dj,Rk_Dj*Rk_Sl,Jsr_Name from Yf_Rk1,Yf_Rk2,Zd_YyJsr where Yf_Rk1.Rk_Code=Yf_Rk2.Rk_Code and Xx_Code='" & C1TrueDBGrid1.Columns("Xx_Code").Value & "' and Rk_Date between'" & Cx_Date1 & "'and'" & Cx_Date2 & "' and Yf_Rk1.Jsr_Code=Zd_YyJsr.Jsr_Code  and Yf_Rk1.Yf_Code='" & V_Yf_Code & "' and Rk_Ok='已完成'" &
       " union all" &
       " select '3' as V_Order,'门诊销售'  ,Mz_Date,Mz.Mz_Code,-Mz_Sl,Mz_Dj,Mz_Dj*Mz_Sl,Jsr_Name from Mz,Mz_Yp,Zd_Yyjsr where Mz.Mz_Code=Mz_Yp.Mz_Code and Xx_code='" & C1TrueDBGrid1.Columns("Xx_Code").Value & "' and Mz_Date between'" & Cx_Date1 & "' And '" & Cx_Date2 & "' and Mz.Jsr_Code=Zd_Yyjsr.Jsr_Code  and Mz.Yf_Code='" & V_Yf_Code & "' and Mz_FyQr='1'" &
       " union all" &
       " select '4' as V_Order,'门诊销售'  ,Mz_Date,Mz_sum.Mz_Code,-Mz_Sl,Mz_Dj,Mz_Dj*Mz_Sl,Jsr_Name from Mz_sum,Mz_Yp_sum,Zd_Yyjsr where Mz_sum.Mz_Code=Mz_Yp_sum.Mz_Code and Xx_code='" & C1TrueDBGrid1.Columns("Xx_Code").Value & "' and Mz_Date between'" & Cx_Date1 & "' And '" & Cx_Date2 & "'and mz_sum.Jsr_Code=Zd_Yyjsr.Jsr_Code  and mz_sum.Yf_Code='" & V_Yf_Code & "' and Mz_FyQr='1'" &
       " union all" &
       " select '5' as V_Order,'住院销售'   ,Cf_Date ,Bl_Cf.Cf_Code,-Cf_Sl,Cf_Dj,Cf_Dj*Cf_Sl,Jsr_Name from Bl_Cf,Bl_CfYp,Zd_Yyjsr where Bl_Cf.Cf_Code=Bl_CfYp.Cf_Code and Xx_code='" & C1TrueDBGrid1.Columns("Xx_Code").Value & "' and Cf_date between'" & Cx_Date1 & "' And '" & Cx_Date2 & "'and Bl_Cf.Jsr_Code=Zd_Yyjsr.Jsr_Code  and Bl_Cf.Yf_Code='" & V_Yf_Code & "' and Cf_Qr='是'" &
       " union all" &
       " select '6' as V_Order,Ks_Name+'支领'  ,Ck_Date,Yf_Ks1.Ck_Code,-Ck_Sl,Ck_Xsj,Ck_Xsj*Ck_Sl,Jsr_Name from Yf_Ks1,Yf_Ks2,Zd_YyJsr,Zd_Yyks where Yf_Ks1.Ks_Code=Zd_YyKs.Ks_Code and Yf_Ks1.Ck_Code=Yf_Ks2.Ck_Code and Xx_Code='" & C1TrueDBGrid1.Columns("Xx_Code").Value & "' and Ck_Date between'" & Cx_Date1 & "'and'" & Cx_Date2 & "' and Yf_Ks1.Jsr_Code=Zd_YyJsr.Jsr_Code  and Yf_Ks1.Yf_Code='" & V_Yf_Code & "' and Ck_Ok='已完成'" &
       " union all" &
       " select '7' as V_Order,'批发'+Kh_Name  ,Ck_Date,Yf_Pf1.Ck_Code,-Ck_Sl,Ck_Dj,Ck_Dj*Ck_Sl,Jsr_Name from Yf_Pf1,Yf_Pf2,Zd_YyJsr where  Yf_Pf1.Ck_Code=Yf_Pf2.Ck_Code and Xx_Code='" & C1TrueDBGrid1.Columns("Xx_Code").Value & "' and Ck_Date between'" & Cx_Date1 & "'and'" & Cx_Date2 & "' and Yf_Pf1.Jsr_Code=Zd_YyJsr.Jsr_Code  and Yf_Pf1.Yf_Code='" & V_Yf_Code & "' and Ck_Ok='已完成'" &
       " union all" &
       " select '8' as V_Order,'药房盘点'   ,Pd_Date ,Pd_Month,Pd_Sl-Mx_Sl,Yk_CgDj,(Pd_Sl-Mx_Sl)*Yk_CgDj,Jsr_Name from Zd_YfPd,Zd_Yyjsr where Xx_code='" & C1TrueDBGrid1.Columns("Xx_Code").Value & "' and Pd_Date between'" & Cx_Date1 & "' And '" & Cx_Date2 & "' And Pd_Sl-Mx_Sl<>0 and Zd_YfPd.Jsr_Code=Zd_Yyjsr.Jsr_Code  and Zd_YfPd.Yf_Code='" & V_Yf_Code & "' and Pd_Wc='是'" &
       " union all" &
       " select '9' as V_Order,'药房退药库'   ,Tk_Date Mx_Date,Yf_tk1.tk_Code Mx_Ph,-tk_Sl Mx_Sl,tk_Dj Mx_CgDj,tk_Dj*tk_Sl Mx_CgMoney,Jsr_Name from Yf_tk1,Yf_tk2,Zd_Yyjsr where Yf_tk1.tk_Code=Yf_tk2.tk_Code and Xx_code='" & C1TrueDBGrid1.Columns("Xx_Code").Value & "' and tk_Date between'" & Cx_Date1 & "' And '" & Cx_Date2 & "' and yf_tk1.Jsr_Code=Zd_Yyjsr.Jsr_Code  and yf_tk1.Yf_Code='" & V_Yf_Code & "' And Tk_Qr=1" &
       " union all" &
       " select '10' as V_Order,'门诊退费'  ,Mz_Date,Mz.Mz_Code,Mz_Sl,Mz_Dj,Mz_Dj*Mz_Sl,Jsr_Name from Mz,Mz_Yp,Zd_Yyjsr,mz_ty where Mz.Mz_Code=Mz_Yp.Mz_Code  and Xx_code='" & C1TrueDBGrid1.Columns("Xx_Code").Value & "' and Mz_Date between'" & Cx_Date1 & "' And '" & Cx_Date2 & "' and mz_yp.mz_ph=mz_ty.mz_ph  and mz_ty.Mz_code=Mz.mz_Code  and mz_ty.Jsr_Code=Zd_Yyjsr.Jsr_Code  and Mz.Yf_Code='" & V_Yf_Code & "' AND Ty_Qr=1" &
       " union all" &
       " select '11' as V_Order,'门诊退费'  ,Mz_Date,Mz_sum.Mz_Code,Mz_Sl,Mz_Dj,Mz_Dj*Mz_Sl,Jsr_Name from Mz_sum,Mz_Yp_sum,Zd_Yyjsr,mz_ty_sum  where Mz_sum.Mz_Code=Mz_Yp_sum.Mz_Code  and Xx_code='" & C1TrueDBGrid1.Columns("Xx_Code").Value & "' and Mz_Date between'" & Cx_Date1 & "' And '" & Cx_Date2 & "' and mz_yp_sum.mz_ph =mz_ty_sum.mz_ph and mz_ty_sum.Mz_code=Mz_sum.mz_Code and mz_ty_sum.Jsr_Code=Zd_Yyjsr.Jsr_Code  and Mz_sum.Yf_Code='" & V_Yf_Code & "' AND Ty_Qr=1" &
        " union all" &
       " select '12' as V_Order,'报损报益'  ,BsBy_Date,BsBy_Code,BsBy_Sl,Yk_Cgj/Mx_Cfbl,Yk_Cgj/Mx_Cfbl*BsBy_Sl,Jsr_Name from Yf_Bsby,Zd_Yyjsr,V_Ypkc  where Yf_Bsby.Xx_Code=V_Ypkc.Xx_Code and Yf_Bsby.Xx_code='" & C1TrueDBGrid1.Columns("Xx_Code").Value & "' and BsBy_Date between'" & Cx_Date1 & "' And '" & Cx_Date2 & "'  and  Yf_Bsby.Jsr_Code=Zd_YyJsr.Jsr_Code  and Yf_Bsby.Yf_Code='" & V_Yf_Code & "'" &
       "  union all " &
       " select '13' as V_Order,'药房退供应商',  tk_Date,Yf_TkPf1.tk_Code,-tk_Sl,tk_Dj,tk_Dj*tk_Sl,Jsr_Name from Yf_TkPf1,Yf_TkPf2,Zd_YyJsr where Yf_TkPf1.tk_Code=Yf_TkPf2.tk_Code and tk_Date between'" & Cx_Date1 & "'and '" & Cx_Date2 & "'and Xx_Code='" & C1TrueDBGrid1.Columns("Xx_Code").Value & "' and Yf_TkPf1.Jsr_Code=Zd_YyJsr.Jsr_Code   and Ck_Ok='已完成' and Yf_TkPf1.Yf_Code='" & V_Yf_Code & "'  Order By V_Order,Ck_Date"

        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, V_Str, "药品详情", True)

        Me.C1TrueDBGrid2.SetDataBinding(My_Dataset, "药品详情", True)
        Call F_Sum()
    End Sub

    Private Sub C1TextBox1_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1TextBox1.TextChanged
        Dim My_View As New DataView
        My_View = Zb_Cm.List
        Dim V_Sort As String = ""
        Dim V_RowFilter As String = ""

        If Trim(Me.C1TextBox1.Text & "") = "" Then
            V_Sort = "Yp_Jc Asc"
            V_RowFilter = ""
        Else
            Select Case Me.C1Combo1.Text
                Case "药品简称"
                    V_Sort = "Yp_Jc Asc"
                    V_RowFilter = "Yp_Jc Like '*" & Trim(C1TextBox1.Text) & "*'"
                Case "药品名称"
                    V_Sort = "Yp_Name Asc"
                    V_RowFilter = "Yp_Name Like '*" & Trim(C1TextBox1.Text) & "*'"
            End Select
        End If

        My_View.Sort = V_Sort
        My_View.RowFilter = V_RowFilter

    End Sub

    Private Sub C1Combo1_Close(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Combo1.Close
        C1TextBox1.Select()
        Select Case Mid(Me.C1Combo1.Text, 3)
            Case "名称"
                InputLanguage.CurrentInputLanguage = HisVar.HisVar.InputCode
            Case "简称"
                InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages(0)
        End Select
    End Sub
    Private Sub CheckBox1_CheckedChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles CheckBox1.CheckedChanged
        If Me.CheckBox1.Checked = True Then
            V_Fiter = ""
            Call Init_Data()
        Else
            V_Fiter = " and isnull(" & Yf_Sl & ",0)<>0"
            Call Init_Data()
        End If

    End Sub

    Private Sub Button2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button2.Click
        Dim My_Dataset As New DataSet
        Dim Cx_Date1 As String = Format(C1DateEdit1.Value, "yyyy-MM-dd 00:00:00")
        Dim Cx_Date2 As String = Format(C1DateEdit2.Value, "yyyy-MM-dd 23:59:59")
        Dim Str As String = ""
        Dim Str0 As String = ""
        Dim Str1 As String = ""
        Dim Str2 As String = ""
        Dim GgCd As String = ""
        Dim Dj As String = ""
        Dim Dw As String = ""
        Dim mz_money As String = ""
        Dim mz_sum_money As String = ""
        Dim zy_money As String = ""
        If RadioButton1.Checked = True Then
            Dj = "Yk_Xsj"
            Dw = "Mx_XsDw"
            mz_money = "Mz_Yp.Mz_Money"
            mz_sum_money = "Mz_Yp_Sum.Mz_Money"
            zy_money = "Bl_Cfyp.Cf_Money"
        ElseIf RadioButton2.Checked = True Then
            Dj = "Yk_Cgj"
            Dw = "Mx_CgDw"
            mz_money = "(" & Dj & "/Mx_Cfbl)*Mz_Sl"
            mz_sum_money = mz_money
            zy_money = "(" & Dj & "/Mx_Cfbl)*Cf_Sl"
        End If
        If C1Combo2.Text = "合计金额" Then
            Str = "Yp_Code"
            Str0 = "Dl_Name,Dl_Code,"
            Str1 = "Dl_Name,Dl_Code,"
            Str2 = " group by Dl_Name,Dl_Code"
            GgCd = ",Dl_Name,Dl_Code"
        Else
            Str = "Mx_Code"
            Str0 = " " & Str & ",yp_name, Mx_Gg,  Mx_Cd," & Dw & "  Dw,Dl_Name,Dl_Code,"
            Str1 = "Kc." & Str & ",yp_name,Mx_Gg, Mx_Cd," & Dw & " ,Dl_Name,Dl_Code,"
            Str2 = " group by Kc." & Str & ",yp_name,Mx_Gg, Mx_Cd," & Dw & ",Dl_Name,Dl_Code"
            GgCd = ",Mx_Gg, Mx_Cd," & Dw & ",Dl_Name,Dl_Code"
      
        End If

        Dim V_Str As String = " select " & Str0 & "Mx_Money,YkYf_Money,Rk_Money,Mz_Money,BlCf_Money,Ks_Money,Pf_Money,Pd_Money,TkYk_Money,BsBy_Money,Tk_Money,Crk_Money,Yfsl,YkYfsl,RkSl,Mzsl,Blcfsl,Kssl,Pfsl,Pdsl,TkYksl,Bsbysl,Tksl,Crksl from (" &
" select " & Str1 & "sum(Mx_Money)Mx_Money,isnull(sum(YkYf_Money),0)YkYf_Money,isnull(sum(Rk_Money),0)Rk_Money,(isnull(sum(Mz_Money),0)+isnull(sum(MzSum_Money),0)-isnull(sum(MzTy_Money),0)-isnull(sum(MzSumTy_Money),0))Mz_Money,isnull(sum(BlCf_Money),0)BlCf_Money,isnull(sum(Ks_Money),0)Ks_Money,isnull(sum(Pf_Money),0)Pf_Money,isnull(sum(Pd_Money),0)Pd_Money,isnull(sum(TkYk_Money),0)TkYk_Money,isnull(sum(BsBy_Money),0)BsBy_Money,isnull(sum(Tk_Money),0)Tk_Money,(isnull(sum(YkYf_Money),0)+isnull(sum(Rk_Money),0)-(isnull(sum(Mz_Money),0)+isnull(sum(MzSum_Money),0)-isnull(sum(MzTy_Money),0)-isnull(sum(MzSumTy_Money),0))-isnull(sum(BlCf_Money),0)-isnull(sum(Ks_Money),0)-isnull(sum(Pf_Money),0)+isnull(sum(Pd_Money),0)-isnull(sum(TkYk_Money),0)+isnull(sum(BsBy_Money),0)-isnull(sum(Tk_Money),0))Crk_Money," &
" sum(Yfsl)Yfsl,isnull(sum(YkYfsl),0)YkYfsl,isnull(sum(RkSl),0)RkSl,(isnull(sum(Mzsl),0)+isnull(sum(MzSumsl),0)-isnull(sum(Mztysl),0)-isnull(sum(Mzsumtysl),0))Mzsl,isnull(sum(Blcfsl),0)Blcfsl,isnull(sum(Kssl),0)Kssl,isnull(sum(Pfsl),0)Pfsl,isnull(sum(Pdsl),0)Pdsl,isnull(sum(TkYksl),0)TkYksl,isnull(sum(Bsbysl),0)Bsbysl,isnull(sum(Tksl),0)Tksl,(isnull(sum(YkYfsl),0)+isnull(sum(RkSl),0)-(isnull(sum(Mzsl),0)+isnull(sum(MzSumsl),0)-isnull(sum(Mztysl),0)-isnull(sum(Mzsumtysl),0))-isnull(sum(Blcfsl),0)-isnull(sum(Kssl),0)-isnull(sum(Pfsl),0)+isnull(sum(Pdsl),0)-isnull(sum(TkYksl),0)+isnull(sum(Bsbysl),0)-isnull(sum(Tksl),0))Crksl from " &
"(select sum(" & Yf_Sl & ") Yfsl,sum((" & Dj & "/Mx_Cfbl)*" & Yf_Sl & ") Mx_Money," & Str & ",Yp_Name" & GgCd & "  from v_ypKc   group by " & Str & ",Yp_Name" & GgCd & ") Kc " &
" left join (select sum(CK_SL*Yk_Yf2.Mx_Cfbl)YkYfsl,sum(" & Dj & "*Ck_Sl)YkYf_Money,V_YpKc." & Str & " from Yk_Yf1,Yk_Yf2,V_YpKc where Yk_Yf1.Ck_Code=Yk_Yf2.Ck_Code  and Ck_Date between'" & Cx_Date1 & "' And '" & Cx_Date2 & "' and Yk_Yf2.xx_code=v_ypkc.xx_Code  and Yk_Yf1.Yf_Code='" & V_Yf_Code & "' And Ck_Qr=1 group by V_YpKc." & Str & ")YkYf on Kc." & Str & "=YkYf." & Str & "" &
" left join (select sum(Rk_Sl)RkSl,sum((" & Dj & "/Mx_Cfbl)*Rk_Sl)Rk_Money,V_YpKc." & Str & " from Yf_Rk1,Yf_Rk2,V_YpKc where Yf_Rk1.Rk_Code=Yf_Rk2.Rk_Code  and Rk_Date between'" & Cx_Date1 & "'and'" & Cx_Date2 & "' and Yf_Rk2.xx_code=v_ypkc.xx_Code  and Yf_Rk1.Yf_Code='" & V_Yf_Code & "' and Rk_Ok='已完成' group by v_ypKc." & Str & ")Rk on Rk." & Str & "=Kc." & Str & "" &
" left join (select sum(Mz_Sl)Mzsl,sum((" & Dj & "/Mx_Cfbl)*Mz_Sl)Mz_Money,V_YpKc." & Str & " from Mz,Mz_Yp,V_YpKc where Mz.Mz_Code=Mz_Yp.Mz_Code  and Mz_Date between'" & Cx_Date1 & "' And '" & Cx_Date2 & "' and Mz_yp.xx_code=v_ypkc.xx_Code  and Mz.Yf_Code='" & V_Yf_Code & "' and Mz_FyQr='1' group by v_ypKc." & Str & ")Mz on Mz." & Str & "=Kc." & Str & "" &
" left join (select sum(Mz_Sl)MzSumsl,sum((" & Dj & "/Mx_Cfbl)*Mz_Sl)MzSum_Money,V_YpKc." & Str & " from Mz_sum,Mz_Yp_sum,V_YpKc where Mz_sum.Mz_Code=Mz_Yp_sum.Mz_Code  and Mz_Date between'" & Cx_Date1 & "' And '" & Cx_Date2 & "'and mz_yp_sum.xx_code=v_ypkc.xx_Code  and mz_sum.Yf_Code='" & V_Yf_Code & "' and Mz_FyQr='1' group by v_ypKc." & Str & ")MzSum on MzSum." & Str & "=Kc." & Str & "" &
" left join (select sum(Cf_Sl)Blcfsl,sum((" & Dj & "/Mx_Cfbl)*Cf_Sl)BlCf_Money,V_YpKc." & Str & " from Bl_Cf,Bl_CfYp,V_YpKc where Bl_Cf.Cf_Code=Bl_CfYp.Cf_Code  and Cf_date between'" & Cx_Date1 & "' And '" & Cx_Date2 & "'and Bl_Cfyp.xx_code=v_ypkc.xx_Code  and Bl_Cf.Yf_Code='" & V_Yf_Code & "' and Cf_Qr='是' group by v_ypKc." & Str & ")BlCf on BlCf." & Str & "=Kc." & Str & "" &
" left join (select sum(Ck_Sl)Kssl,sum((" & Dj & "/Mx_Cfbl)*Ck_Sl)Ks_Money,V_YpKc." & Str & " from Yf_Ks1,Yf_Ks2,V_YpKc where  Yf_Ks1.Ck_Code=Yf_Ks2.Ck_Code  and Ck_Date between'" & Cx_Date1 & "'and'" & Cx_Date2 & "' and Yf_Ks2.xx_code=v_ypkc.xx_Code  and Yf_Ks1.Yf_Code='" & V_Yf_Code & "' and Ck_Ok='已完成' group by v_ypKc." & Str & ")Ks on Ks." & Str & "=Kc." & Str & "" &
" left join (select sum(Ck_Sl)Pfsl,sum((" & Dj & "/Mx_Cfbl)*Ck_Sl)Pf_Money,V_YpKc." & Str & " from Yf_Pf1,Yf_Pf2,V_YpKc where  Yf_Pf1.Ck_Code=Yf_Pf2.Ck_Code  and Ck_Date between'" & Cx_Date1 & "'and'" & Cx_Date2 & "' and Yf_Pf2.xx_code=v_ypkc.xx_Code  and Yf_Pf1.Yf_Code='" & V_Yf_Code & "' and Ck_Ok='已完成' group by v_ypKc." & Str & ")Pf on Pf." & Str & "=Kc." & Str & "" &
" left join (select sum(Pd_Sl-Mx_Sl)Pdsl,sum((Pd_Sl-Mx_Sl)*(" & Dj & "/Mx_Cfbl))Pd_Money,V_YpKc." & Str & " from Zd_YfPd,V_YpKc where  Pd_Date between'" & Cx_Date1 & "' And '" & Cx_Date2 & "' And Pd_Sl-Mx_Sl<>0 and Zd_YfPd.xx_code=v_ypkc.xx_Code  and Zd_YfPd.Yf_Code='" & V_Yf_Code & "' and Pd_Wc='是' group by v_ypKc." & Str & ")Pd on Pd." & Str & "=Kc." & Str & "" &
" left join (select sum(tk_Sl)TkYksl,sum((" & Dj & "/Mx_Cfbl)*tk_Sl)TkYk_Money,V_YpKc." & Str & " from Yf_tk1,Yf_tk2,V_YpKc where Yf_tk1.tk_Code=Yf_tk2.tk_Code  and tk_Date between'" & Cx_Date1 & "' And '" & Cx_Date2 & "' and yf_tk2.xx_code=v_ypkc.xx_Code  and yf_tk1.Yf_Code='" & V_Yf_Code & "' And Tk_Qr=1 group by v_ypKc." & Str & ")TkYk on TkYk." & Str & "=Kc." & Str & "" &
" left join (select sum(Mz_Sl)Mztysl,sum((" & Dj & "/Mx_Cfbl)*Mz_Sl)MzTy_Money,V_YpKc." & Str & " from Mz,Mz_Yp,mz_ty,V_YpKc where Mz.Mz_Code=Mz_Yp.Mz_Code   and Mz_Date between'" & Cx_Date1 & "' And '" & Cx_Date2 & "' and mz_yp.mz_ph=mz_ty.mz_ph  and mz_ty.Mz_code=Mz.mz_Code  and mz_yp.xx_code=v_ypkc.xx_Code  and Mz.Yf_Code='" & V_Yf_Code & "' group by v_ypKc." & Str & ")MzTy on MzTy." & Str & "=Kc." & Str & "" &
" left join (select sum(Mz_Sl)Mzsumtysl,sum((" & Dj & "/Mx_Cfbl)*Mz_Sl)MzSumTy_Money,V_YpKc." & Str & " from Mz_sum,Mz_Yp_sum,mz_ty_sum,V_YpKc  where Mz_sum.Mz_Code=Mz_Yp_sum.Mz_Code  and Mz_Date between'" & Cx_Date1 & "' And '" & Cx_Date2 & "' and mz_yp_sum.mz_ph =mz_ty_sum.mz_ph and mz_ty_sum.Mz_code=Mz_sum.mz_Code and mz_yp_sum.xx_code=v_ypkc.xx_Code  and Mz_sum.Yf_Code='" & V_Yf_Code & "'  group by v_ypKc." & Str & ")MzSumTy on MzSumTy." & Str & "=Kc." & Str & "" &
" left join (select sum(BsBy_Sl)Bsbysl,sum((" & Dj & "/Mx_Cfbl)*BsBy_Sl)BsBy_Money,V_YpKc." & Str & " from Yf_Bsby,V_Ypkc  where Yf_Bsby.Xx_Code=V_Ypkc.Xx_Code and BsBy_Date between'" & Cx_Date1 & "' And '" & Cx_Date2 & "'    and Yf_Bsby.Yf_Code='" & V_Yf_Code & "'  group by v_ypKc." & Str & ")BsBy on BsBy." & Str & "=Kc." & Str & "" &
" left join  (select sum(tk_Sl)Tksl,sum((" & Dj & "/Mx_Cfbl)*tk_Sl)Tk_Money,V_YpKc." & Str & " from Yf_TkPf1,Yf_TkPf2,V_YpKc where Yf_TkPf1.tk_Code=Yf_TkPf2.tk_Code and tk_Date between'" & Cx_Date1 & "'and '" & Cx_Date2 & "' and Yf_TkPf2.xx_code=v_ypkc.xx_Code   and Ck_Ok='已完成' and Yf_TkPf1.Yf_Code='" & V_Yf_Code & "'  group by v_ypKc." & Str & ")Tk on Tk." & Str & "=Kc." & Str & "" &
        "  " & Str2 & ") A where YkYf_Money<>0 or Rk_Money<>0 or Mz_Money<>0  or BlCf_Money<>0 or Ks_Money<>0 or Pf_Money<>0 or Pd_Money<>0 or TkYk_Money<>0 or BsBy_Money<>0 or Tk_Money<>0  order by Dl_Code"

        My_Dataset.EnforceConstraints = False
        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, V_Str, "药房台账统计", True)

        Dim Stirpt As New StiReport
        Stirpt.RegData(My_Dataset.Tables("药房台账统计"))
        Stirpt.Pages(0).PaperSize = Printing.PaperKind.Custom
        If C1Combo2.Text = "合计金额" Then
            Stirpt.Load(".\Rpt\药房台账汇总.mrt")
        Else
            Stirpt.Load(".\Rpt\药房台账统计.mrt")
        End If

        Stirpt.ReportName = "药房台账统计"
        If C1Combo2.Text = "药品级别" Then
            Dim groupBand As New StiGroupHeaderBand
            groupBand = Stirpt.GetComponents("GroupHeaderBand1")
            groupBand.Condition.Value = "{药房台账统计.yp_name}"
            groupBand.Height = 0
            TryCast(Stirpt.Pages(0).GetComponents.Item("Text26"), StiText).Text = ""
            TryCast(Stirpt.Pages(0).GetComponents.Item("Text28"), StiText).Text = ""

            Dim groupBand2 As New StiGroupHeaderBand
            groupBand2 = Stirpt.GetComponents("GroupHeaderBand2")
            groupBand2.Condition.Value = "{药房台账统计.yp_name}{药房台账统计.Dw}"
            groupBand2.Height = 0
            TryCast(Stirpt.Pages(1).GetComponents.Item("Text84"), StiText).Text = ""
            TryCast(Stirpt.Pages(1).GetComponents.Item("Text85"), StiText).Text = ""
        End If
        Stirpt.Compile()
        Stirpt("打印时间") = Format(Now, "yyyy-MM-dd HH:mm:ss")

        Stirpt("查询时间") = Cx_Date1 + "至" + Cx_Date2

        'Stirpt.Design()
        Stirpt.Show()
    End Sub
End Class