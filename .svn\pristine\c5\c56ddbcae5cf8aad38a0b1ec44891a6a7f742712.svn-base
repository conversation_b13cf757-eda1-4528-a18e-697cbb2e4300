﻿Public Class Fp_Tz
    Dim V_HKey As String = "HKEY_CURRENT_USER\中软智通"

    Private Sub Fp_Tz_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Call Form_Init()
    End Sub
    Protected Overrides Sub OnKeyDown(ByVal e As System.Windows.Forms.KeyEventArgs)
        MyBase.OnKeyDown(e)
        If e.Control = True And e.KeyCode = Keys.S Then
            C1Button1.Select()
            Call C1Button1_Click(C1Button1, Nothing)
        End If
    End Sub

    Private Sub Form_Init()
        With Me.C1NumericEdit1
            .Value = 0
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .CustomFormat = "###,###,###0.00##"
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .ShowContextMenu = False
            .EmptyAsNull = True
        End With

        With Me.C1NumericEdit2
            .Value = 0
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .CustomFormat = "###,###,###0.00##"
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .ShowContextMenu = False
            .EmptyAsNull = True
        End With

        With Me.C1NumericEdit3
            .Value = 0
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .CustomFormat = "###,###,###0.00##"
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .ShowContextMenu = False
            .EmptyAsNull = True
        End With

        With Me.C1NumericEdit4
            .Value = 0
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .CustomFormat = "###,###,###0.00##"
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .ShowContextMenu = False
            .EmptyAsNull = True
        End With

        With Me.C1NumericEdit5
            .Value = 0
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .CustomFormat = "###,###,###0.00##"
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .ShowContextMenu = False
            .EmptyAsNull = True
        End With

        With Me.C1NumericEdit6
            .Value = 0
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .CustomFormat = "###,###,###0.00##"
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .ShowContextMenu = False
            .EmptyAsNull = True
        End With

        With Me.C1NumericEdit7
            .Value = 0
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .CustomFormat = "###,###,###0.00##"
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .ShowContextMenu = False
            .EmptyAsNull = True
        End With

        With Me.NumGhTop
            .Value = 0
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .CustomFormat = "###,###,###0.00##"
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .ShowContextMenu = False
            .EmptyAsNull = True
        End With

        With Me.NumGhLeft
            .Value = 0
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .CustomFormat = "###,###,###0.00##"
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .ShowContextMenu = False
            .EmptyAsNull = True
        End With

        With My.Computer.Registry
            If .GetValue(V_HKey, "", Nothing) Is Nothing Then
                .CurrentUser.CreateSubKey("中软智通")
                .SetValue(V_HKey, "", "")
                .SetValue(V_HKey, "门诊上边距", "")
                .SetValue(V_HKey, "门诊下边距", "")
                .SetValue(V_HKey, "门诊左边距", "")
                .SetValue(V_HKey, "住院上边距", "")
                .SetValue(V_HKey, "住院左边距", "")
            Else
                Try
                    C1NumericEdit1.Value = .GetValue(V_HKey, "门诊上边距", Nothing) & ""
                    C1NumericEdit5.Value = .GetValue(V_HKey, "门诊下边距", Nothing) & ""
                    C1NumericEdit2.Value = .GetValue(V_HKey, "门诊左边距", Nothing) & ""
                    C1NumericEdit3.Value = .GetValue(V_HKey, "住院上边距", Nothing) & ""
                    C1NumericEdit4.Value = .GetValue(V_HKey, "住院左边距", Nothing) & ""
                Catch ex As Exception
                End Try
            End If
        End With
        C1NumericEdit6.Value = iniOperate.iniopreate.GetINI("套打边距", "押金上边距", "", HisVar.HisVar.Parapath & "\Config.ini")
        C1NumericEdit7.Value = iniOperate.iniopreate.GetINI("套打边距", "押金左边距", "", HisVar.HisVar.Parapath & "\Config.ini")

        NumGhTop.Value = iniOperate.iniopreate.GetINI("套打边距", "挂号上边距", "", Common.WinFormVar.Var.ParaPath & "\Config.ini")
        NumGhLeft.Value = iniOperate.iniopreate.GetINI("套打边距", "挂号左边距", "", Common.WinFormVar.Var.ParaPath & "\Config.ini")
    End Sub

    Private Sub C1Button2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Button2.Click
        Me.Close()
    End Sub

    Private Sub C1Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Button1.Click
        With My.Computer.Registry
            .SetValue(V_HKey, "门诊上边距", C1NumericEdit1.Value)
            .SetValue(V_HKey, "门诊下边距", C1NumericEdit5.Value)
            .SetValue(V_HKey, "门诊左边距", C1NumericEdit2.Value)
            .SetValue(V_HKey, "住院上边距", C1NumericEdit3.Value)
            .SetValue(V_HKey, "住院左边距", C1NumericEdit4.Value)
        End With
        MsgBox("数据保存成功!", MsgBoxStyle.Information, "提示")

        iniOperate.iniopreate.WriteINI("套打边距", "押金上边距", IIf(C1NumericEdit6.Value Is DBNull.Value, 0, C1NumericEdit6.Value), HisVar.HisVar.Parapath & "\Config.ini")
        iniOperate.iniopreate.WriteINI("套打边距", "押金左边距", IIf(C1NumericEdit7.Value Is DBNull.Value, 0, C1NumericEdit7.Value), HisVar.HisVar.Parapath & "\Config.ini")

        iniOperate.iniopreate.WriteINI("套打边距", "挂号上边距", IIf(NumGhTop.Value Is DBNull.Value, 0, NumGhTop.Value), Common.WinFormVar.Var.ParaPath & "\Config.ini")
        iniOperate.iniopreate.WriteINI("套打边距", "挂号左边距", IIf(NumGhLeft.Value Is DBNull.Value, 0, NumGhLeft.Value), Common.WinFormVar.Var.ParaPath & "\Config.ini")
    End Sub
End Class