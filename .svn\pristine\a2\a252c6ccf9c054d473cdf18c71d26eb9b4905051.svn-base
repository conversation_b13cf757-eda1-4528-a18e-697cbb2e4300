﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class Zd_MzFp13
    Inherits HisControl.BaseChild

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(Zd_MzFp13))
        Me.C1Holder1 = New C1.Win.C1Command.C1CommandHolder
        Me.Move1 = New C1.Win.C1Command.C1Command
        Me.Move2 = New C1.Win.C1Command.C1Command
        Me.Move3 = New C1.Win.C1Command.C1Command
        Me.Move4 = New C1.Win.C1Command.C1Command
        Me.Move5 = New C1.Win.C1Command.C1Command
        Me.Control1 = New C1.Win.C1Command.C1CommandControl
        Me.Control2 = New C1.Win.C1Command.C1CommandControl
        Me.ToolTip1 = New System.Windows.Forms.ToolTip(Me.components)
        Me.Comm1 = New System.Windows.Forms.Button
        Me.Comm2 = New System.Windows.Forms.Button
        Me.C1Combo1 = New C1.Win.C1List.C1Combo
        Me.Label12 = New System.Windows.Forms.Label
        Me.Label01 = New System.Windows.Forms.Label
        Me.Label9 = New System.Windows.Forms.Label
        Me.Panel1 = New System.Windows.Forms.Panel
        CType(Me.C1Holder1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.C1Combo1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Panel1.SuspendLayout()
        Me.SuspendLayout()
        '
        'C1Holder1
        '
        Me.C1Holder1.Commands.Add(Me.Move1)
        Me.C1Holder1.Commands.Add(Me.Move2)
        Me.C1Holder1.Commands.Add(Me.Move3)
        Me.C1Holder1.Commands.Add(Me.Move4)
        Me.C1Holder1.Commands.Add(Me.Move5)
        Me.C1Holder1.Commands.Add(Me.Control1)
        Me.C1Holder1.Commands.Add(Me.Control2)
        Me.C1Holder1.Owner = Me
        '
        'Move1
        '
        Me.Move1.Image = CType(resources.GetObject("Move1.Image"), System.Drawing.Image)
        Me.Move1.Name = "Move1"
        Me.Move1.Shortcut = System.Windows.Forms.Shortcut.F2
        Me.Move1.Text = "最前"
        Me.Move1.ToolTipText = "移到最前记录"
        '
        'Move2
        '
        Me.Move2.Image = CType(resources.GetObject("Move2.Image"), System.Drawing.Image)
        Me.Move2.Name = "Move2"
        Me.Move2.Shortcut = System.Windows.Forms.Shortcut.F3
        Me.Move2.Text = "上移"
        Me.Move2.ToolTipText = "上移记录"
        '
        'Move3
        '
        Me.Move3.Image = CType(resources.GetObject("Move3.Image"), System.Drawing.Image)
        Me.Move3.Name = "Move3"
        Me.Move3.Shortcut = System.Windows.Forms.Shortcut.F4
        Me.Move3.Text = "下移"
        Me.Move3.ToolTipText = "下移记录"
        '
        'Move4
        '
        Me.Move4.Image = CType(resources.GetObject("Move4.Image"), System.Drawing.Image)
        Me.Move4.Name = "Move4"
        Me.Move4.Shortcut = System.Windows.Forms.Shortcut.F5
        Me.Move4.Text = "最后"
        Me.Move4.ToolTipText = "移至最后记录"
        '
        'Move5
        '
        Me.Move5.Image = CType(resources.GetObject("Move5.Image"), System.Drawing.Image)
        Me.Move5.Name = "Move5"
        Me.Move5.Shortcut = System.Windows.Forms.Shortcut.F6
        Me.Move5.Text = "新增"
        '
        'Control1
        '
        Me.Control1.HotFrame = C1.Win.C1Command.HotFrameEnum.None
        Me.Control1.Name = "Control1"
        Me.Control1.ShowShortcut = False
        Me.Control1.ShowTextAsToolTip = False
        '
        'Control2
        '
        Me.Control2.HotFrame = C1.Win.C1Command.HotFrameEnum.None
        Me.Control2.Name = "Control2"
        Me.Control2.ShowShortcut = False
        Me.Control2.ShowTextAsToolTip = False
        '
        'Comm1
        '
        Me.Comm1.Location = New System.Drawing.Point(82, 3)
        Me.Comm1.Name = "Comm1"
        Me.Comm1.Size = New System.Drawing.Size(50, 24)
        Me.Comm1.TabIndex = 0
        Me.Comm1.Tag = "保存"
        Me.Comm1.Text = "保存           &S"
        Me.ToolTip1.SetToolTip(Me.Comm1, "数据存盘(&S)")
        Me.Comm1.UseVisualStyleBackColor = False
        '
        'Comm2
        '
        Me.Comm2.DialogResult = DialogResult.Cancel
        Me.Comm2.Location = New System.Drawing.Point(132, 3)
        Me.Comm2.Name = "Comm2"
        Me.Comm2.Size = New System.Drawing.Size(50, 24)
        Me.Comm2.TabIndex = 143
        Me.Comm2.Tag = "取消"
        Me.Comm2.Text = "取消             &C"
        Me.ToolTip1.SetToolTip(Me.Comm2, "取消存盘并退出(&C)")
        Me.Comm2.UseVisualStyleBackColor = False
        '
        'C1Combo1
        '
        Me.C1Combo1.AddItemSeparator = Global.Microsoft.VisualBasic.ChrW(59)
        Me.C1Combo1.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.C1Combo1.Caption = ""
        Me.C1Combo1.CaptionHeight = 17
        Me.C1Combo1.CharacterCasing = System.Windows.Forms.CharacterCasing.Normal
        Me.C1Combo1.ColumnCaptionHeight = 18
        Me.C1Combo1.ColumnFooterHeight = 18
        Me.C1Combo1.ContentHeight = 16
        Me.C1Combo1.DeadAreaBackColor = System.Drawing.Color.Empty
        Me.C1Combo1.EditorBackColor = System.Drawing.SystemColors.Window
        Me.C1Combo1.EditorFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.C1Combo1.EditorForeColor = System.Drawing.SystemColors.WindowText
        Me.C1Combo1.EditorHeight = 16
        Me.C1Combo1.Images.Add(CType(resources.GetObject("C1Combo1.Images"), System.Drawing.Image))
        Me.C1Combo1.ItemHeight = 15
        Me.C1Combo1.Location = New System.Drawing.Point(82, 26)
        Me.C1Combo1.MatchEntryTimeout = CType(2000, Long)
        Me.C1Combo1.MaxDropDownItems = CType(5, Short)
        Me.C1Combo1.MaxLength = 32767
        Me.C1Combo1.MouseCursor = System.Windows.Forms.Cursors.Default
        Me.C1Combo1.Name = "C1Combo1"
        Me.C1Combo1.RowDivider.Color = System.Drawing.Color.DarkGray
        Me.C1Combo1.RowDivider.Style = C1.Win.C1List.LineStyleEnum.None
        Me.C1Combo1.RowSubDividerColor = System.Drawing.Color.DarkGray
        Me.C1Combo1.ScrollTips = True
        Me.C1Combo1.Size = New System.Drawing.Size(156, 16)
        Me.C1Combo1.SuperBack = True
        Me.C1Combo1.TabIndex = 0
        Me.C1Combo1.PropBag = resources.GetString("C1Combo1.PropBag")
        '
        'Label12
        '
        Me.Label12.BackColor = System.Drawing.SystemColors.Info
        Me.Label12.Location = New System.Drawing.Point(82, 69)
        Me.Label12.Name = "Label12"
        Me.Label12.Size = New System.Drawing.Size(156, 16)
        Me.Label12.TabIndex = 235
        Me.Label12.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'Label01
        '
        Me.Label01.AutoSize = True
        Me.Label01.ForeColor = System.Drawing.Color.DarkRed
        Me.Label01.Location = New System.Drawing.Point(26, 28)
        Me.Label01.Name = "Label01"
        Me.Label01.Size = New System.Drawing.Size(53, 12)
        Me.Label01.TabIndex = 233
        Me.Label01.Text = "简    称"
        '
        'Label9
        '
        Me.Label9.AutoSize = True
        Me.Label9.Location = New System.Drawing.Point(26, 69)
        Me.Label9.Name = "Label9"
        Me.Label9.Size = New System.Drawing.Size(53, 12)
        Me.Label9.TabIndex = 234
        Me.Label9.Text = "名    称"
        '
        'Panel1
        '
        Me.Panel1.Controls.Add(Me.Comm1)
        Me.Panel1.Controls.Add(Me.Comm2)
        Me.Panel1.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.Panel1.Location = New System.Drawing.Point(0, 192)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(265, 27)
        Me.Panel1.TabIndex = 236
        '
        'Zd_MzFp13
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.CancelButton = Me.Comm2
        Me.ClientSize = New System.Drawing.Size(265, 219)
        Me.Controls.Add(Me.Panel1)
        Me.Controls.Add(Me.C1Combo1)
        Me.Controls.Add(Me.Label12)
        Me.Controls.Add(Me.Label01)
        Me.Controls.Add(Me.Label9)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle
        Me.Icon = CType(resources.GetObject("$this.Icon"), System.Drawing.Icon)
        Me.MaximizeBox = False
        Me.Name = "Zd_MzFp13"
        Me.ShowInTaskbar = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "类别明细"
        CType(Me.C1Holder1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.C1Combo1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Panel1.ResumeLayout(False)
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents C1Holder1 As C1.Win.C1Command.C1CommandHolder
    Friend WithEvents Move1 As C1.Win.C1Command.C1Command
    Friend WithEvents Move2 As C1.Win.C1Command.C1Command
    Friend WithEvents Move3 As C1.Win.C1Command.C1Command
    Friend WithEvents Move4 As C1.Win.C1Command.C1Command
    Friend WithEvents Move5 As C1.Win.C1Command.C1Command
    Friend WithEvents Control1 As C1.Win.C1Command.C1CommandControl
    Friend WithEvents ToolTip1 As System.Windows.Forms.ToolTip
    Friend WithEvents Control2 As C1.Win.C1Command.C1CommandControl
    Friend WithEvents Comm2 As System.Windows.Forms.Button
    Friend WithEvents Comm1 As System.Windows.Forms.Button
    Friend WithEvents C1Combo1 As C1.Win.C1List.C1Combo
    Friend WithEvents Label12 As System.Windows.Forms.Label
    Friend WithEvents Label01 As System.Windows.Forms.Label
    Friend WithEvents Label9 As System.Windows.Forms.Label
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
End Class
