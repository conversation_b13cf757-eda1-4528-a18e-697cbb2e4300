﻿Imports C1.Win.C1FlexGrid
Imports System.Drawing

Public Class C_Flex
    Private My_Flex As C1.Win.C1FlexGrid.C1FlexGrid
    Private My_View As DataView

    Public Sub New(ByVal Flex As C1.Win.C1FlexGrid.C1FlexGrid, ByVal View As DataView) ' 定义一个公用构造函数来初始化C1TrueDBGrid
        My_Flex = Flex
        My_View = View
    End Sub

    Public Sub Init_Flex()
        With My_Flex
            .Clear()
            .AllowDelete = False
            .AllowEditing = False
            .AutoResize = False
            .AllowSorting = AllowSortingEnum.SingleColumn
            .AllowDragging = AllowDraggingEnum.Columns
            .AllowResizing = C1.Win.C1FlexGrid.AllowResizingEnum.Both
            .AllowMerging = C1.Win.C1FlexGrid.AllowMergingEnum.None
            .BorderStyle = C1.Win.C1FlexGrid.Util.BaseControls.BorderStyleEnum.Fixed3D

            .ExtendLastCol = True
            .FocusRect = FocusRectEnum.None
            .SelectionMode = SelectionModeEnum.ListBox
            With .Tree
                .Column = 0
                .Indent = 20
                .Style = TreeStyleFlags.CompleteLeaf
                .LineColor = Color.DarkRed
                .LineStyle = Drawing2D.DashStyle.Solid
            End With

            '类型()
            With .Styles.Fixed
                .WordWrap = False
                .Border.Style = C1.Win.C1FlexGrid.BorderStyleEnum.Raised
                .TextAlign = TextAlignEnum.CenterCenter
                .Margins.Top = 1
                .Margins.Bottom = 0
                '.BackColor = Color.FromArgb(0, 78, 152)
                '.ForeColor = Color.FromArgb(255, 255, 255)
            End With

            With .Styles.Highlight
                '.ForeColor = Color.FromArgb(255, 255, 255)
                '.BackColor = Color.FromArgb(49, 106, 197)
            End With

            .Rows(0).Height = 20
            .Redraw = True
            .DataSource = My_View
        End With
    End Sub

    Public Sub Init_Column(ByVal V_标题 As String, ByVal V_字段 As String, ByVal V_长度 As Integer, ByVal V_水平 As String, ByVal V_格式 As String)
        With My_Flex.Cols(V_字段)
            .Caption = V_标题
            .Width = V_长度
            .AllowMerging = True
            .AllowDragging = True
            If V_水平 = "左" Then
                .TextAlign = C1.Win.C1FlexGrid.TextAlignEnum.LeftCenter
            ElseIf V_水平 = "中" Then
                .TextAlign = C1.Win.C1FlexGrid.TextAlignEnum.CenterCenter
            ElseIf V_水平 = "右" Then
                .TextAlign = C1.Win.C1FlexGrid.TextAlignEnum.RightCenter
            End If
            .Format = V_格式
            .Style.WordWrap = True
            .Sort = C1.Win.C1FlexGrid.SortFlags.UseColSort
            If V_长度 = 0 Then
                .Visible = False
            Else
                .Visible = True
            End If
        End With
    End Sub
End Class
