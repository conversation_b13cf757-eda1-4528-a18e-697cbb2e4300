﻿Imports System.Data.SqlClient
Imports BaseClass


Public Class Zd_MzFp11

#Region "变量初始化"

    Public My_Dataset As New DataSet
    Dim My_View As New DataView                 '视图
    Dim V_Finish As Boolean = False             '初始化完成

    Public My_Adapter1 As New SqlDataAdapter
    Public My_Adapter2 As New SqlDataAdapter
    Public My_Table As New DataTable            '药品小类
    Public My_Cm As CurrencyManager             '同步指针
    Public My_Row As DataRow                    '当前选择行
    Public V_Insert As Boolean                  '增加记录
    Public V_FirstLoad As Boolean               '第一次调入明细表
    Public V_Count As Integer                   '节点数量
    Public V_Fl_Code As String
#End Region

    Private Sub Zd_MzFp11_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Call Form_Init()
        Call Init_Tree()
        Call Init_Data2()
        Call Init_Data1()
    End Sub

#Region "窗体初始化"

    Private Sub Form_Init()
        Panel1.BorderStyle = BorderStyle.None
        Panel1.Height = 25
        ToolBar1.Location = New Point(1, 1)
        T_Line1.Location = New Point(ToolBar1.Width + 2, 0)

        Call Init_TDBGrid1()
    End Sub
    'Zd_MzFp12
    Private Sub Init_TDBGrid1()
        '初始化TDBGrid
        Dim My_Grid As New C_Grid(Me.C1TrueDBGrid1)
        With My_Grid
            .Init_Grid()
            .P_Dock(C_Grid.Dock.Fill)
            .Init_Column("编码", "Fl_Code", 60, "中", "")
            .Init_Column("科室类别", "Fl_Name", 100, "左", "")
            .Init_Column("类别简称", "Fl_Jc", 60, "左", "")
            .Init_Column("备注", "Fl_Memo", 80, "左", "")
            .AllSort(False)
        End With
    End Sub
    'Zd_MzFp15
    Private Sub Init_TDBGrid2()
        Dim My_Grid As New C_Grid(Me.C1TrueDBGrid1)
        With My_Grid
            .Init_Grid()
            .P_Dock(C_Grid.Dock.Fill)
            .Init_Column("编码", "Mc_Code", 0, "中", "")
            .Init_Column("名称", "Mc_Name", 150, "左", "")
            .Init_Column("简称", "Mc_Jc", 120, "左", "")
            .Init_Column("明细", "", 1, "中", "")

        End With
        With C1TrueDBGrid1.Splits(0).DisplayColumns(3)
            .ButtonAlways = True
            .Button = True
            .ButtonText = True

        End With
    End Sub
    Private Sub Init_Tree()
        V_Finish = False

        With Me.TreeView1
            .Nodes.Clear()
            .FullRowSelect = True
            .HideSelection = False
            .HotTracking = True
            .Scrollable = False
            .ShowRootLines = False
            .Sorted = False
        End With

        '根节点
        Dim My_Reader As SqlDataReader
        My_Reader = HisVar.HisVar.Sqldal.ExecuteReader("select count(Mc_Code) as Mc_Count From Zd_MzFp31 where Yy_Code='" & HisVar.HisVar.WsyCode & "' ")
        My_Reader.Read()
        V_Count = My_Reader.Item(0)


        Dim My_Root As New TreeNode
        With My_Root
            .Tag = "0"
            .Text = "科室类别(" + V_Count.ToString + ")"
            .ImageIndex = 0
        End With
        My_Reader.Close()
        TreeView1.Nodes.Add(My_Root)

        '一级数据
        My_Reader = HisVar.HisVar.Sqldal.ExecuteReader("select Zd_MzFp3.Fl_Code,Fl_Name, isnull(Mc_Count,0) as Mc_Count   from Zd_MzFp3 left join (select Fl_Code,  count(*)   as Mc_Count from Zd_MzFp31 Where Yy_Code='" & HisVar.HisVar.WsyCode & "' group by Fl_Code ) Xm_Sl on Zd_MzFp3.Fl_Code=Xm_Sl.Fl_Code where Yy_Code='" & HisVar.HisVar.WsyCode & "'")

        While My_Reader.Read()
            Dim My_Node As New TreeNode
            With My_Node
                .Tag = My_Reader.Item(0).ToString
                .Text = My_Reader.Item("Fl_Name").ToString + "(" + My_Reader.Item("Mc_Count").ToString + ")"
                .ImageIndex = 1
                .SelectedImageIndex = 2
            End With
            TreeView1.SelectedNode = My_Root
            TreeView1.SelectedNode.Nodes.Add(My_Node)
        End While
        My_Reader.Close()
        My_Cn.Close()

        With TreeView1
            .SelectedNode = TreeView1.TopNode
            .SelectedNode.Expand()
            .Select()
        End With
        V_Finish = True

    End Sub

    Private Sub Init_Data1()

        Dim Str_Select As String = "Select Yy_Code,Fl_Code,Fl_Name,Fl_Jc,Fl_Memo From Zd_MzFp3 where Yy_Code='" & HisVar.HisVar.WsyCode & "'order  By Fl_Code"
        Dim Str_Update As String = "Update Zd_MzFp3 Set Yy_Code=@Yy_Code,Fl_Code=@Fl_Code,Fl_Name=@Fl_Name,Fl_Jc=@Fl_Jc,Fl_Memo=@Fl_Memo Where Fl_Code=@Old_Fl_Code"
        Dim Str_Insert As String = "Insert Into Zd_MzFp3(Yy_Code,Fl_Code,Fl_Name,Fl_Jc,Fl_Memo)Values(@Yy_Code,@Fl_Code,@Fl_Name,@Fl_Jc,@Fl_Memo)"
        Dim Str_Delete As String = "Delete From Zd_MzFp3 Where Fl_Code=@Fl_Code"

        With My_Adapter1
            .UpdateCommand = New SqlCommand(Str_Update, My_Cn)
            With .UpdateCommand.Parameters
                .Add("@Yy_Code", SqlDbType.Char, 4, "Yy_Code")
                .Add("@Fl_Code", SqlDbType.Char, 6, "Fl_Code")
                .Add("@Fl_Name", SqlDbType.VarChar, 100, "Fl_Name")
                .Add("@Fl_Jc", SqlDbType.VarChar, 50, "Fl_Jc")
                .Add("@Fl_Memo", SqlDbType.VarChar, 200, "Fl_Memo")
                .Add("@Old_Fl_Code", SqlDbType.Char, 6, "Fl_Code")
            End With

            .InsertCommand = New SqlCommand(Str_Insert, My_Cn)
            With .InsertCommand.Parameters
                .Add("@Yy_Code", SqlDbType.Char, 4, "Yy_Code")
                .Add("@Fl_Code", SqlDbType.Char, 6, "Fl_Code")
                .Add("@Fl_Name", SqlDbType.VarChar, 100, "Fl_Name")
                .Add("@Fl_Jc", SqlDbType.VarChar, 50, "Fl_Jc")
                .Add("@Fl_Memo", SqlDbType.VarChar, 200, "Fl_Memo")
            End With

            .DeleteCommand = New SqlCommand(Str_Delete, My_Cn)
            With .DeleteCommand.Parameters
                .Add("@Fl_Code", SqlDbType.Char, 6, "Fl_Code")
            End With

            .SelectCommand = New SqlCommand(Str_Select, My_Cn)
            .Fill(My_Dataset, "科室类别")

            .AcceptChangesDuringFill = True
            .MissingSchemaAction = MissingSchemaAction.AddWithKey

        End With

        My_Table = My_Dataset.Tables("科室类别")
        My_Table.PrimaryKey = New DataColumn() {My_Table.Columns("Fl_Code")}

        With Me.C1TrueDBGrid1
            My_Cm = CType(BindingContext(My_Dataset, "科室类别"), CurrencyManager)
            .SetDataBinding(My_Dataset, "科室类别", True)


        End With

        My_View = My_Cm.List

    End Sub

    Private Sub Init_Data2()
        Dim Str_Insert As String = "Insert Into Zd_MzFp31(Yy_Code,Mc_Code,Fl_Code,Mc_Name,Mc_Jc,Mc_Memo)Values(@Yy_Code,@Mc_Code,@Fl_Code,@Mc_Name,@Mc_Jc,@Mc_Memo)"
        Dim Str_Update As String = "Update Zd_MzFp31 Set Yy_Code=@Yy_Code,Mc_Code=@Mc_Code,Fl_Code=@Fl_Code,Mc_Name=@Mc_Name,Mc_Jc=@Mc_Jc,Mc_Memo=@Mc_Memo Where Mc_Code=@Old_Mc_Code and Yy_Code='" & HisVar.HisVar.WsyCode & "'"
        Dim Str_Delete As String = "Delete From Zd_MzFp31 Where Mc_Code=@Mc_Code and Yy_Code='" & HisVar.HisVar.WsyCode & "'"

        With My_Adapter2
            .UpdateCommand = New SqlCommand(Str_Update, My_Cn)
            With .UpdateCommand.Parameters
                .Add("@Yy_Code", SqlDbType.Char, 4)
                .Add("@Mc_Code", SqlDbType.Char, 8)
                .Add("@Fl_Code", SqlDbType.Char, 6)
                .Add("@Mc_Name", SqlDbType.VarChar, 50)
                .Add("@Mc_Jc", SqlDbType.VarChar, 50)
                .Add("@Mc_Memo", SqlDbType.VarChar, 200)
                .Add("@Old_Mc_Code", SqlDbType.VarChar, 8, "Mc_Code")
            End With

            .InsertCommand = New SqlCommand(Str_Insert, My_Cn)
            With .InsertCommand.Parameters
                .Add("@Yy_Code", SqlDbType.Char, 4)
                .Add("@Mc_Code", SqlDbType.Char, 8)
                .Add("@Fl_Code", SqlDbType.Char, 6)
                .Add("@Mc_Name", SqlDbType.VarChar, 50)
                .Add("@Mc_Jc", SqlDbType.VarChar, 50)
                .Add("@Mc_Memo", SqlDbType.VarChar, 200)


            End With

            .DeleteCommand = New SqlCommand(Str_Delete, My_Cn)
            With .DeleteCommand.Parameters
                .Add("@Mc_Code", SqlDbType.VarChar, 50)
            End With
        End With
    End Sub

    Private Sub P_Init_Data1()
        Dim Str_Select As String = "Select Fl_Code,Fl_Name,Fl_Jc,Fl_Memo From Zd_MzFp3 Where Yy_Code='" & HisVar.HisVar.WsyCode & "'Order By Fl_Code"

        With My_Adapter1
            .SelectCommand = New SqlCommand(Str_Select, My_Cn)
            If My_Dataset.Tables("科室类别") IsNot Nothing Then My_Dataset.Tables("科室类别").Clear()
            .Fill(My_Dataset, "科室类别")
            .AcceptChangesDuringFill = True
            .MissingSchemaAction = MissingSchemaAction.AddWithKey
        End With

        My_Table = My_Dataset.Tables("科室类别")
        My_Table.PrimaryKey = New DataColumn() {My_Table.Columns("Fl_Code")}

        With Me.C1TrueDBGrid1
            My_Cm = CType(BindingContext(My_Dataset, "科室类别"), CurrencyManager)
            .SetDataBinding(My_Dataset, "科室类别", True)

            My_View = My_Cm.List

        End With


    End Sub

    Private Sub P_Init_Data2(ByVal V_Lb_Code As String, ByVal V_All As Boolean)
        Dim Str_Select As String = ""

        If V_All = True Then
            Str_Select = "Select Zd_MzFp31.Yy_Code,Zd_MzFp31.Mc_Code,Fl_Code,Mc_Name,Mc_Jc,Mc_Memo From Zd_MzFp31  where  Zd_MzFp31.Yy_Code='" & HisVar.HisVar.WsyCode & "' "

            'Str_Select = "Select Zd_MzFp4.Yy_Code,Fl_Code,Zd_MzFp4.Lb_Code,Lb_Name,Lb_Jc From Zd_MzFp4 inner join Zd_MzFp1 on Zd_MzFp4.Lb_Code=Zd_MzFp1.Lb_Code and  Zd_MzFp4.Yy_Code='" & HisVar.HisVar.WsyCode & "' UNION ALL Select Zd_MzFp4.Yy_Code,Fl_Code,Zd_MzFp4.Lb_Code,Dl_Name,Dl_Jc From Zd_MzFp4 inner join Zd_ML_Yp1 on Zd_MzFp4.Lb_Code='Y'+Zd_ML_Yp1.Dl_Code and  Zd_MzFp4.Yy_Code='" & HisVar.HisVar.WsyCode & "' Order By Zd_MzFp4.Lb_Code"
        Else
            Str_Select = "Select Zd_MzFp31.Yy_Code,Zd_MzFp31.Mc_Code,Fl_Code,Mc_Name,Mc_Jc,Mc_Memo From Zd_MzFp31   Where Fl_Code='" & V_Lb_Code & "'  and  Zd_MzFp31.Yy_Code='" & HisVar.HisVar.WsyCode & "' Order By Zd_MzFp31.Mc_Code"

        End If

        With My_Adapter2
            .SelectCommand = New SqlCommand(Str_Select, My_Cn)
            If My_Dataset.Tables("项目小类") IsNot Nothing Then My_Dataset.Tables("项目小类").Clear()
            .Fill(My_Dataset, "项目小类")
            .AcceptChangesDuringFill = True
            .MissingSchemaAction = MissingSchemaAction.AddWithKey
        End With

        My_Table = My_Dataset.Tables("项目小类")
        My_Table.PrimaryKey = New DataColumn() {My_Table.Columns("Mc_Code")}

        With Me.C1TrueDBGrid1
            My_Cm = CType(BindingContext(My_Dataset, "项目小类"), CurrencyManager)
            .SetDataBinding(My_Dataset, "项目小类", True)

            My_View = My_Cm.List

        End With


    End Sub

    Private Sub Zd_MzFp1_FormClosing(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosingEventArgs) Handles Me.FormClosing
        My_Dataset.Dispose()
    End Sub

#End Region

#Region "控件__动作"

    Private Sub Comm_Click(ByVal sender As Object, ByVal e As C1.Win.C1Command.ClickEventArgs) Handles Comm1.Click, Comm2.Click, Comm3.Click
        Select Case sender.text
            Case "增加"
                If TreeView1.SelectedNode.Tag = "0" Then
                    Dim My_Reader As SqlDataReader
                    My_Reader = HisVar.HisVar.Sqldal.ExecuteReader("select  count(*)    from Zd_MzFp3  Where Yy_Code='" & HisVar.HisVar.WsyCode & "' ")
                    While My_Reader.Read
                        If My_Reader.Item(0) = 3 Then
                            MsgBox("最多填写三个类别", MsgBoxStyle.Information + MsgBoxStyle.OkOnly, "提示:")
                            My_Reader.Close()
                            Exit Sub
                        End If
                    End While
                    My_Reader.Close()
                    Call P_Conn(False)
                Else
                    Dim My_Reader As SqlDataReader
                    My_Reader = HisVar.HisVar.Sqldal.ExecuteReader("select  count(*)    from Zd_MzFp31  Where Zd_MzFp31.Yy_Code='" & HisVar.HisVar.WsyCode & "' and Zd_MzFp31.Fl_Code='" & TreeView1.SelectedNode.Tag & "' ")
                    While My_Reader.Read
                        If My_Reader.Item(0) = 2 Then
                            MsgBox("最多填写两个类别", MsgBoxStyle.Information + MsgBoxStyle.OkOnly, "提示:")
                            My_Reader.Close()

                            Exit Sub
                        End If
                    End While
                    My_Reader.Close()
                    Call P_Conn(False)
                End If
                Call P_ShowMx("增加")

            Case "删除"
                Beep()
                If C1TrueDBGrid1.Splits(0).Rows.Count = 0 Then Exit Sub
                Call P_Del_Data()

            Case "更新"
                My_Table.AcceptChanges()
                If TreeView1.SelectedNode.Tag = "0" Then
                    My_Adapter1.Fill(My_Dataset, "科室类别")    '刷新记录()
                Else
                    My_Adapter2.Fill(My_Dataset, "项目小类")    '刷新记录
                End If

                C1TrueDBGrid1.Select()
                C1TrueDBGrid1.MoveFirst()
                Call Init_Tree()
                Call Init_TDBGrid1()
                Call Init_Data1()




        End Select
    End Sub

    Private Sub C1TrueDBGrid1_ButtonClick(ByVal sender As Object, ByVal e As C1.Win.C1TrueDBGrid.ColEventArgs) Handles C1TrueDBGrid1.ButtonClick
        'MessageBox.Show(My_Row.Item("Mc_Code"))
        If e.ColIndex = 3 Then
            If Zd_MzFp14 Is Nothing Then                      '窗体没有调入
                V_FirstLoad = True
                Zd_MzFp14.Owner = Me
            End If
            Zd_MzFp14.ShowDialog()
        End If
    End Sub

    Private Sub C1TrueDBGrid1_MouseUp(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles C1TrueDBGrid1.MouseUp
        If e.Button = MouseButtons.Right Then Call P_ShowMx("DBGrid")
    End Sub

    Private Sub C1TrueDBGrid1_KeyDown(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles C1TrueDBGrid1.KeyDown
        Select Case e.KeyCode
            Case Keys.Return
                Call P_ShowMx("DBGrid")
            Case Keys.Delete
                If Me.C1TrueDBGrid1.RowCount > 0 Then Call P_Del_Data()
            Case Keys.Insert
                If TreeView1.SelectedNode.Tag = "0" Then
                    Dim My_Reader As SqlDataReader
                    My_Reader = HisVar.HisVar.Sqldal.GetSingle("select  count(*)    from Zd_MzFp3  Where Yy_Code='" & HisVar.HisVar.WsyCode & "' ")
                    While My_Reader.Read
                        If My_Reader.Item(0) = 3 Then
                            MsgBox("最多填写三个类别", MsgBoxStyle.Information + MsgBoxStyle.OkOnly, "提示:")
                            My_Reader.Close()
                            Call P_Conn(False)
                            Exit Sub
                        End If
                    End While
                    My_Reader.Close()
                    Call P_Conn(False)
                End If
                Call P_ShowMx("增加")
        End Select
    End Sub

    Private Sub TreeView1_AfterSelect(ByVal sender As System.Object, ByVal e As System.Windows.Forms.TreeViewEventArgs) Handles TreeView1.AfterSelect
        If V_Finish = False Then Exit Sub '     '初始化完成
        If Me.TreeView1.SelectedNode.Tag = "0" Then
            Call Init_TDBGrid1()
            Call P_Init_Data1()
        Else

            Call Init_TDBGrid2()
            Call P_Init_Data2(Me.TreeView1.SelectedNode.Tag, False)
        End If
        If C1TrueDBGrid1.RowCount > 0 Then
            My_Row = My_Cm.List(C1TrueDBGrid1.Row).Row
        End If
    End Sub

    Private Sub TreeView1_BeforeCollapse(ByVal sender As Object, ByVal e As System.Windows.Forms.TreeViewCancelEventArgs) Handles TreeView1.BeforeCollapse
        '如果为TopNode不进行折叠
        If e.Node.Tag = TreeView1.TopNode.Tag Then e.Cancel = True
    End Sub

    Dim m_Rc As New BaseClass.C_RowChange

    Private Sub C1TrueDBGrid1_RowColChange(ByVal sender As System.Object, ByVal e As C1.Win.C1TrueDBGrid.RowColChangeEventArgs) Handles C1TrueDBGrid1.RowColChange
        If (C1TrueDBGrid1.Row + 1) > C1TrueDBGrid1.RowCount Then

        Else
            My_Row = My_Cm.List(C1TrueDBGrid1.Row).Row
            m_Rc.ChangeRow(My_Row)
        End If
    End Sub

#End Region

#Region "自定义函数"

    Private Sub P_ShowMx(ByVal V_Lb As String)
        If V_Lb = "增加" Then
            V_Insert = True
        Else
            If C1TrueDBGrid1.RowCount > 0 Then V_Insert = False Else V_Insert = True
        End If
        If TreeView1.SelectedNode.Tag = "0" Then
            Dim vform As New Zd_MzFp12(V_Insert, My_Row, My_Table, C1TrueDBGrid1, My_Adapter1, m_Rc, TreeView1)
            If BaseFunc.BaseFunc.CheckOwnForm(Me, vform) = False Then
                vform.Owner = Me
                vform.Show()
            End If
        Else
            V_Fl_Code = TreeView1.SelectedNode.Tag
            Dim vform As New Zd_MzFp15(V_Insert, My_Row, My_Table, C1TrueDBGrid1, My_Adapter2, m_Rc, TreeView1)
            If BaseFunc.BaseFunc.CheckOwnForm(Me, vform) = False Then
                vform.Owner = Me
                vform.Show()
            End If
        End If
        C1TrueDBGrid1.Select()

    End Sub

    Private Sub P_Del_Data()
        Beep()
        Dim V_Del_Node As String = ""
        If TreeView1.SelectedNode.Tag = "0" Then
            If MsgBox("是否删除:科室类别=" + Me.C1TrueDBGrid1.Columns("Fl_Name").Value + " ", MsgBoxStyle.Information + MsgBoxStyle.OkCancel, "提示:") = MsgBoxResult.Cancel Then Exit Sub
            My_Row = My_Cm.List(C1TrueDBGrid1.Row).Row
            V_Del_Node = My_Row.Item("Fl_Code")
            My_Adapter1.DeleteCommand.Parameters(0).Value = My_Row.Item("Fl_Code")
        Else
            If MsgBox("是否删除:类别=" + Me.C1TrueDBGrid1.Columns("Mc_Name").Value + " ", MsgBoxStyle.Information + MsgBoxStyle.OkCancel, "提示:") = MsgBoxResult.Cancel Then Exit Sub
            My_Row = My_Cm.List(C1TrueDBGrid1.Row).Row
            My_Adapter2.DeleteCommand.Parameters(0).Value = My_Row.Item("Mc_Code")
        End If
        Call P_Conn(True)
        Try
            If TreeView1.SelectedNode.Tag = "0" Then
                My_Adapter1.DeleteCommand.ExecuteNonQuery()
                C1TrueDBGrid1.Delete()
                My_Row.AcceptChanges()

                Call P_Delete_Node(V_Del_Node)
            Else
                My_Adapter2.DeleteCommand.ExecuteNonQuery()
                C1TrueDBGrid1.Delete()
                My_Row.AcceptChanges()

                Call P_Del_Tree()
            End If

        Catch ex As Exception
            Beep()
            MsgBox("请先删除相关数据！", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
            'MsgBox("错误:" + ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
        Finally
            Call P_Conn(False)
            C1TrueDBGrid1.Select()
        End Try

    End Sub

    Private Sub P_Del_Tree()                                                                    '修改Treeview节点
        V_Count = V_Count - 1
        TreeView1.TopNode.Text = "科室类别(" + V_Count.ToString + ")"

        Dim V_NodeText As String = TreeView1.SelectedNode.Text                                  '当前选中的节点
        V_NodeText = Mid(V_NodeText, 1, InStr(V_NodeText, "(") - 1)
        TreeView1.SelectedNode.Text = V_NodeText + "(" + C1TrueDBGrid1.Splits(0).Rows.Count.ToString + ")"
    End Sub



#End Region

    Private Sub P_Delete_Node(ByVal V_Str As String)  '删除Treeview的节点
        TreeView1.SelectedNode = TreeView1.TopNode()
        Dim My_Node As TreeNode
        For Each My_Node In TreeView1.SelectedNode.Nodes
            If My_Node.Tag = V_Str Then
                TreeView1.Nodes.Remove(My_Node)
                Exit For
            End If
        Next
    End Sub

End Class