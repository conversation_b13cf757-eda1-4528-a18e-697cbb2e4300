﻿
<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class Zd_ShikongTwo
    Inherits HisControl.BaseChild

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(Zd_ShikongTwo))
        Me.C1Holder1 = New C1.Win.C1Command.C1CommandHolder()
        Me.Move1 = New C1.Win.C1Command.C1Command()
        Me.Move2 = New C1.Win.C1Command.C1Command()
        Me.Move3 = New C1.Win.C1Command.C1Command()
        Me.Move4 = New C1.Win.C1Command.C1Command()
        Me.Move5 = New C1.Win.C1Command.C1Command()
        Me.Control1 = New C1.Win.C1Command.C1CommandControl()
        Me.T_Label1 = New C1.Win.C1Input.C1Label()
        Me.Control2 = New C1.Win.C1Command.C1CommandControl()
        Me.T_Label2 = New C1.Win.C1Input.C1Label()
        Me.Control3 = New C1.Win.C1Command.C1CommandControl()
        Me.T_Label3 = New C1.Win.C1Input.C1Label()
        Me.Control4 = New C1.Win.C1Command.C1Command()
        Me.ToolBar1 = New C1.Win.C1Command.C1ToolBar()
        Me.Link6 = New C1.Win.C1Command.C1CommandLink()
        Me.Link1 = New C1.Win.C1Command.C1CommandLink()
        Me.Link2 = New C1.Win.C1Command.C1CommandLink()
        Me.Link7 = New C1.Win.C1Command.C1CommandLink()
        Me.Link3 = New C1.Win.C1Command.C1CommandLink()
        Me.Link4 = New C1.Win.C1Command.C1CommandLink()
        Me.Link5 = New C1.Win.C1Command.C1CommandLink()
        Me.Link8 = New C1.Win.C1Command.C1CommandLink()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.Comm2 = New CustomControl.MyButton()
        Me.Comm1 = New CustomControl.MyButton()
        Me.T_Line3 = New System.Windows.Forms.Label()
        Me.ToolTip1 = New System.Windows.Forms.ToolTip(Me.components)
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel()
        Me.skCode_TextBox = New CustomControl.MyTextBox()
        Me.dayNumericEdit = New CustomControl.MyNumericEdit()
        Me.hourNumericEdit = New CustomControl.MyNumericEdit()
        Me.Memo_TextBox = New CustomControl.MyTextBox()
        Me.mblbMyDtComobo = New CustomControl.MyDtComobo()
        Me.mbMyDtComobo = New CustomControl.MyDtComobo()
        Me.ylsjMySingleComobo = New CustomControl.MySingleComobo()
        Me.T_Line1 = New System.Windows.Forms.Label()
        CType(Me.C1Holder1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T_Label1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T_Label2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.T_Label3, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.ToolBar1.SuspendLayout()
        Me.Panel1.SuspendLayout()
        Me.TableLayoutPanel1.SuspendLayout()
        Me.SuspendLayout()
        '
        'C1Holder1
        '
        Me.C1Holder1.Commands.Add(Me.Move1)
        Me.C1Holder1.Commands.Add(Me.Move2)
        Me.C1Holder1.Commands.Add(Me.Move3)
        Me.C1Holder1.Commands.Add(Me.Move4)
        Me.C1Holder1.Commands.Add(Me.Move5)
        Me.C1Holder1.Commands.Add(Me.Control1)
        Me.C1Holder1.Commands.Add(Me.Control2)
        Me.C1Holder1.Commands.Add(Me.Control3)
        Me.C1Holder1.Commands.Add(Me.Control4)
        Me.C1Holder1.Owner = Me
        Me.C1Holder1.VisualStyle = C1.Win.C1Command.VisualStyle.Office2010Blue
        '
        'Move1
        '
        Me.Move1.Image = CType(resources.GetObject("Move1.Image"), System.Drawing.Image)
        Me.Move1.Name = "Move1"
        Me.Move1.Shortcut = System.Windows.Forms.Shortcut.F2
        Me.Move1.ShortcutText = ""
        Me.Move1.Text = "最前"
        Me.Move1.ToolTipText = "移到最前记录"
        '
        'Move2
        '
        Me.Move2.Image = CType(resources.GetObject("Move2.Image"), System.Drawing.Image)
        Me.Move2.Name = "Move2"
        Me.Move2.Shortcut = System.Windows.Forms.Shortcut.F3
        Me.Move2.ShortcutText = ""
        Me.Move2.Text = "上移"
        Me.Move2.ToolTipText = "上移记录"
        '
        'Move3
        '
        Me.Move3.Image = CType(resources.GetObject("Move3.Image"), System.Drawing.Image)
        Me.Move3.Name = "Move3"
        Me.Move3.Shortcut = System.Windows.Forms.Shortcut.F4
        Me.Move3.ShortcutText = ""
        Me.Move3.Text = "下移"
        Me.Move3.ToolTipText = "下移记录"
        '
        'Move4
        '
        Me.Move4.Image = CType(resources.GetObject("Move4.Image"), System.Drawing.Image)
        Me.Move4.Name = "Move4"
        Me.Move4.Shortcut = System.Windows.Forms.Shortcut.F5
        Me.Move4.ShortcutText = ""
        Me.Move4.Text = "最后"
        Me.Move4.ToolTipText = "移至最后记录"
        '
        'Move5
        '
        Me.Move5.Image = CType(resources.GetObject("Move5.Image"), System.Drawing.Image)
        Me.Move5.Name = "Move5"
        Me.Move5.Shortcut = System.Windows.Forms.Shortcut.F6
        Me.Move5.ShortcutText = ""
        Me.Move5.Text = "新增"
        '
        'Control1
        '
        Me.Control1.Control = Me.T_Label1
        Me.Control1.HotFrame = C1.Win.C1Command.HotFrameEnum.None
        Me.Control1.Name = "Control1"
        Me.Control1.ShortcutText = ""
        Me.Control1.ShowShortcut = False
        Me.Control1.ShowTextAsToolTip = False
        '
        'T_Label1
        '
        Me.T_Label1.AutoSize = True
        Me.T_Label1.BackColor = System.Drawing.Color.Transparent
        Me.T_Label1.BorderColor = System.Drawing.Color.Empty
        Me.T_Label1.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.T_Label1.ForeColor = System.Drawing.SystemColors.ControlText
        Me.T_Label1.Location = New System.Drawing.Point(1, 5)
        Me.T_Label1.Name = "T_Label1"
        Me.T_Label1.Size = New System.Drawing.Size(29, 12)
        Me.T_Label1.TabIndex = 36
        Me.T_Label1.Tag = Nothing
        Me.T_Label1.Text = "记录"
        Me.T_Label1.TextAlign = System.Drawing.ContentAlignment.BottomLeft
        Me.T_Label1.TextDetached = True
        '
        'Control2
        '
        Me.Control2.Control = Me.T_Label2
        Me.Control2.HotFrame = C1.Win.C1Command.HotFrameEnum.None
        Me.Control2.Name = "Control2"
        Me.Control2.ShortcutText = ""
        Me.Control2.ShowShortcut = False
        Me.Control2.ShowTextAsToolTip = False
        '
        'T_Label2
        '
        Me.T_Label2.AutoSize = True
        Me.T_Label2.BackColor = System.Drawing.Color.Transparent
        Me.T_Label2.BorderColor = System.Drawing.Color.Empty
        Me.T_Label2.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.T_Label2.ForeColor = System.Drawing.SystemColors.ControlText
        Me.T_Label2.Location = New System.Drawing.Point(80, 5)
        Me.T_Label2.Name = "T_Label2"
        Me.T_Label2.Size = New System.Drawing.Size(23, 12)
        Me.T_Label2.TabIndex = 0
        Me.T_Label2.Tag = Nothing
        Me.T_Label2.Text = "(1)"
        Me.T_Label2.TextAlign = System.Drawing.ContentAlignment.BottomCenter
        Me.T_Label2.TextDetached = True
        Me.T_Label2.TrimStart = True
        '
        'Control3
        '
        Me.Control3.Control = Me.T_Label3
        Me.Control3.HotFrame = C1.Win.C1Command.HotFrameEnum.None
        Me.Control3.Name = "Control3"
        Me.Control3.ShortcutText = ""
        Me.Control3.ShowShortcut = False
        Me.Control3.ShowTextAsToolTip = False
        '
        'T_Label3
        '
        Me.T_Label3.BackColor = System.Drawing.Color.Transparent
        Me.T_Label3.BorderColor = System.Drawing.Color.Empty
        Me.T_Label3.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.T_Label3.ForeColor = System.Drawing.SystemColors.ControlText
        Me.T_Label3.Location = New System.Drawing.Point(212, 3)
        Me.T_Label3.Name = "T_Label3"
        Me.T_Label3.Size = New System.Drawing.Size(35, 15)
        Me.T_Label3.TabIndex = 35
        Me.T_Label3.Tag = Nothing
        Me.T_Label3.Text = "Σ=1 "
        Me.T_Label3.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.T_Label3.TextDetached = True
        '
        'Control4
        '
        Me.Control4.Name = "Control4"
        Me.Control4.ShortcutText = ""
        Me.Control4.Text = "New Command"
        '
        'ToolBar1
        '
        Me.ToolBar1.AccessibleName = "Tool Bar"
        Me.ToolBar1.BackColor = System.Drawing.Color.Transparent
        Me.ToolBar1.CommandHolder = Me.C1Holder1
        Me.ToolBar1.CommandLinks.AddRange(New C1.Win.C1Command.C1CommandLink() {Me.Link6, Me.Link1, Me.Link2, Me.Link7, Me.Link3, Me.Link4, Me.Link5, Me.Link8})
        Me.ToolBar1.Controls.Add(Me.T_Label1)
        Me.ToolBar1.Controls.Add(Me.T_Label3)
        Me.ToolBar1.Controls.Add(Me.T_Label2)
        Me.ToolBar1.Location = New System.Drawing.Point(1, 3)
        Me.ToolBar1.MinButtonSize = 22
        Me.ToolBar1.Movable = False
        Me.ToolBar1.Name = "ToolBar1"
        Me.ToolBar1.Size = New System.Drawing.Size(248, 22)
        Me.ToolBar1.Text = "C1ToolBar2"
        Me.ToolBar1.VisualStyle = C1.Win.C1Command.VisualStyle.Custom
        Me.ToolBar1.VisualStyleBase = C1.Win.C1Command.VisualStyle.Classic
        '
        'Link6
        '
        Me.Link6.Command = Me.Control1
        '
        'Link1
        '
        Me.Link1.Command = Me.Move1
        Me.Link1.Delimiter = True
        Me.Link1.SortOrder = 1
        '
        'Link2
        '
        Me.Link2.Command = Me.Move2
        Me.Link2.SortOrder = 2
        '
        'Link7
        '
        Me.Link7.Command = Me.Control2
        Me.Link7.SortOrder = 3
        '
        'Link3
        '
        Me.Link3.Command = Me.Move3
        Me.Link3.SortOrder = 4
        '
        'Link4
        '
        Me.Link4.Command = Me.Move4
        Me.Link4.SortOrder = 5
        '
        'Link5
        '
        Me.Link5.ButtonLook = CType((C1.Win.C1Command.ButtonLookFlags.Text Or C1.Win.C1Command.ButtonLookFlags.Image), C1.Win.C1Command.ButtonLookFlags)
        Me.Link5.Command = Me.Move5
        Me.Link5.Delimiter = True
        Me.Link5.SortOrder = 6
        Me.Link5.Text = "新增"
        Me.Link5.ToolTipText = "新增记录"
        '
        'Link8
        '
        Me.Link8.Command = Me.Control3
        Me.Link8.Delimiter = True
        Me.Link8.SortOrder = 7
        Me.Link8.Text = "New Command"
        '
        'Panel1
        '
        Me.Panel1.Controls.Add(Me.Comm2)
        Me.Panel1.Controls.Add(Me.Comm1)
        Me.Panel1.Controls.Add(Me.T_Line3)
        Me.Panel1.Controls.Add(Me.ToolBar1)
        Me.Panel1.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.Panel1.Location = New System.Drawing.Point(0, 217)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(476, 32)
        Me.Panel1.TabIndex = 1
        '
        'Comm2
        '
        Me.Comm2.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.Comm2.DialogResult = System.Windows.Forms.DialogResult.None
        Me.Comm2.Location = New System.Drawing.Point(360, 3)
        Me.Comm2.Name = "Comm2"
        Me.Comm2.Size = New System.Drawing.Size(60, 25)
        Me.Comm2.TabIndex = 1
        Me.Comm2.Tag = "取消"
        Me.Comm2.Text = "取消"
        '
        'Comm1
        '
        Me.Comm1.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.Comm1.DialogResult = System.Windows.Forms.DialogResult.None
        Me.Comm1.Location = New System.Drawing.Point(294, 3)
        Me.Comm1.Name = "Comm1"
        Me.Comm1.Size = New System.Drawing.Size(60, 25)
        Me.Comm1.TabIndex = 0
        Me.Comm1.Tag = "保存"
        Me.Comm1.Text = "保存"
        '
        'T_Line3
        '
        Me.T_Line3.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.T_Line3.Dock = System.Windows.Forms.DockStyle.Top
        Me.T_Line3.Location = New System.Drawing.Point(0, 0)
        Me.T_Line3.Name = "T_Line3"
        Me.T_Line3.Size = New System.Drawing.Size(476, 2)
        Me.T_Line3.TabIndex = 101
        Me.T_Line3.Text = "Label2"
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.ColumnCount = 4
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 50.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 220.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 220.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 50.0!))
        Me.TableLayoutPanel1.Controls.Add(Me.skCode_TextBox, 1, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.dayNumericEdit, 2, 4)
        Me.TableLayoutPanel1.Controls.Add(Me.hourNumericEdit, 1, 5)
        Me.TableLayoutPanel1.Controls.Add(Me.Memo_TextBox, 1, 6)
        Me.TableLayoutPanel1.Controls.Add(Me.mblbMyDtComobo, 1, 3)
        Me.TableLayoutPanel1.Controls.Add(Me.mbMyDtComobo, 2, 3)
        Me.TableLayoutPanel1.Controls.Add(Me.ylsjMySingleComobo, 1, 4)
        Me.TableLayoutPanel1.Controls.Add(Me.T_Line1, 0, 2)
        Me.TableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(0, 0)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.RowCount = 6
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 10.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 2.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 20.0!))
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(476, 217)
        Me.TableLayoutPanel1.TabIndex = 0
        '
        'skCode_TextBox
        '
        Me.skCode_TextBox.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.skCode_TextBox.Captain = "时控编码"
        Me.skCode_TextBox.CaptainBackColor = System.Drawing.Color.Transparent
        Me.skCode_TextBox.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.skCode_TextBox.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.skCode_TextBox.CaptainWidth = 60.0!
        Me.skCode_TextBox.ContentForeColor = System.Drawing.Color.Black
        Me.skCode_TextBox.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.skCode_TextBox.Location = New System.Drawing.Point(21, 14)
        Me.skCode_TextBox.Multiline = False
        Me.skCode_TextBox.Name = "skCode_TextBox"
        Me.skCode_TextBox.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.skCode_TextBox.ReadOnly = False
        Me.skCode_TextBox.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.skCode_TextBox.SelectionStart = 0
        Me.skCode_TextBox.SelectStart = 0
        Me.skCode_TextBox.Size = New System.Drawing.Size(214, 20)
        Me.skCode_TextBox.TabIndex = 0
        Me.skCode_TextBox.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.skCode_TextBox.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        '
        'dayNumericEdit
        '
        Me.dayNumericEdit.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.dayNumericEdit.Captain = "天    数"
        Me.dayNumericEdit.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.dayNumericEdit.CaptainWidth = 60.0!
        Me.dayNumericEdit.Location = New System.Drawing.Point(241, 72)
        Me.dayNumericEdit.MaximumSize = New System.Drawing.Size(100000, 80)
        Me.dayNumericEdit.MinimumSize = New System.Drawing.Size(0, 20)
        Me.dayNumericEdit.Name = "dayNumericEdit"
        Me.dayNumericEdit.NumFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.dayNumericEdit.ReadOnly = False
        Me.dayNumericEdit.Size = New System.Drawing.Size(214, 20)
        Me.dayNumericEdit.TabIndex = 3
        Me.dayNumericEdit.ValueIsDbNull = False
        '
        'hourNumericEdit
        '
        Me.hourNumericEdit.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.hourNumericEdit.Captain = "小 时 数"
        Me.hourNumericEdit.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.hourNumericEdit.CaptainWidth = 60.0!
        Me.hourNumericEdit.Location = New System.Drawing.Point(21, 100)
        Me.hourNumericEdit.MaximumSize = New System.Drawing.Size(100000, 80)
        Me.hourNumericEdit.MinimumSize = New System.Drawing.Size(0, 20)
        Me.hourNumericEdit.Name = "hourNumericEdit"
        Me.hourNumericEdit.NumFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.hourNumericEdit.ReadOnly = False
        Me.hourNumericEdit.Size = New System.Drawing.Size(214, 20)
        Me.hourNumericEdit.TabIndex = 4
        Me.hourNumericEdit.ValueIsDbNull = False
        '
        'Memo_TextBox
        '
        Me.Memo_TextBox.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Memo_TextBox.Captain = "备    注"
        Me.Memo_TextBox.CaptainBackColor = System.Drawing.Color.Transparent
        Me.Memo_TextBox.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Memo_TextBox.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.Memo_TextBox.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.Memo_TextBox, 2)
        Me.Memo_TextBox.ContentForeColor = System.Drawing.Color.Black
        Me.Memo_TextBox.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.Memo_TextBox.Location = New System.Drawing.Point(21, 129)
        Me.Memo_TextBox.Multiline = True
        Me.Memo_TextBox.Name = "Memo_TextBox"
        Me.Memo_TextBox.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.Memo_TextBox.ReadOnly = False
        Me.Memo_TextBox.ScrollBars = System.Windows.Forms.ScrollBars.Vertical
        Me.Memo_TextBox.SelectionStart = 0
        Me.Memo_TextBox.SelectStart = 0
        Me.Memo_TextBox.Size = New System.Drawing.Size(434, 83)
        Me.Memo_TextBox.TabIndex = 5
        Me.Memo_TextBox.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Memo_TextBox.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Top
        '
        'mblbMyDtComobo
        '
        Me.mblbMyDtComobo.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.mblbMyDtComobo.Captain = "模版类别"
        Me.mblbMyDtComobo.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.mblbMyDtComobo.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.mblbMyDtComobo.CaptainWidth = 60.0!
        Me.mblbMyDtComobo.DataSource = Nothing
        Me.mblbMyDtComobo.ItemHeight = 16
        Me.mblbMyDtComobo.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.mblbMyDtComobo.Location = New System.Drawing.Point(21, 44)
        Me.mblbMyDtComobo.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.mblbMyDtComobo.MinimumSize = New System.Drawing.Size(0, 20)
        Me.mblbMyDtComobo.Name = "mblbMyDtComobo"
        Me.mblbMyDtComobo.ReadOnly = False
        Me.mblbMyDtComobo.Size = New System.Drawing.Size(214, 20)
        Me.mblbMyDtComobo.TabIndex = 0
        Me.mblbMyDtComobo.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'mbMyDtComobo
        '
        Me.mbMyDtComobo.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.mbMyDtComobo.Captain = "模    版"
        Me.mbMyDtComobo.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.mbMyDtComobo.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.mbMyDtComobo.CaptainWidth = 60.0!
        Me.mbMyDtComobo.DataSource = Nothing
        Me.mbMyDtComobo.ItemHeight = 16
        Me.mbMyDtComobo.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.mbMyDtComobo.Location = New System.Drawing.Point(241, 44)
        Me.mbMyDtComobo.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.mbMyDtComobo.MinimumSize = New System.Drawing.Size(0, 20)
        Me.mbMyDtComobo.Name = "mbMyDtComobo"
        Me.mbMyDtComobo.ReadOnly = False
        Me.mbMyDtComobo.Size = New System.Drawing.Size(214, 20)
        Me.mbMyDtComobo.TabIndex = 1
        Me.mbMyDtComobo.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'ylsjMySingleComobo
        '
        Me.ylsjMySingleComobo.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ylsjMySingleComobo.Captain = "医疗事件"
        Me.ylsjMySingleComobo.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.ylsjMySingleComobo.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.ylsjMySingleComobo.CaptainWidth = 60.0!
        Me.ylsjMySingleComobo.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        Me.ylsjMySingleComobo.ItemHeight = 16
        Me.ylsjMySingleComobo.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.ylsjMySingleComobo.Location = New System.Drawing.Point(21, 72)
        Me.ylsjMySingleComobo.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.ylsjMySingleComobo.MinimumSize = New System.Drawing.Size(0, 20)
        Me.ylsjMySingleComobo.Name = "ylsjMySingleComobo"
        Me.ylsjMySingleComobo.ReadOnly = False
        Me.ylsjMySingleComobo.Size = New System.Drawing.Size(214, 20)
        Me.ylsjMySingleComobo.TabIndex = 2
        Me.ylsjMySingleComobo.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'T_Line1
        '
        Me.T_Line1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.T_Line1.BackColor = System.Drawing.SystemColors.Control
        Me.T_Line1.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.TableLayoutPanel1.SetColumnSpan(Me.T_Line1, 4)
        Me.T_Line1.Location = New System.Drawing.Point(3, 38)
        Me.T_Line1.Name = "T_Line1"
        Me.T_Line1.Size = New System.Drawing.Size(470, 2)
        Me.T_Line1.TabIndex = 131
        Me.T_Line1.Text = "Label1"
        '
        'Zd_ShikongTwo
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(476, 249)
        Me.Controls.Add(Me.TableLayoutPanel1)
        Me.Controls.Add(Me.Panel1)
        Me.MinimizeBox = False
        Me.Name = "Zd_ShikongTwo"
        Me.Text = "时控编辑"
        Me.TopMost = True
        CType(Me.C1Holder1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T_Label1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T_Label2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.T_Label3, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ToolBar1.ResumeLayout(False)
        Me.ToolBar1.PerformLayout()
        Me.Panel1.ResumeLayout(False)
        Me.TableLayoutPanel1.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents C1Holder1 As C1.Win.C1Command.C1CommandHolder
    Friend WithEvents Move1 As C1.Win.C1Command.C1Command
    Friend WithEvents Move2 As C1.Win.C1Command.C1Command
    Friend WithEvents Move3 As C1.Win.C1Command.C1Command
    Friend WithEvents Move4 As C1.Win.C1Command.C1Command
    Friend WithEvents Move5 As C1.Win.C1Command.C1Command
    Friend WithEvents Control1 As C1.Win.C1Command.C1CommandControl
    Friend WithEvents Control2 As C1.Win.C1Command.C1CommandControl
    Friend WithEvents Control3 As C1.Win.C1Command.C1CommandControl
    Friend WithEvents T_Label3 As C1.Win.C1Input.C1Label
    Friend WithEvents T_Label2 As C1.Win.C1Input.C1Label
    Friend WithEvents T_Label1 As C1.Win.C1Input.C1Label
    Friend WithEvents ToolBar1 As C1.Win.C1Command.C1ToolBar
    Friend WithEvents Link1 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link2 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link7 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link3 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link4 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link5 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Link8 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents T_Line3 As System.Windows.Forms.Label
    Friend WithEvents Link6 As C1.Win.C1Command.C1CommandLink
    Friend WithEvents ToolTip1 As System.Windows.Forms.ToolTip
    Friend WithEvents TableLayoutPanel1 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents Control4 As C1.Win.C1Command.C1Command
    Friend WithEvents Comm1 As CustomControl.MyButton
    Friend WithEvents Comm2 As CustomControl.MyButton
    Friend WithEvents skCode_TextBox As CustomControl.MyTextBox
    Friend WithEvents T_Line1 As System.Windows.Forms.Label
    Friend WithEvents mblbMyDtComobo As CustomControl.MyDtComobo
    Friend WithEvents ylsjMySingleComobo As CustomControl.MySingleComobo
    Friend WithEvents mbMyDtComobo As CustomControl.MyDtComobo
    Friend WithEvents dayNumericEdit As CustomControl.MyNumericEdit
    Friend WithEvents hourNumericEdit As CustomControl.MyNumericEdit
    Friend WithEvents Memo_TextBox As CustomControl.MyTextBox
End Class
