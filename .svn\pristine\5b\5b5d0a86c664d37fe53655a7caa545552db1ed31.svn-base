﻿/**  版本信息模板在安装目录下，可自行修改。
* D_Emr_DataField.cs
*
* 功 能： N/A
* 类 名： D_Emr_DataField
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/8/16 星期二 下午 1:38:16   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
//
namespace DAL
{
	/// <summary>
	/// 数据访问类:D_Emr_DataField
	/// </summary>
	public partial class D_Emr_DataField
	{
		public D_Emr_DataField()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string DataField)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from Emr_DataField");
			strSql.Append(" where DataField=@DataField ");
			SqlParameter[] parameters = {
					new SqlParameter("@DataField", SqlDbType.VarChar,50)			};
			parameters[0].Value = DataField;

			return HisVar.HisVar.Sqldal.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ModelOld.M_Emr_DataField model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into Emr_DataField(");
			strSql.Append("DataField,DataName,DataJc)");
			strSql.Append(" values (");
			strSql.Append("@DataField,@DataName,@DataJc)");
			SqlParameter[] parameters = {
					new SqlParameter("@DataField", SqlDbType.VarChar,50),
					new SqlParameter("@DataName", SqlDbType.VarChar,50),
					new SqlParameter("@DataJc", SqlDbType.VarChar,50)};
			parameters[0].Value = model.DataField;
			parameters[1].Value = model.DataName;
			parameters[2].Value = model.DataJc;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ModelOld.M_Emr_DataField model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update Emr_DataField set ");
			strSql.Append("DataName=@DataName,");
			strSql.Append("DataJc=@DataJc");
			strSql.Append(" where DataField=@DataField ");
			SqlParameter[] parameters = {
					new SqlParameter("@DataName", SqlDbType.VarChar,50),
					new SqlParameter("@DataJc", SqlDbType.VarChar,50),
					new SqlParameter("@DataField", SqlDbType.VarChar,50)};
			parameters[0].Value = model.DataName;
			parameters[1].Value = model.DataJc;
			parameters[2].Value = model.DataField;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string DataField)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Emr_DataField ");
			strSql.Append(" where DataField=@DataField ");
			SqlParameter[] parameters = {
					new SqlParameter("@DataField", SqlDbType.VarChar,50)			};
			parameters[0].Value = DataField;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string DataFieldlist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Emr_DataField ");
			strSql.Append(" where DataField in ("+DataFieldlist + ")  ");
			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Emr_DataField GetModel(string DataField)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 DataField,DataName,DataJc from Emr_DataField ");
			strSql.Append(" where DataField=@DataField ");
			SqlParameter[] parameters = {
					new SqlParameter("@DataField", SqlDbType.VarChar,50)			};
			parameters[0].Value = DataField;

			ModelOld.M_Emr_DataField model=new ModelOld.M_Emr_DataField();
			DataSet ds=HisVar.HisVar.Sqldal.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Emr_DataField DataRowToModel(DataRow row)
		{
			ModelOld.M_Emr_DataField model=new ModelOld.M_Emr_DataField();
			if (row != null)
			{
				if(row["DataField"]!=null)
				{
					model.DataField=row["DataField"].ToString();
				}
				if(row["DataName"]!=null)
				{
					model.DataName=row["DataName"].ToString();
				}
				if(row["DataJc"]!=null)
				{
					model.DataJc=row["DataJc"].ToString();
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select DataField,DataName,DataJc ");
			strSql.Append(" FROM Emr_DataField ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" DataField,DataName,DataJc ");
			strSql.Append(" FROM Emr_DataField ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM Emr_DataField ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.DataField desc");
			}
			strSql.Append(")AS Row, T.*  from Emr_DataField T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Emr_DataField";
			parameters[1].Value = "DataField";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return HisVar.HisVar.Sqldal.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

