﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ERX.Model
{
    public class MdlERXInTemp
    {
        public string appId { get; set; }
        public string version { get; set; } = "1.0.0";
        public string timestamp { get; set; }
        public string encType { get; set; } = "SM4";
        public string data { get; set; }
        public string signType { get; set; } = "SM2";
        public string signData { get; set; }
    }

    public class MdlERXIn
    {
        public string appId { get; set; }
        public string version { get; set; } = "1.0.0";
        public string timestamp { get; set; }
        public string encType { get; set; } = "SM4";
        public string encData { get; set; }
        public string signType { get; set; } = "SM2";
        public string signData { get; set; }
    }

    public class MdlERXOut
    {
        public int code { get; set; }
        public string appId { get; set; }
        public string timestamp { get; set; }
        public string encType { get; set; }
        public string signType { get; set; }
        public string signData { get; set; }
        public string encData { get; set; }
        public string message { get; set; }
        public string success { get; set; }
        public string type { get; set; }

    }

    public class MdlERXOutPlaintext<T>
    {
        public int code { get; set; }
        public string appId { get; set; }
        public string timestamp { get; set; }
        public string encType { get; set; }
        public string signType { get; set; }
        public string signData { get; set; }
        public T data { get; set; }
        public string message { get; set; }
        public string success { get; set; }
        public string type { get; set; }

    }
}
