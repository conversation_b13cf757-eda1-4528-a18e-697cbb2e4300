﻿Imports System.Windows.Forms
Public Class BaseChild

    'Private Sub BaseChild_Activated(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Activated
    '    Me.ShowInTaskbar = False
    '    Me.FormBorderStyle = Windows.Forms.FormBorderStyle.Sizable
    'End Sub

    Private Sub BaseChild_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        '   Me.ImeMode = ImeMode.OnHalf
    End Sub
    'Private Sub BaseForm_FormClosing(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosingEventArgs) Handles Me.FormClosing
    '    RemoveTap(Me.Name)
    'End Sub

    Protected Overrides Sub OnKeyDown(ByVal e As System.Windows.Forms.KeyEventArgs)
        MyBase.OnKeyDown(e)
        If e.KeyCode = Keys.Escape Then
            Me.Close()
        End If
    End Sub
End Class
