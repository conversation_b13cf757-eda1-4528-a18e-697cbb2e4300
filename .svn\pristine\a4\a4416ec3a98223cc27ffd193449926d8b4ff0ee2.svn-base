﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="0" />
    <DataSources isList="true" count="0" />
    <Relations isList="true" count="0" />
    <Report isRef="0" />
    <Variables isList="true" count="19">
      <value>,打印日期,打印日期,System.String,,False,False</value>
      <value>,单据号码,单据号码,System.String,,False,False</value>
      <value>,交费日期,交费日期,System.DateTime,,False,False</value>
      <value>,姓名,姓名,System.String,,False,False</value>
      <value>,病历编码,病历编码,System.String,,False,False</value>
      <value>,金额,金额,System.String,,False,False</value>
      <value>,大写,大写,System.String,,False,False</value>
      <value>,收款人,收款人,System.String,,False,False</value>
      <value>,入院编码,入院编码,System.String,,False,False</value>
      <value>,科室名称,科室名称,System.String,,False,False</value>
      <value>,住院事由,住院事由,System.String,,False,False</value>
      <value>,支票单位,支票单位,System.String,,False,False</value>
      <value>,开户行,开户行,System.String,,False,False</value>
      <value>,银行账号,银行账号,System.String,,False,False</value>
      <value>,预计住院天数,预计住院天数,System.String,,False,False</value>
      <value>,收取押金标准,收取押金标准,System.String,,False,False</value>
      <value>,单位名称,单位名称,System.String,,False,False</value>
      <value>,床位,床位,System.String,,False,False</value>
      <value>,支票号,支票号,System.String,,False,False</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="2" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="1">
        <ReportTitleBand1 Ref="3" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,17.3,9.3</ClientRectangle>
          <Components isList="true" count="42">
            <Text1 Ref="4" type="Text" isKey="true">
              <Border>None;Black;1;Double;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,19,1.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>黑体,15,Regular,Point,False,134</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text1</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text1>
            <Text9 Ref="5" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.4,3.4,1.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11.25,Regular,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text9</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{姓名}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text9>
            <Text13 Ref="6" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>10.4,6.7,8.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11.25,Regular,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text13</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{金额}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text13>
            <Text15 Ref="7" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.4,6.7,5.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11.25,Regular,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text15</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{大写}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text15>
            <Text17 Ref="8" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,7.7,3.5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,11.25,Regular,Point,False,134</Font>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text17</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>收款人</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text17>
            <Text18 Ref="9" type="Text" isKey="true">
              <Border>None;Black;1;Double;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>3.5,7.7,4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11.25,Regular,Point,False,134</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text18</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{收款人}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text18>
            <Text21 Ref="10" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>5.2,3.4,2.4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11.25,Regular,Point,False,134</Font>
              <Guid>668867635289447c80748049f5156e6e</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text21</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{病历编码}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text21>
            <Text23 Ref="11" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>8.9,3.4,1.8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11.25,Regular,Point,False,134</Font>
              <Guid>737708a39dcc40fbba2306ff9e46de98</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text23</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{科室名称}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text23>
            <Text24 Ref="12" type="Text" isKey="true">
              <Border>None;Black;1;Double;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,8.3,18.9,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,12,Regular,Point,False,134</Font>
              <Guid>f5419406510f4e1f9bf9c6ebf33ad53e</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text24</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text24>
            <Text16 Ref="13" type="Text" isKey="true">
              <Border>None;Black;1;Double;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,7.2,19,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,12,Regular,Point,False,134</Font>
              <Guid>4bd85b154afa46d39da3165630663ad9</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text16</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text16>
            <Text19 Ref="14" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,6.7,4.4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,11.25,Regular,Point,False,134</Font>
              <Guid>aff020d282464eb8b522ddab08a9391c</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text19</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>支票或现金金额：（大写）</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text19>
            <Text25 Ref="15" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>9.9,6.7,0.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,11.25,Regular,Point,False,134</Font>
              <Guid>502be44d1be34a9a917916ffcda3b0b0</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text25</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>￥</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text25>
            <Text26 Ref="16" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,6.1,19,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,12,Regular,Point,False,134</Font>
              <Guid>e3eac96704b84f1b84b10f26a8f9fc91</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text26</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text26>
            <Text12 Ref="17" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,5.6,2.4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,11.25,Regular,Point,False,134</Font>
              <Guid>5ee21b19caa342ea8b046907b93fd912</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text12</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>支票单位</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text12>
            <Text14 Ref="18" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.4,5.6,2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11.25,Regular,Point,False,134</Font>
              <Guid>23bb40f83cc94545a70749cf4b721b85</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text14</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{支票单位}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text14>
            <Text27 Ref="19" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.4,5.6,1.8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,11.25,Regular,Point,False,134</Font>
              <Guid>026ea0c5f24f429d92c8813eac139e4c</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text27</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>支票号</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text27>
            <Text28 Ref="20" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>6.2,5.6,1.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11.25,Regular,Point,False,134</Font>
              <Guid>f7fb0e96538844f2b64d6b6900b72447</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text28</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{支票号}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
            </Text28>
            <Text29 Ref="21" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7.8,5.6,1.4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,11.25,Regular,Point,False,134</Font>
              <Guid>a2233ae39af74300a47cd071c2c94fc5</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text29</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>开户行</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text29>
            <Text30 Ref="22" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>9.2,5.6,1.2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11.25,Regular,Point,False,134</Font>
              <Guid>031a2401f2aa4e0aa211bc4eca49cc4d</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text30</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{开户行}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text30>
            <Text31 Ref="23" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>10.4,5.6,1.8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,11.25,Regular,Point,False,134</Font>
              <Guid>cae66a470afd442791740e83cf8cd060</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text31</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>银行账户</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text31>
            <Text32 Ref="24" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>12.2,5.6,6.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11.25,Regular,Point,False,134</Font>
              <Guid>92285837422f496c848ebf24d7ca1906</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text32</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{银行账号}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text32>
            <Text34 Ref="25" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,5,19,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,12,Regular,Point,False,134</Font>
              <Guid>d192253e78d5477a97c1d7c5b5784562</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text34</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text34>
            <Text35 Ref="26" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,4.5,2.4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,11.25,Regular,Point,False,134</Font>
              <Guid>0eab1a6021c640e8915175e93b48972a</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text35</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>住院事由</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text35>
            <Text36 Ref="27" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.4,4.5,2.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,12,Regular,Point,False,134</Font>
              <Guid>7699ad5a917e4b448bebe1e0550da117</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text36</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{住院事由}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text36>
            <Text37 Ref="28" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>4.9,4.5,2.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,11.25,Regular,Point,False,134</Font>
              <Guid>a2b72df0372b4d9a8f2ce920bd58cb31</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text37</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>预计住院天数</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text37>
            <Text38 Ref="29" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7.4,4.5,2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11.25,Regular,Point,False,134</Font>
              <Guid>f1db2c333b494e10a986bf18c4126de9</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text38</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{预计住院天数}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text38>
            <Text39 Ref="30" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>9.4,4.5,2.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,11.25,Regular,Point,False,134</Font>
              <Guid>c65e070728ab448e8358a3f7788a2562</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text39</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>收费押金标准</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text39>
            <Text40 Ref="31" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>11.9,4.5,7.4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11.25,Regular,Point,False,134</Font>
              <Guid>047d491abf7f419d9dccff70cd683163</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text40</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{收取押金标准}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text40>
            <Text41 Ref="32" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,3.9,19,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,12,Regular,Point,False,134</Font>
              <Guid>5119e74828e64e3ead1df76f606efce0</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text41</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text41>
            <Text42 Ref="33" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,3.4,2.4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,11.25,Regular,Point,False,134</Font>
              <Guid>2757ca3233c1484fa41834e34a47f068</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text42</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>患者姓名</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text42>
            <Text43 Ref="34" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>3.9,3.4,1.3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,11.25,Regular,Point,False,134</Font>
              <Guid>384feac5d691426ba663f8147e46f4ee</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text43</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>病历号</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text43>
            <Text44 Ref="35" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>10.7,3.4,1.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,11.25,Regular,Point,False,134</Font>
              <Guid>c56d023a053e4ce6bf1ce3d5d5a4f4e1</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text44</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>床位号</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text44>
            <Text45 Ref="36" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7.6,3.4,1.3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,11.25,Regular,Point,False,134</Font>
              <Guid>0026ca2b6367441f918af332c940d067</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text45</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>科别</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text45>
            <Text46 Ref="37" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>12.2,3.4,6.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11.25,Regular,Point,False,134</Font>
              <Guid>510804090f5740059cf82ab1478177ee</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text46</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{床位}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text46>
            <Text47 Ref="38" type="Text" isKey="true">
              <Border>None;Black;1;Double;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,2.9,19,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,12,Regular,Point,False,134</Font>
              <Guid>24440bc935e94a9cb96dbe5fffe4afd2</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text47</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text47>
            <Text8 Ref="39" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,2.3,2.4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,11.25,Regular,Point,False,134</Font>
              <Guid>d19ae360c5814404b7fd28b0ff8ccb8d</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text8</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>收款单位</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text8>
            <Text10 Ref="40" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.4,2.3,5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11.25,Regular,Point,False,134</Font>
              <Guid>17824a072b0240d9b51ee73776a196aa</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text10</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{单位名称}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text10>
            <Text5 Ref="41" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>13,2.3,5.9,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11.25,Regular,Point,False,134</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text5</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{单据号码}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text5>
            <Text6 Ref="42" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7.4,2.3,4.4,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11.25,Regular,Point,False,134</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text6</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{交费日期}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Bottom</VertAlignment>
            </Text6>
            <Text49 Ref="43" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>11.8,2.3,1.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Enabled>False</Enabled>
              <Font>宋体,12,Regular,Point,False,134</Font>
              <Guid>4a86f5eec0b548839f87bb6a9e53094f</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text49</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>No.</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text49>
            <Text4 Ref="44" type="Text" isKey="true">
              <Border>None;Black;1;Double;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.8,19,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,11.25,Regular,Point,False,134</Font>
              <Guid>a5d07aa6c2224520abcced0a6ba902b0</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text4</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>入院编码：{入院编码}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text4>
            <Text7 Ref="45" type="Text" isKey="true">
              <Border>None;Black;1;Double;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.5,7.7,11.6,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,12,Regular,Point,False,134</Font>
              <Guid>30cab1d7f5f74fbb800c38f344b198dd</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text7</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text7>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>ReportTitleBand1</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
        </ReportTitleBand1>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>b290c0160f274f94a9a9816e6fb11eb2</Guid>
      <Margins>0,0,0,0</Margins>
      <Name>Page1</Name>
      <PageHeight>9.3</PageHeight>
      <PageWidth>17.3</PageWidth>
      <Report isRef="0" />
      <Watermark Ref="46" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page1>
  </Pages>
  <PrinterSettings Ref="47" type="Stimulsoft.Report.Print.StiPrinterSettings" isKey="true" />
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>住院押金缴费单</ReportAlias>
  <ReportChanged>5/30/2013 3:33:38 PM</ReportChanged>
  <ReportCreated>12/29/2011 8:55:06 AM</ReportCreated>
  <ReportFile>G:\住院押金缴费单(丰南).mrt</ReportFile>
  <ReportGuid>10c1671b21204d16bb1d6c909309eb08</ReportGuid>
  <ReportName>住院押金缴费单</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2011.2.1026</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class Report : Stimulsoft.Report.StiReport
    {
        public Report()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
        #endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>