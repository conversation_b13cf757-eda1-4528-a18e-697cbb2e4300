﻿/**  版本信息模板在安装目录下，可自行修改。
* M_Materials_Move2.cs
*
* 功 能： N/A
* 类 名： M_Materials_Move2
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016-12-05 13:54:39   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace ModelOld
{
	/// <summary>
	/// 物资移库从表
	/// </summary>
	[Serializable]
	public partial class M_Materials_Move2
	{
		public M_Materials_Move2()
		{}
		#region Model
		private string _m_move_code;
		private string _materials_code;
		private string _new_materialsstock_code;
		private string _old_materialsstock_code;
		private string _m_move_detail_code;
		private string _materialslot;
        private string _materials_spec;
		private DateTime? _materialsexpirydate;
		private decimal? _m_move_num;
		private decimal? _m_move_price;
		private decimal? _m_move_money;
		private string _m_movedetail_memo;
		/// <summary>
		/// yyMMdd+5位流水号
		/// </summary>
		public string M_Move_Code
		{
			set{ _m_move_code=value;}
			get{return _m_move_code;}
		}
		/// <summary>
		/// 物资编码
		/// </summary>
		public string Materials_Code
		{
			set{ _materials_code=value;}
			get{return _materials_code;}
		}
		/// <summary>
		/// 物资编码+6位流水号
		/// </summary>
		public string New_MaterialsStock_Code
		{
			set{ _new_materialsstock_code=value;}
			get{return _new_materialsstock_code;}
		}
		/// <summary>
		/// 物资编码+6位流水号
		/// </summary>
		public string Old_MaterialsStock_Code
		{
			set{ _old_materialsstock_code=value;}
			get{return _old_materialsstock_code;}
		}
		/// <summary>
		/// 移库明细编码
		/// </summary>
		public string M_Move_Detail_Code
		{
			set{ _m_move_detail_code=value;}
			get{return _m_move_detail_code;}
		}
		/// <summary>
		/// 物资批号
		/// </summary>
		public string MaterialsLot
		{
			set{ _materialslot=value;}
			get{return _materialslot;}
		}
		/// <summary>
		/// 物资有效期
		/// </summary>
		public DateTime? MaterialsExpiryDate
		{
			set{ _materialsexpirydate=value;}
			get{return _materialsexpirydate;}
		}

        /// <summary>
        /// 物资规格
        /// </summary>
        public string Materials_Spec
        {
            set { _materials_spec = value; }
            get { return _materials_spec; }
        }
		/// <summary>
		/// 移库数量
		/// </summary>
		public decimal? M_Move_Num
		{
			set{ _m_move_num=value;}
			get{return _m_move_num;}
		}
		/// <summary>
		/// 单价
		/// </summary>
		public decimal? M_Move_Price
		{
			set{ _m_move_price=value;}
			get{return _m_move_price;}
		}
		/// <summary>
		/// 金额
		/// </summary>
		public decimal? M_Move_Money
		{
			set{ _m_move_money=value;}
			get{return _m_move_money;}
		}
		/// <summary>
		/// 备注
		/// </summary>
		public string M_MoveDetail_Memo
		{
			set{ _m_movedetail_memo=value;}
			get{return _m_movedetail_memo;}
		}
		#endregion Model

	}
}

