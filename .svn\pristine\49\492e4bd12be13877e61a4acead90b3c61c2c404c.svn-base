﻿Imports System.Data.SqlClient
Imports Stimulsoft.Report

Public Class Xs_Zy_Yj11

#Region "变量初始化"
    Dim My_View As New DataView                 '数据视图
    Dim My_Date As String
    Dim My_Table As New DataTable            '药品字典
    Dim My_Cm As CurrencyManager             '同步指针
    Dim My_Row As DataRow
    Dim Bl_Jf_Model As New Modelold.M_Bl_Jf '当 前 行
    'Dim V_Insert As Boolean                  '增加记录
    Dim Bl_Jf As New BLLOld.B_Bl_Jf
    Dim m_Rc As New BaseClass.C_RowChange
    Dim form_load As Boolean = False
#End Region

    Private Sub Xs_Zy_Yj11_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Call Form_Init()
        form_load = True
        Call Init_Data()
        AddHandler m_Rc.AddChangeEvent, AddressOf GridMove
    End Sub

#Region "窗体初始化"

    Private Sub Form_Init()
        Panel1.BorderStyle = BorderStyle.None
        Panel1.Height = 25
        ToolBar1.Location = New Point(1, 1)
        T_Line1.Location = New Point(ToolBar1.Width + 2, 0)
        T_Label2.Location = New Point(T_Line1.Left + 4, 5)
        C1DateEdit2.Location = New Point(T_Label2.Left + T_Label2.Width + 2, 4)
        T_Line2.Location = New Point(C1DateEdit2.Left + C1DateEdit2.Width + 2, 2)
        T_Label.Location = New Point(T_Line2.Left + 2, 2)
        '初始化TDBGrid
        With MyGrid1
            .Init_Grid()
            .Init_Column("缴费编码", "Jf_Code", 0, "中", "", False)
            .Init_Column("病人姓名", "Ry_Name", 70, "左", "", False)
            .Init_Column("科室", "Ks_Name", 80, "左", "", False)
            .Init_Column("医生", "Ys_Name", 70, "左", "", False)
            .Init_Column("缴费方式", "Bl_Jffs", 70, "左", "", False)
            .Init_Column("交费金额", "Jf_Money", 70, "右", "0.00", False)
            .Init_Column("交费日期", "Jf_Date", 200, "中", "yyyy-MM-dd HH:mm:ss", False)
            .Init_Column("经手人", "Jsr_Name", 70, "左", "", False)
            .Init_Column("备注", "Jf_Memo", 70, "左", "", False)
            .AllowAddNew = False
            .ColumnFooters = True
        End With

        '当前日期
        My_Date = Date.Today
        With C1DateEdit2
            .Value = My_Date
            .DateTimeInput = True
            .AutoChangePosition = False
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .InitialSelection = C1.Win.C1Input.InitialSelectionEnum.CaretAtEnd
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .EditFormat.CustomFormat = "yyyy-MM-dd"
            .DisplayFormat.CustomFormat = "yyyy-MM-dd"
            .MaskInfo.AutoTabWhenFilled = True
            .AcceptsTab = True
            .EmptyAsNull = True
        End With


        If iniOperate.iniopreate.GetINI("参数", "押金单样式", "", HisVar.HisVar.Parapath & "\Config.ini") = "套打押金单" Then
            RadioButton1.Checked = True
        Else
            RadioButton2.Checked = True
        End If

    End Sub

    Private Sub Init_Data()
        Dim Sql_Select As String
        Sql_Select = "And convert(Varchar(10),Jf_Date,126)='" & Format(C1DateEdit2.Value, "yyyy-MM-dd") & "' And isnull(Ry_CyJsr,'')=''  and Bl_Jf.Jsr_Code='" & HisVar.HisVar.JsrCode & "' "
        My_Table = Bl_Jf.GetYjList(Sql_Select).Tables(0)
        MyGrid1.DataTable = My_Table
        Dim Sum1 As Double = 0
        If MyGrid1.RowCount <> 0 Then
            Sum1 = IIf(My_Table.Compute("Sum(Jf_Money)", "") Is DBNull.Value, 0, My_Table.Compute("Sum(Jf_Money)", ""))
        End If
        With Me.MyGrid1
            My_Cm = CType(BindingContext(My_Table, ""), CurrencyManager)
            .ColumnFooters = True
            .Columns(4).FooterText = Format(Sum1, "###0.00")
            T_Label.Text = "∑=" + MyGrid1.Splits(0).Rows.Count.ToString
        End With
    End Sub


#End Region

#Region "控件__动作"

    Private Sub Comm_Click(ByVal sender As Object, ByVal e As C1.Win.C1Command.ClickEventArgs) Handles Comm1.Click, Comm3.Click, Comm4.Click
        Select Case sender.text
            Case "增加"
                Call P_ShowData("增加")

            Case "更新"
                Call Init_Data()
                MyGrid1.Select()
                MyGrid1.MoveFirst()

            Case "打印"
                If Me.MyGrid1.RowCount > 0 Then
                    My_Row = My_Cm.List(MyGrid1.Row).Row
                    'Bl_Jf_Model = Bl_Jf.DateRowToMode(My_Row)
                    Dim StiRpt As New StiReport
                    If RadioButton1.Checked = True Then
                        StiRpt.Load("Rpt\住院押金缴费单(丰南).mrt")
                        StiRpt.ReportName = "住院押金缴费单"

                        Dim Yj_left As Double = IIf(iniOperate.iniopreate.GetINI("套打边距", "押金左边距", "", HisVar.HisVar.Parapath & "\Config.ini") = "", 0, iniOperate.iniopreate.GetINI("套打边距", "押金左边距", "", HisVar.HisVar.Parapath & "\Config.ini")) / 10
                        Dim Yj_top As Double = IIf(iniOperate.iniopreate.GetINI("套打边距", "押金上边距", "", HisVar.HisVar.Parapath & "\Config.ini") = "", 0, iniOperate.iniopreate.GetINI("套打边距", "押金上边距", "", HisVar.HisVar.Parapath & "\Config.ini")) / 10
                        StiRpt.Pages(0).Margins.Left = StiRpt.Pages(0).Margins.Left + Yj_left
                        StiRpt.Pages(0).Margins.Top = StiRpt.Pages(0).Margins.Top + Yj_top
                        StiRpt.Pages(0).PageHeight = StiRpt.Pages(0).PageHeight + Yj_top
                        StiRpt.Pages(0).PageWidth = StiRpt.Pages(0).PageWidth + Yj_left

                        StiRpt.Compile()
                        StiRpt("入院编码") = My_Row.Item("Bl_Code") & ""
                        StiRpt("单位名称") = HisVar.HisVar.WsyName
                        StiRpt("交费日期") = My_Row.Item("Jf_Date")
                        StiRpt("单据号码") = "JF" & My_Row.Item("Jf_Code")
                        StiRpt("姓名") = My_Row.Item("Ry_Name") & ""
                        StiRpt("病历编码") = My_Row.Item("Ry_BlCode") & ""
                        StiRpt("科室名称") = My_Row.Item("Ks_Name") & ""
                        StiRpt("床位") = HisVar.HisVar.Sqldal.GetSingle("Select Bc_Name from Zd_YyBc Where Bc_Code='" & My_Row.Item("Bc_Code") & "'") & ""
                        StiRpt("金额") = "￥" & My_Row.Item("Jf_Money")
                        StiRpt("收款人") = HisVar.HisVar.JsrName

                    Else
                        StiRpt.Load("Rpt\住院押金缴费单.mrt")
                        StiRpt.ReportName = "住院押金缴费单"

                        StiRpt.Compile()
                        StiRpt("标题") = HisVar.HisVar.WsyName & "住院押金缴费单"
                        StiRpt("打印日期") = "打印日期：" + Format(Now, "yyyy年MM月dd日")
                        StiRpt("单据号码") = "JF" & My_Row.Item("Jf_Code")
                        StiRpt("交费日期") = My_Row.Item("Jf_Date")
                        StiRpt("姓名") = My_Row.Item("Ry_Name")
                        StiRpt("入院编码") = My_Row.Item("Bl_Code")
                        StiRpt("病历编码") = My_Row.Item("Ry_BlCode") & ""
                        StiRpt("科室名称") = My_Row.Item("Ks_Name")
                        StiRpt("金额") = "￥" & My_Row.Item("Jf_Money")
                        StiRpt("打印人") = "打印人：" & HisVar.HisVar.JsrName
                        StiRpt("收款人") = HisVar.HisVar.JsrName

                    End If


                    Dim Money_Dx As New BaseClass.ChineseNum

                    If My_Row.Item("Jf_Money") >= 0 Then
                        Money_Dx.InputString = My_Row.Item("Jf_Money")
                    Else
                        Money_Dx.InputString = -My_Row.Item("Jf_Money")
                    End If

                    If Money_Dx.Valiad = True Then
                        If My_Row.Item("Jf_Money") >= 0 Then
                            StiRpt("大写") = Money_Dx.OutString
                        Else
                            StiRpt("大写") = "负" & Money_Dx.OutString

                        End If
                    Else
                        MsgBox("输入金额有误,请检查后重新打印", MsgBoxStyle.Critical, "提示")
                        Exit Sub
                    End If
                    'StiRpt.Design()
                    StiRpt.Show()

                End If

        End Select

    End Sub
    Private Sub GridMove(ByVal s As String)
        With MyGrid1
            If .RowCount = 0 Then Exit Sub
            Select Case s
                Case "最前"
                    .MoveFirst()
                Case "上移"
                    .MovePrevious()
                Case "下移"
                    .MoveNext()
                Case "最后"
                    .MoveLast()
                    T_Label.Text = "∑=" + .Splits(0).Rows.Count.ToString
            End Select
        End With
    End Sub
#End Region

#Region "自定义函数"

    Private Sub P_ShowData(ByVal V_Lb As String)
        If V_Lb = "增加" Then
            If Format(C1DateEdit2.Value, "yyyy-MM-dd") <> Format(Now, "yyyy-MM-dd") Then
                If MsgBox("您将要录入的时间为【" & Format(C1DateEdit2.Value, "yyyy-MM-dd") & "】与本地时间【" & Format(Now, "yyyy-MM-dd") & "】不一致，是否修正为【" & Format(Now, "yyyy-MM-dd") & "】？", MsgBoxStyle.Critical + MsgBoxStyle.DefaultButton1 + MsgBoxStyle.YesNo, "提示") = MsgBoxResult.Yes Then
                    C1DateEdit2.Value = Now
                End If
            End If
        End If

        Dim vform As New Xs_Zy_Yj12(Nothing, C1DateEdit2.Value, AddressOf DataAdd)
        If BaseFunc.BaseFunc.CheckOwnForm(Me, vform) = False Then
            'If RadioButton1.Checked = True Then
            '    vform.yjdlb = RadioButton1.Text
            'Else
            '    vform.yjdlb = RadioButton2.Text
            'End If
            'vform.Owner = Me
            vform.ShowDialog()
        End If

        MyGrid1.Select()



    End Sub

    Private Sub DataAdd(ByVal m_Jf As Modelold.M_Bl_Jf)
        Dim My_NewRow As DataRow = My_Table.NewRow
        With My_NewRow
            .Item("Yy_Code") = m_Jf.Yy_Code
            .Item("Bl_Code") = m_Jf.Bl_Code
            .Item("Jf_Code") = m_Jf.Jf_Code
            .Item("Jf_Date") = m_Jf.Jf_Date
            .Item("Jsr_Code") = m_Jf.Jsr_Code
            .Item("Jf_Money") = m_Jf.Jf_Money
            .Item("Jf_Memo") = m_Jf.Jf_Memo
            .Item("Bl_Jffs") = m_Jf.Bl_Jffs
            .Item("Ry_Name") = m_Jf.Ry_Name
            .Item("Ks_Name") = m_Jf.Ks_Name
            .Item("Ys_Name") = m_Jf.Ys_Name
            .Item("Jsr_Name") = m_Jf.Jsr_Name
            .Item("Ry_BlCode") = m_Jf.Ry_BlCode
        End With
        My_Table.Rows.Add(My_NewRow)
        MyGrid1.MoveLast()
    End Sub

#End Region

    Private Sub C1DateEdit2_Validated(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1DateEdit2.Validated
        Dim V_New_Date As String = Format(C1DateEdit2.Value, "yyyy-MM-dd")
        If My_Date <> V_New_Date Then
            My_Date = V_New_Date
            Call Init_Data()
        End If
    End Sub

    Private Sub C1DateEdit2_KeyPress(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles C1DateEdit2.KeyPress
        If e.KeyChar = Chr(Keys.Enter) Then
            e.Handled = True
            MyGrid1.Select()
        End If
    End Sub


    Private Sub RadioButton1_CheckedChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles RadioButton1.CheckedChanged
        If form_load Then
            If RadioButton1.Checked = True Then
                iniOperate.iniopreate.WriteINI("参数", "押金单样式", RadioButton1.Text, HisVar.HisVar.Parapath & "\Config.ini")
            End If
        End If
        
    End Sub

    Private Sub RadioButton2_CheckedChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles RadioButton2.CheckedChanged
        If form_load Then
            If RadioButton2.Checked = True Then
                iniOperate.iniopreate.WriteINI("参数", "押金单样式", RadioButton2.Text, HisVar.HisVar.Parapath & "\Config.ini")
            End If
        End If
        
    End Sub
End Class
