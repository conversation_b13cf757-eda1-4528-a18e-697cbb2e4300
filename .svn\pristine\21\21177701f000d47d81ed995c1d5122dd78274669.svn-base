﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<Localization language="Chinese (Simplified)" description="Chinese (Simplified)" cultureName="zh-CHS">
  <A_WebViewer>
    <DayFriday>周五</DayFriday>
    <DayMonday>周一</DayMonday>
    <DaySaturday>周六</DaySaturday>
    <DaySunday>周日</DaySunday>
    <DayThursday>周四</DayThursday>
    <DayTuesday>周二</DayTuesday>
    <DayWednesday>周三</DayWednesday>
    <FirstPage>首页</FirstPage>
    <LastPage>末页</LastPage>
    <Loading>加载...</Loading>
    <MonthApril>4月</MonthApril>
    <MonthAugust>8月</MonthAugust>
    <MonthDecember>12月</MonthDecember>
    <MonthFebruary>2月</MonthFebruary>
    <MonthJanuary>1月</MonthJanuary>
    <MonthJuly>7月</MonthJuly>
    <MonthJune>6月</MonthJune>
    <MonthMarch>3月</MonthMarch>
    <MonthMay>5月</MonthMay>
    <MonthNovember>11月</MonthNovember>
    <MonthOctober>10月</MonthOctober>
    <MonthSeptember>9月</MonthSeptember>
    <NextPage>后页</NextPage>
    <OnePage>一页</OnePage>
    <Page>页</Page>
    <PageOf>of</PageOf>
    <PreviousPage>前页</PreviousPage>
    <PrintReport>打印报表</PrintReport>
    <PrintToPdf>打印为PDF</PrintToPdf>
    <PrintWithoutPreview>直接打印</PrintWithoutPreview>
    <PrintWithPreview>打印预览</PrintWithPreview>
    <SaveReport>保存报表</SaveReport>
    <TodayDate>今天</TodayDate>
    <WholeReport>整个报表</WholeReport>
  </A_WebViewer>
  <Adapters>
    <AdapterBusinessObjects>从业务对象中获取数据</AdapterBusinessObjects>
    <AdapterCsvFiles>从 Csv File 获取数据</AdapterCsvFiles>
    <AdapterDataTables>从 DataSet, DataTables 获取数据</AdapterDataTables>
    <AdapterDataViews>从 DataViews 获取数据</AdapterDataViews>
    <AdapterDB2Connection>从 IBM DB2 连接获取数据</AdapterDB2Connection>
    <AdapterDBaseFiles>从数据库文件获取数据</AdapterDBaseFiles>
    <AdapterFirebirdConnection>从 Firebird 连接获取数据</AdapterFirebirdConnection>
    <AdapterMySQLConnection>从 MySQL 连接获取数据</AdapterMySQLConnection>
    <AdapterOdbcConnection>从 Odbc 连接获取数据</AdapterOdbcConnection>
    <AdapterOleDbConnection>从 OleDb 连接获取数据</AdapterOleDbConnection>
    <AdapterOracleConnection>从 Oracle 连接获取数据</AdapterOracleConnection>
    <AdapterPostgreSQLConnection>从 PostgreSQL 连接获取数据</AdapterPostgreSQLConnection>
    <AdapterSqlCeConnection>从 SqlCe 连接获取数据</AdapterSqlCeConnection>
    <AdapterSqlConnection>从 Sql 连接获取数据</AdapterSqlConnection>
    <AdapterSQLiteConnection>从 SQLite 连接获取数据</AdapterSQLiteConnection>
    <AdapterUniDirectConnection>Uni Direct数据库连接</AdapterUniDirectConnection>
    <AdapterUserSources>从用户源获取数据</AdapterUserSources>
    <AdapterVirtualSource>从其他数据源获取数据</AdapterVirtualSource>
    <AdapterVistaDBConnection>从 VistaDB 连接获取数据</AdapterVistaDBConnection>
  </Adapters>
  <Buttons>
    <Add>添加</Add>
    <AddAllColumns>添加所有列</AddAllColumns>
    <Build>生成...</Build>
    <Cancel>取消(&amp;C)</Cancel>
    <Close>关闭</Close>
    <Delete>删除</Delete>
    <Design>设计</Design>
    <Down>向下</Down>
    <Duplicate>重复</Duplicate>
    <Export>导出</Export>
    <Help>帮助</Help>
    <MoveLeft>左移</MoveLeft>
    <MoveRight>右移</MoveRight>
    <Ok>确定(&amp;O)</Ok>
    <Open>打开</Open>
    <Print>打印</Print>
    <Remove>移除</Remove>
    <Rename>重命名</Rename>
    <RestoreDefaults>恢复默认值</RestoreDefaults>
    <Save>保存</Save>
    <Test>测试</Test>
    <Up>向上</Up>
  </Buttons>
  <Chart>
    <AddCondition>添加条件(&amp;A)</AddCondition>
    <AddConstantLine>添加恒定线</AddConstantLine>
    <AddFilter>添加过滤(&amp;A)</AddFilter>
    <AddSeries>添加(&amp;A)</AddSeries>
    <AddStrip>添加长条</AddStrip>
    <Area>面积图</Area>
    <Axes>坐标</Axes>
    <AxisReverse>反转</AxisReverse>
    <AxisX>X 轴</AxisX>
    <AxisY>Y 轴</AxisY>
    <ChartConditionsCollectionForm>条件</ChartConditionsCollectionForm>
    <ChartFiltersCollectionForm>过滤</ChartFiltersCollectionForm>
    <ChartType>图表类型</ChartType>
    <CheckBoxAutoRotation>自动旋转</CheckBoxAutoRotation>
    <ClusteredBar>簇状条形图
</ClusteredBar>
    <ClusteredColumn>簇状柱形图</ClusteredColumn>
    <ConstantLine>恒定线</ConstantLine>
    <ConstantLinesEditorForm>恒定线编辑器</ConstantLinesEditorForm>
    <DataColumns>数据列</DataColumns>
    <Doughnut>圆环图</Doughnut>
    <FullStackedArea>全堆积面积图</FullStackedArea>
    <FullStackedBar>全堆积条形图</FullStackedBar>
    <FullStackedColumn>全堆积柱形图</FullStackedColumn>
    <FullStackedLine>全堆积折线图</FullStackedLine>
    <FullStackedSpline>全堆积曲线图</FullStackedSpline>
    <FullStackedSplineArea>全堆积曲线面积图</FullStackedSplineArea>
    <Gantt>甘特图</Gantt>
    <GridInterlaced>隔行</GridInterlaced>
    <GridLines>网格线</GridLines>
    <LabelAlignment>对齐方式:</LabelAlignment>
    <LabelAlignmentHorizontal>水平:</LabelAlignmentHorizontal>
    <LabelAlignmentVertical>垂直:</LabelAlignmentVertical>
    <LabelAngle>角:</LabelAngle>
    <LabelArgumentDataColumn>数据列变量:</LabelArgumentDataColumn>
    <LabelAutoRotation>自动旋转:</LabelAutoRotation>
    <LabelHorizontal>水平:</LabelHorizontal>
    <LabelMinorCount>Minor Count:</LabelMinorCount>
    <Labels>标签</Labels>
    <LabelsCenter>居中</LabelsCenter>
    <LabelSeriesName>Series 名称:</LabelSeriesName>
    <LabelsInside>之内</LabelsInside>
    <LabelsInsideBase>基点之内</LabelsInsideBase>
    <LabelsInsideEnd>顶点之内</LabelsInsideEnd>
    <LabelsNone>无</LabelsNone>
    <LabelsOutside>之外</LabelsOutside>
    <LabelsOutsideBase>基点之外</LabelsOutsideBase>
    <LabelsOutsideEnd>顶点之外</LabelsOutsideEnd>
    <LabelsTwoColumns>两栏</LabelsTwoColumns>
    <LabelTextAfter>文本之后:</LabelTextAfter>
    <LabelTextBefore>文本之前:</LabelTextBefore>
    <LabelTitleAlignment>对齐方式:</LabelTitleAlignment>
    <LabelValueDataColumn>数据列值:</LabelValueDataColumn>
    <LabelValueType>值类型:</LabelValueType>
    <LabelVertical>垂直:</LabelVertical>
    <LabelVisible>可见:</LabelVisible>
    <Legend>图例</Legend>
    <LegendSpacing>间距</LegendSpacing>
    <Line>折线图</Line>
    <ListOfValues>列列表</ListOfValues>
    <Marker>标记</Marker>
    <MoveConstantLineDown>下移恒定线</MoveConstantLineDown>
    <MoveConstantLineUp>上移恒定线</MoveConstantLineUp>
    <MoveSeriesDown>下移序列</MoveSeriesDown>
    <MoveSeriesUp>上移序列</MoveSeriesUp>
    <MoveStripDown>下移长条</MoveStripDown>
    <MoveStripUp>上移长条</MoveStripUp>
    <NoConditions>无条件</NoConditions>
    <NoFilters>无过滤</NoFilters>
    <Pie>饼图</Pie>
    <RemoveCondition>移除条件(&amp;R)</RemoveCondition>
    <RemoveConstantLine>移除恒定线</RemoveConstantLine>
    <RemoveFilter>移除过滤(&amp;R)</RemoveFilter>
    <RemoveSeries>移除序列(&amp;R)</RemoveSeries>
    <RemoveStrip>移除长条</RemoveStrip>
    <RunChartWizard>运行图表 &amp;向导</RunChartWizard>
    <Scatter>矢量图</Scatter>
    <ScatterLine>矢量直线</ScatterLine>
    <ScatterSpline>矢量曲线</ScatterSpline>
    <Series>序列</Series>
    <SeriesColorsCollectionForm>序列颜色</SeriesColorsCollectionForm>
    <SeriesEditorForm>序列编辑器</SeriesEditorForm>
    <Serieses>序列</Serieses>
    <Spline>曲线图</Spline>
    <SplineArea>曲线面积图</SplineArea>
    <StackedArea>堆积面积图</StackedArea>
    <StackedBar>堆积条形图</StackedBar>
    <StackedColumn>堆积柱形图</StackedColumn>
    <StackedLine>堆积折线图</StackedLine>
    <StackedSpline>堆积曲线图</StackedSpline>
    <StackedSplineArea>堆积曲线面积图</StackedSplineArea>
    <SteppedArea>阶梯面积图</SteppedArea>
    <SteppedLine>阶梯折线图</SteppedLine>
    <Strip>长条</Strip>
    <StripsEditorForm>长条编辑表单</StripsEditorForm>
    <Style>样式</Style>
  </Chart>
  <Components>
    <StiBarCode>条形码</StiBarCode>
    <StiChart>图表</StiChart>
    <StiCheckBox>复选框</StiCheckBox>
    <StiChildBand>子级区</StiChildBand>
    <StiClone>克隆</StiClone>
    <StiColumnFooterBand>栏尾</StiColumnFooterBand>
    <StiColumnHeaderBand>栏首</StiColumnHeaderBand>
    <StiComponent>组件</StiComponent>
    <StiContainer>容器</StiContainer>
    <StiContourText>交互式轮廓文本</StiContourText>
    <StiCrossColumn>交叉-列</StiCrossColumn>
    <StiCrossColumnTotal>交叉-列合计</StiCrossColumnTotal>
    <StiCrossDataBand>交叉-数据区</StiCrossDataBand>
    <StiCrossFooterBand>交叉-页脚</StiCrossFooterBand>
    <StiCrossGroupFooterBand>交叉-分组页脚</StiCrossGroupFooterBand>
    <StiCrossGroupHeaderBand>交叉-分组页眉</StiCrossGroupHeaderBand>
    <StiCrossHeaderBand>交叉-页眉</StiCrossHeaderBand>
    <StiCrossRow>交叉-行</StiCrossRow>
    <StiCrossRowTotal>交叉-行合计</StiCrossRowTotal>
    <StiCrossSummary>交叉-合计</StiCrossSummary>
    <StiCrossSummaryHeader>交叉汇总头</StiCrossSummaryHeader>
    <StiCrossTab>交叉-Tab</StiCrossTab>
    <StiDataBand>数据区</StiDataBand>
    <StiEmptyBand>空白区</StiEmptyBand>
    <StiFooterBand>页脚</StiFooterBand>
    <StiGroupFooterBand>分组页脚</StiGroupFooterBand>
    <StiGroupHeaderBand>分组页眉</StiGroupHeaderBand>
    <StiHeaderBand>页眉</StiHeaderBand>
    <StiHierarchicalBand>分级区</StiHierarchicalBand>
    <StiHorizontalLinePrimitive>横线</StiHorizontalLinePrimitive>
    <StiImage>图片</StiImage>
    <StiOverlayBand>覆盖</StiOverlayBand>
    <StiPage>页</StiPage>
    <StiPageFooterBand>页脚</StiPageFooterBand>
    <StiPageHeaderBand>页眉</StiPageHeaderBand>
    <StiPanel>面板</StiPanel>
    <StiRectanglePrimitive>矩形</StiRectanglePrimitive>
    <StiReport>报表</StiReport>
    <StiReportSummaryBand>报表合计区</StiReportSummaryBand>
    <StiReportTitleBand>报表标题区</StiReportTitleBand>
    <StiRichText>富文本</StiRichText>
    <StiRoundedRectanglePrimitive>圆角矩形</StiRoundedRectanglePrimitive>
    <StiShape>形状</StiShape>
    <StiSubReport>子报表</StiSubReport>
    <StiSystemText>系统文本</StiSystemText>
    <StiTable>表格</StiTable>
    <StiText>文本</StiText>
    <StiTextInCells>单元格文本</StiTextInCells>
    <StiVerticalLinePrimitive>竖线</StiVerticalLinePrimitive>
    <StiWinControl>Windows 控件</StiWinControl>
    <StiZipCode>邮政编码</StiZipCode>
  </Components>
  <Database>
    <Connection>连接</Connection>
    <DatabaseDB2>IBM DB2 连接</DatabaseDB2>
    <DatabaseFirebird>Firebird 连接</DatabaseFirebird>
    <DatabaseMySQL>MySQL 连接</DatabaseMySQL>
    <DatabaseOdbc>Odbc 连接</DatabaseOdbc>
    <DatabaseOleDb>OleDB 连接</DatabaseOleDb>
    <DatabaseOracle>Oracle 连接</DatabaseOracle>
    <DatabasePostgreSQL>PostgreSQL 连接</DatabasePostgreSQL>
    <DatabaseSql>Sql 连接</DatabaseSql>
    <DatabaseSqlCe>SQLServerCE 连接</DatabaseSqlCe>
    <DatabaseSQLite>SQLite连接</DatabaseSQLite>
    <DatabaseUniDirect>Uni直接连接</DatabaseUniDirect>
    <DatabaseVistaDB>VistaDB 连接</DatabaseVistaDB>
    <DatabaseXml>Xml 数据</DatabaseXml>
  </Database>
  <Dialogs>
    <StiButtonControl>按钮</StiButtonControl>
    <StiCheckBoxControl>复选框</StiCheckBoxControl>
    <StiCheckedListBoxControl>复选列表框</StiCheckedListBoxControl>
    <StiComboBoxControl>下拉框</StiComboBoxControl>
    <StiDateTimePickerControl>日期时间</StiDateTimePickerControl>
    <StiForm>窗体</StiForm>
    <StiGridControl>网格</StiGridControl>
    <StiGroupBoxControl>分组框</StiGroupBoxControl>
    <StiLabelControl>标签</StiLabelControl>
    <StiListBoxControl>列表框</StiListBoxControl>
    <StiListViewControl>查看列表</StiListViewControl>
    <StiLookUpBoxControl>查找框</StiLookUpBoxControl>
    <StiNumericUpDownControl>数值</StiNumericUpDownControl>
    <StiPanelControl>面板</StiPanelControl>
    <StiPictureBoxControl>图片框</StiPictureBoxControl>
    <StiRadioButtonControl>单选框</StiRadioButtonControl>
    <StiReportControl>报表控件</StiReportControl>
    <StiRichTextBoxControl>Rich Text Box</StiRichTextBoxControl>
    <StiTextBoxControl>文件框</StiTextBoxControl>
    <StiTreeViewControl>树视图</StiTreeViewControl>
  </Dialogs>
  <Editor>
    <CantFind>找不到您所要查找的数据.</CantFind>
    <CollapseToDefinitions>折叠到定义(&amp;O)</CollapseToDefinitions>
    <Column>列: {0}</Column>
    <EntireScope>整个范围(&amp;E)</EntireScope>
    <Find>查找(&amp;F)</Find>
    <FindNext>查找下一个(&amp;F)</FindNext>
    <FindWhat>查找内容:</FindWhat>
    <FromCursor>从光标</FromCursor>
    <GotoLine>跳到行(&amp;L)</GotoLine>
    <Line>行: {0}</Line>
    <LineNumber>行号:</LineNumber>
    <LineNumberIndex>行号 ({0} - {1})</LineNumberIndex>
    <MarkAll>全部标注(&amp;M)</MarkAll>
    <MatchCase>区分大小写(&amp;M)</MatchCase>
    <MatchWholeWord>全字匹配(&amp;W)</MatchWholeWord>
    <Outlining>大纲视图(&amp;L)</Outlining>
    <PromptOnReplace>替换时提示</PromptOnReplace>
    <Replace>替换(&amp;R)</Replace>
    <ReplaceAll>全部替换(&amp;A)</ReplaceAll>
    <ReplaceWith>替换为:</ReplaceWith>
    <Search>查找</Search>
    <SearchHiddenText>隐藏文本</SearchHiddenText>
    <SearchUp>向上查找(&amp;U)</SearchUp>
    <SelectionOnly>选定文本(&amp;O)</SelectionOnly>
    <ShowLineNumbers>显示行号</ShowLineNumbers>
    <StopOutlining>停止大纲视图(&amp;P)</StopOutlining>
    <titleFind>查找</titleFind>
    <titleGotoLine>跳到行</titleGotoLine>
    <titleReplace>替换</titleReplace>
    <ToggleAllOutlining>全部切换到大纲视图(&amp;L)</ToggleAllOutlining>
    <ToggleOutliningExpansion>切换到大纲视图扩展(&amp;T)</ToggleOutliningExpansion>
    <UseRegularExpressions>正则表达式(&amp;R)</UseRegularExpressions>
  </Editor>
  <Errors>
    <ComponentIsNotRelease>组件未释放 "{0}".</ComponentIsNotRelease>
    <ContainerIsNotValidForComponent>组件 {1} 不能使用容器 {0}.</ContainerIsNotValidForComponent>
    <DataNotFound>数据未找到.</DataNotFound>
    <Error>出错!</Error>
    <FieldRequire>字段 "{0}" 需要填充.</FieldRequire>
    <FileNotFound>文件 "{0}" 未找到.</FileNotFound>
    <IdentifierIsNotValid>标识 '{0}' 不合法.</IdentifierIsNotValid>
    <ImpossibleFindDataSource>无法找到数据源.</ImpossibleFindDataSource>
    <NameExists>已经有名称为 '{0}' 的对象存在. 对象名区分大小字且必须唯一.</NameExists>
    <NoServices>在 '{0}'找不到服务</NoServices>
    <NotAssign>数据源未定义.</NotAssign>
    <RelationsNotFound>找不到关系定义.</RelationsNotFound>
    <ServiceNotFound>找不到服务 '{0}'.</ServiceNotFound>
  </Errors>
  <Export>
    <AddPageBreaks>增加分页符</AddPageBreaks>
    <AllowAddOrModifyTextAnnotations>允许添加或修改文本附注</AllowAddOrModifyTextAnnotations>
    <AllowCopyTextAndGraphics>允许复制文字和图片</AllowCopyTextAndGraphics>
    <AllowModifyContents>允许修改内容</AllowModifyContents>
    <AllowPrintDocument>允许打印文档</AllowPrintDocument>
    <Color>颜色</Color>
    <Compressed>压缩</Compressed>
    <DigitalSignature>数字签名</DigitalSignature>
    <DocumentSecurity>文档安全</DocumentSecurity>
    <DotMatrixMode>点阵模式</DotMatrixMode>
    <EmbeddedFonts>嵌入字体</EmbeddedFonts>
    <Encoding>编码:</Encoding>
    <ExportDataOnly>仅导出数据</ExportDataOnly>
    <ExportEachPageToSheet>导出每页到工作表</ExportEachPageToSheet>
    <ExportingCalculatingCoordinates>正在计算坐标</ExportingCalculatingCoordinates>
    <ExportingCreatingDocument>创建文档</ExportingCreatingDocument>
    <ExportingFormatingObjects>正在格式化对象</ExportingFormatingObjects>
    <ExportingReport>正在导出报表</ExportingReport>
    <ExportMode>导出模式:</ExportMode>
    <ExportModeFrame>框架</ExportModeFrame>
    <ExportModeTable>表</ExportModeTable>
    <ExportObjectFormatting>导出格式化对象</ExportObjectFormatting>
    <ExportPageBreaks>导出分页符</ExportPageBreaks>
    <ExportRtfTextAsImage>将Rtf文字导出为图片</ExportRtfTextAsImage>
    <ExportTypeBmpFile>BMP 图...</ExportTypeBmpFile>
    <ExportTypeCalcFile>打开 Calc File 文档...</ExportTypeCalcFile>
    <ExportTypeCsvFile>CSV 文件...</ExportTypeCsvFile>
    <ExportTypeDbfFile>DBF 文件...</ExportTypeDbfFile>
    <ExportTypeDifFile>数据交换格式(DIF)文件...</ExportTypeDifFile>
    <ExportTypeExcel2007File>Microsoft Excel 2007/2010 文件...</ExportTypeExcel2007File>
    <ExportTypeExcelFile>Microsoft Excel 文件...</ExportTypeExcelFile>
    <ExportTypeExcelXmlFile>Microsoft Excel Xml 文件...</ExportTypeExcelXmlFile>
    <ExportTypeGifFile>GIF 图片...</ExportTypeGifFile>
    <ExportTypeHtmlFile>HTML 文件...</ExportTypeHtmlFile>
    <ExportTypeJpegFile>JPEG 图片...</ExportTypeJpegFile>
    <ExportTypeMetafile>Windows 图元文件...</ExportTypeMetafile>
    <ExportTypeMhtFile>MHT Web Archive文件...</ExportTypeMhtFile>
    <ExportTypePcxFile>PCX 图像...</ExportTypePcxFile>
    <ExportTypePdfFile>Adobe PDF 文件...</ExportTypePdfFile>
    <ExportTypePngFile>PNG 图片...</ExportTypePngFile>
    <ExportTypeRtfFile>RTF 文件...</ExportTypeRtfFile>
    <ExportTypeSylkFile>符号链接(SYLK)文件...</ExportTypeSylkFile>
    <ExportTypeTiffFile>TIFF 图片...</ExportTypeTiffFile>
    <ExportTypeTxtFile>Text 文件...</ExportTypeTxtFile>
    <ExportTypeWord2007File>Microsoft Word 2007/2010 文件...</ExportTypeWord2007File>
    <ExportTypeWriterFile>打开 Writer File 文档...</ExportTypeWriterFile>
    <ExportTypeXmlFile>XML 文件...</ExportTypeXmlFile>
    <ExportTypeXpsFile>Microsoft XPS 文件...</ExportTypeXpsFile>
    <GetCertificateFromCryptoUI>从 Crypto UI 获取证书</GetCertificateFromCryptoUI>
    <ImageCompressionMethod>图像压缩方法:</ImageCompressionMethod>
    <ImageCutEdges>裁边</ImageCutEdges>
    <ImageFormat>图片格式:</ImageFormat>
    <ImageGrayscale>灰阶</ImageGrayscale>
    <ImageMonochrome>单色</ImageMonochrome>
    <ImageQuality>图像质量</ImageQuality>
    <ImageResolution>清晰度:</ImageResolution>
    <ImageType>图片类型</ImageType>
    <labelEncryptionKeyLength>密钥长度:</labelEncryptionKeyLength>
    <labelOwnerPassword>所有者密码:</labelOwnerPassword>
    <labelSubjectNameString>主题命名字符串:</labelSubjectNameString>
    <labelUserPassword>用户密码:</labelUserPassword>
    <MoreSettings>更多设置</MoreSettings>
    <MultipleFiles>Multiple 文件</MultipleFiles>
    <OpenAfterExport>导出后打开</OpenAfterExport>
    <Separator>分隔符:</Separator>
    <Settings>设置</Settings>
    <SkipColumnHeaders>略过列头</SkipColumnHeaders>
    <StandardPDFFonts>标准PDF字体</StandardPDFFonts>
    <title>导出设置</title>
    <TxtBorderType>边框类型</TxtBorderType>
    <TxtBorderTypeDouble>Unicode-双线</TxtBorderTypeDouble>
    <TxtBorderTypeSimple>单线</TxtBorderTypeSimple>
    <TxtBorderTypeSingle>Unicode-单线</TxtBorderTypeSingle>
    <TxtCutLongLines>截断长行</TxtCutLongLines>
    <TxtDrawBorder>绘制边框</TxtDrawBorder>
    <TxtKillSpaceGraphLines>消除空行</TxtKillSpaceGraphLines>
    <TxtKillSpaceLines>去除空行</TxtKillSpaceLines>
    <TxtPutFeedPageCode>放置页码</TxtPutFeedPageCode>
    <UseDefaultSystemEncoding>使用默认的系统编码</UseDefaultSystemEncoding>
    <UseDigitalSignature>使用数字签名</UseDigitalSignature>
    <UseOnePageHeaderAndFooter>用同一页眉和页脚</UseOnePageHeaderAndFooter>
    <UsePageHeadersAndFooters>使用页眉页尾</UsePageHeadersAndFooters>
    <UseUnicode>用 Unicode 编码</UseUnicode>
    <X>X:</X>
    <Y>Y:</Y>
    <Zoom>缩放:</Zoom>
  </Export>
  <FileFilters>
    <AllImageFiles>全部图片文件</AllImageFiles>
    <BitmapFiles>Bitmap 文件</BitmapFiles>
    <BmpFiles>BMP 图片 (*.bmp)|*.bmp</BmpFiles>
    <CalcFiles>打开 Calc files (*.ods)|*.ods 文档</CalcFiles>
    <CsvFiles>CSV 文件 (*.csv)|*.csv</CsvFiles>
    <DataSetXmlData>DataSet XML data (*.xml)|*.xml</DataSetXmlData>
    <DataSetXmlSchema>DataSet XML schema (*.xsd)|*.xsd</DataSetXmlSchema>
    <DbfFiles>DBF 文件 (*.dbf)|*.dbf</DbfFiles>
    <DictionaryFiles>Files字典 (*.dct)|*.dct</DictionaryFiles>
    <DifFiles>DIF文件(*.dif)|*.dif</DifFiles>
    <DllFiles>DLL 文件 (*.dll)|*.dll</DllFiles>
    <DocumentFiles>Files document (*.mdc)|*.mdc</DocumentFiles>
    <EmfFiles>Metafile (*.emf)|*.emf</EmfFiles>
    <Excel2007Files>Microsoft Excel 2007/2010 files (*.xlsx)|*.xlsx</Excel2007Files>
    <ExcelFiles>Microsoft Excel 文件 (*.xls)|*.xls</ExcelFiles>
    <ExcelXmlFiles>Microsoft Excel XML 文件 (*.xls)|*.xls</ExcelXmlFiles>
    <ExeFiles>EXE 文件 (*.exe)|*.exe</ExeFiles>
    <GifFiles>GIF 图片 (*.gif)|*.gif</GifFiles>
    <HtmlFiles>HTML 文件 (*.html)|*.html</HtmlFiles>
    <InheritedLanguageFiles>{0} 继承报表文件 (*.{1})|*.{2}</InheritedLanguageFiles>
    <JpegFiles>JPEG 图片 (*.jpeg)|*.jpeg</JpegFiles>
    <LanguageFiles>{0} 文件 (*.{1})|*.{2}</LanguageFiles>
    <MetaFiles>元文件</MetaFiles>
    <MhtFiles>MHT Web Archive 文件 (*.mht)|*.mht</MhtFiles>
    <PackedDocumentFiles>Files packed document (*.mdz)|*.mdz</PackedDocumentFiles>
    <PackedReportFiles>Files packed report (*.mrz)|*.mrz</PackedReportFiles>
    <PageFiles>Files page (*.pg)|*.pg</PageFiles>
    <PcxFiles>PCX 图像 (*.pcx)|*.pcx</PcxFiles>
    <PdfFiles>Adobe PDF files (*.pdf)|*.pdf</PdfFiles>
    <PngFiles>PNG 图片 (*.png)|*.png</PngFiles>
    <ReportFiles>Files report (*.mrt)|*.mrt</ReportFiles>
    <RtfFiles>Rich Text (*.rtf)|*.rtf</RtfFiles>
    <StylesFiles>Files style (*.sts)|*.sts</StylesFiles>
    <SylkFiles>SYLK文件(*.slk)|*.slk</SylkFiles>
    <TiffFiles>TIFF 图片 (*.tiff)|*.tiff</TiffFiles>
    <TxtFiles>Text (*.txt)|*.txt</TxtFiles>
    <Word2007Files>Microsoft Word 2007/2010 files (*.docx)|*.docx</Word2007Files>
    <WriterFiles>打开 Writer files (*.odt)|*.odt 文档</WriterFiles>
    <XmlFiles>XML 文件 (*.xml)|*.xml</XmlFiles>
    <XpsFiles>Microsoft XPS files (*.xps)|*.xps</XpsFiles>
  </FileFilters>
  <Formats>
    <custom01>d</custom01>
    <custom02>D</custom02>
    <custom03>f</custom03>
    <custom04>F</custom04>
    <custom05>yy/MM/dd</custom05>
    <custom06>yyyy/MM/dd</custom06>
    <custom07>G</custom07>
    <custom08>$0.00</custom08>
    <custom09>$0</custom09>
    <custom10>c</custom10>
    <custom11>c1</custom11>
    <custom12>c2</custom12>
    <custom13>#.00</custom13>
    <custom14>#,#</custom14>
    <custom15>n</custom15>
    <custom16>n1</custom16>
    <custom17>n2</custom17>
    <custom18>(###) ### - ####</custom18>
    <date01>*d</date01>
    <date02>*D</date02>
    <date03>M.dd</date03>
    <date04>yy.M.dd</date04>
    <date05>yy.MM.dd</date05>
    <date06>MMM.dd</date06>
    <date07>yy.MMM.dd</date07>
    <date08>yyyy, MMMM</date08>
    <date09>*f</date09>
    <date10>*F</date10>
    <date11>MM.dd.yyyy</date11>
    <date12>dd/MM/yyyy</date12>
    <time01>t</time01>
    <time02>T</time02>
    <time03>HH:mm</time03>
    <time04>H:mm</time04>
    <time06>HH:mm:ss</time06>
  </Formats>
  <FormBand>
    <AddFilter>添加过滤(&amp;A)</AddFilter>
    <AddGroup>添加分组(&amp;A)</AddGroup>
    <AddResult>添加结果(&amp;A)</AddResult>
    <AddSort>添加排序(&amp;A)</AddSort>
    <And>和</And>
    <Ascending>升序</Ascending>
    <Descending>降序</Descending>
    <NoFilters>不过滤</NoFilters>
    <NoSort>没有排序</NoSort>
    <RemoveFilter>移除过滤(&amp;R)</RemoveFilter>
    <RemoveGroup>移除分组(&amp;R)</RemoveGroup>
    <RemoveResult>移除结果(&amp;R)</RemoveResult>
    <RemoveSort>移除排序(&amp;R)</RemoveSort>
    <SortBy>主排序</SortBy>
    <ThenBy>次排序</ThenBy>
    <title>数据设置</title>
  </FormBand>
  <FormColorBoxPopup>
    <Color>颜色</Color>
    <Custom>自定义</Custom>
    <Others>其它...</Others>
    <System>系统</System>
    <Web>Web</Web>
  </FormColorBoxPopup>
  <FormConditions>
    <AaBbCcYyZz>AaBbCcYyZz</AaBbCcYyZz>
    <AddCondition>添加条件(&amp;A)</AddCondition>
    <AddLevel>添加级别</AddLevel>
    <AssignExpression>分配表达式</AssignExpression>
    <ChangeFont>改变字体</ChangeFont>
    <ComponentIsEnabled>组件已激活</ComponentIsEnabled>
    <NoConditions>无条件</NoConditions>
    <RemoveCondition>移除条件(&amp;R)</RemoveCondition>
    <SelectStyle>选择样式</SelectStyle>
    <title>条件</title>
  </FormConditions>
  <FormCrossTabDesigner>
    <Columns>列:</Columns>
    <DataSource>数据源:</DataSource>
    <Properties>属性:</Properties>
    <Rows>行:</Rows>
    <Summary>统计:</Summary>
    <Swap>交换行列</Swap>
    <title>交叉报表设计</title>
  </FormCrossTabDesigner>
  <FormDatabaseEdit>
    <ConnectionString>连接字符串:</ConnectionString>
    <DB2Edit>编辑 IBM DB2 连接</DB2Edit>
    <DB2New>新建 IBM DB2 连接</DB2New>
    <FirebirdEdit>编辑 Firebird 连接</FirebirdEdit>
    <FirebirdNew>新建 Firebird 连接</FirebirdNew>
    <InitialCatalog>初始化分类:</InitialCatalog>
    <MySQLEdit>编辑 MySQL 连接</MySQLEdit>
    <MySQLNew>新建 MySQL 连接</MySQLNew>
    <OdbcEdit>编辑 Odbc 连接</OdbcEdit>
    <OdbcNew>新建 Odbc 连接</OdbcNew>
    <OleDbEdit>编辑 OleDb 连接</OleDbEdit>
    <OleDbNew>新建 OleDb 连接</OleDbNew>
    <OracleEdit>编辑 Oracle 连接</OracleEdit>
    <OracleNew>新建 Oracle 连接</OracleNew>
    <PathData>路径到 XML Data:</PathData>
    <PathSchema>路径到 XSD Schema:</PathSchema>
    <PostgreSQLEdit>编辑 PostgreSQL 连接</PostgreSQLEdit>
    <PostgreSQLNew>新建 PostgreSQL 连接</PostgreSQLNew>
    <PromptUserNameAndPassword>提示用户名和密码</PromptUserNameAndPassword>
    <SqlCeEdit>编辑 SQLServerCE 连接</SqlCeEdit>
    <SqlCeNew>新建 SQLServerCE 连接</SqlCeNew>
    <SqlEdit>编辑 Sql 连接</SqlEdit>
    <SQLiteEdit>编辑 SQLite 连接</SQLiteEdit>
    <SQLiteNew>新建 SQLite 连接</SQLiteNew>
    <SqlNew>新建 Sql 连接</SqlNew>
    <UniDirectEdit>编辑Uni Direct连接</UniDirectEdit>
    <UniDirectNew>新建Uni Direct连接</UniDirectNew>
    <VistaDBEdit>编辑 VistaDB 连接</VistaDBEdit>
    <VistaDBNew>新建 VistaDB 连接</VistaDBNew>
    <XmlEdit>编辑 Xml Data</XmlEdit>
    <XmlNew>新建 Xml Data</XmlNew>
  </FormDatabaseEdit>
  <FormDesigner>
    <Code>代码</Code>
    <ColumnsOne>一</ColumnsOne>
    <ColumnsThree>三</ColumnsThree>
    <ColumnsTwo>二</ColumnsTwo>
    <CompilingReport>编译报表</CompilingReport>
    <DockingPanels>面板</DockingPanels>
    <HtmlPreview>HTML 预览</HtmlPreview>
    <labelPleaseSelectTypeOfInterface>请选择接口类型</labelPleaseSelectTypeOfInterface>
    <LoadImage>加载图片...</LoadImage>
    <LocalizePropertyGrid>本地化属性</LocalizePropertyGrid>
    <MarginsNarrow>窄</MarginsNarrow>
    <MarginsNormal>正常</MarginsNormal>
    <MarginsWide>宽</MarginsWide>
    <OrderToolbars>重置工具栏</OrderToolbars>
    <Pages>页面</Pages>
    <Preview>预览</Preview>
    <PropertyChange>属性改变 '{0}'</PropertyChange>
    <SetupToolbox>设计工具栏</SetupToolbox>
    <ShowDescription>显示描述</ShowDescription>
    <title>设计器</title>
  </FormDesigner>
  <FormDictionaryDesigner>
    <Actions>动作</Actions>
    <AutoSort>自动排序</AutoSort>
    <CalcColumnEdit>编辑计算列</CalcColumnEdit>
    <CalcColumnNew>新建计算列</CalcColumnNew>
    <CategoryEdit>编辑分类</CategoryEdit>
    <CategoryNew>新建分类</CategoryNew>
    <ChildSource>子数据源:</ChildSource>
    <ColumnEdit>编辑列</ColumnEdit>
    <ColumnNew>新建列</ColumnNew>
    <DatabaseEdit>编辑数据库</DatabaseEdit>
    <DatabaseNew>新建数据库</DatabaseNew>
    <DataParameterEdit>编辑参数</DataParameterEdit>
    <DataParameterNew>新建参数</DataParameterNew>
    <DataSourceEdit>编辑数据源</DataSourceEdit>
    <DataSourceNew>新建数据源</DataSourceNew>
    <DataSourcesNew>新建数据源</DataSourcesNew>
    <Delete>删除</Delete>
    <DesignTimeQueryText>设计时查询文本</DesignTimeQueryText>
    <DictionaryMerge>合并字典...</DictionaryMerge>
    <DictionaryNew>新建字典...</DictionaryNew>
    <DictionaryOpen>打开字典...</DictionaryOpen>
    <DictionarySaveAs>保存字典为...</DictionarySaveAs>
    <EditQuery>编辑查询</EditQuery>
    <ExecutedSQLStatementSuccessfully>SQL脚本执行成功</ExecutedSQLStatementSuccessfully>
    <GetColumnsFromAssembly>从集合获取列</GetColumnsFromAssembly>
    <ImportRelations>导入关系</ImportRelations>
    <MarkUsedItems>标注已使用项目</MarkUsedItems>
    <NewItem>新项目</NewItem>
    <OpenAssembly>打开集合</OpenAssembly>
    <ParentSource>父数据库:</ParentSource>
    <QueryText>查询文本</QueryText>
    <RelationEdit>编辑关系</RelationEdit>
    <RelationNew>新建关系</RelationNew>
    <RetrieveColumns>获取所有列</RetrieveColumns>
    <Run>运行</Run>
    <SortItems>排序项目</SortItems>
    <Synchronize>同步</Synchronize>
    <SynchronizeHint>同步数据源和字典内容</SynchronizeHint>
    <title>字典设计器</title>
    <VariableEdit>编辑变量</VariableEdit>
    <VariableNew>新建变量</VariableNew>
    <ViewData>查看数据</ViewData>
    <ViewQuery>查看查询</ViewQuery>
  </FormDictionaryDesigner>
  <FormFormatEditor>
    <Boolean>布尔型</Boolean>
    <BooleanDisplay>显示:</BooleanDisplay>
    <BooleanValue>值:</BooleanValue>
    <Currency>货币</Currency>
    <CurrencySymbol>货币符号:</CurrencySymbol>
    <Custom>自定义</Custom>
    <Date>日期</Date>
    <DateTimeFormat>日期时间格式</DateTimeFormat>
    <DecimalDigits>数值:</DecimalDigits>
    <DecimalSeparator>数值分隔符:</DecimalSeparator>
    <FormatMask>格式:</FormatMask>
    <Formats>格式:</Formats>
    <General>常规</General>
    <GroupSeparator>分组符:</GroupSeparator>
    <GroupSize>分组尺寸:</GroupSize>
    <nameFalse>假</nameFalse>
    <nameNo>否</nameNo>
    <nameOff>关</nameOff>
    <nameOn>开</nameOn>
    <nameTrue>真</nameTrue>
    <nameYes>是</nameYes>
    <NegativePattern>负数:</NegativePattern>
    <Number>数字</Number>
    <Percentage>百分数</Percentage>
    <PercentageSymbol>百分号:</PercentageSymbol>
    <PositivePattern>正数:</PositivePattern>
    <Properties>属性</Properties>
    <Sample>示例</Sample>
    <SampleText>示例文本</SampleText>
    <TextFormat>文本格式</TextFormat>
    <Time>时间</Time>
    <title>格式</title>
    <UseGroupSeparator>使用分组符</UseGroupSeparator>
    <UseLocalSetting>使用本地设置</UseLocalSetting>
  </FormFormatEditor>
  <FormGlobalizationEditor>
    <AddCulture>添加 Culture(&amp;A)</AddCulture>
    <AutoLocalizeReportOnRun>运行时自动本地化报表</AutoLocalizeReportOnRun>
    <RemoveCulture>移除 Culture(&amp;R)</RemoveCulture>
    <title>全局编辑器</title>
  </FormGlobalizationEditor>
  <FormOptions>
    <AutoSave>自动保存</AutoSave>
    <AutoSaveReportToReportClass>自动保存报表到 C# 或 Vb.Net 文件</AutoSaveReportToReportClass>
    <Default>默认</Default>
    <Drawing>图纸</Drawing>
    <DrawMarkersWhenMoving>移动时绘制标注</DrawMarkersWhenMoving>
    <EditAfterInsert>插入后编辑</EditAfterInsert>
    <EnableAutoSaveMode>开启自动保存模式</EnableAutoSaveMode>
    <FillBands>填充区</FillBands>
    <FillComponents>填充组件</FillComponents>
    <FillContainers>填充容器</FillContainers>
    <FillCrossBands>填充交叉区</FillCrossBands>
    <GenerateLocalizedName>生成本地化名称</GenerateLocalizedName>
    <Grid>网格</Grid>
    <GridDots>点</GridDots>
    <GridLines>线</GridLines>
    <GridMode>网格模式</GridMode>
    <GridSize>网格大小</GridSize>
    <groupAutoSaveOptions>自动保存选项</groupAutoSaveOptions>
    <groupColorScheme>请选择Gui配色方案</groupColorScheme>
    <groupGridDrawingOptions>网格绘制选项</groupGridDrawingOptions>
    <groupGridOptions>网格选项</groupGridOptions>
    <groupGridSize>网格大小</groupGridSize>
    <groupMainOptions>工作报表设计主要选项</groupMainOptions>
    <groupMarkersStyle>标志样式</groupMarkersStyle>
    <groupOptionsOfQuickInfo>快速信息选项</groupOptionsOfQuickInfo>
    <groupPleaseSelectTypeOfGui>请选择GUI类型</groupPleaseSelectTypeOfGui>
    <groupReportDisplayOptions>报表显示选项</groupReportDisplayOptions>
    <labelColorScheme>配色方案：</labelColorScheme>
    <labelInfoAutoSave>改变报表自动保存参数</labelInfoAutoSave>
    <labelInfoDrawing>设置报表绘制参数</labelInfoDrawing>
    <labelInfoGrid>在所用的报表中如何显示网格</labelInfoGrid>
    <labelInfoGui>在报表设计中选择所使用的GUI模式</labelInfoGui>
    <labelInfoMain>设置报表设计基本参数</labelInfoMain>
    <labelInfoQuickInfo>组件提示信息页</labelInfoQuickInfo>
    <Main>主体</Main>
    <MarkersStyle>标注样式</MarkersStyle>
    <MarkersStyleCorners>三角</MarkersStyleCorners>
    <MarkersStyleDashedRectangle>虚线矩形</MarkersStyleDashedRectangle>
    <MarkersStyleNone>无</MarkersStyleNone>
    <Minutes>{0} 分钟</Minutes>
    <SaveReportEvery>保存全部报表</SaveReportEvery>
    <SelectUILanguage>选择界面语言</SelectUILanguage>
    <ShowDimensionLines>显示尺寸线</ShowDimensionLines>
    <title>选项</title>
    <UseComponentColor>用组件颜色填充</UseComponentColor>
    <UseLastFormat>使用上次格式</UseLastFormat>
  </FormOptions>
  <FormPageSetup>
    <ApplyTo>应用于</ApplyTo>
    <Bottom>下:</Bottom>
    <Columns>栏</Columns>
    <groupColumns>页面栏</groupColumns>
    <groupImage>水印图像</groupImage>
    <groupMargins>页边距</groupMargins>
    <groupOrientation>纸张方向</groupOrientation>
    <groupPaper>纸张大小</groupPaper>
    <groupPaperSource>纸张来源</groupPaperSource>
    <groupText>水印文字</groupText>
    <Height>高:</Height>
    <labelAngle>角度：</labelAngle>
    <labelColumnGaps>列距：</labelColumnGaps>
    <labelColumnWidth>列宽:</labelColumnWidth>
    <labelImageAlignment>图像对齐：</labelImageAlignment>
    <labelImageTransparency>图像透明度：</labelImageTransparency>
    <labelInfoColumns>设置页面栏数</labelInfoColumns>
    <labelInfoPaper>设置当前页的大小和方向</labelInfoPaper>
    <labelInfoUnit>指定当前单元格的页边距</labelInfoUnit>
    <labelInfoWatermark>设置水印显示参数</labelInfoWatermark>
    <labelMultipleFactor>多重因素:</labelMultipleFactor>
    <labelPaperSourceOfFirstPage>首页纸张来源：</labelPaperSourceOfFirstPage>
    <labelPaperSourceOfOtherPages>其它页纸张来源:</labelPaperSourceOfOtherPages>
    <labelSelectColor>选择颜色:</labelSelectColor>
    <labelSelectFont>选择字体:</labelSelectFont>
    <labelSelectImage>选择图像:</labelSelectImage>
    <labelText>文字:</labelText>
    <Left>左:</Left>
    <Margins>页边距</Margins>
    <NumberOfColumns>栏数:</NumberOfColumns>
    <Orientation>方向</Orientation>
    <PageOrientationLandscape>横向</PageOrientationLandscape>
    <PageOrientationPortrait>纵向</PageOrientationPortrait>
    <Paper>纸张:</Paper>
    <RebuildReport>重新生成报表</RebuildReport>
    <Right>右:</Right>
    <ScaleContent>缩放内容</ScaleContent>
    <Size>尺寸:</Size>
    <title>页面设置</title>
    <Top>上:</Top>
    <Width>宽:</Width>
  </FormPageSetup>
  <FormReportSetup>
    <groupDates>报表数据创建和最新数据变化</groupDates>
    <groupDescription>报表描述</groupDescription>
    <groupMainParameters>报表渲染影响参数</groupMainParameters>
    <groupNames>报表名称, 报表别名, 报表作者</groupNames>
    <groupScript>报表脚本语言</groupScript>
    <groupUnits>报表的大小和坐标在指定单位内</groupUnits>
    <labelInfoDescription>显示报表信息</labelInfoDescription>
    <labelInfoMain>改变报表基本参数</labelInfoMain>
    <labelNumberOfPass>密码数字:</labelNumberOfPass>
    <labelReportCacheMode>报告缓存模式：</labelReportCacheMode>
    <ReportChanged>修改时间:</ReportChanged>
    <ReportCreated>创建时间:</ReportCreated>
    <title>报表设置</title>
  </FormReportSetup>
  <FormRichTextEditor>
    <Bullets>项目符号</Bullets>
    <FontName>字体名称</FontName>
    <FontSize>字体大小</FontSize>
    <Insert>插入表达式</Insert>
    <title>Rich Text 编辑器</title>
  </FormRichTextEditor>
  <FormStyleDesigner>
    <Add>添加样式</Add>
    <Duplicate>复制样式</Duplicate>
    <Open>打开样式</Open>
    <Remove>移除样式</Remove>
    <Save>保存样式</Save>
    <Style>样式</Style>
    <title>样式设计器</title>
  </FormStyleDesigner>
  <FormSystemTextEditor>
    <Condition>条件</Condition>
    <LabelDataBand>数据绑定</LabelDataBand>
    <LabelDataColumn>数据列</LabelDataColumn>
    <LabelShowInsteadNullValues>显示替代null值:</LabelShowInsteadNullValues>
    <LabelSummaryFunction>聚合函数</LabelSummaryFunction>
    <pageExpression>表达式</pageExpression>
    <pageSummary>合计</pageSummary>
    <pageSystemVariable>系统变量</pageSystemVariable>
    <RunningTotal>运行总计</RunningTotal>
    <SummaryRunning>运行时聚合</SummaryRunning>
    <SummaryRunningByColumn>列</SummaryRunningByColumn>
    <SummaryRunningByPage>页</SummaryRunningByPage>
    <SummaryRunningByReport>报表</SummaryRunningByReport>
  </FormSystemTextEditor>
  <FormTitles>
    <ConditionEditorForm>条件</ConditionEditorForm>
    <ConnectionSelectForm>选择连接类型</ConnectionSelectForm>
    <ContainerSelectForm>选择容器</ContainerSelectForm>
    <DataAdapterServiceSelectForm>选择数据类型</DataAdapterServiceSelectForm>
    <DataRelationSelectForm>选择数据关系</DataRelationSelectForm>
    <DataSetName>输入 DataSet 名称</DataSetName>
    <DataSourceSelectForm>选择数据源</DataSourceSelectForm>
    <DataSourcesNewForm>新建数据源</DataSourcesNewForm>
    <DataStoreViewerForm>数据存储浏览器</DataStoreViewerForm>
    <EventEditorForm>事件编辑器</EventEditorForm>
    <ExpressionEditorForm>表达式编辑器</ExpressionEditorForm>
    <GroupConditionForm>分组条件</GroupConditionForm>
    <InteractionDrillDownPageSelectForm>选择钻取页</InteractionDrillDownPageSelectForm>
    <MasterComponentSelectForm>选择父级组件</MasterComponentSelectForm>
    <PageAddForm>添加页面</PageAddForm>
    <PageSizeForm>页面尺寸</PageSizeForm>
    <PagesManagerForm>页面管理器</PagesManagerForm>
    <PromptForm>输入登录信息到数据库</PromptForm>
    <ServiceSelectForm>选择服务</ServiceSelectForm>
    <SqlExpressionsForm>Sql 表达式</SqlExpressionsForm>
    <SubReportPageSelectForm>选择子报表页面</SubReportPageSelectForm>
    <TextEditorForm>文本编辑器</TextEditorForm>
    <ViewDataForm>查看数据</ViewDataForm>
  </FormTitles>
  <FormViewer>
    <Bookmarks>书签</Bookmarks>
    <Close>关闭</Close>
    <CollapseAll>全部折叠</CollapseAll>
    <ContextMenu>右键菜单</ContextMenu>
    <DocumentFile>文档 File...</DocumentFile>
    <Editor>编辑器</Editor>
    <ExpandAll>全部展开</ExpandAll>
    <Export>导出...</Export>
    <Find>查找</Find>
    <FirstPage>首页</FirstPage>
    <FullScreen>全屏</FullScreen>
    <GoToPage>转到页</GoToPage>
    <HorScrollBar>水平滚动条</HorScrollBar>
    <JumpToPage>跳到页</JumpToPage>
    <LabelPageN>页:</LabelPageN>
    <LastPage>末页</LastPage>
    <NextPage>下页</NextPage>
    <Open>打开...</Open>
    <PageControl>页面控制</PageControl>
    <PageDelete>删除页面</PageDelete>
    <PageDesign>编辑页面...</PageDesign>
    <PageNew>新页</PageNew>
    <PageNofM>{0} / {1}</PageNofM>
    <PageofM>/ {0}</PageofM>
    <PageSize>页面大小...</PageSize>
    <PageViewModeContinuous>连续</PageViewModeContinuous>
    <PageViewModeMultiplePages>多页</PageViewModeMultiplePages>
    <PageViewModeSinglePage>单页</PageViewModeSinglePage>
    <PrevPage>上页</PrevPage>
    <Print>打印...</Print>
    <qnPageDelete>您是否要删除页面?</qnPageDelete>
    <Save>保存...</Save>
    <SendEMail>发送邮件...</SendEMail>
    <StatusBar>状态栏</StatusBar>
    <Thumbnails>缩略图</Thumbnails>
    <title>预览</title>
    <titlePageSettings>页面设置</titlePageSettings>
    <Toolbar>工具栏</Toolbar>
    <VerScrollBar>垂直滚动条</VerScrollBar>
    <ViewMode>查看方式</ViewMode>
    <Zoom>缩放</Zoom>
    <ZoomMultiplePages>多页</ZoomMultiplePages>
    <ZoomOnePage>整页</ZoomOnePage>
    <ZoomPageWidth>页宽</ZoomPageWidth>
    <ZoomTwoPages>双页</ZoomTwoPages>
    <ZoomXXPages>{0} X {1} 页</ZoomXXPages>
    <ZoomXXPagesCancel>取消</ZoomXXPagesCancel>
  </FormViewer>
  <FormViewerFind>
    <Close>关闭</Close>
    <FindNext>查找下一个</FindNext>
    <FindWhat>查找内容:</FindWhat>
  </FormViewerFind>
  <Gui>
    <barname_cancel>取消</barname_cancel>
    <barname_caption>新建工具栏</barname_caption>
    <barname_msginvalidname>工具栏名称不能为空。</barname_msginvalidname>
    <barname_name>工具栏名称(&amp;T):</barname_name>
    <barname_ok>确定</barname_ok>
    <barrename_caption>重命名工具栏</barrename_caption>
    <barsys_autohide_tooltip>自动隐藏</barsys_autohide_tooltip>
    <barsys_close_tooltip>关闭</barsys_close_tooltip>
    <barsys_customize_tooltip>自定义</barsys_customize_tooltip>
    <colorpicker_morecolors>更多颜色(&amp;M)...</colorpicker_morecolors>
    <colorpicker_nofill>无填充(&amp;N)</colorpicker_nofill>
    <colorpicker_standardcolorslabel>标准颜色</colorpicker_standardcolorslabel>
    <colorpicker_themecolorslabel>主题颜色</colorpicker_themecolorslabel>
    <colorpickerdialog_bluelabel>蓝(&amp;B):</colorpickerdialog_bluelabel>
    <colorpickerdialog_cancelbutton>取消</colorpickerdialog_cancelbutton>
    <colorpickerdialog_caption>颜色</colorpickerdialog_caption>
    <colorpickerdialog_colormodellabel>颜色模式:</colorpickerdialog_colormodellabel>
    <colorpickerdialog_currentcolorlabel>当前</colorpickerdialog_currentcolorlabel>
    <colorpickerdialog_customcolorslabel>颜色:</colorpickerdialog_customcolorslabel>
    <colorpickerdialog_greenlabel>绿(&amp;G):</colorpickerdialog_greenlabel>
    <colorpickerdialog_newcolorlabel>新建</colorpickerdialog_newcolorlabel>
    <colorpickerdialog_okbutton>确定</colorpickerdialog_okbutton>
    <colorpickerdialog_redlabel>红(&amp;R):</colorpickerdialog_redlabel>
    <colorpickerdialog_rgblabel>RGB</colorpickerdialog_rgblabel>
    <colorpickerdialog_standardcolorslabel>颜色:</colorpickerdialog_standardcolorslabel>
    <colorpickerdialog_tabcustom>典型</colorpickerdialog_tabcustom>
    <colorpickerdialog_tabstandard>标准</colorpickerdialog_tabstandard>
    <cust_btn_close>关闭</cust_btn_close>
    <cust_btn_delete>删除</cust_btn_delete>
    <cust_btn_keyboard>键盘(&amp;K)...</cust_btn_keyboard>
    <cust_btn_new>新建(&amp;N)...</cust_btn_new>
    <cust_btn_rename>重命名(&amp;R)...</cust_btn_rename>
    <cust_btn_reset>重置(&amp;R)...</cust_btn_reset>
    <cust_btn_resetusage>重置我的使用情况数据(&amp;R)</cust_btn_resetusage>
    <cust_caption>自定义</cust_caption>
    <cust_cbo_fade>褪色度</cust_cbo_fade>
    <cust_cbo_none>(无)</cust_cbo_none>
    <cust_cbo_random>随机</cust_cbo_random>
    <cust_cbo_slide>幻灯片</cust_cbo_slide>
    <cust_cbo_system>系统默认设置</cust_cbo_system>
    <cust_cbo_unfold>展开</cust_cbo_unfold>
    <cust_chk_delay>在短暂延迟后显示所有菜单</cust_chk_delay>
    <cust_chk_fullmenus>始终显示所有菜单</cust_chk_fullmenus>
    <cust_chk_showsk>在屏幕提示中显示快捷键(&amp;S)</cust_chk_showsk>
    <cust_chk_showst>在工具栏上显示屏幕提示(&amp;T)</cust_chk_showst>
    <cust_lbl_cats>类别(&amp;G):</cust_lbl_cats>
    <cust_lbl_cmds>命令(&amp;D):</cust_lbl_cmds>
    <cust_lbl_cmdsins>要向工具栏添加命令，请选择类别，并将命令从此框中拖放到某个工具栏上。</cust_lbl_cmdsins>
    <cust_lbl_menuan>菜单动画:</cust_lbl_menuan>
    <cust_lbl_other>其他:</cust_lbl_other>
    <cust_lbl_pmt>个性化菜单和工具栏</cust_lbl_pmt>
    <cust_lbl_tlbs>工具栏(&amp;A):</cust_lbl_tlbs>
    <cust_mnu_addremove>添加或删除按钮(&amp;A)</cust_mnu_addremove>
    <cust_mnu_cust>自定义...</cust_mnu_cust>
    <cust_mnu_reset>重置工具栏</cust_mnu_reset>
    <cust_mnu_tooltip>工具栏选项</cust_mnu_tooltip>
    <cust_msg_delete>是否确实要删除 &lt;barname&gt; 工具栏？</cust_msg_delete>
    <cust_pm_begingroup>开始编组</cust_pm_begingroup>
    <cust_pm_delete>删除</cust_pm_delete>
    <cust_pm_name>名称:</cust_pm_name>
    <cust_pm_reset>重置</cust_pm_reset>
    <cust_pm_stydef>默认样式</cust_pm_stydef>
    <cust_pm_styimagetext>图像和文字 (始终)</cust_pm_styimagetext>
    <cust_pm_stytextonly>仅文字 (始终)</cust_pm_stytextonly>
    <cust_tab_commands>命令</cust_tab_commands>
    <cust_tab_options>选项</cust_tab_options>
    <cust_tab_toolbars>工具栏</cust_tab_toolbars>
    <mdisysmenu_close>关闭</mdisysmenu_close>
    <mdisysmenu_maximize>最大化</mdisysmenu_maximize>
    <mdisysmenu_minimize>最小化</mdisysmenu_minimize>
    <mdisysmenu_move>移动</mdisysmenu_move>
    <mdisysmenu_next>下一个</mdisysmenu_next>
    <mdisysmenu_restore>恢复</mdisysmenu_restore>
    <mdisysmenu_size>大小</mdisysmenu_size>
    <mdisystt_close>关闭</mdisystt_close>
    <mdisystt_minimize>最小化</mdisystt_minimize>
    <mdisystt_restore>恢复</mdisystt_restore>
    <navbar_navpaneoptions>导航面板选项(&amp;V)...</navbar_navpaneoptions>
    <navbar_showfewerbuttons>显示较少按钮(&amp;F)</navbar_showfewerbuttons>
    <navbar_showmorebuttons>显示较多按钮(&amp;M)</navbar_showmorebuttons>
    <navPaneCollapseTooltip>折叠导航面板</navPaneCollapseTooltip>
    <navPaneExpandTooltip>展开导航面板</navPaneExpandTooltip>
    <sys_custombar>自定义列</sys_custombar>
    <sys_morebuttons>其他按钮</sys_morebuttons>
  </Gui>
  <MainMenu>
    <menuContextClone>克隆...</menuContextClone>
    <menuContextDesign>设计...</menuContextDesign>
    <menuContextTextFormat>文字格式...</menuContextTextFormat>
    <menuEdit>编辑(&amp;E)</menuEdit>
    <menuEditCalcColumnNew>新建计算列...</menuEditCalcColumnNew>
    <menuEditCantRedo>无法重做</menuEditCantRedo>
    <menuEditCantUndo>无法撤销</menuEditCantUndo>
    <menuEditCategoryNew>新建分类...</menuEditCategoryNew>
    <menuEditClearContents>清除内容</menuEditClearContents>
    <menuEditColumnNew>新建列...</menuEditColumnNew>
    <menuEditConnectionNew>新建连接...</menuEditConnectionNew>
    <menuEditCopy>复制(&amp;C)</menuEditCopy>
    <menuEditCut>剪切(&amp;U)</menuEditCut>
    <menuEditDataParameterNew>新建参数...</menuEditDataParameterNew>
    <menuEditDataSourceNew>新建数据源...</menuEditDataSourceNew>
    <menuEditDataSourcesNew>新建数据源...</menuEditDataSourcesNew>
    <menuEditDelete>删除(&amp;D)</menuEditDelete>
    <menuEditEdit>编辑</menuEditEdit>
    <menuEditImportRelations>导入关系...</menuEditImportRelations>
    <menuEditPaste>粘贴(&amp;P)</menuEditPaste>
    <menuEditRedo>重做(&amp;R)</menuEditRedo>
    <menuEditRedoText>重做(&amp;R){0}</menuEditRedoText>
    <menuEditRelationNew>新建关系...</menuEditRelationNew>
    <menuEditRemoveUnused>移除未使用的项目</menuEditRemoveUnused>
    <menuEditSelectAll>全选(&amp;A)</menuEditSelectAll>
    <menuEditSynchronize>同步</menuEditSynchronize>
    <menuEditUndo>撤销(&amp;U)</menuEditUndo>
    <menuEditUndoText>撤销(&amp;U) {0}</menuEditUndoText>
    <menuEditVariableNew>新建变量...</menuEditVariableNew>
    <menuEditViewData>查看数据...</menuEditViewData>
    <menuFile>文件(&amp;F)</menuFile>
    <menuFileClose>关闭(&amp;C)</menuFileClose>
    <menuFileExit>退出(&amp;X)</menuFileExit>
    <menuFileExportXMLSchema>导出为XML Schema...</menuFileExportXMLSchema>
    <menuFileFormNew>新建窗体</menuFileFormNew>
    <menuFileImportXMLSchema>导入XML Schema...</menuFileImportXMLSchema>
    <menuFileMerge>合并...</menuFileMerge>
    <menuFileMergeXMLSchema>合并XML Schema...</menuFileMergeXMLSchema>
    <menuFileNew>新建(&amp;N)</menuFileNew>
    <menuFileOpen>打开(&amp;O)</menuFileOpen>
    <menuFilePageDelete>删除页面</menuFilePageDelete>
    <menuFilePageNew>新建页面</menuFilePageNew>
    <menuFilePageOpen>打开页面...</menuFilePageOpen>
    <menuFilePageSaveAs>保存页面为...</menuFilePageSaveAs>
    <menuFilePageSetup>页面设置...</menuFilePageSetup>
    <menuFileRecentDocuments>最近文档</menuFileRecentDocuments>
    <menuFileReportNew>新建报表(&amp;N)...</menuFileReportNew>
    <menuFileReportOpen>打开报表(&amp;O)...</menuFileReportOpen>
    <menuFileReportPreview>预览报表(&amp;P)...</menuFileReportPreview>
    <menuFileReportSave>保存报表(&amp;S)...</menuFileReportSave>
    <menuFileReportSaveAs>保存报表为(&amp;A)...</menuFileReportSaveAs>
    <menuFileReportSetup>报表设置(&amp;S)...</menuFileReportSetup>
    <menuFileReportWizardNew>新建报表向导...</menuFileReportWizardNew>
    <menuFileSaveAs>另存为...</menuFileSaveAs>
    <menuHelp>帮助(&amp;H)</menuHelp>
    <menuHelpAboutProgramm>关于(&amp;A)...</menuHelpAboutProgramm>
    <menuHelpContents>内容(&amp;C)...</menuHelpContents>
    <menuHelpFAQPage>FAQ</menuHelpFAQPage>
    <menuHelpHowToRegister>如何注册</menuHelpHowToRegister>
    <menuHelpProductHomePage>产品主页</menuHelpProductHomePage>
    <menuHelpSupport>技术支持(&amp;S)</menuHelpSupport>
    <menuTools>工具(&amp;T)</menuTools>
    <menuToolsDataStore>数据存储(&amp;S)...</menuToolsDataStore>
    <menuToolsDictionary>字典(&amp;D)...</menuToolsDictionary>
    <menuToolsOptions>选项(&amp;O)...</menuToolsOptions>
    <menuToolsPagesManager>页面管理(&amp;P)...</menuToolsPagesManager>
    <menuToolsServicesConfigurator>服务设置(&amp;C)...</menuToolsServicesConfigurator>
    <menuToolsStyleDesigner>样式设计(&amp;D)...</menuToolsStyleDesigner>
    <menuView>查看(&amp;V)</menuView>
    <menuViewAlignToGrid>对齐到网格</menuViewAlignToGrid>
    <menuViewNormal>普通(&amp;N)</menuViewNormal>
    <menuViewPageBreakPreview>分页预览(&amp;B)</menuViewPageBreakPreview>
    <menuViewQuickInfo>提示信息</menuViewQuickInfo>
    <menuViewQuickInfoNone>无</menuViewQuickInfoNone>
    <menuViewQuickInfoOverlay>显示于组件之上</menuViewQuickInfoOverlay>
    <menuViewQuickInfoShowAliases>显示别名</menuViewQuickInfoShowAliases>
    <menuViewQuickInfoShowComponentsNames>显示组件名称</menuViewQuickInfoShowComponentsNames>
    <menuViewQuickInfoShowContent>显示内容</menuViewQuickInfoShowContent>
    <menuViewQuickInfoShowEvents>显示事件</menuViewQuickInfoShowEvents>
    <menuViewQuickInfoShowFields>显示字段</menuViewQuickInfoShowFields>
    <menuViewQuickInfoShowFieldsOnly>只显示字段</menuViewQuickInfoShowFieldsOnly>
    <menuViewShowGrid>显示网格</menuViewShowGrid>
    <menuViewShowHeaders>显示标题栏</menuViewShowHeaders>
    <menuViewShowOrder>显示次序</menuViewShowOrder>
    <menuViewShowRulers>显示标尺</menuViewShowRulers>
    <menuViewToolbars>工具栏</menuViewToolbars>
  </MainMenu>
  <Panels>
    <Dictionary>字典</Dictionary>
    <Messages>消息</Messages>
    <Properties>属性</Properties>
    <ReportTree>报表树</ReportTree>
  </Panels>
  <PropertyCategory>
    <AppearanceCategory>外观</AppearanceCategory>
    <ArgumentCategory>参数</ArgumentCategory>
    <BarCodeAdditionalCategory>附加条形码</BarCodeAdditionalCategory>
    <BarCodeCategory>条形码</BarCodeCategory>
    <BehaviorCategory>行为</BehaviorCategory>
    <ChartAdditionalCategory>附加图表</ChartAdditionalCategory>
    <ChartCategory>图标</ChartCategory>
    <CheckCategory>复选</CheckCategory>
    <ColorsCategory>颜色</ColorsCategory>
    <ColumnsCategory>分栏</ColumnsCategory>
    <ControlCategory>控件</ControlCategory>
    <ControlsEventsCategory>控件事件</ControlsEventsCategory>
    <CrossTabCategory>交叉 Tab</CrossTabCategory>
    <DataCategory>数据</DataCategory>
    <DescriptionCategory>描述</DescriptionCategory>
    <DesignCategory>设计</DesignCategory>
    <DisplayCategory>显示</DisplayCategory>
    <ExportCategory>导出</ExportCategory>
    <ExportEventsCategory>导出事件</ExportEventsCategory>
    <HierarchicalCategory>分级</HierarchicalCategory>
    <ImageAdditionalCategory>附加图片</ImageAdditionalCategory>
    <ImageCategory>图片</ImageCategory>
    <MainCategory>Main</MainCategory>
    <MarkerCategory>标注</MarkerCategory>
    <MiscCategory>其他</MiscCategory>
    <MouseEventsCategory>鼠标事件</MouseEventsCategory>
    <NavigationCategory>导航</NavigationCategory>
    <NavigationEventsCategory>导航事件</NavigationEventsCategory>
    <OptionsCategory>选项</OptionsCategory>
    <PageAdditionalCategory>附加页面</PageAdditionalCategory>
    <PageCategory>页面</PageCategory>
    <PageColumnBreakCategory>分页</PageColumnBreakCategory>
    <ParametersCategory>参数</ParametersCategory>
    <PositionCategory>位置</PositionCategory>
    <PrimitiveCategory>图元</PrimitiveCategory>
    <PrintEventsCategory>打印事件</PrintEventsCategory>
    <RenderEventsCategory>渲染事件</RenderEventsCategory>
    <ShapeCategory>形状</ShapeCategory>
    <SubReportCategory>子报表</SubReportCategory>
    <TextAdditionalCategory>附加文本</TextAdditionalCategory>
    <TextCategory>文本</TextCategory>
    <TitleCategory>标题</TitleCategory>
    <ValueCategory>值</ValueCategory>
    <ValueEndCategory>Value End</ValueEndCategory>
    <ValueEventsCategory>值事件</ValueEventsCategory>
    <WinControlCategory>Windows 控件</WinControlCategory>
    <ZipCodeCategory>邮码</ZipCodeCategory>
  </PropertyCategory>
  <PropertyColor>
    <AliceBlue>湖色</AliceBlue>
    <AntiqueWhite>古典白色</AntiqueWhite>
    <Aqua>浅绿色</Aqua>
    <Aquamarine>海蓝色</Aquamarine>
    <Azure>天蓝色</Azure>
    <Beige>米色</Beige>
    <Bisque>桔黄色</Bisque>
    <Black>黑色</Black>
    <BlanchedAlmond>白杏色</BlanchedAlmond>
    <Blue>蓝色</Blue>
    <BlueViolet>蓝紫色</BlueViolet>
    <Brown>褐色</Brown>
    <BurlyWood>实木色</BurlyWood>
    <CadetBlue>刺桧蓝色</CadetBlue>
    <Chartreuse>亮黄绿色</Chartreuse>
    <Chocolate>巧克力色</Chocolate>
    <Coral>珊瑚色</Coral>
    <CornflowerBlue>矢车菊色</CornflowerBlue>
    <Cornsilk>谷丝色</Cornsilk>
    <Crimson>深红色</Crimson>
    <Cyan>青色</Cyan>
    <DarkBlue>深蓝色</DarkBlue>
    <DarkCyan>深青色</DarkCyan>
    <DarkGoldenrod>深金杆色</DarkGoldenrod>
    <DarkGray>深灰色</DarkGray>
    <DarkGreen>深绿色</DarkGreen>
    <DarkKhaki>深黄褐色</DarkKhaki>
    <DarkMagenta>深洋红色</DarkMagenta>
    <DarkOliveGreen>深橄榄绿色</DarkOliveGreen>
    <DarkOrange>深橙色</DarkOrange>
    <DarkOrchid>深紫色</DarkOrchid>
    <DarkRed>深红色</DarkRed>
    <DarkSalmon>深肉色</DarkSalmon>
    <DarkSeaGreen>深海绿色</DarkSeaGreen>
    <DarkSlateBlue>深暗灰蓝色</DarkSlateBlue>
    <DarkSlateGray>深暗蓝灰色</DarkSlateGray>
    <DarkTurquoise>深青绿色</DarkTurquoise>
    <DarkViolet>深紫色</DarkViolet>
    <DeepPink>深粉色</DeepPink>
    <DeepSkyBlue>深天蓝色</DeepSkyBlue>
    <DimGray>暗灰色</DimGray>
    <DodgerBlue>遮板蓝色</DodgerBlue>
    <Firebrick>砖色</Firebrick>
    <FloralWhite>花白色</FloralWhite>
    <ForestGreen>葱绿色</ForestGreen>
    <Fuchsia>紫红色</Fuchsia>
    <Gainsboro>庚斯博罗灰色</Gainsboro>
    <GhostWhite>幽灵白色</GhostWhite>
    <Gold>金黄色</Gold>
    <Goldenrod>金杆黄色</Goldenrod>
    <Gray>灰色</Gray>
    <Green>绿色</Green>
    <GreenYellow>绿黄色</GreenYellow>
    <Honeydew>蜜汁色</Honeydew>
    <HotPink>亮粉色</HotPink>
    <IndianRed>印第安红色</IndianRed>
    <Indigo>靛青色</Indigo>
    <Ivory>象牙色</Ivory>
    <Khaki>黄褐色</Khaki>
    <Lavender>淡紫色</Lavender>
    <LavenderBlush>浅紫红色</LavenderBlush>
    <LawnGreen>草绿色</LawnGreen>
    <LemonChiffon>柠檬纱色</LemonChiffon>
    <LightBlue>浅蓝色</LightBlue>
    <LightCoral>浅珊瑚色</LightCoral>
    <LightCyan>浅青色</LightCyan>
    <LightGoldenrodYellow>浅金杆黄色</LightGoldenrodYellow>
    <LightGray>浅灰色</LightGray>
    <LightGreen>浅绿色</LightGreen>
    <LightPink>浅粉色</LightPink>
    <LightSalmon>浅肉色</LightSalmon>
    <LightSeaGreen>浅海绿色</LightSeaGreen>
    <LightSkyBlue>浅天蓝色</LightSkyBlue>
    <LightSlateGray>浅暗蓝灰色</LightSlateGray>
    <LightSteelBlue>浅钢青色</LightSteelBlue>
    <LightYellow>浅黄色</LightYellow>
    <Lime>酸橙色</Lime>
    <LimeGreen>橙绿色</LimeGreen>
    <Linen>亚麻色</Linen>
    <Magenta>洋红色</Magenta>
    <Maroon>栗色
</Maroon>
    <MediumAquamarine>间绿色</MediumAquamarine>
    <MediumBlue>间蓝色</MediumBlue>
    <MediumOrchid>间紫色</MediumOrchid>
    <MediumPurple>间紫红色
</MediumPurple>
    <MediumSeaGreen>间海绿色</MediumSeaGreen>
    <MediumSlateBlue>间暗蓝色</MediumSlateBlue>
    <MediumSpringGreen>间春绿色</MediumSpringGreen>
    <MediumTurquoise>间绿宝石色</MediumTurquoise>
    <MediumVioletRed>间紫罗兰色</MediumVioletRed>
    <MidnightBlue>中灰蓝色</MidnightBlue>
    <MintCream>薄荷色</MintCream>
    <MistyRose>浅玫瑰色</MistyRose>
    <Moccasin>鹿皮色</Moccasin>
    <NavajoWhite>纳瓦白</NavajoWhite>
    <Navy>藏青色</Navy>
    <OldLace>老花色</OldLace>
    <Olive>橄榄色</Olive>
    <OliveDrab>深绿褐色</OliveDrab>
    <Orange>橙色</Orange>
    <OrangeRed>橙红色</OrangeRed>
    <Orchid>淡紫色</Orchid>
    <PaleGoldenrod>苍麒麟色</PaleGoldenrod>
    <PaleGreen>苍绿色</PaleGreen>
    <PaleTurquoise>苍绿松色</PaleTurquoise>
    <PaleVioletRed>苍紫罗蓝色</PaleVioletRed>
    <PapayaWhip>番木色</PapayaWhip>
    <PeachPuff>桃色</PeachPuff>
    <Peru>秘鲁色</Peru>
    <Pink>粉红色</Pink>
    <Plum>杨李色</Plum>
    <PowderBlue>粉蓝色</PowderBlue>
    <Purple>紫红色</Purple>
    <Red>红色</Red>
    <RosyBrown>褐玫瑰红</RosyBrown>
    <RoyalBlue>宝蓝色</RoyalBlue>
    <SaddleBrown>重褐色</SaddleBrown>
    <Salmon>鲜肉色</Salmon>
    <SandyBrown>沙褐色</SandyBrown>
    <SeaGreen>海绿色</SeaGreen>
    <SeaShell>海贝色</SeaShell>
    <Sienna>赭色</Sienna>
    <Silver>银白色</Silver>
    <SkyBlue>天蓝色</SkyBlue>
    <SlateBlue>石蓝色</SlateBlue>
    <SlateGray>灰石色</SlateGray>
    <Snow>雪白色</Snow>
    <SpringGreen>春绿色</SpringGreen>
    <SteelBlue>钢蓝色</SteelBlue>
    <Tan>茶色</Tan>
    <Teal>水鸭色</Teal>
    <Thistle>蓟色</Thistle>
    <Tomato>番茄色</Tomato>
    <Transparent>透明色</Transparent>
    <Turquoise>翠蓝色</Turquoise>
    <Violet>紫色</Violet>
    <Wheat>浅黄色</Wheat>
    <White>白色</White>
    <WhiteSmoke>烟白色</WhiteSmoke>
    <Yellow>黄色</Yellow>
    <YellowGreen>黄绿色</YellowGreen>
  </PropertyColor>
  <PropertyEnum>
    <boolFalse>假</boolFalse>
    <boolTrue>真</boolTrue>
    <BorderStyleFixed3D>Fixed 3D</BorderStyleFixed3D>
    <BorderStyleFixedSingle>Fixed Single</BorderStyleFixedSingle>
    <BorderStyleNone>无</BorderStyleNone>
    <ChartAxesTicksAll>全部</ChartAxesTicksAll>
    <ChartAxesTicksMajor>主要</ChartAxesTicksMajor>
    <ChartAxesTicksNone>无</ChartAxesTicksNone>
    <ChartGridLinesAll>全部</ChartGridLinesAll>
    <ChartGridLinesMajor>主要</ChartGridLinesMajor>
    <ChartGridLinesNone>无</ChartGridLinesNone>
    <ComboBoxStyleDropDown>下拉</ComboBoxStyleDropDown>
    <ComboBoxStyleDropDownList>下拉列表</ComboBoxStyleDropDownList>
    <ComboBoxStyleSimple>简单</ComboBoxStyleSimple>
    <ContentAlignmentBottomCenter>底端居中</ContentAlignmentBottomCenter>
    <ContentAlignmentBottomLeft>底端居左</ContentAlignmentBottomLeft>
    <ContentAlignmentBottomRight>底端居右</ContentAlignmentBottomRight>
    <ContentAlignmentMiddleCenter>中间居中</ContentAlignmentMiddleCenter>
    <ContentAlignmentMiddleLeft>中间居左</ContentAlignmentMiddleLeft>
    <ContentAlignmentMiddleRight>中间居右</ContentAlignmentMiddleRight>
    <ContentAlignmentTopCenter>顶端居中</ContentAlignmentTopCenter>
    <ContentAlignmentTopLeft>顶端居左</ContentAlignmentTopLeft>
    <ContentAlignmentTopRight>顶端居右</ContentAlignmentTopRight>
    <DataGridLineStyleNone>无</DataGridLineStyleNone>
    <DataGridLineStyleSolid>实线</DataGridLineStyleSolid>
    <DateTimePickerFormatCustom>自定义</DateTimePickerFormatCustom>
    <DateTimePickerFormatLong>长</DateTimePickerFormatLong>
    <DateTimePickerFormatShort>短</DateTimePickerFormatShort>
    <DateTimePickerFormatTime>时间</DateTimePickerFormatTime>
    <DialogResultAbort>中断</DialogResultAbort>
    <DialogResultCancel>取消</DialogResultCancel>
    <DialogResultIgnore>忽略</DialogResultIgnore>
    <DialogResultNo>否</DialogResultNo>
    <DialogResultNone>无</DialogResultNone>
    <DialogResultOK>是</DialogResultOK>
    <DialogResultRetry>重试</DialogResultRetry>
    <DialogResultYes>是</DialogResultYes>
    <DockStyleBottom>按钮</DockStyleBottom>
    <DockStyleFill>填充</DockStyleFill>
    <DockStyleLeft>左</DockStyleLeft>
    <DockStyleNone>无</DockStyleNone>
    <DockStyleRight>右</DockStyleRight>
    <DockStyleTop>上</DockStyleTop>
    <DuplexDefault>默认</DuplexDefault>
    <DuplexHorizontal>水平</DuplexHorizontal>
    <DuplexSimplex>单一</DuplexSimplex>
    <DuplexVertical>垂直</DuplexVertical>
    <FormStartPositionCenterParent>父窗体居中</FormStartPositionCenterParent>
    <FormStartPositionCenterScreen>屏幕居中</FormStartPositionCenterScreen>
    <FormStartPositionManual>手动</FormStartPositionManual>
    <FormStartPositionWindowsDefaultBounds>窗口默认范围</FormStartPositionWindowsDefaultBounds>
    <FormStartPositionWindowsDefaultLocation>窗口默认位置</FormStartPositionWindowsDefaultLocation>
    <FormWindowStateMaximized>最大化</FormWindowStateMaximized>
    <FormWindowStateMinimized>最小化</FormWindowStateMinimized>
    <FormWindowStateNormal>正常</FormWindowStateNormal>
    <HorizontalAlignmentCenter>居中</HorizontalAlignmentCenter>
    <HorizontalAlignmentLeft>居左</HorizontalAlignmentLeft>
    <HorizontalAlignmentRight>居右</HorizontalAlignmentRight>
    <HotkeyPrefixHide>隐藏</HotkeyPrefixHide>
    <HotkeyPrefixNone>无</HotkeyPrefixNone>
    <HotkeyPrefixShow>显示</HotkeyPrefixShow>
    <LeftRightAlignmentLeft>居左</LeftRightAlignmentLeft>
    <LeftRightAlignmentRight>居右</LeftRightAlignmentRight>
    <PictureBoxSizeModeAutoSize>自动调整</PictureBoxSizeModeAutoSize>
    <PictureBoxSizeModeCenterImage>图片中间</PictureBoxSizeModeCenterImage>
    <PictureBoxSizeModeNormal>正常</PictureBoxSizeModeNormal>
    <PictureBoxSizeModeStretchImage>适应图片</PictureBoxSizeModeStretchImage>
    <RightToLeftInherit>继承</RightToLeftInherit>
    <RightToLeftNo>否</RightToLeftNo>
    <RightToLeftYes>是</RightToLeftYes>
    <SelectionModeMultiExtended>扩展复选</SelectionModeMultiExtended>
    <SelectionModeMultiSimple>简单复选</SelectionModeMultiSimple>
    <SelectionModeNone>无</SelectionModeNone>
    <SelectionModeOne>单选</SelectionModeOne>
    <StiAngleAngle0>0度</StiAngleAngle0>
    <StiAngleAngle180>180度</StiAngleAngle180>
    <StiAngleAngle270>270度</StiAngleAngle270>
    <StiAngleAngle45>45度</StiAngleAngle45>
    <StiAngleAngle90>90度</StiAngleAngle90>
    <StiArrowStyleLines>线</StiArrowStyleLines>
    <StiArrowStyleNone>无</StiArrowStyleNone>
    <StiArrowStyleTriangle>三角形</StiArrowStyleTriangle>
    <StiBorderSidesAll>全部</StiBorderSidesAll>
    <StiBorderSidesBottom>底部</StiBorderSidesBottom>
    <StiBorderSidesLeft>左侧</StiBorderSidesLeft>
    <StiBorderSidesNone>无</StiBorderSidesNone>
    <StiBorderSidesRight>右侧</StiBorderSidesRight>
    <StiBorderSidesTop>上部</StiBorderSidesTop>
    <StiBorderStyleBump>隆起</StiBorderStyleBump>
    <StiBorderStyleEtched>蚀化</StiBorderStyleEtched>
    <StiBorderStyleFlat>平直</StiBorderStyleFlat>
    <StiBorderStyleNone>无</StiBorderStyleNone>
    <StiBorderStyleRaised>凸起</StiBorderStyleRaised>
    <StiBorderStyleRaisedInner>内部凸起</StiBorderStyleRaisedInner>
    <StiBorderStyleRaisedOuter>外部凸起</StiBorderStyleRaisedOuter>
    <StiBorderStyleSunken>凹陷</StiBorderStyleSunken>
    <StiBorderStyleSunkenInner>内部凹陷</StiBorderStyleSunkenInner>
    <StiBorderStyleSunkenOuter>外部凹陷</StiBorderStyleSunkenOuter>
    <StiBrushTypeGlare>眩光刷</StiBrushTypeGlare>
    <StiBrushTypeGradient0>渐变刷, 角度 0</StiBrushTypeGradient0>
    <StiBrushTypeGradient180>渐变刷, 180度</StiBrushTypeGradient180>
    <StiBrushTypeGradient270>渐变刷, 270度</StiBrushTypeGradient270>
    <StiBrushTypeGradient45>渐变刷, 45度</StiBrushTypeGradient45>
    <StiBrushTypeGradient90>渐变刷, 90度</StiBrushTypeGradient90>
    <StiBrushTypeSolid>实心刷</StiBrushTypeSolid>
    <StiChartTitleDockBottom>底部</StiChartTitleDockBottom>
    <StiChartTitleDockLeft>左端</StiChartTitleDockLeft>
    <StiChartTitleDockRight>右端</StiChartTitleDockRight>
    <StiChartTitleDockTop>顶部</StiChartTitleDockTop>
    <StiCheckStyleCheck>复选</StiCheckStyleCheck>
    <StiCheckStyleCheckRectangle>Check Rectangle</StiCheckStyleCheckRectangle>
    <StiCheckStyleCross>交叉</StiCheckStyleCross>
    <StiCheckStyleCrossCircle>Cross Circle</StiCheckStyleCrossCircle>
    <StiCheckStyleCrossRectangle>Cross Rectangle</StiCheckStyleCrossRectangle>
    <StiCheckStyleDotCircle>Dot Circle</StiCheckStyleDotCircle>
    <StiCheckStyleDotRectangle>Dot Rectangle</StiCheckStyleDotRectangle>
    <StiCheckStyleNone>None</StiCheckStyleNone>
    <StiCheckStyleNoneCircle>None Circle</StiCheckStyleNoneCircle>
    <StiCheckStyleNoneRectangle>None Rectangle</StiCheckStyleNoneRectangle>
    <StiCheckSumNo>否</StiCheckSumNo>
    <StiCheckSumYes>是</StiCheckSumYes>
    <StiCode11CheckSumAuto>自动</StiCode11CheckSumAuto>
    <StiCode11CheckSumNone>无</StiCode11CheckSumNone>
    <StiCode11CheckSumOneDigit>一位数字</StiCode11CheckSumOneDigit>
    <StiCode11CheckSumTwoDigits>两位数字</StiCode11CheckSumTwoDigits>
    <StiColumnDirectionAcrossThenDown>交叉再向下</StiColumnDirectionAcrossThenDown>
    <StiColumnDirectionDownThenAcross>向下再交叉</StiColumnDirectionDownThenAcross>
    <StiCrossHorAlignmentCenter>居中</StiCrossHorAlignmentCenter>
    <StiCrossHorAlignmentLeft>居左</StiCrossHorAlignmentLeft>
    <StiCrossHorAlignmentNone>无</StiCrossHorAlignmentNone>
    <StiCrossHorAlignmentRight>居右</StiCrossHorAlignmentRight>
    <StiDockStyleBottom>底部</StiDockStyleBottom>
    <StiDockStyleFill>填充</StiDockStyleFill>
    <StiDockStyleLeft>左</StiDockStyleLeft>
    <StiDockStyleNone>无</StiDockStyleNone>
    <StiDockStyleRight>右</StiDockStyleRight>
    <StiDockStyleTop>顶部</StiDockStyleTop>
    <StiEmptySizeModeAlignFooterToBottom>页脚底端对齐</StiEmptySizeModeAlignFooterToBottom>
    <StiEmptySizeModeAlignFooterToTop>页脚顶端对齐</StiEmptySizeModeAlignFooterToTop>
    <StiEmptySizeModeDecreaseLastRow>减少末行</StiEmptySizeModeDecreaseLastRow>
    <StiEmptySizeModeIncreaseLastRow>增加末行</StiEmptySizeModeIncreaseLastRow>
    <StiEnumeratorTypeABC>英文字母</StiEnumeratorTypeABC>
    <StiEnumeratorTypeArabic>阿拉伯数字</StiEnumeratorTypeArabic>
    <StiEnumeratorTypeNone>无</StiEnumeratorTypeNone>
    <StiEnumeratorTypeRoman>罗马数字</StiEnumeratorTypeRoman>
    <StiFilterConditionBeginningWith>开始于</StiFilterConditionBeginningWith>
    <StiFilterConditionBetween>在两者之间</StiFilterConditionBetween>
    <StiFilterConditionContaining>包含</StiFilterConditionContaining>
    <StiFilterConditionEndingWith>结束于</StiFilterConditionEndingWith>
    <StiFilterConditionEqualTo>等于</StiFilterConditionEqualTo>
    <StiFilterConditionGreaterThan>大于</StiFilterConditionGreaterThan>
    <StiFilterConditionGreaterThanOrEqualTo>大于或等于</StiFilterConditionGreaterThanOrEqualTo>
    <StiFilterConditionLessThan>小于</StiFilterConditionLessThan>
    <StiFilterConditionLessThanOrEqualTo>小于或等于</StiFilterConditionLessThanOrEqualTo>
    <StiFilterConditionNotBetween>不在之间</StiFilterConditionNotBetween>
    <StiFilterConditionNotContaining>未包含</StiFilterConditionNotContaining>
    <StiFilterConditionNotEqualTo>不等于</StiFilterConditionNotEqualTo>
    <StiFilterDataTypeBoolean>布尔型</StiFilterDataTypeBoolean>
    <StiFilterDataTypeDateTime>日期时间</StiFilterDataTypeDateTime>
    <StiFilterDataTypeExpression>表达式</StiFilterDataTypeExpression>
    <StiFilterDataTypeNumeric>数值型</StiFilterDataTypeNumeric>
    <StiFilterDataTypeString>字符型</StiFilterDataTypeString>
    <StiFilterItemArgument>参数</StiFilterItemArgument>
    <StiFilterItemExpression>表达式</StiFilterItemExpression>
    <StiFilterItemValue>值</StiFilterItemValue>
    <StiFilterModeAnd>与</StiFilterModeAnd>
    <StiFilterModeOr>或</StiFilterModeOr>
    <StiFormStartModeOnEnd>在结束</StiFormStartModeOnEnd>
    <StiFormStartModeOnPreview>在预览</StiFormStartModeOnPreview>
    <StiFormStartModeOnStart>在开始</StiFormStartModeOnStart>
    <StiGroupSortDirectionAscending>升序</StiGroupSortDirectionAscending>
    <StiGroupSortDirectionDescending>降序</StiGroupSortDirectionDescending>
    <StiGroupSortDirectionNone>无</StiGroupSortDirectionNone>
    <StiHorAlignmentCenter>居中</StiHorAlignmentCenter>
    <StiHorAlignmentLeft>居左</StiHorAlignmentLeft>
    <StiHorAlignmentRight>居右</StiHorAlignmentRight>
    <StiImageRotationFlipHorizontal>水平翻转</StiImageRotationFlipHorizontal>
    <StiImageRotationFlipVertical>垂直翻转</StiImageRotationFlipVertical>
    <StiImageRotationNone>无</StiImageRotationNone>
    <StiImageRotationRotate180>旋转 180°</StiImageRotationRotate180>
    <StiImageRotationRotate90CCW>旋转 90° CCW</StiImageRotationRotate90CCW>
    <StiImageRotationRotate90CW>旋转 90° CW</StiImageRotationRotate90CW>
    <StiLabelsPlacementNone>无</StiLabelsPlacementNone>
    <StiLabelsPlacementOneLine>单线</StiLabelsPlacementOneLine>
    <StiLabelsPlacementTwoLines>双线</StiLabelsPlacementTwoLines>
    <StiLegendDirectionBottomToTop>从底部到顶端</StiLegendDirectionBottomToTop>
    <StiLegendDirectionLeftToRight>由左到右</StiLegendDirectionLeftToRight>
    <StiLegendDirectionRightToLeft>由右到左</StiLegendDirectionRightToLeft>
    <StiLegendDirectionTopToBottom>从上至下</StiLegendDirectionTopToBottom>
    <StiLegendHorAlignmentCenter>居中</StiLegendHorAlignmentCenter>
    <StiLegendHorAlignmentLeft>居左</StiLegendHorAlignmentLeft>
    <StiLegendHorAlignmentLeftOutside>左外侧</StiLegendHorAlignmentLeftOutside>
    <StiLegendHorAlignmentRight>居右</StiLegendHorAlignmentRight>
    <StiLegendHorAlignmentRightOutside>右外侧</StiLegendHorAlignmentRightOutside>
    <StiLegendVertAlignmentBottom>底端</StiLegendVertAlignmentBottom>
    <StiLegendVertAlignmentBottomOutside>底端外侧</StiLegendVertAlignmentBottomOutside>
    <StiLegendVertAlignmentCenter>居中</StiLegendVertAlignmentCenter>
    <StiLegendVertAlignmentTop>居顶</StiLegendVertAlignmentTop>
    <StiLegendVertAlignmentTopOutside>顶端外侧</StiLegendVertAlignmentTopOutside>
    <StiMarkerAlignmentLeft>居左</StiMarkerAlignmentLeft>
    <StiMarkerAlignmentRight>居右</StiMarkerAlignmentRight>
    <StiMarkerTypeCircle>圆</StiMarkerTypeCircle>
    <StiMarkerTypeHexagon>六边形</StiMarkerTypeHexagon>
    <StiMarkerTypeRectangle>矩形</StiMarkerTypeRectangle>
    <StiMarkerTypeStar5>五角星</StiMarkerTypeStar5>
    <StiMarkerTypeStar6>六角星</StiMarkerTypeStar6>
    <StiMarkerTypeStar7>七角星</StiMarkerTypeStar7>
    <StiMarkerTypeTriangle>三角形</StiMarkerTypeTriangle>
    <StiNumberOfPassDoublePass>双通道</StiNumberOfPassDoublePass>
    <StiNumberOfPassSinglePass>单通道</StiNumberOfPassSinglePass>
    <StiOrientationHorizontal>水平</StiOrientationHorizontal>
    <StiOrientationVertical>垂直</StiOrientationVertical>
    <StiPageOrientationLandscape>横向</StiPageOrientationLandscape>
    <StiPageOrientationPortrait>纵向</StiPageOrientationPortrait>
    <StiPenStyleDash>虚线</StiPenStyleDash>
    <StiPenStyleDashDot>点画线</StiPenStyleDashDot>
    <StiPenStyleDashDotDot>双点画线</StiPenStyleDashDotDot>
    <StiPenStyleDot>点线</StiPenStyleDot>
    <StiPenStyleDouble>Double</StiPenStyleDouble>
    <StiPenStyleNone>无</StiPenStyleNone>
    <StiPenStyleSolid>实线</StiPenStyleSolid>
    <StiPlesseyCheckSumNone>无</StiPlesseyCheckSumNone>
    <StiPreviewModeDotMatrix>矩阵</StiPreviewModeDotMatrix>
    <StiPreviewModeStandard>标准</StiPreviewModeStandard>
    <StiPreviewModeStandardAndDotMatrix>标准和点阵</StiPreviewModeStandardAndDotMatrix>
    <StiPrintOnEvenOddPagesTypeIgnore>忽略</StiPrintOnEvenOddPagesTypeIgnore>
    <StiPrintOnEvenOddPagesTypePrintOnEvenPages>打印偶数页</StiPrintOnEvenOddPagesTypePrintOnEvenPages>
    <StiPrintOnEvenOddPagesTypePrintOnOddPages>打印奇数页</StiPrintOnEvenOddPagesTypePrintOnOddPages>
    <StiPrintOnTypeAllPages>全部页</StiPrintOnTypeAllPages>
    <StiPrintOnTypeExceptFirstAndLastPage>去除首页和末页</StiPrintOnTypeExceptFirstAndLastPage>
    <StiPrintOnTypeExceptFirstPage>去除首页</StiPrintOnTypeExceptFirstPage>
    <StiPrintOnTypeExceptLastPage>去除末页</StiPrintOnTypeExceptLastPage>
    <StiPrintOnTypeOnlyFirstAndLastPage>仅首页和末页</StiPrintOnTypeOnlyFirstAndLastPage>
    <StiPrintOnTypeOnlyFirstPage>仅首页</StiPrintOnTypeOnlyFirstPage>
    <StiPrintOnTypeOnlyLastPage>仅末页</StiPrintOnTypeOnlyLastPage>
    <StiProcessingDuplicatesTypeGlobalHide>全局隐藏</StiProcessingDuplicatesTypeGlobalHide>
    <StiProcessingDuplicatesTypeGlobalMerge>全局合并</StiProcessingDuplicatesTypeGlobalMerge>
    <StiProcessingDuplicatesTypeGlobalRemoveText>全局移除文字</StiProcessingDuplicatesTypeGlobalRemoveText>
    <StiProcessingDuplicatesTypeHide>隐藏</StiProcessingDuplicatesTypeHide>
    <StiProcessingDuplicatesTypeMerge>合并</StiProcessingDuplicatesTypeMerge>
    <StiProcessingDuplicatesTypeNone>无</StiProcessingDuplicatesTypeNone>
    <StiProcessingDuplicatesTypeRemoveText>移除文本</StiProcessingDuplicatesTypeRemoveText>
    <StiReportCacheModeAuto>自动</StiReportCacheModeAuto>
    <StiReportCacheModeOff>关闭</StiReportCacheModeOff>
    <StiReportCacheModeOn>开启</StiReportCacheModeOn>
    <StiReportUnitTypeCentimeters>厘米</StiReportUnitTypeCentimeters>
    <StiReportUnitTypeHundredthsOfInch>百分之一英寸</StiReportUnitTypeHundredthsOfInch>
    <StiReportUnitTypeInches>英寸</StiReportUnitTypeInches>
    <StiReportUnitTypeMillimeters>毫米</StiReportUnitTypeMillimeters>
    <StiReportUnitTypePixels>像素</StiReportUnitTypePixels>
    <StiRestrictionsAll>全部</StiRestrictionsAll>
    <StiRestrictionsAllowChange>允许改变</StiRestrictionsAllowChange>
    <StiRestrictionsAllowDelete>允许删除</StiRestrictionsAllowDelete>
    <StiRestrictionsAllowMove>允许移动</StiRestrictionsAllowMove>
    <StiRestrictionsAllowResize>允许改变大小</StiRestrictionsAllowResize>
    <StiRestrictionsAllowSelect>允许选择</StiRestrictionsAllowSelect>
    <StiRestrictionsNone>无</StiRestrictionsNone>
    <StiSeriesLabelsValueTypeArgument>参数</StiSeriesLabelsValueTypeArgument>
    <StiSeriesLabelsValueTypeArgumentValue>参数 - 值</StiSeriesLabelsValueTypeArgumentValue>
    <StiSeriesLabelsValueTypeSeriesTitle>序列标题</StiSeriesLabelsValueTypeSeriesTitle>
    <StiSeriesLabelsValueTypeSeriesTitleArgument>序列标题 - 参数</StiSeriesLabelsValueTypeSeriesTitleArgument>
    <StiSeriesLabelsValueTypeSeriesTitleValue>序列标题 - 值</StiSeriesLabelsValueTypeSeriesTitleValue>
    <StiSeriesLabelsValueTypeValue>值</StiSeriesLabelsValueTypeValue>
    <StiSeriesLabelsValueTypeValueArgument>值 - 参数</StiSeriesLabelsValueTypeValueArgument>
    <StiSeriesSortDirectionAscending>升序</StiSeriesSortDirectionAscending>
    <StiSeriesSortDirectionDescending>降序</StiSeriesSortDirectionDescending>
    <StiSeriesSortTypeArgument>参数</StiSeriesSortTypeArgument>
    <StiSeriesSortTypeNone>无</StiSeriesSortTypeNone>
    <StiSeriesSortTypeValue>值</StiSeriesSortTypeValue>
    <StiSeriesYAxisLeftYAxis>Y 轴左端</StiSeriesYAxisLeftYAxis>
    <StiSeriesYAxisRightYAxis>Y 轴右端</StiSeriesYAxisRightYAxis>
    <StiShapeDirectionDown>向下</StiShapeDirectionDown>
    <StiShapeDirectionLeft>向左</StiShapeDirectionLeft>
    <StiShapeDirectionRight>向右</StiShapeDirectionRight>
    <StiShapeDirectionUp>向上</StiShapeDirectionUp>
    <StiShiftModeDecreasingSize>减少尺寸</StiShiftModeDecreasingSize>
    <StiShiftModeIncreasingSize>增加尺寸</StiShiftModeIncreasingSize>
    <StiShiftModeNone>无</StiShiftModeNone>
    <StiShiftModeOnlyInWidthOfComponent>仅在组件宽度内</StiShiftModeOnlyInWidthOfComponent>
    <StiShowSeriesLabelsFromChart>从图表</StiShowSeriesLabelsFromChart>
    <StiShowSeriesLabelsFromSeries>从系列</StiShowSeriesLabelsFromSeries>
    <StiShowSeriesLabelsNone>无</StiShowSeriesLabelsNone>
    <StiSortDirectionAsc>升序</StiSortDirectionAsc>
    <StiSortDirectionDesc>降序</StiSortDirectionDesc>
    <StiSortDirectionNone>无</StiSortDirectionNone>
    <StiSortTypeByDisplayValue>按显示值</StiSortTypeByDisplayValue>
    <StiSortTypeByValue>按值</StiSortTypeByValue>
    <StiSummaryValuesAllValues>所有值</StiSummaryValuesAllValues>
    <StiSummaryValuesSkipNulls>忽略无效值</StiSummaryValuesSkipNulls>
    <StiSummaryValuesSkipZerosAndNulls>忽略零和无效值</StiSummaryValuesSkipZerosAndNulls>
    <StiTextHorAlignmentCenter>居中</StiTextHorAlignmentCenter>
    <StiTextHorAlignmentLeft>居左</StiTextHorAlignmentLeft>
    <StiTextHorAlignmentRight>居右</StiTextHorAlignmentRight>
    <StiTextHorAlignmentWidth>宽度</StiTextHorAlignmentWidth>
    <StiTextPositionCenterBottom>底部居中</StiTextPositionCenterBottom>
    <StiTextPositionCenterTop>顶部居中</StiTextPositionCenterTop>
    <StiTextPositionLeftBottom>底部居左</StiTextPositionLeftBottom>
    <StiTextPositionLeftTop>顶部居左</StiTextPositionLeftTop>
    <StiTextPositionRightBottom>底部居右</StiTextPositionRightBottom>
    <StiTextPositionRightTop>顶部居右</StiTextPositionRightTop>
    <StiTextQualityStandard>标准</StiTextQualityStandard>
    <StiTextQualityTypographic>印刷质量</StiTextQualityTypographic>
    <StiTextQualityWysiwyg>所见即所得</StiTextQualityWysiwyg>
    <StiVertAlignmentBottom>底部</StiVertAlignmentBottom>
    <StiVertAlignmentCenter>居中</StiVertAlignmentCenter>
    <StiVertAlignmentTop>顶端</StiVertAlignmentTop>
    <StiViewModeNormal>正常</StiViewModeNormal>
    <StiViewModePageBreakPreview>跨页预览</StiViewModePageBreakPreview>
    <StringAlignmentCenter>居中</StringAlignmentCenter>
    <StringAlignmentFar>远</StringAlignmentFar>
    <StringAlignmentNear>近</StringAlignmentNear>
    <StringTrimmingCharacter>字符</StringTrimmingCharacter>
    <StringTrimmingEllipsisCharacter>省略字符</StringTrimmingEllipsisCharacter>
    <StringTrimmingEllipsisPath>省略路径</StringTrimmingEllipsisPath>
    <StringTrimmingEllipsisWord>省略字</StringTrimmingEllipsisWord>
    <StringTrimmingNone>无</StringTrimmingNone>
    <StringTrimmingWord>字</StringTrimmingWord>
  </PropertyEnum>
  <PropertyEvents>
    <AfterPrintEvent>打印后于</AfterPrintEvent>
    <AfterSelectEvent>选择后</AfterSelectEvent>
    <BeforePrintEvent>打印前于</BeforePrintEvent>
    <BeginRenderEvent>开始渲染</BeginRenderEvent>
    <CheckedChangedEvent>选择改变后</CheckedChangedEvent>
    <ClickEvent>点击</ClickEvent>
    <ClosedFormEvent>窗体关闭后</ClosedFormEvent>
    <ClosingFormEvent>窗体关闭时</ClosingFormEvent>
    <ColumnBeginRenderEvent>列开始渲染</ColumnBeginRenderEvent>
    <ColumnEndRenderEvent>列结束渲染</ColumnEndRenderEvent>
    <DoubleClickEvent>双击</DoubleClickEvent>
    <EndRenderEvent>结束渲染</EndRenderEvent>
    <EnterEvent>键入</EnterEvent>
    <ExportedEvent>导出后</ExportedEvent>
    <ExportingEvent>导出时</ExportingEvent>
    <GetArgumentEvent>获取参数</GetArgumentEvent>
    <GetBookmarkEvent>获取书签</GetBookmarkEvent>
    <GetCrossValueEvent>获取 Cross 值</GetCrossValueEvent>
    <GetCutPieListEvent>获取Cut Pie列表</GetCutPieListEvent>
    <GetDisplayCrossValueEvent>获取显示 Cross 值</GetDisplayCrossValueEvent>
    <GetDrillDownReportEvent>获取钻取报表</GetDrillDownReportEvent>
    <GetExcelSheetEvent>得到 Excel Sheet</GetExcelSheetEvent>
    <GetExcelValueEvent>获取 Excel 值</GetExcelValueEvent>
    <GetHyperlinkEvent>获取超链接</GetHyperlinkEvent>
    <GetImageDataEvent>获取图片数据</GetImageDataEvent>
    <GetImageURLEvent>获取图片 URL</GetImageURLEvent>
    <GetListOfArgumentsEvent>获取参数列表</GetListOfArgumentsEvent>
    <GetListOfValuesEvent>获取参数值</GetListOfValuesEvent>
    <GetTagEvent>获取 Tag</GetTagEvent>
    <GetTitleEvent>得到标题</GetTitleEvent>
    <GetToolTipEvent>获取快速提示</GetToolTipEvent>
    <GetValueEvent>获取值</GetValueEvent>
    <LeaveEvent>离开</LeaveEvent>
    <LoadFormEvent>加载窗体</LoadFormEvent>
    <MouseDownEvent>鼠标下移</MouseDownEvent>
    <MouseEnterEvent>鼠标键入</MouseEnterEvent>
    <MouseLeaveEvent>鼠标离开</MouseLeaveEvent>
    <MouseMoveEvent>鼠标移动</MouseMoveEvent>
    <MouseUpEvent>鼠标上移</MouseUpEvent>
    <NewAutoSeriesEvent>新的自动序列</NewAutoSeriesEvent>
    <PositionChangedEvent>位置改变后</PositionChangedEvent>
    <PrintedEvent>打印后</PrintedEvent>
    <PrintingEvent>打印时</PrintingEvent>
    <ProcessChartEvent>处理图表</ProcessChartEvent>
    <RenderingEvent>正在渲染</RenderingEvent>
    <SelectedIndexChangedEvent>所选索引改变后</SelectedIndexChangedEvent>
    <StateRestoreEvent>状态还原</StateRestoreEvent>
    <StateSaveEvent>状态保存</StateSaveEvent>
    <ValueChangedEvent>值改变后</ValueChangedEvent>
  </PropertyEvents>
  <PropertyHatchStyle>
    <BackwardDiagonal>反向对角线</BackwardDiagonal>
    <Cross>斜线</Cross>
    <DarkDownwardDiagonal>粗反向对角线</DarkDownwardDiagonal>
    <DarkHorizontal>粗横线</DarkHorizontal>
    <DarkUpwardDiagonal>粗向上对角线</DarkUpwardDiagonal>
    <DarkVertical>粗竖线</DarkVertical>
    <DashedDownwardDiagonal>虚向下对角线</DashedDownwardDiagonal>
    <DashedHorizontal>虚横线</DashedHorizontal>
    <DashedUpwardDiagonal>虚向上对角线</DashedUpwardDiagonal>
    <DashedVertical>虚竖线</DashedVertical>
    <DiagonalBrick>对角砖块</DiagonalBrick>
    <DiagonalCross>对角交叉线</DiagonalCross>
    <Divot>草痕</Divot>
    <DottedDiamond>菱形虚框</DottedDiamond>
    <DottedGrid>虚线网格</DottedGrid>
    <ForwardDiagonal>正向对角线</ForwardDiagonal>
    <Horizontal>横线</Horizontal>
    <HorizontalBrick>水平砖块</HorizontalBrick>
    <LargeCheckerBoard>大棋盘</LargeCheckerBoard>
    <LargeConfetti>大碎花</LargeConfetti>
    <LargeGrid>大网格</LargeGrid>
    <LightDownwardDiagonal>细向下对角线</LightDownwardDiagonal>
    <LightHorizontal>细横线</LightHorizontal>
    <LightUpwardDiagonal>细向上对角线</LightUpwardDiagonal>
    <LightVertical>细竖线</LightVertical>
    <NarrowHorizontal>窄横线</NarrowHorizontal>
    <NarrowVertical>窄竖线</NarrowVertical>
    <OutlinedDiamond>菱形实框</OutlinedDiamond>
    <Percent05>05%</Percent05>
    <Percent10>10%</Percent10>
    <Percent20>20%</Percent20>
    <Percent25>25%</Percent25>
    <Percent30>30%</Percent30>
    <Percent40>40%</Percent40>
    <Percent50>50%</Percent50>
    <Percent60>60%</Percent60>
    <Percent70>70%</Percent70>
    <Percent75>75%</Percent75>
    <Percent80>80%</Percent80>
    <Percent90>90%</Percent90>
    <Plaid>方格花纹</Plaid>
    <Shingle>瓦格</Shingle>
    <SmallCheckerBoard>小棋盘</SmallCheckerBoard>
    <SmallConfetti>小碎花</SmallConfetti>
    <SmallGrid>小网格</SmallGrid>
    <SolidDiamond>实心菱形</SolidDiamond>
    <Sphere>球形</Sphere>
    <Trellis>方格</Trellis>
    <Vertical>竖线</Vertical>
    <Weave>编织线</Weave>
    <WideDownwardDiagonal>宽向下对角线</WideDownwardDiagonal>
    <WideUpwardDiagonal>宽向上对角线</WideUpwardDiagonal>
    <ZigZag>锯齿线</ZigZag>
  </PropertyHatchStyle>
  <PropertyMain>
    <AcceptsReturn>接受返回</AcceptsReturn>
    <AcceptsTab>接受Tab</AcceptsTab>
    <Advanced>高级</Advanced>
    <AggregateFunction>聚合函数</AggregateFunction>
    <AggregateFunctions>聚合函数</AggregateFunctions>
    <Alias>别名</Alias>
    <Alignment>对齐</Alignment>
    <AllowApplyStyle>允许套用样式</AllowApplyStyle>
    <AllowHtmlTags>允许Html标签</AllowHtmlTags>
    <AllowSorting>允许排序</AllowSorting>
    <AllowUseBorder>允许使用边界</AllowUseBorder>
    <AllowUseBrush>允许使用格式刷</AllowUseBrush>
    <AllowUseFont>允许使用字体</AllowUseFont>
    <AllowUseHorAlignment>允许使用水平对齐</AllowUseHorAlignment>
    <AllowUseImage>允许使用图像</AllowUseImage>
    <AllowUseTextBrush>允许使用文本刷</AllowUseTextBrush>
    <AllowUseTextOptions>允许使用文本选项</AllowUseTextOptions>
    <AllowUseVertAlignment>允许使用垂直对齐</AllowUseVertAlignment>
    <AlternatingBackColor>交替背景色</AlternatingBackColor>
    <Angle>角度</Angle>
    <Antialiasing>混叠效应</Antialiasing>
    <Area>区域</Area>
    <Argument>参数</Argument>
    <ArgumentDataColumn>参数列</ArgumentDataColumn>
    <ArrowHeight>箭头高度</ArrowHeight>
    <ArrowStyle>箭头样式</ArrowStyle>
    <ArrowWidth>箭头宽度</ArrowWidth>
    <AspectRatio>长宽比</AspectRatio>
    <Author>作者</Author>
    <Auto>自动</Auto>
    <AutoLocalizeReportOnRun>运行时自动本地化报表</AutoLocalizeReportOnRun>
    <AutoRefresh>自动更新</AutoRefresh>
    <AutoRotate>自动旋转</AutoRotate>
    <AutoScale>自动缩放</AutoScale>
    <AutoSeriesColorDataColumn>自动序列数据列颜色</AutoSeriesColorDataColumn>
    <AutoSeriesKeyDataColumn>自动序列数据列关键字</AutoSeriesKeyDataColumn>
    <AutoSeriesTitleDataColumn>自动序列数据列标题</AutoSeriesTitleDataColumn>
    <AutoWidth>自动宽度</AutoWidth>
    <AxisValue>轴值</AxisValue>
    <BackColor>背景色</BackColor>
    <Background>背景</Background>
    <BackgroundColor>背景颜色</BackgroundColor>
    <BarCodeType>条形码类型</BarCodeType>
    <Bold>粗体</Bold>
    <Bookmark>书签</Bookmark>
    <Border>边框</Border>
    <BorderColor>边框颜色</BorderColor>
    <Borders>边框</Borders>
    <BorderStyle>边框样式</BorderStyle>
    <Bottom>底端</Bottom>
    <BottomSide>底部大小</BottomSide>
    <BreakIfLessThan>小于时截断</BreakIfLessThan>
    <Brush>画笔</Brush>
    <BrushType>格式刷类型</BrushType>
    <CacheAllData>缓存全部数据</CacheAllData>
    <CalcInvisible>计算列不可见</CalcInvisible>
    <CalculatedDataColumn>计算列</CalculatedDataColumn>
    <CanBreak>跨页</CanBreak>
    <Cancel>取消</Cancel>
    <CanGrow>自动伸展</CanGrow>
    <CanShrink>自动收缩</CanShrink>
    <Categories>分类</Categories>
    <Category>分类</Category>
    <CategoryConnections>连接</CategoryConnections>
    <CellHeight>单元格高度</CellHeight>
    <CellWidth>单元格宽度</CellWidth>
    <ChartType>图表类型</ChartType>
    <Checked>选中</Checked>
    <CheckOnClick>点击选择</CheckOnClick>
    <CheckStyle>选择样式</CheckStyle>
    <CheckStyleForFalse>未选中样式</CheckStyleForFalse>
    <CheckStyleForTrue>选中样式</CheckStyleForTrue>
    <CheckSum>选择 Sum</CheckSum>
    <CheckSum1>选择 Sum1</CheckSum1>
    <CheckSum2>选择 Sum2</CheckSum2>
    <Child>子级</Child>
    <ChildColumns>子栏</ChildColumns>
    <ChildSource>子数据源</ChildSource>
    <ClearFormat>清除格式</ClearFormat>
    <CloneContainer>克隆容器</CloneContainer>
    <Code>代码</Code>
    <Collapsed>折叠</Collapsed>
    <CollapseGroupFooter>折叠分组底部</CollapseGroupFooter>
    <CollapsingEnabled>可折叠</CollapsingEnabled>
    <Collate>比较</Collate>
    <Color>颜色</Color>
    <ColorEach>颜色每</ColorEach>
    <Column>栏</Column>
    <ColumnDirection>方向</ColumnDirection>
    <ColumnGaps>间距</ColumnGaps>
    <ColumnHeadersVisible>标题可见</ColumnHeadersVisible>
    <Columns>栏数</Columns>
    <ColumnWidth>栏宽</ColumnWidth>
    <CommandTimeout>命令超时</CommandTimeout>
    <ComponentStyle>组件样式</ComponentStyle>
    <Condition>条件</Condition>
    <ConditionOptions>条件选项</ConditionOptions>
    <Conditions>条件</Conditions>
    <ConnectOnStart>开始连接</ConnectOnStart>
    <ConstantLines>恒定线</ConstantLines>
    <Container>容器</Container>
    <ContinuousText>连续文本</ContinuousText>
    <ContourColor>轮廓线颜色</ContourColor>
    <Converting>正在转换</Converting>
    <ConvertNulls>无效转换</ConvertNulls>
    <Copies>拷贝</Copies>
    <CountData>行数</CountData>
    <Create>创建</Create>
    <CreateFieldOnDoubleClick>双击创建字段</CreateFieldOnDoubleClick>
    <CreateLabel>创建标签</CreateLabel>
    <CustomFormat>自定义格式</CustomFormat>
    <CutPieList>Cut Pie列表</CutPieList>
    <Data>数据</Data>
    <DataAdapter>数据适配器</DataAdapter>
    <DataAdapters>数据适配器</DataAdapters>
    <DataBindings>数据绑定</DataBindings>
    <DataColumn>数据列</DataColumn>
    <DataField>数据字段</DataField>
    <DataRelation>数据关系</DataRelation>
    <DataSource>数据源</DataSource>
    <DataSources>数据源</DataSources>
    <DataTextField>数据字段文本</DataTextField>
    <DataType>数据类型</DataType>
    <DateInfo>数据信息</DateInfo>
    <Default>默认</Default>
    <DefaultNamespace>默认命名空间</DefaultNamespace>
    <Description>描述</Description>
    <DetectUrls>指向 Urls</DetectUrls>
    <DialogResult>对话框结果</DialogResult>
    <Diameter>直径</Diameter>
    <Direction>方向</Direction>
    <DisplayValue>显示值</DisplayValue>
    <Distance>距离</Distance>
    <DistanceBetweenTabs>制表符间距</DistanceBetweenTabs>
    <Dock>停靠</Dock>
    <DockStyle>停靠样式</DockStyle>
    <DrawBorder>绘制边框</DrawBorder>
    <DrawLine>绘制线</DrawLine>
    <DrillDownEnabled>可钻取</DrillDownEnabled>
    <DrillDownPage>钻取页</DrillDownPage>
    <DrillDownParameter1>钻取参数1</DrillDownParameter1>
    <DrillDownParameter2>钻取参数2</DrillDownParameter2>
    <DrillDownParameter3>钻取参数3</DrillDownParameter3>
    <DrillDownReport>钻取报表</DrillDownReport>
    <DropDownAlign>下拉对齐</DropDownAlign>
    <DropDownStyle>下拉样式</DropDownStyle>
    <DropDownWidth>下拉宽度</DropDownWidth>
    <DropShadow>下拉阴影</DropShadow>
    <Duplex>全双工</Duplex>
    <Editable>可编辑</Editable>
    <EmptyValue>空值</EmptyValue>
    <Enabled>可用</Enabled>
    <EnableLog>使用日志</EnableLog>
    <EncodingType>编码类型</EncodingType>
    <EndColor>结束颜色</EndColor>
    <EnumeratorSeparator>Enumerator 分隔符</EnumeratorSeparator>
    <EnumeratorType>Enumerator 类型</EnumeratorType>
    <EvenStyle>偶数行样式</EvenStyle>
    <ExcelSheet>Excel Sheet</ExcelSheet>
    <ExcelValue>Excel值</ExcelValue>
    <ExportAsImage>导出为图像</ExportAsImage>
    <Expression>表达式</Expression>
    <FaqPage>FAQ</FaqPage>
    <FieldIs>字段是</FieldIs>
    <File>文件</File>
    <Filter>过滤</Filter>
    <FilterOn>启用过滤</FilterOn>
    <Filters>过滤</Filters>
    <FirstTabOffset>首个制表符偏移</FirstTabOffset>
    <Flat>平滑</Flat>
    <FlatMode>平滑模式</FlatMode>
    <Focus>得到焦点</Focus>
    <Font>字体</Font>
    <FontBold>粗体</FontBold>
    <FontItalic>斜体</FontItalic>
    <FontName>字体名称</FontName>
    <FontSize>字体尺寸</FontSize>
    <FontStrikeout>删除线</FontStrikeout>
    <FontSubscript>下标</FontSubscript>
    <FontSuperscript>上标</FontSuperscript>
    <FontUnderline>下划线</FontUnderline>
    <FontUnit>字体单位</FontUnit>
    <ForeColor>前景色</ForeColor>
    <Format>格式</Format>
    <FullConvertExpression>完整转换表达式</FullConvertExpression>
    <Function>函数</Function>
    <Functions>函数</Functions>
    <GlobalizationStrings>全局字符串</GlobalizationStrings>
    <GlobalizedName>全局名称</GlobalizedName>
    <GridLineColor>网格线颜色</GridLineColor>
    <GridLinesHor>水平网格线</GridLinesHor>
    <GridLinesHorRight>网格线水平向右</GridLinesHorRight>
    <GridLineStyle>网格线样式</GridLineStyle>
    <GridLinesVert>垂直网格线</GridLinesVert>
    <GrowToHeight>适应高度</GrowToHeight>
    <HeaderBackColor>标题背景色</HeaderBackColor>
    <HeaderFont>标题字体</HeaderFont>
    <HeaderForeColor>标题前景色</HeaderForeColor>
    <HeaderText>标题文本</HeaderText>
    <Height>高度</Height>
    <HideZeros>零不显示</HideZeros>
    <High>高</High>
    <HighlightCondition>高亮条件</HighlightCondition>
    <HorAlignment>水平位置</HorAlignment>
    <HorSpacing>水平间距</HorSpacing>
    <HotkeyPrefix>热键前缀</HotkeyPrefix>
    <HtmlTags>Html Tag</HtmlTags>
    <Hyperlink>超链接</Hyperlink>
    <Idents>页面边框</Idents>
    <Image>图片</Image>
    <ImageAlign>图像对齐</ImageAlign>
    <ImageAlignment>对齐</ImageAlignment>
    <ImageData>数据</ImageData>
    <ImageMultipleFactor>缩放</ImageMultipleFactor>
    <ImageRotation>图像旋转</ImageRotation>
    <ImageStretch>拉伸</ImageStretch>
    <ImageTiling>平铺</ImageTiling>
    <ImageTransparency>图像透明度</ImageTransparency>
    <ImageURL>URL</ImageURL>
    <ImportRelations>导入关系</ImportRelations>
    <Increment>增量</Increment>
    <Indent>缩进</Indent>
    <Insert>插入</Insert>
    <Interaction>互动</Interaction>
    <InterlacedBrush>画笔行距</InterlacedBrush>
    <InterlacingHor>水平行距</InterlacingHor>
    <InterlacingVert>垂直行距</InterlacingVert>
    <Italic>斜体</Italic>
    <Item>项目</Item>
    <ItemHeight>项目高度</ItemHeight>
    <Items>项目</Items>
    <KeepChildTogether>保持子区一起</KeepChildTogether>
    <KeepCrossTabTogether>保持跨表连接</KeepCrossTabTogether>
    <KeepDetailsTogether>保持明细在一起</KeepDetailsTogether>
    <KeepFooterTogether>保持页脚一起</KeepFooterTogether>
    <KeepGroupFooterTogether>保持分组尾在一起</KeepGroupFooterTogether>
    <KeepGroupHeaderTogether>保持分组头在一起</KeepGroupHeaderTogether>
    <KeepGroupTogether>保持分组一起</KeepGroupTogether>
    <KeepHeaderTogether>保持页眉一起</KeepHeaderTogether>
    <KeepReportSummaryTogether>保持报表合计在一起</KeepReportSummaryTogether>
    <KeepSubReportTogether>保持子报表连接</KeepSubReportTogether>
    <KeyDataColumn>数据列key</KeyDataColumn>
    <LabelColor>标签颜色</LabelColor>
    <Labels>标签</Labels>
    <LabelsColor>标签颜色</LabelsColor>
    <LabelsOffset>Labels Offset</LabelsOffset>
    <Language>语言</Language>
    <LargeHeight>高度拉伸</LargeHeight>
    <LargeHeightFactor>高度拉伸倍数</LargeHeightFactor>
    <Left>左</Left>
    <LeftSide>左边</LeftSide>
    <Legend>图例</Legend>
    <LegendValueType>图例值类型</LegendValueType>
    <Length>长度</Length>
    <Lighting>加亮</Lighting>
    <LineColor>线颜色</LineColor>
    <LineLimit>线界限</LineLimit>
    <LineMarker>标记线</LineMarker>
    <LinesOfUnderline>下划线</LinesOfUnderline>
    <LineStyle>线样式</LineStyle>
    <LineWidth>线宽</LineWidth>
    <Linked>链接</Linked>
    <ListOfArguments>参数列表</ListOfArguments>
    <ListOfValues>值列表</ListOfValues>
    <Localizable>可定位</Localizable>
    <Location>位置</Location>
    <Locked>固定</Locked>
    <Low>低</Low>
    <Margins>页边距</Margins>
    <Marker>标记</Marker>
    <MarkerAlignment>对齐标注</MarkerAlignment>
    <MarkerColor>标注颜色</MarkerColor>
    <MarkerSize>标注尺寸</MarkerSize>
    <MarkerType>标注类型</MarkerType>
    <MarkerVisible>标注可见</MarkerVisible>
    <MasterComponent>父级组件</MasterComponent>
    <MasterKeyDataColumn>数据列主键</MasterKeyDataColumn>
    <MatrixSize>矩阵大小</MatrixSize>
    <MaxDate>最大日期</MaxDate>
    <MaxDropDownItems>最大下拉项目</MaxDropDownItems>
    <Maximum>最大值</Maximum>
    <MaxLength>最大长度</MaxLength>
    <MaxNumberOfLines>最大行号</MaxNumberOfLines>
    <MaxSize>最大尺寸</MaxSize>
    <MaxValue>最大值</MaxValue>
    <MaxWidth>最大宽度</MaxWidth>
    <MergeDuplicates>合并重复</MergeDuplicates>
    <MergeHeaders>合并列头</MergeHeaders>
    <MinDate>最小日期</MinDate>
    <Minimum>最小值</Minimum>
    <MinimumFontSize>最小字体尺寸</MinimumFontSize>
    <MinorColor>Minor 颜色</MinorColor>
    <MinorCount>Minor 数量</MinorCount>
    <MinorLength>Minor 长度</MinorLength>
    <MinorStyle>Minor 样式</MinorStyle>
    <MinorVisible>Minor 可见</MinorVisible>
    <MinRowsInColumn>栏内最少行数</MinRowsInColumn>
    <MinSize>最小尺寸</MinSize>
    <MinValue>最小值</MinValue>
    <MinWidth>最小宽度</MinWidth>
    <Module>模式</Module>
    <Move>移动</Move>
    <Multiline>多行</Multiline>
    <MultipleFactor>放大倍数</MultipleFactor>
    <Name>名称</Name>
    <NameInSource>命名源</NameInSource>
    <NameParent>父级名称</NameParent>
    <Namespaces>命名空间</Namespaces>
    <NewColumnAfter>在后插入新列</NewColumnAfter>
    <NewColumnBefore>在前插入新列</NewColumnBefore>
    <NewPageAfter>在后插入新页</NewPageAfter>
    <NewPageBefore>在前插入新面</NewPageBefore>
    <NextPage>后页</NextPage>
    <NullText>无效文本</NullText>
    <NumberOfColumns>栏数</NumberOfColumns>
    <NumberOfCopies>复制数</NumberOfCopies>
    <NumberOfPass>通道数</NumberOfPass>
    <OddStyle>奇数行样式</OddStyle>
    <OnlyText>仅文本</OnlyText>
    <Options>选项</Options>
    <Orientation>定向</Orientation>
    <PageHeight>页高</PageHeight>
    <PageNumbers>页码</PageNumbers>
    <PageWidth>页宽</PageWidth>
    <Paper>纸张</Paper>
    <PaperSize>页面大小</PaperSize>
    <PaperSourceFirstPage>首页纸张</PaperSourceFirstPage>
    <PaperSourceOfFirstPage>首页纸张来源</PaperSourceOfFirstPage>
    <PaperSourceOfOtherPages>其它页纸张来源</PaperSourceOfOtherPages>
    <PaperSourceOtherPages>其它页纸张</PaperSourceOtherPages>
    <Parameter>参数</Parameter>
    <Parameters>参数</Parameters>
    <ParentColumns>父级栏</ParentColumns>
    <ParentSource>父级数据源</ParentSource>
    <ParentValue>父类值</ParentValue>
    <PasswordChar>密码字符</PasswordChar>
    <Placement>放置</Placement>
    <PlaceOnToolbox>放置工具箱</PlaceOnToolbox>
    <PointAtCenter>指向中心</PointAtCenter>
    <Position>位置</Position>
    <PreferredColumnWidth>首选列宽</PreferredColumnWidth>
    <PreferredRowHeight>首选行高</PreferredRowHeight>
    <PreventIntersection>防止重叠</PreventIntersection>
    <PreviewMode>预览模式</PreviewMode>
    <PreviewSettings>预览设置</PreviewSettings>
    <Printable>可打印</Printable>
    <PrintAtBottom>在底部打印</PrintAtBottom>
    <PrinterName>打印机名称</PrinterName>
    <PrinterSettings>打印机设置</PrinterSettings>
    <PrintHeadersFootersFromPreviousPage>从前页打印页眉和页脚</PrintHeadersFootersFromPreviousPage>
    <PrintIfDetailEmpty>明细为空时打印</PrintIfDetailEmpty>
    <PrintIfEmpty>为空时打印</PrintIfEmpty>
    <PrintOn>打印在</PrintOn>
    <PrintOnAllPages>打印全部页</PrintOnAllPages>
    <PrintOnEvenOddPages>打印偶数奇数页</PrintOnEvenOddPages>
    <PrintOnPreviousPage>打印前页</PrintOnPreviousPage>
    <ProcessAtEnd>在结束时处理</ProcessAtEnd>
    <ProcessingDuplicates>正在处理重复</ProcessingDuplicates>
    <ProductHomePage>产品主页</ProductHomePage>
    <Range>范围</Range>
    <Ratio>比率</Ratio>
    <ReadOnly>只读</ReadOnly>
    <ReconnectOnEachRow>每行重新连接</ReconnectOnEachRow>
    <ReferencedAssemblies>引用程序集</ReferencedAssemblies>
    <Refresh>刷新</Refresh>
    <Relation>关系</Relation>
    <RelationName>关系名称</RelationName>
    <Relations>关系</Relations>
    <RemoveUnusedDataBeforeStart>在开始前移除未使用数据</RemoveUnusedDataBeforeStart>
    <RenderTo>渲染到</RenderTo>
    <ReportAlias>报表别名</ReportAlias>
    <ReportAuthor>报表作者</ReportAuthor>
    <ReportCacheMode>报表缓存模式</ReportCacheMode>
    <ReportDescription>报表描述</ReportDescription>
    <ReportName>报表名称</ReportName>
    <ReportUnit>报表单位</ReportUnit>
    <ResetDataSource>重设数据源</ResetDataSource>
    <ResetPageNumber>重设页码</ResetPageNumber>
    <Resize>调整大小</Resize>
    <Restrictions>限制</Restrictions>
    <ReturnValue>返回值</ReturnValue>
    <ReverseHor>水平翻转</ReverseHor>
    <ReverseVert>垂直翻转</ReverseVert>
    <Right>右</Right>
    <RightSide>右边</RightSide>
    <RightToLeft>从右到左</RightToLeft>
    <Round>环绕</Round>
    <RowHeadersVisible>行标题可见</RowHeadersVisible>
    <RowHeaderWidth>行标题宽度</RowHeaderWidth>
    <Scale>缩放</Scale>
    <ScaleHor>水平缩放</ScaleHor>
    <ScriptLanguage>脚本语言</ScriptLanguage>
    <SegmentPerHeight>纵向连页</SegmentPerHeight>
    <SegmentPerWidth>横向连页</SegmentPerWidth>
    <SelectedIndex>选择索引</SelectedIndex>
    <SelectedItem>选择项目</SelectedItem>
    <SelectedValue>选择值</SelectedValue>
    <SelectionBackColor>选择背景色</SelectionBackColor>
    <SelectionForeColor>选择前景色</SelectionForeColor>
    <SelectionMode>选择模式</SelectionMode>
    <Series>序列</Series>
    <SeriesLabels>序列标签</SeriesLabels>
    <Shadow>阴影</Shadow>
    <ShadowBrush>阴影刷</ShadowBrush>
    <ShadowColor>阴影颜色</ShadowColor>
    <ShadowSize>阴影尺寸</ShadowSize>
    <ShapeType>形状类型</ShapeType>
    <Shift>Shift</Shift>
    <ShiftMode>Shift 模式</ShiftMode>
    <ShowBehind>显示于后面</ShowBehind>
    <ShowDialog>显示对话框</ShowDialog>
    <ShowEdgeValues>显示边框值</ShowEdgeValues>
    <ShowImageBehind>显示图片于后面</ShowImageBehind>
    <ShowInLegend>显示图例</ShowInLegend>
    <ShowInPercent>显示为百分比</ShowInPercent>
    <ShowLabels>显示标签</ShowLabels>
    <ShowLabelText>显示标签文本</ShowLabelText>
    <ShowMarker>显示标注</ShowMarker>
    <ShowOnZeroValues>显示零值</ShowOnZeroValues>
    <ShowSeriesLabels>显示系列标签</ShowSeriesLabels>
    <ShowShadow>显示阴影</ShowShadow>
    <ShowTotal>显示合计</ShowTotal>
    <ShowUpDown>显示升降</ShowUpDown>
    <ShowValue>显示值</ShowValue>
    <ShowZeros>显示零</ShowZeros>
    <ShrinkFontToFit>压缩</ShrinkFontToFit>
    <ShrinkFontToFitMinimumSize>压缩为最小尺寸</ShrinkFontToFitMinimumSize>
    <Side>边</Side>
    <Sides>边</Sides>
    <Simple>简单</Simple>
    <Size>尺寸</Size>
    <SizeMode>尺寸模式</SizeMode>
    <SkipFirst>忽略第一个</SkipFirst>
    <Smoothing>平滑</Smoothing>
    <Sort>排序</Sort>
    <SortBy>排序为</SortBy>
    <SortDirection>排序方向</SortDirection>
    <Sorted>已排序</Sorted>
    <SortingColumn>列排序</SortingColumn>
    <SortingEnabled>可排序</SortingEnabled>
    <SortType>排序类型</SortType>
    <Space>空格</Space>
    <Spacing>间隔</Spacing>
    <SqlCommand>Sql命令</SqlCommand>
    <StartAngle>开始角度</StartAngle>
    <StartColor>开始颜色</StartColor>
    <StartFromZero>从零开始</StartFromZero>
    <StartMode>开始模式</StartMode>
    <StartNewPage>开始新页</StartNewPage>
    <StartNewPageIfLessThan>开始新页如果少于</StartNewPageIfLessThan>
    <StartPosition>起始位置</StartPosition>
    <Step>步骤</Step>
    <Stop>停止</Stop>
    <StopBeforePage>停止于页面</StopBeforePage>
    <StopBeforePrint>停止打印于</StopBeforePrint>
    <Stretch>拉伸</Stretch>
    <StretchToPrintArea>拉伸至打印区域</StretchToPrintArea>
    <StripBrush>长条刷</StripBrush>
    <Strips>长条</Strips>
    <Style>样式</Style>
    <Styles>样式</Styles>
    <SubReportPage>子报表页面</SubReportPage>
    <Summary>聚合</Summary>
    <SummaryValues>合计值</SummaryValues>
    <SupplementCode>附加码</SupplementCode>
    <SupplementType>附加码类型</SupplementType>
    <SystemVariable>系统变量</SystemVariable>
    <SystemVariables>系统变量</SystemVariables>
    <Tag>标识</Tag>
    <TagValue>标识值</TagValue>
    <Tension>拉伸</Tension>
    <Text>文本</Text>
    <TextAfter>文本于后</TextAfter>
    <TextAlign>对齐文本</TextAlign>
    <TextBefore>文本于前</TextBefore>
    <TextBrush>文本刷</TextBrush>
    <TextColor>文本颜色</TextColor>
    <TextFormat>文本格式</TextFormat>
    <TextOnly>仅文本</TextOnly>
    <TextOptions>文本选项</TextOptions>
    <TextQuality>文本质量</TextQuality>
    <Ticks>记号</Ticks>
    <Title>标题</Title>
    <TitleBeforeHeader>标题在页眉前</TitleBeforeHeader>
    <TitleColor>标题颜色</TitleColor>
    <TitleFont>标题字体</TitleFont>
    <TitleVisible>可见</TitleVisible>
    <Today>当天</Today>
    <ToolTip>提示信息</ToolTip>
    <Top>顶端</Top>
    <TopSide>顶部</TopSide>
    <Total>总计</Total>
    <Totals>总计</Totals>
    <TransparentColor>透明色</TransparentColor>
    <Trimming>截断</Trimming>
    <Type>类型</Type>
    <TypeName>类型名称</TypeName>
    <Types>类型</Types>
    <Underline>下划线</Underline>
    <UndoLimit>撤销限制</UndoLimit>
    <Unit>单位</Unit>
    <UnlimitedBreakable>不限制分页</UnlimitedBreakable>
    <UnlimitedHeight>不限制高度</UnlimitedHeight>
    <UnlimitedWidth>不限制宽度</UnlimitedWidth>
    <UseAliases>使用别名</UseAliases>
    <UseParentStyles>使用父级样式</UseParentStyles>
    <UseRectangularSymbols>使用矩形符号</UseRectangularSymbols>
    <UseSeriesColor>使用序列颜色</UseSeriesColor>
    <UseStyleOfSummaryInColumnTotal>显示列汇总</UseStyleOfSummaryInColumnTotal>
    <UseStyleOfSummaryInRowTotal>显示行汇总</UseStyleOfSummaryInRowTotal>
    <Value>值</Value>
    <ValueDataColumn>值数据列</ValueDataColumn>
    <ValueType>值类型</ValueType>
    <Variable>变量</Variable>
    <Variables>变量</Variables>
    <Version>版本</Version>
    <VertAlignment>垂直位置</VertAlignment>
    <VertSpacing>垂直间距</VertSpacing>
    <ViewMode>查看模式</ViewMode>
    <Visible>可见</Visible>
    <Watermark>水印</Watermark>
    <Width>宽度</Width>
    <WindowState>窗口状态</WindowState>
    <WordWrap>自动换行</WordWrap>
    <Wrap>换行</Wrap>
    <WrapGap>行距</WrapGap>
    <XAxis>X 坐标</XAxis>
    <XTopAxis>X 上坐标</XTopAxis>
    <YAxis>Y 坐标</YAxis>
    <YRightAxis>Y 右坐标</YRightAxis>
    <Zoom>缩放</Zoom>
  </PropertyMain>
  <PropertySystemColors>
    <ActiveBorder>活动边框</ActiveBorder>
    <ActiveCaption>活动标题栏</ActiveCaption>
    <ActiveCaptionText>活动标题栏文本</ActiveCaptionText>
    <AppWorkspace>应用程序工作区</AppWorkspace>
    <Control>按钮表面</Control>
    <ControlDark>按钮亮阴影</ControlDark>
    <ControlDarkDark>按钮暗阴影</ControlDarkDark>
    <ControlLight>按钮阴影</ControlLight>
    <ControlLightLight>按钮突出显示</ControlLightLight>
    <ControlText>按钮文本</ControlText>
    <Desktop>桌面</Desktop>
    <GrayText>无效文本</GrayText>
    <Highlight>突出显示</Highlight>
    <HighlightText>突出显示文本</HighlightText>
    <HotTrack>热跟踪</HotTrack>
    <InactiveBorder>非活动边框</InactiveBorder>
    <InactiveCaption>非活动标题栏</InactiveCaption>
    <InactiveCaptionText>非活动标题栏文本</InactiveCaptionText>
    <Info>工具提示</Info>
    <InfoText>工具提示文本</InfoText>
    <Menu>菜单条</Menu>
    <MenuText>菜单文本</MenuText>
    <ScrollBar>滚动条</ScrollBar>
    <Window>窗口背景</Window>
    <WindowFrame>窗口框架</WindowFrame>
    <WindowText>窗口文本</WindowText>
  </PropertySystemColors>
  <QueryBuilder>
    <AddObject>增加对象</AddObject>
    <AddSubQuery>增加派生表</AddSubQuery>
    <AllObjects>(所有对象)</AllObjects>
    <BadFromObjectExpression>无效的 FROM 表达式!</BadFromObjectExpression>
    <BadObjectName>无效对象名!</BadObjectName>
    <BadSelectStatement>无效 SELECT 语句!</BadSelectStatement>
    <CreateLinksFromForeignKeys>通过外键创建链接</CreateLinksFromForeignKeys>
    <CriteriaAlias>别名</CriteriaAlias>
    <CriteriaCriteria>过滤</CriteriaCriteria>
    <CriteriaExpression>表达式</CriteriaExpression>
    <CriteriaGroupBy>分组</CriteriaGroupBy>
    <CriteriaOr>或...</CriteriaOr>
    <CriteriaOutput>输出</CriteriaOutput>
    <CriteriaSortOrder>排序</CriteriaSortOrder>
    <CriteriaSortType>排序方式</CriteriaSortType>
    <Database>数据库</Database>
    <DataSourceProperties>数据源属性</DataSourceProperties>
    <DialectDontSupportDatabases>服务器不支持从不同数据库查询对象.</DialectDontSupportDatabases>
    <DialectDontSupportSchemas>服务器不支持 schemas.</DialectDontSupportSchemas>
    <DialectDontSupportUnions>服务器不支持联合查询.</DialectDontSupportUnions>
    <DialectDontSupportUnionsBrackets>服务器不支持在联合查询中使用括号.</DialectDontSupportUnionsBrackets>
    <DialectDontSupportUnionsBracketsInSubQuery>服务器不支持子查询.</DialectDontSupportUnionsBracketsInSubQuery>
    <DialectDontSupportUnionsInSubQueries>服务器不支持在子查询中使用 unions.</DialectDontSupportUnionsInSubQueries>
    <Edit>编辑</Edit>
    <EncloseWithBrackets>使用括号</EncloseWithBrackets>
    <Expressions>表达式</Expressions>
    <InsertEmptyItem>插入空项</InsertEmptyItem>
    <JoinExpression>Join 表达式</JoinExpression>
    <LabelAlias>别名:</LabelAlias>
    <LabelFilterObjectsBySchemaName>用 Schema 名过滤对象:</LabelFilterObjectsBySchemaName>
    <LabelJoinExpression>Join 表达式n:</LabelJoinExpression>
    <LabelLeftColumn>左边列:</LabelLeftColumn>
    <LabelLeftObject>左边对象:</LabelLeftObject>
    <LabelObject>对象:</LabelObject>
    <LabelRightColumn>右边列:</LabelRightColumn>
    <LabelRightObject>右边对象:</LabelRightObject>
    <LinkProperties>链接属性</LinkProperties>
    <MetadataProviderCantExecSQL>所使用的元数据提供者不能执行SQL查询.</MetadataProviderCantExecSQL>
    <MetaProviderCantLoadMetadata>所使用的元数据提供者不能自动导入元数据.</MetaProviderCantLoadMetadata>
    <MetaProviderCantLoadMetadataForDatabase>所使用的元数据提供者不能自动从数据库导入元数据: {0}</MetaProviderCantLoadMetadataForDatabase>
    <MoveDown>下移</MoveDown>
    <MoveUp>上移</MoveUp>
    <NewUnionSubQuery>新建 union 子查询</NewUnionSubQuery>
    <NoConnectionObject>无连接对象 (属性 {0} 未分配).</NoConnectionObject>
    <NoTransactionObject>无事务对象 (属性 {0} 未分配).</NoTransactionObject>
    <Objects>对象</Objects>
    <ProcedureParameters>存储过程参数</ProcedureParameters>
    <Procedures>存储过程</Procedures>
    <qnSaveChanges>您是否确定保存查询改变?</qnSaveChanges>
    <Query>查询</Query>
    <QueryBuilder>查询构造器</QueryBuilder>
    <QueryParameters>查询参数</QueryParameters>
    <QueryProperties>查询属性</QueryProperties>
    <Remove>移除</Remove>
    <RemoveBrackets>移除括号</RemoveBrackets>
    <RunQueryBuilder>运行查询构造器</RunQueryBuilder>
    <SelectAllFromLeft>左边所有数据</SelectAllFromLeft>
    <SelectAllFromRight>右边所有数据</SelectAllFromRight>
    <SwitchToDerivedTable>切换到派生表</SwitchToDerivedTable>
    <Tables>表</Tables>
    <UnexpectedTokenAt>未知错误 \"{0}\" 在行 {1}, 列 {2}!</UnexpectedTokenAt>
    <Unions>联合查询</Unions>
    <UnionSubMenu>联合查询</UnionSubMenu>
    <ViewQuery>视图查询</ViewQuery>
    <Views>视图</Views>
  </QueryBuilder>
  <Questions>
    <qnDictionaryNew>您是否想要创建新的字典?</qnDictionaryNew>
    <qnLanguageNew>您已经改变报表语言。这将导致新的报表代码生成。 您是否确定要保存当前语言?</qnLanguageNew>
    <qnPageDelete>您是否想要删除页面?</qnPageDelete>
    <qnRemove>您是否想要移除?</qnRemove>
    <qnRemoveService>您是否想要移除服务?</qnRemoveService>
    <qnRemoveServiceCategory>您是否想要移除分类?</qnRemoveServiceCategory>
    <qnRemoveUnused>您是否想要移除未使用的项目?</qnRemoveUnused>
    <qnRestoreDefault>恢复默认?</qnRestoreDefault>
    <qnSaveChanges>保存改变在{0}?</qnSaveChanges>
    <qnSaveChangesToPreviewPage>您是否要保存页面改变?</qnSaveChangesToPreviewPage>
    <qnSynchronize>是否同步数据存储和数据字典的内容?</qnSynchronize>
    <qnSynchronizeServices>是否同步服务?</qnSynchronizeServices>
  </Questions>
  <Report>
    <Bands>栏</Bands>
    <Charts>图表</Charts>
    <CollapseAll>全部折叠</CollapseAll>
    <CompilingReport>正在编译报表</CompilingReport>
    <Components>组件</Components>
    <ConnectingToData>正在连接数据</ConnectingToData>
    <CreatingReport>正在创建报表</CreatingReport>
    <CrossBands>交叉栏</CrossBands>
    <Dialogs>报表控件</Dialogs>
    <EditStyles>[编辑样式]</EditStyles>
    <EventsTab>事件表</EventsTab>
    <ExpandAll>全部展开</ExpandAll>
    <FilterAnd>并且</FilterAnd>
    <FilterOr>或</FilterOr>
    <FinishingReport>正在完成报表</FinishingReport>
    <FirstPass>第一通道</FirstPass>
    <GenerateNewCode>生成新代码</GenerateNewCode>
    <LabelAlias>别名:</LabelAlias>
    <LabelAuthor>作者:</LabelAuthor>
    <LabelBackground>背景:</LabelBackground>
    <LabelCategory>分类:</LabelCategory>
    <LabelCentimeters>厘米:</LabelCentimeters>
    <LabelColor>颜色:</LabelColor>
    <LabelCountData>行数:</LabelCountData>
    <LabelDataBand>数据绑定:</LabelDataBand>
    <LabelDataColumn>数据列:</LabelDataColumn>
    <LabelDefaultValue>默认值:</LabelDefaultValue>
    <LabelExpression>表达式:</LabelExpression>
    <LabelFontName>字体名称:</LabelFontName>
    <LabelFunction>函数:</LabelFunction>
    <LabelHundredthsOfInch>百分之一英寸:</LabelHundredthsOfInch>
    <LabelInches>英寸:</LabelInches>
    <LabelMillimeters>毫米:</LabelMillimeters>
    <LabelName>名称:</LabelName>
    <LabelNameInSource>命名源:</LabelNameInSource>
    <LabelPassword>密码:</LabelPassword>
    <LabelPixels>像素:</LabelPixels>
    <LabelSystemVariable>系统变量:</LabelSystemVariable>
    <LabelTotals>合计</LabelTotals>
    <LabelType>类型:</LabelType>
    <LabelUserName>用户:</LabelUserName>
    <LabelValue>值:</LabelValue>
    <LoadingReport>正在加载报表</LoadingReport>
    <nameAssembly>程序集</nameAssembly>
    <No>否</No>
    <NotAssigned>未设置</NotAssigned>
    <PageNofM>{0} / {1}页</PageNofM>
    <PreparingReport>正在准备报表</PreparingReport>
    <PropertiesTab>属性表</PropertiesTab>
    <RangeAll>全部</RangeAll>
    <RangeCurrentPage>当前页</RangeCurrentPage>
    <RangeInfo>输入页码并且/或页码范围用逗号分隔。例如: 1, 3, 5-12</RangeInfo>
    <RangePage>页面范围</RangePage>
    <RangePages>页数:</RangePages>
    <SavingReport>正在保存报表</SavingReport>
    <SecondPass>第二通道</SecondPass>
    <StiEmptyBrush>无</StiEmptyBrush>
    <StiGlareBrush>中空</StiGlareBrush>
    <StiGlassBrush>玻璃刷</StiGlassBrush>
    <StiGradientBrush>渐变</StiGradientBrush>
    <StiHatchBrush>斜线</StiHatchBrush>
    <StiSolidBrush>实心</StiSolidBrush>
    <StyleBad>坏</StyleBad>
    <StyleGood>好</StyleGood>
    <StyleNeutral>空档</StyleNeutral>
    <StyleNormal>正常</StyleNormal>
    <StyleNote>注释</StyleNote>
    <StyleWarning>警告</StyleWarning>
  </Report>
  <Services>
    <categoryContextTools>文本工具</categoryContextTools>
    <categoryDesigner>设计</categoryDesigner>
    <categoryDictionary>字典</categoryDictionary>
    <categoryExport>导出</categoryExport>
    <categoryLanguages>语言</categoryLanguages>
    <categoryPanels>面板</categoryPanels>
    <categoryRender>渲染</categoryRender>
    <categoryShapes>形状</categoryShapes>
    <categorySL>保存/加载</categorySL>
    <categorySystem>系统</categorySystem>
    <categoryTextFormat>文本格式</categoryTextFormat>
  </Services>
  <Shapes>
    <Arrow>箭头</Arrow>
    <DiagonalDownLine>反斜线</DiagonalDownLine>
    <DiagonalUpLine>斜线</DiagonalUpLine>
    <HorizontalLine>水平线</HorizontalLine>
    <LeftAndRightLine>双竖线</LeftAndRightLine>
    <Oval>椭圆</Oval>
    <Rectangle>矩形</Rectangle>
    <RoundedRectangle>圆角矩形</RoundedRectangle>
    <ServiceCategory>形状</ServiceCategory>
    <TopAndBottomLine>双横线</TopAndBottomLine>
    <Triangle>三角形</Triangle>
    <VerticalLine>垂直线</VerticalLine>
  </Shapes>
  <Toolbars>
    <Align>对齐</Align>
    <AlignBottom>底端对齐</AlignBottom>
    <AlignCenter>居中对齐</AlignCenter>
    <AlignLeft>左对齐</AlignLeft>
    <AlignMiddle>中间对齐</AlignMiddle>
    <AlignRight>右对齐</AlignRight>
    <AlignToGrid>对齐到网格</AlignToGrid>
    <AlignTop>顶端对齐</AlignTop>
    <AlignWidth>宽度相同</AlignWidth>
    <BringToFront>前置</BringToFront>
    <CenterHorizontally>水平居中</CenterHorizontally>
    <CenterVertically>垂直居中</CenterVertically>
    <Conditions>条件</Conditions>
    <FontGrow>增大字体</FontGrow>
    <FontName>字体名称</FontName>
    <FontShrink>减小字体</FontShrink>
    <FontSize>字体尺寸</FontSize>
    <FontStyleBold>粗体</FontStyleBold>
    <FontStyleItalic>斜体</FontStyleItalic>
    <FontStyleUnderline>下划线</FontStyleUnderline>
    <Link>链接</Link>
    <Lock>固定</Lock>
    <MakeHorizontalSpacingEqual>水平间距相同</MakeHorizontalSpacingEqual>
    <MakeSameHeight>与 {0} 高度相同</MakeSameHeight>
    <MakeSameSize>与 {0} 大小相同</MakeSameSize>
    <MakeSameWidth>与 {0} 宽度相同</MakeSameWidth>
    <MakeVerticalSpacingEqual>垂直间距相同</MakeVerticalSpacingEqual>
    <MoveBackward>后移</MoveBackward>
    <MoveForward>前移</MoveForward>
    <Order>排序</Order>
    <SendToBack>后置</SendToBack>
    <Size>尺寸</Size>
    <StyleDesigner>样式设计</StyleDesigner>
    <Styles>样式列表</Styles>
    <TabHome>主页</TabHome>
    <TabLayout>层</TabLayout>
    <TabPage>页</TabPage>
    <TabView>视图</TabView>
    <TextBrush>画刷</TextBrush>
    <ToolbarAlignment>对齐</ToolbarAlignment>
    <ToolbarArrange>排列</ToolbarArrange>
    <ToolbarBorders>边框</ToolbarBorders>
    <ToolbarClipboard>剪贴板</ToolbarClipboard>
    <ToolbarDockStyle>停靠样式</ToolbarDockStyle>
    <ToolbarFont>字体</ToolbarFont>
    <ToolbarFormatting>格式</ToolbarFormatting>
    <ToolbarLayout>布局</ToolbarLayout>
    <ToolbarPageSetup>页面设置</ToolbarPageSetup>
    <ToolbarStandard>标准</ToolbarStandard>
    <ToolbarStyle>样式</ToolbarStyle>
    <ToolbarTextFormat>文本格式</ToolbarTextFormat>
    <ToolbarTools>工具</ToolbarTools>
    <ToolbarViewOptions>视图选项</ToolbarViewOptions>
    <ToolbarWatermarkImage>水印图像</ToolbarWatermarkImage>
    <ToolbarWatermarkText>水印文本</ToolbarWatermarkText>
  </Toolbars>
  <Toolbox>
    <Create>创建组件</Create>
    <Hand>手形工具</Hand>
    <Select>选择工具</Select>
    <Style>格式刷工具</Style>
    <TextEditor>文本编辑器</TextEditor>
    <title>工具箱</title>
  </Toolbox>
  <Wizards>
    <BlankReport>空白报表</BlankReport>
    <ButtonBack>&lt; 上一步(&amp;B)</ButtonBack>
    <ButtonCancel>取消</ButtonCancel>
    <ButtonFinish>完成(&amp;F)</ButtonFinish>
    <ButtonNext>下一步(&amp;N) &gt;</ButtonNext>
    <ColumnsOrder>列的顺序</ColumnsOrder>
    <Custom>自定义</Custom>
    <DataRelation>关系</DataRelation>
    <DataSource>数据源</DataSource>
    <DataSources>数据源</DataSources>
    <Filters>过滤</Filters>
    <FromReportTemplate>从报表模板</FromReportTemplate>
    <groupCreateNewReport>创建新报表</groupCreateNewReport>
    <Groups>分组</Groups>
    <groupTemplates>模板</groupTemplates>
    <groupWizards>向导</groupWizards>
    <infoColumnsOrder>按需要顺序调整列。</infoColumnsOrder>
    <infoDataSource>选择一个数据源。</infoDataSource>
    <infoDataSources>选择现有的数据源.</infoDataSources>
    <infoFilters>为您的报表过滤数据</infoFilters>
    <infoGroups>按需要选择列进行分组。</infoGroups>
    <infoLabelSettings>进行标签设置。</infoLabelSettings>
    <infoLayout>指定报表布局.</infoLayout>
    <infoRelation>选择一个现有的数据关系.</infoRelation>
    <infoSelectColumns>按所要显示的信息选择列。</infoSelectColumns>
    <infoSort>设置数据排序。您可以选择多列进行排序。</infoSort>
    <infoTotals>为您的报表添加摘要信息。</infoTotals>
    <LabelDirection>说明:</LabelDirection>
    <LabelHeight>高:</LabelHeight>
    <LabelHorizontalGap>水平间距:</LabelHorizontalGap>
    <LabelLabelType>标签类型:</LabelLabelType>
    <LabelLeftMargin>左边距:</LabelLeftMargin>
    <LabelNumberOfColumns>栏数:</LabelNumberOfColumns>
    <LabelNumberOfRows>行数:</LabelNumberOfRows>
    <LabelPageHeight>页高:</LabelPageHeight>
    <LabelPageWidth>页宽:</LabelPageWidth>
    <LabelReport>报表标签</LabelReport>
    <LabelSettings>标签设置</LabelSettings>
    <LabelSize>尺寸:</LabelSize>
    <LabelTopMargin>上边距:</LabelTopMargin>
    <LabelVerticalGap>垂直间距:</LabelVerticalGap>
    <LabelWidth>宽:</LabelWidth>
    <Layout>布局</Layout>
    <MarkAll>全选(&amp;A)</MarkAll>
    <MasterDetailReport>报表主要细节</MasterDetailReport>
    <NoFunction>[无]</NoFunction>
    <OpenExistingReport>打开已经存在的报表</OpenExistingReport>
    <Preview>预览</Preview>
    <Reset>重置(&amp;R)</Reset>
    <SelectColumns>选择列</SelectColumns>
    <Sort>排序</Sort>
    <StandardReport>标准报表</StandardReport>
    <title>新建报表</title>
    <Totals>总计</Totals>
    <UsingReportWizard>使用报表向导</UsingReportWizard>
  </Wizards>
  <Zoom>
    <EmptyValue>空值</EmptyValue>
    <MultiplePages>多页</MultiplePages>
    <OnePage>整页</OnePage>
    <PageHeight>页高</PageHeight>
    <PageWidth>页宽</PageWidth>
    <TwoPages>双页</TwoPages>
    <ZoomTo100>原始大小</ZoomTo100>
  </Zoom>
</Localization>