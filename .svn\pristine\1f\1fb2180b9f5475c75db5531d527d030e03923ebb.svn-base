﻿/**  版本信息模板在安装目录下，可自行修改。
* D_V_YyBc.cs
*
* 功 能： N/A
* 类 名： D_V_YyBc
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/8/29 1:40:24   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;

namespace DAL
{
	/// <summary>
	/// 数据访问类:D_V_YyBc
	/// </summary>
	public partial class D_V_YyBc
	{
		public D_V_YyBc()
		{}
		#region  BasicMethod



		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ModelOld.M_V_YyBc model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into V_YyBc(");
			strSql.Append("Bc_Code,Yy_Code,Bc_Name,Bc_Jc,Bc_Memo,Bc_Use,Bq_Name,Bq_Code)");
			strSql.Append(" values (");
			strSql.Append("@Bc_Code,@Yy_Code,@Bc_Name,@Bc_Jc,@Bc_Memo,@Bc_Use,@Bq_Name,@Bq_Code)");
			SqlParameter[] parameters = {
					new SqlParameter("@Bc_Code", SqlDbType.Char,7),
					new SqlParameter("@Yy_Code", SqlDbType.Char,4),
					new SqlParameter("@Bc_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Bc_Jc", SqlDbType.VarChar,50),
					new SqlParameter("@Bc_Memo", SqlDbType.VarChar,50),
					new SqlParameter("@Bc_Use", SqlDbType.Bit,1),
					new SqlParameter("@Bq_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Bq_Code", SqlDbType.Char,7)};
			parameters[0].Value = model.Bc_Code;
			parameters[1].Value = model.Yy_Code;
			parameters[2].Value = model.Bc_Name;
			parameters[3].Value = model.Bc_Jc;
			parameters[4].Value = model.Bc_Memo;
			parameters[5].Value = model.Bc_Use;
			parameters[6].Value = model.Bq_Name;
			parameters[7].Value = model.Bq_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ModelOld.M_V_YyBc model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update V_YyBc set ");
			strSql.Append("Bc_Code=@Bc_Code,");
			strSql.Append("Yy_Code=@Yy_Code,");
			strSql.Append("Bc_Name=@Bc_Name,");
			strSql.Append("Bc_Jc=@Bc_Jc,");
			strSql.Append("Bc_Memo=@Bc_Memo,");
			strSql.Append("Bc_Use=@Bc_Use,");
			strSql.Append("Bq_Name=@Bq_Name,");
			strSql.Append("Bq_Code=@Bq_Code");
			strSql.Append(" where ");
			SqlParameter[] parameters = {
					new SqlParameter("@Bc_Code", SqlDbType.Char,7),
					new SqlParameter("@Yy_Code", SqlDbType.Char,4),
					new SqlParameter("@Bc_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Bc_Jc", SqlDbType.VarChar,50),
					new SqlParameter("@Bc_Memo", SqlDbType.VarChar,50),
					new SqlParameter("@Bc_Use", SqlDbType.Bit,1),
					new SqlParameter("@Bq_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Bq_Code", SqlDbType.Char,7)};
			parameters[0].Value = model.Bc_Code;
			parameters[1].Value = model.Yy_Code;
			parameters[2].Value = model.Bc_Name;
			parameters[3].Value = model.Bc_Jc;
			parameters[4].Value = model.Bc_Memo;
			parameters[5].Value = model.Bc_Use;
			parameters[6].Value = model.Bq_Name;
			parameters[7].Value = model.Bq_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete()
		{
			//该表无主键信息，请自定义主键/条件字段
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from V_YyBc ");
			strSql.Append(" where ");
			SqlParameter[] parameters = {
			};

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_V_YyBc GetModel()
		{
			//该表无主键信息，请自定义主键/条件字段
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 Bc_Code,Yy_Code,Bc_Name,Bc_Jc,Bc_Memo,Bc_Use,Bq_Name,Bq_Code from V_YyBc ");
			strSql.Append(" where ");
			SqlParameter[] parameters = {
			};

			ModelOld.M_V_YyBc model=new ModelOld.M_V_YyBc();
			DataSet ds=HisVar.HisVar.Sqldal.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_V_YyBc DataRowToModel(DataRow row)
		{
			ModelOld.M_V_YyBc model=new ModelOld.M_V_YyBc();
			if (row != null)
			{
				if(row["Bc_Code"]!=null)
				{
					model.Bc_Code=row["Bc_Code"].ToString();
				}
				if(row["Yy_Code"]!=null)
				{
					model.Yy_Code=row["Yy_Code"].ToString();
				}
				if(row["Bc_Name"]!=null)
				{
					model.Bc_Name=row["Bc_Name"].ToString();
				}
				if(row["Bc_Jc"]!=null)
				{
					model.Bc_Jc=row["Bc_Jc"].ToString();
				}
				if(row["Bc_Memo"]!=null)
				{
					model.Bc_Memo=row["Bc_Memo"].ToString();
				}
				if(row["Bc_Use"]!=null && row["Bc_Use"].ToString()!="")
				{
					if((row["Bc_Use"].ToString()=="1")||(row["Bc_Use"].ToString().ToLower()=="true"))
					{
						model.Bc_Use=true;
					}
					else
					{
						model.Bc_Use=false;
					}
				}
				if(row["Bq_Name"]!=null)
				{
					model.Bq_Name=row["Bq_Name"].ToString();
				}
				if(row["Bq_Code"]!=null)
				{
					model.Bq_Code=row["Bq_Code"].ToString();
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select Bc_Code,Yy_Code,Bc_Name,Bc_Jc,Bc_Memo,Bc_Use,Bq_Name,Bq_Code ");
			strSql.Append(" FROM V_YyBc ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" Bc_Code,Yy_Code,Bc_Name,Bc_Jc,Bc_Memo,Bc_Use,Bq_Name,Bq_Code ");
			strSql.Append(" FROM V_YyBc ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM V_YyBc ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T. desc");
			}
			strSql.Append(")AS Row, T.*  from V_YyBc T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "V_YyBc";
			parameters[1].Value = "";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return HisVar.HisVar.Sqldal.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

