﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="0" />
    <DataSources isList="true" count="1">
      <诊疗卡 Ref="2" type="DataTableSource" isKey="true">
        <Alias>诊疗卡</Alias>
        <Columns isList="true" count="6">
          <value>Cf_Id,System.Int32</value>
          <value>Xm_KsName,System.String</value>
          <value>Xm_Name,System.String</value>
          <value>Xm_Dw,System.String</value>
          <value>Cf_Sl,System.Decimal</value>
          <value>Cf_Money,System.Decimal</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>诊疗卡</Name>
        <NameInSource>诊疗卡</NameInSource>
      </诊疗卡>
    </DataSources>
    <Relations isList="true" count="0" />
    <Report isRef="0" />
    <Variables isList="true" count="8">
      <value>,打印时间,打印时间,System.String,,False,False</value>
      <value>,患者姓名,患者姓名,System.String,,False,False</value>
      <value>,床位,床位,System.String,,False,False</value>
      <value>,处方科室,处方科室,System.String,,False,False</value>
      <value>,性别,性别,System.String,,False,False</value>
      <value>,年龄,年龄,System.String,,False,False</value>
      <value>,处方医生,处方医生,System.String,,False,False</value>
      <value>,疾病,疾病,System.String,,False,False</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="3" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="3">
        <GroupHeaderBand1 Ref="4" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,17.2,2.4</ClientRectangle>
          <Components isList="true" count="14">
            <Text2 Ref="5" type="Text" isKey="true">
              <Border>Top, Right, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.9,4.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Bold,Point,False,134</Font>
              <Guid>13003485beac474d9dc6dc2ca5debb8b</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text2</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>患者姓名:{患者姓名}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text2>
            <Text10 Ref="6" type="Text" isKey="true">
              <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9.8,0.9,7.4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Bold,Point,False,134</Font>
              <Guid>141a6851ecd74dfcb6ef3b2554b2b2cd</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text10</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>床位:{床位}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text10>
            <Text11 Ref="7" type="Text" isKey="true">
              <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.4,4.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Bold,Point,False,134</Font>
              <Guid>d4c63c686b094aafb2b6c30d1ee99ace</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text11</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>处方科室:{处方科室}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text11>
            <Text5 Ref="8" type="Text" isKey="true">
              <Border>Top, Right, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.9,5.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.5,Bold,Point,False,134</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text5</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>药品及项目名称</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text5>
            <Text18 Ref="9" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>5.9,1.9,1.1,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.5,Bold,Point,False,134</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text18</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>数量</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text18>
            <Text22 Ref="10" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7,1.9,1.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.5,Bold,Point,False,134</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text22</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>金额</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text22>
            <Text6 Ref="11" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>8.6,1.9,5.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.5,Bold,Point,False,134</Font>
              <Guid>37c6f060b4de44caa4baaf8b0b1eafee</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text6</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>药品及项目名称</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text6>
            <Text12 Ref="12" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>14.5,1.9,1.1,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.5,Bold,Point,False,134</Font>
              <Guid>2f8ceb7334c94e40b7ce01bff6f6e4e2</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text12</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>数量</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text12>
            <Text1 Ref="13" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,17.2,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Bold,Point,False,134</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text1</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>{诊疗卡.Xm_KsName}诊疗卡</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text1>
            <Text3 Ref="14" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.9,0.9,2.2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Bold,Point,False,134</Font>
              <Guid>323104490cf84aa0b353c1e635d47b54</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text3</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>性别:{性别}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text3>
            <Text14 Ref="15" type="Text" isKey="true">
              <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15.6,1.9,1.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.5,Bold,Point,False,134</Font>
              <Guid>5a61b571699849339ccbc4b00cc80b1f</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text14</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>金额</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text14>
            <Text13 Ref="16" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>7.1,0.9,2.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Bold,Point,False,134</Font>
              <Guid>288f697c9fc54f6d9b6d00447fa4e97d</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text13</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>年龄：{年龄}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text13>
            <Text9 Ref="17" type="Text" isKey="true">
              <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.9,1.4,4.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Bold,Point,False,134</Font>
              <Guid>87ee135e22ba4f55ac720cfa64ec3e55</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text9</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>处方医生:{处方医生}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text9>
            <Text15 Ref="18" type="Text" isKey="true">
              <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9.8,1.4,7.4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Bold,Point,False,134</Font>
              <Guid>aefd05735ec34f73b2788fd9f19c3f72</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text15</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>疾病:{疾病}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text15>
          </Components>
          <Condition>{诊疗卡.Xm_KsName}</Condition>
          <Conditions isList="true" count="0" />
          <Name>GroupHeaderBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </GroupHeaderBand1>
        <DataBand1 Ref="19" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,3.6,17.2,0.5</ClientRectangle>
          <Columns>2</Columns>
          <Components isList="true" count="3">
            <Text4 Ref="20" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,5.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>be530bd5b815479ba1817fa7ecabeaa2</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text4</Name>
              <NullValue>-</NullValue>
              <Page isRef="3" />
              <Parent isRef="19" />
              <Text>{诊疗卡.Xm_Name}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text4>
            <Text17 Ref="21" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>5.9,0,1.1,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>c6950196dd284a74adbd6ff8028702d7</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text17</Name>
              <NullValue>-</NullValue>
              <Page isRef="3" />
              <Parent isRef="19" />
              <Text>{诊疗卡.Cf_Sl}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="22" type="CustomFormat" isKey="true">
                <StringFormat>0.####</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text17>
            <Text21 Ref="23" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>7,0,1.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>86dfced89c5c47ae823c058f1af74977</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text21</Name>
              <NullValue>-</NullValue>
              <Page isRef="3" />
              <Parent isRef="19" />
              <Text>{诊疗卡.Cf_Money}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="24" type="CustomFormat" isKey="true">
                <StringFormat>0.00##</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text21>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>诊疗卡</DataSourceName>
          <Filters isList="true" count="0" />
          <Name>DataBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Sort isList="true" count="2">
            <value>ASC</value>
            <value>Cf_Id</value>
          </Sort>
        </DataBand1>
        <GroupFooterBand1 Ref="25" type="GroupFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,4.9,17.2,1.2</ClientRectangle>
          <Components isList="true" count="3">
            <Text8 Ref="26" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>13.1,0.3,4.1,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,134</Font>
              <Guid>0b28633bac394d3d9b19260217079b00</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text8</Name>
              <Page isRef="3" />
              <Parent isRef="25" />
              <Text>打印时间:  {打印时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text8>
            <Text7 Ref="27" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0.3,3.7,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Bold,Point,False,134</Font>
              <Guid>a7232f6a724b4e32abf262f9e1ee3871</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text7</Name>
              <Page isRef="3" />
              <Parent isRef="25" />
              <Text>签字盖章：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text7>
            <HorizontalLinePrimitive1 Ref="28" type="HorizontalLinePrimitive" isKey="true">
              <ClientRectangle>0,0.1,17.2,0.0254</ClientRectangle>
              <Color>Black</Color>
              <EndCap Ref="29" type="Cap" isKey="true">
                <Color>Black</Color>
              </EndCap>
              <Name>HorizontalLinePrimitive1</Name>
              <Page isRef="3" />
              <Parent isRef="25" />
              <StartCap Ref="30" type="Cap" isKey="true">
                <Color>Black</Color>
              </StartCap>
            </HorizontalLinePrimitive1>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>GroupFooterBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </GroupFooterBand1>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>e67f634a845f412dbae15ab107312b84</Guid>
      <Margins>2,1.8,1,1</Margins>
      <Name>Page1</Name>
      <PageHeight>14</PageHeight>
      <PageWidth>21</PageWidth>
      <Report isRef="0" />
      <Watermark Ref="31" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page1>
  </Pages>
  <PrinterSettings Ref="32" type="Stimulsoft.Report.Print.StiPrinterSettings" isKey="true" />
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>诊疗卡</ReportAlias>
  <ReportChanged>4/23/2015 5:22:40 PM</ReportChanged>
  <ReportCreated>1/9/2012 9:49:05 AM</ReportCreated>
  <ReportFile>E:\正在进行时\唐山\正在修改版\his2010\his2010\Rpt\诊疗卡.mrt</ReportFile>
  <ReportGuid>37d231ee35e64e8595e2afed5da671ad</ReportGuid>
  <ReportName>诊疗卡</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2011.2.1026</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class 患者用药清单标准版 : Stimulsoft.Report.StiReport
    {
        public 患者用药清单标准版()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
        #endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>