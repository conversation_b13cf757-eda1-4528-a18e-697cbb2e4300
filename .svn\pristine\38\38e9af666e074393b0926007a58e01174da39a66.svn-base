﻿Imports System.Text
Imports System.Reflection


Public Class Temparature

#Region "变量初始化"
    Private My_Table As New DataTable            '药品字典
    Private My_Cm As CurrencyManager             '同步指针
    Private My_Row As DataRow                    '当 前 行
    Private V_Insert As Boolean                  '增加记录

    Private vBlCode As String

    Private tmpRow As DataRow
    Private DtTemperature As DataTable
    Private bllTw1 As New BLLOld.B_Bl_TWD1
    Private bllTw2 As New BLLOld.B_Bl_TWD2
    Private modelTw1 As New ModelOld.M_Bl_TWD1
#End Region

    Public Sub New(ByVal bl As String)

        ' 此调用是设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        vBlCode = bl
    End Sub
    Private Sub TemparatureTable_Load(sender As Object, e As System.EventArgs) Handles Me.Load
        DtTemperature = bllTw2.GetList("1=2").Tables(0)
        Call Form_Init()
        Call Init_Data()

    End Sub


#Region "窗体初始化"

    Private Sub Form_Init()

        '初始化MyGrid1

        With MyGrid1
            .Init_Grid()
            .Init_Column("病例号", "Bl_Code", "120", "中", "", False)
            .Init_Column("姓名", "Ry_Name", "60", "左", "", False)
            .Init_Column("简称", "Ry_Jc", "0", "左", "", False)
        End With


        'CL_Code, CL_Time, TWBW, Temperature, Pulse, Breath, WLJW, Event, EventTime, 
        'SpecialEvent, isFH, isHXJ, isXZQBQ, Jsr_Code, Lr_Date
        With MyGrid2
            .Init_Grid()
            .Init_Column("时间", "Cl_Time", 60, "中", "", False)
            .Init_Column("温类", "TWBW", 60, "中", "Combobox", True)
            .Init_Column("体温", "Temperature", "40", "右", "###,###,##0.##", True)
            .Init_Column("脉搏", "Pulse", "42", "右", "###,###,##0.##", True)
            .Init_Column("心率", "HeartRate", "42", "右", "###,###,##0.##", True)
            .Init_Column("呼吸", "Breath", "40", "右", "###,###,##0.##", True)
            .Init_Column("物理降温", "WLJW", 80, "右", "", True)
            .Init_Column("事件", "Event", 60, "中", "Combobox", True)
            .Init_Column("事件时间", "EventTime", 150, "左", "yyyy-MM-dd HH:mm:ss", True)
            .Init_Column("特殊事件", "SpecialEvent", 85, "中", "Combobox", True)
            .Init_Column("复核", "isFH", "40", "中", "Check", True)
            .Init_Column("呼吸机", "isHXJ", "50", "中", "Check", True)
            .Init_Column("起搏器", "isXZQBQ", "50", "中", "Check", True)
            .Init_Column("测量人", "Jsr_Name", "70", "左", "", False)
            With MyGrid2
                '.Columns(1).ValueItems.Values.Add(New C1.Win.C1TrueDBGrid.ValueItem("", ""))
                .Columns("TWBW").ValueItems.Values.Add(New C1.Win.C1TrueDBGrid.ValueItem("腋表", "腋表"))
                .Columns("TWBW").ValueItems.Values.Add(New C1.Win.C1TrueDBGrid.ValueItem("口表", "口表"))
                .Columns("TWBW").ValueItems.Values.Add(New C1.Win.C1TrueDBGrid.ValueItem("肛表", "肛表"))
                .Columns("TWBW").ValueItems.Values.Add(New C1.Win.C1TrueDBGrid.ValueItem("耳表", "耳表"))

                .Columns("Event").ValueItems.Values.Add(New C1.Win.C1TrueDBGrid.ValueItem("", ""))
                .Columns("Event").ValueItems.Values.Add(New C1.Win.C1TrueDBGrid.ValueItem("入院", "入院"))
                .Columns("Event").ValueItems.Values.Add(New C1.Win.C1TrueDBGrid.ValueItem("转入", "转入"))
                .Columns("Event").ValueItems.Values.Add(New C1.Win.C1TrueDBGrid.ValueItem("手术", "手术"))
                .Columns("Event").ValueItems.Values.Add(New C1.Win.C1TrueDBGrid.ValueItem("分娩", "分娩"))
                .Columns("Event").ValueItems.Values.Add(New C1.Win.C1TrueDBGrid.ValueItem("出院", "出院"))
                .Columns("Event").ValueItems.Values.Add(New C1.Win.C1TrueDBGrid.ValueItem("死亡", "死亡"))

                .Columns("SpecialEvent").ValueItems.Values.Add(New C1.Win.C1TrueDBGrid.ValueItem("", ""))
                .Columns("SpecialEvent").ValueItems.Values.Add(New C1.Win.C1TrueDBGrid.ValueItem("拒测", "拒测"))
                .Columns("SpecialEvent").ValueItems.Values.Add(New C1.Win.C1TrueDBGrid.ValueItem("外出", "外出"))
            End With
        End With
        MyGrid2.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightCell
        '初始化MySingleComobo1       

        With DBCSComboBox1
            .Items.Add("0")
            .Items.Add("※")
            .Items.Add("☆")
            .Items.Add("E")
            .Items.Add("0/E")
            .Items.Add("1/E")
            .Items.Add("1     1/E")
            .SelectedIndex = -1
        End With

        With PSJGCombo
            .Additem = ""
            .Additem = "阴性"
            .Additem = "阳性"
            .DroupDownWidth = .Width - .CaptainWidth
            .DisplayColumns(1).Visible = False
            .SelectedIndex = -1
        End With



        ClDateMyDateEdit1.Value = Format(Now, "yyyy-MM-dd")
    End Sub

    Private Sub Init_Data()
        Dim sqlStr As New StringBuilder
        sqlStr.Append(" SELECT Bl_Code, Ry_Name,Ry_Jc")
        sqlStr.Append(" from Bl")
        sqlStr.Append("  WHERE ISNULL(Ry_CyDate,'')=''")

        My_Table = HisVar.HisVar.Sqldal.Query(sqlStr.ToString).Tables(0)
        With My_Table
            .PrimaryKey = New DataColumn() { .Columns("Bl_Code")}
        End With
        With Me.MyGrid1
            My_Cm = CType(BindingContext(My_Table, ""), CurrencyManager)
            .DataTable = My_Table
            T_Label.Text = "∑=" + .Splits(0).Rows.Count.ToString
        End With
        If vBlCode <> "" Then T_Textbox.Text = vBlCode Else T_Textbox.Text = ""
        MyGrid1_RowColChange(Nothing, Nothing)
    End Sub


#End Region

#Region "自定义函数"
    Private Sub Data_Show()
        'Bl_Code, CL_Code, CL_Date, Height, Weight, ZRL, ZCL, YLL, DBCS, XBCS, XBL, GMYW1, 
        'GMYW2, XY1, XY2, IsPregnant, Jsr_Code, Lr_Date
        V_Insert = False
        tmpRow = bllTw1.GetList("Bl_Code='" & My_Row("Bl_code") & "' AND CL_Date='" & ClDateMyDateEdit1.Value & "'").Tables(0).Rows(0)
        HeightMyNumericEdit1.Value = tmpRow("Height")
        WeightMyNumericEdit2.Value = tmpRow("Weight")
        DBCSComboBox1.Text = tmpRow("DBCS")
        XBLMyNumericEdit7.Value = tmpRow("XBL")
        ZRLMyNumericEdit3.Value = tmpRow("ZRL")
        ZCLMyNumericEdit4.Value = tmpRow("ZCL")
        GMYW1MyTextBox1.Text = tmpRow("GMYW1")
        PSJGCombo.Text = tmpRow("PSJG") & ""
        XYTextBox1.Text = tmpRow("XY1")
        DtTemperature = bllTw2.GetList("Cl_Code='" & tmpRow("Cl_Code") & "'").Tables(0)
        MyGrid2.DataTable = DtTemperature
    End Sub

    Private Sub Data_Clear()
        V_Insert = True
        HeightMyNumericEdit1.Value = Nothing
        WeightMyNumericEdit2.Value = Nothing
        DBCSComboBox1.SelectedIndex = -1
        XBLMyNumericEdit7.Value = Nothing
        ZRLMyNumericEdit3.Value = Nothing
        ZCLMyNumericEdit4.Value = Nothing
        GMYW1MyTextBox1.Text = ""
        PSJGCombo.SelectedIndex = -1
        XYTextBox1.Text = ""
        TempDt_Clear()
    End Sub

    Private Sub TempDt_Clear()
        'CL_Code, CL_Time, TWBW, Temperature, Pulse, Breath, WLJW, Event, EventTime, 
        'SpecialEvent, isFH, isHXJ, isXZQBQ, Jsr_Code,jsr_name, Lr_Date
        DtTemperature.Clear()
        Dim ClCode As String = bllTw1.MaxCode
        For i = 2 To 22 Step 4
            Dim row As DataRow = DtTemperature.NewRow
            row(0) = ClCode
            row(1) = IIf(i < 10, "0" & i & ":00", i & ":00")
            row(11) = False
            row(12) = False
            row(13) = False
            row(14) = HisVar.HisVar.JsrCode
            row(16) = HisVar.HisVar.JsrName
            DtTemperature.Rows.Add(row)
        Next
        MyGrid2.DataTable = DtTemperature
    End Sub

    Private Sub DataQuery()
        If MyGrid1.RowCount > 0 Then
            My_Row = My_Cm.List(MyGrid1.Row).Row
            If bllTw1.GetRecordCount("Bl_Code='" & My_Row("Bl_Code") & "' AND CL_Date='" & ClDateMyDateEdit1.Value & "'") > 0 Then
                Data_Show()
            Else
                Data_Clear()
            End If

        End If
    End Sub
#End Region

#Region "控件动作"
    Private Sub T_Textbox_TextChanged(sender As System.Object, e As System.EventArgs) Handles T_Textbox.TextChanged
        Dim view As DataView = My_Cm.List
        view.RowFilter = "Bl_Code like '" & T_Textbox.Text & "' or Ry_Name like '*" & T_Textbox.Text & "*' or ry_jc like '*" & T_Textbox.Text & "*'"
        T_Label.Text = "∑=" & MyGrid1.RowCount
    End Sub

    Private Sub DBCSComboBox1_KeyPress(sender As Object, e As System.Windows.Forms.KeyPressEventArgs) Handles DBCSComboBox1.KeyPress
        If e.KeyChar = Chr(Keys.Return) Then
            If sender.Tag = "DBCSComboBox1" Then
                XBLMyNumericEdit7.Select()
            End If
        End If
    End Sub

    Private Sub ClDateMyDateEdit1_ValueChanged(sender As Object, e As System.EventArgs) Handles ClDateMyDateEdit1.ValueChanged
        DataQuery()
    End Sub

    Private Sub MyGrid2_AfterColEdit(sender As Object, e As C1.Win.C1TrueDBGrid.ColEventArgs) Handles MyGrid2.AfterColEdit
        If e.ColIndex = 7 Then
            DtTemperature.Rows(MyGrid2.Row).Item("EventTime") = Format(ClDateMyDateEdit1.Value, "yyyy-MM-dd") & " " & DtTemperature.Rows(MyGrid2.Row).Item("Cl_Time")
            DtTemperature.AcceptChanges()
        End If
    End Sub
    Private Sub MyGrid2_AfterColUpdate(sender As Object, e As C1.Win.C1TrueDBGrid.ColEventArgs) Handles MyGrid2.AfterColUpdate

    End Sub
    'Private Sub C1TrueDBGrid1_BeforeColEdit(sender As System.Object, e As C1.Win.C1TrueDBGrid.BeforeColEditEventArgs) Handles MyGrid2.BeforeColEdit
    '    If e.ColIndex = 7 Then
    '        DtTemperature.Rows(MyGrid2.Row).Item("EventTime") = Format(ClDateMyDateEdit1.Value, "yyyy-MM-dd") & " " & DtTemperature.Rows(MyGrid2.Row).Item("Cl_Time")
    '        DtTemperature.AcceptChanges()
    '    End If
    'End Sub


    Private Sub MyGrid2_KeyDown(sender As System.Object, e As System.Windows.Forms.KeyEventArgs) Handles MyGrid2.KeyDown
        Select Case e.KeyCode
            Case Keys.Return
                If MyGrid2.Columns(MyGrid2.Col).DataField = "isXZQBQ" Then
                    Me.MyGrid2.DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveNone
                    Me.MyGrid2.Row = Me.MyGrid2.Row + 1
                    Me.MyGrid2.Col = MyGrid2.Splits(0).DisplayColumns.IndexOf(MyGrid2.Columns("TWBW"))
                    Exit Sub
                Else
                    Me.MyGrid2.DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveRight
                End If
        End Select
    End Sub

    Private Sub MyGrid1_RowColChange(sender As Object, e As C1.Win.C1TrueDBGrid.RowColChangeEventArgs) Handles MyGrid1.RowColChange
        PrintDaysNumericEdit1.Value = 7
        DBCSComboBox1.Focus()
        DataQuery()
    End Sub

    Private Sub SaveMyButton1_Click(sender As System.Object, e As System.EventArgs) Handles SaveMyButton1.Click
        MyGrid2.UpdateData()
        If V_Insert = True Then
            Data_Add()
        Else
            Data_Edit()
        End If
    End Sub

    Private Sub MyButton2_Click(sender As System.Object, e As System.EventArgs) Handles DeleteMyButton2.Click
        If V_Insert = False Then
            If bllTw2.Delete(DtTemperature.Rows(0).Item(0)) And bllTw1.Delete(DtTemperature.Rows(0).Item(0)) Then
                MsgBox("删除成功！", MsgBoxStyle.Information, "提示")
                Data_Clear()
            End If
        Else
            MsgBox("没有记录可删除！", MsgBoxStyle.Critical, "提示")
        End If

    End Sub

    Private Sub PrintButton1_Click(sender As System.Object, e As System.EventArgs) Handles PrintButton1.Click
        MyGrid2.UpdateData()
        If V_Insert = True Then
            If Data_Add() = False Then
                Return
            End If
        Else
            If Data_Edit() = False Then
                Return
            End If
        End If
        If V_Insert = False Then
            Dim frm As New TemparatureChart(MyGrid1.Columns(0).CellValue(MyGrid1.Row), DateAdd(DateInterval.Day, -PrintDaysNumericEdit1.Value, ClDateMyDateEdit1.Value), ClDateMyDateEdit1.Value)
            frm.ShowDialog()
        End If
    End Sub

    Private Sub ExitBtn_Click(sender As System.Object, e As System.EventArgs) Handles ExitBtn.Click
        Me.Close()
    End Sub

#End Region

#Region "数据编辑"
    Private Function Data_Add() As Boolean
        Try
            'Bl_Code, CL_Code, CL_Date, Height, Weight, ZRL, ZCL, YLL, DBCS, XBCS, XBL, GMYW1, 
            'GMYW2, XY1, XY2, IsPregnant, Jsr_Code, Lr_Date
            With modelTw1

                .Bl_Code = MyGrid1.Columns(0).CellValue(MyGrid1.Row)
                .CL_Code = DtTemperature.Rows(0).Item(0)
                .CL_Date = ClDateMyDateEdit1.Value
                If Convert.IsDBNull(HeightMyNumericEdit1.Value) = False Then
                    .Height = CInt(HeightMyNumericEdit1.Value)
                End If
                If Convert.IsDBNull(WeightMyNumericEdit2.Value) = False Then
                    .Weight = CInt(WeightMyNumericEdit2.Value)
                End If
                If Convert.IsDBNull(ZRLMyNumericEdit3.Value) = False Then
                    .ZRL = CInt(ZRLMyNumericEdit3.Value)
                End If
                If Convert.IsDBNull(ZCLMyNumericEdit4.Value) = False Then
                    .ZCL = CInt(ZCLMyNumericEdit4.Value)
                End If
                If Convert.IsDBNull(XBLMyNumericEdit7.Value) = False Then
                    .XBL = CInt(XBLMyNumericEdit7.Value)
                End If
                .DBCS = DBCSComboBox1.Text
                .GMYW1 = Trim(GMYW1MyTextBox1.Text)
                .PSJG = Trim(PSJGCombo.Text)
                .XY1 = XYTextBox1.Text
                .Jsr_Code = HisVar.HisVar.JsrCode
                .Lr_Date = Now
            End With

            'CL_Code, CL_Time, TWBW, Temperature, Pulse, Breath, WLJW, Event, EventTime, 
            'SpecialEvent, isFH, isHXJ, isXZQBQ, Jsr_Code, Lr_Date

            Dim list As New List(Of ModelOld.M_Bl_TWD2)
            For i = 0 To 5
                Dim model1 As New ModelOld.M_Bl_TWD2
                With model1
                    .CL_Code = DtTemperature.Rows(i).Item("CL_Code")
                    .CL_Time = DtTemperature.Rows(i).Item("CL_Time")
                    If Trim(DtTemperature.Rows(i).Item("TWBW") & "") = "" Then
                    Else
                        .TWBW = DtTemperature.Rows(i).Item("TWBW") & ""
                    End If

                    If Trim(DtTemperature.Rows(i).Item("Temperature") & "") = "" Then
                    ElseIf DtTemperature.Rows(i).Item("Temperature") < 34 Or DtTemperature.Rows(i).Item("Temperature") > 45 Then
                        MsgBox("体温输入异常", MsgBoxStyle.Critical, "提示")
                        Return False
                    Else
                        .Temperature = DtTemperature.Rows(i).Item("Temperature")
                    End If

                    If Trim(DtTemperature.Rows(i).Item("Pulse") & "") = "" Then
                    ElseIf DtTemperature.Rows(i).Item("Pulse") < 0 Or DtTemperature.Rows(i).Item("Pulse") > 300 Then
                        MsgBox("脉搏输入异常", MsgBoxStyle.Critical, "提示")
                        Return False
                    Else
                        .Pulse = DtTemperature.Rows(i).Item("Pulse")
                    End If

                    If Trim(DtTemperature.Rows(i).Item("HeartRate") & "") = "" Then
                    ElseIf DtTemperature.Rows(i).Item("HeartRate") < 0 Or DtTemperature.Rows(i).Item("HeartRate") > 300 Then
                        MsgBox("心率输入异常", MsgBoxStyle.Critical, "提示")
                        Return False
                    Else
                        .HeartRate = DtTemperature.Rows(i).Item("HeartRate")
                    End If

                    If Trim(DtTemperature.Rows(i).Item("Breath") & "") = "" Then
                    ElseIf DtTemperature.Rows(i).Item("Breath") < 0 Or DtTemperature.Rows(i).Item("Breath") > 200 Then
                        MsgBox("呼吸输入异常", MsgBoxStyle.Critical, "提示")
                        Return False
                    Else
                        .Breath = DtTemperature.Rows(i).Item("Breath")
                    End If

                    If Trim(DtTemperature.Rows(i).Item("WLJW") & "") = "" Then
                    Else
                        .WLJW = DtTemperature.Rows(i).Item("WLJW")
                    End If

                    If Trim(DtTemperature.Rows(i).Item("Event") & "") = "" Then
                    Else
                        .Event = DtTemperature.Rows(i).Item("Event")
                    End If

                    If Trim(DtTemperature.Rows(i).Item("Event") & "") <> "" Then
                        If Trim(DtTemperature.Rows(i).Item("EventTime") & "") = "" Then
                            MsgBox("请填写正确的事件时间", MsgBoxStyle.Critical, "提示")
                            Return False
                        ElseIf Not IsDate(DtTemperature.Rows(i).Item("EventTime")) Then
                            MsgBox("请填写正确的事件时间", MsgBoxStyle.Critical, "提示")
                            Return False
                        ElseIf Convert.ToDateTime(DtTemperature.Rows(i).Item("EventTime")).Date <> Convert.ToDateTime(ClDateMyDateEdit1.Value).Date Then
                            MsgBox("事件时间必须与测量日期是同一天", MsgBoxStyle.Critical, "提示")
                            Return False
                        Else
                            .EventTime = DtTemperature.Rows(i).Item("EventTime")
                        End If
                    End If

                    If Trim(DtTemperature.Rows(i).Item("SpecialEvent") & "") = "" Then
                    Else
                        .SpecialEvent = DtTemperature.Rows(i).Item("SpecialEvent")
                    End If

                    If Trim(DtTemperature.Rows(i).Item("isFH") & "") = "" Then
                    Else
                        .isFH = DtTemperature.Rows(i).Item("isFH")
                    End If
                    If Trim(DtTemperature.Rows(i).Item("isHXJ") & "") = "" Then
                    Else
                        .isHXJ = DtTemperature.Rows(i).Item("isHXJ")
                    End If
                    If Trim(DtTemperature.Rows(i).Item("isXZQBQ") & "") = "" Then
                    Else
                        .isXZQBQ = DtTemperature.Rows(i).Item("isXZQBQ")
                    End If

                    .Jsr_Code = HisVar.HisVar.JsrCode
                    .Lr_Date = Now
                End With
                list.Add(model1)
            Next
            If bllTw2.Add(modelTw1, list) Then
                V_Insert = False
                MsgBox("保存成功！", MsgBoxStyle.Information, "提示")
                Return True
            Else
                MsgBox("保存失败！", MsgBoxStyle.Information, "提示")
                Return False
            End If

        Catch ex As Exception
            MsgBox("保存失败！" & vbCrLf & ex.ToString, MsgBoxStyle.Critical, "提示")
        End Try

    End Function

    Private Function Data_Edit() As Boolean
        Try

            'Bl_Code, CL_Code, CL_Date, Height, Weight, ZRL, ZCL, YLL, DBCS, XBCS, XBL, GMYW1, 
            'GMYW2, XY1, XY2, IsPregnant, Jsr_Code, Lr_Date
            With modelTw1
                .Bl_Code = MyGrid1.Columns(0).CellValue(MyGrid1.Row)
                .CL_Code = DtTemperature.Rows(0).Item(0)
                .CL_Date = ClDateMyDateEdit1.Value
                If Convert.IsDBNull(HeightMyNumericEdit1.Value) = False Then
                    .Height = CInt(HeightMyNumericEdit1.Value)
                End If
                If Convert.IsDBNull(WeightMyNumericEdit2.Value) = False Then
                    .Weight = CInt(WeightMyNumericEdit2.Value)
                End If
                If Convert.IsDBNull(ZRLMyNumericEdit3.Value) = False Then
                    .ZRL = CInt(ZRLMyNumericEdit3.Value)
                End If
                If Convert.IsDBNull(ZCLMyNumericEdit4.Value) = False Then
                    .ZCL = CInt(ZCLMyNumericEdit4.Value)
                End If
                If Convert.IsDBNull(XBLMyNumericEdit7.Value) = False Then
                    .XBL = CInt(XBLMyNumericEdit7.Value)
                End If
                .DBCS = DBCSComboBox1.Text
                .GMYW1 = Trim(GMYW1MyTextBox1.Text)
                .PSJG = Trim(PSJGCombo.Text)
                .XY1 = XYTextBox1.Text
                .Jsr_Code = HisVar.HisVar.JsrCode
                .Lr_Date = Now
            End With

            'CL_Code, CL_Time, TWBW, Temperature, Pulse, Breath, WLJW, Event, EventTime, 
            'SpecialEvent, isFH, isHXJ, isXZQBQ, Jsr_Code, Lr_Date

            Dim list As New List(Of ModelOld.M_Bl_TWD2)
            For i = 0 To 5
                Dim model1 As New ModelOld.M_Bl_TWD2
                With model1
                    .CL_Code = DtTemperature.Rows(i).Item("CL_Code")
                    .CL_Time = DtTemperature.Rows(i).Item("CL_Time")
                    If Trim(DtTemperature.Rows(i).Item("TWBW") & "") = "" Then
                    Else
                        .TWBW = DtTemperature.Rows(i).Item("TWBW") & ""
                    End If

                    If Trim(DtTemperature.Rows(i).Item("Temperature") & "") = "" Then
                    ElseIf DtTemperature.Rows(i).Item("Temperature") < 34 Or DtTemperature.Rows(i).Item("Temperature") > 45 Then
                        MsgBox("体温输入异常", MsgBoxStyle.Critical, "提示")
                        Return False
                    Else
                        .Temperature = DtTemperature.Rows(i).Item("Temperature")
                    End If

                    If Trim(DtTemperature.Rows(i).Item("Pulse") & "") = "" Then
                    ElseIf DtTemperature.Rows(i).Item("Pulse") < 0 Or DtTemperature.Rows(i).Item("Pulse") > 300 Then
                        MsgBox("脉搏输入异常", MsgBoxStyle.Critical, "提示")
                        Return False
                    Else
                        .Pulse = DtTemperature.Rows(i).Item("Pulse")
                    End If

                    If Trim(DtTemperature.Rows(i).Item("HeartRate") & "") = "" Then
                    ElseIf DtTemperature.Rows(i).Item("HeartRate") < 0 Or DtTemperature.Rows(i).Item("HeartRate") > 300 Then
                        MsgBox("心率输入异常", MsgBoxStyle.Critical, "提示")
                        Return False
                    Else
                        .HeartRate = DtTemperature.Rows(i).Item("HeartRate")
                    End If

                    If Trim(DtTemperature.Rows(i).Item("Breath") & "") = "" Then
                    ElseIf DtTemperature.Rows(i).Item("Breath") < 0 Or DtTemperature.Rows(i).Item("Breath") > 200 Then
                        MsgBox("呼吸输入异常", MsgBoxStyle.Critical, "提示")
                        Return False
                    Else
                        .Breath = DtTemperature.Rows(i).Item("Breath")
                    End If

                    If Trim(DtTemperature.Rows(i).Item("WLJW") & "") = "" Then
                    Else
                        .WLJW = DtTemperature.Rows(i).Item("WLJW")
                    End If

                    If Trim(DtTemperature.Rows(i).Item("Event") & "") = "" Then
                    Else
                        .Event = DtTemperature.Rows(i).Item("Event")
                    End If

                    If Trim(DtTemperature.Rows(i).Item("Event") & "") <> "" Then
                        If Trim(DtTemperature.Rows(i).Item("EventTime") & "") = "" Then
                            MsgBox("请填写正确的事件时间", MsgBoxStyle.Critical, "提示")
                            Return False
                        ElseIf Not IsDate(DtTemperature.Rows(i).Item("EventTime")) Then
                            MsgBox("请填写正确的事件时间", MsgBoxStyle.Critical, "提示")
                            Return False
                        ElseIf Strings.Left(DtTemperature.Rows(i).Item("EventTime"), 10) <> Format(ClDateMyDateEdit1.Value, "yyyy-MM-dd") Then
                            MsgBox("请填写正确的事件时间", MsgBoxStyle.Critical, "提示")
                            Return False
                        Else
                            .EventTime = DtTemperature.Rows(i).Item("EventTime")
                        End If
                    End If

                    If Trim(DtTemperature.Rows(i).Item("SpecialEvent") & "") = "" Then
                    Else
                        .SpecialEvent = DtTemperature.Rows(i).Item("SpecialEvent")
                    End If

                    If Trim(DtTemperature.Rows(i).Item("isFH") & "") = "" Then
                    Else
                        .isFH = DtTemperature.Rows(i).Item("isFH")
                    End If
                    If Trim(DtTemperature.Rows(i).Item("isHXJ") & "") = "" Then
                    Else
                        .isHXJ = DtTemperature.Rows(i).Item("isHXJ")
                    End If
                    If Trim(DtTemperature.Rows(i).Item("isXZQBQ") & "") = "" Then
                    Else
                        .isXZQBQ = DtTemperature.Rows(i).Item("isXZQBQ")
                    End If

                    .Jsr_Code = HisVar.HisVar.JsrCode
                    .Lr_Date = Now
                End With
                list.Add(model1)
            Next
            If bllTw2.Update(modelTw1, list) Then
                MsgBox("更新成功！", MsgBoxStyle.Information, "提示")
                Return True
            Else
                MsgBox("更新失败！", MsgBoxStyle.Information, "提示")
                Return False
            End If
        Catch ex As Exception
            MsgBox("更新失败！" & vbCrLf & ex.ToString, MsgBoxStyle.Critical, "提示")
            Return False
        End Try
    End Function
#End Region

    '调整日期
    Private Sub MyButton1_Click(sender As System.Object, e As System.EventArgs) Handles MyButton1.Click
        ClDateMyDateEdit1.Value = DateAdd("y", -1, ClDateMyDateEdit1.Value)
    End Sub

    Private Sub MyButton2_Click_1(sender As System.Object, e As System.EventArgs) Handles MyButton2.Click
        ClDateMyDateEdit1.Value = DateAdd("y", 1, ClDateMyDateEdit1.Value)
    End Sub


End Class