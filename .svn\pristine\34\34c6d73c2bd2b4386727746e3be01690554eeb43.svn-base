﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class Person_Config
    Inherits HisControl.Base

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(Person_Config))
        Me.TabControl1 = New System.Windows.Forms.TabControl()
        Me.TabPage1 = New System.Windows.Forms.TabPage()
        Me.C1Button2 = New C1.Win.C1Input.C1Button()
        Me.C1Button1 = New C1.Win.C1Input.C1Button()
        Me.TextBox4 = New System.Windows.Forms.TextBox()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.TextBox3 = New System.Windows.Forms.TextBox()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.TextBox2 = New System.Windows.Forms.TextBox()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.TextBox1 = New System.Windows.Forms.TextBox()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.TabPage2 = New System.Windows.Forms.TabPage()
        Me.btnColorSelect = New CustomControl.MyButton()
        Me.colorlbl = New System.Windows.Forms.Label()
        Me.inputCombo = New CustomControl.MySingleComobo()
        Me.Label6 = New System.Windows.Forms.Label()
        Me.C1Button3 = New C1.Win.C1Input.C1Button()
        Me.C1Button4 = New C1.Win.C1Input.C1Button()
        Me.TabPage3 = New System.Windows.Forms.TabPage()
        Me.MyGrid1 = New CustomControl.MyGrid()
        Me.TabPage4 = New System.Windows.Forms.TabPage()
        Me.MySingleComobo4 = New CustomControl.MySingleComobo()
        Me.MySingleComobo3 = New CustomControl.MySingleComobo()
        Me.Button1 = New System.Windows.Forms.Button()
        Me.MySingleComobo2 = New CustomControl.MySingleComobo()
        Me.MySingleComobo1 = New CustomControl.MySingleComobo()
        Me.ImageList1 = New System.Windows.Forms.ImageList(Me.components)
        Me.TabControl1.SuspendLayout
        Me.TabPage1.SuspendLayout
        CType(Me.C1Button2,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.C1Button1,System.ComponentModel.ISupportInitialize).BeginInit
        Me.TabPage2.SuspendLayout
        CType(Me.C1Button3,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.C1Button4,System.ComponentModel.ISupportInitialize).BeginInit
        Me.TabPage3.SuspendLayout
        Me.TabPage4.SuspendLayout
        Me.SuspendLayout
        '
        'TabControl1
        '
        Me.TabControl1.Controls.Add(Me.TabPage1)
        Me.TabControl1.Controls.Add(Me.TabPage2)
        Me.TabControl1.Controls.Add(Me.TabPage3)
        Me.TabControl1.Controls.Add(Me.TabPage4)
        Me.TabControl1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TabControl1.ImageList = Me.ImageList1
        Me.TabControl1.Location = New System.Drawing.Point(0, 0)
        Me.TabControl1.Margin = New System.Windows.Forms.Padding(0)
        Me.TabControl1.Name = "TabControl1"
        Me.TabControl1.SelectedIndex = 0
        Me.TabControl1.Size = New System.Drawing.Size(410, 333)
        Me.TabControl1.TabIndex = 0
        '
        'TabPage1
        '
        Me.TabPage1.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.TabPage1.Controls.Add(Me.C1Button2)
        Me.TabPage1.Controls.Add(Me.C1Button1)
        Me.TabPage1.Controls.Add(Me.TextBox4)
        Me.TabPage1.Controls.Add(Me.Label4)
        Me.TabPage1.Controls.Add(Me.TextBox3)
        Me.TabPage1.Controls.Add(Me.Label3)
        Me.TabPage1.Controls.Add(Me.TextBox2)
        Me.TabPage1.Controls.Add(Me.Label2)
        Me.TabPage1.Controls.Add(Me.TextBox1)
        Me.TabPage1.Controls.Add(Me.Label1)
        Me.TabPage1.ImageIndex = 0
        Me.TabPage1.Location = New System.Drawing.Point(4, 23)
        Me.TabPage1.Margin = New System.Windows.Forms.Padding(0)
        Me.TabPage1.Name = "TabPage1"
        Me.TabPage1.Size = New System.Drawing.Size(402, 306)
        Me.TabPage1.TabIndex = 0
        Me.TabPage1.Text = "密码修改"
        Me.TabPage1.UseVisualStyleBackColor = true
        '
        'C1Button2
        '
        Me.C1Button2.Location = New System.Drawing.Point(178, 203)
        Me.C1Button2.Name = "C1Button2"
        Me.C1Button2.Size = New System.Drawing.Size(85, 36)
        Me.C1Button2.TabIndex = 19
        Me.C1Button2.Text = "退出"
        Me.C1Button2.UseVisualStyleBackColor = true
        Me.C1Button2.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'C1Button1
        '
        Me.C1Button1.Location = New System.Drawing.Point(76, 203)
        Me.C1Button1.Name = "C1Button1"
        Me.C1Button1.Size = New System.Drawing.Size(85, 36)
        Me.C1Button1.TabIndex = 3
        Me.C1Button1.Text = "保存"
        Me.C1Button1.UseVisualStyleBackColor = true
        Me.C1Button1.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'TextBox4
        '
        Me.TextBox4.Location = New System.Drawing.Point(134, 143)
        Me.TextBox4.Name = "TextBox4"
        Me.TextBox4.PasswordChar = Global.Microsoft.VisualBasic.ChrW(42)
        Me.TextBox4.Size = New System.Drawing.Size(128, 21)
        Me.TextBox4.TabIndex = 2
        '
        'Label4
        '
        Me.Label4.AutoSize = true
        Me.Label4.Location = New System.Drawing.Point(77, 147)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(53, 12)
        Me.Label4.TabIndex = 16
        Me.Label4.Text = "口令确认"
        '
        'TextBox3
        '
        Me.TextBox3.Location = New System.Drawing.Point(134, 104)
        Me.TextBox3.Name = "TextBox3"
        Me.TextBox3.PasswordChar = Global.Microsoft.VisualBasic.ChrW(42)
        Me.TextBox3.Size = New System.Drawing.Size(128, 21)
        Me.TextBox3.TabIndex = 1
        '
        'Label3
        '
        Me.Label3.AutoSize = true
        Me.Label3.Location = New System.Drawing.Point(77, 107)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(53, 12)
        Me.Label3.TabIndex = 14
        Me.Label3.Text = "新 口 令"
        '
        'TextBox2
        '
        Me.TextBox2.Location = New System.Drawing.Point(134, 68)
        Me.TextBox2.Name = "TextBox2"
        Me.TextBox2.PasswordChar = Global.Microsoft.VisualBasic.ChrW(42)
        Me.TextBox2.Size = New System.Drawing.Size(128, 21)
        Me.TextBox2.TabIndex = 0
        '
        'Label2
        '
        Me.Label2.AutoSize = true
        Me.Label2.Location = New System.Drawing.Point(77, 72)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(53, 12)
        Me.Label2.TabIndex = 12
        Me.Label2.Text = "原始口令"
        '
        'TextBox1
        '
        Me.TextBox1.Location = New System.Drawing.Point(134, 31)
        Me.TextBox1.Name = "TextBox1"
        Me.TextBox1.ReadOnly = true
        Me.TextBox1.Size = New System.Drawing.Size(128, 21)
        Me.TextBox1.TabIndex = 11
        '
        'Label1
        '
        Me.Label1.AutoSize = true
        Me.Label1.Location = New System.Drawing.Point(77, 34)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(53, 12)
        Me.Label1.TabIndex = 10
        Me.Label1.Text = "用户姓名"
        '
        'TabPage2
        '
        Me.TabPage2.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.TabPage2.Controls.Add(Me.btnColorSelect)
        Me.TabPage2.Controls.Add(Me.colorlbl)
        Me.TabPage2.Controls.Add(Me.inputCombo)
        Me.TabPage2.Controls.Add(Me.Label6)
        Me.TabPage2.Controls.Add(Me.C1Button3)
        Me.TabPage2.Controls.Add(Me.C1Button4)
        Me.TabPage2.ImageIndex = 1
        Me.TabPage2.Location = New System.Drawing.Point(4, 23)
        Me.TabPage2.Margin = New System.Windows.Forms.Padding(0)
        Me.TabPage2.Name = "TabPage2"
        Me.TabPage2.Size = New System.Drawing.Size(402, 306)
        Me.TabPage2.TabIndex = 1
        Me.TabPage2.Text = "输入法设置"
        Me.TabPage2.UseVisualStyleBackColor = true
        '
        'btnColorSelect
        '
        Me.btnColorSelect.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.btnColorSelect.DialogResult = System.Windows.Forms.DialogResult.None
        Me.btnColorSelect.Font = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.btnColorSelect.Location = New System.Drawing.Point(315, 131)
        Me.btnColorSelect.Name = "btnColorSelect"
        Me.btnColorSelect.Size = New System.Drawing.Size(60, 27)
        Me.btnColorSelect.TabIndex = 9
        Me.btnColorSelect.Text = "选择"
        '
        'colorlbl
        '
        Me.colorlbl.Location = New System.Drawing.Point(25, 133)
        Me.colorlbl.Name = "colorlbl"
        Me.colorlbl.Size = New System.Drawing.Size(270, 23)
        Me.colorlbl.TabIndex = 8
        Me.colorlbl.Text = "请选择您的电子病历痕迹颜色"
        Me.colorlbl.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'inputCombo
        '
        Me.inputCombo.Captain = "您的常用中文输入法"
        Me.inputCombo.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.inputCombo.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.inputCombo.CaptainWidth = 119!
        Me.inputCombo.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        Me.inputCombo.ItemHeight = 16
        Me.inputCombo.ItemTextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.inputCombo.Location = New System.Drawing.Point(9, 34)
        Me.inputCombo.MaximumSize = New System.Drawing.Size(10000, 22)
        Me.inputCombo.MinimumSize = New System.Drawing.Size(0, 22)
        Me.inputCombo.Name = "inputCombo"
        Me.inputCombo.ReadOnly = false
        Me.inputCombo.Size = New System.Drawing.Size(382, 22)
        Me.inputCombo.TabIndex = 7
        Me.inputCombo.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        '
        'Label6
        '
        Me.Label6.AutoSize = true
        Me.Label6.ForeColor = System.Drawing.Color.Maroon
        Me.Label6.Location = New System.Drawing.Point(108, 76)
        Me.Label6.Name = "Label6"
        Me.Label6.Size = New System.Drawing.Size(185, 12)
        Me.Label6.TabIndex = 6
        Me.Label6.Text = "请选择你常用的输入法，点击保存"
        '
        'C1Button3
        '
        Me.C1Button3.Location = New System.Drawing.Point(209, 223)
        Me.C1Button3.Name = "C1Button3"
        Me.C1Button3.Size = New System.Drawing.Size(85, 36)
        Me.C1Button3.TabIndex = 5
        Me.C1Button3.Text = "退出"
        Me.C1Button3.UseVisualStyleBackColor = true
        Me.C1Button3.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'C1Button4
        '
        Me.C1Button4.Location = New System.Drawing.Point(107, 223)
        Me.C1Button4.Name = "C1Button4"
        Me.C1Button4.Size = New System.Drawing.Size(85, 36)
        Me.C1Button4.TabIndex = 2
        Me.C1Button4.Text = "保存"
        Me.C1Button4.UseVisualStyleBackColor = true
        Me.C1Button4.VisualStyleBaseStyle = C1.Win.C1Input.VisualStyle.Office2007Blue
        '
        'TabPage3
        '
        Me.TabPage3.Controls.Add(Me.MyGrid1)
        Me.TabPage3.Location = New System.Drawing.Point(4, 23)
        Me.TabPage3.Name = "TabPage3"
        Me.TabPage3.Padding = New System.Windows.Forms.Padding(3)
        Me.TabPage3.Size = New System.Drawing.Size(402, 306)
        Me.TabPage3.TabIndex = 2
        Me.TabPage3.Text = "提醒订阅"
        Me.TabPage3.UseVisualStyleBackColor = true
        '
        'MyGrid1
        '
        Me.MyGrid1.AllowColMove = true
        Me.MyGrid1.AllowFilter = true
        Me.MyGrid1.CanCustomCol = false
        Me.MyGrid1.Caption = ""
        Me.MyGrid1.ChildGrid = Nothing
        Me.MyGrid1.Col = 0
        Me.MyGrid1.ColumnFooters = false
        Me.MyGrid1.ColumnHeaders = true
        Me.MyGrid1.DataMember = ""
        Me.MyGrid1.DataSource = Nothing
        Me.MyGrid1.DataView = C1.Win.C1TrueDBGrid.DataViewEnum.Normal
        Me.MyGrid1.DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveRight
        Me.MyGrid1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.MyGrid1.FetchRowStyles = false
        Me.MyGrid1.FilterBar = false
        Me.MyGrid1.GroupByAreaVisible = true
        Me.MyGrid1.Location = New System.Drawing.Point(3, 3)
        Me.MyGrid1.Margin = New System.Windows.Forms.Padding(0)
        Me.MyGrid1.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.MyGrid1.Name = "MyGrid1"
        Me.MyGrid1.Size = New System.Drawing.Size(396, 300)
        Me.MyGrid1.TabAction = C1.Win.C1TrueDBGrid.TabActionEnum.ControlNavigation
        Me.MyGrid1.TabIndex = 0
        Me.MyGrid1.Xmlpath = Nothing
        '
        'TabPage4
        '
        Me.TabPage4.Controls.Add(Me.MySingleComobo4)
        Me.TabPage4.Controls.Add(Me.MySingleComobo3)
        Me.TabPage4.Controls.Add(Me.Button1)
        Me.TabPage4.Controls.Add(Me.MySingleComobo2)
        Me.TabPage4.Controls.Add(Me.MySingleComobo1)
        Me.TabPage4.Location = New System.Drawing.Point(4, 23)
        Me.TabPage4.Name = "TabPage4"
        Me.TabPage4.Padding = New System.Windows.Forms.Padding(3)
        Me.TabPage4.Size = New System.Drawing.Size(402, 306)
        Me.TabPage4.TabIndex = 3
        Me.TabPage4.Text = "打印机设置"
        Me.TabPage4.UseVisualStyleBackColor = true
        '
        'MySingleComobo4
        '
        Me.MySingleComobo4.Captain = "药房管理纸型"
        Me.MySingleComobo4.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.MySingleComobo4.CaptainForeColor = System.Drawing.Color.Maroon
        Me.MySingleComobo4.CaptainWidth = 83!
        Me.MySingleComobo4.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        Me.MySingleComobo4.ForeColor = System.Drawing.Color.Maroon
        Me.MySingleComobo4.ItemHeight = 16
        Me.MySingleComobo4.ItemTextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.MySingleComobo4.Location = New System.Drawing.Point(18, 172)
        Me.MySingleComobo4.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.MySingleComobo4.MinimumSize = New System.Drawing.Size(0, 20)
        Me.MySingleComobo4.Name = "MySingleComobo4"
        Me.MySingleComobo4.ReadOnly = false
        Me.MySingleComobo4.Size = New System.Drawing.Size(348, 20)
        Me.MySingleComobo4.TabIndex = 4
        Me.MySingleComobo4.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        '
        'MySingleComobo3
        '
        Me.MySingleComobo3.Captain = "药库管理纸型"
        Me.MySingleComobo3.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.MySingleComobo3.CaptainForeColor = System.Drawing.Color.Maroon
        Me.MySingleComobo3.CaptainWidth = 83!
        Me.MySingleComobo3.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        Me.MySingleComobo3.ForeColor = System.Drawing.Color.Maroon
        Me.MySingleComobo3.ItemHeight = 16
        Me.MySingleComobo3.ItemTextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.MySingleComobo3.Location = New System.Drawing.Point(18, 128)
        Me.MySingleComobo3.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.MySingleComobo3.MinimumSize = New System.Drawing.Size(0, 20)
        Me.MySingleComobo3.Name = "MySingleComobo3"
        Me.MySingleComobo3.ReadOnly = false
        Me.MySingleComobo3.Size = New System.Drawing.Size(348, 20)
        Me.MySingleComobo3.TabIndex = 3
        Me.MySingleComobo3.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        '
        'Button1
        '
        Me.Button1.Location = New System.Drawing.Point(147, 231)
        Me.Button1.Name = "Button1"
        Me.Button1.Size = New System.Drawing.Size(75, 31)
        Me.Button1.TabIndex = 2
        Me.Button1.Text = "保存"
        Me.Button1.UseVisualStyleBackColor = true
        '
        'MySingleComobo2
        '
        Me.MySingleComobo2.Captain = "住院打印机"
        Me.MySingleComobo2.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.MySingleComobo2.CaptainForeColor = System.Drawing.Color.Maroon
        Me.MySingleComobo2.CaptainWidth = 71!
        Me.MySingleComobo2.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        Me.MySingleComobo2.ForeColor = System.Drawing.Color.Maroon
        Me.MySingleComobo2.ItemHeight = 16
        Me.MySingleComobo2.ItemTextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.MySingleComobo2.Location = New System.Drawing.Point(18, 86)
        Me.MySingleComobo2.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.MySingleComobo2.MinimumSize = New System.Drawing.Size(0, 20)
        Me.MySingleComobo2.Name = "MySingleComobo2"
        Me.MySingleComobo2.ReadOnly = false
        Me.MySingleComobo2.Size = New System.Drawing.Size(348, 20)
        Me.MySingleComobo2.TabIndex = 1
        Me.MySingleComobo2.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        '
        'MySingleComobo1
        '
        Me.MySingleComobo1.Captain = "门诊打印机"
        Me.MySingleComobo1.CaptainFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.MySingleComobo1.CaptainForeColor = System.Drawing.Color.Maroon
        Me.MySingleComobo1.CaptainWidth = 71!
        Me.MySingleComobo1.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        Me.MySingleComobo1.ForeColor = System.Drawing.Color.Maroon
        Me.MySingleComobo1.ItemHeight = 16
        Me.MySingleComobo1.ItemTextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        Me.MySingleComobo1.Location = New System.Drawing.Point(18, 44)
        Me.MySingleComobo1.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.MySingleComobo1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.MySingleComobo1.Name = "MySingleComobo1"
        Me.MySingleComobo1.ReadOnly = false
        Me.MySingleComobo1.Size = New System.Drawing.Size(348, 20)
        Me.MySingleComobo1.TabIndex = 0
        Me.MySingleComobo1.TextFont = New System.Drawing.Font("宋体", 9!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134,Byte))
        '
        'ImageList1
        '
        Me.ImageList1.ImageStream = CType(resources.GetObject("ImageList1.ImageStream"),System.Windows.Forms.ImageListStreamer)
        Me.ImageList1.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList1.Images.SetKeyName(0, "Login.png")
        Me.ImageList1.Images.SetKeyName(1, "Network-Folder.png")
        '
        'Person_Config
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6!, 12!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(410, 333)
        Me.Controls.Add(Me.TabControl1)
        Me.Icon = CType(resources.GetObject("$this.Icon"),System.Drawing.Icon)
        Me.Name = "Person_Config"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "个人设置"
        Me.TabControl1.ResumeLayout(false)
        Me.TabPage1.ResumeLayout(false)
        Me.TabPage1.PerformLayout
        CType(Me.C1Button2,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.C1Button1,System.ComponentModel.ISupportInitialize).EndInit
        Me.TabPage2.ResumeLayout(false)
        Me.TabPage2.PerformLayout
        CType(Me.C1Button3,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.C1Button4,System.ComponentModel.ISupportInitialize).EndInit
        Me.TabPage3.ResumeLayout(false)
        Me.TabPage4.ResumeLayout(false)
        Me.ResumeLayout(false)

End Sub
    Friend WithEvents TabControl1 As System.Windows.Forms.TabControl
    Friend WithEvents TabPage1 As System.Windows.Forms.TabPage
    Friend WithEvents TabPage2 As System.Windows.Forms.TabPage
    Friend WithEvents C1Button2 As C1.Win.C1Input.C1Button
    Friend WithEvents C1Button1 As C1.Win.C1Input.C1Button
    Friend WithEvents TextBox4 As System.Windows.Forms.TextBox
    Friend WithEvents Label4 As System.Windows.Forms.Label
    Friend WithEvents TextBox3 As System.Windows.Forms.TextBox
    Friend WithEvents Label3 As System.Windows.Forms.Label
    Friend WithEvents TextBox2 As System.Windows.Forms.TextBox
    Friend WithEvents Label2 As System.Windows.Forms.Label
    Friend WithEvents TextBox1 As System.Windows.Forms.TextBox
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents C1Button3 As C1.Win.C1Input.C1Button
    Friend WithEvents C1Button4 As C1.Win.C1Input.C1Button
    Friend WithEvents ImageList1 As System.Windows.Forms.ImageList
    Friend WithEvents Label6 As System.Windows.Forms.Label
    Friend WithEvents TabPage3 As System.Windows.Forms.TabPage
    Friend WithEvents TabPage4 As System.Windows.Forms.TabPage
    Friend WithEvents MySingleComobo2 As CustomControl.MySingleComobo
    Friend WithEvents MySingleComobo1 As CustomControl.MySingleComobo
    Friend WithEvents Button1 As System.Windows.Forms.Button
    Friend WithEvents MySingleComobo3 As CustomControl.MySingleComobo
    Friend WithEvents MySingleComobo4 As CustomControl.MySingleComobo
    Friend WithEvents MyGrid1 As CustomControl.MyGrid
    Friend WithEvents inputCombo As CustomControl.MySingleComobo
    Friend WithEvents colorlbl As System.Windows.Forms.Label
    Friend WithEvents btnColorSelect As CustomControl.MyButton
End Class
