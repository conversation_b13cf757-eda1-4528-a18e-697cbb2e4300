﻿Imports BaseClass
Imports System.Drawing
Imports System.Windows.Forms
Public Class MaterialsDict13

#Region "变量定义"
    Dim JCSpell As New BaseClass.Chs2Spell
    Dim BllMDic2 As New BLLOld.B_Materials_Dict
    Dim ModelMDic2 As New ModelOld.M_Materials_Dict

#End Region

#Region "传参"

    Dim <PERSON>ert As Boolean
    Dim Rrow As DataRow
    Dim RZbtb As DataTable
    Dim Rrc As C_RowChange
    Dim RtreeView As TreeView
    Dim RAllowInsert As Boolean
#End Region

    Public Sub New(ByVal tinsert As Boolean, ByVal trow As DataRow, ByVal tZbtb As DataTable, ByRef trc As C_RowChange, ByRef ttreeview As TreeView, ByVal tAllowInset As Boolean)
        InitializeComponent()
        Rinsert = tinsert
        Rrow = trow
        RZbtb = tZbtb
        Rrc = trc
        RtreeView = ttreeview
        RAllowInsert = tAllowInset
    End Sub

    Private Sub MaterialsDict13_FormClosed(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosedEventArgs) Handles Me.FormClosed
        RemoveHandler Rrc.RowChanged, AddressOf Data_Show
    End Sub

    Private Sub MaterialsDict13_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        AddHandler Rrc.RowChanged, AddressOf Data_Show
        Call Form_Init()
        If Rinsert = True Then Call Data_Clear() Else Call Data_Show(Rrow)
    End Sub


#Region "窗体__事件"

    Private Sub Form_Init()
        Panel1.Height = 29
        ToolBar1.Location = New Point(2, 4)

        M_Code_Tb.Enabled = False
        M_Py_Tb.Enabled = False
        M_Class_Tb.Enabled = False

        Button1.Top = 1
        Button2.Location = New Point(Button1.Left + Button1.Width + 2, Button1.Top)
    End Sub

#End Region

#Region "数据__操作"

    Private Sub Data_Clear()
        Move5.Enabled = False
        Rinsert = True
        M_Code_Tb.Text = BllMDic2.MaxCode
        M_Py_Tb.Text = ""
        M_Name_Tb.Text = ""
        M_BulkUnit_Tb.Text = ""
        M_PackUnit_Tb.Text = ""
        M_ConvertRatio_Tb.Text = "1"
        M_MateManu_Tb.Text = ""
        M_Spec_Tb.Text = ""
        M_Class_Tb.Text = RtreeView.SelectedNode.Tag.ToString
        IsUse_Cb.Checked = True
        M_Memo_Tb.Text = ""


        Call Show_Label()
        Me.M_Name_Tb.Select()

    End Sub

    Private Sub Data_Show(ByVal tmp_Row As DataRow)
        If RAllowInsert = True Then
            Move5.Enabled = True
        Else
            Move5.Enabled = False
        End If
        Rrow = tmp_Row
        With Rrow

            M_Code_Tb.Text = .Item("Materials_Code") & ""
            M_Py_Tb.Text = .Item("Materials_Py") & ""
            M_Name_Tb.Text = .Item("Materials_Name") & ""
            M_BulkUnit_Tb.Text = .Item("Bulk_Unit") & ""
            M_PackUnit_Tb.Text = .Item("Pack_Unit") & ""
            M_ConvertRatio_Tb.Text = .Item("Convert_Ratio") & ""
            M_MateManu_Tb.Text = .Item("MateManu_Name") & ""
            M_Spec_Tb.Text = .Item("Materials_Spec") & ""
            M_Class_Tb.Text = .Item("Class_Code") & ""
            If .Item("IsUse") = True Then
                IsUse_Cb.Checked = True
            Else
                IsUse_Cb.Checked = False
            End If
            M_Memo_Tb.Text = .Item("Materials_Memo") & ""

        End With
        Call Show_Label()
        Me.M_Name_Tb.Select()
    End Sub
    Private Sub Show_Label()
        M_Name_Tb.Select()
        If Rinsert = True Then
            Label2.Text = "新增"
        Else

            Label2.Text = IIf(RZbtb.Rows.Count() = 0, "0", CStr((RZbtb.Rows.IndexOf(Rrow)) + 1))
        End If
        Label3.Text = "∑=" & RZbtb.Rows.Count
    End Sub

#End Region

#Region "控件__动作"
    Private Sub Comm_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click, Button2.Click
        Select Case sender.tag
            Case "保存"
                If Trim(M_Name_Tb.Text) = "" Then
                    MsgBox("名称不能为空！", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示")
                    M_Name_Tb.Select()
                ElseIf Trim(M_PackUnit_Tb.Text = "") Then
                    MsgBox("包装单位不能为空！", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示")
                    M_PackUnit_Tb.Select()
                ElseIf Trim(M_ConvertRatio_Tb.Text) = 0 Then
                    MsgBox("拆分比例不能为0！", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示")
                    M_ConvertRatio_Tb.Select()
                ElseIf Trim(M_ConvertRatio_Tb.Text) = "" Then
                    MsgBox("拆分比例不能为空！", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示")
                    M_ConvertRatio_Tb.Select()
                ElseIf Trim(M_BulkUnit_Tb.Text) = "" Then
                    MsgBox("散装单位不能为空！", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示")
                    M_BulkUnit_Tb.Select()
                Else
                    If Rinsert = True Then     '增加记录
                        Call Data_Add()
                    Else                       '编辑记录
                        Call Data_Edit()
                    End If
                End If
            Case "取消"
                Me.Close()
        End Select
    End Sub

    Private Sub Move_Click(ByVal sender As Object, ByVal e As C1.Win.C1Command.ClickEventArgs) Handles Move1.Click, Move2.Click, Move3.Click, Move4.Click, Move5.Click
        If sender.text = "新增" Then                            '增加状态
            Call Data_Clear()
        Else
            Rinsert = False
            Rrc.GridMove(sender.text)
        End If
    End Sub

    Private Sub TextBox_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles IsUse_Cb.KeyPress
        If e.KeyChar <> Chr(Keys.Return) Then Exit Sub
        e.Handled = True
        System.Windows.Forms.SendKeys.Send("{Tab}")
    End Sub

    Private Sub ConvertRatio_Tb_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles M_ConvertRatio_Tb.KeyPress
        If Char.IsDigit(e.KeyChar) Or e.KeyChar = Chr(8) Then
            e.Handled = False
        Else
            e.Handled = True
        End If
    End Sub

    Private Sub DevNameTb_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles M_Name_Tb.Validated
        M_Py_Tb.Text = JCSpell.GetPy(Me.M_Name_Tb.Text & "")
    End Sub

#End Region

#Region "数据__编辑"

    Private Sub Data_Add()
        Dim My_NewRow As DataRow = RZbtb.NewRow
        With My_NewRow
            .Item("Materials_Name") = Trim(M_Name_Tb.Text & "")
            .Item("Materials_Code") = BllMDic2.MaxCode
            .Item("Materials_Py") = Trim(M_Py_Tb.Text & "")
            .Item("Bulk_Unit") = Trim(M_BulkUnit_Tb.Text & "")
            .Item("Pack_Unit") = Trim(M_PackUnit_Tb.Text & "")
            .Item("Convert_Ratio") = Trim(M_ConvertRatio_Tb.Text & "")
            .Item("MateManu_Name") = Trim(M_MateManu_Tb.Text & "")
            .Item("Materials_Spec") = Trim(M_Spec_Tb.Text & "")
            .Item("Class_Code") = Trim(M_Class_Tb.Text & "")
            If IsUse_Cb.Checked = True Then
                .Item("IsUse") = True
            Else
                .Item("IsUse") = False
            End If
            .Item("Materials_Memo") = Trim(M_Memo_Tb.Text & "")

        End With

        '数据保存
        Try
            RZbtb.Rows.Add(My_NewRow)
            Rrc.GridMove("最后")
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            Exit Sub
        Finally
            M_Name_Tb.Select()
        End Try

        '数据更新
        Try
            With ModelMDic2
                .Materials_Code = My_NewRow("Materials_Code")
                .Materials_Name = My_NewRow("Materials_Name")
                .Materials_Py = My_NewRow("Materials_Py")
                .Bulk_Unit = My_NewRow("Bulk_Unit")
                .Pack_Unit = My_NewRow("Pack_Unit")
                .Convert_Ratio = My_NewRow("Convert_Ratio")
                .MateManu_Name = My_NewRow("MateManu_Name")
                .Materials_Spec = My_NewRow("Materials_Spec")
                .Class_Code = My_NewRow("Class_Code")
                .IsUse = My_NewRow("IsUse")
                .Materials_Memo = My_NewRow("Materials_Memo")
            End With
            BllMDic2.Add(ModelMDic2)

            My_NewRow.AcceptChanges()
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            Exit Sub
        Finally
            M_Name_Tb.Select()
        End Try

        RtreeView.SelectedNode.Text = Mid(RtreeView.SelectedNode.Text, 1, InStrRev(RtreeView.SelectedNode.Text, "(")) & BllMDic2.GetRecordCount("Class_Code='" & Trim(M_Class_Tb.Text) & "'") & ")"

        '数据清空
        Call Data_Clear()

    End Sub

    Private Sub Data_Edit()
        Try
            With Rrow
                .BeginEdit()
                .Item("Materials_Name") = Trim(M_Name_Tb.Text & "")
                .Item("Materials_Code") = Trim(M_Code_Tb.Text & "")
                .Item("Materials_Py") = Trim(M_Py_Tb.Text & "")
                .Item("Bulk_Unit") = Trim(M_BulkUnit_Tb.Text & "")
                .Item("Pack_Unit") = Trim(M_PackUnit_Tb.Text & "")
                .Item("Convert_Ratio") = Trim(M_ConvertRatio_Tb.Text & "")
                .Item("MateManu_Name") = Trim(M_MateManu_Tb.Text & "")
                .Item("Materials_Spec") = Trim(M_Spec_Tb.Text & "")
                .Item("Class_Code") = Trim(M_Class_Tb.Text & "")
                If IsUse_Cb.Checked = True Then
                    .Item("IsUse") = True
                Else
                    .Item("IsUse") = False
                End If
                .Item("Materials_Memo") = Trim(M_Memo_Tb.Text & "")

                .EndEdit()
            End With
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Critical, "提示:")
            Rrow.CancelEdit()
            Exit Sub
        Finally
            M_Name_Tb.Select()
        End Try

        '数据更新
        Try
            With ModelMDic2
                .Materials_Code = Rrow("Materials_Code")
                .Materials_Name = Rrow("Materials_Name")
                .Materials_Py = Rrow("Materials_Py")
                .Bulk_Unit = Rrow("Bulk_Unit")
                .Pack_Unit = Rrow("Pack_Unit")
                .Convert_Ratio = Rrow("Convert_Ratio")
                .MateManu_Name = Rrow("MateManu_Name")
                .Materials_Spec = Rrow("Materials_Spec")
                .Class_Code = Rrow("Class_Code")
                .IsUse = Rrow("IsUse")
                .Materials_Memo = Rrow("Materials_Memo")
            End With
            BllMDic2.Update(ModelMDic2)
            Rrow.AcceptChanges()
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
        Finally
            M_Name_Tb.Select()
        End Try
        ' End With

    End Sub

#End Region

#Region "输入法设置"
    'English英語
    Private Sub C1TextBox1_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles M_ConvertRatio_Tb.GotFocus
        InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages(0)
    End Sub

    'Chinese中国語
    Private Sub C1TextBox2_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles M_Name_Tb.GotFocus, M_Spec_Tb.GotFocus, M_PackUnit_Tb.GotFocus, M_BulkUnit_Tb.GotFocus, M_Memo_Tb.GotFocus, M_MateManu_Tb.GotFocus
        InputLanguage.CurrentInputLanguage = HisVar.HisVar.InputCode
    End Sub
#End Region
End Class
