﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.30729</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{AD6CA345-D90D-4C1A-A6D7-D3E9119F883C}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <StartupObject>Sub Main</StartupObject>
    <RootNamespace>His2010</RootNamespace>
    <AssemblyName>His2010</AssemblyName>
    <FileAlignment>512</FileAlignment>
    <MyType>WindowsFormsWithCustomSubMain</MyType>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <OptionExplicit>On</OptionExplicit>
    <OptionCompare>Binary</OptionCompare>
    <OptionStrict>Off</OptionStrict>
    <OptionInfer>On</OptionInfer>
    <ManifestCertificateThumbprint>23DDE51CDA06346D628A9E872BF57B16F21DD06A</ManifestCertificateThumbprint>
    <ManifestKeyFile>MyKey.pfx</ManifestKeyFile>
    <GenerateManifests>true</GenerateManifests>
    <SignManifests>true</SignManifests>
    <IsWebBootstrapper>true</IsWebBootstrapper>
    <ApplicationIcon>44副本.ico</ApplicationIcon>
    <ApplicationManifest>My Project\app.manifest</ApplicationManifest>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <OldToolsVersion>3.5</OldToolsVersion>
    <TargetFrameworkProfile />
    <PublishUrl>D:\IIS\ZTHis4\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Web</InstallFrom>
    <UpdateEnabled>true</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <InstallUrl>http://192.168.1.100/His2019/</InstallUrl>
    <ProductName>医院管理系统4.0</ProductName>
    <PublisherName>中软智通（唐山）科技有限公司</PublisherName>
    <WebPage>index.htm</WebPage>
    <OpenBrowserOnPublish>false</OpenBrowserOnPublish>
    <ApplicationRevision>525</ApplicationRevision>
    <ApplicationVersion>4.0.0.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <CreateDesktopShortcut>true</CreateDesktopShortcut>
    <PublishWizardCompleted>true</PublishWizardCompleted>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <BootstrapperComponentsLocation>Relative</BootstrapperComponentsLocation>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <OutputPath>..\output\</OutputPath>
    <DocumentationFile>His2010.xml</DocumentationFile>
    <NoWarn>41999,42016,42017,42018,42019,42020,42021,42022,42032,42036,42353,42354,42355</NoWarn>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
    <PlatformTarget>x86</PlatformTarget>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DocumentationFile>His2010.xml</DocumentationFile>
    <NoWarn>41999,42016,42017,42018,42019,42020,42021,42022,42032,42036,42353,42354,42355</NoWarn>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup>
    <SignAssembly>false</SignAssembly>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="ActiveReports.Document, Version=6.2.3924.2, Culture=neutral, PublicKeyToken=096a9279a87304f1, processorArchitecture=MSIL">
      <Private>True</Private>
    </Reference>
    <Reference Include="ActiveReports.Viewer6, Version=6.2.3924.2, Culture=neutral, PublicKeyToken=096a9279a87304f1, processorArchitecture=MSIL">
      <Private>True</Private>
    </Reference>
    <Reference Include="ActiveReports6, Version=6.2.3924.2, Culture=neutral, PublicKeyToken=096a9279a87304f1, processorArchitecture=MSIL">
      <Private>True</Private>
    </Reference>
    <Reference Include="C1.C1Excel.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.C1Pdf.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.C1Report.4, Version=4.0.20192.375, Culture=neutral, PublicKeyToken=594a0605db190bb9, processorArchitecture=MSIL" />
    <Reference Include="C1.C1Zip.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=79882d576c6336da">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=944ae1ea0e47ca04, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.C1Command.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=e808566f358766d8, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.C1DX.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=944ae1ea0e47ca04">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.C1FlexGrid.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.C1FlexGrid.Classic.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.C1FlexGrid.SearchPanel.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.C1Input.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=7e7ff60f0c214f9a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.C1List.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=6b24f8f981dbd7bc, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.C1Report.4, Version=4.0.20192.375, Culture=neutral, PublicKeyToken=41780e2fc605e636, processorArchitecture=MSIL" />
    <Reference Include="C1.Win.C1Ribbon.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.C1SuperTooltip.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.C1TrueDBGrid.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=75ae3fb0e2b1e0da, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="C1.Win.TreeView.4, Version=4.0.20163.212, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="DDTek.Oracle, Version=*******, Culture=neutral, PublicKeyToken=c84cd5c63851e072, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dll\DDTek.Oracle.dll</HintPath>
    </Reference>
    <Reference Include="DDTekOracleDalHelper, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dll\DDTekOracleDalHelper.dll</HintPath>
    </Reference>
    <Reference Include="EnCode, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dll\EnCode.dll</HintPath>
    </Reference>
    <Reference Include="iniOperate">
      <HintPath>..\Dll\iniOperate.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=6.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\DLL\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="oledbDalHelper, Version=1.0.0.2, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dll\oledbDalHelper.dll</HintPath>
    </Reference>
    <Reference Include="SqlDal, Version=1.0.0.16, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dll\SqlDal.dll</HintPath>
    </Reference>
    <Reference Include="SqlLiteHelper, Version=1.0.0.2, Culture=neutral, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dll\SqlLiteHelper.dll</HintPath>
    </Reference>
    <Reference Include="Stimulsoft.Base, Version=2011.2.1026.0, Culture=neutral, PublicKeyToken=ebe6666cba19647a, processorArchitecture=MSIL">
      <Private>True</Private>
    </Reference>
    <Reference Include="Stimulsoft.Controls, Version=2011.2.1026.0, Culture=neutral, PublicKeyToken=ebe6666cba19647a, processorArchitecture=MSIL">
      <Private>True</Private>
    </Reference>
    <Reference Include="Stimulsoft.Controls.Win, Version=2011.2.1026.0, Culture=neutral, PublicKeyToken=ebe6666cba19647a, processorArchitecture=MSIL">
      <Private>True</Private>
    </Reference>
    <Reference Include="Stimulsoft.Design, Version=2011.2.1026.0, Culture=neutral, PublicKeyToken=ebe6666cba19647a, processorArchitecture=MSIL">
      <Private>True</Private>
    </Reference>
    <Reference Include="Stimulsoft.Editor, Version=2011.2.1026.0, Culture=neutral, PublicKeyToken=ebe6666cba19647a, processorArchitecture=MSIL">
      <Private>True</Private>
    </Reference>
    <Reference Include="Stimulsoft.Report, Version=2011.2.1026.0, Culture=neutral, PublicKeyToken=ebe6666cba19647a, processorArchitecture=MSIL">
      <Private>True</Private>
    </Reference>
    <Reference Include="Stimulsoft.Report.Check, Version=2011.2.1026.0, Culture=neutral, PublicKeyToken=ebe6666cba19647a, processorArchitecture=MSIL">
      <Private>True</Private>
    </Reference>
    <Reference Include="Stimulsoft.Report.Design, Version=2011.2.1026.0, Culture=neutral, PublicKeyToken=ebe6666cba19647a, processorArchitecture=MSIL">
      <Private>True</Private>
    </Reference>
    <Reference Include="Stimulsoft.Report.Helper, Version=2011.2.1026.0, Culture=neutral, PublicKeyToken=ebe6666cba19647a, processorArchitecture=MSIL">
      <Private>True</Private>
    </Reference>
    <Reference Include="Stimulsoft.Report.Win, Version=2011.2.1026.0, Culture=neutral, PublicKeyToken=ebe6666cba19647a, processorArchitecture=MSIL">
      <Private>True</Private>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Data.SQLite, Version=1.0.82.0, Culture=neutral, PublicKeyToken=db937bc2d44ff139, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dll\System.Data.SQLite.dll</HintPath>
    </Reference>
    <Reference Include="System.Deployment" />
    <Reference Include="System.Design" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Drawing.Design" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="System.Security" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Import Include="Microsoft.VisualBasic" />
    <Import Include="System" />
    <Import Include="System.Collections" />
    <Import Include="System.Collections.Generic" />
    <Import Include="System.Data" />
    <Import Include="System.Drawing" />
    <Import Include="System.Diagnostics" />
    <Import Include="System.Windows.Forms" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="0.公用类\C_Button.vb" />
    <Compile Include="0.公用类\F_Model.vb" />
    <Compile Include="00.启动\F_Login.designer.vb">
      <DependentUpon>F_Login.vb</DependentUpon>
    </Compile>
    <Compile Include="00.启动\F_Login.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="00.启动\MainForm.designer.vb">
      <DependentUpon>MainForm.vb</DependentUpon>
    </Compile>
    <Compile Include="00.启动\MainForm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="01.基础数据\1.01.标准字典\药材字典\Zd_Yp11.designer.vb">
      <DependentUpon>Zd_Yp11.vb</DependentUpon>
    </Compile>
    <Compile Include="01.基础数据\1.01.标准字典\药材字典\Zd_Yp11.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="01.基础数据\1.01.标准字典\药材字典\Zd_Yp12.designer.vb">
      <DependentUpon>Zd_Yp12.vb</DependentUpon>
    </Compile>
    <Compile Include="01.基础数据\1.01.标准字典\药材字典\Zd_Yp12.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="01.基础数据\1.01.标准字典\药材字典\Zd_Yp13.designer.vb">
      <DependentUpon>Zd_Yp13.vb</DependentUpon>
    </Compile>
    <Compile Include="01.基础数据\1.01.标准字典\药材字典\Zd_Yp13.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="01.基础数据\1.01.标准字典\药材字典\Zd_Yp13_Database.designer.vb">
      <DependentUpon>Zd_Yp13_Database.vb</DependentUpon>
    </Compile>
    <Compile Include="01.基础数据\1.01.标准字典\药材字典\Zd_Yp13_Database.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="01.基础数据\1.01.标准字典\药材字典\Zd_Yp14.designer.vb">
      <DependentUpon>Zd_Yp14.vb</DependentUpon>
    </Compile>
    <Compile Include="01.基础数据\1.01.标准字典\药材字典\Zd_Yp14.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="01.基础数据\1.01.标准字典\药材字典\Zd_Yp15.Designer.vb">
      <DependentUpon>Zd_Yp15.vb</DependentUpon>
    </Compile>
    <Compile Include="01.基础数据\1.01.标准字典\药材字典\Zd_Yp15.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="01.基础数据\1.01.标准字典\药材字典\Zd_Yp15_Database.Designer.vb">
      <DependentUpon>Zd_Yp15_Database.vb</DependentUpon>
    </Compile>
    <Compile Include="01.基础数据\1.01.标准字典\药材字典\Zd_Yp15_Database.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="01.基础数据\1.04.发票设置\02.发票边距调整\Fp_Tz.Designer.vb">
      <DependentUpon>Fp_Tz.vb</DependentUpon>
    </Compile>
    <Compile Include="01.基础数据\1.04.发票设置\02.发票边距调整\Fp_Tz.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="01.基础数据\1.04.发票设置\07.门诊发票合并\Zd_MzFpHb1.Designer.vb">
      <DependentUpon>Zd_MzFpHb1.vb</DependentUpon>
    </Compile>
    <Compile Include="01.基础数据\1.04.发票设置\07.门诊发票合并\Zd_MzFpHb1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="01.基础数据\1.04.发票设置\07.门诊发票合并\Zd_MzFpHb2.Designer.vb">
      <DependentUpon>Zd_MzFpHb2.vb</DependentUpon>
    </Compile>
    <Compile Include="01.基础数据\1.04.发票设置\07.门诊发票合并\Zd_MzFpHb2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="01.基础数据\1.04.发票设置\07.门诊发票合并\Zd_MzFpHb3.Designer.vb">
      <DependentUpon>Zd_MzFpHb3.vb</DependentUpon>
    </Compile>
    <Compile Include="01.基础数据\1.04.发票设置\07.门诊发票合并\Zd_MzFpHb3.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="01.基础数据\1.04.发票设置\门诊发票分类\Zd_MzFp1.Designer.vb">
      <DependentUpon>Zd_MzFp1.vb</DependentUpon>
    </Compile>
    <Compile Include="01.基础数据\1.04.发票设置\门诊发票分类\Zd_MzFp1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="01.基础数据\1.04.发票设置\门诊发票分类\Zd_MzFp2.designer.vb">
      <DependentUpon>Zd_MzFp2.vb</DependentUpon>
    </Compile>
    <Compile Include="01.基础数据\1.04.发票设置\门诊发票分类\Zd_MzFp2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="01.基础数据\1.04.发票设置\门诊发票分类\Zd_MzFp3.designer.vb">
      <DependentUpon>Zd_MzFp3.vb</DependentUpon>
    </Compile>
    <Compile Include="01.基础数据\1.04.发票设置\门诊发票分类\Zd_MzFp3.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="01.基础数据\1.04.发票设置\门诊发票第三联分类\Zd_MzFp11.Designer.vb">
      <DependentUpon>Zd_MzFp11.vb</DependentUpon>
    </Compile>
    <Compile Include="01.基础数据\1.04.发票设置\门诊发票第三联分类\Zd_MzFp11.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="01.基础数据\1.04.发票设置\门诊发票第三联分类\Zd_MzFp12.designer.vb">
      <DependentUpon>Zd_MzFp12.vb</DependentUpon>
    </Compile>
    <Compile Include="01.基础数据\1.04.发票设置\门诊发票第三联分类\Zd_MzFp12.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="01.基础数据\1.04.发票设置\门诊发票第三联分类\Zd_MzFp13.designer.vb">
      <DependentUpon>Zd_MzFp13.vb</DependentUpon>
    </Compile>
    <Compile Include="01.基础数据\1.04.发票设置\门诊发票第三联分类\Zd_MzFp13.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="01.基础数据\1.04.发票设置\门诊发票第三联分类\Zd_MzFp14.Designer.vb">
      <DependentUpon>Zd_MzFp14.vb</DependentUpon>
    </Compile>
    <Compile Include="01.基础数据\1.04.发票设置\门诊发票第三联分类\Zd_MzFp14.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="01.基础数据\1.04.发票设置\门诊发票第三联分类\Zd_MzFp15.designer.vb">
      <DependentUpon>Zd_MzFp15.vb</DependentUpon>
    </Compile>
    <Compile Include="01.基础数据\1.04.发票设置\门诊发票第三联分类\Zd_MzFp15.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="02.药库管理\药库接收药房退库\Yk_Js1.Designer.vb">
      <DependentUpon>Yk_Js1.vb</DependentUpon>
    </Compile>
    <Compile Include="02.药库管理\药库接收药房退库\Yk_Js1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="02.药库管理\药库接收药房退库\Yk_Js2.Designer.vb">
      <DependentUpon>Yk_Js2.vb</DependentUpon>
    </Compile>
    <Compile Include="02.药库管理\药库接收药房退库\Yk_Js2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="02.药库管理\药库调价\Yk_Tj1.designer.vb">
      <DependentUpon>Yk_Tj1.vb</DependentUpon>
    </Compile>
    <Compile Include="02.药库管理\药库调价\Yk_Tj1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="02.药库管理\药库调价\Yk_Tj2.designer.vb">
      <DependentUpon>Yk_Tj2.vb</DependentUpon>
    </Compile>
    <Compile Include="02.药库管理\药库调价\Yk_Tj2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="02.药库管理\药库调价\Yk_Tj31.designer.vb">
      <DependentUpon>Yk_Tj31.vb</DependentUpon>
    </Compile>
    <Compile Include="02.药库管理\药库调价\Yk_Tj31.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="02.药库管理\调价生效\Yk_TjQp1.designer.vb">
      <DependentUpon>Yk_TjQp1.vb</DependentUpon>
    </Compile>
    <Compile Include="02.药库管理\调价生效\Yk_TjQp1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="02.药库管理\调价生效\Yk_TjQp2.designer.vb">
      <DependentUpon>Yk_TjQp2.vb</DependentUpon>
    </Compile>
    <Compile Include="02.药库管理\调价生效\Yk_TjQp2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="02.药库管理\调拨药房\Yk_Yf4.designer.vb">
      <DependentUpon>Yk_Yf4.vb</DependentUpon>
    </Compile>
    <Compile Include="02.药库管理\调拨药房\Yk_Yf4.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="03.药房管理\01.药房接收药库\Yf_Js1.Designer.vb">
      <DependentUpon>Yf_Js1.vb</DependentUpon>
    </Compile>
    <Compile Include="03.药房管理\01.药房接收药库\Yf_Js1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="03.药房管理\02.住院药房发药\Zy_Fy1.Designer.vb">
      <DependentUpon>Zy_Fy1.vb</DependentUpon>
    </Compile>
    <Compile Include="03.药房管理\02.住院药房发药\Zy_Fy1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="03.药房管理\08.药房日结\Ar_YfYp_Jz.Designer.vb">
      <DependentUpon>Ar_YfYp_Jz.vb</DependentUpon>
    </Compile>
    <Compile Include="03.药房管理\08.药房日结\Ar_YfYp_Jz.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="03.药房管理\08.药房日结\Ar_YfYp_Jz0.Designer.vb">
      <DependentUpon>Ar_YfYp_Jz0.vb</DependentUpon>
    </Compile>
    <Compile Include="03.药房管理\08.药房日结\Ar_YfYp_Jz0.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="03.药房管理\08.药房日结\Ar_Yf_Jz.Designer.vb">
      <DependentUpon>Ar_Yf_Jz.vb</DependentUpon>
    </Compile>
    <Compile Include="03.药房管理\08.药房日结\Ar_Yf_Jz.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="04.门诊管理\02.门诊录入\Ar_Mz_Print.Designer.vb">
      <DependentUpon>Ar_Mz_Print.vb</DependentUpon>
    </Compile>
    <Compile Include="04.门诊管理\02.门诊录入\Ar_Mz_Print.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="04.门诊管理\02.门诊录入\Ar_Mz_Print_Hz.Designer.vb">
      <DependentUpon>Ar_Mz_Print_Hz.vb</DependentUpon>
    </Compile>
    <Compile Include="04.门诊管理\02.门诊录入\Ar_Mz_Print_Hz.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="04.门诊管理\02.门诊录入\Xs_Mz1.designer.vb">
      <DependentUpon>Xs_Mz1.vb</DependentUpon>
    </Compile>
    <Compile Include="04.门诊管理\02.门诊录入\Xs_Mz1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="03.药房管理\02.住院药房发药\Zy_Fy2.Designer.vb">
      <DependentUpon>Zy_Fy2.vb</DependentUpon>
    </Compile>
    <Compile Include="03.药房管理\02.住院药房发药\Zy_Fy2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="03.药房管理\02.住院药房发药\Zy_Fy3.designer.vb">
      <DependentUpon>Zy_Fy3.vb</DependentUpon>
    </Compile>
    <Compile Include="03.药房管理\02.住院药房发药\Zy_Fy3.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="03.药房管理\06.住院药房退药\Zy_Ty.designer.vb">
      <DependentUpon>Zy_Ty.vb</DependentUpon>
    </Compile>
    <Compile Include="03.药房管理\06.住院药房退药\Zy_Ty.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="03.药房管理\01.药房接收药库\Yf_Js2.Designer.vb">
      <DependentUpon>Yf_Js2.vb</DependentUpon>
    </Compile>
    <Compile Include="03.药房管理\01.药房接收药库\Yf_Js2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="03.药房管理\08.药房日结\Yf_Jz.Designer.vb">
      <DependentUpon>Yf_Jz.vb</DependentUpon>
    </Compile>
    <Compile Include="03.药房管理\08.药房日结\Yf_Jz.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="04.门诊管理\02.门诊录入\Xs_Mz31.designer.vb">
      <DependentUpon>Xs_Mz31.vb</DependentUpon>
    </Compile>
    <Compile Include="04.门诊管理\02.门诊录入\Xs_Mz31.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="04.门诊管理\02.门诊录入\Xs_Mz2.designer.vb">
      <DependentUpon>Xs_Mz2.vb</DependentUpon>
    </Compile>
    <Compile Include="04.门诊管理\02.门诊录入\Xs_Mz2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="04.门诊管理\02.门诊录入\Xs_Mz3.designer.vb">
      <DependentUpon>Xs_Mz3.vb</DependentUpon>
    </Compile>
    <Compile Include="04.门诊管理\02.门诊录入\Xs_Mz3.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="04.门诊管理\02.门诊录入\Xs_Mz34.Designer.vb">
      <DependentUpon>Xs_Mz34.vb</DependentUpon>
    </Compile>
    <Compile Include="04.门诊管理\02.门诊录入\Xs_Mz34.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="04.门诊管理\02.门诊录入\Xs_Mz_Js.Designer.vb">
      <DependentUpon>Xs_Mz_Js.vb</DependentUpon>
    </Compile>
    <Compile Include="04.门诊管理\02.门诊录入\Xs_Mz_Js.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="04.门诊管理\04.医生开处方\Ys_Cf2.designer.vb">
      <DependentUpon>Ys_Cf2.vb</DependentUpon>
    </Compile>
    <Compile Include="04.门诊管理\04.医生开处方\Ys_Cf2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="04.门诊管理\04.医生开处方\Ys_Cf4.Designer.vb">
      <DependentUpon>Ys_Cf4.vb</DependentUpon>
    </Compile>
    <Compile Include="04.门诊管理\04.医生开处方\Ys_Cf4.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="05.住院管理\01.病例录入\Zd_Bl11.designer.vb">
      <DependentUpon>Zd_Bl11.vb</DependentUpon>
    </Compile>
    <Compile Include="05.住院管理\01.病例录入\Zd_Bl11.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="05.住院管理\03.医嘱录入\Zy_Cf1.designer.vb">
      <DependentUpon>Zy_Cf1.vb</DependentUpon>
    </Compile>
    <Compile Include="05.住院管理\03.医嘱录入\Zy_Cf1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="05.住院管理\04.出院\Xs_Zy_Cy1.designer.vb">
      <DependentUpon>Xs_Zy_Cy1.vb</DependentUpon>
    </Compile>
    <Compile Include="05.住院管理\04.出院\Xs_Zy_Cy1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="05.住院管理\05.出院召回\Cy_Zh1.Designer.vb">
      <DependentUpon>Cy_Zh1.vb</DependentUpon>
    </Compile>
    <Compile Include="05.住院管理\05.出院召回\Cy_Zh1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="05.住院管理\06.住院日结\Xs_Zy_Jz.Designer.vb">
      <DependentUpon>Xs_Zy_Jz.vb</DependentUpon>
    </Compile>
    <Compile Include="05.住院管理\06.住院日结\Xs_Zy_Jz.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="05.住院管理\06.住院日结\ZyRj_Print.Designer.vb">
      <DependentUpon>ZyRj_Print.vb</DependentUpon>
    </Compile>
    <Compile Include="05.住院管理\06.住院日结\ZyRj_Print.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="05.住院管理\06.住院日结\Zy_Jz_Zf.Designer.vb">
      <DependentUpon>Zy_Jz_Zf.vb</DependentUpon>
    </Compile>
    <Compile Include="05.住院管理\06.住院日结\Zy_Jz_Zf.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="05.住院管理\04.出院\Ar_Zy_Cy.Designer.vb">
      <DependentUpon>Ar_Zy_Cy.vb</DependentUpon>
    </Compile>
    <Compile Include="05.住院管理\04.出院\Ar_Zy_Cy.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="05.住院管理\05.出院召回\Cy_Zh21.Designer.vb">
      <DependentUpon>Cy_Zh21.vb</DependentUpon>
    </Compile>
    <Compile Include="05.住院管理\05.出院召回\Cy_Zh21.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="05.住院管理\03.医嘱录入\Zy_Cf2.designer.vb">
      <DependentUpon>Zy_Cf2.vb</DependentUpon>
    </Compile>
    <Compile Include="05.住院管理\03.医嘱录入\Zy_Cf2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="05.住院管理\03.医嘱录入\Zy_Cf3.designer.vb">
      <DependentUpon>Zy_Cf3.vb</DependentUpon>
    </Compile>
    <Compile Include="05.住院管理\03.医嘱录入\Zy_Cf3.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="05.住院管理\03.医嘱录入\Zy_Cf31.designer.vb">
      <DependentUpon>Zy_Cf31.vb</DependentUpon>
    </Compile>
    <Compile Include="05.住院管理\03.医嘱录入\Zy_Cf31.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="05.住院管理\03.医嘱录入\Zy_Cf34.Designer.vb">
      <DependentUpon>Zy_Cf34.vb</DependentUpon>
    </Compile>
    <Compile Include="05.住院管理\03.医嘱录入\Zy_Cf34.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="05.住院管理\01.病例录入\Zd_Bl12.designer.vb">
      <DependentUpon>Zd_Bl12.vb</DependentUpon>
    </Compile>
    <Compile Include="05.住院管理\01.病例录入\Zd_Bl12.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="05.住院管理\07.长期医嘱\Cq_Cf1.designer.vb">
      <DependentUpon>Cq_Cf1.vb</DependentUpon>
    </Compile>
    <Compile Include="05.住院管理\07.长期医嘱\Cq_Cf1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="05.住院管理\07.长期医嘱\Cq_Cf2.designer.vb">
      <DependentUpon>Cq_Cf2.vb</DependentUpon>
    </Compile>
    <Compile Include="05.住院管理\07.长期医嘱\Cq_Cf2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="05.住院管理\07.长期医嘱\Cq_Cf3.designer.vb">
      <DependentUpon>Cq_Cf3.vb</DependentUpon>
    </Compile>
    <Compile Include="05.住院管理\07.长期医嘱\Cq_Cf3.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="05.住院管理\07.长期医嘱\Cq_Cf31.designer.vb">
      <DependentUpon>Cq_Cf31.vb</DependentUpon>
    </Compile>
    <Compile Include="05.住院管理\07.长期医嘱\Cq_Cf31.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="05.住院管理\07.长期医嘱\Cq_Cf34.Designer.vb">
      <DependentUpon>Cq_Cf34.vb</DependentUpon>
    </Compile>
    <Compile Include="05.住院管理\07.长期医嘱\Cq_Cf34.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="05.住院管理\08.住院病历添加\Cx_Cqyz.Designer.vb">
      <DependentUpon>Cx_Cqyz.vb</DependentUpon>
    </Compile>
    <Compile Include="05.住院管理\08.住院病历添加\Cx_Cqyz.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="05.住院管理\09.病历封存\Bl_Lock.designer.vb">
      <DependentUpon>Bl_Lock.vb</DependentUpon>
    </Compile>
    <Compile Include="05.住院管理\09.病历封存\Bl_Lock.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="05.住院管理\08.住院病历添加\Zy_Dzbl1.designer.vb">
      <DependentUpon>Zy_Dzbl1.vb</DependentUpon>
    </Compile>
    <Compile Include="05.住院管理\08.住院病历添加\Zy_Dzbl1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="05.住院管理\08.住院病历添加\Zy_Dzbl2.Designer.vb">
      <DependentUpon>Zy_Dzbl2.vb</DependentUpon>
    </Compile>
    <Compile Include="05.住院管理\08.住院病历添加\Zy_Dzbl2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="06.数据查询\01.住院查询\住院日结查询\Zy_Rj_Cx.Designer.vb">
      <DependentUpon>Zy_Rj_Cx.vb</DependentUpon>
    </Compile>
    <Compile Include="06.数据查询\01.住院查询\住院日结查询\Zy_Rj_Cx.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="06.数据查询\01.住院查询\住院未发药\Cx_WZyHz1.designer.vb">
      <DependentUpon>Cx_WZyHz1.vb</DependentUpon>
    </Compile>
    <Compile Include="06.数据查询\01.住院查询\住院未发药\Cx_WZyHz1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="06.数据查询\01.住院查询\在院患者费用\Cx_Zyhz1.designer.vb">
      <DependentUpon>Cx_Zyhz1.vb</DependentUpon>
    </Compile>
    <Compile Include="06.数据查询\01.住院查询\在院患者费用\Cx_Zyhz1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="06.数据查询\01.住院查询\在院患者费用\Cx_Zyhz2.designer.vb">
      <DependentUpon>Cx_Zyhz2.vb</DependentUpon>
    </Compile>
    <Compile Include="06.数据查询\01.住院查询\在院患者费用\Cx_Zyhz2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="06.数据查询\01.住院查询\在院患者费用\Cx_ZyYjMx.designer.vb">
      <DependentUpon>Cx_ZyYjMx.vb</DependentUpon>
    </Compile>
    <Compile Include="06.数据查询\01.住院查询\在院患者费用\Cx_ZyYjMx.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="06.数据查询\01.住院查询\患者用药清单\Cx_Hzyy1.Designer.vb">
      <DependentUpon>Cx_Hzyy1.vb</DependentUpon>
    </Compile>
    <Compile Include="06.数据查询\01.住院查询\患者用药清单\Cx_Hzyy1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="06.数据查询\01.住院查询\患者诊疗项目查询\ZyXmCx.Designer.vb">
      <DependentUpon>ZyXmCx.vb</DependentUpon>
    </Compile>
    <Compile Include="06.数据查询\01.住院查询\患者诊疗项目查询\ZyXmCx.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="06.数据查询\01.住院查询\病案首页导出\BasyExport.Designer.vb">
      <DependentUpon>BasyExport.vb</DependentUpon>
    </Compile>
    <Compile Include="06.数据查询\01.住院查询\病案首页导出\BasyExport.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="06.数据查询\01.住院查询\缴纳押金查询\Cx_Yj.designer.vb">
      <DependentUpon>Cx_Yj.vb</DependentUpon>
    </Compile>
    <Compile Include="06.数据查询\01.住院查询\缴纳押金查询\Cx_Yj.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="06.数据查询\03.药库查询\各药房信息查询\Yk_Cx_AllYf.Designer.vb">
      <DependentUpon>Yk_Cx_AllYf.vb</DependentUpon>
    </Compile>
    <Compile Include="06.数据查询\03.药库查询\各药房信息查询\Yk_Cx_AllYf.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="06.数据查询\03.药库查询\药库出入库台账\Yp_Cr_Tz.Designer.vb">
      <DependentUpon>Yp_Cr_Tz.vb</DependentUpon>
    </Compile>
    <Compile Include="06.数据查询\03.药库查询\药库出入库台账\Yp_Cr_Tz.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="10.健康卡管理\06.公务卡字典维护\Jkk_Gwk.Designer.vb">
      <DependentUpon>Jkk_Gwk.vb</DependentUpon>
    </Compile>
    <Compile Include="10.健康卡管理\06.公务卡字典维护\Jkk_Gwk.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="10.健康卡管理\JkkRyxx.Designer.vb">
      <DependentUpon>JkkRyxx.vb</DependentUpon>
    </Compile>
    <Compile Include="10.健康卡管理\JkkRyxx.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="10.健康卡管理\04.健康卡交易明细\Jkk_Mx.Designer.vb">
      <DependentUpon>Jkk_Mx.vb</DependentUpon>
    </Compile>
    <Compile Include="10.健康卡管理\04.健康卡交易明细\Jkk_Mx.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="10.健康卡管理\Jkk_Js.Designer.vb">
      <DependentUpon>Jkk_Js.vb</DependentUpon>
    </Compile>
    <Compile Include="10.健康卡管理\Jkk_Js.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="10.健康卡管理\03.健康卡交易日结\Jkk_Jz.Designer.vb">
      <DependentUpon>Jkk_Jz.vb</DependentUpon>
    </Compile>
    <Compile Include="10.健康卡管理\03.健康卡交易日结\Jkk_Jz.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="10.健康卡管理\05.健康卡日结查询\Jkk_JzData.Designer.vb">
      <DependentUpon>Jkk_JzData.vb</DependentUpon>
    </Compile>
    <Compile Include="10.健康卡管理\05.健康卡日结查询\Jkk_JzData.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="10.健康卡管理\01.办理新卡及充值\Jkk_BkCz.Designer.vb">
      <DependentUpon>Jkk_BkCz.vb</DependentUpon>
    </Compile>
    <Compile Include="10.健康卡管理\01.办理新卡及充值\Jkk_BkCz.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="10.健康卡管理\02.健康卡账户管理\Jkk_Th.Designer.vb">
      <DependentUpon>Jkk_Th.vb</DependentUpon>
    </Compile>
    <Compile Include="10.健康卡管理\02.健康卡账户管理\Jkk_Th.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="10.健康卡管理\09.POS机参数设置\POS_Set.Designer.vb">
      <DependentUpon>POS_Set.vb</DependentUpon>
    </Compile>
    <Compile Include="10.健康卡管理\09.POS机参数设置\POS_Set.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="88.公用窗体\药库药房出入库录入\YpNetCg_Dr.Designer.vb">
      <DependentUpon>YpNetCg_Dr.vb</DependentUpon>
    </Compile>
    <Compile Include="88.公用窗体\药库药房出入库录入\YpNetCg_Dr.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="06.数据查询\03.药库查询\药库调价查询\Yk_Tj_Cx1.Designer.vb">
      <DependentUpon>Yk_Tj_Cx1.vb</DependentUpon>
    </Compile>
    <Compile Include="06.数据查询\03.药库查询\药库调价查询\Yk_Tj_Cx1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="06.数据查询\04.药房查询\住院门诊发药综合查询\Cx_ZyMz_Fy.Designer.vb">
      <DependentUpon>Cx_ZyMz_Fy.vb</DependentUpon>
    </Compile>
    <Compile Include="06.数据查询\04.药房查询\住院门诊发药综合查询\Cx_ZyMz_Fy.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="06.数据查询\04.药房查询\住院门诊诊疗综合查询\Cx_ZyMz_Xm.Designer.vb">
      <DependentUpon>Cx_ZyMz_Xm.vb</DependentUpon>
    </Compile>
    <Compile Include="06.数据查询\04.药房查询\住院门诊诊疗综合查询\Cx_ZyMz_Xm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="06.数据查询\04.药房查询\药房药品出入库台账\Yf_Cr_Tz.Designer.vb">
      <DependentUpon>Yf_Cr_Tz.vb</DependentUpon>
    </Compile>
    <Compile Include="06.数据查询\04.药房查询\药房药品出入库台账\Yf_Cr_Tz.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="06.数据查询\05.财务查询\全院工作量统计\Work_Tj.Designer.vb">
      <DependentUpon>Work_Tj.vb</DependentUpon>
    </Compile>
    <Compile Include="06.数据查询\05.财务查询\全院工作量统计\Work_Tj.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="06.数据查询\05.财务查询\利润统计\Cw_lrtj1.Designer.vb">
      <DependentUpon>Cw_lrtj1.vb</DependentUpon>
    </Compile>
    <Compile Include="06.数据查询\05.财务查询\利润统计\Cw_lrtj1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="06.数据查询\05.财务查询\床位统计\Cw_Tj.designer.vb">
      <DependentUpon>Cw_Tj.vb</DependentUpon>
    </Compile>
    <Compile Include="06.数据查询\05.财务查询\床位统计\Cw_Tj.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="06.数据查询\05.财务查询\科室收入统计\JcKs_Money_Tj.Designer.vb">
      <DependentUpon>JcKs_Money_Tj.vb</DependentUpon>
    </Compile>
    <Compile Include="06.数据查询\05.财务查询\科室收入统计\JcKs_Money_Tj.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="06.数据查询\05.财务查询\科室收入统计\Money_Tj1.Designer.vb">
      <DependentUpon>Money_Tj1.vb</DependentUpon>
    </Compile>
    <Compile Include="06.数据查询\05.财务查询\科室收入统计\Money_Tj1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="06.数据查询\05.财务查询\门诊住院日结查询\Cw_Cx_MzZy_Rj1.Designer.vb">
      <DependentUpon>Cw_Cx_MzZy_Rj1.vb</DependentUpon>
    </Compile>
    <Compile Include="06.数据查询\05.财务查询\门诊住院日结查询\Cw_Cx_MzZy_Rj1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="06.数据查询\05.财务查询\门诊住院日结查询\Cw_Cx_MzZy_Rj2.Designer.vb">
      <DependentUpon>Cw_Cx_MzZy_Rj2.vb</DependentUpon>
    </Compile>
    <Compile Include="06.数据查询\05.财务查询\门诊住院日结查询\Cw_Cx_MzZy_Rj2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="06.数据查询\05.财务查询\门诊住院日结查询\Cw_Cx_MzZy_Rj3.Designer.vb">
      <DependentUpon>Cw_Cx_MzZy_Rj3.vb</DependentUpon>
    </Compile>
    <Compile Include="06.数据查询\05.财务查询\门诊住院日结查询\Cw_Cx_MzZy_Rj3.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="06.数据查询\项目售价查询\Cm_XmSj.Designer.vb">
      <DependentUpon>Cm_XmSj.vb</DependentUpon>
    </Compile>
    <Compile Include="06.数据查询\项目售价查询\Cm_XmSj.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="08.物资管理\物资出入库\Wz_Crk.designer.vb">
      <DependentUpon>Wz_Crk.vb</DependentUpon>
    </Compile>
    <Compile Include="08.物资管理\物资出入库\Wz_Crk.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="08.物资管理\物资字典\Zd_Wz1.Designer.vb">
      <DependentUpon>Zd_Wz1.vb</DependentUpon>
    </Compile>
    <Compile Include="08.物资管理\物资字典\Zd_Wz1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="08.物资管理\物资字典\Zd_Wz2.designer.vb">
      <DependentUpon>Zd_Wz2.vb</DependentUpon>
    </Compile>
    <Compile Include="08.物资管理\物资字典\Zd_Wz2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="08.物资管理\物资字典\Zd_Wz3.designer.vb">
      <DependentUpon>Zd_Wz3.vb</DependentUpon>
    </Compile>
    <Compile Include="08.物资管理\物资字典\Zd_Wz3.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="08.物资管理\物资库存查询\Wz_KcCx.Designer.vb">
      <DependentUpon>Wz_KcCx.vb</DependentUpon>
    </Compile>
    <Compile Include="08.物资管理\物资库存查询\Wz_KcCx.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="88.公用窗体\药库药房出入库查询\Cx_YkYf_Crk.Designer.vb">
      <DependentUpon>Cx_YkYf_Crk.vb</DependentUpon>
    </Compile>
    <Compile Include="88.公用窗体\药库药房出入库查询\Cx_YkYf_Crk.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="88.公用窗体\药库药房报损报溢\YkYf_Bsby.designer.vb">
      <DependentUpon>YkYf_Bsby.vb</DependentUpon>
    </Compile>
    <Compile Include="88.公用窗体\药库药房报损报溢\YkYf_Bsby.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="88.公用窗体\药库药房数据盘点\YkYf_Pd1.designer.vb">
      <DependentUpon>YkYf_Pd1.vb</DependentUpon>
    </Compile>
    <Compile Include="88.公用窗体\药库药房数据盘点\YkYf_Pd1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="88.公用窗体\药库药房数据盘点\YkYf_Pd2.designer.vb">
      <DependentUpon>YkYf_Pd2.vb</DependentUpon>
    </Compile>
    <Compile Include="88.公用窗体\药库药房数据盘点\YkYf_Pd2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="88.公用窗体\药库药房盘点查询\Cx_YkYf_Pd.designer.vb">
      <DependentUpon>Cx_YkYf_Pd.vb</DependentUpon>
    </Compile>
    <Compile Include="88.公用窗体\药库药房盘点查询\Cx_YkYf_Pd.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="88.公用窗体\药库药房警戒线查询\Cx_YkYf_Alar.Designer.vb">
      <DependentUpon>Cx_YkYf_Alar.vb</DependentUpon>
    </Compile>
    <Compile Include="88.公用窗体\药库药房警戒线查询\Cx_YkYf_Alar.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="98.提醒\01.药库提醒\Msg_YkYxq.Designer.vb">
      <DependentUpon>Msg_YkYxq.vb</DependentUpon>
    </Compile>
    <Compile Include="98.提醒\01.药库提醒\Msg_YkYxq.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="98.提醒\02.药房提醒\Msg_YfYxq.Designer.vb">
      <DependentUpon>Msg_YfYxq.vb</DependentUpon>
    </Compile>
    <Compile Include="98.提醒\02.药房提醒\Msg_YfYxq.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="99.系统设置\01.权限组\Zd_Qx1.designer.vb">
      <DependentUpon>Zd_Qx1.vb</DependentUpon>
    </Compile>
    <Compile Include="99.系统设置\01.权限组\Zd_Qx1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="99.系统设置\01.权限组\Zd_Qx2.designer.vb">
      <DependentUpon>Zd_Qx2.vb</DependentUpon>
    </Compile>
    <Compile Include="99.系统设置\01.权限组\Zd_Qx2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="99.系统设置\02.操作员管理\Zd_Czy1.designer.vb">
      <DependentUpon>Zd_Czy1.vb</DependentUpon>
    </Compile>
    <Compile Include="99.系统设置\02.操作员管理\Zd_Czy1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="99.系统设置\02.操作员管理\Zd_Czy2.designer.vb">
      <DependentUpon>Zd_Czy2.vb</DependentUpon>
    </Compile>
    <Compile Include="99.系统设置\02.操作员管理\Zd_Czy2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="99.系统设置\03.个人设置\Person_Config.Designer.vb">
      <DependentUpon>Person_Config.vb</DependentUpon>
    </Compile>
    <Compile Include="99.系统设置\03.个人设置\Person_Config.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="99.系统设置\05.数据库维护\DataBackUp.Designer.vb">
      <DependentUpon>DataBackUp.vb</DependentUpon>
    </Compile>
    <Compile Include="99.系统设置\05.数据库维护\DataBackUp.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="99.系统设置\Connect_Edit.Designer.vb">
      <DependentUpon>Connect_Edit.vb</DependentUpon>
    </Compile>
    <Compile Include="99.系统设置\Connect_Edit.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="99.系统设置\Zd_Cssz1.designer.vb">
      <DependentUpon>Zd_Cssz1.vb</DependentUpon>
    </Compile>
    <Compile Include="99.系统设置\Zd_Cssz1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="My Project\AssemblyInfo.vb" />
    <Compile Include="My Project\Application.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
    </Compile>
    <Compile Include="My Project\Resources.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="My Project\Settings.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <Compile Include="88.公用窗体\基础数据\Zd_Dict1.Designer.vb">
      <DependentUpon>Zd_Dict1.vb</DependentUpon>
    </Compile>
    <Compile Include="88.公用窗体\基础数据\Zd_Dict1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="88.公用窗体\基础数据\Zd_Dict2.Designer.vb">
      <DependentUpon>Zd_Dict2.vb</DependentUpon>
    </Compile>
    <Compile Include="88.公用窗体\基础数据\Zd_Dict2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="88.公用窗体\精简选择\Zd_JjSave.Designer.vb">
      <DependentUpon>Zd_JjSave.vb</DependentUpon>
    </Compile>
    <Compile Include="88.公用窗体\精简选择\Zd_JjSave.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="88.公用窗体\药库药房出入库录入\YkYf_Ck3.designer.vb">
      <DependentUpon>YkYf_Ck3.vb</DependentUpon>
    </Compile>
    <Compile Include="88.公用窗体\药库药房出入库录入\YkYf_Ck3.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="88.公用窗体\药库药房出入库录入\YkYf_Crk1.designer.vb">
      <DependentUpon>YkYf_Crk1.vb</DependentUpon>
    </Compile>
    <Compile Include="88.公用窗体\药库药房出入库录入\YkYf_Crk1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="88.公用窗体\药库药房出入库录入\YkYf_Crk2.designer.vb">
      <DependentUpon>YkYf_Crk2.vb</DependentUpon>
    </Compile>
    <Compile Include="88.公用窗体\药库药房出入库录入\YkYf_Crk2.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="88.公用窗体\药库药房出入库录入\YkYf_Rk3.designer.vb">
      <DependentUpon>YkYf_Rk3.vb</DependentUpon>
    </Compile>
    <Compile Include="88.公用窗体\药库药房出入库录入\YkYf_Rk3.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="88.公用窗体\药库药房出入库录入\YkYf_TkPf3.designer.vb">
      <DependentUpon>YkYf_TkPf3.vb</DependentUpon>
    </Compile>
    <Compile Include="88.公用窗体\药库药房出入库录入\YkYf_TkPf3.vb">
      <SubType>Form</SubType>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="00.启动\F_Login.resx">
      <DependentUpon>F_Login.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="00.启动\MainForm.resx">
      <DependentUpon>MainForm.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="01.基础数据\1.01.标准字典\药材字典\Zd_Yp11.resx">
      <DependentUpon>Zd_Yp11.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="01.基础数据\1.01.标准字典\药材字典\Zd_Yp12.resx">
      <DependentUpon>Zd_Yp12.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="01.基础数据\1.01.标准字典\药材字典\Zd_Yp13.resx">
      <DependentUpon>Zd_Yp13.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="01.基础数据\1.01.标准字典\药材字典\Zd_Yp13_Database.resx">
      <DependentUpon>Zd_Yp13_Database.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="01.基础数据\1.01.标准字典\药材字典\Zd_Yp14.resx">
      <DependentUpon>Zd_Yp14.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="01.基础数据\1.01.标准字典\药材字典\Zd_Yp15.resx">
      <DependentUpon>Zd_Yp15.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="01.基础数据\1.01.标准字典\药材字典\Zd_Yp15_Database.resx">
      <DependentUpon>Zd_Yp15_Database.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="01.基础数据\1.04.发票设置\02.发票边距调整\Fp_Tz.resx">
      <DependentUpon>Fp_Tz.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="01.基础数据\1.04.发票设置\07.门诊发票合并\Zd_MzFpHb1.resx">
      <DependentUpon>Zd_MzFpHb1.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="01.基础数据\1.04.发票设置\07.门诊发票合并\Zd_MzFpHb2.resx">
      <DependentUpon>Zd_MzFpHb2.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="01.基础数据\1.04.发票设置\07.门诊发票合并\Zd_MzFpHb3.resx">
      <DependentUpon>Zd_MzFpHb3.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="01.基础数据\1.04.发票设置\门诊发票分类\Zd_MzFp1.resx">
      <DependentUpon>Zd_MzFp1.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="01.基础数据\1.04.发票设置\门诊发票分类\Zd_MzFp2.resx">
      <DependentUpon>Zd_MzFp2.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="01.基础数据\1.04.发票设置\门诊发票分类\Zd_MzFp3.resx">
      <DependentUpon>Zd_MzFp3.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="01.基础数据\1.04.发票设置\门诊发票第三联分类\Zd_MzFp11.resx">
      <DependentUpon>Zd_MzFp11.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="01.基础数据\1.04.发票设置\门诊发票第三联分类\Zd_MzFp12.resx">
      <DependentUpon>Zd_MzFp12.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="01.基础数据\1.04.发票设置\门诊发票第三联分类\Zd_MzFp13.resx">
      <DependentUpon>Zd_MzFp13.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="01.基础数据\1.04.发票设置\门诊发票第三联分类\Zd_MzFp14.resx">
      <DependentUpon>Zd_MzFp14.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="01.基础数据\1.04.发票设置\门诊发票第三联分类\Zd_MzFp15.resx">
      <DependentUpon>Zd_MzFp15.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="02.药库管理\药库接收药房退库\Yk_Js1.resx">
      <DependentUpon>Yk_Js1.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="02.药库管理\药库接收药房退库\Yk_Js2.resx">
      <DependentUpon>Yk_Js2.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="02.药库管理\药库调价\Yk_Tj1.resx">
      <DependentUpon>Yk_Tj1.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="02.药库管理\药库调价\Yk_Tj2.resx">
      <DependentUpon>Yk_Tj2.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="02.药库管理\药库调价\Yk_Tj31.resx">
      <DependentUpon>Yk_Tj31.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="02.药库管理\调价生效\Yk_TjQp1.resx">
      <DependentUpon>Yk_TjQp1.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="02.药库管理\调价生效\Yk_TjQp2.resx">
      <DependentUpon>Yk_TjQp2.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="02.药库管理\调拨药房\Yk_Yf4.resx">
      <DependentUpon>Yk_Yf4.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="03.药房管理\01.药房接收药库\Yf_Js1.resx">
      <DependentUpon>Yf_Js1.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="03.药房管理\02.住院药房发药\Zy_Fy1.resx">
      <DependentUpon>Zy_Fy1.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="03.药房管理\08.药房日结\Ar_YfYp_Jz.resx">
      <DependentUpon>Ar_YfYp_Jz.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="03.药房管理\08.药房日结\Ar_YfYp_Jz0.resx">
      <DependentUpon>Ar_YfYp_Jz0.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="03.药房管理\08.药房日结\Ar_Yf_Jz.resx">
      <DependentUpon>Ar_Yf_Jz.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="04.门诊管理\02.门诊录入\Ar_Mz_Print.resx">
      <DependentUpon>Ar_Mz_Print.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="04.门诊管理\02.门诊录入\Ar_Mz_Print_Hz.resx">
      <DependentUpon>Ar_Mz_Print_Hz.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="04.门诊管理\02.门诊录入\Xs_Mz1.resx">
      <DependentUpon>Xs_Mz1.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="03.药房管理\02.住院药房发药\Zy_Fy2.resx">
      <DependentUpon>Zy_Fy2.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="03.药房管理\02.住院药房发药\Zy_Fy3.resx">
      <DependentUpon>Zy_Fy3.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="03.药房管理\06.住院药房退药\Zy_Ty.resx">
      <DependentUpon>Zy_Ty.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="03.药房管理\01.药房接收药库\Yf_Js2.resx">
      <DependentUpon>Yf_Js2.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="03.药房管理\08.药房日结\Yf_Jz.resx">
      <DependentUpon>Yf_Jz.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="04.门诊管理\02.门诊录入\Xs_Mz31.resx">
      <DependentUpon>Xs_Mz31.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="04.门诊管理\02.门诊录入\Xs_Mz2.resx">
      <DependentUpon>Xs_Mz2.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="04.门诊管理\02.门诊录入\Xs_Mz3.resx">
      <DependentUpon>Xs_Mz3.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="04.门诊管理\02.门诊录入\Xs_Mz34.resx">
      <DependentUpon>Xs_Mz34.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="04.门诊管理\02.门诊录入\Xs_Mz_Js.resx">
      <DependentUpon>Xs_Mz_Js.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="04.门诊管理\04.医生开处方\Ys_Cf2.resx">
      <DependentUpon>Ys_Cf2.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="04.门诊管理\04.医生开处方\Ys_Cf4.resx">
      <DependentUpon>Ys_Cf4.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="05.住院管理\01.病例录入\Zd_Bl11.resx">
      <DependentUpon>Zd_Bl11.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="05.住院管理\03.医嘱录入\Zy_Cf1.resx">
      <DependentUpon>Zy_Cf1.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="05.住院管理\04.出院\Xs_Zy_Cy1.resx">
      <DependentUpon>Xs_Zy_Cy1.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="05.住院管理\05.出院召回\Cy_Zh1.resx">
      <DependentUpon>Cy_Zh1.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="05.住院管理\06.住院日结\Xs_Zy_Jz.resx">
      <DependentUpon>Xs_Zy_Jz.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="05.住院管理\06.住院日结\ZyRj_Print.resx">
      <DependentUpon>ZyRj_Print.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="05.住院管理\06.住院日结\Zy_Jz_Zf.resx">
      <DependentUpon>Zy_Jz_Zf.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="05.住院管理\04.出院\Ar_Zy_Cy.resx">
      <DependentUpon>Ar_Zy_Cy.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="05.住院管理\05.出院召回\Cy_Zh21.resx">
      <DependentUpon>Cy_Zh21.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="05.住院管理\03.医嘱录入\Zy_Cf2.resx">
      <DependentUpon>Zy_Cf2.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="05.住院管理\03.医嘱录入\Zy_Cf3.resx">
      <DependentUpon>Zy_Cf3.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="05.住院管理\03.医嘱录入\Zy_Cf31.resx">
      <DependentUpon>Zy_Cf31.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="05.住院管理\03.医嘱录入\Zy_Cf34.resx">
      <DependentUpon>Zy_Cf34.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="05.住院管理\01.病例录入\Zd_Bl12.resx">
      <DependentUpon>Zd_Bl12.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="05.住院管理\07.长期医嘱\Cq_Cf1.resx">
      <DependentUpon>Cq_Cf1.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="05.住院管理\07.长期医嘱\Cq_Cf2.resx">
      <DependentUpon>Cq_Cf2.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="05.住院管理\07.长期医嘱\Cq_Cf3.resx">
      <DependentUpon>Cq_Cf3.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="05.住院管理\07.长期医嘱\Cq_Cf31.resx">
      <DependentUpon>Cq_Cf31.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="05.住院管理\07.长期医嘱\Cq_Cf34.resx">
      <DependentUpon>Cq_Cf34.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="05.住院管理\08.住院病历添加\Cx_Cqyz.resx">
      <DependentUpon>Cx_Cqyz.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="05.住院管理\09.病历封存\Bl_Lock.resx">
      <DependentUpon>Bl_Lock.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="05.住院管理\08.住院病历添加\Zy_Dzbl1.resx">
      <DependentUpon>Zy_Dzbl1.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="05.住院管理\08.住院病历添加\Zy_Dzbl2.resx">
      <DependentUpon>Zy_Dzbl2.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="06.数据查询\01.住院查询\住院日结查询\Zy_Rj_Cx.resx">
      <DependentUpon>Zy_Rj_Cx.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="06.数据查询\01.住院查询\住院未发药\Cx_WZyHz1.resx">
      <DependentUpon>Cx_WZyHz1.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="06.数据查询\01.住院查询\在院患者费用\Cx_Zyhz1.resx">
      <DependentUpon>Cx_Zyhz1.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="06.数据查询\01.住院查询\在院患者费用\Cx_Zyhz2.resx">
      <DependentUpon>Cx_Zyhz2.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="06.数据查询\01.住院查询\在院患者费用\Cx_ZyYjMx.resx">
      <DependentUpon>Cx_ZyYjMx.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="06.数据查询\01.住院查询\患者用药清单\Cx_Hzyy1.resx">
      <DependentUpon>Cx_Hzyy1.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="06.数据查询\01.住院查询\患者诊疗项目查询\ZyXmCx.resx">
      <DependentUpon>ZyXmCx.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="06.数据查询\01.住院查询\病案首页导出\BasyExport.resx">
      <DependentUpon>BasyExport.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="06.数据查询\01.住院查询\缴纳押金查询\Cx_Yj.resx">
      <DependentUpon>Cx_Yj.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="06.数据查询\03.药库查询\各药房信息查询\Yk_Cx_AllYf.resx">
      <DependentUpon>Yk_Cx_AllYf.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="06.数据查询\03.药库查询\药库出入库台账\Yp_Cr_Tz.resx">
      <DependentUpon>Yp_Cr_Tz.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="10.健康卡管理\06.公务卡字典维护\Jkk_Gwk.resx">
      <DependentUpon>Jkk_Gwk.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="10.健康卡管理\JkkRyxx.resx">
      <DependentUpon>JkkRyxx.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="10.健康卡管理\04.健康卡交易明细\Jkk_Mx.resx">
      <DependentUpon>Jkk_Mx.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="10.健康卡管理\Jkk_Js.resx">
      <DependentUpon>Jkk_Js.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="10.健康卡管理\03.健康卡交易日结\Jkk_Jz.resx">
      <DependentUpon>Jkk_Jz.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="10.健康卡管理\05.健康卡日结查询\Jkk_JzData.resx">
      <DependentUpon>Jkk_JzData.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="10.健康卡管理\01.办理新卡及充值\Jkk_BkCz.resx">
      <DependentUpon>Jkk_BkCz.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="10.健康卡管理\02.健康卡账户管理\Jkk_Th.resx">
      <DependentUpon>Jkk_Th.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="10.健康卡管理\09.POS机参数设置\POS_Set.resx">
      <DependentUpon>POS_Set.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="88.公用窗体\药库药房出入库录入\YpNetCg_Dr.resx">
      <DependentUpon>YpNetCg_Dr.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="06.数据查询\03.药库查询\药库调价查询\Yk_Tj_Cx1.resx">
      <DependentUpon>Yk_Tj_Cx1.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="06.数据查询\04.药房查询\住院门诊发药综合查询\Cx_ZyMz_Fy.resx">
      <DependentUpon>Cx_ZyMz_Fy.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="06.数据查询\04.药房查询\住院门诊诊疗综合查询\Cx_ZyMz_Xm.resx">
      <DependentUpon>Cx_ZyMz_Xm.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="06.数据查询\04.药房查询\药房药品出入库台账\Yf_Cr_Tz.resx">
      <DependentUpon>Yf_Cr_Tz.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="06.数据查询\05.财务查询\全院工作量统计\Work_Tj.resx">
      <DependentUpon>Work_Tj.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="06.数据查询\05.财务查询\利润统计\Cw_lrtj1.resx">
      <DependentUpon>Cw_lrtj1.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="06.数据查询\05.财务查询\床位统计\Cw_Tj.resx">
      <DependentUpon>Cw_Tj.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="06.数据查询\05.财务查询\科室收入统计\JcKs_Money_Tj.resx">
      <DependentUpon>JcKs_Money_Tj.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="06.数据查询\05.财务查询\科室收入统计\Money_Tj1.resx">
      <DependentUpon>Money_Tj1.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="06.数据查询\05.财务查询\门诊住院日结查询\Cw_Cx_MzZy_Rj1.resx">
      <DependentUpon>Cw_Cx_MzZy_Rj1.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="06.数据查询\05.财务查询\门诊住院日结查询\Cw_Cx_MzZy_Rj2.resx">
      <DependentUpon>Cw_Cx_MzZy_Rj2.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="06.数据查询\05.财务查询\门诊住院日结查询\Cw_Cx_MzZy_Rj3.resx">
      <DependentUpon>Cw_Cx_MzZy_Rj3.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="06.数据查询\项目售价查询\Cm_XmSj.resx">
      <DependentUpon>Cm_XmSj.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="08.物资管理\物资出入库\Wz_Crk.resx">
      <DependentUpon>Wz_Crk.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="08.物资管理\物资字典\Zd_Wz1.resx">
      <DependentUpon>Zd_Wz1.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="08.物资管理\物资字典\Zd_Wz2.resx">
      <DependentUpon>Zd_Wz2.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="08.物资管理\物资字典\Zd_Wz3.resx">
      <DependentUpon>Zd_Wz3.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="08.物资管理\物资库存查询\Wz_KcCx.resx">
      <DependentUpon>Wz_KcCx.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="88.公用窗体\药库药房出入库查询\Cx_YkYf_Crk.resx">
      <DependentUpon>Cx_YkYf_Crk.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="88.公用窗体\药库药房报损报溢\YkYf_Bsby.resx">
      <DependentUpon>YkYf_Bsby.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="88.公用窗体\药库药房数据盘点\YkYf_Pd1.resx">
      <DependentUpon>YkYf_Pd1.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="88.公用窗体\药库药房数据盘点\YkYf_Pd2.resx">
      <DependentUpon>YkYf_Pd2.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="88.公用窗体\药库药房盘点查询\Cx_YkYf_Pd.resx">
      <DependentUpon>Cx_YkYf_Pd.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="88.公用窗体\药库药房警戒线查询\Cx_YkYf_Alar.resx">
      <DependentUpon>Cx_YkYf_Alar.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="98.提醒\01.药库提醒\Msg_YkYxq.resx">
      <DependentUpon>Msg_YkYxq.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="98.提醒\02.药房提醒\Msg_YfYxq.resx">
      <DependentUpon>Msg_YfYxq.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="99.系统设置\01.权限组\Zd_Qx1.resx">
      <DependentUpon>Zd_Qx1.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="99.系统设置\01.权限组\Zd_Qx2.resx">
      <DependentUpon>Zd_Qx2.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="99.系统设置\02.操作员管理\Zd_Czy1.resx">
      <DependentUpon>Zd_Czy1.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="99.系统设置\02.操作员管理\Zd_Czy2.resx">
      <DependentUpon>Zd_Czy2.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="99.系统设置\03.个人设置\Person_Config.resx">
      <DependentUpon>Person_Config.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="99.系统设置\05.数据库维护\DataBackUp.resx">
      <DependentUpon>DataBackUp.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="99.系统设置\Connect_Edit.resx">
      <DependentUpon>Connect_Edit.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="99.系统设置\Zd_Cssz1.resx">
      <DependentUpon>Zd_Cssz1.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="My Project\licenses.licx" />
    <EmbeddedResource Include="My Project\Resources.resx">
      <Generator>VbMyResourcesResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.vb</LastGenOutput>
      <CustomToolNamespace>My.Resources</CustomToolNamespace>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="88.公用窗体\基础数据\Zd_Dict1.resx">
      <DependentUpon>Zd_Dict1.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="88.公用窗体\基础数据\Zd_Dict2.resx">
      <DependentUpon>Zd_Dict2.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="88.公用窗体\精简选择\Zd_JjSave.resx">
      <DependentUpon>Zd_JjSave.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="88.公用窗体\药库药房出入库录入\YkYf_Ck3.resx">
      <DependentUpon>YkYf_Ck3.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="88.公用窗体\药库药房出入库录入\YkYf_Crk1.resx">
      <DependentUpon>YkYf_Crk1.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="88.公用窗体\药库药房出入库录入\YkYf_Crk2.resx">
      <DependentUpon>YkYf_Crk2.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="88.公用窗体\药库药房出入库录入\YkYf_Rk3.resx">
      <DependentUpon>YkYf_Rk3.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="88.公用窗体\药库药房出入库录入\YkYf_TkPf3.resx">
      <DependentUpon>YkYf_TkPf3.vb</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <Content Include="IDReadDevDLL\Synjones\ID_Fpr.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="IDReadDevDLL\Synjones\ID_FprCap.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="IDReadDevDLL\Synjones\sdtapi.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="IDReadDevDLL\Synjones\SynIDCardAPI.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="IDReadDevDLL\Synjones\USBRead.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="IDReadDevDLL\Synjones\WltRS.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="msvcr71.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="OSQL.EXE">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\190712.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\190718.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\200118.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\200603.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\200605.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\200617.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\201207.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\201208.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\201224.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\210103.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\210112.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\210422.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\210425.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\210426.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\210428.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\210429.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\210430.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\210501.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\210506.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\210507.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\210513.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\210514.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\210515.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\210516.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\dcrf32idcardimagebuild.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\dcsdtapi.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\dc_pboc.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\HealthyCarder.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\HS_Reader.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\JPG.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\wltrs_getbmp.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTReadCard.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\Common\log4net.config">
      <Link>log4net.config</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <None Include="app.config">
      <SubType>Designer</SubType>
    </None>
    <Content Include="data\CodeConfig.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="img\Icon_1469.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="img\insert.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="img\关闭审阅.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="img\关闭痕迹.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="img\开启审阅.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="img\开启痕迹.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="img\打印.bmp">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\Card.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\dcic32.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\dc_nmg_sse.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\Ds_SSSE32.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\Ds_tsSICARD.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\Ds_WinSocket.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\eapagent.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\enhisif.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\gwiIcCard.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\lzkc_dll.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\MT_NMSSSE32.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\scardlog20190515.txt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\SiCard.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\SiInterface.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\TY_NMSE32.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\VerifyLicAuth.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\VerifyLicAuthLayer.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\YD570S_DLL.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Resources\insert.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\appfc.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\CharacterConverse.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\CHNL32.DLL">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\CimP.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\CTDdkUpdateRecords.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\CTDdsUpdateRecords.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\CTDdxUpdateRecords.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\des64.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\dkSICARD.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\dx_ssse32.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\EasySoap.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\EP1KDL20.DLL">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\ep1kutil.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\epSICARD.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\ExPat.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\FindItem.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\ftpexe.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\FT_ND_API.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\getcpuid.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\GetMac.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\GoldDes.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\HBTS_SSSE32.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\hiscomminterface.h">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\Hnic32.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\jkp512.h">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\libeay32.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\libjcc.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\MCS_SR.DLL">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\MessageBox.DLL">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\MsgBox.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\msvcp60.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\MTSClient.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\MyZip.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\nwinface.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\PAD03.DLL">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\PBDOM90.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\pbdwe60.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\pbdwe90.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\pbo8460.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\pbo8490.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\pbodb60.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\pbodb90.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\pbsoapclient90.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\pbvm60.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\pbvm90.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\pbwsdlclient90.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\PBXerces90.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\PrintCtrl.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\psetpage.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\pwdkb.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\Reg.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\Rockey3.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\sscard.h">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\ssleay32.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\tsSICARD.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\UsbDiskApi.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\USER32.DLL">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\WinSocket.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\wjjdblib.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\wod50t.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\xerces-c_2_1_0.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\xerces-c_2_4_0.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\YBICDLL.DLL">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\CameraDll.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\CharacterConverse.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\CTDdkUpdateRecords.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\CTDdsUpdateRecords.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\CTDdxUpdateRecords.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\dcic32.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\dksicard.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\dx_ssse32.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\eapagent.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\epSICARD.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\HBKSDri.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\HBTS_F41.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\HBTS_MDS.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\HBTS_SSSE32.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\hdSICARD.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\HDSSKeyBord.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\HDSSSE32.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\HDSSSE32_TS.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\hiscomminterface.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\Hnic32.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\libjcc.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\MessageBox.DLL">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\MFC42D.DLL">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\MOUSEHOOK.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\MSSOAP30.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\MSVCP60D.DLL">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\MSVCRTD.DLL">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\msxml4.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\Mwic_32.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\mwReader.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\mwSICARD.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\MyZip.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\nwinface.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\PrintCtrl.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\pwdkb.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\sscard.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\SSCardDriver.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\SSCardDriver_DC.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\SSCardDriver_EP.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\SSCardDriver_HD.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\SSCardDriver_MW.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\SSCardDriver_TS.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\SSKeyBDriver.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\SSKeyBDriver_DC.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\SSKeyBDriver_EP.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\SSKeyBDriver_HD.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\SSKeyBDriver_KTL.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\SSKeyBDriver_KTLU.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\sskeybdriver_mw.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\SSKeyBDriver_TS.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\SSSE32.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\SSSE32_C.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\SSSE32_MDS.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\SSSE32_U.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\SSSE32_U30.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\TECSUN.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\tsSICARD.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\TSW-903KU-2.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\TSW-906HU.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\USER32.DLL">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\WinSocket.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\WltRS.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\xerces-c_2_1_0.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\xerces-c_2_4_0.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\171227.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\171228.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\180201.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\180202.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\180203.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\180204.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\180419.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\180824.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\180825.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="更新脚本\180826.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="背景.jpg">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Conf\CodeConfig.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="DDTek.lic">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="KMDLL\borlndmm.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="KMDLL\KMOption.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Conf\Conf.dat">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Conf\Conn.dat">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="EmrMb.ztemr">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="BasyExport.dbf">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="BasyExport_N41.dbf">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <None Include="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
    <None Include="My Project\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <CustomToolNamespace>My</CustomToolNamespace>
      <SubType>Designer</SubType>
      <LastGenOutput>Settings1.Designer.vb</LastGenOutput>
    </None>
    <None Include="MyKey.pfx" />
    <Content Include="HuaDaJkkDLL\BankNo.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="HuaDaJkkDLL\HD300_V1.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="HuaDaJkkDLL\SSSE32.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="HuaDaJkkDLL\WSB_RWInterface.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="HuaDaJkkDLL\WSHealthyTCarder.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="KMDLL\KMAPI.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\Card.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\eapagent.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\eapagent.log">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\License.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\MTConfig.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\Reserve.log">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\SiCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\SiInterface.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\0503License.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\0505License.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <None Include="NMG_YB_DLL\1089License.ini" />
    <None Include="NMG_YB_DLL\124004License.ini" />
    <None Include="NMG_YB_DLL\181001License.ini" />
    <None Include="NMG_YB_DLL\181002License.ini" />
    <Content Include="NMG_YB_DLL\2121License.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2101License.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2002License.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2023License.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2206License.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2301license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2308License.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2010License.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2605License.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2607License.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2610License.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2615License.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2310Licence.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2234License.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2508License.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2317License.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2037license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2005license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2007license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2008license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2009license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2011license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2012license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2013license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2014license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2015license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2016license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2017license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2018license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2019license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2020license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2021license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2022license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2024license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2025license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2027license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2028license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2029license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2031license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2032license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2036license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2102license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2103license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2105license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2106license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2107license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2108license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2109license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2110license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2111license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2112license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2113license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2114license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2115license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2116license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2117license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2118license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2119license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2120license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2122license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2123license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2124license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2125license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2126license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2127license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2128license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2129license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2130license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2203license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2204license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2205license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2207license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2208license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2209license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2210license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2211license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2212license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2213license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2214license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2215license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2216license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2217license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2218license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2219license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2221license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2222license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2223license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2224license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2225license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2226license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2227license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2228license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2229license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2230license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2231license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2232license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2233license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2602license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2603license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2604license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2606license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2608license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2609license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2611license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2612license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2613license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2614license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2616license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2617license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2618license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2619license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2620license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2621license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2622license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2623license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2624license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2625license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2626license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2627license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2628license.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <None Include="Resources\close_16px_1170413_easyicon.net.png" />
    <None Include="Resources\close_hide_16px_10915_easyicon.net.png" />
    <None Include="Resources\window_close_16px_509835_easyicon.net.png" />
    <None Include="Resources\帮助32.png" />
    <None Include="Resources\背景色32.png" />
    <None Include="Resources\个人设置32.png" />
    <None Include="Resources\说明32.png" />
    <None Include="Resources\退出32.png" />
    <None Include="Resources\Icon_1469.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <Content Include="Rpt\新农合门诊统筹补偿单（丰南）.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\住院押金缴费单%28丰南%29.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\卫生室采购计划汇总.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\卫生室采购计划详单.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\唐山门诊发票%28汇总%29.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\患者费用统计表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药房发药表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药品销售统计表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\输液卡.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\输液卡（连打）.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="TemperatureChart.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="TongLianPosDLL\ALLINPAY\ALLINPAY\ALLINPAY.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="TongLianPosDLL\ALLINPAY\ALLINPAY\allinpaymis.exe">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="TongLianPosDLL\ALLINPAY\ALLINPAY\ALLINPAYTEST.exe">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="TongLianPosDLL\ALLINPAY\ALLINPAY\Card.exe">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="TongLianPosDLL\ALLINPAY\ALLINPAY_TRAY\ALLINPAY_TRAY.exe">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="TongLianPosDLL\ALLINPAY\BankCard\BankCard.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="TongLianPosDLL\ALLINPAY\BankCard\settleflag.txt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="TongLianPosDLL\ALLINPAY\BankCard\Trans.txt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="TongLianPosDLL\ALLINPAY\WanShang\settleflag.txt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="TongLianPosDLL\ALLINPAY\WanShang\Trans.txt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="TongLianPosDLL\ALLINPAY\WanShang\WanShang.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="TongLianPosDLL\system32\comdlg32.ocx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="TongLianPosDLL\system32\dhRichClient3.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="TongLianPosDLL\system32\MSCOMM32.OCX">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="TongLianPosDLL\system32\msstdfmt.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="TongLianPosDLL\system32\MSWINSCK.OCX">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="TongLianPosDLL\system32\scrrun.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="TongLianPosDLL\system32\SkinH_VB6.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="TongLianPosDLL\system32\sqlite36_engine.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="TongLianPosDLL\system32\TABCTL32.OCX">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="TongLianPosDLL\system32\VsFlex8.ocx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="TongLianPosDLL\system32\XLDES.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="TongLianPosDLL\安装步骤.txt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\eapagent.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\neuqhd.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\医技科室收入统计.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\催款单.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药房台账汇总.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药房台账统计.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药库台账汇总.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药库台账统计.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\门诊统筹月结 %28一般诊疗%29.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\新农合住院补偿单.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\新农合出院即报明细.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\新农合出院即报月结单.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\诊疗卡.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\门诊处方表%28二分之一A4%29.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\住院处方表%28二分之一A4%29.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药房发药表（带明细）.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药库药房出入库查询清单简化.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药库药房出入库查询药品汇总.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药库药房出入库查询票据汇总.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药库药房出入库查询客户汇总.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药库药房退供应商查询客户汇总.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药库药房退供应商查询清单简化.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药库药房退供应商查询票据汇总.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药库药房退供应商查询药品汇总.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药库药房报损报益表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药库药房盘点表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药库药房盈亏表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\辽宁省住院收费票据.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\辽宁省住院收费票据老版.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\辽宁省门诊收费票据.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\辽宁省门诊收费票据老版.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\患者用药清单热敏打印.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\新农合门诊统筹补偿单（葫芦岛）.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\打印农合本.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\新农合门诊统筹补偿单（玉田）.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\新农合门诊统筹补偿单（迁西）.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="TongLianPosDLL\ALLINPAY\ALLINPAY\Card.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="TongLianPosDLL\ALLINPAY\ALLINPAY\CODE.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="TongLianPosDLL\ALLINPAY\ALLINPAY\Function.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="TongLianPosDLL\ALLINPAY\ALLINPAY\System.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="TongLianPosDLL\ALLINPAY\ALLINPAY_TRAY\Function.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="TongLianPosDLL\ALLINPAY\ALLINPAY_TRAY\System.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="TongLianPosDLL\ALLINPAY\BankCard\bankcard_db">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="TongLianPosDLL\ALLINPAY\BankCard\Config.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="TongLianPosDLL\ALLINPAY\WanShang\Config.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="TongLianPosDLL\ALLINPAY\WanShang\Sale.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="TongLianPosDLL\ALLINPAY\WanShang\wanshang_db">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\健康卡结账.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\门诊统筹月结（丰南个人账户）.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\门诊统筹月结明细（卫生室丰南个人账户）.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\诊疗字典.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="TongLianPosDLL\allinpay_reg.bat">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="TongLianPosDLL\allinpay_reg_win8_64.bat">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="TongLianPosDLL\system32\IOBJSAFE.TLB">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="TongLianPosDLL\system32\skinh.she">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\健康卡交易单.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\健康卡交易明细表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\健康卡充值打印.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\患者用药清单热敏打印75mm.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药房库存表（带采购价）.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\输液卡（丰南连打）.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\新农合门诊统筹补偿单（灯塔）.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\新农合门诊统筹补偿单（灯塔民政）.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\住院日结横打.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\2013年河北省医保住院收费票据.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\2013年河北省医保门诊收费票据.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药房门诊发药汇总表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\物资盘点表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\物资移库表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\物资出库单.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\物资采购入库单.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\医保日结.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\物资退库表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\dkSICARD.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\epSICARD.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\hiscomminterface.lib">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\jkp512.lib">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\sscard.lib">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\tsSICARD.INI">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\物资支领表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\物资其他入库单.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\2013年河北省医保住院收费票据居民.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\2013年河北省医保住院收费票据职工.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\2013年河北省医保门诊收费票据居民.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\2013年河北省医保门诊收费票据职工.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\cfSSCardDriver.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\cfSSKeyBDriver.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\config.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\dkSICARD.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\egapp.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\epSICARD.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\HDKeyBord.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\TECSUN.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保异地就医Dll\tsSICARD.INI">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\门诊日结new.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\门诊处方表%28二分之一A4%29new.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\输液卡（长春连打）.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\套打处方%28长春%29.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\医保处方%28长春%29.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\检验报告单.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\门诊处方表%28二分之一A4%29new%28竖版%29.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药房项目表%28带明细%29.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药房项目表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\内蒙古通辽市医院住院收费专用票.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\内蒙古通辽市门诊收费专用票据.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\通辽市医疗保险住院费用结算单.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\通辽市医疗保险门诊费用结算单.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\通辽市医疗机构住院专用收据.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\内蒙古医保对账.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\内蒙古通辽市霍林河门诊收费专用票据.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\子报表母表new.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\通辽市医疗保险住院费用结算单%28居民%29.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\通辽市医疗保险住院费用结算单%28职工%29.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\1001ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2001ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2002ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2003ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2004ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2601ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2121ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2101ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2110ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2006License.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="NMG_YB_DLL\2040License.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2130ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2104ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2109ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2115ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2124ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2126ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2012ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2024ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2307ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2310ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2315ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2316ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2317ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2318ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2402ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2404ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2409ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2415ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2416ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\0503ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2010ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2018ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2023ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2030ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2037ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2013ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2025ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2026ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2305ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2304ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2321ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\门诊日结操作员现金统计new.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2628ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2036ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2602ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\合力康医院发票.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\医保对账单.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\医保本地数据对账明细.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2403ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\居民医保对账单.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\左中门诊发票样式.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="通辽市民卡Dll\ZTreadCard\2040ZTReadCard.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\内蒙古通辽市门诊收费专用票据%28不套打%29.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\内蒙古通辽市医院住院收费专用票2.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\通辽市医疗保险住院费用结算单%28居民普通%29.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\通辽市医疗保险住院费用结算单%28职工普通%29.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\通辽市医疗保险门诊费用结算单%28普通%29.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\中医病案首页.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\西医病案首页.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Content Include="44副本.ico" />
    <Content Include="img\09553.ico">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="img\09574.ico">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="IDReadDevDLL\PuTian\License.dat">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="IDReadDevDLL\PuTian\cardapi3.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="IDReadDevDLL\PuTian\sdtapi.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="IDReadDevDLL\PuTian\WltRS.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="sound\msg.wav">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="sound\notify.wav">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <None Include="My Project\app.manifest" />
    <Content Include="System.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药库库存表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药房库存表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\科室收入统计表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\住院床位统计.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药库调拨药房表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药库入库单.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药品科室支领表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药库批发表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药库退供应商表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药房退药库表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药房科室支领表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药房日结.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\在院患者查询.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药房批发表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药房入库单.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\住院押金缴费单.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\盘点手抄单.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\患者用药清单标准版.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\患者用药清单简化版.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药库调价查询.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药品警戒线查询.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\医院工作量统计.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\门诊患者用药、诊疗详单.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\门诊电子病历.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\门诊日结.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\住院日结.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\住院日结汇总母表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\患者用药清单标准版二.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\患者用药清单简化版二.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\住院日结患者押金.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\住院日结操作员现金统计.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\子报表母表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\门诊日结操作员现金统计.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\邯郸曲周门诊票据.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\护士站汇总领药.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\唐山门诊发票.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\患者类别统计表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药房住院发药汇总.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\内蒙古通辽住院票据.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\内蒙古通辽门诊票据.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\吉林省医疗机构住院收费专用票据.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\吉林省医疗机构门诊收费专用票据.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\收入汇总表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\医院费用统计.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\支出汇总表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\出院明细表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\物资出入库信息查询.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\物资库存信息查询.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\药品入库情况汇总表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\门诊统筹日结.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\门诊统筹日结明细.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\门诊统筹月结.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Rpt\门诊统筹补偿明细.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\门诊统筹月结明细（卫生室）.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\新农合门诊统筹补偿单.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\科室日报表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\入院患者统计.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\门诊挂号票据.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\住院处方表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\门诊处方表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\2013年河北省住院收费票据.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\2013年河北省门诊收费票据.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\一般诊疗费用报表.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Rpt\新型合作医疗门诊统筹补偿单（丰润%29.mrt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="XzxDkq116D\License.dat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="XzxDkq116D\sdtapi.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="XzxDkq116D\SynIDCardAPI.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="XzxDkq116D\WltRS.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="zh-CHS.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\dcic32.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\eapagent.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\hdcrw.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\hiscomminterface.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\HSMApi.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\ICCARD.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\jkp512.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\LSCard.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\mwic_32.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\sscard.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\SSSE32.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\TYICAPI.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\TYICRD.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="东软医保Dll\TYReader.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="唐山His2010帮助文档.chm">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="sound\Windows Error.wav">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Yy_Db.mdb">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="更新说明.txt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\1.html">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\css\C1WebGrid.css">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\css\css.css">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\css\Page.css">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\1.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\1.swf">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\11.gif">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\1_login.jpg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\2_login.jpg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\80h.gif">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\arrow_down.gif">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\arrow_down_o.gif">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\arrow_up.gif">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\arrow_up_o.gif">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\b1.jpg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\b2.jpg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\b3.jpg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\b4.jpg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\b5.jpg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\bg.gif">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\bg.jpg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\body_bg.jpg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\c.jpg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\c.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\c1.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\chart_bg.jpg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\close.gif">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\c_bg.gif">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\end1.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\gray_footer_bg_slice.gif">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\hh80.gif">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\icon1.gif">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\isleft.gif">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\js.gif">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\khgl-bg.jpg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\khgl-btjb.jpg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\l.jpg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\l1.jpg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\l1.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\last.gif">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\lc_1.gif">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\lc_2.gif">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\lc_3.gif">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\lc_4.gif">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\lc_end.gif">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\lc_left.gif">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\lc_right.gif">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\lc_top.gif">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\left_dh_bg.jpg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\list_bg.jpg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\list_bg_1.jpg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\list_bg_2.jpg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\list_bg_3.jpg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\login.jpg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\next.gif">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\orange_header_bg_slice.gif">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\pic.jpg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\pic1.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\pngfix.js">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\qx.jpg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\r.jpg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\r1.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\softlogo.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\sr_bg.jpg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\title.jpg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\top1.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\toplogo2.jpg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\topnew_b1.jpg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\topnew_bg_1.jpg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\top_bg.jpg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\top_bg1.jpg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\images\top_bg2.jpg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Zy1\index.htm">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="电子病历模版.mdb">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <PublishFile Include="ActiveReports.Chart">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="ActiveReports.Document">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="ActiveReports.Viewer6">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="ActiveReports6">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="data\CodeConfig.xml">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="NPOI">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Stimulsoft.Base">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Stimulsoft.Controls">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Stimulsoft.Controls.Win">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Stimulsoft.Design">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Stimulsoft.Editor">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Stimulsoft.Report">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Stimulsoft.Report.Check">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Stimulsoft.Report.Design">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Stimulsoft.Report.Helper">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Stimulsoft.Report.Win">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Yy_Db.mdb">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="zh-CHS.xml">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
    <PublishFile Include="电子病历模版.mdb">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>File</FileType>
    </PublishFile>
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include=".NETFramework,Version=v4.0">
      <Visible>False</Visible>
      <ProductName>Microsoft .NET Framework 4 %28x86 和 x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Client.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1 Client Profile</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.2.0">
      <Visible>False</Visible>
      <ProductName>.NET Framework 2.0 %28x86%29</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.0">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.0 %28x86%29</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Windows.Installer.4.5">
      <Visible>False</Visible>
      <ProductName>Windows Installer 4.5</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\BaseClass\BaseClass.vbproj">
      <Project>{48964D0C-25C0-413C-A94A-C2246ADE46A1}</Project>
      <Name>BaseClass</Name>
    </ProjectReference>
    <ProjectReference Include="..\BaseFunc\BaseFunc.vbproj">
      <Project>{C7865854-F754-41AC-9912-829068BE5C2A}</Project>
      <Name>BaseFunc</Name>
    </ProjectReference>
    <ProjectReference Include="..\BLLOld\BLLOld.csproj">
      <Project>{42c23254-ba1f-4222-895b-276fc06bf59c}</Project>
      <Name>BLLOld</Name>
    </ProjectReference>
    <ProjectReference Include="..\BLL\BLL.csproj">
      <Project>{46b795c2-6efa-41e6-948e-66f92e591b6a}</Project>
      <Name>BLL</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common.BaseForm\Common.BaseForm.csproj">
      <Project>{1dd7020c-8603-438a-8015-34702dabc229}</Project>
      <Name>Common.BaseForm</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common.Delegate\Common.Delegate.csproj">
      <Project>{943ed6dc-c1fb-42fa-b543-9afaa67ba7c3}</Project>
      <Name>Common.Delegate</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common.WinFormVar\Common.WinFormVar.csproj">
      <Project>{e267bdd2-634a-405b-bdbf-55354adbc027}</Project>
      <Name>Common.WinFormVar</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common\Common.csproj">
      <Project>{92e350a0-3691-4b8d-a07e-ebb0f10e6997}</Project>
      <Name>Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\CustomControl\CustomControl.csproj">
      <Project>{12BF4168-D60E-4A6C-85BF-926130EE6A6D}</Project>
      <Name>CustomControl</Name>
    </ProjectReference>
    <ProjectReference Include="..\DAL\DAL.csproj">
      <Project>{C0DAB999-F761-4901-BE5B-C542365756A6}</Project>
      <Name>DAL</Name>
    </ProjectReference>
    <ProjectReference Include="..\DbProviderFactory\DbProviderFactory.csproj">
      <Project>{fdf5d6d6-d281-4884-a81a-d0c49c2f3bc7}</Project>
      <Name>DbProviderFactory</Name>
    </ProjectReference>
    <ProjectReference Include="..\FastDBF\SocialExplorer.FastDBF.csproj">
      <Project>{9CF5ED11-6D2B-4FEF-8A0A-B2E12DE59867}</Project>
      <Name>SocialExplorer.FastDBF</Name>
    </ProjectReference>
    <ProjectReference Include="..\HisControl\HisControl.vbproj">
      <Project>{EF778791-6E30-4A07-83B9-2D73FD08810A}</Project>
      <Name>HisControl</Name>
    </ProjectReference>
    <ProjectReference Include="..\HisPara\HisPara.vbproj">
      <Project>{3E790840-B7EB-4875-A334-D0386A5633CB}</Project>
      <Name>HisPara</Name>
    </ProjectReference>
    <ProjectReference Include="..\HisVar\HisVar.vbproj">
      <Project>{4DAD5E14-6224-46B2-886D-6D22CE3886BC}</Project>
      <Name>HisVar</Name>
    </ProjectReference>
    <ProjectReference Include="..\IDBUtility\IDBUtility.csproj">
      <Project>{ccbb5cb6-0871-4d97-9eb3-a68b44cb7e86}</Project>
      <Name>IDBUtility</Name>
    </ProjectReference>
    <ProjectReference Include="..\Jkk\Jkk.csproj">
      <Project>{ADEC5669-F0C3-435D-937D-D2CB079EE994}</Project>
      <Name>Jkk</Name>
    </ProjectReference>
    <ProjectReference Include="..\ModelOld\ModelOld.csproj">
      <Project>{1FAF80FE-D42E-4FEB-853A-B9846C1862AE}</Project>
      <Name>ModelOld</Name>
    </ProjectReference>
    <ProjectReference Include="..\Model\Model.csproj">
      <Project>{3fb6ea13-2c32-4d08-a426-c22224f72121}</Project>
      <Name>Model</Name>
    </ProjectReference>
    <ProjectReference Include="..\PublicForm\PublicForm.vbproj">
      <Project>{C2D40F84-CBD2-4BB9-92CF-C995FC2F4A25}</Project>
      <Name>PublicForm</Name>
    </ProjectReference>
    <ProjectReference Include="..\Resources\MyResources.vbproj">
      <Project>{52FE1A23-CE20-42DA-8AFD-1B47B2BBCCC7}</Project>
      <Name>MyResources</Name>
    </ProjectReference>
    <ProjectReference Include="..\SQLServerDAL\SQLServerDAL.csproj">
      <Project>{ac6eb101-399f-43a9-93cd-e4cac537fc45}</Project>
      <Name>SQLServerDAL</Name>
    </ProjectReference>
    <ProjectReference Include="..\Utilities\Utilities.csproj">
      <Project>{ddef90d7-bcf0-4e30-9fce-3bb2d493565d}</Project>
      <Name>Utilities</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZtHis.Emr\ZtHis.Emr.vbproj">
      <Project>{D05310B8-9C6A-4961-8664-8133AE171F15}</Project>
      <Name>ZtHis.Emr</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZtHis.Materials\ZtHis.Materials.vbproj">
      <Project>{DB1F7C8F-F366-440A-BE79-905B41075418}</Project>
      <Name>ZtHis.Materials</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisBaseDict\ZTHisBaseDict.csproj">
      <Project>{1ae32808-6380-43c1-ac61-a89605f1229e}</Project>
      <Name>ZTHisBaseDict</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisDrugStore\ZTHisDrugStore.csproj">
      <Project>{772b7b3c-3fc6-4bcd-828d-ec38ac2e14dc}</Project>
      <Name>ZTHisDrugStore</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisEnum\ZTHisEnum.csproj">
      <Project>{940cdbcc-e9a4-4771-be47-343404a40123}</Project>
      <Name>ZTHisEnum</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisInsurance_NMG\ZTHisInsurance_NMG.csproj">
      <Project>{ac2c7dea-07a2-4b67-958a-65a227002e7c}</Project>
      <Name>ZTHisInsurance_NMG</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisLis\ZTHisLis.csproj">
      <Project>{e800477a-4ffe-4308-a238-44b853fb32fb}</Project>
      <Name>ZTHisLis</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisMaterials\ZTHisMaterials.csproj">
      <Project>{dfdcc0a6-19ca-4927-ad3d-21b371c413dd}</Project>
      <Name>ZTHisMaterials</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisMedicalInsurance\ZTHisMedicalInsurance.csproj">
      <Project>{fc379e5f-930e-4397-b544-8cc37bbfadee}</Project>
      <Name>ZTHisMedicalInsurance</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisOutpatient\ZTHisOutpatient.csproj">
      <Project>{0b858e15-ed2e-45d4-96c2-8a2ff0364fc5}</Project>
      <Name>ZTHisOutpatient</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisPara\ZTHisPara.csproj">
      <Project>{9ca37597-119c-4f39-8063-effc91c2b20d}</Project>
      <Name>ZTHisPara</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisPharmacy\ZTHisPharmacy.csproj">
      <Project>{62d0ec07-80e2-42f8-ac5c-d64b2e37985a}</Project>
      <Name>ZTHisPharmacy</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisPublicFunction\ZTHisPublicFunction.csproj">
      <Project>{484f5b0c-f19f-448d-b819-e183bfc2fd96}</Project>
      <Name>ZTHisPublicFunction</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisResources\ZTHisResources.csproj">
      <Project>{e7e57f38-534a-4aea-841e-9a869e3738ff}</Project>
      <Name>ZTHisResources</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisSysManage\ZTHisSysManage.csproj">
      <Project>{54091883-6bed-4cb5-95d5-2c77d11ebf45}</Project>
      <Name>ZTHisSysManage</Name>
    </ProjectReference>
    <ProjectReference Include="..\ZTHisVar\ZTHisVar.csproj">
      <Project>{26bf0cc0-ae2a-459a-b41b-73f691f5299d}</Project>
      <Name>ZTHisVar</Name>
    </ProjectReference>
    <ProjectReference Include="..\健康档案\健康档案.csproj">
      <Project>{dcec5171-7921-4b3b-b4da-d00bcdc7e50f}</Project>
      <Name>健康档案</Name>
    </ProjectReference>
    <ProjectReference Include="..\医保接口\医保接口.vbproj">
      <Project>{E0C58F16-84F4-44B4-8035-7C7C7D6F2D7A}</Project>
      <Name>医保接口</Name>
    </ProjectReference>
    <ProjectReference Include="..\医改办接口\医改办接口.vbproj">
      <Project>{81945458-E467-4318-8FE7-0440A764E102}</Project>
      <Name>医改办接口</Name>
    </ProjectReference>
    <ProjectReference Include="..\护士站\护士站.vbproj">
      <Project>{392EA0BC-9A6E-4FE5-B87E-591EEE67BF2F}</Project>
      <Name>护士站</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <COMReference Include="AxNsoOfficeLib">
      <Guid>{B21A69CE-901E-42FF-8F96-39616D2F16D1}</Guid>
      <VersionMajor>1</VersionMajor>
      <VersionMinor>0</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>aximp</WrapperTool>
      <Isolated>False</Isolated>
    </COMReference>
    <COMReference Include="NsoOfficeLib">
      <Guid>{B21A69CE-901E-42FF-8F96-39616D2F16D1}</Guid>
      <VersionMajor>1</VersionMajor>
      <VersionMinor>0</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>tlbimp</WrapperTool>
      <Isolated>False</Isolated>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </COMReference>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="保险接口\吉林新农合\Dll\" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.VisualBasic.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>