﻿/**  版本信息模板在安装目录下，可自行修改。
* M_Emr_ZhiKong2.cs
*
* 功 能： N/A
* 类 名： M_Emr_ZhiKong2
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/8/20 12:18:11   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace ModelOld
{
	/// <summary>
	/// M_Emr_ZhiKong2:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class M_Emr_ZhiKong2
	{
		public M_Emr_ZhiKong2()
		{}
		#region Model
		private int _id;
		private string _zk_code;
		private string _mx_name;
		private string _kf_pz;
		private string _kf_pznr;
		private decimal? _zk_kf;
		private string _memo;
		/// <summary>
		/// 
		/// </summary>
		public int id
		{
			set{ _id=value;}
			get{return _id;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Zk_Code
		{
			set{ _zk_code=value;}
			get{return _zk_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Mx_Name
		{
			set{ _mx_name=value;}
			get{return _mx_name;}
		}
		/// <summary>
		/// 1：按等级；2：单项；3：多项
		/// </summary>
		public string Kf_Pz
		{
			set{ _kf_pz=value;}
			get{return _kf_pz;}
		}
		/// <summary>
		/// 扣分标准
		/// </summary>
		public string Kf_PzNr
		{
			set{ _kf_pznr=value;}
			get{return _kf_pznr;}
		}
		/// <summary>
		/// 扣分
		/// </summary>
		public decimal? Zk_Kf
		{
			set{ _zk_kf=value;}
			get{return _zk_kf;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Memo
		{
			set{ _memo=value;}
			get{return _memo;}
		}
		#endregion Model

	}
}

