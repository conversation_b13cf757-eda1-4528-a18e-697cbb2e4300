﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="0" />
    <DataSources isList="true" count="0" />
    <Relations isList="true" count="0" />
    <Report isRef="0" />
    <Variables isList="true" count="20">
      <value>,标题,标题,System.String,,False,False</value>
      <value>,患者姓名,患者姓名,System.String,,False,False</value>
      <value>,医疗证号,医疗证号,System.String,,False,False</value>
      <value>,身份证号,身份证号,System.String,,False,False</value>
      <value>,医疗机构名称,医疗机构名称,System.String,,False,False</value>
      <value>,乡镇名称,乡镇名称,System.String,,False,False</value>
      <value>,村庄名称,村庄名称,System.String,,False,False</value>
      <value>,就诊时间,就诊时间,System.String,,False,False</value>
      <value>,总费用药品费用,总费用药品费用,System.String,,False,False</value>
      <value>,总费用卫生材料,总费用卫生材料,System.String,,False,False</value>
      <value>,总费用合计,总费用合计,System.String,,False,False</value>
      <value>,总费用诊疗费用,总费用诊疗费用,System.String,,False,False</value>
      <value>,账户补偿金额,账户补偿金额,System.String,,False,False</value>
      <value>,家庭账户余额,家庭账户余额,System.String,,False,False</value>
      <value>,自付金额,自付金额,System.String,,False,False</value>
      <value>,报销时间,报销时间,System.String,,False,False</value>
      <value>,大写,大写,System.String,,False,False</value>
      <value>,报销票号,报销票号,System.String,,False,False</value>
      <value>,疾病名称,疾病名称,System.String,,False,False</value>
      <value>,经办人,经办人,System.String,,False,False</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="2" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="1">
        <ReportTitleBand1 Ref="3" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,19,7.5</ClientRectangle>
          <Components isList="true" count="41">
            <Text41 Ref="4" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,19,1</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>黑体,15,Regular,Point,False,134</Font>
              <Guid>931775de4ea047f6b54aaf8f5059cff6</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text41</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{标题}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text41>
            <Text4 Ref="5" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1.5,2.3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10</Font>
              <Guid>a7fe0b8c6a94446d80fbf4aa016ad46b</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text4</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>患者姓名</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text4>
            <Text5 Ref="6" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.3,1.5,2.2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10</Font>
              <Guid>0dd9f9655adf48499e267f66134df1c0</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text5</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{患者姓名}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text5>
            <Text6 Ref="7" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>4.5,1.5,1.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10</Font>
              <Guid>09b06d1694b44b018f861eba3098b0a7</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text6</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>医疗证号</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text6>
            <Text7 Ref="8" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6.4,1.5,2.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10</Font>
              <Guid>1a0c3639f97748ef97786492f3056f5c</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text7</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{医疗证号}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text7>
            <Text8 Ref="9" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13.5,1.5,2.3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10</Font>
              <Guid>8c996b1b7fee4a6483f3b2ad81fba264</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text8</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>身份证号</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text8>
            <Text9 Ref="10" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15.8,1.5,3.2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10</Font>
              <Guid>5164fe962fb64aeea879e5e49c58395f</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text9</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{身份证号}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text9>
            <Text10 Ref="11" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>7.2,1,1.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10</Font>
              <Guid>3af3241bd32b42bf9012e0eaa1e387e2</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text10</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>就诊时间</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text10>
            <Text11 Ref="12" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>8.7,1,3.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10</Font>
              <Guid>80c48013969642b1af3064fa12ac19f9</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text11</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{就诊时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text11>
            <Text13 Ref="13" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,1,7.2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10</Font>
              <Guid>e01092ee8df449778223a4dae8989ed8</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text13</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>报销单位：{医疗机构名称}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text13>
            <Text14 Ref="14" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6.4,2,2.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10</Font>
              <Guid>3fc3a02c95e2452eac612be191a6e7cd</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text14</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>村庄名称</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text14>
            <Text15 Ref="15" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9,2,4.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10</Font>
              <Guid>f83be60d99054b9ca81ab45dd46cd85d</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text15</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{村庄名称}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text15>
            <Text18 Ref="16" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6.4,2.5,4.1,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10</Font>
              <Guid>fb59d065d7da47e7bc70cf593c611f61</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text18</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>诊疗费用</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text18>
            <Text19 Ref="17" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>10.5,2.5,3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10</Font>
              <Guid>06f02bd7a014409bbf505a69edd4dd73</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text19</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>卫生材料</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text19>
            <Text21 Ref="18" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13.5,2.5,5.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10</Font>
              <Guid>18ed4c3a6f364f21a7e64cc26696ba2a</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text21</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>合计</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text21>
            <Text17 Ref="19" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.3,2.5,4.1,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10</Font>
              <Guid>251d5164a87f4edb913395af40a97c15</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text17</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>药品费用</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text17>
            <Text22 Ref="20" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,3,2.3,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10</Font>
              <Guid>ea73e628e0a1438991c98a370eb33985</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text22</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>费用</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text22>
            <Text24 Ref="21" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6.4,3,4.1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10</Font>
              <Guid>b4d53db0e84c4fd881bfe4f493fd90bd</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text24</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{总费用诊疗费用}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text24>
            <Text25 Ref="22" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>10.5,3,3,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10</Font>
              <Guid>1710db4878f94d18b145166240111e5d</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text25</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{总费用卫生材料}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text25>
            <Text27 Ref="23" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13.5,3,5.5,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10</Font>
              <Guid>2871f3f3d30b4ff6834470204225a8de</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text27</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{总费用合计}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text27>
            <Text23 Ref="24" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.3,3,4.1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10</Font>
              <Guid>12b72a313c314c62b8b97013f266b926</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text23</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{总费用药品费用}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text23>
            <Text42 Ref="25" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0.5,5,5.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10</Font>
              <Guid>c14c3236b5764549a4b3938257757ad0</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text42</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>经办人：{经办人}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text42>
            <Text43 Ref="26" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>6.9,5,5.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10</Font>
              <Guid>6ac8585e93f9404eb281dbe57683999c</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text43</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>审核人：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text43>
            <Text44 Ref="27" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>13.2,5,5.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10</Font>
              <Guid>5da021cd8f624ff29ae899deaa898086</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text44</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>领款人:</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text44>
            <Text45 Ref="28" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,4.2,2.3,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10</Font>
              <Guid>861319bf80bf4967bd7d12bfbf67ed51</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text45</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>补偿金额</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text45>
            <Text46 Ref="29" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.3,4.2,16.7,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10</Font>
              <Guid>e996ebbaecbc40019549cbdfa31f5ad8</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text46</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{大写}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text46>
            <Text53 Ref="30" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,2,2.3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10</Font>
              <Guid>121f36d005e94b54a1e6220270a65419</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text53</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>乡镇名称</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text53>
            <Text54 Ref="31" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.3,2,4.1,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10</Font>
              <Guid>91cc584315b4425f8655c4b3a3d8ab88</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text54</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{乡镇名称}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text54>
            <Text20 Ref="32" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13.5,3.6,2.3,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10</Font>
              <Guid>c7d6edbc21514f2090c08300a963b7f5</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text20</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>个人自负金额</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text20>
            <Text26 Ref="33" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15.8,3.6,3.2,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10</Font>
              <Guid>7aa18bd232b94be7a45ca70ac93649ce</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text26</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{自付金额}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text26>
            <Text28 Ref="34" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,3.6,2.3,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10</Font>
              <Guid>9130f1edd0f24a019b3af787cdb2f9ec</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text28</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>账户补偿金额</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text28>
            <Text29 Ref="35" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>2.3,3.6,4.1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10</Font>
              <Guid>01f411790a0a4c5fa60dd69eba1fd672</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text29</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{账户补偿金额}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text29>
            <Text1 Ref="36" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>12.6,1,1.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10</Font>
              <Guid>2c1b04eb86634e028b6e6b1ce5a15f75</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text1</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>报销时间</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text1>
            <Text2 Ref="37" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>14.1,1,4.9,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10</Font>
              <Guid>eeac1a64ce174de79a818ca0343fccd7</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text2</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{报销时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text2>
            <Text3 Ref="38" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,2.5,2.3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10</Font>
              <Guid>134d8eb18c9541e6a956fc5eed8a73c7</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text3</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>项目</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text3>
            <Text16 Ref="39" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>6.4,3.6,4.1,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10</Font>
              <Guid>0d71c7328830425a9102da14d986bcc0</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text16</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>家庭账户余额</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text16>
            <Text31 Ref="40" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>10.5,3.6,3,0.6</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10</Font>
              <Guid>fb70b2736c2e4ea6832236213ca89ae2</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text31</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{家庭账户余额}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text31>
            <Text30 Ref="41" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>9,1.5,1.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10</Font>
              <Guid>04da6977e539498dbc6b388359c352cc</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text30</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>报销票号</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text30>
            <Text32 Ref="42" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>10.5,1.5,3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10</Font>
              <Guid>6c54257601b745c0afd6fb602698ba0d</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text32</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{报销票号}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text32>
            <Text12 Ref="43" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>13.5,2,2.3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10</Font>
              <Guid>6256a4536f02490b802aa1489332f20a</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text12</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>疾病名称</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text12>
            <Text33 Ref="44" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15.8,2,3.2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10</Font>
              <Guid>8bacd19e481241a38af506d5c71fac89</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text33</Name>
              <Page isRef="2" />
              <Parent isRef="3" />
              <Text>{疾病名称}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text33>
          </Components>
          <Conditions isList="true" count="0" />
          <Guid>5b79f1a3ca674d12a171676b5b719ef0</Guid>
          <Name>ReportTitleBand1</Name>
          <Page isRef="2" />
          <Parent isRef="2" />
        </ReportTitleBand1>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>b9ea929964514e9eaa59f8ec7979d9f5</Guid>
      <Margins>1,1,1,1</Margins>
      <Name>Page1</Name>
      <PageHeight>9.9</PageHeight>
      <PageWidth>21</PageWidth>
      <Report isRef="0" />
      <Watermark Ref="45" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page1>
  </Pages>
  <PrinterSettings Ref="46" type="Stimulsoft.Report.Print.StiPrinterSettings" isKey="true" />
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>新农合门诊统筹补偿单</ReportAlias>
  <ReportChanged>5/26/2015 8:55:02 AM</ReportChanged>
  <ReportCreated>10/20/2012 10:19:05 AM</ReportCreated>
  <ReportFile>E:\正在进行时\唐山\正在修改版\his2010\his2010\Rpt\新农合门诊统筹补偿单（迁西）.mrt</ReportFile>
  <ReportGuid>24031a93e8124be28a2416079b1739a5</ReportGuid>
  <ReportName>新农合门诊统筹补偿单</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2011.2.1026</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class Report : Stimulsoft.Report.StiReport
    {
        public Report()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
        #endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>