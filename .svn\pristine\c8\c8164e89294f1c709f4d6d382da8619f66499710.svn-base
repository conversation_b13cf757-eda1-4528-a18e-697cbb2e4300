﻿Imports BaseClass
Imports System.Windows.Forms

Public Class Materials_Buy_Search
    Dim Rk1Bll As New BLLOld.B_Materials_Buy_In1
    Dim Rk1Model As New ModelOld.M_Materials_Buy_In1
    Dim Rk2Bll As New BLLOld.B_Materials_Buy_In2
    Dim Cb_Cm As CurrencyManager
    Dim My_Table As New DataTable
    'Dim m_lb As String
    Public m_Row As DataRow
    Dim m_Rc As C_RowChange
    Dim frmLoad As Boolean
    Public Sub New(ByRef rc As C_RowChange)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        m_Rc = rc
        ' 在 InitializeComponent() 调用之后添加任何初始化。
    End Sub



    Private Sub Materials_Buy_Search_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        frmLoad = False
        Call Form_Init()                        '窗体初始化
        Search_MyButton1_Click(Nothing, Nothing)
        frmLoad = True
    End Sub

    Private Sub Form_Init()
        With MyGrid1
            .Init_Grid()
            .Init_Column("入库编码", "M_Buy_Code", "90", "左", "", False)
            .Init_Column("供应商", "MaterialsSup_Name", "90", "左", "", False)
            .Init_Column("库房", "MaterialsWh_Name", "90", "左", "", False)
            .Init_Column("订货日期", "Order_Date", "90", "左", "yyyy-MM-dd", False)
            .Init_Column("到货日期", "Arrival_Date", "90", "左", "yyyy-MM-dd", False)
            .Init_Column("经手人", "Jsr_Name", "90", "左", "", False)
            .Init_Column("完成状态", "OrdersStatus", "90", "中", "", False)
            .Init_Column("冲销状态", "WriteOffStatus", "90", "中", "", False)
            .Init_Column("冲销编码", "WriteOff_Code", "80", "中", "", False)
            .Splits(0).DisplayColumns("OrdersStatus").FetchStyle = True
            .Splits(0).DisplayColumns("WriteOffStatus").FetchStyle = True
        End With
        MyGrid1.FetchRowStyles = True
        With Search_Date
            .CustomFormat = "yyyy-MM-dd HH:mm"
            .EditFormat = "yyyy-MM-dd"
            .DisplayFormat = "yyyy-MM-dd"
            .Value = Format(Now, "yyyy-MM-dd") & " " & Format(Now, "HH:mm:ss")
        End With
        Dim SupBll As New BLLOld.B_Materials_Sup_Dict
        With MaterialsSup
            .DataView = SupBll.GetList("IsUse='1'").Tables(0).DefaultView
            .Init_Colum("MaterialsSup_Py", "供货商简称", 100, "左")
            .Init_Colum("MaterialsSup_Name", "供货商名称", 210, "左")
            .Init_Colum("MaterialsSup_Code", "编码", 0, "左")
            .Init_Colum("MaterialsSup_Wb", "--", 0, "左")
            .Init_Colum("Contact_Person", "--", 0, "左")
            .Init_Colum("Contact_Phone", "--", 0, "左")
            .Init_Colum("Bank", "--", 0, "左")
            .Init_Colum("BankNo", "--", 0, "左")
            .Init_Colum("MaterialsSup_Add", "--", 0, "左")
            .Init_Colum("MaterialsSup_Memo", "--", 0, "左")
            .Init_Colum("Serial_No", "--", 0, "左")
            .Init_Colum("IsUse", "--", 0, "左")
            .DisplayMember = "MaterialsSup_Name"
            .ValueMember = "MaterialsSup_Code"
            .DroupDownWidth = 510
            .MaxDropDownItems = 15
            .SelectedIndex = -1
            .RowFilterTextNull = ""
            .RowFilterNotTextNull = "MaterialsSup_Py"
        End With

        '库房
        Dim KFBll As New BLLOld.B_Materials_Warehouse_Dict
        With MaterialsWh
            .DataView = KFBll.GetList("IsUse='1' ").Tables(0).DefaultView
            .Init_Colum("MaterialsWh_Py", "简称", 70, "左")
            .Init_Colum("MaterialsWh_Name", "名称", 80, "左")
            .Init_Colum("MaterialsWh_Code", "编码", 0, "左")
            .Init_Colum("MaterialsWh_Wb", "--", 0, "左")
            .Init_Colum("MaterialsWh_Memo", "--", 0, "左")
            .Init_Colum("Serial_No", "--", 0, "左")
            .Init_Colum("IsUse", "--", 0, "左")
            .DisplayMember = "MaterialsWh_Name"
            .ValueMember = "MaterialsWh_Code"
            .DroupDownWidth = 250
            .MaxDropDownItems = 15
            .SelectedIndex = -1
            .RowFilterTextNull = ""
            .RowFilterNotTextNull = "MaterialsWh_Py"
        End With
        '经手人
        Dim JsrBll As New BLLOld.B_Zd_YyJsr
        With Jsr_MyDtComobo1
            .DataView = JsrBll.GetList("").Tables(0).DefaultView
            .Init_Colum("Jsr_Jc", "简称", 0, "左")
            .Init_Colum("Jsr_Name", "名称", 80, "左")
            .Init_Colum("Jsr_Code", "编码", 0, "左")
            .DisplayMember = "Jsr_Name"
            .ValueMember = "Jsr_Code"
            .DroupDownWidth = 250
            .MaxDropDownItems = 15
            .SelectedIndex = -1
            .RowFilterTextNull = ""
            .RowFilterNotTextNull = "Jsr_Jc"
        End With
        With WCZT_MySingleComobo1
            .Additem = "全部"
            .Additem = "录入"
            .Additem = "完成"
            .DroupDownWidth = 75
            .SelectedIndex = 0
            .DisplayColumns(1).Visible = False
        End With
        With CxZt_MySingleComobo1
            .Additem = "全部"
            .Additem = "冲销"
            .Additem = "被冲销"
            .Additem = "空"
            .DroupDownWidth = 75
            .SelectedIndex = 0
            .DisplayColumns(1).Visible = False
        End With
        With Date_MySingleComobo1
            .Additem = "录入日期"
            .Additem = "完成日期"
            .Additem = "订货日期"
            .Additem = "到货日期"
            .DroupDownWidth = 75
            .SelectedIndex = 0
            .DisplayColumns(1).Visible = False
        End With
    End Sub




    Private Sub MyGrid1_RowColChange(ByVal sender As System.Object, ByVal e As C1.Win.C1TrueDBGrid.RowColChangeEventArgs) Handles MyGrid1.RowColChange
        If MyGrid1.RowCount = 0 Then Exit Sub
        If MyGrid1.Row < 0 Then Exit Sub
        If frmLoad = False Then Exit Sub
        m_Row = Cb_Cm.List(MyGrid1.Row).Row
        Rk1Model = Rk1Bll.GetModel(m_Row.Item("M_Buy_Code"))
        m_Rc.ChangeModel(Rk1Model)
    End Sub
    Private Sub C1TrueDBGrid1_FetchCellStyle(ByVal sender As System.Object, ByVal e As C1.Win.C1TrueDBGrid.FetchCellStyleEventArgs) Handles MyGrid1.FetchCellStyle
        If MyGrid1.Columns(e.Col).DataField = "OrdersStatus" Then
            Dim strFlag As String = MyGrid1.Columns("OrdersStatus").CellValue(e.Row).ToString()
            If strFlag = "完成" Then
                e.CellStyle.ForegroundImage = My.Resources.Resources.完成16
            Else
                e.CellStyle.ForegroundImage = My.Resources.Resources.录入16
            End If
            e.CellStyle.ForeGroundPicturePosition = C1.Win.C1TrueDBGrid.ForeGroundPicturePositionEnum.PictureOnly

        ElseIf MyGrid1.Columns(e.Col).DataField = "WriteOffStatus" Then
            Dim strFlag As String = MyGrid1.Columns("WriteOffStatus").CellValue(e.Row).ToString()
            If strFlag = "冲销" Then
                e.CellStyle.ForegroundImage = My.Resources.Resources.冲销16
                e.CellStyle.ForeGroundPicturePosition = C1.Win.C1TrueDBGrid.ForeGroundPicturePositionEnum.PictureOnly
            ElseIf strFlag = "被冲销" Then
                e.CellStyle.ForegroundImage = My.Resources.Resources.被冲销16
                e.CellStyle.ForeGroundPicturePosition = C1.Win.C1TrueDBGrid.ForeGroundPicturePositionEnum.PictureOnly
            Else
                ' e.CellStyle.ForeGroundPicturePosition = C1.Win.C1TrueDBGrid.ForeGroundPicturePositionEnum.TextOnly
            End If


        End If

    End Sub
    Private Sub Search_MyButton1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Search_MyButton1.Click
        Dim str As String = "  1=1"
        If M_Buy_Code.Text & "" <> "" Then
            str = str + " and Materials_Buy_In1.M_Buy_Code='" & M_Buy_Code.Text & "'"
        End If
        If Jsr_MyDtComobo1.SelectedValue <> "" Then
            str = str + " and Materials_Buy_In1.Jsr_Code='" & Jsr_MyDtComobo1.SelectedValue & "'"
        End If
        If WCZT_MySingleComobo1.Text <> "全部" Then
            str = str + " and OrdersStatus='" & WCZT_MySingleComobo1.Text & "'"
        End If
        If CxZt_MySingleComobo1.Text = "全部" Then
            str = str + " and  1=1"
        ElseIf CxZt_MySingleComobo1.Text = "冲销" Then
            str = str + " and WriteOffStatus='冲销'"
        ElseIf CxZt_MySingleComobo1.Text = "被冲销" Then
            str = str + " and WriteOffStatus='被冲销'"
        ElseIf CxZt_MySingleComobo1.Text = "空" Then
            str = str + " and (WriteOffStatus='' or WriteOffStatus is null)"
        End If
        If MaterialsSup.SelectedValue <> "" Then
            str = str + " and Materials_Buy_In1.MaterialsSup_Code='" & MaterialsSup.SelectedValue & "'"
        End If
        If MaterialsWh.SelectedValue <> "" Then
            str = str + " and Materials_Buy_In1.MaterialsWh_Code='" & MaterialsWh.SelectedValue & "'"
        End If
        If Date_MySingleComobo1.Text = "录入日期" Then
            str = str + " and convert(varchar(10),Input_Date,23)='" & Format(Search_Date.Value, "yyyy-MM-dd") & "'"
        ElseIf Date_MySingleComobo1.Text = "完成日期" Then
            str = str + " and convert(varchar(10),Finish_Date,23)='" & Format(Search_Date.Value, "yyyy-MM-dd") & "'"
        ElseIf Date_MySingleComobo1.Text = "订货日期" Then
            str = str + " and convert(varchar(10),Order_Date,23)='" & Format(Search_Date.Value, "yyyy-MM-dd") & "'"
        ElseIf Date_MySingleComobo1.Text = "到货日期" Then
            str = str + " and convert(varchar(10),Arrival_Date,23)='" & Format(Search_Date.Value, "yyyy-MM-dd") & "'"
        End If

        My_Table = Rk1Bll.GetList(str).Tables(0)
        Cb_Cm = CType(BindingContext(My_Table.DataSet, My_Table.TableName), CurrencyManager)
        MyGrid1.DataTable = My_Table
    End Sub

End Class