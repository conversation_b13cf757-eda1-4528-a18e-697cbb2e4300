﻿Imports System.Data.SqlClient

Public Class YpNetCg_Dr
    Dim My_DataSet As New DataSet
    Dim Mx_Table As DataTable
    Dim Zb_Row As DataRow
    Dim P_Yp4_Insert As New SqlCommand
    Public Sub New(ByVal tzbrow As DataRow, ByVal tTable As DataTable)

        ' 此调用是设计器所必需的。
        InitializeComponent()

        Zb_Row = tzbrow
        Mx_Table = tTable
        ' 在 InitializeComponent() 调用之后添加任何初始化。

    End Sub

    Private Sub YpNetCg_Dr_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Try
            Dim My_Grid As New BaseClass.C_Grid(Me.C1TrueDBGrid1)
            With My_Grid
                .Init_Grid()
                .P_Dock(BaseClass.C_Grid.Dock.Fill)
                .Init_Column("配送企业", "配送企业", 180, "左", "")
                .Init_Column("药名名称", "药名名称", 130, "左", "")
                .Init_Column("规格", "规格", 90, "左", "")
                .Init_Column("单位", "包装单位", 50, "中", "")
                .Init_Column("生产企业", "生产企业", 150, "左", "")
                .Init_Column("国药准字", "国药准字", 70, "左", "")
                .Init_Column("批号", "批号", 70, "左", "")
                .Init_Column("有效期", "有效期", 70, "中", "yyyy-MM-dd")
                .Init_Column("配送数量", "配送数量", 60, "右", "0.####")
                .Init_Column("采购价", "采购价", 60, "右", "0.00##")

            End With

            My_DataSet = HisVar.HisVar.YpNetCgservice.GetHISRk_Date(HisPara.PublicConfig.XqCode.ToString.Substring(4, 2) & HisPara.PublicConfig.NhYyCode)

            My_DataSet.Tables(0).DefaultView.RowFilter = "配送企业='" & Zb_Row.Item("Kh_Name") & "'"
            C1TrueDBGrid1.SetDataBinding(My_DataSet.Tables(0), "", True)
            C1TrueDBGrid1.HScrollBar.Style = C1.Win.C1TrueDBGrid.ScrollBarStyleEnum.Automatic

            Call P_Procedure()

        Catch ex As Exception
            MsgBox(ex.Message.ToString, MsgBoxStyle.Information, "提示：")
            Me.Close()
        End Try
    End Sub


    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click
        Try
            If Mx_Table.Rows.Count <> 0 Then
                If MsgBox("导入统采药品前将会清除当前入库单【" & Zb_Row.Item("Rk_Code") & "】所录药品,是否确认？", MsgBoxStyle.DefaultButton1 + MsgBoxStyle.OkCancel, "提示") = MsgBoxResult.Cancel Then Exit Sub
                HisVar.HisVar.Sqldal.ExecuteSql("Delete from Yk_Rk2 Where Yy_Code='" & HisVar.HisVar.WsyCode & "' and Rk_Code='" & Zb_Row.Item("Rk_Code") & "'")
                Mx_Table.Clear()
            End If

            If MsgBox("是否确认导入所选药品？", MsgBoxStyle.DefaultButton1 + MsgBoxStyle.OkCancel, "提示") = MsgBoxResult.Cancel Then Exit Sub

            Dim Cgrow As DataRow
            Dim V_XxCode As String = ""
            For Each Cgrow In My_DataSet.Tables(0).Select("配送企业='" & Zb_Row.Item("Kh_Name") & "'")
                Call P_Conn(True)
                With P_Yp4_Insert
                    .Parameters("@V_Yy_Code").Value = HisVar.HisVar.WsyCode
                    .Parameters("@V_Code").Value = Cgrow.Item("药品编码")
                    .Parameters("@V_Date").Value = Format(Cgrow.Item("有效期"), "yyyy-MM-dd")
                    .Parameters("@V_Cgj").Value = Cgrow.Item("采购价")
                    .ExecuteNonQuery()
                    V_XxCode = .Parameters("@V_Max").Value
                End With
                Call P_Conn(False)

                HisVar.HisVar.Sqldal.ExecuteSql("Insert Into Yk_Rk2(Yy_Code,Rk_Sl,Rk_Dj,Rk_Xsj,Rk_Pfj,Rk_Money,Rk_Memo,Rk_Code,Xx_Code) Values ('" & HisVar.HisVar.WsyCode & "','" & Cgrow.Item("配送数量") & "','" & Cgrow.Item("采购价") & "','" & Cgrow.Item("采购价") & "' ,'" & Cgrow.Item("采购价") & "','" & Cgrow.Item("采购价") * Cgrow.Item("配送数量") & "','','" & Zb_Row.Item("Rk_Code") & "','" & V_XxCode & "')")
                HisVar.HisVar.Sqldal.ExecuteSql("Update Zd_Ml_Yp4 Set Yp_Ph='" & Cgrow.Item("批号") & "',Yp_Scrq='1900-01-01' Where Xx_Code='" & V_XxCode & "' And Yy_Code='" & HisVar.HisVar.WsyCode & "'")

                Dim My_NewRow As DataRow = Mx_Table.NewRow
                With My_NewRow
                    .BeginEdit()
                    .Item("Rk_Code") = Zb_Row.Item("Rk_Code")
                    .Item("Yy_Code") = HisVar.HisVar.WsyCode
                    .Item("Dl_Code") = Mid(Cgrow.Item("药品编码"), 1, 2)
                    .Item("Mx_Code") = Cgrow.Item("药品编码")
                    .Item("Xx_Code") = V_XxCode
                    .Item("Yp_Name") = Cgrow.Item("药名名称")
                    .Item("Mx_Gyzz") = Cgrow.Item("国药准字")                '入库编码
                    .Item("Mx_Gg") = Cgrow.Item("规格")
                    .Item("Mx_Cd") = Cgrow.Item("生产企业")                  '采购单价
                    .Item("Yp_Ph") = Cgrow.Item("批号")                  '采购单价
                    .Item("Yp_Yxq") = Format(Cgrow.Item("有效期"), "yyyy-MM-dd")
                    .Item("Rk_Sl") = Cgrow.Item("配送数量")
                    .Item("Rk_Dj") = Cgrow.Item("采购价")            '采购金额
                    .Item("Rk_Money") = Cgrow.Item("采购价") * Cgrow.Item("配送数量")
                    .Item("Rk_Xsj") = Cgrow.Item("采购价")            '备注
                    .Item("Rk_XsMoney") = Cgrow.Item("采购价") * Cgrow.Item("配送数量")
                    .EndEdit()
                End With
                Mx_Table.Rows.Add(My_NewRow)
            Next
        Catch ex As Exception
            MsgBox(ex.Message.ToString, MsgBoxStyle.Information, "提示")
        End Try
      

    End Sub

    Private Sub P_Procedure()
        With P_Yp4_Insert
            .CommandType = CommandType.StoredProcedure
            .Connection = My_Cn
            .CommandText = "P_Zd_Ml_Yp4"
            With .Parameters
                .Add("@V_Yy_Code", SqlDbType.VarChar, 4)
                .Add("@V_Code", SqlDbType.VarChar, 50)
                .Add("@V_Date", SqlDbType.SmallDateTime)
                .Add("@V_Cgj", SqlDbType.Decimal)
                .Add("@V_Max", SqlDbType.VarChar, 50)
            End With
            .Parameters("@V_Max").Direction = ParameterDirection.Output
        End With
    End Sub


End Class