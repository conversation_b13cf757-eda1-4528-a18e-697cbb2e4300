<?xml version="1.0" encoding="utf-8"?>
<configuration>

  <configSections>
    <sectionGroup name="userSettings" type="System.Configuration.UserSettingsGroup, System, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
      <section name="His2010.My.MySettings" type="System.Configuration.ClientSettingsSection, System, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false"/>
      <section name="HIS.My.MySettings" type="System.Configuration.ClientSettingsSection, System, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false"/>
    </sectionGroup>
  </configSections>
  <connectionStrings/>
  <system.diagnostics>
    <sources>
      <!-- 本节定义 My.Application.Log 的登录配置-->
      <source name="DefaultSource" switchName="DefaultSwitch">
        <listeners>
          <add name="FileLog"/>
          <!-- 取消注释以下一节可写入应用程序事件日志-->
          <!--<add name="EventLog"/>-->
        </listeners>
      </source>
    </sources>
    <switches>
      <add name="DefaultSwitch" value="Information"/>
    </switches>
    <sharedListeners>
      <add name="FileLog" type="Microsoft.VisualBasic.Logging.FileLogTraceListener, Microsoft.VisualBasic, Version=8.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL" initializeData="FileLogWriter"/>
      <!-- 取消注释以下一节并用应用程序名替换 APPLICATION_NAME 可写入应用程序事件日志-->
      <!--<add name="EventLog" type="System.Diagnostics.EventLogTraceListener" initializeData="APPLICATION_NAME"/> -->
    </sharedListeners>
  </system.diagnostics>
  <userSettings>
    <His2010.My.MySettings>
      <setting name="DB_Id" serializeAs="String">
        <value>sa</value>
      </setting>
      <setting name="Is_Ybzl" serializeAs="String">
        <value>是</value>
      </setting>
      <setting name="Is_Yb_Ydjy" serializeAs="String">
        <value>是</value>
      </setting>
      <setting name="JkdaService" serializeAs="String">
        <value>http://kdxf.tszhjk.com/Service/WebRy.asmx</value>
      </setting>
      <setting name="DebugLb" serializeAs="String">
        <value>调试</value>
      </setting>
      <setting name="DB_Pwd" serializeAs="String">
        <value>1029</value>
      </setting>
      <setting name="DB_Name" serializeAs="String">
        <value>His2019_FuYouBaoJian</value>
      </setting>
      <setting name="DB_Ip" serializeAs="String">
        <value>.</value>
      </setting>
    </His2010.My.MySettings>
  </userSettings>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.0"/>
  </startup>
</configuration>
