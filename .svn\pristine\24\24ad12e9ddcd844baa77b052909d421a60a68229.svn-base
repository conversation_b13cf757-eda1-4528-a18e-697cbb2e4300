﻿Imports C1.Win.C1Input
Imports System.Data.SqlClient
Imports BaseClass
Imports System.Drawing

Public Class EmrMb2

#Region "变量定义"
    Dim BllEmr_Mblb As New BLLOld.B_Emr_Mblb
    Dim ModelEmr_Mblb As New ModelOld.M_Emr_Mblb
    Dim BllEmr_Mb As New BLLOld.B_Emr_Mb
    Dim cha As New BaseClass.Chs2Spell
#End Region

#Region "传参"
    Dim Rrc As C_RowChange
    Dim Rinsert As Boolean
    Dim V_LbCount As Integer
    Dim rTag, Rtext, rFTxt, rFTag As String
#End Region

    Public Sub New(ByVal tRc As C_RowChange, ByVal tinsert As Boolean, ByVal tTag As String, ByVal tText As String, Optional ByVal fTag As String = "", Optional ByVal fText As String = "")
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        Rinsert = tinsert
        Rrc = tRc
        rTag = tTag
        Rtext = tText
        rFTag = fTag
        rFTxt = fText
        ' 在 InitializeComponent() 调用之后添加任何初始化。
    End Sub



    Private Sub Zd_Cl_Kq12_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Call Form_Init()
        If Rinsert = True Then Call Data_Clear() Else Call Data_Show()
        lbNameMyTextBox1.Select()
    End Sub

#Region "窗体__事件"

    Private Sub Form_Init()
        Panel1.Height = 27
        ToolBar1.Location = New Point(2, 4)
        lbCodeTextBox1.Enabled = False
        FatherNameTextBox2.Enabled = False
        JcTextBox1.Enabled = False
        '按扭初始化
        MyButton1.Top = 2
        MyButton2.Location = New Point(MyButton1.Left + MyButton1.Width + 2, MyButton1.Top)
    End Sub

#End Region

#Region "数据__操作"

    Private Sub Data_Clear()
        Rinsert = True
        lbCodeTextBox1.Text = BllEmr_Mblb.MaxCode()
        lbNameMyTextBox1.Text = ""
        FatherNameTextBox2.Text = rFTxt
        FatherNameTextBox2.Enabled = False
        lbNameMyTextBox1.Focus()
        JcTextBox1.Text = ""
    End Sub
    Private Sub Data_Show()
        Rinsert = False
        lbCodeTextBox1.Text = Rtag
        lbNameMyTextBox1.Text = Rtext
        FatherNameTextBox2.Text = rFTxt
        FatherNameTextBox2.Enabled = False
        lbNameMyTextBox1.Focus()
        JcTextBox1.Text = cha.GetPy(Rtext)
    End Sub

#End Region

#Region "控件__动作"

    Private Sub Comm_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyButton1.Click, MyButton2.Click
        Select Case sender.tag
            Case "保存"

                If lbNameMyTextBox1.Text.Trim = "" Then
                    MsgBox("类别名称不能为空", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示")
                    If Rinsert = True Then
                        lbNameMyTextBox1.Focus()
                        Exit Sub
                    Else
                        Me.Close()
                        Exit Sub
                    End If
                End If

                If Rinsert = True Then
                    V_LbCount = BllEmr_Mblb.GetRecordCount(" Mblb_Name='" & Trim(lbNameMyTextBox1.Text) & "' ")
                    If V_LbCount > 0 Then
                        MsgBox("该类别已经存在", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示")
                        Call Data_Clear()
                        Exit Sub
                    Else
                        Call Data_Add()
                    End If
                Else

                    V_LbCount = BllEmr_Mblb.GetRecordCount(" Mblb_Name='" & Trim(lbNameMyTextBox1.Text) & "' and Mblb_code<>'" & Rtag & "' ")
                    If V_LbCount > 0 Then
                        MsgBox("该类别已经存在", MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示")
                        Me.Close()
                        Exit Sub
                    Else
                        Call Data_Edit()
                        Me.Close()
                        Exit Sub
                    End If

                End If
            Case "取消"
                Me.Close()

        End Select
    End Sub



    Private Sub lbNameMyTextBox1_TextChanged(sender As Object, e As System.EventArgs) Handles lbNameMyTextBox1.TextChanged
        If lbNameMyTextBox1.Text.Trim <> "" Then JcTextBox1.Text = cha.GetPy(Trim(lbNameMyTextBox1.Text))
    End Sub

#End Region

#Region "数据__编辑"

    Private Sub Data_Add()
        Try


            ModelEmr_Mblb.Mblb_Code = Trim(lbCodeTextBox1.Text)
            ModelEmr_Mblb.Mblb_Name = Trim(lbNameMyTextBox1.Text & "")
            ModelEmr_Mblb.Mblb_Jc = Trim(JcTextBox1.Text & "")
            ModelEmr_Mblb.Father_Code = rFTag
            BllEmr_Mblb.Add(ModelEmr_Mblb)
            ' EmrMb1.V_Insert = Rinsert


            If Rtext = "DataTable" Then

                Dim row As DataRow = MblbTree.My_Table.NewRow
                With row
                    .Item("Mblb_Code") = ModelEmr_Mblb.Mblb_Code
                    .Item("Mblb_Name") = ModelEmr_Mblb.Mblb_Name
                    .Item("Mblb_Jc") = ModelEmr_Mblb.Mblb_Jc
                    .Item("Father_Code") = ModelEmr_Mblb.Father_Code
                End With
                MblbTree.My_Table.Rows.Add(row)
                MblbTree.My_Table.AcceptChanges()
                Rrc.DrDc(ModelEmr_Mblb.Mblb_Code, ModelEmr_Mblb.Mblb_Name & "()", ModelEmr_Mblb.Father_Code)
            Else
                Rrc.TreeAdd(lbCodeTextBox1.Text, Trim(lbNameMyTextBox1.Text) & "(0)")
            End If

            '数据保存
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            lbNameMyTextBox1.Select()
            Exit Sub
        Finally

        End Try
        '数据清空
        Call Data_Clear()

    End Sub

    Private Sub Data_Edit()
        '数据更新
        ModelEmr_Mblb.Mblb_Code = lbCodeTextBox1.Text
        ModelEmr_Mblb.Mblb_Name = Trim(lbNameMyTextBox1.Text & "")
        ModelEmr_Mblb.Mblb_Jc = Trim(JcTextBox1.Text & "")
        ModelEmr_Mblb.Father_Code = rFTag
        Try
            BllEmr_Mblb.Update(ModelEmr_Mblb)
            If MblbTree.My_Table IsNot Nothing Then
                Dim row As DataRow = MblbTree.My_Table.Select("mblb_code='" & ModelEmr_Mblb.Mblb_Code & "'")(0)
                With row
                    .BeginEdit()
                    row("Mblb_Name") = ModelEmr_Mblb.Mblb_Name
                    .EndEdit()
                End With
                row.AcceptChanges()
                Rrc.DrDc(lbCodeTextBox1.Text, Trim(lbNameMyTextBox1.Text) & "(" & BllEmr_Mb.GetRecordCount(" Emr_Mb.Mblb_Code='" & ModelEmr_Mblb.Mblb_Code & "'") & ")", "update")
            Else
                Dim BllMblb As New BLLOld.B_Emr_Mblb
                Call Rrc.TreeAdd(lbCodeTextBox1.Text, Trim(lbNameMyTextBox1.Text) & "(" & BllEmr_Mb.GetRecordCount(" Emr_Mb.Mblb_Code='" & ModelEmr_Mblb.Mblb_Code & "'") & ")")
            End If
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
        Finally
            lbNameMyTextBox1.Select()
        End Try
        ' EmrMb1.V_Insert = Rinsert
       
    End Sub

#End Region


End Class