﻿/**  版本信息模板在安装目录下，可自行修改。
* M_Emr_BlZk2.cs
*
* 功 能： N/A
* 类 名： M_Emr_BlZk2
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/9/19 17:08:27   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace ModelOld
{
	/// <summary>
	/// M_Emr_BlZk2:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class M_Emr_BlZk2
	{
		public M_Emr_BlZk2()
		{}
		#region Model
		private string _bl_code;
		private int _id;
        private string _mb_code;
		private int? _kf_count;
		private decimal? _kf;
		/// <summary>
		/// 
		/// </summary>
		public string Bl_Code
		{
			set{ _bl_code=value;}
			get{return _bl_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public int id
		{
			set{ _id=value;}
			get{return _id;}
		}
        /// <summary>
        /// 
        /// </summary>
        public string Mb_Code
        {
            set { _mb_code = value; }
            get { return _mb_code; }
        }
		/// <summary>
		/// 
		/// </summary>
		public int? Kf_Count
		{
			set{ _kf_count=value;}
			get{return _kf_count;}
		}
		/// <summary>
		/// 
		/// </summary>
		public decimal? Kf
		{
			set{ _kf=value;}
			get{return _kf;}
		}
		#endregion Model

	}
}

