﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class MbElement
    Inherits HisControl.BaseForm

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(MbElement))
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel()
        Me.Panel5 = New System.Windows.Forms.Panel()
        Me.MyButton3 = New CustomControl.MyButton()
        Me.MyButton5 = New CustomControl.MyButton()
        Me.MyButton4 = New CustomControl.MyButton()
        Me.SplitContainer1 = New System.Windows.Forms.SplitContainer()
        Me.ListTextBox1 = New CustomControl.MyTextBox()
        Me.SplitContainer2 = New System.Windows.Forms.SplitContainer()
        Me.DefaultTextBox1 = New CustomControl.MyTextBox()
        Me.DataFieldComobo1 = New CustomControl.MyDtComobo()
        Me.TreeView1 = New System.Windows.Forms.TreeView()
        Me.Image1 = New System.Windows.Forms.ImageList(Me.components)
        Me.TableLayoutPanel1.SuspendLayout()
        Me.Panel5.SuspendLayout()
        Me.SplitContainer1.Panel1.SuspendLayout()
        Me.SplitContainer1.Panel2.SuspendLayout()
        Me.SplitContainer1.SuspendLayout()
        Me.SplitContainer2.Panel1.SuspendLayout()
        Me.SplitContainer2.Panel2.SuspendLayout()
        Me.SplitContainer2.SuspendLayout()
        Me.SuspendLayout()
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.CellBorderStyle = System.Windows.Forms.TableLayoutPanelCellBorderStyle.[Single]
        Me.TableLayoutPanel1.ColumnCount = 2
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 250.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanel1.Controls.Add(Me.Panel5, 1, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.SplitContainer1, 1, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.TreeView1, 0, 0)
        Me.TableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(0, 0)
        Me.TableLayoutPanel1.Margin = New System.Windows.Forms.Padding(0)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.RowCount = 2
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 50.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 20.0!))
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(845, 566)
        Me.TableLayoutPanel1.TabIndex = 7
        '
        'Panel5
        '
        Me.Panel5.Controls.Add(Me.MyButton3)
        Me.Panel5.Controls.Add(Me.MyButton5)
        Me.Panel5.Controls.Add(Me.MyButton4)
        Me.Panel5.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel5.Location = New System.Drawing.Point(252, 1)
        Me.Panel5.Margin = New System.Windows.Forms.Padding(0)
        Me.Panel5.Name = "Panel5"
        Me.Panel5.Size = New System.Drawing.Size(592, 50)
        Me.Panel5.TabIndex = 15
        '
        'MyButton3
        '
        Me.MyButton3.ButtonImageSize = CustomControl.MyButton.imageSize.large
        Me.MyButton3.DialogResult = System.Windows.Forms.DialogResult.None
        Me.MyButton3.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MyButton3.Location = New System.Drawing.Point(345, 8)
        Me.MyButton3.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.MyButton3.Name = "MyButton3"
        Me.MyButton3.Size = New System.Drawing.Size(80, 35)
        Me.MyButton3.TabIndex = 6
        Me.MyButton3.Text = "查询"
        '
        'MyButton5
        '
        Me.MyButton5.ButtonImageSize = CustomControl.MyButton.imageSize.large
        Me.MyButton5.DialogResult = System.Windows.Forms.DialogResult.None
        Me.MyButton5.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MyButton5.Location = New System.Drawing.Point(505, 8)
        Me.MyButton5.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.MyButton5.Name = "MyButton5"
        Me.MyButton5.Size = New System.Drawing.Size(80, 35)
        Me.MyButton5.TabIndex = 5
        Me.MyButton5.Text = "取消"
        '
        'MyButton4
        '
        Me.MyButton4.ButtonImageSize = CustomControl.MyButton.imageSize.large
        Me.MyButton4.DialogResult = System.Windows.Forms.DialogResult.None
        Me.MyButton4.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MyButton4.Location = New System.Drawing.Point(425, 8)
        Me.MyButton4.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.MyButton4.Name = "MyButton4"
        Me.MyButton4.Size = New System.Drawing.Size(80, 35)
        Me.MyButton4.TabIndex = 4
        Me.MyButton4.Text = "插入"
        '
        'SplitContainer1
        '
        Me.SplitContainer1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.SplitContainer1.Location = New System.Drawing.Point(255, 55)
        Me.SplitContainer1.Name = "SplitContainer1"
        '
        'SplitContainer1.Panel1
        '
        Me.SplitContainer1.Panel1.Controls.Add(Me.ListTextBox1)
        '
        'SplitContainer1.Panel2
        '
        Me.SplitContainer1.Panel2.Controls.Add(Me.SplitContainer2)
        Me.SplitContainer1.Size = New System.Drawing.Size(586, 507)
        Me.SplitContainer1.SplitterDistance = 190
        Me.SplitContainer1.TabIndex = 13
        '
        'ListTextBox1
        '
        Me.ListTextBox1.Captain = "标题栏"
        Me.ListTextBox1.CaptainBackColor = System.Drawing.Color.Transparent
        Me.ListTextBox1.CaptainFont = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.ListTextBox1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.ListTextBox1.CaptainVisble = False
        Me.ListTextBox1.CaptainWidth = 0.0!
        Me.ListTextBox1.ContentForeColor = System.Drawing.Color.Black
        Me.ListTextBox1.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.ListTextBox1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.ListTextBox1.Location = New System.Drawing.Point(0, 0)
        Me.ListTextBox1.Multiline = True
        Me.ListTextBox1.Name = "ListTextBox1"
        Me.ListTextBox1.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.ListTextBox1.ReadOnly = False
        Me.ListTextBox1.ScrollBars = System.Windows.Forms.ScrollBars.Vertical
        Me.ListTextBox1.SelectionStart = 0
        Me.ListTextBox1.SelectStart = 0
        Me.ListTextBox1.Size = New System.Drawing.Size(190, 507)
        Me.ListTextBox1.TabIndex = 0
        Me.ListTextBox1.TextFont = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.ListTextBox1.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Top
        '
        'SplitContainer2
        '
        Me.SplitContainer2.Dock = System.Windows.Forms.DockStyle.Fill
        Me.SplitContainer2.Location = New System.Drawing.Point(0, 0)
        Me.SplitContainer2.Name = "SplitContainer2"
        '
        'SplitContainer2.Panel1
        '
        Me.SplitContainer2.Panel1.Controls.Add(Me.DefaultTextBox1)
        '
        'SplitContainer2.Panel2
        '
        Me.SplitContainer2.Panel2.Controls.Add(Me.DataFieldComobo1)
        Me.SplitContainer2.Size = New System.Drawing.Size(392, 507)
        Me.SplitContainer2.SplitterDistance = 146
        Me.SplitContainer2.TabIndex = 0
        '
        'DefaultTextBox1
        '
        Me.DefaultTextBox1.Captain = "默 认 值"
        Me.DefaultTextBox1.CaptainBackColor = System.Drawing.Color.Transparent
        Me.DefaultTextBox1.CaptainFont = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.DefaultTextBox1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.DefaultTextBox1.CaptainWidth = 70.0!
        Me.DefaultTextBox1.ContentForeColor = System.Drawing.Color.Black
        Me.DefaultTextBox1.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.DefaultTextBox1.Location = New System.Drawing.Point(3, 17)
        Me.DefaultTextBox1.Multiline = False
        Me.DefaultTextBox1.Name = "DefaultTextBox1"
        Me.DefaultTextBox1.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.DefaultTextBox1.ReadOnly = False
        Me.DefaultTextBox1.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.DefaultTextBox1.SelectionStart = 0
        Me.DefaultTextBox1.SelectStart = 0
        Me.DefaultTextBox1.Size = New System.Drawing.Size(300, 22)
        Me.DefaultTextBox1.TabIndex = 1
        Me.DefaultTextBox1.TextFont = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.DefaultTextBox1.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        '
        'DataFieldComobo1
        '
        Me.DataFieldComobo1.AutoSize = True
        Me.DataFieldComobo1.Captain = "数据字段"
        Me.DataFieldComobo1.CaptainFont = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.DataFieldComobo1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.DataFieldComobo1.CaptainWidth = 70.0!
        Me.DataFieldComobo1.DataSource = Nothing
        Me.DataFieldComobo1.ItemHeight = 16
        Me.DataFieldComobo1.ItemTextFont = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.DataFieldComobo1.Location = New System.Drawing.Point(8, 19)
        Me.DataFieldComobo1.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.DataFieldComobo1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.DataFieldComobo1.Name = "DataFieldComobo1"
        Me.DataFieldComobo1.ReadOnly = False
        Me.DataFieldComobo1.Size = New System.Drawing.Size(261, 20)
        Me.DataFieldComobo1.TabIndex = 1
        Me.DataFieldComobo1.TextFont = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'TreeView1
        '
        Me.TreeView1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TreeView1.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.TreeView1.FullRowSelect = True
        Me.TreeView1.HotTracking = True
        Me.TreeView1.ImageIndex = 0
        Me.TreeView1.ImageList = Me.Image1
        Me.TreeView1.Location = New System.Drawing.Point(1, 1)
        Me.TreeView1.Margin = New System.Windows.Forms.Padding(0)
        Me.TreeView1.Name = "TreeView1"
        Me.TableLayoutPanel1.SetRowSpan(Me.TreeView1, 2)
        Me.TreeView1.SelectedImageIndex = 0
        Me.TreeView1.Size = New System.Drawing.Size(250, 564)
        Me.TreeView1.TabIndex = 5
        '
        'Image1
        '
        Me.Image1.ImageStream = CType(resources.GetObject("Image1.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.Image1.TransparentColor = System.Drawing.Color.White
        Me.Image1.Images.SetKeyName(0, "")
        Me.Image1.Images.SetKeyName(1, "")
        Me.Image1.Images.SetKeyName(2, "")
        Me.Image1.Images.SetKeyName(3, "Icon_1361.png")
        '
        'MbElement
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(845, 566)
        Me.Controls.Add(Me.TableLayoutPanel1)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle
        Me.MinimizeBox = False
        Me.Name = "MbElement"
        Me.ShowInTaskbar = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "基本元素"
        Me.TableLayoutPanel1.ResumeLayout(False)
        Me.Panel5.ResumeLayout(False)
        Me.SplitContainer1.Panel1.ResumeLayout(False)
        Me.SplitContainer1.Panel2.ResumeLayout(False)
        Me.SplitContainer1.ResumeLayout(False)
        Me.SplitContainer2.Panel1.ResumeLayout(False)
        Me.SplitContainer2.Panel2.ResumeLayout(False)
        Me.SplitContainer2.Panel2.PerformLayout()
        Me.SplitContainer2.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    'Friend WithEvents C1Holder1 As C1.Win.C1Command.C1CommandHolder
    Friend WithEvents T_Label As C1.Win.C1Input.C1Label
    ' Friend WithEvents MyGrid1 As CustomControl.MyGrid
    Friend WithEvents ListBox2 As System.Windows.Forms.ListBox
    Friend WithEvents ComboBox2 As System.Windows.Forms.ComboBox
    Friend WithEvents TableLayoutPanel1 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents TreeView1 As System.Windows.Forms.TreeView
    Friend WithEvents SplitContainer1 As System.Windows.Forms.SplitContainer
    Friend WithEvents SplitContainer2 As System.Windows.Forms.SplitContainer
    Friend WithEvents DefaultTextBox1 As CustomControl.MyTextBox
    Friend WithEvents DataFieldComobo1 As CustomControl.MyDtComobo
    Friend WithEvents Panel5 As System.Windows.Forms.Panel
    Friend WithEvents MyButton5 As CustomControl.MyButton
    Friend WithEvents MyButton4 As CustomControl.MyButton
    Friend WithEvents MyButton3 As CustomControl.MyButton

    Friend WithEvents Image1 As System.Windows.Forms.ImageList

    Friend WithEvents ListTextBox1 As CustomControl.MyTextBox

End Class
