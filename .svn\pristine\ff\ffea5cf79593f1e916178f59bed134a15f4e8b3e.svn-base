﻿/**  版本信息模板在安装目录下，可自行修改。
* D_Bl_Jf.cs
*
* 功 能： N/A
* 类 名： D_Bl_Jf
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/7/1 16:09:00   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;

namespace  DAL
{
	/// <summary>
	/// 数据访问类:D_Bl_Jf
	/// </summary>
	public partial class D_Bl_Jf
	{
		public D_Bl_Jf()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string Jf_Code)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from Bl_Jf");
			strSql.Append(" where Jf_Code=@Jf_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Jf_Code", SqlDbType.Char,14)			};
			parameters[0].Value = Jf_Code;

			return HisVar.HisVar.Sqldal.Exists(strSql.ToString(),parameters);
		}

        public string MaxCode()
        {
            string max = HisVar.HisVar.WsyCode + DateTime.Now.ToString("yyMMdd") + HisVar.HisVar.Sqldal.F_MaxCode("select max(substring(Jf_Code,11,4)) from Bl_Jf where substring(jf_code,1,10)='" + HisVar.HisVar.WsyCode + DateTime.Now.ToString("yyMMdd")+"'", 4);
            return max;
        }

		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add( ModelOld.M_Bl_Jf model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into Bl_Jf(");
			strSql.Append("Yy_Code,Bl_Code,Jf_Code,Jf_Money,Jf_Date,Jsr_Code,Jf_Memo,Jz_Code,Bl_Jffs,JkkId,Bank_No)");
			strSql.Append(" values (");
			strSql.Append("@Yy_Code,@Bl_Code,@Jf_Code,@Jf_Money,@Jf_Date,@Jsr_Code,@Jf_Memo,@Jz_Code,@Bl_Jffs,@JkkId,@Bank_No)");
			SqlParameter[] parameters = {
					new SqlParameter("@Yy_Code", SqlDbType.Char,4),
					new SqlParameter("@Bl_Code", SqlDbType.Char,14),
					new SqlParameter("@Jf_Code", SqlDbType.Char,14),
					new SqlParameter("@Jf_Money", SqlDbType.Decimal,9),
					new SqlParameter("@Jf_Date", SqlDbType.DateTime),
					new SqlParameter("@Jsr_Code", SqlDbType.Char,7),
					new SqlParameter("@Jf_Memo", SqlDbType.VarChar,200),
					new SqlParameter("@Jz_Code", SqlDbType.VarChar,12),
					new SqlParameter("@Bl_Jffs", SqlDbType.VarChar,20),
					new SqlParameter("@JkkId", SqlDbType.Int,4),
					new SqlParameter("@Bank_No", SqlDbType.VarChar,50)};
            if (model.Yy_Code == null)
            {
                parameters[0].Value = DBNull.Value;
            }
            else
            {
                parameters[0].Value = model.Yy_Code;
            }


            if (model.Bl_Code == null)
            {
                parameters[1].Value = DBNull.Value;
            }
            else
            {
                parameters[1].Value = model.Bl_Code;
            }


            if (model.Jf_Code == null)
            {
                parameters[2].Value = DBNull.Value;
            }
            else
            {
                parameters[2].Value = model.Jf_Code;
            }


            if (model.Jf_Money == null)
            {
                parameters[3].Value = DBNull.Value;
            }
            else
            {
                parameters[3].Value = model.Jf_Money;
            }


            if (model.Jf_Date == null)
            {
                parameters[4].Value = DBNull.Value;
            }
            else
            {
                parameters[4].Value = model.Jf_Date;
            }


            if (model.Jsr_Code == null)
            {
                parameters[5].Value = DBNull.Value;
            }
            else
            {
                parameters[5].Value = model.Jsr_Code;
            }


            if (model.Jf_Memo == null)
            {
                parameters[6].Value = DBNull.Value;
            }
            else
            {
                parameters[6].Value = model.Jf_Memo;
            }


            if (model.Jz_Code == null)
            {
                parameters[7].Value = DBNull.Value;
            }
            else
            {
                parameters[7].Value = model.Jz_Code;
            }
            if (model.Bl_Jffs == null)
            {
                parameters[8].Value = DBNull.Value;
            }
            else
            {
                parameters[8].Value = model.Bl_Jffs;
            }


            if (model.JkkId == null)
            {
                parameters[9].Value = DBNull.Value;
            }
            else
            {
                parameters[9].Value = model.JkkId;
            }


            if (model.Bank_No == null)
            {
                parameters[10].Value = DBNull.Value;
            }
            else
            {
                parameters[10].Value = model.Bank_No;
            }
			

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update( ModelOld.M_Bl_Jf model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update Bl_Jf set ");
			strSql.Append("Yy_Code=@Yy_Code,");
			strSql.Append("Bl_Code=@Bl_Code,");
			strSql.Append("Jf_Money=@Jf_Money,");
			strSql.Append("Jf_Date=@Jf_Date,");
			strSql.Append("Jsr_Code=@Jsr_Code,");
			strSql.Append("Jf_Memo=@Jf_Memo,");
			strSql.Append("Jz_Code=@Jz_Code,");
			strSql.Append("Bl_Jffs=@Bl_Jffs,");
			strSql.Append("JkkId=@JkkId,");
			strSql.Append("Bank_No=@Bank_No");
			strSql.Append(" where Jf_Code=@Jf_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Yy_Code", SqlDbType.Char,4),
					new SqlParameter("@Bl_Code", SqlDbType.Char,14),
					new SqlParameter("@Jf_Money", SqlDbType.Decimal,9),
					new SqlParameter("@Jf_Date", SqlDbType.DateTime),
					new SqlParameter("@Jsr_Code", SqlDbType.Char,7),
					new SqlParameter("@Jf_Memo", SqlDbType.VarChar,200),
					new SqlParameter("@Jz_Code", SqlDbType.VarChar,12),
					new SqlParameter("@Bl_Jffs", SqlDbType.VarChar,20),
					new SqlParameter("@JkkId", SqlDbType.Int,4),
					new SqlParameter("@Bank_No", SqlDbType.VarChar,50),
					new SqlParameter("@Jf_Code", SqlDbType.Char,14)};
			parameters[0].Value = model.Yy_Code;
			parameters[1].Value = model.Bl_Code;
			parameters[2].Value = model.Jf_Money;
			parameters[3].Value = model.Jf_Date;
			parameters[4].Value = model.Jsr_Code;
			parameters[5].Value = model.Jf_Memo;
			parameters[6].Value = model.Jz_Code;
			parameters[7].Value = model.Bl_Jffs;
			parameters[8].Value = model.JkkId;
			parameters[9].Value = model.Bank_No;
			parameters[10].Value = model.Jf_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

        public bool UpdateYj(string JkkId, string Bank_No, string Jf_Code)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update Bl_Jf set ");
            strSql.Append("JkkId=@JkkId,");
            strSql.Append("Bank_No=@Bank_No");
            strSql.Append(" where Jf_Code=@Jf_Code ");
            SqlParameter[] parameters = {
					new SqlParameter("@JkkId", SqlDbType.Int,4),
					new SqlParameter("@Bank_No", SqlDbType.VarChar,50),
					new SqlParameter("@Jf_Code", SqlDbType.Char,14)};
            
            parameters[0].Value = JkkId;
            parameters[1].Value = Bank_No;
            parameters[2].Value = Jf_Code;

            int rows = HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(), parameters);
            if (rows > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string Jf_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Bl_Jf ");
			strSql.Append(" where Jf_Code=@Jf_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Jf_Code", SqlDbType.Char,14)			};
			parameters[0].Value = Jf_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string Jf_Codelist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Bl_Jf ");
			strSql.Append(" where Jf_Code in ("+Jf_Codelist + ")  ");
			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public  ModelOld.M_Bl_Jf GetModel(string Jf_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 Yy_Code,Bl_Code,Jf_Code,Jf_Money,Jf_Date,Jsr_Code,Jf_Memo,Jz_Code,Bl_Jffs,JkkId,Bank_No from Bl_Jf ");
			strSql.Append(" where Jf_Code=@Jf_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Jf_Code", SqlDbType.Char,14)			};
			parameters[0].Value = Jf_Code;

			 ModelOld.M_Bl_Jf model=new  ModelOld.M_Bl_Jf();
			DataSet ds=HisVar.HisVar.Sqldal.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public  ModelOld.M_Bl_Jf DataRowToModel(DataRow row)
		{
			 ModelOld.M_Bl_Jf model=new  ModelOld.M_Bl_Jf();
			if (row != null)
			{
				if(row["Yy_Code"]!=null)
				{
					model.Yy_Code=row["Yy_Code"].ToString();
				}
				if(row["Bl_Code"]!=null)
				{
					model.Bl_Code=row["Bl_Code"].ToString();
				}
				if(row["Jf_Code"]!=null)
				{
					model.Jf_Code=row["Jf_Code"].ToString();
				}
				if(row["Jf_Money"]!=null && row["Jf_Money"].ToString()!="")
				{
					model.Jf_Money=decimal.Parse(row["Jf_Money"].ToString());
				}
				if(row["Jf_Date"]!=null && row["Jf_Date"].ToString()!="")
				{
					model.Jf_Date=DateTime.Parse(row["Jf_Date"].ToString());
				}
				if(row["Jsr_Code"]!=null)
				{
					model.Jsr_Code=row["Jsr_Code"].ToString();
				}
				if(row["Jf_Memo"]!=null)
				{
					model.Jf_Memo=row["Jf_Memo"].ToString();
				}
				if(row["Jz_Code"]!=null)
				{
					model.Jz_Code=row["Jz_Code"].ToString();
				}
				if(row["Bl_Jffs"]!=null)
				{
					model.Bl_Jffs=row["Bl_Jffs"].ToString();
				}
				if(row["JkkId"]!=null && row["JkkId"].ToString()!="")
				{
					model.JkkId=int.Parse(row["JkkId"].ToString());
				}
				if(row["Bank_No"]!=null)
				{
					model.Bank_No=row["Bank_No"].ToString();
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select Yy_Code,Bl_Code,Jf_Code,Jf_Money,Jf_Date,Jsr_Code,Jf_Memo,Jz_Code,Bl_Jffs,JkkId,Bank_No ");
			strSql.Append(" FROM Bl_Jf ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" Yy_Code,Bl_Code,Jf_Code,Jf_Money,Jf_Date,Jsr_Code,Jf_Memo,Jz_Code,Bl_Jffs,JkkId,Bank_No ");
			strSql.Append(" FROM Bl_Jf ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM Bl_Jf ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}

        public DataSet GetYjList(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Bl_Jf.Yy_Code,Bl.Bl_Code,Bl_Jf.Jf_Code,Jf_Money,Jf_Date,Jf_Memo,Bl_Jffs,Bl_Jf.Jsr_Code,Bl.Ry_Name,Jsr_Name,Bl.Ks_Code,Bl.Ys_Code,Ks_Name,Ys_Name,Ry_BlCode,Bc_Code ");
            strSql.Append(" FROM Zd_YyJsr,Bl,Bl_Jf,Zd_YyKs,Zd_YyYs ");
            strSql.Append(" where Zd_YyJsr.Jsr_Code=Bl_Jf.Jsr_Code And Bl.Bl_Code = Bl_Jf.Bl_Code And Bl.Ks_Code=Zd_YyKs.Ks_Code And Bl.Ys_Code=Zd_YyYs.Ys_Code ");
            if (strWhere.Trim() != "")
            {
                strSql.Append( strWhere);
            }
            strSql.Append(" order by Bl_Jf.Jf_Code");
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }

        public DataSet GetBrzdList(string Yy_code) {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Bl_code,Ry_Jc,Ry_Name,Bc_Name,Ks_Name,Ys_Name,(Select isnull(sum(Jf_Money),0) Yj_Money from Bl_Jf Where Bl_Jf.Bl_Code=Bl.Bl_Code)-(Select Isnull(Sum(Cf_Money),0) From Bl_Cf Where  Bl_Code=Bl.Bl_Code and Cf_Qr='是') AS Yj_Money,Ry_BlCode  ");
            strSql.Append("  ,Identity_Code");
            strSql.Append("  from Zd_YyKs,Zd_YyYs,Bl left join Zd_Yybc on  Bl.Bc_Code=Zd_Yybc.Bc_Code");
            strSql.Append(" Where  Bl.Ks_Code=Zd_YyKs.Ks_Code and Bl.Ys_Code=Zd_YyYs.Ys_Code  and  isnull(Ry_CyJsr,'')='' ");
            if (Yy_code.Trim() != "")
            {
                strSql.Append(" and Bl.Yy_code=" + Yy_code);
            }
            strSql.Append(" Order By Bl_Code ");
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.Jf_Code desc");
			}
			strSql.Append(")AS Row, T.*  from Bl_Jf T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Bl_Jf";
			parameters[1].Value = "Jf_Code";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return HisVar.HisVar.Sqldal.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

