﻿Imports ZTHisPharmacy

Public Class Yk_Cx_AllYf
    Dim My_Dataset As New DataSet

    Private Sub Yk_Cx_AllYf_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        HisVar.HisVar.Sqldal.QueryDt(My_Dataset, "SELECT Yf_Name,Yf_Jc,Yf_Code FROM Zd_YyYf Where Yy_Code='" & HisVar.HisVar.WsyCode & "'  Order By Yf_Code", "药房字典", True)


        Dim My_Combo2 As New BaseClass.C_Combo2(Me.C1Combo2, My_Dataset.Tables("药房字典").DefaultView, "Yf_Name", "Yf_Code", 154)
        With My_Combo2
            .Init_TDBCombo()
            .Init_Colum("Yf_Name", "药房名称", 100, "左")
            .Init_Colum("Yf_Jc", "药房简称", 0, "左")
            .Init_Colum("Yf_Code", "药房编码", 0, "左")
            .MaxDropDownItems(17)
            .SelectedIndex(0)
        End With

        C1Combo2.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
    End Sub

    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button9.Click
        Dim vform As New Form

        If RadioButton1.Checked = True Then     '"库存查询"
            vform = New ZTHisPharmacy.Yf_Cx(False, C1Combo2.Columns("Yf_Code").Value)
            vform.Text = "全药房信息查询"
        ElseIf RadioButton2.Checked = True Then '"采购入库"
            vform = New ZTHisPharmacy.YfRkCx(False, C1Combo2.Columns("Yf_Code").Value)
            vform.Text = "药房采购入库查询"
        ElseIf RadioButton3.Checked = True Then  '"科室支领"
            vform = New ZTHisPharmacy.YfKsCx(False, C1Combo2.Columns("Yf_Code").Value)
            vform.Text = "药房科室支领查询"
        ElseIf RadioButton4.Checked = True Then ' "批发查询"
            vform = New ZTHisPharmacy.YfPfCx(False, C1Combo2.Columns("Yf_Code").Value)
            vform.Text = "药房批发查询"
        ElseIf RadioButton5.Checked = True Then  '"退回药库"
            vform = New ZTHisPharmacy.YfTkCx(False, C1Combo2.Columns("Yf_Code").Value)
            vform.Text = "药房退药库查询"
        ElseIf RadioButton6.Checked = True Then  '盘点查询"
            vform = New ZTHisPharmacy.YfPdCx(False, C1Combo2.Columns("Yf_Code").Value)
            vform.Text = C1Combo2.Columns("Yf_Name").Value & "盘点查询"
        ElseIf RadioButton7.Checked = True Then  '"台账查询"
            vform = New ZTHisPharmacy.YfLs(False, C1Combo2.Columns("Yf_Code").Value)
            vform.Text = C1Combo2.Columns("Yf_Name").Value & "出入库台帐"
        ElseIf RadioButton8.Checked = True Then  '"警戒线查询"
            vform = New Cx_YkYf_Alar(C1Combo2.Columns("Yf_Code").Value & "", C1Combo2.Columns("Yf_Name").Value & "", "药房警戒线查询")
            vform.Name = "Alar_YfCx"
            vform.Text = C1Combo2.Columns("Yf_Name").Value & "警戒线查询"
        ElseIf RadioButton9.Checked = True Then  '"发药查询"
            vform = New ZTHisPharmacy.Cx_ZyMz_Fy(False, C1Combo2.Columns("Yf_Code").Value)
        ElseIf RadioButton10.Checked = True Then  '"药房日结"
            vform = New Yf_Jz(C1Combo2.Columns("Yf_Code").Value & "", C1Combo2.Columns("Yf_Name").Value & "")
        End If
        BaseFunc.BaseFunc.addTabControl(vform, vform.Text)

    End Sub


End Class