﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="0" />
    <DataSources isList="true" count="2">
      <ds Ref="2" type="DataTableSource" isKey="true">
        <Alias>ds</Alias>
        <Columns isList="true" count="5">
          <value>Yp_Name,System.String</value>
          <value>Use_Num,System.String</value>
          <value>Czd_Memo,System.String</value>
          <value>Mx_XsDw,System.String</value>
          <value>Mx_Gg,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>ds</Name>
        <NameInSource />
      </ds>
      <ds2 Ref="3" type="DataTableSource" isKey="true">
        <Alias>ds</Alias>
        <Columns isList="true" count="15">
          <value>Bc_Name,System.Int32</value>
          <value>Czd_Id,System.Int64</value>
          <value>Cf_Code,System.String</value>
          <value>Bl_Code,System.String</value>
          <value>Z_Name,System.String</value>
          <value>Xx_Code,System.String</value>
          <value>Use_Num,System.Decimal</value>
          <value>Czd_Memo,System.String</value>
          <value>Yp_Name,System.String</value>
          <value>Mx_Gg,System.String</value>
          <value>Mx_Cd,System.String</value>
          <value>Yp_Yxq,System.DateTime</value>
          <value>Ys_Name,System.String</value>
          <value>Ry_Name,System.String</value>
          <value>Mx_Dw,System.String</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>ds2</Name>
        <NameInSource>ds</NameInSource>
      </ds2>
    </DataSources>
    <Relations isList="true" count="0" />
    <Report isRef="0" />
    <Variables isList="true" count="7">
      <value>,医生,医生,System.String,,False,False</value>
      <value>,科室,科室,System.String,,False,False</value>
      <value>,门诊时间,门诊时间,System.String,,False,False</value>
      <value>,患者姓名,患者姓名,System.String,,False,False</value>
      <value>,性别,性别,System.String,,False,False</value>
      <value>,年龄,年龄,System.String,,False,False</value>
      <value>,标题,标题,System.String,,False,False</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="4" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="7">
        <PageHeader1 Ref="5" type="PageHeaderBand" isKey="true">
          <Brush>[255:255:255]</Brush>
          <CanGrow>False</CanGrow>
          <ClientRectangle>0,0.4,9.5,3.7</ClientRectangle>
          <Components isList="true" count="23">
            <TextBox4 Ref="6" type="Text" isKey="true">
              <Border>AdvBlack;1;Solid;Black;1;Solid;Black;1;Solid;Black;1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>2.01,2.43,2.33,0.64</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>TextBox4</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <VertAlignment>Center</VertAlignment>
            </TextBox4>
            <Label4 Ref="7" type="Text" isKey="true">
              <Border>AdvBlack;1;Solid;[0:0:0];1;Solid;Black;1;Solid;Black;1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0.21,2.43,1.8,0.64</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Microsoft Sans Serif,10.5</Font>
              <HorAlignment>Center</HorAlignment>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>Label4</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <Text>姓 名</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <VertAlignment>Center</VertAlignment>
            </Label4>
            <Label5 Ref="8" type="Text" isKey="true">
              <Border>AdvBlack;1;Solid;[0:0:0];1;Solid;Black;1;Solid;Black;1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>4.34,2.43,1.38,0.64</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Microsoft Sans Serif,10.5</Font>
              <HorAlignment>Center</HorAlignment>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>Label5</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <Text>性别</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <VertAlignment>Center</VertAlignment>
            </Label5>
            <TextBox5 Ref="9" type="Text" isKey="true">
              <Border>AdvBlack;1;Solid;Black;1;Solid;Black;1;Solid;Black;1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>5.72,2.43,1.38,0.64</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <HorAlignment>Center</HorAlignment>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>TextBox5</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <VertAlignment>Center</VertAlignment>
            </TextBox5>
            <Label6 Ref="10" type="Text" isKey="true">
              <Border>AdvBlack;1;Solid;[0:0:0];1;Solid;Black;1;Solid;Black;1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>7.09,2.43,1.27,0.64</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Microsoft Sans Serif,10.5</Font>
              <HorAlignment>Center</HorAlignment>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>Label6</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <Text>年龄</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <VertAlignment>Center</VertAlignment>
            </Label6>
            <TextBox6 Ref="11" type="Text" isKey="true">
              <Border>AdvBlack;1;Solid;Black;1;Solid;Black;1;Solid;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>8.36,2.43,1.06,0.64</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <HorAlignment>Center</HorAlignment>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>TextBox6</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <VertAlignment>Center</VertAlignment>
            </TextBox6>
            <Label7 Ref="12" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0.21,0.95,1.8,0.74</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Microsoft Sans Serif,10.5</Font>
              <HorAlignment>Center</HorAlignment>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>Label7</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <Text>医    生:</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <VertAlignment>Center</VertAlignment>
            </Label7>
            <TextBox7 Ref="13" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>2.01,0.95,2.33,0.74</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>TextBox7</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <VertAlignment>Center</VertAlignment>
            </TextBox7>
            <TextBox8 Ref="14" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>2.01,1.69,3.7,0.74</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>TextBox8</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <VertAlignment>Center</VertAlignment>
            </TextBox8>
            <Label8 Ref="15" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0.21,1.69,1.8,0.74</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Microsoft Sans Serif,10.5</Font>
              <HorAlignment>Center</HorAlignment>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>Label8</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <Text>科    室:</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <VertAlignment>Center</VertAlignment>
            </Label8>
            <TextBox15 Ref="16" type="Text" isKey="true">
              <Border>AdvBlack;1;None;Black;1;Solid;Black;1;None;Black;1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>2.01,3.07,3.7,0.64</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9</Font>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>TextBox15</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <VertAlignment>Center</VertAlignment>
            </TextBox15>
            <Label11 Ref="17" type="Text" isKey="true">
              <Border>AdvBlack;1;None;Black;1;Solid;Black;1;Solid;Black;1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0.21,3.07,1.8,0.64</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Microsoft Sans Serif,10.5</Font>
              <HorAlignment>Center</HorAlignment>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>Label11</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <Text>RP:</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <VertAlignment>Center</VertAlignment>
            </Label11>
            <TextBox16 Ref="18" type="Text" isKey="true">
              <Border>AdvBlack;1;None;Black;1;Solid;Black;1;None;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>7.09,3.07,2.33,0.64</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9</Font>
              <HorAlignment>Center</HorAlignment>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>TextBox16</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <Text>（         ）</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <VertAlignment>Center</VertAlignment>
            </TextBox16>
            <TextBox17 Ref="19" type="Text" isKey="true">
              <Border>AdvBlack;1;None;Black;1;Solid;Black;1;None;Black;1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>5.72,3.07,1.38,0.64</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <HorAlignment>Right</HorAlignment>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>TextBox17</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <Text>试 敏</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <VertAlignment>Center</VertAlignment>
            </TextBox17>
            <TextBox9 Ref="20" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>5.72,0.95,3.7,0.74</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5</Font>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>TextBox9</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <VertAlignment>Center</VertAlignment>
            </TextBox9>
            <Label1 Ref="21" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>4.34,0.95,1.38,0.74</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Microsoft Sans Serif,10.5</Font>
              <HorAlignment>Center</HorAlignment>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>Label1</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <Text>护士:</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <VertAlignment>Center</VertAlignment>
            </Label1>
            <Text1 Ref="22" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.03,1.02,2.29,0.76</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,10.5,Regular,Point,False,0</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text1</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <Text>{医生}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text1>
            <Text2 Ref="23" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.03,1.78,2.79,0.51</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,10.5,Regular,Point,False,0</Font>
              <Margins>0,0,0,0</Margins>
              <Name>Text2</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <Text>{科室}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text2>
            <Text3 Ref="24" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>5.08,1.78,4.37,0.51</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,10.5,Regular,Point,False,0</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text3</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <Text>{门诊时间}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text3>
            <Text4 Ref="25" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>2.03,2.54,2.29,0.51</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,10.5,Regular,Point,False,0</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text4</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <Text>{患者姓名}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text4>
            <Text5 Ref="26" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>5.84,2.54,1.27,0.51</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,10.5,Regular,Point,False,0</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text5</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <Text>{性别}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text5>
            <Text6 Ref="27" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>8.38,2.54,1.02,0.51</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,10.5,Regular,Point,False,0</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text6</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <Text>{年龄}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text6>
            <Text7 Ref="28" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0.2,0,9.1,0.7</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Arial,10.5,Regular,Point,False,0</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text7</Name>
              <Page isRef="4" />
              <Parent isRef="5" />
              <Text>{标题}</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Bottom</VertAlignment>
            </Text7>
          </Components>
          <Conditions isList="true" count="0" />
          <Linked>True</Linked>
          <Name>PageHeader1</Name>
          <Page isRef="4" />
          <Parent isRef="4" />
        </PageHeader1>
        <PageFooter1 Ref="29" type="PageFooterBand" isKey="true">
          <Brush>[255:255:255]</Brush>
          <CanGrow>False</CanGrow>
          <ClientRectangle>0,27.7,9.5,0</ClientRectangle>
          <Components isList="true" count="0" />
          <Conditions isList="true" count="0" />
          <Linked>True</Linked>
          <Name>PageFooter1</Name>
          <Page isRef="4" />
          <Parent isRef="4" />
        </PageFooter1>
        <GroupHeader1 Ref="30" type="GroupHeaderBand" isKey="true">
          <Brush>[255:255:255]</Brush>
          <ClientRectangle>0,4.9,9.5,0</ClientRectangle>
          <Components isList="true" count="0" />
          <Condition>{ds2.Z_Name}</Condition>
          <Conditions isList="true" count="0" />
          <Linked>True</Linked>
          <Name>GroupHeader1</Name>
          <Page isRef="4" />
          <Parent isRef="4" />
        </GroupHeader1>
        <GroupHeader2 Ref="31" type="GroupHeaderBand" isKey="true">
          <Brush>[255:255:255]</Brush>
          <ClientRectangle>0,5.7,9.5,0</ClientRectangle>
          <Components isList="true" count="0" />
          <Condition>{ds2.Z_Name}</Condition>
          <Conditions isList="true" count="0" />
          <Linked>True</Linked>
          <Name>GroupHeader2</Name>
          <Page isRef="4" />
          <Parent isRef="4" />
        </GroupHeader2>
        <Detail1 Ref="32" type="DataBand" isKey="true">
          <Brush>[255:255:255]</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,6.5,9.5,1.21</ClientRectangle>
          <Components isList="true" count="5">
            <TextBox1 Ref="33" type="Text" isKey="true">
              <Border>AdvBlack;1;None;Black;1;None;Black;1;Solid;Black;1;None;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0.21,0,5.5,0.89</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Microsoft Sans Serif,9</Font>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>TextBox1</Name>
              <Page isRef="4" />
              <Parent isRef="32" />
              <Text>{ds2.Yp_Name}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </TextBox1>
            <TextBox11 Ref="34" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>7.62,0,1.06,0.64</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Microsoft Sans Serif,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>TextBox11</Name>
              <Page isRef="4" />
              <Parent isRef="32" />
              <Text>{ds2.Use_Num}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="35" type="CustomFormat" isKey="true">
                <StringFormat>0.####</StringFormat>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </TextBox11>
            <TextBox12 Ref="36" type="Text" isKey="true">
              <Border>AdvBlack;1;None;Black;1;None;Black;1;Solid;[0:0:0];1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0.21,0.64,9.21,0.83</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Microsoft Sans Serif,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>TextBox12</Name>
              <Page isRef="4" />
              <Parent isRef="32" />
              <Text>{ds2.Czd_Memo}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </TextBox12>
            <TextBox14 Ref="37" type="Text" isKey="true">
              <Border>AdvBlack;1;None;Black;1;None;Black;1;None;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>8.68,0,0.74,0.89</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Microsoft Sans Serif,10</Font>
              <HideZeros>True</HideZeros>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>TextBox14</Name>
              <Page isRef="4" />
              <Parent isRef="32" />
              <Text>{ds2.Mx_Dw}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </TextBox14>
            <TextBox21 Ref="38" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>5.72,0,1.9,0.64</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>Microsoft Sans Serif,10</Font>
              <HorAlignment>Right</HorAlignment>
              <Linked>True</Linked>
              <Margins>0,0,0,0</Margins>
              <Name>TextBox21</Name>
              <Page isRef="4" />
              <Parent isRef="32" />
              <Text>{ds2.Mx_Gg}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </TextBox21>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>ds2</DataSourceName>
          <Filters isList="true" count="0" />
          <Linked>True</Linked>
          <Name>Detail1</Name>
          <Page isRef="4" />
          <Parent isRef="4" />
          <Sort isList="true" count="0" />
        </Detail1>
        <GroupFooter2 Ref="39" type="GroupFooterBand" isKey="true">
          <Brush>[255:255:255]</Brush>
          <ClientRectangle>0,8.51,9.5,0.05</ClientRectangle>
          <Components isList="true" count="1">
            <Line1 Ref="40" type="HorizontalLinePrimitive" isKey="true">
              <ClientRectangle>0.21,0,9.21,0.0254</ClientRectangle>
              <Color>Black</Color>
              <EndCap Ref="41" type="Cap" isKey="true">
                <Color>Black</Color>
              </EndCap>
              <Linked>True</Linked>
              <Name>Line1</Name>
              <Page isRef="4" />
              <Parent isRef="39" />
              <Size>5</Size>
              <StartCap Ref="42" type="Cap" isKey="true">
                <Color>Black</Color>
              </StartCap>
            </Line1>
          </Components>
          <Conditions isList="true" count="0" />
          <Linked>True</Linked>
          <Name>GroupFooter2</Name>
          <Page isRef="4" />
          <Parent isRef="4" />
        </GroupFooter2>
        <GroupFooter1 Ref="43" type="GroupFooterBand" isKey="true">
          <Brush>[255:255:255]</Brush>
          <CanGrow>False</CanGrow>
          <ClientRectangle>0,9.36,9.5,0</ClientRectangle>
          <Components isList="true" count="0" />
          <Conditions isList="true" count="0" />
          <Linked>True</Linked>
          <Name>GroupFooter1</Name>
          <Page isRef="4" />
          <Parent isRef="4" />
        </GroupFooter1>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>7811a36e0d9d4938ae3caefa15ddec2a</Guid>
      <Margins>0.5,0.5,1,1</Margins>
      <Name>Page1</Name>
      <PageHeight>29.7</PageHeight>
      <PageWidth>10.5</PageWidth>
      <Report isRef="0" />
      <Watermark Ref="44" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page1>
  </Pages>
  <PrinterSettings Ref="45" type="Stimulsoft.Report.Print.StiPrinterSettings" isKey="true" />
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>输液卡</ReportAlias>
  <ReportChanged>11/9/2018 2:53:57 PM</ReportChanged>
  <ReportCreated>11/8/2018 2:54:17 PM</ReportCreated>
  <ReportDescription>ActiveReports Version: 3.1</ReportDescription>
  <ReportFile>E:\下载文件\ZTHis5\ZTHisOutpatient\Rpt\输液卡（长春连打）.mrt</ReportFile>
  <ReportGuid>40a01bd76567478bbc23b2093520c694</ReportGuid>
  <ReportName>输液卡</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2011.2.1026</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class ARNet_Document : Stimulsoft.Report.StiReport
    {
        public ARNet_Document()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
        #endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>