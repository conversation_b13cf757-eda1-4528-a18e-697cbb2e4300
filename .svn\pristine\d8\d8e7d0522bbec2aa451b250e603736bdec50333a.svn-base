﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class EmrMb2
    Inherits HisControl.BaseChild

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(EmrMb2))
        Me.C1Holder1 = New C1.Win.C1Command.C1CommandHolder()
        Me.Move1 = New C1.Win.C1Command.C1Command()
        Me.Move2 = New C1.Win.C1Command.C1Command()
        Me.Move3 = New C1.Win.C1Command.C1Command()
        Me.Move4 = New C1.Win.C1Command.C1Command()
        Me.Control4 = New C1.Win.C1Command.C1Command()
        Me.ToolBar1 = New C1.Win.C1Command.C1ToolBar()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.MyButton2 = New CustomControl.MyButton()
        Me.MyButton1 = New CustomControl.MyButton()
        Me.T_Line3 = New System.Windows.Forms.Label()
        Me.T_Line1 = New System.Windows.Forms.Label()
        Me.ToolTip1 = New System.Windows.Forms.ToolTip(Me.components)
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel()
        Me.lbCodeTextBox1 = New CustomControl.MyTextBox()
        Me.lbNameMyTextBox1 = New CustomControl.MyTextBox()
        Me.FatherNameTextBox2 = New CustomControl.MyTextBox()
        Me.JcTextBox1 = New CustomControl.MyTextBox()
        CType(Me.C1Holder1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Panel1.SuspendLayout()
        Me.TableLayoutPanel1.SuspendLayout()
        Me.SuspendLayout()
        '
        'C1Holder1
        '
        Me.C1Holder1.Commands.Add(Me.Move1)
        Me.C1Holder1.Commands.Add(Me.Move2)
        Me.C1Holder1.Commands.Add(Me.Move3)
        Me.C1Holder1.Commands.Add(Me.Move4)
        Me.C1Holder1.Commands.Add(Me.Control4)
        Me.C1Holder1.Owner = Me
        Me.C1Holder1.VisualStyle = C1.Win.C1Command.VisualStyle.Office2010Blue
        '
        'Move1
        '
        Me.Move1.Image = CType(resources.GetObject("Move1.Image"), System.Drawing.Image)
        Me.Move1.Name = "Move1"
        Me.Move1.Shortcut = System.Windows.Forms.Shortcut.F2
        Me.Move1.ShortcutText = ""
        Me.Move1.Text = "最前"
        Me.Move1.ToolTipText = "移到最前记录"
        '
        'Move2
        '
        Me.Move2.Image = CType(resources.GetObject("Move2.Image"), System.Drawing.Image)
        Me.Move2.Name = "Move2"
        Me.Move2.Shortcut = System.Windows.Forms.Shortcut.F3
        Me.Move2.ShortcutText = ""
        Me.Move2.Text = "上移"
        Me.Move2.ToolTipText = "上移记录"
        '
        'Move3
        '
        Me.Move3.Image = CType(resources.GetObject("Move3.Image"), System.Drawing.Image)
        Me.Move3.Name = "Move3"
        Me.Move3.Shortcut = System.Windows.Forms.Shortcut.F4
        Me.Move3.ShortcutText = ""
        Me.Move3.Text = "下移"
        Me.Move3.ToolTipText = "下移记录"
        '
        'Move4
        '
        Me.Move4.Image = CType(resources.GetObject("Move4.Image"), System.Drawing.Image)
        Me.Move4.Name = "Move4"
        Me.Move4.Shortcut = System.Windows.Forms.Shortcut.F5
        Me.Move4.ShortcutText = ""
        Me.Move4.Text = "最后"
        Me.Move4.ToolTipText = "移至最后记录"
        '
        'Control4
        '
        Me.Control4.Name = "Control4"
        Me.Control4.ShortcutText = ""
        Me.Control4.Text = "New Command"
        '
        'ToolBar1
        '
        Me.ToolBar1.AccessibleName = "Tool Bar"
        Me.ToolBar1.BackColor = System.Drawing.Color.Transparent
        Me.ToolBar1.CommandHolder = Me.C1Holder1
        Me.ToolBar1.Location = New System.Drawing.Point(1, 3)
        Me.ToolBar1.MinButtonSize = 22
        Me.ToolBar1.Movable = False
        Me.ToolBar1.Name = "ToolBar1"
        Me.ToolBar1.Size = New System.Drawing.Size(22, 22)
        Me.ToolBar1.Text = "C1ToolBar2"
        Me.ToolBar1.VisualStyle = C1.Win.C1Command.VisualStyle.Custom
        Me.ToolBar1.VisualStyleBase = C1.Win.C1Command.VisualStyle.Classic
        '
        'Panel1
        '
        Me.Panel1.Controls.Add(Me.MyButton2)
        Me.Panel1.Controls.Add(Me.MyButton1)
        Me.Panel1.Controls.Add(Me.T_Line3)
        Me.Panel1.Controls.Add(Me.ToolBar1)
        Me.Panel1.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.Panel1.Location = New System.Drawing.Point(0, 76)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(454, 30)
        Me.Panel1.TabIndex = 0
        '
        'MyButton2
        '
        Me.MyButton2.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.MyButton2.DialogResult = System.Windows.Forms.DialogResult.None
        Me.MyButton2.Location = New System.Drawing.Point(361, 2)
        Me.MyButton2.Name = "MyButton2"
        Me.MyButton2.Size = New System.Drawing.Size(60, 25)
        Me.MyButton2.TabIndex = 106
        Me.MyButton2.Tag = "取消"
        Me.MyButton2.Text = "取消"
        '
        'MyButton1
        '
        Me.MyButton1.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.MyButton1.DialogResult = System.Windows.Forms.DialogResult.None
        Me.MyButton1.Location = New System.Drawing.Point(286, 2)
        Me.MyButton1.Name = "MyButton1"
        Me.MyButton1.Size = New System.Drawing.Size(60, 25)
        Me.MyButton1.TabIndex = 4
        Me.MyButton1.Tag = "保存"
        Me.MyButton1.Text = "保存"
        '
        'T_Line3
        '
        Me.T_Line3.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.T_Line3.Dock = System.Windows.Forms.DockStyle.Top
        Me.T_Line3.Location = New System.Drawing.Point(0, 0)
        Me.T_Line3.Name = "T_Line3"
        Me.T_Line3.Size = New System.Drawing.Size(454, 2)
        Me.T_Line3.TabIndex = 101
        Me.T_Line3.Text = "Label2"
        '
        'T_Line1
        '
        Me.T_Line1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.T_Line1.BackColor = System.Drawing.SystemColors.Control
        Me.T_Line1.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.TableLayoutPanel1.SetColumnSpan(Me.T_Line1, 5)
        Me.T_Line1.Location = New System.Drawing.Point(3, 34)
        Me.T_Line1.Name = "T_Line1"
        Me.T_Line1.Size = New System.Drawing.Size(448, 2)
        Me.T_Line1.TabIndex = 119
        Me.T_Line1.Text = "Label1"
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.ColumnCount = 5
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 60.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 160.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 60.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 160.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 5.0!))
        Me.TableLayoutPanel1.Controls.Add(Me.T_Line1, 0, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.lbCodeTextBox1, 0, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.lbNameMyTextBox1, 0, 3)
        Me.TableLayoutPanel1.Controls.Add(Me.FatherNameTextBox2, 2, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.JcTextBox1, 2, 3)
        Me.TableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Top
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(0, 0)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.RowCount = 4
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 3.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 8.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28.0!))
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(454, 72)
        Me.TableLayoutPanel1.TabIndex = 130
        '
        'lbCodeTextBox1
        '
        Me.lbCodeTextBox1.Captain = "类别编码"
        Me.lbCodeTextBox1.CaptainBackColor = System.Drawing.Color.Transparent
        Me.lbCodeTextBox1.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lbCodeTextBox1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.lbCodeTextBox1.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.lbCodeTextBox1, 2)
        Me.lbCodeTextBox1.ContentForeColor = System.Drawing.Color.Black
        Me.lbCodeTextBox1.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.lbCodeTextBox1.Location = New System.Drawing.Point(3, 6)
        Me.lbCodeTextBox1.Multiline = False
        Me.lbCodeTextBox1.Name = "lbCodeTextBox1"
        Me.lbCodeTextBox1.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.lbCodeTextBox1.ReadOnly = False
        Me.lbCodeTextBox1.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.lbCodeTextBox1.SelectionStart = 0
        Me.lbCodeTextBox1.SelectStart = 0
        Me.lbCodeTextBox1.Size = New System.Drawing.Size(214, 20)
        Me.lbCodeTextBox1.TabIndex = 128
        Me.lbCodeTextBox1.TabStop = False
        Me.lbCodeTextBox1.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lbCodeTextBox1.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        '
        'lbNameMyTextBox1
        '
        Me.lbNameMyTextBox1.Captain = "类别名称"
        Me.lbNameMyTextBox1.CaptainBackColor = System.Drawing.Color.Transparent
        Me.lbNameMyTextBox1.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lbNameMyTextBox1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.lbNameMyTextBox1.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.lbNameMyTextBox1, 2)
        Me.lbNameMyTextBox1.ContentForeColor = System.Drawing.Color.Black
        Me.lbNameMyTextBox1.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.lbNameMyTextBox1.Location = New System.Drawing.Point(3, 42)
        Me.lbNameMyTextBox1.Multiline = False
        Me.lbNameMyTextBox1.Name = "lbNameMyTextBox1"
        Me.lbNameMyTextBox1.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.lbNameMyTextBox1.ReadOnly = False
        Me.lbNameMyTextBox1.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.lbNameMyTextBox1.SelectionStart = 0
        Me.lbNameMyTextBox1.SelectStart = 0
        Me.lbNameMyTextBox1.Size = New System.Drawing.Size(214, 20)
        Me.lbNameMyTextBox1.TabIndex = 1
        Me.lbNameMyTextBox1.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lbNameMyTextBox1.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        '
        'FatherNameTextBox2
        '
        Me.FatherNameTextBox2.Captain = "父 类 别"
        Me.FatherNameTextBox2.CaptainBackColor = System.Drawing.Color.Transparent
        Me.FatherNameTextBox2.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.FatherNameTextBox2.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.FatherNameTextBox2.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.FatherNameTextBox2, 2)
        Me.FatherNameTextBox2.ContentForeColor = System.Drawing.Color.Black
        Me.FatherNameTextBox2.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.FatherNameTextBox2.Location = New System.Drawing.Point(223, 6)
        Me.FatherNameTextBox2.Multiline = False
        Me.FatherNameTextBox2.Name = "FatherNameTextBox2"
        Me.FatherNameTextBox2.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.FatherNameTextBox2.ReadOnly = False
        Me.FatherNameTextBox2.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.FatherNameTextBox2.SelectionStart = 0
        Me.FatherNameTextBox2.SelectStart = 0
        Me.FatherNameTextBox2.Size = New System.Drawing.Size(214, 20)
        Me.FatherNameTextBox2.TabIndex = 3
        Me.FatherNameTextBox2.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.FatherNameTextBox2.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        '
        'JcTextBox1
        '
        Me.JcTextBox1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.JcTextBox1.Captain = "列别简称"
        Me.JcTextBox1.CaptainBackColor = System.Drawing.Color.Transparent
        Me.JcTextBox1.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.JcTextBox1.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.JcTextBox1.CaptainWidth = 60.0!
        Me.TableLayoutPanel1.SetColumnSpan(Me.JcTextBox1, 2)
        Me.JcTextBox1.ContentForeColor = System.Drawing.Color.Black
        Me.JcTextBox1.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.JcTextBox1.Location = New System.Drawing.Point(223, 45)
        Me.JcTextBox1.Multiline = False
        Me.JcTextBox1.Name = "JcTextBox1"
        Me.JcTextBox1.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.JcTextBox1.ReadOnly = False
        Me.JcTextBox1.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.JcTextBox1.SelectionStart = 0
        Me.JcTextBox1.SelectStart = 0
        Me.JcTextBox1.Size = New System.Drawing.Size(214, 20)
        Me.JcTextBox1.TabIndex = 129
        Me.JcTextBox1.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.JcTextBox1.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        '
        'EmrMb2
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(454, 106)
        Me.Controls.Add(Me.TableLayoutPanel1)
        Me.Controls.Add(Me.Panel1)
        Me.Name = "EmrMb2"
        Me.Text = "模板类别"
        CType(Me.C1Holder1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Panel1.ResumeLayout(False)
        Me.TableLayoutPanel1.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents C1Holder1 As C1.Win.C1Command.C1CommandHolder
    Friend WithEvents Move1 As C1.Win.C1Command.C1Command
    Friend WithEvents Move2 As C1.Win.C1Command.C1Command
    Friend WithEvents Move3 As C1.Win.C1Command.C1Command
    Friend WithEvents Move4 As C1.Win.C1Command.C1Command
    Friend WithEvents ToolBar1 As C1.Win.C1Command.C1ToolBar
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents T_Line3 As System.Windows.Forms.Label
    Friend WithEvents T_Line1 As System.Windows.Forms.Label
    Friend WithEvents ToolTip1 As System.Windows.Forms.ToolTip
    Friend WithEvents TableLayoutPanel1 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents Control4 As C1.Win.C1Command.C1Command
    Friend WithEvents lbCodeTextBox1 As CustomControl.MyTextBox
    Friend WithEvents lbNameMyTextBox1 As CustomControl.MyTextBox
    Friend WithEvents FatherNameTextBox2 As CustomControl.MyTextBox
    Friend WithEvents MyButton2 As CustomControl.MyButton
    Friend WithEvents MyButton1 As CustomControl.MyButton
    Friend WithEvents JcTextBox1 As CustomControl.MyTextBox
End Class
