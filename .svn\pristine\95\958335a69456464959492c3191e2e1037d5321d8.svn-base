﻿Public Class BaseForm
    'Private Sub BaseForm_Activated(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Activated

    '    Me.Dock = DockStyle.Fill
    '    Me.FormBorderStyle = Windows.Forms.FormBorderStyle.Sizable


    'End Sub

    'Private Sub BaseForm_FormClosing(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosingEventArgs) Handles Me.FormClosing
    '    RemoveTap(Me.Name)
    'End Sub

    Public Overridable Sub F_Sum()

    End Sub

    Public Sub New()
        MyBase.KeyPreview = True
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。

    End Sub

    Protected Overrides Sub OnKeyDown(ByVal e As System.Windows.Forms.KeyEventArgs)
        MyBase.OnKeyDown(e)
        If e.KeyCode = Keys.Escape Then
            Me.Close()
        End If
    End Sub
End Class
