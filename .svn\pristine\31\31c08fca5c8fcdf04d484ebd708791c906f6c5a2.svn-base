﻿Imports System.Data.SqlClient
Imports HisControl
Imports BaseClass

Public Class YkYf_Rk3

#Region "定义变量"
    Dim P_Yp4_Insert As New SqlCommand           '存储__增加药品新的批号
    Dim My_DataSet As New DataSet
    Dim My_Cc As New BaseClass.C_Cc()                 '取最大编码及简称的类
    Dim My_Reader As SqlClient.SqlDataReader
    Dim Tmp_Cgj As Double
    Dim Tmp_Xsj As Double
    Dim Tmp_Pfj As Double
#End Region

#Region "传参"
    Dim Rform As BaseForm
    Dim Rinsert As Boolean
    Dim Rrow As DataRow
    Dim RZbtb As DataTable
    Dim Rtdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Dim Rcode As String
    Dim Rrc As C_RowChange
    Dim Form_Lb As String
#End Region

    Public Sub New(ByVal tform As BaseForm, ByVal tinsert As Boolean, ByVal trow As DataRow, ByVal tZbtb As DataTable, ByVal ttdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid, ByVal tcode As String, ByRef trc As C_RowChange, ByVal tformlb As String)
        '此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        Rform = tform
        Rinsert = tinsert
        Rrow = trow
        RZbtb = tZbtb
        Rtdbgrid = ttdbgrid
        Rcode = tcode
        Rrc = trc
        Form_Lb = tformlb
        '在 InitializeComponent() 调用之后添加任何初始化。
    End Sub

    Protected Overrides Sub OnKeyDown(ByVal e As System.Windows.Forms.KeyEventArgs)
        MyBase.OnKeyDown(e)
        If e.Control = True And e.KeyCode = Keys.S Then
            Comm1.Focus()
            Comm1.Select()
            Call Comm_Click(Comm1, Nothing)
        End If
    End Sub

    Private Sub Yk_Rk31_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        AddHandler Rrc.RowChanged, New BaseClass.RowChangedHandler(AddressOf Data_Show)
        Call Form_Init()
        Call P_Procedure()  '存储过程
        If Rinsert = True Then Call Data_Clear() Else Call Data_Show(Rrow)
    End Sub

#Region "窗体__事件"

    Private Sub Form_Init()
        '按扭初始化
        Call P_Comm(Me.Comm1)
        Call P_Comm(Me.Comm2)

        C1Combo3.CharacterCasing = CharacterCasing.Upper

        With Me.C1Numeric1
            .Value = 0
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .CustomFormat = "###,######0.####"
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .ShowContextMenu = False
            .EmptyAsNull = True
        End With

        With Me.C1Numeric2
            .Value = 0
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .CustomFormat = "#########0.00####"
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .ShowContextMenu = False
            .EmptyAsNull = True
        End With


        With Me.C1NumericEdit1
            .Value = 0
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .CustomFormat = "#########0.00####"
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .ShowContextMenu = False
            .EmptyAsNull = True
        End With
        With Me.C1NumericEdit2
            .Value = 0
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .CustomFormat = "#########0.00####"
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .ShowContextMenu = False
            .EmptyAsNull = True
        End With


        With Me.C1DateEdit1
            .Value = DBNull.Value
            .DateTimeInput = True     '显示日期的开关
            .AutoChangePosition = False
            .CaseSensitive = True
            .VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
            .InitialSelection = C1.Win.C1Input.InitialSelectionEnum.CaretAtStart
            '.VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.Modal
            .FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .EditFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .EditFormat.CustomFormat = "yyyy-MM-dd"
            .DisplayFormat.FormatType = C1.Win.C1Input.FormatTypeEnum.CustomFormat
            .DisplayFormat.CustomFormat = "yyyy-MM-dd"

            .MaskInfo.AutoTabWhenFilled = True
            .AcceptsTab = True
            .EmptyAsNull = True
            .ValueIsDbNull = True
            With .ErrorInfo
                .BeepOnError = True
                .CanLoseFocus = False
                .ErrorAction = C1.Win.C1Input.ErrorActionEnum.None
                .ErrorProvider = Me.ErrorProvider1
            End With
        End With

        With YpJcComobo1
            .DataView = DAL.DAL_Dict.GetYpDict.DefaultView

            .Init_Colum("Yp_Jc", "简称", 108, "左")
            .Init_Colum("Yp_Name", "名称", 183, "左")
            .Init_Colum("Jx_Name", "剂型", 60, "左")
            .Init_Colum("Mx_Gyzz", "批准文号", 70, "左")
            .Init_Colum("Mx_Gg", "规格", 70, "左")
            .Init_Colum("Mx_Cd", "产地", 75, "左")
            .Init_Colum("Jx_Code", "", 0, "中")
            If Form_Lb = "药库采购入库" Then
                .Init_Colum("Mx_Cgdw", "单位", 55, "左")
                .Init_Colum("Mx_Xsdw", "", 0, "左")
            ElseIf Form_Lb = "药房采购入库" Then
                .Init_Colum("Mx_Cgdw", "", 0, "左")
                .Init_Colum("Mx_Xsdw", "单位", 55, "左")
            End If
            .Init_Colum("Mx_Code", "编码", 80, "中")
            .Init_Colum("Mx_Cfbl", "拆分比", 0, "右")
            .Init_Colum("Dl_Code", "", 0, "中")
            .Init_Colum("Yp_Code", "", 0, "中")
            .Init_Colum("Dl_Name", "", 0, "中")
            .Init_Colum("IsJb", "基本药品", 80, "中")
            .DisplayMember = "Yp_Name"
            .ValueMember = "Mx_Code"
            .DroupDownWidth = 810
            .MaxDropDownItems = 15
            .SelectedIndex = -1
            .RowFilterNotTextNull = "Yp_Jc+Yp_Name+isnull(Mx_Gg,'')"
            .RowFilterTextNull = ""
        End With

    End Sub

    Private Sub P_Procedure()
        With P_Yp4_Insert
            .CommandType = CommandType.StoredProcedure
            .Connection = My_Cn
            .CommandText = "P_Zd_Ml_Yp4"
            With .Parameters
                .Add("@V_Yy_Code", SqlDbType.VarChar, 4)
                .Add("@V_Code", SqlDbType.VarChar, 50)
                .Add("@V_Date", SqlDbType.SmallDateTime)
                .Add("@V_Cgj", SqlDbType.Decimal)
                .Add("@V_Max", SqlDbType.VarChar, 50)
            End With
            .Parameters("@V_Max").Direction = ParameterDirection.Output
        End With
    End Sub

    Private Sub Yk_Rk3_FormClosed(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosedEventArgs) Handles Me.FormClosed
        RemoveHandler Rrc.RowChanged, New BaseClass.RowChangedHandler(AddressOf Data_Show)
        Me.My_DataSet.Dispose()
    End Sub

#End Region

#Region "其它项目"

    Private Sub C1Numeric_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles C1TextBox7.KeyPress, C1Numeric1.KeyPress, C1Numeric2.KeyPress, C1NumericEdit1.KeyPress, C1NumericEdit2.KeyPress, C1DateEdit1.KeyPress
        If e.KeyChar <> Chr(Keys.Return) Then Exit Sub
        e.Handled = True
        System.Windows.Forms.SendKeys.Send("{Tab}")
    End Sub

    Private Sub C1Numeric1_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1Numeric1.Validated, C1Numeric2.Validated, C1NumericEdit1.Validated, C1NumericEdit2.Validated
        If C1Numeric1.Value Is DBNull.Value Then C1Numeric1.Value = 0
        If C1Numeric2.Value Is DBNull.Value Then C1Numeric2.Value = 0
        If C1NumericEdit1.Value Is DBNull.Value Then C1NumericEdit1.Value = 0
        Label15.Text = Format(C1Numeric1.Value * C1Numeric2.Value, "0.00####")
        Label18.Text = Format(C1Numeric1.Value * C1NumericEdit1.Value, "0.00####")
        Label19.Text = Format(C1Numeric1.Value * C1NumericEdit2.Value, "0.00####")
    End Sub

#End Region

#Region "控件__动作"

    Private Sub Comm_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.Click, Comm2.Click
        Select Case sender.tag
            Case "保存"

                If YpJcComobo1.SelectedValue = "" Then
                    Beep()
                    MsgBox("药品不能为空!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    YpJcComobo1.Select()
                    Exit Sub
                End If

                If Trim(C1Combo3.Text & "") = "" Then
                    Beep()
                    MsgBox("药品批号不能为空!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    C1Combo3.Select()
                    Exit Sub
                End If

                If Trim(C1DateEdit1.Text) = "" Then
                    Beep()
                    MsgBox("药品有效期输入有误!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    C1DateEdit1.Select()
                    Exit Sub
                ElseIf C1DateEdit1.Text < Now Then
                    Beep()
                    MsgBox("药品有效期输入有误!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    C1DateEdit1.Select()
                    Exit Sub
                End If

                If C1Numeric1.Value < 0 Then
                    Beep()
                    MsgBox("数量不能为负数!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    C1Numeric1.Select()
                    Exit Sub
                End If

                If C1Numeric2.Text = "" Or C1Numeric2.Value < 0 Then
                    Beep()
                    MsgBox("采购单价有误!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    C1Numeric2.Select()
                    Exit Sub
                End If

                If C1NumericEdit1.Text = "" Or C1NumericEdit1.Value < 0 Or C1NumericEdit1.Value < C1Numeric2.Value Then
                    Beep()
                    MsgBox("销售单价有误!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    C1NumericEdit1.Select()
                    Exit Sub
                End If
                If C1NumericEdit2.Text = "" Or C1NumericEdit2.Value < 0 Or C1NumericEdit2.Value < C1Numeric2.Value Then
                    Beep()
                    MsgBox("批发价有误!", MsgBoxStyle.OkOnly + MsgBoxStyle.Exclamation, "提示:")
                    C1NumericEdit2.Select()
                    Exit Sub
                End If


                Call Save_Add()
                Rform.F_Sum()
            Case "取消"
                Rtdbgrid.Focus()
                Me.Close()
        End Select
    End Sub

    Private Sub C1Move_Click(ByVal sender As Object, ByVal e As C1.Win.C1Command.ClickEventArgs) Handles Move2.Click, Move3.Click, Move5.Click
        If sender.text = "新增" Then
            Rinsert = True
            Call Data_Clear()
        Else
            Rinsert = False
            Select Case sender.text
                Case "上移"
                    Rtdbgrid.MovePrevious()
                Case "下移"
                    Rtdbgrid.MoveNext()
            End Select
        End If

    End Sub

#Region "YpJcComobo1"

    Private Sub YpJcComobo1_RowChange(ByVal sender As Object, ByVal e As System.EventArgs) Handles YpJcComobo1.RowChange
        If YpJcComobo1.Text = "" Then

        Else
            If YpJcComobo1.WillChangeToValue = "" Then
            Else
                Label24.Text = YpJcComobo1.Columns("Mx_Gyzz").Text
                Label25.Text = YpJcComobo1.Columns("Mx_Gg").Text
                Label26.Text = YpJcComobo1.Columns("Mx_Cd").Text
                Label5.Text = YpJcComobo1.Columns("Jx_Name").Text
                If Form_Lb = "药库采购入库" Then
                    Label23.Text = YpJcComobo1.Columns("Mx_Cgdw").Text
                    Label08.Text = "/" & YpJcComobo1.Columns("Mx_Cgdw").Value
                    Label09.Text = "/" & YpJcComobo1.Columns("Mx_Cgdw").Value
                    Label1.Text = "/" & YpJcComobo1.Columns("Mx_Cgdw").Value
                    Label22.Text = Format(C1Numeric1.Value * YpJcComobo1.Columns("Mx_Cfbl").Value, "0.#####") & " " & YpJcComobo1.Columns("Mx_Xsdw").Value
                ElseIf Form_Lb = "药房采购入库" Then
                    Label23.Text = YpJcComobo1.Columns("Mx_Xsdw").Text
                    Label08.Text = "/" & YpJcComobo1.Columns("Mx_Xsdw").Value
                    Label09.Text = "/" & YpJcComobo1.Columns("Mx_Xsdw").Value
                    Label1.Text = "/" & YpJcComobo1.Columns("Mx_Xsdw").Value
                    Label22.Text = Format(C1Numeric1.Value / YpJcComobo1.Columns("Mx_Cfbl").Value, "0.#####") & " " & YpJcComobo1.Columns("Mx_CgDw").Value
                End If
                Label4.Text = Format(YpJcComobo1.Columns("Mx_Cfbl").Value, "0.######")
                '药品产品批号实例化

                If Form_Lb = "药库采购入库" Then
                    HisVar.HisVar.Sqldal.QueryDt(My_DataSet, "Select Xx_Code,Yp_Ph,Yp_Yxq,Yk_Cgj,Yk_Pfj,Yk_Xsj From Zd_Ml_Yp4 Where Mx_Code='" & YpJcComobo1.SelectedValue & "' And Yy_Code='" & HisVar.HisVar.WsyCode & "' and Yp_Yxq>Getdate() Order By Yp_Ph", "产品批号字典", True)
                ElseIf Form_Lb = "药房采购入库" Then
                    HisVar.HisVar.Sqldal.QueryDt(My_DataSet, "Select Xx_Code,Yp_Ph,Yp_Yxq,Yk_Cgj/Mx_Cfbl as Yk_Cgj,Yk_Pfj/Mx_Cfbl as Yk_Pfj,Yk_Xsj/Mx_Cfbl as Yk_Xsj From V_YpKc Where Mx_Code='" & YpJcComobo1.SelectedValue & "' And Yy_Code='" & HisVar.HisVar.WsyCode & "' and Yp_Yxq>Getdate() Order By Yp_Ph", "产品批号字典", True)
                End If

                My_DataSet.Tables("产品批号字典").PrimaryKey = New DataColumn() {My_DataSet.Tables("产品批号字典").Columns("Xx_Code")}

                Dim My_Combo1 As BaseClass.C_Combo2 = New BaseClass.C_Combo2(C1Combo3, My_DataSet.Tables("产品批号字典").DefaultView, "Yp_Ph", "Xx_Code", 420)
                With My_Combo1
                    .Init_TDBCombo()
                    .Init_Colum("Xx_Code", "编码", 0, "左")
                    .Init_Colum("Yp_Ph", "产品批号", 90, "左")
                    .Init_Colum("Yp_Yxq", "有效期", 80, "中")
                    .Init_Colum("Yk_Cgj", "采购价", 80, "中")
                    .Init_Colum("Yk_Pfj", "批发价", 80, "中")
                    .Init_Colum("Yk_Xsj", "销售价", 80, "中")
                    .MaxDropDownItems(15)
                    .SelectedIndex(-1)
                End With
                C1Combo3.Columns("Yp_Yxq").NumberFormat = "yyyy-MM-dd"
                C1Combo3.Columns("Yk_Cgj").NumberFormat = "0.00####"
                C1Combo3.Columns("Yk_Pfj").NumberFormat = "0.00####"
                C1Combo3.Columns("Yk_Xsj").NumberFormat = "0.00####"

                '取药品上次入库价格

                If Form_Lb = "药库采购入库" Then
                    My_Reader = HisVar.HisVar.Sqldal.ExecuteReader("select top 1 Rk_Dj,Rk_Xsj,Rk_Pfj from Yk_RK2,V_Ypkc where V_Ypkc.Xx_Code=Yk_RK2.Xx_Code And Mx_Code='" & YpJcComobo1.SelectedValue & "' and Yk_Rk2.Yy_Code=V_Ypkc.Yy_Code And Yk_Rk2.Yy_Code='" & HisVar.HisVar.WsyCode & "' order by Rk_ID desc")
                ElseIf Form_Lb = "药房采购入库" Then
                    My_Reader = HisVar.HisVar.Sqldal.ExecuteReader("select top 1 Rk_Dj,Rk_Xsj,Rk_Pfj,Rk_Id from Yf_Rk2 where Left(Xx_Code,11)='" & YpJcComobo1.SelectedValue & "' and  Yf_Rk2.Yy_Code='" & HisVar.HisVar.WsyCode & "'  order by Rk_ID desc")
                End If

                My_Reader.Read()
                If My_Reader.HasRows = True Then
                    C1Numeric2.Value = My_Reader("Rk_Dj")
                    C1NumericEdit1.Value = My_Reader("Rk_Xsj")
                    C1NumericEdit2.Value = My_Reader("Rk_Pfj")
                Else
                    C1Numeric2.Value = 0
                    C1NumericEdit1.Value = 0
                    C1NumericEdit2.Value = 0
                End If
                Tmp_Cgj = C1Numeric2.Value
                Tmp_Xsj = C1NumericEdit1.Value
                Tmp_Pfj = C1NumericEdit2.Value
                My_Reader.Close()

            End If
        End If
    End Sub

#End Region

#Region "C1Combo3"


    Private Sub C1Combo3_RowChange(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1Combo3.RowChange

        If C1Combo3.WillChangeToValue = "" Then
            C1DateEdit1.Value = ""
            C1Numeric2.Value = Tmp_Cgj
            C1NumericEdit1.Value = Tmp_Xsj
            C1NumericEdit2.Value = Tmp_Pfj
        Else
            C1DateEdit1.Value = C1Combo3.Columns("Yp_Yxq").Value & ""
            C1Numeric2.Value = C1Combo3.Columns("Yk_Cgj").Value
            C1NumericEdit1.Value = C1Combo3.Columns("Yk_Xsj").Value
            C1NumericEdit2.Value = C1Combo3.Columns("Yk_Pfj").Value
        End If
    End Sub

    Private Sub C1Combo3_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles C1Combo3.KeyPress
        Select Case e.KeyChar
            Case Chr(Keys.Enter)
                e.Handled = True
                Call C1Combo3_Validating("Enter", Nothing)
            Case Chr(Keys.Escape)
                e.Handled = True
                Call C1Combo3_Validating("Escape", Nothing)
        End Select
    End Sub

    Private Sub C1Combo3_Validating(ByVal sender As Object, ByVal e As System.ComponentModel.CancelEventArgs) Handles C1Combo3.Validating
        Dim V_Code As String = Me.C1Combo3.WillChangeToValue

        Select Case sender.ToString
            Case "Enter"
                If V_Code = "" And Me.C1Combo3.Text <> "" Then                  '编码不存在
                    P_Scph_State(True)
                    C1DateEdit1.Select()
                Else                                                            '编码存在
                    P_Scph_State(False)
                    C1Numeric1.Select()
                End If
        End Select
    End Sub

#End Region


#End Region

#Region "数据__编辑"

    Private Sub Data_Clear()
        Rinsert = True
        If Move5.Enabled = True Then Move5.Enabled = False
        YpJcComobo1.Enabled = True

        YpJcComobo1.SelectedValue = -1
        YpJcComobo1.Text = ""                                      '药品明细编码
        C1Combo3.Text = ""                                    '批准文号

        Label24.Text = ""
        Label26.Text = ""                                    '生产厂家
        Label25.Text = ""                                    '产品规格
        Label23.Text = ""                                    '入库单位

        C1Combo3.SelectedValue = -1


        C1TextBox7.Text = ""                                    '备注
        C1Numeric1.Value = 0                                    '采购数量
        C1Numeric2.Value = 0                                    '采购单价
        Label15.Text = 0                                    '采购金额
        Label22.Text = ""                                   '拆分比例
        C1NumericEdit1.Value = 0
        C1NumericEdit2.Value = 0
        Label18.Text = 0

        C1DateEdit1.Value = ""

        C1Combo3.ReadOnly = False
        C1Combo3.EditorBackColor = SystemColors.Window
        C1Numeric2.ReadOnly = False
        C1Numeric2.BackColor = SystemColors.Window
        C1NumericEdit1.ReadOnly = False
        C1NumericEdit1.BackColor = SystemColors.Window
        C1NumericEdit2.ReadOnly = False
        C1NumericEdit2.BackColor = SystemColors.Window

        Label5.Text = ""

        P_Scph_State(True)

        YpJcComobo1.Select()
    End Sub

    Private Sub Data_Show(ByVal tmp_Row As DataRow)
        Rinsert = False
        YpJcComobo1.DataSource.RowFilter = ""
        If Rtdbgrid.RowCount = 0 Then Exit Sub
        If Rtdbgrid.Row >= Rtdbgrid.RowCount Then Exit Sub

        If Move5.Enabled = False Then Move5.Enabled = True
        T_Label2.Text = CStr((Rtdbgrid.Row) + 1)
        Rrow = tmp_Row
        Call P_Scph_State(False)
        C1Combo3.ReadOnly = True
        C1Combo3.EditorBackColor = SystemColors.Info
        C1Numeric2.ReadOnly = True
        C1Numeric2.BackColor = SystemColors.Info
        C1NumericEdit1.ReadOnly = True
        C1NumericEdit1.BackColor = SystemColors.Info
        C1NumericEdit2.ReadOnly = True
        C1NumericEdit2.BackColor = SystemColors.Info

        With Rrow
            YpJcComobo1.Enabled = False                            '药品名称
            YpJcComobo1.SelectedValue = .Item("Mx_Code") & ""
            C1Combo3.SelectedValue = .Item("Xx_Code") & ""
            C1TextBox7.Text = .Item("Rk_Memo") & ""             '备注
            C1Numeric1.Value = .Item("Rk_Sl")                   '采购数量
            C1Numeric2.Value = .Item("Rk_Dj") & ""
            Label15.Text = Format(.Item("Rk_Money"), "0.00####")                '采购金额
            Label18.Text = Format(.Item("Rk_Xsj") * .Item("Rk_Sl"), "0.00####")
            Label19.Text = Format(.Item("Rk_Pfj") * .Item("Rk_Sl"), "0.00####")
            C1NumericEdit1.Value = .Item("Rk_Xsj")
            C1NumericEdit2.Value = .Item("Rk_Pfj")
        End With


        C1Numeric1.Select()

    End Sub

    Private Sub Save_Add()

        Dim My_NewRow As DataRow
        If Rinsert = True Then
            My_NewRow = RZbtb.NewRow
        Else
            My_NewRow = Rrow
        End If

        With My_NewRow
            .BeginEdit()
            .Item("Yy_Code") = HisVar.HisVar.WsyCode
            .Item("Rk_Code") = Rcode                '入库编码
            If Rinsert = True Then      '增加记录
                .Item("Xx_Code") = F_Xx_Code()
            Else
                .Item("Xx_Code") = My_NewRow.Item("Xx_Code")
            End If
            .Item("Rk_Sl") = C1Numeric1.Value                   '采购单价
            .Item("Rk_Dj") = C1Numeric2.Value                   '采购单价
            .Item("Rk_Xsj") = C1NumericEdit1.Value
            .Item("Rk_Pfj") = C1NumericEdit2.Value
            .Item("Rk_Money") = C1Numeric1.Value * C1Numeric2.Value            '采购金额
            .Item("Rk_XsMoney") = C1NumericEdit1.Value * C1Numeric1.Value
            .Item("Rk_Memo") = C1TextBox7.Text & ""             '备注
            .Item("Dl_Code") = YpJcComobo1.Columns("Dl_Code").Value
            .Item("Yp_Ph") = C1Combo3.Text
            .Item("Yp_Yxq") = Format(C1DateEdit1.Value, "yyyy-MM-dd")
            .Item("Mx_Code") = YpJcComobo1.SelectedValue
            .Item("Jx_Name") = Label5.Text
            .Item("Yp_Name") = YpJcComobo1.Text & ""
            .Item("Mx_Gg") = Label25.Text & ""               '药品规格
            .Item("Mx_Gyzz") = Label24.Text.ToUpper & ""     '批准文号
            .Item("Mx_Cd") = Label26.Text & ""             '生产单位
            .Item("Mx_CgDw") = Label23.Text & ""             '销售单位
            .Item("Mx_XsDw") = YpJcComobo1.Columns("Mx_XsDw").Value & ""
            .Item("Mx_Cfbl") = YpJcComobo1.Columns("Mx_CfBl").Value & ""
            .Item("IsJb") = YpJcComobo1.Columns("IsJb").Value & ""
            .EndEdit()
        End With

        Dim Para(8) As SqlClient.SqlParameter
        Para(0) = New SqlParameter("@Yy_Code", SqlDbType.Char)
        Para(1) = New SqlParameter("@Rk_Sl", SqlDbType.Decimal)
        Para(2) = New SqlParameter("@Rk_Dj", SqlDbType.Decimal)
        Para(3) = New SqlParameter("@Rk_Xsj", SqlDbType.Decimal)
        Para(4) = New SqlParameter("@Rk_Pfj", SqlDbType.Decimal)
        Para(5) = New SqlParameter("@Rk_Money", SqlDbType.Decimal)
        Para(6) = New SqlParameter("@Rk_Memo", SqlDbType.VarChar)
        Para(7) = New SqlParameter("@Rk_Code", SqlDbType.Char)
        Para(8) = New SqlParameter("@Xx_Code", SqlDbType.VarChar)

        Para(0).Value = HisVar.HisVar.WsyCode
        Para(1).Value = My_NewRow.Item("Rk_Sl")
        Para(2).Value = My_NewRow.Item("Rk_Dj")
        Para(3).Value = My_NewRow.Item("Rk_Xsj")
        Para(4).Value = My_NewRow.Item("Rk_Pfj")
        Para(5).Value = My_NewRow.Item("Rk_Money")
        Para(6).Value = My_NewRow.Item("Rk_Memo")
        Para(7).Value = Rcode
        Para(8).Value = My_NewRow.Item("Xx_Code") & ""

        Try
            If Rinsert = True Then
                If Form_Lb = "药库采购入库" Then
                    HisVar.HisVar.Sqldal.ExecuteSql("Insert Into Yk_Rk2(Yy_Code,Rk_Sl,Rk_Dj,Rk_Xsj,Rk_Pfj,Rk_Money,Rk_Memo,Rk_Code,Xx_Code)Values(@Yy_Code,@Rk_Sl,@Rk_Dj,@Rk_Xsj,@Rk_Pfj,@Rk_Money,@Rk_Memo,@Rk_Code,@Xx_Code)", Para)
                ElseIf Form_Lb = "药房采购入库" Then
                    HisVar.HisVar.Sqldal.ExecuteSql("Insert Into Yf_Rk2(Yy_Code,Rk_Sl,Rk_Dj,Rk_Xsj,Rk_Pfj,Rk_Money,Rk_Memo,Rk_Code,Xx_Code)Values(@Yy_Code,@Rk_Sl,@Rk_Dj,@Rk_Xsj,@Rk_Pfj,@Rk_Money,@Rk_Memo,@Rk_Code,@Xx_Code)", Para)
                End If
                HisVar.HisVar.Sqldal.ExecuteSql("Update Zd_Ml_Yp4 Set Yp_Ph='" & My_NewRow.Item("Yp_Ph") & "',Yp_Scrq='1900-01-01' Where Xx_Code='" & My_NewRow.Item("Xx_Code") & "' And Yy_Code='" & HisVar.HisVar.WsyCode & "'")
                RZbtb.Rows.Add(My_NewRow)
                Rtdbgrid.MoveLast()

                Call Data_Clear()
            Else
                If Form_Lb = "药库采购入库" Then
                    HisVar.HisVar.Sqldal.ExecuteSql("Update Yk_Rk2 Set Yy_Code=@Yy_Code,Rk_Sl=@Rk_Sl,Rk_Dj=@Rk_Dj,Rk_Xsj=@Rk_Xsj,Rk_Pfj=@Rk_Pfj,Rk_Money=@Rk_Money,Rk_Memo=@Rk_Memo Where Rk_Code=@Rk_Code and Xx_Code=@Xx_Code", Para)
                ElseIf Form_Lb = "药房采购入库" Then
                    HisVar.HisVar.Sqldal.ExecuteSql("Update Yf_Rk2 Set Yy_Code=@Yy_Code,Rk_Sl=@Rk_Sl,Rk_Dj=@Rk_Dj,Rk_Xsj=@Rk_Xsj,Rk_Pfj=@Rk_Pfj,Rk_Money=@Rk_Money,Rk_Memo=@Rk_Memo Where Rk_Code=@Rk_Code and Xx_Code=@Xx_Code", Para)
                End If

                MsgBox("修改成功", MsgBoxStyle.Information, "提示")
                Me.Close()
            End If
            My_NewRow.AcceptChanges()
        Catch ex As Exception
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
        End Try

    End Sub


#End Region

#Region "鼠标__外观"

    Private Sub P_Comm(ByVal Bt As Button)

        With Bt
            Select Case .Tag
                Case "保存"
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")

                Case "取消"
                    .Location = New Point(Comm1.Left + Comm1.Width + 1, Comm1.Top)
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
                    .Width = Me.Comm1.Width
            End Select

            .Text = ""
            .FlatStyle = FlatStyle.Flat
            .FlatAppearance.BorderSize = 0
            .BackgroundImageLayout = ImageLayout.Center
        End With

    End Sub

    Private Sub Comm_MouseEnter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.MouseEnter, Comm2.MouseEnter
        Select Case sender.tag
            Case "保存"
                Me.Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存2")
                Me.Comm1.Cursor = Cursors.Hand

            Case "取消"
                Me.Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消2")
                Me.Comm2.Cursor = Cursors.Hand

        End Select

    End Sub

    Private Sub Comm_MouseLeave(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.MouseLeave, Comm2.MouseLeave
        Select Case sender.tag

            Case "保存"
                Me.Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")
                Me.Comm1.Cursor = Cursors.Default

            Case "取消"
                Me.Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
                Me.Comm2.Cursor = Cursors.Default

        End Select

    End Sub

    Private Sub Comm_MouseDown(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm1.MouseDown, Comm2.MouseDown
        Select Case sender.tag
            Case "保存"
                Me.Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存3")

            Case "取消"
                Me.Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消3")
        End Select
    End Sub

    Private Sub Comm_MouseUp(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm1.MouseUp, Comm2.MouseUp
        Select Case sender.tag
            Case "保存"
                Me.Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存2")

            Case "取消"
                Me.Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消2")
        End Select
    End Sub


#End Region

#Region "自定义函数"

    Private Function F_Xx_Code() As String

        Call P_Conn(True)
        With P_Yp4_Insert
            .Parameters("@V_Yy_Code").Value = HisVar.HisVar.WsyCode
            .Parameters("@V_Code").Value = YpJcComobo1.SelectedValue
            .Parameters("@V_Date").Value = Format(C1DateEdit1.Value, "yyyy-MM-dd")
            If Form_Lb = "药库采购入库" Then
                .Parameters("@V_Cgj").Value = C1Numeric2.Value
            ElseIf Form_Lb = "药房采购入库" Then
                .Parameters("@V_Cgj").Value = C1Numeric2.Value * YpJcComobo1.Columns("Mx_Cfbl").Value    '换成药库采购价
            End If

            .ExecuteNonQuery()
            F_Xx_Code = .Parameters("@V_Max").Value
        End With
        Call P_Conn(False)
        Return F_Xx_Code
    End Function

    Private Sub P_Scph_State(ByVal V_NewPh As Boolean)

        If V_NewPh = False Then

            C1DateEdit1.BackColor = SystemColors.Info
            C1DateEdit1.ReadOnly = True
            C1DateEdit1.TabStop = False
        Else
            C1DateEdit1.BackColor = SystemColors.Window
            C1DateEdit1.ReadOnly = False
            C1DateEdit1.TabStop = True

        End If
    End Sub

#End Region

#Region "输入法设置"
    '中文
    Private Sub C1TextBox1_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1TextBox7.GotFocus
        InputLanguage.CurrentInputLanguage = HisVar.HisVar.InputCode
    End Sub

    Private Sub C1Numeric1_KeyDown(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Combo3.GotFocus, C1NumericEdit2.GotFocus, C1NumericEdit1.GotFocus, C1Numeric2.GotFocus, C1Numeric1.GotFocus, YpJcComobo1.GotFocus
        InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages(0)
    End Sub



#End Region


    Private Sub C1Numeric2_ValueChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1Numeric2.ValueChanged
        If (ZTHisPara.PublicConfig.CgjJsXsjAndPfj) Then
            C1NumericEdit1.Value = C1Numeric2.Value * ZTHisPara.PublicConfig.CgjXsjRatio
            C1NumericEdit2.Value = C1Numeric2.Value * ZTHisPara.PublicConfig.CgjPfjRatio
        End If
    End Sub

    Private Sub C1Numeric1_ValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles C1Numeric1.ValueChanged, YpJcComobo1.GotFocus

        If YpJcComobo1.SelectedValue = 0 Then Exit Sub
        If Form_Lb = "药库采购入库" Then
            Label22.Text = Format(C1Numeric1.Value * YpJcComobo1.Columns("Mx_Cfbl").Value, "0.######") & YpJcComobo1.Columns("Mx_Xsdw").Value
        ElseIf Form_Lb = "药房采购入库" Then
            Label22.Text = Format(C1Numeric1.Value / YpJcComobo1.Columns("Mx_Cfbl").Value, "0.######") & YpJcComobo1.Columns("Mx_Cgdw").Value
        End If

    End Sub


End Class