﻿Imports System.Data.SqlClient
Public Class Zy_Dzbl2
    Dim m_lb As String
    Dim m_Bl_Row As DataRow
    Dim m_Mb_Row As DataRow
    Dim mbid As Integer
    'Dim Rform As HisControl.BaseForm
    Dim UseMb_Tab As DataTable
    Dim NoUseMb_Tab As DataTable
    Public Sub New(ByVal tform As HisControl.BaseForm, ByVal lb As String, ByVal Bl_Row As DataRow, ByVal Mb_Row As DataRow, ByVal t_UseMbTab As DataTable, ByVal t_NoUseMbTab As DataTable)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        m_lb = lb
        m_Bl_Row = Bl_Row
        m_Mb_Row = Mb_Row
        'Rform = tform
        UseMb_Tab = t_UseMbTab
        NoUseMb_Tab = t_NoUseMbTab
        ' 在 InitializeComponent() 调用之后添加任何初始化。
    End Sub

    Private Sub Zy_Dzbl2_FormClosing(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosingEventArgs) Handles Me.FormClosing
        'Zy_Dzbl1.str_sql = ""
        'Rform.Visible = True
    End Sub


    Private Sub Zy_Dzbl2_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load

        'Rform.Visible = False
        Dim lbytContents() As Byte

        nsOff.Visible = False       '调整office的菜单、工具栏的显示/隐藏时，会让用户看到界面的闪烁，因此先隐藏office界面，处理完后再显示出来

        ''插入新按钮时，先判断扩展工具栏上是否已经存在相关按钮了
        'If Not nsOff.IsToolbarItemOn(1) Then  '判断第1个按钮是否已经插入了
        '    '在addonbar上面插入带图标的按钮时, 需要重启office才能生效, 因此先手动关闭office, 再插入
        '    If nsOff.HasSofficeBin() Then
        '        nsOff.KillSofficeBin()
        '        '关闭office需要一定时间，所以要进行延时等待
        '        System.Threading.Thread.Sleep(2000)
        '    End If
        '    nsOff.DeleteOfficeAppData()     '清除office的缓存数据，如果不清除，插入操作有可能失败
        '    nsOff.RemoveToolbarItemAll(1)
        '    nsOff.InsertToolbarItem(System.Windows.Forms.Application.StartupPath & "\img\保存.png", "1", "保存病历")
        '    nsOff.InsertToolbarItem(System.Windows.Forms.Application.StartupPath & "\img\打印.png", "2", "打印模版")
        '    'nsOff.InsertToolbarItem(System.Windows.Forms.Application.StartupPath & "\tool_copye.png", "2", "test")
        '    ''插入一个不带图标的按钮（如果插入的所有按钮都不带图标，可以不用重启office）
        '    'If nsOff.IsToolbarItemOn(11) Then nsOff.RemoveToolbarItem(11)
        '    'nsOff.InsertToolbarItem("", "11", "office版本")
        '    'If nsOff.IsToolbarItemOn(12) Then nsOff.RemoveToolbarItem(12)
        '    'nsOff.InsertToolbarItem("", "12", "控件版本")
        '    ''重新组织第二个工具栏addonbar2的按钮，要先清除该工具栏上所有按钮，再进行插入，这个操作需要重启office才能生效
        '    'nsOff.RemoveToolbarItemAll(2)
        '    'nsOff.InsertToolbarItem(System.Windows.Forms.Application.StartupPath & "\button2.png", "21", "按钮21")
        '    'nsOff.InsertToolbarItem(System.Windows.Forms.Application.StartupPath & "\button2.png", "22", "按钮22")
        'End If

        '如果office非正常退出，将不会释放监听器，因此先手工释放，确保监听器不会重复打开
        nsOff.RemoveAllListener()

       

        Dim My_Reader As SqlDataReader
        If m_lb = "新建" Then
            lbytContents = HisVar.HisVar.Sqldal.GetSingle("Select Mb_Nr from Zd_DzblMb where Mb_Id='" & m_Mb_Row("Mb_Id") & "'")
            nsOff.OpenDocumentWithStream(lbytContents, 1)

            Dim Mb_Name As String = HisVar.HisVar.Sqldal.GetSingle("Select Mb_Name from Zd_DzblMb where Mb_Id='" & m_Mb_Row("Mb_Id") & "'")
            If Mb_Name.Trim = "长期医嘱单" Then
                FillTableCell("新建")
            End If

        Else
            mbid = m_Mb_Row("Id")
            lbytContents = HisVar.HisVar.Sqldal.GetSingle("Select Mb_Nr from Bl_Mb where Bl_Code='" & m_Bl_Row.Item("Bl_Code") & "' and Mb_Id='" & m_Mb_Row("Mb_Id") & "'")
            nsOff.OpenDocumentWithStream(lbytContents, 1)

            Dim Mb_Name As String = HisVar.HisVar.Sqldal.GetSingle("Select Mb_Name from Bl_Mb where Bl_Code='" & m_Bl_Row.Item("Bl_Code") & "' and Mb_Id='" & m_Mb_Row("Mb_Id") & "'")
            If Mb_Name.Trim = "长期医嘱单" Then
                '从新填充表格内容
                FillTableCell("修改")
            End If

            If HisVar.HisVar.JsrYsCode = m_Mb_Row("Ys_Code") Then '自己修改自己的
                If m_Mb_Row("IsLock") = True Then
                    nsOff.BrowseTemplet(10, 1)
                Else
                    nsOff.BrowseTemplet(0, 0)
                End If
            Else
                Dim m_ks_Code As String = HisVar.HisVar.Sqldal.GetSingle("Select Ks_Code From Zd_Yyys where Ys_Code='" & m_Mb_Row("Ys_Code") & "'")

                If HisPara.PublicConfig.DzblCkXz = 1 Then '允许医生查看本科室其他医生的病历
                    If HisVar.HisVar.XmKs = m_ks_Code Then
                        If m_Mb_Row("IsLock") = True Then
                            nsOff.BrowseTemplet(10, 1)
                        Else
                            nsOff.BrowseTemplet(0, 0)
                            nsOff.ShowRecension(1)
                            nsOff.SetRecensionInfo(HisVar.HisVar.JsrYsCode, HisVar.HisVar.JsrYsName, "", 1, "红色")
                            nsOff.SwitchRecension(True)
                            nsOff.EnableRedlineReview(True)
                        End If
                        Exit Sub
                    Else
                        MsgBox("不允许跨权限查看病历！", MsgBoxStyle.Information, "提示")
                        Exit Sub
                    End If
                ElseIf HisPara.PublicConfig.DzblCkXz = 0 Then '允许医生查看其他科室医生的病历
                    If m_Mb_Row("IsLock") = True Then
                        nsOff.BrowseTemplet(10, 1)
                    Else
                        nsOff.BrowseTemplet(0, 0)
                        nsOff.ShowRecension(1)
                        nsOff.SetRecensionInfo(HisVar.HisVar.JsrYsCode, HisVar.HisVar.JsrYsName, "", 1, "红色")
                        nsOff.SwitchRecension(True)
                        nsOff.EnableRedlineReview(True)
                    End If
                    Exit Sub
                ElseIf HisPara.PublicConfig.DzblCkXz = 2 Then
                    MsgBox("不允许跨权限查看病历！", MsgBoxStyle.Information, "提示")
                    Exit Sub
                End If

            End If


        End If



        nsOff.LockControlLock(True)
        '寻找文档中还没有赋值的系统预定义病历元素
        Dim lstrName As String, lstrID() As String, lstrValue As String, lstrBasicID As String
        Dim i As Integer
        lstrName = nsOff.GetAllControlNameByCurrentDoc()
        If lstrName <> "" Then
            lstrID = Split(lstrName, ",")

            For i = LBound(lstrID) To UBound(lstrID) - 1
                '获取指定的病历元素信息
                lstrValue = nsOff.GetNewControlProp(lstrID(i), "Reserve") '
                lstrBasicID = lstrValue
                My_Reader = HisVar.HisVar.Sqldal.ExecuteReader("Select * from Zd_Reserve where Id='" & lstrBasicID & "'")
                While My_Reader.Read
                    nsOff.SetNewControlText(lstrID(i), m_Bl_Row(My_Reader("Res_Datafield")) & "")
                End While
                My_Reader.Close()
            Next
        End If



        nsOff.LockControlLock(False)


        nsOff.SetMenuBarVisible(True)   '显示菜单栏
        ''隐藏不允许使用的菜单

        'nsOff.HideMenuItem("newDoc")
        'nsOff.HideMenuItem("Open")
        'nsOff.HideMenuItem("CloseDoc")
        'nsOff.HideMenuItem("Save")
        'nsOff.HideMenuItem("SaveAs")
        'nsOff.HideMenuItem("SaveAll")
        'nsOff.HideMenuItem("Reload")
        'nsOff.HideMenuItem("Quit")
        'nsOff.HideMenuItem("Navigator")
        'nsOff.HideMenuItem("About")
        'nsOff.HideMenuItem("NewWindow")
        'nsOff.HideMenuItem("CloseWin")


        'nsOff.SetSpecificToolBarVisible("standardbar", True)


        nsOff.AddGlobalDocumentListener()   '开启全局事件监听
        nsOff.AddKeyListener(0)  '开启键盘监听事件
        nsOff.SetGlobalDocumentListener(1, 1, 1, 1)  '开启全局事件监听后，设定需要产生事件的对象类型
        nsOff.AddFileListener()     '开启文件监听，可在office崩溃后自动恢复当前编辑的文档

        nsOff.EnableCopyFromExternal(True)

        ' EmrPara.PublicConfig.BlCopy
        If 1 = 0 Then
            '设置病人ID的标志，禁止不同病人间的病历进行拷贝（下面两个接口要同时调用）
            nsOff.SetExtraCopyInformation(m_Bl_Row("Bl_Code"))
            nsOff.SetCanCopyFromActiveX(True)
        End If

        nsOff.SetCanCopyWithStruct(False)   '拷贝时不带结构

        '关闭修改痕迹显示，并显示最终状态
        nsOff.SwitchRecension(False)
        nsOff.ShowRecension(2)
        '关闭智能输入
        nsOff.SetIntellectiveInput(False, 0)
        nsOff.SetRulersVisible(True, False)
        '允许英文单词中间换行
        nsOff.SetWestCharBreakAttribute(2, True)


    End Sub

    Private Sub nsOff_NsoFileOpenCompleted(ByVal sender As Object, ByVal e As AxNsoOfficeLib._INsoControlEvents_NsoFileOpenCompletedEvent) Handles nsOff.NsoFileOpenCompleted
        'nsOff.EditTemplet()          '要切换为书写模式，默认会关闭所有菜单、工具栏的显示
        SetMenuToolbar()
        nsOff.Visible = True
    End Sub
    '设置菜单、工具栏的显示
    Private Sub SetMenuToolbar()
        nsOff.SetMenuBarVisible(True)   '显示菜单栏
        nsOff.ShowMenuItem("newDoc")  ''隐藏不允许使用的菜单
        nsOff.ShowMenuItem("Open")
        nsOff.ShowMenuItem("CloseDoc")
        nsOff.ShowMenuItem("Save")
        nsOff.ShowMenuItem("SaveAs")
        nsOff.ShowMenuItem("SaveAll")
        nsOff.ShowMenuItem("Reload")
        nsOff.ShowMenuItem("Quit")
        nsOff.ShowMenuItem("Navigator")
        nsOff.ShowMenuItem("About")
        nsOff.ShowMenuItem("NewWindow")
        nsOff.ShowMenuItem("CloseWin")
        nsOff.ShowMenuItem("PageDialog")
        nsOff.ShowMenuItem("PrintPreview")
        nsOff.SetSpecificToolBarVisible("viewerbar1", True)
        nsOff.SetSpecificToolBarVisible("standardbar", True)
        nsOff.SetSpecificToolBarVisible("textobjectbar", True)
        nsOff.SetSpecificToolBarVisible("insertbar", True)
        nsOff.SetCustomToolbarVisible("StreamLineTool", False)  '这种自定义工具栏用于对office标准工具栏进行自定义组合，里面包含的按钮都是office的标准命令，与前面的addonbar不同
        nsOff.SetSpecificToolBarVisible("addonbar3", False)      '这个是系统预定义的扩展工具栏（共5个），用来扩展用户的自定义功能，里面的按钮图标、名称由用户自定义，点击后会产生事件给用户自行处理
        nsOff.SetSpecificToolBarVisible("addonbar2", False)
        nsOff.SetSpecificToolBarVisible("addonbar", False)

        nsOff.SetStatusBarVisible(True)

        'nsOff.RemoveUserRootMenuItem("PickList")
        'nsOff.RemoveUserRootMenuItem("HelpMenu")
        'nsOff.ShowMenuItem("PrintPreview")

        'nsOff.HideMenuItem("Print")   '隐藏
        'nsOff.SetToolBarsVisible("sw", False)
        'chk背景色.Checked = True        '默认显示数据元的背景色
        nsOff.EnableFloatbar(True)
    End Sub

    Private Sub nsOff_NsoKeyPressedEvent(ByVal sender As System.Object, ByVal e As AxNsoOfficeLib._INsoControlEvents_NsoKeyPressedEventEvent) Handles nsOff.NsoKeyPressedEvent
        '处理鼠标和键盘事件的时候，请尽量不用使用同步方式来显示其他窗体，如果在这些窗体中再打开其他的病历文档，可能会导致文档无法显示出来。因为控制权被这些窗体所掌握，无法回到office进程中，会导致office无法刷新界面。
        If e.nKeyCode = 530 And e.nModifiers = 2 Then
            Savebl()
            e.bCancel = True
        Else
            e.bCancel = False
        End If
    End Sub

    Private Function Savebl() As Boolean
        Dim Str_Update As String = ""

        '先保存到临时文件中，然后读出文件内容，再存入数据库
        Dim lbytContents() As Byte
        Dim lstrFilename As String

        lstrFilename = System.Windows.Forms.Application.StartupPath & "\" & Format(Now, "yyyymmddhhmmss") & Format(Rnd() * 100, "000") & ".odt"
        nsOff.SaveAs2(lstrFilename, True)      '将当前文件另存为一个临时文件
        nsOff.SetDocModified2(False)    '将文件的修改标志设为“未修改”

        lbytContents = My.Computer.FileSystem.ReadAllBytes(lstrFilename)
        My.Computer.FileSystem.DeleteFile(lstrFilename)     '删除该临时文件

        If m_lb = "新建" Then
            mbid = HisVar.HisVar.Sqldal.GetMaxID("id", "Bl_Mb")
            Str_Update = "INSERT INTO Bl_Mb(id,Bl_Code,Mb_id,Mb_Name,Mb_Nr,Jsr_Code,Ys_Code,IsLock)VALUES(@id,@Bl_Code,@Mb_id,@Mb_Name,@Mb_Nr,@Jsr_Code,@Ys_Code,@IsLock)"
            Dim paralist(7) As SqlParameter

            paralist(0) = New SqlParameter("@id", SqlDbType.VarChar)
            paralist(1) = New SqlParameter("@Bl_Code", SqlDbType.VarChar)
            paralist(2) = New SqlParameter("@Mb_id", SqlDbType.Int)
            paralist(3) = New SqlParameter("@Mb_Name", SqlDbType.VarChar)
            paralist(4) = New SqlParameter("@Mb_Nr", SqlDbType.Image)
            paralist(5) = New SqlParameter("@Jsr_Code", SqlDbType.VarChar)
            paralist(6) = New SqlParameter("@Ys_Code", SqlDbType.VarChar)
            paralist(7) = New SqlParameter("@IsLock", SqlDbType.Bit)

            paralist(0).Value = mbid
            paralist(1).Value = m_Bl_Row.Item("Bl_Code") & ""
            paralist(2).Value = m_Mb_Row.Item("Mb_Id")
            paralist(3).Value = m_Mb_Row.Item("Mb_Name") & ""
            paralist(4).Value = lbytContents
            paralist(5).Value = HisVar.HisVar.JsrCode
            paralist(6).Value = HisVar.HisVar.JsrYsCode
            paralist(7).Value = 0

            Dim My_Row As DataRow = UseMb_Tab.NewRow
            My_Row.Item("id") = mbid
            My_Row.Item("Bl_Code") = m_Bl_Row.Item("Bl_Code") & ""
            My_Row.Item("Mb_id") = m_Mb_Row.Item("Mb_Id")
            My_Row.Item("Mb_Name") = m_Mb_Row.Item("Mb_Name") & ""
            My_Row.Item("Jsr_Code") = HisVar.HisVar.JsrCode
            My_Row.Item("Ys_Code") = HisVar.HisVar.JsrYsCode
            My_Row.Item("IsLock") = False
            Try
                Dim num As Integer = HisVar.HisVar.Sqldal.ExecuteSql(Str_Update, paralist)
                If num > 0 Then
                    UseMb_Tab.Rows.Add(My_Row)
                    NoUseMb_Tab.Rows.Remove(m_Mb_Row)
                    m_lb = "修改"
                    Return True
                Else
                    Return False
                End If

            Catch ex As Exception
                MsgBox(ex.Message.ToString, MsgBoxStyle.Information, "提示")
                Return False
            End Try

        Else
            Str_Update = "Update  Bl_Mb  set Mb_Nr=@Mb_Nr where id=@id"
            Dim paralist(1) As SqlParameter
            paralist(0) = New SqlParameter("@Mb_Nr", SqlDbType.Image)
            paralist(1) = New SqlParameter("@id", SqlDbType.Int)

            paralist(0).Value = lbytContents
            paralist(1).Value = mbid
            Try
                Dim num As Integer = HisVar.HisVar.Sqldal.ExecuteSql(Str_Update, paralist)
                If num > 0 Then
                    Return True
                Else
                    Return False
                End If
            Catch ex As Exception
                MsgBox(ex.Message.ToString, MsgBoxStyle.Information, "提示")
                Return False
            End Try
        End If


    End Function

    Private Sub Button1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button1.Click
        If Savebl() = True Then
            HisVar.HisVar.Sqldal.ExecuteSql("UPDATE dbo.Auto_Cf SET IsInsertDzbl = 1 WHERE Bl_Code = '" & m_Bl_Row.Item("Bl_Code") & "' AND IsInsertDzbl = 0")

            MsgBox("保存成功！", MsgBoxStyle.Information, "提示")
        Else
            MsgBox("保存失败！", MsgBoxStyle.Information, "提示")
        End If

        'nsOff.SaveAs2("C:\123.pdf", True)
        'nsOff.ShowMenuItem("ExportToPDF")
    End Sub

    Private Sub Button2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Button2.Click



        Call Savebl()

        Dim lbytContents() As Byte = HisVar.HisVar.Sqldal.GetSingle("Select Mb_Nr from Bl_Mb where Bl_Code='" & m_Bl_Row.Item("Bl_Code") & "' and Mb_Id='" & UseMb_Tab.Rows.Find(mbid).Item("Mb_Id") & "'")

        If lbytContents Is Nothing Then
            MsgBox("未获取到相关打印内容，请关闭后重试！", MsgBoxStyle.Information, "提示：")
            Exit Sub
        End If

        Dim startdate As Date = Now
        Dim Downdata() As Byte = Nothing
        Try
            HisVar.HisVar.DzblPrintservice.UploadFile(lbytContents, HisVar.HisVar.WsyName, m_Bl_Row.Item("Bl_Code") & "(" & UseMb_Tab.Rows.Find(mbid).Item("Mb_Id") & ").odt")
            HisVar.HisVar.DzblPrintservice.pdfDel(HisVar.HisVar.WsyName, "" & m_Bl_Row.Item("Bl_Code") & "(" & UseMb_Tab.Rows.Find(mbid).Item("Mb_Id") & ").pdf")

            While Downdata Is Nothing
                Downdata = HisVar.HisVar.DzblPrintservice.DownLoadPdf(HisVar.HisVar.WsyName, m_Bl_Row.Item("Bl_Code") & "(" & UseMb_Tab.Rows.Find(mbid).Item("Mb_Id") & ").pdf")
                If DateDiff(DateInterval.Minute, startdate, Now) = 1 Then
                    MsgBox("文件请求超时，请稍后重试！", MsgBoxStyle.Information, "提示")
                    Exit Sub
                End If
            End While
        Catch ex As Exception
            MsgBox(ex.Message.ToString, MsgBoxStyle.Information, "提示：")
            Exit Sub
        End Try



        System.IO.File.WriteAllBytes(System.Windows.Forms.Application.StartupPath & "\" & m_Bl_Row.Item("Bl_Code") & "(" & UseMb_Tab.Rows.Find(mbid).Item("Mb_Id") & ").pdf", Downdata)

        Dim startInfo As New ProcessStartInfo()
        startInfo.UseShellExecute = True
        startInfo.Verb = "Print"
        startInfo.CreateNoWindow = True
        startInfo.WindowStyle = ProcessWindowStyle.Hidden
        startInfo.FileName = System.Windows.Forms.Application.StartupPath & "\" & m_Bl_Row.Item("Bl_Code") & "(" & UseMb_Tab.Rows.Find(mbid).Item("Mb_Id") & ").pdf"
        Process.Start(startInfo)



    End Sub

    Private Sub ContinuePrint_Btn_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ContinuePrint_Btn.Click

        Call Savebl()

        If MsgBox("续打时要包含页眉信息吗？", MsgBoxStyle.Question + MsgBoxStyle.YesNo, "系统询问") = MsgBoxResult.No Then
            nsOff.PrintDocBySelect(False)
        Else
            nsOff.PrintDocBySelect(True)
        End If
        nsOff.Refresh()
    End Sub
    '向表格中 添加长期医嘱内容
    Private Sub FillTableCell(ByVal Lb As String)
        Dim str_sql As String = "SELECT CONVERT(VARCHAR(10),Start_Date,126)AS Start_Date,SUBSTRING(CONVERT(VARCHAR(19),Start_Date,126),12,5)AS Start_Time,Cf_Mx.Mx_Gg + ' '+Cf_Mx.Xm_Name+' '+CAST(CAST(Cf_Mx.Cf_Sl AS int) AS VARCHAR) +' '+Cf_Mx.Xm_Dw AS Xm_Name,'' Ys_Qm,'' Hs_Qm,CASE Auto_Zt WHEN '已开始' THEN '' WHEN '停止' THEN CONVERT(VARCHAR(10),End_Date,126) END AS End_Date,CASE Auto_Zt WHEN '已开始' THEN '' WHEN '停止' THEN SUBSTRING(CONVERT(VARCHAR(19),End_Date,126),12,5) END AS End_Time,'' Ys_Qm,'' Hs_Qm FROM dbo.Auto_Cf ,( SELECT '诊疗' AS Lb ,AutoCf_Code ,Cf_Id ,Cf_Dj ,Cf_Sl ,Cf_Money ,'' Mx_Gyzz ,'' AS Mx_Gg ,'' Mx_Cd , Xm_Dw ,Xm_Name ,Cf_Lb , '' Yp_Ph ,NULL Yp_Yxq FROM Auto_Cfxm ,Zd_Ml_Xm3 WHERE Auto_Cfxm.Xm_Code = Zd_Ml_Xm3.Xm_Code UNION ALL SELECT '药品' AS Lb ,AutoCf_Code ,Cf_Id ,Cf_Dj ,Cf_Sl ,Cf_Money ,Mx_Gyzz ,Mx_Gg ,Mx_Cd ,Mx_XsDw ,Yp_Name ,Cf_Lb ,V_YpKc.Yp_Ph ,V_YpKc.Yp_Yxq FROM  Auto_Cfyp ,V_YpKc WHERE Auto_Cfyp.Xx_Code = V_YpKc.Xx_Code) AS Cf_Mx WHERE Auto_Cf.AutoCf_Code = Cf_Mx.AutoCf_Code AND IsInsertDzbl = 0 AND Auto_Zt <> '未开始' AND Bl_Code = '" & m_Bl_Row.Item("Bl_Code") & "' ORDER BY Start_Date"
        Dim ds As New DataSet
        Dim dt As DataTable = Nothing
        Try
            HisVar.HisVar.Sqldal.QueryDt(ds, str_sql, "长期医嘱", True)
        Catch ex As Exception
            MsgBox("获取长期医嘱失败！", MsgBoxStyle.Exclamation, "错误信息")
            Exit Sub
        End Try


        dt = ds.Tables("长期医嘱")

        If dt Is Nothing Then
            Exit Sub
        End If

        If dt.Rows.Count < 1 Then
            Exit Sub
        End If


        '获取 电子病历模板中的 表格
        Dim TableNames As String = nsOff.GetAllTableNamesByCurrentDoc()
        Dim TableName As String = Split(TableNames, ",")(0)
        '获取 表格中的行数 与列数
        Dim RowCounts As Long = nsOff.GetTableRowCount(TableName)
        Dim ColCounts As Long = nsOff.GetTableColCount(TableName)


        '获取有单元格不为空的最后一行行号,默认为0
        Dim NullRowNo As Integer = 0
        Dim IsNullCell As Boolean = True

        '从表的最后一行倒序 遍历
        For i As Integer = RowCounts To 1 Step -1
            For j As Integer = 0 To ColCounts - 1
                Dim CellName As String = "" & Chr(65 + j) & i
                If nsOff.GetCellContent(TableName, CellName.Trim).Trim = "" Then
                    IsNullCell = IsNullCell And True
                Else
                    IsNullCell = IsNullCell And False
                End If
            Next
            If Not IsNullCell Then
                NullRowNo = i
                Exit For
            End If
            IsNullCell = True
        Next

        Dim counts As Integer
        If NullRowNo = 0 Then
            counts = dt.Rows.Count
        Else
            '长期医嘱 数目  与 表格 行数作比较   (RowCounts - NullRowNo + 1) 空行的数目
            counts = dt.Rows.Count - (RowCounts - NullRowNo)
            '如果医嘱数目 大于表格 行数 则 向表格中添加行
            If counts > 0 Then
                nsOff.IncMultiRows(TableName, RowCounts, counts)
            End If
        End If

        '重新获取 表格中的行数
        RowCounts = nsOff.GetTableRowCount(TableName)
        '向表格中添加 医嘱内容
        For i As Integer = 0 To dt.Rows.Count - 1
            For j As Integer = 0 To ColCounts - 1
                nsOff.PutCellContent(TableName, Chr(65 + j) & (i + NullRowNo + 1), dt.Rows(i).Item(j))
            Next
        Next
    End Sub
    ''向表格中 添加长期医嘱内容
    'Private Sub FillTableCell(ByVal Lb As String)


    '    '获取 长期医嘱 数据
    '    'Dim str_sql As String = "SELECT Start_Date,End_Date,Cf_Mx.Mx_Gg,Cf_Mx.Xm_Name,Cf_Mx.Cf_Sl,Cf_Mx.Xm_Dw FROM dbo.Auto_Cf ,( SELECT '诊疗' AS Lb ,AutoCf_Code ,Cf_Id ,Cf_Dj ,Cf_Sl ,Cf_Money ,'' Mx_Gyzz ,'' AS Mx_Gg ,'' Mx_Cd , Xm_Dw ,Xm_Name ,Cf_Lb , '' Yp_Ph ,NULL Yp_Yxq FROM Auto_Cfxm ,Zd_Ml_Xm3 WHERE Auto_Cfxm.Xm_Code = Zd_Ml_Xm3.Xm_Code UNION ALL SELECT '药品' AS Lb ,AutoCf_Code ,Cf_Id ,Cf_Dj ,Cf_Sl ,Cf_Money ,Mx_Gyzz ,Mx_Gg ,Mx_Cd ,Mx_XsDw ,Yp_Name ,Cf_Lb ,V_YpKc.Yp_Ph ,V_YpKc.Yp_Yxq FROM  Auto_Cfyp ,V_YpKc WHERE Auto_Cfyp.Xx_Code = V_YpKc.Xx_Code) AS Cf_Mx WHERE Auto_Cf.AutoCf_Code = Cf_Mx.AutoCf_Code AND Bl_Code = '" & m_Bl_Row.Item("Bl_Code") & "' ORDER BY Start_Date"
    '    Dim str_sql As String

    '    If Zy_Dzbl1.str_sql.Trim = "" Then
    '        'str_sql = "SELECT CONVERT(VARCHAR(10),Start_Date,126)AS Start_Date,SUBSTRING(CONVERT(VARCHAR(19),Start_Date,126),12,8)AS Start_Time,Cf_Mx.Mx_Gg + ' '+Cf_Mx.Xm_Name+' '+CAST(CAST(Cf_Mx.Cf_Sl AS int) AS VARCHAR) +' '+Cf_Mx.Xm_Dw AS Xm_Name,'' Ys_Qm,'' Hs_Qm,CONVERT(VARCHAR(10),End_Date,126)AS End_Date,SUBSTRING(CONVERT(VARCHAR(19),End_Date,126),12,8)AS End_Time,'' Ys_Qm,'' Hs_Qm FROM dbo.Auto_Cf ,( SELECT '诊疗' AS Lb ,AutoCf_Code ,Cf_Id ,Cf_Dj ,Cf_Sl ,Cf_Money ,'' Mx_Gyzz ,'' AS Mx_Gg ,'' Mx_Cd , Xm_Dw ,Xm_Name ,Cf_Lb , '' Yp_Ph ,NULL Yp_Yxq FROM Auto_Cfxm ,Zd_Ml_Xm3 WHERE Auto_Cfxm.Xm_Code = Zd_Ml_Xm3.Xm_Code UNION ALL SELECT '药品' AS Lb ,AutoCf_Code ,Cf_Id ,Cf_Dj ,Cf_Sl ,Cf_Money ,Mx_Gyzz ,Mx_Gg ,Mx_Cd ,Mx_XsDw ,Yp_Name ,Cf_Lb ,V_YpKc.Yp_Ph ,V_YpKc.Yp_Yxq FROM  Auto_Cfyp ,V_YpKc WHERE Auto_Cfyp.Xx_Code = V_YpKc.Xx_Code) AS Cf_Mx WHERE Auto_Cf.AutoCf_Code = Cf_Mx.AutoCf_Code AND Bl_Code = '" & m_Bl_Row.Item("Bl_Code") & "' ORDER BY Start_Date"
    '        Exit Sub
    '    Else
    '        str_sql = "SELECT CONVERT(VARCHAR(10),Start_Date,126)AS Start_Date,SUBSTRING(CONVERT(VARCHAR(19),Start_Date,126),12,8)AS Start_Time,Cf_Mx.Mx_Gg + ' '+Cf_Mx.Xm_Name+' '+CAST(CAST(Cf_Mx.Cf_Sl AS int) AS VARCHAR) +' '+Cf_Mx.Xm_Dw AS Xm_Name,'' Ys_Qm,'' Hs_Qm,CONVERT(VARCHAR(10),End_Date,126)AS End_Date,SUBSTRING(CONVERT(VARCHAR(19),End_Date,126),12,8)AS End_Time,'' Ys_Qm,'' Hs_Qm FROM dbo.Auto_Cf ,( SELECT '诊疗' AS Lb ,AutoCf_Code ,Cf_Id ,Cf_Dj ,Cf_Sl ,Cf_Money ,'' Mx_Gyzz ,'' AS Mx_Gg ,'' Mx_Cd , Xm_Dw ,Xm_Name ,Cf_Lb , '' Yp_Ph ,NULL Yp_Yxq FROM Auto_Cfxm ,Zd_Ml_Xm3 WHERE Auto_Cfxm.Xm_Code = Zd_Ml_Xm3.Xm_Code UNION ALL SELECT '药品' AS Lb ,AutoCf_Code ,Cf_Id ,Cf_Dj ,Cf_Sl ,Cf_Money ,Mx_Gyzz ,Mx_Gg ,Mx_Cd ,Mx_XsDw ,Yp_Name ,Cf_Lb ,V_YpKc.Yp_Ph ,V_YpKc.Yp_Yxq FROM  Auto_Cfyp ,V_YpKc WHERE Auto_Cfyp.Xx_Code = V_YpKc.Xx_Code) AS Cf_Mx WHERE Auto_Cf.AutoCf_Code = Cf_Mx.AutoCf_Code AND Bl_Code = '" & m_Bl_Row.Item("Bl_Code") & "'AND Auto_Cf.AutoCf_Code In (" & Zy_Dzbl1.str_sql.Trim & ") ORDER BY Start_Date"
    '    End If

    '    Dim ds As New DataSet
    '    Dim dt As DataTable = Nothing
    '    Try
    '        HisVar.HisVar.Sqldal.QueryDt(ds, str_sql, "长期医嘱", True)
    '    Catch ex As Exception
    '        MsgBox("获取长期医嘱失败！", MsgBoxStyle.Exclamation, "错误信息")
    '        Exit Sub
    '    End Try


    '    dt = ds.Tables("长期医嘱")

    '    If dt Is Nothing Then
    '        Exit Sub
    '    End If

    '    If dt.Rows.Count < 1 Then
    '        Exit Sub
    '    End If


    '    '获取 电子病历模板中的 表格
    '    Dim TableNames As String = nsOff.GetAllTableNamesByCurrentDoc()
    '    Dim TableName As String = Split(TableNames, ",")(0)
    '    '获取 表格中的行数 与列数
    '    Dim RowCounts As Long = nsOff.GetTableRowCount(TableName)
    '    Dim ColCounts As Long = nsOff.GetTableColCount(TableName)


    '    '修改 类别  清空表内容
    '    'If Lb = "修改" Then
    '    '    nsOff.DelMultiRows(TableName, 2, RowCounts - 2)
    '    '    RowCounts = nsOff.GetTableRowCount(TableName)
    '    'End If

    '    '获取有单元格不为空的最后一行行号,默认为0
    '    Dim NullRowNo As Integer = 0
    '    Dim IsNullCell As Boolean = True

    '    '从表的最后一行倒序 遍历
    '    For i As Integer = RowCounts To 1 Step -1
    '        For j As Integer = 0 To ColCounts - 1
    '            Dim CellName As String = "" & Chr(65 + j) & i
    '            If nsOff.GetCellContent(TableName, CellName.Trim).Trim = "" Then
    '                IsNullCell = IsNullCell And True
    '            Else
    '                IsNullCell = IsNullCell And False
    '            End If
    '        Next
    '        If Not IsNullCell Then
    '            NullRowNo = i
    '            Exit For
    '        End If
    '        IsNullCell = True
    '    Next

    '    Dim counts As Integer
    '    If NullRowNo = 0 Then
    '        counts = dt.Rows.Count
    '    Else
    '        '长期医嘱 数目  与 表格 行数作比较   (RowCounts - NullRowNo + 1) 空行的数目
    '        counts = dt.Rows.Count - (RowCounts - NullRowNo)
    '        '如果医嘱数目 大于表格 行数 则 向表格中添加行
    '        If counts > 0 Then
    '            nsOff.IncMultiRows(TableName, RowCounts, counts)
    '        End If
    '    End If

    '    '重新获取 表格中的行数
    '    RowCounts = nsOff.GetTableRowCount(TableName)
    '    '向表格中添加 医嘱内容
    '    'For i As Integer = 0 To dt.Rows.Count - 1
    '    '    For j As Integer = 0 To ColCounts - 1
    '    '        nsOff.PutCellContent(TableName, Chr(65 + j) & (i + 3), dt.Rows(i).Item(j))
    '    '    Next
    '    'Next
    '    For i As Integer = 0 To dt.Rows.Count - 1
    '        For j As Integer = 0 To ColCounts - 1
    '            nsOff.PutCellContent(TableName, Chr(65 + j) & (i + NullRowNo + 1), dt.Rows(i).Item(j))
    '        Next
    '    Next
    'End Sub
End Class