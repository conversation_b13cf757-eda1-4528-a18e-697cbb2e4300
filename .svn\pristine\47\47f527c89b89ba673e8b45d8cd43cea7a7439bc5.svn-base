﻿/**  版本信息模板在安装目录下，可自行修改。
* D_Emr_SiKong.cs
*
* 功 能： N/A
* 类 名： D_Emr_SiKong
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/8/11 11:19:17   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;

namespace DAL
{
	/// <summary>
	/// 数据访问类:D_Emr_SiKong
	/// </summary>
	public partial class D_Emr_SiKong
	{
		public D_Emr_SiKong()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string Sk_Code)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from Emr_SiKong");
			strSql.Append(" where Sk_Code=@Sk_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Sk_Code", SqlDbType.Char,10)			};
			parameters[0].Value = Sk_Code;

			return HisVar.HisVar.Sqldal.Exists(strSql.ToString(),parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ModelOld.M_Emr_SiKong model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into Emr_SiKong(");
			strSql.Append("Sk_Code,Mblb_Code,Mb_Code,Yl_Event,Days,Hours,Memo)");
			strSql.Append(" values (");
			strSql.Append("@Sk_Code,@Mblb_Code,@Mb_Code,@Yl_Event,@Days,@Hours,@Memo)");
			SqlParameter[] parameters = {
					new SqlParameter("@Sk_Code", SqlDbType.Char,10),
					new SqlParameter("@Mblb_Code", SqlDbType.Char,5),
					new SqlParameter("@Mb_Code", SqlDbType.Char,10),
					new SqlParameter("@Yl_Event", SqlDbType.Char,4),
					new SqlParameter("@Days", SqlDbType.Int,4),
					new SqlParameter("@Hours", SqlDbType.Int,4),
					new SqlParameter("@Memo", SqlDbType.VarChar,50)};
			parameters[0].Value = model.Sk_Code;
			parameters[1].Value = model.Mblb_Code;
            parameters[2].Value = Common.Tools.IsValueNull(model.Mb_Code);
			parameters[3].Value = model.Yl_Event;
			parameters[4].Value = model.Days;
			parameters[5].Value = model.Hours;
			parameters[6].Value = model.Memo;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ModelOld.M_Emr_SiKong model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update Emr_SiKong set ");
			strSql.Append("Mblb_Code=@Mblb_Code,");
			strSql.Append("Mb_Code=@Mb_Code,");
			strSql.Append("Yl_Event=@Yl_Event,");
			strSql.Append("Days=@Days,");
			strSql.Append("Hours=@Hours,");
			strSql.Append("Memo=@Memo");
			strSql.Append(" where Sk_Code=@Sk_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Mblb_Code", SqlDbType.Char,5),
					new SqlParameter("@Mb_Code", SqlDbType.Char,10),
					new SqlParameter("@Yl_Event", SqlDbType.Char,4),
					new SqlParameter("@Days", SqlDbType.Int,4),
					new SqlParameter("@Hours", SqlDbType.Int,4),
					new SqlParameter("@Memo", SqlDbType.VarChar,50),
					new SqlParameter("@Sk_Code", SqlDbType.Char,10)};
			parameters[0].Value = model.Mblb_Code;
		    parameters[1].Value = Common.Tools.IsValueNull(model.Mb_Code);
			parameters[2].Value = model.Yl_Event;
			parameters[3].Value = model.Days;
			parameters[4].Value = model.Hours;
			parameters[5].Value = model.Memo;
			parameters[6].Value = model.Sk_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string Sk_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Emr_SiKong ");
			strSql.Append(" where Sk_Code=@Sk_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Sk_Code", SqlDbType.Char,10)			};
			parameters[0].Value = Sk_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string Sk_Codelist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Emr_SiKong ");
			strSql.Append(" where Sk_Code in ("+Sk_Codelist + ")  ");
			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Emr_SiKong GetModel(string Sk_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 Sk_Code,Mblb_Code,Mb_Code,Yl_Event,Days,Hours,Memo from Emr_SiKong ");
			strSql.Append(" where Sk_Code=@Sk_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Sk_Code", SqlDbType.Char,10)			};
			parameters[0].Value = Sk_Code;

			ModelOld.M_Emr_SiKong model=new ModelOld.M_Emr_SiKong();
			DataSet ds=HisVar.HisVar.Sqldal.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Emr_SiKong DataRowToModel(DataRow row)
		{
			ModelOld.M_Emr_SiKong model=new ModelOld.M_Emr_SiKong();
			if (row != null)
			{
				if(row["Sk_Code"]!=null)
				{
					model.Sk_Code=row["Sk_Code"].ToString();
				}
				if(row["Mblb_Code"]!=null)
				{
					model.Mblb_Code=row["Mblb_Code"].ToString();
				}
				if(row["Mb_Code"]!=null)
				{
					model.Mb_Code=row["Mb_Code"].ToString();
				}
				if(row["Yl_Event"]!=null)
				{
					model.Yl_Event=row["Yl_Event"].ToString();
				}
				if(row["Days"]!=null && row["Days"].ToString()!="")
				{
					model.Days=int.Parse(row["Days"].ToString());
				}
				if(row["Hours"]!=null && row["Hours"].ToString()!="")
				{
					model.Hours=int.Parse(row["Hours"].ToString());
				}
				if(row["Memo"]!=null)
				{
					model.Memo=row["Memo"].ToString();
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select Sk_Code,Mblb_Code,Mb_Code,Yl_Event,Days,Hours,Memo ");
			strSql.Append(" FROM Emr_SiKong ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}
        public DataSet GetListxx(string strWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select Sk_Code,Emr_SiKong.Mblb_Code,Emr_SiKong.Mb_Code,Yl_Event,Days,Hours,Mb_Name,Mblb_Name,Memo ");
            strSql.Append(" FROM dbo.Emr_SiKong left join dbo.Emr_Mblb on Emr_Mblb.Mblb_Code=Emr_SiKong.Mblb_Code left join dbo.Emr_Mb on  Emr_Mb.Mb_Code=Emr_SiKong.Mb_Code  ");
           
            if (strWhere.Trim() != "")
            {
                strSql.Append("where  " + strWhere);
            }
            return HisVar.HisVar.Sqldal.Query(strSql.ToString());
        }
		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" Sk_Code,Mblb_Code,Mb_Code,Yl_Event,Days,Hours,Memo ");
			strSql.Append(" FROM Emr_SiKong ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM Emr_SiKong ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
        public string MaxCode()
        {
            string Max = HisVar.HisVar.Sqldal.F_MaxCode("SELECT MAX(Sk_Code) FROM dbo.Emr_SiKong", 10);
            return Max;
        }
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.Sk_Code desc");
			}
			strSql.Append(")AS Row, T.*  from Emr_SiKong T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Emr_SiKong";
			parameters[1].Value = "Sk_Code";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return HisVar.HisVar.Sqldal.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

