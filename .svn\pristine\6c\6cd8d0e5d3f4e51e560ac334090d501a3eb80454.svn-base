﻿Imports System.Data.SqlClient
Imports C1.Win.C1TrueDBGrid
Imports C1.Win.C1FlexGrid

Public Class Zy_Ty

#Region "定义__变量"
    Dim My_Adapter As New SqlDataAdapter                '从表适配器
    Dim My_Table As New DataTable                       '从表
    Dim My_Reader As SqlClient.SqlDataReader
    Dim V_GridCol As Integer                            '当前TDBGrid所在的列

    Public Cb_Cm As CurrencyManager                     '同步指针
    Public Cb_Row As DataRow                            '当前选择行

    Dim Str_Select As String
    Dim My_Dataset As New DataSet
#End Region

    Private Sub Zy_Ty_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Call Flex_Form()
        Call Form_Init()
        If My_Dataset.Tables("药品明细") IsNot Nothing Then My_Dataset.Tables("药品明细").Clear()

        '窗体初始化
        TfRyComobo.SelectedValue = ""
        C1TextBox2.Text = ""
        C1TextBox4.Text = ""
        C1TextBox5.Text = ""
        C1TextBox6.Text = ""

    End Sub

#Region "窗体__事件"
    Private Sub Flex_Form()
        Me.C1FlexGrid1.Width = Me.Width

        Flex_Init(Me.C1FlexGrid1, 15)

        Flex_Col(Me.C1FlexGrid1, 0, "处方时间", 110, "左", True)
        Flex_Col(Me.C1FlexGrid1, 1, "药品名称", 180, "左", True)
        Flex_Col(Me.C1FlexGrid1, 2, "国药准字", 90, "左", True)
        Flex_Col(Me.C1FlexGrid1, 3, "规格", 100, "左", True)
        Flex_Col(Me.C1FlexGrid1, 4, "单位", 60, "左", True)
        Flex_Col(Me.C1FlexGrid1, 5, "产地", 70, "左", True)
        Flex_Col(Me.C1FlexGrid1, 6, "有效期", 70, "中", True)
        Flex_Col(Me.C1FlexGrid1, 7, "数量", 50, "右", True)
        Flex_Col(Me.C1FlexGrid1, 8, "单价", 70, "右", True)
        Flex_Col(Me.C1FlexGrid1, 9, "金额", 70, "右", True)
        Flex_Col(Me.C1FlexGrid1, 10, "处方类别", 0, "右", False)
        Flex_Col(Me.C1FlexGrid1, 11, "药品编码", 0, "右", False)
        Flex_Col(Me.C1FlexGrid1, 12, "序号", 0, "右", False)
        Flex_Col(Me.C1FlexGrid1, 13, "处方编码", 0, "右", False)
        Flex_Col(Me.C1FlexGrid1, 14, "日结编码", 0, "右", False)

        C1FlexGrid1.Cols(0).Format = "yyyy-MM-dd HH:mm"
        C1FlexGrid1.Cols(6).Format = "yyyy-MM-dd"

        C1FlexGrid1.Cols(7).Format = "###,###,###.####"
        C1FlexGrid1.Cols(8).Format = "###,###,##0.00####"
        C1FlexGrid1.Cols(9).Format = "###,###,##0.00"
        With C1FlexGrid1
            .AllowMerging = C1.Win.C1FlexGrid.AllowMergingEnum.None
            .AllowResizing = AllowResizingEnum.Columns
            .Redraw = True
        End With
    End Sub

    Private Sub Form_Init()
        Panel2.Height = 30
        '按扭初始化
        Call P_Comm(Me.Comm1)
        Call P_Comm(Me.Comm2)

        Dim My_Adapter As SqlDataAdapter = New SqlDataAdapter("Select Bl.Bl_Code,Ry_Jc,Ry_Name,Ry_Sex,Bxlb_Name,Ry_RyDate,Ry_Sfzh,(Select isnull(Sum(Jf_Money),0) From Bl_Jf Where Bl_Jf.Bl_Code=Bl.Bl_Code) As Jf_Money FROM Bl,Zd_Bxlb Where Bl.Bxlb_Code = Zd_Bxlb.Bxlb_code and Bl.Yy_Code='" & HisVar.HisVar.WsyCode & "' and Isnull(Ry_CyDate,'1900-01-01')='1900-01-01' and Bxlb_Name Not In ('城镇居民','城镇职工')", My_Cn)
        My_Adapter.Fill(My_Dataset, "病例字典")
        My_Dataset.Tables("病例字典").PrimaryKey = New DataColumn() {My_Dataset.Tables("病例字典").Columns("Bl_Code")}
        My_Adapter.Dispose()
        'Dim My_Combo As New BaseClass.C_Combo2(Me.C1Combo1, My_Dataset.Tables("病例字典").DefaultView, "Ry_Jc", "Bl_Code", 290)
        'With My_Combo
        '    .Init_TDBCombo()
        '    .Init_Colum("Bl_Code", "编码", 0, "左")
        '    .Init_Colum("Ry_Jc", "患者简称", 60, "左")
        '    .Init_Colum("Ry_Name", "患者姓名", 80, "左")
        '    .Init_Colum("Ry_Sex", "患者性别", 60, "左")
        '    .Init_Colum("Ry_RyDate", "入院时间", 60, "左")
        '    .Init_Colum("Ry_Sfzh", "身份证号", 0, "左")
        '    .Init_Colum("Jf_Money", "缴纳押金", 0, "左")
        '    .MaxDropDownItems(17)
        '    .SelectedIndex(-1)
        'End With
        'C1Combo1.Select()

        With TfRyComobo
            .DataView = My_Dataset.Tables("病例字典").DefaultView
            .Init_Colum("Bl_Code", "编码", 0, "左")
            .Init_Colum("Ry_Jc", "患者简称", 60, "左")
            .Init_Colum("Ry_Name", "患者姓名", 80, "左")
            .Init_Colum("Ry_Sex", "患者性别", 60, "左")
            .Init_Colum("Bxlb_Name", "患者类别", 80, "左")
            .Init_Colum("Ry_RyDate", "入院时间", 60, "左")
            .Init_Colum("Ry_Sfzh", "身份证号", 0, "左")
            .Init_Colum("Jf_Money", "缴纳押金", 0, "左")
            .DisplayMember = "Ry_Name"
            .ValueMember = "Bl_Code"
            .RowFilterTextNull = ""
            .RowFilterNotTextNull = "Ry_Jc" '过滤字段
            .DroupDownWidth = 280
            .MaxDropDownItems = 17
            .SelectedIndex = -1
        End With

    End Sub
#End Region

#Region "控件__动作"
    Private Sub Comm_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.Click, Comm2.Click

        Select Case sender.tag
            Case "退回"
                If Trim(TfRyComobo.Text) = "" Then
                    MsgBox("患者姓名不能为空！", MsgBoxStyle.Information, "提示")
                    TfRyComobo.Select()
                    Exit Sub
                End If


                If C1FlexGrid1.Rows.Count = 1 Then Exit Sub
                If MsgBox("是否确认进行该患者退药？", vbQuestion + vbYesNo + vbDefaultButton1, "提示:") = vbYes Then

                    C1FlexGrid1.Focus()
                    
                    Call Yp_Tf()
                    If My_Dataset.Tables("退费主表") IsNot Nothing Then My_Dataset.Tables("退费主表").Clear()
                    TfRyComobo.Select()
                End If
            Case "取消"
                Me.Close()
        End Select

    End Sub
    Private Sub Yp_Tf()
        Dim bllCountryYbZymx as New YBBLL.BllCountry_YB_ZYMX
        Dim Yf_Sl As String
        Yf_Sl = "Yf_Sl" & CDbl(Mid(HisVar.HisVar.YfCode, 5, 2))

        Dim V_Count As Integer = C1FlexGrid1.Rows.Selected.Count
        If V_Count = 0 Then Exit Sub
        Me.C1FlexGrid1.Rows(0).Selected = False

        C1FlexGrid1.Redraw = False
        Dim R As Row

        For Each R In Me.C1FlexGrid1.Rows.Selected
            Try
                Dim T_List As New ArrayList

                If R.Item(10) = "西药" Or R.Item(10) = "中成药" Or R.Item(10) = "中草药" Or R.Item(10) = "卫生材料" Then

                    '判断是否在医保接口中已经上传，已经上传的就不能在这退费，只能进行负数退费
                    If  bllCountryYbZymx.Exists(R.Item(12),"Yp") Then
                        MsgBox("已经导入到医保接口,不能在此界面退费，请录入负数退费！", MsgBoxStyle.Information, "提示：")
                        Exit For
                    End If

                    Dim Pd As Object = HisVar.HisVar.Sqldal.GetSingle("select distinct Pd_Wc from Zd_YfPd where Pd_Wc='否' and Yf_Code='" & HisVar.HisVar.YfCode & "'")
                    If Pd Is Nothing Then
                    Else
                        MsgBox("正在进行盘点，请等待盘点完成后在进行退药！", MsgBoxStyle.Information, "提示：")
                        Exit For
                    End If
                    
                    T_List.Add("Delete Bl_CfYp Where Cf_Code='" & R.Item(13) & "' And Xx_Code='" & R.Item(11) & "' And Cf_Id='" & R.Item(12) & "'")

                    T_List.Add("Update Zd_Ml_Yp4 Set " & Yf_Sl & "=isnull(" & Yf_Sl & ",0)+(" & R.Item(7) & ") Where Xx_Code='" & R.Item(11) & "' ")
                Else
                    '判断是否在医保接口中已经上传，已经上传的就不能在这退费，只能进行负数退费
                    If  bllCountryYbZymx.Exists(R.Item(12),"Xm") Then
                        MsgBox("已经导入到医保接口,不能在此界面退费，请录入负数退费！", MsgBoxStyle.Information, "提示：")
                        Exit For
                    End If

                    T_List.Add("Delete Bl_Cfxm Where Cf_Code='" & R.Item(13) & "' And Xm_Code='" & R.Item(11) & "' And Cf_Id='" & R.Item(12) & "'")
                End If
                HisVar.HisVar.Sqldal.ExecuteSqlTran(T_List)

                Dim arr As New ArrayList
                If HisVar.HisVar.Sqldal.GetSingle("Select  Cf_Code from Bl_CfYp where Cf_Code='" & R.Item(13) & "'") Is Nothing And HisVar.HisVar.Sqldal.GetSingle("Select  Cf_Code from Bl_CfXm where Cf_Code='" & R.Item(13) & "'") Is Nothing Then
                    arr.Add("Delete from Bl_Cf where Cf_Code='" & R.Item(13) & "'")
                    arr.Add("Delete From Bl_CfLyd where Cf_Code='" & R.Item(13) & "'")
                    arr.Add("Delete From Bl_Czd where Cf_Code='" & R.Item(13) & "'")
                Else
                    arr.Add("Update Bl_Cf Set Cf_Money='" & F_Zb_Money(R.Item(13)) & "',Cf_YpMoney='" & F_ZbYp_Money(R.Item(13)) & "' Where Cf_Code='" & R.Item(13) & "'")
                    arr.Add("Update Bl_Cflyd set Cf_YpMoney='" & F_ZbYp_Money(R.Item(13)) & "' Where Cf_Code='" & R.Item(13) & "'")
                End If

                HisVar.HisVar.Sqldal.ExecuteSqlTran(arr)


                C1FlexGrid1.Rows.Remove(R.Index)
            Catch ex As Exception
                Beep()
                MsgBox(ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")

            End Try
        Next
        C1FlexGrid1.Redraw = True


    End Sub




#End Region

#Region "自定义函数"
    Private Function F_Zb_Money(ByVal V_Zk_Code1 As String) As Double
        F_Zb_Money = 0
        My_Reader = HisVar.HisVar.Sqldal.ExecuteReader("Select Sum(Cf_Money) as Cf_Money from Bl_CfYp where Cf_Code='" & V_Zk_Code1 & "' Group By Cf_Code")

        While My_Reader.Read

            F_Zb_Money = F_Zb_Money + My_Reader.Item("Cf_Money")

        End While
        Call P_Conn(False)
        My_Reader.Close()

        My_Reader = HisVar.HisVar.Sqldal.ExecuteReader("Select Sum(Cf_Money) as Cf_Money from Bl_Cfxm where Cf_Code='" & V_Zk_Code1 & "' Group By Cf_Code")

        While My_Reader.Read
            F_Zb_Money = F_Zb_Money + My_Reader.Item("Cf_Money")

        End While
        Call P_Conn(False)
        My_Reader.Close()
    End Function
    Private Function F_ZbYp_Money(ByVal V_Zk_Code1 As String) As Double
        F_ZbYp_Money = 0
        My_Reader = HisVar.HisVar.Sqldal.ExecuteReader("Select Sum(Cf_Money) as Cf_Money from Bl_CfYp where Cf_Code='" & V_Zk_Code1 & "' Group By Cf_Code")

        While My_Reader.Read
            F_ZbYp_Money = F_ZbYp_Money + My_Reader.Item("Cf_Money")
        End While
        Call P_Conn(False)
        My_Reader.Close()
    End Function
    Private Sub P_Sum()
        If C1FlexGrid1.Rows.Count = 1 Then
            T_Label5.Text = "0条"
        Else
            T_Label5.Text = Trim(C1FlexGrid1.Rows.Count & "条")
        End If
    End Sub
   
    Private Sub P_Data()

        If My_Dataset.Tables("药品明细") IsNot Nothing Then My_Dataset.Tables("药品明细").Clear()
        With My_Adapter
            Str_Select = "select Cf_Date+Cf_Time as Cf_Date,Yp_Name,Mx_Gyzz,Mx_Gg,Mx_XsDw,Mx_Cd,isnull(V_YpKc.Yp_Yxq,''),Cf_Sl,Cf_Dj,Bl_CfYp.Cf_Money,Cf_Lb,V_YpKc.Xx_Code,Cf_Id,Bl_Cf.Cf_Code,Jz_Code From V_YpKc,Bl_CfYp,Bl_Cf Where Bl_CfYp.Xx_Code=V_YpKc.Xx_Code And Bl_Cf.Cf_Code = Bl_CfYp.Cf_Code And Bl_Code='" & TfRyComobo.SelectedValue & "' And Cf_Qr='是'  And Isnull(Jz_Code,'')='' and Yf_Code='" & HisVar.HisVar.YfCode & "' Union All" & " select Cf_Date+Cf_Time as Cf_Date, Xm_Name,''Mx_Gyzz,''Mx_Gg,Xm_Dw as Mx_XsDw,''Mx_Cd,null,Cf_Sl,Cf_Dj,Bl_Cfxm.Cf_Money,Cf_Lb,Zd_Ml_Xm3.Xm_Code as Xx_Code,Cf_Id,Bl_Cf.Cf_Code,Jz_Code  From Zd_Ml_Xm3,Bl_Cfxm,Bl_Cf Where  Zd_Ml_Xm3.Xm_Code=Bl_Cfxm.Xm_Code And Bl_Cf.Cf_Code = Bl_Cfxm.Cf_Code And Bl_Code='" & TfRyComobo.SelectedValue & "' And Cf_Qr='是' And Isnull(Jz_Code,'')='' "
            .SelectCommand = New SqlCommand(Str_Select, My_Cn)
            .Fill(My_Dataset, "药品明细")
        End With
    End Sub
    Private Sub Flex_Data_Init()

        C1FlexGrid1.DataSource = My_Dataset.Tables("药品明细")

        Flex_Col(Me.C1FlexGrid1, 0, "处方时间", 110, "左", True)
        Flex_Col(Me.C1FlexGrid1, 1, "药品名称", 180, "左", True)
        Flex_Col(Me.C1FlexGrid1, 2, "国药准字", 90, "左", True)
        Flex_Col(Me.C1FlexGrid1, 3, "规格", 100, "左", True)
        Flex_Col(Me.C1FlexGrid1, 4, "单位", 60, "左", True)
        Flex_Col(Me.C1FlexGrid1, 5, "产地", 70, "左", True)
        Flex_Col(Me.C1FlexGrid1, 6, "有效期", 70, "中", True)
        Flex_Col(Me.C1FlexGrid1, 7, "数量", 50, "右", True)
        Flex_Col(Me.C1FlexGrid1, 8, "单价", 70, "右", True)
        Flex_Col(Me.C1FlexGrid1, 9, "金额", 70, "右", True)
        Flex_Col(Me.C1FlexGrid1, 10, "处方类别", 0, "右", False)
        Flex_Col(Me.C1FlexGrid1, 11, "药品编码", 0, "右", False)
        Flex_Col(Me.C1FlexGrid1, 12, "序号", 0, "右", False)
        Flex_Col(Me.C1FlexGrid1, 13, "处方编码", 0, "右", False)
        Flex_Col(Me.C1FlexGrid1, 14, "日结编码", 0, "右", False)

        C1FlexGrid1.Cols(0).Format = "yyyy-MM-dd HH:mm"
        C1FlexGrid1.Cols(6).Format = "yyyy-MM-dd"


        C1FlexGrid1.Cols(7).Format = "###,###,###.####"
        C1FlexGrid1.Cols(8).Format = "###,###,##0.00####"
        C1FlexGrid1.Cols(9).Format = "###,###,##0.00"

        With C1FlexGrid1
            .AllowMerging = C1.Win.C1FlexGrid.AllowMergingEnum.None
            .AllowResizing = AllowResizingEnum.Columns
            .Redraw = True
        End With
        Call P_Sum()
    End Sub


#End Region

#Region "鼠标__外观"
    Private Sub P_Comm(ByVal Bt As Button)
        With Bt
            Select Case .Tag
                Case "退回"
                    .Location = New Point(T_Line1.Left + 10, 4)
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_退回1")
                    .Text = "                      &A"
                Case "取消"
                    .Location = New Point(Comm1.Left + Comm1.Width + 4, 4)
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
                    .Width = Me.Comm1.Width
                    .Text = "                      &F"
                    T_Line2.Location = New Point(Me.Comm2.Left + Me.Comm2.Width + 8, 0)
            End Select
            .FlatStyle = FlatStyle.Flat
            .FlatAppearance.BorderSize = 0
            .BackgroundImageLayout = ImageLayout.Center
        End With
    End Sub

    Private Sub Comm_MouseEnter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.MouseEnter, Comm2.MouseEnter
        Select Case sender.tag
            Case "退回"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_退回2")
                Comm1.Cursor = Cursors.Hand
            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消2")
                Comm2.Cursor = Cursors.Hand
        End Select

    End Sub

    Private Sub Comm_MouseLeave(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.MouseLeave, Comm2.MouseLeave
        Select Case sender.tag
            Case "退回"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_退回1")
                Comm1.Cursor = Cursors.Default
            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
                Comm2.Cursor = Cursors.Default
        End Select
    End Sub

    Private Sub Comm_MouseDown(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm1.MouseDown, Comm2.MouseDown
        Select Case sender.tag
            Case "退回"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_退回3")
                Comm1.Cursor = Cursors.Default
            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消3")
                Comm2.Cursor = Cursors.Default
        End Select
    End Sub

    Private Sub Comm_MouseUp(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm1.MouseUp, Comm2.MouseUp
        Select Case sender.tag
            Case "退回"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_退回1")
                Comm1.Cursor = Cursors.Hand
            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
                Comm2.Cursor = Cursors.Hand
        End Select
    End Sub
#End Region

#Region "TfRyComobo动作"
    Private Sub TfRyComobo_KeyPress(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles TfRyComobo.KeyPress
        If e.KeyChar <> Chr(Keys.Return) Then Exit Sub
        e.Handled = True
    End Sub


    Private Sub TfRyComobo_RowChange(ByVal sender As Object, ByVal e As System.EventArgs) Handles TfRyComobo.RowChange
        With TfRyComobo
            If .Text = "" Then
                'If .Text = "" Or .WillChangeToText = "" Then
                TfRyComobo.SelectedValue = ""
                C1TextBox2.Text = ""
                C1TextBox4.Text = ""
                C1TextBox5.Text = ""
                C1TextBox6.Text = ""
            Else
                C1TextBox2.Text = .Columns("Ry_Sfzh").Text
                C1TextBox4.Text = .Columns("Ry_Name").Text
                C1TextBox5.Text = .Columns("Ry_Sex").Text
                C1TextBox6.Text = .Columns("Jf_Money").Text
                Call P_Data()
                Call Flex_Data_Init()
            End If

        End With

    End Sub

#End Region




    
End Class