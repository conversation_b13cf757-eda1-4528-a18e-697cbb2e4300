﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class ZkSc
    Inherits HisControl.BaseForm

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel()
        Me.TableLayoutPanel2 = New System.Windows.Forms.TableLayoutPanel()
        Me.NameText = New CustomControl.MyTextBox()
        Me.BqCombo = New CustomControl.MyDtComobo()
        Me.MyDateEdit1 = New CustomControl.MyDateEdit()
        Me.MyDateEdit2 = New CustomControl.MyDateEdit()
        Me.KsCombo = New CustomControl.MyDtComobo()
        Me.YsCombo = New CustomControl.MyDtComobo()
        Me.ZtCombo = New CustomControl.MySingleComobo()
        Me.BedText = New CustomControl.MyTextBox()
        Me.BtnCx = New CustomControl.MyButton()
        Me.MyButton1 = New CustomControl.MyButton()
        Me.MyGrid1 = New CustomControl.MyGrid()
        Me.TableLayoutPanel1.SuspendLayout()
        Me.TableLayoutPanel2.SuspendLayout()
        Me.SuspendLayout()
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.ColumnCount = 1
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanel1.Controls.Add(Me.TableLayoutPanel2, 0, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.MyGrid1, 0, 1)
        Me.TableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(0, 0)
        Me.TableLayoutPanel1.Margin = New System.Windows.Forms.Padding(0)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.RowCount = 2
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 70.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(927, 658)
        Me.TableLayoutPanel1.TabIndex = 0
        '
        'TableLayoutPanel2
        '
        Me.TableLayoutPanel2.ColumnCount = 8
        Me.TableLayoutPanel2.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 5.0!))
        Me.TableLayoutPanel2.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 180.0!))
        Me.TableLayoutPanel2.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 180.0!))
        Me.TableLayoutPanel2.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 180.0!))
        Me.TableLayoutPanel2.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 180.0!))
        Me.TableLayoutPanel2.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 80.0!))
        Me.TableLayoutPanel2.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 80.0!))
        Me.TableLayoutPanel2.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanel2.Controls.Add(Me.NameText, 1, 0)
        Me.TableLayoutPanel2.Controls.Add(Me.BqCombo, 2, 0)
        Me.TableLayoutPanel2.Controls.Add(Me.MyDateEdit1, 1, 1)
        Me.TableLayoutPanel2.Controls.Add(Me.MyDateEdit2, 2, 1)
        Me.TableLayoutPanel2.Controls.Add(Me.KsCombo, 3, 1)
        Me.TableLayoutPanel2.Controls.Add(Me.YsCombo, 4, 1)
        Me.TableLayoutPanel2.Controls.Add(Me.ZtCombo, 4, 0)
        Me.TableLayoutPanel2.Controls.Add(Me.BedText, 3, 0)
        Me.TableLayoutPanel2.Controls.Add(Me.BtnCx, 5, 0)
        Me.TableLayoutPanel2.Controls.Add(Me.MyButton1, 6, 0)
        Me.TableLayoutPanel2.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TableLayoutPanel2.Location = New System.Drawing.Point(0, 0)
        Me.TableLayoutPanel2.Margin = New System.Windows.Forms.Padding(0)
        Me.TableLayoutPanel2.Name = "TableLayoutPanel2"
        Me.TableLayoutPanel2.RowCount = 2
        Me.TableLayoutPanel2.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 50.0!))
        Me.TableLayoutPanel2.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 50.0!))
        Me.TableLayoutPanel2.Size = New System.Drawing.Size(927, 70)
        Me.TableLayoutPanel2.TabIndex = 0
        '
        'NameText
        '
        Me.NameText.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.NameText.Captain = "姓    名"
        Me.NameText.CaptainBackColor = System.Drawing.Color.Transparent
        Me.NameText.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.NameText.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.NameText.CaptainWidth = 60.0!
        Me.NameText.ContentForeColor = System.Drawing.Color.Black
        Me.NameText.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.NameText.Location = New System.Drawing.Point(8, 7)
        Me.NameText.Multiline = False
        Me.NameText.Name = "NameText"
        Me.NameText.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.NameText.ReadOnly = False
        Me.NameText.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.NameText.SelectionStart = 0
        Me.NameText.SelectStart = 0
        Me.NameText.Size = New System.Drawing.Size(174, 20)
        Me.NameText.TabIndex = 0
        Me.NameText.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.NameText.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        '
        'BqCombo
        '
        Me.BqCombo.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.BqCombo.Captain = "病    区"
        Me.BqCombo.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.BqCombo.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.BqCombo.CaptainWidth = 60.0!
        Me.BqCombo.DataSource = Nothing
        Me.BqCombo.ItemHeight = 18
        Me.BqCombo.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.BqCombo.Location = New System.Drawing.Point(188, 7)
        Me.BqCombo.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.BqCombo.MinimumSize = New System.Drawing.Size(0, 20)
        Me.BqCombo.Name = "BqCombo"
        Me.BqCombo.ReadOnly = False
        Me.BqCombo.Size = New System.Drawing.Size(174, 20)
        Me.BqCombo.TabIndex = 1
        Me.BqCombo.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'MyDateEdit1
        '
        Me.MyDateEdit1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.MyDateEdit1.Captain = "出院时间"
        Me.MyDateEdit1.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MyDateEdit1.CaptainWidth = 60.0!
        Me.MyDateEdit1.InitialSelection = C1.Win.C1Input.InitialSelectionEnum.CaretAtEnd
        Me.MyDateEdit1.Location = New System.Drawing.Point(8, 42)
        Me.MyDateEdit1.MaximumSize = New System.Drawing.Size(100000000, 20)
        Me.MyDateEdit1.MinimumSize = New System.Drawing.Size(0, 20)
        Me.MyDateEdit1.Name = "MyDateEdit1"
        Me.MyDateEdit1.Size = New System.Drawing.Size(174, 20)
        Me.MyDateEdit1.TabIndex = 3
        Me.MyDateEdit1.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MyDateEdit1.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.MyDateEdit1.VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.DropDown
        '
        'MyDateEdit2
        '
        Me.MyDateEdit2.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.MyDateEdit2.Captain = "至"
        Me.MyDateEdit2.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MyDateEdit2.CaptainWidth = 60.0!
        Me.MyDateEdit2.InitialSelection = C1.Win.C1Input.InitialSelectionEnum.CaretAtEnd
        Me.MyDateEdit2.Location = New System.Drawing.Point(188, 42)
        Me.MyDateEdit2.MaximumSize = New System.Drawing.Size(100000000, 20)
        Me.MyDateEdit2.MinimumSize = New System.Drawing.Size(0, 20)
        Me.MyDateEdit2.Name = "MyDateEdit2"
        Me.MyDateEdit2.Size = New System.Drawing.Size(174, 20)
        Me.MyDateEdit2.TabIndex = 3
        Me.MyDateEdit2.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MyDateEdit2.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Bottom
        Me.MyDateEdit2.VisibleButtons = C1.Win.C1Input.DropDownControlButtonFlags.DropDown
        '
        'KsCombo
        '
        Me.KsCombo.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.KsCombo.Captain = "科    室"
        Me.KsCombo.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.KsCombo.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.KsCombo.CaptainWidth = 60.0!
        Me.KsCombo.DataSource = Nothing
        Me.KsCombo.ItemHeight = 18
        Me.KsCombo.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.KsCombo.Location = New System.Drawing.Point(368, 42)
        Me.KsCombo.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.KsCombo.MinimumSize = New System.Drawing.Size(0, 20)
        Me.KsCombo.Name = "KsCombo"
        Me.KsCombo.ReadOnly = False
        Me.KsCombo.Size = New System.Drawing.Size(174, 20)
        Me.KsCombo.TabIndex = 1
        Me.KsCombo.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'YsCombo
        '
        Me.YsCombo.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.YsCombo.Captain = "主治医师"
        Me.YsCombo.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.YsCombo.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.YsCombo.CaptainWidth = 60.0!
        Me.YsCombo.DataSource = Nothing
        Me.YsCombo.ItemHeight = 18
        Me.YsCombo.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.YsCombo.Location = New System.Drawing.Point(548, 42)
        Me.YsCombo.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.YsCombo.MinimumSize = New System.Drawing.Size(0, 20)
        Me.YsCombo.Name = "YsCombo"
        Me.YsCombo.ReadOnly = False
        Me.YsCombo.Size = New System.Drawing.Size(174, 20)
        Me.YsCombo.TabIndex = 1
        Me.YsCombo.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'ZtCombo
        '
        Me.ZtCombo.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ZtCombo.Captain = "状    态"
        Me.ZtCombo.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.ZtCombo.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.ZtCombo.CaptainWidth = 60.0!
        Me.ZtCombo.ComboStyle = C1.Win.C1List.ComboStyleEnum.DropdownList
        Me.ZtCombo.ItemHeight = 16
        Me.ZtCombo.ItemTextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.ZtCombo.Location = New System.Drawing.Point(548, 7)
        Me.ZtCombo.MaximumSize = New System.Drawing.Size(10000, 20)
        Me.ZtCombo.MinimumSize = New System.Drawing.Size(0, 20)
        Me.ZtCombo.Name = "ZtCombo"
        Me.ZtCombo.ReadOnly = False
        Me.ZtCombo.Size = New System.Drawing.Size(174, 20)
        Me.ZtCombo.TabIndex = 2
        Me.ZtCombo.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        '
        'BedText
        '
        Me.BedText.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.BedText.Captain = "床    位"
        Me.BedText.CaptainBackColor = System.Drawing.Color.Transparent
        Me.BedText.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.BedText.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.BedText.CaptainWidth = 60.0!
        Me.BedText.ContentForeColor = System.Drawing.Color.Black
        Me.BedText.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.BedText.Location = New System.Drawing.Point(368, 7)
        Me.BedText.Multiline = False
        Me.BedText.Name = "BedText"
        Me.BedText.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.BedText.ReadOnly = False
        Me.BedText.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.BedText.SelectionStart = 0
        Me.BedText.SelectStart = 0
        Me.BedText.Size = New System.Drawing.Size(174, 20)
        Me.BedText.TabIndex = 0
        Me.BedText.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.BedText.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        '
        'BtnCx
        '
        Me.BtnCx.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.BtnCx.ButtonImageSize = CustomControl.MyButton.imageSize.large
        Me.BtnCx.DialogResult = System.Windows.Forms.DialogResult.None
        Me.BtnCx.Font = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.BtnCx.Location = New System.Drawing.Point(728, 3)
        Me.BtnCx.Name = "BtnCx"
        Me.BtnCx.Size = New System.Drawing.Size(74, 29)
        Me.BtnCx.TabIndex = 4
        Me.BtnCx.Text = "查询"
        '
        'MyButton1
        '
        Me.MyButton1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.MyButton1.ButtonImageSize = CustomControl.MyButton.imageSize.large
        Me.MyButton1.DialogResult = System.Windows.Forms.DialogResult.None
        Me.MyButton1.Font = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MyButton1.Location = New System.Drawing.Point(808, 3)
        Me.MyButton1.Name = "MyButton1"
        Me.MyButton1.Size = New System.Drawing.Size(74, 29)
        Me.MyButton1.TabIndex = 4
        Me.MyButton1.Text = "审查"
        '
        'MyGrid1
        '
        Me.MyGrid1.CanCustomCol = False
        Me.MyGrid1.Col = 0
        Me.MyGrid1.ColumnFooters = False
        Me.MyGrid1.DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveRight
        Me.MyGrid1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.MyGrid1.FetchRowStyles = False
        Me.MyGrid1.Location = New System.Drawing.Point(0, 70)
        Me.MyGrid1.Margin = New System.Windows.Forms.Padding(0)
        Me.MyGrid1.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.MyGrid1.Name = "MyGrid1"
        Me.MyGrid1.Size = New System.Drawing.Size(927, 588)
        Me.MyGrid1.TabAction = C1.Win.C1TrueDBGrid.TabActionEnum.ControlNavigation
        Me.MyGrid1.TabIndex = 1
        Me.MyGrid1.Xmlpath = Nothing
        '
        'ZkSc
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.ClientSize = New System.Drawing.Size(927, 658)
        Me.Controls.Add(Me.TableLayoutPanel1)
        Me.Name = "ZkSc"
        Me.Text = "质控审查"
        Me.TableLayoutPanel1.ResumeLayout(False)
        Me.TableLayoutPanel2.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents TableLayoutPanel1 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents TableLayoutPanel2 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents MyGrid1 As CustomControl.MyGrid
    Friend WithEvents NameText As CustomControl.MyTextBox
    Friend WithEvents BqCombo As CustomControl.MyDtComobo
    Friend WithEvents KsCombo As CustomControl.MyDtComobo
    Friend WithEvents ZtCombo As CustomControl.MySingleComobo
    Friend WithEvents YsCombo As CustomControl.MyDtComobo
    Friend WithEvents MyDateEdit1 As CustomControl.MyDateEdit
    Friend WithEvents MyDateEdit2 As CustomControl.MyDateEdit
    Friend WithEvents BedText As CustomControl.MyTextBox
    Friend WithEvents BtnCx As CustomControl.MyButton
    Friend WithEvents MyButton1 As CustomControl.MyButton

End Class
