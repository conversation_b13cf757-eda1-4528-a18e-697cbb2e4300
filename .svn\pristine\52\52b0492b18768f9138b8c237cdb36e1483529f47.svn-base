﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ERX.Model
{
    /// <summary>
    /// 电子处方审核结果查询
    /// </summary>
    public class MdlrxChkInfoQueryIn
    {
        public string hiRxno { get; set; }   //医保处方编号
        public string fixmedinsCode { get; set; }   //定点医疗机构编号 
        public string mdtrtId { get; set; }   //医保就诊ID 
        public string psnName { get; set; }   //人员名称 
        public string psnCertType { get; set; }   //人员证件类型 
        public string certno { get; set; }   //证件号码 

    }
    public class MdlrxChkInfoQueryOut
    { 
        public string hiRxno { get; set; }   //医保处方编号 
        public string pharName { get; set; }   //医保药师姓名 
        public string pharCode { get; set; }   //医保药师代码
        public string rxChkStasCodg { get; set; }   //处方审核状态代码 
        public string rxChkOpnn { get; set; }   //处方审核意见 
        public string  rxChkTime { get; set; }   //处方审核时间
    }
}
