﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<StiSerializer version="1.02" type="Net" application="StiReport">
  <Dictionary Ref="1" type="Dictionary" isKey="true">
    <BusinessObjects isList="true" count="0" />
    <Databases isList="true" count="0" />
    <DataSources isList="true" count="1">
      <view调价信息 Ref="2" type="Stimulsoft.Report.Dictionary.StiDataViewSource" isKey="true">
        <Alias>view调价信息</Alias>
        <Columns isList="true" count="38">
          <value>Tj_Code,System.String</value>
          <value>Tj_Date,System.DateTime</value>
          <value>Ld_Name,System.String</value>
          <value>Yp_Name,System.String</value>
          <value>Yp_Jc,System.String</value>
          <value>Mx_Gg,System.String</value>
          <value>Mx_Cd,System.String</value>
          <value>Mx_CgDw,System.String</value>
          <value>Mx_XsDw,System.String</value>
          <value>Mx_Cfbl,System.Decimal</value>
          <value>Mx_Gyzz,System.String</value>
          <value>Yp_Yxq,System.DateTime</value>
          <value>Tj_Lsj_Old,System.Decimal</value>
          <value>Tj_Lsj_New,System.Decimal</value>
          <value>Tj_Yk_Lsj_Old,System.Decimal</value>
          <value>Tj_Yk_Lsj_New,System.Decimal</value>
          <value>Tj_Pfj_Old,System.Decimal</value>
          <value>Tj_Pfj_New,System.Decimal</value>
          <value>Tj_Yk_PfCe,System.Decimal</value>
          <value>Tj_Yk_LsCe,System.Decimal</value>
          <value>Tj_Lsj_Ty1,System.String</value>
          <value>Tj_Pfj_Ty1,System.String</value>
          <value>Tj_Yk_Sl,System.Decimal</value>
          <value>Tj_Yf_Sl1,System.Decimal</value>
          <value>Tj_Yf_Sl2,System.Decimal</value>
          <value>Tj_Yf_Sl3,System.Decimal</value>
          <value>TJ_Yf_Sl4,System.Decimal</value>
          <value>Tj_Yf_Sl5,System.Decimal</value>
          <value>Tj_Yf_LsCe1,System.Decimal</value>
          <value>Tj_Yf_LsCe2,System.Decimal</value>
          <value>Tj_Yf_LsCe3,System.Decimal</value>
          <value>Tj_Yf_LsCe4,System.Decimal</value>
          <value>Tj_Yf_LsCe5,System.Decimal</value>
          <value>Tj_Yf_PfCe1,System.Decimal</value>
          <value>Tj_Yf_PfCe2,System.Decimal</value>
          <value>Tj_Yf_PfCe3,System.Decimal</value>
          <value>Tj_Yf_PfCe4,System.Decimal</value>
          <value>Tj_Yf_PfCe5,System.Decimal</value>
        </Columns>
        <Dictionary isRef="1" />
        <Name>view调价信息</Name>
        <NameInSource>view调价信息</NameInSource>
      </view调价信息>
    </DataSources>
    <Relations isList="true" count="0" />
    <Report isRef="0" />
    <Variables isList="true" count="2">
      <value>,标题,标题,System.String,,False,False</value>
      <value>,打印时间,打印时间,System.String,,False,False</value>
    </Variables>
  </Dictionary>
  <EngineVersion>EngineV2</EngineVersion>
  <GlobalizationStrings isList="true" count="0" />
  <MetaTags isList="true" count="0" />
  <Pages isList="true" count="1">
    <Page1 Ref="3" type="Page" isKey="true">
      <Border>None;Black;2;Solid;False;4;Black</Border>
      <Brush>Transparent</Brush>
      <Components isList="true" count="7">
        <PageFooterBand1 Ref="4" type="PageFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,27.2,19,0.5</ClientRectangle>
          <Components isList="true" count="1">
            <Text30 Ref="5" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,19,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text30</Name>
              <Page isRef="3" />
              <Parent isRef="4" />
              <Text>第{PageNumber}页</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text30>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>PageFooterBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </PageFooterBand1>
        <ReportTitleBand1 Ref="6" type="ReportTitleBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,0.4,19,1.3</ClientRectangle>
          <Components isList="true" count="2">
            <Text1 Ref="7" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,19,0.8</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>黑体,15,Regular,Point,False,134</Font>
              <Guid>eb648feec4fe48dea7ecfffcbb9a0560</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text1</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Text>{标题}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text1>
            <Text27 Ref="8" type="Text" isKey="true">
              <Brush>Transparent</Brush>
              <ClientRectangle>13,0.8,6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9.75,Regular,Point,False,134</Font>
              <Guid>782431669b524edda0dcccda96e6a5cc</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text27</Name>
              <Page isRef="3" />
              <Parent isRef="6" />
              <Text>{打印时间}</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text27>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>ReportTitleBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </ReportTitleBand1>
        <HeaderBand1 Ref="9" type="HeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,2.5,19,0.5</ClientRectangle>
          <Components isList="true" count="10">
            <Text3 Ref="10" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>a5b7aa79acde4deeb4fb98f89407ad79</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text3</Name>
              <Page isRef="3" />
              <Parent isRef="9" />
              <Text>药品名称</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text3>
            <Text9 Ref="11" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>7.8,0,1.8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>2e17637932ba422d8e0c2f4fc4848ad9</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text9</Name>
              <Page isRef="3" />
              <Parent isRef="9" />
              <Text>国药准字</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text9>
            <Text11 Ref="12" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>9.6,0,2.2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>8e2d4944cf38445982e130f07d3dd013</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text11</Name>
              <Page isRef="3" />
              <Parent isRef="9" />
              <Text>有效期</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text11>
            <Text13 Ref="13" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>11.8,0,1.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>6ed2b72d388943a1bbd01f1bfbb36dd9</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text13</Name>
              <Page isRef="3" />
              <Parent isRef="9" />
              <Text>调价数量</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text13>
            <Text15 Ref="14" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>13.3,0,1,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>aaac47fe58b44fb59eeda1dd8759a3f8</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text15</Name>
              <Page isRef="3" />
              <Parent isRef="9" />
              <Text>单位</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text15>
            <Text17 Ref="15" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>14.3,0,1.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>2c5b29a5bb2a48538fb119aab565e83b</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text17</Name>
              <Page isRef="3" />
              <Parent isRef="9" />
              <Text>原批发价</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text17>
            <Text19 Ref="16" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>15.9,0,1.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>80b341e99872478f9f62b21f732eb871</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text19</Name>
              <Page isRef="3" />
              <Parent isRef="9" />
              <Text>新批发价</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text19>
            <Text5 Ref="17" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>3,0,2.4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>1e1b13ed56e84c03a6800a6306c9fce3</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text5</Name>
              <Page isRef="3" />
              <Parent isRef="9" />
              <Text>规格</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text5>
            <Text7 Ref="18" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>5.4,0,2.4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10,Bold</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>013c20df37f74b588111a96caaeeeb50</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text7</Name>
              <Page isRef="3" />
              <Parent isRef="9" />
              <Text>生产厂家</Text>
              <TextBrush>Black</TextBrush>
              <VertAlignment>Center</VertAlignment>
            </Text7>
            <Text28 Ref="19" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>17.5,0,1.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10,Bold</Font>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text28</Name>
              <Page isRef="3" />
              <Parent isRef="9" />
              <Text>批发差额</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text28>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>HeaderBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </HeaderBand1>
        <GroupHeaderBand1 Ref="20" type="GroupHeaderBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,3.8,19,0.5</ClientRectangle>
          <Components isList="true" count="1">
            <Text20 Ref="21" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,19,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Bold,Point,False,134</Font>
              <Guid>3c988d232645471399ecca9398878377</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text20</Name>
              <Page isRef="3" />
              <Parent isRef="20" />
              <Text>调价日期:{Format("{0:yyyy-MM-dd}",view调价信息.Tj_Date)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="22" type="CustomFormat" isKey="true">
                <StringFormat>yyyy-MM-dd HH:mm:ss</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text20>
          </Components>
          <Condition>{view调价信息.Tj_Date}</Condition>
          <Conditions isList="true" count="0" />
          <Name>GroupHeaderBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </GroupHeaderBand1>
        <DataBand1 Ref="23" type="DataBand" isKey="true">
          <Brush>Transparent</Brush>
          <BusinessObjectGuid isNull="true" />
          <ClientRectangle>0,5.1,19,0.5</ClientRectangle>
          <Components isList="true" count="10">
            <Text2 Ref="24" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>0,0,3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>62912031d4874470966de69109bf02d1</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text2</Name>
              <Page isRef="3" />
              <Parent isRef="23" />
              <Text>{view调价信息.Yp_Name}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>DataColumn</Type>
              <VertAlignment>Center</VertAlignment>
            </Text2>
            <Text4 Ref="25" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>3,0,2.4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>38e9b77060994f18803e33ec5767a384</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text4</Name>
              <Page isRef="3" />
              <Parent isRef="23" />
              <Text>{view调价信息.Mx_Gg}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text4>
            <Text6 Ref="26" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>5.4,0,2.4,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>f1d710dcd92f4e15a453fa43cbf92766</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text6</Name>
              <Page isRef="3" />
              <Parent isRef="23" />
              <Text>{view调价信息.Mx_Cd}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text6>
            <Text8 Ref="27" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>7.8,0,1.8,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>13994b6f306b43249c4b3edba34504d5</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text8</Name>
              <Page isRef="3" />
              <Parent isRef="23" />
              <Text>{view调价信息.Mx_Gyzz}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text8>
            <Text10 Ref="28" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>9.6,0,2.2,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>fdf1ed9b86f74932ac9ecab58eec946b</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text10</Name>
              <Page isRef="3" />
              <Parent isRef="23" />
              <Text>{view调价信息.Yp_Yxq}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="29" type="CustomFormat" isKey="true">
                <StringFormat>yyyy-MM-dd</StringFormat>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text10>
            <Text12 Ref="30" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>11.8,0,1.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>6a1e5ec4c5364f7cbcb9c00c02b34ac6</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text12</Name>
              <Page isRef="3" />
              <Parent isRef="23" />
              <Text>{view调价信息.Tj_Yk_Sl}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="31" type="CustomFormat" isKey="true">
                <StringFormat>0.####</StringFormat>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text12>
            <Text14 Ref="32" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>13.3,0,1,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>07c2a56efbcf4ae29e28b933998ca823</Guid>
              <HorAlignment>Center</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text14</Name>
              <Page isRef="3" />
              <Parent isRef="23" />
              <Text>{view调价信息.Mx_XsDw}</Text>
              <TextBrush>Black</TextBrush>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text14>
            <Text16 Ref="33" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>14.3,0,1.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>3c09d1c64bdf457ea7491691f782977f</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text16</Name>
              <Page isRef="3" />
              <Parent isRef="23" />
              <Text>{view调价信息.Tj_Pfj_Old}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="34" type="CustomFormat" isKey="true">
                <StringFormat>0.00##</StringFormat>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text16>
            <Text18 Ref="35" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>15.9,0,1.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <Guid>73c76b125a85449a8e0d80d68c624765</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text18</Name>
              <Page isRef="3" />
              <Parent isRef="23" />
              <Text>{view调价信息.Tj_Pfj_New}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="36" type="CustomFormat" isKey="true">
                <StringFormat>0.00##</StringFormat>
              </TextFormat>
              <TextOptions>HotkeyPrefix=None, LineLimit=False, RightToLeft=False, Trimming=None, WordWrap=True, Angle=0, FirstTabOffset=40, DistanceBetweenTabs=20,</TextOptions>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text18>
            <Text29 Ref="37" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <CanGrow>True</CanGrow>
              <ClientRectangle>17.5,0,1.5,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,10.5,Regular,Point,False,134</Font>
              <GrowToHeight>True</GrowToHeight>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text29</Name>
              <Page isRef="3" />
              <Parent isRef="23" />
              <Text>{view调价信息.Tj_Yk_PfCe}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="38" type="CustomFormat" isKey="true">
                <StringFormat>0.00##</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text29>
          </Components>
          <Conditions isList="true" count="0" />
          <DataRelationName isNull="true" />
          <DataSourceName>view调价信息</DataSourceName>
          <Filters isList="true" count="0" />
          <Name>DataBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
          <Sort isList="true" count="0" />
        </DataBand1>
        <GroupFooterBand1 Ref="39" type="GroupFooterBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,6.4,19,0.5</ClientRectangle>
          <Components isList="true" count="3">
            <Text24 Ref="40" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,14.3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Bold,Point,False,134</Font>
              <Guid>9ff2fb4f2c4642df913d87e91059d78d</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text24</Name>
              <Page isRef="3" />
              <Parent isRef="39" />
              <Text>合计：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text24>
            <Text25 Ref="41" type="Text" isKey="true">
              <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>14.3,0,1.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Bold,Point,False,134</Font>
              <Guid>cf40de8fe0d54959966c4d57f4d1e722</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text25</Name>
              <Page isRef="3" />
              <Parent isRef="39" />
              <Text>批发差额：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text25>
            <Text26 Ref="42" type="Text" isKey="true">
              <Border>Top, Right, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15.9,0,3.1,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Bold,Point,False,134</Font>
              <Guid>7a156aa2c7ef4dc1ac9afacf182d0680</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text26</Name>
              <Page isRef="3" />
              <Parent isRef="39" />
              <Text>{Sum(GroupHeaderBand1,view调价信息.Tj_Yk_PfCe)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="43" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text26>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>GroupFooterBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </GroupFooterBand1>
        <ReportSummaryBand1 Ref="44" type="ReportSummaryBand" isKey="true">
          <Brush>Transparent</Brush>
          <ClientRectangle>0,7.7,19,0.5</ClientRectangle>
          <Components isList="true" count="3">
            <Text21 Ref="45" type="Text" isKey="true">
              <Border>All;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>0,0,14.3,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Bold,Point,False,134</Font>
              <Guid>a6162526004043dbb584fe360f3482c2</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text21</Name>
              <Page isRef="3" />
              <Parent isRef="44" />
              <Text>总计：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text21>
            <Text22 Ref="46" type="Text" isKey="true">
              <Border>Top, Left, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>14.3,0,1.6,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Bold,Point,False,134</Font>
              <Guid>16923338fc7b4547b9cf0f0a09146d04</Guid>
              <Margins>0,0,0,0</Margins>
              <Name>Text22</Name>
              <Page isRef="3" />
              <Parent isRef="44" />
              <Text>批发差额：</Text>
              <TextBrush>Black</TextBrush>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text22>
            <Text23 Ref="47" type="Text" isKey="true">
              <Border>Top, Right, Bottom;Black;1;Solid;False;4;Black</Border>
              <Brush>Transparent</Brush>
              <ClientRectangle>15.9,0,3.1,0.5</ClientRectangle>
              <Conditions isList="true" count="0" />
              <Font>宋体,9,Bold,Point,False,134</Font>
              <Guid>a4927b01c3074d38901fe1c98e1b37e9</Guid>
              <HorAlignment>Right</HorAlignment>
              <Margins>0,0,0,0</Margins>
              <Name>Text23</Name>
              <Page isRef="3" />
              <Parent isRef="44" />
              <Text>{Sum(DataBand1,view调价信息.Tj_Yk_PfCe)}</Text>
              <TextBrush>Black</TextBrush>
              <TextFormat Ref="48" type="CustomFormat" isKey="true">
                <StringFormat>0.00</StringFormat>
              </TextFormat>
              <Type>Expression</Type>
              <VertAlignment>Center</VertAlignment>
            </Text23>
          </Components>
          <Conditions isList="true" count="0" />
          <Name>ReportSummaryBand1</Name>
          <Page isRef="3" />
          <Parent isRef="3" />
        </ReportSummaryBand1>
      </Components>
      <Conditions isList="true" count="0" />
      <Guid>fc74e842fd424c61a1df1cc626602847</Guid>
      <Margins>1,1,1,1</Margins>
      <Name>Page1</Name>
      <PageHeight>29.7</PageHeight>
      <PageWidth>21</PageWidth>
      <PaperSize>A4</PaperSize>
      <Report isRef="0" />
      <Watermark Ref="49" type="Stimulsoft.Report.Components.StiWatermark" isKey="true">
        <Font>Arial,100</Font>
        <TextBrush>[50:0:0:0]</TextBrush>
      </Watermark>
    </Page1>
  </Pages>
  <PrinterSettings Ref="50" type="Stimulsoft.Report.Print.StiPrinterSettings" isKey="true" />
  <ReferencedAssemblies isList="true" count="8">
    <value>System.Dll</value>
    <value>System.Drawing.Dll</value>
    <value>System.Windows.Forms.Dll</value>
    <value>System.Data.Dll</value>
    <value>System.Xml.Dll</value>
    <value>Stimulsoft.Controls.Dll</value>
    <value>Stimulsoft.Base.Dll</value>
    <value>Stimulsoft.Report.Dll</value>
  </ReferencedAssemblies>
  <ReportAlias>药库调价查询批发价</ReportAlias>
  <ReportChanged>7/21/2014 5:32:19 PM</ReportChanged>
  <ReportCreated>1/12/2012 3:23:47 PM</ReportCreated>
  <ReportFile>E:\正在进行时\唐山\正在修改版\his2010\his2010\Rpt\药库调价查询.mrt</ReportFile>
  <ReportGuid>eceb0d3ee43d4396b54085f1cc643e95</ReportGuid>
  <ReportName>药库调价查询批发价</ReportName>
  <ReportUnit>Centimeters</ReportUnit>
  <ReportVersion>2011.2.1026</ReportVersion>
  <Script>using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using Stimulsoft.Controls;
using Stimulsoft.Base.Drawing;
using Stimulsoft.Report;
using Stimulsoft.Report.Dialogs;
using Stimulsoft.Report.Components;

namespace Reports
{
    public class Report : Stimulsoft.Report.StiReport
    {
        public Report()        {
            this.InitializeComponent();
        }

        #region StiReport Designer generated code - do not modify
        #endregion StiReport Designer generated code - do not modify
    }
}
</Script>
  <ScriptLanguage>CSharp</ScriptLanguage>
  <Styles isList="true" count="0" />
</StiSerializer>