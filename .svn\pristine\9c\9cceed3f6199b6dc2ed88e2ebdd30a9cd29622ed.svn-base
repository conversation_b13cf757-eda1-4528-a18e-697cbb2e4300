﻿更新说明

更新日期:2025-07-15
1、药库药房入库扫码入库功能增加批量扫码功能
2、追溯码扫码出库支持批量扫码，扫码间隔建议500ms
===============================
更新日期:2025-06-30
1、优化扫码出库，如果追溯码入库扫码，出库无需按药品顺序扫码
2、科室增加科室属性、地址、是否启用
3、药房发药发药扫码时，拆零药品检测已扫码数量是否超过药品包装数量
4、扫追溯码验证药品标识码和追溯码是否正确
5、增加河北医保乡医上传功能
===============================
更新日期:2025-06-12
1、优化医保就诊查询和明细对账查询中冲正流程
2、门诊住院增加医保钱包支付字段
3、门诊医保报销不再导入负处方
4、医保耗材对照改成分页显示
5、医保目录对照模块如果西成药的批准文号和包装数量和医保的不一致，会做为疑点贯标数据标红显示
6、增加1321诊疗目录下载
7、发药扫追溯码码时自动把追溯码前7位赋值药品字典的药品标识码
8、根据医保要求门诊结算和住院结算上传药品追溯码数量和耗材追溯码数量
===============================
更新日期:2025-05-29
1、门诊收费增加显示收费方式
2、根据医保门诊结算回参扩充本地库基金支付类型fund_pay_type
===============================
更新日期:2025-05-19
1、药房出入库台帐增加追溯码显示
2、重构药库出入库台帐
===============================
更新日期:2025-05-08
1、河北医保日对账自动任务
===============================
更新日期:2025-05-07
1、药库入库和药房入库接入码上放心
2、河北医保接入一码付
3、河北医保进销存上传3505增加feedetl_sn
===============================
更新日期:2025-04-14
1、门诊辅助增加历史病历查询
2、修复门诊一键录入历史处方时某个药品库存不足时，无法继续生成其他项目
===============================
更新日期:2025-04-08
1、门诊日结查询增加导出功能
2、门诊医生站增加AI辅助功能
3、重构药库调拨药房模块
4、重构药库科室支领、药房科室支领模块
5、重构药库退供应商、药房退供应商
6、重构药房退回药库模块
===============================
更新日期:2025-02-27
1、单独区分外配处方章
2、门诊用药查询可以根据药品，药品最小数量查询处方
3、重写药房日结
4、重写项目售价查询
5、增加诊疗调价模块，可立即生效和延时生效
===============================
更新日期:2025-02-12
1、允许添加重名医生
2、医护字典增加药师
3、医保医生对照增加导入药师和对照药师
4、增加医生允许修改门诊处方金额配置
5、门诊用药查询增加处方执行性质筛选条件
6、门诊用药查询增加批量导出病历和处方
===============================
更新日期:2025-01-09
1、修改医保结算清单上传otp_wm_dise，wm_dise_code的赋值
===============================
更新日期:2025-01-06
1、增加河北双通道电子处方流转功能
2、国家电子处方流转模块修复读卡流转报错
3、国家电子处方流转模块修复中药流转上传
4、修改河北医保目录下载报错的问题
5、医保手动对账增加汇总
6、全国电子处方流转修改中医诊断和证候问题
7、增加出院检查床位费数量配置
8、追溯码扫码增加20长度验证，增加是否入库检测
===============================
更新日期:2024-12-30
1、紧急修复医保token保留时间
===============================
更新日期:2024-12-26
1、紧急修复门诊处方一键导入历史医嘱时，有输液单时有时会报错的问题
===============================
更新日期:2024-12-23
1、修复医保进销存上传中有结算撤销数据时，销售记录显示重复问题
2、住院报销增加票据配置
3、河北票据增加医保钱包支付金额
4、修复门诊药品开方时，拆零药品计算用药天数的Bug
5、修复启用重复挂号检测时，已作废的挂号记录也检查重复挂号
===============================
更新日期:2024-12-16
1、修复药库和药房入库修改报错的Bug
2、优化医保目录下载选择
3、门诊收费检测医生召回处方修改是否刷新
4、修复住院电子票据药品小类问题
5、修复中药外配处方修改付数检测库存的Bug
6、门诊医生站诊断类型和处方性质可以记忆
7、药品销售统计增加按报销类别查询条件
8、医保模块增加报销明细查询，需要赋权限
9、门诊病历增加电话
10、增加门诊挂号是否检测重复挂号的配置
===============================
更新日期:2024-12-02
1、门诊处方增加时间点
2、诊疗项目字典增加是否是医嘱选项
3、住院医嘱不显示也不打印非医嘱诊疗
4、药房住院发药增加汇总打印
===============================
更新日期:2024-11-27
1、医保电子处方流转只查询有药品的外配处方
2、电子处方上传时未对照药品提示
3、住院医嘱增加医嘱全选功能
4、门诊电子病历增加既往史，过敏史，处置
5、门诊病例模版增加既往史，过敏史，处置
6、门诊电子病例增加自定义报表
===============================
更新日期:2024-11-14
1、药品销售统计增加可以批号有效期选择，增加批号有效期报表
2、药品销售统计表带批号、科室收入统计加入自定义报表
===============================
更新日期:2024-11-08
1、修复护士站汇总领药未选择所属药房报错Bug
2、完善药品字典导入导出功能
3、药房入库单、药品销售统计表加入自定义报表
4、开发新版住院药房发药模块，可扫追溯码
===============================
更新日期:2024-10-31
1、修复外配处方上传空白的Bug
2、系统设置增加医院印章上传，处方增加医院印章
===============================
更新日期:2024-10-30
1、医保进销存上传显示追溯码数量
2、修复医保电子处方流转Bug
3、挂号关联门诊病历时，打印病历合并所有处方的诊疗和药品
4、外配处方可以开无库存药品
===============================
更新日期:2024-10-10
1、紧急修复门诊医生站证件类别有时候为空的Bug
===============================
更新日期:2024-10-08
1、修复门诊挂号更新已就诊的Bug
===============================
更新日期:2024-09-26
1、启用全新的药库盘点查询
2、重构药库批发模块，增加扫追溯码
3、重构药房批发模块，增加扫追溯码
4、门诊历史处方一键导入时，关联门诊输液内容
===============================
更新日期:2024-09-14
1、修复门诊输液如果医生不填用法用量时，配药报错的Bug
2、门诊医生开药界面增加用药天数计算开方数量勾选框，勾选根据天数计算数量，不勾选根据数量计算天数
3、启用全新的药房盘点查询
===============================
更新日期:2024-09-11
1、物资字典物资名称扩充为100个汉字
2、新增医保接口3512定点医药机构入库商品追溯信息查询功能
3、新增医保接口3508,3509,3510,3511,3512功能模块
4、新增内蒙古医保进销存批量接口3501A,3502A,3503A,3504A,3505A,3506A,3507A
5、关掉老版本药库入库、药房入库入口
===============================
更新日期:2024-09-04
1、门诊药房发药中药单付数量和付数加红显示，西成药、卫材不显示
2、增加证件类别字典
3、住院病例录入增加证件类别选择框
4、门诊挂号增加证件类别选择框
5、门诊医生站加证件类别选择框
6、修复新版药库入库、药房入库删除药品明细数据再点完成报错的Bug
7、重构新版财务查询-药品销售统计
8、吉林医保票据增加套打样式二
9、根据药品销售单位和制剂单位来判断是否是拆零药品
10、药房发药等扫追溯码增加复制功能
11、门诊开处方增加填写病例功能
12、门诊开处方增加医保事前审核功能
===============================
更新日期:2024-08-26
1、历史处方增加一键导入药品诊疗所在处方功能
2、历史处方添加诊疗也可以关联耗材
3、药库入库查询和药房入库查询增加批准文号显示
===============================
更新日期:2024-08-19
1、门诊药房发药增加扫入追溯码
2、自动发药时门诊收费增加扫入追溯码
3、医保进销存上传销售和销售退货模块增加补充追溯码
4、医保进销存上传销售和销售退货增加上传追溯码功能
5、重构药房出入库台帐
7、重构药库入库模块，增加扫追溯码
8、重构药房入库模块，增加扫追溯码
9、门诊药房发药增加中药单付数量和付数
10、挂号增加设置有效天数时长
===============================
更新日期:2024-07-22
1、药房科室支领、门诊用药详单加入自定义报表
2、优化住院天数计算，增加住院天数计算参数
3、药房信息查询和药库信息查询增加药品小类查询
4、医生开处方增加年龄天数
5、修复河北医保接口限价查询Bug
6、门诊挂号查询增加显示支付方式缴费金额
7、一次挂号可只填写一次门诊病历(需要在系统设置的进行配置)
8、药库入库查询、药房入库查询、住院门诊发药综合查询增加药品小类查询
===============================
更新日期:2024-06-13
1、疾病字典编码改成自动生成
2、门诊药品增加医保限制使用范围提醒配置
3、目录对照和目录下载加结束日期
4、优化清算申请状态查询
5、修复添加药品明细时剂型不显示的Bug
6、通辽门诊刷脸05上传时改成01
===============================
更新日期:2024-05-27
1、重构药库调拨药房明细录入窗体
2、住院病案首页可以自动导入病例录入中的诊断
3、通辽刷脸验证改成电子医保凭证
===============================
更新日期:2024-05-13
1、诊疗模版可以选择小项，非全选的话就不是模板方式录入
2、门诊用药查询增加病历、发票和处方批量打印
3、吉林医保门诊退费增加读卡身份验证
4、增加门诊病历模版功能
5、增加自定义报表模块，需要赋权限
6、医保住院结算清单上传增加显示病案首页住院医生
7、门诊用药查询增加显示是否填写病历列
8、增加门诊处方是否根据经手人限制显示选项
9、优化西药手动对照，自动根据批准文号查询医保目录
===============================
更新日期:2024-04-17
1、紧急更新：修复药房盘点时门诊收费自动发药仍可发药的问题，只能在药房盘点完成，手动在门诊药房发药
===============================
更新日期:2024-04-15
1、重写科室工作站模块
2、修复挂号信息查询按姓名查询的Bug
3、调整药房入库明细和药库入库明细批发价和销售价计算方式
4、医保门诊报销票据增加多样式选择
5、调整门诊医技申请单分组问题
6、诊疗字典增加关联卫材管理，门诊处方录入诊疗时可以自动录入卫材
===============================
更新日期:2024-03-25
1、修改中医处方里中药名称字数多显示不全的Bug
2、住院电子病历模板增加患者联系方式
3、手术字典增加中西医标记
4、修改长期医嘱不能修改药品的Bug
5、修改长期医嘱不能修改诊疗的Bug
6、修复医保读卡时患者有多个待遇时获取错误待遇的问题
7、修复门诊医生站回车增加诊断疾病编码没保存的Bug
8、重新开发药库入库明细录入模块，允许在录入药品后修改有效期，价格
===============================
更新日期:2024-03-18
1、重构病区模块
2、药房门诊发药增加显示库存参数设置
3、软件设置增加可配置住院药品和诊疗录入可修改价格功能
===============================
更新日期:2024-03-11
1、医保进销存上传增加盘点，药库调拨药房，药房退药库，报损报溢
2、修复门诊录入药品时前一界面合计不累加的Bug
3、门诊和住院医嘱录入药品增加医保甲乙丙类别显示,需要在医保目录对照模块事先查询医保等级
4、系统初始化导入excel增加包装数量列
5、软件设置增加可配置门诊药品和诊疗录入可修改价格功能
6、增加医保电子处方流转模块
===============================
更新日期:2024-03-08
1、修复药房入库时录入药品前一界面合计不累加的Bug
===============================
更新日期:2024-03-06
1、修复慢特病备案不选择结束日期报错的Bug
2、修复诊疗模板明细数量大于1时，设置折扣价错误的Bug
3、修复新版门诊药品录入多药房的Bug
===============================
更新日期:2024-03-05
1、重新开发药房入库明细录入模块，允许在录入药品后修改有效期，价格
2、修复门诊草药乘以付数的Bug
===============================
更新日期:2024-02-29
1、病案首页可以选择是否只显示患者科室医生
2、住院诊疗退费可以退模版
===============================
更新日期:2024-02-27
1、门诊增加门诊病历词条功能
2、门诊病历可以使用词条
===============================
更新日期:2024-02-05
1、修改床位分配模块退床时床位未刷新的Bug
2、住院医嘱单增加诊断和主治医生
3、增加护士站汇总领药增加一个输液卡样式
4、增加护士站汇总领药增加输液瓶签打印
===============================
更新日期:2024-02-01
1、修改通辽刷脸支付的Bug
2、读卡工具栏增加扫脸
===============================
更新日期:2024-01-29
1、修改门诊用药查询按结账时间错误的问题
2、增加门诊处方A5竖版
3、住院结算清单上传增加中医病案首页上传
===============================
更新日期:2024-01-25
1、医院管理字典增加管理职务模块
2、药品明细增加处方职务列
3、医护字典增加管理职务列
4、住院临时医嘱和长期医嘱增加处方职务限制
5、修复Bug
===============================
更新日期:2024-01-22
1、增加住院医嘱模块
===============================
更新日期:2024-01-15
1、门诊用药查询增加病历修改
2、住院病例录入增加腕带打印
===============================
更新日期:2024-01-09
1、住院临时医嘱增加住院诊疗单打印
2、医保门诊慢特病备案结束时间可以不填
3、开发新版病例录入模块
4、医保住院诊断增加删除功能
===============================
更新日期:2024-01-04
1、修复内蒙古目录下载接口9102下载的Bug
2、统一各模块门诊处方打印样式
3、门诊药房发药只允许发当前操作员所属药房
===============================
更新日期:2024-01-02
1、修复体温单中皮试结果未打印的Bug
2、护士站汇总领药单按患者汇总样式修改，增加性别和年龄，增加用法用量
3、护士站汇总领药模块的输液卡增加用法用量
3、调整住院医技申请单
4、调整门诊医技申请单
5、增加河北医保刷脸支付功能
===============================
更新日期:2023-12-24
1、住院模块的医嘱录入改成临时医嘱
2、长期医嘱药品录入增加用法用量
3、长期医嘱界面显示用法用量
4、临时医嘱界面显示用法用量
5、修复护士站体温单身高体重相反的Bug
6、押金缴费单表格增加备注：请保留此票据，凭此缴费单办理出院
7、汇总领药增加年龄，性别，用法用量
8、住院药房发药显示用法用量
9、药房库存增加禁用功能，禁用后医生无法开出该药品
===============================
更新日期:2023-12-23
1、修复多药房情况下自动发药的Bug
===============================
更新日期:2023-12-14
1、门诊收费增加处方总金额和明细金额验证
===============================
更新日期:2023-12-08
1、门诊处置单配药自动进入门诊输液模块
2、吉林医保诊疗服务下载增加路径选择功能
===============================
更新日期:2023-12-05
1、门诊结算时医保无现金的不抹零
2、增加医保对账手动结算数据功能
===============================
更新日期:2023-11-30
1、增加门诊输液模块
2、增加医保刷脸识别
===============================
更新日期:2023-11-21
1、修复门诊收费的Bug
===============================
更新日期:2023-11-20
1、增加吉林门诊医保结算单打印
===============================
更新日期:2023-11-17
1、修复门诊医生站选择挂号患者时，电话和出生日期没有自动赋值的Bug
2、修复门诊诊疗修复数量时报错的Bug
===============================
更新日期:2023-11-14
1、修改数据库维护的初始化模版增加诊疗类别列
2、增加门诊收费结算的抹零方式
===============================
更新日期:2023-11-13
1、更新内蒙古医保读卡动态库
2、软件设置增加通过采购价自动计算销售批发价，药品入库销售价加成，药品入库批发价加成
3、门诊收费时，如果是医保结算患者自动填入基金统筹支付和个人账户支出的合计
4、药品字典可以修改药品简称
5、修复医保门诊挂号时如果患者没有正常参保报错的Bug
===============================
更新日期:2023-10-29
1、门诊医生站新增可以修改医生设置
2、医保接口门诊和住院自费患者上传
===============================
更新日期:2023-10-25
1、门诊医生开处方增加历史处方和处方模版
2、门诊模块增加门诊模版管理，需要在权限管理模块赋权限
===============================
更新日期:2023-10-16
1、药品价格改成6位小数
2、修复门诊日结后收费速查时，查不到费用明细的Bug
3、增加58mm热敏门诊小票
===============================
版本号：4.0.0.673 2023-10-07更新 
1、医保目录下载增加查询功能
2、医保目录下载增加1312查询医保等级功能
3、更新药材字典
4、药房采购入库查询增加国家医保编码，如果没有在列表处点鼠标右键选重置样式
5、升级药房提醒和药库提醒
===============================
版本号：4.0.0.669 2023-08-22更新 
1、结算清单上传增加4104质控结果查询
2、结算清单上传增加3606DIP分组结果查询
3、门诊医生站可以修改
===============================
版本号：4.0.0.667 2023-08-07更新 
1、重新开发处方科室收入统计模块
2、增加门诊和住院报销模块增加事中模块
===============================
版本号：4.0.0.665 2023-07-19更新 
1、增加门诊收费查询功能模块
2、修改门诊慢特病下载报错的问题
===============================
版本号：4.0.0.662 2023-07-06更新 
1、诊疗字典增加停用功能
2、住院医嘱录入和门诊医嘱录入只显示启用的诊疗
3、修复门诊日结汇总支付方式汇总数的Bug
===============================
2023-06-13更新
1、医保慢特病目录增加手动输入功能
===============================
2023-05-25更新
1、修复门诊挂号的一些Bug
===============================
2023-05-24更新
1、医保结算清单上传门急诊诊断编码和名称修改
===============================
2023-05-22更新
1、医保挂号加现金的混合支付
2、修复医保挂号打印电子票价的Bug
===============================
2023-05-18更新
1、通辽发票号默认虚拟号
===============================
2023-05-17更新
1、门诊医保挂号暂时不能收卡余额为0的患者，医保挂号加现金的混合支付正在开发中
===============================
2023-05-16更新
1、修复门诊电子票据提示【收费项目代码不能为空】的Bug
===============================
2023-05-15更新
1、针对通辽4月底医保要求，医疗类别加入门诊（12）类别，门诊挂号，且明细只能有诊察费的要求，门诊挂号改造成可以直接进行医保报销
2、医生字典改成医护字典，增加医生护士的分类
3、医保科室管增加5102医执人员查询接口
4、诊疗项目删除增加验证
5、电子票据分类删除增加验证
===============================
2022-09-06更新
1、增加手术字典模块
===============================

