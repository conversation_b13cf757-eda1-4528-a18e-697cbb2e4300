﻿Imports System.Data.SqlClient
Imports System.Data.OleDb
Imports BaseClass

Public Class Zd_MzFp3
    Dim My_Cc As New BaseClass.C_Cc()         '取最大编码及简称的类
    Dim My_Dataset As New Data.DataSet
    Dim My_Adapter As New SqlDataAdapter

#Region "传参"
    Dim <PERSON>ert As Boolean
    Dim Rrow As DataRow
    Dim RZbtb As DataTable
    Dim Rtdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid
    Dim Rlb As C1.Win.C1Input.C1Label
    Dim Rzbadt As SqlDataAdapter
    Dim Rrc As C_RowChange
    Dim Rtree As TreeView
    Dim R_Count As Integer
#End Region

    Public Sub New(ByVal tinsert As Boolean, ByVal trow As DataRow, ByVal tZbtb As DataTable, ByVal ttdbgrid As C1.Win.C1TrueDBGrid.C1TrueDBGrid, ByVal tlb As C1.Win.C1Input.C1Label, ByVal tzbadt As SqlDataAdapter, ByRef trc As C_RowChange, ByRef ttree As TreeView, ByRef t_Count As Integer)

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        Rinsert = tinsert
        'Rdate = tdate
        Rrow = trow
        RZbtb = tZbtb
        Rtdbgrid = ttdbgrid
        Rlb = tlb
        Rzbadt = tzbadt
        Rrc = trc
        Rtree = ttree
        R_Count = t_Count
        AddHandler Rrc.RowChanged, New BaseClass.RowChangedHandler(AddressOf Data_Show)
        ' 在 InitializeComponent() 调用之后添加任何初始化。
    End Sub

    Protected Overrides Sub OnKeyDown(ByVal e As System.Windows.Forms.KeyEventArgs)
        MyBase.OnKeyDown(e)
        If e.Control = True And e.KeyCode = Keys.S Then
            Comm1.Select()
            Call Comm_Click(Comm1, Nothing)
        End If
    End Sub

    Private Sub Zd_MzFp3_FormClosed(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosedEventArgs) Handles Me.FormClosed
        RemoveHandler Rrc.RowChanged, New BaseClass.RowChangedHandler(AddressOf Data_Show)
        Me.Dispose()
    End Sub

    Private Sub Zd_MzFp3_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Call Form_Init()
        If Rinsert = True Then Call Data_Clear() Else Call Data_Show(Rrow)
    End Sub

#Region "窗体__事件"

    Private Sub Form_Init()
        '按扭初始化
        Call P_Comm(Me.Comm1)
        Call P_Comm(Me.Comm2)
        If My_Dataset.Tables("项目字典") IsNot Nothing Then My_Dataset.Tables("项目字典").Clear()
        With My_Adapter
            .SelectCommand = New SqlCommand("Select Xm_Code,Xm_Jc,Xm_Name From Zd_Ml_Xm3 where Xm_Code not in (select Xm_Code from Zd_MzFp2 where Yy_Code='" & HisVar.HisVar.WsyCode & "') and Xm_Dj<>0 Order By Xm_Jc", My_Cn)
            .Fill(My_Dataset, "项目字典")
        End With
        My_Dataset.Tables("项目字典").PrimaryKey = New DataColumn() {My_Dataset.Tables("项目字典").Columns("Xm_Code")}
        Dim My_Combo As C_Combo2 = New C_Combo2(C1Combo1, My_Dataset.Tables("项目字典").DefaultView, "Xm_Jc", "Xm_Code", 300)
        With My_Combo
            .Init_TDBCombo()
            .Init_Colum("Xm_Jc", "简称", 100, "左")
            .Init_Colum("Xm_Name", "项目名称", 150, "左")
            .Init_Colum("Xm_Code", "", 0, "中")
            .MaxDropDownItems(15)
            .SelectedIndex(-1)
        End With
    End Sub

#End Region

#Region "数据__编辑"

    Private Sub Data_Add()

        Dim My_NewRow As DataRow = RZbtb.NewRow
        If Trim(C1Combo1.Text) = "" Then
            MsgBox("请选择项目名称!", MsgBoxStyle.Critical, "提示")
            C1Combo1.Focus()
            Exit Sub
        End If

        With My_NewRow
            .Item("Yy_Code") = HisVar.HisVar.WsyCode
            .Item("Lb_Code") = Rtree.SelectedNode.Tag
            .Item("Xm_Code") = C1Combo1.SelectedValue                                '最大编码
            .Item("Xm_Name") = C1Combo1.Columns("Xm_Name").Value
            .Item("Xm_Jc") = C1Combo1.Columns("Xm_Jc").Value
        End With
        MsgBox("数据添加成功!", MsgBoxStyle.Information, "提示:")
        '数据保存
        Try
            RZbtb.Rows.Add(My_NewRow)
            Rtdbgrid.MoveLast()
            Rlb.Text = "∑=" + Rtdbgrid.Splits(0).Rows.Count.ToString
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
            Exit Sub
        Finally
            C1Combo1.Select()
        End Try


        '更新数据
        With Rzbadt.InsertCommand
            Try
                Dim I As Integer = 0
                For Each My_Para As SqlParameter In .Parameters
                    My_Para.Value = My_NewRow.Item(I)
                    I = I + 1
                Next
                Call P_Conn(True)
                .ExecuteNonQuery()
                My_NewRow.AcceptChanges()
            Catch ex As Exception
                Beep()
                MsgBox(ex.Message, MsgBoxStyle.Exclamation + MsgBoxStyle.OkOnly, "提示:")
                Exit Sub
            Finally
                C1Combo1.Select()
                Call P_Conn(False)
            End Try
        End With

        '清空记录--继续增加记录
        Call Data_Clear()
    End Sub

    Private Sub Tree_Add()
        R_Count = R_Count + 1
        Rtree.TopNode.Text = "项目字典(" + R_Count.ToString + ")"

        Dim V_NodeText As String = Rtree.SelectedNode.Text         '当前选中的节点
        V_NodeText = Mid(V_NodeText, 1, InStr(V_NodeText, "(") - 1)
        Rtree.SelectedNode.Text = V_NodeText + "(" + Rtdbgrid.Splits(0).Rows.Count.ToString + ")"
    End Sub

    Private Sub Data_Edit()
        Dim My_Row As DataRow = Rrow
        If Trim(C1Combo1.Text) = "" Then
            MsgBox("请选择项目名称!", MsgBoxStyle.Critical, "提示")
            C1Combo1.Focus()
            Exit Sub
        End If
        Try
            MsgBox("数据修改成功!", MsgBoxStyle.Information, "提示:")
            With My_Row
                .BeginEdit()
                .Item("Yy_Code") = HisVar.HisVar.WsyCode
                .Item("Lb_Code") = Rtree.SelectedNode.Tag
                .Item("Xm_Code") = C1Combo1.SelectedValue
                .Item("Xm_Name") = C1Combo1.Columns("Xm_Name").Value
                .Item("Xm_Jc") = C1Combo1.Columns("Xm_Jc").Value
                .EndEdit()
            End With
        Catch ex As Exception
            Beep()
            MsgBox(ex.Message, MsgBoxStyle.Critical, "错误提示:")
            My_Row.CancelEdit()
            Exit Sub
        Finally
            C1Combo1.Select()
        End Try

        '更新数据
        With Rzbadt.UpdateCommand
            Dim I As Integer = 0
            For Each My_Para As SqlParameter In .Parameters
                If I = .Parameters.Count - 1 Then
                    My_Para.Value = My_Row.Item("Xm_Code", DataRowVersion.Original)
                Else
                    My_Para.Value = My_Row.Item(I)
                    I = I + 1
                End If
            Next
            Try
                Call P_Conn(True)
                .ExecuteNonQuery()
                My_Row.AcceptChanges()
            Catch ex As Exception
                Beep()
                MsgBox(ex.Message, MsgBoxStyle.Critical + MsgBoxStyle.OkOnly, "提示:")
                Exit Sub
            Finally
                Me.C1Combo1.Select()
                Call P_Conn(False)
            End Try
        End With


    End Sub

#End Region

#Region "数据__操作"

    Private Sub Data_Clear()
        Rinsert = True
        C1Combo1.Text = ""
        Label12.Text = ""
        If My_Dataset.Tables("项目字典") IsNot Nothing Then My_Dataset.Tables("项目字典").Clear()
        With My_Adapter
            .SelectCommand = New SqlCommand("Select Xm_Code,Xm_Jc,Xm_Name From Zd_Ml_Xm3 where Xm_Code not in (select Xm_Code from Zd_MzFp2 where Yy_Code='" & HisVar.HisVar.WsyCode & "') and Xm_Dj<>0 Order By Xm_Jc", My_Cn)
            .Fill(My_Dataset, "项目字典")
        End With
        My_Dataset.Tables("项目字典").PrimaryKey = New DataColumn() {My_Dataset.Tables("项目字典").Columns("Xm_Code")}
        Dim My_Combo As C_Combo2 = New C_Combo2(C1Combo1, My_Dataset.Tables("项目字典").DefaultView, "Xm_Jc", "Xm_Code", 300)
        With My_Combo
            .Init_TDBCombo()
            .Init_Colum("Xm_Jc", "简称", 100, "左")
            .Init_Colum("Xm_Name", "项目名称", 150, "左")
            .Init_Colum("Xm_Code", "", 0, "中")
            .MaxDropDownItems(15)
            .SelectedIndex(-1)
        End With
    End Sub

    Private Sub Data_Show(ByVal tmp_Row As DataRow)
        Rinsert = False
        Rrow = tmp_Row
        With Rrow
            C1Combo1.Text = .Item("Xm_Jc") & ""
            Label12.Text = .Item("Xm_Name") & ""
        End With
    End Sub

#End Region

#Region "控件__动作"

    Private Sub Comm_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.Click, Comm2.Click
        Select Case sender.tag
            Case "保存"
                If Rinsert = True Then
                    Call Data_Add()
                    Call Tree_Add()
                Else
                    Call Data_Edit()
                End If
            Case "取消"
                Me.Close()
        End Select
    End Sub




#End Region

#Region "自定义按扭"

    Private Sub P_Comm(ByVal Bt As Button)
        With Bt
            Select Case .Tag
                Case "保存"
                    .Top = 3
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")
                    .Text = "            &B"

                Case "取消"
                    .Location = New Point(Comm1.Left + Comm1.Width + 4, Comm1.Top)
                    .BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
                    .Width = Me.Comm1.Width
                    .Text = "            &C"
            End Select
            .FlatStyle = FlatStyle.Flat
            .FlatAppearance.BorderSize = 0
            .BackgroundImageLayout = ImageLayout.Center
        End With

    End Sub

    Private Sub Comm_MouseEnter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.MouseEnter, Comm2.MouseEnter
        Select Case sender.tag
            Case "保存"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存2")
            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消2")
        End Select

    End Sub

    Private Sub Comm_MouseLeave(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Comm1.MouseLeave, Comm2.MouseLeave
        Select Case sender.tag
            Case "保存"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")
            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
        End Select
    End Sub

    Private Sub Comm_MouseDown(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm1.MouseDown, Comm2.MouseDown
        Select Case sender.tag
            Case "保存"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存3")

            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消3")
        End Select
    End Sub

    Private Sub Comm_MouseUp(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Comm1.MouseUp, Comm2.MouseUp
        Select Case sender.tag
            Case "保存"
                Comm1.BackgroundImage = MyResources.C_Resources.getimage("命令_保存1")
            Case "取消"
                Comm2.BackgroundImage = MyResources.C_Resources.getimage("命令_取消1")
        End Select
    End Sub

#End Region

#Region "输入法设置"
    '中文
    Private Sub C1TextBox1_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs)
        InputLanguage.CurrentInputLanguage = HisVar.HisVar.InputCode
    End Sub

    '英文
    Private Sub C1DateEdit1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        InputLanguage.CurrentInputLanguage = InputLanguage.InstalledInputLanguages(0)
    End Sub
#End Region





    Private Sub C1Combo1_RowChange(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1Combo1.RowChange
        If C1Combo1.Text = "" Then
            Label12.Text = ""
        Else
            Label12.Text = C1Combo1.Columns("Xm_Name").Value
        End If
    End Sub

    Private Sub C1Combo1_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles C1Combo1.TextChanged
        If C1Combo1.Text = "" Then
            Label12.Text = ""
        Else
            Label12.Text = C1Combo1.Columns("Xm_Name").Value
        End If
    End Sub




End Class