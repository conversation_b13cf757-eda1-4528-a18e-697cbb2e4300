﻿/**  版本信息模板在安装目录下，可自行修改。
* M_LIS_Test1.cs
*
* 功 能： N/A
* 类 名： M_LIS_Test1
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016/10/21 15:19:57   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace ModelOld
{
	/// <summary>
	/// M_LIS_Test1:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class M_LIS_Test1
	{
		public M_LIS_Test1()
		{}
		#region Model
		private string _test_code;
		private string _test_lb;
		private string _his_code;
		private string _ry_name;
		private string _ry_sfzh;
		private string _ry_sex;
		private string _jkkno;
		private int? _ry_age;
		private int? _ry_age_month;
		private int? _ry_age_week;
		private string _bxlb_code;
		private string _ks_code;
		private string _bc_code;
		private string _testxm_code;
		private string _testsample;
		private string _testsample_barcode;
		private string _sendjsr;
		private string _senddoctor;
		private DateTime? _sendtime;
		private string _getjsr;
		private string _getdoctor;
		private DateTime? _gettime;
		private string _testjsr;
		private DateTime? _testtime;
		private string _checkjsr;
		private DateTime? _checktime;
		private int? _lisprinttimes;
		private int? _selfprinttimes;
		private string _diagnose;
		private string _memo;
		private byte[] _testitem1;
		private string _teststate;
		/// <summary>
		/// 
		/// </summary>
		public string Test_Code
		{
			set{ _test_code=value;}
			get{return _test_code;}
		}
		/// <summary>
		/// 门诊、住院、体检
		/// </summary>
		public string Test_Lb
		{
			set{ _test_lb=value;}
			get{return _test_lb;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string His_Code
		{
			set{ _his_code=value;}
			get{return _his_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Ry_Name
		{
			set{ _ry_name=value;}
			get{return _ry_name;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Ry_Sfzh
		{
			set{ _ry_sfzh=value;}
			get{return _ry_sfzh;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Ry_Sex
		{
			set{ _ry_sex=value;}
			get{return _ry_sex;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string JKkNo
		{
			set{ _jkkno=value;}
			get{return _jkkno;}
		}
		/// <summary>
		/// 
		/// </summary>
		public int? Ry_Age
		{
			set{ _ry_age=value;}
			get{return _ry_age;}
		}
		/// <summary>
		/// 
		/// </summary>
		public int? Ry_Age_Month
		{
			set{ _ry_age_month=value;}
			get{return _ry_age_month;}
		}
		/// <summary>
		/// 
		/// </summary>
		public int? Ry_Age_Week
		{
			set{ _ry_age_week=value;}
			get{return _ry_age_week;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Bxlb_Code
		{
			set{ _bxlb_code=value;}
			get{return _bxlb_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Ks_Code
		{
			set{ _ks_code=value;}
			get{return _ks_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Bc_Code
		{
			set{ _bc_code=value;}
			get{return _bc_code;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string TestXm_Code
		{
			set{ _testxm_code=value;}
			get{return _testxm_code;}
		}
		/// <summary>
		/// 检验标本
		/// </summary>
		public string TestSample
		{
			set{ _testsample=value;}
			get{return _testsample;}
		}
		/// <summary>
		/// 条形码
		/// </summary>
		public string TestSample_BarCode
		{
			set{ _testsample_barcode=value;}
			get{return _testsample_barcode;}
		}
		/// <summary>
		/// 送检人
		/// </summary>
		public string SendJsr
		{
			set{ _sendjsr=value;}
			get{return _sendjsr;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string SendDoctor
		{
			set{ _senddoctor=value;}
			get{return _senddoctor;}
		}
		/// <summary>
		/// 送检时间
		/// </summary>
		public DateTime? SendTime
		{
			set{ _sendtime=value;}
			get{return _sendtime;}
		}
		/// <summary>
		/// 采集人
		/// </summary>
		public string GetJsr
		{
			set{ _getjsr=value;}
			get{return _getjsr;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string GetDoctor
		{
			set{ _getdoctor=value;}
			get{return _getdoctor;}
		}
		/// <summary>
		/// 采集时间
		/// </summary>
		public DateTime? GetTime
		{
			set{ _gettime=value;}
			get{return _gettime;}
		}
		/// <summary>
		/// 检验人
		/// </summary>
		public string TestJsr
		{
			set{ _testjsr=value;}
			get{return _testjsr;}
		}
		/// <summary>
		/// 检验结果生成、导入时间
		/// </summary>
		public DateTime? TestTime
		{
			set{ _testtime=value;}
			get{return _testtime;}
		}
		/// <summary>
		/// 审核人
		/// </summary>
		public string CheckJsr
		{
			set{ _checkjsr=value;}
			get{return _checkjsr;}
		}
		/// <summary>
		/// 审核时间
		/// </summary>
		public DateTime? CheckTime
		{
			set{ _checktime=value;}
			get{return _checktime;}
		}
		/// <summary>
		/// 打印次数
		/// </summary>
		public int? LisPrintTimes
		{
			set{ _lisprinttimes=value;}
			get{return _lisprinttimes;}
		}
		/// <summary>
		/// 自助打印次数（有限制）
		/// </summary>
		public int? SelfPrintTimes
		{
			set{ _selfprinttimes=value;}
			get{return _selfprinttimes;}
		}
		/// <summary>
		/// 诊断
		/// </summary>
		public string Diagnose
		{
			set{ _diagnose=value;}
			get{return _diagnose;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Memo
		{
			set{ _memo=value;}
			get{return _memo;}
		}
		/// <summary>
		/// 
		/// </summary>
		public byte[] TestItem1
		{
			set{ _testitem1=value;}
			get{return _testitem1;}
		}
		/// <summary>
		/// 录入、申请、采集、检验、完成
		/// </summary>
		public string TestState
		{
			set{ _teststate=value;}
			get{return _teststate;}
		}
		#endregion Model

	}
}

