﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class MaterialsDict12
    Inherits HisControl.BaseChild

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.T_Line1 = New System.Windows.Forms.Label()
        Me.T_Line2 = New System.Windows.Forms.Label()
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel()
        Me.MC_Code_Tb = New CustomControl.MyTextBox()
        Me.MC_Name_Tb = New CustomControl.MyTextBox()
        Me.MC_Py_Tb = New CustomControl.MyTextBox()
        Me.MC_Father_Tb = New CustomControl.MyTextBox()
        Me.MC_HaveChild_Cb = New System.Windows.Forms.CheckBox()
        Me.Button1 = New CustomControl.MyButton()
        Me.Button2 = New CustomControl.MyButton()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.TableLayoutPanel1.SuspendLayout()
        Me.Panel1.SuspendLayout()
        Me.SuspendLayout()
        '
        'T_Line1
        '
        Me.T_Line1.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.T_Line1.BackColor = System.Drawing.SystemColors.Control
        Me.T_Line1.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.TableLayoutPanel1.SetColumnSpan(Me.T_Line1, 4)
        Me.T_Line1.Location = New System.Drawing.Point(3, 46)
        Me.T_Line1.Name = "T_Line1"
        Me.T_Line1.Size = New System.Drawing.Size(434, 2)
        Me.T_Line1.TabIndex = 133
        Me.T_Line1.Text = "Label1"
        '
        'T_Line2
        '
        Me.T_Line2.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.T_Line2.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.TableLayoutPanel1.SetColumnSpan(Me.T_Line2, 4)
        Me.T_Line2.Location = New System.Drawing.Point(3, 104)
        Me.T_Line2.Name = "T_Line2"
        Me.T_Line2.Size = New System.Drawing.Size(434, 2)
        Me.T_Line2.TabIndex = 158
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.ColumnCount = 5
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 20.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 200.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 200.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 20.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle())
        Me.TableLayoutPanel1.Controls.Add(Me.T_Line1, 0, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.T_Line2, 0, 5)
        Me.TableLayoutPanel1.Controls.Add(Me.MC_Code_Tb, 1, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.MC_Name_Tb, 1, 3)
        Me.TableLayoutPanel1.Controls.Add(Me.MC_Py_Tb, 2, 3)
        Me.TableLayoutPanel1.Controls.Add(Me.MC_Father_Tb, 2, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.MC_HaveChild_Cb, 1, 4)
        Me.TableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(0, 0)
        Me.TableLayoutPanel1.Margin = New System.Windows.Forms.Padding(0)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.RowCount = 7
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 18.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 2.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 2.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 28.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 20.0!))
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(429, 136)
        Me.TableLayoutPanel1.TabIndex = 0
        '
        'MC_Code_Tb
        '
        Me.MC_Code_Tb.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.MC_Code_Tb.Captain = "编    码"
        Me.MC_Code_Tb.CaptainBackColor = System.Drawing.Color.Transparent
        Me.MC_Code_Tb.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MC_Code_Tb.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.MC_Code_Tb.CaptainWidth = 60.0!
        Me.MC_Code_Tb.ContentForeColor = System.Drawing.Color.Black
        Me.MC_Code_Tb.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.MC_Code_Tb.Location = New System.Drawing.Point(23, 22)
        Me.MC_Code_Tb.Multiline = False
        Me.MC_Code_Tb.Name = "MC_Code_Tb"
        Me.MC_Code_Tb.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.MC_Code_Tb.ReadOnly = False
        Me.MC_Code_Tb.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.MC_Code_Tb.SelectionStart = 0
        Me.MC_Code_Tb.SelectStart = 0
        Me.MC_Code_Tb.Size = New System.Drawing.Size(194, 20)
        Me.MC_Code_Tb.TabIndex = 159
        Me.MC_Code_Tb.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MC_Code_Tb.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.MC_Code_Tb.Watermark = Nothing
        '
        'MC_Name_Tb
        '
        Me.MC_Name_Tb.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.MC_Name_Tb.Captain = "名    称"
        Me.MC_Name_Tb.CaptainBackColor = System.Drawing.Color.Transparent
        Me.MC_Name_Tb.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MC_Name_Tb.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.MC_Name_Tb.CaptainWidth = 60.0!
        Me.MC_Name_Tb.ContentForeColor = System.Drawing.Color.Black
        Me.MC_Name_Tb.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.MC_Name_Tb.Location = New System.Drawing.Point(23, 52)
        Me.MC_Name_Tb.Multiline = False
        Me.MC_Name_Tb.Name = "MC_Name_Tb"
        Me.MC_Name_Tb.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.MC_Name_Tb.ReadOnly = False
        Me.MC_Name_Tb.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.MC_Name_Tb.SelectionStart = 0
        Me.MC_Name_Tb.SelectStart = 0
        Me.MC_Name_Tb.Size = New System.Drawing.Size(194, 20)
        Me.MC_Name_Tb.TabIndex = 0
        Me.MC_Name_Tb.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MC_Name_Tb.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.MC_Name_Tb.Watermark = Nothing
        '
        'MC_Py_Tb
        '
        Me.MC_Py_Tb.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.MC_Py_Tb.Captain = "拼音简称"
        Me.MC_Py_Tb.CaptainBackColor = System.Drawing.Color.Transparent
        Me.MC_Py_Tb.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MC_Py_Tb.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.MC_Py_Tb.CaptainWidth = 60.0!
        Me.MC_Py_Tb.ContentForeColor = System.Drawing.Color.Black
        Me.MC_Py_Tb.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.MC_Py_Tb.Location = New System.Drawing.Point(223, 52)
        Me.MC_Py_Tb.Multiline = False
        Me.MC_Py_Tb.Name = "MC_Py_Tb"
        Me.MC_Py_Tb.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.MC_Py_Tb.ReadOnly = False
        Me.MC_Py_Tb.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.MC_Py_Tb.SelectionStart = 0
        Me.MC_Py_Tb.SelectStart = 0
        Me.MC_Py_Tb.Size = New System.Drawing.Size(194, 20)
        Me.MC_Py_Tb.TabIndex = 161
        Me.MC_Py_Tb.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MC_Py_Tb.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.MC_Py_Tb.Watermark = Nothing
        '
        'MC_Father_Tb
        '
        Me.MC_Father_Tb.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.MC_Father_Tb.Captain = "父    级"
        Me.MC_Father_Tb.CaptainBackColor = System.Drawing.Color.Transparent
        Me.MC_Father_Tb.CaptainFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MC_Father_Tb.CaptainForeColor = System.Drawing.SystemColors.ControlText
        Me.MC_Father_Tb.CaptainWidth = 60.0!
        Me.MC_Father_Tb.ContentForeColor = System.Drawing.Color.Black
        Me.MC_Father_Tb.DisabledForeColor = System.Drawing.Color.FromArgb(CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer), CType(CType(183, Byte), Integer))
        Me.MC_Father_Tb.Location = New System.Drawing.Point(223, 22)
        Me.MC_Father_Tb.Multiline = False
        Me.MC_Father_Tb.Name = "MC_Father_Tb"
        Me.MC_Father_Tb.PasswordChar = Global.Microsoft.VisualBasic.ChrW(0)
        Me.MC_Father_Tb.ReadOnly = False
        Me.MC_Father_Tb.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.MC_Father_Tb.SelectionStart = 0
        Me.MC_Father_Tb.SelectStart = 0
        Me.MC_Father_Tb.Size = New System.Drawing.Size(194, 20)
        Me.MC_Father_Tb.TabIndex = 171
        Me.MC_Father_Tb.TextFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MC_Father_Tb.VerticalAlign = C1.Win.C1Input.VerticalAlignEnum.Middle
        Me.MC_Father_Tb.Watermark = Nothing
        '
        'MC_HaveChild_Cb
        '
        Me.MC_HaveChild_Cb.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.MC_HaveChild_Cb.AutoSize = True
        Me.MC_HaveChild_Cb.CheckAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.MC_HaveChild_Cb.Location = New System.Drawing.Point(23, 82)
        Me.MC_HaveChild_Cb.Name = "MC_HaveChild_Cb"
        Me.MC_HaveChild_Cb.Size = New System.Drawing.Size(72, 16)
        Me.MC_HaveChild_Cb.TabIndex = 1
        Me.MC_HaveChild_Cb.Text = "有无子级"
        Me.MC_HaveChild_Cb.UseVisualStyleBackColor = True
        '
        'Button1
        '
        Me.Button1.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.Button1.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.Button1.DialogResult = System.Windows.Forms.DialogResult.None
        Me.Button1.Location = New System.Drawing.Point(274, 1)
        Me.Button1.Name = "Button1"
        Me.Button1.Size = New System.Drawing.Size(67, 27)
        Me.Button1.TabIndex = 0
        Me.Button1.Tag = "保存"
        Me.Button1.Text = "保存"
        '
        'Button2
        '
        Me.Button2.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.Button2.ButtonImageSize = CustomControl.MyButton.imageSize.small
        Me.Button2.DialogResult = System.Windows.Forms.DialogResult.Cancel
        Me.Button2.Location = New System.Drawing.Point(347, 1)
        Me.Button2.Name = "Button2"
        Me.Button2.Size = New System.Drawing.Size(67, 27)
        Me.Button2.TabIndex = 1
        Me.Button2.Tag = "取消"
        Me.Button2.Text = "取消"
        '
        'Panel1
        '
        Me.Panel1.Controls.Add(Me.Button2)
        Me.Panel1.Controls.Add(Me.Button1)
        Me.Panel1.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.Panel1.Location = New System.Drawing.Point(0, 107)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(429, 29)
        Me.Panel1.TabIndex = 162
        '
        'MaterialsDict12
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(429, 136)
        Me.Controls.Add(Me.Panel1)
        Me.Controls.Add(Me.TableLayoutPanel1)
        Me.Name = "MaterialsDict12"
        Me.Text = "物资分类"
        Me.TableLayoutPanel1.ResumeLayout(False)
        Me.TableLayoutPanel1.PerformLayout()
        Me.Panel1.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents T_Line1 As System.Windows.Forms.Label
    Friend WithEvents TableLayoutPanel1 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents T_Line2 As System.Windows.Forms.Label
    Friend WithEvents MC_Code_Tb As CustomControl.MyTextBox
    Friend WithEvents MC_Name_Tb As CustomControl.MyTextBox
    Friend WithEvents MC_Py_Tb As CustomControl.MyTextBox
    Friend WithEvents MC_Father_Tb As CustomControl.MyTextBox
    Friend WithEvents MC_HaveChild_Cb As System.Windows.Forms.CheckBox
    Friend WithEvents Button1 As CustomControl.MyButton
    Friend WithEvents Button2 As CustomControl.MyButton
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
End Class
