﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using Common;
using ERX.Model;
using ZTHisInsuranceAPI;

namespace ERX.Form
{
    public partial class ErxypmlQuery : Common.BaseForm.BaseFather
    {
        private MdlcircDrugQueryOut mdlcircDrugQueryOut = new MdlcircDrugQueryOut();
        private MdlcircDrugQueryIn mdlcircDrugQueryIn = new MdlcircDrugQueryIn();
        private ERXApi _api = new ERXApi();
        private DataTable _table = new DataTable();
        public ErxypmlQuery(MdlcircDrugQueryIn mdlcircDrugQueryIn ,MdlcircDrugQueryOut mdlcircDrugQueryOut)
        {
            InitializeComponent();
            this.mdlcircDrugQueryOut = mdlcircDrugQueryOut;
            this.mdlcircDrugQueryIn = mdlcircDrugQueryIn;
        }

        private void ErxypmlQuery_Load(object sender, EventArgs e)
        {
            label1.Text = "总数：" + mdlcircDrugQueryOut.total + "当前条数：" + mdlcircDrugQueryOut.size;
            //设置分页控件总数
            uiPagination1.TotalCount =Convert.ToInt32(mdlcircDrugQueryOut.total);
            //设置分页控件每页数量
            uiPagination1.PageSize = Convert.ToInt32(mdlcircDrugQueryIn.pageSize);
            uiPagination1.ActivePage = 1;
            myGrid1.Init_Grid();
            myGrid1.Init_Column("医疗目录编码 ", "medListCodg", 200, "左", "", false);
            myGrid1.Init_Column("国家药品编号 ", "natDrugNo", 200, "左", "", false);
            myGrid1.Init_Column("通用名", "genname", 120, "左", "", false);
            myGrid1.Init_Column("商品名 ", "prodname", 120, "左", "", false);
            myGrid1.Init_Column("注册名", "regName", 120, "左", "", false);
            myGrid1.Init_Column("目录类别 ", "listType", 120, "左", "", false);
            myGrid1.Init_Column("目录类别名称 ", "listTypeName", 200, "左", "", false);
            myGrid1.Init_Column("规格名称 ", "specName", 120, "左", "", false);
            myGrid1.Init_Column("生产厂家 ", "prdrName", 120, "左", "", false);
            myGrid1.Init_Column("批准文号 ", "aprvno", 120, "左", "", false);
            myGrid1.Init_Column("剂型名称", "dosformName", 120, "左", "", false);
            myGrid1.Init_Column("最小包装单位 ", "minPacunt", 202, "左", "", false);
            myGrid1.Init_Column("最小包装数量 ", "minPacCnt", 200, "左", "", false);
            myGrid1.Init_Column("最小制剂单位", "minPrepunt", 200, "左", "", false);
            myGrid1.Init_Column("统筹区编号", "poolareaNo", 120, "左", "", false);
            myGrid1.Init_Column("统筹区名称", "poolareaName", 120, "左", "", false);
            myGrid1.Init_Column("是否双通道标 ", "dualchnlFlag", 200, "左", "", false);
            myGrid1.Init_Column("开始时间 ", "begntime", 120, "中", "yyyy-MM-dd HH:mm:ss", false);
            myGrid1.Init_Column("结束时间 ", "endtime", 120, "中", "yyyy-MM-dd HH:mm:ss", false);
             _table = DataTableToList.ToDataTable(mdlcircDrugQueryOut.list);
            myGrid1.DataTable = _table;

        }
        #region 分页
        private void uiPagination1_PageChanged(object sender, object pagingSource, int pageIndex, int count)
        {
            MdlcircDrugQueryIn mdlcircDrugQueryIn1 = new MdlcircDrugQueryIn();
            MdlcircDrugQueryOut mdlcircDrugQueryOut1 = new MdlcircDrugQueryOut();
            mdlcircDrugQueryIn1.fixmedinsCode = mdlcircDrugQueryIn.fixmedinsCode;
            mdlcircDrugQueryIn1.pageNum = pageIndex.ToString();
            mdlcircDrugQueryIn1.pageSize = "100";
            string msg = "";
            int result = -1;
            result = _api.circDrugQuery(mdlcircDrugQueryIn1, ref msg, ref mdlcircDrugQueryOut1);
            if (result != 0)
            {
                MessageBox.Show( "失败!" + "\r\n" + msg, "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                uiPagination1.ActivePage = 1;
                return;
            }

            if (_table != null)
            {
                _table.Clear();
                _table = DataTableToList.ToDataTable(mdlcircDrugQueryOut1.list);
                myGrid1.DataTable = _table;
            }

        }
        #endregion

    }
}
