﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{C0DAB999-F761-4901-BE5B-C542365756A6}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>DAL</RootNamespace>
    <AssemblyName>DAL</AssemblyName>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\output\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>x86</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="SqlDal">
      <HintPath>..\Dll\SqlDal.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="DAL_Dict.cs" />
    <Compile Include="D_Bl.cs" />
    <Compile Include="D_Bl_Jf.cs" />
    <Compile Include="D_Bl_TWD1.cs" />
    <Compile Include="D_Bl_TWD2.cs" />
    <Compile Include="D_Emr_BasicElementList.cs" />
    <Compile Include="D_Emr_BasicElementTree.cs" />
    <Compile Include="D_Emr_BasicElementValue.cs" />
    <Compile Include="D_Emr_Bl.cs" />
    <Compile Include="D_Emr_BlZk1.cs" />
    <Compile Include="D_Emr_BlZk2.cs" />
    <Compile Include="D_Emr_DataField.cs" />
    <Compile Include="D_Emr_GDlog.cs" />
    <Compile Include="D_Emr_Mb.cs" />
    <Compile Include="D_Emr_Mblb.cs" />
    <Compile Include="D_Emr_SiKong.cs" />
    <Compile Include="D_Emr_ZhiKong1.cs" />
    <Compile Include="D_Emr_ZhiKong2.cs" />
    <Compile Include="D_Emr_ZkDj.cs" />
    <Compile Include="D_Jkk_Consume.cs" />
    <Compile Include="D_Jkk_Cz.cs" />
    <Compile Include="D_LIS_DevPara.cs" />
    <Compile Include="D_LIS_DictDev.cs" />
    <Compile Include="D_LIS_Element1.cs" />
    <Compile Include="D_LIS_Element2.cs" />
    <Compile Include="D_LIS_Test1.cs" />
    <Compile Include="D_LIS_TestItem.cs" />
    <Compile Include="D_LIS_TestXm.cs" />
    <Compile Include="D_LIS_TestXmDy.cs" />
    <Compile Include="D_Materials_Buy_In1.cs" />
    <Compile Include="D_Materials_Buy_In2.cs" />
    <Compile Include="D_Materials_Check1.cs" />
    <Compile Include="D_Materials_Check2.cs" />
    <Compile Include="D_Materials_Class_Dict.cs" />
    <Compile Include="D_Materials_Dict.cs" />
    <Compile Include="D_Materials_InOut_Class_Dict.cs" />
    <Compile Include="D_Materials_Manufacturer_Dict.cs" />
    <Compile Include="D_Materials_Move1.cs" />
    <Compile Include="D_Materials_Move2.cs" />
    <Compile Include="D_Materials_Other_In1.cs" />
    <Compile Include="D_Materials_Other_In2.cs" />
    <Compile Include="D_Materials_Other_Out1.cs" />
    <Compile Include="D_Materials_Other_Out2.cs" />
    <Compile Include="D_Materials_Return1.cs" />
    <Compile Include="D_Materials_Return2.cs" />
    <Compile Include="D_Materials_Stock.cs" />
    <Compile Include="D_Materials_Sup_Dict.cs" />
    <Compile Include="D_Materials_Use_Out1.cs" />
    <Compile Include="D_Materials_Use_Out2.cs" />
    <Compile Include="D_Materials_Warehouse_Dict.cs" />
    <Compile Include="D_Zd_Msg2.cs" />
    <Compile Include="D_Zd_YyBq1.cs" />
    <Compile Include="D_Zd_YyJsr.cs" />
    <Compile Include="D_Zd_YyKs.cs" />
    <Compile Include="D_Zd_YyYs.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\BaseClass\BaseClass.vbproj">
      <Project>{48964d0c-25c0-413c-a94a-c2246ade46a1}</Project>
      <Name>BaseClass</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common.WinFormVar\Common.WinFormVar.csproj">
      <Project>{e267bdd2-634a-405b-bdbf-55354adbc027}</Project>
      <Name>Common.WinFormVar</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common\Common.csproj">
      <Project>{92E350A0-3691-4B8D-A07E-EBB0F10E6997}</Project>
      <Name>Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\DBUtility\DBUtility.csproj">
      <Project>{9aa4cca5-b6d1-4719-b1af-880910bbc87e}</Project>
      <Name>DBUtility</Name>
    </ProjectReference>
    <ProjectReference Include="..\HisPara\HisPara.vbproj">
      <Project>{3E790840-B7EB-4875-A334-D0386A5633CB}</Project>
      <Name>HisPara</Name>
    </ProjectReference>
    <ProjectReference Include="..\HisVar\HisVar.vbproj">
      <Project>{4dad5e14-6224-46b2-886d-6d22ce3886bc}</Project>
      <Name>HisVar</Name>
    </ProjectReference>
    <ProjectReference Include="..\IDBUtility\IDBUtility.csproj">
      <Project>{ccbb5cb6-0871-4d97-9eb3-a68b44cb7e86}</Project>
      <Name>IDBUtility</Name>
    </ProjectReference>
    <ProjectReference Include="..\ModelOld\ModelOld.csproj">
      <Project>{1FAF80FE-D42E-4FEB-853A-B9846C1862AE}</Project>
      <Name>ModelOld</Name>
    </ProjectReference>
    <ProjectReference Include="..\Model\Model.csproj">
      <Project>{3fb6ea13-2c32-4d08-a426-c22224f72121}</Project>
      <Name>Model</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>