﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class ShiXianCx
    Inherits HisControl.BaseForm

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.MyButton1 = New CustomControl.MyButton()
        Me.Panel2 = New System.Windows.Forms.Panel()
        Me.TabControlEx1 = New CustomControl.TabControlEx()
        Me.TabPage1 = New System.Windows.Forms.TabPage()
        Me.MyGrid1 = New CustomControl.MyGrid()
        Me.TabPage2 = New System.Windows.Forms.TabPage()
        Me.MyGrid2 = New CustomControl.MyGrid()
        Me.Panel1.SuspendLayout()
        Me.Panel2.SuspendLayout()
        Me.TabControlEx1.SuspendLayout()
        Me.TabPage1.SuspendLayout()
        Me.TabPage2.SuspendLayout()
        Me.SuspendLayout()
        '
        'Panel1
        '
        Me.Panel1.Controls.Add(Me.MyButton1)
        Me.Panel1.Dock = System.Windows.Forms.DockStyle.Top
        Me.Panel1.Location = New System.Drawing.Point(0, 0)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(865, 45)
        Me.Panel1.TabIndex = 1
        '
        'MyButton1
        '
        Me.MyButton1.ButtonImageSize = CustomControl.MyButton.imageSize.large
        Me.MyButton1.DialogResult = System.Windows.Forms.DialogResult.None
        Me.MyButton1.Font = New System.Drawing.Font("微软雅黑", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.MyButton1.Location = New System.Drawing.Point(7, 4)
        Me.MyButton1.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.MyButton1.Name = "MyButton1"
        Me.MyButton1.Size = New System.Drawing.Size(88, 37)
        Me.MyButton1.TabIndex = 0
        Me.MyButton1.Text = "刷新"
        '
        'Panel2
        '
        Me.Panel2.Controls.Add(Me.TabControlEx1)
        Me.Panel2.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel2.Location = New System.Drawing.Point(0, 45)
        Me.Panel2.Name = "Panel2"
        Me.Panel2.Size = New System.Drawing.Size(865, 653)
        Me.Panel2.TabIndex = 2
        '
        'TabControlEx1
        '
        Me.TabControlEx1.ArrowColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(79, Byte), Integer), CType(CType(125, Byte), Integer))
        Me.TabControlEx1.Controls.Add(Me.TabPage1)
        Me.TabControlEx1.Controls.Add(Me.TabPage2)
        Me.TabControlEx1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TabControlEx1.Location = New System.Drawing.Point(0, 0)
        Me.TabControlEx1.Name = "TabControlEx1"
        Me.TabControlEx1.SelectedIndex = 0
        Me.TabControlEx1.Size = New System.Drawing.Size(865, 653)
        Me.TabControlEx1.TabIndex = 1
        '
        'TabPage1
        '
        Me.TabPage1.Controls.Add(Me.MyGrid1)
        Me.TabPage1.Location = New System.Drawing.Point(4, 26)
        Me.TabPage1.Name = "TabPage1"
        Me.TabPage1.Padding = New System.Windows.Forms.Padding(3)
        Me.TabPage1.Size = New System.Drawing.Size(857, 623)
        Me.TabPage1.TabIndex = 0
        Me.TabPage1.Text = "超时限病历"
        Me.TabPage1.UseVisualStyleBackColor = True
        '
        'MyGrid1
        '
        Me.MyGrid1.CanCustomCol = False
        Me.MyGrid1.Col = 0
        Me.MyGrid1.ColumnFooters = False
        Me.MyGrid1.DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveRight
        Me.MyGrid1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.MyGrid1.FetchRowStyles = False
        Me.MyGrid1.Location = New System.Drawing.Point(3, 3)
        Me.MyGrid1.Margin = New System.Windows.Forms.Padding(0)
        Me.MyGrid1.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.MyGrid1.Name = "MyGrid1"
        Me.MyGrid1.Size = New System.Drawing.Size(851, 617)
        Me.MyGrid1.TabAction = C1.Win.C1TrueDBGrid.TabActionEnum.ControlNavigation
        Me.MyGrid1.TabIndex = 0
        Me.MyGrid1.Xmlpath = Nothing
        '
        'TabPage2
        '
        Me.TabPage2.Controls.Add(Me.MyGrid2)
        Me.TabPage2.Location = New System.Drawing.Point(4, 26)
        Me.TabPage2.Name = "TabPage2"
        Me.TabPage2.Padding = New System.Windows.Forms.Padding(3)
        Me.TabPage2.Size = New System.Drawing.Size(857, 623)
        Me.TabPage2.TabIndex = 1
        Me.TabPage2.Text = "未归档病历"
        Me.TabPage2.UseVisualStyleBackColor = True
        '
        'MyGrid2
        '
        Me.MyGrid2.CanCustomCol = False
        Me.MyGrid2.Col = 0
        Me.MyGrid2.ColumnFooters = False
        Me.MyGrid2.DirectionAfterEnter = C1.Win.C1TrueDBGrid.DirectionAfterEnterEnum.MoveRight
        Me.MyGrid2.Dock = System.Windows.Forms.DockStyle.Fill
        Me.MyGrid2.FetchRowStyles = False
        Me.MyGrid2.Location = New System.Drawing.Point(3, 3)
        Me.MyGrid2.Margin = New System.Windows.Forms.Padding(0)
        Me.MyGrid2.MarqueeStyle = C1.Win.C1TrueDBGrid.MarqueeEnum.HighlightRow
        Me.MyGrid2.Name = "MyGrid2"
        Me.MyGrid2.Size = New System.Drawing.Size(851, 617)
        Me.MyGrid2.TabAction = C1.Win.C1TrueDBGrid.TabActionEnum.ControlNavigation
        Me.MyGrid2.TabIndex = 0
        Me.MyGrid2.Xmlpath = Nothing
        '
        'ShiXianCx
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.ClientSize = New System.Drawing.Size(865, 698)
        Me.Controls.Add(Me.Panel2)
        Me.Controls.Add(Me.Panel1)
        Me.Name = "ShiXianCx"
        Me.Text = "时限查询"
        Me.Panel1.ResumeLayout(False)
        Me.Panel2.ResumeLayout(False)
        Me.TabControlEx1.ResumeLayout(False)
        Me.TabPage1.ResumeLayout(False)
        Me.TabPage2.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents MyButton1 As CustomControl.MyButton
    Friend WithEvents Panel2 As System.Windows.Forms.Panel
    Friend WithEvents TabControlEx1 As CustomControl.TabControlEx
    Friend WithEvents TabPage1 As System.Windows.Forms.TabPage
    Friend WithEvents MyGrid1 As CustomControl.MyGrid
    Friend WithEvents TabPage2 As System.Windows.Forms.TabPage
    Friend WithEvents MyGrid2 As CustomControl.MyGrid

End Class
