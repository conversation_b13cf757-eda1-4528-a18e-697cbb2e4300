﻿/**  版本信息模板在安装目录下，可自行修改。
* D_Zd_QxModule.cs
*
* 功 能： N/A
* 类 名： D_Zd_QxModule
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2016-08-15 09:56:33   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
namespace DAL
{
	/// <summary>
	/// 数据访问类:D_Zd_QxModule
	/// </summary>
	public partial class D_Zd_QxModule
	{
		public D_Zd_QxModule()
		{}
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string Module_Code)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) from Zd_QxModule");
			strSql.Append(" where Module_Code=@Module_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Module_Code", SqlDbType.Char,4)			};
			parameters[0].Value = Module_Code;

			return HisVar.HisVar.Sqldal.Exists(strSql.ToString(),parameters);
		}


        /// <summary>
        /// 是否存在该记录
        /// </summary>
        public bool ExistsByName(string Xl_Name)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from Zd_QxModule");
            strSql.Append(" where Xl_Name=@Xl_Name ");
            SqlParameter[] parameters = {
					new SqlParameter("@Xl_Name", SqlDbType.VarChar,50)			};
            parameters[0].Value = Xl_Name;

            return HisVar.HisVar.Sqldal.Exists(strSql.ToString(), parameters);
        }

		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(ModelOld.M_Zd_QxModule model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("insert into Zd_QxModule(");
			strSql.Append("Module_Code,Dl_Name,Xl_Name,Module_Order,SecondMenu_Code)");
			strSql.Append(" values (");
			strSql.Append("@Module_Code,@Dl_Name,@Xl_Name,@Module_Order,@SecondMenu_Code)");
			SqlParameter[] parameters = {
					new SqlParameter("@Module_Code", SqlDbType.Char,4),
					new SqlParameter("@Dl_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Xl_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Module_Order", SqlDbType.Int,4),
					new SqlParameter("@SecondMenu_Code", SqlDbType.Char,5)};
			parameters[0].Value = model.Module_Code;
			parameters[1].Value = model.Dl_Name;
			parameters[2].Value = model.Xl_Name;
			parameters[3].Value = model.Module_Order;
			parameters[4].Value = model.SecondMenu_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(ModelOld.M_Zd_QxModule model)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("update Zd_QxModule set ");
			strSql.Append("Dl_Name=@Dl_Name,");
			strSql.Append("Xl_Name=@Xl_Name,");
			strSql.Append("Module_Order=@Module_Order,");
			strSql.Append("SecondMenu_Code=@SecondMenu_Code");
			strSql.Append(" where Module_Code=@Module_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Dl_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Xl_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Module_Order", SqlDbType.Int,4),
					new SqlParameter("@SecondMenu_Code", SqlDbType.Char,5),
					new SqlParameter("@Module_Code", SqlDbType.Char,4)};
			parameters[0].Value = model.Dl_Name;
			parameters[1].Value = model.Xl_Name;
			parameters[2].Value = model.Module_Order;
			parameters[3].Value = model.SecondMenu_Code;
			parameters[4].Value = model.Module_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string Module_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Zd_QxModule ");
			strSql.Append(" where Module_Code=@Module_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Module_Code", SqlDbType.Char,4)			};
			parameters[0].Value = Module_Code;

			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString(),parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string Module_Codelist )
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("delete from Zd_QxModule ");
			strSql.Append(" where Module_Code in ("+Module_Codelist + ")  ");
			int rows=HisVar.HisVar.Sqldal.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Zd_QxModule GetModel(string Module_Code)
		{
			
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select  top 1 Module_Code,Dl_Name,Xl_Name,Module_Order,SecondMenu_Code from Zd_QxModule ");
			strSql.Append(" where Module_Code=@Module_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Module_Code", SqlDbType.Char,4)			};
			parameters[0].Value = Module_Code;

			ModelOld.M_Zd_QxModule model=new ModelOld.M_Zd_QxModule();
			DataSet ds=HisVar.HisVar.Sqldal.Query(strSql.ToString(),parameters);
			if(ds.Tables[0].Rows.Count>0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public ModelOld.M_Zd_QxModule DataRowToModel(DataRow row)
		{
			ModelOld.M_Zd_QxModule model=new ModelOld.M_Zd_QxModule();
			if (row != null)
			{
				if(row["Module_Code"]!=null)
				{
					model.Module_Code=row["Module_Code"].ToString();
				}
				if(row["Dl_Name"]!=null)
				{
					model.Dl_Name=row["Dl_Name"].ToString();
				}
				if(row["Xl_Name"]!=null)
				{
					model.Xl_Name=row["Xl_Name"].ToString();
				}
				if(row["Module_Order"]!=null && row["Module_Order"].ToString()!="")
				{
					model.Module_Order=int.Parse(row["Module_Order"].ToString());
				}
				if(row["SecondMenu_Code"]!=null)
				{
					model.SecondMenu_Code=row["SecondMenu_Code"].ToString();
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
            StringBuilder strSql = new StringBuilder();
            strSql.Append("SELECT Zd_QxMenu1.Menu_Code,Zd_QxMenu2.SecondMenu_Code,Zd_QxModule.Module_Code,Menu_Name,SecondMenu_Name,Xl_Name,Module_Order  ");
            strSql.Append("FROM dbo.Zd_QxMenu1,dbo.Zd_QxMenu2,dbo.Zd_QxModule,dbo.Zd_Qx2 ");
            strSql.Append("WHERE Zd_QxMenu1.Menu_Code=Zd_QxMenu2.Menu_Code AND Zd_QxMenu2.SecondMenu_Code=Zd_QxModule.SecondMenu_Code AND Zd_Qx2.Module_Code=Zd_QxModule.Module_Code  ");
            if (strWhere.Trim() != "")
            {
                strSql.Append(" and " + strWhere);
            }
            strSql.Append("ORDER BY Menu_Order,SecondMenu_Order,Module_Order ");
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top,string strWhere,string filedOrder)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select ");
			if(Top>0)
			{
				strSql.Append(" top "+Top.ToString());
			}
			strSql.Append(" Module_Code,Dl_Name,Xl_Name,Module_Order,SecondMenu_Code ");
			strSql.Append(" FROM Zd_QxModule ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("select count(1) FROM Zd_QxModule ");
			if(strWhere.Trim()!="")
			{
				strSql.Append(" where "+strWhere);
			}
			object obj = HisVar.HisVar.Sqldal.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql=new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby );
			}
			else
			{
				strSql.Append("order by T.Module_Code desc");
			}
			strSql.Append(")AS Row, T.*  from Zd_QxModule T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return HisVar.HisVar.Sqldal.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Zd_QxModule";
			parameters[1].Value = "Module_Code";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return HisVar.HisVar.Sqldal.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		#endregion  ExtensionMethod
	}
}

