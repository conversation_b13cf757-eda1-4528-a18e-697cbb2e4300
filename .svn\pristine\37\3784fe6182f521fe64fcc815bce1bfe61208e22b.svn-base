﻿Imports System.Runtime.InteropServices
Imports System.Text
Imports System.IO
Imports System.Windows.Forms

Public Class PtCpIDMR02TG
    <StructLayout(LayoutKind.Sequential, CharSet:=CharSet.Unicode, Pack:=8)> _
          Private Structure IDCardData
        <MarshalAs(UnmanagedType.ByValTStr, SizeConst:=16)> _
        Public name As String
        <MarshalAs(UnmanagedType.ByValTStr, SizeConst:=2)> _
        Public sex As String
        <MarshalAs(UnmanagedType.ByValTStr, SizeConst:=10)> _
        Public nation As String
        <MarshalAs(UnmanagedType.ByValTStr, SizeConst:=10)> _
        Public birthday As String
        <MarshalAs(UnmanagedType.ByValTStr, SizeConst:=36)> _
        Public address As String
        <MarshalAs(UnmanagedType.ByValTStr, SizeConst:=20)> _
        Public cardId As String
        <MarshalAs(UnmanagedType.ByValTStr, SizeConst:=16)> _
        Public police As String
        <MarshalAs(UnmanagedType.ByValTStr, SizeConst:=10)> _
        Public validStart As String
        <MarshalAs(UnmanagedType.ByValTStr, SizeConst:=10)> _
        Public validEnd As String
        <MarshalAs(UnmanagedType.ByValTStr, SizeConst:=2)> _
        Public sexCode As String
        <MarshalAs(UnmanagedType.ByValTStr, SizeConst:=4)> _
        Public nationCode As String
        <MarshalAs(UnmanagedType.ByValTStr, SizeConst:=36)> _
        Public appendMsg As String
    End Structure
    <DllImport(".\PtDkqCpIDMR02TG\cardapi3.dll", EntryPoint:="OpenCardReader", _
        CallingConvention:=CallingConvention.StdCall, CharSet:=CharSet.Unicode)> _
    Private Shared Function OpenCardReader(ByVal lPort As Int32, ByVal ulFlag As UInt32, ByVal ulBaudRate As UInt32) As Int32
    End Function
    <DllImport(".\PtDkqCpIDMR02TG\cardapi3.dll", EntryPoint:="GetPersonMsgW", _
        CallingConvention:=CallingConvention.StdCall, CharSet:=CharSet.Unicode)> _
    Private Shared Function GetPersonMsgW(ByRef pInfo As IDCardData, ByVal pszImageFile As String) As Int32
    End Function
    <DllImport(".\PtDkqCpIDMR02TG\cardapi3.dll", EntryPoint:="CloseCardReader", _
        CallingConvention:=CallingConvention.StdCall, CharSet:=CharSet.Unicode)> _
    Private Shared Function CloseCardReader() As Int32
    End Function
    <DllImport(".\PtDkqCpIDMR02TG\cardapi3.dll", entrypoint:="GetErrorTextW", _
    CallingConvention:=CallingConvention.StdCall, CharSet:=CharSet.Unicode)> _
    Private Shared Sub GetErrorTextW(ByVal pszBuffer As StringBuilder, ByVal dwBufLen As UInt32)
    End Sub

    Public Shared Function Dkq_RyXx()
        FileCopy(Application.StartupPath & "\PtDkqCpIDMR02TG\License.dat", "C:\License.dat")
        Dim CardMsg As New IDCardData()

        Dim result As Int32
        Dim errorText As StringBuilder
        Dim maxErrorTextLen As Integer = 32
        Dim error_text As String
        Dim Ryxx(4) As String
        errorText = New StringBuilder(maxErrorTextLen)
        '参数1为端口号。1表示串口1，2表示串口2，依次类推。1001表示USB。0表示自动选择。
        '参数2为标志位。0x02表示启用重复读卡。0x04表示读卡后接着读取新地址。
        '各个数值可以用“按位或”运算符组合起来。
        '参数3为波特率。使用串口阅读器的程序应正确设置此参数。出厂机器的波特率一般为115200。
        result = OpenCardReader(0, 2, 115200)

        GetErrorTextW(errorText, maxErrorTextLen)
        error_text = errorText.ToString()

        If result = 0 Then

            Dim imagePath As String


            imagePath = Path.GetTempPath() + "image.bmp"
            '当程序打开设备后，可以多次调用读取信息函数。
            result = GetPersonMsgW(CardMsg, imagePath)
            result = CloseCardReader()
            GetErrorTextW(errorText, maxErrorTextLen)
            error_text = errorText.ToString()
            File.Delete(imagePath)
            If 0 = result Then

                Ryxx(0) = Trim(CardMsg.name)
                Ryxx(1) = Trim(CardMsg.sex)
                Ryxx(2) = Trim(CardMsg.nation)
                Ryxx(3) = Trim(CardMsg.address)
                Ryxx(4) = Trim(CardMsg.cardId)

            Else
                Ryxx(0) = error_text

            End If

        Else
            Ryxx(0) = error_text

        End If

        Return Ryxx



    End Function


End Class
